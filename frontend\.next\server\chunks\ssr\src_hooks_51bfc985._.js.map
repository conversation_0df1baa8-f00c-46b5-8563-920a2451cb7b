{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/api/useApiQuery.ts"], "sourcesContent": ["/**\r\n * @file Standardized API Query Hook\r\n * @description Provides consistent API query patterns with error handling, caching, and loading states\r\n */\r\n\r\nimport {\r\n  type QueryKey,\r\n  useQuery,\r\n  type UseQueryOptions,\r\n  type UseQueryResult,\r\n} from '@tanstack/react-query';\r\nimport { useEffect } from 'react';\r\n\r\nimport type { PaginatedResponse } from '@/types';\r\n\r\nimport { useToast } from '@/hooks/utils/use-toast';\r\n\r\n/**\r\n * Configuration options for API queries\r\n */\r\nexport interface ApiQueryOptions<T>\r\n  extends Omit<UseQueryOptions<T>, 'queryFn' | 'queryKey'> {\r\n  /** Cache duration in milliseconds */\r\n  cacheDuration?: number;\r\n  /** Whether to retry on error */\r\n  enableRetry?: boolean;\r\n  /** Custom error message */\r\n  errorMessage?: string;\r\n  /** Number of retry attempts */\r\n  retryAttempts?: number;\r\n  /** Whether to show error toasts automatically */\r\n  showErrorToast?: boolean;\r\n  /** Whether to show success toasts automatically */\r\n  showSuccessToast?: boolean;\r\n  /** Custom success message */\r\n  successMessage?: string;\r\n}\r\n\r\n/**\r\n * Enhanced API query result with additional utilities\r\n */\r\nexport type ApiQueryResult<T> = UseQueryResult<T> & {\r\n  /** Force refresh the query */\r\n  forceRefresh: () => Promise<UseQueryResult<T>>;\r\n  /** Check if data is stale */\r\n  isStale: boolean;\r\n  /** Last updated timestamp */\r\n  lastUpdated: null | number;\r\n};\r\n\r\n/**\r\n * Standardized API query hook with consistent error handling and caching\r\n *\r\n * @example\r\n * ```typescript\r\n * const { data, isLoading, error, forceRefresh } = useApiQuery(\r\n *   ['users', filters],\r\n *   () => userService.getAll(filters),\r\n *   {\r\n *     showErrorToast: true,\r\n *     cacheDuration: 5 * 60 * 1000, // 5 minutes\r\n *     enableRetry: true,\r\n *   }\r\n * );\r\n * ```\r\n */\r\nexport const useApiQuery = <T>(\r\n  queryKey: QueryKey,\r\n  queryFn: () => Promise<T>,\r\n  options: ApiQueryOptions<T> = {}\r\n): ApiQueryResult<T> => {\r\n  const { toast } = useToast();\r\n\r\n  const {\r\n    cacheDuration = 5 * 60 * 1000, // 5 minutes default\r\n    enableRetry = true,\r\n    errorMessage,\r\n    retryAttempts = 3,\r\n    showErrorToast = true,\r\n    showSuccessToast = false,\r\n    successMessage,\r\n    ...queryOptions\r\n  } = options;\r\n\r\n  const queryResult = useQuery({\r\n    gcTime: cacheDuration * 2, // Keep in cache for twice the stale time\r\n    queryFn,\r\n    queryKey,\r\n    retry: enableRetry ? retryAttempts : false,\r\n    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),\r\n    staleTime: cacheDuration,\r\n    ...queryOptions,\r\n  });\r\n\r\n  // FIXED: Handle success notifications in useEffect to prevent setState during render\r\n  useEffect(() => {\r\n    if (\r\n      showSuccessToast &&\r\n      queryResult.isSuccess &&\r\n      queryResult.data &&\r\n      successMessage\r\n    ) {\r\n      toast({\r\n        description: successMessage,\r\n        title: 'Success',\r\n      });\r\n    }\r\n  }, [\r\n    showSuccessToast,\r\n    queryResult.isSuccess,\r\n    queryResult.data,\r\n    successMessage,\r\n    toast,\r\n  ]);\r\n\r\n  // FIXED: Handle error notifications in useEffect to prevent setState during render\r\n  useEffect(() => {\r\n    if (showErrorToast && queryResult.isError) {\r\n      const message =\r\n        errorMessage ||\r\n        (queryResult.error instanceof Error\r\n          ? queryResult.error.message\r\n          : 'An error occurred');\r\n\r\n      toast({\r\n        description: message,\r\n        title: 'Error',\r\n        variant: 'destructive',\r\n      });\r\n    }\r\n  }, [\r\n    showErrorToast,\r\n    queryResult.isError,\r\n    queryResult.error,\r\n    errorMessage,\r\n    toast,\r\n  ]);\r\n\r\n  // Enhanced result with additional utilities\r\n  const enhancedResult: ApiQueryResult<T> = {\r\n    ...queryResult,\r\n    forceRefresh: async () => await queryResult.refetch(),\r\n    isStale: queryResult.isStale || false,\r\n    lastUpdated: queryResult.dataUpdatedAt || null,\r\n  };\r\n\r\n  return enhancedResult;\r\n};\r\n\r\n/**\r\n * Hook for queries that depend on other queries\r\n * Automatically handles dependency loading states\r\n */\r\nexport const useDependentApiQuery = <T, TDep>(\r\n  queryKey: QueryKey,\r\n  queryFn: (dependency: TDep) => Promise<T>,\r\n  dependency: TDep | undefined,\r\n  options: ApiQueryOptions<T> = {}\r\n): ApiQueryResult<T> => {\r\n  return useApiQuery(\r\n    queryKey,\r\n    () => {\r\n      if (!dependency) {\r\n        throw new Error('Dependency not available');\r\n      }\r\n      return queryFn(dependency);\r\n    },\r\n    {\r\n      ...options,\r\n      enabled: !!dependency && options.enabled !== false,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Type for the raw API response of a paginated query\r\n */\r\nexport interface PaginatedApiResponse<T> {\r\n  data: T[];\r\n  pagination: PaginatedResponse<any>['pagination'];\r\n}\r\n\r\n/**\r\n * Hook for paginated API queries with consistent pagination handling\r\n */\r\nexport interface PaginatedQueryOptions<T>\r\n  extends ApiQueryOptions<PaginatedApiResponse<T>> {\r\n  /** Whether to keep previous data while loading new page */\r\n  keepPreviousData?: boolean;\r\n  /** Current page number */\r\n  page?: number;\r\n  /** Items per page */\r\n  pageSize?: number;\r\n}\r\n\r\nexport type PaginatedQueryResult<T> = Omit<\r\n  ApiQueryResult<PaginatedApiResponse<T>>,\r\n  'data'\r\n> & {\r\n  /** Current page number */\r\n  currentPage: number;\r\n  /** The actual data array */\r\n  data: T[];\r\n  /** Go to specific page */\r\n  goToPage: (page: number) => void;\r\n  /** Whether there's a next page */\r\n  hasNextPage: boolean;\r\n  /** Whether there's a previous page */\r\n  hasPrevPage: boolean;\r\n  /** Go to next page */\r\n  nextPage: () => void;\r\n  /** The pagination object */\r\n  pagination: PaginatedResponse<any>['pagination'];\r\n  /** Go to previous page */\r\n  prevPage: () => void;\r\n  /** Total number of pages */\r\n  totalPages: number;\r\n};\r\n\r\n/**\r\n * Standardized paginated API query hook\r\n */\r\nexport const usePaginatedApiQuery = <T>(\r\n  baseQueryKey: QueryKey,\r\n  queryFn: (page: number, pageSize: number) => Promise<PaginatedApiResponse<T>>,\r\n  options: PaginatedQueryOptions<T> = {}\r\n): PaginatedQueryResult<T> => {\r\n  const {\r\n    keepPreviousData = true,\r\n    page = 1,\r\n    pageSize = 10,\r\n    ...apiOptions\r\n  } = options;\r\n\r\n  const queryKey = [...baseQueryKey, 'paginated', page, pageSize];\r\n\r\n  const queryResult = useApiQuery<PaginatedApiResponse<T>>(\r\n    queryKey,\r\n    () => queryFn(page, pageSize),\r\n    {\r\n      ...apiOptions,\r\n      ...(keepPreviousData\r\n        ? {\r\n            placeholderData: (prev: PaginatedApiResponse<T> | undefined) =>\r\n              prev,\r\n          }\r\n        : {}),\r\n    }\r\n  );\r\n\r\n  const pagination = queryResult.data?.pagination;\r\n\r\n  const enhancedResult: PaginatedQueryResult<T> = {\r\n    ...queryResult,\r\n    currentPage: page,\r\n    data: queryResult.data?.data ?? [],\r\n    goToPage: (newPage: number) => {\r\n      // This would typically be handled by the parent component\r\n    },\r\n    hasNextPage: pagination ? pagination.hasNext : false,\r\n    hasPrevPage: pagination ? pagination.hasPrevious : false,\r\n    nextPage: () => {\r\n      if (pagination && pagination.hasNext) {\r\n        // This would typically be handled by the parent component\r\n        // by updating the page state that's passed to this hook\r\n      }\r\n    },\r\n    pagination: pagination ?? {\r\n      hasNext: false,\r\n      hasPrevious: false,\r\n      limit: pageSize,\r\n      page: 1,\r\n      total: 0,\r\n      totalPages: 1,\r\n    },\r\n    prevPage: () => {\r\n      if (pagination && pagination.hasPrevious) {\r\n        // This would typically be handled by the parent component\r\n      }\r\n    },\r\n    totalPages: pagination ? pagination.totalPages : 1,\r\n  };\r\n\r\n  return enhancedResult;\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AAMA;AAIA;;;;AAmDO,MAAM,cAAc,CACzB,UACA,SACA,UAA8B,CAAC,CAAC;IAEhC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,EACJ,gBAAgB,IAAI,KAAK,IAAI,EAC7B,cAAc,IAAI,EAClB,YAAY,EACZ,gBAAgB,CAAC,EACjB,iBAAiB,IAAI,EACrB,mBAAmB,KAAK,EACxB,cAAc,EACd,GAAG,cACJ,GAAG;IAEJ,MAAM,cAAc,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QAC3B,QAAQ,gBAAgB;QACxB;QACA;QACA,OAAO,cAAc,gBAAgB;QACrC,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAC/D,WAAW;QACX,GAAG,YAAY;IACjB;IAEA,qFAAqF;IACrF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IACE,oBACA,YAAY,SAAS,IACrB,YAAY,IAAI,IAChB,gBACA;YACA,MAAM;gBACJ,aAAa;gBACb,OAAO;YACT;QACF;IACF,GAAG;QACD;QACA,YAAY,SAAS;QACrB,YAAY,IAAI;QAChB;QACA;KACD;IAED,mFAAmF;IACnF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,kBAAkB,YAAY,OAAO,EAAE;YACzC,MAAM,UACJ,gBACA,CAAC,YAAY,KAAK,YAAY,QAC1B,YAAY,KAAK,CAAC,OAAO,GACzB,mBAAmB;YAEzB,MAAM;gBACJ,aAAa;gBACb,OAAO;gBACP,SAAS;YACX;QACF;IACF,GAAG;QACD;QACA,YAAY,OAAO;QACnB,YAAY,KAAK;QACjB;QACA;KACD;IAED,4CAA4C;IAC5C,MAAM,iBAAoC;QACxC,GAAG,WAAW;QACd,cAAc,UAAY,MAAM,YAAY,OAAO;QACnD,SAAS,YAAY,OAAO,IAAI;QAChC,aAAa,YAAY,aAAa,IAAI;IAC5C;IAEA,OAAO;AACT;AAMO,MAAM,uBAAuB,CAClC,UACA,SACA,YACA,UAA8B,CAAC,CAAC;IAEhC,OAAO,YACL,UACA;QACE,IAAI,CAAC,YAAY;YACf,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,QAAQ;IACjB,GACA;QACE,GAAG,OAAO;QACV,SAAS,CAAC,CAAC,cAAc,QAAQ,OAAO,KAAK;IAC/C;AAEJ;AAkDO,MAAM,uBAAuB,CAClC,cACA,SACA,UAAoC,CAAC,CAAC;IAEtC,MAAM,EACJ,mBAAmB,IAAI,EACvB,OAAO,CAAC,EACR,WAAW,EAAE,EACb,GAAG,YACJ,GAAG;IAEJ,MAAM,WAAW;WAAI;QAAc;QAAa;QAAM;KAAS;IAE/D,MAAM,cAAc,YAClB,UACA,IAAM,QAAQ,MAAM,WACpB;QACE,GAAG,UAAU;QACb,GAAI,mBACA;YACE,iBAAiB,CAAC,OAChB;QACJ,IACA,CAAC,CAAC;IACR;IAGF,MAAM,aAAa,YAAY,IAAI,EAAE;IAErC,MAAM,iBAA0C;QAC9C,GAAG,WAAW;QACd,aAAa;QACb,MAAM,YAAY,IAAI,EAAE,QAAQ,EAAE;QAClC,UAAU,CAAC;QACT,0DAA0D;QAC5D;QACA,aAAa,aAAa,WAAW,OAAO,GAAG;QAC/C,aAAa,aAAa,WAAW,WAAW,GAAG;QACnD,UAAU;YACR,IAAI,cAAc,WAAW,OAAO,EAAE;YACpC,0DAA0D;YAC1D,wDAAwD;YAC1D;QACF;QACA,YAAY,cAAc;YACxB,SAAS;YACT,aAAa;YACb,OAAO;YACP,MAAM;YACN,OAAO;YACP,YAAY;QACd;QACA,UAAU;YACR,IAAI,cAAc,WAAW,WAAW,EAAE;YACxC,0DAA0D;YAC5D;QACF;QACA,YAAY,aAAa,WAAW,UAAU,GAAG;IACnD;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/api/useApiMutation.ts"], "sourcesContent": ["/**\r\n * @file Standardized API Mutation Hook\r\n * @description Provides consistent API mutation patterns with optimistic updates, error handling, and loading states\r\n */\r\n\r\nimport {\r\n  type QueryKey,\r\n  type UseMutationOptions,\r\n  type UseMutationResult,\r\n  useMutation,\r\n  useQueryClient,\r\n} from '@tanstack/react-query';\r\n\r\nimport { useToast } from '@/hooks/utils/use-toast';\r\n\r\n/**\r\n * Configuration options for API mutations\r\n */\r\nexport interface ApiMutationOptions<TData, TVariables>\r\n  extends Omit<UseMutationOptions<TData, Error, TVariables>, 'mutationFn'> {\r\n  /** Whether to show error toasts automatically */\r\n  showErrorToast?: boolean;\r\n  /** Whether to show success toasts automatically */\r\n  showSuccessToast?: boolean;\r\n  /** Custom success message */\r\n  successMessage?: string;\r\n  /** Custom error message */\r\n  errorMessage?: string;\r\n  /** Query keys to invalidate on success */\r\n  invalidateQueries?: QueryKey[];\r\n  /** Whether to enable optimistic updates */\r\n  enableOptimisticUpdates?: boolean;\r\n  /** Function to generate optimistic update data */\r\n  optimisticUpdateFn?: (variables: TVariables) => TData;\r\n  /** Query key for optimistic updates */\r\n  optimisticQueryKey?: QueryKey;\r\n}\r\n\r\n/**\r\n * Enhanced API mutation result with additional utilities\r\n */\r\nexport interface ApiMutationResult<TData, TVariables> {\r\n  /** Execute mutation with automatic error handling */\r\n  executeAsync: (variables: TVariables) => Promise<TData>;\r\n  /** Check if mutation is in progress */\r\n  isExecuting: boolean;\r\n  /** Last execution timestamp */\r\n  lastExecuted: number | null;\r\n  /** All properties from UseMutationResult */\r\n  mutate: (variables: TVariables, options?: any) => void;\r\n  mutateAsync: (variables: TVariables, options?: any) => Promise<TData>;\r\n  reset: () => void;\r\n  isPending: boolean;\r\n  isError: boolean;\r\n  isSuccess: boolean;\r\n  isIdle: boolean;\r\n  data: TData | undefined;\r\n  error: Error | null;\r\n  variables: TVariables | undefined;\r\n  context: unknown;\r\n  failureCount: number;\r\n  failureReason: Error | null;\r\n  isPaused: boolean;\r\n  status: 'idle' | 'pending' | 'error' | 'success';\r\n  submittedAt: number;\r\n}\r\n\r\n/**\r\n * Standardized API mutation hook with consistent error handling and optimistic updates\r\n *\r\n * @example\r\n * ```typescript\r\n * const { mutate, isLoading, executeAsync } = useApiMutation(\r\n *   (userData) => userService.create(userData),\r\n *   {\r\n *     showSuccessToast: true,\r\n *     successMessage: 'User created successfully',\r\n *     invalidateQueries: [['users']],\r\n *     enableOptimisticUpdates: true,\r\n *     optimisticQueryKey: ['users'],\r\n *     optimisticUpdateFn: (userData) => ({ ...userData, id: 'temp-id' }),\r\n *   }\r\n * );\r\n * ```\r\n */\r\nexport const useApiMutation = <TData, TVariables = void>(\r\n  mutationFn: (variables: TVariables) => Promise<TData>,\r\n  options: ApiMutationOptions<TData, TVariables> = {}\r\n): ApiMutationResult<TData, TVariables> => {\r\n  const { toast } = useToast();\r\n  const queryClient = useQueryClient();\r\n\r\n  const {\r\n    showErrorToast = true,\r\n    showSuccessToast = true,\r\n    successMessage = 'Operation completed successfully',\r\n    errorMessage,\r\n    invalidateQueries = [],\r\n    enableOptimisticUpdates = false,\r\n    optimisticUpdateFn,\r\n    optimisticQueryKey,\r\n    onSuccess,\r\n    onError,\r\n    onMutate,\r\n    ...mutationOptions\r\n  } = options;\r\n\r\n  const mutationResult = useMutation({\r\n    mutationFn,\r\n    onMutate: async variables => {\r\n      // Handle optimistic updates\r\n      if (enableOptimisticUpdates && optimisticQueryKey && optimisticUpdateFn) {\r\n        // Cancel any outgoing refetches\r\n        await queryClient.cancelQueries({ queryKey: optimisticQueryKey });\r\n\r\n        // Snapshot the previous value\r\n        const previousData = queryClient.getQueryData(optimisticQueryKey);\r\n\r\n        // Optimistically update to the new value\r\n        const optimisticData = optimisticUpdateFn(variables);\r\n        queryClient.setQueryData(optimisticQueryKey, optimisticData);\r\n\r\n        // Return a context object with the snapshotted value\r\n        const context = { previousData };\r\n\r\n        // Call custom onMutate if provided\r\n        const customContext = await onMutate?.(variables);\r\n\r\n        return customContext ? { ...context, ...customContext } : context;\r\n      }\r\n\r\n      return onMutate?.(variables);\r\n    },\r\n    onSuccess: (data, variables, context) => {\r\n      // Show success toast\r\n      if (showSuccessToast) {\r\n        toast({\r\n          title: 'Success',\r\n          description: successMessage,\r\n        });\r\n      }\r\n\r\n      // Invalidate and refetch queries\r\n      invalidateQueries.forEach(queryKey => {\r\n        queryClient.invalidateQueries({ queryKey });\r\n      });\r\n\r\n      // Call custom onSuccess if provided\r\n      onSuccess?.(data, variables, context);\r\n    },\r\n    onError: (error, variables, context) => {\r\n      // Rollback optimistic updates\r\n      if (\r\n        enableOptimisticUpdates &&\r\n        optimisticQueryKey &&\r\n        context &&\r\n        typeof context === 'object' &&\r\n        'previousData' in context\r\n      ) {\r\n        queryClient.setQueryData(\r\n          optimisticQueryKey,\r\n          (context as any).previousData\r\n        );\r\n      }\r\n\r\n      // Show error toast\r\n      if (showErrorToast) {\r\n        const message = errorMessage || error.message || 'An error occurred';\r\n        toast({\r\n          title: 'Error',\r\n          description: message,\r\n          variant: 'destructive',\r\n        });\r\n      }\r\n\r\n      // Call custom onError if provided\r\n      onError?.(error, variables, context);\r\n    },\r\n    ...mutationOptions,\r\n  });\r\n\r\n  // Enhanced result with additional utilities\r\n  const enhancedResult: ApiMutationResult<TData, TVariables> = {\r\n    ...mutationResult,\r\n    executeAsync: async (variables: TVariables): Promise<TData> => {\r\n      return new Promise((resolve, reject) => {\r\n        mutationResult.mutate(variables, {\r\n          onSuccess: data => resolve(data),\r\n          onError: error => reject(error),\r\n        });\r\n      });\r\n    },\r\n    isExecuting: mutationResult.isPending,\r\n    lastExecuted: mutationResult.submittedAt || null,\r\n  };\r\n\r\n  return enhancedResult;\r\n};\r\n\r\n/**\r\n * Hook for mutations that create new resources\r\n * Automatically handles cache updates for list queries\r\n */\r\nexport const useCreateMutation = <TData, TVariables = void>(\r\n  mutationFn: (variables: TVariables) => Promise<TData>,\r\n  listQueryKey: QueryKey,\r\n  options: Omit<ApiMutationOptions<TData, TVariables>, 'invalidateQueries'> = {}\r\n): ApiMutationResult<TData, TVariables> => {\r\n  return useApiMutation(mutationFn, {\r\n    ...options,\r\n    invalidateQueries: [listQueryKey],\r\n    successMessage: options.successMessage || 'Item created successfully',\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for mutations that update existing resources\r\n * Automatically handles cache updates for both list and detail queries\r\n */\r\nexport const useUpdateMutation = <TData, TVariables = void>(\r\n  mutationFn: (variables: TVariables) => Promise<TData>,\r\n  listQueryKey: QueryKey,\r\n  getDetailQueryKey: (data: TData) => QueryKey,\r\n  options: Omit<\r\n    ApiMutationOptions<TData, TVariables>,\r\n    'invalidateQueries' | 'onSuccess'\r\n  > = {}\r\n): ApiMutationResult<TData, TVariables> => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useApiMutation(mutationFn, {\r\n    ...options,\r\n    successMessage: options.successMessage || 'Item updated successfully',\r\n    onSuccess: (data, variables, context) => {\r\n      // Invalidate list query\r\n      queryClient.invalidateQueries({ queryKey: listQueryKey });\r\n\r\n      // Update detail query cache\r\n      const detailQueryKey = getDetailQueryKey(data);\r\n      queryClient.setQueryData(detailQueryKey, data);\r\n\r\n      // Call custom onSuccess if provided\r\n      (options as any).onSuccess?.(data, variables, context);\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for mutations that delete resources\r\n * Automatically handles cache cleanup\r\n */\r\nexport const useDeleteMutation = <TVariables = void>(\r\n  mutationFn: (variables: TVariables) => Promise<void>,\r\n  listQueryKey: QueryKey,\r\n  getDetailQueryKey: (variables: TVariables) => QueryKey,\r\n  options: Omit<\r\n    ApiMutationOptions<void, TVariables>,\r\n    'invalidateQueries' | 'onSuccess'\r\n  > = {}\r\n): ApiMutationResult<void, TVariables> => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useApiMutation(mutationFn, {\r\n    ...options,\r\n    successMessage: options.successMessage || 'Item deleted successfully',\r\n    onSuccess: (data, variables, context) => {\r\n      // Invalidate list query\r\n      queryClient.invalidateQueries({ queryKey: listQueryKey });\r\n\r\n      // Remove detail query from cache\r\n      const detailQueryKey = getDetailQueryKey(variables);\r\n      queryClient.removeQueries({ queryKey: detailQueryKey });\r\n\r\n      // Call custom onSuccess if provided\r\n      (options as any).onSuccess?.(data, variables, context);\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;AAAA;AAQA;;;AAwEO,MAAM,iBAAiB,CAC5B,YACA,UAAiD,CAAC,CAAC;IAEnD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,EACJ,iBAAiB,IAAI,EACrB,mBAAmB,IAAI,EACvB,iBAAiB,kCAAkC,EACnD,YAAY,EACZ,oBAAoB,EAAE,EACtB,0BAA0B,KAAK,EAC/B,kBAAkB,EAClB,kBAAkB,EAClB,SAAS,EACT,OAAO,EACP,QAAQ,EACR,GAAG,iBACJ,GAAG;IAEJ,MAAM,iBAAiB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjC;QACA,UAAU,OAAM;YACd,4BAA4B;YAC5B,IAAI,2BAA2B,sBAAsB,oBAAoB;gBACvE,gCAAgC;gBAChC,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU;gBAAmB;gBAE/D,8BAA8B;gBAC9B,MAAM,eAAe,YAAY,YAAY,CAAC;gBAE9C,yCAAyC;gBACzC,MAAM,iBAAiB,mBAAmB;gBAC1C,YAAY,YAAY,CAAC,oBAAoB;gBAE7C,qDAAqD;gBACrD,MAAM,UAAU;oBAAE;gBAAa;gBAE/B,mCAAmC;gBACnC,MAAM,gBAAgB,MAAM,WAAW;gBAEvC,OAAO,gBAAgB;oBAAE,GAAG,OAAO;oBAAE,GAAG,aAAa;gBAAC,IAAI;YAC5D;YAEA,OAAO,WAAW;QACpB;QACA,WAAW,CAAC,MAAM,WAAW;YAC3B,qBAAqB;YACrB,IAAI,kBAAkB;gBACpB,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF;YAEA,iCAAiC;YACjC,kBAAkB,OAAO,CAAC,CAAA;gBACxB,YAAY,iBAAiB,CAAC;oBAAE;gBAAS;YAC3C;YAEA,oCAAoC;YACpC,YAAY,MAAM,WAAW;QAC/B;QACA,SAAS,CAAC,OAAO,WAAW;YAC1B,8BAA8B;YAC9B,IACE,2BACA,sBACA,WACA,OAAO,YAAY,YACnB,kBAAkB,SAClB;gBACA,YAAY,YAAY,CACtB,oBACA,AAAC,QAAgB,YAAY;YAEjC;YAEA,mBAAmB;YACnB,IAAI,gBAAgB;gBAClB,MAAM,UAAU,gBAAgB,MAAM,OAAO,IAAI;gBACjD,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF;YAEA,kCAAkC;YAClC,UAAU,OAAO,WAAW;QAC9B;QACA,GAAG,eAAe;IACpB;IAEA,4CAA4C;IAC5C,MAAM,iBAAuD;QAC3D,GAAG,cAAc;QACjB,cAAc,OAAO;YACnB,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,eAAe,MAAM,CAAC,WAAW;oBAC/B,WAAW,CAAA,OAAQ,QAAQ;oBAC3B,SAAS,CAAA,QAAS,OAAO;gBAC3B;YACF;QACF;QACA,aAAa,eAAe,SAAS;QACrC,cAAc,eAAe,WAAW,IAAI;IAC9C;IAEA,OAAO;AACT;AAMO,MAAM,oBAAoB,CAC/B,YACA,cACA,UAA4E,CAAC,CAAC;IAE9E,OAAO,eAAe,YAAY;QAChC,GAAG,OAAO;QACV,mBAAmB;YAAC;SAAa;QACjC,gBAAgB,QAAQ,cAAc,IAAI;IAC5C;AACF;AAMO,MAAM,oBAAoB,CAC/B,YACA,cACA,mBACA,UAGI,CAAC,CAAC;IAEN,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,eAAe,YAAY;QAChC,GAAG,OAAO;QACV,gBAAgB,QAAQ,cAAc,IAAI;QAC1C,WAAW,CAAC,MAAM,WAAW;YAC3B,wBAAwB;YACxB,YAAY,iBAAiB,CAAC;gBAAE,UAAU;YAAa;YAEvD,4BAA4B;YAC5B,MAAM,iBAAiB,kBAAkB;YACzC,YAAY,YAAY,CAAC,gBAAgB;YAEzC,oCAAoC;YACnC,QAAgB,SAAS,GAAG,MAAM,WAAW;QAChD;IACF;AACF;AAMO,MAAM,oBAAoB,CAC/B,YACA,cACA,mBACA,UAGI,CAAC,CAAC;IAEN,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,eAAe,YAAY;QAChC,GAAG,OAAO;QACV,gBAAgB,QAAQ,cAAc,IAAI;QAC1C,WAAW,CAAC,MAAM,WAAW;YAC3B,wBAAwB;YACxB,YAAY,iBAAiB,CAAC;gBAAE,UAAU;YAAa;YAEvD,iCAAiC;YACjC,MAAM,iBAAiB,kBAAkB;YACzC,YAAY,aAAa,CAAC;gBAAE,UAAU;YAAe;YAErD,oCAAoC;YACnC,QAAgB,SAAS,GAAG,MAAM,WAAW;QAChD;IACF;AACF", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/api/useNavigationPrefetch.ts"], "sourcesContent": ["/**\r\n * @file Navigation prefetching hook for performance optimization\r\n * @module hooks/useNavigationPrefetch\r\n */\r\n\r\nimport { useRouter } from 'next/navigation';\r\nimport { useCallback, useEffect } from 'react';\r\nimport { useQueryClient } from '@tanstack/react-query';\r\n\r\nimport {\r\n  delegationApiService,\r\n  employeeApiService,\r\n  taskApiService,\r\n  vehicleApiService,\r\n} from '../../lib/api/services/apiServiceFactory';\r\nimport { prefetchUtils, queryClient } from '../../lib/stores/queryClient';\r\nimport { useAuthContext } from '../../contexts/AuthContext';\r\n\r\n/**\r\n * Create authentication-aware prefetch patterns\r\n * @param isAuthReady - Whether authentication system is ready for API calls\r\n */\r\nconst createPrefetchPatterns = (isAuthReady: boolean) => ({\r\n  // Dashboard route - prefetch all critical data\r\n  '/': () => prefetchUtils.prefetchDashboardData(isAuthReady),\r\n\r\n  // Admin routes - Updated to use new reliability API service\r\n  '/admin': () => {\r\n    if (!isAuthReady) {\r\n      console.warn('Authentication not ready, deferring admin data prefetch.');\r\n      return Promise.resolve();\r\n    }\r\n    return Promise.all([\r\n      queryClient.prefetchQuery({\r\n        queryFn: () => employeeApiService.getAll(), // Admin users are employees\r\n        queryKey: ['admin', 'users'],\r\n        staleTime: 5 * 60 * 1000,\r\n      }),\r\n      // Note: Performance metrics now available in reliability dashboard\r\n      // Legacy admin performance metrics kept for backward compatibility\r\n    ]);\r\n  },\r\n\r\n  '/admin/audit': () => {\r\n    if (!isAuthReady) {\r\n      console.warn(\r\n        'Authentication not ready, deferring admin audit data prefetch.'\r\n      );\r\n      return Promise.resolve();\r\n    }\r\n    return queryClient.prefetchQuery({\r\n      queryFn: async () => {\r\n        const { adminService } = await import('@/lib/api/services/admin');\r\n        return adminService.getRecentErrors();\r\n      },\r\n      queryKey: ['admin', 'audit'],\r\n      staleTime: 1 * 60 * 1000, // 1 minute for audit logs\r\n    });\r\n  },\r\n\r\n  // Reliability dashboard routes - New comprehensive monitoring\r\n  '/reliability': () => {\r\n    if (!isAuthReady) {\r\n      console.warn(\r\n        'Authentication not ready, deferring reliability data prefetch.'\r\n      );\r\n      return Promise.resolve();\r\n    }\r\n    return Promise.all([\r\n      queryClient.prefetchQuery({\r\n        queryFn: async () => {\r\n          const { reliabilityApiService } = await import(\r\n            '../../lib/api/services/apiServiceFactory'\r\n          );\r\n          // Prefetch system health data\r\n          return reliabilityApiService.getSystemHealth();\r\n        },\r\n        queryKey: ['reliability', 'health'],\r\n        staleTime: 15 * 1000, // 15 seconds for real-time data\r\n      }),\r\n      queryClient.prefetchQuery({\r\n        queryFn: async () => {\r\n          const { reliabilityApiService } = await import(\r\n            '../../lib/api/services/apiServiceFactory'\r\n          );\r\n          // Prefetch circuit breaker data\r\n          return reliabilityApiService.getCircuitBreakerStatus();\r\n        },\r\n        queryKey: ['reliability', 'circuit-breakers'],\r\n        staleTime: 30 * 1000, // 30 seconds for circuit breaker data\r\n      }),\r\n    ]);\r\n  },\r\n\r\n  '/admin/users': () => {\r\n    if (!isAuthReady) {\r\n      console.warn(\r\n        'Authentication not ready, deferring admin users data prefetch.'\r\n      );\r\n      return Promise.resolve();\r\n    }\r\n    return queryClient.prefetchQuery({\r\n      queryFn: () => employeeApiService.getAll(),\r\n      queryKey: ['admin', 'users'],\r\n      staleTime: 5 * 60 * 1000,\r\n    });\r\n  },\r\n\r\n  // Delegation routes\r\n  '/delegations': () => {\r\n    if (!isAuthReady) {\r\n      console.warn(\r\n        'Authentication not ready, deferring delegations data prefetch.'\r\n      );\r\n      return Promise.resolve();\r\n    }\r\n    return Promise.all([\r\n      queryClient.prefetchQuery({\r\n        queryFn: () => delegationApiService.getAll(),\r\n        queryKey: ['delegations'],\r\n        staleTime: 5 * 60 * 1000,\r\n      }),\r\n      queryClient.prefetchQuery({\r\n        queryFn: () => employeeApiService.getAll(),\r\n        queryKey: ['employees'],\r\n        staleTime: 10 * 60 * 1000,\r\n      }),\r\n      queryClient.prefetchQuery({\r\n        queryFn: () => vehicleApiService.getAll(),\r\n        queryKey: ['vehicles'],\r\n        staleTime: 10 * 60 * 1000,\r\n      }),\r\n    ]);\r\n  },\r\n\r\n  '/delegations/add': () => {\r\n    if (!isAuthReady) {\r\n      console.warn(\r\n        'Authentication not ready, deferring delegation add data prefetch.'\r\n      );\r\n      return Promise.resolve();\r\n    }\r\n    return Promise.all([\r\n      queryClient.prefetchQuery({\r\n        queryFn: () => employeeApiService.getAll(),\r\n        queryKey: ['employees'],\r\n        staleTime: 10 * 60 * 1000,\r\n      }),\r\n      queryClient.prefetchQuery({\r\n        queryFn: () => vehicleApiService.getAll(),\r\n        queryKey: ['vehicles'],\r\n        staleTime: 10 * 60 * 1000,\r\n      }),\r\n    ]);\r\n  },\r\n\r\n  // Employee routes\r\n  '/employees': () => {\r\n    if (!isAuthReady) {\r\n      console.warn(\r\n        'Authentication not ready, deferring employees data prefetch.'\r\n      );\r\n      return Promise.resolve();\r\n    }\r\n    return queryClient.prefetchQuery({\r\n      queryFn: () => employeeApiService.getAll(),\r\n      queryKey: ['employees'],\r\n      staleTime: 10 * 60 * 1000,\r\n    });\r\n  },\r\n\r\n  '/employees/new': () => {\r\n    if (!isAuthReady) {\r\n      console.warn(\r\n        'Authentication not ready, deferring employees new data prefetch.'\r\n      );\r\n      return Promise.resolve();\r\n    }\r\n    return queryClient.prefetchQuery({\r\n      queryFn: () => employeeApiService.getAll(),\r\n      queryKey: ['employees'],\r\n      staleTime: 10 * 60 * 1000,\r\n    });\r\n  },\r\n\r\n  '/supabase-diagnostics': () => {\r\n    if (!isAuthReady) {\r\n      console.warn(\r\n        'Authentication not ready, deferring diagnostics data prefetch.'\r\n      );\r\n      return Promise.resolve();\r\n    }\r\n    return queryClient.prefetchQuery({\r\n      queryFn: async () => {\r\n        // Diagnostics now redirect to reliability dashboard\r\n        // Keep minimal prefetch for backward compatibility\r\n        return { redirectTo: '/reliability' };\r\n      },\r\n      queryKey: ['admin', 'diagnostics'],\r\n      staleTime: 30 * 1000, // 30 seconds for diagnostics\r\n    });\r\n  },\r\n\r\n  // Task routes\r\n  '/tasks': () => prefetchUtils.prefetchTaskManagementData(isAuthReady),\r\n\r\n  '/tasks/new': () => prefetchUtils.prefetchTaskManagementData(isAuthReady),\r\n\r\n  // Vehicle routes\r\n  '/vehicles': () => {\r\n    if (!isAuthReady) {\r\n      console.warn(\r\n        'Authentication not ready, deferring vehicles data prefetch.'\r\n      );\r\n      return Promise.resolve();\r\n    }\r\n    return queryClient.prefetchQuery({\r\n      queryFn: () => vehicleApiService.getAll(),\r\n      queryKey: ['vehicles'],\r\n      staleTime: 10 * 60 * 1000,\r\n    });\r\n  },\r\n\r\n  '/vehicles/new': () => {\r\n    if (!isAuthReady) {\r\n      console.warn(\r\n        'Authentication not ready, deferring vehicles new data prefetch.'\r\n      );\r\n      return Promise.resolve();\r\n    }\r\n    return Promise.all([\r\n      queryClient.prefetchQuery({\r\n        queryFn: () => vehicleApiService.getAll(),\r\n        queryKey: ['vehicles'],\r\n        staleTime: 10 * 60 * 1000,\r\n      }),\r\n    ]);\r\n  },\r\n});\r\n\r\n/**\r\n * Hook for intelligent navigation prefetching\r\n * Prefetches data for routes before user navigates to them\r\n */\r\nexport const useNavigationPrefetch = () => {\r\n  const router = useRouter();\r\n  const { isInitialized, loading } = useAuthContext();\r\n\r\n  // Determine if authentication system is ready for API calls\r\n  const isAuthReady = isInitialized && !loading;\r\n\r\n  /**\r\n   * Prefetch data for a specific route\r\n   */\r\n  const prefetchRoute = useCallback(\r\n    async (route: string) => {\r\n      try {\r\n        // Get authentication-aware prefetch patterns\r\n        const PREFETCH_PATTERNS = createPrefetchPatterns(isAuthReady);\r\n\r\n        // Find exact match first\r\n        const exactPattern =\r\n          PREFETCH_PATTERNS[route as keyof typeof PREFETCH_PATTERNS];\r\n        if (exactPattern) {\r\n          await exactPattern();\r\n          return;\r\n        }\r\n\r\n        // Check for dynamic routes (e.g., /vehicles/[id])\r\n        if (route.includes('/vehicles/') && route !== '/vehicles/new') {\r\n          const vehicleId = route.split('/vehicles/')[1]?.split('/')[0];\r\n          if (vehicleId && !isNaN(Number(vehicleId))) {\r\n            await prefetchUtils.prefetchVehicleDetails(\r\n              Number(vehicleId),\r\n              isAuthReady\r\n            );\r\n          }\r\n          return;\r\n        }\r\n\r\n        // Check for edit routes\r\n        if (route.includes('/edit')) {\r\n          const basePath = route.split('/edit')[0];\r\n          const basePattern =\r\n            PREFETCH_PATTERNS[basePath as keyof typeof PREFETCH_PATTERNS];\r\n          if (basePattern) {\r\n            await basePattern();\r\n          }\r\n          return;\r\n        }\r\n\r\n        // Fallback: prefetch common data for unknown routes\r\n        console.log(\r\n          `No specific prefetch pattern for route: ${route}, using fallback`\r\n        );\r\n      } catch (error) {\r\n        console.warn(`Failed to prefetch data for route ${route}:`, error);\r\n      }\r\n    },\r\n    [isAuthReady]\r\n  );\r\n\r\n  /**\r\n   * Enhanced navigation with prefetching\r\n   */\r\n  const navigateWithPrefetch = useCallback(\r\n    async (route: string) => {\r\n      // Start prefetching immediately\r\n      const prefetchPromise = prefetchRoute(route);\r\n\r\n      // Navigate immediately (don't wait for prefetch)\r\n      router.push(route);\r\n\r\n      // Let prefetch complete in background\r\n      prefetchPromise.catch(error => {\r\n        console.warn(`Background prefetch failed for ${route}:`, error);\r\n      });\r\n    },\r\n    [router, prefetchRoute]\r\n  );\r\n\r\n  /**\r\n   * Prefetch on hover (for link components)\r\n   */\r\n  const handleLinkHover = useCallback(\r\n    (route: string) => {\r\n      // Debounce hover prefetching to avoid excessive requests\r\n      const timeoutId = setTimeout(() => {\r\n        prefetchRoute(route);\r\n      }, 100);\r\n\r\n      return () => clearTimeout(timeoutId);\r\n    },\r\n    [prefetchRoute]\r\n  );\r\n\r\n  /**\r\n   * Prefetch multiple routes (for anticipated user journeys)\r\n   */\r\n  const prefetchUserJourney = useCallback(\r\n    async (routes: readonly string[]) => {\r\n      const prefetchPromises = routes.map(route => prefetchRoute(route));\r\n      await Promise.allSettled(prefetchPromises);\r\n    },\r\n    [prefetchRoute]\r\n  );\r\n\r\n  return {\r\n    handleLinkHover,\r\n    navigateWithPrefetch,\r\n    prefetchRoute,\r\n    prefetchUserJourney,\r\n  };\r\n};\r\n\r\n/**\r\n * Common user journey patterns for bulk prefetching\r\n */\r\nexport const USER_JOURNEYS = {\r\n  // Admin workflow\r\n  ADMIN_WORKFLOW: [\r\n    '/admin',\r\n    '/admin/users',\r\n    '/admin/audit',\r\n    '/supabase-diagnostics',\r\n  ],\r\n\r\n  // Dashboard to detail views\r\n  DASHBOARD_DRILL_DOWN: [\r\n    '/',\r\n    '/vehicles',\r\n    '/tasks',\r\n    '/delegations',\r\n    '/employees',\r\n  ],\r\n\r\n  // Delegation workflow\r\n  DELEGATION_WORKFLOW: [\r\n    '/delegations',\r\n    '/delegations/add',\r\n    '/employees',\r\n    '/vehicles',\r\n  ],\r\n\r\n  // Task management workflow\r\n  TASK_MANAGEMENT: ['/tasks', '/tasks/new', '/employees'],\r\n\r\n  // Vehicle management workflow\r\n  VEHICLE_MANAGEMENT: ['/vehicles', '/vehicles/new'],\r\n} as const;\r\n\r\n/**\r\n * Hook for prefetching common user journeys\r\n */\r\nexport const useJourneyPrefetch = () => {\r\n  const { prefetchUserJourney } = useNavigationPrefetch();\r\n\r\n  const prefetchVehicleManagement = useCallback(() => {\r\n    return prefetchUserJourney(USER_JOURNEYS.VEHICLE_MANAGEMENT);\r\n  }, [prefetchUserJourney]);\r\n\r\n  const prefetchTaskManagement = useCallback(() => {\r\n    return prefetchUserJourney(USER_JOURNEYS.TASK_MANAGEMENT);\r\n  }, [prefetchUserJourney]);\r\n\r\n  const prefetchDelegationWorkflow = useCallback(() => {\r\n    return prefetchUserJourney(USER_JOURNEYS.DELEGATION_WORKFLOW);\r\n  }, [prefetchUserJourney]);\r\n\r\n  const prefetchDashboardDrillDown = useCallback(() => {\r\n    return prefetchUserJourney(USER_JOURNEYS.DASHBOARD_DRILL_DOWN);\r\n  }, [prefetchUserJourney]);\r\n\r\n  const prefetchAdminWorkflow = useCallback(() => {\r\n    return prefetchUserJourney(USER_JOURNEYS.ADMIN_WORKFLOW);\r\n  }, [prefetchUserJourney]);\r\n\r\n  return {\r\n    prefetchAdminWorkflow,\r\n    prefetchDashboardDrillDown,\r\n    prefetchDelegationWorkflow,\r\n    prefetchTaskManagement,\r\n    prefetchVehicleManagement,\r\n  };\r\n};\r\n\r\nconst getRecentErrors = () => {\r\n  return [];\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AAGA;AAAA;AAMA;AACA;;;;;;AAEA;;;CAGC,GACD,MAAM,yBAAyB,CAAC,cAAyB,CAAC;QACxD,+CAA+C;QAC/C,KAAK,IAAM,mIAAA,CAAA,gBAAa,CAAC,qBAAqB,CAAC;QAE/C,4DAA4D;QAC5D,UAAU;YACR,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CAAC;gBACb,OAAO,QAAQ,OAAO;YACxB;YACA,OAAO,QAAQ,GAAG,CAAC;gBACjB,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;oBACxB,SAAS,IAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM;oBACxC,UAAU;wBAAC;wBAAS;qBAAQ;oBAC5B,WAAW,IAAI,KAAK;gBACtB;aAGD;QACH;QAEA,gBAAgB;YACd,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CACV;gBAEF,OAAO,QAAQ,OAAO;YACxB;YACA,OAAO,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;gBAC/B,SAAS;oBACP,MAAM,EAAE,YAAY,EAAE,GAAG;oBACzB,OAAO,aAAa,eAAe;gBACrC;gBACA,UAAU;oBAAC;oBAAS;iBAAQ;gBAC5B,WAAW,IAAI,KAAK;YACtB;QACF;QAEA,8DAA8D;QAC9D,gBAAgB;YACd,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CACV;gBAEF,OAAO,QAAQ,OAAO;YACxB;YACA,OAAO,QAAQ,GAAG,CAAC;gBACjB,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;oBACxB,SAAS;wBACP,MAAM,EAAE,qBAAqB,EAAE,GAAG;wBAGlC,8BAA8B;wBAC9B,OAAO,sBAAsB,eAAe;oBAC9C;oBACA,UAAU;wBAAC;wBAAe;qBAAS;oBACnC,WAAW,KAAK;gBAClB;gBACA,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;oBACxB,SAAS;wBACP,MAAM,EAAE,qBAAqB,EAAE,GAAG;wBAGlC,gCAAgC;wBAChC,OAAO,sBAAsB,uBAAuB;oBACtD;oBACA,UAAU;wBAAC;wBAAe;qBAAmB;oBAC7C,WAAW,KAAK;gBAClB;aACD;QACH;QAEA,gBAAgB;YACd,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CACV;gBAEF,OAAO,QAAQ,OAAO;YACxB;YACA,OAAO,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;gBAC/B,SAAS,IAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM;gBACxC,UAAU;oBAAC;oBAAS;iBAAQ;gBAC5B,WAAW,IAAI,KAAK;YACtB;QACF;QAEA,oBAAoB;QACpB,gBAAgB;YACd,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CACV;gBAEF,OAAO,QAAQ,OAAO;YACxB;YACA,OAAO,QAAQ,GAAG,CAAC;gBACjB,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;oBACxB,SAAS,IAAM,wIAAA,CAAA,uBAAoB,CAAC,MAAM;oBAC1C,UAAU;wBAAC;qBAAc;oBACzB,WAAW,IAAI,KAAK;gBACtB;gBACA,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;oBACxB,SAAS,IAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM;oBACxC,UAAU;wBAAC;qBAAY;oBACvB,WAAW,KAAK,KAAK;gBACvB;gBACA,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;oBACxB,SAAS,IAAM,wIAAA,CAAA,oBAAiB,CAAC,MAAM;oBACvC,UAAU;wBAAC;qBAAW;oBACtB,WAAW,KAAK,KAAK;gBACvB;aACD;QACH;QAEA,oBAAoB;YAClB,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CACV;gBAEF,OAAO,QAAQ,OAAO;YACxB;YACA,OAAO,QAAQ,GAAG,CAAC;gBACjB,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;oBACxB,SAAS,IAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM;oBACxC,UAAU;wBAAC;qBAAY;oBACvB,WAAW,KAAK,KAAK;gBACvB;gBACA,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;oBACxB,SAAS,IAAM,wIAAA,CAAA,oBAAiB,CAAC,MAAM;oBACvC,UAAU;wBAAC;qBAAW;oBACtB,WAAW,KAAK,KAAK;gBACvB;aACD;QACH;QAEA,kBAAkB;QAClB,cAAc;YACZ,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CACV;gBAEF,OAAO,QAAQ,OAAO;YACxB;YACA,OAAO,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;gBAC/B,SAAS,IAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM;gBACxC,UAAU;oBAAC;iBAAY;gBACvB,WAAW,KAAK,KAAK;YACvB;QACF;QAEA,kBAAkB;YAChB,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CACV;gBAEF,OAAO,QAAQ,OAAO;YACxB;YACA,OAAO,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;gBAC/B,SAAS,IAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM;gBACxC,UAAU;oBAAC;iBAAY;gBACvB,WAAW,KAAK,KAAK;YACvB;QACF;QAEA,yBAAyB;YACvB,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CACV;gBAEF,OAAO,QAAQ,OAAO;YACxB;YACA,OAAO,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;gBAC/B,SAAS;oBACP,oDAAoD;oBACpD,mDAAmD;oBACnD,OAAO;wBAAE,YAAY;oBAAe;gBACtC;gBACA,UAAU;oBAAC;oBAAS;iBAAc;gBAClC,WAAW,KAAK;YAClB;QACF;QAEA,cAAc;QACd,UAAU,IAAM,mIAAA,CAAA,gBAAa,CAAC,0BAA0B,CAAC;QAEzD,cAAc,IAAM,mIAAA,CAAA,gBAAa,CAAC,0BAA0B,CAAC;QAE7D,iBAAiB;QACjB,aAAa;YACX,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CACV;gBAEF,OAAO,QAAQ,OAAO;YACxB;YACA,OAAO,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;gBAC/B,SAAS,IAAM,wIAAA,CAAA,oBAAiB,CAAC,MAAM;gBACvC,UAAU;oBAAC;iBAAW;gBACtB,WAAW,KAAK,KAAK;YACvB;QACF;QAEA,iBAAiB;YACf,IAAI,CAAC,aAAa;gBAChB,QAAQ,IAAI,CACV;gBAEF,OAAO,QAAQ,OAAO;YACxB;YACA,OAAO,QAAQ,GAAG,CAAC;gBACjB,mIAAA,CAAA,cAAW,CAAC,aAAa,CAAC;oBACxB,SAAS,IAAM,wIAAA,CAAA,oBAAiB,CAAC,MAAM;oBACvC,UAAU;wBAAC;qBAAW;oBACtB,WAAW,KAAK,KAAK;gBACvB;aACD;QACH;IACF,CAAC;AAMM,MAAM,wBAAwB;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAEhD,4DAA4D;IAC5D,MAAM,cAAc,iBAAiB,CAAC;IAEtC;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,OAAO;QACL,IAAI;YACF,6CAA6C;YAC7C,MAAM,oBAAoB,uBAAuB;YAEjD,yBAAyB;YACzB,MAAM,eACJ,iBAAiB,CAAC,MAAwC;YAC5D,IAAI,cAAc;gBAChB,MAAM;gBACN;YACF;YAEA,kDAAkD;YAClD,IAAI,MAAM,QAAQ,CAAC,iBAAiB,UAAU,iBAAiB;gBAC7D,MAAM,YAAY,MAAM,KAAK,CAAC,aAAa,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,EAAE;gBAC7D,IAAI,aAAa,CAAC,MAAM,OAAO,aAAa;oBAC1C,MAAM,mIAAA,CAAA,gBAAa,CAAC,sBAAsB,CACxC,OAAO,YACP;gBAEJ;gBACA;YACF;YAEA,wBAAwB;YACxB,IAAI,MAAM,QAAQ,CAAC,UAAU;gBAC3B,MAAM,WAAW,MAAM,KAAK,CAAC,QAAQ,CAAC,EAAE;gBACxC,MAAM,cACJ,iBAAiB,CAAC,SAA2C;gBAC/D,IAAI,aAAa;oBACf,MAAM;gBACR;gBACA;YACF;YAEA,oDAAoD;YACpD,QAAQ,GAAG,CACT,CAAC,wCAAwC,EAAE,MAAM,gBAAgB,CAAC;QAEtE,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,CAAC,kCAAkC,EAAE,MAAM,CAAC,CAAC,EAAE;QAC9D;IACF,GACA;QAAC;KAAY;IAGf;;GAEC,GACD,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,OAAO;QACL,gCAAgC;QAChC,MAAM,kBAAkB,cAAc;QAEtC,iDAAiD;QACjD,OAAO,IAAI,CAAC;QAEZ,sCAAsC;QACtC,gBAAgB,KAAK,CAAC,CAAA;YACpB,QAAQ,IAAI,CAAC,CAAC,+BAA+B,EAAE,MAAM,CAAC,CAAC,EAAE;QAC3D;IACF,GACA;QAAC;QAAQ;KAAc;IAGzB;;GAEC,GACD,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC;QACC,yDAAyD;QACzD,MAAM,YAAY,WAAW;YAC3B,cAAc;QAChB,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GACA;QAAC;KAAc;IAGjB;;GAEC,GACD,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,OAAO;QACL,MAAM,mBAAmB,OAAO,GAAG,CAAC,CAAA,QAAS,cAAc;QAC3D,MAAM,QAAQ,UAAU,CAAC;IAC3B,GACA;QAAC;KAAc;IAGjB,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAKO,MAAM,gBAAgB;IAC3B,iBAAiB;IACjB,gBAAgB;QACd;QACA;QACA;QACA;KACD;IAED,4BAA4B;IAC5B,sBAAsB;QACpB;QACA;QACA;QACA;QACA;KACD;IAED,sBAAsB;IACtB,qBAAqB;QACnB;QACA;QACA;QACA;KACD;IAED,2BAA2B;IAC3B,iBAAiB;QAAC;QAAU;QAAc;KAAa;IAEvD,8BAA8B;IAC9B,oBAAoB;QAAC;QAAa;KAAgB;AACpD;AAKO,MAAM,qBAAqB;IAChC,MAAM,EAAE,mBAAmB,EAAE,GAAG;IAEhC,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5C,OAAO,oBAAoB,cAAc,kBAAkB;IAC7D,GAAG;QAAC;KAAoB;IAExB,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,OAAO,oBAAoB,cAAc,eAAe;IAC1D,GAAG;QAAC;KAAoB;IAExB,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7C,OAAO,oBAAoB,cAAc,mBAAmB;IAC9D,GAAG;QAAC;KAAoB;IAExB,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7C,OAAO,oBAAoB,cAAc,oBAAoB;IAC/D,GAAG;QAAC;KAAoB;IAExB,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,OAAO,oBAAoB,cAAc,cAAc;IACzD,GAAG;QAAC;KAAoB;IAExB,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,MAAM,kBAAkB;IACtB,OAAO,EAAE;AACX", "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/api/useQueryOptimization.ts"], "sourcesContent": ["/**\r\n * @file Query optimization hook for enhanced performance\r\n * @module hooks/useQueryOptimization\r\n */\r\n\r\nimport type {\r\n  UseMutationOptions,\r\n  UseQueryOptions} from '@tanstack/react-query';\r\n\r\nimport {\r\n  useQueryClient\r\n} from '@tanstack/react-query';\r\nimport { useCallback } from 'react';\r\n\r\n/**\r\n * Configuration for query optimization\r\n */\r\ninterface QueryOptimizationConfig {\r\n  /**\r\n   * Custom cache time override\r\n   */\r\n  customCacheTime?: number;\r\n\r\n  /**\r\n   * Custom stale time override\r\n   */\r\n  customStaleTime?: number;\r\n\r\n  /**\r\n   * Data type for optimized cache settings\r\n   */\r\n  dataType?: 'critical' | 'dynamic' | 'realtime' | 'static';\r\n\r\n  /**\r\n   * Enable background refetching\r\n   */\r\n  enableBackgroundRefetch?: boolean;\r\n\r\n  /**\r\n   * Enable optimistic updates for mutations\r\n   */\r\n  enableOptimisticUpdates?: boolean;\r\n}\r\n\r\n/**\r\n * Optimized query options based on data type\r\n */\r\nconst OPTIMIZATION_PRESETS = {\r\n  critical: {\r\n    gcTime: 2 * 60 * 1000, // 2 minutes\r\n    refetchInterval: false,\r\n    refetchOnReconnect: true,\r\n    refetchOnWindowFocus: true,\r\n    staleTime: 0, // Always fresh\r\n  },\r\n  dynamic: {\r\n    gcTime: 15 * 60 * 1000, // 15 minutes\r\n    refetchInterval: false,\r\n    refetchOnReconnect: true,\r\n    refetchOnWindowFocus: true,\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n  },\r\n  realtime: {\r\n    gcTime: 5 * 60 * 1000, // 5 minutes\r\n    refetchInterval: false,\r\n    refetchOnReconnect: true,\r\n    refetchOnWindowFocus: true,\r\n    staleTime: 30 * 1000, // 30 seconds\r\n  },\r\n  static: {\r\n    gcTime: 60 * 60 * 1000, // 1 hour\r\n    refetchInterval: false,\r\n    refetchOnReconnect: false,\r\n    refetchOnWindowFocus: false,\r\n    staleTime: 30 * 60 * 1000, // 30 minutes\r\n  },\r\n} as const;\r\n\r\n/**\r\n * Hook for query optimization with intelligent caching strategies\r\n */\r\nexport const useQueryOptimization = (config: QueryOptimizationConfig = {}) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  const {\r\n    customCacheTime,\r\n    customStaleTime,\r\n    dataType = 'dynamic',\r\n    enableBackgroundRefetch = true,\r\n    enableOptimisticUpdates = true,\r\n  } = config;\r\n\r\n  /**\r\n   * Get optimized query options based on data type\r\n   */\r\n  const getOptimizedQueryOptions = useCallback(\r\n    <T = unknown>(\r\n      baseOptions: Partial<UseQueryOptions<T>> = {}\r\n    ): Partial<UseQueryOptions<T>> => {\r\n      const preset = OPTIMIZATION_PRESETS[dataType];\r\n\r\n      return {\r\n        ...baseOptions,\r\n        gcTime: customCacheTime ?? preset.gcTime,\r\n        refetchInterval: enableBackgroundRefetch\r\n          ? preset.refetchInterval\r\n          : false,\r\n        refetchOnReconnect: enableBackgroundRefetch\r\n          ? preset.refetchOnReconnect\r\n          : false,\r\n        refetchOnWindowFocus: enableBackgroundRefetch\r\n          ? preset.refetchOnWindowFocus\r\n          : false,\r\n        // Enhanced error handling\r\n        retry: (failureCount, error) => {\r\n          // Don't retry on 4xx errors\r\n          if (error && typeof error === 'object' && 'status' in error) {\r\n            const status = (error as any).status;\r\n            if (status >= 400 && status < 500) {\r\n              return false;\r\n            }\r\n          }\r\n          return failureCount < 3;\r\n        },\r\n\r\n        // Exponential backoff\r\n        retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),\r\n\r\n        staleTime: customStaleTime ?? preset.staleTime,\r\n      };\r\n    },\r\n    [dataType, enableBackgroundRefetch, customStaleTime, customCacheTime]\r\n  );\r\n\r\n  /**\r\n   * Get optimized mutation options with optimistic updates\r\n   */\r\n  const getOptimizedMutationOptions = useCallback(\r\n    <T = unknown, E = unknown, V = unknown>(\r\n      baseMutationOptions: UseMutationOptions<T, E, V> = {},\r\n      optimisticUpdateConfig?: {\r\n        queryKey: unknown[];\r\n        updateFn: (oldData: any, variables: V) => any;\r\n      }\r\n    ): UseMutationOptions<T, E, V> => {\r\n      const options: UseMutationOptions<T, E, V> = {\r\n        ...baseMutationOptions,\r\n\r\n        // Enhanced retry logic for mutations\r\n        retry: (failureCount, error) => {\r\n          // Generally don't retry mutations to avoid side effects\r\n          // Only retry on network errors\r\n          if (error && typeof error === 'object' && 'message' in error) {\r\n            const message = (error as any).message?.toLowerCase() || '';\r\n            if (message.includes('network') || message.includes('timeout')) {\r\n              return failureCount < 1; // Only retry once for network errors\r\n            }\r\n          }\r\n          return false;\r\n        },\r\n      };\r\n\r\n      // Add optimistic updates if enabled and config provided\r\n      if (enableOptimisticUpdates && optimisticUpdateConfig) {\r\n        const { queryKey, updateFn } = optimisticUpdateConfig;\r\n\r\n        options.onMutate = async variables => {\r\n          // Cancel any outgoing refetches\r\n          await queryClient.cancelQueries({ queryKey });\r\n\r\n          // Snapshot the previous value\r\n          const previousData = queryClient.getQueryData(queryKey);\r\n\r\n          // Optimistically update to the new value\r\n          queryClient.setQueryData(queryKey, (old: any) =>\r\n            updateFn(old, variables)\r\n          );\r\n\r\n          // Return a context object with the snapshotted value\r\n          return { previousData };\r\n        };\r\n\r\n        options.onError = (err, variables, context) => {\r\n          // If the mutation fails, use the context returned from onMutate to roll back\r\n          if (\r\n            context &&\r\n            typeof context === 'object' &&\r\n            'previousData' in context\r\n          ) {\r\n            queryClient.setQueryData(queryKey, (context as any).previousData);\r\n          }\r\n\r\n          // Call original onError if provided\r\n          baseMutationOptions.onError?.(err, variables, context);\r\n        };\r\n\r\n        options.onSettled = (data, error, variables, context) => {\r\n          // Always refetch after error or success to ensure we have the latest data\r\n          queryClient.invalidateQueries({ queryKey });\r\n\r\n          // Call original onSettled if provided\r\n          baseMutationOptions.onSettled?.(data, error, variables, context);\r\n        };\r\n      }\r\n\r\n      return options;\r\n    },\r\n    [queryClient, enableOptimisticUpdates]\r\n  );\r\n\r\n  /**\r\n   * Prefetch related data based on current query\r\n   */\r\n  const prefetchRelatedData = useCallback(\r\n    async (\r\n      _currentQueryKey: unknown[],\r\n      relatedQueries: {\r\n        queryFn: () => Promise<any>;\r\n        queryKey: unknown[];\r\n        staleTime?: number;\r\n      }[]\r\n    ) => {\r\n      const prefetchPromises = relatedQueries.map(\r\n        ({ queryFn, queryKey, staleTime }) =>\r\n          queryClient.prefetchQuery({\r\n            queryFn,\r\n            queryKey,\r\n            staleTime: staleTime ?? OPTIMIZATION_PRESETS[dataType].staleTime,\r\n          })\r\n      );\r\n\r\n      await Promise.allSettled(prefetchPromises);\r\n    },\r\n    [queryClient, dataType]\r\n  );\r\n\r\n  /**\r\n   * Intelligent cache invalidation\r\n   */\r\n  const invalidateRelatedQueries = useCallback(\r\n    (baseQueryKey: unknown[], relatedPatterns: string[] = []) => {\r\n      // Invalidate the base query\r\n      queryClient.invalidateQueries({ queryKey: baseQueryKey });\r\n\r\n      // Invalidate related queries based on patterns\r\n      for (const pattern of relatedPatterns) {\r\n        queryClient.invalidateQueries({\r\n          predicate: query => {\r\n            const keyString = JSON.stringify(query.queryKey);\r\n            return keyString.includes(pattern);\r\n          },\r\n        });\r\n      }\r\n    },\r\n    [queryClient]\r\n  );\r\n\r\n  /**\r\n   * Batch multiple cache updates\r\n   */\r\n  const batchCacheUpdates = useCallback(\r\n    (\r\n      updates: {\r\n        queryKey: unknown[];\r\n        updateFn: (oldData: any) => any;\r\n      }[]\r\n    ) => {\r\n      // Batch updates manually since batch method may not be available\r\n      for (const { queryKey, updateFn } of updates) {\r\n        queryClient.setQueryData(queryKey, updateFn);\r\n      }\r\n    },\r\n    [queryClient]\r\n  );\r\n\r\n  /**\r\n   * Get cache statistics for monitoring\r\n   */\r\n  const getCacheStats = useCallback(() => {\r\n    const cache = queryClient.getQueryCache();\r\n    const queries = cache.getAll();\r\n\r\n    const stats = {\r\n      activeQueries: queries.filter(q => q.getObserversCount() > 0).length,\r\n      errorQueries: queries.filter(q => q.state.status === 'error').length,\r\n      loadingQueries: queries.filter(q => q.state.status === 'pending').length,\r\n      staleQueries: queries.filter(q => q.isStale()).length,\r\n      totalQueries: queries.length,\r\n    };\r\n\r\n    return stats;\r\n  }, [queryClient]);\r\n\r\n  return {\r\n    batchCacheUpdates,\r\n    getCacheStats,\r\n    getOptimizedMutationOptions,\r\n    getOptimizedQueryOptions,\r\n    invalidateRelatedQueries,\r\n    prefetchRelatedData,\r\n  };\r\n};\r\n\r\n/**\r\n * Specialized hooks for different data types\r\n */\r\n\r\n/**\r\n * Hook for static data (rarely changes)\r\n */\r\nexport const useStaticDataOptimization = () => {\r\n  return useQueryOptimization({\r\n    dataType: 'static',\r\n    enableBackgroundRefetch: false,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for dynamic data (changes moderately)\r\n */\r\nexport const useDynamicDataOptimization = () => {\r\n  return useQueryOptimization({\r\n    dataType: 'dynamic',\r\n    enableBackgroundRefetch: true,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for real-time data (changes frequently)\r\n */\r\nexport const useRealTimeDataOptimization = () => {\r\n  return useQueryOptimization({\r\n    dataType: 'realtime',\r\n    enableBackgroundRefetch: true,\r\n    enableOptimisticUpdates: true,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for critical data (always fresh)\r\n */\r\nexport const useCriticalDataOptimization = () => {\r\n  return useQueryOptimization({\r\n    dataType: 'critical',\r\n    enableBackgroundRefetch: true,\r\n    enableOptimisticUpdates: true,\r\n  });\r\n};\r\n\r\n/**\r\n * Performance monitoring hook for queries\r\n */\r\nexport const useQueryPerformanceMonitor = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  const getSlowQueries = useCallback(\r\n    (thresholdMs = 2000) => {\r\n      const cache = queryClient.getQueryCache();\r\n      const queries = cache.getAll();\r\n\r\n      return queries\r\n        .filter(query => {\r\n          // Use dataUpdatedAt as a proxy for query duration\r\n          const now = Date.now();\r\n          const lastUpdate = query.state.dataUpdatedAt || 0;\r\n          const duration = now - lastUpdate;\r\n          return duration > thresholdMs && query.state.status === 'success';\r\n        })\r\n        .map(query => ({\r\n          duration: Date.now() - (query.state.dataUpdatedAt || 0),\r\n          queryKey: query.queryKey,\r\n          status: query.state.status,\r\n        }));\r\n    },\r\n    [queryClient]\r\n  );\r\n\r\n  const getCacheEfficiency = useCallback(() => {\r\n    const cache = queryClient.getQueryCache();\r\n    const queries = cache.getAll();\r\n\r\n    const totalQueries = queries.length;\r\n    const cachedQueries = queries.filter(\r\n      q => q.state.data !== undefined\r\n    ).length;\r\n\r\n    return totalQueries > 0 ? (cachedQueries / totalQueries) * 100 : 0;\r\n  }, [queryClient]);\r\n\r\n  return {\r\n    getCacheEfficiency,\r\n    getSlowQueries,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAMD;AAGA;;;AAgCA;;CAEC,GACD,MAAM,uBAAuB;IAC3B,UAAU;QACR,QAAQ,IAAI,KAAK;QACjB,iBAAiB;QACjB,oBAAoB;QACpB,sBAAsB;QACtB,WAAW;IACb;IACA,SAAS;QACP,QAAQ,KAAK,KAAK;QAClB,iBAAiB;QACjB,oBAAoB;QACpB,sBAAsB;QACtB,WAAW,IAAI,KAAK;IACtB;IACA,UAAU;QACR,QAAQ,IAAI,KAAK;QACjB,iBAAiB;QACjB,oBAAoB;QACpB,sBAAsB;QACtB,WAAW,KAAK;IAClB;IACA,QAAQ;QACN,QAAQ,KAAK,KAAK;QAClB,iBAAiB;QACjB,oBAAoB;QACpB,sBAAsB;QACtB,WAAW,KAAK,KAAK;IACvB;AACF;AAKO,MAAM,uBAAuB,CAAC,SAAkC,CAAC,CAAC;IACvE,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,EACJ,eAAe,EACf,eAAe,EACf,WAAW,SAAS,EACpB,0BAA0B,IAAI,EAC9B,0BAA0B,IAAI,EAC/B,GAAG;IAEJ;;GAEC,GACD,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACzC,CACE,cAA2C,CAAC,CAAC;QAE7C,MAAM,SAAS,oBAAoB,CAAC,SAAS;QAE7C,OAAO;YACL,GAAG,WAAW;YACd,QAAQ,mBAAmB,OAAO,MAAM;YACxC,iBAAiB,0BACb,OAAO,eAAe,GACtB;YACJ,oBAAoB,0BAChB,OAAO,kBAAkB,GACzB;YACJ,sBAAsB,0BAClB,OAAO,oBAAoB,GAC3B;YACJ,0BAA0B;YAC1B,OAAO,CAAC,cAAc;gBACpB,4BAA4B;gBAC5B,IAAI,SAAS,OAAO,UAAU,YAAY,YAAY,OAAO;oBAC3D,MAAM,SAAS,AAAC,MAAc,MAAM;oBACpC,IAAI,UAAU,OAAO,SAAS,KAAK;wBACjC,OAAO;oBACT;gBACF;gBACA,OAAO,eAAe;YACxB;YAEA,sBAAsB;YACtB,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;YAE/D,WAAW,mBAAmB,OAAO,SAAS;QAChD;IACF,GACA;QAAC;QAAU;QAAyB;QAAiB;KAAgB;IAGvE;;GAEC,GACD,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5C,CACE,sBAAmD,CAAC,CAAC,EACrD;QAKA,MAAM,UAAuC;YAC3C,GAAG,mBAAmB;YAEtB,qCAAqC;YACrC,OAAO,CAAC,cAAc;gBACpB,wDAAwD;gBACxD,+BAA+B;gBAC/B,IAAI,SAAS,OAAO,UAAU,YAAY,aAAa,OAAO;oBAC5D,MAAM,UAAU,AAAC,MAAc,OAAO,EAAE,iBAAiB;oBACzD,IAAI,QAAQ,QAAQ,CAAC,cAAc,QAAQ,QAAQ,CAAC,YAAY;wBAC9D,OAAO,eAAe,GAAG,qCAAqC;oBAChE;gBACF;gBACA,OAAO;YACT;QACF;QAEA,wDAAwD;QACxD,IAAI,2BAA2B,wBAAwB;YACrD,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;YAE/B,QAAQ,QAAQ,GAAG,OAAM;gBACvB,gCAAgC;gBAChC,MAAM,YAAY,aAAa,CAAC;oBAAE;gBAAS;gBAE3C,8BAA8B;gBAC9B,MAAM,eAAe,YAAY,YAAY,CAAC;gBAE9C,yCAAyC;gBACzC,YAAY,YAAY,CAAC,UAAU,CAAC,MAClC,SAAS,KAAK;gBAGhB,qDAAqD;gBACrD,OAAO;oBAAE;gBAAa;YACxB;YAEA,QAAQ,OAAO,GAAG,CAAC,KAAK,WAAW;gBACjC,6EAA6E;gBAC7E,IACE,WACA,OAAO,YAAY,YACnB,kBAAkB,SAClB;oBACA,YAAY,YAAY,CAAC,UAAU,AAAC,QAAgB,YAAY;gBAClE;gBAEA,oCAAoC;gBACpC,oBAAoB,OAAO,GAAG,KAAK,WAAW;YAChD;YAEA,QAAQ,SAAS,GAAG,CAAC,MAAM,OAAO,WAAW;gBAC3C,0EAA0E;gBAC1E,YAAY,iBAAiB,CAAC;oBAAE;gBAAS;gBAEzC,sCAAsC;gBACtC,oBAAoB,SAAS,GAAG,MAAM,OAAO,WAAW;YAC1D;QACF;QAEA,OAAO;IACT,GACA;QAAC;QAAa;KAAwB;IAGxC;;GAEC,GACD,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,OACE,kBACA;QAMA,MAAM,mBAAmB,eAAe,GAAG,CACzC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,GAC/B,YAAY,aAAa,CAAC;gBACxB;gBACA;gBACA,WAAW,aAAa,oBAAoB,CAAC,SAAS,CAAC,SAAS;YAClE;QAGJ,MAAM,QAAQ,UAAU,CAAC;IAC3B,GACA;QAAC;QAAa;KAAS;IAGzB;;GAEC,GACD,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACzC,CAAC,cAAyB,kBAA4B,EAAE;QACtD,4BAA4B;QAC5B,YAAY,iBAAiB,CAAC;YAAE,UAAU;QAAa;QAEvD,+CAA+C;QAC/C,KAAK,MAAM,WAAW,gBAAiB;YACrC,YAAY,iBAAiB,CAAC;gBAC5B,WAAW,CAAA;oBACT,MAAM,YAAY,KAAK,SAAS,CAAC,MAAM,QAAQ;oBAC/C,OAAO,UAAU,QAAQ,CAAC;gBAC5B;YACF;QACF;IACF,GACA;QAAC;KAAY;IAGf;;GAEC,GACD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CACE;QAKA,iEAAiE;QACjE,KAAK,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,QAAS;YAC5C,YAAY,YAAY,CAAC,UAAU;QACrC;IACF,GACA;QAAC;KAAY;IAGf;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,MAAM,QAAQ,YAAY,aAAa;QACvC,MAAM,UAAU,MAAM,MAAM;QAE5B,MAAM,QAAQ;YACZ,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,iBAAiB,KAAK,GAAG,MAAM;YACpE,cAAc,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK,SAAS,MAAM;YACpE,gBAAgB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,KAAK,WAAW,MAAM;YACxE,cAAc,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,MAAM;YACrD,cAAc,QAAQ,MAAM;QAC9B;QAEA,OAAO;IACT,GAAG;QAAC;KAAY;IAEhB,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AASO,MAAM,4BAA4B;IACvC,OAAO,qBAAqB;QAC1B,UAAU;QACV,yBAAyB;IAC3B;AACF;AAKO,MAAM,6BAA6B;IACxC,OAAO,qBAAqB;QAC1B,UAAU;QACV,yBAAyB;IAC3B;AACF;AAKO,MAAM,8BAA8B;IACzC,OAAO,qBAAqB;QAC1B,UAAU;QACV,yBAAyB;QACzB,yBAAyB;IAC3B;AACF;AAKO,MAAM,8BAA8B;IACzC,OAAO,qBAAqB;QAC1B,UAAU;QACV,yBAAyB;QACzB,yBAAyB;IAC3B;AACF;AAKO,MAAM,6BAA6B;IACxC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC/B,CAAC,cAAc,IAAI;QACjB,MAAM,QAAQ,YAAY,aAAa;QACvC,MAAM,UAAU,MAAM,MAAM;QAE5B,OAAO,QACJ,MAAM,CAAC,CAAA;YACN,kDAAkD;YAClD,MAAM,MAAM,KAAK,GAAG;YACpB,MAAM,aAAa,MAAM,KAAK,CAAC,aAAa,IAAI;YAChD,MAAM,WAAW,MAAM;YACvB,OAAO,WAAW,eAAe,MAAM,KAAK,CAAC,MAAM,KAAK;QAC1D,GACC,GAAG,CAAC,CAAA,QAAS,CAAC;gBACb,UAAU,KAAK,GAAG,KAAK,CAAC,MAAM,KAAK,CAAC,aAAa,IAAI,CAAC;gBACtD,UAAU,MAAM,QAAQ;gBACxB,QAAQ,MAAM,KAAK,CAAC,MAAM;YAC5B,CAAC;IACL,GACA;QAAC;KAAY;IAGf,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,MAAM,QAAQ,YAAY,aAAa;QACvC,MAAM,UAAU,MAAM,MAAM;QAE5B,MAAM,eAAe,QAAQ,MAAM;QACnC,MAAM,gBAAgB,QAAQ,MAAM,CAClC,CAAA,IAAK,EAAE,KAAK,CAAC,IAAI,KAAK,WACtB,MAAM;QAER,OAAO,eAAe,IAAI,AAAC,gBAAgB,eAAgB,MAAM;IACnE,GAAG;QAAC;KAAY;IAEhB,OAAO;QACL;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 958, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/api/useSmartQuery.ts"], "sourcesContent": ["/**\r\n * @file Smart Query Hook with WebSocket Integration\r\n * Automatically disables polling when WebSocket is connected\r\n * Follows modern best practices for real-time data management\r\n * @module hooks/useSmartQuery\r\n */\r\n\r\nimport type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query';\r\n\r\nimport { useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { useEffect, useState } from 'react';\r\n\r\nimport type { DomainChannel } from '../../lib/services/WebSocketManager';\r\n\r\nimport { getWebSocketManager } from '../../lib/services/WebSocketManager';\r\n\r\n/**\r\n * Mapping of domain channels to Socket.IO room names\r\n * This ensures the frontend joins the correct rooms that the backend emits events to\r\n */\r\nconst CHANNEL_ROOM_MAPPING: Record<DomainChannel, string> = {\r\n  crud: 'entity-updates',\r\n  notifications: 'notifications-monitoring',\r\n  reliability: 'reliability-monitoring',\r\n  system: 'system-monitoring',\r\n} as const;\r\n\r\n/**\r\n * Smart query configuration for WebSocket integration\r\n * @template T - The data type returned by the query function\r\n */\r\nexport interface SmartQueryConfig<T = unknown> {\r\n  /**\r\n   * Domain channel for WebSocket events\r\n   * Automatically maps to appropriate Socket.IO room via CHANNEL_ROOM_MAPPING\r\n   */\r\n  channel: DomainChannel;\r\n  /** Whether to enable fallback polling when WebSocket is disconnected */\r\n  enableFallback?: boolean;\r\n  /** Whether to enable WebSocket integration and room joining */\r\n  enableWebSocket?: boolean;\r\n  /** Events that should trigger data refetch when received via WebSocket */\r\n  events: string[];\r\n  /** Fallback polling interval when WebSocket is disconnected (ms) */\r\n  fallbackInterval?: number;\r\n}\r\n\r\n/**\r\n * Hook for CRUD operations with smart real-time updates\r\n */\r\nexport function useCrudQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  entityType: string,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'crud',\r\n      events: [\r\n        `${entityType}:created`,\r\n        `${entityType}:updated`,\r\n        `${entityType}:deleted`,\r\n        `refresh:${entityType}`,\r\n      ],\r\n      fallbackInterval: 30_000,\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Hook for system notifications with smart real-time updates\r\n */\r\nexport function useNotificationQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'notifications',\r\n      events: ['notification-created', 'notification-updated'],\r\n      fallbackInterval: 60_000, // 1 minute for notifications\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Hook for reliability monitoring with smart real-time updates\r\n */\r\nexport function useReliabilityQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  monitoringType: 'alerts' | 'circuit-breakers' | 'health' | 'metrics',\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  // Increased intervals to reduce aggressive polling and cancellations\r\n  const intervalMap = {\r\n    alerts: 30_000, // 30 seconds for alerts (was 10s)\r\n    'circuit-breakers': 60_000, // 60 seconds for circuit breakers (was 30s)\r\n    health: 45_000, // 45 seconds for health (was 15s)\r\n    metrics: 60_000, // 60 seconds for metrics (was 30s)\r\n  };\r\n\r\n  const webSocketManager = getWebSocketManager();\r\n\r\n  // Join reliability monitoring room when WebSocket is connected\r\n  useEffect(() => {\r\n    if (webSocketManager.isConnected()) {\r\n      console.debug(\r\n        `[ReliabilityQuery] Joining reliability-monitoring room for ${monitoringType}`\r\n      );\r\n      webSocketManager.joinRoom('reliability-monitoring');\r\n    }\r\n\r\n    // Subscribe to connection state changes to join room when connected\r\n    const unsubscribe = webSocketManager.onStateChange(state => {\r\n      if (state === 'connected') {\r\n        console.debug(\r\n          `[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ${monitoringType}`\r\n        );\r\n        webSocketManager.joinRoom('reliability-monitoring');\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      unsubscribe();\r\n      // Leave room when component unmounts\r\n      if (webSocketManager.isConnected()) {\r\n        webSocketManager.leaveRoom('reliability-monitoring');\r\n      }\r\n    };\r\n  }, [webSocketManager, monitoringType]);\r\n\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'reliability',\r\n      events: [\r\n        `${monitoringType}-update`,\r\n        `${monitoringType}-created`,\r\n        `${monitoringType}-resolved`,\r\n      ],\r\n      fallbackInterval: intervalMap[monitoringType],\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Smart Query Hook with Socket.IO Room Management\r\n *\r\n * Combines React Query with WebSocket real-time updates and automatic Socket.IO room joining.\r\n * This hook automatically:\r\n * - Joins the appropriate Socket.IO room based on the domain channel\r\n * - Subscribes to WebSocket events for real-time data updates\r\n * - Switches between WebSocket and polling based on connection state\r\n * - Handles room cleanup when component unmounts\r\n *\r\n * **Room Mapping:**\r\n * - `crud` channel → `entity-updates` room\r\n * - `reliability` channel → `reliability-monitoring` room\r\n * - `notifications` channel → `notifications-monitoring` room\r\n * - `system` channel → `system-monitoring` room\r\n *\r\n * @template T - The data type returned by the query function\r\n * @template E - The error type for failed queries\r\n * @param queryKey - React Query key for caching and invalidation\r\n * @param queryFn - Data fetching function that returns a Promise<T>\r\n * @param config - Smart query configuration including channel and events\r\n * @param options - Additional React Query options (merged with smart defaults)\r\n * @returns Enhanced query result with WebSocket integration and connection state\r\n */\r\nexport function useSmartQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  config: SmartQueryConfig<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n): UseQueryResult<T, E> & {\r\n  isUsingFallback: boolean;\r\n  isWebSocketConnected: boolean;\r\n} {\r\n  const {\r\n    channel,\r\n    enableFallback = true,\r\n    enableWebSocket = true,\r\n    events,\r\n    fallbackInterval = 30_000,\r\n  } = config;\r\n\r\n  const [isWebSocketConnected, setIsWebSocketConnected] = useState(false);\r\n  const webSocketManager = getWebSocketManager();\r\n\r\n  // Track WebSocket connection state\r\n  useEffect(() => {\r\n    const updateConnectionState = () => {\r\n      setIsWebSocketConnected(webSocketManager.isConnected());\r\n    };\r\n\r\n    // Initial state\r\n    updateConnectionState();\r\n\r\n    // Subscribe to state changes\r\n    const unsubscribe = webSocketManager.onStateChange(updateConnectionState);\r\n\r\n    return unsubscribe;\r\n  }, [webSocketManager]);\r\n\r\n  // Determine if we should use fallback polling\r\n  const isUsingFallback =\r\n    enableFallback && (!enableWebSocket || !isWebSocketConnected);\r\n\r\n  // Configure React Query options based on WebSocket state\r\n  const queryOptions: UseQueryOptions<T, E> = {\r\n    // Longer cache time for better performance\r\n    gcTime: 10 * 60 * 1000, // 10 minutes\r\n    queryFn,\r\n    queryKey,\r\n    // Disable polling when WebSocket is connected\r\n    refetchInterval: isUsingFallback ? fallbackInterval : false,\r\n    refetchOnReconnect: true, // Always refetch on network reconnect\r\n    // Enable background refetch only when using fallback\r\n    refetchOnWindowFocus: isUsingFallback,\r\n    // Shorter stale time when using WebSocket (real-time updates)\r\n    staleTime: isWebSocketConnected ? 0 : 30_000,\r\n    ...options,\r\n  };\r\n\r\n  const queryClient = useQueryClient();\r\n  const queryResult = useQuery<T, E>(queryOptions);\r\n\r\n  // Manage Socket.IO room joining/leaving based on channel\r\n  useEffect(() => {\r\n    if (!enableWebSocket || !isWebSocketConnected) {\r\n      return;\r\n    }\r\n\r\n    const roomName = CHANNEL_ROOM_MAPPING[channel];\r\n    if (!roomName) {\r\n      console.warn(\r\n        `[SmartQuery] No room mapping found for channel: ${channel}`\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Join the appropriate room for this channel\r\n    try {\r\n      webSocketManager.joinRoom(roomName);\r\n      console.log(\r\n        `[SmartQuery] Joined room: ${roomName} for channel: ${channel}`\r\n      );\r\n    } catch (error) {\r\n      console.error(`[SmartQuery] Failed to join room ${roomName}:`, error);\r\n    }\r\n\r\n    // Cleanup: leave room when component unmounts or dependencies change\r\n    return () => {\r\n      try {\r\n        webSocketManager.leaveRoom(roomName);\r\n        console.log(\r\n          `[SmartQuery] Left room: ${roomName} for channel: ${channel}`\r\n        );\r\n      } catch (error) {\r\n        console.error(`[SmartQuery] Failed to leave room ${roomName}:`, error);\r\n      }\r\n    };\r\n  }, [enableWebSocket, isWebSocketConnected, channel, webSocketManager]);\r\n\r\n  // Subscribe to WebSocket events for real-time updates\r\n  useEffect(() => {\r\n    if (!enableWebSocket || !isWebSocketConnected || events.length === 0) {\r\n      return;\r\n    }\r\n\r\n    const unsubscribers: (() => void)[] = [];\r\n\r\n    // Subscribe to each event\r\n    for (const event of events) {\r\n      const unsubscribe = webSocketManager.subscribe(\r\n        channel,\r\n        event,\r\n        (data: unknown) => {\r\n          console.log(\r\n            `[SmartQuery] WebSocket event received: ${channel}:${event}`,\r\n            data\r\n          );\r\n\r\n          // Invalidate the specific query to trigger refetch\r\n          queryClient.invalidateQueries({ queryKey });\r\n        }\r\n      );\r\n\r\n      unsubscribers.push(unsubscribe);\r\n    }\r\n\r\n    return () => {\r\n      for (const unsubscribe of unsubscribers) unsubscribe();\r\n    };\r\n  }, [\r\n    enableWebSocket,\r\n    isWebSocketConnected,\r\n    events,\r\n    channel,\r\n    webSocketManager,\r\n    queryClient,\r\n    queryKey,\r\n  ]);\r\n\r\n  return {\r\n    ...queryResult,\r\n    isUsingFallback,\r\n    isWebSocketConnected,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for system-wide events with smart real-time updates\r\n */\r\nexport function useSystemQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'system',\r\n      events: ['system-update', 'config-changed'],\r\n      fallbackInterval: 120_000, // 2 minutes for system events\r\n    },\r\n    options\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;AAID;AAAA;AACA;AAIA;;;;AAEA;;;CAGC,GACD,MAAM,uBAAsD;IAC1D,MAAM;IACN,eAAe;IACf,aAAa;IACb,QAAQ;AACV;AAyBO,SAAS,aACd,QAAmB,EACnB,OAAyB,EACzB,UAAkB,EAClB,OAA6D;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YACN,GAAG,WAAW,QAAQ,CAAC;YACvB,GAAG,WAAW,QAAQ,CAAC;YACvB,GAAG,WAAW,QAAQ,CAAC;YACvB,CAAC,QAAQ,EAAE,YAAY;SACxB;QACD,kBAAkB;IACpB,GACA;AAEJ;AAKO,SAAS,qBACd,QAAmB,EACnB,OAAyB,EACzB,OAA6D;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YAAC;YAAwB;SAAuB;QACxD,kBAAkB;IACpB,GACA;AAEJ;AAKO,SAAS,oBACd,QAAmB,EACnB,OAAyB,EACzB,cAAoE,EACpE,OAA6D;IAE7D,qEAAqE;IACrE,MAAM,cAAc;QAClB,QAAQ;QACR,oBAAoB;QACpB,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,mBAAmB,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD;IAE3C,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,WAAW,IAAI;YAClC,QAAQ,KAAK,CACX,CAAC,2DAA2D,EAAE,gBAAgB;YAEhF,iBAAiB,QAAQ,CAAC;QAC5B;QAEA,oEAAoE;QACpE,MAAM,cAAc,iBAAiB,aAAa,CAAC,CAAA;YACjD,IAAI,UAAU,aAAa;gBACzB,QAAQ,KAAK,CACX,CAAC,gFAAgF,EAAE,gBAAgB;gBAErG,iBAAiB,QAAQ,CAAC;YAC5B;QACF;QAEA,OAAO;YACL;YACA,qCAAqC;YACrC,IAAI,iBAAiB,WAAW,IAAI;gBAClC,iBAAiB,SAAS,CAAC;YAC7B;QACF;IACF,GAAG;QAAC;QAAkB;KAAe;IAErC,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YACN,GAAG,eAAe,OAAO,CAAC;YAC1B,GAAG,eAAe,QAAQ,CAAC;YAC3B,GAAG,eAAe,SAAS,CAAC;SAC7B;QACD,kBAAkB,WAAW,CAAC,eAAe;IAC/C,GACA;AAEJ;AA0BO,SAAS,cACd,QAAmB,EACnB,OAAyB,EACzB,MAA2B,EAC3B,OAA6D;IAK7D,MAAM,EACJ,OAAO,EACP,iBAAiB,IAAI,EACrB,kBAAkB,IAAI,EACtB,MAAM,EACN,mBAAmB,MAAM,EAC1B,GAAG;IAEJ,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,mBAAmB,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD;IAE3C,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,wBAAwB;YAC5B,wBAAwB,iBAAiB,WAAW;QACtD;QAEA,gBAAgB;QAChB;QAEA,6BAA6B;QAC7B,MAAM,cAAc,iBAAiB,aAAa,CAAC;QAEnD,OAAO;IACT,GAAG;QAAC;KAAiB;IAErB,8CAA8C;IAC9C,MAAM,kBACJ,kBAAkB,CAAC,CAAC,mBAAmB,CAAC,oBAAoB;IAE9D,yDAAyD;IACzD,MAAM,eAAsC;QAC1C,2CAA2C;QAC3C,QAAQ,KAAK,KAAK;QAClB;QACA;QACA,8CAA8C;QAC9C,iBAAiB,kBAAkB,mBAAmB;QACtD,oBAAoB;QACpB,qDAAqD;QACrD,sBAAsB;QACtB,8DAA8D;QAC9D,WAAW,uBAAuB,IAAI;QACtC,GAAG,OAAO;IACZ;IAEA,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,cAAc,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAQ;IAEnC,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,sBAAsB;YAC7C;QACF;QAEA,MAAM,WAAW,oBAAoB,CAAC,QAAQ;QAC9C,IAAI,CAAC,UAAU;YACb,QAAQ,IAAI,CACV,CAAC,gDAAgD,EAAE,SAAS;YAE9D;QACF;QAEA,6CAA6C;QAC7C,IAAI;YACF,iBAAiB,QAAQ,CAAC;YAC1B,QAAQ,GAAG,CACT,CAAC,0BAA0B,EAAE,SAAS,cAAc,EAAE,SAAS;QAEnE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,SAAS,CAAC,CAAC,EAAE;QACjE;QAEA,qEAAqE;QACrE,OAAO;YACL,IAAI;gBACF,iBAAiB,SAAS,CAAC;gBAC3B,QAAQ,GAAG,CACT,CAAC,wBAAwB,EAAE,SAAS,cAAc,EAAE,SAAS;YAEjE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;YAClE;QACF;IACF,GAAG;QAAC;QAAiB;QAAsB;QAAS;KAAiB;IAErE,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,OAAO,MAAM,KAAK,GAAG;YACpE;QACF;QAEA,MAAM,gBAAgC,EAAE;QAExC,0BAA0B;QAC1B,KAAK,MAAM,SAAS,OAAQ;YAC1B,MAAM,cAAc,iBAAiB,SAAS,CAC5C,SACA,OACA,CAAC;gBACC,QAAQ,GAAG,CACT,CAAC,uCAAuC,EAAE,QAAQ,CAAC,EAAE,OAAO,EAC5D;gBAGF,mDAAmD;gBACnD,YAAY,iBAAiB,CAAC;oBAAE;gBAAS;YAC3C;YAGF,cAAc,IAAI,CAAC;QACrB;QAEA,OAAO;YACL,KAAK,MAAM,eAAe,cAAe;QAC3C;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO;QACL,GAAG,WAAW;QACd;QACA;IACF;AACF;AAKO,SAAS,eACd,QAAmB,EACnB,OAAyB,EACzB,OAA6D;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YAAC;YAAiB;SAAiB;QAC3C,kBAAkB;IACpB,GACA;AAEJ", "debugId": null}}, {"offset": {"line": 1170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/api/index.ts"], "sourcesContent": ["/**\r\n * @file API Hooks Index\r\n * @description Centralized exports for all API-related hooks\r\n */\r\n\r\n// Core API hooks\r\nexport {\r\n  useApiQuery,\r\n  useDependentApiQuery,\r\n  usePaginatedApiQuery,\r\n  type ApiQueryOptions,\r\n  type ApiQueryResult,\r\n  type PaginatedQueryOptions,\r\n  type PaginatedQueryResult,\r\n} from './useApiQuery';\r\n\r\nexport {\r\n  useApiMutation,\r\n  useCreateMutation,\r\n  useUpdateMutation,\r\n  useDeleteMutation,\r\n  type ApiMutationOptions,\r\n  type ApiMutationResult,\r\n} from './useApiMutation';\r\n\r\n// Advanced API hooks (moved from lib/hooks)\r\nexport {\r\n  useNavigationPrefetch,\r\n  useJourneyPrefetch,\r\n  USER_JOURNEYS,\r\n} from './useNavigationPrefetch';\r\nexport {\r\n  useQueryOptimization,\r\n  useQueryPerformanceMonitor,\r\n} from './useQueryOptimization';\r\nexport { useSmartQuery } from './useSmartQuery';\r\n\r\n// Re-export commonly used React Query types for convenience\r\nexport type {\r\n  QueryKey,\r\n  UseQueryOptions,\r\n  UseQueryResult,\r\n  UseMutationOptions,\r\n  UseMutationResult,\r\n} from '@tanstack/react-query';\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,iBAAiB;;AACjB;AAUA;AASA,4CAA4C;AAC5C;AAKA;AAIA", "debugId": null}}, {"offset": {"line": 1205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/ui/useNotifications.ts"], "sourcesContent": ["/**\r\n * @file Custom hook for notification management using Zustand AppStore\r\n * @module hooks/useNotifications\r\n */\r\n\r\nimport { useCallback } from 'react';\r\n\r\nimport { useAppStore } from '@/lib/stores/zustand/appStore';\r\nimport { undefinedToNull } from '../../lib/utils/typeHelpers';\r\n\r\n/**\r\n * Custom hook for simplified notification management\r\n * Provides convenient methods for showing different types of notifications\r\n */\r\nexport const useNotifications = () => {\r\n  const addNotification = useAppStore(state => state.addNotification);\r\n  const removeNotification = useAppStore(state => state.removeNotification);\r\n  const clearAllNotifications = useAppStore(\r\n    state => state.clearAllNotifications\r\n  );\r\n  const unreadCount = useAppStore(state => state.unreadNotificationCount);\r\n\r\n  /**\r\n   * Show a success notification\r\n   */\r\n  const showSuccess = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'success',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show an error notification\r\n   */\r\n  const showError = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'error',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a warning notification\r\n   */\r\n  const showWarning = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'warning',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show an info notification\r\n   */\r\n  const showInfo = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'info',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a notification for API operation results\r\n   */\r\n  const showApiResult = useCallback(\r\n    (success: boolean, successMessage: string, errorMessage: string) => {\r\n      if (success) {\r\n        showSuccess(successMessage);\r\n      } else {\r\n        showError(errorMessage);\r\n      }\r\n    },\r\n    [showSuccess, showError]\r\n  );\r\n\r\n  /**\r\n   * Show a notification with auto-dismiss after specified time\r\n   */\r\n  const showTemporary = useCallback(\r\n    (\r\n      type: 'error' | 'info' | 'success' | 'warning',\r\n      message: string,\r\n      dismissAfter = 5000\r\n    ) => {\r\n      addNotification({ message, type });\r\n\r\n      // Auto-dismiss after specified time\r\n      setTimeout(() => {\r\n        // Note: This is a simplified approach. In a real implementation,\r\n        // you might want to store the notification ID and remove specifically that one\r\n        const notifications = useAppStore.getState().notifications;\r\n        const latestNotification = notifications.at(-1);\r\n        if (latestNotification && latestNotification.message === message) {\r\n          removeNotification(latestNotification.id);\r\n        }\r\n      }, dismissAfter);\r\n    },\r\n    [addNotification, removeNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a loading notification that can be updated\r\n   */\r\n  const showLoading = useCallback(\r\n    (message = 'Loading...') => {\r\n      addNotification({\r\n        message,\r\n        type: 'info',\r\n      });\r\n\r\n      // Return the notification ID for potential updates\r\n      const notifications = useAppStore.getState().notifications;\r\n      return notifications.at(-1)?.id;\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Update a loading notification to success or error\r\n   */\r\n  const updateLoadingNotification = useCallback(\r\n    (notificationId: string, success: boolean, message: string) => {\r\n      removeNotification(notificationId);\r\n      if (success) {\r\n        showSuccess(message);\r\n      } else {\r\n        showError(message);\r\n      }\r\n    },\r\n    [removeNotification, showSuccess, showError]\r\n  );\r\n\r\n  return {\r\n    clearAllNotifications,\r\n    // Store methods\r\n    removeNotification,\r\n    // Advanced methods\r\n    showApiResult,\r\n    showError,\r\n\r\n    showInfo,\r\n    showLoading,\r\n    // Basic notification methods\r\n    showSuccess,\r\n    showTemporary,\r\n\r\n    showWarning,\r\n    unreadCount,\r\n    updateLoadingNotification,\r\n  };\r\n};\r\n\r\n/**\r\n * Enhanced notification hook with WorkHub-specific notification types\r\n */\r\nexport const useWorkHubNotifications = () => {\r\n  const {\r\n    clearAllNotifications,\r\n    removeNotification,\r\n    showError,\r\n    showInfo,\r\n    showSuccess,\r\n    showWarning,\r\n    unreadCount,\r\n  } = useNotifications();\r\n\r\n  /**\r\n   * Show delegation-related notifications\r\n   */\r\n  const showDelegationUpdate = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'delegation',\r\n        message,\r\n        type: 'delegation-update',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show vehicle maintenance notifications\r\n   */\r\n  const showVehicleMaintenance = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'vehicle',\r\n        message,\r\n        type: 'vehicle-maintenance',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show task assignment notifications\r\n   */\r\n  const showTaskAssigned = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'task',\r\n        message,\r\n        type: 'task-assigned',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show employee update notifications\r\n   */\r\n  const showEmployeeUpdate = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'employee',\r\n        message,\r\n        type: 'employee-update',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  return {\r\n    clearAllNotifications,\r\n    // Management\r\n    removeNotification,\r\n    // WorkHub-specific notifications\r\n    showDelegationUpdate,\r\n    showEmployeeUpdate,\r\n\r\n    showError,\r\n    showInfo,\r\n    // Basic notifications\r\n    showSuccess,\r\n    showTaskAssigned,\r\n\r\n    showVehicleMaintenance,\r\n    showWarning,\r\n    unreadCount,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AAEA;;;AAOO,MAAM,mBAAmB;IAC9B,MAAM,kBAAkB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,eAAe;IAClE,MAAM,qBAAqB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,kBAAkB;IACxE,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EACtC,CAAA,QAAS,MAAM,qBAAqB;IAEtC,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,uBAAuB;IAEtE;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC1B,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACzB,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,SAAkB,gBAAwB;QACzC,IAAI,SAAS;YACX,YAAY;QACd,OAAO;YACL,UAAU;QACZ;IACF,GACA;QAAC;QAAa;KAAU;IAG1B;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CACE,MACA,SACA,eAAe,IAAI;QAEnB,gBAAgB;YAAE;YAAS;QAAK;QAEhC,oCAAoC;QACpC,WAAW;YACT,iEAAiE;YACjE,+EAA+E;YAC/E,MAAM,gBAAgB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;YAC1D,MAAM,qBAAqB,cAAc,EAAE,CAAC,CAAC;YAC7C,IAAI,sBAAsB,mBAAmB,OAAO,KAAK,SAAS;gBAChE,mBAAmB,mBAAmB,EAAE;YAC1C;QACF,GAAG;IACL,GACA;QAAC;QAAiB;KAAmB;IAGvC;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC,UAAU,YAAY;QACrB,gBAAgB;YACd;YACA,MAAM;QACR;QAEA,mDAAmD;QACnD,MAAM,gBAAgB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;QAC1D,OAAO,cAAc,EAAE,CAAC,CAAC,IAAI;IAC/B,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC1C,CAAC,gBAAwB,SAAkB;QACzC,mBAAmB;QACnB,IAAI,SAAS;YACX,YAAY;QACd,OAAO;YACL,UAAU;QACZ;IACF,GACA;QAAC;QAAoB;QAAa;KAAU;IAG9C,OAAO;QACL;QACA,gBAAgB;QAChB;QACA,mBAAmB;QACnB;QACA;QAEA;QACA;QACA,6BAA6B;QAC7B;QACA;QAEA;QACA;QACA;IACF;AACF;AAKO,MAAM,0BAA0B;IACrC,MAAM,EACJ,qBAAqB,EACrB,kBAAkB,EAClB,SAAS,EACT,QAAQ,EACR,WAAW,EACX,WAAW,EACX,WAAW,EACZ,GAAG;IAEJ;;GAEC,GACD,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACvC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ,OAAO;QACL;QACA,aAAa;QACb;QACA,iCAAiC;QACjC;QACA;QAEA;QACA;QACA,sBAAsB;QACtB;QACA;QAEA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/ui/useSidebar.ts"], "sourcesContent": ["/**\r\n * @file Sidebar management hook using Zustand AppStore\r\n * @module hooks/useSidebar\r\n */\r\n\r\nimport { useCallback } from 'react';\r\n\r\nimport { useAppStore } from '@/lib/stores/zustand/appStore';\r\n\r\n/**\r\n * Hook for sidebar management\r\n * Provides convenient methods for sidebar state control\r\n */\r\nexport const useSidebar = () => {\r\n  const sidebarOpen = useAppStore(state => state.sidebarOpen);\r\n  const toggleSidebar = useAppStore(state => state.toggleSidebar);\r\n\r\n  /**\r\n   * Open the sidebar if it's closed\r\n   */\r\n  const openSidebar = useCallback(() => {\r\n    if (!sidebarOpen) {\r\n      toggleSidebar();\r\n    }\r\n  }, [sidebarOpen, toggleSidebar]);\r\n\r\n  /**\r\n   * Close the sidebar if it's open\r\n   */\r\n  const closeSidebar = useCallback(() => {\r\n    if (sidebarOpen) {\r\n      toggleSidebar();\r\n    }\r\n  }, [sidebarOpen, toggleSidebar]);\r\n\r\n  /**\r\n   * Get sidebar-specific CSS classes\r\n   */\r\n  const getSidebarClasses = useCallback(() => {\r\n    return {\r\n      content: sidebarOpen ? 'content-shifted' : 'content-normal',\r\n      overlay: sidebarOpen ? 'overlay-visible' : 'overlay-hidden',\r\n      sidebar: sidebarOpen ? 'sidebar-open' : 'sidebar-closed',\r\n      toggle: sidebarOpen ? 'toggle-close' : 'toggle-open',\r\n    };\r\n  }, [sidebarOpen]);\r\n\r\n  /**\r\n   * Get sidebar state for accessibility\r\n   */\r\n  const getAriaAttributes = useCallback(() => {\r\n    return {\r\n      'aria-expanded': sidebarOpen,\r\n      'aria-label': sidebarOpen ? 'Close sidebar' : 'Open sidebar',\r\n    };\r\n  }, [sidebarOpen]);\r\n\r\n  return {\r\n    closeSidebar,\r\n    getAriaAttributes,\r\n    // Utilities\r\n    getSidebarClasses,\r\n\r\n    isClosed: !sidebarOpen,\r\n    isOpen: sidebarOpen,\r\n    openSidebar,\r\n\r\n    // State\r\n    sidebarOpen,\r\n    // Actions\r\n    toggleSidebar,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAEA;;;AAMO,MAAM,aAAa;IACxB,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,WAAW;IAC1D,MAAM,gBAAgB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,aAAa;IAE9D;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,CAAC,aAAa;YAChB;QACF;IACF,GAAG;QAAC;QAAa;KAAc;IAE/B;;GAEC,GACD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,aAAa;YACf;QACF;IACF,GAAG;QAAC;QAAa;KAAc;IAE/B;;GAEC,GACD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,OAAO;YACL,SAAS,cAAc,oBAAoB;YAC3C,SAAS,cAAc,oBAAoB;YAC3C,SAAS,cAAc,iBAAiB;YACxC,QAAQ,cAAc,iBAAiB;QACzC;IACF,GAAG;QAAC;KAAY;IAEhB;;GAEC,GACD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,OAAO;YACL,iBAAiB;YACjB,cAAc,cAAc,kBAAkB;QAChD;IACF,GAAG;QAAC;KAAY;IAEhB,OAAO;QACL;QACA;QACA,YAAY;QACZ;QAEA,UAAU,CAAC;QACX,QAAQ;QACR;QAEA,QAAQ;QACR;QACA,UAAU;QACV;IACF;AACF", "debugId": null}}, {"offset": {"line": 1490, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/forms/services/FormSubmissionConfig.ts"], "sourcesContent": ["/**\r\n * @file Form Submission Configuration Service\r\n * @description Default configurations for form submission following SRP\r\n * @version 2.0.0\r\n * <AUTHOR> Development Team\r\n */\r\n\r\nimport type {\r\n  RetryConfig,\r\n  AccessibilityConfig,\r\n  PerformanceConfig,\r\n  ToastConfig,\r\n} from '../types/FormSubmissionTypes';\r\n\r\n/**\r\n * Default retry configuration\r\n */\r\nexport const DEFAULT_RETRY_CONFIG: RetryConfig = {\r\n  maxAttempts: 3,\r\n  delay: 1000,\r\n  exponentialBackoff: true,\r\n  retryCondition: error =>\r\n    error.message.includes('network') ||\r\n    error.message.includes('timeout') ||\r\n    error.message.includes('502') ||\r\n    error.message.includes('503') ||\r\n    error.message.includes('504'),\r\n};\r\n\r\n/**\r\n * Default accessibility configuration\r\n */\r\nexport const DEFAULT_ACCESSIBILITY_CONFIG: AccessibilityConfig = {\r\n  announceStatus: true,\r\n  focusManagement: 'first-error',\r\n  screenReaderAnnouncements: true,\r\n};\r\n\r\n/**\r\n * Default performance configuration\r\n */\r\nexport const DEFAULT_PERFORMANCE_CONFIG: PerformanceConfig = {\r\n  debounceMs: 300,\r\n  enableDeduplication: true,\r\n  cacheResults: false,\r\n  timeoutMs: 30000,\r\n};\r\n\r\n/**\r\n * Default toast configuration\r\n */\r\nexport const DEFAULT_TOAST_CONFIG: ToastConfig = {\r\n  showSuccessToast: true,\r\n  showErrorToast: true,\r\n  successMessage: 'Operation completed successfully',\r\n  errorMessage: 'An unexpected error occurred',\r\n  entityType: 'generic',\r\n};\r\n\r\n/**\r\n * Configuration merger utility following DRY principle\r\n */\r\nexport class FormSubmissionConfigService {\r\n  /**\r\n   * Merge user config with defaults for retry settings\r\n   */\r\n  static mergeRetryConfig(userConfig?: Partial<RetryConfig>): RetryConfig {\r\n    return { ...DEFAULT_RETRY_CONFIG, ...userConfig };\r\n  }\r\n\r\n  /**\r\n   * Merge user config with defaults for accessibility settings\r\n   */\r\n  static mergeAccessibilityConfig(\r\n    userConfig?: Partial<AccessibilityConfig>\r\n  ): AccessibilityConfig {\r\n    return { ...DEFAULT_ACCESSIBILITY_CONFIG, ...userConfig };\r\n  }\r\n\r\n  /**\r\n   * Merge user config with defaults for performance settings\r\n   */\r\n  static mergePerformanceConfig(\r\n    userConfig?: Partial<PerformanceConfig>\r\n  ): PerformanceConfig {\r\n    return { ...DEFAULT_PERFORMANCE_CONFIG, ...userConfig };\r\n  }\r\n\r\n  /**\r\n   * Merge user config with defaults for toast settings\r\n   */\r\n  static mergeToastConfig(userConfig?: Partial<ToastConfig>): ToastConfig {\r\n    return { ...DEFAULT_TOAST_CONFIG, ...userConfig };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;AAYM,MAAM,uBAAoC;IAC/C,aAAa;IACb,OAAO;IACP,oBAAoB;IACpB,gBAAgB,CAAA,QACd,MAAM,OAAO,CAAC,QAAQ,CAAC,cACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,cACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,UACvB,MAAM,OAAO,CAAC,QAAQ,CAAC,UACvB,MAAM,OAAO,CAAC,QAAQ,CAAC;AAC3B;AAKO,MAAM,+BAAoD;IAC/D,gBAAgB;IAChB,iBAAiB;IACjB,2BAA2B;AAC7B;AAKO,MAAM,6BAAgD;IAC3D,YAAY;IACZ,qBAAqB;IACrB,cAAc;IACd,WAAW;AACb;AAKO,MAAM,uBAAoC;IAC/C,kBAAkB;IAClB,gBAAgB;IAChB,gBAAgB;IAChB,cAAc;IACd,YAAY;AACd;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,iBAAiB,UAAiC,EAAe;QACtE,OAAO;YAAE,GAAG,oBAAoB;YAAE,GAAG,UAAU;QAAC;IAClD;IAEA;;GAEC,GACD,OAAO,yBACL,UAAyC,EACpB;QACrB,OAAO;YAAE,GAAG,4BAA4B;YAAE,GAAG,UAAU;QAAC;IAC1D;IAEA;;GAEC,GACD,OAAO,uBACL,UAAuC,EACpB;QACnB,OAAO;YAAE,GAAG,0BAA0B;YAAE,GAAG,UAAU;QAAC;IACxD;IAEA;;GAEC,GACD,OAAO,iBAAiB,UAAiC,EAAe;QACtE,OAAO;YAAE,GAAG,oBAAoB;YAAE,GAAG,UAAU;QAAC;IAClD;AACF", "debugId": null}}, {"offset": {"line": 1566, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/forms/services/FormSubmissionToastService.ts"], "sourcesContent": ["/**\r\n * @file Form Submission Toast Service\r\n * @description Handles toast notifications for form submissions following SRP\r\n * @version 2.0.0\r\n * <AUTHOR> Development Team\r\n */\r\n\r\nimport type { ToastConfig } from '../types/FormSubmissionTypes';\r\nimport {\r\n  toastService,\r\n  employeeToast,\r\n  vehicleToast,\r\n  taskToast,\r\n  delegationToast,\r\n  serviceRecordToast,\r\n  createSimpleEntityToastService,\r\n} from '@/lib/services/toastService';\r\n\r\n/**\r\n * Form submission toast service following SRP\r\n * Responsible only for handling toast notifications during form submission\r\n */\r\nexport class FormSubmissionToastService {\r\n  /**\r\n   * Show success toast based on entity type and configuration\r\n   */\r\n  static showSuccessToast(config: ToastConfig, data?: any, result?: any): void {\r\n    if (!config.showSuccessToast) return;\r\n\r\n    const { entityType, entity, successMessage } = config;\r\n\r\n    try {\r\n      switch (entityType) {\r\n        case 'employee':\r\n          if (entity) {\r\n            employeeToast.entityCreated(entity);\r\n          } else {\r\n            toastService.success('Employee Created', successMessage);\r\n          }\r\n          break;\r\n\r\n        case 'vehicle':\r\n          if (entity) {\r\n            vehicleToast.entityCreated(entity);\r\n          } else {\r\n            toastService.success('Vehicle Added', successMessage);\r\n          }\r\n          break;\r\n\r\n        case 'task':\r\n          if (entity) {\r\n            taskToast.entityCreated(entity);\r\n          } else {\r\n            toastService.success('Task Created', successMessage);\r\n          }\r\n          break;\r\n\r\n        case 'delegation':\r\n          if (entity) {\r\n            delegationToast.entityCreated(entity);\r\n          } else {\r\n            toastService.success('Delegation Created', successMessage);\r\n          }\r\n          break;\r\n\r\n        case 'serviceRecord':\r\n          if (entity && result) {\r\n            serviceRecordToast.serviceRecordCreated(\r\n              entity.vehicleName || 'Vehicle',\r\n              entity.serviceType || 'Service'\r\n            );\r\n          } else {\r\n            toastService.success('Service Record Added', successMessage);\r\n          }\r\n          break;\r\n\r\n        case 'generic':\r\n        default:\r\n          toastService.success(\r\n            'Success',\r\n            successMessage || 'Operation completed successfully'\r\n          );\r\n          break;\r\n      }\r\n    } catch (error) {\r\n      // Fallback to generic success toast if entity-specific fails\r\n      toastService.success(\r\n        'Success',\r\n        successMessage || 'Operation completed successfully'\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Show error toast based on entity type and configuration\r\n   */\r\n  static showErrorToast(config: ToastConfig, error: Error, data?: any): void {\r\n    if (!config.showErrorToast) return;\r\n\r\n    const { entityType, errorMessage } = config;\r\n    const errorMsg =\r\n      error.message || errorMessage || 'An unexpected error occurred';\r\n\r\n    try {\r\n      switch (entityType) {\r\n        case 'employee':\r\n          employeeToast.entityCreationError(errorMsg);\r\n          break;\r\n\r\n        case 'vehicle':\r\n          vehicleToast.entityCreationError(errorMsg);\r\n          break;\r\n\r\n        case 'task':\r\n          taskToast.entityCreationError(errorMsg);\r\n          break;\r\n\r\n        case 'delegation':\r\n          delegationToast.entityCreationError(errorMsg);\r\n          break;\r\n\r\n        case 'serviceRecord':\r\n          serviceRecordToast.serviceRecordCreationError(errorMsg);\r\n          break;\r\n\r\n        case 'generic':\r\n        default:\r\n          toastService.error('Error', errorMsg);\r\n          break;\r\n      }\r\n    } catch (toastError) {\r\n      // Fallback to generic error toast if entity-specific fails\r\n      toastService.error('Error', errorMsg);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Show update success toast for existing entities\r\n   */\r\n  static showUpdateSuccessToast(\r\n    config: ToastConfig,\r\n    data?: any,\r\n    result?: any\r\n  ): void {\r\n    if (!config.showSuccessToast) return;\r\n\r\n    const { entityType, entity, successMessage } = config;\r\n\r\n    try {\r\n      switch (entityType) {\r\n        case 'employee':\r\n          if (entity) {\r\n            employeeToast.entityUpdated(entity);\r\n          } else {\r\n            toastService.success('Employee Updated', successMessage);\r\n          }\r\n          break;\r\n\r\n        case 'vehicle':\r\n          if (entity) {\r\n            vehicleToast.entityUpdated(entity);\r\n          } else {\r\n            toastService.success('Vehicle Updated', successMessage);\r\n          }\r\n          break;\r\n\r\n        case 'task':\r\n          if (entity) {\r\n            taskToast.entityUpdated(entity);\r\n          } else {\r\n            toastService.success('Task Updated', successMessage);\r\n          }\r\n          break;\r\n\r\n        case 'delegation':\r\n          if (entity) {\r\n            delegationToast.entityUpdated(entity);\r\n          } else {\r\n            toastService.success('Delegation Updated', successMessage);\r\n          }\r\n          break;\r\n\r\n        case 'serviceRecord':\r\n          if (entity && result) {\r\n            serviceRecordToast.serviceRecordUpdated(\r\n              entity.vehicleName || 'Vehicle',\r\n              entity.serviceType || 'Service'\r\n            );\r\n          } else {\r\n            toastService.success('Service Record Updated', successMessage);\r\n          }\r\n          break;\r\n\r\n        case 'generic':\r\n        default:\r\n          toastService.success(\r\n            'Success',\r\n            successMessage || 'Update completed successfully'\r\n          );\r\n          break;\r\n      }\r\n    } catch (error) {\r\n      // Fallback to generic success toast\r\n      toastService.success(\r\n        'Success',\r\n        successMessage || 'Update completed successfully'\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Show update error toast for existing entities\r\n   */\r\n  static showUpdateErrorToast(\r\n    config: ToastConfig,\r\n    error: Error,\r\n    data?: any\r\n  ): void {\r\n    if (!config.showErrorToast) return;\r\n\r\n    const { entityType, errorMessage } = config;\r\n    const errorMsg =\r\n      error.message || errorMessage || 'An unexpected error occurred';\r\n\r\n    try {\r\n      switch (entityType) {\r\n        case 'employee':\r\n          employeeToast.entityUpdateError(errorMsg);\r\n          break;\r\n\r\n        case 'vehicle':\r\n          vehicleToast.entityUpdateError(errorMsg);\r\n          break;\r\n\r\n        case 'task':\r\n          taskToast.entityUpdateError(errorMsg);\r\n          break;\r\n\r\n        case 'delegation':\r\n          delegationToast.entityUpdateError(errorMsg);\r\n          break;\r\n\r\n        case 'serviceRecord':\r\n          serviceRecordToast.serviceRecordUpdateError(errorMsg);\r\n          break;\r\n\r\n        case 'generic':\r\n        default:\r\n          toastService.error('Update Failed', errorMsg);\r\n          break;\r\n      }\r\n    } catch (toastError) {\r\n      // Fallback to generic error toast\r\n      toastService.error('Update Failed', errorMsg);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create a dynamic toast service for custom entity types\r\n   */\r\n  static createCustomEntityToastService<T>(\r\n    entityName: string,\r\n    getDisplayName: (entity: T) => string\r\n  ) {\r\n    return createSimpleEntityToastService<T>(entityName, getDisplayName);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAGD;;AAcO,MAAM;IACX;;GAEC,GACD,OAAO,iBAAiB,MAAmB,EAAE,IAAU,EAAE,MAAY,EAAQ;QAC3E,IAAI,CAAC,OAAO,gBAAgB,EAAE;QAE9B,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG;QAE/C,IAAI;YACF,OAAQ;gBACN,KAAK;oBACH,IAAI,QAAQ;wBACV,sIAAA,CAAA,gBAAa,CAAC,aAAa,CAAC;oBAC9B,OAAO;wBACL,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,oBAAoB;oBAC3C;oBACA;gBAEF,KAAK;oBACH,IAAI,QAAQ;wBACV,sIAAA,CAAA,eAAY,CAAC,aAAa,CAAC;oBAC7B,OAAO;wBACL,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,iBAAiB;oBACxC;oBACA;gBAEF,KAAK;oBACH,IAAI,QAAQ;wBACV,sIAAA,CAAA,YAAS,CAAC,aAAa,CAAC;oBAC1B,OAAO;wBACL,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,gBAAgB;oBACvC;oBACA;gBAEF,KAAK;oBACH,IAAI,QAAQ;wBACV,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC;oBAChC,OAAO;wBACL,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,sBAAsB;oBAC7C;oBACA;gBAEF,KAAK;oBACH,IAAI,UAAU,QAAQ;wBACpB,sIAAA,CAAA,qBAAkB,CAAC,oBAAoB,CACrC,OAAO,WAAW,IAAI,WACtB,OAAO,WAAW,IAAI;oBAE1B,OAAO;wBACL,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,wBAAwB;oBAC/C;oBACA;gBAEF,KAAK;gBACL;oBACE,sIAAA,CAAA,eAAY,CAAC,OAAO,CAClB,WACA,kBAAkB;oBAEpB;YACJ;QACF,EAAE,OAAO,OAAO;YACd,6DAA6D;YAC7D,sIAAA,CAAA,eAAY,CAAC,OAAO,CAClB,WACA,kBAAkB;QAEtB;IACF;IAEA;;GAEC,GACD,OAAO,eAAe,MAAmB,EAAE,KAAY,EAAE,IAAU,EAAQ;QACzE,IAAI,CAAC,OAAO,cAAc,EAAE;QAE5B,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG;QACrC,MAAM,WACJ,MAAM,OAAO,IAAI,gBAAgB;QAEnC,IAAI;YACF,OAAQ;gBACN,KAAK;oBACH,sIAAA,CAAA,gBAAa,CAAC,mBAAmB,CAAC;oBAClC;gBAEF,KAAK;oBACH,sIAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC;oBACjC;gBAEF,KAAK;oBACH,sIAAA,CAAA,YAAS,CAAC,mBAAmB,CAAC;oBAC9B;gBAEF,KAAK;oBACH,sIAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;oBACpC;gBAEF,KAAK;oBACH,sIAAA,CAAA,qBAAkB,CAAC,0BAA0B,CAAC;oBAC9C;gBAEF,KAAK;gBACL;oBACE,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,SAAS;oBAC5B;YACJ;QACF,EAAE,OAAO,YAAY;YACnB,2DAA2D;YAC3D,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,SAAS;QAC9B;IACF;IAEA;;GAEC,GACD,OAAO,uBACL,MAAmB,EACnB,IAAU,EACV,MAAY,EACN;QACN,IAAI,CAAC,OAAO,gBAAgB,EAAE;QAE9B,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG;QAE/C,IAAI;YACF,OAAQ;gBACN,KAAK;oBACH,IAAI,QAAQ;wBACV,sIAAA,CAAA,gBAAa,CAAC,aAAa,CAAC;oBAC9B,OAAO;wBACL,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,oBAAoB;oBAC3C;oBACA;gBAEF,KAAK;oBACH,IAAI,QAAQ;wBACV,sIAAA,CAAA,eAAY,CAAC,aAAa,CAAC;oBAC7B,OAAO;wBACL,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,mBAAmB;oBAC1C;oBACA;gBAEF,KAAK;oBACH,IAAI,QAAQ;wBACV,sIAAA,CAAA,YAAS,CAAC,aAAa,CAAC;oBAC1B,OAAO;wBACL,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,gBAAgB;oBACvC;oBACA;gBAEF,KAAK;oBACH,IAAI,QAAQ;wBACV,sIAAA,CAAA,kBAAe,CAAC,aAAa,CAAC;oBAChC,OAAO;wBACL,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,sBAAsB;oBAC7C;oBACA;gBAEF,KAAK;oBACH,IAAI,UAAU,QAAQ;wBACpB,sIAAA,CAAA,qBAAkB,CAAC,oBAAoB,CACrC,OAAO,WAAW,IAAI,WACtB,OAAO,WAAW,IAAI;oBAE1B,OAAO;wBACL,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,0BAA0B;oBACjD;oBACA;gBAEF,KAAK;gBACL;oBACE,sIAAA,CAAA,eAAY,CAAC,OAAO,CAClB,WACA,kBAAkB;oBAEpB;YACJ;QACF,EAAE,OAAO,OAAO;YACd,oCAAoC;YACpC,sIAAA,CAAA,eAAY,CAAC,OAAO,CAClB,WACA,kBAAkB;QAEtB;IACF;IAEA;;GAEC,GACD,OAAO,qBACL,MAAmB,EACnB,KAAY,EACZ,IAAU,EACJ;QACN,IAAI,CAAC,OAAO,cAAc,EAAE;QAE5B,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG;QACrC,MAAM,WACJ,MAAM,OAAO,IAAI,gBAAgB;QAEnC,IAAI;YACF,OAAQ;gBACN,KAAK;oBACH,sIAAA,CAAA,gBAAa,CAAC,iBAAiB,CAAC;oBAChC;gBAEF,KAAK;oBACH,sIAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC;oBAC/B;gBAEF,KAAK;oBACH,sIAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC;oBAC5B;gBAEF,KAAK;oBACH,sIAAA,CAAA,kBAAe,CAAC,iBAAiB,CAAC;oBAClC;gBAEF,KAAK;oBACH,sIAAA,CAAA,qBAAkB,CAAC,wBAAwB,CAAC;oBAC5C;gBAEF,KAAK;gBACL;oBACE,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,iBAAiB;oBACpC;YACJ;QACF,EAAE,OAAO,YAAY;YACnB,kCAAkC;YAClC,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,iBAAiB;QACtC;IACF;IAEA;;GAEC,GACD,OAAO,+BACL,UAAkB,EAClB,cAAqC,EACrC;QACA,OAAO,CAAA,GAAA,sIAAA,CAAA,iCAA8B,AAAD,EAAK,YAAY;IACvD;AACF", "debugId": null}}, {"offset": {"line": 1759, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/forms/services/FormSubmissionAccessibilityService.ts"], "sourcesContent": ["/**\r\n * @file Form Submission Accessibility Service\r\n * @description Handles accessibility features for form submissions following SRP\r\n * @version 2.0.0\r\n * <AUTHOR> Development Team\r\n */\r\n\r\nimport type {\r\n  AccessibilityConfig,\r\n  AriaAttributes,\r\n} from '../types/FormSubmissionTypes';\r\n\r\n/**\r\n * Form submission accessibility service following SRP\r\n * Responsible only for handling accessibility features during form submission\r\n */\r\nexport class FormSubmissionAccessibilityService {\r\n  private config: AccessibilityConfig;\r\n\r\n  constructor(config: AccessibilityConfig) {\r\n    this.config = config;\r\n  }\r\n\r\n  /**\r\n   * Announce status for screen readers\r\n   */\r\n  announceStatus(\r\n    message: string,\r\n    priority: 'polite' | 'assertive' = 'polite'\r\n  ): void {\r\n    if (!this.config.announceStatus || !this.config.screenReaderAnnouncements) {\r\n      return;\r\n    }\r\n\r\n    // Create or update live region for announcements\r\n    let liveRegion = document.getElementById('form-submission-announcements');\r\n    if (!liveRegion) {\r\n      liveRegion = document.createElement('div');\r\n      liveRegion.id = 'form-submission-announcements';\r\n      liveRegion.setAttribute('aria-live', priority);\r\n      liveRegion.setAttribute('aria-atomic', 'true');\r\n      liveRegion.className =\r\n        'sr-only absolute left-[-10000px] top-[-10000px] w-[1px] h-[1px] overflow-hidden';\r\n      document.body.appendChild(liveRegion);\r\n    }\r\n\r\n    // Update the announcement\r\n    liveRegion.textContent = message;\r\n\r\n    // Clear the announcement after a delay to allow for multiple announcements\r\n    setTimeout(() => {\r\n      if (liveRegion && liveRegion.textContent === message) {\r\n        liveRegion.textContent = '';\r\n      }\r\n    }, 1000);\r\n  }\r\n\r\n  /**\r\n   * Generate ARIA attributes for form elements\r\n   */\r\n  generateAriaAttributes(\r\n    isLoading: boolean,\r\n    hasError: boolean,\r\n    state: string\r\n  ): AriaAttributes {\r\n    return {\r\n      'aria-busy': isLoading,\r\n      'aria-invalid': hasError,\r\n      'aria-describedby':\r\n        this.config.errorDescribedBy || (hasError ? 'form-error' : undefined),\r\n      'aria-live':\r\n        state === 'submitting' || state === 'validating' ? 'polite' : 'off',\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Manage focus based on configuration\r\n   */\r\n  manageFocus(\r\n    focusType: 'success' | 'error' | 'retry',\r\n    formFocus?: (fieldName: string) => void\r\n  ): void {\r\n    if (this.config.focusManagement === 'none') return;\r\n\r\n    switch (focusType) {\r\n      case 'error':\r\n        if (this.config.focusManagement === 'first-error' && formFocus) {\r\n          // Focus first field with error\r\n          formFocus('first-error');\r\n        }\r\n        break;\r\n\r\n      case 'success':\r\n        if (this.config.focusManagement === 'success-message') {\r\n          // Focus success message if it exists\r\n          const successElement = document.getElementById(\r\n            'form-success-message'\r\n          );\r\n          if (successElement) {\r\n            successElement.focus();\r\n          }\r\n        } else if (this.config.focusManagement === 'next-field' && formFocus) {\r\n          // Focus next logical field or submit button\r\n          formFocus('next-field');\r\n        }\r\n        break;\r\n\r\n      case 'retry':\r\n        if (formFocus) {\r\n          // Focus retry button or first field\r\n          formFocus('retry-button');\r\n        }\r\n        break;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create accessible error message element\r\n   */\r\n  createErrorMessage(error: string): HTMLElement {\r\n    const errorElement = document.createElement('div');\r\n    errorElement.id = this.config.errorDescribedBy || 'form-error';\r\n    errorElement.setAttribute('role', 'alert');\r\n    errorElement.setAttribute('aria-live', 'assertive');\r\n    errorElement.className = 'sr-only';\r\n    errorElement.textContent = error;\r\n\r\n    return errorElement;\r\n  }\r\n\r\n  /**\r\n   * Update or create error message in DOM\r\n   */\r\n  updateErrorMessage(error: string | null): void {\r\n    const errorId = this.config.errorDescribedBy || 'form-error';\r\n    let errorElement = document.getElementById(errorId);\r\n\r\n    if (error) {\r\n      if (!errorElement) {\r\n        errorElement = this.createErrorMessage(error);\r\n        document.body.appendChild(errorElement);\r\n      } else {\r\n        errorElement.textContent = error;\r\n      }\r\n    } else if (errorElement) {\r\n      errorElement.remove();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get status message for current state\r\n   */\r\n  getStatusMessage(\r\n    state: string,\r\n    retryAttempt?: number,\r\n    maxAttempts?: number\r\n  ): string {\r\n    switch (state) {\r\n      case 'validating':\r\n        return 'Validating form data...';\r\n      case 'submitting':\r\n        return 'Submitting form...';\r\n      case 'retrying':\r\n        return `Retrying submission... (Attempt ${retryAttempt || 1}/${maxAttempts || 3})`;\r\n      case 'success':\r\n        return 'Form submitted successfully';\r\n      case 'error':\r\n        return 'Form submission failed';\r\n      default:\r\n        return '';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Set up keyboard navigation for submission states\r\n   */\r\n  setupKeyboardNavigation(): () => void {\r\n    // Add keyboard shortcuts for common actions\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      // Escape key to cancel submission\r\n      if (event.key === 'Escape') {\r\n        const cancelButton = document.querySelector(\r\n          '[data-form-cancel]'\r\n        ) as HTMLElement;\r\n        if (cancelButton) {\r\n          cancelButton.click();\r\n        }\r\n      }\r\n\r\n      // Ctrl+Enter or Cmd+Enter to submit\r\n      if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {\r\n        const submitButton = document.querySelector(\r\n          '[type=\"submit\"]'\r\n        ) as HTMLButtonElement;\r\n        if (submitButton && !submitButton.disabled) {\r\n          submitButton.click();\r\n        }\r\n      }\r\n    };\r\n\r\n    document.addEventListener('keydown', handleKeyDown);\r\n\r\n    // Return cleanup function\r\n    return () => {\r\n      document.removeEventListener('keydown', handleKeyDown);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create progress announcements for long-running operations\r\n   */\r\n  announceProgress(\r\n    step: number,\r\n    totalSteps: number,\r\n    stepDescription: string\r\n  ): void {\r\n    if (!this.config.screenReaderAnnouncements) return;\r\n\r\n    const message = `Step ${step} of ${totalSteps}: ${stepDescription}`;\r\n    this.announceStatus(message, 'polite');\r\n  }\r\n\r\n  /**\r\n   * Cleanup accessibility resources\r\n   */\r\n  cleanup(): void {\r\n    // Remove live region\r\n    const liveRegion = document.getElementById('form-submission-announcements');\r\n    if (liveRegion) {\r\n      liveRegion.remove();\r\n    }\r\n\r\n    // Remove error message\r\n    this.updateErrorMessage(null);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAWM,MAAM;IACH,OAA4B;IAEpC,YAAY,MAA2B,CAAE;QACvC,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;GAEC,GACD,eACE,OAAe,EACf,WAAmC,QAAQ,EACrC;QACN,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE;YACzE;QACF;QAEA,iDAAiD;QACjD,IAAI,aAAa,SAAS,cAAc,CAAC;QACzC,IAAI,CAAC,YAAY;YACf,aAAa,SAAS,aAAa,CAAC;YACpC,WAAW,EAAE,GAAG;YAChB,WAAW,YAAY,CAAC,aAAa;YACrC,WAAW,YAAY,CAAC,eAAe;YACvC,WAAW,SAAS,GAClB;YACF,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;QAEA,0BAA0B;QAC1B,WAAW,WAAW,GAAG;QAEzB,2EAA2E;QAC3E,WAAW;YACT,IAAI,cAAc,WAAW,WAAW,KAAK,SAAS;gBACpD,WAAW,WAAW,GAAG;YAC3B;QACF,GAAG;IACL;IAEA;;GAEC,GACD,uBACE,SAAkB,EAClB,QAAiB,EACjB,KAAa,EACG;QAChB,OAAO;YACL,aAAa;YACb,gBAAgB;YAChB,oBACE,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,WAAW,eAAe,SAAS;YACtE,aACE,UAAU,gBAAgB,UAAU,eAAe,WAAW;QAClE;IACF;IAEA;;GAEC,GACD,YACE,SAAwC,EACxC,SAAuC,EACjC;QACN,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,KAAK,QAAQ;QAE5C,OAAQ;YACN,KAAK;gBACH,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,KAAK,iBAAiB,WAAW;oBAC9D,+BAA+B;oBAC/B,UAAU;gBACZ;gBACA;YAEF,KAAK;gBACH,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,KAAK,mBAAmB;oBACrD,qCAAqC;oBACrC,MAAM,iBAAiB,SAAS,cAAc,CAC5C;oBAEF,IAAI,gBAAgB;wBAClB,eAAe,KAAK;oBACtB;gBACF,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,KAAK,gBAAgB,WAAW;oBACpE,4CAA4C;oBAC5C,UAAU;gBACZ;gBACA;YAEF,KAAK;gBACH,IAAI,WAAW;oBACb,oCAAoC;oBACpC,UAAU;gBACZ;gBACA;QACJ;IACF;IAEA;;GAEC,GACD,mBAAmB,KAAa,EAAe;QAC7C,MAAM,eAAe,SAAS,aAAa,CAAC;QAC5C,aAAa,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI;QAClD,aAAa,YAAY,CAAC,QAAQ;QAClC,aAAa,YAAY,CAAC,aAAa;QACvC,aAAa,SAAS,GAAG;QACzB,aAAa,WAAW,GAAG;QAE3B,OAAO;IACT;IAEA;;GAEC,GACD,mBAAmB,KAAoB,EAAQ;QAC7C,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI;QAChD,IAAI,eAAe,SAAS,cAAc,CAAC;QAE3C,IAAI,OAAO;YACT,IAAI,CAAC,cAAc;gBACjB,eAAe,IAAI,CAAC,kBAAkB,CAAC;gBACvC,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,aAAa,WAAW,GAAG;YAC7B;QACF,OAAO,IAAI,cAAc;YACvB,aAAa,MAAM;QACrB;IACF;IAEA;;GAEC,GACD,iBACE,KAAa,EACb,YAAqB,EACrB,WAAoB,EACZ;QACR,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,gCAAgC,EAAE,gBAAgB,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,CAAC;YACpF,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,0BAAsC;QACpC,4CAA4C;QAC5C,MAAM,gBAAgB,CAAC;YACrB,kCAAkC;YAClC,IAAI,MAAM,GAAG,KAAK,UAAU;gBAC1B,MAAM,eAAe,SAAS,aAAa,CACzC;gBAEF,IAAI,cAAc;oBAChB,aAAa,KAAK;gBACpB;YACF;YAEA,oCAAoC;YACpC,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,KAAK,MAAM,GAAG,KAAK,SAAS;gBAC7D,MAAM,eAAe,SAAS,aAAa,CACzC;gBAEF,IAAI,gBAAgB,CAAC,aAAa,QAAQ,EAAE;oBAC1C,aAAa,KAAK;gBACpB;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QAErC,0BAA0B;QAC1B,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF;IAEA;;GAEC,GACD,iBACE,IAAY,EACZ,UAAkB,EAClB,eAAuB,EACjB;QACN,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE;QAE5C,MAAM,UAAU,CAAC,KAAK,EAAE,KAAK,IAAI,EAAE,WAAW,EAAE,EAAE,iBAAiB;QACnE,IAAI,CAAC,cAAc,CAAC,SAAS;IAC/B;IAEA;;GAEC,GACD,UAAgB;QACd,qBAAqB;QACrB,MAAM,aAAa,SAAS,cAAc,CAAC;QAC3C,IAAI,YAAY;YACd,WAAW,MAAM;QACnB;QAEA,uBAAuB;QACvB,IAAI,CAAC,kBAAkB,CAAC;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 1934, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/forms/services/FormSubmissionRetryService.ts"], "sourcesContent": ["/**\r\n * @file Form Submission Retry Service\r\n * @description Handles retry logic for form submissions following SRP\r\n * @version 2.0.0\r\n * <AUTHOR> Development Team\r\n */\r\n\r\nimport type { RetryConfig } from '../types/FormSubmissionTypes';\r\n\r\n/**\r\n * Form submission retry service following SRP\r\n * Responsible only for handling retry logic and timing\r\n */\r\nexport class FormSubmissionRetryService {\r\n  private config: RetryConfig;\r\n  private currentAttempt: number = 0;\r\n\r\n  constructor(config: RetryConfig) {\r\n    this.config = config;\r\n  }\r\n\r\n  /**\r\n   * Check if retry should be attempted\r\n   */\r\n  shouldRetry(error: Error): boolean {\r\n    return (\r\n      this.currentAttempt < this.config.maxAttempts &&\r\n      (this.config.retryCondition ? this.config.retryCondition(error) : true)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get the delay for the next retry attempt\r\n   */\r\n  getRetryDelay(): number {\r\n    const baseDelay = this.config.delay;\r\n\r\n    if (this.config.exponentialBackoff) {\r\n      return baseDelay * Math.pow(2, this.currentAttempt);\r\n    }\r\n\r\n    return baseDelay;\r\n  }\r\n\r\n  /**\r\n   * Increment the retry attempt counter\r\n   */\r\n  incrementAttempt(): number {\r\n    this.currentAttempt += 1;\r\n    return this.currentAttempt;\r\n  }\r\n\r\n  /**\r\n   * Reset the retry attempt counter\r\n   */\r\n  resetAttempts(): void {\r\n    this.currentAttempt = 0;\r\n  }\r\n\r\n  /**\r\n   * Get current attempt number\r\n   */\r\n  getCurrentAttempt(): number {\r\n    return this.currentAttempt;\r\n  }\r\n\r\n  /**\r\n   * Get maximum attempts\r\n   */\r\n  getMaxAttempts(): number {\r\n    return this.config.maxAttempts;\r\n  }\r\n\r\n  /**\r\n   * Sleep utility for retry delays\r\n   */\r\n  async sleep(ms: number): Promise<void> {\r\n    return new Promise(resolve => setTimeout(resolve, ms));\r\n  }\r\n\r\n  /**\r\n   * Execute retry with proper delay\r\n   */\r\n  async executeRetry<T>(retryFn: () => Promise<T>): Promise<T> {\r\n    if (!this.shouldRetry(new Error('Manual retry'))) {\r\n      throw new Error('Maximum retry attempts exceeded');\r\n    }\r\n\r\n    const delay = this.getRetryDelay();\r\n    this.incrementAttempt();\r\n\r\n    await this.sleep(delay);\r\n    return retryFn();\r\n  }\r\n\r\n  /**\r\n   * Get retry status information\r\n   */\r\n  getRetryStatus(): {\r\n    currentAttempt: number;\r\n    maxAttempts: number;\r\n    hasRetriesLeft: boolean;\r\n    nextDelay: number;\r\n  } {\r\n    return {\r\n      currentAttempt: this.currentAttempt,\r\n      maxAttempts: this.config.maxAttempts,\r\n      hasRetriesLeft: this.currentAttempt < this.config.maxAttempts,\r\n      nextDelay: this.getRetryDelay(),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Create a new retry service with updated configuration\r\n   */\r\n  withConfig(newConfig: Partial<RetryConfig>): FormSubmissionRetryService {\r\n    return new FormSubmissionRetryService({\r\n      ...this.config,\r\n      ...newConfig,\r\n    });\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAQM,MAAM;IACH,OAAoB;IACpB,iBAAyB,EAAE;IAEnC,YAAY,MAAmB,CAAE;QAC/B,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;GAEC,GACD,YAAY,KAAY,EAAW;QACjC,OACE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAC7C,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,IAAI;IAE1E;IAEA;;GAEC,GACD,gBAAwB;QACtB,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC,KAAK;QAEnC,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE;YAClC,OAAO,YAAY,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc;QACpD;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,mBAA2B;QACzB,IAAI,CAAC,cAAc,IAAI;QACvB,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA;;GAEC,GACD,gBAAsB;QACpB,IAAI,CAAC,cAAc,GAAG;IACxB;IAEA;;GAEC,GACD,oBAA4B;QAC1B,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA;;GAEC,GACD,iBAAyB;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW;IAChC;IAEA;;GAEC,GACD,MAAM,MAAM,EAAU,EAAiB;QACrC,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IACpD;IAEA;;GAEC,GACD,MAAM,aAAgB,OAAyB,EAAc;QAC3D,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,MAAM,kBAAkB;YAChD,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,QAAQ,IAAI,CAAC,aAAa;QAChC,IAAI,CAAC,gBAAgB;QAErB,MAAM,IAAI,CAAC,KAAK,CAAC;QACjB,OAAO;IACT;IAEA;;GAEC,GACD,iBAKE;QACA,OAAO;YACL,gBAAgB,IAAI,CAAC,cAAc;YACnC,aAAa,IAAI,CAAC,MAAM,CAAC,WAAW;YACpC,gBAAgB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW;YAC7D,WAAW,IAAI,CAAC,aAAa;QAC/B;IACF;IAEA;;GAEC,GACD,WAAW,SAA+B,EAA8B;QACtE,OAAO,IAAI,2BAA2B;YACpC,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,SAAS;QACd;IACF;AACF", "debugId": null}}, {"offset": {"line": 2024, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/forms/services/FormSubmissionPerformanceService.ts"], "sourcesContent": ["/**\r\n * @file Form Submission Performance Service\r\n * @description Handles performance tracking and optimization for form submissions following SRP\r\n * @version 2.0.0\r\n * <AUTHOR> Development Team\r\n */\r\n\r\nimport type {\r\n  PerformanceConfig,\r\n  PerformanceMetrics,\r\n} from '../types/FormSubmissionTypes';\r\n\r\n/**\r\n * Form submission performance service following SRP\r\n * Responsible only for performance metrics and optimization\r\n */\r\nexport class FormSubmissionPerformanceService {\r\n  private config: PerformanceConfig;\r\n  private metrics: PerformanceMetrics;\r\n  private submissionStartTime: number | null = null;\r\n  private debounceTimer: NodeJS.Timeout | null = null;\r\n\r\n  constructor(config: PerformanceConfig) {\r\n    this.config = config;\r\n    this.metrics = {\r\n      totalSubmissions: 0,\r\n      successfulSubmissions: 0,\r\n      failedSubmissions: 0,\r\n      averageDuration: 0,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Start tracking submission performance\r\n   */\r\n  startTiming(): void {\r\n    this.submissionStartTime = Date.now();\r\n  }\r\n\r\n  /**\r\n   * End tracking and update metrics\r\n   */\r\n  endTiming(success: boolean): number {\r\n    if (!this.submissionStartTime) {\r\n      return 0;\r\n    }\r\n\r\n    const duration = Date.now() - this.submissionStartTime;\r\n    this.updateMetrics(success, duration);\r\n    this.submissionStartTime = null;\r\n\r\n    return duration;\r\n  }\r\n\r\n  /**\r\n   * Update performance metrics\r\n   */\r\n  private updateMetrics(success: boolean, duration: number): void {\r\n    const newTotal = this.metrics.totalSubmissions + 1;\r\n    const newSuccessful = success\r\n      ? this.metrics.successfulSubmissions + 1\r\n      : this.metrics.successfulSubmissions;\r\n    const newFailed = success\r\n      ? this.metrics.failedSubmissions\r\n      : this.metrics.failedSubmissions + 1;\r\n\r\n    const totalDuration =\r\n      this.metrics.averageDuration * this.metrics.totalSubmissions + duration;\r\n\r\n    this.metrics = {\r\n      totalSubmissions: newTotal,\r\n      successfulSubmissions: newSuccessful,\r\n      failedSubmissions: newFailed,\r\n      averageDuration: totalDuration / newTotal,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get current performance metrics\r\n   */\r\n  getMetrics(): PerformanceMetrics {\r\n    return { ...this.metrics };\r\n  }\r\n\r\n  /**\r\n   * Reset performance metrics\r\n   */\r\n  resetMetrics(): void {\r\n    this.metrics = {\r\n      totalSubmissions: 0,\r\n      successfulSubmissions: 0,\r\n      failedSubmissions: 0,\r\n      averageDuration: 0,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Apply debouncing to function execution\r\n   */\r\n  debounce<T extends (...args: any[]) => void>(\r\n    func: T,\r\n    delay: number = this.config.debounceMs\r\n  ): (...args: Parameters<T>) => void {\r\n    return (...args: Parameters<T>) => {\r\n      if (this.debounceTimer) {\r\n        clearTimeout(this.debounceTimer);\r\n      }\r\n\r\n      this.debounceTimer = setTimeout(() => {\r\n        func(...args);\r\n      }, delay);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Clear any pending debounced operations\r\n   */\r\n  clearDebounce(): void {\r\n    if (this.debounceTimer) {\r\n      clearTimeout(this.debounceTimer);\r\n      this.debounceTimer = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Create timeout promise for request timeout handling\r\n   */\r\n  createTimeoutPromise(\r\n    timeoutMs: number = this.config.timeoutMs\r\n  ): Promise<never> {\r\n    return new Promise((_, reject) => {\r\n      setTimeout(() => {\r\n        reject(new Error(`Request timeout after ${timeoutMs}ms`));\r\n      }, timeoutMs);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Wrap a promise with timeout functionality\r\n   */\r\n  async withTimeout<T>(\r\n    promise: Promise<T>,\r\n    timeoutMs: number = this.config.timeoutMs\r\n  ): Promise<T> {\r\n    return Promise.race([promise, this.createTimeoutPromise(timeoutMs)]);\r\n  }\r\n\r\n  /**\r\n   * Get success rate as percentage\r\n   */\r\n  getSuccessRate(): number {\r\n    if (this.metrics.totalSubmissions === 0) return 0;\r\n    return (\r\n      (this.metrics.successfulSubmissions / this.metrics.totalSubmissions) * 100\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Get failure rate as percentage\r\n   */\r\n  getFailureRate(): number {\r\n    if (this.metrics.totalSubmissions === 0) return 0;\r\n    return (\r\n      (this.metrics.failedSubmissions / this.metrics.totalSubmissions) * 100\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Check if performance is within acceptable thresholds\r\n   */\r\n  isPerformanceAcceptable(thresholds?: {\r\n    maxAverageDuration?: number;\r\n    minSuccessRate?: number;\r\n  }): boolean {\r\n    const defaultThresholds = {\r\n      maxAverageDuration: 5000, // 5 seconds\r\n      minSuccessRate: 95, // 95%\r\n    };\r\n\r\n    const config = { ...defaultThresholds, ...thresholds };\r\n\r\n    return (\r\n      this.metrics.averageDuration <= config.maxAverageDuration &&\r\n      this.getSuccessRate() >= config.minSuccessRate\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Generate performance report\r\n   */\r\n  generateReport(): {\r\n    metrics: PerformanceMetrics;\r\n    successRate: number;\r\n    failureRate: number;\r\n    isAcceptable: boolean;\r\n    recommendations: string[];\r\n  } {\r\n    const successRate = this.getSuccessRate();\r\n    const failureRate = this.getFailureRate();\r\n    const isAcceptable = this.isPerformanceAcceptable();\r\n\r\n    const recommendations: string[] = [];\r\n\r\n    if (this.metrics.averageDuration > 3000) {\r\n      recommendations.push(\r\n        'Consider optimizing form validation or submission logic'\r\n      );\r\n    }\r\n\r\n    if (successRate < 90) {\r\n      recommendations.push(\r\n        'High failure rate detected - review error handling'\r\n      );\r\n    }\r\n\r\n    if (\r\n      this.metrics.totalSubmissions > 100 &&\r\n      this.metrics.averageDuration > 1000\r\n    ) {\r\n      recommendations.push(\r\n        'Consider implementing caching for better performance'\r\n      );\r\n    }\r\n\r\n    return {\r\n      metrics: this.getMetrics(),\r\n      successRate,\r\n      failureRate,\r\n      isAcceptable,\r\n      recommendations,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Cleanup performance tracking resources\r\n   */\r\n  cleanup(): void {\r\n    this.clearDebounce();\r\n    this.submissionStartTime = null;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAWM,MAAM;IACH,OAA0B;IAC1B,QAA4B;IAC5B,sBAAqC,KAAK;IAC1C,gBAAuC,KAAK;IAEpD,YAAY,MAAyB,CAAE;QACrC,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;YACb,kBAAkB;YAClB,uBAAuB;YACvB,mBAAmB;YACnB,iBAAiB;QACnB;IACF;IAEA;;GAEC,GACD,cAAoB;QAClB,IAAI,CAAC,mBAAmB,GAAG,KAAK,GAAG;IACrC;IAEA;;GAEC,GACD,UAAU,OAAgB,EAAU;QAClC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,OAAO;QACT;QAEA,MAAM,WAAW,KAAK,GAAG,KAAK,IAAI,CAAC,mBAAmB;QACtD,IAAI,CAAC,aAAa,CAAC,SAAS;QAC5B,IAAI,CAAC,mBAAmB,GAAG;QAE3B,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,cAAc,OAAgB,EAAE,QAAgB,EAAQ;QAC9D,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG;QACjD,MAAM,gBAAgB,UAClB,IAAI,CAAC,OAAO,CAAC,qBAAqB,GAAG,IACrC,IAAI,CAAC,OAAO,CAAC,qBAAqB;QACtC,MAAM,YAAY,UACd,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAC9B,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG;QAErC,MAAM,gBACJ,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG;QAEjE,IAAI,CAAC,OAAO,GAAG;YACb,kBAAkB;YAClB,uBAAuB;YACvB,mBAAmB;YACnB,iBAAiB,gBAAgB;QACnC;IACF;IAEA;;GAEC,GACD,aAAiC;QAC/B,OAAO;YAAE,GAAG,IAAI,CAAC,OAAO;QAAC;IAC3B;IAEA;;GAEC,GACD,eAAqB;QACnB,IAAI,CAAC,OAAO,GAAG;YACb,kBAAkB;YAClB,uBAAuB;YACvB,mBAAmB;YACnB,iBAAiB;QACnB;IACF;IAEA;;GAEC,GACD,SACE,IAAO,EACP,QAAgB,IAAI,CAAC,MAAM,CAAC,UAAU,EACJ;QAClC,OAAO,CAAC,GAAG;YACT,IAAI,IAAI,CAAC,aAAa,EAAE;gBACtB,aAAa,IAAI,CAAC,aAAa;YACjC;YAEA,IAAI,CAAC,aAAa,GAAG,WAAW;gBAC9B,QAAQ;YACV,GAAG;QACL;IACF;IAEA;;GAEC,GACD,gBAAsB;QACpB,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,aAAa,IAAI,CAAC,aAAa;YAC/B,IAAI,CAAC,aAAa,GAAG;QACvB;IACF;IAEA;;GAEC,GACD,qBACE,YAAoB,IAAI,CAAC,MAAM,CAAC,SAAS,EACzB;QAChB,OAAO,IAAI,QAAQ,CAAC,GAAG;YACrB,WAAW;gBACT,OAAO,IAAI,MAAM,CAAC,sBAAsB,EAAE,UAAU,EAAE,CAAC;YACzD,GAAG;QACL;IACF;IAEA;;GAEC,GACD,MAAM,YACJ,OAAmB,EACnB,YAAoB,IAAI,CAAC,MAAM,CAAC,SAAS,EAC7B;QACZ,OAAO,QAAQ,IAAI,CAAC;YAAC;YAAS,IAAI,CAAC,oBAAoB,CAAC;SAAW;IACrE;IAEA;;GAEC,GACD,iBAAyB;QACvB,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,KAAK,GAAG,OAAO;QAChD,OACE,AAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAI;IAE3E;IAEA;;GAEC,GACD,iBAAyB;QACvB,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,KAAK,GAAG,OAAO;QAChD,OACE,AAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAI;IAEvE;IAEA;;GAEC,GACD,wBAAwB,UAGvB,EAAW;QACV,MAAM,oBAAoB;YACxB,oBAAoB;YACpB,gBAAgB;QAClB;QAEA,MAAM,SAAS;YAAE,GAAG,iBAAiB;YAAE,GAAG,UAAU;QAAC;QAErD,OACE,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,OAAO,kBAAkB,IACzD,IAAI,CAAC,cAAc,MAAM,OAAO,cAAc;IAElD;IAEA;;GAEC,GACD,iBAME;QACA,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,MAAM,eAAe,IAAI,CAAC,uBAAuB;QAEjD,MAAM,kBAA4B,EAAE;QAEpC,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,MAAM;YACvC,gBAAgB,IAAI,CAClB;QAEJ;QAEA,IAAI,cAAc,IAAI;YACpB,gBAAgB,IAAI,CAClB;QAEJ;QAEA,IACE,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,OAChC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,MAC/B;YACA,gBAAgB,IAAI,CAClB;QAEJ;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,UAAU;YACxB;YACA;YACA;YACA;QACF;IACF;IAEA;;GAEC,GACD,UAAgB;QACd,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,mBAAmB,GAAG;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 2192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/forms/useFormSubmission.ts"], "sourcesContent": ["/**\r\n * @file Universal Form Submission Hook - SOLID Architecture\r\n * @description Modern form submission hook following SOLID principles\r\n * @version 4.0.0 - Legacy-free implementation\r\n * <AUTHOR> Development Team\r\n *\r\n * SOLID Principles Applied:\r\n * ✅ Single Responsibility Principle (SRP): Each service handles one concern\r\n * ✅ Open/Closed Principle: Extensible through configuration and services\r\n * ✅ Liskov Substitution Principle: Compatible service implementations\r\n * ✅ Interface Segregation Principle: Focused service interfaces\r\n * ✅ Dependency Inversion Principle: Abstraction-based dependencies\r\n *\r\n * Modern Features:\r\n * 🚀 Specialized service architecture with clean separation\r\n * 🎯 Integrated toast service with entity-specific messaging\r\n * 🔄 Built-in retry logic with exponential backoff\r\n * ♿ Comprehensive accessibility support\r\n * 📊 Performance tracking and optimization\r\n * 🛡️ Type-safe implementation with full TypeScript support\r\n */\r\n\r\nimport type React from 'react';\r\nimport type { FieldValues } from 'react-hook-form';\r\nimport { useCallback, useState, useRef, useEffect } from 'react';\r\n\r\n// Import types\r\nimport type {\r\n  FormSubmissionOptions,\r\n  FormSubmissionResult,\r\n  SubmissionState,\r\n} from './types/FormSubmissionTypes';\r\n\r\n// Import services following SRP\r\nimport { FormSubmissionConfigService } from './services/FormSubmissionConfig';\r\nimport { FormSubmissionToastService } from './services/FormSubmissionToastService';\r\nimport { FormSubmissionAccessibilityService } from './services/FormSubmissionAccessibilityService';\r\nimport { FormSubmissionRetryService } from './services/FormSubmissionRetryService';\r\nimport { FormSubmissionPerformanceService } from './services/FormSubmissionPerformanceService';\r\n\r\n/**\r\n * Universal Form Submission Hook - Modern SOLID Architecture\r\n *\r\n * Coordinates specialized services following SOLID principles for clean,\r\n * maintainable, and accessible form submission handling.\r\n *\r\n * @example Entity-Specific Form Submission\r\n * ```typescript\r\n * const { handleSubmit, isLoading, error, state } = useFormSubmission(\r\n *   async (data) => await api.createEmployee(data),\r\n *   {\r\n *     toast: {\r\n *       entityType: 'employee',\r\n *       entity: { name: data.name },\r\n *     },\r\n *     preSubmitValidation: (data) => data.name && data.email,\r\n *     transformData: (data) => ({\r\n *       ...data,\r\n *       email: data.email.toLowerCase().trim(),\r\n *     }),\r\n *   }\r\n * );\r\n * ```\r\n *\r\n * @example Production-Ready Form with All Features\r\n * ```typescript\r\n * const {\r\n *   handleSubmit,\r\n *   state,\r\n *   retry,\r\n *   ariaAttributes,\r\n *   metrics\r\n * } = useFormSubmission(\r\n *   async (data) => await api.createVehicle(data),\r\n *   {\r\n *     toast: {\r\n *       entityType: 'vehicle',\r\n *       entity: { make: data.make, model: data.model },\r\n *     },\r\n *     preSubmitValidation: async (data) => {\r\n *       return data.make && data.model && data.licensePlate;\r\n *     },\r\n *     transformData: (data) => ({\r\n *       ...data,\r\n *       year: parseInt(data.year, 10),\r\n *       licensePlate: data.licensePlate.toUpperCase(),\r\n *     }),\r\n *     retry: {\r\n *       maxAttempts: 3,\r\n *       exponentialBackoff: true,\r\n *       retryCondition: (error) => error.message.includes('network'),\r\n *     },\r\n *     accessibility: {\r\n *       focusManagement: 'first-error',\r\n *       announceStatus: true,\r\n *     },\r\n *     performance: {\r\n *       debounceMs: 300,\r\n *       timeoutMs: 30000,\r\n *     },\r\n *     onSuccess: (data, result) => {\r\n *       router.push(`/vehicles/${result.id}`);\r\n *     },\r\n *   }\r\n * );\r\n * ```\r\n */\r\nexport const useFormSubmission = <T extends FieldValues>(\r\n  onSubmit: (data: T) => Promise<any>,\r\n  options: FormSubmissionOptions<T> = {}\r\n): FormSubmissionResult<T> => {\r\n  // Merge configurations using config service\r\n  const retryConfig = FormSubmissionConfigService.mergeRetryConfig(\r\n    options.retry\r\n  );\r\n  const accessibilityConfig =\r\n    FormSubmissionConfigService.mergeAccessibilityConfig(options.accessibility);\r\n  const performanceConfig = FormSubmissionConfigService.mergePerformanceConfig(\r\n    options.performance\r\n  );\r\n  const toastConfig = FormSubmissionConfigService.mergeToastConfig(\r\n    options.toast\r\n  );\r\n\r\n  // Initialize services following SRP\r\n  const accessibilityService = useRef(\r\n    new FormSubmissionAccessibilityService(accessibilityConfig)\r\n  ).current;\r\n  const retryService = useRef(\r\n    new FormSubmissionRetryService(retryConfig)\r\n  ).current;\r\n  const performanceService = useRef(\r\n    new FormSubmissionPerformanceService(performanceConfig)\r\n  ).current;\r\n\r\n  // Core state management\r\n  const [state, setState] = useState<SubmissionState>('idle');\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [errorObject, setErrorObject] = useState<Error | null>(null);\r\n  const [lastSubmittedData, setLastSubmittedData] = useState<T | null>(null);\r\n  const [lastSubmitted, setLastSubmitted] = useState<number | null>(null);\r\n  const [lastResult, setLastResult] = useState<any>(null);\r\n  const [submissionDuration, setSubmissionDuration] = useState<number | null>(\r\n    null\r\n  );\r\n\r\n  // Refs for cleanup and cancellation\r\n  const abortControllerRef = useRef<AbortController | null>(null);\r\n\r\n  // Derived state\r\n  const isLoading = state === 'submitting' || state === 'validating';\r\n  const isSuccess = state === 'success';\r\n  const isValidating = state === 'validating';\r\n  const isRetrying = state === 'retrying';\r\n  const retryAttempt = retryService.getCurrentAttempt();\r\n\r\n  // Cleanup on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (abortControllerRef.current) {\r\n        abortControllerRef.current.abort();\r\n      }\r\n      performanceService.cleanup();\r\n      accessibilityService.cleanup();\r\n    };\r\n  }, [performanceService, accessibilityService]);\r\n\r\n  // Utility Functions\r\n  const clearError = useCallback(() => {\r\n    setError(null);\r\n    setErrorObject(null);\r\n    accessibilityService.updateErrorMessage(null);\r\n    if (state === 'error') {\r\n      setState('idle');\r\n    }\r\n  }, [state, accessibilityService]);\r\n\r\n  const reset = useCallback(() => {\r\n    setState('idle');\r\n    setError(null);\r\n    setErrorObject(null);\r\n    setLastSubmittedData(null);\r\n    setLastSubmitted(null);\r\n    setLastResult(null);\r\n    setSubmissionDuration(null);\r\n    retryService.resetAttempts();\r\n    performanceService.resetMetrics();\r\n    accessibilityService.updateErrorMessage(null);\r\n  }, [retryService, performanceService, accessibilityService]);\r\n\r\n  const cancel = useCallback(() => {\r\n    if (abortControllerRef.current) {\r\n      abortControllerRef.current.abort();\r\n    }\r\n    setState('idle');\r\n    accessibilityService.announceStatus('Form submission cancelled');\r\n  }, [accessibilityService]);\r\n\r\n  // Core submission logic using coordinated services\r\n  const performSubmission = useCallback(\r\n    async (data: T, isRetryAttempt = false): Promise<void> => {\r\n      try {\r\n        // Start performance tracking\r\n        performanceService.startTiming();\r\n\r\n        // Create abort controller for cancellation\r\n        abortControllerRef.current = new AbortController();\r\n\r\n        // Set appropriate state and announce\r\n        const currentState = isRetryAttempt ? 'retrying' : 'submitting';\r\n        setState(currentState);\r\n\r\n        const statusMessage = accessibilityService.getStatusMessage(\r\n          currentState,\r\n          retryAttempt,\r\n          retryConfig.maxAttempts\r\n        );\r\n        accessibilityService.announceStatus(statusMessage);\r\n\r\n        // Call onSubmitStart callback\r\n        if (options.onSubmitStart) {\r\n          await options.onSubmitStart(data);\r\n        }\r\n\r\n        // Pre-submission validation\r\n        if (options.preSubmitValidation) {\r\n          setState('validating');\r\n          accessibilityService.announceStatus('Validating form data...');\r\n\r\n          const isValid = await options.preSubmitValidation(data);\r\n          if (!isValid) {\r\n            throw new Error('Validation failed');\r\n          }\r\n        }\r\n\r\n        setState(currentState);\r\n\r\n        // Transform data if needed\r\n        let submissionData = data;\r\n        if (options.transformData) {\r\n          submissionData = await options.transformData(data);\r\n        }\r\n\r\n        // Perform submission with timeout\r\n        const submissionPromise = onSubmit(submissionData);\r\n        const result = await performanceService.withTimeout(submissionPromise);\r\n\r\n        // Transform result if needed\r\n        let finalResult = result;\r\n        if (options.transformResult) {\r\n          finalResult = await options.transformResult(result);\r\n        }\r\n\r\n        // Post-submission validation\r\n        if (options.postSubmitValidation) {\r\n          const isValid = await options.postSubmitValidation(finalResult);\r\n          if (!isValid) {\r\n            throw new Error('Post-submission validation failed');\r\n          }\r\n        }\r\n\r\n        // Success handling\r\n        const duration = performanceService.endTiming(true);\r\n        setState('success');\r\n        setLastResult(finalResult);\r\n        setLastSubmitted(Date.now());\r\n        setLastSubmittedData(data);\r\n        setSubmissionDuration(duration);\r\n        retryService.resetAttempts();\r\n\r\n        // Show success toast using integrated toast service\r\n        FormSubmissionToastService.showSuccessToast(\r\n          toastConfig,\r\n          data,\r\n          finalResult\r\n        );\r\n\r\n        // Announce success\r\n        accessibilityService.announceStatus(\r\n          'Form submitted successfully',\r\n          'assertive'\r\n        );\r\n        accessibilityService.manageFocus('success', options.formFocus);\r\n\r\n        // Reset form if configured\r\n        if (options.resetOnSuccess && options.formReset) {\r\n          options.formReset();\r\n        }\r\n\r\n        // Call success callback\r\n        if (options.onSuccess) {\r\n          await options.onSuccess(data, finalResult);\r\n        }\r\n\r\n        // Call completion callback\r\n        if (options.onSubmitComplete) {\r\n          await options.onSubmitComplete(data, true);\r\n        }\r\n      } catch (error_: unknown) {\r\n        const errorObj =\r\n          error_ instanceof Error ? error_ : new Error(String(error_));\r\n        const duration = performanceService.endTiming(false);\r\n\r\n        // Check if we should retry\r\n        const shouldRetry =\r\n          !isRetryAttempt && retryService.shouldRetry(errorObj);\r\n\r\n        if (shouldRetry) {\r\n          setState('retrying');\r\n          const delay = retryService.getRetryDelay();\r\n          retryService.incrementAttempt();\r\n\r\n          accessibilityService.announceStatus(\r\n            `Retrying in ${delay}ms... (Attempt ${retryService.getCurrentAttempt()}/${retryConfig.maxAttempts})`\r\n          );\r\n\r\n          await retryService.sleep(delay);\r\n          return performSubmission(data, true);\r\n        }\r\n\r\n        // Handle error\r\n        setState('error');\r\n        const errorMessage =\r\n          errorObj.message ||\r\n          toastConfig.errorMessage ||\r\n          'An unexpected error occurred';\r\n        setError(errorMessage);\r\n        setErrorObject(errorObj);\r\n        setSubmissionDuration(duration);\r\n\r\n        // Show error toast using integrated toast service\r\n        FormSubmissionToastService.showErrorToast(toastConfig, errorObj, data);\r\n\r\n        // Update accessibility\r\n        accessibilityService.updateErrorMessage(errorMessage);\r\n        accessibilityService.announceStatus(\r\n          `Error: ${errorMessage}`,\r\n          'assertive'\r\n        );\r\n        accessibilityService.manageFocus('error', options.formFocus);\r\n\r\n        // Call error callback\r\n        if (options.onError) {\r\n          await options.onError(errorObj, data);\r\n        }\r\n\r\n        // Call completion callback\r\n        if (options.onSubmitComplete) {\r\n          await options.onSubmitComplete(data, false);\r\n        }\r\n      }\r\n    },\r\n    [\r\n      onSubmit,\r\n      options,\r\n      retryService,\r\n      performanceService,\r\n      accessibilityService,\r\n      toastConfig,\r\n      retryConfig.maxAttempts,\r\n      retryAttempt,\r\n    ]\r\n  );\r\n\r\n  // Debounced submit handler using performance service\r\n  const handleSubmit = useCallback(\r\n    async (data: T, event?: React.BaseSyntheticEvent): Promise<void> => {\r\n      // Prevent default form submission\r\n      if (event) {\r\n        event.preventDefault();\r\n      }\r\n\r\n      // Apply debouncing using performance service\r\n      const debouncedSubmit = performanceService.debounce(\r\n        () => performSubmission(data),\r\n        performanceConfig.debounceMs\r\n      );\r\n\r\n      debouncedSubmit();\r\n    },\r\n    [performSubmission, performanceService, performanceConfig.debounceMs]\r\n  );\r\n\r\n  // Retry handler\r\n  const retry = useCallback(async (): Promise<void> => {\r\n    if (lastSubmittedData) {\r\n      retryService.resetAttempts(); // Reset retry count for manual retry\r\n      await performSubmission(lastSubmittedData);\r\n    }\r\n  }, [lastSubmittedData, performSubmission, retryService]);\r\n\r\n  // ARIA attributes using accessibility service\r\n  const ariaAttributes = accessibilityService.generateAriaAttributes(\r\n    isLoading,\r\n    !!error,\r\n    state\r\n  );\r\n\r\n  // Return comprehensive result object\r\n  return {\r\n    // Core State\r\n    isLoading,\r\n    state,\r\n    error,\r\n    errorObject,\r\n    isSuccess,\r\n    isValidating,\r\n    isRetrying,\r\n\r\n    // Submission Data\r\n    lastSubmittedData,\r\n    lastSubmitted,\r\n    lastResult,\r\n    retryAttempt,\r\n\r\n    // Actions\r\n    handleSubmit,\r\n    clearError,\r\n    reset,\r\n    retry,\r\n    cancel,\r\n\r\n    // Accessibility\r\n    ariaAttributes,\r\n\r\n    // Performance Metrics\r\n    submissionDuration,\r\n    metrics: performanceService.getMetrics(),\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC;;;AAID;AASA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;;;;;;;AAqEO,MAAM,oBAAoB,CAC/B,UACA,UAAoC,CAAC,CAAC;IAEtC,4CAA4C;IAC5C,MAAM,cAAc,yJAAA,CAAA,8BAA2B,CAAC,gBAAgB,CAC9D,QAAQ,KAAK;IAEf,MAAM,sBACJ,yJAAA,CAAA,8BAA2B,CAAC,wBAAwB,CAAC,QAAQ,aAAa;IAC5E,MAAM,oBAAoB,yJAAA,CAAA,8BAA2B,CAAC,sBAAsB,CAC1E,QAAQ,WAAW;IAErB,MAAM,cAAc,yJAAA,CAAA,8BAA2B,CAAC,gBAAgB,CAC9D,QAAQ,KAAK;IAGf,oCAAoC;IACpC,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAChC,IAAI,uKAAA,CAAA,qCAAkC,CAAC,sBACvC,OAAO;IACT,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EACxB,IAAI,+JAAA,CAAA,6BAA0B,CAAC,cAC/B,OAAO;IACT,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAC9B,IAAI,qKAAA,CAAA,mCAAgC,CAAC,oBACrC,OAAO;IAET,wBAAwB;IACxB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAClD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACzD;IAGF,oCAAoC;IACpC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAE1D,gBAAgB;IAChB,MAAM,YAAY,UAAU,gBAAgB,UAAU;IACtD,MAAM,YAAY,UAAU;IAC5B,MAAM,eAAe,UAAU;IAC/B,MAAM,aAAa,UAAU;IAC7B,MAAM,eAAe,aAAa,iBAAiB;IAEnD,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,mBAAmB,OAAO,CAAC,KAAK;YAClC;YACA,mBAAmB,OAAO;YAC1B,qBAAqB,OAAO;QAC9B;IACF,GAAG;QAAC;QAAoB;KAAqB;IAE7C,oBAAoB;IACpB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS;QACT,eAAe;QACf,qBAAqB,kBAAkB,CAAC;QACxC,IAAI,UAAU,SAAS;YACrB,SAAS;QACX;IACF,GAAG;QAAC;QAAO;KAAqB;IAEhC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,SAAS;QACT,SAAS;QACT,eAAe;QACf,qBAAqB;QACrB,iBAAiB;QACjB,cAAc;QACd,sBAAsB;QACtB,aAAa,aAAa;QAC1B,mBAAmB,YAAY;QAC/B,qBAAqB,kBAAkB,CAAC;IAC1C,GAAG;QAAC;QAAc;QAAoB;KAAqB;IAE3D,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,KAAK;QAClC;QACA,SAAS;QACT,qBAAqB,cAAc,CAAC;IACtC,GAAG;QAAC;KAAqB;IAEzB,mDAAmD;IACnD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,OAAO,MAAS,iBAAiB,KAAK;QACpC,IAAI;YACF,6BAA6B;YAC7B,mBAAmB,WAAW;YAE9B,2CAA2C;YAC3C,mBAAmB,OAAO,GAAG,IAAI;YAEjC,qCAAqC;YACrC,MAAM,eAAe,iBAAiB,aAAa;YACnD,SAAS;YAET,MAAM,gBAAgB,qBAAqB,gBAAgB,CACzD,cACA,cACA,YAAY,WAAW;YAEzB,qBAAqB,cAAc,CAAC;YAEpC,8BAA8B;YAC9B,IAAI,QAAQ,aAAa,EAAE;gBACzB,MAAM,QAAQ,aAAa,CAAC;YAC9B;YAEA,4BAA4B;YAC5B,IAAI,QAAQ,mBAAmB,EAAE;gBAC/B,SAAS;gBACT,qBAAqB,cAAc,CAAC;gBAEpC,MAAM,UAAU,MAAM,QAAQ,mBAAmB,CAAC;gBAClD,IAAI,CAAC,SAAS;oBACZ,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,SAAS;YAET,2BAA2B;YAC3B,IAAI,iBAAiB;YACrB,IAAI,QAAQ,aAAa,EAAE;gBACzB,iBAAiB,MAAM,QAAQ,aAAa,CAAC;YAC/C;YAEA,kCAAkC;YAClC,MAAM,oBAAoB,SAAS;YACnC,MAAM,SAAS,MAAM,mBAAmB,WAAW,CAAC;YAEpD,6BAA6B;YAC7B,IAAI,cAAc;YAClB,IAAI,QAAQ,eAAe,EAAE;gBAC3B,cAAc,MAAM,QAAQ,eAAe,CAAC;YAC9C;YAEA,6BAA6B;YAC7B,IAAI,QAAQ,oBAAoB,EAAE;gBAChC,MAAM,UAAU,MAAM,QAAQ,oBAAoB,CAAC;gBACnD,IAAI,CAAC,SAAS;oBACZ,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,mBAAmB;YACnB,MAAM,WAAW,mBAAmB,SAAS,CAAC;YAC9C,SAAS;YACT,cAAc;YACd,iBAAiB,KAAK,GAAG;YACzB,qBAAqB;YACrB,sBAAsB;YACtB,aAAa,aAAa;YAE1B,oDAAoD;YACpD,+JAAA,CAAA,6BAA0B,CAAC,gBAAgB,CACzC,aACA,MACA;YAGF,mBAAmB;YACnB,qBAAqB,cAAc,CACjC,+BACA;YAEF,qBAAqB,WAAW,CAAC,WAAW,QAAQ,SAAS;YAE7D,2BAA2B;YAC3B,IAAI,QAAQ,cAAc,IAAI,QAAQ,SAAS,EAAE;gBAC/C,QAAQ,SAAS;YACnB;YAEA,wBAAwB;YACxB,IAAI,QAAQ,SAAS,EAAE;gBACrB,MAAM,QAAQ,SAAS,CAAC,MAAM;YAChC;YAEA,2BAA2B;YAC3B,IAAI,QAAQ,gBAAgB,EAAE;gBAC5B,MAAM,QAAQ,gBAAgB,CAAC,MAAM;YACvC;QACF,EAAE,OAAO,QAAiB;YACxB,MAAM,WACJ,kBAAkB,QAAQ,SAAS,IAAI,MAAM,OAAO;YACtD,MAAM,WAAW,mBAAmB,SAAS,CAAC;YAE9C,2BAA2B;YAC3B,MAAM,cACJ,CAAC,kBAAkB,aAAa,WAAW,CAAC;YAE9C,IAAI,aAAa;gBACf,SAAS;gBACT,MAAM,QAAQ,aAAa,aAAa;gBACxC,aAAa,gBAAgB;gBAE7B,qBAAqB,cAAc,CACjC,CAAC,YAAY,EAAE,MAAM,eAAe,EAAE,aAAa,iBAAiB,GAAG,CAAC,EAAE,YAAY,WAAW,CAAC,CAAC,CAAC;gBAGtG,MAAM,aAAa,KAAK,CAAC;gBACzB,OAAO,kBAAkB,MAAM;YACjC;YAEA,eAAe;YACf,SAAS;YACT,MAAM,eACJ,SAAS,OAAO,IAChB,YAAY,YAAY,IACxB;YACF,SAAS;YACT,eAAe;YACf,sBAAsB;YAEtB,kDAAkD;YAClD,+JAAA,CAAA,6BAA0B,CAAC,cAAc,CAAC,aAAa,UAAU;YAEjE,uBAAuB;YACvB,qBAAqB,kBAAkB,CAAC;YACxC,qBAAqB,cAAc,CACjC,CAAC,OAAO,EAAE,cAAc,EACxB;YAEF,qBAAqB,WAAW,CAAC,SAAS,QAAQ,SAAS;YAE3D,sBAAsB;YACtB,IAAI,QAAQ,OAAO,EAAE;gBACnB,MAAM,QAAQ,OAAO,CAAC,UAAU;YAClC;YAEA,2BAA2B;YAC3B,IAAI,QAAQ,gBAAgB,EAAE;gBAC5B,MAAM,QAAQ,gBAAgB,CAAC,MAAM;YACvC;QACF;IACF,GACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA,YAAY,WAAW;QACvB;KACD;IAGH,qDAAqD;IACrD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7B,OAAO,MAAS;QACd,kCAAkC;QAClC,IAAI,OAAO;YACT,MAAM,cAAc;QACtB;QAEA,6CAA6C;QAC7C,MAAM,kBAAkB,mBAAmB,QAAQ,CACjD,IAAM,kBAAkB,OACxB,kBAAkB,UAAU;QAG9B;IACF,GACA;QAAC;QAAmB;QAAoB,kBAAkB,UAAU;KAAC;IAGvE,gBAAgB;IAChB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,IAAI,mBAAmB;YACrB,aAAa,aAAa,IAAI,qCAAqC;YACnE,MAAM,kBAAkB;QAC1B;IACF,GAAG;QAAC;QAAmB;QAAmB;KAAa;IAEvD,8CAA8C;IAC9C,MAAM,iBAAiB,qBAAqB,sBAAsB,CAChE,WACA,CAAC,CAAC,OACF;IAGF,qCAAqC;IACrC,OAAO;QACL,aAAa;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,kBAAkB;QAClB;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QAEA,gBAAgB;QAChB;QAEA,sBAAsB;QACtB;QACA,SAAS,mBAAmB,UAAU;IACxC;AACF", "debugId": null}}, {"offset": {"line": 2480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/forms/useFormToast.ts"], "sourcesContent": ["/**\r\n * Form Toast Hook - Integrates generic toast service with form operations\r\n *\r\n * This hook provides a consistent interface for showing toast notifications\r\n * in form components, supporting both generic and entity-specific messaging.\r\n */\r\n\r\nimport { useCallback } from 'react';\r\n\r\nimport {\r\n  type EntityToastConfig,\r\n  type GenericEntityToastService,\r\n  toastService,\r\n  createEntityToastService,\r\n} from '@/lib/services/toastService';\r\n\r\nexport interface FormToastOptions {\r\n  successTitle?: string;\r\n  successDescription?: string;\r\n  errorTitle?: string;\r\n  errorDescription?: string;\r\n}\r\n\r\nexport interface EntityFormToastOptions<T> {\r\n  entityConfig?: EntityToastConfig<T>;\r\n  entityService?: GenericEntityToastService<T>;\r\n}\r\n\r\n/**\r\n * Hook for generic form toast notifications\r\n */\r\nexport function useFormToast() {\r\n  const showSuccess = useCallback((title: string, description?: string) => {\r\n    return toastService.success(title, description);\r\n  }, []);\r\n\r\n  const showError = useCallback((title: string, description?: string) => {\r\n    return toastService.error(title, description);\r\n  }, []);\r\n\r\n  const showInfo = useCallback((title: string, description?: string) => {\r\n    return toastService.info(title, description);\r\n  }, []);\r\n\r\n  const showFormSuccess = useCallback(\r\n    (options?: FormToastOptions) => {\r\n      return showSuccess(\r\n        options?.successTitle || 'Success',\r\n        options?.successDescription || 'Operation completed successfully'\r\n      );\r\n    },\r\n    [showSuccess]\r\n  );\r\n\r\n  const showFormError = useCallback(\r\n    (error: Error | string, options?: FormToastOptions) => {\r\n      const errorMessage = error instanceof Error ? error.message : error;\r\n      return showError(\r\n        options?.errorTitle || 'Error',\r\n        options?.errorDescription ||\r\n          errorMessage ||\r\n          'An unexpected error occurred'\r\n      );\r\n    },\r\n    [showError]\r\n  );\r\n\r\n  return {\r\n    showSuccess,\r\n    showError,\r\n    showInfo,\r\n    showFormSuccess,\r\n    showFormError,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for entity-specific form toast notifications\r\n */\r\nexport function useEntityFormToast<T>(\r\n  entityConfig?: EntityToastConfig<T>,\r\n  entityService?: GenericEntityToastService<T>\r\n) {\r\n  const { showFormSuccess, showFormError } = useFormToast();\r\n\r\n  // Create or use provided entity service\r\n  const entityToastService =\r\n    entityService ||\r\n    (entityConfig ? createEntityToastService(entityConfig) : null);\r\n\r\n  const showEntityCreated = useCallback(\r\n    (entity: T) => {\r\n      if (entityToastService) {\r\n        return entityToastService.entityCreated(entity);\r\n      }\r\n      return showFormSuccess({\r\n        successTitle: 'Created',\r\n        successDescription: 'Item has been created successfully',\r\n      });\r\n    },\r\n    [entityToastService, showFormSuccess]\r\n  );\r\n\r\n  const showEntityUpdated = useCallback(\r\n    (entity: T) => {\r\n      if (entityToastService) {\r\n        return entityToastService.entityUpdated(entity);\r\n      }\r\n      return showFormSuccess({\r\n        successTitle: 'Updated',\r\n        successDescription: 'Item has been updated successfully',\r\n      });\r\n    },\r\n    [entityToastService, showFormSuccess]\r\n  );\r\n\r\n  const showEntityDeleted = useCallback(\r\n    (entity: T) => {\r\n      if (entityToastService) {\r\n        return entityToastService.entityDeleted(entity);\r\n      }\r\n      return showFormSuccess({\r\n        successTitle: 'Deleted',\r\n        successDescription: 'Item has been deleted successfully',\r\n      });\r\n    },\r\n    [entityToastService, showFormSuccess]\r\n  );\r\n\r\n  const showEntityCreationError = useCallback(\r\n    (error: Error | string) => {\r\n      if (entityToastService) {\r\n        const errorMessage = error instanceof Error ? error.message : error;\r\n        return entityToastService.entityCreationError(errorMessage);\r\n      }\r\n      return showFormError(error, { errorTitle: 'Creation Failed' });\r\n    },\r\n    [entityToastService, showFormError]\r\n  );\r\n\r\n  const showEntityUpdateError = useCallback(\r\n    (error: Error | string) => {\r\n      if (entityToastService) {\r\n        const errorMessage = error instanceof Error ? error.message : error;\r\n        return entityToastService.entityUpdateError(errorMessage);\r\n      }\r\n      return showFormError(error, { errorTitle: 'Update Failed' });\r\n    },\r\n    [entityToastService, showFormError]\r\n  );\r\n\r\n  const showEntityDeletionError = useCallback(\r\n    (error: Error | string) => {\r\n      if (entityToastService) {\r\n        const errorMessage = error instanceof Error ? error.message : error;\r\n        return entityToastService.entityDeletionError(errorMessage);\r\n      }\r\n      return showFormError(error, { errorTitle: 'Deletion Failed' });\r\n    },\r\n    [entityToastService, showFormError]\r\n  );\r\n\r\n  return {\r\n    showEntityCreated,\r\n    showEntityUpdated,\r\n    showEntityDeleted,\r\n    showEntityCreationError,\r\n    showEntityUpdateError,\r\n    showEntityDeletionError,\r\n    // Also expose generic methods\r\n    showFormSuccess,\r\n    showFormError,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for predefined entity toast services\r\n */\r\nexport function usePredefinedEntityToast(\r\n  entityType: 'employee' | 'vehicle' | 'task' | 'delegation'\r\n) {\r\n  let entityService: GenericEntityToastService<any>;\r\n\r\n  // Lazy import to avoid circular dependencies\r\n  switch (entityType) {\r\n    case 'employee':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').employeeToast;\r\n      break;\r\n    case 'vehicle':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').vehicleToast;\r\n      break;\r\n    case 'task':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').taskToast;\r\n      break;\r\n    case 'delegation':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').delegationToast;\r\n      break;\r\n    default:\r\n      throw new Error(`Unknown entity type: ${entityType}`);\r\n  }\r\n\r\n  return useEntityFormToast(undefined, entityService);\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAED;AAEA;;;AAsBO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC9C,OAAO,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,OAAO;IACrC,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC5C,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,OAAO;IACnC,GAAG,EAAE;IAEL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC3C,OAAO,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,OAAO;IAClC,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC;QACC,OAAO,YACL,SAAS,gBAAgB,WACzB,SAAS,sBAAsB;IAEnC,GACA;QAAC;KAAY;IAGf,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,OAAuB;QACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC9D,OAAO,UACL,SAAS,cAAc,SACvB,SAAS,oBACP,gBACA;IAEN,GACA;QAAC;KAAU;IAGb,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,mBACd,YAAmC,EACnC,aAA4C;IAE5C,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,GAAG;IAE3C,wCAAwC;IACxC,MAAM,qBACJ,iBACA,CAAC,eAAe,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE,gBAAgB,IAAI;IAE/D,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,IAAI,oBAAoB;YACtB,OAAO,mBAAmB,aAAa,CAAC;QAC1C;QACA,OAAO,gBAAgB;YACrB,cAAc;YACd,oBAAoB;QACtB;IACF,GACA;QAAC;QAAoB;KAAgB;IAGvC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,IAAI,oBAAoB;YACtB,OAAO,mBAAmB,aAAa,CAAC;QAC1C;QACA,OAAO,gBAAgB;YACrB,cAAc;YACd,oBAAoB;QACtB;IACF,GACA;QAAC;QAAoB;KAAgB;IAGvC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,IAAI,oBAAoB;YACtB,OAAO,mBAAmB,aAAa,CAAC;QAC1C;QACA,OAAO,gBAAgB;YACrB,cAAc;YACd,oBAAoB;QACtB;IACF,GACA;QAAC;QAAoB;KAAgB;IAGvC,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxC,CAAC;QACC,IAAI,oBAAoB;YACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO,mBAAmB,mBAAmB,CAAC;QAChD;QACA,OAAO,cAAc,OAAO;YAAE,YAAY;QAAkB;IAC9D,GACA;QAAC;QAAoB;KAAc;IAGrC,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACtC,CAAC;QACC,IAAI,oBAAoB;YACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO,mBAAmB,iBAAiB,CAAC;QAC9C;QACA,OAAO,cAAc,OAAO;YAAE,YAAY;QAAgB;IAC5D,GACA;QAAC;QAAoB;KAAc;IAGrC,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxC,CAAC;QACC,IAAI,oBAAoB;YACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO,mBAAmB,mBAAmB,CAAC;QAChD;QACA,OAAO,cAAc,OAAO;YAAE,YAAY;QAAkB;IAC9D,GACA;QAAC;QAAoB;KAAc;IAGrC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,8BAA8B;QAC9B;QACA;IACF;AACF;AAKO,SAAS,yBACd,UAA0D;IAE1D,IAAI;IAEJ,6CAA6C;IAC7C,OAAQ;QACN,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,6FAAuC,aAAa;YACpE;QACF,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,6FAAuC,YAAY;YACnE;QACF,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,6FAAuC,SAAS;YAChE;QACF,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,6FAAuC,eAAe;YACtE;QACF;YACE,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,YAAY;IACxD;IAEA,OAAO,mBAAmB,WAAW;AACvC", "debugId": null}}, {"offset": {"line": 2642, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/forms/useFormValidation.ts"], "sourcesContent": ["/**\r\n * @file Centralized Form Validation Hook\r\n * @description Provides consistent form validation patterns with Zod schema integration\r\n */\r\n\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { useCallback, useMemo } from 'react';\r\nimport {\r\n  type FieldErrors,\r\n  type FieldValues,\r\n  type UseFormReturn,\r\n  useForm,\r\n  type DefaultValues,\r\n  type Path,\r\n} from 'react-hook-form';\r\nimport { type ZodSchema, ZodError } from 'zod';\r\n\r\nimport { useToast } from '@/hooks/utils/use-toast';\r\n\r\n/**\r\n * Configuration options for form validation\r\n */\r\nexport interface FormValidationOptions<T extends FieldValues> {\r\n  /** Zod schema for validation */\r\n  schema: ZodSchema<T>;\r\n  /** Default values for the form */\r\n  defaultValues?: Partial<T>;\r\n  /** Validation mode */\r\n  mode?: 'onChange' | 'onBlur' | 'onSubmit' | 'onTouched' | 'all';\r\n  /** Whether to revalidate on change */\r\n  reValidateMode?: 'onChange' | 'onBlur' | 'onSubmit';\r\n  /** Whether to show validation errors immediately */\r\n  showErrorsImmediately?: boolean;\r\n  /** Custom error messages */\r\n  customErrorMessages?: Record<string, string>;\r\n}\r\n\r\n/**\r\n * Enhanced form validation result\r\n */\r\nexport interface FormValidationResult<T extends FieldValues> {\r\n  /** React Hook Form instance */\r\n  form: UseFormReturn<T>;\r\n  /** Whether the form is valid */\r\n  isValid: boolean;\r\n  /** Whether the form has been touched */\r\n  isDirty: boolean;\r\n  /** Current form errors */\r\n  errors: FieldErrors<T>;\r\n  /** Validate specific field */\r\n  validateField: (fieldName: keyof T) => Promise<boolean>;\r\n  /** Validate entire form */\r\n  validateForm: () => Promise<boolean>;\r\n  /** Clear all errors */\r\n  clearErrors: () => void;\r\n  /** Clear specific field error */\r\n  clearFieldError: (fieldName: keyof T) => void;\r\n  /** Get formatted error message for field */\r\n  getFieldError: (fieldName: keyof T) => string | undefined;\r\n  /** Check if specific field has error */\r\n  hasFieldError: (fieldName: keyof T) => boolean;\r\n  /** Reset form to default values */\r\n  resetForm: () => void;\r\n}\r\n\r\n/**\r\n * Centralized form validation hook with Zod schema integration\r\n *\r\n * @example\r\n * ```typescript\r\n * const { form, isValid, validateForm, getFieldError } = useFormValidation({\r\n *   schema: UserSchema,\r\n *   defaultValues: { name: '', email: '' },\r\n *   mode: 'onChange',\r\n *   showErrorsImmediately: true,\r\n * });\r\n * ```\r\n */\r\nexport const useFormValidation = <T extends FieldValues>(\r\n  options: FormValidationOptions<T>\r\n): FormValidationResult<T> => {\r\n  const {\r\n    schema,\r\n    defaultValues,\r\n    mode = 'onChange',\r\n    reValidateMode = 'onChange',\r\n    showErrorsImmediately = false,\r\n    customErrorMessages = {},\r\n  } = options;\r\n\r\n  const form = useForm<T>({\r\n    resolver: zodResolver(schema),\r\n    defaultValues: defaultValues as DefaultValues<T>,\r\n    mode,\r\n    reValidateMode,\r\n    criteriaMode: 'all', // Show all validation errors\r\n  });\r\n\r\n  const {\r\n    formState: { errors, isValid, isDirty },\r\n    trigger,\r\n    clearErrors: clearAllErrors,\r\n    setError,\r\n    reset,\r\n  } = form;\r\n\r\n  // Validate specific field\r\n  const validateField = useCallback(\r\n    async (fieldName: keyof T): Promise<boolean> => {\r\n      const result = await trigger(fieldName as Path<T>);\r\n      return result;\r\n    },\r\n    [trigger]\r\n  );\r\n\r\n  // Validate entire form\r\n  const validateForm = useCallback(async (): Promise<boolean> => {\r\n    const result = await trigger();\r\n    return result;\r\n  }, [trigger]);\r\n\r\n  // Clear all errors\r\n  const clearErrors = useCallback(() => {\r\n    clearAllErrors();\r\n  }, [clearAllErrors]);\r\n\r\n  // Clear specific field error\r\n  const clearFieldError = useCallback(\r\n    (fieldName: keyof T) => {\r\n      clearAllErrors(fieldName as Path<T>);\r\n    },\r\n    [clearAllErrors]\r\n  );\r\n\r\n  // Get formatted error message for field\r\n  const getFieldError = useCallback(\r\n    (fieldName: keyof T): string | undefined => {\r\n      const fieldError = errors[fieldName];\r\n      if (!fieldError) return undefined;\r\n\r\n      // Check for custom error message first\r\n      const customMessage = customErrorMessages[fieldName as string];\r\n      if (customMessage) return customMessage;\r\n\r\n      // Return the error message from validation\r\n      if (typeof fieldError.message === 'string') {\r\n        return fieldError.message;\r\n      }\r\n\r\n      // Fallback for complex error structures\r\n      return 'This field is invalid';\r\n    },\r\n    [errors, customErrorMessages]\r\n  );\r\n\r\n  // Check if specific field has error\r\n  const hasFieldError = useCallback(\r\n    (fieldName: keyof T): boolean => {\r\n      return !!errors[fieldName];\r\n    },\r\n    [errors]\r\n  );\r\n\r\n  // Reset form to default values\r\n  const resetForm = useCallback(() => {\r\n    reset(defaultValues as DefaultValues<T>);\r\n  }, [reset, defaultValues]);\r\n\r\n  const result: FormValidationResult<T> = useMemo(\r\n    () => ({\r\n      form,\r\n      isValid,\r\n      isDirty,\r\n      errors,\r\n      validateField,\r\n      validateForm,\r\n      clearErrors,\r\n      clearFieldError,\r\n      getFieldError,\r\n      hasFieldError,\r\n      resetForm,\r\n    }),\r\n    [\r\n      form,\r\n      isValid,\r\n      isDirty,\r\n      errors,\r\n      validateField,\r\n      validateForm,\r\n      clearErrors,\r\n      clearFieldError,\r\n      getFieldError,\r\n      hasFieldError,\r\n      resetForm,\r\n    ]\r\n  );\r\n\r\n  return result;\r\n};\r\n\r\n/**\r\n * Hook for validating form data without a form instance\r\n * Useful for validating data before submission or in other contexts\r\n */\r\nexport const useSchemaValidation = <T>(schema: ZodSchema<T>) => {\r\n  const validateData = useCallback(\r\n    (data: unknown): { isValid: boolean; errors?: ZodError; validData?: T } => {\r\n      try {\r\n        const validData = schema.parse(data);\r\n        return { isValid: true, validData };\r\n      } catch (error) {\r\n        if (error instanceof ZodError) {\r\n          return { isValid: false, errors: error };\r\n        }\r\n        throw error;\r\n      }\r\n    },\r\n    [schema]\r\n  );\r\n\r\n  const validateAsync = useCallback(\r\n    async (\r\n      data: unknown\r\n    ): Promise<{ isValid: boolean; errors?: ZodError; validData?: T }> => {\r\n      try {\r\n        const validData = await schema.parseAsync(data);\r\n        return { isValid: true, validData };\r\n      } catch (error) {\r\n        if (error instanceof ZodError) {\r\n          return { isValid: false, errors: error };\r\n        }\r\n        throw error;\r\n      }\r\n    },\r\n    [schema]\r\n  );\r\n\r\n  const getFieldErrors = useCallback(\r\n    (zodError: ZodError): Record<string, string> => {\r\n      const fieldErrors: Record<string, string> = {};\r\n\r\n      zodError.errors.forEach(error => {\r\n        const fieldPath = error.path.join('.');\r\n        fieldErrors[fieldPath] = error.message;\r\n      });\r\n\r\n      return fieldErrors;\r\n    },\r\n    []\r\n  );\r\n\r\n  return {\r\n    validateData,\r\n    validateAsync,\r\n    getFieldErrors,\r\n  };\r\n};\r\n\r\n/**\r\n * Hook for conditional validation based on form state\r\n * Useful for complex forms with conditional fields\r\n */\r\nexport const useConditionalValidation = <T extends FieldValues>(\r\n  baseSchema: ZodSchema<T>,\r\n  conditions: Array<{\r\n    condition: (data: T) => boolean;\r\n    schema: ZodSchema<T>;\r\n  }>\r\n) => {\r\n  const getDynamicSchema = useCallback(\r\n    (data: T): ZodSchema<T> => {\r\n      // Find the first matching condition\r\n      const matchingCondition = conditions.find(({ condition }) =>\r\n        condition(data)\r\n      );\r\n\r\n      // Return the conditional schema or base schema\r\n      return matchingCondition ? matchingCondition.schema : baseSchema;\r\n    },\r\n    [baseSchema, conditions]\r\n  );\r\n\r\n  const validateWithConditions = useCallback(\r\n    (data: T): { isValid: boolean; errors?: ZodError; validData?: T } => {\r\n      const dynamicSchema = getDynamicSchema(data);\r\n\r\n      try {\r\n        const validData = dynamicSchema.parse(data);\r\n        return { isValid: true, validData };\r\n      } catch (error) {\r\n        if (error instanceof ZodError) {\r\n          return { isValid: false, errors: error };\r\n        }\r\n        throw error;\r\n      }\r\n    },\r\n    [getDynamicSchema]\r\n  );\r\n\r\n  return {\r\n    getDynamicSchema,\r\n    validateWithConditions,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AACA;AAQA;AAAA;;;;;AA+DO,MAAM,oBAAoB,CAC/B;IAEA,MAAM,EACJ,MAAM,EACN,aAAa,EACb,OAAO,UAAU,EACjB,iBAAiB,UAAU,EAC3B,wBAAwB,KAAK,EAC7B,sBAAsB,CAAC,CAAC,EACzB,GAAG;IAEJ,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAK;QACtB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;QACf;QACA;QACA,cAAc;IAChB;IAEA,MAAM,EACJ,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,EACvC,OAAO,EACP,aAAa,cAAc,EAC3B,QAAQ,EACR,KAAK,EACN,GAAG;IAEJ,0BAA0B;IAC1B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,OAAO;QACL,MAAM,SAAS,MAAM,QAAQ;QAC7B,OAAO;IACT,GACA;QAAC;KAAQ;IAGX,uBAAuB;IACvB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,MAAM,SAAS,MAAM;QACrB,OAAO;IACT,GAAG;QAAC;KAAQ;IAEZ,mBAAmB;IACnB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B;IACF,GAAG;QAAC;KAAe;IAEnB,6BAA6B;IAC7B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC;QACC,eAAe;IACjB,GACA;QAAC;KAAe;IAGlB,wCAAwC;IACxC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACC,MAAM,aAAa,MAAM,CAAC,UAAU;QACpC,IAAI,CAAC,YAAY,OAAO;QAExB,uCAAuC;QACvC,MAAM,gBAAgB,mBAAmB,CAAC,UAAoB;QAC9D,IAAI,eAAe,OAAO;QAE1B,2CAA2C;QAC3C,IAAI,OAAO,WAAW,OAAO,KAAK,UAAU;YAC1C,OAAO,WAAW,OAAO;QAC3B;QAEA,wCAAwC;QACxC,OAAO;IACT,GACA;QAAC;QAAQ;KAAoB;IAG/B,oCAAoC;IACpC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACC,OAAO,CAAC,CAAC,MAAM,CAAC,UAAU;IAC5B,GACA;QAAC;KAAO;IAGV,+BAA+B;IAC/B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,MAAM;IACR,GAAG;QAAC;QAAO;KAAc;IAEzB,MAAM,SAAkC,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAC5C,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,OAAO;AACT;AAMO,MAAM,sBAAsB,CAAI;IACrC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7B,CAAC;QACC,IAAI;YACF,MAAM,YAAY,OAAO,KAAK,CAAC;YAC/B,OAAO;gBAAE,SAAS;gBAAM;YAAU;QACpC,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,oJAAA,CAAA,WAAQ,EAAE;gBAC7B,OAAO;oBAAE,SAAS;oBAAO,QAAQ;gBAAM;YACzC;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAO;IAGV,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,OACE;QAEA,IAAI;YACF,MAAM,YAAY,MAAM,OAAO,UAAU,CAAC;YAC1C,OAAO;gBAAE,SAAS;gBAAM;YAAU;QACpC,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,oJAAA,CAAA,WAAQ,EAAE;gBAC7B,OAAO;oBAAE,SAAS;oBAAO,QAAQ;gBAAM;YACzC;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAO;IAGV,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC/B,CAAC;QACC,MAAM,cAAsC,CAAC;QAE7C,SAAS,MAAM,CAAC,OAAO,CAAC,CAAA;YACtB,MAAM,YAAY,MAAM,IAAI,CAAC,IAAI,CAAC;YAClC,WAAW,CAAC,UAAU,GAAG,MAAM,OAAO;QACxC;QAEA,OAAO;IACT,GACA,EAAE;IAGJ,OAAO;QACL;QACA;QACA;IACF;AACF;AAMO,MAAM,2BAA2B,CACtC,YACA;IAKA,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjC,CAAC;QACC,oCAAoC;QACpC,MAAM,oBAAoB,WAAW,IAAI,CAAC,CAAC,EAAE,SAAS,EAAE,GACtD,UAAU;QAGZ,+CAA+C;QAC/C,OAAO,oBAAoB,kBAAkB,MAAM,GAAG;IACxD,GACA;QAAC;QAAY;KAAW;IAG1B,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACvC,CAAC;QACC,MAAM,gBAAgB,iBAAiB;QAEvC,IAAI;YACF,MAAM,YAAY,cAAc,KAAK,CAAC;YACtC,OAAO;gBAAE,SAAS;gBAAM;YAAU;QACpC,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,oJAAA,CAAA,WAAQ,EAAE;gBAC7B,OAAO;oBAAE,SAAS;oBAAO,QAAQ;gBAAM;YACzC;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAiB;IAGpB,OAAO;QACL;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 2846, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/forms/index.ts"], "sourcesContent": ["/**\r\n * @file Form Hooks Index - SOLID Principles Implementation\r\n * @description Centralized exports for all form-related hooks and services\r\n * @version 3.0.0\r\n * <AUTHOR> Development Team\r\n */\r\n\r\n// Main form submission hook (refactored for SOLID principles)\r\nexport { useFormSubmission } from './useFormSubmission';\r\n\r\n// Specialized validation hooks\r\nexport { useLoginValidation } from './useLoginValidation';\r\n\r\n// Type definitions\r\nexport type {\r\n  FormSubmissionOptions,\r\n  FormSubmissionResult,\r\n  SubmissionState,\r\n  RetryConfig,\r\n  AccessibilityConfig,\r\n  PerformanceConfig,\r\n  ToastConfig,\r\n  PerformanceMetrics,\r\n  AriaAttributes,\r\n} from './types/FormSubmissionTypes';\r\n\r\n// Services (following SRP)\r\nexport { FormSubmissionConfigService } from './services/FormSubmissionConfig';\r\nexport { FormSubmissionToastService } from './services/FormSubmissionToastService';\r\nexport { FormSubmissionAccessibilityService } from './services/FormSubmissionAccessibilityService';\r\nexport { FormSubmissionRetryService } from './services/FormSubmissionRetryService';\r\nexport { FormSubmissionPerformanceService } from './services/FormSubmissionPerformanceService';\r\n\r\n// Configuration constants\r\nexport {\r\n  DEFAULT_RETRY_CONFIG,\r\n  DEFAULT_ACCESSIBILITY_CONFIG,\r\n  DEFAULT_PERFORMANCE_CONFIG,\r\n  DEFAULT_TOAST_CONFIG,\r\n} from './services/FormSubmissionConfig';\r\n\r\n// Other existing form hooks\r\nexport { useFormToast } from './useFormToast';\r\nexport { useFormValidation } from './useFormValidation';\r\n\r\n// Re-export commonly used React Hook Form types for convenience\r\nexport type {\r\n  FieldValues,\r\n  FieldErrors,\r\n  UseFormReturn,\r\n  SubmitHandler,\r\n} from 'react-hook-form';\r\n\r\n// Re-export Zod types for convenience\r\nexport type { ZodSchema, ZodError } from 'zod';\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,8DAA8D;;AAC9D;AAEA,+BAA+B;AAC/B;AAeA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AAUA,4BAA4B;AAC5B;AACA", "debugId": null}}, {"offset": {"line": 2898, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/ui/index.ts"], "sourcesContent": ["/**\r\n * @file UI Hooks Index\r\n * @description Centralized exports for all UI-related hooks\r\n */\r\n\r\n// Theme management\r\nexport { useTheme } from './useTheme';\r\n\r\n// Sidebar management\r\nexport { useSidebar } from './useSidebar';\r\n\r\n// Modal management\r\nexport { useModal, type ModalContent } from './useModal';\r\n\r\n// UI preferences\r\nexport { useUiPreferences } from './useUiPreferences';\r\n\r\n// Notifications\r\nexport { useNotifications, useWorkHubNotifications } from './useNotifications';\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,mBAAmB;;AACnB;AAEA,qBAAqB;AACrB;AAEA,mBAAmB;AACnB;AAEA,iBAAiB;AACjB;AAEA,gBAAgB;AAChB", "debugId": null}}, {"offset": {"line": 2936, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/security.ts"], "sourcesContent": ["/**\r\n * @file Security Hooks Exports - DRY Principle\r\n * @module hooks/security\r\n *\r\n * Centralized exports for all security-related hooks following DRY principles.\r\n * This provides a single import point for security hooks across the application.\r\n */\r\n\r\nimport { usePermissions } from '../lib/api/security';\r\nimport { useCSRFProtection } from '../lib/api/security/hooks/useCSRFProtection';\r\nimport { useSecureApi } from '../lib/api/security/hooks/useSecureApi';\r\nimport { useSessionSecurity } from '../lib/api/security/hooks/useSessionSecurity';\r\n// Import hooks for re-export object\r\nimport { useTokenManagement } from '../lib/api/security/hooks/useTokenManagement';\r\n// CSRF Protection Hook (DRY)\r\nexport { useCSRFProtection } from '../lib/api/security/hooks/useCSRFProtection';\r\nexport type {\r\n  CSRFProtectionActions,\r\n  CSRFProtectionState,\r\n  UseCSRFProtectionReturn,\r\n} from '../lib/api/security/hooks/useCSRFProtection';\r\n\r\n// Secure API Hook (DRY + Separation of Concerns)\r\nexport { useSecureApi } from '../lib/api/security/hooks/useSecureApi';\r\nexport type {\r\n  ApiError,\r\n  ApiResponse,\r\n  RequestConfig,\r\n  UseSecureApiReturn,\r\n} from '../lib/api/security/hooks/useSecureApi';\r\n\r\n// Session Security Hook (SRP)\r\nexport { useSessionSecurity } from '../lib/api/security/hooks/useSessionSecurity';\r\nexport type {\r\n  SessionSecurityActions,\r\n  SessionSecurityState,\r\n  UseSessionSecurityReturn,\r\n} from '../lib/api/security/hooks/useSessionSecurity';\r\n\r\n// Token Management Hook (SRP)\r\nexport { useTokenManagement } from '../lib/api/security/hooks/useTokenManagement';\r\nexport type {\r\n  TokenManagementActions,\r\n  TokenManagementState,\r\n  UseTokenManagementReturn,\r\n} from '../lib/api/security/hooks/useTokenManagement';\r\n\r\n// Re-export for convenience (DRY)\r\nexport const SecurityHooks = {\r\n  useCSRFProtection,\r\n  usePermissions,\r\n  useSecureApi,\r\n  useSessionSecurity,\r\n  useTokenManagement,\r\n} as const;\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AAED;AAAA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;;;;;;;;;;AAmCO,MAAM,gBAAgB;IAC3B,mBAAA,2JAAA,CAAA,oBAAiB;IACjB,gBAAA,wJAAA,CAAA,iBAAc;IACd,cAAA,sJAAA,CAAA,eAAY;IACZ,oBAAA,4JAAA,CAAA,qBAAkB;IAClB,oBAAA,4JAAA,CAAA,qBAAkB;AACpB", "debugId": null}}, {"offset": {"line": 2987, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/domain/useDelegationInfo.ts"], "sourcesContent": ["/**\r\n * @file Custom hook for delegation info extraction\r\n * @module hooks/useDelegationInfo\r\n */\r\n\r\nimport { useMemo } from 'react';\r\nimport type {\r\n  Delegation,\r\n  DelegationEscort,\r\n  DelegationVehicleAssignment,\r\n} from '@/lib/types/domain';\r\n\r\n// Define types for the partial employee/vehicle data\r\ntype PartialEmployeeInfo = NonNullable<DelegationEscort['employee']>;\r\ntype PartialVehicleInfo = NonNullable<DelegationVehicleAssignment['vehicle']>;\r\n\r\ninterface DelegationInfo {\r\n  escortInfo: PartialEmployeeInfo | null;\r\n  driverInfo: PartialEmployeeInfo | null;\r\n  vehicleInfo: PartialVehicleInfo | null;\r\n  hasFlightDetails: boolean;\r\n  needsEscortAssignment: boolean;\r\n  isActive: boolean;\r\n}\r\n\r\n/**\r\n * Custom hook to extract and process delegation information\r\n * Follows SRP by handling only data extraction logic\r\n *\r\n * @param delegation - Delegation object\r\n * @returns Processed delegation information\r\n */\r\nexport const useDelegationInfo = (delegation: Delegation): DelegationInfo => {\r\n  return useMemo(() => {\r\n    // Extract escort information\r\n    const escortInfo =\r\n      delegation.escorts &&\r\n      delegation.escorts.length > 0 &&\r\n      delegation.escorts[0]?.employee\r\n        ? delegation.escorts[0].employee\r\n        : null;\r\n\r\n    // Extract driver information\r\n    const driverInfo =\r\n      delegation.drivers &&\r\n      delegation.drivers.length > 0 &&\r\n      delegation.drivers[0]?.employee\r\n        ? delegation.drivers[0].employee\r\n        : null;\r\n\r\n    // Extract vehicle information\r\n    const vehicleInfo =\r\n      delegation.vehicles &&\r\n      delegation.vehicles.length > 0 &&\r\n      delegation.vehicles[0]?.vehicle\r\n        ? delegation.vehicles[0].vehicle\r\n        : null;\r\n\r\n    // Check if flight details exist\r\n    const hasFlightDetails = Boolean(\r\n      delegation.arrivalFlight || delegation.departureFlight\r\n    );\r\n\r\n    // Check if escort assignment is needed\r\n    const needsEscortAssignment =\r\n      !escortInfo &&\r\n      delegation.status !== 'Completed' &&\r\n      delegation.status !== 'Cancelled';\r\n\r\n    // Check if delegation is currently active\r\n    const isActive = delegation.status === 'In_Progress';\r\n\r\n    return {\r\n      escortInfo,\r\n      driverInfo,\r\n      vehicleInfo,\r\n      hasFlightDetails,\r\n      needsEscortAssignment,\r\n      isActive,\r\n    };\r\n  }, [delegation]);\r\n};\r\n\r\nexport default useDelegationInfo;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AA2BO,MAAM,oBAAoB,CAAC;IAChC,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,6BAA6B;QAC7B,MAAM,aACJ,WAAW,OAAO,IAClB,WAAW,OAAO,CAAC,MAAM,GAAG,KAC5B,WAAW,OAAO,CAAC,EAAE,EAAE,WACnB,WAAW,OAAO,CAAC,EAAE,CAAC,QAAQ,GAC9B;QAEN,6BAA6B;QAC7B,MAAM,aACJ,WAAW,OAAO,IAClB,WAAW,OAAO,CAAC,MAAM,GAAG,KAC5B,WAAW,OAAO,CAAC,EAAE,EAAE,WACnB,WAAW,OAAO,CAAC,EAAE,CAAC,QAAQ,GAC9B;QAEN,8BAA8B;QAC9B,MAAM,cACJ,WAAW,QAAQ,IACnB,WAAW,QAAQ,CAAC,MAAM,GAAG,KAC7B,WAAW,QAAQ,CAAC,EAAE,EAAE,UACpB,WAAW,QAAQ,CAAC,EAAE,CAAC,OAAO,GAC9B;QAEN,gCAAgC;QAChC,MAAM,mBAAmB,QACvB,WAAW,aAAa,IAAI,WAAW,eAAe;QAGxD,uCAAuC;QACvC,MAAM,wBACJ,CAAC,cACD,WAAW,MAAM,KAAK,eACtB,WAAW,MAAM,KAAK;QAExB,0CAA0C;QAC1C,MAAM,WAAW,WAAW,MAAM,KAAK;QAEvC,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;KAAW;AACjB;uCAEe", "debugId": null}}, {"offset": {"line": 3029, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/domain/useDashboardStore.ts"], "sourcesContent": ["/**\r\n * @file Generic dashboard store hook\r\n * @module hooks/useDashboardStore\r\n */\r\n\r\nimport { useMemo } from 'react';\r\nimport { create } from 'zustand';\r\nimport { devtools, persist } from 'zustand/middleware';\r\n\r\nimport type {\r\n  DashboardConfig,\r\n  DashboardStore,\r\n  ViewMode,\r\n} from '@/components/dashboard/types';\r\n\r\n/**\r\n * Create a dashboard store for a specific entity type\r\n */\r\nexport function createDashboardStore(entityType: string) {\r\n  return create<DashboardStore>()(\r\n    devtools(\r\n      persist(\r\n        (set, get) => ({\r\n          // Initial state\r\n          activeTab: 'all',\r\n          layout: {\r\n            viewMode: 'cards' as ViewMode,\r\n            gridColumns: 3,\r\n            compactMode: false,\r\n            showFilters: true,\r\n            showSettings: false,\r\n          },\r\n          monitoring: {\r\n            enabled: false,\r\n            refreshInterval: 30000,\r\n            autoRefresh: true,\r\n            pausedDataTypes: new Set<string>(),\r\n          },\r\n          filters: {},\r\n          sortBy: 'createdAt',\r\n          sortDirection: 'desc',\r\n          selectedItems: new Set<string>(),\r\n          searchTerm: '',\r\n\r\n          // Actions\r\n          setActiveTab: (tab: string) =>\r\n            set({ activeTab: tab }, false, 'setActiveTab'),\r\n\r\n          setViewMode: (mode: ViewMode) =>\r\n            set(\r\n              state => ({\r\n                layout: { ...state.layout, viewMode: mode },\r\n              }),\r\n              false,\r\n              'setViewMode'\r\n            ),\r\n\r\n          setGridColumns: (columns: number) =>\r\n            set(\r\n              state => ({\r\n                layout: { ...state.layout, gridColumns: columns },\r\n              }),\r\n              false,\r\n              'setGridColumns'\r\n            ),\r\n\r\n          toggleCompactMode: () =>\r\n            set(\r\n              state => ({\r\n                layout: {\r\n                  ...state.layout,\r\n                  compactMode: !state.layout.compactMode,\r\n                },\r\n              }),\r\n              false,\r\n              'toggleCompactMode'\r\n            ),\r\n\r\n          toggleFilters: () =>\r\n            set(\r\n              state => ({\r\n                layout: {\r\n                  ...state.layout,\r\n                  showFilters: !state.layout.showFilters,\r\n                },\r\n              }),\r\n              false,\r\n              'toggleFilters'\r\n            ),\r\n\r\n          toggleSettings: () =>\r\n            set(\r\n              state => ({\r\n                layout: {\r\n                  ...state.layout,\r\n                  showSettings: !state.layout.showSettings,\r\n                },\r\n              }),\r\n              false,\r\n              'toggleSettings'\r\n            ),\r\n\r\n          updateFilter: (filterId: string, value: any) =>\r\n            set(\r\n              state => ({\r\n                filters: { ...state.filters, [filterId]: value },\r\n              }),\r\n              false,\r\n              'updateFilter'\r\n            ),\r\n\r\n          clearFilters: () => set({ filters: {} }, false, 'clearFilters'),\r\n\r\n          setSorting: (field: string, direction: 'asc' | 'desc') =>\r\n            set(\r\n              { sortBy: field, sortDirection: direction },\r\n              false,\r\n              'setSorting'\r\n            ),\r\n\r\n          setSearchTerm: (term: string) =>\r\n            set({ searchTerm: term }, false, 'setSearchTerm'),\r\n\r\n          toggleItemSelection: (id: string) =>\r\n            set(\r\n              state => {\r\n                const newSelection = new Set(state.selectedItems);\r\n                if (newSelection.has(id)) {\r\n                  newSelection.delete(id);\r\n                } else {\r\n                  newSelection.add(id);\r\n                }\r\n                return { selectedItems: newSelection };\r\n              },\r\n              false,\r\n              'toggleItemSelection'\r\n            ),\r\n\r\n          clearSelection: () =>\r\n            set({ selectedItems: new Set() }, false, 'clearSelection'),\r\n\r\n          selectAll: (ids: string[]) =>\r\n            set({ selectedItems: new Set(ids) }, false, 'selectAll'),\r\n\r\n          // Monitoring actions\r\n          setMonitoringEnabled: (enabled: boolean) =>\r\n            set(\r\n              state => ({\r\n                monitoring: { ...state.monitoring, enabled },\r\n              }),\r\n              false,\r\n              'setMonitoringEnabled'\r\n            ),\r\n\r\n          setRefreshInterval: (interval: number) =>\r\n            set(\r\n              state => ({\r\n                monitoring: { ...state.monitoring, refreshInterval: interval },\r\n              }),\r\n              false,\r\n              'setRefreshInterval'\r\n            ),\r\n\r\n          toggleAutoRefresh: () =>\r\n            set(\r\n              state => ({\r\n                monitoring: {\r\n                  ...state.monitoring,\r\n                  autoRefresh: !state.monitoring.autoRefresh,\r\n                },\r\n              }),\r\n              false,\r\n              'toggleAutoRefresh'\r\n            ),\r\n\r\n          pauseDataType: (dataType: string) =>\r\n            set(\r\n              state => ({\r\n                monitoring: {\r\n                  ...state.monitoring,\r\n                  pausedDataTypes: new Set([\r\n                    ...state.monitoring.pausedDataTypes,\r\n                    dataType,\r\n                  ]),\r\n                },\r\n              }),\r\n              false,\r\n              'pauseDataType'\r\n            ),\r\n\r\n          resumeDataType: (dataType: string) =>\r\n            set(\r\n              state => {\r\n                const newPausedTypes = new Set(\r\n                  state.monitoring.pausedDataTypes\r\n                );\r\n                newPausedTypes.delete(dataType);\r\n                return {\r\n                  monitoring: {\r\n                    ...state.monitoring,\r\n                    pausedDataTypes: newPausedTypes,\r\n                  },\r\n                };\r\n              },\r\n              false,\r\n              'resumeDataType'\r\n            ),\r\n\r\n          resetSettings: () =>\r\n            set(\r\n              {\r\n                layout: {\r\n                  viewMode: 'cards' as ViewMode,\r\n                  gridColumns: 3,\r\n                  compactMode: false,\r\n                  showFilters: true,\r\n                  showSettings: false,\r\n                },\r\n                monitoring: {\r\n                  enabled: false,\r\n                  refreshInterval: 30000,\r\n                  autoRefresh: true,\r\n                  pausedDataTypes: new Set<string>(),\r\n                },\r\n                filters: {},\r\n                sortBy: 'createdAt',\r\n                sortDirection: 'desc',\r\n                selectedItems: new Set<string>(),\r\n                searchTerm: '',\r\n              },\r\n              false,\r\n              'resetSettings'\r\n            ),\r\n\r\n          // Computed selectors\r\n          getFilteredData: <T extends { id: string }>(\r\n            data: T[],\r\n            config: DashboardConfig<T>\r\n          ): T[] => {\r\n            const state = get();\r\n            let filtered = [...data];\r\n\r\n            // Apply search filter\r\n            if (state.searchTerm) {\r\n              const searchLower = state.searchTerm.toLowerCase();\r\n              filtered = filtered.filter((item: any) =>\r\n                Object.values(item).some(value =>\r\n                  String(value).toLowerCase().includes(searchLower)\r\n                )\r\n              );\r\n            }\r\n\r\n            // Apply other filters\r\n            Object.entries(state.filters).forEach(([filterId, value]) => {\r\n              if (value !== undefined && value !== null && value !== '') {\r\n                filtered = filtered.filter((item: any) => {\r\n                  const filterConfig = config.filters?.find(\r\n                    f => f.id === filterId\r\n                  );\r\n                  if (!filterConfig) return true;\r\n\r\n                  switch (filterConfig.type) {\r\n                    case 'select':\r\n                      return item[filterId] === value;\r\n                    case 'multiselect':\r\n                      return Array.isArray(value)\r\n                        ? value.includes(item[filterId])\r\n                        : true;\r\n                    case 'toggle':\r\n                      return value ? item[filterId] : true;\r\n                    default:\r\n                      return true;\r\n                  }\r\n                });\r\n              }\r\n            });\r\n\r\n            // Apply sorting\r\n            filtered.sort((a: any, b: any) => {\r\n              const aValue = a[state.sortBy];\r\n              const bValue = b[state.sortBy];\r\n              const direction = state.sortDirection === 'asc' ? 1 : -1;\r\n\r\n              if (aValue < bValue) return -1 * direction;\r\n              if (aValue > bValue) return 1 * direction;\r\n              return 0;\r\n            });\r\n\r\n            return filtered;\r\n          },\r\n\r\n          getSelectedCount: () => get().selectedItems.size,\r\n\r\n          hasActiveFilters: () => {\r\n            const state = get();\r\n            return (\r\n              state.searchTerm.length > 0 ||\r\n              Object.values(state.filters).some(\r\n                value => value !== undefined && value !== null && value !== ''\r\n              )\r\n            );\r\n          },\r\n        }),\r\n        {\r\n          name: `workhub-dashboard-${entityType}`,\r\n          partialize: state => ({\r\n            layout: state.layout,\r\n            monitoring: state.monitoring,\r\n            filters: state.filters,\r\n            sortBy: state.sortBy,\r\n            sortDirection: state.sortDirection,\r\n          }),\r\n        }\r\n      ),\r\n      {\r\n        name: `dashboard-${entityType}`,\r\n      }\r\n    )\r\n  );\r\n}\r\n\r\n/**\r\n * Store registry to ensure single instance per entity type\r\n */\r\nconst storeRegistry = new Map<\r\n  string,\r\n  ReturnType<typeof createDashboardStore>\r\n>();\r\n\r\n/**\r\n * Hook to use dashboard store for a specific entity type\r\n */\r\nexport function useDashboardStore(entityType: string) {\r\n  return useMemo(() => {\r\n    if (!storeRegistry.has(entityType)) {\r\n      storeRegistry.set(entityType, createDashboardStore(entityType));\r\n    }\r\n    return storeRegistry.get(entityType)!;\r\n  }, [entityType]);\r\n}\r\n\r\nexport default useDashboardStore;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AACA;;;;AAWO,SAAS,qBAAqB,UAAkB;IACrD,OAAO,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACV,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;YACb,gBAAgB;YAChB,WAAW;YACX,QAAQ;gBACN,UAAU;gBACV,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,cAAc;YAChB;YACA,YAAY;gBACV,SAAS;gBACT,iBAAiB;gBACjB,aAAa;gBACb,iBAAiB,IAAI;YACvB;YACA,SAAS,CAAC;YACV,QAAQ;YACR,eAAe;YACf,eAAe,IAAI;YACnB,YAAY;YAEZ,UAAU;YACV,cAAc,CAAC,MACb,IAAI;oBAAE,WAAW;gBAAI,GAAG,OAAO;YAEjC,aAAa,CAAC,OACZ,IACE,CAAA,QAAS,CAAC;wBACR,QAAQ;4BAAE,GAAG,MAAM,MAAM;4BAAE,UAAU;wBAAK;oBAC5C,CAAC,GACD,OACA;YAGJ,gBAAgB,CAAC,UACf,IACE,CAAA,QAAS,CAAC;wBACR,QAAQ;4BAAE,GAAG,MAAM,MAAM;4BAAE,aAAa;wBAAQ;oBAClD,CAAC,GACD,OACA;YAGJ,mBAAmB,IACjB,IACE,CAAA,QAAS,CAAC;wBACR,QAAQ;4BACN,GAAG,MAAM,MAAM;4BACf,aAAa,CAAC,MAAM,MAAM,CAAC,WAAW;wBACxC;oBACF,CAAC,GACD,OACA;YAGJ,eAAe,IACb,IACE,CAAA,QAAS,CAAC;wBACR,QAAQ;4BACN,GAAG,MAAM,MAAM;4BACf,aAAa,CAAC,MAAM,MAAM,CAAC,WAAW;wBACxC;oBACF,CAAC,GACD,OACA;YAGJ,gBAAgB,IACd,IACE,CAAA,QAAS,CAAC;wBACR,QAAQ;4BACN,GAAG,MAAM,MAAM;4BACf,cAAc,CAAC,MAAM,MAAM,CAAC,YAAY;wBAC1C;oBACF,CAAC,GACD,OACA;YAGJ,cAAc,CAAC,UAAkB,QAC/B,IACE,CAAA,QAAS,CAAC;wBACR,SAAS;4BAAE,GAAG,MAAM,OAAO;4BAAE,CAAC,SAAS,EAAE;wBAAM;oBACjD,CAAC,GACD,OACA;YAGJ,cAAc,IAAM,IAAI;oBAAE,SAAS,CAAC;gBAAE,GAAG,OAAO;YAEhD,YAAY,CAAC,OAAe,YAC1B,IACE;oBAAE,QAAQ;oBAAO,eAAe;gBAAU,GAC1C,OACA;YAGJ,eAAe,CAAC,OACd,IAAI;oBAAE,YAAY;gBAAK,GAAG,OAAO;YAEnC,qBAAqB,CAAC,KACpB,IACE,CAAA;oBACE,MAAM,eAAe,IAAI,IAAI,MAAM,aAAa;oBAChD,IAAI,aAAa,GAAG,CAAC,KAAK;wBACxB,aAAa,MAAM,CAAC;oBACtB,OAAO;wBACL,aAAa,GAAG,CAAC;oBACnB;oBACA,OAAO;wBAAE,eAAe;oBAAa;gBACvC,GACA,OACA;YAGJ,gBAAgB,IACd,IAAI;oBAAE,eAAe,IAAI;gBAAM,GAAG,OAAO;YAE3C,WAAW,CAAC,MACV,IAAI;oBAAE,eAAe,IAAI,IAAI;gBAAK,GAAG,OAAO;YAE9C,qBAAqB;YACrB,sBAAsB,CAAC,UACrB,IACE,CAAA,QAAS,CAAC;wBACR,YAAY;4BAAE,GAAG,MAAM,UAAU;4BAAE;wBAAQ;oBAC7C,CAAC,GACD,OACA;YAGJ,oBAAoB,CAAC,WACnB,IACE,CAAA,QAAS,CAAC;wBACR,YAAY;4BAAE,GAAG,MAAM,UAAU;4BAAE,iBAAiB;wBAAS;oBAC/D,CAAC,GACD,OACA;YAGJ,mBAAmB,IACjB,IACE,CAAA,QAAS,CAAC;wBACR,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB,aAAa,CAAC,MAAM,UAAU,CAAC,WAAW;wBAC5C;oBACF,CAAC,GACD,OACA;YAGJ,eAAe,CAAC,WACd,IACE,CAAA,QAAS,CAAC;wBACR,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB,iBAAiB,IAAI,IAAI;mCACpB,MAAM,UAAU,CAAC,eAAe;gCACnC;6BACD;wBACH;oBACF,CAAC,GACD,OACA;YAGJ,gBAAgB,CAAC,WACf,IACE,CAAA;oBACE,MAAM,iBAAiB,IAAI,IACzB,MAAM,UAAU,CAAC,eAAe;oBAElC,eAAe,MAAM,CAAC;oBACtB,OAAO;wBACL,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB,iBAAiB;wBACnB;oBACF;gBACF,GACA,OACA;YAGJ,eAAe,IACb,IACE;oBACE,QAAQ;wBACN,UAAU;wBACV,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb,cAAc;oBAChB;oBACA,YAAY;wBACV,SAAS;wBACT,iBAAiB;wBACjB,aAAa;wBACb,iBAAiB,IAAI;oBACvB;oBACA,SAAS,CAAC;oBACV,QAAQ;oBACR,eAAe;oBACf,eAAe,IAAI;oBACnB,YAAY;gBACd,GACA,OACA;YAGJ,qBAAqB;YACrB,iBAAiB,CACf,MACA;gBAEA,MAAM,QAAQ;gBACd,IAAI,WAAW;uBAAI;iBAAK;gBAExB,sBAAsB;gBACtB,IAAI,MAAM,UAAU,EAAE;oBACpB,MAAM,cAAc,MAAM,UAAU,CAAC,WAAW;oBAChD,WAAW,SAAS,MAAM,CAAC,CAAC,OAC1B,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,CAAA,QACvB,OAAO,OAAO,WAAW,GAAG,QAAQ,CAAC;gBAG3C;gBAEA,sBAAsB;gBACtB,OAAO,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,UAAU,MAAM;oBACtD,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;wBACzD,WAAW,SAAS,MAAM,CAAC,CAAC;4BAC1B,MAAM,eAAe,OAAO,OAAO,EAAE,KACnC,CAAA,IAAK,EAAE,EAAE,KAAK;4BAEhB,IAAI,CAAC,cAAc,OAAO;4BAE1B,OAAQ,aAAa,IAAI;gCACvB,KAAK;oCACH,OAAO,IAAI,CAAC,SAAS,KAAK;gCAC5B,KAAK;oCACH,OAAO,MAAM,OAAO,CAAC,SACjB,MAAM,QAAQ,CAAC,IAAI,CAAC,SAAS,IAC7B;gCACN,KAAK;oCACH,OAAO,QAAQ,IAAI,CAAC,SAAS,GAAG;gCAClC;oCACE,OAAO;4BACX;wBACF;oBACF;gBACF;gBAEA,gBAAgB;gBAChB,SAAS,IAAI,CAAC,CAAC,GAAQ;oBACrB,MAAM,SAAS,CAAC,CAAC,MAAM,MAAM,CAAC;oBAC9B,MAAM,SAAS,CAAC,CAAC,MAAM,MAAM,CAAC;oBAC9B,MAAM,YAAY,MAAM,aAAa,KAAK,QAAQ,IAAI,CAAC;oBAEvD,IAAI,SAAS,QAAQ,OAAO,CAAC,IAAI;oBACjC,IAAI,SAAS,QAAQ,OAAO,IAAI;oBAChC,OAAO;gBACT;gBAEA,OAAO;YACT;YAEA,kBAAkB,IAAM,MAAM,aAAa,CAAC,IAAI;YAEhD,kBAAkB;gBAChB,MAAM,QAAQ;gBACd,OACE,MAAM,UAAU,CAAC,MAAM,GAAG,KAC1B,OAAO,MAAM,CAAC,MAAM,OAAO,EAAE,IAAI,CAC/B,CAAA,QAAS,UAAU,aAAa,UAAU,QAAQ,UAAU;YAGlE;QACF,CAAC,GACD;QACE,MAAM,CAAC,kBAAkB,EAAE,YAAY;QACvC,YAAY,CAAA,QAAS,CAAC;gBACpB,QAAQ,MAAM,MAAM;gBACpB,YAAY,MAAM,UAAU;gBAC5B,SAAS,MAAM,OAAO;gBACtB,QAAQ,MAAM,MAAM;gBACpB,eAAe,MAAM,aAAa;YACpC,CAAC;IACH,IAEF;QACE,MAAM,CAAC,UAAU,EAAE,YAAY;IACjC;AAGN;AAEA;;CAEC,GACD,MAAM,gBAAgB,IAAI;AAQnB,SAAS,kBAAkB,UAAkB;IAClD,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,IAAI,CAAC,cAAc,GAAG,CAAC,aAAa;YAClC,cAAc,GAAG,CAAC,YAAY,qBAAqB;QACrD;QACA,OAAO,cAAc,GAAG,CAAC;IAC3B,GAAG;QAAC;KAAW;AACjB;uCAEe", "debugId": null}}, {"offset": {"line": 3269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/domain/index.ts"], "sourcesContent": ["/**\r\n * @file Domain Hooks Index\r\n * @description Centralized exports for all domain-specific hooks\r\n */\r\n\r\n// Delegation domain hooks\r\nexport { useDelegationInfo } from './useDelegationInfo';\r\n\r\n// Dashboard domain hooks (moved from lib/hooks)\r\nexport { useDashboardStore } from './useDashboardStore';\r\n\r\n// Future domain hooks can be added here:\r\n// export { useTaskInfo } from './useTaskInfo';\r\n// export { useVehicleInfo } from './useVehicleInfo';\r\n// export { useEmployeeInfo } from './useEmployeeInfo';\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,0BAA0B;;AAC1B;AAEA,gDAAgD;AAChD,qPAEA,yCAAyC;CACzC,+CAA+C;CAC/C,qDAAqD;CACrD,uDAAuD", "debugId": null}}, {"offset": {"line": 3298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/utils/use-mobile.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nconst MOBILE_BREAKPOINT = 768;\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(\r\n    \r\n  );\r\n\r\n  React.useEffect(() => {\r\n    const mql = globalThis.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n    };\r\n    mql.addEventListener('change', onChange);\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n    return () => mql.removeEventListener('change', onChange);\r\n  }, []);\r\n\r\n  return !!isMobile;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD;IAI7C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,WAAW,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QAC3E,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 3323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/utils/useRequestDeduplication.ts"], "sourcesContent": ["import { useCallback, useRef } from 'react';\r\n\r\n/**\r\n * Global request deduplication instance\r\n * Use this for API calls that should be deduplicated across the entire app\r\n */\r\nclass GlobalRequestDeduplicator {\r\n  private readonly pendingRequests = new Map<string, Promise<any>>();\r\n\r\n  clear(): void {\r\n    this.pendingRequests.clear();\r\n  }\r\n\r\n  async deduplicate<T>(key: string, requestFn: () => Promise<T>): Promise<T> {\r\n    // If there's already a pending request for this key, return it\r\n    if (this.pendingRequests.has(key)) {\r\n      console.log(`🔄 Global Request DEDUPLICATED for ${key}`);\r\n      return this.pendingRequests.get(key)!;\r\n    }\r\n\r\n    // Create new request and store it\r\n    const request = requestFn().finally(() => {\r\n      // Remove from pending requests when completed\r\n      this.pendingRequests.delete(key);\r\n    });\r\n\r\n    this.pendingRequests.set(key, request);\r\n    console.log(`🔄 Global Request STARTED for ${key}`);\r\n\r\n    return request;\r\n  }\r\n\r\n  getPendingCount(): number {\r\n    return this.pendingRequests.size;\r\n  }\r\n\r\n  getPendingKeys(): string[] {\r\n    return [...this.pendingRequests.keys()];\r\n  }\r\n}\r\n\r\n/**\r\n * Hook to prevent duplicate API requests when multiple components\r\n * try to fetch the same data simultaneously\r\n */\r\nexport function useRequestDeduplication() {\r\n  const pendingRequests = useRef(new Map<string, Promise<any>>());\r\n\r\n  const deduplicateRequest = useCallback(\r\n    <T>(key: string, requestFn: () => Promise<T>): Promise<T> => {\r\n      // If there's already a pending request for this key, return it\r\n      if (pendingRequests.current.has(key)) {\r\n        console.log(`🔄 Request DEDUPLICATED for ${key}`);\r\n        return pendingRequests.current.get(key)!;\r\n      }\r\n\r\n      // Create new request and store it\r\n      const request = requestFn().finally(() => {\r\n        // Remove from pending requests when completed\r\n        pendingRequests.current.delete(key);\r\n      });\r\n\r\n      pendingRequests.current.set(key, request);\r\n      console.log(`🔄 Request STARTED for ${key}`);\r\n\r\n      return request;\r\n    },\r\n    []\r\n  );\r\n\r\n  const clearPendingRequests = useCallback(() => {\r\n    pendingRequests.current.clear();\r\n  }, []);\r\n\r\n  const getPendingRequestsCount = useCallback(() => {\r\n    return pendingRequests.current.size;\r\n  }, []);\r\n\r\n  return {\r\n    clearPendingRequests,\r\n    deduplicateRequest,\r\n    getPendingRequestsCount,\r\n  };\r\n}\r\n\r\nexport const globalRequestDeduplicator = new GlobalRequestDeduplicator();\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA;;;CAGC,GACD,MAAM;IACa,kBAAkB,IAAI,MAA4B;IAEnE,QAAc;QACZ,IAAI,CAAC,eAAe,CAAC,KAAK;IAC5B;IAEA,MAAM,YAAe,GAAW,EAAE,SAA2B,EAAc;QACzE,+DAA+D;QAC/D,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM;YACjC,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,KAAK;YACvD,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QAClC;QAEA,kCAAkC;QAClC,MAAM,UAAU,YAAY,OAAO,CAAC;YAClC,8CAA8C;YAC9C,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QAC9B;QAEA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK;QAC9B,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,KAAK;QAElD,OAAO;IACT;IAEA,kBAA0B;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI;IAClC;IAEA,iBAA2B;QACzB,OAAO;eAAI,IAAI,CAAC,eAAe,CAAC,IAAI;SAAG;IACzC;AACF;AAMO,SAAS;IACd,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,IAAI;IAEnC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,CAAI,KAAa;QACf,+DAA+D;QAC/D,IAAI,gBAAgB,OAAO,CAAC,GAAG,CAAC,MAAM;YACpC,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK;YAChD,OAAO,gBAAgB,OAAO,CAAC,GAAG,CAAC;QACrC;QAEA,kCAAkC;QAClC,MAAM,UAAU,YAAY,OAAO,CAAC;YAClC,8CAA8C;YAC9C,gBAAgB,OAAO,CAAC,MAAM,CAAC;QACjC;QAEA,gBAAgB,OAAO,CAAC,GAAG,CAAC,KAAK;QACjC,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,KAAK;QAE3C,OAAO;IACT,GACA,EAAE;IAGJ,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,gBAAgB,OAAO,CAAC,KAAK;IAC/B,GAAG,EAAE;IAEL,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1C,OAAO,gBAAgB,OAAO,CAAC,IAAI;IACrC,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;IACF;AACF;AAEO,MAAM,4BAA4B,IAAI", "debugId": null}}, {"offset": {"line": 3397, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/utils/index.ts"], "sourcesContent": ["/**\r\n * @file Utility Hooks Index\r\n * @description Centralized exports for all utility hooks\r\n */\r\n\r\n// Mobile detection\r\nexport { useIsMobile as useMobile } from './use-mobile';\r\n\r\n// Toast notifications\r\nexport { useToast } from './use-toast';\r\n\r\n// Request deduplication\r\nexport {\r\n  useRequestDeduplication,\r\n  globalRequestDeduplicator,\r\n} from './useRequestDeduplication';\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,mBAAmB;;AACnB;AAEA,sBAAsB;AACtB;AAEA,wBAAwB;AACxB", "debugId": null}}, {"offset": {"line": 3427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/index.ts"], "sourcesContent": ["/**\r\n * @file Hooks Index\r\n * @description Centralized exports for all custom hooks following Phase 4 standardization\r\n */\r\n\r\n// ===== PHASE 4 STANDARDIZED HOOKS =====\r\n\r\n// API Hooks - Standardized patterns for API interactions\r\nexport * from './api';\r\n\r\n// Form Hooks - Standardized patterns for form handling\r\nexport * from './forms';\r\n\r\n// UI Hooks - Standardized patterns for UI state management\r\nexport * from './ui';\r\n\r\n// ===== CATEGORIZED HOOKS =====\r\n\r\n// Authentication Hooks - Standardized patterns for authentication\r\n// Auth hooks removed - use AuthContext directly\r\n// export * from './auth';\r\n\r\n// Security Hooks - Standardized patterns for security\r\nexport * from './security';\r\n\r\n// Domain Hooks - Domain-specific business logic\r\nexport * from './domain';\r\n\r\n// Utility Hooks - General utility functions\r\nexport * from './utils';\r\n\r\n// ===== LEGACY HOOKS (Maintained for backward compatibility) =====\r\n\r\n// Store hooks (re-exported for convenience)\r\nexport { useAppStore } from '../lib/stores/zustand/appStore';\r\nexport { useReliabilityStore } from '../lib/stores/zustand/reliabilityStore';\r\nexport { useUiStore } from '../lib/stores/zustand/uiStore';\r\n\r\n// Legacy API hooks (will be deprecated in favor of standardized API hooks)\r\n// Note: useApi.ts has been removed as it duplicated functionality with new standardized hooks\r\n\r\n// ===== CONVENIENCE HOOKS =====\r\n\r\n/**\r\n * Convenience hook that combines commonly used UI hooks\r\n * @example\r\n * ```typescript\r\n * const { theme, notifications, sidebar, modal } = useWorkHubCore();\r\n * ```\r\n */\r\nexport const useWorkHubCore = () => {\r\n  // Import from new standardized locations\r\n  const { useTheme } = require('./ui/useTheme');\r\n  const { useNotifications } = require('./ui/useNotifications');\r\n  const { useUiPreferences } = require('./ui/useUiPreferences');\r\n  const { useSidebar } = require('./ui/useSidebar');\r\n  const { useModal } = require('./ui/useModal');\r\n\r\n  const theme = useTheme();\r\n  const notifications = useNotifications();\r\n  const uiPreferences = useUiPreferences();\r\n  const sidebar = useSidebar();\r\n  const modal = useModal();\r\n\r\n  return {\r\n    modal,\r\n    notifications,\r\n    sidebar,\r\n    theme,\r\n    uiPreferences,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,yCAAyC;AAEzC,yDAAyD;;;;AACzD;AAEA,uDAAuD;AACvD;AAEA,2DAA2D;AAC3D;AAEA,gCAAgC;AAEhC,kEAAkE;AAClE,gDAAgD;AAChD,0BAA0B;AAE1B,sDAAsD;AACtD;AAEA,gDAAgD;AAChD;AAEA,4CAA4C;AAC5C;AAEA,mEAAmE;AAEnE,4CAA4C;AAC5C;AACA;AACA;;;;;;;;;;AAcO,MAAM,iBAAiB;IAC5B,yCAAyC;IACzC,MAAM,EAAE,QAAQ,EAAE;IAClB,MAAM,EAAE,gBAAgB,EAAE;IAC1B,MAAM,EAAE,gBAAgB,EAAE;IAC1B,MAAM,EAAE,UAAU,EAAE;IACpB,MAAM,EAAE,QAAQ,EAAE;IAElB,MAAM,QAAQ;IACd,MAAM,gBAAgB;IACtB,MAAM,gBAAgB;IACtB,MAAM,UAAU;IAChB,MAAM,QAAQ;IAEd,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}]}