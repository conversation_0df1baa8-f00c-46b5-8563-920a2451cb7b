"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4036],{3695:(e,t,r)=>{r.d(t,{hD:()=>s,v3:()=>i,v7:()=>a,PO:()=>n,Dr:()=>l,m_:()=>c,_7:()=>u});var o=function(e){return e.AUTHENTICATION_ERROR="authentication_error",e.AUTHORIZATION_ERROR="authorization_error",e.CLIENT_ERROR="client_error",e.NETWORK_ERROR="network_error",e.NOT_FOUND="not_found",e.PARSING_ERROR="parsing_error",e.RATE_LIMIT="rate_limit",e.SERVER_ERROR="server_error",e.TIMEOUT="timeout",e.UNKNOWN="unknown",e.VALIDATION_ERROR="validation_error",e}({});class s extends Error{static create(e,t,r){return new s(e,{details:null==r?void 0:r.details,...(null==r?void 0:r.errorType)&&{errorType:r.errorType},receivedData:null==r?void 0:r.receivedData,status:t,...(null==r?void 0:r.validationErrors)&&{validationErrors:r.validationErrors}})}getFormattedMessage(){if(this.isValidationError()){let e=this.validationErrors.map(e=>"".concat(e.path,": ").concat(e.message)).join("; ");return"Validation failed: ".concat(e)}switch(this.errorType){case"authentication_error":return"Authentication required. Please log in and try again.";case"authorization_error":return"You do not have permission to perform this action.";case"network_error":return"Network error: Unable to connect to the server. Please check your internet connection.";case"not_found":return"Resource not found: ".concat(this.endpoint||"The requested resource"," could not be found.");case"parsing_error":return"Could not parse the server response. Please try again or contact support.";case"rate_limit":return"Too many requests. Please try again later.";case"server_error":return"Server error (".concat(this.status,"): ").concat(this.message,". Please try again later.");case"timeout":return"Request timed out. The server is taking too long to respond.";default:return this.message}}getTechnicalDetails(){let e=["Status: ".concat(this.status),"Type: ".concat(this.errorType),"Message: ".concat(this.message)];return this.details&&e.push("Details: ".concat(JSON.stringify(this.details))),this.validationErrors&&e.push("Validation Errors: ".concat(JSON.stringify(this.validationErrors))),e.join("\n")}isValidationError(){return 400===this.status&&Array.isArray(this.validationErrors)&&this.validationErrors.length>0}determineErrorType(){return this.status>=500?"server_error":429===this.status?"rate_limit":401===this.status?"authentication_error":403===this.status?"authorization_error":400===this.status&&this.isValidationError()?"validation_error":this.status>=400&&this.status<500?"client_error":"unknown"}isRetryable(){return"server_error"===this.errorType||"network_error"===this.errorType||"timeout"===this.errorType||"rate_limit"===this.errorType}constructor(e,t){super(e),this.name="ApiError",this.status=t.status,t.code&&(this.code=t.code),t.endpoint&&(this.endpoint=t.endpoint),t.validationErrors&&(this.validationErrors=t.validationErrors),this.receivedData=t.receivedData,this.details=t.details,this.errorType=t.errorType||this.determineErrorType(),this.retryable=this.isRetryable(),Object.setPrototypeOf(this,s.prototype)}}class i extends s{constructor(e="Authentication Failed",t){super(e,{details:t,errorType:o.AUTHENTICATION_ERROR,status:401}),this.name="AuthenticationError",Object.setPrototypeOf(this,i.prototype)}}class a extends s{constructor(e="Bad Request",t){super(e,{details:t,errorType:o.CLIENT_ERROR,status:400}),this.name="BadRequestError",Object.setPrototypeOf(this,a.prototype)}}class n extends s{constructor(e="Internal Server Error",t){super(e,{details:t,errorType:o.SERVER_ERROR,status:500}),this.name="InternalServerError",Object.setPrototypeOf(this,n.prototype)}}class l extends s{constructor(e="Network Error",t){super(e,{details:t,errorType:o.NETWORK_ERROR,status:0}),this.name="NetworkError",Object.setPrototypeOf(this,l.prototype)}}class c extends s{constructor(e="Resource Not Found",t){super(e,{details:t,errorType:o.NOT_FOUND,status:404}),this.name="NotFoundError",Object.setPrototypeOf(this,c.prototype)}}class u extends s{constructor(e,t,r,s){super(e,{details:s,errorType:o.CLIENT_ERROR,status:r||500}),this.code=t,this.statusCode=r,this.context=s,this.name="ServiceError",Object.setPrototypeOf(this,u.prototype)}}},10694:(e,t,r)=>{r.d(t,{vz:()=>o});var o=function(e){return e.CLOSED="CLOSED",e.HALF_OPEN="HALF_OPEN",e.OPEN="OPEN",e}(o||{});class s{async execute(e){if("OPEN"===this.state)if(this.shouldAttemptReset())this.state="HALF_OPEN",this.successCount=0,console.log("\uD83D\uDD04 Circuit Breaker: Attempting to reset (HALF_OPEN)");else{let e=Math.round((this.lastFailureTime+this.options.recoveryTimeout-Date.now())/1e3);throw Error("Circuit breaker is OPEN. Retry in ".concat(e," seconds."))}try{let t=await this.executeWithTimeout(e);return this.onSuccess(),t}catch(e){throw this.onFailure(e),e}}getStatus(){let e={failureCount:this.failureCount,lastFailureTime:this.lastFailureTime,state:this.state};if("OPEN"===this.state){let t=Math.max(0,this.lastFailureTime+this.options.recoveryTimeout-Date.now());return{...e,timeUntilRetry:t}}return e}reset(){this.state="CLOSED",this.failureCount=0,this.lastFailureTime=0,this.successCount=0,console.log("\uD83D\uDD04 Circuit Breaker: Manually reset (CLOSED)")}async executeWithTimeout(e){return new Promise((t,r)=>{let o=setTimeout(()=>{r(Error("Request timeout"))},this.options.requestTimeout);e().then(t).catch(r).finally(()=>clearTimeout(o))})}onFailure(e){this.options.isFailure(e)&&(this.failureCount++,this.lastFailureTime=Date.now(),"HALF_OPEN"===this.state?(this.state="OPEN",console.log("\uD83D\uDD04 Circuit Breaker: Reset failed, opening circuit (OPEN)")):this.failureCount>=this.options.failureThreshold&&(this.state="OPEN",console.log("\uD83D\uDD04 Circuit Breaker: Failure threshold reached (".concat(this.failureCount,"), opening circuit (OPEN)"))))}onSuccess(){this.failureCount=0,"HALF_OPEN"===this.state&&(this.successCount++,this.successCount>=3&&(this.state="CLOSED",console.log("\uD83D\uDD04 Circuit Breaker: Reset successful (CLOSED)")))}shouldAttemptReset(){return Date.now()-this.lastFailureTime>=this.options.recoveryTimeout}constructor(e={}){this.failureCount=0,this.lastFailureTime=0,this.state="CLOSED",this.successCount=0,this.options={failureThreshold:5,isFailure:e=>(null==e?void 0:e.status)===429||(null==e?void 0:e.status)>=500&&(null==e?void 0:e.status)<600,recoveryTimeout:6e4,requestTimeout:1e4,...e}}}new s({failureThreshold:3,recoveryTimeout:6e4,requestTimeout:8e3}),new s({failureThreshold:5,recoveryTimeout:3e4,requestTimeout:8e3}),new s({failureThreshold:3,recoveryTimeout:3e4,requestTimeout:5e3}),new s({failureThreshold:3,recoveryTimeout:6e4,requestTimeout:1e4}),new s({failureThreshold:3,recoveryTimeout:6e4,requestTimeout:1e4})},16146:(e,t,r)=>{r.d(t,{og:()=>a,tL:()=>n});var o=r(60408),s=r.n(o);let i=(e,t)=>{let r=URL.createObjectURL(e),o=document.createElement("a");o.href=r,o.download=t,document.body.append(o),o.click(),o.remove(),URL.revokeObjectURL(r)},a=async(e,t,r)=>{r.toLowerCase().endsWith(".csv")||(r+=".csv");let o=[t,...e];i(new Blob([s().unparse(o)],{type:"text/csv;charset=utf-8;"}),r)},n=e=>{var t,r;let o=document.querySelector(e);if(!o)throw Error("Table element not found: ".concat(e));let s=o.querySelector("thead tr");if(!s)throw Error("Table header row not found");let i=[];for(let e of s.querySelectorAll("th"))Object.hasOwn(e.dataset,"skipExport")||i.push((null==(t=e.textContent)?void 0:t.trim())||"");let a=[];for(let e of o.querySelectorAll("tbody tr")){let t=[],o=0;for(let i of e.querySelectorAll("td")){let e=s.querySelectorAll("th")[o];if(o++,e&&!Object.hasOwn(e.dataset,"skipExport")){let e=i.dataset.exportValue||(null==(r=i.textContent)?void 0:r.trim())||"";t.push(e)}}a.push(t)}return{data:a,headers:i}}},21876:(e,t,r)=>{r.d(t,{B7:()=>h,Qr:()=>d,g3:()=>f,rm:()=>u});var o=r(83343),s=r(44861),i=r(389),a=r(41784),n=r(25399);let l={fallbackToBothFormats:!0,locale:"en-GB",primaryFormat:"EU"},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l;if(!e||""===e.trim())return null;let r=e.trim();try{if(r.includes("T")||r.includes("Z")||/^\d{4}-\d{2}-\d{2}/.test(r)){let e=(0,o.H)(r);if((0,s.f)(e))return e}if(/^\d{4}-\d{2}-\d{2}$/.test(r)){let e=new Date(r+"T00:00:00");if((0,s.f)(e))return e}let e=r.match(/^(\d{1,2})\/(\d{1,2})\/(\d{2,4})$/);if(e){let[,o,a]=e;for(let e of"EU"===t.primaryFormat?["dd/MM/yyyy","MM/dd/yyyy"]:["MM/dd/yyyy","dd/MM/yyyy"])try{let t=(0,i.qg)(r,e,new Date);if((0,s.f)(t)){let r=e.startsWith("MM")?Number.parseInt(o,10):Number.parseInt(a,10),s=e.startsWith("MM")?Number.parseInt(a,10):Number.parseInt(o,10);if(r>=1&&r<=12&&s>=1&&s<=31)return t}}catch(e){console.debug("Error occurred while parsing date:",e)}if(!t.fallbackToBothFormats)return null}for(let e of["dd-MM-yyyy","MM-dd-yyyy","yyyy/MM/dd","dd.MM.yyyy","MM.dd.yyyy"])try{let t=(0,i.qg)(r,e,new Date);if((0,s.f)(t))return t}catch(e){}let a=new Date(r);if((0,s.f)(a))return a;return null}catch(e){return null}},u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"datetime-local";if(!e)return"";try{let r="string"==typeof e?(0,o.H)(e):e;if(!(0,s.f)(r))return"";if("date"===t)return(0,a.GP)(r,"yyyy-MM-dd");return(0,a.GP)(r,"yyyy-MM-dd'T'HH:mm")}catch(e){return""}},h=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l;if(!e)return"";try{if((0,n.$)(e))return e.toISOString();if("string"==typeof e){let r=c(e,t);if(r&&(0,s.f)(r))return r.toISOString()}return""}catch(e){return""}},d=e=>{if(!e)return!1;try{let t=new Date(e);return(0,s.f)(t)}catch(e){return!1}},f=e=>e&&""!==e.trim()?h(e):""},50546:(e,t,r)=>{r.d(t,{iG:()=>i,u1:()=>a});var o=r(3695);let s={DATABASE_ERROR:"A database error occurred. Please try again or contact support.",EMPLOYEE_NOT_ACTIVE:"The selected employee is not currently active. Please select an active employee.",EMPLOYEE_NOT_FOUND:"The selected employee could not be found. Please refresh and try again.",INVALID_DRIVER_ROLE:"The selected employee does not have driver role. Please select a different employee.",INVALID_FORMAT:"Please check the format of your input.",NETWORK_ERROR:"Network error. Please check your connection and try again.",REQUIRED_FIELD:"This field is required.",SERVER_ERROR:"Server error. Please try again later.",TIMEOUT_ERROR:"Request timed out. Please try again.",VALIDATION_ERROR:"Please check your input and try again.",VEHICLE_NOT_FOUND:"The selected vehicle could not be found. Please refresh and try again.",VEHICLE_WITHOUT_DRIVER:"A vehicle cannot be assigned without selecting a driver first."};function i(e,t){var r,o;let s=a(e)||"An unexpected error occurred. Please try again.",i="Error";return n(o=e)&&"string"==typeof o.code&&["INVALID_DRIVER_ROLE","EMPLOYEE_NOT_FOUND","EMPLOYEE_NOT_ACTIVE","VEHICLE_NOT_FOUND","VEHICLE_WITHOUT_DRIVER"].includes(o.code)?i="Assignment Error":n(e)?i="Validation Error":t&&(i="".concat(t," Error")),{code:null==e?void 0:e.code,field:(null==e?void 0:e.field)||(null==e||null==(r=e.path)?void 0:r[0]),message:s,title:i}}function a(e){if(!e)return null;if(e&&"object"==typeof e){if(e.code&&s[e.code]){var t;return null!=(t=s[e.code])?t:null}if(e.error&&"string"==typeof e.error)return e.error;if(e.message&&"string"==typeof e.message)return e.message;if(e.errors&&Array.isArray(e.errors)&&e.errors.length>0)return e.errors.map(e=>e.message||e).join(", ")}if("string"==typeof e)return e;if(e instanceof Error){if(e instanceof o.hD&&e.details){if("object"==typeof e.details){let t=e.details;if(Array.isArray(t.errors)&&t.errors.length>0)return t.errors.map(e=>e.message||e).join(", ");if(t.fieldErrors&&"object"==typeof t.fieldErrors){let e=Object.values(t.fieldErrors).flat().map(e=>e.message||e);if(e.length>0)return e.join(", ")}if(t.message)return t.message;if(t.error)return t.error}if("string"==typeof e.details)return e.details}return e.message}return"An unexpected error occurred. Please try again."}function n(e){return e&&"object"==typeof e&&"code"in e&&"message"in e&&"error"in e}},54036:(e,t,r)=>{r.d(t,{cn:()=>a});let o={backoffFactor:2,initialDelay:300,maxDelay:5e3,maxRetries:3,shouldRetry:e=>!e.response||e.response.status>=500&&e.response.status<600};r(21876),r(50546),r(16146),r(10694),r(77170);var s=r(52596),i=r(39688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,s.$)(t))}},77170:(e,t,r)=>{r.d(t,{Qb:()=>s,xR:()=>i});class o{clear(){let e=this.cache.size;this.cache.clear(),console.log("\uD83D\uDD04 Cache: Cleared ".concat(e," entries"))}async get(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o={...this.defaultOptions,...r},s=Date.now(),i=this.cache.get(e);if(i&&s<i.expiresAt){let t=Math.round((i.expiresAt-s)/1e3);return console.log("\uD83D\uDD04 Cache HIT for ".concat(e," (expires in ").concat(t,"s)")),i.data}let a=this.lastRequestTime.get(e)||0;if(s-a<o.throttleTime&&i){let t=Math.round((o.throttleTime-(s-a))/1e3);return console.log("\uD83D\uDD04 Cache THROTTLED for ".concat(e,", returning stale data (throttle ends in ").concat(t,"s)")),i.data}return i&&o.staleWhileRevalidate&&s<i.expiresAt+o.duration?(console.log("\uD83D\uDD04 Cache STALE for ".concat(e,", revalidating in background")),this.revalidateInBackground(e,t,o),i.data):(console.log("\uD83D\uDD04 Cache MISS for ".concat(e,", fetching fresh data")),this.fetchAndCache(e,t,o))}getStats(){let e=Date.now();return{entries:[...this.cache.entries()].map(t=>{let[r,o]=t;return{age:e-o.timestamp,expiresIn:o.expiresAt-e,key:r}}),size:this.cache.size}}invalidate(e){this.cache.delete(e),console.log("\uD83D\uDD04 Cache: Invalidated ".concat(e))}invalidatePattern(e){let t=[];for(let r of this.cache.keys())e.test(r)&&t.push(r);for(let e of t)this.cache.delete(e);console.log("\uD83D\uDD04 Cache: Invalidated ".concat(t.length," entries matching pattern"))}cleanup(e){if(this.cache.size<=e)return;let t=[...this.cache.entries()].sort((e,t)=>{let[,r]=e,[,o]=t;return r.timestamp-o.timestamp}).slice(0,this.cache.size-e);for(let[e]of t)this.cache.delete(e);console.log("\uD83D\uDD04 Cache: Cleaned up ".concat(t.length," old entries"))}async fetchAndCache(e,t,r){try{let o=Date.now();this.lastRequestTime.set(e,o);let s=await t();return this.cache.set(e,{data:s,expiresAt:o+r.duration,timestamp:o}),this.cleanup(r.maxEntries),s}catch(r){let t=this.cache.get(e);if(t)return console.warn("\uD83D\uDD04 Cache: Returning stale data for ".concat(e," due to error:"),r),t.data;throw r}}async revalidateInBackground(e,t,r){try{await this.fetchAndCache(e,t,r),console.log("\uD83D\uDD04 Cache: Background revalidation completed for ".concat(e))}catch(t){console.warn("\uD83D\uDD04 Cache: Background revalidation failed for ".concat(e,":"),t)}}constructor(){this.cache=new Map,this.defaultOptions={duration:3e4,maxEntries:100,staleWhileRevalidate:!0,throttleTime:1e3},this.lastRequestTime=new Map}}let s=new o,i={AUDIT_LOGS:6e4,ERROR_LOGS:3e4,HEALTH_STATUS:6e4,PERFORMANCE_METRICS:12e4,USER_DATA:3e5}}}]);