{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/data/services/ReportGenerationService.ts"], "sourcesContent": ["// frontend/src/components/features/reporting/data/services/ReportGenerationService.ts\r\n\r\n/**\r\n * Report Generation Service\r\n *\r\n * Follows SOLID Principles:\r\n * - SRP: Single responsibility for coordinating report generation\r\n * - OCP: Open for extension with new report types\r\n * - LSP: Implements consistent interface for all report types\r\n * - ISP: Focused interface for report generation\r\n * - DIP: Depends on abstractions (data services, export services)\r\n */\r\n\r\n// Import types from the hook file where they are defined\r\nexport interface ReportGenerationResult {\r\n  data?: any; // Main report data\r\n  report?: any; // Legacy support\r\n  metadata: {\r\n    id: string;\r\n    type: string;\r\n    entityTypes?: string[];\r\n    entityType?: string;\r\n    entityId?: string;\r\n    format: string;\r\n    template: string;\r\n    generatedAt: string;\r\n    generatedBy: string;\r\n    filters?: Record<string, any>;\r\n    options?: Record<string, any>;\r\n  };\r\n}\r\n\r\nexport interface AggregateReportConfig {\r\n  entityType: string;\r\n  filters?: Record<string, any>;\r\n  template?: string;\r\n  format?: string;\r\n  options?: Record<string, any>;\r\n}\r\n\r\nexport interface IndividualReportConfig {\r\n  entityType: string;\r\n  entityId: string;\r\n  template?: string;\r\n  format?: string;\r\n  options?: Record<string, any>;\r\n}\r\n\r\nexport interface ReportGenerationConfig {\r\n  entityTypes: string[];\r\n  template: string;\r\n  format: string;\r\n  filters?: Record<string, any>;\r\n  options?: {\r\n    name?: string;\r\n    description?: string;\r\n    includeCharts?: boolean;\r\n    includeSummary?: boolean;\r\n  };\r\n}\r\n\r\n/**\r\n * API Client interface for dependency injection\r\n */\r\nexport interface IApiClient {\r\n  request(config: {\r\n    url: string;\r\n    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';\r\n    data?: any;\r\n  }): Promise<{ data: any }>;\r\n}\r\n\r\n/**\r\n * Interface for report generation service (DIP - Dependency Inversion)\r\n */\r\nexport interface IReportGenerationService {\r\n  generateAggregateReport(\r\n    config: AggregateReportConfig\r\n  ): Promise<ReportGenerationResult>;\r\n  generateIndividualReport(\r\n    config: IndividualReportConfig\r\n  ): Promise<ReportGenerationResult>;\r\n  generateComprehensiveReport(\r\n    config: ReportGenerationConfig\r\n  ): Promise<ReportGenerationResult>;\r\n}\r\n\r\n/**\r\n * Concrete implementation of report generation service\r\n *\r\n * SRP: Handles only report generation coordination\r\n * DRY: Reuses common request logic\r\n */\r\nexport class ReportGenerationService implements IReportGenerationService {\r\n  constructor(private apiClient: IApiClient) {}\r\n  /**\r\n   * Generate aggregate analytics report for entity type\r\n   *\r\n   * @param config - Aggregate report configuration\r\n   * @returns Promise<ReportGenerationResult>\r\n   */\r\n  async generateAggregateReport(\r\n    config: AggregateReportConfig\r\n  ): Promise<ReportGenerationResult> {\r\n    try {\r\n      const response = await this.apiClient.request({\r\n        url: `/api/reporting/reports/aggregate/${config.entityType}`,\r\n        method: 'POST',\r\n        data: {\r\n          filters: config.filters,\r\n          template: config.template || 'default',\r\n          format: 'json', // Always get JSON data for frontend processing\r\n          options: config.options || {},\r\n        },\r\n      });\r\n\r\n      // Normalize response structure - handle nested aggregate data\r\n      const responseData = response.data.data || response.data;\r\n      const aggregateData = responseData?.aggregate || responseData;\r\n\r\n      return {\r\n        data: aggregateData,\r\n        metadata: response.data.metadata ||\r\n          responseData?.metadata || {\r\n            id: `aggregate_${config.entityType}_${Date.now()}`,\r\n            type: 'aggregate',\r\n            entityType: config.entityType,\r\n            format: config.format || 'json',\r\n            template: config.template || 'default',\r\n            generatedAt: new Date().toISOString(),\r\n            generatedBy: 'system',\r\n            filters: config.filters,\r\n            options: config.options,\r\n          },\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to generate aggregate report:', error);\r\n      throw new Error(\r\n        `Failed to generate ${config.entityType} aggregate report`\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate individual entity report\r\n   *\r\n   * @param config - Individual report configuration\r\n   * @returns Promise<ReportGenerationResult>\r\n   */\r\n  async generateIndividualReport(\r\n    config: IndividualReportConfig\r\n  ): Promise<ReportGenerationResult> {\r\n    try {\r\n      const response = await this.apiClient.request({\r\n        url: `/api/reporting/reports/individual/${config.entityType}/${config.entityId}`,\r\n        method: 'POST',\r\n        data: {\r\n          template: config.template || 'default',\r\n          format: 'json', // Always get JSON data for frontend processing\r\n          options: config.options || {},\r\n        },\r\n      });\r\n\r\n      return {\r\n        data: response.data.data || response.data,\r\n        metadata: response.data.metadata || {\r\n          id: `individual_${config.entityType}_${config.entityId}_${Date.now()}`,\r\n          type: 'individual',\r\n          entityType: config.entityType,\r\n          entityId: config.entityId,\r\n          format: config.format || 'json',\r\n          template: config.template || 'default',\r\n          generatedAt: new Date().toISOString(),\r\n          generatedBy: 'system',\r\n          options: config.options,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to generate individual report:', error);\r\n      throw new Error(\r\n        `Failed to generate ${config.entityType} individual report`\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Generate comprehensive cross-entity report\r\n   *\r\n   * @param config - Comprehensive report configuration\r\n   * @returns Promise<ReportGenerationResult>\r\n   */\r\n  async generateComprehensiveReport(\r\n    config: ReportGenerationConfig\r\n  ): Promise<ReportGenerationResult> {\r\n    try {\r\n      const response = await this.apiClient.request({\r\n        url: '/api/reporting/reports/generate',\r\n        method: 'POST',\r\n        data: {\r\n          entityTypes: config.entityTypes,\r\n          filters: config.filters,\r\n          template: config.template || 'comprehensive',\r\n          format: 'json', // Always get JSON data for frontend processing\r\n          options: config.options || {},\r\n        },\r\n      });\r\n\r\n      return {\r\n        data: response.data.data || response.data,\r\n        metadata: response.data.metadata || {\r\n          id: `comprehensive_${Date.now()}`,\r\n          type: 'comprehensive',\r\n          entityTypes: config.entityTypes,\r\n          format: config.format || 'json',\r\n          template: config.template || 'comprehensive',\r\n          generatedAt: new Date().toISOString(),\r\n          generatedBy: 'system',\r\n          filters: config.filters,\r\n          options: config.options,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to generate comprehensive report:', error);\r\n      throw new Error('Failed to generate comprehensive report');\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Default API client implementation\r\n * Note: This will be injected from the hook that has access to secureRequest\r\n */\r\nclass DefaultApiClient implements IApiClient {\r\n  async request(config: {\r\n    url: string;\r\n    method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';\r\n    data?: any;\r\n  }): Promise<{ data: any }> {\r\n    throw new Error(\r\n      'API client not initialized. Use createReportGenerationService with proper API client.'\r\n    );\r\n  }\r\n}\r\n\r\n/**\r\n * Factory function for dependency injection (DIP)\r\n * Allows for easy testing and mocking\r\n */\r\nexport const createReportGenerationService = (\r\n  apiClient: IApiClient\r\n): IReportGenerationService => {\r\n  return new ReportGenerationService(apiClient);\r\n};\r\n\r\n/**\r\n * Default instance (will be replaced by hook with proper API client)\r\n */\r\nexport const reportGenerationService = new ReportGenerationService(\r\n  new DefaultApiClient()\r\n);\r\n"], "names": [], "mappings": "AAAA,sFAAsF;AAEtF;;;;;;;;;CASC,GAED,yDAAyD;;;;;;AAgFlD,MAAM;;IACX,YAAY,AAAQ,SAAqB,CAAE;aAAvB,YAAA;IAAwB;IAC5C;;;;;GAKC,GACD,MAAM,wBACJ,MAA6B,EACI;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC5C,KAAK,CAAC,iCAAiC,EAAE,OAAO,UAAU,EAAE;gBAC5D,QAAQ;gBACR,MAAM;oBACJ,SAAS,OAAO,OAAO;oBACvB,UAAU,OAAO,QAAQ,IAAI;oBAC7B,QAAQ;oBACR,SAAS,OAAO,OAAO,IAAI,CAAC;gBAC9B;YACF;YAEA,8DAA8D;YAC9D,MAAM,eAAe,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;YACxD,MAAM,gBAAgB,cAAc,aAAa;YAEjD,OAAO;gBACL,MAAM;gBACN,UAAU,SAAS,IAAI,CAAC,QAAQ,IAC9B,cAAc,YAAY;oBACxB,IAAI,CAAC,UAAU,EAAE,OAAO,UAAU,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;oBAClD,MAAM;oBACN,YAAY,OAAO,UAAU;oBAC7B,QAAQ,OAAO,MAAM,IAAI;oBACzB,UAAU,OAAO,QAAQ,IAAI;oBAC7B,aAAa,IAAI,OAAO,WAAW;oBACnC,aAAa;oBACb,SAAS,OAAO,OAAO;oBACvB,SAAS,OAAO,OAAO;gBACzB;YACJ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MACR,CAAC,mBAAmB,EAAE,OAAO,UAAU,CAAC,iBAAiB,CAAC;QAE9D;IACF;IAEA;;;;;GAKC,GACD,MAAM,yBACJ,MAA8B,EACG;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC5C,KAAK,CAAC,kCAAkC,EAAE,OAAO,UAAU,CAAC,CAAC,EAAE,OAAO,QAAQ,EAAE;gBAChF,QAAQ;gBACR,MAAM;oBACJ,UAAU,OAAO,QAAQ,IAAI;oBAC7B,QAAQ;oBACR,SAAS,OAAO,OAAO,IAAI,CAAC;gBAC9B;YACF;YAEA,OAAO;gBACL,MAAM,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;gBACzC,UAAU,SAAS,IAAI,CAAC,QAAQ,IAAI;oBAClC,IAAI,CAAC,WAAW,EAAE,OAAO,UAAU,CAAC,CAAC,EAAE,OAAO,QAAQ,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;oBACtE,MAAM;oBACN,YAAY,OAAO,UAAU;oBAC7B,UAAU,OAAO,QAAQ;oBACzB,QAAQ,OAAO,MAAM,IAAI;oBACzB,UAAU,OAAO,QAAQ,IAAI;oBAC7B,aAAa,IAAI,OAAO,WAAW;oBACnC,aAAa;oBACb,SAAS,OAAO,OAAO;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM,IAAI,MACR,CAAC,mBAAmB,EAAE,OAAO,UAAU,CAAC,kBAAkB,CAAC;QAE/D;IACF;IAEA;;;;;GAKC,GACD,MAAM,4BACJ,MAA8B,EACG;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;gBAC5C,KAAK;gBACL,QAAQ;gBACR,MAAM;oBACJ,aAAa,OAAO,WAAW;oBAC/B,SAAS,OAAO,OAAO;oBACvB,UAAU,OAAO,QAAQ,IAAI;oBAC7B,QAAQ;oBACR,SAAS,OAAO,OAAO,IAAI,CAAC;gBAC9B;YACF;YAEA,OAAO;gBACL,MAAM,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI;gBACzC,UAAU,SAAS,IAAI,CAAC,QAAQ,IAAI;oBAClC,IAAI,CAAC,cAAc,EAAE,KAAK,GAAG,IAAI;oBACjC,MAAM;oBACN,aAAa,OAAO,WAAW;oBAC/B,QAAQ,OAAO,MAAM,IAAI;oBACzB,UAAU,OAAO,QAAQ,IAAI;oBAC7B,aAAa,IAAI,OAAO,WAAW;oBACnC,aAAa;oBACb,SAAS,OAAO,OAAO;oBACvB,SAAS,OAAO,OAAO;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM,IAAI,MAAM;QAClB;IACF;AACF;AAEA;;;CAGC,GACD,MAAM;IACJ,MAAM,QAAQ,MAIb,EAA0B;QACzB,MAAM,IAAI,MACR;IAEJ;AACF;AAMO,MAAM,gCAAgC,CAC3C;IAEA,OAAO,IAAI,wBAAwB;AACrC;AAKO,MAAM,0BAA0B,IAAI,wBACzC,IAAI", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/data/transformers/reportingTransformers.ts"], "sourcesContent": ["// PHASE 1 ENHANCEMENT: Import new entity analytics types\r\nimport {\r\n  DelegationAnalytics,\r\n  DelegationReportData,\r\n  TaskMetrics,\r\n  TrendData,\r\n  LocationMetrics,\r\n  DelegationStatusPrisma,\r\n  TaskPriorityPrisma,\r\n  TaskStatusPrisma,\r\n  StatusDistributionData,\r\n  ServiceHistoryData,\r\n  ServiceCostSummary,\r\n  TaskAnalyticsData,\r\n  RawDelegationAnalytics,\r\n  RawTaskMetrics,\r\n  RawTrendData,\r\n  RawLocationMetrics,\r\n  RawServiceHistoryData,\r\n  RawServiceCostSummary,\r\n  RawTaskAnalyticsData,\r\n  // PHASE 1: New entity analytics types\r\n  TaskAnalytics,\r\n  VehicleAnalytics,\r\n  EmployeeAnalytics,\r\n  CrossEntityAnalytics,\r\n  TaskStatusDistributionData,\r\n  TaskPriorityDistributionData,\r\n} from '../types/reporting';\r\nimport {\r\n  ServiceTypePrisma,\r\n  ServiceStatusPrisma,\r\n} from '../types/vehicleService';\r\n\r\n/**\r\n * Helper function to get color for delegation status.\r\n * FIXED: Use correct Prisma enum values instead of incorrect UPPERCASE versions.\r\n * @param status - Delegation status\r\n * @returns Color string for the status\r\n */\r\nconst getStatusColor = (status: string): string => {\r\n  const colorMap: Record<string, string> = {\r\n    Planned: '#3b82f6', // Blue\r\n    Confirmed: '#10b981', // Green\r\n    In_Progress: '#f59e0b', // Amber\r\n    Completed: '#22c55e', // Green\r\n    Cancelled: '#ef4444', // Red\r\n    No_details: '#6b7280', // Gray\r\n  };\r\n  return colorMap[status] || '#6b7280';\r\n};\r\n\r\n/**\r\n * PHASE 1 ENHANCEMENT: Helper function to get color for task status\r\n * @param status - Task status\r\n * @returns Color string for the status\r\n */\r\nconst getTaskStatusColor = (status: string): string => {\r\n  const colorMap: Record<string, string> = {\r\n    Pending: '#6b7280', // Gray\r\n    Assigned: '#3b82f6', // Blue\r\n    In_Progress: '#f59e0b', // Amber\r\n    Completed: '#22c55e', // Green\r\n    Cancelled: '#ef4444', // Red\r\n  };\r\n  return colorMap[status] || '#6b7280';\r\n};\r\n\r\n/**\r\n * PHASE 1 ENHANCEMENT: Helper function to get color for task priority\r\n * @param priority - Task priority\r\n * @returns Color string for the priority\r\n */\r\nconst getTaskPriorityColor = (priority: string): string => {\r\n  const colorMap: Record<string, string> = {\r\n    Low: '#10b981', // Green\r\n    Medium: '#f59e0b', // Amber\r\n    High: '#ef4444', // Red\r\n  };\r\n  return colorMap[priority] || '#6b7280';\r\n};\r\n\r\n/**\r\n * Transforms raw API response to DelegationAnalytics format\r\n * @param rawData - Raw API response data\r\n * @returns Transformed delegation analytics data\r\n */\r\nexport const transformDelegationAnalytics = (\r\n  rawData: RawDelegationAnalytics\r\n): DelegationAnalytics => {\r\n  const result: DelegationAnalytics = {\r\n    totalCount: rawData.totalCount || 0,\r\n    statusDistribution:\r\n      rawData.statusDistribution?.map((item: any) => ({\r\n        status: item.status,\r\n        count: item.count || 0,\r\n        percentage: item.percentage || 0,\r\n        color: getStatusColor(item.status), // Use the helper function\r\n      })) || [],\r\n    trendData:\r\n      rawData.trendData?.map((item: any) => ({\r\n        date: item.date,\r\n        created: item.created || 0,\r\n        completed: item.completed || 0,\r\n        inProgress: item.inProgress || 0,\r\n      })) || [],\r\n    locationMetrics: rawData.locationMetrics || [],\r\n    summary: {\r\n      totalDelegations: rawData.summary?.totalDelegations || 0,\r\n      activeDelegations: rawData.summary?.activeDelegations || 0,\r\n      completedDelegations: rawData.summary?.completedDelegations || 0,\r\n      totalDelegates: rawData.summary?.totalDelegates || 0,\r\n      averageDuration: rawData.summary?.averageDuration || 0,\r\n      completionRate: rawData.summary?.completionRate || 0,\r\n    },\r\n    // FIXED: Initialize delegations array\r\n    delegations: [],\r\n  };\r\n\r\n  // ENHANCED: Optional service history data transformation\r\n  if (rawData.serviceHistory) {\r\n    result.serviceHistory = transformServiceHistory(rawData.serviceHistory);\r\n  }\r\n\r\n  if (\r\n    rawData.serviceCosts &&\r\n    typeof rawData.serviceCosts === 'object' &&\r\n    !Array.isArray(rawData.serviceCosts)\r\n  ) {\r\n    result.serviceCosts = transformServiceCostSummary(rawData.serviceCosts);\r\n  }\r\n\r\n  if (\r\n    rawData.taskData &&\r\n    typeof rawData.taskData === 'object' &&\r\n    !Array.isArray(rawData.taskData)\r\n  ) {\r\n    result.taskData = transformTaskAnalyticsData(rawData.taskData);\r\n  }\r\n\r\n  // FIXED: Add real delegations data from API response\r\n  result.delegations =\r\n    rawData.delegations?.map((item: any) => ({\r\n      id: (item.id || 0).toString(),\r\n      delegationId: item.delegationId || item.id?.toString() || '',\r\n      customerName:\r\n        item.customerName || item.customer?.name || 'Unknown Customer',\r\n      vehicleModel:\r\n        item.vehicleModel || item.vehicle?.model || 'Unknown Vehicle',\r\n      licensePlate:\r\n        item.licensePlate || item.vehicle?.licensePlate || 'Unknown',\r\n      status: item.status,\r\n      assignedEmployee:\r\n        item.driverEmployee?.name || item.staffEmployee?.name || 'Unassigned',\r\n      location: item.location || '',\r\n      createdAt: item.createdAt || '',\r\n      completedAt: item.completedAt,\r\n    })) || [];\r\n\r\n  return result;\r\n};\r\n\r\n/**\r\n * Transforms raw API response to TaskMetrics format\r\n * @param rawData - Raw API response data\r\n * @returns Transformed task metrics data\r\n */\r\nexport const transformTaskMetrics = (rawData: RawTaskMetrics): TaskMetrics => {\r\n  return {\r\n    totalTasks: rawData.totalTasks || 0,\r\n    completedTasks: rawData.completedTasks || 0,\r\n    pendingTasks: rawData.pendingTasks || 0,\r\n    inProgressTasks: rawData.inProgressTasks || 0,\r\n    overdueTasks: rawData.overdueTasks || 0,\r\n    averageCompletionTime: rawData.averageCompletionTime || 0,\r\n    tasksByPriority:\r\n      rawData.tasksByPriority?.map((item: any) => ({\r\n        priority: item.priority,\r\n        count: item.count || 0,\r\n      })) || [],\r\n    tasksByStatus:\r\n      rawData.tasksByStatus?.map((item: any) => ({\r\n        status: item.status,\r\n        count: item.count || 0,\r\n      })) || [],\r\n  };\r\n};\r\n\r\n/**\r\n * Transforms raw API response to TrendData array format\r\n * @param rawData - Raw API response data\r\n * @returns Transformed trend data array\r\n */\r\nexport const transformTrendData = (rawData: RawTrendData[]): TrendData[] => {\r\n  return (\r\n    rawData?.map((item: any) => ({\r\n      date: item.date,\r\n      created: item.created || 0,\r\n      completed: item.completed || 0,\r\n      inProgress: item.inProgress || 0,\r\n    })) || []\r\n  );\r\n};\r\n\r\n/**\r\n * Transforms raw API response to LocationMetrics array format\r\n * @param rawData - Raw API response data\r\n * @returns Transformed location metrics array\r\n */\r\nexport const transformLocationMetrics = (\r\n  rawData: RawLocationMetrics[]\r\n): LocationMetrics[] => {\r\n  return (\r\n    rawData?.map((item: any) => ({\r\n      location: item.location || '',\r\n      delegationCount:\r\n        item.delegationCount || item.delegationsCount || item.count || 0,\r\n      averageDuration: item.averageDuration || 0,\r\n      completionRate: item.completionRate || 0,\r\n    })) || []\r\n  );\r\n};\r\n\r\n/**\r\n * ENHANCED: Transforms raw API response to ServiceHistoryData array format\r\n * @param rawData - Raw API response data\r\n * @returns Transformed service history data array\r\n */\r\nexport const transformServiceHistory = (rawData: any): ServiceHistoryData => {\r\n  return {\r\n    id: rawData.id || '',\r\n    vehicleId: rawData.vehicleId || 0,\r\n    vehicleName: rawData.vehicleName || '',\r\n    serviceType: rawData.serviceType as ServiceTypePrisma,\r\n    status: rawData.status as ServiceStatusPrisma,\r\n    scheduledDate: rawData.scheduledDate || '',\r\n    completedDate: rawData.completedDate,\r\n    cost: rawData.cost || 0,\r\n    description: rawData.description || '',\r\n    relatedDelegationId: rawData.relatedDelegationId,\r\n    relatedTaskId: rawData.relatedTaskId,\r\n  };\r\n};\r\n\r\n/**\r\n * ENHANCED: Transforms raw API response to ServiceCostSummary format\r\n * @param rawData - Raw API response data\r\n * @returns Transformed service cost summary data\r\n */\r\nexport const transformServiceCostSummary = (\r\n  rawData: RawServiceCostSummary\r\n): ServiceCostSummary => {\r\n  return {\r\n    totalCost: rawData.totalCost || 0,\r\n    averageCostPerService: rawData.averageCostPerService || 0,\r\n    costByType:\r\n      rawData.costByType?.map((item: any) => ({\r\n        type: item.type as ServiceTypePrisma,\r\n        cost: item.cost || 0,\r\n        count: item.count || 0,\r\n      })) || [],\r\n    monthlyTrend:\r\n      rawData.monthlyTrend?.map((item: any) => ({\r\n        month: item.month || '',\r\n        cost: item.cost || 0,\r\n      })) || [],\r\n  };\r\n};\r\n\r\n/**\r\n * ENHANCED: Transforms raw API response to TaskAnalyticsData format\r\n * @param rawData - Raw API response data\r\n * @returns Transformed task analytics data\r\n */\r\nexport const transformTaskAnalyticsData = (\r\n  rawData: RawTaskAnalyticsData\r\n): TaskAnalyticsData => {\r\n  return {\r\n    totalTasks: rawData.totalTasks || 0,\r\n    completedTasks: rawData.completedTasks || 0,\r\n    pendingTasks: rawData.pendingTasks || 0,\r\n    overdueTasks: rawData.overdueTasks || 0,\r\n    averageCompletionTime: rawData.averageCompletionTime || 0,\r\n    tasksByPriority:\r\n      rawData.tasksByPriority?.map((item: any) => ({\r\n        priority: item.priority,\r\n        count: item.count || 0,\r\n      })) || [],\r\n  };\r\n};\r\n\r\n/**\r\n * PHASE 1 ENHANCEMENT: Transforms raw API response to TaskAnalytics format\r\n * @param rawData - Raw API response data\r\n * @returns Transformed task analytics data\r\n */\r\nexport const transformTaskAnalytics = (rawData: any): TaskAnalytics => {\r\n  const totalCount = rawData.totalCount || 0;\r\n\r\n  return {\r\n    totalCount,\r\n    statusDistribution:\r\n      rawData.statusDistribution?.map((item: any) => ({\r\n        status: item.status as TaskStatusPrisma,\r\n        count: item.count || 0,\r\n        percentage: totalCount > 0 ? ((item.count || 0) / totalCount) * 100 : 0,\r\n        color: getTaskStatusColor(item.status),\r\n      })) || [],\r\n    priorityDistribution:\r\n      rawData.priorityDistribution?.map((item: any) => ({\r\n        priority: item.priority as TaskPriorityPrisma,\r\n        count: item.count || 0,\r\n        percentage: totalCount > 0 ? ((item.count || 0) / totalCount) * 100 : 0,\r\n        color: getTaskPriorityColor(item.priority),\r\n      })) || [],\r\n    completionRate: rawData.completionRate || 0,\r\n    overdueCount: rawData.overdueCount || 0,\r\n    averageCompletionTime: rawData.averageCompletionTime || 0,\r\n    assignmentMetrics:\r\n      rawData.assignmentMetrics?.map((item: any) => ({\r\n        employeeId: item.employeeId || 0,\r\n        employeeName: item.employeeName || '',\r\n        assignedTasks: item.assignedTasks || 0,\r\n        completedTasks: item.completedTasks || 0,\r\n        completionRate: item.completionRate || 0,\r\n        averageCompletionTime: item.averageCompletionTime || 0,\r\n      })) || [],\r\n    trendData:\r\n      rawData.trendData?.map((item: any) => ({\r\n        date: item.date || '',\r\n        created: item.created || 0,\r\n        completed: item.completed || 0,\r\n        inProgress: item.inProgress || 0,\r\n        overdue: item.overdue || 0,\r\n      })) || [],\r\n  };\r\n};\r\n\r\n/**\r\n * PHASE 1 ENHANCEMENT: Transforms raw API response to VehicleAnalytics format\r\n * @param rawData - Raw API response data\r\n * @returns Transformed vehicle analytics data\r\n */\r\nexport const transformVehicleAnalytics = (rawData: any): VehicleAnalytics => {\r\n  return {\r\n    totalCount: rawData.totalCount || 0,\r\n    serviceHistory:\r\n      rawData.serviceHistory?.map((item: any) =>\r\n        transformServiceHistory(item)\r\n      ) || [],\r\n    costAnalysis: rawData.costAnalysis\r\n      ? transformServiceCostSummary(rawData.costAnalysis)\r\n      : {\r\n          totalCost: 0,\r\n          averageCostPerService: 0,\r\n          costByType: [],\r\n          monthlyTrend: [],\r\n        },\r\n    utilizationMetrics:\r\n      rawData.utilizationMetrics?.map((item: any) => ({\r\n        vehicleId: item.vehicleId || 0,\r\n        vehicleName: item.vehicleName || '',\r\n        utilizationRate: item.utilizationRate || 0,\r\n        totalDelegations: item.totalDelegations || 0,\r\n        activeDelegations: item.activeDelegations || 0,\r\n        maintenanceHours: item.maintenanceHours || 0,\r\n      })) || [],\r\n    maintenanceSchedule:\r\n      rawData.maintenanceSchedule?.map((item: any) => ({\r\n        vehicleId: item.vehicleId || 0,\r\n        vehicleName: item.vehicleName || '',\r\n        nextMaintenanceDate: item.nextMaintenanceDate || '',\r\n        maintenanceType: item.maintenanceType as ServiceTypePrisma,\r\n        priority: item.priority || 'Medium',\r\n        estimatedCost: item.estimatedCost || 0,\r\n      })) || [],\r\n    performanceMetrics:\r\n      rawData.performanceMetrics?.map((item: any) => ({\r\n        vehicleId: item.vehicleId || 0,\r\n        vehicleName: item.vehicleName || '',\r\n        fuelEfficiency: item.fuelEfficiency || 0,\r\n        maintenanceCost: item.maintenanceCost || 0,\r\n        downtime: item.downtime || 0,\r\n        reliabilityScore: item.reliabilityScore || 0,\r\n      })) || [],\r\n  };\r\n};\r\n\r\n/**\r\n * PHASE 1 ENHANCEMENT: Transforms raw API response to EmployeeAnalytics format\r\n * @param rawData - Raw API response data\r\n * @returns Transformed employee analytics data\r\n */\r\nexport const transformEmployeeAnalytics = (rawData: any): EmployeeAnalytics => {\r\n  return {\r\n    totalCount: rawData.totalCount || 0,\r\n    performanceMetrics:\r\n      rawData.performanceMetrics?.map((item: any) => ({\r\n        employeeId: item.employeeId || 0,\r\n        employeeName: item.employeeName || '',\r\n        completedDelegations: item.completedDelegations || 0,\r\n        completedTasks: item.completedTasks || 0,\r\n        averageRating: item.averageRating || 0,\r\n        onTimePerformance: item.onTimePerformance || 0,\r\n        workloadScore: item.workloadScore || 0,\r\n      })) || [],\r\n    delegationHistory:\r\n      rawData.delegationHistory?.map((item: any) => ({\r\n        employeeId: item.employeeId || 0,\r\n        employeeName: item.employeeName || '',\r\n        totalDelegations: item.totalDelegations || 0,\r\n        completedDelegations: item.completedDelegations || 0,\r\n        averageDuration: item.averageDuration || 0,\r\n        successRate: item.successRate || 0,\r\n      })) || [],\r\n    taskAssignments:\r\n      rawData.taskAssignments?.map((item: any) => ({\r\n        employeeId: item.employeeId || 0,\r\n        employeeName: item.employeeName || '',\r\n        assignedTasks: item.assignedTasks || 0,\r\n        completedTasks: item.completedTasks || 0,\r\n        pendingTasks: item.pendingTasks || 0,\r\n        overdueTasksCount: item.overdueTasksCount || 0,\r\n      })) || [],\r\n    availabilityMetrics:\r\n      rawData.availabilityMetrics?.map((item: any) => ({\r\n        employeeId: item.employeeId || 0,\r\n        employeeName: item.employeeName || '',\r\n        availableHours: item.availableHours || 0,\r\n        scheduledHours: item.scheduledHours || 0,\r\n        utilizationRate: item.utilizationRate || 0,\r\n        overtimeHours: item.overtimeHours || 0,\r\n      })) || [],\r\n    workloadDistribution:\r\n      rawData.workloadDistribution?.map((item: any) => ({\r\n        employeeId: item.employeeId || 0,\r\n        employeeName: item.employeeName || '',\r\n        currentWorkload: item.currentWorkload || 0,\r\n        capacity: item.capacity || 0,\r\n        workloadPercentage: item.workloadPercentage || 0,\r\n        status: item.status || 'Optimal',\r\n      })) || [],\r\n  };\r\n};\r\n\r\n/**\r\n * PHASE 1 ENHANCEMENT: Transforms raw API response to CrossEntityAnalytics format\r\n * @param rawData - Raw API response data\r\n * @returns Transformed cross-entity analytics data\r\n */\r\nexport const transformCrossEntityAnalytics = (\r\n  rawData: any\r\n): CrossEntityAnalytics => {\r\n  const result: CrossEntityAnalytics = {\r\n    correlations: {\r\n      employeeVehicle: rawData.correlations?.employeeVehicle || [],\r\n      taskDelegation: rawData.correlations?.taskDelegation || [],\r\n      performanceWorkload: rawData.correlations?.performanceWorkload || [],\r\n      overall: rawData.correlations?.overall || [],\r\n    },\r\n    metrics: {\r\n      employeeVehicle: rawData.metrics?.employeeVehicle || 0,\r\n      taskDelegation: rawData.metrics?.taskDelegation || 0,\r\n      performanceWorkload: rawData.metrics?.performanceWorkload || 0,\r\n      overallEfficiency: rawData.metrics?.overallEfficiency || 0,\r\n    },\r\n  };\r\n\r\n  // Add optional properties only if they exist\r\n  if (rawData.network) {\r\n    result.network = {\r\n      nodes: rawData.network.nodes || [],\r\n      edges: rawData.network.edges || [],\r\n    };\r\n  }\r\n\r\n  if (rawData.insights) {\r\n    result.insights = rawData.insights || [];\r\n  }\r\n\r\n  return result;\r\n};\r\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;;;;;;;;;AAkCzD;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC;IACtB,MAAM,WAAmC;QACvC,SAAS;QACT,WAAW;QACX,aAAa;QACb,WAAW;QACX,WAAW;QACX,YAAY;IACd;IACA,OAAO,QAAQ,CAAC,OAAO,IAAI;AAC7B;AAEA;;;;CAIC,GACD,MAAM,qBAAqB,CAAC;IAC1B,MAAM,WAAmC;QACvC,SAAS;QACT,UAAU;QACV,aAAa;QACb,WAAW;QACX,WAAW;IACb;IACA,OAAO,QAAQ,CAAC,OAAO,IAAI;AAC7B;AAEA;;;;CAIC,GACD,MAAM,uBAAuB,CAAC;IAC5B,MAAM,WAAmC;QACvC,KAAK;QACL,QAAQ;QACR,MAAM;IACR;IACA,OAAO,QAAQ,CAAC,SAAS,IAAI;AAC/B;AAOO,MAAM,+BAA+B,CAC1C;IAEA,MAAM,SAA8B;QAClC,YAAY,QAAQ,UAAU,IAAI;QAClC,oBACE,QAAQ,kBAAkB,EAAE,IAAI,CAAC,OAAc,CAAC;gBAC9C,QAAQ,KAAK,MAAM;gBACnB,OAAO,KAAK,KAAK,IAAI;gBACrB,YAAY,KAAK,UAAU,IAAI;gBAC/B,OAAO,eAAe,KAAK,MAAM;YACnC,CAAC,MAAM,EAAE;QACX,WACE,QAAQ,SAAS,EAAE,IAAI,CAAC,OAAc,CAAC;gBACrC,MAAM,KAAK,IAAI;gBACf,SAAS,KAAK,OAAO,IAAI;gBACzB,WAAW,KAAK,SAAS,IAAI;gBAC7B,YAAY,KAAK,UAAU,IAAI;YACjC,CAAC,MAAM,EAAE;QACX,iBAAiB,QAAQ,eAAe,IAAI,EAAE;QAC9C,SAAS;YACP,kBAAkB,QAAQ,OAAO,EAAE,oBAAoB;YACvD,mBAAmB,QAAQ,OAAO,EAAE,qBAAqB;YACzD,sBAAsB,QAAQ,OAAO,EAAE,wBAAwB;YAC/D,gBAAgB,QAAQ,OAAO,EAAE,kBAAkB;YACnD,iBAAiB,QAAQ,OAAO,EAAE,mBAAmB;YACrD,gBAAgB,QAAQ,OAAO,EAAE,kBAAkB;QACrD;QACA,sCAAsC;QACtC,aAAa,EAAE;IACjB;IAEA,yDAAyD;IACzD,IAAI,QAAQ,cAAc,EAAE;QAC1B,OAAO,cAAc,GAAG,wBAAwB,QAAQ,cAAc;IACxE;IAEA,IACE,QAAQ,YAAY,IACpB,OAAO,QAAQ,YAAY,KAAK,YAChC,CAAC,MAAM,OAAO,CAAC,QAAQ,YAAY,GACnC;QACA,OAAO,YAAY,GAAG,4BAA4B,QAAQ,YAAY;IACxE;IAEA,IACE,QAAQ,QAAQ,IAChB,OAAO,QAAQ,QAAQ,KAAK,YAC5B,CAAC,MAAM,OAAO,CAAC,QAAQ,QAAQ,GAC/B;QACA,OAAO,QAAQ,GAAG,2BAA2B,QAAQ,QAAQ;IAC/D;IAEA,qDAAqD;IACrD,OAAO,WAAW,GAChB,QAAQ,WAAW,EAAE,IAAI,CAAC,OAAc,CAAC;YACvC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,QAAQ;YAC3B,cAAc,KAAK,YAAY,IAAI,KAAK,EAAE,EAAE,cAAc;YAC1D,cACE,KAAK,YAAY,IAAI,KAAK,QAAQ,EAAE,QAAQ;YAC9C,cACE,KAAK,YAAY,IAAI,KAAK,OAAO,EAAE,SAAS;YAC9C,cACE,KAAK,YAAY,IAAI,KAAK,OAAO,EAAE,gBAAgB;YACrD,QAAQ,KAAK,MAAM;YACnB,kBACE,KAAK,cAAc,EAAE,QAAQ,KAAK,aAAa,EAAE,QAAQ;YAC3D,UAAU,KAAK,QAAQ,IAAI;YAC3B,WAAW,KAAK,SAAS,IAAI;YAC7B,aAAa,KAAK,WAAW;QAC/B,CAAC,MAAM,EAAE;IAEX,OAAO;AACT;AAOO,MAAM,uBAAuB,CAAC;IACnC,OAAO;QACL,YAAY,QAAQ,UAAU,IAAI;QAClC,gBAAgB,QAAQ,cAAc,IAAI;QAC1C,cAAc,QAAQ,YAAY,IAAI;QACtC,iBAAiB,QAAQ,eAAe,IAAI;QAC5C,cAAc,QAAQ,YAAY,IAAI;QACtC,uBAAuB,QAAQ,qBAAqB,IAAI;QACxD,iBACE,QAAQ,eAAe,EAAE,IAAI,CAAC,OAAc,CAAC;gBAC3C,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK,IAAI;YACvB,CAAC,MAAM,EAAE;QACX,eACE,QAAQ,aAAa,EAAE,IAAI,CAAC,OAAc,CAAC;gBACzC,QAAQ,KAAK,MAAM;gBACnB,OAAO,KAAK,KAAK,IAAI;YACvB,CAAC,MAAM,EAAE;IACb;AACF;AAOO,MAAM,qBAAqB,CAAC;IACjC,OACE,SAAS,IAAI,CAAC,OAAc,CAAC;YAC3B,MAAM,KAAK,IAAI;YACf,SAAS,KAAK,OAAO,IAAI;YACzB,WAAW,KAAK,SAAS,IAAI;YAC7B,YAAY,KAAK,UAAU,IAAI;QACjC,CAAC,MAAM,EAAE;AAEb;AAOO,MAAM,2BAA2B,CACtC;IAEA,OACE,SAAS,IAAI,CAAC,OAAc,CAAC;YAC3B,UAAU,KAAK,QAAQ,IAAI;YAC3B,iBACE,KAAK,eAAe,IAAI,KAAK,gBAAgB,IAAI,KAAK,KAAK,IAAI;YACjE,iBAAiB,KAAK,eAAe,IAAI;YACzC,gBAAgB,KAAK,cAAc,IAAI;QACzC,CAAC,MAAM,EAAE;AAEb;AAOO,MAAM,0BAA0B,CAAC;IACtC,OAAO;QACL,IAAI,QAAQ,EAAE,IAAI;QAClB,WAAW,QAAQ,SAAS,IAAI;QAChC,aAAa,QAAQ,WAAW,IAAI;QACpC,aAAa,QAAQ,WAAW;QAChC,QAAQ,QAAQ,MAAM;QACtB,eAAe,QAAQ,aAAa,IAAI;QACxC,eAAe,QAAQ,aAAa;QACpC,MAAM,QAAQ,IAAI,IAAI;QACtB,aAAa,QAAQ,WAAW,IAAI;QACpC,qBAAqB,QAAQ,mBAAmB;QAChD,eAAe,QAAQ,aAAa;IACtC;AACF;AAOO,MAAM,8BAA8B,CACzC;IAEA,OAAO;QACL,WAAW,QAAQ,SAAS,IAAI;QAChC,uBAAuB,QAAQ,qBAAqB,IAAI;QACxD,YACE,QAAQ,UAAU,EAAE,IAAI,CAAC,OAAc,CAAC;gBACtC,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI,IAAI;gBACnB,OAAO,KAAK,KAAK,IAAI;YACvB,CAAC,MAAM,EAAE;QACX,cACE,QAAQ,YAAY,EAAE,IAAI,CAAC,OAAc,CAAC;gBACxC,OAAO,KAAK,KAAK,IAAI;gBACrB,MAAM,KAAK,IAAI,IAAI;YACrB,CAAC,MAAM,EAAE;IACb;AACF;AAOO,MAAM,6BAA6B,CACxC;IAEA,OAAO;QACL,YAAY,QAAQ,UAAU,IAAI;QAClC,gBAAgB,QAAQ,cAAc,IAAI;QAC1C,cAAc,QAAQ,YAAY,IAAI;QACtC,cAAc,QAAQ,YAAY,IAAI;QACtC,uBAAuB,QAAQ,qBAAqB,IAAI;QACxD,iBACE,QAAQ,eAAe,EAAE,IAAI,CAAC,OAAc,CAAC;gBAC3C,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK,IAAI;YACvB,CAAC,MAAM,EAAE;IACb;AACF;AAOO,MAAM,yBAAyB,CAAC;IACrC,MAAM,aAAa,QAAQ,UAAU,IAAI;IAEzC,OAAO;QACL;QACA,oBACE,QAAQ,kBAAkB,EAAE,IAAI,CAAC,OAAc,CAAC;gBAC9C,QAAQ,KAAK,MAAM;gBACnB,OAAO,KAAK,KAAK,IAAI;gBACrB,YAAY,aAAa,IAAI,AAAC,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,aAAc,MAAM;gBACtE,OAAO,mBAAmB,KAAK,MAAM;YACvC,CAAC,MAAM,EAAE;QACX,sBACE,QAAQ,oBAAoB,EAAE,IAAI,CAAC,OAAc,CAAC;gBAChD,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK,IAAI;gBACrB,YAAY,aAAa,IAAI,AAAC,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,aAAc,MAAM;gBACtE,OAAO,qBAAqB,KAAK,QAAQ;YAC3C,CAAC,MAAM,EAAE;QACX,gBAAgB,QAAQ,cAAc,IAAI;QAC1C,cAAc,QAAQ,YAAY,IAAI;QACtC,uBAAuB,QAAQ,qBAAqB,IAAI;QACxD,mBACE,QAAQ,iBAAiB,EAAE,IAAI,CAAC,OAAc,CAAC;gBAC7C,YAAY,KAAK,UAAU,IAAI;gBAC/B,cAAc,KAAK,YAAY,IAAI;gBACnC,eAAe,KAAK,aAAa,IAAI;gBACrC,gBAAgB,KAAK,cAAc,IAAI;gBACvC,gBAAgB,KAAK,cAAc,IAAI;gBACvC,uBAAuB,KAAK,qBAAqB,IAAI;YACvD,CAAC,MAAM,EAAE;QACX,WACE,QAAQ,SAAS,EAAE,IAAI,CAAC,OAAc,CAAC;gBACrC,MAAM,KAAK,IAAI,IAAI;gBACnB,SAAS,KAAK,OAAO,IAAI;gBACzB,WAAW,KAAK,SAAS,IAAI;gBAC7B,YAAY,KAAK,UAAU,IAAI;gBAC/B,SAAS,KAAK,OAAO,IAAI;YAC3B,CAAC,MAAM,EAAE;IACb;AACF;AAOO,MAAM,4BAA4B,CAAC;IACxC,OAAO;QACL,YAAY,QAAQ,UAAU,IAAI;QAClC,gBACE,QAAQ,cAAc,EAAE,IAAI,CAAC,OAC3B,wBAAwB,UACrB,EAAE;QACT,cAAc,QAAQ,YAAY,GAC9B,4BAA4B,QAAQ,YAAY,IAChD;YACE,WAAW;YACX,uBAAuB;YACvB,YAAY,EAAE;YACd,cAAc,EAAE;QAClB;QACJ,oBACE,QAAQ,kBAAkB,EAAE,IAAI,CAAC,OAAc,CAAC;gBAC9C,WAAW,KAAK,SAAS,IAAI;gBAC7B,aAAa,KAAK,WAAW,IAAI;gBACjC,iBAAiB,KAAK,eAAe,IAAI;gBACzC,kBAAkB,KAAK,gBAAgB,IAAI;gBAC3C,mBAAmB,KAAK,iBAAiB,IAAI;gBAC7C,kBAAkB,KAAK,gBAAgB,IAAI;YAC7C,CAAC,MAAM,EAAE;QACX,qBACE,QAAQ,mBAAmB,EAAE,IAAI,CAAC,OAAc,CAAC;gBAC/C,WAAW,KAAK,SAAS,IAAI;gBAC7B,aAAa,KAAK,WAAW,IAAI;gBACjC,qBAAqB,KAAK,mBAAmB,IAAI;gBACjD,iBAAiB,KAAK,eAAe;gBACrC,UAAU,KAAK,QAAQ,IAAI;gBAC3B,eAAe,KAAK,aAAa,IAAI;YACvC,CAAC,MAAM,EAAE;QACX,oBACE,QAAQ,kBAAkB,EAAE,IAAI,CAAC,OAAc,CAAC;gBAC9C,WAAW,KAAK,SAAS,IAAI;gBAC7B,aAAa,KAAK,WAAW,IAAI;gBACjC,gBAAgB,KAAK,cAAc,IAAI;gBACvC,iBAAiB,KAAK,eAAe,IAAI;gBACzC,UAAU,KAAK,QAAQ,IAAI;gBAC3B,kBAAkB,KAAK,gBAAgB,IAAI;YAC7C,CAAC,MAAM,EAAE;IACb;AACF;AAOO,MAAM,6BAA6B,CAAC;IACzC,OAAO;QACL,YAAY,QAAQ,UAAU,IAAI;QAClC,oBACE,QAAQ,kBAAkB,EAAE,IAAI,CAAC,OAAc,CAAC;gBAC9C,YAAY,KAAK,UAAU,IAAI;gBAC/B,cAAc,KAAK,YAAY,IAAI;gBACnC,sBAAsB,KAAK,oBAAoB,IAAI;gBACnD,gBAAgB,KAAK,cAAc,IAAI;gBACvC,eAAe,KAAK,aAAa,IAAI;gBACrC,mBAAmB,KAAK,iBAAiB,IAAI;gBAC7C,eAAe,KAAK,aAAa,IAAI;YACvC,CAAC,MAAM,EAAE;QACX,mBACE,QAAQ,iBAAiB,EAAE,IAAI,CAAC,OAAc,CAAC;gBAC7C,YAAY,KAAK,UAAU,IAAI;gBAC/B,cAAc,KAAK,YAAY,IAAI;gBACnC,kBAAkB,KAAK,gBAAgB,IAAI;gBAC3C,sBAAsB,KAAK,oBAAoB,IAAI;gBACnD,iBAAiB,KAAK,eAAe,IAAI;gBACzC,aAAa,KAAK,WAAW,IAAI;YACnC,CAAC,MAAM,EAAE;QACX,iBACE,QAAQ,eAAe,EAAE,IAAI,CAAC,OAAc,CAAC;gBAC3C,YAAY,KAAK,UAAU,IAAI;gBAC/B,cAAc,KAAK,YAAY,IAAI;gBACnC,eAAe,KAAK,aAAa,IAAI;gBACrC,gBAAgB,KAAK,cAAc,IAAI;gBACvC,cAAc,KAAK,YAAY,IAAI;gBACnC,mBAAmB,KAAK,iBAAiB,IAAI;YAC/C,CAAC,MAAM,EAAE;QACX,qBACE,QAAQ,mBAAmB,EAAE,IAAI,CAAC,OAAc,CAAC;gBAC/C,YAAY,KAAK,UAAU,IAAI;gBAC/B,cAAc,KAAK,YAAY,IAAI;gBACnC,gBAAgB,KAAK,cAAc,IAAI;gBACvC,gBAAgB,KAAK,cAAc,IAAI;gBACvC,iBAAiB,KAAK,eAAe,IAAI;gBACzC,eAAe,KAAK,aAAa,IAAI;YACvC,CAAC,MAAM,EAAE;QACX,sBACE,QAAQ,oBAAoB,EAAE,IAAI,CAAC,OAAc,CAAC;gBAChD,YAAY,KAAK,UAAU,IAAI;gBAC/B,cAAc,KAAK,YAAY,IAAI;gBACnC,iBAAiB,KAAK,eAAe,IAAI;gBACzC,UAAU,KAAK,QAAQ,IAAI;gBAC3B,oBAAoB,KAAK,kBAAkB,IAAI;gBAC/C,QAAQ,KAAK,MAAM,IAAI;YACzB,CAAC,MAAM,EAAE;IACb;AACF;AAOO,MAAM,gCAAgC,CAC3C;IAEA,MAAM,SAA+B;QACnC,cAAc;YACZ,iBAAiB,QAAQ,YAAY,EAAE,mBAAmB,EAAE;YAC5D,gBAAgB,QAAQ,YAAY,EAAE,kBAAkB,EAAE;YAC1D,qBAAqB,QAAQ,YAAY,EAAE,uBAAuB,EAAE;YACpE,SAAS,QAAQ,YAAY,EAAE,WAAW,EAAE;QAC9C;QACA,SAAS;YACP,iBAAiB,QAAQ,OAAO,EAAE,mBAAmB;YACrD,gBAAgB,QAAQ,OAAO,EAAE,kBAAkB;YACnD,qBAAqB,QAAQ,OAAO,EAAE,uBAAuB;YAC7D,mBAAmB,QAAQ,OAAO,EAAE,qBAAqB;QAC3D;IACF;IAEA,6CAA6C;IAC7C,IAAI,QAAQ,OAAO,EAAE;QACnB,OAAO,OAAO,GAAG;YACf,OAAO,QAAQ,OAAO,CAAC,KAAK,IAAI,EAAE;YAClC,OAAO,QAAQ,OAAO,CAAC,KAAK,IAAI,EAAE;QACpC;IACF;IAEA,IAAI,QAAQ,QAAQ,EAAE;QACpB,OAAO,QAAQ,GAAG,QAAQ,QAAQ,IAAI,EAAE;IAC1C;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/data/services/ReportingDataService.ts"], "sourcesContent": ["// frontend/src/components/features/reporting/data/services/ReportingDataService.ts\r\n\r\nimport { apiClient } from '@/lib/api';\r\n\r\n// PHASE 1 ENHANCEMENT: Import new entity analytics types\r\nimport type {\r\n  CrossEntityAnalytics,\r\n  DelegationAnalytics,\r\n  EmployeeAnalytics,\r\n  IReportingDataService,\r\n  LocationMetrics,\r\n  PaginatedDelegationsResponse,\r\n  ReportingFilters,\r\n  ServiceCostSummary,\r\n  ServiceHistoryData,\r\n  // PHASE 1: New entity analytics types\r\n  TaskAnalytics,\r\n  TaskMetrics,\r\n  TrendData,\r\n  VehicleAnalytics,\r\n} from '../types/reporting';\r\n\r\nimport {\r\n  transformDelegationAnalytics,\r\n  transformLocationMetrics,\r\n  transformTaskMetrics,\r\n  transformTrendData,\r\n} from '../transformers/reportingTransformers';\r\nimport { TaskAnalyticsData } from '../types/reporting';\r\n\r\n// API Response interface for consistent typing\r\ninterface ApiResponse<T = any> {\r\n  data?: T;\r\n  status?: string;\r\n  timestamp?: string;\r\n}\r\n\r\n/**\r\n * ReportingDataService - Single Responsibility: Data fetching and transformation for reporting\r\n *\r\n * This service abstracts all API calls and basic data transformation for the reporting module.\r\n * It does NOT manage component state or server cache - that's handled by React Query hooks.\r\n *\r\n * Follows SRP by having one clear purpose: data access layer for reporting.\r\n */\r\nexport class ReportingDataService implements IReportingDataService {\r\n  private readonly baseUrl: string;\r\n\r\n  constructor(baseUrl = '/api/reporting') {\r\n    this.baseUrl = baseUrl;\r\n  }\r\n\r\n  /**\r\n   * PHASE 1 ENHANCEMENT: Get cross-entity analytics data\r\n   * @param filters - Reporting filters to apply\r\n   * @returns Promise resolving to cross-entity analytics data\r\n   */\r\n  async getCrossEntityAnalytics(\r\n    filters: ReportingFilters\r\n  ): Promise<CrossEntityAnalytics> {\r\n    try {\r\n      const queryParamsString = this.buildQueryParams(filters);\r\n      const queryParams = new URLSearchParams(queryParamsString);\r\n\r\n      // Add cross-entity parameters\r\n      if (filters.includeCrossEntityCorrelations) {\r\n        queryParams.append('includeCrossEntityCorrelations', 'true');\r\n      }\r\n\r\n      const apiResponse = (await apiClient.get(\r\n        `/reporting/cross-entity/analytics?${queryParams.toString()}`\r\n      )) as any;\r\n      return apiResponse.data || apiResponse;\r\n    } catch (error) {\r\n      console.error('Error fetching cross-entity analytics:', error);\r\n      throw new Error(\r\n        `Failed to load cross-entity analytics: ${error instanceof Error ? error.message : 'Unknown error'}`\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fetches delegation analytics data based on provided filters\r\n   * ENHANCED: Now supports service history and task data integration\r\n   * @param filters - Reporting filters to apply\r\n   * @returns Promise resolving to delegation analytics data\r\n   */\r\n  async getDelegationAnalytics(\r\n    filters: ReportingFilters\r\n  ): Promise<DelegationAnalytics> {\r\n    try {\r\n      const queryParamsString = this.buildQueryParams(filters);\r\n      const queryParams = new URLSearchParams(queryParamsString);\r\n\r\n      // ENHANCED: Add service history and task data parameters\r\n      if (filters.includeServiceHistory) {\r\n        queryParams.append('includeServiceHistory', 'true');\r\n      }\r\n      if (filters.includeTaskData) {\r\n        queryParams.append('includeTaskData', 'true');\r\n      }\r\n\r\n      const apiResponse = (await apiClient.get(\r\n        `/reporting/delegations/analytics?${queryParams.toString()}`\r\n      )) as any;\r\n      // Backend returns { status: 'success', data: {...}, timestamp: '...' }\r\n      return transformDelegationAnalytics(apiResponse.data || apiResponse);\r\n    } catch (error) {\r\n      console.error('Error fetching delegation analytics:', error);\r\n      throw new Error(\r\n        `Failed to load delegation analytics: ${error instanceof Error ? error.message : 'Unknown error'}`\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fetches paginated delegations data based on provided filters\r\n   * @param filters - Reporting filters to apply\r\n   * @param pagination - Pagination parameters\r\n   * @returns Promise resolving to paginated delegations response\r\n   */\r\n  async getDelegations(\r\n    filters: ReportingFilters,\r\n    pagination: { page: number; pageSize: number }\r\n  ): Promise<PaginatedDelegationsResponse> {\r\n    try {\r\n      const queryParams = this.buildQueryParams(filters);\r\n      const paginationParams = new URLSearchParams(queryParams);\r\n\r\n      // Add pagination parameters\r\n      paginationParams.append('page', pagination.page.toString());\r\n      paginationParams.append('pageSize', pagination.pageSize.toString());\r\n\r\n      const apiResponse = (await apiClient.get(\r\n        `/reporting/delegations?${paginationParams.toString()}`\r\n      )) as any;\r\n      // Backend returns { status: 'success', data: {...}, timestamp: '...' }\r\n      return apiResponse.data || apiResponse;\r\n    } catch (error) {\r\n      console.error('Error fetching delegations:', error);\r\n      throw new Error(\r\n        `Failed to load delegations: ${error instanceof Error ? error.message : 'Unknown error'}`\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * PHASE 1 ENHANCEMENT: Get employee analytics data\r\n   * @param filters - Reporting filters to apply\r\n   * @returns Promise resolving to employee analytics data\r\n   */\r\n  async getEmployeeAnalytics(\r\n    filters: ReportingFilters\r\n  ): Promise<EmployeeAnalytics> {\r\n    try {\r\n      const queryParamsString = this.buildQueryParams(filters);\r\n      const apiResponse = (await apiClient.get(\r\n        `/reporting/employee/analytics?${queryParamsString}`\r\n      )) as any;\r\n      return apiResponse.data || apiResponse;\r\n    } catch (error) {\r\n      console.error('Error fetching employee analytics:', error);\r\n      throw new Error(\r\n        `Failed to load employee analytics: ${error instanceof Error ? error.message : 'Unknown error'}`\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fetches location metrics based on provided filters\r\n   * @param filters - Reporting filters to apply\r\n   * @returns Promise resolving to location metrics array\r\n   */\r\n  async getLocationMetrics(\r\n    filters: ReportingFilters\r\n  ): Promise<LocationMetrics[]> {\r\n    try {\r\n      const queryParams = this.buildQueryParams(filters);\r\n      const apiResponse = (await apiClient.get(\r\n        `/reporting/locations/metrics?${queryParams}`\r\n      )) as any;\r\n      // Backend returns { status: 'success', data: [...], timestamp: '...' }\r\n      return transformLocationMetrics(apiResponse.data || apiResponse);\r\n    } catch (error) {\r\n      console.error('Error fetching location metrics:', error);\r\n      throw new Error(\r\n        `Failed to load location metrics: ${error instanceof Error ? error.message : 'Unknown error'}`\r\n      );\r\n    }\r\n  }\r\n\r\n  // Private helper methods for data transformation and utility functions\r\n\r\n  /**\r\n   * ENHANCED: Fetches service cost summary\r\n   * @param filters - Reporting filters to apply\r\n   * @returns Promise resolving to service cost summary\r\n   */\r\n  async getServiceCostSummary(\r\n    filters: ReportingFilters\r\n  ): Promise<ServiceCostSummary> {\r\n    try {\r\n      const queryParamsString = this.buildQueryParams(filters);\r\n      const rawData = (await apiClient.get(\r\n        `/reporting/services/costs?${queryParamsString}`\r\n      )) as any;\r\n      return (\r\n        rawData.data || {\r\n          averageCostPerService: 0,\r\n          costByType: [],\r\n          monthlyTrend: [],\r\n          totalCost: 0,\r\n        }\r\n      );\r\n    } catch (error) {\r\n      console.error('Error fetching service costs:', error);\r\n      throw new Error(\r\n        `Failed to load service costs: ${error instanceof Error ? error.message : 'Unknown error'}`\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * ENHANCED: Fetches service history data for vehicles\r\n   * @param filters - Reporting filters to apply\r\n   * @returns Promise resolving to service history data\r\n   */\r\n  async getServiceHistory(\r\n    filters: ReportingFilters\r\n  ): Promise<ServiceHistoryData[]> {\r\n    try {\r\n      const queryParamsString = this.buildQueryParams(filters);\r\n      const rawData = (await apiClient.get(\r\n        `/reporting/services/history?${queryParamsString}`\r\n      )) as any;\r\n      // Transform service history data if needed\r\n      return rawData.data || [];\r\n    } catch (error) {\r\n      console.error('Error fetching service history:', error);\r\n      throw new Error(\r\n        `Failed to load service history: ${error instanceof Error ? error.message : 'Unknown error'}`\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * PHASE 1 ENHANCEMENT: Get task analytics data\r\n   * @param filters - Reporting filters to apply\r\n   * @returns Promise resolving to task analytics data\r\n   */\r\n  async getTaskAnalytics(filters: ReportingFilters): Promise<TaskAnalytics> {\r\n    try {\r\n      const queryParamsString = this.buildQueryParams(filters);\r\n      const queryParams = new URLSearchParams(queryParamsString);\r\n\r\n      // Add task-specific parameters\r\n      if (filters.taskStatus && filters.taskStatus.length > 0) {\r\n        queryParams.append('taskStatus', filters.taskStatus.join(','));\r\n      }\r\n      if (filters.taskPriority && filters.taskPriority.length > 0) {\r\n        queryParams.append('taskPriority', filters.taskPriority.join(','));\r\n      }\r\n\r\n      const apiResponse = (await apiClient.get(\r\n        `/reporting/tasks/analytics?${queryParams.toString()}`\r\n      )) as any;\r\n      return apiResponse.data || apiResponse;\r\n    } catch (error) {\r\n      console.error('Error fetching task analytics:', error);\r\n      throw new Error(\r\n        `Failed to load task analytics: ${error instanceof Error ? error.message : 'Unknown error'}`\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fetches task metrics for specific delegations or all tasks\r\n   * @param delegationIds - Optional array of delegation IDs to filter by\r\n   * @returns Promise resolving to task metrics data\r\n   */\r\n  async getTaskMetrics(delegationIds?: string[]): Promise<TaskMetrics> {\r\n    try {\r\n      const queryParams = delegationIds?.length\r\n        ? `delegationIds=${delegationIds.join(',')}`\r\n        : '';\r\n\r\n      const apiResponse = (await apiClient.get(\r\n        `/reporting/tasks/metrics?${queryParams}`\r\n      )) as any;\r\n      // Backend returns { status: 'success', data: {...}, timestamp: '...' }\r\n      return transformTaskMetrics(apiResponse.data || apiResponse);\r\n    } catch (error) {\r\n      console.error('Error fetching task metrics:', error);\r\n      throw new Error(\r\n        `Failed to load task metrics: ${error instanceof Error ? error.message : 'Unknown error'}`\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fetches trend data based on provided filters\r\n   * @param filters - Reporting filters to apply\r\n   * @returns Promise resolving to trend data array\r\n   */\r\n  async getTrendData(filters: ReportingFilters): Promise<TrendData[]> {\r\n    try {\r\n      const queryParams = this.buildQueryParams(filters);\r\n      const apiResponse = (await apiClient.get(\r\n        `/reporting/trends?${queryParams}`\r\n      )) as any;\r\n      // Backend returns { status: 'success', data: [...], timestamp: '...' }\r\n      return transformTrendData(apiResponse.data || apiResponse);\r\n    } catch (error) {\r\n      console.error('Error fetching trend data:', error);\r\n      throw new Error(\r\n        `Failed to load trend data: ${error instanceof Error ? error.message : 'Unknown error'}`\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * PHASE 1 ENHANCEMENT: Get vehicle analytics data\r\n   * @param filters - Reporting filters to apply\r\n   * @returns Promise resolving to vehicle analytics data\r\n   */\r\n  async getVehicleAnalytics(\r\n    filters: ReportingFilters\r\n  ): Promise<VehicleAnalytics> {\r\n    try {\r\n      const queryParamsString = this.buildQueryParams(filters);\r\n      const queryParams = new URLSearchParams(queryParamsString);\r\n\r\n      // Add vehicle-specific parameters\r\n      if (filters.vehicles && filters.vehicles.length > 0) {\r\n        queryParams.append('vehicles', filters.vehicles.join(','));\r\n      }\r\n      if (filters.serviceTypes && filters.serviceTypes.length > 0) {\r\n        queryParams.append('serviceTypes', filters.serviceTypes.join(','));\r\n      }\r\n      if (filters.serviceStatus && filters.serviceStatus.length > 0) {\r\n        queryParams.append('serviceStatus', filters.serviceStatus.join(','));\r\n      }\r\n\r\n      const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    } catch (error) {\r\n      console.error('Error fetching vehicle analytics:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * PHASE 1 ENHANCEMENT: Helper method for appending array parameters (DRY principle)\r\n   * @param params - URLSearchParams object\r\n   * @param key - Parameter key\r\n   * @param values - Array of values to append\r\n   */\r\n  private appendArrayParams(\r\n    params: URLSearchParams,\r\n    key: string,\r\n    values: any[]\r\n  ): void {\r\n    if (values && values.length > 0) {\r\n      params.append(key, values.join(','));\r\n    }\r\n  }\r\n\r\n  /**\r\n   * PHASE 1 ENHANCEMENT: Builds query parameters string from filters object\r\n   * Following DRY principle: Reusable query building logic\r\n   * @param filters - Reporting filters\r\n   * @returns URL-encoded query parameters string\r\n   */\r\n  private buildQueryParams(filters: ReportingFilters): string {\r\n    const params = new URLSearchParams();\r\n\r\n    // Date range - use the format expected by backend validation\r\n    // Defensive programming: Ensure dates are Date objects\r\n    try {\r\n      const fromDate =\r\n        filters.dateRange.from instanceof Date\r\n          ? filters.dateRange.from\r\n          : new Date(filters.dateRange.from);\r\n      const toDate =\r\n        filters.dateRange.to instanceof Date\r\n          ? filters.dateRange.to\r\n          : new Date(filters.dateRange.to);\r\n\r\n      // Validate that dates are valid\r\n      if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {\r\n        throw new TypeError('Invalid date range provided');\r\n      }\r\n\r\n      params.append(\r\n        'dateRange.from',\r\n        fromDate.toISOString().split('T')[0] || fromDate.toISOString()\r\n      );\r\n      params.append(\r\n        'dateRange.to',\r\n        toDate.toISOString().split('T')[0] || toDate.toISOString()\r\n      );\r\n    } catch (error) {\r\n      console.error('Error processing date range:', error);\r\n      // Fallback to default date range (last 30 days)\r\n      const defaultToDate = new Date();\r\n      const defaultFromDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\r\n      params.append(\r\n        'dateRange.from',\r\n        defaultFromDate.toISOString().split('T')[0] ||\r\n          defaultFromDate.toISOString()\r\n      );\r\n      params.append(\r\n        'dateRange.to',\r\n        defaultToDate.toISOString().split('T')[0] || defaultToDate.toISOString()\r\n      );\r\n    }\r\n\r\n    // Common array handling using helper method (DRY principle)\r\n    this.appendArrayParams(params, 'status', filters.status);\r\n    this.appendArrayParams(params, 'locations', filters.locations);\r\n    this.appendArrayParams(params, 'employees', filters.employees);\r\n    this.appendArrayParams(params, 'vehicles', filters.vehicles);\r\n\r\n    // PHASE 1: Additional entity parameters\r\n    if (filters.taskStatus) {\r\n      this.appendArrayParams(params, 'taskStatus', filters.taskStatus);\r\n    }\r\n    if (filters.taskPriority) {\r\n      this.appendArrayParams(params, 'taskPriority', filters.taskPriority);\r\n    }\r\n    if (filters.serviceTypes) {\r\n      this.appendArrayParams(params, 'serviceTypes', filters.serviceTypes);\r\n    }\r\n    if (filters.serviceStatus) {\r\n      this.appendArrayParams(params, 'serviceStatus', filters.serviceStatus);\r\n    }\r\n\r\n    // Cost range handling\r\n    if (filters.costRange) {\r\n      params.append('minCost', filters.costRange.min.toString());\r\n      params.append('maxCost', filters.costRange.max.toString());\r\n    }\r\n\r\n    return params.toString();\r\n  }\r\n}\r\n\r\n// Export singleton instance for use throughout the application\r\nexport const reportingDataService = new ReportingDataService();\r\n"], "names": [], "mappings": "AAAA,mFAAmF;;;;;AAEnF;AAAA;AAoBA;;;AAuBO,MAAM;IACM,QAAgB;IAEjC,YAAY,UAAU,gBAAgB,CAAE;QACtC,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA;;;;GAIC,GACD,MAAM,wBACJ,OAAyB,EACM;QAC/B,IAAI;YACF,MAAM,oBAAoB,IAAI,CAAC,gBAAgB,CAAC;YAChD,MAAM,cAAc,IAAI,gBAAgB;YAExC,8BAA8B;YAC9B,IAAI,QAAQ,8BAA8B,EAAE;gBAC1C,YAAY,MAAM,CAAC,kCAAkC;YACvD;YAEA,MAAM,cAAe,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CACtC,CAAC,kCAAkC,EAAE,YAAY,QAAQ,IAAI;YAE/D,OAAO,YAAY,IAAI,IAAI;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,MAAM,IAAI,MACR,CAAC,uCAAuC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAExG;IACF;IAEA;;;;;GAKC,GACD,MAAM,uBACJ,OAAyB,EACK;QAC9B,IAAI;YACF,MAAM,oBAAoB,IAAI,CAAC,gBAAgB,CAAC;YAChD,MAAM,cAAc,IAAI,gBAAgB;YAExC,yDAAyD;YACzD,IAAI,QAAQ,qBAAqB,EAAE;gBACjC,YAAY,MAAM,CAAC,yBAAyB;YAC9C;YACA,IAAI,QAAQ,eAAe,EAAE;gBAC3B,YAAY,MAAM,CAAC,mBAAmB;YACxC;YAEA,MAAM,cAAe,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CACtC,CAAC,iCAAiC,EAAE,YAAY,QAAQ,IAAI;YAE9D,uEAAuE;YACvE,OAAO,CAAA,GAAA,2LAAA,CAAA,+BAA4B,AAAD,EAAE,YAAY,IAAI,IAAI;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM,IAAI,MACR,CAAC,qCAAqC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAEtG;IACF;IAEA;;;;;GAKC,GACD,MAAM,eACJ,OAAyB,EACzB,UAA8C,EACP;QACvC,IAAI;YACF,MAAM,cAAc,IAAI,CAAC,gBAAgB,CAAC;YAC1C,MAAM,mBAAmB,IAAI,gBAAgB;YAE7C,4BAA4B;YAC5B,iBAAiB,MAAM,CAAC,QAAQ,WAAW,IAAI,CAAC,QAAQ;YACxD,iBAAiB,MAAM,CAAC,YAAY,WAAW,QAAQ,CAAC,QAAQ;YAEhE,MAAM,cAAe,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CACtC,CAAC,uBAAuB,EAAE,iBAAiB,QAAQ,IAAI;YAEzD,uEAAuE;YACvE,OAAO,YAAY,IAAI,IAAI;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM,IAAI,MACR,CAAC,4BAA4B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAE7F;IACF;IAEA;;;;GAIC,GACD,MAAM,qBACJ,OAAyB,EACG;QAC5B,IAAI;YACF,MAAM,oBAAoB,IAAI,CAAC,gBAAgB,CAAC;YAChD,MAAM,cAAe,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CACtC,CAAC,8BAA8B,EAAE,mBAAmB;YAEtD,OAAO,YAAY,IAAI,IAAI;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM,IAAI,MACR,CAAC,mCAAmC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAEpG;IACF;IAEA;;;;GAIC,GACD,MAAM,mBACJ,OAAyB,EACG;QAC5B,IAAI;YACF,MAAM,cAAc,IAAI,CAAC,gBAAgB,CAAC;YAC1C,MAAM,cAAe,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CACtC,CAAC,6BAA6B,EAAE,aAAa;YAE/C,uEAAuE;YACvE,OAAO,CAAA,GAAA,2LAAA,CAAA,2BAAwB,AAAD,EAAE,YAAY,IAAI,IAAI;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM,IAAI,MACR,CAAC,iCAAiC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAElG;IACF;IAEA,uEAAuE;IAEvE;;;;GAIC,GACD,MAAM,sBACJ,OAAyB,EACI;QAC7B,IAAI;YACF,MAAM,oBAAoB,IAAI,CAAC,gBAAgB,CAAC;YAChD,MAAM,UAAW,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,0BAA0B,EAAE,mBAAmB;YAElD,OACE,QAAQ,IAAI,IAAI;gBACd,uBAAuB;gBACvB,YAAY,EAAE;gBACd,cAAc,EAAE;gBAChB,WAAW;YACb;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,IAAI,MACR,CAAC,8BAA8B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAE/F;IACF;IAEA;;;;GAIC,GACD,MAAM,kBACJ,OAAyB,EACM;QAC/B,IAAI;YACF,MAAM,oBAAoB,IAAI,CAAC,gBAAgB,CAAC;YAChD,MAAM,UAAW,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,4BAA4B,EAAE,mBAAmB;YAEpD,2CAA2C;YAC3C,OAAO,QAAQ,IAAI,IAAI,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM,IAAI,MACR,CAAC,gCAAgC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAEjG;IACF;IAEA;;;;GAIC,GACD,MAAM,iBAAiB,OAAyB,EAA0B;QACxE,IAAI;YACF,MAAM,oBAAoB,IAAI,CAAC,gBAAgB,CAAC;YAChD,MAAM,cAAc,IAAI,gBAAgB;YAExC,+BAA+B;YAC/B,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,GAAG;gBACvD,YAAY,MAAM,CAAC,cAAc,QAAQ,UAAU,CAAC,IAAI,CAAC;YAC3D;YACA,IAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,MAAM,GAAG,GAAG;gBAC3D,YAAY,MAAM,CAAC,gBAAgB,QAAQ,YAAY,CAAC,IAAI,CAAC;YAC/D;YAEA,MAAM,cAAe,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CACtC,CAAC,2BAA2B,EAAE,YAAY,QAAQ,IAAI;YAExD,OAAO,YAAY,IAAI,IAAI;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM,IAAI,MACR,CAAC,+BAA+B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAEhG;IACF;IAEA;;;;GAIC,GACD,MAAM,eAAe,aAAwB,EAAwB;QACnE,IAAI;YACF,MAAM,cAAc,eAAe,SAC/B,CAAC,cAAc,EAAE,cAAc,IAAI,CAAC,MAAM,GAC1C;YAEJ,MAAM,cAAe,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CACtC,CAAC,yBAAyB,EAAE,aAAa;YAE3C,uEAAuE;YACvE,OAAO,CAAA,GAAA,2LAAA,CAAA,uBAAoB,AAAD,EAAE,YAAY,IAAI,IAAI;QAClD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MACR,CAAC,6BAA6B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAE9F;IACF;IAEA;;;;GAIC,GACD,MAAM,aAAa,OAAyB,EAAwB;QAClE,IAAI;YACF,MAAM,cAAc,IAAI,CAAC,gBAAgB,CAAC;YAC1C,MAAM,cAAe,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CACtC,CAAC,kBAAkB,EAAE,aAAa;YAEpC,uEAAuE;YACvE,OAAO,CAAA,GAAA,2LAAA,CAAA,qBAAkB,AAAD,EAAE,YAAY,IAAI,IAAI;QAChD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,IAAI,MACR,CAAC,2BAA2B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAE5F;IACF;IAEA;;;;GAIC,GACD,MAAM,oBACJ,OAAyB,EACE;QAC3B,IAAI;YACF,MAAM,oBAAoB,IAAI,CAAC,gBAAgB,CAAC;YAChD,MAAM,cAAc,IAAI,gBAAgB;YAExC,kCAAkC;YAClC,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACnD,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC,IAAI,CAAC;YACvD;YACA,IAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,MAAM,GAAG,GAAG;gBAC3D,YAAY,MAAM,CAAC,gBAAgB,QAAQ,YAAY,CAAC,IAAI,CAAC;YAC/D;YACA,IAAI,QAAQ,aAAa,IAAI,QAAQ,aAAa,CAAC,MAAM,GAAG,GAAG;gBAC7D,YAAY,MAAM,CAAC,iBAAiB,QAAQ,aAAa,CAAC,IAAI,CAAC;YACjE;YAEA,MAAM,MAAM,CAAC,6BAA6B,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YACxG,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACpC,OAAO,OAAO,IAAI,IAAI;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM;QACR;IACF;IAEA;;;;;GAKC,GACD,AAAQ,kBACN,MAAuB,EACvB,GAAW,EACX,MAAa,EACP;QACN,IAAI,UAAU,OAAO,MAAM,GAAG,GAAG;YAC/B,OAAO,MAAM,CAAC,KAAK,OAAO,IAAI,CAAC;QACjC;IACF;IAEA;;;;;GAKC,GACD,AAAQ,iBAAiB,OAAyB,EAAU;QAC1D,MAAM,SAAS,IAAI;QAEnB,6DAA6D;QAC7D,uDAAuD;QACvD,IAAI;YACF,MAAM,WACJ,QAAQ,SAAS,CAAC,IAAI,YAAY,OAC9B,QAAQ,SAAS,CAAC,IAAI,GACtB,IAAI,KAAK,QAAQ,SAAS,CAAC,IAAI;YACrC,MAAM,SACJ,QAAQ,SAAS,CAAC,EAAE,YAAY,OAC5B,QAAQ,SAAS,CAAC,EAAE,GACpB,IAAI,KAAK,QAAQ,SAAS,CAAC,EAAE;YAEnC,gCAAgC;YAChC,IAAI,MAAM,SAAS,OAAO,OAAO,MAAM,OAAO,OAAO,KAAK;gBACxD,MAAM,IAAI,UAAU;YACtB;YAEA,OAAO,MAAM,CACX,kBACA,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,SAAS,WAAW;YAE9D,OAAO,MAAM,CACX,gBACA,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO,WAAW;QAE5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,gDAAgD;YAChD,MAAM,gBAAgB,IAAI;YAC1B,MAAM,kBAAkB,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;YAClE,OAAO,MAAM,CACX,kBACA,gBAAgB,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IACzC,gBAAgB,WAAW;YAE/B,OAAO,MAAM,CACX,gBACA,cAAc,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,cAAc,WAAW;QAE1E;QAEA,4DAA4D;QAC5D,IAAI,CAAC,iBAAiB,CAAC,QAAQ,UAAU,QAAQ,MAAM;QACvD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,aAAa,QAAQ,SAAS;QAC7D,IAAI,CAAC,iBAAiB,CAAC,QAAQ,aAAa,QAAQ,SAAS;QAC7D,IAAI,CAAC,iBAAiB,CAAC,QAAQ,YAAY,QAAQ,QAAQ;QAE3D,wCAAwC;QACxC,IAAI,QAAQ,UAAU,EAAE;YACtB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,cAAc,QAAQ,UAAU;QACjE;QACA,IAAI,QAAQ,YAAY,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,gBAAgB,QAAQ,YAAY;QACrE;QACA,IAAI,QAAQ,YAAY,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,gBAAgB,QAAQ,YAAY;QACrE;QACA,IAAI,QAAQ,aAAa,EAAE;YACzB,IAAI,CAAC,iBAAiB,CAAC,QAAQ,iBAAiB,QAAQ,aAAa;QACvE;QAEA,sBAAsB;QACtB,IAAI,QAAQ,SAAS,EAAE;YACrB,OAAO,MAAM,CAAC,WAAW,QAAQ,SAAS,CAAC,GAAG,CAAC,QAAQ;YACvD,OAAO,MAAM,CAAC,WAAW,QAAQ,SAAS,CAAC,GAAG,CAAC,QAAQ;QACzD;QAEA,OAAO,OAAO,QAAQ;IACxB;AACF;AAGO,MAAM,uBAAuB,IAAI", "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/data/hooks/useReportingData.ts"], "sourcesContent": ["/**\r\n * @file Reporting Data Hooks\r\n * @description Contains custom hooks for fetching and managing reporting data.\r\n */\r\n\r\nimport { useApiQuery, type ApiQueryOptions } from '@/hooks/api';\r\nimport { reportingDataService } from '../services/ReportingDataService';\r\n\r\nimport type {\r\n  DelegationAnalytics,\r\n  ReportingFilters,\r\n  TrendData,\r\n  LocationMetrics,\r\n} from '../types';\r\n\r\n/**\r\n * Hook to fetch delegation analytics data.\r\n * @param filters - The filters to apply to the query.\r\n * @param options - Optional query options.\r\n * @returns The result of the API query.\r\n */\r\nexport const useDelegationAnalytics = (\r\n  filters: ReportingFilters,\r\n  options: ApiQueryOptions<DelegationAnalytics> = {}\r\n) => {\r\n  return useApiQuery(\r\n    ['delegationAnalytics', filters],\r\n    () => reportingDataService.getDelegationAnalytics(filters),\r\n    options\r\n  );\r\n};\r\n\r\n/**\r\n * Hook to fetch task metrics.\r\n * @param delegationIds - The IDs of the delegations to fetch metrics for.\r\n * @param options - Optional query options.\r\n * @returns The result of the API query.\r\n */\r\nexport const useTaskMetrics = (\r\n  delegationIds: string[],\r\n  options: ApiQueryOptions<any> = {} // Replace 'any' with TaskMetrics type\r\n) => {\r\n  return useApiQuery(\r\n    ['taskMetrics', delegationIds],\r\n    () => reportingDataService.getTaskMetrics(delegationIds),\r\n    {\r\n      enabled: !!delegationIds && delegationIds.length > 0, // Only run if IDs are provided\r\n      ...options,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook to fetch trend data.\r\n * @param filters - The filters to apply to the query.\r\n * @param options - Optional query options.\r\n * @returns The result of the API query.\r\n */\r\nexport const useTrendData = (\r\n  filters: ReportingFilters,\r\n  options: ApiQueryOptions<TrendData[]> = {}\r\n) => {\r\n  return useApiQuery(\r\n    ['trendData', filters],\r\n    () => reportingDataService.getTrendData(filters),\r\n    options\r\n  );\r\n};\r\n\r\n/**\r\n * Hook to fetch location metrics.\r\n * @param filters - The filters to apply to the query.\r\n * @param options - Optional query options.\r\n * @returns The result of the API query.\r\n */\r\nexport const useLocationMetrics = (\r\n  filters: ReportingFilters,\r\n  options: ApiQueryOptions<LocationMetrics[]> = {}\r\n) => {\r\n  return useApiQuery(\r\n    ['locationMetrics', filters],\r\n    () => reportingDataService.getLocationMetrics(filters),\r\n    options\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;AAAA;AACA;;;AAeO,MAAM,yBAAyB,CACpC,SACA,UAAgD,CAAC,CAAC;IAElD,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAuB;KAAQ,EAChC,IAAM,sLAAA,CAAA,uBAAoB,CAAC,sBAAsB,CAAC,UAClD;AAEJ;AAQO,MAAM,iBAAiB,CAC5B,eACA,UAAgC,CAAC,EAAE,sCAAsC;AAAvC;IAElC,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAe;KAAc,EAC9B,IAAM,sLAAA,CAAA,uBAAoB,CAAC,cAAc,CAAC,gBAC1C;QACE,SAAS,CAAC,CAAC,iBAAiB,cAAc,MAAM,GAAG;QACnD,GAAG,OAAO;IACZ;AAEJ;AAQO,MAAM,eAAe,CAC1B,SACA,UAAwC,CAAC,CAAC;IAE1C,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAa;KAAQ,EACtB,IAAM,sLAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,UACxC;AAEJ;AAQO,MAAM,qBAAqB,CAChC,SACA,UAA8C,CAAC,CAAC;IAEhD,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAmB;KAAQ,EAC5B,IAAM,sLAAA,CAAA,uBAAoB,CAAC,kBAAkB,CAAC,UAC9C;AAEJ", "debugId": null}}, {"offset": {"line": 831, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/data/hooks/useDelegations.ts"], "sourcesContent": ["/**\r\n * @file useDelegations Hook\r\n * @description A hook for fetching paginated delegation data.\r\n */\r\n\r\nimport { useApiQuery } from '@/lib/hooks';\r\nimport { reportingDataService } from '../services/ReportingDataService';\r\nimport type { ReportingFilters, PaginatedDelegationsResponse } from '../types';\r\nimport { useQuery } from '@tanstack/react-query';\r\nimport { useMemo } from 'react';\r\nimport type { DelegationStatusPrisma } from '@/lib/types/domain';\r\n\r\n/**\r\n * Interface for delegation data returned by analytics endpoint\r\n * This matches the transformed structure from the backend\r\n */\r\ninterface AnalyticsDelegation {\r\n  id: number;\r\n  title: string; // Mapped from eventName\r\n  assignedTo: string; // Joined employee names\r\n  location: string;\r\n  status: DelegationStatusPrisma;\r\n  createdAt: string;\r\n  completedAt?: string;\r\n  actualHours?: number;\r\n  estimatedHours: number;\r\n  dueDate: string;\r\n  progress: number;\r\n  priority: string;\r\n  assignedBy: string;\r\n}\r\n\r\n/**\r\n * Fetches paginated delegations using the reporting service.\r\n * FIXED: Use analytics endpoint since /delegations endpoint doesn't exist\r\n *\r\n * @param {ReportingFilters} filters - The filters to apply to the query.\r\n * @param {{ page: number; pageSize: number }} pagination - The pagination parameters.\r\n * @param {object} [options] - Optional TanStack Query options.\r\n * @returns The result of the TanStack Query.\r\n */\r\nexport const useDelegations = (\r\n  filters: ReportingFilters,\r\n  pagination: { page: number; pageSize: number },\r\n  options?: object\r\n) => {\r\n  // FIXED: Use analytics endpoint which includes delegations data\r\n  const analyticsQuery = useApiQuery(\r\n    ['delegations', filters, pagination], // Keep same query key for consistency\r\n    () => reportingDataService.getDelegationAnalytics(filters),\r\n    {\r\n      placeholderData: prev => prev, // Smooth pagination experience\r\n      showErrorToast: true, // Show error toasts for debugging\r\n      ...options,\r\n    }\r\n  );\r\n\r\n  // Transform analytics response to match expected PaginatedDelegationsResponse format\r\n  const transformedData = useMemo(():\r\n    | PaginatedDelegationsResponse\r\n    | undefined => {\r\n    if (!analyticsQuery.data?.delegations) {\r\n      return undefined;\r\n    }\r\n\r\n    // Type assertion since we know the backend returns AnalyticsDelegation structure\r\n    const allDelegations = analyticsQuery.data\r\n      .delegations as unknown as AnalyticsDelegation[];\r\n    const startIndex = (pagination.page - 1) * pagination.pageSize;\r\n    const endIndex = startIndex + pagination.pageSize;\r\n    const paginatedDelegations = allDelegations.slice(startIndex, endIndex);\r\n\r\n    return {\r\n      data: paginatedDelegations.map(delegation => ({\r\n        id: delegation.id,\r\n        delegationId: delegation.id.toString(),\r\n        customerName: delegation.title, // Backend returns 'title' field (mapped from eventName)\r\n        vehicleModel: 'N/A', // TODO: Add vehicle info to analytics response\r\n        licensePlate: 'N/A', // TODO: Add license plate to analytics response\r\n        status: delegation.status,\r\n        assignedEmployee: delegation.assignedTo, // Backend returns 'assignedTo' field\r\n        location: delegation.location,\r\n        createdAt: delegation.createdAt,\r\n        completedAt: delegation.completedAt || null, // Fix type: convert undefined to null\r\n      })),\r\n      meta: {\r\n        total: allDelegations.length,\r\n        page: pagination.page,\r\n        pageSize: pagination.pageSize,\r\n        totalPages: Math.ceil(allDelegations.length / pagination.pageSize),\r\n      },\r\n    };\r\n  }, [analyticsQuery.data?.delegations, pagination.page, pagination.pageSize]);\r\n\r\n  return {\r\n    ...analyticsQuery,\r\n    data: transformedData,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAAA;AACA;AAGA;;;;AAgCO,MAAM,iBAAiB,CAC5B,SACA,YACA;IAEA,gEAAgE;IAChE,MAAM,iBAAiB,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAC/B;QAAC;QAAe;QAAS;KAAW,EACpC,IAAM,sLAAA,CAAA,uBAAoB,CAAC,sBAAsB,CAAC,UAClD;QACE,iBAAiB,CAAA,OAAQ;QACzB,gBAAgB;QAChB,GAAG,OAAO;IACZ;IAGF,qFAAqF;IACrF,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAG9B,IAAI,CAAC,eAAe,IAAI,EAAE,aAAa;YACrC,OAAO;QACT;QAEA,iFAAiF;QACjF,MAAM,iBAAiB,eAAe,IAAI,CACvC,WAAW;QACd,MAAM,aAAa,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,QAAQ;QAC9D,MAAM,WAAW,aAAa,WAAW,QAAQ;QACjD,MAAM,uBAAuB,eAAe,KAAK,CAAC,YAAY;QAE9D,OAAO;YACL,MAAM,qBAAqB,GAAG,CAAC,CAAA,aAAc,CAAC;oBAC5C,IAAI,WAAW,EAAE;oBACjB,cAAc,WAAW,EAAE,CAAC,QAAQ;oBACpC,cAAc,WAAW,KAAK;oBAC9B,cAAc;oBACd,cAAc;oBACd,QAAQ,WAAW,MAAM;oBACzB,kBAAkB,WAAW,UAAU;oBACvC,UAAU,WAAW,QAAQ;oBAC7B,WAAW,WAAW,SAAS;oBAC/B,aAAa,WAAW,WAAW,IAAI;gBACzC,CAAC;YACD,MAAM;gBACJ,OAAO,eAAe,MAAM;gBAC5B,MAAM,WAAW,IAAI;gBACrB,UAAU,WAAW,QAAQ;gBAC7B,YAAY,KAAK,IAAI,CAAC,eAAe,MAAM,GAAG,WAAW,QAAQ;YACnE;QACF;IACF,GAAG;QAAC,eAAe,IAAI,EAAE;QAAa,WAAW,IAAI;QAAE,WAAW,QAAQ;KAAC;IAE3E,OAAO;QACL,GAAG,cAAc;QACjB,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 901, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/data/hooks/useReportingQueries.ts"], "sourcesContent": ["// frontend/src/components/features/reporting/data/hooks/useReportingQueries.ts\r\n\r\nimport {\r\n  useQuery,\r\n  useQueries,\r\n  useQueryClient,\r\n  UseQueryOptions,\r\n  QueryKey,\r\n} from '@tanstack/react-query';\r\nimport { useCallback, useMemo } from 'react';\r\n// PHASE 1 ENHANCEMENT: Import new entity analytics types\r\nimport {\r\n  ReportingFilters,\r\n  DelegationAnalytics,\r\n  TaskMetrics,\r\n  TrendData,\r\n  LocationMetrics,\r\n  ServiceHistoryData,\r\n  ServiceCostSummary,\r\n  // PHASE 1: New entity analytics types\r\n  TaskAnalytics,\r\n  VehicleAnalytics,\r\n  EmployeeAnalytics,\r\n  CrossEntityAnalytics,\r\n} from '../types/reporting';\r\nimport { reportingDataService } from '../services/ReportingDataService';\r\n\r\n/**\r\n * Query Keys Factory - Centralized query key management following React Query best practices\r\n *\r\n * Follows SRP by having one responsibility: managing query keys for reporting\r\n */\r\nexport const reportingQueryKeys = {\r\n  all: ['reporting'] as const,\r\n  analytics: () => [...reportingQueryKeys.all, 'analytics'] as const,\r\n  delegationAnalytics: (filters: ReportingFilters) =>\r\n    [...reportingQueryKeys.analytics(), 'delegations', filters] as const,\r\n  taskMetrics: (delegationIds?: string[]) =>\r\n    [...reportingQueryKeys.all, 'tasks', 'metrics', delegationIds] as const,\r\n  trends: (filters: ReportingFilters) =>\r\n    [...reportingQueryKeys.all, 'trends', filters] as const,\r\n  locationMetrics: (filters: ReportingFilters) =>\r\n    [...reportingQueryKeys.all, 'locations', 'metrics', filters] as const,\r\n\r\n  // ENHANCED: Service history query keys\r\n  serviceHistory: (filters: ReportingFilters) =>\r\n    [...reportingQueryKeys.all, 'services', 'history', filters] as const,\r\n  serviceCosts: (filters: ReportingFilters) =>\r\n    [...reportingQueryKeys.all, 'services', 'costs', filters] as const,\r\n  \r\n  // PHASE 1: New entity analytics query keys\r\n  taskAnalytics: (filters: ReportingFilters) =>\r\n    [...reportingQueryKeys.analytics(), 'tasks', filters] as const,\r\n  vehicleAnalytics: (filters: ReportingFilters) =>\r\n    [...reportingQueryKeys.analytics(), 'vehicles', filters] as const,\r\n  employeeAnalytics: (filters: ReportingFilters) =>\r\n    [...reportingQueryKeys.analytics(), 'employees', filters] as const,\r\n  crossEntityAnalytics: (filters: ReportingFilters) =>\r\n    [...reportingQueryKeys.analytics(), 'cross-entity', filters] as const,\r\n} as const;\r\n\r\n/**\r\n * Hook for fetching delegation analytics with smart caching and error handling\r\n *\r\n * Follows Separation of Concerns: Only handles data fetching, not business logic or UI state\r\n *\r\n * @param filters - Reporting filters to apply\r\n * @param options - Additional React Query options\r\n * @returns Query result with delegation analytics data\r\n */\r\nexport const useDelegationAnalytics = (\r\n  filters: ReportingFilters,\r\n  options?: Omit<\r\n    UseQueryOptions<DelegationAnalytics, Error>,\r\n    'queryKey' | 'queryFn'\r\n  >\r\n) => {\r\n  const queryKey = useMemo(\r\n    () => reportingQueryKeys.delegationAnalytics(filters),\r\n    [filters]\r\n  );\r\n\r\n  const queryFn = useCallback(\r\n    () => reportingDataService.getDelegationAnalytics(filters),\r\n    [filters]\r\n  );\r\n\r\n  return useQuery({\r\n    queryKey,\r\n    queryFn,\r\n    staleTime: 5 * 60 * 1000, // 5 minutes - analytics data doesn't change frequently\r\n    gcTime: 10 * 60 * 1000, // 10 minutes garbage collection\r\n    retry: 3,\r\n    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),\r\n    refetchOnWindowFocus: false, // Prevent unnecessary refetches on window focus\r\n    refetchOnMount: true,\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for fetching task metrics with optimized caching\r\n *\r\n * @param delegationIds - Optional array of delegation IDs to filter by\r\n * @param options - Additional React Query options\r\n * @returns Query result with task metrics data\r\n */\r\nexport const useTaskMetrics = (\r\n  delegationIds?: string[],\r\n  options?: Omit<UseQueryOptions<TaskMetrics, Error>, 'queryKey' | 'queryFn'>\r\n) => {\r\n  const queryKey = useMemo(\r\n    () => reportingQueryKeys.taskMetrics(delegationIds),\r\n    [delegationIds]\r\n  );\r\n\r\n  const queryFn = useCallback(\r\n    () => reportingDataService.getTaskMetrics(delegationIds),\r\n    [delegationIds]\r\n  );\r\n\r\n  return useQuery({\r\n    queryKey,\r\n    queryFn,\r\n    staleTime: 2 * 60 * 1000, // 2 minutes - task metrics change more frequently\r\n    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection\r\n    retry: 3,\r\n    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),\r\n    refetchOnWindowFocus: false,\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for fetching trend data with smart caching\r\n *\r\n * @param filters - Reporting filters to apply\r\n * @param options - Additional React Query options\r\n * @returns Query result with trend data\r\n */\r\nexport const useTrendData = (\r\n  filters: ReportingFilters,\r\n  options?: Omit<UseQueryOptions<TrendData[], Error>, 'queryKey' | 'queryFn'>\r\n) => {\r\n  const queryKey = useMemo(() => reportingQueryKeys.trends(filters), [filters]);\r\n\r\n  const queryFn = useCallback(\r\n    () => reportingDataService.getTrendData(filters),\r\n    [filters]\r\n  );\r\n\r\n  return useQuery({\r\n    queryKey,\r\n    queryFn,\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n    gcTime: 10 * 60 * 1000, // 10 minutes\r\n    retry: 3,\r\n    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),\r\n    refetchOnWindowFocus: false,\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for fetching location metrics\r\n *\r\n * @param filters - Reporting filters to apply\r\n * @param options - Additional React Query options\r\n * @returns Query result with location metrics data\r\n */\r\nexport const useLocationMetrics = (\r\n  filters: ReportingFilters,\r\n  options?: Omit<\r\n    UseQueryOptions<LocationMetrics[], Error>,\r\n    'queryKey' | 'queryFn'\r\n  >\r\n) => {\r\n  const queryKey = useMemo(\r\n    () => reportingQueryKeys.locationMetrics(filters),\r\n    [filters]\r\n  );\r\n\r\n  const queryFn = useCallback(\r\n    () => reportingDataService.getLocationMetrics(filters),\r\n    [filters]\r\n  );\r\n\r\n  return useQuery({\r\n    queryKey,\r\n    queryFn,\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n    gcTime: 10 * 60 * 1000, // 10 minutes\r\n    retry: 3,\r\n    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),\r\n    refetchOnWindowFocus: false,\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * Parallel queries hook for fetching multiple reporting data sets simultaneously\r\n *\r\n * Follows DRY principle by reusing individual query configurations\r\n * Optimizes performance by fetching data in parallel rather than sequentially\r\n *\r\n * @param filters - Reporting filters to apply\r\n * @param delegationIds - Optional delegation IDs for task metrics\r\n * @returns Array of query results for all reporting data\r\n */\r\nexport const useReportingData = (\r\n  filters: ReportingFilters,\r\n  delegationIds?: string[]\r\n) => {\r\n  return useQueries({\r\n    queries: [\r\n      {\r\n        queryKey: reportingQueryKeys.delegationAnalytics(filters),\r\n        queryFn: () => reportingDataService.getDelegationAnalytics(filters),\r\n        staleTime: 5 * 60 * 1000,\r\n        gcTime: 10 * 60 * 1000,\r\n        retry: 3,\r\n        retryDelay: (attemptIndex: number) =>\r\n          Math.min(1000 * 2 ** attemptIndex, 30000),\r\n      },\r\n      {\r\n        queryKey: reportingQueryKeys.taskMetrics(delegationIds),\r\n        queryFn: () => reportingDataService.getTaskMetrics(delegationIds),\r\n        staleTime: 2 * 60 * 1000,\r\n        gcTime: 5 * 60 * 1000,\r\n        retry: 3,\r\n        retryDelay: (attemptIndex: number) =>\r\n          Math.min(1000 * 2 ** attemptIndex, 30000),\r\n      },\r\n      {\r\n        queryKey: reportingQueryKeys.trends(filters),\r\n        queryFn: () => reportingDataService.getTrendData(filters),\r\n        staleTime: 5 * 60 * 1000,\r\n        gcTime: 10 * 60 * 1000,\r\n        retry: 3,\r\n        retryDelay: (attemptIndex: number) =>\r\n          Math.min(1000 * 2 ** attemptIndex, 30000),\r\n      },\r\n      {\r\n        queryKey: reportingQueryKeys.locationMetrics(filters),\r\n        queryFn: () => reportingDataService.getLocationMetrics(filters),\r\n        staleTime: 5 * 60 * 1000,\r\n        gcTime: 10 * 60 * 1000,\r\n        retry: 3,\r\n        retryDelay: (attemptIndex: number) =>\r\n          Math.min(1000 * 2 ** attemptIndex, 30000),\r\n      },\r\n    ],\r\n  });\r\n};\r\n\r\n/**\r\n * Hook for prefetching reporting data to improve user experience\r\n *\r\n * @param filters - Reporting filters to prefetch data for\r\n * @param delegationIds - Optional delegation IDs for task metrics\r\n */\r\nexport const usePrefetchReportingData = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  const prefetchDelegationAnalytics = useCallback(\r\n    (filters: ReportingFilters) => {\r\n      return queryClient.prefetchQuery({\r\n        queryKey: reportingQueryKeys.delegationAnalytics(filters),\r\n        queryFn: () => reportingDataService.getDelegationAnalytics(filters),\r\n        staleTime: 5 * 60 * 1000,\r\n      });\r\n    },\r\n    [queryClient]\r\n  );\r\n\r\n  const prefetchTaskMetrics = useCallback(\r\n    (delegationIds?: string[]) => {\r\n      return queryClient.prefetchQuery({\r\n        queryKey: reportingQueryKeys.taskMetrics(delegationIds),\r\n        queryFn: () => reportingDataService.getTaskMetrics(delegationIds),\r\n        staleTime: 2 * 60 * 1000,\r\n      });\r\n    },\r\n    [queryClient]\r\n  );\r\n\r\n  return {\r\n    prefetchDelegationAnalytics,\r\n    prefetchTaskMetrics,\r\n  };\r\n};\r\n\r\n/**\r\n * ENHANCED: Hook for fetching service history data\r\n *\r\n * @param filters - Reporting filters to apply\r\n * @param options - Additional React Query options\r\n * @returns Query result with service history data\r\n */\r\nexport const useServiceHistory = (\r\n  filters: ReportingFilters,\r\n  options?: Omit<\r\n    UseQueryOptions<ServiceHistoryData[], Error>,\r\n    'queryKey' | 'queryFn'\r\n  >\r\n) => {\r\n  const queryKey = useMemo(\r\n    () => reportingQueryKeys.serviceHistory(filters),\r\n    [filters]\r\n  );\r\n\r\n  const queryFn = useCallback(\r\n    () => reportingDataService.getServiceHistory(filters),\r\n    [filters]\r\n  );\r\n\r\n  return useQuery({\r\n    queryKey,\r\n    queryFn,\r\n    staleTime: 3 * 60 * 1000, // 3 minutes - service history changes moderately\r\n    gcTime: 8 * 60 * 1000, // 8 minutes garbage collection\r\n    retry: 3,\r\n    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),\r\n    refetchOnWindowFocus: false,\r\n    enabled: !!filters.includeServiceHistory, // Only fetch when service history is requested\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * PHASE 1 ENHANCEMENT: Hook for fetching task analytics data\r\n * Following existing patterns for consistency\r\n *\r\n * @param filters - Reporting filters to apply\r\n * @param options - Additional React Query options\r\n * @returns Query result with task analytics data\r\n */\r\nexport const useTaskAnalytics = (\r\n  filters: ReportingFilters,\r\n  options?: Omit<UseQueryOptions<TaskAnalytics, Error>, 'queryKey' | 'queryFn'>\r\n) => {\r\n  const queryKey = useMemo(\r\n    () => reportingQueryKeys.taskAnalytics(filters),\r\n    [filters]\r\n  );\r\n\r\n  const queryFn = useCallback(\r\n    () => reportingDataService.getTaskAnalytics(filters),\r\n    [filters]\r\n  );\r\n\r\n  return useQuery({\r\n    queryKey,\r\n    queryFn,\r\n    staleTime: 3 * 60 * 1000, // 3 minutes - task analytics change moderately\r\n    gcTime: 8 * 60 * 1000, // 8 minutes garbage collection\r\n    retry: 3,\r\n    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),\r\n    refetchOnWindowFocus: false,\r\n    enabled: !!filters.includeTaskData, // Only fetch when task data is requested\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * PHASE 1 ENHANCEMENT: Hook for fetching vehicle analytics data\r\n *\r\n * @param filters - Reporting filters to apply\r\n * @param options - Additional React Query options\r\n * @returns Query result with vehicle analytics data\r\n */\r\nexport const useVehicleAnalytics = (\r\n  filters: ReportingFilters,\r\n  options?: Omit<UseQueryOptions<VehicleAnalytics, Error>, 'queryKey' | 'queryFn'>\r\n) => {\r\n  const queryKey = useMemo(\r\n    () => reportingQueryKeys.vehicleAnalytics(filters),\r\n    [filters]\r\n  );\r\n\r\n  const queryFn = useCallback(\r\n    () => reportingDataService.getVehicleAnalytics(filters),\r\n    [filters]\r\n  );\r\n\r\n  return useQuery({\r\n    queryKey,\r\n    queryFn,\r\n    staleTime: 5 * 60 * 1000, // 5 minutes - vehicle analytics are relatively stable\r\n    gcTime: 10 * 60 * 1000, // 10 minutes garbage collection\r\n    retry: 3,\r\n    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),\r\n    refetchOnWindowFocus: false,\r\n    enabled: !!filters.includeVehicleAnalytics, // Only fetch when vehicle analytics is requested\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * PHASE 1 ENHANCEMENT: Hook for fetching employee analytics data\r\n *\r\n * @param filters - Reporting filters to apply\r\n * @param options - Additional React Query options\r\n * @returns Query result with employee analytics data\r\n */\r\nexport const useEmployeeAnalytics = (\r\n  filters: ReportingFilters,\r\n  options?: Omit<UseQueryOptions<EmployeeAnalytics, Error>, 'queryKey' | 'queryFn'>\r\n) => {\r\n  const queryKey = useMemo(\r\n    () => reportingQueryKeys.employeeAnalytics(filters),\r\n    [filters]\r\n  );\r\n\r\n  const queryFn = useCallback(\r\n    () => reportingDataService.getEmployeeAnalytics(filters),\r\n    [filters]\r\n  );\r\n\r\n  return useQuery({\r\n    queryKey,\r\n    queryFn,\r\n    staleTime: 4 * 60 * 1000, // 4 minutes - employee analytics change moderately\r\n    gcTime: 9 * 60 * 1000, // 9 minutes garbage collection\r\n    retry: 3,\r\n    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),\r\n    refetchOnWindowFocus: false,\r\n    enabled: !!filters.includeEmployeeMetrics, // Only fetch when employee metrics is requested\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * PHASE 1 ENHANCEMENT: Hook for fetching cross-entity analytics data\r\n *\r\n * @param filters - Reporting filters to apply\r\n * @param options - Additional React Query options\r\n * @returns Query result with cross-entity analytics data\r\n */\r\nexport const useCrossEntityAnalytics = (\r\n  filters: ReportingFilters,\r\n  options?: Omit<UseQueryOptions<CrossEntityAnalytics, Error>, 'queryKey' | 'queryFn'>\r\n) => {\r\n  const queryKey = useMemo(\r\n    () => reportingQueryKeys.crossEntityAnalytics(filters),\r\n    [filters]\r\n  );\r\n\r\n  const queryFn = useCallback(\r\n    () => reportingDataService.getCrossEntityAnalytics(filters),\r\n    [filters]\r\n  );\r\n\r\n  return useQuery({\r\n    queryKey,\r\n    queryFn,\r\n    staleTime: 6 * 60 * 1000, // 6 minutes - cross-entity analytics are complex and stable\r\n    gcTime: 12 * 60 * 1000, // 12 minutes garbage collection\r\n    retry: 3,\r\n    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),\r\n    refetchOnWindowFocus: false,\r\n    enabled: !!(\r\n      filters.includeCrossEntityCorrelations ||\r\n      filters.includeTaskData ||\r\n      filters.includeEmployeeMetrics ||\r\n      filters.includeVehicleAnalytics\r\n    ), // Only fetch when cross-entity data is requested\r\n    ...options,\r\n  });\r\n};\r\n\r\n/**\r\n * ENHANCED: Hook for fetching service cost summary\r\n *\r\n * @param filters - Reporting filters to apply\r\n * @param options - Additional React Query options\r\n * @returns Query result with service cost summary\r\n */\r\nexport const useServiceCostSummary = (\r\n  filters: ReportingFilters,\r\n  options?: Omit<\r\n    UseQueryOptions<ServiceCostSummary, Error>,\r\n    'queryKey' | 'queryFn'\r\n  >\r\n) => {\r\n  const queryKey = useMemo(\r\n    () => reportingQueryKeys.serviceCosts(filters),\r\n    [filters]\r\n  );\r\n\r\n  const queryFn = useCallback(\r\n    () => reportingDataService.getServiceCostSummary(filters),\r\n    [filters]\r\n  );\r\n\r\n  return useQuery({\r\n    queryKey,\r\n    queryFn,\r\n    staleTime: 5 * 60 * 1000, // 5 minutes - cost data is relatively stable\r\n    gcTime: 10 * 60 * 1000, // 10 minutes garbage collection\r\n    retry: 3,\r\n    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),\r\n    refetchOnWindowFocus: false,\r\n    enabled: !!filters.includeServiceHistory, // Only fetch when service history is requested\r\n    ...options,\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA,+EAA+E;;;;;;;;;;;;;;;;AAE/E;AAAA;AAAA;AAOA;AAgBA;;;;AAOO,MAAM,qBAAqB;IAChC,KAAK;QAAC;KAAY;IAClB,WAAW,IAAM;eAAI,mBAAmB,GAAG;YAAE;SAAY;IACzD,qBAAqB,CAAC,UACpB;eAAI,mBAAmB,SAAS;YAAI;YAAe;SAAQ;IAC7D,aAAa,CAAC,gBACZ;eAAI,mBAAmB,GAAG;YAAE;YAAS;YAAW;SAAc;IAChE,QAAQ,CAAC,UACP;eAAI,mBAAmB,GAAG;YAAE;YAAU;SAAQ;IAChD,iBAAiB,CAAC,UAChB;eAAI,mBAAmB,GAAG;YAAE;YAAa;YAAW;SAAQ;IAE9D,uCAAuC;IACvC,gBAAgB,CAAC,UACf;eAAI,mBAAmB,GAAG;YAAE;YAAY;YAAW;SAAQ;IAC7D,cAAc,CAAC,UACb;eAAI,mBAAmB,GAAG;YAAE;YAAY;YAAS;SAAQ;IAE3D,2CAA2C;IAC3C,eAAe,CAAC,UACd;eAAI,mBAAmB,SAAS;YAAI;YAAS;SAAQ;IACvD,kBAAkB,CAAC,UACjB;eAAI,mBAAmB,SAAS;YAAI;YAAY;SAAQ;IAC1D,mBAAmB,CAAC,UAClB;eAAI,mBAAmB,SAAS;YAAI;YAAa;SAAQ;IAC3D,sBAAsB,CAAC,UACrB;eAAI,mBAAmB,SAAS;YAAI;YAAgB;SAAQ;AAChE;AAWO,MAAM,yBAAyB,CACpC,SACA;IAKA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACrB,IAAM,mBAAmB,mBAAmB,CAAC,UAC7C;QAAC;KAAQ;IAGX,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxB,IAAM,sLAAA,CAAA,uBAAoB,CAAC,sBAAsB,CAAC,UAClD;QAAC;KAAQ;IAGX,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;QAClB,OAAO;QACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAC/D,sBAAsB;QACtB,gBAAgB;QAChB,GAAG,OAAO;IACZ;AACF;AASO,MAAM,iBAAiB,CAC5B,eACA;IAEA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACrB,IAAM,mBAAmB,WAAW,CAAC,gBACrC;QAAC;KAAc;IAGjB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxB,IAAM,sLAAA,CAAA,uBAAoB,CAAC,cAAc,CAAC,gBAC1C;QAAC;KAAc;IAGjB,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,WAAW,IAAI,KAAK;QACpB,QAAQ,IAAI,KAAK;QACjB,OAAO;QACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAC/D,sBAAsB;QACtB,GAAG,OAAO;IACZ;AACF;AASO,MAAM,eAAe,CAC1B,SACA;IAEA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,mBAAmB,MAAM,CAAC,UAAU;QAAC;KAAQ;IAE5E,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxB,IAAM,sLAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,UACxC;QAAC;KAAQ;IAGX,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;QAClB,OAAO;QACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAC/D,sBAAsB;QACtB,GAAG,OAAO;IACZ;AACF;AASO,MAAM,qBAAqB,CAChC,SACA;IAKA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACrB,IAAM,mBAAmB,eAAe,CAAC,UACzC;QAAC;KAAQ;IAGX,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxB,IAAM,sLAAA,CAAA,uBAAoB,CAAC,kBAAkB,CAAC,UAC9C;QAAC;KAAQ;IAGX,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;QAClB,OAAO;QACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAC/D,sBAAsB;QACtB,GAAG,OAAO;IACZ;AACF;AAYO,MAAM,mBAAmB,CAC9B,SACA;IAEA,OAAO,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE;QAChB,SAAS;YACP;gBACE,UAAU,mBAAmB,mBAAmB,CAAC;gBACjD,SAAS,IAAM,sLAAA,CAAA,uBAAoB,CAAC,sBAAsB,CAAC;gBAC3D,WAAW,IAAI,KAAK;gBACpB,QAAQ,KAAK,KAAK;gBAClB,OAAO;gBACP,YAAY,CAAC,eACX,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;YACvC;YACA;gBACE,UAAU,mBAAmB,WAAW,CAAC;gBACzC,SAAS,IAAM,sLAAA,CAAA,uBAAoB,CAAC,cAAc,CAAC;gBACnD,WAAW,IAAI,KAAK;gBACpB,QAAQ,IAAI,KAAK;gBACjB,OAAO;gBACP,YAAY,CAAC,eACX,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;YACvC;YACA;gBACE,UAAU,mBAAmB,MAAM,CAAC;gBACpC,SAAS,IAAM,sLAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC;gBACjD,WAAW,IAAI,KAAK;gBACpB,QAAQ,KAAK,KAAK;gBAClB,OAAO;gBACP,YAAY,CAAC,eACX,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;YACvC;YACA;gBACE,UAAU,mBAAmB,eAAe,CAAC;gBAC7C,SAAS,IAAM,sLAAA,CAAA,uBAAoB,CAAC,kBAAkB,CAAC;gBACvD,WAAW,IAAI,KAAK;gBACpB,QAAQ,KAAK,KAAK;gBAClB,OAAO;gBACP,YAAY,CAAC,eACX,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;YACvC;SACD;IACH;AACF;AAQO,MAAM,2BAA2B;IACtC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,8BAA8B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5C,CAAC;QACC,OAAO,YAAY,aAAa,CAAC;YAC/B,UAAU,mBAAmB,mBAAmB,CAAC;YACjD,SAAS,IAAM,sLAAA,CAAA,uBAAoB,CAAC,sBAAsB,CAAC;YAC3D,WAAW,IAAI,KAAK;QACtB;IACF,GACA;QAAC;KAAY;IAGf,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACpC,CAAC;QACC,OAAO,YAAY,aAAa,CAAC;YAC/B,UAAU,mBAAmB,WAAW,CAAC;YACzC,SAAS,IAAM,sLAAA,CAAA,uBAAoB,CAAC,cAAc,CAAC;YACnD,WAAW,IAAI,KAAK;QACtB;IACF,GACA;QAAC;KAAY;IAGf,OAAO;QACL;QACA;IACF;AACF;AASO,MAAM,oBAAoB,CAC/B,SACA;IAKA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACrB,IAAM,mBAAmB,cAAc,CAAC,UACxC;QAAC;KAAQ;IAGX,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxB,IAAM,sLAAA,CAAA,uBAAoB,CAAC,iBAAiB,CAAC,UAC7C;QAAC;KAAQ;IAGX,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,WAAW,IAAI,KAAK;QACpB,QAAQ,IAAI,KAAK;QACjB,OAAO;QACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAC/D,sBAAsB;QACtB,SAAS,CAAC,CAAC,QAAQ,qBAAqB;QACxC,GAAG,OAAO;IACZ;AACF;AAUO,MAAM,mBAAmB,CAC9B,SACA;IAEA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACrB,IAAM,mBAAmB,aAAa,CAAC,UACvC;QAAC;KAAQ;IAGX,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxB,IAAM,sLAAA,CAAA,uBAAoB,CAAC,gBAAgB,CAAC,UAC5C;QAAC;KAAQ;IAGX,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,WAAW,IAAI,KAAK;QACpB,QAAQ,IAAI,KAAK;QACjB,OAAO;QACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAC/D,sBAAsB;QACtB,SAAS,CAAC,CAAC,QAAQ,eAAe;QAClC,GAAG,OAAO;IACZ;AACF;AASO,MAAM,sBAAsB,CACjC,SACA;IAEA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACrB,IAAM,mBAAmB,gBAAgB,CAAC,UAC1C;QAAC;KAAQ;IAGX,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxB,IAAM,sLAAA,CAAA,uBAAoB,CAAC,mBAAmB,CAAC,UAC/C;QAAC;KAAQ;IAGX,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;QAClB,OAAO;QACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAC/D,sBAAsB;QACtB,SAAS,CAAC,CAAC,QAAQ,uBAAuB;QAC1C,GAAG,OAAO;IACZ;AACF;AASO,MAAM,uBAAuB,CAClC,SACA;IAEA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACrB,IAAM,mBAAmB,iBAAiB,CAAC,UAC3C;QAAC;KAAQ;IAGX,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxB,IAAM,sLAAA,CAAA,uBAAoB,CAAC,oBAAoB,CAAC,UAChD;QAAC;KAAQ;IAGX,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,WAAW,IAAI,KAAK;QACpB,QAAQ,IAAI,KAAK;QACjB,OAAO;QACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAC/D,sBAAsB;QACtB,SAAS,CAAC,CAAC,QAAQ,sBAAsB;QACzC,GAAG,OAAO;IACZ;AACF;AASO,MAAM,0BAA0B,CACrC,SACA;IAEA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACrB,IAAM,mBAAmB,oBAAoB,CAAC,UAC9C;QAAC;KAAQ;IAGX,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxB,IAAM,sLAAA,CAAA,uBAAoB,CAAC,uBAAuB,CAAC,UACnD;QAAC;KAAQ;IAGX,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;QAClB,OAAO;QACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAC/D,sBAAsB;QACtB,SAAS,CAAC,CAAC,CACT,QAAQ,8BAA8B,IACtC,QAAQ,eAAe,IACvB,QAAQ,sBAAsB,IAC9B,QAAQ,uBAAuB,AACjC;QACA,GAAG,OAAO;IACZ;AACF;AASO,MAAM,wBAAwB,CACnC,SACA;IAKA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EACrB,IAAM,mBAAmB,YAAY,CAAC,UACtC;QAAC;KAAQ;IAGX,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxB,IAAM,sLAAA,CAAA,uBAAoB,CAAC,qBAAqB,CAAC,UACjD;QAAC;KAAQ;IAGX,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd;QACA;QACA,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;QAClB,OAAO;QACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;QAC/D,sBAAsB;QACtB,SAAS,CAAC,CAAC,QAAQ,qBAAqB;QACxC,GAAG,OAAO;IACZ;AACF", "debugId": null}}, {"offset": {"line": 1246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/data/hooks/useRealtimeReportingUpdates.ts"], "sourcesContent": ["// frontend/src/components/features/reporting/data/hooks/useRealtimeReportingUpdates.ts\r\n\r\n'use client';\r\n\r\nimport { useEffect, useRef, useCallback, useState } from 'react';\r\nimport { useQueryClient } from '@tanstack/react-query';\r\nimport { getEnvironmentConfig } from '../../../../../lib/config/environment';\r\nimport { reportingQueryKeys } from './useReportingQueries';\r\nimport { ReportingFilters } from '../types/reporting';\r\n\r\n/**\r\n * WebSocket connection states for better UX feedback\r\n */\r\nexport enum WebSocketConnectionState {\r\n  CONNECTING = 'connecting',\r\n  CONNECTED = 'connected',\r\n  DISCONNECTED = 'disconnected',\r\n  RECONNECTING = 'reconnecting',\r\n  ERROR = 'error',\r\n}\r\n\r\n/**\r\n * Real-time update event types for reporting data\r\n */\r\nexport interface ReportingUpdateEvent {\r\n  type:\r\n    | 'delegation-updated'\r\n    | 'task-updated'\r\n    | 'analytics-refresh'\r\n    | 'location-updated';\r\n  data: {\r\n    delegationId?: string;\r\n    taskId?: string;\r\n    affectedFilters?: Partial<ReportingFilters>;\r\n    timestamp: string;\r\n  };\r\n}\r\n\r\n/**\r\n * Configuration options for the WebSocket connection\r\n */\r\nexport interface WebSocketConfig {\r\n  url?: string;\r\n  reconnectInterval?: number;\r\n  maxReconnectAttempts?: number;\r\n  heartbeatInterval?: number;\r\n  batchUpdateDelay?: number;\r\n}\r\n\r\n/**\r\n * Hook for managing real-time reporting updates via WebSocket\r\n *\r\n * Follows SRP: Only responsible for WebSocket connection management and real-time updates\r\n * Does NOT handle initial data fetching or UI state - that's handled by React Query hooks\r\n *\r\n * @param filters - Current reporting filters to subscribe to relevant updates\r\n * @param config - WebSocket configuration options\r\n * @returns Connection state and control functions\r\n */\r\nexport const useRealtimeReportingUpdates = (\r\n  filters: ReportingFilters,\r\n  config: WebSocketConfig = {}\r\n) => {\r\n  const queryClient = useQueryClient();\r\n  const wsRef = useRef<WebSocket | null>(null);\r\n  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);\r\n  const updateBatchRef = useRef<Set<string>>(new Set());\r\n  const batchTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  // Configuration with defaults using environment-aware configuration\r\n  const envConfig = getEnvironmentConfig();\r\n  const {\r\n    url = `${envConfig.wsUrl}/ws/reporting`,\r\n    reconnectInterval = 3000,\r\n    maxReconnectAttempts = 5,\r\n    heartbeatInterval = 30000,\r\n    batchUpdateDelay = 1000,\r\n  } = config;\r\n\r\n  // Connection state management\r\n  const [connectionState, setConnectionState] =\r\n    useState<WebSocketConnectionState>(WebSocketConnectionState.DISCONNECTED);\r\n  const [reconnectAttempts, setReconnectAttempts] = useState(0);\r\n  const [lastError, setLastError] = useState<string | null>(null);\r\n\r\n  /**\r\n   * Batched cache invalidation to prevent excessive re-renders\r\n   * Collects multiple updates and processes them together\r\n   */\r\n  const processBatchedUpdates = useCallback(() => {\r\n    if (updateBatchRef.current.size === 0) return;\r\n\r\n    const updates = Array.from(updateBatchRef.current);\r\n    updateBatchRef.current.clear();\r\n\r\n    // Invalidate relevant queries based on batched updates\r\n    const queryKeysToInvalidate = new Set<string>();\r\n\r\n    updates.forEach(updateType => {\r\n      switch (updateType) {\r\n        case 'delegation-updated':\r\n          queryKeysToInvalidate.add('delegation-analytics');\r\n          queryKeysToInvalidate.add('trends');\r\n          queryKeysToInvalidate.add('location-metrics');\r\n          break;\r\n        case 'task-updated':\r\n          queryKeysToInvalidate.add('task-metrics');\r\n          break;\r\n        case 'analytics-refresh':\r\n          queryKeysToInvalidate.add('delegation-analytics');\r\n          queryKeysToInvalidate.add('trends');\r\n          queryKeysToInvalidate.add('location-metrics');\r\n          queryKeysToInvalidate.add('task-metrics');\r\n          break;\r\n        case 'location-updated':\r\n          queryKeysToInvalidate.add('location-metrics');\r\n          break;\r\n      }\r\n    });\r\n\r\n    // Invalidate queries efficiently\r\n    queryKeysToInvalidate.forEach(queryType => {\r\n      switch (queryType) {\r\n        case 'delegation-analytics':\r\n          queryClient.invalidateQueries({\r\n            queryKey: reportingQueryKeys.analytics(),\r\n          });\r\n          break;\r\n        case 'task-metrics':\r\n          queryClient.invalidateQueries({\r\n            queryKey: [...reportingQueryKeys.all, 'tasks', 'metrics'],\r\n          });\r\n          break;\r\n        case 'trends':\r\n          queryClient.invalidateQueries({\r\n            queryKey: [...reportingQueryKeys.all, 'trends'],\r\n          });\r\n          break;\r\n        case 'location-metrics':\r\n          queryClient.invalidateQueries({\r\n            queryKey: [...reportingQueryKeys.all, 'locations', 'metrics'],\r\n          });\r\n          break;\r\n      }\r\n    });\r\n  }, [queryClient]);\r\n\r\n  /**\r\n   * Handles incoming WebSocket messages with proper error handling\r\n   */\r\n  const handleMessage = useCallback(\r\n    (event: MessageEvent) => {\r\n      try {\r\n        const message: ReportingUpdateEvent = JSON.parse(event.data);\r\n\r\n        // Add to batch for processing\r\n        updateBatchRef.current.add(message.type);\r\n\r\n        // Clear existing batch timeout and set new one\r\n        if (batchTimeoutRef.current) {\r\n          clearTimeout(batchTimeoutRef.current);\r\n        }\r\n\r\n        batchTimeoutRef.current = setTimeout(() => {\r\n          processBatchedUpdates();\r\n        }, batchUpdateDelay);\r\n      } catch (error) {\r\n        console.error('Failed to parse WebSocket message:', error);\r\n      }\r\n    },\r\n    [processBatchedUpdates, batchUpdateDelay]\r\n  );\r\n\r\n  /**\r\n   * Establishes WebSocket connection with proper error handling\r\n   */\r\n  const connect = useCallback(() => {\r\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\r\n      return; // Already connected\r\n    }\r\n\r\n    setConnectionState(WebSocketConnectionState.CONNECTING);\r\n    setLastError(null);\r\n\r\n    try {\r\n      const ws = new WebSocket(url);\r\n      wsRef.current = ws;\r\n\r\n      ws.onopen = () => {\r\n        setConnectionState(WebSocketConnectionState.CONNECTED);\r\n        setReconnectAttempts(0);\r\n\r\n        // Send subscription message with current filters\r\n        ws.send(\r\n          JSON.stringify({\r\n            type: 'subscribe',\r\n            filters: filters,\r\n            timestamp: new Date().toISOString(),\r\n          })\r\n        );\r\n\r\n        // Start heartbeat\r\n        heartbeatIntervalRef.current = setInterval(() => {\r\n          if (ws.readyState === WebSocket.OPEN) {\r\n            ws.send(JSON.stringify({ type: 'ping' }));\r\n          }\r\n        }, heartbeatInterval);\r\n      };\r\n\r\n      ws.onmessage = handleMessage;\r\n\r\n      ws.onclose = event => {\r\n        setConnectionState(WebSocketConnectionState.DISCONNECTED);\r\n\r\n        // Clear heartbeat\r\n        if (heartbeatIntervalRef.current) {\r\n          clearInterval(heartbeatIntervalRef.current);\r\n          heartbeatIntervalRef.current = null;\r\n        }\r\n\r\n        // Attempt reconnection if not a clean close\r\n        if (!event.wasClean && reconnectAttempts < maxReconnectAttempts) {\r\n          setConnectionState(WebSocketConnectionState.RECONNECTING);\r\n          setReconnectAttempts(prev => prev + 1);\r\n\r\n          reconnectTimeoutRef.current = setTimeout(() => {\r\n            connect();\r\n          }, reconnectInterval);\r\n        }\r\n      };\r\n\r\n      ws.onerror = error => {\r\n        console.error('WebSocket error:', error);\r\n        setConnectionState(WebSocketConnectionState.ERROR);\r\n        setLastError('WebSocket connection failed');\r\n      };\r\n    } catch (error) {\r\n      console.error('Failed to create WebSocket connection:', error);\r\n      setConnectionState(WebSocketConnectionState.ERROR);\r\n      setLastError(\r\n        error instanceof Error ? error.message : 'Unknown connection error'\r\n      );\r\n    }\r\n  }, [\r\n    url,\r\n    filters,\r\n    handleMessage,\r\n    reconnectAttempts,\r\n    maxReconnectAttempts,\r\n    reconnectInterval,\r\n    heartbeatInterval,\r\n  ]);\r\n\r\n  /**\r\n   * Cleanly disconnects WebSocket connection\r\n   */\r\n  const disconnect = useCallback(() => {\r\n    // Clear all timeouts\r\n    if (reconnectTimeoutRef.current) {\r\n      clearTimeout(reconnectTimeoutRef.current);\r\n      reconnectTimeoutRef.current = null;\r\n    }\r\n\r\n    if (heartbeatIntervalRef.current) {\r\n      clearInterval(heartbeatIntervalRef.current);\r\n      heartbeatIntervalRef.current = null;\r\n    }\r\n\r\n    if (batchTimeoutRef.current) {\r\n      clearTimeout(batchTimeoutRef.current);\r\n      batchTimeoutRef.current = null;\r\n    }\r\n\r\n    // Close WebSocket connection\r\n    if (wsRef.current) {\r\n      wsRef.current.close(1000, 'Component unmounting');\r\n      wsRef.current = null;\r\n    }\r\n\r\n    setConnectionState(WebSocketConnectionState.DISCONNECTED);\r\n    setReconnectAttempts(0);\r\n    setLastError(null);\r\n  }, []);\r\n\r\n  /**\r\n   * Manually trigger reconnection\r\n   */\r\n  const reconnect = useCallback(() => {\r\n    disconnect();\r\n    setReconnectAttempts(0);\r\n    setTimeout(connect, 100); // Small delay to ensure clean disconnect\r\n  }, [disconnect, connect]);\r\n\r\n  // Auto-connect on mount and filter changes\r\n  useEffect(() => {\r\n    connect();\r\n    return disconnect;\r\n  }, [connect, disconnect]);\r\n\r\n  // Update subscription when filters change\r\n  useEffect(() => {\r\n    if (wsRef.current?.readyState === WebSocket.OPEN) {\r\n      wsRef.current.send(\r\n        JSON.stringify({\r\n          type: 'update-subscription',\r\n          filters: filters,\r\n          timestamp: new Date().toISOString(),\r\n        })\r\n      );\r\n    }\r\n  }, [filters]);\r\n\r\n  return {\r\n    connectionState,\r\n    reconnectAttempts,\r\n    lastError,\r\n    connect,\r\n    disconnect,\r\n    reconnect,\r\n    isConnected: connectionState === WebSocketConnectionState.CONNECTED,\r\n    isConnecting: connectionState === WebSocketConnectionState.CONNECTING,\r\n    isReconnecting: connectionState === WebSocketConnectionState.RECONNECTING,\r\n    hasError: connectionState === WebSocketConnectionState.ERROR,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA,uFAAuF;;;;;AAIvF;AACA;AACA;AACA;AALA;;;;;AAWO,IAAA,AAAK,kDAAA;;;;;;WAAA;;AA8CL,MAAM,8BAA8B,CACzC,SACA,SAA0B,CAAC,CAAC;IAE5B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IACvC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAC1D,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAC3D,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe,IAAI;IAC/C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAEtD,oEAAoE;IACpE,MAAM,YAAY,CAAA,GAAA,mIAAA,CAAA,uBAAoB,AAAD;IACrC,MAAM,EACJ,MAAM,GAAG,UAAU,KAAK,CAAC,aAAa,CAAC,EACvC,oBAAoB,IAAI,EACxB,uBAAuB,CAAC,EACxB,oBAAoB,KAAK,EACzB,mBAAmB,IAAI,EACxB,GAAG;IAEJ,8BAA8B;IAC9B,MAAM,CAAC,iBAAiB,mBAAmB,GACzC,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IACT,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D;;;GAGC,GACD,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxC,IAAI,eAAe,OAAO,CAAC,IAAI,KAAK,GAAG;QAEvC,MAAM,UAAU,MAAM,IAAI,CAAC,eAAe,OAAO;QACjD,eAAe,OAAO,CAAC,KAAK;QAE5B,uDAAuD;QACvD,MAAM,wBAAwB,IAAI;QAElC,QAAQ,OAAO,CAAC,CAAA;YACd,OAAQ;gBACN,KAAK;oBACH,sBAAsB,GAAG,CAAC;oBAC1B,sBAAsB,GAAG,CAAC;oBAC1B,sBAAsB,GAAG,CAAC;oBAC1B;gBACF,KAAK;oBACH,sBAAsB,GAAG,CAAC;oBAC1B;gBACF,KAAK;oBACH,sBAAsB,GAAG,CAAC;oBAC1B,sBAAsB,GAAG,CAAC;oBAC1B,sBAAsB,GAAG,CAAC;oBAC1B,sBAAsB,GAAG,CAAC;oBAC1B;gBACF,KAAK;oBACH,sBAAsB,GAAG,CAAC;oBAC1B;YACJ;QACF;QAEA,iCAAiC;QACjC,sBAAsB,OAAO,CAAC,CAAA;YAC5B,OAAQ;gBACN,KAAK;oBACH,YAAY,iBAAiB,CAAC;wBAC5B,UAAU,kLAAA,CAAA,qBAAkB,CAAC,SAAS;oBACxC;oBACA;gBACF,KAAK;oBACH,YAAY,iBAAiB,CAAC;wBAC5B,UAAU;+BAAI,kLAAA,CAAA,qBAAkB,CAAC,GAAG;4BAAE;4BAAS;yBAAU;oBAC3D;oBACA;gBACF,KAAK;oBACH,YAAY,iBAAiB,CAAC;wBAC5B,UAAU;+BAAI,kLAAA,CAAA,qBAAkB,CAAC,GAAG;4BAAE;yBAAS;oBACjD;oBACA;gBACF,KAAK;oBACH,YAAY,iBAAiB,CAAC;wBAC5B,UAAU;+BAAI,kLAAA,CAAA,qBAAkB,CAAC,GAAG;4BAAE;4BAAa;yBAAU;oBAC/D;oBACA;YACJ;QACF;IACF,GAAG;QAAC;KAAY;IAEhB;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACC,IAAI;YACF,MAAM,UAAgC,KAAK,KAAK,CAAC,MAAM,IAAI;YAE3D,8BAA8B;YAC9B,eAAe,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI;YAEvC,+CAA+C;YAC/C,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,aAAa,gBAAgB,OAAO;YACtC;YAEA,gBAAgB,OAAO,GAAG,WAAW;gBACnC;YACF,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF,GACA;QAAC;QAAuB;KAAiB;IAG3C;;GAEC,GACD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,IAAI,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI,EAAE;YAChD,QAAQ,oBAAoB;QAC9B;QAEA;QACA,aAAa;QAEb,IAAI;YACF,MAAM,KAAK,IAAI,UAAU;YACzB,MAAM,OAAO,GAAG;YAEhB,GAAG,MAAM,GAAG;gBACV;gBACA,qBAAqB;gBAErB,iDAAiD;gBACjD,GAAG,IAAI,CACL,KAAK,SAAS,CAAC;oBACb,MAAM;oBACN,SAAS;oBACT,WAAW,IAAI,OAAO,WAAW;gBACnC;gBAGF,kBAAkB;gBAClB,qBAAqB,OAAO,GAAG,YAAY;oBACzC,IAAI,GAAG,UAAU,KAAK,UAAU,IAAI,EAAE;wBACpC,GAAG,IAAI,CAAC,KAAK,SAAS,CAAC;4BAAE,MAAM;wBAAO;oBACxC;gBACF,GAAG;YACL;YAEA,GAAG,SAAS,GAAG;YAEf,GAAG,OAAO,GAAG,CAAA;gBACX;gBAEA,kBAAkB;gBAClB,IAAI,qBAAqB,OAAO,EAAE;oBAChC,cAAc,qBAAqB,OAAO;oBAC1C,qBAAqB,OAAO,GAAG;gBACjC;gBAEA,4CAA4C;gBAC5C,IAAI,CAAC,MAAM,QAAQ,IAAI,oBAAoB,sBAAsB;oBAC/D;oBACA,qBAAqB,CAAA,OAAQ,OAAO;oBAEpC,oBAAoB,OAAO,GAAG,WAAW;wBACvC;oBACF,GAAG;gBACL;YACF;YAEA,GAAG,OAAO,GAAG,CAAA;gBACX,QAAQ,KAAK,CAAC,oBAAoB;gBAClC;gBACA,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD;YACA,aACE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAE7C;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED;;GAEC,GACD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,qBAAqB;QACrB,IAAI,oBAAoB,OAAO,EAAE;YAC/B,aAAa,oBAAoB,OAAO;YACxC,oBAAoB,OAAO,GAAG;QAChC;QAEA,IAAI,qBAAqB,OAAO,EAAE;YAChC,cAAc,qBAAqB,OAAO;YAC1C,qBAAqB,OAAO,GAAG;QACjC;QAEA,IAAI,gBAAgB,OAAO,EAAE;YAC3B,aAAa,gBAAgB,OAAO;YACpC,gBAAgB,OAAO,GAAG;QAC5B;QAEA,6BAA6B;QAC7B,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM;YAC1B,MAAM,OAAO,GAAG;QAClB;QAEA;QACA,qBAAqB;QACrB,aAAa;IACf,GAAG,EAAE;IAEL;;GAEC,GACD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B;QACA,qBAAqB;QACrB,WAAW,SAAS,MAAM,yCAAyC;IACrE,GAAG;QAAC;QAAY;KAAQ;IAExB,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,OAAO;IACT,GAAG;QAAC;QAAS;KAAW;IAExB,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,OAAO,EAAE,eAAe,UAAU,IAAI,EAAE;YAChD,MAAM,OAAO,CAAC,IAAI,CAChB,KAAK,SAAS,CAAC;gBACb,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI,OAAO,WAAW;YACnC;QAEJ;IACF,GAAG;QAAC;KAAQ;IAEZ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 1511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/data/hooks/index.ts"], "sourcesContent": ["// Export specific hooks to avoid conflicts\r\nexport {\r\n  useDelegationAnalytics,\r\n  useTaskMetrics,\r\n  useTrendData,\r\n  useLocationMetrics,\r\n} from './useReportingData';\r\nexport { useDelegations } from './useDelegations';\r\nexport { useRealtimeReportingUpdates } from './useRealtimeReportingUpdates';\r\n\r\n// Export unique hooks from useReportingQueries\r\nexport {\r\n  useDelegationAnalytics as useDelegationAnalyticsQuery,\r\n  useLocationMetrics as useLocationMetricsQuery,\r\n  useTaskMetrics as useTaskMetricsQuery,\r\n  useTrendData as useTrendDataQuery,\r\n} from './useReportingQueries';\r\n"], "names": [], "mappings": "AAAA,2CAA2C;;AAC3C;AAMA;AACA;AAEA,+CAA+C;AAC/C", "debugId": null}}, {"offset": {"line": 1550, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/data/stores/useReportingFiltersStore.ts"], "sourcesContent": ["// frontend/src/components/features/reporting/data/stores/useReportingFiltersStore.ts\r\n\r\nimport { create } from 'zustand';\r\nimport { createJSONStorage, persist } from 'zustand/middleware';\r\nimport { devtools } from 'zustand/middleware';\r\nimport { subscribeWithSelector } from 'zustand/middleware';\r\nimport { useShallow } from 'zustand/react/shallow';\r\nimport { shallow } from 'zustand/shallow';\r\n\r\nimport type {\r\n  DelegationStatusPrisma,\r\n  ReportingFilters,\r\n  TaskPriorityPrisma,\r\n  TaskStatusPrisma,\r\n} from '../types/reporting';\r\nimport type {\r\n  ServiceStatusPrisma,\r\n  ServiceTypePrisma,\r\n} from '../types/vehicleService';\r\n\r\nimport { ServicePriorityPrisma } from '../types/vehicleService';\r\n\r\n/**\r\n * Reporting Filters Store Actions Interface\r\n *\r\n * Follows SRP by grouping related actions together\r\n */\r\ninterface ReportingFiltersActions {\r\n  applyFilters: () => void;\r\n  // Presets\r\n  applyPreset: (presetName: string) => void;\r\n  clearValidationErrors: () => void;\r\n  deletePreset: (name: string) => void;\r\n  getPresets: () => Record<string, ReportingFilters>;\r\n\r\n  resetFilters: () => void;\r\n  revertChanges: () => void;\r\n  saveAsPreset: (name: string) => void;\r\n  setCostRange: (min: number, max: number) => void;\r\n\r\n  // Filter value setters\r\n  setDateRange: (from: Date, to: Date) => void;\r\n  setEmployees: (employees: number[]) => void;\r\n  setFilterPanelOpen: (open: boolean) => void;\r\n\r\n  // Bulk operations\r\n  setFilters: (filters: Partial<ReportingFilters>) => void;\r\n  setIncludeServiceHistory: (include: boolean) => void;\r\n  setIncludeTaskData: (include: boolean) => void;\r\n  setLocations: (locations: string[]) => void;\r\n\r\n  setServiceStatus: (status: ServiceStatusPrisma[]) => void;\r\n  // ENHANCED: Service history filter setters\r\n  setServiceTypes: (types: ServiceTypePrisma[]) => void;\r\n\r\n  setStatus: (status: DelegationStatusPrisma[]) => void;\r\n  setTaskPriorities: (priorities: TaskPriorityPrisma[]) => void;\r\n\r\n  // ENHANCED: Task filter setters\r\n  setTaskStatus: (status: TaskStatusPrisma[]) => void;\r\n  setVehicles: (vehicles: number[]) => void;\r\n  // UI state management\r\n  toggleFilterPanel: () => void;\r\n  // Validation\r\n  validateFilters: () => boolean;\r\n}\r\n\r\n/**\r\n * Reporting Filters Store State Interface\r\n *\r\n * Follows Interface Segregation Principle by defining focused state structure\r\n */\r\ninterface ReportingFiltersState {\r\n  // Current filter values\r\n  filters: ReportingFilters;\r\n\r\n  hasUnsavedChanges: boolean;\r\n  // UI state for filter management\r\n  isFilterPanelOpen: boolean;\r\n  isValid: boolean;\r\n\r\n  lastAppliedFilters: ReportingFilters;\r\n  // Validation state\r\n  validationErrors: Record<string, string>;\r\n}\r\n\r\n/**\r\n * Complete store type combining state and actions\r\n */\r\ntype ReportingFiltersStore = ReportingFiltersActions & ReportingFiltersState;\r\n\r\n/**\r\n * Default filter values following business requirements\r\n * FIXED: Create stable default filters to prevent infinite re-renders\r\n * Use a function that creates fresh dates only when needed\r\n */\r\nconst getDefaultFilters = (): ReportingFilters => {\r\n  const now = new Date();\r\n  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\r\n\r\n  return {\r\n    costRange: { max: 10_000, min: 0 },\r\n    dateRange: {\r\n      from: thirtyDaysAgo,\r\n      to: now,\r\n    },\r\n    employees: [],\r\n    includeServiceHistory: false,\r\n    includeTaskData: false,\r\n\r\n    locations: [],\r\n    serviceStatus: [],\r\n    // ENHANCED: Default service history values\r\n    serviceTypes: [],\r\n    status: [], // All statuses by default\r\n    vehicles: [],\r\n  };\r\n};\r\n\r\n/**\r\n * FIXED: Create stable default filters object to prevent infinite re-renders\r\n * Only create new dates when absolutely necessary\r\n */\r\nconst createStableDefaultFilters = (): ReportingFilters => {\r\n  const now = new Date();\r\n  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\r\n\r\n  return {\r\n    costRange: { max: 10_000, min: 0 },\r\n    dateRange: {\r\n      from: thirtyDaysAgo,\r\n      to: now,\r\n    },\r\n    employees: [],\r\n    includeServiceHistory: false,\r\n    includeTaskData: false,\r\n    locations: [],\r\n    serviceStatus: [],\r\n    serviceTypes: [],\r\n    status: [],\r\n    vehicles: [],\r\n  };\r\n};\r\n\r\n// FIXED: Create stable default filters instance\r\nconst STABLE_DEFAULT_FILTERS = createStableDefaultFilters();\r\n\r\n/**\r\n * Filter validation logic\r\n *\r\n * @param filters - Filters to validate\r\n * @returns Validation errors object\r\n */\r\nconst validateFiltersLogic = (\r\n  filters: ReportingFilters\r\n): Record<string, string> => {\r\n  const errors: Record<string, string> = {};\r\n\r\n  // Date range validation\r\n  if (filters.dateRange) {\r\n    if (filters.dateRange.from > filters.dateRange.to) {\r\n      errors.dateRange = 'Start date must be before end date';\r\n    }\r\n\r\n    const daysDiff =\r\n      Math.abs(\r\n        filters.dateRange.to.getTime() - filters.dateRange.from.getTime()\r\n      ) /\r\n      (1000 * 60 * 60 * 24);\r\n    if (daysDiff > 365) {\r\n      errors.dateRange = 'Date range cannot exceed 365 days';\r\n    }\r\n  }\r\n\r\n  // Status validation\r\n  if (filters.status.length > 10) {\r\n    errors.status = 'Too many statuses selected (maximum 10)';\r\n  }\r\n\r\n  // Location validation\r\n  if (filters.locations.length > 50) {\r\n    errors.locations = 'Too many locations selected (maximum 50)';\r\n  }\r\n\r\n  // Employee validation\r\n  if (filters.employees.length > 100) {\r\n    errors.employees = 'Too many employees selected (maximum 100)';\r\n  }\r\n\r\n  // Vehicle validation\r\n  if (filters.vehicles.length > 100) {\r\n    errors.vehicles = 'Too many vehicles selected (maximum 100)';\r\n  }\r\n\r\n  // ENHANCED: Service history validation\r\n  if (filters.serviceTypes && filters.serviceTypes.length > 20) {\r\n    errors.serviceTypes = 'Too many service types selected (maximum 20)';\r\n  }\r\n\r\n  if (filters.serviceStatus && filters.serviceStatus.length > 10) {\r\n    errors.serviceStatus = 'Too many service statuses selected (maximum 10)';\r\n  }\r\n\r\n  if (filters.costRange) {\r\n    if (filters.costRange.min < 0) {\r\n      errors.costRange = 'Minimum cost cannot be negative';\r\n    }\r\n    if (filters.costRange.min >= filters.costRange.max) {\r\n      errors.costRange = 'Minimum cost must be less than maximum cost';\r\n    }\r\n    if (filters.costRange.max > 1_000_000) {\r\n      errors.costRange = 'Maximum cost cannot exceed $1,000,000';\r\n    }\r\n  }\r\n\r\n  return errors;\r\n};\r\n\r\n/**\r\n * Zustand store for managing reporting filters state\r\n *\r\n * Follows SRP: Only responsible for filter state management\r\n * Uses middleware for persistence, devtools, and subscriptions\r\n *\r\n * Features:\r\n * - Persistent storage with localStorage\r\n * - URL synchronization (handled by separate hook)\r\n * - Validation with error tracking\r\n * - Preset management\r\n * - Optimistic updates with revert capability\r\n */\r\nexport const useReportingFiltersStore = create<ReportingFiltersStore>()(\r\n  devtools(\r\n    subscribeWithSelector(\r\n      persist(\r\n        (set, get) => ({\r\n          applyFilters: () => {\r\n            const { filters, isValid } = get();\r\n            if (isValid) {\r\n              set({\r\n                hasUnsavedChanges: false,\r\n                lastAppliedFilters: { ...filters },\r\n              });\r\n            }\r\n          },\r\n          // Preset management\r\n          applyPreset: (presetName: string) => {\r\n            try {\r\n              const stored = localStorage.getItem('reporting-filter-presets');\r\n              const presets = stored ? JSON.parse(stored) : {};\r\n              const preset = presets[presetName];\r\n\r\n              if (preset) {\r\n                set(state => ({\r\n                  filters: { ...preset },\r\n                  hasUnsavedChanges: true,\r\n                  lastAppliedFilters: state.lastAppliedFilters,\r\n                }));\r\n              }\r\n            } catch (error) {\r\n              console.error('Failed to apply preset:', error);\r\n            }\r\n          },\r\n          clearValidationErrors: () => {\r\n            set({\r\n              isValid: true,\r\n              validationErrors: {},\r\n            });\r\n          },\r\n          deletePreset: (name: string) => {\r\n            try {\r\n              const stored = localStorage.getItem('reporting-filter-presets');\r\n              const presets = stored ? JSON.parse(stored) : {};\r\n              delete presets[name];\r\n              localStorage.setItem(\r\n                'reporting-filter-presets',\r\n                JSON.stringify(presets)\r\n              );\r\n            } catch (error) {\r\n              console.error('Failed to delete preset:', error);\r\n            }\r\n          },\r\n          // Initial state\r\n          filters: getDefaultFilters(),\r\n          getPresets: () => {\r\n            try {\r\n              const stored = localStorage.getItem('reporting-filter-presets');\r\n              return stored ? JSON.parse(stored) : {};\r\n            } catch {\r\n              return {};\r\n            }\r\n          },\r\n\r\n          hasUnsavedChanges: false,\r\n\r\n          isFilterPanelOpen: false,\r\n\r\n          isValid: true,\r\n\r\n          lastAppliedFilters: getDefaultFilters(),\r\n\r\n          resetFilters: () => {\r\n            set({\r\n              filters: getDefaultFilters(),\r\n              hasUnsavedChanges: true,\r\n              isValid: true,\r\n              validationErrors: {},\r\n            });\r\n          },\r\n\r\n          revertChanges: () => {\r\n            const { lastAppliedFilters } = get();\r\n            set({\r\n              filters: { ...lastAppliedFilters },\r\n              hasUnsavedChanges: false,\r\n              isValid: true,\r\n              validationErrors: {},\r\n            });\r\n          },\r\n\r\n          saveAsPreset: (name: string) => {\r\n            try {\r\n              const { filters } = get();\r\n              const stored = localStorage.getItem('reporting-filter-presets');\r\n              const presets = stored ? JSON.parse(stored) : {};\r\n              presets[name] = { ...filters };\r\n              localStorage.setItem(\r\n                'reporting-filter-presets',\r\n                JSON.stringify(presets)\r\n              );\r\n            } catch (error) {\r\n              console.error('Failed to save preset:', error);\r\n            }\r\n          },\r\n\r\n          setCostRange: (min: number, max: number) => {\r\n            set(state => {\r\n              const newFilters = { ...state.filters, costRange: { max, min } };\r\n              const errors = validateFiltersLogic(newFilters);\r\n\r\n              return {\r\n                filters: newFilters,\r\n                hasUnsavedChanges: true,\r\n                isValid: Object.keys(errors).length === 0,\r\n                validationErrors: errors,\r\n              };\r\n            });\r\n          },\r\n\r\n          // Filter value setters\r\n          setDateRange: (from: Date, to: Date) => {\r\n            set(state => {\r\n              const newFilters = {\r\n                ...state.filters,\r\n                dateRange: { from, to },\r\n              };\r\n              const errors = validateFiltersLogic(newFilters);\r\n\r\n              return {\r\n                filters: newFilters,\r\n                hasUnsavedChanges: true,\r\n                isValid: Object.keys(errors).length === 0,\r\n                validationErrors: errors,\r\n              };\r\n            });\r\n          },\r\n\r\n          setEmployees: (employees: number[]) => {\r\n            set(state => {\r\n              const newFilters = { ...state.filters, employees };\r\n              const errors = validateFiltersLogic(newFilters);\r\n\r\n              return {\r\n                filters: newFilters,\r\n                hasUnsavedChanges: true,\r\n                isValid: Object.keys(errors).length === 0,\r\n                validationErrors: errors,\r\n              };\r\n            });\r\n          },\r\n\r\n          setFilterPanelOpen: (open: boolean) => {\r\n            set({ isFilterPanelOpen: open });\r\n          },\r\n\r\n          // Bulk operations\r\n          setFilters: (partialFilters: Partial<ReportingFilters>) => {\r\n            set(state => {\r\n              const newFilters = { ...state.filters, ...partialFilters };\r\n              const errors = validateFiltersLogic(newFilters);\r\n\r\n              return {\r\n                filters: newFilters,\r\n                hasUnsavedChanges: true,\r\n                isValid: Object.keys(errors).length === 0,\r\n                validationErrors: errors,\r\n              };\r\n            });\r\n          },\r\n\r\n          setIncludeServiceHistory: (includeServiceHistory: boolean) => {\r\n            set(state => {\r\n              const newFilters = { ...state.filters, includeServiceHistory };\r\n              const errors = validateFiltersLogic(newFilters);\r\n\r\n              return {\r\n                filters: newFilters,\r\n                hasUnsavedChanges: true,\r\n                isValid: Object.keys(errors).length === 0,\r\n                validationErrors: errors,\r\n              };\r\n            });\r\n          },\r\n\r\n          setIncludeTaskData: (includeTaskData: boolean) => {\r\n            set(state => {\r\n              const newFilters = { ...state.filters, includeTaskData };\r\n              const errors = validateFiltersLogic(newFilters);\r\n\r\n              return {\r\n                filters: newFilters,\r\n                hasUnsavedChanges: true,\r\n                isValid: Object.keys(errors).length === 0,\r\n                validationErrors: errors,\r\n              };\r\n            });\r\n          },\r\n\r\n          setLocations: (locations: string[]) => {\r\n            set(state => {\r\n              const newFilters = { ...state.filters, locations };\r\n              const errors = validateFiltersLogic(newFilters);\r\n\r\n              return {\r\n                filters: newFilters,\r\n                hasUnsavedChanges: true,\r\n                isValid: Object.keys(errors).length === 0,\r\n                validationErrors: errors,\r\n              };\r\n            });\r\n          },\r\n\r\n          setServiceStatus: (serviceStatus: ServiceStatusPrisma[]) => {\r\n            set(state => {\r\n              const newFilters = { ...state.filters, serviceStatus };\r\n              const errors = validateFiltersLogic(newFilters);\r\n\r\n              return {\r\n                filters: newFilters,\r\n                hasUnsavedChanges: true,\r\n                isValid: Object.keys(errors).length === 0,\r\n                validationErrors: errors,\r\n              };\r\n            });\r\n          },\r\n\r\n          // ENHANCED: Service history filter setters\r\n          setServiceTypes: (serviceTypes: ServiceTypePrisma[]) => {\r\n            set(state => {\r\n              const newFilters = { ...state.filters, serviceTypes };\r\n              const errors = validateFiltersLogic(newFilters);\r\n\r\n              return {\r\n                filters: newFilters,\r\n                hasUnsavedChanges: true,\r\n                isValid: Object.keys(errors).length === 0,\r\n                validationErrors: errors,\r\n              };\r\n            });\r\n          },\r\n\r\n          setStatus: (status: DelegationStatusPrisma[]) => {\r\n            set(state => {\r\n              const newFilters = { ...state.filters, status };\r\n              const errors = validateFiltersLogic(newFilters);\r\n\r\n              return {\r\n                filters: newFilters,\r\n                hasUnsavedChanges: true,\r\n                isValid: Object.keys(errors).length === 0,\r\n                validationErrors: errors,\r\n              };\r\n            });\r\n          },\r\n\r\n          setTaskPriorities: (taskPriorities: TaskPriorityPrisma[]) => {\r\n            set(state => {\r\n              const newFilters = { ...state.filters, taskPriorities };\r\n              const errors = validateFiltersLogic(newFilters);\r\n\r\n              return {\r\n                filters: newFilters,\r\n                hasUnsavedChanges: true,\r\n                isValid: Object.keys(errors).length === 0,\r\n                validationErrors: errors,\r\n              };\r\n            });\r\n          },\r\n\r\n          // ENHANCED: Task filter setters\r\n          setTaskStatus: (taskStatus: TaskStatusPrisma[]) => {\r\n            set(state => {\r\n              const newFilters = { ...state.filters, taskStatus };\r\n              const errors = validateFiltersLogic(newFilters);\r\n\r\n              return {\r\n                filters: newFilters,\r\n                hasUnsavedChanges: true,\r\n                isValid: Object.keys(errors).length === 0,\r\n                validationErrors: errors,\r\n              };\r\n            });\r\n          },\r\n\r\n          setVehicles: (vehicles: number[]) => {\r\n            set(state => {\r\n              const newFilters = { ...state.filters, vehicles };\r\n              const errors = validateFiltersLogic(newFilters);\r\n\r\n              return {\r\n                filters: newFilters,\r\n                hasUnsavedChanges: true,\r\n                isValid: Object.keys(errors).length === 0,\r\n                validationErrors: errors,\r\n              };\r\n            });\r\n          },\r\n\r\n          // UI state management\r\n          toggleFilterPanel: () => {\r\n            set(state => ({\r\n              isFilterPanelOpen: !state.isFilterPanelOpen,\r\n            }));\r\n          },\r\n\r\n          // Validation\r\n          validateFilters: () => {\r\n            const { filters } = get();\r\n            const errors = validateFiltersLogic(filters);\r\n            const isValid = Object.keys(errors).length === 0;\r\n\r\n            set({\r\n              isValid,\r\n              validationErrors: errors,\r\n            });\r\n\r\n            return isValid;\r\n          },\r\n\r\n          validationErrors: {},\r\n        }),\r\n        {\r\n          name: 'reporting-filters-storage',\r\n          partialize: state => ({\r\n            // Only persist essential filter state, not UI state\r\n            filters: state.filters,\r\n            lastAppliedFilters: state.lastAppliedFilters,\r\n          }),\r\n          storage: createJSONStorage(() => localStorage),\r\n        }\r\n      )\r\n    ),\r\n    {\r\n      name: 'reporting-filters-store',\r\n    }\r\n  )\r\n);\r\n\r\n/**\r\n * Selector hooks for optimized component subscriptions\r\n *\r\n * Follows DRY principle by providing reusable selectors\r\n * FIXED: Create stable selectors to prevent infinite re-renders\r\n */\r\n\r\n// FIXED: Create stable selector functions to prevent infinite loops\r\nconst filtersSelector = (state: ReportingFiltersStore) => state.filters;\r\nconst actionsSelector = (state: ReportingFiltersStore) => ({\r\n  applyFilters: state.applyFilters,\r\n  resetFilters: state.resetFilters,\r\n  revertChanges: state.revertChanges,\r\n  setCostRange: state.setCostRange,\r\n  setDateRange: state.setDateRange,\r\n  setEmployees: state.setEmployees,\r\n  setFilters: state.setFilters,\r\n  setIncludeServiceHistory: state.setIncludeServiceHistory,\r\n  setIncludeTaskData: state.setIncludeTaskData,\r\n  setLocations: state.setLocations,\r\n  setServiceStatus: state.setServiceStatus,\r\n  setServiceTypes: state.setServiceTypes,\r\n  setStatus: state.setStatus,\r\n  setTaskPriorities: state.setTaskPriorities,\r\n  setTaskStatus: state.setTaskStatus,\r\n  setVehicles: state.setVehicles,\r\n});\r\n\r\nconst uiSelector = (state: ReportingFiltersStore) => ({\r\n  hasUnsavedChanges: state.hasUnsavedChanges,\r\n  isFilterPanelOpen: state.isFilterPanelOpen,\r\n  setFilterPanelOpen: state.setFilterPanelOpen,\r\n  toggleFilterPanel: state.toggleFilterPanel,\r\n});\r\n\r\nconst validationSelector = (state: ReportingFiltersStore) => ({\r\n  clearValidationErrors: state.clearValidationErrors,\r\n  isValid: state.isValid,\r\n  validateFilters: state.validateFilters,\r\n  validationErrors: state.validationErrors,\r\n});\r\n\r\nconst presetsSelector = (state: ReportingFiltersStore) => ({\r\n  applyPreset: state.applyPreset,\r\n  deletePreset: state.deletePreset,\r\n  getPresets: state.getPresets,\r\n  saveAsPreset: state.saveAsPreset,\r\n});\r\n\r\nexport const useReportingFilters = () =>\r\n  useReportingFiltersStore(useShallow(filtersSelector));\r\n\r\nexport const useReportingFiltersActions = () =>\r\n  useReportingFiltersStore(useShallow(actionsSelector));\r\n\r\nexport const useReportingFiltersUI = () =>\r\n  useReportingFiltersStore(useShallow(uiSelector));\r\n\r\nexport const useReportingFiltersValidation = () =>\r\n  useReportingFiltersStore(useShallow(validationSelector));\r\n\r\nexport const useReportingFiltersPresets = () =>\r\n  useReportingFiltersStore(useShallow(presetsSelector));\r\n"], "names": [], "mappings": "AAAA,qFAAqF;;;;;;;;;AAErF;AACA;AAGA;;;;;;AAqFA;;;;CAIC,GACD,MAAM,oBAAoB;IACxB,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;IAEnE,OAAO;QACL,WAAW;YAAE,KAAK;YAAQ,KAAK;QAAE;QACjC,WAAW;YACT,MAAM;YACN,IAAI;QACN;QACA,WAAW,EAAE;QACb,uBAAuB;QACvB,iBAAiB;QAEjB,WAAW,EAAE;QACb,eAAe,EAAE;QACjB,2CAA2C;QAC3C,cAAc,EAAE;QAChB,QAAQ,EAAE;QACV,UAAU,EAAE;IACd;AACF;AAEA;;;CAGC,GACD,MAAM,6BAA6B;IACjC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;IAEnE,OAAO;QACL,WAAW;YAAE,KAAK;YAAQ,KAAK;QAAE;QACjC,WAAW;YACT,MAAM;YACN,IAAI;QACN;QACA,WAAW,EAAE;QACb,uBAAuB;QACvB,iBAAiB;QACjB,WAAW,EAAE;QACb,eAAe,EAAE;QACjB,cAAc,EAAE;QAChB,QAAQ,EAAE;QACV,UAAU,EAAE;IACd;AACF;AAEA,gDAAgD;AAChD,MAAM,yBAAyB;AAE/B;;;;;CAKC,GACD,MAAM,uBAAuB,CAC3B;IAEA,MAAM,SAAiC,CAAC;IAExC,wBAAwB;IACxB,IAAI,QAAQ,SAAS,EAAE;QACrB,IAAI,QAAQ,SAAS,CAAC,IAAI,GAAG,QAAQ,SAAS,CAAC,EAAE,EAAE;YACjD,OAAO,SAAS,GAAG;QACrB;QAEA,MAAM,WACJ,KAAK,GAAG,CACN,QAAQ,SAAS,CAAC,EAAE,CAAC,OAAO,KAAK,QAAQ,SAAS,CAAC,IAAI,CAAC,OAAO,MAEjE,CAAC,OAAO,KAAK,KAAK,EAAE;QACtB,IAAI,WAAW,KAAK;YAClB,OAAO,SAAS,GAAG;QACrB;IACF;IAEA,oBAAoB;IACpB,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IAAI;QAC9B,OAAO,MAAM,GAAG;IAClB;IAEA,sBAAsB;IACtB,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,IAAI;QACjC,OAAO,SAAS,GAAG;IACrB;IAEA,sBAAsB;IACtB,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,KAAK;QAClC,OAAO,SAAS,GAAG;IACrB;IAEA,qBAAqB;IACrB,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,KAAK;QACjC,OAAO,QAAQ,GAAG;IACpB;IAEA,uCAAuC;IACvC,IAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,MAAM,GAAG,IAAI;QAC5D,OAAO,YAAY,GAAG;IACxB;IAEA,IAAI,QAAQ,aAAa,IAAI,QAAQ,aAAa,CAAC,MAAM,GAAG,IAAI;QAC9D,OAAO,aAAa,GAAG;IACzB;IAEA,IAAI,QAAQ,SAAS,EAAE;QACrB,IAAI,QAAQ,SAAS,CAAC,GAAG,GAAG,GAAG;YAC7B,OAAO,SAAS,GAAG;QACrB;QACA,IAAI,QAAQ,SAAS,CAAC,GAAG,IAAI,QAAQ,SAAS,CAAC,GAAG,EAAE;YAClD,OAAO,SAAS,GAAG;QACrB;QACA,IAAI,QAAQ,SAAS,CAAC,GAAG,GAAG,WAAW;YACrC,OAAO,SAAS,GAAG;QACrB;IACF;IAEA,OAAO;AACT;AAeO,MAAM,2BAA2B,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC3C,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,wBAAqB,AAAD,EAClB,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,cAAc;YACZ,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;YAC7B,IAAI,SAAS;gBACX,IAAI;oBACF,mBAAmB;oBACnB,oBAAoB;wBAAE,GAAG,OAAO;oBAAC;gBACnC;YACF;QACF;QACA,oBAAoB;QACpB,aAAa,CAAC;YACZ,IAAI;gBACF,MAAM,SAAS,aAAa,OAAO,CAAC;gBACpC,MAAM,UAAU,SAAS,KAAK,KAAK,CAAC,UAAU,CAAC;gBAC/C,MAAM,SAAS,OAAO,CAAC,WAAW;gBAElC,IAAI,QAAQ;oBACV,IAAI,CAAA,QAAS,CAAC;4BACZ,SAAS;gCAAE,GAAG,MAAM;4BAAC;4BACrB,mBAAmB;4BACnB,oBAAoB,MAAM,kBAAkB;wBAC9C,CAAC;gBACH;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;QACA,uBAAuB;YACrB,IAAI;gBACF,SAAS;gBACT,kBAAkB,CAAC;YACrB;QACF;QACA,cAAc,CAAC;YACb,IAAI;gBACF,MAAM,SAAS,aAAa,OAAO,CAAC;gBACpC,MAAM,UAAU,SAAS,KAAK,KAAK,CAAC,UAAU,CAAC;gBAC/C,OAAO,OAAO,CAAC,KAAK;gBACpB,aAAa,OAAO,CAClB,4BACA,KAAK,SAAS,CAAC;YAEnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;QACF;QACA,gBAAgB;QAChB,SAAS;QACT,YAAY;YACV,IAAI;gBACF,MAAM,SAAS,aAAa,OAAO,CAAC;gBACpC,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU,CAAC;YACxC,EAAE,OAAM;gBACN,OAAO,CAAC;YACV;QACF;QAEA,mBAAmB;QAEnB,mBAAmB;QAEnB,SAAS;QAET,oBAAoB;QAEpB,cAAc;YACZ,IAAI;gBACF,SAAS;gBACT,mBAAmB;gBACnB,SAAS;gBACT,kBAAkB,CAAC;YACrB;QACF;QAEA,eAAe;YACb,MAAM,EAAE,kBAAkB,EAAE,GAAG;YAC/B,IAAI;gBACF,SAAS;oBAAE,GAAG,kBAAkB;gBAAC;gBACjC,mBAAmB;gBACnB,SAAS;gBACT,kBAAkB,CAAC;YACrB;QACF;QAEA,cAAc,CAAC;YACb,IAAI;gBACF,MAAM,EAAE,OAAO,EAAE,GAAG;gBACpB,MAAM,SAAS,aAAa,OAAO,CAAC;gBACpC,MAAM,UAAU,SAAS,KAAK,KAAK,CAAC,UAAU,CAAC;gBAC/C,OAAO,CAAC,KAAK,GAAG;oBAAE,GAAG,OAAO;gBAAC;gBAC7B,aAAa,OAAO,CAClB,4BACA,KAAK,SAAS,CAAC;YAEnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;QACF;QAEA,cAAc,CAAC,KAAa;YAC1B,IAAI,CAAA;gBACF,MAAM,aAAa;oBAAE,GAAG,MAAM,OAAO;oBAAE,WAAW;wBAAE;wBAAK;oBAAI;gBAAE;gBAC/D,MAAM,SAAS,qBAAqB;gBAEpC,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;oBACxC,kBAAkB;gBACpB;YACF;QACF;QAEA,uBAAuB;QACvB,cAAc,CAAC,MAAY;YACzB,IAAI,CAAA;gBACF,MAAM,aAAa;oBACjB,GAAG,MAAM,OAAO;oBAChB,WAAW;wBAAE;wBAAM;oBAAG;gBACxB;gBACA,MAAM,SAAS,qBAAqB;gBAEpC,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;oBACxC,kBAAkB;gBACpB;YACF;QACF;QAEA,cAAc,CAAC;YACb,IAAI,CAAA;gBACF,MAAM,aAAa;oBAAE,GAAG,MAAM,OAAO;oBAAE;gBAAU;gBACjD,MAAM,SAAS,qBAAqB;gBAEpC,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;oBACxC,kBAAkB;gBACpB;YACF;QACF;QAEA,oBAAoB,CAAC;YACnB,IAAI;gBAAE,mBAAmB;YAAK;QAChC;QAEA,kBAAkB;QAClB,YAAY,CAAC;YACX,IAAI,CAAA;gBACF,MAAM,aAAa;oBAAE,GAAG,MAAM,OAAO;oBAAE,GAAG,cAAc;gBAAC;gBACzD,MAAM,SAAS,qBAAqB;gBAEpC,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;oBACxC,kBAAkB;gBACpB;YACF;QACF;QAEA,0BAA0B,CAAC;YACzB,IAAI,CAAA;gBACF,MAAM,aAAa;oBAAE,GAAG,MAAM,OAAO;oBAAE;gBAAsB;gBAC7D,MAAM,SAAS,qBAAqB;gBAEpC,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;oBACxC,kBAAkB;gBACpB;YACF;QACF;QAEA,oBAAoB,CAAC;YACnB,IAAI,CAAA;gBACF,MAAM,aAAa;oBAAE,GAAG,MAAM,OAAO;oBAAE;gBAAgB;gBACvD,MAAM,SAAS,qBAAqB;gBAEpC,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;oBACxC,kBAAkB;gBACpB;YACF;QACF;QAEA,cAAc,CAAC;YACb,IAAI,CAAA;gBACF,MAAM,aAAa;oBAAE,GAAG,MAAM,OAAO;oBAAE;gBAAU;gBACjD,MAAM,SAAS,qBAAqB;gBAEpC,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;oBACxC,kBAAkB;gBACpB;YACF;QACF;QAEA,kBAAkB,CAAC;YACjB,IAAI,CAAA;gBACF,MAAM,aAAa;oBAAE,GAAG,MAAM,OAAO;oBAAE;gBAAc;gBACrD,MAAM,SAAS,qBAAqB;gBAEpC,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;oBACxC,kBAAkB;gBACpB;YACF;QACF;QAEA,2CAA2C;QAC3C,iBAAiB,CAAC;YAChB,IAAI,CAAA;gBACF,MAAM,aAAa;oBAAE,GAAG,MAAM,OAAO;oBAAE;gBAAa;gBACpD,MAAM,SAAS,qBAAqB;gBAEpC,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;oBACxC,kBAAkB;gBACpB;YACF;QACF;QAEA,WAAW,CAAC;YACV,IAAI,CAAA;gBACF,MAAM,aAAa;oBAAE,GAAG,MAAM,OAAO;oBAAE;gBAAO;gBAC9C,MAAM,SAAS,qBAAqB;gBAEpC,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;oBACxC,kBAAkB;gBACpB;YACF;QACF;QAEA,mBAAmB,CAAC;YAClB,IAAI,CAAA;gBACF,MAAM,aAAa;oBAAE,GAAG,MAAM,OAAO;oBAAE;gBAAe;gBACtD,MAAM,SAAS,qBAAqB;gBAEpC,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;oBACxC,kBAAkB;gBACpB;YACF;QACF;QAEA,gCAAgC;QAChC,eAAe,CAAC;YACd,IAAI,CAAA;gBACF,MAAM,aAAa;oBAAE,GAAG,MAAM,OAAO;oBAAE;gBAAW;gBAClD,MAAM,SAAS,qBAAqB;gBAEpC,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;oBACxC,kBAAkB;gBACpB;YACF;QACF;QAEA,aAAa,CAAC;YACZ,IAAI,CAAA;gBACF,MAAM,aAAa;oBAAE,GAAG,MAAM,OAAO;oBAAE;gBAAS;gBAChD,MAAM,SAAS,qBAAqB;gBAEpC,OAAO;oBACL,SAAS;oBACT,mBAAmB;oBACnB,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;oBACxC,kBAAkB;gBACpB;YACF;QACF;QAEA,sBAAsB;QACtB,mBAAmB;YACjB,IAAI,CAAA,QAAS,CAAC;oBACZ,mBAAmB,CAAC,MAAM,iBAAiB;gBAC7C,CAAC;QACH;QAEA,aAAa;QACb,iBAAiB;YACf,MAAM,EAAE,OAAO,EAAE,GAAG;YACpB,MAAM,SAAS,qBAAqB;YACpC,MAAM,UAAU,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;YAE/C,IAAI;gBACF;gBACA,kBAAkB;YACpB;YAEA,OAAO;QACT;QAEA,kBAAkB,CAAC;IACrB,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAA,QAAS,CAAC;YACpB,oDAAoD;YACpD,SAAS,MAAM,OAAO;YACtB,oBAAoB,MAAM,kBAAkB;QAC9C,CAAC;IACD,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;AACnC,KAGJ;IACE,MAAM;AACR;AAIJ;;;;;CAKC,GAED,oEAAoE;AACpE,MAAM,kBAAkB,CAAC,QAAiC,MAAM,OAAO;AACvE,MAAM,kBAAkB,CAAC,QAAiC,CAAC;QACzD,cAAc,MAAM,YAAY;QAChC,cAAc,MAAM,YAAY;QAChC,eAAe,MAAM,aAAa;QAClC,cAAc,MAAM,YAAY;QAChC,cAAc,MAAM,YAAY;QAChC,cAAc,MAAM,YAAY;QAChC,YAAY,MAAM,UAAU;QAC5B,0BAA0B,MAAM,wBAAwB;QACxD,oBAAoB,MAAM,kBAAkB;QAC5C,cAAc,MAAM,YAAY;QAChC,kBAAkB,MAAM,gBAAgB;QACxC,iBAAiB,MAAM,eAAe;QACtC,WAAW,MAAM,SAAS;QAC1B,mBAAmB,MAAM,iBAAiB;QAC1C,eAAe,MAAM,aAAa;QAClC,aAAa,MAAM,WAAW;IAChC,CAAC;AAED,MAAM,aAAa,CAAC,QAAiC,CAAC;QACpD,mBAAmB,MAAM,iBAAiB;QAC1C,mBAAmB,MAAM,iBAAiB;QAC1C,oBAAoB,MAAM,kBAAkB;QAC5C,mBAAmB,MAAM,iBAAiB;IAC5C,CAAC;AAED,MAAM,qBAAqB,CAAC,QAAiC,CAAC;QAC5D,uBAAuB,MAAM,qBAAqB;QAClD,SAAS,MAAM,OAAO;QACtB,iBAAiB,MAAM,eAAe;QACtC,kBAAkB,MAAM,gBAAgB;IAC1C,CAAC;AAED,MAAM,kBAAkB,CAAC,QAAiC,CAAC;QACzD,aAAa,MAAM,WAAW;QAC9B,cAAc,MAAM,YAAY;QAChC,YAAY,MAAM,UAAU;QAC5B,cAAc,MAAM,YAAY;IAClC,CAAC;AAEM,MAAM,sBAAsB,IACjC,yBAAyB,CAAA,GAAA,mJAAA,CAAA,aAAU,AAAD,EAAE;AAE/B,MAAM,6BAA6B,IACxC,yBAAyB,CAAA,GAAA,mJAAA,CAAA,aAAU,AAAD,EAAE;AAE/B,MAAM,wBAAwB,IACnC,yBAAyB,CAAA,GAAA,mJAAA,CAAA,aAAU,AAAD,EAAE;AAE/B,MAAM,gCAAgC,IAC3C,yBAAyB,CAAA,GAAA,mJAAA,CAAA,aAAU,AAAD,EAAE;AAE/B,MAAM,6BAA6B,IACxC,yBAAyB,CAAA,GAAA,mJAAA,CAAA,aAAU,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 2060, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/data/stores/index.ts"], "sourcesContent": ["export * from './useReportingFiltersStore';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 2098, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/hooks/useReportGeneration.ts"], "sourcesContent": ["/**\r\n * @file useReportGeneration.ts\r\n * @description Hook for managing data report generation\r\n */\r\n\r\nimport { useState, useCallback } from 'react';\r\nimport { useApiQuery } from '@/hooks/api/useApiQuery';\r\nimport { useSecureApiClient } from '@/lib/api/security';\r\nimport {\r\n  createReportGenerationService,\r\n  IReportGenerationService,\r\n  IApiClient,\r\n} from '../data/services/ReportGenerationService';\r\n\r\n/**\r\n * Report generation configuration interface\r\n */\r\nexport interface ReportGenerationConfig {\r\n  entityTypes: string[];\r\n  template: string;\r\n  format: string;\r\n  filters?: Record<string, any>;\r\n  options?: {\r\n    name?: string;\r\n    description?: string;\r\n    includeCharts?: boolean;\r\n    includeSummary?: boolean;\r\n  };\r\n}\r\n\r\n/**\r\n * Individual report generation configuration\r\n */\r\nexport interface IndividualReportConfig {\r\n  entityType: string;\r\n  entityId: string;\r\n  template?: string;\r\n  format?: string;\r\n  options?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Aggregate report generation configuration\r\n */\r\nexport interface AggregateReportConfig {\r\n  entityType: string;\r\n  filters?: Record<string, any>;\r\n  template?: string;\r\n  format?: string;\r\n  options?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Report generation result\r\n */\r\nexport interface ReportGenerationResult {\r\n  data?: any; // Main report data\r\n  report?: any; // Legacy support\r\n  metadata: {\r\n    id: string;\r\n    type: string;\r\n    entityTypes?: string[];\r\n    entityType?: string;\r\n    entityId?: string;\r\n    format: string;\r\n    template: string;\r\n    generatedAt: string;\r\n    generatedBy: string;\r\n    filters?: Record<string, any>;\r\n    options?: Record<string, any>;\r\n  };\r\n}\r\n\r\n/**\r\n * Report history item\r\n */\r\nexport interface ReportHistoryItem {\r\n  id: string;\r\n  type: 'comprehensive' | 'individual' | 'aggregate';\r\n  entityTypes?: string[];\r\n  entityType?: string;\r\n  entityId?: string;\r\n  format: string;\r\n  template: string;\r\n  status: 'completed' | 'failed' | 'processing';\r\n  generatedAt: string;\r\n  generatedBy: string;\r\n  downloadUrl?: string;\r\n  fileSize?: string;\r\n}\r\n\r\n/**\r\n * Hook for managing report generation\r\n *\r\n * Follows SOLID Principles:\r\n * - SRP: Single responsibility for React state management\r\n * - DIP: Depends on IReportGenerationService abstraction\r\n * - OCP: Open for extension with new report types\r\n */\r\nexport const useReportGeneration = () => {\r\n  const [isGenerating, setIsGenerating] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const { client } = useSecureApiClient();\r\n\r\n  // Create API client adapter for the service\r\n  const apiClient: IApiClient = {\r\n    request: async config => {\r\n      const response = await client.request(config);\r\n      return { data: response };\r\n    },\r\n  };\r\n\r\n  // Create service instance with proper API client\r\n  const service = createReportGenerationService(apiClient);\r\n\r\n  /**\r\n   * Generate comprehensive data report\r\n   * Uses service layer following DIP principle\r\n   */\r\n  const generateReport = useCallback(\r\n    async (config: ReportGenerationConfig): Promise<ReportGenerationResult> => {\r\n      setIsGenerating(true);\r\n      setError(null);\r\n\r\n      try {\r\n        return await service.generateComprehensiveReport(config);\r\n      } catch (err) {\r\n        const errorMessage =\r\n          err instanceof Error ? err.message : 'Failed to generate report';\r\n        setError(errorMessage);\r\n        throw err;\r\n      } finally {\r\n        setIsGenerating(false);\r\n      }\r\n    },\r\n    [service]\r\n  );\r\n\r\n  /**\r\n   * Generate individual entity report\r\n   * Uses service layer following DIP principle\r\n   */\r\n  const generateIndividualReport = useCallback(\r\n    async (config: IndividualReportConfig): Promise<ReportGenerationResult> => {\r\n      setIsGenerating(true);\r\n      setError(null);\r\n\r\n      try {\r\n        return await service.generateIndividualReport(config);\r\n      } catch (err) {\r\n        const errorMessage =\r\n          err instanceof Error\r\n            ? err.message\r\n            : 'Failed to generate individual report';\r\n        setError(errorMessage);\r\n        throw err;\r\n      } finally {\r\n        setIsGenerating(false);\r\n      }\r\n    },\r\n    [service]\r\n  );\r\n\r\n  /**\r\n   * Generate aggregate entity report\r\n   * Uses service layer following DIP principle\r\n   */\r\n  const generateAggregateReport = useCallback(\r\n    async (config: AggregateReportConfig): Promise<ReportGenerationResult> => {\r\n      setIsGenerating(true);\r\n      setError(null);\r\n\r\n      try {\r\n        return await service.generateAggregateReport(config);\r\n      } catch (err) {\r\n        const errorMessage =\r\n          err instanceof Error\r\n            ? err.message\r\n            : 'Failed to generate aggregate report';\r\n        setError(errorMessage);\r\n        throw err;\r\n      } finally {\r\n        setIsGenerating(false);\r\n      }\r\n    },\r\n    [service]\r\n  );\r\n\r\n  /**\r\n   * Export generated report data to PDF/Excel/CSV\r\n   */\r\n  const exportReport = useCallback(\r\n    async (\r\n      reportData: any,\r\n      format: 'pdf' | 'excel' | 'csv',\r\n      entityType: 'delegations' | 'tasks' | 'vehicles' | 'employees',\r\n      reportTitle?: string,\r\n      filename?: string\r\n    ): Promise<void> => {\r\n      try {\r\n        const { useExport } = await import('../exports/hooks/useExport');\r\n        const { exportReportToPDF, exportReportToExcel, exportToCSV } =\r\n          useExport(filename || 'report');\r\n\r\n        switch (format) {\r\n          case 'pdf':\r\n            // Convert report data to PDF using entity-specific components\r\n            await exportReportToPDF(\r\n              reportData,\r\n              entityType,\r\n              reportTitle ||\r\n                `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} Report`,\r\n              filename\r\n            );\r\n            break;\r\n\r\n          case 'excel':\r\n            // Convert to Excel format with proper sheets\r\n            exportReportToExcel(reportData, entityType, filename);\r\n            break;\r\n\r\n          case 'csv':\r\n            // Convert to CSV format (flatten data for CSV)\r\n            const csvData = Array.isArray(reportData.data)\r\n              ? reportData.data\r\n              : [reportData.data || reportData];\r\n            exportToCSV(csvData, { filename: filename || 'report' });\r\n            break;\r\n\r\n          default:\r\n            throw new Error(`Unsupported export format: ${format}`);\r\n        }\r\n      } catch (error) {\r\n        console.error('Export failed:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  return {\r\n    generateComprehensiveReport: generateReport,\r\n    generateIndividualReport,\r\n    generateAggregateReport,\r\n    exportReport,\r\n    isGenerating,\r\n    error,\r\n  };\r\n};\r\n\r\n/**\r\n * Hook for managing report history\r\n */\r\nexport const useReportHistory = (filters?: {\r\n  type?: string;\r\n  entityType?: string;\r\n}) => {\r\n  const { client } = useSecureApiClient();\r\n  const queryParams = new URLSearchParams();\r\n  if (filters?.type) queryParams.append('type', filters.type);\r\n  if (filters?.entityType) queryParams.append('entityType', filters.entityType);\r\n\r\n  const historyQuery = useApiQuery(\r\n    ['report-history', filters],\r\n    async (): Promise<{ reports: ReportHistoryItem[]; pagination: any }> => {\r\n      const response = await client.get(\r\n        `/api/reporting/reports/history?${queryParams.toString()}`\r\n      );\r\n      return response;\r\n    },\r\n    {\r\n      cacheDuration: 2 * 60 * 1000, // 2 minutes\r\n      enableRetry: true,\r\n    }\r\n  );\r\n\r\n  return {\r\n    reports: historyQuery.data?.reports || [],\r\n    pagination: historyQuery.data?.pagination,\r\n    isLoading: historyQuery.isLoading,\r\n    error: historyQuery.error,\r\n    refetch: historyQuery.refetch,\r\n  };\r\n};\r\n\r\n/**\r\n * Hook for downloading reports\r\n */\r\nexport const useReportDownload = () => {\r\n  const [isDownloading, setIsDownloading] = useState(false);\r\n  const [downloadError, setDownloadError] = useState<string | null>(null);\r\n  const { client } = useSecureApiClient();\r\n\r\n  const downloadReport = useCallback(\r\n    async (reportId: string): Promise<void> => {\r\n      setIsDownloading(true);\r\n      setDownloadError(null);\r\n\r\n      try {\r\n        const response = await client.get(\r\n          `/api/reporting/reports/${reportId}/download`\r\n        );\r\n\r\n        // For now, just show the response since actual file download isn't implemented yet\r\n        console.log('Download result:', response);\r\n\r\n        // TODO: Implement actual file download when backend file storage is ready\r\n        alert('Download functionality will be implemented with file storage');\r\n      } catch (err) {\r\n        const errorMessage =\r\n          err instanceof Error ? err.message : 'Failed to download report';\r\n        setDownloadError(errorMessage);\r\n        throw err;\r\n      } finally {\r\n        setIsDownloading(false);\r\n      }\r\n    },\r\n    [client]\r\n  );\r\n\r\n  return {\r\n    downloadReport,\r\n    isDownloading,\r\n    downloadError,\r\n  };\r\n};\r\n\r\n/**\r\n * Hook for report templates\r\n */\r\nexport const useReportTemplates = () => {\r\n  const { client } = useSecureApiClient();\r\n\r\n  const templatesQuery = useApiQuery(\r\n    ['report-templates'],\r\n    async () => {\r\n      const response = await client.get('/api/reporting/reports/templates');\r\n\r\n      // Ensure we return an array, handle different response structures\r\n      const data = response;\r\n      if (Array.isArray(data)) {\r\n        return data;\r\n      }\r\n\r\n      // If response has nested data property (API wrapper format)\r\n      if (data && Array.isArray(data.data)) {\r\n        return data.data;\r\n      }\r\n\r\n      // Fallback to empty array if data is not in expected format\r\n      console.warn('Report templates API returned unexpected format:', data);\r\n      return [];\r\n    },\r\n    {\r\n      cacheDuration: 10 * 60 * 1000, // 10 minutes\r\n      enableRetry: true,\r\n    }\r\n  );\r\n\r\n  return {\r\n    templates: Array.isArray(templatesQuery.data) ? templatesQuery.data : [],\r\n    isLoading: templatesQuery.isLoading,\r\n    error: templatesQuery.error,\r\n    refetch: templatesQuery.refetch,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;AACA;AACA;AAAA;AACA;;;;;AA2FO,MAAM,sBAAsB;IACjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,qBAAkB,AAAD;IAEpC,4CAA4C;IAC5C,MAAM,YAAwB;QAC5B,SAAS,OAAM;YACb,MAAM,WAAW,MAAM,OAAO,OAAO,CAAC;YACtC,OAAO;gBAAE,MAAM;YAAS;QAC1B;IACF;IAEA,iDAAiD;IACjD,MAAM,UAAU,CAAA,GAAA,yLAAA,CAAA,gCAA6B,AAAD,EAAE;IAE9C;;;GAGC,GACD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC/B,OAAO;QACL,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,OAAO,MAAM,QAAQ,2BAA2B,CAAC;QACnD,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,SAAS;YACT,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF,GACA;QAAC;KAAQ;IAGX;;;GAGC,GACD,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACzC,OAAO;QACL,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,OAAO,MAAM,QAAQ,wBAAwB,CAAC;QAChD,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QACX,IAAI,OAAO,GACX;YACN,SAAS;YACT,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF,GACA;QAAC;KAAQ;IAGX;;;GAGC,GACD,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxC,OAAO;QACL,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,OAAO,MAAM,QAAQ,uBAAuB,CAAC;QAC/C,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QACX,IAAI,OAAO,GACX;YACN,SAAS;YACT,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF,GACA;QAAC;KAAQ;IAGX;;GAEC,GACD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7B,OACE,YACA,QACA,YACA,aACA;QAEA,IAAI;YACF,MAAM,EAAE,SAAS,EAAE,GAAG;YACtB,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,WAAW,EAAE,GAC3D,UAAU,YAAY;YAExB,OAAQ;gBACN,KAAK;oBACH,8DAA8D;oBAC9D,MAAM,kBACJ,YACA,YACA,eACE,GAAG,WAAW,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,KAAK,CAAC,GAAG,OAAO,CAAC,EACtE;oBAEF;gBAEF,KAAK;oBACH,6CAA6C;oBAC7C,oBAAoB,YAAY,YAAY;oBAC5C;gBAEF,KAAK;oBACH,+CAA+C;oBAC/C,MAAM,UAAU,MAAM,OAAO,CAAC,WAAW,IAAI,IACzC,WAAW,IAAI,GACf;wBAAC,WAAW,IAAI,IAAI;qBAAW;oBACnC,YAAY,SAAS;wBAAE,UAAU,YAAY;oBAAS;oBACtD;gBAEF;oBACE,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,QAAQ;YAC1D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM;QACR;IACF,GACA,EAAE;IAGJ,OAAO;QACL,6BAA6B;QAC7B;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,MAAM,mBAAmB,CAAC;IAI/B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,qBAAkB,AAAD;IACpC,MAAM,cAAc,IAAI;IACxB,IAAI,SAAS,MAAM,YAAY,MAAM,CAAC,QAAQ,QAAQ,IAAI;IAC1D,IAAI,SAAS,YAAY,YAAY,MAAM,CAAC,cAAc,QAAQ,UAAU;IAE5E,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAC7B;QAAC;QAAkB;KAAQ,EAC3B;QACE,MAAM,WAAW,MAAM,OAAO,GAAG,CAC/B,CAAC,+BAA+B,EAAE,YAAY,QAAQ,IAAI;QAE5D,OAAO;IACT,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;IAGF,OAAO;QACL,SAAS,aAAa,IAAI,EAAE,WAAW,EAAE;QACzC,YAAY,aAAa,IAAI,EAAE;QAC/B,WAAW,aAAa,SAAS;QACjC,OAAO,aAAa,KAAK;QACzB,SAAS,aAAa,OAAO;IAC/B;AACF;AAKO,MAAM,oBAAoB;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,qBAAkB,AAAD;IAEpC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC/B,OAAO;QACL,iBAAiB;QACjB,iBAAiB;QAEjB,IAAI;YACF,MAAM,WAAW,MAAM,OAAO,GAAG,CAC/B,CAAC,uBAAuB,EAAE,SAAS,SAAS,CAAC;YAG/C,mFAAmF;YACnF,QAAQ,GAAG,CAAC,oBAAoB;YAEhC,0EAA0E;YAC1E,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;YACvC,iBAAiB;YACjB,MAAM;QACR,SAAU;YACR,iBAAiB;QACnB;IACF,GACA;QAAC;KAAO;IAGV,OAAO;QACL;QACA;QACA;IACF;AACF;AAKO,MAAM,qBAAqB;IAChC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,qBAAkB,AAAD;IAEpC,MAAM,iBAAiB,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAC/B;QAAC;KAAmB,EACpB;QACE,MAAM,WAAW,MAAM,OAAO,GAAG,CAAC;QAElC,kEAAkE;QAClE,MAAM,OAAO;QACb,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,OAAO;QACT;QAEA,4DAA4D;QAC5D,IAAI,QAAQ,MAAM,OAAO,CAAC,KAAK,IAAI,GAAG;YACpC,OAAO,KAAK,IAAI;QAClB;QAEA,4DAA4D;QAC5D,QAAQ,IAAI,CAAC,oDAAoD;QACjE,OAAO,EAAE;IACX,GACA;QACE,eAAe,KAAK,KAAK;QACzB,aAAa;IACf;IAGF,OAAO;QACL,WAAW,MAAM,OAAO,CAAC,eAAe,IAAI,IAAI,eAAe,IAAI,GAAG,EAAE;QACxE,WAAW,eAAe,SAAS;QACnC,OAAO,eAAe,KAAK;QAC3B,SAAS,eAAe,OAAO;IACjC;AACF", "debugId": null}}, {"offset": {"line": 2313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/hooks/useReportTypes.ts"], "sourcesContent": ["/**\r\n * @file useReportTypes.ts\r\n * @description Hook for managing report types following existing patterns\r\n */\r\n\r\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\r\n\r\nimport { useApiQuery } from '@/hooks/api';\r\nimport { apiClient } from '@/lib/api';\r\n\r\nimport type { ReportType } from '../data/types/reporting';\r\n\r\n/**\r\n * Hook for managing report types\r\n *\r\n * Follows existing patterns from other management hooks.\r\n * Integrates with the established API and caching patterns.\r\n *\r\n * @returns Query and mutation functions for report type management\r\n */\r\nexport const useReportTypes = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  // Query for fetching all report types\r\n  const reportTypesQuery = useApiQuery(\r\n    ['report-types'],\r\n    async (): Promise<ReportType[]> => {\r\n      const result = (await apiClient.get('/reporting/report-types')) as any;\r\n      // Extract the data array from the API response structure\r\n      return result.data?.data || [];\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes\r\n      enableRetry: true,\r\n    }\r\n  );\r\n\r\n  // Mutation for creating a new report type\r\n  const createReportType = useMutation({\r\n    mutationFn: async (\r\n      reportTypeData: Partial<ReportType>\r\n    ): Promise<ReportType> => {\r\n      const result = (await apiClient.post(\r\n        '/reporting/report-types',\r\n        reportTypeData\r\n      )) as any;\r\n      // Extract the data from the API response structure\r\n      return result.data?.data || result.data;\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate and refetch report types\r\n      queryClient.invalidateQueries({ queryKey: ['report-types'] });\r\n    },\r\n  });\r\n\r\n  // Mutation for updating an existing report type\r\n  const updateReportType = useMutation({\r\n    mutationFn: async ({\r\n      id,\r\n      ...reportTypeData\r\n    }: Partial<ReportType> & { id: string }): Promise<ReportType> => {\r\n      const result = (await apiClient.put(\r\n        `/reporting/report-types/${id}`,\r\n        reportTypeData\r\n      )) as any;\r\n      // Extract the data from the API response structure\r\n      return result.data?.data || result.data;\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate and refetch report types\r\n      queryClient.invalidateQueries({ queryKey: ['report-types'] });\r\n    },\r\n  });\r\n\r\n  // Mutation for deleting a report type\r\n  const deleteReportType = useMutation({\r\n    mutationFn: async (id: string): Promise<void> => {\r\n      await apiClient.delete(`/reporting/report-types/${id}`);\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate and refetch report types\r\n      queryClient.invalidateQueries({ queryKey: ['report-types'] });\r\n    },\r\n  });\r\n\r\n  // Mutation for duplicating a report type\r\n  const duplicateReportType = useMutation({\r\n    mutationFn: async (id: string): Promise<ReportType> => {\r\n      const result = (await apiClient.post(\r\n        `/reporting/report-types/${id}/duplicate`\r\n      )) as any;\r\n      // Extract the data from the API response structure\r\n      return result.data?.data || result.data;\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate and refetch report types\r\n      queryClient.invalidateQueries({ queryKey: ['report-types'] });\r\n    },\r\n  });\r\n\r\n  // Mutation for toggling report type active status\r\n  const toggleReportTypeActive = useMutation({\r\n    mutationFn: async ({\r\n      id,\r\n      isActive,\r\n    }: {\r\n      id: string;\r\n      isActive: boolean;\r\n    }): Promise<ReportType> => {\r\n      const result = (await apiClient.patch(\r\n        `/reporting/report-types/${id}/toggle-active`,\r\n        { isActive }\r\n      )) as any;\r\n      // Extract the data from the API response structure\r\n      return result.data?.data || result.data;\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate and refetch report types\r\n      queryClient.invalidateQueries({ queryKey: ['report-types'] });\r\n    },\r\n  });\r\n\r\n  return {\r\n    // Mutations\r\n    createReportType,\r\n    // Query data\r\n    data: reportTypesQuery.data,\r\n    deleteReportType,\r\n\r\n    duplicateReportType,\r\n    error: reportTypesQuery.error,\r\n    isLoading: reportTypesQuery.isLoading,\r\n    // Utility functions\r\n    refetch: reportTypesQuery.refetch,\r\n    toggleReportTypeActive,\r\n\r\n    updateReportType,\r\n  };\r\n};\r\n\r\n/**\r\n * Hook for fetching a single report type by ID\r\n *\r\n * @param id - Report type ID\r\n * @returns Query result with single report type data\r\n */\r\nexport const useReportType = (id: string) => {\r\n  return useApiQuery(\r\n    ['report-type', id],\r\n    async (): Promise<ReportType> => {\r\n      const result = (await apiClient.get(\r\n        `/reporting/report-types/${id}`\r\n      )) as any;\r\n      // Extract the data from the API response structure\r\n      return result.data?.data || result.data;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enabled: !!id,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching report types by category\r\n *\r\n * @param category - Report type category\r\n * @returns Query result with filtered report types\r\n */\r\nexport const useReportTypesByCategory = (category: string) => {\r\n  return useApiQuery(\r\n    ['report-types', 'category', category],\r\n    async (): Promise<ReportType[]> => {\r\n      const result = (await apiClient.get(\r\n        `/reporting/report-types?category=${encodeURIComponent(category)}`\r\n      )) as any;\r\n      // Extract the data array from the API response structure\r\n      return result.data?.data || [];\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enabled: !!category,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AAAA;AAEA;AAAA;AACA;AAAA;;;;AAYO,MAAM,iBAAiB;IAC5B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,sCAAsC;IACtC,MAAM,mBAAmB,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACjC;QAAC;KAAe,EAChB;QACE,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACpC,yDAAyD;QACzD,OAAO,OAAO,IAAI,EAAE,QAAQ,EAAE;IAChC,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;IAGF,0CAA0C;IAC1C,MAAM,mBAAmB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACnC,YAAY,OACV;YAEA,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,IAAI,CAClC,2BACA;YAEF,mDAAmD;YACnD,OAAO,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI;QACzC;QACA,WAAW;YACT,sCAAsC;YACtC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAe;YAAC;QAC7D;IACF;IAEA,gDAAgD;IAChD,MAAM,mBAAmB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACnC,YAAY,OAAO,EACjB,EAAE,EACF,GAAG,gBACkC;YACrC,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CACjC,CAAC,wBAAwB,EAAE,IAAI,EAC/B;YAEF,mDAAmD;YACnD,OAAO,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI;QACzC;QACA,WAAW;YACT,sCAAsC;YACtC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAe;YAAC;QAC7D;IACF;IAEA,sCAAsC;IACtC,MAAM,mBAAmB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACnC,YAAY,OAAO;YACjB,MAAM,0IAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,wBAAwB,EAAE,IAAI;QACxD;QACA,WAAW;YACT,sCAAsC;YACtC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAe;YAAC;QAC7D;IACF;IAEA,yCAAyC;IACzC,MAAM,sBAAsB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACtC,YAAY,OAAO;YACjB,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,IAAI,CAClC,CAAC,wBAAwB,EAAE,GAAG,UAAU,CAAC;YAE3C,mDAAmD;YACnD,OAAO,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI;QACzC;QACA,WAAW;YACT,sCAAsC;YACtC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAe;YAAC;QAC7D;IACF;IAEA,kDAAkD;IAClD,MAAM,yBAAyB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACzC,YAAY,OAAO,EACjB,EAAE,EACF,QAAQ,EAIT;YACC,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,KAAK,CACnC,CAAC,wBAAwB,EAAE,GAAG,cAAc,CAAC,EAC7C;gBAAE;YAAS;YAEb,mDAAmD;YACnD,OAAO,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI;QACzC;QACA,WAAW;YACT,sCAAsC;YACtC,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAe;YAAC;QAC7D;IACF;IAEA,OAAO;QACL,YAAY;QACZ;QACA,aAAa;QACb,MAAM,iBAAiB,IAAI;QAC3B;QAEA;QACA,OAAO,iBAAiB,KAAK;QAC7B,WAAW,iBAAiB,SAAS;QACrC,oBAAoB;QACpB,SAAS,iBAAiB,OAAO;QACjC;QAEA;IACF;AACF;AAQO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAe;KAAG,EACnB;QACE,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CACjC,CAAC,wBAAwB,EAAE,IAAI;QAEjC,mDAAmD;QACnD,OAAO,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI;IACzC,GACA;QACE,eAAe,IAAI,KAAK;QACxB,SAAS,CAAC,CAAC;QACX,aAAa;IACf;AAEJ;AAQO,MAAM,2BAA2B,CAAC;IACvC,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAgB;QAAY;KAAS,EACtC;QACE,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CACjC,CAAC,iCAAiC,EAAE,mBAAmB,WAAW;QAEpE,yDAAyD;QACzD,OAAO,OAAO,IAAI,EAAE,QAAQ,EAAE;IAChC,GACA;QACE,eAAe,IAAI,KAAK;QACxB,SAAS,CAAC,CAAC;QACX,aAAa;IACf;AAEJ", "debugId": null}}, {"offset": {"line": 2473, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/hooks/useVehicleAnalytics.ts"], "sourcesContent": ["/**\r\n * @file useVehicleAnalytics.ts\r\n * @description Hook for fetching vehicle analytics data following existing patterns\r\n */\r\n\r\nimport { useQuery } from '@tanstack/react-query';\r\nimport { useApiQuery } from '@/hooks/api';\r\nimport { apiClient } from '@/lib/api';\r\nimport type {\r\n  ReportingFilters,\r\n  VehicleAnalytics,\r\n} from '../data/types/reporting';\r\n\r\n/**\r\n * Hook for fetching vehicle analytics data\r\n *\r\n * Follows existing patterns from useTaskAnalytics and other reporting hooks.\r\n * Integrates with the established API and caching patterns.\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with vehicle analytics data\r\n */\r\nexport const useVehicleAnalytics = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['vehicle-analytics', filters],\r\n    async (): Promise<VehicleAnalytics> => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        // Defensive programming: Ensure dates are Date objects\r\n        const fromDate =\r\n          filters.dateRange.from instanceof Date\r\n            ? filters.dateRange.from\r\n            : new Date(filters.dateRange.from);\r\n        const toDate =\r\n          filters.dateRange.to instanceof Date\r\n            ? filters.dateRange.to\r\n            : new Date(filters.dateRange.to);\r\n\r\n        queryParams.append(\r\n          'dateRange.from',\r\n          fromDate.toISOString().split('T')[0] || fromDate.toISOString()\r\n        );\r\n        queryParams.append(\r\n          'dateRange.to',\r\n          toDate.toISOString().split('T')[0] || toDate.toISOString()\r\n        );\r\n      }\r\n\r\n      if (filters?.vehicles) {\r\n        filters.vehicles.forEach(vehicle =>\r\n          queryParams.append('vehicles', vehicle.toString())\r\n        );\r\n      }\r\n\r\n      if (filters?.serviceTypes) {\r\n        filters.serviceTypes.forEach(type =>\r\n          queryParams.append('serviceTypes', type)\r\n        );\r\n      }\r\n\r\n      if (filters?.serviceStatus) {\r\n        filters.serviceStatus.forEach(status =>\r\n          queryParams.append('serviceStatus', status)\r\n        );\r\n      }\r\n\r\n      const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes\r\n      enableRetry: true,\r\n      retryAttempts: 3,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching vehicle utilization metrics specifically\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with vehicle utilization data\r\n */\r\nexport const useVehicleUtilization = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['vehicle-utilization', filters],\r\n    async () => {\r\n      // Get utilization data from vehicle analytics endpoint\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.vehicles) {\r\n        filters.vehicles.forEach(vehicle =>\r\n          queryParams.append('vehicles', vehicle.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      const vehicleAnalytics = result.data || result;\r\n\r\n      // Return the utilization metrics from vehicle analytics\r\n      return vehicleAnalytics.utilizationMetrics || [];\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching vehicle maintenance schedule\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with maintenance schedule data\r\n */\r\nexport const useVehicleMaintenanceSchedule = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['vehicle-maintenance-schedule', filters],\r\n    async () => {\r\n      // Get maintenance data from vehicle analytics endpoint\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.vehicles) {\r\n        filters.vehicles.forEach(vehicle =>\r\n          queryParams.append('vehicles', vehicle.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      const vehicleAnalytics = result.data || result;\r\n\r\n      // Return the maintenance schedule from vehicle analytics\r\n      return vehicleAnalytics.maintenanceSchedule || [];\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching vehicle cost analytics\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with cost analytics data\r\n */\r\nexport const useVehicleCostAnalytics = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['vehicle-cost-analytics', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.vehicles) {\r\n        filters.vehicles.forEach(vehicle =>\r\n          queryParams.append('vehicles', vehicle.toString())\r\n        );\r\n      }\r\n\r\n      if (filters?.serviceTypes) {\r\n        filters.serviceTypes.forEach(type =>\r\n          queryParams.append('serviceTypes', type)\r\n        );\r\n      }\r\n\r\n      const url = `/reporting/vehicle-costs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAGD;AAAA;AACA;AAAA;;;AAeO,MAAM,sBAAsB,CAAC;IAClC,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAqB;KAAQ,EAC9B;QACE,MAAM,cAAc,IAAI;QAExB,IAAI,SAAS,WAAW;YACtB,uDAAuD;YACvD,MAAM,WACJ,QAAQ,SAAS,CAAC,IAAI,YAAY,OAC9B,QAAQ,SAAS,CAAC,IAAI,GACtB,IAAI,KAAK,QAAQ,SAAS,CAAC,IAAI;YACrC,MAAM,SACJ,QAAQ,SAAS,CAAC,EAAE,YAAY,OAC5B,QAAQ,SAAS,CAAC,EAAE,GACpB,IAAI,KAAK,QAAQ,SAAS,CAAC,EAAE;YAEnC,YAAY,MAAM,CAChB,kBACA,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,SAAS,WAAW;YAE9D,YAAY,MAAM,CAChB,gBACA,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO,WAAW;QAE5D;QAEA,IAAI,SAAS,UAAU;YACrB,QAAQ,QAAQ,CAAC,OAAO,CAAC,CAAA,UACvB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;QAEnD;QAEA,IAAI,SAAS,cAAc;YACzB,QAAQ,YAAY,CAAC,OAAO,CAAC,CAAA,OAC3B,YAAY,MAAM,CAAC,gBAAgB;QAEvC;QAEA,IAAI,SAAS,eAAe;YAC1B,QAAQ,aAAa,CAAC,OAAO,CAAC,CAAA,SAC5B,YAAY,MAAM,CAAC,iBAAiB;QAExC;QAEA,MAAM,MAAM,CAAC,6BAA6B,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QACxG,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACpC,OAAO,OAAO,IAAI,IAAI;IACxB,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;QACb,eAAe;IACjB;AAEJ;AAQO,MAAM,wBAAwB,CAAC;IACpC,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAuB;KAAQ,EAChC;QACE,uDAAuD;QACvD,MAAM,cAAc,IAAI;QAExB,IAAI,SAAS,WAAW;YACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;YAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;QAC3D;QAEA,IAAI,SAAS,UAAU;YACrB,QAAQ,QAAQ,CAAC,OAAO,CAAC,CAAA,UACvB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;QAEnD;QAEA,MAAM,MAAM,CAAC,6BAA6B,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QACxG,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACpC,MAAM,mBAAmB,OAAO,IAAI,IAAI;QAExC,wDAAwD;QACxD,OAAO,iBAAiB,kBAAkB,IAAI,EAAE;IAClD,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;AAQO,MAAM,gCAAgC,CAAC;IAC5C,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAgC;KAAQ,EACzC;QACE,uDAAuD;QACvD,MAAM,cAAc,IAAI;QAExB,IAAI,SAAS,WAAW;YACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;YAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;QAC3D;QAEA,IAAI,SAAS,UAAU;YACrB,QAAQ,QAAQ,CAAC,OAAO,CAAC,CAAA,UACvB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;QAEnD;QAEA,MAAM,MAAM,CAAC,6BAA6B,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QACxG,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACpC,MAAM,mBAAmB,OAAO,IAAI,IAAI;QAExC,yDAAyD;QACzD,OAAO,iBAAiB,mBAAmB,IAAI,EAAE;IACnD,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;AAQO,MAAM,0BAA0B,CAAC;IACtC,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAA0B;KAAQ,EACnC;QACE,MAAM,cAAc,IAAI;QAExB,IAAI,SAAS,WAAW;YACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;YAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;QAC3D;QAEA,IAAI,SAAS,UAAU;YACrB,QAAQ,QAAQ,CAAC,OAAO,CAAC,CAAA,UACvB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;QAEnD;QAEA,IAAI,SAAS,cAAc;YACzB,QAAQ,YAAY,CAAC,OAAO,CAAC,CAAA,OAC3B,YAAY,MAAM,CAAC,gBAAgB;QAEvC;QAEA,MAAM,MAAM,CAAC,wBAAwB,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QACnG,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACpC,OAAO,OAAO,IAAI,IAAI;IACxB,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ", "debugId": null}}, {"offset": {"line": 2597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/hooks/useEmployeeAnalytics.ts"], "sourcesContent": ["/**\r\n * @file useEmployeeAnalytics.ts\r\n * @description Hook for fetching employee analytics data following existing patterns\r\n */\r\n\r\nimport { useQuery } from '@tanstack/react-query';\r\nimport { useApiQuery } from '@/hooks/api';\r\nimport { apiClient } from '@/lib/api';\r\nimport type {\r\n  ReportingFilters,\r\n  EmployeeAnalytics,\r\n} from '../data/types/reporting';\r\n\r\n/**\r\n * Hook for fetching employee analytics data\r\n *\r\n * Follows existing patterns from useTaskAnalytics and useVehicleAnalytics.\r\n * Integrates with the established API and caching patterns.\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with employee analytics data\r\n */\r\nexport const useEmployeeAnalytics = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['employee-analytics', filters],\r\n    async (): Promise<EmployeeAnalytics> => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        // Defensive programming: Ensure dates are Date objects\r\n        const fromDate =\r\n          filters.dateRange.from instanceof Date\r\n            ? filters.dateRange.from\r\n            : new Date(filters.dateRange.from);\r\n        const toDate =\r\n          filters.dateRange.to instanceof Date\r\n            ? filters.dateRange.to\r\n            : new Date(filters.dateRange.to);\r\n\r\n        queryParams.append(\r\n          'dateRange.from',\r\n          fromDate.toISOString().split('T')[0] || fromDate.toISOString()\r\n        );\r\n        queryParams.append(\r\n          'dateRange.to',\r\n          toDate.toISOString().split('T')[0] || toDate.toISOString()\r\n        );\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      if (filters?.locations) {\r\n        filters.locations.forEach(location =>\r\n          queryParams.append('locations', location)\r\n        );\r\n      }\r\n\r\n      if (filters?.includeEmployeeMetrics) {\r\n        queryParams.append('includeEmployeeMetrics', 'true');\r\n      }\r\n\r\n      const url = `/reporting/employees/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes\r\n      enableRetry: true,\r\n      retryAttempts: 3,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching employee performance metrics specifically\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with employee performance data\r\n */\r\nexport const useEmployeePerformance = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['employee-performance', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/api/reporting/employee-performance${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const response = await fetch(url);\r\n      if (!response.ok) {\r\n        throw new Error(\r\n          `Failed to fetch employee performance: ${response.statusText}`\r\n        );\r\n      }\r\n      return response.json();\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching employee workload distribution\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with workload distribution data\r\n */\r\nexport const useEmployeeWorkload = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['employee-workload', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/api/reporting/employee-workload${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const response = await fetch(url);\r\n      if (!response.ok) {\r\n        throw new Error(\r\n          `Failed to fetch employee workload: ${response.statusText}`\r\n        );\r\n      }\r\n      return response.json();\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching employee task assignments\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with task assignment data\r\n */\r\nexport const useEmployeeTaskAssignments = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['employee-task-assignments', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/api/reporting/employee-tasks${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const response = await fetch(url);\r\n      if (!response.ok) {\r\n        throw new Error(\r\n          `Failed to fetch employee task assignments: ${response.statusText}`\r\n        );\r\n      }\r\n      return response.json();\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching employee availability metrics\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with availability metrics data\r\n */\r\nexport const useEmployeeAvailability = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['employee-availability', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/api/reporting/employee-availability${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const response = await fetch(url);\r\n      if (!response.ok) {\r\n        throw new Error(\r\n          `Failed to fetch employee availability: ${response.statusText}`\r\n        );\r\n      }\r\n      return response.json();\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAGD;AAAA;AACA;AAAA;;;AAeO,MAAM,uBAAuB,CAAC;IACnC,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAsB;KAAQ,EAC/B;QACE,MAAM,cAAc,IAAI;QAExB,IAAI,SAAS,WAAW;YACtB,uDAAuD;YACvD,MAAM,WACJ,QAAQ,SAAS,CAAC,IAAI,YAAY,OAC9B,QAAQ,SAAS,CAAC,IAAI,GACtB,IAAI,KAAK,QAAQ,SAAS,CAAC,IAAI;YACrC,MAAM,SACJ,QAAQ,SAAS,CAAC,EAAE,YAAY,OAC5B,QAAQ,SAAS,CAAC,EAAE,GACpB,IAAI,KAAK,QAAQ,SAAS,CAAC,EAAE;YAEnC,YAAY,MAAM,CAChB,kBACA,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,SAAS,WAAW;YAE9D,YAAY,MAAM,CAChB,gBACA,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO,WAAW;QAE5D;QAEA,IAAI,SAAS,WAAW;YACtB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;QAErD;QAEA,IAAI,SAAS,WAAW;YACtB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa;QAEpC;QAEA,IAAI,SAAS,wBAAwB;YACnC,YAAY,MAAM,CAAC,0BAA0B;QAC/C;QAEA,MAAM,MAAM,CAAC,8BAA8B,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QACzG,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACpC,OAAO,OAAO,IAAI,IAAI;IACxB,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;QACb,eAAe;IACjB;AAEJ;AAQO,MAAM,yBAAyB,CAAC;IACrC,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAwB;KAAQ,EACjC;QACE,MAAM,cAAc,IAAI;QAExB,IAAI,SAAS,WAAW;YACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;YAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;QAC3D;QAEA,IAAI,SAAS,WAAW;YACtB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;QAErD;QAEA,MAAM,MAAM,CAAC,mCAAmC,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QAC9G,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MACR,CAAC,sCAAsC,EAAE,SAAS,UAAU,EAAE;QAElE;QACA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;AAQO,MAAM,sBAAsB,CAAC;IAClC,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAqB;KAAQ,EAC9B;QACE,MAAM,cAAc,IAAI;QAExB,IAAI,SAAS,WAAW;YACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;YAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;QAC3D;QAEA,IAAI,SAAS,WAAW;YACtB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;QAErD;QAEA,MAAM,MAAM,CAAC,gCAAgC,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QAC3G,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MACR,CAAC,mCAAmC,EAAE,SAAS,UAAU,EAAE;QAE/D;QACA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;AAQO,MAAM,6BAA6B,CAAC;IACzC,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAA6B;KAAQ,EACtC;QACE,MAAM,cAAc,IAAI;QAExB,IAAI,SAAS,WAAW;YACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;YAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;QAC3D;QAEA,IAAI,SAAS,WAAW;YACtB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;QAErD;QAEA,MAAM,MAAM,CAAC,6BAA6B,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QACxG,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MACR,CAAC,2CAA2C,EAAE,SAAS,UAAU,EAAE;QAEvE;QACA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;AAQO,MAAM,0BAA0B,CAAC;IACtC,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAyB;KAAQ,EAClC;QACE,MAAM,cAAc,IAAI;QAExB,IAAI,SAAS,WAAW;YACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;YAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;QAC3D;QAEA,IAAI,SAAS,WAAW;YACtB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;QAErD;QAEA,MAAM,MAAM,CAAC,oCAAoC,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QAC/G,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MACR,CAAC,uCAAuC,EAAE,SAAS,UAAU,EAAE;QAEnE;QACA,OAAO,SAAS,IAAI;IACtB,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ", "debugId": null}}, {"offset": {"line": 2746, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/hooks/useCrossEntityAnalytics.ts"], "sourcesContent": ["/**\r\n * @file useCrossEntityAnalytics.ts\r\n * @description Hook for fetching cross-entity analytics data following existing patterns\r\n */\r\n\r\nimport { useQuery } from '@tanstack/react-query';\r\nimport { useApiQuery } from '@/hooks/api';\r\nimport { apiClient } from '@/lib/api';\r\nimport type {\r\n  ReportingFilters,\r\n  CrossEntityAnalytics,\r\n} from '../data/types/reporting';\r\n\r\n/**\r\n * Hook for fetching cross-entity analytics data\r\n *\r\n * Follows existing patterns from other analytics hooks.\r\n * Integrates with the established API and caching patterns.\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with cross-entity analytics data\r\n */\r\nexport const useCrossEntityAnalytics = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['cross-entity-analytics', filters],\r\n    async (): Promise<CrossEntityAnalytics> => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        // Defensive programming: Ensure dates are Date objects\r\n        const fromDate =\r\n          filters.dateRange.from instanceof Date\r\n            ? filters.dateRange.from\r\n            : new Date(filters.dateRange.from);\r\n        const toDate =\r\n          filters.dateRange.to instanceof Date\r\n            ? filters.dateRange.to\r\n            : new Date(filters.dateRange.to);\r\n\r\n        queryParams.append(\r\n          'dateRange.from',\r\n          fromDate.toISOString().split('T')[0] || fromDate.toISOString()\r\n        );\r\n        queryParams.append(\r\n          'dateRange.to',\r\n          toDate.toISOString().split('T')[0] || toDate.toISOString()\r\n        );\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      if (filters?.vehicles) {\r\n        filters.vehicles.forEach(vehicle =>\r\n          queryParams.append('vehicles', vehicle.toString())\r\n        );\r\n      }\r\n\r\n      if (filters?.locations) {\r\n        filters.locations.forEach(location =>\r\n          queryParams.append('locations', location)\r\n        );\r\n      }\r\n\r\n      // Note: includeCorrelations doesn't exist in ReportingFilters type\r\n      // if (filters?.includeCorrelations) {\r\n      //   queryParams.append('includeCorrelations', 'true');\r\n      // }\r\n\r\n      const url = `/reporting/cross-entity/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes\r\n      enableRetry: true,\r\n      retryAttempts: 3,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching employee-vehicle correlations specifically\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with employee-vehicle correlation data\r\n */\r\nexport const useEmployeeVehicleCorrelations = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['employee-vehicle-correlations', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      if (filters?.vehicles) {\r\n        filters.vehicles.forEach(vehicle =>\r\n          queryParams.append('vehicles', vehicle.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/reporting/employee-vehicle-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching task-delegation correlations\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with task-delegation correlation data\r\n */\r\nexport const useTaskDelegationCorrelations = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['task-delegation-correlations', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      const url = `/reporting/task-delegation-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching performance-workload correlations\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with performance-workload correlation data\r\n */\r\nexport const usePerformanceWorkloadCorrelations = (\r\n  filters?: ReportingFilters\r\n) => {\r\n  return useApiQuery(\r\n    ['performance-workload-correlations', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/reporting/performance-workload-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAGD;AAAA;AACA;AAAA;;;AAeO,MAAM,0BAA0B,CAAC;IACtC,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAA0B;KAAQ,EACnC;QACE,MAAM,cAAc,IAAI;QAExB,IAAI,SAAS,WAAW;YACtB,uDAAuD;YACvD,MAAM,WACJ,QAAQ,SAAS,CAAC,IAAI,YAAY,OAC9B,QAAQ,SAAS,CAAC,IAAI,GACtB,IAAI,KAAK,QAAQ,SAAS,CAAC,IAAI;YACrC,MAAM,SACJ,QAAQ,SAAS,CAAC,EAAE,YAAY,OAC5B,QAAQ,SAAS,CAAC,EAAE,GACpB,IAAI,KAAK,QAAQ,SAAS,CAAC,EAAE;YAEnC,YAAY,MAAM,CAChB,kBACA,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,SAAS,WAAW;YAE9D,YAAY,MAAM,CAChB,gBACA,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO,WAAW;QAE5D;QAEA,IAAI,SAAS,WAAW;YACtB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;QAErD;QAEA,IAAI,SAAS,UAAU;YACrB,QAAQ,QAAQ,CAAC,OAAO,CAAC,CAAA,UACvB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;QAEnD;QAEA,IAAI,SAAS,WAAW;YACtB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa;QAEpC;QAEA,mEAAmE;QACnE,sCAAsC;QACtC,uDAAuD;QACvD,IAAI;QAEJ,MAAM,MAAM,CAAC,iCAAiC,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QAC5G,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACpC,OAAO,OAAO,IAAI,IAAI;IACxB,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;QACb,eAAe;IACjB;AAEJ;AAQO,MAAM,iCAAiC,CAAC;IAC7C,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAiC;KAAQ,EAC1C;QACE,MAAM,cAAc,IAAI;QAExB,IAAI,SAAS,WAAW;YACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;YAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;QAC3D;QAEA,IAAI,SAAS,WAAW;YACtB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;QAErD;QAEA,IAAI,SAAS,UAAU;YACrB,QAAQ,QAAQ,CAAC,OAAO,CAAC,CAAA,UACvB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;QAEnD;QAEA,MAAM,MAAM,CAAC,wCAAwC,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QACnH,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACpC,OAAO,OAAO,IAAI,IAAI;IACxB,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;AAQO,MAAM,gCAAgC,CAAC;IAC5C,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAgC;KAAQ,EACzC;QACE,MAAM,cAAc,IAAI;QAExB,IAAI,SAAS,WAAW;YACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;YAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;QAC3D;QAEA,MAAM,MAAM,CAAC,uCAAuC,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QAClH,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACpC,OAAO,OAAO,IAAI,IAAI;IACxB,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;AAQO,MAAM,qCAAqC,CAChD;IAEA,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAqC;KAAQ,EAC9C;QACE,MAAM,cAAc,IAAI;QAExB,IAAI,SAAS,WAAW;YACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;YAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;QAC3D;QAEA,IAAI,SAAS,WAAW;YACtB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;QAErD;QAEA,MAAM,MAAM,CAAC,4CAA4C,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QACvH,MAAM,SAAU,MAAM,0IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACpC,OAAO,OAAO,IAAI,IAAI;IACxB,GACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ", "debugId": null}}, {"offset": {"line": 2865, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/components/DateRangePicker.tsx"], "sourcesContent": ["/**\r\n * @file DateRangePicker.tsx\r\n * @description Date range picker component for report filtering\r\n */\r\n\r\nimport React, { useState, useEffect, useMemo } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Calendar } from '@/components/ui/calendar';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { CalendarIcon, X, AlertTriangle, CheckCircle } from 'lucide-react';\r\nimport {\r\n  format,\r\n  subDays,\r\n  subWeeks,\r\n  subMonths,\r\n  startOfMonth,\r\n  endOfMonth,\r\n  startOfYear,\r\n  endOfYear,\r\n  differenceInDays,\r\n  isAfter,\r\n  isBefore,\r\n  isValid,\r\n} from 'date-fns';\r\nimport { cn } from '@/lib/utils';\r\n\r\n/**\r\n * Date range interface\r\n */\r\nexport interface DateRange {\r\n  from: Date;\r\n  to: Date;\r\n}\r\n\r\n/**\r\n * Predefined date range options with enhanced defaults\r\n */\r\nconst PRESET_RANGES = [\r\n  {\r\n    label: 'Today',\r\n    getValue: () => ({\r\n      from: new Date(),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'Yesterday',\r\n    getValue: () => {\r\n      const yesterday = subDays(new Date(), 1);\r\n      return {\r\n        from: yesterday,\r\n        to: yesterday,\r\n      };\r\n    },\r\n  },\r\n  {\r\n    label: 'Last 3 days',\r\n    getValue: () => ({\r\n      from: subDays(new Date(), 2),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'Last 7 days',\r\n    getValue: () => ({\r\n      from: subDays(new Date(), 6),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'Last 2 weeks',\r\n    getValue: () => ({\r\n      from: subWeeks(new Date(), 2),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'Last 30 days',\r\n    getValue: () => ({\r\n      from: subDays(new Date(), 29),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'This week',\r\n    getValue: () => ({\r\n      from: subDays(new Date(), new Date().getDay()),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'This month',\r\n    getValue: () => ({\r\n      from: startOfMonth(new Date()),\r\n      to: endOfMonth(new Date()),\r\n    }),\r\n  },\r\n  {\r\n    label: 'Last month',\r\n    getValue: () => {\r\n      const lastMonth = subMonths(new Date(), 1);\r\n      return {\r\n        from: startOfMonth(lastMonth),\r\n        to: endOfMonth(lastMonth),\r\n      };\r\n    },\r\n  },\r\n  {\r\n    label: 'Last 3 months',\r\n    getValue: () => ({\r\n      from: subMonths(new Date(), 3),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'This year',\r\n    getValue: () => ({\r\n      from: startOfYear(new Date()),\r\n      to: endOfYear(new Date()),\r\n    }),\r\n  },\r\n];\r\n\r\n/**\r\n * Validation result interface\r\n */\r\ninterface ValidationResult {\r\n  isValid: boolean;\r\n  message?: string;\r\n  type?: 'error' | 'warning' | 'info';\r\n}\r\n\r\ninterface DateRangePickerProps {\r\n  value?: DateRange | null;\r\n  onChange?: (range: DateRange | null) => void;\r\n  placeholder?: string;\r\n  className?: string;\r\n  disabled?: boolean;\r\n  maxDays?: number; // Maximum allowed days in range\r\n  minDays?: number; // Minimum required days in range\r\n  maxDate?: Date; // Maximum allowed end date\r\n  minDate?: Date; // Minimum allowed start date\r\n  showValidation?: boolean; // Show real-time validation feedback\r\n}\r\n\r\n/**\r\n * DateRangePicker Component\r\n *\r\n * Enhanced with real-time validation and performance optimization.\r\n * Follows SOLID principles with single responsibility for date range selection.\r\n */\r\nexport const DateRangePicker: React.FC<DateRangePickerProps> = ({\r\n  value,\r\n  onChange,\r\n  placeholder = 'Select date range',\r\n  className,\r\n  disabled = false,\r\n  maxDays = 365, // Default max 1 year\r\n  minDays = 1, // Default min 1 day\r\n  maxDate = new Date(), // Default to today\r\n  minDate = new Date(2020, 0, 1), // Default to 2020\r\n  showValidation = true,\r\n}) => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [tempRange, setTempRange] = useState<DateRange | null>(value || null);\r\n\r\n  // Real-time validation with memoization for performance\r\n  const validation = useMemo((): ValidationResult => {\r\n    if (!tempRange) {\r\n      return { isValid: true };\r\n    }\r\n\r\n    const { from, to } = tempRange;\r\n\r\n    // Validate date objects\r\n    if (!isValid(from) || !isValid(to)) {\r\n      return {\r\n        isValid: false,\r\n        message: 'Invalid date selected',\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    // Check date order\r\n    if (isAfter(from, to)) {\r\n      return {\r\n        isValid: false,\r\n        message: 'Start date must be before end date',\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    // Check date range limits\r\n    if (minDate && isBefore(from, minDate)) {\r\n      return {\r\n        isValid: false,\r\n        message: `Start date cannot be before ${format(minDate, 'MMM dd, yyyy')}`,\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    if (maxDate && isAfter(to, maxDate)) {\r\n      return {\r\n        isValid: false,\r\n        message: `End date cannot be after ${format(maxDate, 'MMM dd, yyyy')}`,\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    // Check range duration\r\n    const daysDiff = differenceInDays(to, from) + 1;\r\n\r\n    if (daysDiff < minDays) {\r\n      return {\r\n        isValid: false,\r\n        message: `Date range must be at least ${minDays} day${minDays > 1 ? 's' : ''}`,\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    if (daysDiff > maxDays) {\r\n      return {\r\n        isValid: false,\r\n        message: `Date range cannot exceed ${maxDays} days`,\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    // Performance warning for large ranges\r\n    if (daysDiff > 90) {\r\n      return {\r\n        isValid: true,\r\n        message: `Large date range (${daysDiff} days) may affect performance`,\r\n        type: 'warning',\r\n      };\r\n    }\r\n\r\n    return {\r\n      isValid: true,\r\n      message: `${daysDiff} day${daysDiff > 1 ? 's' : ''} selected`,\r\n      type: 'info',\r\n    };\r\n  }, [tempRange, maxDays, minDays, maxDate, minDate]);\r\n\r\n  // Update temp range when value changes\r\n  useEffect(() => {\r\n    setTempRange(value || null);\r\n  }, [value]);\r\n\r\n  /**\r\n   * Handle preset range selection with validation\r\n   */\r\n  const handlePresetSelect = (preset: (typeof PRESET_RANGES)[0]) => {\r\n    const range = preset.getValue();\r\n    setTempRange(range);\r\n\r\n    // Only call onChange if validation passes\r\n    const tempValidation = validateRange(range);\r\n    if (tempValidation.isValid) {\r\n      onChange?.(range);\r\n      setIsOpen(false);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Validate a date range\r\n   */\r\n  const validateRange = (range: DateRange | null): ValidationResult => {\r\n    if (!range) return { isValid: true };\r\n\r\n    const { from, to } = range;\r\n    const daysDiff = differenceInDays(to, from) + 1;\r\n\r\n    if (daysDiff > maxDays) {\r\n      return {\r\n        isValid: false,\r\n        message: `Date range cannot exceed ${maxDays} days`,\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    return { isValid: true };\r\n  };\r\n\r\n  /**\r\n   * Handle custom date selection with enhanced logic\r\n   */\r\n  const handleDateSelect = (date: Date | undefined) => {\r\n    if (!date || disabled) return;\r\n\r\n    // Validate date is within allowed range\r\n    if (minDate && isBefore(date, minDate)) return;\r\n    if (maxDate && isAfter(date, maxDate)) return;\r\n\r\n    if (!tempRange || (tempRange.from && tempRange.to)) {\r\n      // Start new range\r\n      const newRange = { from: date, to: date };\r\n      setTempRange(newRange);\r\n    } else if (tempRange.from && !tempRange.to) {\r\n      // Complete the range\r\n      const newRange = {\r\n        from: date < tempRange.from ? date : tempRange.from,\r\n        to: date < tempRange.from ? tempRange.from : date,\r\n      };\r\n      setTempRange(newRange);\r\n\r\n      // Only call onChange if validation passes\r\n      const tempValidation = validateRange(newRange);\r\n      if (tempValidation.isValid) {\r\n        onChange?.(newRange);\r\n        setIsOpen(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handle range clear\r\n   */\r\n  const handleClear = () => {\r\n    setTempRange(null);\r\n    onChange?.(null);\r\n    setIsOpen(false);\r\n  };\r\n\r\n  /**\r\n   * Format display text\r\n   */\r\n  const getDisplayText = () => {\r\n    if (!value) return placeholder;\r\n\r\n    if (value.from.toDateString() === value.to.toDateString()) {\r\n      return format(value.from, 'MMM dd, yyyy');\r\n    }\r\n\r\n    return `${format(value.from, 'MMM dd, yyyy')} - ${format(value.to, 'MMM dd, yyyy')}`;\r\n  };\r\n\r\n  return (\r\n    <div className={cn('relative', className)}>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"outline\"\r\n            className={cn(\r\n              'w-full justify-start text-left font-normal',\r\n              !value && 'text-muted-foreground'\r\n            )}\r\n            disabled={disabled}\r\n          >\r\n            <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n            {getDisplayText()}\r\n            {value && (\r\n              <Badge variant=\"secondary\" className=\"ml-auto\">\r\n                {Math.ceil(\r\n                  (value.to.getTime() - value.from.getTime()) /\r\n                    (1000 * 60 * 60 * 24)\r\n                ) + 1}{' '}\r\n                days\r\n              </Badge>\r\n            )}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-auto max-w-4xl p-0\" align=\"start\">\r\n          <div className=\"flex\">\r\n            {/* Preset Options */}\r\n            <div className=\"border-r p-4 space-y-2 min-w-[160px]\">\r\n              <h4 className=\"font-medium text-sm text-gray-900 mb-3\">\r\n                Quick Select\r\n              </h4>\r\n              {PRESET_RANGES.map(preset => (\r\n                <Button\r\n                  key={preset.label}\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"w-full justify-start text-xs h-8 px-2\"\r\n                  onClick={() => handlePresetSelect(preset)}\r\n                >\r\n                  {preset.label}\r\n                </Button>\r\n              ))}\r\n\r\n              {value && (\r\n                <>\r\n                  <div className=\"border-t pt-2 mt-3\">\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"sm\"\r\n                      className=\"w-full justify-start text-sm text-red-600 hover:text-red-700\"\r\n                      onClick={handleClear}\r\n                    >\r\n                      <X className=\"mr-2 h-3 w-3\" />\r\n                      Clear\r\n                    </Button>\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n\r\n            {/* Enhanced Calendar with Range Mode */}\r\n            <div className=\"p-4 min-w-[600px]\">\r\n              <Calendar\r\n                mode=\"range\"\r\n                selected={tempRange || undefined}\r\n                onSelect={range => {\r\n                  if (range && range.from) {\r\n                    const dateRange = {\r\n                      from: range.from,\r\n                      to: range.to || range.from,\r\n                    };\r\n                    setTempRange(dateRange);\r\n                    // Auto-apply valid ranges\r\n                    const tempValidation = validateRange(dateRange);\r\n                    if (\r\n                      tempValidation.isValid &&\r\n                      dateRange.from &&\r\n                      dateRange.to\r\n                    ) {\r\n                      onChange?.(dateRange);\r\n                      setIsOpen(false);\r\n                    }\r\n                  } else {\r\n                    setTempRange(null);\r\n                  }\r\n                }}\r\n                numberOfMonths={2}\r\n                className=\"rounded-md border-0\"\r\n                disabled={[\r\n                  // Disable dates outside the allowed range\r\n                  ...(minDate ? [{ before: minDate }] : []),\r\n                  ...(maxDate ? [{ after: maxDate }] : []),\r\n                ]}\r\n                showOutsideDays\r\n                fixedWeeks\r\n              />\r\n\r\n              {tempRange && (\r\n                <div className=\"mt-4 p-3 bg-gray-50 rounded-lg\">\r\n                  <h5 className=\"font-medium text-sm text-gray-900 mb-2\">\r\n                    Selected Range\r\n                  </h5>\r\n                  <div className=\"space-y-1 text-sm text-gray-600\">\r\n                    <div>From: {format(tempRange.from, 'MMM dd, yyyy')}</div>\r\n                    <div>To: {format(tempRange.to, 'MMM dd, yyyy')}</div>\r\n                    <div className=\"text-xs text-gray-500\">\r\n                      {Math.ceil(\r\n                        (tempRange.to.getTime() - tempRange.from.getTime()) /\r\n                          (1000 * 60 * 60 * 24)\r\n                      ) + 1}{' '}\r\n                      days\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Real-time Validation Feedback */}\r\n                  {showValidation && validation.message && (\r\n                    <Alert\r\n                      className={cn(\r\n                        'mt-3 py-2 px-3',\r\n                        validation.type === 'error' &&\r\n                          'border-red-200 bg-red-50',\r\n                        validation.type === 'warning' &&\r\n                          'border-yellow-200 bg-yellow-50',\r\n                        validation.type === 'info' &&\r\n                          'border-blue-200 bg-blue-50'\r\n                      )}\r\n                    >\r\n                      <div className=\"flex items-center gap-2\">\r\n                        {validation.type === 'error' && (\r\n                          <AlertTriangle className=\"h-3 w-3 text-red-600\" />\r\n                        )}\r\n                        {validation.type === 'warning' && (\r\n                          <AlertTriangle className=\"h-3 w-3 text-yellow-600\" />\r\n                        )}\r\n                        {validation.type === 'info' && (\r\n                          <CheckCircle className=\"h-3 w-3 text-blue-600\" />\r\n                        )}\r\n                        <AlertDescription\r\n                          className={cn(\r\n                            'text-xs',\r\n                            validation.type === 'error' && 'text-red-700',\r\n                            validation.type === 'warning' && 'text-yellow-700',\r\n                            validation.type === 'info' && 'text-blue-700'\r\n                          )}\r\n                        >\r\n                          {validation.message}\r\n                        </AlertDescription>\r\n                      </div>\r\n                    </Alert>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AACA;AACA;AAYA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAAA;;;;;;;;;;;AAUA;;CAEC,GACD,MAAM,gBAAgB;IACpB;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,IAAI;gBACV,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU;YACR,MAAM,YAAY,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;YACtC,OAAO;gBACL,MAAM;gBACN,IAAI;YACN;QACF;IACF;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;gBAC1B,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;gBAC1B,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,QAAQ;gBAC3B,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;gBAC1B,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ,IAAI,OAAO,MAAM;gBAC3C,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,4IAAA,CAAA,eAAY,AAAD,EAAE,IAAI;gBACvB,IAAI,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD,EAAE,IAAI;YACrB,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU;YACR,MAAM,YAAY,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE,IAAI,QAAQ;YACxC,OAAO;gBACL,MAAM,CAAA,GAAA,4IAAA,CAAA,eAAY,AAAD,EAAE;gBACnB,IAAI,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD,EAAE;YACjB;QACF;IACF;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE,IAAI,QAAQ;gBAC5B,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,IAAI;gBACtB,IAAI,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YACpB,CAAC;IACH;CACD;AA8BM,MAAM,kBAAkD,CAAC,EAC9D,KAAK,EACL,QAAQ,EACR,cAAc,mBAAmB,EACjC,SAAS,EACT,WAAW,KAAK,EAChB,UAAU,GAAG,EACb,UAAU,CAAC,EACX,UAAU,IAAI,MAAM,EACpB,UAAU,IAAI,KAAK,MAAM,GAAG,EAAE,EAC9B,iBAAiB,IAAI,EACtB;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,SAAS;IAEtE,wDAAwD;IACxD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,IAAI,CAAC,WAAW;YACd,OAAO;gBAAE,SAAS;YAAK;QACzB;QAEA,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG;QAErB,wBAAwB;QACxB,IAAI,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,SAAS,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,KAAK;YAClC,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QACF;QAEA,mBAAmB;QACnB,IAAI,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,MAAM,KAAK;YACrB,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,MAAM;YACR;QACF;QAEA,0BAA0B;QAC1B,IAAI,WAAW,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,UAAU;YACtC,OAAO;gBACL,SAAS;gBACT,SAAS,CAAC,4BAA4B,EAAE,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,iBAAiB;gBACzE,MAAM;YACR;QACF;QAEA,IAAI,WAAW,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,UAAU;YACnC,OAAO;gBACL,SAAS;gBACT,SAAS,CAAC,yBAAyB,EAAE,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,iBAAiB;gBACtE,MAAM;YACR;QACF;QAEA,uBAAuB;QACvB,MAAM,WAAW,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,QAAQ;QAE9C,IAAI,WAAW,SAAS;YACtB,OAAO;gBACL,SAAS;gBACT,SAAS,CAAC,4BAA4B,EAAE,QAAQ,IAAI,EAAE,UAAU,IAAI,MAAM,IAAI;gBAC9E,MAAM;YACR;QACF;QAEA,IAAI,WAAW,SAAS;YACtB,OAAO;gBACL,SAAS;gBACT,SAAS,CAAC,yBAAyB,EAAE,QAAQ,KAAK,CAAC;gBACnD,MAAM;YACR;QACF;QAEA,uCAAuC;QACvC,IAAI,WAAW,IAAI;YACjB,OAAO;gBACL,SAAS;gBACT,SAAS,CAAC,kBAAkB,EAAE,SAAS,6BAA6B,CAAC;gBACrE,MAAM;YACR;QACF;QAEA,OAAO;YACL,SAAS;YACT,SAAS,GAAG,SAAS,IAAI,EAAE,WAAW,IAAI,MAAM,GAAG,SAAS,CAAC;YAC7D,MAAM;QACR;IACF,GAAG;QAAC;QAAW;QAAS;QAAS;QAAS;KAAQ;IAElD,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa,SAAS;IACxB,GAAG;QAAC;KAAM;IAEV;;GAEC,GACD,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,OAAO,QAAQ;QAC7B,aAAa;QAEb,0CAA0C;QAC1C,MAAM,iBAAiB,cAAc;QACrC,IAAI,eAAe,OAAO,EAAE;YAC1B,WAAW;YACX,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,OAAO,OAAO;YAAE,SAAS;QAAK;QAEnC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG;QACrB,MAAM,WAAW,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,QAAQ;QAE9C,IAAI,WAAW,SAAS;YACtB,OAAO;gBACL,SAAS;gBACT,SAAS,CAAC,yBAAyB,EAAE,QAAQ,KAAK,CAAC;gBACnD,MAAM;YACR;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,QAAQ,UAAU;QAEvB,wCAAwC;QACxC,IAAI,WAAW,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,UAAU;QACxC,IAAI,WAAW,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,MAAM,UAAU;QAEvC,IAAI,CAAC,aAAc,UAAU,IAAI,IAAI,UAAU,EAAE,EAAG;YAClD,kBAAkB;YAClB,MAAM,WAAW;gBAAE,MAAM;gBAAM,IAAI;YAAK;YACxC,aAAa;QACf,OAAO,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YAC1C,qBAAqB;YACrB,MAAM,WAAW;gBACf,MAAM,OAAO,UAAU,IAAI,GAAG,OAAO,UAAU,IAAI;gBACnD,IAAI,OAAO,UAAU,IAAI,GAAG,UAAU,IAAI,GAAG;YAC/C;YACA,aAAa;YAEb,0CAA0C;YAC1C,MAAM,iBAAiB,cAAc;YACrC,IAAI,eAAe,OAAO,EAAE;gBAC1B,WAAW;gBACX,UAAU;YACZ;QACF;IACF;IAEA;;GAEC,GACD,MAAM,cAAc;QAClB,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IAEA;;GAEC,GACD,MAAM,iBAAiB;QACrB,IAAI,CAAC,OAAO,OAAO;QAEnB,IAAI,MAAM,IAAI,CAAC,YAAY,OAAO,MAAM,EAAE,CAAC,YAAY,IAAI;YACzD,OAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EAAE;QAC5B;QAEA,OAAO,GAAG,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EAAE,gBAAgB,GAAG,EAAE,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,EAAE,EAAE,iBAAiB;IACtF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kBAC7B,cAAA,8OAAC,mIAAA,CAAA,UAAO;YAAC,MAAM;YAAQ,cAAc;;8BACnC,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,SAAS;wBAEZ,UAAU;;0CAEV,8OAAC,8MAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BACvB;4BACA,uBACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;;oCAClC,KAAK,IAAI,CACR,CAAC,MAAM,EAAE,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,OAAO,EAAE,IACxC,CAAC,OAAO,KAAK,KAAK,EAAE,KACpB;oCAAG;oCAAI;;;;;;;;;;;;;;;;;;8BAMnB,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAuB,OAAM;8BACrD,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;oCAGtD,cAAc,GAAG,CAAC,CAAA,uBACjB,8OAAC,kIAAA,CAAA,SAAM;4CAEL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,mBAAmB;sDAEjC,OAAO,KAAK;2CANR,OAAO,KAAK;;;;;oCAUpB,uBACC;kDACE,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;;kEAET,8OAAC,4LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CASxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,WAAQ;wCACP,MAAK;wCACL,UAAU,aAAa;wCACvB,UAAU,CAAA;4CACR,IAAI,SAAS,MAAM,IAAI,EAAE;gDACvB,MAAM,YAAY;oDAChB,MAAM,MAAM,IAAI;oDAChB,IAAI,MAAM,EAAE,IAAI,MAAM,IAAI;gDAC5B;gDACA,aAAa;gDACb,0BAA0B;gDAC1B,MAAM,iBAAiB,cAAc;gDACrC,IACE,eAAe,OAAO,IACtB,UAAU,IAAI,IACd,UAAU,EAAE,EACZ;oDACA,WAAW;oDACX,UAAU;gDACZ;4CACF,OAAO;gDACL,aAAa;4CACf;wCACF;wCACA,gBAAgB;wCAChB,WAAU;wCACV,UAAU;4CACR,0CAA0C;+CACtC,UAAU;gDAAC;oDAAE,QAAQ;gDAAQ;6CAAE,GAAG,EAAE;+CACpC,UAAU;gDAAC;oDAAE,OAAO;gDAAQ;6CAAE,GAAG,EAAE;yCACxC;wCACD,eAAe;wCACf,UAAU;;;;;;oCAGX,2BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DAGvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAI;4DAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,UAAU,IAAI,EAAE;;;;;;;kEACnC,8OAAC;;4DAAI;4DAAK,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,UAAU,EAAE,EAAE;;;;;;;kEAC/B,8OAAC;wDAAI,WAAU;;4DACZ,KAAK,IAAI,CACR,CAAC,UAAU,EAAE,CAAC,OAAO,KAAK,UAAU,IAAI,CAAC,OAAO,EAAE,IAChD,CAAC,OAAO,KAAK,KAAK,EAAE,KACpB;4DAAG;4DAAI;;;;;;;;;;;;;4CAMd,kBAAkB,WAAW,OAAO,kBACnC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,kBACA,WAAW,IAAI,KAAK,WAClB,4BACF,WAAW,IAAI,KAAK,aAClB,kCACF,WAAW,IAAI,KAAK,UAClB;0DAGJ,cAAA,8OAAC;oDAAI,WAAU;;wDACZ,WAAW,IAAI,KAAK,yBACnB,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAE1B,WAAW,IAAI,KAAK,2BACnB,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAE1B,WAAW,IAAI,KAAK,wBACnB,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEAEzB,8OAAC,iIAAA,CAAA,mBAAgB;4DACf,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,WACA,WAAW,IAAI,KAAK,WAAW,gBAC/B,WAAW,IAAI,KAAK,aAAa,mBACjC,WAAW,IAAI,KAAK,UAAU;sEAG/B,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa7C", "debugId": null}}]}