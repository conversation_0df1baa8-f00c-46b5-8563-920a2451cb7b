{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/error-boundaries/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ErrorInfo, ReactNode } from 'react';\r\n\r\nimport { <PERSON><PERSON><PERSON>riangle, RefreshCw } from 'lucide-react';\r\nimport React, { Component } from 'react';\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Button } from '@/components/ui/button';\r\n\r\ninterface Props {\r\n  children: ReactNode;\r\n  description?: string;\r\n  fallback?: ReactNode;\r\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\r\n  resetLabel?: string;\r\n  title?: string;\r\n}\r\n\r\ninterface State {\r\n  error: Error | null;\r\n  errorInfo: ErrorInfo | null;\r\n  hasError: boolean;\r\n}\r\n\r\n/**\r\n * Generic Error Boundary component\r\n * Catches errors in its child component tree and displays a fallback UI\r\n */\r\nclass ErrorBoundary extends Component<Props, State> {\r\n  constructor(props: Props) {\r\n    super(props);\r\n    this.state = {\r\n      error: null,\r\n      errorInfo: null,\r\n      hasError: false,\r\n    };\r\n  }\r\n\r\n  static getDerivedStateFromError(error: Error): Partial<State> {\r\n    // Update state so the next render will show the fallback UI\r\n    return {\r\n      error,\r\n      hasError: true,\r\n    };\r\n  }\r\n\r\n  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {\r\n    // Update state with error info for detailed reporting\r\n    this.setState({\r\n      errorInfo,\r\n    });\r\n\r\n    // Log the error\r\n    console.error('Error caught by ErrorBoundary:', error);\r\n    console.error('Component stack:', errorInfo.componentStack);\r\n\r\n    // Call onError prop if provided\r\n    if (this.props.onError) {\r\n      this.props.onError(error, errorInfo);\r\n    }\r\n\r\n    // In a production app, you would send this to a monitoring service\r\n    // Example: errorReportingService.captureError(error, errorInfo);\r\n  }\r\n\r\n  handleRetry = (): void => {\r\n    // Reset the error boundary state to trigger a re-render\r\n    this.setState({\r\n      error: null,\r\n      errorInfo: null,\r\n      hasError: false,\r\n    });\r\n  };\r\n\r\n  render(): ReactNode {\r\n    const {\r\n      description = 'An unexpected error occurred.',\r\n      resetLabel = 'Try Again',\r\n      title = 'Something went wrong',\r\n    } = this.props;\r\n\r\n    if (this.state.hasError) {\r\n      // If a custom fallback is provided, use it\r\n      if (this.props.fallback) {\r\n        return this.props.fallback;\r\n      }\r\n\r\n      // Otherwise, use the default error UI\r\n      return (\r\n        <Alert className=\"my-4\" variant=\"destructive\">\r\n          <AlertTriangle className=\"mr-2 size-4\" />\r\n          <AlertTitle className=\"text-lg font-semibold\">{title}</AlertTitle>\r\n          <AlertDescription className=\"mt-2\">\r\n            <p className=\"mb-2\">{this.state.error?.message || description}</p>\r\n            {process.env.NODE_ENV !== 'production' && this.state.errorInfo && (\r\n              <details className=\"mt-2 text-xs\">\r\n                <summary>Error details</summary>\r\n                <pre className=\"mt-2 max-h-[200px] overflow-auto whitespace-pre-wrap rounded bg-slate-100 p-2 dark:bg-slate-900\">\r\n                  {this.state.error?.stack}\r\n                  {'\\n\\nComponent Stack:\\n'}\r\n                  {this.state.errorInfo.componentStack}\r\n                </pre>\r\n              </details>\r\n            )}\r\n            <Button\r\n              className=\"mt-4\"\r\n              onClick={this.handleRetry}\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n            >\r\n              <RefreshCw className=\"mr-2 size-4\" />\r\n              {resetLabel}\r\n            </Button>\r\n          </AlertDescription>\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // If there's no error, render the children\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\nexport default ErrorBoundary;\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AACA;AAEA;AACA;AARA;;;;;;AAyBA;;;CAGC,GACD,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YACX,OAAO;YACP,WAAW;YACX,UAAU;QACZ;IACF;IAEA,OAAO,yBAAyB,KAAY,EAAkB;QAC5D,4DAA4D;QAC5D,OAAO;YACL;YACA,UAAU;QACZ;IACF;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAQ;QAC1D,sDAAsD;QACtD,IAAI,CAAC,QAAQ,CAAC;YACZ;QACF;QAEA,gBAAgB;QAChB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,QAAQ,KAAK,CAAC,oBAAoB,UAAU,cAAc;QAE1D,gCAAgC;QAChC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;IAEA,mEAAmE;IACnE,iEAAiE;IACnE;IAEA,cAAc;QACZ,wDAAwD;QACxD,IAAI,CAAC,QAAQ,CAAC;YACZ,OAAO;YACP,WAAW;YACX,UAAU;QACZ;IACF,EAAE;IAEF,SAAoB;QAClB,MAAM,EACJ,cAAc,+BAA+B,EAC7C,aAAa,WAAW,EACxB,QAAQ,sBAAsB,EAC/B,GAAG,IAAI,CAAC,KAAK;QAEd,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,2CAA2C;YAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,sCAAsC;YACtC,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBAAC,WAAU;gBAAO,SAAQ;;kCAC9B,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,8OAAC,iIAAA,CAAA,aAAU;wBAAC,WAAU;kCAAyB;;;;;;kCAC/C,8OAAC,iIAAA,CAAA,mBAAgB;wBAAC,WAAU;;0CAC1B,8OAAC;gCAAE,WAAU;0CAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW;;;;;;4BACjD,oDAAyB,gBAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,kBAC5D,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;kDAAQ;;;;;;kDACT,8OAAC;wCAAI,WAAU;;4CACZ,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;4CAClB;4CACA,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;0CAI1C,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,IAAI,CAAC,WAAW;gCACzB,MAAK;gCACL,SAAQ;;kDAER,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCACpB;;;;;;;;;;;;;;;;;;;QAKX;QAEA,2CAA2C;QAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/error-boundaries/ServiceRecordsErrorBoundary.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ReactNode } from 'react';\r\n\r\nimport React from 'react';\r\n\r\nimport ErrorBoundary from './ErrorBoundary';\r\n\r\ninterface ServiceRecordsErrorBoundaryProps {\r\n  children: ReactNode;\r\n  fallback?: ReactNode;\r\n}\r\n\r\n/**\r\n * Specialized error boundary for service records components\r\n * Uses the generic ErrorBoundary with service records specific messaging\r\n */\r\nconst ServiceRecordsErrorBoundary: React.FC<\r\n  ServiceRecordsErrorBoundaryProps\r\n> = ({ children, fallback }) => {\r\n  return (\r\n    <ErrorBoundary\r\n      description=\"An unexpected error occurred while loading service records.\"\r\n      fallback={fallback}\r\n      onError={(error, errorInfo) => {\r\n        // Log the error with service records context\r\n        console.error('ServiceRecords component error:', error);\r\n        console.error('Component stack:', errorInfo.componentStack);\r\n\r\n        // In a production app, you would send this to a monitoring service\r\n        // Example: errorReportingService.captureError(error, {\r\n        //   context: 'ServiceRecords',\r\n        //   componentStack: errorInfo.componentStack\r\n        // });\r\n      }}\r\n      resetLabel=\"Try Again\"\r\n      title=\"Error Loading Service Records\"\r\n    >\r\n      {children}\r\n    </ErrorBoundary>\r\n  );\r\n};\r\n\r\nexport default ServiceRecordsErrorBoundary;\r\n"], "names": [], "mappings": ";;;;AAMA;AANA;;;AAaA;;;CAGC,GACD,MAAM,8BAEF,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE;IACzB,qBACE,8OAAC,0JAAA,CAAA,UAAa;QACZ,aAAY;QACZ,UAAU;QACV,SAAS,CAAC,OAAO;YACf,6CAA6C;YAC7C,QAAQ,KAAK,CAAC,mCAAmC;YACjD,QAAQ,KAAK,CAAC,oBAAoB,UAAU,cAAc;QAE1D,mEAAmE;QACnE,uDAAuD;QACvD,+BAA+B;QAC/B,6CAA6C;QAC7C,MAAM;QACR;QACA,YAAW;QACX,OAAM;kBAEL;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/action-button.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { Loader2 } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport type { ButtonProps } from '@/components/ui/button';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport interface ActionButtonProps extends Omit<ButtonProps, 'variant'> {\r\n  /**\r\n   * The type of action this button represents\r\n   * - primary: Main actions (Create, Save, Submit)\r\n   * - secondary: Alternative actions (View, Edit)\r\n   * - tertiary: Optional actions (Cancel, Back)\r\n   * - danger: Destructive actions (Delete, Remove)\r\n   */\r\n  actionType?: ActionType;\r\n\r\n  /**\r\n   * Icon to display before the button text\r\n   * Should be a Lucide icon with consistent sizing (h-4 w-4)\r\n   */\r\n  icon?: React.ReactNode;\r\n\r\n  /**\r\n   * Whether the button is in a loading state\r\n   */\r\n  isLoading?: boolean;\r\n\r\n  /**\r\n   * Text to display when button is loading\r\n   * If not provided, will use children\r\n   */\r\n  loadingText?: string;\r\n}\r\n\r\nexport type ActionType = 'danger' | 'primary' | 'secondary' | 'tertiary';\r\n\r\n/**\r\n * ActionButton component for consistent action styling across the application\r\n *\r\n * @example\r\n * <ActionButton actionType=\"primary\" icon={<PlusCircle />}>\r\n *   Add New\r\n * </ActionButton>\r\n */\r\nexport const ActionButton = React.forwardRef<\r\n  HTMLButtonElement,\r\n  ActionButtonProps\r\n>(\r\n  (\r\n    {\r\n      actionType = 'primary',\r\n      asChild = false,\r\n      children,\r\n      className,\r\n      disabled,\r\n      icon,\r\n      isLoading = false,\r\n      loadingText,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    // Map action types to shadcn/ui button variants and additional styling\r\n    const actionStyles: Record<\r\n      ActionType,\r\n      { className: string; variant: ButtonProps['variant']; }\r\n    > = {\r\n      danger: {\r\n        className: 'shadow-md',\r\n        variant: 'destructive',\r\n      },\r\n      primary: {\r\n        className: 'shadow-md',\r\n        variant: 'default',\r\n      },\r\n      secondary: {\r\n        className: '',\r\n        variant: 'secondary',\r\n      },\r\n      tertiary: {\r\n        className: '',\r\n        variant: 'outline',\r\n      },\r\n    };\r\n\r\n    const { className: actionClassName, variant } = actionStyles[actionType];\r\n\r\n    // const Comp = asChild ? Slot : \"button\"; // This was for an older structure, Button handles asChild now\r\n\r\n    return (\r\n      <Button\r\n        asChild={asChild} // This is passed to the underlying shadcn Button\r\n        className={cn(actionClassName, className)}\r\n        disabled={isLoading || disabled}\r\n        ref={ref}\r\n        variant={variant}\r\n        {...props}\r\n      >\r\n        {isLoading ? (\r\n          <span className=\"inline-flex items-center\">\r\n            {' '}\r\n            {/* Replaced Fragment with span */}\r\n            <Loader2 className=\"mr-2 size-4 animate-spin\" />\r\n            {loadingText || children}\r\n          </span>\r\n        ) : (\r\n          <span className=\"inline-flex items-center\">\r\n            {' '}\r\n            {/* Replaced Fragment with span */}\r\n            {icon && <span className=\"mr-2\">{icon}</span>}\r\n            {children}\r\n          </span>\r\n        )}\r\n      </Button>\r\n    );\r\n  }\r\n);\r\n\r\nActionButton.displayName = 'ActionButton';\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAIA;AACA;AAAA;AATA;;;;;;AAiDO,MAAM,6BAAe,qMAAA,CAAA,UAAK,CAAC,UAAU,CAI1C,CACE,EACE,aAAa,SAAS,EACtB,UAAU,KAAK,EACf,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,YAAY,KAAK,EACjB,WAAW,EACX,GAAG,OACJ,EACD;IAEA,uEAAuE;IACvE,MAAM,eAGF;QACF,QAAQ;YACN,WAAW;YACX,SAAS;QACX;QACA,SAAS;YACP,WAAW;YACX,SAAS;QACX;QACA,WAAW;YACT,WAAW;YACX,SAAS;QACX;QACA,UAAU;YACR,WAAW;YACX,SAAS;QACX;IACF;IAEA,MAAM,EAAE,WAAW,eAAe,EAAE,OAAO,EAAE,GAAG,YAAY,CAAC,WAAW;IAExE,yGAAyG;IAEzG,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC/B,UAAU,aAAa;QACvB,KAAK;QACL,SAAS;QACR,GAAG,KAAK;kBAER,0BACC,8OAAC;YAAK,WAAU;;gBACb;8BAED,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBAClB,eAAe;;;;;;iCAGlB,8OAAC;YAAK,WAAU;;gBACb;gBAEA,sBAAQ,8OAAC;oBAAK,WAAU;8BAAQ;;;;;;gBAChC;;;;;;;;;;;;AAKX;AAGF,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/services/toastService.ts"], "sourcesContent": ["/**\r\n * Unified Generic Toast Service - Following SRP and DRY principles\r\n *\r\n * This service provides a consistent interface for all toast notifications\r\n * across the application, eliminating code duplication and ensuring\r\n * consistent messaging patterns using generics.\r\n */\r\n\r\nimport { toast } from '@/hooks/utils/use-toast';\r\n\r\nexport type ToastVariant = 'default' | 'destructive';\r\n\r\nexport interface ToastOptions {\r\n  title?: string | undefined;\r\n  description?: string | undefined;\r\n  variant?: ToastVariant | undefined;\r\n  duration?: number | undefined;\r\n}\r\n\r\n/**\r\n * Configuration interface for entity-specific toast messages\r\n */\r\nexport interface EntityToastConfig<T = any> {\r\n  entityName: string; // e.g., \"Employee\", \"Vehicle\", \"Service Record\"\r\n  getDisplayName: (entity: T) => string; // Function to get display name from entity\r\n  messages: {\r\n    created: {\r\n      title: string;\r\n      description: (displayName: string) => string;\r\n    };\r\n    updated: {\r\n      title: string;\r\n      description: (displayName: string) => string;\r\n    };\r\n    deleted: {\r\n      title: string;\r\n      description: (displayName: string) => string;\r\n    };\r\n    creationError: {\r\n      title: string;\r\n      description: (error: string) => string;\r\n    };\r\n    updateError: {\r\n      title: string;\r\n      description: (error: string) => string;\r\n    };\r\n    deletionError: {\r\n      title: string;\r\n      description: (error: string) => string;\r\n    };\r\n  };\r\n}\r\n\r\n/**\r\n * Base toast service class following SRP\r\n */\r\nclass ToastService {\r\n  /**\r\n   * Show a generic toast notification\r\n   */\r\n  show(options: ToastOptions) {\r\n    return toast({\r\n      title: options.title,\r\n      description: options.description,\r\n      variant: options.variant || 'default',\r\n      ...(options.duration && { duration: options.duration }),\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show a success toast notification\r\n   */\r\n  success(title: string, description?: string) {\r\n    return this.show({\r\n      title,\r\n      description,\r\n      variant: 'default',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show an error toast notification\r\n   */\r\n  error(title: string, description?: string) {\r\n    return this.show({\r\n      title,\r\n      description,\r\n      variant: 'destructive',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show an info toast notification\r\n   */\r\n  info(title: string, description?: string) {\r\n    return this.show({\r\n      title,\r\n      description,\r\n      variant: 'default',\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * Generic Entity Toast Service - Works with any entity type T\r\n */\r\nexport class GenericEntityToastService<T = any> extends ToastService {\r\n  private config: EntityToastConfig<T>;\r\n\r\n  constructor(config: EntityToastConfig<T>) {\r\n    super();\r\n    this.config = config;\r\n  }\r\n\r\n  /**\r\n   * Show entity created success toast\r\n   */\r\n  entityCreated(entity: T) {\r\n    const displayName = this.config.getDisplayName(entity);\r\n    return this.success(\r\n      this.config.messages.created.title,\r\n      this.config.messages.created.description(displayName)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity updated success toast\r\n   */\r\n  entityUpdated(entity: T) {\r\n    const displayName = this.config.getDisplayName(entity);\r\n    return this.success(\r\n      this.config.messages.updated.title,\r\n      this.config.messages.updated.description(displayName)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity deleted success toast\r\n   */\r\n  entityDeleted(entity: T) {\r\n    const displayName = this.config.getDisplayName(entity);\r\n    return this.success(\r\n      this.config.messages.deleted.title,\r\n      this.config.messages.deleted.description(displayName)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity creation error toast\r\n   */\r\n  entityCreationError(error: string) {\r\n    return this.error(\r\n      this.config.messages.creationError.title,\r\n      this.config.messages.creationError.description(error)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity update error toast\r\n   */\r\n  entityUpdateError(error: string) {\r\n    return this.error(\r\n      this.config.messages.updateError.title,\r\n      this.config.messages.updateError.description(error)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity deletion error toast\r\n   */\r\n  entityDeletionError(error: string) {\r\n    return this.error(\r\n      this.config.messages.deletionError.title,\r\n      this.config.messages.deletionError.description(error)\r\n    );\r\n  }\r\n}\r\n\r\n// =============================================================================\r\n// ENTITY CONFIGURATIONS - Define toast messages for each domain\r\n// =============================================================================\r\n\r\n/**\r\n * Employee entity toast configuration\r\n */\r\nconst employeeToastConfig: EntityToastConfig<{ name: string }> = {\r\n  entityName: 'Employee',\r\n  getDisplayName: employee => employee.name,\r\n  messages: {\r\n    created: {\r\n      title: 'Employee Added',\r\n      description: name =>\r\n        `The employee \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Employee Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Employee Deleted Successfully',\r\n      description: name =>\r\n        `${name} has been permanently removed from the system.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Employee',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the employee.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the employee.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Employee',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the employee.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Delegation entity toast configuration\r\n */\r\nconst delegationToastConfig: EntityToastConfig<{\r\n  event?: string;\r\n  location?: string;\r\n}> = {\r\n  entityName: 'Delegation',\r\n  getDisplayName: delegation =>\r\n    delegation.event || delegation.location || 'Delegation',\r\n  messages: {\r\n    created: {\r\n      title: 'Delegation Created',\r\n      description: name =>\r\n        `The delegation \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Delegation Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Delegation Deleted Successfully',\r\n      description: name => `${name} has been permanently removed.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Delegation',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the delegation.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the delegation.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Delegation',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the delegation.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Vehicle entity toast configuration\r\n */\r\nconst vehicleToastConfig: EntityToastConfig<{ make: string; model: string }> = {\r\n  entityName: 'Vehicle',\r\n  getDisplayName: vehicle => `${vehicle.make} ${vehicle.model}`,\r\n  messages: {\r\n    created: {\r\n      title: 'Vehicle Added',\r\n      description: name =>\r\n        `The vehicle \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Vehicle Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Vehicle Deleted Successfully',\r\n      description: name => `${name} has been permanently removed.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Vehicle',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the vehicle.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the vehicle.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Vehicle',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the vehicle.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Task entity toast configuration\r\n */\r\nconst taskToastConfig: EntityToastConfig<{ title?: string; name?: string }> = {\r\n  entityName: 'Task',\r\n  getDisplayName: task => task.title || task.name || 'Task',\r\n  messages: {\r\n    created: {\r\n      title: 'Task Created',\r\n      description: name => `The task \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Task Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Task Deleted Successfully',\r\n      description: name => `${name} has been permanently removed.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Task',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the task.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the task.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Task',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the task.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Service Record-specific toast messages following DRY principles\r\n */\r\nexport class ServiceRecordToastService extends ToastService {\r\n  /**\r\n   * Show service record created success toast\r\n   */\r\n  serviceRecordCreated(vehicleName: string, serviceType: string) {\r\n    return this.success(\r\n      'Service Record Added',\r\n      `${serviceType} service for \"${vehicleName}\" has been successfully logged.`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record updated success toast\r\n   */\r\n  serviceRecordUpdated(vehicleName: string, serviceType: string) {\r\n    return this.success(\r\n      'Service Record Updated',\r\n      `${serviceType} service for \"${vehicleName}\" has been updated.`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record deleted success toast\r\n   */\r\n  serviceRecordDeleted(vehicleName: string, serviceType: string) {\r\n    return this.success(\r\n      'Service Record Deleted',\r\n      `${serviceType} service record for \"${vehicleName}\" has been permanently removed.`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record creation error toast\r\n   */\r\n  serviceRecordCreationError(error: string) {\r\n    return this.error(\r\n      'Failed to Log Service Record',\r\n      error || 'An unexpected error occurred while logging the service record.'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record update error toast\r\n   */\r\n  serviceRecordUpdateError(error: string) {\r\n    return this.error(\r\n      'Update Failed',\r\n      error || 'An unexpected error occurred while updating the service record.'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record deletion error toast\r\n   */\r\n  serviceRecordDeletionError(error: string) {\r\n    return this.error(\r\n      'Failed to Delete Service Record',\r\n      error || 'An unexpected error occurred while deleting the service record.'\r\n    );\r\n  }\r\n}\r\n\r\n// =============================================================================\r\n// UTILITY FACTORY FUNCTIONS\r\n// =============================================================================\r\n\r\n/**\r\n * Factory function to create a generic entity toast service for any entity type\r\n * Useful for creating toast services for new entities without pre-configuration\r\n */\r\nexport function createEntityToastService<T>(config: EntityToastConfig<T>) {\r\n  return new GenericEntityToastService<T>(config);\r\n}\r\n\r\n/**\r\n * Factory function to create a simple entity toast service with minimal configuration\r\n * For entities that only need basic CRUD messages\r\n */\r\nexport function createSimpleEntityToastService<T>(\r\n  entityName: string,\r\n  getDisplayName: (entity: T) => string\r\n): GenericEntityToastService<T> {\r\n  const config: EntityToastConfig<T> = {\r\n    entityName,\r\n    getDisplayName,\r\n    messages: {\r\n      created: {\r\n        title: `${entityName} Created`,\r\n        description: (displayName: string) =>\r\n          `The ${entityName.toLowerCase()} \"${displayName}\" has been successfully created.`,\r\n      },\r\n      updated: {\r\n        title: `${entityName} Updated Successfully`,\r\n        description: (displayName: string) =>\r\n          `${displayName} has been updated.`,\r\n      },\r\n      deleted: {\r\n        title: `${entityName} Deleted Successfully`,\r\n        description: (displayName: string) =>\r\n          `${displayName} has been permanently removed.`,\r\n      },\r\n      creationError: {\r\n        title: `Failed to Create ${entityName}`,\r\n        description: (error: string) =>\r\n          error ||\r\n          `An unexpected error occurred while creating the ${entityName.toLowerCase()}.`,\r\n      },\r\n      updateError: {\r\n        title: 'Update Failed',\r\n        description: (error: string) =>\r\n          error ||\r\n          `An unexpected error occurred while updating the ${entityName.toLowerCase()}.`,\r\n      },\r\n      deletionError: {\r\n        title: `Failed to Delete ${entityName}`,\r\n        description: (error: string) =>\r\n          error ||\r\n          `An unexpected error occurred while deleting the ${entityName.toLowerCase()}.`,\r\n      },\r\n    },\r\n  };\r\n\r\n  return new GenericEntityToastService<T>(config);\r\n}\r\n\r\n// =============================================================================\r\n// SINGLETON INSTANCES - Pre-configured toast services for each domain\r\n// =============================================================================\r\n\r\n// Base toast service for generic use\r\nexport const toastService = new ToastService();\r\n\r\n// Entity-specific toast services using the generic service\r\nexport const employeeToast = new GenericEntityToastService(employeeToastConfig);\r\nexport const delegationToast = new GenericEntityToastService(\r\n  delegationToastConfig\r\n);\r\nexport const vehicleToast = new GenericEntityToastService(vehicleToastConfig);\r\nexport const taskToast = new GenericEntityToastService(taskToastConfig);\r\nexport const serviceRecordToast = new ServiceRecordToastService();\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;;AAED;;AA6CA;;CAEC,GACD,MAAM;IACJ;;GAEC,GACD,KAAK,OAAqB,EAAE;QAC1B,OAAO,CAAA,GAAA,qIAAA,CAAA,QAAK,AAAD,EAAE;YACX,OAAO,QAAQ,KAAK;YACpB,aAAa,QAAQ,WAAW;YAChC,SAAS,QAAQ,OAAO,IAAI;YAC5B,GAAI,QAAQ,QAAQ,IAAI;gBAAE,UAAU,QAAQ,QAAQ;YAAC,CAAC;QACxD;IACF;IAEA;;GAEC,GACD,QAAQ,KAAa,EAAE,WAAoB,EAAE;QAC3C,OAAO,IAAI,CAAC,IAAI,CAAC;YACf;YACA;YACA,SAAS;QACX;IACF;IAEA;;GAEC,GACD,MAAM,KAAa,EAAE,WAAoB,EAAE;QACzC,OAAO,IAAI,CAAC,IAAI,CAAC;YACf;YACA;YACA,SAAS;QACX;IACF;IAEA;;GAEC,GACD,KAAK,KAAa,EAAE,WAAoB,EAAE;QACxC,OAAO,IAAI,CAAC,IAAI,CAAC;YACf;YACA;YACA,SAAS;QACX;IACF;AACF;AAKO,MAAM,kCAA2C;IAC9C,OAA6B;IAErC,YAAY,MAA4B,CAAE;QACxC,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;GAEC,GACD,cAAc,MAAS,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IAE7C;IAEA;;GAEC,GACD,cAAc,MAAS,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IAE7C;IAEA;;GAEC,GACD,cAAc,MAAS,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IAE7C;IAEA;;GAEC,GACD,oBAAoB,KAAa,EAAE;QACjC,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EACxC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC;IAEnD;IAEA;;GAEC,GACD,kBAAkB,KAAa,EAAE;QAC/B,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,EACtC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC;IAEjD;IAEA;;GAEC,GACD,oBAAoB,KAAa,EAAE;QACjC,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EACxC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC;IAEnD;AACF;AAEA,gFAAgF;AAChF,gEAAgE;AAChE,gFAAgF;AAEhF;;CAEC,GACD,MAAM,sBAA2D;IAC/D,YAAY;IACZ,gBAAgB,CAAA,WAAY,SAAS,IAAI;IACzC,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,CAAC,cAAc,EAAE,KAAK,gCAAgC,CAAC;QAC3D;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,GAAG,KAAK,8CAA8C,CAAC;QAC3D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAEA;;CAEC,GACD,MAAM,wBAGD;IACH,YAAY;IACZ,gBAAgB,CAAA,aACd,WAAW,KAAK,IAAI,WAAW,QAAQ,IAAI;IAC7C,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,CAAC,gBAAgB,EAAE,KAAK,gCAAgC,CAAC;QAC7D;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,8BAA8B,CAAC;QAC9D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAEA;;CAEC,GACD,MAAM,qBAAyE;IAC7E,YAAY;IACZ,gBAAgB,CAAA,UAAW,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,EAAE;IAC7D,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,CAAC,aAAa,EAAE,KAAK,gCAAgC,CAAC;QAC1D;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,8BAA8B,CAAC;QAC9D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAEA;;CAEC,GACD,MAAM,kBAAwE;IAC5E,YAAY;IACZ,gBAAgB,CAAA,OAAQ,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI;IACnD,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,CAAC,UAAU,EAAE,KAAK,gCAAgC,CAAC;QAC1E;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,8BAA8B,CAAC;QAC9D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAKO,MAAM,kCAAkC;IAC7C;;GAEC,GACD,qBAAqB,WAAmB,EAAE,WAAmB,EAAE;QAC7D,OAAO,IAAI,CAAC,OAAO,CACjB,wBACA,GAAG,YAAY,cAAc,EAAE,YAAY,+BAA+B,CAAC;IAE/E;IAEA;;GAEC,GACD,qBAAqB,WAAmB,EAAE,WAAmB,EAAE;QAC7D,OAAO,IAAI,CAAC,OAAO,CACjB,0BACA,GAAG,YAAY,cAAc,EAAE,YAAY,mBAAmB,CAAC;IAEnE;IAEA;;GAEC,GACD,qBAAqB,WAAmB,EAAE,WAAmB,EAAE;QAC7D,OAAO,IAAI,CAAC,OAAO,CACjB,0BACA,GAAG,YAAY,qBAAqB,EAAE,YAAY,+BAA+B,CAAC;IAEtF;IAEA;;GAEC,GACD,2BAA2B,KAAa,EAAE;QACxC,OAAO,IAAI,CAAC,KAAK,CACf,gCACA,SAAS;IAEb;IAEA;;GAEC,GACD,yBAAyB,KAAa,EAAE;QACtC,OAAO,IAAI,CAAC,KAAK,CACf,iBACA,SAAS;IAEb;IAEA;;GAEC,GACD,2BAA2B,KAAa,EAAE;QACxC,OAAO,IAAI,CAAC,KAAK,CACf,mCACA,SAAS;IAEb;AACF;AAUO,SAAS,yBAA4B,MAA4B;IACtE,OAAO,IAAI,0BAA6B;AAC1C;AAMO,SAAS,+BACd,UAAkB,EAClB,cAAqC;IAErC,MAAM,SAA+B;QACnC;QACA;QACA,UAAU;YACR,SAAS;gBACP,OAAO,GAAG,WAAW,QAAQ,CAAC;gBAC9B,aAAa,CAAC,cACZ,CAAC,IAAI,EAAE,WAAW,WAAW,GAAG,EAAE,EAAE,YAAY,gCAAgC,CAAC;YACrF;YACA,SAAS;gBACP,OAAO,GAAG,WAAW,qBAAqB,CAAC;gBAC3C,aAAa,CAAC,cACZ,GAAG,YAAY,kBAAkB,CAAC;YACtC;YACA,SAAS;gBACP,OAAO,GAAG,WAAW,qBAAqB,CAAC;gBAC3C,aAAa,CAAC,cACZ,GAAG,YAAY,8BAA8B,CAAC;YAClD;YACA,eAAe;gBACb,OAAO,CAAC,iBAAiB,EAAE,YAAY;gBACvC,aAAa,CAAC,QACZ,SACA,CAAC,gDAAgD,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;YAClF;YACA,aAAa;gBACX,OAAO;gBACP,aAAa,CAAC,QACZ,SACA,CAAC,gDAAgD,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;YAClF;YACA,eAAe;gBACb,OAAO,CAAC,iBAAiB,EAAE,YAAY;gBACvC,aAAa,CAAC,QACZ,SACA,CAAC,gDAAgD,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;YAClF;QACF;IACF;IAEA,OAAO,IAAI,0BAA6B;AAC1C;AAOO,MAAM,eAAe,IAAI;AAGzB,MAAM,gBAAgB,IAAI,0BAA0B;AACpD,MAAM,kBAAkB,IAAI,0BACjC;AAEK,MAAM,eAAe,IAAI,0BAA0B;AACnD,MAAM,YAAY,IAAI,0BAA0B;AAChD,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/forms/useFormToast.ts"], "sourcesContent": ["/**\r\n * Form Toast Hook - Integrates generic toast service with form operations\r\n *\r\n * This hook provides a consistent interface for showing toast notifications\r\n * in form components, supporting both generic and entity-specific messaging.\r\n */\r\n\r\nimport { useCallback } from 'react';\r\n\r\nimport {\r\n  type EntityToastConfig,\r\n  type GenericEntityToastService,\r\n  toastService,\r\n  createEntityToastService,\r\n} from '@/lib/services/toastService';\r\n\r\nexport interface FormToastOptions {\r\n  successTitle?: string;\r\n  successDescription?: string;\r\n  errorTitle?: string;\r\n  errorDescription?: string;\r\n}\r\n\r\nexport interface EntityFormToastOptions<T> {\r\n  entityConfig?: EntityToastConfig<T>;\r\n  entityService?: GenericEntityToastService<T>;\r\n}\r\n\r\n/**\r\n * Hook for generic form toast notifications\r\n */\r\nexport function useFormToast() {\r\n  const showSuccess = useCallback((title: string, description?: string) => {\r\n    return toastService.success(title, description);\r\n  }, []);\r\n\r\n  const showError = useCallback((title: string, description?: string) => {\r\n    return toastService.error(title, description);\r\n  }, []);\r\n\r\n  const showInfo = useCallback((title: string, description?: string) => {\r\n    return toastService.info(title, description);\r\n  }, []);\r\n\r\n  const showFormSuccess = useCallback(\r\n    (options?: FormToastOptions) => {\r\n      return showSuccess(\r\n        options?.successTitle || 'Success',\r\n        options?.successDescription || 'Operation completed successfully'\r\n      );\r\n    },\r\n    [showSuccess]\r\n  );\r\n\r\n  const showFormError = useCallback(\r\n    (error: Error | string, options?: FormToastOptions) => {\r\n      const errorMessage = error instanceof Error ? error.message : error;\r\n      return showError(\r\n        options?.errorTitle || 'Error',\r\n        options?.errorDescription ||\r\n          errorMessage ||\r\n          'An unexpected error occurred'\r\n      );\r\n    },\r\n    [showError]\r\n  );\r\n\r\n  return {\r\n    showSuccess,\r\n    showError,\r\n    showInfo,\r\n    showFormSuccess,\r\n    showFormError,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for entity-specific form toast notifications\r\n */\r\nexport function useEntityFormToast<T>(\r\n  entityConfig?: EntityToastConfig<T>,\r\n  entityService?: GenericEntityToastService<T>\r\n) {\r\n  const { showFormSuccess, showFormError } = useFormToast();\r\n\r\n  // Create or use provided entity service\r\n  const entityToastService =\r\n    entityService ||\r\n    (entityConfig ? createEntityToastService(entityConfig) : null);\r\n\r\n  const showEntityCreated = useCallback(\r\n    (entity: T) => {\r\n      if (entityToastService) {\r\n        return entityToastService.entityCreated(entity);\r\n      }\r\n      return showFormSuccess({\r\n        successTitle: 'Created',\r\n        successDescription: 'Item has been created successfully',\r\n      });\r\n    },\r\n    [entityToastService, showFormSuccess]\r\n  );\r\n\r\n  const showEntityUpdated = useCallback(\r\n    (entity: T) => {\r\n      if (entityToastService) {\r\n        return entityToastService.entityUpdated(entity);\r\n      }\r\n      return showFormSuccess({\r\n        successTitle: 'Updated',\r\n        successDescription: 'Item has been updated successfully',\r\n      });\r\n    },\r\n    [entityToastService, showFormSuccess]\r\n  );\r\n\r\n  const showEntityDeleted = useCallback(\r\n    (entity: T) => {\r\n      if (entityToastService) {\r\n        return entityToastService.entityDeleted(entity);\r\n      }\r\n      return showFormSuccess({\r\n        successTitle: 'Deleted',\r\n        successDescription: 'Item has been deleted successfully',\r\n      });\r\n    },\r\n    [entityToastService, showFormSuccess]\r\n  );\r\n\r\n  const showEntityCreationError = useCallback(\r\n    (error: Error | string) => {\r\n      if (entityToastService) {\r\n        const errorMessage = error instanceof Error ? error.message : error;\r\n        return entityToastService.entityCreationError(errorMessage);\r\n      }\r\n      return showFormError(error, { errorTitle: 'Creation Failed' });\r\n    },\r\n    [entityToastService, showFormError]\r\n  );\r\n\r\n  const showEntityUpdateError = useCallback(\r\n    (error: Error | string) => {\r\n      if (entityToastService) {\r\n        const errorMessage = error instanceof Error ? error.message : error;\r\n        return entityToastService.entityUpdateError(errorMessage);\r\n      }\r\n      return showFormError(error, { errorTitle: 'Update Failed' });\r\n    },\r\n    [entityToastService, showFormError]\r\n  );\r\n\r\n  const showEntityDeletionError = useCallback(\r\n    (error: Error | string) => {\r\n      if (entityToastService) {\r\n        const errorMessage = error instanceof Error ? error.message : error;\r\n        return entityToastService.entityDeletionError(errorMessage);\r\n      }\r\n      return showFormError(error, { errorTitle: 'Deletion Failed' });\r\n    },\r\n    [entityToastService, showFormError]\r\n  );\r\n\r\n  return {\r\n    showEntityCreated,\r\n    showEntityUpdated,\r\n    showEntityDeleted,\r\n    showEntityCreationError,\r\n    showEntityUpdateError,\r\n    showEntityDeletionError,\r\n    // Also expose generic methods\r\n    showFormSuccess,\r\n    showFormError,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for predefined entity toast services\r\n */\r\nexport function usePredefinedEntityToast(\r\n  entityType: 'employee' | 'vehicle' | 'task' | 'delegation'\r\n) {\r\n  let entityService: GenericEntityToastService<any>;\r\n\r\n  // Lazy import to avoid circular dependencies\r\n  switch (entityType) {\r\n    case 'employee':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').employeeToast;\r\n      break;\r\n    case 'vehicle':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').vehicleToast;\r\n      break;\r\n    case 'task':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').taskToast;\r\n      break;\r\n    case 'delegation':\r\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\r\n      entityService = require('@/lib/services/toastService').delegationToast;\r\n      break;\r\n    default:\r\n      throw new Error(`Unknown entity type: ${entityType}`);\r\n  }\r\n\r\n  return useEntityFormToast(undefined, entityService);\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAED;AAEA;;;AAsBO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC9C,OAAO,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,OAAO;IACrC,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC5C,OAAO,sIAAA,CAAA,eAAY,CAAC,KAAK,CAAC,OAAO;IACnC,GAAG,EAAE;IAEL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QAC3C,OAAO,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,OAAO;IAClC,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC;QACC,OAAO,YACL,SAAS,gBAAgB,WACzB,SAAS,sBAAsB;IAEnC,GACA;QAAC;KAAY;IAGf,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,OAAuB;QACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC9D,OAAO,UACL,SAAS,cAAc,SACvB,SAAS,oBACP,gBACA;IAEN,GACA;QAAC;KAAU;IAGb,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAKO,SAAS,mBACd,YAAmC,EACnC,aAA4C;IAE5C,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,GAAG;IAE3C,wCAAwC;IACxC,MAAM,qBACJ,iBACA,CAAC,eAAe,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE,gBAAgB,IAAI;IAE/D,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,IAAI,oBAAoB;YACtB,OAAO,mBAAmB,aAAa,CAAC;QAC1C;QACA,OAAO,gBAAgB;YACrB,cAAc;YACd,oBAAoB;QACtB;IACF,GACA;QAAC;QAAoB;KAAgB;IAGvC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,IAAI,oBAAoB;YACtB,OAAO,mBAAmB,aAAa,CAAC;QAC1C;QACA,OAAO,gBAAgB;YACrB,cAAc;YACd,oBAAoB;QACtB;IACF,GACA;QAAC;QAAoB;KAAgB;IAGvC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,IAAI,oBAAoB;YACtB,OAAO,mBAAmB,aAAa,CAAC;QAC1C;QACA,OAAO,gBAAgB;YACrB,cAAc;YACd,oBAAoB;QACtB;IACF,GACA;QAAC;QAAoB;KAAgB;IAGvC,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxC,CAAC;QACC,IAAI,oBAAoB;YACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO,mBAAmB,mBAAmB,CAAC;QAChD;QACA,OAAO,cAAc,OAAO;YAAE,YAAY;QAAkB;IAC9D,GACA;QAAC;QAAoB;KAAc;IAGrC,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACtC,CAAC;QACC,IAAI,oBAAoB;YACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO,mBAAmB,iBAAiB,CAAC;QAC9C;QACA,OAAO,cAAc,OAAO;YAAE,YAAY;QAAgB;IAC5D,GACA;QAAC;QAAoB;KAAc;IAGrC,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACxC,CAAC;QACC,IAAI,oBAAoB;YACtB,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,OAAO,mBAAmB,mBAAmB,CAAC;QAChD;QACA,OAAO,cAAc,OAAO;YAAE,YAAY;QAAkB;IAC9D,GACA;QAAC;QAAoB;KAAc;IAGrC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,8BAA8B;QAC9B;QACA;IACF;AACF;AAKO,SAAS,yBACd,UAA0D;IAE1D,IAAI;IAEJ,6CAA6C;IAC7C,OAAQ;QACN,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,6FAAuC,aAAa;YACpE;QACF,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,6FAAuC,YAAY;YACnE;QACF,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,6FAAuC,SAAS;YAChE;QACF,KAAK;YACH,8DAA8D;YAC9D,gBAAgB,6FAAuC,eAAe;YACtE;QACF;YACE,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,YAAY;IACxD;IAEA,OAAO,mBAAmB,WAAW;AACvC", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/reports/ReportActions.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport {\r\n  Download,\r\n  FileSpreadsheet,\r\n  FileText,\r\n  Loader2,\r\n  Printer,\r\n} from 'lucide-react';\r\nimport React, { useState } from 'react';\r\n\r\nimport { ActionButton } from '@/components/ui/action-button';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { useFormToast } from '@/hooks/forms/useFormToast';\r\nimport { cn } from '@/lib/utils';\r\nimport {\r\n  extractTableDataForCsv,\r\n  generateCsvFromData,\r\n} from '@/lib/utils/fileUtils';\r\n\r\nexport interface ReportActionsProps {\r\n  /** Additional CSS class names */\r\n  className?: string;\r\n  /** Raw data for CSV export (alternative to tableId if CSV is enabled) */\r\n  csvData?: {\r\n    data: any[][];\r\n    headers: string[];\r\n  };\r\n  /** Whether CSV export is available */\r\n  enableCsv?: boolean;\r\n  /** ID of the entity to generate the report for */\r\n  entityId?: string;\r\n  /** Base name for downloaded files (without extension) */\r\n  fileName: string;\r\n  /** ID or selector of the report content element for UI purposes */\r\n  reportContentId: string;\r\n  /** Type of report to download (e.g., 'vehicle', 'service', 'delegation') */\r\n  reportType: string;\r\n  /** ID or selector of the table element (for CSV export from table) */\r\n  tableId?: string;\r\n}\r\n\r\n/**\r\n * Standardized report actions component for print and download functionality\r\n */\r\nexport function ReportActions({\r\n  className,\r\n  csvData,\r\n  enableCsv = false,\r\n  entityId,\r\n  fileName,\r\n  reportContentId,\r\n  reportType,\r\n  tableId,\r\n}: ReportActionsProps) {\r\n  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);\r\n  const [isGeneratingCsv, setIsGeneratingCsv] = useState(false);\r\n  const { showFormSuccess, showFormError } = useFormToast();\r\n\r\n  const handlePrint = () => {\r\n    if (globalThis.window !== undefined) {\r\n      globalThis.print();\r\n    }\r\n  };\r\n\r\n  const handleDownloadPdf = async () => {\r\n    setIsGeneratingPdf(true);\r\n    try {\r\n      // Use the backend API to generate and download the PDF\r\n      const apiUrl = `/api/reports/${reportType}${\r\n        entityId ? `/${entityId}` : ''\r\n      }`;\r\n\r\n      // Create a link to download the file directly from the API\r\n      const link = document.createElement('a');\r\n      link.href = apiUrl;\r\n      link.download = `${fileName}.pdf`;\r\n      link.target = '_blank'; // Open in a new tab/window\r\n      document.body.append(link);\r\n      link.click();\r\n      link.remove();\r\n\r\n      showFormSuccess({\r\n        successTitle: 'PDF Downloaded',\r\n        successDescription: 'Your report is being downloaded as a PDF.',\r\n      });\r\n    } catch (error: any) {\r\n      console.error('Error generating PDF:', error);\r\n      showFormError(\r\n        `PDF download failed: ${error.message || 'Please try again.'}`,\r\n        {\r\n          errorTitle: 'Download Failed',\r\n        }\r\n      );\r\n    } finally {\r\n      setIsGeneratingPdf(false);\r\n    }\r\n  };\r\n\r\n  const handleDownloadCsv = async () => {\r\n    if (!enableCsv) return;\r\n    setIsGeneratingCsv(true);\r\n    try {\r\n      if (csvData?.data && csvData.headers) {\r\n        generateCsvFromData(csvData.data, csvData.headers, `${fileName}.csv`);\r\n      } else if (tableId) {\r\n        const extractedData = extractTableDataForCsv(tableId);\r\n        generateCsvFromData(\r\n          extractedData.data,\r\n          extractedData.headers,\r\n          `${fileName}.csv`\r\n        );\r\n      } else {\r\n        throw new Error(\r\n          'CSV export requires either `csvData` or a `tableId` to be provided.'\r\n        );\r\n      }\r\n      showFormSuccess({\r\n        successTitle: 'CSV Downloaded',\r\n        successDescription: 'Your report has been downloaded as a CSV file.',\r\n      });\r\n    } catch (error: any) {\r\n      console.error('Error generating CSV:', error);\r\n      showFormError(\r\n        `CSV generation failed: ${error.message || 'Please try again.'}`,\r\n        {\r\n          errorTitle: 'Download Failed',\r\n        }\r\n      );\r\n    } finally {\r\n      setIsGeneratingCsv(false);\r\n    }\r\n  };\r\n\r\n  const isGenerating = isGeneratingPdf || isGeneratingCsv;\r\n\r\n  return (\r\n    <div className={cn('flex items-center gap-2 no-print', className)}>\r\n      <ActionButton\r\n        actionType=\"secondary\"\r\n        aria-label=\"Print report\"\r\n        onClick={handlePrint}\r\n        size=\"icon\"\r\n        title=\"Print Report\"\r\n      >\r\n        <Printer className=\"size-4\" />\r\n      </ActionButton>\r\n\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <ActionButton\r\n            actionType=\"secondary\"\r\n            aria-label=\"Download report\"\r\n            disabled={isGenerating}\r\n            size=\"icon\"\r\n            title=\"Download Report\"\r\n          >\r\n            {isGenerating ? (\r\n              <Loader2 className=\"size-4 animate-spin\" />\r\n            ) : (\r\n              <Download className=\"size-4\" />\r\n            )}\r\n          </ActionButton>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\">\r\n          <DropdownMenuItem\r\n            disabled={isGeneratingPdf}\r\n            onClick={handleDownloadPdf}\r\n          >\r\n            {isGeneratingPdf ? (\r\n              <Loader2 className=\"mr-2 size-4 animate-spin\" />\r\n            ) : (\r\n              <FileText className=\"mr-2 size-4\" />\r\n            )}\r\n            <span>Download PDF</span>\r\n          </DropdownMenuItem>\r\n          {enableCsv && (\r\n            <DropdownMenuItem\r\n              disabled={isGeneratingCsv}\r\n              onClick={handleDownloadCsv}\r\n            >\r\n              {isGeneratingCsv ? (\r\n                <Loader2 className=\"mr-2 size-4 animate-spin\" />\r\n              ) : (\r\n                <FileSpreadsheet className=\"mr-2 size-4\" />\r\n              )}\r\n              <span>Download CSV</span>\r\n            </DropdownMenuItem>\r\n          )}\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAOA;AAEA;AACA;AAMA;AACA;AAAA;AACA;AApBA;;;;;;;;;AAkDO,SAAS,cAAc,EAC5B,SAAS,EACT,OAAO,EACP,YAAY,KAAK,EACjB,QAAQ,EACR,QAAQ,EACR,eAAe,EACf,UAAU,EACV,OAAO,EACY;IACnB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IAEtD,MAAM,cAAc;QAClB,IAAI,WAAW,MAAM,KAAK,WAAW;YACnC,WAAW,KAAK;QAClB;IACF;IAEA,MAAM,oBAAoB;QACxB,mBAAmB;QACnB,IAAI;YACF,uDAAuD;YACvD,MAAM,SAAS,CAAC,aAAa,EAAE,aAC7B,WAAW,CAAC,CAAC,EAAE,UAAU,GAAG,IAC5B;YAEF,2DAA2D;YAC3D,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG,GAAG,SAAS,IAAI,CAAC;YACjC,KAAK,MAAM,GAAG,UAAU,2BAA2B;YACnD,SAAS,IAAI,CAAC,MAAM,CAAC;YACrB,KAAK,KAAK;YACV,KAAK,MAAM;YAEX,gBAAgB;gBACd,cAAc;gBACd,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,cACE,CAAC,qBAAqB,EAAE,MAAM,OAAO,IAAI,qBAAqB,EAC9D;gBACE,YAAY;YACd;QAEJ,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW;QAChB,mBAAmB;QACnB,IAAI;YACF,IAAI,SAAS,QAAQ,QAAQ,OAAO,EAAE;gBACpC,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,IAAI,EAAE,QAAQ,OAAO,EAAE,GAAG,SAAS,IAAI,CAAC;YACtE,OAAO,IAAI,SAAS;gBAClB,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,yBAAsB,AAAD,EAAE;gBAC7C,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAChB,cAAc,IAAI,EAClB,cAAc,OAAO,EACrB,GAAG,SAAS,IAAI,CAAC;YAErB,OAAO;gBACL,MAAM,IAAI,MACR;YAEJ;YACA,gBAAgB;gBACd,cAAc;gBACd,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,cACE,CAAC,uBAAuB,EAAE,MAAM,OAAO,IAAI,qBAAqB,EAChE;gBACE,YAAY;YACd;QAEJ,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,eAAe,mBAAmB;IAExC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;;0BACrD,8OAAC,4IAAA,CAAA,eAAY;gBACX,YAAW;gBACX,cAAW;gBACX,SAAS;gBACT,MAAK;gBACL,OAAM;0BAEN,cAAA,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;0BAGrB,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,4IAAA,CAAA,eAAY;4BACX,YAAW;4BACX,cAAW;4BACX,UAAU;4BACV,MAAK;4BACL,OAAM;sCAEL,6BACC,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAEnB,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAI1B,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAM;;0CACzB,8OAAC,4IAAA,CAAA,mBAAgB;gCACf,UAAU;gCACV,SAAS;;oCAER,gCACC,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDAEtB,8OAAC;kDAAK;;;;;;;;;;;;4BAEP,2BACC,8OAAC,4IAAA,CAAA,mBAAgB;gCACf,UAAU;gCACV,SAAS;;oCAER,gCACC,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,8OAAC,4NAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;kDAE7B,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 1012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/reports/ServiceHistorySummary.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\nimport type { EnrichedServiceRecord } from '@/lib/types/domain';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { cn } from '@/lib/utils';\r\n\r\n/**\r\n * Props for the ServiceHistorySummary component\r\n */\r\ninterface ServiceHistorySummaryProps {\r\n  /** Additional CSS class names */\r\n  className?: string;\r\n  /** Array of service records to display summary statistics for */\r\n  records: EnrichedServiceRecord[];\r\n  /** Whether this summary is for a vehicle-specific view */\r\n  vehicleSpecific?: boolean;\r\n}\r\n\r\n/**\r\n * Props for the SummaryStatCard component\r\n */\r\ninterface SummaryStatCardProps {\r\n  /** Additional CSS class for the card background */\r\n  className?: string;\r\n  /** Optional column span for the card */\r\n  colSpan?: string;\r\n  /** Label to display below the value */\r\n  label: string;\r\n  /** CSS class for the label text color */\r\n  textColor?: string;\r\n  /** Value to display (typically a number) */\r\n  value: number | string;\r\n}\r\n\r\n/**\r\n * A component that displays summary statistics for service history records\r\n *\r\n * @example\r\n * ```tsx\r\n * <ServiceHistorySummary records={filteredRecords} vehicleSpecific={false} />\r\n * ```\r\n */\r\nexport function ServiceHistorySummary({\r\n  className,\r\n  records,\r\n  vehicleSpecific = false,\r\n}: ServiceHistorySummaryProps) {\r\n  // Calculate statistics\r\n  const totalRecords = records.length;\r\n  const totalCost = records.reduce(\r\n    (sum, record) => sum + (Number(record.cost) || 0),\r\n    0\r\n  );\r\n\r\n  // Get date range\r\n  const dates = records.map(record => new Date(record.date).getTime());\r\n  const oldestDate = dates.length > 0 ? new Date(Math.min(...dates)) : null;\r\n  const newestDate = dates.length > 0 ? new Date(Math.max(...dates)) : null;\r\n\r\n  // Get service type counts\r\n  const serviceTypeCounts = records.reduce(\r\n    (acc, record) => {\r\n      for (const service of record.servicePerformed) {\r\n        acc[service] = (acc[service] || 0) + 1;\r\n      }\r\n      return acc;\r\n    },\r\n    {} as Record<string, number>\r\n  );\r\n\r\n  // Get top services\r\n  const topServices = Object.entries(serviceTypeCounts)\r\n    .sort((a, b) => b[1] - a[1])\r\n    .slice(0, 3);\r\n\r\n  // Calculate odometer range for vehicle-specific view\r\n  const odometerRange =\r\n    vehicleSpecific && records.length > 0\r\n      ? Math.max(...records.map(r => r.odometer)) -\r\n        Math.min(...records.map(r => r.odometer))\r\n      : 0;\r\n\r\n  // Count unique vehicles for general view\r\n  const uniqueVehicleCount =\r\n    !vehicleSpecific && records.length > 0\r\n      ? new Set(records.map(r => r.vehicleId)).size\r\n      : 0;\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'mt-6 grid grid-cols-2 sm:grid-cols-3 gap-4 summary-grid',\r\n        className\r\n      )}\r\n    >\r\n      {/* Total Services Card */}\r\n      <SummaryStatCard\r\n        className=\"border-primary/10\"\r\n        label=\"Total Services\"\r\n        value={totalRecords}\r\n      />\r\n\r\n      {/* Total Cost Card */}\r\n      <SummaryStatCard\r\n        className=\"border-primary/10\"\r\n        label=\"Total Cost\"\r\n        value={`$${totalCost.toFixed(2)}`}\r\n      />\r\n\r\n      {/* Vehicles Serviced Card (only for general view) */}\r\n      {!vehicleSpecific && records.length > 0 && (\r\n        <SummaryStatCard\r\n          className=\"border-primary/10\"\r\n          label=\"Vehicles Serviced\"\r\n          value={uniqueVehicleCount}\r\n        />\r\n      )}\r\n\r\n      {/* Odometer Range Card (only for vehicle-specific view) */}\r\n      {vehicleSpecific && records.length > 0 && (\r\n        <SummaryStatCard\r\n          className=\"border-primary/10\"\r\n          label=\"Odometer Range Covered\"\r\n          value={odometerRange.toLocaleString()}\r\n        />\r\n      )}\r\n\r\n      {/* Date Range Card */}\r\n      {records.length > 0 && (\r\n        <SummaryStatCard\r\n          className=\"border-primary/10\"\r\n          colSpan=\"col-span-2 sm:col-span-3\"\r\n          label=\"Date Range\"\r\n          value={`${oldestDate?.toLocaleDateString()} - ${newestDate?.toLocaleDateString()}`}\r\n        />\r\n      )}\r\n\r\n      {/* Top Services Card */}\r\n      {topServices.length > 0 && (\r\n        <Card className=\"col-span-2 overflow-hidden border-primary/10 sm:col-span-3\">\r\n          <CardContent className=\"p-4\">\r\n            <h3 className=\"mb-2 text-sm font-semibold text-card-foreground\">\r\n              Top Services\r\n            </h3>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {topServices.map(([service, count]) => (\r\n                <Badge\r\n                  aria-label={`${service}: ${count} services`}\r\n                  className=\"px-2 py-1 text-xs\"\r\n                  key={service}\r\n                  variant=\"secondary\"\r\n                >\r\n                  {service} ({count})\r\n                </Badge>\r\n              ))}\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * A card component for displaying a summary statistic\r\n */\r\nfunction SummaryStatCard({\r\n  className,\r\n  colSpan,\r\n  label,\r\n  textColor = 'text-muted-foreground',\r\n  value,\r\n}: SummaryStatCardProps) {\r\n  return (\r\n    <Card className={cn('overflow-hidden', className, colSpan)}>\r\n      <CardContent className=\"p-4 text-center\">\r\n        <p className=\"text-2xl font-semibold text-card-foreground\">{value}</p>\r\n        <p className={cn('text-sm', textColor)}>{label}</p>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAMA;AACA;AACA;AAAA;AARA;;;;;AA8CO,SAAS,sBAAsB,EACpC,SAAS,EACT,OAAO,EACP,kBAAkB,KAAK,EACI;IAC3B,uBAAuB;IACvB,MAAM,eAAe,QAAQ,MAAM;IACnC,MAAM,YAAY,QAAQ,MAAM,CAC9B,CAAC,KAAK,SAAW,MAAM,CAAC,OAAO,OAAO,IAAI,KAAK,CAAC,GAChD;IAGF,iBAAiB;IACjB,MAAM,QAAQ,QAAQ,GAAG,CAAC,CAAA,SAAU,IAAI,KAAK,OAAO,IAAI,EAAE,OAAO;IACjE,MAAM,aAAa,MAAM,MAAM,GAAG,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI,UAAU;IACrE,MAAM,aAAa,MAAM,MAAM,GAAG,IAAI,IAAI,KAAK,KAAK,GAAG,IAAI,UAAU;IAErE,0BAA0B;IAC1B,MAAM,oBAAoB,QAAQ,MAAM,CACtC,CAAC,KAAK;QACJ,KAAK,MAAM,WAAW,OAAO,gBAAgB,CAAE;YAC7C,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI;QACvC;QACA,OAAO;IACT,GACA,CAAC;IAGH,mBAAmB;IACnB,MAAM,cAAc,OAAO,OAAO,CAAC,mBAChC,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAC1B,KAAK,CAAC,GAAG;IAEZ,qDAAqD;IACrD,MAAM,gBACJ,mBAAmB,QAAQ,MAAM,GAAG,IAChC,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,KACvC,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,KACvC;IAEN,yCAAyC;IACzC,MAAM,qBACJ,CAAC,mBAAmB,QAAQ,MAAM,GAAG,IACjC,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,SAAS,GAAG,IAAI,GAC3C;IAEN,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2DACA;;0BAIF,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,OAAO;;;;;;0BAIT,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,OAAO,CAAC,CAAC,EAAE,UAAU,OAAO,CAAC,IAAI;;;;;;YAIlC,CAAC,mBAAmB,QAAQ,MAAM,GAAG,mBACpC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,OAAO;;;;;;YAKV,mBAAmB,QAAQ,MAAM,GAAG,mBACnC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,OAAO,cAAc,cAAc;;;;;;YAKtC,QAAQ,MAAM,GAAG,mBAChB,8OAAC;gBACC,WAAU;gBACV,SAAQ;gBACR,OAAM;gBACN,OAAO,GAAG,YAAY,qBAAqB,GAAG,EAAE,YAAY,sBAAsB;;;;;;YAKrF,YAAY,MAAM,GAAG,mBACpB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAG,WAAU;sCAAkD;;;;;;sCAGhE,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,CAAC,SAAS,MAAM,iBAChC,8OAAC,iIAAA,CAAA,QAAK;oCACJ,cAAY,GAAG,QAAQ,EAAE,EAAE,MAAM,SAAS,CAAC;oCAC3C,WAAU;oCAEV,SAAQ;;wCAEP;wCAAQ;wCAAG;wCAAM;;mCAHb;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvB;AAEA;;CAEC,GACD,SAAS,gBAAgB,EACvB,SAAS,EACT,OAAO,EACP,KAAK,EACL,YAAY,uBAAuB,EACnC,KAAK,EACgB;IACrB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB,WAAW;kBAChD,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,8OAAC;oBAAE,WAAU;8BAA+C;;;;;;8BAC5D,8OAAC;oBAAE,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW;8BAAa;;;;;;;;;;;;;;;;;AAIjD", "debugId": null}}, {"offset": {"line": 1190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/error-handler.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Al<PERSON><PERSON>riangle, Info, XCircle } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport type { ApiValidationError } from '@/lib/types/api';\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { useFormToast } from '@/hooks/forms/useFormToast';\r\nimport {\r\n  ERROR_CODE_MESSAGES,\r\n  formatErrorForUser,\r\n  getErrorMessage,\r\n  isAssignmentError,\r\n  isValidationError,\r\n  type UserFriendlyError,\r\n} from '@/lib/utils/errorHandling';\r\n\r\n/**\r\n * PHASE 3: Consolidated Error Handler Component\r\n * Integrates with errorHandling.ts utilities for consistent error handling\r\n * Handles the {message, error, code} format from Phase 2 backend validation\r\n */\r\nexport interface StructuredError {\r\n  code: string;\r\n  error: string;\r\n  message: string;\r\n}\r\n\r\ninterface ErrorHandlerProps {\r\n  className?: string;\r\n  context?: string;\r\n  error: Error | null | string | StructuredError;\r\n  showToast?: boolean;\r\n}\r\n\r\n/**\r\n * Component to display structured errors from the backend\r\n * Handles the Phase 2 backend error format: {message, error, code}\r\n */\r\nexport function ErrorHandler({\r\n  className = '',\r\n  context,\r\n  error,\r\n  showToast = false,\r\n}: ErrorHandlerProps) {\r\n  const { showFormError } = useFormToast();\r\n\r\n  React.useEffect(() => {\r\n    if (error && showToast) {\r\n      // Use centralized error message extraction\r\n      const errorMessage = getErrorMessage(error);\r\n      const userFriendlyError = formatErrorForUser(error, context);\r\n\r\n      showFormError(userFriendlyError.message, {\r\n        errorTitle: userFriendlyError.title,\r\n      });\r\n    }\r\n  }, [error, showToast, showFormError, context]);\r\n\r\n  if (!error) return null;\r\n\r\n  // Use centralized error formatting\r\n  const userFriendlyError = formatErrorForUser(error, context);\r\n  const severity = getErrorSeverity(userFriendlyError.code || 'UNKNOWN_ERROR');\r\n\r\n  return (\r\n    <Alert className={`${className} ${getSeverityStyles(severity)}`}>\r\n      {getErrorIcon(severity)}\r\n      <AlertTitle>{userFriendlyError.title}</AlertTitle>\r\n      <AlertDescription>\r\n        {userFriendlyError.message}\r\n        {userFriendlyError.code && (\r\n          <span className=\"mt-1 block text-xs text-muted-foreground\">\r\n            Error Code: {userFriendlyError.code}\r\n          </span>\r\n        )}\r\n        {userFriendlyError.field && (\r\n          <span className=\"mt-1 block text-xs text-muted-foreground\">\r\n            Field: {userFriendlyError.field}\r\n          </span>\r\n        )}\r\n      </AlertDescription>\r\n    </Alert>\r\n  );\r\n}\r\n\r\n/**\r\n * Utility function to check if an error is a structured backend error\r\n */\r\nexport function isStructuredError(error: any): error is StructuredError {\r\n  return (\r\n    error &&\r\n    typeof error === 'object' &&\r\n    'message' in error &&\r\n    'error' in error &&\r\n    'code' in error\r\n  );\r\n}\r\n\r\n// Note: parseError and getErrorMessage functions removed - using centralized utilities from errorHandling.ts\r\n\r\n/**\r\n * Hook for handling API errors with structured error format\r\n */\r\nexport function useErrorHandler() {\r\n  const { showFormError } = useFormToast();\r\n\r\n  const handleError = React.useCallback(\r\n    (error: Error | string | StructuredError, context?: string) => {\r\n      // Use centralized error formatting\r\n      const userFriendlyError = formatErrorForUser(error, context);\r\n\r\n      showFormError(userFriendlyError.message, {\r\n        errorTitle: userFriendlyError.title,\r\n      });\r\n\r\n      // Log for debugging with full error details\r\n      console.error('Error handled:', {\r\n        code: userFriendlyError.code,\r\n        context,\r\n        field: userFriendlyError.field,\r\n        message: userFriendlyError.message,\r\n        originalError: error,\r\n        title: userFriendlyError.title,\r\n      });\r\n    },\r\n    [showFormError]\r\n  );\r\n\r\n  return { handleError };\r\n}\r\n\r\n/**\r\n * Get appropriate icon for error severity\r\n */\r\nfunction getErrorIcon(severity: 'error' | 'info' | 'warning') {\r\n  switch (severity) {\r\n    case 'error': {\r\n      return <XCircle className=\"size-4\" />;\r\n    }\r\n    case 'warning': {\r\n      return <AlertTriangle className=\"size-4\" />;\r\n    }\r\n    case 'info':\r\n    default: {\r\n      return <Info className=\"size-4\" />;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Determine error severity based on error code\r\n */\r\nfunction getErrorSeverity(code: string): 'error' | 'info' | 'warning' {\r\n  const errorCodes = ['VALIDATION_ERROR', 'ASSIGNMENT_ERROR', 'ROLE_ERROR'];\r\n  const warningCodes = ['BUSINESS_RULE_WARNING', 'DEPRECATION_WARNING'];\r\n\r\n  if (errorCodes.some(ec => code.includes(ec))) {\r\n    return 'error';\r\n  }\r\n\r\n  if (warningCodes.some(wc => code.includes(wc))) {\r\n    return 'warning';\r\n  }\r\n\r\n  return 'info';\r\n}\r\n\r\n/**\r\n * Get CSS classes for error severity\r\n */\r\nfunction getSeverityStyles(severity: 'error' | 'info' | 'warning'): string {\r\n  switch (severity) {\r\n    case 'error': {\r\n      return 'border-destructive/50 text-destructive dark:border-destructive';\r\n    }\r\n    case 'warning': {\r\n      return 'border-yellow-500/50 text-yellow-600 dark:border-yellow-500 dark:text-yellow-400';\r\n    }\r\n    case 'info':\r\n    default: {\r\n      return 'border-blue-500/50 text-blue-600 dark:border-blue-500 dark:text-blue-400';\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAAA;AAAA;AACA;AAIA;AACA;AACA;AATA;;;;;;;AAwCO,SAAS,aAAa,EAC3B,YAAY,EAAE,EACd,OAAO,EACP,KAAK,EACL,YAAY,KAAK,EACC;IAClB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IAErC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,SAAS,WAAW;YACtB,2CAA2C;YAC3C,MAAM,eAAe,CAAA,GAAA,oIAAA,CAAA,kBAAe,AAAD,EAAE;YACrC,MAAM,oBAAoB,CAAA,GAAA,oIAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAEpD,cAAc,kBAAkB,OAAO,EAAE;gBACvC,YAAY,kBAAkB,KAAK;YACrC;QACF;IACF,GAAG;QAAC;QAAO;QAAW;QAAe;KAAQ;IAE7C,IAAI,CAAC,OAAO,OAAO;IAEnB,mCAAmC;IACnC,MAAM,oBAAoB,CAAA,GAAA,oIAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;IACpD,MAAM,WAAW,iBAAiB,kBAAkB,IAAI,IAAI;IAE5D,qBACE,8OAAC,iIAAA,CAAA,QAAK;QAAC,WAAW,GAAG,UAAU,CAAC,EAAE,kBAAkB,WAAW;;YAC5D,aAAa;0BACd,8OAAC,iIAAA,CAAA,aAAU;0BAAE,kBAAkB,KAAK;;;;;;0BACpC,8OAAC,iIAAA,CAAA,mBAAgB;;oBACd,kBAAkB,OAAO;oBACzB,kBAAkB,IAAI,kBACrB,8OAAC;wBAAK,WAAU;;4BAA2C;4BAC5C,kBAAkB,IAAI;;;;;;;oBAGtC,kBAAkB,KAAK,kBACtB,8OAAC;wBAAK,WAAU;;4BAA2C;4BACjD,kBAAkB,KAAK;;;;;;;;;;;;;;;;;;;AAM3C;AAKO,SAAS,kBAAkB,KAAU;IAC1C,OACE,SACA,OAAO,UAAU,YACjB,aAAa,SACb,WAAW,SACX,UAAU;AAEd;AAOO,SAAS;IACd,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IAErC,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,WAAW,CACnC,CAAC,OAAyC;QACxC,mCAAmC;QACnC,MAAM,oBAAoB,CAAA,GAAA,oIAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;QAEpD,cAAc,kBAAkB,OAAO,EAAE;YACvC,YAAY,kBAAkB,KAAK;QACrC;QAEA,4CAA4C;QAC5C,QAAQ,KAAK,CAAC,kBAAkB;YAC9B,MAAM,kBAAkB,IAAI;YAC5B;YACA,OAAO,kBAAkB,KAAK;YAC9B,SAAS,kBAAkB,OAAO;YAClC,eAAe;YACf,OAAO,kBAAkB,KAAK;QAChC;IACF,GACA;QAAC;KAAc;IAGjB,OAAO;QAAE;IAAY;AACvB;AAEA;;CAEC,GACD,SAAS,aAAa,QAAsC;IAC1D,OAAQ;QACN,KAAK;YAAS;gBACZ,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;QACA,KAAK;YAAW;gBACd,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;QACA,KAAK;QACL;YAAS;gBACP,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;IACF;AACF;AAEA;;CAEC,GACD,SAAS,iBAAiB,IAAY;IACpC,MAAM,aAAa;QAAC;QAAoB;QAAoB;KAAa;IACzE,MAAM,eAAe;QAAC;QAAyB;KAAsB;IAErE,IAAI,WAAW,IAAI,CAAC,CAAA,KAAM,KAAK,QAAQ,CAAC,MAAM;QAC5C,OAAO;IACT;IAEA,IAAI,aAAa,IAAI,CAAC,CAAA,KAAM,KAAK,QAAQ,CAAC,MAAM;QAC9C,OAAO;IACT;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,kBAAkB,QAAsC;IAC/D,OAAQ;QACN,KAAK;YAAS;gBACZ,OAAO;YACT;QACA,KAAK;YAAW;gBACd,OAAO;YACT;QACA,KAAK;QACL;YAAS;gBACP,OAAO;YACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 1389, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn('animate-pulse rounded-md bg-muted', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/loading.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON>cle, Loader2, LucideIcon } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport { ActionButton } from '@/components/ui/action-button';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { cn } from '@/lib/utils';\r\n\r\n// Size variants for the spinner\r\nexport type SpinnerSize = 'lg' | 'md' | 'sm' | 'xl';\r\n\r\n// Size mappings for the spinner\r\nconst spinnerSizeClasses: Record<SpinnerSize, string> = {\r\n  lg: 'h-8 w-8',\r\n  md: 'h-6 w-6',\r\n  sm: 'h-4 w-4',\r\n  xl: 'h-12 w-12',\r\n};\r\n\r\n// Text size mappings for the spinner\r\nconst spinnerTextSizeClasses: Record<SpinnerSize, string> = {\r\n  lg: 'text-base',\r\n  md: 'text-sm',\r\n  sm: 'text-xs',\r\n  xl: 'text-lg',\r\n};\r\n\r\nexport interface DataLoaderProps<T> {\r\n  /**\r\n   * Render function for the data\r\n   */\r\n  children: (data: T) => React.ReactNode;\r\n\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * The data to render\r\n   */\r\n  data: null | T | undefined;\r\n\r\n  /**\r\n   * Custom empty state component\r\n   */\r\n  emptyComponent?: React.ReactNode;\r\n\r\n  /**\r\n   * Error message, if any\r\n   */\r\n  error?: null | string;\r\n\r\n  /**\r\n   * Custom error component\r\n   */\r\n  errorComponent?: React.ReactNode;\r\n\r\n  /**\r\n   * Whether data is currently loading\r\n   */\r\n  isLoading: boolean;\r\n\r\n  /**\r\n   * Custom loading component\r\n   */\r\n  loadingComponent?: React.ReactNode;\r\n\r\n  /**\r\n   * Function to retry loading data\r\n   */\r\n  onRetry?: () => void;\r\n}\r\n\r\nexport interface EmptyStateProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Description text to display\r\n   */\r\n  description?: string;\r\n\r\n  /**\r\n   * Icon to display (Lucide icon component)\r\n   */\r\n  icon?: LucideIcon;\r\n\r\n  /**\r\n   * Primary action button\r\n   */\r\n  primaryAction?: {\r\n    label: string;\r\n    onClick?: () => void;\r\n    href?: string;\r\n    icon?: React.ReactNode;\r\n  };\r\n\r\n  /**\r\n   * Secondary action button\r\n   */\r\n  secondaryAction?: {\r\n    label: string;\r\n    onClick?: () => void;\r\n    href?: string;\r\n    icon?: React.ReactNode;\r\n  };\r\n\r\n  /**\r\n   * Title text to display\r\n   */\r\n  title: string;\r\n}\r\n\r\nexport interface ErrorDisplayProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Error message to display\r\n   */\r\n  message: string;\r\n\r\n  /**\r\n   * Function to retry the operation\r\n   */\r\n  onRetry?: () => void;\r\n}\r\n\r\nexport interface LoadingSpinnerProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Whether to display as a full-page overlay\r\n   */\r\n  fullPage?: boolean;\r\n\r\n  /**\r\n   * Size of the spinner\r\n   */\r\n  size?: SpinnerSize;\r\n\r\n  /**\r\n   * Text to display below the spinner\r\n   */\r\n  text?: string;\r\n}\r\n\r\nexport interface SkeletonLoaderProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Number of skeleton items to display\r\n   */\r\n  count?: number;\r\n\r\n  /**\r\n   * Test ID for testing\r\n   */\r\n  testId?: string;\r\n\r\n  /**\r\n   * Type of content to show a skeleton for\r\n   */\r\n  variant?: SkeletonVariant;\r\n}\r\n\r\n// Skeleton variants\r\nexport type SkeletonVariant = 'card' | 'default' | 'list' | 'stats' | 'table';\r\n\r\n/**\r\n * DataLoader component for handling loading, error, and empty states\r\n *\r\n * @example\r\n * <DataLoader\r\n *   isLoading={isLoading}\r\n *   error={error}\r\n *   data={vehicles}\r\n *   onRetry={refetch}\r\n *   loadingComponent={<SkeletonLoader variant=\"card\" count={3} />}\r\n * >\r\n *   {(vehicles) => (\r\n *     <div className=\"grid grid-cols-3 gap-4\">\r\n *       {vehicles.map(vehicle => (\r\n *         <VehicleCard key={vehicle.id} vehicle={vehicle} />\r\n *       ))}\r\n *     </div>\r\n *   )}\r\n * </DataLoader>\r\n */\r\nexport function DataLoader<T>({\r\n  children,\r\n  className,\r\n  data,\r\n  emptyComponent,\r\n  error,\r\n  errorComponent,\r\n  isLoading,\r\n  loadingComponent,\r\n  onRetry,\r\n}: DataLoaderProps<T>) {\r\n  if (isLoading) {\r\n    return (\r\n      loadingComponent || (\r\n        <LoadingSpinner {...(className && { className })} text=\"Loading...\" />\r\n      )\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      errorComponent || (\r\n        <ErrorDisplay\r\n          {...(className && { className })}\r\n          message={error}\r\n          {...(onRetry && { onRetry })}\r\n        />\r\n      )\r\n    );\r\n  }\r\n\r\n  if (!data || (Array.isArray(data) && data.length === 0)) {\r\n    return (\r\n      emptyComponent || (\r\n        <div className={cn('text-center py-8', className)}>\r\n          <p className=\"text-muted-foreground\">No data available</p>\r\n        </div>\r\n      )\r\n    );\r\n  }\r\n\r\n  return <div className={className}>{children(data)}</div>;\r\n}\r\n\r\n/**\r\n * Unified empty state component following the design pattern from delegations page\r\n *\r\n * @example\r\n * <EmptyState\r\n *   title=\"No Service Records Found\"\r\n *   description=\"There are no service records matching your current filters.\"\r\n *   icon={History}\r\n *   primaryAction={{\r\n *     label: \"Log New Service\",\r\n *     href: \"/vehicles\",\r\n *     icon: <PlusCircle className=\"size-4\" />\r\n *   }}\r\n *   secondaryAction={{\r\n *     label: \"Clear Filters\",\r\n *     onClick: clearFilters\r\n *   }}\r\n * />\r\n */\r\nexport function EmptyState({\r\n  className,\r\n  description,\r\n  icon: Icon,\r\n  primaryAction,\r\n  secondaryAction,\r\n  title,\r\n}: EmptyStateProps) {\r\n  return (\r\n    <div className={cn('space-y-6 text-center py-12', className)}>\r\n      {Icon && (\r\n        <div className=\"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted\">\r\n          <Icon className=\"h-10 w-10 text-muted-foreground\" />\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"space-y-2\">\r\n        <h3 className=\"text-2xl font-semibold text-foreground\">{title}</h3>\r\n        {description && (\r\n          <p className=\"text-muted-foreground max-w-md mx-auto\">\r\n            {description}\r\n          </p>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\r\n        {primaryAction && (\r\n          <ActionButton\r\n            actionType=\"primary\"\r\n            asChild={!!primaryAction.href}\r\n            icon={primaryAction.icon}\r\n            onClick={primaryAction.onClick}\r\n          >\r\n            {primaryAction.href ? (\r\n              <a href={primaryAction.href}>{primaryAction.label}</a>\r\n            ) : (\r\n              primaryAction.label\r\n            )}\r\n          </ActionButton>\r\n        )}\r\n\r\n        {secondaryAction && (\r\n          <ActionButton\r\n            actionType=\"tertiary\"\r\n            asChild={!!secondaryAction.href}\r\n            icon={secondaryAction.icon}\r\n            onClick={secondaryAction.onClick}\r\n          >\r\n            {secondaryAction.href ? (\r\n              <a href={secondaryAction.href}>{secondaryAction.label}</a>\r\n            ) : (\r\n              secondaryAction.label\r\n            )}\r\n          </ActionButton>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * Unified error display component\r\n *\r\n * @example\r\n * <ErrorDisplay message=\"Failed to load data\" onRetry={refetch} />\r\n */\r\nexport function ErrorDisplay({\r\n  className,\r\n  message,\r\n  onRetry,\r\n}: ErrorDisplayProps) {\r\n  return (\r\n    <Alert className={cn('my-4', className)} variant=\"destructive\">\r\n      <AlertCircle className=\"size-4\" />\r\n      <AlertTitle>Error</AlertTitle>\r\n      <AlertDescription>\r\n        <div className=\"mt-2\">\r\n          <p className=\"mb-4 text-sm text-muted-foreground\">{message}</p>\r\n          {onRetry && (\r\n            <ActionButton\r\n              actionType=\"tertiary\"\r\n              icon={<Loader2 className=\"size-4\" />}\r\n              onClick={onRetry}\r\n              size=\"sm\"\r\n            >\r\n              Try Again\r\n            </ActionButton>\r\n          )}\r\n        </div>\r\n      </AlertDescription>\r\n    </Alert>\r\n  );\r\n}\r\n\r\n/**\r\n * Unified loading spinner component\r\n *\r\n * @example\r\n * <LoadingSpinner size=\"md\" text=\"Loading data...\" />\r\n */\r\nexport function LoadingSpinner({\r\n  className,\r\n  fullPage = false,\r\n  size = 'md',\r\n  text,\r\n}: LoadingSpinnerProps) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'flex items-center justify-center',\r\n        fullPage && 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50',\r\n        className\r\n      )}\r\n    >\r\n      <div className=\"flex flex-col items-center\">\r\n        <Loader2\r\n          className={cn('animate-spin text-primary', spinnerSizeClasses[size])}\r\n        />\r\n        {text && (\r\n          <span\r\n            className={cn(\r\n              'mt-2 text-muted-foreground',\r\n              spinnerTextSizeClasses[size]\r\n            )}\r\n          >\r\n            {text}\r\n          </span>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * Unified skeleton loader component\r\n *\r\n * @example\r\n * <SkeletonLoader variant=\"card\" count={3} />\r\n */\r\nexport function SkeletonLoader({\r\n  className,\r\n  count = 1,\r\n  testId = 'loading-skeleton',\r\n  variant = 'default',\r\n}: SkeletonLoaderProps) {\r\n  // Render card skeleton (for entity cards like vehicles, employees)\r\n  if (variant === 'card') {\r\n    return (\r\n      <div\r\n        className={cn(\r\n          'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',\r\n          className\r\n        )}\r\n        data-testid={testId}\r\n      >\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div\r\n            className=\"overflow-hidden rounded-lg border bg-card shadow-md\"\r\n            key={i}\r\n          >\r\n            <Skeleton className=\"aspect-[16/10] w-full\" />\r\n            <div className=\"p-5\">\r\n              <Skeleton className=\"mb-1 h-7 w-3/4\" />\r\n              <Skeleton className=\"mb-3 h-4 w-1/2\" />\r\n              <Skeleton className=\"my-3 h-px w-full\" />\r\n              <div className=\"space-y-2.5\">\r\n                {Array.from({ length: 3 })\r\n                  .fill(0)\r\n                  .map((_, j) => (\r\n                    <div className=\"flex items-center\" key={j}>\r\n                      <Skeleton className=\"mr-2.5 size-5 rounded-full\" />\r\n                      <Skeleton className=\"h-5 w-2/3\" />\r\n                    </div>\r\n                  ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render table skeleton\r\n  if (variant === 'table') {\r\n    return (\r\n      <div className={cn('space-y-3', className)} data-testid={testId}>\r\n        <div className=\"flex gap-4\">\r\n          {Array.from({ length: 3 })\r\n            .fill(0)\r\n            .map((_, i) => (\r\n              <Skeleton className=\"h-8 flex-1\" key={i} />\r\n            ))}\r\n        </div>\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div className=\"flex gap-4\" key={i}>\r\n            {Array.from({ length: 3 })\r\n              .fill(0)\r\n              .map((_, j) => (\r\n                <Skeleton className=\"h-6 flex-1\" key={j} />\r\n              ))}\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render list skeleton\r\n  if (variant === 'list') {\r\n    return (\r\n      <div className={cn('space-y-3', className)} data-testid={testId}>\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div className=\"flex items-center gap-4\" key={i}>\r\n            <Skeleton className=\"size-12 rounded-full\" />\r\n            <div className=\"flex-1 space-y-2\">\r\n              <Skeleton className=\"h-4 w-1/3\" />\r\n              <Skeleton className=\"h-4 w-full\" />\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render stats skeleton\r\n  if (variant === 'stats') {\r\n    return (\r\n      <div\r\n        className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}\r\n        data-testid={testId}\r\n      >\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div className=\"rounded-lg border bg-card p-5 shadow-sm\" key={i}>\r\n            <div className=\"flex justify-between\">\r\n              <Skeleton className=\"h-5 w-1/3\" />\r\n              <Skeleton className=\"size-5 rounded-full\" />\r\n            </div>\r\n            <Skeleton className=\"mt-3 h-8 w-1/2\" />\r\n            <Skeleton className=\"mt-2 h-4 w-2/3\" />\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Default skeleton\r\n  return (\r\n    <div className={cn('space-y-2', className)} data-testid={testId}>\r\n      {new Array(count).fill(0).map((_, i) => (\r\n        <Skeleton className=\"h-5 w-full\" key={i} />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AARA;;;;;;;AAaA,gCAAgC;AAChC,MAAM,qBAAkD;IACtD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEA,qCAAqC;AACrC,MAAM,yBAAsD;IAC1D,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AA+KO,SAAS,WAAc,EAC5B,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,cAAc,EACd,KAAK,EACL,cAAc,EACd,SAAS,EACT,gBAAgB,EAChB,OAAO,EACY;IACnB,IAAI,WAAW;QACb,OACE,kCACE,8OAAC;YAAgB,GAAI,aAAa;gBAAE;YAAU,CAAC;YAAG,MAAK;;;;;;IAG7D;IAEA,IAAI,OAAO;QACT,OACE,gCACE,8OAAC;YACE,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC/B,SAAS;YACR,GAAI,WAAW;gBAAE;YAAQ,CAAC;;;;;;IAInC;IAEA,IAAI,CAAC,QAAS,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,KAAK,GAAI;QACvD,OACE,gCACE,8OAAC;YAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;sBACrC,cAAA,8OAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;IAI7C;IAEA,qBAAO,8OAAC;QAAI,WAAW;kBAAY,SAAS;;;;;;AAC9C;AAqBO,SAAS,WAAW,EACzB,SAAS,EACT,WAAW,EACX,MAAM,IAAI,EACV,aAAa,EACb,eAAe,EACf,KAAK,EACW;IAChB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;;YAC/C,sBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;;;;;;;;;;;0BAIpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;oBACvD,6BACC,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAKP,8OAAC;gBAAI,WAAU;;oBACZ,+BACC,8OAAC,4IAAA,CAAA,eAAY;wBACX,YAAW;wBACX,SAAS,CAAC,CAAC,cAAc,IAAI;wBAC7B,MAAM,cAAc,IAAI;wBACxB,SAAS,cAAc,OAAO;kCAE7B,cAAc,IAAI,iBACjB,8OAAC;4BAAE,MAAM,cAAc,IAAI;sCAAG,cAAc,KAAK;;;;;mCAEjD,cAAc,KAAK;;;;;;oBAKxB,iCACC,8OAAC,4IAAA,CAAA,eAAY;wBACX,YAAW;wBACX,SAAS,CAAC,CAAC,gBAAgB,IAAI;wBAC/B,MAAM,gBAAgB,IAAI;wBAC1B,SAAS,gBAAgB,OAAO;kCAE/B,gBAAgB,IAAI,iBACnB,8OAAC;4BAAE,MAAM,gBAAgB,IAAI;sCAAG,gBAAgB,KAAK;;;;;mCAErD,gBAAgB,KAAK;;;;;;;;;;;;;;;;;;AAOnC;AAQO,SAAS,aAAa,EAC3B,SAAS,EACT,OAAO,EACP,OAAO,EACW;IAClB,qBACE,8OAAC,iIAAA,CAAA,QAAK;QAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAY,SAAQ;;0BAC/C,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,8OAAC,iIAAA,CAAA,aAAU;0BAAC;;;;;;0BACZ,8OAAC,iIAAA,CAAA,mBAAgB;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;wBAClD,yBACC,8OAAC,4IAAA,CAAA,eAAY;4BACX,YAAW;4BACX,oBAAM,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BACzB,SAAS;4BACT,MAAK;sCACN;;;;;;;;;;;;;;;;;;;;;;;AAQb;AAQO,SAAS,eAAe,EAC7B,SAAS,EACT,WAAW,KAAK,EAChB,OAAO,IAAI,EACX,IAAI,EACgB;IACpB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,oCACA,YAAY,wDACZ;kBAGF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iNAAA,CAAA,UAAO;oBACN,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B,kBAAkB,CAAC,KAAK;;;;;;gBAEpE,sBACC,8OAAC;oBACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,8BACA,sBAAsB,CAAC,KAAK;8BAG7B;;;;;;;;;;;;;;;;;AAMb;AAQO,SAAS,eAAe,EAC7B,SAAS,EACT,QAAQ,CAAC,EACT,SAAS,kBAAkB,EAC3B,UAAU,SAAS,EACC;IACpB,mEAAmE;IACnE,IAAI,YAAY,QAAQ;QACtB,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wDACA;YAEF,eAAa;sBAEZ,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC;oBACC,WAAU;;sCAGV,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAE,GACrB,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;2CAFkB;;;;;;;;;;;;;;;;;mBAX3C;;;;;;;;;;IAsBf;IAEA,wBAAwB;IACxB,IAAI,YAAY,SAAS;QACvB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAY,eAAa;;8BACvD,8OAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GACrB,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;2BAAkB;;;;;;;;;;gBAG3C,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GACrB,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;+BAAkB;;;;;uBAJX;;;;;;;;;;;IAUzC;IAEA,uBAAuB;IACvB,IAAI,YAAY,QAAQ;QACtB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAY,eAAa;sBACtD,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;mBAJsB;;;;;;;;;;IAUtD;IAEA,wBAAwB;IACxB,IAAI,YAAY,SAAS;QACvB,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;YAC1D,eAAa;sBAEZ,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;mBANwC;;;;;;;;;;IAWtE;IAEA,mBAAmB;IACnB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAAY,eAAa;kBACtD,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC,oIAAA,CAAA,WAAQ;gBAAC,WAAU;eAAkB;;;;;;;;;;AAI9C", "debugId": null}}, {"offset": {"line": 2003, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/services/WebSocketManager.ts"], "sourcesContent": ["/**\r\n * @file Unified WebSocket Manager for WorkHub Application\r\n * Provides centralized WebSocket connection management with domain-specific channels\r\n * Follows SRP and DRY principles with smart fallback strategies\r\n * @module services/WebSocketManager\r\n */\r\n\r\nimport type { Socket } from 'socket.io-client';\r\n\r\nimport { io } from 'socket.io-client';\r\n\r\nimport { getEnvironmentConfig } from '../config/environment';\r\nimport { supabase } from '../supabase';\r\nimport { getTokenRefreshService } from './TokenRefreshService';\r\n/*import logger from '../utils/logger';\r\n\r\n/**\r\n * WebSocket connection states\r\n */\r\nexport type ConnectionState =\r\n  | 'connected'\r\n  | 'connecting'\r\n  | 'disconnected'\r\n  | 'error'\r\n  | 'reconnecting';\r\n\r\n/**\r\n * Domain-specific channels for organized event management\r\n */\r\nexport type DomainChannel = 'crud' | 'notifications' | 'reliability' | 'system';\r\n\r\n/**\r\n * Event subscription callback type\r\n */\r\nexport type EventCallback<T = any> = (data: T) => void;\r\n\r\n/**\r\n * WebSocket configuration options\r\n */\r\nexport interface WebSocketConfig {\r\n  autoConnect?: boolean;\r\n  reconnectAttempts?: number;\r\n  reconnectDelay?: number;\r\n  timeout?: number;\r\n  url?: string;\r\n}\r\n\r\n/**\r\n * Unified WebSocket Manager\r\n * Implements Singleton pattern for single connection per application\r\n * Provides domain-specific channels and centralized subscription management\r\n */\r\nexport class WebSocketManager {\r\n  private static instance: null | WebSocketManager = null;\r\n  private readonly config: Required<WebSocketConfig>;\r\n  private connectionState: ConnectionState = 'disconnected';\r\n  private reconnectAttempts = 0;\r\n  private socket: null | Socket = null;\r\n  private readonly stateListeners = new Set<(state: ConnectionState) => void>();\r\n  private readonly subscriptions = new Map<string, Set<EventCallback>>();\r\n\r\n  private constructor(config: WebSocketConfig = {}) {\r\n    this.config = {\r\n      autoConnect: config.autoConnect ?? true,\r\n      reconnectAttempts: config.reconnectAttempts ?? 5,\r\n      reconnectDelay: config.reconnectDelay ?? 1000,\r\n      timeout: config.timeout ?? 10_000,\r\n      url:\r\n        config.url ??\r\n        process.env.NEXT_PUBLIC_WEBSOCKET_URL ??\r\n        getEnvironmentConfig()\r\n          .wsUrl.replace('ws://', 'http://')\r\n          .replace('wss://', 'https://'),\r\n    };\r\n\r\n    if (this.config.autoConnect) {\r\n      this.connect();\r\n    }\r\n\r\n    // Subscribe to token refresh events\r\n    this.setupTokenRefreshHandling();\r\n  }\r\n\r\n  /**\r\n   * Get singleton instance\r\n   */\r\n  public static getInstance(config?: WebSocketConfig): WebSocketManager {\r\n    WebSocketManager.instance ??= new WebSocketManager(config);\r\n    return WebSocketManager.instance;\r\n  }\r\n\r\n  /**\r\n   * Connect to WebSocket server\r\n   */\r\n  public async connect(): Promise<void> {\r\n    if (this.socket?.connected) {\r\n      console.debug('WebSocket already connected');\r\n      return;\r\n    }\r\n\r\n    this.setConnectionState('connecting');\r\n\r\n    try {\r\n      // Get current session and token for authentication\r\n      const {\r\n        data: { session },\r\n        error,\r\n      } = await supabase.auth.getSession();\r\n\r\n      if (error) {\r\n        console.warn('Failed to get session for WebSocket connection:', error);\r\n      }\r\n\r\n      const connectionOptions: any = {\r\n        forceNew: true,\r\n        timeout: this.config.timeout,\r\n        transports: ['websocket', 'polling'],\r\n        withCredentials: true, // Ensure cookies are sent with WebSocket handshake\r\n      };\r\n\r\n      // Add authentication token if available\r\n      if (session?.access_token) {\r\n        connectionOptions.auth = {\r\n          token: session.access_token,\r\n        };\r\n        console.debug('🔐 WebSocket connecting with authentication token');\r\n\r\n        // Validate token expiration\r\n        const tokenExpiry = session.expires_at ? session.expires_at * 1000 : 0;\r\n        const now = Date.now();\r\n        const timeUntilExpiry = tokenExpiry - now;\r\n\r\n        if (timeUntilExpiry <= 60_000) {\r\n          // Less than 1 minute\r\n          console.warn('⚠️ WebSocket token expires soon, may need refresh');\r\n        }\r\n      } else {\r\n        console.warn(\r\n          '⚠️ WebSocket connecting without authentication token - connection may fail'\r\n        );\r\n      }\r\n\r\n      this.socket = io(this.config.url, connectionOptions);\r\n\r\n      this.setupEventHandlers();\r\n    } catch (error) {\r\n      console.error('Failed to connect WebSocket:', error);\r\n      this.setConnectionState('error');\r\n      this.scheduleReconnect();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleanup resources\r\n   */\r\n  public destroy(): void {\r\n    this.disconnect();\r\n    this.subscriptions.clear();\r\n    this.stateListeners.clear();\r\n    WebSocketManager.instance = null;\r\n  }\r\n\r\n  /**\r\n   * Disconnect from WebSocket server\r\n   */\r\n  public disconnect(): void {\r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n      this.socket = null;\r\n    }\r\n    this.setConnectionState('disconnected');\r\n    this.reconnectAttempts = 0;\r\n  }\r\n\r\n  /**\r\n   * Emit event to specific domain channel\r\n   */\r\n  public emit(channel: DomainChannel, event: string, data?: any): void {\r\n    if (!this.socket?.connected) {\r\n      console.warn(`Cannot emit ${channel}:${event} - WebSocket not connected`);\r\n      return;\r\n    }\r\n\r\n    this.socket.emit(event, data);\r\n  }\r\n\r\n  /**\r\n   * Get current connection state\r\n   */\r\n  public getConnectionState(): ConnectionState {\r\n    return this.connectionState;\r\n  }\r\n\r\n  /**\r\n   * Check if WebSocket is connected\r\n   */\r\n  public isConnected(): boolean {\r\n    return (\r\n      this.connectionState === 'connected' && this.socket?.connected === true\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Join domain-specific room\r\n   */\r\n  public joinRoom(room: string): void {\r\n    if (!this.socket?.connected) {\r\n      console.warn(`Cannot join room ${room} - WebSocket not connected`);\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('join-room', room);\r\n  }\r\n\r\n  /**\r\n   * Leave domain-specific room\r\n   */\r\n  public leaveRoom(room: string): void {\r\n    if (!this.socket?.connected) {\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('leave-room', room);\r\n  }\r\n\r\n  /**\r\n   * Subscribe to connection state changes\r\n   */\r\n  public onStateChange(callback: (state: ConnectionState) => void): () => void {\r\n    this.stateListeners.add(callback);\r\n\r\n    return () => {\r\n      this.stateListeners.delete(callback);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Subscribe to domain-specific events\r\n   */\r\n  public subscribe<T = any>(\r\n    channel: DomainChannel,\r\n    event: string,\r\n    callback: EventCallback<T>\r\n  ): () => void {\r\n    const eventKey = `${channel}:${event}`;\r\n\r\n    if (!this.subscriptions.has(eventKey)) {\r\n      this.subscriptions.set(eventKey, new Set());\r\n    }\r\n\r\n    this.subscriptions.get(eventKey)!.add(callback);\r\n\r\n    // Set up socket listener if connected\r\n    if (this.socket?.connected && event) {\r\n      this.socket.on(event, callback);\r\n    }\r\n\r\n    // Return unsubscribe function\r\n    return () => {\r\n      const callbacks = this.subscriptions.get(eventKey);\r\n      if (callbacks) {\r\n        callbacks.delete(callback);\r\n        if (callbacks.size === 0) {\r\n          this.subscriptions.delete(eventKey);\r\n        }\r\n      }\r\n\r\n      if (this.socket && event) {\r\n        this.socket.off(event, callback);\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Handle authentication errors by triggering token refresh\r\n   */\r\n  private handleAuthenticationError(): void {\r\n    const tokenRefreshService = getTokenRefreshService();\r\n\r\n    console.log('🔐 Handling WebSocket authentication error...');\r\n\r\n    // Disconnect current socket to prevent further auth errors\r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n      this.socket = null;\r\n    }\r\n\r\n    // Attempt to refresh token\r\n    tokenRefreshService\r\n      .refreshNow()\r\n      .then(success => {\r\n        if (success) {\r\n          console.log(\r\n            '🔄 Token refresh successful, retrying WebSocket connection'\r\n          );\r\n          // The reconnection will be handled by setupTokenRefreshHandling\r\n        } else {\r\n          console.error('🔄 Token refresh failed, scheduling normal reconnect');\r\n          this.scheduleReconnect();\r\n        }\r\n      })\r\n      .catch(error => {\r\n        console.error('🔄 Token refresh error:', error);\r\n        this.scheduleReconnect();\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Resubscribe to all events after reconnection\r\n   */\r\n  private resubscribeToEvents(): void {\r\n    if (!this.socket) return;\r\n\r\n    for (const [eventKey, callbacks] of this.subscriptions) {\r\n      const [, event] = eventKey.split(':');\r\n      for (const callback of callbacks) {\r\n        if (event) {\r\n          this.socket!.on(event, callback);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Schedule reconnection with exponential backoff\r\n   */\r\n  private scheduleReconnect(): void {\r\n    if (this.reconnectAttempts >= this.config.reconnectAttempts) {\r\n      console.error('Max reconnection attempts reached');\r\n      this.setConnectionState('error');\r\n      return;\r\n    }\r\n\r\n    this.setConnectionState('reconnecting');\r\n    this.reconnectAttempts++;\r\n\r\n    const delay =\r\n      this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\r\n\r\n    setTimeout(() => {\r\n      console.info(\r\n        `Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts})`\r\n      );\r\n      this.connect();\r\n    }, delay);\r\n  }\r\n\r\n  /**\r\n   * Set connection state and notify listeners\r\n   */\r\n  private setConnectionState(state: ConnectionState): void {\r\n    if (this.connectionState !== state) {\r\n      this.connectionState = state;\r\n      for (const listener of this.stateListeners) listener(state);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup socket event handlers\r\n   */\r\n  private setupEventHandlers(): void {\r\n    if (!this.socket) return;\r\n\r\n    this.socket.on('connect', () => {\r\n      console.info('WebSocket connected');\r\n      this.setConnectionState('connected');\r\n      this.reconnectAttempts = 0;\r\n      this.resubscribeToEvents();\r\n    });\r\n\r\n    this.socket.on('disconnect', reason => {\r\n      console.warn('WebSocket disconnected:', reason);\r\n      this.setConnectionState('disconnected');\r\n\r\n      if (reason === 'io server disconnect') {\r\n        // Server initiated disconnect, don't reconnect automatically\r\n        return;\r\n      }\r\n\r\n      this.scheduleReconnect();\r\n    });\r\n\r\n    this.socket.on('connect_error', error => {\r\n      console.error('WebSocket connection error:', error);\r\n      this.setConnectionState('error');\r\n\r\n      // Check if error is authentication-related\r\n      if (\r\n        error.message?.includes('Authentication') ||\r\n        error.message?.includes('token') ||\r\n        error.message?.includes('No token provided') ||\r\n        error.message?.includes('Unauthorized')\r\n      ) {\r\n        console.warn(\r\n          '🔐 Authentication error detected, attempting token refresh'\r\n        );\r\n        this.handleAuthenticationError();\r\n      } else {\r\n        this.scheduleReconnect();\r\n      }\r\n    });\r\n\r\n    // Listen for authentication errors from the server\r\n    this.socket.on('auth_error', errorData => {\r\n      console.error('🔐 Server authentication error:', errorData);\r\n      this.handleAuthenticationError();\r\n    });\r\n\r\n    // Listen for token refresh requests from server\r\n    this.socket.on('token_refresh_required', () => {\r\n      console.warn('🔄 Server requested token refresh');\r\n      this.handleAuthenticationError();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Setup token refresh event handling\r\n   */\r\n  private setupTokenRefreshHandling(): void {\r\n    const tokenRefreshService = getTokenRefreshService();\r\n\r\n    tokenRefreshService.subscribe((event, _data) => {\r\n      switch (event) {\r\n        case 'critical_refresh_failed': {\r\n          console.error(\r\n            '🔄 Critical token refresh failure, disconnecting WebSocket'\r\n          );\r\n          this.disconnect();\r\n          this.setConnectionState('error');\r\n          break;\r\n        }\r\n\r\n        case 'refresh_failed': {\r\n          console.error(\r\n            '🔄 Token refresh failed, WebSocket may lose connection'\r\n          );\r\n          break;\r\n        }\r\n\r\n        case 'refresh_success': {\r\n          console.log(\r\n            '🔄 Token refreshed, reconnecting WebSocket with new token'\r\n          );\r\n          // Disconnect current connection and reconnect with new token\r\n          if (this.socket) {\r\n            this.socket.disconnect();\r\n            this.socket = null;\r\n          }\r\n          // Reconnect with fresh token\r\n          setTimeout(() => this.connect(), 500);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * Get the singleton WebSocket manager instance\r\n */\r\nexport const getWebSocketManager = (\r\n  config?: WebSocketConfig\r\n): WebSocketManager => {\r\n  return WebSocketManager.getInstance(config);\r\n};\r\n\r\n/**\r\n * Hook for WebSocket connection state\r\n */\r\nexport const useWebSocketState = () => {\r\n  const manager = getWebSocketManager();\r\n  return {\r\n    connectionState: manager.getConnectionState(),\r\n    isConnected: manager.isConnected(),\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAID;AAAA;AAEA;AACA;AACA;;;;;AAuCO,MAAM;IACX,OAAe,WAAoC,KAAK;IACvC,OAAkC;IAC3C,kBAAmC,eAAe;IAClD,oBAAoB,EAAE;IACtB,SAAwB,KAAK;IACpB,iBAAiB,IAAI,MAAwC;IAC7D,gBAAgB,IAAI,MAAkC;IAEvE,YAAoB,SAA0B,CAAC,CAAC,CAAE;QAChD,IAAI,CAAC,MAAM,GAAG;YACZ,aAAa,OAAO,WAAW,IAAI;YACnC,mBAAmB,OAAO,iBAAiB,IAAI;YAC/C,gBAAgB,OAAO,cAAc,IAAI;YACzC,SAAS,OAAO,OAAO,IAAI;YAC3B,KACE,OAAO,GAAG,IACV,QAAQ,GAAG,CAAC,yBAAyB,IACrC,CAAA,GAAA,mIAAA,CAAA,uBAAoB,AAAD,IAChB,KAAK,CAAC,OAAO,CAAC,SAAS,WACvB,OAAO,CAAC,UAAU;QACzB;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC3B,IAAI,CAAC,OAAO;QACd;QAEA,oCAAoC;QACpC,IAAI,CAAC,yBAAyB;IAChC;IAEA;;GAEC,GACD,OAAc,YAAY,MAAwB,EAAoB;QACpE,iBAAiB,QAAQ,KAAK,IAAI,iBAAiB;QACnD,OAAO,iBAAiB,QAAQ;IAClC;IAEA;;GAEC,GACD,MAAa,UAAyB;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE,WAAW;YAC1B,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC;QAExB,IAAI;YACF,mDAAmD;YACnD,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EACjB,KAAK,EACN,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAElC,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,mDAAmD;YAClE;YAEA,MAAM,oBAAyB;gBAC7B,UAAU;gBACV,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO;gBAC5B,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,iBAAiB;YACnB;YAEA,wCAAwC;YACxC,IAAI,SAAS,cAAc;gBACzB,kBAAkB,IAAI,GAAG;oBACvB,OAAO,QAAQ,YAAY;gBAC7B;gBACA,QAAQ,KAAK,CAAC;gBAEd,4BAA4B;gBAC5B,MAAM,cAAc,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG,OAAO;gBACrE,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,kBAAkB,cAAc;gBAEtC,IAAI,mBAAmB,QAAQ;oBAC7B,qBAAqB;oBACrB,QAAQ,IAAI,CAAC;gBACf;YACF,OAAO;gBACL,QAAQ,IAAI,CACV;YAEJ;YAEA,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YAElC,IAAI,CAAC,kBAAkB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,iBAAiB;QACxB;IACF;IAEA;;GAEC,GACD,AAAO,UAAgB;QACrB,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,aAAa,CAAC,KAAK;QACxB,IAAI,CAAC,cAAc,CAAC,KAAK;QACzB,iBAAiB,QAAQ,GAAG;IAC9B;IAEA;;GAEC,GACD,AAAO,aAAmB;QACxB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG;IAC3B;IAEA;;GAEC,GACD,AAAO,KAAK,OAAsB,EAAE,KAAa,EAAE,IAAU,EAAQ;QACnE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,MAAM,0BAA0B,CAAC;YACxE;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;IAC1B;IAEA;;GAEC,GACD,AAAO,qBAAsC;QAC3C,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA;;GAEC,GACD,AAAO,cAAuB;QAC5B,OACE,IAAI,CAAC,eAAe,KAAK,eAAe,IAAI,CAAC,MAAM,EAAE,cAAc;IAEvE;IAEA;;GAEC,GACD,AAAO,SAAS,IAAY,EAAQ;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B,QAAQ,IAAI,CAAC,CAAC,iBAAiB,EAAE,KAAK,0BAA0B,CAAC;YACjE;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa;IAChC;IAEA;;GAEC,GACD,AAAO,UAAU,IAAY,EAAQ;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc;IACjC;IAEA;;GAEC,GACD,AAAO,cAAc,QAA0C,EAAc;QAC3E,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAExB,OAAO;YACL,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,AAAO,UACL,OAAsB,EACtB,KAAa,EACb,QAA0B,EACd;QACZ,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,OAAO;QAEtC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW;YACrC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,IAAI;QACvC;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAW,GAAG,CAAC;QAEtC,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,EAAE,aAAa,OAAO;YACnC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;QACxB;QAEA,8BAA8B;QAC9B,OAAO;YACL,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;YACzC,IAAI,WAAW;gBACb,UAAU,MAAM,CAAC;gBACjB,IAAI,UAAU,IAAI,KAAK,GAAG;oBACxB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;gBAC5B;YACF;YAEA,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO;gBACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO;YACzB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,4BAAkC;QACxC,MAAM,sBAAsB,CAAA,GAAA,6IAAA,CAAA,yBAAsB,AAAD;QAEjD,QAAQ,GAAG,CAAC;QAEZ,2DAA2D;QAC3D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;QAEA,2BAA2B;QAC3B,oBACG,UAAU,GACV,IAAI,CAAC,CAAA;YACJ,IAAI,SAAS;gBACX,QAAQ,GAAG,CACT;YAEF,gEAAgE;YAClE,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,IAAI,CAAC,iBAAiB;YACxB;QACF,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,2BAA2B;YACzC,IAAI,CAAC,iBAAiB;QACxB;IACJ;IAEA;;GAEC,GACD,AAAQ,sBAA4B;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,KAAK,MAAM,CAAC,UAAU,UAAU,IAAI,IAAI,CAAC,aAAa,CAAE;YACtD,MAAM,GAAG,MAAM,GAAG,SAAS,KAAK,CAAC;YACjC,KAAK,MAAM,YAAY,UAAW;gBAChC,IAAI,OAAO;oBACT,IAAI,CAAC,MAAM,CAAE,EAAE,CAAC,OAAO;gBACzB;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,oBAA0B;QAChC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;YAC3D,QAAQ,KAAK,CAAC;YACd,IAAI,CAAC,kBAAkB,CAAC;YACxB;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,iBAAiB;QAEtB,MAAM,QACJ,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG;QAEpE,WAAW;YACT,QAAQ,IAAI,CACV,CAAC,yBAAyB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAExF,IAAI,CAAC,OAAO;QACd,GAAG;IACL;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAsB,EAAQ;QACvD,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO;YAClC,IAAI,CAAC,eAAe,GAAG;YACvB,KAAK,MAAM,YAAY,IAAI,CAAC,cAAc,CAAE,SAAS;QACvD;IACF;IAEA;;GAEC,GACD,AAAQ,qBAA2B;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YACxB,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,iBAAiB,GAAG;YACzB,IAAI,CAAC,mBAAmB;QAC1B;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAA;YAC3B,QAAQ,IAAI,CAAC,2BAA2B;YACxC,IAAI,CAAC,kBAAkB,CAAC;YAExB,IAAI,WAAW,wBAAwB;gBACrC,6DAA6D;gBAC7D;YACF;YAEA,IAAI,CAAC,iBAAiB;QACxB;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAA;YAC9B,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,CAAC,kBAAkB,CAAC;YAExB,2CAA2C;YAC3C,IACE,MAAM,OAAO,EAAE,SAAS,qBACxB,MAAM,OAAO,EAAE,SAAS,YACxB,MAAM,OAAO,EAAE,SAAS,wBACxB,MAAM,OAAO,EAAE,SAAS,iBACxB;gBACA,QAAQ,IAAI,CACV;gBAEF,IAAI,CAAC,yBAAyB;YAChC,OAAO;gBACL,IAAI,CAAC,iBAAiB;YACxB;QACF;QAEA,mDAAmD;QACnD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAA;YAC3B,QAAQ,KAAK,CAAC,mCAAmC;YACjD,IAAI,CAAC,yBAAyB;QAChC;QAEA,gDAAgD;QAChD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,0BAA0B;YACvC,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,yBAAyB;QAChC;IACF;IAEA;;GAEC,GACD,AAAQ,4BAAkC;QACxC,MAAM,sBAAsB,CAAA,GAAA,6IAAA,CAAA,yBAAsB,AAAD;QAEjD,oBAAoB,SAAS,CAAC,CAAC,OAAO;YACpC,OAAQ;gBACN,KAAK;oBAA2B;wBAC9B,QAAQ,KAAK,CACX;wBAEF,IAAI,CAAC,UAAU;wBACf,IAAI,CAAC,kBAAkB,CAAC;wBACxB;oBACF;gBAEA,KAAK;oBAAkB;wBACrB,QAAQ,KAAK,CACX;wBAEF;oBACF;gBAEA,KAAK;oBAAmB;wBACtB,QAAQ,GAAG,CACT;wBAEF,6DAA6D;wBAC7D,IAAI,IAAI,CAAC,MAAM,EAAE;4BACf,IAAI,CAAC,MAAM,CAAC,UAAU;4BACtB,IAAI,CAAC,MAAM,GAAG;wBAChB;wBACA,6BAA6B;wBAC7B,WAAW,IAAM,IAAI,CAAC,OAAO,IAAI;wBACjC;oBACF;YACF;QACF;IACF;AACF;AAKO,MAAM,sBAAsB,CACjC;IAEA,OAAO,iBAAiB,WAAW,CAAC;AACtC;AAKO,MAAM,oBAAoB;IAC/B,MAAM,UAAU;IAChB,OAAO;QACL,iBAAiB,QAAQ,kBAAkB;QAC3C,aAAa,QAAQ,WAAW;IAClC;AACF", "debugId": null}}, {"offset": {"line": 2338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/api/useSmartQuery.ts"], "sourcesContent": ["/**\r\n * @file Smart Query Hook with WebSocket Integration\r\n * Automatically disables polling when WebSocket is connected\r\n * Follows modern best practices for real-time data management\r\n * @module hooks/useSmartQuery\r\n */\r\n\r\nimport type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query';\r\n\r\nimport { useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { useEffect, useState } from 'react';\r\n\r\nimport type { DomainChannel } from '../../lib/services/WebSocketManager';\r\n\r\nimport { getWebSocketManager } from '../../lib/services/WebSocketManager';\r\n\r\n/**\r\n * Mapping of domain channels to Socket.IO room names\r\n * This ensures the frontend joins the correct rooms that the backend emits events to\r\n */\r\nconst CHANNEL_ROOM_MAPPING: Record<DomainChannel, string> = {\r\n  crud: 'entity-updates',\r\n  notifications: 'notifications-monitoring',\r\n  reliability: 'reliability-monitoring',\r\n  system: 'system-monitoring',\r\n} as const;\r\n\r\n/**\r\n * Smart query configuration for WebSocket integration\r\n * @template T - The data type returned by the query function\r\n */\r\nexport interface SmartQueryConfig<T = unknown> {\r\n  /**\r\n   * Domain channel for WebSocket events\r\n   * Automatically maps to appropriate Socket.IO room via CHANNEL_ROOM_MAPPING\r\n   */\r\n  channel: DomainChannel;\r\n  /** Whether to enable fallback polling when WebSocket is disconnected */\r\n  enableFallback?: boolean;\r\n  /** Whether to enable WebSocket integration and room joining */\r\n  enableWebSocket?: boolean;\r\n  /** Events that should trigger data refetch when received via WebSocket */\r\n  events: string[];\r\n  /** Fallback polling interval when WebSocket is disconnected (ms) */\r\n  fallbackInterval?: number;\r\n}\r\n\r\n/**\r\n * Hook for CRUD operations with smart real-time updates\r\n */\r\nexport function useCrudQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  entityType: string,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'crud',\r\n      events: [\r\n        `${entityType}:created`,\r\n        `${entityType}:updated`,\r\n        `${entityType}:deleted`,\r\n        `refresh:${entityType}`,\r\n      ],\r\n      fallbackInterval: 30_000,\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Hook for system notifications with smart real-time updates\r\n */\r\nexport function useNotificationQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'notifications',\r\n      events: ['notification-created', 'notification-updated'],\r\n      fallbackInterval: 60_000, // 1 minute for notifications\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Hook for reliability monitoring with smart real-time updates\r\n */\r\nexport function useReliabilityQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  monitoringType: 'alerts' | 'circuit-breakers' | 'health' | 'metrics',\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  // Increased intervals to reduce aggressive polling and cancellations\r\n  const intervalMap = {\r\n    alerts: 30_000, // 30 seconds for alerts (was 10s)\r\n    'circuit-breakers': 60_000, // 60 seconds for circuit breakers (was 30s)\r\n    health: 45_000, // 45 seconds for health (was 15s)\r\n    metrics: 60_000, // 60 seconds for metrics (was 30s)\r\n  };\r\n\r\n  const webSocketManager = getWebSocketManager();\r\n\r\n  // Join reliability monitoring room when WebSocket is connected\r\n  useEffect(() => {\r\n    if (webSocketManager.isConnected()) {\r\n      console.debug(\r\n        `[ReliabilityQuery] Joining reliability-monitoring room for ${monitoringType}`\r\n      );\r\n      webSocketManager.joinRoom('reliability-monitoring');\r\n    }\r\n\r\n    // Subscribe to connection state changes to join room when connected\r\n    const unsubscribe = webSocketManager.onStateChange(state => {\r\n      if (state === 'connected') {\r\n        console.debug(\r\n          `[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ${monitoringType}`\r\n        );\r\n        webSocketManager.joinRoom('reliability-monitoring');\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      unsubscribe();\r\n      // Leave room when component unmounts\r\n      if (webSocketManager.isConnected()) {\r\n        webSocketManager.leaveRoom('reliability-monitoring');\r\n      }\r\n    };\r\n  }, [webSocketManager, monitoringType]);\r\n\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'reliability',\r\n      events: [\r\n        `${monitoringType}-update`,\r\n        `${monitoringType}-created`,\r\n        `${monitoringType}-resolved`,\r\n      ],\r\n      fallbackInterval: intervalMap[monitoringType],\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Smart Query Hook with Socket.IO Room Management\r\n *\r\n * Combines React Query with WebSocket real-time updates and automatic Socket.IO room joining.\r\n * This hook automatically:\r\n * - Joins the appropriate Socket.IO room based on the domain channel\r\n * - Subscribes to WebSocket events for real-time data updates\r\n * - Switches between WebSocket and polling based on connection state\r\n * - Handles room cleanup when component unmounts\r\n *\r\n * **Room Mapping:**\r\n * - `crud` channel → `entity-updates` room\r\n * - `reliability` channel → `reliability-monitoring` room\r\n * - `notifications` channel → `notifications-monitoring` room\r\n * - `system` channel → `system-monitoring` room\r\n *\r\n * @template T - The data type returned by the query function\r\n * @template E - The error type for failed queries\r\n * @param queryKey - React Query key for caching and invalidation\r\n * @param queryFn - Data fetching function that returns a Promise<T>\r\n * @param config - Smart query configuration including channel and events\r\n * @param options - Additional React Query options (merged with smart defaults)\r\n * @returns Enhanced query result with WebSocket integration and connection state\r\n */\r\nexport function useSmartQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  config: SmartQueryConfig<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n): UseQueryResult<T, E> & {\r\n  isUsingFallback: boolean;\r\n  isWebSocketConnected: boolean;\r\n} {\r\n  const {\r\n    channel,\r\n    enableFallback = true,\r\n    enableWebSocket = true,\r\n    events,\r\n    fallbackInterval = 30_000,\r\n  } = config;\r\n\r\n  const [isWebSocketConnected, setIsWebSocketConnected] = useState(false);\r\n  const webSocketManager = getWebSocketManager();\r\n\r\n  // Track WebSocket connection state\r\n  useEffect(() => {\r\n    const updateConnectionState = () => {\r\n      setIsWebSocketConnected(webSocketManager.isConnected());\r\n    };\r\n\r\n    // Initial state\r\n    updateConnectionState();\r\n\r\n    // Subscribe to state changes\r\n    const unsubscribe = webSocketManager.onStateChange(updateConnectionState);\r\n\r\n    return unsubscribe;\r\n  }, [webSocketManager]);\r\n\r\n  // Determine if we should use fallback polling\r\n  const isUsingFallback =\r\n    enableFallback && (!enableWebSocket || !isWebSocketConnected);\r\n\r\n  // Configure React Query options based on WebSocket state\r\n  const queryOptions: UseQueryOptions<T, E> = {\r\n    // Longer cache time for better performance\r\n    gcTime: 10 * 60 * 1000, // 10 minutes\r\n    queryFn,\r\n    queryKey,\r\n    // Disable polling when WebSocket is connected\r\n    refetchInterval: isUsingFallback ? fallbackInterval : false,\r\n    refetchOnReconnect: true, // Always refetch on network reconnect\r\n    // Enable background refetch only when using fallback\r\n    refetchOnWindowFocus: isUsingFallback,\r\n    // Shorter stale time when using WebSocket (real-time updates)\r\n    staleTime: isWebSocketConnected ? 0 : 30_000,\r\n    ...options,\r\n  };\r\n\r\n  const queryClient = useQueryClient();\r\n  const queryResult = useQuery<T, E>(queryOptions);\r\n\r\n  // Manage Socket.IO room joining/leaving based on channel\r\n  useEffect(() => {\r\n    if (!enableWebSocket || !isWebSocketConnected) {\r\n      return;\r\n    }\r\n\r\n    const roomName = CHANNEL_ROOM_MAPPING[channel];\r\n    if (!roomName) {\r\n      console.warn(\r\n        `[SmartQuery] No room mapping found for channel: ${channel}`\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Join the appropriate room for this channel\r\n    try {\r\n      webSocketManager.joinRoom(roomName);\r\n      console.log(\r\n        `[SmartQuery] Joined room: ${roomName} for channel: ${channel}`\r\n      );\r\n    } catch (error) {\r\n      console.error(`[SmartQuery] Failed to join room ${roomName}:`, error);\r\n    }\r\n\r\n    // Cleanup: leave room when component unmounts or dependencies change\r\n    return () => {\r\n      try {\r\n        webSocketManager.leaveRoom(roomName);\r\n        console.log(\r\n          `[SmartQuery] Left room: ${roomName} for channel: ${channel}`\r\n        );\r\n      } catch (error) {\r\n        console.error(`[SmartQuery] Failed to leave room ${roomName}:`, error);\r\n      }\r\n    };\r\n  }, [enableWebSocket, isWebSocketConnected, channel, webSocketManager]);\r\n\r\n  // Subscribe to WebSocket events for real-time updates\r\n  useEffect(() => {\r\n    if (!enableWebSocket || !isWebSocketConnected || events.length === 0) {\r\n      return;\r\n    }\r\n\r\n    const unsubscribers: (() => void)[] = [];\r\n\r\n    // Subscribe to each event\r\n    for (const event of events) {\r\n      const unsubscribe = webSocketManager.subscribe(\r\n        channel,\r\n        event,\r\n        (data: unknown) => {\r\n          console.log(\r\n            `[SmartQuery] WebSocket event received: ${channel}:${event}`,\r\n            data\r\n          );\r\n\r\n          // Invalidate the specific query to trigger refetch\r\n          queryClient.invalidateQueries({ queryKey });\r\n        }\r\n      );\r\n\r\n      unsubscribers.push(unsubscribe);\r\n    }\r\n\r\n    return () => {\r\n      for (const unsubscribe of unsubscribers) unsubscribe();\r\n    };\r\n  }, [\r\n    enableWebSocket,\r\n    isWebSocketConnected,\r\n    events,\r\n    channel,\r\n    webSocketManager,\r\n    queryClient,\r\n    queryKey,\r\n  ]);\r\n\r\n  return {\r\n    ...queryResult,\r\n    isUsingFallback,\r\n    isWebSocketConnected,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for system-wide events with smart real-time updates\r\n */\r\nexport function useSystemQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'system',\r\n      events: ['system-update', 'config-changed'],\r\n      fallbackInterval: 120_000, // 2 minutes for system events\r\n    },\r\n    options\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;AAID;AAAA;AACA;AAIA;;;;AAEA;;;CAGC,GACD,MAAM,uBAAsD;IAC1D,MAAM;IACN,eAAe;IACf,aAAa;IACb,QAAQ;AACV;AAyBO,SAAS,aACd,QAAmB,EACnB,OAAyB,EACzB,UAAkB,EAClB,OAA6D;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YACN,GAAG,WAAW,QAAQ,CAAC;YACvB,GAAG,WAAW,QAAQ,CAAC;YACvB,GAAG,WAAW,QAAQ,CAAC;YACvB,CAAC,QAAQ,EAAE,YAAY;SACxB;QACD,kBAAkB;IACpB,GACA;AAEJ;AAKO,SAAS,qBACd,QAAmB,EACnB,OAAyB,EACzB,OAA6D;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YAAC;YAAwB;SAAuB;QACxD,kBAAkB;IACpB,GACA;AAEJ;AAKO,SAAS,oBACd,QAAmB,EACnB,OAAyB,EACzB,cAAoE,EACpE,OAA6D;IAE7D,qEAAqE;IACrE,MAAM,cAAc;QAClB,QAAQ;QACR,oBAAoB;QACpB,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,mBAAmB,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD;IAE3C,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,WAAW,IAAI;YAClC,QAAQ,KAAK,CACX,CAAC,2DAA2D,EAAE,gBAAgB;YAEhF,iBAAiB,QAAQ,CAAC;QAC5B;QAEA,oEAAoE;QACpE,MAAM,cAAc,iBAAiB,aAAa,CAAC,CAAA;YACjD,IAAI,UAAU,aAAa;gBACzB,QAAQ,KAAK,CACX,CAAC,gFAAgF,EAAE,gBAAgB;gBAErG,iBAAiB,QAAQ,CAAC;YAC5B;QACF;QAEA,OAAO;YACL;YACA,qCAAqC;YACrC,IAAI,iBAAiB,WAAW,IAAI;gBAClC,iBAAiB,SAAS,CAAC;YAC7B;QACF;IACF,GAAG;QAAC;QAAkB;KAAe;IAErC,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YACN,GAAG,eAAe,OAAO,CAAC;YAC1B,GAAG,eAAe,QAAQ,CAAC;YAC3B,GAAG,eAAe,SAAS,CAAC;SAC7B;QACD,kBAAkB,WAAW,CAAC,eAAe;IAC/C,GACA;AAEJ;AA0BO,SAAS,cACd,QAAmB,EACnB,OAAyB,EACzB,MAA2B,EAC3B,OAA6D;IAK7D,MAAM,EACJ,OAAO,EACP,iBAAiB,IAAI,EACrB,kBAAkB,IAAI,EACtB,MAAM,EACN,mBAAmB,MAAM,EAC1B,GAAG;IAEJ,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,mBAAmB,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD;IAE3C,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,wBAAwB;YAC5B,wBAAwB,iBAAiB,WAAW;QACtD;QAEA,gBAAgB;QAChB;QAEA,6BAA6B;QAC7B,MAAM,cAAc,iBAAiB,aAAa,CAAC;QAEnD,OAAO;IACT,GAAG;QAAC;KAAiB;IAErB,8CAA8C;IAC9C,MAAM,kBACJ,kBAAkB,CAAC,CAAC,mBAAmB,CAAC,oBAAoB;IAE9D,yDAAyD;IACzD,MAAM,eAAsC;QAC1C,2CAA2C;QAC3C,QAAQ,KAAK,KAAK;QAClB;QACA;QACA,8CAA8C;QAC9C,iBAAiB,kBAAkB,mBAAmB;QACtD,oBAAoB;QACpB,qDAAqD;QACrD,sBAAsB;QACtB,8DAA8D;QAC9D,WAAW,uBAAuB,IAAI;QACtC,GAAG,OAAO;IACZ;IAEA,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,cAAc,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAQ;IAEnC,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,sBAAsB;YAC7C;QACF;QAEA,MAAM,WAAW,oBAAoB,CAAC,QAAQ;QAC9C,IAAI,CAAC,UAAU;YACb,QAAQ,IAAI,CACV,CAAC,gDAAgD,EAAE,SAAS;YAE9D;QACF;QAEA,6CAA6C;QAC7C,IAAI;YACF,iBAAiB,QAAQ,CAAC;YAC1B,QAAQ,GAAG,CACT,CAAC,0BAA0B,EAAE,SAAS,cAAc,EAAE,SAAS;QAEnE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,SAAS,CAAC,CAAC,EAAE;QACjE;QAEA,qEAAqE;QACrE,OAAO;YACL,IAAI;gBACF,iBAAiB,SAAS,CAAC;gBAC3B,QAAQ,GAAG,CACT,CAAC,wBAAwB,EAAE,SAAS,cAAc,EAAE,SAAS;YAEjE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;YAClE;QACF;IACF,GAAG;QAAC;QAAiB;QAAsB;QAAS;KAAiB;IAErE,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,OAAO,MAAM,KAAK,GAAG;YACpE;QACF;QAEA,MAAM,gBAAgC,EAAE;QAExC,0BAA0B;QAC1B,KAAK,MAAM,SAAS,OAAQ;YAC1B,MAAM,cAAc,iBAAiB,SAAS,CAC5C,SACA,OACA,CAAC;gBACC,QAAQ,GAAG,CACT,CAAC,uCAAuC,EAAE,QAAQ,CAAC,EAAE,OAAO,EAC5D;gBAGF,mDAAmD;gBACnD,YAAY,iBAAiB,CAAC;oBAAE;gBAAS;YAC3C;YAGF,cAAc,IAAI,CAAC;QACrB;QAEA,OAAO;YACL,KAAK,MAAM,eAAe,cAAe;QAC3C;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO;QACL,GAAG,WAAW;QACd;QACA;IACF;AACF;AAKO,SAAS,eACd,QAAmB,EACnB,OAAyB,EACzB,OAA6D;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YAAC;YAAiB;SAAiB;QAC3C,kBAAkB;IACpB,GACA;AAEJ", "debugId": null}}, {"offset": {"line": 2550, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/useServiceRecords.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\r\n\r\nimport type { EnrichedServiceRecord } from '@/lib/types/domain';\r\nimport type { ServiceRecord as DomainServiceRecord } from '@/lib/types/domain';\r\nimport type { ServiceRecordApiResponse } from '@/lib/types/apiContracts';\r\n\r\nimport { ApiClient } from '@/lib/api/core/apiClient';\r\nimport {\r\n  BaseApiService,\r\n  type DataTransformer,\r\n  type ServiceConfig,\r\n} from '@/lib/api/core/baseApiService';\r\nimport { useCrudQuery } from '@/hooks/api/useSmartQuery';\r\n\r\nexport interface CreateServiceRecordPayload {\r\n  cost?: number | undefined; // Allow undefined explicitly due to exactOptionalPropertyTypes\r\n  date: string;\r\n  employeeId?: null | number;\r\n  notes?: string;\r\n  odometer: number;\r\n  servicePerformed: string[];\r\n  vehicleId: number;\r\n}\r\n\r\nconst ServiceRecordTransformer: DataTransformer<DomainServiceRecord> = {\r\n  fromApi(apiData: any): DomainServiceRecord {\r\n    // Handle both regular service records and enriched service records\r\n    const baseRecord = {\r\n      cost: apiData.cost,\r\n      createdAt: apiData.createdAt,\r\n      date: apiData.date,\r\n      employeeId: apiData.employeeId,\r\n      id: apiData.id,\r\n      notes: apiData.notes,\r\n      odometer: apiData.odometer,\r\n      servicePerformed: Array.isArray(apiData.servicePerformed)\r\n        ? apiData.servicePerformed\r\n        : [],\r\n      updatedAt: apiData.updatedAt,\r\n      vehicleId: apiData.vehicleId,\r\n    };\r\n\r\n    // Add enriched fields if available (for EnrichedServiceRecord)\r\n    if (apiData.vehicleMake || apiData.vehicleModel || apiData.vehicleYear) {\r\n      return {\r\n        ...baseRecord,\r\n        vehicleMake: apiData.vehicleMake || 'Unknown',\r\n        vehicleModel: apiData.vehicleModel || 'Unknown',\r\n        vehicleYear: apiData.vehicleYear || new Date().getFullYear(),\r\n        licensePlate: apiData.licensePlate || null,\r\n        employeeName: apiData.employeeName || null,\r\n      } as any; // Cast to handle both ServiceRecord and EnrichedServiceRecord\r\n    }\r\n\r\n    return baseRecord;\r\n  },\r\n  toApi: (data: any) => data,\r\n};\r\n\r\nclass ServiceRecordApiService extends BaseApiService<\r\n  DomainServiceRecord,\r\n  CreateServiceRecordPayload,\r\n  Partial<CreateServiceRecordPayload>\r\n> {\r\n  protected endpoint = '/servicerecords';\r\n  protected transformer: DataTransformer<DomainServiceRecord> =\r\n    ServiceRecordTransformer;\r\n\r\n  constructor(apiClient: ApiClient, config?: ServiceConfig) {\r\n    super(apiClient, {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes for service records\r\n      retryAttempts: 3,\r\n      circuitBreakerThreshold: 5,\r\n      enableMetrics: true,\r\n      ...config,\r\n    });\r\n  }\r\n\r\n  async getById(id: string): Promise<EnrichedServiceRecord> {\r\n    return this.executeWithInfrastructure(\r\n      `${this.endpoint}:${id}`,\r\n      async () => {\r\n        const response = await this.apiClient.get<EnrichedServiceRecord>(\r\n          `${this.endpoint}/${id}`\r\n        );\r\n\r\n        // Apply transformer if available\r\n        const transformedResponse = this.transformer.fromApi\r\n          ? this.transformer.fromApi(response)\r\n          : response;\r\n\r\n        return transformedResponse as EnrichedServiceRecord;\r\n      }\r\n    );\r\n  }\r\n\r\n  // Custom update method for service records, handling vehicleId in path\r\n  async updateRecord(\r\n    id: string,\r\n    vehicleId: number, // Keep vehicleId as a separate parameter for clarity in this specific method\r\n    data: Partial<CreateServiceRecordPayload>\r\n  ): Promise<DomainServiceRecord> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      // Remove vehicleId from data if present to avoid conflicts, use the parameter instead\r\n      const { vehicleId: _, ...updateData } = data;\r\n      const response = await this.apiClient.put<any>(\r\n        `/vehicles/${vehicleId}/servicerecords/${id}`,\r\n        updateData // Send only the actual update data\r\n      );\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`)); // Invalidate all service records\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:${id}`)); // Invalidate specific record\r\n      this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`)); // Invalidate vehicle-specific caches\r\n      return this.transformer.fromApi\r\n        ? this.transformer.fromApi(response)\r\n        : response;\r\n    });\r\n  }\r\n\r\n  // Custom delete method for service records, handling vehicleId in path\r\n  async deleteRecord(id: string, vehicleId: number): Promise<void> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      await this.apiClient.delete(\r\n        `/vehicles/${vehicleId}/servicerecords/${id}`\r\n      );\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`)); // Invalidate all service records\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:${id}`)); // Invalidate specific record\r\n      this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`)); // Invalidate vehicle-specific caches\r\n    });\r\n  }\r\n\r\n  async getVehicleServiceRecords(\r\n    vehicleId: number\r\n  ): Promise<EnrichedServiceRecord[]> {\r\n    return this.executeWithInfrastructure(\r\n      `vehicles:${vehicleId}:servicerecords`,\r\n      async () => {\r\n        const response = await this.apiClient.get<EnrichedServiceRecord[]>(\r\n          `/vehicles/${vehicleId}/servicerecords`\r\n        );\r\n        return response;\r\n      }\r\n    );\r\n  }\r\n\r\n  async createVehicleServiceRecord(\r\n    vehicleId: number,\r\n    data: CreateServiceRecordPayload\r\n  ): Promise<DomainServiceRecord> {\r\n    return this.executeWithInfrastructure(null, async () => {\r\n      const response = await this.apiClient.post<any>(\r\n        `/vehicles/${vehicleId}/servicerecords`,\r\n        data\r\n      );\r\n\r\n      this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));\r\n      this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`));\r\n\r\n      return this.transformer.fromApi\r\n        ? this.transformer.fromApi(response)\r\n        : response;\r\n    });\r\n  }\r\n\r\n  // Method to fetch enriched service records\r\n  async getAllEnriched(): Promise<EnrichedServiceRecord[]> {\r\n    return this.executeWithInfrastructure(\r\n      `${this.endpoint}:enriched`,\r\n      async () => {\r\n        const response = await this.apiClient.get<EnrichedServiceRecord[]>(\r\n          `${this.endpoint}/enriched`\r\n        );\r\n\r\n        // Apply transformer to each record\r\n        return response.map(record => {\r\n          const transformed = this.transformer.fromApi\r\n            ? this.transformer.fromApi(record)\r\n            : record;\r\n          return transformed as EnrichedServiceRecord;\r\n        });\r\n      }\r\n    );\r\n  }\r\n}\r\n\r\nimport { apiClient } from '../../api';\r\nconst serviceRecordApiService = new ServiceRecordApiService(apiClient);\r\n\r\nexport const SERVICE_RECORD_QUERY_KEY = 'serviceRecords';\r\n\r\nexport const useServiceRecord = (\r\n  id: string,\r\n  options?: { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<EnrichedServiceRecord, Error>(\r\n    [SERVICE_RECORD_QUERY_KEY, id],\r\n    () => serviceRecordApiService.getById(id),\r\n    'serviceRecord',\r\n    {\r\n      enabled: options?.enabled ?? !!id, // Only enable if ID is present\r\n      staleTime: 1000 * 60 * 5,\r\n    }\r\n  );\r\n};\r\n\r\nexport const useEnrichedServiceRecords = (options?: { enabled?: boolean }) => {\r\n  return useCrudQuery<EnrichedServiceRecord[], Error>(\r\n    [SERVICE_RECORD_QUERY_KEY, 'allEnriched'],\r\n    () => serviceRecordApiService.getAllEnriched(),\r\n    'serviceRecord',\r\n    {\r\n      enabled: options?.enabled ?? true,\r\n      staleTime: 1000 * 60 * 5,\r\n    }\r\n  );\r\n};\r\n\r\nexport const useVehicleServiceRecords = (\r\n  vehicleId: number,\r\n  options?: { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<EnrichedServiceRecord[], Error>(\r\n    [SERVICE_RECORD_QUERY_KEY, 'forVehicle', vehicleId],\r\n    () => serviceRecordApiService.getVehicleServiceRecords(vehicleId),\r\n    'serviceRecord',\r\n    {\r\n      enabled: options?.enabled ?? true,\r\n      staleTime: 1000 * 60 * 5,\r\n    }\r\n  );\r\n};\r\n\r\nexport const useCreateServiceRecord = () => {\r\n  const queryClient = useQueryClient();\r\n  return useMutation<DomainServiceRecord, Error, CreateServiceRecordPayload>({\r\n    mutationFn: async (\r\n      newRecordData: CreateServiceRecordPayload\r\n    ): Promise<DomainServiceRecord> => {\r\n      const { vehicleId } = newRecordData;\r\n      return serviceRecordApiService.createVehicleServiceRecord(\r\n        vehicleId,\r\n        newRecordData\r\n      );\r\n    },\r\n    onSuccess: (data, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, 'allEnriched'],\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, 'forVehicle', variables.vehicleId],\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: ['vehicle', variables.vehicleId],\r\n      });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateServiceRecord = () => {\r\n  const queryClient = useQueryClient();\r\n  return useMutation<\r\n    DomainServiceRecord,\r\n    Error,\r\n    { id: string; vehicleId: number; data: Partial<CreateServiceRecordPayload> }\r\n  >({\r\n    mutationFn: async ({\r\n      id,\r\n      vehicleId,\r\n      data,\r\n    }): Promise<DomainServiceRecord> => {\r\n      // Call the custom updateRecord method\r\n      return serviceRecordApiService.updateRecord(id, vehicleId, data);\r\n    },\r\n    onSuccess: (_, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, 'allEnriched'],\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, variables.id],\r\n      }); // Invalidate specific record\r\n      // Use variables.vehicleId directly as it's now part of the mutation variables\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, 'forVehicle', variables.vehicleId],\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: ['vehicle', variables.vehicleId],\r\n      });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useDeleteServiceRecord = () => {\r\n  const queryClient = useQueryClient();\r\n  return useMutation<void, Error, { id: string; vehicleId: number }>({\r\n    mutationFn: async ({ id, vehicleId }): Promise<void> => {\r\n      // Call the custom deleteRecord method\r\n      return serviceRecordApiService.deleteRecord(id, vehicleId);\r\n    },\r\n    onSuccess: (_, variables) => {\r\n      // Use variables instead of id for consistency\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, 'allEnriched'],\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, variables.id],\r\n      });\r\n      // Invalidate all vehicle-specific caches since we don't know which vehicle this record belonged to\r\n      queryClient.invalidateQueries({\r\n        queryKey: [SERVICE_RECORD_QUERY_KEY, 'forVehicle'],\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: ['vehicle'],\r\n      });\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAOA;AAKA;AA4KA;AAAA;;;;AAhKA,MAAM,2BAAiE;IACrE,SAAQ,OAAY;QAClB,mEAAmE;QACnE,MAAM,aAAa;YACjB,MAAM,QAAQ,IAAI;YAClB,WAAW,QAAQ,SAAS;YAC5B,MAAM,QAAQ,IAAI;YAClB,YAAY,QAAQ,UAAU;YAC9B,IAAI,QAAQ,EAAE;YACd,OAAO,QAAQ,KAAK;YACpB,UAAU,QAAQ,QAAQ;YAC1B,kBAAkB,MAAM,OAAO,CAAC,QAAQ,gBAAgB,IACpD,QAAQ,gBAAgB,GACxB,EAAE;YACN,WAAW,QAAQ,SAAS;YAC5B,WAAW,QAAQ,SAAS;QAC9B;QAEA,+DAA+D;QAC/D,IAAI,QAAQ,WAAW,IAAI,QAAQ,YAAY,IAAI,QAAQ,WAAW,EAAE;YACtE,OAAO;gBACL,GAAG,UAAU;gBACb,aAAa,QAAQ,WAAW,IAAI;gBACpC,cAAc,QAAQ,YAAY,IAAI;gBACtC,aAAa,QAAQ,WAAW,IAAI,IAAI,OAAO,WAAW;gBAC1D,cAAc,QAAQ,YAAY,IAAI;gBACtC,cAAc,QAAQ,YAAY,IAAI;YACxC,GAAU,8DAA8D;QAC1E;QAEA,OAAO;IACT;IACA,OAAO,CAAC,OAAc;AACxB;AAEA,MAAM,gCAAgC,2IAAA,CAAA,iBAAc;IAKxC,WAAW,kBAAkB;IAC7B,cACR,yBAAyB;IAE3B,YAAY,SAAoB,EAAE,MAAsB,CAAE;QACxD,KAAK,CAAC,WAAW;YACf,eAAe,IAAI,KAAK;YACxB,eAAe;YACf,yBAAyB;YACzB,eAAe;YACf,GAAG,MAAM;QACX;IACF;IAEA,MAAM,QAAQ,EAAU,EAAkC;QACxD,OAAO,IAAI,CAAC,yBAAyB,CACnC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,EACxB;YACE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI;YAG1B,iCAAiC;YACjC,MAAM,sBAAsB,IAAI,CAAC,WAAW,CAAC,OAAO,GAChD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YACzB;YAEJ,OAAO;QACT;IAEJ;IAEA,uEAAuE;IACvE,MAAM,aACJ,EAAU,EACV,SAAiB,EACjB,IAAyC,EACX;QAC9B,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,sFAAsF;YACtF,MAAM,EAAE,WAAW,CAAC,EAAE,GAAG,YAAY,GAAG;YACxC,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,UAAU,EAAE,UAAU,gBAAgB,EAAE,IAAI,EAC7C,WAAW,mCAAmC;;YAEhD,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,iCAAiC;YACjG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,IAAI,6BAA6B;YAClG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,IAAI,qCAAqC;YAC1G,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,GAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YACzB;QACN;IACF;IAEA,uEAAuE;IACvE,MAAM,aAAa,EAAU,EAAE,SAAiB,EAAiB;QAC/D,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CACzB,CAAC,UAAU,EAAE,UAAU,gBAAgB,EAAE,IAAI;YAE/C,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,iCAAiC;YACjG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,IAAI,6BAA6B;YAClG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,IAAI,qCAAqC;QAC5G;IACF;IAEA,MAAM,yBACJ,SAAiB,EACiB;QAClC,OAAO,IAAI,CAAC,yBAAyB,CACnC,CAAC,SAAS,EAAE,UAAU,eAAe,CAAC,EACtC;YACE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,CAAC,UAAU,EAAE,UAAU,eAAe,CAAC;YAEzC,OAAO;QACT;IAEJ;IAEA,MAAM,2BACJ,SAAiB,EACjB,IAAgC,EACF;QAC9B,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM;YAC1C,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CACxC,CAAC,UAAU,EAAE,UAAU,eAAe,CAAC,EACvC;YAGF,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC5D,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAEjE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,GAC3B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,YACzB;QACN;IACF;IAEA,2CAA2C;IAC3C,MAAM,iBAAmD;QACvD,OAAO,IAAI,CAAC,yBAAyB,CACnC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAC3B;YACE,MAAM,WAAW,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CACvC,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAG7B,mCAAmC;YACnC,OAAO,SAAS,GAAG,CAAC,CAAA;gBAClB,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,OAAO,GACxC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UACzB;gBACJ,OAAO;YACT;QACF;IAEJ;AACF;;AAGA,MAAM,0BAA0B,IAAI,wBAAwB,0IAAA,CAAA,YAAS;AAE9D,MAAM,2BAA2B;AAEjC,MAAM,mBAAmB,CAC9B,IACA;IAEA,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;QAAC;QAA0B;KAAG,EAC9B,IAAM,wBAAwB,OAAO,CAAC,KACtC,iBACA;QACE,SAAS,SAAS,WAAW,CAAC,CAAC;QAC/B,WAAW,OAAO,KAAK;IACzB;AAEJ;AAEO,MAAM,4BAA4B,CAAC;IACxC,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;QAAC;QAA0B;KAAc,EACzC,IAAM,wBAAwB,cAAc,IAC5C,iBACA;QACE,SAAS,SAAS,WAAW;QAC7B,WAAW,OAAO,KAAK;IACzB;AAEJ;AAEO,MAAM,2BAA2B,CACtC,WACA;IAEA,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;QAAC;QAA0B;QAAc;KAAU,EACnD,IAAM,wBAAwB,wBAAwB,CAAC,YACvD,iBACA;QACE,SAAS,SAAS,WAAW;QAC7B,WAAW,OAAO,KAAK;IACzB;AAEJ;AAEO,MAAM,yBAAyB;IACpC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAA0D;QACzE,YAAY,OACV;YAEA,MAAM,EAAE,SAAS,EAAE,GAAG;YACtB,OAAO,wBAAwB,0BAA0B,CACvD,WACA;QAEJ;QACA,WAAW,CAAC,MAAM;YAChB,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAA0B;iBAAc;YACrD;YACA,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAA0B;oBAAc,UAAU,SAAS;iBAAC;YACzE;YACA,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAAW,UAAU,SAAS;iBAAC;YAC5C;QACF;IACF;AACF;AAEO,MAAM,yBAAyB;IACpC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAIf;QACA,YAAY,OAAO,EACjB,EAAE,EACF,SAAS,EACT,IAAI,EACL;YACC,sCAAsC;YACtC,OAAO,wBAAwB,YAAY,CAAC,IAAI,WAAW;QAC7D;QACA,WAAW,CAAC,GAAG;YACb,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAA0B;iBAAc;YACrD;YACA,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAA0B,UAAU,EAAE;iBAAC;YACpD,IAAI,6BAA6B;YACjC,8EAA8E;YAC9E,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAA0B;oBAAc,UAAU,SAAS;iBAAC;YACzE;YACA,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAAW,UAAU,SAAS;iBAAC;YAC5C;QACF;IACF;AACF;AAEO,MAAM,yBAAyB;IACpC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAkD;QACjE,YAAY,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE;YAClC,sCAAsC;YACtC,OAAO,wBAAwB,YAAY,CAAC,IAAI;QAClD;QACA,WAAW,CAAC,GAAG;YACb,8CAA8C;YAC9C,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAA0B;iBAAc;YACrD;YACA,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAA0B,UAAU,EAAE;iBAAC;YACpD;YACA,mGAAmG;YACnG,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAA0B;iBAAa;YACpD;YACA,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;iBAAU;YACvB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 2806, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { Check, ChevronDown, ChevronUp } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ children, className, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    className={cn(\r\n      'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"size-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    className={cn(\r\n      'flex cursor-default items-center justify-center py-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"size-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    className={cn(\r\n      'flex cursor-default items-center justify-center py-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"size-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ children, className, position = 'popper', ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      className={cn(\r\n        'relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\r\n        position === 'popper' &&\r\n          'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n        className\r\n      )}\r\n      position={position}\r\n      ref={ref}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          'p-1',\r\n          position === 'popper' &&\r\n            'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]'\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    className={cn('py-1.5 pl-8 pr-2 text-sm font-semibold', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.memo(\r\n  React.forwardRef<\r\n    React.ElementRef<typeof SelectPrimitive.Item>,\r\n    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n  >(({ children, className, ...props }, forwardedRef) => {\r\n    const composedRefs = React.useCallback(\r\n      (node: any) => {\r\n        if (typeof forwardedRef === 'function') {\r\n          forwardedRef(node);\r\n        } else if (forwardedRef) {\r\n          (forwardedRef as React.MutableRefObject<any>).current = node;\r\n        }\r\n      },\r\n      [forwardedRef]\r\n    );\r\n\r\n    return (\r\n      <SelectPrimitive.Item\r\n        className={cn(\r\n          'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\r\n          className\r\n        )}\r\n        ref={composedRefs}\r\n        {...props}\r\n      >\r\n        <span className=\"absolute left-2 flex size-3.5 items-center justify-center\">\r\n          <SelectPrimitive.ItemIndicator>\r\n            <Check className=\"size-4\" />\r\n          </SelectPrimitive.ItemIndicator>\r\n        </span>\r\n\r\n        <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n      </SelectPrimitive.Item>\r\n    );\r\n  })\r\n);\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AAAA;AAAA;AACA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACV,KAAK;YACJ,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,OAAU,AAAD,gBAC1B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGb,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACpC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EACnC,CAAC;QACC,IAAI,OAAO,iBAAiB,YAAY;YACtC,aAAa;QACf,OAAO,IAAI,cAAc;YACtB,aAA6C,OAAO,GAAG;QAC1D;IACF,GACA;QAAC;KAAa;IAGhB,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAEF,KAAK;QACJ,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEF,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QAC1C,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3010, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      className={cn('w-full caption-bottom text-sm', className)}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  </div>\r\n));\r\nTable.displayName = 'Table';\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead className={cn('[&_tr]:border-b', className)} ref={ref} {...props} />\r\n));\r\nTableHeader.displayName = 'TableHeader';\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    className={cn('[&_tr:last-child]:border-0', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableBody.displayName = 'TableBody';\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    className={cn(\r\n      'border-t bg-muted/50 font-medium [&>tr]:last:border-b-0',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableFooter.displayName = 'TableFooter';\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    className={cn(\r\n      'border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableRow.displayName = 'TableRow';\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    className={cn(\r\n      'h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableHead.displayName = 'TableHead';\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    className={cn('p-4 align-middle [&:has([role=checkbox])]:pr-0', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableCell.displayName = 'TableCell';\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    className={cn('mt-4 text-sm text-muted-foreground', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableCaption.displayName = 'TableCaption';\r\n\r\nexport {\r\n  Table,\r\n  TableBody,\r\n  TableCaption,\r\n  TableCell,\r\n  TableFooter,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;AAAA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC/C,KAAK;YACJ,GAAG,KAAK;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAM,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAY,KAAK;QAAM,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC5C,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAChE,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/tables/DataTable.tsx"], "sourcesContent": ["/**\r\n * Generic DataTable Component\r\n *\r\n * A reusable table component built on TanStack Table that provides:\r\n * - Sorting, filtering, and pagination\r\n * - Row selection with bulk actions\r\n * - Column visibility controls\r\n * - Responsive design with shadcn/ui styling\r\n * - Type-safe implementation with generics\r\n *\r\n * Based on the excellent patterns from DelegationTable.tsx\r\n */\r\n\r\n'use client';\r\n\r\nimport type {\r\n  ColumnDef,\r\n  ColumnFiltersState,\r\n  SortingState,\r\n  VisibilityState,\r\n} from '@tanstack/react-table';\r\n\r\nimport {\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getFilteredRowModel,\r\n  getPaginationRowModel,\r\n  getSortedRowModel,\r\n  useReactTable,\r\n} from '@tanstack/react-table';\r\nimport {\r\n  ChevronDown,\r\n  Settings,\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  ChevronsLeft,\r\n  ChevronsRight,\r\n} from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Input } from '@/components/ui/input';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@/components/ui/table';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport interface DataTableProps<T> {\r\n  data: T[];\r\n  columns: ColumnDef<T>[];\r\n  className?: string;\r\n  onRowClick?: (row: T) => void;\r\n  searchPlaceholder?: string;\r\n  searchColumn?: string;\r\n  enableRowSelection?: boolean;\r\n  enableColumnVisibility?: boolean;\r\n  enableGlobalFilter?: boolean;\r\n  pageSize?: number;\r\n  emptyMessage?: string;\r\n  // Advanced features from DelegationTable\r\n  enableBulkActions?: boolean;\r\n  bulkActions?: Array<{\r\n    label: string;\r\n    icon?: React.ComponentType<{ className?: string }>;\r\n    onClick: (selectedRows: T[]) => void;\r\n    variant?: 'default' | 'destructive';\r\n  }>;\r\n  // Professional styling options\r\n  tableClassName?: string;\r\n  headerClassName?: string;\r\n  rowClassName?: string;\r\n}\r\n\r\nexport function DataTable<T>({\r\n  data,\r\n  columns,\r\n  className = '',\r\n  onRowClick,\r\n  searchPlaceholder = 'Search...',\r\n  searchColumn,\r\n  enableRowSelection = false,\r\n  enableColumnVisibility = true,\r\n  enableGlobalFilter = true,\r\n  pageSize = 10,\r\n  emptyMessage = 'No results found.',\r\n  enableBulkActions = false,\r\n  bulkActions = [],\r\n  tableClassName = '',\r\n  headerClassName = '',\r\n  rowClassName = '',\r\n}: DataTableProps<T>) {\r\n  const [sorting, setSorting] = React.useState<SortingState>([]);\r\n  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(\r\n    []\r\n  );\r\n  const [columnVisibility, setColumnVisibility] =\r\n    React.useState<VisibilityState>({});\r\n  const [rowSelection, setRowSelection] = React.useState({});\r\n  const [globalFilter, setGlobalFilter] = React.useState('');\r\n\r\n  const table = useReactTable({\r\n    data,\r\n    columns,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getPaginationRowModel: getPaginationRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    onSortingChange: setSorting,\r\n    onColumnFiltersChange: setColumnFilters,\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    onRowSelectionChange: setRowSelection,\r\n    onGlobalFilterChange: setGlobalFilter,\r\n    state: {\r\n      sorting,\r\n      columnFilters,\r\n      columnVisibility,\r\n      rowSelection,\r\n      globalFilter,\r\n    },\r\n    initialState: {\r\n      pagination: {\r\n        pageSize,\r\n      },\r\n    },\r\n  });\r\n\r\n  // Handle search input for specific column\r\n  const handleSearch = (value: string) => {\r\n    if (searchColumn) {\r\n      table.getColumn(searchColumn)?.setFilterValue(value);\r\n    } else {\r\n      setGlobalFilter(value);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={cn('space-y-4', className)}>\r\n      <Card className=\"shadow-md\">\r\n        {/* Table Controls Header */}\r\n        <div className=\"flex items-center justify-between p-4\">\r\n          {/* Search Input */}\r\n          {enableGlobalFilter && (\r\n            <Input\r\n              placeholder={searchPlaceholder}\r\n              value={\r\n                searchColumn\r\n                  ? ((table\r\n                      .getColumn(searchColumn)\r\n                      ?.getFilterValue() as string) ?? '')\r\n                  : globalFilter\r\n              }\r\n              onChange={event => handleSearch(event.target.value)}\r\n              className=\"max-w-sm\"\r\n            />\r\n          )}\r\n\r\n          <div className=\"flex items-center space-x-2\">\r\n            {/* Bulk Actions */}\r\n            {enableBulkActions &&\r\n              enableRowSelection &&\r\n              table.getFilteredSelectedRowModel().rows.length > 0 && (\r\n                <DropdownMenu>\r\n                  <DropdownMenuTrigger asChild>\r\n                    <Button variant=\"outline\" size=\"sm\">\r\n                      Actions ({table.getFilteredSelectedRowModel().rows.length}\r\n                      )\r\n                      <ChevronDown className=\"ml-2 h-4 w-4\" />\r\n                    </Button>\r\n                  </DropdownMenuTrigger>\r\n                  <DropdownMenuContent align=\"end\">\r\n                    {bulkActions.map((action, index) => (\r\n                      <DropdownMenuItem\r\n                        key={index}\r\n                        onClick={() =>\r\n                          action.onClick(\r\n                            table\r\n                              .getFilteredSelectedRowModel()\r\n                              .rows.map(row => row.original)\r\n                          )\r\n                        }\r\n                        className={\r\n                          action.variant === 'destructive'\r\n                            ? 'text-destructive'\r\n                            : ''\r\n                        }\r\n                      >\r\n                        {action.icon && (\r\n                          <action.icon className=\"mr-2 h-4 w-4\" />\r\n                        )}\r\n                        {action.label}\r\n                      </DropdownMenuItem>\r\n                    ))}\r\n                  </DropdownMenuContent>\r\n                </DropdownMenu>\r\n              )}\r\n\r\n            {/* Column Visibility */}\r\n            {enableColumnVisibility && (\r\n              <DropdownMenu>\r\n                <DropdownMenuTrigger asChild>\r\n                  <Button variant=\"outline\">\r\n                    <Settings className=\"mr-2 h-4 w-4\" />\r\n                    Columns\r\n                    <ChevronDown className=\"ml-2 h-4 w-4\" />\r\n                  </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent align=\"end\" className=\"w-[150px]\">\r\n                  {table\r\n                    .getAllColumns()\r\n                    .filter(column => column.getCanHide())\r\n                    .map(column => {\r\n                      return (\r\n                        <DropdownMenuCheckboxItem\r\n                          key={column.id}\r\n                          className=\"capitalize\"\r\n                          checked={column.getIsVisible()}\r\n                          onCheckedChange={value =>\r\n                            column.toggleVisibility(!!value)\r\n                          }\r\n                        >\r\n                          {column.id}\r\n                        </DropdownMenuCheckboxItem>\r\n                      );\r\n                    })}\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Table Content */}\r\n        <CardContent className=\"p-0\">\r\n          <div className={cn('border-t', tableClassName)}>\r\n            <Table>\r\n              <TableHeader>\r\n                {table.getHeaderGroups().map(headerGroup => (\r\n                  <TableRow\r\n                    key={headerGroup.id}\r\n                    className={cn(\r\n                      'border-b border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800',\r\n                      headerClassName\r\n                    )}\r\n                  >\r\n                    {headerGroup.headers.map(header => {\r\n                      return (\r\n                        <TableHead\r\n                          key={header.id}\r\n                          className=\"py-4 font-semibold text-gray-900 dark:text-white\"\r\n                        >\r\n                          {header.isPlaceholder ? null : (\r\n                            <div\r\n                              className={cn(\r\n                                'flex items-center space-x-1',\r\n                                header.column.getCanSort() &&\r\n                                  'cursor-pointer select-none hover:text-gray-600 dark:hover:text-gray-300'\r\n                              )}\r\n                              onClick={header.column.getToggleSortingHandler()}\r\n                            >\r\n                              <span>\r\n                                {flexRender(\r\n                                  header.column.columnDef.header,\r\n                                  header.getContext()\r\n                                )}\r\n                              </span>\r\n                            </div>\r\n                          )}\r\n                        </TableHead>\r\n                      );\r\n                    })}\r\n                  </TableRow>\r\n                ))}\r\n              </TableHeader>\r\n              <TableBody>\r\n                {table.getRowModel().rows?.length ? (\r\n                  table.getRowModel().rows.map(row => (\r\n                    <TableRow\r\n                      key={row.id}\r\n                      data-state={row.getIsSelected() && 'selected'}\r\n                      className={cn(\r\n                        'border-b border-gray-100 hover:bg-gray-50 dark:border-gray-800 dark:hover:bg-gray-800/50',\r\n                        onRowClick && 'cursor-pointer',\r\n                        row.getIsSelected() && 'bg-blue-50 dark:bg-blue-900/20',\r\n                        rowClassName\r\n                      )}\r\n                      onClick={() => onRowClick?.(row.original)}\r\n                    >\r\n                      {row.getVisibleCells().map(cell => (\r\n                        <TableCell key={cell.id}>\r\n                          {flexRender(\r\n                            cell.column.columnDef.cell,\r\n                            cell.getContext()\r\n                          )}\r\n                        </TableCell>\r\n                      ))}\r\n                    </TableRow>\r\n                  ))\r\n                ) : (\r\n                  <TableRow>\r\n                    <TableCell\r\n                      colSpan={columns.length}\r\n                      className=\"h-24 text-center\"\r\n                    >\r\n                      {emptyMessage}\r\n                    </TableCell>\r\n                  </TableRow>\r\n                )}\r\n              </TableBody>\r\n            </Table>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Pagination Footer */}\r\n      <Card className=\"flex items-center justify-between border-t p-4\">\r\n        <div className=\"flex-1 text-sm text-muted-foreground\">\r\n          {enableRowSelection &&\r\n          table.getFilteredSelectedRowModel().rows.length > 0 ? (\r\n            <>\r\n              {table.getFilteredSelectedRowModel().rows.length} of{' '}\r\n              {table.getFilteredRowModel().rows.length} row(s) selected.\r\n            </>\r\n          ) : (\r\n            <>\r\n              Showing{' '}\r\n              {table.getState().pagination.pageIndex *\r\n                table.getState().pagination.pageSize +\r\n                1}{' '}\r\n              to{' '}\r\n              {Math.min(\r\n                (table.getState().pagination.pageIndex + 1) *\r\n                  table.getState().pagination.pageSize,\r\n                table.getFilteredRowModel().rows.length\r\n              )}{' '}\r\n              of {table.getFilteredRowModel().rows.length} entries\r\n              {table.getFilteredRowModel().rows.length !== data.length &&\r\n                ` (filtered from ${data.length} total)`}\r\n            </>\r\n          )}\r\n        </div>\r\n        <div className=\"flex items-center space-x-6 lg:space-x-8\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <p className=\"text-sm font-medium\">Rows per page</p>\r\n            <Select\r\n              value={`${table.getState().pagination.pageSize}`}\r\n              onValueChange={value => {\r\n                table.setPageSize(Number(value));\r\n              }}\r\n            >\r\n              <SelectTrigger className=\"h-8 w-[70px]\">\r\n                <SelectValue\r\n                  placeholder={table.getState().pagination.pageSize}\r\n                />\r\n              </SelectTrigger>\r\n              <SelectContent side=\"top\">\r\n                {[10, 20, 30, 40, 50].map(pageSize => (\r\n                  <SelectItem key={pageSize} value={`${pageSize}`}>\r\n                    {pageSize}\r\n                  </SelectItem>\r\n                ))}\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n          <div className=\"flex w-[100px] items-center justify-center text-sm font-medium\">\r\n            Page {table.getState().pagination.pageIndex + 1} of{' '}\r\n            {table.getPageCount()}\r\n          </div>\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n              onClick={() => table.setPageIndex(0)}\r\n              disabled={!table.getCanPreviousPage()}\r\n            >\r\n              <span className=\"sr-only\">Go to first page</span>\r\n              <ChevronsLeft className=\"h-4 w-4\" />\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"h-8 w-8 p-0\"\r\n              onClick={() => table.previousPage()}\r\n              disabled={!table.getCanPreviousPage()}\r\n            >\r\n              <span className=\"sr-only\">Go to previous page</span>\r\n              <ChevronLeft className=\"h-4 w-4\" />\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"h-8 w-8 p-0\"\r\n              onClick={() => table.nextPage()}\r\n              disabled={!table.getCanNextPage()}\r\n            >\r\n              <span className=\"sr-only\">Go to next page</span>\r\n              <ChevronRight className=\"h-4 w-4\" />\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n              onClick={() => table.setPageIndex(table.getPageCount() - 1)}\r\n              disabled={!table.getCanNextPage()}\r\n            >\r\n              <span className=\"sr-only\">Go to last page</span>\r\n              <ChevronsRight className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;AAWD;AAAA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAEA;AACA;AACA;AAOA;AACA;AAOA;AAQA;AAAA;AApDA;;;;;;;;;;;;AAgFO,SAAS,UAAa,EAC3B,IAAI,EACJ,OAAO,EACP,YAAY,EAAE,EACd,UAAU,EACV,oBAAoB,WAAW,EAC/B,YAAY,EACZ,qBAAqB,KAAK,EAC1B,yBAAyB,IAAI,EAC7B,qBAAqB,IAAI,EACzB,WAAW,EAAE,EACb,eAAe,mBAAmB,EAClC,oBAAoB,KAAK,EACzB,cAAc,EAAE,EAChB,iBAAiB,EAAE,EACnB,kBAAkB,EAAE,EACpB,eAAe,EAAE,EACC;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAgB,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EACrD,EAAE;IAEJ,MAAM,CAAC,kBAAkB,oBAAoB,GAC3C,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAmB,CAAC;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,CAAC;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEvD,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,iBAAiB,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;QAC/B,uBAAuB,CAAA,GAAA,qKAAA,CAAA,wBAAqB,AAAD;QAC3C,mBAAmB,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD;QACnC,qBAAqB,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD;QACvC,iBAAiB;QACjB,uBAAuB;QACvB,0BAA0B;QAC1B,sBAAsB;QACtB,sBAAsB;QACtB,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;QACA,cAAc;YACZ,YAAY;gBACV;YACF;QACF;IACF;IAEA,0CAA0C;IAC1C,MAAM,eAAe,CAAC;QACpB,IAAI,cAAc;YAChB,MAAM,SAAS,CAAC,eAAe,eAAe;QAChD,OAAO;YACL,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;4BAEZ,oCACC,8OAAC,iIAAA,CAAA,QAAK;gCACJ,aAAa;gCACb,OACE,eACK,AAAC,MACC,SAAS,CAAC,eACT,oBAA+B,KACnC;gCAEN,UAAU,CAAA,QAAS,aAAa,MAAM,MAAM,CAAC,KAAK;gCAClD,WAAU;;;;;;0CAId,8OAAC;gCAAI,WAAU;;oCAEZ,qBACC,sBACA,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM,GAAG,mBAChD,8OAAC,4IAAA,CAAA,eAAY;;0DACX,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;;wDAAK;wDACxB,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;wDAAC;sEAE1D,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAG3B,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAM;0DACxB,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,4IAAA,CAAA,mBAAgB;wDAEf,SAAS,IACP,OAAO,OAAO,CACZ,MACG,2BAA2B,GAC3B,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ;wDAGnC,WACE,OAAO,OAAO,KAAK,gBACf,qBACA;;4DAGL,OAAO,IAAI,kBACV,8OAAC,OAAO,IAAI;gEAAC,WAAU;;;;;;4DAExB,OAAO,KAAK;;uDAjBR;;;;;;;;;;;;;;;;oCAyBhB,wCACC,8OAAC,4IAAA,CAAA,eAAY;;0DACX,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;;sEACd,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;sEAErC,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAG3B,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAM;gDAAM,WAAU;0DACxC,MACE,aAAa,GACb,MAAM,CAAC,CAAA,SAAU,OAAO,UAAU,IAClC,GAAG,CAAC,CAAA;oDACH,qBACE,8OAAC,4IAAA,CAAA,2BAAwB;wDAEvB,WAAU;wDACV,SAAS,OAAO,YAAY;wDAC5B,iBAAiB,CAAA,QACf,OAAO,gBAAgB,CAAC,CAAC,CAAC;kEAG3B,OAAO,EAAE;uDAPL,OAAO,EAAE;;;;;gDAUpB;;;;;;;;;;;;;;;;;;;;;;;;kCAQZ,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,YAAY;sCAC7B,cAAA,8OAAC,iIAAA,CAAA,QAAK;;kDACJ,8OAAC,iIAAA,CAAA,cAAW;kDACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAA,4BAC3B,8OAAC,iIAAA,CAAA,WAAQ;gDAEP,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,6EACA;0DAGD,YAAY,OAAO,CAAC,GAAG,CAAC,CAAA;oDACvB,qBACE,8OAAC,iIAAA,CAAA,YAAS;wDAER,WAAU;kEAET,OAAO,aAAa,GAAG,qBACtB,8OAAC;4DACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,+BACA,OAAO,MAAM,CAAC,UAAU,MACtB;4DAEJ,SAAS,OAAO,MAAM,CAAC,uBAAuB;sEAE9C,cAAA,8OAAC;0EACE,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACR,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;;;;;;;;;;;uDAfpB,OAAO,EAAE;;;;;gDAsBpB;+CA/BK,YAAY,EAAE;;;;;;;;;;kDAmCzB,8OAAC,iIAAA,CAAA,YAAS;kDACP,MAAM,WAAW,GAAG,IAAI,EAAE,SACzB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA,oBAC3B,8OAAC,iIAAA,CAAA,WAAQ;gDAEP,cAAY,IAAI,aAAa,MAAM;gDACnC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,4FACA,cAAc,kBACd,IAAI,aAAa,MAAM,kCACvB;gDAEF,SAAS,IAAM,aAAa,IAAI,QAAQ;0DAEvC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAA,qBACzB,8OAAC,iIAAA,CAAA,YAAS;kEACP,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;uDAHH,KAAK,EAAE;;;;;+CAXpB,IAAI,EAAE;;;;sEAqBf,8OAAC,iIAAA,CAAA,WAAQ;sDACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;gDACR,SAAS,QAAQ,MAAM;gDACvB,WAAU;0DAET;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC;wBAAI,WAAU;kCACZ,sBACD,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM,GAAG,kBAChD;;gCACG,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;gCAAC;gCAAI;gCACpD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCAAC;;yDAG3C;;gCAAE;gCACQ;gCACP,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GACpC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,GACpC;gCAAG;gCAAI;gCACN;gCACF,KAAK,GAAG,CACP,CAAC,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,CAAC,IACxC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EACtC,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCACtC;gCAAI;gCACH,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCAAC;gCAC3C,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,MAAM,IACtD,CAAC,gBAAgB,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;;;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE;wCAChD,eAAe,CAAA;4CACb,MAAM,WAAW,CAAC,OAAO;wCAC3B;;0DAEA,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDACV,aAAa,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;;;;;;;;;;;0DAGrD,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,MAAK;0DACjB;oDAAC;oDAAI;oDAAI;oDAAI;oDAAI;iDAAG,CAAC,GAAG,CAAC,CAAA,yBACxB,8OAAC,kIAAA,CAAA,aAAU;wDAAgB,OAAO,GAAG,UAAU;kEAC5C;uDADc;;;;;;;;;;;;;;;;;;;;;;0CAOzB,8OAAC;gCAAI,WAAU;;oCAAiE;oCACxE,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG;oCAAE;oCAAI;oCACnD,MAAM,YAAY;;;;;;;0CAErB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,MAAM,YAAY,CAAC;wCAClC,UAAU,CAAC,MAAM,kBAAkB;;0DAEnC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,MAAM,YAAY;wCACjC,UAAU,CAAC,MAAM,kBAAkB;;0DAEnC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAEzB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,MAAM,QAAQ;wCAC7B,UAAU,CAAC,MAAM,cAAc;;0DAE/B,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,MAAM,YAAY,CAAC,MAAM,YAAY,KAAK;wCACzD,UAAU,CAAC,MAAM,cAAc;;0DAE/B,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}, {"offset": {"line": 3715, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/tables/columnHelpers.tsx"], "sourcesContent": ["/**\r\n * Column Helper Utilities for DataTable\r\n *\r\n * Provides reusable column definitions and helper functions for common\r\n * table column patterns, reducing duplication across different tables.\r\n */\r\n\r\nimport type { ColumnDef } from '@tanstack/react-table';\r\nimport { format } from 'date-fns';\r\nimport {\r\n  ArrowUpDown,\r\n  ArrowUp,\r\n  ArrowDown,\r\n  Edit,\r\n  Eye,\r\n  MoreHorizontal,\r\n  Trash,\r\n} from 'lucide-react';\r\nimport Link from 'next/link';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { cn } from '@/lib/utils';\r\n\r\n/**\r\n * Creates a sortable header component with proper sorting state indicators\r\n */\r\nexport const createSortableHeader = (title: string) => {\r\n  return ({ column }: { column: any }) => {\r\n    const sortDirection = column.getIsSorted();\r\n\r\n    return (\r\n      <Button\r\n        variant=\"ghost\"\r\n        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n        className=\"h-auto p-0 font-semibold hover:bg-transparent\"\r\n      >\r\n        {title}\r\n        {sortDirection === 'asc' ? (\r\n          <ArrowUp className=\"ml-2 h-4 w-4\" />\r\n        ) : sortDirection === 'desc' ? (\r\n          <ArrowDown className=\"ml-2 h-4 w-4\" />\r\n        ) : (\r\n          <ArrowUpDown className=\"ml-2 h-4 w-4 opacity-50\" />\r\n        )}\r\n      </Button>\r\n    );\r\n  };\r\n};\r\n\r\n/**\r\n * Creates a date column with consistent formatting\r\n */\r\nexport const createDateColumn = <T,>(\r\n  accessorKey: keyof T,\r\n  header: string,\r\n  dateFormat: string = 'MMM dd, yyyy'\r\n): ColumnDef<T> => ({\r\n  accessorKey: accessorKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ getValue }) => {\r\n    const date = getValue() as string | Date;\r\n    if (!date) return '-';\r\n\r\n    try {\r\n      const dateObj = typeof date === 'string' ? new Date(date) : date;\r\n      return format(dateObj, dateFormat);\r\n    } catch {\r\n      return '-';\r\n    }\r\n  },\r\n});\r\n\r\n/**\r\n * Creates a status badge column\r\n */\r\nexport const createStatusColumn = <T,>(\r\n  accessorKey: keyof T,\r\n  header: string = 'Status',\r\n  statusConfig?: Record<string, { variant: string; label?: string }>\r\n): ColumnDef<T> => ({\r\n  accessorKey: accessorKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ getValue }) => {\r\n    const status = getValue() as string;\r\n    if (!status) return '-';\r\n\r\n    const config = statusConfig?.[status] || { variant: 'secondary' };\r\n    const label = config.label || status;\r\n\r\n    return <Badge variant={config.variant as any}>{label}</Badge>;\r\n  },\r\n});\r\n\r\n/**\r\n * Creates an actions column with common CRUD operations\r\n */\r\nexport const createActionsColumn = <\r\n  T extends { id: number | string },\r\n>(options: {\r\n  onView?: (item: T) => void;\r\n  onEdit?: (item: T) => void;\r\n  onDelete?: (item: T) => void;\r\n  viewHref?: (item: T) => string;\r\n  editHref?: (item: T) => string;\r\n  customActions?: Array<{\r\n    label: string;\r\n    icon?: React.ComponentType<{ className?: string }>;\r\n    onClick: (item: T) => void;\r\n    variant?: 'default' | 'destructive';\r\n  }>;\r\n}): ColumnDef<T> => ({\r\n  id: 'actions',\r\n  header: 'Actions',\r\n  cell: ({ row }) => {\r\n    const item = row.original;\r\n\r\n    return (\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\r\n            <span className=\"sr-only\">Open menu</span>\r\n            <MoreHorizontal className=\"h-4 w-4\" />\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\">\r\n          {/* View Action */}\r\n          {(options.onView || options.viewHref) && (\r\n            <>\r\n              {options.viewHref ? (\r\n                <DropdownMenuItem asChild>\r\n                  <Link href={options.viewHref(item)}>\r\n                    <Eye className=\"mr-2 h-4 w-4\" />\r\n                    View\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n              ) : (\r\n                <DropdownMenuItem onClick={() => options.onView?.(item)}>\r\n                  <Eye className=\"mr-2 h-4 w-4\" />\r\n                  View\r\n                </DropdownMenuItem>\r\n              )}\r\n            </>\r\n          )}\r\n\r\n          {/* Edit Action */}\r\n          {(options.onEdit || options.editHref) && (\r\n            <>\r\n              {options.editHref ? (\r\n                <DropdownMenuItem asChild>\r\n                  <Link href={options.editHref(item)}>\r\n                    <Edit className=\"mr-2 h-4 w-4\" />\r\n                    Edit\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n              ) : (\r\n                <DropdownMenuItem onClick={() => options.onEdit?.(item)}>\r\n                  <Edit className=\"mr-2 h-4 w-4\" />\r\n                  Edit\r\n                </DropdownMenuItem>\r\n              )}\r\n            </>\r\n          )}\r\n\r\n          {/* Custom Actions */}\r\n          {options.customActions?.map((action, index) => (\r\n            <DropdownMenuItem\r\n              key={index}\r\n              onClick={() => action.onClick(item)}\r\n              className={\r\n                action.variant === 'destructive' ? 'text-destructive' : ''\r\n              }\r\n            >\r\n              {action.icon && <action.icon className=\"mr-2 h-4 w-4\" />}\r\n              {action.label}\r\n            </DropdownMenuItem>\r\n          ))}\r\n\r\n          {/* Delete Action */}\r\n          {options.onDelete && (\r\n            <>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                onClick={() => options.onDelete?.(item)}\r\n                className=\"text-destructive\"\r\n              >\r\n                <Trash className=\"mr-2 h-4 w-4\" />\r\n                Delete\r\n              </DropdownMenuItem>\r\n            </>\r\n          )}\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    );\r\n  },\r\n});\r\n\r\n/**\r\n * Creates a text column with optional truncation\r\n */\r\nexport const createTextColumn = <T,>(\r\n  accessorKey: keyof T,\r\n  header: string,\r\n  options?: {\r\n    maxLength?: number;\r\n    className?: string;\r\n  }\r\n): ColumnDef<T> => ({\r\n  accessorKey: accessorKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ getValue }) => {\r\n    const value = getValue() as string;\r\n    if (!value) return '-';\r\n\r\n    const truncated =\r\n      options?.maxLength && value.length > options.maxLength\r\n        ? `${value.substring(0, options.maxLength)}...`\r\n        : value;\r\n\r\n    return (\r\n      <span className={options?.className} title={value}>\r\n        {truncated}\r\n      </span>\r\n    );\r\n  },\r\n});\r\n\r\n/**\r\n * Creates a numeric column with optional formatting\r\n */\r\nexport const createNumericColumn = <T,>(\r\n  accessorKey: keyof T,\r\n  header: string,\r\n  options?: {\r\n    format?: 'currency' | 'percentage' | 'decimal';\r\n    decimals?: number;\r\n    prefix?: string;\r\n    suffix?: string;\r\n  }\r\n): ColumnDef<T> => ({\r\n  accessorKey: accessorKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ getValue }) => {\r\n    const rawValue = getValue();\r\n    const value =\r\n      typeof rawValue === 'string'\r\n        ? parseFloat(rawValue)\r\n        : (rawValue as number);\r\n\r\n    if (value === null || value === undefined || isNaN(value)) return '-';\r\n\r\n    let formatted = value.toString();\r\n\r\n    if (options?.format === 'currency') {\r\n      formatted = new Intl.NumberFormat('en-US', {\r\n        style: 'currency',\r\n        currency: 'USD',\r\n        minimumFractionDigits: options.decimals ?? 2,\r\n      }).format(value);\r\n    } else if (options?.format === 'percentage') {\r\n      formatted = `${(value * 100).toFixed(options.decimals ?? 1)}%`;\r\n    } else if (options?.decimals !== undefined) {\r\n      formatted = value.toFixed(options.decimals);\r\n    }\r\n\r\n    if (options?.prefix) formatted = options.prefix + formatted;\r\n    if (options?.suffix) formatted = formatted + options.suffix;\r\n\r\n    return <span className=\"font-mono\">{formatted}</span>;\r\n  },\r\n});\r\n\r\n/**\r\n * Creates a boolean column with checkmark/cross display\r\n */\r\nexport const createBooleanColumn = <T,>(\r\n  accessorKey: keyof T,\r\n  header: string,\r\n  labels?: { true: string; false: string }\r\n): ColumnDef<T> => ({\r\n  accessorKey: accessorKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ getValue }) => {\r\n    const value = getValue() as boolean;\r\n\r\n    if (labels) {\r\n      return (\r\n        <Badge variant={value ? 'default' : 'secondary'}>\r\n          {value ? labels.true : labels.false}\r\n        </Badge>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <span className={value ? 'text-green-600' : 'text-gray-400'}>\r\n        {value ? '✓' : '✗'}\r\n      </span>\r\n    );\r\n  },\r\n});\r\n\r\n/**\r\n * Creates a row selection column (checkbox)\r\n */\r\nexport const createSelectionColumn = <T,>(): ColumnDef<T> => ({\r\n  id: 'select',\r\n  header: ({ table }) => (\r\n    <Checkbox\r\n      checked={\r\n        table.getIsAllPageRowsSelected() ||\r\n        (table.getIsSomePageRowsSelected() && 'indeterminate')\r\n      }\r\n      onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}\r\n      aria-label=\"Select all\"\r\n    />\r\n  ),\r\n  cell: ({ row }) => (\r\n    <Checkbox\r\n      checked={row.getIsSelected()}\r\n      onCheckedChange={value => row.toggleSelected(!!value)}\r\n      aria-label=\"Select row\"\r\n    />\r\n  ),\r\n  enableSorting: false,\r\n  enableHiding: false,\r\n});\r\n\r\n/**\r\n * Creates a complex column with title and subtitle (like DelegationTable eventName)\r\n */\r\nexport const createTitleSubtitleColumn = <T,>(\r\n  titleKey: keyof T,\r\n  subtitleKey: keyof T,\r\n  header: string\r\n): ColumnDef<T> => ({\r\n  accessorKey: titleKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ row }) => {\r\n    const title = row.getValue(titleKey as string) as string;\r\n    const subtitle = (row.getValue(subtitleKey as string) || '') as string;\r\n\r\n    return (\r\n      <div className=\"space-y-1\">\r\n        <div className=\"font-semibold text-foreground\">{title || '-'}</div>\r\n        {subtitle && (\r\n          <div className=\"line-clamp-1 text-xs text-muted-foreground\">\r\n            {subtitle}\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  },\r\n});\r\n\r\n/**\r\n * Creates a column with icon and text (like DelegationTable delegates count)\r\n */\r\nexport const createIconTextColumn = <T,>(\r\n  accessorKey: keyof T,\r\n  header: string,\r\n  icon: React.ComponentType<{ className?: string }>,\r\n  options?: {\r\n    formatter?: (value: any) => string;\r\n    className?: string;\r\n  }\r\n): ColumnDef<T> => ({\r\n  accessorKey: accessorKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ row }) => {\r\n    const value = row.getValue(accessorKey as string);\r\n    const Icon = icon;\r\n    const displayValue = options?.formatter ? options.formatter(value) : value;\r\n\r\n    return (\r\n      <div\r\n        className={cn(\r\n          'flex items-center justify-center gap-1 text-sm',\r\n          options?.className\r\n        )}\r\n      >\r\n        <Icon className=\"size-3 text-muted-foreground\" />\r\n        {String(displayValue)}\r\n      </div>\r\n    );\r\n  },\r\n});\r\n\r\n/**\r\n * Enhanced actions column with professional styling (like DelegationTable)\r\n */\r\nexport const createEnhancedActionsColumn = <\r\n  T extends { id: number | string },\r\n>(options: {\r\n  onView?: (item: T) => void;\r\n  onEdit?: (item: T) => void;\r\n  onDelete?: (item: T) => void;\r\n  viewHref?: (item: T) => string;\r\n  editHref?: (item: T) => string;\r\n  customActions?: Array<{\r\n    label: string;\r\n    icon?: React.ComponentType<{ className?: string }>;\r\n    onClick: (item: T) => void;\r\n    variant?: 'default' | 'destructive';\r\n  }>;\r\n  showCopyId?: boolean;\r\n}): ColumnDef<T> => ({\r\n  id: 'actions',\r\n  header: 'Actions',\r\n  cell: ({ row }) => {\r\n    const item = row.original;\r\n\r\n    return (\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant=\"ghost\" className=\"size-8 p-0\">\r\n            <span className=\"sr-only\">Open menu</span>\r\n            <MoreHorizontal className=\"size-4\" />\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\">\r\n          <DropdownMenuLabel>Actions</DropdownMenuLabel>\r\n\r\n          {/* View Action */}\r\n          {(options.onView || options.viewHref) && (\r\n            <>\r\n              {options.viewHref ? (\r\n                <DropdownMenuItem asChild>\r\n                  <Link href={options.viewHref(item)}>\r\n                    <Eye className=\"mr-2 size-4\" />\r\n                    View Details\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n              ) : (\r\n                <DropdownMenuItem onClick={() => options.onView?.(item)}>\r\n                  <Eye className=\"mr-2 size-4\" />\r\n                  View Details\r\n                </DropdownMenuItem>\r\n              )}\r\n            </>\r\n          )}\r\n\r\n          {/* Edit Action */}\r\n          {(options.onEdit || options.editHref) && (\r\n            <>\r\n              {options.editHref ? (\r\n                <DropdownMenuItem asChild>\r\n                  <Link href={options.editHref(item)}>\r\n                    <Edit className=\"mr-2 size-4\" />\r\n                    Edit\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n              ) : (\r\n                <DropdownMenuItem onClick={() => options.onEdit?.(item)}>\r\n                  <Edit className=\"mr-2 size-4\" />\r\n                  Edit\r\n                </DropdownMenuItem>\r\n              )}\r\n            </>\r\n          )}\r\n\r\n          {/* Custom Actions */}\r\n          {options.customActions?.map((action, index) => (\r\n            <DropdownMenuItem\r\n              key={index}\r\n              onClick={() => action.onClick(item)}\r\n              className={\r\n                action.variant === 'destructive' ? 'text-destructive' : ''\r\n              }\r\n            >\r\n              {action.icon && <action.icon className=\"mr-2 size-4\" />}\r\n              {action.label}\r\n            </DropdownMenuItem>\r\n          ))}\r\n\r\n          {/* Copy ID */}\r\n          {options.showCopyId && (\r\n            <DropdownMenuItem\r\n              onClick={() => navigator.clipboard.writeText(String(item.id))}\r\n            >\r\n              Copy ID\r\n            </DropdownMenuItem>\r\n          )}\r\n\r\n          {/* Delete Action */}\r\n          {options.onDelete && (\r\n            <>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                onClick={() => options.onDelete?.(item)}\r\n                className=\"text-destructive\"\r\n              >\r\n                <Trash className=\"mr-2 size-4\" />\r\n                Delete\r\n              </DropdownMenuItem>\r\n            </>\r\n          )}\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    );\r\n  },\r\n});\r\n\r\n/**\r\n * Status configuration presets for common entities\r\n */\r\nexport const statusConfigs = {\r\n  delegation: {\r\n    Planned: { variant: 'secondary', label: 'Planned' },\r\n    'In Progress': { variant: 'default', label: 'In Progress' },\r\n    Completed: { variant: 'success', label: 'Completed' },\r\n    Cancelled: { variant: 'destructive', label: 'Cancelled' },\r\n  },\r\n  employee: {\r\n    Active: { variant: 'success', label: 'Active' },\r\n    Inactive: { variant: 'secondary', label: 'Inactive' },\r\n    'On Leave': { variant: 'warning', label: 'On Leave' },\r\n  },\r\n  task: {\r\n    Pending: { variant: 'secondary', label: 'Pending' },\r\n    In_Progress: { variant: 'default', label: 'In Progress' }, // Changed from 'In Progress'\r\n    Completed: { variant: 'success', label: 'Completed' },\r\n    Overdue: { variant: 'destructive', label: 'Overdue' },\r\n    Cancelled: { variant: 'destructive', label: 'Cancelled' },\r\n    Assigned: { variant: 'default', label: 'Assigned' },\r\n  },\r\n} as const;\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;;;;;AAGD;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAEA;AACA;AACA;AACA;AAQA;AAAA;;;;;;;;;;AAKO,MAAM,uBAAuB,CAAC;IACnC,OAAO,CAAC,EAAE,MAAM,EAAmB;QACjC,MAAM,gBAAgB,OAAO,WAAW;QAExC,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAQ;YACR,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;YAC7D,WAAU;;gBAET;gBACA,kBAAkB,sBACjB,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;2BACjB,kBAAkB,uBACpB,8OAAC,gNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;yCAErB,8OAAC,wNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;IAI/B;AACF;AAKO,MAAM,mBAAmB,CAC9B,aACA,QACA,aAAqB,cAAc,GAClB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,QAAQ,EAAE;YACjB,MAAM,OAAO;YACb,IAAI,CAAC,MAAM,OAAO;YAElB,IAAI;gBACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;gBAC5D,OAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;YACzB,EAAE,OAAM;gBACN,OAAO;YACT;QACF;IACF,CAAC;AAKM,MAAM,qBAAqB,CAChC,aACA,SAAiB,QAAQ,EACzB,eACiB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,QAAQ,EAAE;YACjB,MAAM,SAAS;YACf,IAAI,CAAC,QAAQ,OAAO;YAEpB,MAAM,SAAS,cAAc,CAAC,OAAO,IAAI;gBAAE,SAAS;YAAY;YAChE,MAAM,QAAQ,OAAO,KAAK,IAAI;YAE9B,qBAAO,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,OAAO,OAAO;0BAAU;;;;;;QACjD;IACF,CAAC;AAKM,MAAM,sBAAsB,CAEjC,UAYkB,CAAC;QACnB,IAAI;QACJ,QAAQ;QACR,MAAM,CAAC,EAAE,GAAG,EAAE;YACZ,MAAM,OAAO,IAAI,QAAQ;YAEzB,qBACE,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,WAAU;;8CAChC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,gNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAM;;4BAExB,CAAC,QAAQ,MAAM,IAAI,QAAQ,QAAQ,mBAClC;0CACG,QAAQ,QAAQ,iBACf,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,QAAQ,QAAQ,CAAC;;0DAC3B,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;yDAKpC,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,QAAQ,MAAM,GAAG;;sDAChD,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;4BAQvC,CAAC,QAAQ,MAAM,IAAI,QAAQ,QAAQ,mBAClC;0CACG,QAAQ,QAAQ,iBACf,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,QAAQ,QAAQ,CAAC;;0DAC3B,8OAAC,2MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;yDAKrC,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,QAAQ,MAAM,GAAG;;sDAChD,8OAAC,2MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;4BAQxC,QAAQ,aAAa,EAAE,IAAI,CAAC,QAAQ,sBACnC,8OAAC,4IAAA,CAAA,mBAAgB;oCAEf,SAAS,IAAM,OAAO,OAAO,CAAC;oCAC9B,WACE,OAAO,OAAO,KAAK,gBAAgB,qBAAqB;;wCAGzD,OAAO,IAAI,kBAAI,8OAAC,OAAO,IAAI;4CAAC,WAAU;;;;;;wCACtC,OAAO,KAAK;;mCAPR;;;;;4BAYR,QAAQ,QAAQ,kBACf;;kDACE,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS,IAAM,QAAQ,QAAQ,GAAG;wCAClC,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;QAQhD;IACF,CAAC;AAKM,MAAM,mBAAmB,CAC9B,aACA,QACA,UAIiB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,QAAQ,EAAE;YACjB,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO,OAAO;YAEnB,MAAM,YACJ,SAAS,aAAa,MAAM,MAAM,GAAG,QAAQ,SAAS,GAClD,GAAG,MAAM,SAAS,CAAC,GAAG,QAAQ,SAAS,EAAE,GAAG,CAAC,GAC7C;YAEN,qBACE,8OAAC;gBAAK,WAAW,SAAS;gBAAW,OAAO;0BACzC;;;;;;QAGP;IACF,CAAC;AAKM,MAAM,sBAAsB,CACjC,aACA,QACA,UAMiB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,QAAQ,EAAE;YACjB,MAAM,WAAW;YACjB,MAAM,QACJ,OAAO,aAAa,WAChB,WAAW,YACV;YAEP,IAAI,UAAU,QAAQ,UAAU,aAAa,MAAM,QAAQ,OAAO;YAElE,IAAI,YAAY,MAAM,QAAQ;YAE9B,IAAI,SAAS,WAAW,YAAY;gBAClC,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;oBACzC,OAAO;oBACP,UAAU;oBACV,uBAAuB,QAAQ,QAAQ,IAAI;gBAC7C,GAAG,MAAM,CAAC;YACZ,OAAO,IAAI,SAAS,WAAW,cAAc;gBAC3C,YAAY,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,QAAQ,QAAQ,IAAI,GAAG,CAAC,CAAC;YAChE,OAAO,IAAI,SAAS,aAAa,WAAW;gBAC1C,YAAY,MAAM,OAAO,CAAC,QAAQ,QAAQ;YAC5C;YAEA,IAAI,SAAS,QAAQ,YAAY,QAAQ,MAAM,GAAG;YAClD,IAAI,SAAS,QAAQ,YAAY,YAAY,QAAQ,MAAM;YAE3D,qBAAO,8OAAC;gBAAK,WAAU;0BAAa;;;;;;QACtC;IACF,CAAC;AAKM,MAAM,sBAAsB,CACjC,aACA,QACA,SACiB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,QAAQ,EAAE;YACjB,MAAM,QAAQ;YAEd,IAAI,QAAQ;gBACV,qBACE,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAS,QAAQ,YAAY;8BACjC,QAAQ,OAAO,IAAI,GAAG,OAAO,KAAK;;;;;;YAGzC;YAEA,qBACE,8OAAC;gBAAK,WAAW,QAAQ,mBAAmB;0BACzC,QAAQ,MAAM;;;;;;QAGrB;IACF,CAAC;AAKM,MAAM,wBAAwB,IAAwB,CAAC;QAC5D,IAAI;QACJ,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,SACE,MAAM,wBAAwB,MAC7B,MAAM,yBAAyB,MAAM;gBAExC,iBAAiB,CAAA,QAAS,MAAM,yBAAyB,CAAC,CAAC,CAAC;gBAC5D,cAAW;;;;;;QAGf,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC,oIAAA,CAAA,WAAQ;gBACP,SAAS,IAAI,aAAa;gBAC1B,iBAAiB,CAAA,QAAS,IAAI,cAAc,CAAC,CAAC,CAAC;gBAC/C,cAAW;;;;;;QAGf,eAAe;QACf,cAAc;IAChB,CAAC;AAKM,MAAM,4BAA4B,CACvC,UACA,aACA,SACiB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,GAAG,EAAE;YACZ,MAAM,QAAQ,IAAI,QAAQ,CAAC;YAC3B,MAAM,WAAY,IAAI,QAAQ,CAAC,gBAA0B;YAEzD,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAiC,SAAS;;;;;;oBACxD,0BACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;QAKX;IACF,CAAC;AAKM,MAAM,uBAAuB,CAClC,aACA,QACA,MACA,UAIiB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,GAAG,EAAE;YACZ,MAAM,QAAQ,IAAI,QAAQ,CAAC;YAC3B,MAAM,OAAO;YACb,MAAM,eAAe,SAAS,YAAY,QAAQ,SAAS,CAAC,SAAS;YAErE,qBACE,8OAAC;gBACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,kDACA,SAAS;;kCAGX,8OAAC;wBAAK,WAAU;;;;;;oBACf,OAAO;;;;;;;QAGd;IACF,CAAC;AAKM,MAAM,8BAA8B,CAEzC,UAakB,CAAC;QACnB,IAAI;QACJ,QAAQ;QACR,MAAM,CAAC,EAAE,GAAG,EAAE;YACZ,MAAM,OAAO,IAAI,QAAQ;YAEzB,qBACE,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,WAAU;;8CAChC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,gNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAM;;0CACzB,8OAAC,4IAAA,CAAA,oBAAiB;0CAAC;;;;;;4BAGlB,CAAC,QAAQ,MAAM,IAAI,QAAQ,QAAQ,mBAClC;0CACG,QAAQ,QAAQ,iBACf,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,QAAQ,QAAQ,CAAC;;0DAC3B,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;;;;;yDAKnC,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,QAAQ,MAAM,GAAG;;sDAChD,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAgB;;;;;;;;4BAQtC,CAAC,QAAQ,MAAM,IAAI,QAAQ,QAAQ,mBAClC;0CACG,QAAQ,QAAQ,iBACf,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,QAAQ,QAAQ,CAAC;;0DAC3B,8OAAC,2MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;;;;;yDAKpC,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,QAAQ,MAAM,GAAG;;sDAChD,8OAAC,2MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAgB;;;;;;;;4BAQvC,QAAQ,aAAa,EAAE,IAAI,CAAC,QAAQ,sBACnC,8OAAC,4IAAA,CAAA,mBAAgB;oCAEf,SAAS,IAAM,OAAO,OAAO,CAAC;oCAC9B,WACE,OAAO,OAAO,KAAK,gBAAgB,qBAAqB;;wCAGzD,OAAO,IAAI,kBAAI,8OAAC,OAAO,IAAI;4CAAC,WAAU;;;;;;wCACtC,OAAO,KAAK;;mCAPR;;;;;4BAYR,QAAQ,UAAU,kBACjB,8OAAC,4IAAA,CAAA,mBAAgB;gCACf,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,KAAK,EAAE;0CAC5D;;;;;;4BAMF,QAAQ,QAAQ,kBACf;;kDACE,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS,IAAM,QAAQ,QAAQ,GAAG;wCAClC,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;;;;;;;;;;;;;;;QAQ/C;IACF,CAAC;AAKM,MAAM,gBAAgB;IAC3B,YAAY;QACV,SAAS;YAAE,SAAS;YAAa,OAAO;QAAU;QAClD,eAAe;YAAE,SAAS;YAAW,OAAO;QAAc;QAC1D,WAAW;YAAE,SAAS;YAAW,OAAO;QAAY;QACpD,WAAW;YAAE,SAAS;YAAe,OAAO;QAAY;IAC1D;IACA,UAAU;QACR,QAAQ;YAAE,SAAS;YAAW,OAAO;QAAS;QAC9C,UAAU;YAAE,SAAS;YAAa,OAAO;QAAW;QACpD,YAAY;YAAE,SAAS;YAAW,OAAO;QAAW;IACtD;IACA,MAAM;QACJ,SAAS;YAAE,SAAS;YAAa,OAAO;QAAU;QAClD,aAAa;YAAE,SAAS;YAAW,OAAO;QAAc;QACxD,WAAW;YAAE,SAAS;YAAW,OAAO;QAAY;QACpD,SAAS;YAAE,SAAS;YAAe,OAAO;QAAU;QACpD,WAAW;YAAE,SAAS;YAAe,OAAO;QAAY;QACxD,UAAU;YAAE,SAAS;YAAW,OAAO;QAAW;IACpD;AACF", "debugId": null}}, {"offset": {"line": 4436, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/tables/index.ts"], "sourcesContent": ["/**\r\n * Table Components Index\r\n *\r\n * Centralized exports for all table-related components, providing a clean\r\n * interface for importing table functionality throughout the application.\r\n */\r\n\r\n// Export main table components\r\nexport { DataTable } from './DataTable';\r\nexport type { DataTableProps } from './DataTable';\r\n\r\n// Export column helpers\r\nexport {\r\n  createSortableHeader,\r\n  createDateColumn,\r\n  createStatusColumn,\r\n  createActionsColumn,\r\n  createTextColumn,\r\n  createNumericColumn,\r\n  createBooleanColumn,\r\n  createSelectionColumn,\r\n  createTitleSubtitleColumn,\r\n  createIconTextColumn,\r\n  createEnhancedActionsColumn,\r\n  statusConfigs,\r\n} from './columnHelpers';\r\n\r\n// Re-export TanStack Table types for convenience\r\nexport type {\r\n  ColumnDef,\r\n  ColumnFiltersState,\r\n  SortingState,\r\n  VisibilityState,\r\n  Row,\r\n  Table as TanStackTable,\r\n} from '@tanstack/react-table';\r\n\r\n// Re-export shadcn/ui table components for direct use\r\nexport {\r\n  Table,\r\n  TableBody,\r\n  TableCaption,\r\n  TableCell,\r\n  TableFooter,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@/components/ui/table';\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,+BAA+B;;AAC/B;AAGA,wBAAwB;AACxB;AAyBA,sDAAsD;AACtD", "debugId": null}}, {"offset": {"line": 4468, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/service-history/ServiceHistoryTable.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ColumnDef } from '@tanstack/react-table';\r\nimport { Trash } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport type { EnrichedServiceRecord } from '@/lib/types/domain';\r\n\r\nimport {\r\n  DataTable,\r\n  createSelectionColumn,\r\n  createTextColumn,\r\n  createDateColumn,\r\n  createNumericColumn,\r\n  createEnhancedActionsColumn,\r\n  createSortableHeader,\r\n} from '@/components/ui/tables';\r\n\r\n/**\r\n * Props for the ServiceHistoryTable component\r\n */\r\ninterface ServiceHistoryTableProps {\r\n  /** Additional CSS class names */\r\n  className?: string;\r\n  /** Array of service records to display */\r\n  records: EnrichedServiceRecord[];\r\n  /** Whether to show vehicle information column */\r\n  showVehicleInfo?: boolean;\r\n  /** Callback for record deletion */\r\n  onDelete?: (record: EnrichedServiceRecord) => Promise<void>;\r\n  /** Callback for bulk record deletion */\r\n  onBulkDelete?: (records: EnrichedServiceRecord[]) => Promise<void>;\r\n}\r\n\r\n/**\r\n * A component that displays service history records using the standardized DataTable component\r\n *\r\n * @example\r\n * ```tsx\r\n * <ServiceHistoryTable\r\n *   records={records}\r\n *   showVehicleInfo={true}\r\n *   onDelete={handleDelete}\r\n * />\r\n * ```\r\n */\r\nexport function ServiceHistoryTable({\r\n  className = '',\r\n  records,\r\n  showVehicleInfo = true,\r\n  onDelete,\r\n  onBulkDelete,\r\n}: ServiceHistoryTableProps) {\r\n  // Define columns using standardized helpers\r\n  const columns: ColumnDef<EnrichedServiceRecord>[] = [\r\n    // Row selection\r\n    createSelectionColumn<EnrichedServiceRecord>(),\r\n\r\n    // Date column\r\n    createDateColumn('date', 'Date', 'MMM dd, yyyy'),\r\n\r\n    // Vehicle info column (conditional)\r\n    ...(showVehicleInfo\r\n      ? [\r\n          {\r\n            accessorKey: 'vehicleMake',\r\n            header: createSortableHeader('Vehicle'),\r\n            cell: ({ row }: { row: any }) => {\r\n              const record = row.original;\r\n              return (\r\n                <div className=\"space-y-1\">\r\n                  <div className=\"font-medium text-sm\">\r\n                    {record.vehicleMake} {record.vehicleModel} (\r\n                    {record.vehicleYear})\r\n                  </div>\r\n                  {record.licensePlate && (\r\n                    <div className=\"text-xs text-muted-foreground font-mono\">\r\n                      {record.licensePlate}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              );\r\n            },\r\n          },\r\n        ]\r\n      : []),\r\n\r\n    // Service performed\r\n    {\r\n      accessorKey: 'servicePerformed',\r\n      header: createSortableHeader('Service(s)'),\r\n      cell: ({ row }) => {\r\n        const services = row.getValue('servicePerformed') as string[];\r\n        const servicesText = services.join(', ');\r\n        return (\r\n          <div className=\"max-w-xs truncate\" title={servicesText}>\r\n            {servicesText}\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n\r\n    // Odometer reading\r\n    {\r\n      accessorKey: 'odometer',\r\n      header: createSortableHeader('Odometer'),\r\n      cell: ({ row }) => {\r\n        const odometer = row.getValue('odometer') as number;\r\n        return odometer ? odometer.toLocaleString() : '-';\r\n      },\r\n    },\r\n\r\n    // Cost\r\n    createNumericColumn('cost', 'Cost', {\r\n      decimals: 2,\r\n      prefix: '$',\r\n    }),\r\n\r\n    // Notes\r\n    createTextColumn('notes', 'Notes', {\r\n      maxLength: 50,\r\n      className: 'max-w-xs',\r\n    }),\r\n\r\n    // Actions\r\n    createEnhancedActionsColumn({\r\n      viewHref: record => `/service-records/${record.id}`,\r\n      editHref: record => `/service-records/${record.id}/edit`,\r\n      ...(onDelete && {\r\n        onDelete: (record: EnrichedServiceRecord) => {\r\n          onDelete(record);\r\n        },\r\n      }),\r\n      showCopyId: true,\r\n      customActions: [\r\n        {\r\n          label: 'View Vehicle',\r\n          onClick: record => {\r\n            window.location.href = `/vehicles/${record.vehicleId}`;\r\n          },\r\n        },\r\n      ],\r\n    }),\r\n  ];\r\n\r\n  // Bulk actions for selected rows\r\n  const bulkActions = [\r\n    ...(onBulkDelete\r\n      ? [\r\n          {\r\n            label: 'Delete Selected',\r\n            icon: ({ className }: { className?: string }) => (\r\n              <Trash className={className} />\r\n            ),\r\n            onClick: async (selectedRecords: EnrichedServiceRecord[]) => {\r\n              await onBulkDelete(selectedRecords);\r\n            },\r\n            variant: 'destructive' as const,\r\n          },\r\n        ]\r\n      : []),\r\n  ];\r\n\r\n  return (\r\n    <DataTable\r\n      data={records}\r\n      columns={columns}\r\n      className={className}\r\n      searchPlaceholder=\"Search service records by service type or notes...\"\r\n      searchColumn=\"servicePerformed\"\r\n      emptyMessage=\"No service records found. Add your first service record to get started.\"\r\n      pageSize={15}\r\n      // Advanced features\r\n      enableRowSelection={true}\r\n      enableBulkActions={bulkActions.length > 0}\r\n      bulkActions={bulkActions}\r\n      enableColumnVisibility={true}\r\n      // Professional styling with service theme\r\n      tableClassName=\"shadow-lg\"\r\n      headerClassName=\"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20\"\r\n      rowClassName=\"hover:bg-green-50/50 dark:hover:bg-green-900/10\"\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAKA;AAAA;AAAA;AARA;;;;AA8CO,SAAS,oBAAoB,EAClC,YAAY,EAAE,EACd,OAAO,EACP,kBAAkB,IAAI,EACtB,QAAQ,EACR,YAAY,EACa;IACzB,4CAA4C;IAC5C,MAAM,UAA8C;QAClD,gBAAgB;QAChB,CAAA,GAAA,mJAAA,CAAA,wBAAqB,AAAD;QAEpB,cAAc;QACd,CAAA,GAAA,mJAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,QAAQ;QAEjC,oCAAoC;WAChC,kBACA;YACE;gBACE,aAAa;gBACb,QAAQ,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EAAE;gBAC7B,MAAM,CAAC,EAAE,GAAG,EAAgB;oBAC1B,MAAM,SAAS,IAAI,QAAQ;oBAC3B,qBACE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,WAAW;oCAAC;oCAAE,OAAO,YAAY;oCAAC;oCACzC,OAAO,WAAW;oCAAC;;;;;;;4BAErB,OAAO,YAAY,kBAClB,8OAAC;gCAAI,WAAU;0CACZ,OAAO,YAAY;;;;;;;;;;;;gBAK9B;YACF;SACD,GACD,EAAE;QAEN,oBAAoB;QACpB;YACE,aAAa;YACb,QAAQ,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EAAE;YAC7B,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,WAAW,IAAI,QAAQ,CAAC;gBAC9B,MAAM,eAAe,SAAS,IAAI,CAAC;gBACnC,qBACE,8OAAC;oBAAI,WAAU;oBAAoB,OAAO;8BACvC;;;;;;YAGP;QACF;QAEA,mBAAmB;QACnB;YACE,aAAa;YACb,QAAQ,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EAAE;YAC7B,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,WAAW,IAAI,QAAQ,CAAC;gBAC9B,OAAO,WAAW,SAAS,cAAc,KAAK;YAChD;QACF;QAEA,OAAO;QACP,CAAA,GAAA,mJAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,QAAQ;YAClC,UAAU;YACV,QAAQ;QACV;QAEA,QAAQ;QACR,CAAA,GAAA,mJAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,SAAS;YACjC,WAAW;YACX,WAAW;QACb;QAEA,UAAU;QACV,CAAA,GAAA,mJAAA,CAAA,8BAA2B,AAAD,EAAE;YAC1B,UAAU,CAAA,SAAU,CAAC,iBAAiB,EAAE,OAAO,EAAE,EAAE;YACnD,UAAU,CAAA,SAAU,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC;YACxD,GAAI,YAAY;gBACd,UAAU,CAAC;oBACT,SAAS;gBACX;YACF,CAAC;YACD,YAAY;YACZ,eAAe;gBACb;oBACE,OAAO;oBACP,SAAS,CAAA;wBACP,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,OAAO,SAAS,EAAE;oBACxD;gBACF;aACD;QACH;KACD;IAED,iCAAiC;IACjC,MAAM,cAAc;WACd,eACA;YACE;gBACE,OAAO;gBACP,MAAM,CAAC,EAAE,SAAS,EAA0B,iBAC1C,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAW;;;;;;gBAEpB,SAAS,OAAO;oBACd,MAAM,aAAa;gBACrB;gBACA,SAAS;YACX;SACD,GACD,EAAE;KACP;IAED,qBACE,8OAAC,+IAAA,CAAA,YAAS;QACR,MAAM;QACN,SAAS;QACT,WAAW;QACX,mBAAkB;QAClB,cAAa;QACb,cAAa;QACb,UAAU;QACV,oBAAoB;QACpB,oBAAoB;QACpB,mBAAmB,YAAY,MAAM,GAAG;QACxC,aAAa;QACb,wBAAwB;QACxB,0CAA0C;QAC1C,gBAAe;QACf,iBAAgB;QAChB,cAAa;;;;;;AAGnB", "debugId": null}}, {"offset": {"line": 4634, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/service-history/EnhancedServiceHistoryContainer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { History, PlusCircle, RefreshCw } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport type { EnrichedServiceRecord } from '@/lib/types/domain';\r\n\r\nimport { ServiceHistorySummary } from '@/components/reports/ServiceHistorySummary';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { ErrorHandler } from '@/components/ui/error-handler';\r\nimport { EmptyState, SkeletonLoader } from '@/components/ui/loading';\r\nimport { useToast } from '@/hooks/utils/use-toast';\r\nimport { useDeleteServiceRecord } from '@/lib/stores/queries/useServiceRecords';\r\n\r\nimport { ServiceHistoryTable } from './ServiceHistoryTable';\r\n\r\n/**\r\n * Props for the EnhancedServiceHistoryContainer component\r\n */\r\ninterface EnhancedServiceHistoryContainerProps {\r\n  /** Additional CSS class names */\r\n  className?: string;\r\n  /** Error message, if any */\r\n  error: null | string;\r\n  /** Whether data is currently loading */\r\n  isLoading: boolean;\r\n  /** Function to retry loading data */\r\n  onRetry: () => void;\r\n  /** Array of service records to display */\r\n  records: EnrichedServiceRecord[];\r\n  /** Whether to show vehicle information column */\r\n  showVehicleInfo?: boolean;\r\n  /** Whether this is a vehicle-specific view */\r\n  vehicleSpecific?: boolean;\r\n}\r\n\r\n/**\r\n * A container component that handles sorting, pagination, and display of service history records\r\n *\r\n * @example\r\n * ```tsx\r\n * <EnhancedServiceHistoryContainer\r\n *   records={filteredRecords}\r\n *   isLoading={isLoading}\r\n *   error={error}\r\n *   onRetry={handleRetry}\r\n *   showVehicleInfo={true}\r\n *   vehicleSpecific={false}\r\n * />\r\n * ```\r\n */\r\nexport function EnhancedServiceHistoryContainer({\r\n  className,\r\n  error,\r\n  isLoading,\r\n  onRetry,\r\n  records,\r\n  showVehicleInfo = true,\r\n  vehicleSpecific = false,\r\n}: EnhancedServiceHistoryContainerProps) {\r\n  const { toast } = useToast();\r\n  const deleteMutation = useDeleteServiceRecord();\r\n\r\n  // Handle single record deletion\r\n  const handleDelete = async (record: EnrichedServiceRecord) => {\r\n    try {\r\n      // Pass the required object with id and vehicleId\r\n      await deleteMutation.mutateAsync({\r\n        id: record.id,\r\n        vehicleId: record.vehicleId,\r\n      });\r\n\r\n      toast({\r\n        title: 'Deleted!',\r\n        description: 'Service record deleted successfully.',\r\n        variant: 'default',\r\n      });\r\n\r\n      // Trigger a refetch of the data\r\n      onRetry();\r\n    } catch (error) {\r\n      toast({\r\n        title: 'Error',\r\n        description: 'Failed to delete service record. Please try again.',\r\n        variant: 'destructive',\r\n      });\r\n    }\r\n  };\r\n\r\n  // Handle bulk deletion\r\n  const handleBulkDelete = async (selectedRecords: EnrichedServiceRecord[]) => {\r\n    try {\r\n      // Delete all selected records\r\n      await Promise.all(\r\n        selectedRecords.map(record =>\r\n          // Pass the required object with id and vehicleId for each record\r\n          deleteMutation.mutateAsync({\r\n            id: record.id,\r\n            vehicleId: record.vehicleId,\r\n          })\r\n        )\r\n      );\r\n\r\n      toast({\r\n        title: 'Deleted!',\r\n        description: `${selectedRecords.length} service records deleted successfully.`,\r\n        variant: 'default',\r\n      });\r\n\r\n      // Trigger a refetch of the data\r\n      onRetry();\r\n    } catch (error) {\r\n      toast({\r\n        title: 'Error',\r\n        description: 'Failed to delete some service records. Please try again.',\r\n        variant: 'destructive',\r\n      });\r\n    }\r\n  };\r\n\r\n  // Since DataTable handles sorting and pagination internally,\r\n  // we only need to manage the data display logic here\r\n\r\n  // Render loading state\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"space-y-4\" data-testid=\"loading-skeleton\">\r\n        <SkeletonLoader count={5} variant=\"table\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render error state\r\n  if (error) {\r\n    return (\r\n      <div className=\"space-y-4\">\r\n        <ErrorHandler context=\"Loading Service Records\" error={error} />\r\n        <div className=\"flex justify-center\">\r\n          <Button\r\n            aria-label=\"Try loading service records again\"\r\n            onClick={onRetry}\r\n            size=\"sm\"\r\n            variant=\"outline\"\r\n          >\r\n            <RefreshCw className=\"mr-2 size-4\" />\r\n            Try Again\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render empty state\r\n  if (records.length === 0) {\r\n    return (\r\n      <EmptyState\r\n        title=\"No Service Records Found\"\r\n        description={\r\n          vehicleSpecific\r\n            ? 'No service records available for this vehicle. You can log a new service record to get started.'\r\n            : 'There are no service records matching your current filters. You can log a new service record or adjust your filters.'\r\n        }\r\n        icon={History}\r\n        primaryAction={{\r\n          label: 'Log New Service',\r\n          href: '/vehicles',\r\n          icon: <PlusCircle className=\"size-4\" />,\r\n        }}\r\n      />\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Summary Statistics */}\r\n      <ServiceHistorySummary\r\n        records={records}\r\n        vehicleSpecific={vehicleSpecific}\r\n      />\r\n\r\n      {/* Service Records Table */}\r\n      <Card className=\"card-print shadow-md\">\r\n        <CardContent className=\"p-0\">\r\n          <div className=\"overflow-x-auto\">\r\n            <ServiceHistoryTable\r\n              records={records}\r\n              showVehicleInfo={showVehicleInfo}\r\n              onDelete={handleDelete}\r\n              onBulkDelete={handleBulkDelete}\r\n            />\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAfA;;;;;;;;;;;AAoDO,SAAS,gCAAgC,EAC9C,SAAS,EACT,KAAK,EACL,SAAS,EACT,OAAO,EACP,OAAO,EACP,kBAAkB,IAAI,EACtB,kBAAkB,KAAK,EACc;IACrC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,iBAAiB,CAAA,GAAA,oJAAA,CAAA,yBAAsB,AAAD;IAE5C,gCAAgC;IAChC,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,iDAAiD;YACjD,MAAM,eAAe,WAAW,CAAC;gBAC/B,IAAI,OAAO,EAAE;gBACb,WAAW,OAAO,SAAS;YAC7B;YAEA,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YAEA,gCAAgC;YAChC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,8BAA8B;YAC9B,MAAM,QAAQ,GAAG,CACf,gBAAgB,GAAG,CAAC,CAAA,SAClB,iEAAiE;gBACjE,eAAe,WAAW,CAAC;oBACzB,IAAI,OAAO,EAAE;oBACb,WAAW,OAAO,SAAS;gBAC7B;YAIJ,MAAM;gBACJ,OAAO;gBACP,aAAa,GAAG,gBAAgB,MAAM,CAAC,sCAAsC,CAAC;gBAC9E,SAAS;YACX;YAEA,gCAAgC;YAChC;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,6DAA6D;IAC7D,qDAAqD;IAErD,uBAAuB;IACvB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;YAAY,eAAY;sBACrC,cAAA,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;gBAAG,SAAQ;;;;;;;;;;;IAGxC;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4IAAA,CAAA,eAAY;oBAAC,SAAQ;oBAA0B,OAAO;;;;;;8BACvD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,cAAW;wBACX,SAAS;wBACT,MAAK;wBACL,SAAQ;;0CAER,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAgB;;;;;;;;;;;;;;;;;;IAM/C;IAEA,qBAAqB;IACrB,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,qBACE,8OAAC,mIAAA,CAAA,aAAU;YACT,OAAM;YACN,aACE,kBACI,oGACA;YAEN,MAAM,wMAAA,CAAA,UAAO;YACb,eAAe;gBACb,OAAO;gBACP,MAAM;gBACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC9B;;;;;;IAGN;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,sJAAA,CAAA,wBAAqB;gBACpB,SAAS;gBACT,iBAAiB;;;;;;0BAInB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,+JAAA,CAAA,sBAAmB;4BAClB,SAAS;4BACT,iBAAiB;4BACjB,UAAU;4BACV,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5B", "debugId": null}}, {"offset": {"line": 4856, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { ChevronRight, MoreHorizontal } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Breadcrumb = React.forwardRef<\r\n  HTMLElement,\r\n  React.HTMLAttributes<HTMLElement>\r\n>(({ className, ...props }, ref) => (\r\n  <nav\r\n    aria-label=\"breadcrumb\"\r\n    className={cn(\r\n      'flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumb.displayName = 'Breadcrumb';\r\n\r\nconst BreadcrumbList = React.forwardRef<\r\n  HTMLOListElement,\r\n  React.OlHTMLAttributes<HTMLOListElement>\r\n>(({ className, ...props }, ref) => (\r\n  <ol\r\n    className={cn(\r\n      'flex flex-wrap items-center gap-1.5 break-words text-sm',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumbList.displayName = 'BreadcrumbList';\r\n\r\nconst BreadcrumbItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.LiHTMLAttributes<HTMLLIElement>\r\n>(({ className, ...props }, ref) => (\r\n  <li\r\n    className={cn('inline-flex items-center gap-1.5', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumbItem.displayName = 'BreadcrumbItem';\r\n\r\nconst BreadcrumbLink = React.forwardRef<\r\n  HTMLAnchorElement,\r\n  React.AnchorHTMLAttributes<HTMLAnchorElement> & {\r\n    asChild?: boolean;\r\n  }\r\n>(({ asChild, className, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : 'a';\r\n\r\n  return (\r\n    <Comp\r\n      className={cn('transition-colors hover:text-foreground', className)}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nBreadcrumbLink.displayName = 'BreadcrumbLink';\r\n\r\nconst BreadcrumbPage = React.forwardRef<\r\n  HTMLSpanElement,\r\n  React.HTMLAttributes<HTMLSpanElement>\r\n>(({ className, ...props }, ref) => (\r\n  <span\r\n    aria-current=\"page\"\r\n    aria-disabled=\"true\"\r\n    className={cn('font-normal text-foreground', className)}\r\n    ref={ref}\r\n    role=\"link\"\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumbPage.displayName = 'BreadcrumbPage';\r\n\r\nconst BreadcrumbSeparator = ({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => (\r\n  <span\r\n    aria-hidden=\"true\"\r\n    className={cn('[&>svg]:size-3.5', className)}\r\n    role=\"presentation\"\r\n    {...props}\r\n  >\r\n    {children ?? <ChevronRight className=\"size-4\" />}\r\n  </span>\r\n);\r\nBreadcrumbSeparator.displayName = 'BreadcrumbSeparator';\r\n\r\nconst BreadcrumbEllipsis = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => (\r\n  <span\r\n    aria-hidden=\"true\"\r\n    className={cn('flex h-9 w-9 items-center justify-center', className)}\r\n    role=\"presentation\"\r\n    {...props}\r\n  >\r\n    <MoreHorizontal className=\"size-4\" />\r\n    <span className=\"sr-only\">More</span>\r\n  </span>\r\n);\r\nBreadcrumbEllipsis.displayName = 'BreadcrumbElipssis';\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbEllipsis,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAAA;AACA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,cAAW;QACX,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,iFACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QAClD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKpC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACnC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACzD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,gBAAa;QACb,iBAAc;QACd,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC7C,KAAK;QACL,MAAK;QACJ,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,sBAAsB,CAAC,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACmC,iBACtC,8OAAC;QACC,eAAY;QACZ,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QAClC,MAAK;QACJ,GAAG,KAAK;kBAER,0BAAY,8OAAC,sNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;;;;;;AAGzC,oBAAoB,WAAW,GAAG;AAElC,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OACmC,iBACtC,8OAAC;QACC,eAAY;QACZ,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QAC1D,MAAK;QACJ,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4988, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/app-breadcrumb.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport React from 'react';\r\n\r\nimport {\r\n  Breadcrumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from '@/components/ui/breadcrumb';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface AppBreadcrumbProps {\r\n  className?: string;\r\n  homeHref?: string;\r\n  homeLabel?: string;\r\n  showContainer?: boolean;\r\n}\r\n\r\n/**\r\n * Enhanced AppBreadcrumb Component\r\n *\r\n * Professional breadcrumb navigation with improved styling, accessibility,\r\n * and responsive design. Automatically generates breadcrumbs from the current\r\n * pathname with intelligent segment formatting.\r\n *\r\n * Features:\r\n * - Professional visual design with subtle container styling\r\n * - Responsive layout that adapts to screen size\r\n * - Intelligent path segment formatting (handles IDs, special cases)\r\n * - Enhanced accessibility with proper ARIA labels\r\n * - Consistent integration with design system\r\n * - Smooth hover transitions and visual feedback\r\n */\r\nexport function AppBreadcrumb({\r\n  className,\r\n  homeHref = '/',\r\n  homeLabel = 'Dashboard',\r\n  showContainer = true,\r\n}: AppBreadcrumbProps) {\r\n  const pathname = usePathname();\r\n  const pathSegments = pathname ? pathname.split('/').filter(Boolean) : [];\r\n\r\n  /**\r\n   * Format path segments with intelligent handling of different segment types\r\n   */\r\n  const formatSegment = (segment: string): string => {\r\n    // Handle numeric IDs (don't capitalize, show as \"ID: 123\")\r\n    if (/^\\d+$/.test(segment)) {\r\n      return `ID: ${segment}`;\r\n    }\r\n\r\n    // Handle UUIDs or long alphanumeric strings (show as \"Details\")\r\n    if (segment.length > 10 && /^[a-zA-Z0-9-]+$/.test(segment)) {\r\n      return 'Details';\r\n    }\r\n\r\n    // Handle special cases\r\n    const specialCases: Record<string, string> = {\r\n      add: 'Add New',\r\n      admin: 'Administration',\r\n      edit: 'Edit',\r\n      reports: 'Reports',\r\n      'service-history': 'Service History',\r\n      settings: 'Settings',\r\n    };\r\n\r\n    if (specialCases[segment]) {\r\n      return specialCases[segment];\r\n    }\r\n\r\n    // Default formatting: capitalize and replace dashes with spaces\r\n    return segment\r\n      .split('-')\r\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n      .join(' ');\r\n  };\r\n\r\n  const breadcrumbItems = pathSegments.map((segment, index) => {\r\n    const href = '/' + pathSegments.slice(0, index + 1).join('/');\r\n    const isLast = index === pathSegments.length - 1;\r\n    const displaySegment = formatSegment(segment);\r\n\r\n    return (\r\n      <React.Fragment key={href}>\r\n        <BreadcrumbItem>\r\n          {isLast ? (\r\n            <BreadcrumbPage className=\"font-medium text-foreground\">\r\n              {displaySegment}\r\n            </BreadcrumbPage>\r\n          ) : (\r\n            <BreadcrumbLink asChild>\r\n              <Link\r\n                className=\"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2\"\r\n                href={href}\r\n              >\r\n                {displaySegment}\r\n              </Link>\r\n            </BreadcrumbLink>\r\n          )}\r\n        </BreadcrumbItem>\r\n        {!isLast && <BreadcrumbSeparator />}\r\n      </React.Fragment>\r\n    );\r\n  });\r\n\r\n  const breadcrumbContent = (\r\n    <Breadcrumb className={cn('text-sm', className)}>\r\n      <BreadcrumbList className=\"flex-wrap\">\r\n        <BreadcrumbItem>\r\n          <BreadcrumbLink asChild>\r\n            <Link\r\n              className=\"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2\"\r\n              href={homeHref}\r\n            >\r\n              {homeLabel}\r\n            </Link>\r\n          </BreadcrumbLink>\r\n        </BreadcrumbItem>\r\n        {pathSegments.length > 0 && <BreadcrumbSeparator />}\r\n        {breadcrumbItems}\r\n      </BreadcrumbList>\r\n    </Breadcrumb>\r\n  );\r\n\r\n  if (!showContainer) {\r\n    return breadcrumbContent;\r\n  }\r\n\r\n  return (\r\n    <div className=\"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm\">\r\n      <div className=\"flex items-center\">{breadcrumbContent}</div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAQA;AAAA;AAdA;;;;;;;AAsCO,SAAS,cAAc,EAC5B,SAAS,EACT,WAAW,GAAG,EACd,YAAY,WAAW,EACvB,gBAAgB,IAAI,EACD;IACnB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC,WAAW,EAAE;IAExE;;GAEC,GACD,MAAM,gBAAgB,CAAC;QACrB,2DAA2D;QAC3D,IAAI,QAAQ,IAAI,CAAC,UAAU;YACzB,OAAO,CAAC,IAAI,EAAE,SAAS;QACzB;QAEA,gEAAgE;QAChE,IAAI,QAAQ,MAAM,GAAG,MAAM,kBAAkB,IAAI,CAAC,UAAU;YAC1D,OAAO;QACT;QAEA,uBAAuB;QACvB,MAAM,eAAuC;YAC3C,KAAK;YACL,OAAO;YACP,MAAM;YACN,SAAS;YACT,mBAAmB;YACnB,UAAU;QACZ;QAEA,IAAI,YAAY,CAAC,QAAQ,EAAE;YACzB,OAAO,YAAY,CAAC,QAAQ;QAC9B;QAEA,gEAAgE;QAChE,OAAO,QACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IAEA,MAAM,kBAAkB,aAAa,GAAG,CAAC,CAAC,SAAS;QACjD,MAAM,OAAO,MAAM,aAAa,KAAK,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC;QACzD,MAAM,SAAS,UAAU,aAAa,MAAM,GAAG;QAC/C,MAAM,iBAAiB,cAAc;QAErC,qBACE,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;8BACb,8OAAC,sIAAA,CAAA,iBAAc;8BACZ,uBACC,8OAAC,sIAAA,CAAA,iBAAc;wBAAC,WAAU;kCACvB;;;;;6CAGH,8OAAC,sIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,WAAU;4BACV,MAAM;sCAEL;;;;;;;;;;;;;;;;gBAKR,CAAC,wBAAU,8OAAC,sIAAA,CAAA,sBAAmB;;;;;;WAjBb;;;;;IAoBzB;IAEA,MAAM,kCACJ,8OAAC,sIAAA,CAAA,aAAU;QAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW;kBACnC,cAAA,8OAAC,sIAAA,CAAA,iBAAc;YAAC,WAAU;;8BACxB,8OAAC,sIAAA,CAAA,iBAAc;8BACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,WAAU;4BACV,MAAM;sCAEL;;;;;;;;;;;;;;;;gBAIN,aAAa,MAAM,GAAG,mBAAK,8OAAC,sIAAA,CAAA,sBAAmB;;;;;gBAC/C;;;;;;;;;;;;IAKP,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBAAqB;;;;;;;;;;;AAG1C", "debugId": null}}, {"offset": {"line": 5150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/PageHeader.tsx"], "sourcesContent": ["import type { LucideIcon } from 'lucide-react';\r\n\r\nimport React from 'react';\r\n\r\ninterface PageHeaderProps {\r\n  children?: React.ReactNode; // For action buttons like \"Add New\"\r\n  description?: string;\r\n  icon?: LucideIcon;\r\n  title: string;\r\n}\r\n\r\nexport function PageHeader({\r\n  children,\r\n  description,\r\n  icon: Icon,\r\n  title,\r\n}: PageHeaderProps) {\r\n  return (\r\n    <div className=\"mb-6 flex items-center justify-between border-b border-border/50 pb-4\">\r\n      <div>\r\n        <div className=\"flex items-center gap-3\">\r\n          {Icon && <Icon className=\"size-8 text-primary\" />}\r\n          <h1 className=\"text-3xl font-bold tracking-tight text-foreground\">\r\n            {title}\r\n          </h1>\r\n        </div>\r\n        {description && (\r\n          <p className=\"mt-1 text-muted-foreground\">{description}</p>\r\n        )}\r\n      </div>\r\n      {children && <div className=\"flex items-center gap-2\">{children}</div>}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAWO,SAAS,WAAW,EACzB,QAAQ,EACR,WAAW,EACX,MAAM,IAAI,EACV,KAAK,EACW;IAChB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;;4BACZ,sBAAQ,8OAAC;gCAAK,WAAU;;;;;;0CACzB,8OAAC;gCAAG,WAAU;0CACX;;;;;;;;;;;;oBAGJ,6BACC,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;YAG9C,0BAAY,8OAAC;gBAAI,WAAU;0BAA2B;;;;;;;;;;;;AAG7D", "debugId": null}}, {"offset": {"line": 5220, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/ui/useNotifications.ts"], "sourcesContent": ["/**\r\n * @file Custom hook for notification management using Zustand AppStore\r\n * @module hooks/useNotifications\r\n */\r\n\r\nimport { useCallback } from 'react';\r\n\r\nimport { useAppStore } from '@/lib/stores/zustand/appStore';\r\nimport { undefinedToNull } from '../../lib/utils/typeHelpers';\r\n\r\n/**\r\n * Custom hook for simplified notification management\r\n * Provides convenient methods for showing different types of notifications\r\n */\r\nexport const useNotifications = () => {\r\n  const addNotification = useAppStore(state => state.addNotification);\r\n  const removeNotification = useAppStore(state => state.removeNotification);\r\n  const clearAllNotifications = useAppStore(\r\n    state => state.clearAllNotifications\r\n  );\r\n  const unreadCount = useAppStore(state => state.unreadNotificationCount);\r\n\r\n  /**\r\n   * Show a success notification\r\n   */\r\n  const showSuccess = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'success',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show an error notification\r\n   */\r\n  const showError = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'error',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a warning notification\r\n   */\r\n  const showWarning = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'warning',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show an info notification\r\n   */\r\n  const showInfo = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'info',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a notification for API operation results\r\n   */\r\n  const showApiResult = useCallback(\r\n    (success: boolean, successMessage: string, errorMessage: string) => {\r\n      if (success) {\r\n        showSuccess(successMessage);\r\n      } else {\r\n        showError(errorMessage);\r\n      }\r\n    },\r\n    [showSuccess, showError]\r\n  );\r\n\r\n  /**\r\n   * Show a notification with auto-dismiss after specified time\r\n   */\r\n  const showTemporary = useCallback(\r\n    (\r\n      type: 'error' | 'info' | 'success' | 'warning',\r\n      message: string,\r\n      dismissAfter = 5000\r\n    ) => {\r\n      addNotification({ message, type });\r\n\r\n      // Auto-dismiss after specified time\r\n      setTimeout(() => {\r\n        // Note: This is a simplified approach. In a real implementation,\r\n        // you might want to store the notification ID and remove specifically that one\r\n        const notifications = useAppStore.getState().notifications;\r\n        const latestNotification = notifications.at(-1);\r\n        if (latestNotification && latestNotification.message === message) {\r\n          removeNotification(latestNotification.id);\r\n        }\r\n      }, dismissAfter);\r\n    },\r\n    [addNotification, removeNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a loading notification that can be updated\r\n   */\r\n  const showLoading = useCallback(\r\n    (message = 'Loading...') => {\r\n      addNotification({\r\n        message,\r\n        type: 'info',\r\n      });\r\n\r\n      // Return the notification ID for potential updates\r\n      const notifications = useAppStore.getState().notifications;\r\n      return notifications.at(-1)?.id;\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Update a loading notification to success or error\r\n   */\r\n  const updateLoadingNotification = useCallback(\r\n    (notificationId: string, success: boolean, message: string) => {\r\n      removeNotification(notificationId);\r\n      if (success) {\r\n        showSuccess(message);\r\n      } else {\r\n        showError(message);\r\n      }\r\n    },\r\n    [removeNotification, showSuccess, showError]\r\n  );\r\n\r\n  return {\r\n    clearAllNotifications,\r\n    // Store methods\r\n    removeNotification,\r\n    // Advanced methods\r\n    showApiResult,\r\n    showError,\r\n\r\n    showInfo,\r\n    showLoading,\r\n    // Basic notification methods\r\n    showSuccess,\r\n    showTemporary,\r\n\r\n    showWarning,\r\n    unreadCount,\r\n    updateLoadingNotification,\r\n  };\r\n};\r\n\r\n/**\r\n * Enhanced notification hook with WorkHub-specific notification types\r\n */\r\nexport const useWorkHubNotifications = () => {\r\n  const {\r\n    clearAllNotifications,\r\n    removeNotification,\r\n    showError,\r\n    showInfo,\r\n    showSuccess,\r\n    showWarning,\r\n    unreadCount,\r\n  } = useNotifications();\r\n\r\n  /**\r\n   * Show delegation-related notifications\r\n   */\r\n  const showDelegationUpdate = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'delegation',\r\n        message,\r\n        type: 'delegation-update',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show vehicle maintenance notifications\r\n   */\r\n  const showVehicleMaintenance = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'vehicle',\r\n        message,\r\n        type: 'vehicle-maintenance',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show task assignment notifications\r\n   */\r\n  const showTaskAssigned = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'task',\r\n        message,\r\n        type: 'task-assigned',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show employee update notifications\r\n   */\r\n  const showEmployeeUpdate = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'employee',\r\n        message,\r\n        type: 'employee-update',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  return {\r\n    clearAllNotifications,\r\n    // Management\r\n    removeNotification,\r\n    // WorkHub-specific notifications\r\n    showDelegationUpdate,\r\n    showEmployeeUpdate,\r\n\r\n    showError,\r\n    showInfo,\r\n    // Basic notifications\r\n    showSuccess,\r\n    showTaskAssigned,\r\n\r\n    showVehicleMaintenance,\r\n    showWarning,\r\n    unreadCount,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AAEA;;;AAOO,MAAM,mBAAmB;IAC9B,MAAM,kBAAkB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,eAAe;IAClE,MAAM,qBAAqB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,kBAAkB;IACxE,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EACtC,CAAA,QAAS,MAAM,qBAAqB;IAEtC,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,uBAAuB;IAEtE;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC1B,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACzB,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,SAAkB,gBAAwB;QACzC,IAAI,SAAS;YACX,YAAY;QACd,OAAO;YACL,UAAU;QACZ;IACF,GACA;QAAC;QAAa;KAAU;IAG1B;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CACE,MACA,SACA,eAAe,IAAI;QAEnB,gBAAgB;YAAE;YAAS;QAAK;QAEhC,oCAAoC;QACpC,WAAW;YACT,iEAAiE;YACjE,+EAA+E;YAC/E,MAAM,gBAAgB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;YAC1D,MAAM,qBAAqB,cAAc,EAAE,CAAC,CAAC;YAC7C,IAAI,sBAAsB,mBAAmB,OAAO,KAAK,SAAS;gBAChE,mBAAmB,mBAAmB,EAAE;YAC1C;QACF,GAAG;IACL,GACA;QAAC;QAAiB;KAAmB;IAGvC;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC,UAAU,YAAY;QACrB,gBAAgB;YACd;YACA,MAAM;QACR;QAEA,mDAAmD;QACnD,MAAM,gBAAgB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;QAC1D,OAAO,cAAc,EAAE,CAAC,CAAC,IAAI;IAC/B,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC1C,CAAC,gBAAwB,SAAkB;QACzC,mBAAmB;QACnB,IAAI,SAAS;YACX,YAAY;QACd,OAAO;YACL,UAAU;QACZ;IACF,GACA;QAAC;QAAoB;QAAa;KAAU;IAG9C,OAAO;QACL;QACA,gBAAgB;QAChB;QACA,mBAAmB;QACnB;QACA;QAEA;QACA;QACA,6BAA6B;QAC7B;QACA;QAEA;QACA;QACA;IACF;AACF;AAKO,MAAM,0BAA0B;IACrC,MAAM,EACJ,qBAAqB,EACrB,kBAAkB,EAClB,SAAS,EACT,QAAQ,EACR,WAAW,EACX,WAAW,EACX,WAAW,EACZ,GAAG;IAEJ;;GAEC,GACD,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACvC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ,OAAO;QACL;QACA,aAAa;QACb;QACA,iCAAiC;QACjC;QACA;QAEA;QACA;QACA,sBAAsB;QACtB;QACA;QAEA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 5430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/useVehicles.ts"], "sourcesContent": ["/**\r\n * @file TanStack Query hooks for Vehicle-related data.\r\n * @module stores/queries/useVehicles\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query';\r\n\r\nimport type { ApiError } from '@/lib/types/api';\r\nimport type { CreateVehicleData, Vehicle } from '@/lib/types/domain';\r\n\r\nimport { useCrudQuery } from '@/hooks/api/useSmartQuery';\r\nimport { useNotifications } from '@/hooks/ui/useNotifications';\r\nimport { VehicleTransformer } from '@/lib/transformers/vehicleTransformer';\r\n\r\nimport { vehicleApiService } from '../../api/services/apiServiceFactory'; // Use centralized service from factory\r\n\r\nexport const vehicleQueryKeys = {\r\n  all: ['vehicles'] as const,\r\n  detail: (id: number) => ['vehicles', id] as const,\r\n};\r\n\r\n/**\r\n * Hook to fetch all vehicles.\r\n */\r\nexport const useVehicles = (\r\n  options?: Omit<\r\n    UseQueryOptions<Vehicle[], ApiError>,\r\n    'queryFn' | 'queryKey'\r\n  > & { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<Vehicle[], ApiError>(\r\n    [...vehicleQueryKeys.all], // Spread to create a mutable array\r\n    async () => {\r\n      const response = await vehicleApiService.getAll();\r\n      return response.data;\r\n    },\r\n    'vehicle', // entityType for WebSocket events\r\n    {\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n      ...options,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook to fetch a single vehicle by its ID.\r\n */\r\nexport const useVehicle = (\r\n  id: null | number,\r\n  options?: Omit<\r\n    UseQueryOptions<Vehicle, ApiError>,\r\n    'enabled' | 'queryFn' | 'queryKey'\r\n  > & { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<Vehicle, ApiError>(\r\n    [...vehicleQueryKeys.detail(id!)],\r\n    () => vehicleApiService.getById(id!),\r\n    'vehicle', // entityType for WebSocket events\r\n    {\r\n      enabled: !!id && (options?.enabled ?? true), // Only run query if id is not null AND options.enabled is true (or undefined)\r\n      staleTime: 5 * 60 * 1000,\r\n      ...options, // Spread additional options\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook to create a new vehicle.\r\n */\r\nexport const useCreateVehicle = () => {\r\n  const queryClient = useQueryClient();\r\n  const { showError, showSuccess } = useNotifications();\r\n\r\n  return useMutation<Vehicle, ApiError, CreateVehicleData>({\r\n    mutationFn: (newVehicleData: CreateVehicleData) => {\r\n      const transformedData =\r\n        VehicleTransformer.toCreateRequest(newVehicleData);\r\n      return vehicleApiService.create(transformedData);\r\n    },\r\n    onError: error => {\r\n      showError(\r\n        `Failed to create vehicle: ${error.message || 'Unknown error occurred'}`\r\n      );\r\n    },\r\n    onSuccess: data => {\r\n      // Invalidate and refetch all vehicles query after a successful creation\r\n      queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all });\r\n      showSuccess(\r\n        `Vehicle \"${data.licensePlate}\" has been created successfully!`\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to update an existing vehicle.\r\n */\r\nexport const useUpdateVehicle = () => {\r\n  const queryClient = useQueryClient();\r\n  const { showError, showSuccess } = useNotifications();\r\n\r\n  return useMutation<\r\n    Vehicle,\r\n    ApiError,\r\n    { data: Partial<CreateVehicleData>; id: number }\r\n  >({\r\n    mutationFn: ({ data, id }) => {\r\n      const transformedData = VehicleTransformer.toUpdateRequest(data);\r\n      return vehicleApiService.update(id, transformedData);\r\n    },\r\n    onError: error => {\r\n      showError(\r\n        `Failed to update vehicle: ${error.message || 'Unknown error occurred'}`\r\n      );\r\n    },\r\n    onSuccess: updatedVehicle => {\r\n      queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all });\r\n      queryClient.invalidateQueries({\r\n        queryKey: vehicleQueryKeys.detail(updatedVehicle.id),\r\n      });\r\n      showSuccess(\r\n        `Vehicle \"${updatedVehicle.licensePlate}\" has been updated successfully!`\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to delete a vehicle.\r\n */\r\nexport const useDeleteVehicle = () => {\r\n  const queryClient = useQueryClient();\r\n  const { showError, showSuccess } = useNotifications();\r\n\r\n  return useMutation<void, ApiError, number>({\r\n    mutationFn: (id: number) => vehicleApiService.delete(id),\r\n    onError: error => {\r\n      showError(\r\n        `Failed to delete vehicle: ${error.message || 'Unknown error occurred'}`\r\n      );\r\n    },\r\n    onSuccess: (_data, id) => {\r\n      queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all });\r\n      queryClient.removeQueries({ queryKey: vehicleQueryKeys.detail(id) });\r\n      showSuccess('Vehicle has been deleted successfully!');\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAID;AAAA;AAKA;AACA;AACA;AAEA,gTAA0E,uCAAuC;AAAjH;;;;;;AAEO,MAAM,mBAAmB;IAC9B,KAAK;QAAC;KAAW;IACjB,QAAQ,CAAC,KAAe;YAAC;YAAY;SAAG;AAC1C;AAKO,MAAM,cAAc,CACzB;IAKA,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,iBAAiB,GAAG;KAAC,EACzB;QACE,MAAM,WAAW,MAAM,wIAAA,CAAA,oBAAiB,CAAC,MAAM;QAC/C,OAAO,SAAS,IAAI;IACtB,GACA,WACA;QACE,WAAW,IAAI,KAAK;QACpB,GAAG,OAAO;IACZ;AAEJ;AAKO,MAAM,aAAa,CACxB,IACA;IAKA,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,iBAAiB,MAAM,CAAC;KAAK,EACjC,IAAM,wIAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC,KAChC,WACA;QACE,SAAS,CAAC,CAAC,MAAM,CAAC,SAAS,WAAW,IAAI;QAC1C,WAAW,IAAI,KAAK;QACpB,GAAG,OAAO;IACZ;AAEJ;AAKO,MAAM,mBAAmB;IAC9B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,mBAAgB,AAAD;IAElD,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAwC;QACvD,YAAY,CAAC;YACX,MAAM,kBACJ,gJAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;YACrC,OAAO,wIAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC;QAClC;QACA,SAAS,CAAA;YACP,UACE,CAAC,0BAA0B,EAAE,MAAM,OAAO,IAAI,0BAA0B;QAE5E;QACA,WAAW,CAAA;YACT,wEAAwE;YACxE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,iBAAiB,GAAG;YAAC;YAC/D,YACE,CAAC,SAAS,EAAE,KAAK,YAAY,CAAC,gCAAgC,CAAC;QAEnE;IACF;AACF;AAKO,MAAM,mBAAmB;IAC9B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,mBAAgB,AAAD;IAElD,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAIf;QACA,YAAY,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;YACvB,MAAM,kBAAkB,gJAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;YAC3D,OAAO,wIAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,IAAI;QACtC;QACA,SAAS,CAAA;YACP,UACE,CAAC,0BAA0B,EAAE,MAAM,OAAO,IAAI,0BAA0B;QAE5E;QACA,WAAW,CAAA;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,iBAAiB,GAAG;YAAC;YAC/D,YAAY,iBAAiB,CAAC;gBAC5B,UAAU,iBAAiB,MAAM,CAAC,eAAe,EAAE;YACrD;YACA,YACE,CAAC,SAAS,EAAE,eAAe,YAAY,CAAC,gCAAgC,CAAC;QAE7E;IACF;AACF;AAKO,MAAM,mBAAmB;IAC9B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,mBAAgB,AAAD;IAElD,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAA0B;QACzC,YAAY,CAAC,KAAe,wIAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC;QACrD,SAAS,CAAA;YACP,UACE,CAAC,0BAA0B,EAAE,MAAM,OAAO,IAAI,0BAA0B;QAE5E;QACA,WAAW,CAAC,OAAO;YACjB,YAAY,iBAAiB,CAAC;gBAAE,UAAU,iBAAiB,GAAG;YAAC;YAC/D,YAAY,aAAa,CAAC;gBAAE,UAAU,iBAAiB,MAAM,CAAC;YAAI;YAClE,YAAY;QACd;IACF;AACF", "debugId": null}}, {"offset": {"line": 5549, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/app/service-history/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Car, History, PlusCircle, Search, Wrench, X } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport { useCallback, useEffect, useMemo, useState } from 'react';\r\n\r\nimport type { Vehicle } from '@/lib/types/domain';\r\nimport type { EnrichedServiceRecord } from '@/lib/types/domain'; // Assuming this type is what useEnrichedServiceRecords returns\r\n\r\nimport ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';\r\nimport ServiceRecordsErrorBoundary from '@/components/error-boundaries/ServiceRecordsErrorBoundary';\r\nimport { ReportActions } from '@/components/reports/ReportActions';\r\nimport { EnhancedServiceHistoryContainer } from '@/components/service-history/EnhancedServiceHistoryContainer';\r\nimport { ActionButton } from '@/components/ui/action-button';\r\nimport { AppBreadcrumb } from '@/components/ui/app-breadcrumb';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { EmptyState } from '@/components/ui/loading';\r\nimport { PageHeader } from '@/components/ui/PageHeader';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { useAuthContext } from '@/contexts/AuthContext';\r\nimport { useEnrichedServiceRecords } from '@/lib/stores/queries/useServiceRecords'; // Using the new hook\r\nimport { useVehicles } from '@/lib/stores/queries/useVehicles';\r\n\r\nconst commonServicesList = [\r\n  'Oil Change',\r\n  'Tire Rotation',\r\n  'Brake Service',\r\n  'Battery Replacement',\r\n  'Air Filter Replacement',\r\n  'Cabin Air Filter Replacement',\r\n  'Wiper Blade Replacement',\r\n  'Fluid Check/Top-up',\r\n  'Spark Plug Replacement',\r\n  'Coolant Flush',\r\n  'Transmission Service',\r\n  'Wheel Alignment',\r\n  'State Inspection',\r\n  'Other',\r\n];\r\n\r\nexport default function ServiceHistoryPage() {\r\n  const { loading: authLoading, session, user } = useAuthContext();\r\n  const isAuthenticated = !!user && !!session?.access_token;\r\n\r\n  const {\r\n    data: allVehiclesData,\r\n    error: vehiclesError,\r\n    isLoading: rqIsLoadingVehicles,\r\n    refetch: refetchVehicles,\r\n  } = useVehicles({ enabled: isAuthenticated && !authLoading });\r\n  const allVehicles = useMemo(() => allVehiclesData || [], [allVehiclesData]);\r\n\r\n  const {\r\n    data: serviceRecordsData,\r\n    error: recordsError,\r\n    isLoading: isLoadingRecords,\r\n    refetch: refetchServiceRecords,\r\n  } = useEnrichedServiceRecords({ enabled: isAuthenticated && !authLoading }); // Pass enabled option\r\n\r\n  const serviceRecords = useMemo(\r\n    () => serviceRecordsData || [],\r\n    [serviceRecordsData]\r\n  );\r\n\r\n  const [selectedVehicleId, setSelectedVehicleId] = useState('all');\r\n  const [selectedServiceType, setSelectedServiceType] = useState('all');\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');\r\n\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setDebouncedSearchTerm(searchTerm);\r\n    }, 300);\r\n    return () => clearTimeout(timer);\r\n  }, [searchTerm]);\r\n\r\n  const uniqueServiceTypes = useMemo(() => {\r\n    const types = new Set<string>();\r\n    serviceRecords.forEach((record: EnrichedServiceRecord) => {\r\n      record.servicePerformed.forEach((service: string) => types.add(service));\r\n    });\r\n    commonServicesList.forEach((service: string) => types.add(service));\r\n    return [...types].sort();\r\n  }, [serviceRecords]);\r\n\r\n  // 🔧 FIX: Replace useEffect + useState with useMemo to prevent infinite loop\r\n  // This prevents the \"Maximum update depth exceeded\" error by avoiding unstable array references\r\n  const filteredRecords = useMemo(() => {\r\n    let tempRecords = [...serviceRecords];\r\n    if (selectedVehicleId !== 'all') {\r\n      tempRecords = tempRecords.filter(\r\n        record => String(record.vehicleId) === selectedVehicleId\r\n      );\r\n    }\r\n    if (selectedServiceType !== 'all') {\r\n      tempRecords = tempRecords.filter(record =>\r\n        record.servicePerformed.includes(selectedServiceType)\r\n      );\r\n    }\r\n    if (debouncedSearchTerm) {\r\n      const lowerSearchTerm = debouncedSearchTerm.toLowerCase();\r\n      tempRecords = tempRecords.filter(\r\n        record =>\r\n          `${record.vehicleMake} ${record.vehicleModel}`\r\n            .toLowerCase()\r\n            .includes(lowerSearchTerm) ||\r\n          record.servicePerformed\r\n            .join(' ')\r\n            .toLowerCase()\r\n            .includes(lowerSearchTerm) ||\r\n          record.notes?.toLowerCase().includes(lowerSearchTerm) ||\r\n          record.licensePlate?.toLowerCase().includes(lowerSearchTerm) ||\r\n          record.odometer.toString().includes(lowerSearchTerm)\r\n      );\r\n    }\r\n    return tempRecords;\r\n  }, [\r\n    serviceRecords,\r\n    selectedVehicleId,\r\n    selectedServiceType,\r\n    debouncedSearchTerm,\r\n  ]);\r\n\r\n  const handleRetry = useCallback(() => {\r\n    if (typeof refetchVehicles === 'function') refetchVehicles();\r\n    if (typeof refetchServiceRecords === 'function') refetchServiceRecords();\r\n  }, [refetchVehicles, refetchServiceRecords]);\r\n\r\n  const pageIsLoading = authLoading || rqIsLoadingVehicles || isLoadingRecords;\r\n\r\n  const errorMessage = useMemo(() => {\r\n    const errors = [];\r\n    if (vehiclesError)\r\n      errors.push(`Vehicles: ${(vehiclesError as Error).message}`);\r\n    if (recordsError)\r\n      errors.push(`Service Records: ${(recordsError as Error).message}`);\r\n    return errors.length > 0 ? errors.join('; ') : null;\r\n  }, [vehiclesError, recordsError]);\r\n\r\n  if (pageIsLoading) {\r\n    return (\r\n      <ErrorBoundary>\r\n        <div className=\"space-y-6\">\r\n          <PageHeader icon={History} title=\"Service History\">\r\n            <div className=\"flex gap-2\">\r\n              <Skeleton className=\"h-10 w-32\" />\r\n              <Skeleton className=\"h-10 w-32\" />\r\n            </div>\r\n          </PageHeader>\r\n          <Card>\r\n            <CardContent className=\"pt-6\">\r\n              <div className=\"mb-6 grid grid-cols-1 gap-4 md:grid-cols-3\">\r\n                <Skeleton className=\"h-10 w-full\" />\r\n                <Skeleton className=\"h-10 w-full\" />\r\n                <Skeleton className=\"h-10 w-full\" />\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n          <div className=\"space-y-4\">\r\n            {Array.from({ length: 5 }).map((_, i) => (\r\n              <div className=\"flex items-center space-x-4 border-b p-4\" key={i}>\r\n                <Skeleton className=\"h-6 w-1/6\" />\r\n                <Skeleton className=\"h-6 w-1/6\" />\r\n                <Skeleton className=\"h-6 w-2/6\" />\r\n                <Skeleton className=\"h-6 w-1/6\" />\r\n                <Skeleton className=\"h-6 w-1/6\" />\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </ErrorBoundary>\r\n    );\r\n  }\r\n\r\n  if (!authLoading && !isAuthenticated) {\r\n    return (\r\n      <ErrorBoundary>\r\n        <div className=\"space-y-6 text-center\">\r\n          <PageHeader icon={History} title=\"Access Denied\" />\r\n          <p>Please sign in to view service history.</p>\r\n        </div>\r\n      </ErrorBoundary>\r\n    );\r\n  }\r\n\r\n  // NEW: Unified Empty Page for no records\r\n  if (\r\n    !pageIsLoading &&\r\n    isAuthenticated &&\r\n    filteredRecords.length === 0 &&\r\n    !errorMessage\r\n  ) {\r\n    const hasActiveFilters =\r\n      selectedVehicleId !== 'all' ||\r\n      selectedServiceType !== 'all' ||\r\n      searchTerm;\r\n\r\n    return (\r\n      <ErrorBoundary>\r\n        <div className=\"space-y-6\">\r\n          <AppBreadcrumb />\r\n          <PageHeader\r\n            description=\"View and manage all service records for your vehicles.\"\r\n            icon={History}\r\n            title=\"Service History Report\"\r\n          >\r\n            <div className=\"no-print flex gap-2\">\r\n              <ActionButton\r\n                actionType=\"tertiary\"\r\n                asChild\r\n                icon={<PlusCircle className=\"size-4\" />}\r\n              >\r\n                <Link href=\"/vehicles\">Log New Service</Link>\r\n              </ActionButton>\r\n            </div>\r\n          </PageHeader>\r\n\r\n          <EmptyState\r\n            description={\r\n              hasActiveFilters\r\n                ? 'There are no service records matching your current filters. You can log a new service record or adjust your filters.'\r\n                : 'No service records have been logged yet. Get started by logging your first service record.'\r\n            }\r\n            icon={History}\r\n            primaryAction={{\r\n              href: '/vehicles',\r\n              icon: <PlusCircle className=\"size-4\" />,\r\n              label: 'Log New Service',\r\n            }}\r\n            title=\"No Service Records Found\"\r\n            {...(hasActiveFilters && {\r\n              secondaryAction: {\r\n                icon: <X className=\"size-4\" />,\r\n                label: 'Reset Filters',\r\n                onClick: () => {\r\n                  setSelectedVehicleId('all');\r\n                  setSelectedServiceType('all');\r\n                  setSearchTerm('');\r\n                },\r\n              },\r\n            })}\r\n          />\r\n        </div>\r\n      </ErrorBoundary>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <ErrorBoundary>\r\n      <div className=\"print-container space-y-6\">\r\n        <AppBreadcrumb />\r\n        <PageHeader\r\n          description=\"View and manage all service records for your vehicles.\"\r\n          icon={History}\r\n          title=\"Service History Report\"\r\n        >\r\n          <div className=\"no-print flex gap-2\">\r\n            <ActionButton\r\n              actionType=\"tertiary\"\r\n              asChild\r\n              icon={<PlusCircle className=\"size-4\" />}\r\n            >\r\n              <Link href=\"/vehicles\">Log New Service</Link>\r\n            </ActionButton>\r\n            <ReportActions\r\n              enableCsv={filteredRecords.length > 0}\r\n              fileName={`service-history-report-${\r\n                new Date().toISOString().split('T')[0]\r\n              }`}\r\n              reportContentId=\"#service-history-report-content\"\r\n              reportType=\"service-history\"\r\n              tableId=\"#service-history-table\"\r\n            />\r\n          </div>\r\n        </PageHeader>\r\n\r\n        <Card className=\"no-print shadow-sm\">\r\n          <CardHeader className=\"pb-2\">\r\n            <CardTitle className=\"text-lg\">Filter Options</CardTitle>\r\n            <CardDescription>\r\n              Filter service records by vehicle, service type, or search terms\r\n            </CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"filter-grid grid grid-cols-1 items-end gap-6 md:grid-cols-2 lg:grid-cols-3\">\r\n              <div>\r\n                <Label htmlFor=\"vehicle-filter\">Filter by Vehicle</Label>\r\n                <Select\r\n                  aria-label=\"Filter by vehicle\"\r\n                  onValueChange={setSelectedVehicleId}\r\n                  value={selectedVehicleId}\r\n                >\r\n                  <SelectTrigger className=\"mt-1.5 w-full\" id=\"vehicle-filter\">\r\n                    <SelectValue placeholder=\"All Vehicles\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"all\">All Vehicles</SelectItem>\r\n                    {allVehicles.map(vehicle => (\r\n                      <SelectItem key={vehicle.id} value={String(vehicle.id)}>\r\n                        {vehicle.make} {vehicle.model} ({vehicle.year})\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n              <div>\r\n                <Label htmlFor=\"service-filter\">Filter by Service Type</Label>\r\n                <Select\r\n                  aria-label=\"Filter by service type\"\r\n                  onValueChange={setSelectedServiceType}\r\n                  value={selectedServiceType}\r\n                >\r\n                  <SelectTrigger className=\"mt-1.5 w-full\" id=\"service-filter\">\r\n                    <SelectValue placeholder=\"All Services\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"all\">All Services</SelectItem>\r\n                    {uniqueServiceTypes.map(service => (\r\n                      <SelectItem key={service} value={service}>\r\n                        {service}\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n              <div>\r\n                <Label htmlFor=\"search-records\">Search Records</Label>\r\n                <div className=\"relative mt-1.5\">\r\n                  <Input\r\n                    aria-label=\"Search service records\"\r\n                    className=\"px-10\"\r\n                    id=\"search-records\"\r\n                    onChange={e => setSearchTerm(e.target.value)}\r\n                    placeholder=\"Search by keyword, notes, plate...\"\r\n                    type=\"text\"\r\n                    value={searchTerm}\r\n                  />\r\n                  <Search\r\n                    aria-hidden=\"true\"\r\n                    className=\"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground\"\r\n                  />\r\n                  {searchTerm && (\r\n                    <Button\r\n                      aria-label=\"Clear search\"\r\n                      className=\"absolute right-1 top-1/2 size-7 -translate-y-1/2\"\r\n                      onClick={() => setSearchTerm('')}\r\n                      size=\"icon\"\r\n                      variant=\"ghost\"\r\n                    >\r\n                      <X className=\"size-4\" />\r\n                      <span className=\"sr-only\">Clear search</span>\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {(selectedVehicleId !== 'all' ||\r\n              selectedServiceType !== 'all' ||\r\n              searchTerm) && (\r\n              <div className=\"mt-6 flex flex-wrap items-center justify-between rounded-md border border-border p-3\">\r\n                <div className=\"flex flex-wrap items-center gap-2\">\r\n                  <span className=\"text-sm font-medium\">Active Filters:</span>\r\n                  {selectedVehicleId !== 'all' && (\r\n                    <Badge\r\n                      className=\"flex items-center gap-1\"\r\n                      variant=\"outline\"\r\n                    >\r\n                      <Car className=\"size-3\" />\r\n                      {\r\n                        allVehicles.find(\r\n                          v => String(v.id) === selectedVehicleId\r\n                        )?.make\r\n                      }{' '}\r\n                      {\r\n                        allVehicles.find(\r\n                          v => String(v.id) === selectedVehicleId\r\n                        )?.model\r\n                      }\r\n                    </Badge>\r\n                  )}\r\n                  {selectedServiceType !== 'all' && (\r\n                    <Badge\r\n                      className=\"flex items-center gap-1\"\r\n                      variant=\"outline\"\r\n                    >\r\n                      <Wrench className=\"size-3\" />\r\n                      {selectedServiceType}\r\n                    </Badge>\r\n                  )}\r\n                  {searchTerm && (\r\n                    <Badge\r\n                      className=\"flex items-center gap-1\"\r\n                      variant=\"outline\"\r\n                    >\r\n                      <Search className=\"size-3\" />\"{searchTerm}\"\r\n                    </Badge>\r\n                  )}\r\n                </div>\r\n                <Button\r\n                  aria-label=\"Reset all filters\"\r\n                  className=\"mt-2 sm:mt-0\"\r\n                  onClick={() => {\r\n                    setSelectedVehicleId('all');\r\n                    setSelectedServiceType('all');\r\n                    setSearchTerm('');\r\n                  }}\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                >\r\n                  <X className=\"mr-1 size-3\" />\r\n                  Reset Filters\r\n                </Button>\r\n              </div>\r\n            )}\r\n          </CardContent>\r\n        </Card>\r\n\r\n        <div className=\"report-content\" id=\"service-history-report-content\">\r\n          <ServiceRecordsErrorBoundary>\r\n            <EnhancedServiceHistoryContainer\r\n              error={errorMessage}\r\n              isLoading={isLoadingRecords && !serviceRecordsData}\r\n              onRetry={handleRetry}\r\n              records={filteredRecords}\r\n              showVehicleInfo={true}\r\n              vehicleSpecific={false}\r\n            />\r\n          </ServiceRecordsErrorBoundary>\r\n\r\n          <footer className=\"mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500\">\r\n            <p>Report generated on: {new Date().toLocaleDateString()}</p>\r\n            <p>WorkHub - Vehicle Service Management</p>\r\n          </footer>\r\n        </div>\r\n\r\n        <style global jsx>{`\r\n          /* Styles remain the same */\r\n        `}</style>\r\n      </div>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA,oQAAoF,qBAAqB;AACzG;AAtCA;;;;;;;;;;;;;;;;;;;;;;;;AAwCA,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,MAAM,EAAE,SAAS,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAC7D,MAAM,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS;IAE7C,MAAM,EACJ,MAAM,eAAe,EACrB,OAAO,aAAa,EACpB,WAAW,mBAAmB,EAC9B,SAAS,eAAe,EACzB,GAAG,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD,EAAE;QAAE,SAAS,mBAAmB,CAAC;IAAY;IAC3D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,mBAAmB,EAAE,EAAE;QAAC;KAAgB;IAE1E,MAAM,EACJ,MAAM,kBAAkB,EACxB,OAAO,YAAY,EACnB,WAAW,gBAAgB,EAC3B,SAAS,qBAAqB,EAC/B,GAAG,CAAA,GAAA,oJAAA,CAAA,4BAAyB,AAAD,EAAE;QAAE,SAAS,mBAAmB,CAAC;IAAY,IAAI,sBAAsB;IAEnG,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAC3B,IAAM,sBAAsB,EAAE,EAC9B;QAAC;KAAmB;IAGtB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,uBAAuB;QACzB,GAAG;QACH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAW;IAEf,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACjC,MAAM,QAAQ,IAAI;QAClB,eAAe,OAAO,CAAC,CAAC;YACtB,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC,UAAoB,MAAM,GAAG,CAAC;QACjE;QACA,mBAAmB,OAAO,CAAC,CAAC,UAAoB,MAAM,GAAG,CAAC;QAC1D,OAAO;eAAI;SAAM,CAAC,IAAI;IACxB,GAAG;QAAC;KAAe;IAEnB,6EAA6E;IAC7E,gGAAgG;IAChG,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,IAAI,cAAc;eAAI;SAAe;QACrC,IAAI,sBAAsB,OAAO;YAC/B,cAAc,YAAY,MAAM,CAC9B,CAAA,SAAU,OAAO,OAAO,SAAS,MAAM;QAE3C;QACA,IAAI,wBAAwB,OAAO;YACjC,cAAc,YAAY,MAAM,CAAC,CAAA,SAC/B,OAAO,gBAAgB,CAAC,QAAQ,CAAC;QAErC;QACA,IAAI,qBAAqB;YACvB,MAAM,kBAAkB,oBAAoB,WAAW;YACvD,cAAc,YAAY,MAAM,CAC9B,CAAA,SACE,GAAG,OAAO,WAAW,CAAC,CAAC,EAAE,OAAO,YAAY,EAAE,CAC3C,WAAW,GACX,QAAQ,CAAC,oBACZ,OAAO,gBAAgB,CACpB,IAAI,CAAC,KACL,WAAW,GACX,QAAQ,CAAC,oBACZ,OAAO,KAAK,EAAE,cAAc,SAAS,oBACrC,OAAO,YAAY,EAAE,cAAc,SAAS,oBAC5C,OAAO,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAE1C;QACA,OAAO;IACT,GAAG;QACD;QACA;QACA;QACA;KACD;IAED,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,OAAO,oBAAoB,YAAY;QAC3C,IAAI,OAAO,0BAA0B,YAAY;IACnD,GAAG;QAAC;QAAiB;KAAsB;IAE3C,MAAM,gBAAgB,eAAe,uBAAuB;IAE5D,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,MAAM,SAAS,EAAE;QACjB,IAAI,eACF,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,AAAC,cAAwB,OAAO,EAAE;QAC7D,IAAI,cACF,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,AAAC,aAAuB,OAAO,EAAE;QACnE,OAAO,OAAO,MAAM,GAAG,IAAI,OAAO,IAAI,CAAC,QAAQ;IACjD,GAAG;QAAC;QAAe;KAAa;IAEhC,IAAI,eAAe;QACjB,qBACE,8OAAC,0JAAA,CAAA,UAAa;sBACZ,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,aAAU;wBAAC,MAAM,wMAAA,CAAA,UAAO;wBAAE,OAAM;kCAC/B,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGxB,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAI1B,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;+BALyC;;;;;;;;;;;;;;;;;;;;;IAY3E;IAEA,IAAI,CAAC,eAAe,CAAC,iBAAiB;QACpC,qBACE,8OAAC,0JAAA,CAAA,UAAa;sBACZ,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,aAAU;wBAAC,MAAM,wMAAA,CAAA,UAAO;wBAAE,OAAM;;;;;;kCACjC,8OAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,yCAAyC;IACzC,IACE,CAAC,iBACD,mBACA,gBAAgB,MAAM,KAAK,KAC3B,CAAC,cACD;QACA,MAAM,mBACJ,sBAAsB,SACtB,wBAAwB,SACxB;QAEF,qBACE,8OAAC,0JAAA,CAAA,UAAa;sBACZ,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6IAAA,CAAA,gBAAa;;;;;kCACd,8OAAC,sIAAA,CAAA,aAAU;wBACT,aAAY;wBACZ,MAAM,wMAAA,CAAA,UAAO;wBACb,OAAM;kCAEN,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4IAAA,CAAA,eAAY;gCACX,YAAW;gCACX,OAAO;gCACP,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;0CAE5B,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAY;;;;;;;;;;;;;;;;;;;;;kCAK7B,8OAAC,mIAAA,CAAA,aAAU;wBACT,aACE,mBACI,yHACA;wBAEN,MAAM,wMAAA,CAAA,UAAO;wBACb,eAAe;4BACb,MAAM;4BACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAC5B,OAAO;wBACT;wBACA,OAAM;wBACL,GAAI,oBAAoB;4BACvB,iBAAiB;gCACf,oBAAM,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCACnB,OAAO;gCACP,SAAS;oCACP,qBAAqB;oCACrB,uBAAuB;oCACvB,cAAc;gCAChB;4BACF;wBACF,CAAC;;;;;;;;;;;;;;;;;IAKX;IAEA,qBACE,8OAAC,0JAAA,CAAA,UAAa;kBACZ,cAAA,8OAAC;sDAAc;;8BACb,8OAAC,6IAAA,CAAA,gBAAa;;;;;8BACd,8OAAC,sIAAA,CAAA,aAAU;oBACT,aAAY;oBACZ,MAAM,wMAAA,CAAA,UAAO;oBACb,OAAM;8BAEN,cAAA,8OAAC;kEAAc;;0CACb,8OAAC,4IAAA,CAAA,eAAY;gCACX,YAAW;gCACX,OAAO;gCACP,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;0CAE5B,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAY;;;;;;;;;;;0CAEzB,8OAAC,8IAAA,CAAA,gBAAa;gCACZ,WAAW,gBAAgB,MAAM,GAAG;gCACpC,UAAU,CAAC,uBAAuB,EAChC,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EACtC;gCACF,iBAAgB;gCAChB,YAAW;gCACX,SAAQ;;;;;;;;;;;;;;;;;8BAKd,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,8OAAC,gIAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAU;;;;;;8CAC/B,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;;8CACV,8OAAC;8EAAc;;sDACb,8OAAC;;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAiB;;;;;;8DAChC,8OAAC,kIAAA,CAAA,SAAM;oDACL,cAAW;oDACX,eAAe;oDACf,OAAO;;sEAEP,8OAAC,kIAAA,CAAA,gBAAa;4DAAC,WAAU;4DAAgB,IAAG;sEAC1C,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAM;;;;;;gEACvB,YAAY,GAAG,CAAC,CAAA,wBACf,8OAAC,kIAAA,CAAA,aAAU;wEAAkB,OAAO,OAAO,QAAQ,EAAE;;4EAClD,QAAQ,IAAI;4EAAC;4EAAE,QAAQ,KAAK;4EAAC;4EAAG,QAAQ,IAAI;4EAAC;;uEAD/B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;sDAOnC,8OAAC;;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAiB;;;;;;8DAChC,8OAAC,kIAAA,CAAA,SAAM;oDACL,cAAW;oDACX,eAAe;oDACf,OAAO;;sEAEP,8OAAC,kIAAA,CAAA,gBAAa;4DAAC,WAAU;4DAAgB,IAAG;sEAC1C,cAAA,8OAAC,kIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8EACZ,8OAAC,kIAAA,CAAA,aAAU;oEAAC,OAAM;8EAAM;;;;;;gEACvB,mBAAmB,GAAG,CAAC,CAAA,wBACtB,8OAAC,kIAAA,CAAA,aAAU;wEAAe,OAAO;kFAC9B;uEADc;;;;;;;;;;;;;;;;;;;;;;;sDAOzB,8OAAC;;;8DACC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAiB;;;;;;8DAChC,8OAAC;8FAAc;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DACJ,cAAW;4DACX,WAAU;4DACV,IAAG;4DACH,UAAU,CAAA,IAAK,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC3C,aAAY;4DACZ,MAAK;4DACL,OAAO;;;;;;sEAET,8OAAC,sMAAA,CAAA,SAAM;4DACL,eAAY;4DACZ,WAAU;;;;;;wDAEX,4BACC,8OAAC,kIAAA,CAAA,SAAM;4DACL,cAAW;4DACX,WAAU;4DACV,SAAS,IAAM,cAAc;4DAC7B,MAAK;4DACL,SAAQ;;8EAER,8OAAC,4LAAA,CAAA,IAAC;oEAAC,WAAU;;;;;;8EACb,8OAAC;8GAAe;8EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAOnC,CAAC,sBAAsB,SACtB,wBAAwB,SACxB,UAAU,mBACV,8OAAC;8EAAc;;sDACb,8OAAC;sFAAc;;8DACb,8OAAC;8FAAe;8DAAsB;;;;;;gDACrC,sBAAsB,uBACrB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,WAAU;oDACV,SAAQ;;sEAER,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAEb,YAAY,IAAI,CACd,CAAA,IAAK,OAAO,EAAE,EAAE,MAAM,oBACrB;wDACH;wDAEA,YAAY,IAAI,CACd,CAAA,IAAK,OAAO,EAAE,EAAE,MAAM,oBACrB;;;;;;;gDAIR,wBAAwB,uBACvB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,WAAU;oDACV,SAAQ;;sEAER,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACjB;;;;;;;gDAGJ,4BACC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,WAAU;oDACV,SAAQ;;sEAER,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAW;wDAAE;wDAAW;;;;;;;;;;;;;sDAIhD,8OAAC,kIAAA,CAAA,SAAM;4CACL,cAAW;4CACX,WAAU;4CACV,SAAS;gDACP,qBAAqB;gDACrB,uBAAuB;gDACvB,cAAc;4CAChB;4CACA,MAAK;4CACL,SAAQ;;8DAER,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;8BAQvC,8OAAC;oBAA+B,IAAG;8DAApB;;sCACb,8OAAC,wKAAA,CAAA,UAA2B;sCAC1B,cAAA,8OAAC,2KAAA,CAAA,kCAA+B;gCAC9B,OAAO;gCACP,WAAW,oBAAoB,CAAC;gCAChC,SAAS;gCACT,SAAS;gCACT,iBAAiB;gCACjB,iBAAiB;;;;;;;;;;;sCAIrB,8OAAC;sEAAiB;;8CAChB,8OAAC;;;wCAAE;wCAAsB,IAAI,OAAO,kBAAkB;;;;;;;8CACtD,8OAAC;;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUf", "debugId": null}}]}