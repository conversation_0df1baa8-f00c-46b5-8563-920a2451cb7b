(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5055],{6560:(e,s,t)=>{"use strict";t.d(s,{r:()=>d});var a=t(95155),r=t(50172),l=t(12115),i=t(30285),n=t(54036);let d=l.forwardRef((e,s)=>{let{actionType:t="primary",asChild:l=!1,children:d,className:c,disabled:o,icon:m,isLoading:x=!1,loadingText:h,...p}=e,{className:u,variant:g}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[t];return(0,a.jsx)(i.$,{asChild:l,className:(0,n.cn)(u,c),disabled:x||o,ref:s,variant:g,...p,children:x?(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,a.jsx)(r.A,{className:"mr-2 size-4 animate-spin"}),h||d]}):(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",m&&(0,a.jsx)("span",{className:"mr-2",children:m}),d]})})});d.displayName="ActionButton"},16059:(e,s,t)=>{Promise.resolve().then(t.bind(t,59965))},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>n});var a=t(95155),r=t(74466);t(12115);var l=t(54036);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function n(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(i({variant:t}),s),...r})}},33271:(e,s,t)=>{"use strict";t.d(s,{k:()=>u});var a=t(95155),r=t(18018),l=t(50172),i=t(68718),n=t(15300),d=t(60679),c=t(12115),o=t(6560),m=t(44838),x=t(53712),h=t(54036),p=t(16146);function u(e){let{className:s,csvData:t,enableCsv:u=!1,entityId:g,fileName:f,reportContentId:j,reportType:N,tableId:b}=e,[v,y]=(0,c.useState)(!1),[w,D]=(0,c.useState)(!1),{showFormSuccess:A,showFormError:z}=(0,x.t6)(),C=async()=>{y(!0);try{let e="/api/reports/".concat(N).concat(g?"/".concat(g):""),s=document.createElement("a");s.href=e,s.download="".concat(f,".pdf"),s.target="_blank",document.body.append(s),s.click(),s.remove(),A({successTitle:"PDF Downloaded",successDescription:"Your report is being downloaded as a PDF."})}catch(e){console.error("Error generating PDF:",e),z("PDF download failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{y(!1)}},E=async()=>{if(u){D(!0);try{if((null==t?void 0:t.data)&&t.headers)(0,p.og)(t.data,t.headers,"".concat(f,".csv"));else if(b){let e=(0,p.tL)(b);(0,p.og)(e.data,e.headers,"".concat(f,".csv"))}else throw Error("CSV export requires either `csvData` or a `tableId` to be provided.");A({successTitle:"CSV Downloaded",successDescription:"Your report has been downloaded as a CSV file."})}catch(e){console.error("Error generating CSV:",e),z("CSV generation failed: ".concat(e.message||"Please try again."),{errorTitle:"Download Failed"})}finally{D(!1)}}},_=v||w;return(0,a.jsxs)("div",{className:(0,h.cn)("flex items-center gap-2 no-print",s),children:[(0,a.jsx)(o.r,{actionType:"secondary","aria-label":"Print report",onClick:()=>{void 0!==globalThis.window&&globalThis.print()},size:"icon",title:"Print Report",children:(0,a.jsx)(r.A,{className:"size-4"})}),(0,a.jsxs)(m.rI,{children:[(0,a.jsx)(m.ty,{asChild:!0,children:(0,a.jsx)(o.r,{actionType:"secondary","aria-label":"Download report",disabled:_,size:"icon",title:"Download Report",children:_?(0,a.jsx)(l.A,{className:"size-4 animate-spin"}):(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsxs)(m.SQ,{align:"end",children:[(0,a.jsxs)(m._2,{disabled:v,onClick:C,children:[v?(0,a.jsx)(l.A,{className:"mr-2 size-4 animate-spin"}):(0,a.jsx)(n.A,{className:"mr-2 size-4"}),(0,a.jsx)("span",{children:"Download PDF"})]}),u&&(0,a.jsxs)(m._2,{disabled:w,onClick:E,children:[w?(0,a.jsx)(l.A,{className:"mr-2 size-4 animate-spin"}):(0,a.jsx)(d.A,{className:"mr-2 size-4"}),(0,a.jsx)("span",{children:"Download CSV"})]})]})]})]})}},44838:(e,s,t)=>{"use strict";t.d(s,{SQ:()=>x,_2:()=>h,hO:()=>p,lp:()=>u,mB:()=>g,rI:()=>o,ty:()=>m});var a=t(95155),r=t(12115),l=t(48698),i=t(73158),n=t(10518),d=t(70154),c=t(54036);let o=l.bL,m=l.l9;l.YJ,l.ZL,l.Pb,l.z6,r.forwardRef((e,s)=>{let{className:t,inset:r,children:n,...d}=e;return(0,a.jsxs)(l.ZP,{ref:s,className:(0,c.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",t),...d,children:[n,(0,a.jsx)(i.A,{className:"ml-auto"})]})}).displayName=l.ZP.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.G5,{ref:s,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...r})}).displayName=l.G5.displayName;let x=r.forwardRef((e,s)=>{let{className:t,sideOffset:r=4,...i}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsx)(l.UC,{ref:s,sideOffset:r,className:(0,c.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...i})})});x.displayName=l.UC.displayName;let h=r.forwardRef((e,s)=>{let{className:t,inset:r,...i}=e;return(0,a.jsx)(l.q7,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",t),...i})});h.displayName=l.q7.displayName;let p=r.forwardRef((e,s)=>{let{className:t,children:r,checked:i,...d}=e;return(0,a.jsxs)(l.H_,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...void 0!==i&&{checked:i},...d,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})}),r]})});p.displayName=l.H_.displayName,r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(l.hN,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(d.A,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=l.hN.displayName;let u=r.forwardRef((e,s)=>{let{className:t,inset:r,...i}=e;return(0,a.jsx)(l.JU,{ref:s,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",t),...i})});u.displayName=l.JU.displayName;let g=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...r})});g.displayName=l.wv.displayName},55365:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>d,TN:()=>o,XL:()=>c});var a=t(95155),r=t(74466),l=t(12115),i=t(54036);let n=(0,r.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),d=l.forwardRef((e,s)=>{let{className:t,variant:r,...l}=e;return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:r}),t),ref:s,role:"alert",...l})});d.displayName="Alert";let c=l.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h5",{className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),ref:s,...r})});c.displayName="AlertTitle";let o=l.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),ref:s,...r})});o.displayName="AlertDescription"},59965:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var a=t(95155),r=t(41784),l=t(83343),i=t(58260),n=t(37648),d=t(83662),c=t(66766),o=t(35695),m=t(12115),x=t(33271),h=t(26126),p=t(77023),u=t(17841),g=t(54036),f=t(99673);let j=function(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return"N/A";try{return(0,r.GP)((0,l.H)(e),s?"MMM d, yyyy, HH:mm":"MMM d, yyyy")}catch(e){return"Invalid Date"}},N=e=>{switch(e){case"Cancelled":return"bg-red-100 text-red-800 border-red-300";case"Completed":return"bg-purple-100 text-purple-800 border-purple-300";case"Confirmed":return"bg-green-100 text-green-800 border-green-300";case"In_Progress":return"bg-yellow-100 text-yellow-800 border-yellow-300";case"Planned":return"bg-blue-100 text-blue-800 border-blue-300";default:return"bg-gray-100 text-gray-800 border-gray-300"}};function b(){var e;let s=(0,o.useParams)().id,{data:t,error:r,isError:l,isLoading:b}=(0,u.kA)(s);return((0,m.useEffect)(()=>{t&&(document.title="".concat(t.eventName," - Delegation Report"))},[t]),b)?(0,a.jsxs)("div",{className:"mx-auto max-w-4xl p-4",children:[(0,a.jsx)(p.jt,{count:1,variant:"card"}),(0,a.jsx)(p.jt,{className:"mt-6",count:3,variant:"table"}),(0,a.jsx)(p.jt,{className:"mt-6",count:2,variant:"table"})]}):l||!t?(0,a.jsxs)("div",{className:"py-10 text-center",children:["Error loading delegation report or delegation not found."," ",null==r?void 0:r.message]}):(0,a.jsxs)("div",{className:"mx-auto max-w-4xl bg-white p-2 text-gray-800 sm:p-4",children:[(0,a.jsx)("div",{className:"no-print mb-4 text-right",children:(0,a.jsx)(x.k,{enableCsv:t.delegates&&t.delegates.length>0||t.statusHistory&&t.statusHistory.length>0,fileName:"delegation-report-".concat(t.eventName.replaceAll(/\s+/g,"-")),reportContentId:"#delegation-report-content",tableId:"#delegates-table"})}),(0,a.jsxs)("div",{className:"report-content",id:"delegation-report-content",children:[(0,a.jsxs)("header",{className:"mb-8 border-b-2 border-gray-300 pb-4 text-center",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"Delegation Report"}),(0,a.jsx)("p",{className:"text-xl text-gray-600",children:t.eventName}),(0,a.jsxs)(h.E,{className:(0,g.cn)("mt-2 text-sm py-1 px-3 font-semibold",N(t.status)),children:["Status: ",(0,f.fZ)(t.status)]})]}),(0,a.jsxs)("section",{className:"card-print mb-6 rounded border border-gray-200 p-4",children:[(0,a.jsx)("h2",{className:"mb-3 border-b border-gray-200 pb-2 text-xl font-semibold text-gray-700",children:"Delegation Summary"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-x-4 gap-y-2 text-sm md:grid-cols-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Event Name:"})," ",t.eventName]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Location:"})," ",t.location]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Duration:"})," ",j(t.durationFrom)," ","to ",j(t.durationTo)]}),t.invitationFrom&&(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Invitation From:"})," ",t.invitationFrom]}),t.invitationTo&&(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Invitation To:"})," ",t.invitationTo]})]}),t.imageUrl&&(0,a.jsx)("div",{className:"no-print relative mx-auto mt-4 aspect-[16/9] w-full max-w-md overflow-hidden rounded",children:(0,a.jsx)(c.default,{alt:t.eventName,"data-ai-hint":"event placeholder",layout:"fill",objectFit:"contain",src:t.imageUrl})}),t.notes&&(0,a.jsxs)("div",{className:"mt-3 text-sm",children:[(0,a.jsx)("strong",{children:"Notes:"})," ",(0,a.jsx)("span",{className:"italic",children:t.notes})]})]}),(0,a.jsxs)("section",{className:"card-print mb-6 rounded border border-gray-200 p-4",children:[(0,a.jsxs)("h2",{className:"mb-3 border-b border-gray-200 pb-2 text-xl font-semibold text-gray-700",children:["Delegates (",(null==(e=t.delegates)?void 0:e.length)||0,")"]}),t.delegates&&t.delegates.length>0?(0,a.jsxs)("table",{className:"w-full text-left text-sm text-gray-600",id:"delegates-table",children:[(0,a.jsx)("thead",{className:"bg-gray-50 text-xs uppercase text-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Name"}),(0,a.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Title"}),(0,a.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Notes"})]})}),(0,a.jsx)("tbody",{children:t.delegates.map(e=>(0,a.jsxs)("tr",{className:"border-b bg-white hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-3 py-2 font-medium",children:e.name}),(0,a.jsx)("td",{className:"px-3 py-2",children:e.title}),(0,a.jsx)("td",{className:"px-3 py-2",children:e.notes||"-"})]},e.id))})]}):(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No delegates listed."})]}),(t.flightArrivalDetails||t.flightDepartureDetails)&&(0,a.jsxs)("section",{className:"card-print mb-6 rounded border border-gray-200 p-4",children:[(0,a.jsx)("h2",{className:"mb-3 border-b border-gray-200 pb-2 text-xl font-semibold text-gray-700",children:"Flight Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 text-sm md:grid-cols-2",children:[t.flightArrivalDetails&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-md mb-1 font-semibold",children:"Arrival Details"}),(0,a.jsxs)("p",{children:[(0,a.jsx)(i.A,{className:"mr-1 inline size-4"}),(0,a.jsx)("strong",{children:"Flight:"})," ",t.flightArrivalDetails.flightNumber]}),(0,a.jsxs)("p",{children:[(0,a.jsx)(n.A,{className:"mr-1 inline size-4"}),(0,a.jsx)("strong",{children:"Time:"})," ",j(t.flightArrivalDetails.dateTime,!0)]}),(0,a.jsxs)("p",{children:[(0,a.jsx)(d.A,{className:"mr-1 inline size-4"}),(0,a.jsx)("strong",{children:"Airport:"})," ",t.flightArrivalDetails.airport," ",t.flightArrivalDetails.terminal&&"(Terminal ".concat(t.flightArrivalDetails.terminal,")")]}),t.flightArrivalDetails.notes&&(0,a.jsxs)("p",{className:"mt-1 text-xs italic",children:[(0,a.jsx)("strong",{children:"Notes:"})," ",t.flightArrivalDetails.notes]})]}),t.flightDepartureDetails&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-md mb-1 font-semibold",children:"Departure Details"}),(0,a.jsxs)("p",{children:[(0,a.jsx)(i.A,{className:"mr-1 inline size-4"}),(0,a.jsx)("strong",{children:"Flight:"})," ",t.flightDepartureDetails.flightNumber]}),(0,a.jsxs)("p",{children:[(0,a.jsx)(n.A,{className:"mr-1 inline size-4"}),(0,a.jsx)("strong",{children:"Time:"})," ",j(t.flightDepartureDetails.dateTime,!0)]}),(0,a.jsxs)("p",{children:[(0,a.jsx)(d.A,{className:"mr-1 inline size-4"}),(0,a.jsx)("strong",{children:"Airport:"})," ",t.flightDepartureDetails.airport," ",t.flightDepartureDetails.terminal&&"(Terminal ".concat(t.flightDepartureDetails.terminal,")")]}),t.flightDepartureDetails.notes&&(0,a.jsxs)("p",{className:"mt-1 text-xs italic",children:[(0,a.jsx)("strong",{children:"Notes:"})," ",t.flightDepartureDetails.notes]})]})]}),!t.flightArrivalDetails&&!t.flightDepartureDetails&&(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No flight details logged."})]}),(0,a.jsxs)("section",{className:"card-print rounded border border-gray-200 p-4",children:[(0,a.jsx)("h2",{className:"mb-3 border-b border-gray-200 pb-2 text-xl font-semibold text-gray-700",children:"Status History"}),t.statusHistory&&t.statusHistory.length>0?(0,a.jsxs)("table",{className:"w-full text-left text-sm text-gray-600",id:"status-history-table",children:[(0,a.jsx)("thead",{className:"bg-gray-50 text-xs uppercase text-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Status"}),(0,a.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Changed At"}),(0,a.jsx)("th",{className:"px-3 py-2",scope:"col",children:"Reason"})]})}),(0,a.jsx)("tbody",{children:[...t.statusHistory].sort((e,s)=>new Date(s.changedAt).getTime()-new Date(e.changedAt).getTime()).map(e=>(0,a.jsxs)("tr",{className:"border-b bg-white hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-3 py-2",children:(0,a.jsx)(h.E,{className:(0,g.cn)("text-xs py-0.5 px-1.5",N(e.status)),children:(0,f.fZ)(e.status)})}),(0,a.jsx)("td",{className:"px-3 py-2",children:j(e.changedAt,!0)}),(0,a.jsx)("td",{className:"px-3 py-2",children:e.reason||"-"})]},e.id))})]}):(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"No status history available."})]}),(0,a.jsxs)("footer",{className:"mt-10 border-t-2 border-gray-300 pt-4 text-center text-xs text-gray-500",children:[(0,a.jsxs)("p",{children:["Report generated on: ",new Date().toLocaleDateString()]}),(0,a.jsx)("p",{children:"WorkHub - Delegation Management"})]})]})]})}},68856:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(95155),r=t(54036);function l(e){let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,r.cn)("animate-pulse rounded-md bg-muted",s),...t})}},77023:(e,s,t)=>{"use strict";t.d(s,{gO:()=>x,jt:()=>g,pp:()=>h});var a=t(95155),r=t(11133),l=t(50172);t(12115);var i=t(6560),n=t(55365),d=t(68856),c=t(54036);let o={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},m={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function x(e){let{children:s,className:t,data:r,emptyComponent:l,error:i,errorComponent:n,isLoading:d,loadingComponent:o,onRetry:m}=e;return d?o||(0,a.jsx)(u,{...t&&{className:t},text:"Loading..."}):i?n||(0,a.jsx)(p,{...t&&{className:t},message:i,...m&&{onRetry:m}}):!r||Array.isArray(r)&&0===r.length?l||(0,a.jsx)("div",{className:(0,c.cn)("text-center py-8",t),children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,a.jsx)("div",{className:t,children:s(r)})}function h(e){let{className:s,description:t,icon:r,primaryAction:l,secondaryAction:n,title:d}=e;return(0,a.jsxs)("div",{className:(0,c.cn)("space-y-6 text-center py-12",s),children:[r&&(0,a.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,a.jsx)(r,{className:"h-10 w-10 text-muted-foreground"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:d}),t&&(0,a.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:t})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[l&&(0,a.jsx)(i.r,{actionType:"primary",asChild:!!l.href,icon:l.icon,onClick:l.onClick,children:l.href?(0,a.jsx)("a",{href:l.href,children:l.label}):l.label}),n&&(0,a.jsx)(i.r,{actionType:"tertiary",asChild:!!n.href,icon:n.icon,onClick:n.onClick,children:n.href?(0,a.jsx)("a",{href:n.href,children:n.label}):n.label})]})]})}function p(e){let{className:s,message:t,onRetry:d}=e;return(0,a.jsxs)(n.Fc,{className:(0,c.cn)("my-4",s),variant:"destructive",children:[(0,a.jsx)(r.A,{className:"size-4"}),(0,a.jsx)(n.XL,{children:"Error"}),(0,a.jsx)(n.TN,{children:(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:t}),d&&(0,a.jsx)(i.r,{actionType:"tertiary",icon:(0,a.jsx)(l.A,{className:"size-4"}),onClick:d,size:"sm",children:"Try Again"})]})})]})}function u(e){let{className:s,fullPage:t=!1,size:r="md",text:i}=e;return(0,a.jsx)("div",{className:(0,c.cn)("flex items-center justify-center",t&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",s),children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(l.A,{className:(0,c.cn)("animate-spin text-primary",o[r])}),i&&(0,a.jsx)("span",{className:(0,c.cn)("mt-2 text-muted-foreground",m[r]),children:i})]})})}function g(e){let{className:s,count:t=1,testId:r="loading-skeleton",variant:l="default"}=e;return"card"===l?(0,a.jsx)("div",{className:(0,c.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",s),"data-testid":r,children:Array(t).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,a.jsx)(d.E,{className:"aspect-[16/10] w-full"}),(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsx)(d.E,{className:"mb-1 h-7 w-3/4"}),(0,a.jsx)(d.E,{className:"mb-3 h-4 w-1/2"}),(0,a.jsx)(d.E,{className:"my-3 h-px w-full"}),(0,a.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.E,{className:"mr-2.5 size-5 rounded-full"}),(0,a.jsx)(d.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===l?(0,a.jsxs)("div",{className:(0,c.cn)("space-y-3",s),"data-testid":r,children:[(0,a.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,a.jsx)(d.E,{className:"h-8 flex-1"},s))}),Array(t).fill(0).map((e,s)=>(0,a.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,a.jsx)(d.E,{className:"h-6 flex-1"},s))},s))]}):"list"===l?(0,a.jsx)("div",{className:(0,c.cn)("space-y-3",s),"data-testid":r,children:Array(t).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(d.E,{className:"size-12 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)(d.E,{className:"h-4 w-1/3"}),(0,a.jsx)(d.E,{className:"h-4 w-full"})]})]},s))}):"stats"===l?(0,a.jsx)("div",{className:(0,c.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",s),"data-testid":r,children:Array(t).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(d.E,{className:"h-5 w-1/3"}),(0,a.jsx)(d.E,{className:"size-5 rounded-full"})]}),(0,a.jsx)(d.E,{className:"mt-3 h-8 w-1/2"}),(0,a.jsx)(d.E,{className:"mt-2 h-4 w-2/3"})]},s))}):(0,a.jsx)("div",{className:(0,c.cn)("space-y-2",s),"data-testid":r,children:Array(t).fill(0).map((e,s)=>(0,a.jsx)(d.E,{className:"h-5 w-full"},s))})}},99673:(e,s,t)=>{"use strict";function a(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function r(e){var s,t;if(null==(s=e.fullName)?void 0:s.trim())return e.fullName.trim();if(null==(t=e.name)?void 0:t.trim()){let s=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(s.toLowerCase())||s.includes("_")){let e=s.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(e," (Role)")}return s}if(e.role){let s=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(s," (Role)")}return"Unknown Employee"}function l(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function i(e){return e.replaceAll("_"," ")}t.d(s,{DV:()=>r,fZ:()=>a,s:()=>l,vq:()=>i})}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,1137,3860,9664,1263,5495,5247,6766,703,4036,8658,111,3712,7841,8441,1684,7358],()=>s(16059)),_N_E=e.O()}]);