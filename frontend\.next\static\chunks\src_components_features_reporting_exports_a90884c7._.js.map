{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/exports/pdf/DelegationReportDocument.tsx"], "sourcesContent": ["import {\r\n  Document,\r\n  Font,\r\n  Page,\r\n  StyleSheet,\r\n  Text,\r\n  View,\r\n} from '@react-pdf/renderer';\r\n// frontend/src/components/features/reporting/exports/pdf/DelegationReportDocument.tsx\r\nimport React from 'react';\r\n\r\nimport type { ReportDocumentProps } from '../../data/types/export';\r\n\r\n// Register a font if needed (e.g., for emojis or specific styling)\r\n// Font.register({ family: 'Roboto', src: '/fonts/Roboto-Regular.ttf' });\r\n\r\nconst styles = StyleSheet.create({\r\n  header: {\r\n    color: '#333',\r\n    fontSize: 24,\r\n    marginBottom: 10,\r\n    textAlign: 'center',\r\n  },\r\n  page: {\r\n    backgroundColor: '#E4E4E4',\r\n    flexDirection: 'column',\r\n    padding: 30,\r\n  },\r\n  section: {\r\n    flexGrow: 1,\r\n    margin: 10,\r\n    padding: 10,\r\n  },\r\n  subheader: {\r\n    color: '#555',\r\n    fontSize: 16,\r\n    marginBottom: 5,\r\n  },\r\n  text: {\r\n    fontSize: 12,\r\n    marginBottom: 3,\r\n  },\r\n  // Add more styles for tables, charts, etc.\r\n});\r\n\r\n/**\r\n * @component DelegationReportDocument\r\n * @description React-PDF component for generating the Delegation Analytics Report.\r\n * Adheres to SRP by focusing solely on the PDF document structure and rendering.\r\n * @param {ReportDocumentProps} props - The data and metadata for the report.\r\n */\r\nexport const DelegationReportDocument: React.FC<ReportDocumentProps> = ({\r\n  data,\r\n  filters,\r\n  reportDate,\r\n  reportTitle,\r\n}) => {\r\n  // Defensive programming for robust data handling\r\n  const { metadata, reportData, summary, totalCount } = React.useMemo(() => {\r\n    // Handle case where data might be null/undefined\r\n    if (!data) {\r\n      return {\r\n        metadata: {},\r\n        reportData: {},\r\n        summary: { message: 'No data available' },\r\n        totalCount: 0,\r\n      };\r\n    }\r\n\r\n    const rawData = data?.data ?? data ?? {};\r\n    const meta = data?.metadata ?? {};\r\n\r\n    // Ensure rawData is an object before destructuring\r\n    const safeData =\r\n      typeof rawData === 'object' && rawData !== null && !Array.isArray(rawData)\r\n        ? rawData\r\n        : {};\r\n\r\n    return {\r\n      metadata: meta,\r\n      reportData: safeData,\r\n      summary: safeData.summary ?? { message: 'No summary available' },\r\n      totalCount:\r\n        safeData.totalCount ?? (Array.isArray(safeData) ? safeData.length : 0),\r\n    };\r\n  }, [data]);\r\n\r\n  // Ensure filters is always an object with proper typing\r\n  const safeFilters = filters ?? {};\r\n  const safeDateRange = (safeFilters as any)?.dateRange ?? {};\r\n  const safeStatus = (safeFilters as any)?.status;\r\n  const safeReportTitle = reportTitle ?? 'Delegation Report';\r\n  const safeReportDate = reportDate ?? new Date().toLocaleString();\r\n\r\n  return (\r\n    <Document>\r\n      <Page size=\"A4\" style={styles.page}>\r\n        <View style={styles.section}>\r\n          <Text style={styles.header}>{safeReportTitle}</Text>\r\n          <Text style={styles.text}>Report Generated: {safeReportDate}</Text>\r\n          <Text style={styles.text}>Total Delegations: {totalCount}</Text>\r\n\r\n          {/* Summary Section */}\r\n          {summary && Object.keys(summary).length > 0 && (\r\n            <View style={{ marginTop: 15 }}>\r\n              <Text style={styles.subheader}>Summary:</Text>\r\n              {Object.entries(summary).map(([key, value]) => (\r\n                <Text key={key} style={styles.text}>\r\n                  {key\r\n                    .replaceAll(/([A-Z])/g, ' $1')\r\n                    .replace(/^./, str => str.toUpperCase())}\r\n                  : {String(value ?? 'N/A')}\r\n                </Text>\r\n              ))}\r\n            </View>\r\n          )}\r\n\r\n          {/* Filters Section */}\r\n          {safeFilters && Object.keys(safeFilters).length > 0 && (\r\n            <View style={{ marginTop: 20 }}>\r\n              <Text style={styles.subheader}>Filters Applied:</Text>\r\n              <Text style={styles.text}>\r\n                Date Range:{' '}\r\n                {safeDateRange.from?.toLocaleDateString?.() ?? 'N/A'} -{' '}\r\n                {safeDateRange.to?.toLocaleDateString?.() ?? 'N/A'}\r\n                <Text style={styles.text}>\r\n                  Status: {safeStatus?.join?.(', ') ?? 'All'}\r\n                </Text>\r\n              </Text>\r\n            </View>\r\n          )}\r\n\r\n          {/* Metadata Section */}\r\n          {metadata.id && (\r\n            <View style={{ marginTop: 15 }}>\r\n              <Text style={styles.subheader}>Report Details:</Text>\r\n              <Text style={styles.text}>Report ID: {metadata.id}</Text>\r\n              <Text style={styles.text}>\r\n                Generated At:{' '}\r\n                {metadata.generatedAt\r\n                  ? new Date(metadata.generatedAt).toLocaleString()\r\n                  : 'N/A'}\r\n              </Text>\r\n            </View>\r\n          )}\r\n        </View>\r\n      </Page>\r\n    </Document>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAQA,sFAAsF;AACtF;;;;;AAIA,mEAAmE;AACnE,yEAAyE;AAEzE,MAAM,SAAS,+LAAA,CAAA,aAAU,CAAC,MAAM,CAAC;IAC/B,QAAQ;QACN,OAAO;QACP,UAAU;QACV,cAAc;QACd,WAAW;IACb;IACA,MAAM;QACJ,iBAAiB;QACjB,eAAe;QACf,SAAS;IACX;IACA,SAAS;QACP,UAAU;QACV,QAAQ;QACR,SAAS;IACX;IACA,WAAW;QACT,OAAO;QACP,UAAU;QACV,cAAc;IAChB;IACA,MAAM;QACJ,UAAU;QACV,cAAc;IAChB;AAEF;AAQO,MAAM,2BAA0D,CAAC,EACtE,IAAI,EACJ,OAAO,EACP,UAAU,EACV,WAAW,EACZ;;IACC,iDAAiD;IACjD,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,6JAAA,CAAA,UAAK,CAAC,OAAO;4CAAC;YAClE,iDAAiD;YACjD,IAAI,CAAC,MAAM;gBACT,OAAO;oBACL,UAAU,CAAC;oBACX,YAAY,CAAC;oBACb,SAAS;wBAAE,SAAS;oBAAoB;oBACxC,YAAY;gBACd;YACF;YAEA,MAAM,UAAU,MAAM,QAAQ,QAAQ,CAAC;YACvC,MAAM,OAAO,MAAM,YAAY,CAAC;YAEhC,mDAAmD;YACnD,MAAM,WACJ,OAAO,YAAY,YAAY,YAAY,QAAQ,CAAC,MAAM,OAAO,CAAC,WAC9D,UACA,CAAC;YAEP,OAAO;gBACL,UAAU;gBACV,YAAY;gBACZ,SAAS,SAAS,OAAO,IAAI;oBAAE,SAAS;gBAAuB;gBAC/D,YACE,SAAS,UAAU,IAAI,CAAC,MAAM,OAAO,CAAC,YAAY,SAAS,MAAM,GAAG,CAAC;YACzE;QACF;2CAAG;QAAC;KAAK;IAET,wDAAwD;IACxD,MAAM,cAAc,WAAW,CAAC;IAChC,MAAM,gBAAgB,AAAC,aAAqB,aAAa,CAAC;IAC1D,MAAM,aAAc,aAAqB;IACzC,MAAM,kBAAkB,eAAe;IACvC,MAAM,iBAAiB,cAAc,IAAI,OAAO,cAAc;IAE9D,qBACE,6LAAC,+JAAA,CAAA,WAAQ;kBACP,cAAA,6LAAC,+JAAA,CAAA,OAAI;YAAC,MAAK;YAAK,OAAO,OAAO,IAAI;sBAChC,cAAA,6LAAC,+JAAA,CAAA,OAAI;gBAAC,OAAO,OAAO,OAAO;;kCACzB,6LAAC,+JAAA,CAAA,OAAI;wBAAC,OAAO,OAAO,MAAM;kCAAG;;;;;;kCAC7B,6LAAC,+JAAA,CAAA,OAAI;wBAAC,OAAO,OAAO,IAAI;;4BAAE;4BAAmB;;;;;;;kCAC7C,6LAAC,+JAAA,CAAA,OAAI;wBAAC,OAAO,OAAO,IAAI;;4BAAE;4BAAoB;;;;;;;oBAG7C,WAAW,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,mBACxC,6LAAC,+JAAA,CAAA,OAAI;wBAAC,OAAO;4BAAE,WAAW;wBAAG;;0CAC3B,6LAAC,+JAAA,CAAA,OAAI;gCAAC,OAAO,OAAO,SAAS;0CAAE;;;;;;4BAC9B,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACxC,6LAAC,+JAAA,CAAA,OAAI;oCAAW,OAAO,OAAO,IAAI;;wCAC/B,IACE,UAAU,CAAC,YAAY,OACvB,OAAO,CAAC,MAAM,CAAA,MAAO,IAAI,WAAW;wCAAI;wCACxC,OAAO,SAAS;;mCAJV;;;;;;;;;;;oBAWhB,eAAe,OAAO,IAAI,CAAC,aAAa,MAAM,GAAG,mBAChD,6LAAC,+JAAA,CAAA,OAAI;wBAAC,OAAO;4BAAE,WAAW;wBAAG;;0CAC3B,6LAAC,+JAAA,CAAA,OAAI;gCAAC,OAAO,OAAO,SAAS;0CAAE;;;;;;0CAC/B,6LAAC,+JAAA,CAAA,OAAI;gCAAC,OAAO,OAAO,IAAI;;oCAAE;oCACZ;oCACX,cAAc,IAAI,EAAE,0BAA0B;oCAAM;oCAAG;oCACvD,cAAc,EAAE,EAAE,0BAA0B;kDAC7C,6LAAC,+JAAA,CAAA,OAAI;wCAAC,OAAO,OAAO,IAAI;;4CAAE;4CACf,YAAY,OAAO,SAAS;;;;;;;;;;;;;;;;;;;oBAO5C,SAAS,EAAE,kBACV,6LAAC,+JAAA,CAAA,OAAI;wBAAC,OAAO;4BAAE,WAAW;wBAAG;;0CAC3B,6LAAC,+JAAA,CAAA,OAAI;gCAAC,OAAO,OAAO,SAAS;0CAAE;;;;;;0CAC/B,6LAAC,+JAAA,CAAA,OAAI;gCAAC,OAAO,OAAO,IAAI;;oCAAE;oCAAY,SAAS,EAAE;;;;;;;0CACjD,6LAAC,+JAAA,CAAA,OAAI;gCAAC,OAAO,OAAO,IAAI;;oCAAE;oCACV;oCACb,SAAS,WAAW,GACjB,IAAI,KAAK,SAAS,WAAW,EAAE,cAAc,KAC7C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;GAlGa;KAAA", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/exports/pdf/EmployeeReportDocument.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/exports/pdf/EmployeeReportDocument.tsx\r\nimport React from 'react';\r\nimport { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';\r\n\r\nconst styles = StyleSheet.create({\r\n  page: {\r\n    flexDirection: 'column',\r\n    backgroundColor: '#FFFFFF',\r\n    padding: 30,\r\n    fontSize: 12,\r\n  },\r\n  header: {\r\n    fontSize: 20,\r\n    marginBottom: 20,\r\n    textAlign: 'center',\r\n    color: '#7c3aed',\r\n    fontWeight: 'bold',\r\n  },\r\n  subheader: {\r\n    fontSize: 16,\r\n    marginBottom: 15,\r\n    color: '#374151',\r\n    fontWeight: 'bold',\r\n    borderBottom: '1px solid #e5e7eb',\r\n    paddingBottom: 5,\r\n  },\r\n  section: {\r\n    marginBottom: 20,\r\n  },\r\n  row: {\r\n    flexDirection: 'row',\r\n    marginBottom: 8,\r\n    paddingVertical: 4,\r\n  },\r\n  label: {\r\n    width: '40%',\r\n    fontWeight: 'bold',\r\n    color: '#4b5563',\r\n  },\r\n  value: {\r\n    width: '60%',\r\n    color: '#111827',\r\n  },\r\n  table: {\r\n    marginTop: 10,\r\n  },\r\n  tableHeader: {\r\n    flexDirection: 'row',\r\n    backgroundColor: '#faf5ff',\r\n    padding: 8,\r\n    fontWeight: 'bold',\r\n  },\r\n  tableRow: {\r\n    flexDirection: 'row',\r\n    padding: 8,\r\n    borderBottom: '1px solid #e5e7eb',\r\n  },\r\n  tableCell: {\r\n    flex: 1,\r\n    fontSize: 10,\r\n  },\r\n  metadata: {\r\n    marginTop: 30,\r\n    padding: 15,\r\n    backgroundColor: '#faf5ff',\r\n    borderRadius: 5,\r\n  },\r\n  metadataText: {\r\n    fontSize: 10,\r\n    color: '#6b7280',\r\n    marginBottom: 3,\r\n  },\r\n});\r\n\r\ninterface EmployeeReportDocumentProps {\r\n  data: any;\r\n  reportTitle: string;\r\n  metadata?: any;\r\n}\r\n\r\nexport const EmployeeReportDocument: React.FC<EmployeeReportDocumentProps> = ({\r\n  data,\r\n  reportTitle,\r\n  metadata,\r\n}) => {\r\n  // Defensive programming: ensure all data is properly defined\r\n  const employeeData = React.useMemo(() => {\r\n    if (!data) return {};\r\n    const rawData = data?.data || data;\r\n    if (!rawData || typeof rawData !== 'object') return {};\r\n\r\n    return {\r\n      totalCount: rawData.totalCount || 0,\r\n      activeCount: rawData.activeCount || 0,\r\n      onLeaveCount: rawData.onLeaveCount || 0,\r\n      averagePerformanceScore: rawData.averagePerformanceScore || 0,\r\n      satisfactionRate: rawData.satisfactionRate || 0,\r\n      performanceMetrics: rawData.performanceMetrics || {},\r\n      departmentDistribution: Array.isArray(rawData.departmentDistribution) ? rawData.departmentDistribution : [],\r\n      taskAssignments: rawData.taskAssignments || {},\r\n      workloadDistribution: Array.isArray(rawData.workloadDistribution) ? rawData.workloadDistribution : [],\r\n      availabilityMetrics: rawData.availabilityMetrics || {},\r\n      ...rawData,\r\n    };\r\n  }, [data]);\r\n  \r\n  const safeReportTitle = reportTitle || 'Employee Report';\r\n  const safeMetadata = metadata || {};\r\n\r\n  return (\r\n    <Document>\r\n      <Page size=\"A4\" style={styles.page}>\r\n        <Text style={styles.header}>{safeReportTitle}</Text>\r\n\r\n        {/* Employee Summary */}\r\n        <View style={styles.section}>\r\n          <Text style={styles.subheader}>Employee Summary</Text>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Total Employees:</Text>\r\n            <Text style={styles.value}>{employeeData.totalCount || 0}</Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Active Employees:</Text>\r\n            <Text style={styles.value}>{employeeData.activeCount || 0}</Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>On Leave:</Text>\r\n            <Text style={styles.value}>{employeeData.onLeaveCount || 0}</Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Average Performance Score:</Text>\r\n            <Text style={styles.value}>\r\n              {employeeData.averagePerformanceScore?.toFixed(2) || 0}\r\n            </Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Employee Satisfaction Rate:</Text>\r\n            <Text style={styles.value}>\r\n              {employeeData.satisfactionRate?.toFixed(2) || 0}%\r\n            </Text>\r\n          </View>\r\n        </View>\r\n\r\n        {/* Performance Metrics */}\r\n        {employeeData.performanceMetrics && (\r\n          <View style={styles.section}>\r\n            <Text style={styles.subheader}>Performance Metrics</Text>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>High Performers:</Text>\r\n              <Text style={styles.value}>\r\n                {employeeData.performanceMetrics.highPerformers || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Average Performers:</Text>\r\n              <Text style={styles.value}>\r\n                {employeeData.performanceMetrics.averagePerformers || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Low Performers:</Text>\r\n              <Text style={styles.value}>\r\n                {employeeData.performanceMetrics.lowPerformers || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Performance Improvement Rate:</Text>\r\n              <Text style={styles.value}>\r\n                {employeeData.performanceMetrics.improvementRate?.toFixed(2) ||\r\n                  0}\r\n                %\r\n              </Text>\r\n            </View>\r\n          </View>\r\n        )}\r\n\r\n        {/* Department Distribution */}\r\n        {employeeData.departmentDistribution && (\r\n          <View style={styles.section}>\r\n            <Text style={styles.subheader}>Department Distribution</Text>\r\n            <View style={styles.table}>\r\n              <View style={styles.tableHeader}>\r\n                <Text style={styles.tableCell}>Department</Text>\r\n                <Text style={styles.tableCell}>Count</Text>\r\n                <Text style={styles.tableCell}>Percentage</Text>\r\n              </View>\r\n              {employeeData.departmentDistribution.map(\r\n                (item: any, index: number) => (\r\n                  <View key={`dept-${index}`} style={styles.tableRow}>\r\n                    <Text style={styles.tableCell}>\r\n                      {item?.department || item?._id || 'Unknown'}\r\n                    </Text>\r\n                    <Text style={styles.tableCell}>\r\n                      {item?.count || item?._count?.department || 0}\r\n                    </Text>\r\n                    <Text style={styles.tableCell}>\r\n                      {item?.percentage\r\n                        ? `${Number(item.percentage).toFixed(1)}%`\r\n                        : 'N/A'}\r\n                    </Text>\r\n                  </View>\r\n                )\r\n              )}\r\n            </View>\r\n          </View>\r\n        )}\r\n\r\n        {/* Task Assignments */}\r\n        {employeeData.taskAssignments && (\r\n          <View style={styles.section}>\r\n            <Text style={styles.subheader}>Task Assignment Metrics</Text>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Total Tasks Assigned:</Text>\r\n              <Text style={styles.value}>\r\n                {employeeData.taskAssignments.totalAssigned || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Completed Tasks:</Text>\r\n              <Text style={styles.value}>\r\n                {employeeData.taskAssignments.completed || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Pending Tasks:</Text>\r\n              <Text style={styles.value}>\r\n                {employeeData.taskAssignments.pending || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Task Completion Rate:</Text>\r\n              <Text style={styles.value}>\r\n                {employeeData.taskAssignments.completionRate?.toFixed(2) || 0}%\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Average Tasks per Employee:</Text>\r\n              <Text style={styles.value}>\r\n                {employeeData.taskAssignments.averagePerEmployee?.toFixed(1) ||\r\n                  0}\r\n              </Text>\r\n            </View>\r\n          </View>\r\n        )}\r\n\r\n        {/* Workload Distribution */}\r\n        {employeeData.workloadDistribution && (\r\n          <View style={styles.section}>\r\n            <Text style={styles.subheader}>Workload Distribution</Text>\r\n            <View style={styles.table}>\r\n              <View style={styles.tableHeader}>\r\n                <Text style={styles.tableCell}>Workload Level</Text>\r\n                <Text style={styles.tableCell}>Employee Count</Text>\r\n                <Text style={styles.tableCell}>Percentage</Text>\r\n              </View>\r\n              {employeeData.workloadDistribution.map(\r\n                (item: any, index: number) => (\r\n                  <View key={`workload-${index}`} style={styles.tableRow}>\r\n                    <Text style={styles.tableCell}>\r\n                      {item?.level || item?._id || 'Unknown'}\r\n                    </Text>\r\n                    <Text style={styles.tableCell}>\r\n                      {item?.count || item?._count?.level || 0}\r\n                    </Text>\r\n                    <Text style={styles.tableCell}>\r\n                      {item?.percentage\r\n                        ? `${Number(item.percentage).toFixed(1)}%`\r\n                        : 'N/A'}\r\n                    </Text>\r\n                  </View>\r\n                )\r\n              )}\r\n            </View>\r\n          </View>\r\n        )}\r\n\r\n        {/* Availability Metrics */}\r\n        {employeeData.availabilityMetrics && (\r\n          <View style={styles.section}>\r\n            <Text style={styles.subheader}>Availability Metrics</Text>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Available Employees:</Text>\r\n              <Text style={styles.value}>\r\n                {employeeData.availabilityMetrics.available || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>On Assignment:</Text>\r\n              <Text style={styles.value}>\r\n                {employeeData.availabilityMetrics.onAssignment || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>On Leave:</Text>\r\n              <Text style={styles.value}>\r\n                {employeeData.availabilityMetrics.onLeave || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Availability Rate:</Text>\r\n              <Text style={styles.value}>\r\n                {employeeData.availabilityMetrics.availabilityRate?.toFixed(\r\n                  2\r\n                ) || 0}\r\n                %\r\n              </Text>\r\n            </View>\r\n          </View>\r\n        )}\r\n\r\n        {/* Report Metadata */}\r\n        {metadata && (\r\n          <View style={styles.metadata}>\r\n            <Text style={styles.subheader}>Report Information</Text>\r\n            <Text style={styles.metadataText}>Report ID: {metadata.id}</Text>\r\n            <Text style={styles.metadataText}>Type: {metadata.type}</Text>\r\n            <Text style={styles.metadataText}>\r\n              Entity Type: {metadata.entityType}\r\n            </Text>\r\n            <Text style={styles.metadataText}>\r\n              Generated: {new Date(metadata.generatedAt).toLocaleString()}\r\n            </Text>\r\n            <Text style={styles.metadataText}>\r\n              Generated By: {metadata.generatedBy}\r\n            </Text>\r\n          </View>\r\n        )}\r\n      </Page>\r\n    </Document>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,oFAAoF;;;;;AACpF;AACA;AAAA;AAAA;;;;;AAEA,MAAM,SAAS,+LAAA,CAAA,aAAU,CAAC,MAAM,CAAC;IAC/B,MAAM;QACJ,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,UAAU;IACZ;IACA,QAAQ;QACN,UAAU;QACV,cAAc;QACd,WAAW;QACX,OAAO;QACP,YAAY;IACd;IACA,WAAW;QACT,UAAU;QACV,cAAc;QACd,OAAO;QACP,YAAY;QACZ,cAAc;QACd,eAAe;IACjB;IACA,SAAS;QACP,cAAc;IAChB;IACA,KAAK;QACH,eAAe;QACf,cAAc;QACd,iBAAiB;IACnB;IACA,OAAO;QACL,OAAO;QACP,YAAY;QACZ,OAAO;IACT;IACA,OAAO;QACL,OAAO;QACP,OAAO;IACT;IACA,OAAO;QACL,WAAW;IACb;IACA,aAAa;QACX,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,YAAY;IACd;IACA,UAAU;QACR,eAAe;QACf,SAAS;QACT,cAAc;IAChB;IACA,WAAW;QACT,MAAM;QACN,UAAU;IACZ;IACA,UAAU;QACR,WAAW;QACX,SAAS;QACT,iBAAiB;QACjB,cAAc;IAChB;IACA,cAAc;QACZ,UAAU;QACV,OAAO;QACP,cAAc;IAChB;AACF;AAQO,MAAM,yBAAgE,CAAC,EAC5E,IAAI,EACJ,WAAW,EACX,QAAQ,EACT;;IACC,6DAA6D;IAC7D,MAAM,eAAe,6JAAA,CAAA,UAAK,CAAC,OAAO;wDAAC;YACjC,IAAI,CAAC,MAAM,OAAO,CAAC;YACnB,MAAM,UAAU,MAAM,QAAQ;YAC9B,IAAI,CAAC,WAAW,OAAO,YAAY,UAAU,OAAO,CAAC;YAErD,OAAO;gBACL,YAAY,QAAQ,UAAU,IAAI;gBAClC,aAAa,QAAQ,WAAW,IAAI;gBACpC,cAAc,QAAQ,YAAY,IAAI;gBACtC,yBAAyB,QAAQ,uBAAuB,IAAI;gBAC5D,kBAAkB,QAAQ,gBAAgB,IAAI;gBAC9C,oBAAoB,QAAQ,kBAAkB,IAAI,CAAC;gBACnD,wBAAwB,MAAM,OAAO,CAAC,QAAQ,sBAAsB,IAAI,QAAQ,sBAAsB,GAAG,EAAE;gBAC3G,iBAAiB,QAAQ,eAAe,IAAI,CAAC;gBAC7C,sBAAsB,MAAM,OAAO,CAAC,QAAQ,oBAAoB,IAAI,QAAQ,oBAAoB,GAAG,EAAE;gBACrG,qBAAqB,QAAQ,mBAAmB,IAAI,CAAC;gBACrD,GAAG,OAAO;YACZ;QACF;uDAAG;QAAC;KAAK;IAET,MAAM,kBAAkB,eAAe;IACvC,MAAM,eAAe,YAAY,CAAC;IAElC,qBACE,6LAAC,+JAAA,CAAA,WAAQ;kBACP,cAAA,6LAAC,+JAAA,CAAA,OAAI;YAAC,MAAK;YAAK,OAAO,OAAO,IAAI;;8BAChC,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,MAAM;8BAAG;;;;;;8BAG7B,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAG,aAAa,UAAU,IAAI;;;;;;;;;;;;sCAEzD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAG,aAAa,WAAW,IAAI;;;;;;;;;;;;sCAE1D,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAG,aAAa,YAAY,IAAI;;;;;;;;;;;;sCAE3D,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,aAAa,uBAAuB,EAAE,QAAQ,MAAM;;;;;;;;;;;;sCAGzD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCACtB,aAAa,gBAAgB,EAAE,QAAQ,MAAM;wCAAE;;;;;;;;;;;;;;;;;;;gBAMrD,aAAa,kBAAkB,kBAC9B,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,aAAa,kBAAkB,CAAC,cAAc,IAAI;;;;;;;;;;;;sCAGvD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,aAAa,kBAAkB,CAAC,iBAAiB,IAAI;;;;;;;;;;;;sCAG1D,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,aAAa,kBAAkB,CAAC,aAAa,IAAI;;;;;;;;;;;;sCAGtD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCACtB,aAAa,kBAAkB,CAAC,eAAe,EAAE,QAAQ,MACxD;wCAAE;;;;;;;;;;;;;;;;;;;gBAQX,aAAa,sBAAsB,kBAClC,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,KAAK;;8CACvB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,WAAW;;sDAC7B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;sDAC/B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;sDAC/B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;;;;;;;gCAEhC,aAAa,sBAAsB,CAAC,GAAG,CACtC,CAAC,MAAW,sBACV,6LAAC,+JAAA,CAAA,OAAI;wCAAuB,OAAO,OAAO,QAAQ;;0DAChD,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,cAAc,MAAM,OAAO;;;;;;0DAEpC,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,SAAS,MAAM,QAAQ,cAAc;;;;;;0DAE9C,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,aACH,GAAG,OAAO,KAAK,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACxC;;;;;;;uCAVG,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;gBAoBnC,aAAa,eAAe,kBAC3B,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,aAAa,eAAe,CAAC,aAAa,IAAI;;;;;;;;;;;;sCAGnD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,aAAa,eAAe,CAAC,SAAS,IAAI;;;;;;;;;;;;sCAG/C,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,aAAa,eAAe,CAAC,OAAO,IAAI;;;;;;;;;;;;sCAG7C,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCACtB,aAAa,eAAe,CAAC,cAAc,EAAE,QAAQ,MAAM;wCAAE;;;;;;;;;;;;;sCAGlE,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,aAAa,eAAe,CAAC,kBAAkB,EAAE,QAAQ,MACxD;;;;;;;;;;;;;;;;;;gBAOT,aAAa,oBAAoB,kBAChC,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,KAAK;;8CACvB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,WAAW;;sDAC7B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;sDAC/B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;sDAC/B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;;;;;;;gCAEhC,aAAa,oBAAoB,CAAC,GAAG,CACpC,CAAC,MAAW,sBACV,6LAAC,+JAAA,CAAA,OAAI;wCAA2B,OAAO,OAAO,QAAQ;;0DACpD,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,SAAS,MAAM,OAAO;;;;;;0DAE/B,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,SAAS,MAAM,QAAQ,SAAS;;;;;;0DAEzC,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,aACH,GAAG,OAAO,KAAK,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACxC;;;;;;;uCAVG,CAAC,SAAS,EAAE,OAAO;;;;;;;;;;;;;;;;;gBAoBvC,aAAa,mBAAmB,kBAC/B,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,aAAa,mBAAmB,CAAC,SAAS,IAAI;;;;;;;;;;;;sCAGnD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,aAAa,mBAAmB,CAAC,YAAY,IAAI;;;;;;;;;;;;sCAGtD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,aAAa,mBAAmB,CAAC,OAAO,IAAI;;;;;;;;;;;;sCAGjD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCACtB,aAAa,mBAAmB,CAAC,gBAAgB,EAAE,QAClD,MACG;wCAAE;;;;;;;;;;;;;;;;;;;gBAQd,0BACC,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,QAAQ;;sCAC1B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCAAY,SAAS,EAAE;;;;;;;sCACzD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCAAO,SAAS,IAAI;;;;;;;sCACtD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCAClB,SAAS,UAAU;;;;;;;sCAEnC,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCACpB,IAAI,KAAK,SAAS,WAAW,EAAE,cAAc;;;;;;;sCAE3D,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCACjB,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;GA1Pa;KAAA", "debugId": null}}, {"offset": {"line": 1205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/exports/pdf/TaskReportDocument.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/exports/pdf/TaskReportDocument.tsx\r\nimport React from 'react';\r\nimport { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';\r\n\r\nconst styles = StyleSheet.create({\r\n  page: {\r\n    flexDirection: 'column',\r\n    backgroundColor: '#FFFFFF',\r\n    padding: 30,\r\n    fontSize: 12,\r\n  },\r\n  header: {\r\n    fontSize: 20,\r\n    marginBottom: 20,\r\n    textAlign: 'center',\r\n    color: '#059669',\r\n    fontWeight: 'bold',\r\n  },\r\n  subheader: {\r\n    fontSize: 16,\r\n    marginBottom: 15,\r\n    color: '#374151',\r\n    fontWeight: 'bold',\r\n    borderBottom: '1px solid #e5e7eb',\r\n    paddingBottom: 5,\r\n  },\r\n  section: {\r\n    marginBottom: 20,\r\n  },\r\n  row: {\r\n    flexDirection: 'row',\r\n    marginBottom: 8,\r\n    paddingVertical: 4,\r\n  },\r\n  label: {\r\n    width: '40%',\r\n    fontWeight: 'bold',\r\n    color: '#4b5563',\r\n  },\r\n  value: {\r\n    width: '60%',\r\n    color: '#111827',\r\n  },\r\n  table: {\r\n    marginTop: 10,\r\n  },\r\n  tableHeader: {\r\n    flexDirection: 'row',\r\n    backgroundColor: '#ecfdf5',\r\n    padding: 8,\r\n    fontWeight: 'bold',\r\n  },\r\n  tableRow: {\r\n    flexDirection: 'row',\r\n    padding: 8,\r\n    borderBottom: '1px solid #e5e7eb',\r\n  },\r\n  tableCell: {\r\n    flex: 1,\r\n    fontSize: 10,\r\n  },\r\n  metadata: {\r\n    marginTop: 30,\r\n    padding: 15,\r\n    backgroundColor: '#f0fdf4',\r\n    borderRadius: 5,\r\n  },\r\n  metadataText: {\r\n    fontSize: 10,\r\n    color: '#6b7280',\r\n    marginBottom: 3,\r\n  },\r\n});\r\n\r\ninterface TaskReportDocumentProps {\r\n  data: any;\r\n  reportTitle: string;\r\n  metadata?: any;\r\n}\r\n\r\nexport const TaskReportDocument: React.FC<TaskReportDocumentProps> = ({\r\n  data,\r\n  reportTitle,\r\n  metadata,\r\n}) => {\r\n  // Defensive programming: ensure all data is properly defined\r\n  const taskData = React.useMemo(() => {\r\n    if (!data) return {};\r\n    const rawData = data?.data || data;\r\n    if (!rawData || typeof rawData !== 'object') return {};\r\n\r\n    return {\r\n      totalCount: rawData.totalCount || 0,\r\n      completedTasks: rawData.completedTasks || 0,\r\n      pendingTasks: rawData.pendingTasks || 0,\r\n      overdueCount: rawData.overdueCount || 0,\r\n      completionRate: rawData.completionRate || 0,\r\n      averageCompletionTime: rawData.averageCompletionTime || 0,\r\n      statusDistribution: Array.isArray(rawData.statusDistribution) ? rawData.statusDistribution : [],\r\n      priorityDistribution: Array.isArray(rawData.priorityDistribution) ? rawData.priorityDistribution : [],\r\n      assignmentMetrics: rawData.assignmentMetrics || {},\r\n      ...rawData,\r\n    };\r\n  }, [data]);\r\n  \r\n  const safeReportTitle = reportTitle || 'Task Report';\r\n  const safeMetadata = metadata || {};\r\n\r\n  return (\r\n    <Document>\r\n      <Page size=\"A4\" style={styles.page}>\r\n        <Text style={styles.header}>{safeReportTitle}</Text>\r\n\r\n        {/* Task Summary */}\r\n        <View style={styles.section}>\r\n          <Text style={styles.subheader}>Task Summary</Text>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Total Tasks:</Text>\r\n            <Text style={styles.value}>{taskData.totalCount || 0}</Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Completed Tasks:</Text>\r\n            <Text style={styles.value}>{taskData.completedTasks || 0}</Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Pending Tasks:</Text>\r\n            <Text style={styles.value}>{taskData.pendingTasks || 0}</Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Overdue Tasks:</Text>\r\n            <Text style={styles.value}>{taskData.overdueCount || 0}</Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Completion Rate:</Text>\r\n            <Text style={styles.value}>\r\n              {taskData.completionRate?.toFixed(2) || 0}%\r\n            </Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Average Completion Time:</Text>\r\n            <Text style={styles.value}>\r\n              {taskData.averageCompletionTime?.toFixed(2) || 0} hours\r\n            </Text>\r\n          </View>\r\n        </View>\r\n\r\n        {/* Status Distribution */}\r\n        {taskData.statusDistribution && (\r\n          <View style={styles.section}>\r\n            <Text style={styles.subheader}>Status Distribution</Text>\r\n            <View style={styles.table}>\r\n              <View style={styles.tableHeader}>\r\n                <Text style={styles.tableCell}>Status</Text>\r\n                <Text style={styles.tableCell}>Count</Text>\r\n                <Text style={styles.tableCell}>Percentage</Text>\r\n              </View>\r\n              {taskData.statusDistribution.map((item: any, index: number) => (\r\n                <View key={`status-${index}`} style={styles.tableRow}>\r\n                  <Text style={styles.tableCell}>\r\n                    {item?.status || item?._id || 'Unknown'}\r\n                  </Text>\r\n                  <Text style={styles.tableCell}>\r\n                    {item?.count || item?._count?.status || 0}\r\n                  </Text>\r\n                  <Text style={styles.tableCell}>\r\n                    {item?.percentage\r\n                      ? `${Number(item.percentage).toFixed(1)}%`\r\n                      : 'N/A'}\r\n                  </Text>\r\n                </View>\r\n              ))}\r\n            </View>\r\n          </View>\r\n        )}\r\n\r\n        {/* Priority Distribution */}\r\n        {taskData.priorityDistribution && (\r\n          <View style={styles.section}>\r\n            <Text style={styles.subheader}>Priority Distribution</Text>\r\n            <View style={styles.table}>\r\n              <View style={styles.tableHeader}>\r\n                <Text style={styles.tableCell}>Priority</Text>\r\n                <Text style={styles.tableCell}>Count</Text>\r\n                <Text style={styles.tableCell}>Percentage</Text>\r\n              </View>\r\n              {taskData.priorityDistribution.map((item: any, index: number) => (\r\n                <View key={`priority-${index}`} style={styles.tableRow}>\r\n                  <Text style={styles.tableCell}>\r\n                    {item?.priority || item?._id || 'Unknown'}\r\n                  </Text>\r\n                  <Text style={styles.tableCell}>\r\n                    {item?.count || item?._count?.priority || 0}\r\n                  </Text>\r\n                  <Text style={styles.tableCell}>\r\n                    {item?.percentage\r\n                      ? `${Number(item.percentage).toFixed(1)}%`\r\n                      : 'N/A'}\r\n                  </Text>\r\n                </View>\r\n              ))}\r\n            </View>\r\n          </View>\r\n        )}\r\n\r\n        {/* Assignment Metrics */}\r\n        {taskData.assignmentMetrics && (\r\n          <View style={styles.section}>\r\n            <Text style={styles.subheader}>Assignment Metrics</Text>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Assigned Tasks:</Text>\r\n              <Text style={styles.value}>\r\n                {taskData.assignmentMetrics.assignedCount || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Unassigned Tasks:</Text>\r\n              <Text style={styles.value}>\r\n                {taskData.assignmentMetrics.unassignedCount || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Assignment Rate:</Text>\r\n              <Text style={styles.value}>\r\n                {taskData.assignmentMetrics.assignmentRate?.toFixed(2) || 0}%\r\n              </Text>\r\n            </View>\r\n          </View>\r\n        )}\r\n\r\n        {/* Report Metadata */}\r\n        {safeMetadata && Object.keys(safeMetadata).length > 0 && (\r\n          <View style={styles.metadata}>\r\n            <Text style={styles.subheader}>Report Information</Text>\r\n            <Text style={styles.metadataText}>\r\n              Report ID: {safeMetadata.id || 'N/A'}\r\n            </Text>\r\n            <Text style={styles.metadataText}>\r\n              Type: {safeMetadata.type || 'N/A'}\r\n            </Text>\r\n            <Text style={styles.metadataText}>\r\n              Entity Type: {safeMetadata.entityType || 'N/A'}\r\n            </Text>\r\n            <Text style={styles.metadataText}>\r\n              Generated:{' '}\r\n              {safeMetadata.generatedAt\r\n                ? new Date(safeMetadata.generatedAt).toLocaleString()\r\n                : 'N/A'}\r\n            </Text>\r\n            <Text style={styles.metadataText}>\r\n              Generated By: {safeMetadata.generatedBy || 'N/A'}\r\n            </Text>\r\n          </View>\r\n        )}\r\n      </Page>\r\n    </Document>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,gFAAgF;;;;;AAChF;AACA;AAAA;AAAA;;;;;AAEA,MAAM,SAAS,+LAAA,CAAA,aAAU,CAAC,MAAM,CAAC;IAC/B,MAAM;QACJ,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,UAAU;IACZ;IACA,QAAQ;QACN,UAAU;QACV,cAAc;QACd,WAAW;QACX,OAAO;QACP,YAAY;IACd;IACA,WAAW;QACT,UAAU;QACV,cAAc;QACd,OAAO;QACP,YAAY;QACZ,cAAc;QACd,eAAe;IACjB;IACA,SAAS;QACP,cAAc;IAChB;IACA,KAAK;QACH,eAAe;QACf,cAAc;QACd,iBAAiB;IACnB;IACA,OAAO;QACL,OAAO;QACP,YAAY;QACZ,OAAO;IACT;IACA,OAAO;QACL,OAAO;QACP,OAAO;IACT;IACA,OAAO;QACL,WAAW;IACb;IACA,aAAa;QACX,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,YAAY;IACd;IACA,UAAU;QACR,eAAe;QACf,SAAS;QACT,cAAc;IAChB;IACA,WAAW;QACT,MAAM;QACN,UAAU;IACZ;IACA,UAAU;QACR,WAAW;QACX,SAAS;QACT,iBAAiB;QACjB,cAAc;IAChB;IACA,cAAc;QACZ,UAAU;QACV,OAAO;QACP,cAAc;IAChB;AACF;AAQO,MAAM,qBAAwD,CAAC,EACpE,IAAI,EACJ,WAAW,EACX,QAAQ,EACT;;IACC,6DAA6D;IAC7D,MAAM,WAAW,6JAAA,CAAA,UAAK,CAAC,OAAO;gDAAC;YAC7B,IAAI,CAAC,MAAM,OAAO,CAAC;YACnB,MAAM,UAAU,MAAM,QAAQ;YAC9B,IAAI,CAAC,WAAW,OAAO,YAAY,UAAU,OAAO,CAAC;YAErD,OAAO;gBACL,YAAY,QAAQ,UAAU,IAAI;gBAClC,gBAAgB,QAAQ,cAAc,IAAI;gBAC1C,cAAc,QAAQ,YAAY,IAAI;gBACtC,cAAc,QAAQ,YAAY,IAAI;gBACtC,gBAAgB,QAAQ,cAAc,IAAI;gBAC1C,uBAAuB,QAAQ,qBAAqB,IAAI;gBACxD,oBAAoB,MAAM,OAAO,CAAC,QAAQ,kBAAkB,IAAI,QAAQ,kBAAkB,GAAG,EAAE;gBAC/F,sBAAsB,MAAM,OAAO,CAAC,QAAQ,oBAAoB,IAAI,QAAQ,oBAAoB,GAAG,EAAE;gBACrG,mBAAmB,QAAQ,iBAAiB,IAAI,CAAC;gBACjD,GAAG,OAAO;YACZ;QACF;+CAAG;QAAC;KAAK;IAET,MAAM,kBAAkB,eAAe;IACvC,MAAM,eAAe,YAAY,CAAC;IAElC,qBACE,6LAAC,+JAAA,CAAA,WAAQ;kBACP,cAAA,6LAAC,+JAAA,CAAA,OAAI;YAAC,MAAK;YAAK,OAAO,OAAO,IAAI;;8BAChC,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,MAAM;8BAAG;;;;;;8BAG7B,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAG,SAAS,UAAU,IAAI;;;;;;;;;;;;sCAErD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAG,SAAS,cAAc,IAAI;;;;;;;;;;;;sCAEzD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAG,SAAS,YAAY,IAAI;;;;;;;;;;;;sCAEvD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAG,SAAS,YAAY,IAAI;;;;;;;;;;;;sCAEvD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCACtB,SAAS,cAAc,EAAE,QAAQ,MAAM;wCAAE;;;;;;;;;;;;;sCAG9C,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCACtB,SAAS,qBAAqB,EAAE,QAAQ,MAAM;wCAAE;;;;;;;;;;;;;;;;;;;gBAMtD,SAAS,kBAAkB,kBAC1B,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,KAAK;;8CACvB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,WAAW;;sDAC7B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;sDAC/B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;sDAC/B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;;;;;;;gCAEhC,SAAS,kBAAkB,CAAC,GAAG,CAAC,CAAC,MAAW,sBAC3C,6LAAC,+JAAA,CAAA,OAAI;wCAAyB,OAAO,OAAO,QAAQ;;0DAClD,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,UAAU,MAAM,OAAO;;;;;;0DAEhC,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,SAAS,MAAM,QAAQ,UAAU;;;;;;0DAE1C,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,aACH,GAAG,OAAO,KAAK,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACxC;;;;;;;uCAVG,CAAC,OAAO,EAAE,OAAO;;;;;;;;;;;;;;;;;gBAmBnC,SAAS,oBAAoB,kBAC5B,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,KAAK;;8CACvB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,WAAW;;sDAC7B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;sDAC/B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;sDAC/B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;;;;;;;gCAEhC,SAAS,oBAAoB,CAAC,GAAG,CAAC,CAAC,MAAW,sBAC7C,6LAAC,+JAAA,CAAA,OAAI;wCAA2B,OAAO,OAAO,QAAQ;;0DACpD,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,YAAY,MAAM,OAAO;;;;;;0DAElC,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,SAAS,MAAM,QAAQ,YAAY;;;;;;0DAE5C,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,aACH,GAAG,OAAO,KAAK,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACxC;;;;;;;uCAVG,CAAC,SAAS,EAAE,OAAO;;;;;;;;;;;;;;;;;gBAmBrC,SAAS,iBAAiB,kBACzB,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,SAAS,iBAAiB,CAAC,aAAa,IAAI;;;;;;;;;;;;sCAGjD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,SAAS,iBAAiB,CAAC,eAAe,IAAI;;;;;;;;;;;;sCAGnD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCACtB,SAAS,iBAAiB,CAAC,cAAc,EAAE,QAAQ,MAAM;wCAAE;;;;;;;;;;;;;;;;;;;gBAOnE,gBAAgB,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,mBAClD,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,QAAQ;;sCAC1B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCACpB,aAAa,EAAE,IAAI;;;;;;;sCAEjC,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCACzB,aAAa,IAAI,IAAI;;;;;;;sCAE9B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCAClB,aAAa,UAAU,IAAI;;;;;;;sCAE3C,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCACrB;gCACV,aAAa,WAAW,GACrB,IAAI,KAAK,aAAa,WAAW,EAAE,cAAc,KACjD;;;;;;;sCAEN,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCACjB,aAAa,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;AAOzD;GAhLa;KAAA", "debugId": null}}, {"offset": {"line": 1876, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/exports/pdf/VehicleReportDocument.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/exports/pdf/VehicleReportDocument.tsx\r\nimport React from 'react';\r\nimport { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';\r\n\r\nconst styles = StyleSheet.create({\r\n  page: {\r\n    flexDirection: 'column',\r\n    backgroundColor: '#FFFFFF',\r\n    padding: 30,\r\n    fontSize: 12,\r\n  },\r\n  header: {\r\n    fontSize: 20,\r\n    marginBottom: 20,\r\n    textAlign: 'center',\r\n    color: '#dc2626',\r\n    fontWeight: 'bold',\r\n  },\r\n  subheader: {\r\n    fontSize: 16,\r\n    marginBottom: 15,\r\n    color: '#374151',\r\n    fontWeight: 'bold',\r\n    borderBottom: '1px solid #e5e7eb',\r\n    paddingBottom: 5,\r\n  },\r\n  section: {\r\n    marginBottom: 20,\r\n  },\r\n  row: {\r\n    flexDirection: 'row',\r\n    marginBottom: 8,\r\n    paddingVertical: 4,\r\n  },\r\n  label: {\r\n    width: '40%',\r\n    fontWeight: 'bold',\r\n    color: '#4b5563',\r\n  },\r\n  value: {\r\n    width: '60%',\r\n    color: '#111827',\r\n  },\r\n  table: {\r\n    marginTop: 10,\r\n  },\r\n  tableHeader: {\r\n    flexDirection: 'row',\r\n    backgroundColor: '#fef2f2',\r\n    padding: 8,\r\n    fontWeight: 'bold',\r\n  },\r\n  tableRow: {\r\n    flexDirection: 'row',\r\n    padding: 8,\r\n    borderBottom: '1px solid #e5e7eb',\r\n  },\r\n  tableCell: {\r\n    flex: 1,\r\n    fontSize: 10,\r\n  },\r\n  metadata: {\r\n    marginTop: 30,\r\n    padding: 15,\r\n    backgroundColor: '#fef2f2',\r\n    borderRadius: 5,\r\n  },\r\n  metadataText: {\r\n    fontSize: 10,\r\n    color: '#6b7280',\r\n    marginBottom: 3,\r\n  },\r\n});\r\n\r\ninterface VehicleReportDocumentProps {\r\n  data: any;\r\n  reportTitle: string;\r\n  metadata?: any;\r\n}\r\n\r\nexport const VehicleReportDocument: React.FC<VehicleReportDocumentProps> = ({\r\n  data,\r\n  reportTitle,\r\n  metadata,\r\n}) => {\r\n  // Defensive programming: ensure all data is properly defined\r\n  const vehicleData = React.useMemo(() => {\r\n    if (!data) return {};\r\n\r\n    // Handle different data structures\r\n    const rawData = data?.data || data;\r\n\r\n    // Ensure we have a valid object\r\n    if (!rawData || typeof rawData !== 'object') {\r\n      return {};\r\n    }\r\n\r\n    // Return a safe copy with fallbacks\r\n    return {\r\n      totalCount: rawData.totalCount || 0,\r\n      activeCount: rawData.activeCount || 0,\r\n      maintenanceCount: rawData.maintenanceCount || 0,\r\n      outOfServiceCount: rawData.outOfServiceCount || 0,\r\n      utilizationRate: rawData.utilizationRate || 0,\r\n      averageMileage: rawData.averageMileage || 0,\r\n      statusDistribution: Array.isArray(rawData.statusDistribution)\r\n        ? rawData.statusDistribution\r\n        : [],\r\n      typeDistribution: Array.isArray(rawData.typeDistribution)\r\n        ? rawData.typeDistribution\r\n        : [],\r\n      maintenanceMetrics: rawData.maintenanceMetrics || {},\r\n      ...rawData,\r\n    };\r\n  }, [data]);\r\n\r\n  const safeReportTitle = reportTitle || 'Vehicle Report';\r\n  const safeMetadata = metadata || {};\r\n\r\n  return (\r\n    <Document>\r\n      <Page size=\"A4\" style={styles.page}>\r\n        <Text style={styles.header}>{safeReportTitle}</Text>\r\n\r\n        {/* Vehicle Fleet Summary */}\r\n        <View style={styles.section}>\r\n          <Text style={styles.subheader}>Fleet Summary</Text>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Total Vehicles:</Text>\r\n            <Text style={styles.value}>{vehicleData.totalCount || 0}</Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Active Vehicles:</Text>\r\n            <Text style={styles.value}>{vehicleData.activeCount || 0}</Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>In Maintenance:</Text>\r\n            <Text style={styles.value}>\r\n              {vehicleData.maintenanceCount || 0}\r\n            </Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Out of Service:</Text>\r\n            <Text style={styles.value}>\r\n              {vehicleData.outOfServiceCount || 0}\r\n            </Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Fleet Utilization Rate:</Text>\r\n            <Text style={styles.value}>\r\n              {vehicleData.utilizationRate?.toFixed(2) || 0}%\r\n            </Text>\r\n          </View>\r\n          <View style={styles.row}>\r\n            <Text style={styles.label}>Average Mileage:</Text>\r\n            <Text style={styles.value}>\r\n              {vehicleData.averageMileage?.toFixed(0) || 0} km\r\n            </Text>\r\n          </View>\r\n        </View>\r\n\r\n        {/* Status Distribution */}\r\n        {vehicleData.statusDistribution && (\r\n          <View style={styles.section}>\r\n            <Text style={styles.subheader}>Vehicle Status Distribution</Text>\r\n            <View style={styles.table}>\r\n              <View style={styles.tableHeader}>\r\n                <Text style={styles.tableCell}>Status</Text>\r\n                <Text style={styles.tableCell}>Count</Text>\r\n                <Text style={styles.tableCell}>Percentage</Text>\r\n              </View>\r\n              {vehicleData.statusDistribution.map(\r\n                (item: any, index: number) => (\r\n                  <View key={`status-${index}`} style={styles.tableRow}>\r\n                    <Text style={styles.tableCell}>\r\n                      {item?.status || item?._id || 'Unknown'}\r\n                    </Text>\r\n                    <Text style={styles.tableCell}>\r\n                      {item?.count || item?._count?.status || 0}\r\n                    </Text>\r\n                    <Text style={styles.tableCell}>\r\n                      {item?.percentage\r\n                        ? `${Number(item.percentage).toFixed(1)}%`\r\n                        : 'N/A'}\r\n                    </Text>\r\n                  </View>\r\n                )\r\n              )}\r\n            </View>\r\n          </View>\r\n        )}\r\n\r\n        {/* Vehicle Type Distribution */}\r\n        {vehicleData.typeDistribution && (\r\n          <View style={styles.section}>\r\n            <Text style={styles.subheader}>Vehicle Type Distribution</Text>\r\n            <View style={styles.table}>\r\n              <View style={styles.tableHeader}>\r\n                <Text style={styles.tableCell}>Type</Text>\r\n                <Text style={styles.tableCell}>Count</Text>\r\n                <Text style={styles.tableCell}>Percentage</Text>\r\n              </View>\r\n              {vehicleData.typeDistribution.map((item: any, index: number) => (\r\n                <View key={`type-${index}`} style={styles.tableRow}>\r\n                  <Text style={styles.tableCell}>\r\n                    {item?.type || item?._id || 'Unknown'}\r\n                  </Text>\r\n                  <Text style={styles.tableCell}>\r\n                    {item?.count || item?._count?.type || 0}\r\n                  </Text>\r\n                  <Text style={styles.tableCell}>\r\n                    {item?.percentage\r\n                      ? `${Number(item.percentage).toFixed(1)}%`\r\n                      : 'N/A'}\r\n                  </Text>\r\n                </View>\r\n              ))}\r\n            </View>\r\n          </View>\r\n        )}\r\n\r\n        {/* Maintenance Metrics */}\r\n        {vehicleData.maintenanceMetrics && (\r\n          <View style={styles.section}>\r\n            <Text style={styles.subheader}>Maintenance Metrics</Text>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Total Maintenance Records:</Text>\r\n              <Text style={styles.value}>\r\n                {vehicleData.maintenanceMetrics.totalRecords || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Average Cost per Service:</Text>\r\n              <Text style={styles.value}>\r\n                ${vehicleData.maintenanceMetrics.averageCost?.toFixed(2) || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Total Maintenance Cost:</Text>\r\n              <Text style={styles.value}>\r\n                ${vehicleData.maintenanceMetrics.totalCost?.toFixed(2) || 0}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Preventive Maintenance Rate:</Text>\r\n              <Text style={styles.value}>\r\n                {vehicleData.maintenanceMetrics.preventiveRate?.toFixed(2) || 0}\r\n                %\r\n              </Text>\r\n            </View>\r\n          </View>\r\n        )}\r\n\r\n        {/* Utilization Metrics */}\r\n        {vehicleData.utilizationMetrics && (\r\n          <View style={styles.section}>\r\n            <Text style={styles.subheader}>Utilization Metrics</Text>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Average Daily Usage:</Text>\r\n              <Text style={styles.value}>\r\n                {vehicleData.utilizationMetrics.averageDailyUsage?.toFixed(2) ||\r\n                  0}{' '}\r\n                hours\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Peak Usage Hours:</Text>\r\n              <Text style={styles.value}>\r\n                {vehicleData.utilizationMetrics.peakUsageHours || 'N/A'}\r\n              </Text>\r\n            </View>\r\n            <View style={styles.row}>\r\n              <Text style={styles.label}>Idle Time Percentage:</Text>\r\n              <Text style={styles.value}>\r\n                {vehicleData.utilizationMetrics.idleTimePercentage?.toFixed(\r\n                  2\r\n                ) || 0}\r\n                %\r\n              </Text>\r\n            </View>\r\n          </View>\r\n        )}\r\n\r\n        {/* Report Metadata */}\r\n        {metadata && (\r\n          <View style={styles.metadata}>\r\n            <Text style={styles.subheader}>Report Information</Text>\r\n            <Text style={styles.metadataText}>Report ID: {metadata.id}</Text>\r\n            <Text style={styles.metadataText}>Type: {metadata.type}</Text>\r\n            <Text style={styles.metadataText}>\r\n              Entity Type: {metadata.entityType}\r\n            </Text>\r\n            <Text style={styles.metadataText}>\r\n              Generated: {new Date(metadata.generatedAt).toLocaleString()}\r\n            </Text>\r\n            <Text style={styles.metadataText}>\r\n              Generated By: {metadata.generatedBy}\r\n            </Text>\r\n          </View>\r\n        )}\r\n      </Page>\r\n    </Document>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,mFAAmF;;;;;AACnF;AACA;AAAA;AAAA;;;;;AAEA,MAAM,SAAS,+LAAA,CAAA,aAAU,CAAC,MAAM,CAAC;IAC/B,MAAM;QACJ,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,UAAU;IACZ;IACA,QAAQ;QACN,UAAU;QACV,cAAc;QACd,WAAW;QACX,OAAO;QACP,YAAY;IACd;IACA,WAAW;QACT,UAAU;QACV,cAAc;QACd,OAAO;QACP,YAAY;QACZ,cAAc;QACd,eAAe;IACjB;IACA,SAAS;QACP,cAAc;IAChB;IACA,KAAK;QACH,eAAe;QACf,cAAc;QACd,iBAAiB;IACnB;IACA,OAAO;QACL,OAAO;QACP,YAAY;QACZ,OAAO;IACT;IACA,OAAO;QACL,OAAO;QACP,OAAO;IACT;IACA,OAAO;QACL,WAAW;IACb;IACA,aAAa;QACX,eAAe;QACf,iBAAiB;QACjB,SAAS;QACT,YAAY;IACd;IACA,UAAU;QACR,eAAe;QACf,SAAS;QACT,cAAc;IAChB;IACA,WAAW;QACT,MAAM;QACN,UAAU;IACZ;IACA,UAAU;QACR,WAAW;QACX,SAAS;QACT,iBAAiB;QACjB,cAAc;IAChB;IACA,cAAc;QACZ,UAAU;QACV,OAAO;QACP,cAAc;IAChB;AACF;AAQO,MAAM,wBAA8D,CAAC,EAC1E,IAAI,EACJ,WAAW,EACX,QAAQ,EACT;;IACC,6DAA6D;IAC7D,MAAM,cAAc,6JAAA,CAAA,UAAK,CAAC,OAAO;sDAAC;YAChC,IAAI,CAAC,MAAM,OAAO,CAAC;YAEnB,mCAAmC;YACnC,MAAM,UAAU,MAAM,QAAQ;YAE9B,gCAAgC;YAChC,IAAI,CAAC,WAAW,OAAO,YAAY,UAAU;gBAC3C,OAAO,CAAC;YACV;YAEA,oCAAoC;YACpC,OAAO;gBACL,YAAY,QAAQ,UAAU,IAAI;gBAClC,aAAa,QAAQ,WAAW,IAAI;gBACpC,kBAAkB,QAAQ,gBAAgB,IAAI;gBAC9C,mBAAmB,QAAQ,iBAAiB,IAAI;gBAChD,iBAAiB,QAAQ,eAAe,IAAI;gBAC5C,gBAAgB,QAAQ,cAAc,IAAI;gBAC1C,oBAAoB,MAAM,OAAO,CAAC,QAAQ,kBAAkB,IACxD,QAAQ,kBAAkB,GAC1B,EAAE;gBACN,kBAAkB,MAAM,OAAO,CAAC,QAAQ,gBAAgB,IACpD,QAAQ,gBAAgB,GACxB,EAAE;gBACN,oBAAoB,QAAQ,kBAAkB,IAAI,CAAC;gBACnD,GAAG,OAAO;YACZ;QACF;qDAAG;QAAC;KAAK;IAET,MAAM,kBAAkB,eAAe;IACvC,MAAM,eAAe,YAAY,CAAC;IAElC,qBACE,6LAAC,+JAAA,CAAA,WAAQ;kBACP,cAAA,6LAAC,+JAAA,CAAA,OAAI;YAAC,MAAK;YAAK,OAAO,OAAO,IAAI;;8BAChC,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,MAAM;8BAAG;;;;;;8BAG7B,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAG,YAAY,UAAU,IAAI;;;;;;;;;;;;sCAExD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAG,YAAY,WAAW,IAAI;;;;;;;;;;;;sCAEzD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,YAAY,gBAAgB,IAAI;;;;;;;;;;;;sCAGrC,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,YAAY,iBAAiB,IAAI;;;;;;;;;;;;sCAGtC,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCACtB,YAAY,eAAe,EAAE,QAAQ,MAAM;wCAAE;;;;;;;;;;;;;sCAGlD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCACtB,YAAY,cAAc,EAAE,QAAQ,MAAM;wCAAE;;;;;;;;;;;;;;;;;;;gBAMlD,YAAY,kBAAkB,kBAC7B,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,KAAK;;8CACvB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,WAAW;;sDAC7B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;sDAC/B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;sDAC/B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;;;;;;;gCAEhC,YAAY,kBAAkB,CAAC,GAAG,CACjC,CAAC,MAAW,sBACV,6LAAC,+JAAA,CAAA,OAAI;wCAAyB,OAAO,OAAO,QAAQ;;0DAClD,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,UAAU,MAAM,OAAO;;;;;;0DAEhC,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,SAAS,MAAM,QAAQ,UAAU;;;;;;0DAE1C,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,aACH,GAAG,OAAO,KAAK,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACxC;;;;;;;uCAVG,CAAC,OAAO,EAAE,OAAO;;;;;;;;;;;;;;;;;gBAoBrC,YAAY,gBAAgB,kBAC3B,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,KAAK;;8CACvB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,WAAW;;sDAC7B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;sDAC/B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;sDAC/B,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO,OAAO,SAAS;sDAAE;;;;;;;;;;;;gCAEhC,YAAY,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAW,sBAC5C,6LAAC,+JAAA,CAAA,OAAI;wCAAuB,OAAO,OAAO,QAAQ;;0DAChD,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,QAAQ,MAAM,OAAO;;;;;;0DAE9B,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,SAAS,MAAM,QAAQ,QAAQ;;;;;;0DAExC,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO,OAAO,SAAS;0DAC1B,MAAM,aACH,GAAG,OAAO,KAAK,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,GACxC;;;;;;;uCAVG,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;gBAmBjC,YAAY,kBAAkB,kBAC7B,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,YAAY,kBAAkB,CAAC,YAAY,IAAI;;;;;;;;;;;;sCAGpD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCAAE;wCACvB,YAAY,kBAAkB,CAAC,WAAW,EAAE,QAAQ,MAAM;;;;;;;;;;;;;sCAGhE,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCAAE;wCACvB,YAAY,kBAAkB,CAAC,SAAS,EAAE,QAAQ,MAAM;;;;;;;;;;;;;sCAG9D,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCACtB,YAAY,kBAAkB,CAAC,cAAc,EAAE,QAAQ,MAAM;wCAAE;;;;;;;;;;;;;;;;;;;gBAQvE,YAAY,kBAAkB,kBAC7B,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,OAAO;;sCACzB,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCACtB,YAAY,kBAAkB,CAAC,iBAAiB,EAAE,QAAQ,MACzD;wCAAG;wCAAI;;;;;;;;;;;;;sCAIb,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CACtB,YAAY,kBAAkB,CAAC,cAAc,IAAI;;;;;;;;;;;;sCAGtD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,GAAG;;8CACrB,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;8CAAE;;;;;;8CAC3B,6LAAC,+JAAA,CAAA,OAAI;oCAAC,OAAO,OAAO,KAAK;;wCACtB,YAAY,kBAAkB,CAAC,kBAAkB,EAAE,QAClD,MACG;wCAAE;;;;;;;;;;;;;;;;;;;gBAQd,0BACC,6LAAC,+JAAA,CAAA,OAAI;oBAAC,OAAO,OAAO,QAAQ;;sCAC1B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,SAAS;sCAAE;;;;;;sCAC/B,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCAAY,SAAS,EAAE;;;;;;;sCACzD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCAAO,SAAS,IAAI;;;;;;;sCACtD,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCAClB,SAAS,UAAU;;;;;;;sCAEnC,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCACpB,IAAI,KAAK,SAAS,WAAW,EAAE,cAAc;;;;;;;sCAE3D,6LAAC,+JAAA,CAAA,OAAI;4BAAC,OAAO,OAAO,YAAY;;gCAAE;gCACjB,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;GA/Na;KAAA", "debugId": null}}, {"offset": {"line": 2681, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/exports/hooks/useExport.tsx"], "sourcesContent": ["import { Document, Page, pdf, Text, View } from '@react-pdf/renderer'; // Import React-PDF components\r\n// frontend/src/components/features/reporting/exports/hooks/useExport.tsx\r\nimport { useCallback, useState } from 'react';\r\nimport * as XLSX from 'xlsx'; // Assuming xlsx library for Excel\r\n\r\nimport type {\r\n  DelegationAnalytics,\r\n  ExportOptions,\r\n  ReportingFilters,\r\n} from '../../data/types'; // Import ExportOptions, DelegationAnalytics, ReportingFilters\r\n\r\nimport { DelegationReportDocument } from '../pdf/DelegationReportDocument'; // Import the PDF document component\r\nimport { EmployeeReportDocument } from '../pdf/EmployeeReportDocument';\r\nimport { TaskReportDocument } from '../pdf/TaskReportDocument';\r\nimport { VehicleReportDocument } from '../pdf/VehicleReportDocument';\r\n// Note: UniversalReportDocument removed - using entity-specific components instead\r\n\r\n/**\r\n * @hook useExport\r\n * @description Provides utility functions for exporting data to various formats (PDF, Excel, CSV).\r\n * Adheres to SRP by encapsulating export logic.\r\n * @param {string} defaultFilename - The default filename for exported files.\r\n */\r\nexport const useExport = (defaultFilename = 'report') => {\r\n  const [isExporting, setIsExporting] = useState(false);\r\n  const [exportError, setExportError] = useState<null | string>(null);\r\n\r\n  /**\r\n   * Exports data to a PDF using @react-pdf/renderer.\r\n   * @param {DelegationAnalytics} data - The analytics data to include in the report.\r\n   * @param {ReportingFilters} filters - The filters applied to the data.\r\n   * @param {ExportOptions} options - Export options including filename.\r\n   */\r\n  const exportToPDF = useCallback(\r\n    async (\r\n      data: DelegationAnalytics,\r\n      filters: ReportingFilters,\r\n      options: ExportOptions\r\n    ) => {\r\n      setIsExporting(true);\r\n      setExportError(null);\r\n      try {\r\n        const blob = await pdf(\r\n          <DelegationReportDocument\r\n            data={data}\r\n            filters={filters}\r\n            reportDate={new Date().toLocaleString()} // Using current date/time\r\n            reportTitle={options.filename || defaultFilename}\r\n          />\r\n        ).toBlob();\r\n\r\n        const url = URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute(\r\n          'download',\r\n          `${options.filename || defaultFilename}.pdf`\r\n        );\r\n        document.body.append(link);\r\n        link.click();\r\n        link.remove();\r\n        URL.revokeObjectURL(url); // Clean up the URL object\r\n      } catch (error: any) {\r\n        console.error('PDF export failed:', error);\r\n        setExportError(error.message || 'Failed to export to PDF.');\r\n      } finally {\r\n        setIsExporting(false);\r\n      }\r\n    },\r\n    [defaultFilename]\r\n  );\r\n\r\n  /**\r\n   * Exports tabular data to an Excel (XLSX) file.\r\n   * @param {Array<Record<string, any>>} data - The array of objects to export.\r\n   * @param {ExportOptions} options - Export options including filename.\r\n   */\r\n  const exportToExcel = useCallback(\r\n    (data: Record<string, any>[], options: ExportOptions) => {\r\n      setIsExporting(true);\r\n      setExportError(null);\r\n      try {\r\n        const worksheet = XLSX.utils.json_to_sheet(data);\r\n        const workbook = XLSX.utils.book_new();\r\n        XLSX.utils.book_append_sheet(workbook, worksheet, 'Report Data');\r\n        XLSX.writeFile(workbook, `${options.filename || defaultFilename}.xlsx`);\r\n      } catch (error: any) {\r\n        console.error('Excel export failed:', error);\r\n        setExportError(error.message || 'Failed to export to Excel.');\r\n      } finally {\r\n        setIsExporting(false);\r\n      }\r\n    },\r\n    [defaultFilename]\r\n  );\r\n\r\n  /**\r\n   * Exports tabular data to a CSV file.\r\n   * @param {Array<Record<string, any>>} data - The array of objects to export.\r\n   * @param {ExportOptions} options - Export options including filename.\r\n   */\r\n  const exportToCSV = useCallback(\r\n    (data: Record<string, any>[], options: ExportOptions) => {\r\n      setIsExporting(true);\r\n      setExportError(null);\r\n      try {\r\n        const header = Object.keys(data[0] || {}).join(',');\r\n        const rows = data.map(row =>\r\n          Object.values(row)\r\n            .map(value => `\"${String(value).replaceAll('\"', '\"\"')}\"`)\r\n            .join(',')\r\n        );\r\n        const csvContent = [header, ...rows].join('\\n');\r\n        const blob = new Blob([csvContent], {\r\n          type: 'text/csv;charset=utf-8;',\r\n        });\r\n        const link = document.createElement('a');\r\n        if (link.download !== undefined) {\r\n          const url = URL.createObjectURL(blob);\r\n          link.setAttribute('href', url);\r\n          link.setAttribute(\r\n            'download',\r\n            `${options.filename || defaultFilename}.csv`\r\n          );\r\n          link.style.visibility = 'hidden';\r\n          document.body.append(link);\r\n          link.click();\r\n          link.remove();\r\n        }\r\n      } catch (error: any) {\r\n        console.error('CSV export failed:', error);\r\n        setExportError(error.message || 'Failed to export to CSV.');\r\n      } finally {\r\n        setIsExporting(false);\r\n      }\r\n    },\r\n    [defaultFilename]\r\n  );\r\n\r\n  // ENHANCED: Modern React-PDF dashboard export\r\n  const exportDashboardToPDF = useCallback(\r\n    async (\r\n      data: DelegationAnalytics,\r\n      filters: ReportingFilters,\r\n      filename?: string\r\n    ) => {\r\n      setIsExporting(true);\r\n      setExportError(null);\r\n\r\n      try {\r\n        // Use existing React-PDF infrastructure - modern approach\r\n        const blob = await pdf(\r\n          <DelegationReportDocument\r\n            data={data}\r\n            filters={filters}\r\n            includeCharts={true}\r\n            includeServiceHistory={Boolean(filters.includeServiceHistory)}\r\n            reportDate={new Date().toLocaleString()}\r\n            reportTitle={filename || 'Dashboard Report'}\r\n          />\r\n        ).toBlob();\r\n\r\n        const url = URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute('download', `${filename || 'dashboard-report'}.pdf`);\r\n        document.body.append(link);\r\n        link.click();\r\n        link.remove();\r\n        URL.revokeObjectURL(url);\r\n      } catch (error: any) {\r\n        console.error('Dashboard PDF export failed:', error);\r\n        setExportError(error.message || 'Failed to export dashboard to PDF.');\r\n      } finally {\r\n        setIsExporting(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  // ENHANCED: Chart-specific PDF export using React-PDF\r\n  const exportChartToPDF = useCallback(\r\n    async (chartData: any, title: string, filename?: string) => {\r\n      setIsExporting(true);\r\n      setExportError(null);\r\n\r\n      try {\r\n        // Create a minimal chart-focused PDF document\r\n        const ChartDocument = () => (\r\n          <Document>\r\n            <Page orientation=\"landscape\" size=\"A4\" style={{ padding: 30 }}>\r\n              <View style={{ marginBottom: 20 }}>\r\n                <Text style={{ fontSize: 20, fontWeight: 'bold' }}>\r\n                  {title}\r\n                </Text>\r\n                <Text style={{ fontSize: 10, marginTop: 5 }}>\r\n                  Generated on: {new Date().toLocaleDateString()}\r\n                </Text>\r\n              </View>\r\n              <View\r\n                style={{\r\n                  alignItems: 'center',\r\n                  flex: 1,\r\n                  justifyContent: 'center',\r\n                }}\r\n              >\r\n                <Text style={{ fontSize: 14 }}>\r\n                  Chart data: {JSON.stringify(chartData, null, 2)}\r\n                </Text>\r\n              </View>\r\n            </Page>\r\n          </Document>\r\n        );\r\n\r\n        const blob = await pdf(<ChartDocument />).toBlob();\r\n\r\n        const url = URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        link.setAttribute(\r\n          'download',\r\n          filename || `${title.toLowerCase().replaceAll(/\\s+/g, '-')}.pdf`\r\n        );\r\n        document.body.append(link);\r\n        link.click();\r\n        link.remove();\r\n        URL.revokeObjectURL(url);\r\n      } catch (error: any) {\r\n        console.error('Chart PDF export failed:', error);\r\n        setExportError(error.message || 'Failed to export chart to PDF.');\r\n      } finally {\r\n        setIsExporting(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Export report data to PDF based on entity type\r\n   */\r\n  const exportReportToPDF = useCallback(\r\n    async (\r\n      reportData: any,\r\n      entityType: 'delegations' | 'employees' | 'tasks' | 'vehicles',\r\n      reportTitle: string,\r\n      filename?: string\r\n    ) => {\r\n      setIsExporting(true);\r\n      setExportError(null);\r\n\r\n      // --- START AGGRESSIVE SANITIZATION ---\r\n      const sanitizeData = (data: any): any => {\r\n        if (data === null || data === undefined) {\r\n          return null; // Keep null/undefined as null for React-PDF compatibility\r\n        }\r\n        if (Array.isArray(data)) {\r\n          return data.map(sanitizeData); // Recursively sanitize array elements\r\n        }\r\n        if (typeof data === 'object' && data.constructor === Object) {\r\n          const sanitizedObject: Record<string, any> = {};\r\n          for (const key in data) {\r\n            // Ensure we only process own properties and avoid problematic keys\r\n            if (\r\n              Object.prototype.hasOwnProperty.call(data, key) &&\r\n              key !== '__proto__' &&\r\n              key !== 'constructor'\r\n            ) {\r\n              const sanitizedValue = sanitizeData(data[key]);\r\n              // Only include non-undefined values\r\n              if (sanitizedValue !== undefined) {\r\n                sanitizedObject[key] = sanitizedValue;\r\n              }\r\n            }\r\n          }\r\n          return sanitizedObject;\r\n        }\r\n        // Handle functions, symbols, and other non-serializable types\r\n        if (typeof data === 'function' || typeof data === 'symbol') {\r\n          return null;\r\n        }\r\n        // Return primitives (string, number, boolean) as is\r\n        return data;\r\n      };\r\n      // --- END AGGRESSIVE SANITIZATION ---\r\n\r\n      try {\r\n        if (!reportData) {\r\n          throw new Error('No report data provided for PDF export');\r\n        }\r\n\r\n        // Ensure we have a valid data structure to work with\r\n        const rawData = reportData?.data || reportData || {};\r\n\r\n        // Create a fully sanitized and safe data structure\r\n        const sanitizedReportData = sanitizeData(rawData);\r\n\r\n        // Ensure we have at least basic structure for PDF generation\r\n        const safeData = {\r\n          totalCount: sanitizedReportData?.totalCount || 0,\r\n          summary: sanitizedReportData?.summary || {\r\n            message: 'No data available',\r\n          },\r\n          records: sanitizedReportData?.records || [],\r\n          statusDistribution: sanitizedReportData?.statusDistribution || [],\r\n          priorityDistribution: sanitizedReportData?.priorityDistribution || [],\r\n          ...sanitizedReportData,\r\n        };\r\n\r\n        const normalizedData = {\r\n          data: safeData,\r\n          metadata: reportData?.metadata || {\r\n            entityType,\r\n            format: 'pdf',\r\n            generatedAt: new Date().toISOString(),\r\n            generatedBy: 'system',\r\n            id: `${entityType}_${Date.now()}`,\r\n            type: 'aggregate',\r\n          },\r\n        };\r\n\r\n        // Final check to ensure we have something to render\r\n        if (Object.keys(normalizedData.data).length === 0) {\r\n          throw new Error(\r\n            'After sanitization, no valid data is available for PDF export.'\r\n          );\r\n        }\r\n\r\n        let PDFComponent;\r\n\r\n        const safeReportTitle = reportTitle || `${entityType} Report`;\r\n        const safeMetadata = normalizedData.metadata || {};\r\n\r\n        switch (entityType) {\r\n          case 'delegations': {\r\n            // Create safe filters object with proper structure\r\n            const safeFilters = {\r\n              dateRange: {\r\n                from: new Date(),\r\n                to: new Date(),\r\n              },\r\n              employees: [],\r\n              locations: [],\r\n              status: [],\r\n              vehicles: [],\r\n            };\r\n\r\n            PDFComponent = (\r\n              <DelegationReportDocument\r\n                data={normalizedData}\r\n                filters={safeFilters}\r\n                reportDate={new Date().toLocaleString()}\r\n                reportTitle={safeReportTitle}\r\n              />\r\n            );\r\n            break;\r\n          }\r\n          case 'employees': {\r\n            PDFComponent = (\r\n              <EmployeeReportDocument\r\n                data={normalizedData}\r\n                metadata={safeMetadata}\r\n                reportTitle={safeReportTitle}\r\n              />\r\n            );\r\n            break;\r\n          }\r\n          case 'tasks': {\r\n            PDFComponent = (\r\n              <TaskReportDocument\r\n                data={normalizedData}\r\n                metadata={safeMetadata}\r\n                reportTitle={safeReportTitle}\r\n              />\r\n            );\r\n            break;\r\n          }\r\n          case 'vehicles': {\r\n            PDFComponent = (\r\n              <VehicleReportDocument\r\n                data={normalizedData}\r\n                metadata={safeMetadata}\r\n                reportTitle={safeReportTitle}\r\n              />\r\n            );\r\n            break;\r\n          }\r\n          default: {\r\n            throw new Error(\r\n              `Unsupported entity type: ${entityType}. Supported types are: delegations, tasks, vehicles, employees`\r\n            );\r\n          }\r\n        }\r\n\r\n        console.log('Generating PDF with SANITIZED data:', normalizedData);\r\n        console.log('Entity Type:', entityType);\r\n        console.log('Raw data structure:', reportData);\r\n        console.log('Normalized data structure:', normalizedData);\r\n        console.log('PDF Component:', PDFComponent);\r\n        console.log('Entity type:', entityType);\r\n        console.log('Report title:', safeReportTitle);\r\n\r\n        // Generate PDF with additional error handling\r\n        let blob;\r\n        try {\r\n          console.log('Starting PDF blob generation...');\r\n          blob = await pdf(PDFComponent).toBlob();\r\n          console.log('PDF blob generated successfully:', {\r\n            size: blob.size,\r\n            type: blob.type,\r\n          });\r\n        } catch (pdfError: any) {\r\n          console.error('PDF generation failed:', pdfError);\r\n          console.error('PDF error stack:', pdfError.stack);\r\n          throw new Error(\r\n            `PDF generation failed: ${pdfError.message ?? 'Unknown PDF error'}`\r\n          );\r\n        }\r\n\r\n        // Verify blob is valid before attempting download\r\n        if (!blob || blob.size === 0) {\r\n          throw new Error('Generated PDF blob is empty or invalid');\r\n        }\r\n\r\n        console.log('Creating download link...');\r\n        const url = URL.createObjectURL(blob);\r\n        const link = document.createElement('a');\r\n        link.href = url;\r\n        const downloadFilename = `${filename || `${entityType}-report`}.pdf`;\r\n        link.setAttribute('download', downloadFilename);\r\n\r\n        console.log('Triggering download for:', downloadFilename);\r\n        document.body.append(link);\r\n        link.click();\r\n        link.remove();\r\n        URL.revokeObjectURL(url);\r\n        console.log('Download triggered successfully');\r\n      } catch (error: any) {\r\n        console.error('Report PDF export failed:', error);\r\n        setExportError(error.message || 'Failed to export report to PDF.');\r\n      } finally {\r\n        setIsExporting(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Export report data to Excel with proper formatting\r\n   */\r\n  const exportReportToExcel = useCallback(\r\n    (reportData: any, entityType: string, filename?: string) => {\r\n      setIsExporting(true);\r\n      setExportError(null);\r\n\r\n      try {\r\n        const workbook = XLSX.utils.book_new();\r\n        const data = reportData.data || reportData;\r\n\r\n        // Create summary sheet\r\n        if (data.summary || data.totalCount) {\r\n          const summaryData = [];\r\n          if (data.totalCount)\r\n            summaryData.push({ Metric: 'Total Count', Value: data.totalCount });\r\n          if (data.summary) {\r\n            for (const [key, value] of Object.entries(data.summary)) {\r\n              summaryData.push({\r\n                Metric: key\r\n                  .replaceAll(/([A-Z])/g, ' $1')\r\n                  .replace(/^./, str => str.toUpperCase()),\r\n                Value: value,\r\n              });\r\n            }\r\n          }\r\n          const summarySheet = XLSX.utils.json_to_sheet(summaryData);\r\n          XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');\r\n        }\r\n\r\n        // Create distribution sheets\r\n        if (data.statusDistribution) {\r\n          const statusSheet = XLSX.utils.json_to_sheet(data.statusDistribution);\r\n          XLSX.utils.book_append_sheet(\r\n            workbook,\r\n            statusSheet,\r\n            'Status Distribution'\r\n          );\r\n        }\r\n\r\n        if (data.priorityDistribution) {\r\n          const prioritySheet = XLSX.utils.json_to_sheet(\r\n            data.priorityDistribution\r\n          );\r\n          XLSX.utils.book_append_sheet(\r\n            workbook,\r\n            prioritySheet,\r\n            'Priority Distribution'\r\n          );\r\n        }\r\n\r\n        // Add entity-specific sheets\r\n        if (data.locationMetrics) {\r\n          const locationSheet = XLSX.utils.json_to_sheet(data.locationMetrics);\r\n          XLSX.utils.book_append_sheet(\r\n            workbook,\r\n            locationSheet,\r\n            'Location Metrics'\r\n          );\r\n        }\r\n\r\n        if (data.maintenanceMetrics) {\r\n          const maintenanceSheet = XLSX.utils.json_to_sheet([\r\n            data.maintenanceMetrics,\r\n          ]);\r\n          XLSX.utils.book_append_sheet(\r\n            workbook,\r\n            maintenanceSheet,\r\n            'Maintenance Metrics'\r\n          );\r\n        }\r\n\r\n        if (data.performanceMetrics) {\r\n          const performanceSheet = XLSX.utils.json_to_sheet([\r\n            data.performanceMetrics,\r\n          ]);\r\n          XLSX.utils.book_append_sheet(\r\n            workbook,\r\n            performanceSheet,\r\n            'Performance Metrics'\r\n          );\r\n        }\r\n\r\n        XLSX.writeFile(workbook, `${filename || `${entityType}-report`}.xlsx`);\r\n      } catch (error: any) {\r\n        console.error('Report Excel export failed:', error);\r\n        setExportError(error.message || 'Failed to export report to Excel.');\r\n      } finally {\r\n        setIsExporting(false);\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  return {\r\n    exportChartToPDF,\r\n    // ENHANCED: Advanced export methods\r\n    exportDashboardToPDF,\r\n    exportError,\r\n    exportReportToExcel,\r\n    // NEW: Report-specific export methods\r\n    exportReportToPDF,\r\n    exportToCSV,\r\n    exportToExcel,\r\n    exportToPDF,\r\n    isExporting,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA,gWAAuE,8BAA8B;AAArG;AAAA;AACA,yEAAyE;AACzE;AACA,kOAA8B,kCAAkC;AAQhE,8UAA4E,oCAAoC;AAChH;AACA;AACA;;;;;;;;;;AASO,MAAM,YAAY,CAAC,kBAAkB,QAAQ;;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D;;;;;GAKC,GACD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAC5B,OACE,MACA,SACA;YAEA,eAAe;YACf,eAAe;YACf,IAAI;gBACF,MAAM,OAAO,MAAM,CAAA,GAAA,+LAAA,CAAA,MAAG,AAAD,gBACnB,6LAAC,4LAAA,CAAA,2BAAwB;oBACvB,MAAM;oBACN,SAAS;oBACT,YAAY,IAAI,OAAO,cAAc;oBACrC,aAAa,QAAQ,QAAQ,IAAI;;;;;0BAEnC,MAAM;gBAER,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBACZ,KAAK,YAAY,CACf,YACA,GAAG,QAAQ,QAAQ,IAAI,gBAAgB,IAAI,CAAC;gBAE9C,SAAS,IAAI,CAAC,MAAM,CAAC;gBACrB,KAAK,KAAK;gBACV,KAAK,MAAM;gBACX,IAAI,eAAe,CAAC,MAAM,0BAA0B;YACtD,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,eAAe,MAAM,OAAO,IAAI;YAClC,SAAU;gBACR,eAAe;YACjB;QACF;6CACA;QAAC;KAAgB;IAGnB;;;;GAIC,GACD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAC9B,CAAC,MAA6B;YAC5B,eAAe;YACf,eAAe;YACf,IAAI;gBACF,MAAM,YAAY,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC;gBAC3C,MAAM,WAAW,gIAAA,CAAA,QAAU,CAAC,QAAQ;gBACpC,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,WAAW;gBAClD,CAAA,GAAA,gIAAA,CAAA,YAAc,AAAD,EAAE,UAAU,GAAG,QAAQ,QAAQ,IAAI,gBAAgB,KAAK,CAAC;YACxE,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,eAAe,MAAM,OAAO,IAAI;YAClC,SAAU;gBACR,eAAe;YACjB;QACF;+CACA;QAAC;KAAgB;IAGnB;;;;GAIC,GACD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAC5B,CAAC,MAA6B;YAC5B,eAAe;YACf,eAAe;YACf,IAAI;gBACF,MAAM,SAAS,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;gBAC/C,MAAM,OAAO,KAAK,GAAG;+DAAC,CAAA,MACpB,OAAO,MAAM,CAAC,KACX,GAAG;uEAAC,CAAA,QAAS,CAAC,CAAC,EAAE,OAAO,OAAO,UAAU,CAAC,KAAK,MAAM,CAAC,CAAC;sEACvD,IAAI,CAAC;;gBAEV,MAAM,aAAa;oBAAC;uBAAW;iBAAK,CAAC,IAAI,CAAC;gBAC1C,MAAM,OAAO,IAAI,KAAK;oBAAC;iBAAW,EAAE;oBAClC,MAAM;gBACR;gBACA,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,IAAI,KAAK,QAAQ,KAAK,WAAW;oBAC/B,MAAM,MAAM,IAAI,eAAe,CAAC;oBAChC,KAAK,YAAY,CAAC,QAAQ;oBAC1B,KAAK,YAAY,CACf,YACA,GAAG,QAAQ,QAAQ,IAAI,gBAAgB,IAAI,CAAC;oBAE9C,KAAK,KAAK,CAAC,UAAU,GAAG;oBACxB,SAAS,IAAI,CAAC,MAAM,CAAC;oBACrB,KAAK,KAAK;oBACV,KAAK,MAAM;gBACb;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,eAAe,MAAM,OAAO,IAAI;YAClC,SAAU;gBACR,eAAe;YACjB;QACF;6CACA;QAAC;KAAgB;IAGnB,8CAA8C;IAC9C,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDACrC,OACE,MACA,SACA;YAEA,eAAe;YACf,eAAe;YAEf,IAAI;gBACF,0DAA0D;gBAC1D,MAAM,OAAO,MAAM,CAAA,GAAA,+LAAA,CAAA,MAAG,AAAD,gBACnB,6LAAC,4LAAA,CAAA,2BAAwB;oBACvB,MAAM;oBACN,SAAS;oBACT,eAAe;oBACf,uBAAuB,QAAQ,QAAQ,qBAAqB;oBAC5D,YAAY,IAAI,OAAO,cAAc;oBACrC,aAAa,YAAY;;;;;0BAE3B,MAAM;gBAER,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBACZ,KAAK,YAAY,CAAC,YAAY,GAAG,YAAY,mBAAmB,IAAI,CAAC;gBACrE,SAAS,IAAI,CAAC,MAAM,CAAC;gBACrB,KAAK,KAAK;gBACV,KAAK,MAAM;gBACX,IAAI,eAAe,CAAC;YACtB,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,eAAe,MAAM,OAAO,IAAI;YAClC,SAAU;gBACR,eAAe;YACjB;QACF;sDACA,EAAE;IAGJ,sDAAsD;IACtD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDACjC,OAAO,WAAgB,OAAe;YACpC,eAAe;YACf,eAAe;YAEf,IAAI;gBACF,8CAA8C;gBAC9C,MAAM;6EAAgB,kBACpB,6LAAC,+JAAA,CAAA,WAAQ;sCACP,cAAA,6LAAC,+JAAA,CAAA,OAAI;gCAAC,aAAY;gCAAY,MAAK;gCAAK,OAAO;oCAAE,SAAS;gCAAG;;kDAC3D,6LAAC,+JAAA,CAAA,OAAI;wCAAC,OAAO;4CAAE,cAAc;wCAAG;;0DAC9B,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO;oDAAE,UAAU;oDAAI,YAAY;gDAAO;0DAC7C;;;;;;0DAEH,6LAAC,+JAAA,CAAA,OAAI;gDAAC,OAAO;oDAAE,UAAU;oDAAI,WAAW;gDAAE;;oDAAG;oDAC5B,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;kDAGhD,6LAAC,+JAAA,CAAA,OAAI;wCACH,OAAO;4CACL,YAAY;4CACZ,MAAM;4CACN,gBAAgB;wCAClB;kDAEA,cAAA,6LAAC,+JAAA,CAAA,OAAI;4CAAC,OAAO;gDAAE,UAAU;4CAAG;;gDAAG;gDAChB,KAAK,SAAS,CAAC,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;gBAOvD,MAAM,OAAO,MAAM,CAAA,GAAA,+LAAA,CAAA,MAAG,AAAD,gBAAE,6LAAC;;;;0BAAkB,MAAM;gBAEhD,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBACZ,KAAK,YAAY,CACf,YACA,YAAY,GAAG,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,KAAK,IAAI,CAAC;gBAElE,SAAS,IAAI,CAAC,MAAM,CAAC;gBACrB,KAAK,KAAK;gBACV,KAAK,MAAM;gBACX,IAAI,eAAe,CAAC;YACtB,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,eAAe,MAAM,OAAO,IAAI;YAClC,SAAU;gBACR,eAAe;YACjB;QACF;kDACA,EAAE;IAGJ;;GAEC,GACD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAClC,OACE,YACA,YACA,aACA;YAEA,eAAe;YACf,eAAe;YAEf,wCAAwC;YACxC,MAAM;yEAAe,CAAC;oBACpB,IAAI,SAAS,QAAQ,SAAS,WAAW;wBACvC,OAAO,MAAM,0DAA0D;oBACzE;oBACA,IAAI,MAAM,OAAO,CAAC,OAAO;wBACvB,OAAO,KAAK,GAAG,CAAC,eAAe,sCAAsC;oBACvE;oBACA,IAAI,OAAO,SAAS,YAAY,KAAK,WAAW,KAAK,QAAQ;wBAC3D,MAAM,kBAAuC,CAAC;wBAC9C,IAAK,MAAM,OAAO,KAAM;4BACtB,mEAAmE;4BACnE,IACE,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,QAC3C,QAAQ,eACR,QAAQ,eACR;gCACA,MAAM,iBAAiB,aAAa,IAAI,CAAC,IAAI;gCAC7C,oCAAoC;gCACpC,IAAI,mBAAmB,WAAW;oCAChC,eAAe,CAAC,IAAI,GAAG;gCACzB;4BACF;wBACF;wBACA,OAAO;oBACT;oBACA,8DAA8D;oBAC9D,IAAI,OAAO,SAAS,cAAc,OAAO,SAAS,UAAU;wBAC1D,OAAO;oBACT;oBACA,oDAAoD;oBACpD,OAAO;gBACT;;YACA,sCAAsC;YAEtC,IAAI;gBACF,IAAI,CAAC,YAAY;oBACf,MAAM,IAAI,MAAM;gBAClB;gBAEA,qDAAqD;gBACrD,MAAM,UAAU,YAAY,QAAQ,cAAc,CAAC;gBAEnD,mDAAmD;gBACnD,MAAM,sBAAsB,aAAa;gBAEzC,6DAA6D;gBAC7D,MAAM,WAAW;oBACf,YAAY,qBAAqB,cAAc;oBAC/C,SAAS,qBAAqB,WAAW;wBACvC,SAAS;oBACX;oBACA,SAAS,qBAAqB,WAAW,EAAE;oBAC3C,oBAAoB,qBAAqB,sBAAsB,EAAE;oBACjE,sBAAsB,qBAAqB,wBAAwB,EAAE;oBACrE,GAAG,mBAAmB;gBACxB;gBAEA,MAAM,iBAAiB;oBACrB,MAAM;oBACN,UAAU,YAAY,YAAY;wBAChC;wBACA,QAAQ;wBACR,aAAa,IAAI,OAAO,WAAW;wBACnC,aAAa;wBACb,IAAI,GAAG,WAAW,CAAC,EAAE,KAAK,GAAG,IAAI;wBACjC,MAAM;oBACR;gBACF;gBAEA,oDAAoD;gBACpD,IAAI,OAAO,IAAI,CAAC,eAAe,IAAI,EAAE,MAAM,KAAK,GAAG;oBACjD,MAAM,IAAI,MACR;gBAEJ;gBAEA,IAAI;gBAEJ,MAAM,kBAAkB,eAAe,GAAG,WAAW,OAAO,CAAC;gBAC7D,MAAM,eAAe,eAAe,QAAQ,IAAI,CAAC;gBAEjD,OAAQ;oBACN,KAAK;wBAAe;4BAClB,mDAAmD;4BACnD,MAAM,cAAc;gCAClB,WAAW;oCACT,MAAM,IAAI;oCACV,IAAI,IAAI;gCACV;gCACA,WAAW,EAAE;gCACb,WAAW,EAAE;gCACb,QAAQ,EAAE;gCACV,UAAU,EAAE;4BACd;4BAEA,6BACE,6LAAC,4LAAA,CAAA,2BAAwB;gCACvB,MAAM;gCACN,SAAS;gCACT,YAAY,IAAI,OAAO,cAAc;gCACrC,aAAa;;;;;;4BAGjB;wBACF;oBACA,KAAK;wBAAa;4BAChB,6BACE,6LAAC,0LAAA,CAAA,yBAAsB;gCACrB,MAAM;gCACN,UAAU;gCACV,aAAa;;;;;;4BAGjB;wBACF;oBACA,KAAK;wBAAS;4BACZ,6BACE,6LAAC,sLAAA,CAAA,qBAAkB;gCACjB,MAAM;gCACN,UAAU;gCACV,aAAa;;;;;;4BAGjB;wBACF;oBACA,KAAK;wBAAY;4BACf,6BACE,6LAAC,yLAAA,CAAA,wBAAqB;gCACpB,MAAM;gCACN,UAAU;gCACV,aAAa;;;;;;4BAGjB;wBACF;oBACA;wBAAS;4BACP,MAAM,IAAI,MACR,CAAC,yBAAyB,EAAE,WAAW,8DAA8D,CAAC;wBAE1G;gBACF;gBAEA,QAAQ,GAAG,CAAC,uCAAuC;gBACnD,QAAQ,GAAG,CAAC,gBAAgB;gBAC5B,QAAQ,GAAG,CAAC,uBAAuB;gBACnC,QAAQ,GAAG,CAAC,8BAA8B;gBAC1C,QAAQ,GAAG,CAAC,kBAAkB;gBAC9B,QAAQ,GAAG,CAAC,gBAAgB;gBAC5B,QAAQ,GAAG,CAAC,iBAAiB;gBAE7B,8CAA8C;gBAC9C,IAAI;gBACJ,IAAI;oBACF,QAAQ,GAAG,CAAC;oBACZ,OAAO,MAAM,CAAA,GAAA,+LAAA,CAAA,MAAG,AAAD,EAAE,cAAc,MAAM;oBACrC,QAAQ,GAAG,CAAC,oCAAoC;wBAC9C,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;oBACjB;gBACF,EAAE,OAAO,UAAe;oBACtB,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,QAAQ,KAAK,CAAC,oBAAoB,SAAS,KAAK;oBAChD,MAAM,IAAI,MACR,CAAC,uBAAuB,EAAE,SAAS,OAAO,IAAI,qBAAqB;gBAEvE;gBAEA,kDAAkD;gBAClD,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,GAAG;oBAC5B,MAAM,IAAI,MAAM;gBAClB;gBAEA,QAAQ,GAAG,CAAC;gBACZ,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBACZ,MAAM,mBAAmB,GAAG,YAAY,GAAG,WAAW,OAAO,CAAC,CAAC,IAAI,CAAC;gBACpE,KAAK,YAAY,CAAC,YAAY;gBAE9B,QAAQ,GAAG,CAAC,4BAA4B;gBACxC,SAAS,IAAI,CAAC,MAAM,CAAC;gBACrB,KAAK,KAAK;gBACV,KAAK,MAAM;gBACX,IAAI,eAAe,CAAC;gBACpB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,eAAe,MAAM,OAAO,IAAI;YAClC,SAAU;gBACR,eAAe;YACjB;QACF;mDACA,EAAE;IAGJ;;GAEC,GACD,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDACpC,CAAC,YAAiB,YAAoB;YACpC,eAAe;YACf,eAAe;YAEf,IAAI;gBACF,MAAM,WAAW,gIAAA,CAAA,QAAU,CAAC,QAAQ;gBACpC,MAAM,OAAO,WAAW,IAAI,IAAI;gBAEhC,uBAAuB;gBACvB,IAAI,KAAK,OAAO,IAAI,KAAK,UAAU,EAAE;oBACnC,MAAM,cAAc,EAAE;oBACtB,IAAI,KAAK,UAAU,EACjB,YAAY,IAAI,CAAC;wBAAE,QAAQ;wBAAe,OAAO,KAAK,UAAU;oBAAC;oBACnE,IAAI,KAAK,OAAO,EAAE;wBAChB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAK,OAAO,EAAG;4BACvD,YAAY,IAAI,CAAC;gCACf,QAAQ,IACL,UAAU,CAAC,YAAY,OACvB,OAAO,CAAC;kFAAM,CAAA,MAAO,IAAI,WAAW;;gCACvC,OAAO;4BACT;wBACF;oBACF;oBACA,MAAM,eAAe,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC;oBAC9C,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,cAAc;gBACvD;gBAEA,6BAA6B;gBAC7B,IAAI,KAAK,kBAAkB,EAAE;oBAC3B,MAAM,cAAc,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC,KAAK,kBAAkB;oBACpE,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAC1B,UACA,aACA;gBAEJ;gBAEA,IAAI,KAAK,oBAAoB,EAAE;oBAC7B,MAAM,gBAAgB,gIAAA,CAAA,QAAU,CAAC,aAAa,CAC5C,KAAK,oBAAoB;oBAE3B,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAC1B,UACA,eACA;gBAEJ;gBAEA,6BAA6B;gBAC7B,IAAI,KAAK,eAAe,EAAE;oBACxB,MAAM,gBAAgB,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC,KAAK,eAAe;oBACnE,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAC1B,UACA,eACA;gBAEJ;gBAEA,IAAI,KAAK,kBAAkB,EAAE;oBAC3B,MAAM,mBAAmB,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC;wBAChD,KAAK,kBAAkB;qBACxB;oBACD,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAC1B,UACA,kBACA;gBAEJ;gBAEA,IAAI,KAAK,kBAAkB,EAAE;oBAC3B,MAAM,mBAAmB,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC;wBAChD,KAAK,kBAAkB;qBACxB;oBACD,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAC1B,UACA,kBACA;gBAEJ;gBAEA,CAAA,GAAA,gIAAA,CAAA,YAAc,AAAD,EAAE,UAAU,GAAG,YAAY,GAAG,WAAW,OAAO,CAAC,CAAC,KAAK,CAAC;YACvE,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,eAAe,MAAM,OAAO,IAAI;YAClC,SAAU;gBACR,eAAe;YACjB;QACF;qDACA,EAAE;IAGJ,OAAO;QACL;QACA,oCAAoC;QACpC;QACA;QACA;QACA,sCAAsC;QACtC;QACA;QACA;QACA;QACA;IACF;AACF;GAnhBa", "debugId": null}}]}