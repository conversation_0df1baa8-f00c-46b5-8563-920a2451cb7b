"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[396],{3638:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},12543:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},14347:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("CirclePlay",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polygon",{points:"10 8 16 12 10 16 10 8",key:"1cimsy"}]])},17649:(e,t,a)=>{a.d(t,{UC:()=>P,VY:()=>Z,ZD:()=>S,ZL:()=>q,bL:()=>R,hE:()=>T,hJ:()=>z,l9:()=>V,rc:()=>E});var r=a(12115),l=a(46081),d=a(6101),i=a(15452),n=a(85185),o=a(99708),c=a(95155),s="AlertDialog",[h,y]=(0,l.A)(s,[i.Hs]),p=(0,i.Hs)(),u=e=>{let{__scopeAlertDialog:t,...a}=e,r=p(t);return(0,c.jsx)(i.bL,{...r,...a,modal:!0})};u.displayName=s;var k=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=p(a);return(0,c.jsx)(i.l9,{...l,...r,ref:t})});k.displayName="AlertDialogTrigger";var g=e=>{let{__scopeAlertDialog:t,...a}=e,r=p(t);return(0,c.jsx)(i.ZL,{...r,...a})};g.displayName="AlertDialogPortal";var A=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=p(a);return(0,c.jsx)(i.hJ,{...l,...r,ref:t})});A.displayName="AlertDialogOverlay";var v="AlertDialogContent",[f,m]=h(v),M=(0,o.Dc)("AlertDialogContent"),x=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,children:l,...o}=e,s=p(a),h=r.useRef(null),y=(0,d.s)(t,h),u=r.useRef(null);return(0,c.jsx)(i.G$,{contentName:v,titleName:b,docsSlug:"alert-dialog",children:(0,c.jsx)(f,{scope:a,cancelRef:u,children:(0,c.jsxs)(i.UC,{role:"alertdialog",...s,...o,ref:y,onOpenAutoFocus:(0,n.m)(o.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=u.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,c.jsx)(M,{children:l}),(0,c.jsx)(N,{contentRef:h})]})})})});x.displayName=v;var b="AlertDialogTitle",w=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=p(a);return(0,c.jsx)(i.hE,{...l,...r,ref:t})});w.displayName=b;var j="AlertDialogDescription",D=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=p(a);return(0,c.jsx)(i.VY,{...l,...r,ref:t})});D.displayName=j;var C=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,l=p(a);return(0,c.jsx)(i.bm,{...l,...r,ref:t})});C.displayName="AlertDialogAction";var H="AlertDialogCancel",L=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,{cancelRef:l}=m(H,a),n=p(a),o=(0,d.s)(t,l);return(0,c.jsx)(i.bm,{...n,...r,ref:o})});L.displayName=H;var N=e=>{let{contentRef:t}=e,a="`".concat(v,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(v,"` by passing a `").concat(j,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(v,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(a)},[a,t]),null},R=u,V=k,q=g,z=A,P=x,E=C,S=L,T=w,Z=D},18018:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},18046:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},18763:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},24371:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},24386:(e,t,a)=>{a.d(t,{c:()=>d});var r=a(39140),l=a(35476);function d(e,t){let a=(0,l.a)(e),d=(0,l.a)(t),n=i(a,d),o=Math.abs((0,r.m)(a,d));a.setDate(a.getDate()-n*o);let c=Number(i(a,d)===-n),s=n*(o-c);return 0===s?0:s}function i(e,t){let a=e.getFullYear()-t.getFullYear()||e.getMonth()-t.getMonth()||e.getDate()-t.getDate()||e.getHours()-t.getHours()||e.getMinutes()-t.getMinutes()||e.getSeconds()-t.getSeconds()||e.getMilliseconds()-t.getMilliseconds();return a<0?-1:a>0?1:a}},31949:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},35079:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("ClipboardList",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},37648:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},45731:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},50286:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},50594:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},51920:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},55414:(e,t,a)=>{a.d(t,{b:()=>n});var r=a(12115),l=a(63655),d=a(95155),i=r.forwardRef((e,t)=>{let{ratio:a=1,style:r,...i}=e;return(0,d.jsx)("div",{style:{position:"relative",width:"100%",paddingBottom:`${100/a}%`},"data-radix-aspect-ratio-wrapper":"",children:(0,d.jsx)(l.sG.div,{...i,ref:t,style:{...r,position:"absolute",top:0,right:0,bottom:0,left:0}})})});i.displayName="AspectRatio";var n=i},57082:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58127:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},58260:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},74465:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("PlaneTakeoff",[["path",{d:"M2 22h20",key:"272qi7"}],["path",{d:"M6.36 17.4 4 17l-2-4 1.1-.55a2 2 0 0 1 1.8 0l.17.1a2 2 0 0 0 1.8 0L8 12 5 6l.9-.45a2 2 0 0 1 2.09.2l4.02 3a2 2 0 0 0 2.1.2l4.19-2.06a2.41 2.41 0 0 1 1.73-.17L21 7a1.4 1.4 0 0 1 .87 1.99l-.38.76c-.23.46-.6.84-1.07 1.08L7.58 17.2a2 2 0 0 1-1.22.18Z",key:"fkigj9"}]])},77223:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},83082:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("PlaneLanding",[["path",{d:"M2 22h20",key:"272qi7"}],["path",{d:"M3.77 10.77 2 9l2-4.5 1.1.55c.55.28.9.84.9 1.45s.35 1.17.9 1.45L8 8.5l3-6 1.05.53a2 2 0 0 1 1.09 1.52l.72 5.4a2 2 0 0 0 1.09 1.52l4.4 2.2c.42.22.78.55 1.01.96l.6 1.03c.49.88-.06 1.98-1.06 2.1l-1.18.15c-.47.06-.95-.02-1.37-.24L4.29 11.15a2 2 0 0 1-.52-.38Z",key:"1ma21e"}]])},85268:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},91721:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])}}]);