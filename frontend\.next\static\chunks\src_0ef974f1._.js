(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/progress.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Progress": (()=>Progress)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$progress$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-progress/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-client] (ecmascript) <locals>");
'use client';
;
;
;
;
const Progress = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, value, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$progress$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('relative h-4 w-full overflow-hidden rounded-full bg-secondary', className),
        ref: ref,
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$progress$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Indicator"], {
            className: "size-full flex-1 bg-primary transition-all",
            style: {
                transform: `translateX(-${100 - (value || 0)}%)`
            }
        }, void 0, false, {
            fileName: "[project]/src/components/ui/progress.tsx",
            lineNumber: 20,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/progress.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this));
_c1 = Progress;
Progress.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$progress$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"].displayName;
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Progress$React.forwardRef");
__turbopack_context__.k.register(_c1, "Progress");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/services/WebSocketManager.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Unified WebSocket Manager for WorkHub Application
 * Provides centralized WebSocket connection management with domain-specific channels
 * Follows SRP and DRY principles with smart fallback strategies
 * @module services/WebSocketManager
 */ __turbopack_context__.s({
    "WebSocketManager": (()=>WebSocketManager),
    "getWebSocketManager": (()=>getWebSocketManager),
    "useWebSocketState": (()=>useWebSocketState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/TokenRefreshService.ts [app-client] (ecmascript)");
;
;
;
class WebSocketManager {
    static instance = null;
    config;
    connectionState = 'disconnected';
    reconnectAttempts = 0;
    socket = null;
    stateListeners = new Set();
    subscriptions = new Map();
    constructor(config = {}){
        this.config = {
            autoConnect: config.autoConnect ?? true,
            reconnectAttempts: config.reconnectAttempts ?? 5,
            reconnectDelay: config.reconnectDelay ?? 1000,
            timeout: config.timeout ?? 10_000,
            url: config.url ?? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_WEBSOCKET_URL ?? 'http://localhost:3001'
        };
        if (this.config.autoConnect) {
            this.connect();
        }
        // Subscribe to token refresh events
        this.setupTokenRefreshHandling();
    }
    /**
   * Get singleton instance
   */ static getInstance(config) {
        WebSocketManager.instance ??= new WebSocketManager(config);
        return WebSocketManager.instance;
    }
    /**
   * Connect to WebSocket server
   */ async connect() {
        if (this.socket?.connected) {
            console.debug('WebSocket already connected');
            return;
        }
        this.setConnectionState('connecting');
        try {
            // Get current session and token for authentication
            const { data: { session }, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supabase"].auth.getSession();
            if (error) {
                console.warn('Failed to get session for WebSocket connection:', error);
            }
            const connectionOptions = {
                forceNew: true,
                timeout: this.config.timeout,
                transports: [
                    'websocket',
                    'polling'
                ],
                withCredentials: true
            };
            // Add authentication token if available
            if (session?.access_token) {
                connectionOptions.auth = {
                    token: session.access_token
                };
                console.debug('🔐 WebSocket connecting with authentication token');
                // Validate token expiration
                const tokenExpiry = session.expires_at ? session.expires_at * 1000 : 0;
                const now = Date.now();
                const timeUntilExpiry = tokenExpiry - now;
                if (timeUntilExpiry <= 60_000) {
                    // Less than 1 minute
                    console.warn('⚠️ WebSocket token expires soon, may need refresh');
                }
            } else {
                console.warn('⚠️ WebSocket connecting without authentication token - connection may fail');
            }
            this.socket = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(this.config.url, connectionOptions);
            this.setupEventHandlers();
        } catch (error) {
            console.error('Failed to connect WebSocket:', error);
            this.setConnectionState('error');
            this.scheduleReconnect();
        }
    }
    /**
   * Cleanup resources
   */ destroy() {
        this.disconnect();
        this.subscriptions.clear();
        this.stateListeners.clear();
        WebSocketManager.instance = null;
    }
    /**
   * Disconnect from WebSocket server
   */ disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
        this.setConnectionState('disconnected');
        this.reconnectAttempts = 0;
    }
    /**
   * Emit event to specific domain channel
   */ emit(channel, event, data) {
        if (!this.socket?.connected) {
            console.warn(`Cannot emit ${channel}:${event} - WebSocket not connected`);
            return;
        }
        this.socket.emit(event, data);
    }
    /**
   * Get current connection state
   */ getConnectionState() {
        return this.connectionState;
    }
    /**
   * Check if WebSocket is connected
   */ isConnected() {
        return this.connectionState === 'connected' && this.socket?.connected === true;
    }
    /**
   * Join domain-specific room
   */ joinRoom(room) {
        if (!this.socket?.connected) {
            console.warn(`Cannot join room ${room} - WebSocket not connected`);
            return;
        }
        this.socket.emit('join-room', room);
    }
    /**
   * Leave domain-specific room
   */ leaveRoom(room) {
        if (!this.socket?.connected) {
            return;
        }
        this.socket.emit('leave-room', room);
    }
    /**
   * Subscribe to connection state changes
   */ onStateChange(callback) {
        this.stateListeners.add(callback);
        return ()=>{
            this.stateListeners.delete(callback);
        };
    }
    /**
   * Subscribe to domain-specific events
   */ subscribe(channel, event, callback) {
        const eventKey = `${channel}:${event}`;
        if (!this.subscriptions.has(eventKey)) {
            this.subscriptions.set(eventKey, new Set());
        }
        this.subscriptions.get(eventKey).add(callback);
        // Set up socket listener if connected
        if (this.socket?.connected && event) {
            this.socket.on(event, callback);
        }
        // Return unsubscribe function
        return ()=>{
            const callbacks = this.subscriptions.get(eventKey);
            if (callbacks) {
                callbacks.delete(callback);
                if (callbacks.size === 0) {
                    this.subscriptions.delete(eventKey);
                }
            }
            if (this.socket && event) {
                this.socket.off(event, callback);
            }
        };
    }
    /**
   * Handle authentication errors by triggering token refresh
   */ handleAuthenticationError() {
        const tokenRefreshService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTokenRefreshService"])();
        console.log('🔐 Handling WebSocket authentication error...');
        // Disconnect current socket to prevent further auth errors
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
        // Attempt to refresh token
        tokenRefreshService.refreshNow().then((success)=>{
            if (success) {
                console.log('🔄 Token refresh successful, retrying WebSocket connection');
            // The reconnection will be handled by setupTokenRefreshHandling
            } else {
                console.error('🔄 Token refresh failed, scheduling normal reconnect');
                this.scheduleReconnect();
            }
        }).catch((error)=>{
            console.error('🔄 Token refresh error:', error);
            this.scheduleReconnect();
        });
    }
    /**
   * Resubscribe to all events after reconnection
   */ resubscribeToEvents() {
        if (!this.socket) return;
        for (const [eventKey, callbacks] of this.subscriptions){
            const [, event] = eventKey.split(':');
            for (const callback of callbacks){
                if (event) {
                    this.socket.on(event, callback);
                }
            }
        }
    }
    /**
   * Schedule reconnection with exponential backoff
   */ scheduleReconnect() {
        if (this.reconnectAttempts >= this.config.reconnectAttempts) {
            console.error('Max reconnection attempts reached');
            this.setConnectionState('error');
            return;
        }
        this.setConnectionState('reconnecting');
        this.reconnectAttempts++;
        const delay = this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        setTimeout(()=>{
            console.info(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts})`);
            this.connect();
        }, delay);
    }
    /**
   * Set connection state and notify listeners
   */ setConnectionState(state) {
        if (this.connectionState !== state) {
            this.connectionState = state;
            for (const listener of this.stateListeners)listener(state);
        }
    }
    /**
   * Setup socket event handlers
   */ setupEventHandlers() {
        if (!this.socket) return;
        this.socket.on('connect', ()=>{
            console.info('WebSocket connected');
            this.setConnectionState('connected');
            this.reconnectAttempts = 0;
            this.resubscribeToEvents();
        });
        this.socket.on('disconnect', (reason)=>{
            console.warn('WebSocket disconnected:', reason);
            this.setConnectionState('disconnected');
            if (reason === 'io server disconnect') {
                // Server initiated disconnect, don't reconnect automatically
                return;
            }
            this.scheduleReconnect();
        });
        this.socket.on('connect_error', (error)=>{
            console.error('WebSocket connection error:', error);
            this.setConnectionState('error');
            // Check if error is authentication-related
            if (error.message?.includes('Authentication') || error.message?.includes('token') || error.message?.includes('No token provided') || error.message?.includes('Unauthorized')) {
                console.warn('🔐 Authentication error detected, attempting token refresh');
                this.handleAuthenticationError();
            } else {
                this.scheduleReconnect();
            }
        });
        // Listen for authentication errors from the server
        this.socket.on('auth_error', (errorData)=>{
            console.error('🔐 Server authentication error:', errorData);
            this.handleAuthenticationError();
        });
        // Listen for token refresh requests from server
        this.socket.on('token_refresh_required', ()=>{
            console.warn('🔄 Server requested token refresh');
            this.handleAuthenticationError();
        });
    }
    /**
   * Setup token refresh event handling
   */ setupTokenRefreshHandling() {
        const tokenRefreshService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTokenRefreshService"])();
        tokenRefreshService.subscribe((event, _data)=>{
            switch(event){
                case 'critical_refresh_failed':
                    {
                        console.error('🔄 Critical token refresh failure, disconnecting WebSocket');
                        this.disconnect();
                        this.setConnectionState('error');
                        break;
                    }
                case 'refresh_failed':
                    {
                        console.error('🔄 Token refresh failed, WebSocket may lose connection');
                        break;
                    }
                case 'refresh_success':
                    {
                        console.log('🔄 Token refreshed, reconnecting WebSocket with new token');
                        // Disconnect current connection and reconnect with new token
                        if (this.socket) {
                            this.socket.disconnect();
                            this.socket = null;
                        }
                        // Reconnect with fresh token
                        setTimeout(()=>this.connect(), 500);
                        break;
                    }
            }
        });
    }
}
const getWebSocketManager = (config)=>{
    return WebSocketManager.getInstance(config);
};
const useWebSocketState = ()=>{
    const manager = getWebSocketManager();
    return {
        connectionState: manager.getConnectionState(),
        isConnected: manager.isConnected()
    };
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/api/useSmartQuery.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Smart Query Hook with WebSocket Integration
 * Automatically disables polling when WebSocket is connected
 * Follows modern best practices for real-time data management
 * @module hooks/useSmartQuery
 */ __turbopack_context__.s({
    "useCrudQuery": (()=>useCrudQuery),
    "useNotificationQuery": (()=>useNotificationQuery),
    "useReliabilityQuery": (()=>useReliabilityQuery),
    "useSmartQuery": (()=>useSmartQuery),
    "useSystemQuery": (()=>useSystemQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$WebSocketManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/WebSocketManager.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature();
;
;
;
/**
 * Mapping of domain channels to Socket.IO room names
 * This ensures the frontend joins the correct rooms that the backend emits events to
 */ const CHANNEL_ROOM_MAPPING = {
    crud: 'entity-updates',
    notifications: 'notifications-monitoring',
    reliability: 'reliability-monitoring',
    system: 'system-monitoring'
};
function useCrudQuery(queryKey, queryFn, entityType, options) {
    _s();
    return useSmartQuery(queryKey, queryFn, {
        channel: 'crud',
        events: [
            `${entityType}:created`,
            `${entityType}:updated`,
            `${entityType}:deleted`,
            `refresh:${entityType}`
        ],
        fallbackInterval: 30_000
    }, options);
}
_s(useCrudQuery, "3cVlcIAwCvBXE2sGv8BwBFtyOC4=", false, function() {
    return [
        useSmartQuery
    ];
});
function useNotificationQuery(queryKey, queryFn, options) {
    _s1();
    return useSmartQuery(queryKey, queryFn, {
        channel: 'notifications',
        events: [
            'notification-created',
            'notification-updated'
        ],
        fallbackInterval: 60_000
    }, options);
}
_s1(useNotificationQuery, "3cVlcIAwCvBXE2sGv8BwBFtyOC4=", false, function() {
    return [
        useSmartQuery
    ];
});
function useReliabilityQuery(queryKey, queryFn, monitoringType, options) {
    _s2();
    // Increased intervals to reduce aggressive polling and cancellations
    const intervalMap = {
        alerts: 30_000,
        'circuit-breakers': 60_000,
        health: 45_000,
        metrics: 60_000
    };
    const webSocketManager = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$WebSocketManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWebSocketManager"])();
    // Join reliability monitoring room when WebSocket is connected
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useReliabilityQuery.useEffect": ()=>{
            if (webSocketManager.isConnected()) {
                console.debug(`[ReliabilityQuery] Joining reliability-monitoring room for ${monitoringType}`);
                webSocketManager.joinRoom('reliability-monitoring');
            }
            // Subscribe to connection state changes to join room when connected
            const unsubscribe = webSocketManager.onStateChange({
                "useReliabilityQuery.useEffect.unsubscribe": (state)=>{
                    if (state === 'connected') {
                        console.debug(`[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ${monitoringType}`);
                        webSocketManager.joinRoom('reliability-monitoring');
                    }
                }
            }["useReliabilityQuery.useEffect.unsubscribe"]);
            return ({
                "useReliabilityQuery.useEffect": ()=>{
                    unsubscribe();
                    // Leave room when component unmounts
                    if (webSocketManager.isConnected()) {
                        webSocketManager.leaveRoom('reliability-monitoring');
                    }
                }
            })["useReliabilityQuery.useEffect"];
        }
    }["useReliabilityQuery.useEffect"], [
        webSocketManager,
        monitoringType
    ]);
    return useSmartQuery(queryKey, queryFn, {
        channel: 'reliability',
        events: [
            `${monitoringType}-update`,
            `${monitoringType}-created`,
            `${monitoringType}-resolved`
        ],
        fallbackInterval: intervalMap[monitoringType]
    }, options);
}
_s2(useReliabilityQuery, "yhYNlEYOvHF6ilFRL+Cp7gEYRTw=", false, function() {
    return [
        useSmartQuery
    ];
});
function useSmartQuery(queryKey, queryFn, config, options) {
    _s3();
    const { channel, enableFallback = true, enableWebSocket = true, events, fallbackInterval = 30_000 } = config;
    const [isWebSocketConnected, setIsWebSocketConnected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const webSocketManager = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$WebSocketManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWebSocketManager"])();
    // Track WebSocket connection state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSmartQuery.useEffect": ()=>{
            const updateConnectionState = {
                "useSmartQuery.useEffect.updateConnectionState": ()=>{
                    setIsWebSocketConnected(webSocketManager.isConnected());
                }
            }["useSmartQuery.useEffect.updateConnectionState"];
            // Initial state
            updateConnectionState();
            // Subscribe to state changes
            const unsubscribe = webSocketManager.onStateChange(updateConnectionState);
            return unsubscribe;
        }
    }["useSmartQuery.useEffect"], [
        webSocketManager
    ]);
    // Determine if we should use fallback polling
    const isUsingFallback = enableFallback && (!enableWebSocket || !isWebSocketConnected);
    // Configure React Query options based on WebSocket state
    const queryOptions = {
        // Longer cache time for better performance
        gcTime: 10 * 60 * 1000,
        queryFn,
        queryKey,
        // Disable polling when WebSocket is connected
        refetchInterval: isUsingFallback ? fallbackInterval : false,
        refetchOnReconnect: true,
        // Enable background refetch only when using fallback
        refetchOnWindowFocus: isUsingFallback,
        // Shorter stale time when using WebSocket (real-time updates)
        staleTime: isWebSocketConnected ? 0 : 30_000,
        ...options
    };
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const queryResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])(queryOptions);
    // Manage Socket.IO room joining/leaving based on channel
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSmartQuery.useEffect": ()=>{
            if (!enableWebSocket || !isWebSocketConnected) {
                return;
            }
            const roomName = CHANNEL_ROOM_MAPPING[channel];
            if (!roomName) {
                console.warn(`[SmartQuery] No room mapping found for channel: ${channel}`);
                return;
            }
            // Join the appropriate room for this channel
            try {
                webSocketManager.joinRoom(roomName);
                console.log(`[SmartQuery] Joined room: ${roomName} for channel: ${channel}`);
            } catch (error) {
                console.error(`[SmartQuery] Failed to join room ${roomName}:`, error);
            }
            // Cleanup: leave room when component unmounts or dependencies change
            return ({
                "useSmartQuery.useEffect": ()=>{
                    try {
                        webSocketManager.leaveRoom(roomName);
                        console.log(`[SmartQuery] Left room: ${roomName} for channel: ${channel}`);
                    } catch (error) {
                        console.error(`[SmartQuery] Failed to leave room ${roomName}:`, error);
                    }
                }
            })["useSmartQuery.useEffect"];
        }
    }["useSmartQuery.useEffect"], [
        enableWebSocket,
        isWebSocketConnected,
        channel,
        webSocketManager
    ]);
    // Subscribe to WebSocket events for real-time updates
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useSmartQuery.useEffect": ()=>{
            if (!enableWebSocket || !isWebSocketConnected || events.length === 0) {
                return;
            }
            const unsubscribers = [];
            // Subscribe to each event
            for (const event of events){
                const unsubscribe = webSocketManager.subscribe(channel, event, {
                    "useSmartQuery.useEffect.unsubscribe": (data)=>{
                        console.log(`[SmartQuery] WebSocket event received: ${channel}:${event}`, data);
                        // Invalidate the specific query to trigger refetch
                        queryClient.invalidateQueries({
                            queryKey
                        });
                    }
                }["useSmartQuery.useEffect.unsubscribe"]);
                unsubscribers.push(unsubscribe);
            }
            return ({
                "useSmartQuery.useEffect": ()=>{
                    for (const unsubscribe of unsubscribers)unsubscribe();
                }
            })["useSmartQuery.useEffect"];
        }
    }["useSmartQuery.useEffect"], [
        enableWebSocket,
        isWebSocketConnected,
        events,
        channel,
        webSocketManager,
        queryClient,
        queryKey
    ]);
    return {
        ...queryResult,
        isUsingFallback,
        isWebSocketConnected
    };
}
_s3(useSmartQuery, "i/s8kx27S4VOid4YpXdq/PU9ml0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
function useSystemQuery(queryKey, queryFn, options) {
    _s4();
    return useSmartQuery(queryKey, queryFn, {
        channel: 'system',
        events: [
            'system-update',
            'config-changed'
        ],
        fallbackInterval: 120_000
    }, options);
}
_s4(useSystemQuery, "3cVlcIAwCvBXE2sGv8BwBFtyOC4=", false, function() {
    return [
        useSmartQuery
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/transformers/delegationEnrichment.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Delegation enrichment transformer following established patterns
 * @description Handles the enrichment of delegation data with employee and vehicle details
 * @module transformers/delegationEnrichment
 */ __turbopack_context__.s({
    "DelegationEnrichmentTransformer": (()=>DelegationEnrichmentTransformer),
    "enrichDelegation": (()=>enrichDelegation)
});
class DelegationEnrichmentTransformer {
    /**
   * Main enrichment method that combines delegation data with employee and vehicle details
   * @param delegation - Base delegation data
   * @param employees - Array of employees
   * @param vehicles - Array of vehicles
   * @returns Fully enriched delegation
   */ static enrich(delegation, employees, vehicles) {
        const { employeeMap, vehicleMap } = this.createLookupMaps(employees, vehicles);
        return {
            ...delegation,
            drivers: this.enrichDrivers(delegation.drivers, employeeMap) ?? [],
            escorts: this.enrichEscorts(delegation.escorts, employeeMap) ?? [],
            vehicles: this.enrichVehicles(delegation.vehicles, vehicleMap) ?? []
        };
    }
    /**
   * Creates optimized lookup maps for O(1) performance
   * @param employees - Array of employees
   * @param vehicles - Array of vehicles
   * @returns Object containing employee and vehicle maps
   */ static createLookupMaps(employees, vehicles) {
        return {
            employeeMap: new Map(employees.map((emp)=>[
                    emp.id,
                    emp
                ])),
            vehicleMap: new Map(vehicles.map((veh)=>[
                    veh.id,
                    veh
                ]))
        };
    }
    /**
   * Enriches driver assignments with employee details
   * @param drivers - Array of driver assignments
   * @param employeeMap - Map of employees for O(1) lookup
   * @returns Enriched driver assignments
   */ static enrichDrivers(drivers, employeeMap) {
        return drivers?.map((driver)=>{
            const employee = driver.employee || employeeMap.get(Number(driver.employeeId));
            return {
                ...driver,
                ...employee && {
                    employee
                }
            };
        });
    }
    /**
   * Enriches escort assignments with employee details
   * @param escorts - Array of escort assignments
   * @param employeeMap - Map of employees for O(1) lookup
   * @returns Enriched escort assignments
   */ static enrichEscorts(escorts, employeeMap) {
        return escorts?.map((escort)=>{
            const employee = escort.employee || employeeMap.get(Number(escort.employeeId));
            return {
                ...escort,
                ...employee && {
                    employee
                }
            };
        });
    }
    /**
   * Enriches vehicle assignments with vehicle details
   * @param vehicles - Array of vehicle assignments
   * @param vehicleMap - Map of vehicles for O(1) lookup
   * @returns Enriched vehicle assignments
   */ static enrichVehicles(vehicles, vehicleMap) {
        return vehicles?.map((vehicleAssignment)=>{
            const vehicle = vehicleAssignment.vehicle || vehicleMap.get(vehicleAssignment.vehicleId);
            return {
                ...vehicleAssignment,
                ...vehicle && {
                    vehicle
                }
            };
        });
    }
}
const enrichDelegation = (delegation, employees, vehicles)=>{
    return DelegationEnrichmentTransformer.enrich(delegation, employees, vehicles);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/stores/queries/delegationQueries.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Delegation query configurations following Single Responsibility Principle
 * @description Centralized query configurations for delegation-related data fetching
 */ __turbopack_context__.s({
    "createDelegationQuery": (()=>createDelegationQuery),
    "createDelegationWithAssignmentsQueries": (()=>createDelegationWithAssignmentsQueries),
    "createEmployeesQuery": (()=>createEmployeesQuery),
    "createVehiclesQuery": (()=>createVehiclesQuery),
    "delegationQueryKeys": (()=>delegationQueryKeys),
    "delegationQueryOptions": (()=>delegationQueryOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <module evaluation>"); // Use centralized services
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-client] (ecmascript)");
;
const delegationQueryKeys = {
    all: [
        'delegations'
    ],
    detail: (id)=>[
            'delegations',
            id
        ],
    withAssignments: (id)=>[
            'delegations',
            id,
            'with-assignments'
        ]
};
const createDelegationQuery = (id)=>({
        enabled: !!id,
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationApiService"].getById(id),
        queryKey: delegationQueryKeys.detail(id),
        staleTime: 5 * 60 * 1000
    });
const createEmployeesQuery = ()=>({
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeApiService"].getAll(),
        queryKey: [
            'employees'
        ],
        staleTime: 10 * 60 * 1000
    });
const createVehiclesQuery = ()=>({
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["vehicleApiService"].getAll(),
        queryKey: [
            'vehicles'
        ],
        staleTime: 10 * 60 * 1000
    });
const createDelegationWithAssignmentsQueries = (id)=>[
        createDelegationQuery(id),
        createEmployeesQuery(),
        createVehiclesQuery()
    ];
const delegationQueryOptions = {
    gcTime: 10 * 60 * 1000,
    retry: 3,
    retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30_000),
    staleTime: 5 * 60 * 1000
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/stores/queries/useDelegations.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file TanStack Query hooks for Delegation-related data.
 * These hooks manage fetching, caching, and mutating delegation data,
 * integrating with the DelegationApiService and DelegationTransformer.
 * @module stores/queries/useDelegations
 */ __turbopack_context__.s({
    "useCreateDelegation": (()=>useCreateDelegation),
    "useDelegation": (()=>useDelegation),
    "useDelegationEnriched": (()=>useDelegationEnriched),
    "useDelegationWithAssignments": (()=>useDelegationWithAssignments),
    "useDelegations": (()=>useDelegations),
    "useDeleteDelegation": (()=>useDeleteDelegation),
    "useManageDelegationFlightDetails": (()=>useManageDelegationFlightDetails),
    "useUpdateDelegation": (()=>useUpdateDelegation),
    "useUpdateDelegationStatus": (()=>useUpdateDelegationStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQueries.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/typeHelpers.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-client] (ecmascript)"); // Adjusted import path
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <module evaluation>"); // Use centralized service
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationEnrichment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/delegationEnrichment.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/delegationTransformer.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/delegationQueries.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature(), _s7 = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
const useDelegations = (options)=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
    ], {
        "useDelegations.useCrudQuery": async ()=>{
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationApiService"].getAll();
            return result.data;
        }
    }["useDelegations.useCrudQuery"], 'delegation', {
        staleTime: 0,
        ...options
    });
};
_s(useDelegations, "Ztp436ph0p7uLj6V/dsGnQuV8ZM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"]
    ];
});
const useDelegation = (id)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)
    ], {
        "useDelegation.useCrudQuery": async ()=>{
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationApiService"].getById(id);
        }
    }["useDelegation.useCrudQuery"], 'delegation', {
        enabled: !!id,
        staleTime: 5 * 60 * 1000
    });
};
_s1(useDelegation, "Ztp436ph0p7uLj6V/dsGnQuV8ZM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"]
    ];
});
const useDelegationWithAssignments = (id)=>{
    _s2();
    // Execute all queries in parallel using useQueries for maximum performance
    const results = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueries"])({
        queries: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createDelegationWithAssignmentsQueries"])(id)
    });
    const [delegationQuery, employeesQuery, vehiclesQuery] = results;
    // Compute enriched delegation when all data is available
    const enrichedDelegation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useDelegationWithAssignments.useMemo[enrichedDelegation]": ()=>{
            if (!delegationQuery?.data || !employeesQuery?.data || !vehiclesQuery?.data) {
                return;
            }
            try {
                // ✅ PRODUCTION FIX: delegationQuery.data is already transformed by the service layer
                // No need to apply DelegationTransformer.fromApi() again
                const delegation = delegationQuery.data;
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationEnrichment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["enrichDelegation"])(delegation, employeesQuery.data, vehiclesQuery.data);
            } catch (error) {
                console.error('Error enriching delegation data:', error);
                throw error;
            }
        }
    }["useDelegationWithAssignments.useMemo[enrichedDelegation]"], [
        delegationQuery?.data,
        employeesQuery?.data,
        vehiclesQuery?.data
    ]);
    // ✅ PRODUCTION FIX: Memoize refetch function to prevent infinite re-renders
    const refetch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useDelegationWithAssignments.useCallback[refetch]": ()=>{
            delegationQuery?.refetch();
            employeesQuery?.refetch();
            vehiclesQuery?.refetch();
        }
    }["useDelegationWithAssignments.useCallback[refetch]"], [
        delegationQuery?.refetch,
        employeesQuery?.refetch,
        vehiclesQuery?.refetch
    ]);
    // Return combined state with optimized loading states
    return {
        data: enrichedDelegation,
        error: delegationQuery?.error || employeesQuery?.error || vehiclesQuery?.error,
        isError: delegationQuery?.isError || employeesQuery?.isError || vehiclesQuery?.isError,
        isLoading: delegationQuery?.isLoading || employeesQuery?.isLoading || vehiclesQuery?.isLoading,
        isPending: delegationQuery?.isPending || employeesQuery?.isPending || vehiclesQuery?.isPending,
        refetch
    };
};
_s2(useDelegationWithAssignments, "Q9hNFpY9Bj8mGY1c8JQ0oGtUDAQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueries"]
    ];
});
const useDelegationEnriched = useDelegationWithAssignments;
const useCreateDelegation = ()=>{
    _s3();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCreateDelegation.useMutation": async (delegationData)=>{
                const apiPayload = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DelegationTransformer"].toCreateRequest(delegationData);
                // ✅ PRODUCTION FIX: delegationApiService.create() already applies transformation
                return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationApiService"].create(apiPayload);
            }
        }["useCreateDelegation.useMutation"],
        onError: {
            "useCreateDelegation.useMutation": (err, _delegationData, context)=>{
                if (context?.previousDelegations) {
                    queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all, context.previousDelegations);
                }
                console.error('Failed to create delegation:', err);
                // Invalidate to refetch correct data on error
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
                });
            }
        }["useCreateDelegation.useMutation"],
        onMutate: {
            "useCreateDelegation.useMutation": async (delegationData)=>{
                await queryClient.cancelQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
                });
                const previousDelegations = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all);
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all, {
                    "useCreateDelegation.useMutation": (old = [])=>{
                        const tempId = `optimistic-${Date.now()}`;
                        const now = new Date().toISOString();
                        const optimisticArrivalFlight = delegationData.flightArrivalDetails ? {
                            id: `optimistic-flight-arr-${Date.now()}`,
                            ...delegationData.flightArrivalDetails
                        } : null;
                        const optimisticDepartureFlight = delegationData.flightDepartureDetails ? {
                            id: `optimistic-flight-dep-${Date.now() + 1}`,
                            ...delegationData.flightDepartureDetails
                        } : null;
                        const optimisticDelegates = delegationData.delegates?.map({
                            "useCreateDelegation.useMutation": (d, index)=>({
                                    id: `optimistic-delegate-${tempId}-${index}`,
                                    name: d.name,
                                    notes: d.notes ?? null,
                                    title: d.title
                                })
                        }["useCreateDelegation.useMutation"]) || [];
                        const optimisticDelegation = {
                            arrivalFlight: optimisticArrivalFlight ?? null,
                            createdAt: now,
                            delegates: optimisticDelegates,
                            departureFlight: optimisticDepartureFlight ?? null,
                            drivers: delegationData.drivers?.map({
                                "useCreateDelegation.useMutation": (d)=>({
                                        createdAt: now,
                                        createdBy: null,
                                        delegationId: tempId,
                                        employeeId: d.employeeId,
                                        id: `optimistic-driver-${tempId}-${d.employeeId}`,
                                        notes: d.notes ?? null,
                                        updatedAt: now
                                    })
                            }["useCreateDelegation.useMutation"]) || [],
                            durationFrom: delegationData.durationFrom,
                            durationTo: delegationData.durationTo,
                            escorts: delegationData.escorts?.map({
                                "useCreateDelegation.useMutation": (e)=>({
                                        createdAt: now,
                                        createdBy: null,
                                        delegationId: tempId,
                                        employeeId: e.employeeId,
                                        id: `optimistic-escort-${tempId}-${e.employeeId}`,
                                        notes: e.notes ?? null,
                                        updatedAt: now
                                    })
                            }["useCreateDelegation.useMutation"]) || [],
                            eventName: delegationData.eventName,
                            id: tempId,
                            imageUrl: delegationData.imageUrl ?? null,
                            invitationFrom: delegationData.invitationFrom ?? null,
                            invitationTo: delegationData.invitationTo ?? null,
                            location: delegationData.location,
                            notes: delegationData.notes ?? null,
                            status: delegationData.status || 'Planned',
                            statusHistory: [],
                            updatedAt: now,
                            vehicles: delegationData.vehicles?.map({
                                "useCreateDelegation.useMutation": (v)=>({
                                        assignedDate: v.assignedDate,
                                        createdAt: now,
                                        createdBy: null,
                                        delegationId: tempId,
                                        id: `optimistic-vehicle-${tempId}-${v.vehicleId}`,
                                        notes: v.notes ?? null,
                                        returnDate: v.returnDate ?? null,
                                        updatedAt: now,
                                        vehicleId: v.vehicleId
                                    })
                            }["useCreateDelegation.useMutation"]) || []
                        };
                        return [
                            ...old,
                            optimisticDelegation
                        ];
                    }
                }["useCreateDelegation.useMutation"]);
                return {
                    previousDelegations
                };
            }
        }["useCreateDelegation.useMutation"],
        onSettled: {
            "useCreateDelegation.useMutation": ()=>{
                // Invalidate to ensure consistency after success or failure
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
                });
            }
        }["useCreateDelegation.useMutation"]
    });
};
_s3(useCreateDelegation, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useUpdateDelegation = ()=>{
    _s4();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateDelegation.useMutation": async ({ data, id })=>{
                // ✅ PRODUCTION FIX: delegationApiService.update() already applies transformation
                return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationApiService"].update(id, data);
            }
        }["useUpdateDelegation.useMutation"],
        onError: {
            "useUpdateDelegation.useMutation": (err, variables, context)=>{
                if (context?.previousDelegation) {
                    queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id), context.previousDelegation);
                }
                if (context?.previousDelegationsList) {
                    queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all, context.previousDelegationsList);
                }
                console.error('Failed to update delegation:', err);
                // Invalidate to refetch correct data on error
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id)
                });
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
                });
            }
        }["useUpdateDelegation.useMutation"],
        onMutate: {
            "useUpdateDelegation.useMutation": async ({ data, id })=>{
                // data is UpdateDelegationRequest
                await queryClient.cancelQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
                });
                await queryClient.cancelQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)
                });
                const previousDelegation = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id));
                const previousDelegationsList = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all);
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id), {
                    "useUpdateDelegation.useMutation": (old)=>{
                        if (!old) return;
                        const now = new Date().toISOString();
                        // ✅ PRODUCTION FIX: Use correct field mappings for UpdateDelegationRequest
                        const updatedOptimistic = {
                            ...old,
                            // Handle flight details updates
                            arrivalFlight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.flightArrivalDetails === null ? null // Explicitly set to null if requested
                             : data.flightArrivalDetails === undefined ? old.arrivalFlight : {
                                airport: data.flightArrivalDetails.airport || old.arrivalFlight?.airport || '',
                                dateTime: data.flightArrivalDetails.dateTime || old.arrivalFlight?.dateTime || '',
                                flightNumber: data.flightArrivalDetails.flightNumber || old.arrivalFlight?.flightNumber || '',
                                id: old.arrivalFlight?.id || `optimistic-arr-${Date.now()}`,
                                notes: data.flightArrivalDetails.notes ?? old.arrivalFlight?.notes ?? null,
                                terminal: data.flightArrivalDetails.terminal ?? old.arrivalFlight?.terminal ?? null
                            } // Keep old value if not in request
                            ),
                            departureFlight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.flightDepartureDetails === null ? null // Explicitly set to null if requested
                             : data.flightDepartureDetails === undefined ? old.departureFlight : {
                                airport: data.flightDepartureDetails.airport || old.departureFlight?.airport || '',
                                dateTime: data.flightDepartureDetails.dateTime || old.departureFlight?.dateTime || '',
                                flightNumber: data.flightDepartureDetails.flightNumber || old.departureFlight?.flightNumber || '',
                                id: old.departureFlight?.id || `optimistic-dep-${Date.now()}`,
                                notes: data.flightDepartureDetails.notes ?? old.departureFlight?.notes ?? null,
                                terminal: data.flightDepartureDetails.terminal ?? old.departureFlight?.terminal ?? null
                            } // Keep old value if not in request
                            ),
                            durationFrom: data.durationFrom ?? old.durationFrom,
                            durationTo: data.durationTo ?? old.durationTo,
                            // Direct field mappings (no transformation needed)
                            eventName: data.eventName ?? old.eventName,
                            imageUrl: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.imageUrl ?? old.imageUrl),
                            invitationFrom: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.invitationFrom ?? old.invitationFrom),
                            invitationTo: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.invitationTo ?? old.invitationTo),
                            location: data.location ?? old.location,
                            notes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.notes ?? old.notes),
                            status: data.status ?? old.status,
                            updatedAt: now
                        };
                        return updatedOptimistic;
                    }
                }["useUpdateDelegation.useMutation"]);
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all, {
                    "useUpdateDelegation.useMutation": (oldList = [])=>oldList.map({
                            "useUpdateDelegation.useMutation": (delegation)=>delegation.id === id ? queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)) || delegation : delegation
                        }["useUpdateDelegation.useMutation"])
                }["useUpdateDelegation.useMutation"]);
                return {
                    previousDelegation,
                    previousDelegationsList
                };
            }
        }["useUpdateDelegation.useMutation"],
        onSettled: {
            "useUpdateDelegation.useMutation": (_data, _error, variables)=>{
                // Always refetch after error or success
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id)
                });
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
                });
            }
        }["useUpdateDelegation.useMutation"]
    });
};
_s4(useUpdateDelegation, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useUpdateDelegationStatus = ()=>{
    _s5();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateDelegationStatus.useMutation": async ({ id, status, statusChangeReason })=>{
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationApiService"].updateStatus(id, status, statusChangeReason);
                return response;
            }
        }["useUpdateDelegationStatus.useMutation"],
        onError: {
            "useUpdateDelegationStatus.useMutation": (err, variables, context)=>{
                if (context?.previousDelegation) {
                    queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id), context.previousDelegation);
                }
                console.error('Failed to update delegation status:', err);
            }
        }["useUpdateDelegationStatus.useMutation"],
        onMutate: {
            "useUpdateDelegationStatus.useMutation": async ({ id, status })=>{
                await queryClient.cancelQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)
                });
                const previousDelegation = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id));
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id), {
                    "useUpdateDelegationStatus.useMutation": (old)=>old ? {
                            ...old,
                            status: status
                        } : undefined
                }["useUpdateDelegationStatus.useMutation"]);
                return {
                    previousDelegation
                };
            }
        }["useUpdateDelegationStatus.useMutation"],
        onSettled: {
            "useUpdateDelegationStatus.useMutation": (_data, _error, variables)=>{
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id)
                });
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
                });
            }
        }["useUpdateDelegationStatus.useMutation"]
    });
};
_s5(useUpdateDelegationStatus, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useManageDelegationFlightDetails = ()=>{
    _s6();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useManageDelegationFlightDetails.useMutation": async ({ flightDetails, id })=>{
                // This service method might need adjustment if flightDetails from form is Omit<FlightDetails, 'id'>
                // For now, assuming it expects full FlightDetails (including ID for existing, or will generate for new)
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationApiService"].manageFlightDetails(id, flightDetails);
                return response;
            }
        }["useManageDelegationFlightDetails.useMutation"],
        onError: {
            "useManageDelegationFlightDetails.useMutation": (err, variables, context)=>{
                if (context?.previousDelegation) {
                    queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id), context.previousDelegation);
                }
                console.error('Failed to manage delegation flight details:', err);
            }
        }["useManageDelegationFlightDetails.useMutation"],
        onMutate: {
            "useManageDelegationFlightDetails.useMutation": async ({ flightDetails, id })=>{
                await queryClient.cancelQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)
                });
                const previousDelegation = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id));
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id), {
                    "useManageDelegationFlightDetails.useMutation": (old)=>{
                        if (!old) return;
                        // This optimistic update assumes flightDetails is for arrival.
                        // A more robust solution would need to know if it's arrival or departure.
                        return {
                            ...old,
                            arrivalFlight: flightDetails
                        };
                    }
                }["useManageDelegationFlightDetails.useMutation"]);
                return {
                    previousDelegation
                };
            }
        }["useManageDelegationFlightDetails.useMutation"],
        onSettled: {
            "useManageDelegationFlightDetails.useMutation": (_data, _error, variables)=>{
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id)
                });
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
                });
            }
        }["useManageDelegationFlightDetails.useMutation"]
    });
};
_s6(useManageDelegationFlightDetails, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useDeleteDelegation = ()=>{
    _s7();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useDeleteDelegation.useMutation": async (id)=>{
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationApiService"].delete(id);
                return id;
            }
        }["useDeleteDelegation.useMutation"],
        onError: {
            "useDeleteDelegation.useMutation": (err, _id, context)=>{
                if (context?.previousDelegationsList) {
                    queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all, context.previousDelegationsList);
                }
                console.error('Failed to delete delegation:', err);
            }
        }["useDeleteDelegation.useMutation"],
        onMutate: {
            "useDeleteDelegation.useMutation": async (id)=>{
                await queryClient.cancelQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
                });
                await queryClient.cancelQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)
                });
                const previousDelegationsList = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all);
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all, {
                    "useDeleteDelegation.useMutation": (old = [])=>old.filter({
                            "useDeleteDelegation.useMutation": (delegation)=>delegation.id !== id
                        }["useDeleteDelegation.useMutation"])
                }["useDeleteDelegation.useMutation"]);
                queryClient.removeQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)
                });
                return {
                    previousDelegationsList
                };
            }
        }["useDeleteDelegation.useMutation"],
        onSettled: {
            "useDeleteDelegation.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
                });
            }
        }["useDeleteDelegation.useMutation"]
    });
};
_s7(useDeleteDelegation, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/ui/useNotifications.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Custom hook for notification management using Zustand AppStore
 * @module hooks/useNotifications
 */ __turbopack_context__.s({
    "useNotifications": (()=>useNotifications),
    "useWorkHubNotifications": (()=>useWorkHubNotifications)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/appStore.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
;
;
const useNotifications = ()=>{
    _s();
    const addNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotifications.useAppStore[addNotification]": (state)=>state.addNotification
    }["useNotifications.useAppStore[addNotification]"]);
    const removeNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotifications.useAppStore[removeNotification]": (state)=>state.removeNotification
    }["useNotifications.useAppStore[removeNotification]"]);
    const clearAllNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotifications.useAppStore[clearAllNotifications]": (state)=>state.clearAllNotifications
    }["useNotifications.useAppStore[clearAllNotifications]"]);
    const unreadCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"])({
        "useNotifications.useAppStore[unreadCount]": (state)=>state.unreadNotificationCount
    }["useNotifications.useAppStore[unreadCount]"]);
    /**
   * Show a success notification
   */ const showSuccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[showSuccess]": (message)=>{
            addNotification({
                message,
                type: 'success'
            });
        }
    }["useNotifications.useCallback[showSuccess]"], [
        addNotification
    ]);
    /**
   * Show an error notification
   */ const showError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[showError]": (message)=>{
            addNotification({
                message,
                type: 'error'
            });
        }
    }["useNotifications.useCallback[showError]"], [
        addNotification
    ]);
    /**
   * Show a warning notification
   */ const showWarning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[showWarning]": (message)=>{
            addNotification({
                message,
                type: 'warning'
            });
        }
    }["useNotifications.useCallback[showWarning]"], [
        addNotification
    ]);
    /**
   * Show an info notification
   */ const showInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[showInfo]": (message)=>{
            addNotification({
                message,
                type: 'info'
            });
        }
    }["useNotifications.useCallback[showInfo]"], [
        addNotification
    ]);
    /**
   * Show a notification for API operation results
   */ const showApiResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[showApiResult]": (success, successMessage, errorMessage)=>{
            if (success) {
                showSuccess(successMessage);
            } else {
                showError(errorMessage);
            }
        }
    }["useNotifications.useCallback[showApiResult]"], [
        showSuccess,
        showError
    ]);
    /**
   * Show a notification with auto-dismiss after specified time
   */ const showTemporary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[showTemporary]": (type, message, dismissAfter = 5000)=>{
            addNotification({
                message,
                type
            });
            // Auto-dismiss after specified time
            setTimeout({
                "useNotifications.useCallback[showTemporary]": ()=>{
                    // Note: This is a simplified approach. In a real implementation,
                    // you might want to store the notification ID and remove specifically that one
                    const notifications = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().notifications;
                    const latestNotification = notifications.at(-1);
                    if (latestNotification && latestNotification.message === message) {
                        removeNotification(latestNotification.id);
                    }
                }
            }["useNotifications.useCallback[showTemporary]"], dismissAfter);
        }
    }["useNotifications.useCallback[showTemporary]"], [
        addNotification,
        removeNotification
    ]);
    /**
   * Show a loading notification that can be updated
   */ const showLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[showLoading]": (message = 'Loading...')=>{
            addNotification({
                message,
                type: 'info'
            });
            // Return the notification ID for potential updates
            const notifications = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().notifications;
            return notifications.at(-1)?.id;
        }
    }["useNotifications.useCallback[showLoading]"], [
        addNotification
    ]);
    /**
   * Update a loading notification to success or error
   */ const updateLoadingNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useNotifications.useCallback[updateLoadingNotification]": (notificationId, success, message)=>{
            removeNotification(notificationId);
            if (success) {
                showSuccess(message);
            } else {
                showError(message);
            }
        }
    }["useNotifications.useCallback[updateLoadingNotification]"], [
        removeNotification,
        showSuccess,
        showError
    ]);
    return {
        clearAllNotifications,
        // Store methods
        removeNotification,
        // Advanced methods
        showApiResult,
        showError,
        showInfo,
        showLoading,
        // Basic notification methods
        showSuccess,
        showTemporary,
        showWarning,
        unreadCount,
        updateLoadingNotification
    };
};
_s(useNotifications, "/BYtFwygHLYNELsENOMZKcd3h6k=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"]
    ];
});
const useWorkHubNotifications = ()=>{
    _s1();
    const { clearAllNotifications, removeNotification, showError, showInfo, showSuccess, showWarning, unreadCount } = useNotifications();
    /**
   * Show delegation-related notifications
   */ const showDelegationUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useWorkHubNotifications.useCallback[showDelegationUpdate]": (message, actionUrl)=>{
            const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
            addNotification({
                ...actionUrl && {
                    actionUrl
                },
                category: 'delegation',
                message,
                type: 'delegation-update'
            });
        }
    }["useWorkHubNotifications.useCallback[showDelegationUpdate]"], []);
    /**
   * Show vehicle maintenance notifications
   */ const showVehicleMaintenance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useWorkHubNotifications.useCallback[showVehicleMaintenance]": (message, actionUrl)=>{
            const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
            addNotification({
                ...actionUrl && {
                    actionUrl
                },
                category: 'vehicle',
                message,
                type: 'vehicle-maintenance'
            });
        }
    }["useWorkHubNotifications.useCallback[showVehicleMaintenance]"], []);
    /**
   * Show task assignment notifications
   */ const showTaskAssigned = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useWorkHubNotifications.useCallback[showTaskAssigned]": (message, actionUrl)=>{
            const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
            addNotification({
                ...actionUrl && {
                    actionUrl
                },
                category: 'task',
                message,
                type: 'task-assigned'
            });
        }
    }["useWorkHubNotifications.useCallback[showTaskAssigned]"], []);
    /**
   * Show employee update notifications
   */ const showEmployeeUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useWorkHubNotifications.useCallback[showEmployeeUpdate]": (message, actionUrl)=>{
            const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
            addNotification({
                ...actionUrl && {
                    actionUrl
                },
                category: 'employee',
                message,
                type: 'employee-update'
            });
        }
    }["useWorkHubNotifications.useCallback[showEmployeeUpdate]"], []);
    return {
        clearAllNotifications,
        // Management
        removeNotification,
        // WorkHub-specific notifications
        showDelegationUpdate,
        showEmployeeUpdate,
        showError,
        showInfo,
        // Basic notifications
        showSuccess,
        showTaskAssigned,
        showVehicleMaintenance,
        showWarning,
        unreadCount
    };
};
_s1(useWorkHubNotifications, "KoT5E64RRpgu31OumjjOEKa2MtE=", false, function() {
    return [
        useNotifications
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/stores/queries/useEmployees.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file TanStack Query hooks for Employee-related data.
 * These hooks manage fetching, caching, and mutating employee data,
 * integrating with the EmployeeApiService and EmployeeTransformer.
 * @module stores/queries/useEmployees
 */ __turbopack_context__.s({
    "employeeQueryKeys": (()=>employeeQueryKeys),
    "useCreateEmployee": (()=>useCreateEmployee),
    "useDeleteEmployee": (()=>useDeleteEmployee),
    "useEmployee": (()=>useEmployee),
    "useEmployees": (()=>useEmployees),
    "useEmployeesByRole": (()=>useEmployeesByRole),
    "useUpdateEmployee": (()=>useUpdateEmployee),
    "useUpdateEmployeeAvailabilityStatus": (()=>useUpdateEmployeeAvailabilityStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-client] (ecmascript)"); // Adjusted import path
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useNotifications.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/employeeTransformer.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/typeHelpers.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <module evaluation>"); // Use centralized service
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature();
;
;
;
;
;
;
const employeeQueryKeys = {
    all: [
        'employees'
    ],
    detail: (id)=>[
            'employees',
            id
        ]
};
const useEmployees = (options)=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...employeeQueryKeys.all
    ], {
        "useEmployees.useCrudQuery": async ()=>{
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeApiService"].getAll();
            return response.data;
        }
    }["useEmployees.useCrudQuery"], 'employee', {
        staleTime: 5 * 60 * 1000,
        ...options
    });
};
_s(useEmployees, "Ztp436ph0p7uLj6V/dsGnQuV8ZM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"]
    ];
});
const useEmployeesByRole = (role, options)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"])(role ? [
        'employees',
        'role',
        role
    ] : [
        ...employeeQueryKeys.all
    ], {
        "useEmployeesByRole.useCrudQuery": async ()=>{
            if (role) {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeApiService"].getByRole(role);
                return response;
            } else {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeApiService"].getAll();
                return response.data;
            }
        }
    }["useEmployeesByRole.useCrudQuery"], 'employee', {
        staleTime: 5 * 60 * 1000,
        ...options
    });
};
_s1(useEmployeesByRole, "Ztp436ph0p7uLj6V/dsGnQuV8ZM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"]
    ];
});
const useEmployee = (id)=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...employeeQueryKeys.detail(id)
    ], {
        "useEmployee.useCrudQuery": async ()=>{
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeApiService"].getById(id);
        }
    }["useEmployee.useCrudQuery"], 'employee', {
        enabled: !!id,
        staleTime: 5 * 60 * 1000
    });
};
_s2(useEmployee, "Ztp436ph0p7uLj6V/dsGnQuV8ZM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"]
    ];
});
const useCreateEmployee = ()=>{
    _s3();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { showError, showSuccess } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCreateEmployee.useMutation": async (employeeData)=>{
                const request = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EmployeeTransformer"].toCreateRequest(employeeData);
                return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeApiService"].create(request); // Removed redundant EmployeeTransformer.fromApi
            }
        }["useCreateEmployee.useMutation"],
        onError: {
            "useCreateEmployee.useMutation": (err, _newEmployeeData, context)=>{
                if (context?.previousEmployees) {
                    queryClient.setQueryData(employeeQueryKeys.all, context.previousEmployees);
                }
                showError(`Failed to create employee: ${err.message || 'Unknown error occurred'}`);
            }
        }["useCreateEmployee.useMutation"],
        onMutate: {
            "useCreateEmployee.useMutation": async (newEmployeeData)=>{
                await queryClient.cancelQueries({
                    queryKey: employeeQueryKeys.all
                });
                const previousEmployees = queryClient.getQueryData(employeeQueryKeys.all);
                queryClient.setQueryData(employeeQueryKeys.all, {
                    "useCreateEmployee.useMutation": (old = [])=>{
                        const tempId = 'optimistic-' + Date.now().toString();
                        const now = new Date().toISOString();
                        const optimisticEmployee = {
                            availability: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.availability),
                            contactEmail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.contactEmail),
                            contactInfo: newEmployeeData.contactInfo,
                            contactMobile: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.contactMobile),
                            contactPhone: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.contactPhone),
                            createdAt: now,
                            currentLocation: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.currentLocation),
                            department: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.department),
                            employeeId: newEmployeeData.employeeId,
                            fullName: newEmployeeData.fullName || newEmployeeData.name,
                            generalAssignments: newEmployeeData.generalAssignments || [],
                            hireDate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.hireDate),
                            id: Number(tempId.replace('optimistic-', '')),
                            name: newEmployeeData.name,
                            notes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.notes),
                            position: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.position),
                            profileImageUrl: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.profileImageUrl),
                            role: newEmployeeData.role,
                            shiftSchedule: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.shiftSchedule),
                            skills: newEmployeeData.skills || [],
                            status: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.status),
                            updatedAt: now,
                            ...newEmployeeData.workingHours !== undefined && {
                                workingHours: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.workingHours)
                            }
                        };
                        return [
                            ...old,
                            optimisticEmployee
                        ];
                    }
                }["useCreateEmployee.useMutation"]);
                return {
                    previousEmployees
                };
            }
        }["useCreateEmployee.useMutation"],
        onSettled: {
            "useCreateEmployee.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: employeeQueryKeys.all
                });
            }
        }["useCreateEmployee.useMutation"],
        onSuccess: {
            "useCreateEmployee.useMutation": (data)=>{
                showSuccess(`Employee "${data.name}" has been created successfully!`);
            }
        }["useCreateEmployee.useMutation"]
    });
};
_s3(useCreateEmployee, "Hg8lK9aCa8gQZM3QZV4WiaMNbs4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useUpdateEmployee = ()=>{
    _s4();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateEmployee.useMutation": async ({ data, id })=>{
                const request = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EmployeeTransformer"].toUpdateRequest(data); // Removed cast
                return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeApiService"].update(id, request); // Removed redundant EmployeeTransformer.fromApi
            }
        }["useUpdateEmployee.useMutation"],
        onError: {
            "useUpdateEmployee.useMutation": (err, variables, context)=>{
                if (context?.previousEmployee) {
                    queryClient.setQueryData(employeeQueryKeys.detail(variables.id), context.previousEmployee);
                }
                if (context?.previousEmployeesList) {
                    queryClient.setQueryData(employeeQueryKeys.all, context.previousEmployeesList);
                }
                console.error('Failed to update employee:', err);
            }
        }["useUpdateEmployee.useMutation"],
        onMutate: {
            "useUpdateEmployee.useMutation": async ({ data, id })=>{
                await queryClient.cancelQueries({
                    queryKey: employeeQueryKeys.all
                });
                await queryClient.cancelQueries({
                    queryKey: employeeQueryKeys.detail(id)
                });
                const previousEmployee = queryClient.getQueryData(employeeQueryKeys.detail(id));
                const previousEmployeesList = queryClient.getQueryData(employeeQueryKeys.all);
                queryClient.setQueryData(employeeQueryKeys.detail(id), {
                    "useUpdateEmployee.useMutation": (old)=>{
                        if (!old) return old;
                        const now = new Date().toISOString();
                        // Explicitly map updated fields to avoid issues with spread operator on different types
                        const updatedOptimistic = {
                            ...old,
                            availability: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.availability === undefined ? old.availability : data.availability),
                            contactEmail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.contactEmail === undefined ? old.contactEmail : data.contactEmail),
                            contactInfo: data.contactInfo ?? old.contactInfo,
                            contactMobile: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.contactMobile === undefined ? old.contactMobile : data.contactMobile),
                            contactPhone: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.contactPhone === undefined ? old.contactPhone : data.contactPhone),
                            currentLocation: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.currentLocation === undefined ? old.currentLocation : data.currentLocation),
                            department: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.department ?? old.department),
                            employeeId: data.employeeId ?? old.employeeId,
                            fullName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.fullName ?? old.fullName),
                            generalAssignments: data.generalAssignments ?? old.generalAssignments,
                            hireDate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.hireDate ?? old.hireDate),
                            name: data.name ?? old.name,
                            notes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.notes === undefined ? old.notes : data.notes),
                            position: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.position ?? old.position),
                            profileImageUrl: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.profileImageUrl === undefined ? old.profileImageUrl : data.profileImageUrl),
                            role: data.role ?? old.role,
                            shiftSchedule: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.shiftSchedule === undefined ? old.shiftSchedule : data.shiftSchedule),
                            skills: data.skills ?? old.skills,
                            status: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.status ?? old.status),
                            updatedAt: now,
                            ...data.workingHours !== undefined && {
                                workingHours: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.workingHours)
                            }
                        };
                        return updatedOptimistic;
                    }
                }["useUpdateEmployee.useMutation"]);
                queryClient.setQueryData(employeeQueryKeys.all, {
                    "useUpdateEmployee.useMutation": (old = [])=>{
                        return old.map({
                            "useUpdateEmployee.useMutation": (employee)=>{
                                if (employee.id === Number(id)) {
                                    const now = new Date().toISOString();
                                    return {
                                        ...employee,
                                        availability: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.availability ?? employee.availability),
                                        contactEmail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.contactEmail ?? employee.contactEmail),
                                        contactInfo: data.contactInfo ?? employee.contactInfo,
                                        contactMobile: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.contactMobile ?? employee.contactMobile),
                                        contactPhone: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.contactPhone ?? employee.contactPhone),
                                        currentLocation: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.currentLocation ?? employee.currentLocation),
                                        department: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.department ?? employee.department),
                                        employeeId: data.employeeId ?? employee.employeeId,
                                        fullName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.fullName ?? employee.fullName),
                                        generalAssignments: data.generalAssignments ?? employee.generalAssignments,
                                        hireDate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.hireDate ?? employee.hireDate),
                                        name: data.name ?? employee.name,
                                        notes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.notes ?? employee.notes),
                                        position: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.position ?? employee.position),
                                        profileImageUrl: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.profileImageUrl ?? employee.profileImageUrl),
                                        role: data.role ?? employee.role,
                                        shiftSchedule: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.shiftSchedule ?? employee.shiftSchedule),
                                        skills: data.skills ?? employee.skills,
                                        status: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.status ?? employee.status),
                                        updatedAt: now,
                                        ...data.workingHours !== undefined && {
                                            workingHours: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.workingHours)
                                        }
                                    };
                                }
                                return employee;
                            }
                        }["useUpdateEmployee.useMutation"]);
                    }
                }["useUpdateEmployee.useMutation"]);
                return {
                    previousEmployee,
                    previousEmployeesList
                };
            }
        }["useUpdateEmployee.useMutation"],
        onSettled: {
            "useUpdateEmployee.useMutation": (_data, _error, variables)=>{
                queryClient.invalidateQueries({
                    queryKey: employeeQueryKeys.detail(variables.id)
                });
                queryClient.invalidateQueries({
                    queryKey: employeeQueryKeys.all
                });
            }
        }["useUpdateEmployee.useMutation"]
    });
};
_s4(useUpdateEmployee, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useDeleteEmployee = ()=>{
    _s5();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useDeleteEmployee.useMutation": async (id)=>{
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeApiService"].delete(id);
                return id;
            }
        }["useDeleteEmployee.useMutation"],
        onError: {
            "useDeleteEmployee.useMutation": (err, _id, context)=>{
                if (context?.previousEmployeesList) {
                    queryClient.setQueryData(employeeQueryKeys.all, context.previousEmployeesList);
                }
                console.error('Failed to delete employee:', err);
            }
        }["useDeleteEmployee.useMutation"],
        onMutate: {
            "useDeleteEmployee.useMutation": async (id)=>{
                await queryClient.cancelQueries({
                    queryKey: employeeQueryKeys.all
                });
                await queryClient.cancelQueries({
                    queryKey: employeeQueryKeys.detail(id)
                });
                const previousEmployeesList = queryClient.getQueryData(employeeQueryKeys.all);
                queryClient.setQueryData(employeeQueryKeys.all, {
                    "useDeleteEmployee.useMutation": (old = [])=>old.filter({
                            "useDeleteEmployee.useMutation": (employee)=>employee.id !== Number(id)
                        }["useDeleteEmployee.useMutation"]) // Compare number ID
                }["useDeleteEmployee.useMutation"]);
                queryClient.removeQueries({
                    queryKey: employeeQueryKeys.detail(id)
                });
                return {
                    previousEmployeesList
                };
            }
        }["useDeleteEmployee.useMutation"],
        onSettled: {
            "useDeleteEmployee.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: employeeQueryKeys.all
                });
            }
        }["useDeleteEmployee.useMutation"]
    });
};
_s5(useDeleteEmployee, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useUpdateEmployeeAvailabilityStatus = ()=>{
    _s6();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    // The input 'status' should align with DriverAvailabilityPrisma
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateEmployeeAvailabilityStatus.useMutation": async ({ employeeId, status })=>{
                // employeeApiService.updateAvailabilityStatus now expects DriverAvailabilityPrisma directly
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeApiService"].updateAvailabilityStatus(employeeId, status);
                return response; // Removed redundant EmployeeTransformer.fromApi
            }
        }["useUpdateEmployeeAvailabilityStatus.useMutation"],
        onError: {
            "useUpdateEmployeeAvailabilityStatus.useMutation": (err, variables, context)=>{
                if (context?.previousEmployee) {
                    queryClient.setQueryData(employeeQueryKeys.detail(variables.employeeId), context.previousEmployee);
                }
                console.error('Failed to update employee availability status:', err);
            }
        }["useUpdateEmployeeAvailabilityStatus.useMutation"],
        onMutate: {
            "useUpdateEmployeeAvailabilityStatus.useMutation": async ({ employeeId, status })=>{
                await queryClient.cancelQueries({
                    queryKey: employeeQueryKeys.detail(employeeId)
                });
                const previousEmployee = queryClient.getQueryData(employeeQueryKeys.detail(employeeId));
                queryClient.setQueryData(employeeQueryKeys.detail(employeeId), {
                    "useUpdateEmployeeAvailabilityStatus.useMutation": (old)=>{
                        // Update the 'availability' field in the domain model
                        return old ? {
                            ...old,
                            availability: status
                        } : old;
                    }
                }["useUpdateEmployeeAvailabilityStatus.useMutation"]);
                return {
                    previousEmployee
                };
            }
        }["useUpdateEmployeeAvailabilityStatus.useMutation"],
        onSettled: {
            "useUpdateEmployeeAvailabilityStatus.useMutation": (_data, _error, variables)=>{
                queryClient.invalidateQueries({
                    queryKey: employeeQueryKeys.detail(variables.employeeId)
                });
                queryClient.invalidateQueries({
                    queryKey: employeeQueryKeys.all
                });
            }
        }["useUpdateEmployeeAvailabilityStatus.useMutation"]
    });
};
_s6(useUpdateEmployeeAvailabilityStatus, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/stores/queries/useServiceRecords.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SERVICE_RECORD_QUERY_KEY": (()=>SERVICE_RECORD_QUERY_KEY),
    "useCreateServiceRecord": (()=>useCreateServiceRecord),
    "useDeleteServiceRecord": (()=>useDeleteServiceRecord),
    "useEnrichedServiceRecords": (()=>useEnrichedServiceRecords),
    "useServiceRecord": (()=>useServiceRecord),
    "useUpdateServiceRecord": (()=>useUpdateServiceRecord),
    "useVehicleServiceRecords": (()=>useVehicleServiceRecords)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <locals>");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature();
;
;
;
const ServiceRecordTransformer = {
    fromApi (apiData) {
        // Handle both regular service records and enriched service records
        const baseRecord = {
            cost: apiData.cost,
            createdAt: apiData.createdAt,
            date: apiData.date,
            employeeId: apiData.employeeId,
            id: apiData.id,
            notes: apiData.notes,
            odometer: apiData.odometer,
            servicePerformed: Array.isArray(apiData.servicePerformed) ? apiData.servicePerformed : [],
            updatedAt: apiData.updatedAt,
            vehicleId: apiData.vehicleId
        };
        // Add enriched fields if available (for EnrichedServiceRecord)
        if (apiData.vehicleMake || apiData.vehicleModel || apiData.vehicleYear) {
            return {
                ...baseRecord,
                vehicleMake: apiData.vehicleMake || 'Unknown',
                vehicleModel: apiData.vehicleModel || 'Unknown',
                vehicleYear: apiData.vehicleYear || new Date().getFullYear(),
                licensePlate: apiData.licensePlate || null,
                employeeName: apiData.employeeName || null
            }; // Cast to handle both ServiceRecord and EnrichedServiceRecord
        }
        return baseRecord;
    },
    toApi: (data)=>data
};
class ServiceRecordApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/servicerecords';
    transformer = ServiceRecordTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 5 * 60 * 1000,
            retryAttempts: 3,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            ...config
        });
    }
    async getById(id) {
        return this.executeWithInfrastructure(`${this.endpoint}:${id}`, async ()=>{
            const response = await this.apiClient.get(`${this.endpoint}/${id}`);
            // Apply transformer if available
            const transformedResponse = this.transformer.fromApi ? this.transformer.fromApi(response) : response;
            return transformedResponse;
        });
    }
    // Custom update method for service records, handling vehicleId in path
    async updateRecord(id, vehicleId, data) {
        return this.executeWithInfrastructure(null, async ()=>{
            // Remove vehicleId from data if present to avoid conflicts, use the parameter instead
            const { vehicleId: _, ...updateData } = data;
            const response = await this.apiClient.put(`/vehicles/${vehicleId}/servicerecords/${id}`, updateData // Send only the actual update data
            );
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`)); // Invalidate all service records
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:${id}`)); // Invalidate specific record
            this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`)); // Invalidate vehicle-specific caches
            return this.transformer.fromApi ? this.transformer.fromApi(response) : response;
        });
    }
    // Custom delete method for service records, handling vehicleId in path
    async deleteRecord(id, vehicleId) {
        return this.executeWithInfrastructure(null, async ()=>{
            await this.apiClient.delete(`/vehicles/${vehicleId}/servicerecords/${id}`);
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`)); // Invalidate all service records
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:${id}`)); // Invalidate specific record
            this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`)); // Invalidate vehicle-specific caches
        });
    }
    async getVehicleServiceRecords(vehicleId) {
        return this.executeWithInfrastructure(`vehicles:${vehicleId}:servicerecords`, async ()=>{
            const response = await this.apiClient.get(`/vehicles/${vehicleId}/servicerecords`);
            return response;
        });
    }
    async createVehicleServiceRecord(vehicleId, data) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post(`/vehicles/${vehicleId}/servicerecords`, data);
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`));
            return this.transformer.fromApi ? this.transformer.fromApi(response) : response;
        });
    }
    // Method to fetch enriched service records
    async getAllEnriched() {
        return this.executeWithInfrastructure(`${this.endpoint}:enriched`, async ()=>{
            const response = await this.apiClient.get(`${this.endpoint}/enriched`);
            // Apply transformer to each record
            return response.map((record)=>{
                const transformed = this.transformer.fromApi ? this.transformer.fromApi(record) : record;
                return transformed;
            });
        });
    }
}
;
const serviceRecordApiService = new ServiceRecordApiService(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"]);
const SERVICE_RECORD_QUERY_KEY = 'serviceRecords';
const useServiceRecord = (id, options)=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        SERVICE_RECORD_QUERY_KEY,
        id
    ], {
        "useServiceRecord.useCrudQuery": ()=>serviceRecordApiService.getById(id)
    }["useServiceRecord.useCrudQuery"], 'serviceRecord', {
        enabled: options?.enabled ?? !!id,
        staleTime: 1000 * 60 * 5
    });
};
_s(useServiceRecord, "Ztp436ph0p7uLj6V/dsGnQuV8ZM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"]
    ];
});
const useEnrichedServiceRecords = (options)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        SERVICE_RECORD_QUERY_KEY,
        'allEnriched'
    ], {
        "useEnrichedServiceRecords.useCrudQuery": ()=>serviceRecordApiService.getAllEnriched()
    }["useEnrichedServiceRecords.useCrudQuery"], 'serviceRecord', {
        enabled: options?.enabled ?? true,
        staleTime: 1000 * 60 * 5
    });
};
_s1(useEnrichedServiceRecords, "Ztp436ph0p7uLj6V/dsGnQuV8ZM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"]
    ];
});
const useVehicleServiceRecords = (vehicleId, options)=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        SERVICE_RECORD_QUERY_KEY,
        'forVehicle',
        vehicleId
    ], {
        "useVehicleServiceRecords.useCrudQuery": ()=>serviceRecordApiService.getVehicleServiceRecords(vehicleId)
    }["useVehicleServiceRecords.useCrudQuery"], 'serviceRecord', {
        enabled: options?.enabled ?? true,
        staleTime: 1000 * 60 * 5
    });
};
_s2(useVehicleServiceRecords, "Ztp436ph0p7uLj6V/dsGnQuV8ZM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"]
    ];
});
const useCreateServiceRecord = ()=>{
    _s3();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCreateServiceRecord.useMutation": async (newRecordData)=>{
                const { vehicleId } = newRecordData;
                return serviceRecordApiService.createVehicleServiceRecord(vehicleId, newRecordData);
            }
        }["useCreateServiceRecord.useMutation"],
        onSuccess: {
            "useCreateServiceRecord.useMutation": (data, variables)=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        SERVICE_RECORD_QUERY_KEY,
                        'allEnriched'
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        SERVICE_RECORD_QUERY_KEY,
                        'forVehicle',
                        variables.vehicleId
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        'vehicle',
                        variables.vehicleId
                    ]
                });
            }
        }["useCreateServiceRecord.useMutation"]
    });
};
_s3(useCreateServiceRecord, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useUpdateServiceRecord = ()=>{
    _s4();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateServiceRecord.useMutation": async ({ id, vehicleId, data })=>{
                // Call the custom updateRecord method
                return serviceRecordApiService.updateRecord(id, vehicleId, data);
            }
        }["useUpdateServiceRecord.useMutation"],
        onSuccess: {
            "useUpdateServiceRecord.useMutation": (_, variables)=>{
                queryClient.invalidateQueries({
                    queryKey: [
                        SERVICE_RECORD_QUERY_KEY,
                        'allEnriched'
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        SERVICE_RECORD_QUERY_KEY,
                        variables.id
                    ]
                }); // Invalidate specific record
                // Use variables.vehicleId directly as it's now part of the mutation variables
                queryClient.invalidateQueries({
                    queryKey: [
                        SERVICE_RECORD_QUERY_KEY,
                        'forVehicle',
                        variables.vehicleId
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        'vehicle',
                        variables.vehicleId
                    ]
                });
            }
        }["useUpdateServiceRecord.useMutation"]
    });
};
_s4(useUpdateServiceRecord, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useDeleteServiceRecord = ()=>{
    _s5();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useDeleteServiceRecord.useMutation": async ({ id, vehicleId })=>{
                // Call the custom deleteRecord method
                return serviceRecordApiService.deleteRecord(id, vehicleId);
            }
        }["useDeleteServiceRecord.useMutation"],
        onSuccess: {
            "useDeleteServiceRecord.useMutation": (_, variables)=>{
                // Use variables instead of id for consistency
                queryClient.invalidateQueries({
                    queryKey: [
                        SERVICE_RECORD_QUERY_KEY,
                        'allEnriched'
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        SERVICE_RECORD_QUERY_KEY,
                        variables.id
                    ]
                });
                // Invalidate all vehicle-specific caches since we don't know which vehicle this record belonged to
                queryClient.invalidateQueries({
                    queryKey: [
                        SERVICE_RECORD_QUERY_KEY,
                        'forVehicle'
                    ]
                });
                queryClient.invalidateQueries({
                    queryKey: [
                        'vehicle'
                    ]
                });
            }
        }["useDeleteServiceRecord.useMutation"]
    });
};
_s5(useDeleteServiceRecord, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/transformers/taskEnrichment.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Task enrichment transformer following established patterns
 * @description Handles the enrichment of task data with employee and vehicle details
 * @module transformers/taskEnrichment
 */ __turbopack_context__.s({
    "TaskEnrichmentTransformer": (()=>TaskEnrichmentTransformer),
    "enrichTask": (()=>enrichTask)
});
class TaskEnrichmentTransformer {
    /**
   * Main enrichment method that combines task data with employee and vehicle details
   * @param task - Base task data
   * @param employees - Array of employees
   * @param vehicles - Array of vehicles
   * @returns Fully enriched task
   */ static enrich(task, employees, vehicles) {
        const { employeeMap, vehicleMap } = this.createLookupMaps(employees, vehicles);
        // Apply all enrichments sequentially
        let enrichedTask = this.enrichStaffEmployee(task, employeeMap);
        enrichedTask = this.enrichDriverEmployee(enrichedTask, employeeMap);
        enrichedTask = this.enrichVehicle(enrichedTask, vehicleMap);
        return enrichedTask;
    }
    /**
   * Creates optimized lookup maps for O(1) performance
   * @param employees - Array of employees
   * @param vehicles - Array of vehicles
   * @returns Object containing employee and vehicle maps
   */ static createLookupMaps(employees, vehicles) {
        // Defensive programming: Ensure inputs are arrays
        const safeEmployees = Array.isArray(employees) ? employees : [];
        const safeVehicles = Array.isArray(vehicles) ? vehicles : [];
        return {
            employeeMap: new Map(safeEmployees.map((emp)=>[
                    emp.id,
                    emp
                ])),
            vehicleMap: new Map(safeVehicles.map((veh)=>[
                    veh.id,
                    veh
                ]))
        };
    }
    /**
   * Enriches driver employee assignment with employee details
   * @param task - Base task data
   * @param employeeMap - Map of employees for O(1) lookup
   * @returns Task with enriched driver employee data
   */ static enrichDriverEmployee(task, employeeMap) {
        if (!task.driverEmployeeId) {
            return task;
        }
        const driverEmployee = task.driverEmployee ?? employeeMap.get(task.driverEmployeeId) ?? null;
        return {
            ...task,
            driverEmployee
        };
    }
    /**
   * Enriches staff employee assignment with employee details
   * @param task - Base task data
   * @param employeeMap - Map of employees for O(1) lookup
   * @returns Task with enriched staff employee data
   */ static enrichStaffEmployee(task, employeeMap) {
        if (!task.staffEmployeeId) {
            return task;
        }
        const staffEmployee = task.staffEmployee ?? employeeMap.get(task.staffEmployeeId) ?? null;
        return {
            ...task,
            staffEmployee
        };
    }
    /**
   * Enriches vehicle assignment with vehicle details
   * @param task - Base task data
   * @param vehicleMap - Map of vehicles for O(1) lookup
   * @returns Task with enriched vehicle data
   */ static enrichVehicle(task, vehicleMap) {
        if (!task.vehicleId) {
            return task;
        }
        const vehicle = task.vehicle ?? vehicleMap.get(task.vehicleId) ?? null;
        return {
            ...task,
            vehicle
        };
    }
}
const enrichTask = (task, employees, vehicles)=>{
    return TaskEnrichmentTransformer.enrich(task, employees, vehicles);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/stores/queries/taskQueries.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Task query configurations following Single Responsibility Principle
 * @description Centralized query configurations for task-related data fetching
 */ __turbopack_context__.s({
    "createEmployeesQuery": (()=>createEmployeesQuery),
    "createTaskQuery": (()=>createTaskQuery),
    "createTaskWithAssignmentsQueries": (()=>createTaskWithAssignmentsQueries),
    "createVehiclesQuery": (()=>createVehiclesQuery),
    "taskQueryKeys": (()=>taskQueryKeys),
    "taskQueryOptions": (()=>taskQueryOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-client] (ecmascript)");
;
const taskQueryKeys = {
    all: [
        'tasks'
    ],
    detail: (id)=>[
            'tasks',
            id
        ],
    withAssignments: (id)=>[
            'tasks',
            id,
            'with-assignments'
        ]
};
const createTaskQuery = (id)=>({
        enabled: !!id,
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskApiService"].getById(id),
        queryKey: taskQueryKeys.detail(id),
        staleTime: 5 * 60 * 1000
    });
const createEmployeesQuery = ()=>({
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["employeeApiService"].getAll(),
        queryKey: [
            'employees'
        ],
        staleTime: 10 * 60 * 1000
    });
const createVehiclesQuery = ()=>({
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["vehicleApiService"].getAll(),
        queryKey: [
            'vehicles'
        ],
        staleTime: 10 * 60 * 1000
    });
const createTaskWithAssignmentsQueries = (id)=>[
        createTaskQuery(id),
        createEmployeesQuery(),
        createVehiclesQuery()
    ];
const taskQueryOptions = {
    gcTime: 10 * 60 * 1000,
    retry: 3,
    retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30_000),
    staleTime: 5 * 60 * 1000
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/stores/queries/useTasks.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file TanStack Query hooks for Task-related data.
 * These hooks manage fetching, caching, and mutating task data,
 * integrating with the TaskApiService and TaskTransformer.
 * @module stores/queries/useTasks
 */ __turbopack_context__.s({
    "useCreateTask": (()=>useCreateTask),
    "useDeleteTask": (()=>useDeleteTask),
    "useTask": (()=>useTask),
    "useTaskEnriched": (()=>useTaskEnriched),
    "useTaskWithAssignments": (()=>useTaskWithAssignments),
    "useTasks": (()=>useTasks),
    "useUpdateTask": (()=>useUpdateTask)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQueries.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-client] (ecmascript)"); // Adjusted import path
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <module evaluation>"); // Use centralized service
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskEnrichment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/taskEnrichment.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/taskTransformer.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/taskQueries.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/typeHelpers.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
const useTasks = (options)=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
    ], {
        "useTasks.useCrudQuery": async ()=>{
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskApiService"].getAll();
            return response.data;
        }
    }["useTasks.useCrudQuery"], 'task', {
        staleTime: 0,
        ...options
    });
};
_s(useTasks, "Ztp436ph0p7uLj6V/dsGnQuV8ZM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"]
    ];
});
const useTask = (id)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(id)
    ], {
        "useTask.useCrudQuery": async ()=>{
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskApiService"].getById(id);
        }
    }["useTask.useCrudQuery"], 'task', {
        enabled: !!id,
        staleTime: 5 * 60 * 1000
    });
};
_s1(useTask, "Ztp436ph0p7uLj6V/dsGnQuV8ZM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"]
    ];
});
const useTaskWithAssignments = (id)=>{
    _s2();
    // Execute all queries in parallel using useQueries for maximum performance
    const results = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueries"])({
        queries: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createTaskWithAssignmentsQueries"])(id)
    });
    const [taskQuery, employeesQuery, vehiclesQuery] = results;
    // Compute enriched task when all data is available
    const enrichedTask = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useTaskWithAssignments.useMemo[enrichedTask]": ()=>{
            if (!taskQuery?.data || !employeesQuery?.data || !vehiclesQuery?.data) {
                return;
            }
            try {
                const task = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskTransformer"].fromApi(taskQuery.data);
                // Defensive programming: Ensure employees and vehicles are arrays
                const employees = Array.isArray(employeesQuery.data) ? employeesQuery.data : [];
                const vehicles = Array.isArray(vehiclesQuery.data) ? vehiclesQuery.data : [];
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskEnrichment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["enrichTask"])(task, employees, vehicles);
            } catch (error) {
                console.error('Error enriching task data:', error);
                throw error;
            }
        }
    }["useTaskWithAssignments.useMemo[enrichedTask]"], [
        taskQuery?.data,
        employeesQuery?.data,
        vehiclesQuery?.data
    ]);
    // ✅ PRODUCTION FIX: Memoize refetch function to prevent infinite re-renders
    const refetch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTaskWithAssignments.useCallback[refetch]": ()=>{
            taskQuery?.refetch();
            employeesQuery?.refetch();
            vehiclesQuery?.refetch();
        }
    }["useTaskWithAssignments.useCallback[refetch]"], [
        taskQuery?.refetch,
        employeesQuery?.refetch,
        vehiclesQuery?.refetch
    ]);
    // Return combined state with optimized loading states
    return {
        data: enrichedTask,
        error: taskQuery?.error || employeesQuery?.error || vehiclesQuery?.error,
        isError: taskQuery?.isError || employeesQuery?.isError || vehiclesQuery?.isError,
        isLoading: taskQuery?.isLoading || employeesQuery?.isLoading || vehiclesQuery?.isLoading,
        isPending: taskQuery?.isPending || employeesQuery?.isPending || vehiclesQuery?.isPending,
        refetch
    };
};
_s2(useTaskWithAssignments, "fIluMZDGHi+Q36Zshz/CeK2PjGU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueries"]
    ];
});
const useTaskEnriched = useTaskWithAssignments;
const useCreateTask = ()=>{
    _s3();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCreateTask.useMutation": async (taskData)=>{
                const request = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskTransformer"].toCreateRequest(taskData);
                return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskApiService"].create(request); // Removed redundant TaskTransformer.fromApi
            }
        }["useCreateTask.useMutation"],
        onError: {
            "useCreateTask.useMutation": (err, newTaskData, context)=>{
                if (context?.previousTasks) {
                    queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all, context.previousTasks);
                }
                console.error('Failed to create task:', err);
            }
        }["useCreateTask.useMutation"],
        onMutate: {
            "useCreateTask.useMutation": async (newTaskData)=>{
                await queryClient.cancelQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
                });
                const previousTasks = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all);
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all, {
                    "useCreateTask.useMutation": (old = [])=>{
                        const tempId = 'optimistic-' + Date.now().toString();
                        const now = new Date().toISOString();
                        const optimisticTask = {
                            createdAt: now,
                            dateTime: newTaskData.dateTime ?? null,
                            deadline: newTaskData.deadline ?? null,
                            description: newTaskData.description,
                            driverEmployee: null,
                            driverEmployeeId: newTaskData.driverEmployeeId ?? null,
                            estimatedDuration: newTaskData.estimatedDuration ?? null,
                            id: tempId,
                            location: newTaskData.location ?? null,
                            notes: newTaskData.notes ?? null,
                            priority: newTaskData.priority,
                            requiredSkills: newTaskData.requiredSkills ?? null,
                            staffEmployee: null,
                            staffEmployeeId: newTaskData.staffEmployeeId ?? null,
                            status: newTaskData.status || 'Pending',
                            subtasks: newTaskData.subtasks?.map({
                                "useCreateTask.useMutation": (s)=>({
                                        completed: s.completed || false,
                                        id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,
                                        taskId: tempId,
                                        title: s.title
                                    })
                            }["useCreateTask.useMutation"]) || [],
                            updatedAt: now,
                            vehicle: null,
                            vehicleId: newTaskData.vehicleId ?? null
                        };
                        return [
                            ...old,
                            optimisticTask
                        ];
                    }
                }["useCreateTask.useMutation"]);
                return {
                    previousTasks
                };
            }
        }["useCreateTask.useMutation"],
        onSettled: {
            "useCreateTask.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
                });
            }
        }["useCreateTask.useMutation"]
    });
};
_s3(useCreateTask, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useUpdateTask = ()=>{
    _s4();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateTask.useMutation": async ({ data, id })=>{
                const request = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TaskTransformer"].toUpdateRequest(data); // Removed cast
                return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskApiService"].update(id, request); // Removed redundant TaskTransformer.fromApi
            }
        }["useUpdateTask.useMutation"],
        onError: {
            "useUpdateTask.useMutation": (err, variables, context)=>{
                if (context?.previousTask) {
                    queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(variables.id), context.previousTask);
                }
                if (context?.previousTasksList) {
                    queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all, context.previousTasksList);
                }
                console.error('Failed to update task:', err);
            }
        }["useUpdateTask.useMutation"],
        onMutate: {
            "useUpdateTask.useMutation": async ({ data, id })=>{
                await queryClient.cancelQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
                });
                await queryClient.cancelQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(id)
                });
                const previousTask = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(id));
                const previousTasksList = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all);
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(id), {
                    "useUpdateTask.useMutation": (old)=>{
                        if (!old) return old;
                        const now = new Date().toISOString();
                        // Explicitly map updated fields to avoid issues with spread operator on different types
                        const updatedOptimistic = {
                            ...old,
                            dateTime: data.dateTime !== undefined ? data.dateTime : old.dateTime,
                            deadline: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.deadline !== undefined ? data.deadline : old.deadline),
                            description: data.description ?? old.description,
                            driverEmployeeId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.driverEmployeeId !== undefined ? data.driverEmployeeId : old.driverEmployeeId),
                            estimatedDuration: data.estimatedDuration !== undefined ? data.estimatedDuration : old.estimatedDuration,
                            location: data.location !== undefined ? data.location : old.location,
                            notes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.notes !== undefined ? data.notes : old.notes),
                            priority: data.priority ?? old.priority,
                            requiredSkills: data.requiredSkills !== undefined ? data.requiredSkills : old.requiredSkills,
                            staffEmployeeId: data.staffEmployeeId !== undefined ? data.staffEmployeeId : old.staffEmployeeId,
                            status: data.status ?? old.status,
                            subtasks: data.subtasks?.map({
                                "useUpdateTask.useMutation": (s)=>({
                                        completed: s.completed ?? false,
                                        id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,
                                        taskId: id,
                                        title: s.title
                                    })
                            }["useUpdateTask.useMutation"]) || old.subtasks || [],
                            updatedAt: now,
                            vehicleId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.vehicleId !== undefined ? data.vehicleId : old.vehicleId)
                        };
                        return updatedOptimistic;
                    }
                }["useUpdateTask.useMutation"]);
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all, {
                    "useUpdateTask.useMutation": (old = [])=>{
                        return old.map({
                            "useUpdateTask.useMutation": (task)=>{
                                if (task.id === id) {
                                    const now = new Date().toISOString();
                                    const optimisticSubtasks = data.subtasks?.map({
                                        "useUpdateTask.useMutation": (s)=>({
                                                completed: s.completed ?? false,
                                                id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,
                                                taskId: id,
                                                title: s.title
                                            })
                                    }["useUpdateTask.useMutation"]) || task.subtasks || [];
                                    return {
                                        ...task,
                                        dateTime: data.dateTime !== undefined ? data.dateTime : task.dateTime,
                                        deadline: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.deadline !== undefined ? data.deadline : task.deadline),
                                        description: data.description ?? task.description,
                                        driverEmployeeId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.driverEmployeeId !== undefined ? data.driverEmployeeId : task.driverEmployeeId),
                                        estimatedDuration: data.estimatedDuration !== undefined ? data.estimatedDuration : task.estimatedDuration,
                                        location: data.location !== undefined ? data.location : task.location,
                                        notes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.notes !== undefined ? data.notes : task.notes),
                                        priority: data.priority ?? task.priority,
                                        requiredSkills: data.requiredSkills !== undefined ? data.requiredSkills : task.requiredSkills,
                                        staffEmployeeId: data.staffEmployeeId !== undefined ? data.staffEmployeeId : task.staffEmployeeId,
                                        status: data.status ?? task.status,
                                        subtasks: optimisticSubtasks,
                                        updatedAt: now,
                                        vehicleId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.vehicleId !== undefined ? data.vehicleId : task.vehicleId)
                                    };
                                }
                                return task;
                            }
                        }["useUpdateTask.useMutation"]);
                    }
                }["useUpdateTask.useMutation"]);
                return {
                    previousTask,
                    previousTasksList
                };
            }
        }["useUpdateTask.useMutation"],
        onSettled: {
            "useUpdateTask.useMutation": (data, error, variables)=>{
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(variables.id)
                });
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
                });
            }
        }["useUpdateTask.useMutation"]
    });
};
_s4(useUpdateTask, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useDeleteTask = ()=>{
    _s5();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useDeleteTask.useMutation": async (id)=>{
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskApiService"].delete(id);
                return id;
            }
        }["useDeleteTask.useMutation"],
        onError: {
            "useDeleteTask.useMutation": (err, id, context)=>{
                if (context?.previousTasksList) {
                    queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all, context.previousTasksList);
                }
                console.error('Failed to delete task:', err);
            }
        }["useDeleteTask.useMutation"],
        onMutate: {
            "useDeleteTask.useMutation": async (id)=>{
                await queryClient.cancelQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
                });
                await queryClient.cancelQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(id)
                });
                const previousTasksList = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all);
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all, {
                    "useDeleteTask.useMutation": (old = [])=>old.filter({
                            "useDeleteTask.useMutation": (task)=>task.id !== id
                        }["useDeleteTask.useMutation"])
                }["useDeleteTask.useMutation"]);
                queryClient.removeQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(id)
                });
                return {
                    previousTasksList
                };
            }
        }["useDeleteTask.useMutation"],
        onSettled: {
            "useDeleteTask.useMutation": ()=>{
                queryClient.invalidateQueries({
                    queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
                });
            }
        }["useDeleteTask.useMutation"]
    });
}; // Removed useAssignTask and useManageSubtasks hooks as their functionality is now
 // handled by the main create/update task mutations.
_s5(useDeleteTask, "YK0wzM21ECnncaq5SECwU+/SVdQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/stores/queries/useTasks.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskEnrichment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/taskEnrichment.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/taskTransformer.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/taskQueries.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/typeHelpers.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useTasks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useTasks.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/lib/stores/queries/useVehicles.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file TanStack Query hooks for Vehicle-related data.
 * @module stores/queries/useVehicles
 */ __turbopack_context__.s({
    "useCreateVehicle": (()=>useCreateVehicle),
    "useDeleteVehicle": (()=>useDeleteVehicle),
    "useUpdateVehicle": (()=>useUpdateVehicle),
    "useVehicle": (()=>useVehicle),
    "useVehicles": (()=>useVehicles),
    "vehicleQueryKeys": (()=>vehicleQueryKeys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useNotifications.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/vehicleTransformer.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <module evaluation>"); // Use centralized service from factory
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature();
;
;
;
;
;
const vehicleQueryKeys = {
    all: [
        'vehicles'
    ],
    detail: (id)=>[
            'vehicles',
            id
        ]
};
const useVehicles = (options)=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...vehicleQueryKeys.all
    ], {
        "useVehicles.useCrudQuery": async ()=>{
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["vehicleApiService"].getAll();
            return response.data;
        }
    }["useVehicles.useCrudQuery"], 'vehicle', {
        staleTime: 5 * 60 * 1000,
        ...options
    });
};
_s(useVehicles, "Ztp436ph0p7uLj6V/dsGnQuV8ZM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"]
    ];
});
const useVehicle = (id, options)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...vehicleQueryKeys.detail(id)
    ], {
        "useVehicle.useCrudQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["vehicleApiService"].getById(id)
    }["useVehicle.useCrudQuery"], 'vehicle', {
        enabled: !!id && (options?.enabled ?? true),
        staleTime: 5 * 60 * 1000,
        ...options
    });
};
_s1(useVehicle, "Ztp436ph0p7uLj6V/dsGnQuV8ZM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCrudQuery"]
    ];
});
const useCreateVehicle = ()=>{
    _s2();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { showError, showSuccess } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useCreateVehicle.useMutation": (newVehicleData)=>{
                const transformedData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VehicleTransformer"].toCreateRequest(newVehicleData);
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["vehicleApiService"].create(transformedData);
            }
        }["useCreateVehicle.useMutation"],
        onError: {
            "useCreateVehicle.useMutation": (error)=>{
                showError(`Failed to create vehicle: ${error.message || 'Unknown error occurred'}`);
            }
        }["useCreateVehicle.useMutation"],
        onSuccess: {
            "useCreateVehicle.useMutation": (data)=>{
                // Invalidate and refetch all vehicles query after a successful creation
                queryClient.invalidateQueries({
                    queryKey: vehicleQueryKeys.all
                });
                showSuccess(`Vehicle "${data.licensePlate}" has been created successfully!`);
            }
        }["useCreateVehicle.useMutation"]
    });
};
_s2(useCreateVehicle, "Hg8lK9aCa8gQZM3QZV4WiaMNbs4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useUpdateVehicle = ()=>{
    _s3();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { showError, showSuccess } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useUpdateVehicle.useMutation": ({ data, id })=>{
                const transformedData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VehicleTransformer"].toUpdateRequest(data);
                return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["vehicleApiService"].update(id, transformedData);
            }
        }["useUpdateVehicle.useMutation"],
        onError: {
            "useUpdateVehicle.useMutation": (error)=>{
                showError(`Failed to update vehicle: ${error.message || 'Unknown error occurred'}`);
            }
        }["useUpdateVehicle.useMutation"],
        onSuccess: {
            "useUpdateVehicle.useMutation": (updatedVehicle)=>{
                queryClient.invalidateQueries({
                    queryKey: vehicleQueryKeys.all
                });
                queryClient.invalidateQueries({
                    queryKey: vehicleQueryKeys.detail(updatedVehicle.id)
                });
                showSuccess(`Vehicle "${updatedVehicle.licensePlate}" has been updated successfully!`);
            }
        }["useUpdateVehicle.useMutation"]
    });
};
_s3(useUpdateVehicle, "Hg8lK9aCa8gQZM3QZV4WiaMNbs4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useDeleteVehicle = ()=>{
    _s4();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { showError, showSuccess } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useDeleteVehicle.useMutation": (id)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["vehicleApiService"].delete(id)
        }["useDeleteVehicle.useMutation"],
        onError: {
            "useDeleteVehicle.useMutation": (error)=>{
                showError(`Failed to delete vehicle: ${error.message || 'Unknown error occurred'}`);
            }
        }["useDeleteVehicle.useMutation"],
        onSuccess: {
            "useDeleteVehicle.useMutation": (_data, id)=>{
                queryClient.invalidateQueries({
                    queryKey: vehicleQueryKeys.all
                });
                queryClient.removeQueries({
                    queryKey: vehicleQueryKeys.detail(id)
                });
                showSuccess('Vehicle has been deleted successfully!');
            }
        }["useDeleteVehicle.useMutation"]
    });
};
_s4(useDeleteVehicle, "Hg8lK9aCa8gQZM3QZV4WiaMNbs4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useNotifications"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/dashboard/ModernDashboard.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Modern dashboard component with widgets and insights
 * @module components/dashboard/ModernDashboard
 */ __turbopack_context__.s({
    "ModernDashboard": (()=>ModernDashboard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/activity.js [app-client] (ecmascript) <export default as Activity>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-client] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-client] (ecmascript) <export default as BarChart3>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/car.js [app-client] (ecmascript) <export default as Car>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eye.js [app-client] (ecmascript) <export default as Eye>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-down.js [app-client] (ecmascript) <export default as TrendingDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-client] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wrench$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wrench$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wrench.js [app-client] (ecmascript) <export default as Wrench>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$progress$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/progress.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useDelegations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useDelegations.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useEmployees$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useEmployees.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useServiceRecords$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useServiceRecords.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useTasks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useTasks.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useTasks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useTasks.ts [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useVehicles$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useVehicles.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const StatCard = ({ title, value, description, icon: Icon, trend, href })=>{
    const CardWrapper = href ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] : 'div';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CardWrapper, {
        href: href || '#',
        className: href ? 'block' : '',
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('transition-all duration-200', href && 'hover:shadow-md cursor-pointer'),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "p-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm font-medium text-muted-foreground",
                                    children: title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 98,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-3xl font-bold",
                                    children: value
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 101,
                                    columnNumber: 15
                                }, this),
                                description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs text-muted-foreground mt-1",
                                    children: description
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 103,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 97,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col items-end gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "rounded-md bg-primary/10 p-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                        className: "size-4 text-primary"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                        lineNumber: 110,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 109,
                                    columnNumber: 15
                                }, this),
                                trend && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('flex items-center text-xs', trend.isPositive ? 'text-green-600' : 'text-red-600'),
                                    children: [
                                        trend.isPositive ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                            className: "size-3 mr-1"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 120,
                                            columnNumber: 21
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__["TrendingDown"], {
                                            className: "size-3 mr-1"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 122,
                                            columnNumber: 21
                                        }, this),
                                        Math.abs(trend.value),
                                        "%"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 113,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 108,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                    lineNumber: 96,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 95,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
            lineNumber: 89,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, this);
};
_c = StatCard;
const QuickAction = ({ title, description, icon: Icon, href, variant = 'outline' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        className: "hover:shadow-md transition-all duration-200",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
            className: "p-4",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-start gap-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "rounded-md bg-primary/10 p-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                            className: "size-4 text-primary"
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 147,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 146,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 min-w-0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "text-sm font-medium",
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 150,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-muted-foreground mt-1",
                                children: description
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 151,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                asChild: true,
                                variant: variant,
                                size: "sm",
                                className: "mt-3 w-full",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: href,
                                    children: "Get Started"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 153,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 152,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 149,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 145,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
            lineNumber: 144,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
        lineNumber: 143,
        columnNumber: 5
    }, this);
};
_c1 = QuickAction;
const ModernDashboard = ()=>{
    _s();
    const { user, isInitialized, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthContext"])();
    // Only make API calls when user is authenticated and auth system is ready
    // MEMOIZED to prevent infinite re-renders caused by user object reference changes
    const isAuthReady = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ModernDashboard.useMemo[isAuthReady]": ()=>{
            return isInitialized && !loading && !!user;
        }
    }["ModernDashboard.useMemo[isAuthReady]"], [
        isInitialized,
        loading,
        user?.id
    ]); // Use user.id instead of user object to prevent reference issues
    const { data: vehicles = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useVehicles$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useVehicles"])({
        enabled: isAuthReady
    });
    const { data: delegations = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useDelegations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDelegations"])({
        enabled: isAuthReady
    });
    const { data: tasks = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useTasks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTasks"])({
        enabled: isAuthReady
    });
    const { data: employees = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useEmployees$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEmployees"])({
        enabled: isAuthReady
    });
    const { data: serviceRecords = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useServiceRecords$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEnrichedServiceRecords"])({
        enabled: isAuthReady
    });
    // Show loading state while authentication is initializing
    if (!isInitialized || loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 184,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-4 text-sm text-muted-foreground",
                        children: "Initializing..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 185,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 183,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
            lineNumber: 182,
            columnNumber: 7
        }, this);
    }
    // Show login prompt if not authenticated
    if (!user) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                className: "w-full max-w-md",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                children: "Welcome to WorkHub"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 197,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardDescription"], {
                                children: "Please sign in to access your dashboard"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 198,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 196,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            asChild: true,
                            className: "w-full",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: "/login",
                                children: "Sign In"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 204,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 203,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 202,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 195,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
            lineNumber: 194,
            columnNumber: 7
        }, this);
    }
    // Calculate statistics
    const stats = {
        totalVehicles: vehicles.length,
        activeDelegations: delegations.filter((d)=>d.status === 'In_Progress').length,
        pendingTasks: tasks.filter((t)=>t.status === 'Assigned' || t.status === 'In_Progress').length,
        maintenancesDue: serviceRecords.filter((r)=>{
            // This is a simplified check - you might want more sophisticated logic
            // Simplified maintenance due logic
            return Math.random() > 0.8; // Mock 20% of records as needing maintenance
        }).length,
        teamMembers: employees.length
    };
    // Mock recent activity - you'd fetch this from your API
    const recentActivity = [
        {
            id: '1',
            title: 'Vehicle VIN-123 maintenance completed',
            description: 'Oil change and tire rotation finished',
            timestamp: '2 hours ago',
            type: 'maintenance'
        },
        {
            id: '2',
            title: 'New task assigned: Fleet inspection',
            description: 'Quarterly safety inspection due next week',
            timestamp: '4 hours ago',
            type: 'task',
            priority: 'high'
        },
        {
            id: '3',
            title: 'Project Alpha milestone completed',
            description: 'Phase 2 deliverables submitted',
            timestamp: '1 day ago',
            type: 'delegation'
        }
    ];
    const quickActions = [
        {
            title: 'Add New Vehicle',
            description: 'Register a new asset to your fleet',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"],
            href: '/add-vehicle',
            variant: 'default'
        },
        {
            title: 'Schedule Maintenance',
            description: 'Plan upcoming service appointments',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"],
            href: '/service-records'
        },
        {
            title: 'View Analytics',
            description: 'See detailed reports and insights',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"],
            href: '/reports'
        },
        {
            title: 'Assign Task',
            description: 'Create and delegate new tasks',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"],
            href: '/tasks'
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-8",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-3xl font-bold tracking-tight",
                        children: [
                            "Welcome back",
                            user?.user_metadata?.full_name ? `, ${user.user_metadata.full_name}` : '',
                            "!"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 286,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-muted-foreground",
                        children: "Here's what's happening with your operations today."
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 293,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 285,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid gap-4 md:grid-cols-2 lg:grid-cols-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StatCard, {
                        title: "Total Vehicles",
                        value: stats.totalVehicles,
                        description: "Active fleet assets",
                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__["Car"],
                        href: "/vehicles",
                        trend: {
                            value: 5.2,
                            isPositive: true
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 300,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StatCard, {
                        title: "Active Projects",
                        value: stats.activeDelegations,
                        description: "In progress delegations",
                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__["Activity"],
                        href: "/delegations"
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 308,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StatCard, {
                        title: "Pending Tasks",
                        value: stats.pendingTasks,
                        description: "Awaiting completion",
                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"],
                        href: "/tasks",
                        trend: {
                            value: 2.1,
                            isPositive: false
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 315,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(StatCard, {
                        title: "Maintenance Due",
                        value: stats.maintenancesDue,
                        description: "Requires attention",
                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"],
                        href: "/service-history"
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 323,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 299,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid gap-6 lg:grid-cols-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "lg:col-span-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                    className: "flex flex-row items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                                    children: "Recent Activity"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 338,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardDescription"], {
                                                    children: "Latest updates from your operations"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 339,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 337,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "outline",
                                            size: "sm",
                                            asChild: true,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/activity",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                                        className: "size-4 mr-2"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                        lineNumber: 345,
                                                        columnNumber: 19
                                                    }, this),
                                                    "View All"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                lineNumber: 344,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 343,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 336,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-4",
                                        children: recentActivity.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-start gap-3 p-3 rounded-lg bg-muted/30",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "rounded-full bg-primary/10 p-1",
                                                        children: [
                                                            item.type === 'maintenance' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wrench$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Wrench$3e$__["Wrench"], {
                                                                className: "size-3 text-primary"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 359,
                                                                columnNumber: 25
                                                            }, this),
                                                            item.type === 'task' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                                                className: "size-3 text-primary"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 362,
                                                                columnNumber: 25
                                                            }, this),
                                                            item.type === 'delegation' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                                                className: "size-3 text-primary"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 365,
                                                                columnNumber: 25
                                                            }, this),
                                                            item.type === 'vehicle' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__["Car"], {
                                                                className: "size-3 text-primary"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 368,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                        lineNumber: 357,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex-1 min-w-0",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center gap-2",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "text-sm font-medium",
                                                                        children: item.title
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                        lineNumber: 373,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    item.priority && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                                                        variant: item.priority === 'high' ? 'destructive' : 'secondary',
                                                                        className: "text-xs",
                                                                        children: item.priority
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                        lineNumber: 375,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 372,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-xs text-muted-foreground",
                                                                children: item.description
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 387,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-xs text-muted-foreground mt-1",
                                                                children: item.timestamp
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 390,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                        lineNumber: 371,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, item.id, true, {
                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                lineNumber: 353,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                        lineNumber: 351,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 350,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 335,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 334,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                            children: "Quick Actions"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 405,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardDescription"], {
                                            children: "Common tasks and shortcuts"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 406,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 404,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-3",
                                        children: quickActions.map((action, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(QuickAction, {
                                                ...action
                                            }, index, false, {
                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                lineNumber: 411,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                        lineNumber: 409,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 408,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 403,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 402,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 332,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                children: "Fleet Performance"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 422,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardDescription"], {
                                children: "Overview of fleet efficiency and maintenance status"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 423,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 421,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid gap-6 md:grid-cols-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between text-sm",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-muted-foreground",
                                                    children: "Fleet Utilization"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 431,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: "87%"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 432,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 430,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$progress$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Progress"], {
                                            value: 87,
                                            className: "h-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 434,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 429,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between text-sm",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-muted-foreground",
                                                    children: "Maintenance Up-to-date"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 438,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: "92%"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 441,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 437,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$progress$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Progress"], {
                                            value: 92,
                                            className: "h-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 443,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 436,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between text-sm",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-muted-foreground",
                                                    children: "Task Completion Rate"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 447,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: "78%"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 450,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 446,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$progress$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Progress"], {
                                            value: 78,
                                            className: "h-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 452,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 445,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 428,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 427,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 420,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
        lineNumber: 283,
        columnNumber: 5
    }, this);
};
_s(ModernDashboard, "vca45UTAnuJ7zkP4LRJgsT9d8Cs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthContext"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useVehicles$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useVehicles"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useDelegations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDelegations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useTasks$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTasks"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useEmployees$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEmployees"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useServiceRecords$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEnrichedServiceRecords"]
    ];
});
_c2 = ModernDashboard;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "StatCard");
__turbopack_context__.k.register(_c1, "QuickAction");
__turbopack_context__.k.register(_c2, "ModernDashboard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DashboardPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$ModernDashboard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/ModernDashboard.tsx [app-client] (ecmascript)");
'use client';
;
;
// Set page metadata
if (typeof document !== 'undefined') {
    document.title = 'Dashboard - WorkHub';
}
function DashboardPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$ModernDashboard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ModernDashboard"], {}, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 11,
        columnNumber: 10
    }, this);
}
_c = DashboardPage;
var _c;
__turbopack_context__.k.register(_c, "DashboardPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_0ef974f1._.js.map