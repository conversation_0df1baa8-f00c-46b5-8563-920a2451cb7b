module.exports = {

"[project]/src/hooks/api/useApiQuery.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Standardized API Query Hook
 * @description Provides consistent API query patterns with error handling, caching, and loading states
 */ __turbopack_context__.s({
    "useApiQuery": (()=>useApiQuery),
    "useDependentApiQuery": (()=>useDependentApiQuery),
    "usePaginatedApiQuery": (()=>usePaginatedApiQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/utils/use-toast.ts [app-ssr] (ecmascript)");
;
;
;
const useApiQuery = (queryKey, queryFn, options = {})=>{
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useToast"])();
    const { cacheDuration = 5 * 60 * 1000, enableRetry = true, errorMessage, retryAttempts = 3, showErrorToast = true, showSuccessToast = false, successMessage, ...queryOptions } = options;
    const queryResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        gcTime: cacheDuration * 2,
        queryFn,
        queryKey,
        retry: enableRetry ? retryAttempts : false,
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30_000),
        staleTime: cacheDuration,
        ...queryOptions
    });
    // FIXED: Handle success notifications in useEffect to prevent setState during render
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (showSuccessToast && queryResult.isSuccess && queryResult.data && successMessage) {
            toast({
                description: successMessage,
                title: 'Success'
            });
        }
    }, [
        showSuccessToast,
        queryResult.isSuccess,
        queryResult.data,
        successMessage,
        toast
    ]);
    // FIXED: Handle error notifications in useEffect to prevent setState during render
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (showErrorToast && queryResult.isError) {
            const message = errorMessage || (queryResult.error instanceof Error ? queryResult.error.message : 'An error occurred');
            toast({
                description: message,
                title: 'Error',
                variant: 'destructive'
            });
        }
    }, [
        showErrorToast,
        queryResult.isError,
        queryResult.error,
        errorMessage,
        toast
    ]);
    // Enhanced result with additional utilities
    const enhancedResult = {
        ...queryResult,
        forceRefresh: async ()=>await queryResult.refetch(),
        isStale: queryResult.isStale || false,
        lastUpdated: queryResult.dataUpdatedAt || null
    };
    return enhancedResult;
};
const useDependentApiQuery = (queryKey, queryFn, dependency, options = {})=>{
    return useApiQuery(queryKey, ()=>{
        if (!dependency) {
            throw new Error('Dependency not available');
        }
        return queryFn(dependency);
    }, {
        ...options,
        enabled: !!dependency && options.enabled !== false
    });
};
const usePaginatedApiQuery = (baseQueryKey, queryFn, options = {})=>{
    const { keepPreviousData = true, page = 1, pageSize = 10, ...apiOptions } = options;
    const queryKey = [
        ...baseQueryKey,
        'paginated',
        page,
        pageSize
    ];
    const queryResult = useApiQuery(queryKey, ()=>queryFn(page, pageSize), {
        ...apiOptions,
        ...keepPreviousData ? {
            placeholderData: (prev)=>prev
        } : {}
    });
    const pagination = queryResult.data?.pagination;
    const enhancedResult = {
        ...queryResult,
        currentPage: page,
        data: queryResult.data?.data ?? [],
        goToPage: (newPage)=>{
        // This would typically be handled by the parent component
        },
        hasNextPage: pagination ? pagination.hasNext : false,
        hasPrevPage: pagination ? pagination.hasPrevious : false,
        nextPage: ()=>{
            if (pagination && pagination.hasNext) {
            // This would typically be handled by the parent component
            // by updating the page state that's passed to this hook
            }
        },
        pagination: pagination ?? {
            hasNext: false,
            hasPrevious: false,
            limit: pageSize,
            page: 1,
            total: 0,
            totalPages: 1
        },
        prevPage: ()=>{
            if (pagination && pagination.hasPrevious) {
            // This would typically be handled by the parent component
            }
        },
        totalPages: pagination ? pagination.totalPages : 1
    };
    return enhancedResult;
};
}}),
"[project]/src/hooks/api/useApiMutation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Standardized API Mutation Hook
 * @description Provides consistent API mutation patterns with optimistic updates, error handling, and loading states
 */ __turbopack_context__.s({
    "useApiMutation": (()=>useApiMutation),
    "useCreateMutation": (()=>useCreateMutation),
    "useDeleteMutation": (()=>useDeleteMutation),
    "useUpdateMutation": (()=>useUpdateMutation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/utils/use-toast.ts [app-ssr] (ecmascript)");
;
;
const useApiMutation = (mutationFn, options = {})=>{
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useToast"])();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { showErrorToast = true, showSuccessToast = true, successMessage = 'Operation completed successfully', errorMessage, invalidateQueries = [], enableOptimisticUpdates = false, optimisticUpdateFn, optimisticQueryKey, onSuccess, onError, onMutate, ...mutationOptions } = options;
    const mutationResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn,
        onMutate: async (variables)=>{
            // Handle optimistic updates
            if (enableOptimisticUpdates && optimisticQueryKey && optimisticUpdateFn) {
                // Cancel any outgoing refetches
                await queryClient.cancelQueries({
                    queryKey: optimisticQueryKey
                });
                // Snapshot the previous value
                const previousData = queryClient.getQueryData(optimisticQueryKey);
                // Optimistically update to the new value
                const optimisticData = optimisticUpdateFn(variables);
                queryClient.setQueryData(optimisticQueryKey, optimisticData);
                // Return a context object with the snapshotted value
                const context = {
                    previousData
                };
                // Call custom onMutate if provided
                const customContext = await onMutate?.(variables);
                return customContext ? {
                    ...context,
                    ...customContext
                } : context;
            }
            return onMutate?.(variables);
        },
        onSuccess: (data, variables, context)=>{
            // Show success toast
            if (showSuccessToast) {
                toast({
                    title: 'Success',
                    description: successMessage
                });
            }
            // Invalidate and refetch queries
            invalidateQueries.forEach((queryKey)=>{
                queryClient.invalidateQueries({
                    queryKey
                });
            });
            // Call custom onSuccess if provided
            onSuccess?.(data, variables, context);
        },
        onError: (error, variables, context)=>{
            // Rollback optimistic updates
            if (enableOptimisticUpdates && optimisticQueryKey && context && typeof context === 'object' && 'previousData' in context) {
                queryClient.setQueryData(optimisticQueryKey, context.previousData);
            }
            // Show error toast
            if (showErrorToast) {
                const message = errorMessage || error.message || 'An error occurred';
                toast({
                    title: 'Error',
                    description: message,
                    variant: 'destructive'
                });
            }
            // Call custom onError if provided
            onError?.(error, variables, context);
        },
        ...mutationOptions
    });
    // Enhanced result with additional utilities
    const enhancedResult = {
        ...mutationResult,
        executeAsync: async (variables)=>{
            return new Promise((resolve, reject)=>{
                mutationResult.mutate(variables, {
                    onSuccess: (data)=>resolve(data),
                    onError: (error)=>reject(error)
                });
            });
        },
        isExecuting: mutationResult.isPending,
        lastExecuted: mutationResult.submittedAt || null
    };
    return enhancedResult;
};
const useCreateMutation = (mutationFn, listQueryKey, options = {})=>{
    return useApiMutation(mutationFn, {
        ...options,
        invalidateQueries: [
            listQueryKey
        ],
        successMessage: options.successMessage || 'Item created successfully'
    });
};
const useUpdateMutation = (mutationFn, listQueryKey, getDetailQueryKey, options = {})=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return useApiMutation(mutationFn, {
        ...options,
        successMessage: options.successMessage || 'Item updated successfully',
        onSuccess: (data, variables, context)=>{
            // Invalidate list query
            queryClient.invalidateQueries({
                queryKey: listQueryKey
            });
            // Update detail query cache
            const detailQueryKey = getDetailQueryKey(data);
            queryClient.setQueryData(detailQueryKey, data);
            // Call custom onSuccess if provided
            options.onSuccess?.(data, variables, context);
        }
    });
};
const useDeleteMutation = (mutationFn, listQueryKey, getDetailQueryKey, options = {})=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return useApiMutation(mutationFn, {
        ...options,
        successMessage: options.successMessage || 'Item deleted successfully',
        onSuccess: (data, variables, context)=>{
            // Invalidate list query
            queryClient.invalidateQueries({
                queryKey: listQueryKey
            });
            // Remove detail query from cache
            const detailQueryKey = getDetailQueryKey(variables);
            queryClient.removeQueries({
                queryKey: detailQueryKey
            });
            // Call custom onSuccess if provided
            options.onSuccess?.(data, variables, context);
        }
    });
};
}}),
"[project]/src/hooks/api/useNavigationPrefetch.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Navigation prefetching hook for performance optimization
 * @module hooks/useNavigationPrefetch
 */ __turbopack_context__.s({
    "USER_JOURNEYS": (()=>USER_JOURNEYS),
    "useJourneyPrefetch": (()=>useJourneyPrefetch),
    "useNavigationPrefetch": (()=>useNavigationPrefetch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queryClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)");
;
;
;
;
;
/**
 * Create authentication-aware prefetch patterns
 * @param isAuthReady - Whether authentication system is ready for API calls
 */ const createPrefetchPatterns = (isAuthReady)=>({
        // Dashboard route - prefetch all critical data
        '/': ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["prefetchUtils"].prefetchDashboardData(isAuthReady),
        // Admin routes - Updated to use new reliability API service
        '/admin': ()=>{
            if (!isAuthReady) {
                console.warn('Authentication not ready, deferring admin data prefetch.');
                return Promise.resolve();
            }
            return Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                    queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].getAll(),
                    queryKey: [
                        'admin',
                        'users'
                    ],
                    staleTime: 5 * 60 * 1000
                })
            ]);
        },
        '/admin/audit': ()=>{
            if (!isAuthReady) {
                console.warn('Authentication not ready, deferring admin audit data prefetch.');
                return Promise.resolve();
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                queryFn: async ()=>{
                    const { adminService } = await __turbopack_context__.r("[project]/src/lib/api/services/admin/index.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
                    return adminService.getRecentErrors();
                },
                queryKey: [
                    'admin',
                    'audit'
                ],
                staleTime: 1 * 60 * 1000
            });
        },
        // Reliability dashboard routes - New comprehensive monitoring
        '/reliability': ()=>{
            if (!isAuthReady) {
                console.warn('Authentication not ready, deferring reliability data prefetch.');
                return Promise.resolve();
            }
            return Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                    queryFn: async ()=>{
                        const { reliabilityApiService } = await __turbopack_context__.r("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
                        // Prefetch system health data
                        return reliabilityApiService.getSystemHealth();
                    },
                    queryKey: [
                        'reliability',
                        'health'
                    ],
                    staleTime: 15 * 1000
                }),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                    queryFn: async ()=>{
                        const { reliabilityApiService } = await __turbopack_context__.r("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
                        // Prefetch circuit breaker data
                        return reliabilityApiService.getCircuitBreakerStatus();
                    },
                    queryKey: [
                        'reliability',
                        'circuit-breakers'
                    ],
                    staleTime: 30 * 1000
                })
            ]);
        },
        '/admin/users': ()=>{
            if (!isAuthReady) {
                console.warn('Authentication not ready, deferring admin users data prefetch.');
                return Promise.resolve();
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].getAll(),
                queryKey: [
                    'admin',
                    'users'
                ],
                staleTime: 5 * 60 * 1000
            });
        },
        // Delegation routes
        '/delegations': ()=>{
            if (!isAuthReady) {
                console.warn('Authentication not ready, deferring delegations data prefetch.');
                return Promise.resolve();
            }
            return Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                    queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationApiService"].getAll(),
                    queryKey: [
                        'delegations'
                    ],
                    staleTime: 5 * 60 * 1000
                }),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                    queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].getAll(),
                    queryKey: [
                        'employees'
                    ],
                    staleTime: 10 * 60 * 1000
                }),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                    queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].getAll(),
                    queryKey: [
                        'vehicles'
                    ],
                    staleTime: 10 * 60 * 1000
                })
            ]);
        },
        '/delegations/add': ()=>{
            if (!isAuthReady) {
                console.warn('Authentication not ready, deferring delegation add data prefetch.');
                return Promise.resolve();
            }
            return Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                    queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].getAll(),
                    queryKey: [
                        'employees'
                    ],
                    staleTime: 10 * 60 * 1000
                }),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                    queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].getAll(),
                    queryKey: [
                        'vehicles'
                    ],
                    staleTime: 10 * 60 * 1000
                })
            ]);
        },
        // Employee routes
        '/employees': ()=>{
            if (!isAuthReady) {
                console.warn('Authentication not ready, deferring employees data prefetch.');
                return Promise.resolve();
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].getAll(),
                queryKey: [
                    'employees'
                ],
                staleTime: 10 * 60 * 1000
            });
        },
        '/employees/new': ()=>{
            if (!isAuthReady) {
                console.warn('Authentication not ready, deferring employees new data prefetch.');
                return Promise.resolve();
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].getAll(),
                queryKey: [
                    'employees'
                ],
                staleTime: 10 * 60 * 1000
            });
        },
        '/supabase-diagnostics': ()=>{
            if (!isAuthReady) {
                console.warn('Authentication not ready, deferring diagnostics data prefetch.');
                return Promise.resolve();
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                queryFn: async ()=>{
                    // Diagnostics now redirect to reliability dashboard
                    // Keep minimal prefetch for backward compatibility
                    return {
                        redirectTo: '/reliability'
                    };
                },
                queryKey: [
                    'admin',
                    'diagnostics'
                ],
                staleTime: 30 * 1000
            });
        },
        // Task routes
        '/tasks': ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["prefetchUtils"].prefetchTaskManagementData(isAuthReady),
        '/tasks/new': ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["prefetchUtils"].prefetchTaskManagementData(isAuthReady),
        // Vehicle routes
        '/vehicles': ()=>{
            if (!isAuthReady) {
                console.warn('Authentication not ready, deferring vehicles data prefetch.');
                return Promise.resolve();
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].getAll(),
                queryKey: [
                    'vehicles'
                ],
                staleTime: 10 * 60 * 1000
            });
        },
        '/vehicles/new': ()=>{
            if (!isAuthReady) {
                console.warn('Authentication not ready, deferring vehicles new data prefetch.');
                return Promise.resolve();
            }
            return Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"].prefetchQuery({
                    queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].getAll(),
                    queryKey: [
                        'vehicles'
                    ],
                    staleTime: 10 * 60 * 1000
                })
            ]);
        }
    });
const useNavigationPrefetch = ()=>{
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const { isInitialized, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthContext"])();
    // Determine if authentication system is ready for API calls
    const isAuthReady = isInitialized && !loading;
    /**
   * Prefetch data for a specific route
   */ const prefetchRoute = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (route)=>{
        try {
            // Get authentication-aware prefetch patterns
            const PREFETCH_PATTERNS = createPrefetchPatterns(isAuthReady);
            // Find exact match first
            const exactPattern = PREFETCH_PATTERNS[route];
            if (exactPattern) {
                await exactPattern();
                return;
            }
            // Check for dynamic routes (e.g., /vehicles/[id])
            if (route.includes('/vehicles/') && route !== '/vehicles/new') {
                const vehicleId = route.split('/vehicles/')[1]?.split('/')[0];
                if (vehicleId && !isNaN(Number(vehicleId))) {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["prefetchUtils"].prefetchVehicleDetails(Number(vehicleId), isAuthReady);
                }
                return;
            }
            // Check for edit routes
            if (route.includes('/edit')) {
                const basePath = route.split('/edit')[0];
                const basePattern = PREFETCH_PATTERNS[basePath];
                if (basePattern) {
                    await basePattern();
                }
                return;
            }
            // Fallback: prefetch common data for unknown routes
            console.log(`No specific prefetch pattern for route: ${route}, using fallback`);
        } catch (error) {
            console.warn(`Failed to prefetch data for route ${route}:`, error);
        }
    }, [
        isAuthReady
    ]);
    /**
   * Enhanced navigation with prefetching
   */ const navigateWithPrefetch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (route)=>{
        // Start prefetching immediately
        const prefetchPromise = prefetchRoute(route);
        // Navigate immediately (don't wait for prefetch)
        router.push(route);
        // Let prefetch complete in background
        prefetchPromise.catch((error)=>{
            console.warn(`Background prefetch failed for ${route}:`, error);
        });
    }, [
        router,
        prefetchRoute
    ]);
    /**
   * Prefetch on hover (for link components)
   */ const handleLinkHover = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((route)=>{
        // Debounce hover prefetching to avoid excessive requests
        const timeoutId = setTimeout(()=>{
            prefetchRoute(route);
        }, 100);
        return ()=>clearTimeout(timeoutId);
    }, [
        prefetchRoute
    ]);
    /**
   * Prefetch multiple routes (for anticipated user journeys)
   */ const prefetchUserJourney = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (routes)=>{
        const prefetchPromises = routes.map((route)=>prefetchRoute(route));
        await Promise.allSettled(prefetchPromises);
    }, [
        prefetchRoute
    ]);
    return {
        handleLinkHover,
        navigateWithPrefetch,
        prefetchRoute,
        prefetchUserJourney
    };
};
const USER_JOURNEYS = {
    // Admin workflow
    ADMIN_WORKFLOW: [
        '/admin',
        '/admin/users',
        '/admin/audit',
        '/supabase-diagnostics'
    ],
    // Dashboard to detail views
    DASHBOARD_DRILL_DOWN: [
        '/',
        '/vehicles',
        '/tasks',
        '/delegations',
        '/employees'
    ],
    // Delegation workflow
    DELEGATION_WORKFLOW: [
        '/delegations',
        '/delegations/add',
        '/employees',
        '/vehicles'
    ],
    // Task management workflow
    TASK_MANAGEMENT: [
        '/tasks',
        '/tasks/new',
        '/employees'
    ],
    // Vehicle management workflow
    VEHICLE_MANAGEMENT: [
        '/vehicles',
        '/vehicles/new'
    ]
};
const useJourneyPrefetch = ()=>{
    const { prefetchUserJourney } = useNavigationPrefetch();
    const prefetchVehicleManagement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return prefetchUserJourney(USER_JOURNEYS.VEHICLE_MANAGEMENT);
    }, [
        prefetchUserJourney
    ]);
    const prefetchTaskManagement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return prefetchUserJourney(USER_JOURNEYS.TASK_MANAGEMENT);
    }, [
        prefetchUserJourney
    ]);
    const prefetchDelegationWorkflow = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return prefetchUserJourney(USER_JOURNEYS.DELEGATION_WORKFLOW);
    }, [
        prefetchUserJourney
    ]);
    const prefetchDashboardDrillDown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return prefetchUserJourney(USER_JOURNEYS.DASHBOARD_DRILL_DOWN);
    }, [
        prefetchUserJourney
    ]);
    const prefetchAdminWorkflow = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return prefetchUserJourney(USER_JOURNEYS.ADMIN_WORKFLOW);
    }, [
        prefetchUserJourney
    ]);
    return {
        prefetchAdminWorkflow,
        prefetchDashboardDrillDown,
        prefetchDelegationWorkflow,
        prefetchTaskManagement,
        prefetchVehicleManagement
    };
};
const getRecentErrors = ()=>{
    return [];
};
}}),
"[project]/src/hooks/api/useQueryOptimization.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Query optimization hook for enhanced performance
 * @module hooks/useQueryOptimization
 */ __turbopack_context__.s({
    "useCriticalDataOptimization": (()=>useCriticalDataOptimization),
    "useDynamicDataOptimization": (()=>useDynamicDataOptimization),
    "useQueryOptimization": (()=>useQueryOptimization),
    "useQueryPerformanceMonitor": (()=>useQueryPerformanceMonitor),
    "useRealTimeDataOptimization": (()=>useRealTimeDataOptimization),
    "useStaticDataOptimization": (()=>useStaticDataOptimization)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
/**
 * Optimized query options based on data type
 */ const OPTIMIZATION_PRESETS = {
    critical: {
        gcTime: 2 * 60 * 1000,
        refetchInterval: false,
        refetchOnReconnect: true,
        refetchOnWindowFocus: true,
        staleTime: 0
    },
    dynamic: {
        gcTime: 15 * 60 * 1000,
        refetchInterval: false,
        refetchOnReconnect: true,
        refetchOnWindowFocus: true,
        staleTime: 5 * 60 * 1000
    },
    realtime: {
        gcTime: 5 * 60 * 1000,
        refetchInterval: false,
        refetchOnReconnect: true,
        refetchOnWindowFocus: true,
        staleTime: 30 * 1000
    },
    static: {
        gcTime: 60 * 60 * 1000,
        refetchInterval: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
        staleTime: 30 * 60 * 1000
    }
};
const useQueryOptimization = (config = {})=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { customCacheTime, customStaleTime, dataType = 'dynamic', enableBackgroundRefetch = true, enableOptimisticUpdates = true } = config;
    /**
   * Get optimized query options based on data type
   */ const getOptimizedQueryOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((baseOptions = {})=>{
        const preset = OPTIMIZATION_PRESETS[dataType];
        return {
            ...baseOptions,
            gcTime: customCacheTime ?? preset.gcTime,
            refetchInterval: enableBackgroundRefetch ? preset.refetchInterval : false,
            refetchOnReconnect: enableBackgroundRefetch ? preset.refetchOnReconnect : false,
            refetchOnWindowFocus: enableBackgroundRefetch ? preset.refetchOnWindowFocus : false,
            // Enhanced error handling
            retry: (failureCount, error)=>{
                // Don't retry on 4xx errors
                if (error && typeof error === 'object' && 'status' in error) {
                    const status = error.status;
                    if (status >= 400 && status < 500) {
                        return false;
                    }
                }
                return failureCount < 3;
            },
            // Exponential backoff
            retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30_000),
            staleTime: customStaleTime ?? preset.staleTime
        };
    }, [
        dataType,
        enableBackgroundRefetch,
        customStaleTime,
        customCacheTime
    ]);
    /**
   * Get optimized mutation options with optimistic updates
   */ const getOptimizedMutationOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((baseMutationOptions = {}, optimisticUpdateConfig)=>{
        const options = {
            ...baseMutationOptions,
            // Enhanced retry logic for mutations
            retry: (failureCount, error)=>{
                // Generally don't retry mutations to avoid side effects
                // Only retry on network errors
                if (error && typeof error === 'object' && 'message' in error) {
                    const message = error.message?.toLowerCase() || '';
                    if (message.includes('network') || message.includes('timeout')) {
                        return failureCount < 1; // Only retry once for network errors
                    }
                }
                return false;
            }
        };
        // Add optimistic updates if enabled and config provided
        if (enableOptimisticUpdates && optimisticUpdateConfig) {
            const { queryKey, updateFn } = optimisticUpdateConfig;
            options.onMutate = async (variables)=>{
                // Cancel any outgoing refetches
                await queryClient.cancelQueries({
                    queryKey
                });
                // Snapshot the previous value
                const previousData = queryClient.getQueryData(queryKey);
                // Optimistically update to the new value
                queryClient.setQueryData(queryKey, (old)=>updateFn(old, variables));
                // Return a context object with the snapshotted value
                return {
                    previousData
                };
            };
            options.onError = (err, variables, context)=>{
                // If the mutation fails, use the context returned from onMutate to roll back
                if (context && typeof context === 'object' && 'previousData' in context) {
                    queryClient.setQueryData(queryKey, context.previousData);
                }
                // Call original onError if provided
                baseMutationOptions.onError?.(err, variables, context);
            };
            options.onSettled = (data, error, variables, context)=>{
                // Always refetch after error or success to ensure we have the latest data
                queryClient.invalidateQueries({
                    queryKey
                });
                // Call original onSettled if provided
                baseMutationOptions.onSettled?.(data, error, variables, context);
            };
        }
        return options;
    }, [
        queryClient,
        enableOptimisticUpdates
    ]);
    /**
   * Prefetch related data based on current query
   */ const prefetchRelatedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (_currentQueryKey, relatedQueries)=>{
        const prefetchPromises = relatedQueries.map(({ queryFn, queryKey, staleTime })=>queryClient.prefetchQuery({
                queryFn,
                queryKey,
                staleTime: staleTime ?? OPTIMIZATION_PRESETS[dataType].staleTime
            }));
        await Promise.allSettled(prefetchPromises);
    }, [
        queryClient,
        dataType
    ]);
    /**
   * Intelligent cache invalidation
   */ const invalidateRelatedQueries = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((baseQueryKey, relatedPatterns = [])=>{
        // Invalidate the base query
        queryClient.invalidateQueries({
            queryKey: baseQueryKey
        });
        // Invalidate related queries based on patterns
        for (const pattern of relatedPatterns){
            queryClient.invalidateQueries({
                predicate: (query)=>{
                    const keyString = JSON.stringify(query.queryKey);
                    return keyString.includes(pattern);
                }
            });
        }
    }, [
        queryClient
    ]);
    /**
   * Batch multiple cache updates
   */ const batchCacheUpdates = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((updates)=>{
        // Batch updates manually since batch method may not be available
        for (const { queryKey, updateFn } of updates){
            queryClient.setQueryData(queryKey, updateFn);
        }
    }, [
        queryClient
    ]);
    /**
   * Get cache statistics for monitoring
   */ const getCacheStats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const cache = queryClient.getQueryCache();
        const queries = cache.getAll();
        const stats = {
            activeQueries: queries.filter((q)=>q.getObserversCount() > 0).length,
            errorQueries: queries.filter((q)=>q.state.status === 'error').length,
            loadingQueries: queries.filter((q)=>q.state.status === 'pending').length,
            staleQueries: queries.filter((q)=>q.isStale()).length,
            totalQueries: queries.length
        };
        return stats;
    }, [
        queryClient
    ]);
    return {
        batchCacheUpdates,
        getCacheStats,
        getOptimizedMutationOptions,
        getOptimizedQueryOptions,
        invalidateRelatedQueries,
        prefetchRelatedData
    };
};
const useStaticDataOptimization = ()=>{
    return useQueryOptimization({
        dataType: 'static',
        enableBackgroundRefetch: false
    });
};
const useDynamicDataOptimization = ()=>{
    return useQueryOptimization({
        dataType: 'dynamic',
        enableBackgroundRefetch: true
    });
};
const useRealTimeDataOptimization = ()=>{
    return useQueryOptimization({
        dataType: 'realtime',
        enableBackgroundRefetch: true,
        enableOptimisticUpdates: true
    });
};
const useCriticalDataOptimization = ()=>{
    return useQueryOptimization({
        dataType: 'critical',
        enableBackgroundRefetch: true,
        enableOptimisticUpdates: true
    });
};
const useQueryPerformanceMonitor = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const getSlowQueries = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((thresholdMs = 2000)=>{
        const cache = queryClient.getQueryCache();
        const queries = cache.getAll();
        return queries.filter((query)=>{
            // Use dataUpdatedAt as a proxy for query duration
            const now = Date.now();
            const lastUpdate = query.state.dataUpdatedAt || 0;
            const duration = now - lastUpdate;
            return duration > thresholdMs && query.state.status === 'success';
        }).map((query)=>({
                duration: Date.now() - (query.state.dataUpdatedAt || 0),
                queryKey: query.queryKey,
                status: query.state.status
            }));
    }, [
        queryClient
    ]);
    const getCacheEfficiency = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const cache = queryClient.getQueryCache();
        const queries = cache.getAll();
        const totalQueries = queries.length;
        const cachedQueries = queries.filter((q)=>q.state.data !== undefined).length;
        return totalQueries > 0 ? cachedQueries / totalQueries * 100 : 0;
    }, [
        queryClient
    ]);
    return {
        getCacheEfficiency,
        getSlowQueries
    };
};
}}),
"[project]/src/hooks/api/useSmartQuery.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Smart Query Hook with WebSocket Integration
 * Automatically disables polling when WebSocket is connected
 * Follows modern best practices for real-time data management
 * @module hooks/useSmartQuery
 */ __turbopack_context__.s({
    "useCrudQuery": (()=>useCrudQuery),
    "useNotificationQuery": (()=>useNotificationQuery),
    "useReliabilityQuery": (()=>useReliabilityQuery),
    "useSmartQuery": (()=>useSmartQuery),
    "useSystemQuery": (()=>useSystemQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$WebSocketManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/WebSocketManager.ts [app-ssr] (ecmascript)");
;
;
;
/**
 * Mapping of domain channels to Socket.IO room names
 * This ensures the frontend joins the correct rooms that the backend emits events to
 */ const CHANNEL_ROOM_MAPPING = {
    crud: 'entity-updates',
    notifications: 'notifications-monitoring',
    reliability: 'reliability-monitoring',
    system: 'system-monitoring'
};
function useCrudQuery(queryKey, queryFn, entityType, options) {
    return useSmartQuery(queryKey, queryFn, {
        channel: 'crud',
        events: [
            `${entityType}:created`,
            `${entityType}:updated`,
            `${entityType}:deleted`,
            `refresh:${entityType}`
        ],
        fallbackInterval: 30_000
    }, options);
}
function useNotificationQuery(queryKey, queryFn, options) {
    return useSmartQuery(queryKey, queryFn, {
        channel: 'notifications',
        events: [
            'notification-created',
            'notification-updated'
        ],
        fallbackInterval: 60_000
    }, options);
}
function useReliabilityQuery(queryKey, queryFn, monitoringType, options) {
    // Increased intervals to reduce aggressive polling and cancellations
    const intervalMap = {
        alerts: 30_000,
        'circuit-breakers': 60_000,
        health: 45_000,
        metrics: 60_000
    };
    const webSocketManager = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$WebSocketManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWebSocketManager"])();
    // Join reliability monitoring room when WebSocket is connected
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (webSocketManager.isConnected()) {
            console.debug(`[ReliabilityQuery] Joining reliability-monitoring room for ${monitoringType}`);
            webSocketManager.joinRoom('reliability-monitoring');
        }
        // Subscribe to connection state changes to join room when connected
        const unsubscribe = webSocketManager.onStateChange((state)=>{
            if (state === 'connected') {
                console.debug(`[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ${monitoringType}`);
                webSocketManager.joinRoom('reliability-monitoring');
            }
        });
        return ()=>{
            unsubscribe();
            // Leave room when component unmounts
            if (webSocketManager.isConnected()) {
                webSocketManager.leaveRoom('reliability-monitoring');
            }
        };
    }, [
        webSocketManager,
        monitoringType
    ]);
    return useSmartQuery(queryKey, queryFn, {
        channel: 'reliability',
        events: [
            `${monitoringType}-update`,
            `${monitoringType}-created`,
            `${monitoringType}-resolved`
        ],
        fallbackInterval: intervalMap[monitoringType]
    }, options);
}
function useSmartQuery(queryKey, queryFn, config, options) {
    const { channel, enableFallback = true, enableWebSocket = true, events, fallbackInterval = 30_000 } = config;
    const [isWebSocketConnected, setIsWebSocketConnected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const webSocketManager = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$WebSocketManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWebSocketManager"])();
    // Track WebSocket connection state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const updateConnectionState = ()=>{
            setIsWebSocketConnected(webSocketManager.isConnected());
        };
        // Initial state
        updateConnectionState();
        // Subscribe to state changes
        const unsubscribe = webSocketManager.onStateChange(updateConnectionState);
        return unsubscribe;
    }, [
        webSocketManager
    ]);
    // Determine if we should use fallback polling
    const isUsingFallback = enableFallback && (!enableWebSocket || !isWebSocketConnected);
    // Configure React Query options based on WebSocket state
    const queryOptions = {
        // Longer cache time for better performance
        gcTime: 10 * 60 * 1000,
        queryFn,
        queryKey,
        // Disable polling when WebSocket is connected
        refetchInterval: isUsingFallback ? fallbackInterval : false,
        refetchOnReconnect: true,
        // Enable background refetch only when using fallback
        refetchOnWindowFocus: isUsingFallback,
        // Shorter stale time when using WebSocket (real-time updates)
        staleTime: isWebSocketConnected ? 0 : 30_000,
        ...options
    };
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const queryResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])(queryOptions);
    // Manage Socket.IO room joining/leaving based on channel
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!enableWebSocket || !isWebSocketConnected) {
            return;
        }
        const roomName = CHANNEL_ROOM_MAPPING[channel];
        if (!roomName) {
            console.warn(`[SmartQuery] No room mapping found for channel: ${channel}`);
            return;
        }
        // Join the appropriate room for this channel
        try {
            webSocketManager.joinRoom(roomName);
            console.log(`[SmartQuery] Joined room: ${roomName} for channel: ${channel}`);
        } catch (error) {
            console.error(`[SmartQuery] Failed to join room ${roomName}:`, error);
        }
        // Cleanup: leave room when component unmounts or dependencies change
        return ()=>{
            try {
                webSocketManager.leaveRoom(roomName);
                console.log(`[SmartQuery] Left room: ${roomName} for channel: ${channel}`);
            } catch (error) {
                console.error(`[SmartQuery] Failed to leave room ${roomName}:`, error);
            }
        };
    }, [
        enableWebSocket,
        isWebSocketConnected,
        channel,
        webSocketManager
    ]);
    // Subscribe to WebSocket events for real-time updates
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!enableWebSocket || !isWebSocketConnected || events.length === 0) {
            return;
        }
        const unsubscribers = [];
        // Subscribe to each event
        for (const event of events){
            const unsubscribe = webSocketManager.subscribe(channel, event, (data)=>{
                console.log(`[SmartQuery] WebSocket event received: ${channel}:${event}`, data);
                // Invalidate the specific query to trigger refetch
                queryClient.invalidateQueries({
                    queryKey
                });
            });
            unsubscribers.push(unsubscribe);
        }
        return ()=>{
            for (const unsubscribe of unsubscribers)unsubscribe();
        };
    }, [
        enableWebSocket,
        isWebSocketConnected,
        events,
        channel,
        webSocketManager,
        queryClient,
        queryKey
    ]);
    return {
        ...queryResult,
        isUsingFallback,
        isWebSocketConnected
    };
}
function useSystemQuery(queryKey, queryFn, options) {
    return useSmartQuery(queryKey, queryFn, {
        channel: 'system',
        events: [
            'system-update',
            'config-changed'
        ],
        fallbackInterval: 120_000
    }, options);
}
}}),
"[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file API Hooks Index
 * @description Centralized exports for all API-related hooks
 */ // Core API hooks
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiMutation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiMutation.ts [app-ssr] (ecmascript)");
// Advanced API hooks (moved from lib/hooks)
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useNavigationPrefetch$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useNavigationPrefetch.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useQueryOptimization$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useQueryOptimization.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-ssr] (ecmascript)");
;
;
;
;
;
}}),
"[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiMutation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiMutation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useNavigationPrefetch$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useNavigationPrefetch.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useQueryOptimization$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useQueryOptimization.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/hooks/ui/useNotifications.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Custom hook for notification management using Zustand AppStore
 * @module hooks/useNotifications
 */ __turbopack_context__.s({
    "useNotifications": (()=>useNotifications),
    "useWorkHubNotifications": (()=>useWorkHubNotifications)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/appStore.ts [app-ssr] (ecmascript)");
;
;
const useNotifications = ()=>{
    const addNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.addNotification);
    const removeNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.removeNotification);
    const clearAllNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.clearAllNotifications);
    const unreadCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.unreadNotificationCount);
    /**
   * Show a success notification
   */ const showSuccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        addNotification({
            message,
            type: 'success'
        });
    }, [
        addNotification
    ]);
    /**
   * Show an error notification
   */ const showError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        addNotification({
            message,
            type: 'error'
        });
    }, [
        addNotification
    ]);
    /**
   * Show a warning notification
   */ const showWarning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        addNotification({
            message,
            type: 'warning'
        });
    }, [
        addNotification
    ]);
    /**
   * Show an info notification
   */ const showInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        addNotification({
            message,
            type: 'info'
        });
    }, [
        addNotification
    ]);
    /**
   * Show a notification for API operation results
   */ const showApiResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((success, successMessage, errorMessage)=>{
        if (success) {
            showSuccess(successMessage);
        } else {
            showError(errorMessage);
        }
    }, [
        showSuccess,
        showError
    ]);
    /**
   * Show a notification with auto-dismiss after specified time
   */ const showTemporary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((type, message, dismissAfter = 5000)=>{
        addNotification({
            message,
            type
        });
        // Auto-dismiss after specified time
        setTimeout(()=>{
            // Note: This is a simplified approach. In a real implementation,
            // you might want to store the notification ID and remove specifically that one
            const notifications = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().notifications;
            const latestNotification = notifications.at(-1);
            if (latestNotification && latestNotification.message === message) {
                removeNotification(latestNotification.id);
            }
        }, dismissAfter);
    }, [
        addNotification,
        removeNotification
    ]);
    /**
   * Show a loading notification that can be updated
   */ const showLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message = 'Loading...')=>{
        addNotification({
            message,
            type: 'info'
        });
        // Return the notification ID for potential updates
        const notifications = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().notifications;
        return notifications.at(-1)?.id;
    }, [
        addNotification
    ]);
    /**
   * Update a loading notification to success or error
   */ const updateLoadingNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((notificationId, success, message)=>{
        removeNotification(notificationId);
        if (success) {
            showSuccess(message);
        } else {
            showError(message);
        }
    }, [
        removeNotification,
        showSuccess,
        showError
    ]);
    return {
        clearAllNotifications,
        // Store methods
        removeNotification,
        // Advanced methods
        showApiResult,
        showError,
        showInfo,
        showLoading,
        // Basic notification methods
        showSuccess,
        showTemporary,
        showWarning,
        unreadCount,
        updateLoadingNotification
    };
};
const useWorkHubNotifications = ()=>{
    const { clearAllNotifications, removeNotification, showError, showInfo, showSuccess, showWarning, unreadCount } = useNotifications();
    /**
   * Show delegation-related notifications
   */ const showDelegationUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message, actionUrl)=>{
        const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
        addNotification({
            ...actionUrl && {
                actionUrl
            },
            category: 'delegation',
            message,
            type: 'delegation-update'
        });
    }, []);
    /**
   * Show vehicle maintenance notifications
   */ const showVehicleMaintenance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message, actionUrl)=>{
        const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
        addNotification({
            ...actionUrl && {
                actionUrl
            },
            category: 'vehicle',
            message,
            type: 'vehicle-maintenance'
        });
    }, []);
    /**
   * Show task assignment notifications
   */ const showTaskAssigned = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message, actionUrl)=>{
        const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
        addNotification({
            ...actionUrl && {
                actionUrl
            },
            category: 'task',
            message,
            type: 'task-assigned'
        });
    }, []);
    /**
   * Show employee update notifications
   */ const showEmployeeUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message, actionUrl)=>{
        const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
        addNotification({
            ...actionUrl && {
                actionUrl
            },
            category: 'employee',
            message,
            type: 'employee-update'
        });
    }, []);
    return {
        clearAllNotifications,
        // Management
        removeNotification,
        // WorkHub-specific notifications
        showDelegationUpdate,
        showEmployeeUpdate,
        showError,
        showInfo,
        // Basic notifications
        showSuccess,
        showTaskAssigned,
        showVehicleMaintenance,
        showWarning,
        unreadCount
    };
};
}}),
"[project]/src/hooks/ui/useSidebar.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Sidebar management hook using Zustand AppStore
 * @module hooks/useSidebar
 */ __turbopack_context__.s({
    "useSidebar": (()=>useSidebar)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/appStore.ts [app-ssr] (ecmascript)");
;
;
const useSidebar = ()=>{
    const sidebarOpen = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.sidebarOpen);
    const toggleSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.toggleSidebar);
    /**
   * Open the sidebar if it's closed
   */ const openSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!sidebarOpen) {
            toggleSidebar();
        }
    }, [
        sidebarOpen,
        toggleSidebar
    ]);
    /**
   * Close the sidebar if it's open
   */ const closeSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (sidebarOpen) {
            toggleSidebar();
        }
    }, [
        sidebarOpen,
        toggleSidebar
    ]);
    /**
   * Get sidebar-specific CSS classes
   */ const getSidebarClasses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return {
            content: sidebarOpen ? 'content-shifted' : 'content-normal',
            overlay: sidebarOpen ? 'overlay-visible' : 'overlay-hidden',
            sidebar: sidebarOpen ? 'sidebar-open' : 'sidebar-closed',
            toggle: sidebarOpen ? 'toggle-close' : 'toggle-open'
        };
    }, [
        sidebarOpen
    ]);
    /**
   * Get sidebar state for accessibility
   */ const getAriaAttributes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return {
            'aria-expanded': sidebarOpen,
            'aria-label': sidebarOpen ? 'Close sidebar' : 'Open sidebar'
        };
    }, [
        sidebarOpen
    ]);
    return {
        closeSidebar,
        getAriaAttributes,
        // Utilities
        getSidebarClasses,
        isClosed: !sidebarOpen,
        isOpen: sidebarOpen,
        openSidebar,
        // State
        sidebarOpen,
        // Actions
        toggleSidebar
    };
};
}}),
"[project]/src/hooks/forms/services/FormSubmissionConfig.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Form Submission Configuration Service
 * @description Default configurations for form submission following SRP
 * @version 2.0.0
 * <AUTHOR> Development Team
 */ __turbopack_context__.s({
    "DEFAULT_ACCESSIBILITY_CONFIG": (()=>DEFAULT_ACCESSIBILITY_CONFIG),
    "DEFAULT_PERFORMANCE_CONFIG": (()=>DEFAULT_PERFORMANCE_CONFIG),
    "DEFAULT_RETRY_CONFIG": (()=>DEFAULT_RETRY_CONFIG),
    "DEFAULT_TOAST_CONFIG": (()=>DEFAULT_TOAST_CONFIG),
    "FormSubmissionConfigService": (()=>FormSubmissionConfigService)
});
const DEFAULT_RETRY_CONFIG = {
    maxAttempts: 3,
    delay: 1000,
    exponentialBackoff: true,
    retryCondition: (error)=>error.message.includes('network') || error.message.includes('timeout') || error.message.includes('502') || error.message.includes('503') || error.message.includes('504')
};
const DEFAULT_ACCESSIBILITY_CONFIG = {
    announceStatus: true,
    focusManagement: 'first-error',
    screenReaderAnnouncements: true
};
const DEFAULT_PERFORMANCE_CONFIG = {
    debounceMs: 300,
    enableDeduplication: true,
    cacheResults: false,
    timeoutMs: 30000
};
const DEFAULT_TOAST_CONFIG = {
    showSuccessToast: true,
    showErrorToast: true,
    successMessage: 'Operation completed successfully',
    errorMessage: 'An unexpected error occurred',
    entityType: 'generic'
};
class FormSubmissionConfigService {
    /**
   * Merge user config with defaults for retry settings
   */ static mergeRetryConfig(userConfig) {
        return {
            ...DEFAULT_RETRY_CONFIG,
            ...userConfig
        };
    }
    /**
   * Merge user config with defaults for accessibility settings
   */ static mergeAccessibilityConfig(userConfig) {
        return {
            ...DEFAULT_ACCESSIBILITY_CONFIG,
            ...userConfig
        };
    }
    /**
   * Merge user config with defaults for performance settings
   */ static mergePerformanceConfig(userConfig) {
        return {
            ...DEFAULT_PERFORMANCE_CONFIG,
            ...userConfig
        };
    }
    /**
   * Merge user config with defaults for toast settings
   */ static mergeToastConfig(userConfig) {
        return {
            ...DEFAULT_TOAST_CONFIG,
            ...userConfig
        };
    }
}
}}),
"[project]/src/hooks/forms/services/FormSubmissionToastService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Form Submission Toast Service
 * @description Handles toast notifications for form submissions following SRP
 * @version 2.0.0
 * <AUTHOR> Development Team
 */ __turbopack_context__.s({
    "FormSubmissionToastService": (()=>FormSubmissionToastService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/toastService.ts [app-ssr] (ecmascript)");
;
class FormSubmissionToastService {
    /**
   * Show success toast based on entity type and configuration
   */ static showSuccessToast(config, data, result) {
        if (!config.showSuccessToast) return;
        const { entityType, entity, successMessage } = config;
        try {
            switch(entityType){
                case 'employee':
                    if (entity) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeToast"].entityCreated(entity);
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Employee Created', successMessage);
                    }
                    break;
                case 'vehicle':
                    if (entity) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleToast"].entityCreated(entity);
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Vehicle Added', successMessage);
                    }
                    break;
                case 'task':
                    if (entity) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskToast"].entityCreated(entity);
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Task Created', successMessage);
                    }
                    break;
                case 'delegation':
                    if (entity) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationToast"].entityCreated(entity);
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Delegation Created', successMessage);
                    }
                    break;
                case 'serviceRecord':
                    if (entity && result) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serviceRecordToast"].serviceRecordCreated(entity.vehicleName || 'Vehicle', entity.serviceType || 'Service');
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Service Record Added', successMessage);
                    }
                    break;
                case 'generic':
                default:
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Success', successMessage || 'Operation completed successfully');
                    break;
            }
        } catch (error) {
            // Fallback to generic success toast if entity-specific fails
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Success', successMessage || 'Operation completed successfully');
        }
    }
    /**
   * Show error toast based on entity type and configuration
   */ static showErrorToast(config, error, data) {
        if (!config.showErrorToast) return;
        const { entityType, errorMessage } = config;
        const errorMsg = error.message || errorMessage || 'An unexpected error occurred';
        try {
            switch(entityType){
                case 'employee':
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeToast"].entityCreationError(errorMsg);
                    break;
                case 'vehicle':
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleToast"].entityCreationError(errorMsg);
                    break;
                case 'task':
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskToast"].entityCreationError(errorMsg);
                    break;
                case 'delegation':
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationToast"].entityCreationError(errorMsg);
                    break;
                case 'serviceRecord':
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serviceRecordToast"].serviceRecordCreationError(errorMsg);
                    break;
                case 'generic':
                default:
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].error('Error', errorMsg);
                    break;
            }
        } catch (toastError) {
            // Fallback to generic error toast if entity-specific fails
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].error('Error', errorMsg);
        }
    }
    /**
   * Show update success toast for existing entities
   */ static showUpdateSuccessToast(config, data, result) {
        if (!config.showSuccessToast) return;
        const { entityType, entity, successMessage } = config;
        try {
            switch(entityType){
                case 'employee':
                    if (entity) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeToast"].entityUpdated(entity);
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Employee Updated', successMessage);
                    }
                    break;
                case 'vehicle':
                    if (entity) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleToast"].entityUpdated(entity);
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Vehicle Updated', successMessage);
                    }
                    break;
                case 'task':
                    if (entity) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskToast"].entityUpdated(entity);
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Task Updated', successMessage);
                    }
                    break;
                case 'delegation':
                    if (entity) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationToast"].entityUpdated(entity);
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Delegation Updated', successMessage);
                    }
                    break;
                case 'serviceRecord':
                    if (entity && result) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serviceRecordToast"].serviceRecordUpdated(entity.vehicleName || 'Vehicle', entity.serviceType || 'Service');
                    } else {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Service Record Updated', successMessage);
                    }
                    break;
                case 'generic':
                default:
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Success', successMessage || 'Update completed successfully');
                    break;
            }
        } catch (error) {
            // Fallback to generic success toast
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success('Success', successMessage || 'Update completed successfully');
        }
    }
    /**
   * Show update error toast for existing entities
   */ static showUpdateErrorToast(config, error, data) {
        if (!config.showErrorToast) return;
        const { entityType, errorMessage } = config;
        const errorMsg = error.message || errorMessage || 'An unexpected error occurred';
        try {
            switch(entityType){
                case 'employee':
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeToast"].entityUpdateError(errorMsg);
                    break;
                case 'vehicle':
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleToast"].entityUpdateError(errorMsg);
                    break;
                case 'task':
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskToast"].entityUpdateError(errorMsg);
                    break;
                case 'delegation':
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationToast"].entityUpdateError(errorMsg);
                    break;
                case 'serviceRecord':
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serviceRecordToast"].serviceRecordUpdateError(errorMsg);
                    break;
                case 'generic':
                default:
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].error('Update Failed', errorMsg);
                    break;
            }
        } catch (toastError) {
            // Fallback to generic error toast
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].error('Update Failed', errorMsg);
        }
    }
    /**
   * Create a dynamic toast service for custom entity types
   */ static createCustomEntityToastService(entityName, getDisplayName) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSimpleEntityToastService"])(entityName, getDisplayName);
    }
}
}}),
"[project]/src/hooks/forms/services/FormSubmissionAccessibilityService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Form Submission Accessibility Service
 * @description Handles accessibility features for form submissions following SRP
 * @version 2.0.0
 * <AUTHOR> Development Team
 */ __turbopack_context__.s({
    "FormSubmissionAccessibilityService": (()=>FormSubmissionAccessibilityService)
});
class FormSubmissionAccessibilityService {
    config;
    constructor(config){
        this.config = config;
    }
    /**
   * Announce status for screen readers
   */ announceStatus(message, priority = 'polite') {
        if (!this.config.announceStatus || !this.config.screenReaderAnnouncements) {
            return;
        }
        // Create or update live region for announcements
        let liveRegion = document.getElementById('form-submission-announcements');
        if (!liveRegion) {
            liveRegion = document.createElement('div');
            liveRegion.id = 'form-submission-announcements';
            liveRegion.setAttribute('aria-live', priority);
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.className = 'sr-only absolute left-[-10000px] top-[-10000px] w-[1px] h-[1px] overflow-hidden';
            document.body.appendChild(liveRegion);
        }
        // Update the announcement
        liveRegion.textContent = message;
        // Clear the announcement after a delay to allow for multiple announcements
        setTimeout(()=>{
            if (liveRegion && liveRegion.textContent === message) {
                liveRegion.textContent = '';
            }
        }, 1000);
    }
    /**
   * Generate ARIA attributes for form elements
   */ generateAriaAttributes(isLoading, hasError, state) {
        return {
            'aria-busy': isLoading,
            'aria-invalid': hasError,
            'aria-describedby': this.config.errorDescribedBy || (hasError ? 'form-error' : undefined),
            'aria-live': state === 'submitting' || state === 'validating' ? 'polite' : 'off'
        };
    }
    /**
   * Manage focus based on configuration
   */ manageFocus(focusType, formFocus) {
        if (this.config.focusManagement === 'none') return;
        switch(focusType){
            case 'error':
                if (this.config.focusManagement === 'first-error' && formFocus) {
                    // Focus first field with error
                    formFocus('first-error');
                }
                break;
            case 'success':
                if (this.config.focusManagement === 'success-message') {
                    // Focus success message if it exists
                    const successElement = document.getElementById('form-success-message');
                    if (successElement) {
                        successElement.focus();
                    }
                } else if (this.config.focusManagement === 'next-field' && formFocus) {
                    // Focus next logical field or submit button
                    formFocus('next-field');
                }
                break;
            case 'retry':
                if (formFocus) {
                    // Focus retry button or first field
                    formFocus('retry-button');
                }
                break;
        }
    }
    /**
   * Create accessible error message element
   */ createErrorMessage(error) {
        const errorElement = document.createElement('div');
        errorElement.id = this.config.errorDescribedBy || 'form-error';
        errorElement.setAttribute('role', 'alert');
        errorElement.setAttribute('aria-live', 'assertive');
        errorElement.className = 'sr-only';
        errorElement.textContent = error;
        return errorElement;
    }
    /**
   * Update or create error message in DOM
   */ updateErrorMessage(error) {
        const errorId = this.config.errorDescribedBy || 'form-error';
        let errorElement = document.getElementById(errorId);
        if (error) {
            if (!errorElement) {
                errorElement = this.createErrorMessage(error);
                document.body.appendChild(errorElement);
            } else {
                errorElement.textContent = error;
            }
        } else if (errorElement) {
            errorElement.remove();
        }
    }
    /**
   * Get status message for current state
   */ getStatusMessage(state, retryAttempt, maxAttempts) {
        switch(state){
            case 'validating':
                return 'Validating form data...';
            case 'submitting':
                return 'Submitting form...';
            case 'retrying':
                return `Retrying submission... (Attempt ${retryAttempt || 1}/${maxAttempts || 3})`;
            case 'success':
                return 'Form submitted successfully';
            case 'error':
                return 'Form submission failed';
            default:
                return '';
        }
    }
    /**
   * Set up keyboard navigation for submission states
   */ setupKeyboardNavigation() {
        // Add keyboard shortcuts for common actions
        const handleKeyDown = (event)=>{
            // Escape key to cancel submission
            if (event.key === 'Escape') {
                const cancelButton = document.querySelector('[data-form-cancel]');
                if (cancelButton) {
                    cancelButton.click();
                }
            }
            // Ctrl+Enter or Cmd+Enter to submit
            if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                const submitButton = document.querySelector('[type="submit"]');
                if (submitButton && !submitButton.disabled) {
                    submitButton.click();
                }
            }
        };
        document.addEventListener('keydown', handleKeyDown);
        // Return cleanup function
        return ()=>{
            document.removeEventListener('keydown', handleKeyDown);
        };
    }
    /**
   * Create progress announcements for long-running operations
   */ announceProgress(step, totalSteps, stepDescription) {
        if (!this.config.screenReaderAnnouncements) return;
        const message = `Step ${step} of ${totalSteps}: ${stepDescription}`;
        this.announceStatus(message, 'polite');
    }
    /**
   * Cleanup accessibility resources
   */ cleanup() {
        // Remove live region
        const liveRegion = document.getElementById('form-submission-announcements');
        if (liveRegion) {
            liveRegion.remove();
        }
        // Remove error message
        this.updateErrorMessage(null);
    }
}
}}),
"[project]/src/hooks/forms/services/FormSubmissionRetryService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Form Submission Retry Service
 * @description Handles retry logic for form submissions following SRP
 * @version 2.0.0
 * <AUTHOR> Development Team
 */ __turbopack_context__.s({
    "FormSubmissionRetryService": (()=>FormSubmissionRetryService)
});
class FormSubmissionRetryService {
    config;
    currentAttempt = 0;
    constructor(config){
        this.config = config;
    }
    /**
   * Check if retry should be attempted
   */ shouldRetry(error) {
        return this.currentAttempt < this.config.maxAttempts && (this.config.retryCondition ? this.config.retryCondition(error) : true);
    }
    /**
   * Get the delay for the next retry attempt
   */ getRetryDelay() {
        const baseDelay = this.config.delay;
        if (this.config.exponentialBackoff) {
            return baseDelay * Math.pow(2, this.currentAttempt);
        }
        return baseDelay;
    }
    /**
   * Increment the retry attempt counter
   */ incrementAttempt() {
        this.currentAttempt += 1;
        return this.currentAttempt;
    }
    /**
   * Reset the retry attempt counter
   */ resetAttempts() {
        this.currentAttempt = 0;
    }
    /**
   * Get current attempt number
   */ getCurrentAttempt() {
        return this.currentAttempt;
    }
    /**
   * Get maximum attempts
   */ getMaxAttempts() {
        return this.config.maxAttempts;
    }
    /**
   * Sleep utility for retry delays
   */ async sleep(ms) {
        return new Promise((resolve)=>setTimeout(resolve, ms));
    }
    /**
   * Execute retry with proper delay
   */ async executeRetry(retryFn) {
        if (!this.shouldRetry(new Error('Manual retry'))) {
            throw new Error('Maximum retry attempts exceeded');
        }
        const delay = this.getRetryDelay();
        this.incrementAttempt();
        await this.sleep(delay);
        return retryFn();
    }
    /**
   * Get retry status information
   */ getRetryStatus() {
        return {
            currentAttempt: this.currentAttempt,
            maxAttempts: this.config.maxAttempts,
            hasRetriesLeft: this.currentAttempt < this.config.maxAttempts,
            nextDelay: this.getRetryDelay()
        };
    }
    /**
   * Create a new retry service with updated configuration
   */ withConfig(newConfig) {
        return new FormSubmissionRetryService({
            ...this.config,
            ...newConfig
        });
    }
}
}}),
"[project]/src/hooks/forms/services/FormSubmissionPerformanceService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Form Submission Performance Service
 * @description Handles performance tracking and optimization for form submissions following SRP
 * @version 2.0.0
 * <AUTHOR> Development Team
 */ __turbopack_context__.s({
    "FormSubmissionPerformanceService": (()=>FormSubmissionPerformanceService)
});
class FormSubmissionPerformanceService {
    config;
    metrics;
    submissionStartTime = null;
    debounceTimer = null;
    constructor(config){
        this.config = config;
        this.metrics = {
            totalSubmissions: 0,
            successfulSubmissions: 0,
            failedSubmissions: 0,
            averageDuration: 0
        };
    }
    /**
   * Start tracking submission performance
   */ startTiming() {
        this.submissionStartTime = Date.now();
    }
    /**
   * End tracking and update metrics
   */ endTiming(success) {
        if (!this.submissionStartTime) {
            return 0;
        }
        const duration = Date.now() - this.submissionStartTime;
        this.updateMetrics(success, duration);
        this.submissionStartTime = null;
        return duration;
    }
    /**
   * Update performance metrics
   */ updateMetrics(success, duration) {
        const newTotal = this.metrics.totalSubmissions + 1;
        const newSuccessful = success ? this.metrics.successfulSubmissions + 1 : this.metrics.successfulSubmissions;
        const newFailed = success ? this.metrics.failedSubmissions : this.metrics.failedSubmissions + 1;
        const totalDuration = this.metrics.averageDuration * this.metrics.totalSubmissions + duration;
        this.metrics = {
            totalSubmissions: newTotal,
            successfulSubmissions: newSuccessful,
            failedSubmissions: newFailed,
            averageDuration: totalDuration / newTotal
        };
    }
    /**
   * Get current performance metrics
   */ getMetrics() {
        return {
            ...this.metrics
        };
    }
    /**
   * Reset performance metrics
   */ resetMetrics() {
        this.metrics = {
            totalSubmissions: 0,
            successfulSubmissions: 0,
            failedSubmissions: 0,
            averageDuration: 0
        };
    }
    /**
   * Apply debouncing to function execution
   */ debounce(func, delay = this.config.debounceMs) {
        return (...args)=>{
            if (this.debounceTimer) {
                clearTimeout(this.debounceTimer);
            }
            this.debounceTimer = setTimeout(()=>{
                func(...args);
            }, delay);
        };
    }
    /**
   * Clear any pending debounced operations
   */ clearDebounce() {
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = null;
        }
    }
    /**
   * Create timeout promise for request timeout handling
   */ createTimeoutPromise(timeoutMs = this.config.timeoutMs) {
        return new Promise((_, reject)=>{
            setTimeout(()=>{
                reject(new Error(`Request timeout after ${timeoutMs}ms`));
            }, timeoutMs);
        });
    }
    /**
   * Wrap a promise with timeout functionality
   */ async withTimeout(promise, timeoutMs = this.config.timeoutMs) {
        return Promise.race([
            promise,
            this.createTimeoutPromise(timeoutMs)
        ]);
    }
    /**
   * Get success rate as percentage
   */ getSuccessRate() {
        if (this.metrics.totalSubmissions === 0) return 0;
        return this.metrics.successfulSubmissions / this.metrics.totalSubmissions * 100;
    }
    /**
   * Get failure rate as percentage
   */ getFailureRate() {
        if (this.metrics.totalSubmissions === 0) return 0;
        return this.metrics.failedSubmissions / this.metrics.totalSubmissions * 100;
    }
    /**
   * Check if performance is within acceptable thresholds
   */ isPerformanceAcceptable(thresholds) {
        const defaultThresholds = {
            maxAverageDuration: 5000,
            minSuccessRate: 95
        };
        const config = {
            ...defaultThresholds,
            ...thresholds
        };
        return this.metrics.averageDuration <= config.maxAverageDuration && this.getSuccessRate() >= config.minSuccessRate;
    }
    /**
   * Generate performance report
   */ generateReport() {
        const successRate = this.getSuccessRate();
        const failureRate = this.getFailureRate();
        const isAcceptable = this.isPerformanceAcceptable();
        const recommendations = [];
        if (this.metrics.averageDuration > 3000) {
            recommendations.push('Consider optimizing form validation or submission logic');
        }
        if (successRate < 90) {
            recommendations.push('High failure rate detected - review error handling');
        }
        if (this.metrics.totalSubmissions > 100 && this.metrics.averageDuration > 1000) {
            recommendations.push('Consider implementing caching for better performance');
        }
        return {
            metrics: this.getMetrics(),
            successRate,
            failureRate,
            isAcceptable,
            recommendations
        };
    }
    /**
   * Cleanup performance tracking resources
   */ cleanup() {
        this.clearDebounce();
        this.submissionStartTime = null;
    }
}
}}),
"[project]/src/hooks/forms/useFormSubmission.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Universal Form Submission Hook - SOLID Architecture
 * @description Modern form submission hook following SOLID principles
 * @version 4.0.0 - Legacy-free implementation
 * <AUTHOR> Development Team
 *
 * SOLID Principles Applied:
 * ✅ Single Responsibility Principle (SRP): Each service handles one concern
 * ✅ Open/Closed Principle: Extensible through configuration and services
 * ✅ Liskov Substitution Principle: Compatible service implementations
 * ✅ Interface Segregation Principle: Focused service interfaces
 * ✅ Dependency Inversion Principle: Abstraction-based dependencies
 *
 * Modern Features:
 * 🚀 Specialized service architecture with clean separation
 * 🎯 Integrated toast service with entity-specific messaging
 * 🔄 Built-in retry logic with exponential backoff
 * ♿ Comprehensive accessibility support
 * 📊 Performance tracking and optimization
 * 🛡️ Type-safe implementation with full TypeScript support
 */ __turbopack_context__.s({
    "useFormSubmission": (()=>useFormSubmission)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
// Import services following SRP
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionConfig.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionToastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionToastService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionAccessibilityService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionAccessibilityService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionRetryService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionRetryService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionPerformanceService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionPerformanceService.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
const useFormSubmission = (onSubmit, options = {})=>{
    // Merge configurations using config service
    const retryConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormSubmissionConfigService"].mergeRetryConfig(options.retry);
    const accessibilityConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormSubmissionConfigService"].mergeAccessibilityConfig(options.accessibility);
    const performanceConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormSubmissionConfigService"].mergePerformanceConfig(options.performance);
    const toastConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormSubmissionConfigService"].mergeToastConfig(options.toast);
    // Initialize services following SRP
    const accessibilityService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionAccessibilityService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormSubmissionAccessibilityService"](accessibilityConfig)).current;
    const retryService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionRetryService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormSubmissionRetryService"](retryConfig)).current;
    const performanceService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionPerformanceService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormSubmissionPerformanceService"](performanceConfig)).current;
    // Core state management
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('idle');
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [errorObject, setErrorObject] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [lastSubmittedData, setLastSubmittedData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [lastSubmitted, setLastSubmitted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [lastResult, setLastResult] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [submissionDuration, setSubmissionDuration] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Refs for cleanup and cancellation
    const abortControllerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Derived state
    const isLoading = state === 'submitting' || state === 'validating';
    const isSuccess = state === 'success';
    const isValidating = state === 'validating';
    const isRetrying = state === 'retrying';
    const retryAttempt = retryService.getCurrentAttempt();
    // Cleanup on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        return ()=>{
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
            performanceService.cleanup();
            accessibilityService.cleanup();
        };
    }, [
        performanceService,
        accessibilityService
    ]);
    // Utility Functions
    const clearError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setError(null);
        setErrorObject(null);
        accessibilityService.updateErrorMessage(null);
        if (state === 'error') {
            setState('idle');
        }
    }, [
        state,
        accessibilityService
    ]);
    const reset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setState('idle');
        setError(null);
        setErrorObject(null);
        setLastSubmittedData(null);
        setLastSubmitted(null);
        setLastResult(null);
        setSubmissionDuration(null);
        retryService.resetAttempts();
        performanceService.resetMetrics();
        accessibilityService.updateErrorMessage(null);
    }, [
        retryService,
        performanceService,
        accessibilityService
    ]);
    const cancel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }
        setState('idle');
        accessibilityService.announceStatus('Form submission cancelled');
    }, [
        accessibilityService
    ]);
    // Core submission logic using coordinated services
    const performSubmission = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (data, isRetryAttempt = false)=>{
        try {
            // Start performance tracking
            performanceService.startTiming();
            // Create abort controller for cancellation
            abortControllerRef.current = new AbortController();
            // Set appropriate state and announce
            const currentState = isRetryAttempt ? 'retrying' : 'submitting';
            setState(currentState);
            const statusMessage = accessibilityService.getStatusMessage(currentState, retryAttempt, retryConfig.maxAttempts);
            accessibilityService.announceStatus(statusMessage);
            // Call onSubmitStart callback
            if (options.onSubmitStart) {
                await options.onSubmitStart(data);
            }
            // Pre-submission validation
            if (options.preSubmitValidation) {
                setState('validating');
                accessibilityService.announceStatus('Validating form data...');
                const isValid = await options.preSubmitValidation(data);
                if (!isValid) {
                    throw new Error('Validation failed');
                }
            }
            setState(currentState);
            // Transform data if needed
            let submissionData = data;
            if (options.transformData) {
                submissionData = await options.transformData(data);
            }
            // Perform submission with timeout
            const submissionPromise = onSubmit(submissionData);
            const result = await performanceService.withTimeout(submissionPromise);
            // Transform result if needed
            let finalResult = result;
            if (options.transformResult) {
                finalResult = await options.transformResult(result);
            }
            // Post-submission validation
            if (options.postSubmitValidation) {
                const isValid = await options.postSubmitValidation(finalResult);
                if (!isValid) {
                    throw new Error('Post-submission validation failed');
                }
            }
            // Success handling
            const duration = performanceService.endTiming(true);
            setState('success');
            setLastResult(finalResult);
            setLastSubmitted(Date.now());
            setLastSubmittedData(data);
            setSubmissionDuration(duration);
            retryService.resetAttempts();
            // Show success toast using integrated toast service
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionToastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormSubmissionToastService"].showSuccessToast(toastConfig, data, finalResult);
            // Announce success
            accessibilityService.announceStatus('Form submitted successfully', 'assertive');
            accessibilityService.manageFocus('success', options.formFocus);
            // Reset form if configured
            if (options.resetOnSuccess && options.formReset) {
                options.formReset();
            }
            // Call success callback
            if (options.onSuccess) {
                await options.onSuccess(data, finalResult);
            }
            // Call completion callback
            if (options.onSubmitComplete) {
                await options.onSubmitComplete(data, true);
            }
        } catch (error_) {
            const errorObj = error_ instanceof Error ? error_ : new Error(String(error_));
            const duration = performanceService.endTiming(false);
            // Check if we should retry
            const shouldRetry = !isRetryAttempt && retryService.shouldRetry(errorObj);
            if (shouldRetry) {
                setState('retrying');
                const delay = retryService.getRetryDelay();
                retryService.incrementAttempt();
                accessibilityService.announceStatus(`Retrying in ${delay}ms... (Attempt ${retryService.getCurrentAttempt()}/${retryConfig.maxAttempts})`);
                await retryService.sleep(delay);
                return performSubmission(data, true);
            }
            // Handle error
            setState('error');
            const errorMessage = errorObj.message || toastConfig.errorMessage || 'An unexpected error occurred';
            setError(errorMessage);
            setErrorObject(errorObj);
            setSubmissionDuration(duration);
            // Show error toast using integrated toast service
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionToastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FormSubmissionToastService"].showErrorToast(toastConfig, errorObj, data);
            // Update accessibility
            accessibilityService.updateErrorMessage(errorMessage);
            accessibilityService.announceStatus(`Error: ${errorMessage}`, 'assertive');
            accessibilityService.manageFocus('error', options.formFocus);
            // Call error callback
            if (options.onError) {
                await options.onError(errorObj, data);
            }
            // Call completion callback
            if (options.onSubmitComplete) {
                await options.onSubmitComplete(data, false);
            }
        }
    }, [
        onSubmit,
        options,
        retryService,
        performanceService,
        accessibilityService,
        toastConfig,
        retryConfig.maxAttempts,
        retryAttempt
    ]);
    // Debounced submit handler using performance service
    const handleSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (data, event)=>{
        // Prevent default form submission
        if (event) {
            event.preventDefault();
        }
        // Apply debouncing using performance service
        const debouncedSubmit = performanceService.debounce(()=>performSubmission(data), performanceConfig.debounceMs);
        debouncedSubmit();
    }, [
        performSubmission,
        performanceService,
        performanceConfig.debounceMs
    ]);
    // Retry handler
    const retry = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (lastSubmittedData) {
            retryService.resetAttempts(); // Reset retry count for manual retry
            await performSubmission(lastSubmittedData);
        }
    }, [
        lastSubmittedData,
        performSubmission,
        retryService
    ]);
    // ARIA attributes using accessibility service
    const ariaAttributes = accessibilityService.generateAriaAttributes(isLoading, !!error, state);
    // Return comprehensive result object
    return {
        // Core State
        isLoading,
        state,
        error,
        errorObject,
        isSuccess,
        isValidating,
        isRetrying,
        // Submission Data
        lastSubmittedData,
        lastSubmitted,
        lastResult,
        retryAttempt,
        // Actions
        handleSubmit,
        clearError,
        reset,
        retry,
        cancel,
        // Accessibility
        ariaAttributes,
        // Performance Metrics
        submissionDuration,
        metrics: performanceService.getMetrics()
    };
};
}}),
"[project]/src/hooks/forms/useFormToast.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Form Toast Hook - Integrates generic toast service with form operations
 *
 * This hook provides a consistent interface for showing toast notifications
 * in form components, supporting both generic and entity-specific messaging.
 */ __turbopack_context__.s({
    "useEntityFormToast": (()=>useEntityFormToast),
    "useFormToast": (()=>useFormToast),
    "usePredefinedEntityToast": (()=>usePredefinedEntityToast)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/toastService.ts [app-ssr] (ecmascript)");
;
;
function useFormToast() {
    const showSuccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((title, description)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].success(title, description);
    }, []);
    const showError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((title, description)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].error(title, description);
    }, []);
    const showInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((title, description)=>{
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toastService"].info(title, description);
    }, []);
    const showFormSuccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((options)=>{
        return showSuccess(options?.successTitle || 'Success', options?.successDescription || 'Operation completed successfully');
    }, [
        showSuccess
    ]);
    const showFormError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((error, options)=>{
        const errorMessage = error instanceof Error ? error.message : error;
        return showError(options?.errorTitle || 'Error', options?.errorDescription || errorMessage || 'An unexpected error occurred');
    }, [
        showError
    ]);
    return {
        showSuccess,
        showError,
        showInfo,
        showFormSuccess,
        showFormError
    };
}
function useEntityFormToast(entityConfig, entityService) {
    const { showFormSuccess, showFormError } = useFormToast();
    // Create or use provided entity service
    const entityToastService = entityService || (entityConfig ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$toastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createEntityToastService"])(entityConfig) : null);
    const showEntityCreated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((entity)=>{
        if (entityToastService) {
            return entityToastService.entityCreated(entity);
        }
        return showFormSuccess({
            successTitle: 'Created',
            successDescription: 'Item has been created successfully'
        });
    }, [
        entityToastService,
        showFormSuccess
    ]);
    const showEntityUpdated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((entity)=>{
        if (entityToastService) {
            return entityToastService.entityUpdated(entity);
        }
        return showFormSuccess({
            successTitle: 'Updated',
            successDescription: 'Item has been updated successfully'
        });
    }, [
        entityToastService,
        showFormSuccess
    ]);
    const showEntityDeleted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((entity)=>{
        if (entityToastService) {
            return entityToastService.entityDeleted(entity);
        }
        return showFormSuccess({
            successTitle: 'Deleted',
            successDescription: 'Item has been deleted successfully'
        });
    }, [
        entityToastService,
        showFormSuccess
    ]);
    const showEntityCreationError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((error)=>{
        if (entityToastService) {
            const errorMessage = error instanceof Error ? error.message : error;
            return entityToastService.entityCreationError(errorMessage);
        }
        return showFormError(error, {
            errorTitle: 'Creation Failed'
        });
    }, [
        entityToastService,
        showFormError
    ]);
    const showEntityUpdateError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((error)=>{
        if (entityToastService) {
            const errorMessage = error instanceof Error ? error.message : error;
            return entityToastService.entityUpdateError(errorMessage);
        }
        return showFormError(error, {
            errorTitle: 'Update Failed'
        });
    }, [
        entityToastService,
        showFormError
    ]);
    const showEntityDeletionError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((error)=>{
        if (entityToastService) {
            const errorMessage = error instanceof Error ? error.message : error;
            return entityToastService.entityDeletionError(errorMessage);
        }
        return showFormError(error, {
            errorTitle: 'Deletion Failed'
        });
    }, [
        entityToastService,
        showFormError
    ]);
    return {
        showEntityCreated,
        showEntityUpdated,
        showEntityDeleted,
        showEntityCreationError,
        showEntityUpdateError,
        showEntityDeletionError,
        // Also expose generic methods
        showFormSuccess,
        showFormError
    };
}
function usePredefinedEntityToast(entityType) {
    let entityService;
    // Lazy import to avoid circular dependencies
    switch(entityType){
        case 'employee':
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            entityService = __turbopack_context__.r("[project]/src/lib/services/toastService.ts [app-ssr] (ecmascript)").employeeToast;
            break;
        case 'vehicle':
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            entityService = __turbopack_context__.r("[project]/src/lib/services/toastService.ts [app-ssr] (ecmascript)").vehicleToast;
            break;
        case 'task':
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            entityService = __turbopack_context__.r("[project]/src/lib/services/toastService.ts [app-ssr] (ecmascript)").taskToast;
            break;
        case 'delegation':
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            entityService = __turbopack_context__.r("[project]/src/lib/services/toastService.ts [app-ssr] (ecmascript)").delegationToast;
            break;
        default:
            throw new Error(`Unknown entity type: ${entityType}`);
    }
    return useEntityFormToast(undefined, entityService);
}
}}),
"[project]/src/hooks/forms/useFormValidation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Centralized Form Validation Hook
 * @description Provides consistent form validation patterns with Zod schema integration
 */ __turbopack_context__.s({
    "useConditionalValidation": (()=>useConditionalValidation),
    "useFormValidation": (()=>useFormValidation),
    "useSchemaValidation": (()=>useSchemaValidation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$ZodError$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/dist/esm/v3/ZodError.js [app-ssr] (ecmascript)");
;
;
;
;
const useFormValidation = (options)=>{
    const { schema, defaultValues, mode = 'onChange', reValidateMode = 'onChange', showErrorsImmediately = false, customErrorMessages = {} } = options;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["zodResolver"])(schema),
        defaultValues: defaultValues,
        mode,
        reValidateMode,
        criteriaMode: 'all'
    });
    const { formState: { errors, isValid, isDirty }, trigger, clearErrors: clearAllErrors, setError, reset } = form;
    // Validate specific field
    const validateField = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (fieldName)=>{
        const result = await trigger(fieldName);
        return result;
    }, [
        trigger
    ]);
    // Validate entire form
    const validateForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        const result = await trigger();
        return result;
    }, [
        trigger
    ]);
    // Clear all errors
    const clearErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        clearAllErrors();
    }, [
        clearAllErrors
    ]);
    // Clear specific field error
    const clearFieldError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((fieldName)=>{
        clearAllErrors(fieldName);
    }, [
        clearAllErrors
    ]);
    // Get formatted error message for field
    const getFieldError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((fieldName)=>{
        const fieldError = errors[fieldName];
        if (!fieldError) return undefined;
        // Check for custom error message first
        const customMessage = customErrorMessages[fieldName];
        if (customMessage) return customMessage;
        // Return the error message from validation
        if (typeof fieldError.message === 'string') {
            return fieldError.message;
        }
        // Fallback for complex error structures
        return 'This field is invalid';
    }, [
        errors,
        customErrorMessages
    ]);
    // Check if specific field has error
    const hasFieldError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((fieldName)=>{
        return !!errors[fieldName];
    }, [
        errors
    ]);
    // Reset form to default values
    const resetForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        reset(defaultValues);
    }, [
        reset,
        defaultValues
    ]);
    const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            form,
            isValid,
            isDirty,
            errors,
            validateField,
            validateForm,
            clearErrors,
            clearFieldError,
            getFieldError,
            hasFieldError,
            resetForm
        }), [
        form,
        isValid,
        isDirty,
        errors,
        validateField,
        validateForm,
        clearErrors,
        clearFieldError,
        getFieldError,
        hasFieldError,
        resetForm
    ]);
    return result;
};
const useSchemaValidation = (schema)=>{
    const validateData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((data)=>{
        try {
            const validData = schema.parse(data);
            return {
                isValid: true,
                validData
            };
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$ZodError$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ZodError"]) {
                return {
                    isValid: false,
                    errors: error
                };
            }
            throw error;
        }
    }, [
        schema
    ]);
    const validateAsync = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (data)=>{
        try {
            const validData = await schema.parseAsync(data);
            return {
                isValid: true,
                validData
            };
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$ZodError$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ZodError"]) {
                return {
                    isValid: false,
                    errors: error
                };
            }
            throw error;
        }
    }, [
        schema
    ]);
    const getFieldErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((zodError)=>{
        const fieldErrors = {};
        zodError.errors.forEach((error)=>{
            const fieldPath = error.path.join('.');
            fieldErrors[fieldPath] = error.message;
        });
        return fieldErrors;
    }, []);
    return {
        validateData,
        validateAsync,
        getFieldErrors
    };
};
const useConditionalValidation = (baseSchema, conditions)=>{
    const getDynamicSchema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((data)=>{
        // Find the first matching condition
        const matchingCondition = conditions.find(({ condition })=>condition(data));
        // Return the conditional schema or base schema
        return matchingCondition ? matchingCondition.schema : baseSchema;
    }, [
        baseSchema,
        conditions
    ]);
    const validateWithConditions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((data)=>{
        const dynamicSchema = getDynamicSchema(data);
        try {
            const validData = dynamicSchema.parse(data);
            return {
                isValid: true,
                validData
            };
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$dist$2f$esm$2f$v3$2f$ZodError$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ZodError"]) {
                return {
                    isValid: false,
                    errors: error
                };
            }
            throw error;
        }
    }, [
        getDynamicSchema
    ]);
    return {
        getDynamicSchema,
        validateWithConditions
    };
};
}}),
"[project]/src/hooks/forms/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Form Hooks Index - SOLID Principles Implementation
 * @description Centralized exports for all form-related hooks and services
 * @version 3.0.0
 * <AUTHOR> Development Team
 */ // Main form submission hook (refactored for SOLID principles)
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$useFormSubmission$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/useFormSubmission.ts [app-ssr] (ecmascript)");
// Specialized validation hooks
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$useLoginValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/useLoginValidation.ts [app-ssr] (ecmascript)");
// Services (following SRP)
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionConfig.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionToastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionToastService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionAccessibilityService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionAccessibilityService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionRetryService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionRetryService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionPerformanceService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionPerformanceService.ts [app-ssr] (ecmascript)");
// Other existing form hooks
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$useFormToast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/useFormToast.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$useFormValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/useFormValidation.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
}}),
"[project]/src/hooks/forms/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$useFormSubmission$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/useFormSubmission.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$useLoginValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/useLoginValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionConfig$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionConfig.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionToastService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionToastService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionAccessibilityService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionAccessibilityService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionRetryService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionRetryService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$services$2f$FormSubmissionPerformanceService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/services/FormSubmissionPerformanceService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$useFormToast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/useFormToast.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$useFormValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/forms/useFormValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/hooks/forms/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/hooks/ui/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file UI Hooks Index
 * @description Centralized exports for all UI-related hooks
 */ // Theme management
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useTheme$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useTheme.ts [app-ssr] (ecmascript)");
// Sidebar management
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useSidebar$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useSidebar.ts [app-ssr] (ecmascript)");
// Modal management
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useModal$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useModal.ts [app-ssr] (ecmascript)");
// UI preferences
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useUiPreferences$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useUiPreferences.ts [app-ssr] (ecmascript)");
// Notifications
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useNotifications.ts [app-ssr] (ecmascript)");
;
;
;
;
;
}}),
"[project]/src/hooks/ui/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useTheme$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useTheme.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useSidebar$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useSidebar.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useModal$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useModal.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useUiPreferences$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useUiPreferences.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useNotifications.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/hooks/ui/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/hooks/security.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Security Hooks Exports - DRY Principle
 * @module hooks/security
 *
 * Centralized exports for all security-related hooks following DRY principles.
 * This provides a single import point for security hooks across the application.
 */ __turbopack_context__.s({
    "SecurityHooks": (()=>SecurityHooks)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$usePermissions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/usePermissions.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useCSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useCSRFProtection.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSessionSecurity$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSessionSecurity.ts [app-ssr] (ecmascript)");
// Import hooks for re-export object
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useTokenManagement$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useTokenManagement.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
const SecurityHooks = {
    useCSRFProtection: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useCSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCSRFProtection"],
    usePermissions: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$usePermissions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePermissions"],
    useSecureApi: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecureApi"],
    useSessionSecurity: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSessionSecurity$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSessionSecurity"],
    useTokenManagement: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useTokenManagement$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTokenManagement"]
};
}}),
"[project]/src/hooks/security.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useCSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useCSRFProtection.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSessionSecurity$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSessionSecurity.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useTokenManagement$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useTokenManagement.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$security$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/hooks/security.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/hooks/domain/useDelegationInfo.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Custom hook for delegation info extraction
 * @module hooks/useDelegationInfo
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "useDelegationInfo": (()=>useDelegationInfo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
const useDelegationInfo = (delegation)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        // Extract escort information
        const escortInfo = delegation.escorts && delegation.escorts.length > 0 && delegation.escorts[0]?.employee ? delegation.escorts[0].employee : null;
        // Extract driver information
        const driverInfo = delegation.drivers && delegation.drivers.length > 0 && delegation.drivers[0]?.employee ? delegation.drivers[0].employee : null;
        // Extract vehicle information
        const vehicleInfo = delegation.vehicles && delegation.vehicles.length > 0 && delegation.vehicles[0]?.vehicle ? delegation.vehicles[0].vehicle : null;
        // Check if flight details exist
        const hasFlightDetails = Boolean(delegation.arrivalFlight || delegation.departureFlight);
        // Check if escort assignment is needed
        const needsEscortAssignment = !escortInfo && delegation.status !== 'Completed' && delegation.status !== 'Cancelled';
        // Check if delegation is currently active
        const isActive = delegation.status === 'In_Progress';
        return {
            escortInfo,
            driverInfo,
            vehicleInfo,
            hasFlightDetails,
            needsEscortAssignment,
            isActive
        };
    }, [
        delegation
    ]);
};
const __TURBOPACK__default__export__ = useDelegationInfo;
}}),
"[project]/src/hooks/domain/useDashboardStore.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Generic dashboard store hook
 * @module hooks/useDashboardStore
 */ __turbopack_context__.s({
    "createDashboardStore": (()=>createDashboardStore),
    "default": (()=>__TURBOPACK__default__export__),
    "useDashboardStore": (()=>useDashboardStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
;
function createDashboardStore(entityType) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["devtools"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
            // Initial state
            activeTab: 'all',
            layout: {
                viewMode: 'cards',
                gridColumns: 3,
                compactMode: false,
                showFilters: true,
                showSettings: false
            },
            monitoring: {
                enabled: false,
                refreshInterval: 30000,
                autoRefresh: true,
                pausedDataTypes: new Set()
            },
            filters: {},
            sortBy: 'createdAt',
            sortDirection: 'desc',
            selectedItems: new Set(),
            searchTerm: '',
            // Actions
            setActiveTab: (tab)=>set({
                    activeTab: tab
                }, false, 'setActiveTab'),
            setViewMode: (mode)=>set((state)=>({
                        layout: {
                            ...state.layout,
                            viewMode: mode
                        }
                    }), false, 'setViewMode'),
            setGridColumns: (columns)=>set((state)=>({
                        layout: {
                            ...state.layout,
                            gridColumns: columns
                        }
                    }), false, 'setGridColumns'),
            toggleCompactMode: ()=>set((state)=>({
                        layout: {
                            ...state.layout,
                            compactMode: !state.layout.compactMode
                        }
                    }), false, 'toggleCompactMode'),
            toggleFilters: ()=>set((state)=>({
                        layout: {
                            ...state.layout,
                            showFilters: !state.layout.showFilters
                        }
                    }), false, 'toggleFilters'),
            toggleSettings: ()=>set((state)=>({
                        layout: {
                            ...state.layout,
                            showSettings: !state.layout.showSettings
                        }
                    }), false, 'toggleSettings'),
            updateFilter: (filterId, value)=>set((state)=>({
                        filters: {
                            ...state.filters,
                            [filterId]: value
                        }
                    }), false, 'updateFilter'),
            clearFilters: ()=>set({
                    filters: {}
                }, false, 'clearFilters'),
            setSorting: (field, direction)=>set({
                    sortBy: field,
                    sortDirection: direction
                }, false, 'setSorting'),
            setSearchTerm: (term)=>set({
                    searchTerm: term
                }, false, 'setSearchTerm'),
            toggleItemSelection: (id)=>set((state)=>{
                    const newSelection = new Set(state.selectedItems);
                    if (newSelection.has(id)) {
                        newSelection.delete(id);
                    } else {
                        newSelection.add(id);
                    }
                    return {
                        selectedItems: newSelection
                    };
                }, false, 'toggleItemSelection'),
            clearSelection: ()=>set({
                    selectedItems: new Set()
                }, false, 'clearSelection'),
            selectAll: (ids)=>set({
                    selectedItems: new Set(ids)
                }, false, 'selectAll'),
            // Monitoring actions
            setMonitoringEnabled: (enabled)=>set((state)=>({
                        monitoring: {
                            ...state.monitoring,
                            enabled
                        }
                    }), false, 'setMonitoringEnabled'),
            setRefreshInterval: (interval)=>set((state)=>({
                        monitoring: {
                            ...state.monitoring,
                            refreshInterval: interval
                        }
                    }), false, 'setRefreshInterval'),
            toggleAutoRefresh: ()=>set((state)=>({
                        monitoring: {
                            ...state.monitoring,
                            autoRefresh: !state.monitoring.autoRefresh
                        }
                    }), false, 'toggleAutoRefresh'),
            pauseDataType: (dataType)=>set((state)=>({
                        monitoring: {
                            ...state.monitoring,
                            pausedDataTypes: new Set([
                                ...state.monitoring.pausedDataTypes,
                                dataType
                            ])
                        }
                    }), false, 'pauseDataType'),
            resumeDataType: (dataType)=>set((state)=>{
                    const newPausedTypes = new Set(state.monitoring.pausedDataTypes);
                    newPausedTypes.delete(dataType);
                    return {
                        monitoring: {
                            ...state.monitoring,
                            pausedDataTypes: newPausedTypes
                        }
                    };
                }, false, 'resumeDataType'),
            resetSettings: ()=>set({
                    layout: {
                        viewMode: 'cards',
                        gridColumns: 3,
                        compactMode: false,
                        showFilters: true,
                        showSettings: false
                    },
                    monitoring: {
                        enabled: false,
                        refreshInterval: 30000,
                        autoRefresh: true,
                        pausedDataTypes: new Set()
                    },
                    filters: {},
                    sortBy: 'createdAt',
                    sortDirection: 'desc',
                    selectedItems: new Set(),
                    searchTerm: ''
                }, false, 'resetSettings'),
            // Computed selectors
            getFilteredData: (data, config)=>{
                const state = get();
                let filtered = [
                    ...data
                ];
                // Apply search filter
                if (state.searchTerm) {
                    const searchLower = state.searchTerm.toLowerCase();
                    filtered = filtered.filter((item)=>Object.values(item).some((value)=>String(value).toLowerCase().includes(searchLower)));
                }
                // Apply other filters
                Object.entries(state.filters).forEach(([filterId, value])=>{
                    if (value !== undefined && value !== null && value !== '') {
                        filtered = filtered.filter((item)=>{
                            const filterConfig = config.filters?.find((f)=>f.id === filterId);
                            if (!filterConfig) return true;
                            switch(filterConfig.type){
                                case 'select':
                                    return item[filterId] === value;
                                case 'multiselect':
                                    return Array.isArray(value) ? value.includes(item[filterId]) : true;
                                case 'toggle':
                                    return value ? item[filterId] : true;
                                default:
                                    return true;
                            }
                        });
                    }
                });
                // Apply sorting
                filtered.sort((a, b)=>{
                    const aValue = a[state.sortBy];
                    const bValue = b[state.sortBy];
                    const direction = state.sortDirection === 'asc' ? 1 : -1;
                    if (aValue < bValue) return -1 * direction;
                    if (aValue > bValue) return 1 * direction;
                    return 0;
                });
                return filtered;
            },
            getSelectedCount: ()=>get().selectedItems.size,
            hasActiveFilters: ()=>{
                const state = get();
                return state.searchTerm.length > 0 || Object.values(state.filters).some((value)=>value !== undefined && value !== null && value !== '');
            }
        }), {
        name: `workhub-dashboard-${entityType}`,
        partialize: (state)=>({
                layout: state.layout,
                monitoring: state.monitoring,
                filters: state.filters,
                sortBy: state.sortBy,
                sortDirection: state.sortDirection
            })
    }), {
        name: `dashboard-${entityType}`
    }));
}
/**
 * Store registry to ensure single instance per entity type
 */ const storeRegistry = new Map();
function useDashboardStore(entityType) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!storeRegistry.has(entityType)) {
            storeRegistry.set(entityType, createDashboardStore(entityType));
        }
        return storeRegistry.get(entityType);
    }, [
        entityType
    ]);
}
const __TURBOPACK__default__export__ = useDashboardStore;
}}),
"[project]/src/hooks/domain/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Domain Hooks Index
 * @description Centralized exports for all domain-specific hooks
 */ // Delegation domain hooks
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$domain$2f$useDelegationInfo$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/domain/useDelegationInfo.ts [app-ssr] (ecmascript)");
// Dashboard domain hooks (moved from lib/hooks)
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$domain$2f$useDashboardStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/domain/useDashboardStore.ts [app-ssr] (ecmascript)"); // Future domain hooks can be added here:
 // export { useTaskInfo } from './useTaskInfo';
 // export { useVehicleInfo } from './useVehicleInfo';
 // export { useEmployeeInfo } from './useEmployeeInfo';
;
;
}}),
"[project]/src/hooks/domain/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$domain$2f$useDelegationInfo$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/domain/useDelegationInfo.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$domain$2f$useDashboardStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/domain/useDashboardStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$domain$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/hooks/domain/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/hooks/utils/use-mobile.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useIsMobile": (()=>useIsMobile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
const MOBILE_BREAKPOINT = 768;
function useIsMobile() {
    const [isMobile, setIsMobile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const mql = globalThis.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
        const onChange = ()=>{
            setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
        };
        mql.addEventListener('change', onChange);
        setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);
        return ()=>mql.removeEventListener('change', onChange);
    }, []);
    return !!isMobile;
}
}}),
"[project]/src/hooks/utils/useRequestDeduplication.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "globalRequestDeduplicator": (()=>globalRequestDeduplicator),
    "useRequestDeduplication": (()=>useRequestDeduplication)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
/**
 * Global request deduplication instance
 * Use this for API calls that should be deduplicated across the entire app
 */ class GlobalRequestDeduplicator {
    pendingRequests = new Map();
    clear() {
        this.pendingRequests.clear();
    }
    async deduplicate(key, requestFn) {
        // If there's already a pending request for this key, return it
        if (this.pendingRequests.has(key)) {
            console.log(`🔄 Global Request DEDUPLICATED for ${key}`);
            return this.pendingRequests.get(key);
        }
        // Create new request and store it
        const request = requestFn().finally(()=>{
            // Remove from pending requests when completed
            this.pendingRequests.delete(key);
        });
        this.pendingRequests.set(key, request);
        console.log(`🔄 Global Request STARTED for ${key}`);
        return request;
    }
    getPendingCount() {
        return this.pendingRequests.size;
    }
    getPendingKeys() {
        return [
            ...this.pendingRequests.keys()
        ];
    }
}
function useRequestDeduplication() {
    const pendingRequests = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(new Map());
    const deduplicateRequest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((key, requestFn)=>{
        // If there's already a pending request for this key, return it
        if (pendingRequests.current.has(key)) {
            console.log(`🔄 Request DEDUPLICATED for ${key}`);
            return pendingRequests.current.get(key);
        }
        // Create new request and store it
        const request = requestFn().finally(()=>{
            // Remove from pending requests when completed
            pendingRequests.current.delete(key);
        });
        pendingRequests.current.set(key, request);
        console.log(`🔄 Request STARTED for ${key}`);
        return request;
    }, []);
    const clearPendingRequests = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        pendingRequests.current.clear();
    }, []);
    const getPendingRequestsCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return pendingRequests.current.size;
    }, []);
    return {
        clearPendingRequests,
        deduplicateRequest,
        getPendingRequestsCount
    };
}
const globalRequestDeduplicator = new GlobalRequestDeduplicator();
}}),
"[project]/src/hooks/utils/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Utility Hooks Index
 * @description Centralized exports for all utility hooks
 */ // Mobile detection
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$use$2d$mobile$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/utils/use-mobile.tsx [app-ssr] (ecmascript)");
// Toast notifications
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/utils/use-toast.ts [app-ssr] (ecmascript)");
// Request deduplication
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$useRequestDeduplication$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/utils/useRequestDeduplication.ts [app-ssr] (ecmascript)");
;
;
;
}}),
"[project]/src/hooks/utils/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$use$2d$mobile$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/utils/use-mobile.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$use$2d$toast$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/utils/use-toast.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$useRequestDeduplication$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/utils/useRequestDeduplication.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/hooks/utils/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/hooks/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Hooks Index
 * @description Centralized exports for all custom hooks following Phase 4 standardization
 */ // ===== PHASE 4 STANDARDIZED HOOKS =====
// API Hooks - Standardized patterns for API interactions
__turbopack_context__.s({
    "useWorkHubCore": (()=>useWorkHubCore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
// Form Hooks - Standardized patterns for form handling
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/forms/index.ts [app-ssr] (ecmascript) <module evaluation>");
// UI Hooks - Standardized patterns for UI state management
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/ui/index.ts [app-ssr] (ecmascript) <module evaluation>");
// ===== CATEGORIZED HOOKS =====
// Authentication Hooks - Standardized patterns for authentication
// Auth hooks removed - use AuthContext directly
// export * from './auth';
// Security Hooks - Standardized patterns for security
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$security$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/security.ts [app-ssr] (ecmascript) <module evaluation>");
// Domain Hooks - Domain-specific business logic
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$domain$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/domain/index.ts [app-ssr] (ecmascript) <module evaluation>");
// Utility Hooks - General utility functions
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/utils/index.ts [app-ssr] (ecmascript) <module evaluation>");
// ===== LEGACY HOOKS (Maintained for backward compatibility) =====
// Store hooks (re-exported for convenience)
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/appStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$reliabilityStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/reliabilityStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/uiStore.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
const useWorkHubCore = ()=>{
    // Import from new standardized locations
    const { useTheme } = __turbopack_context__.r("[project]/src/hooks/ui/useTheme.ts [app-ssr] (ecmascript)");
    const { useNotifications } = __turbopack_context__.r("[project]/src/hooks/ui/useNotifications.ts [app-ssr] (ecmascript)");
    const { useUiPreferences } = __turbopack_context__.r("[project]/src/hooks/ui/useUiPreferences.ts [app-ssr] (ecmascript)");
    const { useSidebar } = __turbopack_context__.r("[project]/src/hooks/ui/useSidebar.ts [app-ssr] (ecmascript)");
    const { useModal } = __turbopack_context__.r("[project]/src/hooks/ui/useModal.ts [app-ssr] (ecmascript)");
    const theme = useTheme();
    const notifications = useNotifications();
    const uiPreferences = useUiPreferences();
    const sidebar = useSidebar();
    const modal = useModal();
    return {
        modal,
        notifications,
        sidebar,
        theme,
        uiPreferences
    };
};
}}),
"[project]/src/hooks/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$forms$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/forms/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/ui/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$security$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/security.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$domain$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/domain/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/utils/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/appStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$reliabilityStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/reliabilityStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/uiStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/hooks/index.ts [app-ssr] (ecmascript) <locals>");
}}),

};

//# sourceMappingURL=src_hooks_51bfc985._.js.map