"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9212],{3561:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},15452:(e,t,r)=>{r.d(t,{G$:()=>z,Hs:()=>w,UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>$,bm:()=>el,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(12115),o=r(85185),l=r(6101),i=r(46081),a=r(61285),s=r(5845),u=r(19178),c=r(25519),d=r(34378),p=r(28905),f=r(63655),g=r(92293),h=r(31114),v=r(38168),x=r(99708),y=r(95155),m="Dialog",[b,w]=(0,i.A)(m),[C,R]=b(m),E=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:l,onOpenChange:i,modal:u=!0}=e,c=n.useRef(null),d=n.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=l&&l,onChange:i,caller:m});return(0,y.jsx)(C,{scope:t,triggerRef:c,contentRef:d,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:u,children:r})};E.displayName=m;var T="DialogTrigger",j=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=R(T,r),a=(0,l.s)(t,i.triggerRef);return(0,y.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":Y(i.open),...n,ref:a,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});j.displayName=T;var k="DialogPortal",[D,_]=b(k,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:l}=e,i=R(k,t);return(0,y.jsx)(D,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,y.jsx)(p.C,{present:r||i.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:l,children:e})}))})};I.displayName=k;var L="DialogOverlay",O=n.forwardRef((e,t)=>{let r=_(L,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,l=R(L,e.__scopeDialog);return l.modal?(0,y.jsx)(p.C,{present:n||l.open,children:(0,y.jsx)(N,{...o,ref:t})}):null});O.displayName=L;var M=(0,x.TL)("DialogOverlay.RemoveScroll"),N=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(L,r);return(0,y.jsx)(h.A,{as:M,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(f.sG.div,{"data-state":Y(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",A=n.forwardRef((e,t)=>{let r=_(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,l=R(P,e.__scopeDialog);return(0,y.jsx)(p.C,{present:n||l.open,children:l.modal?(0,y.jsx)(F,{...o,ref:t}):(0,y.jsx)(B,{...o,ref:t})})});A.displayName=P;var F=n.forwardRef((e,t)=>{let r=R(P,e.__scopeDialog),i=n.useRef(null),a=(0,l.s)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(q,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=n.forwardRef((e,t)=>{let r=R(P,e.__scopeDialog),o=n.useRef(!1),l=n.useRef(!1);return(0,y.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,i;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(i=r.triggerRef.current)||i.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var n,i;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(l.current=!0));let a=t.target;(null==(i=r.triggerRef.current)?void 0:i.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),q=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...s}=e,d=R(P,r),p=n.useRef(null),f=(0,l.s)(t,p);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,y.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":Y(d.open),...s,ref:f,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(V,{titleId:d.titleId}),(0,y.jsx)(J,{contentRef:p,descriptionId:d.descriptionId})]})]})}),G="DialogTitle",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(G,r);return(0,y.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});H.displayName=G;var S="DialogDescription",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(S,r);return(0,y.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});W.displayName=S;var U="DialogClose",X=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=R(U,r);return(0,y.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})});function Y(e){return e?"open":"closed"}X.displayName=U;var Z="DialogTitleWarning",[z,K]=(0,i.q)(Z,{contentName:P,titleName:G,docsSlug:"dialog"}),V=e=>{let{titleId:t}=e,r=K(Z),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},J=e=>{let{contentRef:t,descriptionId:r}=e,o=K("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(l))},[l,t,r]),null},$=E,Q=j,ee=I,et=O,er=A,en=H,eo=W,el=X},25318:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},67554:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},89613:(e,t,r)=>{r.d(t,{Kq:()=>G,UC:()=>W,bL:()=>H,l9:()=>S});var n=r(12115),o=r(85185),l=r(6101),i=r(46081),a=r(19178),s=r(61285),u=r(35152),c=(r(34378),r(28905)),d=r(63655),p=r(99708),f=r(5845),g=r(2564),h=r(95155),[v,x]=(0,i.A)("Tooltip",[u.Bk]),y=(0,u.Bk)(),m="TooltipProvider",b="tooltip.open",[w,C]=v(m),R=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:l=!1,children:i}=e,a=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,h.jsx)(w,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:l,children:i})};R.displayName=m;var E="Tooltip",[T,j]=v(E),k=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:l,onOpenChange:i,disableHoverableContent:a,delayDuration:c}=e,d=C(E,e.__scopeTooltip),p=y(t),[g,v]=n.useState(null),x=(0,s.B)(),m=n.useRef(0),w=null!=a?a:d.disableHoverableContent,R=null!=c?c:d.delayDuration,j=n.useRef(!1),[k,D]=(0,f.i)({prop:o,defaultProp:null!=l&&l,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(b))):d.onClose(),null==i||i(e)},caller:E}),_=n.useMemo(()=>k?j.current?"delayed-open":"instant-open":"closed",[k]),I=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,j.current=!1,D(!0)},[D]),L=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,D(!1)},[D]),O=n.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{j.current=!0,D(!0),m.current=0},R)},[R,D]);return n.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,h.jsx)(u.bL,{...p,children:(0,h.jsx)(T,{scope:t,contentId:x,open:k,stateAttribute:_,trigger:g,onTriggerChange:v,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?O():I()},[d.isOpenDelayedRef,O,I]),onTriggerLeave:n.useCallback(()=>{w?L():(window.clearTimeout(m.current),m.current=0)},[L,w]),onOpen:I,onClose:L,disableHoverableContent:w,children:r})})};k.displayName=E;var D="TooltipTrigger",_=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...i}=e,a=j(D,r),s=C(D,r),c=y(r),p=n.useRef(null),f=(0,l.s)(t,p,a.onTriggerChange),g=n.useRef(!1),v=n.useRef(!1),x=n.useCallback(()=>g.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",x),[x]),(0,h.jsx)(u.Mz,{asChild:!0,...c,children:(0,h.jsx)(d.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...i,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(v.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),v.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),v.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),g.current=!0,document.addEventListener("pointerup",x,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{g.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});_.displayName=D;var[I,L]=v("TooltipPortal",{forceMount:void 0}),O="TooltipContent",M=n.forwardRef((e,t)=>{let r=L(O,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...l}=e,i=j(O,e.__scopeTooltip);return(0,h.jsx)(c.C,{present:n||i.open,children:i.disableHoverableContent?(0,h.jsx)(B,{side:o,...l,ref:t}):(0,h.jsx)(N,{side:o,...l,ref:t})})}),N=n.forwardRef((e,t)=>{let r=j(O,e.__scopeTooltip),o=C(O,e.__scopeTooltip),i=n.useRef(null),a=(0,l.s)(t,i),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,p=i.current,{onPointerInTransitChange:f}=o,g=n.useCallback(()=>{u(null),f(!1)},[f]),v=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),l=Math.abs(t.left-e.x);switch(Math.min(r,n,o,l)){case l:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>g(),[g]),n.useEffect(()=>{if(c&&p){let e=e=>v(e,p),t=e=>v(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,v,g]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],s=i.x,u=i.y,c=a.x,d=a.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}(r,s);n?g():o&&(g(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,d,g]),(0,h.jsx)(B,{...e,ref:a})}),[P,A]=v(E,{isInside:!1}),F=(0,p.Dc)("TooltipContent"),B=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":l,onEscapeKeyDown:i,onPointerDownOutside:s,...c}=e,d=j(O,r),p=y(r),{onClose:f}=d;return n.useEffect(()=>(document.addEventListener(b,f),()=>document.removeEventListener(b,f)),[f]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,h.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,h.jsxs)(u.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,h.jsx)(F,{children:o}),(0,h.jsx)(P,{scope:r,isInside:!0,children:(0,h.jsx)(g.bL,{id:d.contentId,role:"tooltip",children:l||o})})]})})});M.displayName=O;var q="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=y(r);return A(q,r).isInside?null:(0,h.jsx)(u.i3,{...o,...n,ref:t})}).displayName=q;var G=R,H=k,S=_,W=M}}]);