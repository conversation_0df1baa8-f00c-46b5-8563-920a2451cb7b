(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7530],{25335:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>ew});var t=a(95155),r=a(57082),l=a(34301),n=a(18271),i=a(6874),d=a.n(i),c=a(12115),o=a(88240),m=a(37648),x=a(50286),h=a(8376);a(52639);var u=a(97697),g=a(17841);r.A,m.A,x.A,h.A;var v=a(38342);let j={entityType:"delegation",title:"Delegation Dashboard",description:"Track and manage all your events, trips, and delegate information.",viewModes:["cards","table","list","calendar"],defaultViewMode:"cards",enableBulkActions:!0,enableExport:!0,refreshInterval:3e4},p=e=>{let{className:s=""}=e,{layout:a,monitoring:r,setViewMode:l,setGridColumns:n,toggleCompactMode:i,setMonitoringEnabled:d,setRefreshInterval:c,toggleAutoRefresh:o,resetSettings:m}=(0,u.fX)("delegation")();return(0,t.jsx)(v.s,{config:j,entityType:"delegation",layout:a,monitoring:r,setViewMode:l,setGridColumns:n,toggleCompactMode:i,setMonitoringEnabled:d,setRefreshInterval:c,toggleAutoRefresh:o,resetSettings:m,className:s})};var N=a(27300),f=a(25318),b=a(98328),y=a(83662),w=a(28328),k=a(75074),A=a(9572),C=a(30285),D=a(62523),S=a(85057),z=a(26126),M=a(47262),R=a(85511),T=a(22346),E=a(14636),F=a(38382),P=a(54036),I=a(41784);let L=[{value:"Planned",label:"Planned",icon:N.A,color:"border-blue-200 text-blue-700"},{value:"Confirmed",label:"Confirmed",icon:h.A,color:"border-green-200 text-green-700"},{value:"In_Progress",label:"In Progress",icon:N.A,color:"border-yellow-200 text-yellow-700"},{value:"Completed",label:"Completed",icon:h.A,color:"border-emerald-200 text-emerald-700"},{value:"Cancelled",label:"Cancelled",icon:f.A,color:"border-red-200 text-red-700"},{value:"No_details",label:"No Details",icon:N.A,color:"border-gray-200 text-gray-700"}],$=e=>{let{onFiltersChange:s,className:a,initialFilters:r={},employeesList:l=[],vehiclesList:n=[],locationsList:i=[]}=e,[d,o]=(0,c.useState)(!1),[m,u]=(0,c.useState)({search:"",status:[],dateRange:{},location:[],drivers:[],escorts:[],vehicles:[],...r}),g=e=>{let a={...m,...e};u(a),null==s||s(a)},v=()=>{let e={search:"",status:[],dateRange:{},location:[],drivers:[],escorts:[],vehicles:[]};u(e),null==s||s(e)},j=e=>{g({status:m.status.includes(e)?m.status.filter(s=>s!==e):[...m.status,e]})},p=e=>{g({location:m.location.includes(e)?m.location.filter(s=>s!==e):[...m.location,e]})},N=e=>{g({drivers:m.drivers.includes(e)?m.drivers.filter(s=>s!==e):[...m.drivers,e]})},$=e=>{g({vehicles:m.vehicles.includes(e)?m.vehicles.filter(s=>s!==e):[...m.vehicles,e]})},_=e=>{var s,a;g({dateRange:{from:null!=(s=null==e?void 0:e.from)?s:void 0,to:null!=(a=null==e?void 0:e.to)?a:void 0}})},O=+!!m.search+m.status.length+m.location.length+m.drivers.length+m.escorts.length+m.vehicles.length+(m.dateRange.from||m.dateRange.to?1:0);return(0,t.jsxs)("div",{className:(0,P.cn)("flex flex-col gap-4",a),children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(k.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,t.jsx)(D.p,{placeholder:"Search delegations (Event, Location, Delegate, Status...)",value:m.search,onChange:e=>g({search:e.target.value}),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,t.jsxs)("div",{className:"hidden md:flex items-center gap-2",children:[(0,t.jsx)(()=>(0,t.jsxs)(E.AM,{children:[(0,t.jsx)(E.Wv,{asChild:!0,children:(0,t.jsxs)(C.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(h.A,{className:"size-4"}),"Status",m.status.length>0&&(0,t.jsx)(z.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:m.status.length})]})}),(0,t.jsx)(E.hl,{className:"w-56 p-3",align:"start",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Delegation Status"}),(0,t.jsx)(C.$,{variant:"ghost",size:"sm",onClick:()=>g({status:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)(T.w,{}),(0,t.jsx)("div",{className:"space-y-2",children:L.map(e=>{let s=e.icon;return(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(M.S,{id:"status-".concat(e.value),checked:m.status.includes(e.value),onCheckedChange:()=>j(e.value)}),(0,t.jsxs)(S.J,{htmlFor:"status-".concat(e.value),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,t.jsx)(s,{className:"size-3"}),(0,t.jsx)(z.E,{variant:"outline",className:(0,P.cn)("text-xs border",e.color),children:e.label})]})]},e.value)})})]})})]}),{}),(0,t.jsx)(()=>(0,t.jsxs)(E.AM,{children:[(0,t.jsx)(E.Wv,{asChild:!0,children:(0,t.jsxs)(C.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(b.A,{className:"size-4"}),"Date Range",(m.dateRange.from||m.dateRange.to)&&(0,t.jsx)(z.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:"1"})]})}),(0,t.jsx)(E.hl,{className:"w-auto p-0",align:"start",children:(0,t.jsxs)("div",{className:"p-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Delegation Date Range"}),(0,t.jsx)(C.$,{variant:"ghost",size:"sm",onClick:()=>g({dateRange:{}}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)(R.V,{mode:"range",selected:{from:m.dateRange.from,to:m.dateRange.to},onSelect:_,numberOfMonths:2,className:"rounded-md border-0"}),(0,t.jsx)("div",{className:"mt-3 text-xs text-muted-foreground text-center",children:m.dateRange.from&&!m.dateRange.to?"Select end date to complete range":"Click start date, then end date"})]})})]}),{}),i.length>0&&(0,t.jsx)(()=>(0,t.jsxs)(E.AM,{children:[(0,t.jsx)(E.Wv,{asChild:!0,children:(0,t.jsxs)(C.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(y.A,{className:"size-4"}),"Location",m.location.length>0&&(0,t.jsx)(z.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:m.location.length})]})}),(0,t.jsx)(E.hl,{className:"w-64 p-3",align:"start",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Delegation Location"}),(0,t.jsx)(C.$,{variant:"ghost",size:"sm",onClick:()=>g({location:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)(T.w,{}),(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:i.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(M.S,{id:"location-".concat(e),checked:m.location.includes(e),onCheckedChange:()=>p(e)}),(0,t.jsxs)(S.J,{htmlFor:"location-".concat(e),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,t.jsx)(y.A,{className:"size-3"}),(0,t.jsx)("span",{children:e})]})]},e))})]})})]}),{}),l.some(e=>"driver"===e.role)&&(0,t.jsx)(()=>(0,t.jsxs)(E.AM,{children:[(0,t.jsx)(E.Wv,{asChild:!0,children:(0,t.jsxs)(C.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(x.A,{className:"size-4"}),"Drivers",m.drivers.length>0&&(0,t.jsx)(z.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:m.drivers.length})]})}),(0,t.jsx)(E.hl,{className:"w-64 p-3",align:"start",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Assigned Drivers"}),(0,t.jsx)(C.$,{variant:"ghost",size:"sm",onClick:()=>g({drivers:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)(T.w,{}),(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:l.filter(e=>"driver"===e.role).map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(M.S,{id:"driver-".concat(e.id),checked:m.drivers.includes(e.id),onCheckedChange:()=>N(e.id)}),(0,t.jsxs)(S.J,{htmlFor:"driver-".concat(e.id),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,t.jsx)(x.A,{className:"size-3"}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{children:e.name}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground capitalize",children:e.role})]})]})]},e.id))})]})})]}),{}),n.length>0&&(0,t.jsx)(()=>(0,t.jsxs)(E.AM,{children:[(0,t.jsx)(E.Wv,{asChild:!0,children:(0,t.jsxs)(C.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(w.A,{className:"size-4"}),"Vehicles",m.vehicles.length>0&&(0,t.jsx)(z.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:m.vehicles.length})]})}),(0,t.jsx)(E.hl,{className:"w-64 p-3",align:"start",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:"Assigned Vehicles"}),(0,t.jsx)(C.$,{variant:"ghost",size:"sm",onClick:()=>g({vehicles:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,t.jsx)(T.w,{}),(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:n.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(M.S,{id:"vehicle-".concat(e.id),checked:m.vehicles.includes(e.id),onCheckedChange:()=>$(e.id)}),(0,t.jsxs)(S.J,{htmlFor:"vehicle-".concat(e.id),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,t.jsx)(w.A,{className:"size-3"}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{children:e.name}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:e.type})]})]})]},e.id))})]})})]}),{})]}),(0,t.jsx)("div",{className:"md:hidden",children:(0,t.jsxs)(F.cj,{open:d,onOpenChange:o,children:[(0,t.jsx)(F.CG,{asChild:!0,children:(0,t.jsxs)(C.$,{variant:"outline",className:"gap-2",children:[(0,t.jsx)(A.A,{className:"size-4"}),"Filters",O>0&&(0,t.jsx)(z.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:O})]})}),(0,t.jsxs)(F.h,{side:"bottom",className:"max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)(F.Fm,{children:[(0,t.jsx)(F.qp,{children:"Filter Delegations"}),(0,t.jsx)(F.Qs,{children:"Apply filters to find specific delegations"})]}),(0,t.jsxs)("div",{className:"mt-6 space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(S.J,{className:"text-sm font-medium",children:"Status"}),(0,t.jsx)("div",{className:"grid gap-2",children:L.map(e=>{let s=e.icon;return(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,t.jsx)(M.S,{id:"mobile-status-".concat(e.value),checked:m.status.includes(e.value),onCheckedChange:()=>j(e.value)}),(0,t.jsxs)(S.J,{htmlFor:"mobile-status-".concat(e.value),className:"cursor-pointer text-sm flex-1 flex items-center gap-2",children:[(0,t.jsx)(s,{className:"size-3"}),e.label]})]},e.value)})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(S.J,{className:"text-sm font-medium",children:"Date Range"}),(0,t.jsx)("div",{className:"border rounded-md p-3",children:(0,t.jsx)(R.V,{mode:"range",selected:{from:m.dateRange.from,to:m.dateRange.to},onSelect:_,numberOfMonths:1,className:"rounded-md border-0"})})]}),O>0&&(0,t.jsxs)(C.$,{variant:"outline",onClick:v,className:"w-full gap-2",children:[(0,t.jsx)(f.A,{className:"size-4"}),"Clear All Filters (",O,")"]})]})]})]})}),O>0&&(0,t.jsxs)(C.$,{variant:"ghost",size:"sm",onClick:v,className:"gap-1 text-muted-foreground hover:text-foreground hidden md:flex",children:[(0,t.jsx)(f.A,{className:"size-3"}),"Clear (",O,")"]})]}),O>0&&(0,t.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Active filters:"}),m.status.map(e=>{let s=L.find(s=>s.value===e);return s?(0,t.jsxs)(z.E,{variant:"secondary",className:"gap-1",children:[s.label,(0,t.jsx)(C.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>j(e),children:(0,t.jsx)(f.A,{className:"size-3"})})]},e):null}),m.location.map(e=>(0,t.jsxs)(z.E,{variant:"secondary",className:"gap-1",children:["Location: ",e,(0,t.jsx)(C.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>p(e),children:(0,t.jsx)(f.A,{className:"size-3"})})]},e)),m.drivers.map(e=>{let s=l.find(s=>s.id===e);return(0,t.jsxs)(z.E,{variant:"secondary",className:"gap-1",children:["Driver: ",(null==s?void 0:s.name)||"Unknown",(0,t.jsx)(C.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>N(e),children:(0,t.jsx)(f.A,{className:"size-3"})})]},e)}),m.vehicles.map(e=>{let s=n.find(s=>s.id===e);return(0,t.jsxs)(z.E,{variant:"secondary",className:"gap-1",children:["Vehicle: ",(null==s?void 0:s.name)||"Unknown",(0,t.jsx)(C.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>$(e),children:(0,t.jsx)(f.A,{className:"size-3"})})]},e)}),(m.dateRange.from||m.dateRange.to)&&(0,t.jsxs)(z.E,{variant:"secondary",className:"gap-1",children:["Date:"," ",m.dateRange.from?(0,I.GP)(m.dateRange.from,"MMM d"):"?"," ","-"," ",m.dateRange.to?(0,I.GP)(m.dateRange.to,"MMM d, yyyy"):"?",(0,t.jsx)(C.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>g({dateRange:{}}),children:(0,t.jsx)(f.A,{className:"size-3"})})]})]})]})};var _=a(51920),O=a(44956),V=a(65064),B=a(1350),G=a(40879);let W=e=>{let{delegations:s,className:a="",onDelete:r,onBulkDelete:l,onBulkArchive:n}=e,{toast:i}=(0,G.dj)(),d=e=>{try{return(0,I.GP)(new Date(e),"MMM dd, yyyy")}catch(e){return"Invalid Date"}},c=[(0,B.BZ)(),{accessorKey:"eventName",header:(0,B.YB)("Event Name"),cell:e=>{let{row:s}=e,a=s.getValue("eventName"),r=s.original.notes;return(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"font-semibold text-foreground",children:a||"-"}),r&&(0,t.jsx)("div",{className:"line-clamp-1 text-xs text-muted-foreground",children:r})]})}},(0,B.ZI)("status","Status",{Planned:{variant:"secondary",label:"Planned"},"In Progress":{variant:"default",label:"In Progress"},Completed:{variant:"success",label:"Completed"},Cancelled:{variant:"destructive",label:"Cancelled"}}),(0,B.nh)("location","Location",e=>{let{className:s}=e;return(0,t.jsx)(y.A,{className:s})}),{accessorKey:"durationFrom",header:(0,B.YB)("Start Date"),cell:e=>{let{row:s}=e,a=s.getValue("durationFrom");return(0,t.jsxs)("div",{className:"flex items-center gap-1 text-sm",children:[(0,t.jsx)(_.A,{className:"size-3 text-muted-foreground"}),d(a)]})}},{accessorKey:"durationTo",header:(0,B.YB)("End Date"),cell:e=>{let{row:s}=e,a=s.getValue("durationTo");return(0,t.jsxs)("div",{className:"flex items-center gap-1 text-sm",children:[(0,t.jsx)(_.A,{className:"size-3 text-muted-foreground"}),d(a)]})}},(0,B.nh)("delegates","Delegates",e=>{let{className:s}=e;return(0,t.jsx)(x.A,{className:s})},{formatter:e=>{var s;return null!=(s=null==e?void 0:e.length)?s:0}}),(0,B.Wy)({viewHref:e=>"/delegations/".concat(e.id),editHref:e=>"/delegations/".concat(e.id,"/edit"),...r&&{onDelete:e=>{r(e)}},showCopyId:!0,customActions:[{label:"Duplicate",onClick:e=>{i({title:"Feature Coming Soon",description:"Duplicate functionality for ".concat(e.eventName)})}}]})],o=[{label:"Delete Selected",icon:e=>{let{className:s}=e;return(0,t.jsx)(O.A,{className:s})},onClick:async e=>{l&&(await l(e),i({title:"Delegations Deleted",description:"".concat(e.length," delegations have been deleted")}))},variant:"destructive"},{label:"Archive Selected",icon:e=>{let{className:s}=e;return(0,t.jsx)(V.A,{className:s})},onClick:async e=>{n&&(await n(e),i({title:"Delegations Archived",description:"".concat(e.length," delegations have been archived")}))}}];return(0,t.jsx)(B.bQ,{data:s,columns:c,className:a,searchPlaceholder:"Search delegations by event name or location...",searchColumn:"eventName",emptyMessage:"No delegations found. Create your first delegation to get started.",pageSize:15,enableRowSelection:!0,enableBulkActions:!0,bulkActions:o,enableColumnVisibility:!0,tableClassName:"shadow-lg",headerClassName:"bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900",rowClassName:"hover:bg-gray-50/50 dark:hover:bg-gray-800/50"})};function Z(e){let{children:s,searchTerm:a="",filters:r}=e;console.log("DelegationListContainer rendered");let{data:l=[],error:n,isLoading:i,refetch:d}=(0,g.BD)(),o=n?n.message:null,m=(0,c.useMemo)(()=>{console.log("Filtering delegations",{allDelegationsCount:l.length,searchTerm:a,filters:r});let e=[...l],s=(null==r?void 0:r.search)||a;if(s){let a=s.toLowerCase();e=e.filter(e=>{var s;return e.eventName.toLowerCase().includes(a)||e.location.toLowerCase().includes(a)||e.status.toLowerCase().includes(a)||(null==(s=e.delegates)?void 0:s.some(e=>e.name.toLowerCase().includes(a)))})}return(null==r?void 0:r.status)&&r.status.length>0&&(e=e.filter(e=>r.status.includes(e.status))),(null==r?void 0:r.location)&&r.location.length>0&&(e=e.filter(e=>r.location.includes(e.location))),(null==r?void 0:r.dateRange)&&(r.dateRange.from||r.dateRange.to)&&(e=e.filter(e=>{let s=new Date(e.durationFrom),a=new Date(e.durationTo);return r.dateRange.from&&r.dateRange.to?s<=r.dateRange.to&&a>=r.dateRange.from:r.dateRange.from?a>=r.dateRange.from:!r.dateRange.to||s<=r.dateRange.to})),(null==r?void 0:r.drivers)&&r.drivers.length>0&&(e=e.filter(e=>{var s;return null==(s=e.drivers)?void 0:s.some(e=>{var s;return r.drivers.includes((null==(s=e.employee)?void 0:s.id.toString())||"")})})),(null==r?void 0:r.escorts)&&r.escorts.length>0&&(e=e.filter(e=>{var s;return null==(s=e.escorts)?void 0:s.some(e=>{var s;return r.escorts.includes((null==(s=e.employee)?void 0:s.id.toString())||"")})})),(null==r?void 0:r.vehicles)&&r.vehicles.length>0&&(e=e.filter(e=>{var s;return null==(s=e.vehicles)?void 0:s.some(e=>{var s;return r.vehicles.includes((null==(s=e.vehicle)?void 0:s.id.toString())||"")})})),e},[a,l,r]),x=(0,c.useCallback)(async()=>{console.log("Manual fetch triggered via fetchDelegations prop (refetch)"),await d()},[d]);return(0,t.jsx)(t.Fragment,{children:s({delegations:m,error:o,fetchDelegations:x,loading:i})})}var H=a(19164),Y=a(74641),J=a(35476),U=a(44598),K=a(76959),X=a(965),q=a(73158),Q=a(66695);let ee=e=>{switch(e){case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Completed":return"bg-purple-500/20 text-purple-700 border-purple-500/30 dark:text-purple-400 dark:bg-purple-500/10 dark:border-purple-500/20";case"Confirmed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"In_Progress":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";case"Planned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},es=e=>{let s=new Date(e);return Number.isNaN(s.getTime())?"N/A":(0,I.GP)(s,"MMM d, yyyy")},ea=e=>{let s=new Date(e);return Number.isNaN(s.getTime())?"N/A":(0,I.GP)(s,"HH:mm")};var et=a(99673);let er=(e,s)=>e.filter(e=>{let a=new Date(e.durationFrom),t=new Date(e.durationTo);return s>=a&&s<=t}),el=e=>{let{className:s="",delegations:a}=e,[r,l]=c.useState(new Date),n=(0,H.w)(r),i=function(e,s){let a=(0,J.a)(e.start),t=(0,J.a)(e.end),r=+a>+t,l=r?+a:+t,n=r?t:a;n.setHours(0,0,0,0);let i=1;if(!i)return[];i<0&&(i=-i,r=!r);let d=[];for(;+n<=l;)d.push((0,J.a)(n)),n.setDate(n.getDate()+i),n.setHours(0,0,0,0);return r?d.reverse():d}({end:(0,Y.p)(r),start:n});return(0,t.jsxs)("div",{className:(0,P.cn)("space-y-6",s),children:[(0,t.jsx)(Q.Zp,{className:"border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-900",children:(0,t.jsx)(Q.aR,{className:"pb-4",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0",children:[(0,t.jsxs)(Q.ZB,{className:"flex items-center gap-3 text-2xl font-semibold text-gray-900 dark:text-white",children:[(0,t.jsx)("div",{className:"rounded-full bg-blue-600 p-2",children:(0,t.jsx)(_.A,{className:"size-6 text-white"})}),(0,I.GP)(r,"MMMM yyyy")]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(C.$,{className:"border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800",onClick:()=>{l(new Date)},size:"sm",variant:"outline",children:"Today"}),(0,t.jsx)(C.$,{className:"border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800",onClick:()=>{l(new Date(r.getFullYear(),r.getMonth()-1,1))},size:"sm",variant:"outline",children:(0,t.jsx)(X.A,{className:"size-4"})}),(0,t.jsx)(C.$,{className:"border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800",onClick:()=>{l(new Date(r.getFullYear(),r.getMonth()+1,1))},size:"sm",variant:"outline",children:(0,t.jsx)(q.A,{className:"size-4"})})]})]})})}),(0,t.jsx)(Q.Zp,{className:"border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-900",children:(0,t.jsxs)(Q.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-7 gap-1",children:[["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(e=>(0,t.jsx)("div",{className:"border border-gray-200 bg-gray-50 p-3 text-center text-sm font-semibold text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300",children:e},e)),i.map(e=>{let s=er(a,e),r=function(e){return(0,K.r)(e,(0,U.A)(e))}(e);return(0,t.jsxs)("div",{className:(0,P.cn)("min-h-[120px] border border-gray-200 dark:border-gray-700 p-2 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200",r&&"bg-blue-50 dark:bg-blue-950/30 border-blue-300 dark:border-blue-700"),children:[(0,t.jsx)("div",{className:(0,P.cn)("text-sm font-medium mb-2 flex items-center justify-center w-6 h-6 rounded-full",r?"bg-blue-600 text-white shadow-sm":"text-gray-700 dark:text-gray-300"),children:(0,I.GP)(e,"d")}),(0,t.jsxs)("div",{className:"space-y-1",children:[s.slice(0,2).map(e=>(0,t.jsx)(d(),{className:"block",href:"/delegations/".concat(e.id),children:(0,t.jsxs)("div",{className:(0,P.cn)("text-xs p-2 rounded border cursor-pointer hover:shadow-sm transition-all duration-200",ee(e.status)),title:"".concat(e.eventName," - ").concat((0,et.fZ)(e.status)),children:[(0,t.jsx)("div",{className:"truncate font-medium",children:e.eventName}),(0,t.jsx)("div",{className:"mt-0.5 text-xs opacity-75",children:ea(e.durationFrom)})]})},e.id)),s.length>2&&(0,t.jsxs)("div",{className:"rounded border border-gray-200 bg-gray-100 p-1 text-center text-xs text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400",children:["+",s.length-2," more"]})]})]},e.toISOString())})]}),(0,t.jsxs)("div",{className:"mt-8 border-t border-gray-200 pt-6 dark:border-gray-700",children:[(0,t.jsx)("div",{className:"mb-4 text-sm font-semibold text-gray-900 dark:text-white",children:"Status Legend"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-3",children:[{label:"Planned",status:"Planned"},{label:"Confirmed",status:"Confirmed"},{label:"In Progress",status:"In_Progress"},{label:"Completed",status:"Completed"},{label:"Cancelled",status:"Cancelled"}].map(e=>{let{label:s,status:a}=e;return(0,t.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 dark:border-gray-700 dark:bg-gray-800",children:[(0,t.jsx)("div",{className:(0,P.cn)("w-3 h-3 rounded-full",ee(a))}),(0,t.jsx)("span",{className:"text-xs font-medium text-gray-700 dark:text-gray-300",children:s})]},a)})})]}),(0,t.jsx)("div",{className:"mt-6 border-t border-gray-200 pt-4 dark:border-gray-700",children:(0,t.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-sm text-gray-600 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400",children:[(0,t.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:a.length})," ","delegation",1===a.length?"":"s"," this month"]})})]})})]})};var en=a(58260),ei=a(91721),ed=a(31949),ec=a(50594),eo=a(17607),em=a(6560);let ex=e=>{let{status:s,className:a,size:r="md",floating:l=!1}=e,n=(0,P.cn)("font-medium border shadow-sm transition-all duration-200",{sm:"text-xs py-1 px-2",md:"text-xs py-1.5 px-3",lg:"text-sm py-2 px-4"}[r],l&&"absolute top-4 right-4",ee(s));return(0,t.jsx)(z.E,{className:(0,P.cn)(n,a),children:(0,et.fZ)(s)})};var eh=a(69738);function eu(e){var s,a;let{delegation:r,className:l}=e,{driverInfo:n,escortInfo:i,hasFlightDetails:c,isActive:o,needsEscortAssignment:m,vehicleInfo:h}=(0,eh.y)(r);return(0,t.jsxs)(Q.Zp,{className:(0,P.cn)("flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md","transition-all duration-200 ease-in-out","hover:shadow-lg hover:border-primary/30","group",l),children:[(0,t.jsx)("div",{className:(0,P.cn)("h-1 w-full transition-all duration-200",o?"bg-gradient-to-r from-primary to-accent":"Completed"===r.status?"bg-green-500":"Cancelled"===r.status?"bg-destructive":"bg-muted")}),(0,t.jsxs)(Q.aR,{className:"p-5 pb-3",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsx)(Q.ZB,{className:"text-xl font-semibold text-primary mb-1 line-clamp-2",children:r.eventName}),(0,t.jsxs)(Q.BT,{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,t.jsx)(y.A,{className:"size-4 text-muted-foreground shrink-0"}),(0,t.jsx)("span",{className:"truncate",children:r.location})]})]}),(0,t.jsx)(ex,{size:"sm",status:r.status,className:"shrink-0"})]}),o&&(0,t.jsxs)("div",{className:"flex w-fit items-center gap-2 rounded-full bg-primary/10 px-3 py-1.5 border border-primary/20 mt-3",children:[(0,t.jsx)("div",{className:"size-2 animate-pulse rounded-full bg-primary"}),(0,t.jsx)("span",{className:"text-xs font-medium text-primary",children:"Currently Active"})]})]}),(0,t.jsxs)(Q.Wu,{className:"flex-1 p-5 pt-0",children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)(b.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Duration"})]}),(0,t.jsxs)("p",{className:"text-sm font-medium",children:[es(r.durationFrom)," -"," ",es(r.durationTo)]})]}),(0,t.jsx)(T.w,{className:"my-4"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Delegates"})]}),(0,t.jsx)(z.E,{variant:"secondary",className:"text-xs",children:null!=(a=null==(s=r.delegates)?void 0:s.length)?a:0})]}),c&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(en.A,{className:"size-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Flight Details"})]}),(0,t.jsx)(z.E,{variant:"outline",className:"text-xs text-blue-600 border-blue-200",children:"Available"})]}),(i||n||h)&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(T.w,{className:"my-3"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("span",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide",children:"Assignments"}),(0,t.jsxs)("div",{className:"space-y-2",children:[i&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(ei.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm",children:"Escort"})]}),(0,t.jsx)("span",{className:"text-sm font-medium truncate max-w-32",children:(0,et.DV)(i)})]}),n&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(ei.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm",children:"Driver"})]}),(0,t.jsx)("span",{className:"text-sm font-medium truncate max-w-32",children:(0,et.DV)(n)})]}),h&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(w.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm",children:"Vehicle"})]}),(0,t.jsxs)("span",{className:"text-sm font-medium truncate max-w-32",children:[h.make," ",h.model]})]})]})]})]}),m&&(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 rounded-lg bg-destructive/10 border border-destructive/20 mt-3",children:[(0,t.jsx)(ed.A,{className:"size-4 text-destructive shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-destructive",children:"Escort Required"}),(0,t.jsx)("p",{className:"text-xs text-destructive/80",children:"No escort assigned"})]})]})]}),r.notes&&(0,t.jsx)("div",{className:"mt-4 p-3 rounded-lg bg-muted/30",children:(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(ec.A,{className:"size-4 text-muted-foreground mt-0.5 shrink-0"}),(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsx)("p",{className:"text-xs font-medium text-muted-foreground mb-1",children:"Notes"}),(0,t.jsx)("p",{className:"text-sm line-clamp-2 text-muted-foreground",children:r.notes})]})]})})]}),(0,t.jsx)(Q.wL,{className:"border-t bg-muted/20 p-4",children:(0,t.jsx)(em.r,{actionType:"primary",asChild:!0,className:"w-full",icon:(0,t.jsx)(eo.A,{className:"size-4"}),children:(0,t.jsx)(d(),{href:"/delegations/".concat(r.id),children:"View Details"})})})]})}let eg=e=>{let{className:s="",compactMode:a,delegations:r,gridColumns:l,viewMode:n}=e;switch(n){case"calendar":return(0,t.jsx)(el,{className:s,delegations:r});case"list":return(0,t.jsx)("div",{className:(0,P.cn)("flex flex-col",a?"gap-2":"gap-4",s),children:r.map(e=>(0,t.jsx)(eu,{delegation:e},e.id))});case"table":return(0,t.jsx)(W,{className:s,delegations:r});default:return(0,t.jsx)("div",{className:(0,P.cn)("grid grid-cols-1 gap-6","md:grid-cols-2 lg:grid-cols-".concat(l),a&&"gap-3",s),children:r.map(e=>(0,t.jsx)(eu,{delegation:e},e.id))})}};var ev=a(24865),ej=a(89440),ep=a(54165),eN=a(77023),ef=a(95647),eb=a(83761),ey=a(80937);function ew(){let[e,s]=(0,c.useState)(""),[a,i]=(0,c.useState)({dateRange:{},drivers:[],escorts:[],location:[],search:"",status:[],vehicles:[]}),{layout:m}=(0,u.fX)("delegation")(),{data:x=[]}=(0,g.BD)(),{data:h=[]}=(0,eb.nR)(),{data:v=[]}=(0,ey.T$)(),j=[...new Set(x.map(e=>e.location).filter(Boolean))],N=h.map(e=>({id:e.id.toString(),name:e.name,role:e.role})),f=v.map(e=>({id:e.id.toString(),name:"".concat(e.make," ").concat(e.model),type:"".concat(e.year," ").concat(e.licensePlate)}));return(0,t.jsx)(o.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(ej.AppBreadcrumb,{homeHref:"/",homeLabel:"Dashboard"}),(0,t.jsx)(ef.z,{description:"Track and manage all your events, trips, and delegate information.",icon:r.A,title:"Manage Delegations",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(em.r,{actionType:"primary",asChild:!0,icon:(0,t.jsx)(l.A,{className:"mr-2 size-4"}),children:(0,t.jsx)(d(),{href:"/delegations/add",children:"Add New Delegation"})}),(0,t.jsx)(ev.M,{getReportUrl:()=>{let s=new URLSearchParams({searchTerm:a.search||e}).toString();return"/delegations/report/list?".concat(s)},isList:!0}),(0,t.jsxs)(ep.lG,{children:[(0,t.jsx)(ep.zM,{asChild:!0,children:(0,t.jsx)(em.r,{actionType:"secondary",icon:(0,t.jsx)(n.A,{className:"size-4"}),children:"Settings"})}),(0,t.jsxs)(ep.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsx)(ep.L3,{children:"Dashboard Settings"}),(0,t.jsx)(ep.rr,{children:"Customize the display and behavior of your delegation dashboard."}),(0,t.jsx)(p,{})]})]})]})}),(0,t.jsx)("div",{className:"mb-6 rounded-lg bg-card p-4 shadow-md",children:(0,t.jsx)($,{employeesList:N,initialFilters:{dateRange:{},drivers:[],escorts:[],location:[],search:e,status:[],vehicles:[]},locationsList:j,onFiltersChange:e=>{i(e),s(e.search)},vehiclesList:f})}),(0,t.jsx)(Z,{filters:a,searchTerm:e,children:s=>{let{delegations:a,error:n,fetchDelegations:i,loading:c}=s;return(0,t.jsx)(eN.gO,{data:a,emptyComponent:(0,t.jsxs)("div",{className:"rounded-lg bg-card py-12 text-center shadow-md",children:[(0,t.jsx)(r.A,{className:"mx-auto mb-6 size-16 text-muted-foreground"}),(0,t.jsx)("h3",{className:"mb-2 text-2xl font-semibold text-foreground",children:e?"No Delegations Match Your Search":"No Delegations Yet!"}),(0,t.jsx)("p",{className:"mx-auto mb-6 mt-2 max-w-md text-muted-foreground",children:e?"Try adjusting your search terms or add a new delegation.":"It looks like you haven't added any delegations yet. Get started by adding one."}),!e&&(0,t.jsx)(em.r,{actionType:"primary",asChild:!0,icon:(0,t.jsx)(l.A,{className:"size-4"}),size:"lg",children:(0,t.jsx)(d(),{href:"/delegations/add",children:"Add Your First Delegation"})})]}),error:n,isLoading:c,loadingComponent:(0,t.jsx)(eN.jt,{count:3,variant:"card"}),onRetry:i,children:e=>(0,t.jsx)(eg,{compactMode:m.compactMode,delegations:e,gridColumns:m.gridColumns,viewMode:m.viewMode})})}})]})})}},40879:(e,s,a)=>{"use strict";a.d(s,{dj:()=>x,oR:()=>m});var t=a(12115);let r=0,l=new Map,n=e=>{if(l.has(e))return;let s=setTimeout(()=>{l.delete(e),o({toastId:e,type:"REMOVE_TOAST"})},1e6);l.set(e,s)},i=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"DISMISS_TOAST":{let{toastId:a}=s;if(a)n(a);else for(let s of e.toasts)n(s.id);return{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)}}},d=[],c={toasts:[]};function o(e){for(let s of(c=i(c,e),d))s(c)}function m(e){let{...s}=e,a=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),t=()=>o({toastId:a,type:"DISMISS_TOAST"});return o({toast:{...s,id:a,onOpenChange:e=>{e||t()},open:!0},type:"ADD_TOAST"}),{dismiss:t,id:a,update:e=>o({toast:{...e,id:a},type:"UPDATE_TOAST"})}}function x(){let[e,s]=t.useState(c);return t.useEffect(()=>(d.push(s),()=>{let e=d.indexOf(s);-1!==e&&d.splice(e,1)}),[e]),{...e,dismiss:e=>o({type:"DISMISS_TOAST",...e&&{toastId:e}}),toast:m}}},44598:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var t=a(92084);function r(e){return(0,t.w)(e,Date.now())}},50594:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},52639:(e,s,a)=>{"use strict";a.d(s,{GW:()=>i});var t=a(95155);a(12115);var r=a(88240),l=a(54036);let n=e=>{let{children:s,className:a="",config:n}=e;return(0,t.jsx)(r.A,{children:(0,t.jsx)("div",{className:(0,l.cn)("min-h-screen bg-background",a),children:(0,t.jsx)("main",{className:"flex-1",children:(0,t.jsx)("div",{className:"container mx-auto space-y-6 px-4 py-6 sm:px-6 lg:px-8",children:s})})})})},i=e=>{let{children:s,className:a="",config:r}=e;return(0,t.jsx)(n,{config:r,className:a,children:(0,t.jsx)("div",{className:"space-y-8",children:s})})}},57082:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58260:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(40157).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},69738:(e,s,a)=>{"use strict";a.d(s,{y:()=>r});var t=a(12115);let r=e=>(0,t.useMemo)(()=>{var s,a,t;let r=e.escorts&&e.escorts.length>0&&(null==(s=e.escorts[0])?void 0:s.employee)?e.escorts[0].employee:null,l=e.drivers&&e.drivers.length>0&&(null==(a=e.drivers[0])?void 0:a.employee)?e.drivers[0].employee:null,n=e.vehicles&&e.vehicles.length>0&&(null==(t=e.vehicles[0])?void 0:t.vehicle)?e.vehicles[0].vehicle:null,i=!!(e.arrivalFlight||e.departureFlight),d=!r&&"Completed"!==e.status&&"Cancelled"!==e.status;return{escortInfo:r,driverInfo:l,vehicleInfo:n,hasFlightDetails:i,needsEscortAssignment:d,isActive:"In_Progress"===e.status}},[e])},74156:(e,s,a)=>{Promise.resolve().then(a.bind(a,25335))}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,1137,3860,9664,1263,5495,1859,6874,5247,6463,7454,3030,6233,3769,4036,8658,111,7515,3615,7841,5320,6554,5916,8986,8441,1684,7358],()=>s(74156)),_N_E=e.O()}]);