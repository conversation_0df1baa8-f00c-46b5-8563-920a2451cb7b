module.exports = {

"[project]/src/components/ui/progress.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Progress": (()=>Progress)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$progress$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-progress/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <locals>");
'use client';
;
;
;
;
const Progress = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, value, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$progress$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('relative h-4 w-full overflow-hidden rounded-full bg-secondary', className),
        ref: ref,
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$progress$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Indicator"], {
            className: "size-full flex-1 bg-primary transition-all",
            style: {
                transform: `translateX(-${100 - (value || 0)}%)`
            }
        }, void 0, false, {
            fileName: "[project]/src/components/ui/progress.tsx",
            lineNumber: 20,
            columnNumber: 5
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/progress.tsx",
        lineNumber: 12,
        columnNumber: 3
    }, this));
Progress.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$progress$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"].displayName;
;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[project]/src/lib/services/WebSocketManager.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Unified WebSocket Manager for WorkHub Application
 * Provides centralized WebSocket connection management with domain-specific channels
 * Follows SRP and DRY principles with smart fallback strategies
 * @module services/WebSocketManager
 */ __turbopack_context__.s({
    "WebSocketManager": (()=>WebSocketManager),
    "getWebSocketManager": (()=>getWebSocketManager),
    "useWebSocketState": (()=>useWebSocketState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/TokenRefreshService.ts [app-ssr] (ecmascript)");
;
;
;
;
class WebSocketManager {
    static instance = null;
    config;
    connectionState = 'disconnected';
    reconnectAttempts = 0;
    socket = null;
    stateListeners = new Set();
    subscriptions = new Map();
    constructor(config = {}){
        this.config = {
            autoConnect: config.autoConnect ?? true,
            reconnectAttempts: config.reconnectAttempts ?? 5,
            reconnectDelay: config.reconnectDelay ?? 1000,
            timeout: config.timeout ?? 10_000,
            url: config.url ?? process.env.NEXT_PUBLIC_WEBSOCKET_URL ?? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getEnvironmentConfig"])().wsUrl.replace('ws://', 'http://').replace('wss://', 'https://')
        };
        if (this.config.autoConnect) {
            this.connect();
        }
        // Subscribe to token refresh events
        this.setupTokenRefreshHandling();
    }
    /**
   * Get singleton instance
   */ static getInstance(config) {
        WebSocketManager.instance ??= new WebSocketManager(config);
        return WebSocketManager.instance;
    }
    /**
   * Connect to WebSocket server
   */ async connect() {
        if (this.socket?.connected) {
            console.debug('WebSocket already connected');
            return;
        }
        this.setConnectionState('connecting');
        try {
            // Get current session and token for authentication
            const { data: { session }, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.getSession();
            if (error) {
                console.warn('Failed to get session for WebSocket connection:', error);
            }
            const connectionOptions = {
                forceNew: true,
                timeout: this.config.timeout,
                transports: [
                    'websocket',
                    'polling'
                ],
                withCredentials: true
            };
            // Add authentication token if available
            if (session?.access_token) {
                connectionOptions.auth = {
                    token: session.access_token
                };
                console.debug('🔐 WebSocket connecting with authentication token');
                // Validate token expiration
                const tokenExpiry = session.expires_at ? session.expires_at * 1000 : 0;
                const now = Date.now();
                const timeUntilExpiry = tokenExpiry - now;
                if (timeUntilExpiry <= 60_000) {
                    // Less than 1 minute
                    console.warn('⚠️ WebSocket token expires soon, may need refresh');
                }
            } else {
                console.warn('⚠️ WebSocket connecting without authentication token - connection may fail');
            }
            this.socket = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(this.config.url, connectionOptions);
            this.setupEventHandlers();
        } catch (error) {
            console.error('Failed to connect WebSocket:', error);
            this.setConnectionState('error');
            this.scheduleReconnect();
        }
    }
    /**
   * Cleanup resources
   */ destroy() {
        this.disconnect();
        this.subscriptions.clear();
        this.stateListeners.clear();
        WebSocketManager.instance = null;
    }
    /**
   * Disconnect from WebSocket server
   */ disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
        this.setConnectionState('disconnected');
        this.reconnectAttempts = 0;
    }
    /**
   * Emit event to specific domain channel
   */ emit(channel, event, data) {
        if (!this.socket?.connected) {
            console.warn(`Cannot emit ${channel}:${event} - WebSocket not connected`);
            return;
        }
        this.socket.emit(event, data);
    }
    /**
   * Get current connection state
   */ getConnectionState() {
        return this.connectionState;
    }
    /**
   * Check if WebSocket is connected
   */ isConnected() {
        return this.connectionState === 'connected' && this.socket?.connected === true;
    }
    /**
   * Join domain-specific room
   */ joinRoom(room) {
        if (!this.socket?.connected) {
            console.warn(`Cannot join room ${room} - WebSocket not connected`);
            return;
        }
        this.socket.emit('join-room', room);
    }
    /**
   * Leave domain-specific room
   */ leaveRoom(room) {
        if (!this.socket?.connected) {
            return;
        }
        this.socket.emit('leave-room', room);
    }
    /**
   * Subscribe to connection state changes
   */ onStateChange(callback) {
        this.stateListeners.add(callback);
        return ()=>{
            this.stateListeners.delete(callback);
        };
    }
    /**
   * Subscribe to domain-specific events
   */ subscribe(channel, event, callback) {
        const eventKey = `${channel}:${event}`;
        if (!this.subscriptions.has(eventKey)) {
            this.subscriptions.set(eventKey, new Set());
        }
        this.subscriptions.get(eventKey).add(callback);
        // Set up socket listener if connected
        if (this.socket?.connected && event) {
            this.socket.on(event, callback);
        }
        // Return unsubscribe function
        return ()=>{
            const callbacks = this.subscriptions.get(eventKey);
            if (callbacks) {
                callbacks.delete(callback);
                if (callbacks.size === 0) {
                    this.subscriptions.delete(eventKey);
                }
            }
            if (this.socket && event) {
                this.socket.off(event, callback);
            }
        };
    }
    /**
   * Handle authentication errors by triggering token refresh
   */ handleAuthenticationError() {
        const tokenRefreshService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTokenRefreshService"])();
        console.log('🔐 Handling WebSocket authentication error...');
        // Disconnect current socket to prevent further auth errors
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
        // Attempt to refresh token
        tokenRefreshService.refreshNow().then((success)=>{
            if (success) {
                console.log('🔄 Token refresh successful, retrying WebSocket connection');
            // The reconnection will be handled by setupTokenRefreshHandling
            } else {
                console.error('🔄 Token refresh failed, scheduling normal reconnect');
                this.scheduleReconnect();
            }
        }).catch((error)=>{
            console.error('🔄 Token refresh error:', error);
            this.scheduleReconnect();
        });
    }
    /**
   * Resubscribe to all events after reconnection
   */ resubscribeToEvents() {
        if (!this.socket) return;
        for (const [eventKey, callbacks] of this.subscriptions){
            const [, event] = eventKey.split(':');
            for (const callback of callbacks){
                if (event) {
                    this.socket.on(event, callback);
                }
            }
        }
    }
    /**
   * Schedule reconnection with exponential backoff
   */ scheduleReconnect() {
        if (this.reconnectAttempts >= this.config.reconnectAttempts) {
            console.error('Max reconnection attempts reached');
            this.setConnectionState('error');
            return;
        }
        this.setConnectionState('reconnecting');
        this.reconnectAttempts++;
        const delay = this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        setTimeout(()=>{
            console.info(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts})`);
            this.connect();
        }, delay);
    }
    /**
   * Set connection state and notify listeners
   */ setConnectionState(state) {
        if (this.connectionState !== state) {
            this.connectionState = state;
            for (const listener of this.stateListeners)listener(state);
        }
    }
    /**
   * Setup socket event handlers
   */ setupEventHandlers() {
        if (!this.socket) return;
        this.socket.on('connect', ()=>{
            console.info('WebSocket connected');
            this.setConnectionState('connected');
            this.reconnectAttempts = 0;
            this.resubscribeToEvents();
        });
        this.socket.on('disconnect', (reason)=>{
            console.warn('WebSocket disconnected:', reason);
            this.setConnectionState('disconnected');
            if (reason === 'io server disconnect') {
                // Server initiated disconnect, don't reconnect automatically
                return;
            }
            this.scheduleReconnect();
        });
        this.socket.on('connect_error', (error)=>{
            console.error('WebSocket connection error:', error);
            this.setConnectionState('error');
            // Check if error is authentication-related
            if (error.message?.includes('Authentication') || error.message?.includes('token') || error.message?.includes('No token provided') || error.message?.includes('Unauthorized')) {
                console.warn('🔐 Authentication error detected, attempting token refresh');
                this.handleAuthenticationError();
            } else {
                this.scheduleReconnect();
            }
        });
        // Listen for authentication errors from the server
        this.socket.on('auth_error', (errorData)=>{
            console.error('🔐 Server authentication error:', errorData);
            this.handleAuthenticationError();
        });
        // Listen for token refresh requests from server
        this.socket.on('token_refresh_required', ()=>{
            console.warn('🔄 Server requested token refresh');
            this.handleAuthenticationError();
        });
    }
    /**
   * Setup token refresh event handling
   */ setupTokenRefreshHandling() {
        const tokenRefreshService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTokenRefreshService"])();
        tokenRefreshService.subscribe((event, _data)=>{
            switch(event){
                case 'critical_refresh_failed':
                    {
                        console.error('🔄 Critical token refresh failure, disconnecting WebSocket');
                        this.disconnect();
                        this.setConnectionState('error');
                        break;
                    }
                case 'refresh_failed':
                    {
                        console.error('🔄 Token refresh failed, WebSocket may lose connection');
                        break;
                    }
                case 'refresh_success':
                    {
                        console.log('🔄 Token refreshed, reconnecting WebSocket with new token');
                        // Disconnect current connection and reconnect with new token
                        if (this.socket) {
                            this.socket.disconnect();
                            this.socket = null;
                        }
                        // Reconnect with fresh token
                        setTimeout(()=>this.connect(), 500);
                        break;
                    }
            }
        });
    }
}
const getWebSocketManager = (config)=>{
    return WebSocketManager.getInstance(config);
};
const useWebSocketState = ()=>{
    const manager = getWebSocketManager();
    return {
        connectionState: manager.getConnectionState(),
        isConnected: manager.isConnected()
    };
};
}}),
"[project]/src/hooks/api/useSmartQuery.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Smart Query Hook with WebSocket Integration
 * Automatically disables polling when WebSocket is connected
 * Follows modern best practices for real-time data management
 * @module hooks/useSmartQuery
 */ __turbopack_context__.s({
    "useCrudQuery": (()=>useCrudQuery),
    "useNotificationQuery": (()=>useNotificationQuery),
    "useReliabilityQuery": (()=>useReliabilityQuery),
    "useSmartQuery": (()=>useSmartQuery),
    "useSystemQuery": (()=>useSystemQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$WebSocketManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/WebSocketManager.ts [app-ssr] (ecmascript)");
;
;
;
/**
 * Mapping of domain channels to Socket.IO room names
 * This ensures the frontend joins the correct rooms that the backend emits events to
 */ const CHANNEL_ROOM_MAPPING = {
    crud: 'entity-updates',
    notifications: 'notifications-monitoring',
    reliability: 'reliability-monitoring',
    system: 'system-monitoring'
};
function useCrudQuery(queryKey, queryFn, entityType, options) {
    return useSmartQuery(queryKey, queryFn, {
        channel: 'crud',
        events: [
            `${entityType}:created`,
            `${entityType}:updated`,
            `${entityType}:deleted`,
            `refresh:${entityType}`
        ],
        fallbackInterval: 30_000
    }, options);
}
function useNotificationQuery(queryKey, queryFn, options) {
    return useSmartQuery(queryKey, queryFn, {
        channel: 'notifications',
        events: [
            'notification-created',
            'notification-updated'
        ],
        fallbackInterval: 60_000
    }, options);
}
function useReliabilityQuery(queryKey, queryFn, monitoringType, options) {
    // Increased intervals to reduce aggressive polling and cancellations
    const intervalMap = {
        alerts: 30_000,
        'circuit-breakers': 60_000,
        health: 45_000,
        metrics: 60_000
    };
    const webSocketManager = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$WebSocketManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWebSocketManager"])();
    // Join reliability monitoring room when WebSocket is connected
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (webSocketManager.isConnected()) {
            console.debug(`[ReliabilityQuery] Joining reliability-monitoring room for ${monitoringType}`);
            webSocketManager.joinRoom('reliability-monitoring');
        }
        // Subscribe to connection state changes to join room when connected
        const unsubscribe = webSocketManager.onStateChange((state)=>{
            if (state === 'connected') {
                console.debug(`[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ${monitoringType}`);
                webSocketManager.joinRoom('reliability-monitoring');
            }
        });
        return ()=>{
            unsubscribe();
            // Leave room when component unmounts
            if (webSocketManager.isConnected()) {
                webSocketManager.leaveRoom('reliability-monitoring');
            }
        };
    }, [
        webSocketManager,
        monitoringType
    ]);
    return useSmartQuery(queryKey, queryFn, {
        channel: 'reliability',
        events: [
            `${monitoringType}-update`,
            `${monitoringType}-created`,
            `${monitoringType}-resolved`
        ],
        fallbackInterval: intervalMap[monitoringType]
    }, options);
}
function useSmartQuery(queryKey, queryFn, config, options) {
    const { channel, enableFallback = true, enableWebSocket = true, events, fallbackInterval = 30_000 } = config;
    const [isWebSocketConnected, setIsWebSocketConnected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const webSocketManager = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$WebSocketManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWebSocketManager"])();
    // Track WebSocket connection state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const updateConnectionState = ()=>{
            setIsWebSocketConnected(webSocketManager.isConnected());
        };
        // Initial state
        updateConnectionState();
        // Subscribe to state changes
        const unsubscribe = webSocketManager.onStateChange(updateConnectionState);
        return unsubscribe;
    }, [
        webSocketManager
    ]);
    // Determine if we should use fallback polling
    const isUsingFallback = enableFallback && (!enableWebSocket || !isWebSocketConnected);
    // Configure React Query options based on WebSocket state
    const queryOptions = {
        // Longer cache time for better performance
        gcTime: 10 * 60 * 1000,
        queryFn,
        queryKey,
        // Disable polling when WebSocket is connected
        refetchInterval: isUsingFallback ? fallbackInterval : false,
        refetchOnReconnect: true,
        // Enable background refetch only when using fallback
        refetchOnWindowFocus: isUsingFallback,
        // Shorter stale time when using WebSocket (real-time updates)
        staleTime: isWebSocketConnected ? 0 : 30_000,
        ...options
    };
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const queryResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])(queryOptions);
    // Manage Socket.IO room joining/leaving based on channel
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!enableWebSocket || !isWebSocketConnected) {
            return;
        }
        const roomName = CHANNEL_ROOM_MAPPING[channel];
        if (!roomName) {
            console.warn(`[SmartQuery] No room mapping found for channel: ${channel}`);
            return;
        }
        // Join the appropriate room for this channel
        try {
            webSocketManager.joinRoom(roomName);
            console.log(`[SmartQuery] Joined room: ${roomName} for channel: ${channel}`);
        } catch (error) {
            console.error(`[SmartQuery] Failed to join room ${roomName}:`, error);
        }
        // Cleanup: leave room when component unmounts or dependencies change
        return ()=>{
            try {
                webSocketManager.leaveRoom(roomName);
                console.log(`[SmartQuery] Left room: ${roomName} for channel: ${channel}`);
            } catch (error) {
                console.error(`[SmartQuery] Failed to leave room ${roomName}:`, error);
            }
        };
    }, [
        enableWebSocket,
        isWebSocketConnected,
        channel,
        webSocketManager
    ]);
    // Subscribe to WebSocket events for real-time updates
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!enableWebSocket || !isWebSocketConnected || events.length === 0) {
            return;
        }
        const unsubscribers = [];
        // Subscribe to each event
        for (const event of events){
            const unsubscribe = webSocketManager.subscribe(channel, event, (data)=>{
                console.log(`[SmartQuery] WebSocket event received: ${channel}:${event}`, data);
                // Invalidate the specific query to trigger refetch
                queryClient.invalidateQueries({
                    queryKey
                });
            });
            unsubscribers.push(unsubscribe);
        }
        return ()=>{
            for (const unsubscribe of unsubscribers)unsubscribe();
        };
    }, [
        enableWebSocket,
        isWebSocketConnected,
        events,
        channel,
        webSocketManager,
        queryClient,
        queryKey
    ]);
    return {
        ...queryResult,
        isUsingFallback,
        isWebSocketConnected
    };
}
function useSystemQuery(queryKey, queryFn, options) {
    return useSmartQuery(queryKey, queryFn, {
        channel: 'system',
        events: [
            'system-update',
            'config-changed'
        ],
        fallbackInterval: 120_000
    }, options);
}
}}),
"[project]/src/lib/transformers/delegationEnrichment.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Delegation enrichment transformer following established patterns
 * @description Handles the enrichment of delegation data with employee and vehicle details
 * @module transformers/delegationEnrichment
 */ __turbopack_context__.s({
    "DelegationEnrichmentTransformer": (()=>DelegationEnrichmentTransformer),
    "enrichDelegation": (()=>enrichDelegation)
});
class DelegationEnrichmentTransformer {
    /**
   * Main enrichment method that combines delegation data with employee and vehicle details
   * @param delegation - Base delegation data
   * @param employees - Array of employees
   * @param vehicles - Array of vehicles
   * @returns Fully enriched delegation
   */ static enrich(delegation, employees, vehicles) {
        const { employeeMap, vehicleMap } = this.createLookupMaps(employees, vehicles);
        return {
            ...delegation,
            drivers: this.enrichDrivers(delegation.drivers, employeeMap) ?? [],
            escorts: this.enrichEscorts(delegation.escorts, employeeMap) ?? [],
            vehicles: this.enrichVehicles(delegation.vehicles, vehicleMap) ?? []
        };
    }
    /**
   * Creates optimized lookup maps for O(1) performance
   * @param employees - Array of employees
   * @param vehicles - Array of vehicles
   * @returns Object containing employee and vehicle maps
   */ static createLookupMaps(employees, vehicles) {
        return {
            employeeMap: new Map(employees.map((emp)=>[
                    emp.id,
                    emp
                ])),
            vehicleMap: new Map(vehicles.map((veh)=>[
                    veh.id,
                    veh
                ]))
        };
    }
    /**
   * Enriches driver assignments with employee details
   * @param drivers - Array of driver assignments
   * @param employeeMap - Map of employees for O(1) lookup
   * @returns Enriched driver assignments
   */ static enrichDrivers(drivers, employeeMap) {
        return drivers?.map((driver)=>{
            const employee = driver.employee || employeeMap.get(Number(driver.employeeId));
            return {
                ...driver,
                ...employee && {
                    employee
                }
            };
        });
    }
    /**
   * Enriches escort assignments with employee details
   * @param escorts - Array of escort assignments
   * @param employeeMap - Map of employees for O(1) lookup
   * @returns Enriched escort assignments
   */ static enrichEscorts(escorts, employeeMap) {
        return escorts?.map((escort)=>{
            const employee = escort.employee || employeeMap.get(Number(escort.employeeId));
            return {
                ...escort,
                ...employee && {
                    employee
                }
            };
        });
    }
    /**
   * Enriches vehicle assignments with vehicle details
   * @param vehicles - Array of vehicle assignments
   * @param vehicleMap - Map of vehicles for O(1) lookup
   * @returns Enriched vehicle assignments
   */ static enrichVehicles(vehicles, vehicleMap) {
        return vehicles?.map((vehicleAssignment)=>{
            const vehicle = vehicleAssignment.vehicle || vehicleMap.get(vehicleAssignment.vehicleId);
            return {
                ...vehicleAssignment,
                ...vehicle && {
                    vehicle
                }
            };
        });
    }
}
const enrichDelegation = (delegation, employees, vehicles)=>{
    return DelegationEnrichmentTransformer.enrich(delegation, employees, vehicles);
};
}}),
"[project]/src/lib/stores/queries/delegationQueries.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Delegation query configurations following Single Responsibility Principle
 * @description Centralized query configurations for delegation-related data fetching
 */ __turbopack_context__.s({
    "createDelegationQuery": (()=>createDelegationQuery),
    "createDelegationWithAssignmentsQueries": (()=>createDelegationWithAssignmentsQueries),
    "createEmployeesQuery": (()=>createEmployeesQuery),
    "createVehiclesQuery": (()=>createVehiclesQuery),
    "delegationQueryKeys": (()=>delegationQueryKeys),
    "delegationQueryOptions": (()=>delegationQueryOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>"); // Use centralized services
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
;
const delegationQueryKeys = {
    all: [
        'delegations'
    ],
    detail: (id)=>[
            'delegations',
            id
        ],
    withAssignments: (id)=>[
            'delegations',
            id,
            'with-assignments'
        ]
};
const createDelegationQuery = (id)=>({
        enabled: !!id,
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationApiService"].getById(id),
        queryKey: delegationQueryKeys.detail(id),
        staleTime: 5 * 60 * 1000
    });
const createEmployeesQuery = ()=>({
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].getAll(),
        queryKey: [
            'employees'
        ],
        staleTime: 10 * 60 * 1000
    });
const createVehiclesQuery = ()=>({
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].getAll(),
        queryKey: [
            'vehicles'
        ],
        staleTime: 10 * 60 * 1000
    });
const createDelegationWithAssignmentsQueries = (id)=>[
        createDelegationQuery(id),
        createEmployeesQuery(),
        createVehiclesQuery()
    ];
const delegationQueryOptions = {
    gcTime: 10 * 60 * 1000,
    retry: 3,
    retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30_000),
    staleTime: 5 * 60 * 1000
};
}}),
"[project]/src/lib/stores/queries/useDelegations.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file TanStack Query hooks for Delegation-related data.
 * These hooks manage fetching, caching, and mutating delegation data,
 * integrating with the DelegationApiService and DelegationTransformer.
 * @module stores/queries/useDelegations
 */ __turbopack_context__.s({
    "useCreateDelegation": (()=>useCreateDelegation),
    "useDelegation": (()=>useDelegation),
    "useDelegationEnriched": (()=>useDelegationEnriched),
    "useDelegationWithAssignments": (()=>useDelegationWithAssignments),
    "useDelegations": (()=>useDelegations),
    "useDeleteDelegation": (()=>useDeleteDelegation),
    "useManageDelegationFlightDetails": (()=>useManageDelegationFlightDetails),
    "useUpdateDelegation": (()=>useUpdateDelegation),
    "useUpdateDelegationStatus": (()=>useUpdateDelegationStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQueries.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/typeHelpers.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-ssr] (ecmascript)"); // Adjusted import path
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>"); // Use centralized service
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationEnrichment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/delegationEnrichment.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/delegationTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/delegationQueries.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
const useDelegations = (options)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
    ], async ()=>{
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationApiService"].getAll();
        return result.data;
    }, 'delegation', {
        staleTime: 0,
        ...options
    });
};
const useDelegation = (id)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)
    ], async ()=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationApiService"].getById(id);
    }, 'delegation', {
        enabled: !!id,
        staleTime: 5 * 60 * 1000
    });
};
const useDelegationWithAssignments = (id)=>{
    // Execute all queries in parallel using useQueries for maximum performance
    const results = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueries"])({
        queries: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createDelegationWithAssignmentsQueries"])(id)
    });
    const [delegationQuery, employeesQuery, vehiclesQuery] = results;
    // Compute enriched delegation when all data is available
    const enrichedDelegation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!delegationQuery?.data || !employeesQuery?.data || !vehiclesQuery?.data) {
            return;
        }
        try {
            // ✅ PRODUCTION FIX: delegationQuery.data is already transformed by the service layer
            // No need to apply DelegationTransformer.fromApi() again
            const delegation = delegationQuery.data;
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationEnrichment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enrichDelegation"])(delegation, employeesQuery.data, vehiclesQuery.data);
        } catch (error) {
            console.error('Error enriching delegation data:', error);
            throw error;
        }
    }, [
        delegationQuery?.data,
        employeesQuery?.data,
        vehiclesQuery?.data
    ]);
    // ✅ PRODUCTION FIX: Memoize refetch function to prevent infinite re-renders
    const refetch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        delegationQuery?.refetch();
        employeesQuery?.refetch();
        vehiclesQuery?.refetch();
    }, [
        delegationQuery?.refetch,
        employeesQuery?.refetch,
        vehiclesQuery?.refetch
    ]);
    // Return combined state with optimized loading states
    return {
        data: enrichedDelegation,
        error: delegationQuery?.error || employeesQuery?.error || vehiclesQuery?.error,
        isError: delegationQuery?.isError || employeesQuery?.isError || vehiclesQuery?.isError,
        isLoading: delegationQuery?.isLoading || employeesQuery?.isLoading || vehiclesQuery?.isLoading,
        isPending: delegationQuery?.isPending || employeesQuery?.isPending || vehiclesQuery?.isPending,
        refetch
    };
};
const useDelegationEnriched = useDelegationWithAssignments;
const useCreateDelegation = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async (delegationData)=>{
            const apiPayload = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$delegationTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTransformer"].toCreateRequest(delegationData);
            // ✅ PRODUCTION FIX: delegationApiService.create() already applies transformation
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationApiService"].create(apiPayload);
        },
        onError: (err, _delegationData, context)=>{
            if (context?.previousDelegations) {
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all, context.previousDelegations);
            }
            console.error('Failed to create delegation:', err);
            // Invalidate to refetch correct data on error
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
            });
        },
        onMutate: async (delegationData)=>{
            await queryClient.cancelQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
            });
            const previousDelegations = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all);
            queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all, (old = [])=>{
                const tempId = `optimistic-${Date.now()}`;
                const now = new Date().toISOString();
                const optimisticArrivalFlight = delegationData.flightArrivalDetails ? {
                    id: `optimistic-flight-arr-${Date.now()}`,
                    ...delegationData.flightArrivalDetails
                } : null;
                const optimisticDepartureFlight = delegationData.flightDepartureDetails ? {
                    id: `optimistic-flight-dep-${Date.now() + 1}`,
                    ...delegationData.flightDepartureDetails
                } : null;
                const optimisticDelegates = delegationData.delegates?.map((d, index)=>({
                        id: `optimistic-delegate-${tempId}-${index}`,
                        name: d.name,
                        notes: d.notes ?? null,
                        title: d.title
                    })) || [];
                const optimisticDelegation = {
                    arrivalFlight: optimisticArrivalFlight ?? null,
                    createdAt: now,
                    delegates: optimisticDelegates,
                    departureFlight: optimisticDepartureFlight ?? null,
                    drivers: delegationData.drivers?.map((d)=>({
                            createdAt: now,
                            createdBy: null,
                            delegationId: tempId,
                            employeeId: d.employeeId,
                            id: `optimistic-driver-${tempId}-${d.employeeId}`,
                            notes: d.notes ?? null,
                            updatedAt: now
                        })) || [],
                    durationFrom: delegationData.durationFrom,
                    durationTo: delegationData.durationTo,
                    escorts: delegationData.escorts?.map((e)=>({
                            createdAt: now,
                            createdBy: null,
                            delegationId: tempId,
                            employeeId: e.employeeId,
                            id: `optimistic-escort-${tempId}-${e.employeeId}`,
                            notes: e.notes ?? null,
                            updatedAt: now
                        })) || [],
                    eventName: delegationData.eventName,
                    id: tempId,
                    imageUrl: delegationData.imageUrl ?? null,
                    invitationFrom: delegationData.invitationFrom ?? null,
                    invitationTo: delegationData.invitationTo ?? null,
                    location: delegationData.location,
                    notes: delegationData.notes ?? null,
                    status: delegationData.status || 'Planned',
                    statusHistory: [],
                    updatedAt: now,
                    vehicles: delegationData.vehicles?.map((v)=>({
                            assignedDate: v.assignedDate,
                            createdAt: now,
                            createdBy: null,
                            delegationId: tempId,
                            id: `optimistic-vehicle-${tempId}-${v.vehicleId}`,
                            notes: v.notes ?? null,
                            returnDate: v.returnDate ?? null,
                            updatedAt: now,
                            vehicleId: v.vehicleId
                        })) || []
                };
                return [
                    ...old,
                    optimisticDelegation
                ];
            });
            return {
                previousDelegations
            };
        },
        onSettled: ()=>{
            // Invalidate to ensure consistency after success or failure
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
            });
        }
    });
};
const useUpdateDelegation = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async ({ data, id })=>{
            // ✅ PRODUCTION FIX: delegationApiService.update() already applies transformation
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationApiService"].update(id, data);
        },
        onError: (err, variables, context)=>{
            if (context?.previousDelegation) {
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id), context.previousDelegation);
            }
            if (context?.previousDelegationsList) {
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all, context.previousDelegationsList);
            }
            console.error('Failed to update delegation:', err);
            // Invalidate to refetch correct data on error
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id)
            });
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
            });
        },
        onMutate: async ({ data, id })=>{
            // data is UpdateDelegationRequest
            await queryClient.cancelQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
            });
            await queryClient.cancelQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)
            });
            const previousDelegation = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id));
            const previousDelegationsList = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all);
            queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id), (old)=>{
                if (!old) return;
                const now = new Date().toISOString();
                // ✅ PRODUCTION FIX: Use correct field mappings for UpdateDelegationRequest
                const updatedOptimistic = {
                    ...old,
                    // Handle flight details updates
                    arrivalFlight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.flightArrivalDetails === null ? null // Explicitly set to null if requested
                     : data.flightArrivalDetails === undefined ? old.arrivalFlight : {
                        airport: data.flightArrivalDetails.airport || old.arrivalFlight?.airport || '',
                        dateTime: data.flightArrivalDetails.dateTime || old.arrivalFlight?.dateTime || '',
                        flightNumber: data.flightArrivalDetails.flightNumber || old.arrivalFlight?.flightNumber || '',
                        id: old.arrivalFlight?.id || `optimistic-arr-${Date.now()}`,
                        notes: data.flightArrivalDetails.notes ?? old.arrivalFlight?.notes ?? null,
                        terminal: data.flightArrivalDetails.terminal ?? old.arrivalFlight?.terminal ?? null
                    } // Keep old value if not in request
                    ),
                    departureFlight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.flightDepartureDetails === null ? null // Explicitly set to null if requested
                     : data.flightDepartureDetails === undefined ? old.departureFlight : {
                        airport: data.flightDepartureDetails.airport || old.departureFlight?.airport || '',
                        dateTime: data.flightDepartureDetails.dateTime || old.departureFlight?.dateTime || '',
                        flightNumber: data.flightDepartureDetails.flightNumber || old.departureFlight?.flightNumber || '',
                        id: old.departureFlight?.id || `optimistic-dep-${Date.now()}`,
                        notes: data.flightDepartureDetails.notes ?? old.departureFlight?.notes ?? null,
                        terminal: data.flightDepartureDetails.terminal ?? old.departureFlight?.terminal ?? null
                    } // Keep old value if not in request
                    ),
                    durationFrom: data.durationFrom ?? old.durationFrom,
                    durationTo: data.durationTo ?? old.durationTo,
                    // Direct field mappings (no transformation needed)
                    eventName: data.eventName ?? old.eventName,
                    imageUrl: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.imageUrl ?? old.imageUrl),
                    invitationFrom: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.invitationFrom ?? old.invitationFrom),
                    invitationTo: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.invitationTo ?? old.invitationTo),
                    location: data.location ?? old.location,
                    notes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.notes ?? old.notes),
                    status: data.status ?? old.status,
                    updatedAt: now
                };
                return updatedOptimistic;
            });
            queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all, (oldList = [])=>oldList.map((delegation)=>delegation.id === id ? queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)) || delegation : delegation));
            return {
                previousDelegation,
                previousDelegationsList
            };
        },
        onSettled: (_data, _error, variables)=>{
            // Always refetch after error or success
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id)
            });
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
            });
        }
    });
};
const useUpdateDelegationStatus = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async ({ id, status, statusChangeReason })=>{
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationApiService"].updateStatus(id, status, statusChangeReason);
            return response;
        },
        onError: (err, variables, context)=>{
            if (context?.previousDelegation) {
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id), context.previousDelegation);
            }
            console.error('Failed to update delegation status:', err);
        },
        onMutate: async ({ id, status })=>{
            await queryClient.cancelQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)
            });
            const previousDelegation = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id));
            queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id), (old)=>old ? {
                    ...old,
                    status: status
                } : undefined);
            return {
                previousDelegation
            };
        },
        onSettled: (_data, _error, variables)=>{
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id)
            });
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
            });
        }
    });
};
const useManageDelegationFlightDetails = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async ({ flightDetails, id })=>{
            // This service method might need adjustment if flightDetails from form is Omit<FlightDetails, 'id'>
            // For now, assuming it expects full FlightDetails (including ID for existing, or will generate for new)
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationApiService"].manageFlightDetails(id, flightDetails);
            return response;
        },
        onError: (err, variables, context)=>{
            if (context?.previousDelegation) {
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id), context.previousDelegation);
            }
            console.error('Failed to manage delegation flight details:', err);
        },
        onMutate: async ({ flightDetails, id })=>{
            await queryClient.cancelQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)
            });
            const previousDelegation = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id));
            queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id), (old)=>{
                if (!old) return;
                // This optimistic update assumes flightDetails is for arrival.
                // A more robust solution would need to know if it's arrival or departure.
                return {
                    ...old,
                    arrivalFlight: flightDetails
                };
            });
            return {
                previousDelegation
            };
        },
        onSettled: (_data, _error, variables)=>{
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(variables.id)
            });
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
            });
        }
    });
};
const useDeleteDelegation = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async (id)=>{
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationApiService"].delete(id);
            return id;
        },
        onError: (err, _id, context)=>{
            if (context?.previousDelegationsList) {
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all, context.previousDelegationsList);
            }
            console.error('Failed to delete delegation:', err);
        },
        onMutate: async (id)=>{
            await queryClient.cancelQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
            });
            await queryClient.cancelQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)
            });
            const previousDelegationsList = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all);
            queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all, (old = [])=>old.filter((delegation)=>delegation.id !== id));
            queryClient.removeQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].detail(id)
            });
            return {
                previousDelegationsList
            };
        },
        onSettled: ()=>{
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$delegationQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationQueryKeys"].all
            });
        }
    });
};
}}),
"[project]/src/hooks/ui/useNotifications.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Custom hook for notification management using Zustand AppStore
 * @module hooks/useNotifications
 */ __turbopack_context__.s({
    "useNotifications": (()=>useNotifications),
    "useWorkHubNotifications": (()=>useWorkHubNotifications)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/appStore.ts [app-ssr] (ecmascript)");
;
;
const useNotifications = ()=>{
    const addNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.addNotification);
    const removeNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.removeNotification);
    const clearAllNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.clearAllNotifications);
    const unreadCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.unreadNotificationCount);
    /**
   * Show a success notification
   */ const showSuccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        addNotification({
            message,
            type: 'success'
        });
    }, [
        addNotification
    ]);
    /**
   * Show an error notification
   */ const showError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        addNotification({
            message,
            type: 'error'
        });
    }, [
        addNotification
    ]);
    /**
   * Show a warning notification
   */ const showWarning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        addNotification({
            message,
            type: 'warning'
        });
    }, [
        addNotification
    ]);
    /**
   * Show an info notification
   */ const showInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message)=>{
        addNotification({
            message,
            type: 'info'
        });
    }, [
        addNotification
    ]);
    /**
   * Show a notification for API operation results
   */ const showApiResult = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((success, successMessage, errorMessage)=>{
        if (success) {
            showSuccess(successMessage);
        } else {
            showError(errorMessage);
        }
    }, [
        showSuccess,
        showError
    ]);
    /**
   * Show a notification with auto-dismiss after specified time
   */ const showTemporary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((type, message, dismissAfter = 5000)=>{
        addNotification({
            message,
            type
        });
        // Auto-dismiss after specified time
        setTimeout(()=>{
            // Note: This is a simplified approach. In a real implementation,
            // you might want to store the notification ID and remove specifically that one
            const notifications = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().notifications;
            const latestNotification = notifications.at(-1);
            if (latestNotification && latestNotification.message === message) {
                removeNotification(latestNotification.id);
            }
        }, dismissAfter);
    }, [
        addNotification,
        removeNotification
    ]);
    /**
   * Show a loading notification that can be updated
   */ const showLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message = 'Loading...')=>{
        addNotification({
            message,
            type: 'info'
        });
        // Return the notification ID for potential updates
        const notifications = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().notifications;
        return notifications.at(-1)?.id;
    }, [
        addNotification
    ]);
    /**
   * Update a loading notification to success or error
   */ const updateLoadingNotification = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((notificationId, success, message)=>{
        removeNotification(notificationId);
        if (success) {
            showSuccess(message);
        } else {
            showError(message);
        }
    }, [
        removeNotification,
        showSuccess,
        showError
    ]);
    return {
        clearAllNotifications,
        // Store methods
        removeNotification,
        // Advanced methods
        showApiResult,
        showError,
        showInfo,
        showLoading,
        // Basic notification methods
        showSuccess,
        showTemporary,
        showWarning,
        unreadCount,
        updateLoadingNotification
    };
};
const useWorkHubNotifications = ()=>{
    const { clearAllNotifications, removeNotification, showError, showInfo, showSuccess, showWarning, unreadCount } = useNotifications();
    /**
   * Show delegation-related notifications
   */ const showDelegationUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message, actionUrl)=>{
        const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
        addNotification({
            ...actionUrl && {
                actionUrl
            },
            category: 'delegation',
            message,
            type: 'delegation-update'
        });
    }, []);
    /**
   * Show vehicle maintenance notifications
   */ const showVehicleMaintenance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message, actionUrl)=>{
        const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
        addNotification({
            ...actionUrl && {
                actionUrl
            },
            category: 'vehicle',
            message,
            type: 'vehicle-maintenance'
        });
    }, []);
    /**
   * Show task assignment notifications
   */ const showTaskAssigned = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message, actionUrl)=>{
        const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
        addNotification({
            ...actionUrl && {
                actionUrl
            },
            category: 'task',
            message,
            type: 'task-assigned'
        });
    }, []);
    /**
   * Show employee update notifications
   */ const showEmployeeUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((message, actionUrl)=>{
        const addNotification = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"].getState().addNotification;
        addNotification({
            ...actionUrl && {
                actionUrl
            },
            category: 'employee',
            message,
            type: 'employee-update'
        });
    }, []);
    return {
        clearAllNotifications,
        // Management
        removeNotification,
        // WorkHub-specific notifications
        showDelegationUpdate,
        showEmployeeUpdate,
        showError,
        showInfo,
        // Basic notifications
        showSuccess,
        showTaskAssigned,
        showVehicleMaintenance,
        showWarning,
        unreadCount
    };
};
}}),
"[project]/src/lib/stores/queries/useEmployees.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file TanStack Query hooks for Employee-related data.
 * These hooks manage fetching, caching, and mutating employee data,
 * integrating with the EmployeeApiService and EmployeeTransformer.
 * @module stores/queries/useEmployees
 */ __turbopack_context__.s({
    "employeeQueryKeys": (()=>employeeQueryKeys),
    "useCreateEmployee": (()=>useCreateEmployee),
    "useDeleteEmployee": (()=>useDeleteEmployee),
    "useEmployee": (()=>useEmployee),
    "useEmployees": (()=>useEmployees),
    "useEmployeesByRole": (()=>useEmployeesByRole),
    "useUpdateEmployee": (()=>useUpdateEmployee),
    "useUpdateEmployeeAvailabilityStatus": (()=>useUpdateEmployeeAvailabilityStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-ssr] (ecmascript)"); // Adjusted import path
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useNotifications.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/employeeTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/typeHelpers.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>"); // Use centralized service
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
const employeeQueryKeys = {
    all: [
        'employees'
    ],
    detail: (id)=>[
            'employees',
            id
        ]
};
const useEmployees = (options)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...employeeQueryKeys.all
    ], async ()=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].getAll();
        return response.data;
    }, 'employee', {
        staleTime: 5 * 60 * 1000,
        ...options
    });
};
const useEmployeesByRole = (role, options)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])(role ? [
        'employees',
        'role',
        role
    ] : [
        ...employeeQueryKeys.all
    ], async ()=>{
        if (role) {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].getByRole(role);
            return response;
        } else {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].getAll();
            return response.data;
        }
    }, 'employee', {
        staleTime: 5 * 60 * 1000,
        ...options
    });
};
const useEmployee = (id)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...employeeQueryKeys.detail(id)
    ], async ()=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].getById(id);
    }, 'employee', {
        enabled: !!id,
        staleTime: 5 * 60 * 1000
    });
};
const useCreateEmployee = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { showError, showSuccess } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useNotifications"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async (employeeData)=>{
            const request = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeTransformer"].toCreateRequest(employeeData);
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].create(request); // Removed redundant EmployeeTransformer.fromApi
        },
        onError: (err, _newEmployeeData, context)=>{
            if (context?.previousEmployees) {
                queryClient.setQueryData(employeeQueryKeys.all, context.previousEmployees);
            }
            showError(`Failed to create employee: ${err.message || 'Unknown error occurred'}`);
        },
        onMutate: async (newEmployeeData)=>{
            await queryClient.cancelQueries({
                queryKey: employeeQueryKeys.all
            });
            const previousEmployees = queryClient.getQueryData(employeeQueryKeys.all);
            queryClient.setQueryData(employeeQueryKeys.all, (old = [])=>{
                const tempId = 'optimistic-' + Date.now().toString();
                const now = new Date().toISOString();
                const optimisticEmployee = {
                    availability: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.availability),
                    contactEmail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.contactEmail),
                    contactInfo: newEmployeeData.contactInfo,
                    contactMobile: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.contactMobile),
                    contactPhone: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.contactPhone),
                    createdAt: now,
                    currentLocation: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.currentLocation),
                    department: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.department),
                    employeeId: newEmployeeData.employeeId,
                    fullName: newEmployeeData.fullName || newEmployeeData.name,
                    generalAssignments: newEmployeeData.generalAssignments || [],
                    hireDate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.hireDate),
                    id: Number(tempId.replace('optimistic-', '')),
                    name: newEmployeeData.name,
                    notes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.notes),
                    position: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.position),
                    profileImageUrl: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.profileImageUrl),
                    role: newEmployeeData.role,
                    shiftSchedule: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.shiftSchedule),
                    skills: newEmployeeData.skills || [],
                    status: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.status),
                    updatedAt: now,
                    ...newEmployeeData.workingHours !== undefined && {
                        workingHours: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(newEmployeeData.workingHours)
                    }
                };
                return [
                    ...old,
                    optimisticEmployee
                ];
            });
            return {
                previousEmployees
            };
        },
        onSettled: ()=>{
            queryClient.invalidateQueries({
                queryKey: employeeQueryKeys.all
            });
        },
        onSuccess: (data)=>{
            showSuccess(`Employee "${data.name}" has been created successfully!`);
        }
    });
};
const useUpdateEmployee = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async ({ data, id })=>{
            const request = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$employeeTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeTransformer"].toUpdateRequest(data); // Removed cast
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].update(id, request); // Removed redundant EmployeeTransformer.fromApi
        },
        onError: (err, variables, context)=>{
            if (context?.previousEmployee) {
                queryClient.setQueryData(employeeQueryKeys.detail(variables.id), context.previousEmployee);
            }
            if (context?.previousEmployeesList) {
                queryClient.setQueryData(employeeQueryKeys.all, context.previousEmployeesList);
            }
            console.error('Failed to update employee:', err);
        },
        onMutate: async ({ data, id })=>{
            await queryClient.cancelQueries({
                queryKey: employeeQueryKeys.all
            });
            await queryClient.cancelQueries({
                queryKey: employeeQueryKeys.detail(id)
            });
            const previousEmployee = queryClient.getQueryData(employeeQueryKeys.detail(id));
            const previousEmployeesList = queryClient.getQueryData(employeeQueryKeys.all);
            queryClient.setQueryData(employeeQueryKeys.detail(id), (old)=>{
                if (!old) return old;
                const now = new Date().toISOString();
                // Explicitly map updated fields to avoid issues with spread operator on different types
                const updatedOptimistic = {
                    ...old,
                    availability: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.availability === undefined ? old.availability : data.availability),
                    contactEmail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.contactEmail === undefined ? old.contactEmail : data.contactEmail),
                    contactInfo: data.contactInfo ?? old.contactInfo,
                    contactMobile: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.contactMobile === undefined ? old.contactMobile : data.contactMobile),
                    contactPhone: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.contactPhone === undefined ? old.contactPhone : data.contactPhone),
                    currentLocation: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.currentLocation === undefined ? old.currentLocation : data.currentLocation),
                    department: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.department ?? old.department),
                    employeeId: data.employeeId ?? old.employeeId,
                    fullName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.fullName ?? old.fullName),
                    generalAssignments: data.generalAssignments ?? old.generalAssignments,
                    hireDate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.hireDate ?? old.hireDate),
                    name: data.name ?? old.name,
                    notes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.notes === undefined ? old.notes : data.notes),
                    position: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.position ?? old.position),
                    profileImageUrl: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.profileImageUrl === undefined ? old.profileImageUrl : data.profileImageUrl),
                    role: data.role ?? old.role,
                    shiftSchedule: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.shiftSchedule === undefined ? old.shiftSchedule : data.shiftSchedule),
                    skills: data.skills ?? old.skills,
                    status: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.status ?? old.status),
                    updatedAt: now,
                    ...data.workingHours !== undefined && {
                        workingHours: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.workingHours)
                    }
                };
                return updatedOptimistic;
            });
            queryClient.setQueryData(employeeQueryKeys.all, (old = [])=>{
                return old.map((employee)=>{
                    if (employee.id === Number(id)) {
                        const now = new Date().toISOString();
                        return {
                            ...employee,
                            availability: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.availability ?? employee.availability),
                            contactEmail: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.contactEmail ?? employee.contactEmail),
                            contactInfo: data.contactInfo ?? employee.contactInfo,
                            contactMobile: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.contactMobile ?? employee.contactMobile),
                            contactPhone: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.contactPhone ?? employee.contactPhone),
                            currentLocation: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.currentLocation ?? employee.currentLocation),
                            department: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.department ?? employee.department),
                            employeeId: data.employeeId ?? employee.employeeId,
                            fullName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.fullName ?? employee.fullName),
                            generalAssignments: data.generalAssignments ?? employee.generalAssignments,
                            hireDate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.hireDate ?? employee.hireDate),
                            name: data.name ?? employee.name,
                            notes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.notes ?? employee.notes),
                            position: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.position ?? employee.position),
                            profileImageUrl: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.profileImageUrl ?? employee.profileImageUrl),
                            role: data.role ?? employee.role,
                            shiftSchedule: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.shiftSchedule ?? employee.shiftSchedule),
                            skills: data.skills ?? employee.skills,
                            status: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.status ?? employee.status),
                            updatedAt: now,
                            ...data.workingHours !== undefined && {
                                workingHours: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.workingHours)
                            }
                        };
                    }
                    return employee;
                });
            });
            return {
                previousEmployee,
                previousEmployeesList
            };
        },
        onSettled: (_data, _error, variables)=>{
            queryClient.invalidateQueries({
                queryKey: employeeQueryKeys.detail(variables.id)
            });
            queryClient.invalidateQueries({
                queryKey: employeeQueryKeys.all
            });
        }
    });
};
const useDeleteEmployee = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async (id)=>{
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].delete(id);
            return id;
        },
        onError: (err, _id, context)=>{
            if (context?.previousEmployeesList) {
                queryClient.setQueryData(employeeQueryKeys.all, context.previousEmployeesList);
            }
            console.error('Failed to delete employee:', err);
        },
        onMutate: async (id)=>{
            await queryClient.cancelQueries({
                queryKey: employeeQueryKeys.all
            });
            await queryClient.cancelQueries({
                queryKey: employeeQueryKeys.detail(id)
            });
            const previousEmployeesList = queryClient.getQueryData(employeeQueryKeys.all);
            queryClient.setQueryData(employeeQueryKeys.all, (old = [])=>old.filter((employee)=>employee.id !== Number(id)) // Compare number ID
            );
            queryClient.removeQueries({
                queryKey: employeeQueryKeys.detail(id)
            });
            return {
                previousEmployeesList
            };
        },
        onSettled: ()=>{
            queryClient.invalidateQueries({
                queryKey: employeeQueryKeys.all
            });
        }
    });
};
const useUpdateEmployeeAvailabilityStatus = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    // The input 'status' should align with DriverAvailabilityPrisma
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async ({ employeeId, status })=>{
            // employeeApiService.updateAvailabilityStatus now expects DriverAvailabilityPrisma directly
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].updateAvailabilityStatus(employeeId, status);
            return response; // Removed redundant EmployeeTransformer.fromApi
        },
        onError: (err, variables, context)=>{
            if (context?.previousEmployee) {
                queryClient.setQueryData(employeeQueryKeys.detail(variables.employeeId), context.previousEmployee);
            }
            console.error('Failed to update employee availability status:', err);
        },
        onMutate: async ({ employeeId, status })=>{
            await queryClient.cancelQueries({
                queryKey: employeeQueryKeys.detail(employeeId)
            });
            const previousEmployee = queryClient.getQueryData(employeeQueryKeys.detail(employeeId));
            queryClient.setQueryData(employeeQueryKeys.detail(employeeId), (old)=>{
                // Update the 'availability' field in the domain model
                return old ? {
                    ...old,
                    availability: status
                } : old;
            });
            return {
                previousEmployee
            };
        },
        onSettled: (_data, _error, variables)=>{
            queryClient.invalidateQueries({
                queryKey: employeeQueryKeys.detail(variables.employeeId)
            });
            queryClient.invalidateQueries({
                queryKey: employeeQueryKeys.all
            });
        }
    });
};
}}),
"[project]/src/lib/stores/queries/useServiceRecords.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SERVICE_RECORD_QUERY_KEY": (()=>SERVICE_RECORD_QUERY_KEY),
    "useCreateServiceRecord": (()=>useCreateServiceRecord),
    "useDeleteServiceRecord": (()=>useDeleteServiceRecord),
    "useEnrichedServiceRecords": (()=>useEnrichedServiceRecords),
    "useServiceRecord": (()=>useServiceRecord),
    "useUpdateServiceRecord": (()=>useUpdateServiceRecord),
    "useVehicleServiceRecords": (()=>useVehicleServiceRecords)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
;
;
;
const ServiceRecordTransformer = {
    fromApi (apiData) {
        // Handle both regular service records and enriched service records
        const baseRecord = {
            cost: apiData.cost,
            createdAt: apiData.createdAt,
            date: apiData.date,
            employeeId: apiData.employeeId,
            id: apiData.id,
            notes: apiData.notes,
            odometer: apiData.odometer,
            servicePerformed: Array.isArray(apiData.servicePerformed) ? apiData.servicePerformed : [],
            updatedAt: apiData.updatedAt,
            vehicleId: apiData.vehicleId
        };
        // Add enriched fields if available (for EnrichedServiceRecord)
        if (apiData.vehicleMake || apiData.vehicleModel || apiData.vehicleYear) {
            return {
                ...baseRecord,
                vehicleMake: apiData.vehicleMake || 'Unknown',
                vehicleModel: apiData.vehicleModel || 'Unknown',
                vehicleYear: apiData.vehicleYear || new Date().getFullYear(),
                licensePlate: apiData.licensePlate || null,
                employeeName: apiData.employeeName || null
            }; // Cast to handle both ServiceRecord and EnrichedServiceRecord
        }
        return baseRecord;
    },
    toApi: (data)=>data
};
class ServiceRecordApiService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/servicerecords';
    transformer = ServiceRecordTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 5 * 60 * 1000,
            retryAttempts: 3,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            ...config
        });
    }
    async getById(id) {
        return this.executeWithInfrastructure(`${this.endpoint}:${id}`, async ()=>{
            const response = await this.apiClient.get(`${this.endpoint}/${id}`);
            // Apply transformer if available
            const transformedResponse = this.transformer.fromApi ? this.transformer.fromApi(response) : response;
            return transformedResponse;
        });
    }
    // Custom update method for service records, handling vehicleId in path
    async updateRecord(id, vehicleId, data) {
        return this.executeWithInfrastructure(null, async ()=>{
            // Remove vehicleId from data if present to avoid conflicts, use the parameter instead
            const { vehicleId: _, ...updateData } = data;
            const response = await this.apiClient.put(`/vehicles/${vehicleId}/servicerecords/${id}`, updateData // Send only the actual update data
            );
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`)); // Invalidate all service records
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:${id}`)); // Invalidate specific record
            this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`)); // Invalidate vehicle-specific caches
            return this.transformer.fromApi ? this.transformer.fromApi(response) : response;
        });
    }
    // Custom delete method for service records, handling vehicleId in path
    async deleteRecord(id, vehicleId) {
        return this.executeWithInfrastructure(null, async ()=>{
            await this.apiClient.delete(`/vehicles/${vehicleId}/servicerecords/${id}`);
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`)); // Invalidate all service records
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:${id}`)); // Invalidate specific record
            this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`)); // Invalidate vehicle-specific caches
        });
    }
    async getVehicleServiceRecords(vehicleId) {
        return this.executeWithInfrastructure(`vehicles:${vehicleId}:servicerecords`, async ()=>{
            const response = await this.apiClient.get(`/vehicles/${vehicleId}/servicerecords`);
            return response;
        });
    }
    async createVehicleServiceRecord(vehicleId, data) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post(`/vehicles/${vehicleId}/servicerecords`, data);
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidatePattern(new RegExp(`^vehicles:${vehicleId}:`));
            return this.transformer.fromApi ? this.transformer.fromApi(response) : response;
        });
    }
    // Method to fetch enriched service records
    async getAllEnriched() {
        return this.executeWithInfrastructure(`${this.endpoint}:enriched`, async ()=>{
            const response = await this.apiClient.get(`${this.endpoint}/enriched`);
            // Apply transformer to each record
            return response.map((record)=>{
                const transformed = this.transformer.fromApi ? this.transformer.fromApi(record) : record;
                return transformed;
            });
        });
    }
}
;
const serviceRecordApiService = new ServiceRecordApiService(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"]);
const SERVICE_RECORD_QUERY_KEY = 'serviceRecords';
const useServiceRecord = (id, options)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        SERVICE_RECORD_QUERY_KEY,
        id
    ], ()=>serviceRecordApiService.getById(id), 'serviceRecord', {
        enabled: options?.enabled ?? !!id,
        staleTime: 1000 * 60 * 5
    });
};
const useEnrichedServiceRecords = (options)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        SERVICE_RECORD_QUERY_KEY,
        'allEnriched'
    ], ()=>serviceRecordApiService.getAllEnriched(), 'serviceRecord', {
        enabled: options?.enabled ?? true,
        staleTime: 1000 * 60 * 5
    });
};
const useVehicleServiceRecords = (vehicleId, options)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        SERVICE_RECORD_QUERY_KEY,
        'forVehicle',
        vehicleId
    ], ()=>serviceRecordApiService.getVehicleServiceRecords(vehicleId), 'serviceRecord', {
        enabled: options?.enabled ?? true,
        staleTime: 1000 * 60 * 5
    });
};
const useCreateServiceRecord = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async (newRecordData)=>{
            const { vehicleId } = newRecordData;
            return serviceRecordApiService.createVehicleServiceRecord(vehicleId, newRecordData);
        },
        onSuccess: (data, variables)=>{
            queryClient.invalidateQueries({
                queryKey: [
                    SERVICE_RECORD_QUERY_KEY,
                    'allEnriched'
                ]
            });
            queryClient.invalidateQueries({
                queryKey: [
                    SERVICE_RECORD_QUERY_KEY,
                    'forVehicle',
                    variables.vehicleId
                ]
            });
            queryClient.invalidateQueries({
                queryKey: [
                    'vehicle',
                    variables.vehicleId
                ]
            });
        }
    });
};
const useUpdateServiceRecord = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async ({ id, vehicleId, data })=>{
            // Call the custom updateRecord method
            return serviceRecordApiService.updateRecord(id, vehicleId, data);
        },
        onSuccess: (_, variables)=>{
            queryClient.invalidateQueries({
                queryKey: [
                    SERVICE_RECORD_QUERY_KEY,
                    'allEnriched'
                ]
            });
            queryClient.invalidateQueries({
                queryKey: [
                    SERVICE_RECORD_QUERY_KEY,
                    variables.id
                ]
            }); // Invalidate specific record
            // Use variables.vehicleId directly as it's now part of the mutation variables
            queryClient.invalidateQueries({
                queryKey: [
                    SERVICE_RECORD_QUERY_KEY,
                    'forVehicle',
                    variables.vehicleId
                ]
            });
            queryClient.invalidateQueries({
                queryKey: [
                    'vehicle',
                    variables.vehicleId
                ]
            });
        }
    });
};
const useDeleteServiceRecord = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async ({ id, vehicleId })=>{
            // Call the custom deleteRecord method
            return serviceRecordApiService.deleteRecord(id, vehicleId);
        },
        onSuccess: (_, variables)=>{
            // Use variables instead of id for consistency
            queryClient.invalidateQueries({
                queryKey: [
                    SERVICE_RECORD_QUERY_KEY,
                    'allEnriched'
                ]
            });
            queryClient.invalidateQueries({
                queryKey: [
                    SERVICE_RECORD_QUERY_KEY,
                    variables.id
                ]
            });
            // Invalidate all vehicle-specific caches since we don't know which vehicle this record belonged to
            queryClient.invalidateQueries({
                queryKey: [
                    SERVICE_RECORD_QUERY_KEY,
                    'forVehicle'
                ]
            });
            queryClient.invalidateQueries({
                queryKey: [
                    'vehicle'
                ]
            });
        }
    });
};
}}),
"[project]/src/lib/transformers/taskEnrichment.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Task enrichment transformer following established patterns
 * @description Handles the enrichment of task data with employee and vehicle details
 * @module transformers/taskEnrichment
 */ __turbopack_context__.s({
    "TaskEnrichmentTransformer": (()=>TaskEnrichmentTransformer),
    "enrichTask": (()=>enrichTask)
});
class TaskEnrichmentTransformer {
    /**
   * Main enrichment method that combines task data with employee and vehicle details
   * @param task - Base task data
   * @param employees - Array of employees
   * @param vehicles - Array of vehicles
   * @returns Fully enriched task
   */ static enrich(task, employees, vehicles) {
        const { employeeMap, vehicleMap } = this.createLookupMaps(employees, vehicles);
        // Apply all enrichments sequentially
        let enrichedTask = this.enrichStaffEmployee(task, employeeMap);
        enrichedTask = this.enrichDriverEmployee(enrichedTask, employeeMap);
        enrichedTask = this.enrichVehicle(enrichedTask, vehicleMap);
        return enrichedTask;
    }
    /**
   * Creates optimized lookup maps for O(1) performance
   * @param employees - Array of employees
   * @param vehicles - Array of vehicles
   * @returns Object containing employee and vehicle maps
   */ static createLookupMaps(employees, vehicles) {
        // Defensive programming: Ensure inputs are arrays
        const safeEmployees = Array.isArray(employees) ? employees : [];
        const safeVehicles = Array.isArray(vehicles) ? vehicles : [];
        return {
            employeeMap: new Map(safeEmployees.map((emp)=>[
                    emp.id,
                    emp
                ])),
            vehicleMap: new Map(safeVehicles.map((veh)=>[
                    veh.id,
                    veh
                ]))
        };
    }
    /**
   * Enriches driver employee assignment with employee details
   * @param task - Base task data
   * @param employeeMap - Map of employees for O(1) lookup
   * @returns Task with enriched driver employee data
   */ static enrichDriverEmployee(task, employeeMap) {
        if (!task.driverEmployeeId) {
            return task;
        }
        const driverEmployee = task.driverEmployee ?? employeeMap.get(task.driverEmployeeId) ?? null;
        return {
            ...task,
            driverEmployee
        };
    }
    /**
   * Enriches staff employee assignment with employee details
   * @param task - Base task data
   * @param employeeMap - Map of employees for O(1) lookup
   * @returns Task with enriched staff employee data
   */ static enrichStaffEmployee(task, employeeMap) {
        if (!task.staffEmployeeId) {
            return task;
        }
        const staffEmployee = task.staffEmployee ?? employeeMap.get(task.staffEmployeeId) ?? null;
        return {
            ...task,
            staffEmployee
        };
    }
    /**
   * Enriches vehicle assignment with vehicle details
   * @param task - Base task data
   * @param vehicleMap - Map of vehicles for O(1) lookup
   * @returns Task with enriched vehicle data
   */ static enrichVehicle(task, vehicleMap) {
        if (!task.vehicleId) {
            return task;
        }
        const vehicle = task.vehicle ?? vehicleMap.get(task.vehicleId) ?? null;
        return {
            ...task,
            vehicle
        };
    }
}
const enrichTask = (task, employees, vehicles)=>{
    return TaskEnrichmentTransformer.enrich(task, employees, vehicles);
};
}}),
"[project]/src/lib/stores/queries/taskQueries.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Task query configurations following Single Responsibility Principle
 * @description Centralized query configurations for task-related data fetching
 */ __turbopack_context__.s({
    "createEmployeesQuery": (()=>createEmployeesQuery),
    "createTaskQuery": (()=>createTaskQuery),
    "createTaskWithAssignmentsQueries": (()=>createTaskWithAssignmentsQueries),
    "createVehiclesQuery": (()=>createVehiclesQuery),
    "taskQueryKeys": (()=>taskQueryKeys),
    "taskQueryOptions": (()=>taskQueryOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
;
const taskQueryKeys = {
    all: [
        'tasks'
    ],
    detail: (id)=>[
            'tasks',
            id
        ],
    withAssignments: (id)=>[
            'tasks',
            id,
            'with-assignments'
        ]
};
const createTaskQuery = (id)=>({
        enabled: !!id,
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskApiService"].getById(id),
        queryKey: taskQueryKeys.detail(id),
        staleTime: 5 * 60 * 1000
    });
const createEmployeesQuery = ()=>({
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"].getAll(),
        queryKey: [
            'employees'
        ],
        staleTime: 10 * 60 * 1000
    });
const createVehiclesQuery = ()=>({
        queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].getAll(),
        queryKey: [
            'vehicles'
        ],
        staleTime: 10 * 60 * 1000
    });
const createTaskWithAssignmentsQueries = (id)=>[
        createTaskQuery(id),
        createEmployeesQuery(),
        createVehiclesQuery()
    ];
const taskQueryOptions = {
    gcTime: 10 * 60 * 1000,
    retry: 3,
    retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30_000),
    staleTime: 5 * 60 * 1000
};
}}),
"[project]/src/lib/stores/queries/useTasks.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file TanStack Query hooks for Task-related data.
 * These hooks manage fetching, caching, and mutating task data,
 * integrating with the TaskApiService and TaskTransformer.
 * @module stores/queries/useTasks
 */ __turbopack_context__.s({
    "useCreateTask": (()=>useCreateTask),
    "useDeleteTask": (()=>useDeleteTask),
    "useTask": (()=>useTask),
    "useTaskEnriched": (()=>useTaskEnriched),
    "useTaskWithAssignments": (()=>useTaskWithAssignments),
    "useTasks": (()=>useTasks),
    "useUpdateTask": (()=>useUpdateTask)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQueries.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-ssr] (ecmascript)"); // Adjusted import path
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>"); // Use centralized service
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskEnrichment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/taskEnrichment.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/taskTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/taskQueries.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/typeHelpers.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
const useTasks = (options)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
    ], async ()=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskApiService"].getAll();
        return response.data;
    }, 'task', {
        staleTime: 0,
        ...options
    });
};
const useTask = (id)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(id)
    ], async ()=>{
        return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskApiService"].getById(id);
    }, 'task', {
        enabled: !!id,
        staleTime: 5 * 60 * 1000
    });
};
const useTaskWithAssignments = (id)=>{
    // Execute all queries in parallel using useQueries for maximum performance
    const results = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueries"])({
        queries: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createTaskWithAssignmentsQueries"])(id)
    });
    const [taskQuery, employeesQuery, vehiclesQuery] = results;
    // Compute enriched task when all data is available
    const enrichedTask = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!taskQuery?.data || !employeesQuery?.data || !vehiclesQuery?.data) {
            return;
        }
        try {
            const task = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskTransformer"].fromApi(taskQuery.data);
            // Defensive programming: Ensure employees and vehicles are arrays
            const employees = Array.isArray(employeesQuery.data) ? employeesQuery.data : [];
            const vehicles = Array.isArray(vehiclesQuery.data) ? vehiclesQuery.data : [];
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskEnrichment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enrichTask"])(task, employees, vehicles);
        } catch (error) {
            console.error('Error enriching task data:', error);
            throw error;
        }
    }, [
        taskQuery?.data,
        employeesQuery?.data,
        vehiclesQuery?.data
    ]);
    // ✅ PRODUCTION FIX: Memoize refetch function to prevent infinite re-renders
    const refetch = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        taskQuery?.refetch();
        employeesQuery?.refetch();
        vehiclesQuery?.refetch();
    }, [
        taskQuery?.refetch,
        employeesQuery?.refetch,
        vehiclesQuery?.refetch
    ]);
    // Return combined state with optimized loading states
    return {
        data: enrichedTask,
        error: taskQuery?.error || employeesQuery?.error || vehiclesQuery?.error,
        isError: taskQuery?.isError || employeesQuery?.isError || vehiclesQuery?.isError,
        isLoading: taskQuery?.isLoading || employeesQuery?.isLoading || vehiclesQuery?.isLoading,
        isPending: taskQuery?.isPending || employeesQuery?.isPending || vehiclesQuery?.isPending,
        refetch
    };
};
const useTaskEnriched = useTaskWithAssignments;
const useCreateTask = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async (taskData)=>{
            const request = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskTransformer"].toCreateRequest(taskData);
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskApiService"].create(request); // Removed redundant TaskTransformer.fromApi
        },
        onError: (err, newTaskData, context)=>{
            if (context?.previousTasks) {
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all, context.previousTasks);
            }
            console.error('Failed to create task:', err);
        },
        onMutate: async (newTaskData)=>{
            await queryClient.cancelQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
            });
            const previousTasks = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all);
            queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all, (old = [])=>{
                const tempId = 'optimistic-' + Date.now().toString();
                const now = new Date().toISOString();
                const optimisticTask = {
                    createdAt: now,
                    dateTime: newTaskData.dateTime ?? null,
                    deadline: newTaskData.deadline ?? null,
                    description: newTaskData.description,
                    driverEmployee: null,
                    driverEmployeeId: newTaskData.driverEmployeeId ?? null,
                    estimatedDuration: newTaskData.estimatedDuration ?? null,
                    id: tempId,
                    location: newTaskData.location ?? null,
                    notes: newTaskData.notes ?? null,
                    priority: newTaskData.priority,
                    requiredSkills: newTaskData.requiredSkills ?? null,
                    staffEmployee: null,
                    staffEmployeeId: newTaskData.staffEmployeeId ?? null,
                    status: newTaskData.status || 'Pending',
                    subtasks: newTaskData.subtasks?.map((s)=>({
                            completed: s.completed || false,
                            id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,
                            taskId: tempId,
                            title: s.title
                        })) || [],
                    updatedAt: now,
                    vehicle: null,
                    vehicleId: newTaskData.vehicleId ?? null
                };
                return [
                    ...old,
                    optimisticTask
                ];
            });
            return {
                previousTasks
            };
        },
        onSettled: ()=>{
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
            });
        }
    });
};
const useUpdateTask = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async ({ data, id })=>{
            const request = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskTransformer"].toUpdateRequest(data); // Removed cast
            return await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskApiService"].update(id, request); // Removed redundant TaskTransformer.fromApi
        },
        onError: (err, variables, context)=>{
            if (context?.previousTask) {
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(variables.id), context.previousTask);
            }
            if (context?.previousTasksList) {
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all, context.previousTasksList);
            }
            console.error('Failed to update task:', err);
        },
        onMutate: async ({ data, id })=>{
            await queryClient.cancelQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
            });
            await queryClient.cancelQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(id)
            });
            const previousTask = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(id));
            const previousTasksList = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all);
            queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(id), (old)=>{
                if (!old) return old;
                const now = new Date().toISOString();
                // Explicitly map updated fields to avoid issues with spread operator on different types
                const updatedOptimistic = {
                    ...old,
                    dateTime: data.dateTime !== undefined ? data.dateTime : old.dateTime,
                    deadline: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.deadline !== undefined ? data.deadline : old.deadline),
                    description: data.description ?? old.description,
                    driverEmployeeId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.driverEmployeeId !== undefined ? data.driverEmployeeId : old.driverEmployeeId),
                    estimatedDuration: data.estimatedDuration !== undefined ? data.estimatedDuration : old.estimatedDuration,
                    location: data.location !== undefined ? data.location : old.location,
                    notes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.notes !== undefined ? data.notes : old.notes),
                    priority: data.priority ?? old.priority,
                    requiredSkills: data.requiredSkills !== undefined ? data.requiredSkills : old.requiredSkills,
                    staffEmployeeId: data.staffEmployeeId !== undefined ? data.staffEmployeeId : old.staffEmployeeId,
                    status: data.status ?? old.status,
                    subtasks: data.subtasks?.map((s)=>({
                            completed: s.completed ?? false,
                            id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,
                            taskId: id,
                            title: s.title
                        })) || old.subtasks || [],
                    updatedAt: now,
                    vehicleId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.vehicleId !== undefined ? data.vehicleId : old.vehicleId)
                };
                return updatedOptimistic;
            });
            queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all, (old = [])=>{
                return old.map((task)=>{
                    if (task.id === id) {
                        const now = new Date().toISOString();
                        const optimisticSubtasks = data.subtasks?.map((s)=>({
                                completed: s.completed ?? false,
                                id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,
                                taskId: id,
                                title: s.title
                            })) || task.subtasks || [];
                        return {
                            ...task,
                            dateTime: data.dateTime !== undefined ? data.dateTime : task.dateTime,
                            deadline: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.deadline !== undefined ? data.deadline : task.deadline),
                            description: data.description ?? task.description,
                            driverEmployeeId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.driverEmployeeId !== undefined ? data.driverEmployeeId : task.driverEmployeeId),
                            estimatedDuration: data.estimatedDuration !== undefined ? data.estimatedDuration : task.estimatedDuration,
                            location: data.location !== undefined ? data.location : task.location,
                            notes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.notes !== undefined ? data.notes : task.notes),
                            priority: data.priority ?? task.priority,
                            requiredSkills: data.requiredSkills !== undefined ? data.requiredSkills : task.requiredSkills,
                            staffEmployeeId: data.staffEmployeeId !== undefined ? data.staffEmployeeId : task.staffEmployeeId,
                            status: data.status ?? task.status,
                            subtasks: optimisticSubtasks,
                            updatedAt: now,
                            vehicleId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["undefinedToNull"])(data.vehicleId !== undefined ? data.vehicleId : task.vehicleId)
                        };
                    }
                    return task;
                });
            });
            return {
                previousTask,
                previousTasksList
            };
        },
        onSettled: (data, error, variables)=>{
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(variables.id)
            });
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
            });
        }
    });
};
const useDeleteTask = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async (id)=>{
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskApiService"].delete(id);
            return id;
        },
        onError: (err, id, context)=>{
            if (context?.previousTasksList) {
                queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all, context.previousTasksList);
            }
            console.error('Failed to delete task:', err);
        },
        onMutate: async (id)=>{
            await queryClient.cancelQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
            });
            await queryClient.cancelQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(id)
            });
            const previousTasksList = queryClient.getQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all);
            queryClient.setQueryData(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all, (old = [])=>old.filter((task)=>task.id !== id));
            queryClient.removeQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].detail(id)
            });
            return {
                previousTasksList
            };
        },
        onSettled: ()=>{
            queryClient.invalidateQueries({
                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskQueryKeys"].all
            });
        }
    });
}; // Removed useAssignTask and useManageSubtasks hooks as their functionality is now
 // handled by the main create/update task mutations.
}}),
"[project]/src/lib/stores/queries/useTasks.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskEnrichment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/taskEnrichment.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$taskTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/taskTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$taskQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/taskQueries.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$typeHelpers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/typeHelpers.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useTasks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useTasks.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/stores/queries/useVehicles.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file TanStack Query hooks for Vehicle-related data.
 * @module stores/queries/useVehicles
 */ __turbopack_context__.s({
    "useCreateVehicle": (()=>useCreateVehicle),
    "useDeleteVehicle": (()=>useDeleteVehicle),
    "useUpdateVehicle": (()=>useUpdateVehicle),
    "useVehicle": (()=>useVehicle),
    "useVehicles": (()=>useVehicles),
    "vehicleQueryKeys": (()=>vehicleQueryKeys)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useSmartQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useNotifications.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/transformers/vehicleTransformer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>"); // Use centralized service from factory
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
;
;
;
;
;
const vehicleQueryKeys = {
    all: [
        'vehicles'
    ],
    detail: (id)=>[
            'vehicles',
            id
        ]
};
const useVehicles = (options)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...vehicleQueryKeys.all
    ], async ()=>{
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].getAll();
        return response.data;
    }, 'vehicle', {
        staleTime: 5 * 60 * 1000,
        ...options
    });
};
const useVehicle = (id, options)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useSmartQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCrudQuery"])([
        ...vehicleQueryKeys.detail(id)
    ], ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].getById(id), 'vehicle', {
        enabled: !!id && (options?.enabled ?? true),
        staleTime: 5 * 60 * 1000,
        ...options
    });
};
const useCreateVehicle = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { showError, showSuccess } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useNotifications"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (newVehicleData)=>{
            const transformedData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleTransformer"].toCreateRequest(newVehicleData);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].create(transformedData);
        },
        onError: (error)=>{
            showError(`Failed to create vehicle: ${error.message || 'Unknown error occurred'}`);
        },
        onSuccess: (data)=>{
            // Invalidate and refetch all vehicles query after a successful creation
            queryClient.invalidateQueries({
                queryKey: vehicleQueryKeys.all
            });
            showSuccess(`Vehicle "${data.licensePlate}" has been created successfully!`);
        }
    });
};
const useUpdateVehicle = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { showError, showSuccess } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useNotifications"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: ({ data, id })=>{
            const transformedData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$transformers$2f$vehicleTransformer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleTransformer"].toUpdateRequest(data);
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].update(id, transformedData);
        },
        onError: (error)=>{
            showError(`Failed to update vehicle: ${error.message || 'Unknown error occurred'}`);
        },
        onSuccess: (updatedVehicle)=>{
            queryClient.invalidateQueries({
                queryKey: vehicleQueryKeys.all
            });
            queryClient.invalidateQueries({
                queryKey: vehicleQueryKeys.detail(updatedVehicle.id)
            });
            showSuccess(`Vehicle "${updatedVehicle.licensePlate}" has been updated successfully!`);
        }
    });
};
const useDeleteVehicle = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const { showError, showSuccess } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useNotifications$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useNotifications"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: (id)=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"].delete(id),
        onError: (error)=>{
            showError(`Failed to delete vehicle: ${error.message || 'Unknown error occurred'}`);
        },
        onSuccess: (_data, id)=>{
            queryClient.invalidateQueries({
                queryKey: vehicleQueryKeys.all
            });
            queryClient.removeQueries({
                queryKey: vehicleQueryKeys.detail(id)
            });
            showSuccess('Vehicle has been deleted successfully!');
        }
    });
};
}}),
"[project]/src/components/dashboard/ModernDashboard.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Modern dashboard component with widgets and insights
 * @module components/dashboard/ModernDashboard
 */ __turbopack_context__.s({
    "ModernDashboard": (()=>ModernDashboard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/activity.js [app-ssr] (ecmascript) <export default as Activity>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-ssr] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-ssr] (ecmascript) <export default as BarChart3>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-ssr] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/car.js [app-ssr] (ecmascript) <export default as Car>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-ssr] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eye.js [app-ssr] (ecmascript) <export default as Eye>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-ssr] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-down.js [app-ssr] (ecmascript) <export default as TrendingDown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-ssr] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-ssr] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wrench$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wrench$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/wrench.js [app-ssr] (ecmascript) <export default as Wrench>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$progress$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/progress.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useDelegations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useDelegations.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useEmployees$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useEmployees.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useServiceRecords$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useServiceRecords.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useTasks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useTasks.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useTasks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useTasks.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useVehicles$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queries/useVehicles.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <locals>");
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const StatCard = ({ title, value, description, icon: Icon, trend, href })=>{
    const CardWrapper = href ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"] : 'div';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(CardWrapper, {
        href: href || '#',
        className: href ? 'block' : '',
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('transition-all duration-200', href && 'hover:shadow-md cursor-pointer'),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "p-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm font-medium text-muted-foreground",
                                    children: title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 98,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-3xl font-bold",
                                    children: value
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 101,
                                    columnNumber: 15
                                }, this),
                                description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs text-muted-foreground mt-1",
                                    children: description
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 103,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 97,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col items-end gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "rounded-md bg-primary/10 p-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                                        className: "size-4 text-primary"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                        lineNumber: 110,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 109,
                                    columnNumber: 15
                                }, this),
                                trend && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('flex items-center text-xs', trend.isPositive ? 'text-green-600' : 'text-red-600'),
                                    children: [
                                        trend.isPositive ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                            className: "size-3 mr-1"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 120,
                                            columnNumber: 21
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$down$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingDown$3e$__["TrendingDown"], {
                                            className: "size-3 mr-1"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 122,
                                            columnNumber: 21
                                        }, this),
                                        Math.abs(trend.value),
                                        "%"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 113,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 108,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                    lineNumber: 96,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 95,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
            lineNumber: 89,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, this);
};
const QuickAction = ({ title, description, icon: Icon, href, variant = 'outline' })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
        className: "hover:shadow-md transition-all duration-200",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
            className: "p-4",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-start gap-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "rounded-md bg-primary/10 p-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                            className: "size-4 text-primary"
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 147,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 146,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 min-w-0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "text-sm font-medium",
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 150,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-muted-foreground mt-1",
                                children: description
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 151,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                asChild: true,
                                variant: variant,
                                size: "sm",
                                className: "mt-3 w-full",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    href: href,
                                    children: "Get Started"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 153,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 152,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 149,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 145,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
            lineNumber: 144,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
        lineNumber: 143,
        columnNumber: 5
    }, this);
};
const ModernDashboard = ()=>{
    const { user, isInitialized, loading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthContext"])();
    // Only make API calls when user is authenticated and auth system is ready
    // MEMOIZED to prevent infinite re-renders caused by user object reference changes
    const isAuthReady = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return isInitialized && !loading && !!user;
    }, [
        isInitialized,
        loading,
        user?.id
    ]); // Use user.id instead of user object to prevent reference issues
    const { data: vehicles = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useVehicles$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useVehicles"])({
        enabled: isAuthReady
    });
    const { data: delegations = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useDelegations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDelegations"])({
        enabled: isAuthReady
    });
    const { data: tasks = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useTasks$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTasks"])({
        enabled: isAuthReady
    });
    const { data: employees = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useEmployees$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEmployees"])({
        enabled: isAuthReady
    });
    const { data: serviceRecords = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queries$2f$useServiceRecords$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEnrichedServiceRecords"])({
        enabled: isAuthReady
    });
    // Show loading state while authentication is initializing
    if (!isInitialized || loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 184,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-4 text-sm text-muted-foreground",
                        children: "Initializing..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 185,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 183,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
            lineNumber: 182,
            columnNumber: 7
        }, this);
    }
    // Show login prompt if not authenticated
    if (!user) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center min-h-screen",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                className: "w-full max-w-md",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                children: "Welcome to WorkHub"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 197,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardDescription"], {
                                children: "Please sign in to access your dashboard"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 198,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 196,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                            asChild: true,
                            className: "w-full",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                href: "/login",
                                children: "Sign In"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 204,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 203,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 202,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 195,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
            lineNumber: 194,
            columnNumber: 7
        }, this);
    }
    // Calculate statistics
    const stats = {
        totalVehicles: vehicles.length,
        activeDelegations: delegations.filter((d)=>d.status === 'In_Progress').length,
        pendingTasks: tasks.filter((t)=>t.status === 'Assigned' || t.status === 'In_Progress').length,
        maintenancesDue: serviceRecords.filter((r)=>{
            // This is a simplified check - you might want more sophisticated logic
            // Simplified maintenance due logic
            return Math.random() > 0.8; // Mock 20% of records as needing maintenance
        }).length,
        teamMembers: employees.length
    };
    // Mock recent activity - you'd fetch this from your API
    const recentActivity = [
        {
            id: '1',
            title: 'Vehicle VIN-123 maintenance completed',
            description: 'Oil change and tire rotation finished',
            timestamp: '2 hours ago',
            type: 'maintenance'
        },
        {
            id: '2',
            title: 'New task assigned: Fleet inspection',
            description: 'Quarterly safety inspection due next week',
            timestamp: '4 hours ago',
            type: 'task',
            priority: 'high'
        },
        {
            id: '3',
            title: 'Project Alpha milestone completed',
            description: 'Phase 2 deliverables submitted',
            timestamp: '1 day ago',
            type: 'delegation'
        }
    ];
    const quickActions = [
        {
            title: 'Add New Vehicle',
            description: 'Register a new asset to your fleet',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"],
            href: '/add-vehicle',
            variant: 'default'
        },
        {
            title: 'Schedule Maintenance',
            description: 'Plan upcoming service appointments',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"],
            href: '/service-records'
        },
        {
            title: 'View Analytics',
            description: 'See detailed reports and insights',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"],
            href: '/reports'
        },
        {
            title: 'Assign Task',
            description: 'Create and delegate new tasks',
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"],
            href: '/tasks'
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-8",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-3xl font-bold tracking-tight",
                        children: [
                            "Welcome back",
                            user?.user_metadata?.full_name ? `, ${user.user_metadata.full_name}` : '',
                            "!"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 286,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-muted-foreground",
                        children: "Here's what's happening with your operations today."
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 293,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 285,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid gap-4 md:grid-cols-2 lg:grid-cols-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(StatCard, {
                        title: "Total Vehicles",
                        value: stats.totalVehicles,
                        description: "Active fleet assets",
                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__["Car"],
                        href: "/vehicles",
                        trend: {
                            value: 5.2,
                            isPositive: true
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 300,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(StatCard, {
                        title: "Active Projects",
                        value: stats.activeDelegations,
                        description: "In progress delegations",
                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$activity$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Activity$3e$__["Activity"],
                        href: "/delegations"
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 308,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(StatCard, {
                        title: "Pending Tasks",
                        value: stats.pendingTasks,
                        description: "Awaiting completion",
                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"],
                        href: "/tasks",
                        trend: {
                            value: 2.1,
                            isPositive: false
                        }
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 315,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(StatCard, {
                        title: "Maintenance Due",
                        value: stats.maintenancesDue,
                        description: "Requires attention",
                        icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"],
                        href: "/service-history"
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 323,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 299,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid gap-6 lg:grid-cols-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "lg:col-span-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                    className: "flex flex-row items-center justify-between",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                                    children: "Recent Activity"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 338,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardDescription"], {
                                                    children: "Latest updates from your operations"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 339,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 337,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "outline",
                                            size: "sm",
                                            asChild: true,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/activity",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                                        className: "size-4 mr-2"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                        lineNumber: 345,
                                                        columnNumber: 19
                                                    }, this),
                                                    "View All"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                lineNumber: 344,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 343,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 336,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-4",
                                        children: recentActivity.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-start gap-3 p-3 rounded-lg bg-muted/30",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "rounded-full bg-primary/10 p-1",
                                                        children: [
                                                            item.type === 'maintenance' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$wrench$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Wrench$3e$__["Wrench"], {
                                                                className: "size-3 text-primary"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 359,
                                                                columnNumber: 25
                                                            }, this),
                                                            item.type === 'task' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                                                className: "size-3 text-primary"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 362,
                                                                columnNumber: 25
                                                            }, this),
                                                            item.type === 'delegation' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                                                                className: "size-3 text-primary"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 365,
                                                                columnNumber: 25
                                                            }, this),
                                                            item.type === 'vehicle' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__["Car"], {
                                                                className: "size-3 text-primary"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 368,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                        lineNumber: 357,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex-1 min-w-0",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center gap-2",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                        className: "text-sm font-medium",
                                                                        children: item.title
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                        lineNumber: 373,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    item.priority && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                                                        variant: item.priority === 'high' ? 'destructive' : 'secondary',
                                                                        className: "text-xs",
                                                                        children: item.priority
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                        lineNumber: 375,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 372,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-xs text-muted-foreground",
                                                                children: item.description
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 387,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-xs text-muted-foreground mt-1",
                                                                children: item.timestamp
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                                lineNumber: 390,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                        lineNumber: 371,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, item.id, true, {
                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                lineNumber: 353,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                        lineNumber: 351,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 350,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 335,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 334,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                            children: "Quick Actions"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 405,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardDescription"], {
                                            children: "Common tasks and shortcuts"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 406,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 404,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-3",
                                        children: quickActions.map((action, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(QuickAction, {
                                                ...action
                                            }, index, false, {
                                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                lineNumber: 411,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                        lineNumber: 409,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 408,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 403,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 402,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 332,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                children: "Fleet Performance"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 422,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardDescription"], {
                                children: "Overview of fleet efficiency and maintenance status"
                            }, void 0, false, {
                                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                lineNumber: 423,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 421,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid gap-6 md:grid-cols-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between text-sm",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-muted-foreground",
                                                    children: "Fleet Utilization"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 431,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: "87%"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 432,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 430,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$progress$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Progress"], {
                                            value: 87,
                                            className: "h-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 434,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 429,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between text-sm",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-muted-foreground",
                                                    children: "Maintenance Up-to-date"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 438,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: "92%"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 441,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 437,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$progress$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Progress"], {
                                            value: 92,
                                            className: "h-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 443,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 436,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center justify-between text-sm",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-muted-foreground",
                                                    children: "Task Completion Rate"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 447,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "font-medium",
                                                    children: "78%"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                                    lineNumber: 450,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 446,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$progress$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Progress"], {
                                            value: 78,
                                            className: "h-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                            lineNumber: 452,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                                    lineNumber: 445,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                            lineNumber: 428,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                        lineNumber: 427,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
                lineNumber: 420,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/dashboard/ModernDashboard.tsx",
        lineNumber: 283,
        columnNumber: 5
    }, this);
};
}}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DashboardPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$ModernDashboard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/ModernDashboard.tsx [app-ssr] (ecmascript)");
'use client';
;
;
// Set page metadata
if (typeof document !== 'undefined') {
    document.title = 'Dashboard - WorkHub';
}
function DashboardPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$ModernDashboard$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModernDashboard"], {}, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 11,
        columnNumber: 10
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__6545b084._.js.map