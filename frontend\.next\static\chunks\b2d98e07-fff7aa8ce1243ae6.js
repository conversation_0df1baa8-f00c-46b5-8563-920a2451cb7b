"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4792],{91765:(e,t,r)=>{let i,n,a,o,s,c;r.d(t,{Ay:()=>nz,Kc:()=>nb});var u,l,h,d,f,m,g,p,y,v,A,b,_,w,T,S,x,O,k=r(5635),E=r(61354),R=r(37374),C=r(23192),U=r(28261),D=r(10530),P=r(84225),F=r(67907),L=r(26475),M=r(32109),Y="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},I=[],N=[],B="undefined"!=typeof Uint8Array?Uint8Array:Array,W=!1;function z(){W=!0;for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t=0,r=e.length;t<r;++t)I[t]=e[t],N[e.charCodeAt(t)]=t;N[45]=62,N[95]=63}function j(e){W||z();for(var t,r=e.length,i=r%3,n="",a=[],o=0,s=r-i;o<s;o+=16383)a.push(function(e,t,r){for(var i,n=[],a=t;a<r;a+=3)i=(e[a]<<16)+(e[a+1]<<8)+e[a+2],n.push(I[i>>18&63]+I[i>>12&63]+I[i>>6&63]+I[63&i]);return n.join("")}(e,o,o+16383>s?s:o+16383));return 1===i?(n+=I[(t=e[r-1])>>2],n+=I[t<<4&63],n+="=="):2===i&&(n+=I[(t=(e[r-2]<<8)+e[r-1])>>10],n+=I[t>>4&63],n+=I[t<<2&63],n+="="),a.push(n),a.join("")}function q(e,t,r,i,n){var a,o,s=8*n-i-1,c=(1<<s)-1,u=c>>1,l=-7,h=r?n-1:0,d=r?-1:1,f=e[t+h];for(h+=d,a=f&(1<<-l)-1,f>>=-l,l+=s;l>0;a=256*a+e[t+h],h+=d,l-=8);for(o=a&(1<<-l)-1,a>>=-l,l+=i;l>0;o=256*o+e[t+h],h+=d,l-=8);if(0===a)a=1-u;else{if(a===c)return o?NaN:1/0*(f?-1:1);o+=Math.pow(2,i),a-=u}return(f?-1:1)*o*Math.pow(2,a-i)}function V(e,t,r,i,n,a){var o,s,c,u=8*a-n-1,l=(1<<u)-1,h=l>>1,d=5960464477539062e-23*(23===n),f=i?0:a-1,m=i?1:-1,g=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(s=+!!isNaN(t),o=l):(o=Math.floor(Math.log(t)/Math.LN2),t*(c=Math.pow(2,-o))<1&&(o--,c*=2),o+h>=1?t+=d/c:t+=d*Math.pow(2,1-h),t*c>=2&&(o++,c/=2),o+h>=l?(s=0,o=l):o+h>=1?(s=(t*c-1)*Math.pow(2,n),o+=h):(s=t*Math.pow(2,h-1)*Math.pow(2,n),o=0));n>=8;e[r+f]=255&s,f+=m,s/=256,n-=8);for(o=o<<n|s,u+=n;u>0;e[r+f]=255&o,f+=m,o/=256,u-=8);e[r+f-m]|=128*g}var K={}.toString,G=Array.isArray||function(e){return"[object Array]"==K.call(e)};X.TYPED_ARRAY_SUPPORT=void 0===Y.TYPED_ARRAY_SUPPORT||Y.TYPED_ARRAY_SUPPORT;var H=$();function $(){return X.TYPED_ARRAY_SUPPORT?0x7fffffff:0x3fffffff}function Z(e,t){if($()<t)throw RangeError("Invalid typed array length");return X.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=X.prototype:(null===e&&(e=new X(t)),e.length=t),e}function X(e,t,r){if(!X.TYPED_ARRAY_SUPPORT&&!(this instanceof X))return new X(e,t,r);if("number"==typeof e){if("string"==typeof t)throw Error("If encoding is specified then the first argument must be a string");return ee(this,e)}return J(this,e,t,r)}function J(e,t,r,i){if("number"==typeof t)throw TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,i){if(t.byteLength,r<0||t.byteLength<r)throw RangeError("'offset' is out of bounds");if(t.byteLength<r+(i||0))throw RangeError("'length' is out of bounds");return t=void 0===r&&void 0===i?new Uint8Array(t):void 0===i?new Uint8Array(t,r):new Uint8Array(t,r,i),X.TYPED_ARRAY_SUPPORT?(e=t).__proto__=X.prototype:e=et(e,t),e}(e,t,r,i):"string"==typeof t?function(e,t,r){if(("string"!=typeof r||""===r)&&(r="utf8"),!X.isEncoding(r))throw TypeError('"encoding" must be a valid string encoding');var i=0|en(t,r),n=(e=Z(e,i)).write(t,r);return n!==i&&(e=e.slice(0,n)),e}(e,t,r):function(e,t){if(ei(t)){var r,i=0|er(t.length);return 0===(e=Z(e,i)).length||t.copy(e,0,0,i),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t){return"number"!=typeof t.length||(r=t.length)!=r?Z(e,0):et(e,t)}if("Buffer"===t.type&&G(t.data))return et(e,t.data)}throw TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function Q(e){if("number"!=typeof e)throw TypeError('"size" argument must be a number');if(e<0)throw RangeError('"size" argument must not be negative')}function ee(e,t){if(Q(t),e=Z(e,t<0?0:0|er(t)),!X.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function et(e,t){var r=t.length<0?0:0|er(t.length);e=Z(e,r);for(var i=0;i<r;i+=1)e[i]=255&t[i];return e}function er(e){if(e>=$())throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+$().toString(16)+" bytes");return 0|e}function ei(e){return!!(null!=e&&e._isBuffer)}function en(e,t){if(ei(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return ev(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return eb(e).length;default:if(i)return ev(e).length;t=(""+t).toLowerCase(),i=!0}}function ea(e,t,r){var i,n,a,o=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var i,n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var a="",o=t;o<r;++o){a+=(i=e[o])<16?"0"+i.toString(16):i.toString(16)}return a}(this,t,r);case"utf8":case"utf-8":return eu(this,t,r);case"ascii":return function(e,t,r){var i="";r=Math.min(e.length,r);for(var n=t;n<r;++n)i+=String.fromCharCode(127&e[n]);return i}(this,t,r);case"latin1":case"binary":return function(e,t,r){var i="";r=Math.min(e.length,r);for(var n=t;n<r;++n)i+=String.fromCharCode(e[n]);return i}(this,t,r);case"base64":return i=this,n=t,a=r,0===n&&a===i.length?j(i):j(i.slice(n,a));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var i=e.slice(t,r),n="",a=0;a<i.length;a+=2)n+=String.fromCharCode(i[a]+256*i[a+1]);return n}(this,t,r);default:if(o)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),o=!0}}function eo(e,t,r){var i=e[t];e[t]=e[r],e[r]=i}function es(e,t,r,i,n){if(0===e.length)return -1;if("string"==typeof r?(i=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),isNaN(r*=1)&&(r=n?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(n)return -1;else r=e.length-1;else if(r<0)if(!n)return -1;else r=0;if("string"==typeof t&&(t=X.from(t,i)),ei(t))return 0===t.length?-1:ec(e,t,r,i,n);if("number"==typeof t){if(t&=255,X.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf)if(n)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return ec(e,[t],r,i,n)}throw TypeError("val must be string, number or Buffer")}function ec(e,t,r,i,n){var a,o=1,s=e.length,c=t.length;if(void 0!==i&&("ucs2"===(i=String(i).toLowerCase())||"ucs-2"===i||"utf16le"===i||"utf-16le"===i)){if(e.length<2||t.length<2)return -1;o=2,s/=2,c/=2,r/=2}function u(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(n){var l=-1;for(a=r;a<s;a++)if(u(e,a)===u(t,-1===l?0:a-l)){if(-1===l&&(l=a),a-l+1===c)return l*o}else -1!==l&&(a-=a-l),l=-1}else for(r+c>s&&(r=s-c),a=r;a>=0;a--){for(var h=!0,d=0;d<c;d++)if(u(e,a+d)!==u(t,d)){h=!1;break}if(h)return a}return -1}X.poolSize=8192,X._augment=function(e){return e.__proto__=X.prototype,e},X.from=function(e,t,r){return J(null,e,t,r)},X.TYPED_ARRAY_SUPPORT&&(X.prototype.__proto__=Uint8Array.prototype,X.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&X[Symbol.species]),X.alloc=function(e,t,r){return(Q(e),e<=0)?Z(null,e):void 0!==t?"string"==typeof r?Z(null,e).fill(t,r):Z(null,e).fill(t):Z(null,e)},X.allocUnsafe=function(e){return ee(null,e)},X.allocUnsafeSlow=function(e){return ee(null,e)},X.isBuffer=ew,X.compare=function(e,t){if(!ei(e)||!ei(t))throw TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,i=t.length,n=0,a=Math.min(r,i);n<a;++n)if(e[n]!==t[n]){r=e[n],i=t[n];break}return r<i?-1:+(i<r)},X.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},X.concat=function(e,t){if(!G(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return X.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,i=X.allocUnsafe(t),n=0;for(r=0;r<e.length;++r){var a=e[r];if(!ei(a))throw TypeError('"list" argument must be an Array of Buffers');a.copy(i,n),n+=a.length}return i},X.byteLength=en,X.prototype._isBuffer=!0,X.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)eo(this,t,t+1);return this},X.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)eo(this,t,t+3),eo(this,t+1,t+2);return this},X.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)eo(this,t,t+7),eo(this,t+1,t+6),eo(this,t+2,t+5),eo(this,t+3,t+4);return this},X.prototype.toString=function(){var e=0|this.length;return 0===e?"":0==arguments.length?eu(this,0,e):ea.apply(this,arguments)},X.prototype.equals=function(e){if(!ei(e))throw TypeError("Argument must be a Buffer");return this===e||0===X.compare(this,e)},X.prototype.inspect=function(){var e="";return this.length>0&&(e=this.toString("hex",0,50).match(/.{2}/g).join(" "),this.length>50&&(e+=" ... ")),"<Buffer "+e+">"},X.prototype.compare=function(e,t,r,i,n){if(!ei(e))throw TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===i&&(i=0),void 0===n&&(n=this.length),t<0||r>e.length||i<0||n>this.length)throw RangeError("out of range index");if(i>=n&&t>=r)return 0;if(i>=n)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,i>>>=0,n>>>=0,this===e)return 0;for(var a=n-i,o=r-t,s=Math.min(a,o),c=this.slice(i,n),u=e.slice(t,r),l=0;l<s;++l)if(c[l]!==u[l]){a=c[l],o=u[l];break}return a<o?-1:+(o<a)},X.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},X.prototype.indexOf=function(e,t,r){return es(this,e,t,r,!0)},X.prototype.lastIndexOf=function(e,t,r){return es(this,e,t,r,!1)};function eu(e,t,r){r=Math.min(e.length,r);for(var i=[],n=t;n<r;){var a,o,s,c,u=e[n],l=null,h=u>239?4:u>223?3:u>191?2:1;if(n+h<=r)switch(h){case 1:u<128&&(l=u);break;case 2:(192&(a=e[n+1]))==128&&(c=(31&u)<<6|63&a)>127&&(l=c);break;case 3:a=e[n+1],o=e[n+2],(192&a)==128&&(192&o)==128&&(c=(15&u)<<12|(63&a)<<6|63&o)>2047&&(c<55296||c>57343)&&(l=c);break;case 4:a=e[n+1],o=e[n+2],s=e[n+3],(192&a)==128&&(192&o)==128&&(192&s)==128&&(c=(15&u)<<18|(63&a)<<12|(63&o)<<6|63&s)>65535&&c<1114112&&(l=c)}null===l?(l=65533,h=1):l>65535&&(l-=65536,i.push(l>>>10&1023|55296),l=56320|1023&l),i.push(l),n+=h}var d=i,f=d.length;if(f<=4096)return String.fromCharCode.apply(String,d);for(var m="",g=0;g<f;)m+=String.fromCharCode.apply(String,d.slice(g,g+=4096));return m}function el(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function eh(e,t,r,i,n,a){if(!ei(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>n||t<a)throw RangeError('"value" argument is out of bounds');if(r+i>e.length)throw RangeError("Index out of range")}function ed(e,t,r,i){t<0&&(t=65535+t+1);for(var n=0,a=Math.min(e.length-r,2);n<a;++n)e[r+n]=(t&255<<8*(i?n:1-n))>>>(i?n:1-n)*8}function ef(e,t,r,i){t<0&&(t=0xffffffff+t+1);for(var n=0,a=Math.min(e.length-r,4);n<a;++n)e[r+n]=t>>>(i?n:3-n)*8&255}function em(e,t,r,i,n,a){if(r+i>e.length||r<0)throw RangeError("Index out of range")}function eg(e,t,r,i,n){return n||em(e,t,r,4),V(e,t,r,i,23,4),r+4}function ep(e,t,r,i,n){return n||em(e,t,r,8),V(e,t,r,i,52,8),r+8}X.prototype.write=function(e,t,r,i){if(void 0===t)i="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)i=t,r=this.length,t=0;else if(isFinite(t))t|=0,isFinite(r)?(r|=0,void 0===i&&(i="utf8")):(i=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var n,a,o,s,c,u,l,h,d=this.length-t;if((void 0===r||r>d)&&(r=d),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");i||(i="utf8");for(var f=!1;;)switch(i){case"hex":return function(e,t,r,i){r=Number(r)||0;var n=e.length-r;i?(i=Number(i))>n&&(i=n):i=n;var a=t.length;if(a%2!=0)throw TypeError("Invalid hex string");i>a/2&&(i=a/2);for(var o=0;o<i;++o){var s=parseInt(t.substr(2*o,2),16);if(isNaN(s))break;e[r+o]=s}return o}(this,e,t,r);case"utf8":case"utf-8":return n=t,a=r,e_(ev(e,this.length-n),this,n,a);case"ascii":return o=t,s=r,e_(eA(e),this,o,s);case"latin1":case"binary":return function(e,t,r,i){return e_(eA(t),e,r,i)}(this,e,t,r);case"base64":return c=t,u=r,e_(eb(e),this,c,u);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return l=t,h=r,e_(function(e,t){for(var r,i,n=[],a=0;a<e.length&&!((t-=2)<0);++a)i=(r=e.charCodeAt(a))>>8,n.push(r%256),n.push(i);return n}(e,this.length-l),this,l,h);default:if(f)throw TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),f=!0}},X.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},X.prototype.slice=function(e,t){var r,i=this.length;if(e=~~e,t=void 0===t?i:~~t,e<0?(e+=i)<0&&(e=0):e>i&&(e=i),t<0?(t+=i)<0&&(t=0):t>i&&(t=i),t<e&&(t=e),X.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=X.prototype;else{var n=t-e;r=new X(n,void 0);for(var a=0;a<n;++a)r[a]=this[a+e]}return r},X.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||el(e,t,this.length);for(var i=this[e],n=1,a=0;++a<t&&(n*=256);)i+=this[e+a]*n;return i},X.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||el(e,t,this.length);for(var i=this[e+--t],n=1;t>0&&(n*=256);)i+=this[e+--t]*n;return i},X.prototype.readUInt8=function(e,t){return t||el(e,1,this.length),this[e]},X.prototype.readUInt16LE=function(e,t){return t||el(e,2,this.length),this[e]|this[e+1]<<8},X.prototype.readUInt16BE=function(e,t){return t||el(e,2,this.length),this[e]<<8|this[e+1]},X.prototype.readUInt32LE=function(e,t){return t||el(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},X.prototype.readUInt32BE=function(e,t){return t||el(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},X.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||el(e,t,this.length);for(var i=this[e],n=1,a=0;++a<t&&(n*=256);)i+=this[e+a]*n;return i>=(n*=128)&&(i-=Math.pow(2,8*t)),i},X.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||el(e,t,this.length);for(var i=t,n=1,a=this[e+--i];i>0&&(n*=256);)a+=this[e+--i]*n;return a>=(n*=128)&&(a-=Math.pow(2,8*t)),a},X.prototype.readInt8=function(e,t){return(t||el(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},X.prototype.readInt16LE=function(e,t){t||el(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},X.prototype.readInt16BE=function(e,t){t||el(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},X.prototype.readInt32LE=function(e,t){return t||el(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},X.prototype.readInt32BE=function(e,t){return t||el(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},X.prototype.readFloatLE=function(e,t){return t||el(e,4,this.length),q(this,e,!0,23,4)},X.prototype.readFloatBE=function(e,t){return t||el(e,4,this.length),q(this,e,!1,23,4)},X.prototype.readDoubleLE=function(e,t){return t||el(e,8,this.length),q(this,e,!0,52,8)},X.prototype.readDoubleBE=function(e,t){return t||el(e,8,this.length),q(this,e,!1,52,8)},X.prototype.writeUIntLE=function(e,t,r,i){if(e*=1,t|=0,r|=0,!i){var n=Math.pow(2,8*r)-1;eh(this,e,t,r,n,0)}var a=1,o=0;for(this[t]=255&e;++o<r&&(a*=256);)this[t+o]=e/a&255;return t+r},X.prototype.writeUIntBE=function(e,t,r,i){if(e*=1,t|=0,r|=0,!i){var n=Math.pow(2,8*r)-1;eh(this,e,t,r,n,0)}var a=r-1,o=1;for(this[t+a]=255&e;--a>=0&&(o*=256);)this[t+a]=e/o&255;return t+r},X.prototype.writeUInt8=function(e,t,r){return e*=1,t|=0,r||eh(this,e,t,1,255,0),X.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},X.prototype.writeUInt16LE=function(e,t,r){return e*=1,t|=0,r||eh(this,e,t,2,65535,0),X.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):ed(this,e,t,!0),t+2},X.prototype.writeUInt16BE=function(e,t,r){return e*=1,t|=0,r||eh(this,e,t,2,65535,0),X.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):ed(this,e,t,!1),t+2},X.prototype.writeUInt32LE=function(e,t,r){return e*=1,t|=0,r||eh(this,e,t,4,0xffffffff,0),X.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):ef(this,e,t,!0),t+4},X.prototype.writeUInt32BE=function(e,t,r){return e*=1,t|=0,r||eh(this,e,t,4,0xffffffff,0),X.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):ef(this,e,t,!1),t+4},X.prototype.writeIntLE=function(e,t,r,i){if(e*=1,t|=0,!i){var n=Math.pow(2,8*r-1);eh(this,e,t,r,n-1,-n)}var a=0,o=1,s=0;for(this[t]=255&e;++a<r&&(o*=256);)e<0&&0===s&&0!==this[t+a-1]&&(s=1),this[t+a]=(e/o|0)-s&255;return t+r},X.prototype.writeIntBE=function(e,t,r,i){if(e*=1,t|=0,!i){var n=Math.pow(2,8*r-1);eh(this,e,t,r,n-1,-n)}var a=r-1,o=1,s=0;for(this[t+a]=255&e;--a>=0&&(o*=256);)e<0&&0===s&&0!==this[t+a+1]&&(s=1),this[t+a]=(e/o|0)-s&255;return t+r},X.prototype.writeInt8=function(e,t,r){return e*=1,t|=0,r||eh(this,e,t,1,127,-128),X.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},X.prototype.writeInt16LE=function(e,t,r){return e*=1,t|=0,r||eh(this,e,t,2,32767,-32768),X.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):ed(this,e,t,!0),t+2},X.prototype.writeInt16BE=function(e,t,r){return e*=1,t|=0,r||eh(this,e,t,2,32767,-32768),X.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):ed(this,e,t,!1),t+2},X.prototype.writeInt32LE=function(e,t,r){return e*=1,t|=0,r||eh(this,e,t,4,0x7fffffff,-0x80000000),X.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):ef(this,e,t,!0),t+4},X.prototype.writeInt32BE=function(e,t,r){return e*=1,t|=0,r||eh(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),X.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):ef(this,e,t,!1),t+4},X.prototype.writeFloatLE=function(e,t,r){return eg(this,e,t,!0,r)},X.prototype.writeFloatBE=function(e,t,r){return eg(this,e,t,!1,r)},X.prototype.writeDoubleLE=function(e,t,r){return ep(this,e,t,!0,r)},X.prototype.writeDoubleBE=function(e,t,r){return ep(this,e,t,!1,r)},X.prototype.copy=function(e,t,r,i){if(r||(r=0),i||0===i||(i=this.length),t>=e.length&&(t=e.length),t||(t=0),i>0&&i<r&&(i=r),i===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("sourceStart out of bounds");if(i<0)throw RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),e.length-t<i-r&&(i=e.length-t+r);var n,a=i-r;if(this===e&&r<t&&t<i)for(n=a-1;n>=0;--n)e[n+t]=this[n+r];else if(a<1e3||!X.TYPED_ARRAY_SUPPORT)for(n=0;n<a;++n)e[n+t]=this[n+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+a),t);return a},X.prototype.fill=function(e,t,r,i){if("string"==typeof e){if("string"==typeof t?(i=t,t=0,r=this.length):"string"==typeof r&&(i=r,r=this.length),1===e.length){var n,a=e.charCodeAt(0);a<256&&(e=a)}if(void 0!==i&&"string"!=typeof i)throw TypeError("encoding must be a string");if("string"==typeof i&&!X.isEncoding(i))throw TypeError("Unknown encoding: "+i)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(n=t;n<r;++n)this[n]=e;else{var o=ei(e)?e:ev(new X(e,i).toString()),s=o.length;for(n=0;n<r-t;++n)this[n+t]=o[n%s]}return this};var ey=/[^+\/0-9A-Za-z-_]/g;function ev(e,t){t=t||1/0;for(var r,i=e.length,n=null,a=[],o=0;o<i;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!n){if(r>56319||o+1===i){(t-=3)>-1&&a.push(239,191,189);continue}n=r;continue}if(r<56320){(t-=3)>-1&&a.push(239,191,189),n=r;continue}r=(n-55296<<10|r-56320)+65536}else n&&(t-=3)>-1&&a.push(239,191,189);if(n=null,r<128){if((t-=1)<0)break;a.push(r)}else if(r<2048){if((t-=2)<0)break;a.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;a.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return a}function eA(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function eb(e){return function(e){W||z();var t,r,i,n,a,o,s=e.length;if(s%4>0)throw Error("Invalid string. Length must be a multiple of 4");a="="===e[s-2]?2:+("="===e[s-1]),o=new B(3*s/4-a),i=a>0?s-4:s;var c=0;for(t=0,r=0;t<i;t+=4,r+=3)n=N[e.charCodeAt(t)]<<18|N[e.charCodeAt(t+1)]<<12|N[e.charCodeAt(t+2)]<<6|N[e.charCodeAt(t+3)],o[c++]=n>>16&255,o[c++]=n>>8&255,o[c++]=255&n;return 2===a?(n=N[e.charCodeAt(t)]<<2|N[e.charCodeAt(t+1)]>>4,o[c++]=255&n):1===a&&(n=N[e.charCodeAt(t)]<<10|N[e.charCodeAt(t+1)]<<4|N[e.charCodeAt(t+2)]>>2,o[c++]=n>>8&255,o[c++]=255&n),o}(function(e){var t;if((e=((t=e).trim?t.trim():t.replace(/^\s+|\s+$/g,"")).replace(ey,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function e_(e,t,r,i){for(var n=0;n<i&&!(n+r>=t.length)&&!(n>=e.length);++n)t[n+r]=e[n];return n}function ew(e){var t;return null!=e&&(!!e._isBuffer||eT(e)||"function"==typeof(t=e).readFloatLE&&"function"==typeof t.slice&&eT(t.slice(0,0)))}function eT(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}var eS=Object.freeze({__proto__:null,Buffer:X,INSPECT_MAX_BYTES:50,SlowBuffer:function(e){return+e!=e&&(e=0),X.alloc(+e)},isBuffer:ew,kMaxLength:H}),ex="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function eO(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function ek(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var r=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(t){var i=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,i.get?i:{enumerable:!0,get:function(){return e[t]}})}),r}var eE={exports:{}},eR={};function eC(){throw Error("setTimeout has not been defined")}function eU(){throw Error("clearTimeout has not been defined")}var eD=eC,eP=eU;function eF(e){if(eD===setTimeout)return setTimeout(e,0);if((eD===eC||!eD)&&setTimeout)return eD=setTimeout,setTimeout(e,0);try{return eD(e,0)}catch(t){try{return eD.call(null,e,0)}catch(t){return eD.call(this,e,0)}}}"function"==typeof Y.setTimeout&&(eD=setTimeout),"function"==typeof Y.clearTimeout&&(eP=clearTimeout);var eL=[],eM=!1,eY=-1;function eI(){eM&&u&&(eM=!1,u.length?eL=u.concat(eL):eY=-1,eL.length&&eN())}function eN(){if(!eM){var e=eF(eI);eM=!0;for(var t=eL.length;t;){for(u=eL,eL=[];++eY<t;)u&&u[eY].run();eY=-1,t=eL.length}u=null,eM=!1,function(e){if(eP===clearTimeout)return clearTimeout(e);if((eP===eU||!eP)&&clearTimeout)return eP=clearTimeout,clearTimeout(e);try{eP(e)}catch(t){try{return eP.call(null,e)}catch(t){return eP.call(this,e)}}}(e)}}function eB(e,t){this.fun=e,this.array=t}function eW(){}eB.prototype.run=function(){this.fun.apply(null,this.array)};var ez=Y.performance||{},ej=ez.now||ez.mozNow||ez.msNow||ez.oNow||ez.webkitNow||function(){return new Date().getTime()},eq=new Date,eV={nextTick:function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];eL.push(new eB(e,t)),1!==eL.length||eM||eF(eN)},title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:eW,addListener:eW,once:eW,off:eW,removeListener:eW,removeAllListeners:eW,emit:eW,binding:function(e){throw Error("process.binding is not supported")},cwd:function(){return"/"},chdir:function(e){throw Error("process.chdir is not supported")},umask:function(){return 0},hrtime:function(e){var t=.001*ej.call(ez),r=Math.floor(t),i=Math.floor(t%1*1e9);return e&&(r-=e[0],(i-=e[1])<0&&(r--,i+=1e9)),[r,i]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-eq)/1e3}};function eK(){}function eG(){eG.init.call(this)}function eH(e){return void 0===e._maxListeners?eG.defaultMaxListeners:e._maxListeners}function e$(e,t,r,i){if("function"!=typeof r)throw TypeError('"listener" argument must be a function');if((o=e._events)?(o.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),o=e._events),s=o[t]):(o=e._events=new eK,e._eventsCount=0),s){if("function"==typeof s?s=o[t]=i?[r,s]:[s,r]:i?s.unshift(r):s.push(r),!s.warned&&(a=eH(e))&&a>0&&s.length>a){s.warned=!0;var n,a,o,s,c=Error("Possible EventEmitter memory leak detected. "+s.length+" "+t+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=t,c.count=s.length,n=c,"function"==typeof console.warn?console.warn(n):console.log(n)}}else s=o[t]=r,++e._eventsCount;return e}function eZ(e,t,r){var i=!1;function n(){e.removeListener(t,n),i||(i=!0,r.apply(e,arguments))}return n.listener=r,n}function eX(e){var t=this._events;if(t){var r=t[e];if("function"==typeof r)return 1;if(r)return r.length}return 0}function eJ(e,t){for(var r=Array(t);t--;)r[t]=e[t];return r}eK.prototype=Object.create(null),eG.EventEmitter=eG,eG.usingDomains=!1,eG.prototype.domain=void 0,eG.prototype._events=void 0,eG.prototype._maxListeners=void 0,eG.defaultMaxListeners=10,eG.init=function(){this.domain=null,!eG.usingDomains||!l.active||this instanceof l.Domain||(this.domain=l.active),this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=new eK,this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},eG.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||isNaN(e))throw TypeError('"n" argument must be a positive number');return this._maxListeners=e,this},eG.prototype.getMaxListeners=function(){return eH(this)},eG.prototype.emit=function(e){var t,r,i,n,a,o,s,c="error"===e;if(o=this._events)c=c&&null==o.error;else if(!c)return!1;if(s=this.domain,c){if(t=arguments[1],s)t||(t=Error('Uncaught, unspecified "error" event')),t.domainEmitter=this,t.domain=s,t.domainThrown=!1,s.emit("error",t);else if(t instanceof Error)throw t;else{var u=Error('Uncaught, unspecified "error" event. ('+t+")");throw u.context=t,u}return!1}if(!(r=o[e]))return!1;var l="function"==typeof r;switch(i=arguments.length){case 1:!function(e,t,r){if(t)e.call(r);else for(var i=e.length,n=eJ(e,i),a=0;a<i;++a)n[a].call(r)}(r,l,this);break;case 2:!function(e,t,r,i){if(t)e.call(r,i);else for(var n=e.length,a=eJ(e,n),o=0;o<n;++o)a[o].call(r,i)}(r,l,this,arguments[1]);break;case 3:!function(e,t,r,i,n){if(t)e.call(r,i,n);else for(var a=e.length,o=eJ(e,a),s=0;s<a;++s)o[s].call(r,i,n)}(r,l,this,arguments[1],arguments[2]);break;case 4:!function(e,t,r,i,n,a){if(t)e.call(r,i,n,a);else for(var o=e.length,s=eJ(e,o),c=0;c<o;++c)s[c].call(r,i,n,a)}(r,l,this,arguments[1],arguments[2],arguments[3]);break;default:for(a=1,n=Array(i-1);a<i;a++)n[a-1]=arguments[a];!function(e,t,r,i){if(t)e.apply(r,i);else for(var n=e.length,a=eJ(e,n),o=0;o<n;++o)a[o].apply(r,i)}(r,l,this,n)}return!0},eG.prototype.addListener=function(e,t){return e$(this,e,t,!1)},eG.prototype.on=eG.prototype.addListener,eG.prototype.prependListener=function(e,t){return e$(this,e,t,!0)},eG.prototype.once=function(e,t){if("function"!=typeof t)throw TypeError('"listener" argument must be a function');return this.on(e,eZ(this,e,t)),this},eG.prototype.prependOnceListener=function(e,t){if("function"!=typeof t)throw TypeError('"listener" argument must be a function');return this.prependListener(e,eZ(this,e,t)),this},eG.prototype.removeListener=function(e,t){var r,i,n,a,o;if("function"!=typeof t)throw TypeError('"listener" argument must be a function');if(!(i=this._events)||!(r=i[e]))return this;if(r===t||r.listener&&r.listener===t)0==--this._eventsCount?this._events=new eK:(delete i[e],i.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(n=-1,a=r.length;a-- >0;)if(r[a]===t||r[a].listener&&r[a].listener===t){o=r[a].listener,n=a;break}if(n<0)return this;if(1===r.length){if(r[0]=void 0,0==--this._eventsCount)return this._events=new eK,this;delete i[e]}else{for(var s=r,c=n,u=c,l=u+1,h=s.length;l<h;u+=1,l+=1)s[u]=s[l];s.pop()}i.removeListener&&this.emit("removeListener",e,o||t)}return this},eG.prototype.off=function(e,t){return this.removeListener(e,t)},eG.prototype.removeAllListeners=function(e){if(!(r=this._events))return this;if(!r.removeListener)return 0==arguments.length?(this._events=new eK,this._eventsCount=0):r[e]&&(0==--this._eventsCount?this._events=new eK:delete r[e]),this;if(0==arguments.length){for(var t,r,i,n=Object.keys(r),a=0;a<n.length;++a)"removeListener"!==(i=n[a])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=new eK,this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(t)do this.removeListener(e,t[t.length-1]);while(t[0]);return this},eG.prototype.listeners=function(e){var t,r,i=this._events;return i&&(t=i[e])?"function"==typeof t?[t.listener||t]:function(e){for(var t=Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(t):[]},eG.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):eX.call(e,t)},eG.prototype.listenerCount=eX,eG.prototype.eventNames=function(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]};var eQ=ek(Object.freeze({__proto__:null,EventEmitter:eG,default:eG})),e0=eQ.EventEmitter,e5=ek(eS);h="function"==typeof Object.create?function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e};var e1=Object.getOwnPropertyDescriptors||function(e){for(var t=Object.keys(e),r={},i=0;i<t.length;i++)r[t[i]]=Object.getOwnPropertyDescriptor(e,t[i]);return r},e2=/%[sdj%]/g;function e3(e){if(!tu(e)){for(var t=[],r=0;r<arguments.length;r++)t.push(e8(arguments[r]));return t.join(" ")}for(var r=1,i=arguments,n=i.length,a=String(e).replace(e2,function(e){if("%%"===e)return"%";if(r>=n)return e;switch(e){case"%s":return String(i[r++]);case"%d":return Number(i[r++]);case"%j":try{return JSON.stringify(i[r++])}catch(e){return"[Circular]"}default:return e}}),o=i[r];r<n;o=i[++r])to(o)||!tf(o)?a+=" "+o:a+=" "+e8(o);return a}function e4(e,t){if(th(Y.process))return function(){return e4(e,t).apply(this,arguments)};if(!0===eV.noDeprecation)return e;var r=!1;return function(){if(!r){if(eV.throwDeprecation)throw Error(t);eV.traceDeprecation?console.trace(t):console.error(t),r=!0}return e.apply(this,arguments)}}var e6={};function e7(e){return th(d)&&(d=eV.env.NODE_DEBUG||""),e6[e=e.toUpperCase()]||(RegExp("\\b"+e+"\\b","i").test(d)?e6[e]=function(){var t=e3.apply(null,arguments);console.error("%s %d: %s",e,0,t)}:e6[e]=function(){}),e6[e]}function e8(e,t){var r={seen:[],stylize:te};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),ta(t)?r.showHidden=t:t&&tT(r,t),th(r.showHidden)&&(r.showHidden=!1),th(r.depth)&&(r.depth=2),th(r.colors)&&(r.colors=!1),th(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=e9),tt(r,e,r.depth)}function e9(e,t){var r=e8.styles[t];return r?"\x1b["+e8.colors[r][0]+"m"+e+"\x1b["+e8.colors[r][1]+"m":e}function te(e,t){return e}function tt(e,t,r){if(e.customInspect&&t&&tp(t.inspect)&&t.inspect!==e8&&!(t.constructor&&t.constructor.prototype===t)){var i,n,a,o,s,c=t.inspect(r,e);return tu(c)||(c=tt(e,c,r)),c}var u=function(e,t){if(th(t))return e.stylize("undefined","undefined");if(tu(t)){var r="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(r,"string")}return tc(t)?e.stylize(""+t,"number"):ta(t)?e.stylize(""+t,"boolean"):to(t)?e.stylize("null","null"):void 0}(e,t);if(u)return u;var l=Object.keys(t),h=(o={},l.forEach(function(e,t){o[e]=!0}),o);if(e.showHidden&&(l=Object.getOwnPropertyNames(t)),tg(t)&&(l.indexOf("message")>=0||l.indexOf("description")>=0))return tr(t);if(0===l.length){if(tp(t)){var d=t.name?": "+t.name:"";return e.stylize("[Function"+d+"]","special")}if(td(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(tm(t))return e.stylize(Date.prototype.toString.call(t),"date");if(tg(t))return tr(t)}var f="",m=!1,g=["{","}"];if(tn(t)&&(m=!0,g=["[","]"]),tp(t)&&(f=" [Function"+(t.name?": "+t.name:"")+"]"),td(t)&&(f=" "+RegExp.prototype.toString.call(t)),tm(t)&&(f=" "+Date.prototype.toUTCString.call(t)),tg(t)&&(f=" "+tr(t)),0===l.length&&(!m||0==t.length))return g[0]+f+g[1];if(r<0)if(td(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");else return e.stylize("[Object]","special");return e.seen.push(t),s=m?function(e,t,r,i,n){for(var a=[],o=0,s=t.length;o<s;++o)tS(t,String(o))?a.push(ti(e,t,r,i,String(o),!0)):a.push("");return n.forEach(function(n){n.match(/^\d+$/)||a.push(ti(e,t,r,i,n,!0))}),a}(e,t,r,h,l):l.map(function(i){return ti(e,t,r,h,i,m)}),e.seen.pop(),i=s,n=f,a=g,i.reduce(function(e,t){return t.indexOf("\n"),e+t.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60?a[0]+(""===n?"":n+"\n ")+" "+i.join(",\n  ")+" "+a[1]:a[0]+n+" "+i.join(", ")+" "+a[1]}function tr(e){return"["+Error.prototype.toString.call(e)+"]"}function ti(e,t,r,i,n,a){var o,s,c;if((c=Object.getOwnPropertyDescriptor(t,n)||{value:t[n]}).get?s=c.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):c.set&&(s=e.stylize("[Setter]","special")),tS(i,n)||(o="["+n+"]"),!s&&(0>e.seen.indexOf(c.value)?(s=to(r)?tt(e,c.value,null):tt(e,c.value,r-1)).indexOf("\n")>-1&&(s=a?s.split("\n").map(function(e){return"  "+e}).join("\n").substr(2):"\n"+s.split("\n").map(function(e){return"   "+e}).join("\n")):s=e.stylize("[Circular]","special")),th(o)){if(a&&n.match(/^\d+$/))return s;(o=JSON.stringify(""+n)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(o=o.substr(1,o.length-2),o=e.stylize(o,"name")):(o=o.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),o=e.stylize(o,"string"))}return o+": "+s}function tn(e){return Array.isArray(e)}function ta(e){return"boolean"==typeof e}function to(e){return null===e}function ts(e){return null==e}function tc(e){return"number"==typeof e}function tu(e){return"string"==typeof e}function tl(e){return"symbol"==typeof e}function th(e){return void 0===e}function td(e){return tf(e)&&"[object RegExp]"===tA(e)}function tf(e){return"object"==typeof e&&null!==e}function tm(e){return tf(e)&&"[object Date]"===tA(e)}function tg(e){return tf(e)&&("[object Error]"===tA(e)||e instanceof Error)}function tp(e){return"function"==typeof e}function ty(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e}function tv(e){return X.isBuffer(e)}function tA(e){return Object.prototype.toString.call(e)}function tb(e){return e<10?"0"+e.toString(10):e.toString(10)}e8.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},e8.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"};var t_=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function tw(){var e,t;console.log("%s - %s",(t=[tb((e=new Date).getHours()),tb(e.getMinutes()),tb(e.getSeconds())].join(":"),[e.getDate(),t_[e.getMonth()],t].join(" ")),e3.apply(null,arguments))}function tT(e,t){if(!t||!tf(t))return e;for(var r=Object.keys(t),i=r.length;i--;)e[r[i]]=t[r[i]];return e}function tS(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var tx="undefined"!=typeof Symbol?Symbol("util.promisify.custom"):void 0;function tO(e){if("function"!=typeof e)throw TypeError('The "original" argument must be of type Function');if(tx&&e[tx]){var t=e[tx];if("function"!=typeof t)throw TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(t,tx,{value:t,enumerable:!1,writable:!1,configurable:!0}),t}function t(){for(var t,r,i=new Promise(function(e,i){t=e,r=i}),n=[],a=0;a<arguments.length;a++)n.push(arguments[a]);n.push(function(e,i){e?r(e):t(i)});try{e.apply(this,n)}catch(e){r(e)}return i}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),tx&&Object.defineProperty(t,tx,{value:t,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(t,e1(e))}function tk(e,t){if(!e){var r=Error("Promise was rejected with a falsy value");r.reason=e,e=r}return t(e)}function tE(e){if("function"!=typeof e)throw TypeError('The "original" argument must be of type Function');function t(){for(var t=[],r=0;r<arguments.length;r++)t.push(arguments[r]);var i=t.pop();if("function"!=typeof i)throw TypeError("The last argument must be of type Function");var n=this,a=function(){return i.apply(n,arguments)};e.apply(this,t).then(function(e){eV.nextTick(a.bind(null,null,e))},function(e){eV.nextTick(tk.bind(null,e,a))})}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),Object.defineProperties(t,e1(e)),t}tO.custom=tx;var tR=ek(Object.freeze({__proto__:null,_extend:tT,callbackify:tE,debuglog:e7,default:{inherits:h,_extend:tT,log:tw,isBuffer:tv,isPrimitive:ty,isFunction:tp,isError:tg,isDate:tm,isObject:tf,isRegExp:td,isUndefined:th,isSymbol:tl,isString:tu,isNumber:tc,isNullOrUndefined:ts,isNull:to,isBoolean:ta,isArray:tn,inspect:e8,deprecate:e4,format:e3,debuglog:e7,promisify:tO,callbackify:tE},deprecate:e4,format:e3,inherits:h,inspect:e8,isArray:tn,isBoolean:ta,isBuffer:tv,isDate:tm,isError:tg,isFunction:tp,isNull:to,isNullOrUndefined:ts,isNumber:tc,isObject:tf,isPrimitive:ty,isRegExp:td,isString:tu,isSymbol:tl,isUndefined:th,log:tw,promisify:tO}));function tC(e,t){tD(e,t),tU(e)}function tU(e){(!e._writableState||e._writableState.emitClose)&&(!e._readableState||e._readableState.emitClose)&&e.emit("close")}function tD(e,t){e.emit("error",t)}var tP={destroy:function(e,t){var r=this,i=this._readableState&&this._readableState.destroyed,n=this._writableState&&this._writableState.destroyed;return i||n?t?t(e):e&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,eV.nextTick(tD,this,e)):eV.nextTick(tD,this,e)):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,function(e){!t&&e?r._writableState?r._writableState.errorEmitted?eV.nextTick(tU,r):(r._writableState.errorEmitted=!0,eV.nextTick(tC,r,e)):eV.nextTick(tC,r,e):t?(eV.nextTick(tU,r),t(e)):eV.nextTick(tU,r)})),this},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)},errorOrDestroy:function(e,t){var r=e._readableState,i=e._writableState;r&&r.autoDestroy||i&&i.autoDestroy?e.destroy(t):e.emit("error",t)}},tF={},tL={};function tM(e,t,r){r||(r=Error);var i=function(e){function r(r,i,n){return e.call(this,"string"==typeof t?t:t(r,i,n))||this}return r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.__proto__=e,r}(r);i.prototype.name=r.name,i.prototype.code=e,tL[e]=i}function tY(e,t){if(!Array.isArray(e))return"of ".concat(t," ").concat(String(e));var r=e.length;return(e=e.map(function(e){return String(e)}),r>2)?"one of ".concat(t," ").concat(e.slice(0,r-1).join(", "),", or ")+e[r-1]:2===r?"one of ".concat(t," ").concat(e[0]," or ").concat(e[1]):"of ".concat(t," ").concat(e[0])}tM("ERR_INVALID_OPT_VALUE",function(e,t){return'The value "'+t+'" is invalid for option "'+e+'"'},TypeError),tM("ERR_INVALID_ARG_TYPE",function(e,t,r){if("string"==typeof t&&(i="not ",t.substr(0,i.length)===i)?(s="must not be",t=t.replace(/^not /,"")):s="must be",n=" argument",(void 0===a||a>e.length)&&(a=e.length),e.substring(a-n.length,a)===n)c="The ".concat(e," ").concat(s," ").concat(tY(t,"type"));else{var i,n,a,o,s,c,u=("number"!=typeof o&&(o=0),o+1>e.length||-1===e.indexOf(".",o))?"argument":"property";c='The "'.concat(e,'" ').concat(u," ").concat(s," ").concat(tY(t,"type"))}return c+". Received type ".concat(typeof r)},TypeError),tM("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),tM("ERR_METHOD_NOT_IMPLEMENTED",function(e){return"The "+e+" method is not implemented"}),tM("ERR_STREAM_PREMATURE_CLOSE","Premature close"),tM("ERR_STREAM_DESTROYED",function(e){return"Cannot call "+e+" after a stream was destroyed"}),tM("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),tM("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),tM("ERR_STREAM_WRITE_AFTER_END","write after end"),tM("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),tM("ERR_UNKNOWN_ENCODING",function(e){return"Unknown encoding: "+e},TypeError),tM("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),tF.codes=tL;var tI=tF.codes.ERR_INVALID_OPT_VALUE,tN={getHighWaterMark:function(e,t,r,i){var n=null!=t.highWaterMark?t.highWaterMark:i?t[r]:null;if(null!=n){if(!(isFinite(n)&&Math.floor(n)===n)||n<0)throw new tI(i?r:"highWaterMark",n);return Math.floor(n)}return e.objectMode?16:16384}},tB=ek(Object.freeze({__proto__:null,default:"function"==typeof Object.create?function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}})),tW=X.isEncoding||function(e){switch(e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function tz(e){this.encoding=(e||"utf8").toLowerCase().replace(/[-_]/,"");if(e&&!tW(e))throw Error("Unknown encoding: "+e);switch(this.encoding){case"utf8":this.surrogateSize=3;break;case"ucs2":case"utf16le":this.surrogateSize=2,this.detectIncompleteChar=tq;break;case"base64":this.surrogateSize=3,this.detectIncompleteChar=tV;break;default:this.write=tj;return}this.charBuffer=new X(6),this.charReceived=0,this.charLength=0}function tj(e){return e.toString(this.encoding)}function tq(e){this.charReceived=e.length%2,this.charLength=2*!!this.charReceived}function tV(e){this.charReceived=e.length%3,this.charLength=3*!!this.charReceived}tz.prototype.write=function(e){for(var t="";this.charLength;){var r=e.length>=this.charLength-this.charReceived?this.charLength-this.charReceived:e.length;if(e.copy(this.charBuffer,this.charReceived,0,r),this.charReceived+=r,this.charReceived<this.charLength)return"";e=e.slice(r,e.length);var i=(t=this.charBuffer.slice(0,this.charLength).toString(this.encoding)).charCodeAt(t.length-1);if(i>=55296&&i<=56319){this.charLength+=this.surrogateSize,t="";continue}if(this.charReceived=this.charLength=0,0===e.length)return t;break}this.detectIncompleteChar(e);var n=e.length;this.charLength&&(e.copy(this.charBuffer,0,e.length-this.charReceived,n),n-=this.charReceived);var n=(t+=e.toString(this.encoding,0,n)).length-1,i=t.charCodeAt(n);if(i>=55296&&i<=56319){var a=this.surrogateSize;return this.charLength+=a,this.charReceived+=a,this.charBuffer.copy(this.charBuffer,a,0,a),e.copy(this.charBuffer,0,0,a),t.substring(0,n)}return t},tz.prototype.detectIncompleteChar=function(e){for(var t=e.length>=3?3:e.length;t>0;t--){var r=e[e.length-t];if(1==t&&r>>5==6){this.charLength=2;break}if(t<=2&&r>>4==14){this.charLength=3;break}if(t<=3&&r>>3==30){this.charLength=4;break}}this.charReceived=t},tz.prototype.end=function(e){var t="";if(e&&e.length&&(t=this.write(e)),this.charReceived){var r=this.charReceived,i=this.charBuffer,n=this.encoding;t+=i.slice(0,r).toString(n)}return t};var tK=ek(Object.freeze({__proto__:null,StringDecoder:tz})),tG=tF.codes.ERR_STREAM_PREMATURE_CLOSE;function tH(){}var t$=function e(t,r,i){if("function"==typeof r)return e(t,null,r);r||(r={}),n=i||tH,a=!1,i=function(){if(!a){a=!0;for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];n.apply(this,t)}};var n,a,o=r.readable||!1!==r.readable&&t.readable,s=r.writable||!1!==r.writable&&t.writable,c=function(){t.writable||l()},u=t._writableState&&t._writableState.finished,l=function(){s=!1,u=!0,o||i.call(t)},h=t._readableState&&t._readableState.endEmitted,d=function(){o=!1,h=!0,s||i.call(t)},f=function(e){i.call(t,e)},m=function(){var e;return o&&!h?(t._readableState&&t._readableState.ended||(e=new tG),i.call(t,e)):s&&!u?(t._writableState&&t._writableState.ended||(e=new tG),i.call(t,e)):void 0},g=function(){t.req.on("finish",l)};return t.setHeader&&"function"==typeof t.abort?(t.on("complete",l),t.on("abort",m),t.req?g():t.on("request",g)):s&&!t._writableState&&(t.on("end",c),t.on("close",c)),t.on("end",d),t.on("finish",l),!1!==r.error&&t.on("error",f),t.on("close",m),function(){t.removeListener("complete",l),t.removeListener("abort",m),t.removeListener("request",g),t.req&&t.req.removeListener("finish",l),t.removeListener("end",c),t.removeListener("close",c),t.removeListener("finish",l),t.removeListener("end",d),t.removeListener("error",f),t.removeListener("close",m)}};eR.Readable=t9,t9.ReadableState=t8,eQ.EventEmitter;var tZ=function(e,t){return e.listeners(t).length},tX=e5.Buffer,tJ=ex.Uint8Array||function(){};b=tR&&tR.debuglog?tR.debuglog("stream"):function(){};var tQ=function(){if(m)return f;function e(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}m=1;var t=e5.Buffer,r=tR.inspect,i=r&&r.custom||"inspect";return f=function(){var n;function a(){if(!(this instanceof a))throw TypeError("Cannot call a class as a function");this.head=null,this.tail=null,this.length=0}return n=[{key:"push",value:function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length}},{key:"unshift",value:function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length}},{key:"shift",value:function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(e){if(0===this.length)return"";for(var t=this.head,r=""+t.data;t=t.next;)r+=e+t.data;return r}},{key:"concat",value:function(e){if(0===this.length)return t.alloc(0);for(var r,i,n=t.allocUnsafe(e>>>0),a=this.head,o=0;a;)r=a.data,i=o,t.prototype.copy.call(r,n,i),o+=a.data.length,a=a.next;return n}},{key:"consume",value:function(e,t){var r;return e<this.head.data.length?(r=this.head.data.slice(0,e),this.head.data=this.head.data.slice(e)):r=e===this.head.data.length?this.shift():t?this._getString(e):this._getBuffer(e),r}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(e){var t=this.head,r=1,i=t.data;for(e-=i.length;t=t.next;){var n=t.data,a=e>n.length?n.length:e;if(a===n.length?i+=n:i+=n.slice(0,e),0==(e-=a)){a===n.length?(++r,t.next?this.head=t.next:this.head=this.tail=null):(this.head=t,t.data=n.slice(a));break}++r}return this.length-=r,i}},{key:"_getBuffer",value:function(e){var r=t.allocUnsafe(e),i=this.head,n=1;for(i.data.copy(r),e-=i.data.length;i=i.next;){var a=i.data,o=e>a.length?a.length:e;if(a.copy(r,r.length-e,0,o),0==(e-=o)){o===a.length?(++n,i.next?this.head=i.next:this.head=this.tail=null):(this.head=i,i.data=a.slice(o));break}++n}return this.length-=n,r}},{key:i,value:function(t,i){return r(this,function(t){for(var r=1;r<arguments.length;r++){var i=null!=arguments[r]?arguments[r]:{};r%2?e(Object(i),!0).forEach(function(e){var r,n,a;r=t,n=e,a=i[e],n in r?Object.defineProperty(r,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[n]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):e(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}({},i,{depth:0,customInspect:!1}))}}],function(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}(a.prototype,n),a}()}(),t0=tN.getHighWaterMark,t5=tF.codes,t1=t5.ERR_INVALID_ARG_TYPE,t2=t5.ERR_STREAM_PUSH_AFTER_EOF,t3=t5.ERR_METHOD_NOT_IMPLEMENTED,t4=t5.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;tB(t9,e0);var t6=tP.errorOrDestroy,t7=["error","close","destroy","pause","resume"];function t8(e,t,r){e=e||{},"boolean"!=typeof r&&(r=t instanceof eR.Duplex),this.objectMode=!!e.objectMode,r&&(this.objectMode=this.objectMode||!!e.readableObjectMode),this.highWaterMark=t0(this,e,"readableHighWaterMark",r),this.buffer=new tQ,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==e.emitClose,this.autoDestroy=!!e.autoDestroy,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(_||(_=tK.StringDecoder),this.decoder=new _(e.encoding),this.encoding=e.encoding)}function t9(e){if(!(this instanceof t9))return new t9(e);var t=this instanceof eR.Duplex;this._readableState=new t8(e,this,t),this.readable=!0,e&&("function"==typeof e.read&&(this._read=e.read),"function"==typeof e.destroy&&(this._destroy=e.destroy)),e0.call(this)}function re(e,t,r,i,n){b("readableAddChunk",t);var a,o,s=e._readableState;if(null===t)s.reading=!1,function(e,t){if(b("onEofChunk"),!t.ended){if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,t.sync?ri(e):(t.needReadable=!1,t.emittedReadable||(t.emittedReadable=!0,rn(e)))}}(e,s);else if(n||(o=function(e,t){var r;return tX.isBuffer(t)||t instanceof tJ||"string"==typeof t||void 0===t||e.objectMode||(r=new t1("chunk",["string","Buffer","Uint8Array"],t)),r}(s,t)),o)t6(e,o);else if(s.objectMode||t&&t.length>0)if("string"==typeof t||s.objectMode||Object.getPrototypeOf(t)===tX.prototype||(a=t,t=tX.from(a)),i)s.endEmitted?t6(e,new t4):rt(e,s,t,!0);else if(s.ended)t6(e,new t2);else{if(s.destroyed)return!1;s.reading=!1,s.decoder&&!r?(t=s.decoder.write(t),s.objectMode||0!==t.length?rt(e,s,t,!1):ra(e,s)):rt(e,s,t,!1)}else i||(s.reading=!1,ra(e,s));return!s.ended&&(s.length<s.highWaterMark||0===s.length)}function rt(e,t,r,i){t.flowing&&0===t.length&&!t.sync?(t.awaitDrain=0,e.emit("data",r)):(t.length+=t.objectMode?1:r.length,i?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&ri(e)),ra(e,t)}function rr(e,t){var r;if(e<=0||0===t.length&&t.ended)return 0;if(t.objectMode)return 1;if(e!=e)if(t.flowing&&t.length)return t.buffer.head.data.length;else return t.length;return(e>t.highWaterMark&&((r=e)>=0x40000000?r=0x40000000:(r--,r|=r>>>1,r|=r>>>2,r|=r>>>4,r|=r>>>8,r|=r>>>16,r++),t.highWaterMark=r),e<=t.length)?e:t.ended?t.length:(t.needReadable=!0,0)}function ri(e){var t=e._readableState;b("emitReadable",t.needReadable,t.emittedReadable),t.needReadable=!1,t.emittedReadable||(b("emitReadable",t.flowing),t.emittedReadable=!0,eV.nextTick(rn,e))}function rn(e){var t=e._readableState;b("emitReadable_",t.destroyed,t.length,t.ended),!t.destroyed&&(t.length||t.ended)&&(e.emit("readable"),t.emittedReadable=!1),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,rl(e)}function ra(e,t){t.readingMore||(t.readingMore=!0,eV.nextTick(ro,e,t))}function ro(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&0===t.length);){var r=t.length;if(b("maybeReadMore read 0"),e.read(0),r===t.length)break}t.readingMore=!1}function rs(e){var t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&!t.paused?t.flowing=!0:e.listenerCount("data")>0&&e.resume()}function rc(e){b("readable nexttick read 0"),e.read(0)}function ru(e,t){b("resume",t.reading),t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),rl(e),t.flowing&&!t.reading&&e.read(0)}function rl(e){var t=e._readableState;for(b("flow",t.flowing);t.flowing&&null!==e.read(););}function rh(e,t){var r;return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.first():t.buffer.concat(t.length),t.buffer.clear()):r=t.buffer.consume(e,t.decoder),r)}function rd(e){var t=e._readableState;b("endReadable",t.endEmitted),t.endEmitted||(t.ended=!0,eV.nextTick(rf,t,e))}function rf(e,t){if(b("endReadableNT",e.endEmitted,e.length),!e.endEmitted&&0===e.length&&(e.endEmitted=!0,t.readable=!1,t.emit("end"),e.autoDestroy)){var r=t._writableState;(!r||r.autoDestroy&&r.finished)&&t.destroy()}}function rm(e,t){for(var r=0,i=e.length;r<i;r++)if(e[r]===t)return r;return -1}Object.defineProperty(t9.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),t9.prototype.destroy=tP.destroy,t9.prototype._undestroy=tP.undestroy,t9.prototype._destroy=function(e,t){t(e)},t9.prototype.push=function(e,t){var r,i=this._readableState;return i.objectMode?r=!0:"string"==typeof e&&((t=t||i.defaultEncoding)!==i.encoding&&(e=tX.from(e,t),t=""),r=!0),re(this,e,t,!1,r)},t9.prototype.unshift=function(e){return re(this,e,null,!0,!1)},t9.prototype.isPaused=function(){return!1===this._readableState.flowing},t9.prototype.setEncoding=function(e){_||(_=tK.StringDecoder);var t=new _(e);this._readableState.decoder=t,this._readableState.encoding=this._readableState.decoder.encoding;for(var r=this._readableState.buffer.head,i="";null!==r;)i+=t.write(r.data),r=r.next;return this._readableState.buffer.clear(),""!==i&&this._readableState.buffer.push(i),this._readableState.length=i.length,this},t9.prototype.read=function(e){b("read",e),e=parseInt(e,10);var t,r=this._readableState,i=e;if(0!==e&&(r.emittedReadable=!1),0===e&&r.needReadable&&((0!==r.highWaterMark?r.length>=r.highWaterMark:r.length>0)||r.ended))return b("read: emitReadable",r.length,r.ended),0===r.length&&r.ended?rd(this):ri(this),null;if(0===(e=rr(e,r))&&r.ended)return 0===r.length&&rd(this),null;var n=r.needReadable;return b("need readable",n),(0===r.length||r.length-e<r.highWaterMark)&&b("length less than watermark",n=!0),r.ended||r.reading?b("reading or ended",n=!1):n&&(b("do read"),r.reading=!0,r.sync=!0,0===r.length&&(r.needReadable=!0),this._read(r.highWaterMark),r.sync=!1,r.reading||(e=rr(i,r))),null===(t=e>0?rh(e,r):null)?(r.needReadable=r.length<=r.highWaterMark,e=0):(r.length-=e,r.awaitDrain=0),0===r.length&&(r.ended||(r.needReadable=!0),i!==e&&r.ended&&rd(this)),null!==t&&this.emit("data",t),t},t9.prototype._read=function(e){t6(this,new t3("_read()"))},t9.prototype.pipe=function(e,t){var r,i=this,n=this._readableState;switch(n.pipesCount){case 0:n.pipes=e;break;case 1:n.pipes=[n.pipes,e];break;default:n.pipes.push(e)}n.pipesCount+=1,b("pipe count=%d opts=%j",n.pipesCount,t);var a=t&&!1===t.end||e===eV.stdout||e===eV.stderr?f:o;function o(){b("onend"),e.end()}n.endEmitted?eV.nextTick(a):i.once("end",a),e.on("unpipe",function t(r,a){b("onunpipe"),r===i&&a&&!1===a.hasUnpiped&&(a.hasUnpiped=!0,b("cleanup"),e.removeListener("close",h),e.removeListener("finish",d),e.removeListener("drain",s),e.removeListener("error",l),e.removeListener("unpipe",t),i.removeListener("end",o),i.removeListener("end",f),i.removeListener("data",u),c=!0,n.awaitDrain&&(!e._writableState||e._writableState.needDrain)&&s())});var s=(r=i,function(){var e=r._readableState;b("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&tZ(r,"data")&&(e.flowing=!0,rl(r))});e.on("drain",s);var c=!1;function u(t){b("ondata");var r=e.write(t);b("dest.write",r),!1===r&&((1===n.pipesCount&&n.pipes===e||n.pipesCount>1&&-1!==rm(n.pipes,e))&&!c&&(b("false write response, pause",n.awaitDrain),n.awaitDrain++),i.pause())}function l(t){b("onerror",t),f(),e.removeListener("error",l),0===tZ(e,"error")&&t6(e,t)}function h(){e.removeListener("finish",d),f()}function d(){b("onfinish"),e.removeListener("close",h),f()}function f(){b("unpipe"),i.unpipe(e)}return i.on("data",u),!function(e,t,r){if("function"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?Array.isArray(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(e,"error",l),e.once("close",h),e.once("finish",d),e.emit("pipe",i),n.flowing||(b("pipe resume"),i.resume()),e},t9.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r)),this;if(!e){var i=t.pipes,n=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var a=0;a<n;a++)i[a].emit("unpipe",this,{hasUnpiped:!1});return this}var o=rm(t.pipes,e);return -1===o||(t.pipes.splice(o,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r)),this},t9.prototype.on=function(e,t){var r=e0.prototype.on.call(this,e,t),i=this._readableState;return"data"===e?(i.readableListening=this.listenerCount("readable")>0,!1!==i.flowing&&this.resume()):"readable"!==e||i.endEmitted||i.readableListening||(i.readableListening=i.needReadable=!0,i.flowing=!1,i.emittedReadable=!1,b("on readable",i.length,i.reading),i.length?ri(this):i.reading||eV.nextTick(rc,this)),r},t9.prototype.addListener=t9.prototype.on,t9.prototype.removeListener=function(e,t){var r=e0.prototype.removeListener.call(this,e,t);return"readable"===e&&eV.nextTick(rs,this),r},t9.prototype.removeAllListeners=function(e){var t=e0.prototype.removeAllListeners.apply(this,arguments);return("readable"===e||void 0===e)&&eV.nextTick(rs,this),t},t9.prototype.resume=function(){var e,t,r=this._readableState;return r.flowing||(b("resume"),r.flowing=!r.readableListening,e=this,(t=r).resumeScheduled||(t.resumeScheduled=!0,eV.nextTick(ru,e,t))),r.paused=!1,this},t9.prototype.pause=function(){return b("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(b("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},t9.prototype.wrap=function(e){var t=this,r=this._readableState,i=!1;for(var n in e.on("end",function(){if(b("wrapped end"),r.decoder&&!r.ended){var e=r.decoder.end();e&&e.length&&t.push(e)}t.push(null)}),e.on("data",function(n){if(b("wrapped data"),r.decoder&&(n=r.decoder.write(n)),!r.objectMode||null!=n)(r.objectMode||n&&n.length)&&(t.push(n)||(i=!0,e.pause()))}),e)void 0===this[n]&&"function"==typeof e[n]&&(this[n]=function(t){return function(){return e[t].apply(e,arguments)}}(n));for(var a=0;a<t7.length;a++)e.on(t7[a],this.emit.bind(this,t7[a]));return this._read=function(t){b("wrapped _read",t),i&&(i=!1,e.resume())},this},"function"==typeof Symbol&&(t9.prototype[Symbol.asyncIterator]=function(){return void 0===w&&(w=function(){if(y)return p;function e(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}y=1;var t,r=Symbol("lastResolve"),i=Symbol("lastReject"),n=Symbol("error"),a=Symbol("ended"),o=Symbol("lastPromise"),s=Symbol("handlePromise"),c=Symbol("stream");function u(e,t){return{value:e,done:t}}function l(e){var t=e[r];if(null!==t){var n=e[c].read();null!==n&&(e[o]=null,e[r]=null,e[i]=null,t(u(n,!1)))}}function h(e){eV.nextTick(l,e)}var d=Object.getPrototypeOf(function(){}),f=Object.setPrototypeOf((e(t={get stream(){return this[c]},next:function(){var e,t,r=this,i=this[n];if(null!==i)return Promise.reject(i);if(this[a])return Promise.resolve(u(void 0,!0));if(this[c].destroyed)return new Promise(function(e,t){eV.nextTick(function(){r[n]?t(r[n]):e(u(void 0,!0))})});var l=this[o];if(l)t=new Promise((e=this,function(t,r){l.then(function(){if(e[a])return void t(u(void 0,!0));e[s](t,r)},r)}));else{var h=this[c].read();if(null!==h)return Promise.resolve(u(h,!1));t=new Promise(this[s])}return this[o]=t,t}},Symbol.asyncIterator,function(){return this}),e(t,"return",function(){var e=this;return new Promise(function(t,r){e[c].destroy(null,function(e){if(e)return void r(e);t(u(void 0,!0))})})}),t),d);return p=function(t){var l,d=Object.create(f,(e(l={},c,{value:t,writable:!0}),e(l,r,{value:null,writable:!0}),e(l,i,{value:null,writable:!0}),e(l,n,{value:null,writable:!0}),e(l,a,{value:t._readableState.endEmitted,writable:!0}),e(l,s,{value:function(e,t){var n=d[c].read();n?(d[o]=null,d[r]=null,d[i]=null,e(u(n,!1))):(d[r]=e,d[i]=t)},writable:!0}),l));return d[o]=null,t$(t,function(e){if(e&&"ERR_STREAM_PREMATURE_CLOSE"!==e.code){var t=d[i];null!==t&&(d[o]=null,d[r]=null,d[i]=null,t(e)),d[n]=e;return}var s=d[r];null!==s&&(d[o]=null,d[r]=null,d[i]=null,s(u(void 0,!0))),d[a]=!0}),t.on("readable",h.bind(null,d)),d}}()),w(this)}),Object.defineProperty(t9.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(t9.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(t9.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(e){this._readableState&&(this._readableState.flowing=e)}}),t9._fromList=rh,Object.defineProperty(t9.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}}),"function"==typeof Symbol&&(t9.from=function(e,t){return void 0===T&&(T=A?v:(A=1,v=function(){throw Error("Readable.from is not available in the browser")})),T(t9,e,t)});function rg(e){try{if(!ex.localStorage)return!1}catch(e){return!1}var t=ex.localStorage[e];return null!=t&&"true"===String(t).toLowerCase()}function rp(e){var t=this;this.next=null,this.entry=null,this.finish=function(){var r,i=t,n=e,a=i.entry;for(i.entry=null;a;){var o=a.callback;n.pendingcb--,o(void 0),a=a.next}n.corkedRequestsFree.next=i}}eR.Writable=rD,rD.WritableState=rU;var ry=e5.Buffer,rv=ex.Uint8Array||function(){},rA=tN.getHighWaterMark,rb=tF.codes,r_=rb.ERR_INVALID_ARG_TYPE,rw=rb.ERR_METHOD_NOT_IMPLEMENTED,rT=rb.ERR_MULTIPLE_CALLBACK,rS=rb.ERR_STREAM_CANNOT_PIPE,rx=rb.ERR_STREAM_DESTROYED,rO=rb.ERR_STREAM_NULL_VALUES,rk=rb.ERR_STREAM_WRITE_AFTER_END,rE=rb.ERR_UNKNOWN_ENCODING,rR=tP.errorOrDestroy;function rC(){}function rU(e,t,r){e=e||{},"boolean"!=typeof r&&(r=t instanceof eR.Duplex),this.objectMode=!!e.objectMode,r&&(this.objectMode=this.objectMode||!!e.writableObjectMode),this.highWaterMark=rA(this,e,"writableHighWaterMark",r),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var i=!1===e.decodeStrings;this.decodeStrings=!i,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var r=e._writableState,i=r.sync,n=r.writecb;if("function"!=typeof n)throw new rT;if(r.writing=!1,r.writecb=null,r.length-=r.writelen,r.writelen=0,t)--r.pendingcb,i?(eV.nextTick(n,t),eV.nextTick(rI,e,r),e._writableState.errorEmitted=!0,rR(e,t)):(n(t),e._writableState.errorEmitted=!0,rR(e,t),rI(e,r));else{var a=rM(r)||e.destroyed;a||r.corked||r.bufferProcessing||!r.bufferedRequest||rL(e,r),i?eV.nextTick(rF,e,r,a,n):rF(e,r,a,n)}}(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==e.emitClose,this.autoDestroy=!!e.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new rp(this)}tB(rD,e0),rU.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t};try{Object.defineProperty(rU.prototype,"buffer",{get:({deprecate:function(e,t){if(rg("noDeprecation"))return e;var r=!1;return function(){if(!r){if(rg("throwDeprecation"))throw Error(t);rg("traceDeprecation")?console.trace(t):console.warn(t),r=!0}return e.apply(this,arguments)}}}).deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}function rD(e){var t=this instanceof eR.Duplex;if(!t&&!S.call(rD,this))return new rD(e);this._writableState=new rU(e,this,t),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev),"function"==typeof e.destroy&&(this._destroy=e.destroy),"function"==typeof e.final&&(this._final=e.final)),e0.call(this)}function rP(e,t,r,i,n,a,o){t.writelen=i,t.writecb=o,t.writing=!0,t.sync=!0,t.destroyed?t.onwrite(new rx("write")):r?e._writev(n,t.onwrite):e._write(n,a,t.onwrite),t.sync=!1}function rF(e,t,r,i){var n,a;r||(n=e,0===(a=t).length&&a.needDrain&&(a.needDrain=!1,n.emit("drain"))),t.pendingcb--,i(),rI(e,t)}function rL(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var i=Array(t.bufferedRequestCount),n=t.corkedRequestsFree;n.entry=r;for(var a=0,o=!0;r;)i[a]=r,r.isBuf||(o=!1),r=r.next,a+=1;i.allBuffers=o,rP(e,t,!0,t.length,i,"",n.finish),t.pendingcb++,t.lastBufferedRequest=null,n.next?(t.corkedRequestsFree=n.next,n.next=null):t.corkedRequestsFree=new rp(t),t.bufferedRequestCount=0}else{for(;r;){var s=r.chunk,c=r.encoding,u=r.callback,l=t.objectMode?1:s.length;if(rP(e,t,!1,l,s,c,u),r=r.next,t.bufferedRequestCount--,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}function rM(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function rY(e,t){e._final(function(r){t.pendingcb--,r&&rR(e,r),t.prefinished=!0,e.emit("prefinish"),rI(e,t)})}function rI(e,t){var r=rM(t);if(r&&(t.prefinished||t.finalCalled||("function"!=typeof e._final||t.destroyed?(t.prefinished=!0,e.emit("prefinish")):(t.pendingcb++,t.finalCalled=!0,eV.nextTick(rY,e,t))),0===t.pendingcb&&(t.finished=!0,e.emit("finish"),t.autoDestroy))){var i=e._readableState;(!i||i.autoDestroy&&i.endEmitted)&&e.destroy()}return r}"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(S=Function.prototype[Symbol.hasInstance],Object.defineProperty(rD,Symbol.hasInstance,{value:function(e){return!!S.call(this,e)||this===rD&&e&&e._writableState instanceof rU}})):S=function(e){return e instanceof this},rD.prototype.pipe=function(){rR(this,new rS)},rD.prototype.write=function(e,t,r){var i,n,a,o,s,c,u,l=this._writableState,h=!1,d=!l.objectMode&&(i=e,ry.isBuffer(i)||i instanceof rv);return(d&&!ry.isBuffer(e)&&(n=e,e=ry.from(n)),"function"==typeof t&&(r=t,t=null),d?t="buffer":t||(t=l.defaultEncoding),"function"!=typeof r&&(r=rC),l.ending)?(a=r,rR(this,o=new rk),eV.nextTick(a,o)):(d||(s=e,c=r,null===s?u=new rO:"string"==typeof s||l.objectMode||(u=new r_("chunk",["string","Buffer"],s)),!u||(rR(this,u),eV.nextTick(c,u),0)))&&(l.pendingcb++,h=function(e,t,r,i,n,a){if(!r){var o,s,c=(o=i,s=n,t.objectMode||!1===t.decodeStrings||"string"!=typeof o||(o=ry.from(o,s)),o);i!==c&&(r=!0,n="buffer",i=c)}var u=t.objectMode?1:i.length;t.length+=u;var l=t.length<t.highWaterMark;if(l||(t.needDrain=!0),t.writing||t.corked){var h=t.lastBufferedRequest;t.lastBufferedRequest={chunk:i,encoding:n,isBuf:r,callback:a,next:null},h?h.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else rP(e,t,!1,u,i,n,a);return l}(this,l,d,e,t,r)),h},rD.prototype.cork=function(){this._writableState.corked++},rD.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||rL(this,e))},rD.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new rE(e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(rD.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(rD.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),rD.prototype._write=function(e,t,r){r(new rw("_write()"))},rD.prototype._writev=null,rD.prototype.end=function(e,t,r){var i,n,a,o=this._writableState;return"function"==typeof e?(r=e,e=null,t=null):"function"==typeof t&&(r=t,t=null),null!=e&&this.write(e,t),o.corked&&(o.corked=1,this.uncork()),o.ending||(i=this,n=o,a=r,n.ending=!0,rI(i,n),a&&(n.finished?eV.nextTick(a):i.once("finish",a)),n.ended=!0,i.writable=!1),this},Object.defineProperty(rD.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(rD.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),rD.prototype.destroy=tP.destroy,rD.prototype._undestroy=tP.undestroy,rD.prototype._destroy=function(e,t){t(e)};var rN=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};eR.Duplex=rj,tB(rj,eR.Readable);for(var rB=rN(eR.Writable.prototype),rW=0;rW<rB.length;rW++){var rz=rB[rW];rj.prototype[rz]||(rj.prototype[rz]=eR.Writable.prototype[rz])}function rj(e){if(!(this instanceof rj))return new rj(e);eR.Readable.call(this,e),eR.Writable.call(this,e),this.allowHalfOpen=!0,e&&(!1===e.readable&&(this.readable=!1),!1===e.writable&&(this.writable=!1),!1===e.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",rq)))}function rq(){this._writableState.ended||eV.nextTick(rV,this)}function rV(e){e.end()}Object.defineProperty(rj.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(rj.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(rj.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(rj.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&this._readableState.destroyed&&this._writableState.destroyed},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),eR.Transform=rJ;var rK=tF.codes,rG=rK.ERR_METHOD_NOT_IMPLEMENTED,rH=rK.ERR_MULTIPLE_CALLBACK,r$=rK.ERR_TRANSFORM_ALREADY_TRANSFORMING,rZ=rK.ERR_TRANSFORM_WITH_LENGTH_0;function rX(e,t){var r=this._transformState;r.transforming=!1;var i=r.writecb;if(null===i)return this.emit("error",new rH);r.writechunk=null,r.writecb=null,null!=t&&this.push(t),i(e);var n=this._readableState;n.reading=!1,(n.needReadable||n.length<n.highWaterMark)&&this._read(n.highWaterMark)}function rJ(e){if(!(this instanceof rJ))return new rJ(e);eR.Duplex.call(this,e),this._transformState={afterTransform:rX.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",rQ)}function rQ(){var e=this;"function"!=typeof this._flush||this._readableState.destroyed?r0(this,null,null):this._flush(function(t,r){r0(e,t,r)})}function r0(e,t,r){if(t)return e.emit("error",t);if(null!=r&&e.push(r),e._writableState.length)throw new rZ;if(e._transformState.transforming)throw new r$;return e.push(null)}function r5(e){if(!(this instanceof r5))return new r5(e);Transform.call(this,e)}tB(rJ,eR.Duplex),rJ.prototype.push=function(e,t){return this._transformState.needTransform=!1,eR.Duplex.prototype.push.call(this,e,t)},rJ.prototype._transform=function(e,t,r){r(new rG("_transform()"))},rJ.prototype._write=function(e,t,r){var i=this._transformState;if(i.writecb=r,i.writechunk=e,i.writeencoding=t,!i.transforming){var n=this._readableState;(i.needTransform||n.needReadable||n.length<n.highWaterMark)&&this._read(n.highWaterMark)}},rJ.prototype._read=function(e){var t=this._transformState;null===t.writechunk||t.transforming?t.needTransform=!0:(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform))},rJ.prototype._destroy=function(e,t){eR.Duplex.prototype._destroy.call(this,e,function(e){t(e)})},eR.PassThrough=r5,tB(r5,eR.Transform),r5.prototype._transform=function(e,t,r){r(null,e)};var r1=tF.codes,r2=r1.ERR_MISSING_ARGS,r3=r1.ERR_STREAM_DESTROYED;function r4(e){if(e)throw e}function r6(e){e()}function r7(e,t){return e.pipe(t)}var r8=function(){for(var e,t,r=arguments.length,i=Array(r),n=0;n<r;n++)i[n]=arguments[n];var a=(e=i).length&&"function"==typeof e[e.length-1]?e.pop():r4;if(Array.isArray(i[0])&&(i=i[0]),i.length<2)throw new r2("streams");var o=i.map(function(e,r){var n,s,c,u,l,h,d=r<i.length-1;return n=r>0,c=s=function(e){t||(t=e),e&&o.forEach(r6),d||(o.forEach(r6),a(t))},u=!1,s=function(){u||(u=!0,c.apply(void 0,arguments))},l=!1,e.on("close",function(){l=!0}),void 0===x&&(x=t$),x(e,{readable:d,writable:n},function(e){if(e)return s(e);l=!0,s()}),h=!1,function(t){if(!l&&!h){if(h=!0,e.setHeader&&"function"==typeof e.abort)return e.abort();if("function"==typeof e.destroy)return e.destroy();s(t||new r3("pipe"))}}});return i.reduce(r7)};!function(e,t){(t=e.exports=eR.Readable).Stream=eR.Readable,t.Readable=eR.Readable,t.Writable=eR.Writable,t.Duplex=eR.Duplex,t.Transform=eR.Transform,t.PassThrough=eR.PassThrough,t.finished=t$,t.pipeline=r8}(eE,eE.exports);var r9=eE.exports,ie=eO(r9),it={},ir={};function ii(e,t){if(e===t)return 0;for(var r=e.length,i=t.length,n=0,a=Math.min(r,i);n<a;++n)if(e[n]!==t[n]){r=e[n],i=t[n];break}return r<i?-1:+(i<r)}var ia=Object.prototype.hasOwnProperty,io=Object.keys||function(e){var t=[];for(var r in e)ia.call(e,r)&&t.push(r);return t},is=Array.prototype.slice;function ic(){return void 0!==O?O:O="foo"===(function(){}).name}function iu(e){return Object.prototype.toString.call(e)}function il(e){return!ew(e)&&"function"==typeof Y.ArrayBuffer&&("function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):!!e&&!!(e instanceof DataView||e.buffer&&e.buffer instanceof ArrayBuffer))}function ih(e,t){e||iv(e,!0,t,"==",iA)}var id=/\s*function\s+([^\(\s]*)\s*/;function im(e){if(tp(e)){if(ic())return e.name;var t=e.toString().match(id);return t&&t[1]}}function ig(e){this.name="AssertionError",this.actual=e.actual,this.expected=e.expected,this.operator=e.operator,e.message?(this.message=e.message,this.generatedMessage=!1):(this.message=(t=this,ip(iy(t.actual),128)+" "+t.operator+" "+ip(iy(t.expected),128)),this.generatedMessage=!0);var t,r=e.stackStartFunction||iv;if(Error.captureStackTrace)Error.captureStackTrace(this,r);else{var i=Error();if(i.stack){var n=i.stack,a=im(r),o=n.indexOf("\n"+a);if(o>=0){var s=n.indexOf("\n",o+1);n=n.substring(s+1)}this.stack=n}}}function ip(e,t){return"string"==typeof e?e.length<t?e:e.slice(0,t):e}function iy(e){if(ic()||!tp(e))return e8(e);var t=im(e);return"[Function"+(t?": "+t:"")+"]"}function iv(e,t,r,i,n){throw new ig({message:r,actual:e,expected:t,operator:i,stackStartFunction:n})}function iA(e,t){e||iv(e,!0,t,"==",iA)}function ib(e,t,r){e!=t&&iv(e,t,r,"==",ib)}function i_(e,t,r){e==t&&iv(e,t,r,"!=",i_)}function iw(e,t,r){iS(e,t,!1)||iv(e,t,r,"deepEqual",iw)}function iT(e,t,r){iS(e,t,!0)||iv(e,t,r,"deepStrictEqual",iT)}function iS(e,t,r,i){if(e===t)return!0;if(ew(e)&&ew(t))return 0===ii(e,t);if(tm(e)&&tm(t))return e.getTime()===t.getTime();if(td(e)&&td(t))return e.source===t.source&&e.global===t.global&&e.multiline===t.multiline&&e.lastIndex===t.lastIndex&&e.ignoreCase===t.ignoreCase;if((null===e||"object"!=typeof e)&&(null===t||"object"!=typeof t))return r?e===t:e==t;if(il(e)&&il(t)&&iu(e)===iu(t)&&!(e instanceof Float32Array||e instanceof Float64Array))return 0===ii(new Uint8Array(e.buffer),new Uint8Array(t.buffer));if(ew(e)!==ew(t))return!1;var n=(i=i||{actual:[],expected:[]}).actual.indexOf(e);return -1!==n&&n===i.expected.indexOf(t)||(i.actual.push(e),i.expected.push(t),function(e,t,r,i){if(null==e||null==t)return!1;if(ty(e)||ty(t))return e===t;if(r&&Object.getPrototypeOf(e)!==Object.getPrototypeOf(t))return!1;var n,a,o=ix(e),s=ix(t);if(o&&!s||!o&&s)return!1;if(o)return iS(e=is.call(e),t=is.call(t),r);var c=io(e),u=io(t);if(c.length!==u.length)return!1;for(c.sort(),u.sort(),a=c.length-1;a>=0;a--)if(c[a]!==u[a])return!1;for(a=c.length-1;a>=0;a--)if(!iS(e[n=c[a]],t[n],r,i))return!1;return!0}(e,t,r,i))}function ix(e){return"[object Arguments]"==Object.prototype.toString.call(e)}function iO(e,t,r){iS(e,t,!1)&&iv(e,t,r,"notDeepEqual",iO)}function ik(e,t,r){iS(e,t,!0)&&iv(e,t,r,"notDeepStrictEqual",ik)}function iE(e,t,r){e!==t&&iv(e,t,r,"===",iE)}function iR(e,t,r){e===t&&iv(e,t,r,"!==",iR)}function iC(e,t){if(!e||!t)return!1;if("[object RegExp]"==Object.prototype.toString.call(t))return t.test(e);try{if(e instanceof t)return!0}catch(e){}return!Error.isPrototypeOf(t)&&!0===t.call({},e)}function iU(e,t,r,i){if("function"!=typeof t)throw TypeError('"block" argument must be a function');"string"==typeof r&&(i=r,r=null),n=function(e){var t;try{e()}catch(e){t=e}return t}(t),i=(r&&r.name?" ("+r.name+").":".")+(i?" "+i:"."),e&&!n&&iv(n,r,"Missing expected exception"+i);var n,a="string"==typeof i,o=!e&&tg(n),s=!e&&n&&!r;if((o&&a&&iC(n,r)||s)&&iv(n,r,"Got unwanted exception"+i),e&&n&&r&&!iC(n,r)||!e&&n)throw n}function iD(e,t,r){iU(!0,e,t,r)}function iP(e,t,r){iU(!1,e,t,r)}function iF(e){if(e)throw e}ih.AssertionError=ig,h(ig,Error),ih.fail=iv,ih.ok=iA,ih.equal=ib,ih.notEqual=i_,ih.deepEqual=iw,ih.deepStrictEqual=iT,ih.notDeepEqual=iO,ih.notDeepStrictEqual=ik,ih.strictEqual=iE,ih.notStrictEqual=iR,ih.throws=iD,ih.doesNotThrow=iP,ih.ifError=iF;var iL=ek(Object.freeze({__proto__:null,AssertionError:ig,assert:iA,deepEqual:iw,deepStrictEqual:iT,default:ih,doesNotThrow:iP,equal:ib,fail:iv,ifError:iF,notDeepEqual:iO,notDeepStrictEqual:ik,notEqual:i_,notStrictEqual:iR,ok:iA,strictEqual:iE,throws:iD}));!function(e){for(var t in C)e[t]=C[t];function r(t){if("number"!=typeof t||t<e.DEFLATE||t>e.UNZIP)throw TypeError("Bad argument");this.dictionary=null,this.err=0,this.flush=0,this.init_done=!1,this.level=0,this.memLevel=0,this.mode=t,this.strategy=0,this.windowBits=0,this.write_in_progress=!1,this.pending_close=!1,this.gzip_id_bytes_read=0}e.NONE=0,e.DEFLATE=1,e.INFLATE=2,e.GZIP=3,e.GUNZIP=4,e.DEFLATERAW=5,e.INFLATERAW=6,e.UNZIP=7,r.prototype.close=function(){if(this.write_in_progress){this.pending_close=!0;return}this.pending_close=!1,iL(this.init_done,"close before init"),iL(this.mode<=e.UNZIP),this.mode===e.DEFLATE||this.mode===e.GZIP||this.mode===e.DEFLATERAW?E.deflateEnd(this.strm):(this.mode===e.INFLATE||this.mode===e.GUNZIP||this.mode===e.INFLATERAW||this.mode===e.UNZIP)&&R.inflateEnd(this.strm),this.mode=e.NONE,this.dictionary=null},r.prototype.write=function(e,t,r,i,n,a,o){return this._write(!0,e,t,r,i,n,a,o)},r.prototype.writeSync=function(e,t,r,i,n,a,o){return this._write(!1,e,t,r,i,n,a,o)},r.prototype._write=function(t,r,i,n,a,o,s,c){if(iL.equal(arguments.length,8),iL(this.init_done,"write before init"),iL(this.mode!==e.NONE,"already finalized"),iL.equal(!1,this.write_in_progress,"write already in progress"),iL.equal(!1,this.pending_close,"close is pending"),this.write_in_progress=!0,iL.equal(!1,void 0===r,"must provide flush value"),this.write_in_progress=!0,r!==e.Z_NO_FLUSH&&r!==e.Z_PARTIAL_FLUSH&&r!==e.Z_SYNC_FLUSH&&r!==e.Z_FULL_FLUSH&&r!==e.Z_FINISH&&r!==e.Z_BLOCK)throw Error("Invalid flush value");if(null==i&&(i=X.alloc(0),a=0,n=0),this.strm.avail_in=a,this.strm.input=i,this.strm.next_in=n,this.strm.avail_out=c,this.strm.output=o,this.strm.next_out=s,this.flush=r,!t)return(this._process(),this._checkError())?this._afterSync():void 0;var u=this;return eV.nextTick(function(){u._process(),u._after()}),this},r.prototype._afterSync=function(){var e=this.strm.avail_out,t=this.strm.avail_in;return this.write_in_progress=!1,[t,e]},r.prototype._process=function(){var t=null;switch(this.mode){case e.DEFLATE:case e.GZIP:case e.DEFLATERAW:this.err=E.deflate(this.strm,this.flush);break;case e.UNZIP:switch(this.strm.avail_in>0&&(t=this.strm.next_in),this.gzip_id_bytes_read){case 0:if(null===t)break;if(31===this.strm.input[t]){if(this.gzip_id_bytes_read=1,t++,1===this.strm.avail_in)break}else{this.mode=e.INFLATE;break}case 1:if(null===t)break;139===this.strm.input[t]?(this.gzip_id_bytes_read=2,this.mode=e.GUNZIP):this.mode=e.INFLATE;break;default:throw Error("invalid number of gzip magic number bytes read")}case e.INFLATE:case e.GUNZIP:case e.INFLATERAW:for(this.err=R.inflate(this.strm,this.flush),this.err===e.Z_NEED_DICT&&this.dictionary&&(this.err=R.inflateSetDictionary(this.strm,this.dictionary),this.err===e.Z_OK?this.err=R.inflate(this.strm,this.flush):this.err===e.Z_DATA_ERROR&&(this.err=e.Z_NEED_DICT));this.strm.avail_in>0&&this.mode===e.GUNZIP&&this.err===e.Z_STREAM_END&&0!==this.strm.next_in[0];)this.reset(),this.err=R.inflate(this.strm,this.flush);break;default:throw Error("Unknown mode "+this.mode)}},r.prototype._checkError=function(){switch(this.err){case e.Z_OK:case e.Z_BUF_ERROR:if(0!==this.strm.avail_out&&this.flush===e.Z_FINISH)return this._error("unexpected end of file"),!1;break;case e.Z_STREAM_END:break;case e.Z_NEED_DICT:return null==this.dictionary?this._error("Missing dictionary"):this._error("Bad dictionary"),!1;default:return this._error("Zlib error"),!1}return!0},r.prototype._after=function(){if(this._checkError()){var e=this.strm.avail_out,t=this.strm.avail_in;this.write_in_progress=!1,this.callback(t,e),this.pending_close&&this.close()}},r.prototype._error=function(e){this.strm.msg&&(e=this.strm.msg),this.onerror(e,this.err),this.write_in_progress=!1,this.pending_close&&this.close()},r.prototype.init=function(t,r,i,n,a){iL(4==arguments.length||5==arguments.length,"init(windowBits, level, memLevel, strategy, [dictionary])"),iL(t>=8&&t<=15,"invalid windowBits"),iL(r>=-1&&r<=9,"invalid compression level"),iL(i>=1&&i<=9,"invalid memlevel"),iL(n===e.Z_FILTERED||n===e.Z_HUFFMAN_ONLY||n===e.Z_RLE||n===e.Z_FIXED||n===e.Z_DEFAULT_STRATEGY,"invalid strategy"),this._init(r,t,i,n,a),this._setDictionary()},r.prototype.params=function(){throw Error("deflateParams Not supported")},r.prototype.reset=function(){this._reset(),this._setDictionary()},r.prototype._init=function(t,r,i,n,a){switch(this.level=t,this.windowBits=r,this.memLevel=i,this.strategy=n,this.flush=e.Z_NO_FLUSH,this.err=e.Z_OK,(this.mode===e.GZIP||this.mode===e.GUNZIP)&&(this.windowBits+=16),this.mode===e.UNZIP&&(this.windowBits+=32),(this.mode===e.DEFLATERAW||this.mode===e.INFLATERAW)&&(this.windowBits=-1*this.windowBits),this.strm=new k,this.mode){case e.DEFLATE:case e.GZIP:case e.DEFLATERAW:this.err=E.deflateInit2(this.strm,this.level,e.Z_DEFLATED,this.windowBits,this.memLevel,this.strategy);break;case e.INFLATE:case e.GUNZIP:case e.INFLATERAW:case e.UNZIP:this.err=R.inflateInit2(this.strm,this.windowBits);break;default:throw Error("Unknown mode "+this.mode)}this.err!==e.Z_OK&&this._error("Init error"),this.dictionary=a,this.write_in_progress=!1,this.init_done=!0},r.prototype._setDictionary=function(){if(null!=this.dictionary){switch(this.err=e.Z_OK,this.mode){case e.DEFLATE:case e.DEFLATERAW:this.err=E.deflateSetDictionary(this.strm,this.dictionary)}this.err!==e.Z_OK&&this._error("Failed to set dictionary")}},r.prototype._reset=function(){switch(this.err=e.Z_OK,this.mode){case e.DEFLATE:case e.DEFLATERAW:case e.GZIP:this.err=E.deflateReset(this.strm);break;case e.INFLATE:case e.INFLATERAW:case e.GUNZIP:this.err=R.inflateReset(this.strm)}this.err!==e.Z_OK&&this._error("Failed to reset stream")},e.Zlib=r}(ir),function(e){var t=e5.Buffer,r=r9.Transform,i=iL.ok,n=e5.kMaxLength,a="Cannot create final Buffer. It would be larger than 0x"+n.toString(16)+" bytes";ir.Z_MIN_WINDOWBITS=8,ir.Z_MAX_WINDOWBITS=15,ir.Z_DEFAULT_WINDOWBITS=15,ir.Z_MIN_CHUNK=64,ir.Z_MAX_CHUNK=1/0,ir.Z_DEFAULT_CHUNK=16384,ir.Z_MIN_MEMLEVEL=1,ir.Z_MAX_MEMLEVEL=9,ir.Z_DEFAULT_MEMLEVEL=8,ir.Z_MIN_LEVEL=-1,ir.Z_MAX_LEVEL=9,ir.Z_DEFAULT_LEVEL=ir.Z_DEFAULT_COMPRESSION;for(var o=Object.keys(ir),s=0;s<o.length;s++){var c=o[s];c.match(/^Z/)&&Object.defineProperty(e,c,{enumerable:!0,value:ir[c],writable:!1})}for(var u={Z_OK:ir.Z_OK,Z_STREAM_END:ir.Z_STREAM_END,Z_NEED_DICT:ir.Z_NEED_DICT,Z_ERRNO:ir.Z_ERRNO,Z_STREAM_ERROR:ir.Z_STREAM_ERROR,Z_DATA_ERROR:ir.Z_DATA_ERROR,Z_MEM_ERROR:ir.Z_MEM_ERROR,Z_BUF_ERROR:ir.Z_BUF_ERROR,Z_VERSION_ERROR:ir.Z_VERSION_ERROR},l=Object.keys(u),h=0;h<l.length;h++){var d=l[h];u[u[d]]=d}function f(e,r,i){var o=[],s=0;function c(){for(var t;null!==(t=e.read());)o.push(t),s+=t.length;e.once("readable",c)}function u(){var r,c=null;s>=n?c=RangeError(a):r=t.concat(o,s),o=[],e.close(),i(c,r)}e.on("error",function(t){e.removeListener("end",u),e.removeListener("readable",c),i(t)}),e.on("end",u),e.end(r),c()}function m(e,r){if("string"==typeof r&&(r=t.from(r)),!t.isBuffer(r))throw TypeError("Not a string or buffer");var i=e._finishFlushFlag;return e._processChunk(r,i)}function g(e){if(!(this instanceof g))return new g(e);T.call(this,e,ir.DEFLATE)}function p(e){if(!(this instanceof p))return new p(e);T.call(this,e,ir.INFLATE)}function y(e){if(!(this instanceof y))return new y(e);T.call(this,e,ir.GZIP)}function v(e){if(!(this instanceof v))return new v(e);T.call(this,e,ir.GUNZIP)}function A(e){if(!(this instanceof A))return new A(e);T.call(this,e,ir.DEFLATERAW)}function b(e){if(!(this instanceof b))return new b(e);T.call(this,e,ir.INFLATERAW)}function _(e){if(!(this instanceof _))return new _(e);T.call(this,e,ir.UNZIP)}function w(e){return e===ir.Z_NO_FLUSH||e===ir.Z_PARTIAL_FLUSH||e===ir.Z_SYNC_FLUSH||e===ir.Z_FULL_FLUSH||e===ir.Z_FINISH||e===ir.Z_BLOCK}function T(i,n){var a=this;if(this._opts=i=i||{},this._chunkSize=i.chunkSize||e.Z_DEFAULT_CHUNK,r.call(this,i),i.flush&&!w(i.flush))throw Error("Invalid flush flag: "+i.flush);if(i.finishFlush&&!w(i.finishFlush))throw Error("Invalid flush flag: "+i.finishFlush);if(this._flushFlag=i.flush||ir.Z_NO_FLUSH,this._finishFlushFlag=void 0!==i.finishFlush?i.finishFlush:ir.Z_FINISH,i.chunkSize&&(i.chunkSize<e.Z_MIN_CHUNK||i.chunkSize>e.Z_MAX_CHUNK))throw Error("Invalid chunk size: "+i.chunkSize);if(i.windowBits&&(i.windowBits<e.Z_MIN_WINDOWBITS||i.windowBits>e.Z_MAX_WINDOWBITS))throw Error("Invalid windowBits: "+i.windowBits);if(i.level&&(i.level<e.Z_MIN_LEVEL||i.level>e.Z_MAX_LEVEL))throw Error("Invalid compression level: "+i.level);if(i.memLevel&&(i.memLevel<e.Z_MIN_MEMLEVEL||i.memLevel>e.Z_MAX_MEMLEVEL))throw Error("Invalid memLevel: "+i.memLevel);if(i.strategy&&i.strategy!=e.Z_FILTERED&&i.strategy!=e.Z_HUFFMAN_ONLY&&i.strategy!=e.Z_RLE&&i.strategy!=e.Z_FIXED&&i.strategy!=e.Z_DEFAULT_STRATEGY)throw Error("Invalid strategy: "+i.strategy);if(i.dictionary&&!t.isBuffer(i.dictionary))throw Error("Invalid dictionary: it should be a Buffer instance");this._handle=new ir.Zlib(n);var o=this;this._hadError=!1,this._handle.onerror=function(t,r){S(o),o._hadError=!0;var i=Error(t);i.errno=r,i.code=e.codes[r],o.emit("error",i)};var s=e.Z_DEFAULT_COMPRESSION;"number"==typeof i.level&&(s=i.level);var c=e.Z_DEFAULT_STRATEGY;"number"==typeof i.strategy&&(c=i.strategy),this._handle.init(i.windowBits||e.Z_DEFAULT_WINDOWBITS,s,i.memLevel||e.Z_DEFAULT_MEMLEVEL,c,i.dictionary),this._buffer=t.allocUnsafe(this._chunkSize),this._offset=0,this._level=s,this._strategy=c,this.once("end",this.close),Object.defineProperty(this,"_closed",{get:function(){return!a._handle},configurable:!0,enumerable:!0})}function S(e,t){t&&eV.nextTick(t),e._handle&&(e._handle.close(),e._handle=null)}function x(e){e.emit("close")}Object.defineProperty(e,"codes",{enumerable:!0,value:Object.freeze(u),writable:!1}),e.Deflate=g,e.Inflate=p,e.Gzip=y,e.Gunzip=v,e.DeflateRaw=A,e.InflateRaw=b,e.Unzip=_,e.createDeflate=function(e){return new g(e)},e.createInflate=function(e){return new p(e)},e.createDeflateRaw=function(e){return new A(e)},e.createInflateRaw=function(e){return new b(e)},e.createGzip=function(e){return new y(e)},e.createGunzip=function(e){return new v(e)},e.createUnzip=function(e){return new _(e)},e.deflate=function(e,t,r){return"function"==typeof t&&(r=t,t={}),f(new g(t),e,r)},e.deflateSync=function(e,t){return m(new g(t),e)},e.gzip=function(e,t,r){return"function"==typeof t&&(r=t,t={}),f(new y(t),e,r)},e.gzipSync=function(e,t){return m(new y(t),e)},e.deflateRaw=function(e,t,r){return"function"==typeof t&&(r=t,t={}),f(new A(t),e,r)},e.deflateRawSync=function(e,t){return m(new A(t),e)},e.unzip=function(e,t,r){return"function"==typeof t&&(r=t,t={}),f(new _(t),e,r)},e.unzipSync=function(e,t){return m(new _(t),e)},e.inflate=function(e,t,r){return"function"==typeof t&&(r=t,t={}),f(new p(t),e,r)},e.inflateSync=function(e,t){return m(new p(t),e)},e.gunzip=function(e,t,r){return"function"==typeof t&&(r=t,t={}),f(new v(t),e,r)},e.gunzipSync=function(e,t){return m(new v(t),e)},e.inflateRaw=function(e,t,r){return"function"==typeof t&&(r=t,t={}),f(new b(t),e,r)},e.inflateRawSync=function(e,t){return m(new b(t),e)},tR.inherits(T,r),T.prototype.params=function(t,r,n){if(t<e.Z_MIN_LEVEL||t>e.Z_MAX_LEVEL)throw RangeError("Invalid compression level: "+t);if(r!=e.Z_FILTERED&&r!=e.Z_HUFFMAN_ONLY&&r!=e.Z_RLE&&r!=e.Z_FIXED&&r!=e.Z_DEFAULT_STRATEGY)throw TypeError("Invalid strategy: "+r);if(this._level!==t||this._strategy!==r){var a=this;this.flush(ir.Z_SYNC_FLUSH,function(){i(a._handle,"zlib binding closed"),a._handle.params(t,r),!a._hadError&&(a._level=t,a._strategy=r,n&&n())})}else eV.nextTick(n)},T.prototype.reset=function(){return i(this._handle,"zlib binding closed"),this._handle.reset()},T.prototype._flush=function(e){this._transform(t.alloc(0),"",e)},T.prototype.flush=function(e,r){var i=this,n=this._writableState;"function"!=typeof e&&(void 0!==e||r)||(r=e,e=ir.Z_FULL_FLUSH),n.ended?r&&eV.nextTick(r):n.ending?r&&this.once("end",r):n.needDrain?r&&this.once("drain",function(){return i.flush(e,r)}):(this._flushFlag=e,this.write(t.alloc(0),"",r))},T.prototype.close=function(e){S(this,e),eV.nextTick(x,this)},T.prototype._transform=function(e,r,i){var n,a=this._writableState,o=(a.ending||a.ended)&&(!e||a.length===e.length);return null===e||t.isBuffer(e)?this._handle?void(o?n=this._finishFlushFlag:(n=this._flushFlag,e.length>=a.length&&(this._flushFlag=this._opts.flush||ir.Z_NO_FLUSH)),this._processChunk(e,n,i)):i(Error("zlib binding closed")):i(Error("invalid input"))},T.prototype._processChunk=function(e,r,o){var s=e&&e.length,c=this._chunkSize-this._offset,u=0,l=this,h="function"==typeof o;if(!h){var d,f=[],m=0;this.on("error",function(e){d=e}),i(this._handle,"zlib binding closed");do var g=this._handle.writeSync(r,e,u,s,this._buffer,this._offset,c);while(!this._hadError&&v(g[0],g[1]));if(this._hadError)throw d;if(m>=n)throw S(this),RangeError(a);var p=t.concat(f,m);return S(this),p}i(this._handle,"zlib binding closed");var y=this._handle.write(r,e,u,s,this._buffer,this._offset,c);function v(n,a){if(this&&(this.buffer=null,this.callback=null),!l._hadError){var d=c-a;if(i(d>=0,"have should not go down"),d>0){var g=l._buffer.slice(l._offset,l._offset+d);l._offset+=d,h?l.push(g):(f.push(g),m+=g.length)}if((0===a||l._offset>=l._chunkSize)&&(c=l._chunkSize,l._offset=0,l._buffer=t.allocUnsafe(l._chunkSize)),0===a){if(u+=s-n,s=n,!h)return!0;var p=l._handle.write(r,e,u,s,l._buffer,l._offset,l._chunkSize);return p.callback=v,void(p.buffer=e)}if(!h)return!1;o()}}y.buffer=e,y.callback=v},tR.inherits(g,T),tR.inherits(p,T),tR.inherits(y,T),tR.inherits(v,T),tR.inherits(A,T),tR.inherits(b,T),tR.inherits(_,T)}(it);var iM=eO(it);class iY extends ie.Writable{constructor(e,t,r){super({decodeStrings:!1}),this.finalize=this.finalize.bind(this),this.document=e,this.id=t,null==r&&(r={}),this.data=r,this.gen=0,this.deflate=null,this.compress=this.document.compress&&!this.data.Filter,this.uncompressedLength=0,this.chunks=[]}initDeflate(){return this.data.Filter="FlateDecode",this.deflate=iM.createDeflate(),this.deflate.on("data",e=>(this.chunks.push(e),this.data.Length+=e.length)),this.deflate.on("end",this.finalize)}_write(e,t,r){return e instanceof Uint8Array||(e=X.from(e+"\n","binary")),this.uncompressedLength+=e.length,null==this.data.Length&&(this.data.Length=0),this.compress?(this.deflate||this.initDeflate(),this.deflate.write(e)):(this.chunks.push(e),this.data.Length+=e.length),r()}end(){return(super.end(...arguments),this.deflate)?this.deflate.end():this.finalize()}finalize(){if(this.offset=this.document._offset,this.document._write(`${this.id} ${this.gen} obj`),this.document._write(iq.convert(this.data)),this.chunks.length){for(let e of(this.document._write("stream"),Array.from(this.chunks)))this.document._write(e);this.chunks.length=0,this.document._write("\nendstream")}return this.document._write("endobj"),this.document._refEnd(this)}toString(){return`${this.id} ${this.gen} R`}}class iI{constructor(e){void 0===e&&(e={}),this._items={},this.limits="boolean"!=typeof e.limits||e.limits}add(e,t){return this._items[e]=t}get(e){return this._items[e]}toString(){let e=Object.keys(this._items).sort((e,t)=>this._compareKeys(e,t)),t=["<<"];if(this.limits&&e.length>1){let r=e[0],i=e[e.length-1];t.push(`  /Limits ${iq.convert([this._dataForKey(r),this._dataForKey(i)])}`)}for(let r of(t.push(`  /${this._keysName()} [`),e))t.push(`    ${iq.convert(this._dataForKey(r))} ${iq.convert(this._items[r])}`);return t.push("]"),t.push(">>"),t.join("\n")}_compareKeys(){throw Error("Must be implemented by subclasses")}_keysName(){throw Error("Must be implemented by subclasses")}_dataForKey(){throw Error("Must be implemented by subclasses")}}class iN extends iI{_compareKeys(e,t){return e.localeCompare(t)}_keysName(){return"Names"}_dataForKey(e){return new String(e)}}let iB=(e,t)=>(Array(t+1).join("0")+e).slice(-t),iW=/[\n\r\t\b\f()\\]/g,iz={"\n":"\\n","\r":"\\r","	":"\\t","\b":"\\b","\f":"\\f","\\":"\\\\","(":"\\(",")":"\\)"},ij=function(e){let t=e.length;if(1&t)throw Error("Buffer length must be even");for(let r=0,i=t-1;r<i;r+=2){let t=e[r];e[r]=e[r+1],e[r+1]=t}return e};class iq{static convert(e){if("string"==typeof e)return`/${e}`;if(e instanceof String){let t=e,r=!1;for(let e=0,i=t.length;e<i;e++)if(t.charCodeAt(e)>127){r=!0;break}return r&&(t=ij(X.from(`\ufeff${t}`,"utf16le")).toString("binary")),t=t.replace(iW,e=>iz[e]),`(${t})`}if(X.isBuffer(e))return`<${e.toString("hex")}>`;if(e instanceof iY||e instanceof iN)return e.toString();if(e instanceof Date)return`(D:${iB(e.getUTCFullYear(),4)}`+iB(e.getUTCMonth()+1,2)+iB(e.getUTCDate(),2)+iB(e.getUTCHours(),2)+iB(e.getUTCMinutes(),2)+iB(e.getUTCSeconds(),2)+"Z)";if(Array.isArray(e)){let t=Array.from(e).map(e=>iq.convert(e)).join(" ");return`[${t}]`}if("[object Object]"===({}).toString.call(e)){let t=["<<"];for(let r in e){let i=e[r];t.push(`/${r} ${iq.convert(i)}`)}return t.push(">>"),t.join("\n")}return"number"==typeof e?iq.number(e):`${e}`}static number(e){if(e>-1e21&&e<1e21)return Math.round(1e6*e)/1e6;throw Error(`unsupported number: ${e}`)}}let iV={top:72,left:72,bottom:72,right:72},iK={"4A0":[4767.87,6740.79],"2A0":[3370.39,4767.87],A0:[2383.94,3370.39],A1:[1683.78,2383.94],A2:[1190.55,1683.78],A3:[841.89,1190.55],A4:[595.28,841.89],A5:[419.53,595.28],A6:[297.64,419.53],A7:[209.76,297.64],A8:[147.4,209.76],A9:[104.88,147.4],A10:[73.7,104.88],B0:[2834.65,4008.19],B1:[2004.09,2834.65],B2:[1417.32,2004.09],B3:[1000.63,1417.32],B4:[708.66,1000.63],B5:[498.9,708.66],B6:[354.33,498.9],B7:[249.45,354.33],B8:[175.75,249.45],B9:[124.72,175.75],B10:[87.87,124.72],C0:[2599.37,3676.54],C1:[1836.85,2599.37],C2:[1298.27,1836.85],C3:[918.43,1298.27],C4:[649.13,918.43],C5:[459.21,649.13],C6:[323.15,459.21],C7:[229.61,323.15],C8:[161.57,229.61],C9:[113.39,161.57],C10:[79.37,113.39],RA0:[2437.8,3458.27],RA1:[1729.13,2437.8],RA2:[1218.9,1729.13],RA3:[864.57,1218.9],RA4:[609.45,864.57],SRA0:[2551.18,3628.35],SRA1:[1814.17,2551.18],SRA2:[1275.59,1814.17],SRA3:[907.09,1275.59],SRA4:[637.8,907.09],EXECUTIVE:[521.86,756],FOLIO:[612,936],LEGAL:[612,1008],LETTER:[612,792],TABLOID:[792,1224]};class iG{constructor(e,t){void 0===t&&(t={}),this.document=e,this._options=t,this.size=t.size||"letter",this.layout=t.layout||"portrait",this.userUnit=t.userUnit||1,"number"==typeof t.margin?this.margins={top:t.margin,left:t.margin,bottom:t.margin,right:t.margin}:this.margins=t.margins||iV;let r=Array.isArray(this.size)?this.size:iK[this.size.toUpperCase()];this.width=r[+("portrait"!==this.layout)],this.height=r[+("portrait"===this.layout)],this.content=this.document.ref(),t.font&&e.font(t.font,t.fontFamily),t.fontSize&&e.fontSize(t.fontSize),this.resources=this.document.ref({ProcSet:["PDF","Text","ImageB","ImageC","ImageI"]}),this.dictionary=this.document.ref({Type:"Page",Parent:this.document._root.data.Pages,MediaBox:[0,0,this.width,this.height],Contents:this.content,Resources:this.resources,UserUnit:this.userUnit}),this.markings=[]}get fonts(){let e=this.resources.data;return null!=e.Font?e.Font:e.Font={}}get xobjects(){let e=this.resources.data;return null!=e.XObject?e.XObject:e.XObject={}}get ext_gstates(){let e=this.resources.data;return null!=e.ExtGState?e.ExtGState:e.ExtGState={}}get patterns(){let e=this.resources.data;return null!=e.Pattern?e.Pattern:e.Pattern={}}get colorSpaces(){let e=this.resources.data;return e.ColorSpace||(e.ColorSpace={})}get annotations(){let e=this.dictionary.data;return null!=e.Annots?e.Annots:e.Annots=[]}get structParentTreeKey(){let e=this.dictionary.data;return null!=e.StructParents?e.StructParents:e.StructParents=this.document.createStructParentTreeNextKey()}maxY(){return this.height-this.margins.bottom}write(e){return this.content.write(e)}_setTabOrder(){!this.dictionary.Tabs&&this.document.hasMarkInfoDictionary()&&(this.dictionary.data.Tabs="S")}end(){for(let e of(this._setTabOrder(),this.dictionary.end(),this.resources.data.ColorSpace=this.resources.data.ColorSpace||{},Object.values(this.document.spotColors)))this.resources.data.ColorSpace[e.id]=e;return this.resources.end(),this.content.end()}}let iH=e=>{let t=[];for(let r=0;r<e.sigBytes;r++)t.push(e.words[Math.floor(r/4)]>>8*(3-r%4)&255);return X.from(t)};class i${static generateFileID(e){void 0===e&&(e={});let t=`${e.CreationDate.getTime()}
`;for(let r in e)e.hasOwnProperty(r)&&(t+=`${r}: ${e[r].valueOf()}
`);return iH(U(t))}}let{number:iZ}=iq,iX=class{constructor(e){this.doc=e,this.stops=[],this.embedded=!1,this.transform=[1,0,0,1,0,0]}stop(e,t,r){if(null==r&&(r=1),t=this.doc._normalizeColor(t),0===this.stops.length)if(3===t.length)this._colorSpace="DeviceRGB";else if(4===t.length)this._colorSpace="DeviceCMYK";else if(1===t.length)this._colorSpace="DeviceGray";else throw Error("Unknown color space");else if("DeviceRGB"===this._colorSpace&&3!==t.length||"DeviceCMYK"===this._colorSpace&&4!==t.length||"DeviceGray"===this._colorSpace&&1!==t.length)throw Error("All gradient stops must use the same color space");return r=Math.max(0,Math.min(1,r)),this.stops.push([e,t,r]),this}setTransform(e,t,r,i,n,a){return this.transform=[e,t,r,i,n,a],this}embed(e){let t,r=this.stops.length;if(0===r)return;this.embedded=!0,this.matrix=e;let i=this.stops[r-1];i[0]<1&&this.stops.push([1,i[1],i[2]]);let n=[],a=[],o=[];for(let e=0;e<r-1;e++)a.push(0,1),e+2!==r&&n.push(this.stops[e+1][0]),t=this.doc.ref({FunctionType:2,Domain:[0,1],C0:this.stops[e+0][1],C1:this.stops[e+1][1],N:1}),o.push(t),t.end();1===r?t=o[0]:(t=this.doc.ref({FunctionType:3,Domain:[0,1],Functions:o,Bounds:n,Encode:a})).end(),this.id=`Sh${++this.doc._gradCount}`;let s=this.shader(t);s.end();let c=this.doc.ref({Type:"Pattern",PatternType:2,Shading:s,Matrix:this.matrix.map(iZ)});if(c.end(),this.stops.some(e=>e[2]<1)){let e=this.opacityGradient();for(let t of(e._colorSpace="DeviceGray",this.stops))e.stop(t[0],[t[2]]);e=e.embed(this.matrix);let t=[0,0,this.doc.page.width,this.doc.page.height],r=this.doc.ref({Type:"XObject",Subtype:"Form",FormType:1,BBox:t,Group:{Type:"Group",S:"Transparency",CS:"DeviceGray"},Resources:{ProcSet:["PDF","Text","ImageB","ImageC","ImageI"],Pattern:{Sh1:e}}});r.write("/Pattern cs /Sh1 scn"),r.end(`${t.join(" ")} re f`);let i=this.doc.ref({Type:"ExtGState",SMask:{Type:"Mask",S:"Luminosity",G:r}});i.end();let n=this.doc.ref({Type:"Pattern",PatternType:1,PaintType:1,TilingType:2,BBox:t,XStep:t[2],YStep:t[3],Resources:{ProcSet:["PDF","Text","ImageB","ImageC","ImageI"],Pattern:{Sh1:c},ExtGState:{Gs1:i}}});n.write("/Gs1 gs /Pattern cs /Sh1 scn"),n.end(`${t.join(" ")} re f`),this.doc.page.patterns[this.id]=n}else this.doc.page.patterns[this.id]=c;return c}apply(e){let[t,r,i,n,a,o]=this.doc._ctm,[s,c,u,l,h,d]=this.transform,f=[t*s+i*c,r*s+n*c,t*u+i*l,r*u+n*l,t*h+i*d+a,r*h+n*d+o];this.embedded&&f.join(" ")===this.matrix.join(" ")||this.embed(f),this.doc._setColorSpace("Pattern",e);let m=e?"SCN":"scn";return this.doc.addContent(`/${this.id} ${m}`)}},iJ=class e extends iX{constructor(e,t,r,i,n){super(e),this.x1=t,this.y1=r,this.x2=i,this.y2=n}shader(e){return this.doc.ref({ShadingType:2,ColorSpace:this._colorSpace,Coords:[this.x1,this.y1,this.x2,this.y2],Function:e,Extend:[!0,!0]})}opacityGradient(){return new e(this.doc,this.x1,this.y1,this.x2,this.y2)}},iQ=class e extends iX{constructor(e,t,r,i,n,a,o){super(e),this.doc=e,this.x1=t,this.y1=r,this.r1=i,this.x2=n,this.y2=a,this.r2=o}shader(e){return this.doc.ref({ShadingType:3,ColorSpace:this._colorSpace,Coords:[this.x1,this.y1,this.r1,this.x2,this.y2,this.r2],Function:e,Extend:[!0,!0]})}opacityGradient(){return new e(this.doc,this.x1,this.y1,this.r1,this.x2,this.y2,this.r2)}},i0=["DeviceCMYK","DeviceRGB"],i5=class{constructor(e,t,r,i,n){this.doc=e,this.bBox=t,this.xStep=r,this.yStep=i,this.stream=n}createPattern(){let e=this.doc.ref();e.end();let[t,r,i,n,a,o]=this.doc._ctm,[s,c,u,l,h,d]=[1,0,0,1,0,0],f=this.doc.ref({Type:"Pattern",PatternType:1,PaintType:2,TilingType:2,BBox:this.bBox,XStep:this.xStep,YStep:this.yStep,Matrix:[t*s+i*c,r*s+n*c,t*u+i*l,r*u+n*l,t*h+i*d+a,r*h+n*d+o].map(e=>+e.toFixed(5)),Resources:e});return f.end(this.stream),f}embedPatternColorSpaces(){i0.forEach(e=>{let t=this.getPatternColorSpaceId(e);if(this.doc.page.colorSpaces[t])return;let r=this.doc.ref(["Pattern",e]);r.end(),this.doc.page.colorSpaces[t]=r})}getPatternColorSpaceId(e){return`CsP${e}`}embed(){this.id||(this.doc._patternCount=this.doc._patternCount+1,this.id="P"+this.doc._patternCount,this.pattern=this.createPattern()),this.doc.page.patterns[this.id]||(this.doc.page.patterns[this.id]=this.pattern)}apply(e,t){this.embedPatternColorSpaces(),this.embed();let r=this.doc._normalizeColor(t);if(!r)throw Error(`invalid pattern color. (value: ${t})`);let i=this.getPatternColorSpaceId(this.doc._getColorSpace(r));this.doc._setColorSpace(i,e);let n=e?"SCN":"scn";return this.doc.addContent(`${r.join(" ")} /${this.id} ${n}`)}};class i1{constructor(e,t,r,i,n,a){this.id="CS"+Object.keys(e.spotColors).length,this.name=t,this.values=[r,i,n,a],this.ref=e.ref(["Separation",this.name,"DeviceCMYK",{Range:[0,1,0,1,0,1,0,1],C0:[0,0,0,0],C1:this.values.map(e=>e/100),FunctionType:2,Domain:[0,1],N:1}]),this.ref.end()}toString(){return`${this.ref.id} 0 R`}}let{PDFGradient:i2,PDFLinearGradient:i3,PDFRadialGradient:i4}={PDFGradient:iX,PDFLinearGradient:iJ,PDFRadialGradient:iQ},{PDFTilingPattern:i6}={PDFTilingPattern:i5};var i7={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};i=n=a=o=s=c=0;let i8={M:2,m:2,Z:0,z:0,L:2,l:2,H:1,h:1,V:1,v:1,C:6,c:6,S:4,s:4,Q:4,q:4,T:2,t:2,A:7,a:7},i9=e=>e in i8,ne=e=>{let t=e.codePointAt(0);return 32===t||9===t||13===t||10===t},nt=e=>{let t=e.codePointAt(0);return null!=t&&48<=t&&t<=57},nr=(e,t)=>{let r=t,i="",n="none";for(;r<e.length;r+=1){let t=e[r];if("+"===t||"-"===t){if("none"===n){n="sign",i+=t;continue}if("e"===n){n="exponent_sign",i+=t;continue}}if(nt(t)){if("none"===n||"sign"===n||"whole"===n){n="whole",i+=t;continue}if("decimal_point"===n||"decimal"===n){n="decimal",i+=t;continue}if("e"===n||"exponent_sign"===n||"exponent"===n){n="exponent",i+=t;continue}}if("."===t&&("none"===n||"sign"===n||"whole"===n)){n="decimal_point",i+=t;continue}if(("E"===t||"e"===t)&&("whole"===n||"decimal_point"===n||"decimal"===n)){n="e",i+=t;continue}break}let a=Number.parseFloat(i);return Number.isNaN(a)?[t,null]:[r-1,a]},ni=e=>{let t=[],r=null,i=[],n=0,a=!1,o=!1;for(let s=0;s<e.length;s+=1){let c=e.charAt(s);if(ne(c))continue;if(a&&","===c){if(o)break;o=!0;continue}if(i9(c)){if(o)return t;if(null==r){if("M"!==c&&"m"!==c)return t}else if(0!==i.length)return t;i=[],n=i8[r=c],a=!1,0===n&&t.push({command:r,args:i});continue}if(null==r)break;let u=s,l=null;if("A"===r||"a"===r){let t=i.length;(0===t||1===t)&&"+"!==c&&"-"!==c&&([u,l]=nr(e,s)),(2===t||5===t||6===t)&&([u,l]=nr(e,s)),(3===t||4===t)&&("0"===c&&(l=0),"1"===c&&(l=1))}else[u,l]=nr(e,s);if(null==l)break;i.push(l),a=!0,o=!1,s=u,i.length===n&&(t.push({command:r,args:i}),"M"===r&&(r="L"),"m"===r&&(r="l"),i=[])}return t},nn=function(e,t){i=n=a=o=s=c=0;for(let r=0;r<e.length;r++){let{command:i,args:n}=e[r];"function"==typeof na[i]&&na[i](t,n)}},na={M:(e,t)=>(i=t[0],n=t[1],a=o=null,s=i,c=n,e.moveTo(i,n)),m:(e,t)=>(i+=t[0],n+=t[1],a=o=null,s=i,c=n,e.moveTo(i,n)),C:(e,t)=>(i=t[4],n=t[5],a=t[2],o=t[3],e.bezierCurveTo(...t)),c:(e,t)=>(e.bezierCurveTo(t[0]+i,t[1]+n,t[2]+i,t[3]+n,t[4]+i,t[5]+n),a=i+t[2],o=n+t[3],i+=t[4],n+=t[5]),S:(e,t)=>(null===a&&(a=i,o=n),e.bezierCurveTo(i-(a-i),n-(o-n),t[0],t[1],t[2],t[3]),a=t[0],o=t[1],i=t[2],n=t[3]),s:(e,t)=>(null===a&&(a=i,o=n),e.bezierCurveTo(i-(a-i),n-(o-n),i+t[0],n+t[1],i+t[2],n+t[3]),a=i+t[0],o=n+t[1],i+=t[2],n+=t[3]),Q:(e,t)=>(a=t[0],o=t[1],i=t[2],n=t[3],e.quadraticCurveTo(t[0],t[1],i,n)),q:(e,t)=>(e.quadraticCurveTo(t[0]+i,t[1]+n,t[2]+i,t[3]+n),a=i+t[0],o=n+t[1],i+=t[2],n+=t[3]),T:(e,t)=>(null===a?(a=i,o=n):(a=i-(a-i),o=n-(o-n)),e.quadraticCurveTo(a,o,t[0],t[1]),a=i-(a-i),o=n-(o-n),i=t[0],n=t[1]),t:(e,t)=>(null===a?(a=i,o=n):(a=i-(a-i),o=n-(o-n)),e.quadraticCurveTo(a,o,i+t[0],n+t[1]),i+=t[0],n+=t[1]),A:(e,t)=>(no(e,i,n,t),i=t[5],n=t[6]),a:(e,t)=>(t[5]+=i,t[6]+=n,no(e,i,n,t),i=t[5],n=t[6]),L:(e,t)=>(i=t[0],n=t[1],a=o=null,e.lineTo(i,n)),l:(e,t)=>(i+=t[0],n+=t[1],a=o=null,e.lineTo(i,n)),H:(e,t)=>(i=t[0],a=o=null,e.lineTo(i,n)),h:(e,t)=>(i+=t[0],a=o=null,e.lineTo(i,n)),V:(e,t)=>(n=t[0],a=o=null,e.lineTo(i,n)),v:(e,t)=>(n+=t[0],a=o=null,e.lineTo(i,n)),Z:e=>(e.closePath(),i=s,n=c),z:e=>(e.closePath(),i=s,n=c)},no=function(e,t,r,i){let[n,a,o,s,c,u,l]=i;for(let i of ns(u,l,n,a,s,c,o,t,r)){let t=nc(...i);e.bezierCurveTo(...t)}},ns=function(e,t,r,i,n,s,c,u,l){let h=Math.PI/180*c,d=Math.sin(h),f=Math.cos(h);r=Math.abs(r),i=Math.abs(i);let m=(a=f*(u-e)*.5+d*(l-t)*.5)*a/(r*r)+(o=f*(l-t)*.5-d*(u-e)*.5)*o/(i*i);m>1&&(r*=m=Math.sqrt(m),i*=m);let g=f/r,p=d/r,y=-d/i,v=f/i,A=g*u+p*l,b=y*u+v*l,_=g*e+p*t,w=y*e+v*t,T=1/((_-A)*(_-A)+(w-b)*(w-b))-.25;T<0&&(T=0);let S=Math.sqrt(T);s===n&&(S=-S);let x=.5*(A+_)-S*(w-b),O=.5*(b+w)+S*(_-A),k=Math.atan2(b-O,A-x),E=Math.atan2(w-O,_-x)-k;E<0&&1===s?E+=2*Math.PI:E>0&&0===s&&(E-=2*Math.PI);let R=Math.ceil(Math.abs(E/(.5*Math.PI+.001))),C=[];for(let e=0;e<R;e++){let t=k+e*E/R,n=k+(e+1)*E/R;C[e]=[x,O,t,n,r,i,d,f]}return C},nc=function(e,t,r,i,n,a,o,s){let c=s*n,u=-o*a,l=o*n,h=s*a,d=.5*(i-r),f=8/3*Math.sin(.5*d)*Math.sin(.5*d)/Math.sin(d),m=e+Math.cos(r)-f*Math.sin(r),g=t+Math.sin(r)+f*Math.cos(r),p=e+Math.cos(i),y=t+Math.sin(i),v=p+f*Math.sin(i),A=y-f*Math.cos(i);return[c*m+u*g,l*m+h*g,c*v+u*A,l*v+h*A,c*p+u*y,l*p+h*y]};class nu{static apply(e,t){nn(ni(t),e)}}let{number:nl}=iq,nh=(Math.sqrt(2)-1)/3*4;var nd={};let nf=(e,t,r)=>{let i=[],n=t+1;for(let t=e;t<n;t++)i.push(t);return i},nm={402:131,8211:150,8212:151,8216:145,8217:146,8218:130,8220:147,8221:148,8222:132,8224:134,8225:135,8226:149,8230:133,8364:128,8240:137,8249:139,8250:155,710:136,8482:153,338:140,339:156,732:152,352:138,353:154,376:159,381:142,382:158},ng=`\
.notdef       .notdef        .notdef        .notdef
.notdef       .notdef        .notdef        .notdef
.notdef       .notdef        .notdef        .notdef
.notdef       .notdef        .notdef        .notdef
.notdef       .notdef        .notdef        .notdef
.notdef       .notdef        .notdef        .notdef
.notdef       .notdef        .notdef        .notdef
.notdef       .notdef        .notdef        .notdef

space         exclam         quotedbl       numbersign
dollar        percent        ampersand      quotesingle
parenleft     parenright     asterisk       plus
comma         hyphen         period         slash
zero          one            two            three
four          five           six            seven
eight         nine           colon          semicolon
less          equal          greater        question

at            A              B              C
D             E              F              G
H             I              J              K
L             M              N              O
P             Q              R              S
T             U              V              W
X             Y              Z              bracketleft
backslash     bracketright   asciicircum    underscore

grave         a              b              c
d             e              f              g
h             i              j              k
l             m              n              o
p             q              r              s
t             u              v              w
x             y              z              braceleft
bar           braceright     asciitilde     .notdef

Euro          .notdef        quotesinglbase florin
quotedblbase  ellipsis       dagger         daggerdbl
circumflex    perthousand    Scaron         guilsinglleft
OE            .notdef        Zcaron         .notdef
.notdef       quoteleft      quoteright     quotedblleft
quotedblright bullet         endash         emdash
tilde         trademark      scaron         guilsinglright
oe            .notdef        zcaron         ydieresis

space         exclamdown     cent           sterling
currency      yen            brokenbar      section
dieresis      copyright      ordfeminine    guillemotleft
logicalnot    hyphen         registered     macron
degree        plusminus      twosuperior    threesuperior
acute         mu             paragraph      periodcentered
cedilla       onesuperior    ordmasculine   guillemotright
onequarter    onehalf        threequarters  questiondown

Agrave        Aacute         Acircumflex    Atilde
Adieresis     Aring          AE             Ccedilla
Egrave        Eacute         Ecircumflex    Edieresis
Igrave        Iacute         Icircumflex    Idieresis
Eth           Ntilde         Ograve         Oacute
Ocircumflex   Otilde         Odieresis      multiply
Oslash        Ugrave         Uacute         Ucircumflex
Udieresis     Yacute         Thorn          germandbls

agrave        aacute         acircumflex    atilde
adieresis     aring          ae             ccedilla
egrave        eacute         ecircumflex    edieresis
igrave        iacute         icircumflex    idieresis
eth           ntilde         ograve         oacute
ocircumflex   otilde         odieresis      divide
oslash        ugrave         uacute         ucircumflex
udieresis     yacute         thorn          ydieresis\
`.split(/\s+/);class np{static open(e){throw Error("AFMFont.open not available on browser build")}static fromJson(e){return new np(e)}constructor(e){"string"==typeof e?(this.contents=e,this.parse()):(this.attributes=e.attributes,this.glyphWidths=e.glyphWidths,this.kernPairs=e.kernPairs),this.charWidths=nf(0,255).map(e=>this.glyphWidths[ng[e]]),this.bbox=Array.from(this.attributes.FontBBox.split(/\s+/)).map(e=>+e),this.ascender=+(this.attributes.Ascender||0),this.descender=+(this.attributes.Descender||0),this.xHeight=+(this.attributes.XHeight||0),this.capHeight=+(this.attributes.CapHeight||0),this.lineGap=this.bbox[3]-this.bbox[1]-(this.ascender-this.descender)}parse(){let e=function(e){let t={attributes:{},glyphWidths:{},kernPairs:{}},r="";for(let c of e.split("\n")){if(n=c.match(/^Start(\w+)/)){r=n[1];continue}if(n=c.match(/^End(\w+)/)){r="";continue}switch(r){case"FontMetrics":var i,n=c.match(/(^\w+)\s+(.*)/),a=n[1],o=n[2];(i=t.attributes[a])?(Array.isArray(i)||(i=t.attributes[a]=[i]),i.push(o)):t.attributes[a]=o;break;case"CharMetrics":if(!/^CH?\s/.test(c))continue;var s=c.match(/\bN\s+(\.?\w+)\s*;/)[1];t.glyphWidths[s]=+c.match(/\bWX\s+(\d+)\s*;/)[1];break;case"KernPairs":(n=c.match(/^KPX\s+(\.?\w+)\s+(\.?\w+)\s+(-?\d+)/))&&(t.kernPairs[n[1]+n[2]]=parseInt(n[3]))}}return t}(this.contents);this.attributes=e.attributes,this.glyphWidths=e.glyphWidths,this.kernPairs=e.kernPairs}encodeText(e){let t=[];for(let r=0,i=e.length,n=0<=i;n?r<i:r>i;n?r++:r--){let i=e.charCodeAt(r);i=nm[i]||i,t.push(i.toString(16))}return t}glyphsForString(e){let t=[];for(let r=0,i=e.length,n=0<=i;n?r<i:r>i;n?r++:r--){let i=e.charCodeAt(r);t.push(this.characterToGlyph(i))}return t}characterToGlyph(e){return ng[nm[e]||e]||".notdef"}widthOfGlyph(e){return this.glyphWidths[e]||0}getKernPair(e,t){return this.kernPairs[e+t]||0}advancesForGlyphs(e){let t=[];for(let r=0;r<e.length;r++){let i=e[r],n=e[r+1];t.push(this.widthOfGlyph(i)+this.getKernPair(i,n))}return t}}let ny=e=>[e.FontName,{attributes:e,glyphWidths:{},kernPairs:{}}],nv=(e=>{let{attributes:t,glyphWidths:r,kernPairs:i}=e,n=t.map(ny);return Object.keys(r).forEach(e=>{r[e].forEach((t,r)=>{t&&(n[r][1].glyphWidths[e]=t)})}),Object.keys(i).forEach(e=>{i[e].forEach((t,r)=>{t&&(n[r][1].kernPairs[e]=t)})}),Object.fromEntries(n)})({attributes:[{Comment:["Copyright (c) 1985, 1987, 1989, 1990, 1997 Adobe Systems Incorporated.  All Rights Reserved.","Creation Date: Thu May  1 12:43:52 1997","UniqueID 43052","VMusage 37169 48194"],FontName:"Helvetica-Bold",FullName:"Helvetica Bold",FamilyName:"Helvetica",Weight:"Bold",ItalicAngle:"0",IsFixedPitch:"false",CharacterSet:"ExtendedRoman",FontBBox:"-170 -228 1003 962 ",UnderlinePosition:"-100",UnderlineThickness:"50",Version:"002.000",Notice:"Copyright (c) 1985, 1987, 1989, 1990, 1997 Adobe Systems Incorporated.  All Rights Reserved.Helvetica is a trademark of Linotype-Hell AG and/or its subsidiaries.",EncodingScheme:"AdobeStandardEncoding",CapHeight:"718",XHeight:"532",Ascender:"718",Descender:"-207",StdHW:"118",StdVW:"140"},{Comment:["Copyright (c) 1985, 1987, 1989, 1990, 1997 Adobe Systems Incorporated.  All Rights Reserved.","Creation Date: Thu May  1 12:45:12 1997","UniqueID 43053","VMusage 14482 68586"],FontName:"Helvetica-BoldOblique",FullName:"Helvetica Bold Oblique",FamilyName:"Helvetica",Weight:"Bold",ItalicAngle:"-12",IsFixedPitch:"false",CharacterSet:"ExtendedRoman",FontBBox:"-174 -228 1114 962",UnderlinePosition:"-100",UnderlineThickness:"50",Version:"002.000",Notice:"Copyright (c) 1985, 1987, 1989, 1990, 1997 Adobe Systems Incorporated.  All Rights Reserved.Helvetica is a trademark of Linotype-Hell AG and/or its subsidiaries.",EncodingScheme:"AdobeStandardEncoding",CapHeight:"718",XHeight:"532",Ascender:"718",Descender:"-207",StdHW:"118",StdVW:"140"},{Comment:["Copyright (c) 1985, 1987, 1989, 1990, 1997 Adobe Systems Incorporated.  All Rights Reserved.","Creation Date: Thu May  1 12:44:31 1997","UniqueID 43055","VMusage 14960 69346"],FontName:"Helvetica-Oblique",FullName:"Helvetica Oblique",FamilyName:"Helvetica",Weight:"Medium",ItalicAngle:"-12",IsFixedPitch:"false",CharacterSet:"ExtendedRoman",FontBBox:"-170 -225 1116 931 ",UnderlinePosition:"-100",UnderlineThickness:"50",Version:"002.000",Notice:"Copyright (c) 1985, 1987, 1989, 1990, 1997 Adobe Systems Incorporated.  All Rights Reserved.Helvetica is a trademark of Linotype-Hell AG and/or its subsidiaries.",EncodingScheme:"AdobeStandardEncoding",CapHeight:"718",XHeight:"523",Ascender:"718",Descender:"-207",StdHW:"76",StdVW:"88"},{Comment:["Copyright (c) 1985, 1987, 1989, 1990, 1997 Adobe Systems Incorporated.  All Rights Reserved.","Creation Date: Thu May  1 12:38:23 1997","UniqueID 43054","VMusage 37069 48094"],FontName:"Helvetica",FullName:"Helvetica",FamilyName:"Helvetica",Weight:"Medium",ItalicAngle:"0",IsFixedPitch:"false",CharacterSet:"ExtendedRoman",FontBBox:"-166 -225 1000 931 ",UnderlinePosition:"-100",UnderlineThickness:"50",Version:"002.000",Notice:"Copyright (c) 1985, 1987, 1989, 1990, 1997 Adobe Systems Incorporated.  All Rights Reserved.Helvetica is a trademark of Linotype-Hell AG and/or its subsidiaries.",EncodingScheme:"AdobeStandardEncoding",CapHeight:"718",XHeight:"523",Ascender:"718",Descender:"-207",StdHW:"76",StdVW:"88"},{Comment:["Copyright (c) 1985, 1987, 1989, 1990, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.","Creation Date: Thu May  1 12:52:56 1997","UniqueID 43065","VMusage 41636 52661"],FontName:"Times-Bold",FullName:"Times Bold",FamilyName:"Times",Weight:"Bold",ItalicAngle:"0",IsFixedPitch:"false",CharacterSet:"ExtendedRoman",FontBBox:"-168 -218 1000 935 ",UnderlinePosition:"-100",UnderlineThickness:"50",Version:"002.000",Notice:"Copyright (c) 1985, 1987, 1989, 1990, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.Times is a trademark of Linotype-Hell AG and/or its subsidiaries.",EncodingScheme:"AdobeStandardEncoding",CapHeight:"676",XHeight:"461",Ascender:"683",Descender:"-217",StdHW:"44",StdVW:"139"},{Comment:["Copyright (c) 1985, 1987, 1989, 1990, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.","Creation Date: Thu May  1 13:04:06 1997","UniqueID 43066","VMusage 45874 56899"],FontName:"Times-BoldItalic",FullName:"Times Bold Italic",FamilyName:"Times",Weight:"Bold",ItalicAngle:"-15",IsFixedPitch:"false",CharacterSet:"ExtendedRoman",FontBBox:"-200 -218 996 921",UnderlinePosition:"-100",UnderlineThickness:"50",Version:"002.000",Notice:"Copyright (c) 1985, 1987, 1989, 1990, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.Times is a trademark of Linotype-Hell AG and/or its subsidiaries.",EncodingScheme:"AdobeStandardEncoding",CapHeight:"669",XHeight:"462",Ascender:"683",Descender:"-217",StdHW:"42",StdVW:"121"},{Comment:["Copyright (c) 1985, 1987, 1989, 1990, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.","Creation Date: Thu May  1 12:56:55 1997","UniqueID 43067","VMusage 47727 58752"],FontName:"Times-Italic",FullName:"Times Italic",FamilyName:"Times",Weight:"Medium",ItalicAngle:"-15.5",IsFixedPitch:"false",CharacterSet:"ExtendedRoman",FontBBox:"-169 -217 1010 883 ",UnderlinePosition:"-100",UnderlineThickness:"50",Version:"002.000",Notice:"Copyright (c) 1985, 1987, 1989, 1990, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.Times is a trademark of Linotype-Hell AG and/or its subsidiaries.",EncodingScheme:"AdobeStandardEncoding",CapHeight:"653",XHeight:"441",Ascender:"683",Descender:"-217",StdHW:"32",StdVW:"76"},{Comment:["Copyright (c) 1985, 1987, 1989, 1990, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.","Creation Date: Thu May  1 12:49:17 1997","UniqueID 43068","VMusage 43909 54934"],FontName:"Times-Roman",FullName:"Times Roman",FamilyName:"Times",Weight:"Roman",ItalicAngle:"0",IsFixedPitch:"false",CharacterSet:"ExtendedRoman",FontBBox:"-168 -218 1000 898 ",UnderlinePosition:"-100",UnderlineThickness:"50",Version:"002.000",Notice:"Copyright (c) 1985, 1987, 1989, 1990, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.Times is a trademark of Linotype-Hell AG and/or its subsidiaries.",EncodingScheme:"AdobeStandardEncoding",CapHeight:"662",XHeight:"450",Ascender:"683",Descender:"-217",StdHW:"28",StdVW:"84"},{Comment:["Copyright (c) 1989, 1990, 1991, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.","Creation Date: Mon Jun 23 16:28:00 1997","UniqueID 43048","VMusage 41139 52164"],FontName:"Courier-Bold",FullName:"Courier Bold",FamilyName:"Courier",Weight:"Bold",ItalicAngle:"0",IsFixedPitch:"true",CharacterSet:"ExtendedRoman",FontBBox:"-113 -250 749 801 ",UnderlinePosition:"-100",UnderlineThickness:"50",Version:"003.000",Notice:"Copyright (c) 1989, 1990, 1991, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.",EncodingScheme:"AdobeStandardEncoding",CapHeight:"562",XHeight:"439",Ascender:"629",Descender:"-157",StdHW:"84",StdVW:"106"},{Comment:["Copyright (c) 1989, 1990, 1991, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.","Creation Date: Mon Jun 23 16:28:46 1997","UniqueID 43049","VMusage 17529 79244"],FontName:"Courier-BoldOblique",FullName:"Courier Bold Oblique",FamilyName:"Courier",Weight:"Bold",ItalicAngle:"-12",IsFixedPitch:"true",CharacterSet:"ExtendedRoman",FontBBox:"-57 -250 869 801",UnderlinePosition:"-100",UnderlineThickness:"50",Version:"003.000",Notice:"Copyright (c) 1989, 1990, 1991, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.",EncodingScheme:"AdobeStandardEncoding",CapHeight:"562",XHeight:"439",Ascender:"629",Descender:"-157",StdHW:"84",StdVW:"106"},{Comment:["Copyright (c) 1989, 1990, 1991, 1992, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.","Creation Date: Thu May  1 17:37:52 1997","UniqueID 43051","VMusage 16248 75829"],FontName:"Courier-Oblique",FullName:"Courier Oblique",FamilyName:"Courier",Weight:"Medium",ItalicAngle:"-12",IsFixedPitch:"true",CharacterSet:"ExtendedRoman",FontBBox:"-27 -250 849 805 ",UnderlinePosition:"-100",UnderlineThickness:"50",Version:"003.000",Notice:"Copyright (c) 1989, 1990, 1991, 1992, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.",EncodingScheme:"AdobeStandardEncoding",CapHeight:"562",XHeight:"426",Ascender:"629",Descender:"-157",StdHW:"51",StdVW:"51"},{Comment:["Copyright (c) 1989, 1990, 1991, 1992, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.","Creation Date: Thu May  1 17:27:09 1997","UniqueID 43050","VMusage 39754 50779"],FontName:"Courier",FullName:"Courier",FamilyName:"Courier",Weight:"Medium",ItalicAngle:"0",IsFixedPitch:"true",CharacterSet:"ExtendedRoman",FontBBox:"-23 -250 715 805 ",UnderlinePosition:"-100",UnderlineThickness:"50",Version:"003.000",Notice:"Copyright (c) 1989, 1990, 1991, 1992, 1993, 1997 Adobe Systems Incorporated.  All Rights Reserved.",EncodingScheme:"AdobeStandardEncoding",CapHeight:"562",XHeight:"426",Ascender:"629",Descender:"-157",StdHW:"51",StdVW:"51"}],glyphWidths:{space:[278,278,278,278,250,250,250,250,600,600,600,600],exclam:[333,333,278,278,333,389,333,333,600,600,600,600],quotedbl:[474,474,355,355,555,555,420,408,600,600,600,600],numbersign:[556,556,556,556,500,500,500,500,600,600,600,600],dollar:[556,556,556,556,500,500,500,500,600,600,600,600],percent:[889,889,889,889,1e3,833,833,833,600,600,600,600],ampersand:[722,722,667,667,833,778,778,778,600,600,600,600],quoteright:[278,278,222,222,333,333,333,333,600,600,600,600],parenleft:[333,333,333,333,333,333,333,333,600,600,600,600],parenright:[333,333,333,333,333,333,333,333,600,600,600,600],asterisk:[389,389,389,389,500,500,500,500,600,600,600,600],plus:[584,584,584,584,570,570,675,564,600,600,600,600],comma:[278,278,278,278,250,250,250,250,600,600,600,600],hyphen:[333,333,333,333,333,333,333,333,600,600,600,600],period:[278,278,278,278,250,250,250,250,600,600,600,600],slash:[278,278,278,278,278,278,278,278,600,600,600,600],zero:[556,556,556,556,500,500,500,500,600,600,600,600],one:[556,556,556,556,500,500,500,500,600,600,600,600],two:[556,556,556,556,500,500,500,500,600,600,600,600],three:[556,556,556,556,500,500,500,500,600,600,600,600],four:[556,556,556,556,500,500,500,500,600,600,600,600],five:[556,556,556,556,500,500,500,500,600,600,600,600],six:[556,556,556,556,500,500,500,500,600,600,600,600],seven:[556,556,556,556,500,500,500,500,600,600,600,600],eight:[556,556,556,556,500,500,500,500,600,600,600,600],nine:[556,556,556,556,500,500,500,500,600,600,600,600],colon:[333,333,278,278,333,333,333,278,600,600,600,600],semicolon:[333,333,278,278,333,333,333,278,600,600,600,600],less:[584,584,584,584,570,570,675,564,600,600,600,600],equal:[584,584,584,584,570,570,675,564,600,600,600,600],greater:[584,584,584,584,570,570,675,564,600,600,600,600],question:[611,611,556,556,500,500,500,444,600,600,600,600],at:[975,975,1015,1015,930,832,920,921,600,600,600,600],A:[722,722,667,667,722,667,611,722,600,600,600,600],B:[722,722,667,667,667,667,611,667,600,600,600,600],C:[722,722,722,722,722,667,667,667,600,600,600,600],D:[722,722,722,722,722,722,722,722,600,600,600,600],E:[667,667,667,667,667,667,611,611,600,600,600,600],F:[611,611,611,611,611,667,611,556,600,600,600,600],G:[778,778,778,778,778,722,722,722,600,600,600,600],H:[722,722,722,722,778,778,722,722,600,600,600,600],I:[278,278,278,278,389,389,333,333,600,600,600,600],J:[556,556,500,500,500,500,444,389,600,600,600,600],K:[722,722,667,667,778,667,667,722,600,600,600,600],L:[611,611,556,556,667,611,556,611,600,600,600,600],M:[833,833,833,833,944,889,833,889,600,600,600,600],N:[722,722,722,722,722,722,667,722,600,600,600,600],O:[778,778,778,778,778,722,722,722,600,600,600,600],P:[667,667,667,667,611,611,611,556,600,600,600,600],Q:[778,778,778,778,778,722,722,722,600,600,600,600],R:[722,722,722,722,722,667,611,667,600,600,600,600],S:[667,667,667,667,556,556,500,556,600,600,600,600],T:[611,611,611,611,667,611,556,611,600,600,600,600],U:[722,722,722,722,722,722,722,722,600,600,600,600],V:[667,667,667,667,722,667,611,722,600,600,600,600],W:[944,944,944,944,1e3,889,833,944,600,600,600,600],X:[667,667,667,667,722,667,611,722,600,600,600,600],Y:[667,667,667,667,722,611,556,722,600,600,600,600],Z:[611,611,611,611,667,611,556,611,600,600,600,600],bracketleft:[333,333,278,278,333,333,389,333,600,600,600,600],backslash:[278,278,278,278,278,278,278,278,600,600,600,600],bracketright:[333,333,278,278,333,333,389,333,600,600,600,600],asciicircum:[584,584,469,469,581,570,422,469,600,600,600,600],underscore:[556,556,556,556,500,500,500,500,600,600,600,600],quoteleft:[278,278,222,222,333,333,333,333,600,600,600,600],a:[556,556,556,556,500,500,500,444,600,600,600,600],b:[611,611,556,556,556,500,500,500,600,600,600,600],c:[556,556,500,500,444,444,444,444,600,600,600,600],d:[611,611,556,556,556,500,500,500,600,600,600,600],e:[556,556,556,556,444,444,444,444,600,600,600,600],f:[333,333,278,278,333,333,278,333,600,600,600,600],g:[611,611,556,556,500,500,500,500,600,600,600,600],h:[611,611,556,556,556,556,500,500,600,600,600,600],i:[278,278,222,222,278,278,278,278,600,600,600,600],j:[278,278,222,222,333,278,278,278,600,600,600,600],k:[556,556,500,500,556,500,444,500,600,600,600,600],l:[278,278,222,222,278,278,278,278,600,600,600,600],m:[889,889,833,833,833,778,722,778,600,600,600,600],n:[611,611,556,556,556,556,500,500,600,600,600,600],o:[611,611,556,556,500,500,500,500,600,600,600,600],p:[611,611,556,556,556,500,500,500,600,600,600,600],q:[611,611,556,556,556,500,500,500,600,600,600,600],r:[389,389,333,333,444,389,389,333,600,600,600,600],s:[556,556,500,500,389,389,389,389,600,600,600,600],t:[333,333,278,278,333,278,278,278,600,600,600,600],u:[611,611,556,556,556,556,500,500,600,600,600,600],v:[556,556,500,500,500,444,444,500,600,600,600,600],w:[778,778,722,722,722,667,667,722,600,600,600,600],x:[556,556,500,500,500,500,444,500,600,600,600,600],y:[556,556,500,500,500,444,444,500,600,600,600,600],z:[500,500,500,500,444,389,389,444,600,600,600,600],braceleft:[389,389,334,334,394,348,400,480,600,600,600,600],bar:[280,280,260,260,220,220,275,200,600,600,600,600],braceright:[389,389,334,334,394,348,400,480,600,600,600,600],asciitilde:[584,584,584,584,520,570,541,541,600,600,600,600],exclamdown:[333,333,333,333,333,389,389,333,600,600,600,600],cent:[556,556,556,556,500,500,500,500,600,600,600,600],sterling:[556,556,556,556,500,500,500,500,600,600,600,600],fraction:[167,167,167,167,167,167,167,167,600,600,600,600],yen:[556,556,556,556,500,500,500,500,600,600,600,600],florin:[556,556,556,556,500,500,500,500,600,600,600,600],section:[556,556,556,556,500,500,500,500,600,600,600,600],currency:[556,556,556,556,500,500,500,500,600,600,600,600],quotesingle:[238,238,191,191,278,278,214,180,600,600,600,600],quotedblleft:[500,500,333,333,500,500,556,444,600,600,600,600],guillemotleft:[556,556,556,556,500,500,500,500,600,600,600,600],guilsinglleft:[333,333,333,333,333,333,333,333,600,600,600,600],guilsinglright:[333,333,333,333,333,333,333,333,600,600,600,600],fi:[611,611,500,500,556,556,500,556,600,600,600,600],fl:[611,611,500,500,556,556,500,556,600,600,600,600],endash:[556,556,556,556,500,500,500,500,600,600,600,600],dagger:[556,556,556,556,500,500,500,500,600,600,600,600],daggerdbl:[556,556,556,556,500,500,500,500,600,600,600,600],periodcentered:[278,278,278,278,250,250,250,250,600,600,600,600],paragraph:[556,556,537,537,540,500,523,453,600,600,600,600],bullet:[350,350,350,350,350,350,350,350,600,600,600,600],quotesinglbase:[278,278,222,222,333,333,333,333,600,600,600,600],quotedblbase:[500,500,333,333,500,500,556,444,600,600,600,600],quotedblright:[500,500,333,333,500,500,556,444,600,600,600,600],guillemotright:[556,556,556,556,500,500,500,500,600,600,600,600],ellipsis:[1e3,1e3,1e3,1e3,1e3,1e3,889,1e3,600,600,600,600],perthousand:[1e3,1e3,1e3,1e3,1e3,1e3,1e3,1e3,600,600,600,600],questiondown:[611,611,611,611,500,500,500,444,600,600,600,600],grave:[333,333,333,333,333,333,333,333,600,600,600,600],acute:[333,333,333,333,333,333,333,333,600,600,600,600],circumflex:[333,333,333,333,333,333,333,333,600,600,600,600],tilde:[333,333,333,333,333,333,333,333,600,600,600,600],macron:[333,333,333,333,333,333,333,333,600,600,600,600],breve:[333,333,333,333,333,333,333,333,600,600,600,600],dotaccent:[333,333,333,333,333,333,333,333,600,600,600,600],dieresis:[333,333,333,333,333,333,333,333,600,600,600,600],ring:[333,333,333,333,333,333,333,333,600,600,600,600],cedilla:[333,333,333,333,333,333,333,333,600,600,600,600],hungarumlaut:[333,333,333,333,333,333,333,333,600,600,600,600],ogonek:[333,333,333,333,333,333,333,333,600,600,600,600],caron:[333,333,333,333,333,333,333,333,600,600,600,600],emdash:[1e3,1e3,1e3,1e3,1e3,1e3,889,1e3,600,600,600,600],AE:[1e3,1e3,1e3,1e3,1e3,944,889,889,600,600,600,600],ordfeminine:[370,370,370,370,300,266,276,276,600,600,600,600],Lslash:[611,611,556,556,667,611,556,611,600,600,600,600],Oslash:[778,778,778,778,778,722,722,722,600,600,600,600],OE:[1e3,1e3,1e3,1e3,1e3,944,944,889,600,600,600,600],ordmasculine:[365,365,365,365,330,300,310,310,600,600,600,600],ae:[889,889,889,889,722,722,667,667,600,600,600,600],dotlessi:[278,278,278,278,278,278,278,278,600,600,600,600],lslash:[278,278,222,222,278,278,278,278,600,600,600,600],oslash:[611,611,611,611,500,500,500,500,600,600,600,600],oe:[944,944,944,944,722,722,667,722,600,600,600,600],germandbls:[611,611,611,611,556,500,500,500,600,600,600,600],Idieresis:[278,278,278,278,389,389,333,333,600,600,600,600],eacute:[556,556,556,556,444,444,444,444,600,600,600,600],abreve:[556,556,556,556,500,500,500,444,600,600,600,600],uhungarumlaut:[611,611,556,556,556,556,500,500,600,600,600,600],ecaron:[556,556,556,556,444,444,444,444,600,600,600,600],Ydieresis:[667,667,667,667,722,611,556,722,600,600,600,600],divide:[584,584,584,584,570,570,675,564,600,600,600,600],Yacute:[667,667,667,667,722,611,556,722,600,600,600,600],Acircumflex:[722,722,667,667,722,667,611,722,600,600,600,600],aacute:[556,556,556,556,500,500,500,444,600,600,600,600],Ucircumflex:[722,722,722,722,722,722,722,722,600,600,600,600],yacute:[556,556,500,500,500,444,444,500,600,600,600,600],scommaaccent:[556,556,500,500,389,389,389,389,600,600,600,600],ecircumflex:[556,556,556,556,444,444,444,444,600,600,600,600],Uring:[722,722,722,722,722,722,722,722,600,600,600,600],Udieresis:[722,722,722,722,722,722,722,722,600,600,600,600],aogonek:[556,556,556,556,500,500,500,444,600,600,600,600],Uacute:[722,722,722,722,722,722,722,722,600,600,600,600],uogonek:[611,611,556,556,556,556,500,500,600,600,600,600],Edieresis:[667,667,667,667,667,667,611,611,600,600,600,600],Dcroat:[722,722,722,722,722,722,722,722,600,600,600,600],commaaccent:[250,250,250,250,250,250,250,250,600,600,600,600],copyright:[737,737,737,737,747,747,760,760,600,600,600,600],Emacron:[667,667,667,667,667,667,611,611,600,600,600,600],ccaron:[556,556,500,500,444,444,444,444,600,600,600,600],aring:[556,556,556,556,500,500,500,444,600,600,600,600],Ncommaaccent:[722,722,722,722,722,722,667,722,600,600,600,600],lacute:[278,278,222,222,278,278,278,278,600,600,600,600],agrave:[556,556,556,556,500,500,500,444,600,600,600,600],Tcommaaccent:[611,611,611,611,667,611,556,611,600,600,600,600],Cacute:[722,722,722,722,722,667,667,667,600,600,600,600],atilde:[556,556,556,556,500,500,500,444,600,600,600,600],Edotaccent:[667,667,667,667,667,667,611,611,600,600,600,600],scaron:[556,556,500,500,389,389,389,389,600,600,600,600],scedilla:[556,556,500,500,389,389,389,389,600,600,600,600],iacute:[278,278,278,278,278,278,278,278,600,600,600,600],lozenge:[494,494,471,471,494,494,471,471,600,600,600,600],Rcaron:[722,722,722,722,722,667,611,667,600,600,600,600],Gcommaaccent:[778,778,778,778,778,722,722,722,600,600,600,600],ucircumflex:[611,611,556,556,556,556,500,500,600,600,600,600],acircumflex:[556,556,556,556,500,500,500,444,600,600,600,600],Amacron:[722,722,667,667,722,667,611,722,600,600,600,600],rcaron:[389,389,333,333,444,389,389,333,600,600,600,600],ccedilla:[556,556,500,500,444,444,444,444,600,600,600,600],Zdotaccent:[611,611,611,611,667,611,556,611,600,600,600,600],Thorn:[667,667,667,667,611,611,611,556,600,600,600,600],Omacron:[778,778,778,778,778,722,722,722,600,600,600,600],Racute:[722,722,722,722,722,667,611,667,600,600,600,600],Sacute:[667,667,667,667,556,556,500,556,600,600,600,600],dcaron:[743,743,643,643,672,608,544,588,600,600,600,600],Umacron:[722,722,722,722,722,722,722,722,600,600,600,600],uring:[611,611,556,556,556,556,500,500,600,600,600,600],threesuperior:[333,333,333,333,300,300,300,300,600,600,600,600],Ograve:[778,778,778,778,778,722,722,722,600,600,600,600],Agrave:[722,722,667,667,722,667,611,722,600,600,600,600],Abreve:[722,722,667,667,722,667,611,722,600,600,600,600],multiply:[584,584,584,584,570,570,675,564,600,600,600,600],uacute:[611,611,556,556,556,556,500,500,600,600,600,600],Tcaron:[611,611,611,611,667,611,556,611,600,600,600,600],partialdiff:[494,494,476,476,494,494,476,476,600,600,600,600],ydieresis:[556,556,500,500,500,444,444,500,600,600,600,600],Nacute:[722,722,722,722,722,722,667,722,600,600,600,600],icircumflex:[278,278,278,278,278,278,278,278,600,600,600,600],Ecircumflex:[667,667,667,667,667,667,611,611,600,600,600,600],adieresis:[556,556,556,556,500,500,500,444,600,600,600,600],edieresis:[556,556,556,556,444,444,444,444,600,600,600,600],cacute:[556,556,500,500,444,444,444,444,600,600,600,600],nacute:[611,611,556,556,556,556,500,500,600,600,600,600],umacron:[611,611,556,556,556,556,500,500,600,600,600,600],Ncaron:[722,722,722,722,722,722,667,722,600,600,600,600],Iacute:[278,278,278,278,389,389,333,333,600,600,600,600],plusminus:[584,584,584,584,570,570,675,564,600,600,600,600],brokenbar:[280,280,260,260,220,220,275,200,600,600,600,600],registered:[737,737,737,737,747,747,760,760,600,600,600,600],Gbreve:[778,778,778,778,778,722,722,722,600,600,600,600],Idotaccent:[278,278,278,278,389,389,333,333,600,600,600,600],summation:[600,600,600,600,600,600,600,600,600,600,600,600],Egrave:[667,667,667,667,667,667,611,611,600,600,600,600],racute:[389,389,333,333,444,389,389,333,600,600,600,600],omacron:[611,611,556,556,500,500,500,500,600,600,600,600],Zacute:[611,611,611,611,667,611,556,611,600,600,600,600],Zcaron:[611,611,611,611,667,611,556,611,600,600,600,600],greaterequal:[549,549,549,549,549,549,549,549,600,600,600,600],Eth:[722,722,722,722,722,722,722,722,600,600,600,600],Ccedilla:[722,722,722,722,722,667,667,667,600,600,600,600],lcommaaccent:[278,278,222,222,278,278,278,278,600,600,600,600],tcaron:[389,389,317,317,416,366,300,326,600,600,600,600],eogonek:[556,556,556,556,444,444,444,444,600,600,600,600],Uogonek:[722,722,722,722,722,722,722,722,600,600,600,600],Aacute:[722,722,667,667,722,667,611,722,600,600,600,600],Adieresis:[722,722,667,667,722,667,611,722,600,600,600,600],egrave:[556,556,556,556,444,444,444,444,600,600,600,600],zacute:[500,500,500,500,444,389,389,444,600,600,600,600],iogonek:[278,278,222,222,278,278,278,278,600,600,600,600],Oacute:[778,778,778,778,778,722,722,722,600,600,600,600],oacute:[611,611,556,556,500,500,500,500,600,600,600,600],amacron:[556,556,556,556,500,500,500,444,600,600,600,600],sacute:[556,556,500,500,389,389,389,389,600,600,600,600],idieresis:[278,278,278,278,278,278,278,278,600,600,600,600],Ocircumflex:[778,778,778,778,778,722,722,722,600,600,600,600],Ugrave:[722,722,722,722,722,722,722,722,600,600,600,600],Delta:[612,612,612,612,612,612,612,612,600,600,600,600],thorn:[611,611,556,556,556,500,500,500,600,600,600,600],twosuperior:[333,333,333,333,300,300,300,300,600,600,600,600],Odieresis:[778,778,778,778,778,722,722,722,600,600,600,600],mu:[611,611,556,556,556,576,500,500,600,600,600,600],igrave:[278,278,278,278,278,278,278,278,600,600,600,600],ohungarumlaut:[611,611,556,556,500,500,500,500,600,600,600,600],Eogonek:[667,667,667,667,667,667,611,611,600,600,600,600],dcroat:[611,611,556,556,556,500,500,500,600,600,600,600],threequarters:[834,834,834,834,750,750,750,750,600,600,600,600],Scedilla:[667,667,667,667,556,556,500,556,600,600,600,600],lcaron:[400,400,299,299,394,382,300,344,600,600,600,600],Kcommaaccent:[722,722,667,667,778,667,667,722,600,600,600,600],Lacute:[611,611,556,556,667,611,556,611,600,600,600,600],trademark:[1e3,1e3,1e3,1e3,1e3,1e3,980,980,600,600,600,600],edotaccent:[556,556,556,556,444,444,444,444,600,600,600,600],Igrave:[278,278,278,278,389,389,333,333,600,600,600,600],Imacron:[278,278,278,278,389,389,333,333,600,600,600,600],Lcaron:[611,611,556,556,667,611,611,611,600,600,600,600],onehalf:[834,834,834,834,750,750,750,750,600,600,600,600],lessequal:[549,549,549,549,549,549,549,549,600,600,600,600],ocircumflex:[611,611,556,556,500,500,500,500,600,600,600,600],ntilde:[611,611,556,556,556,556,500,500,600,600,600,600],Uhungarumlaut:[722,722,722,722,722,722,722,722,600,600,600,600],Eacute:[667,667,667,667,667,667,611,611,600,600,600,600],emacron:[556,556,556,556,444,444,444,444,600,600,600,600],gbreve:[611,611,556,556,500,500,500,500,600,600,600,600],onequarter:[834,834,834,834,750,750,750,750,600,600,600,600],Scaron:[667,667,667,667,556,556,500,556,600,600,600,600],Scommaaccent:[667,667,667,667,556,556,500,556,600,600,600,600],Ohungarumlaut:[778,778,778,778,778,722,722,722,600,600,600,600],degree:[400,400,400,400,400,400,400,400,600,600,600,600],ograve:[611,611,556,556,500,500,500,500,600,600,600,600],Ccaron:[722,722,722,722,722,667,667,667,600,600,600,600],ugrave:[611,611,556,556,556,556,500,500,600,600,600,600],radical:[549,549,453,453,549,549,453,453,600,600,600,600],Dcaron:[722,722,722,722,722,722,722,722,600,600,600,600],rcommaaccent:[389,389,333,333,444,389,389,333,600,600,600,600],Ntilde:[722,722,722,722,722,722,667,722,600,600,600,600],otilde:[611,611,556,556,500,500,500,500,600,600,600,600],Rcommaaccent:[722,722,722,722,722,667,611,667,600,600,600,600],Lcommaaccent:[611,611,556,556,667,611,556,611,600,600,600,600],Atilde:[722,722,667,667,722,667,611,722,600,600,600,600],Aogonek:[722,722,667,667,722,667,611,722,600,600,600,600],Aring:[722,722,667,667,722,667,611,722,600,600,600,600],Otilde:[778,778,778,778,778,722,722,722,600,600,600,600],zdotaccent:[500,500,500,500,444,389,389,444,600,600,600,600],Ecaron:[667,667,667,667,667,667,611,611,600,600,600,600],Iogonek:[278,278,278,278,389,389,333,333,600,600,600,600],kcommaaccent:[556,556,500,500,556,500,444,500,600,600,600,600],minus:[584,584,584,584,570,606,675,564,600,600,600,600],Icircumflex:[278,278,278,278,389,389,333,333,600,600,600,600],ncaron:[611,611,556,556,556,556,500,500,600,600,600,600],tcommaaccent:[333,333,278,278,333,278,278,278,600,600,600,600],logicalnot:[584,584,584,584,570,606,675,564,600,600,600,600],odieresis:[611,611,556,556,500,500,500,500,600,600,600,600],udieresis:[611,611,556,556,556,556,500,500,600,600,600,600],notequal:[549,549,549,549,549,549,549,549,600,600,600,600],gcommaaccent:[611,611,556,556,500,500,500,500,600,600,600,600],eth:[611,611,556,556,500,500,500,500,600,600,600,600],zcaron:[500,500,500,500,444,389,389,444,600,600,600,600],ncommaaccent:[611,611,556,556,556,556,500,500,600,600,600,600],onesuperior:[333,333,333,333,300,300,300,300,600,600,600,600],imacron:[278,278,278,278,278,278,278,278,600,600,600,600],Euro:[556,556,556,556,500,500,500,500,600,600,600,600]},kernPairs:{AC:[-40,-40,-30,-30,-55,-65,-30,-40],ACacute:[-40,-40,-30,-30,-55,-65,-30,-40],ACcaron:[-40,-40,-30,-30,-55,-65,-30,-40],ACcedilla:[-40,-40,-30,-30,-55,-65,-30,-40],AG:[-50,-50,-30,-30,-55,-60,-35,-40],AGbreve:[-50,-50,-30,-30,-55,-60,-35,-40],AGcommaaccent:[-50,-50,-30,-30,-55,-60,-35,-40],AO:[-40,-40,-30,-30,-45,-50,-40,-55],AOacute:[-40,-40,-30,-30,-45,-50,-40,-55],AOcircumflex:[-40,-40,-30,-30,-45,-50,-40,-55],AOdieresis:[-40,-40,-30,-30,-45,-50,-40,-55],AOgrave:[-40,-40,-30,-30,-45,-50,-40,-55],AOhungarumlaut:[-40,-40,-30,-30,-45,-50,-40,-55],AOmacron:[-40,-40,-30,-30,-45,-50,-40,-55],AOslash:[-40,-40,-30,-30,-45,-50,-40,-55],AOtilde:[-40,-40,-30,-30,-45,-50,-40,-55],AQ:[-40,-40,-30,-30,-45,-55,-40,-55],AT:[-90,-90,-120,-120,-95,-55,-37,-111],ATcaron:[-90,-90,-120,-120,-95,-55,-37,-111],ATcommaaccent:[-90,-90,-120,-120,-95,-55,-37,-111],AU:[-50,-50,-50,-50,-50,-50,-50,-55],AUacute:[-50,-50,-50,-50,-50,-50,-50,-55],AUcircumflex:[-50,-50,-50,-50,-50,-50,-50,-55],AUdieresis:[-50,-50,-50,-50,-50,-50,-50,-55],AUgrave:[-50,-50,-50,-50,-50,-50,-50,-55],AUhungarumlaut:[-50,-50,-50,-50,-50,-50,-50,-55],AUmacron:[-50,-50,-50,-50,-50,-50,-50,-55],AUogonek:[-50,-50,-50,-50,-50,-50,-50,-55],AUring:[-50,-50,-50,-50,-50,-50,-50,-55],AV:[-80,-80,-70,-70,-145,-95,-105,-135],AW:[-60,-60,-50,-50,-130,-100,-95,-90],AY:[-110,-110,-100,-100,-100,-70,-55,-105],AYacute:[-110,-110,-100,-100,-100,-70,-55,-105],AYdieresis:[-110,-110,-100,-100,-100,-70,-55,-105],Au:[-30,-30,-30,-30,-50,-30,-20],Auacute:[-30,-30,-30,-30,-50,-30,-20],Aucircumflex:[-30,-30,-30,-30,-50,-30,-20],Audieresis:[-30,-30,-30,-30,-50,-30,-20],Augrave:[-30,-30,-30,-30,-50,-30,-20],Auhungarumlaut:[-30,-30,-30,-30,-50,-30,-20],Aumacron:[-30,-30,-30,-30,-50,-30,-20],Auogonek:[-30,-30,-30,-30,-50,-30,-20],Auring:[-30,-30,-30,-30,-50,-30,-20],Av:[-40,-40,-40,-40,-100,-74,-55,-74],Aw:[-30,-30,-40,-40,-90,-74,-55,-92],Ay:[-30,-30,-40,-40,-74,-74,-55,-92],Ayacute:[-30,-30,-40,-40,-74,-74,-55,-92],Aydieresis:[-30,-30,-40,-40,-74,-74,-55,-92],AacuteC:[-40,-40,-30,-30,-55,-65,-30,-40],AacuteCacute:[-40,-40,-30,-30,-55,-65,-30,-40],AacuteCcaron:[-40,-40,-30,-30,-55,-65,-30,-40],AacuteCcedilla:[-40,-40,-30,-30,-55,-65,-30,-40],AacuteG:[-50,-50,-30,-30,-55,-60,-35,-40],AacuteGbreve:[-50,-50,-30,-30,-55,-60,-35,-40],AacuteGcommaaccent:[-50,-50,-30,-30,-55,-60,-35,-40],AacuteO:[-40,-40,-30,-30,-45,-50,-40,-55],AacuteOacute:[-40,-40,-30,-30,-45,-50,-40,-55],AacuteOcircumflex:[-40,-40,-30,-30,-45,-50,-40,-55],AacuteOdieresis:[-40,-40,-30,-30,-45,-50,-40,-55],AacuteOgrave:[-40,-40,-30,-30,-45,-50,-40,-55],AacuteOhungarumlaut:[-40,-40,-30,-30,-45,-50,-40,-55],AacuteOmacron:[-40,-40,-30,-30,-45,-50,-40,-55],AacuteOslash:[-40,-40,-30,-30,-45,-50,-40,-55],AacuteOtilde:[-40,-40,-30,-30,-45,-50,-40,-55],AacuteQ:[-40,-40,-30,-30,-45,-55,-40,-55],AacuteT:[-90,-90,-120,-120,-95,-55,-37,-111],AacuteTcaron:[-90,-90,-120,-120,-95,-55,-37,-111],AacuteTcommaaccent:[-90,-90,-120,-120,-95,-55,-37,-111],AacuteU:[-50,-50,-50,-50,-50,-50,-50,-55],AacuteUacute:[-50,-50,-50,-50,-50,-50,-50,-55],AacuteUcircumflex:[-50,-50,-50,-50,-50,-50,-50,-55],AacuteUdieresis:[-50,-50,-50,-50,-50,-50,-50,-55],AacuteUgrave:[-50,-50,-50,-50,-50,-50,-50,-55],AacuteUhungarumlaut:[-50,-50,-50,-50,-50,-50,-50,-55],AacuteUmacron:[-50,-50,-50,-50,-50,-50,-50,-55],AacuteUogonek:[-50,-50,-50,-50,-50,-50,-50,-55],AacuteUring:[-50,-50,-50,-50,-50,-50,-50,-55],AacuteV:[-80,-80,-70,-70,-145,-95,-105,-135],AacuteW:[-60,-60,-50,-50,-130,-100,-95,-90],AacuteY:[-110,-110,-100,-100,-100,-70,-55,-105],AacuteYacute:[-110,-110,-100,-100,-100,-70,-55,-105],AacuteYdieresis:[-110,-110,-100,-100,-100,-70,-55,-105],Aacuteu:[-30,-30,-30,-30,-50,-30,-20],Aacuteuacute:[-30,-30,-30,-30,-50,-30,-20],Aacuteucircumflex:[-30,-30,-30,-30,-50,-30,-20],Aacuteudieresis:[-30,-30,-30,-30,-50,-30,-20],Aacuteugrave:[-30,-30,-30,-30,-50,-30,-20],Aacuteuhungarumlaut:[-30,-30,-30,-30,-50,-30,-20],Aacuteumacron:[-30,-30,-30,-30,-50,-30,-20],Aacuteuogonek:[-30,-30,-30,-30,-50,-30,-20],Aacuteuring:[-30,-30,-30,-30,-50,-30,-20],Aacutev:[-40,-40,-40,-40,-100,-74,-55,-74],Aacutew:[-30,-30,-40,-40,-90,-74,-55,-92],Aacutey:[-30,-30,-40,-40,-74,-74,-55,-92],Aacuteyacute:[-30,-30,-40,-40,-74,-74,-55,-92],Aacuteydieresis:[-30,-30,-40,-40,-74,-74,-55,-92],AbreveC:[-40,-40,-30,-30,-55,-65,-30,-40],AbreveCacute:[-40,-40,-30,-30,-55,-65,-30,-40],AbreveCcaron:[-40,-40,-30,-30,-55,-65,-30,-40],AbreveCcedilla:[-40,-40,-30,-30,-55,-65,-30,-40],AbreveG:[-50,-50,-30,-30,-55,-60,-35,-40],AbreveGbreve:[-50,-50,-30,-30,-55,-60,-35,-40],AbreveGcommaaccent:[-50,-50,-30,-30,-55,-60,-35,-40],AbreveO:[-40,-40,-30,-30,-45,-50,-40,-55],AbreveOacute:[-40,-40,-30,-30,-45,-50,-40,-55],AbreveOcircumflex:[-40,-40,-30,-30,-45,-50,-40,-55],AbreveOdieresis:[-40,-40,-30,-30,-45,-50,-40,-55],AbreveOgrave:[-40,-40,-30,-30,-45,-50,-40,-55],AbreveOhungarumlaut:[-40,-40,-30,-30,-45,-50,-40,-55],AbreveOmacron:[-40,-40,-30,-30,-45,-50,-40,-55],AbreveOslash:[-40,-40,-30,-30,-45,-50,-40,-55],AbreveOtilde:[-40,-40,-30,-30,-45,-50,-40,-55],AbreveQ:[-40,-40,-30,-30,-45,-55,-40,-55],AbreveT:[-90,-90,-120,-120,-95,-55,-37,-111],AbreveTcaron:[-90,-90,-120,-120,-95,-55,-37,-111],AbreveTcommaaccent:[-90,-90,-120,-120,-95,-55,-37,-111],AbreveU:[-50,-50,-50,-50,-50,-50,-50,-55],AbreveUacute:[-50,-50,-50,-50,-50,-50,-50,-55],AbreveUcircumflex:[-50,-50,-50,-50,-50,-50,-50,-55],AbreveUdieresis:[-50,-50,-50,-50,-50,-50,-50,-55],AbreveUgrave:[-50,-50,-50,-50,-50,-50,-50,-55],AbreveUhungarumlaut:[-50,-50,-50,-50,-50,-50,-50,-55],AbreveUmacron:[-50,-50,-50,-50,-50,-50,-50,-55],AbreveUogonek:[-50,-50,-50,-50,-50,-50,-50,-55],AbreveUring:[-50,-50,-50,-50,-50,-50,-50,-55],AbreveV:[-80,-80,-70,-70,-145,-95,-105,-135],AbreveW:[-60,-60,-50,-50,-130,-100,-95,-90],AbreveY:[-110,-110,-100,-100,-100,-70,-55,-105],AbreveYacute:[-110,-110,-100,-100,-100,-70,-55,-105],AbreveYdieresis:[-110,-110,-100,-100,-100,-70,-55,-105],Abreveu:[-30,-30,-30,-30,-50,-30,-20],Abreveuacute:[-30,-30,-30,-30,-50,-30,-20],Abreveucircumflex:[-30,-30,-30,-30,-50,-30,-20],Abreveudieresis:[-30,-30,-30,-30,-50,-30,-20],Abreveugrave:[-30,-30,-30,-30,-50,-30,-20],Abreveuhungarumlaut:[-30,-30,-30,-30,-50,-30,-20],Abreveumacron:[-30,-30,-30,-30,-50,-30,-20],Abreveuogonek:[-30,-30,-30,-30,-50,-30,-20],Abreveuring:[-30,-30,-30,-30,-50,-30,-20],Abrevev:[-40,-40,-40,-40,-100,-74,-55,-74],Abrevew:[-30,-30,-40,-40,-90,-74,-55,-92],Abrevey:[-30,-30,-40,-40,-74,-74,-55,-92],Abreveyacute:[-30,-30,-40,-40,-74,-74,-55,-92],Abreveydieresis:[-30,-30,-40,-40,-74,-74,-55,-92],AcircumflexC:[-40,-40,-30,-30,-55,-65,-30,-40],AcircumflexCacute:[-40,-40,-30,-30,-55,-65,-30,-40],AcircumflexCcaron:[-40,-40,-30,-30,-55,-65,-30,-40],AcircumflexCcedilla:[-40,-40,-30,-30,-55,-65,-30,-40],AcircumflexG:[-50,-50,-30,-30,-55,-60,-35,-40],AcircumflexGbreve:[-50,-50,-30,-30,-55,-60,-35,-40],AcircumflexGcommaaccent:[-50,-50,-30,-30,-55,-60,-35,-40],AcircumflexO:[-40,-40,-30,-30,-45,-50,-40,-55],AcircumflexOacute:[-40,-40,-30,-30,-45,-50,-40,-55],AcircumflexOcircumflex:[-40,-40,-30,-30,-45,-50,-40,-55],AcircumflexOdieresis:[-40,-40,-30,-30,-45,-50,-40,-55],AcircumflexOgrave:[-40,-40,-30,-30,-45,-50,-40,-55],AcircumflexOhungarumlaut:[-40,-40,-30,-30,-45,-50,-40,-55],AcircumflexOmacron:[-40,-40,-30,-30,-45,-50,-40,-55],AcircumflexOslash:[-40,-40,-30,-30,-45,-50,-40,-55],AcircumflexOtilde:[-40,-40,-30,-30,-45,-50,-40,-55],AcircumflexQ:[-40,-40,-30,-30,-45,-55,-40,-55],AcircumflexT:[-90,-90,-120,-120,-95,-55,-37,-111],AcircumflexTcaron:[-90,-90,-120,-120,-95,-55,-37,-111],AcircumflexTcommaaccent:[-90,-90,-120,-120,-95,-55,-37,-111],AcircumflexU:[-50,-50,-50,-50,-50,-50,-50,-55],AcircumflexUacute:[-50,-50,-50,-50,-50,-50,-50,-55],AcircumflexUcircumflex:[-50,-50,-50,-50,-50,-50,-50,-55],AcircumflexUdieresis:[-50,-50,-50,-50,-50,-50,-50,-55],AcircumflexUgrave:[-50,-50,-50,-50,-50,-50,-50,-55],AcircumflexUhungarumlaut:[-50,-50,-50,-50,-50,-50,-50,-55],AcircumflexUmacron:[-50,-50,-50,-50,-50,-50,-50,-55],AcircumflexUogonek:[-50,-50,-50,-50,-50,-50,-50,-55],AcircumflexUring:[-50,-50,-50,-50,-50,-50,-50,-55],AcircumflexV:[-80,-80,-70,-70,-145,-95,-105,-135],AcircumflexW:[-60,-60,-50,-50,-130,-100,-95,-90],AcircumflexY:[-110,-110,-100,-100,-100,-70,-55,-105],AcircumflexYacute:[-110,-110,-100,-100,-100,-70,-55,-105],AcircumflexYdieresis:[-110,-110,-100,-100,-100,-70,-55,-105],Acircumflexu:[-30,-30,-30,-30,-50,-30,-20],Acircumflexuacute:[-30,-30,-30,-30,-50,-30,-20],Acircumflexucircumflex:[-30,-30,-30,-30,-50,-30,-20],Acircumflexudieresis:[-30,-30,-30,-30,-50,-30,-20],Acircumflexugrave:[-30,-30,-30,-30,-50,-30,-20],Acircumflexuhungarumlaut:[-30,-30,-30,-30,-50,-30,-20],Acircumflexumacron:[-30,-30,-30,-30,-50,-30,-20],Acircumflexuogonek:[-30,-30,-30,-30,-50,-30,-20],Acircumflexuring:[-30,-30,-30,-30,-50,-30,-20],Acircumflexv:[-40,-40,-40,-40,-100,-74,-55,-74],Acircumflexw:[-30,-30,-40,-40,-90,-74,-55,-92],Acircumflexy:[-30,-30,-40,-40,-74,-74,-55,-92],Acircumflexyacute:[-30,-30,-40,-40,-74,-74,-55,-92],Acircumflexydieresis:[-30,-30,-40,-40,-74,-74,-55,-92],AdieresisC:[-40,-40,-30,-30,-55,-65,-30,-40],AdieresisCacute:[-40,-40,-30,-30,-55,-65,-30,-40],AdieresisCcaron:[-40,-40,-30,-30,-55,-65,-30,-40],AdieresisCcedilla:[-40,-40,-30,-30,-55,-65,-30,-40],AdieresisG:[-50,-50,-30,-30,-55,-60,-35,-40],AdieresisGbreve:[-50,-50,-30,-30,-55,-60,-35,-40],AdieresisGcommaaccent:[-50,-50,-30,-30,-55,-60,-35,-40],AdieresisO:[-40,-40,-30,-30,-45,-50,-40,-55],AdieresisOacute:[-40,-40,-30,-30,-45,-50,-40,-55],AdieresisOcircumflex:[-40,-40,-30,-30,-45,-50,-40,-55],AdieresisOdieresis:[-40,-40,-30,-30,-45,-50,-40,-55],AdieresisOgrave:[-40,-40,-30,-30,-45,-50,-40,-55],AdieresisOhungarumlaut:[-40,-40,-30,-30,-45,-50,-40,-55],AdieresisOmacron:[-40,-40,-30,-30,-45,-50,-40,-55],AdieresisOslash:[-40,-40,-30,-30,-45,-50,-40,-55],AdieresisOtilde:[-40,-40,-30,-30,-45,-50,-40,-55],AdieresisQ:[-40,-40,-30,-30,-45,-55,-40,-55],AdieresisT:[-90,-90,-120,-120,-95,-55,-37,-111],AdieresisTcaron:[-90,-90,-120,-120,-95,-55,-37,-111],AdieresisTcommaaccent:[-90,-90,-120,-120,-95,-55,-37,-111],AdieresisU:[-50,-50,-50,-50,-50,-50,-50,-55],AdieresisUacute:[-50,-50,-50,-50,-50,-50,-50,-55],AdieresisUcircumflex:[-50,-50,-50,-50,-50,-50,-50,-55],AdieresisUdieresis:[-50,-50,-50,-50,-50,-50,-50,-55],AdieresisUgrave:[-50,-50,-50,-50,-50,-50,-50,-55],AdieresisUhungarumlaut:[-50,-50,-50,-50,-50,-50,-50,-55],AdieresisUmacron:[-50,-50,-50,-50,-50,-50,-50,-55],AdieresisUogonek:[-50,-50,-50,-50,-50,-50,-50,-55],AdieresisUring:[-50,-50,-50,-50,-50,-50,-50,-55],AdieresisV:[-80,-80,-70,-70,-145,-95,-105,-135],AdieresisW:[-60,-60,-50,-50,-130,-100,-95,-90],AdieresisY:[-110,-110,-100,-100,-100,-70,-55,-105],AdieresisYacute:[-110,-110,-100,-100,-100,-70,-55,-105],AdieresisYdieresis:[-110,-110,-100,-100,-100,-70,-55,-105],Adieresisu:[-30,-30,-30,-30,-50,-30,-20],Adieresisuacute:[-30,-30,-30,-30,-50,-30,-20],Adieresisucircumflex:[-30,-30,-30,-30,-50,-30,-20],Adieresisudieresis:[-30,-30,-30,-30,-50,-30,-20],Adieresisugrave:[-30,-30,-30,-30,-50,-30,-20],Adieresisuhungarumlaut:[-30,-30,-30,-30,-50,-30,-20],Adieresisumacron:[-30,-30,-30,-30,-50,-30,-20],Adieresisuogonek:[-30,-30,-30,-30,-50,-30,-20],Adieresisuring:[-30,-30,-30,-30,-50,-30,-20],Adieresisv:[-40,-40,-40,-40,-100,-74,-55,-74],Adieresisw:[-30,-30,-40,-40,-90,-74,-55,-92],Adieresisy:[-30,-30,-40,-40,-74,-74,-55,-92],Adieresisyacute:[-30,-30,-40,-40,-74,-74,-55,-92],Adieresisydieresis:[-30,-30,-40,-40,-74,-74,-55,-92],AgraveC:[-40,-40,-30,-30,-55,-65,-30,-40],AgraveCacute:[-40,-40,-30,-30,-55,-65,-30,-40],AgraveCcaron:[-40,-40,-30,-30,-55,-65,-30,-40],AgraveCcedilla:[-40,-40,-30,-30,-55,-65,-30,-40],AgraveG:[-50,-50,-30,-30,-55,-60,-35,-40],AgraveGbreve:[-50,-50,-30,-30,-55,-60,-35,-40],AgraveGcommaaccent:[-50,-50,-30,-30,-55,-60,-35,-40],AgraveO:[-40,-40,-30,-30,-45,-50,-40,-55],AgraveOacute:[-40,-40,-30,-30,-45,-50,-40,-55],AgraveOcircumflex:[-40,-40,-30,-30,-45,-50,-40,-55],AgraveOdieresis:[-40,-40,-30,-30,-45,-50,-40,-55],AgraveOgrave:[-40,-40,-30,-30,-45,-50,-40,-55],AgraveOhungarumlaut:[-40,-40,-30,-30,-45,-50,-40,-55],AgraveOmacron:[-40,-40,-30,-30,-45,-50,-40,-55],AgraveOslash:[-40,-40,-30,-30,-45,-50,-40,-55],AgraveOtilde:[-40,-40,-30,-30,-45,-50,-40,-55],AgraveQ:[-40,-40,-30,-30,-45,-55,-40,-55],AgraveT:[-90,-90,-120,-120,-95,-55,-37,-111],AgraveTcaron:[-90,-90,-120,-120,-95,-55,-37,-111],AgraveTcommaaccent:[-90,-90,-120,-120,-95,-55,-37,-111],AgraveU:[-50,-50,-50,-50,-50,-50,-50,-55],AgraveUacute:[-50,-50,-50,-50,-50,-50,-50,-55],AgraveUcircumflex:[-50,-50,-50,-50,-50,-50,-50,-55],AgraveUdieresis:[-50,-50,-50,-50,-50,-50,-50,-55],AgraveUgrave:[-50,-50,-50,-50,-50,-50,-50,-55],AgraveUhungarumlaut:[-50,-50,-50,-50,-50,-50,-50,-55],AgraveUmacron:[-50,-50,-50,-50,-50,-50,-50,-55],AgraveUogonek:[-50,-50,-50,-50,-50,-50,-50,-55],AgraveUring:[-50,-50,-50,-50,-50,-50,-50,-55],AgraveV:[-80,-80,-70,-70,-145,-95,-105,-135],AgraveW:[-60,-60,-50,-50,-130,-100,-95,-90],AgraveY:[-110,-110,-100,-100,-100,-70,-55,-105],AgraveYacute:[-110,-110,-100,-100,-100,-70,-55,-105],AgraveYdieresis:[-110,-110,-100,-100,-100,-70,-55,-105],Agraveu:[-30,-30,-30,-30,-50,-30,-20],Agraveuacute:[-30,-30,-30,-30,-50,-30,-20],Agraveucircumflex:[-30,-30,-30,-30,-50,-30,-20],Agraveudieresis:[-30,-30,-30,-30,-50,-30,-20],Agraveugrave:[-30,-30,-30,-30,-50,-30,-20],Agraveuhungarumlaut:[-30,-30,-30,-30,-50,-30,-20],Agraveumacron:[-30,-30,-30,-30,-50,-30,-20],Agraveuogonek:[-30,-30,-30,-30,-50,-30,-20],Agraveuring:[-30,-30,-30,-30,-50,-30,-20],Agravev:[-40,-40,-40,-40,-100,-74,-55,-74],Agravew:[-30,-30,-40,-40,-90,-74,-55,-92],Agravey:[-30,-30,-40,-40,-74,-74,-55,-92],Agraveyacute:[-30,-30,-40,-40,-74,-74,-55,-92],Agraveydieresis:[-30,-30,-40,-40,-74,-74,-55,-92],AmacronC:[-40,-40,-30,-30,-55,-65,-30,-40],AmacronCacute:[-40,-40,-30,-30,-55,-65,-30,-40],AmacronCcaron:[-40,-40,-30,-30,-55,-65,-30,-40],AmacronCcedilla:[-40,-40,-30,-30,-55,-65,-30,-40],AmacronG:[-50,-50,-30,-30,-55,-60,-35,-40],AmacronGbreve:[-50,-50,-30,-30,-55,-60,-35,-40],AmacronGcommaaccent:[-50,-50,-30,-30,-55,-60,-35,-40],AmacronO:[-40,-40,-30,-30,-45,-50,-40,-55],AmacronOacute:[-40,-40,-30,-30,-45,-50,-40,-55],AmacronOcircumflex:[-40,-40,-30,-30,-45,-50,-40,-55],AmacronOdieresis:[-40,-40,-30,-30,-45,-50,-40,-55],AmacronOgrave:[-40,-40,-30,-30,-45,-50,-40,-55],AmacronOhungarumlaut:[-40,-40,-30,-30,-45,-50,-40,-55],AmacronOmacron:[-40,-40,-30,-30,-45,-50,-40,-55],AmacronOslash:[-40,-40,-30,-30,-45,-50,-40,-55],AmacronOtilde:[-40,-40,-30,-30,-45,-50,-40,-55],AmacronQ:[-40,-40,-30,-30,-45,-55,-40,-55],AmacronT:[-90,-90,-120,-120,-95,-55,-37,-111],AmacronTcaron:[-90,-90,-120,-120,-95,-55,-37,-111],AmacronTcommaaccent:[-90,-90,-120,-120,-95,-55,-37,-111],AmacronU:[-50,-50,-50,-50,-50,-50,-50,-55],AmacronUacute:[-50,-50,-50,-50,-50,-50,-50,-55],AmacronUcircumflex:[-50,-50,-50,-50,-50,-50,-50,-55],AmacronUdieresis:[-50,-50,-50,-50,-50,-50,-50,-55],AmacronUgrave:[-50,-50,-50,-50,-50,-50,-50,-55],AmacronUhungarumlaut:[-50,-50,-50,-50,-50,-50,-50,-55],AmacronUmacron:[-50,-50,-50,-50,-50,-50,-50,-55],AmacronUogonek:[-50,-50,-50,-50,-50,-50,-50,-55],AmacronUring:[-50,-50,-50,-50,-50,-50,-50,-55],AmacronV:[-80,-80,-70,-70,-145,-95,-105,-135],AmacronW:[-60,-60,-50,-50,-130,-100,-95,-90],AmacronY:[-110,-110,-100,-100,-100,-70,-55,-105],AmacronYacute:[-110,-110,-100,-100,-100,-70,-55,-105],AmacronYdieresis:[-110,-110,-100,-100,-100,-70,-55,-105],Amacronu:[-30,-30,-30,-30,-50,-30,-20],Amacronuacute:[-30,-30,-30,-30,-50,-30,-20],Amacronucircumflex:[-30,-30,-30,-30,-50,-30,-20],Amacronudieresis:[-30,-30,-30,-30,-50,-30,-20],Amacronugrave:[-30,-30,-30,-30,-50,-30,-20],Amacronuhungarumlaut:[-30,-30,-30,-30,-50,-30,-20],Amacronumacron:[-30,-30,-30,-30,-50,-30,-20],Amacronuogonek:[-30,-30,-30,-30,-50,-30,-20],Amacronuring:[-30,-30,-30,-30,-50,-30,-20],Amacronv:[-40,-40,-40,-40,-100,-74,-55,-74],Amacronw:[-30,-30,-40,-40,-90,-74,-55,-92],Amacrony:[-30,-30,-40,-40,-74,-74,-55,-92],Amacronyacute:[-30,-30,-40,-40,-74,-74,-55,-92],Amacronydieresis:[-30,-30,-40,-40,-74,-74,-55,-92],AogonekC:[-40,-40,-30,-30,-55,-65,-30,-40],AogonekCacute:[-40,-40,-30,-30,-55,-65,-30,-40],AogonekCcaron:[-40,-40,-30,-30,-55,-65,-30,-40],AogonekCcedilla:[-40,-40,-30,-30,-55,-65,-30,-40],AogonekG:[-50,-50,-30,-30,-55,-60,-35,-40],AogonekGbreve:[-50,-50,-30,-30,-55,-60,-35,-40],AogonekGcommaaccent:[-50,-50,-30,-30,-55,-60,-35,-40],AogonekO:[-40,-40,-30,-30,-45,-50,-40,-55],AogonekOacute:[-40,-40,-30,-30,-45,-50,-40,-55],AogonekOcircumflex:[-40,-40,-30,-30,-45,-50,-40,-55],AogonekOdieresis:[-40,-40,-30,-30,-45,-50,-40,-55],AogonekOgrave:[-40,-40,-30,-30,-45,-50,-40,-55],AogonekOhungarumlaut:[-40,-40,-30,-30,-45,-50,-40,-55],AogonekOmacron:[-40,-40,-30,-30,-45,-50,-40,-55],AogonekOslash:[-40,-40,-30,-30,-45,-50,-40,-55],AogonekOtilde:[-40,-40,-30,-30,-45,-50,-40,-55],AogonekQ:[-40,-40,-30,-30,-45,-55,-40,-55],AogonekT:[-90,-90,-120,-120,-95,-55,-37,-111],AogonekTcaron:[-90,-90,-120,-120,-95,-55,-37,-111],AogonekTcommaaccent:[-90,-90,-120,-120,-95,-55,-37,-111],AogonekU:[-50,-50,-50,-50,-50,-50,-50,-55],AogonekUacute:[-50,-50,-50,-50,-50,-50,-50,-55],AogonekUcircumflex:[-50,-50,-50,-50,-50,-50,-50,-55],AogonekUdieresis:[-50,-50,-50,-50,-50,-50,-50,-55],AogonekUgrave:[-50,-50,-50,-50,-50,-50,-50,-55],AogonekUhungarumlaut:[-50,-50,-50,-50,-50,-50,-50,-55],AogonekUmacron:[-50,-50,-50,-50,-50,-50,-50,-55],AogonekUogonek:[-50,-50,-50,-50,-50,-50,-50,-55],AogonekUring:[-50,-50,-50,-50,-50,-50,-50,-55],AogonekV:[-80,-80,-70,-70,-145,-95,-105,-135],AogonekW:[-60,-60,-50,-50,-130,-100,-95,-90],AogonekY:[-110,-110,-100,-100,-100,-70,-55,-105],AogonekYacute:[-110,-110,-100,-100,-100,-70,-55,-105],AogonekYdieresis:[-110,-110,-100,-100,-100,-70,-55,-105],Aogoneku:[-30,-30,-30,-30,-50,-30,-20],Aogonekuacute:[-30,-30,-30,-30,-50,-30,-20],Aogonekucircumflex:[-30,-30,-30,-30,-50,-30,-20],Aogonekudieresis:[-30,-30,-30,-30,-50,-30,-20],Aogonekugrave:[-30,-30,-30,-30,-50,-30,-20],Aogonekuhungarumlaut:[-30,-30,-30,-30,-50,-30,-20],Aogonekumacron:[-30,-30,-30,-30,-50,-30,-20],Aogonekuogonek:[-30,-30,-30,-30,-50,-30,-20],Aogonekuring:[-30,-30,-30,-30,-50,-30,-20],Aogonekv:[-40,-40,-40,-40,-100,-74,-55,-74],Aogonekw:[-30,-30,-40,-40,-90,-74,-55,-52],Aogoneky:[-30,-30,-40,-40,-34,-34,-55,-52],Aogonekyacute:[-30,-30,-40,-40,-34,-34,-55,-52],Aogonekydieresis:[-30,-30,-40,-40,-34,-34,-55,-52],AringC:[-40,-40,-30,-30,-55,-65,-30,-40],AringCacute:[-40,-40,-30,-30,-55,-65,-30,-40],AringCcaron:[-40,-40,-30,-30,-55,-65,-30,-40],AringCcedilla:[-40,-40,-30,-30,-55,-65,-30,-40],AringG:[-50,-50,-30,-30,-55,-60,-35,-40],AringGbreve:[-50,-50,-30,-30,-55,-60,-35,-40],AringGcommaaccent:[-50,-50,-30,-30,-55,-60,-35,-40],AringO:[-40,-40,-30,-30,-45,-50,-40,-55],AringOacute:[-40,-40,-30,-30,-45,-50,-40,-55],AringOcircumflex:[-40,-40,-30,-30,-45,-50,-40,-55],AringOdieresis:[-40,-40,-30,-30,-45,-50,-40,-55],AringOgrave:[-40,-40,-30,-30,-45,-50,-40,-55],AringOhungarumlaut:[-40,-40,-30,-30,-45,-50,-40,-55],AringOmacron:[-40,-40,-30,-30,-45,-50,-40,-55],AringOslash:[-40,-40,-30,-30,-45,-50,-40,-55],AringOtilde:[-40,-40,-30,-30,-45,-50,-40,-55],AringQ:[-40,-40,-30,-30,-45,-55,-40,-55],AringT:[-90,-90,-120,-120,-95,-55,-37,-111],AringTcaron:[-90,-90,-120,-120,-95,-55,-37,-111],AringTcommaaccent:[-90,-90,-120,-120,-95,-55,-37,-111],AringU:[-50,-50,-50,-50,-50,-50,-50,-55],AringUacute:[-50,-50,-50,-50,-50,-50,-50,-55],AringUcircumflex:[-50,-50,-50,-50,-50,-50,-50,-55],AringUdieresis:[-50,-50,-50,-50,-50,-50,-50,-55],AringUgrave:[-50,-50,-50,-50,-50,-50,-50,-55],AringUhungarumlaut:[-50,-50,-50,-50,-50,-50,-50,-55],AringUmacron:[-50,-50,-50,-50,-50,-50,-50,-55],AringUogonek:[-50,-50,-50,-50,-50,-50,-50,-55],AringUring:[-50,-50,-50,-50,-50,-50,-50,-55],AringV:[-80,-80,-70,-70,-145,-95,-105,-135],AringW:[-60,-60,-50,-50,-130,-100,-95,-90],AringY:[-110,-110,-100,-100,-100,-70,-55,-105],AringYacute:[-110,-110,-100,-100,-100,-70,-55,-105],AringYdieresis:[-110,-110,-100,-100,-100,-70,-55,-105],Aringu:[-30,-30,-30,-30,-50,-30,-20],Aringuacute:[-30,-30,-30,-30,-50,-30,-20],Aringucircumflex:[-30,-30,-30,-30,-50,-30,-20],Aringudieresis:[-30,-30,-30,-30,-50,-30,-20],Aringugrave:[-30,-30,-30,-30,-50,-30,-20],Aringuhungarumlaut:[-30,-30,-30,-30,-50,-30,-20],Aringumacron:[-30,-30,-30,-30,-50,-30,-20],Aringuogonek:[-30,-30,-30,-30,-50,-30,-20],Aringuring:[-30,-30,-30,-30,-50,-30,-20],Aringv:[-40,-40,-40,-40,-100,-74,-55,-74],Aringw:[-30,-30,-40,-40,-90,-74,-55,-92],Aringy:[-30,-30,-40,-40,-74,-74,-55,-92],Aringyacute:[-30,-30,-40,-40,-74,-74,-55,-92],Aringydieresis:[-30,-30,-40,-40,-74,-74,-55,-92],AtildeC:[-40,-40,-30,-30,-55,-65,-30,-40],AtildeCacute:[-40,-40,-30,-30,-55,-65,-30,-40],AtildeCcaron:[-40,-40,-30,-30,-55,-65,-30,-40],AtildeCcedilla:[-40,-40,-30,-30,-55,-65,-30,-40],AtildeG:[-50,-50,-30,-30,-55,-60,-35,-40],AtildeGbreve:[-50,-50,-30,-30,-55,-60,-35,-40],AtildeGcommaaccent:[-50,-50,-30,-30,-55,-60,-35,-40],AtildeO:[-40,-40,-30,-30,-45,-50,-40,-55],AtildeOacute:[-40,-40,-30,-30,-45,-50,-40,-55],AtildeOcircumflex:[-40,-40,-30,-30,-45,-50,-40,-55],AtildeOdieresis:[-40,-40,-30,-30,-45,-50,-40,-55],AtildeOgrave:[-40,-40,-30,-30,-45,-50,-40,-55],AtildeOhungarumlaut:[-40,-40,-30,-30,-45,-50,-40,-55],AtildeOmacron:[-40,-40,-30,-30,-45,-50,-40,-55],AtildeOslash:[-40,-40,-30,-30,-45,-50,-40,-55],AtildeOtilde:[-40,-40,-30,-30,-45,-50,-40,-55],AtildeQ:[-40,-40,-30,-30,-45,-55,-40,-55],AtildeT:[-90,-90,-120,-120,-95,-55,-37,-111],AtildeTcaron:[-90,-90,-120,-120,-95,-55,-37,-111],AtildeTcommaaccent:[-90,-90,-120,-120,-95,-55,-37,-111],AtildeU:[-50,-50,-50,-50,-50,-50,-50,-55],AtildeUacute:[-50,-50,-50,-50,-50,-50,-50,-55],AtildeUcircumflex:[-50,-50,-50,-50,-50,-50,-50,-55],AtildeUdieresis:[-50,-50,-50,-50,-50,-50,-50,-55],AtildeUgrave:[-50,-50,-50,-50,-50,-50,-50,-55],AtildeUhungarumlaut:[-50,-50,-50,-50,-50,-50,-50,-55],AtildeUmacron:[-50,-50,-50,-50,-50,-50,-50,-55],AtildeUogonek:[-50,-50,-50,-50,-50,-50,-50,-55],AtildeUring:[-50,-50,-50,-50,-50,-50,-50,-55],AtildeV:[-80,-80,-70,-70,-145,-95,-105,-135],AtildeW:[-60,-60,-50,-50,-130,-100,-95,-90],AtildeY:[-110,-110,-100,-100,-100,-70,-55,-105],AtildeYacute:[-110,-110,-100,-100,-100,-70,-55,-105],AtildeYdieresis:[-110,-110,-100,-100,-100,-70,-55,-105],Atildeu:[-30,-30,-30,-30,-50,-30,-20],Atildeuacute:[-30,-30,-30,-30,-50,-30,-20],Atildeucircumflex:[-30,-30,-30,-30,-50,-30,-20],Atildeudieresis:[-30,-30,-30,-30,-50,-30,-20],Atildeugrave:[-30,-30,-30,-30,-50,-30,-20],Atildeuhungarumlaut:[-30,-30,-30,-30,-50,-30,-20],Atildeumacron:[-30,-30,-30,-30,-50,-30,-20],Atildeuogonek:[-30,-30,-30,-30,-50,-30,-20],Atildeuring:[-30,-30,-30,-30,-50,-30,-20],Atildev:[-40,-40,-40,-40,-100,-74,-55,-74],Atildew:[-30,-30,-40,-40,-90,-74,-55,-92],Atildey:[-30,-30,-40,-40,-74,-74,-55,-92],Atildeyacute:[-30,-30,-40,-40,-74,-74,-55,-92],Atildeydieresis:[-30,-30,-40,-40,-74,-74,-55,-92],BA:[-30,-30,0,0,-30,-25,-25,-35],BAacute:[-30,-30,0,0,-30,-25,-25,-35],BAbreve:[-30,-30,0,0,-30,-25,-25,-35],BAcircumflex:[-30,-30,0,0,-30,-25,-25,-35],BAdieresis:[-30,-30,0,0,-30,-25,-25,-35],BAgrave:[-30,-30,0,0,-30,-25,-25,-35],BAmacron:[-30,-30,0,0,-30,-25,-25,-35],BAogonek:[-30,-30,0,0,-30,-25,-25,-35],BAring:[-30,-30,0,0,-30,-25,-25,-35],BAtilde:[-30,-30,0,0,-30,-25,-25,-35],BU:[-10,-10,-10,-10,-10,-10,-10,-10],BUacute:[-10,-10,-10,-10,-10,-10,-10,-10],BUcircumflex:[-10,-10,-10,-10,-10,-10,-10,-10],BUdieresis:[-10,-10,-10,-10,-10,-10,-10,-10],BUgrave:[-10,-10,-10,-10,-10,-10,-10,-10],BUhungarumlaut:[-10,-10,-10,-10,-10,-10,-10,-10],BUmacron:[-10,-10,-10,-10,-10,-10,-10,-10],BUogonek:[-10,-10,-10,-10,-10,-10,-10,-10],BUring:[-10,-10,-10,-10,-10,-10,-10,-10],DA:[-40,-40,-40,-40,-35,-25,-35,-40],DAacute:[-40,-40,-40,-40,-35,-25,-35,-40],DAbreve:[-40,-40,-40,-40,-35,-25,-35,-40],DAcircumflex:[-40,-40,-40,-40,-35,-25,-35,-40],DAdieresis:[-40,-40,-40,-40,-35,-25,-35,-40],DAgrave:[-40,-40,-40,-40,-35,-25,-35,-40],DAmacron:[-40,-40,-40,-40,-35,-25,-35,-40],DAogonek:[-40,-40,-40,-40,-35,-25,-35,-40],DAring:[-40,-40,-40,-40,-35,-25,-35,-40],DAtilde:[-40,-40,-40,-40,-35,-25,-35,-40],DV:[-40,-40,-70,-70,-40,-50,-40,-40],DW:[-40,-40,-40,-40,-40,-40,-40,-30],DY:[-70,-70,-90,-90,-40,-50,-40,-55],DYacute:[-70,-70,-90,-90,-40,-50,-40,-55],DYdieresis:[-70,-70,-90,-90,-40,-50,-40,-55],Dcomma:[-30,-30,-70,-70],Dperiod:[-30,-30,-70,-70,-20],DcaronA:[-40,-40,-40,-40,-35,-25,-35,-40],DcaronAacute:[-40,-40,-40,-40,-35,-25,-35,-40],DcaronAbreve:[-40,-40,-40,-40,-35,-25,-35,-40],DcaronAcircumflex:[-40,-40,-40,-40,-35,-25,-35,-40],DcaronAdieresis:[-40,-40,-40,-40,-35,-25,-35,-40],DcaronAgrave:[-40,-40,-40,-40,-35,-25,-35,-40],DcaronAmacron:[-40,-40,-40,-40,-35,-25,-35,-40],DcaronAogonek:[-40,-40,-40,-40,-35,-25,-35,-40],DcaronAring:[-40,-40,-40,-40,-35,-25,-35,-40],DcaronAtilde:[-40,-40,-40,-40,-35,-25,-35,-40],DcaronV:[-40,-40,-70,-70,-40,-50,-40,-40],DcaronW:[-40,-40,-40,-40,-40,-40,-40,-30],DcaronY:[-70,-70,-90,-90,-40,-50,-40,-55],DcaronYacute:[-70,-70,-90,-90,-40,-50,-40,-55],DcaronYdieresis:[-70,-70,-90,-90,-40,-50,-40,-55],Dcaroncomma:[-30,-30,-70,-70],Dcaronperiod:[-30,-30,-70,-70,-20],DcroatA:[-40,-40,-40,-40,-35,-25,-35,-40],DcroatAacute:[-40,-40,-40,-40,-35,-25,-35,-40],DcroatAbreve:[-40,-40,-40,-40,-35,-25,-35,-40],DcroatAcircumflex:[-40,-40,-40,-40,-35,-25,-35,-40],DcroatAdieresis:[-40,-40,-40,-40,-35,-25,-35,-40],DcroatAgrave:[-40,-40,-40,-40,-35,-25,-35,-40],DcroatAmacron:[-40,-40,-40,-40,-35,-25,-35,-40],DcroatAogonek:[-40,-40,-40,-40,-35,-25,-35,-40],DcroatAring:[-40,-40,-40,-40,-35,-25,-35,-40],DcroatAtilde:[-40,-40,-40,-40,-35,-25,-35,-40],DcroatV:[-40,-40,-70,-70,-40,-50,-40,-40],DcroatW:[-40,-40,-40,-40,-40,-40,-40,-30],DcroatY:[-70,-70,-90,-90,-40,-50,-40,-55],DcroatYacute:[-70,-70,-90,-90,-40,-50,-40,-55],DcroatYdieresis:[-70,-70,-90,-90,-40,-50,-40,-55],Dcroatcomma:[-30,-30,-70,-70],Dcroatperiod:[-30,-30,-70,-70,-20],FA:[-80,-80,-80,-80,-90,-100,-115,-74],FAacute:[-80,-80,-80,-80,-90,-100,-115,-74],FAbreve:[-80,-80,-80,-80,-90,-100,-115,-74],FAcircumflex:[-80,-80,-80,-80,-90,-100,-115,-74],FAdieresis:[-80,-80,-80,-80,-90,-100,-115,-74],FAgrave:[-80,-80,-80,-80,-90,-100,-115,-74],FAmacron:[-80,-80,-80,-80,-90,-100,-115,-74],FAogonek:[-80,-80,-80,-80,-90,-100,-115,-74],FAring:[-80,-80,-80,-80,-90,-100,-115,-74],FAtilde:[-80,-80,-80,-80,-90,-100,-115,-74],Fa:[-20,-20,-50,-50,-25,-95,-75,-15],Faacute:[-20,-20,-50,-50,-25,-95,-75,-15],Fabreve:[-20,-20,-50,-50,-25,-95,-75,-15],Facircumflex:[-20,-20,-50,-50,-25,-95,-75,-15],Fadieresis:[-20,-20,-50,-50,-25,-95,-75,-15],Fagrave:[-20,-20,-50,-50,-25,-95,-75,-15],Famacron:[-20,-20,-50,-50,-25,-95,-75,-15],Faogonek:[-20,-20,-50,-50,-25,-95,-75,-15],Faring:[-20,-20,-50,-50,-25,-95,-75,-15],Fatilde:[-20,-20,-50,-50,-25,-95,-75,-15],Fcomma:[-100,-100,-150,-150,-92,-129,-135,-80],Fperiod:[-100,-100,-150,-150,-110,-129,-135,-80],JA:[-20,-20,-20,-20,-30,-25,-40,-60],JAacute:[-20,-20,-20,-20,-30,-25,-40,-60],JAbreve:[-20,-20,-20,-20,-30,-25,-40,-60],JAcircumflex:[-20,-20,-20,-20,-30,-25,-40,-60],JAdieresis:[-20,-20,-20,-20,-30,-25,-40,-60],JAgrave:[-20,-20,-20,-20,-30,-25,-40,-60],JAmacron:[-20,-20,-20,-20,-30,-25,-40,-60],JAogonek:[-20,-20,-20,-20,-30,-25,-40,-60],JAring:[-20,-20,-20,-20,-30,-25,-40,-60],JAtilde:[-20,-20,-20,-20,-30,-25,-40,-60],Jcomma:[-20,-20,-30,-30,0,-10,-25],Jperiod:[-20,-20,-30,-30,-20,-10,-25],Ju:[-20,-20,-20,-20,-15,-40,-35],Juacute:[-20,-20,-20,-20,-15,-40,-35],Jucircumflex:[-20,-20,-20,-20,-15,-40,-35],Judieresis:[-20,-20,-20,-20,-15,-40,-35],Jugrave:[-20,-20,-20,-20,-15,-40,-35],Juhungarumlaut:[-20,-20,-20,-20,-15,-40,-35],Jumacron:[-20,-20,-20,-20,-15,-40,-35],Juogonek:[-20,-20,-20,-20,-15,-40,-35],Juring:[-20,-20,-20,-20,-15,-40,-35],KO:[-30,-30,-50,-50,-30,-30,-50,-30],KOacute:[-30,-30,-50,-50,-30,-30,-50,-30],KOcircumflex:[-30,-30,-50,-50,-30,-30,-50,-30],KOdieresis:[-30,-30,-50,-50,-30,-30,-50,-30],KOgrave:[-30,-30,-50,-50,-30,-30,-50,-30],KOhungarumlaut:[-30,-30,-50,-50,-30,-30,-50,-30],KOmacron:[-30,-30,-50,-50,-30,-30,-50,-30],KOslash:[-30,-30,-50,-50,-30,-30,-50,-30],KOtilde:[-30,-30,-50,-50,-30,-30,-50,-30],Ke:[-15,-15,-40,-40,-25,-25,-35,-25],Keacute:[-15,-15,-40,-40,-25,-25,-35,-25],Kecaron:[-15,-15,-40,-40,-25,-25,-35,-25],Kecircumflex:[-15,-15,-40,-40,-25,-25,-35,-25],Kedieresis:[-15,-15,-40,-40,-25,-25,-35,-25],Kedotaccent:[-15,-15,-40,-40,-25,-25,-35,-25],Kegrave:[-15,-15,-40,-40,-25,-25,-35,-25],Kemacron:[-15,-15,-40,-40,-25,-25,-35,-25],Keogonek:[-15,-15,-40,-40,-25,-25,-35,-25],Ko:[-35,-35,-40,-40,-25,-25,-40,-35],Koacute:[-35,-35,-40,-40,-25,-25,-40,-35],Kocircumflex:[-35,-35,-40,-40,-25,-25,-40,-35],Kodieresis:[-35,-35,-40,-40,-25,-25,-40,-35],Kograve:[-35,-35,-40,-40,-25,-25,-40,-35],Kohungarumlaut:[-35,-35,-40,-40,-25,-25,-40,-35],Komacron:[-35,-35,-40,-40,-25,-25,-40,-35],Koslash:[-35,-35,-40,-40,-25,-25,-40,-35],Kotilde:[-35,-35,-40,-40,-25,-25,-40,-35],Ku:[-30,-30,-30,-30,-15,-20,-40,-15],Kuacute:[-30,-30,-30,-30,-15,-20,-40,-15],Kucircumflex:[-30,-30,-30,-30,-15,-20,-40,-15],Kudieresis:[-30,-30,-30,-30,-15,-20,-40,-15],Kugrave:[-30,-30,-30,-30,-15,-20,-40,-15],Kuhungarumlaut:[-30,-30,-30,-30,-15,-20,-40,-15],Kumacron:[-30,-30,-30,-30,-15,-20,-40,-15],Kuogonek:[-30,-30,-30,-30,-15,-20,-40,-15],Kuring:[-30,-30,-30,-30,-15,-20,-40,-15],Ky:[-40,-40,-50,-50,-45,-20,-40,-25],Kyacute:[-40,-40,-50,-50,-45,-20,-40,-25],Kydieresis:[-40,-40,-50,-50,-45,-20,-40,-25],KcommaaccentO:[-30,-30,-50,-50,-30,-30,-50,-30],KcommaaccentOacute:[-30,-30,-50,-50,-30,-30,-50,-30],KcommaaccentOcircumflex:[-30,-30,-50,-50,-30,-30,-50,-30],KcommaaccentOdieresis:[-30,-30,-50,-50,-30,-30,-50,-30],KcommaaccentOgrave:[-30,-30,-50,-50,-30,-30,-50,-30],KcommaaccentOhungarumlaut:[-30,-30,-50,-50,-30,-30,-50,-30],KcommaaccentOmacron:[-30,-30,-50,-50,-30,-30,-50,-30],KcommaaccentOslash:[-30,-30,-50,-50,-30,-30,-50,-30],KcommaaccentOtilde:[-30,-30,-50,-50,-30,-30,-50,-30],Kcommaaccente:[-15,-15,-40,-40,-25,-25,-35,-25],Kcommaaccenteacute:[-15,-15,-40,-40,-25,-25,-35,-25],Kcommaaccentecaron:[-15,-15,-40,-40,-25,-25,-35,-25],Kcommaaccentecircumflex:[-15,-15,-40,-40,-25,-25,-35,-25],Kcommaaccentedieresis:[-15,-15,-40,-40,-25,-25,-35,-25],Kcommaaccentedotaccent:[-15,-15,-40,-40,-25,-25,-35,-25],Kcommaaccentegrave:[-15,-15,-40,-40,-25,-25,-35,-25],Kcommaaccentemacron:[-15,-15,-40,-40,-25,-25,-35,-25],Kcommaaccenteogonek:[-15,-15,-40,-40,-25,-25,-35,-25],Kcommaaccento:[-35,-35,-40,-40,-25,-25,-40,-35],Kcommaaccentoacute:[-35,-35,-40,-40,-25,-25,-40,-35],Kcommaaccentocircumflex:[-35,-35,-40,-40,-25,-25,-40,-35],Kcommaaccentodieresis:[-35,-35,-40,-40,-25,-25,-40,-35],Kcommaaccentograve:[-35,-35,-40,-40,-25,-25,-40,-35],Kcommaaccentohungarumlaut:[-35,-35,-40,-40,-25,-25,-40,-35],Kcommaaccentomacron:[-35,-35,-40,-40,-25,-25,-40,-35],Kcommaaccentoslash:[-35,-35,-40,-40,-25,-25,-40,-35],Kcommaaccentotilde:[-35,-35,-40,-40,-25,-25,-40,-35],Kcommaaccentu:[-30,-30,-30,-30,-15,-20,-40,-15],Kcommaaccentuacute:[-30,-30,-30,-30,-15,-20,-40,-15],Kcommaaccentucircumflex:[-30,-30,-30,-30,-15,-20,-40,-15],Kcommaaccentudieresis:[-30,-30,-30,-30,-15,-20,-40,-15],Kcommaaccentugrave:[-30,-30,-30,-30,-15,-20,-40,-15],Kcommaaccentuhungarumlaut:[-30,-30,-30,-30,-15,-20,-40,-15],Kcommaaccentumacron:[-30,-30,-30,-30,-15,-20,-40,-15],Kcommaaccentuogonek:[-30,-30,-30,-30,-15,-20,-40,-15],Kcommaaccenturing:[-30,-30,-30,-30,-15,-20,-40,-15],Kcommaaccenty:[-40,-40,-50,-50,-45,-20,-40,-25],Kcommaaccentyacute:[-40,-40,-50,-50,-45,-20,-40,-25],Kcommaaccentydieresis:[-40,-40,-50,-50,-45,-20,-40,-25],LT:[-90,-90,-110,-110,-92,-18,-20,-92],LTcaron:[-90,-90,-110,-110,-92,-18,-20,-92],LTcommaaccent:[-90,-90,-110,-110,-92,-18,-20,-92],LV:[-110,-110,-110,-110,-92,-37,-55,-100],LW:[-80,-80,-70,-70,-92,-37,-55,-74],LY:[-120,-120,-140,-140,-92,-37,-20,-100],LYacute:[-120,-120,-140,-140,-92,-37,-20,-100],LYdieresis:[-120,-120,-140,-140,-92,-37,-20,-100],Lquotedblright:[-140,-140,-140,-140,-20],Lquoteright:[-140,-140,-160,-160,-110,-55,-37,-92],Ly:[-30,-30,-30,-30,-55,-37,-30,-55],Lyacute:[-30,-30,-30,-30,-55,-37,-30,-55],Lydieresis:[-30,-30,-30,-30,-55,-37,-30,-55],LacuteT:[-90,-90,-110,-110,-92,-18,-20,-92],LacuteTcaron:[-90,-90,-110,-110,-92,-18,-20,-92],LacuteTcommaaccent:[-90,-90,-110,-110,-92,-18,-20,-92],LacuteV:[-110,-110,-110,-110,-92,-37,-55,-100],LacuteW:[-80,-80,-70,-70,-92,-37,-55,-74],LacuteY:[-120,-120,-140,-140,-92,-37,-20,-100],LacuteYacute:[-120,-120,-140,-140,-92,-37,-20,-100],LacuteYdieresis:[-120,-120,-140,-140,-92,-37,-20,-100],Lacutequotedblright:[-140,-140,-140,-140,-20],Lacutequoteright:[-140,-140,-160,-160,-110,-55,-37,-92],Lacutey:[-30,-30,-30,-30,-55,-37,-30,-55],Lacuteyacute:[-30,-30,-30,-30,-55,-37,-30,-55],Lacuteydieresis:[-30,-30,-30,-30,-55,-37,-30,-55],LcommaaccentT:[-90,-90,-110,-110,-92,-18,-20,-92],LcommaaccentTcaron:[-90,-90,-110,-110,-92,-18,-20,-92],LcommaaccentTcommaaccent:[-90,-90,-110,-110,-92,-18,-20,-92],LcommaaccentV:[-110,-110,-110,-110,-92,-37,-55,-100],LcommaaccentW:[-80,-80,-70,-70,-92,-37,-55,-74],LcommaaccentY:[-120,-120,-140,-140,-92,-37,-20,-100],LcommaaccentYacute:[-120,-120,-140,-140,-92,-37,-20,-100],LcommaaccentYdieresis:[-120,-120,-140,-140,-92,-37,-20,-100],Lcommaaccentquotedblright:[-140,-140,-140,-140,-20],Lcommaaccentquoteright:[-140,-140,-160,-160,-110,-55,-37,-92],Lcommaaccenty:[-30,-30,-30,-30,-55,-37,-30,-55],Lcommaaccentyacute:[-30,-30,-30,-30,-55,-37,-30,-55],Lcommaaccentydieresis:[-30,-30,-30,-30,-55,-37,-30,-55],LslashT:[-90,-90,-110,-110,-92,-18,-20,-92],LslashTcaron:[-90,-90,-110,-110,-92,-18,-20,-92],LslashTcommaaccent:[-90,-90,-110,-110,-92,-18,-20,-92],LslashV:[-110,-110,-110,-110,-92,-37,-55,-100],LslashW:[-80,-80,-70,-70,-92,-37,-55,-74],LslashY:[-120,-120,-140,-140,-92,-37,-20,-100],LslashYacute:[-120,-120,-140,-140,-92,-37,-20,-100],LslashYdieresis:[-120,-120,-140,-140,-92,-37,-20,-100],Lslashquotedblright:[-140,-140,-140,-140,-20],Lslashquoteright:[-140,-140,-160,-160,-110,-55,-37,-92],Lslashy:[-30,-30,-30,-30,-55,-37,-30,-55],Lslashyacute:[-30,-30,-30,-30,-55,-37,-30,-55],Lslashydieresis:[-30,-30,-30,-30,-55,-37,-30,-55],OA:[-50,-50,-20,-20,-40,-40,-55,-35],OAacute:[-50,-50,-20,-20,-40,-40,-55,-35],OAbreve:[-50,-50,-20,-20,-40,-40,-55,-35],OAcircumflex:[-50,-50,-20,-20,-40,-40,-55,-35],OAdieresis:[-50,-50,-20,-20,-40,-40,-55,-35],OAgrave:[-50,-50,-20,-20,-40,-40,-55,-35],OAmacron:[-50,-50,-20,-20,-40,-40,-55,-35],OAogonek:[-50,-50,-20,-20,-40,-40,-55,-35],OAring:[-50,-50,-20,-20,-40,-40,-55,-35],OAtilde:[-50,-50,-20,-20,-40,-40,-55,-35],OT:[-40,-40,-40,-40,-40,-40,-40,-40],OTcaron:[-40,-40,-40,-40,-40,-40,-40,-40],OTcommaaccent:[-40,-40,-40,-40,-40,-40,-40,-40],OV:[-50,-50,-50,-50,-50,-50,-50,-50],OW:[-50,-50,-30,-30,-50,-50,-50,-35],OX:[-50,-50,-60,-60,-40,-40,-40,-40],OY:[-70,-70,-70,-70,-50,-50,-50,-50],OYacute:[-70,-70,-70,-70,-50,-50,-50,-50],OYdieresis:[-70,-70,-70,-70,-50,-50,-50,-50],Ocomma:[-40,-40,-40,-40],Operiod:[-40,-40,-40,-40],OacuteA:[-50,-50,-20,-20,-40,-40,-55,-35],OacuteAacute:[-50,-50,-20,-20,-40,-40,-55,-35],OacuteAbreve:[-50,-50,-20,-20,-40,-40,-55,-35],OacuteAcircumflex:[-50,-50,-20,-20,-40,-40,-55,-35],OacuteAdieresis:[-50,-50,-20,-20,-40,-40,-55,-35],OacuteAgrave:[-50,-50,-20,-20,-40,-40,-55,-35],OacuteAmacron:[-50,-50,-20,-20,-40,-40,-55,-35],OacuteAogonek:[-50,-50,-20,-20,-40,-40,-55,-35],OacuteAring:[-50,-50,-20,-20,-40,-40,-55,-35],OacuteAtilde:[-50,-50,-20,-20,-40,-40,-55,-35],OacuteT:[-40,-40,-40,-40,-40,-40,-40,-40],OacuteTcaron:[-40,-40,-40,-40,-40,-40,-40,-40],OacuteTcommaaccent:[-40,-40,-40,-40,-40,-40,-40,-40],OacuteV:[-50,-50,-50,-50,-50,-50,-50,-50],OacuteW:[-50,-50,-30,-30,-50,-50,-50,-35],OacuteX:[-50,-50,-60,-60,-40,-40,-40,-40],OacuteY:[-70,-70,-70,-70,-50,-50,-50,-50],OacuteYacute:[-70,-70,-70,-70,-50,-50,-50,-50],OacuteYdieresis:[-70,-70,-70,-70,-50,-50,-50,-50],Oacutecomma:[-40,-40,-40,-40],Oacuteperiod:[-40,-40,-40,-40],OcircumflexA:[-50,-50,-20,-20,-40,-40,-55,-35],OcircumflexAacute:[-50,-50,-20,-20,-40,-40,-55,-35],OcircumflexAbreve:[-50,-50,-20,-20,-40,-40,-55,-35],OcircumflexAcircumflex:[-50,-50,-20,-20,-40,-40,-55,-35],OcircumflexAdieresis:[-50,-50,-20,-20,-40,-40,-55,-35],OcircumflexAgrave:[-50,-50,-20,-20,-40,-40,-55,-35],OcircumflexAmacron:[-50,-50,-20,-20,-40,-40,-55,-35],OcircumflexAogonek:[-50,-50,-20,-20,-40,-40,-55,-35],OcircumflexAring:[-50,-50,-20,-20,-40,-40,-55,-35],OcircumflexAtilde:[-50,-50,-20,-20,-40,-40,-55,-35],OcircumflexT:[-40,-40,-40,-40,-40,-40,-40,-40],OcircumflexTcaron:[-40,-40,-40,-40,-40,-40,-40,-40],OcircumflexTcommaaccent:[-40,-40,-40,-40,-40,-40,-40,-40],OcircumflexV:[-50,-50,-50,-50,-50,-50,-50,-50],OcircumflexW:[-50,-50,-30,-30,-50,-50,-50,-35],OcircumflexX:[-50,-50,-60,-60,-40,-40,-40,-40],OcircumflexY:[-70,-70,-70,-70,-50,-50,-50,-50],OcircumflexYacute:[-70,-70,-70,-70,-50,-50,-50,-50],OcircumflexYdieresis:[-70,-70,-70,-70,-50,-50,-50,-50],Ocircumflexcomma:[-40,-40,-40,-40],Ocircumflexperiod:[-40,-40,-40,-40],OdieresisA:[-50,-50,-20,-20,-40,-40,-55,-35],OdieresisAacute:[-50,-50,-20,-20,-40,-40,-55,-35],OdieresisAbreve:[-50,-50,-20,-20,-40,-40,-55,-35],OdieresisAcircumflex:[-50,-50,-20,-20,-40,-40,-55,-35],OdieresisAdieresis:[-50,-50,-20,-20,-40,-40,-55,-35],OdieresisAgrave:[-50,-50,-20,-20,-40,-40,-55,-35],OdieresisAmacron:[-50,-50,-20,-20,-40,-40,-55,-35],OdieresisAogonek:[-50,-50,-20,-20,-40,-40,-55,-35],OdieresisAring:[-50,-50,-20,-20,-40,-40,-55,-35],OdieresisAtilde:[-50,-50,-20,-20,-40,-40,-55,-35],OdieresisT:[-40,-40,-40,-40,-40,-40,-40,-40],OdieresisTcaron:[-40,-40,-40,-40,-40,-40,-40,-40],OdieresisTcommaaccent:[-40,-40,-40,-40,-40,-40,-40,-40],OdieresisV:[-50,-50,-50,-50,-50,-50,-50,-50],OdieresisW:[-50,-50,-30,-30,-50,-50,-50,-35],OdieresisX:[-50,-50,-60,-60,-40,-40,-40,-40],OdieresisY:[-70,-70,-70,-70,-50,-50,-50,-50],OdieresisYacute:[-70,-70,-70,-70,-50,-50,-50,-50],OdieresisYdieresis:[-70,-70,-70,-70,-50,-50,-50,-50],Odieresiscomma:[-40,-40,-40,-40],Odieresisperiod:[-40,-40,-40,-40],OgraveA:[-50,-50,-20,-20,-40,-40,-55,-35],OgraveAacute:[-50,-50,-20,-20,-40,-40,-55,-35],OgraveAbreve:[-50,-50,-20,-20,-40,-40,-55,-35],OgraveAcircumflex:[-50,-50,-20,-20,-40,-40,-55,-35],OgraveAdieresis:[-50,-50,-20,-20,-40,-40,-55,-35],OgraveAgrave:[-50,-50,-20,-20,-40,-40,-55,-35],OgraveAmacron:[-50,-50,-20,-20,-40,-40,-55,-35],OgraveAogonek:[-50,-50,-20,-20,-40,-40,-55,-35],OgraveAring:[-50,-50,-20,-20,-40,-40,-55,-35],OgraveAtilde:[-50,-50,-20,-20,-40,-40,-55,-35],OgraveT:[-40,-40,-40,-40,-40,-40,-40,-40],OgraveTcaron:[-40,-40,-40,-40,-40,-40,-40,-40],OgraveTcommaaccent:[-40,-40,-40,-40,-40,-40,-40,-40],OgraveV:[-50,-50,-50,-50,-50,-50,-50,-50],OgraveW:[-50,-50,-30,-30,-50,-50,-50,-35],OgraveX:[-50,-50,-60,-60,-40,-40,-40,-40],OgraveY:[-70,-70,-70,-70,-50,-50,-50,-50],OgraveYacute:[-70,-70,-70,-70,-50,-50,-50,-50],OgraveYdieresis:[-70,-70,-70,-70,-50,-50,-50,-50],Ogravecomma:[-40,-40,-40,-40],Ograveperiod:[-40,-40,-40,-40],OhungarumlautA:[-50,-50,-20,-20,-40,-40,-55,-35],OhungarumlautAacute:[-50,-50,-20,-20,-40,-40,-55,-35],OhungarumlautAbreve:[-50,-50,-20,-20,-40,-40,-55,-35],OhungarumlautAcircumflex:[-50,-50,-20,-20,-40,-40,-55,-35],OhungarumlautAdieresis:[-50,-50,-20,-20,-40,-40,-55,-35],OhungarumlautAgrave:[-50,-50,-20,-20,-40,-40,-55,-35],OhungarumlautAmacron:[-50,-50,-20,-20,-40,-40,-55,-35],OhungarumlautAogonek:[-50,-50,-20,-20,-40,-40,-55,-35],OhungarumlautAring:[-50,-50,-20,-20,-40,-40,-55,-35],OhungarumlautAtilde:[-50,-50,-20,-20,-40,-40,-55,-35],OhungarumlautT:[-40,-40,-40,-40,-40,-40,-40,-40],OhungarumlautTcaron:[-40,-40,-40,-40,-40,-40,-40,-40],OhungarumlautTcommaaccent:[-40,-40,-40,-40,-40,-40,-40,-40],OhungarumlautV:[-50,-50,-50,-50,-50,-50,-50,-50],OhungarumlautW:[-50,-50,-30,-30,-50,-50,-50,-35],OhungarumlautX:[-50,-50,-60,-60,-40,-40,-40,-40],OhungarumlautY:[-70,-70,-70,-70,-50,-50,-50,-50],OhungarumlautYacute:[-70,-70,-70,-70,-50,-50,-50,-50],OhungarumlautYdieresis:[-70,-70,-70,-70,-50,-50,-50,-50],Ohungarumlautcomma:[-40,-40,-40,-40],Ohungarumlautperiod:[-40,-40,-40,-40],OmacronA:[-50,-50,-20,-20,-40,-40,-55,-35],OmacronAacute:[-50,-50,-20,-20,-40,-40,-55,-35],OmacronAbreve:[-50,-50,-20,-20,-40,-40,-55,-35],OmacronAcircumflex:[-50,-50,-20,-20,-40,-40,-55,-35],OmacronAdieresis:[-50,-50,-20,-20,-40,-40,-55,-35],OmacronAgrave:[-50,-50,-20,-20,-40,-40,-55,-35],OmacronAmacron:[-50,-50,-20,-20,-40,-40,-55,-35],OmacronAogonek:[-50,-50,-20,-20,-40,-40,-55,-35],OmacronAring:[-50,-50,-20,-20,-40,-40,-55,-35],OmacronAtilde:[-50,-50,-20,-20,-40,-40,-55,-35],OmacronT:[-40,-40,-40,-40,-40,-40,-40,-40],OmacronTcaron:[-40,-40,-40,-40,-40,-40,-40,-40],OmacronTcommaaccent:[-40,-40,-40,-40,-40,-40,-40,-40],OmacronV:[-50,-50,-50,-50,-50,-50,-50,-50],OmacronW:[-50,-50,-30,-30,-50,-50,-50,-35],OmacronX:[-50,-50,-60,-60,-40,-40,-40,-40],OmacronY:[-70,-70,-70,-70,-50,-50,-50,-50],OmacronYacute:[-70,-70,-70,-70,-50,-50,-50,-50],OmacronYdieresis:[-70,-70,-70,-70,-50,-50,-50,-50],Omacroncomma:[-40,-40,-40,-40],Omacronperiod:[-40,-40,-40,-40],OslashA:[-50,-50,-20,-20,-40,-40,-55,-35],OslashAacute:[-50,-50,-20,-20,-40,-40,-55,-35],OslashAbreve:[-50,-50,-20,-20,-40,-40,-55,-35],OslashAcircumflex:[-50,-50,-20,-20,-40,-40,-55,-35],OslashAdieresis:[-50,-50,-20,-20,-40,-40,-55,-35],OslashAgrave:[-50,-50,-20,-20,-40,-40,-55,-35],OslashAmacron:[-50,-50,-20,-20,-40,-40,-55,-35],OslashAogonek:[-50,-50,-20,-20,-40,-40,-55,-35],OslashAring:[-50,-50,-20,-20,-40,-40,-55,-35],OslashAtilde:[-50,-50,-20,-20,-40,-40,-55,-35],OslashT:[-40,-40,-40,-40,-40,-40,-40,-40],OslashTcaron:[-40,-40,-40,-40,-40,-40,-40,-40],OslashTcommaaccent:[-40,-40,-40,-40,-40,-40,-40,-40],OslashV:[-50,-50,-50,-50,-50,-50,-50,-50],OslashW:[-50,-50,-30,-30,-50,-50,-50,-35],OslashX:[-50,-50,-60,-60,-40,-40,-40,-40],OslashY:[-70,-70,-70,-70,-50,-50,-50,-50],OslashYacute:[-70,-70,-70,-70,-50,-50,-50,-50],OslashYdieresis:[-70,-70,-70,-70,-50,-50,-50,-50],Oslashcomma:[-40,-40,-40,-40],Oslashperiod:[-40,-40,-40,-40],OtildeA:[-50,-50,-20,-20,-40,-40,-55,-35],OtildeAacute:[-50,-50,-20,-20,-40,-40,-55,-35],OtildeAbreve:[-50,-50,-20,-20,-40,-40,-55,-35],OtildeAcircumflex:[-50,-50,-20,-20,-40,-40,-55,-35],OtildeAdieresis:[-50,-50,-20,-20,-40,-40,-55,-35],OtildeAgrave:[-50,-50,-20,-20,-40,-40,-55,-35],OtildeAmacron:[-50,-50,-20,-20,-40,-40,-55,-35],OtildeAogonek:[-50,-50,-20,-20,-40,-40,-55,-35],OtildeAring:[-50,-50,-20,-20,-40,-40,-55,-35],OtildeAtilde:[-50,-50,-20,-20,-40,-40,-55,-35],OtildeT:[-40,-40,-40,-40,-40,-40,-40,-40],OtildeTcaron:[-40,-40,-40,-40,-40,-40,-40,-40],OtildeTcommaaccent:[-40,-40,-40,-40,-40,-40,-40,-40],OtildeV:[-50,-50,-50,-50,-50,-50,-50,-50],OtildeW:[-50,-50,-30,-30,-50,-50,-50,-35],OtildeX:[-50,-50,-60,-60,-40,-40,-40,-40],OtildeY:[-70,-70,-70,-70,-50,-50,-50,-50],OtildeYacute:[-70,-70,-70,-70,-50,-50,-50,-50],OtildeYdieresis:[-70,-70,-70,-70,-50,-50,-50,-50],Otildecomma:[-40,-40,-40,-40],Otildeperiod:[-40,-40,-40,-40],PA:[-100,-100,-120,-120,-74,-85,-90,-92],PAacute:[-100,-100,-120,-120,-74,-85,-90,-92],PAbreve:[-100,-100,-120,-120,-74,-85,-90,-92],PAcircumflex:[-100,-100,-120,-120,-74,-85,-90,-92],PAdieresis:[-100,-100,-120,-120,-74,-85,-90,-92],PAgrave:[-100,-100,-120,-120,-74,-85,-90,-92],PAmacron:[-100,-100,-120,-120,-74,-85,-90,-92],PAogonek:[-100,-100,-120,-120,-74,-85,-90,-92],PAring:[-100,-100,-120,-120,-74,-85,-90,-92],PAtilde:[-100,-100,-120,-120,-74,-85,-90,-92],Pa:[-30,-30,-40,-40,-10,-40,-80,-15],Paacute:[-30,-30,-40,-40,-10,-40,-80,-15],Pabreve:[-30,-30,-40,-40,-10,-40,-80,-15],Pacircumflex:[-30,-30,-40,-40,-10,-40,-80,-15],Padieresis:[-30,-30,-40,-40,-10,-40,-80,-15],Pagrave:[-30,-30,-40,-40,-10,-40,-80,-15],Pamacron:[-30,-30,-40,-40,-10,-40,-80,-15],Paogonek:[-30,-30,-40,-40,-10,-40,-80,-15],Paring:[-30,-30,-40,-40,-10,-40,-80,-15],Patilde:[-30,-30,-40,-40,-10,-40,-80,-15],Pcomma:[-120,-120,-180,-180,-92,-129,-135,-111],Pe:[-30,-30,-50,-50,-20,-50,-80],Peacute:[-30,-30,-50,-50,-20,-50,-80],Pecaron:[-30,-30,-50,-50,-20,-50,-80],Pecircumflex:[-30,-30,-50,-50,-20,-50,-80],Pedieresis:[-30,-30,-50,-50,-20,-50,-80],Pedotaccent:[-30,-30,-50,-50,-20,-50,-80],Pegrave:[-30,-30,-50,-50,-20,-50,-80],Pemacron:[-30,-30,-50,-50,-20,-50,-80],Peogonek:[-30,-30,-50,-50,-20,-50,-80],Po:[-40,-40,-50,-50,-20,-55,-80],Poacute:[-40,-40,-50,-50,-20,-55,-80],Pocircumflex:[-40,-40,-50,-50,-20,-55,-80],Podieresis:[-40,-40,-50,-50,-20,-55,-80],Pograve:[-40,-40,-50,-50,-20,-55,-80],Pohungarumlaut:[-40,-40,-50,-50,-20,-55,-80],Pomacron:[-40,-40,-50,-50,-20,-55,-80],Poslash:[-40,-40,-50,-50,-20,-55,-80],Potilde:[-40,-40,-50,-50,-20,-55,-80],Pperiod:[-120,-120,-180,-180,-110,-129,-135,-111],QU:[-10,-10,-10,-10,-10,-10,-10,-10],QUacute:[-10,-10,-10,-10,-10,-10,-10,-10],QUcircumflex:[-10,-10,-10,-10,-10,-10,-10,-10],QUdieresis:[-10,-10,-10,-10,-10,-10,-10,-10],QUgrave:[-10,-10,-10,-10,-10,-10,-10,-10],QUhungarumlaut:[-10,-10,-10,-10,-10,-10,-10,-10],QUmacron:[-10,-10,-10,-10,-10,-10,-10,-10],QUogonek:[-10,-10,-10,-10,-10,-10,-10,-10],QUring:[-10,-10,-10,-10,-10,-10,-10,-10],Qcomma:[20,20],Qperiod:[20,20,0,0,-20],RO:[-20,-20,-20,-20,-30,-40,-40,-40],ROacute:[-20,-20,-20,-20,-30,-40,-40,-40],ROcircumflex:[-20,-20,-20,-20,-30,-40,-40,-40],ROdieresis:[-20,-20,-20,-20,-30,-40,-40,-40],ROgrave:[-20,-20,-20,-20,-30,-40,-40,-40],ROhungarumlaut:[-20,-20,-20,-20,-30,-40,-40,-40],ROmacron:[-20,-20,-20,-20,-30,-40,-40,-40],ROslash:[-20,-20,-20,-20,-30,-40,-40,-40],ROtilde:[-20,-20,-20,-20,-30,-40,-40,-40],RT:[-20,-20,-30,-30,-40,-30,0,-60],RTcaron:[-20,-20,-30,-30,-40,-30,0,-60],RTcommaaccent:[-20,-20,-30,-30,-40,-30,0,-60],RU:[-20,-20,-40,-40,-30,-40,-40,-40],RUacute:[-20,-20,-40,-40,-30,-40,-40,-40],RUcircumflex:[-20,-20,-40,-40,-30,-40,-40,-40],RUdieresis:[-20,-20,-40,-40,-30,-40,-40,-40],RUgrave:[-20,-20,-40,-40,-30,-40,-40,-40],RUhungarumlaut:[-20,-20,-40,-40,-30,-40,-40,-40],RUmacron:[-20,-20,-40,-40,-30,-40,-40,-40],RUogonek:[-20,-20,-40,-40,-30,-40,-40,-40],RUring:[-20,-20,-40,-40,-30,-40,-40,-40],RV:[-50,-50,-50,-50,-55,-18,-18,-80],RW:[-40,-40,-30,-30,-35,-18,-18,-55],RY:[-50,-50,-50,-50,-35,-18,-18,-65],RYacute:[-50,-50,-50,-50,-35,-18,-18,-65],RYdieresis:[-50,-50,-50,-50,-35,-18,-18,-65],RacuteO:[-20,-20,-20,-20,-30,-40,-40,-40],RacuteOacute:[-20,-20,-20,-20,-30,-40,-40,-40],RacuteOcircumflex:[-20,-20,-20,-20,-30,-40,-40,-40],RacuteOdieresis:[-20,-20,-20,-20,-30,-40,-40,-40],RacuteOgrave:[-20,-20,-20,-20,-30,-40,-40,-40],RacuteOhungarumlaut:[-20,-20,-20,-20,-30,-40,-40,-40],RacuteOmacron:[-20,-20,-20,-20,-30,-40,-40,-40],RacuteOslash:[-20,-20,-20,-20,-30,-40,-40,-40],RacuteOtilde:[-20,-20,-20,-20,-30,-40,-40,-40],RacuteT:[-20,-20,-30,-30,-40,-30,0,-60],RacuteTcaron:[-20,-20,-30,-30,-40,-30,0,-60],RacuteTcommaaccent:[-20,-20,-30,-30,-40,-30,0,-60],RacuteU:[-20,-20,-40,-40,-30,-40,-40,-40],RacuteUacute:[-20,-20,-40,-40,-30,-40,-40,-40],RacuteUcircumflex:[-20,-20,-40,-40,-30,-40,-40,-40],RacuteUdieresis:[-20,-20,-40,-40,-30,-40,-40,-40],RacuteUgrave:[-20,-20,-40,-40,-30,-40,-40,-40],RacuteUhungarumlaut:[-20,-20,-40,-40,-30,-40,-40,-40],RacuteUmacron:[-20,-20,-40,-40,-30,-40,-40,-40],RacuteUogonek:[-20,-20,-40,-40,-30,-40,-40,-40],RacuteUring:[-20,-20,-40,-40,-30,-40,-40,-40],RacuteV:[-50,-50,-50,-50,-55,-18,-18,-80],RacuteW:[-40,-40,-30,-30,-35,-18,-18,-55],RacuteY:[-50,-50,-50,-50,-35,-18,-18,-65],RacuteYacute:[-50,-50,-50,-50,-35,-18,-18,-65],RacuteYdieresis:[-50,-50,-50,-50,-35,-18,-18,-65],RcaronO:[-20,-20,-20,-20,-30,-40,-40,-40],RcaronOacute:[-20,-20,-20,-20,-30,-40,-40,-40],RcaronOcircumflex:[-20,-20,-20,-20,-30,-40,-40,-40],RcaronOdieresis:[-20,-20,-20,-20,-30,-40,-40,-40],RcaronOgrave:[-20,-20,-20,-20,-30,-40,-40,-40],RcaronOhungarumlaut:[-20,-20,-20,-20,-30,-40,-40,-40],RcaronOmacron:[-20,-20,-20,-20,-30,-40,-40,-40],RcaronOslash:[-20,-20,-20,-20,-30,-40,-40,-40],RcaronOtilde:[-20,-20,-20,-20,-30,-40,-40,-40],RcaronT:[-20,-20,-30,-30,-40,-30,0,-60],RcaronTcaron:[-20,-20,-30,-30,-40,-30,0,-60],RcaronTcommaaccent:[-20,-20,-30,-30,-40,-30,0,-60],RcaronU:[-20,-20,-40,-40,-30,-40,-40,-40],RcaronUacute:[-20,-20,-40,-40,-30,-40,-40,-40],RcaronUcircumflex:[-20,-20,-40,-40,-30,-40,-40,-40],RcaronUdieresis:[-20,-20,-40,-40,-30,-40,-40,-40],RcaronUgrave:[-20,-20,-40,-40,-30,-40,-40,-40],RcaronUhungarumlaut:[-20,-20,-40,-40,-30,-40,-40,-40],RcaronUmacron:[-20,-20,-40,-40,-30,-40,-40,-40],RcaronUogonek:[-20,-20,-40,-40,-30,-40,-40,-40],RcaronUring:[-20,-20,-40,-40,-30,-40,-40,-40],RcaronV:[-50,-50,-50,-50,-55,-18,-18,-80],RcaronW:[-40,-40,-30,-30,-35,-18,-18,-55],RcaronY:[-50,-50,-50,-50,-35,-18,-18,-65],RcaronYacute:[-50,-50,-50,-50,-35,-18,-18,-65],RcaronYdieresis:[-50,-50,-50,-50,-35,-18,-18,-65],RcommaaccentO:[-20,-20,-20,-20,-30,-40,-40,-40],RcommaaccentOacute:[-20,-20,-20,-20,-30,-40,-40,-40],RcommaaccentOcircumflex:[-20,-20,-20,-20,-30,-40,-40,-40],RcommaaccentOdieresis:[-20,-20,-20,-20,-30,-40,-40,-40],RcommaaccentOgrave:[-20,-20,-20,-20,-30,-40,-40,-40],RcommaaccentOhungarumlaut:[-20,-20,-20,-20,-30,-40,-40,-40],RcommaaccentOmacron:[-20,-20,-20,-20,-30,-40,-40,-40],RcommaaccentOslash:[-20,-20,-20,-20,-30,-40,-40,-40],RcommaaccentOtilde:[-20,-20,-20,-20,-30,-40,-40,-40],RcommaaccentT:[-20,-20,-30,-30,-40,-30,0,-60],RcommaaccentTcaron:[-20,-20,-30,-30,-40,-30,0,-60],RcommaaccentTcommaaccent:[-20,-20,-30,-30,-40,-30,0,-60],RcommaaccentU:[-20,-20,-40,-40,-30,-40,-40,-40],RcommaaccentUacute:[-20,-20,-40,-40,-30,-40,-40,-40],RcommaaccentUcircumflex:[-20,-20,-40,-40,-30,-40,-40,-40],RcommaaccentUdieresis:[-20,-20,-40,-40,-30,-40,-40,-40],RcommaaccentUgrave:[-20,-20,-40,-40,-30,-40,-40,-40],RcommaaccentUhungarumlaut:[-20,-20,-40,-40,-30,-40,-40,-40],RcommaaccentUmacron:[-20,-20,-40,-40,-30,-40,-40,-40],RcommaaccentUogonek:[-20,-20,-40,-40,-30,-40,-40,-40],RcommaaccentUring:[-20,-20,-40,-40,-30,-40,-40,-40],RcommaaccentV:[-50,-50,-50,-50,-55,-18,-18,-80],RcommaaccentW:[-40,-40,-30,-30,-35,-18,-18,-55],RcommaaccentY:[-50,-50,-50,-50,-35,-18,-18,-65],RcommaaccentYacute:[-50,-50,-50,-50,-35,-18,-18,-65],RcommaaccentYdieresis:[-50,-50,-50,-50,-35,-18,-18,-65],TA:[-90,-90,-120,-120,-90,-55,-50,-93],TAacute:[-90,-90,-120,-120,-90,-55,-50,-93],TAbreve:[-90,-90,-120,-120,-90,-55,-50,-93],TAcircumflex:[-90,-90,-120,-120,-90,-55,-50,-93],TAdieresis:[-90,-90,-120,-120,-90,-55,-50,-93],TAgrave:[-90,-90,-120,-120,-90,-55,-50,-93],TAmacron:[-90,-90,-120,-120,-90,-55,-50,-93],TAogonek:[-90,-90,-120,-120,-90,-55,-50,-93],TAring:[-90,-90,-120,-120,-90,-55,-50,-93],TAtilde:[-90,-90,-120,-120,-90,-55,-50,-93],TO:[-40,-40,-40,-40,-18,-18,-18,-18],TOacute:[-40,-40,-40,-40,-18,-18,-18,-18],TOcircumflex:[-40,-40,-40,-40,-18,-18,-18,-18],TOdieresis:[-40,-40,-40,-40,-18,-18,-18,-18],TOgrave:[-40,-40,-40,-40,-18,-18,-18,-18],TOhungarumlaut:[-40,-40,-40,-40,-18,-18,-18,-18],TOmacron:[-40,-40,-40,-40,-18,-18,-18,-18],TOslash:[-40,-40,-40,-40,-18,-18,-18,-18],TOtilde:[-40,-40,-40,-40,-18,-18,-18,-18],Ta:[-80,-80,-120,-120,-92,-92,-92,-80],Taacute:[-80,-80,-120,-120,-92,-92,-92,-80],Tabreve:[-80,-80,-60,-60,-52,-92,-92,-80],Tacircumflex:[-80,-80,-120,-120,-52,-92,-92,-80],Tadieresis:[-80,-80,-120,-120,-52,-92,-92,-40],Tagrave:[-80,-80,-120,-120,-52,-92,-92,-40],Tamacron:[-80,-80,-60,-60,-52,-92,-92,-40],Taogonek:[-80,-80,-120,-120,-92,-92,-92,-80],Taring:[-80,-80,-120,-120,-92,-92,-92,-80],Tatilde:[-80,-80,-60,-60,-52,-92,-92,-40],Tcolon:[-40,-40,-20,-20,-74,-74,-55,-50],Tcomma:[-80,-80,-120,-120,-74,-92,-74,-74],Te:[-60,-60,-120,-120,-92,-92,-92,-70],Teacute:[-60,-60,-120,-120,-92,-92,-92,-70],Tecaron:[-60,-60,-120,-120,-92,-92,-92,-70],Tecircumflex:[-60,-60,-120,-120,-92,-92,-52,-70],Tedieresis:[-60,-60,-120,-120,-52,-52,-52,-30],Tedotaccent:[-60,-60,-120,-120,-92,-92,-92,-70],Tegrave:[-60,-60,-60,-60,-52,-52,-52,-70],Temacron:[-60,-60,-60,-60,-52,-52,-52,-30],Teogonek:[-60,-60,-120,-120,-92,-92,-92,-70],Thyphen:[-120,-120,-140,-140,-92,-92,-74,-92],To:[-80,-80,-120,-120,-92,-95,-92,-80],Toacute:[-80,-80,-120,-120,-92,-95,-92,-80],Tocircumflex:[-80,-80,-120,-120,-92,-95,-92,-80],Todieresis:[-80,-80,-120,-120,-92,-95,-92,-80],Tograve:[-80,-80,-120,-120,-92,-95,-92,-80],Tohungarumlaut:[-80,-80,-120,-120,-92,-95,-92,-80],Tomacron:[-80,-80,-60,-60,-92,-95,-92,-80],Toslash:[-80,-80,-120,-120,-92,-95,-92,-80],Totilde:[-80,-80,-60,-60,-92,-95,-92,-80],Tperiod:[-80,-80,-120,-120,-90,-92,-74,-74],Tr:[-80,-80,-120,-120,-74,-37,-55,-35],Tracute:[-80,-80,-120,-120,-74,-37,-55,-35],Trcommaaccent:[-80,-80,-120,-120,-74,-37,-55,-35],Tsemicolon:[-40,-40,-20,-20,-74,-74,-65,-55],Tu:[-90,-90,-120,-120,-92,-37,-55,-45],Tuacute:[-90,-90,-120,-120,-92,-37,-55,-45],Tucircumflex:[-90,-90,-120,-120,-92,-37,-55,-45],Tudieresis:[-90,-90,-120,-120,-92,-37,-55,-45],Tugrave:[-90,-90,-120,-120,-92,-37,-55,-45],Tuhungarumlaut:[-90,-90,-120,-120,-92,-37,-55,-45],Tumacron:[-90,-90,-60,-60,-92,-37,-55,-45],Tuogonek:[-90,-90,-120,-120,-92,-37,-55,-45],Turing:[-90,-90,-120,-120,-92,-37,-55,-45],Tw:[-60,-60,-120,-120,-74,-37,-74,-80],Ty:[-60,-60,-120,-120,-34,-37,-74,-80],Tyacute:[-60,-60,-120,-120,-34,-37,-74,-80],Tydieresis:[-60,-60,-60,-60,-34,-37,-34,-80],TcaronA:[-90,-90,-120,-120,-90,-55,-50,-93],TcaronAacute:[-90,-90,-120,-120,-90,-55,-50,-93],TcaronAbreve:[-90,-90,-120,-120,-90,-55,-50,-93],TcaronAcircumflex:[-90,-90,-120,-120,-90,-55,-50,-93],TcaronAdieresis:[-90,-90,-120,-120,-90,-55,-50,-93],TcaronAgrave:[-90,-90,-120,-120,-90,-55,-50,-93],TcaronAmacron:[-90,-90,-120,-120,-90,-55,-50,-93],TcaronAogonek:[-90,-90,-120,-120,-90,-55,-50,-93],TcaronAring:[-90,-90,-120,-120,-90,-55,-50,-93],TcaronAtilde:[-90,-90,-120,-120,-90,-55,-50,-93],TcaronO:[-40,-40,-40,-40,-18,-18,-18,-18],TcaronOacute:[-40,-40,-40,-40,-18,-18,-18,-18],TcaronOcircumflex:[-40,-40,-40,-40,-18,-18,-18,-18],TcaronOdieresis:[-40,-40,-40,-40,-18,-18,-18,-18],TcaronOgrave:[-40,-40,-40,-40,-18,-18,-18,-18],TcaronOhungarumlaut:[-40,-40,-40,-40,-18,-18,-18,-18],TcaronOmacron:[-40,-40,-40,-40,-18,-18,-18,-18],TcaronOslash:[-40,-40,-40,-40,-18,-18,-18,-18],TcaronOtilde:[-40,-40,-40,-40,-18,-18,-18,-18],Tcarona:[-80,-80,-120,-120,-92,-92,-92,-80],Tcaronaacute:[-80,-80,-120,-120,-92,-92,-92,-80],Tcaronabreve:[-80,-80,-60,-60,-52,-92,-92,-80],Tcaronacircumflex:[-80,-80,-120,-120,-52,-92,-92,-80],Tcaronadieresis:[-80,-80,-120,-120,-52,-92,-92,-40],Tcaronagrave:[-80,-80,-120,-120,-52,-92,-92,-40],Tcaronamacron:[-80,-80,-60,-60,-52,-92,-92,-40],Tcaronaogonek:[-80,-80,-120,-120,-92,-92,-92,-80],Tcaronaring:[-80,-80,-120,-120,-92,-92,-92,-80],Tcaronatilde:[-80,-80,-60,-60,-52,-92,-92,-40],Tcaroncolon:[-40,-40,-20,-20,-74,-74,-55,-50],Tcaroncomma:[-80,-80,-120,-120,-74,-92,-74,-74],Tcarone:[-60,-60,-120,-120,-92,-92,-92,-70],Tcaroneacute:[-60,-60,-120,-120,-92,-92,-92,-70],Tcaronecaron:[-60,-60,-120,-120,-92,-92,-92,-70],Tcaronecircumflex:[-60,-60,-120,-120,-92,-92,-52,-30],Tcaronedieresis:[-60,-60,-120,-120,-52,-52,-52,-30],Tcaronedotaccent:[-60,-60,-120,-120,-92,-92,-92,-70],Tcaronegrave:[-60,-60,-60,-60,-52,-52,-52,-70],Tcaronemacron:[-60,-60,-60,-60,-52,-52,-52,-30],Tcaroneogonek:[-60,-60,-120,-120,-92,-92,-92,-70],Tcaronhyphen:[-120,-120,-140,-140,-92,-92,-74,-92],Tcarono:[-80,-80,-120,-120,-92,-95,-92,-80],Tcaronoacute:[-80,-80,-120,-120,-92,-95,-92,-80],Tcaronocircumflex:[-80,-80,-120,-120,-92,-95,-92,-80],Tcaronodieresis:[-80,-80,-120,-120,-92,-95,-92,-80],Tcaronograve:[-80,-80,-120,-120,-92,-95,-92,-80],Tcaronohungarumlaut:[-80,-80,-120,-120,-92,-95,-92,-80],Tcaronomacron:[-80,-80,-60,-60,-92,-95,-92,-80],Tcaronoslash:[-80,-80,-120,-120,-92,-95,-92,-80],Tcaronotilde:[-80,-80,-60,-60,-92,-95,-92,-80],Tcaronperiod:[-80,-80,-120,-120,-90,-92,-74,-74],Tcaronr:[-80,-80,-120,-120,-74,-37,-55,-35],Tcaronracute:[-80,-80,-120,-120,-74,-37,-55,-35],Tcaronrcommaaccent:[-80,-80,-120,-120,-74,-37,-55,-35],Tcaronsemicolon:[-40,-40,-20,-20,-74,-74,-65,-55],Tcaronu:[-90,-90,-120,-120,-92,-37,-55,-45],Tcaronuacute:[-90,-90,-120,-120,-92,-37,-55,-45],Tcaronucircumflex:[-90,-90,-120,-120,-92,-37,-55,-45],Tcaronudieresis:[-90,-90,-120,-120,-92,-37,-55,-45],Tcaronugrave:[-90,-90,-120,-120,-92,-37,-55,-45],Tcaronuhungarumlaut:[-90,-90,-120,-120,-92,-37,-55,-45],Tcaronumacron:[-90,-90,-60,-60,-92,-37,-55,-45],Tcaronuogonek:[-90,-90,-120,-120,-92,-37,-55,-45],Tcaronuring:[-90,-90,-120,-120,-92,-37,-55,-45],Tcaronw:[-60,-60,-120,-120,-74,-37,-74,-80],Tcarony:[-60,-60,-120,-120,-34,-37,-74,-80],Tcaronyacute:[-60,-60,-120,-120,-34,-37,-74,-80],Tcaronydieresis:[-60,-60,-60,-60,-34,-37,-34,-80],TcommaaccentA:[-90,-90,-120,-120,-90,-55,-50,-93],TcommaaccentAacute:[-90,-90,-120,-120,-90,-55,-50,-93],TcommaaccentAbreve:[-90,-90,-120,-120,-90,-55,-50,-93],TcommaaccentAcircumflex:[-90,-90,-120,-120,-90,-55,-50,-93],TcommaaccentAdieresis:[-90,-90,-120,-120,-90,-55,-50,-93],TcommaaccentAgrave:[-90,-90,-120,-120,-90,-55,-50,-93],TcommaaccentAmacron:[-90,-90,-120,-120,-90,-55,-50,-93],TcommaaccentAogonek:[-90,-90,-120,-120,-90,-55,-50,-93],TcommaaccentAring:[-90,-90,-120,-120,-90,-55,-50,-93],TcommaaccentAtilde:[-90,-90,-120,-120,-90,-55,-50,-93],TcommaaccentO:[-40,-40,-40,-40,-18,-18,-18,-18],TcommaaccentOacute:[-40,-40,-40,-40,-18,-18,-18,-18],TcommaaccentOcircumflex:[-40,-40,-40,-40,-18,-18,-18,-18],TcommaaccentOdieresis:[-40,-40,-40,-40,-18,-18,-18,-18],TcommaaccentOgrave:[-40,-40,-40,-40,-18,-18,-18,-18],TcommaaccentOhungarumlaut:[-40,-40,-40,-40,-18,-18,-18,-18],TcommaaccentOmacron:[-40,-40,-40,-40,-18,-18,-18,-18],TcommaaccentOslash:[-40,-40,-40,-40,-18,-18,-18,-18],TcommaaccentOtilde:[-40,-40,-40,-40,-18,-18,-18,-18],Tcommaaccenta:[-80,-80,-120,-120,-92,-92,-92,-80],Tcommaaccentaacute:[-80,-80,-120,-120,-92,-92,-92,-80],Tcommaaccentabreve:[-80,-80,-60,-60,-52,-92,-92,-80],Tcommaaccentacircumflex:[-80,-80,-120,-120,-52,-92,-92,-80],Tcommaaccentadieresis:[-80,-80,-120,-120,-52,-92,-92,-40],Tcommaaccentagrave:[-80,-80,-120,-120,-52,-92,-92,-40],Tcommaaccentamacron:[-80,-80,-60,-60,-52,-92,-92,-40],Tcommaaccentaogonek:[-80,-80,-120,-120,-92,-92,-92,-80],Tcommaaccentaring:[-80,-80,-120,-120,-92,-92,-92,-80],Tcommaaccentatilde:[-80,-80,-60,-60,-52,-92,-92,-40],Tcommaaccentcolon:[-40,-40,-20,-20,-74,-74,-55,-50],Tcommaaccentcomma:[-80,-80,-120,-120,-74,-92,-74,-74],Tcommaaccente:[-60,-60,-120,-120,-92,-92,-92,-70],Tcommaaccenteacute:[-60,-60,-120,-120,-92,-92,-92,-70],Tcommaaccentecaron:[-60,-60,-120,-120,-92,-92,-92,-70],Tcommaaccentecircumflex:[-60,-60,-120,-120,-92,-92,-52,-30],Tcommaaccentedieresis:[-60,-60,-120,-120,-52,-52,-52,-30],Tcommaaccentedotaccent:[-60,-60,-120,-120,-92,-92,-92,-70],Tcommaaccentegrave:[-60,-60,-60,-60,-52,-52,-52,-30],Tcommaaccentemacron:[-60,-60,-60,-60,-52,-52,-52,-70],Tcommaaccenteogonek:[-60,-60,-120,-120,-92,-92,-92,-70],Tcommaaccenthyphen:[-120,-120,-140,-140,-92,-92,-74,-92],Tcommaaccento:[-80,-80,-120,-120,-92,-95,-92,-80],Tcommaaccentoacute:[-80,-80,-120,-120,-92,-95,-92,-80],Tcommaaccentocircumflex:[-80,-80,-120,-120,-92,-95,-92,-80],Tcommaaccentodieresis:[-80,-80,-120,-120,-92,-95,-92,-80],Tcommaaccentograve:[-80,-80,-120,-120,-92,-95,-92,-80],Tcommaaccentohungarumlaut:[-80,-80,-120,-120,-92,-95,-92,-80],Tcommaaccentomacron:[-80,-80,-60,-60,-92,-95,-92,-80],Tcommaaccentoslash:[-80,-80,-120,-120,-92,-95,-92,-80],Tcommaaccentotilde:[-80,-80,-60,-60,-92,-95,-92,-80],Tcommaaccentperiod:[-80,-80,-120,-120,-90,-92,-74,-74],Tcommaaccentr:[-80,-80,-120,-120,-74,-37,-55,-35],Tcommaaccentracute:[-80,-80,-120,-120,-74,-37,-55,-35],Tcommaaccentrcommaaccent:[-80,-80,-120,-120,-74,-37,-55,-35],Tcommaaccentsemicolon:[-40,-40,-20,-20,-74,-74,-65,-55],Tcommaaccentu:[-90,-90,-120,-120,-92,-37,-55,-45],Tcommaaccentuacute:[-90,-90,-120,-120,-92,-37,-55,-45],Tcommaaccentucircumflex:[-90,-90,-120,-120,-92,-37,-55,-45],Tcommaaccentudieresis:[-90,-90,-120,-120,-92,-37,-55,-45],Tcommaaccentugrave:[-90,-90,-120,-120,-92,-37,-55,-45],Tcommaaccentuhungarumlaut:[-90,-90,-120,-120,-92,-37,-55,-45],Tcommaaccentumacron:[-90,-90,-60,-60,-92,-37,-55,-45],Tcommaaccentuogonek:[-90,-90,-120,-120,-92,-37,-55,-45],Tcommaaccenturing:[-90,-90,-120,-120,-92,-37,-55,-45],Tcommaaccentw:[-60,-60,-120,-120,-74,-37,-74,-80],Tcommaaccenty:[-60,-60,-120,-120,-34,-37,-74,-80],Tcommaaccentyacute:[-60,-60,-120,-120,-34,-37,-74,-80],Tcommaaccentydieresis:[-60,-60,-60,-60,-34,-37,-34,-80],UA:[-50,-50,-40,-40,-60,-45,-40,-40],UAacute:[-50,-50,-40,-40,-60,-45,-40,-40],UAbreve:[-50,-50,-40,-40,-60,-45,-40,-40],UAcircumflex:[-50,-50,-40,-40,-60,-45,-40,-40],UAdieresis:[-50,-50,-40,-40,-60,-45,-40,-40],UAgrave:[-50,-50,-40,-40,-60,-45,-40,-40],UAmacron:[-50,-50,-40,-40,-60,-45,-40,-40],UAogonek:[-50,-50,-40,-40,-60,-45,-40,-40],UAring:[-50,-50,-40,-40,-60,-45,-40,-40],UAtilde:[-50,-50,-40,-40,-60,-45,-40,-40],Ucomma:[-30,-30,-40,-40,-50,0,-25],Uperiod:[-30,-30,-40,-40,-50,0,-25],UacuteA:[-50,-50,-40,-40,-60,-45,-40,-40],UacuteAacute:[-50,-50,-40,-40,-60,-45,-40,-40],UacuteAbreve:[-50,-50,-40,-40,-60,-45,-40,-40],UacuteAcircumflex:[-50,-50,-40,-40,-60,-45,-40,-40],UacuteAdieresis:[-50,-50,-40,-40,-60,-45,-40,-40],UacuteAgrave:[-50,-50,-40,-40,-60,-45,-40,-40],UacuteAmacron:[-50,-50,-40,-40,-60,-45,-40,-40],UacuteAogonek:[-50,-50,-40,-40,-60,-45,-40,-40],UacuteAring:[-50,-50,-40,-40,-60,-45,-40,-40],UacuteAtilde:[-50,-50,-40,-40,-60,-45,-40,-40],Uacutecomma:[-30,-30,-40,-40,-50,0,-25],Uacuteperiod:[-30,-30,-40,-40,-50,0,-25],UcircumflexA:[-50,-50,-40,-40,-60,-45,-40,-40],UcircumflexAacute:[-50,-50,-40,-40,-60,-45,-40,-40],UcircumflexAbreve:[-50,-50,-40,-40,-60,-45,-40,-40],UcircumflexAcircumflex:[-50,-50,-40,-40,-60,-45,-40,-40],UcircumflexAdieresis:[-50,-50,-40,-40,-60,-45,-40,-40],UcircumflexAgrave:[-50,-50,-40,-40,-60,-45,-40,-40],UcircumflexAmacron:[-50,-50,-40,-40,-60,-45,-40,-40],UcircumflexAogonek:[-50,-50,-40,-40,-60,-45,-40,-40],UcircumflexAring:[-50,-50,-40,-40,-60,-45,-40,-40],UcircumflexAtilde:[-50,-50,-40,-40,-60,-45,-40,-40],Ucircumflexcomma:[-30,-30,-40,-40,-50,0,-25],Ucircumflexperiod:[-30,-30,-40,-40,-50,0,-25],UdieresisA:[-50,-50,-40,-40,-60,-45,-40,-40],UdieresisAacute:[-50,-50,-40,-40,-60,-45,-40,-40],UdieresisAbreve:[-50,-50,-40,-40,-60,-45,-40,-40],UdieresisAcircumflex:[-50,-50,-40,-40,-60,-45,-40,-40],UdieresisAdieresis:[-50,-50,-40,-40,-60,-45,-40,-40],UdieresisAgrave:[-50,-50,-40,-40,-60,-45,-40,-40],UdieresisAmacron:[-50,-50,-40,-40,-60,-45,-40,-40],UdieresisAogonek:[-50,-50,-40,-40,-60,-45,-40,-40],UdieresisAring:[-50,-50,-40,-40,-60,-45,-40,-40],UdieresisAtilde:[-50,-50,-40,-40,-60,-45,-40,-40],Udieresiscomma:[-30,-30,-40,-40,-50,0,-25],Udieresisperiod:[-30,-30,-40,-40,-50,0,-25],UgraveA:[-50,-50,-40,-40,-60,-45,-40,-40],UgraveAacute:[-50,-50,-40,-40,-60,-45,-40,-40],UgraveAbreve:[-50,-50,-40,-40,-60,-45,-40,-40],UgraveAcircumflex:[-50,-50,-40,-40,-60,-45,-40,-40],UgraveAdieresis:[-50,-50,-40,-40,-60,-45,-40,-40],UgraveAgrave:[-50,-50,-40,-40,-60,-45,-40,-40],UgraveAmacron:[-50,-50,-40,-40,-60,-45,-40,-40],UgraveAogonek:[-50,-50,-40,-40,-60,-45,-40,-40],UgraveAring:[-50,-50,-40,-40,-60,-45,-40,-40],UgraveAtilde:[-50,-50,-40,-40,-60,-45,-40,-40],Ugravecomma:[-30,-30,-40,-40,-50,0,-25],Ugraveperiod:[-30,-30,-40,-40,-50,0,-25],UhungarumlautA:[-50,-50,-40,-40,-60,-45,-40,-40],UhungarumlautAacute:[-50,-50,-40,-40,-60,-45,-40,-40],UhungarumlautAbreve:[-50,-50,-40,-40,-60,-45,-40,-40],UhungarumlautAcircumflex:[-50,-50,-40,-40,-60,-45,-40,-40],UhungarumlautAdieresis:[-50,-50,-40,-40,-60,-45,-40,-40],UhungarumlautAgrave:[-50,-50,-40,-40,-60,-45,-40,-40],UhungarumlautAmacron:[-50,-50,-40,-40,-60,-45,-40,-40],UhungarumlautAogonek:[-50,-50,-40,-40,-60,-45,-40,-40],UhungarumlautAring:[-50,-50,-40,-40,-60,-45,-40,-40],UhungarumlautAtilde:[-50,-50,-40,-40,-60,-45,-40,-40],Uhungarumlautcomma:[-30,-30,-40,-40,-50,0,-25],Uhungarumlautperiod:[-30,-30,-40,-40,-50,0,-25],UmacronA:[-50,-50,-40,-40,-60,-45,-40,-40],UmacronAacute:[-50,-50,-40,-40,-60,-45,-40,-40],UmacronAbreve:[-50,-50,-40,-40,-60,-45,-40,-40],UmacronAcircumflex:[-50,-50,-40,-40,-60,-45,-40,-40],UmacronAdieresis:[-50,-50,-40,-40,-60,-45,-40,-40],UmacronAgrave:[-50,-50,-40,-40,-60,-45,-40,-40],UmacronAmacron:[-50,-50,-40,-40,-60,-45,-40,-40],UmacronAogonek:[-50,-50,-40,-40,-60,-45,-40,-40],UmacronAring:[-50,-50,-40,-40,-60,-45,-40,-40],UmacronAtilde:[-50,-50,-40,-40,-60,-45,-40,-40],Umacroncomma:[-30,-30,-40,-40,-50,0,-25],Umacronperiod:[-30,-30,-40,-40,-50,0,-25],UogonekA:[-50,-50,-40,-40,-60,-45,-40,-40],UogonekAacute:[-50,-50,-40,-40,-60,-45,-40,-40],UogonekAbreve:[-50,-50,-40,-40,-60,-45,-40,-40],UogonekAcircumflex:[-50,-50,-40,-40,-60,-45,-40,-40],UogonekAdieresis:[-50,-50,-40,-40,-60,-45,-40,-40],UogonekAgrave:[-50,-50,-40,-40,-60,-45,-40,-40],UogonekAmacron:[-50,-50,-40,-40,-60,-45,-40,-40],UogonekAogonek:[-50,-50,-40,-40,-60,-45,-40,-40],UogonekAring:[-50,-50,-40,-40,-60,-45,-40,-40],UogonekAtilde:[-50,-50,-40,-40,-60,-45,-40,-40],Uogonekcomma:[-30,-30,-40,-40,-50,0,-25],Uogonekperiod:[-30,-30,-40,-40,-50,0,-25],UringA:[-50,-50,-40,-40,-60,-45,-40,-40],UringAacute:[-50,-50,-40,-40,-60,-45,-40,-40],UringAbreve:[-50,-50,-40,-40,-60,-45,-40,-40],UringAcircumflex:[-50,-50,-40,-40,-60,-45,-40,-40],UringAdieresis:[-50,-50,-40,-40,-60,-45,-40,-40],UringAgrave:[-50,-50,-40,-40,-60,-45,-40,-40],UringAmacron:[-50,-50,-40,-40,-60,-45,-40,-40],UringAogonek:[-50,-50,-40,-40,-60,-45,-40,-40],UringAring:[-50,-50,-40,-40,-60,-45,-40,-40],UringAtilde:[-50,-50,-40,-40,-60,-45,-40,-40],Uringcomma:[-30,-30,-40,-40,-50,0,-25],Uringperiod:[-30,-30,-40,-40,-50,0,-25],VA:[-80,-80,-80,-80,-135,-85,-60,-135],VAacute:[-80,-80,-80,-80,-135,-85,-60,-135],VAbreve:[-80,-80,-80,-80,-135,-85,-60,-135],VAcircumflex:[-80,-80,-80,-80,-135,-85,-60,-135],VAdieresis:[-80,-80,-80,-80,-135,-85,-60,-135],VAgrave:[-80,-80,-80,-80,-135,-85,-60,-135],VAmacron:[-80,-80,-80,-80,-135,-85,-60,-135],VAogonek:[-80,-80,-80,-80,-135,-85,-60,-135],VAring:[-80,-80,-80,-80,-135,-85,-60,-135],VAtilde:[-80,-80,-80,-80,-135,-85,-60,-135],VG:[-50,-50,-40,-40,-30,-10,0,-15],VGbreve:[-50,-50,-40,-40,-30,-10,0,-15],VGcommaaccent:[-50,-50,-40,-40,-30,-10,0,-15],VO:[-50,-50,-40,-40,-45,-30,-30,-40],VOacute:[-50,-50,-40,-40,-45,-30,-30,-40],VOcircumflex:[-50,-50,-40,-40,-45,-30,-30,-40],VOdieresis:[-50,-50,-40,-40,-45,-30,-30,-40],VOgrave:[-50,-50,-40,-40,-45,-30,-30,-40],VOhungarumlaut:[-50,-50,-40,-40,-45,-30,-30,-40],VOmacron:[-50,-50,-40,-40,-45,-30,-30,-40],VOslash:[-50,-50,-40,-40,-45,-30,-30,-40],VOtilde:[-50,-50,-40,-40,-45,-30,-30,-40],Va:[-60,-60,-70,-70,-92,-111,-111,-111],Vaacute:[-60,-60,-70,-70,-92,-111,-111,-111],Vabreve:[-60,-60,-70,-70,-92,-111,-111,-111],Vacircumflex:[-60,-60,-70,-70,-92,-111,-111,-71],Vadieresis:[-60,-60,-70,-70,-92,-111,-111,-71],Vagrave:[-60,-60,-70,-70,-92,-111,-111,-71],Vamacron:[-60,-60,-70,-70,-92,-111,-111,-71],Vaogonek:[-60,-60,-70,-70,-92,-111,-111,-111],Varing:[-60,-60,-70,-70,-92,-111,-111,-111],Vatilde:[-60,-60,-70,-70,-92,-111,-111,-71],Vcolon:[-40,-40,-40,-40,-92,-74,-65,-74],Vcomma:[-120,-120,-125,-125,-129,-129,-129,-129],Ve:[-50,-50,-80,-80,-100,-111,-111,-111],Veacute:[-50,-50,-80,-80,-100,-111,-111,-111],Vecaron:[-50,-50,-80,-80,-100,-111,-111,-71],Vecircumflex:[-50,-50,-80,-80,-100,-111,-111,-71],Vedieresis:[-50,-50,-80,-80,-100,-71,-71,-71],Vedotaccent:[-50,-50,-80,-80,-100,-111,-111,-111],Vegrave:[-50,-50,-80,-80,-100,-71,-71,-71],Vemacron:[-50,-50,-80,-80,-100,-71,-71,-71],Veogonek:[-50,-50,-80,-80,-100,-111,-111,-111],Vhyphen:[-80,-80,-80,-80,-74,-70,-55,-100],Vo:[-90,-90,-80,-80,-100,-111,-111,-129],Voacute:[-90,-90,-80,-80,-100,-111,-111,-129],Vocircumflex:[-90,-90,-80,-80,-100,-111,-111,-129],Vodieresis:[-90,-90,-80,-80,-100,-111,-111,-89],Vograve:[-90,-90,-80,-80,-100,-111,-111,-89],Vohungarumlaut:[-90,-90,-80,-80,-100,-111,-111,-129],Vomacron:[-90,-90,-80,-80,-100,-111,-111,-89],Voslash:[-90,-90,-80,-80,-100,-111,-111,-129],Votilde:[-90,-90,-80,-80,-100,-111,-111,-89],Vperiod:[-120,-120,-125,-125,-145,-129,-129,-129],Vsemicolon:[-40,-40,-40,-40,-92,-74,-74,-74],Vu:[-60,-60,-70,-70,-92,-55,-74,-75],Vuacute:[-60,-60,-70,-70,-92,-55,-74,-75],Vucircumflex:[-60,-60,-70,-70,-92,-55,-74,-75],Vudieresis:[-60,-60,-70,-70,-92,-55,-74,-75],Vugrave:[-60,-60,-70,-70,-92,-55,-74,-75],Vuhungarumlaut:[-60,-60,-70,-70,-92,-55,-74,-75],Vumacron:[-60,-60,-70,-70,-92,-55,-74,-75],Vuogonek:[-60,-60,-70,-70,-92,-55,-74,-75],Vuring:[-60,-60,-70,-70,-92,-55,-74,-75],WA:[-60,-60,-50,-50,-120,-74,-60,-120],WAacute:[-60,-60,-50,-50,-120,-74,-60,-120],WAbreve:[-60,-60,-50,-50,-120,-74,-60,-120],WAcircumflex:[-60,-60,-50,-50,-120,-74,-60,-120],WAdieresis:[-60,-60,-50,-50,-120,-74,-60,-120],WAgrave:[-60,-60,-50,-50,-120,-74,-60,-120],WAmacron:[-60,-60,-50,-50,-120,-74,-60,-120],WAogonek:[-60,-60,-50,-50,-120,-74,-60,-120],WAring:[-60,-60,-50,-50,-120,-74,-60,-120],WAtilde:[-60,-60,-50,-50,-120,-74,-60,-120],WO:[-20,-20,-20,-20,-10,-15,-25,-10],WOacute:[-20,-20,-20,-20,-10,-15,-25,-10],WOcircumflex:[-20,-20,-20,-20,-10,-15,-25,-10],WOdieresis:[-20,-20,-20,-20,-10,-15,-25,-10],WOgrave:[-20,-20,-20,-20,-10,-15,-25,-10],WOhungarumlaut:[-20,-20,-20,-20,-10,-15,-25,-10],WOmacron:[-20,-20,-20,-20,-10,-15,-25,-10],WOslash:[-20,-20,-20,-20,-10,-15,-25,-10],WOtilde:[-20,-20,-20,-20,-10,-15,-25,-10],Wa:[-40,-40,-40,-40,-65,-85,-92,-80],Waacute:[-40,-40,-40,-40,-65,-85,-92,-80],Wabreve:[-40,-40,-40,-40,-65,-85,-92,-80],Wacircumflex:[-40,-40,-40,-40,-65,-85,-92,-80],Wadieresis:[-40,-40,-40,-40,-65,-85,-92,-80],Wagrave:[-40,-40,-40,-40,-65,-85,-92,-80],Wamacron:[-40,-40,-40,-40,-65,-85,-92,-80],Waogonek:[-40,-40,-40,-40,-65,-85,-92,-80],Waring:[-40,-40,-40,-40,-65,-85,-92,-80],Watilde:[-40,-40,-40,-40,-65,-85,-92,-80],Wcolon:[-10,-10,0,0,-55,-55,-65,-37],Wcomma:[-80,-80,-80,-80,-92,-74,-92,-92],We:[-35,-35,-30,-30,-65,-90,-92,-80],Weacute:[-35,-35,-30,-30,-65,-90,-92,-80],Wecaron:[-35,-35,-30,-30,-65,-90,-92,-80],Wecircumflex:[-35,-35,-30,-30,-65,-90,-92,-80],Wedieresis:[-35,-35,-30,-30,-65,-50,-52,-40],Wedotaccent:[-35,-35,-30,-30,-65,-90,-92,-80],Wegrave:[-35,-35,-30,-30,-65,-50,-52,-40],Wemacron:[-35,-35,-30,-30,-65,-50,-52,-40],Weogonek:[-35,-35,-30,-30,-65,-90,-92,-80],Whyphen:[-40,-40,-40,-40,-37,-50,-37,-65],Wo:[-60,-60,-30,-30,-75,-80,-92,-80],Woacute:[-60,-60,-30,-30,-75,-80,-92,-80],Wocircumflex:[-60,-60,-30,-30,-75,-80,-92,-80],Wodieresis:[-60,-60,-30,-30,-75,-80,-92,-80],Wograve:[-60,-60,-30,-30,-75,-80,-92,-80],Wohungarumlaut:[-60,-60,-30,-30,-75,-80,-92,-80],Womacron:[-60,-60,-30,-30,-75,-80,-92,-80],Woslash:[-60,-60,-30,-30,-75,-80,-92,-80],Wotilde:[-60,-60,-30,-30,-75,-80,-92,-80],Wperiod:[-80,-80,-80,-80,-92,-74,-92,-92],Wsemicolon:[-10,-10,0,0,-55,-55,-65,-37],Wu:[-45,-45,-30,-30,-50,-55,-55,-50],Wuacute:[-45,-45,-30,-30,-50,-55,-55,-50],Wucircumflex:[-45,-45,-30,-30,-50,-55,-55,-50],Wudieresis:[-45,-45,-30,-30,-50,-55,-55,-50],Wugrave:[-45,-45,-30,-30,-50,-55,-55,-50],Wuhungarumlaut:[-45,-45,-30,-30,-50,-55,-55,-50],Wumacron:[-45,-45,-30,-30,-50,-55,-55,-50],Wuogonek:[-45,-45,-30,-30,-50,-55,-55,-50],Wuring:[-45,-45,-30,-30,-50,-55,-55,-50],Wy:[-20,-20,-20,-20,-60,-55,-70,-73],Wyacute:[-20,-20,-20,-20,-60,-55,-70,-73],Wydieresis:[-20,-20,-20,-20,-60,-55,-70,-73],YA:[-110,-110,-110,-110,-110,-74,-50,-120],YAacute:[-110,-110,-110,-110,-110,-74,-50,-120],YAbreve:[-110,-110,-110,-110,-110,-74,-50,-120],YAcircumflex:[-110,-110,-110,-110,-110,-74,-50,-120],YAdieresis:[-110,-110,-110,-110,-110,-74,-50,-120],YAgrave:[-110,-110,-110,-110,-110,-74,-50,-120],YAmacron:[-110,-110,-110,-110,-110,-74,-50,-120],YAogonek:[-110,-110,-110,-110,-110,-74,-50,-120],YAring:[-110,-110,-110,-110,-110,-74,-50,-120],YAtilde:[-110,-110,-110,-110,-110,-74,-50,-120],YO:[-70,-70,-85,-85,-35,-25,-15,-30],YOacute:[-70,-70,-85,-85,-35,-25,-15,-30],YOcircumflex:[-70,-70,-85,-85,-35,-25,-15,-30],YOdieresis:[-70,-70,-85,-85,-35,-25,-15,-30],YOgrave:[-70,-70,-85,-85,-35,-25,-15,-30],YOhungarumlaut:[-70,-70,-85,-85,-35,-25,-15,-30],YOmacron:[-70,-70,-85,-85,-35,-25,-15,-30],YOslash:[-70,-70,-85,-85,-35,-25,-15,-30],YOtilde:[-70,-70,-85,-85,-35,-25,-15,-30],Ya:[-90,-90,-140,-140,-85,-92,-92,-100],Yaacute:[-90,-90,-140,-140,-85,-92,-92,-100],Yabreve:[-90,-90,-70,-70,-85,-92,-92,-100],Yacircumflex:[-90,-90,-140,-140,-85,-92,-92,-100],Yadieresis:[-90,-90,-140,-140,-85,-92,-92,-60],Yagrave:[-90,-90,-140,-140,-85,-92,-92,-60],Yamacron:[-90,-90,-70,-70,-85,-92,-92,-60],Yaogonek:[-90,-90,-140,-140,-85,-92,-92,-100],Yaring:[-90,-90,-140,-140,-85,-92,-92,-100],Yatilde:[-90,-90,-140,-140,-85,-92,-92,-60],Ycolon:[-50,-50,-60,-60,-92,-92,-65,-92],Ycomma:[-100,-100,-140,-140,-92,-92,-92,-129],Ye:[-80,-80,-140,-140,-111,-111,-92,-100],Yeacute:[-80,-80,-140,-140,-111,-111,-92,-100],Yecaron:[-80,-80,-140,-140,-111,-111,-92,-100],Yecircumflex:[-80,-80,-140,-140,-111,-71,-92,-100],Yedieresis:[-80,-80,-140,-140,-71,-71,-52,-60],Yedotaccent:[-80,-80,-140,-140,-111,-111,-92,-100],Yegrave:[-80,-80,-140,-140,-71,-71,-52,-60],Yemacron:[-80,-80,-70,-70,-71,-71,-52,-60],Yeogonek:[-80,-80,-140,-140,-111,-111,-92,-100],Yo:[-100,-100,-140,-140,-111,-111,-92,-110],Yoacute:[-100,-100,-140,-140,-111,-111,-92,-110],Yocircumflex:[-100,-100,-140,-140,-111,-111,-92,-110],Yodieresis:[-100,-100,-140,-140,-111,-111,-92,-70],Yograve:[-100,-100,-140,-140,-111,-111,-92,-70],Yohungarumlaut:[-100,-100,-140,-140,-111,-111,-92,-110],Yomacron:[-100,-100,-140,-140,-111,-111,-92,-70],Yoslash:[-100,-100,-140,-140,-111,-111,-92,-110],Yotilde:[-100,-100,-140,-140,-111,-111,-92,-70],Yperiod:[-100,-100,-140,-140,-92,-74,-92,-129],Ysemicolon:[-50,-50,-60,-60,-92,-92,-65,-92],Yu:[-100,-100,-110,-110,-92,-92,-92,-111],Yuacute:[-100,-100,-110,-110,-92,-92,-92,-111],Yucircumflex:[-100,-100,-110,-110,-92,-92,-92,-111],Yudieresis:[-100,-100,-110,-110,-92,-92,-92,-71],Yugrave:[-100,-100,-110,-110,-92,-92,-92,-71],Yuhungarumlaut:[-100,-100,-110,-110,-92,-92,-92,-111],Yumacron:[-100,-100,-110,-110,-92,-92,-92,-71],Yuogonek:[-100,-100,-110,-110,-92,-92,-92,-111],Yuring:[-100,-100,-110,-110,-92,-92,-92,-111],YacuteA:[-110,-110,-110,-110,-110,-74,-50,-120],YacuteAacute:[-110,-110,-110,-110,-110,-74,-50,-120],YacuteAbreve:[-110,-110,-110,-110,-110,-74,-50,-120],YacuteAcircumflex:[-110,-110,-110,-110,-110,-74,-50,-120],YacuteAdieresis:[-110,-110,-110,-110,-110,-74,-50,-120],YacuteAgrave:[-110,-110,-110,-110,-110,-74,-50,-120],YacuteAmacron:[-110,-110,-110,-110,-110,-74,-50,-120],YacuteAogonek:[-110,-110,-110,-110,-110,-74,-50,-120],YacuteAring:[-110,-110,-110,-110,-110,-74,-50,-120],YacuteAtilde:[-110,-110,-110,-110,-110,-74,-50,-120],YacuteO:[-70,-70,-85,-85,-35,-25,-15,-30],YacuteOacute:[-70,-70,-85,-85,-35,-25,-15,-30],YacuteOcircumflex:[-70,-70,-85,-85,-35,-25,-15,-30],YacuteOdieresis:[-70,-70,-85,-85,-35,-25,-15,-30],YacuteOgrave:[-70,-70,-85,-85,-35,-25,-15,-30],YacuteOhungarumlaut:[-70,-70,-85,-85,-35,-25,-15,-30],YacuteOmacron:[-70,-70,-85,-85,-35,-25,-15,-30],YacuteOslash:[-70,-70,-85,-85,-35,-25,-15,-30],YacuteOtilde:[-70,-70,-85,-85,-35,-25,-15,-30],Yacutea:[-90,-90,-140,-140,-85,-92,-92,-100],Yacuteaacute:[-90,-90,-140,-140,-85,-92,-92,-100],Yacuteabreve:[-90,-90,-70,-70,-85,-92,-92,-100],Yacuteacircumflex:[-90,-90,-140,-140,-85,-92,-92,-100],Yacuteadieresis:[-90,-90,-140,-140,-85,-92,-92,-60],Yacuteagrave:[-90,-90,-140,-140,-85,-92,-92,-60],Yacuteamacron:[-90,-90,-70,-70,-85,-92,-92,-60],Yacuteaogonek:[-90,-90,-140,-140,-85,-92,-92,-100],Yacutearing:[-90,-90,-140,-140,-85,-92,-92,-100],Yacuteatilde:[-90,-90,-70,-70,-85,-92,-92,-60],Yacutecolon:[-50,-50,-60,-60,-92,-92,-65,-92],Yacutecomma:[-100,-100,-140,-140,-92,-92,-92,-129],Yacutee:[-80,-80,-140,-140,-111,-111,-92,-100],Yacuteeacute:[-80,-80,-140,-140,-111,-111,-92,-100],Yacuteecaron:[-80,-80,-140,-140,-111,-111,-92,-100],Yacuteecircumflex:[-80,-80,-140,-140,-111,-71,-92,-100],Yacuteedieresis:[-80,-80,-140,-140,-71,-71,-52,-60],Yacuteedotaccent:[-80,-80,-140,-140,-111,-111,-92,-100],Yacuteegrave:[-80,-80,-140,-140,-71,-71,-52,-60],Yacuteemacron:[-80,-80,-70,-70,-71,-71,-52,-60],Yacuteeogonek:[-80,-80,-140,-140,-111,-111,-92,-100],Yacuteo:[-100,-100,-140,-140,-111,-111,-92,-110],Yacuteoacute:[-100,-100,-140,-140,-111,-111,-92,-110],Yacuteocircumflex:[-100,-100,-140,-140,-111,-111,-92,-110],Yacuteodieresis:[-100,-100,-140,-140,-111,-111,-92,-70],Yacuteograve:[-100,-100,-140,-140,-111,-111,-92,-70],Yacuteohungarumlaut:[-100,-100,-140,-140,-111,-111,-92,-110],Yacuteomacron:[-100,-100,-70,-70,-111,-111,-92,-70],Yacuteoslash:[-100,-100,-140,-140,-111,-111,-92,-110],Yacuteotilde:[-100,-100,-140,-140,-111,-111,-92,-70],Yacuteperiod:[-100,-100,-140,-140,-92,-74,-92,-129],Yacutesemicolon:[-50,-50,-60,-60,-92,-92,-65,-92],Yacuteu:[-100,-100,-110,-110,-92,-92,-92,-111],Yacuteuacute:[-100,-100,-110,-110,-92,-92,-92,-111],Yacuteucircumflex:[-100,-100,-110,-110,-92,-92,-92,-111],Yacuteudieresis:[-100,-100,-110,-110,-92,-92,-92,-71],Yacuteugrave:[-100,-100,-110,-110,-92,-92,-92,-71],Yacuteuhungarumlaut:[-100,-100,-110,-110,-92,-92,-92,-111],Yacuteumacron:[-100,-100,-110,-110,-92,-92,-92,-71],Yacuteuogonek:[-100,-100,-110,-110,-92,-92,-92,-111],Yacuteuring:[-100,-100,-110,-110,-92,-92,-92,-111],YdieresisA:[-110,-110,-110,-110,-110,-74,-50,-120],YdieresisAacute:[-110,-110,-110,-110,-110,-74,-50,-120],YdieresisAbreve:[-110,-110,-110,-110,-110,-74,-50,-120],YdieresisAcircumflex:[-110,-110,-110,-110,-110,-74,-50,-120],YdieresisAdieresis:[-110,-110,-110,-110,-110,-74,-50,-120],YdieresisAgrave:[-110,-110,-110,-110,-110,-74,-50,-120],YdieresisAmacron:[-110,-110,-110,-110,-110,-74,-50,-120],YdieresisAogonek:[-110,-110,-110,-110,-110,-74,-50,-120],YdieresisAring:[-110,-110,-110,-110,-110,-74,-50,-120],YdieresisAtilde:[-110,-110,-110,-110,-110,-74,-50,-120],YdieresisO:[-70,-70,-85,-85,-35,-25,-15,-30],YdieresisOacute:[-70,-70,-85,-85,-35,-25,-15,-30],YdieresisOcircumflex:[-70,-70,-85,-85,-35,-25,-15,-30],YdieresisOdieresis:[-70,-70,-85,-85,-35,-25,-15,-30],YdieresisOgrave:[-70,-70,-85,-85,-35,-25,-15,-30],YdieresisOhungarumlaut:[-70,-70,-85,-85,-35,-25,-15,-30],YdieresisOmacron:[-70,-70,-85,-85,-35,-25,-15,-30],YdieresisOslash:[-70,-70,-85,-85,-35,-25,-15,-30],YdieresisOtilde:[-70,-70,-85,-85,-35,-25,-15,-30],Ydieresisa:[-90,-90,-140,-140,-85,-92,-92,-100],Ydieresisaacute:[-90,-90,-140,-140,-85,-92,-92,-100],Ydieresisabreve:[-90,-90,-70,-70,-85,-92,-92,-100],Ydieresisacircumflex:[-90,-90,-140,-140,-85,-92,-92,-100],Ydieresisadieresis:[-90,-90,-140,-140,-85,-92,-92,-60],Ydieresisagrave:[-90,-90,-140,-140,-85,-92,-92,-60],Ydieresisamacron:[-90,-90,-70,-70,-85,-92,-92,-60],Ydieresisaogonek:[-90,-90,-140,-140,-85,-92,-92,-100],Ydieresisaring:[-90,-90,-140,-140,-85,-92,-92,-100],Ydieresisatilde:[-90,-90,-70,-70,-85,-92,-92,-100],Ydieresiscolon:[-50,-50,-60,-60,-92,-92,-65,-92],Ydieresiscomma:[-100,-100,-140,-140,-92,-92,-92,-129],Ydieresise:[-80,-80,-140,-140,-111,-111,-92,-100],Ydieresiseacute:[-80,-80,-140,-140,-111,-111,-92,-100],Ydieresisecaron:[-80,-80,-140,-140,-111,-111,-92,-100],Ydieresisecircumflex:[-80,-80,-140,-140,-111,-71,-92,-100],Ydieresisedieresis:[-80,-80,-140,-140,-71,-71,-52,-60],Ydieresisedotaccent:[-80,-80,-140,-140,-111,-111,-92,-100],Ydieresisegrave:[-80,-80,-140,-140,-71,-71,-52,-60],Ydieresisemacron:[-80,-80,-70,-70,-71,-71,-52,-60],Ydieresiseogonek:[-80,-80,-140,-140,-111,-111,-92,-100],Ydieresiso:[-100,-100,-140,-140,-111,-111,-92,-110],Ydieresisoacute:[-100,-100,-140,-140,-111,-111,-92,-110],Ydieresisocircumflex:[-100,-100,-140,-140,-111,-111,-92,-110],Ydieresisodieresis:[-100,-100,-140,-140,-111,-111,-92,-70],Ydieresisograve:[-100,-100,-140,-140,-111,-111,-92,-70],Ydieresisohungarumlaut:[-100,-100,-140,-140,-111,-111,-92,-110],Ydieresisomacron:[-100,-100,-140,-140,-111,-111,-92,-70],Ydieresisoslash:[-100,-100,-140,-140,-111,-111,-92,-110],Ydieresisotilde:[-100,-100,-140,-140,-111,-111,-92,-70],Ydieresisperiod:[-100,-100,-140,-140,-92,-74,-92,-129],Ydieresissemicolon:[-50,-50,-60,-60,-92,-92,-65,-92],Ydieresisu:[-100,-100,-110,-110,-92,-92,-92,-111],Ydieresisuacute:[-100,-100,-110,-110,-92,-92,-92,-111],Ydieresisucircumflex:[-100,-100,-110,-110,-92,-92,-92,-111],Ydieresisudieresis:[-100,-100,-110,-110,-92,-92,-92,-71],Ydieresisugrave:[-100,-100,-110,-110,-92,-92,-92,-71],Ydieresisuhungarumlaut:[-100,-100,-110,-110,-92,-92,-92,-111],Ydieresisumacron:[-100,-100,-110,-110,-92,-92,-92,-71],Ydieresisuogonek:[-100,-100,-110,-110,-92,-92,-92,-111],Ydieresisuring:[-100,-100,-110,-110,-92,-92,-92,-111],ag:[-10,-10,0,0,0,0,-10],agbreve:[-10,-10,0,0,0,0,-10],agcommaaccent:[-10,-10,0,0,0,0,-10],av:[-15,-15,-20,-20,-25,0,0,-20],aw:[-15,-15,-20,-20,0,0,0,-15],ay:[-20,-20,-30,-30],ayacute:[-20,-20,-30,-30],aydieresis:[-20,-20,-30,-30],aacuteg:[-10,-10,0,0,0,0,-10],aacutegbreve:[-10,-10,0,0,0,0,-10],aacutegcommaaccent:[-10,-10,0,0,0,0,-10],aacutev:[-15,-15,-20,-20,-25,0,0,-20],aacutew:[-15,-15,-20,-20,0,0,0,-15],aacutey:[-20,-20,-30,-30],aacuteyacute:[-20,-20,-30,-30],aacuteydieresis:[-20,-20,-30,-30],abreveg:[-10,-10,0,0,0,0,-10],abrevegbreve:[-10,-10,0,0,0,0,-10],abrevegcommaaccent:[-10,-10,0,0,0,0,-10],abrevev:[-15,-15,-20,-20,-25,0,0,-20],abrevew:[-15,-15,-20,-20,0,0,0,-15],abrevey:[-20,-20,-30,-30],abreveyacute:[-20,-20,-30,-30],abreveydieresis:[-20,-20,-30,-30],acircumflexg:[-10,-10,0,0,0,0,-10],acircumflexgbreve:[-10,-10,0,0,0,0,-10],acircumflexgcommaaccent:[-10,-10,0,0,0,0,-10],acircumflexv:[-15,-15,-20,-20,-25,0,0,-20],acircumflexw:[-15,-15,-20,-20,0,0,0,-15],acircumflexy:[-20,-20,-30,-30],acircumflexyacute:[-20,-20,-30,-30],acircumflexydieresis:[-20,-20,-30,-30],adieresisg:[-10,-10,0,0,0,0,-10],adieresisgbreve:[-10,-10,0,0,0,0,-10],adieresisgcommaaccent:[-10,-10,0,0,0,0,-10],adieresisv:[-15,-15,-20,-20,-25,0,0,-20],adieresisw:[-15,-15,-20,-20,0,0,0,-15],adieresisy:[-20,-20,-30,-30],adieresisyacute:[-20,-20,-30,-30],adieresisydieresis:[-20,-20,-30,-30],agraveg:[-10,-10,0,0,0,0,-10],agravegbreve:[-10,-10,0,0,0,0,-10],agravegcommaaccent:[-10,-10,0,0,0,0,-10],agravev:[-15,-15,-20,-20,-25,0,0,-20],agravew:[-15,-15,-20,-20,0,0,0,-15],agravey:[-20,-20,-30,-30],agraveyacute:[-20,-20,-30,-30],agraveydieresis:[-20,-20,-30,-30],amacrong:[-10,-10,0,0,0,0,-10],amacrongbreve:[-10,-10,0,0,0,0,-10],amacrongcommaaccent:[-10,-10,0,0,0,0,-10],amacronv:[-15,-15,-20,-20,-25,0,0,-20],amacronw:[-15,-15,-20,-20,0,0,0,-15],amacrony:[-20,-20,-30,-30],amacronyacute:[-20,-20,-30,-30],amacronydieresis:[-20,-20,-30,-30],aogonekg:[-10,-10,0,0,0,0,-10],aogonekgbreve:[-10,-10,0,0,0,0,-10],aogonekgcommaaccent:[-10,-10,0,0,0,0,-10],aogonekv:[-15,-15,-20,-20,-25,0,0,-20],aogonekw:[-15,-15,-20,-20,0,0,0,-15],aogoneky:[-20,-20,-30,-30],aogonekyacute:[-20,-20,-30,-30],aogonekydieresis:[-20,-20,-30,-30],aringg:[-10,-10,0,0,0,0,-10],aringgbreve:[-10,-10,0,0,0,0,-10],aringgcommaaccent:[-10,-10,0,0,0,0,-10],aringv:[-15,-15,-20,-20,-25,0,0,-20],aringw:[-15,-15,-20,-20,0,0,0,-15],aringy:[-20,-20,-30,-30],aringyacute:[-20,-20,-30,-30],aringydieresis:[-20,-20,-30,-30],atildeg:[-10,-10,0,0,0,0,-10],atildegbreve:[-10,-10,0,0,0,0,-10],atildegcommaaccent:[-10,-10,0,0,0,0,-10],atildev:[-15,-15,-20,-20,-25,0,0,-20],atildew:[-15,-15,-20,-20,0,0,0,-15],atildey:[-20,-20,-30,-30],atildeyacute:[-20,-20,-30,-30],atildeydieresis:[-20,-20,-30,-30],bl:[-10,-10,-20,-20],blacute:[-10,-10,-20,-20],blcommaaccent:[-10,-10,-20,-20],blslash:[-10,-10,-20,-20],bu:[-20,-20,-20,-20,-20,-20,-20,-20],buacute:[-20,-20,-20,-20,-20,-20,-20,-20],bucircumflex:[-20,-20,-20,-20,-20,-20,-20,-20],budieresis:[-20,-20,-20,-20,-20,-20,-20,-20],bugrave:[-20,-20,-20,-20,-20,-20,-20,-20],buhungarumlaut:[-20,-20,-20,-20,-20,-20,-20,-20],bumacron:[-20,-20,-20,-20,-20,-20,-20,-20],buogonek:[-20,-20,-20,-20,-20,-20,-20,-20],buring:[-20,-20,-20,-20,-20,-20,-20,-20],bv:[-20,-20,-20,-20,-15,0,0,-15],by:[-20,-20,-20,-20],byacute:[-20,-20,-20,-20],bydieresis:[-20,-20,-20,-20],ch:[-10,-10,0,0,0,-10,-15],ck:[-20,-20,-20,-20,0,-10,-20],ckcommaaccent:[-20,-20,-20,-20,0,-10,-20],cl:[-20,-20],clacute:[-20,-20],clcommaaccent:[-20,-20],clslash:[-20,-20],cy:[-10,-10,0,0,0,0,0,-15],cyacute:[-10,-10,0,0,0,0,0,-15],cydieresis:[-10,-10,0,0,0,0,0,-15],cacuteh:[-10,-10,0,0,0,-10,-15],cacutek:[-20,-20,-20,-20,0,-10,-20],cacutekcommaaccent:[-20,-20,-20,-20,0,-10,-20],cacutel:[-20,-20],cacutelacute:[-20,-20],cacutelcommaaccent:[-20,-20],cacutelslash:[-20,-20],cacutey:[-10,-10,0,0,0,0,0,-15],cacuteyacute:[-10,-10,0,0,0,0,0,-15],cacuteydieresis:[-10,-10,0,0,0,0,0,-15],ccaronh:[-10,-10,0,0,0,-10,-15],ccaronk:[-20,-20,-20,-20,0,-10,-20],ccaronkcommaaccent:[-20,-20,-20,-20,0,-10,-20],ccaronl:[-20,-20],ccaronlacute:[-20,-20],ccaronlcommaaccent:[-20,-20],ccaronlslash:[-20,-20],ccarony:[-10,-10,0,0,0,0,0,-15],ccaronyacute:[-10,-10,0,0,0,0,0,-15],ccaronydieresis:[-10,-10,0,0,0,0,0,-15],ccedillah:[-10,-10,0,0,0,-10,-15],ccedillak:[-20,-20,-20,-20,0,-10,-20],ccedillakcommaaccent:[-20,-20,-20,-20,0,-10,-20],ccedillal:[-20,-20],ccedillalacute:[-20,-20],ccedillalcommaaccent:[-20,-20],ccedillalslash:[-20,-20],ccedillay:[-10,-10,0,0,0,0,0,-15],ccedillayacute:[-10,-10,0,0,0,0,0,-15],ccedillaydieresis:[-10,-10,0,0,0,0,0,-15],colonspace:[-40,-40,-50,-50],commaquotedblright:[-120,-120,-100,-100,-45,-95,-140,-70],commaquoteright:[-120,-120,-100,-100,-55,-95,-140,-70],commaspace:[-40,-40],dd:[-10,-10],ddcroat:[-10,-10],dv:[-15,-15],dw:[-15,-15,0,0,-15],dy:[-15,-15],dyacute:[-15,-15],dydieresis:[-15,-15],dcroatd:[-10,-10],dcroatdcroat:[-10,-10],dcroatv:[-15,-15],dcroatw:[-15,-15,0,0,-15],dcroaty:[-15,-15],dcroatyacute:[-15,-15],dcroatydieresis:[-15,-15],ecomma:[10,10,-15,-15,0,0,-10],eperiod:[20,20,-15,-15,0,0,-15],ev:[-15,-15,-30,-30,-15,0,-15,-25],ew:[-15,-15,-20,-20,0,0,-15,-25],ex:[-15,-15,-30,-30,0,0,-20,-15],ey:[-15,-15,-20,-20,0,0,-30,-15],eyacute:[-15,-15,-20,-20,0,0,-30,-15],eydieresis:[-15,-15,-20,-20,0,0,-30,-15],eacutecomma:[10,10,-15,-15,0,0,-10],eacuteperiod:[20,20,-15,-15,0,0,-15],eacutev:[-15,-15,-30,-30,-15,0,-15,-25],eacutew:[-15,-15,-20,-20,0,0,-15,-25],eacutex:[-15,-15,-30,-30,0,0,-20,-15],eacutey:[-15,-15,-20,-20,0,0,-30,-15],eacuteyacute:[-15,-15,-20,-20,0,0,-30,-15],eacuteydieresis:[-15,-15,-20,-20,0,0,-30,-15],ecaroncomma:[10,10,-15,-15,0,0,-10],ecaronperiod:[20,20,-15,-15,0,0,-15],ecaronv:[-15,-15,-30,-30,-15,0,-15,-25],ecaronw:[-15,-15,-20,-20,0,0,-15,-25],ecaronx:[-15,-15,-30,-30,0,0,-20,-15],ecarony:[-15,-15,-20,-20,0,0,-30,-15],ecaronyacute:[-15,-15,-20,-20,0,0,-30,-15],ecaronydieresis:[-15,-15,-20,-20,0,0,-30,-15],ecircumflexcomma:[10,10,-15,-15,0,0,-10],ecircumflexperiod:[20,20,-15,-15,0,0,-15],ecircumflexv:[-15,-15,-30,-30,-15,0,-15,-25],ecircumflexw:[-15,-15,-20,-20,0,0,-15,-25],ecircumflexx:[-15,-15,-30,-30,0,0,-20,-15],ecircumflexy:[-15,-15,-20,-20,0,0,-30,-15],ecircumflexyacute:[-15,-15,-20,-20,0,0,-30,-15],ecircumflexydieresis:[-15,-15,-20,-20,0,0,-30,-15],edieresiscomma:[10,10,-15,-15,0,0,-10],edieresisperiod:[20,20,-15,-15,0,0,-15],edieresisv:[-15,-15,-30,-30,-15,0,-15,-25],edieresisw:[-15,-15,-20,-20,0,0,-15,-25],edieresisx:[-15,-15,-30,-30,0,0,-20,-15],edieresisy:[-15,-15,-20,-20,0,0,-30,-15],edieresisyacute:[-15,-15,-20,-20,0,0,-30,-15],edieresisydieresis:[-15,-15,-20,-20,0,0,-30,-15],edotaccentcomma:[10,10,-15,-15,0,0,-10],edotaccentperiod:[20,20,-15,-15,0,0,-15],edotaccentv:[-15,-15,-30,-30,-15,0,-15,-25],edotaccentw:[-15,-15,-20,-20,0,0,-15,-25],edotaccentx:[-15,-15,-30,-30,0,0,-20,-15],edotaccenty:[-15,-15,-20,-20,0,0,-30,-15],edotaccentyacute:[-15,-15,-20,-20,0,0,-30,-15],edotaccentydieresis:[-15,-15,-20,-20,0,0,-30,-15],egravecomma:[10,10,-15,-15,0,0,-10],egraveperiod:[20,20,-15,-15,0,0,-15],egravev:[-15,-15,-30,-30,-15,0,-15,-25],egravew:[-15,-15,-20,-20,0,0,-15,-25],egravex:[-15,-15,-30,-30,0,0,-20,-15],egravey:[-15,-15,-20,-20,0,0,-30,-15],egraveyacute:[-15,-15,-20,-20,0,0,-30,-15],egraveydieresis:[-15,-15,-20,-20,0,0,-30,-15],emacroncomma:[10,10,-15,-15,0,0,-10],emacronperiod:[20,20,-15,-15,0,0,-15],emacronv:[-15,-15,-30,-30,-15,0,-15,-25],emacronw:[-15,-15,-20,-20,0,0,-15,-25],emacronx:[-15,-15,-30,-30,0,0,-20,-15],emacrony:[-15,-15,-20,-20,0,0,-30,-15],emacronyacute:[-15,-15,-20,-20,0,0,-30,-15],emacronydieresis:[-15,-15,-20,-20,0,0,-30,-15],eogonekcomma:[10,10,-15,-15,0,0,-10],eogonekperiod:[20,20,-15,-15,0,0,-15],eogonekv:[-15,-15,-30,-30,-15,0,-15,-25],eogonekw:[-15,-15,-20,-20,0,0,-15,-25],eogonekx:[-15,-15,-30,-30,0,0,-20,-15],eogoneky:[-15,-15,-20,-20,0,0,-30,-15],eogonekyacute:[-15,-15,-20,-20,0,0,-30,-15],eogonekydieresis:[-15,-15,-20,-20,0,0,-30,-15],fcomma:[-10,-10,-30,-30,-15,-10,-10],fe:[-10,-10,-30,-30,0,-10],feacute:[-10,-10,-30,-30,0,-10],fecaron:[-10,-10,-30,-30],fecircumflex:[-10,-10,-30,-30],fedieresis:[-10,-10,-30,-30],fedotaccent:[-10,-10,-30,-30,0,-10],fegrave:[-10,-10,-30,-30],femacron:[-10,-10,-30,-30],feogonek:[-10,-10,-30,-30,0,-10],fo:[-20,-20,-30,-30,-25,-10],foacute:[-20,-20,-30,-30,-25,-10],focircumflex:[-20,-20,-30,-30,-25,-10],fodieresis:[-20,-20,-30,-30,-25],fograve:[-20,-20,-30,-30,-25,-10],fohungarumlaut:[-20,-20,-30,-30,-25,-10],fomacron:[-20,-20,-30,-30,-25],foslash:[-20,-20,-30,-30,-25,-10],fotilde:[-20,-20,-30,-30,-25,-10],fperiod:[-10,-10,-30,-30,-15,-10,-15],fquotedblright:[30,30,60,60,50],fquoteright:[30,30,50,50,55,55,92,55],ge:[10,10,0,0,0,0,-10],geacute:[10,10,0,0,0,0,-10],gecaron:[10,10,0,0,0,0,-10],gecircumflex:[10,10,0,0,0,0,-10],gedieresis:[10,10,0,0,0,0,-10],gedotaccent:[10,10,0,0,0,0,-10],gegrave:[10,10,0,0,0,0,-10],gemacron:[10,10,0,0,0,0,-10],geogonek:[10,10,0,0,0,0,-10],gg:[-10,-10,0,0,0,0,-10],ggbreve:[-10,-10,0,0,0,0,-10],ggcommaaccent:[-10,-10,0,0,0,0,-10],gbrevee:[10,10,0,0,0,0,-10],gbreveeacute:[10,10,0,0,0,0,-10],gbreveecaron:[10,10,0,0,0,0,-10],gbreveecircumflex:[10,10,0,0,0,0,-10],gbreveedieresis:[10,10,0,0,0,0,-10],gbreveedotaccent:[10,10,0,0,0,0,-10],gbreveegrave:[10,10,0,0,0,0,-10],gbreveemacron:[10,10,0,0,0,0,-10],gbreveeogonek:[10,10,0,0,0,0,-10],gbreveg:[-10,-10,0,0,0,0,-10],gbrevegbreve:[-10,-10,0,0,0,0,-10],gbrevegcommaaccent:[-10,-10,0,0,0,0,-10],gcommaaccente:[10,10,0,0,0,0,-10],gcommaaccenteacute:[10,10,0,0,0,0,-10],gcommaaccentecaron:[10,10,0,0,0,0,-10],gcommaaccentecircumflex:[10,10,0,0,0,0,-10],gcommaaccentedieresis:[10,10,0,0,0,0,-10],gcommaaccentedotaccent:[10,10,0,0,0,0,-10],gcommaaccentegrave:[10,10,0,0,0,0,-10],gcommaaccentemacron:[10,10,0,0,0,0,-10],gcommaaccenteogonek:[10,10,0,0,0,0,-10],gcommaaccentg:[-10,-10,0,0,0,0,-10],gcommaaccentgbreve:[-10,-10,0,0,0,0,-10],gcommaaccentgcommaaccent:[-10,-10,0,0,0,0,-10],hy:[-20,-20,-30,-30,-15,0,0,-5],hyacute:[-20,-20,-30,-30,-15,0,0,-5],hydieresis:[-20,-20,-30,-30,-15,0,0,-5],ko:[-15,-15,-20,-20,-15,-10,-10,-10],koacute:[-15,-15,-20,-20,-15,-10,-10,-10],kocircumflex:[-15,-15,-20,-20,-15,-10,-10,-10],kodieresis:[-15,-15,-20,-20,-15,-10,-10,-10],kograve:[-15,-15,-20,-20,-15,-10,-10,-10],kohungarumlaut:[-15,-15,-20,-20,-15,-10,-10,-10],komacron:[-15,-15,-20,-20,-15,-10,-10,-10],koslash:[-15,-15,-20,-20,-15,-10,-10,-10],kotilde:[-15,-15,-20,-20,-15,-10,-10,-10],kcommaaccento:[-15,-15,-20,-20,-15,-10,-10,-10],kcommaaccentoacute:[-15,-15,-20,-20,-15,-10,-10,-10],kcommaaccentocircumflex:[-15,-15,-20,-20,-15,-10,-10,-10],kcommaaccentodieresis:[-15,-15,-20,-20,-15,-10,-10,-10],kcommaaccentograve:[-15,-15,-20,-20,-15,-10,-10,-10],kcommaaccentohungarumlaut:[-15,-15,-20,-20,-15,-10,-10,-10],kcommaaccentomacron:[-15,-15,-20,-20,-15,-10,-10,-10],kcommaaccentoslash:[-15,-15,-20,-20,-15,-10,-10,-10],kcommaaccentotilde:[-15,-15,-20,-20,-15,-10,-10,-10],lw:[-15,-15,0,0,0,0,0,-10],ly:[-15,-15],lyacute:[-15,-15],lydieresis:[-15,-15],lacutew:[-15,-15,0,0,0,0,0,-10],lacutey:[-15,-15],lacuteyacute:[-15,-15],lacuteydieresis:[-15,-15],lcommaaccentw:[-15,-15,0,0,0,0,0,-10],lcommaaccenty:[-15,-15],lcommaaccentyacute:[-15,-15],lcommaaccentydieresis:[-15,-15],lslashw:[-15,-15,0,0,0,0,0,-10],lslashy:[-15,-15],lslashyacute:[-15,-15],lslashydieresis:[-15,-15],mu:[-20,-20,-10,-10],muacute:[-20,-20,-10,-10],mucircumflex:[-20,-20,-10,-10],mudieresis:[-20,-20,-10,-10],mugrave:[-20,-20,-10,-10],muhungarumlaut:[-20,-20,-10,-10],mumacron:[-20,-20,-10,-10],muogonek:[-20,-20,-10,-10],muring:[-20,-20,-10,-10],my:[-30,-30,-15,-15],myacute:[-30,-30,-15,-15],mydieresis:[-30,-30,-15,-15],nu:[-10,-10,-10,-10],nuacute:[-10,-10,-10,-10],nucircumflex:[-10,-10,-10,-10],nudieresis:[-10,-10,-10,-10],nugrave:[-10,-10,-10,-10],nuhungarumlaut:[-10,-10,-10,-10],numacron:[-10,-10,-10,-10],nuogonek:[-10,-10,-10,-10],nuring:[-10,-10,-10,-10],nv:[-40,-40,-20,-20,-40,-40,-40,-40],ny:[-20,-20,-15,-15,0,0,0,-15],nyacute:[-20,-20,-15,-15,0,0,0,-15],nydieresis:[-20,-20,-15,-15,0,0,0,-15],nacuteu:[-10,-10,-10,-10],nacuteuacute:[-10,-10,-10,-10],nacuteucircumflex:[-10,-10,-10,-10],nacuteudieresis:[-10,-10,-10,-10],nacuteugrave:[-10,-10,-10,-10],nacuteuhungarumlaut:[-10,-10,-10,-10],nacuteumacron:[-10,-10,-10,-10],nacuteuogonek:[-10,-10,-10,-10],nacuteuring:[-10,-10,-10,-10],nacutev:[-40,-40,-20,-20,-40,-40,-40,-40],nacutey:[-20,-20,-15,-15,0,0,0,-15],nacuteyacute:[-20,-20,-15,-15,0,0,0,-15],nacuteydieresis:[-20,-20,-15,-15,0,0,0,-15],ncaronu:[-10,-10,-10,-10],ncaronuacute:[-10,-10,-10,-10],ncaronucircumflex:[-10,-10,-10,-10],ncaronudieresis:[-10,-10,-10,-10],ncaronugrave:[-10,-10,-10,-10],ncaronuhungarumlaut:[-10,-10,-10,-10],ncaronumacron:[-10,-10,-10,-10],ncaronuogonek:[-10,-10,-10,-10],ncaronuring:[-10,-10,-10,-10],ncaronv:[-40,-40,-20,-20,-40,-40,-40,-40],ncarony:[-20,-20,-15,-15,0,0,0,-15],ncaronyacute:[-20,-20,-15,-15,0,0,0,-15],ncaronydieresis:[-20,-20,-15,-15,0,0,0,-15],ncommaaccentu:[-10,-10,-10,-10],ncommaaccentuacute:[-10,-10,-10,-10],ncommaaccentucircumflex:[-10,-10,-10,-10],ncommaaccentudieresis:[-10,-10,-10,-10],ncommaaccentugrave:[-10,-10,-10,-10],ncommaaccentuhungarumlaut:[-10,-10,-10,-10],ncommaaccentumacron:[-10,-10,-10,-10],ncommaaccentuogonek:[-10,-10,-10,-10],ncommaaccenturing:[-10,-10,-10,-10],ncommaaccentv:[-40,-40,-20,-20,-40,-40,-40,-40],ncommaaccenty:[-20,-20,-15,-15,0,0,0,-15],ncommaaccentyacute:[-20,-20,-15,-15,0,0,0,-15],ncommaaccentydieresis:[-20,-20,-15,-15,0,0,0,-15],ntildeu:[-10,-10,-10,-10],ntildeuacute:[-10,-10,-10,-10],ntildeucircumflex:[-10,-10,-10,-10],ntildeudieresis:[-10,-10,-10,-10],ntildeugrave:[-10,-10,-10,-10],ntildeuhungarumlaut:[-10,-10,-10,-10],ntildeumacron:[-10,-10,-10,-10],ntildeuogonek:[-10,-10,-10,-10],ntildeuring:[-10,-10,-10,-10],ntildev:[-40,-40,-20,-20,-40,-40,-40,-40],ntildey:[-20,-20,-15,-15,0,0,0,-15],ntildeyacute:[-20,-20,-15,-15,0,0,0,-15],ntildeydieresis:[-20,-20,-15,-15,0,0,0,-15],ov:[-20,-20,-15,-15,-10,-15,-10,-15],ow:[-15,-15,-15,-15,-10,-25,0,-25],ox:[-30,-30,-30,-30,0,-10],oy:[-20,-20,-30,-30,0,-10,0,-10],oyacute:[-20,-20,-30,-30,0,-10,0,-10],oydieresis:[-20,-20,-30,-30,0,-10,0,-10],oacutev:[-20,-20,-15,-15,-10,-15,-10,-15],oacutew:[-15,-15,-15,-15,-10,-25,0,-25],oacutex:[-30,-30,-30,-30,0,-10],oacutey:[-20,-20,-30,-30,0,-10,0,-10],oacuteyacute:[-20,-20,-30,-30,0,-10,0,-10],oacuteydieresis:[-20,-20,-30,-30,0,-10,0,-10],ocircumflexv:[-20,-20,-15,-15,-10,-15,-10,-15],ocircumflexw:[-15,-15,-15,-15,-10,-25,0,-25],ocircumflexx:[-30,-30,-30,-30,0,-10],ocircumflexy:[-20,-20,-30,-30,0,-10,0,-10],ocircumflexyacute:[-20,-20,-30,-30,0,-10,0,-10],ocircumflexydieresis:[-20,-20,-30,-30,0,-10,0,-10],odieresisv:[-20,-20,-15,-15,-10,-15,-10,-15],odieresisw:[-15,-15,-15,-15,-10,-25,0,-25],odieresisx:[-30,-30,-30,-30,0,-10],odieresisy:[-20,-20,-30,-30,0,-10,0,-10],odieresisyacute:[-20,-20,-30,-30,0,-10,0,-10],odieresisydieresis:[-20,-20,-30,-30,0,-10,0,-10],ogravev:[-20,-20,-15,-15,-10,-15,-10,-15],ogravew:[-15,-15,-15,-15,-10,-25,0,-25],ogravex:[-30,-30,-30,-30,0,-10],ogravey:[-20,-20,-30,-30,0,-10,0,-10],ograveyacute:[-20,-20,-30,-30,0,-10,0,-10],ograveydieresis:[-20,-20,-30,-30,0,-10,0,-10],ohungarumlautv:[-20,-20,-15,-15,-10,-15,-10,-15],ohungarumlautw:[-15,-15,-15,-15,-10,-25,0,-25],ohungarumlautx:[-30,-30,-30,-30,0,-10],ohungarumlauty:[-20,-20,-30,-30,0,-10,0,-10],ohungarumlautyacute:[-20,-20,-30,-30,0,-10,0,-10],ohungarumlautydieresis:[-20,-20,-30,-30,0,-10,0,-10],omacronv:[-20,-20,-15,-15,-10,-15,-10,-15],omacronw:[-15,-15,-15,-15,-10,-25,0,-25],omacronx:[-30,-30,-30,-30,0,-10],omacrony:[-20,-20,-30,-30,0,-10,0,-10],omacronyacute:[-20,-20,-30,-30,0,-10,0,-10],omacronydieresis:[-20,-20,-30,-30,0,-10,0,-10],oslashv:[-20,-20,-70,-70,-10,-15,-10,-15],oslashw:[-15,-15,-70,-70,-10,-25,0,-25],oslashx:[-30,-30,-85,-85,0,-10],oslashy:[-20,-20,-70,-70,0,-10,0,-10],oslashyacute:[-20,-20,-70,-70,0,-10,0,-10],oslashydieresis:[-20,-20,-70,-70,0,-10,0,-10],otildev:[-20,-20,-15,-15,-10,-15,-10,-15],otildew:[-15,-15,-15,-15,-10,-25,0,-25],otildex:[-30,-30,-30,-30,0,-10],otildey:[-20,-20,-30,-30,0,-10,0,-10],otildeyacute:[-20,-20,-30,-30,0,-10,0,-10],otildeydieresis:[-20,-20,-30,-30,0,-10,0,-10],py:[-15,-15,-30,-30,0,0,0,-10],pyacute:[-15,-15,-30,-30,0,0,0,-10],pydieresis:[-15,-15,-30,-30,0,0,0,-10],periodquotedblright:[-120,-120,-100,-100,-55,-95,-140,-70],periodquoteright:[-120,-120,-100,-100,-55,-95,-140,-70],periodspace:[-40,-40,-60,-60],quotedblrightspace:[-80,-80,-40,-40],quoteleftquoteleft:[-46,-46,-57,-57,-63,-74,-111,-74],quoterightd:[-80,-80,-50,-50,-20,-15,-25,-50],quoterightdcroat:[-80,-80,-50,-50,-20,-15,-25,-50],quoterightl:[-20,-20,0,0,0,0,0,-10],quoterightlacute:[-20,-20,0,0,0,0,0,-10],quoterightlcommaaccent:[-20,-20,0,0,0,0,0,-10],quoterightlslash:[-20,-20,0,0,0,0,0,-10],quoterightquoteright:[-46,-46,-57,-57,-63,-74,-111,-74],quoterightr:[-40,-40,-50,-50,-20,-15,-25,-50],quoterightracute:[-40,-40,-50,-50,-20,-15,-25,-50],quoterightrcaron:[-40,-40,-50,-50,-20,-15,-25,-50],quoterightrcommaaccent:[-40,-40,-50,-50,-20,-15,-25,-50],quoterights:[-60,-60,-50,-50,-37,-74,-40,-55],quoterightsacute:[-60,-60,-50,-50,-37,-74,-40,-55],quoterightscaron:[-60,-60,-50,-50,-37,-74,-40,-55],quoterightscedilla:[-60,-60,-50,-50,-37,-74,-40,-55],quoterightscommaaccent:[-60,-60,-50,-50,-37,-74,-40,-55],quoterightspace:[-80,-80,-70,-70,-74,-74,-111,-74],quoterightv:[-20,-20,0,0,-20,-15,-10,-50],rc:[-20,-20,0,0,-18,0,-37],rcacute:[-20,-20,0,0,-18,0,-37],rccaron:[-20,-20,0,0,-18,0,-37],rccedilla:[-20,-20,0,0,-18,0,-37],rcomma:[-60,-60,-50,-50,-92,-65,-111,-40],rd:[-20,-20,0,0,0,0,-37],rdcroat:[-20,-20,0,0,0,0,-37],rg:[-15,-15,0,0,-10,0,-37,-18],rgbreve:[-15,-15,0,0,-10,0,-37,-18],rgcommaaccent:[-15,-15,0,0,-10,0,-37,-18],rhyphen:[-20,-20,0,0,-37,0,-20,-20],ro:[-20,-20,0,0,-18,0,-45],roacute:[-20,-20,0,0,-18,0,-45],rocircumflex:[-20,-20,0,0,-18,0,-45],rodieresis:[-20,-20,0,0,-18,0,-45],rograve:[-20,-20,0,0,-18,0,-45],rohungarumlaut:[-20,-20,0,0,-18,0,-45],romacron:[-20,-20,0,0,-18,0,-45],roslash:[-20,-20,0,0,-18,0,-45],rotilde:[-20,-20,0,0,-18,0,-45],rperiod:[-60,-60,-50,-50,-100,-65,-111,-55],rq:[-20,-20,0,0,-18,0,-37],rs:[-15,-15,0,0,0,0,-10],rsacute:[-15,-15,0,0,0,0,-10],rscaron:[-15,-15,0,0,0,0,-10],rscedilla:[-15,-15,0,0,0,0,-10],rscommaaccent:[-15,-15,0,0,0,0,-10],rt:[20,20,40,40],rtcommaaccent:[20,20,40,40],rv:[10,10,30,30,-10],ry:[10,10,30,30],ryacute:[10,10,30,30],rydieresis:[10,10,30,30],racutec:[-20,-20,0,0,-18,0,-37],racutecacute:[-20,-20,0,0,-18,0,-37],racuteccaron:[-20,-20,0,0,-18,0,-37],racuteccedilla:[-20,-20,0,0,-18,0,-37],racutecomma:[-60,-60,-50,-50,-92,-65,-111,-40],racuted:[-20,-20,0,0,0,0,-37],racutedcroat:[-20,-20,0,0,0,0,-37],racuteg:[-15,-15,0,0,-10,0,-37,-18],racutegbreve:[-15,-15,0,0,-10,0,-37,-18],racutegcommaaccent:[-15,-15,0,0,-10,0,-37,-18],racutehyphen:[-20,-20,0,0,-37,0,-20,-20],racuteo:[-20,-20,0,0,-18,0,-45],racuteoacute:[-20,-20,0,0,-18,0,-45],racuteocircumflex:[-20,-20,0,0,-18,0,-45],racuteodieresis:[-20,-20,0,0,-18,0,-45],racuteograve:[-20,-20,0,0,-18,0,-45],racuteohungarumlaut:[-20,-20,0,0,-18,0,-45],racuteomacron:[-20,-20,0,0,-18,0,-45],racuteoslash:[-20,-20,0,0,-18,0,-45],racuteotilde:[-20,-20,0,0,-18,0,-45],racuteperiod:[-60,-60,-50,-50,-100,-65,-111,-55],racuteq:[-20,-20,0,0,-18,0,-37],racutes:[-15,-15,0,0,0,0,-10],racutesacute:[-15,-15,0,0,0,0,-10],racutescaron:[-15,-15,0,0,0,0,-10],racutescedilla:[-15,-15,0,0,0,0,-10],racutescommaaccent:[-15,-15,0,0,0,0,-10],racutet:[20,20,40,40],racutetcommaaccent:[20,20,40,40],racutev:[10,10,30,30,-10],racutey:[10,10,30,30],racuteyacute:[10,10,30,30],racuteydieresis:[10,10,30,30],rcaronc:[-20,-20,0,0,-18,0,-37],rcaroncacute:[-20,-20,0,0,-18,0,-37],rcaronccaron:[-20,-20,0,0,-18,0,-37],rcaronccedilla:[-20,-20,0,0,-18,0,-37],rcaroncomma:[-60,-60,-50,-50,-92,-65,-111,-40],rcarond:[-20,-20,0,0,0,0,-37],rcarondcroat:[-20,-20,0,0,0,0,-37],rcarong:[-15,-15,0,0,-10,0,-37,-18],rcarongbreve:[-15,-15,0,0,-10,0,-37,-18],rcarongcommaaccent:[-15,-15,0,0,-10,0,-37,-18],rcaronhyphen:[-20,-20,0,0,-37,0,-20,-20],rcarono:[-20,-20,0,0,-18,0,-45],rcaronoacute:[-20,-20,0,0,-18,0,-45],rcaronocircumflex:[-20,-20,0,0,-18,0,-45],rcaronodieresis:[-20,-20,0,0,-18,0,-45],rcaronograve:[-20,-20,0,0,-18,0,-45],rcaronohungarumlaut:[-20,-20,0,0,-18,0,-45],rcaronomacron:[-20,-20,0,0,-18,0,-45],rcaronoslash:[-20,-20,0,0,-18,0,-45],rcaronotilde:[-20,-20,0,0,-18,0,-45],rcaronperiod:[-60,-60,-50,-50,-100,-65,-111,-55],rcaronq:[-20,-20,0,0,-18,0,-37],rcarons:[-15,-15,0,0,0,0,-10],rcaronsacute:[-15,-15,0,0,0,0,-10],rcaronscaron:[-15,-15,0,0,0,0,-10],rcaronscedilla:[-15,-15,0,0,0,0,-10],rcaronscommaaccent:[-15,-15,0,0,0,0,-10],rcaront:[20,20,40,40],rcarontcommaaccent:[20,20,40,40],rcaronv:[10,10,30,30,-10],rcarony:[10,10,30,30],rcaronyacute:[10,10,30,30],rcaronydieresis:[10,10,30,30],rcommaaccentc:[-20,-20,0,0,-18,0,-37],rcommaaccentcacute:[-20,-20,0,0,-18,0,-37],rcommaaccentccaron:[-20,-20,0,0,-18,0,-37],rcommaaccentccedilla:[-20,-20,0,0,-18,0,-37],rcommaaccentcomma:[-60,-60,-50,-50,-92,-65,-111,-40],rcommaaccentd:[-20,-20,0,0,0,0,-37],rcommaaccentdcroat:[-20,-20,0,0,0,0,-37],rcommaaccentg:[-15,-15,0,0,-10,0,-37,-18],rcommaaccentgbreve:[-15,-15,0,0,-10,0,-37,-18],rcommaaccentgcommaaccent:[-15,-15,0,0,-10,0,-37,-18],rcommaaccenthyphen:[-20,-20,0,0,-37,0,-20,-20],rcommaaccento:[-20,-20,0,0,-18,0,-45],rcommaaccentoacute:[-20,-20,0,0,-18,0,-45],rcommaaccentocircumflex:[-20,-20,0,0,-18,0,-45],rcommaaccentodieresis:[-20,-20,0,0,-18,0,-45],rcommaaccentograve:[-20,-20,0,0,-18,0,-45],rcommaaccentohungarumlaut:[-20,-20,0,0,-18,0,-45],rcommaaccentomacron:[-20,-20,0,0,-18,0,-45],rcommaaccentoslash:[-20,-20,0,0,-18,0,-45],rcommaaccentotilde:[-20,-20,0,0,-18,0,-45],rcommaaccentperiod:[-60,-60,-50,-50,-100,-65,-111,-55],rcommaaccentq:[-20,-20,0,0,-18,0,-37],rcommaaccents:[-15,-15,0,0,0,0,-10],rcommaaccentsacute:[-15,-15,0,0,0,0,-10],rcommaaccentscaron:[-15,-15,0,0,0,0,-10],rcommaaccentscedilla:[-15,-15,0,0,0,0,-10],rcommaaccentscommaaccent:[-15,-15,0,0,0,0,-10],rcommaaccentt:[20,20,40,40],rcommaaccenttcommaaccent:[20,20,40,40],rcommaaccentv:[10,10,30,30,-10],rcommaaccenty:[10,10,30,30],rcommaaccentyacute:[10,10,30,30],rcommaaccentydieresis:[10,10,30,30],sw:[-15,-15,-30,-30],sacutew:[-15,-15,-30,-30],scaronw:[-15,-15,-30,-30],scedillaw:[-15,-15,-30,-30],scommaaccentw:[-15,-15,-30,-30],semicolonspace:[-40,-40,-50,-50],spaceT:[-100,-100,-50,-50,-30,0,-18,-18],spaceTcaron:[-100,-100,-50,-50,-30,0,-18,-18],spaceTcommaaccent:[-100,-100,-50,-50,-30,0,-18,-18],spaceV:[-80,-80,-50,-50,-45,-70,-35,-50],spaceW:[-80,-80,-40,-40,-30,-70,-40,-30],spaceY:[-120,-120,-90,-90,-55,-70,-75,-90],spaceYacute:[-120,-120,-90,-90,-55,-70,-75,-90],spaceYdieresis:[-120,-120,-90,-90,-55,-70,-75,-90],spacequotedblleft:[-80,-80,-30,-30],spacequoteleft:[-60,-60,-60,-60],va:[-20,-20,-25,-25,-10,0,0,-25],vaacute:[-20,-20,-25,-25,-10,0,0,-25],vabreve:[-20,-20,-25,-25,-10,0,0,-25],vacircumflex:[-20,-20,-25,-25,-10,0,0,-25],vadieresis:[-20,-20,-25,-25,-10,0,0,-25],vagrave:[-20,-20,-25,-25,-10,0,0,-25],vamacron:[-20,-20,-25,-25,-10,0,0,-25],vaogonek:[-20,-20,-25,-25,-10,0,0,-25],varing:[-20,-20,-25,-25,-10,0,0,-25],vatilde:[-20,-20,-25,-25,-10,0,0,-25],vcomma:[-80,-80,-80,-80,-55,-37,-74,-65],vo:[-30,-30,-25,-25,-10,-15,0,-20],voacute:[-30,-30,-25,-25,-10,-15,0,-20],vocircumflex:[-30,-30,-25,-25,-10,-15,0,-20],vodieresis:[-30,-30,-25,-25,-10,-15,0,-20],vograve:[-30,-30,-25,-25,-10,-15,0,-20],vohungarumlaut:[-30,-30,-25,-25,-10,-15,0,-20],vomacron:[-30,-30,-25,-25,-10,-15,0,-20],voslash:[-30,-30,-25,-25,-10,-15,0,-20],votilde:[-30,-30,-25,-25,-10,-15,0,-20],vperiod:[-80,-80,-80,-80,-70,-37,-74,-65],wcomma:[-40,-40,-60,-60,-55,-37,-74,-65],wo:[-20,-20,-10,-10,-10,-15,0,-10],woacute:[-20,-20,-10,-10,-10,-15,0,-10],wocircumflex:[-20,-20,-10,-10,-10,-15,0,-10],wodieresis:[-20,-20,-10,-10,-10,-15,0,-10],wograve:[-20,-20,-10,-10,-10,-15,0,-10],wohungarumlaut:[-20,-20,-10,-10,-10,-15,0,-10],womacron:[-20,-20,-10,-10,-10,-15,0,-10],woslash:[-20,-20,-10,-10,-10,-15,0,-10],wotilde:[-20,-20,-10,-10,-10,-15,0,-10],wperiod:[-40,-40,-60,-60,-70,-37,-74,-65],xe:[-10,-10,-30,-30,0,-10,0,-15],xeacute:[-10,-10,-30,-30,0,-10,0,-15],xecaron:[-10,-10,-30,-30,0,-10,0,-15],xecircumflex:[-10,-10,-30,-30,0,-10,0,-15],xedieresis:[-10,-10,-30,-30,0,-10,0,-15],xedotaccent:[-10,-10,-30,-30,0,-10,0,-15],xegrave:[-10,-10,-30,-30,0,-10,0,-15],xemacron:[-10,-10,-30,-30,0,-10,0,-15],xeogonek:[-10,-10,-30,-30,0,-10,0,-15],ya:[-30,-30,-20,-20],yaacute:[-30,-30,-20,-20],yabreve:[-30,-30,-20,-20],yacircumflex:[-30,-30,-20,-20],yadieresis:[-30,-30,-20,-20],yagrave:[-30,-30,-20,-20],yamacron:[-30,-30,-20,-20],yaogonek:[-30,-30,-20,-20],yaring:[-30,-30,-20,-20],yatilde:[-30,-30,-20,-20],ycomma:[-80,-80,-100,-100,-55,-37,-55,-65],ye:[-10,-10,-20,-20,-10],yeacute:[-10,-10,-20,-20,-10],yecaron:[-10,-10,-20,-20,-10],yecircumflex:[-10,-10,-20,-20,-10],yedieresis:[-10,-10,-20,-20,-10],yedotaccent:[-10,-10,-20,-20,-10],yegrave:[-10,-10,-20,-20,-10],yemacron:[-10,-10,-20,-20,-10],yeogonek:[-10,-10,-20,-20,-10],yo:[-25,-25,-20,-20,-25],yoacute:[-25,-25,-20,-20,-25],yocircumflex:[-25,-25,-20,-20,-25],yodieresis:[-25,-25,-20,-20,-25],yograve:[-25,-25,-20,-20,-25],yohungarumlaut:[-25,-25,-20,-20,-25],yomacron:[-25,-25,-20,-20,-25],yoslash:[-25,-25,-20,-20,-25],yotilde:[-25,-25,-20,-20,-25],yperiod:[-80,-80,-100,-100,-70,-37,-55,-65],yacutea:[-30,-30,-20,-20],yacuteaacute:[-30,-30,-20,-20],yacuteabreve:[-30,-30,-20,-20],yacuteacircumflex:[-30,-30,-20,-20],yacuteadieresis:[-30,-30,-20,-20],yacuteagrave:[-30,-30,-20,-20],yacuteamacron:[-30,-30,-20,-20],yacuteaogonek:[-30,-30,-20,-20],yacutearing:[-30,-30,-20,-20],yacuteatilde:[-30,-30,-20,-20],yacutecomma:[-80,-80,-100,-100,-55,-37,-55,-65],yacutee:[-10,-10,-20,-20,-10],yacuteeacute:[-10,-10,-20,-20,-10],yacuteecaron:[-10,-10,-20,-20,-10],yacuteecircumflex:[-10,-10,-20,-20,-10],yacuteedieresis:[-10,-10,-20,-20,-10],yacuteedotaccent:[-10,-10,-20,-20,-10],yacuteegrave:[-10,-10,-20,-20,-10],yacuteemacron:[-10,-10,-20,-20,-10],yacuteeogonek:[-10,-10,-20,-20,-10],yacuteo:[-25,-25,-20,-20,-25],yacuteoacute:[-25,-25,-20,-20,-25],yacuteocircumflex:[-25,-25,-20,-20,-25],yacuteodieresis:[-25,-25,-20,-20,-25],yacuteograve:[-25,-25,-20,-20,-25],yacuteohungarumlaut:[-25,-25,-20,-20,-25],yacuteomacron:[-25,-25,-20,-20,-25],yacuteoslash:[-25,-25,-20,-20,-25],yacuteotilde:[-25,-25,-20,-20,-25],yacuteperiod:[-80,-80,-100,-100,-70,-37,-55,-65],ydieresisa:[-30,-30,-20,-20],ydieresisaacute:[-30,-30,-20,-20],ydieresisabreve:[-30,-30,-20,-20],ydieresisacircumflex:[-30,-30,-20,-20],ydieresisadieresis:[-30,-30,-20,-20],ydieresisagrave:[-30,-30,-20,-20],ydieresisamacron:[-30,-30,-20,-20],ydieresisaogonek:[-30,-30,-20,-20],ydieresisaring:[-30,-30,-20,-20],ydieresisatilde:[-30,-30,-20,-20],ydieresiscomma:[-80,-80,-100,-100,-55,-37,-55,-65],ydieresise:[-10,-10,-20,-20,-10],ydieresiseacute:[-10,-10,-20,-20,-10],ydieresisecaron:[-10,-10,-20,-20,-10],ydieresisecircumflex:[-10,-10,-20,-20,-10],ydieresisedieresis:[-10,-10,-20,-20,-10],ydieresisedotaccent:[-10,-10,-20,-20,-10],ydieresisegrave:[-10,-10,-20,-20,-10],ydieresisemacron:[-10,-10,-20,-20,-10],ydieresiseogonek:[-10,-10,-20,-20,-10],ydieresiso:[-25,-25,-20,-20,-25],ydieresisoacute:[-25,-25,-20,-20,-25],ydieresisocircumflex:[-25,-25,-20,-20,-25],ydieresisodieresis:[-25,-25,-20,-20,-25],ydieresisograve:[-25,-25,-20,-20,-25],ydieresisohungarumlaut:[-25,-25,-20,-20,-25],ydieresisomacron:[-25,-25,-20,-20,-25],ydieresisoslash:[-25,-25,-20,-20,-25],ydieresisotilde:[-25,-25,-20,-20,-25],ydieresisperiod:[-80,-80,-100,-100,-70,-37,-55,-65],ze:[10,10,-15,-15],zeacute:[10,10,-15,-15],zecaron:[10,10,-15,-15],zecircumflex:[10,10,-15,-15],zedieresis:[10,10,-15,-15],zedotaccent:[10,10,-15,-15],zegrave:[10,10,-15,-15],zemacron:[10,10,-15,-15],zeogonek:[10,10,-15,-15],zacutee:[10,10,-15,-15],zacuteeacute:[10,10,-15,-15],zacuteecaron:[10,10,-15,-15],zacuteecircumflex:[10,10,-15,-15],zacuteedieresis:[10,10,-15,-15],zacuteedotaccent:[10,10,-15,-15],zacuteegrave:[10,10,-15,-15],zacuteemacron:[10,10,-15,-15],zacuteeogonek:[10,10,-15,-15],zcarone:[10,10,-15,-15],zcaroneacute:[10,10,-15,-15],zcaronecaron:[10,10,-15,-15],zcaronecircumflex:[10,10,-15,-15],zcaronedieresis:[10,10,-15,-15],zcaronedotaccent:[10,10,-15,-15],zcaronegrave:[10,10,-15,-15],zcaronemacron:[10,10,-15,-15],zcaroneogonek:[10,10,-15,-15],zdotaccente:[10,10,-15,-15],zdotaccenteacute:[10,10,-15,-15],zdotaccentecaron:[10,10,-15,-15],zdotaccentecircumflex:[10,10,-15,-15],zdotaccentedieresis:[10,10,-15,-15],zdotaccentedotaccent:[10,10,-15,-15],zdotaccentegrave:[10,10,-15,-15],zdotaccentemacron:[10,10,-15,-15],zdotaccenteogonek:[10,10,-15,-15],Bcomma:[0,0,-20,-20],Bperiod:[0,0,-20,-20],Ccomma:[0,0,-30,-30],Cperiod:[0,0,-30,-30],Cacutecomma:[0,0,-30,-30],Cacuteperiod:[0,0,-30,-30],Ccaroncomma:[0,0,-30,-30],Ccaronperiod:[0,0,-30,-30],Ccedillacomma:[0,0,-30,-30],Ccedillaperiod:[0,0,-30,-30],Fe:[0,0,-30,-30,-25,-100,-75],Feacute:[0,0,-30,-30,-25,-100,-75],Fecaron:[0,0,-30,-30,-25,-100,-75],Fecircumflex:[0,0,-30,-30,-25,-100,-75],Fedieresis:[0,0,-30,-30,-25,-100,-75],Fedotaccent:[0,0,-30,-30,-25,-100,-75],Fegrave:[0,0,-30,-30,-25,-100,-75],Femacron:[0,0,-30,-30,-25,-100,-75],Feogonek:[0,0,-30,-30,-25,-100,-75],Fo:[0,0,-30,-30,-25,-70,-105,-15],Foacute:[0,0,-30,-30,-25,-70,-105,-15],Focircumflex:[0,0,-30,-30,-25,-70,-105,-15],Fodieresis:[0,0,-30,-30,-25,-70,-105,-15],Fograve:[0,0,-30,-30,-25,-70,-105,-15],Fohungarumlaut:[0,0,-30,-30,-25,-70,-105,-15],Fomacron:[0,0,-30,-30,-25,-70,-105,-15],Foslash:[0,0,-30,-30,-25,-70,-105,-15],Fotilde:[0,0,-30,-30,-25,-70,-105,-15],Fr:[0,0,-45,-45,0,-50,-55],Fracute:[0,0,-45,-45,0,-50,-55],Frcaron:[0,0,-45,-45,0,-50,-55],Frcommaaccent:[0,0,-45,-45,0,-50,-55],Ja:[0,0,-20,-20,-15,-40,-35],Jaacute:[0,0,-20,-20,-15,-40,-35],Jabreve:[0,0,-20,-20,-15,-40,-35],Jacircumflex:[0,0,-20,-20,-15,-40,-35],Jadieresis:[0,0,-20,-20,-15,-40,-35],Jagrave:[0,0,-20,-20,-15,-40,-35],Jamacron:[0,0,-20,-20,-15,-40,-35],Jaogonek:[0,0,-20,-20,-15,-40,-35],Jaring:[0,0,-20,-20,-15,-40,-35],Jatilde:[0,0,-20,-20,-15,-40,-35],LcaronT:[0,0,-110,-110],LcaronTcaron:[0,0,-110,-110],LcaronTcommaaccent:[0,0,-110,-110],LcaronV:[0,0,-110,-110],LcaronW:[0,0,-70,-70],LcaronY:[0,0,-140,-140],LcaronYacute:[0,0,-140,-140],LcaronYdieresis:[0,0,-140,-140],Lcaronquotedblright:[0,0,-140,-140],Lcaronquoteright:[0,0,-160,-160,0,0,0,-92],Lcarony:[0,0,-30,-30,0,0,0,-55],Lcaronyacute:[0,0,-30,-30,0,0,0,-55],Lcaronydieresis:[0,0,-30,-30,0,0,0,-55],Scomma:[0,0,-20,-20],Speriod:[0,0,-20,-20],Sacutecomma:[0,0,-20,-20],Sacuteperiod:[0,0,-20,-20],Scaroncomma:[0,0,-20,-20],Scaronperiod:[0,0,-20,-20],Scedillacomma:[0,0,-20,-20],Scedillaperiod:[0,0,-20,-20],Scommaaccentcomma:[0,0,-20,-20],Scommaaccentperiod:[0,0,-20,-20],Trcaron:[0,0,-120,-120,-74,-37,-55,-35],Tcaronrcaron:[0,0,-120,-120,-74,-37,-55,-35],Tcommaaccentrcaron:[0,0,-120,-120,-74,-37,-55,-35],Yhyphen:[0,0,-140,-140,-92,-92,-74,-111],Yi:[0,0,-20,-20,-37,-55,-74,-55],Yiacute:[0,0,-20,-20,-37,-55,-74,-55],Yiogonek:[0,0,-20,-20,-37,-55,-74,-55],Yacutehyphen:[0,0,-140,-140,-92,-92,-74,-111],Yacutei:[0,0,-20,-20,-37,-55,-74,-55],Yacuteiacute:[0,0,-20,-20,-37,-55,-74,-55],Yacuteiogonek:[0,0,-20,-20,-37,-55,-74,-55],Ydieresishyphen:[0,0,-140,-140,-92,-92,-74,-111],Ydieresisi:[0,0,-20,-20,-37,-55,-74,-55],Ydieresisiacute:[0,0,-20,-20,-37,-55,-74,-55],Ydieresisiogonek:[0,0,-20,-20,-37,-55,-74,-55],bb:[0,0,-10,-10,-10,-10],bcomma:[0,0,-40,-40],bperiod:[0,0,-40,-40,-40,-40,-40,-40],ccomma:[0,0,-15,-15],cacutecomma:[0,0,-15,-15],ccaroncomma:[0,0,-15,-15],ccedillacomma:[0,0,-15,-15],fa:[0,0,-30,-30,0,0,0,-10],faacute:[0,0,-30,-30,0,0,0,-10],fabreve:[0,0,-30,-30,0,0,0,-10],facircumflex:[0,0,-30,-30,0,0,0,-10],fadieresis:[0,0,-30,-30,0,0,0,-10],fagrave:[0,0,-30,-30,0,0,0,-10],famacron:[0,0,-30,-30,0,0,0,-10],faogonek:[0,0,-30,-30,0,0,0,-10],faring:[0,0,-30,-30,0,0,0,-10],fatilde:[0,0,-30,-30,0,0,0,-10],fdotlessi:[0,0,-28,-28,-35,-30,-60,-50],gr:[0,0,-10,-10],gracute:[0,0,-10,-10],grcaron:[0,0,-10,-10],grcommaaccent:[0,0,-10,-10],gbrever:[0,0,-10,-10],gbreveracute:[0,0,-10,-10],gbrevercaron:[0,0,-10,-10],gbrevercommaaccent:[0,0,-10,-10],gcommaaccentr:[0,0,-10,-10],gcommaaccentracute:[0,0,-10,-10],gcommaaccentrcaron:[0,0,-10,-10],gcommaaccentrcommaaccent:[0,0,-10,-10],ke:[0,0,-20,-20,-10,-30,-10,-10],keacute:[0,0,-20,-20,-10,-30,-10,-10],kecaron:[0,0,-20,-20,-10,-30,-10,-10],kecircumflex:[0,0,-20,-20,-10,-30,-10,-10],kedieresis:[0,0,-20,-20,-10,-30,-10,-10],kedotaccent:[0,0,-20,-20,-10,-30,-10,-10],kegrave:[0,0,-20,-20,-10,-30,-10,-10],kemacron:[0,0,-20,-20,-10,-30,-10,-10],keogonek:[0,0,-20,-20,-10,-30,-10,-10],kcommaaccente:[0,0,-20,-20,-10,-30,-10,-10],kcommaaccenteacute:[0,0,-20,-20,-10,-30,-10,-10],kcommaaccentecaron:[0,0,-20,-20,-10,-30,-10,-10],kcommaaccentecircumflex:[0,0,-20,-20,-10,-30,-10,-10],kcommaaccentedieresis:[0,0,-20,-20,-10,-30,-10,-10],kcommaaccentedotaccent:[0,0,-20,-20,-10,-30,-10,-10],kcommaaccentegrave:[0,0,-20,-20,-10,-30,-10,-10],kcommaaccentemacron:[0,0,-20,-20,-10,-30,-10,-10],kcommaaccenteogonek:[0,0,-20,-20,-10,-30,-10,-10],ocomma:[0,0,-40,-40],operiod:[0,0,-40,-40],oacutecomma:[0,0,-40,-40],oacuteperiod:[0,0,-40,-40],ocircumflexcomma:[0,0,-40,-40],ocircumflexperiod:[0,0,-40,-40],odieresiscomma:[0,0,-40,-40],odieresisperiod:[0,0,-40,-40],ogravecomma:[0,0,-40,-40],ograveperiod:[0,0,-40,-40],ohungarumlautcomma:[0,0,-40,-40],ohungarumlautperiod:[0,0,-40,-40],omacroncomma:[0,0,-40,-40],omacronperiod:[0,0,-40,-40],oslasha:[0,0,-55,-55],oslashaacute:[0,0,-55,-55],oslashabreve:[0,0,-55,-55],oslashacircumflex:[0,0,-55,-55],oslashadieresis:[0,0,-55,-55],oslashagrave:[0,0,-55,-55],oslashamacron:[0,0,-55,-55],oslashaogonek:[0,0,-55,-55],oslasharing:[0,0,-55,-55],oslashatilde:[0,0,-55,-55],oslashb:[0,0,-55,-55],oslashc:[0,0,-55,-55],oslashcacute:[0,0,-55,-55],oslashccaron:[0,0,-55,-55],oslashccedilla:[0,0,-55,-55],oslashcomma:[0,0,-95,-95],oslashd:[0,0,-55,-55],oslashdcroat:[0,0,-55,-55],oslashe:[0,0,-55,-55],oslasheacute:[0,0,-55,-55],oslashecaron:[0,0,-55,-55],oslashecircumflex:[0,0,-55,-55],oslashedieresis:[0,0,-55,-55],oslashedotaccent:[0,0,-55,-55],oslashegrave:[0,0,-55,-55],oslashemacron:[0,0,-55,-55],oslasheogonek:[0,0,-55,-55],oslashf:[0,0,-55,-55],oslashg:[0,0,-55,-55,0,0,-10],oslashgbreve:[0,0,-55,-55,0,0,-10],oslashgcommaaccent:[0,0,-55,-55,0,0,-10],oslashh:[0,0,-55,-55],oslashi:[0,0,-55,-55],oslashiacute:[0,0,-55,-55],oslashicircumflex:[0,0,-55,-55],oslashidieresis:[0,0,-55,-55],oslashigrave:[0,0,-55,-55],oslashimacron:[0,0,-55,-55],oslashiogonek:[0,0,-55,-55],oslashj:[0,0,-55,-55],oslashk:[0,0,-55,-55],oslashkcommaaccent:[0,0,-55,-55],oslashl:[0,0,-55,-55],oslashlacute:[0,0,-55,-55],oslashlcommaaccent:[0,0,-55,-55],oslashlslash:[0,0,-55,-55],oslashm:[0,0,-55,-55],oslashn:[0,0,-55,-55],oslashnacute:[0,0,-55,-55],oslashncaron:[0,0,-55,-55],oslashncommaaccent:[0,0,-55,-55],oslashntilde:[0,0,-55,-55],oslasho:[0,0,-55,-55],oslashoacute:[0,0,-55,-55],oslashocircumflex:[0,0,-55,-55],oslashodieresis:[0,0,-55,-55],oslashograve:[0,0,-55,-55],oslashohungarumlaut:[0,0,-55,-55],oslashomacron:[0,0,-55,-55],oslashoslash:[0,0,-55,-55],oslashotilde:[0,0,-55,-55],oslashp:[0,0,-55,-55],oslashperiod:[0,0,-95,-95],oslashq:[0,0,-55,-55],oslashr:[0,0,-55,-55],oslashracute:[0,0,-55,-55],oslashrcaron:[0,0,-55,-55],oslashrcommaaccent:[0,0,-55,-55],oslashs:[0,0,-55,-55],oslashsacute:[0,0,-55,-55],oslashscaron:[0,0,-55,-55],oslashscedilla:[0,0,-55,-55],oslashscommaaccent:[0,0,-55,-55],oslasht:[0,0,-55,-55],oslashtcommaaccent:[0,0,-55,-55],oslashu:[0,0,-55,-55],oslashuacute:[0,0,-55,-55],oslashucircumflex:[0,0,-55,-55],oslashudieresis:[0,0,-55,-55],oslashugrave:[0,0,-55,-55],oslashuhungarumlaut:[0,0,-55,-55],oslashumacron:[0,0,-55,-55],oslashuogonek:[0,0,-55,-55],oslashuring:[0,0,-55,-55],oslashz:[0,0,-55,-55],oslashzacute:[0,0,-55,-55],oslashzcaron:[0,0,-55,-55],oslashzdotaccent:[0,0,-55,-55],otildecomma:[0,0,-40,-40],otildeperiod:[0,0,-40,-40],pcomma:[0,0,-35,-35],pperiod:[0,0,-35,-35],ra:[0,0,-10,-10,0,0,-15],raacute:[0,0,-10,-10,0,0,-15],rabreve:[0,0,-10,-10,0,0,-15],racircumflex:[0,0,-10,-10,0,0,-15],radieresis:[0,0,-10,-10,0,0,-15],ragrave:[0,0,-10,-10,0,0,-15],ramacron:[0,0,-10,-10,0,0,-15],raogonek:[0,0,-10,-10,0,0,-15],raring:[0,0,-10,-10,0,0,-15],ratilde:[0,0,-10,-10,0,0,-15],rcolon:[0,0,30,30],ri:[0,0,15,15],riacute:[0,0,15,15],ricircumflex:[0,0,15,15],ridieresis:[0,0,15,15],rigrave:[0,0,15,15],rimacron:[0,0,15,15],riogonek:[0,0,15,15],rk:[0,0,15,15],rkcommaaccent:[0,0,15,15],rl:[0,0,15,15],rlacute:[0,0,15,15],rlcommaaccent:[0,0,15,15],rlslash:[0,0,15,15],rm:[0,0,25,25],rn:[0,0,25,25,-15],rnacute:[0,0,25,25,-15],rncaron:[0,0,25,25,-15],rncommaaccent:[0,0,25,25,-15],rntilde:[0,0,25,25,-15],rp:[0,0,30,30,-10],rsemicolon:[0,0,30,30],ru:[0,0,15,15],ruacute:[0,0,15,15],rucircumflex:[0,0,15,15],rudieresis:[0,0,15,15],rugrave:[0,0,15,15],ruhungarumlaut:[0,0,15,15],rumacron:[0,0,15,15],ruogonek:[0,0,15,15],ruring:[0,0,15,15],racutea:[0,0,-10,-10,0,0,-15],racuteaacute:[0,0,-10,-10,0,0,-15],racuteabreve:[0,0,-10,-10,0,0,-15],racuteacircumflex:[0,0,-10,-10,0,0,-15],racuteadieresis:[0,0,-10,-10,0,0,-15],racuteagrave:[0,0,-10,-10,0,0,-15],racuteamacron:[0,0,-10,-10,0,0,-15],racuteaogonek:[0,0,-10,-10,0,0,-15],racutearing:[0,0,-10,-10,0,0,-15],racuteatilde:[0,0,-10,-10,0,0,-15],racutecolon:[0,0,30,30],racutei:[0,0,15,15],racuteiacute:[0,0,15,15],racuteicircumflex:[0,0,15,15],racuteidieresis:[0,0,15,15],racuteigrave:[0,0,15,15],racuteimacron:[0,0,15,15],racuteiogonek:[0,0,15,15],racutek:[0,0,15,15],racutekcommaaccent:[0,0,15,15],racutel:[0,0,15,15],racutelacute:[0,0,15,15],racutelcommaaccent:[0,0,15,15],racutelslash:[0,0,15,15],racutem:[0,0,25,25],racuten:[0,0,25,25,-15],racutenacute:[0,0,25,25,-15],racutencaron:[0,0,25,25,-15],racutencommaaccent:[0,0,25,25,-15],racutentilde:[0,0,25,25,-15],racutep:[0,0,30,30,-10],racutesemicolon:[0,0,30,30],racuteu:[0,0,15,15],racuteuacute:[0,0,15,15],racuteucircumflex:[0,0,15,15],racuteudieresis:[0,0,15,15],racuteugrave:[0,0,15,15],racuteuhungarumlaut:[0,0,15,15],racuteumacron:[0,0,15,15],racuteuogonek:[0,0,15,15],racuteuring:[0,0,15,15],rcarona:[0,0,-10,-10,0,0,-15],rcaronaacute:[0,0,-10,-10,0,0,-15],rcaronabreve:[0,0,-10,-10,0,0,-15],rcaronacircumflex:[0,0,-10,-10,0,0,-15],rcaronadieresis:[0,0,-10,-10,0,0,-15],rcaronagrave:[0,0,-10,-10,0,0,-15],rcaronamacron:[0,0,-10,-10,0,0,-15],rcaronaogonek:[0,0,-10,-10,0,0,-15],rcaronaring:[0,0,-10,-10,0,0,-15],rcaronatilde:[0,0,-10,-10,0,0,-15],rcaroncolon:[0,0,30,30],rcaroni:[0,0,15,15],rcaroniacute:[0,0,15,15],rcaronicircumflex:[0,0,15,15],rcaronidieresis:[0,0,15,15],rcaronigrave:[0,0,15,15],rcaronimacron:[0,0,15,15],rcaroniogonek:[0,0,15,15],rcaronk:[0,0,15,15],rcaronkcommaaccent:[0,0,15,15],rcaronl:[0,0,15,15],rcaronlacute:[0,0,15,15],rcaronlcommaaccent:[0,0,15,15],rcaronlslash:[0,0,15,15],rcaronm:[0,0,25,25],rcaronn:[0,0,25,25,-15],rcaronnacute:[0,0,25,25,-15],rcaronncaron:[0,0,25,25,-15],rcaronncommaaccent:[0,0,25,25,-15],rcaronntilde:[0,0,25,25,-15],rcaronp:[0,0,30,30,-10],rcaronsemicolon:[0,0,30,30],rcaronu:[0,0,15,15],rcaronuacute:[0,0,15,15],rcaronucircumflex:[0,0,15,15],rcaronudieresis:[0,0,15,15],rcaronugrave:[0,0,15,15],rcaronuhungarumlaut:[0,0,15,15],rcaronumacron:[0,0,15,15],rcaronuogonek:[0,0,15,15],rcaronuring:[0,0,15,15],rcommaaccenta:[0,0,-10,-10,0,0,-15],rcommaaccentaacute:[0,0,-10,-10,0,0,-15],rcommaaccentabreve:[0,0,-10,-10,0,0,-15],rcommaaccentacircumflex:[0,0,-10,-10,0,0,-15],rcommaaccentadieresis:[0,0,-10,-10,0,0,-15],rcommaaccentagrave:[0,0,-10,-10,0,0,-15],rcommaaccentamacron:[0,0,-10,-10,0,0,-15],rcommaaccentaogonek:[0,0,-10,-10,0,0,-15],rcommaaccentaring:[0,0,-10,-10,0,0,-15],rcommaaccentatilde:[0,0,-10,-10,0,0,-15],rcommaaccentcolon:[0,0,30,30],rcommaaccenti:[0,0,15,15],rcommaaccentiacute:[0,0,15,15],rcommaaccenticircumflex:[0,0,15,15],rcommaaccentidieresis:[0,0,15,15],rcommaaccentigrave:[0,0,15,15],rcommaaccentimacron:[0,0,15,15],rcommaaccentiogonek:[0,0,15,15],rcommaaccentk:[0,0,15,15],rcommaaccentkcommaaccent:[0,0,15,15],rcommaaccentl:[0,0,15,15],rcommaaccentlacute:[0,0,15,15],rcommaaccentlcommaaccent:[0,0,15,15],rcommaaccentlslash:[0,0,15,15],rcommaaccentm:[0,0,25,25],rcommaaccentn:[0,0,25,25,-15],rcommaaccentnacute:[0,0,25,25,-15],rcommaaccentncaron:[0,0,25,25,-15],rcommaaccentncommaaccent:[0,0,25,25,-15],rcommaaccentntilde:[0,0,25,25,-15],rcommaaccentp:[0,0,30,30,-10],rcommaaccentsemicolon:[0,0,30,30],rcommaaccentu:[0,0,15,15],rcommaaccentuacute:[0,0,15,15],rcommaaccentucircumflex:[0,0,15,15],rcommaaccentudieresis:[0,0,15,15],rcommaaccentugrave:[0,0,15,15],rcommaaccentuhungarumlaut:[0,0,15,15],rcommaaccentumacron:[0,0,15,15],rcommaaccentuogonek:[0,0,15,15],rcommaaccenturing:[0,0,15,15],scomma:[0,0,-15,-15],speriod:[0,0,-15,-15],sacutecomma:[0,0,-15,-15],sacuteperiod:[0,0,-15,-15],scaroncomma:[0,0,-15,-15],scaronperiod:[0,0,-15,-15],scedillacomma:[0,0,-15,-15],scedillaperiod:[0,0,-15,-15],scommaaccentcomma:[0,0,-15,-15],scommaaccentperiod:[0,0,-15,-15],ve:[0,0,-25,-25,-10,-15,0,-15],veacute:[0,0,-25,-25,-10,-15,0,-15],vecaron:[0,0,-25,-25,-10,-15,0,-15],vecircumflex:[0,0,-25,-25,-10,-15,0,-15],vedieresis:[0,0,-25,-25,-10,-15,0,-15],vedotaccent:[0,0,-25,-25,-10,-15,0,-15],vegrave:[0,0,-25,-25,-10,-15,0,-15],vemacron:[0,0,-25,-25,-10,-15,0,-15],veogonek:[0,0,-25,-25,-10,-15,0,-15],wa:[0,0,-15,-15,0,-10,0,-10],waacute:[0,0,-15,-15,0,-10,0,-10],wabreve:[0,0,-15,-15,0,-10,0,-10],wacircumflex:[0,0,-15,-15,0,-10,0,-10],wadieresis:[0,0,-15,-15,0,-10,0,-10],wagrave:[0,0,-15,-15,0,-10,0,-10],wamacron:[0,0,-15,-15,0,-10,0,-10],waogonek:[0,0,-15,-15,0,-10,0,-10],waring:[0,0,-15,-15,0,-10,0,-10],watilde:[0,0,-15,-15,0,-10,0,-10],we:[0,0,-10,-10,0,-10],weacute:[0,0,-10,-10,0,-10],wecaron:[0,0,-10,-10,0,-10],wecircumflex:[0,0,-10,-10,0,-10],wedieresis:[0,0,-10,-10,0,-10],wedotaccent:[0,0,-10,-10,0,-10],wegrave:[0,0,-10,-10,0,-10],wemacron:[0,0,-10,-10,0,-10],weogonek:[0,0,-10,-10,0,-10],zo:[0,0,-15,-15],zoacute:[0,0,-15,-15],zocircumflex:[0,0,-15,-15],zodieresis:[0,0,-15,-15],zograve:[0,0,-15,-15],zohungarumlaut:[0,0,-15,-15],zomacron:[0,0,-15,-15],zoslash:[0,0,-15,-15],zotilde:[0,0,-15,-15],zacuteo:[0,0,-15,-15],zacuteoacute:[0,0,-15,-15],zacuteocircumflex:[0,0,-15,-15],zacuteodieresis:[0,0,-15,-15],zacuteograve:[0,0,-15,-15],zacuteohungarumlaut:[0,0,-15,-15],zacuteomacron:[0,0,-15,-15],zacuteoslash:[0,0,-15,-15],zacuteotilde:[0,0,-15,-15],zcarono:[0,0,-15,-15],zcaronoacute:[0,0,-15,-15],zcaronocircumflex:[0,0,-15,-15],zcaronodieresis:[0,0,-15,-15],zcaronograve:[0,0,-15,-15],zcaronohungarumlaut:[0,0,-15,-15],zcaronomacron:[0,0,-15,-15],zcaronoslash:[0,0,-15,-15],zcaronotilde:[0,0,-15,-15],zdotaccento:[0,0,-15,-15],zdotaccentoacute:[0,0,-15,-15],zdotaccentocircumflex:[0,0,-15,-15],zdotaccentodieresis:[0,0,-15,-15],zdotaccentograve:[0,0,-15,-15],zdotaccentohungarumlaut:[0,0,-15,-15],zdotaccentomacron:[0,0,-15,-15],zdotaccentoslash:[0,0,-15,-15],zdotaccentotilde:[0,0,-15,-15],Ap:[0,0,0,0,-25],Aquoteright:[0,0,0,0,-74,-74,-37,-111],Aacutep:[0,0,0,0,-25],Aacutequoteright:[0,0,0,0,-74,-74,-37,-111],Abrevep:[0,0,0,0,-25],Abrevequoteright:[0,0,0,0,-74,-74,-37,-111],Acircumflexp:[0,0,0,0,-25],Acircumflexquoteright:[0,0,0,0,-74,-74,-37,-111],Adieresisp:[0,0,0,0,-25],Adieresisquoteright:[0,0,0,0,-74,-74,-37,-111],Agravep:[0,0,0,0,-25],Agravequoteright:[0,0,0,0,-74,-74,-37,-111],Amacronp:[0,0,0,0,-25],Amacronquoteright:[0,0,0,0,-74,-74,-37,-111],Aogonekp:[0,0,0,0,-25],Aogonekquoteright:[0,0,0,0,-74,-74,-37,-111],Aringp:[0,0,0,0,-25],Aringquoteright:[0,0,0,0,-74,-74,-37,-111],Atildep:[0,0,0,0,-25],Atildequoteright:[0,0,0,0,-74,-74,-37,-111],Je:[0,0,0,0,-15,-40,-25],Jeacute:[0,0,0,0,-15,-40,-25],Jecaron:[0,0,0,0,-15,-40,-25],Jecircumflex:[0,0,0,0,-15,-40,-25],Jedieresis:[0,0,0,0,-15,-40,-25],Jedotaccent:[0,0,0,0,-15,-40,-25],Jegrave:[0,0,0,0,-15,-40,-25],Jemacron:[0,0,0,0,-15,-40,-25],Jeogonek:[0,0,0,0,-15,-40,-25],Jo:[0,0,0,0,-15,-40,-25],Joacute:[0,0,0,0,-15,-40,-25],Jocircumflex:[0,0,0,0,-15,-40,-25],Jodieresis:[0,0,0,0,-15,-40,-25],Jograve:[0,0,0,0,-15,-40,-25],Johungarumlaut:[0,0,0,0,-15,-40,-25],Jomacron:[0,0,0,0,-15,-40,-25],Joslash:[0,0,0,0,-15,-40,-25],Jotilde:[0,0,0,0,-15,-40,-25],NA:[0,0,0,0,-20,-30,-27,-35],NAacute:[0,0,0,0,-20,-30,-27,-35],NAbreve:[0,0,0,0,-20,-30,-27,-35],NAcircumflex:[0,0,0,0,-20,-30,-27,-35],NAdieresis:[0,0,0,0,-20,-30,-27,-35],NAgrave:[0,0,0,0,-20,-30,-27,-35],NAmacron:[0,0,0,0,-20,-30,-27,-35],NAogonek:[0,0,0,0,-20,-30,-27,-35],NAring:[0,0,0,0,-20,-30,-27,-35],NAtilde:[0,0,0,0,-20,-30,-27,-35],NacuteA:[0,0,0,0,-20,-30,-27,-35],NacuteAacute:[0,0,0,0,-20,-30,-27,-35],NacuteAbreve:[0,0,0,0,-20,-30,-27,-35],NacuteAcircumflex:[0,0,0,0,-20,-30,-27,-35],NacuteAdieresis:[0,0,0,0,-20,-30,-27,-35],NacuteAgrave:[0,0,0,0,-20,-30,-27,-35],NacuteAmacron:[0,0,0,0,-20,-30,-27,-35],NacuteAogonek:[0,0,0,0,-20,-30,-27,-35],NacuteAring:[0,0,0,0,-20,-30,-27,-35],NacuteAtilde:[0,0,0,0,-20,-30,-27,-35],NcaronA:[0,0,0,0,-20,-30,-27,-35],NcaronAacute:[0,0,0,0,-20,-30,-27,-35],NcaronAbreve:[0,0,0,0,-20,-30,-27,-35],NcaronAcircumflex:[0,0,0,0,-20,-30,-27,-35],NcaronAdieresis:[0,0,0,0,-20,-30,-27,-35],NcaronAgrave:[0,0,0,0,-20,-30,-27,-35],NcaronAmacron:[0,0,0,0,-20,-30,-27,-35],NcaronAogonek:[0,0,0,0,-20,-30,-27,-35],NcaronAring:[0,0,0,0,-20,-30,-27,-35],NcaronAtilde:[0,0,0,0,-20,-30,-27,-35],NcommaaccentA:[0,0,0,0,-20,-30,-27,-35],NcommaaccentAacute:[0,0,0,0,-20,-30,-27,-35],NcommaaccentAbreve:[0,0,0,0,-20,-30,-27,-35],NcommaaccentAcircumflex:[0,0,0,0,-20,-30,-27,-35],NcommaaccentAdieresis:[0,0,0,0,-20,-30,-27,-35],NcommaaccentAgrave:[0,0,0,0,-20,-30,-27,-35],NcommaaccentAmacron:[0,0,0,0,-20,-30,-27,-35],NcommaaccentAogonek:[0,0,0,0,-20,-30,-27,-35],NcommaaccentAring:[0,0,0,0,-20,-30,-27,-35],NcommaaccentAtilde:[0,0,0,0,-20,-30,-27,-35],NtildeA:[0,0,0,0,-20,-30,-27,-35],NtildeAacute:[0,0,0,0,-20,-30,-27,-35],NtildeAbreve:[0,0,0,0,-20,-30,-27,-35],NtildeAcircumflex:[0,0,0,0,-20,-30,-27,-35],NtildeAdieresis:[0,0,0,0,-20,-30,-27,-35],NtildeAgrave:[0,0,0,0,-20,-30,-27,-35],NtildeAmacron:[0,0,0,0,-20,-30,-27,-35],NtildeAogonek:[0,0,0,0,-20,-30,-27,-35],NtildeAring:[0,0,0,0,-20,-30,-27,-35],NtildeAtilde:[0,0,0,0,-20,-30,-27,-35],Ti:[0,0,0,0,-18,-37,-55,-35],Tiacute:[0,0,0,0,-18,-37,-55,-35],Tiogonek:[0,0,0,0,-18,-37,-55,-35],Tcaroni:[0,0,0,0,-18,-37,-55,-35],Tcaroniacute:[0,0,0,0,-18,-37,-55,-35],Tcaroniogonek:[0,0,0,0,-18,-37,-55,-35],Tcommaaccenti:[0,0,0,0,-18,-37,-55,-35],Tcommaaccentiacute:[0,0,0,0,-18,-37,-55,-35],Tcommaaccentiogonek:[0,0,0,0,-18,-37,-55,-35],Vi:[0,0,0,0,-37,-55,-74,-60],Viacute:[0,0,0,0,-37,-55,-74,-60],Vicircumflex:[0,0,0,0,-37,0,-34,-20],Vidieresis:[0,0,0,0,-37,0,-34,-20],Vigrave:[0,0,0,0,-37,0,-34,-20],Vimacron:[0,0,0,0,-37,0,-34,-20],Viogonek:[0,0,0,0,-37,-55,-74,-60],Wi:[0,0,0,0,-18,-37,-55,-40],Wiacute:[0,0,0,0,-18,-37,-55,-40],Wiogonek:[0,0,0,0,-18,-37,-55,-40],fi:[0,0,0,0,-25,0,-20,-20],gperiod:[0,0,0,0,-15,0,-15],gbreveperiod:[0,0,0,0,-15,0,-15],gcommaaccentperiod:[0,0,0,0,-15,0,-15],iv:[0,0,0,0,-10,0,0,-25],iacutev:[0,0,0,0,-10,0,0,-25],icircumflexv:[0,0,0,0,-10,0,0,-25],idieresisv:[0,0,0,0,-10,0,0,-25],igravev:[0,0,0,0,-10,0,0,-25],imacronv:[0,0,0,0,-10,0,0,-25],iogonekv:[0,0,0,0,-10,0,0,-25],ky:[0,0,0,0,-15,0,-10,-15],kyacute:[0,0,0,0,-15,0,-10,-15],kydieresis:[0,0,0,0,-15,0,-10,-15],kcommaaccenty:[0,0,0,0,-15,0,-10,-15],kcommaaccentyacute:[0,0,0,0,-15,0,-10,-15],kcommaaccentydieresis:[0,0,0,0,-15,0,-10,-15],quotedblleftA:[0,0,0,0,-10,0,0,-80],quotedblleftAacute:[0,0,0,0,-10,0,0,-80],quotedblleftAbreve:[0,0,0,0,-10,0,0,-80],quotedblleftAcircumflex:[0,0,0,0,-10,0,0,-80],quotedblleftAdieresis:[0,0,0,0,-10,0,0,-80],quotedblleftAgrave:[0,0,0,0,-10,0,0,-80],quotedblleftAmacron:[0,0,0,0,-10,0,0,-80],quotedblleftAogonek:[0,0,0,0,-10,0,0,-80],quotedblleftAring:[0,0,0,0,-10,0,0,-80],quotedblleftAtilde:[0,0,0,0,-10,0,0,-80],quoteleftA:[0,0,0,0,-10,0,0,-80],quoteleftAacute:[0,0,0,0,-10,0,0,-80],quoteleftAbreve:[0,0,0,0,-10,0,0,-80],quoteleftAcircumflex:[0,0,0,0,-10,0,0,-80],quoteleftAdieresis:[0,0,0,0,-10,0,0,-80],quoteleftAgrave:[0,0,0,0,-10,0,0,-80],quoteleftAmacron:[0,0,0,0,-10,0,0,-80],quoteleftAogonek:[0,0,0,0,-10,0,0,-80],quoteleftAring:[0,0,0,0,-10,0,0,-80],quoteleftAtilde:[0,0,0,0,-10,0,0,-80],re:[0,0,0,0,-18,0,-37],reacute:[0,0,0,0,-18,0,-37],recaron:[0,0,0,0,-18,0,-37],recircumflex:[0,0,0,0,-18,0,-37],redieresis:[0,0,0,0,-18,0,-37],redotaccent:[0,0,0,0,-18,0,-37],regrave:[0,0,0,0,-18,0,-37],remacron:[0,0,0,0,-18,0,-37],reogonek:[0,0,0,0,-18,0,-37],racutee:[0,0,0,0,-18,0,-37],racuteeacute:[0,0,0,0,-18,0,-37],racuteecaron:[0,0,0,0,-18,0,-37],racuteecircumflex:[0,0,0,0,-18,0,-37],racuteedieresis:[0,0,0,0,-18,0,-37],racuteedotaccent:[0,0,0,0,-18,0,-37],racuteegrave:[0,0,0,0,-18,0,-37],racuteemacron:[0,0,0,0,-18,0,-37],racuteeogonek:[0,0,0,0,-18,0,-37],rcarone:[0,0,0,0,-18,0,-37],rcaroneacute:[0,0,0,0,-18,0,-37],rcaronecaron:[0,0,0,0,-18,0,-37],rcaronecircumflex:[0,0,0,0,-18,0,-37],rcaronedieresis:[0,0,0,0,-18,0,-37],rcaronedotaccent:[0,0,0,0,-18,0,-37],rcaronegrave:[0,0,0,0,-18,0,-37],rcaronemacron:[0,0,0,0,-18,0,-37],rcaroneogonek:[0,0,0,0,-18,0,-37],rcommaaccente:[0,0,0,0,-18,0,-37],rcommaaccenteacute:[0,0,0,0,-18,0,-37],rcommaaccentecaron:[0,0,0,0,-18,0,-37],rcommaaccentecircumflex:[0,0,0,0,-18,0,-37],rcommaaccentedieresis:[0,0,0,0,-18,0,-37],rcommaaccentedotaccent:[0,0,0,0,-18,0,-37],rcommaaccentegrave:[0,0,0,0,-18,0,-37],rcommaaccentemacron:[0,0,0,0,-18,0,-37],rcommaaccenteogonek:[0,0,0,0,-18,0,-37],spaceA:[0,0,0,0,-55,-37,-18,-55],spaceAacute:[0,0,0,0,-55,-37,-18,-55],spaceAbreve:[0,0,0,0,-55,-37,-18,-55],spaceAcircumflex:[0,0,0,0,-55,-37,-18,-55],spaceAdieresis:[0,0,0,0,-55,-37,-18,-55],spaceAgrave:[0,0,0,0,-55,-37,-18,-55],spaceAmacron:[0,0,0,0,-55,-37,-18,-55],spaceAogonek:[0,0,0,0,-55,-37,-18,-55],spaceAring:[0,0,0,0,-55,-37,-18,-55],spaceAtilde:[0,0,0,0,-55,-37,-18,-55],Fi:[0,0,0,0,0,-40,-45],Fiacute:[0,0,0,0,0,-40,-45],Ficircumflex:[0,0,0,0,0,-40,-45],Fidieresis:[0,0,0,0,0,-40,-45],Figrave:[0,0,0,0,0,-40,-45],Fimacron:[0,0,0,0,0,-40,-45],Fiogonek:[0,0,0,0,0,-40,-45],eb:[0,0,0,0,0,-10],eacuteb:[0,0,0,0,0,-10],ecaronb:[0,0,0,0,0,-10],ecircumflexb:[0,0,0,0,0,-10],edieresisb:[0,0,0,0,0,-10],edotaccentb:[0,0,0,0,0,-10],egraveb:[0,0,0,0,0,-10],emacronb:[0,0,0,0,0,-10],eogonekb:[0,0,0,0,0,-10],ff:[0,0,0,0,0,-18,-18,-25],quoterightt:[0,0,0,0,0,-37,-30,-18],quoterighttcommaaccent:[0,0,0,0,0,-37,-30,-18],Yicircumflex:[0,0,0,0,0,0,-34],Yidieresis:[0,0,0,0,0,0,-34],Yigrave:[0,0,0,0,0,0,-34],Yimacron:[0,0,0,0,0,0,-34],Yacuteicircumflex:[0,0,0,0,0,0,-34],Yacuteidieresis:[0,0,0,0,0,0,-34],Yacuteigrave:[0,0,0,0,0,0,-34],Yacuteimacron:[0,0,0,0,0,0,-34],Ydieresisicircumflex:[0,0,0,0,0,0,-34],Ydieresisidieresis:[0,0,0,0,0,0,-34],Ydieresisigrave:[0,0,0,0,0,0,-34],Ydieresisimacron:[0,0,0,0,0,0,-34],eg:[0,0,0,0,0,0,-40,-15],egbreve:[0,0,0,0,0,0,-40,-15],egcommaaccent:[0,0,0,0,0,0,-40,-15],eacuteg:[0,0,0,0,0,0,-40,-15],eacutegbreve:[0,0,0,0,0,0,-40,-15],eacutegcommaaccent:[0,0,0,0,0,0,-40,-15],ecarong:[0,0,0,0,0,0,-40,-15],ecarongbreve:[0,0,0,0,0,0,-40,-15],ecarongcommaaccent:[0,0,0,0,0,0,-40,-15],ecircumflexg:[0,0,0,0,0,0,-40,-15],ecircumflexgbreve:[0,0,0,0,0,0,-40,-15],ecircumflexgcommaaccent:[0,0,0,0,0,0,-40,-15],edieresisg:[0,0,0,0,0,0,-40,-15],edieresisgbreve:[0,0,0,0,0,0,-40,-15],edieresisgcommaaccent:[0,0,0,0,0,0,-40,-15],edotaccentg:[0,0,0,0,0,0,-40,-15],edotaccentgbreve:[0,0,0,0,0,0,-40,-15],edotaccentgcommaaccent:[0,0,0,0,0,0,-40,-15],egraveg:[0,0,0,0,0,0,-40,-15],egravegbreve:[0,0,0,0,0,0,-40,-15],egravegcommaaccent:[0,0,0,0,0,0,-40,-15],emacrong:[0,0,0,0,0,0,-40,-15],emacrongbreve:[0,0,0,0,0,0,-40,-15],emacrongcommaaccent:[0,0,0,0,0,0,-40,-15],eogonekg:[0,0,0,0,0,0,-40,-15],eogonekgbreve:[0,0,0,0,0,0,-40,-15],eogonekgcommaaccent:[0,0,0,0,0,0,-40,-15],fiogonek:[0,0,0,0,0,0,-20],gcomma:[0,0,0,0,0,0,-10],gbrevecomma:[0,0,0,0,0,0,-10],gcommaaccentcomma:[0,0,0,0,0,0,-10],og:[0,0,0,0,0,0,-10],ogbreve:[0,0,0,0,0,0,-10],ogcommaaccent:[0,0,0,0,0,0,-10],oacuteg:[0,0,0,0,0,0,-10],oacutegbreve:[0,0,0,0,0,0,-10],oacutegcommaaccent:[0,0,0,0,0,0,-10],ocircumflexg:[0,0,0,0,0,0,-10],ocircumflexgbreve:[0,0,0,0,0,0,-10],ocircumflexgcommaaccent:[0,0,0,0,0,0,-10],odieresisg:[0,0,0,0,0,0,-10],odieresisgbreve:[0,0,0,0,0,0,-10],odieresisgcommaaccent:[0,0,0,0,0,0,-10],ograveg:[0,0,0,0,0,0,-10],ogravegbreve:[0,0,0,0,0,0,-10],ogravegcommaaccent:[0,0,0,0,0,0,-10],ohungarumlautg:[0,0,0,0,0,0,-10],ohungarumlautgbreve:[0,0,0,0,0,0,-10],ohungarumlautgcommaaccent:[0,0,0,0,0,0,-10],omacrong:[0,0,0,0,0,0,-10],omacrongbreve:[0,0,0,0,0,0,-10],omacrongcommaaccent:[0,0,0,0,0,0,-10],otildeg:[0,0,0,0,0,0,-10],otildegbreve:[0,0,0,0,0,0,-10],otildegcommaaccent:[0,0,0,0,0,0,-10],fiacute:[0,0,0,0,0,0,0,-20],ga:[0,0,0,0,0,0,0,-5],gaacute:[0,0,0,0,0,0,0,-5],gabreve:[0,0,0,0,0,0,0,-5],gacircumflex:[0,0,0,0,0,0,0,-5],gadieresis:[0,0,0,0,0,0,0,-5],gagrave:[0,0,0,0,0,0,0,-5],gamacron:[0,0,0,0,0,0,0,-5],gaogonek:[0,0,0,0,0,0,0,-5],garing:[0,0,0,0,0,0,0,-5],gatilde:[0,0,0,0,0,0,0,-5],gbrevea:[0,0,0,0,0,0,0,-5],gbreveaacute:[0,0,0,0,0,0,0,-5],gbreveabreve:[0,0,0,0,0,0,0,-5],gbreveacircumflex:[0,0,0,0,0,0,0,-5],gbreveadieresis:[0,0,0,0,0,0,0,-5],gbreveagrave:[0,0,0,0,0,0,0,-5],gbreveamacron:[0,0,0,0,0,0,0,-5],gbreveaogonek:[0,0,0,0,0,0,0,-5],gbrevearing:[0,0,0,0,0,0,0,-5],gbreveatilde:[0,0,0,0,0,0,0,-5],gcommaaccenta:[0,0,0,0,0,0,0,-5],gcommaaccentaacute:[0,0,0,0,0,0,0,-5],gcommaaccentabreve:[0,0,0,0,0,0,0,-5],gcommaaccentacircumflex:[0,0,0,0,0,0,0,-5],gcommaaccentadieresis:[0,0,0,0,0,0,0,-5],gcommaaccentagrave:[0,0,0,0,0,0,0,-5],gcommaaccentamacron:[0,0,0,0,0,0,0,-5],gcommaaccentaogonek:[0,0,0,0,0,0,0,-5],gcommaaccentaring:[0,0,0,0,0,0,0,-5],gcommaaccentatilde:[0,0,0,0,0,0,0,-5]}}),nA=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return Array.from(t).map(e=>`0000${e.toString(16)}`.slice(-4)).join("")};class nb{static open(e,t,r,i){let n;if("string"==typeof t){if(n_.isStandardFont(t))return new n_(e,t,i);throw Error(`Can't open ${t} in browser build`)}if(t instanceof Uint8Array?n=D.vt(t,r):t instanceof ArrayBuffer?n=D.vt(new Uint8Array(t),r):"object"==typeof t&&(n=t),null==n)throw Error("Not a supported font format or standard PDF font.");return new nw(e,n,i)}encode(){throw Error("Must be implemented by subclasses")}widthOfString(){throw Error("Must be implemented by subclasses")}ref(){return null!=this.dictionary?this.dictionary:this.dictionary=this.document.ref()}finalize(){if(!this.embedded&&null!=this.dictionary)return this.embed(),this.embedded=!0}embed(){throw Error("Must be implemented by subclasses")}lineHeight(e,t){null==t&&(t=!1);let r=t?this.lineGap:0;return(this.ascender+r-this.descender)/1e3*e}}let n_=(e=>class extends e{constructor(e,t,r){super(),this.document=e,this.name=t,this.id=r,this.font=np.fromJson(nv[this.name]),this.ascender=this.font.ascender,this.descender=this.font.descender,this.bbox=this.font.bbox,this.lineGap=this.font.lineGap}embed(){return this.dictionary.data={Type:"Font",BaseFont:this.name,Subtype:"Type1",Encoding:"WinAnsiEncoding"},this.dictionary.end()}encode(e){let t=this.font.encodeText(e),r=this.font.glyphsForString(`${e}`),i=this.font.advancesForGlyphs(r),n=[];for(let e=0;e<r.length;e++){let t=r[e];n.push({xAdvance:i[e],yAdvance:0,xOffset:0,yOffset:0,advanceWidth:this.font.widthOfGlyph(t)})}return[t,n]}encodeGlyphs(e){let t=[];for(let r of Array.from(e))t.push(`00${r.id.toString(16)}`.slice(-2));return t}widthOfString(e,t){let r=this.font.glyphsForString(`${e}`),i=this.font.advancesForGlyphs(r),n=0;for(let e of Array.from(i))n+=e;return t/1e3*n}static isStandardFont(e){return e in nv}})(nb),nw=(e=>class extends e{constructor(e,t,r){super(),this.document=e,this.font=t,this.id=r,this.subset=this.font.createSubset(),this.unicode=[[0]],this.widths=[this.font.getGlyph(0).advanceWidth],this.name=this.font.postscriptName,this.scale=1e3/this.font.unitsPerEm,this.ascender=this.font.ascent*this.scale,this.descender=this.font.descent*this.scale,this.xHeight=this.font.xHeight*this.scale,this.capHeight=this.font.capHeight*this.scale,this.lineGap=this.font.lineGap*this.scale,this.bbox=this.font.bbox,this.layoutCache=Object.create(null)}layoutRun(e,t){let r=this.font.layout(e,t,void 0,void 0,"ltr");for(let e=0;e<r.positions.length;e++){let t=r.positions[e];for(let e in t)t[e]*=this.scale;t.advanceWidth=r.glyphs[e].advanceWidth*this.scale}return r}layoutCached(e){let t;if(t=this.layoutCache[e])return t;let r=this.layoutRun(e);return this.layoutCache[e]=r,r}layout(e,t,r){if(null==r&&(r=!1),t)return this.layoutRun(e,t);let i=r?null:[],n=r?null:[],a=0,o=0,s=0;for(;s<=e.length;)if(s===e.length&&o<s||[" ","	"].includes(e.charAt(s))){let t=this.layoutCached(e.slice(o,++s));r||(i.push(...Array.from(t.glyphs||[])),n.push(...Array.from(t.positions||[]))),a+=t.advanceWidth,o=s}else s++;return{glyphs:i,positions:n,advanceWidth:a}}encode(e,t){let{glyphs:r,positions:i}=this.layout(e,t),n=[];for(let e=0;e<r.length;e++){let t=r[e],i=this.subset.includeGlyph(t.id);n.push(`0000${i.toString(16)}`.slice(-4)),null==this.widths[i]&&(this.widths[i]=t.advanceWidth*this.scale),null==this.unicode[i]&&(this.unicode[i]=t.codePoints)}return[n,i]}encodeGlyphs(e){let t=[];for(let r=0;r<e.length;r++){let i=e[r],n=this.subset.includeGlyph(i.id);t.push(`0000${n.toString(16)}`.slice(-4)),null==this.widths[n]&&(this.widths[n]=i.advanceWidth*this.scale),null==this.unicode[n]&&(this.unicode[n]=i.codePoints)}return t}widthOfString(e,t,r){return t/1e3*this.layout(e,r,!0).advanceWidth}embed(){let e=null!=this.subset.cff,t=this.document.ref();e&&(t.data.Subtype="CIDFontType0C"),t.end(this.subset.encode());let r=((null!=this.font["OS/2"]?this.font["OS/2"].sFamilyClass:void 0)||0)>>8,i=0;this.font.post.isFixedPitch&&(i|=1),1<=r&&r<=7&&(i|=2),i|=4,10===r&&(i|=8),this.font.head.macStyle.italic&&(i|=64);let n=[0,1,2,3,4,5].map(()=>String.fromCharCode(26*Math.random()+65)).join("")+"+"+this.font.postscriptName,{bbox:a}=this.font,o=this.document.ref({Type:"FontDescriptor",FontName:n,Flags:i,FontBBox:[a.minX*this.scale,a.minY*this.scale,a.maxX*this.scale,a.maxY*this.scale],ItalicAngle:this.font.italicAngle,Ascent:this.ascender,Descent:this.descender,CapHeight:(this.font.capHeight||this.font.ascent)*this.scale,XHeight:(this.font.xHeight||0)*this.scale,StemV:0});e?o.data.FontFile3=t:o.data.FontFile2=t,o.end();let s={Type:"Font",Subtype:"CIDFontType0",BaseFont:n,CIDSystemInfo:{Registry:new String("Adobe"),Ordering:new String("Identity"),Supplement:0},FontDescriptor:o,W:[0,this.widths]};e||(s.Subtype="CIDFontType2",s.CIDToGIDMap="Identity");let c=this.document.ref(s);return c.end(),this.dictionary.data={Type:"Font",Subtype:"Type0",BaseFont:n,Encoding:"Identity-H",DescendantFonts:[c],ToUnicode:this.toUnicodeCmap()},this.dictionary.end()}toUnicodeCmap(){let e=this.document.ref(),t=[],r="/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000><ffff>\nendcodespacerange";for(let[e,i]of this.unicode.entries()){let n=[];for(let e of(t.length>=100&&(r+="\n"+t.length+" beginbfchar\n"+t.join("\n")+"\nendbfchar",t=[]),i))e>65535&&(e-=65536,n.push(nA(e>>>10&1023|55296)),e=56320|1023&e),n.push(nA(e));t.push("<"+nA(e)+"><"+n.join(" ")+">")}return t.length&&(r+="\n"+t.length+" beginbfchar\n"+t.join("\n")+"\nendbfchar\n"),r+="endcmap\nCMapName currentdict /CMap defineresource pop\nend\nend",e.end(r),e}})(nb);function nT(e){return Math.fround(e)}class nS extends eG{constructor(e,t){super(),this.document=e,this.horizontalScaling=t.horizontalScaling||100,this.indent=(t.indent||0)*this.horizontalScaling/100,this.characterSpacing=(t.characterSpacing||0)*this.horizontalScaling/100,this.wordSpacing=(0===t.wordSpacing)*this.horizontalScaling/100,this.columns=t.columns||1,this.columnGap=(null!=t.columnGap?t.columnGap:18)*this.horizontalScaling/100,this.lineWidth=(t.width*this.horizontalScaling/100-this.columnGap*(this.columns-1))/this.columns,this.spaceLeft=this.lineWidth,this.startX=this.document.x,this.startY=this.document.y,this.column=1,this.ellipsis=t.ellipsis,this.continuedX=0,this.features=t.features,null!=t.height?(this.height=t.height,this.maxY=nT(this.startY+t.height)):this.maxY=nT(this.document.page.maxY()),this.on("firstLine",e=>{let t=this.continuedX||this.indent;if(this.document.x+=t,this.lineWidth-=t,!e.indentAllLines)return this.once("line",()=>{if(this.document.x-=t,this.lineWidth+=t,e.continued&&!this.continuedX&&(this.continuedX=this.indent),!e.continued)return this.continuedX=0})}),this.on("lastLine",e=>{let{align:t}=e;return"justify"===t&&(e.align="left"),this.lastLine=!0,this.once("line",()=>(this.document.y+=e.paragraphGap||0,e.align=t,this.lastLine=!1))})}wordWidth(e){return this.document.widthOfString(e,this)+this.characterSpacing+this.wordSpacing}canFit(e,t){return"\xad"!=e[e.length-1]?t<=this.spaceLeft:t+this.wordWidth("-")<=this.spaceLeft}eachWord(e,t){let r,i=new P.A(e),n=null,a=Object.create(null);for(;r=i.nextBreak();){var o,s,c;let i=e.slice((null!=n?n.position:void 0)||0,r.position),u=null!=a[i]?a[i]:a[i]=this.wordWidth(i);if(u>this.lineWidth+this.continuedX){let e=n,a={};for(;i.length;){u>this.spaceLeft?(s=Math.ceil(this.spaceLeft/(u/i.length)),c=(u=this.wordWidth(i.slice(0,s)))<=this.spaceLeft&&s<i.length):s=i.length;let n=u>this.spaceLeft&&s>0;for(;n||c;)n?n=(u=this.wordWidth(i.slice(0,--s)))>this.spaceLeft&&s>0:(n=(u=this.wordWidth(i.slice(0,++s)))>this.spaceLeft&&s>0,c=u<=this.spaceLeft&&s<i.length);if(0===s&&this.spaceLeft===this.lineWidth&&(s=1),a.required=r.required||s<i.length,o=t(i.slice(0,s),u,a,e),e={required:!1},i=i.slice(s),u=this.wordWidth(i),!1===o)break}}else o=t(i,u,r,n);if(!1===o)break;n=r}}wrap(e,t){this.horizontalScaling=t.horizontalScaling||100,null!=t.indent&&(this.indent=t.indent*this.horizontalScaling/100),null!=t.characterSpacing&&(this.characterSpacing=t.characterSpacing*this.horizontalScaling/100),null!=t.wordSpacing&&(this.wordSpacing=t.wordSpacing*this.horizontalScaling/100),null!=t.ellipsis&&(this.ellipsis=t.ellipsis);let r=this.document.y+this.document.currentLineHeight(!0);(this.document.y>this.maxY||r>this.maxY)&&this.nextSection();let i="",n=0,a=0,o=0,{y:s}=this.document,c=()=>(t.textWidth=n+this.wordSpacing*(a-1),t.wordCount=a,t.lineWidth=this.lineWidth,{y:s}=this.document,this.emit("line",i,t,this),o++);return(this.emit("sectionStart",t,this),this.eachWord(e,(e,r,o,s)=>{if((null==s||s.required)&&(this.emit("firstLine",t,this),this.spaceLeft=this.lineWidth),this.canFit(e,r)&&(i+=e,n+=r,a++),!o.required&&this.canFit(e,r))return this.spaceLeft-=r;{let s=this.document.currentLineHeight(!0);if(null!=this.height&&this.ellipsis&&nT(this.document.y+2*s)>this.maxY&&this.column>=this.columns){for(!0===this.ellipsis&&(this.ellipsis="…"),i=i.replace(/\s+$/,""),n=this.wordWidth(i+this.ellipsis);i&&n>this.lineWidth;)i=i.slice(0,-1).replace(/\s+$/,""),n=this.wordWidth(i+this.ellipsis);n<=this.lineWidth&&(i+=this.ellipsis),n=this.wordWidth(i)}return(o.required&&(r>this.spaceLeft&&(c(),i=e,n=r,a=1),this.emit("lastLine",t,this)),"\xad"==i[i.length-1]&&(i=i.slice(0,-1)+"-",this.spaceLeft-=this.wordWidth("-")),c(),nT(this.document.y+s)>this.maxY&&!this.nextSection())?(a=0,i="",!1):o.required?(this.spaceLeft=this.lineWidth,i="",n=0,a=0):(this.spaceLeft=this.lineWidth-r,i=e,n=r,a=1)}}),a>0&&(this.emit("lastLine",t,this),c()),this.emit("sectionEnd",t,this),!0===t.continued)?(o>1&&(this.continuedX=0),this.continuedX+=t.textWidth||0,this.document.y=s):this.document.x=this.startX}nextSection(e){if(this.emit("sectionEnd",e,this),++this.column>this.columns){if(null!=this.height)return!1;this.document.continueOnNewPage(),this.column=1,this.startY=this.document.page.margins.top,this.maxY=this.document.page.maxY(),this.document.x=this.startX,this.document._fillColor&&this.document.fillColor(...this.document._fillColor),this.emit("pageBreak",e,this)}else this.document.x+=this.lineWidth+this.columnGap,this.document.y=this.startY,this.emit("columnBreak",e,this);return this.emit("sectionStart",e,this),!0}}let{number:nx}=iq,nO={1:"DeviceGray",3:"DeviceRGB",4:"DeviceCMYK"};class nk{constructor(e,t){if(this.data=e,this.label=t,this.orientation=1,65496!==this.data.readUInt16BE(0))throw"SOI not found in JPEG";let r=F.A.decode(this.data);for(let e=0;e<r.length;e+=1){let t=r[e];"EXIF"===t.name&&t.entries.orientation&&(this.orientation=t.entries.orientation),"SOF"===t.name&&(this.bits||=t.precision,this.width||=t.width,this.height||=t.height,this.colorSpace||=nO[t.numberOfComponents])}this.obj=null}embed(e){if(!this.obj)return this.obj=e.ref({Type:"XObject",Subtype:"Image",BitsPerComponent:this.bits,Width:this.width,Height:this.height,ColorSpace:this.colorSpace,Filter:"DCTDecode"}),"DeviceCMYK"===this.colorSpace&&(this.obj.data.Decode=[1,0,1,0,1,0,1,0]),this.obj.end(this.data),this.data=null}}class nE{constructor(e,t){this.label=t,this.image=new L.A(e),this.width=this.image.width,this.height=this.image.height,this.imgData=this.image.imgData,this.obj=null}embed(e){let t=!1;if(this.document=e,this.obj)return;let r=this.image.hasAlphaChannel,i=1===this.image.interlaceMethod;if(this.obj=this.document.ref({Type:"XObject",Subtype:"Image",BitsPerComponent:r?8:this.image.bits,Width:this.width,Height:this.height,Filter:"FlateDecode"}),!r){let e=this.document.ref({Predictor:i?1:15,Colors:this.image.colors,BitsPerComponent:this.image.bits,Columns:this.width});this.obj.data.DecodeParms=e,e.end()}if(0===this.image.palette.length)this.obj.data.ColorSpace=this.image.colorSpace;else{let e=this.document.ref();e.end(X.from(this.image.palette)),this.obj.data.ColorSpace=["Indexed","DeviceRGB",this.image.palette.length/3-1,e]}if(null!=this.image.transparency.grayscale){let e=this.image.transparency.grayscale;this.obj.data.Mask=[e,e]}else if(this.image.transparency.rgb){let{rgb:e}=this.image.transparency,t=[];for(let r of e)t.push(r,r);this.obj.data.Mask=t}else if(this.image.transparency.indexed)return t=!0,this.loadIndexedAlphaChannel();else if(r)return t=!0,this.splitAlphaChannel();if(i&&!t)return this.decodeData();this.finalize()}finalize(){if(this.alphaChannel){let e=this.document.ref({Type:"XObject",Subtype:"Image",Height:this.height,Width:this.width,BitsPerComponent:8,Filter:"FlateDecode",ColorSpace:"DeviceGray",Decode:[0,1]});e.end(this.alphaChannel),this.obj.data.SMask=e}return this.obj.end(this.imgData),this.image=null,this.imgData=null}splitAlphaChannel(){return this.image.decodePixels(e=>{let t,r,i=this.image.colors,n=this.width*this.height,a=X.alloc(n*i),o=X.alloc(n),s=r=t=0,c=e.length,u=+(16===this.image.bits);for(;s<c;){for(let t=0;t<i;t++)a[r++]=e[s++],s+=u;o[t++]=e[s++],s+=u}return this.imgData=iM.deflateSync(a),this.alphaChannel=iM.deflateSync(o),this.finalize()})}loadIndexedAlphaChannel(){let e=this.image.transparency.indexed;return this.image.decodePixels(t=>{let r=X.alloc(this.width*this.height),i=0;for(let n=0,a=t.length;n<a;n++)r[i++]=e[t[n]];return this.alphaChannel=iM.deflateSync(r),this.finalize()})}decodeData(){this.image.decodePixels(e=>{this.imgData=iM.deflateSync(e),this.finalize()})}}class nR{static open(e,t){let r;if(X.isBuffer(e))r=e;else if(e instanceof ArrayBuffer)r=X.from(new Uint8Array(e));else{let t;if(t=/^data:.+?;base64,(.*)$/.exec(e))r=X.from(t[1],"base64");else if(!(r=nd.readFileSync(e)))return}if(255===r[0]&&216===r[1])return new nk(r,t);if(137===r[0]&&"PNG"===r.toString("ascii",1,4))return new nE(r,t);throw Error("Unknown image format.")}}class nC{constructor(e,t,r,i,n){void 0===n&&(n={expanded:!1}),this.document=e,this.options=n,this.outlineData={},null!==i&&(this.outlineData.Dest=[i.dictionary,"Fit"]),null!==t&&(this.outlineData.Parent=t),null!==r&&(this.outlineData.Title=new String(r)),this.dictionary=this.document.ref(this.outlineData),this.children=[]}addItem(e,t){void 0===t&&(t={expanded:!1});let r=new nC(this.document,this.dictionary,e,this.document.page,t);return this.children.push(r),r}endOutline(){if(this.children.length>0){this.options.expanded&&(this.outlineData.Count=this.children.length);let e=this.children[0],t=this.children[this.children.length-1];this.outlineData.First=e.dictionary,this.outlineData.Last=t.dictionary;for(let e=0,t=this.children.length;e<t;e++){let t=this.children[e];e>0&&(t.outlineData.Prev=this.children[e-1].dictionary),e<this.children.length-1&&(t.outlineData.Next=this.children[e+1].dictionary),t.endOutline()}}return this.dictionary.end()}}class nU{constructor(e,t){this.refs=[{pageRef:e,mcid:t}]}push(e){e.refs.forEach(e=>this.refs.push(e))}}class nD{constructor(e,t,r,i){void 0===r&&(r={}),void 0===i&&(i=null),this.document=e,this._attached=!1,this._ended=!1,this._flushed=!1,this.dictionary=e.ref({S:t});let n=this.dictionary.data;(Array.isArray(r)||this._isValidChild(r))&&(i=r,r={}),void 0!==r.title&&(n.T=new String(r.title)),void 0!==r.lang&&(n.Lang=new String(r.lang)),void 0!==r.alt&&(n.Alt=new String(r.alt)),void 0!==r.expanded&&(n.E=new String(r.expanded)),void 0!==r.actual&&(n.ActualText=new String(r.actual)),this._children=[],i&&(Array.isArray(i)||(i=[i]),i.forEach(e=>this.add(e)),this.end())}add(e){if(this._ended)throw Error("Cannot add child to already-ended structure element");if(!this._isValidChild(e))throw Error("Invalid structure element child");return e instanceof nD&&(e.setParent(this.dictionary),this._attached&&e.setAttached()),e instanceof nU&&this._addContentToParentTree(e),"function"==typeof e&&this._attached&&(e=this._contentForClosure(e)),this._children.push(e),this}_addContentToParentTree(e){e.refs.forEach(e=>{let{pageRef:t,mcid:r}=e;this.document.getStructParentTree().get(t.data.StructParents)[r]=this.dictionary})}setParent(e){if(this.dictionary.data.P)throw Error("Structure element added to more than one parent");this.dictionary.data.P=e,this._flush()}setAttached(){this._attached||(this._children.forEach((e,t)=>{e instanceof nD&&e.setAttached(),"function"==typeof e&&(this._children[t]=this._contentForClosure(e))}),this._attached=!0,this._flush())}end(){this._ended||(this._children.filter(e=>e instanceof nD).forEach(e=>e.end()),this._ended=!0,this._flush())}_isValidChild(e){return e instanceof nD||e instanceof nU||"function"==typeof e}_contentForClosure(e){let t=this.document.markStructureContent(this.dictionary.data.S);return e(),this.document.endMarkedContent(),this._addContentToParentTree(t),t}_isFlushable(){return!!this.dictionary.data.P&&!!this._ended&&this._children.every(e=>"function"!=typeof e&&(!(e instanceof nD)||e._isFlushable()))}_flush(){!this._flushed&&this._isFlushable()&&(this.dictionary.data.K=[],this._children.forEach(e=>this._flushChild(e)),this.dictionary.end(),this._children=[],this.dictionary.data.K=null,this._flushed=!0)}_flushChild(e){e instanceof nD&&this.dictionary.data.K.push(e.dictionary),e instanceof nU&&e.refs.forEach(e=>{let{pageRef:t,mcid:r}=e;this.dictionary.data.Pg||(this.dictionary.data.Pg=t),this.dictionary.data.Pg===t?this.dictionary.data.K.push(r):this.dictionary.data.K.push({Type:"MCR",Pg:t,MCID:r})})}}class nP extends iI{_compareKeys(e,t){return parseInt(e)-parseInt(t)}_keysName(){return"Nums"}_dataForKey(e){return parseInt(e)}}let nF={readOnly:1,required:2,noExport:4,multiline:4096,password:8192,toggleToOffButton:16384,radioButton:32768,pushButton:65536,combo:131072,edit:262144,sort:524288,multiSelect:2097152,noSpell:4194304},nL={left:0,center:1,right:2},nM={value:"V",defaultValue:"DV"},nY={zip:"0",zipPlus4:"1",zip4:"1",phone:"2",ssn:"3"},nI={number:{nDec:0,sepComma:!1,negStyle:"MinusBlack",currency:"",currencyPrepend:!0},percent:{nDec:0,sepComma:!1}};var nN={initPDFA(e){"-"===e.charAt(e.length-3)?(this.subset_conformance=e.charAt(e.length-1).toUpperCase(),this.subset=parseInt(e.charAt(e.length-2))):(this.subset_conformance="B",this.subset=parseInt(e.charAt(e.length-1)))},endSubset(){this._addPdfaMetadata(),this._addColorOutputIntent()},_addColorOutputIntent(){let e=nd.readFileSync("/home/<USER>/work/react-pdf/react-pdf/packages/pdfkit/src/mixins/data/sRGB_IEC61966_2_1.icc"),t=this.ref({Length:e.length,N:3});t.write(e),t.end();let r=this.ref({Type:"OutputIntent",S:"GTS_PDFA1",Info:new String("sRGB IEC61966-2.1"),OutputConditionIdentifier:new String("sRGB IEC61966-2.1"),DestOutputProfile:t});r.end(),this._root.data.OutputIntents=[r]},_getPdfaid(){return`
        <rdf:Description xmlns:pdfaid="http://www.aiim.org/pdfa/ns/id/" rdf:about="">
            <pdfaid:part>${this.subset}</pdfaid:part>
            <pdfaid:conformance>${this.subset_conformance}</pdfaid:conformance>
        </rdf:Description>
        `},_addPdfaMetadata(){this.appendXML(this._getPdfaid())}},nB={initPDFUA(){this.subset=1},endSubset(){this._addPdfuaMetadata()},_addPdfuaMetadata(){this.appendXML(this._getPdfuaid())},_getPdfuaid(){return`
        <rdf:Description xmlns:pdfuaid="http://www.aiim.org/pdfua/ns/id/" rdf:about="">
            <pdfuaid:part>${this.subset}</pdfuaid:part>
        </rdf:Description>
        `}};class nW{constructor(){this._metadata=`
        <?xpacket begin="\ufeff" id="W5M0MpCehiHzreSzNTczkc9d"?>
            <x:xmpmeta xmlns:x="adobe:ns:meta/">
                <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
        `}_closeTags(){this._metadata=this._metadata.concat(`
                </rdf:RDF>
            </x:xmpmeta>
        <?xpacket end="w"?>
        `)}append(e,t){void 0===t&&(t=!0),this._metadata=this._metadata.concat(e),t&&(this._metadata=this._metadata.concat("\n"))}getXML(){return this._metadata}getLength(){return this._metadata.length}end(){this._closeTags(),this._metadata=this._metadata.trim()}}class nz extends ie.Readable{constructor(e){switch(void 0===e&&(e={}),super(e),this.options=e,e.pdfVersion){case"1.4":this.version=1.4;break;case"1.5":this.version=1.5;break;case"1.6":this.version=1.6;break;case"1.7":case"1.7ext3":this.version=1.7;break;default:this.version=1.3}this.compress=null==this.options.compress||this.options.compress,this._pageBuffer=[],this._pageBufferStart=0,this._offsets=[],this._waiting=0,this._ended=!1,this._offset=0;let t=this.ref({Type:"Pages",Count:0,Kids:[]}),r=this.ref({Dests:new iN});if(this._root=this.ref({Type:"Catalog",Pages:t,Names:r}),this.options.lang&&(this._root.data.Lang=new String(this.options.lang)),this.page=null,this.initMetadata(),this.initColor(),this.initVector(),this.initFonts(e.font),this.initText(),this.initImages(),this.initOutline(),this.initMarkings(e),this.initSubset(e),this.info={Producer:"PDFKit",Creator:"PDFKit",CreationDate:new Date},this.options.info)for(let e in this.options.info){let t=this.options.info[e];this.info[e]=t}this.options.displayTitle&&(this._root.data.ViewerPreferences=this.ref({DisplayDocTitle:!0})),this._id=i$.generateFileID(this.info),this._write(`%PDF-${this.version}`),this._write("%\xff\xff\xff\xff"),!1!==this.options.autoFirstPage&&this.addPage()}addPage(e){null==e&&({options:e}=this),this.options.bufferPages||this.flushPages(),this.page=new iG(this,e),this._pageBuffer.push(this.page);let t=this._root.data.Pages.data;return t.Kids.push(this.page.dictionary),t.Count++,this.x=this.page.margins.left,this.y=this.page.margins.top,this._ctm=[1,0,0,1,0,0],this.transform(1,0,0,-1,0,this.page.height),this.emit("pageAdded"),this}continueOnNewPage(e){let t=this.endPageMarkings(this.page);return this.addPage(null!=e?e:this.page._options),this.initPageMarkings(t),this}bufferedPageRange(){return{start:this._pageBufferStart,count:this._pageBuffer.length}}switchToPage(e){let t;if(!(t=this._pageBuffer[e-this._pageBufferStart]))throw Error(`switchToPage(${e}) out of bounds, current buffer covers pages ${this._pageBufferStart} to ${this._pageBufferStart+this._pageBuffer.length-1}`);return this.page=t}flushPages(){let e=this._pageBuffer;for(let t of(this._pageBuffer=[],this._pageBufferStart+=e.length,e))this.endPageMarkings(t),t.end()}addNamedDestination(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];0===r.length&&(r=["XYZ",null,null,null]),"XYZ"===r[0]&&null!==r[2]&&(r[2]=this.page.height-r[2]),r.unshift(this.page.dictionary),this._root.data.Names.data.Dests.add(e,r)}addNamedEmbeddedFile(e,t){this._root.data.Names.data.EmbeddedFiles||(this._root.data.Names.data.EmbeddedFiles=new iN({limits:!1})),this._root.data.Names.data.EmbeddedFiles.add(e,t)}addNamedJavaScript(e,t){this._root.data.Names.data.JavaScript||(this._root.data.Names.data.JavaScript=new iN);let r={JS:new String(t),S:"JavaScript"};this._root.data.Names.data.JavaScript.add(e,r)}ref(e){let t=new iY(this,this._offsets.length+1,e);return this._offsets.push(null),this._waiting++,t}_read(){}_write(e){return X.isBuffer(e)||(e=X.from(e+"\n","binary")),this.push(e),this._offset+=e.length}addContent(e){return this.page.write(e),this}_refEnd(e){if(this._offsets[e.id-1]=e.offset,0==--this._waiting&&this._ended)return this._finalize(),this._ended=!1}end(){for(let e in this.flushPages(),this._info=this.ref(),this.info){let t=this.info[e];"string"==typeof t&&(t=new String(t));let r=this.ref(t);r.end(),this._info.data[e]=r}for(let e in this._info.end(),this._fontFamilies)this._fontFamilies[e].finalize();return(this.endOutline(),this.endMarkings(),this.subset&&this.endSubset(),this.endMetadata(),this._root.end(),this._root.data.Pages.end(),this._root.data.Names.end(),this.endAcroForm(),this._root.data.ViewerPreferences&&this._root.data.ViewerPreferences.end(),this._security&&this._security.end(),0===this._waiting)?this._finalize():this._ended=!0}_finalize(){let e=this._offset;for(let e of(this._write("xref"),this._write(`0 ${this._offsets.length+1}`),this._write("0000000000 65535 f "),this._offsets))e=`0000000000${e}`.slice(-10),this._write(e+" 00000 n ");let t={Size:this._offsets.length+1,Root:this._root,Info:this._info,ID:[this._id,this._id]};return this._security&&(t.Encrypt=this._security.dictionary),this._write("trailer"),this._write(iq.convert(t)),this._write("startxref"),this._write(`${e}`),this._write("%%EOF"),this.push(null)}toString(){return"[object PDFDocument]"}}let nj=e=>{Object.assign(nz.prototype,e)};nj({initMetadata(){this.metadata=new nW},appendXML(e,t){void 0===t&&(t=!0),this.metadata.append(e,t)},_addInfo(){this.appendXML(`
        <rdf:Description rdf:about="" xmlns:xmp="http://ns.adobe.com/xap/1.0/">
            <xmp:CreateDate>${this.info.CreationDate.toISOString().split(".")[0]+"Z"}</xmp:CreateDate>
            <xmp:CreatorTool>${this.info.Creator}</xmp:CreatorTool>
        </rdf:Description>
        `),(this.info.Title||this.info.Author||this.info.Subject)&&(this.appendXML(`
            <rdf:Description rdf:about="" xmlns:dc="http://purl.org/dc/elements/1.1/">
            `),this.info.Title&&this.appendXML(`
                <dc:title>
                    <rdf:Alt>
                        <rdf:li xml:lang="x-default">${this.info.Title}</rdf:li>
                    </rdf:Alt>
                </dc:title>
                `),this.info.Author&&this.appendXML(`
                <dc:creator>
                    <rdf:Seq>
                        <rdf:li>${this.info.Author}</rdf:li>
                    </rdf:Seq>
                </dc:creator>
                `),this.info.Subject&&this.appendXML(`
                <dc:description>
                    <rdf:Alt>
                        <rdf:li xml:lang="x-default">${this.info.Subject}</rdf:li>
                    </rdf:Alt>
                </dc:description>
                `),this.appendXML(`
            </rdf:Description>
            `)),this.appendXML(`
        <rdf:Description rdf:about="" xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
            <pdf:Producer>${this.info.Creator}</pdf:Producer>`,!1),this.info.Keywords&&this.appendXML(`
            <pdf:Keywords>${this.info.Keywords}</pdf:Keywords>`,!1),this.appendXML(`
        </rdf:Description>
        `)},endMetadata(){this._addInfo(),this.metadata.end(),1.3!=this.version&&(this.metadataRef=this.ref({length:this.metadata.getLength(),Type:"Metadata",Subtype:"XML"}),this.metadataRef.compress=!1,this.metadataRef.write(X.from(this.metadata.getXML(),"utf-8")),this.metadataRef.end(),this._root.data.Metadata=this.metadataRef)}}),nj({initColor(){return this.spotColors={},this._opacityRegistry={},this._opacityCount=0,this._patternCount=0,this._gradCount=0},_normalizeColor(e){if("string"==typeof e){if("#"===e.charAt(0)){4===e.length&&(e=e.replace(/#([0-9A-F])([0-9A-F])([0-9A-F])/i,"#$1$1$2$2$3$3"));let t=parseInt(e.slice(1),16);e=[t>>16,t>>8&255,255&t]}else if(i7[e])e=i7[e];else if(this.spotColors[e])return this.spotColors[e]}return Array.isArray(e)?(3===e.length?e=e.map(e=>e/255):4===e.length&&(e=e.map(e=>e/100)),e):null},_setColor(e,t){return e instanceof i2?(e.apply(t),!0):Array.isArray(e)&&e[0]instanceof i6?(e[0].apply(t,e[1]),!0):this._setColorCore(e,t)},_setColorCore(e,t){if(!(e=this._normalizeColor(e)))return!1;let r=t?"SCN":"scn",i=this._getColorSpace(e);return this._setColorSpace(i,t),e instanceof i1?(this.page.colorSpaces[e.id]=e.ref,this.addContent(`1 ${r}`)):this.addContent(`${e.join(" ")} ${r}`),!0},_setColorSpace(e,t){return this.addContent(`/${e} ${t?"CS":"cs"}`)},_getColorSpace:e=>e instanceof i1?e.id:4===e.length?"DeviceCMYK":"DeviceRGB",fillColor(e,t){return this._setColor(e,!1)&&this.fillOpacity(t),this._fillColor=[e,t],this},strokeColor(e,t){return this._setColor(e,!0)&&this.strokeOpacity(t),this},opacity(e){return this._doOpacity(e,e),this},fillOpacity(e){return this._doOpacity(e,null),this},strokeOpacity(e){return this._doOpacity(null,e),this},_doOpacity(e,t){let r,i;if(null==e&&null==t)return;null!=e&&(e=Math.max(0,Math.min(1,e))),null!=t&&(t=Math.max(0,Math.min(1,t)));let n=`${e}_${t}`;if(this._opacityRegistry[n])[r,i]=this._opacityRegistry[n];else{r={Type:"ExtGState"},null!=e&&(r.ca=e),null!=t&&(r.CA=t),(r=this.ref(r)).end();let a=++this._opacityCount;i=`Gs${a}`,this._opacityRegistry[n]=[r,i]}return this.page.ext_gstates[i]=r,this.addContent(`/${i} gs`)},linearGradient(e,t,r,i){return new i3(this,e,t,r,i)},radialGradient(e,t,r,i,n,a){return new i4(this,e,t,r,i,n,a)},pattern(e,t,r,i){return new i6(this,e,t,r,i)},addSpotColor(e,t,r,i,n){let a=new i1(this,e,t,r,i,n);return this.spotColors[e]=a,this}}),nj({initVector(){return this._ctm=[1,0,0,1,0,0],this._ctmStack=[]},save(){return this._ctmStack.push(this._ctm.slice()),this.addContent("q")},restore(){return this._ctm=this._ctmStack.pop()||[1,0,0,1,0,0],this.addContent("Q")},closePath(){return this.addContent("h")},lineWidth(e){return this.addContent(`${nl(e)} w`)},_CAP_STYLES:{BUTT:0,ROUND:1,SQUARE:2},lineCap(e){return"string"==typeof e&&(e=this._CAP_STYLES[e.toUpperCase()]),this.addContent(`${e} J`)},_JOIN_STYLES:{MITER:0,ROUND:1,BEVEL:2},lineJoin(e){return"string"==typeof e&&(e=this._JOIN_STYLES[e.toUpperCase()]),this.addContent(`${e} j`)},miterLimit(e){return this.addContent(`${nl(e)} M`)},dash(e,t){void 0===t&&(t={});let r=e;if(Array.isArray(e)||(e=[e,t.space||e]),!e.every(e=>Number.isFinite(e)&&e>0))throw Error(`dash(${JSON.stringify(r)}, ${JSON.stringify(t)}) invalid, lengths must be numeric and greater than zero`);return e=e.map(nl).join(" "),this.addContent(`[${e}] ${nl(t.phase||0)} d`)},undash(){return this.addContent("[] 0 d")},moveTo(e,t){return this.addContent(`${nl(e)} ${nl(t)} m`)},lineTo(e,t){return this.addContent(`${nl(e)} ${nl(t)} l`)},bezierCurveTo(e,t,r,i,n,a){return this.addContent(`${nl(e)} ${nl(t)} ${nl(r)} ${nl(i)} ${nl(n)} ${nl(a)} c`)},quadraticCurveTo(e,t,r,i){return this.addContent(`${nl(e)} ${nl(t)} ${nl(r)} ${nl(i)} v`)},rect(e,t,r,i){return this.addContent(`${nl(e)} ${nl(t)} ${nl(r)} ${nl(i)} re`)},roundedRect(e,t,r,i,n){null==n&&(n=0);let a=(n=Math.min(n,.5*r,.5*i))*(1-nh);return this.moveTo(e+n,t),this.lineTo(e+r-n,t),this.bezierCurveTo(e+r-a,t,e+r,t+a,e+r,t+n),this.lineTo(e+r,t+i-n),this.bezierCurveTo(e+r,t+i-a,e+r-a,t+i,e+r-n,t+i),this.lineTo(e+n,t+i),this.bezierCurveTo(e+a,t+i,e,t+i-a,e,t+i-n),this.lineTo(e,t+n),this.bezierCurveTo(e,t+a,e+a,t,e+n,t),this.closePath()},ellipse(e,t,r,i){null==i&&(i=r),e-=r,t-=i;let n=r*nh,a=i*nh,o=e+2*r,s=t+2*i,c=e+r,u=t+i;return this.moveTo(e,u),this.bezierCurveTo(e,u-a,c-n,t,c,t),this.bezierCurveTo(c+n,t,o,u-a,o,u),this.bezierCurveTo(o,u+a,c+n,s,c,s),this.bezierCurveTo(c-n,s,e,u+a,e,u),this.closePath()},circle(e,t,r){return this.ellipse(e,t,r)},arc(e,t,r,i,n,a){null==a&&(a=!1);let o=2*Math.PI,s=.5*Math.PI,c=n-i;Math.abs(c)>o?c=o:0!==c&&a!==c<0&&(c=(a?-1:1)*o+c);let u=Math.ceil(Math.abs(c)/s),l=c/u,h=l/s*nh*r,d=i,f=-Math.sin(d)*h,m=Math.cos(d)*h,g=e+Math.cos(d)*r,p=t+Math.sin(d)*r;this.moveTo(g,p);for(let i=0;i<u;i++){let i=g+f,n=p+m;d+=l,g=e+Math.cos(d)*r,p=t+Math.sin(d)*r;let a=g-(f=-Math.sin(d)*h),o=p-(m=Math.cos(d)*h);this.bezierCurveTo(i,n,a,o,g,p)}return this},polygon(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];for(let e of(this.moveTo(...t.shift()||[]),t))this.lineTo(...e||[]);return this.closePath()},path(e){return nu.apply(this,e),this},_windingRule:e=>/even-?odd/.test(e)?"*":"",fill(e,t){return/(even-?odd)|(non-?zero)/.test(e)&&(t=e,e=null),e&&this.fillColor(e),this.addContent(`f${this._windingRule(t)}`)},stroke(e){return e&&this.strokeColor(e),this.addContent("S")},fillAndStroke(e,t,r){null==t&&(t=e);let i=/(even-?odd)|(non-?zero)/;return i.test(e)&&(r=e,e=null),i.test(t)&&(r=t,t=e),e&&(this.fillColor(e),this.strokeColor(t)),this.addContent(`B${this._windingRule(r)}`)},clip(e){return this.addContent(`W${this._windingRule(e)} n`)},transform(e,t,r,i,n,a){if(1===e&&0===t&&0===r&&1===i&&0===n&&0===a)return this;let o=this._ctm,[s,c,u,l,h,d]=o;o[0]=s*e+u*t,o[1]=c*e+l*t,o[2]=s*r+u*i,o[3]=c*r+l*i,o[4]=s*n+u*a+h,o[5]=c*n+l*a+d;let f=[e,t,r,i,n,a].map(e=>nl(e)).join(" ");return this.addContent(`${f} cm`)},translate(e,t){return this.transform(1,0,0,1,e,t)},rotate(e,t){let r;void 0===t&&(t={});let i=e*Math.PI/180,n=Math.cos(i),a=Math.sin(i),o=r=0;if(null!=t.origin){[o,r]=t.origin;let e=o*n-r*a,i=o*a+r*n;o-=e,r-=i}return this.transform(n,a,-a,n,o,r)},scale(e,t,r){let i;void 0===r&&(r={}),null==t&&(t=e),"object"==typeof t&&(r=t,t=e);let n=i=0;return null!=r.origin&&([n,i]=r.origin,n-=e*n,i-=t*i),this.transform(e,0,0,t,n,i)}}),nj({initFonts(){return this._fontFamilies={},this._fontCount=0,this._fontSize=12,this._font=null,this._registeredFonts={},this.font("Helvetica")},font(e,t,r){let i,n;if("number"==typeof t&&(r=t,t=null),"string"==typeof e&&this._registeredFonts[e]?(i=e,{src:e,family:t}=this._registeredFonts[e]):"string"!=typeof(i=t||e)&&(i=null),null!=r&&this.fontSize(r),n=this._fontFamilies[i])return this._font=n,this;let a=`F${++this._fontCount}`;return(this._font=nb.open(this,e,t,a),n=this._fontFamilies[this._font.name])?this._font=n:(i&&(this._fontFamilies[i]=this._font),this._font.name&&(this._fontFamilies[this._font.name]=this._font)),this},fontSize(e){return this._fontSize=e,this},currentLineHeight(e){return null==e&&(e=!1),this._font.lineHeight(this._fontSize,e)},registerFont(e,t,r){return this._registeredFonts[e]={src:t,family:r},this}}),nj({initText(){return this._line=this._line.bind(this),this.x=0,this.y=0,this._lineGap=0},lineGap(e){return this._lineGap=e,this},moveDown(e){return null==e&&(e=1),this.y+=this.currentLineHeight(!0)*e+this._lineGap,this},moveUp(e){return null==e&&(e=1),this.y-=this.currentLineHeight(!0)*e+this._lineGap,this},_text(e,t,r,i,n){i=this._initOptions(t,r,i),e=null==e?"":`${e}`,i.wordSpacing&&(e=e.replace(/\s{2,}/g," "));let a=()=>{i.structParent&&i.structParent.add(this.struct(i.structType||"P",[this.markStructureContent(i.structType||"P")]))};if(0!==i.rotation&&(this.save(),this.rotate(-i.rotation,{origin:[this.x,this.y]})),i.width){let t=this._wrapper;t||((t=new nS(this,i)).on("line",n),t.on("firstLine",a)),this._wrapper=i.continued?t:null,this._textOptions=i.continued?i:null,t.wrap(e,i)}else for(let t of e.split("\n"))a(),n(t,i);return 0!==i.rotation&&this.restore(),this},text(e,t,r,i){return this._text(e,t,r,i,this._line)},widthOfString(e,t){void 0===t&&(t={});let r=t.horizontalScaling||100;return(this._font.widthOfString(e,this._fontSize,t.features)+(t.characterSpacing||0)*(e.length-1))*r/100},boundsOfString(e,t,r,i){},heightOfString(e,t){let{x:r,y:i}=this;(t=this._initOptions(t)).height=1/0;let n=t.lineGap||this._lineGap||0;this._text(e,this.x,this.y,t,()=>this.y+=this.currentLineHeight(!0)+n);let a=this.y-i;return this.x=r,this.y=i,a},list(e,t,r,i,n){let a=(i=this._initOptions(t,r,i)).listType||"bullet",o=Math.round(this._font.ascender/1e3*this._fontSize),s=o/2,c=i.bulletRadius||o/3,u=i.textIndent||("bullet"===a?5*c:2*o),l=i.bulletIndent||("bullet"===a?8*c:2*o),h=1,d=[],f=[],m=[];var g=function(e){let t=1;for(let r=0;r<e.length;r++){let i=e[r];Array.isArray(i)?(h++,g(i),h--):(d.push(i),f.push(h),"bullet"!==a&&m.push(t++))}};g(e);let p=function(e){switch(a){case"numbered":return`${e}.`;case"lettered":var t=String.fromCharCode((e-1)%26+65),r=Array(Math.floor((e-1)/26+1)+1).join(t);return`${r}.`}},y=function(e,t){(n=new nS(this,i)).on("line",this._line),h=1,n.once("firstLine",()=>{let e,r,o,d,g;if(i.structParent&&(i.structTypes?[r,o,d]=i.structTypes:[r,o,d]=["LI","Lbl","LBody"]),r?(e=this.struct(r),i.structParent.add(e)):i.structParent&&(e=i.structParent),(g=f[t++])!==h){let e=l*(g-h);this.x+=e,n.lineWidth-=e,h=g}switch(e&&(o||d)&&e.add(this.struct(o||d,[this.markStructureContent(o||d)])),a){case"bullet":this.circle(this.x-u+c,this.y+s,c),this.fill();break;case"numbered":case"lettered":var y=p(m[t-1]);this._fragment(y,this.x-u,this.y,i)}e&&o&&d&&e.add(this.struct(d,[this.markStructureContent(d)])),e&&e!==i.structParent&&e.end()}),n.on("sectionStart",()=>{let e=u+l*(h-1);return this.x+=e,n.lineWidth-=e}),n.on("sectionEnd",()=>{let e=u+l*(h-1);return this.x-=e,n.lineWidth+=e}),n.wrap(e,i)};for(let e=0;e<d.length;e++)y.call(this,d[e],e);return this},_initOptions(e,t,r){var i;void 0===e&&(e={}),void 0===r&&(r={}),"object"==typeof e&&(r=e,e=null);let n=Object.assign({},r);if(this._textOptions)for(let e in this._textOptions){let t=this._textOptions[e];"continued"!==e&&void 0===n[e]&&(n[e]=t)}return null!=e&&(this.x=e),null!=t&&(this.y=t),!1!==n.lineBreak&&(null==n.width&&(n.width=this.page.width-this.x-this.page.margins.right),n.width=Math.max(n.width,0)),n.columns||(n.columns=0),null==n.columnGap&&(n.columnGap=18),n.rotation=Number(null!=(i=r.rotation)?i:0)%360,n.rotation<0&&(n.rotation+=360),n},_line(e,t,r){void 0===t&&(t={}),this._fragment(e,this.x,this.y,t);let i=t.lineGap||this._lineGap||0;return r?this.y+=this.currentLineHeight(!0)+i:this.x+=this.widthOfString(e,t)},_fragment(e,t,r,i){let n,a,o,s,c,u;if(0===(e=`${e}`.replace(/\n/g,"")).length)return;let l=i.align||"left",h=i.wordSpacing||0,d=i.characterSpacing||0,f=i.horizontalScaling||100;if(i.width)switch(l){case"right":c=this.widthOfString(e.replace(/\s+$/,""),i),t+=i.lineWidth-c;break;case"center":t+=i.lineWidth/2-i.textWidth/2;break;case"justify":u=e.trim().split(/\s+/),c=this.widthOfString(e.replace(/\s+/g,""),i);var m=this.widthOfString(" ")+d;h=Math.max(0,(i.lineWidth-c)/Math.max(1,u.length-1)-m)}if("number"==typeof i.baseline)n=-i.baseline;else{switch(i.baseline){case"svg-middle":n=.5*this._font.xHeight;break;case"middle":case"svg-central":n=.5*(this._font.descender+this._font.ascender);break;case"bottom":case"ideographic":n=this._font.descender;break;case"alphabetic":n=0;break;case"mathematical":n=.5*this._font.ascender;break;case"hanging":n=.8*this._font.ascender;break;default:n=this._font.ascender}n=n/1e3*this._fontSize}let g=i.textWidth+h*(i.wordCount-1)+d*(e.length-1);if(null!=i.link&&this.link(t,r,g,this.currentLineHeight(),i.link),null!=i.goTo&&this.goTo(t,r,g,this.currentLineHeight(),i.goTo),null!=i.destination&&this.addNamedDestination(i.destination,"XYZ",t,r,null),i.underline){this.save(),i.stroke||this.strokeColor(...this._fillColor||[]);let e=this._fontSize<10?.5:Math.floor(this._fontSize/10);this.lineWidth(e);let n=r+this.currentLineHeight()-e;this.moveTo(t,n),this.lineTo(t+g,n),this.stroke(),this.restore()}if(i.strike){this.save(),i.stroke||this.strokeColor(...this._fillColor||[]);let e=this._fontSize<10?.5:Math.floor(this._fontSize/10);this.lineWidth(e);let n=r+this.currentLineHeight()/2;this.moveTo(t,n),this.lineTo(t+g,n),this.stroke(),this.restore()}if(this.save(),i.oblique){let e;e="number"==typeof i.oblique?-Math.tan(i.oblique*Math.PI/180):-.25,this.transform(1,0,0,1,t,r),this.transform(1,0,e,1,-e*n,0),this.transform(1,0,0,1,-t,-r)}this.transform(1,0,0,-1,0,this.page.height),r=this.page.height-r-n,null==this.page.fonts[this._font.id]&&(this.page.fonts[this._font.id]=this._font.ref()),this.addContent("BT"),this.addContent(`1 0 0 1 ${nx(t)} ${nx(r)} Tm`),this.addContent(`/${this._font.id} ${nx(this._fontSize)} Tf`);let p=i.fill&&i.stroke?2:+!!i.stroke;if(p&&this.addContent(`${p} Tr`),d&&this.addContent(`${nx(d)} Tc`),100!==f&&this.addContent(`${f} Tz`),h)for(let t of(u=e.trim().split(/\s+/),h+=this.widthOfString(" ")+d,h*=1e3/this._fontSize,a=[],s=[],u)){let[e,r]=this._font.encode(t,i.features);a=a.concat(e);let n={},o=(s=s.concat(r))[s.length-1];for(let e in o){let t=o[e];n[e]=t}n.xAdvance+=h,s[s.length-1]=n}else[a,s]=this._font.encode(e,i.features);let y=this._fontSize/1e3,v=[],A=0,b=!1,_=e=>{if(A<e){let t=a.slice(A,e).join(""),r=s[e-1].xAdvance-s[e-1].advanceWidth;v.push(`<${t}> ${nx(-r)}`)}return A=e},w=e=>{if(_(e),v.length>0)return this.addContent(`[${v.join(" ")}] TJ`),v.length=0};for(o=0;o<s.length;o++){let e=s[o];e.xOffset||e.yOffset?(w(o),this.addContent(`1 0 0 1 ${nx(t+e.xOffset*y)} ${nx(r+e.yOffset*y)} Tm`),w(o+1),b=!0):(b&&(this.addContent(`1 0 0 1 ${nx(t)} ${nx(r)} Tm`),b=!1),e.xAdvance-e.advanceWidth!=0&&_(o+1)),t+=e.xAdvance*y}return w(o),this.addContent("ET"),this.restore()}}),nj({initImages(){return this._imageRegistry={},this._imageCount=0},image(e,t,r,i){let n,a,o,s,c,u,l,h,d,f;void 0===i&&(i={}),"object"==typeof t&&(i=t,t=null);let m=i.ignoreOrientation||!1!==i.ignoreOrientation&&this.options.ignoreOrientation;t=null!=(u=null!=t?t:i.x)?u:this.x,r=null!=(l=null!=r?r:i.y)?l:this.y,"string"==typeof e&&(s=this._imageRegistry[e]),s||(s=e.width&&e.height?e:this.openImage(e)),s.obj||s.embed(this),null==this.page.xobjects[s.label]&&(this.page.xobjects[s.label]=s.obj);let{width:g,height:p}=s;!m&&s.orientation>4&&([g,p]=[p,g]);let y=i.width||g,v=i.height||p;if(i.width&&!i.height){let e=y/g;y=g*e,v=p*e}else if(i.height&&!i.width){let e=v/p;y=g*e,v=p*e}else i.scale?(y=g*i.scale,v=p*i.scale):i.fit?([o,n]=i.fit,a=o/n,(c=g/p)>a?(y=o,v=o/c):(v=n,y=n*c)):i.cover&&([o,n]=i.cover,a=o/n,(c=g/p)>a?(v=n,y=n*c):(y=o,v=o/c));if((i.fit||i.cover)&&("center"===i.align?t=t+o/2-y/2:"right"===i.align&&(t=t+o-y),"center"===i.valign?r=r+n/2-v/2:"bottom"===i.valign&&(r=r+n-v)),m)r-=v=-v,h=0;else switch(s.orientation){default:case 1:r-=v=-v,h=0;break;case 2:v=-v,t-=y=-y,r-=v,h=0;break;case 3:d=t,f=r,v=-v,t-=y,h=180;break;case 4:break;case 5:d=t,f=r,[y,v]=[v,y],r-=v,h=90;break;case 6:d=t,f=r,[y,v]=[v,y],v=-v,h=90;break;case 7:d=t,f=r,[y,v]=[v,y],v=-v,t-=y=-y,h=90;break;case 8:d=t,f=r,[y,v]=[v,y],v=-v,t-=y,r-=v,h=-90}return null!=i.link&&this.link(t,r,y,v,i.link),null!=i.goTo&&this.goTo(t,r,y,v,i.goTo),null!=i.destination&&this.addNamedDestination(i.destination,"XYZ",t,r,null),this.y===r&&(this.y+=v),this.save(),h&&this.rotate(h,{origin:[d,f]}),this.transform(y,0,0,v,t,r),this.addContent(`/${s.label} Do`),this.restore(),this},openImage(e){let t;return"string"==typeof e&&(t=this._imageRegistry[e]),t||(t=nR.open(e,`I${++this._imageCount}`),"string"==typeof e&&(this._imageRegistry[e]=t)),t}}),nj({annotate(e,t,r,i,n){for(let a in n.Type="Annot",n.Rect=this._convertRect(e,t,r,i),n.Border=[0,0,0],"Link"===n.Subtype&&void 0===n.F&&(n.F=4),"Link"!==n.Subtype&&null==n.C&&(n.C=this._normalizeColor(n.color||[0,0,0])),delete n.color,"string"==typeof n.Dest&&(n.Dest=new String(n.Dest)),n){let e=n[a];n[a[0].toUpperCase()+a.slice(1)]=e}let a=this.ref(n);return this.page.annotations.push(a),a.end(),this},note(e,t,r,i,n,a){return void 0===a&&(a={}),a.Subtype="Text",a.Contents=new String(n),null==a.Name&&(a.Name="Comment"),null==a.color&&(a.color=[243,223,92]),this.annotate(e,t,r,i,a)},goTo(e,t,r,i,n,a){return void 0===a&&(a={}),a.Subtype="Link",a.A=this.ref({S:"GoTo",D:new String(n)}),a.A.end(),this.annotate(e,t,r,i,a)},link(e,t,r,i,n,a){if(void 0===a&&(a={}),a.Subtype="Link","number"==typeof n){let e=this._root.data.Pages.data;if(n>=0&&n<e.Kids.length)a.A=this.ref({S:"GoTo",D:[e.Kids[n],"XYZ",null,null,null]}),a.A.end();else throw Error(`The document has no page ${n}`)}else a.A=this.ref({S:"URI",URI:new String(n)}),a.A.end();return this.annotate(e,t,r,i,a)},_markup(e,t,r,i,n){void 0===n&&(n={});let[a,o,s,c]=this._convertRect(e,t,r,i);return n.QuadPoints=[a,c,s,c,a,o,s,o],n.Contents=new String,this.annotate(e,t,r,i,n)},highlight(e,t,r,i,n){return void 0===n&&(n={}),n.Subtype="Highlight",null==n.color&&(n.color=[241,238,148]),this._markup(e,t,r,i,n)},underline(e,t,r,i,n){return void 0===n&&(n={}),n.Subtype="Underline",this._markup(e,t,r,i,n)},strike(e,t,r,i,n){return void 0===n&&(n={}),n.Subtype="StrikeOut",this._markup(e,t,r,i,n)},lineAnnotation(e,t,r,i,n){return void 0===n&&(n={}),n.Subtype="Line",n.Contents=new String,n.L=[e,this.page.height-t,r,this.page.height-i],this.annotate(e,t,r,i,n)},rectAnnotation(e,t,r,i,n){return void 0===n&&(n={}),n.Subtype="Square",n.Contents=new String,this.annotate(e,t,r,i,n)},ellipseAnnotation(e,t,r,i,n){return void 0===n&&(n={}),n.Subtype="Circle",n.Contents=new String,this.annotate(e,t,r,i,n)},textAnnotation(e,t,r,i,n,a){return void 0===a&&(a={}),a.Subtype="FreeText",a.Contents=new String(n),a.DA=new String,this.annotate(e,t,r,i,a)},fileAnnotation(e,t,r,i,n,a){void 0===n&&(n={}),void 0===a&&(a={});let o=this.file(n.src,Object.assign({hidden:!0},n));return a.Subtype="FileAttachment",a.FS=o,a.Contents?a.Contents=new String(a.Contents):o.data.Desc&&(a.Contents=o.data.Desc),this.annotate(e,t,r,i,a)},_convertRect(e,t,r,i){let n=t;t+=i;let a=e+r,[o,s,c,u,l,h]=this._ctm;return t=s*(e=o*e+c*t+l)+u*t+h,n=s*(a=o*a+c*n+l)+u*n+h,[e,t,a,n]}}),nj({initOutline(){return this.outline=new nC(this,null,null,null)},endOutline(){if(this.outline.endOutline(),this.outline.children.length>0)return this._root.data.Outlines=this.outline.dictionary,this._root.data.PageMode="UseOutlines"}}),nj({initMarkings(e){this.structChildren=[],e.tagged&&(this.getMarkInfoDictionary().data.Marked=!0,this.getStructTreeRoot())},markContent(e,t){if(void 0===t&&(t=null),"Artifact"===e||t&&t.mcid){let e=0;for(this.page.markings.forEach(t=>{(e||t.structContent||"Artifact"===t.tag)&&e++});e--;)this.endMarkedContent()}if(!t)return this.page.markings.push({tag:e}),this.addContent(`/${e} BMC`),this;this.page.markings.push({tag:e,options:t});let r={};return void 0!==t.mcid&&(r.MCID=t.mcid),"Artifact"===e&&("string"==typeof t.type&&(r.Type=t.type),Array.isArray(t.bbox)&&(r.BBox=[t.bbox[0],this.page.height-t.bbox[3],t.bbox[2],this.page.height-t.bbox[1]]),Array.isArray(t.attached)&&t.attached.every(e=>"string"==typeof e)&&(r.Attached=t.attached)),"Span"===e&&(t.lang&&(r.Lang=new String(t.lang)),t.alt&&(r.Alt=new String(t.alt)),t.expanded&&(r.E=new String(t.expanded)),t.actual&&(r.ActualText=new String(t.actual))),this.addContent(`/${e} ${iq.convert(r)} BDC`),this},markStructureContent(e,t){void 0===t&&(t={});let r=this.getStructParentTree().get(this.page.structParentTreeKey),i=r.length;r.push(null),this.markContent(e,{...t,mcid:i});let n=new nU(this.page.dictionary,i);return this.page.markings.slice(-1)[0].structContent=n,n},endMarkedContent(){return this.page.markings.pop(),this.addContent("EMC"),this},struct(e,t,r){return void 0===t&&(t={}),void 0===r&&(r=null),new nD(this,e,t,r)},addStructure(e){let t=this.getStructTreeRoot();return e.setParent(t),e.setAttached(),this.structChildren.push(e),t.data.K||(t.data.K=[]),t.data.K.push(e.dictionary),this},initPageMarkings(e){e.forEach(e=>{if(e.structContent){let t=e.structContent,r=this.markStructureContent(e.tag,e.options);t.push(r),this.page.markings.slice(-1)[0].structContent=t}else this.markContent(e.tag,e.options)})},endPageMarkings(e){let t=e.markings;return t.forEach(()=>e.write("EMC")),e.markings=[],t},getMarkInfoDictionary(){return this._root.data.MarkInfo||(this._root.data.MarkInfo=this.ref({})),this._root.data.MarkInfo},hasMarkInfoDictionary(){return!!this._root.data.MarkInfo},getStructTreeRoot(){return this._root.data.StructTreeRoot||(this._root.data.StructTreeRoot=this.ref({Type:"StructTreeRoot",ParentTree:new nP,ParentTreeNextKey:0})),this._root.data.StructTreeRoot},getStructParentTree(){return this.getStructTreeRoot().data.ParentTree},createStructParentTreeNextKey(){this.getMarkInfoDictionary();let e=this.getStructTreeRoot(),t=e.data.ParentTreeNextKey++;return e.data.ParentTree.add(t,[]),t},endMarkings(){let e=this._root.data.StructTreeRoot;e&&(e.end(),this.structChildren.forEach(e=>e.end())),this._root.data.MarkInfo&&this._root.data.MarkInfo.end()}}),nj({initForm(){if(!this._font)throw Error("Must set a font before calling initForm method");this._acroform={fonts:{},defaultFont:this._font.name},this._acroform.fonts[this._font.id]=this._font.ref();let e={Fields:[],NeedAppearances:!0,DA:new String(`/${this._font.id} 0 Tf 0 g`),DR:{Font:{}}};e.DR.Font[this._font.id]=this._font.ref();let t=this.ref(e);return this._root.data.AcroForm=t,this},endAcroForm(){if(this._root.data.AcroForm){if(!Object.keys(this._acroform.fonts).length&&!this._acroform.defaultFont)throw Error("No fonts specified for PDF form");let e=this._root.data.AcroForm.data.DR.Font;Object.keys(this._acroform.fonts).forEach(t=>{e[t]=this._acroform.fonts[t]}),this._root.data.AcroForm.data.Fields.forEach(e=>{this._endChild(e)}),this._root.data.AcroForm.end()}return this},_endChild(e){return Array.isArray(e.data.Kids)&&(e.data.Kids.forEach(e=>{this._endChild(e)}),e.end()),this},formField(e,t){void 0===t&&(t={});let r=this._fieldDict(e,null,t),i=this.ref(r);return this._addToParent(i),i},formAnnotation(e,t,r,i,n,a,o){void 0===o&&(o={});let s=this._fieldDict(e,t,o);s.Subtype="Widget",void 0===s.F&&(s.F=4),this.annotate(r,i,n,a,s);let c=this.page.annotations[this.page.annotations.length-1];return this._addToParent(c)},formText(e,t,r,i,n,a){return void 0===a&&(a={}),this.formAnnotation(e,"text",t,r,i,n,a)},formPushButton(e,t,r,i,n,a){return void 0===a&&(a={}),this.formAnnotation(e,"pushButton",t,r,i,n,a)},formCombo(e,t,r,i,n,a){return void 0===a&&(a={}),this.formAnnotation(e,"combo",t,r,i,n,a)},formList(e,t,r,i,n,a){return void 0===a&&(a={}),this.formAnnotation(e,"list",t,r,i,n,a)},formRadioButton(e,t,r,i,n,a){return void 0===a&&(a={}),this.formAnnotation(e,"radioButton",t,r,i,n,a)},formCheckbox(e,t,r,i,n,a){return void 0===a&&(a={}),this.formAnnotation(e,"checkbox",t,r,i,n,a)},_addToParent(e){let t=e.data.Parent;return t?(t.data.Kids||(t.data.Kids=[]),t.data.Kids.push(e)):this._root.data.AcroForm.data.Fields.push(e),this},_fieldDict(e,t,r){if(void 0===r&&(r={}),!this._acroform)throw Error("Call document.initForm() method before adding form elements to document");let i=Object.assign({},r);return null!==t&&(i=this._resolveType(t,r)),i=this._resolveFlags(i),i=this._resolveJustify(i),i=this._resolveFont(i),i=this._resolveStrings(i),i=this._resolveColors(i),(i=this._resolveFormat(i)).T=new String(e),i.parent&&(i.Parent=i.parent,delete i.parent),i},_resolveType(e,t){if("text"===e)t.FT="Tx";else if("pushButton"===e)t.FT="Btn",t.pushButton=!0;else if("radioButton"===e)t.FT="Btn",t.radioButton=!0;else if("checkbox"===e)t.FT="Btn";else if("combo"===e)t.FT="Ch",t.combo=!0;else if("list"===e)t.FT="Ch";else throw Error(`Invalid form annotation type '${e}'`);return t},_resolveFormat(e){let t=e.format;if(t&&t.type){let r,i,n="";if(void 0!==nY[t.type])r="AFSpecial_Keystroke",i="AFSpecial_Format",n=nY[t.type];else{let e=t.type.charAt(0).toUpperCase()+t.type.slice(1);if(r=`AF${e}_Keystroke`,i=`AF${e}_Format`,"date"===t.type)r+="Ex",n=String(t.param);else if("time"===t.type)n=String(t.param);else if("number"===t.type){let e=Object.assign({},nI.number,t);n=String([String(e.nDec),e.sepComma?"0":"1",'"'+e.negStyle+'"',"null",'"'+e.currency+'"',String(e.currencyPrepend)].join(","))}else if("percent"===t.type){let e=Object.assign({},nI.percent,t);n=String([String(e.nDec),e.sepComma?"0":"1"].join(","))}}e.AA=e.AA?e.AA:{},e.AA.K={S:"JavaScript",JS:new String(`${r}(${n});`)},e.AA.F={S:"JavaScript",JS:new String(`${i}(${n});`)}}return delete e.format,e},_resolveColors(e){let t=this._normalizeColor(e.backgroundColor);return t&&(e.MK||(e.MK={}),e.MK.BG=t),(t=this._normalizeColor(e.borderColor))&&(e.MK||(e.MK={}),e.MK.BC=t),delete e.backgroundColor,delete e.borderColor,e},_resolveFlags(e){let t=0;return Object.keys(e).forEach(r=>{nF[r]&&(e[r]&&(t|=nF[r]),delete e[r])}),0!==t&&(e.Ff=e.Ff?e.Ff:0,e.Ff|=t),e},_resolveJustify(e){let t=0;return void 0!==e.align&&("number"==typeof nL[e.align]&&(t=nL[e.align]),delete e.align),0!==t&&(e.Q=t),e},_resolveFont(e){if(null==this._acroform.fonts[this._font.id]&&(this._acroform.fonts[this._font.id]=this._font.ref()),this._acroform.defaultFont!==this._font.name){e.DR={Font:{}};let t=e.fontSize||0;e.DR.Font[this._font.id]=this._font.ref(),e.DA=new String(`/${this._font.id} ${t} Tf 0 g`)}return e},_resolveStrings(e){let t=[];function r(e){if(Array.isArray(e))for(let r=0;r<e.length;r++)"string"==typeof e[r]?t.push(new String(e[r])):t.push(e[r])}return r(e.Opt),e.select&&(r(e.select),delete e.select),t.length&&(e.Opt=t),Object.keys(nM).forEach(t=>{void 0!==e[t]&&(e[nM[t]]=e[t],delete e[t])}),["V","DV"].forEach(t=>{"string"==typeof e[t]&&(e[t]=new String(e[t]))}),e.MK&&e.MK.CA&&(e.MK.CA=new String(e.MK.CA)),e.label&&(e.MK=e.MK?e.MK:{},e.MK.CA=new String(e.label),delete e.label),e}}),nj({file(e,t){var r,i;let n,a;void 0===t&&(t={}),t.name=t.name||e,t.relationship=t.relationship||"Unspecified";let o={Type:"EmbeddedFile",Params:{}};if(!e)throw Error("No src specified");if(X.isBuffer(e))n=e;else if(e instanceof ArrayBuffer)n=X.from(new Uint8Array(e));else{let t;if(t=/^data:(.*?);base64,(.*)$/.exec(e))t[1]&&(o.Subtype=t[1].replace("/","#2F")),n=X.from(t[2],"base64");else{if(!(n=nd.readFileSync(e)))throw Error(`Could not read contents of file at filepath ${e}`);let{birthtime:t,ctime:r}=nd.statSync(e);o.Params.CreationDate=t,o.Params.ModDate=r}}t.creationDate instanceof Date&&(o.Params.CreationDate=t.creationDate),t.modifiedDate instanceof Date&&(o.Params.ModDate=t.modifiedDate),t.type&&(o.Subtype=t.type.replace("/","#2F"));let s=M.MD5(M.lib.WordArray.create(new Uint8Array(n)));o.Params.CheckSum=new String(s),o.Params.Size=n.byteLength,this._fileRegistry||(this._fileRegistry={});let c=this._fileRegistry[t.name];c&&(r=o,i=c,r.Subtype===i.Subtype&&r.Params.CheckSum.toString()===i.Params.CheckSum.toString()&&r.Params.Size===i.Params.Size&&r.Params.CreationDate.getTime()===i.Params.CreationDate.getTime()&&(void 0===r.Params.ModDate&&void 0===i.Params.ModDate||r.Params.ModDate.getTime()===i.Params.ModDate.getTime()))?a=c.ref:((a=this.ref(o)).end(n),this._fileRegistry[t.name]={...o,ref:a});let u={Type:"Filespec",AFRelationship:t.relationship,F:new String(t.name),EF:{F:a},UF:new String(t.name)};t.description&&(u.Desc=new String(t.description));let l=this.ref(u);return l.end(),t.hidden||this.addNamedEmbeddedFile(t.name,l),this._root.data.AF?this._root.data.AF.push(l):this._root.data.AF=[l],l}}),nj({_importSubset(e){Object.assign(this,e)},initSubset(e){switch(e.subset){case"PDF/A-1":case"PDF/A-1a":case"PDF/A-1b":case"PDF/A-2":case"PDF/A-2a":case"PDF/A-2b":case"PDF/A-3":case"PDF/A-3a":case"PDF/A-3b":this._importSubset(nN),this.initPDFA(e.subset);break;case"PDF/UA":this._importSubset(nB),this.initPDFUA()}}}),nz.LineWrapper=nS}}]);