(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4344],{15748:(e,t,i)=>{Promise.resolve().then(i.bind(i,33569))},33569:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>k});var s=i(95155),a=i(41784),d=i(83343);let r=(0,i(40157).A)("FilePen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]]);var l=i(35695),n=i(12115),o=i(76541),c=i(95647),u=i(68856),p=i(53712),m=i(61051);let y=e=>{var t;return{dateTime:e.dateTime?(0,a.GP)((0,d.H)(e.dateTime),"yyyy-MM-dd'T'HH:mm"):"",deadline:e.deadline?(0,a.GP)((0,d.H)(e.deadline),"yyyy-MM-dd'T'HH:mm"):"",description:e.description||"",driverEmployeeId:e.driverEmployeeId||null,estimatedDuration:e.estimatedDuration||0,location:e.location||"",notes:e.notes||"",priority:e.priority,requiredSkills:e.requiredSkills||[],staffEmployeeId:e.staffEmployeeId,status:e.status.replace("_"," "),subtasks:(null==(t=e.subtasks)?void 0:t.map(e=>({completed:e.completed,id:e.id,taskId:e.taskId,title:e.title})))||[],vehicleId:e.vehicleId||null}};function k(){let e=(0,l.useRouter)(),t=(0,l.useParams)(),{showEntityUpdated:i,showEntityUpdateError:a}=(0,p.O_)("task"),d=null==t?void 0:t.id,{data:k,error:v,isLoading:f}=(0,m.b7)(d),{error:h,isPending:b,mutateAsync:E}=(0,m.K)(),g=async t=>{if(d&&k){if(!t.staffEmployeeId)return void a("Staff employee is required for task updates.");try{var s;let r={dateTime:t.dateTime,deadline:t.deadline,description:t.description,driverEmployeeId:t.driverEmployeeId||void 0,estimatedDuration:t.estimatedDuration,location:t.location,notes:t.notes,priority:t.priority,requiredSkills:t.requiredSkills,staffEmployeeId:t.staffEmployeeId,status:t.status.replace(" ","_"),subtasks:null==(s=t.subtasks)?void 0:s.map(e=>{var t;return{completed:null!=(t=e.completed)&&t,taskId:d,title:e.title}}),vehicleId:t.vehicleId||void 0},l=Object.fromEntries(Object.entries(r).filter(e=>{let[,t]=e;return void 0!==t})),n=await E({data:l,id:d});if(n){let t={title:n.description.slice(0,30)+(n.description.length>30?"...":""),name:n.description.slice(0,30)+(n.description.length>30?"...":"")};i(t),e.push("/tasks/".concat(d))}else a("Failed to update task (no data returned).")}catch(e){console.error("Error updating task:",e),a(e.message||(null==h?void 0:h.message)||"Failed to update task. Please try again.")}}},x=(0,n.useMemo)(()=>{if(k)return y(k)},[k]);return f?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(c.z,{icon:r,title:"Loading Task..."}),(0,s.jsx)(u.E,{className:"h-[600px] w-full rounded-lg bg-card"})]}):k&&x?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(c.z,{description:"Modify the details for this task.",icon:r,title:"Edit Task: ".concat(k.description.slice(0,50)).concat(k.description.length>50?"...":"")}),h&&(0,s.jsxs)("p",{className:"rounded-md bg-red-100 p-3 text-red-500",children:["Error updating: ",h.message]}),(0,s.jsx)(o.A,{initialData:x,isEditing:!0,isLoading:b,onSubmit:g})]}):(0,s.jsxs)("div",{className:"space-y-6 text-center",children:[(0,s.jsx)(c.z,{description:"The requested task could not be found.",icon:r,title:"Task Not Found"}),(0,s.jsx)("button",{className:"text-blue-500",onClick:()=>e.push("/tasks"),children:"Back to Tasks"})]})}},68856:(e,t,i)=>{"use strict";i.d(t,{E:()=>d});var s=i(95155),a=i(54036);function d(e){let{className:t,...i}=e;return(0,s.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",t),...i})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6476,1137,3860,9664,1263,5495,1859,5669,4629,4036,8658,111,3712,7515,1051,9124,8441,1684,7358],()=>t(15748)),_N_E=e.O()}]);