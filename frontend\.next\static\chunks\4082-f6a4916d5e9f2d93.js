"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4082],{5484:(e,t,a)=>{a.d(t,{g:()=>i});var r=a(80937);function i(e,t){var a,i;let{data:n,error:l,isLoading:s,refetch:c}=(0,r.W_)(null!=e?e:0,{enabled:(null==(a=null==t?void 0:t.enabled)||a)&&!!e});return{error:l,isLoading:s,refetch:c,vehicleInfo:n?{color:null!=(i=n.color)?i:null,id:n.id,licensePlate:n.licensePlate,make:n.make,model:n.model,vin:n.vin,year:n.year}:null}}},22346:(e,t,a)=>{a.d(t,{w:()=>s});var r=a(95155),i=a(87489),n=a(12115),l=a(54036);let s=n.forwardRef((e,t)=>{let{className:a,decorative:n=!0,orientation:s="horizontal",...c}=e;return(0,r.jsx)(i.b,{className:(0,l.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",a),decorative:n,orientation:s,ref:t,...c})});s.displayName=i.b.displayName},40879:(e,t,a)=>{a.d(t,{dj:()=>f,oR:()=>u});var r=a(12115);let i=0,n=new Map,l=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),d({toastId:e,type:"REMOVE_TOAST"})},1e6);n.set(e,t)},s=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"DISMISS_TOAST":{let{toastId:a}=t;if(a)l(a);else for(let t of e.toasts)l(t.id);return{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)}}},c=[],o={toasts:[]};function d(e){for(let t of(o=s(o,e),c))t(o)}function u(e){let{...t}=e,a=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>d({toastId:a,type:"DISMISS_TOAST"});return d({toast:{...t,id:a,onOpenChange:e=>{e||r()},open:!0},type:"ADD_TOAST"}),{dismiss:r,id:a,update:e=>d({toast:{...e,id:a},type:"UPDATE_TOAST"})}}function f(){let[e,t]=r.useState(o);return r.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);-1!==e&&c.splice(e,1)}),[e]),{...e,dismiss:e=>d({type:"DISMISS_TOAST",...e&&{toastId:e}}),toast:u}}},42366:(e,t,a)=>{a.r(t),a.d(t,{useNotifications:()=>n,useWorkHubNotifications:()=>l});var r=a(12115),i=a(96016);let n=()=>{let e=(0,i.C)(e=>e.addNotification),t=(0,i.C)(e=>e.removeNotification),a=(0,i.C)(e=>e.clearAllNotifications),n=(0,i.C)(e=>e.unreadNotificationCount),l=(0,r.useCallback)(t=>{e({message:t,type:"success"})},[e]),s=(0,r.useCallback)(t=>{e({message:t,type:"error"})},[e]),c=(0,r.useCallback)(t=>{e({message:t,type:"warning"})},[e]),o=(0,r.useCallback)(t=>{e({message:t,type:"info"})},[e]),d=(0,r.useCallback)((e,t,a)=>{e?l(t):s(a)},[l,s]),u=(0,r.useCallback)(function(a,r){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;e({message:r,type:a}),setTimeout(()=>{let e=i.C.getState().notifications.at(-1);e&&e.message===r&&t(e.id)},n)},[e,t]),f=(0,r.useCallback)(function(){var t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";return e({message:a,type:"info"}),null==(t=i.C.getState().notifications.at(-1))?void 0:t.id},[e]),p=(0,r.useCallback)((e,a,r)=>{t(e),a?l(r):s(r)},[t,l,s]);return{clearAllNotifications:a,removeNotification:t,showApiResult:d,showError:s,showInfo:o,showLoading:f,showSuccess:l,showTemporary:u,showWarning:c,unreadCount:n,updateLoadingNotification:p}},l=()=>{let{clearAllNotifications:e,removeNotification:t,showError:a,showInfo:l,showSuccess:s,showWarning:c,unreadCount:o}=n(),d=(0,r.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"delegation",message:e,type:"delegation-update"})},[]),u=(0,r.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),f=(0,r.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:t,showDelegationUpdate:d,showEmployeeUpdate:(0,r.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"employee",message:e,type:"employee-update"})},[]),showError:a,showInfo:l,showSuccess:s,showTaskAssigned:f,showVehicleMaintenance:u,showWarning:c,unreadCount:o}}},55365:(e,t,a)=>{a.d(t,{Fc:()=>c,TN:()=>d,XL:()=>o});var r=a(95155),i=a(74466),n=a(12115),l=a(54036);let s=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),c=n.forwardRef((e,t)=>{let{className:a,variant:i,...n}=e;return(0,r.jsx)("div",{className:(0,l.cn)(s({variant:i}),a),ref:t,role:"alert",...n})});c.displayName="Alert";let o=n.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("h5",{className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",a),ref:t,...i})});o.displayName="AlertTitle";let d=n.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{className:(0,l.cn)("text-sm [&_p]:leading-relaxed",a),ref:t,...i})});d.displayName="AlertDescription"},66695:(e,t,a)=>{a.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>l,aR:()=>s,wL:()=>u});var r=a(95155),i=a(12115),n=a(54036);let l=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),ref:t,...i})});l.displayName="Card";let s=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),ref:t,...i})});s.displayName="CardHeader";let c=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),ref:t,...i})});c.displayName="CardTitle";let o=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{className:(0,n.cn)("text-sm text-muted-foreground",a),ref:t,...i})});o.displayName="CardDescription";let d=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{className:(0,n.cn)("p-6 pt-0",a),ref:t,...i})});d.displayName="CardContent";let u=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,r.jsx)("div",{className:(0,n.cn)("flex items-center p-6 pt-0",a),ref:t,...i})});u.displayName="CardFooter"},68856:(e,t,a)=>{a.d(t,{E:()=>n});var r=a(95155),i=a(54036);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",t),...a})}},80937:(e,t,a)=>{a.d(t,{NS:()=>v,T$:()=>d,W_:()=>u,Y1:()=>f,lR:()=>p});var r=a(26715),i=a(5041),n=a(90111),l=a(42366),s=a(99605),c=a(75908);let o={all:["vehicles"],detail:e=>["vehicles",e]},d=e=>(0,n.GK)([...o.all],async()=>(await c.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),u=(e,t)=>{var a;return(0,n.GK)([...o.detail(e)],()=>c.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(null==(a=null==t?void 0:t.enabled)||a),staleTime:3e5,...t})},f=()=>{let e=(0,r.jE)(),{showError:t,showSuccess:a}=(0,l.useNotifications)();return(0,i.n)({mutationFn:e=>{let t=s.M.toCreateRequest(e);return c.vehicleApiService.create(t)},onError:e=>{t("Failed to create vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:t=>{e.invalidateQueries({queryKey:o.all}),a('Vehicle "'.concat(t.licensePlate,'" has been created successfully!'))}})},p=()=>{let e=(0,r.jE)(),{showError:t,showSuccess:a}=(0,l.useNotifications)();return(0,i.n)({mutationFn:e=>{let{data:t,id:a}=e,r=s.M.toUpdateRequest(t);return c.vehicleApiService.update(a,r)},onError:e=>{t("Failed to update vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:t=>{e.invalidateQueries({queryKey:o.all}),e.invalidateQueries({queryKey:o.detail(t.id)}),a('Vehicle "'.concat(t.licensePlate,'" has been updated successfully!'))}})},v=()=>{let e=(0,r.jE)(),{showError:t,showSuccess:a}=(0,l.useNotifications)();return(0,i.n)({mutationFn:e=>c.vehicleApiService.delete(e),onError:e=>{t("Failed to delete vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:(t,r)=>{e.invalidateQueries({queryKey:o.all}),e.removeQueries({queryKey:o.detail(r)}),a("Vehicle has been deleted successfully!")}})}},90010:(e,t,a)=>{a.d(t,{$v:()=>m,EO:()=>f,Lt:()=>c,Rx:()=>y,Zr:()=>g,ck:()=>v,r7:()=>h,tv:()=>o,wd:()=>p});var r=a(95155),i=a(17649),n=a(12115),l=a(30285),s=a(54036);let c=i.bL,o=i.l9,d=i.ZL,u=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(i.hJ,{className:(0,s.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...n,ref:t})});u.displayName=i.hJ.displayName;let f=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsxs)(d,{children:[(0,r.jsx)(u,{}),(0,r.jsx)(i.UC,{className:(0,s.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),ref:t,...n})]})});f.displayName=i.UC.displayName;let p=e=>{let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,s.cn)("flex flex-col space-y-2 text-center sm:text-left",t),...a})};p.displayName="AlertDialogHeader";let v=e=>{let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,s.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};v.displayName="AlertDialogFooter";let h=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(i.hE,{className:(0,s.cn)("text-lg font-semibold",a),ref:t,...n})});h.displayName=i.hE.displayName;let m=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(i.VY,{className:(0,s.cn)("text-sm text-muted-foreground",a),ref:t,...n})});m.displayName=i.VY.displayName;let y=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(i.rc,{className:(0,s.cn)((0,l.r)(),a),ref:t,...n})});y.displayName=i.rc.displayName;let g=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(i.ZD,{className:(0,s.cn)((0,l.r)({variant:"outline"}),"mt-2 sm:mt-0",a),ref:t,...n})});g.displayName=i.ZD.displayName},96016:(e,t,a)=>{a.d(t,{C:()=>n});var r=a(65453),i=a(46786);let n=(0,r.v)()((0,i.lt)((0,i.Zr)((e,t)=>({addNotification:t=>e(e=>({notifications:[...e.notifications,{...t,id:crypto.randomUUID(),read:!1,timestamp:new Date().toISOString()}]})),clearAllNotifications:()=>e({notifications:[]}),currentTheme:"light",markNotificationAsRead:t=>e(e=>({notifications:e.notifications.map(e=>e.id===t?{...e,read:!0}:e)})),notifications:[],removeNotification:t=>e(e=>({notifications:e.notifications.filter(e=>e.id!==t)})),setTheme:t=>{e({currentTheme:t})},sidebarOpen:!1,toggleSidebar:()=>e(e=>({sidebarOpen:!e.sidebarOpen})),unreadNotificationCount:()=>{let{notifications:e}=t();return e.filter(e=>!e.read).length}}),{name:"workhub-app-store",partialize:e=>({currentTheme:e.currentTheme})}),{name:"app-store"}))},98691:(e,t,a)=>{a.d(t,{Ln:()=>m,WV:()=>f,fs:()=>p,kI:()=>h,xH:()=>v,xT:()=>y});var r=a(26715),i=a(5041),n=a(25982),l=a(90111),s=a(72248);let c={fromApi(e){let t={cost:e.cost,createdAt:e.createdAt,date:e.date,employeeId:e.employeeId,id:e.id,notes:e.notes,odometer:e.odometer,servicePerformed:Array.isArray(e.servicePerformed)?e.servicePerformed:[],updatedAt:e.updatedAt,vehicleId:e.vehicleId};return e.vehicleMake||e.vehicleModel||e.vehicleYear?{...t,vehicleMake:e.vehicleMake||"Unknown",vehicleModel:e.vehicleModel||"Unknown",vehicleYear:e.vehicleYear||new Date().getFullYear(),licensePlate:e.licensePlate||null,employeeName:e.employeeName||null}:t},toApi:e=>e};class o extends n.v{async getById(e){return this.executeWithInfrastructure("".concat(this.endpoint,":").concat(e),async()=>{let t=await this.apiClient.get("".concat(this.endpoint,"/").concat(e));return this.transformer.fromApi?this.transformer.fromApi(t):t})}async updateRecord(e,t,a){return this.executeWithInfrastructure(null,async()=>{let{vehicleId:r,...i}=a,n=await this.apiClient.put("/vehicles/".concat(t,"/servicerecords/").concat(e),i);return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":").concat(e))),this.cache.invalidatePattern(new RegExp("^vehicles:".concat(t,":"))),this.transformer.fromApi?this.transformer.fromApi(n):n})}async deleteRecord(e,t){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete("/vehicles/".concat(t,"/servicerecords/").concat(e)),this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":").concat(e))),this.cache.invalidatePattern(new RegExp("^vehicles:".concat(t,":")))})}async getVehicleServiceRecords(e){return this.executeWithInfrastructure("vehicles:".concat(e,":servicerecords"),async()=>await this.apiClient.get("/vehicles/".concat(e,"/servicerecords")))}async createVehicleServiceRecord(e,t){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.post("/vehicles/".concat(e,"/servicerecords"),t);return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidatePattern(new RegExp("^vehicles:".concat(e,":"))),this.transformer.fromApi?this.transformer.fromApi(a):a})}async getAllEnriched(){return this.executeWithInfrastructure("".concat(this.endpoint,":enriched"),async()=>(await this.apiClient.get("".concat(this.endpoint,"/enriched"))).map(e=>this.transformer.fromApi?this.transformer.fromApi(e):e))}constructor(e,t){super(e,{cacheDuration:3e5,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/servicerecords",this.transformer=c}}let d=new o(s.uE),u="serviceRecords",f=(e,t)=>{var a;return(0,l.GK)([u,e],()=>d.getById(e),"serviceRecord",{enabled:null!=(a=null==t?void 0:t.enabled)?a:!!e,staleTime:3e5})},p=e=>{var t;return(0,l.GK)([u,"allEnriched"],()=>d.getAllEnriched(),"serviceRecord",{enabled:null==(t=null==e?void 0:e.enabled)||t,staleTime:3e5})},v=(e,t)=>{var a;return(0,l.GK)([u,"forVehicle",e],()=>d.getVehicleServiceRecords(e),"serviceRecord",{enabled:null==(a=null==t?void 0:t.enabled)||a,staleTime:3e5})},h=()=>{let e=(0,r.jE)();return(0,i.n)({mutationFn:async e=>{let{vehicleId:t}=e;return d.createVehicleServiceRecord(t,e)},onSuccess:(t,a)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,"forVehicle",a.vehicleId]}),e.invalidateQueries({queryKey:["vehicle",a.vehicleId]})}})},m=()=>{let e=(0,r.jE)();return(0,i.n)({mutationFn:async e=>{let{id:t,vehicleId:a,data:r}=e;return d.updateRecord(t,a,r)},onSuccess:(t,a)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,a.id]}),e.invalidateQueries({queryKey:[u,"forVehicle",a.vehicleId]}),e.invalidateQueries({queryKey:["vehicle",a.vehicleId]})}})},y=()=>{let e=(0,r.jE)();return(0,i.n)({mutationFn:async e=>{let{id:t,vehicleId:a}=e;return d.deleteRecord(t,a)},onSuccess:(t,a)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,a.id]}),e.invalidateQueries({queryKey:[u,"forVehicle"]}),e.invalidateQueries({queryKey:["vehicle"]})}})}}}]);