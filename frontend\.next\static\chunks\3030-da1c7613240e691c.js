"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3030],{4884:(e,t,r)=>{r.d(t,{bL:()=>k,zi:()=>R});var n=r(12115),o=r(85185),a=r(6101),i=r(46081),l=r(5845),d=r(45503),s=r(11275),u=r(63655),c=r(95155),f="Switch",[p,h]=(0,i.A)(f),[m,v]=p(f),w=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:d,defaultChecked:s,required:p,disabled:h,value:v="on",onCheckedChange:w,form:y,...b}=e,[k,R]=n.useState(null),j=(0,a.s)(t,e=>R(e)),S=n.useRef(!1),E=!k||y||!!k.closest("form"),[M,A]=(0,l.i)({prop:d,defaultProp:null!=s&&s,onChange:w,caller:f});return(0,c.jsxs)(m,{scope:r,checked:M,disabled:h,children:[(0,c.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":M,"aria-required":p,"data-state":x(M),"data-disabled":h?"":void 0,disabled:h,value:v,...b,ref:j,onClick:(0,o.m)(e.onClick,e=>{A(e=>!e),E&&(S.current=e.isPropagationStopped(),S.current||e.stopPropagation())})}),E&&(0,c.jsx)(g,{control:k,bubbles:!S.current,name:i,value:v,checked:M,required:p,disabled:h,form:y,style:{transform:"translateX(-100%)"}})]})});w.displayName=f;var y="SwitchThumb",b=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=v(y,r);return(0,c.jsx)(u.sG.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});b.displayName=y;var g=n.forwardRef((e,t)=>{let{__scopeSwitch:r,control:o,checked:i,bubbles:l=!0,...u}=e,f=n.useRef(null),p=(0,a.s)(f,t),h=(0,d.Z)(i),m=(0,s.X)(o);return n.useEffect(()=>{let e=f.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==i&&t){let r=new Event("click",{bubbles:l});t.call(e,i),e.dispatchEvent(r)}},[h,i,l]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...u,tabIndex:-1,ref:p,style:{...u.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}g.displayName="SwitchBubbleInput";var k=w,R=b},29471:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},34214:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},54059:(e,t,r)=>{r.d(t,{C1:()=>H,bL:()=>z,q7:()=>q});var n=r(12115),o=r(85185),a=r(6101),i=r(46081),l=r(63655),d=r(89196),s=r(5845),u=r(94315),c=r(11275),f=r(45503),p=r(28905),h=r(95155),m="Radio",[v,w]=(0,i.A)(m),[y,b]=v(m),g=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:i,checked:d=!1,required:s,disabled:u,value:c="on",onCheck:f,form:p,...m}=e,[v,w]=n.useState(null),b=(0,a.s)(t,e=>w(e)),g=n.useRef(!1),x=!v||p||!!v.closest("form");return(0,h.jsxs)(y,{scope:r,checked:d,disabled:u,children:[(0,h.jsx)(l.sG.button,{type:"button",role:"radio","aria-checked":d,"data-state":j(d),"data-disabled":u?"":void 0,disabled:u,value:c,...m,ref:b,onClick:(0,o.m)(e.onClick,e=>{d||null==f||f(),x&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),x&&(0,h.jsx)(R,{control:v,bubbles:!g.current,name:i,value:c,checked:d,required:s,disabled:u,form:p,style:{transform:"translateX(-100%)"}})]})});g.displayName=m;var x="RadioIndicator",k=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...o}=e,a=b(x,r);return(0,h.jsx)(p.C,{present:n||a.checked,children:(0,h.jsx)(l.sG.span,{"data-state":j(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t})})});k.displayName=x;var R=n.forwardRef((e,t)=>{let{__scopeRadio:r,control:o,checked:i,bubbles:d=!0,...s}=e,u=n.useRef(null),p=(0,a.s)(u,t),m=(0,f.Z)(i),v=(0,c.X)(o);return n.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==i&&t){let r=new Event("click",{bubbles:d});t.call(e,i),e.dispatchEvent(r)}},[m,i,d]),(0,h.jsx)(l.sG.input,{type:"radio","aria-hidden":!0,defaultChecked:i,...s,tabIndex:-1,ref:p,style:{...s.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}R.displayName="RadioBubbleInput";var S=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],E="RadioGroup",[M,A]=(0,i.A)(E,[d.RG,w]),D=(0,d.RG)(),P=w(),[C,I]=M(E),_=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:o,value:a,required:i=!1,disabled:c=!1,orientation:f,dir:p,loop:m=!0,onValueChange:v,...w}=e,y=D(r),b=(0,u.jH)(p),[g,x]=(0,s.i)({prop:a,defaultProp:null!=o?o:null,onChange:v,caller:E});return(0,h.jsx)(C,{scope:r,name:n,required:i,disabled:c,value:g,onValueChange:x,children:(0,h.jsx)(d.bL,{asChild:!0,...y,orientation:f,dir:b,loop:m,children:(0,h.jsx)(l.sG.div,{role:"radiogroup","aria-required":i,"aria-orientation":f,"data-disabled":c?"":void 0,dir:b,...w,ref:t})})})});_.displayName=E;var G="RadioGroupItem",L=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:i,...l}=e,s=I(G,r),u=s.disabled||i,c=D(r),f=P(r),p=n.useRef(null),m=(0,a.s)(t,p),v=s.value===l.value,w=n.useRef(!1);return n.useEffect(()=>{let e=e=>{S.includes(e.key)&&(w.current=!0)},t=()=>w.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,h.jsx)(d.q7,{asChild:!0,...c,focusable:!u,active:v,children:(0,h.jsx)(g,{disabled:u,required:s.required,checked:v,...f,...l,name:s.name,ref:m,onCheck:()=>s.onValueChange(l.value),onKeyDown:(0,o.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.m)(l.onFocus,()=>{var e;w.current&&(null==(e=p.current)||e.click())})})})});L.displayName=G;var N=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,o=P(r);return(0,h.jsx)(k,{...o,...n,ref:t})});N.displayName="RadioGroupIndicator";var z=_,q=L,H=N},54073:(e,t,r)=>{r.d(t,{CC:()=>T,Q6:()=>U,bL:()=>X,zi:()=>B});var n=r(12115),o=r(89367),a=r(85185),i=r(6101),l=r(46081),d=r(5845),s=r(94315),u=r(45503),c=r(11275),f=r(63655),p=r(29855),h=r(95155),m=["PageUp","PageDown"],v=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],w={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},y="Slider",[b,g,x]=(0,p.N)(y),[k,R]=(0,l.A)(y,[x]),[j,S]=k(y),E=n.forwardRef((e,t)=>{let{name:r,min:i=0,max:l=100,step:s=1,orientation:u="horizontal",disabled:c=!1,minStepsBetweenThumbs:f=0,defaultValue:p=[i],value:w,onValueChange:y=()=>{},onValueCommit:g=()=>{},inverted:x=!1,form:k,...R}=e,S=n.useRef(new Set),E=n.useRef(0),M="horizontal"===u,[A=[],C]=(0,d.i)({prop:w,defaultProp:p,onChange:e=>{var t;null==(t=[...S.current][E.current])||t.focus(),y(e)}}),I=n.useRef(A);function _(e,t){let{commit:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{commit:!1};let n=(String(s).split(".")[1]||"").length,a=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-i)/s)*s+i,n),d=(0,o.q)(a,[i,l]);C(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,d,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,f*s))return e;{E.current=n.indexOf(d);let t=String(n)!==String(e);return t&&r&&g(n),t?n:e}})}return(0,h.jsx)(j,{scope:e.__scopeSlider,name:r,disabled:c,min:i,max:l,valueIndexToChangeRef:E,thumbs:S.current,values:A,orientation:u,form:k,children:(0,h.jsx)(b.Provider,{scope:e.__scopeSlider,children:(0,h.jsx)(b.Slot,{scope:e.__scopeSlider,children:(0,h.jsx)(M?D:P,{"aria-disabled":c,"data-disabled":c?"":void 0,...R,ref:t,onPointerDown:(0,a.m)(R.onPointerDown,()=>{c||(I.current=A)}),min:i,max:l,inverted:x,onSlideStart:c?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(A,e);_(e,t)},onSlideMove:c?void 0:function(e){_(e,E.current)},onSlideEnd:c?void 0:function(){let e=I.current[E.current];A[E.current]!==e&&g(A)},onHomeKeyDown:()=>!c&&_(i,0,{commit:!0}),onEndKeyDown:()=>!c&&_(l,A.length-1,{commit:!0}),onStepKeyDown:e=>{let{event:t,direction:r}=e;if(!c){let e=m.includes(t.key)||t.shiftKey&&v.includes(t.key),n=E.current;_(A[n]+s*(e?10:1)*r,n,{commit:!0})}}})})})})});E.displayName=y;var[M,A]=k(y,{startEdge:"left",endEdge:"right",size:"width",direction:1}),D=n.forwardRef((e,t)=>{let{min:r,max:o,dir:a,inverted:l,onSlideStart:d,onSlideMove:u,onSlideEnd:c,onStepKeyDown:f,...p}=e,[m,v]=n.useState(null),y=(0,i.s)(t,e=>v(e)),b=n.useRef(void 0),g=(0,s.jH)(a),x="ltr"===g,k=x&&!l||!x&&l;function R(e){let t=b.current||m.getBoundingClientRect(),n=O([0,t.width],k?[r,o]:[o,r]);return b.current=t,n(e-t.left)}return(0,h.jsx)(M,{scope:e.__scopeSlider,startEdge:k?"left":"right",endEdge:k?"right":"left",direction:k?1:-1,size:"width",children:(0,h.jsx)(C,{dir:g,"data-orientation":"horizontal",...p,ref:y,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=R(e.clientX);null==d||d(t)},onSlideMove:e=>{let t=R(e.clientX);null==u||u(t)},onSlideEnd:()=>{b.current=void 0,null==c||c()},onStepKeyDown:e=>{let t=w[k?"from-left":"from-right"].includes(e.key);null==f||f({event:e,direction:t?-1:1})}})})}),P=n.forwardRef((e,t)=>{let{min:r,max:o,inverted:a,onSlideStart:l,onSlideMove:d,onSlideEnd:s,onStepKeyDown:u,...c}=e,f=n.useRef(null),p=(0,i.s)(t,f),m=n.useRef(void 0),v=!a;function y(e){let t=m.current||f.current.getBoundingClientRect(),n=O([0,t.height],v?[o,r]:[r,o]);return m.current=t,n(e-t.top)}return(0,h.jsx)(M,{scope:e.__scopeSlider,startEdge:v?"bottom":"top",endEdge:v?"top":"bottom",size:"height",direction:v?1:-1,children:(0,h.jsx)(C,{"data-orientation":"vertical",...c,ref:p,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=y(e.clientY);null==l||l(t)},onSlideMove:e=>{let t=y(e.clientY);null==d||d(t)},onSlideEnd:()=>{m.current=void 0,null==s||s()},onStepKeyDown:e=>{let t=w[v?"from-bottom":"from-top"].includes(e.key);null==u||u({event:e,direction:t?-1:1})}})})}),C=n.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:n,onSlideMove:o,onSlideEnd:i,onHomeKeyDown:l,onEndKeyDown:d,onStepKeyDown:s,...u}=e,c=S(y,r);return(0,h.jsx)(f.sG.span,{...u,ref:t,onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Home"===e.key?(l(e),e.preventDefault()):"End"===e.key?(d(e),e.preventDefault()):m.concat(v).includes(e.key)&&(s(e),e.preventDefault())}),onPointerDown:(0,a.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),c.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,a.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&o(e)}),onPointerUp:(0,a.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),i(e))})})}),I="SliderTrack",_=n.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,o=S(I,r);return(0,h.jsx)(f.sG.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...n,ref:t})});_.displayName=I;var G="SliderRange",L=n.forwardRef((e,t)=>{let{__scopeSlider:r,...o}=e,a=S(G,r),l=A(G,r),d=n.useRef(null),s=(0,i.s)(t,d),u=a.values.length,c=a.values.map(e=>K(e,a.min,a.max)),p=u>1?Math.min(...c):0,m=100-Math.max(...c);return(0,h.jsx)(f.sG.span,{"data-orientation":a.orientation,"data-disabled":a.disabled?"":void 0,...o,ref:s,style:{...e.style,[l.startEdge]:p+"%",[l.endEdge]:m+"%"}})});L.displayName=G;var N="SliderThumb",z=n.forwardRef((e,t)=>{let r=g(e.__scopeSlider),[o,a]=n.useState(null),l=(0,i.s)(t,e=>a(e)),d=n.useMemo(()=>o?r().findIndex(e=>e.ref.current===o):-1,[r,o]);return(0,h.jsx)(q,{...e,ref:l,index:d})}),q=n.forwardRef((e,t)=>{let{__scopeSlider:r,index:o,name:l,...d}=e,s=S(N,r),u=A(N,r),[p,m]=n.useState(null),v=(0,i.s)(t,e=>m(e)),w=!p||s.form||!!p.closest("form"),y=(0,c.X)(p),g=s.values[o],x=void 0===g?0:K(g,s.min,s.max),k=function(e,t){return t>2?"Value ".concat(e+1," of ").concat(t):2===t?["Minimum","Maximum"][e]:void 0}(o,s.values.length),R=null==y?void 0:y[u.size],j=R?function(e,t,r){let n=e/2,o=O([0,50],[0,n]);return(n-o(t)*r)*r}(R,x,u.direction):0;return n.useEffect(()=>{if(p)return s.thumbs.add(p),()=>{s.thumbs.delete(p)}},[p,s.thumbs]),(0,h.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[u.startEdge]:"calc(".concat(x,"% + ").concat(j,"px)")},children:[(0,h.jsx)(b.ItemSlot,{scope:e.__scopeSlider,children:(0,h.jsx)(f.sG.span,{role:"slider","aria-label":e["aria-label"]||k,"aria-valuemin":s.min,"aria-valuenow":g,"aria-valuemax":s.max,"aria-orientation":s.orientation,"data-orientation":s.orientation,"data-disabled":s.disabled?"":void 0,tabIndex:s.disabled?void 0:0,...d,ref:v,style:void 0===g?{display:"none"}:e.style,onFocus:(0,a.m)(e.onFocus,()=>{s.valueIndexToChangeRef.current=o})})}),w&&(0,h.jsx)(H,{name:null!=l?l:s.name?s.name+(s.values.length>1?"[]":""):void 0,form:s.form,value:g},o)]})});z.displayName=N;var H=n.forwardRef((e,t)=>{let{__scopeSlider:r,value:o,...a}=e,l=n.useRef(null),d=(0,i.s)(l,t),s=(0,u.Z)(o);return n.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(s!==o&&t){let r=new Event("input",{bubbles:!0});t.call(e,o),e.dispatchEvent(r)}},[s,o]),(0,h.jsx)(f.sG.input,{style:{display:"none"},...a,ref:d,defaultValue:o})});function K(e,t,r){return(0,o.q)(100/(r-t)*(e-t),[0,100])}function O(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}H.displayName="RadioBubbleInput";var X=E,T=_,U=L,B=z},65064:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},89829:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])}}]);