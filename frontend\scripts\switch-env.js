#!/usr/bin/env node

/**
 * Environment Configuration Switcher
 * 
 * This script helps switch between different environment configurations
 * for development, network access, and production deployments.
 * 
 * Usage:
 *   npm run env:local     - Switch to localhost configuration
 *   npm run env:network   - Switch to network access configuration
 *   npm run env:production - Switch to production configuration
 */

const fs = require('fs');
const path = require('path');

const ENV_CONFIGS = {
  local: '.env.local',
  network: '.env.network', 
  production: '.env.production'
};

const TARGET_ENV = '.env.local';

function switchEnvironment(envType) {
  const sourceFile = ENV_CONFIGS[envType];
  
  if (!sourceFile) {
    console.error(`❌ Unknown environment type: ${envType}`);
    console.log('Available environments:', Object.keys(ENV_CONFIGS).join(', '));
    process.exit(1);
  }

  const sourcePath = path.join(__dirname, '..', sourceFile);
  const targetPath = path.join(__dirname, '..', TARGET_ENV);

  if (!fs.existsSync(sourcePath)) {
    console.error(`❌ Environment file not found: ${sourceFile}`);
    process.exit(1);
  }

  try {
    // Backup current .env.local if it exists
    if (fs.existsSync(targetPath)) {
      const backupPath = `${targetPath}.backup.${Date.now()}`;
      fs.copyFileSync(targetPath, backupPath);
      console.log(`📦 Backed up current configuration to: ${path.basename(backupPath)}`);
    }

    // Copy the new configuration
    fs.copyFileSync(sourcePath, targetPath);
    console.log(`✅ Switched to ${envType} environment configuration`);
    console.log(`📁 Active configuration: ${TARGET_ENV}`);
    
    // Show current configuration summary
    showConfigSummary(targetPath);
    
  } catch (error) {
    console.error(`❌ Failed to switch environment: ${error.message}`);
    process.exit(1);
  }
}

function showConfigSummary(configPath) {
  try {
    const content = fs.readFileSync(configPath, 'utf8');
    const apiUrl = content.match(/NEXT_PUBLIC_API_BASE_URL=(.+)/)?.[1];
    const wsUrl = content.match(/NEXT_PUBLIC_WS_URL=(.+)/)?.[1];
    
    console.log('\n📋 Current Configuration:');
    console.log(`   API URL: ${apiUrl || 'Not set'}`);
    console.log(`   WebSocket URL: ${wsUrl || 'Not set'}`);
    console.log('\n🔄 Please restart your development server for changes to take effect.');
  } catch (error) {
    console.warn('⚠️  Could not read configuration summary');
  }
}

// Get environment type from command line arguments
const envType = process.argv[2];

if (!envType) {
  console.log('🌍 Environment Configuration Switcher\n');
  console.log('Usage: node switch-env.js <environment>');
  console.log('\nAvailable environments:');
  console.log('  local      - Localhost development (localhost:3001)');
  console.log('  network    - Network access (**************:3001)');
  console.log('  production - Production deployment');
  console.log('\nExample: node switch-env.js network');
  process.exit(0);
}

switchEnvironment(envType);
