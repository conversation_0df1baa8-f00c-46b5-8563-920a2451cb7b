"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2700],{5041:(e,t,r)=>{r.d(t,{n:()=>c});var n=r(12115),s=r(34560),i=r(7165),a=r(25910),o=r(52020),l=class extends a.Q{#e;#t=void 0;#r;#n;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#s()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#s(),this.#i(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#s(),this.#i()}mutate(e,t){return this.#n=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#s(){let e=this.#r?.state??(0,s.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#i(e){i.jG.batch(()=>{if(this.#n&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#n.onSuccess?.(e.data,t,r),this.#n.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#n.onError?.(e.error,t,r),this.#n.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},u=r(26715);function c(e,t){let r=(0,u.jE)(t),[s]=n.useState(()=>new l(r,e));n.useEffect(()=>{s.setOptions(e)},[s,e]);let a=n.useSyncExternalStore(n.useCallback(e=>s.subscribe(i.jG.batchCalls(e)),[s]),()=>s.getCurrentResult(),()=>s.getCurrentResult()),c=n.useCallback((e,t)=>{s.mutate(e,t).catch(o.lQ)},[s]);if(a.error&&(0,o.GU)(s.options.throwOnError,[a.error]))throw a.error;return{...a,mutate:c,mutateAsync:a.mutate}}},13896:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},17649:(e,t,r)=>{r.d(t,{UC:()=>L,VY:()=>J,ZD:()=>U,ZL:()=>_,bL:()=>k,hE:()=>P,hJ:()=>C,l9:()=>M,rc:()=>T});var n=r(12115),s=r(46081),i=r(6101),a=r(15452),o=r(85185),l=r(99708),u=r(95155),c="AlertDialog",[d,h]=(0,s.A)(c,[a.Hs]),f=(0,a.Hs)(),p=e=>{let{__scopeAlertDialog:t,...r}=e,n=f(t);return(0,u.jsx)(a.bL,{...n,...r,modal:!0})};p.displayName=c;var v=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,s=f(r);return(0,u.jsx)(a.l9,{...s,...n,ref:t})});v.displayName="AlertDialogTrigger";var m=e=>{let{__scopeAlertDialog:t,...r}=e,n=f(t);return(0,u.jsx)(a.ZL,{...n,...r})};m.displayName="AlertDialogPortal";var y=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,s=f(r);return(0,u.jsx)(a.hJ,{...s,...n,ref:t})});y.displayName="AlertDialogOverlay";var g="AlertDialogContent",[b,S]=d(g),O=(0,l.Dc)("AlertDialogContent"),w=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:s,...l}=e,c=f(r),d=n.useRef(null),h=(0,i.s)(t,d),p=n.useRef(null);return(0,u.jsx)(a.G$,{contentName:g,titleName:E,docsSlug:"alert-dialog",children:(0,u.jsx)(b,{scope:r,cancelRef:p,children:(0,u.jsxs)(a.UC,{role:"alertdialog",...c,...l,ref:h,onOpenAutoFocus:(0,o.m)(l.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=p.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,u.jsx)(O,{children:s}),(0,u.jsx)(D,{contentRef:d})]})})})});w.displayName=g;var E="AlertDialogTitle",x=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,s=f(r);return(0,u.jsx)(a.hE,{...s,...n,ref:t})});x.displayName=E;var j="AlertDialogDescription",N=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,s=f(r);return(0,u.jsx)(a.VY,{...s,...n,ref:t})});N.displayName=j;var R=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,s=f(r);return(0,u.jsx)(a.bm,{...s,...n,ref:t})});R.displayName="AlertDialogAction";var A="AlertDialogCancel",I=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:s}=S(A,r),o=f(r),l=(0,i.s)(t,s);return(0,u.jsx)(a.bm,{...o,...n,ref:l})});I.displayName=A;var D=e=>{let{contentRef:t}=e,r="`".concat(g,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(g,"` by passing a `").concat(j,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(g,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return n.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},k=p,M=v,_=m,C=y,L=w,T=R,U=I,P=x,J=N},22436:(e,t,r)=>{var n=r(12115),s="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,a=n.useEffect,o=n.useLayoutEffect,l=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!s(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=i({inst:{value:r,getSnapshot:t}}),s=n[0].inst,c=n[1];return o(function(){s.value=r,s.getSnapshot=t,u(s)&&c({inst:s})},[e,r,t]),a(function(){return u(s)&&c({inst:s}),e(function(){u(s)&&c({inst:s})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},24371:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>o});var n=r(12115),s=r(63655),i=r(95155),a=n.forwardRef((e,t)=>(0,i.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var o=a},46786:(e,t,r)=>{r.d(t,{KU:()=>d,Zr:()=>f,eh:()=>c,lt:()=>l});let n=new Map,s=e=>{let t=n.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},i=(e,t,r)=>{if(void 0===e)return{type:"untracked",connection:t.connect(r)};let s=n.get(r.name);if(s)return{type:"tracked",store:e,...s};let i={connection:t.connect(r),stores:{}};return n.set(r.name,i),{type:"tracked",store:e,...i}},a=(e,t)=>{if(void 0===t)return;let r=n.get(e);r&&(delete r.stores[t],0===Object.keys(r.stores).length&&n.delete(e))},o=e=>{var t,r;if(!e)return;let n=e.split("\n"),s=n.findIndex(e=>e.includes("api.setState"));if(s<0)return;let i=(null==(t=n[s+1])?void 0:t.trim())||"";return null==(r=/.+ (.+) .+/.exec(i))?void 0:r[1]},l=(e,t={})=>(r,n,l)=>{let c,{enabled:d,anonymousActionType:h,store:f,...p}=t;try{c=(null==d||d)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!c)return e(r,n,l);let{connection:v,...m}=i(f,c,p),y=!0;l.setState=(e,t,i)=>{let a=r(e,t);if(!y)return a;let u=o(Error().stack),c=void 0===i?{type:h||u||"anonymous"}:"string"==typeof i?{type:i}:i;return void 0===f?null==v||v.send(c,n()):null==v||v.send({...c,type:`${f}/${c.type}`},{...s(p.name),[f]:l.getState()}),a},l.devtools={cleanup:()=>{v&&"function"==typeof v.unsubscribe&&v.unsubscribe(),a(p.name,f)}};let g=(...e)=>{let t=y;y=!1,r(...e),y=t},b=e(l.setState,n,l);if("untracked"===m.type?null==v||v.init(b):(m.stores[m.store]=l,null==v||v.init(Object.fromEntries(Object.entries(m.stores).map(([e,t])=>[e,e===m.store?b:t.getState()])))),l.dispatchFromDevtools&&"function"==typeof l.dispatch){let e=!1,t=l.dispatch;l.dispatch=(...r)=>{"__setState"!==r[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...r)}}return v.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload)return void console.error("[zustand devtools middleware] Unsupported action format");return u(e.payload,e=>{if("__setState"===e.type){if(void 0===f)return void g(e.state);1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[f];return void(null==t||JSON.stringify(l.getState())!==JSON.stringify(t)&&g(t))}l.dispatchFromDevtools&&"function"==typeof l.dispatch&&l.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(g(b),void 0===f)return null==v?void 0:v.init(l.getState());return null==v?void 0:v.init(s(p.name));case"COMMIT":if(void 0===f){null==v||v.init(l.getState());break}return null==v?void 0:v.init(s(p.name));case"ROLLBACK":return u(e.state,e=>{if(void 0===f){g(e),null==v||v.init(l.getState());return}g(e[f]),null==v||v.init(s(p.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return u(e.state,e=>{if(void 0===f)return void g(e);JSON.stringify(l.getState())!==JSON.stringify(e[f])&&g(e[f])});case"IMPORT_STATE":{let{nextLiftedState:r}=e.payload,n=null==(t=r.computedStates.slice(-1)[0])?void 0:t.state;if(!n)return;void 0===f?g(n):g(n[f]),null==v||v.send(null,r);break}case"PAUSE_RECORDING":return y=!y}return}}),b},u=(e,t)=>{let r;try{r=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==r&&t(r)},c=e=>(t,r,n)=>{let s=n.subscribe;return n.subscribe=(e,t,r)=>{let i=e;if(t){let s=(null==r?void 0:r.equalityFn)||Object.is,a=e(n.getState());i=r=>{let n=e(r);if(!s(a,n)){let e=a;t(a=n,e)}},(null==r?void 0:r.fireImmediately)&&t(a,a)}return s(i)},e(t,r,n)};function d(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var n;let s=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),i=null!=(n=r.getItem(e))?n:null;return i instanceof Promise?i.then(s):s(i)},setItem:(e,n)=>r.setItem(e,JSON.stringify(n,null==t?void 0:t.replacer)),removeItem:e=>r.removeItem(e)}}let h=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>h(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>h(t)(e)}}},f=(e,t)=>(r,n,s)=>{let i,a={storage:d(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},o=!1,l=new Set,u=new Set,c=a.storage;if(!c)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),r(...e)},n,s);let f=()=>{let e=a.partialize({...n()});return c.setItem(a.name,{state:e,version:a.version})},p=s.setState;s.setState=(e,t)=>{p(e,t),f()};let v=e((...e)=>{r(...e),f()},n,s);s.getInitialState=()=>v;let m=()=>{var e,t;if(!c)return;o=!1,l.forEach(e=>{var t;return e(null!=(t=n())?t:v)});let s=(null==(t=a.onRehydrateStorage)?void 0:t.call(a,null!=(e=n())?e:v))||void 0;return h(c.getItem.bind(c))(a.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===a.version)return[!1,e.state];else{if(a.migrate){let t=a.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[s,o]=e;if(r(i=a.merge(o,null!=(t=n())?t:v),!0),s)return f()}).then(()=>{null==s||s(i,void 0),i=n(),o=!0,u.forEach(e=>e(i))}).catch(e=>{null==s||s(void 0,e)})};return s.persist={setOptions:e=>{a={...a,...e},e.storage&&(c=e.storage)},clearStorage:()=>{null==c||c.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>m(),hasHydrated:()=>o,onHydrate:e=>(l.add(e),()=>{l.delete(e)}),onFinishHydration:e=>(u.add(e),()=>{u.delete(e)})},a.skipHydration||m(),i||v}},49033:(e,t,r)=>{e.exports=r(22436)},54011:(e,t,r)=>{r.d(t,{H4:()=>x,_V:()=>E,bL:()=>w});var n=r(12115),s=r(46081),i=r(39033),a=r(52712),o=r(63655),l=r(49033);function u(){return()=>{}}var c=r(95155),d="Avatar",[h,f]=(0,s.A)(d),[p,v]=h(d),m=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...s}=e,[i,a]=n.useState("idle");return(0,c.jsx)(p,{scope:r,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,c.jsx)(o.sG.span,{...s,ref:t})})});m.displayName=d;var y="AvatarImage",g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:s,onLoadingStatusChange:d=()=>{},...h}=e,f=v(y,r),p=function(e,t){let{referrerPolicy:r,crossOrigin:s}=t,i=(0,l.useSyncExternalStore)(u,()=>!0,()=>!1),o=n.useRef(null),c=i?(o.current||(o.current=new window.Image),o.current):null,[d,h]=n.useState(()=>O(c,e));return(0,a.N)(()=>{h(O(c,e))},[c,e]),(0,a.N)(()=>{let e=e=>()=>{h(e)};if(!c)return;let t=e("loaded"),n=e("error");return c.addEventListener("load",t),c.addEventListener("error",n),r&&(c.referrerPolicy=r),"string"==typeof s&&(c.crossOrigin=s),()=>{c.removeEventListener("load",t),c.removeEventListener("error",n)}},[c,s,r]),d}(s,h),m=(0,i.c)(e=>{d(e),f.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==p&&m(p)},[p,m]),"loaded"===p?(0,c.jsx)(o.sG.img,{...h,ref:t,src:s}):null});g.displayName=y;var b="AvatarFallback",S=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:s,...i}=e,a=v(b,r),[l,u]=n.useState(void 0===s);return n.useEffect(()=>{if(void 0!==s){let e=window.setTimeout(()=>u(!0),s);return()=>window.clearTimeout(e)}},[s]),l&&"loaded"!==a.imageLoadingStatus?(0,c.jsx)(o.sG.span,{...i,ref:t}):null});function O(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}S.displayName=b;var w=m,E=g,x=S},65453:(e,t,r)=>{r.d(t,{v:()=>l});var n=r(12115);let s=e=>{let t,r=new Set,n=(e,n)=>{let s="function"==typeof e?e(t):e;if(!Object.is(s,t)){let e=t;t=(null!=n?n:"object"!=typeof s||null===s)?s:Object.assign({},t,s),r.forEach(r=>r(t,e))}},s=()=>t,i={setState:n,getState:s,getInitialState:()=>a,subscribe:e=>(r.add(e),()=>r.delete(e))},a=t=e(n,s,i);return i},i=e=>e?s(e):s,a=e=>e,o=e=>{let t=i(e),r=e=>(function(e,t=a){let r=n.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return n.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},l=e=>e?o(e):o}}]);