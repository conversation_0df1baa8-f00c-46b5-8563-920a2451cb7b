(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8544],{26119:(e,s,t)=>{"use strict";t.d(s,{n:()=>i});var a=t(65453),r=t(46786);let i=(0,a.v)()((0,r.lt)((0,r.Zr)(e=>({autoRefreshInterval:30,closeModal:()=>e({isModalOpen:!1,modalContent:null}),dashboardLayout:"cards",fontSize:"medium",isModalOpen:!1,mapViewPreference:"roadmap",modalContent:null,notificationsEnabled:!0,openModal:s=>e({isModalOpen:!0,modalContent:s}),setAutoRefreshInterval:s=>e({autoRefreshInterval:s}),setDashboardLayout:s=>e({dashboardLayout:s}),setFontSize:s=>e({fontSize:s}),setMapViewPreference:s=>e({mapViewPreference:s}),setTableDensity:s=>e({tableDensity:s}),tableDensity:"comfortable",toggleNotifications:()=>e(e=>({notificationsEnabled:!e.notificationsEnabled}))}),{name:"workhub-ui-store",partialize:e=>({autoRefreshInterval:e.autoRefreshInterval,dashboardLayout:e.dashboardLayout,fontSize:e.fontSize,mapViewPreference:e.mapViewPreference,notificationsEnabled:e.notificationsEnabled,tableDensity:e.tableDensity})}),{name:"ui-store"}))},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(95155),r=t(74466);t(12115);var i=t(54036);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function l(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:t}),s),...r})}},26642:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(95155),r=t(12543),i=t(73350),n=t(17607),l=t(6874),c=t.n(l);t(12115);var o=t(27945),d=t(26126),m=t(30285),u=t(66695),x=t(36846);function h(){let{fontSize:e,getFontSizeClass:s,setFontSize:t}=(0,x.useUiPreferences)();return(0,a.jsxs)("div",{className:"container mx-auto space-y-8 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(c(),{href:"/",children:(0,a.jsxs)(m.$,{className:"flex items-center gap-2",size:"sm",variant:"ghost",children:[(0,a.jsx)(r.A,{className:"size-4"}),"Back to Dashboard"]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"flex items-center gap-2 text-3xl font-bold",children:[(0,a.jsx)(i.A,{className:"size-8"}),"Font Size Demo"]}),(0,a.jsx)("p",{className:"mt-1 text-muted-foreground",children:"Test and preview font size changes in real-time"})]})]}),(0,a.jsxs)(u.Zp,{children:[(0,a.jsxs)(u.aR,{children:[(0,a.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"size-5"}),"Current Font Size"]}),(0,a.jsx)(u.BT,{children:"The currently selected font size affects all content on this page"})]}),(0,a.jsx)(u.Wu,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-medium",children:"Active Setting"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Applied globally across WorkHub"})]}),(0,a.jsx)(d.E,{className:"px-4 py-2 text-lg capitalize",variant:"secondary",children:e})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-3",children:[(0,a.jsxs)(u.Zp,{children:[(0,a.jsx)(u.aR,{children:(0,a.jsx)(u.ZB,{className:"text-lg",children:"Quick Toggle"})}),(0,a.jsx)(u.Wu,{children:(0,a.jsx)(o.Ln,{})})]}),(0,a.jsxs)(u.Zp,{children:[(0,a.jsx)(u.aR,{children:(0,a.jsx)(u.ZB,{className:"text-lg",children:"Dropdown Selector"})}),(0,a.jsx)(u.Wu,{children:(0,a.jsx)(o.A5,{})})]}),(0,a.jsxs)(u.Zp,{children:[(0,a.jsx)(u.aR,{children:(0,a.jsx)(u.ZB,{className:"text-lg",children:"Button Controls"})}),(0,a.jsx)(u.Wu,{children:(0,a.jsx)("div",{className:"flex gap-1",children:["small","medium","large"].map(s=>(0,a.jsx)(m.$,{className:"flex-1 capitalize",onClick:()=>t(s),size:"sm",variant:e===s?"default":"outline",children:s},s))})})]})]}),(0,a.jsxs)(u.Zp,{children:[(0,a.jsxs)(u.aR,{children:[(0,a.jsx)(u.ZB,{children:"Sample Content Preview"}),(0,a.jsx)(u.BT,{children:"See how different content types are affected by font size changes"})]}),(0,a.jsxs)(u.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"mb-2 text-xl font-semibold",children:"Heading Text"}),(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"WorkHub Font Size Demonstration"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"mb-2 text-xl font-semibold",children:"Paragraph Text"}),(0,a.jsx)("p",{className:"leading-relaxed",children:"This page demonstrates how font size changes affect the entire application. The font size setting is applied globally and affects all text content throughout WorkHub."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"mb-2 text-xl font-semibold",children:"List Items"}),(0,a.jsx)("ul",{className:"list-inside list-disc space-y-1",children:["Dashboard statistics and metrics","Navigation menu items","Form labels and input text","Table data and headers","Button text and descriptions"].map((e,s)=>(0,a.jsx)("li",{children:e},s))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"mb-2 text-xl font-semibold",children:"Quote Text"}),(0,a.jsx)("blockquote",{className:"border-l-4 border-primary pl-4 italic text-muted-foreground",children:"Good typography is invisible. Bad typography is everywhere."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"mb-2 text-xl font-semibold",children:"Secondary Text"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"This is secondary text that appears in descriptions, captions, and helper text throughout the application."})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"mb-4 text-2xl font-semibold",children:"Complete Font Size Settings"}),(0,a.jsx)(o.uq,{})]}),(0,a.jsxs)(u.Zp,{children:[(0,a.jsx)(u.aR,{children:(0,a.jsx)(u.ZB,{children:"How to Use Font Size Controls"})}),(0,a.jsxs)(u.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-2 font-medium",children:"1. Navigation Bar"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:'Click the "A" icon in the navigation bar for quick font size controls.'})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-2 font-medium",children:"2. Settings Page"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Visit the Settings page for comprehensive font size management with preview."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-2 font-medium",children:"3. This Demo Page"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Use this page to test font size changes and see how they affect different content types."})]})]})]})]})}},27945:(e,s,t)=>{"use strict";t.d(s,{A5:()=>u,Ln:()=>x,uq:()=>m});var a=t(95155),r=t(73350),i=t(10518);t(12115);var n=t(26126),l=t(30285),c=t(66695),o=t(36846);let d=[{className:"text-sm",description:"Compact text for more content",example:"The quick brown fox jumps over the lazy dog",label:"Small",value:"small"},{className:"text-base",description:"Standard readable text",example:"The quick brown fox jumps over the lazy dog",label:"Medium",value:"medium"},{className:"text-lg",description:"Larger text for better accessibility",example:"The quick brown fox jumps over the lazy dog",label:"Large",value:"large"}],m=()=>{let{fontSize:e,getFontSizeClass:s,setFontSize:t}=(0,o.useUiPreferences)();return(0,a.jsxs)(c.Zp,{className:"w-full max-w-2xl",children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(r.A,{className:"size-5"}),"Font Size Preferences"]}),(0,a.jsx)(c.BT,{children:"Choose your preferred font size for better readability and accessibility"})]}),(0,a.jsxs)(c.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-muted/50 p-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Current Font Size"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Applied across the entire application"})]}),(0,a.jsx)(n.E,{className:"capitalize",variant:"secondary",children:e})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium uppercase tracking-wide text-muted-foreground",children:"Available Options"}),d.map(s=>(0,a.jsx)("div",{className:"\n                relative cursor-pointer rounded-lg border p-4 transition-all\n                ".concat(e===s.value?"border-primary bg-primary/5 ring-1 ring-primary/20":"border-border hover:border-primary/50 hover:bg-muted/30","\n              "),onClick:()=>t(s.value),children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:[(0,a.jsx)("h5",{className:"font-medium",children:s.label}),e===s.value&&(0,a.jsx)(i.A,{className:"size-4 text-primary"})]}),(0,a.jsx)("p",{className:"mb-3 text-sm text-muted-foreground",children:s.description}),(0,a.jsx)("div",{className:"rounded border bg-background p-3 ".concat(s.className),children:s.example})]})})},s.value))]}),(0,a.jsxs)("div",{className:"flex items-center justify-between border-t pt-4",children:[(0,a.jsx)("div",{className:"flex gap-2",children:d.map(s=>(0,a.jsx)(l.$,{className:"capitalize",onClick:()=>t(s.value),size:"sm",variant:e===s.value?"default":"outline",children:s.label},s.value))}),(0,a.jsx)(l.$,{className:"text-muted-foreground",onClick:()=>t("medium"),size:"sm",variant:"ghost",children:"Reset to Default"})]}),(0,a.jsxs)("div",{className:"rounded-lg border bg-background p-4",children:[(0,a.jsx)("h5",{className:"mb-2 font-medium",children:"Live Preview"}),(0,a.jsxs)("div",{className:"space-y-2 ".concat(s()),children:[(0,a.jsx)("p",{className:"font-semibold",children:"Heading Text"}),(0,a.jsx)("p",{children:"This is how regular paragraph text will appear with your selected font size. The setting applies to all text content throughout the WorkHub application."}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Secondary text and descriptions will also scale accordingly."})]})]})]})]})},u=()=>{let{fontSize:e,setFontSize:s}=(0,o.useUiPreferences)();return(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(r.A,{className:"size-4 text-muted-foreground"}),(0,a.jsxs)("select",{className:"   rounded border border-input bg-background px-2 py-1 text-sm   focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2   ",onChange:e=>s(e.target.value),value:e,children:[(0,a.jsx)("option",{value:"small",children:"Small"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"large",children:"Large"})]})]})},x=()=>{let{fontSize:e,setFontSize:s}=(0,o.useUiPreferences)();return(0,a.jsxs)(l.$,{className:"flex items-center gap-2",onClick:()=>{let t=["small","medium","large"],a=(t.indexOf(e)+1)%t.length,r=t[a];r&&s(r)},size:"sm",title:"Current: ".concat(e,". Click to cycle."),variant:"ghost",children:[(0,a.jsx)(r.A,{className:"size-4"}),(0,a.jsx)("span",{className:"text-xs capitalize",children:e})]})}},30285:(e,s,t)=>{"use strict";t.d(s,{$:()=>o,r:()=>c});var a=t(95155),r=t(12115),i=t(99708),n=t(74466),l=t(54036);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,s)=>{let{className:t,variant:r,size:n,asChild:o=!1,...d}=e,m=o?i.DX:"button";return(0,a.jsx)(m,{className:(0,l.cn)(c({variant:r,size:n,className:t})),ref:s,...d})});o.displayName="Button"},36846:(e,s,t)=>{"use strict";t.r(s),t.d(s,{useUiPreferences:()=>i});var a=t(12115),r=t(26119);let i=()=>{let e=(0,r.n)(e=>e.fontSize),s=(0,r.n)(e=>e.setFontSize),t=(0,r.n)(e=>e.notificationsEnabled),i=(0,r.n)(e=>e.toggleNotifications),n=(0,r.n)(e=>e.tableDensity),l=(0,r.n)(e=>e.setTableDensity),c=(0,r.n)(e=>e.mapViewPreference),o=(0,r.n)(e=>e.setMapViewPreference),d=(0,r.n)(e=>e.dashboardLayout),m=(0,r.n)(e=>e.setDashboardLayout),u=(0,r.n)(e=>e.autoRefreshInterval),x=(0,r.n)(e=>e.setAutoRefreshInterval),h=(0,a.useCallback)(()=>{switch(e){case"large":return"text-lg";case"small":return"text-sm";default:return"text-base"}},[e]),f=(0,a.useCallback)(()=>{switch(n){case"compact":return{cell:"py-1 px-2",row:"h-8",table:"table-compact"};case"spacious":return{cell:"py-4 px-4",row:"h-16",table:"table-spacious"};default:return{cell:"py-2 px-3",row:"h-12",table:"table-comfortable"}}},[n]),p=(0,a.useCallback)(()=>{switch(d){case"cards":default:return"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6";case"grid":return"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4";case"list":return"flex flex-col space-y-4"}},[d]),g=(0,a.useCallback)(()=>{t||i()},[t,i]),b=(0,a.useCallback)(()=>{t&&i()},[t,i]),v=(0,a.useCallback)(()=>{s("medium"),l("comfortable"),o("roadmap"),m("cards"),x(30)},[s,l,o,m,x]),j=(0,a.useCallback)(()=>({autoRefreshInterval:u,dashboardLayout:d,fontSize:e,mapViewPreference:c,notificationsEnabled:t,tableDensity:n}),[e,t,n,c,d,u]);return{autoRefreshInterval:u,dashboardLayout:d,disableNotifications:b,enableNotifications:g,fontSize:e,getAllPreferences:j,getDashboardLayoutClasses:p,getFontSizeClass:h,getTableDensityClasses:f,mapViewPreference:c,notificationsEnabled:t,resetPreferences:v,setAutoRefreshInterval:x,setDashboardLayout:m,setFontSize:s,setMapViewPreference:o,setTableDensity:l,tableDensity:n,toggleNotifications:i}}},39116:(e,s,t)=>{Promise.resolve().then(t.bind(t,26642))},66695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>n,aR:()=>l,wL:()=>m});var a=t(95155),r=t(12115),i=t(54036);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),ref:s,...r})});n.displayName="Card";let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),ref:s,...r})});l.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",t),ref:s,...r})});c.displayName="CardTitle";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)("text-sm text-muted-foreground",t),ref:s,...r})});o.displayName="CardDescription";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)("p-6 pt-0",t),ref:s,...r})});d.displayName="CardContent";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex items-center p-6 pt-0",t),ref:s,...r})});m.displayName="CardFooter"}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,6874,5019,4036,8441,1684,7358],()=>s(39116)),_N_E=e.O()}]);