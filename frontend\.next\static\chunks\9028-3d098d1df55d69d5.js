"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9028],{2602:(e,t,a)=>{a.d(t,{q:()=>u});var r=a(7165),i=a(34560),s=a(52020),n=a(25910),u=class extends n.Q{constructor(e={}){super(),this.config=e,this.#e=new Set,this.#t=new Map,this.#a=0}#e;#t;#a;build(e,t,a){let r=new i.s({mutationCache:this,mutationId:++this.#a,options:e.defaultMutationOptions(t),state:a});return this.add(r),r}add(e){this.#e.add(e);let t=o(e);if("string"==typeof t){let a=this.#t.get(t);a?a.push(e):this.#t.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#e.delete(e)){let t=o(e);if("string"==typeof t){let a=this.#t.get(t);if(a)if(a.length>1){let t=a.indexOf(e);-1!==t&&a.splice(t,1)}else a[0]===e&&this.#t.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=o(e);if("string"!=typeof t)return!0;{let a=this.#t.get(t),r=a?.find(e=>"pending"===e.state.status);return!r||r===e}}runNext(e){let t=o(e);if("string"!=typeof t)return Promise.resolve();{let a=this.#t.get(t)?.find(t=>t!==e&&t.state.isPaused);return a?.continue()??Promise.resolve()}}clear(){r.jG.batch(()=>{this.#e.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#e.clear(),this.#t.clear()})}getAll(){return Array.from(this.#e)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,s.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,s.nJ)(e,t))}notify(e){r.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return r.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(s.lQ))))}};function o(e){return e.options.scope?.id}},3638:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},5263:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},19968:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},27461:(e,t,a)=>{a.d(t,{E:()=>c});var r=a(52020),i=a(55513),s=a(2602),n=a(50920),u=a(21239),o=a(7165);function l(e){return{onFetch:(t,a)=>{let i=t.options,s=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],u=t.state.data?.pageParams||[],o={pages:[],pageParams:[]},l=0,c=async()=>{let a=!1,c=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?a=!0:t.signal.addEventListener("abort",()=>{a=!0}),t.signal)})},d=(0,r.ZM)(t.options,t.fetchOptions),f=async(e,i,s)=>{if(a)return Promise.reject();if(null==i&&e.pages.length)return Promise.resolve(e);let n=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:i,direction:s?"backward":"forward",meta:t.options.meta};return c(e),e})(),u=await d(n),{maxPages:o}=t.options,l=s?r.ZZ:r.y9;return{pages:l(e.pages,u,o),pageParams:l(e.pageParams,i,o)}};if(s&&n.length){let e="backward"===s,t={pages:n,pageParams:u},a=(e?function(e,{pages:t,pageParams:a}){return t.length>0?e.getPreviousPageParam?.(t[0],t,a[0],a):void 0}:h)(i,t);o=await f(t,a,e)}else{let t=e??n.length;do{let e=0===l?u[0]??i.initialPageParam:h(i,o);if(l>0&&null==e)break;o=await f(o,e),l++}while(l<t)}return o};t.options.persister?t.fetchFn=()=>t.options.persister?.(c,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},a):t.fetchFn=c}}}function h(e,{pages:t,pageParams:a}){let r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,a[r],a):void 0}var c=class{#r;#i;#s;#n;#u;#o;#l;#h;constructor(e={}){this.#r=e.queryCache||new i.$,this.#i=e.mutationCache||new s.q,this.#s=e.defaultOptions||{},this.#n=new Map,this.#u=new Map,this.#o=0}mount(){this.#o++,1===this.#o&&(this.#l=n.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#r.onFocus())}),this.#h=u.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#r.onOnline())}))}unmount(){this.#o--,0===this.#o&&(this.#l?.(),this.#l=void 0,this.#h?.(),this.#h=void 0)}isFetching(e){return this.#r.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#i.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#r.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),a=this.#r.build(this,t),i=a.state.data;return void 0===i?this.fetchQuery(e):(e.revalidateIfStale&&a.isStaleByTime((0,r.d2)(t.staleTime,a))&&this.prefetchQuery(t),Promise.resolve(i))}getQueriesData(e){return this.#r.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,a){let i=this.defaultQueryOptions({queryKey:e}),s=this.#r.get(i.queryHash),n=s?.state.data,u=(0,r.Zw)(t,n);if(void 0!==u)return this.#r.build(this,i).setData(u,{...a,manual:!0})}setQueriesData(e,t,a){return o.jG.batch(()=>this.#r.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,a)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#r.get(t.queryHash)?.state}removeQueries(e){let t=this.#r;o.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let a=this.#r;return o.jG.batch(()=>(a.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let a={revert:!0,...t};return Promise.all(o.jG.batch(()=>this.#r.findAll(e).map(e=>e.cancel(a)))).then(r.lQ).catch(r.lQ)}invalidateQueries(e,t={}){return o.jG.batch(()=>(this.#r.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let a={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(o.jG.batch(()=>this.#r.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,a);return a.throwOnError||(t=t.catch(r.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(r.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let a=this.#r.build(this,t);return a.isStaleByTime((0,r.d2)(t.staleTime,a))?a.fetch(t):Promise.resolve(a.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(r.lQ).catch(r.lQ)}fetchInfiniteQuery(e){return e.behavior=l(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(r.lQ).catch(r.lQ)}ensureInfiniteQueryData(e){return e.behavior=l(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return u.t.isOnline()?this.#i.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#r}getMutationCache(){return this.#i}getDefaultOptions(){return this.#s}setDefaultOptions(e){this.#s=e}setQueryDefaults(e,t){this.#n.set((0,r.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#n.values()],a={};return t.forEach(t=>{(0,r.Cp)(e,t.queryKey)&&Object.assign(a,t.defaultOptions)}),a}setMutationDefaults(e,t){this.#u.set((0,r.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#u.values()],a={};return t.forEach(t=>{(0,r.Cp)(e,t.mutationKey)&&Object.assign(a,t.defaultOptions)}),a}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#s.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,r.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===r.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#s.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#r.clear(),this.#i.clear()}}},29159:(e,t,a)=>{a.d(t,{s:()=>i});class r extends Error{}function i(e,t){let a;if("string"!=typeof e)throw new r("Invalid token specified: must be a string");t||(t={});let i=+(!0!==t.header),s=e.split(".")[i];if("string"!=typeof s)throw new r(`Invalid token specified: missing part #${i+1}`);try{a=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw Error("base64 string is not of the correct length")}try{var a;return a=t,decodeURIComponent(atob(a).replace(/(.)/g,(e,t)=>{let a=t.charCodeAt(0).toString(16).toUpperCase();return a.length<2&&(a="0"+a),"%"+a}))}catch(e){return atob(t)}}(s)}catch(e){throw new r(`Invalid token specified: invalid base64 for part #${i+1} (${e.message})`)}try{return JSON.parse(a)}catch(e){throw new r(`Invalid token specified: invalid json for part #${i+1} (${e.message})`)}}r.prototype.name="InvalidTokenError"},55513:(e,t,a)=>{a.d(t,{$:()=>u});var r=a(52020),i=a(39853),s=a(7165),n=a(25910),u=class extends n.Q{constructor(e={}){super(),this.config=e,this.#c=new Map}#c;build(e,t,a){let s=t.queryKey,n=t.queryHash??(0,r.F$)(s,t),u=this.get(n);return u||(u=new i.X({client:e,queryKey:s,queryHash:n,options:e.defaultQueryOptions(t),state:a,defaultOptions:e.getQueryDefaults(s)}),this.add(u)),u}add(e){this.#c.has(e.queryHash)||(this.#c.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#c.get(e.queryHash);t&&(e.destroy(),t===e&&this.#c.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){s.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#c.get(e)}getAll(){return[...this.#c.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,r.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,r.MK)(e,t)):t}notify(e){s.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){s.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){s.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}}},60704:(e,t,a)=>{a.d(t,{B8:()=>D,UC:()=>E,bL:()=>k,l9:()=>j});var r=a(12115),i=a(85185),s=a(46081),n=a(89196),u=a(28905),o=a(63655),l=a(94315),h=a(5845),c=a(61285),d=a(95155),f="Tabs",[y,p]=(0,s.A)(f,[n.RG]),m=(0,n.RG)(),[g,b]=y(f),v=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,onValueChange:i,defaultValue:s,orientation:n="horizontal",dir:u,activationMode:y="automatic",...p}=e,m=(0,l.jH)(u),[b,v]=(0,h.i)({prop:r,onChange:i,defaultProp:null!=s?s:"",caller:f});return(0,d.jsx)(g,{scope:a,baseId:(0,c.B)(),value:b,onValueChange:v,orientation:n,dir:m,activationMode:y,children:(0,d.jsx)(o.sG.div,{dir:m,"data-orientation":n,...p,ref:t})})});v.displayName=f;var q="TabsList",C=r.forwardRef((e,t)=>{let{__scopeTabs:a,loop:r=!0,...i}=e,s=b(q,a),u=m(a);return(0,d.jsx)(n.bL,{asChild:!0,...u,orientation:s.orientation,dir:s.dir,loop:r,children:(0,d.jsx)(o.sG.div,{role:"tablist","aria-orientation":s.orientation,...i,ref:t})})});C.displayName=q;var w="TabsTrigger",Q=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,disabled:s=!1,...u}=e,l=b(w,a),h=m(a),c=M(l.baseId,r),f=P(l.baseId,r),y=r===l.value;return(0,d.jsx)(n.q7,{asChild:!0,...h,focusable:!s,active:y,children:(0,d.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":y,"aria-controls":f,"data-state":y?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:c,...u,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{s||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(r)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(r)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;y||s||!e||l.onValueChange(r)})})})});Q.displayName=w;var O="TabsContent",A=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:i,forceMount:s,children:n,...l}=e,h=b(O,a),c=M(h.baseId,i),f=P(h.baseId,i),y=i===h.value,p=r.useRef(y);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,d.jsx)(u.C,{present:s||y,children:a=>{let{present:r}=a;return(0,d.jsx)(o.sG.div,{"data-state":y?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:f,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&n})}})});function M(e,t){return"".concat(e,"-trigger-").concat(t)}function P(e,t){return"".concat(e,"-content-").concat(t)}A.displayName=O;var k=v,D=C,j=Q,E=A},80659:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(40157).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},87489:(e,t,a)=>{a.d(t,{b:()=>l});var r=a(12115),i=a(63655),s=a(95155),n="horizontal",u=["horizontal","vertical"],o=r.forwardRef((e,t)=>{var a;let{decorative:r,orientation:o=n,...l}=e,h=(a=o,u.includes(a))?o:n;return(0,s.jsx)(i.sG.div,{"data-orientation":h,...r?{role:"none"}:{"aria-orientation":"vertical"===h?h:void 0,role:"separator"},...l,ref:t})});o.displayName="Separator";var l=o}}]);