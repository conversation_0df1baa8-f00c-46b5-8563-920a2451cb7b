/**
 * @file Centralized exports for the API service layer.
 * @module api
 */

// Core infrastructure
export * from './core/apiClient';
export * from './core/baseApiService';
export * from './core/errors';
export * from './core/types';
export * from './core/interfaces';

// Create and export a configured API client instance
import { ApiClient } from './core/apiClient';
import { getEnvironmentConfig } from '../config/environment';

// Global auth token provider - will be set by the auth context
let globalAuthTokenProvider: (() => string | null) | null = null;

/**
 * Set the global auth token provider
 * This should be called by the AuthContext when authentication is initialized
 */
export function setGlobalAuthTokenProvider(provider: () => string | null) {
  globalAuthTokenProvider = provider;
}

/**
 * Get the current auth token from the global provider
 */
function getAuthToken(): string | null {
  return globalAuthTokenProvider ? globalAuthTokenProvider() : null;
}

/**
 * Get the global auth token provider (for testing/debugging)
 */
export function getGlobalAuthTokenProvider(): (() => string | null) | null {
  return globalAuthTokenProvider;
}

// Get environment-aware configuration
const envConfig = getEnvironmentConfig();

export const apiClient = new ApiClient({
  baseURL: envConfig.apiBaseUrl, // Use environment-aware configuration
  getAuthToken, // Provide the auth token getter
  headers: {
    'Content-Type': 'application/json',
  },
  retryAttempts: 3,
  timeout: 10_000,
});

// Security architecture (selective exports to avoid conflicts)
export {
  // Security hooks
  useSecureApiClient,
  useSecureApiReplacement,
  useSecureHttpClient,
  useCSRFProtection,
  useInputValidation,
  useSecurityMonitoring,
  useSessionSecurity,
  useTokenManagement,
  // Security providers
  SecurityConfigProvider,
  useSecurityConfig,
  useSecurityConfigValue,
  // Security composer
  SecurityComposer,
  createSecurityComposer,
  // Secure API client
  SecureApiClient,
  createSecureApiClient,
} from './security';

export type {
  // Security types (avoid RequestConfig conflict)
  UseSecureApiClientReturn,
  SecureApiRequestConfig,
  UseSecureHttpClientReturn,
  SecurityFeatures,
  SecureApiClientConfig,
} from './security';

// Domain-specific API services
export * from './services/domain/delegationApi';
export * from './services/domain/employeeApi';
export * from './services/domain/taskApi';
export * from './services/domain/vehicleApi';

// External API services
export * from './services/external/flightApi';
export * from './services/external/flightDetailsApi';
