{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/app-breadcrumb.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppBreadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppBreadcrumb() from the server but AppBreadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/app-breadcrumb.tsx <module evaluation>\",\n    \"AppBreadcrumb\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,sEACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/app-breadcrumb.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AppBreadcrumb = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppBreadcrumb() from the server but AppBreadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/app-breadcrumb.tsx\",\n    \"AppBreadcrumb\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,kDACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/app/reports/layout.tsx"], "sourcesContent": ["// frontend/src/app/reports/layout.tsx\r\n\r\nimport type { Metadata } from 'next';\r\n\r\nimport { AppBreadcrumb } from '@/components/ui/app-breadcrumb';\r\n\r\nexport const metadata: Metadata = {\r\n  description: 'Comprehensive reporting and analytics dashboard',\r\n  title: 'Reports - WorkHub',\r\n};\r\n\r\ninterface ReportsLayoutProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\n/**\r\n * Reports Layout - Simplified Hierarchy\r\n *\r\n * Provides clean, focused layout for all reporting pages.\r\n * Removed redundant navigation cards per UX recommendations.\r\n */\r\nexport default function ReportsLayout({ children }: ReportsLayoutProps) {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Streamlined Header */}\r\n      <div className=\"flex flex-col space-y-4\">\r\n        <AppBreadcrumb homeHref=\"/\" homeLabel=\"Dashboard\" />\r\n        <div className=\"flex items-center justify-between\">\r\n          <div>\r\n            <h1 className=\"text-3xl font-bold tracking-tight\">\r\n              Reports & Analytics\r\n            </h1>\r\n            <p className=\"text-muted-foreground mt-2\">\r\n              Comprehensive delegation analytics and reporting\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main Content - Direct access to dashboard */}\r\n      <div className=\"min-h-[600px]\">{children}</div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;;;AAItC;;;AAEO,MAAM,WAAqB;IAChC,aAAa;IACb,OAAO;AACT;AAYe,SAAS,cAAc,EAAE,QAAQ,EAAsB;IACpE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6IAAA,CAAA,gBAAa;wBAAC,UAAS;wBAAI,WAAU;;;;;;kCACtC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;0BAQhD,8OAAC;gBAAI,WAAU;0BAAiB;;;;;;;;;;;;AAGtC", "debugId": null}}]}