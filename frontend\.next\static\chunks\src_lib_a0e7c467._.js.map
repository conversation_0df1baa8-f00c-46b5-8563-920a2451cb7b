{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/services/toastService.ts"], "sourcesContent": ["/**\r\n * Unified Generic Toast Service - Following SRP and DRY principles\r\n *\r\n * This service provides a consistent interface for all toast notifications\r\n * across the application, eliminating code duplication and ensuring\r\n * consistent messaging patterns using generics.\r\n */\r\n\r\nimport { toast } from '@/hooks/utils/use-toast';\r\n\r\nexport type ToastVariant = 'default' | 'destructive';\r\n\r\nexport interface ToastOptions {\r\n  title?: string | undefined;\r\n  description?: string | undefined;\r\n  variant?: ToastVariant | undefined;\r\n  duration?: number | undefined;\r\n}\r\n\r\n/**\r\n * Configuration interface for entity-specific toast messages\r\n */\r\nexport interface EntityToastConfig<T = any> {\r\n  entityName: string; // e.g., \"Employee\", \"Vehicle\", \"Service Record\"\r\n  getDisplayName: (entity: T) => string; // Function to get display name from entity\r\n  messages: {\r\n    created: {\r\n      title: string;\r\n      description: (displayName: string) => string;\r\n    };\r\n    updated: {\r\n      title: string;\r\n      description: (displayName: string) => string;\r\n    };\r\n    deleted: {\r\n      title: string;\r\n      description: (displayName: string) => string;\r\n    };\r\n    creationError: {\r\n      title: string;\r\n      description: (error: string) => string;\r\n    };\r\n    updateError: {\r\n      title: string;\r\n      description: (error: string) => string;\r\n    };\r\n    deletionError: {\r\n      title: string;\r\n      description: (error: string) => string;\r\n    };\r\n  };\r\n}\r\n\r\n/**\r\n * Base toast service class following SRP\r\n */\r\nclass ToastService {\r\n  /**\r\n   * Show a generic toast notification\r\n   */\r\n  show(options: ToastOptions) {\r\n    return toast({\r\n      title: options.title,\r\n      description: options.description,\r\n      variant: options.variant || 'default',\r\n      ...(options.duration && { duration: options.duration }),\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show a success toast notification\r\n   */\r\n  success(title: string, description?: string) {\r\n    return this.show({\r\n      title,\r\n      description,\r\n      variant: 'default',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show an error toast notification\r\n   */\r\n  error(title: string, description?: string) {\r\n    return this.show({\r\n      title,\r\n      description,\r\n      variant: 'destructive',\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show an info toast notification\r\n   */\r\n  info(title: string, description?: string) {\r\n    return this.show({\r\n      title,\r\n      description,\r\n      variant: 'default',\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * Generic Entity Toast Service - Works with any entity type T\r\n */\r\nexport class GenericEntityToastService<T = any> extends ToastService {\r\n  private config: EntityToastConfig<T>;\r\n\r\n  constructor(config: EntityToastConfig<T>) {\r\n    super();\r\n    this.config = config;\r\n  }\r\n\r\n  /**\r\n   * Show entity created success toast\r\n   */\r\n  entityCreated(entity: T) {\r\n    const displayName = this.config.getDisplayName(entity);\r\n    return this.success(\r\n      this.config.messages.created.title,\r\n      this.config.messages.created.description(displayName)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity updated success toast\r\n   */\r\n  entityUpdated(entity: T) {\r\n    const displayName = this.config.getDisplayName(entity);\r\n    return this.success(\r\n      this.config.messages.updated.title,\r\n      this.config.messages.updated.description(displayName)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity deleted success toast\r\n   */\r\n  entityDeleted(entity: T) {\r\n    const displayName = this.config.getDisplayName(entity);\r\n    return this.success(\r\n      this.config.messages.deleted.title,\r\n      this.config.messages.deleted.description(displayName)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity creation error toast\r\n   */\r\n  entityCreationError(error: string) {\r\n    return this.error(\r\n      this.config.messages.creationError.title,\r\n      this.config.messages.creationError.description(error)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity update error toast\r\n   */\r\n  entityUpdateError(error: string) {\r\n    return this.error(\r\n      this.config.messages.updateError.title,\r\n      this.config.messages.updateError.description(error)\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show entity deletion error toast\r\n   */\r\n  entityDeletionError(error: string) {\r\n    return this.error(\r\n      this.config.messages.deletionError.title,\r\n      this.config.messages.deletionError.description(error)\r\n    );\r\n  }\r\n}\r\n\r\n// =============================================================================\r\n// ENTITY CONFIGURATIONS - Define toast messages for each domain\r\n// =============================================================================\r\n\r\n/**\r\n * Employee entity toast configuration\r\n */\r\nconst employeeToastConfig: EntityToastConfig<{ name: string }> = {\r\n  entityName: 'Employee',\r\n  getDisplayName: employee => employee.name,\r\n  messages: {\r\n    created: {\r\n      title: 'Employee Added',\r\n      description: name =>\r\n        `The employee \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Employee Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Employee Deleted Successfully',\r\n      description: name =>\r\n        `${name} has been permanently removed from the system.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Employee',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the employee.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the employee.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Employee',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the employee.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Delegation entity toast configuration\r\n */\r\nconst delegationToastConfig: EntityToastConfig<{\r\n  event?: string;\r\n  location?: string;\r\n}> = {\r\n  entityName: 'Delegation',\r\n  getDisplayName: delegation =>\r\n    delegation.event || delegation.location || 'Delegation',\r\n  messages: {\r\n    created: {\r\n      title: 'Delegation Created',\r\n      description: name =>\r\n        `The delegation \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Delegation Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Delegation Deleted Successfully',\r\n      description: name => `${name} has been permanently removed.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Delegation',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the delegation.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the delegation.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Delegation',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the delegation.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Vehicle entity toast configuration\r\n */\r\nconst vehicleToastConfig: EntityToastConfig<{ make: string; model: string }> = {\r\n  entityName: 'Vehicle',\r\n  getDisplayName: vehicle => `${vehicle.make} ${vehicle.model}`,\r\n  messages: {\r\n    created: {\r\n      title: 'Vehicle Added',\r\n      description: name =>\r\n        `The vehicle \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Vehicle Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Vehicle Deleted Successfully',\r\n      description: name => `${name} has been permanently removed.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Vehicle',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the vehicle.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the vehicle.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Vehicle',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the vehicle.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Task entity toast configuration\r\n */\r\nconst taskToastConfig: EntityToastConfig<{ title?: string; name?: string }> = {\r\n  entityName: 'Task',\r\n  getDisplayName: task => task.title || task.name || 'Task',\r\n  messages: {\r\n    created: {\r\n      title: 'Task Created',\r\n      description: name => `The task \"${name}\" has been successfully created.`,\r\n    },\r\n    updated: {\r\n      title: 'Task Updated Successfully',\r\n      description: name => `${name} has been updated.`,\r\n    },\r\n    deleted: {\r\n      title: 'Task Deleted Successfully',\r\n      description: name => `${name} has been permanently removed.`,\r\n    },\r\n    creationError: {\r\n      title: 'Failed to Create Task',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while creating the task.',\r\n    },\r\n    updateError: {\r\n      title: 'Update Failed',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while updating the task.',\r\n    },\r\n    deletionError: {\r\n      title: 'Failed to Delete Task',\r\n      description: error =>\r\n        error || 'An unexpected error occurred while deleting the task.',\r\n    },\r\n  },\r\n};\r\n\r\n/**\r\n * Service Record-specific toast messages following DRY principles\r\n */\r\nexport class ServiceRecordToastService extends ToastService {\r\n  /**\r\n   * Show service record created success toast\r\n   */\r\n  serviceRecordCreated(vehicleName: string, serviceType: string) {\r\n    return this.success(\r\n      'Service Record Added',\r\n      `${serviceType} service for \"${vehicleName}\" has been successfully logged.`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record updated success toast\r\n   */\r\n  serviceRecordUpdated(vehicleName: string, serviceType: string) {\r\n    return this.success(\r\n      'Service Record Updated',\r\n      `${serviceType} service for \"${vehicleName}\" has been updated.`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record deleted success toast\r\n   */\r\n  serviceRecordDeleted(vehicleName: string, serviceType: string) {\r\n    return this.success(\r\n      'Service Record Deleted',\r\n      `${serviceType} service record for \"${vehicleName}\" has been permanently removed.`\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record creation error toast\r\n   */\r\n  serviceRecordCreationError(error: string) {\r\n    return this.error(\r\n      'Failed to Log Service Record',\r\n      error || 'An unexpected error occurred while logging the service record.'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record update error toast\r\n   */\r\n  serviceRecordUpdateError(error: string) {\r\n    return this.error(\r\n      'Update Failed',\r\n      error || 'An unexpected error occurred while updating the service record.'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Show service record deletion error toast\r\n   */\r\n  serviceRecordDeletionError(error: string) {\r\n    return this.error(\r\n      'Failed to Delete Service Record',\r\n      error || 'An unexpected error occurred while deleting the service record.'\r\n    );\r\n  }\r\n}\r\n\r\n// =============================================================================\r\n// UTILITY FACTORY FUNCTIONS\r\n// =============================================================================\r\n\r\n/**\r\n * Factory function to create a generic entity toast service for any entity type\r\n * Useful for creating toast services for new entities without pre-configuration\r\n */\r\nexport function createEntityToastService<T>(config: EntityToastConfig<T>) {\r\n  return new GenericEntityToastService<T>(config);\r\n}\r\n\r\n/**\r\n * Factory function to create a simple entity toast service with minimal configuration\r\n * For entities that only need basic CRUD messages\r\n */\r\nexport function createSimpleEntityToastService<T>(\r\n  entityName: string,\r\n  getDisplayName: (entity: T) => string\r\n): GenericEntityToastService<T> {\r\n  const config: EntityToastConfig<T> = {\r\n    entityName,\r\n    getDisplayName,\r\n    messages: {\r\n      created: {\r\n        title: `${entityName} Created`,\r\n        description: (displayName: string) =>\r\n          `The ${entityName.toLowerCase()} \"${displayName}\" has been successfully created.`,\r\n      },\r\n      updated: {\r\n        title: `${entityName} Updated Successfully`,\r\n        description: (displayName: string) =>\r\n          `${displayName} has been updated.`,\r\n      },\r\n      deleted: {\r\n        title: `${entityName} Deleted Successfully`,\r\n        description: (displayName: string) =>\r\n          `${displayName} has been permanently removed.`,\r\n      },\r\n      creationError: {\r\n        title: `Failed to Create ${entityName}`,\r\n        description: (error: string) =>\r\n          error ||\r\n          `An unexpected error occurred while creating the ${entityName.toLowerCase()}.`,\r\n      },\r\n      updateError: {\r\n        title: 'Update Failed',\r\n        description: (error: string) =>\r\n          error ||\r\n          `An unexpected error occurred while updating the ${entityName.toLowerCase()}.`,\r\n      },\r\n      deletionError: {\r\n        title: `Failed to Delete ${entityName}`,\r\n        description: (error: string) =>\r\n          error ||\r\n          `An unexpected error occurred while deleting the ${entityName.toLowerCase()}.`,\r\n      },\r\n    },\r\n  };\r\n\r\n  return new GenericEntityToastService<T>(config);\r\n}\r\n\r\n// =============================================================================\r\n// SINGLETON INSTANCES - Pre-configured toast services for each domain\r\n// =============================================================================\r\n\r\n// Base toast service for generic use\r\nexport const toastService = new ToastService();\r\n\r\n// Entity-specific toast services using the generic service\r\nexport const employeeToast = new GenericEntityToastService(employeeToastConfig);\r\nexport const delegationToast = new GenericEntityToastService(\r\n  delegationToastConfig\r\n);\r\nexport const vehicleToast = new GenericEntityToastService(vehicleToastConfig);\r\nexport const taskToast = new GenericEntityToastService(taskToastConfig);\r\nexport const serviceRecordToast = new ServiceRecordToastService();\r\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;;AAED;;AA6CA;;CAEC,GACD,MAAM;IACJ;;GAEC,GACD,KAAK,OAAqB,EAAE;QAC1B,OAAO,CAAA,GAAA,wIAAA,CAAA,QAAK,AAAD,EAAE;YACX,OAAO,QAAQ,KAAK;YACpB,aAAa,QAAQ,WAAW;YAChC,SAAS,QAAQ,OAAO,IAAI;YAC5B,GAAI,QAAQ,QAAQ,IAAI;gBAAE,UAAU,QAAQ,QAAQ;YAAC,CAAC;QACxD;IACF;IAEA;;GAEC,GACD,QAAQ,KAAa,EAAE,WAAoB,EAAE;QAC3C,OAAO,IAAI,CAAC,IAAI,CAAC;YACf;YACA;YACA,SAAS;QACX;IACF;IAEA;;GAEC,GACD,MAAM,KAAa,EAAE,WAAoB,EAAE;QACzC,OAAO,IAAI,CAAC,IAAI,CAAC;YACf;YACA;YACA,SAAS;QACX;IACF;IAEA;;GAEC,GACD,KAAK,KAAa,EAAE,WAAoB,EAAE;QACxC,OAAO,IAAI,CAAC,IAAI,CAAC;YACf;YACA;YACA,SAAS;QACX;IACF;AACF;AAKO,MAAM,kCAA2C;IAC9C,OAA6B;IAErC,YAAY,MAA4B,CAAE;QACxC,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA;;GAEC,GACD,cAAc,MAAS,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IAE7C;IAEA;;GAEC,GACD,cAAc,MAAS,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IAE7C;IAEA;;GAEC,GACD,cAAc,MAAS,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAClC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;IAE7C;IAEA;;GAEC,GACD,oBAAoB,KAAa,EAAE;QACjC,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EACxC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC;IAEnD;IAEA;;GAEC,GACD,kBAAkB,KAAa,EAAE;QAC/B,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,EACtC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC;IAEjD;IAEA;;GAEC,GACD,oBAAoB,KAAa,EAAE;QACjC,OAAO,IAAI,CAAC,KAAK,CACf,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,EACxC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC;IAEnD;AACF;AAEA,gFAAgF;AAChF,gEAAgE;AAChE,gFAAgF;AAEhF;;CAEC,GACD,MAAM,sBAA2D;IAC/D,YAAY;IACZ,gBAAgB,CAAA,WAAY,SAAS,IAAI;IACzC,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,CAAC,cAAc,EAAE,KAAK,gCAAgC,CAAC;QAC3D;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,GAAG,KAAK,8CAA8C,CAAC;QAC3D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAEA;;CAEC,GACD,MAAM,wBAGD;IACH,YAAY;IACZ,gBAAgB,CAAA,aACd,WAAW,KAAK,IAAI,WAAW,QAAQ,IAAI;IAC7C,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,CAAC,gBAAgB,EAAE,KAAK,gCAAgC,CAAC;QAC7D;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,8BAA8B,CAAC;QAC9D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAEA;;CAEC,GACD,MAAM,qBAAyE;IAC7E,YAAY;IACZ,gBAAgB,CAAA,UAAW,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,EAAE;IAC7D,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OACX,CAAC,aAAa,EAAE,KAAK,gCAAgC,CAAC;QAC1D;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,8BAA8B,CAAC;QAC9D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAEA;;CAEC,GACD,MAAM,kBAAwE;IAC5E,YAAY;IACZ,gBAAgB,CAAA,OAAQ,KAAK,KAAK,IAAI,KAAK,IAAI,IAAI;IACnD,UAAU;QACR,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,CAAC,UAAU,EAAE,KAAK,gCAAgC,CAAC;QAC1E;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,kBAAkB,CAAC;QAClD;QACA,SAAS;YACP,OAAO;YACP,aAAa,CAAA,OAAQ,GAAG,KAAK,8BAA8B,CAAC;QAC9D;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,aAAa;YACX,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;QACA,eAAe;YACb,OAAO;YACP,aAAa,CAAA,QACX,SAAS;QACb;IACF;AACF;AAKO,MAAM,kCAAkC;IAC7C;;GAEC,GACD,qBAAqB,WAAmB,EAAE,WAAmB,EAAE;QAC7D,OAAO,IAAI,CAAC,OAAO,CACjB,wBACA,GAAG,YAAY,cAAc,EAAE,YAAY,+BAA+B,CAAC;IAE/E;IAEA;;GAEC,GACD,qBAAqB,WAAmB,EAAE,WAAmB,EAAE;QAC7D,OAAO,IAAI,CAAC,OAAO,CACjB,0BACA,GAAG,YAAY,cAAc,EAAE,YAAY,mBAAmB,CAAC;IAEnE;IAEA;;GAEC,GACD,qBAAqB,WAAmB,EAAE,WAAmB,EAAE;QAC7D,OAAO,IAAI,CAAC,OAAO,CACjB,0BACA,GAAG,YAAY,qBAAqB,EAAE,YAAY,+BAA+B,CAAC;IAEtF;IAEA;;GAEC,GACD,2BAA2B,KAAa,EAAE;QACxC,OAAO,IAAI,CAAC,KAAK,CACf,gCACA,SAAS;IAEb;IAEA;;GAEC,GACD,yBAAyB,KAAa,EAAE;QACtC,OAAO,IAAI,CAAC,KAAK,CACf,iBACA,SAAS;IAEb;IAEA;;GAEC,GACD,2BAA2B,KAAa,EAAE;QACxC,OAAO,IAAI,CAAC,KAAK,CACf,mCACA,SAAS;IAEb;AACF;AAUO,SAAS,yBAA4B,MAA4B;IACtE,OAAO,IAAI,0BAA6B;AAC1C;AAMO,SAAS,+BACd,UAAkB,EAClB,cAAqC;IAErC,MAAM,SAA+B;QACnC;QACA;QACA,UAAU;YACR,SAAS;gBACP,OAAO,GAAG,WAAW,QAAQ,CAAC;gBAC9B,aAAa,CAAC,cACZ,CAAC,IAAI,EAAE,WAAW,WAAW,GAAG,EAAE,EAAE,YAAY,gCAAgC,CAAC;YACrF;YACA,SAAS;gBACP,OAAO,GAAG,WAAW,qBAAqB,CAAC;gBAC3C,aAAa,CAAC,cACZ,GAAG,YAAY,kBAAkB,CAAC;YACtC;YACA,SAAS;gBACP,OAAO,GAAG,WAAW,qBAAqB,CAAC;gBAC3C,aAAa,CAAC,cACZ,GAAG,YAAY,8BAA8B,CAAC;YAClD;YACA,eAAe;gBACb,OAAO,CAAC,iBAAiB,EAAE,YAAY;gBACvC,aAAa,CAAC,QACZ,SACA,CAAC,gDAAgD,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;YAClF;YACA,aAAa;gBACX,OAAO;gBACP,aAAa,CAAC,QACZ,SACA,CAAC,gDAAgD,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;YAClF;YACA,eAAe;gBACb,OAAO,CAAC,iBAAiB,EAAE,YAAY;gBACvC,aAAa,CAAC,QACZ,SACA,CAAC,gDAAgD,EAAE,WAAW,WAAW,GAAG,CAAC,CAAC;YAClF;QACF;IACF;IAEA,OAAO,IAAI,0BAA6B;AAC1C;AAOO,MAAM,eAAe,IAAI;AAGzB,MAAM,gBAAgB,IAAI,0BAA0B;AACpD,MAAM,kBAAkB,IAAI,0BACjC;AAEK,MAAM,eAAe,IAAI,0BAA0B;AACnD,MAAM,YAAY,IAAI,0BAA0B;AAChD,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/services/WebSocketManager.ts"], "sourcesContent": ["/**\r\n * @file Unified WebSocket Manager for WorkHub Application\r\n * Provides centralized WebSocket connection management with domain-specific channels\r\n * Follows SRP and DRY principles with smart fallback strategies\r\n * @module services/WebSocketManager\r\n */\r\n\r\nimport type { Socket } from 'socket.io-client';\r\n\r\nimport { io } from 'socket.io-client';\r\n\r\nimport { getEnvironmentConfig } from '../config/environment';\r\nimport { supabase } from '../supabase';\r\nimport { getTokenRefreshService } from './TokenRefreshService';\r\n/*import logger from '../utils/logger';\r\n\r\n/**\r\n * WebSocket connection states\r\n */\r\nexport type ConnectionState =\r\n  | 'connected'\r\n  | 'connecting'\r\n  | 'disconnected'\r\n  | 'error'\r\n  | 'reconnecting';\r\n\r\n/**\r\n * Domain-specific channels for organized event management\r\n */\r\nexport type DomainChannel = 'crud' | 'notifications' | 'reliability' | 'system';\r\n\r\n/**\r\n * Event subscription callback type\r\n */\r\nexport type EventCallback<T = any> = (data: T) => void;\r\n\r\n/**\r\n * WebSocket configuration options\r\n */\r\nexport interface WebSocketConfig {\r\n  autoConnect?: boolean;\r\n  reconnectAttempts?: number;\r\n  reconnectDelay?: number;\r\n  timeout?: number;\r\n  url?: string;\r\n}\r\n\r\n/**\r\n * Unified WebSocket Manager\r\n * Implements Singleton pattern for single connection per application\r\n * Provides domain-specific channels and centralized subscription management\r\n */\r\nexport class WebSocketManager {\r\n  private static instance: null | WebSocketManager = null;\r\n  private readonly config: Required<WebSocketConfig>;\r\n  private connectionState: ConnectionState = 'disconnected';\r\n  private reconnectAttempts = 0;\r\n  private socket: null | Socket = null;\r\n  private readonly stateListeners = new Set<(state: ConnectionState) => void>();\r\n  private readonly subscriptions = new Map<string, Set<EventCallback>>();\r\n\r\n  private constructor(config: WebSocketConfig = {}) {\r\n    this.config = {\r\n      autoConnect: config.autoConnect ?? true,\r\n      reconnectAttempts: config.reconnectAttempts ?? 5,\r\n      reconnectDelay: config.reconnectDelay ?? 1000,\r\n      timeout: config.timeout ?? 10_000,\r\n      url:\r\n        config.url ??\r\n        process.env.NEXT_PUBLIC_WEBSOCKET_URL ??\r\n        getEnvironmentConfig()\r\n          .wsUrl.replace('ws://', 'http://')\r\n          .replace('wss://', 'https://'),\r\n    };\r\n\r\n    if (this.config.autoConnect) {\r\n      this.connect();\r\n    }\r\n\r\n    // Subscribe to token refresh events\r\n    this.setupTokenRefreshHandling();\r\n  }\r\n\r\n  /**\r\n   * Get singleton instance\r\n   */\r\n  public static getInstance(config?: WebSocketConfig): WebSocketManager {\r\n    WebSocketManager.instance ??= new WebSocketManager(config);\r\n    return WebSocketManager.instance;\r\n  }\r\n\r\n  /**\r\n   * Connect to WebSocket server\r\n   */\r\n  public async connect(): Promise<void> {\r\n    if (this.socket?.connected) {\r\n      console.debug('WebSocket already connected');\r\n      return;\r\n    }\r\n\r\n    this.setConnectionState('connecting');\r\n\r\n    try {\r\n      // Get current session and token for authentication\r\n      const {\r\n        data: { session },\r\n        error,\r\n      } = await supabase.auth.getSession();\r\n\r\n      if (error) {\r\n        console.warn('Failed to get session for WebSocket connection:', error);\r\n      }\r\n\r\n      const connectionOptions: any = {\r\n        forceNew: true,\r\n        timeout: this.config.timeout,\r\n        transports: ['websocket', 'polling'],\r\n        withCredentials: true, // Ensure cookies are sent with WebSocket handshake\r\n      };\r\n\r\n      // Add authentication token if available\r\n      if (session?.access_token) {\r\n        connectionOptions.auth = {\r\n          token: session.access_token,\r\n        };\r\n        console.debug('🔐 WebSocket connecting with authentication token');\r\n\r\n        // Validate token expiration\r\n        const tokenExpiry = session.expires_at ? session.expires_at * 1000 : 0;\r\n        const now = Date.now();\r\n        const timeUntilExpiry = tokenExpiry - now;\r\n\r\n        if (timeUntilExpiry <= 60_000) {\r\n          // Less than 1 minute\r\n          console.warn('⚠️ WebSocket token expires soon, may need refresh');\r\n        }\r\n      } else {\r\n        console.warn(\r\n          '⚠️ WebSocket connecting without authentication token - connection may fail'\r\n        );\r\n      }\r\n\r\n      this.socket = io(this.config.url, connectionOptions);\r\n\r\n      this.setupEventHandlers();\r\n    } catch (error) {\r\n      console.error('Failed to connect WebSocket:', error);\r\n      this.setConnectionState('error');\r\n      this.scheduleReconnect();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleanup resources\r\n   */\r\n  public destroy(): void {\r\n    this.disconnect();\r\n    this.subscriptions.clear();\r\n    this.stateListeners.clear();\r\n    WebSocketManager.instance = null;\r\n  }\r\n\r\n  /**\r\n   * Disconnect from WebSocket server\r\n   */\r\n  public disconnect(): void {\r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n      this.socket = null;\r\n    }\r\n    this.setConnectionState('disconnected');\r\n    this.reconnectAttempts = 0;\r\n  }\r\n\r\n  /**\r\n   * Emit event to specific domain channel\r\n   */\r\n  public emit(channel: DomainChannel, event: string, data?: any): void {\r\n    if (!this.socket?.connected) {\r\n      console.warn(`Cannot emit ${channel}:${event} - WebSocket not connected`);\r\n      return;\r\n    }\r\n\r\n    this.socket.emit(event, data);\r\n  }\r\n\r\n  /**\r\n   * Get current connection state\r\n   */\r\n  public getConnectionState(): ConnectionState {\r\n    return this.connectionState;\r\n  }\r\n\r\n  /**\r\n   * Check if WebSocket is connected\r\n   */\r\n  public isConnected(): boolean {\r\n    return (\r\n      this.connectionState === 'connected' && this.socket?.connected === true\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Join domain-specific room\r\n   */\r\n  public joinRoom(room: string): void {\r\n    if (!this.socket?.connected) {\r\n      console.warn(`Cannot join room ${room} - WebSocket not connected`);\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('join-room', room);\r\n  }\r\n\r\n  /**\r\n   * Leave domain-specific room\r\n   */\r\n  public leaveRoom(room: string): void {\r\n    if (!this.socket?.connected) {\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('leave-room', room);\r\n  }\r\n\r\n  /**\r\n   * Subscribe to connection state changes\r\n   */\r\n  public onStateChange(callback: (state: ConnectionState) => void): () => void {\r\n    this.stateListeners.add(callback);\r\n\r\n    return () => {\r\n      this.stateListeners.delete(callback);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Subscribe to domain-specific events\r\n   */\r\n  public subscribe<T = any>(\r\n    channel: DomainChannel,\r\n    event: string,\r\n    callback: EventCallback<T>\r\n  ): () => void {\r\n    const eventKey = `${channel}:${event}`;\r\n\r\n    if (!this.subscriptions.has(eventKey)) {\r\n      this.subscriptions.set(eventKey, new Set());\r\n    }\r\n\r\n    this.subscriptions.get(eventKey)!.add(callback);\r\n\r\n    // Set up socket listener if connected\r\n    if (this.socket?.connected && event) {\r\n      this.socket.on(event, callback);\r\n    }\r\n\r\n    // Return unsubscribe function\r\n    return () => {\r\n      const callbacks = this.subscriptions.get(eventKey);\r\n      if (callbacks) {\r\n        callbacks.delete(callback);\r\n        if (callbacks.size === 0) {\r\n          this.subscriptions.delete(eventKey);\r\n        }\r\n      }\r\n\r\n      if (this.socket && event) {\r\n        this.socket.off(event, callback);\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Handle authentication errors by triggering token refresh\r\n   */\r\n  private handleAuthenticationError(): void {\r\n    const tokenRefreshService = getTokenRefreshService();\r\n\r\n    console.log('🔐 Handling WebSocket authentication error...');\r\n\r\n    // Disconnect current socket to prevent further auth errors\r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n      this.socket = null;\r\n    }\r\n\r\n    // Attempt to refresh token\r\n    tokenRefreshService\r\n      .refreshNow()\r\n      .then(success => {\r\n        if (success) {\r\n          console.log(\r\n            '🔄 Token refresh successful, retrying WebSocket connection'\r\n          );\r\n          // The reconnection will be handled by setupTokenRefreshHandling\r\n        } else {\r\n          console.error('🔄 Token refresh failed, scheduling normal reconnect');\r\n          this.scheduleReconnect();\r\n        }\r\n      })\r\n      .catch(error => {\r\n        console.error('🔄 Token refresh error:', error);\r\n        this.scheduleReconnect();\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Resubscribe to all events after reconnection\r\n   */\r\n  private resubscribeToEvents(): void {\r\n    if (!this.socket) return;\r\n\r\n    for (const [eventKey, callbacks] of this.subscriptions) {\r\n      const [, event] = eventKey.split(':');\r\n      for (const callback of callbacks) {\r\n        if (event) {\r\n          this.socket!.on(event, callback);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Schedule reconnection with exponential backoff\r\n   */\r\n  private scheduleReconnect(): void {\r\n    if (this.reconnectAttempts >= this.config.reconnectAttempts) {\r\n      console.error('Max reconnection attempts reached');\r\n      this.setConnectionState('error');\r\n      return;\r\n    }\r\n\r\n    this.setConnectionState('reconnecting');\r\n    this.reconnectAttempts++;\r\n\r\n    const delay =\r\n      this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\r\n\r\n    setTimeout(() => {\r\n      console.info(\r\n        `Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts})`\r\n      );\r\n      this.connect();\r\n    }, delay);\r\n  }\r\n\r\n  /**\r\n   * Set connection state and notify listeners\r\n   */\r\n  private setConnectionState(state: ConnectionState): void {\r\n    if (this.connectionState !== state) {\r\n      this.connectionState = state;\r\n      for (const listener of this.stateListeners) listener(state);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup socket event handlers\r\n   */\r\n  private setupEventHandlers(): void {\r\n    if (!this.socket) return;\r\n\r\n    this.socket.on('connect', () => {\r\n      console.info('WebSocket connected');\r\n      this.setConnectionState('connected');\r\n      this.reconnectAttempts = 0;\r\n      this.resubscribeToEvents();\r\n    });\r\n\r\n    this.socket.on('disconnect', reason => {\r\n      console.warn('WebSocket disconnected:', reason);\r\n      this.setConnectionState('disconnected');\r\n\r\n      if (reason === 'io server disconnect') {\r\n        // Server initiated disconnect, don't reconnect automatically\r\n        return;\r\n      }\r\n\r\n      this.scheduleReconnect();\r\n    });\r\n\r\n    this.socket.on('connect_error', error => {\r\n      console.error('WebSocket connection error:', error);\r\n      this.setConnectionState('error');\r\n\r\n      // Check if error is authentication-related\r\n      if (\r\n        error.message?.includes('Authentication') ||\r\n        error.message?.includes('token') ||\r\n        error.message?.includes('No token provided') ||\r\n        error.message?.includes('Unauthorized')\r\n      ) {\r\n        console.warn(\r\n          '🔐 Authentication error detected, attempting token refresh'\r\n        );\r\n        this.handleAuthenticationError();\r\n      } else {\r\n        this.scheduleReconnect();\r\n      }\r\n    });\r\n\r\n    // Listen for authentication errors from the server\r\n    this.socket.on('auth_error', errorData => {\r\n      console.error('🔐 Server authentication error:', errorData);\r\n      this.handleAuthenticationError();\r\n    });\r\n\r\n    // Listen for token refresh requests from server\r\n    this.socket.on('token_refresh_required', () => {\r\n      console.warn('🔄 Server requested token refresh');\r\n      this.handleAuthenticationError();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Setup token refresh event handling\r\n   */\r\n  private setupTokenRefreshHandling(): void {\r\n    const tokenRefreshService = getTokenRefreshService();\r\n\r\n    tokenRefreshService.subscribe((event, _data) => {\r\n      switch (event) {\r\n        case 'critical_refresh_failed': {\r\n          console.error(\r\n            '🔄 Critical token refresh failure, disconnecting WebSocket'\r\n          );\r\n          this.disconnect();\r\n          this.setConnectionState('error');\r\n          break;\r\n        }\r\n\r\n        case 'refresh_failed': {\r\n          console.error(\r\n            '🔄 Token refresh failed, WebSocket may lose connection'\r\n          );\r\n          break;\r\n        }\r\n\r\n        case 'refresh_success': {\r\n          console.log(\r\n            '🔄 Token refreshed, reconnecting WebSocket with new token'\r\n          );\r\n          // Disconnect current connection and reconnect with new token\r\n          if (this.socket) {\r\n            this.socket.disconnect();\r\n            this.socket = null;\r\n          }\r\n          // Reconnect with fresh token\r\n          setTimeout(() => this.connect(), 500);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * Get the singleton WebSocket manager instance\r\n */\r\nexport const getWebSocketManager = (\r\n  config?: WebSocketConfig\r\n): WebSocketManager => {\r\n  return WebSocketManager.getInstance(config);\r\n};\r\n\r\n/**\r\n * Hook for WebSocket connection state\r\n */\r\nexport const useWebSocketState = () => {\r\n  const manager = getWebSocketManager();\r\n  return {\r\n    connectionState: manager.getConnectionState(),\r\n    isConnected: manager.isConnected(),\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAgEO;AA5DR;AAAA;AAEA;AACA;AACA;;;;;AAuCO,MAAM;IACX,OAAe,WAAoC,KAAK;IACvC,OAAkC;IAC3C,kBAAmC,eAAe;IAClD,oBAAoB,EAAE;IACtB,SAAwB,KAAK;IACpB,iBAAiB,IAAI,MAAwC;IAC7D,gBAAgB,IAAI,MAAkC;IAEvE,YAAoB,SAA0B,CAAC,CAAC,CAAE;QAChD,IAAI,CAAC,MAAM,GAAG;YACZ,aAAa,OAAO,WAAW,IAAI;YACnC,mBAAmB,OAAO,iBAAiB,IAAI;YAC/C,gBAAgB,OAAO,cAAc,IAAI;YACzC,SAAS,OAAO,OAAO,IAAI;YAC3B,KACE,OAAO,GAAG,IACV,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB,IACrC,CAAA,GAAA,sIAAA,CAAA,uBAAoB,AAAD,IAChB,KAAK,CAAC,OAAO,CAAC,SAAS,WACvB,OAAO,CAAC,UAAU;QACzB;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC3B,IAAI,CAAC,OAAO;QACd;QAEA,oCAAoC;QACpC,IAAI,CAAC,yBAAyB;IAChC;IAEA;;GAEC,GACD,OAAc,YAAY,MAAwB,EAAoB;QACpE,iBAAiB,QAAQ,KAAK,IAAI,iBAAiB;QACnD,OAAO,iBAAiB,QAAQ;IAClC;IAEA;;GAEC,GACD,MAAa,UAAyB;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE,WAAW;YAC1B,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC;QAExB,IAAI;YACF,mDAAmD;YACnD,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EACjB,KAAK,EACN,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAElC,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,mDAAmD;YAClE;YAEA,MAAM,oBAAyB;gBAC7B,UAAU;gBACV,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO;gBAC5B,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,iBAAiB;YACnB;YAEA,wCAAwC;YACxC,IAAI,SAAS,cAAc;gBACzB,kBAAkB,IAAI,GAAG;oBACvB,OAAO,QAAQ,YAAY;gBAC7B;gBACA,QAAQ,KAAK,CAAC;gBAEd,4BAA4B;gBAC5B,MAAM,cAAc,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG,OAAO;gBACrE,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,kBAAkB,cAAc;gBAEtC,IAAI,mBAAmB,QAAQ;oBAC7B,qBAAqB;oBACrB,QAAQ,IAAI,CAAC;gBACf;YACF,OAAO;gBACL,QAAQ,IAAI,CACV;YAEJ;YAEA,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,kLAAA,CAAA,KAAE,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YAElC,IAAI,CAAC,kBAAkB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,iBAAiB;QACxB;IACF;IAEA;;GAEC,GACD,AAAO,UAAgB;QACrB,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,aAAa,CAAC,KAAK;QACxB,IAAI,CAAC,cAAc,CAAC,KAAK;QACzB,iBAAiB,QAAQ,GAAG;IAC9B;IAEA;;GAEC,GACD,AAAO,aAAmB;QACxB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG;IAC3B;IAEA;;GAEC,GACD,AAAO,KAAK,OAAsB,EAAE,KAAa,EAAE,IAAU,EAAQ;QACnE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,MAAM,0BAA0B,CAAC;YACxE;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;IAC1B;IAEA;;GAEC,GACD,AAAO,qBAAsC;QAC3C,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA;;GAEC,GACD,AAAO,cAAuB;QAC5B,OACE,IAAI,CAAC,eAAe,KAAK,eAAe,IAAI,CAAC,MAAM,EAAE,cAAc;IAEvE;IAEA;;GAEC,GACD,AAAO,SAAS,IAAY,EAAQ;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B,QAAQ,IAAI,CAAC,CAAC,iBAAiB,EAAE,KAAK,0BAA0B,CAAC;YACjE;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa;IAChC;IAEA;;GAEC,GACD,AAAO,UAAU,IAAY,EAAQ;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc;IACjC;IAEA;;GAEC,GACD,AAAO,cAAc,QAA0C,EAAc;QAC3E,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAExB,OAAO;YACL,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,AAAO,UACL,OAAsB,EACtB,KAAa,EACb,QAA0B,EACd;QACZ,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,OAAO;QAEtC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW;YACrC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,IAAI;QACvC;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAW,GAAG,CAAC;QAEtC,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,EAAE,aAAa,OAAO;YACnC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;QACxB;QAEA,8BAA8B;QAC9B,OAAO;YACL,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;YACzC,IAAI,WAAW;gBACb,UAAU,MAAM,CAAC;gBACjB,IAAI,UAAU,IAAI,KAAK,GAAG;oBACxB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;gBAC5B;YACF;YAEA,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO;gBACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO;YACzB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,4BAAkC;QACxC,MAAM,sBAAsB,CAAA,GAAA,gJAAA,CAAA,yBAAsB,AAAD;QAEjD,QAAQ,GAAG,CAAC;QAEZ,2DAA2D;QAC3D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;QAEA,2BAA2B;QAC3B,oBACG,UAAU,GACV,IAAI,CAAC,CAAA;YACJ,IAAI,SAAS;gBACX,QAAQ,GAAG,CACT;YAEF,gEAAgE;YAClE,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,IAAI,CAAC,iBAAiB;YACxB;QACF,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,2BAA2B;YACzC,IAAI,CAAC,iBAAiB;QACxB;IACJ;IAEA;;GAEC,GACD,AAAQ,sBAA4B;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,KAAK,MAAM,CAAC,UAAU,UAAU,IAAI,IAAI,CAAC,aAAa,CAAE;YACtD,MAAM,GAAG,MAAM,GAAG,SAAS,KAAK,CAAC;YACjC,KAAK,MAAM,YAAY,UAAW;gBAChC,IAAI,OAAO;oBACT,IAAI,CAAC,MAAM,CAAE,EAAE,CAAC,OAAO;gBACzB;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,oBAA0B;QAChC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;YAC3D,QAAQ,KAAK,CAAC;YACd,IAAI,CAAC,kBAAkB,CAAC;YACxB;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,iBAAiB;QAEtB,MAAM,QACJ,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG;QAEpE,WAAW;YACT,QAAQ,IAAI,CACV,CAAC,yBAAyB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAExF,IAAI,CAAC,OAAO;QACd,GAAG;IACL;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAsB,EAAQ;QACvD,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO;YAClC,IAAI,CAAC,eAAe,GAAG;YACvB,KAAK,MAAM,YAAY,IAAI,CAAC,cAAc,CAAE,SAAS;QACvD;IACF;IAEA;;GAEC,GACD,AAAQ,qBAA2B;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YACxB,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,iBAAiB,GAAG;YACzB,IAAI,CAAC,mBAAmB;QAC1B;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAA;YAC3B,QAAQ,IAAI,CAAC,2BAA2B;YACxC,IAAI,CAAC,kBAAkB,CAAC;YAExB,IAAI,WAAW,wBAAwB;gBACrC,6DAA6D;gBAC7D;YACF;YAEA,IAAI,CAAC,iBAAiB;QACxB;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAA;YAC9B,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,CAAC,kBAAkB,CAAC;YAExB,2CAA2C;YAC3C,IACE,MAAM,OAAO,EAAE,SAAS,qBACxB,MAAM,OAAO,EAAE,SAAS,YACxB,MAAM,OAAO,EAAE,SAAS,wBACxB,MAAM,OAAO,EAAE,SAAS,iBACxB;gBACA,QAAQ,IAAI,CACV;gBAEF,IAAI,CAAC,yBAAyB;YAChC,OAAO;gBACL,IAAI,CAAC,iBAAiB;YACxB;QACF;QAEA,mDAAmD;QACnD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAA;YAC3B,QAAQ,KAAK,CAAC,mCAAmC;YACjD,IAAI,CAAC,yBAAyB;QAChC;QAEA,gDAAgD;QAChD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,0BAA0B;YACvC,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,yBAAyB;QAChC;IACF;IAEA;;GAEC,GACD,AAAQ,4BAAkC;QACxC,MAAM,sBAAsB,CAAA,GAAA,gJAAA,CAAA,yBAAsB,AAAD;QAEjD,oBAAoB,SAAS,CAAC,CAAC,OAAO;YACpC,OAAQ;gBACN,KAAK;oBAA2B;wBAC9B,QAAQ,KAAK,CACX;wBAEF,IAAI,CAAC,UAAU;wBACf,IAAI,CAAC,kBAAkB,CAAC;wBACxB;oBACF;gBAEA,KAAK;oBAAkB;wBACrB,QAAQ,KAAK,CACX;wBAEF;oBACF;gBAEA,KAAK;oBAAmB;wBACtB,QAAQ,GAAG,CACT;wBAEF,6DAA6D;wBAC7D,IAAI,IAAI,CAAC,MAAM,EAAE;4BACf,IAAI,CAAC,MAAM,CAAC,UAAU;4BACtB,IAAI,CAAC,MAAM,GAAG;wBAChB;wBACA,6BAA6B;wBAC7B,WAAW,IAAM,IAAI,CAAC,OAAO,IAAI;wBACjC;oBACF;YACF;QACF;IACF;AACF;AAKO,MAAM,sBAAsB,CACjC;IAEA,OAAO,iBAAiB,WAAW,CAAC;AACtC;AAKO,MAAM,oBAAoB;IAC/B,MAAM,UAAU;IAChB,OAAO;QACL,iBAAiB,QAAQ,kBAAkB;QAC3C,aAAa,QAAQ,WAAW;IAClC;AACF", "debugId": null}}, {"offset": {"line": 663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/transformers/taskEnrichment.ts"], "sourcesContent": ["/**\r\n * @file Task enrichment transformer following established patterns\r\n * @description Handles the enrichment of task data with employee and vehicle details\r\n * @module transformers/taskEnrichment\r\n */\r\n\r\nimport type { Employee, Task, Vehicle } from '../types/domain';\r\n\r\n/**\r\n * Transformer class for enriching task data with related entities\r\n * Follows the same pattern as DelegationEnrichmentTransformer\r\n */\r\nexport class TaskEnrichmentTransformer {\r\n  /**\r\n   * Main enrichment method that combines task data with employee and vehicle details\r\n   * @param task - Base task data\r\n   * @param employees - Array of employees\r\n   * @param vehicles - Array of vehicles\r\n   * @returns Fully enriched task\r\n   */\r\n  static enrich(task: Task, employees: Employee[], vehicles: Vehicle[]): Task {\r\n    const { employeeMap, vehicleMap } = this.createLookupMaps(\r\n      employees,\r\n      vehicles\r\n    );\r\n\r\n    // Apply all enrichments sequentially\r\n    let enrichedTask = this.enrichStaffEmployee(task, employeeMap);\r\n    enrichedTask = this.enrichDriverEmployee(enrichedTask, employeeMap);\r\n    enrichedTask = this.enrichVehicle(enrichedTask, vehicleMap);\r\n\r\n    return enrichedTask;\r\n  }\r\n\r\n  /**\r\n   * Creates optimized lookup maps for O(1) performance\r\n   * @param employees - Array of employees\r\n   * @param vehicles - Array of vehicles\r\n   * @returns Object containing employee and vehicle maps\r\n   */\r\n  private static createLookupMaps(employees: Employee[], vehicles: Vehicle[]) {\r\n    // Defensive programming: Ensure inputs are arrays\r\n    const safeEmployees = Array.isArray(employees) ? employees : [];\r\n    const safeVehicles = Array.isArray(vehicles) ? vehicles : [];\r\n\r\n    return {\r\n      employeeMap: new Map(safeEmployees.map(emp => [emp.id, emp])),\r\n      vehicleMap: new Map(safeVehicles.map(veh => [veh.id, veh])),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Enriches driver employee assignment with employee details\r\n   * @param task - Base task data\r\n   * @param employeeMap - Map of employees for O(1) lookup\r\n   * @returns Task with enriched driver employee data\r\n   */\r\n  private static enrichDriverEmployee(\r\n    task: Task,\r\n    employeeMap: Map<number, Employee>\r\n  ): Task {\r\n    if (!task.driverEmployeeId) {\r\n      return task;\r\n    }\r\n\r\n    const driverEmployee =\r\n      task.driverEmployee ?? employeeMap.get(task.driverEmployeeId) ?? null;\r\n\r\n    return {\r\n      ...task,\r\n      driverEmployee,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Enriches staff employee assignment with employee details\r\n   * @param task - Base task data\r\n   * @param employeeMap - Map of employees for O(1) lookup\r\n   * @returns Task with enriched staff employee data\r\n   */\r\n  private static enrichStaffEmployee(\r\n    task: Task,\r\n    employeeMap: Map<number, Employee>\r\n  ): Task {\r\n    if (!task.staffEmployeeId) {\r\n      return task;\r\n    }\r\n\r\n    const staffEmployee =\r\n      task.staffEmployee ?? employeeMap.get(task.staffEmployeeId) ?? null;\r\n\r\n    return {\r\n      ...task,\r\n      staffEmployee,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Enriches vehicle assignment with vehicle details\r\n   * @param task - Base task data\r\n   * @param vehicleMap - Map of vehicles for O(1) lookup\r\n   * @returns Task with enriched vehicle data\r\n   */\r\n  private static enrichVehicle(\r\n    task: Task,\r\n    vehicleMap: Map<number, Vehicle>\r\n  ): Task {\r\n    if (!task.vehicleId) {\r\n      return task;\r\n    }\r\n\r\n    const vehicle = task.vehicle ?? vehicleMap.get(task.vehicleId) ?? null;\r\n\r\n    return {\r\n      ...task,\r\n      vehicle,\r\n    };\r\n  }\r\n}\r\n\r\n// Export the main enrichment function for backward compatibility\r\nexport const enrichTask = (\r\n  task: Task,\r\n  employees: Employee[],\r\n  vehicles: Vehicle[]\r\n): Task => {\r\n  return TaskEnrichmentTransformer.enrich(task, employees, vehicles);\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAQM,MAAM;IACX;;;;;;GAMC,GACD,OAAO,OAAO,IAAU,EAAE,SAAqB,EAAE,QAAmB,EAAQ;QAC1E,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,gBAAgB,CACvD,WACA;QAGF,qCAAqC;QACrC,IAAI,eAAe,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAClD,eAAe,IAAI,CAAC,oBAAoB,CAAC,cAAc;QACvD,eAAe,IAAI,CAAC,aAAa,CAAC,cAAc;QAEhD,OAAO;IACT;IAEA;;;;;GAKC,GACD,OAAe,iBAAiB,SAAqB,EAAE,QAAmB,EAAE;QAC1E,kDAAkD;QAClD,MAAM,gBAAgB,MAAM,OAAO,CAAC,aAAa,YAAY,EAAE;QAC/D,MAAM,eAAe,MAAM,OAAO,CAAC,YAAY,WAAW,EAAE;QAE5D,OAAO;YACL,aAAa,IAAI,IAAI,cAAc,GAAG,CAAC,CAAA,MAAO;oBAAC,IAAI,EAAE;oBAAE;iBAAI;YAC3D,YAAY,IAAI,IAAI,aAAa,GAAG,CAAC,CAAA,MAAO;oBAAC,IAAI,EAAE;oBAAE;iBAAI;QAC3D;IACF;IAEA;;;;;GAKC,GACD,OAAe,qBACb,IAAU,EACV,WAAkC,EAC5B;QACN,IAAI,CAAC,KAAK,gBAAgB,EAAE;YAC1B,OAAO;QACT;QAEA,MAAM,iBACJ,KAAK,cAAc,IAAI,YAAY,GAAG,CAAC,KAAK,gBAAgB,KAAK;QAEnE,OAAO;YACL,GAAG,IAAI;YACP;QACF;IACF;IAEA;;;;;GAKC,GACD,OAAe,oBACb,IAAU,EACV,WAAkC,EAC5B;QACN,IAAI,CAAC,KAAK,eAAe,EAAE;YACzB,OAAO;QACT;QAEA,MAAM,gBACJ,KAAK,aAAa,IAAI,YAAY,GAAG,CAAC,KAAK,eAAe,KAAK;QAEjE,OAAO;YACL,GAAG,IAAI;YACP;QACF;IACF;IAEA;;;;;GAKC,GACD,OAAe,cACb,IAAU,EACV,UAAgC,EAC1B;QACN,IAAI,CAAC,KAAK,SAAS,EAAE;YACnB,OAAO;QACT;QAEA,MAAM,UAAU,KAAK,OAAO,IAAI,WAAW,GAAG,CAAC,KAAK,SAAS,KAAK;QAElE,OAAO;YACL,GAAG,IAAI;YACP;QACF;IACF;AACF;AAGO,MAAM,aAAa,CACxB,MACA,WACA;IAEA,OAAO,0BAA0B,MAAM,CAAC,MAAM,WAAW;AAC3D", "debugId": null}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/taskQueries.ts"], "sourcesContent": ["/**\r\n * @file Task query configurations following Single Responsibility Principle\r\n * @description Centralized query configurations for task-related data fetching\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport type { Task } from '../../types/domain';\r\n\r\nimport {\r\n  taskApiService,\r\n  employeeApiService,\r\n  vehicleApiService,\r\n} from '../../api/services/apiServiceFactory';\r\nimport { TaskTransformer } from '../../transformers/taskTransformer';\r\n\r\n/**\r\n * Query keys for task-related queries\r\n */\r\nexport const taskQueryKeys = {\r\n  all: ['tasks'] as const,\r\n  detail: (id: string) => ['tasks', id] as const,\r\n  withAssignments: (id: string) => ['tasks', id, 'with-assignments'] as const,\r\n};\r\n\r\n/**\r\n * Creates query configuration for fetching a single task\r\n */\r\nexport const createTaskQuery = (id: string) => ({\r\n  enabled: !!id,\r\n  queryFn: () => taskApiService.getById(id),\r\n  queryKey: taskQueryKeys.detail(id),\r\n  staleTime: 5 * 60 * 1000, // 5 minutes\r\n});\r\n\r\n/**\r\n * Creates query configuration for fetching all employees\r\n */\r\nexport const createEmployeesQuery = () => ({\r\n  queryFn: () => employeeApiService.getAll(),\r\n  queryKey: ['employees'] as const,\r\n  staleTime: 10 * 60 * 1000, // 10 minutes - employees change less frequently\r\n});\r\n\r\n/**\r\n * Creates query configuration for fetching all vehicles\r\n */\r\nexport const createVehiclesQuery = () => ({\r\n  queryFn: () => vehicleApiService.getAll(),\r\n  queryKey: ['vehicles'] as const,\r\n  staleTime: 10 * 60 * 1000, // 10 minutes - vehicles change less frequently\r\n});\r\n\r\n/**\r\n * Creates parallel query configurations for task with assignments\r\n */\r\nexport const createTaskWithAssignmentsQueries = (id: string) => [\r\n  createTaskQuery(id),\r\n  createEmployeesQuery(),\r\n  createVehiclesQuery(),\r\n];\r\n\r\n/**\r\n * Standard query options for task queries\r\n */\r\nexport const taskQueryOptions: Partial<UseQueryOptions<Task, Error>> = {\r\n  gcTime: 10 * 60 * 1000, // 10 minutes garbage collection time\r\n  retry: 3,\r\n  retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),\r\n  staleTime: 5 * 60 * 1000,\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAMD;AAAA;;AAUO,MAAM,gBAAgB;IAC3B,KAAK;QAAC;KAAQ;IACd,QAAQ,CAAC,KAAe;YAAC;YAAS;SAAG;IACrC,iBAAiB,CAAC,KAAe;YAAC;YAAS;YAAI;SAAmB;AACpE;AAKO,MAAM,kBAAkB,CAAC,KAAe,CAAC;QAC9C,SAAS,CAAC,CAAC;QACX,SAAS,IAAM,2IAAA,CAAA,iBAAc,CAAC,OAAO,CAAC;QACtC,UAAU,cAAc,MAAM,CAAC;QAC/B,WAAW,IAAI,KAAK;IACtB,CAAC;AAKM,MAAM,uBAAuB,IAAM,CAAC;QACzC,SAAS,IAAM,2IAAA,CAAA,qBAAkB,CAAC,MAAM;QACxC,UAAU;YAAC;SAAY;QACvB,WAAW,KAAK,KAAK;IACvB,CAAC;AAKM,MAAM,sBAAsB,IAAM,CAAC;QACxC,SAAS,IAAM,2IAAA,CAAA,oBAAiB,CAAC,MAAM;QACvC,UAAU;YAAC;SAAW;QACtB,WAAW,KAAK,KAAK;IACvB,CAAC;AAKM,MAAM,mCAAmC,CAAC,KAAe;QAC9D,gBAAgB;QAChB;QACA;KACD;AAKM,MAAM,mBAA0D;IACrE,QAAQ,KAAK,KAAK;IAClB,OAAO;IACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;IAC/D,WAAW,IAAI,KAAK;AACtB", "debugId": null}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/useTasks.ts"], "sourcesContent": ["/**\r\n * @file TanStack Query hooks for Task-related data.\r\n * These hooks manage fetching, caching, and mutating task data,\r\n * integrating with the TaskApiService and TaskTransformer.\r\n * @module stores/queries/useTasks\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport { useMutation, useQueries, useQueryClient } from '@tanstack/react-query';\r\nimport { useCallback, useMemo } from 'react';\r\nimport { useCrudQuery } from '@/hooks/api/useSmartQuery'; // Adjusted import path\r\n\r\nimport type { CreateTaskData, Task } from '@/lib/types/domain';\r\n\r\nimport { taskApiService } from '../../api/services/apiServiceFactory'; // Use centralized service\r\nimport { enrichTask } from '../../transformers/taskEnrichment';\r\nimport { TaskTransformer } from '@/lib/transformers/taskTransformer';\r\nimport { createTaskWithAssignmentsQueries, taskQueryKeys } from './taskQueries';\r\nimport { undefinedToNull } from '@/lib/utils/typeHelpers';\r\n\r\n// Re-export query keys from taskQueries for backward compatibility\r\nexport { taskQueryKeys } from './taskQueries';\r\n\r\n/**\r\n * Custom hook to fetch all tasks.\r\n * @param options - Optional React Query options\r\n * @returns Query result containing an array of Task domain models.\r\n */\r\nexport const useTasks = (\r\n  options?: Omit<UseQueryOptions<Task[], Error>, 'queryFn' | 'queryKey'>\r\n) => {\r\n  return useCrudQuery<Task[], Error>(\r\n    [...taskQueryKeys.all], // queryKey - spread for mutability\r\n    async () => {\r\n      const response = await taskApiService.getAll();\r\n      return response.data;\r\n    },\r\n    'task', // entityType\r\n    {\r\n      staleTime: 0, // Existing staleTime\r\n      ...options,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Custom hook to fetch a single task by its ID.\r\n * @param id - The ID of the task to fetch.\r\n * @returns Query result containing a single Task domain model or undefined.\r\n */\r\nexport const useTask = (id: string) => {\r\n  return useCrudQuery<Task, Error>(\r\n    [...taskQueryKeys.detail(id)],\r\n    async () => {\r\n      return await taskApiService.getById(id);\r\n    },\r\n    'task', // entityType for WebSocket events\r\n    {\r\n      enabled: !!id, // Only run query if id is truthy\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n    }\r\n  );\r\n};\r\n\r\n// ✅ OPTIMIZED: Fast parallel data fetching for task with assignments\r\nexport const useTaskWithAssignments = (id: string) => {\r\n  // Execute all queries in parallel using useQueries for maximum performance\r\n  const results = useQueries({\r\n    queries: createTaskWithAssignmentsQueries(id),\r\n  });\r\n\r\n  const [taskQuery, employeesQuery, vehiclesQuery] = results;\r\n\r\n  // Compute enriched task when all data is available\r\n  const enrichedTask = useMemo(() => {\r\n    if (!taskQuery?.data || !employeesQuery?.data || !vehiclesQuery?.data) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const task = TaskTransformer.fromApi(taskQuery.data as any);\r\n\r\n      // Defensive programming: Ensure employees and vehicles are arrays\r\n      const employees = Array.isArray(employeesQuery.data)\r\n        ? employeesQuery.data\r\n        : [];\r\n      const vehicles = Array.isArray(vehiclesQuery.data)\r\n        ? vehiclesQuery.data\r\n        : [];\r\n\r\n      return enrichTask(task, employees, vehicles);\r\n    } catch (error) {\r\n      console.error('Error enriching task data:', error);\r\n      throw error;\r\n    }\r\n  }, [taskQuery?.data, employeesQuery?.data, vehiclesQuery?.data]);\r\n\r\n  // ✅ PRODUCTION FIX: Memoize refetch function to prevent infinite re-renders\r\n  const refetch = useCallback(() => {\r\n    taskQuery?.refetch();\r\n    employeesQuery?.refetch();\r\n    vehiclesQuery?.refetch();\r\n  }, [taskQuery?.refetch, employeesQuery?.refetch, vehiclesQuery?.refetch]);\r\n\r\n  // Return combined state with optimized loading states\r\n  return {\r\n    data: enrichedTask,\r\n    error: taskQuery?.error || employeesQuery?.error || vehiclesQuery?.error,\r\n    isError:\r\n      taskQuery?.isError || employeesQuery?.isError || vehiclesQuery?.isError,\r\n    isLoading:\r\n      taskQuery?.isLoading ||\r\n      employeesQuery?.isLoading ||\r\n      vehiclesQuery?.isLoading,\r\n    isPending:\r\n      taskQuery?.isPending ||\r\n      employeesQuery?.isPending ||\r\n      vehiclesQuery?.isPending,\r\n    refetch,\r\n  };\r\n};\r\n\r\n// ✅ BACKWARD COMPATIBILITY: Alias for the optimized hook\r\nexport const useTaskEnriched = useTaskWithAssignments;\r\n\r\n/**\r\n * Custom hook for creating a new task.\r\n * Includes optimistic updates and cache invalidation.\r\n * @returns Mutation result for creating a task.\r\n */\r\nexport const useCreateTask = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface CreateContext {\r\n    previousTasks: Task[] | undefined;\r\n  }\r\n\r\n  return useMutation<Task, Error, CreateTaskData, CreateContext>({\r\n    mutationFn: async (taskData: CreateTaskData) => {\r\n      const request = TaskTransformer.toCreateRequest(taskData);\r\n      return await taskApiService.create(request); // Removed redundant TaskTransformer.fromApi\r\n    },\r\n    onError: (err, newTaskData, context) => {\r\n      if (context?.previousTasks) {\r\n        queryClient.setQueryData(taskQueryKeys.all, context.previousTasks);\r\n      }\r\n      console.error('Failed to create task:', err);\r\n    },\r\n    onMutate: async newTaskData => {\r\n      await queryClient.cancelQueries({ queryKey: taskQueryKeys.all });\r\n      const previousTasks = queryClient.getQueryData<Task[]>(taskQueryKeys.all);\r\n\r\n      queryClient.setQueryData<Task[]>(taskQueryKeys.all, (old = []) => {\r\n        const tempId = 'optimistic-' + Date.now().toString();\r\n        const now = new Date().toISOString();\r\n        const optimisticTask: Task = {\r\n          createdAt: now,\r\n          dateTime: newTaskData.dateTime ?? null,\r\n          deadline: newTaskData.deadline ?? null,\r\n          description: newTaskData.description, // Direct mapping\r\n          driverEmployee: null,\r\n          driverEmployeeId: newTaskData.driverEmployeeId ?? null,\r\n          estimatedDuration: newTaskData.estimatedDuration ?? null,\r\n          id: tempId,\r\n          location: newTaskData.location ?? null,\r\n          notes: newTaskData.notes ?? null,\r\n          priority: newTaskData.priority,\r\n          requiredSkills: newTaskData.requiredSkills ?? null,\r\n          staffEmployee: null,\r\n          staffEmployeeId: newTaskData.staffEmployeeId ?? null,\r\n          status: newTaskData.status || 'Pending',\r\n          subtasks:\r\n            newTaskData.subtasks?.map(s => ({\r\n              completed: s.completed || false,\r\n              id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,\r\n              taskId: tempId, // Assign the optimistic task's ID as subtask's taskId\r\n              title: s.title,\r\n            })) || [],\r\n          updatedAt: now,\r\n          vehicle: null,\r\n          vehicleId: newTaskData.vehicleId ?? null,\r\n        };\r\n        return [...old, optimisticTask];\r\n      });\r\n\r\n      return { previousTasks };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: taskQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Custom hook for updating an existing task.\r\n * Includes optimistic updates and rollback on error.\r\n * @returns Mutation result for updating a task.\r\n */\r\nexport const useUpdateTask = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface UpdateContext {\r\n    previousTask: Task | undefined;\r\n    previousTasksList: Task[] | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Task,\r\n    Error,\r\n    { data: Partial<CreateTaskData>; id: string }, // Corrected data type\r\n    UpdateContext\r\n  >({\r\n    mutationFn: async ({ data, id }) => {\r\n      const request = TaskTransformer.toUpdateRequest(data); // Removed cast\r\n      return await taskApiService.update(id, request); // Removed redundant TaskTransformer.fromApi\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousTask) {\r\n        queryClient.setQueryData(\r\n          taskQueryKeys.detail(variables.id),\r\n          context.previousTask\r\n        );\r\n      }\r\n      if (context?.previousTasksList) {\r\n        queryClient.setQueryData(taskQueryKeys.all, context.previousTasksList);\r\n      }\r\n      console.error('Failed to update task:', err);\r\n    },\r\n    onMutate: async ({ data, id }) => {\r\n      await queryClient.cancelQueries({ queryKey: taskQueryKeys.all });\r\n      await queryClient.cancelQueries({ queryKey: taskQueryKeys.detail(id) });\r\n\r\n      const previousTask = queryClient.getQueryData<Task>(\r\n        taskQueryKeys.detail(id)\r\n      );\r\n      const previousTasksList = queryClient.getQueryData<Task[]>(\r\n        taskQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Task>(taskQueryKeys.detail(id), old => {\r\n        if (!old) return old;\r\n        const now = new Date().toISOString();\r\n\r\n        // Explicitly map updated fields to avoid issues with spread operator on different types\r\n        const updatedOptimistic: Task = {\r\n          ...old,\r\n          dateTime: data.dateTime !== undefined ? data.dateTime : old.dateTime,\r\n          deadline: undefinedToNull(\r\n            data.deadline !== undefined ? data.deadline : old.deadline\r\n          ),\r\n          description: data.description ?? old.description,\r\n          driverEmployeeId: undefinedToNull(\r\n            data.driverEmployeeId !== undefined\r\n              ? data.driverEmployeeId\r\n              : old.driverEmployeeId\r\n          ),\r\n          estimatedDuration:\r\n            data.estimatedDuration !== undefined\r\n              ? data.estimatedDuration\r\n              : old.estimatedDuration,\r\n          location: data.location !== undefined ? data.location : old.location,\r\n          notes: undefinedToNull(\r\n            data.notes !== undefined ? data.notes : old.notes\r\n          ),\r\n          priority: data.priority ?? old.priority,\r\n          requiredSkills:\r\n            data.requiredSkills !== undefined\r\n              ? data.requiredSkills\r\n              : old.requiredSkills,\r\n          staffEmployeeId:\r\n            data.staffEmployeeId !== undefined\r\n              ? data.staffEmployeeId\r\n              : old.staffEmployeeId,\r\n          status: data.status ?? old.status,\r\n          subtasks:\r\n            data.subtasks?.map(s => ({\r\n              completed: s.completed ?? false,\r\n              id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,\r\n              taskId: id, // Assign the current task's ID as subtask's taskId\r\n              title: s.title,\r\n            })) ||\r\n            old.subtasks ||\r\n            [],\r\n          updatedAt: now,\r\n          vehicleId: undefinedToNull(\r\n            data.vehicleId !== undefined ? data.vehicleId : old.vehicleId\r\n          ),\r\n        };\r\n        return updatedOptimistic;\r\n      });\r\n\r\n      queryClient.setQueryData<Task[]>(taskQueryKeys.all, (old = []) => {\r\n        return old.map(task => {\r\n          if (task.id === id) {\r\n            const now = new Date().toISOString();\r\n            const optimisticSubtasks =\r\n              data.subtasks?.map(s => ({\r\n                completed: s.completed ?? false,\r\n                id: `optimistic-subtask-${Date.now()}-${Math.random().toString(36).slice(2, 7)}`,\r\n                taskId: id, // Assign the current task's ID as subtask's taskId\r\n                title: s.title,\r\n              })) ||\r\n              task.subtasks ||\r\n              [];\r\n            return {\r\n              ...task,\r\n              dateTime:\r\n                data.dateTime !== undefined ? data.dateTime : task.dateTime,\r\n              deadline: undefinedToNull(\r\n                data.deadline !== undefined ? data.deadline : task.deadline\r\n              ),\r\n              description: data.description ?? task.description,\r\n              driverEmployeeId: undefinedToNull(\r\n                data.driverEmployeeId !== undefined\r\n                  ? data.driverEmployeeId\r\n                  : task.driverEmployeeId\r\n              ),\r\n              estimatedDuration:\r\n                data.estimatedDuration !== undefined\r\n                  ? data.estimatedDuration\r\n                  : task.estimatedDuration,\r\n              location:\r\n                data.location !== undefined ? data.location : task.location,\r\n              notes: undefinedToNull(\r\n                data.notes !== undefined ? data.notes : task.notes\r\n              ),\r\n              priority: data.priority ?? task.priority,\r\n              requiredSkills:\r\n                data.requiredSkills !== undefined\r\n                  ? data.requiredSkills\r\n                  : task.requiredSkills,\r\n              staffEmployeeId:\r\n                data.staffEmployeeId !== undefined\r\n                  ? data.staffEmployeeId\r\n                  : task.staffEmployeeId,\r\n              status: data.status ?? task.status,\r\n              subtasks: optimisticSubtasks,\r\n              updatedAt: now,\r\n              vehicleId: undefinedToNull(\r\n                data.vehicleId !== undefined ? data.vehicleId : task.vehicleId\r\n              ),\r\n            };\r\n          }\r\n          return task;\r\n        });\r\n      });\r\n\r\n      return { previousTask, previousTasksList };\r\n    },\r\n    onSettled: (data, error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: taskQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: taskQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Custom hook for deleting an existing task.\r\n * Includes cache updates.\r\n * @returns Mutation result for deleting a task.\r\n */\r\nexport const useDeleteTask = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface DeleteContext {\r\n    previousTasksList: Task[] | undefined;\r\n  }\r\n\r\n  return useMutation<string, Error, string, DeleteContext>({\r\n    mutationFn: async (id: string) => {\r\n      await taskApiService.delete(id);\r\n      return id;\r\n    },\r\n    onError: (err, id, context) => {\r\n      if (context?.previousTasksList) {\r\n        queryClient.setQueryData(taskQueryKeys.all, context.previousTasksList);\r\n      }\r\n      console.error('Failed to delete task:', err);\r\n    },\r\n    onMutate: async id => {\r\n      await queryClient.cancelQueries({ queryKey: taskQueryKeys.all });\r\n      await queryClient.cancelQueries({ queryKey: taskQueryKeys.detail(id) });\r\n\r\n      const previousTasksList = queryClient.getQueryData<Task[]>(\r\n        taskQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Task[]>(taskQueryKeys.all, (old = []) =>\r\n        old.filter(task => task.id !== id)\r\n      );\r\n\r\n      queryClient.removeQueries({ queryKey: taskQueryKeys.detail(id) });\r\n\r\n      return { previousTasksList };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: taskQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\n// Removed useAssignTask and useManageSubtasks hooks as their functionality is now\r\n// handled by the main create/update task mutations.\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;AAID;AAAA;AAAA;AACA;AACA,6OAA0D,uBAAuB;AAIjF,sTAAuE,0BAA0B;AAAjG;AACA;AACA;AACA;AACA;;;;;;;;;;;AAUO,MAAM,WAAW,CACtB;;IAEA,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,iJAAA,CAAA,gBAAa,CAAC,GAAG;KAAC;iCACtB;YACE,MAAM,WAAW,MAAM,2IAAA,CAAA,iBAAc,CAAC,MAAM;YAC5C,OAAO,SAAS,IAAI;QACtB;gCACA,QACA;QACE,WAAW;QACX,GAAG,OAAO;IACZ;AAEJ;GAfa;;QAGJ,uIAAA,CAAA,eAAY;;;AAmBd,MAAM,UAAU,CAAC;;IACtB,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;KAAI;gCAC7B;YACE,OAAO,MAAM,2IAAA,CAAA,iBAAc,CAAC,OAAO,CAAC;QACtC;+BACA,QACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAZa;;QACJ,uIAAA,CAAA,eAAY;;;AAcd,MAAM,yBAAyB,CAAC;;IACrC,2EAA2E;IAC3E,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE;QACzB,SAAS,CAAA,GAAA,iJAAA,CAAA,mCAAgC,AAAD,EAAE;IAC5C;IAEA,MAAM,CAAC,WAAW,gBAAgB,cAAc,GAAG;IAEnD,mDAAmD;IACnD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wDAAE;YAC3B,IAAI,CAAC,WAAW,QAAQ,CAAC,gBAAgB,QAAQ,CAAC,eAAe,MAAM;gBACrE;YACF;YAEA,IAAI;gBACF,MAAM,OAAO,gJAAA,CAAA,kBAAe,CAAC,OAAO,CAAC,UAAU,IAAI;gBAEnD,kEAAkE;gBAClE,MAAM,YAAY,MAAM,OAAO,CAAC,eAAe,IAAI,IAC/C,eAAe,IAAI,GACnB,EAAE;gBACN,MAAM,WAAW,MAAM,OAAO,CAAC,cAAc,IAAI,IAC7C,cAAc,IAAI,GAClB,EAAE;gBAEN,OAAO,CAAA,GAAA,+IAAA,CAAA,aAAU,AAAD,EAAE,MAAM,WAAW;YACrC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,MAAM;YACR;QACF;uDAAG;QAAC,WAAW;QAAM,gBAAgB;QAAM,eAAe;KAAK;IAE/D,4EAA4E;IAC5E,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC1B,WAAW;YACX,gBAAgB;YAChB,eAAe;QACjB;sDAAG;QAAC,WAAW;QAAS,gBAAgB;QAAS,eAAe;KAAQ;IAExE,sDAAsD;IACtD,OAAO;QACL,MAAM;QACN,OAAO,WAAW,SAAS,gBAAgB,SAAS,eAAe;QACnE,SACE,WAAW,WAAW,gBAAgB,WAAW,eAAe;QAClE,WACE,WAAW,aACX,gBAAgB,aAChB,eAAe;QACjB,WACE,WAAW,aACX,gBAAgB,aAChB,eAAe;QACjB;IACF;AACF;IAvDa;;QAEK,gLAAA,CAAA,aAAU;;;AAwDrB,MAAM,kBAAkB;AAOxB,MAAM,gBAAgB;;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAMjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAA8C;QAC7D,UAAU;yCAAE,OAAO;gBACjB,MAAM,UAAU,gJAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;gBAChD,OAAO,MAAM,2IAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,UAAU,4CAA4C;YAC3F;;QACA,OAAO;yCAAE,CAAC,KAAK,aAAa;gBAC1B,IAAI,SAAS,eAAe;oBAC1B,YAAY,YAAY,CAAC,iJAAA,CAAA,gBAAa,CAAC,GAAG,EAAE,QAAQ,aAAa;gBACnE;gBACA,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;QACA,QAAQ;yCAAE,OAAM;gBACd,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAAC;gBAC9D,MAAM,gBAAgB,YAAY,YAAY,CAAS,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAExE,YAAY,YAAY,CAAS,iJAAA,CAAA,gBAAa,CAAC,GAAG;iDAAE,CAAC,MAAM,EAAE;wBAC3D,MAAM,SAAS,gBAAgB,KAAK,GAAG,GAAG,QAAQ;wBAClD,MAAM,MAAM,IAAI,OAAO,WAAW;wBAClC,MAAM,iBAAuB;4BAC3B,WAAW;4BACX,UAAU,YAAY,QAAQ,IAAI;4BAClC,UAAU,YAAY,QAAQ,IAAI;4BAClC,aAAa,YAAY,WAAW;4BACpC,gBAAgB;4BAChB,kBAAkB,YAAY,gBAAgB,IAAI;4BAClD,mBAAmB,YAAY,iBAAiB,IAAI;4BACpD,IAAI;4BACJ,UAAU,YAAY,QAAQ,IAAI;4BAClC,OAAO,YAAY,KAAK,IAAI;4BAC5B,UAAU,YAAY,QAAQ;4BAC9B,gBAAgB,YAAY,cAAc,IAAI;4BAC9C,eAAe;4BACf,iBAAiB,YAAY,eAAe,IAAI;4BAChD,QAAQ,YAAY,MAAM,IAAI;4BAC9B,UACE,YAAY,QAAQ,EAAE;6DAAI,CAAA,IAAK,CAAC;wCAC9B,WAAW,EAAE,SAAS,IAAI;wCAC1B,IAAI,CAAC,mBAAmB,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI;wCAChF,QAAQ;wCACR,OAAO,EAAE,KAAK;oCAChB,CAAC;+DAAM,EAAE;4BACX,WAAW;4BACX,SAAS;4BACT,WAAW,YAAY,SAAS,IAAI;wBACtC;wBACA,OAAO;+BAAI;4BAAK;yBAAe;oBACjC;;gBAEA,OAAO;oBAAE;gBAAc;YACzB;;QACA,SAAS;yCAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAAC;YAC9D;;IACF;AACF;IA7Da;;QACS,yLAAA,CAAA,iBAAc;QAM3B,iLAAA,CAAA,cAAW;;;AA6Db,MAAM,gBAAgB;;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAOjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAKf;QACA,UAAU;yCAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC7B,MAAM,UAAU,gJAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,OAAO,eAAe;gBACtE,OAAO,MAAM,2IAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,IAAI,UAAU,4CAA4C;YAC/F;;QACA,OAAO;yCAAE,CAAC,KAAK,WAAW;gBACxB,IAAI,SAAS,cAAc;oBACzB,YAAY,YAAY,CACtB,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,UAAU,EAAE,GACjC,QAAQ,YAAY;gBAExB;gBACA,IAAI,SAAS,mBAAmB;oBAC9B,YAAY,YAAY,CAAC,iJAAA,CAAA,gBAAa,CAAC,GAAG,EAAE,QAAQ,iBAAiB;gBACvE;gBACA,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;QACA,QAAQ;yCAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC3B,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAAC;gBAC9D,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBAAI;gBAErE,MAAM,eAAe,YAAY,YAAY,CAC3C,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBAEvB,MAAM,oBAAoB,YAAY,YAAY,CAChD,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAGnB,YAAY,YAAY,CAAO,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;iDAAK,CAAA;wBACvD,IAAI,CAAC,KAAK,OAAO;wBACjB,MAAM,MAAM,IAAI,OAAO,WAAW;wBAElC,wFAAwF;wBACxF,MAAM,oBAA0B;4BAC9B,GAAG,GAAG;4BACN,UAAU,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG,IAAI,QAAQ;4BACpE,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACtB,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG,IAAI,QAAQ;4BAE5D,aAAa,KAAK,WAAW,IAAI,IAAI,WAAW;4BAChD,kBAAkB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC9B,KAAK,gBAAgB,KAAK,YACtB,KAAK,gBAAgB,GACrB,IAAI,gBAAgB;4BAE1B,mBACE,KAAK,iBAAiB,KAAK,YACvB,KAAK,iBAAiB,GACtB,IAAI,iBAAiB;4BAC3B,UAAU,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG,IAAI,QAAQ;4BACpE,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACnB,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK,GAAG,IAAI,KAAK;4BAEnD,UAAU,KAAK,QAAQ,IAAI,IAAI,QAAQ;4BACvC,gBACE,KAAK,cAAc,KAAK,YACpB,KAAK,cAAc,GACnB,IAAI,cAAc;4BACxB,iBACE,KAAK,eAAe,KAAK,YACrB,KAAK,eAAe,GACpB,IAAI,eAAe;4BACzB,QAAQ,KAAK,MAAM,IAAI,IAAI,MAAM;4BACjC,UACE,KAAK,QAAQ,EAAE;6DAAI,CAAA,IAAK,CAAC;wCACvB,WAAW,EAAE,SAAS,IAAI;wCAC1B,IAAI,CAAC,mBAAmB,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI;wCAChF,QAAQ;wCACR,OAAO,EAAE,KAAK;oCAChB,CAAC;+DACD,IAAI,QAAQ,IACZ,EAAE;4BACJ,WAAW;4BACX,WAAW,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACvB,KAAK,SAAS,KAAK,YAAY,KAAK,SAAS,GAAG,IAAI,SAAS;wBAEjE;wBACA,OAAO;oBACT;;gBAEA,YAAY,YAAY,CAAS,iJAAA,CAAA,gBAAa,CAAC,GAAG;iDAAE,CAAC,MAAM,EAAE;wBAC3D,OAAO,IAAI,GAAG;yDAAC,CAAA;gCACb,IAAI,KAAK,EAAE,KAAK,IAAI;oCAClB,MAAM,MAAM,IAAI,OAAO,WAAW;oCAClC,MAAM,qBACJ,KAAK,QAAQ,EAAE;qEAAI,CAAA,IAAK,CAAC;gDACvB,WAAW,EAAE,SAAS,IAAI;gDAC1B,IAAI,CAAC,mBAAmB,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI;gDAChF,QAAQ;gDACR,OAAO,EAAE,KAAK;4CAChB,CAAC;uEACD,KAAK,QAAQ,IACb,EAAE;oCACJ,OAAO;wCACL,GAAG,IAAI;wCACP,UACE,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG,KAAK,QAAQ;wCAC7D,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACtB,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG,KAAK,QAAQ;wCAE7D,aAAa,KAAK,WAAW,IAAI,KAAK,WAAW;wCACjD,kBAAkB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC9B,KAAK,gBAAgB,KAAK,YACtB,KAAK,gBAAgB,GACrB,KAAK,gBAAgB;wCAE3B,mBACE,KAAK,iBAAiB,KAAK,YACvB,KAAK,iBAAiB,GACtB,KAAK,iBAAiB;wCAC5B,UACE,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,GAAG,KAAK,QAAQ;wCAC7D,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACnB,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK,GAAG,KAAK,KAAK;wCAEpD,UAAU,KAAK,QAAQ,IAAI,KAAK,QAAQ;wCACxC,gBACE,KAAK,cAAc,KAAK,YACpB,KAAK,cAAc,GACnB,KAAK,cAAc;wCACzB,iBACE,KAAK,eAAe,KAAK,YACrB,KAAK,eAAe,GACpB,KAAK,eAAe;wCAC1B,QAAQ,KAAK,MAAM,IAAI,KAAK,MAAM;wCAClC,UAAU;wCACV,WAAW;wCACX,WAAW,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EACvB,KAAK,SAAS,KAAK,YAAY,KAAK,SAAS,GAAG,KAAK,SAAS;oCAElE;gCACF;gCACA,OAAO;4BACT;;oBACF;;gBAEA,OAAO;oBAAE;oBAAc;gBAAkB;YAC3C;;QACA,SAAS;yCAAE,CAAC,MAAM,OAAO;gBACvB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,UAAU,EAAE;gBAC7C;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAAC;YAC9D;;IACF;AACF;IA9Ja;;QACS,yLAAA,CAAA,iBAAc;QAO3B,iLAAA,CAAA,cAAW;;;AA6Jb,MAAM,gBAAgB;;IAC3B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAMjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAwC;QACvD,UAAU;yCAAE,OAAO;gBACjB,MAAM,2IAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;gBAC5B,OAAO;YACT;;QACA,OAAO;yCAAE,CAAC,KAAK,IAAI;gBACjB,IAAI,SAAS,mBAAmB;oBAC9B,YAAY,YAAY,CAAC,iJAAA,CAAA,gBAAa,CAAC,GAAG,EAAE,QAAQ,iBAAiB;gBACvE;gBACA,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;;QACA,QAAQ;yCAAE,OAAM;gBACd,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAAC;gBAC9D,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBAAI;gBAErE,MAAM,oBAAoB,YAAY,YAAY,CAChD,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAGnB,YAAY,YAAY,CAAS,iJAAA,CAAA,gBAAa,CAAC,GAAG;iDAAE,CAAC,MAAM,EAAE,GAC3D,IAAI,MAAM;yDAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;;gBAGjC,YAAY,aAAa,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;gBAAI;gBAE/D,OAAO;oBAAE;gBAAkB;YAC7B;;QACA,SAAS;yCAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,iJAAA,CAAA,gBAAa,CAAC,GAAG;gBAAC;YAC9D;;IACF;AACF,GAEA,kFAAkF;CAClF,oDAAoD;IAzCvC;;QACS,yLAAA,CAAA,iBAAc;QAM3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1247, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/zustand/reliabilityStore.ts"], "sourcesContent": ["/**\r\n * @file Zustand store for Reliability dashboard state management.\r\n * This store manages reliability dashboard preferences, UI state, and real-time monitoring controls,\r\n * complementing the React Query hooks for comprehensive reliability monitoring.\r\n * @module stores/zustand/reliabilityStore\r\n */\r\n\r\nimport { create } from 'zustand';\r\nimport { devtools, persist } from 'zustand/middleware';\r\n\r\nimport type { AlertSeverity, AlertStatus } from '@/lib/types/domain';\r\n\r\n/**\r\n * Alert filtering configuration\r\n */\r\nexport interface AlertFilters {\r\n  /** Text search filter */\r\n  searchText: string;\r\n  /** Filter by alert severity levels */\r\n  severities: Set<AlertSeverity>;\r\n  /** Filter by alert source/type */\r\n  sources: Set<string>;\r\n  /** Filter by alert status */\r\n  statuses: Set<AlertStatus>;\r\n  /** Time range for alert history */\r\n  timeRange: TimeRange;\r\n}\r\n\r\n/**\r\n * Batch operation loading states\r\n */\r\nexport interface BatchOperations {\r\n  'acknowledge-multiple-alerts': boolean;\r\n  'export-metrics': boolean;\r\n  'refresh-all-data': boolean;\r\n  'resolve-multiple-alerts': boolean;\r\n}\r\n\r\n/**\r\n * WebSocket connection status states\r\n */\r\nexport type ConnectionStatus = 'connected' | 'disconnected' | 'reconnecting';\r\n\r\n/**\r\n * Dashboard layout configuration options\r\n */\r\nexport type DashboardLayout = 'compact' | 'grid' | 'list';\r\n\r\n/**\r\n * Dashboard layout preferences\r\n */\r\nexport interface DashboardLayoutPreferences {\r\n  /** Expanded/collapsed widget states */\r\n  expandedWidgets: Set<WidgetId>;\r\n  /** Grid columns for grid layout */\r\n  gridColumns: number;\r\n  /** Layout type (grid, list, compact) */\r\n  layout: DashboardLayout;\r\n  /** Visible widgets */\r\n  visibleWidgets: Set<WidgetId>;\r\n  /** Widget order/arrangement */\r\n  widgetOrder: WidgetId[];\r\n}\r\n\r\n/**\r\n * Dashboard tab/view identifiers\r\n */\r\nexport type DashboardTab =\r\n  | 'alerts'\r\n  | 'health'\r\n  | 'history'\r\n  | 'metrics'\r\n  | 'overview';\r\n\r\n/**\r\n * Data type identifiers for refresh interval configuration\r\n */\r\nexport type DataType =\r\n  | 'alert-statistics'\r\n  | 'alerts'\r\n  | 'circuit-breakers'\r\n  | 'dependencies'\r\n  | 'detailed-health'\r\n  | 'health'\r\n  | 'metrics';\r\n\r\n/**\r\n * Last refresh timestamps for data freshness tracking\r\n */\r\nexport interface LastRefreshTimestamps {\r\n  'alert-statistics'?: string;\r\n  alerts?: string;\r\n  'circuit-breakers'?: string;\r\n  dependencies?: string;\r\n  'detailed-health'?: string;\r\n  health?: string;\r\n  metrics?: string;\r\n}\r\n\r\n/**\r\n * Notification preferences for alerts\r\n */\r\nexport interface NotificationPreferences {\r\n  /** Auto-dismiss timeout in seconds (0 = no auto-dismiss) */\r\n  autoDismissTimeout: number;\r\n  /** Enable desktop notifications */\r\n  desktopEnabled: boolean;\r\n  /** Minimum severity level for notifications */\r\n  minimumSeverity: AlertSeverity;\r\n  /** Enable sound notifications for alerts */\r\n  soundEnabled: boolean;\r\n}\r\n\r\n/**\r\n * Refresh interval configuration for different data types\r\n */\r\nexport interface RefreshIntervals {\r\n  'alert-statistics': number;\r\n  alerts: number;\r\n  'circuit-breakers': number;\r\n  dependencies: number;\r\n  'detailed-health': number;\r\n  health: number;\r\n  metrics: number;\r\n}\r\n\r\n/**\r\n * Time range options for historical data\r\n */\r\nexport type TimeRange = '1h' | '6h' | '7d' | '24h' | '30d';\r\n\r\n/**\r\n * Dashboard widget identifiers for visibility control\r\n */\r\nexport type WidgetId =\r\n  | 'active-alerts'\r\n  | 'alert-statistics'\r\n  | 'circuit-breaker-alerts'\r\n  | 'circuit-breaker-history'\r\n  | 'circuit-breaker-list'\r\n  | 'circuit-breaker-metrics'\r\n  | 'circuit-breakers'\r\n  | 'deduplication-metrics'\r\n  | 'dependency-health'\r\n  | 'dependency-status'\r\n  | 'health-status-indicators'\r\n  | 'health-trends'\r\n  | 'http-metrics'\r\n  | 'performance-metrics'\r\n  | 'performance-overview'\r\n  | 'system-health'\r\n  | 'system-metrics'\r\n  | 'system-resources';\r\n\r\n/**\r\n * Complete reliability store state interface\r\n */\r\ninterface ReliabilityState {\r\n  clearAlertFilters: () => void;\r\n\r\n  clearAlertSelection: () => void;\r\n\r\n  getActiveFilters: () => Partial<AlertFilters>;\r\n\r\n  getSelectedAlertCount: () => number;\r\n  // Computed selectors\r\n  getVisibleWidgets: () => WidgetId[];\r\n  isDataTypePaused: (dataType: DataType) => boolean;\r\n  // Real-time Monitoring State (ephemeral)\r\n  monitoring: {\r\n    connectionStatus: ConnectionStatus;\r\n    isEnabled: boolean;\r\n    lastRefresh: LastRefreshTimestamps;\r\n    pausedDataTypes: Set<DataType>;\r\n  };\r\n  pauseAllMonitoring: () => void;\r\n  pauseDataType: (dataType: DataType) => void;\r\n  // Preferences (persisted)\r\n  preferences: {\r\n    dashboardLayout: DashboardLayoutPreferences;\r\n    defaultTimeRange: TimeRange;\r\n    notifications: NotificationPreferences;\r\n    refreshIntervals: RefreshIntervals;\r\n  };\r\n  reorderWidgets: (newOrder: WidgetId[]) => void;\r\n  resetPreferencesToDefaults: () => void;\r\n\r\n  resumeAllMonitoring: () => void;\r\n  resumeDataType: (dataType: DataType) => void;\r\n  selectAllAlerts: (alertIds: string[]) => void;\r\n  // Actions for UI state\r\n  setActiveTab: (tab: DashboardTab) => void;\r\n  setAlertFilters: (filters: Partial<AlertFilters>) => void;\r\n  setBatchOperationLoading: (\r\n    operation: keyof BatchOperations,\r\n    loading: boolean\r\n  ) => void;\r\n  setConnectionStatus: (status: ConnectionStatus) => void;\r\n  setDashboardLayout: (layout: DashboardLayout) => void;\r\n\r\n  setDefaultTimeRange: (timeRange: TimeRange) => void;\r\n  setGridColumns: (columns: number) => void;\r\n  // Actions for monitoring state\r\n  setMonitoringEnabled: (enabled: boolean) => void;\r\n  setNotificationPreferences: (\r\n    preferences: Partial<NotificationPreferences>\r\n  ) => void;\r\n  // Actions for preferences\r\n  setRefreshInterval: (dataType: DataType, interval: number) => void;\r\n  setWidgetExpanded: (widgetId: WidgetId, expanded: boolean) => void;\r\n  toggleAlertSelection: (alertId: string) => void;\r\n\r\n  toggleFilterPanel: () => void;\r\n  toggleWidget: (widgetId: WidgetId) => void;\r\n  // UI State (ephemeral)\r\n  ui: {\r\n    activeTab: DashboardTab;\r\n    batchOperations: BatchOperations;\r\n    filters: AlertFilters;\r\n    isFilterPanelOpen: boolean;\r\n    selectedAlerts: Set<string>;\r\n  };\r\n  updateLastRefresh: (dataType: DataType, timestamp?: string) => void;\r\n}\r\n\r\n/**\r\n * Default values for the reliability store\r\n */\r\nconst defaultRefreshIntervals: RefreshIntervals = {\r\n  'alert-statistics': 300_000, // 5 minutes\r\n  alerts: 10_000, // 10 seconds\r\n  'circuit-breakers': 30_000, // 30 seconds\r\n  dependencies: 45_000, // 45 seconds\r\n  'detailed-health': 60_000, // 1 minute\r\n  health: 15_000, // 15 seconds\r\n  metrics: 30_000, // 30 seconds\r\n};\r\n\r\nconst defaultDashboardLayout: DashboardLayoutPreferences = {\r\n  expandedWidgets: new Set([\r\n    'system-health',\r\n    'health-status-indicators',\r\n    'circuit-breakers',\r\n    'active-alerts',\r\n  ]),\r\n  gridColumns: 3,\r\n  layout: 'grid',\r\n  visibleWidgets: new Set([\r\n    'system-health',\r\n    'health-status-indicators',\r\n    'circuit-breakers',\r\n    'active-alerts',\r\n    'alert-statistics',\r\n    'dependency-status',\r\n    'health-trends',\r\n    'circuit-breaker-metrics',\r\n    'circuit-breaker-history',\r\n    'deduplication-metrics',\r\n    'performance-metrics',\r\n    'performance-overview',\r\n    'system-metrics',\r\n    'http-metrics',\r\n    'dependency-health',\r\n  ]),\r\n  widgetOrder: [\r\n    'system-health',\r\n    'health-status-indicators',\r\n    'circuit-breakers',\r\n    'active-alerts',\r\n    'alert-statistics',\r\n    'dependency-status',\r\n    'health-trends',\r\n    'circuit-breaker-metrics',\r\n    'circuit-breaker-list',\r\n    'circuit-breaker-history',\r\n    'circuit-breaker-alerts',\r\n    'system-resources',\r\n    'performance-overview',\r\n    'system-metrics',\r\n    'http-metrics',\r\n    'deduplication-metrics',\r\n    'performance-metrics',\r\n    'dependency-health',\r\n  ],\r\n};\r\n\r\nconst defaultNotificationPreferences: NotificationPreferences = {\r\n  autoDismissTimeout: 5000, // 5 seconds\r\n  desktopEnabled: true,\r\n  minimumSeverity: 'medium',\r\n  soundEnabled: true,\r\n};\r\n\r\nconst defaultAlertFilters: AlertFilters = {\r\n  searchText: '',\r\n  severities: new Set(['critical', 'high', 'low', 'medium']),\r\n  sources: new Set(),\r\n  statuses: new Set(['acknowledged', 'active']),\r\n  timeRange: '24h',\r\n};\r\n\r\nconst defaultBatchOperations: BatchOperations = {\r\n  'acknowledge-multiple-alerts': false,\r\n  'export-metrics': false,\r\n  'refresh-all-data': false,\r\n  'resolve-multiple-alerts': false,\r\n};\r\n\r\n/**\r\n * Zustand store for managing reliability dashboard state.\r\n * Enhanced with persistence for user preferences and devtools for development.\r\n */\r\nexport const useReliabilityStore = create<ReliabilityState>()(\r\n  devtools(\r\n    persist(\r\n      (set, get) => ({\r\n        clearAlertFilters: () =>\r\n          set(state => ({\r\n            ui: {\r\n              ...state.ui,\r\n              filters: defaultAlertFilters,\r\n            },\r\n          })),\r\n\r\n        clearAlertSelection: () =>\r\n          set(state => ({\r\n            ui: {\r\n              ...state.ui,\r\n              selectedAlerts: new Set(),\r\n            },\r\n          })),\r\n\r\n        getActiveFilters: () => {\r\n          const { ui } = get();\r\n          const filters = ui.filters;\r\n          const activeFilters: Partial<AlertFilters> = {};\r\n\r\n          // Only include non-default filter values\r\n          if (filters.severities.size !== 4) {\r\n            activeFilters.severities = filters.severities;\r\n          }\r\n          if (filters.statuses.size !== 2) {\r\n            activeFilters.statuses = filters.statuses;\r\n          }\r\n          if (filters.sources.size > 0) {\r\n            activeFilters.sources = filters.sources;\r\n          }\r\n          if (filters.searchText.trim()) {\r\n            activeFilters.searchText = filters.searchText;\r\n          }\r\n          if (filters.timeRange !== '24h') {\r\n            activeFilters.timeRange = filters.timeRange;\r\n          }\r\n\r\n          return activeFilters;\r\n        },\r\n\r\n        getSelectedAlertCount: () => {\r\n          const { ui } = get();\r\n          return ui.selectedAlerts.size;\r\n        },\r\n\r\n        // Computed selectors\r\n        getVisibleWidgets: () => {\r\n          const { preferences } = get();\r\n          return preferences.dashboardLayout.widgetOrder.filter(widgetId =>\r\n            preferences.dashboardLayout.visibleWidgets.has(widgetId)\r\n          );\r\n        },\r\n\r\n        isDataTypePaused: (dataType: DataType) => {\r\n          const { monitoring } = get();\r\n          return (\r\n            monitoring.pausedDataTypes.has(dataType) || !monitoring.isEnabled\r\n          );\r\n        },\r\n\r\n        monitoring: {\r\n          connectionStatus: 'disconnected',\r\n          isEnabled: true,\r\n          lastRefresh: {},\r\n          pausedDataTypes: new Set(),\r\n        },\r\n\r\n        pauseAllMonitoring: () =>\r\n          set(state => ({\r\n            monitoring: {\r\n              ...state.monitoring,\r\n              isEnabled: false,\r\n              pausedDataTypes: new Set([\r\n                'alert-statistics',\r\n                'alerts',\r\n                'circuit-breakers',\r\n                'dependencies',\r\n                'detailed-health',\r\n                'health',\r\n                'metrics',\r\n              ]),\r\n            },\r\n          })),\r\n\r\n        pauseDataType: (dataType: DataType) =>\r\n          set(state => {\r\n            const newPausedDataTypes = new Set(\r\n              state.monitoring.pausedDataTypes\r\n            );\r\n            newPausedDataTypes.add(dataType);\r\n            return {\r\n              monitoring: {\r\n                ...state.monitoring,\r\n                pausedDataTypes: newPausedDataTypes,\r\n              },\r\n            };\r\n          }),\r\n\r\n        // Initial state\r\n        preferences: {\r\n          dashboardLayout: defaultDashboardLayout,\r\n          defaultTimeRange: '24h',\r\n          notifications: defaultNotificationPreferences,\r\n          refreshIntervals: defaultRefreshIntervals,\r\n        },\r\n\r\n        reorderWidgets: (newOrder: WidgetId[]) =>\r\n          set(state => ({\r\n            preferences: {\r\n              ...state.preferences,\r\n              dashboardLayout: {\r\n                ...state.preferences.dashboardLayout,\r\n                widgetOrder: newOrder,\r\n              },\r\n            },\r\n          })),\r\n\r\n        resetPreferencesToDefaults: () =>\r\n          set(state => ({\r\n            preferences: {\r\n              dashboardLayout: defaultDashboardLayout,\r\n              defaultTimeRange: '24h',\r\n              notifications: defaultNotificationPreferences,\r\n              refreshIntervals: defaultRefreshIntervals,\r\n            },\r\n          })),\r\n\r\n        resumeAllMonitoring: () =>\r\n          set(state => ({\r\n            monitoring: {\r\n              ...state.monitoring,\r\n              isEnabled: true,\r\n              pausedDataTypes: new Set(),\r\n            },\r\n          })),\r\n\r\n        resumeDataType: (dataType: DataType) =>\r\n          set(state => {\r\n            const newPausedDataTypes = new Set(\r\n              state.monitoring.pausedDataTypes\r\n            );\r\n            newPausedDataTypes.delete(dataType);\r\n            return {\r\n              monitoring: {\r\n                ...state.monitoring,\r\n                pausedDataTypes: newPausedDataTypes,\r\n              },\r\n            };\r\n          }),\r\n\r\n        selectAllAlerts: (alertIds: string[]) =>\r\n          set(state => ({\r\n            ui: {\r\n              ...state.ui,\r\n              selectedAlerts: new Set(alertIds),\r\n            },\r\n          })),\r\n\r\n        // UI state actions\r\n        setActiveTab: (tab: DashboardTab) =>\r\n          set(state => ({\r\n            ui: {\r\n              ...state.ui,\r\n              activeTab: tab,\r\n            },\r\n          })),\r\n\r\n        setAlertFilters: (filters: Partial<AlertFilters>) =>\r\n          set(state => ({\r\n            ui: {\r\n              ...state.ui,\r\n              filters: {\r\n                ...state.ui.filters,\r\n                ...filters,\r\n                // Handle Set types properly\r\n                severities: filters.severities || state.ui.filters.severities,\r\n                sources: filters.sources || state.ui.filters.sources,\r\n                statuses: filters.statuses || state.ui.filters.statuses,\r\n              },\r\n            },\r\n          })),\r\n\r\n        setBatchOperationLoading: (\r\n          operation: keyof BatchOperations,\r\n          loading: boolean\r\n        ) =>\r\n          set(state => ({\r\n            ui: {\r\n              ...state.ui,\r\n              batchOperations: {\r\n                ...state.ui.batchOperations,\r\n                [operation]: loading,\r\n              },\r\n            },\r\n          })),\r\n\r\n        setConnectionStatus: (status: ConnectionStatus) =>\r\n          set(state => ({\r\n            monitoring: {\r\n              ...state.monitoring,\r\n              connectionStatus: status,\r\n            },\r\n          })),\r\n\r\n        setDashboardLayout: (layout: DashboardLayout) =>\r\n          set(state => ({\r\n            preferences: {\r\n              ...state.preferences,\r\n              dashboardLayout: {\r\n                ...state.preferences.dashboardLayout,\r\n                layout,\r\n              },\r\n            },\r\n          })),\r\n\r\n        setDefaultTimeRange: (timeRange: TimeRange) =>\r\n          set(state => ({\r\n            preferences: {\r\n              ...state.preferences,\r\n              defaultTimeRange: timeRange,\r\n            },\r\n          })),\r\n\r\n        setGridColumns: (columns: number) =>\r\n          set(state => ({\r\n            preferences: {\r\n              ...state.preferences,\r\n              dashboardLayout: {\r\n                ...state.preferences.dashboardLayout,\r\n                gridColumns: Math.max(1, Math.min(6, columns)), // Clamp between 1-6\r\n              },\r\n            },\r\n          })),\r\n\r\n        // Monitoring state actions\r\n        setMonitoringEnabled: (enabled: boolean) =>\r\n          set(state => ({\r\n            monitoring: {\r\n              ...state.monitoring,\r\n              isEnabled: enabled,\r\n            },\r\n          })),\r\n\r\n        setNotificationPreferences: (\r\n          preferences: Partial<NotificationPreferences>\r\n        ) =>\r\n          set(state => ({\r\n            preferences: {\r\n              ...state.preferences,\r\n              notifications: {\r\n                ...state.preferences.notifications,\r\n                ...preferences,\r\n              },\r\n            },\r\n          })),\r\n\r\n        // Preference actions\r\n        setRefreshInterval: (dataType: DataType, interval: number) =>\r\n          set(state => ({\r\n            preferences: {\r\n              ...state.preferences,\r\n              refreshIntervals: {\r\n                ...state.preferences.refreshIntervals,\r\n                [dataType]: interval,\r\n              },\r\n            },\r\n          })),\r\n\r\n        setWidgetExpanded: (widgetId: WidgetId, expanded: boolean) =>\r\n          set(state => {\r\n            const newExpandedWidgets = new Set(\r\n              state.preferences.dashboardLayout.expandedWidgets\r\n            );\r\n            if (expanded) {\r\n              newExpandedWidgets.add(widgetId);\r\n            } else {\r\n              newExpandedWidgets.delete(widgetId);\r\n            }\r\n            return {\r\n              preferences: {\r\n                ...state.preferences,\r\n                dashboardLayout: {\r\n                  ...state.preferences.dashboardLayout,\r\n                  expandedWidgets: newExpandedWidgets,\r\n                },\r\n              },\r\n            };\r\n          }),\r\n\r\n        toggleAlertSelection: (alertId: string) =>\r\n          set(state => {\r\n            const newSelectedAlerts = new Set(state.ui.selectedAlerts);\r\n            if (newSelectedAlerts.has(alertId)) {\r\n              newSelectedAlerts.delete(alertId);\r\n            } else {\r\n              newSelectedAlerts.add(alertId);\r\n            }\r\n            return {\r\n              ui: {\r\n                ...state.ui,\r\n                selectedAlerts: newSelectedAlerts,\r\n              },\r\n            };\r\n          }),\r\n\r\n        toggleFilterPanel: () =>\r\n          set(state => ({\r\n            ui: {\r\n              ...state.ui,\r\n              isFilterPanelOpen: !state.ui.isFilterPanelOpen,\r\n            },\r\n          })),\r\n\r\n        toggleWidget: (widgetId: WidgetId) =>\r\n          set(state => {\r\n            const newVisibleWidgets = new Set(\r\n              state.preferences.dashboardLayout.visibleWidgets\r\n            );\r\n            if (newVisibleWidgets.has(widgetId)) {\r\n              newVisibleWidgets.delete(widgetId);\r\n            } else {\r\n              newVisibleWidgets.add(widgetId);\r\n            }\r\n            return {\r\n              preferences: {\r\n                ...state.preferences,\r\n                dashboardLayout: {\r\n                  ...state.preferences.dashboardLayout,\r\n                  visibleWidgets: newVisibleWidgets,\r\n                },\r\n              },\r\n            };\r\n          }),\r\n\r\n        ui: {\r\n          activeTab: 'overview',\r\n          batchOperations: defaultBatchOperations,\r\n          filters: defaultAlertFilters,\r\n          isFilterPanelOpen: false,\r\n          selectedAlerts: new Set(),\r\n        },\r\n\r\n        updateLastRefresh: (dataType: DataType, timestamp?: string) =>\r\n          set(state => ({\r\n            monitoring: {\r\n              ...state.monitoring,\r\n              lastRefresh: {\r\n                ...state.monitoring.lastRefresh,\r\n                [dataType]: timestamp || new Date().toISOString(),\r\n              },\r\n            },\r\n          })),\r\n      }),\r\n      {\r\n        // Note: Custom serialization for Set types handled by storage adapter\r\n        merge: (persistedState: any, currentState: ReliabilityState) => {\r\n          const state = persistedState as ReliabilityState;\r\n          // Rehydrate Set objects from arrays\r\n          state.preferences.dashboardLayout.expandedWidgets = new Set(\r\n            state.preferences.dashboardLayout.expandedWidgets\r\n          );\r\n          state.preferences.dashboardLayout.visibleWidgets = new Set(\r\n            state.preferences.dashboardLayout.visibleWidgets\r\n          );\r\n          return { ...currentState, ...state };\r\n        },\r\n        name: 'workhub-reliability-store', // Storage key\r\n        partialize: state => ({\r\n          preferences: {\r\n            dashboardLayout: {\r\n              expandedWidgets: [\r\n                ...state.preferences.dashboardLayout.expandedWidgets,\r\n              ],\r\n              gridColumns: state.preferences.dashboardLayout.gridColumns,\r\n              layout: state.preferences.dashboardLayout.layout,\r\n              visibleWidgets: [\r\n                ...state.preferences.dashboardLayout.visibleWidgets,\r\n              ],\r\n              widgetOrder: state.preferences.dashboardLayout.widgetOrder,\r\n            },\r\n            defaultTimeRange: state.preferences.defaultTimeRange,\r\n            notifications: state.preferences.notifications,\r\n            refreshIntervals: state.preferences.refreshIntervals,\r\n          },\r\n          // Don't persist UI state or monitoring state\r\n        }),\r\n      }\r\n    ),\r\n    {\r\n      name: 'reliability-store', // DevTools name\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;AAED;AACA;;;AAyNA;;CAEC,GACD,MAAM,0BAA4C;IAChD,oBAAoB;IACpB,QAAQ;IACR,oBAAoB;IACpB,cAAc;IACd,mBAAmB;IACnB,QAAQ;IACR,SAAS;AACX;AAEA,MAAM,yBAAqD;IACzD,iBAAiB,IAAI,IAAI;QACvB;QACA;QACA;QACA;KACD;IACD,aAAa;IACb,QAAQ;IACR,gBAAgB,IAAI,IAAI;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,aAAa;QACX;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,MAAM,iCAA0D;IAC9D,oBAAoB;IACpB,gBAAgB;IAChB,iBAAiB;IACjB,cAAc;AAChB;AAEA,MAAM,sBAAoC;IACxC,YAAY;IACZ,YAAY,IAAI,IAAI;QAAC;QAAY;QAAQ;QAAO;KAAS;IACzD,SAAS,IAAI;IACb,UAAU,IAAI,IAAI;QAAC;QAAgB;KAAS;IAC5C,WAAW;AACb;AAEA,MAAM,yBAA0C;IAC9C,+BAA+B;IAC/B,kBAAkB;IAClB,oBAAoB;IACpB,2BAA2B;AAC7B;AAMO,MAAM,sBAAsB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACtC,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,mBAAmB,IACjB,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,SAAS;oBACX;gBACF,CAAC;QAEH,qBAAqB,IACnB,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,gBAAgB,IAAI;oBACtB;gBACF,CAAC;QAEH,kBAAkB;YAChB,MAAM,EAAE,EAAE,EAAE,GAAG;YACf,MAAM,UAAU,GAAG,OAAO;YAC1B,MAAM,gBAAuC,CAAC;YAE9C,yCAAyC;YACzC,IAAI,QAAQ,UAAU,CAAC,IAAI,KAAK,GAAG;gBACjC,cAAc,UAAU,GAAG,QAAQ,UAAU;YAC/C;YACA,IAAI,QAAQ,QAAQ,CAAC,IAAI,KAAK,GAAG;gBAC/B,cAAc,QAAQ,GAAG,QAAQ,QAAQ;YAC3C;YACA,IAAI,QAAQ,OAAO,CAAC,IAAI,GAAG,GAAG;gBAC5B,cAAc,OAAO,GAAG,QAAQ,OAAO;YACzC;YACA,IAAI,QAAQ,UAAU,CAAC,IAAI,IAAI;gBAC7B,cAAc,UAAU,GAAG,QAAQ,UAAU;YAC/C;YACA,IAAI,QAAQ,SAAS,KAAK,OAAO;gBAC/B,cAAc,SAAS,GAAG,QAAQ,SAAS;YAC7C;YAEA,OAAO;QACT;QAEA,uBAAuB;YACrB,MAAM,EAAE,EAAE,EAAE,GAAG;YACf,OAAO,GAAG,cAAc,CAAC,IAAI;QAC/B;QAEA,qBAAqB;QACrB,mBAAmB;YACjB,MAAM,EAAE,WAAW,EAAE,GAAG;YACxB,OAAO,YAAY,eAAe,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA,WACpD,YAAY,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC;QAEnD;QAEA,kBAAkB,CAAC;YACjB,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,OACE,WAAW,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC,WAAW,SAAS;QAErE;QAEA,YAAY;YACV,kBAAkB;YAClB,WAAW;YACX,aAAa,CAAC;YACd,iBAAiB,IAAI;QACvB;QAEA,oBAAoB,IAClB,IAAI,CAAA,QAAS,CAAC;oBACZ,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,WAAW;wBACX,iBAAiB,IAAI,IAAI;4BACvB;4BACA;4BACA;4BACA;4BACA;4BACA;4BACA;yBACD;oBACH;gBACF,CAAC;QAEH,eAAe,CAAC,WACd,IAAI,CAAA;gBACF,MAAM,qBAAqB,IAAI,IAC7B,MAAM,UAAU,CAAC,eAAe;gBAElC,mBAAmB,GAAG,CAAC;gBACvB,OAAO;oBACL,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,iBAAiB;oBACnB;gBACF;YACF;QAEF,gBAAgB;QAChB,aAAa;YACX,iBAAiB;YACjB,kBAAkB;YAClB,eAAe;YACf,kBAAkB;QACpB;QAEA,gBAAgB,CAAC,WACf,IAAI,CAAA,QAAS,CAAC;oBACZ,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB,iBAAiB;4BACf,GAAG,MAAM,WAAW,CAAC,eAAe;4BACpC,aAAa;wBACf;oBACF;gBACF,CAAC;QAEH,4BAA4B,IAC1B,IAAI,CAAA,QAAS,CAAC;oBACZ,aAAa;wBACX,iBAAiB;wBACjB,kBAAkB;wBAClB,eAAe;wBACf,kBAAkB;oBACpB;gBACF,CAAC;QAEH,qBAAqB,IACnB,IAAI,CAAA,QAAS,CAAC;oBACZ,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,WAAW;wBACX,iBAAiB,IAAI;oBACvB;gBACF,CAAC;QAEH,gBAAgB,CAAC,WACf,IAAI,CAAA;gBACF,MAAM,qBAAqB,IAAI,IAC7B,MAAM,UAAU,CAAC,eAAe;gBAElC,mBAAmB,MAAM,CAAC;gBAC1B,OAAO;oBACL,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,iBAAiB;oBACnB;gBACF;YACF;QAEF,iBAAiB,CAAC,WAChB,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,gBAAgB,IAAI,IAAI;oBAC1B;gBACF,CAAC;QAEH,mBAAmB;QACnB,cAAc,CAAC,MACb,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,WAAW;oBACb;gBACF,CAAC;QAEH,iBAAiB,CAAC,UAChB,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,SAAS;4BACP,GAAG,MAAM,EAAE,CAAC,OAAO;4BACnB,GAAG,OAAO;4BACV,4BAA4B;4BAC5B,YAAY,QAAQ,UAAU,IAAI,MAAM,EAAE,CAAC,OAAO,CAAC,UAAU;4BAC7D,SAAS,QAAQ,OAAO,IAAI,MAAM,EAAE,CAAC,OAAO,CAAC,OAAO;4BACpD,UAAU,QAAQ,QAAQ,IAAI,MAAM,EAAE,CAAC,OAAO,CAAC,QAAQ;wBACzD;oBACF;gBACF,CAAC;QAEH,0BAA0B,CACxB,WACA,UAEA,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,iBAAiB;4BACf,GAAG,MAAM,EAAE,CAAC,eAAe;4BAC3B,CAAC,UAAU,EAAE;wBACf;oBACF;gBACF,CAAC;QAEH,qBAAqB,CAAC,SACpB,IAAI,CAAA,QAAS,CAAC;oBACZ,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,kBAAkB;oBACpB;gBACF,CAAC;QAEH,oBAAoB,CAAC,SACnB,IAAI,CAAA,QAAS,CAAC;oBACZ,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB,iBAAiB;4BACf,GAAG,MAAM,WAAW,CAAC,eAAe;4BACpC;wBACF;oBACF;gBACF,CAAC;QAEH,qBAAqB,CAAC,YACpB,IAAI,CAAA,QAAS,CAAC;oBACZ,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB,kBAAkB;oBACpB;gBACF,CAAC;QAEH,gBAAgB,CAAC,UACf,IAAI,CAAA,QAAS,CAAC;oBACZ,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB,iBAAiB;4BACf,GAAG,MAAM,WAAW,CAAC,eAAe;4BACpC,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;wBACvC;oBACF;gBACF,CAAC;QAEH,2BAA2B;QAC3B,sBAAsB,CAAC,UACrB,IAAI,CAAA,QAAS,CAAC;oBACZ,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,WAAW;oBACb;gBACF,CAAC;QAEH,4BAA4B,CAC1B,cAEA,IAAI,CAAA,QAAS,CAAC;oBACZ,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB,eAAe;4BACb,GAAG,MAAM,WAAW,CAAC,aAAa;4BAClC,GAAG,WAAW;wBAChB;oBACF;gBACF,CAAC;QAEH,qBAAqB;QACrB,oBAAoB,CAAC,UAAoB,WACvC,IAAI,CAAA,QAAS,CAAC;oBACZ,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB,kBAAkB;4BAChB,GAAG,MAAM,WAAW,CAAC,gBAAgB;4BACrC,CAAC,SAAS,EAAE;wBACd;oBACF;gBACF,CAAC;QAEH,mBAAmB,CAAC,UAAoB,WACtC,IAAI,CAAA;gBACF,MAAM,qBAAqB,IAAI,IAC7B,MAAM,WAAW,CAAC,eAAe,CAAC,eAAe;gBAEnD,IAAI,UAAU;oBACZ,mBAAmB,GAAG,CAAC;gBACzB,OAAO;oBACL,mBAAmB,MAAM,CAAC;gBAC5B;gBACA,OAAO;oBACL,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB,iBAAiB;4BACf,GAAG,MAAM,WAAW,CAAC,eAAe;4BACpC,iBAAiB;wBACnB;oBACF;gBACF;YACF;QAEF,sBAAsB,CAAC,UACrB,IAAI,CAAA;gBACF,MAAM,oBAAoB,IAAI,IAAI,MAAM,EAAE,CAAC,cAAc;gBACzD,IAAI,kBAAkB,GAAG,CAAC,UAAU;oBAClC,kBAAkB,MAAM,CAAC;gBAC3B,OAAO;oBACL,kBAAkB,GAAG,CAAC;gBACxB;gBACA,OAAO;oBACL,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,gBAAgB;oBAClB;gBACF;YACF;QAEF,mBAAmB,IACjB,IAAI,CAAA,QAAS,CAAC;oBACZ,IAAI;wBACF,GAAG,MAAM,EAAE;wBACX,mBAAmB,CAAC,MAAM,EAAE,CAAC,iBAAiB;oBAChD;gBACF,CAAC;QAEH,cAAc,CAAC,WACb,IAAI,CAAA;gBACF,MAAM,oBAAoB,IAAI,IAC5B,MAAM,WAAW,CAAC,eAAe,CAAC,cAAc;gBAElD,IAAI,kBAAkB,GAAG,CAAC,WAAW;oBACnC,kBAAkB,MAAM,CAAC;gBAC3B,OAAO;oBACL,kBAAkB,GAAG,CAAC;gBACxB;gBACA,OAAO;oBACL,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB,iBAAiB;4BACf,GAAG,MAAM,WAAW,CAAC,eAAe;4BACpC,gBAAgB;wBAClB;oBACF;gBACF;YACF;QAEF,IAAI;YACF,WAAW;YACX,iBAAiB;YACjB,SAAS;YACT,mBAAmB;YACnB,gBAAgB,IAAI;QACtB;QAEA,mBAAmB,CAAC,UAAoB,YACtC,IAAI,CAAA,QAAS,CAAC;oBACZ,YAAY;wBACV,GAAG,MAAM,UAAU;wBACnB,aAAa;4BACX,GAAG,MAAM,UAAU,CAAC,WAAW;4BAC/B,CAAC,SAAS,EAAE,aAAa,IAAI,OAAO,WAAW;wBACjD;oBACF;gBACF,CAAC;IACL,CAAC,GACD;IACE,sEAAsE;IACtE,OAAO,CAAC,gBAAqB;QAC3B,MAAM,QAAQ;QACd,oCAAoC;QACpC,MAAM,WAAW,CAAC,eAAe,CAAC,eAAe,GAAG,IAAI,IACtD,MAAM,WAAW,CAAC,eAAe,CAAC,eAAe;QAEnD,MAAM,WAAW,CAAC,eAAe,CAAC,cAAc,GAAG,IAAI,IACrD,MAAM,WAAW,CAAC,eAAe,CAAC,cAAc;QAElD,OAAO;YAAE,GAAG,YAAY;YAAE,GAAG,KAAK;QAAC;IACrC;IACA,MAAM;IACN,YAAY,CAAA,QAAS,CAAC;YACpB,aAAa;gBACX,iBAAiB;oBACf,iBAAiB;2BACZ,MAAM,WAAW,CAAC,eAAe,CAAC,eAAe;qBACrD;oBACD,aAAa,MAAM,WAAW,CAAC,eAAe,CAAC,WAAW;oBAC1D,QAAQ,MAAM,WAAW,CAAC,eAAe,CAAC,MAAM;oBAChD,gBAAgB;2BACX,MAAM,WAAW,CAAC,eAAe,CAAC,cAAc;qBACpD;oBACD,aAAa,MAAM,WAAW,CAAC,eAAe,CAAC,WAAW;gBAC5D;gBACA,kBAAkB,MAAM,WAAW,CAAC,gBAAgB;gBACpD,eAAe,MAAM,WAAW,CAAC,aAAa;gBAC9C,kBAAkB,MAAM,WAAW,CAAC,gBAAgB;YACtD;QAEF,CAAC;AACH,IAEF;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 1668, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/hooks/index.ts"], "sourcesContent": ["/**\r\n * @file Centralized exports for all custom hooks\r\n * @module hooks/index\r\n */\r\n\r\n// Store hooks (re-exported for convenience)\r\nexport { useAppStore } from '../stores/zustand/appStore';\r\nexport { useReliabilityStore } from '../stores/zustand/reliabilityStore';\r\nexport { useUiStore } from '../stores/zustand/uiStore';\r\n\r\n// WebSocket hooks\r\n/* // Removing these exports\r\nexport {\r\n  useReliabilityWebSocket,\r\n  useReliabilityWebSocketSubscription,\r\n  useReliabilityWebSocketStatus,\r\n  useReliabilityWebSocketManager,\r\n} from './useReliabilityWebSocket';\r\n*/\r\n\r\n// API hooks\r\nexport {\r\n  useApiQuery,\r\n  useDependentApiQuery,\r\n  usePaginatedApiQuery,\r\n  type ApiQueryOptions,\r\n  type ApiQueryResult,\r\n} from '../../hooks/api';\r\n\r\n// Other existing hooks\r\n// export { useAuthenticatedApi } from './useAuthenticatedApi'; // File not found\r\n// export { useGlobalBackgroundSync } from './useBackgroundSync'; // Removed export\r\n\r\n// UI hooks - Updated to use new standardized locations\r\nexport { type ModalContent, useModal } from '../../hooks/ui/useModal';\r\nexport {\r\n  useNotifications,\r\n  useWorkHubNotifications,\r\n} from '../../hooks/ui/useNotifications';\r\nexport { useSidebar } from '../../hooks/ui/useSidebar';\r\nexport { useTheme } from '../../hooks/ui/useTheme';\r\nexport { useUiPreferences } from '../../hooks/ui/useUiPreferences';\r\n\r\n// Re-export useWorkHubCore from main hooks index to avoid duplication\r\nexport { useWorkHubCore } from '../../hooks';\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,4CAA4C;;AAC5C;AACA;AACA;AAEA,kBAAkB;AAClB;;;;;;;AAOA,GAEA,YAAY;AACZ;AAQA,uBAAuB;AACvB,iFAAiF;AACjF,mFAAmF;AAEnF,uDAAuD;AACvD;AACA;AAIA;AACA;AACA;AAEA,sEAAsE;AACtE", "debugId": null}}, {"offset": {"line": 1734, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/transformers/delegationEnrichment.ts"], "sourcesContent": ["/**\r\n * @file Delegation enrichment transformer following established patterns\r\n * @description Handles the enrichment of delegation data with employee and vehicle details\r\n * @module transformers/delegationEnrichment\r\n */\r\n\r\nimport type {\r\n  Delegation,\r\n  DelegationDriver,\r\n  DelegationEscort,\r\n  DelegationVehicleAssignment,\r\n  Employee,\r\n  Vehicle,\r\n} from '../types/domain';\r\n\r\n/**\r\n * Transformer class for enriching delegation data with related entities\r\n * Follows the same pattern as other transformers in the codebase\r\n */\r\nexport class DelegationEnrichmentTransformer {\r\n  /**\r\n   * Main enrichment method that combines delegation data with employee and vehicle details\r\n   * @param delegation - Base delegation data\r\n   * @param employees - Array of employees\r\n   * @param vehicles - Array of vehicles\r\n   * @returns Fully enriched delegation\r\n   */\r\n  static enrich(\r\n    delegation: Delegation,\r\n    employees: Employee[],\r\n    vehicles: Vehicle[]\r\n  ): Delegation {\r\n    const { employeeMap, vehicleMap } = this.createLookupMaps(\r\n      employees,\r\n      vehicles\r\n    );\r\n\r\n    return {\r\n      ...delegation,\r\n      drivers: this.enrichDrivers(delegation.drivers, employeeMap) ?? [],\r\n      escorts: this.enrichEscorts(delegation.escorts, employeeMap) ?? [],\r\n      vehicles: this.enrichVehicles(delegation.vehicles, vehicleMap) ?? [],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates optimized lookup maps for O(1) performance\r\n   * @param employees - Array of employees\r\n   * @param vehicles - Array of vehicles\r\n   * @returns Object containing employee and vehicle maps\r\n   */\r\n  private static createLookupMaps(employees: Employee[], vehicles: Vehicle[]) {\r\n    return {\r\n      employeeMap: new Map(employees.map(emp => [emp.id, emp])),\r\n      vehicleMap: new Map(vehicles.map(veh => [veh.id, veh])),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Enriches driver assignments with employee details\r\n   * @param drivers - Array of driver assignments\r\n   * @param employeeMap - Map of employees for O(1) lookup\r\n   * @returns Enriched driver assignments\r\n   */\r\n  private static enrichDrivers(\r\n    drivers: DelegationDriver[] | undefined,\r\n    employeeMap: Map<number, Employee>\r\n  ): DelegationDriver[] | undefined {\r\n    return drivers?.map(driver => {\r\n      const employee =\r\n        driver.employee || employeeMap.get(Number(driver.employeeId));\r\n      return {\r\n        ...driver,\r\n        ...(employee && { employee }),\r\n      };\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Enriches escort assignments with employee details\r\n   * @param escorts - Array of escort assignments\r\n   * @param employeeMap - Map of employees for O(1) lookup\r\n   * @returns Enriched escort assignments\r\n   */\r\n  private static enrichEscorts(\r\n    escorts: DelegationEscort[] | undefined,\r\n    employeeMap: Map<number, Employee>\r\n  ): DelegationEscort[] | undefined {\r\n    return escorts?.map(escort => {\r\n      const employee =\r\n        escort.employee || employeeMap.get(Number(escort.employeeId));\r\n      return {\r\n        ...escort,\r\n        ...(employee && { employee }),\r\n      };\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Enriches vehicle assignments with vehicle details\r\n   * @param vehicles - Array of vehicle assignments\r\n   * @param vehicleMap - Map of vehicles for O(1) lookup\r\n   * @returns Enriched vehicle assignments\r\n   */\r\n  private static enrichVehicles(\r\n    vehicles: DelegationVehicleAssignment[] | undefined,\r\n    vehicleMap: Map<number, Vehicle>\r\n  ): DelegationVehicleAssignment[] | undefined {\r\n    return vehicles?.map(vehicleAssignment => {\r\n      const vehicle =\r\n        vehicleAssignment.vehicle ||\r\n        vehicleMap.get(vehicleAssignment.vehicleId);\r\n      return {\r\n        ...vehicleAssignment,\r\n        ...(vehicle && { vehicle }),\r\n      };\r\n    });\r\n  }\r\n}\r\n\r\n// Export the main enrichment function for backward compatibility\r\nexport const enrichDelegation = (\r\n  delegation: Delegation,\r\n  employees: Employee[],\r\n  vehicles: Vehicle[]\r\n): Delegation => {\r\n  return DelegationEnrichmentTransformer.enrich(\r\n    delegation,\r\n    employees,\r\n    vehicles\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAeM,MAAM;IACX;;;;;;GAMC,GACD,OAAO,OACL,UAAsB,EACtB,SAAqB,EACrB,QAAmB,EACP;QACZ,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,gBAAgB,CACvD,WACA;QAGF,OAAO;YACL,GAAG,UAAU;YACb,SAAS,IAAI,CAAC,aAAa,CAAC,WAAW,OAAO,EAAE,gBAAgB,EAAE;YAClE,SAAS,IAAI,CAAC,aAAa,CAAC,WAAW,OAAO,EAAE,gBAAgB,EAAE;YAClE,UAAU,IAAI,CAAC,cAAc,CAAC,WAAW,QAAQ,EAAE,eAAe,EAAE;QACtE;IACF;IAEA;;;;;GAKC,GACD,OAAe,iBAAiB,SAAqB,EAAE,QAAmB,EAAE;QAC1E,OAAO;YACL,aAAa,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA,MAAO;oBAAC,IAAI,EAAE;oBAAE;iBAAI;YACvD,YAAY,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,MAAO;oBAAC,IAAI,EAAE;oBAAE;iBAAI;QACvD;IACF;IAEA;;;;;GAKC,GACD,OAAe,cACb,OAAuC,EACvC,WAAkC,EACF;QAChC,OAAO,SAAS,IAAI,CAAA;YAClB,MAAM,WACJ,OAAO,QAAQ,IAAI,YAAY,GAAG,CAAC,OAAO,OAAO,UAAU;YAC7D,OAAO;gBACL,GAAG,MAAM;gBACT,GAAI,YAAY;oBAAE;gBAAS,CAAC;YAC9B;QACF;IACF;IAEA;;;;;GAKC,GACD,OAAe,cACb,OAAuC,EACvC,WAAkC,EACF;QAChC,OAAO,SAAS,IAAI,CAAA;YAClB,MAAM,WACJ,OAAO,QAAQ,IAAI,YAAY,GAAG,CAAC,OAAO,OAAO,UAAU;YAC7D,OAAO;gBACL,GAAG,MAAM;gBACT,GAAI,YAAY;oBAAE;gBAAS,CAAC;YAC9B;QACF;IACF;IAEA;;;;;GAKC,GACD,OAAe,eACb,QAAmD,EACnD,UAAgC,EACW;QAC3C,OAAO,UAAU,IAAI,CAAA;YACnB,MAAM,UACJ,kBAAkB,OAAO,IACzB,WAAW,GAAG,CAAC,kBAAkB,SAAS;YAC5C,OAAO;gBACL,GAAG,iBAAiB;gBACpB,GAAI,WAAW;oBAAE;gBAAQ,CAAC;YAC5B;QACF;IACF;AACF;AAGO,MAAM,mBAAmB,CAC9B,YACA,WACA;IAEA,OAAO,gCAAgC,MAAM,CAC3C,YACA,WACA;AAEJ", "debugId": null}}, {"offset": {"line": 1836, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/delegationQueries.ts"], "sourcesContent": ["/**\r\n * @file Delegation query configurations following Single Responsibility Principle\r\n * @description Centralized query configurations for delegation-related data fetching\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport type { Delegation } from '../../types/domain';\r\n\r\nimport {\r\n  delegationApiService,\r\n  employeeApiService,\r\n  vehicleApiService,\r\n} from '../../api/services/apiServiceFactory'; // Use centralized services\r\nimport { DelegationTransformer } from '../../transformers/delegationTransformer';\r\n\r\n/**\r\n * Query keys for delegation-related queries\r\n */\r\nexport const delegationQueryKeys = {\r\n  all: ['delegations'] as const,\r\n  detail: (id: string) => ['delegations', id] as const,\r\n  withAssignments: (id: string) =>\r\n    ['delegations', id, 'with-assignments'] as const,\r\n};\r\n\r\n/**\r\n * Creates query configuration for fetching a single delegation\r\n */\r\nexport const createDelegationQuery = (id: string) => ({\r\n  enabled: !!id,\r\n  queryFn: () => delegationApiService.getById(id),\r\n  queryKey: delegationQueryKeys.detail(id),\r\n  staleTime: 5 * 60 * 1000, // 5 minutes\r\n});\r\n\r\n/**\r\n * Creates query configuration for fetching all employees\r\n */\r\nexport const createEmployeesQuery = () => ({\r\n  queryFn: () => employeeApiService.getAll(),\r\n  queryKey: ['employees'] as const,\r\n  staleTime: 10 * 60 * 1000, // 10 minutes - employees change less frequently\r\n});\r\n\r\n/**\r\n * Creates query configuration for fetching all vehicles\r\n */\r\nexport const createVehiclesQuery = () => ({\r\n  queryFn: () => vehicleApiService.getAll(),\r\n  queryKey: ['vehicles'] as const,\r\n  staleTime: 10 * 60 * 1000, // 10 minutes - vehicles change less frequently\r\n});\r\n\r\n/**\r\n * Creates parallel query configurations for delegation with assignments\r\n */\r\nexport const createDelegationWithAssignmentsQueries = (id: string) => [\r\n  createDelegationQuery(id),\r\n  createEmployeesQuery(),\r\n  createVehiclesQuery(),\r\n];\r\n\r\n/**\r\n * Standard query options for delegation queries\r\n */\r\nexport const delegationQueryOptions: Partial<\r\n  UseQueryOptions<Delegation, Error>\r\n> = {\r\n  gcTime: 10 * 60 * 1000, // 10 minutes garbage collection time\r\n  retry: 3,\r\n  retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),\r\n  staleTime: 5 * 60 * 1000,\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAMD,sTAI+C,2BAA2B;AAJ1E;;AAUO,MAAM,sBAAsB;IACjC,KAAK;QAAC;KAAc;IACpB,QAAQ,CAAC,KAAe;YAAC;YAAe;SAAG;IAC3C,iBAAiB,CAAC,KAChB;YAAC;YAAe;YAAI;SAAmB;AAC3C;AAKO,MAAM,wBAAwB,CAAC,KAAe,CAAC;QACpD,SAAS,CAAC,CAAC;QACX,SAAS,IAAM,2IAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC;QAC5C,UAAU,oBAAoB,MAAM,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB,CAAC;AAKM,MAAM,uBAAuB,IAAM,CAAC;QACzC,SAAS,IAAM,2IAAA,CAAA,qBAAkB,CAAC,MAAM;QACxC,UAAU;YAAC;SAAY;QACvB,WAAW,KAAK,KAAK;IACvB,CAAC;AAKM,MAAM,sBAAsB,IAAM,CAAC;QACxC,SAAS,IAAM,2IAAA,CAAA,oBAAiB,CAAC,MAAM;QACvC,UAAU;YAAC;SAAW;QACtB,WAAW,KAAK,KAAK;IACvB,CAAC;AAKM,MAAM,yCAAyC,CAAC,KAAe;QACpE,sBAAsB;QACtB;QACA;KACD;AAKM,MAAM,yBAET;IACF,QAAQ,KAAK,KAAK;IAClB,OAAO;IACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;IAC/D,WAAW,IAAI,KAAK;AACtB", "debugId": null}}, {"offset": {"line": 1904, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/useDelegations.ts"], "sourcesContent": ["/**\r\n * @file TanStack Query hooks for Delegation-related data.\r\n * These hooks manage fetching, caching, and mutating delegation data,\r\n * integrating with the DelegationApiService and DelegationTransformer.\r\n * @module stores/queries/useDelegations\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport { useMutation, useQueries, useQueryClient } from '@tanstack/react-query';\r\nimport { useCallback, useMemo } from 'react';\r\n\r\nimport { undefinedToNull } from '@/lib/utils/typeHelpers';\r\n\r\n// import { DelegationFormData } from '../../schemas/delegationSchemas'; // Not directly used by hooks' public API\r\nimport type { UpdateDelegationRequest } from '../../types/api'; // For useUpdateDelegation\r\nimport type {\r\n  CreateDelegationData,\r\n  Delegation,\r\n  DelegationStatusPrisma,\r\n  FlightDetails,\r\n  // DelegationEscort, // Not directly used in optimistic updates in a way that needs separate import here\r\n  // DelegationDriver,\r\n  // DelegationVehicleAssignment,\r\n} from '../../types/domain';\r\n\r\nimport { useCrudQuery } from '../../../hooks/api/useSmartQuery'; // Adjusted import path\r\nimport { delegationApiService } from '../../api/services/apiServiceFactory'; // Use centralized service\r\nimport { enrichDelegation } from '../../transformers/delegationEnrichment';\r\nimport { DelegationTransformer } from '../../transformers/delegationTransformer';\r\nimport {\r\n  createDelegationWithAssignmentsQueries,\r\n  delegationQueryKeys,\r\n} from './delegationQueries';\r\n\r\nexport const useDelegations = (\r\n  options?: Omit<\r\n    UseQueryOptions<Delegation[], Error>,\r\n    'queryFn' | 'queryKey'\r\n  > & { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<Delegation[], Error>(\r\n    [...delegationQueryKeys.all], // queryKey - spread for mutability\r\n    async () => {\r\n      const result = await delegationApiService.getAll();\r\n      return result.data;\r\n    },\r\n    'delegation', // entityType\r\n    {\r\n      staleTime: 0, // Existing staleTime\r\n      ...options, // Spread additional options\r\n    }\r\n  );\r\n};\r\n\r\nexport const useDelegation = (id: string) => {\r\n  return useCrudQuery<Delegation, Error>(\r\n    [...delegationQueryKeys.detail(id)],\r\n    async () => {\r\n      return await delegationApiService.getById(id);\r\n    },\r\n    'delegation', // entityType for WebSocket events\r\n    {\r\n      enabled: !!id,\r\n      staleTime: 5 * 60 * 1000,\r\n    }\r\n  );\r\n};\r\n\r\n// ✅ OPTIMIZED: Fast parallel data fetching for delegation with assignments\r\nexport const useDelegationWithAssignments = (id: string) => {\r\n  // Execute all queries in parallel using useQueries for maximum performance\r\n  const results = useQueries({\r\n    queries: createDelegationWithAssignmentsQueries(id),\r\n  });\r\n\r\n  const [delegationQuery, employeesQuery, vehiclesQuery] = results;\r\n\r\n  // Compute enriched delegation when all data is available\r\n  const enrichedDelegation = useMemo(() => {\r\n    if (\r\n      !delegationQuery?.data ||\r\n      !employeesQuery?.data ||\r\n      !vehiclesQuery?.data\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // ✅ PRODUCTION FIX: delegationQuery.data is already transformed by the service layer\r\n      // No need to apply DelegationTransformer.fromApi() again\r\n      const delegation = delegationQuery.data as Delegation;\r\n      return enrichDelegation(\r\n        delegation,\r\n        employeesQuery.data as any,\r\n        vehiclesQuery.data as any\r\n      );\r\n    } catch (error) {\r\n      console.error('Error enriching delegation data:', error);\r\n      throw error;\r\n    }\r\n  }, [delegationQuery?.data, employeesQuery?.data, vehiclesQuery?.data]);\r\n\r\n  // ✅ PRODUCTION FIX: Memoize refetch function to prevent infinite re-renders\r\n  const refetch = useCallback(() => {\r\n    delegationQuery?.refetch();\r\n    employeesQuery?.refetch();\r\n    vehiclesQuery?.refetch();\r\n  }, [\r\n    delegationQuery?.refetch,\r\n    employeesQuery?.refetch,\r\n    vehiclesQuery?.refetch,\r\n  ]);\r\n\r\n  // Return combined state with optimized loading states\r\n  return {\r\n    data: enrichedDelegation,\r\n    error:\r\n      delegationQuery?.error || employeesQuery?.error || vehiclesQuery?.error,\r\n    isError:\r\n      delegationQuery?.isError ||\r\n      employeesQuery?.isError ||\r\n      vehiclesQuery?.isError,\r\n    isLoading:\r\n      delegationQuery?.isLoading ||\r\n      employeesQuery?.isLoading ||\r\n      vehiclesQuery?.isLoading,\r\n    isPending:\r\n      delegationQuery?.isPending ||\r\n      employeesQuery?.isPending ||\r\n      vehiclesQuery?.isPending,\r\n    refetch,\r\n  };\r\n};\r\n\r\n// ✅ BACKWARD COMPATIBILITY: Alias for the optimized hook\r\nexport const useDelegationEnriched = useDelegationWithAssignments;\r\n\r\nexport const useCreateDelegation = () => {\r\n  const queryClient = useQueryClient();\r\n  interface CreateContext {\r\n    previousDelegations: Delegation[] | undefined;\r\n  }\r\n\r\n  return useMutation<Delegation, Error, CreateDelegationData, CreateContext>({\r\n    mutationFn: async (delegationData: CreateDelegationData) => {\r\n      const apiPayload = DelegationTransformer.toCreateRequest(delegationData);\r\n      // ✅ PRODUCTION FIX: delegationApiService.create() already applies transformation\r\n      return await delegationApiService.create(apiPayload);\r\n    },\r\n    onError: (err, _delegationData, context) => {\r\n      if (context?.previousDelegations) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.all,\r\n          context.previousDelegations\r\n        );\r\n      }\r\n      console.error('Failed to create delegation:', err);\r\n      // Invalidate to refetch correct data on error\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n    onMutate: async (delegationData: CreateDelegationData) => {\r\n      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });\r\n      const previousDelegations = queryClient.getQueryData<Delegation[]>(\r\n        delegationQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation[]>(\r\n        delegationQueryKeys.all,\r\n        (old = []) => {\r\n          const tempId = `optimistic-${Date.now()}`;\r\n          const now = new Date().toISOString();\r\n\r\n          const optimisticArrivalFlight: FlightDetails | null =\r\n            delegationData.flightArrivalDetails\r\n              ? {\r\n                  id: `optimistic-flight-arr-${Date.now()}`,\r\n                  ...delegationData.flightArrivalDetails,\r\n                }\r\n              : null;\r\n\r\n          const optimisticDepartureFlight: FlightDetails | null =\r\n            delegationData.flightDepartureDetails\r\n              ? {\r\n                  id: `optimistic-flight-dep-${Date.now() + 1}`,\r\n                  ...delegationData.flightDepartureDetails,\r\n                }\r\n              : null;\r\n\r\n          const optimisticDelegates: Delegation['delegates'] =\r\n            delegationData.delegates?.map((d, index) => ({\r\n              id: `optimistic-delegate-${tempId}-${index}`,\r\n              name: d.name, // Use d.name directly\r\n              notes: d.notes ?? null,\r\n              title: d.title, // Use d.title directly\r\n            })) || [];\r\n\r\n          const optimisticDelegation: Delegation = {\r\n            arrivalFlight: optimisticArrivalFlight ?? null,\r\n            createdAt: now,\r\n            delegates: optimisticDelegates,\r\n            departureFlight: optimisticDepartureFlight ?? null,\r\n            drivers:\r\n              delegationData.drivers?.map(d => ({\r\n                createdAt: now, // Placeholder timestamp\r\n                createdBy: null, // Placeholder\r\n                delegationId: tempId, // Link to optimistic delegation\r\n                employeeId: d.employeeId, // Keep as number\r\n                id: `optimistic-driver-${tempId}-${d.employeeId}`, // Placeholder ID\r\n                notes: d.notes ?? null, // Include notes if available in CreateDelegationData\r\n                updatedAt: now, // Placeholder timestamp\r\n              })) || [],\r\n            durationFrom: delegationData.durationFrom,\r\n            durationTo: delegationData.durationTo,\r\n            escorts:\r\n              delegationData.escorts?.map(e => ({\r\n                createdAt: now, // Placeholder timestamp\r\n                createdBy: null, // Placeholder\r\n                delegationId: tempId, // Link to optimistic delegation\r\n                employeeId: e.employeeId, // Keep as number\r\n                id: `optimistic-escort-${tempId}-${e.employeeId}`, // Placeholder ID\r\n                notes: e.notes ?? null, // Include notes if available in CreateDelegationData\r\n                updatedAt: now, // Placeholder timestamp\r\n              })) || [],\r\n            eventName: delegationData.eventName,\r\n            id: tempId,\r\n            imageUrl: delegationData.imageUrl ?? null,\r\n            invitationFrom: delegationData.invitationFrom ?? null,\r\n            invitationTo: delegationData.invitationTo ?? null,\r\n            location: delegationData.location,\r\n            notes: delegationData.notes ?? null,\r\n            status: delegationData.status || 'Planned',\r\n            statusHistory: [],\r\n            updatedAt: now,\r\n            vehicles:\r\n              delegationData.vehicles?.map(v => ({\r\n                assignedDate: v.assignedDate,\r\n                createdAt: now, // Placeholder timestamp\r\n                createdBy: null, // Placeholder\r\n                delegationId: tempId, // Link to optimistic delegation\r\n                id: `optimistic-vehicle-${tempId}-${v.vehicleId}`, // Placeholder ID\r\n                notes: v.notes ?? null,\r\n                returnDate: v.returnDate ?? null,\r\n                updatedAt: now, // Placeholder timestamp\r\n                vehicleId: v.vehicleId,\r\n              })) || [],\r\n          };\r\n          return [...old, optimisticDelegation];\r\n        }\r\n      );\r\n      return { previousDelegations };\r\n    },\r\n    onSettled: () => {\r\n      // Invalidate to ensure consistency after success or failure\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateDelegation = () => {\r\n  const queryClient = useQueryClient();\r\n  interface UpdateContext {\r\n    previousDelegation: Delegation | undefined;\r\n    previousDelegationsList: Delegation[] | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Delegation,\r\n    Error,\r\n    { data: UpdateDelegationRequest; id: string }, // Corrected: data is UpdateDelegationRequest\r\n    UpdateContext\r\n  >({\r\n    mutationFn: async ({ data, id }) => {\r\n      // ✅ PRODUCTION FIX: delegationApiService.update() already applies transformation\r\n      return await delegationApiService.update(id, data);\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousDelegation) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.detail(variables.id),\r\n          context.previousDelegation\r\n        );\r\n      }\r\n      if (context?.previousDelegationsList) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.all,\r\n          context.previousDelegationsList\r\n        );\r\n      }\r\n      console.error('Failed to update delegation:', err);\r\n      // Invalidate to refetch correct data on error\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n    onMutate: async ({ data, id }) => {\r\n      // data is UpdateDelegationRequest\r\n      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousDelegation = queryClient.getQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id)\r\n      );\r\n      const previousDelegationsList = queryClient.getQueryData<Delegation[]>(\r\n        delegationQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id),\r\n        old => {\r\n          if (!old) return;\r\n          const now = new Date().toISOString();\r\n\r\n          // ✅ PRODUCTION FIX: Use correct field mappings for UpdateDelegationRequest\r\n          const updatedOptimistic: Delegation = {\r\n            ...old,\r\n            // Handle flight details updates\r\n            arrivalFlight: undefinedToNull(\r\n              data.flightArrivalDetails === null\r\n                ? null // Explicitly set to null if requested\r\n                : data.flightArrivalDetails === undefined\r\n                  ? old.arrivalFlight\r\n                  : {\r\n                      airport:\r\n                        data.flightArrivalDetails.airport ||\r\n                        old.arrivalFlight?.airport ||\r\n                        '',\r\n                      dateTime:\r\n                        data.flightArrivalDetails.dateTime ||\r\n                        old.arrivalFlight?.dateTime ||\r\n                        '',\r\n                      flightNumber:\r\n                        data.flightArrivalDetails.flightNumber ||\r\n                        old.arrivalFlight?.flightNumber ||\r\n                        '',\r\n                      id:\r\n                        old.arrivalFlight?.id || `optimistic-arr-${Date.now()}`, // Keep old ID or generate new\r\n                      notes:\r\n                        data.flightArrivalDetails.notes ??\r\n                        old.arrivalFlight?.notes ??\r\n                        null,\r\n                      terminal:\r\n                        data.flightArrivalDetails.terminal ??\r\n                        old.arrivalFlight?.terminal ??\r\n                        null,\r\n                    } // Keep old value if not in request\r\n            ),\r\n            departureFlight: undefinedToNull(\r\n              data.flightDepartureDetails === null\r\n                ? null // Explicitly set to null if requested\r\n                : data.flightDepartureDetails === undefined\r\n                  ? old.departureFlight\r\n                  : {\r\n                      airport:\r\n                        data.flightDepartureDetails.airport ||\r\n                        old.departureFlight?.airport ||\r\n                        '',\r\n                      dateTime:\r\n                        data.flightDepartureDetails.dateTime ||\r\n                        old.departureFlight?.dateTime ||\r\n                        '',\r\n                      flightNumber:\r\n                        data.flightDepartureDetails.flightNumber ||\r\n                        old.departureFlight?.flightNumber ||\r\n                        '',\r\n                      id:\r\n                        old.departureFlight?.id ||\r\n                        `optimistic-dep-${Date.now()}`, // Keep old ID or generate new\r\n                      notes:\r\n                        data.flightDepartureDetails.notes ??\r\n                        old.departureFlight?.notes ??\r\n                        null,\r\n                      terminal:\r\n                        data.flightDepartureDetails.terminal ??\r\n                        old.departureFlight?.terminal ??\r\n                        null,\r\n                    } // Keep old value if not in request\r\n            ),\r\n            durationFrom: data.durationFrom ?? old.durationFrom, // ✅ Direct mapping\r\n            durationTo: data.durationTo ?? old.durationTo, // ✅ Direct mapping\r\n            // Direct field mappings (no transformation needed)\r\n            eventName: data.eventName ?? old.eventName, // ✅ Direct mapping\r\n            imageUrl: undefinedToNull(data.imageUrl ?? old.imageUrl),\r\n            invitationFrom: undefinedToNull(\r\n              data.invitationFrom ?? old.invitationFrom\r\n            ),\r\n            invitationTo: undefinedToNull(\r\n              data.invitationTo ?? old.invitationTo\r\n            ),\r\n            location: data.location ?? old.location,\r\n            notes: undefinedToNull(data.notes ?? old.notes),\r\n            status: (data.status as DelegationStatusPrisma) ?? old.status, // Cast status\r\n            updatedAt: now,\r\n            // Note: Nested assignments (escorts, drivers, vehicles) are typically managed via separate mutations,\r\n            // so they are not included in the main delegation update optimistic logic here.\r\n          };\r\n          return updatedOptimistic;\r\n        }\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation[]>(\r\n        delegationQueryKeys.all,\r\n        (oldList = []) =>\r\n          oldList.map(delegation =>\r\n            delegation.id === id\r\n              ? queryClient.getQueryData<Delegation>(\r\n                  delegationQueryKeys.detail(id)\r\n                ) || delegation\r\n              : delegation\r\n          )\r\n      );\r\n\r\n      return { previousDelegation, previousDelegationsList };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      // Always refetch after error or success\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateDelegationStatus = () => {\r\n  const queryClient = useQueryClient();\r\n  interface StatusUpdateContext {\r\n    previousDelegation: Delegation | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Delegation,\r\n    Error,\r\n    { id: string; status: DelegationStatusPrisma; statusChangeReason?: string },\r\n    StatusUpdateContext\r\n  >({\r\n    mutationFn: async ({ id, status, statusChangeReason }) => {\r\n      const response = await delegationApiService.updateStatus(\r\n        id,\r\n        status,\r\n        statusChangeReason\r\n      );\r\n      return response;\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousDelegation) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.detail(variables.id),\r\n          context.previousDelegation\r\n        );\r\n      }\r\n      console.error('Failed to update delegation status:', err);\r\n    },\r\n    onMutate: async ({ id, status }) => {\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n      const previousDelegation = queryClient.getQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id)\r\n      );\r\n      queryClient.setQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id),\r\n        old => (old ? { ...old, status: status } : undefined)\r\n      );\r\n      return { previousDelegation };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useManageDelegationFlightDetails = () => {\r\n  const queryClient = useQueryClient();\r\n  interface FlightDetailsContext {\r\n    previousDelegation: Delegation | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Delegation,\r\n    Error,\r\n    { flightDetails: FlightDetails; id: string },\r\n    FlightDetailsContext\r\n  >({\r\n    mutationFn: async ({ flightDetails, id }) => {\r\n      // This service method might need adjustment if flightDetails from form is Omit<FlightDetails, 'id'>\r\n      // For now, assuming it expects full FlightDetails (including ID for existing, or will generate for new)\r\n      const response = await delegationApiService.manageFlightDetails(\r\n        id,\r\n        flightDetails\r\n      );\r\n      return response;\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousDelegation) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.detail(variables.id),\r\n          context.previousDelegation\r\n        );\r\n      }\r\n      console.error('Failed to manage delegation flight details:', err);\r\n    },\r\n    onMutate: async ({ flightDetails, id }) => {\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n      const previousDelegation = queryClient.getQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id)\r\n      );\r\n      queryClient.setQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id),\r\n        old => {\r\n          if (!old) return;\r\n          // This optimistic update assumes flightDetails is for arrival.\r\n          // A more robust solution would need to know if it's arrival or departure.\r\n          return { ...old, arrivalFlight: flightDetails };\r\n        }\r\n      );\r\n      return { previousDelegation };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useDeleteDelegation = () => {\r\n  const queryClient = useQueryClient();\r\n  interface DeleteContext {\r\n    previousDelegationsList: Delegation[] | undefined;\r\n  }\r\n\r\n  return useMutation<string, Error, string, DeleteContext>({\r\n    mutationFn: async (id: string) => {\r\n      await delegationApiService.delete(id);\r\n      return id;\r\n    },\r\n    onError: (err, _id, context) => {\r\n      if (context?.previousDelegationsList) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.all,\r\n          context.previousDelegationsList\r\n        );\r\n      }\r\n      console.error('Failed to delete delegation:', err);\r\n    },\r\n    onMutate: async id => {\r\n      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousDelegationsList = queryClient.getQueryData<Delegation[]>(\r\n        delegationQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation[]>(\r\n        delegationQueryKeys.all,\r\n        (old = []) => old.filter(delegation => delegation.id !== id)\r\n      );\r\n\r\n      queryClient.removeQueries({ queryKey: delegationQueryKeys.detail(id) });\r\n\r\n      return { previousDelegationsList };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;AAID;AAAA;AAAA;AACA;AAEA;AAcA,6OAAiE,uBAAuB;AACxF,sTAA6E,0BAA0B;AAAvG;AACA;AACA;AACA;;;;;;;;;;AAKO,MAAM,iBAAiB,CAC5B;;IAKA,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,uJAAA,CAAA,sBAAmB,CAAC,GAAG;KAAC;uCAC5B;YACE,MAAM,SAAS,MAAM,2IAAA,CAAA,uBAAoB,CAAC,MAAM;YAChD,OAAO,OAAO,IAAI;QACpB;sCACA,cACA;QACE,WAAW;QACX,GAAG,OAAO;IACZ;AAEJ;GAlBa;;QAMJ,uIAAA,CAAA,eAAY;;;AAcd,MAAM,gBAAgB,CAAC;;IAC5B,OAAO,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;KAAI;sCACnC;YACE,OAAO,MAAM,2IAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC;QAC5C;qCACA,cACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;IAZa;;QACJ,uIAAA,CAAA,eAAY;;;AAcd,MAAM,+BAA+B,CAAC;;IAC3C,2EAA2E;IAC3E,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE;QACzB,SAAS,CAAA,GAAA,uJAAA,CAAA,yCAAsC,AAAD,EAAE;IAClD;IAEA,MAAM,CAAC,iBAAiB,gBAAgB,cAAc,GAAG;IAEzD,yDAAyD;IACzD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oEAAE;YACjC,IACE,CAAC,iBAAiB,QAClB,CAAC,gBAAgB,QACjB,CAAC,eAAe,MAChB;gBACA;YACF;YAEA,IAAI;gBACF,qFAAqF;gBACrF,yDAAyD;gBACzD,MAAM,aAAa,gBAAgB,IAAI;gBACvC,OAAO,CAAA,GAAA,qJAAA,CAAA,mBAAgB,AAAD,EACpB,YACA,eAAe,IAAI,EACnB,cAAc,IAAI;YAEtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,MAAM;YACR;QACF;mEAAG;QAAC,iBAAiB;QAAM,gBAAgB;QAAM,eAAe;KAAK;IAErE,4EAA4E;IAC5E,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YAC1B,iBAAiB;YACjB,gBAAgB;YAChB,eAAe;QACjB;4DAAG;QACD,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;KAChB;IAED,sDAAsD;IACtD,OAAO;QACL,MAAM;QACN,OACE,iBAAiB,SAAS,gBAAgB,SAAS,eAAe;QACpE,SACE,iBAAiB,WACjB,gBAAgB,WAChB,eAAe;QACjB,WACE,iBAAiB,aACjB,gBAAgB,aAChB,eAAe;QACjB,WACE,iBAAiB,aACjB,gBAAgB,aAChB,eAAe;QACjB;IACF;AACF;IA/Da;;QAEK,gLAAA,CAAA,aAAU;;;AAgErB,MAAM,wBAAwB;AAE9B,MAAM,sBAAsB;;IACjC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAA0D;QACzE,UAAU;+CAAE,OAAO;gBACjB,MAAM,aAAa,sJAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC;gBACzD,iFAAiF;gBACjF,OAAO,MAAM,2IAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC;YAC3C;;QACA,OAAO;+CAAE,CAAC,KAAK,iBAAiB;gBAC9B,IAAI,SAAS,qBAAqB;oBAChC,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,QAAQ,mBAAmB;gBAE/B;gBACA,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,8CAA8C;gBAC9C,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;QACA,QAAQ;+CAAE,OAAO;gBACf,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;gBACpE,MAAM,sBAAsB,YAAY,YAAY,CAClD,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAGzB,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG;uDACvB,CAAC,MAAM,EAAE;wBACP,MAAM,SAAS,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI;wBACzC,MAAM,MAAM,IAAI,OAAO,WAAW;wBAElC,MAAM,0BACJ,eAAe,oBAAoB,GAC/B;4BACE,IAAI,CAAC,sBAAsB,EAAE,KAAK,GAAG,IAAI;4BACzC,GAAG,eAAe,oBAAoB;wBACxC,IACA;wBAEN,MAAM,4BACJ,eAAe,sBAAsB,GACjC;4BACE,IAAI,CAAC,sBAAsB,EAAE,KAAK,GAAG,KAAK,GAAG;4BAC7C,GAAG,eAAe,sBAAsB;wBAC1C,IACA;wBAEN,MAAM,sBACJ,eAAe,SAAS,EAAE;+DAAI,CAAC,GAAG,QAAU,CAAC;oCAC3C,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,EAAE,OAAO;oCAC5C,MAAM,EAAE,IAAI;oCACZ,OAAO,EAAE,KAAK,IAAI;oCAClB,OAAO,EAAE,KAAK;gCAChB,CAAC;iEAAM,EAAE;wBAEX,MAAM,uBAAmC;4BACvC,eAAe,2BAA2B;4BAC1C,WAAW;4BACX,WAAW;4BACX,iBAAiB,6BAA6B;4BAC9C,SACE,eAAe,OAAO,EAAE;mEAAI,CAAA,IAAK,CAAC;wCAChC,WAAW;wCACX,WAAW;wCACX,cAAc;wCACd,YAAY,EAAE,UAAU;wCACxB,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,EAAE,EAAE,UAAU,EAAE;wCACjD,OAAO,EAAE,KAAK,IAAI;wCAClB,WAAW;oCACb,CAAC;qEAAM,EAAE;4BACX,cAAc,eAAe,YAAY;4BACzC,YAAY,eAAe,UAAU;4BACrC,SACE,eAAe,OAAO,EAAE;mEAAI,CAAA,IAAK,CAAC;wCAChC,WAAW;wCACX,WAAW;wCACX,cAAc;wCACd,YAAY,EAAE,UAAU;wCACxB,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,EAAE,EAAE,UAAU,EAAE;wCACjD,OAAO,EAAE,KAAK,IAAI;wCAClB,WAAW;oCACb,CAAC;qEAAM,EAAE;4BACX,WAAW,eAAe,SAAS;4BACnC,IAAI;4BACJ,UAAU,eAAe,QAAQ,IAAI;4BACrC,gBAAgB,eAAe,cAAc,IAAI;4BACjD,cAAc,eAAe,YAAY,IAAI;4BAC7C,UAAU,eAAe,QAAQ;4BACjC,OAAO,eAAe,KAAK,IAAI;4BAC/B,QAAQ,eAAe,MAAM,IAAI;4BACjC,eAAe,EAAE;4BACjB,WAAW;4BACX,UACE,eAAe,QAAQ,EAAE;mEAAI,CAAA,IAAK,CAAC;wCACjC,cAAc,EAAE,YAAY;wCAC5B,WAAW;wCACX,WAAW;wCACX,cAAc;wCACd,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE;wCACjD,OAAO,EAAE,KAAK,IAAI;wCAClB,YAAY,EAAE,UAAU,IAAI;wCAC5B,WAAW;wCACX,WAAW,EAAE,SAAS;oCACxB,CAAC;qEAAM,EAAE;wBACb;wBACA,OAAO;+BAAI;4BAAK;yBAAqB;oBACvC;;gBAEF,OAAO;oBAAE;gBAAoB;YAC/B;;QACA,SAAS;+CAAE;gBACT,4DAA4D;gBAC5D,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IAvHa;;QACS,yLAAA,CAAA,iBAAc;QAK3B,iLAAA,CAAA,cAAW;;;AAmHb,MAAM,sBAAsB;;IACjC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAMjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAKf;QACA,UAAU;+CAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC7B,iFAAiF;gBACjF,OAAO,MAAM,2IAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,IAAI;YAC/C;;QACA,OAAO;+CAAE,CAAC,KAAK,WAAW;gBACxB,IAAI,SAAS,oBAAoB;oBAC/B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,GACvC,QAAQ,kBAAkB;gBAE9B;gBACA,IAAI,SAAS,yBAAyB;oBACpC,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,QAAQ,uBAAuB;gBAEnC;gBACA,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,8CAA8C;gBAC9C,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;QACA,QAAQ;+CAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC3B,kCAAkC;gBAClC,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;gBACpE,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBACvC;gBAEA,MAAM,qBAAqB,YAAY,YAAY,CACjD,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBAE7B,MAAM,0BAA0B,YAAY,YAAY,CACtD,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAGzB,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;uDAC3B,CAAA;wBACE,IAAI,CAAC,KAAK;wBACV,MAAM,MAAM,IAAI,OAAO,WAAW;wBAElC,2EAA2E;wBAC3E,MAAM,oBAAgC;4BACpC,GAAG,GAAG;4BACN,gCAAgC;4BAChC,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,oBAAoB,KAAK,OAC1B,KAAK,sCAAsC;+BAC3C,KAAK,oBAAoB,KAAK,YAC5B,IAAI,aAAa,GACjB;gCACE,SACE,KAAK,oBAAoB,CAAC,OAAO,IACjC,IAAI,aAAa,EAAE,WACnB;gCACF,UACE,KAAK,oBAAoB,CAAC,QAAQ,IAClC,IAAI,aAAa,EAAE,YACnB;gCACF,cACE,KAAK,oBAAoB,CAAC,YAAY,IACtC,IAAI,aAAa,EAAE,gBACnB;gCACF,IACE,IAAI,aAAa,EAAE,MAAM,CAAC,eAAe,EAAE,KAAK,GAAG,IAAI;gCACzD,OACE,KAAK,oBAAoB,CAAC,KAAK,IAC/B,IAAI,aAAa,EAAE,SACnB;gCACF,UACE,KAAK,oBAAoB,CAAC,QAAQ,IAClC,IAAI,aAAa,EAAE,YACnB;4BACJ,EAAE,mCAAmC;;4BAE7C,iBAAiB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,sBAAsB,KAAK,OAC5B,KAAK,sCAAsC;+BAC3C,KAAK,sBAAsB,KAAK,YAC9B,IAAI,eAAe,GACnB;gCACE,SACE,KAAK,sBAAsB,CAAC,OAAO,IACnC,IAAI,eAAe,EAAE,WACrB;gCACF,UACE,KAAK,sBAAsB,CAAC,QAAQ,IACpC,IAAI,eAAe,EAAE,YACrB;gCACF,cACE,KAAK,sBAAsB,CAAC,YAAY,IACxC,IAAI,eAAe,EAAE,gBACrB;gCACF,IACE,IAAI,eAAe,EAAE,MACrB,CAAC,eAAe,EAAE,KAAK,GAAG,IAAI;gCAChC,OACE,KAAK,sBAAsB,CAAC,KAAK,IACjC,IAAI,eAAe,EAAE,SACrB;gCACF,UACE,KAAK,sBAAsB,CAAC,QAAQ,IACpC,IAAI,eAAe,EAAE,YACrB;4BACJ,EAAE,mCAAmC;;4BAE7C,cAAc,KAAK,YAAY,IAAI,IAAI,YAAY;4BACnD,YAAY,KAAK,UAAU,IAAI,IAAI,UAAU;4BAC7C,mDAAmD;4BACnD,WAAW,KAAK,SAAS,IAAI,IAAI,SAAS;4BAC1C,UAAU,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,QAAQ;4BACvD,gBAAgB,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC5B,KAAK,cAAc,IAAI,IAAI,cAAc;4BAE3C,cAAc,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,IAAI,IAAI,YAAY;4BAEvC,UAAU,KAAK,QAAQ,IAAI,IAAI,QAAQ;4BACvC,OAAO,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK;4BAC9C,QAAQ,AAAC,KAAK,MAAM,IAA+B,IAAI,MAAM;4BAC7D,WAAW;wBAGb;wBACA,OAAO;oBACT;;gBAGF,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG;uDACvB,CAAC,UAAU,EAAE,GACX,QAAQ,GAAG;+DAAC,CAAA,aACV,WAAW,EAAE,KAAK,KACd,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,QACxB,aACL;;;gBAIV,OAAO;oBAAE;oBAAoB;gBAAwB;YACvD;;QACA,SAAS;+CAAE,CAAC,OAAO,QAAQ;gBACzB,wCAAwC;gBACxC,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IAtKa;;QACS,yLAAA,CAAA,iBAAc;QAM3B,iLAAA,CAAA,cAAW;;;AAiKb,MAAM,4BAA4B;;IACvC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAKf;QACA,UAAU;qDAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE;gBACnD,MAAM,WAAW,MAAM,2IAAA,CAAA,uBAAoB,CAAC,YAAY,CACtD,IACA,QACA;gBAEF,OAAO;YACT;;QACA,OAAO;qDAAE,CAAC,KAAK,WAAW;gBACxB,IAAI,SAAS,oBAAoB;oBAC/B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,GACvC,QAAQ,kBAAkB;gBAE9B;gBACA,QAAQ,KAAK,CAAC,uCAAuC;YACvD;;QACA,QAAQ;qDAAE,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE;gBAC7B,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBACvC;gBACA,MAAM,qBAAqB,YAAY,YAAY,CACjD,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBAE7B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;6DAC3B,CAAA,MAAQ,MAAM;4BAAE,GAAG,GAAG;4BAAE,QAAQ;wBAAO,IAAI;;gBAE7C,OAAO;oBAAE;gBAAmB;YAC9B;;QACA,SAAS;qDAAE,CAAC,OAAO,QAAQ;gBACzB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IAjDa;;QACS,yLAAA,CAAA,iBAAc;QAK3B,iLAAA,CAAA,cAAW;;;AA6Cb,MAAM,mCAAmC;;IAC9C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAKf;QACA,UAAU;4DAAE,OAAO,EAAE,aAAa,EAAE,EAAE,EAAE;gBACtC,oGAAoG;gBACpG,wGAAwG;gBACxG,MAAM,WAAW,MAAM,2IAAA,CAAA,uBAAoB,CAAC,mBAAmB,CAC7D,IACA;gBAEF,OAAO;YACT;;QACA,OAAO;4DAAE,CAAC,KAAK,WAAW;gBACxB,IAAI,SAAS,oBAAoB;oBAC/B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,GACvC,QAAQ,kBAAkB;gBAE9B;gBACA,QAAQ,KAAK,CAAC,+CAA+C;YAC/D;;QACA,QAAQ;4DAAE,OAAO,EAAE,aAAa,EAAE,EAAE,EAAE;gBACpC,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBACvC;gBACA,MAAM,qBAAqB,YAAY,YAAY,CACjD,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBAE7B,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;oEAC3B,CAAA;wBACE,IAAI,CAAC,KAAK;wBACV,+DAA+D;wBAC/D,0EAA0E;wBAC1E,OAAO;4BAAE,GAAG,GAAG;4BAAE,eAAe;wBAAc;oBAChD;;gBAEF,OAAO;oBAAE;gBAAmB;YAC9B;;QACA,SAAS;4DAAE,CAAC,OAAO,QAAQ;gBACzB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;gBACnD;gBACA,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IAvDa;;QACS,yLAAA,CAAA,iBAAc;QAK3B,iLAAA,CAAA,cAAW;;;AAmDb,MAAM,sBAAsB;;IACjC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAwC;QACvD,UAAU;+CAAE,OAAO;gBACjB,MAAM,2IAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC;gBAClC,OAAO;YACT;;QACA,OAAO;+CAAE,CAAC,KAAK,KAAK;gBAClB,IAAI,SAAS,yBAAyB;oBACpC,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,QAAQ,uBAAuB;gBAEnC;gBACA,QAAQ,KAAK,CAAC,gCAAgC;YAChD;;QACA,QAAQ;+CAAE,OAAM;gBACd,MAAM,YAAY,aAAa,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;gBACpE,MAAM,YAAY,aAAa,CAAC;oBAC9B,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBACvC;gBAEA,MAAM,0BAA0B,YAAY,YAAY,CACtD,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAGzB,YAAY,YAAY,CACtB,uJAAA,CAAA,sBAAmB,CAAC,GAAG;uDACvB,CAAC,MAAM,EAAE,GAAK,IAAI,MAAM;+DAAC,CAAA,aAAc,WAAW,EAAE,KAAK;;;gBAG3D,YAAY,aAAa,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;gBAAI;gBAErE,OAAO;oBAAE;gBAAwB;YACnC;;QACA,SAAS;+CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,uJAAA,CAAA,sBAAmB,CAAC,GAAG;gBAAC;YACpE;;IACF;AACF;IA3Ca;;QACS,yLAAA,CAAA,iBAAc;QAK3B,iLAAA,CAAA,cAAW", "debugId": null}}]}