{"version": 4, "routes": {"/favicon.ico": {"initialHeaders": {"cache-control": "public, max-age=0, must-revalidate", "content-type": "image/x-icon", "x-next-cache-tags": "_N_T_/layout,_N_T_/favicon.ico/layout,_N_T_/favicon.ico/route,_N_T_/favicon.ico"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/favicon.ico", "dataRoute": null, "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "fb7185c6b17b3dbc7f5b846dd8e6aef3", "previewModeSigningKey": "69978816801060d73911c0bc1a49f8c82d7ca0fc80e120b76499b369012bad68", "previewModeEncryptionKey": "4926a647526a8763c292faa47dc2c17d95e8d0b07ae76fdaef45a404014e43ce"}}