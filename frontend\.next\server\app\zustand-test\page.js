(()=>{var e={};e.id=3217,e.ids=[3217],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11012:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]])},11997:e=>{"use strict";e.exports=require("punycode")},12662:(e,s,r)=>{"use strict";r.d(s,{AppBreadcrumb:()=>m});var t=r(60687),a=r(85814),i=r.n(a),n=r(16189),l=r(43210),c=r.n(l),d=r(70640),o=r(22482);function m({className:e,homeHref:s="/",homeLabel:r="Dashboard",showContainer:a=!0}){let l=(0,n.usePathname)(),m=l?l.split("/").filter(Boolean):[],x=e=>{if(/^\d+$/.test(e))return`ID: ${e}`;if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let s={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return s[e]?s[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},u=m.map((e,s)=>{let r="/"+m.slice(0,s+1).join("/"),a=s===m.length-1,n=x(e);return(0,t.jsxs)(c().Fragment,{children:[(0,t.jsx)(d.BreadcrumbItem,{children:a?(0,t.jsx)(d.BreadcrumbPage,{className:"font-medium text-foreground",children:n}):(0,t.jsx)(d.BreadcrumbLink,{asChild:!0,children:(0,t.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:r,children:n})})}),!a&&(0,t.jsx)(d.BreadcrumbSeparator,{})]},r)}),h=(0,t.jsx)(d.Breadcrumb,{className:(0,o.cn)("text-sm",e),children:(0,t.jsxs)(d.BreadcrumbList,{className:"flex-wrap",children:[(0,t.jsx)(d.BreadcrumbItem,{children:(0,t.jsx)(d.BreadcrumbLink,{asChild:!0,children:(0,t.jsx)(i(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:s,children:r})})}),m.length>0&&(0,t.jsx)(d.BreadcrumbSeparator,{}),u]})});return a?(0,t.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,t.jsx)("div",{className:"flex items-center",children:h})}):h}},15809:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var t=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(s,c);let d={children:["",{children:["zustand-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,56985)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\zustand-test\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,34595)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\zustand-test\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/zustand-test/page",pathname:"/zustand-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29091:(e,s,r)=>{Promise.resolve().then(r.bind(r,47079))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},47079:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>R});var t=r(60687);let a=(0,r(82614).A)("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5c-1.4 0-2.5-1.1-2.5-2.5V2",key:"125lnx"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]);var i=r(3662),n=r(72963),l=r(99196),c=r(10218),d=r(43210),o=r.n(d),m=r(11012),x=r(29848),u=r(10453),h=r(5600),p=r(96834),j=r(29523),f=r(44493),g=r(41181);let N=()=>{let{resolvedTheme:e,setTheme:s,systemTheme:r,theme:a}=(0,c.D)(),{currentTheme:i,isDark:n,setTheme:l}=(0,g.useTheme)(),[d,N]=o().useState(!1);return(o().useEffect(()=>{N(!0)},[]),d)?(0,t.jsxs)(f.Zp,{className:"w-full max-w-2xl",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"size-5"}),"Theme Debug Information"]})}),(0,t.jsxs)(f.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Next-themes State"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"theme:"}),(0,t.jsx)(p.E,{variant:"outline",children:a||"undefined"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"systemTheme:"}),(0,t.jsx)(p.E,{variant:"outline",children:r||"undefined"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"resolvedTheme:"}),(0,t.jsx)(p.E,{variant:"outline",children:e||"undefined"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Zustand State"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"currentTheme:"}),(0,t.jsx)(p.E,{variant:"outline",children:i})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"isDark:"}),(0,t.jsx)(p.E,{variant:"outline",children:n?"true":"false"})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Visual State"}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"size-4 rounded border bg-background"}),(0,t.jsx)("span",{className:"text-sm",children:"Background"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"size-4 rounded bg-foreground"}),(0,t.jsx)("span",{className:"text-sm",children:"Foreground"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"size-4 rounded bg-primary"}),(0,t.jsx)("span",{className:"text-sm",children:"Primary"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"size-4 rounded bg-secondary"}),(0,t.jsx)("span",{className:"text-sm",children:"Secondary"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Test Controls"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium",children:"Next-themes Controls"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(j.$,{onClick:()=>s("light"),size:"sm",variant:"light"===a?"default":"outline",children:[(0,t.jsx)(x.A,{className:"mr-1 size-4"}),"Light"]}),(0,t.jsxs)(j.$,{onClick:()=>s("dark"),size:"sm",variant:"dark"===a?"default":"outline",children:[(0,t.jsx)(u.A,{className:"mr-1 size-4"}),"Dark"]}),(0,t.jsxs)(j.$,{onClick:()=>s("system"),size:"sm",variant:"system"===a?"default":"outline",children:[(0,t.jsx)(h.A,{className:"mr-1 size-4"}),"System"]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium",children:"Zustand Controls"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(j.$,{onClick:()=>l("light"),size:"sm",variant:"light"===i?"default":"outline",children:[(0,t.jsx)(x.A,{className:"mr-1 size-4"}),"Light"]}),(0,t.jsxs)(j.$,{onClick:()=>l("dark"),size:"sm",variant:"dark"===i?"default":"outline",children:[(0,t.jsx)(u.A,{className:"mr-1 size-4"}),"Dark"]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Synchronization Status"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Themes match:"}),(0,t.jsx)(p.E,{variant:e===i?"default":"destructive",children:e===i?"Yes":"No"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Expected sync:"}),(0,t.jsxs)("span",{className:"text-muted-foreground",children:["system"===a?`${r} (from system)`:a," ","→ ",i]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Troubleshooting"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[e!==i&&(0,t.jsx)("div",{className:"rounded border border-yellow-200 bg-yellow-50 p-2 dark:border-yellow-800 dark:bg-yellow-950",children:"⚠️ Themes are not synchronized. This may indicate a sync issue."}),!a&&(0,t.jsx)("div",{className:"rounded border border-red-200 bg-red-50 p-2 dark:border-red-800 dark:bg-red-950",children:"❌ Next-themes not initialized. Check ThemeProvider setup."}),e===i&&(0,t.jsx)("div",{className:"rounded border border-green-200 bg-green-50 p-2 dark:border-green-800 dark:bg-green-950",children:"✅ Themes are properly synchronized."})]})]})]})]}):(0,t.jsxs)(f.Zp,{className:"w-full max-w-2xl",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"size-5"}),"Theme Debug (Loading...)"]})}),(0,t.jsx)(f.Wu,{children:(0,t.jsx)("p",{children:"Loading theme information..."})})]})};var v=r(24343),y=r(78726),b=r(64181),k=r(58369),w=r(94538),C=r(51887);let z=()=>{let{currentTheme:e,setTheme:s}=(0,w.C)();return(0,t.jsxs)(f.Zp,{className:"w-full max-w-md",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:["light"===e?(0,t.jsx)(x.A,{className:"size-5"}):(0,t.jsx)(u.A,{className:"size-5"}),"Theme Toggle"]})}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{children:["Current theme: ",e]}),(0,t.jsxs)(j.$,{onClick:()=>s("light"===e?"dark":"light"),size:"sm",variant:"outline",children:["Switch to ","light"===e?"dark":"light"]})]})})]})},S=()=>{let{sidebarOpen:e,toggleSidebar:s}=(0,w.C)();return(0,t.jsxs)(f.Zp,{className:"w-full max-w-md",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"size-5"}),"Sidebar Control"]})}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("span",{children:["Sidebar is ",e?"open":"closed"]}),(0,t.jsxs)(j.$,{onClick:s,size:"sm",variant:"outline",children:[e?(0,t.jsx)(y.A,{className:"size-4"}):(0,t.jsx)(v.A,{className:"size-4"}),e?"Close":"Open"]})]})})]})},T=()=>{let{addNotification:e,clearAllNotifications:s,markNotificationAsRead:r,notifications:a,removeNotification:i,unreadNotificationCount:n}=(0,w.C)(),l=s=>{e({message:{error:"An error occurred. Please try again.",info:"This is an informational message",success:"Operation completed successfully!",warning:"Please review this warning"}[s],type:s})};return(0,t.jsxs)(f.Zp,{className:"w-full max-w-2xl",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(b.A,{className:"size-5"}),"Notification System",n()>0&&(0,t.jsx)(p.E,{variant:"destructive",children:n()})]})}),(0,t.jsxs)(f.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsx)(j.$,{onClick:()=>l("info"),size:"sm",variant:"outline",children:"Info"}),(0,t.jsx)(j.$,{onClick:()=>l("success"),size:"sm",variant:"outline",children:"Success"}),(0,t.jsx)(j.$,{onClick:()=>l("warning"),size:"sm",variant:"outline",children:"Warning"}),(0,t.jsx)(j.$,{onClick:()=>l("error"),size:"sm",variant:"outline",children:"Error"}),a.length>0&&(0,t.jsx)(j.$,{onClick:s,size:"sm",variant:"destructive",children:"Clear All"})]}),(0,t.jsx)("div",{className:"max-h-64 space-y-2 overflow-y-auto",children:0===a.length?(0,t.jsx)("p",{className:"py-4 text-center text-muted-foreground",children:"No notifications"}):a.map(e=>(0,t.jsx)("div",{className:`rounded border p-3 ${e.read?"bg-muted":"bg-background"} ${"error"===e.type?"border-red-200":"warning"===e.type?"border-yellow-200":"success"===e.type?"border-green-200":"border-blue-200"}`,children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p.E,{variant:"error"===e.type?"destructive":"warning"===e.type?"secondary":"success"===e.type?"default":"outline",children:e.type}),!e.read&&(0,t.jsx)(p.E,{variant:"outline",children:"New"})]}),(0,t.jsx)("p",{className:"mt-1 text-sm",children:e.message}),(0,t.jsx)("p",{className:"mt-1 text-xs text-muted-foreground",children:new Date(e.timestamp).toLocaleString()})]}),(0,t.jsxs)("div",{className:"ml-2 flex gap-1",children:[!e.read&&(0,t.jsx)(j.$,{onClick:()=>r(e.id),size:"sm",variant:"ghost",children:"Mark Read"}),(0,t.jsx)(j.$,{onClick:()=>i(e.id),size:"sm",variant:"ghost",children:"\xd7"})]})]})},e.id))})]})]})},A=()=>{let{fontSize:e,setFontSize:s}=(0,C.n)();return(0,t.jsxs)(f.Zp,{className:"w-full max-w-md",children:[(0,t.jsx)(f.aR,{children:(0,t.jsx)(f.ZB,{children:"Font Size Preference"})}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("label",{htmlFor:"font-size",children:"Font Size:"}),(0,t.jsxs)("select",{className:"rounded border px-2 py-1",id:"font-size",onChange:e=>s(e.target.value),value:e,children:[(0,t.jsx)("option",{value:"small",children:"Small"}),(0,t.jsx)("option",{value:"medium",children:"Medium"}),(0,t.jsx)("option",{value:"large",children:"Large"})]})]}),(0,t.jsxs)("div",{className:`rounded border p-3 ${"small"===e?"text-sm":"large"===e?"text-lg":"text-base"}`,children:["This text demonstrates the ",e," font size setting."]})]})})]})},B=()=>{let{closeModal:e,isModalOpen:s,modalContent:r,openModal:a}=(0,C.n)();return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(f.Zp,{className:"w-full max-w-md",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(k.A,{className:"size-5"}),"Modal System"]})}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsx)(j.$,{onClick:()=>a("login"),size:"sm",variant:"outline",children:"Login Modal"}),(0,t.jsx)(j.$,{onClick:()=>a("signup"),size:"sm",variant:"outline",children:"Signup Modal"}),(0,t.jsx)(j.$,{onClick:()=>a("settings"),size:"sm",variant:"outline",children:"Settings Modal"})]})})]}),s&&(0,t.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",onClick:e,children:(0,t.jsxs)("div",{className:"mx-4 w-full max-w-md rounded-lg bg-white p-6",onClick:e=>e.stopPropagation(),children:[(0,t.jsxs)("div",{className:"mb-4 flex items-center justify-between",children:[(0,t.jsxs)("h2",{className:"text-lg font-semibold capitalize",children:[r," Modal"]}),(0,t.jsx)(j.$,{onClick:e,size:"sm",variant:"ghost",children:"\xd7"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:["login"===r&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{children:"Login form would go here..."}),(0,t.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,t.jsx)("input",{className:"w-full rounded border px-3 py-2",placeholder:"Email",type:"email"}),(0,t.jsx)("input",{className:"w-full rounded border px-3 py-2",placeholder:"Password",type:"password"})]})]}),"signup"===r&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{children:"Signup form would go here..."}),(0,t.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,t.jsx)("input",{className:"w-full rounded border px-3 py-2",placeholder:"Full Name",type:"text"}),(0,t.jsx)("input",{className:"w-full rounded border px-3 py-2",placeholder:"Email",type:"email"}),(0,t.jsx)("input",{className:"w-full rounded border px-3 py-2",placeholder:"Password",type:"password"})]})]}),"settings"===r&&(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{children:"Settings form would go here..."}),(0,t.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,t.jsxs)("label",{className:"flex items-center gap-2",children:[(0,t.jsx)("input",{type:"checkbox"}),"Enable notifications"]}),(0,t.jsxs)("label",{className:"flex items-center gap-2",children:[(0,t.jsx)("input",{type:"checkbox"}),"Dark mode"]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,t.jsx)(j.$,{className:"flex-1",onClick:e,variant:"outline",children:"Cancel"}),(0,t.jsx)(j.$,{className:"flex-1",children:"login"===r?"Login":"signup"===r?"Sign Up":"Save"})]})]})]})})]})},$=()=>(0,t.jsxs)("div",{className:"space-y-6 p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Zustand Stores Showcase"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,t.jsx)(z,{}),(0,t.jsx)(S,{}),(0,t.jsx)(A,{}),(0,t.jsx)(B,{})]}),(0,t.jsx)(T,{})]});var P=r(12662),Z=r(87676);let E=()=>{let{currentTheme:e,setTheme:s}=(0,g.useTheme)(),{setTheme:r,theme:a}=(0,c.D)(),{showInfo:i,showSuccess:n}=(0,Z.useNotifications)();return(0,t.jsxs)(f.Zp,{className:"mb-6",children:[(0,t.jsx)(f.aR,{children:(0,t.jsx)(f.ZB,{className:"flex items-center gap-2",children:"\uD83C\uDFA8 Theme Integration Test"})}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"rounded border p-3",children:[(0,t.jsx)("h4",{className:"font-medium",children:"Zustand Theme"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Current: ",e]})]}),(0,t.jsxs)("div",{className:"rounded border p-3",children:[(0,t.jsx)("h4",{className:"font-medium",children:"Next-themes"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Current: ",a]})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(j.$,{onClick:()=>{i("Testing theme synchronization between Next-themes and Zustand..."),setTimeout(()=>{s("dark"),i("Set Zustand theme to dark")},1e3),setTimeout(()=>{s("light"),i("Set Zustand theme to light")},2e3),setTimeout(()=>{r("dark"),i("Set Next-themes to dark")},3e3),setTimeout(()=>{n("Theme synchronization test completed!")},4e3)},variant:"outline",children:"Test Theme Sync"}),(0,t.jsx)(j.$,{onClick:()=>s("dark"),size:"sm",variant:"outline",children:"Zustand Dark"}),(0,t.jsx)(j.$,{onClick:()=>s("light"),size:"sm",variant:"outline",children:"Zustand Light"}),(0,t.jsx)(j.$,{onClick:()=>r("system"),size:"sm",variant:"outline",children:"System Theme"})]}),(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:"Both theme values should stay synchronized. Test by clicking buttons and checking values above."})]})})]})},M=()=>{let{currentTheme:e}=(0,w.C)(),{fontSize:s,notificationsEnabled:r}=(0,C.n)(),{unreadCount:l}=(0,Z.useNotifications)(),c=[{description:`Current theme: ${e}`,name:"Theme Persistence",status:e?"pass":"fail"},{description:`Font size: ${s}`,name:"Font Size Setting",status:s?"pass":"fail"},{description:`Unread notifications: ${l()}`,name:"Notification System",status:"function"==typeof l?"pass":"fail"},{description:`Notifications enabled: ${r}`,name:"UI Preferences",status:"boolean"==typeof r?"pass":"fail"}];return(0,t.jsxs)(f.Zp,{className:"mb-6",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(a,{className:"size-5"}),"Zustand Store Integration Tests"]})}),(0,t.jsx)(f.Wu,{children:(0,t.jsx)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:c.map((e,s)=>(0,t.jsx)("div",{className:"flex items-center justify-between rounded border p-3",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsxs)(p.E,{variant:"pass"===e.status?"default":"destructive",children:["pass"===e.status?(0,t.jsx)(i.A,{className:"mr-1 size-3"}):(0,t.jsx)(n.A,{className:"mr-1 size-3"}),e.status]})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]})},s))})})]})},q=()=>{let{showInfo:e,showSuccess:s}=(0,Z.useNotifications)();return(0,t.jsxs)(f.Zp,{className:"mb-6",children:[(0,t.jsx)(f.aR,{children:(0,t.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(l.A,{className:"size-5"}),"Persistence Testing"]})}),(0,t.jsx)(f.Wu,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Test that your theme and UI preferences persist across browser sessions."}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(j.$,{onClick:()=>{e("Refresh the page to test persistence. Theme and font size should be maintained.")},variant:"outline",children:"Test Persistence"}),(0,t.jsx)(j.$,{onClick:()=>{s("Persistence test passed! Your preferences were maintained after refresh.")},variant:"default",children:"Confirm Persistence Works"})]})]})})]})};function R(){return(0,t.jsxs)("div",{className:"container mx-auto py-8",children:[(0,t.jsx)(P.AppBreadcrumb,{}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"mb-2 text-3xl font-bold",children:"Zustand Store Testing"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"This page demonstrates and tests all Zustand store functionality in WorkHub. Use this page to verify that stores are working correctly and persistence is functioning."})]}),(0,t.jsx)(N,{}),(0,t.jsx)(E,{}),(0,t.jsx)(M,{}),(0,t.jsx)(q,{}),(0,t.jsx)($,{})]})}},53067:(e,s,r)=>{Promise.resolve().then(r.bind(r,56985))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56985:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\zustand-test\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\zustand-test\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69795:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(82614).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},70440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70640:(e,s,r)=>{"use strict";r.d(s,{Breadcrumb:()=>c,BreadcrumbItem:()=>o,BreadcrumbLink:()=>m,BreadcrumbList:()=>d,BreadcrumbPage:()=>x,BreadcrumbSeparator:()=>u});var t=r(60687),a=r(8730),i=r(74158),n=(r(69795),r(43210)),l=r(22482);let c=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("nav",{"aria-label":"breadcrumb",className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",e),ref:r,...s}));c.displayName="Breadcrumb";let d=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("ol",{className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",e),ref:r,...s}));d.displayName="BreadcrumbList";let o=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("li",{className:(0,l.cn)("inline-flex items-center gap-1.5",e),ref:r,...s}));o.displayName="BreadcrumbItem";let m=n.forwardRef(({asChild:e,className:s,...r},i)=>{let n=e?a.DX:"a";return(0,t.jsx)(n,{className:(0,l.cn)("transition-colors hover:text-foreground",s),ref:i,...r})});m.displayName="BreadcrumbLink";let x=n.forwardRef(({className:e,...s},r)=>(0,t.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,l.cn)("font-normal text-foreground",e),ref:r,role:"link",...s}));x.displayName="BreadcrumbPage";let u=({children:e,className:s,...r})=>(0,t.jsx)("span",{"aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",s),role:"presentation",...r,children:e??(0,t.jsx)(i.A,{className:"size-4"})});u.displayName="BreadcrumbSeparator"},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},87676:(e,s,r)=>{"use strict";r.r(s),r.d(s,{useNotifications:()=>i,useWorkHubNotifications:()=>n});var t=r(43210),a=r(94538);let i=()=>{let e=(0,a.C)(e=>e.addNotification),s=(0,a.C)(e=>e.removeNotification),r=(0,a.C)(e=>e.clearAllNotifications),i=(0,a.C)(e=>e.unreadNotificationCount),n=(0,t.useCallback)(s=>{e({message:s,type:"success"})},[e]),l=(0,t.useCallback)(s=>{e({message:s,type:"error"})},[e]),c=(0,t.useCallback)(s=>{e({message:s,type:"warning"})},[e]),d=(0,t.useCallback)(s=>{e({message:s,type:"info"})},[e]),o=(0,t.useCallback)((e,s,r)=>{e?n(s):l(r)},[n,l]),m=(0,t.useCallback)((r,t,i=5e3)=>{e({message:t,type:r}),setTimeout(()=>{let e=a.C.getState().notifications.at(-1);e&&e.message===t&&s(e.id)},i)},[e,s]),x=(0,t.useCallback)((s="Loading...")=>{e({message:s,type:"info"});let r=a.C.getState().notifications;return r.at(-1)?.id},[e]),u=(0,t.useCallback)((e,r,t)=>{s(e),r?n(t):l(t)},[s,n,l]);return{clearAllNotifications:r,removeNotification:s,showApiResult:o,showError:l,showInfo:d,showLoading:x,showSuccess:n,showTemporary:m,showWarning:c,unreadCount:i,updateLoadingNotification:u}},n=()=>{let{clearAllNotifications:e,removeNotification:s,showError:r,showInfo:n,showSuccess:l,showWarning:c,unreadCount:d}=i(),o=(0,t.useCallback)((e,s)=>{(0,a.C.getState().addNotification)({...s&&{actionUrl:s},category:"delegation",message:e,type:"delegation-update"})},[]),m=(0,t.useCallback)((e,s)=>{(0,a.C.getState().addNotification)({...s&&{actionUrl:s},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),x=(0,t.useCallback)((e,s)=>{(0,a.C.getState().addNotification)({...s&&{actionUrl:s},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:s,showDelegationUpdate:o,showEmployeeUpdate:(0,t.useCallback)((e,s)=>{(0,a.C.getState().addNotification)({...s&&{actionUrl:s},category:"employee",message:e,type:"employee-update"})},[]),showError:r,showInfo:n,showSuccess:l,showTaskAssigned:x,showVehicleMaintenance:m,showWarning:c,unreadCount:d}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,211,1658,5941],()=>r(15809));module.exports=t})();