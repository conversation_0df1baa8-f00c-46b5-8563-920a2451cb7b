{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__752a1ada._.js", "server/edge/chunks/edge-wrapper_14765a8d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "TnfBoJPKWQYH1eM+KpPYnyt/wa230zuC+Vf1AA2T1lg=", "__NEXT_PREVIEW_MODE_ID": "f171785a870c4da83a2c7a6a933588b9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4aff67c06d2e7dc9c3abb989db7ddecab57d5ec175470abb20d3671f41fc19b1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fbcf37fb110a6c485052787c6f11ff9cc582ddf76872fbd2c9bee19778585a87"}}}, "sortedMiddleware": ["/"], "functions": {}}