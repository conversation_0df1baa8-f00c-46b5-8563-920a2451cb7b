{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/DelegationStatusWidget.tsx"], "sourcesContent": ["/**\r\n * @file Delegation Status Widget - UX Enhanced with Clear Legend\r\n * @description Professional pie chart with prominent legend for better usability\r\n *\r\n * UX Improvements Applied:\r\n * - Enhanced legend positioning and styling for clarity\r\n * - Better color contrast and accessibility\r\n * - Professional spacing and typography\r\n * - Clear status mapping with percentages\r\n */\r\n\r\n'use client';\r\n\r\nimport React from 'react';\r\nimport {\r\n  PieChart,\r\n  Pie,\r\n  Cell,\r\n  Tooltip,\r\n  Legend,\r\n  ResponsiveContainer,\r\n} from 'recharts';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { useDelegationAnalyticsQuery } from '../../data/hooks';\r\nimport { useReportingFilters } from '../../data/stores';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { BarChart3, CheckCircle } from 'lucide-react';\r\n\r\n// Enhanced custom legend component for better visibility\r\nconst CustomLegend = ({ payload }: any) => {\r\n  if (!payload || payload.length === 0) return null;\r\n\r\n  return (\r\n    <div className=\"flex flex-wrap justify-center gap-4 mt-6 pt-4 border-t border-border\">\r\n      {payload.map((entry: any, index: number) => (\r\n        <div\r\n          key={`legend-${index}`}\r\n          className=\"flex items-center gap-2 text-sm\"\r\n        >\r\n          <div\r\n            className=\"w-4 h-4 rounded-full border border-gray-300\"\r\n            style={{ backgroundColor: entry.color }}\r\n          />\r\n          <span className=\"font-medium text-gray-700 dark:text-gray-300\">\r\n            {entry.value}\r\n          </span>\r\n          {entry.payload && (\r\n            <span className=\"text-muted-foreground\">\r\n              ({entry.payload.count})\r\n            </span>\r\n          )}\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\n// Enhanced tooltip with percentage information\r\nconst CustomTooltip = ({ active, payload }: any) => {\r\n  if (active && payload && payload.length) {\r\n    const data = payload[0];\r\n    const total = payload[0].payload.total || 100;\r\n    const percentage = total > 0 ? Math.round((data.value / total) * 100) : 0;\r\n\r\n    return (\r\n      <div className=\"bg-white dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg\">\r\n        <p className=\"font-semibold text-gray-900 dark:text-gray-100 mb-2\">\r\n          {data.name}\r\n        </p>\r\n        <div className=\"space-y-1\">\r\n          <div className=\"flex justify-between items-center gap-4\">\r\n            <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Count:\r\n            </span>\r\n            <span className=\"font-bold text-lg\" style={{ color: data.color }}>\r\n              {data.value}\r\n            </span>\r\n          </div>\r\n          <div className=\"flex justify-between items-center gap-4\">\r\n            <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Percentage:\r\n            </span>\r\n            <span className=\"font-medium text-primary\">{percentage}%</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const DelegationStatusWidget = () => {\r\n  const filters = useReportingFilters();\r\n  const { data, isLoading, error } = useDelegationAnalyticsQuery(filters);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <Card>\r\n        <CardHeader>\r\n          <Skeleton className=\"h-7 w-48\" />\r\n        </CardHeader>\r\n        <CardContent>\r\n          <Skeleton className=\"h-[400px] w-full rounded-lg\" />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-3\">\r\n            <BarChart3 className=\"h-6 w-6\" />\r\n            Delegation Status\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <Alert variant=\"destructive\">\r\n            <AlertTitle>Error</AlertTitle>\r\n            <AlertDescription>{error.message}</AlertDescription>\r\n          </Alert>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  const chartData = data?.statusDistribution ?? [];\r\n\r\n  // Calculate total for percentage calculations\r\n  const total = chartData.reduce((sum, item) => sum + item.count, 0);\r\n  const enrichedData = chartData.map(item => ({\r\n    ...item,\r\n    total,\r\n  }));\r\n\r\n  // Default colors if not provided in data\r\n  const defaultColors = ['#22c55e', '#f59e0b', '#ef4444', '#3b82f6', '#8b5cf6'];\r\n  const dataWithColors = enrichedData.map((item, index) => ({\r\n    ...item,\r\n    color: item.color || defaultColors[index % defaultColors.length],\r\n  }));\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader className=\"pb-6\">\r\n        <CardTitle className=\"flex items-center gap-3 text-xl\">\r\n          <BarChart3 className=\"h-6 w-6 text-primary\" />\r\n          Delegation Status\r\n        </CardTitle>\r\n        <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\r\n          <CheckCircle className=\"h-4 w-4\" />\r\n          <span>Distribution of delegation statuses</span>\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent className=\"pt-6\">\r\n        <ResponsiveContainer width=\"100%\" height={300}>\r\n          <PieChart>\r\n            <Pie\r\n              data={dataWithColors}\r\n              cx=\"50%\"\r\n              cy=\"50%\"\r\n              labelLine={false}\r\n              outerRadius={100}\r\n              innerRadius={40} // Create a donut chart for better visual appeal\r\n              fill=\"#8884d8\"\r\n              dataKey=\"count\"\r\n              nameKey=\"status\"\r\n              stroke=\"#fff\"\r\n              strokeWidth={2}\r\n            >\r\n              {dataWithColors.map((entry, index) => (\r\n                <Cell key={`cell-${index}`} fill={entry.color} />\r\n              ))}\r\n            </Pie>\r\n            <Tooltip content={<CustomTooltip />} />\r\n          </PieChart>\r\n        </ResponsiveContainer>\r\n\r\n        {/* Clear, prominent legend */}\r\n        <CustomLegend\r\n          payload={dataWithColors.map(item => ({\r\n            value: item.status,\r\n            color: item.color,\r\n            payload: item,\r\n          }))}\r\n        />\r\n\r\n        {/* Summary statistics */}\r\n        {total > 0 && (\r\n          <div className=\"mt-6 pt-4 border-t border-border\">\r\n            <div className=\"flex items-center justify-between text-sm\">\r\n              <span className=\"text-muted-foreground\">Total Delegations:</span>\r\n              <span className=\"font-semibold text-primary\">{total}</span>\r\n            </div>\r\n            {dataWithColors.length > 0 && dataWithColors[0] && (\r\n              <div className=\"flex items-center justify-between text-sm mt-2\">\r\n                <span className=\"text-muted-foreground\">\r\n                  Most Common Status:\r\n                </span>\r\n                <span\r\n                  className=\"font-semibold\"\r\n                  style={{ color: dataWithColors[0].color }}\r\n                >\r\n                  {dataWithColors[0].status}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;;AAKD;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAhBA;;;;;;;;;AAkBA,yDAAyD;AACzD,MAAM,eAAe,CAAC,EAAE,OAAO,EAAO;IACpC,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG,OAAO;IAE7C,qBACE,8OAAC;QAAI,WAAU;kBACZ,QAAQ,GAAG,CAAC,CAAC,OAAY,sBACxB,8OAAC;gBAEC,WAAU;;kCAEV,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB,MAAM,KAAK;wBAAC;;;;;;kCAExC,8OAAC;wBAAK,WAAU;kCACb,MAAM,KAAK;;;;;;oBAEb,MAAM,OAAO,kBACZ,8OAAC;wBAAK,WAAU;;4BAAwB;4BACpC,MAAM,OAAO,CAAC,KAAK;4BAAC;;;;;;;;eAZrB,CAAC,OAAO,EAAE,OAAO;;;;;;;;;;AAmBhC;AAEA,+CAA+C;AAC/C,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAO;IAC7C,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;QACvC,MAAM,OAAO,OAAO,CAAC,EAAE;QACvB,MAAM,QAAQ,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,IAAI;QAC1C,MAAM,aAAa,QAAQ,IAAI,KAAK,KAAK,CAAC,AAAC,KAAK,KAAK,GAAG,QAAS,OAAO;QAExE,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BACV,KAAK,IAAI;;;;;;8BAEZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;8CAG3D,8OAAC;oCAAK,WAAU;oCAAoB,OAAO;wCAAE,OAAO,KAAK,KAAK;oCAAC;8CAC5D,KAAK,KAAK;;;;;;;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;8CAG3D,8OAAC;oCAAK,WAAU;;wCAA4B;wCAAW;;;;;;;;;;;;;;;;;;;;;;;;;IAKjE;IACA,OAAO;AACT;AAEO,MAAM,yBAAyB;IACpC,MAAM,UAAU,CAAA,GAAA,wLAAA,CAAA,sBAAmB,AAAD;IAClC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2PAAA,CAAA,8BAA2B,AAAD,EAAE;IAE/D,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8BACH,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;8BAEtB,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;IAI5B;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8BACH,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,kNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIrC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,8OAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,iIAAA,CAAA,mBAAgB;0CAAE,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;IAK1C;IAEA,MAAM,YAAY,MAAM,sBAAsB,EAAE;IAEhD,8CAA8C;IAC9C,MAAM,QAAQ,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IAChE,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,OAAQ,CAAC;YAC1C,GAAG,IAAI;YACP;QACF,CAAC;IAED,yCAAyC;IACzC,MAAM,gBAAgB;QAAC;QAAW;QAAW;QAAW;QAAW;KAAU;IAC7E,MAAM,iBAAiB,aAAa,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;YACxD,GAAG,IAAI;YACP,OAAO,KAAK,KAAK,IAAI,aAAa,CAAC,QAAQ,cAAc,MAAM,CAAC;QAClE,CAAC;IAED,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,kNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAyB;;;;;;;kCAGhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAGV,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC,mKAAA,CAAA,sBAAmB;wBAAC,OAAM;wBAAO,QAAQ;kCACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;8CACP,8OAAC,+IAAA,CAAA,MAAG;oCACF,MAAM;oCACN,IAAG;oCACH,IAAG;oCACH,WAAW;oCACX,aAAa;oCACb,aAAa;oCACb,MAAK;oCACL,SAAQ;oCACR,SAAQ;oCACR,QAAO;oCACP,aAAa;8CAEZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC,oJAAA,CAAA,OAAI;4CAAuB,MAAM,MAAM,KAAK;2CAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;8CAG9B,8OAAC,uJAAA,CAAA,UAAO;oCAAC,uBAAS,8OAAC;;;;;;;;;;;;;;;;;;;;;kCAKvB,8OAAC;wBACC,SAAS,eAAe,GAAG,CAAC,CAAA,OAAQ,CAAC;gCACnC,OAAO,KAAK,MAAM;gCAClB,OAAO,KAAK,KAAK;gCACjB,SAAS;4BACX,CAAC;;;;;;oBAIF,QAAQ,mBACP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;kDAA8B;;;;;;;;;;;;4BAE/C,eAAe,MAAM,GAAG,KAAK,cAAc,CAAC,EAAE,kBAC7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDAGxC,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,cAAc,CAAC,EAAE,CAAC,KAAK;wCAAC;kDAEvC,cAAc,CAAC,EAAE,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C", "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/DelegationTrendWidget.tsx"], "sourcesContent": ["/**\r\n * @file Delegation Trend Widget\r\n * @description A widget that displays a line chart of delegation trends over time.\r\n */\r\n\r\n'use client';\r\n\r\nimport React from 'react';\r\nimport {\r\n  LineChart,\r\n  Line,\r\n  XAxis,\r\n  YAxis,\r\n  CartesianGrid,\r\n  Tooltip,\r\n  Legend,\r\n  ResponsiveContainer,\r\n} from 'recharts';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { useTrendDataQuery } from '../../data/hooks';\r\nimport { useReportingFilters } from '../../data/stores';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { TrendingUp } from 'lucide-react';\r\n\r\nexport const DelegationTrendWidget = () => {\r\n  const filters = useReportingFilters();\r\n  const { data, isLoading, error } = useTrendDataQuery(filters);\r\n\r\n  if (isLoading) {\r\n    return <Skeleton className=\"h-[350px] w-full\" />;\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <Alert variant=\"destructive\">\r\n        <AlertTitle>Error</AlertTitle>\r\n        <AlertDescription>{error.message}</AlertDescription>\r\n      </Alert>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center gap-2\">\r\n          <TrendingUp className=\"h-5 w-5\" />\r\n          Delegation Trends\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <ResponsiveContainer width=\"100%\" height={300}>\r\n          <LineChart data={data || []}>\r\n            <CartesianGrid strokeDasharray=\"3 3\" />\r\n            <XAxis dataKey=\"date\" />\r\n            <YAxis />\r\n            <Tooltip />\r\n            <Legend />\r\n            <Line\r\n              type=\"monotone\"\r\n              dataKey=\"created\"\r\n              stroke=\"#8884d8\"\r\n              name=\"Created\"\r\n            />\r\n            <Line\r\n              type=\"monotone\"\r\n              dataKey=\"completed\"\r\n              stroke=\"#82ca9d\"\r\n              name=\"Completed\"\r\n            />\r\n            <Line\r\n              type=\"monotone\"\r\n              dataKey=\"inProgress\"\r\n              stroke=\"#ffc658\"\r\n              name=\"In Progress\"\r\n            />\r\n          </LineChart>\r\n        </ResponsiveContainer>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAKD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAlBA;;;;;;;;;AAoBO,MAAM,wBAAwB;IACnC,MAAM,UAAU,CAAA,GAAA,wLAAA,CAAA,sBAAmB,AAAD;IAClC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uOAAA,CAAA,oBAAiB,AAAD,EAAE;IAErD,IAAI,WAAW;QACb,qBAAO,8OAAC,oIAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,SAAQ;;8BACb,8OAAC,iIAAA,CAAA,aAAU;8BAAC;;;;;;8BACZ,8OAAC,iIAAA,CAAA,mBAAgB;8BAAE,MAAM,OAAO;;;;;;;;;;;;IAGtC;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,kNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;0BAItC,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAQ;8BACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;wBAAC,MAAM,QAAQ,EAAE;;0CACzB,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;;;;;;0CAC/B,8OAAC,qJAAA,CAAA,QAAK;gCAAC,SAAQ;;;;;;0CACf,8OAAC,qJAAA,CAAA,QAAK;;;;;0CACN,8OAAC,uJAAA,CAAA,UAAO;;;;;0CACR,8OAAC,sJAAA,CAAA,SAAM;;;;;0CACP,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,MAAK;;;;;;0CAEP,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,MAAK;;;;;;0CAEP,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnB", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/LocationDistributionWidget.tsx"], "sourcesContent": ["/**\r\n * @file Location Distribution Widget - UX Optimized\r\n * @description Professional horizontal bar chart with proper label positioning and enhanced tooltips\r\n *\r\n * UX Improvements Applied:\r\n * - Moved location labels to left of bars for better readability\r\n * - Enhanced tooltip with percentage and more descriptive information\r\n * - Improved responsive layout and spacing\r\n * - Professional styling with better visual hierarchy\r\n */\r\n\r\n'use client';\r\n\r\nimport React from 'react';\r\nimport {\r\n  BarChart,\r\n  Bar,\r\n  XAxis,\r\n  YAxis,\r\n  CartesianGrid,\r\n  Tooltip,\r\n  ResponsiveContainer,\r\n} from 'recharts';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { useLocationMetricsQuery } from '../../data/hooks';\r\nimport { useReportingFilters } from '../../data/stores';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { MapPin, TrendingUp } from 'lucide-react';\r\n\r\n// Enhanced custom tooltip with percentage and better formatting\r\nconst CustomTooltip = ({ active, payload, label }: any) => {\r\n  if (active && payload && payload.length) {\r\n    const data = payload[0].payload;\r\n    const total = payload[0].payload.total || 100; // Fallback if total not provided\r\n    const percentage =\r\n      total > 0 ? Math.round((data.delegationCount / total) * 100) : 0;\r\n\r\n    return (\r\n      <div className=\"bg-white dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg\">\r\n        <p className=\"font-semibold text-gray-900 dark:text-gray-100 mb-3\">\r\n          {label}\r\n        </p>\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex justify-between items-center gap-6\">\r\n            <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Delegations:\r\n            </span>\r\n            <span className=\"font-bold text-blue-600 dark:text-blue-400 text-lg\">\r\n              {data.delegationCount}\r\n            </span>\r\n          </div>\r\n          <div className=\"flex justify-between items-center gap-6\">\r\n            <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Percentage:\r\n            </span>\r\n            <span className=\"font-medium text-emerald-600 dark:text-emerald-400\">\r\n              {percentage}%\r\n            </span>\r\n          </div>\r\n          {data.completionRate !== undefined && (\r\n            <div className=\"flex justify-between items-center gap-6\">\r\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                Completion Rate:\r\n              </span>\r\n              <span className=\"font-medium text-green-600 dark:text-green-400\">\r\n                {data.completionRate}%\r\n              </span>\r\n            </div>\r\n          )}\r\n          {data.averageResponseTime && (\r\n            <div className=\"flex justify-between items-center gap-6\">\r\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                Avg Response:\r\n              </span>\r\n              <span className=\"font-medium text-yellow-600 dark:text-yellow-400\">\r\n                {data.averageResponseTime}h\r\n              </span>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const LocationDistributionWidget = () => {\r\n  const filters = useReportingFilters();\r\n  const { data, isLoading, error } = useLocationMetricsQuery(filters);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <Card>\r\n        <CardHeader>\r\n          <Skeleton className=\"h-7 w-48\" />\r\n        </CardHeader>\r\n        <CardContent>\r\n          <Skeleton className=\"h-[350px] w-full rounded-lg\" />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-3\">\r\n            <MapPin className=\"h-6 w-6\" />\r\n            Location Distribution\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <Alert variant=\"destructive\">\r\n            <AlertTitle>Error</AlertTitle>\r\n            <AlertDescription>{error.message}</AlertDescription>\r\n          </Alert>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Calculate total for percentage calculations\r\n  const total = data?.reduce((sum, item) => sum + item.delegationCount, 0) || 0;\r\n  const enrichedData =\r\n    data?.map(item => ({\r\n      ...item,\r\n      total,\r\n    })) || [];\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader className=\"pb-6\">\r\n        <CardTitle className=\"flex items-center gap-3 text-xl\">\r\n          <MapPin className=\"h-6 w-6 text-primary\" />\r\n          Location Distribution\r\n        </CardTitle>\r\n        <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\r\n          <TrendingUp className=\"h-4 w-4\" />\r\n          <span>Delegation distribution across locations</span>\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent className=\"pt-6\">\r\n        <ResponsiveContainer width=\"100%\" height={350}>\r\n          <BarChart\r\n            data={enrichedData}\r\n            layout=\"vertical\"\r\n            margin={{ top: 8, right: 40, left: 20, bottom: 8 }}\r\n          >\r\n            <CartesianGrid\r\n              strokeDasharray=\"3 3\"\r\n              stroke=\"#f1f5f9\"\r\n              horizontal={true}\r\n              vertical={false}\r\n            />\r\n            <XAxis\r\n              type=\"number\"\r\n              fontSize={12}\r\n              tick={{ fill: '#64748b' }}\r\n              axisLine={{ stroke: '#e2e8f0' }}\r\n              tickLine={{ stroke: '#e2e8f0' }}\r\n            />\r\n            <YAxis\r\n              type=\"category\"\r\n              dataKey=\"location\"\r\n              width={140} // Increased width to accommodate longer location names\r\n              fontSize={12}\r\n              tick={{ fill: '#374151', fontSize: 12 }}\r\n              axisLine={{ stroke: '#e2e8f0' }}\r\n              tickLine={{ stroke: '#e2e8f0' }}\r\n              // Position labels on the left of bars (standard practice)\r\n              orientation=\"left\"\r\n            />\r\n            <Tooltip content={<CustomTooltip />} />\r\n            <Bar\r\n              dataKey=\"delegationCount\"\r\n              fill=\"#3b82f6\"\r\n              name=\"Delegations\"\r\n              radius={[0, 6, 6, 0]} // Rounded right corners only\r\n              stroke=\"#2563eb\"\r\n              strokeWidth={1}\r\n            />\r\n          </BarChart>\r\n        </ResponsiveContainer>\r\n\r\n        {/* Summary statistics */}\r\n        {total > 0 && (\r\n          <div className=\"mt-6 pt-4 border-t border-border\">\r\n            <div className=\"flex items-center justify-between text-sm\">\r\n              <span className=\"text-muted-foreground\">Total Delegations:</span>\r\n              <span className=\"font-semibold text-primary\">{total}</span>\r\n            </div>\r\n            <div className=\"flex items-center justify-between text-sm mt-2\">\r\n              <span className=\"text-muted-foreground\">Active Locations:</span>\r\n              <span className=\"font-semibold text-primary\">\r\n                {enrichedData.length}\r\n              </span>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;;AAKD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAjBA;;;;;;;;;AAmBA,gEAAgE;AAChE,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;IACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;QACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;QAC/B,MAAM,QAAQ,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,IAAI,KAAK,iCAAiC;QAChF,MAAM,aACJ,QAAQ,IAAI,KAAK,KAAK,CAAC,AAAC,KAAK,eAAe,GAAG,QAAS,OAAO;QAEjE,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BACV;;;;;;8BAEH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;8CAG3D,8OAAC;oCAAK,WAAU;8CACb,KAAK,eAAe;;;;;;;;;;;;sCAGzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;8CAG3D,8OAAC;oCAAK,WAAU;;wCACb;wCAAW;;;;;;;;;;;;;wBAGf,KAAK,cAAc,KAAK,2BACvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;8CAG3D,8OAAC;oCAAK,WAAU;;wCACb,KAAK,cAAc;wCAAC;;;;;;;;;;;;;wBAI1B,KAAK,mBAAmB,kBACvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA2C;;;;;;8CAG3D,8OAAC;oCAAK,WAAU;;wCACb,KAAK,mBAAmB;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;IAOxC;IACA,OAAO;AACT;AAEO,MAAM,6BAA6B;IACxC,MAAM,UAAU,CAAA,GAAA,wLAAA,CAAA,sBAAmB,AAAD;IAClC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mPAAA,CAAA,0BAAuB,AAAD,EAAE;IAE3D,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8BACH,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;8BAEtB,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;IAI5B;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8BACH,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIlC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,8OAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,iIAAA,CAAA,mBAAgB;0CAAE,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;IAK1C;IAEA,8CAA8C;IAC9C,MAAM,QAAQ,MAAM,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,eAAe,EAAE,MAAM;IAC5E,MAAM,eACJ,MAAM,IAAI,CAAA,OAAQ,CAAC;YACjB,GAAG,IAAI;YACP;QACF,CAAC,MAAM,EAAE;IAEX,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAyB;;;;;;;kCAG7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAGV,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC,mKAAA,CAAA,sBAAmB;wBAAC,OAAM;wBAAO,QAAQ;kCACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;4BACP,MAAM;4BACN,QAAO;4BACP,QAAQ;gCAAE,KAAK;gCAAG,OAAO;gCAAI,MAAM;gCAAI,QAAQ;4BAAE;;8CAEjD,8OAAC,6JAAA,CAAA,gBAAa;oCACZ,iBAAgB;oCAChB,QAAO;oCACP,YAAY;oCACZ,UAAU;;;;;;8CAEZ,8OAAC,qJAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,UAAU;oCACV,MAAM;wCAAE,MAAM;oCAAU;oCACxB,UAAU;wCAAE,QAAQ;oCAAU;oCAC9B,UAAU;wCAAE,QAAQ;oCAAU;;;;;;8CAEhC,8OAAC,qJAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,SAAQ;oCACR,OAAO;oCACP,UAAU;oCACV,MAAM;wCAAE,MAAM;wCAAW,UAAU;oCAAG;oCACtC,UAAU;wCAAE,QAAQ;oCAAU;oCAC9B,UAAU;wCAAE,QAAQ;oCAAU;oCAC9B,0DAA0D;oCAC1D,aAAY;;;;;;8CAEd,8OAAC,uJAAA,CAAA,UAAO;oCAAC,uBAAS,8OAAC;;;;;;;;;;8CACnB,8OAAC,mJAAA,CAAA,MAAG;oCACF,SAAQ;oCACR,MAAK;oCACL,MAAK;oCACL,QAAQ;wCAAC;wCAAG;wCAAG;wCAAG;qCAAE;oCACpB,QAAO;oCACP,aAAa;;;;;;;;;;;;;;;;;oBAMlB,QAAQ,mBACP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;kDAA8B;;;;;;;;;;;;0CAEhD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAK,WAAU;kDACb,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpC", "debugId": null}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/TaskStatusChart.tsx"], "sourcesContent": ["/**\r\n * @file Task Status Chart Component - Phase 2 Implementation\r\n * @description Task status distribution chart following existing chart patterns\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of visualizing task status distribution\r\n * - OCP: Open for extension via props configuration and styling options\r\n * - DIP: Depends on existing chart utilities and component abstractions\r\n *\r\n * Architecture Compliance:\r\n * - Follows existing chart component patterns\r\n * - Uses established chart utilities and styling\r\n * - Integrates with existing chart framework\r\n * - Maintains consistent visual design\r\n */\r\n\r\n'use client';\r\n\r\nimport { BarChart3 } from 'lucide-react';\r\nimport React, { useMemo } from 'react';\r\nimport {\r\n  Cell,\r\n  Legend,\r\n  Pie,\r\n  PieChart,\r\n  ResponsiveContainer,\r\n  Tooltip,\r\n} from 'recharts';\r\n\r\nimport type { TaskStatusPrisma } from '@/lib/types/domain';\r\n\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\n\r\nimport type { TaskStatusDistributionData } from '../../data/types/reporting';\r\n\r\ninterface TaskStatusChartProps {\r\n  className?: string;\r\n  data?: TaskStatusDistributionData[];\r\n  height?: number;\r\n  interactive?: boolean;\r\n  showLegend?: boolean;\r\n}\r\n\r\n/**\r\n * @component TaskStatusChart\r\n * @description Task status distribution chart following existing chart patterns\r\n *\r\n * Responsibilities:\r\n * - Visualize task status distribution using pie chart\r\n * - Follow existing chart component patterns\r\n * - Integrate with existing chart utilities\r\n * - Maintain consistent chart styling\r\n * - Provide interactive features when enabled\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying task status chart\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on chart library abstractions\r\n */\r\nexport const TaskStatusChart: React.FC<TaskStatusChartProps> = ({\r\n  className = '',\r\n  data = [],\r\n  height = 300,\r\n  interactive = true,\r\n  showLegend = true,\r\n}) => {\r\n  // Transform data for chart consumption following existing patterns\r\n  const chartData = useMemo(() => {\r\n    return data.map(item => ({\r\n      color: item.color || getTaskStatusColor(item.status),\r\n      name: formatStatusLabel(item.status),\r\n      percentage: Math.round(item.percentage),\r\n      status: item.status,\r\n      value: item.count,\r\n    }));\r\n  }, [data]);\r\n\r\n  // Calculate total for display\r\n  const totalTasks = useMemo(() => {\r\n    return chartData.reduce((sum, item) => sum + item.value, 0);\r\n  }, [chartData]);\r\n\r\n  // Custom tooltip component following existing patterns\r\n  const CustomTooltip = ({ active, payload }: any) => {\r\n    if (active && payload?.length) {\r\n      const data = payload[0].payload;\r\n      return (\r\n        <div className=\"rounded-lg border bg-white p-3 shadow-lg dark:bg-gray-800\">\r\n          <p className=\"font-medium\">{data.name}</p>\r\n          <p className=\"text-sm text-muted-foreground\">\r\n            Count: <span className=\"font-medium\">{data.value}</span>\r\n          </p>\r\n          <p className=\"text-sm text-muted-foreground\">\r\n            Percentage: <span className=\"font-medium\">{data.percentage}%</span>\r\n          </p>\r\n        </div>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  // Custom legend component following existing patterns\r\n  const CustomLegend = ({ payload }: any) => {\r\n    if (!showLegend || !payload) return null;\r\n\r\n    return (\r\n      <div className=\"mt-4 flex flex-wrap justify-center gap-4\">\r\n        {payload.map((entry: any, index: number) => (\r\n          <div className=\"flex items-center gap-2\" key={index}>\r\n            <div\r\n              className=\"size-3 rounded-full\"\r\n              style={{ backgroundColor: entry.color }}\r\n            />\r\n            <span className=\"text-sm text-muted-foreground\">\r\n              {entry.value} ({entry.payload.percentage}%)\r\n            </span>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Handle empty data state\r\n  if (!data || data.length === 0) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <BarChart3 className=\"size-5\" />\r\n            Task Status Distribution\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent\r\n          className=\"flex items-center justify-center\"\r\n          style={{ height }}\r\n        >\r\n          <div className=\"text-center text-muted-foreground\">\r\n            <BarChart3 className=\"mx-auto mb-2 size-12 opacity-50\" />\r\n            <p>No task status data available</p>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center gap-2\">\r\n          <BarChart3 className=\"size-5\" />\r\n          Task Status Distribution\r\n        </CardTitle>\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Total Tasks: {totalTasks}\r\n        </p>\r\n      </CardHeader>\r\n\r\n      <CardContent>\r\n        <ResponsiveContainer height={height} width=\"100%\">\r\n          <PieChart>\r\n            <Pie\r\n              cx=\"50%\"\r\n              cy=\"50%\"\r\n              data={chartData}\r\n              dataKey=\"value\"\r\n              fill=\"#8884d8\"\r\n              label={({ name, percentage }) => `${name}: ${percentage}%`}\r\n              labelLine={false}\r\n              outerRadius={Math.min(height * 0.3, 100)}\r\n            >\r\n              {chartData.map((entry, index) => (\r\n                <Cell fill={entry.color} key={`cell-${index}`} />\r\n              ))}\r\n            </Pie>\r\n            {interactive && <Tooltip content={<CustomTooltip />} />}\r\n            {showLegend && <Legend content={<CustomLegend />} />}\r\n          </PieChart>\r\n        </ResponsiveContainer>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\n/**\r\n * Utility function to format status labels for display\r\n * Following existing patterns for status formatting\r\n */\r\nconst formatStatusLabel = (status: TaskStatusPrisma): string => {\r\n  const statusLabels: Record<TaskStatusPrisma, string> = {\r\n    Assigned: 'Assigned',\r\n    Cancelled: 'Cancelled',\r\n    Completed: 'Completed',\r\n    In_Progress: 'In Progress',\r\n    Pending: 'Pending',\r\n  };\r\n  return statusLabels[status] || status;\r\n};\r\n\r\n/**\r\n * Utility function to get consistent colors for task statuses\r\n * Following existing color patterns and accessibility guidelines\r\n */\r\nconst getTaskStatusColor = (status: TaskStatusPrisma): string => {\r\n  const colorMap: Record<TaskStatusPrisma, string> = {\r\n    Assigned: '#3b82f6', // Blue - assigned/active\r\n    Cancelled: '#ef4444', // Red - cancelled/error\r\n    Completed: '#10b981', // Green - completed/success\r\n    In_Progress: '#8b5cf6', // Purple - in progress\r\n    Pending: '#f59e0b', // Amber - waiting/pending\r\n  };\r\n  return colorMap[status] || '#6b7280'; // Gray fallback\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AAID;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAfA;;;;;;AA2CO,MAAM,kBAAkD,CAAC,EAC9D,YAAY,EAAE,EACd,OAAO,EAAE,EACT,SAAS,GAAG,EACZ,cAAc,IAAI,EAClB,aAAa,IAAI,EAClB;IACC,mEAAmE;IACnE,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACvB,OAAO,KAAK,KAAK,IAAI,mBAAmB,KAAK,MAAM;gBACnD,MAAM,kBAAkB,KAAK,MAAM;gBACnC,YAAY,KAAK,KAAK,CAAC,KAAK,UAAU;gBACtC,QAAQ,KAAK,MAAM;gBACnB,OAAO,KAAK,KAAK;YACnB,CAAC;IACH,GAAG;QAAC;KAAK;IAET,8BAA8B;IAC9B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IAC3D,GAAG;QAAC;KAAU;IAEd,uDAAuD;IACvD,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAO;QAC7C,IAAI,UAAU,SAAS,QAAQ;YAC7B,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;YAC/B,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAe,KAAK,IAAI;;;;;;kCACrC,8OAAC;wBAAE,WAAU;;4BAAgC;0CACpC,8OAAC;gCAAK,WAAU;0CAAe,KAAK,KAAK;;;;;;;;;;;;kCAElD,8OAAC;wBAAE,WAAU;;4BAAgC;0CAC/B,8OAAC;gCAAK,WAAU;;oCAAe,KAAK,UAAU;oCAAC;;;;;;;;;;;;;;;;;;;QAInE;QACA,OAAO;IACT;IAEA,sDAAsD;IACtD,MAAM,eAAe,CAAC,EAAE,OAAO,EAAO;QACpC,IAAI,CAAC,cAAc,CAAC,SAAS,OAAO;QAEpC,qBACE,8OAAC;YAAI,WAAU;sBACZ,QAAQ,GAAG,CAAC,CAAC,OAAY,sBACxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,iBAAiB,MAAM,KAAK;4BAAC;;;;;;sCAExC,8OAAC;4BAAK,WAAU;;gCACb,MAAM,KAAK;gCAAC;gCAAG,MAAM,OAAO,CAAC,UAAU;gCAAC;;;;;;;;mBANC;;;;;;;;;;IAYtD;IAEA,0BAA0B;IAC1B,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,kNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAW;;;;;;;;;;;;8BAIpC,8OAAC,gIAAA,CAAA,cAAW;oBACV,WAAU;oBACV,OAAO;wBAAE;oBAAO;8BAEhB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,kNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAW;;;;;;;kCAGlC,8OAAC;wBAAE,WAAU;;4BAAgC;4BAC7B;;;;;;;;;;;;;0BAIlB,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,QAAQ;oBAAQ,OAAM;8BACzC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;0CACP,8OAAC,+IAAA,CAAA,MAAG;gCACF,IAAG;gCACH,IAAG;gCACH,MAAM;gCACN,SAAQ;gCACR,MAAK;gCACL,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAK,GAAG,KAAK,EAAE,EAAE,WAAW,CAAC,CAAC;gCAC1D,WAAW;gCACX,aAAa,KAAK,GAAG,CAAC,SAAS,KAAK;0CAEnC,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,8OAAC,oJAAA,CAAA,OAAI;wCAAC,MAAM,MAAM,KAAK;uCAAO,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;4BAGhD,6BAAe,8OAAC,uJAAA,CAAA,UAAO;gCAAC,uBAAS,8OAAC;;;;;;;;;;4BAClC,4BAAc,8OAAC,sJAAA,CAAA,SAAM;gCAAC,uBAAS,8OAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7C;AAEA;;;CAGC,GACD,MAAM,oBAAoB,CAAC;IACzB,MAAM,eAAiD;QACrD,UAAU;QACV,WAAW;QACX,WAAW;QACX,aAAa;QACb,SAAS;IACX;IACA,OAAO,YAAY,CAAC,OAAO,IAAI;AACjC;AAEA;;;CAGC,GACD,MAAM,qBAAqB,CAAC;IAC1B,MAAM,WAA6C;QACjD,UAAU;QACV,WAAW;QACX,WAAW;QACX,aAAa;QACb,SAAS;IACX;IACA,OAAO,QAAQ,CAAC,OAAO,IAAI,WAAW,gBAAgB;AACxD", "debugId": null}}, {"offset": {"line": 1581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/TaskPriorityDistribution.tsx"], "sourcesContent": ["/**\r\n * @file Task Priority Distribution Component - Phase 2 Implementation\r\n * @description Task priority distribution chart following existing chart patterns\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of visualizing task priority distribution\r\n * - OCP: Open for extension via props configuration and styling options\r\n * - DIP: Depends on existing chart utilities and component abstractions\r\n *\r\n * Architecture Compliance:\r\n * - Follows existing chart component patterns\r\n * - Uses established chart utilities and styling\r\n * - Integrates with existing chart framework\r\n * - Maintains consistent visual design\r\n */\r\n\r\n'use client';\r\n\r\nimport React, { useMemo } from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  Bar,\r\n  XAxis,\r\n  YAxis,\r\n  CartesianGrid,\r\n  Tooltip,\r\n  ResponsiveContainer,\r\n  Cell,\r\n} from 'recharts';\r\nimport { TaskPriorityPrisma } from '@/lib/types/domain';\r\nimport { TaskPriorityDistributionData } from '../../data/types/reporting';\r\nimport { Flag } from 'lucide-react';\r\n\r\ninterface TaskPriorityDistributionProps {\r\n  data?: TaskPriorityDistributionData[];\r\n  className?: string;\r\n  showLegend?: boolean;\r\n  interactive?: boolean;\r\n  height?: number;\r\n}\r\n\r\n/**\r\n * @component TaskPriorityDistribution\r\n * @description Task priority distribution chart following existing chart patterns\r\n *\r\n * Responsibilities:\r\n * - Visualize task priority distribution using bar chart\r\n * - Follow existing chart component patterns\r\n * - Integrate with existing chart utilities\r\n * - Maintain consistent chart styling\r\n * - Provide interactive features when enabled\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying task priority chart\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on chart library abstractions\r\n */\r\nexport const TaskPriorityDistribution: React.FC<\r\n  TaskPriorityDistributionProps\r\n> = ({\r\n  data = [],\r\n  className = '',\r\n  showLegend = true,\r\n  interactive = true,\r\n  height = 300,\r\n}) => {\r\n  // Transform data for chart consumption following existing patterns\r\n  const chartData = useMemo(() => {\r\n    return data.map(item => ({\r\n      name: formatPriorityLabel(item.priority),\r\n      value: item.count,\r\n      color: item.color || getTaskPriorityColor(item.priority),\r\n      percentage: Math.round(item.percentage),\r\n      priority: item.priority,\r\n    }));\r\n  }, [data]);\r\n\r\n  // Calculate total for display\r\n  const totalTasks = useMemo(() => {\r\n    return chartData.reduce((sum, item) => sum + item.value, 0);\r\n  }, [chartData]);\r\n\r\n  // Custom tooltip component following existing patterns\r\n  const CustomTooltip = ({ active, payload, label }: any) => {\r\n    if (active && payload && payload.length) {\r\n      const data = payload[0].payload;\r\n      return (\r\n        <div className=\"bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg\">\r\n          <p className=\"font-medium\">{label}</p>\r\n          <p className=\"text-sm text-muted-foreground\">\r\n            Count: <span className=\"font-medium\">{data.value}</span>\r\n          </p>\r\n          <p className=\"text-sm text-muted-foreground\">\r\n            Percentage: <span className=\"font-medium\">{data.percentage}%</span>\r\n          </p>\r\n        </div>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  // Custom bar component with priority-based colors\r\n  const CustomBar = (props: any) => {\r\n    const { fill, ...rest } = props;\r\n    const data = props.payload;\r\n    return <Bar {...rest} fill={data?.color || fill} />;\r\n  };\r\n\r\n  // Handle empty data state\r\n  if (!data || data.length === 0) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Flag className=\"h-5 w-5\" />\r\n            Task Priority Distribution\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent\r\n          className=\"flex items-center justify-center\"\r\n          style={{ height }}\r\n        >\r\n          <div className=\"text-center text-muted-foreground\">\r\n            <Flag className=\"h-12 w-12 mx-auto mb-2 opacity-50\" />\r\n            <p>No task priority data available</p>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center gap-2\">\r\n          <Flag className=\"h-5 w-5\" />\r\n          Task Priority Distribution\r\n        </CardTitle>\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Total Tasks: {totalTasks}\r\n        </p>\r\n      </CardHeader>\r\n\r\n      <CardContent>\r\n        <ResponsiveContainer width=\"100%\" height={height}>\r\n          <BarChart\r\n            data={chartData}\r\n            margin={{\r\n              top: 20,\r\n              right: 30,\r\n              left: 20,\r\n              bottom: 5,\r\n            }}\r\n          >\r\n            <CartesianGrid strokeDasharray=\"3 3\" className=\"opacity-30\" />\r\n            <XAxis\r\n              dataKey=\"name\"\r\n              tick={{ fontSize: 12 }}\r\n              className=\"text-muted-foreground\"\r\n            />\r\n            <YAxis tick={{ fontSize: 12 }} className=\"text-muted-foreground\" />\r\n            {interactive && <Tooltip content={<CustomTooltip />} />}\r\n            <Bar dataKey=\"value\" radius={[4, 4, 0, 0]} fill=\"#8884d8\">\r\n              {chartData.map((entry, index) => (\r\n                <Cell key={`cell-${index}`} fill={entry.color} />\r\n              ))}\r\n            </Bar>\r\n          </BarChart>\r\n        </ResponsiveContainer>\r\n\r\n        {/* Priority Legend */}\r\n        {showLegend && (\r\n          <div className=\"flex flex-wrap justify-center gap-4 mt-4\">\r\n            {chartData.map((entry, index) => (\r\n              <div key={index} className=\"flex items-center gap-2\">\r\n                <div\r\n                  className=\"w-3 h-3 rounded\"\r\n                  style={{ backgroundColor: entry.color }}\r\n                />\r\n                <span className=\"text-sm text-muted-foreground\">\r\n                  {entry.name}: {entry.value} ({entry.percentage}%)\r\n                </span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\n/**\r\n * Utility function to format priority labels for display\r\n * Following existing patterns for priority formatting\r\n */\r\nconst formatPriorityLabel = (priority: TaskPriorityPrisma): string => {\r\n  const priorityLabels: Record<TaskPriorityPrisma, string> = {\r\n    Low: 'Low',\r\n    Medium: 'Medium',\r\n    High: 'High',\r\n  };\r\n  return priorityLabels[priority] || priority;\r\n};\r\n\r\n/**\r\n * Utility function to get consistent colors for task priorities\r\n * Following existing color patterns and accessibility guidelines\r\n */\r\nconst getTaskPriorityColor = (priority: TaskPriorityPrisma): string => {\r\n  const colorMap: Record<TaskPriorityPrisma, string> = {\r\n    Low: '#10b981', // Green - low priority\r\n    Medium: '#f59e0b', // Amber - medium priority\r\n    High: '#ef4444', // Red - high priority\r\n  };\r\n  return colorMap[priority] || '#6b7280'; // Gray fallback\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AAID;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAhBA;;;;;;AA0CO,MAAM,2BAET,CAAC,EACH,OAAO,EAAE,EACT,YAAY,EAAE,EACd,aAAa,IAAI,EACjB,cAAc,IAAI,EAClB,SAAS,GAAG,EACb;IACC,mEAAmE;IACnE,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACvB,MAAM,oBAAoB,KAAK,QAAQ;gBACvC,OAAO,KAAK,KAAK;gBACjB,OAAO,KAAK,KAAK,IAAI,qBAAqB,KAAK,QAAQ;gBACvD,YAAY,KAAK,KAAK,CAAC,KAAK,UAAU;gBACtC,UAAU,KAAK,QAAQ;YACzB,CAAC;IACH,GAAG;QAAC;KAAK;IAET,8BAA8B;IAC9B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAAE;IAC3D,GAAG;QAAC;KAAU;IAEd,uDAAuD;IACvD,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;QACpD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;YAC/B,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAe;;;;;;kCAC5B,8OAAC;wBAAE,WAAU;;4BAAgC;0CACpC,8OAAC;gCAAK,WAAU;0CAAe,KAAK,KAAK;;;;;;;;;;;;kCAElD,8OAAC;wBAAE,WAAU;;4BAAgC;0CAC/B,8OAAC;gCAAK,WAAU;;oCAAe,KAAK,UAAU;oCAAC;;;;;;;;;;;;;;;;;;;QAInE;QACA,OAAO;IACT;IAEA,kDAAkD;IAClD,MAAM,YAAY,CAAC;QACjB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,GAAG;QAC1B,MAAM,OAAO,MAAM,OAAO;QAC1B,qBAAO,8OAAC,mJAAA,CAAA,MAAG;YAAE,GAAG,IAAI;YAAE,MAAM,MAAM,SAAS;;;;;;IAC7C;IAEA,0BAA0B;IAC1B,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIhC,8OAAC,gIAAA,CAAA,cAAW;oBACV,WAAU;oBACV,OAAO;wBAAE;oBAAO;8BAEhB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAG9B,8OAAC;wBAAE,WAAU;;4BAAgC;4BAC7B;;;;;;;;;;;;;0BAIlB,8OAAC,gIAAA,CAAA,cAAW;;kCACV,8OAAC,mKAAA,CAAA,sBAAmB;wBAAC,OAAM;wBAAO,QAAQ;kCACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;4BACP,MAAM;4BACN,QAAQ;gCACN,KAAK;gCACL,OAAO;gCACP,MAAM;gCACN,QAAQ;4BACV;;8CAEA,8OAAC,6JAAA,CAAA,gBAAa;oCAAC,iBAAgB;oCAAM,WAAU;;;;;;8CAC/C,8OAAC,qJAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,MAAM;wCAAE,UAAU;oCAAG;oCACrB,WAAU;;;;;;8CAEZ,8OAAC,qJAAA,CAAA,QAAK;oCAAC,MAAM;wCAAE,UAAU;oCAAG;oCAAG,WAAU;;;;;;gCACxC,6BAAe,8OAAC,uJAAA,CAAA,UAAO;oCAAC,uBAAS,8OAAC;;;;;;;;;;8CACnC,8OAAC,mJAAA,CAAA,MAAG;oCAAC,SAAQ;oCAAQ,QAAQ;wCAAC;wCAAG;wCAAG;wCAAG;qCAAE;oCAAE,MAAK;8CAC7C,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,8OAAC,oJAAA,CAAA,OAAI;4CAAuB,MAAM,MAAM,KAAK;2CAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;oBAOjC,4BACC,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,MAAM,KAAK;wCAAC;;;;;;kDAExC,8OAAC;wCAAK,WAAU;;4CACb,MAAM,IAAI;4CAAC;4CAAG,MAAM,KAAK;4CAAC;4CAAG,MAAM,UAAU;4CAAC;;;;;;;;+BANzC;;;;;;;;;;;;;;;;;;;;;;AAexB;AAEA;;;CAGC,GACD,MAAM,sBAAsB,CAAC;IAC3B,MAAM,iBAAqD;QACzD,KAAK;QACL,QAAQ;QACR,MAAM;IACR;IACA,OAAO,cAAc,CAAC,SAAS,IAAI;AACrC;AAEA;;;CAGC,GACD,MAAM,uBAAuB,CAAC;IAC5B,MAAM,WAA+C;QACnD,KAAK;QACL,QAAQ;QACR,MAAM;IACR;IACA,OAAO,QAAQ,CAAC,SAAS,IAAI,WAAW,gBAAgB;AAC1D", "debugId": null}}, {"offset": {"line": 1986, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/TaskAssignmentMetrics.tsx"], "sourcesContent": ["/**\r\n * @file Task Assignment Metrics Component - Phase 2 Implementation\r\n * @description Task assignment analytics component following existing patterns\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying task assignment metrics\r\n * - OCP: Open for extension via props configuration and styling options\r\n * - DIP: Depends on existing component and utility abstractions\r\n *\r\n * Architecture Compliance:\r\n * - Follows existing component patterns\r\n * - Uses established styling and layout patterns\r\n * - Integrates with existing data structures\r\n * - Maintains consistent visual design\r\n */\r\n\r\n'use client';\r\n\r\nimport React, { useMemo } from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { TaskAssignmentMetrics as TaskAssignmentMetricsType } from '../../data/types/reporting';\r\nimport { Users, Download, TrendingUp, Clock, CheckCircle } from 'lucide-react';\r\n\r\ninterface TaskAssignmentMetricsProps {\r\n  data?: TaskAssignmentMetricsType[];\r\n  className?: string;\r\n  showExportOptions?: boolean;\r\n  maxDisplayItems?: number;\r\n}\r\n\r\n/**\r\n * @component TaskAssignmentMetrics\r\n * @description Task assignment analytics component following existing patterns\r\n *\r\n * Responsibilities:\r\n * - Display task assignment metrics for employees\r\n * - Show completion rates and performance indicators\r\n * - Provide export functionality\r\n * - Follow established component patterns\r\n * - Maintain consistent styling and behavior\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying assignment metrics\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on existing component abstractions\r\n */\r\nexport const TaskAssignmentMetrics: React.FC<TaskAssignmentMetricsProps> = ({\r\n  data = [],\r\n  className = '',\r\n  showExportOptions = true,\r\n  maxDisplayItems = 10,\r\n}) => {\r\n  // Process and sort data for display\r\n  const processedData = useMemo(() => {\r\n    return data\r\n      .sort((a, b) => b.completionRate - a.completionRate)\r\n      .slice(0, maxDisplayItems);\r\n  }, [data, maxDisplayItems]);\r\n\r\n  // Calculate summary statistics\r\n  const summaryStats = useMemo(() => {\r\n    if (!data || data.length === 0) {\r\n      return {\r\n        totalEmployees: 0,\r\n        totalAssignedTasks: 0,\r\n        totalCompletedTasks: 0,\r\n        averageCompletionRate: 0,\r\n        averageCompletionTime: 0,\r\n      };\r\n    }\r\n\r\n    const totalAssignedTasks = data.reduce(\r\n      (sum, item) => sum + item.assignedTasks,\r\n      0\r\n    );\r\n    const totalCompletedTasks = data.reduce(\r\n      (sum, item) => sum + item.completedTasks,\r\n      0\r\n    );\r\n    const averageCompletionRate =\r\n      data.reduce((sum, item) => sum + item.completionRate, 0) / data.length;\r\n    const averageCompletionTime =\r\n      data.reduce((sum, item) => sum + item.averageCompletionTime, 0) /\r\n      data.length;\r\n\r\n    return {\r\n      totalEmployees: data.length,\r\n      totalAssignedTasks,\r\n      totalCompletedTasks,\r\n      averageCompletionRate: Math.round(averageCompletionRate),\r\n      averageCompletionTime: Math.round(averageCompletionTime * 10) / 10,\r\n    };\r\n  }, [data]);\r\n\r\n  // Export functionality following existing patterns\r\n  const handleExportCSV = async () => {\r\n    try {\r\n      if (!data || data.length === 0) {\r\n        console.warn('No data to export');\r\n        return;\r\n      }\r\n\r\n      const csvData = data.map(item => ({\r\n        'Employee Name': item.employeeName,\r\n        'Assigned Tasks': item.assignedTasks,\r\n        'Completed Tasks': item.completedTasks,\r\n        'Completion Rate (%)': Math.round(item.completionRate),\r\n        'Average Completion Time (days)': item.averageCompletionTime,\r\n      }));\r\n\r\n      if (csvData.length === 0) {\r\n        console.warn('No data to export');\r\n        return;\r\n      }\r\n\r\n      const csvContent = [\r\n        Object.keys(csvData[0]!).join(','),\r\n        ...csvData.map(row => Object.values(row).join(',')),\r\n      ].join('\\n');\r\n\r\n      const blob = new Blob([csvContent], { type: 'text/csv' });\r\n      const url = window.URL.createObjectURL(blob);\r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `task-assignment-metrics-${new Date().toISOString().split('T')[0]}.csv`;\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      window.URL.revokeObjectURL(url);\r\n      document.body.removeChild(a);\r\n    } catch (error) {\r\n      console.error('Export failed:', error);\r\n    }\r\n  };\r\n\r\n  // Get performance badge variant based on completion rate\r\n  const getPerformanceBadge = (completionRate: number) => {\r\n    if (completionRate >= 90)\r\n      return { variant: 'default' as const, label: 'Excellent' };\r\n    if (completionRate >= 75)\r\n      return { variant: 'secondary' as const, label: 'Good' };\r\n    if (completionRate >= 60)\r\n      return { variant: 'outline' as const, label: 'Average' };\r\n    return { variant: 'destructive' as const, label: 'Needs Improvement' };\r\n  };\r\n\r\n  // Handle empty data state\r\n  if (!data || data.length === 0) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Users className=\"h-5 w-5\" />\r\n            Task Assignment Metrics\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent className=\"flex items-center justify-center py-12\">\r\n          <div className=\"text-center text-muted-foreground\">\r\n            <Users className=\"h-12 w-12 mx-auto mb-2 opacity-50\" />\r\n            <p>No task assignment data available</p>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-4\">\r\n        <div>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Users className=\"h-5 w-5\" />\r\n            Task Assignment Metrics\r\n          </CardTitle>\r\n          <p className=\"text-sm text-muted-foreground mt-1\">\r\n            Employee task performance and completion rates\r\n          </p>\r\n        </div>\r\n        {showExportOptions && (\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={handleExportCSV}\r\n            className=\"h-8\"\r\n          >\r\n            <Download className=\"h-3 w-3 mr-1\" />\r\n            Export CSV\r\n          </Button>\r\n        )}\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-6\">\r\n        {/* Summary Statistics */}\r\n        <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4\">\r\n          <div className=\"flex flex-col items-center justify-center p-3 bg-blue-50 rounded-lg border\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <Users className=\"h-5 w-5 text-blue-600\" />\r\n              <span className=\"text-2xl font-bold text-blue-600\">\r\n                {summaryStats.totalEmployees}\r\n              </span>\r\n            </div>\r\n            <p className=\"text-sm font-medium text-blue-700\">Employees</p>\r\n          </div>\r\n\r\n          <div className=\"flex flex-col items-center justify-center p-3 bg-purple-50 rounded-lg border\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <TrendingUp className=\"h-5 w-5 text-purple-600\" />\r\n              <span className=\"text-2xl font-bold text-purple-600\">\r\n                {summaryStats.totalAssignedTasks}\r\n              </span>\r\n            </div>\r\n            <p className=\"text-sm font-medium text-purple-700\">Assigned</p>\r\n          </div>\r\n\r\n          <div className=\"flex flex-col items-center justify-center p-3 bg-green-50 rounded-lg border\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <CheckCircle className=\"h-5 w-5 text-green-600\" />\r\n              <span className=\"text-2xl font-bold text-green-600\">\r\n                {summaryStats.totalCompletedTasks}\r\n              </span>\r\n            </div>\r\n            <p className=\"text-sm font-medium text-green-700\">Completed</p>\r\n          </div>\r\n\r\n          <div className=\"flex flex-col items-center justify-center p-3 bg-emerald-50 rounded-lg border\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <TrendingUp className=\"h-5 w-5 text-emerald-600\" />\r\n              <span className=\"text-2xl font-bold text-emerald-600\">\r\n                {summaryStats.averageCompletionRate}%\r\n              </span>\r\n            </div>\r\n            <p className=\"text-sm font-medium text-emerald-700\">Avg. Rate</p>\r\n          </div>\r\n\r\n          <div className=\"flex flex-col items-center justify-center p-3 bg-orange-50 rounded-lg border\">\r\n            <div className=\"flex items-center gap-2 mb-2\">\r\n              <Clock className=\"h-5 w-5 text-orange-600\" />\r\n              <span className=\"text-2xl font-bold text-orange-600\">\r\n                {summaryStats.averageCompletionTime}d\r\n              </span>\r\n            </div>\r\n            <p className=\"text-sm font-medium text-orange-700\">Avg. Time</p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Employee Assignment Details */}\r\n        <div className=\"space-y-4\">\r\n          <h4 className=\"text-sm font-medium text-muted-foreground\">\r\n            Employee Performance ({processedData.length} of {data.length})\r\n          </h4>\r\n\r\n          <div className=\"space-y-3\">\r\n            {processedData.map((employee, index) => {\r\n              const performanceBadge = getPerformanceBadge(\r\n                employee.completionRate\r\n              );\r\n\r\n              return (\r\n                <div\r\n                  key={employee.employeeId}\r\n                  className=\"flex flex-col md:flex-row md:items-center justify-between p-4 border rounded-lg bg-muted/20 gap-4\"\r\n                >\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 mb-3\">\r\n                      <h5 className=\"font-medium truncate\">\r\n                        {employee.employeeName}\r\n                      </h5>\r\n                      <Badge\r\n                        variant={performanceBadge.variant}\r\n                        className=\"text-xs w-fit\"\r\n                      >\r\n                        {performanceBadge.label}\r\n                      </Badge>\r\n                    </div>\r\n\r\n                    <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3 text-sm\">\r\n                      <div className=\"flex flex-col\">\r\n                        <span className=\"text-muted-foreground text-xs\">\r\n                          Assigned\r\n                        </span>\r\n                        <span className=\"font-medium text-base\">\r\n                          {employee.assignedTasks}\r\n                        </span>\r\n                      </div>\r\n                      <div className=\"flex flex-col\">\r\n                        <span className=\"text-muted-foreground text-xs\">\r\n                          Completed\r\n                        </span>\r\n                        <span className=\"font-medium text-base\">\r\n                          {employee.completedTasks}\r\n                        </span>\r\n                      </div>\r\n                      <div className=\"flex flex-col\">\r\n                        <span className=\"text-muted-foreground text-xs\">\r\n                          Rate\r\n                        </span>\r\n                        <span className=\"font-medium text-base\">\r\n                          {Math.round(employee.completionRate)}%\r\n                        </span>\r\n                      </div>\r\n                      <div className=\"flex flex-col\">\r\n                        <span className=\"text-muted-foreground text-xs\">\r\n                          Avg. Time\r\n                        </span>\r\n                        <span className=\"font-medium text-base\">\r\n                          {employee.averageCompletionTime}d\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"w-full md:w-32 flex flex-col items-center\">\r\n                    <Progress\r\n                      value={employee.completionRate}\r\n                      className=\"h-3 w-full\"\r\n                    />\r\n                    <p className=\"text-sm font-medium mt-2 text-center\">\r\n                      {Math.round(employee.completionRate)}%\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          {data.length > maxDisplayItems && (\r\n            <div className=\"text-center pt-2\">\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                Showing top {maxDisplayItems} employees by completion rate\r\n              </p>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AAID;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AAiCO,MAAM,wBAA8D,CAAC,EAC1E,OAAO,EAAE,EACT,YAAY,EAAE,EACd,oBAAoB,IAAI,EACxB,kBAAkB,EAAE,EACrB;IACC,oCAAoC;IACpC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,OAAO,KACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,cAAc,GAAG,EAAE,cAAc,EAClD,KAAK,CAAC,GAAG;IACd,GAAG;QAAC;QAAM;KAAgB;IAE1B,+BAA+B;IAC/B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;YAC9B,OAAO;gBACL,gBAAgB;gBAChB,oBAAoB;gBACpB,qBAAqB;gBACrB,uBAAuB;gBACvB,uBAAuB;YACzB;QACF;QAEA,MAAM,qBAAqB,KAAK,MAAM,CACpC,CAAC,KAAK,OAAS,MAAM,KAAK,aAAa,EACvC;QAEF,MAAM,sBAAsB,KAAK,MAAM,CACrC,CAAC,KAAK,OAAS,MAAM,KAAK,cAAc,EACxC;QAEF,MAAM,wBACJ,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,cAAc,EAAE,KAAK,KAAK,MAAM;QACxE,MAAM,wBACJ,KAAK,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,qBAAqB,EAAE,KAC7D,KAAK,MAAM;QAEb,OAAO;YACL,gBAAgB,KAAK,MAAM;YAC3B;YACA;YACA,uBAAuB,KAAK,KAAK,CAAC;YAClC,uBAAuB,KAAK,KAAK,CAAC,wBAAwB,MAAM;QAClE;IACF,GAAG;QAAC;KAAK;IAET,mDAAmD;IACnD,MAAM,kBAAkB;QACtB,IAAI;YACF,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;gBAC9B,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,MAAM,UAAU,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAChC,iBAAiB,KAAK,YAAY;oBAClC,kBAAkB,KAAK,aAAa;oBACpC,mBAAmB,KAAK,cAAc;oBACtC,uBAAuB,KAAK,KAAK,CAAC,KAAK,cAAc;oBACrD,kCAAkC,KAAK,qBAAqB;gBAC9D,CAAC;YAED,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACxB,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,MAAM,aAAa;gBACjB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,EAAG,IAAI,CAAC;mBAC3B,QAAQ,GAAG,CAAC,CAAA,MAAO,OAAO,MAAM,CAAC,KAAK,IAAI,CAAC;aAC/C,CAAC,IAAI,CAAC;YAEP,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAW,EAAE;gBAAE,MAAM;YAAW;YACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,wBAAwB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YACpF,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,OAAO,GAAG,CAAC,eAAe,CAAC;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,yDAAyD;IACzD,MAAM,sBAAsB,CAAC;QAC3B,IAAI,kBAAkB,IACpB,OAAO;YAAE,SAAS;YAAoB,OAAO;QAAY;QAC3D,IAAI,kBAAkB,IACpB,OAAO;YAAE,SAAS;YAAsB,OAAO;QAAO;QACxD,IAAI,kBAAkB,IACpB,OAAO;YAAE,SAAS;YAAoB,OAAO;QAAU;QACzD,OAAO;YAAE,SAAS;YAAwB,OAAO;QAAoB;IACvE;IAEA,0BAA0B;IAC1B,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIjC,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;;0CACC,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;oBAInD,mCACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAM3C,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DACb,aAAa,cAAc;;;;;;;;;;;;kDAGhC,8OAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;0CAGnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;gDAAK,WAAU;0DACb,aAAa,kBAAkB;;;;;;;;;;;;kDAGpC,8OAAC;wCAAE,WAAU;kDAAsC;;;;;;;;;;;;0CAGrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAK,WAAU;0DACb,aAAa,mBAAmB;;;;;;;;;;;;kDAGrC,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;;;;;;;0CAGpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;gDAAK,WAAU;;oDACb,aAAa,qBAAqB;oDAAC;;;;;;;;;;;;;kDAGxC,8OAAC;wCAAE,WAAU;kDAAuC;;;;;;;;;;;;0CAGtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;;oDACb,aAAa,qBAAqB;oDAAC;;;;;;;;;;;;;kDAGxC,8OAAC;wCAAE,WAAU;kDAAsC;;;;;;;;;;;;;;;;;;kCAKvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA4C;oCACjC,cAAc,MAAM;oCAAC;oCAAK,KAAK,MAAM;oCAAC;;;;;;;0CAG/D,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,UAAU;oCAC5B,MAAM,mBAAmB,oBACvB,SAAS,cAAc;oCAGzB,qBACE,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,SAAS,YAAY;;;;;;0EAExB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,SAAS,iBAAiB,OAAO;gEACjC,WAAU;0EAET,iBAAiB,KAAK;;;;;;;;;;;;kEAI3B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgC;;;;;;kFAGhD,8OAAC;wEAAK,WAAU;kFACb,SAAS,aAAa;;;;;;;;;;;;0EAG3B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgC;;;;;;kFAGhD,8OAAC;wEAAK,WAAU;kFACb,SAAS,cAAc;;;;;;;;;;;;0EAG5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgC;;;;;;kFAGhD,8OAAC;wEAAK,WAAU;;4EACb,KAAK,KAAK,CAAC,SAAS,cAAc;4EAAE;;;;;;;;;;;;;0EAGzC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgC;;;;;;kFAGhD,8OAAC;wEAAK,WAAU;;4EACb,SAAS,qBAAqB;4EAAC;;;;;;;;;;;;;;;;;;;;;;;;;0DAMxC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oIAAA,CAAA,WAAQ;wDACP,OAAO,SAAS,cAAc;wDAC9B,WAAU;;;;;;kEAEZ,8OAAC;wDAAE,WAAU;;4DACV,KAAK,KAAK,CAAC,SAAS,cAAc;4DAAE;;;;;;;;;;;;;;uCA1DpC,SAAS,UAAU;;;;;gCA+D9B;;;;;;4BAGD,KAAK,MAAM,GAAG,iCACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCAAgC;wCAC9B;wCAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C", "debugId": null}}, {"offset": {"line": 2724, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/TaskMetricsWidget.tsx"], "sourcesContent": ["/**\r\n * @file Enhanced Task Metrics Widget - UX Optimized Design\r\n * @description Clean, professional task analytics widget following UX best practices\r\n *\r\n * UX Improvements Applied:\r\n * - Dramatically increased internal padding for breathing room\r\n * - Simplified layout with clean two-column grid structure\r\n * - Removed redundant icons and clutter\r\n * - Enhanced typography hierarchy and readability\r\n * - Consolidated export actions to main dashboard toolbar\r\n * - Professional spacing using 8px increments\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying task metrics clearly\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on existing widget framework abstractions\r\n */\r\n\r\n'use client';\r\n\r\nimport React, { useMemo } from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { useTaskAnalytics } from '../../data/hooks/useReportingQueries';\r\nimport { useReportingFilters } from '../../data/stores';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  CheckSquare,\r\n  Clock,\r\n  AlertTriangle,\r\n  CheckCircle,\r\n  TrendingUp,\r\n  Users,\r\n  BarChart3,\r\n} from 'lucide-react';\r\nimport { TaskStatusChart } from './TaskStatusChart';\r\nimport { TaskPriorityDistribution } from './TaskPriorityDistribution';\r\nimport { TaskAssignmentMetrics } from './TaskAssignmentMetrics';\r\n\r\ninterface TaskMetricsWidgetProps {\r\n  className?: string;\r\n  compact?: boolean;\r\n  showCharts?: boolean;\r\n}\r\n\r\n/**\r\n * @component TaskMetricsWidget\r\n * @description Professional task metrics widget with clean, spacious design\r\n *\r\n * Key UX Improvements:\r\n * - Generous internal padding (8px spacing system)\r\n * - Simplified metric cards with clear hierarchy\r\n * - Clean typography with proper contrast\r\n * - Removed clutter and redundant visual elements\r\n * - Export actions moved to main dashboard toolbar\r\n */\r\nexport const TaskMetricsWidget: React.FC<TaskMetricsWidgetProps> = ({\r\n  className = '',\r\n  compact = false,\r\n  showCharts = true,\r\n}) => {\r\n  // Use existing filter patterns and data hooks\r\n  const filters = useReportingFilters();\r\n  const {\r\n    data: taskAnalytics,\r\n    isLoading,\r\n    error,\r\n  } = useTaskAnalytics(filters, {\r\n    enabled: true,\r\n    staleTime: 2 * 60 * 1000, // 2 minutes\r\n  });\r\n\r\n  // Memoized calculations for performance\r\n  const metricsData = useMemo(() => {\r\n    if (!taskAnalytics) return null;\r\n\r\n    return {\r\n      totalTasks: taskAnalytics.totalCount || 0,\r\n      completedTasks:\r\n        taskAnalytics.statusDistribution?.find(s => s.status === 'Completed')\r\n          ?.count || 0,\r\n      completionRate: Math.round((taskAnalytics.completionRate || 0) * 100),\r\n      overdueTasks: taskAnalytics.overdueCount || 0,\r\n      averageCompletionTime: taskAnalytics.averageCompletionTime || 0,\r\n      assignedEmployees: taskAnalytics.assignmentMetrics?.length || 0,\r\n    };\r\n  }, [taskAnalytics]);\r\n\r\n  // Loading state with improved skeleton\r\n  if (isLoading) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader className=\"pb-6\">\r\n          <Skeleton className=\"h-7 w-48\" />\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-8\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n            {[...Array(6)].map((_, i) => (\r\n              <Skeleton key={i} className=\"h-32 rounded-lg\" />\r\n            ))}\r\n          </div>\r\n          {showCharts && !compact && (\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\r\n              <Skeleton className=\"h-80 rounded-lg\" />\r\n              <Skeleton className=\"h-80 rounded-lg\" />\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Clean error state\r\n  if (error) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-3\">\r\n            <CheckSquare className=\"h-6 w-6\" />\r\n            Task Analytics\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent className=\"pt-6\">\r\n          <Alert variant=\"destructive\">\r\n            <AlertTriangle className=\"h-4 w-4\" />\r\n            <AlertTitle>Error Loading Task Analytics</AlertTitle>\r\n            <AlertDescription>\r\n              {error.message || 'Failed to load task analytics data'}\r\n            </AlertDescription>\r\n          </Alert>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Main widget render with professional spacing and clean design\r\n  return (\r\n    <Card className={`${className} overflow-hidden`}>\r\n      {/* Clean header with generous spacing */}\r\n      <CardHeader className=\"pb-8\">\r\n        <CardTitle className=\"flex items-center gap-3 text-xl\">\r\n          <CheckSquare className=\"h-6 w-6 text-primary\" />\r\n          Task Analytics\r\n        </CardTitle>\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"px-8 pb-8 space-y-10\">\r\n        {/* Clean metrics grid with generous spacing */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n          {/* Total Tasks */}\r\n          <div className=\"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200\">\r\n            <div className=\"p-4 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-6\">\r\n              <CheckSquare className=\"h-8 w-8 text-blue-600\" />\r\n            </div>\r\n            <div className=\"text-4xl font-bold text-blue-600 mb-3\">\r\n              {metricsData?.totalTasks || 0}\r\n            </div>\r\n            <div className=\"text-sm font-medium text-muted-foreground uppercase tracking-wide\">\r\n              Total Tasks\r\n            </div>\r\n          </div>\r\n\r\n          {/* Completed Tasks */}\r\n          <div className=\"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200\">\r\n            <div className=\"p-4 rounded-full bg-green-100 dark:bg-green-900/30 mb-6\">\r\n              <CheckCircle className=\"h-8 w-8 text-green-600\" />\r\n            </div>\r\n            <div className=\"text-4xl font-bold text-green-600 mb-3\">\r\n              {metricsData?.completedTasks || 0}\r\n            </div>\r\n            <div className=\"text-sm font-medium text-muted-foreground uppercase tracking-wide\">\r\n              Completed\r\n            </div>\r\n          </div>\r\n\r\n          {/* Completion Rate */}\r\n          <div className=\"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200\">\r\n            <div className=\"p-4 rounded-full bg-emerald-100 dark:bg-emerald-900/30 mb-6\">\r\n              <TrendingUp className=\"h-8 w-8 text-emerald-600\" />\r\n            </div>\r\n            <div className=\"text-4xl font-bold text-emerald-600 mb-3\">\r\n              {metricsData?.completionRate || 0}%\r\n            </div>\r\n            <div className=\"text-sm font-medium text-muted-foreground uppercase tracking-wide\">\r\n              Completion Rate\r\n            </div>\r\n          </div>\r\n\r\n          {/* Overdue Tasks */}\r\n          <div className=\"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200\">\r\n            <div className=\"p-4 rounded-full bg-red-100 dark:bg-red-900/30 mb-6\">\r\n              <AlertTriangle className=\"h-8 w-8 text-red-600\" />\r\n            </div>\r\n            <div className=\"text-4xl font-bold text-red-600 mb-3\">\r\n              {metricsData?.overdueTasks || 0}\r\n            </div>\r\n            <div className=\"text-sm font-medium text-muted-foreground uppercase tracking-wide\">\r\n              Overdue\r\n            </div>\r\n          </div>\r\n\r\n          {/* Average Completion Time */}\r\n          <div className=\"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200\">\r\n            <div className=\"p-4 rounded-full bg-yellow-100 dark:bg-yellow-900/30 mb-6\">\r\n              <Clock className=\"h-8 w-8 text-yellow-600\" />\r\n            </div>\r\n            <div className=\"text-4xl font-bold text-yellow-600 mb-3\">\r\n              {metricsData?.averageCompletionTime || 0}d\r\n            </div>\r\n            <div className=\"text-sm font-medium text-muted-foreground uppercase tracking-wide\">\r\n              Avg. Completion\r\n            </div>\r\n          </div>\r\n\r\n          {/* Assigned Staff */}\r\n          <div className=\"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200\">\r\n            <div className=\"p-4 rounded-full bg-purple-100 dark:bg-purple-900/30 mb-6\">\r\n              <Users className=\"h-8 w-8 text-purple-600\" />\r\n            </div>\r\n            <div className=\"text-4xl font-bold text-purple-600 mb-3\">\r\n              {metricsData?.assignedEmployees || 0}\r\n            </div>\r\n            <div className=\"text-sm font-medium text-muted-foreground uppercase tracking-wide\">\r\n              Assigned Staff\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Charts Section with professional spacing */}\r\n        {showCharts && !compact && taskAnalytics && (\r\n          <div className=\"space-y-10\">\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\r\n              <Card className=\"border-2\">\r\n                <CardHeader className=\"pb-6\">\r\n                  <CardTitle className=\"text-lg\">Status Distribution</CardTitle>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <TaskStatusChart\r\n                    data={taskAnalytics.statusDistribution}\r\n                    className=\"h-80 w-full\"\r\n                    showLegend={true}\r\n                    interactive={true}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n\r\n              <Card className=\"border-2\">\r\n                <CardHeader className=\"pb-6\">\r\n                  <CardTitle className=\"text-lg\">\r\n                    Priority Distribution\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent>\r\n                  <TaskPriorityDistribution\r\n                    data={taskAnalytics.priorityDistribution}\r\n                    className=\"h-80 w-full\"\r\n                    showLegend={true}\r\n                    interactive={true}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n\r\n            {/* Assignment Metrics */}\r\n            <TaskAssignmentMetrics\r\n              data={taskAnalytics.assignmentMetrics}\r\n              className=\"w-full\"\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {/* Compact view */}\r\n        {compact && taskAnalytics && (\r\n          <div className=\"flex items-center justify-between p-6 bg-muted/50 rounded-lg\">\r\n            <div className=\"flex items-center gap-4\">\r\n              <div className=\"text-2xl font-bold text-primary\">\r\n                {metricsData?.completionRate}%\r\n              </div>\r\n              <span className=\"text-muted-foreground\">completion rate</span>\r\n            </div>\r\n            <Button variant=\"ghost\" size=\"sm\" className=\"gap-2\">\r\n              <BarChart3 className=\"h-4 w-4\" />\r\n              View Details\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;CAgBC;;;;AAID;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AApBA;;;;;;;;;;;;;AAuCO,MAAM,oBAAsD,CAAC,EAClE,YAAY,EAAE,EACd,UAAU,KAAK,EACf,aAAa,IAAI,EAClB;IACC,8CAA8C;IAC9C,MAAM,UAAU,CAAA,GAAA,wLAAA,CAAA,sBAAmB,AAAD;IAClC,MAAM,EACJ,MAAM,aAAa,EACnB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,kLAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS;QAC5B,SAAS;QACT,WAAW,IAAI,KAAK;IACtB;IAEA,wCAAwC;IACxC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,IAAI,CAAC,eAAe,OAAO;QAE3B,OAAO;YACL,YAAY,cAAc,UAAU,IAAI;YACxC,gBACE,cAAc,kBAAkB,EAAE,KAAK,CAAA,IAAK,EAAE,MAAM,KAAK,cACrD,SAAS;YACf,gBAAgB,KAAK,KAAK,CAAC,CAAC,cAAc,cAAc,IAAI,CAAC,IAAI;YACjE,cAAc,cAAc,YAAY,IAAI;YAC5C,uBAAuB,cAAc,qBAAqB,IAAI;YAC9D,mBAAmB,cAAc,iBAAiB,EAAE,UAAU;QAChE;IACF,GAAG;QAAC;KAAc;IAElB,uCAAuC;IACvC,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;8BAEtB,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,oIAAA,CAAA,WAAQ;oCAAS,WAAU;mCAAb;;;;;;;;;;wBAGlB,cAAc,CAAC,yBACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;IAMhC;IAEA,oBAAoB;IACpB,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIvC,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,iIAAA,CAAA,mBAAgB;0CACd,MAAM,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;IAM9B;IAEA,gEAAgE;IAChE,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,GAAG,UAAU,gBAAgB,CAAC;;0BAE7C,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAAyB;;;;;;;;;;;;0BAKpD,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC;wCAAI,WAAU;kDACZ,aAAa,cAAc;;;;;;kDAE9B,8OAAC;wCAAI,WAAU;kDAAoE;;;;;;;;;;;;0CAMrF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC;wCAAI,WAAU;kDACZ,aAAa,kBAAkB;;;;;;kDAElC,8OAAC;wCAAI,WAAU;kDAAoE;;;;;;;;;;;;0CAMrF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;wCAAI,WAAU;;4CACZ,aAAa,kBAAkB;4CAAE;;;;;;;kDAEpC,8OAAC;wCAAI,WAAU;kDAAoE;;;;;;;;;;;;0CAMrF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAE3B,8OAAC;wCAAI,WAAU;kDACZ,aAAa,gBAAgB;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;kDAAoE;;;;;;;;;;;;0CAMrF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;;4CACZ,aAAa,yBAAyB;4CAAE;;;;;;;kDAE3C,8OAAC;wCAAI,WAAU;kDAAoE;;;;;;;;;;;;0CAMrF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;kDACZ,aAAa,qBAAqB;;;;;;kDAErC,8OAAC;wCAAI,WAAU;kDAAoE;;;;;;;;;;;;;;;;;;oBAOtF,cAAc,CAAC,WAAW,+BACzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAEjC,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,sLAAA,CAAA,kBAAe;oDACd,MAAM,cAAc,kBAAkB;oDACtC,WAAU;oDACV,YAAY;oDACZ,aAAa;;;;;;;;;;;;;;;;;kDAKnB,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAIjC,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,+LAAA,CAAA,2BAAwB;oDACvB,MAAM,cAAc,oBAAoB;oDACxC,WAAU;oDACV,YAAY;oDACZ,aAAa;;;;;;;;;;;;;;;;;;;;;;;0CAOrB,8OAAC,4LAAA,CAAA,wBAAqB;gCACpB,MAAM,cAAc,iBAAiB;gCACrC,WAAU;;;;;;;;;;;;oBAMf,WAAW,+BACV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ,aAAa;4CAAe;;;;;;;kDAE/B,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAE1C,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;;kDAC1C,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C", "debugId": null}}, {"offset": {"line": 3403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/ReportingTableWidget.tsx"], "sourcesContent": ["/**\r\n * @file Reporting Table Widget\r\n * @description A widget that displays paginated delegation data in a table.\r\n */\r\n\r\n'use client';\r\n\r\nimport type { ColumnDef } from '@tanstack/react-table';\r\n\r\nimport {\r\n  flexRender,\r\n  getCoreRowModel,\r\n  useReactTable,\r\n} from '@tanstack/react-table';\r\nimport { format } from 'date-fns';\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Eye, Download, Settings } from 'lucide-react';\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@/components/ui/table';\r\nimport { useToast } from '@/hooks/utils/use-toast';\r\n\r\nimport type { Delegation } from '../../data/types';\r\n\r\nimport { useDelegations } from '../../data/hooks';\r\nimport { useReportingFilters } from '../../data/stores';\r\n\r\nexport const ReportingTableWidget = () => {\r\n  const filters = useReportingFilters();\r\n  const [pagination, setPagination] = React.useState({ page: 1, pageSize: 10 });\r\n  const router = useRouter();\r\n  const { toast } = useToast();\r\n\r\n  const { data, error, isLoading } = useDelegations(filters, pagination);\r\n\r\n  // Handle view delegation details\r\n  const handleViewDelegation = (delegation: Delegation) => {\r\n    // Navigate to delegation details page\r\n    router.push(`/delegations/${delegation.id}`);\r\n  };\r\n\r\n  // Handle export data\r\n  const handleExportData = () => {\r\n    if (!data?.data.length) {\r\n      toast({\r\n        title: 'No Data',\r\n        description: 'No data available to export.',\r\n        variant: 'destructive',\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Create CSV content\r\n    const headers = [\r\n      'ID',\r\n      'Customer',\r\n      'Vehicle',\r\n      'Status',\r\n      'Location',\r\n      'Created At',\r\n      'Completed At',\r\n    ];\r\n    const csvContent = [\r\n      headers.join(','),\r\n      ...data.data.map(delegation =>\r\n        [\r\n          delegation.delegationId,\r\n          delegation.customerName,\r\n          delegation.vehicleModel,\r\n          delegation.status,\r\n          delegation.location,\r\n          delegation.createdAt,\r\n          delegation.completedAt || 'N/A',\r\n        ].join(',')\r\n      ),\r\n    ].join('\\n');\r\n\r\n    // Download CSV file\r\n    const blob = new Blob([csvContent], { type: 'text/csv' });\r\n    const url = window.URL.createObjectURL(blob);\r\n    const a = document.createElement('a');\r\n    a.href = url;\r\n    a.download = `delegation-report-${new Date().toISOString().split('T')[0]}.csv`;\r\n    document.body.appendChild(a);\r\n    a.click();\r\n    document.body.removeChild(a);\r\n    window.URL.revokeObjectURL(url);\r\n\r\n    toast({\r\n      title: 'Export Successful',\r\n      description: 'Delegation data has been exported to CSV.',\r\n    });\r\n  };\r\n\r\n  // Column definitions for the reporting table\r\n  const columns: ColumnDef<Delegation>[] = [\r\n    {\r\n      accessorKey: 'delegationId',\r\n      header: 'Delegation ID',\r\n    },\r\n    {\r\n      accessorKey: 'customerName',\r\n      header: 'Customer',\r\n    },\r\n    {\r\n      accessorKey: 'vehicleModel',\r\n      header: 'Vehicle',\r\n    },\r\n    {\r\n      accessorKey: 'status',\r\n      cell: ({ row }) => <Badge>{row.original.status}</Badge>,\r\n      header: 'Status',\r\n    },\r\n    {\r\n      accessorKey: 'location',\r\n      header: 'Location',\r\n    },\r\n    {\r\n      accessorKey: 'createdAt',\r\n      cell: ({ row }) => format(new Date(row.original.createdAt), 'PPpp'),\r\n      header: 'Date',\r\n    },\r\n    {\r\n      cell: ({ row }) => (\r\n        <Button\r\n          size=\"sm\"\r\n          variant=\"outline\"\r\n          onClick={() => handleViewDelegation(row.original)}\r\n          className=\"flex items-center gap-1\"\r\n        >\r\n          <Eye className=\"h-3 w-3\" />\r\n          View\r\n        </Button>\r\n      ),\r\n      id: 'actions',\r\n      header: 'Actions',\r\n    },\r\n  ];\r\n\r\n  const table = useReactTable({\r\n    columns,\r\n    data: data?.data ?? [],\r\n    getCoreRowModel: getCoreRowModel(),\r\n    manualPagination: true,\r\n    rowCount: data?.meta.total ?? 0,\r\n  });\r\n\r\n  if (error) {\r\n    return (\r\n      <Alert variant=\"destructive\">\r\n        <AlertTitle>Error</AlertTitle>\r\n        <AlertDescription>{error.message}</AlertDescription>\r\n      </Alert>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <div className=\"flex items-center justify-between\">\r\n          <CardTitle>Detailed Report</CardTitle>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={handleExportData}\r\n              disabled={!data?.data.length}\r\n              className=\"flex items-center gap-1\"\r\n            >\r\n              <Download className=\"h-3 w-3\" />\r\n              Export\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <div className=\"rounded-md border\">\r\n          <Table>\r\n            <TableHeader>\r\n              {table.getHeaderGroups().map(headerGroup => (\r\n                <TableRow key={headerGroup.id}>\r\n                  {headerGroup.headers.map(header => (\r\n                    <TableHead key={header.id}>\r\n                      {flexRender(\r\n                        header.column.columnDef.header,\r\n                        header.getContext()\r\n                      )}\r\n                    </TableHead>\r\n                  ))}\r\n                </TableRow>\r\n              ))}\r\n            </TableHeader>\r\n            <TableBody>\r\n              {isLoading\r\n                ? Array.from({ length: pagination.pageSize }).map((_, i) => (\r\n                    <TableRow key={`loading-row-${i}`}>\r\n                      {columns.map((col, colIndex) => (\r\n                        <TableCell\r\n                          key={`loading-cell-${i}-${col.id ?? colIndex}`}\r\n                        >\r\n                          <Skeleton className=\"h-6 w-full\" />\r\n                        </TableCell>\r\n                      ))}\r\n                    </TableRow>\r\n                  ))\r\n                : table.getRowModel().rows.map(row => (\r\n                    <TableRow key={row.id}>\r\n                      {row.getVisibleCells().map(cell => (\r\n                        <TableCell key={cell.id}>\r\n                          {flexRender(\r\n                            cell.column.columnDef.cell,\r\n                            cell.getContext()\r\n                          )}\r\n                        </TableCell>\r\n                      ))}\r\n                    </TableRow>\r\n                  ))}\r\n            </TableBody>\r\n          </Table>\r\n        </div>\r\n        <div className=\"mt-4 flex items-center justify-between\">\r\n          <p className=\"text-sm text-muted-foreground\">\r\n            Page {data?.meta.page} of {data?.meta.totalPages} (\r\n            {data?.meta.total} total records)\r\n          </p>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Button\r\n              disabled={pagination.page === 1}\r\n              onClick={() =>\r\n                setPagination(prev => ({ ...prev, page: prev.page - 1 }))\r\n              }\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n            >\r\n              Previous\r\n            </Button>\r\n            <Button\r\n              disabled={pagination.page >= (data?.meta.totalPages ?? 1)}\r\n              onClick={() =>\r\n                setPagination(prev => ({ ...prev, page: prev.page + 1 }))\r\n              }\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n            >\r\n              Next\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAMD;AAAA;AAKA;AACA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAQA;AAIA;AAAA;AACA;AAAA;AAhCA;;;;;;;;;;;;;;;;AAkCO,MAAM,uBAAuB;IAClC,MAAM,UAAU,CAAA,GAAA,wLAAA,CAAA,sBAAmB,AAAD;IAClC,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;QAAE,MAAM;QAAG,UAAU;IAAG;IAC3E,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD,EAAE,SAAS;IAE3D,iCAAiC;IACjC,MAAM,uBAAuB,CAAC;QAC5B,sCAAsC;QACtC,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE,EAAE;IAC7C;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,IAAI,CAAC,MAAM,KAAK,QAAQ;YACtB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,qBAAqB;QACrB,MAAM,UAAU;YACd;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,MAAM,aAAa;YACjB,QAAQ,IAAI,CAAC;eACV,KAAK,IAAI,CAAC,GAAG,CAAC,CAAA,aACf;oBACE,WAAW,YAAY;oBACvB,WAAW,YAAY;oBACvB,WAAW,YAAY;oBACvB,WAAW,MAAM;oBACjB,WAAW,QAAQ;oBACnB,WAAW,SAAS;oBACpB,WAAW,WAAW,IAAI;iBAC3B,CAAC,IAAI,CAAC;SAEV,CAAC,IAAI,CAAC;QAEP,oBAAoB;QACpB,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,kBAAkB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;QAC9E,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;QAE3B,MAAM;YACJ,OAAO;YACP,aAAa;QACf;IACF;IAEA,6CAA6C;IAC7C,MAAM,UAAmC;QACvC;YACE,aAAa;YACb,QAAQ;QACV;QACA;YACE,aAAa;YACb,QAAQ;QACV;QACA;YACE,aAAa;YACb,QAAQ;QACV;QACA;YACE,aAAa;YACb,MAAM,CAAC,EAAE,GAAG,EAAE,iBAAK,8OAAC,iIAAA,CAAA,QAAK;8BAAE,IAAI,QAAQ,CAAC,MAAM;;;;;;YAC9C,QAAQ;QACV;QACA;YACE,aAAa;YACb,QAAQ;QACV;QACA;YACE,aAAa;YACb,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,IAAI,QAAQ,CAAC,SAAS,GAAG;YAC5D,QAAQ;QACV;QACA;YACE,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC,kIAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAQ;oBACR,SAAS,IAAM,qBAAqB,IAAI,QAAQ;oBAChD,WAAU;;sCAEV,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;wBAAY;;;;;;;YAI/B,IAAI;YACJ,QAAQ;QACV;KACD;IAED,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA,MAAM,MAAM,QAAQ,EAAE;QACtB,iBAAiB,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;QAC/B,kBAAkB;QAClB,UAAU,MAAM,KAAK,SAAS;IAChC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,SAAQ;;8BACb,8OAAC,iIAAA,CAAA,aAAU;8BAAC;;;;;;8BACZ,8OAAC,iIAAA,CAAA,mBAAgB;8BAAE,MAAM,OAAO;;;;;;;;;;;;IAGtC;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;sCACX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC,MAAM,KAAK;gCACtB,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;;;;;0BAMxC,8OAAC,gIAAA,CAAA,cAAW;;kCACV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;8CACJ,8OAAC,iIAAA,CAAA,cAAW;8CACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAA,4BAC3B,8OAAC,iIAAA,CAAA,WAAQ;sDACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAA,uBACvB,8OAAC,iIAAA,CAAA,YAAS;8DACP,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACR,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;mDAHL,OAAO,EAAE;;;;;2CAFd,YAAY,EAAE;;;;;;;;;;8CAYjC,8OAAC,iIAAA,CAAA,YAAS;8CACP,YACG,MAAM,IAAI,CAAC;wCAAE,QAAQ,WAAW,QAAQ;oCAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClD,8OAAC,iIAAA,CAAA,WAAQ;sDACN,QAAQ,GAAG,CAAC,CAAC,KAAK,yBACjB,8OAAC,iIAAA,CAAA,YAAS;8DAGR,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;mDAFf,CAAC,aAAa,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,UAAU;;;;;2CAHrC,CAAC,YAAY,EAAE,GAAG;;;;oDAUnC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA,oBAC3B,8OAAC,iIAAA,CAAA,WAAQ;sDACN,IAAI,eAAe,GAAG,GAAG,CAAC,CAAA,qBACzB,8OAAC,iIAAA,CAAA,YAAS;8DACP,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;mDAHH,KAAK,EAAE;;;;;2CAFZ,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;kCAcjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;oCAAgC;oCACrC,MAAM,KAAK;oCAAK;oCAAK,MAAM,KAAK;oCAAW;oCAChD,MAAM,KAAK;oCAAM;;;;;;;0CAEpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,UAAU,WAAW,IAAI,KAAK;wCAC9B,SAAS,IACP,cAAc,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,KAAK,IAAI,GAAG;gDAAE,CAAC;wCAEzD,MAAK;wCACL,SAAQ;kDACT;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,UAAU,WAAW,IAAI,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC;wCACxD,SAAS,IACP,cAAc,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,KAAK,IAAI,GAAG;gDAAE,CAAC;wCAEzD,MAAK;wCACL,SAAQ;kDACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 3808, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/BaseWidget.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/dashboard/widgets/BaseWidget.tsx\r\nimport React from 'react';\r\nimport {\r\n  Card,\r\n  CardHeader,\r\n  CardTitle,\r\n  CardDescription,\r\n  CardContent,\r\n  CardFooter,\r\n} from '@/components/ui/card'; // Assuming shadcn/ui card components\r\nimport { BaseWidgetProps } from '../../data/types'; // Import BaseWidgetProps from types\r\n\r\n/**\r\n * @component BaseWidget\r\n * @description A foundational component for all dashboard widgets, providing a consistent card-like structure.\r\n * Adheres to SRP by handling the common widget container UI.\r\n * Adheres to OCP by being extensible via `children` and `actions`.\r\n * @param {BaseWidgetProps} props - The component props.\r\n * @param {string} props.title - The title of the widget.\r\n * @param {string} [props.description] - An optional description for the widget.\r\n * @param {boolean} [props.loading] - If true, displays a loading state (e.g., skeleton or spinner).\r\n * @param {string} [props.error] - If provided, displays an error message.\r\n * @param {React.ReactNode} [props.actions] - Optional action buttons or components for the widget header.\r\n * @param {React.ReactNode} props.children - The main content of the widget.\r\n */\r\nexport const BaseWidget: React.FC<\r\n  BaseWidgetProps & { children: React.ReactNode }\r\n> = ({ title, description, loading, error, actions, children }) => {\r\n  return (\r\n    <Card className=\"h-full flex flex-col\">\r\n      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n        <CardTitle className=\"text-lg font-medium\">{title}</CardTitle>\r\n        {actions && <div className=\"flex-shrink-0\">{actions}</div>}\r\n      </CardHeader>\r\n      {description && (\r\n        <CardDescription className=\"px-6 pb-2\">{description}</CardDescription>\r\n      )}\r\n      <CardContent className=\"flex-grow p-6\">\r\n        {loading ? (\r\n          <div className=\"flex items-center justify-center h-full\">\r\n            {/* Placeholder for a loading spinner or skeleton */}\r\n            <p>Loading...</p>\r\n          </div>\r\n        ) : error ? (\r\n          <div className=\"flex items-center justify-center h-full text-red-500\">\r\n            <p>Error: {error}</p>\r\n          </div>\r\n        ) : (\r\n          children\r\n        )}\r\n      </CardContent>\r\n      {/* Optional CardFooter for additional actions or info */}\r\n      {/* <CardFooter>\r\n\t\t\t\t<p className=\"text-sm text-muted-foreground\">Widget footer content</p>\r\n\t\t\t</CardFooter> */}\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,8EAA8E;;;;;AAE9E,+NAO+B,qCAAqC;;;AAgB7D,MAAM,aAET,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE;IAC5D,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAuB;;;;;;oBAC3C,yBAAW,8OAAC;wBAAI,WAAU;kCAAiB;;;;;;;;;;;;YAE7C,6BACC,8OAAC,gIAAA,CAAA,kBAAe;gBAAC,WAAU;0BAAa;;;;;;0BAE1C,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACpB,wBACC,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;kCAAE;;;;;;;;;;2BAEH,sBACF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;4BAAE;4BAAQ;;;;;;;;;;;2BAGb;;;;;;;;;;;;AASV", "debugId": null}}, {"offset": {"line": 3903, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/SingleDelegationWidget.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/dashboard/widgets/SingleDelegationWidget.tsx\r\n\r\nimport React from 'react';\r\nimport { BaseWidget } from './BaseWidget';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Separator } from '@/components/ui/separator';\r\n// Import real delegation hook from existing API integration\r\nimport { useDelegation } from '@/lib/stores/queries/useDelegations';\r\nimport {\r\n  Calendar,\r\n  MapPin,\r\n  Users,\r\n  Car,\r\n  Clock,\r\n  CheckCircle,\r\n  AlertCircle,\r\n  FileText,\r\n  Download,\r\n  ExternalLink,\r\n} from 'lucide-react';\r\nimport { format } from 'date-fns';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface SingleDelegationWidgetProps {\r\n  delegationId: string;\r\n  title?: string;\r\n  showActions?: boolean;\r\n  onExport?: () => void;\r\n  onViewDetails?: () => void;\r\n}\r\n\r\n/**\r\n * @component SingleDelegationWidget\r\n * @description Widget for displaying detailed information about a single delegation\r\n *\r\n * Responsibilities:\r\n * - Shows comprehensive delegation details\r\n * - Displays progress and status information\r\n * - Provides quick actions for the delegation\r\n * - Handles loading and error states\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of presenting single delegation data\r\n * - OCP: Open for extension via props\r\n * - DIP: Depends on delegation hook abstractions\r\n */\r\nexport const SingleDelegationWidget: React.FC<SingleDelegationWidgetProps> = ({\r\n  delegationId,\r\n  title = 'Delegation Details',\r\n  showActions = true,\r\n  onExport,\r\n  onViewDetails,\r\n}) => {\r\n  const {\r\n    data: delegation,\r\n    isLoading,\r\n    error,\r\n    refetch,\r\n  } = useDelegation(delegationId);\r\n\r\n  // Calculate delegation progress\r\n  const getProgress = () => {\r\n    if (!delegation) return 0;\r\n\r\n    const statusProgress = {\r\n      DRAFT: 10,\r\n      PENDING: 25,\r\n      APPROVED: 50,\r\n      IN_PROGRESS: 75,\r\n      COMPLETED: 100,\r\n      CANCELLED: 0,\r\n    };\r\n\r\n    return (\r\n      statusProgress[delegation.status as keyof typeof statusProgress] || 0\r\n    );\r\n  };\r\n\r\n  // Get status color\r\n  const getStatusColor = (status: string) => {\r\n    const colors = {\r\n      DRAFT: 'bg-gray-100 text-gray-800',\r\n      PENDING: 'bg-yellow-100 text-yellow-800',\r\n      APPROVED: 'bg-blue-100 text-blue-800',\r\n      IN_PROGRESS: 'bg-purple-100 text-purple-800',\r\n      COMPLETED: 'bg-green-100 text-green-800',\r\n      CANCELLED: 'bg-red-100 text-red-800',\r\n    };\r\n    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';\r\n  };\r\n\r\n  // Handle actions\r\n  const handleExport = () => {\r\n    if (onExport) {\r\n      onExport();\r\n    } else {\r\n      console.log('Exporting delegation:', delegationId);\r\n    }\r\n  };\r\n\r\n  const handleViewDetails = () => {\r\n    if (onViewDetails) {\r\n      onViewDetails();\r\n    } else {\r\n      window.open(`/delegations/${delegationId}`, '_blank');\r\n    }\r\n  };\r\n\r\n  // Render actions\r\n  const renderActions = () => {\r\n    if (!showActions) return null;\r\n\r\n    return (\r\n      <div className=\"flex items-center gap-2\">\r\n        <Button variant=\"outline\" size=\"sm\" onClick={handleExport}>\r\n          <Download className=\"h-4 w-4 mr-2\" />\r\n          Export\r\n        </Button>\r\n        <Button variant=\"outline\" size=\"sm\" onClick={handleViewDetails}>\r\n          <ExternalLink className=\"h-4 w-4 mr-2\" />\r\n          View\r\n        </Button>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Render delegation info cards\r\n  const renderInfoCards = () => {\r\n    if (!delegation) return null;\r\n\r\n    const cards = [\r\n      {\r\n        icon: Calendar,\r\n        label: 'Start Date',\r\n        value: delegation.durationFrom\r\n          ? format(new Date(delegation.durationFrom), 'MMM d, yyyy')\r\n          : 'Not set',\r\n      },\r\n      {\r\n        icon: Calendar,\r\n        label: 'End Date',\r\n        value: delegation.durationTo\r\n          ? format(new Date(delegation.durationTo), 'MMM d, yyyy')\r\n          : 'Not set',\r\n      },\r\n      {\r\n        icon: MapPin,\r\n        label: 'Location',\r\n        value: delegation.location || 'Not specified',\r\n      },\r\n      {\r\n        icon: Users,\r\n        label: 'Delegates',\r\n        value: delegation.delegates?.length || 0,\r\n      },\r\n    ];\r\n\r\n    return (\r\n      <div className=\"grid grid-cols-2 gap-3\">\r\n        {cards.map((card, index) => {\r\n          const Icon = card.icon;\r\n          return (\r\n            <div\r\n              key={index}\r\n              className=\"flex items-center gap-2 p-2 bg-muted/50 rounded\"\r\n            >\r\n              <Icon className=\"h-4 w-4 text-muted-foreground\" />\r\n              <div>\r\n                <p className=\"text-xs text-muted-foreground\">{card.label}</p>\r\n                <p className=\"text-sm font-medium\">{card.value}</p>\r\n              </div>\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <BaseWidget\r\n      title={title}\r\n      loading={isLoading}\r\n      error={error?.message}\r\n      actions={renderActions()}\r\n    >\r\n      {delegation && (\r\n        <div className=\"space-y-4\">\r\n          {/* Delegation Header */}\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <h3 className=\"font-semibold text-lg truncate\">\r\n                {delegation.eventName}\r\n              </h3>\r\n              <Badge\r\n                className={cn('text-xs', getStatusColor(delegation.status))}\r\n              >\r\n                {delegation.status.replace('_', ' ')}\r\n              </Badge>\r\n            </div>\r\n\r\n            {delegation.notes && (\r\n              <p className=\"text-sm text-muted-foreground line-clamp-2\">\r\n                {delegation.notes}\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          {/* Progress */}\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm font-medium\">Progress</span>\r\n              <span className=\"text-sm text-muted-foreground\">\r\n                {getProgress()}%\r\n              </span>\r\n            </div>\r\n            <Progress value={getProgress()} className=\"h-2\" />\r\n          </div>\r\n\r\n          <Separator />\r\n\r\n          {/* Info Cards */}\r\n          {renderInfoCards()}\r\n\r\n          {/* Delegates Summary */}\r\n          {delegation.delegates && delegation.delegates.length > 0 && (\r\n            <>\r\n              <Separator />\r\n              <div className=\"space-y-2\">\r\n                <h4 className=\"font-medium flex items-center gap-2\">\r\n                  <Users className=\"h-4 w-4\" />\r\n                  Delegates ({delegation.delegates.length})\r\n                </h4>\r\n                <div className=\"space-y-1\">\r\n                  {delegation.delegates\r\n                    .slice(0, 3)\r\n                    .map((delegate: any, index: number) => (\r\n                      <div\r\n                        key={index}\r\n                        className=\"text-sm text-muted-foreground\"\r\n                      >\r\n                        {delegate.name} - {delegate.title}\r\n                      </div>\r\n                    ))}\r\n                  {delegation.delegates.length > 3 && (\r\n                    <div className=\"text-xs text-muted-foreground\">\r\n                      +{delegation.delegates.length - 3} more\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n        </div>\r\n      )}\r\n    </BaseWidget>\r\n  );\r\n};\r\n\r\nexport default SingleDelegationWidget;\r\n"], "names": [], "mappings": "AAAA,0FAA0F;;;;;;AAG1F;AAEA;AACA;AACA;AACA;AACA,4DAA4D;AAC5D;AACA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAAA;;;;;;;;;;;AAyBO,MAAM,yBAAgE,CAAC,EAC5E,YAAY,EACZ,QAAQ,oBAAoB,EAC5B,cAAc,IAAI,EAClB,QAAQ,EACR,aAAa,EACd;IACC,MAAM,EACJ,MAAM,UAAU,EAChB,SAAS,EACT,KAAK,EACL,OAAO,EACR,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD,EAAE;IAElB,gCAAgC;IAChC,MAAM,cAAc;QAClB,IAAI,CAAC,YAAY,OAAO;QAExB,MAAM,iBAAiB;YACrB,OAAO;YACP,SAAS;YACT,UAAU;YACV,aAAa;YACb,WAAW;YACX,WAAW;QACb;QAEA,OACE,cAAc,CAAC,WAAW,MAAM,CAAgC,IAAI;IAExE;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,OAAO;YACP,SAAS;YACT,UAAU;YACV,aAAa;YACb,WAAW;YACX,WAAW;QACb;QACA,OAAO,MAAM,CAAC,OAA8B,IAAI;IAClD;IAEA,iBAAiB;IACjB,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,yBAAyB;QACvC;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,eAAe;YACjB;QACF,OAAO;YACL,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,cAAc,EAAE;QAC9C;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,IAAI,CAAC,aAAa,OAAO;QAEzB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAK,SAAS;;sCAC3C,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAGvC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAK,SAAS;;sCAC3C,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;IAKjD;IAEA,+BAA+B;IAC/B,MAAM,kBAAkB;QACtB,IAAI,CAAC,YAAY,OAAO;QAExB,MAAM,QAAQ;YACZ;gBACE,MAAM,0MAAA,CAAA,WAAQ;gBACd,OAAO;gBACP,OAAO,WAAW,YAAY,GAC1B,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,WAAW,YAAY,GAAG,iBAC1C;YACN;YACA;gBACE,MAAM,0MAAA,CAAA,WAAQ;gBACd,OAAO;gBACP,OAAO,WAAW,UAAU,GACxB,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,WAAW,UAAU,GAAG,iBACxC;YACN;YACA;gBACE,MAAM,0MAAA,CAAA,SAAM;gBACZ,OAAO;gBACP,OAAO,WAAW,QAAQ,IAAI;YAChC;YACA;gBACE,MAAM,oMAAA,CAAA,QAAK;gBACX,OAAO;gBACP,OAAO,WAAW,SAAS,EAAE,UAAU;YACzC;SACD;QAED,qBACE,8OAAC;YAAI,WAAU;sBACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gBAChB,MAAM,OAAO,KAAK,IAAI;gBACtB,qBACE,8OAAC;oBAEC,WAAU;;sCAEV,8OAAC;4BAAK,WAAU;;;;;;sCAChB,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAAiC,KAAK,KAAK;;;;;;8CACxD,8OAAC;oCAAE,WAAU;8CAAuB,KAAK,KAAK;;;;;;;;;;;;;mBAN3C;;;;;YAUX;;;;;;IAGN;IAEA,qBACE,8OAAC,iLAAA,CAAA,aAAU;QACT,OAAO;QACP,SAAS;QACT,OAAO,OAAO;QACd,SAAS;kBAER,4BACC,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,WAAW,SAAS;;;;;;8CAEvB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW,eAAe,WAAW,MAAM;8CAExD,WAAW,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;wBAInC,WAAW,KAAK,kBACf,8OAAC;4BAAE,WAAU;sCACV,WAAW,KAAK;;;;;;;;;;;;8BAMvB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAsB;;;;;;8CACtC,8OAAC;oCAAK,WAAU;;wCACb;wCAAc;;;;;;;;;;;;;sCAGnB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,OAAO;4BAAe,WAAU;;;;;;;;;;;;8BAG5C,8OAAC,qIAAA,CAAA,YAAS;;;;;gBAGT;gBAGA,WAAW,SAAS,IAAI,WAAW,SAAS,CAAC,MAAM,GAAG,mBACrD;;sCACE,8OAAC,qIAAA,CAAA,YAAS;;;;;sCACV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;wCACjB,WAAW,SAAS,CAAC,MAAM;wCAAC;;;;;;;8CAE1C,8OAAC;oCAAI,WAAU;;wCACZ,WAAW,SAAS,CAClB,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,UAAe,sBACnB,8OAAC;gDAEC,WAAU;;oDAET,SAAS,IAAI;oDAAC;oDAAI,SAAS,KAAK;;+CAH5B;;;;;wCAMV,WAAW,SAAS,CAAC,MAAM,GAAG,mBAC7B,8OAAC;4CAAI,WAAU;;gDAAgC;gDAC3C,WAAW,SAAS,CAAC,MAAM,GAAG;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxD;uCAEe", "debugId": null}}, {"offset": {"line": 4293, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/SingleTaskWidget.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/dashboard/widgets/SingleTaskWidget.tsx\r\n\r\nimport React from 'react';\r\nimport { BaseWidget } from './BaseWidget';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport { Separator } from '@/components/ui/separator';\r\n// Import real task hook from existing API integration\r\nimport { useTask } from '@/lib/stores/queries/useTasks';\r\nimport {\r\n  Calendar,\r\n  User,\r\n  AlertTriangle,\r\n  Clock,\r\n  CheckCircle,\r\n  FileText,\r\n  Download,\r\n  ExternalLink,\r\n  Flag,\r\n} from 'lucide-react';\r\nimport { format } from 'date-fns';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface SingleTaskWidgetProps {\r\n  taskId: string;\r\n  title?: string;\r\n  showActions?: boolean;\r\n  onExport?: () => void;\r\n  onViewDetails?: () => void;\r\n}\r\n\r\n/**\r\n * @component SingleTaskWidget\r\n * @description Widget for displaying detailed information about a single task\r\n *\r\n * Responsibilities:\r\n * - Shows comprehensive task details\r\n * - Displays progress and status information\r\n * - Provides quick actions for the task\r\n * - Handles loading and error states\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of presenting single task data\r\n * - OCP: Open for extension via props\r\n * - DIP: Depends on task hook abstractions\r\n */\r\nexport const SingleTaskWidget: React.FC<SingleTaskWidgetProps> = ({\r\n  taskId,\r\n  title = 'Task Details',\r\n  showActions = true,\r\n  onExport,\r\n  onViewDetails,\r\n}) => {\r\n  const { data: task, isLoading, error, refetch } = useTask(taskId);\r\n\r\n  // Calculate task progress based on status\r\n  const getProgress = () => {\r\n    if (!task) return 0;\r\n\r\n    const statusProgress = {\r\n      PENDING: 0,\r\n      IN_PROGRESS: 50,\r\n      COMPLETED: 100,\r\n      CANCELLED: 0,\r\n      ON_HOLD: 25,\r\n    };\r\n\r\n    return statusProgress[task.status as keyof typeof statusProgress] || 0;\r\n  };\r\n\r\n  // Get status color\r\n  const getStatusColor = (status: string) => {\r\n    const colors = {\r\n      PENDING: 'bg-gray-100 text-gray-800',\r\n      IN_PROGRESS: 'bg-blue-100 text-blue-800',\r\n      COMPLETED: 'bg-green-100 text-green-800',\r\n      CANCELLED: 'bg-red-100 text-red-800',\r\n      ON_HOLD: 'bg-yellow-100 text-yellow-800',\r\n    };\r\n    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';\r\n  };\r\n\r\n  // Get priority color\r\n  const getPriorityColor = (priority: string) => {\r\n    const colors = {\r\n      LOW: 'bg-green-100 text-green-800',\r\n      MEDIUM: 'bg-yellow-100 text-yellow-800',\r\n      HIGH: 'bg-orange-100 text-orange-800',\r\n      URGENT: 'bg-red-100 text-red-800',\r\n    };\r\n    return (\r\n      colors[priority as keyof typeof colors] || 'bg-gray-100 text-gray-800'\r\n    );\r\n  };\r\n\r\n  // Check if task is overdue\r\n  const isOverdue = () => {\r\n    if (!task?.deadline) return false; // Use deadline instead of dueDate\r\n    return new Date(task.deadline) < new Date() && task.status !== 'Completed';\r\n  };\r\n\r\n  // Handle actions\r\n  const handleExport = () => {\r\n    if (onExport) {\r\n      onExport();\r\n    } else {\r\n      console.log('Exporting task:', taskId);\r\n    }\r\n  };\r\n\r\n  const handleViewDetails = () => {\r\n    if (onViewDetails) {\r\n      onViewDetails();\r\n    } else {\r\n      window.open(`/tasks/${taskId}`, '_blank');\r\n    }\r\n  };\r\n\r\n  // Render actions\r\n  const renderActions = () => {\r\n    if (!showActions) return null;\r\n\r\n    return (\r\n      <div className=\"flex items-center gap-2\">\r\n        <Button variant=\"outline\" size=\"sm\" onClick={handleExport}>\r\n          <Download className=\"h-4 w-4 mr-2\" />\r\n          Export\r\n        </Button>\r\n        <Button variant=\"outline\" size=\"sm\" onClick={handleViewDetails}>\r\n          <ExternalLink className=\"h-4 w-4 mr-2\" />\r\n          View\r\n        </Button>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Render task info cards\r\n  const renderInfoCards = () => {\r\n    if (!task) return null;\r\n\r\n    const cards = [\r\n      {\r\n        icon: Calendar,\r\n        label: 'Due Date',\r\n        value: task.deadline\r\n          ? format(new Date(task.deadline), 'MMM d, yyyy')\r\n          : 'Not set',\r\n        isOverdue: isOverdue(),\r\n      },\r\n      {\r\n        icon: User,\r\n        label: 'Assignee',\r\n        value:\r\n          task.staffEmployee?.name || task.driverEmployee?.name || 'Unassigned',\r\n      },\r\n      {\r\n        icon: Flag,\r\n        label: 'Priority',\r\n        value: task.priority || 'Not set',\r\n        badge: true,\r\n        badgeColor: getPriorityColor(task.priority || ''),\r\n      },\r\n      {\r\n        icon: FileText,\r\n        label: 'Location',\r\n        value: task.location || 'No location',\r\n      },\r\n    ];\r\n\r\n    return (\r\n      <div className=\"grid grid-cols-1 gap-3\">\r\n        {cards.map((card, index) => {\r\n          const Icon = card.icon;\r\n          return (\r\n            <div\r\n              key={index}\r\n              className=\"flex items-center gap-3 p-2 bg-muted/50 rounded\"\r\n            >\r\n              <Icon\r\n                className={cn(\r\n                  'h-4 w-4',\r\n                  card.isOverdue ? 'text-red-500' : 'text-muted-foreground'\r\n                )}\r\n              />\r\n              <div className=\"flex-1\">\r\n                <p className=\"text-xs text-muted-foreground\">{card.label}</p>\r\n                {card.badge ? (\r\n                  <Badge className={cn('text-xs', card.badgeColor)}>\r\n                    {card.value}\r\n                  </Badge>\r\n                ) : (\r\n                  <p\r\n                    className={cn(\r\n                      'text-sm font-medium',\r\n                      card.isOverdue ? 'text-red-600' : ''\r\n                    )}\r\n                  >\r\n                    {card.value}\r\n                    {card.isOverdue && (\r\n                      <span className=\"ml-1 text-red-500\">(Overdue)</span>\r\n                    )}\r\n                  </p>\r\n                )}\r\n              </div>\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <BaseWidget\r\n      title={title}\r\n      loading={isLoading}\r\n      error={error?.message}\r\n      actions={renderActions()}\r\n    >\r\n      {task && (\r\n        <div className=\"space-y-4\">\r\n          {/* Task Header */}\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-start justify-between gap-2\">\r\n              <h3 className=\"font-semibold text-lg leading-tight\">\r\n                {task.description}\r\n              </h3>\r\n              <div className=\"flex flex-col gap-1\">\r\n                <Badge className={cn('text-xs', getStatusColor(task.status))}>\r\n                  {task.status.replace('_', ' ')}\r\n                </Badge>\r\n                {isOverdue() && (\r\n                  <Badge variant=\"destructive\" className=\"text-xs\">\r\n                    <AlertTriangle className=\"h-3 w-3 mr-1\" />\r\n                    Overdue\r\n                  </Badge>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {task.notes && (\r\n              <p className=\"text-sm text-muted-foreground line-clamp-3\">\r\n                {task.notes}\r\n              </p>\r\n            )}\r\n          </div>\r\n\r\n          {/* Progress */}\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm font-medium\">Progress</span>\r\n              <span className=\"text-sm text-muted-foreground\">\r\n                {getProgress()}%\r\n              </span>\r\n            </div>\r\n            <Progress value={getProgress()} className=\"h-2\" />\r\n          </div>\r\n\r\n          <Separator />\r\n\r\n          {/* Info Cards */}\r\n          {renderInfoCards()}\r\n\r\n          {/* Time Tracking */}\r\n          {task.estimatedDuration && (\r\n            <>\r\n              <Separator />\r\n              <div className=\"space-y-2\">\r\n                <h4 className=\"font-medium flex items-center gap-2\">\r\n                  <Clock className=\"h-4 w-4\" />\r\n                  Time Tracking\r\n                </h4>\r\n                <div className=\"grid grid-cols-1 gap-2\">\r\n                  <div className=\"p-2 bg-blue-50 rounded text-center\">\r\n                    <p className=\"text-xs text-blue-600\">Estimated Duration</p>\r\n                    <p className=\"font-semibold text-blue-700\">\r\n                      {task.estimatedDuration || 0} minutes\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n        </div>\r\n      )}\r\n    </BaseWidget>\r\n  );\r\n};\r\n\r\nexport default SingleTaskWidget;\r\n"], "names": [], "mappings": "AAAA,oFAAoF;;;;;;AAGpF;AACA;AACA;AACA;AACA;AACA,sDAAsD;AACtD;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAAA;;;;;;;;;;;AAyBO,MAAM,mBAAoD,CAAC,EAChE,MAAM,EACN,QAAQ,cAAc,EACtB,cAAc,IAAI,EAClB,QAAQ,EACR,aAAa,EACd;IACC,MAAM,EAAE,MAAM,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE;IAE1D,0CAA0C;IAC1C,MAAM,cAAc;QAClB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,iBAAiB;YACrB,SAAS;YACT,aAAa;YACb,WAAW;YACX,WAAW;YACX,SAAS;QACX;QAEA,OAAO,cAAc,CAAC,KAAK,MAAM,CAAgC,IAAI;IACvE;IAEA,mBAAmB;IACnB,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,SAAS;YACT,aAAa;YACb,WAAW;YACX,WAAW;YACX,SAAS;QACX;QACA,OAAO,MAAM,CAAC,OAA8B,IAAI;IAClD;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,KAAK;YACL,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA,OACE,MAAM,CAAC,SAAgC,IAAI;IAE/C;IAEA,2BAA2B;IAC3B,MAAM,YAAY;QAChB,IAAI,CAAC,MAAM,UAAU,OAAO,OAAO,kCAAkC;QACrE,OAAO,IAAI,KAAK,KAAK,QAAQ,IAAI,IAAI,UAAU,KAAK,MAAM,KAAK;IACjE;IAEA,iBAAiB;IACjB,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ;QACF,OAAO;YACL,QAAQ,GAAG,CAAC,mBAAmB;QACjC;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,eAAe;YACjB;QACF,OAAO;YACL,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QAClC;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,IAAI,CAAC,aAAa,OAAO;QAEzB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAK,SAAS;;sCAC3C,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAGvC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAK,SAAS;;sCAC3C,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;IAKjD;IAEA,yBAAyB;IACzB,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,QAAQ;YACZ;gBACE,MAAM,0MAAA,CAAA,WAAQ;gBACd,OAAO;gBACP,OAAO,KAAK,QAAQ,GAChB,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,QAAQ,GAAG,iBAChC;gBACJ,WAAW;YACb;YACA;gBACE,MAAM,kMAAA,CAAA,OAAI;gBACV,OAAO;gBACP,OACE,KAAK,aAAa,EAAE,QAAQ,KAAK,cAAc,EAAE,QAAQ;YAC7D;YACA;gBACE,MAAM,kMAAA,CAAA,OAAI;gBACV,OAAO;gBACP,OAAO,KAAK,QAAQ,IAAI;gBACxB,OAAO;gBACP,YAAY,iBAAiB,KAAK,QAAQ,IAAI;YAChD;YACA;gBACE,MAAM,8MAAA,CAAA,WAAQ;gBACd,OAAO;gBACP,OAAO,KAAK,QAAQ,IAAI;YAC1B;SACD;QAED,qBACE,8OAAC;YAAI,WAAU;sBACZ,MAAM,GAAG,CAAC,CAAC,MAAM;gBAChB,MAAM,OAAO,KAAK,IAAI;gBACtB,qBACE,8OAAC;oBAEC,WAAU;;sCAEV,8OAAC;4BACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,WACA,KAAK,SAAS,GAAG,iBAAiB;;;;;;sCAGtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAiC,KAAK,KAAK;;;;;;gCACvD,KAAK,KAAK,iBACT,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW,KAAK,UAAU;8CAC5C,KAAK,KAAK;;;;;yDAGb,8OAAC;oCACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,uBACA,KAAK,SAAS,GAAG,iBAAiB;;wCAGnC,KAAK,KAAK;wCACV,KAAK,SAAS,kBACb,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;;;;;;;;mBAxBvC;;;;;YA+BX;;;;;;IAGN;IAEA,qBACE,8OAAC,iLAAA,CAAA,aAAU;QACT,OAAO;QACP,SAAS;QACT,OAAO,OAAO;QACd,SAAS;kBAER,sBACC,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,KAAK,WAAW;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW,eAAe,KAAK,MAAM;sDACvD,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;wCAE3B,6BACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAc,WAAU;;8DACrC,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;wBAOjD,KAAK,KAAK,kBACT,8OAAC;4BAAE,WAAU;sCACV,KAAK,KAAK;;;;;;;;;;;;8BAMjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAsB;;;;;;8CACtC,8OAAC;oCAAK,WAAU;;wCACb;wCAAc;;;;;;;;;;;;;sCAGnB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,OAAO;4BAAe,WAAU;;;;;;;;;;;;8BAG5C,8OAAC,qIAAA,CAAA,YAAS;;;;;gBAGT;gBAGA,KAAK,iBAAiB,kBACrB;;sCACE,8OAAC,qIAAA,CAAA,YAAS;;;;;sCACV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;;oDACV,KAAK,iBAAiB,IAAI;oDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnD;uCAEe", "debugId": null}}, {"offset": {"line": 4748, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/VehicleAnalyticsWidget.tsx"], "sourcesContent": ["/**\r\n * @file VehicleAnalyticsWidget.tsx\r\n * @description Vehicle analytics widget following existing widget patterns and SOLID principles\r\n */\r\n\r\nimport React from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Car,\r\n  TrendingUp,\r\n  AlertTriangle,\r\n  Clock,\r\n  DollarSign,\r\n  Wrench,\r\n  Download,\r\n  MoreHorizontal,\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { useVehicleAnalytics } from '../../hooks/useVehicleAnalytics';\r\nimport { DataLoader } from '@/components/ui/data-loader';\r\nimport { ErrorDisplay } from '@/components/ui/error-display';\r\nimport type { ReportingFilters } from '../../data/types/reporting';\r\n\r\n/**\r\n * Props interface for VehicleAnalyticsWidget\r\n */\r\ninterface VehicleAnalyticsWidgetProps {\r\n  filters?: ReportingFilters;\r\n  className?: string;\r\n  showExportOptions?: boolean;\r\n  compact?: boolean;\r\n}\r\n\r\n/**\r\n * Metric card component for displaying individual metrics\r\n */\r\ninterface MetricCardProps {\r\n  label: string;\r\n  value: string | number;\r\n  icon: React.ReactNode;\r\n  trend?: {\r\n    value: number;\r\n    isPositive: boolean;\r\n  };\r\n  variant?: 'default' | 'warning' | 'success' | 'destructive';\r\n}\r\n\r\nconst MetricCard: React.FC<MetricCardProps> = ({\r\n  label,\r\n  value,\r\n  icon,\r\n  trend,\r\n  variant = 'default',\r\n}) => {\r\n  const variantStyles = {\r\n    default: 'border-gray-200',\r\n    warning: 'border-orange-200 bg-orange-50',\r\n    success: 'border-green-200 bg-green-50',\r\n    destructive: 'border-red-200 bg-red-50',\r\n  };\r\n\r\n  return (\r\n    <div className={cn('p-4 border rounded-lg', variantStyles[variant])}>\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          {icon}\r\n          <span className=\"text-sm font-medium text-gray-600\">{label}</span>\r\n        </div>\r\n        {trend && (\r\n          <Badge\r\n            variant={trend.isPositive ? 'default' : 'destructive'}\r\n            className=\"text-xs\"\r\n          >\r\n            {trend.isPositive ? '+' : ''}\r\n            {trend.value}%\r\n          </Badge>\r\n        )}\r\n      </div>\r\n      <div className=\"mt-2\">\r\n        <span className=\"text-2xl font-bold\">{value}</span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * VehicleAnalyticsWidget Component\r\n *\r\n * Displays comprehensive vehicle analytics following existing widget patterns.\r\n *\r\n * Responsibilities:\r\n * - Display vehicle analytics in widget format\r\n * - Integrate with existing dashboard layout\r\n * - Follow established widget composition patterns\r\n * - Maintain consistent styling and behavior\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying vehicle metrics\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on existing widget framework abstractions\r\n */\r\nexport const VehicleAnalyticsWidget: React.FC<VehicleAnalyticsWidgetProps> = ({\r\n  filters,\r\n  className = '',\r\n  showExportOptions = true,\r\n  compact = false,\r\n}) => {\r\n  // Use existing hook patterns\r\n  const {\r\n    data: vehicleAnalytics,\r\n    isLoading,\r\n    error,\r\n  } = useVehicleAnalytics(filters);\r\n\r\n  // Handle loading state\r\n  if (isLoading) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Car className=\"h-5 w-5\" />\r\n            Vehicle Analytics\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <DataLoader isLoading={true} data={null} error={null}>\r\n            {() => null}\r\n          </DataLoader>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Handle error state\r\n  if (error) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Car className=\"h-5 w-5\" />\r\n            Vehicle Analytics\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <ErrorDisplay error={error} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Calculate derived metrics\r\n  const totalVehicles = vehicleAnalytics?.totalCount || 0;\r\n  const activeVehicles =\r\n    vehicleAnalytics?.utilizationMetrics?.filter(v => v.utilizationRate > 0)\r\n      .length || 0;\r\n  const utilizationRate =\r\n    totalVehicles > 0 ? (activeVehicles / totalVehicles) * 100 : 0;\r\n  const totalCost = vehicleAnalytics?.costAnalysis?.totalCost || 0;\r\n  const avgCostPerService =\r\n    vehicleAnalytics?.costAnalysis?.averageCostPerService || 0;\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader className=\"pb-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Car className=\"h-5 w-5\" />\r\n            Vehicle Analytics\r\n          </CardTitle>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              {totalVehicles} vehicles\r\n            </Badge>\r\n            {showExportOptions && (\r\n              <Button variant=\"ghost\" size=\"sm\">\r\n                <Download className=\"h-4 w-4\" />\r\n              </Button>\r\n            )}\r\n            <Button variant=\"ghost\" size=\"sm\">\r\n              <MoreHorizontal className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-6\">\r\n        {/* Key Metrics Grid - Following existing patterns */}\r\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n          <MetricCard\r\n            label=\"Total Vehicles\"\r\n            value={totalVehicles}\r\n            icon={<Car className=\"h-4 w-4\" />}\r\n          />\r\n          <MetricCard\r\n            label=\"Utilization Rate\"\r\n            value={`${Math.round(utilizationRate)}%`}\r\n            icon={<TrendingUp className=\"h-4 w-4\" />}\r\n            variant={utilizationRate < 70 ? 'warning' : 'success'}\r\n          />\r\n          <MetricCard\r\n            label=\"Total Service Cost\"\r\n            value={`$${totalCost.toLocaleString()}`}\r\n            icon={<DollarSign className=\"h-4 w-4\" />}\r\n          />\r\n          <MetricCard\r\n            label=\"Avg. Cost/Service\"\r\n            value={`$${Math.round(avgCostPerService)}`}\r\n            icon={<Wrench className=\"h-4 w-4\" />}\r\n          />\r\n        </div>\r\n\r\n        {/* Quick Stats */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm font-medium text-blue-700\">\r\n                Active Vehicles\r\n              </span>\r\n              <Car className=\"h-4 w-4 text-blue-600\" />\r\n            </div>\r\n            <span className=\"text-xl font-bold text-blue-900\">\r\n              {activeVehicles}\r\n            </span>\r\n          </div>\r\n\r\n          <div className=\"p-4 bg-green-50 rounded-lg\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm font-medium text-green-700\">\r\n                Maintenance Due\r\n              </span>\r\n              <AlertTriangle className=\"h-4 w-4 text-green-600\" />\r\n            </div>\r\n            <span className=\"text-xl font-bold text-green-900\">\r\n              {vehicleAnalytics?.maintenanceSchedule?.filter(\r\n                m => new Date(m.nextMaintenanceDate) <= new Date()\r\n              ).length || 0}\r\n            </span>\r\n          </div>\r\n\r\n          <div className=\"p-4 bg-orange-50 rounded-lg\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm font-medium text-orange-700\">\r\n                Avg. Service Time\r\n              </span>\r\n              <Clock className=\"h-4 w-4 text-orange-600\" />\r\n            </div>\r\n            <span className=\"text-xl font-bold text-orange-900\">\r\n              {vehicleAnalytics?.serviceHistory &&\r\n              vehicleAnalytics.serviceHistory.length > 0\r\n                ? Math.round(\r\n                    vehicleAnalytics.serviceHistory.reduce(\r\n                      (acc, service) => acc + (service.cost || 0),\r\n                      0\r\n                    ) / vehicleAnalytics.serviceHistory.length\r\n                  )\r\n                : 0}\r\n              h\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        {!compact && (\r\n          <div className=\"pt-4 border-t\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm text-gray-600\">\r\n                Last updated: {new Date().toLocaleTimeString()}\r\n              </span>\r\n              <Button variant=\"outline\" size=\"sm\">\r\n                View Details\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default VehicleAnalyticsWidget;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAAA;AACA;AACA;AAAA;AACA;;;;;;;;;;AA2BA,MAAM,aAAwC,CAAC,EAC7C,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,UAAU,SAAS,EACpB;IACC,MAAM,gBAAgB;QACpB,SAAS;QACT,SAAS;QACT,SAAS;QACT,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,aAAa,CAAC,QAAQ;;0BAChE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ;0CACD,8OAAC;gCAAK,WAAU;0CAAqC;;;;;;;;;;;;oBAEtD,uBACC,8OAAC,iIAAA,CAAA,QAAK;wBACJ,SAAS,MAAM,UAAU,GAAG,YAAY;wBACxC,WAAU;;4BAET,MAAM,UAAU,GAAG,MAAM;4BACzB,MAAM,KAAK;4BAAC;;;;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BAAsB;;;;;;;;;;;;;;;;;AAI9C;AAkBO,MAAM,yBAAgE,CAAC,EAC5E,OAAO,EACP,YAAY,EAAE,EACd,oBAAoB,IAAI,EACxB,UAAU,KAAK,EAChB;IACC,6BAA6B;IAC7B,MAAM,EACJ,MAAM,gBAAgB,EACtB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE;IAExB,uBAAuB;IACvB,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAI/B,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,aAAU;wBAAC,WAAW;wBAAM,MAAM;wBAAM,OAAO;kCAC7C,IAAM;;;;;;;;;;;;;;;;;IAKjB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAI/B,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,4IAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;;;;;;;;;;;;IAI7B;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB,kBAAkB,cAAc;IACtD,MAAM,iBACJ,kBAAkB,oBAAoB,OAAO,CAAA,IAAK,EAAE,eAAe,GAAG,GACnE,UAAU;IACf,MAAM,kBACJ,gBAAgB,IAAI,AAAC,iBAAiB,gBAAiB,MAAM;IAC/D,MAAM,YAAY,kBAAkB,cAAc,aAAa;IAC/D,MAAM,oBACJ,kBAAkB,cAAc,yBAAyB;IAE3D,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;wCAClC;wCAAc;;;;;;;gCAEhB,mCACC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAGxB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAM;gCACN,OAAO;gCACP,oBAAM,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEvB,8OAAC;gCACC,OAAM;gCACN,OAAO,GAAG,KAAK,KAAK,CAAC,iBAAiB,CAAC,CAAC;gCACxC,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAC5B,SAAS,kBAAkB,KAAK,YAAY;;;;;;0CAE9C,8OAAC;gCACC,OAAM;gCACN,OAAO,CAAC,CAAC,EAAE,UAAU,cAAc,IAAI;gCACvC,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;0CAE9B,8OAAC;gCACC,OAAM;gCACN,OAAO,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,oBAAoB;gCAC1C,oBAAM,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAK5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;0DAGpD,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;0DAGrD,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;kDAE3B,8OAAC;wCAAK,WAAU;kDACb,kBAAkB,qBAAqB,OACtC,CAAA,IAAK,IAAI,KAAK,EAAE,mBAAmB,KAAK,IAAI,QAC5C,UAAU;;;;;;;;;;;;0CAIhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;0DAGtD,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,8OAAC;wCAAK,WAAU;;4CACb,kBAAkB,kBACnB,iBAAiB,cAAc,CAAC,MAAM,GAAG,IACrC,KAAK,KAAK,CACR,iBAAiB,cAAc,CAAC,MAAM,CACpC,CAAC,KAAK,UAAY,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,GAC1C,KACE,iBAAiB,cAAc,CAAC,MAAM,IAE5C;4CAAE;;;;;;;;;;;;;;;;;;;oBAMX,CAAC,yBACA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;wCAAwB;wCACvB,IAAI,OAAO,kBAAkB;;;;;;;8CAE9C,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD;uCAEe", "debugId": null}}, {"offset": {"line": 5313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/VehicleUtilizationChart.tsx"], "sourcesContent": ["/**\r\n * @file VehicleUtilizationChart.tsx\r\n * @description Vehicle utilization chart following existing chart patterns and SOLID principles\r\n */\r\n\r\nimport React, { useMemo } from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  BarChart,\r\n  Bar,\r\n  XAxis,\r\n  YAxis,\r\n  CartesianGrid,\r\n  Tooltip,\r\n  ResponsiveContainer,\r\n  Cell,\r\n} from 'recharts';\r\nimport { Car, TrendingUp } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { useVehicleAnalytics } from '../../hooks/useVehicleAnalytics';\r\nimport { DataLoader } from '@/components/ui/data-loader';\r\nimport { ErrorDisplay } from '@/components/ui/error-display';\r\nimport type { ReportingFilters } from '../../data/types/reporting';\r\nimport type { VehicleUtilizationData } from '../../data/types/reporting';\r\n\r\n/**\r\n * Props interface for VehicleUtilizationChart\r\n */\r\ninterface VehicleUtilizationChartProps {\r\n  data?: VehicleUtilizationData[];\r\n  filters?: ReportingFilters;\r\n  className?: string;\r\n  showLegend?: boolean;\r\n  interactive?: boolean;\r\n  height?: number;\r\n}\r\n\r\n/**\r\n * Custom tooltip component for the chart\r\n */\r\ninterface CustomTooltipProps {\r\n  active?: boolean;\r\n  payload?: any[];\r\n  label?: string;\r\n}\r\n\r\nconst CustomTooltip: React.FC<CustomTooltipProps> = ({\r\n  active,\r\n  payload,\r\n  label,\r\n}) => {\r\n  if (active && payload && payload.length) {\r\n    const data = payload[0].payload;\r\n    return (\r\n      <div className=\"bg-white p-3 border rounded-lg shadow-lg\">\r\n        <p className=\"font-medium\">{data.vehicleName}</p>\r\n        <p className=\"text-sm text-gray-600\">\r\n          Utilization:{' '}\r\n          <span className=\"font-medium\">{data.utilizationRate}%</span>\r\n        </p>\r\n        <p className=\"text-sm text-gray-600\">\r\n          Active Delegations:{' '}\r\n          <span className=\"font-medium\">{data.activeDelegations}</span>\r\n        </p>\r\n        <p className=\"text-sm text-gray-600\">\r\n          Total Delegations:{' '}\r\n          <span className=\"font-medium\">{data.totalDelegations}</span>\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n  return null;\r\n};\r\n\r\n/**\r\n * Utility function to get color based on utilization rate\r\n */\r\nconst getUtilizationColor = (rate: number): string => {\r\n  if (rate >= 80) return '#ef4444'; // Red - overutilized\r\n  if (rate >= 60) return '#f59e0b'; // Orange - high utilization\r\n  if (rate >= 40) return '#10b981'; // Green - good utilization\r\n  if (rate >= 20) return '#3b82f6'; // Blue - moderate utilization\r\n  return '#6b7280'; // Gray - low utilization\r\n};\r\n\r\n/**\r\n * VehicleUtilizationChart Component\r\n *\r\n * Displays vehicle utilization data in a bar chart format following existing chart patterns.\r\n *\r\n * Responsibilities:\r\n * - Visualize vehicle utilization distribution\r\n * - Follow existing chart component patterns\r\n * - Integrate with existing chart utilities\r\n * - Maintain consistent chart styling\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying utilization data\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on chart framework abstractions\r\n */\r\nexport const VehicleUtilizationChart: React.FC<\r\n  VehicleUtilizationChartProps\r\n> = ({\r\n  data: propData,\r\n  filters,\r\n  className = '',\r\n  showLegend = true,\r\n  interactive = true,\r\n  height = 300,\r\n}) => {\r\n  // Use hook if data not provided via props\r\n  const { data: hookData, isLoading, error } = useVehicleAnalytics(filters);\r\n  const utilizationData = propData || hookData?.utilizationMetrics;\r\n\r\n  // Transform data for chart display\r\n  const chartData = useMemo(() => {\r\n    if (!utilizationData) return [];\r\n\r\n    return utilizationData\r\n      .map(item => ({\r\n        vehicleName: item.vehicleName,\r\n        utilizationRate: Math.round(item.utilizationRate),\r\n        activeDelegations: item.activeDelegations,\r\n        totalDelegations: item.totalDelegations,\r\n        color: getUtilizationColor(item.utilizationRate),\r\n        // Truncate long vehicle names for display\r\n        displayName:\r\n          item.vehicleName.length > 15\r\n            ? `${item.vehicleName.substring(0, 12)}...`\r\n            : item.vehicleName,\r\n      }))\r\n      .sort((a, b) => b.utilizationRate - a.utilizationRate) // Sort by utilization rate\r\n      .slice(0, 10); // Show top 10 vehicles\r\n  }, [utilizationData]);\r\n\r\n  // Handle loading state\r\n  if (isLoading) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Car className=\"h-5 w-5\" />\r\n            Vehicle Utilization\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <DataLoader isLoading={true} data={null} error={null}>\r\n            {() => null}\r\n          </DataLoader>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Handle error state\r\n  if (error) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Car className=\"h-5 w-5\" />\r\n            Vehicle Utilization\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <ErrorDisplay error={error} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Calculate summary stats\r\n  const avgUtilization =\r\n    chartData.length > 0\r\n      ? Math.round(\r\n          chartData.reduce((sum, item) => sum + item.utilizationRate, 0) /\r\n            chartData.length\r\n        )\r\n      : 0;\r\n\r\n  const highUtilizationCount = chartData.filter(\r\n    item => item.utilizationRate >= 80\r\n  ).length;\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader className=\"pb-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Car className=\"h-5 w-5\" />\r\n            Vehicle Utilization\r\n          </CardTitle>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              Avg: {avgUtilization}%\r\n            </Badge>\r\n            {highUtilizationCount > 0 && (\r\n              <Badge variant=\"destructive\" className=\"text-xs\">\r\n                {highUtilizationCount} overutilized\r\n              </Badge>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n\r\n      <CardContent>\r\n        {chartData.length === 0 ? (\r\n          <div className=\"flex items-center justify-center h-64 text-gray-500\">\r\n            <div className=\"text-center\">\r\n              <Car className=\"h-12 w-12 mx-auto mb-2 opacity-50\" />\r\n              <p>No vehicle utilization data available</p>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            <ResponsiveContainer width=\"100%\" height={height}>\r\n              <BarChart\r\n                data={chartData}\r\n                margin={{\r\n                  top: 20,\r\n                  right: 30,\r\n                  left: 20,\r\n                  bottom: 60,\r\n                }}\r\n              >\r\n                <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\r\n                <XAxis\r\n                  dataKey=\"displayName\"\r\n                  angle={-45}\r\n                  textAnchor=\"end\"\r\n                  height={60}\r\n                  fontSize={12}\r\n                />\r\n                <YAxis\r\n                  domain={[0, 100]}\r\n                  tickFormatter={value => `${value}%`}\r\n                  fontSize={12}\r\n                />\r\n                {interactive && <Tooltip content={<CustomTooltip />} />}\r\n                <Bar\r\n                  dataKey=\"utilizationRate\"\r\n                  radius={[4, 4, 0, 0]}\r\n                  name=\"Utilization Rate\"\r\n                >\r\n                  {chartData.map((entry, index) => (\r\n                    <Cell key={`cell-${index}`} fill={entry.color} />\r\n                  ))}\r\n                </Bar>\r\n              </BarChart>\r\n            </ResponsiveContainer>\r\n\r\n            {showLegend && (\r\n              <div className=\"mt-4 flex flex-wrap gap-4 text-xs\">\r\n                <div className=\"flex items-center gap-1\">\r\n                  <div className=\"w-3 h-3 bg-red-500 rounded\"></div>\r\n                  <span>Overutilized (80%+)</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <div className=\"w-3 h-3 bg-orange-500 rounded\"></div>\r\n                  <span>High (60-79%)</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <div className=\"w-3 h-3 bg-green-500 rounded\"></div>\r\n                  <span>Good (40-59%)</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <div className=\"w-3 h-3 bg-blue-500 rounded\"></div>\r\n                  <span>Moderate (20-39%)</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <div className=\"w-3 h-3 bg-gray-500 rounded\"></div>\r\n                  <span>Low (0-19%)</span>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default VehicleUtilizationChart;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAEA;AACA;AAAA;AACA;;;;;;;;;;AAyBA,MAAM,gBAA8C,CAAC,EACnD,MAAM,EACN,OAAO,EACP,KAAK,EACN;IACC,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;QACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;QAC/B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BAAe,KAAK,WAAW;;;;;;8BAC5C,8OAAC;oBAAE,WAAU;;wBAAwB;wBACtB;sCACb,8OAAC;4BAAK,WAAU;;gCAAe,KAAK,eAAe;gCAAC;;;;;;;;;;;;;8BAEtD,8OAAC;oBAAE,WAAU;;wBAAwB;wBACf;sCACpB,8OAAC;4BAAK,WAAU;sCAAe,KAAK,iBAAiB;;;;;;;;;;;;8BAEvD,8OAAC;oBAAE,WAAU;;wBAAwB;wBAChB;sCACnB,8OAAC;4BAAK,WAAU;sCAAe,KAAK,gBAAgB;;;;;;;;;;;;;;;;;;IAI5D;IACA,OAAO;AACT;AAEA;;CAEC,GACD,MAAM,sBAAsB,CAAC;IAC3B,IAAI,QAAQ,IAAI,OAAO,WAAW,qBAAqB;IACvD,IAAI,QAAQ,IAAI,OAAO,WAAW,4BAA4B;IAC9D,IAAI,QAAQ,IAAI,OAAO,WAAW,2BAA2B;IAC7D,IAAI,QAAQ,IAAI,OAAO,WAAW,8BAA8B;IAChE,OAAO,WAAW,yBAAyB;AAC7C;AAkBO,MAAM,0BAET,CAAC,EACH,MAAM,QAAQ,EACd,OAAO,EACP,YAAY,EAAE,EACd,aAAa,IAAI,EACjB,cAAc,IAAI,EAClB,SAAS,GAAG,EACb;IACC,0CAA0C;IAC1C,MAAM,EAAE,MAAM,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE;IACjE,MAAM,kBAAkB,YAAY,UAAU;IAE9C,mCAAmC;IACnC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,IAAI,CAAC,iBAAiB,OAAO,EAAE;QAE/B,OAAO,gBACJ,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,aAAa,KAAK,WAAW;gBAC7B,iBAAiB,KAAK,KAAK,CAAC,KAAK,eAAe;gBAChD,mBAAmB,KAAK,iBAAiB;gBACzC,kBAAkB,KAAK,gBAAgB;gBACvC,OAAO,oBAAoB,KAAK,eAAe;gBAC/C,0CAA0C;gBAC1C,aACE,KAAK,WAAW,CAAC,MAAM,GAAG,KACtB,GAAG,KAAK,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GACzC,KAAK,WAAW;YACxB,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,eAAe,GAAG,EAAE,eAAe,EAAE,2BAA2B;SACjF,KAAK,CAAC,GAAG,KAAK,uBAAuB;IAC1C,GAAG;QAAC;KAAgB;IAEpB,uBAAuB;IACvB,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAI/B,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,aAAU;wBAAC,WAAW;wBAAM,MAAM;wBAAM,OAAO;kCAC7C,IAAM;;;;;;;;;;;;;;;;;IAKjB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAI/B,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,4IAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;;;;;;;;;;;;IAI7B;IAEA,0BAA0B;IAC1B,MAAM,iBACJ,UAAU,MAAM,GAAG,IACf,KAAK,KAAK,CACR,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,eAAe,EAAE,KAC1D,UAAU,MAAM,IAEpB;IAEN,MAAM,uBAAuB,UAAU,MAAM,CAC3C,CAAA,OAAQ,KAAK,eAAe,IAAI,IAChC,MAAM;IAER,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;wCAAU;wCACvC;wCAAe;;;;;;;gCAEtB,uBAAuB,mBACtB,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAc,WAAU;;wCACpC;wCAAqB;;;;;;;;;;;;;;;;;;;;;;;;0BAOhC,8OAAC,gIAAA,CAAA,cAAW;0BACT,UAAU,MAAM,KAAK,kBACpB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC;0CAAE;;;;;;;;;;;;;;;;yCAIP;;sCACE,8OAAC,mKAAA,CAAA,sBAAmB;4BAAC,OAAM;4BAAO,QAAQ;sCACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;gCACP,MAAM;gCACN,QAAQ;oCACN,KAAK;oCACL,OAAO;oCACP,MAAM;oCACN,QAAQ;gCACV;;kDAEA,8OAAC,6JAAA,CAAA,gBAAa;wCAAC,iBAAgB;wCAAM,QAAO;;;;;;kDAC5C,8OAAC,qJAAA,CAAA,QAAK;wCACJ,SAAQ;wCACR,OAAO,CAAC;wCACR,YAAW;wCACX,QAAQ;wCACR,UAAU;;;;;;kDAEZ,8OAAC,qJAAA,CAAA,QAAK;wCACJ,QAAQ;4CAAC;4CAAG;yCAAI;wCAChB,eAAe,CAAA,QAAS,GAAG,MAAM,CAAC,CAAC;wCACnC,UAAU;;;;;;oCAEX,6BAAe,8OAAC,uJAAA,CAAA,UAAO;wCAAC,uBAAS,8OAAC;;;;;;;;;;kDACnC,8OAAC,mJAAA,CAAA,MAAG;wCACF,SAAQ;wCACR,QAAQ;4CAAC;4CAAG;4CAAG;4CAAG;yCAAE;wCACpB,MAAK;kDAEJ,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,8OAAC,oJAAA,CAAA,OAAI;gDAAuB,MAAM,MAAM,KAAK;+CAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;wBAMjC,4BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;uCAEe", "debugId": null}}, {"offset": {"line": 5893, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/VehicleMaintenanceWidget.tsx"], "sourcesContent": ["/**\r\n * @file VehicleMaintenanceWidget.tsx\r\n * @description Vehicle maintenance tracking widget following existing patterns and SOLID principles\r\n */\r\n\r\nimport { addDays, format, isAfter, isBefore } from 'date-fns';\r\nimport {\r\n  AlertTriangle,\r\n  Calendar,\r\n  Car,\r\n  CheckCircle,\r\n  Clock,\r\n  MoreHorizontal,\r\n  Wrench,\r\n} from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { DataLoader } from '@/components/ui/data-loader';\r\nimport { ErrorDisplay } from '@/components/ui/error-display';\r\nimport { cn } from '@/lib/utils';\r\n\r\nimport type { ReportingFilters } from '../../data/types/reporting';\r\nimport type { MaintenanceScheduleData } from '../../data/types/reporting';\r\n\r\nimport { useVehicleAnalytics } from '../../hooks/useVehicleAnalytics';\r\n\r\n/**\r\n * Maintenance item component\r\n */\r\ninterface MaintenanceItemProps {\r\n  compact?: boolean;\r\n  item: MaintenanceScheduleData;\r\n}\r\n\r\n/**\r\n * Props interface for VehicleMaintenanceWidget\r\n */\r\ninterface VehicleMaintenanceWidgetProps {\r\n  className?: string;\r\n  compact?: boolean;\r\n  filters?: ReportingFilters;\r\n  maxItems?: number;\r\n}\r\n\r\nconst MaintenanceItem: React.FC<MaintenanceItemProps> = ({\r\n  compact = false,\r\n  item,\r\n}) => {\r\n  const scheduledDate = new Date(item.nextMaintenanceDate);\r\n  const today = new Date();\r\n  const isOverdue = isBefore(scheduledDate, today);\r\n  const isDueSoon =\r\n    isAfter(scheduledDate, today) && isBefore(scheduledDate, addDays(today, 7));\r\n\r\n  const getStatusIcon = () => {\r\n    // MaintenanceScheduleData doesn't have status, determine from dates\r\n    if (isOverdue) return <AlertTriangle className=\"size-4 text-red-600\" />;\r\n    if (isDueSoon) return <Clock className=\"size-4 text-orange-600\" />;\r\n    return <Calendar className=\"size-4 text-blue-600\" />;\r\n  };\r\n\r\n  const getStatusBadge = () => {\r\n    // MaintenanceScheduleData doesn't have status, determine from dates\r\n    if (isOverdue) return <Badge variant=\"destructive\">Overdue</Badge>;\r\n    if (isDueSoon)\r\n      return (\r\n        <Badge className=\"bg-orange-100 text-orange-800\" variant=\"secondary\">\r\n          Due Soon\r\n        </Badge>\r\n      );\r\n    return <Badge variant=\"outline\">Scheduled</Badge>;\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'flex items-center justify-between p-3 border rounded-lg',\r\n        isOverdue && 'border-red-200 bg-red-50',\r\n        isDueSoon && 'border-orange-200 bg-orange-50'\r\n        // MaintenanceScheduleData doesn't have status, so no completed styling\r\n      )}\r\n    >\r\n      <div className=\"flex items-center space-x-3\">\r\n        {getStatusIcon()}\r\n        <div className=\"flex-1\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <p className=\"text-sm font-medium\">{item.maintenanceType}</p>\r\n            {!compact && getStatusBadge()}\r\n          </div>\r\n          <div className=\"mt-1 flex items-center gap-4 text-xs text-gray-500\">\r\n            <span className=\"flex items-center gap-1\">\r\n              <Car className=\"size-3\" />\r\n              {item.vehicleName}\r\n            </span>\r\n            <span className=\"flex items-center gap-1\">\r\n              <Calendar className=\"size-3\" />\r\n              {format(scheduledDate, 'MMM dd, yyyy')}\r\n            </span>\r\n            {item.estimatedCost && (\r\n              <span>${item.estimatedCost.toLocaleString()}</span>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {compact && <div className=\"text-right\">{getStatusBadge()}</div>}\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * VehicleMaintenanceWidget Component\r\n *\r\n * Displays vehicle maintenance schedule and status following existing widget patterns.\r\n *\r\n * Responsibilities:\r\n * - Display maintenance schedule in widget format\r\n * - Show overdue and upcoming maintenance items\r\n * - Follow established widget composition patterns\r\n * - Maintain consistent styling and behavior\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying maintenance data\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on existing widget framework abstractions\r\n */\r\nexport const VehicleMaintenanceWidget: React.FC<\r\n  VehicleMaintenanceWidgetProps\r\n> = ({ className = '', compact = false, filters, maxItems = 5 }) => {\r\n  // Use existing hook patterns\r\n  const {\r\n    data: vehicleAnalytics,\r\n    error,\r\n    isLoading,\r\n  } = useVehicleAnalytics(filters);\r\n  const maintenanceData = vehicleAnalytics?.maintenanceSchedule || [];\r\n\r\n  // Handle loading state\r\n  if (isLoading) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Wrench className=\"size-5\" />\r\n            Vehicle Maintenance\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <DataLoader data={null} error={null} isLoading={true}>\r\n            {() => null}\r\n          </DataLoader>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Handle error state\r\n  if (error) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Wrench className=\"size-5\" />\r\n            Vehicle Maintenance\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <ErrorDisplay error={error} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Process maintenance data\r\n  const today = new Date();\r\n  const overdueItems = maintenanceData.filter(item =>\r\n    isBefore(new Date(item.nextMaintenanceDate), today)\r\n  );\r\n  const dueSoonItems = maintenanceData.filter(\r\n    item =>\r\n      isAfter(new Date(item.nextMaintenanceDate), today) &&\r\n      isBefore(new Date(item.nextMaintenanceDate), addDays(today, 7))\r\n  );\r\n  // MaintenanceScheduleData doesn't have status, so we can't filter completed items\r\n  const completedItems: MaintenanceScheduleData[] = [];\r\n\r\n  // Sort and limit items for display\r\n  const displayItems = maintenanceData\r\n    .sort((a, b) => {\r\n      // Prioritize overdue, then due soon, then by date\r\n      const aDate = new Date(a.nextMaintenanceDate);\r\n      const bDate = new Date(b.nextMaintenanceDate);\r\n      const aOverdue = isBefore(aDate, today);\r\n      const bOverdue = isBefore(bDate, today);\r\n\r\n      if (aOverdue && !bOverdue) return -1;\r\n      if (!aOverdue && bOverdue) return 1;\r\n\r\n      return aDate.getTime() - bDate.getTime();\r\n    })\r\n    .slice(0, maxItems);\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader className=\"pb-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Wrench className=\"size-5\" />\r\n            Vehicle Maintenance\r\n          </CardTitle>\r\n          <div className=\"flex items-center gap-2\">\r\n            {overdueItems.length > 0 && (\r\n              <Badge className=\"text-xs\" variant=\"destructive\">\r\n                {overdueItems.length} overdue\r\n              </Badge>\r\n            )}\r\n            {dueSoonItems.length > 0 && (\r\n              <Badge\r\n                className=\"bg-orange-100 text-xs text-orange-800\"\r\n                variant=\"secondary\"\r\n              >\r\n                {dueSoonItems.length} due soon\r\n              </Badge>\r\n            )}\r\n            <Button size=\"sm\" variant=\"ghost\">\r\n              <MoreHorizontal className=\"size-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Summary Stats */}\r\n        <div className=\"grid grid-cols-3 gap-4\">\r\n          <div className=\"rounded-lg bg-red-50 p-3 text-center\">\r\n            <div className=\"text-2xl font-bold text-red-700\">\r\n              {overdueItems.length}\r\n            </div>\r\n            <div className=\"text-xs text-red-600\">Overdue</div>\r\n          </div>\r\n          <div className=\"rounded-lg bg-orange-50 p-3 text-center\">\r\n            <div className=\"text-2xl font-bold text-orange-700\">\r\n              {dueSoonItems.length}\r\n            </div>\r\n            <div className=\"text-xs text-orange-600\">Due Soon</div>\r\n          </div>\r\n          <div className=\"rounded-lg bg-green-50 p-3 text-center\">\r\n            <div className=\"text-2xl font-bold text-green-700\">\r\n              {completedItems.length}\r\n            </div>\r\n            <div className=\"text-xs text-green-600\">Completed</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Maintenance Items List */}\r\n        <div className=\"space-y-3\">\r\n          {displayItems.length === 0 ? (\r\n            <div className=\"py-8 text-center text-gray-500\">\r\n              <Wrench className=\"mx-auto mb-2 size-12 opacity-50\" />\r\n              <p>No maintenance items scheduled</p>\r\n            </div>\r\n          ) : (\r\n            displayItems.map((item, index) => (\r\n              <MaintenanceItem\r\n                compact={compact}\r\n                item={item}\r\n                key={`${item.vehicleId}-${item.maintenanceType}-${index}`}\r\n              />\r\n            ))\r\n          )}\r\n        </div>\r\n\r\n        {!compact && maintenanceData.length > maxItems && (\r\n          <div className=\"border-t pt-4\">\r\n            <Button className=\"w-full\" size=\"sm\" variant=\"outline\">\r\n              View All Maintenance ({maintenanceData.length} total)\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default VehicleMaintenanceWidget;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAKA;;;;;;;;;;;AAoBA,MAAM,kBAAkD,CAAC,EACvD,UAAU,KAAK,EACf,IAAI,EACL;IACC,MAAM,gBAAgB,IAAI,KAAK,KAAK,mBAAmB;IACvD,MAAM,QAAQ,IAAI;IAClB,MAAM,YAAY,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,eAAe;IAC1C,MAAM,YACJ,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,eAAe,UAAU,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;IAE1E,MAAM,gBAAgB;QACpB,oEAAoE;QACpE,IAAI,WAAW,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QAC/C,IAAI,WAAW,qBAAO,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvC,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,MAAM,iBAAiB;QACrB,oEAAoE;QACpE,IAAI,WAAW,qBAAO,8OAAC,iIAAA,CAAA,QAAK;YAAC,SAAQ;sBAAc;;;;;;QACnD,IAAI,WACF,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,WAAU;YAAgC,SAAQ;sBAAY;;;;;;QAIzE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;YAAC,SAAQ;sBAAU;;;;;;IAClC;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2DACA,aAAa,4BACb,aAAa;;0BAIf,8OAAC;gBAAI,WAAU;;oBACZ;kCACD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAuB,KAAK,eAAe;;;;;;oCACvD,CAAC,WAAW;;;;;;;0CAEf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;0DACd,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CACd,KAAK,WAAW;;;;;;;kDAEnB,8OAAC;wCAAK,WAAU;;0DACd,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;;;;;;;oCAExB,KAAK,aAAa,kBACjB,8OAAC;;4CAAK;4CAAE,KAAK,aAAa,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;YAMhD,yBAAW,8OAAC;gBAAI,WAAU;0BAAc;;;;;;;;;;;;AAG/C;AAkBO,MAAM,2BAET,CAAC,EAAE,YAAY,EAAE,EAAE,UAAU,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC,EAAE;IAC7D,6BAA6B;IAC7B,MAAM,EACJ,MAAM,gBAAgB,EACtB,KAAK,EACL,SAAS,EACV,GAAG,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE;IACxB,MAAM,kBAAkB,kBAAkB,uBAAuB,EAAE;IAEnE,uBAAuB;IACvB,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAW;;;;;;;;;;;;8BAIjC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,aAAU;wBAAC,MAAM;wBAAM,OAAO;wBAAM,WAAW;kCAC7C,IAAM;;;;;;;;;;;;;;;;;IAKjB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAW;;;;;;;;;;;;8BAIjC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,4IAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;;;;;;;;;;;;IAI7B;IAEA,2BAA2B;IAC3B,MAAM,QAAQ,IAAI;IAClB,MAAM,eAAe,gBAAgB,MAAM,CAAC,CAAA,OAC1C,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,KAAK,KAAK,mBAAmB,GAAG;IAE/C,MAAM,eAAe,gBAAgB,MAAM,CACzC,CAAA,OACE,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,KAAK,KAAK,mBAAmB,GAAG,UAC5C,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,KAAK,KAAK,mBAAmB,GAAG,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;IAEhE,kFAAkF;IAClF,MAAM,iBAA4C,EAAE;IAEpD,mCAAmC;IACnC,MAAM,eAAe,gBAClB,IAAI,CAAC,CAAC,GAAG;QACR,kDAAkD;QAClD,MAAM,QAAQ,IAAI,KAAK,EAAE,mBAAmB;QAC5C,MAAM,QAAQ,IAAI,KAAK,EAAE,mBAAmB;QAC5C,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;QACjC,MAAM,WAAW,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;QAEjC,IAAI,YAAY,CAAC,UAAU,OAAO,CAAC;QACnC,IAAI,CAAC,YAAY,UAAU,OAAO;QAElC,OAAO,MAAM,OAAO,KAAK,MAAM,OAAO;IACxC,GACC,KAAK,CAAC,GAAG;IAEZ,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAW;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;;gCACZ,aAAa,MAAM,GAAG,mBACrB,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;oCAAU,SAAQ;;wCAChC,aAAa,MAAM;wCAAC;;;;;;;gCAGxB,aAAa,MAAM,GAAG,mBACrB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,WAAU;oCACV,SAAQ;;wCAEP,aAAa,MAAM;wCAAC;;;;;;;8CAGzB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;8CACxB,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,aAAa,MAAM;;;;;;kDAEtB,8OAAC;wCAAI,WAAU;kDAAuB;;;;;;;;;;;;0CAExC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,aAAa,MAAM;;;;;;kDAEtB,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;;;;;;;0CAE3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,eAAe,MAAM;;;;;;kDAExB,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAK5C,8OAAC;wBAAI,WAAU;kCACZ,aAAa,MAAM,KAAK,kBACvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;8CAAE;;;;;;;;;;;mCAGL,aAAa,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;gCACC,SAAS;gCACT,MAAM;+BACD,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,eAAe,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;oBAMhE,CAAC,WAAW,gBAAgB,MAAM,GAAG,0BACpC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,WAAU;4BAAS,MAAK;4BAAK,SAAQ;;gCAAU;gCAC9B,gBAAgB,MAAM;gCAAC;;;;;;;;;;;;;;;;;;;;;;;;AAO5D;uCAEe", "debugId": null}}, {"offset": {"line": 6464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/VehicleCostAnalyticsWidget.tsx"], "sourcesContent": ["/**\r\n * @file VehicleCostAnalyticsWidget.tsx\r\n * @description Vehicle cost analytics widget following existing patterns and SOLID principles\r\n */\r\n\r\nimport React, { useMemo } from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport {\r\n  DollarSign,\r\n  TrendingUp,\r\n  TrendingDown,\r\n  AlertTriangle,\r\n  PieChart,\r\n  BarChart3,\r\n  MoreHorizontal,\r\n} from 'lucide-react';\r\nimport {\r\n  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart,\r\n  Pie,\r\n  Cell,\r\n  ResponsiveContainer,\r\n  Tooltip,\r\n  LineChart,\r\n  Line,\r\n  XAxis,\r\n  YAxis,\r\n  CartesianGrid,\r\n} from 'recharts';\r\nimport { cn } from '@/lib/utils';\r\nimport { format } from 'date-fns';\r\nimport { useVehicleAnalytics } from '../../hooks/useVehicleAnalytics';\r\nimport { DataLoader } from '@/components/ui/data-loader';\r\nimport { ErrorDisplay } from '@/components/ui/error-display';\r\nimport type { ReportingFilters } from '../../data/types/reporting';\r\n\r\n/**\r\n * Props interface for VehicleCostAnalyticsWidget\r\n */\r\ninterface VehicleCostAnalyticsWidgetProps {\r\n  filters?: ReportingFilters;\r\n  className?: string;\r\n  showTrend?: boolean;\r\n  compact?: boolean;\r\n}\r\n\r\n/**\r\n * Cost metric card component\r\n */\r\ninterface CostMetricCardProps {\r\n  label: string;\r\n  value: string;\r\n  icon: React.ReactNode;\r\n  trend?: {\r\n    value: number;\r\n    isPositive: boolean;\r\n  };\r\n  variant?: 'default' | 'warning' | 'success' | 'destructive';\r\n}\r\n\r\nconst CostMetricCard: React.FC<CostMetricCardProps> = ({\r\n  label,\r\n  value,\r\n  icon,\r\n  trend,\r\n  variant = 'default',\r\n}) => {\r\n  const variantStyles = {\r\n    default: 'border-gray-200',\r\n    warning: 'border-orange-200 bg-orange-50',\r\n    success: 'border-green-200 bg-green-50',\r\n    destructive: 'border-red-200 bg-red-50',\r\n  };\r\n\r\n  return (\r\n    <div className={cn('p-4 border rounded-lg', variantStyles[variant])}>\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          {icon}\r\n          <span className=\"text-sm font-medium text-gray-600\">{label}</span>\r\n        </div>\r\n        {trend && (\r\n          <div className=\"flex items-center gap-1\">\r\n            {trend.isPositive ? (\r\n              <TrendingUp className=\"h-3 w-3 text-green-600\" />\r\n            ) : (\r\n              <TrendingDown className=\"h-3 w-3 text-red-600\" />\r\n            )}\r\n            <Badge\r\n              variant={trend.isPositive ? 'default' : 'destructive'}\r\n              className=\"text-xs\"\r\n            >\r\n              {trend.isPositive ? '+' : ''}\r\n              {trend.value}%\r\n            </Badge>\r\n          </div>\r\n        )}\r\n      </div>\r\n      <div className=\"mt-2\">\r\n        <span className=\"text-2xl font-bold\">{value}</span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * Custom tooltip for pie chart\r\n */\r\nconst CustomPieTooltip = ({ active, payload }: any) => {\r\n  if (active && payload && payload.length) {\r\n    const data = payload[0].payload;\r\n    return (\r\n      <div className=\"bg-white p-3 border rounded-lg shadow-lg\">\r\n        <p className=\"font-medium\">{data.name}</p>\r\n        <p className=\"text-sm text-gray-600\">\r\n          Cost:{' '}\r\n          <span className=\"font-medium\">${data.value.toLocaleString()}</span>\r\n        </p>\r\n        <p className=\"text-sm text-gray-600\">\r\n          Percentage: <span className=\"font-medium\">{data.percentage}%</span>\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n  return null;\r\n};\r\n\r\n/**\r\n * VehicleCostAnalyticsWidget Component\r\n *\r\n * Displays vehicle cost analytics and trends following existing widget patterns.\r\n *\r\n * Responsibilities:\r\n * - Display cost analytics in widget format\r\n * - Show cost breakdown and trends\r\n * - Follow established widget composition patterns\r\n * - Maintain consistent styling and behavior\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying cost analytics\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on existing widget framework abstractions\r\n */\r\nexport const VehicleCostAnalyticsWidget: React.FC<\r\n  VehicleCostAnalyticsWidgetProps\r\n> = ({ filters, className = '', showTrend = true, compact = false }) => {\r\n  // Use existing hook patterns\r\n  const {\r\n    data: vehicleAnalytics,\r\n    isLoading,\r\n    error,\r\n  } = useVehicleAnalytics(filters);\r\n  const costAnalysis = vehicleAnalytics?.costAnalysis;\r\n\r\n  // Transform data for charts\r\n  const pieChartData = useMemo(() => {\r\n    if (!costAnalysis?.costByType) return [];\r\n\r\n    const colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6'];\r\n\r\n    return costAnalysis.costByType.map((item, index) => ({\r\n      name: item.type,\r\n      value: item.cost,\r\n      percentage: Math.round((item.cost / costAnalysis.totalCost) * 100),\r\n      color: colors[index % colors.length],\r\n    }));\r\n  }, [costAnalysis]);\r\n\r\n  const trendData = useMemo(() => {\r\n    if (!costAnalysis?.monthlyTrend) return [];\r\n\r\n    return costAnalysis.monthlyTrend.map((item, index) => ({\r\n      month: format(new Date(item.month), 'MMM'),\r\n      cost: item.cost,\r\n      services: index + 1, // Placeholder since serviceCount doesn't exist\r\n    }));\r\n  }, [costAnalysis]);\r\n\r\n  // Handle loading state\r\n  if (isLoading) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <DollarSign className=\"h-5 w-5\" />\r\n            Cost Analytics\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <DataLoader isLoading={true} data={null} error={null}>\r\n            {() => null}\r\n          </DataLoader>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Handle error state\r\n  if (error) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <DollarSign className=\"h-5 w-5\" />\r\n            Cost Analytics\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <ErrorDisplay error={error} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Calculate derived metrics\r\n  const totalCost = costAnalysis?.totalCost || 0;\r\n  const avgCostPerService = costAnalysis?.averageCostPerService || 0;\r\n  // These properties don't exist in ServiceCostSummary, so we'll calculate them\r\n  const budgetUtilization =\r\n    totalCost > 0 ? Math.min((totalCost / 10000) * 100, 100) : 0; // Assuming 10k budget\r\n  const projectedAnnualCost = totalCost * 12; // Simple projection\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader className=\"pb-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <DollarSign className=\"h-5 w-5\" />\r\n            Cost Analytics\r\n          </CardTitle>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              ${totalCost.toLocaleString()} total\r\n            </Badge>\r\n            {budgetUtilization > 90 && (\r\n              <Badge variant=\"destructive\" className=\"text-xs\">\r\n                Budget Alert\r\n              </Badge>\r\n            )}\r\n            <Button variant=\"ghost\" size=\"sm\">\r\n              <MoreHorizontal className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-6\">\r\n        {/* Key Metrics Grid */}\r\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n          <CostMetricCard\r\n            label=\"Total Cost\"\r\n            value={`$${totalCost.toLocaleString()}`}\r\n            icon={<DollarSign className=\"h-4 w-4\" />}\r\n          />\r\n          <CostMetricCard\r\n            label=\"Avg/Service\"\r\n            value={`$${Math.round(avgCostPerService)}`}\r\n            icon={<BarChart3 className=\"h-4 w-4\" />}\r\n          />\r\n          <CostMetricCard\r\n            label=\"Budget Used\"\r\n            value={`${Math.round(budgetUtilization)}%`}\r\n            icon={<PieChart className=\"h-4 w-4\" />}\r\n            variant={\r\n              budgetUtilization > 90\r\n                ? 'destructive'\r\n                : budgetUtilization > 75\r\n                  ? 'warning'\r\n                  : 'success'\r\n            }\r\n          />\r\n          <CostMetricCard\r\n            label=\"Projected Annual\"\r\n            value={`$${Math.round(projectedAnnualCost).toLocaleString()}`}\r\n            icon={<TrendingUp className=\"h-4 w-4\" />}\r\n          />\r\n        </div>\r\n\r\n        {!compact && (\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n            {/* Cost Breakdown Pie Chart */}\r\n            <div>\r\n              <h4 className=\"text-sm font-medium mb-3\">Cost by Service Type</h4>\r\n              {pieChartData.length > 0 ? (\r\n                <ResponsiveContainer width=\"100%\" height={200}>\r\n                  <RechartsPieChart>\r\n                    <Pie\r\n                      data={pieChartData}\r\n                      cx=\"50%\"\r\n                      cy=\"50%\"\r\n                      outerRadius={80}\r\n                      dataKey=\"value\"\r\n                      label={({ name, percentage }) =>\r\n                        `${name}: ${percentage}%`\r\n                      }\r\n                      labelLine={false}\r\n                    >\r\n                      {pieChartData.map((entry, index) => (\r\n                        <Cell key={`cell-${index}`} fill={entry.color} />\r\n                      ))}\r\n                    </Pie>\r\n                    <Tooltip content={<CustomPieTooltip />} />\r\n                  </RechartsPieChart>\r\n                </ResponsiveContainer>\r\n              ) : (\r\n                <div className=\"h-48 flex items-center justify-center text-gray-500\">\r\n                  No cost breakdown data available\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Cost Trend Chart */}\r\n            {showTrend && (\r\n              <div>\r\n                <h4 className=\"text-sm font-medium mb-3\">Monthly Cost Trend</h4>\r\n                {trendData.length > 0 ? (\r\n                  <ResponsiveContainer width=\"100%\" height={200}>\r\n                    <LineChart data={trendData}>\r\n                      <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f0f0f0\" />\r\n                      <XAxis dataKey=\"month\" fontSize={12} />\r\n                      <YAxis\r\n                        tickFormatter={value => `$${value}`}\r\n                        fontSize={12}\r\n                      />\r\n                      <Tooltip\r\n                        formatter={(value: number) => [\r\n                          `$${value.toLocaleString()}`,\r\n                          'Cost',\r\n                        ]}\r\n                        labelFormatter={label => `Month: ${label}`}\r\n                      />\r\n                      <Line\r\n                        type=\"monotone\"\r\n                        dataKey=\"cost\"\r\n                        stroke=\"#3b82f6\"\r\n                        strokeWidth={2}\r\n                        dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}\r\n                      />\r\n                    </LineChart>\r\n                  </ResponsiveContainer>\r\n                ) : (\r\n                  <div className=\"h-48 flex items-center justify-center text-gray-500\">\r\n                    No trend data available\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Budget Alert */}\r\n        {budgetUtilization > 90 && (\r\n          <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <AlertTriangle className=\"h-5 w-5 text-red-600\" />\r\n              <span className=\"font-medium text-red-800\">Budget Alert</span>\r\n            </div>\r\n            <p className=\"text-sm text-red-700 mt-1\">\r\n              You've used {Math.round(budgetUtilization)}% of your maintenance\r\n              budget. Consider reviewing upcoming expenses.\r\n            </p>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default VehicleCostAnalyticsWidget;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAAA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;;;;;AA2BA,MAAM,iBAAgD,CAAC,EACrD,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,UAAU,SAAS,EACpB;IACC,MAAM,gBAAgB;QACpB,SAAS;QACT,SAAS;QACT,SAAS;QACT,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,aAAa,CAAC,QAAQ;;0BAChE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ;0CACD,8OAAC;gCAAK,WAAU;0CAAqC;;;;;;;;;;;;oBAEtD,uBACC,8OAAC;wBAAI,WAAU;;4BACZ,MAAM,UAAU,iBACf,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;qDAEtB,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CAE1B,8OAAC,iIAAA,CAAA,QAAK;gCACJ,SAAS,MAAM,UAAU,GAAG,YAAY;gCACxC,WAAU;;oCAET,MAAM,UAAU,GAAG,MAAM;oCACzB,MAAM,KAAK;oCAAC;;;;;;;;;;;;;;;;;;;0BAKrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BAAsB;;;;;;;;;;;;;;;;;AAI9C;AAEA;;CAEC,GACD,MAAM,mBAAmB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAO;IAChD,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;QACvC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;QAC/B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BAAe,KAAK,IAAI;;;;;;8BACrC,8OAAC;oBAAE,WAAU;;wBAAwB;wBAC7B;sCACN,8OAAC;4BAAK,WAAU;;gCAAc;gCAAE,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;8BAE3D,8OAAC;oBAAE,WAAU;;wBAAwB;sCACvB,8OAAC;4BAAK,WAAU;;gCAAe,KAAK,UAAU;gCAAC;;;;;;;;;;;;;;;;;;;IAInE;IACA,OAAO;AACT;AAkBO,MAAM,6BAET,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,YAAY,IAAI,EAAE,UAAU,KAAK,EAAE;IACjE,6BAA6B;IAC7B,MAAM,EACJ,MAAM,gBAAgB,EACtB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE;IACxB,MAAM,eAAe,kBAAkB;IAEvC,4BAA4B;IAC5B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,IAAI,CAAC,cAAc,YAAY,OAAO,EAAE;QAExC,MAAM,SAAS;YAAC;YAAW;YAAW;YAAW;YAAW;SAAU;QAEtE,OAAO,aAAa,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBACnD,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,IAAI;gBAChB,YAAY,KAAK,KAAK,CAAC,AAAC,KAAK,IAAI,GAAG,aAAa,SAAS,GAAI;gBAC9D,OAAO,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;YACtC,CAAC;IACH,GAAG;QAAC;KAAa;IAEjB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,IAAI,CAAC,cAAc,cAAc,OAAO,EAAE;QAE1C,OAAO,aAAa,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBACrD,OAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,KAAK,GAAG;gBACpC,MAAM,KAAK,IAAI;gBACf,UAAU,QAAQ;YACpB,CAAC;IACH,GAAG;QAAC;KAAa;IAEjB,uBAAuB;IACvB,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAItC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,aAAU;wBAAC,WAAW;wBAAM,MAAM;wBAAM,OAAO;kCAC7C,IAAM;;;;;;;;;;;;;;;;;IAKjB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAItC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,4IAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;;;;;;;;;;;;IAI7B;IAEA,4BAA4B;IAC5B,MAAM,YAAY,cAAc,aAAa;IAC7C,MAAM,oBAAoB,cAAc,yBAAyB;IACjE,8EAA8E;IAC9E,MAAM,oBACJ,YAAY,IAAI,KAAK,GAAG,CAAC,AAAC,YAAY,QAAS,KAAK,OAAO,GAAG,sBAAsB;IACtF,MAAM,sBAAsB,YAAY,IAAI,oBAAoB;IAEhE,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGpC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;wCAAU;wCAC3C,UAAU,cAAc;wCAAG;;;;;;;gCAE9B,oBAAoB,oBACnB,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAc,WAAU;8CAAU;;;;;;8CAInD,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAM;gCACN,OAAO,CAAC,CAAC,EAAE,UAAU,cAAc,IAAI;gCACvC,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;0CAE9B,8OAAC;gCACC,OAAM;gCACN,OAAO,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,oBAAoB;gCAC1C,oBAAM,8OAAC,kNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAE7B,8OAAC;gCACC,OAAM;gCACN,OAAO,GAAG,KAAK,KAAK,CAAC,mBAAmB,CAAC,CAAC;gCAC1C,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAC1B,SACE,oBAAoB,KAChB,gBACA,oBAAoB,KAClB,YACA;;;;;;0CAGV,8OAAC;gCACC,OAAM;gCACN,OAAO,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,qBAAqB,cAAc,IAAI;gCAC7D,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAI/B,CAAC,yBACA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2B;;;;;;oCACxC,aAAa,MAAM,GAAG,kBACrB,8OAAC,mKAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAO,QAAQ;kDACxC,cAAA,8OAAC,oJAAA,CAAA,WAAgB;;8DACf,8OAAC,+IAAA,CAAA,MAAG;oDACF,MAAM;oDACN,IAAG;oDACH,IAAG;oDACH,aAAa;oDACb,SAAQ;oDACR,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,GAC1B,GAAG,KAAK,EAAE,EAAE,WAAW,CAAC,CAAC;oDAE3B,WAAW;8DAEV,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC,oJAAA,CAAA,OAAI;4DAAuB,MAAM,MAAM,KAAK;2DAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;8DAG9B,8OAAC,uJAAA,CAAA,UAAO;oDAAC,uBAAS,8OAAC;;;;;;;;;;;;;;;;;;;;6DAIvB,8OAAC;wCAAI,WAAU;kDAAsD;;;;;;;;;;;;4BAOxE,2BACC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2B;;;;;;oCACxC,UAAU,MAAM,GAAG,kBAClB,8OAAC,mKAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAO,QAAQ;kDACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;4CAAC,MAAM;;8DACf,8OAAC,6JAAA,CAAA,gBAAa;oDAAC,iBAAgB;oDAAM,QAAO;;;;;;8DAC5C,8OAAC,qJAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAQ,UAAU;;;;;;8DACjC,8OAAC,qJAAA,CAAA,QAAK;oDACJ,eAAe,CAAA,QAAS,CAAC,CAAC,EAAE,OAAO;oDACnC,UAAU;;;;;;8DAEZ,8OAAC,uJAAA,CAAA,UAAO;oDACN,WAAW,CAAC,QAAkB;4DAC5B,CAAC,CAAC,EAAE,MAAM,cAAc,IAAI;4DAC5B;yDACD;oDACD,gBAAgB,CAAA,QAAS,CAAC,OAAO,EAAE,OAAO;;;;;;8DAE5C,8OAAC,oJAAA,CAAA,OAAI;oDACH,MAAK;oDACL,SAAQ;oDACR,QAAO;oDACP,aAAa;oDACb,KAAK;wDAAE,MAAM;wDAAW,aAAa;wDAAG,GAAG;oDAAE;;;;;;;;;;;;;;;;6DAKnD,8OAAC;wCAAI,WAAU;kDAAsD;;;;;;;;;;;;;;;;;;oBAU9E,oBAAoB,oBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;0CAE7C,8OAAC;gCAAE,WAAU;;oCAA4B;oCAC1B,KAAK,KAAK,CAAC;oCAAmB;;;;;;;;;;;;;;;;;;;;;;;;;AAQzD;uCAEe", "debugId": null}}, {"offset": {"line": 7201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/EmployeeAnalyticsWidget.tsx"], "sourcesContent": ["/**\r\n * @file EmployeeAnalyticsWidget.tsx\r\n * @description Employee analytics widget following existing patterns and SOLID principles\r\n */\r\n\r\nimport React from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Users,\r\n  TrendingUp,\r\n  AlertTriangle,\r\n  Clock,\r\n  CheckCircle,\r\n  UserCheck,\r\n  Download,\r\n  MoreHorizontal,\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { useEmployeeAnalytics } from '../../hooks/useEmployeeAnalytics';\r\nimport { DataLoader } from '@/components/ui/data-loader';\r\nimport { ErrorDisplay } from '@/components/ui/error-display';\r\nimport type { ReportingFilters } from '../../data/types/reporting';\r\n\r\n/**\r\n * Props interface for EmployeeAnalyticsWidget\r\n */\r\ninterface EmployeeAnalyticsWidgetProps {\r\n  filters?: ReportingFilters;\r\n  className?: string;\r\n  showExportOptions?: boolean;\r\n  compact?: boolean;\r\n}\r\n\r\n/**\r\n * Metric card component for displaying individual metrics\r\n */\r\ninterface MetricCardProps {\r\n  label: string;\r\n  value: string | number;\r\n  icon: React.ReactNode;\r\n  trend?: {\r\n    value: number;\r\n    isPositive: boolean;\r\n  };\r\n  variant?: 'default' | 'warning' | 'success' | 'destructive';\r\n}\r\n\r\nconst MetricCard: React.FC<MetricCardProps> = ({\r\n  label,\r\n  value,\r\n  icon,\r\n  trend,\r\n  variant = 'default',\r\n}) => {\r\n  const variantStyles = {\r\n    default: 'border-gray-200',\r\n    warning: 'border-orange-200 bg-orange-50',\r\n    success: 'border-green-200 bg-green-50',\r\n    destructive: 'border-red-200 bg-red-50',\r\n  };\r\n\r\n  return (\r\n    <div className={cn('p-4 border rounded-lg', variantStyles[variant])}>\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          {icon}\r\n          <span className=\"text-sm font-medium text-gray-600\">{label}</span>\r\n        </div>\r\n        {trend && (\r\n          <Badge\r\n            variant={trend.isPositive ? 'default' : 'destructive'}\r\n            className=\"text-xs\"\r\n          >\r\n            {trend.isPositive ? '+' : ''}\r\n            {trend.value}%\r\n          </Badge>\r\n        )}\r\n      </div>\r\n      <div className=\"mt-2\">\r\n        <span className=\"text-2xl font-bold\">{value}</span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * EmployeeAnalyticsWidget Component\r\n *\r\n * Displays comprehensive employee analytics following existing widget patterns.\r\n *\r\n * Responsibilities:\r\n * - Display employee analytics in widget format\r\n * - Integrate with existing dashboard layout\r\n * - Follow established widget composition patterns\r\n * - Maintain consistent styling and behavior\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying employee metrics\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on existing widget framework abstractions\r\n */\r\nexport const EmployeeAnalyticsWidget: React.FC<\r\n  EmployeeAnalyticsWidgetProps\r\n> = ({\r\n  filters,\r\n  className = '',\r\n  showExportOptions = true,\r\n  compact = false,\r\n}) => {\r\n  // Use existing hook patterns\r\n  const {\r\n    data: employeeAnalytics,\r\n    isLoading,\r\n    error,\r\n  } = useEmployeeAnalytics(filters);\r\n\r\n  // Handle loading state\r\n  if (isLoading) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Users className=\"h-5 w-5\" />\r\n            Employee Analytics\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <DataLoader isLoading={true} data={null} error={null}>\r\n            {() => null}\r\n          </DataLoader>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Handle error state\r\n  if (error) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Users className=\"h-5 w-5\" />\r\n            Employee Analytics\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <ErrorDisplay error={error} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Calculate derived metrics\r\n  const totalEmployees = employeeAnalytics?.totalCount || 0;\r\n  const activeEmployees =\r\n    employeeAnalytics?.performanceMetrics?.filter(\r\n      emp => emp.completedDelegations > 0 || emp.completedTasks > 0\r\n    ).length || 0;\r\n\r\n  const avgPerformanceScore =\r\n    employeeAnalytics?.performanceMetrics?.length &&\r\n    employeeAnalytics.performanceMetrics.length > 0\r\n      ? Math.round(\r\n          employeeAnalytics.performanceMetrics.reduce(\r\n            (sum, emp) => sum + emp.averageRating,\r\n            0\r\n          ) / employeeAnalytics.performanceMetrics.length\r\n        )\r\n      : 0;\r\n\r\n  const totalCompletedTasks =\r\n    employeeAnalytics?.taskAssignments?.reduce(\r\n      (sum, emp) => sum + emp.completedTasks,\r\n      0\r\n    ) || 0;\r\n\r\n  const totalOverdueTasks =\r\n    employeeAnalytics?.taskAssignments?.reduce(\r\n      (sum, emp) => sum + emp.overdueTasksCount,\r\n      0\r\n    ) || 0;\r\n\r\n  const avgUtilization =\r\n    employeeAnalytics?.availabilityMetrics?.length &&\r\n    employeeAnalytics.availabilityMetrics.length > 0\r\n      ? Math.round(\r\n          employeeAnalytics.availabilityMetrics.reduce(\r\n            (sum, emp) => sum + emp.utilizationRate,\r\n            0\r\n          ) / employeeAnalytics.availabilityMetrics.length\r\n        )\r\n      : 0;\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader className=\"pb-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Users className=\"h-5 w-5\" />\r\n            Employee Analytics\r\n          </CardTitle>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              {totalEmployees} employees\r\n            </Badge>\r\n            {totalOverdueTasks > 0 && (\r\n              <Badge variant=\"destructive\" className=\"text-xs\">\r\n                {totalOverdueTasks} overdue\r\n              </Badge>\r\n            )}\r\n            {showExportOptions && (\r\n              <Button variant=\"ghost\" size=\"sm\">\r\n                <Download className=\"h-4 w-4\" />\r\n              </Button>\r\n            )}\r\n            <Button variant=\"ghost\" size=\"sm\">\r\n              <MoreHorizontal className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-6\">\r\n        {/* Key Metrics Grid - Following existing patterns */}\r\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n          <MetricCard\r\n            label=\"Total Employees\"\r\n            value={totalEmployees}\r\n            icon={<Users className=\"h-4 w-4\" />}\r\n          />\r\n          <MetricCard\r\n            label=\"Active Employees\"\r\n            value={activeEmployees}\r\n            icon={<UserCheck className=\"h-4 w-4\" />}\r\n            variant={\r\n              activeEmployees < totalEmployees * 0.8 ? 'warning' : 'success'\r\n            }\r\n          />\r\n          <MetricCard\r\n            label=\"Avg Performance\"\r\n            value={`${avgPerformanceScore}/10`}\r\n            icon={<TrendingUp className=\"h-4 w-4\" />}\r\n            variant={avgPerformanceScore < 7 ? 'warning' : 'success'}\r\n          />\r\n          <MetricCard\r\n            label=\"Utilization Rate\"\r\n            value={`${avgUtilization}%`}\r\n            icon={<Clock className=\"h-4 w-4\" />}\r\n            variant={\r\n              avgUtilization < 70\r\n                ? 'warning'\r\n                : avgUtilization > 90\r\n                  ? 'destructive'\r\n                  : 'success'\r\n            }\r\n          />\r\n        </div>\r\n\r\n        {/* Quick Stats */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm font-medium text-blue-700\">\r\n                Completed Tasks\r\n              </span>\r\n              <CheckCircle className=\"h-4 w-4 text-blue-600\" />\r\n            </div>\r\n            <span className=\"text-xl font-bold text-blue-900\">\r\n              {totalCompletedTasks}\r\n            </span>\r\n          </div>\r\n\r\n          <div className=\"p-4 bg-red-50 rounded-lg\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm font-medium text-red-700\">\r\n                Overdue Tasks\r\n              </span>\r\n              <AlertTriangle className=\"h-4 w-4 text-red-600\" />\r\n            </div>\r\n            <span className=\"text-xl font-bold text-red-900\">\r\n              {totalOverdueTasks}\r\n            </span>\r\n          </div>\r\n\r\n          <div className=\"p-4 bg-green-50 rounded-lg\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm font-medium text-green-700\">\r\n                On-Time Rate\r\n              </span>\r\n              <Clock className=\"h-4 w-4 text-green-600\" />\r\n            </div>\r\n            <span className=\"text-xl font-bold text-green-900\">\r\n              {employeeAnalytics?.performanceMetrics?.length &&\r\n              employeeAnalytics.performanceMetrics.length > 0\r\n                ? Math.round(\r\n                    employeeAnalytics.performanceMetrics.reduce(\r\n                      (sum, emp) => sum + emp.onTimePerformance,\r\n                      0\r\n                    ) / employeeAnalytics.performanceMetrics.length\r\n                  )\r\n                : 0}\r\n              %\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Workload Distribution Summary */}\r\n        {!compact && employeeAnalytics?.workloadDistribution && (\r\n          <div className=\"space-y-3\">\r\n            <h4 className=\"text-sm font-medium\">Workload Distribution</h4>\r\n            <div className=\"grid grid-cols-3 gap-4 text-center\">\r\n              <div className=\"p-3 bg-green-50 rounded-lg\">\r\n                <div className=\"text-lg font-bold text-green-700\">\r\n                  {\r\n                    employeeAnalytics.workloadDistribution.filter(\r\n                      emp => emp.status === 'Underutilized'\r\n                    ).length\r\n                  }\r\n                </div>\r\n                <div className=\"text-xs text-green-600\">Underutilized</div>\r\n              </div>\r\n              <div className=\"p-3 bg-blue-50 rounded-lg\">\r\n                <div className=\"text-lg font-bold text-blue-700\">\r\n                  {\r\n                    employeeAnalytics.workloadDistribution.filter(\r\n                      emp => emp.status === 'Optimal'\r\n                    ).length\r\n                  }\r\n                </div>\r\n                <div className=\"text-xs text-blue-600\">Optimal</div>\r\n              </div>\r\n              <div className=\"p-3 bg-red-50 rounded-lg\">\r\n                <div className=\"text-lg font-bold text-red-700\">\r\n                  {\r\n                    employeeAnalytics.workloadDistribution.filter(\r\n                      emp => emp.status === 'Overloaded'\r\n                    ).length\r\n                  }\r\n                </div>\r\n                <div className=\"text-xs text-red-600\">Overloaded</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {!compact && (\r\n          <div className=\"pt-4 border-t\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm text-gray-600\">\r\n                Last updated: {new Date().toLocaleTimeString()}\r\n              </span>\r\n              <Button variant=\"outline\" size=\"sm\">\r\n                View Details\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default EmployeeAnalyticsWidget;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAAA;AACA;AACA;AAAA;AACA;;;;;;;;;;AA2BA,MAAM,aAAwC,CAAC,EAC7C,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,UAAU,SAAS,EACpB;IACC,MAAM,gBAAgB;QACpB,SAAS;QACT,SAAS;QACT,SAAS;QACT,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,aAAa,CAAC,QAAQ;;0BAChE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ;0CACD,8OAAC;gCAAK,WAAU;0CAAqC;;;;;;;;;;;;oBAEtD,uBACC,8OAAC,iIAAA,CAAA,QAAK;wBACJ,SAAS,MAAM,UAAU,GAAG,YAAY;wBACxC,WAAU;;4BAET,MAAM,UAAU,GAAG,MAAM;4BACzB,MAAM,KAAK;4BAAC;;;;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BAAsB;;;;;;;;;;;;;;;;;AAI9C;AAkBO,MAAM,0BAET,CAAC,EACH,OAAO,EACP,YAAY,EAAE,EACd,oBAAoB,IAAI,EACxB,UAAU,KAAK,EAChB;IACC,6BAA6B;IAC7B,MAAM,EACJ,MAAM,iBAAiB,EACvB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,2KAAA,CAAA,uBAAoB,AAAD,EAAE;IAEzB,uBAAuB;IACvB,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIjC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,aAAU;wBAAC,WAAW;wBAAM,MAAM;wBAAM,OAAO;kCAC7C,IAAM;;;;;;;;;;;;;;;;;IAKjB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIjC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,4IAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;;;;;;;;;;;;IAI7B;IAEA,4BAA4B;IAC5B,MAAM,iBAAiB,mBAAmB,cAAc;IACxD,MAAM,kBACJ,mBAAmB,oBAAoB,OACrC,CAAA,MAAO,IAAI,oBAAoB,GAAG,KAAK,IAAI,cAAc,GAAG,GAC5D,UAAU;IAEd,MAAM,sBACJ,mBAAmB,oBAAoB,UACvC,kBAAkB,kBAAkB,CAAC,MAAM,GAAG,IAC1C,KAAK,KAAK,CACR,kBAAkB,kBAAkB,CAAC,MAAM,CACzC,CAAC,KAAK,MAAQ,MAAM,IAAI,aAAa,EACrC,KACE,kBAAkB,kBAAkB,CAAC,MAAM,IAEjD;IAEN,MAAM,sBACJ,mBAAmB,iBAAiB,OAClC,CAAC,KAAK,MAAQ,MAAM,IAAI,cAAc,EACtC,MACG;IAEP,MAAM,oBACJ,mBAAmB,iBAAiB,OAClC,CAAC,KAAK,MAAQ,MAAM,IAAI,iBAAiB,EACzC,MACG;IAEP,MAAM,iBACJ,mBAAmB,qBAAqB,UACxC,kBAAkB,mBAAmB,CAAC,MAAM,GAAG,IAC3C,KAAK,KAAK,CACR,kBAAkB,mBAAmB,CAAC,MAAM,CAC1C,CAAC,KAAK,MAAQ,MAAM,IAAI,eAAe,EACvC,KACE,kBAAkB,mBAAmB,CAAC,MAAM,IAElD;IAEN,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;wCAClC;wCAAe;;;;;;;gCAEjB,oBAAoB,mBACnB,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAc,WAAU;;wCACpC;wCAAkB;;;;;;;gCAGtB,mCACC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAGxB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,OAAM;gCACN,OAAO;gCACP,oBAAM,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEzB,8OAAC;gCACC,OAAM;gCACN,OAAO;gCACP,oBAAM,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAC3B,SACE,kBAAkB,iBAAiB,MAAM,YAAY;;;;;;0CAGzD,8OAAC;gCACC,OAAM;gCACN,OAAO,GAAG,oBAAoB,GAAG,CAAC;gCAClC,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAC5B,SAAS,sBAAsB,IAAI,YAAY;;;;;;0CAEjD,8OAAC;gCACC,OAAM;gCACN,OAAO,GAAG,eAAe,CAAC,CAAC;gCAC3B,oBAAM,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCACvB,SACE,iBAAiB,KACb,YACA,iBAAiB,KACf,gBACA;;;;;;;;;;;;kCAMZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;0DAGpD,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAEzB,8OAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;0DAGnD,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;kDAE3B,8OAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;0CAIL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;0DAGrD,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,8OAAC;wCAAK,WAAU;;4CACb,mBAAmB,oBAAoB,UACxC,kBAAkB,kBAAkB,CAAC,MAAM,GAAG,IAC1C,KAAK,KAAK,CACR,kBAAkB,kBAAkB,CAAC,MAAM,CACzC,CAAC,KAAK,MAAQ,MAAM,IAAI,iBAAiB,EACzC,KACE,kBAAkB,kBAAkB,CAAC,MAAM,IAEjD;4CAAE;;;;;;;;;;;;;;;;;;;oBAOX,CAAC,WAAW,mBAAmB,sCAC9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAEX,kBAAkB,oBAAoB,CAAC,MAAM,CAC3C,CAAA,MAAO,IAAI,MAAM,KAAK,iBACtB,MAAM;;;;;;0DAGZ,8OAAC;gDAAI,WAAU;0DAAyB;;;;;;;;;;;;kDAE1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAEX,kBAAkB,oBAAoB,CAAC,MAAM,CAC3C,CAAA,MAAO,IAAI,MAAM,KAAK,WACtB,MAAM;;;;;;0DAGZ,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAEX,kBAAkB,oBAAoB,CAAC,MAAM,CAC3C,CAAA,MAAO,IAAI,MAAM,KAAK,cACtB,MAAM;;;;;;0DAGZ,8OAAC;gDAAI,WAAU;0DAAuB;;;;;;;;;;;;;;;;;;;;;;;;oBAM7C,CAAC,yBACA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;wCAAwB;wCACvB,IAAI,OAAO,kBAAkB;;;;;;;8CAE9C,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD;uCAEe", "debugId": null}}, {"offset": {"line": 7882, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/EmployeePerformanceChart.tsx"], "sourcesContent": ["/**\r\n * @file EmployeePerformanceChart.tsx\r\n * @description Employee performance chart following existing chart patterns and SOLID principles\r\n */\r\n\r\nimport { Award, TrendingUp, Users } from 'lucide-react';\r\nimport React, { useMemo } from 'react';\r\nimport {\r\n  Bar,\r\n  BarChart,\r\n  CartesianGrid,\r\n  Cell,\r\n  ResponsiveContainer,\r\n  Tooltip,\r\n  XAxis,\r\n  YAxis,\r\n} from 'recharts';\r\n\r\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { DataLoader } from '@/components/ui/data-loader';\r\nimport { ErrorDisplay } from '@/components/ui/error-display';\r\nimport { cn } from '@/lib/utils';\r\n\r\nimport type { ReportingFilters } from '../../data/types/reporting';\r\nimport type { EmployeePerformanceData } from '../../data/types/reporting';\r\n\r\nimport { useEmployeeAnalytics } from '../../hooks/useEmployeeAnalytics';\r\n\r\n/**\r\n * Custom tooltip component for the chart\r\n */\r\ninterface CustomTooltipProps {\r\n  active?: boolean;\r\n  label?: string;\r\n  payload?: any[];\r\n}\r\n\r\n/**\r\n * Props interface for EmployeePerformanceChart\r\n */\r\ninterface EmployeePerformanceChartProps {\r\n  className?: string;\r\n  data?: EmployeePerformanceData[];\r\n  filters?: ReportingFilters;\r\n  height?: number;\r\n  interactive?: boolean;\r\n  maxEmployees?: number;\r\n  showLegend?: boolean;\r\n}\r\n\r\nconst CustomTooltip: React.FC<CustomTooltipProps> = ({\r\n  active,\r\n  label,\r\n  payload,\r\n}) => {\r\n  if (active && payload?.length) {\r\n    const data = payload[0].payload;\r\n    return (\r\n      <div className=\"rounded-lg border bg-white p-3 shadow-lg\">\r\n        <p className=\"font-medium\">{data.employeeName}</p>\r\n        <p className=\"text-sm text-gray-600\">\r\n          Performance Score:{' '}\r\n          <span className=\"font-medium\">{data.averageRating}/10</span>\r\n        </p>\r\n        <p className=\"text-sm text-gray-600\">\r\n          Completed Delegations:{' '}\r\n          <span className=\"font-medium\">{data.completedDelegations}</span>\r\n        </p>\r\n        <p className=\"text-sm text-gray-600\">\r\n          Completed Tasks:{' '}\r\n          <span className=\"font-medium\">{data.completedTasks}</span>\r\n        </p>\r\n        <p className=\"text-sm text-gray-600\">\r\n          On-Time Rate:{' '}\r\n          <span className=\"font-medium\">{data.onTimePerformance}%</span>\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n  return null;\r\n};\r\n\r\n/**\r\n * Utility function to get color based on performance score\r\n */\r\nconst getPerformanceColor = (score: number): string => {\r\n  if (score >= 9) return '#10b981'; // Green - excellent\r\n  if (score >= 8) return '#3b82f6'; // Blue - good\r\n  if (score >= 7) return '#f59e0b'; // Orange - average\r\n  if (score >= 6) return '#ef4444'; // Red - below average\r\n  return '#6b7280'; // Gray - poor\r\n};\r\n\r\n/**\r\n * EmployeePerformanceChart Component\r\n *\r\n * Displays employee performance data in a bar chart format following existing chart patterns.\r\n *\r\n * Responsibilities:\r\n * - Visualize employee performance distribution\r\n * - Follow existing chart component patterns\r\n * - Integrate with existing chart utilities\r\n * - Maintain consistent chart styling\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying performance data\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on chart framework abstractions\r\n */\r\nexport const EmployeePerformanceChart: React.FC<\r\n  EmployeePerformanceChartProps\r\n> = ({\r\n  className = '',\r\n  data: propData,\r\n  filters,\r\n  height = 300,\r\n  interactive = true,\r\n  maxEmployees = 10,\r\n  showLegend = true,\r\n}) => {\r\n  // Use hook if data not provided via props\r\n  const { data: hookData, error, isLoading } = useEmployeeAnalytics(filters);\r\n  const performanceData = propData || hookData?.performanceMetrics;\r\n\r\n  // Transform data for chart display\r\n  const chartData = useMemo(() => {\r\n    if (!performanceData) return [];\r\n\r\n    return performanceData\r\n      .map(item => ({\r\n        averageRating: item.averageRating,\r\n        color: getPerformanceColor(item.averageRating),\r\n        completedDelegations: item.completedDelegations,\r\n        completedTasks: item.completedTasks,\r\n        // Truncate long names for display\r\n        displayName:\r\n          item.employeeName.length > 12\r\n            ? `${item.employeeName.slice(0, 9)}...`\r\n            : item.employeeName,\r\n        employeeName: item.employeeName,\r\n        onTimePerformance: item.onTimePerformance,\r\n        // Calculate overall performance score\r\n        overallScore: Math.round(\r\n          item.averageRating * 0.4 +\r\n            item.onTimePerformance * 0.01 * 10 * 0.3 +\r\n            item.workloadScore * 0.3\r\n        ),\r\n        workloadScore: item.workloadScore,\r\n      }))\r\n      .sort((a, b) => b.overallScore - a.overallScore) // Sort by overall performance\r\n      .slice(0, maxEmployees); // Show top performers\r\n  }, [performanceData, maxEmployees]);\r\n\r\n  // Handle loading state\r\n  if (isLoading) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Users className=\"size-5\" />\r\n            Employee Performance\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <DataLoader data={null} error={null} isLoading={true}>\r\n            {() => null}\r\n          </DataLoader>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Handle error state\r\n  if (error) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Users className=\"size-5\" />\r\n            Employee Performance\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <ErrorDisplay error={error} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Calculate summary stats\r\n  const avgPerformance =\r\n    chartData.length > 0\r\n      ? Math.round(\r\n          (chartData.reduce((sum, item) => sum + item.averageRating, 0) /\r\n            chartData.length) *\r\n            10\r\n        ) / 10\r\n      : 0;\r\n\r\n  const topPerformers = chartData.filter(\r\n    item => item.averageRating >= 8\r\n  ).length;\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader className=\"pb-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Users className=\"size-5\" />\r\n            Employee Performance\r\n          </CardTitle>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Badge className=\"text-xs\" variant=\"secondary\">\r\n              Avg: {avgPerformance}/10\r\n            </Badge>\r\n            {topPerformers > 0 && (\r\n              <Badge className=\"text-xs\" variant=\"default\">\r\n                <Award className=\"mr-1 size-3\" />\r\n                {topPerformers} top performers\r\n              </Badge>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n\r\n      <CardContent>\r\n        {chartData.length === 0 ? (\r\n          <div className=\"flex h-64 items-center justify-center text-gray-500\">\r\n            <div className=\"text-center\">\r\n              <Users className=\"mx-auto mb-2 size-12 opacity-50\" />\r\n              <p>No employee performance data available</p>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            <ResponsiveContainer height={height} width=\"100%\">\r\n              <BarChart\r\n                data={chartData}\r\n                margin={{\r\n                  bottom: 60,\r\n                  left: 20,\r\n                  right: 30,\r\n                  top: 20,\r\n                }}\r\n              >\r\n                <CartesianGrid stroke=\"#f0f0f0\" strokeDasharray=\"3 3\" />\r\n                <XAxis\r\n                  angle={-45}\r\n                  dataKey=\"displayName\"\r\n                  fontSize={12}\r\n                  height={60}\r\n                  textAnchor=\"end\"\r\n                />\r\n                <YAxis\r\n                  domain={[0, 10]}\r\n                  fontSize={12}\r\n                  tickFormatter={value => `${value}/10`}\r\n                />\r\n                {interactive && <Tooltip content={<CustomTooltip />} />}\r\n                <Bar\r\n                  dataKey=\"averageRating\"\r\n                  name=\"Performance Score\"\r\n                  radius={[4, 4, 0, 0]}\r\n                >\r\n                  {chartData.map((entry, index) => (\r\n                    <Cell fill={entry.color} key={`cell-${index}`} />\r\n                  ))}\r\n                </Bar>\r\n              </BarChart>\r\n            </ResponsiveContainer>\r\n\r\n            {showLegend && (\r\n              <div className=\"mt-4 flex flex-wrap gap-4 text-xs\">\r\n                <div className=\"flex items-center gap-1\">\r\n                  <div className=\"size-3 rounded bg-green-500\"></div>\r\n                  <span>Excellent (9-10)</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <div className=\"size-3 rounded bg-blue-500\"></div>\r\n                  <span>Good (8-8.9)</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <div className=\"size-3 rounded bg-orange-500\"></div>\r\n                  <span>Average (7-7.9)</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <div className=\"size-3 rounded bg-red-500\"></div>\r\n                  <span>Below Average (6-6.9)</span>\r\n                </div>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <div className=\"size-3 rounded bg-gray-500\"></div>\r\n                  <span>Poor (&lt;6)</span>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Top Performers List */}\r\n            <div className=\"mt-6 space-y-2\">\r\n              <h4 className=\"text-sm font-medium\">Top Performers</h4>\r\n              <div className=\"space-y-2\">\r\n                {chartData.slice(0, 3).map((employee, index) => (\r\n                  <div\r\n                    className=\"flex items-center justify-between rounded bg-gray-50 p-2\"\r\n                    key={employee.employeeName}\r\n                  >\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Badge\r\n                        className=\"flex size-6 items-center justify-center p-0 text-xs\"\r\n                        variant=\"outline\"\r\n                      >\r\n                        {index + 1}\r\n                      </Badge>\r\n                      <Avatar className=\"size-6\">\r\n                        <AvatarFallback className=\"text-xs\">\r\n                          {employee.employeeName\r\n                            .split(' ')\r\n                            .map(n => n[0])\r\n                            .join('')}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <span className=\"text-sm font-medium\">\r\n                        {employee.employeeName}\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"text-right\">\r\n                      <div className=\"text-sm font-bold\">\r\n                        {employee.averageRating}/10\r\n                      </div>\r\n                      <div className=\"text-xs text-gray-500\">\r\n                        {employee.onTimePerformance}% on-time\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default EmployeePerformanceChart;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AAAA;AACA;AAMA;;;;;;;;;;;AAwBA,MAAM,gBAA8C,CAAC,EACnD,MAAM,EACN,KAAK,EACL,OAAO,EACR;IACC,IAAI,UAAU,SAAS,QAAQ;QAC7B,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;QAC/B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BAAe,KAAK,YAAY;;;;;;8BAC7C,8OAAC;oBAAE,WAAU;;wBAAwB;wBAChB;sCACnB,8OAAC;4BAAK,WAAU;;gCAAe,KAAK,aAAa;gCAAC;;;;;;;;;;;;;8BAEpD,8OAAC;oBAAE,WAAU;;wBAAwB;wBACZ;sCACvB,8OAAC;4BAAK,WAAU;sCAAe,KAAK,oBAAoB;;;;;;;;;;;;8BAE1D,8OAAC;oBAAE,WAAU;;wBAAwB;wBAClB;sCACjB,8OAAC;4BAAK,WAAU;sCAAe,KAAK,cAAc;;;;;;;;;;;;8BAEpD,8OAAC;oBAAE,WAAU;;wBAAwB;wBACrB;sCACd,8OAAC;4BAAK,WAAU;;gCAAe,KAAK,iBAAiB;gCAAC;;;;;;;;;;;;;;;;;;;IAI9D;IACA,OAAO;AACT;AAEA;;CAEC,GACD,MAAM,sBAAsB,CAAC;IAC3B,IAAI,SAAS,GAAG,OAAO,WAAW,oBAAoB;IACtD,IAAI,SAAS,GAAG,OAAO,WAAW,cAAc;IAChD,IAAI,SAAS,GAAG,OAAO,WAAW,mBAAmB;IACrD,IAAI,SAAS,GAAG,OAAO,WAAW,sBAAsB;IACxD,OAAO,WAAW,cAAc;AAClC;AAkBO,MAAM,2BAET,CAAC,EACH,YAAY,EAAE,EACd,MAAM,QAAQ,EACd,OAAO,EACP,SAAS,GAAG,EACZ,cAAc,IAAI,EAClB,eAAe,EAAE,EACjB,aAAa,IAAI,EAClB;IACC,0CAA0C;IAC1C,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,uBAAoB,AAAD,EAAE;IAClE,MAAM,kBAAkB,YAAY,UAAU;IAE9C,mCAAmC;IACnC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,IAAI,CAAC,iBAAiB,OAAO,EAAE;QAE/B,OAAO,gBACJ,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,eAAe,KAAK,aAAa;gBACjC,OAAO,oBAAoB,KAAK,aAAa;gBAC7C,sBAAsB,KAAK,oBAAoB;gBAC/C,gBAAgB,KAAK,cAAc;gBACnC,kCAAkC;gBAClC,aACE,KAAK,YAAY,CAAC,MAAM,GAAG,KACvB,GAAG,KAAK,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GACrC,KAAK,YAAY;gBACvB,cAAc,KAAK,YAAY;gBAC/B,mBAAmB,KAAK,iBAAiB;gBACzC,sCAAsC;gBACtC,cAAc,KAAK,KAAK,CACtB,KAAK,aAAa,GAAG,MACnB,KAAK,iBAAiB,GAAG,OAAO,KAAK,MACrC,KAAK,aAAa,GAAG;gBAEzB,eAAe,KAAK,aAAa;YACnC,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY,EAAE,8BAA8B;SAC9E,KAAK,CAAC,GAAG,eAAe,sBAAsB;IACnD,GAAG;QAAC;QAAiB;KAAa;IAElC,uBAAuB;IACvB,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAW;;;;;;;;;;;;8BAIhC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,aAAU;wBAAC,MAAM;wBAAM,OAAO;wBAAM,WAAW;kCAC7C,IAAM;;;;;;;;;;;;;;;;;IAKjB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAW;;;;;;;;;;;;8BAIhC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,4IAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;;;;;;;;;;;;IAI7B;IAEA,0BAA0B;IAC1B,MAAM,iBACJ,UAAU,MAAM,GAAG,IACf,KAAK,KAAK,CACR,AAAC,UAAU,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,aAAa,EAAE,KACzD,UAAU,MAAM,GAChB,MACA,KACJ;IAEN,MAAM,gBAAgB,UAAU,MAAM,CACpC,CAAA,OAAQ,KAAK,aAAa,IAAI,GAC9B,MAAM;IAER,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAW;;;;;;;sCAG9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;oCAAU,SAAQ;;wCAAY;wCACvC;wCAAe;;;;;;;gCAEtB,gBAAgB,mBACf,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;oCAAU,SAAQ;;sDACjC,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAChB;wCAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAOzB,8OAAC,gIAAA,CAAA,cAAW;0BACT,UAAU,MAAM,KAAK,kBACpB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;0CAAE;;;;;;;;;;;;;;;;yCAIP;;sCACE,8OAAC,mKAAA,CAAA,sBAAmB;4BAAC,QAAQ;4BAAQ,OAAM;sCACzC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;gCACP,MAAM;gCACN,QAAQ;oCACN,QAAQ;oCACR,MAAM;oCACN,OAAO;oCACP,KAAK;gCACP;;kDAEA,8OAAC,6JAAA,CAAA,gBAAa;wCAAC,QAAO;wCAAU,iBAAgB;;;;;;kDAChD,8OAAC,qJAAA,CAAA,QAAK;wCACJ,OAAO,CAAC;wCACR,SAAQ;wCACR,UAAU;wCACV,QAAQ;wCACR,YAAW;;;;;;kDAEb,8OAAC,qJAAA,CAAA,QAAK;wCACJ,QAAQ;4CAAC;4CAAG;yCAAG;wCACf,UAAU;wCACV,eAAe,CAAA,QAAS,GAAG,MAAM,GAAG,CAAC;;;;;;oCAEtC,6BAAe,8OAAC,uJAAA,CAAA,UAAO;wCAAC,uBAAS,8OAAC;;;;;;;;;;kDACnC,8OAAC,mJAAA,CAAA,MAAG;wCACF,SAAQ;wCACR,MAAK;wCACL,QAAQ;4CAAC;4CAAG;4CAAG;4CAAG;yCAAE;kDAEnB,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,8OAAC,oJAAA,CAAA,OAAI;gDAAC,MAAM,MAAM,KAAK;+CAAO,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;wBAMpD,4BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAMZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;8CACpC,8OAAC;oCAAI,WAAU;8CACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBACpC,8OAAC;4CACC,WAAU;;8DAGV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DACJ,WAAU;4DACV,SAAQ;sEAEP,QAAQ;;;;;;sEAEX,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;sEAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,SAAS,YAAY,CACnB,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EACb,IAAI,CAAC;;;;;;;;;;;sEAGZ,8OAAC;4DAAK,WAAU;sEACb,SAAS,YAAY;;;;;;;;;;;;8DAG1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,SAAS,aAAa;gEAAC;;;;;;;sEAE1B,8OAAC;4DAAI,WAAU;;gEACZ,SAAS,iBAAiB;gEAAC;;;;;;;;;;;;;;2CA1B3B,SAAS,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsC9C;uCAEe", "debugId": null}}, {"offset": {"line": 8604, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/EmployeeWorkloadWidget.tsx"], "sourcesContent": ["/**\r\n * @file EmployeeWorkloadWidget.tsx\r\n * @description Employee workload distribution widget following existing patterns and SOLID principles\r\n */\r\n\r\nimport React from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar';\r\nimport { Progress } from '@/components/ui/progress';\r\nimport {\r\n  Users,\r\n  AlertTriangle,\r\n  CheckCircle,\r\n  Clock,\r\n  TrendingUp,\r\n  MoreHorizontal,\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { useEmployeeAnalytics } from '../../hooks/useEmployeeAnalytics';\r\nimport { DataLoader } from '@/components/ui/data-loader';\r\nimport { ErrorDisplay } from '@/components/ui/error-display';\r\nimport type { ReportingFilters } from '../../data/types/reporting';\r\nimport type { WorkloadDistributionData } from '../../data/types/reporting';\r\n\r\n/**\r\n * Props interface for EmployeeWorkloadWidget\r\n */\r\ninterface EmployeeWorkloadWidgetProps {\r\n  filters?: ReportingFilters;\r\n  className?: string;\r\n  compact?: boolean;\r\n  maxItems?: number;\r\n}\r\n\r\n/**\r\n * Workload item component\r\n */\r\ninterface WorkloadItemProps {\r\n  item: WorkloadDistributionData;\r\n  compact?: boolean;\r\n}\r\n\r\nconst WorkloadItem: React.FC<WorkloadItemProps> = ({\r\n  item,\r\n  compact = false,\r\n}) => {\r\n  const getStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'Underutilized':\r\n        return 'text-blue-600 bg-blue-100';\r\n      case 'Optimal':\r\n        return 'text-green-600 bg-green-100';\r\n      case 'Overloaded':\r\n        return 'text-red-600 bg-red-100';\r\n      default:\r\n        return 'text-gray-600 bg-gray-100';\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = (status: string) => {\r\n    switch (status) {\r\n      case 'Underutilized':\r\n        return <Clock className=\"h-4 w-4 text-blue-600\" />;\r\n      case 'Optimal':\r\n        return <CheckCircle className=\"h-4 w-4 text-green-600\" />;\r\n      case 'Overloaded':\r\n        return <AlertTriangle className=\"h-4 w-4 text-red-600\" />;\r\n      default:\r\n        return <Users className=\"h-4 w-4 text-gray-600\" />;\r\n    }\r\n  };\r\n\r\n  const progressColor =\r\n    item.status === 'Overloaded'\r\n      ? 'bg-red-500'\r\n      : item.status === 'Optimal'\r\n        ? 'bg-green-500'\r\n        : 'bg-blue-500';\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'flex items-center justify-between p-3 border rounded-lg',\r\n        item.status === 'Overloaded' && 'border-red-200 bg-red-50',\r\n        item.status === 'Optimal' && 'border-green-200 bg-green-50',\r\n        item.status === 'Underutilized' && 'border-blue-200 bg-blue-50'\r\n      )}\r\n    >\r\n      <div className=\"flex items-center space-x-3 flex-1\">\r\n        <Avatar className=\"h-8 w-8\">\r\n          <AvatarFallback className=\"text-xs\">\r\n            {item.employeeName\r\n              .split(' ')\r\n              .map(n => n[0])\r\n              .join('')}\r\n          </AvatarFallback>\r\n        </Avatar>\r\n        <div className=\"flex-1\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <p className=\"font-medium text-sm\">{item.employeeName}</p>\r\n            {!compact && (\r\n              <Badge className={cn('text-xs', getStatusColor(item.status))}>\r\n                {item.status}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n          <div className=\"mt-1\">\r\n            <div className=\"flex items-center gap-2 text-xs text-gray-500\">\r\n              <span>\r\n                Workload: {item.currentWorkload}/{item.capacity}\r\n              </span>\r\n              <span>•</span>\r\n              <span>{item.workloadPercentage}%</span>\r\n            </div>\r\n            <Progress\r\n              value={item.workloadPercentage}\r\n              className=\"h-2 mt-1\"\r\n              // Custom color based on status\r\n              style={\r\n                {\r\n                  '--progress-background':\r\n                    item.status === 'Overloaded'\r\n                      ? '#ef4444'\r\n                      : item.status === 'Optimal'\r\n                        ? '#10b981'\r\n                        : '#3b82f6',\r\n                } as React.CSSProperties\r\n              }\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex items-center gap-2\">\r\n        {getStatusIcon(item.status)}\r\n        {compact && (\r\n          <Badge className={cn('text-xs', getStatusColor(item.status))}>\r\n            {item.workloadPercentage}%\r\n          </Badge>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * EmployeeWorkloadWidget Component\r\n *\r\n * Displays employee workload distribution following existing widget patterns.\r\n *\r\n * Responsibilities:\r\n * - Display workload distribution in widget format\r\n * - Show overloaded and underutilized employees\r\n * - Follow established widget composition patterns\r\n * - Maintain consistent styling and behavior\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying workload data\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on existing widget framework abstractions\r\n */\r\nexport const EmployeeWorkloadWidget: React.FC<EmployeeWorkloadWidgetProps> = ({\r\n  filters,\r\n  className = '',\r\n  compact = false,\r\n  maxItems = 8,\r\n}) => {\r\n  // Use existing hook patterns\r\n  const {\r\n    data: employeeAnalytics,\r\n    isLoading,\r\n    error,\r\n  } = useEmployeeAnalytics(filters);\r\n  const workloadData = employeeAnalytics?.workloadDistribution || [];\r\n\r\n  // Handle loading state\r\n  if (isLoading) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Users className=\"h-5 w-5\" />\r\n            Employee Workload\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <DataLoader isLoading={true} data={null} error={null}>\r\n            {() => null}\r\n          </DataLoader>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Handle error state\r\n  if (error) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Users className=\"h-5 w-5\" />\r\n            Employee Workload\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <ErrorDisplay error={error} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Process workload data\r\n  const overloadedEmployees = workloadData.filter(\r\n    emp => emp.status === 'Overloaded'\r\n  );\r\n  const underutilizedEmployees = workloadData.filter(\r\n    emp => emp.status === 'Underutilized'\r\n  );\r\n  const optimalEmployees = workloadData.filter(emp => emp.status === 'Optimal');\r\n\r\n  // Sort and limit items for display - prioritize overloaded employees\r\n  const displayItems = [\r\n    ...overloadedEmployees,\r\n    ...optimalEmployees,\r\n    ...underutilizedEmployees,\r\n  ].slice(0, maxItems);\r\n\r\n  const avgWorkloadPercentage =\r\n    workloadData.length > 0\r\n      ? Math.round(\r\n          workloadData.reduce((sum, emp) => sum + emp.workloadPercentage, 0) /\r\n            workloadData.length\r\n        )\r\n      : 0;\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader className=\"pb-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Users className=\"h-5 w-5\" />\r\n            Employee Workload\r\n          </CardTitle>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              Avg: {avgWorkloadPercentage}%\r\n            </Badge>\r\n            {overloadedEmployees.length > 0 && (\r\n              <Badge variant=\"destructive\" className=\"text-xs\">\r\n                {overloadedEmployees.length} overloaded\r\n              </Badge>\r\n            )}\r\n            <Button variant=\"ghost\" size=\"sm\">\r\n              <MoreHorizontal className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Summary Stats */}\r\n        <div className=\"grid grid-cols-3 gap-4\">\r\n          <div className=\"text-center p-3 bg-blue-50 rounded-lg\">\r\n            <div className=\"text-2xl font-bold text-blue-700\">\r\n              {underutilizedEmployees.length}\r\n            </div>\r\n            <div className=\"text-xs text-blue-600\">Underutilized</div>\r\n          </div>\r\n          <div className=\"text-center p-3 bg-green-50 rounded-lg\">\r\n            <div className=\"text-2xl font-bold text-green-700\">\r\n              {optimalEmployees.length}\r\n            </div>\r\n            <div className=\"text-xs text-green-600\">Optimal</div>\r\n          </div>\r\n          <div className=\"text-center p-3 bg-red-50 rounded-lg\">\r\n            <div className=\"text-2xl font-bold text-red-700\">\r\n              {overloadedEmployees.length}\r\n            </div>\r\n            <div className=\"text-xs text-red-600\">Overloaded</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Workload Items List */}\r\n        <div className=\"space-y-3\">\r\n          {displayItems.length === 0 ? (\r\n            <div className=\"text-center py-8 text-gray-500\">\r\n              <Users className=\"h-12 w-12 mx-auto mb-2 opacity-50\" />\r\n              <p>No workload data available</p>\r\n            </div>\r\n          ) : (\r\n            displayItems.map((item, index) => (\r\n              <WorkloadItem\r\n                key={`${item.employeeId}-${index}`}\r\n                item={item}\r\n                compact={compact}\r\n              />\r\n            ))\r\n          )}\r\n        </div>\r\n\r\n        {/* Workload Alerts */}\r\n        {overloadedEmployees.length > 0 && (\r\n          <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <AlertTriangle className=\"h-5 w-5 text-red-600\" />\r\n              <span className=\"font-medium text-red-800\">Workload Alert</span>\r\n            </div>\r\n            <p className=\"text-sm text-red-700 mt-1\">\r\n              {overloadedEmployees.length} employee\r\n              {overloadedEmployees.length > 1 ? 's are' : ' is'} overloaded.\r\n              Consider redistributing tasks or adjusting schedules.\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {!compact && workloadData.length > maxItems && (\r\n          <div className=\"pt-4 border-t\">\r\n            <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\r\n              View All Employees ({workloadData.length} total)\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default EmployeeWorkloadWidget;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAQA;AAAA;AACA;AACA;AAAA;AACA;;;;;;;;;;;;AAsBA,MAAM,eAA4C,CAAC,EACjD,IAAI,EACJ,UAAU,KAAK,EAChB;IACC,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,gBACJ,KAAK,MAAM,KAAK,eACZ,eACA,KAAK,MAAM,KAAK,YACd,iBACA;IAER,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2DACA,KAAK,MAAM,KAAK,gBAAgB,4BAChC,KAAK,MAAM,KAAK,aAAa,gCAC7B,KAAK,MAAM,KAAK,mBAAmB;;0BAGrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBAAC,WAAU;kCAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACvB,KAAK,YAAY,CACf,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EACb,IAAI,CAAC;;;;;;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAuB,KAAK,YAAY;;;;;;oCACpD,CAAC,yBACA,8OAAC,iIAAA,CAAA,QAAK;wCAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW,eAAe,KAAK,MAAM;kDACvD,KAAK,MAAM;;;;;;;;;;;;0CAIlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAK;oDACO,KAAK,eAAe;oDAAC;oDAAE,KAAK,QAAQ;;;;;;;0DAEjD,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;oDAAM,KAAK,kBAAkB;oDAAC;;;;;;;;;;;;;kDAEjC,8OAAC,oIAAA,CAAA,WAAQ;wCACP,OAAO,KAAK,kBAAkB;wCAC9B,WAAU;wCACV,+BAA+B;wCAC/B,OACE;4CACE,yBACE,KAAK,MAAM,KAAK,eACZ,YACA,KAAK,MAAM,KAAK,YACd,YACA;wCACV;;;;;;;;;;;;;;;;;;;;;;;;0BAOV,8OAAC;gBAAI,WAAU;;oBACZ,cAAc,KAAK,MAAM;oBACzB,yBACC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW,eAAe,KAAK,MAAM;;4BACvD,KAAK,kBAAkB;4BAAC;;;;;;;;;;;;;;;;;;;AAMrC;AAkBO,MAAM,yBAAgE,CAAC,EAC5E,OAAO,EACP,YAAY,EAAE,EACd,UAAU,KAAK,EACf,WAAW,CAAC,EACb;IACC,6BAA6B;IAC7B,MAAM,EACJ,MAAM,iBAAiB,EACvB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,2KAAA,CAAA,uBAAoB,AAAD,EAAE;IACzB,MAAM,eAAe,mBAAmB,wBAAwB,EAAE;IAElE,uBAAuB;IACvB,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIjC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,aAAU;wBAAC,WAAW;wBAAM,MAAM;wBAAM,OAAO;kCAC7C,IAAM;;;;;;;;;;;;;;;;;IAKjB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIjC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,4IAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;;;;;;;;;;;;IAI7B;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,aAAa,MAAM,CAC7C,CAAA,MAAO,IAAI,MAAM,KAAK;IAExB,MAAM,yBAAyB,aAAa,MAAM,CAChD,CAAA,MAAO,IAAI,MAAM,KAAK;IAExB,MAAM,mBAAmB,aAAa,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;IAEnE,qEAAqE;IACrE,MAAM,eAAe;WAChB;WACA;WACA;KACJ,CAAC,KAAK,CAAC,GAAG;IAEX,MAAM,wBACJ,aAAa,MAAM,GAAG,IAClB,KAAK,KAAK,CACR,aAAa,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,kBAAkB,EAAE,KAC9D,aAAa,MAAM,IAEvB;IAEN,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;wCAAU;wCACvC;wCAAsB;;;;;;;gCAE7B,oBAAoB,MAAM,GAAG,mBAC5B,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAc,WAAU;;wCACpC,oBAAoB,MAAM;wCAAC;;;;;;;8CAGhC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,uBAAuB,MAAM;;;;;;kDAEhC,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,iBAAiB,MAAM;;;;;;kDAE1B,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;0CAE1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,oBAAoB,MAAM;;;;;;kDAE7B,8OAAC;wCAAI,WAAU;kDAAuB;;;;;;;;;;;;;;;;;;kCAK1C,8OAAC;wBAAI,WAAU;kCACZ,aAAa,MAAM,KAAK,kBACvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;8CAAE;;;;;;;;;;;mCAGL,aAAa,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;gCAEC,MAAM;gCACN,SAAS;+BAFJ,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;oBASzC,oBAAoB,MAAM,GAAG,mBAC5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;0CAE7C,8OAAC;gCAAE,WAAU;;oCACV,oBAAoB,MAAM;oCAAC;oCAC3B,oBAAoB,MAAM,GAAG,IAAI,UAAU;oCAAM;;;;;;;;;;;;;oBAMvD,CAAC,WAAW,aAAa,MAAM,GAAG,0BACjC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAK,WAAU;;gCAAS;gCAChC,aAAa,MAAM;gCAAC;;;;;;;;;;;;;;;;;;;;;;;;AAOvD;uCAEe", "debugId": null}}, {"offset": {"line": 9247, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/CrossEntityCorrelationWidget.tsx"], "sourcesContent": ["/**\r\n * @file CrossEntityCorrelationWidget.tsx\r\n * @description Cross-entity correlation widget showing relationships between delegations, tasks, vehicles, and employees\r\n */\r\n\r\nimport {\r\n  Car,\r\n  CheckSquare,\r\n  FileText,\r\n  MoreHorizontal,\r\n  Network,\r\n  TrendingUp,\r\n  Users,\r\n} from 'lucide-react';\r\nimport React, { useMemo } from 'react';\r\nimport {\r\n  CartesianGrid,\r\n  Cell,\r\n  ResponsiveContainer,\r\n  Scatter,\r\n  ScatterChart,\r\n  Tooltip,\r\n  XAxis,\r\n  YAxis,\r\n} from 'recharts';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { DataLoader } from '@/components/ui/data-loader';\r\nimport { ErrorDisplay } from '@/components/ui/error-display';\r\nimport { cn } from '@/lib/utils';\r\n\r\nimport type { ReportingFilters } from '../../data/types/reporting';\r\n\r\nimport { useCrossEntityAnalytics } from '../../hooks/useCrossEntityAnalytics';\r\n\r\n/**\r\n * Props interface for CrossEntityCorrelationWidget\r\n */\r\ninterface CrossEntityCorrelationWidgetProps {\r\n  className?: string;\r\n  correlationType?:\r\n    | 'all'\r\n    | 'employee-vehicle'\r\n    | 'performance-workload'\r\n    | 'task-delegation';\r\n  filters?: ReportingFilters;\r\n  interactive?: boolean;\r\n}\r\n\r\n/**\r\n * Custom tooltip for correlation chart\r\n */\r\nconst CustomTooltip = ({ active, payload }: any) => {\r\n  if (active && payload?.length) {\r\n    const data = payload[0].payload;\r\n    return (\r\n      <div className=\"rounded-lg border bg-white p-3 shadow-lg\">\r\n        <p className=\"font-medium\">{data.name}</p>\r\n        <p className=\"text-sm text-gray-600\">\r\n          X-Axis: <span className=\"font-medium\">{data.x}</span>\r\n        </p>\r\n        <p className=\"text-sm text-gray-600\">\r\n          Y-Axis: <span className=\"font-medium\">{data.y}</span>\r\n        </p>\r\n        {data.correlation && (\r\n          <p className=\"text-sm text-gray-600\">\r\n            Correlation: <span className=\"font-medium\">{data.correlation}</span>\r\n          </p>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n  return null;\r\n};\r\n\r\n/**\r\n * Correlation metric card component\r\n */\r\ninterface CorrelationMetricProps {\r\n  description: string;\r\n  icon: React.ReactNode;\r\n  title: string;\r\n  value: number;\r\n}\r\n\r\nconst CorrelationMetric: React.FC<CorrelationMetricProps> = ({\r\n  description,\r\n  icon,\r\n  title,\r\n  value,\r\n}) => {\r\n  const getCorrelationColor = (correlation: number) => {\r\n    const abs = Math.abs(correlation);\r\n    if (abs >= 0.8) return 'text-green-600 bg-green-100';\r\n    if (abs >= 0.6) return 'text-blue-600 bg-blue-100';\r\n    if (abs >= 0.4) return 'text-orange-600 bg-orange-100';\r\n    return 'text-gray-600 bg-gray-100';\r\n  };\r\n\r\n  const getCorrelationStrength = (correlation: number) => {\r\n    const abs = Math.abs(correlation);\r\n    if (abs >= 0.8) return 'Strong';\r\n    if (abs >= 0.6) return 'Moderate';\r\n    if (abs >= 0.4) return 'Weak';\r\n    return 'Very Weak';\r\n  };\r\n\r\n  return (\r\n    <div className=\"rounded-lg border p-4\">\r\n      <div className=\"mb-2 flex items-center justify-between\">\r\n        <div className=\"flex items-center gap-2\">\r\n          {icon}\r\n          <span className=\"text-sm font-medium\">{title}</span>\r\n        </div>\r\n        <Badge className={cn('text-xs', getCorrelationColor(value))}>\r\n          {getCorrelationStrength(value)}\r\n        </Badge>\r\n      </div>\r\n      <div className=\"mb-1 text-2xl font-bold\">{value.toFixed(3)}</div>\r\n      <div className=\"text-xs text-gray-600\">{description}</div>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * CrossEntityCorrelationWidget Component\r\n *\r\n * Displays correlations and relationships between different entities in the system.\r\n *\r\n * Responsibilities:\r\n * - Show correlations between employees, vehicles, tasks, and delegations\r\n * - Visualize relationships using scatter plots and metrics\r\n * - Follow established widget composition patterns\r\n * - Maintain consistent styling and behavior\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying cross-entity correlations\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on existing widget framework abstractions\r\n */\r\nexport const CrossEntityCorrelationWidget: React.FC<\r\n  CrossEntityCorrelationWidgetProps\r\n> = ({\r\n  className = '',\r\n  correlationType = 'all',\r\n  filters,\r\n  interactive = true,\r\n}) => {\r\n  // Use hook for cross-entity analytics\r\n  const {\r\n    data: correlationData,\r\n    error,\r\n    isLoading,\r\n  } = useCrossEntityAnalytics(filters);\r\n\r\n  // Transform data for visualization\r\n  const chartData = useMemo(() => {\r\n    if (!correlationData?.correlations) return [];\r\n\r\n    switch (correlationType) {\r\n      case 'employee-vehicle': {\r\n        return (\r\n          correlationData.correlations.employeeVehicle?.map((item, index) => ({\r\n            color:\r\n              item.correlation > 0.5\r\n                ? '#10b981'\r\n                : item.correlation > 0\r\n                  ? '#3b82f6'\r\n                  : '#ef4444',\r\n            correlation: item.correlation,\r\n            name: `${item.employeeName} - ${item.vehicleName}`,\r\n            x: item.employeePerformance,\r\n            y: item.vehicleUtilization,\r\n          })) || []\r\n        );\r\n      }\r\n\r\n      case 'performance-workload': {\r\n        return (\r\n          correlationData.correlations.performanceWorkload?.map(\r\n            (item, index) => ({\r\n              color:\r\n                item.correlation > 0.5\r\n                  ? '#10b981'\r\n                  : item.correlation > 0\r\n                    ? '#3b82f6'\r\n                    : '#ef4444',\r\n              correlation: item.correlation,\r\n              name: item.employeeName,\r\n              x: item.workloadPercentage,\r\n              y: item.performanceScore,\r\n            })\r\n          ) || []\r\n        );\r\n      }\r\n\r\n      case 'task-delegation': {\r\n        return (\r\n          correlationData.correlations.taskDelegation?.map((item, index) => ({\r\n            color:\r\n              item.correlation > 0.5\r\n                ? '#10b981'\r\n                : item.correlation > 0\r\n                  ? '#3b82f6'\r\n                  : '#ef4444',\r\n            correlation: item.correlation,\r\n            name: item.taskType,\r\n            x: item.taskComplexity,\r\n            y: item.delegationSuccess,\r\n          })) || []\r\n        );\r\n      }\r\n\r\n      default: {\r\n        return (\r\n          correlationData.correlations.overall?.map((item, index) => ({\r\n            color:\r\n              item.correlation > 0.5\r\n                ? '#10b981'\r\n                : item.correlation > 0\r\n                  ? '#3b82f6'\r\n                  : '#ef4444',\r\n            correlation: item.correlation,\r\n            name: item.entityName,\r\n            x: item.xValue,\r\n            y: item.yValue,\r\n          })) || []\r\n        );\r\n      }\r\n    }\r\n  }, [correlationData, correlationType]);\r\n\r\n  // Handle loading state\r\n  if (isLoading) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Network className=\"size-5\" />\r\n            Cross-Entity Correlations\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <DataLoader data={null} error={null} isLoading={true}>\r\n            {() => null}\r\n          </DataLoader>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Handle error state\r\n  if (error) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Network className=\"size-5\" />\r\n            Cross-Entity Correlations\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <ErrorDisplay error={error} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  const correlationMetrics = correlationData?.metrics ?? {\r\n    employeeVehicle: 0,\r\n    overallEfficiency: 0,\r\n    performanceWorkload: 0,\r\n    taskDelegation: 0,\r\n  };\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader className=\"pb-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Network className=\"size-5\" />\r\n            Cross-Entity Correlations\r\n          </CardTitle>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Badge className=\"text-xs\" variant=\"secondary\">\r\n              {chartData.length} relationships\r\n            </Badge>\r\n            <Button size=\"sm\" variant=\"ghost\">\r\n              <MoreHorizontal className=\"size-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-6\">\r\n        {/* Correlation Metrics Grid */}\r\n        <div className=\"grid grid-cols-2 gap-4 md:grid-cols-4\">\r\n          <CorrelationMetric\r\n            description=\"Performance vs Vehicle Usage\"\r\n            icon={<Users className=\"size-4\" />}\r\n            title=\"Employee-Vehicle\"\r\n            value={correlationMetrics.employeeVehicle || 0}\r\n          />\r\n          <CorrelationMetric\r\n            description=\"Task Complexity vs Success\"\r\n            icon={<CheckSquare className=\"size-4\" />}\r\n            title=\"Task-Delegation\"\r\n            value={correlationMetrics.taskDelegation || 0}\r\n          />\r\n          <CorrelationMetric\r\n            description=\"Workload vs Performance\"\r\n            icon={<TrendingUp className=\"size-4\" />}\r\n            title=\"Performance-Workload\"\r\n            value={correlationMetrics.performanceWorkload || 0}\r\n          />\r\n          <CorrelationMetric\r\n            description=\"System-wide Correlation\"\r\n            icon={<Network className=\"size-4\" />}\r\n            title=\"Overall Efficiency\"\r\n            value={correlationMetrics.overallEfficiency || 0}\r\n          />\r\n        </div>\r\n\r\n        {/* Correlation Scatter Plot */}\r\n        <div>\r\n          <h4 className=\"mb-3 text-sm font-medium\">\r\n            Relationship Visualization\r\n          </h4>\r\n          {chartData.length > 0 ? (\r\n            <ResponsiveContainer height={300} width=\"100%\">\r\n              <ScatterChart\r\n                data={chartData}\r\n                margin={{\r\n                  bottom: 20,\r\n                  left: 20,\r\n                  right: 30,\r\n                  top: 20,\r\n                }}\r\n              >\r\n                <CartesianGrid stroke=\"#f0f0f0\" strokeDasharray=\"3 3\" />\r\n                <XAxis dataKey=\"x\" fontSize={12} name=\"X-Axis\" type=\"number\" />\r\n                <YAxis dataKey=\"y\" fontSize={12} name=\"Y-Axis\" type=\"number\" />\r\n                {interactive && <Tooltip content={<CustomTooltip />} />}\r\n                <Scatter dataKey=\"y\" fill=\"#3b82f6\">\r\n                  {chartData.map((entry, index) => (\r\n                    <Cell fill={entry.color} key={`cell-${index}`} />\r\n                  ))}\r\n                </Scatter>\r\n              </ScatterChart>\r\n            </ResponsiveContainer>\r\n          ) : (\r\n            <div className=\"flex h-64 items-center justify-center text-gray-500\">\r\n              <div className=\"text-center\">\r\n                <Network className=\"mx-auto mb-2 size-12 opacity-50\" />\r\n                <p>No correlation data available</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Key Insights */}\r\n        {correlationData?.insights && correlationData.insights.length > 0 && (\r\n          <div className=\"space-y-3\">\r\n            <h4 className=\"text-sm font-medium\">Key Insights</h4>\r\n            <div className=\"space-y-2\">\r\n              {correlationData.insights.slice(0, 3).map((insight, index) => (\r\n                <div\r\n                  className=\"rounded-lg border border-blue-200 bg-blue-50 p-3\"\r\n                  key={index}\r\n                >\r\n                  <div className=\"flex items-start gap-2\">\r\n                    <TrendingUp className=\"mt-0.5 size-4 text-blue-600\" />\r\n                    <div>\r\n                      <p className=\"text-sm font-medium text-blue-800\">\r\n                        {insight.title}\r\n                      </p>\r\n                      <p className=\"mt-1 text-xs text-blue-700\">\r\n                        {insight.description}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default CrossEntityCorrelationWidget;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAIA;;;;;;;;;;;;AAgBA;;CAEC,GACD,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAO;IAC7C,IAAI,UAAU,SAAS,QAAQ;QAC7B,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO;QAC/B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BAAe,KAAK,IAAI;;;;;;8BACrC,8OAAC;oBAAE,WAAU;;wBAAwB;sCAC3B,8OAAC;4BAAK,WAAU;sCAAe,KAAK,CAAC;;;;;;;;;;;;8BAE/C,8OAAC;oBAAE,WAAU;;wBAAwB;sCAC3B,8OAAC;4BAAK,WAAU;sCAAe,KAAK,CAAC;;;;;;;;;;;;gBAE9C,KAAK,WAAW,kBACf,8OAAC;oBAAE,WAAU;;wBAAwB;sCACtB,8OAAC;4BAAK,WAAU;sCAAe,KAAK,WAAW;;;;;;;;;;;;;;;;;;IAKtE;IACA,OAAO;AACT;AAYA,MAAM,oBAAsD,CAAC,EAC3D,WAAW,EACX,IAAI,EACJ,KAAK,EACL,KAAK,EACN;IACC,MAAM,sBAAsB,CAAC;QAC3B,MAAM,MAAM,KAAK,GAAG,CAAC;QACrB,IAAI,OAAO,KAAK,OAAO;QACvB,IAAI,OAAO,KAAK,OAAO;QACvB,IAAI,OAAO,KAAK,OAAO;QACvB,OAAO;IACT;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,MAAM,KAAK,GAAG,CAAC;QACrB,IAAI,OAAO,KAAK,OAAO;QACvB,IAAI,OAAO,KAAK,OAAO;QACvB,IAAI,OAAO,KAAK,OAAO;QACvB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;4BACZ;0CACD,8OAAC;gCAAK,WAAU;0CAAuB;;;;;;;;;;;;kCAEzC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW,oBAAoB;kCACjD,uBAAuB;;;;;;;;;;;;0BAG5B,8OAAC;gBAAI,WAAU;0BAA2B,MAAM,OAAO,CAAC;;;;;;0BACxD,8OAAC;gBAAI,WAAU;0BAAyB;;;;;;;;;;;;AAG9C;AAkBO,MAAM,+BAET,CAAC,EACH,YAAY,EAAE,EACd,kBAAkB,KAAK,EACvB,OAAO,EACP,cAAc,IAAI,EACnB;IACC,sCAAsC;IACtC,MAAM,EACJ,MAAM,eAAe,EACrB,KAAK,EACL,SAAS,EACV,GAAG,CAAA,GAAA,8KAAA,CAAA,0BAAuB,AAAD,EAAE;IAE5B,mCAAmC;IACnC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,IAAI,CAAC,iBAAiB,cAAc,OAAO,EAAE;QAE7C,OAAQ;YACN,KAAK;gBAAoB;oBACvB,OACE,gBAAgB,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,MAAM,QAAU,CAAC;4BAClE,OACE,KAAK,WAAW,GAAG,MACf,YACA,KAAK,WAAW,GAAG,IACjB,YACA;4BACR,aAAa,KAAK,WAAW;4BAC7B,MAAM,GAAG,KAAK,YAAY,CAAC,GAAG,EAAE,KAAK,WAAW,EAAE;4BAClD,GAAG,KAAK,mBAAmB;4BAC3B,GAAG,KAAK,kBAAkB;wBAC5B,CAAC,MAAM,EAAE;gBAEb;YAEA,KAAK;gBAAwB;oBAC3B,OACE,gBAAgB,YAAY,CAAC,mBAAmB,EAAE,IAChD,CAAC,MAAM,QAAU,CAAC;4BAChB,OACE,KAAK,WAAW,GAAG,MACf,YACA,KAAK,WAAW,GAAG,IACjB,YACA;4BACR,aAAa,KAAK,WAAW;4BAC7B,MAAM,KAAK,YAAY;4BACvB,GAAG,KAAK,kBAAkB;4BAC1B,GAAG,KAAK,gBAAgB;wBAC1B,CAAC,MACE,EAAE;gBAEX;YAEA,KAAK;gBAAmB;oBACtB,OACE,gBAAgB,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,MAAM,QAAU,CAAC;4BACjE,OACE,KAAK,WAAW,GAAG,MACf,YACA,KAAK,WAAW,GAAG,IACjB,YACA;4BACR,aAAa,KAAK,WAAW;4BAC7B,MAAM,KAAK,QAAQ;4BACnB,GAAG,KAAK,cAAc;4BACtB,GAAG,KAAK,iBAAiB;wBAC3B,CAAC,MAAM,EAAE;gBAEb;YAEA;gBAAS;oBACP,OACE,gBAAgB,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,QAAU,CAAC;4BAC1D,OACE,KAAK,WAAW,GAAG,MACf,YACA,KAAK,WAAW,GAAG,IACjB,YACA;4BACR,aAAa,KAAK,WAAW;4BAC7B,MAAM,KAAK,UAAU;4BACrB,GAAG,KAAK,MAAM;4BACd,GAAG,KAAK,MAAM;wBAChB,CAAC,MAAM,EAAE;gBAEb;QACF;IACF,GAAG;QAAC;QAAiB;KAAgB;IAErC,uBAAuB;IACvB,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAAW;;;;;;;;;;;;8BAIlC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,aAAU;wBAAC,MAAM;wBAAM,OAAO;wBAAM,WAAW;kCAC7C,IAAM;;;;;;;;;;;;;;;;;IAKjB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAAW;;;;;;;;;;;;8BAIlC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,4IAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;;;;;;;;;;;;IAI7B;IAEA,MAAM,qBAAqB,iBAAiB,WAAW;QACrD,iBAAiB;QACjB,mBAAmB;QACnB,qBAAqB;QACrB,gBAAgB;IAClB;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAW;;;;;;;sCAGhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;oCAAU,SAAQ;;wCAChC,UAAU,MAAM;wCAAC;;;;;;;8CAEpB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;8CACxB,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,aAAY;gCACZ,oBAAM,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCACvB,OAAM;gCACN,OAAO,mBAAmB,eAAe,IAAI;;;;;;0CAE/C,8OAAC;gCACC,aAAY;gCACZ,oBAAM,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAC7B,OAAM;gCACN,OAAO,mBAAmB,cAAc,IAAI;;;;;;0CAE9C,8OAAC;gCACC,aAAY;gCACZ,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAC5B,OAAM;gCACN,OAAO,mBAAmB,mBAAmB,IAAI;;;;;;0CAEnD,8OAAC;gCACC,aAAY;gCACZ,oBAAM,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCACzB,OAAM;gCACN,OAAO,mBAAmB,iBAAiB,IAAI;;;;;;;;;;;;kCAKnD,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;4BAGxC,UAAU,MAAM,GAAG,kBAClB,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,QAAQ;gCAAK,OAAM;0CACtC,cAAA,8OAAC,wJAAA,CAAA,eAAY;oCACX,MAAM;oCACN,QAAQ;wCACN,QAAQ;wCACR,MAAM;wCACN,OAAO;wCACP,KAAK;oCACP;;sDAEA,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,QAAO;4CAAU,iBAAgB;;;;;;sDAChD,8OAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAI,UAAU;4CAAI,MAAK;4CAAS,MAAK;;;;;;sDACpD,8OAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAI,UAAU;4CAAI,MAAK;4CAAS,MAAK;;;;;;wCACnD,6BAAe,8OAAC,uJAAA,CAAA,UAAO;4CAAC,uBAAS,8OAAC;;;;;;;;;;sDACnC,8OAAC,uJAAA,CAAA,UAAO;4CAAC,SAAQ;4CAAI,MAAK;sDACvB,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,8OAAC,oJAAA,CAAA,OAAI;oDAAC,MAAM,MAAM,KAAK;mDAAO,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;qDAMrD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;oBAOV,iBAAiB,YAAY,gBAAgB,QAAQ,CAAC,MAAM,GAAG,mBAC9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAClD,8OAAC;wCACC,WAAU;kDAGV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEACV,QAAQ,KAAK;;;;;;sEAEhB,8OAAC;4DAAE,WAAU;sEACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;uCATrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBvB;uCAEe", "debugId": null}}, {"offset": {"line": 9962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/EntityRelationshipNetworkWidget.tsx"], "sourcesContent": ["/**\r\n * @file EntityRelationshipNetworkWidget.tsx\r\n * @description Entity relationship network widget showing connections between system entities\r\n */\r\n\r\nimport React, { useMemo } from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Avatar, AvatarFallback } from '@/components/ui/avatar';\r\nimport {\r\n  Network,\r\n  Users,\r\n  Car,\r\n  CheckSquare,\r\n  FileText,\r\n  ArrowRight,\r\n  MoreHorizontal,\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { useCrossEntityAnalytics } from '../../hooks/useCrossEntityAnalytics';\r\nimport { DataLoader } from '@/components/ui/data-loader';\r\nimport { ErrorDisplay } from '@/components/ui/error-display';\r\nimport type { ReportingFilters } from '../../data/types/reporting';\r\n\r\n/**\r\n * Props interface for EntityRelationshipNetworkWidget\r\n */\r\ninterface EntityRelationshipNetworkWidgetProps {\r\n  filters?: ReportingFilters;\r\n  className?: string;\r\n  maxConnections?: number;\r\n  showDetails?: boolean;\r\n}\r\n\r\n/**\r\n * Entity node component\r\n */\r\ninterface EntityNodeProps {\r\n  entity: {\r\n    id: string;\r\n    name: string;\r\n    type: 'employee' | 'vehicle' | 'task' | 'delegation';\r\n    connections: number;\r\n    strength: number;\r\n  };\r\n  size?: 'sm' | 'md' | 'lg';\r\n}\r\n\r\nconst EntityNode: React.FC<EntityNodeProps> = ({ entity, size = 'md' }) => {\r\n  const getEntityIcon = (type: string) => {\r\n    switch (type) {\r\n      case 'employee':\r\n        return <Users className=\"h-4 w-4\" />;\r\n      case 'vehicle':\r\n        return <Car className=\"h-4 w-4\" />;\r\n      case 'task':\r\n        return <CheckSquare className=\"h-4 w-4\" />;\r\n      case 'delegation':\r\n        return <FileText className=\"h-4 w-4\" />;\r\n      default:\r\n        return <Network className=\"h-4 w-4\" />;\r\n    }\r\n  };\r\n\r\n  const getEntityColor = (type: string) => {\r\n    switch (type) {\r\n      case 'employee':\r\n        return 'bg-blue-100 text-blue-700 border-blue-200';\r\n      case 'vehicle':\r\n        return 'bg-green-100 text-green-700 border-green-200';\r\n      case 'task':\r\n        return 'bg-orange-100 text-orange-700 border-orange-200';\r\n      case 'delegation':\r\n        return 'bg-purple-100 text-purple-700 border-purple-200';\r\n      default:\r\n        return 'bg-gray-100 text-gray-700 border-gray-200';\r\n    }\r\n  };\r\n\r\n  const sizeClasses = {\r\n    sm: 'p-2 text-xs',\r\n    md: 'p-3 text-sm',\r\n    lg: 'p-4 text-base',\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'border rounded-lg flex items-center gap-2',\r\n        getEntityColor(entity.type),\r\n        sizeClasses[size]\r\n      )}\r\n    >\r\n      {getEntityIcon(entity.type)}\r\n      <div className=\"flex-1 min-w-0\">\r\n        <div className=\"font-medium truncate\">{entity.name}</div>\r\n        <div className=\"text-xs opacity-75\">\r\n          {entity.connections} connections\r\n        </div>\r\n      </div>\r\n      <Badge variant=\"outline\" className=\"text-xs\">\r\n        {Math.round(entity.strength * 100)}%\r\n      </Badge>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * Connection component showing relationship between entities\r\n */\r\ninterface ConnectionProps {\r\n  from: EntityNodeProps['entity'];\r\n  to: EntityNodeProps['entity'];\r\n  strength: number;\r\n  type: string;\r\n}\r\n\r\nconst Connection: React.FC<ConnectionProps> = ({\r\n  from,\r\n  to,\r\n  strength,\r\n  type,\r\n}) => {\r\n  const getConnectionColor = (strength: number) => {\r\n    if (strength >= 0.8) return 'border-green-500 bg-green-50';\r\n    if (strength >= 0.6) return 'border-blue-500 bg-blue-50';\r\n    if (strength >= 0.4) return 'border-orange-500 bg-orange-50';\r\n    return 'border-gray-500 bg-gray-50';\r\n  };\r\n\r\n  return (\r\n    <div className={cn('p-3 border rounded-lg', getConnectionColor(strength))}>\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center gap-2 flex-1\">\r\n          <EntityNode entity={from} size=\"sm\" />\r\n          <ArrowRight className=\"h-4 w-4 text-gray-400\" />\r\n          <EntityNode entity={to} size=\"sm\" />\r\n        </div>\r\n        <div className=\"text-right\">\r\n          <Badge variant=\"outline\" className=\"text-xs\">\r\n            {Math.round(strength * 100)}%\r\n          </Badge>\r\n          <div className=\"text-xs text-gray-500 mt-1\">{type}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * EntityRelationshipNetworkWidget Component\r\n *\r\n * Displays entity relationships and network connections in the system.\r\n *\r\n * Responsibilities:\r\n * - Show network of relationships between entities\r\n * - Visualize connection strengths and types\r\n * - Follow established widget composition patterns\r\n * - Maintain consistent styling and behavior\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying entity relationships\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on existing widget framework abstractions\r\n */\r\nexport const EntityRelationshipNetworkWidget: React.FC<\r\n  EntityRelationshipNetworkWidgetProps\r\n> = ({ filters, className = '', maxConnections = 10, showDetails = true }) => {\r\n  // Use hook for cross-entity analytics\r\n  const {\r\n    data: networkData,\r\n    isLoading,\r\n    error,\r\n  } = useCrossEntityAnalytics(filters);\r\n\r\n  // Transform data for network visualization\r\n  const { entities, connections } = useMemo(() => {\r\n    if (!networkData?.network) {\r\n      return { entities: [], connections: [] };\r\n    }\r\n\r\n    const entities =\r\n      networkData.network.nodes?.map(node => ({\r\n        id: node.id,\r\n        name: node.name,\r\n        type: node.type as 'employee' | 'vehicle' | 'task' | 'delegation',\r\n        connections: node.connectionCount || 0,\r\n        strength: node.strength || 0,\r\n      })) || [];\r\n\r\n    const connections =\r\n      networkData.network.edges?.slice(0, maxConnections).map(edge => ({\r\n        from: entities.find(e => e.id === edge.from) || entities[0],\r\n        to: entities.find(e => e.id === edge.to) || entities[0],\r\n        strength: edge.weight || 0,\r\n        type: edge.type || 'related',\r\n      })) || [];\r\n\r\n    return { entities, connections };\r\n  }, [networkData, maxConnections]);\r\n\r\n  // Handle loading state\r\n  if (isLoading) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Network className=\"h-5 w-5\" />\r\n            Entity Relationship Network\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <DataLoader isLoading={true} data={null} error={null}>\r\n            {() => null}\r\n          </DataLoader>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Handle error state\r\n  if (error) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Network className=\"h-5 w-5\" />\r\n            Entity Relationship Network\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <ErrorDisplay error={error} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Calculate network statistics\r\n  const totalEntities = entities.length;\r\n  const totalConnections = connections.length;\r\n  const avgConnectionStrength =\r\n    connections.length > 0\r\n      ? connections.reduce((sum, conn) => sum + conn.strength, 0) /\r\n        connections.length\r\n      : 0;\r\n\r\n  const entityTypes = entities.reduce(\r\n    (acc, entity) => {\r\n      acc[entity.type] = (acc[entity.type] || 0) + 1;\r\n      return acc;\r\n    },\r\n    {} as Record<string, number>\r\n  );\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader className=\"pb-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Network className=\"h-5 w-5\" />\r\n            Entity Relationship Network\r\n          </CardTitle>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              {totalEntities} entities\r\n            </Badge>\r\n            <Badge variant=\"outline\" className=\"text-xs\">\r\n              {totalConnections} connections\r\n            </Badge>\r\n            <Button variant=\"ghost\" size=\"sm\">\r\n              <MoreHorizontal className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-6\">\r\n        {/* Network Statistics */}\r\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\r\n          <div className=\"text-center p-3 bg-blue-50 rounded-lg\">\r\n            <div className=\"text-2xl font-bold text-blue-700\">\r\n              {entityTypes.employee || 0}\r\n            </div>\r\n            <div className=\"text-xs text-blue-600\">Employees</div>\r\n          </div>\r\n          <div className=\"text-center p-3 bg-green-50 rounded-lg\">\r\n            <div className=\"text-2xl font-bold text-green-700\">\r\n              {entityTypes.vehicle || 0}\r\n            </div>\r\n            <div className=\"text-xs text-green-600\">Vehicles</div>\r\n          </div>\r\n          <div className=\"text-center p-3 bg-orange-50 rounded-lg\">\r\n            <div className=\"text-2xl font-bold text-orange-700\">\r\n              {entityTypes.task || 0}\r\n            </div>\r\n            <div className=\"text-xs text-orange-600\">Tasks</div>\r\n          </div>\r\n          <div className=\"text-center p-3 bg-purple-50 rounded-lg\">\r\n            <div className=\"text-2xl font-bold text-purple-700\">\r\n              {entityTypes.delegation || 0}\r\n            </div>\r\n            <div className=\"text-xs text-purple-600\">Delegations</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Top Connected Entities */}\r\n        <div>\r\n          <h4 className=\"text-sm font-medium mb-3\">Most Connected Entities</h4>\r\n          <div className=\"space-y-2\">\r\n            {entities\r\n              .sort((a, b) => b.connections - a.connections)\r\n              .slice(0, 5)\r\n              .map((entity, index) => (\r\n                <EntityNode key={entity.id} entity={entity} />\r\n              ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Strongest Connections */}\r\n        {showDetails && connections.length > 0 && (\r\n          <div>\r\n            <h4 className=\"text-sm font-medium mb-3\">Strongest Connections</h4>\r\n            <div className=\"space-y-3\">\r\n              {connections\r\n                .sort((a, b) => b.strength - a.strength)\r\n                .slice(0, 5)\r\n                .filter(connection => connection.from && connection.to)\r\n                .map((connection, index) => (\r\n                  <Connection\r\n                    key={`${connection.from!.id}-${connection.to!.id}-${index}`}\r\n                    from={connection.from!}\r\n                    to={connection.to!}\r\n                    strength={connection.strength}\r\n                    type={connection.type}\r\n                  />\r\n                ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Network Health Indicator */}\r\n        <div className=\"p-4 bg-gray-50 rounded-lg\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <span className=\"text-sm font-medium\">Network Health</span>\r\n            <Badge\r\n              variant={\r\n                avgConnectionStrength > 0.7\r\n                  ? 'default'\r\n                  : avgConnectionStrength > 0.4\r\n                    ? 'secondary'\r\n                    : 'destructive'\r\n              }\r\n              className=\"text-xs\"\r\n            >\r\n              {avgConnectionStrength > 0.7\r\n                ? 'Strong'\r\n                : avgConnectionStrength > 0.4\r\n                  ? 'Moderate'\r\n                  : 'Weak'}\r\n            </Badge>\r\n          </div>\r\n          <div className=\"text-xs text-gray-600 mt-1\">\r\n            Average connection strength:{' '}\r\n            {Math.round(avgConnectionStrength * 100)}%\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default EntityRelationshipNetworkWidget;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAAA;AACA;AACA;AAAA;AACA;;;;;;;;;;;AA2BA,MAAM,aAAwC,CAAC,EAAE,MAAM,EAAE,OAAO,IAAI,EAAE;IACpE,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B;gBACE,qBAAO,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;QAC9B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,6CACA,eAAe,OAAO,IAAI,GAC1B,WAAW,CAAC,KAAK;;YAGlB,cAAc,OAAO,IAAI;0BAC1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAwB,OAAO,IAAI;;;;;;kCAClD,8OAAC;wBAAI,WAAU;;4BACZ,OAAO,WAAW;4BAAC;;;;;;;;;;;;;0BAGxB,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;;oBAChC,KAAK,KAAK,CAAC,OAAO,QAAQ,GAAG;oBAAK;;;;;;;;;;;;;AAI3C;AAYA,MAAM,aAAwC,CAAC,EAC7C,IAAI,EACJ,EAAE,EACF,QAAQ,EACR,IAAI,EACL;IACC,MAAM,qBAAqB,CAAC;QAC1B,IAAI,YAAY,KAAK,OAAO;QAC5B,IAAI,YAAY,KAAK,OAAO;QAC5B,IAAI,YAAY,KAAK,OAAO;QAC5B,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,mBAAmB;kBAC7D,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAW,QAAQ;4BAAM,MAAK;;;;;;sCAC/B,8OAAC,kNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;sCACtB,8OAAC;4BAAW,QAAQ;4BAAI,MAAK;;;;;;;;;;;;8BAE/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;;gCAChC,KAAK,KAAK,CAAC,WAAW;gCAAK;;;;;;;sCAE9B,8OAAC;4BAAI,WAAU;sCAA8B;;;;;;;;;;;;;;;;;;;;;;;AAKvD;AAkBO,MAAM,kCAET,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE,iBAAiB,EAAE,EAAE,cAAc,IAAI,EAAE;IACvE,sCAAsC;IACtC,MAAM,EACJ,MAAM,WAAW,EACjB,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,8KAAA,CAAA,0BAAuB,AAAD,EAAE;IAE5B,2CAA2C;IAC3C,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxC,IAAI,CAAC,aAAa,SAAS;YACzB,OAAO;gBAAE,UAAU,EAAE;gBAAE,aAAa,EAAE;YAAC;QACzC;QAEA,MAAM,WACJ,YAAY,OAAO,CAAC,KAAK,EAAE,IAAI,CAAA,OAAQ,CAAC;gBACtC,IAAI,KAAK,EAAE;gBACX,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,aAAa,KAAK,eAAe,IAAI;gBACrC,UAAU,KAAK,QAAQ,IAAI;YAC7B,CAAC,MAAM,EAAE;QAEX,MAAM,cACJ,YAAY,OAAO,CAAC,KAAK,EAAE,MAAM,GAAG,gBAAgB,IAAI,CAAA,OAAQ,CAAC;gBAC/D,MAAM,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,IAAI,KAAK,QAAQ,CAAC,EAAE;gBAC3D,IAAI,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE,KAAK,QAAQ,CAAC,EAAE;gBACvD,UAAU,KAAK,MAAM,IAAI;gBACzB,MAAM,KAAK,IAAI,IAAI;YACrB,CAAC,MAAM,EAAE;QAEX,OAAO;YAAE;YAAU;QAAY;IACjC,GAAG;QAAC;QAAa;KAAe;IAEhC,uBAAuB;IACvB,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAInC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,aAAU;wBAAC,WAAW;wBAAM,MAAM;wBAAM,OAAO;kCAC7C,IAAM;;;;;;;;;;;;;;;;;IAKjB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAInC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,4IAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;;;;;;;;;;;;IAI7B;IAEA,+BAA+B;IAC/B,MAAM,gBAAgB,SAAS,MAAM;IACrC,MAAM,mBAAmB,YAAY,MAAM;IAC3C,MAAM,wBACJ,YAAY,MAAM,GAAG,IACjB,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE,KACvD,YAAY,MAAM,GAClB;IAEN,MAAM,cAAc,SAAS,MAAM,CACjC,CAAC,KAAK;QACJ,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;QAC7C,OAAO;IACT,GACA,CAAC;IAGH,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;wCAClC;wCAAc;;;;;;;8CAEjB,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAChC;wCAAiB;;;;;;;8CAEpB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,YAAY,QAAQ,IAAI;;;;;;kDAE3B,8OAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAEzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,YAAY,OAAO,IAAI;;;;;;kDAE1B,8OAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;0CAE1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,YAAY,IAAI,IAAI;;;;;;kDAEvB,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;;;;;;;0CAE3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,YAAY,UAAU,IAAI;;;;;;kDAE7B,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;kCAK7C,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,8OAAC;gCAAI,WAAU;0CACZ,SACE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW,EAC5C,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,QAAQ,sBACZ,8OAAC;wCAA2B,QAAQ;uCAAnB,OAAO,EAAE;;;;;;;;;;;;;;;;oBAMjC,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,8OAAC;gCAAI,WAAU;0CACZ,YACE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ,EACtC,KAAK,CAAC,GAAG,GACT,MAAM,CAAC,CAAA,aAAc,WAAW,IAAI,IAAI,WAAW,EAAE,EACrD,GAAG,CAAC,CAAC,YAAY,sBAChB,8OAAC;wCAEC,MAAM,WAAW,IAAI;wCACrB,IAAI,WAAW,EAAE;wCACjB,UAAU,WAAW,QAAQ;wCAC7B,MAAM,WAAW,IAAI;uCAJhB,GAAG,WAAW,IAAI,CAAE,EAAE,CAAC,CAAC,EAAE,WAAW,EAAE,CAAE,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;kCAYvE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAsB;;;;;;kDACtC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,SACE,wBAAwB,MACpB,YACA,wBAAwB,MACtB,cACA;wCAER,WAAU;kDAET,wBAAwB,MACrB,WACA,wBAAwB,MACtB,aACA;;;;;;;;;;;;0CAGV,8OAAC;gCAAI,WAAU;;oCAA6B;oCACb;oCAC5B,KAAK,KAAK,CAAC,wBAAwB;oCAAK;;;;;;;;;;;;;;;;;;;;;;;;;AAMrD;uCAEe", "debugId": null}}, {"offset": {"line": 10663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/widgets/index.ts"], "sourcesContent": ["export * from './DelegationStatusWidget';\r\nexport * from './DelegationTrendWidget';\r\nexport * from './LocationDistributionWidget';\r\nexport * from './TaskMetricsWidget';\r\nexport * from './ReportingTableWidget';\r\nexport * from './SingleDelegationWidget';\r\nexport * from './SingleTaskWidget';\r\nexport * from './BaseWidget';\r\n\r\n// Phase 2: Task Reporting Components\r\nexport * from './TaskStatusChart';\r\nexport * from './TaskPriorityDistribution';\r\nexport * from './TaskAssignmentMetrics';\r\n\r\n// Phase 2: Vehicle Reporting Components\r\nexport * from './VehicleAnalyticsWidget';\r\nexport * from './VehicleUtilizationChart';\r\nexport * from './VehicleMaintenanceWidget';\r\nexport * from './VehicleCostAnalyticsWidget';\r\n\r\n// Phase 2: Employee Reporting Components\r\nexport * from './EmployeeAnalyticsWidget';\r\nexport * from './EmployeePerformanceChart';\r\nexport * from './EmployeeWorkloadWidget';\r\n\r\n// Phase 2: Cross-Entity Correlation Components\r\nexport * from './CrossEntityCorrelationWidget';\r\nexport * from './EntityRelationshipNetworkWidget';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,qCAAqC;AACrC;AACA;AACA;AAEA,wCAAwC;AACxC;AACA;AACA;AACA;AAEA,yCAAyC;AACzC;AACA;AACA;AAEA,+CAA+C;AAC/C;AACA", "debugId": null}}]}