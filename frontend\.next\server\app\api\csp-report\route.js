(()=>{var e={};e.id=2727,e.ids=[2727],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12906:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>u,OPTIONS:()=>p,POST:()=>c});var o=t(96559),n=t(48088),i=t(37719),a=t(32190);async function c(e){try{let r=e.headers.get("content-type");if(!r?.includes("application/csp-report")&&!r?.includes("application/json"))return a.NextResponse.json({error:"Invalid content type"},{status:400});let t=(await e.json())["csp-report"];if(!t)return a.NextResponse.json({error:"Invalid CSP report format"},{status:400});let s={timestamp:new Date().toISOString(),documentUri:t["document-uri"],violatedDirective:t["violated-directive"],effectiveDirective:t["effective-directive"],blockedUri:t["blocked-uri"],sourceFile:t["source-file"],lineNumber:t["line-number"],columnNumber:t["column-number"],scriptSample:t["script-sample"],userAgent:e.headers.get("user-agent"),ip:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")};if(console.error("CSP Violation:",s),["chrome-extension:","moz-extension:","safari-extension:","about:blank","data:text/html,chromewebdata"].some(e=>s.blockedUri?.includes(e)))return a.NextResponse.json({status:"ignored"},{status:200});return["script-src","object-src","base-uri"].includes(s.effectiveDirective)&&console.error("\uD83D\uDEA8 CRITICAL CSP VIOLATION:",s),a.NextResponse.json({status:"received"},{status:200})}catch(e){return console.error("Error processing CSP report:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(){return a.NextResponse.json({},{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}async function u(){return a.NextResponse.json({error:"Method not allowed"},{status:405})}let d=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/csp-report/route",pathname:"/api/csp-report",filename:"route",bundlePath:"app/api/csp-report/route"},resolvedPagePath:"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\api\\csp-report\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:m}=d;function v(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,580],()=>t(12906));module.exports=s})();