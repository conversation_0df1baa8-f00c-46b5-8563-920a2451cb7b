// frontend/src/components/features/reporting/data/hooks/useRealtimeReportingUpdates.ts

'use client';

import { useEffect, useRef, useCallback, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { getEnvironmentConfig } from '../../../../../lib/config/environment';
import { reportingQueryKeys } from './useReportingQueries';
import { ReportingFilters } from '../types/reporting';

/**
 * WebSocket connection states for better UX feedback
 */
export enum WebSocketConnectionState {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

/**
 * Real-time update event types for reporting data
 */
export interface ReportingUpdateEvent {
  type:
    | 'delegation-updated'
    | 'task-updated'
    | 'analytics-refresh'
    | 'location-updated';
  data: {
    delegationId?: string;
    taskId?: string;
    affectedFilters?: Partial<ReportingFilters>;
    timestamp: string;
  };
}

/**
 * Configuration options for the WebSocket connection
 */
export interface WebSocketConfig {
  url?: string;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  batchUpdateDelay?: number;
}

/**
 * Hook for managing real-time reporting updates via WebSocket
 *
 * Follows SRP: Only responsible for WebSocket connection management and real-time updates
 * Does NOT handle initial data fetching or UI state - that's handled by React Query hooks
 *
 * @param filters - Current reporting filters to subscribe to relevant updates
 * @param config - WebSocket configuration options
 * @returns Connection state and control functions
 */
export const useRealtimeReportingUpdates = (
  filters: ReportingFilters,
  config: WebSocketConfig = {}
) => {
  const queryClient = useQueryClient();
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const updateBatchRef = useRef<Set<string>>(new Set());
  const batchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Configuration with defaults using environment-aware configuration
  const envConfig = getEnvironmentConfig();
  const {
    url = `${envConfig.wsUrl}/ws/reporting`,
    reconnectInterval = 3000,
    maxReconnectAttempts = 5,
    heartbeatInterval = 30000,
    batchUpdateDelay = 1000,
  } = config;

  // Connection state management
  const [connectionState, setConnectionState] =
    useState<WebSocketConnectionState>(WebSocketConnectionState.DISCONNECTED);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [lastError, setLastError] = useState<string | null>(null);

  /**
   * Batched cache invalidation to prevent excessive re-renders
   * Collects multiple updates and processes them together
   */
  const processBatchedUpdates = useCallback(() => {
    if (updateBatchRef.current.size === 0) return;

    const updates = Array.from(updateBatchRef.current);
    updateBatchRef.current.clear();

    // Invalidate relevant queries based on batched updates
    const queryKeysToInvalidate = new Set<string>();

    updates.forEach(updateType => {
      switch (updateType) {
        case 'delegation-updated':
          queryKeysToInvalidate.add('delegation-analytics');
          queryKeysToInvalidate.add('trends');
          queryKeysToInvalidate.add('location-metrics');
          break;
        case 'task-updated':
          queryKeysToInvalidate.add('task-metrics');
          break;
        case 'analytics-refresh':
          queryKeysToInvalidate.add('delegation-analytics');
          queryKeysToInvalidate.add('trends');
          queryKeysToInvalidate.add('location-metrics');
          queryKeysToInvalidate.add('task-metrics');
          break;
        case 'location-updated':
          queryKeysToInvalidate.add('location-metrics');
          break;
      }
    });

    // Invalidate queries efficiently
    queryKeysToInvalidate.forEach(queryType => {
      switch (queryType) {
        case 'delegation-analytics':
          queryClient.invalidateQueries({
            queryKey: reportingQueryKeys.analytics(),
          });
          break;
        case 'task-metrics':
          queryClient.invalidateQueries({
            queryKey: [...reportingQueryKeys.all, 'tasks', 'metrics'],
          });
          break;
        case 'trends':
          queryClient.invalidateQueries({
            queryKey: [...reportingQueryKeys.all, 'trends'],
          });
          break;
        case 'location-metrics':
          queryClient.invalidateQueries({
            queryKey: [...reportingQueryKeys.all, 'locations', 'metrics'],
          });
          break;
      }
    });
  }, [queryClient]);

  /**
   * Handles incoming WebSocket messages with proper error handling
   */
  const handleMessage = useCallback(
    (event: MessageEvent) => {
      try {
        const message: ReportingUpdateEvent = JSON.parse(event.data);

        // Add to batch for processing
        updateBatchRef.current.add(message.type);

        // Clear existing batch timeout and set new one
        if (batchTimeoutRef.current) {
          clearTimeout(batchTimeoutRef.current);
        }

        batchTimeoutRef.current = setTimeout(() => {
          processBatchedUpdates();
        }, batchUpdateDelay);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    },
    [processBatchedUpdates, batchUpdateDelay]
  );

  /**
   * Establishes WebSocket connection with proper error handling
   */
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    setConnectionState(WebSocketConnectionState.CONNECTING);
    setLastError(null);

    try {
      const ws = new WebSocket(url);
      wsRef.current = ws;

      ws.onopen = () => {
        setConnectionState(WebSocketConnectionState.CONNECTED);
        setReconnectAttempts(0);

        // Send subscription message with current filters
        ws.send(
          JSON.stringify({
            type: 'subscribe',
            filters: filters,
            timestamp: new Date().toISOString(),
          })
        );

        // Start heartbeat
        heartbeatIntervalRef.current = setInterval(() => {
          if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({ type: 'ping' }));
          }
        }, heartbeatInterval);
      };

      ws.onmessage = handleMessage;

      ws.onclose = event => {
        setConnectionState(WebSocketConnectionState.DISCONNECTED);

        // Clear heartbeat
        if (heartbeatIntervalRef.current) {
          clearInterval(heartbeatIntervalRef.current);
          heartbeatIntervalRef.current = null;
        }

        // Attempt reconnection if not a clean close
        if (!event.wasClean && reconnectAttempts < maxReconnectAttempts) {
          setConnectionState(WebSocketConnectionState.RECONNECTING);
          setReconnectAttempts(prev => prev + 1);

          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        }
      };

      ws.onerror = error => {
        console.error('WebSocket error:', error);
        setConnectionState(WebSocketConnectionState.ERROR);
        setLastError('WebSocket connection failed');
      };
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setConnectionState(WebSocketConnectionState.ERROR);
      setLastError(
        error instanceof Error ? error.message : 'Unknown connection error'
      );
    }
  }, [
    url,
    filters,
    handleMessage,
    reconnectAttempts,
    maxReconnectAttempts,
    reconnectInterval,
    heartbeatInterval,
  ]);

  /**
   * Cleanly disconnects WebSocket connection
   */
  const disconnect = useCallback(() => {
    // Clear all timeouts
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = null;
    }

    if (batchTimeoutRef.current) {
      clearTimeout(batchTimeoutRef.current);
      batchTimeoutRef.current = null;
    }

    // Close WebSocket connection
    if (wsRef.current) {
      wsRef.current.close(1000, 'Component unmounting');
      wsRef.current = null;
    }

    setConnectionState(WebSocketConnectionState.DISCONNECTED);
    setReconnectAttempts(0);
    setLastError(null);
  }, []);

  /**
   * Manually trigger reconnection
   */
  const reconnect = useCallback(() => {
    disconnect();
    setReconnectAttempts(0);
    setTimeout(connect, 100); // Small delay to ensure clean disconnect
  }, [disconnect, connect]);

  // Auto-connect on mount and filter changes
  useEffect(() => {
    connect();
    return disconnect;
  }, [connect, disconnect]);

  // Update subscription when filters change
  useEffect(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(
        JSON.stringify({
          type: 'update-subscription',
          filters: filters,
          timestamp: new Date().toISOString(),
        })
      );
    }
  }, [filters]);

  return {
    connectionState,
    reconnectAttempts,
    lastError,
    connect,
    disconnect,
    reconnect,
    isConnected: connectionState === WebSocketConnectionState.CONNECTED,
    isConnecting: connectionState === WebSocketConnectionState.CONNECTING,
    isReconnecting: connectionState === WebSocketConnectionState.RECONNECTING,
    hasError: connectionState === WebSocketConnectionState.ERROR,
  };
};
