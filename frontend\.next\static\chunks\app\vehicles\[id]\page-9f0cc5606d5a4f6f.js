(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1272],{1190:(e,r,t)=>{"use strict";t.d(r,{fe:()=>c,mm:()=>i,oS:()=>n});var a=t(71153),s=t(21876);let l={date:a.Yj().min(1,"Date is required"),servicePerformed:a.YO(a.Yj().min(1,"Service cannot be empty")).min(1,"At least one service must be performed").max(10,"Maximum 10 services allowed"),odometer:a.au.number({required_error:"Odometer reading is required"}).min(0,"Odometer cannot be negative").max(9999999,"Odometer reading seems unrealistic"),cost:a.au.number({required_error:"Cost is required"}).min(0,"Cost cannot be negative").max(999999,"Cost seems unrealistic"),notes:a.Yj().max(1e3,"Notes cannot exceed 1000 characters").optional(),employeeId:a.au.number().nullable().optional()},i=a.Ik(l),n=a.Ik({...l,vehicleId:a.ai().positive("Vehicle ID is required")});a.Ik(l).partial();let c={validateFormData:e=>i.safeParse(e),transformToApiPayload:(e,r)=>{let t={date:e.date,servicePerformed:e.servicePerformed,odometer:e.odometer,vehicleId:r};return"number"==typeof e.cost&&e.cost>=0&&(t.cost=e.cost),e.notes&&e.notes.trim().length>0&&(t.notes=e.notes.trim()),void 0!==e.employeeId&&(t.employeeId=e.employeeId),t},transformToUpdatePayload:e=>{let r={};return e.date&&(r.date=(0,s.B7)(e.date,{primaryFormat:"US",fallbackToBothFormats:!0})),void 0!==e.servicePerformed&&(r.servicePerformed=e.servicePerformed),void 0!==e.odometer&&(r.odometer=e.odometer),"number"==typeof e.cost&&e.cost>0?r.cost=e.cost:0===e.cost&&(r.cost=void 0),void 0!==e.notes&&e.notes&&e.notes.trim().length>0&&(r.notes=e.notes.trim()),void 0!==e.employeeId&&(r.employeeId=e.employeeId),r},validateServicePerformed:e=>e.length>0&&e.length<=10&&e.every(e=>e.trim().length>0),formatServicePerformed:e=>e.join(", "),parseServicePerformed:e=>e.split(",").map(e=>e.trim()).filter(e=>e.length>0)}},3235:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},3638:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},10233:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("ChevronsRight",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},15300:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},17759:(e,r,t)=>{"use strict";t.d(r,{C5:()=>g,MJ:()=>v,Rr:()=>f,eI:()=>p,lR:()=>x,lV:()=>o,zB:()=>m});var a=t(95155),s=t(12115),l=t(99708),i=t(62177),n=t(54036),c=t(85057);let o=i.Op,d=s.createContext({}),m=e=>{let{...r}=e;return(0,a.jsx)(d.Provider,{value:{name:r.name},children:(0,a.jsx)(i.xI,{...r})})},u=()=>{let e=s.useContext(d),r=s.useContext(h),{getFieldState:t,formState:a}=(0,i.xW)(),l=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=r;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...l}},h=s.createContext({}),p=s.forwardRef((e,r)=>{let{className:t,...l}=e,i=s.useId();return(0,a.jsx)(h.Provider,{value:{id:i},children:(0,a.jsx)("div",{ref:r,className:(0,n.cn)("space-y-2",t),...l})})});p.displayName="FormItem";let x=s.forwardRef((e,r)=>{let{className:t,...s}=e,{error:l,formItemId:i}=u();return(0,a.jsx)(c.J,{ref:r,className:(0,n.cn)(l&&"text-destructive",t),htmlFor:i,...s})});x.displayName="FormLabel";let v=s.forwardRef((e,r)=>{let{...t}=e,{error:s,formItemId:i,formDescriptionId:n,formMessageId:c}=u();return(0,a.jsx)(l.DX,{ref:r,id:i,"aria-describedby":s?"".concat(n," ").concat(c):"".concat(n),"aria-invalid":!!s,...t})});v.displayName="FormControl";let f=s.forwardRef((e,r)=>{let{className:t,...s}=e,{formDescriptionId:l}=u();return(0,a.jsx)("p",{ref:r,id:l,className:(0,n.cn)("text-sm text-muted-foreground",t),...s})});f.displayName="FormDescription";let g=s.forwardRef((e,r)=>{var t;let{className:s,children:l,...i}=e,{error:c,formMessageId:o}=u(),d=c?String(null!=(t=null==c?void 0:c.message)?t:""):l;return d?(0,a.jsx)("p",{ref:r,id:o,className:(0,n.cn)("text-sm font-medium text-destructive",s),...i,children:d}):null});g.displayName="FormMessage"},19018:(e,r,t)=>{"use strict";t.d(r,{Breadcrumb:()=>c,BreadcrumbItem:()=>d,BreadcrumbLink:()=>m,BreadcrumbList:()=>o,BreadcrumbPage:()=>u,BreadcrumbSeparator:()=>h});var a=t(95155),s=t(99708),l=t(73158),i=(t(3561),t(12115)),n=t(54036);let c=i.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("nav",{"aria-label":"breadcrumb",className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground",t),ref:r,...s})});c.displayName="Breadcrumb";let o=i.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("ol",{className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm",t),ref:r,...s})});o.displayName="BreadcrumbList";let d=i.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("li",{className:(0,n.cn)("inline-flex items-center gap-1.5",t),ref:r,...s})});d.displayName="BreadcrumbItem";let m=i.forwardRef((e,r)=>{let{asChild:t,className:l,...i}=e,c=t?s.DX:"a";return(0,a.jsx)(c,{className:(0,n.cn)("transition-colors hover:text-foreground",l),ref:r,...i})});m.displayName="BreadcrumbLink";let u=i.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("span",{"aria-current":"page","aria-disabled":"true",className:(0,n.cn)("font-normal text-foreground",t),ref:r,role:"link",...s})});u.displayName="BreadcrumbPage";let h=e=>{let{children:r,className:t,...s}=e;return(0,a.jsx)("span",{"aria-hidden":"true",className:(0,n.cn)("[&>svg]:size-3.5",t),role:"presentation",...s,children:null!=r?r:(0,a.jsx)(l.A,{className:"size-4"})})};h.displayName="BreadcrumbSeparator"},20203:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},24371:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},24865:(e,r,t)=>{"use strict";t.d(r,{M:()=>o});var a=t(95155),s=t(15300),l=t(61840),i=t(6874),n=t.n(i);t(12115);var c=t(6560);function o(e){let{className:r,getReportUrl:t,href:i,isList:o=!1}=e;if(!i&&!t)return console.error("ViewReportButton requires either href or getReportUrl prop"),null;let d=o?"View List Report":"View Report";return i?(0,a.jsx)(c.r,{actionType:"secondary",asChild:!0,className:r,icon:(0,a.jsx)(s.A,{className:"size-4"}),children:(0,a.jsxs)(n(),{href:i,rel:"noopener noreferrer",target:"_blank",children:[d,(0,a.jsx)(l.A,{"aria-hidden":"true",className:"ml-1.5 inline-block size-3"}),(0,a.jsx)("span",{className:"sr-only",children:"(opens in new tab)"})]})}):(0,a.jsxs)(c.r,{actionType:"secondary",className:r,icon:(0,a.jsx)(s.A,{className:"size-4"}),onClick:()=>{if(t){let e=t();window.open(e,"_blank","noopener,noreferrer")}},children:[d,(0,a.jsx)(l.A,{"aria-hidden":"true",className:"ml-1.5 inline-block size-3"})]})}},27150:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},28041:(e,r,t)=>{Promise.resolve().then(t.bind(t,80879))},28328:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("Car",[["path",{d:"M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2",key:"5owen"}],["circle",{cx:"7",cy:"17",r:"2",key:"u2ysq9"}],["path",{d:"M9 17h6",key:"r8uit2"}],["circle",{cx:"17",cy:"17",r:"2",key:"axvx0g"}]])},29159:(e,r,t)=>{"use strict";t.d(r,{s:()=>s});class a extends Error{}function s(e,r){let t;if("string"!=typeof e)throw new a("Invalid token specified: must be a string");r||(r={});let s=+(!0!==r.header),l=e.split(".")[s];if("string"!=typeof l)throw new a(`Invalid token specified: missing part #${s+1}`);try{t=function(e){let r=e.replace(/-/g,"+").replace(/_/g,"/");switch(r.length%4){case 0:break;case 2:r+="==";break;case 3:r+="=";break;default:throw Error("base64 string is not of the correct length")}try{var t;return t=r,decodeURIComponent(atob(t).replace(/(.)/g,(e,r)=>{let t=r.charCodeAt(0).toString(16).toUpperCase();return t.length<2&&(t="0"+t),"%"+t}))}catch(e){return atob(r)}}(l)}catch(e){throw new a(`Invalid token specified: invalid base64 for part #${s+1} (${e.message})`)}try{return JSON.parse(t)}catch(e){throw new a(`Invalid token specified: invalid json for part #${s+1} (${e.message})`)}}a.prototype.name="InvalidTokenError"},31896:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("ChevronsLeft",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},34301:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},34477:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{callServer:function(){return a.callServer},createServerReference:function(){return l},findSourceMapURL:function(){return s.findSourceMapURL}});let a=t(53806),s=t(31818),l=t(34979).createServerReference},44956:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},50594:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},61840:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},69321:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("Gauge",[["path",{d:"m12 14 4-4",key:"9kzdfg"}],["path",{d:"M3.34 19a10 10 0 1 1 17.32 0",key:"19p75a"}]])},73926:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},77223:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},80879:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>ee});var a=t(95155),s=t(31949),l=t(28328),i=t(18763),n=t(77223),c=t(6874),o=t.n(c),d=t(35695),m=t(12115),u=t(88240),h=t(24865),p=t(58824),x=t(6560),v=t(89440),f=t(77023),g=t(95647),y=t(53712),j=t(52747),b=t(98691),k=t(80937),N=t(40157);let A=(0,N.A)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);var w=t(50172),M=t(50594),C=t(34477);let S=(0,C.createServerReference)("404fa393737c4b6f25a5053556cb12ec079b835d4d",C.callServer,void 0,C.findSourceMapURL,"getMaintenanceSuggestions");var R=t(55365),I=t(30285),P=t(66695),B=t(62523),z=t(85057),O=t(88539);function L(e){var r,t,l,i,n;let{vehicle:c}=e,[o,d]=(0,m.useState)(!1),[u,h]=(0,m.useState)(null),[p,x]=(0,m.useState)(null),[v,f]=(0,m.useState)((null!=(l=null==(r=c.serviceHistory)?void 0:r.length)?l:0)>0?Math.max(...null!=(i=null==(t=c.serviceHistory)?void 0:t.map(e=>e.odometer))?i:[0]):null!=(n=c.initialOdometer)?n:0),g=async()=>{var e,r,t,a;if(d(!0),h(null),x(null),v<(null!=(r=c.initialOdometer)?r:0)){x("Current odometer cannot be less than initial odometer."),d(!1);return}if(null==(e=c.serviceHistory)?void 0:e.some(e=>e.odometer>v)){x("Current odometer cannot be less than a previously logged service odometer."),d(!1);return}let s={currentOdometer:v,serviceHistory:(a=null!=(t=c.serviceHistory)?t:[])&&0!==a.length?a.map(e=>"Date: ".concat(e.date,", Mileage: ").concat(e.odometer," miles, Service: ").concat(e.servicePerformed.join(", ")).concat(e.notes?", Notes: ".concat(e.notes):"").concat(e.cost?", Cost: $".concat(e.cost):"")).join("; "):"No service history available.",vehicleMake:c.make,vehicleModel:c.model,vehicleYear:c.year},l=await S(s);l.success&&l.data?h(l.data):x(l.error||"Failed to get suggestions."),d(!1)};return(0,a.jsxs)(P.Zp,{className:"bg-card shadow-lg",children:[(0,a.jsxs)(P.aR,{children:[(0,a.jsxs)(P.ZB,{className:"flex items-center text-xl font-semibold text-primary",children:[(0,a.jsx)(A,{className:"mr-2 size-5 text-accent"}),"AI Maintenance Advisor"]}),(0,a.jsx)(P.BT,{children:"Get AI-powered maintenance schedule suggestions based on your vehicle's details and service history."})]}),(0,a.jsxs)(P.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(z.J,{className:"mb-1 block text-sm font-medium",htmlFor:"currentOdometerAi",children:"Current Odometer for Suggestion"}),(0,a.jsx)(B.p,{className:"mb-4",id:"currentOdometerAi",onChange:e=>f(Number.parseInt(e.target.value,10)||0),placeholder:"Enter current mileage",type:"number",value:v})]}),(0,a.jsx)(I.$,{className:"w-full bg-accent text-accent-foreground hover:bg-accent/90",disabled:o,onClick:g,children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(w.A,{className:"mr-2 size-4 animate-spin"}),"Generating..."]}):"Get Maintenance Suggestions"}),p&&(0,a.jsxs)(R.Fc,{className:"mt-4",variant:"destructive",children:[(0,a.jsx)(s.A,{className:"size-4"}),(0,a.jsx)(R.XL,{children:"Error"}),(0,a.jsx)(R.TN,{children:p})]}),u&&(0,a.jsxs)("div",{className:"mt-6 space-y-4 rounded-lg border border-border bg-background p-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-2 text-lg font-semibold text-primary",children:"Suggested Maintenance Schedule:"}),(0,a.jsx)(O.T,{"aria-label":"Suggested Maintenance Schedule",className:"min-h-[150px] bg-muted/50",readOnly:!0,value:u.suggestedMaintenanceSchedule})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-2 text-lg font-semibold text-primary",children:"Reasoning:"}),(0,a.jsx)(O.T,{"aria-label":"Reasoning for suggestions",className:"min-h-[100px] bg-muted/50",readOnly:!0,value:u.reasoning})]})]}),!u&&!o&&!p&&(0,a.jsxs)(R.Fc,{className:"mt-4 border-primary/30",children:[(0,a.jsx)(M.A,{className:"size-4 text-primary"}),(0,a.jsx)(R.XL,{className:"text-primary",children:"Ready for Advice?"}),(0,a.jsx)(R.TN,{children:"Enter your vehicle's current odometer reading above and click the button to receive personalized maintenance suggestions from our AI Advisor."})]})]})]})}var T=t(90221),F=t(34301),q=t(62177),V=t(47262),D=t(17759),H=t(83940),E=t(21876),_=t(1190);let U=["Oil Change","Tire Rotation","Brake Service","Battery Replacement","Air Filter Replacement","Cabin Air Filter Replacement","Wiper Blade Replacement","Fluid Check/Top-up","Spark Plug Replacement","Coolant Flush","Transmission Service","Wheel Alignment","State Inspection","Other"];function J(e){let{currentOdometerReading:r=0,onServiceRecordAdded:t,vehicle:s}=e,l=(0,q.mN)({defaultValues:{cost:0,date:new Date().toISOString().split("T")[0],notes:"",odometer:r||0,servicePerformed:[],vehicleId:s.id},resolver:(0,T.u)(_.oS)}),{error:i,isPending:n,mutateAsync:c}=(0,b.kI)(),o=async e=>{let r={...e,date:(0,E.B7)(e.date)},a=_.fe.transformToApiPayload(r,s.id);try{await c(a),t();let r="".concat(s.make," ").concat(s.model),i=1===e.servicePerformed.length?e.servicePerformed[0]:"".concat(e.servicePerformed.length," services");H.oz.serviceRecordCreated(r,i),l.reset({cost:0,date:new Date().toISOString().split("T")[0],notes:"",odometer:e.odometer,servicePerformed:[],vehicleId:s.id})}catch(r){console.error("Error adding service record:",r);let e=r.message||(null==i?void 0:i.message)||"An error occurred. Please try again.";H.oz.serviceRecordCreationError(e)}};return(0,a.jsxs)(P.Zp,{className:"bg-card shadow-md",children:[(0,a.jsx)(P.aR,{className:"p-5",children:(0,a.jsx)(P.ZB,{className:"text-xl font-semibold text-primary",children:"Log New Service"})}),(0,a.jsx)(D.lV,{...l,children:(0,a.jsxs)("form",{onSubmit:l.handleSubmit(o),children:[(0,a.jsxs)(P.Wu,{className:"space-y-4 p-5",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsx)(D.zB,{control:l.control,name:"date",render:e=>{let{field:r}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Date of Service"}),(0,a.jsx)(D.MJ,{children:(0,a.jsx)(B.p,{type:"date",...r})}),(0,a.jsx)(D.C5,{})]})}}),(0,a.jsx)(D.zB,{control:l.control,name:"odometer",render:e=>{let{field:r}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Odometer (miles)"}),(0,a.jsx)(D.MJ,{children:(0,a.jsx)(B.p,{placeholder:"e.g., 25000",type:"number",...r,onChange:e=>r.onChange(Number(e.target.value)||0)})}),(0,a.jsx)(D.C5,{})]})}})]}),(0,a.jsx)(D.zB,{control:l.control,name:"servicePerformed",render:()=>(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Services Performed"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-x-4 gap-y-2 sm:grid-cols-3",children:U.map(e=>(0,a.jsx)(D.zB,{control:l.control,name:"servicePerformed",render:r=>{var t;let{field:s}=r;return(0,a.jsxs)(D.eI,{className:"flex flex-row items-center space-x-2 space-y-0",children:[(0,a.jsx)(D.MJ,{children:(0,a.jsx)(V.S,{checked:null==(t=s.value)?void 0:t.includes(e),onCheckedChange:r=>{let t=s.value||[];r?s.onChange([...t,e]):s.onChange(t.filter(r=>r!==e))}})}),(0,a.jsx)(D.lR,{className:"text-sm font-normal",children:e})]})}},e))}),(0,a.jsx)(D.C5,{})]})}),(0,a.jsx)(D.zB,{control:l.control,name:"notes",render:e=>{let{field:r}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Notes (Optional)"}),(0,a.jsx)(D.MJ,{children:(0,a.jsx)(O.T,{placeholder:"e.g., Used synthetic oil, checked tire pressure.",...r})}),(0,a.jsx)(D.C5,{})]})}}),(0,a.jsx)(D.zB,{control:l.control,name:"cost",render:e=>{let{field:r}=e;return(0,a.jsxs)(D.eI,{children:[(0,a.jsx)(D.lR,{children:"Cost (Optional)"}),(0,a.jsx)(D.MJ,{children:(0,a.jsx)(B.p,{placeholder:"e.g., 75.50",step:"0.01",type:"number",...r,onChange:e=>r.onChange(Number(e.target.value)||0)})}),(0,a.jsx)(D.C5,{})]})}})]}),(0,a.jsx)(P.wL,{className:"p-5",children:(0,a.jsx)(x.r,{actionType:"primary",className:"w-full",icon:(0,a.jsx)(F.A,{className:"size-4"}),isLoading:n,loadingText:"Saving...",type:"submit",children:"Add Service Record"})})]})})]})}var $=t(98328);let W=(0,N.A)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]);var Z=t(82733),Y=t(69321),X=t(3235),G=t(66766);function K(e){let{vehicle:r}=e,t=r.serviceHistory||[],s=t.length>0,l=null!==r.initialOdometer&&void 0!==r.initialOdometer,i=s?Math.max(...t.map(e=>e.odometer),r.initialOdometer||0):r.initialOdometer||0;return(0,a.jsxs)(P.Zp,{className:"bg-card shadow-lg",children:[(0,a.jsx)(P.aR,{className:"flex flex-row items-start justify-between gap-4 border-b p-5",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)(P.ZB,{className:"mb-1 text-2xl font-bold text-primary",children:[r.make," ",r.model]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Vehicle Overview"})]})}),(0,a.jsxs)(P.Wu,{className:"grid gap-x-8 gap-y-4 p-5 md:grid-cols-2",children:[(0,a.jsx)("div",{className:"relative mb-4 aspect-[16/10] w-full overflow-hidden rounded-lg shadow-sm md:col-span-2 lg:col-span-1 lg:mb-0",children:(0,a.jsx)(G.default,{alt:"".concat(r.make," ").concat(r.model),className:"bg-muted object-cover","data-ai-hint":"car side",fill:!0,priority:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",src:r.imageUrl||"https://picsum.photos/seed/".concat(r.id,"/600/375")})}),(0,a.jsxs)("div",{className:"space-y-3 text-foreground lg:col-span-1",children:[(0,a.jsx)(Q,{icon:$.A,label:"Year",value:r.year.toString()}),r.licensePlate&&(0,a.jsx)(Q,{icon:W,label:"License Plate",value:r.licensePlate}),r.color&&(0,a.jsx)(Q,{icon:Z.A,label:"Color",value:r.color}),(0,a.jsx)(Q,{icon:Y.A,label:"Initial Odometer",value:l?"".concat(r.initialOdometer.toLocaleString()," miles"):"Not recorded"}),(0,a.jsx)(Q,{icon:Y.A,label:"Latest Odometer",value:s?"".concat(i.toLocaleString()," miles"):l?"".concat(r.initialOdometer.toLocaleString()," miles"):"No odometer data"}),(0,a.jsx)(Q,{icon:X.A,label:"Services Logged",value:t.length.toString()})]})]})]})}function Q(e){let{icon:r,label:t,value:s}=e;return(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(r,{className:"mr-3 size-5 shrink-0 text-accent"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:t}),(0,a.jsx)("p",{className:"text-md font-semibold",children:s})]})]})}let ee=()=>{var e;let r=(0,d.useRouter)(),t=(0,d.useParams)(),{showEntityDeleted:c,showEntityDeletionError:N}=(0,y.O_)("vehicle"),{hasPermission:A}=(0,j.Sk)(),w=(null==t?void 0:t.id)?Number(t.id):null,{data:M,error:C,isLoading:S,refetch:R}=(0,k.W_)(w),I=(0,k.NS)(),{data:P=[],error:B,isLoading:z,refetch:O}=(0,b.xH)(null!=w?w:0,{enabled:!!w}),T=(0,m.useCallback)(()=>{O()},[O]),F=async()=>{if(w&&M){if(!A("vehicles:delete"))return void N("You do not have permission to delete vehicles.");if(globalThis.confirm("Are you sure you want to delete this vehicle permanently?"))try{await I.mutateAsync(w);let e={make:M.make,model:M.model};c(e),r.push("/vehicles")}catch(e){console.error("Failed to delete vehicle:",e),N(e.message||"Failed to delete vehicle. Please try again.")}}},q=()=>{R(),O()};return(0,a.jsx)(u.A,{children:(0,a.jsxs)("div",{className:"container mx-auto space-y-6 py-8",children:[(0,a.jsx)(v.AppBreadcrumb,{}),(0,a.jsx)(f.gO,{data:M,emptyComponent:(0,a.jsxs)("div",{className:"py-10 text-center",children:[(0,a.jsx)(g.z,{icon:s.A,title:"Vehicle Not Found"}),(0,a.jsx)("p",{className:"mb-4",children:"The requested vehicle could not be found."})]}),error:null!=(e=null==C?void 0:C.message)?e:null,isLoading:S,loadingComponent:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(g.z,{icon:l.A,title:"Loading Vehicle..."}),(0,a.jsx)(f.jt,{count:1,variant:"card"}),(0,a.jsxs)("div",{className:"grid items-start gap-6 lg:grid-cols-3",children:[(0,a.jsx)(f.jt,{className:"lg:col-span-2",count:1,variant:"card"}),(0,a.jsx)(f.jt,{count:1,variant:"card"})]})]}),onRetry:R,children:e=>{var r,t,s;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.z,{description:"VIN: ".concat(e.vin," | Plate: ").concat(e.licensePlate),icon:l.A,title:"".concat(e.make," ").concat(e.model),children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(x.r,{actionType:"secondary",asChild:!0,icon:(0,a.jsx)(i.A,{className:"size-4"}),children:(0,a.jsx)(o(),{href:"/vehicles/edit/".concat(e.id),children:"Edit"})}),(0,a.jsx)(h.M,{href:"/vehicles/".concat(e.id,"/report")}),(0,a.jsx)(x.r,{actionType:"danger",icon:(0,a.jsx)(n.A,{className:"size-4"}),isLoading:I.isPending,loadingText:"Deleting...",onClick:F,children:"Delete"})]})}),(0,a.jsxs)("div",{className:"grid items-start gap-6 lg:grid-cols-3",children:[(0,a.jsxs)("div",{className:"space-y-6 lg:col-span-2",children:[(0,a.jsx)(K,{vehicle:e}),(0,a.jsx)(p.R,{error:null!=(r=null==B?void 0:B.message)?r:null,isLoading:z,onRetry:T,records:P,showVehicleInfo:!1,vehicleSpecific:!0})]}),(0,a.jsxs)("div",{className:"space-y-6 lg:col-span-1",children:[(0,a.jsx)(J,{currentOdometerReading:Math.max(...(null!=(t=e.serviceHistory)?t:[]).map(e=>e.odometer),null!=(s=e.initialOdometer)?s:0),onServiceRecordAdded:q,vehicle:e}),(0,a.jsx)(L,{vehicle:e})]})]})]})}})]})})}},82733:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},85057:(e,r,t)=>{"use strict";t.d(r,{J:()=>o});var a=t(95155),s=t(12115),l=t(40968),i=t(74466),n=t(54036);let c=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(l.b,{ref:r,className:(0,n.cn)(c(),t),...s})});o.displayName=l.b.displayName},88539:(e,r,t)=>{"use strict";t.d(r,{T:()=>i});var a=t(95155),s=t(12115),l=t(54036);let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...s})});i.displayName="Textarea"},89440:(e,r,t)=>{"use strict";t.d(r,{AppBreadcrumb:()=>d});var a=t(95155),s=t(6874),l=t.n(s),i=t(35695),n=t(12115),c=t(19018),o=t(54036);function d(e){let{className:r,homeHref:t="/",homeLabel:s="Dashboard",showContainer:d=!0}=e,m=(0,i.usePathname)(),u=m?m.split("/").filter(Boolean):[],h=e=>{if(/^\d+$/.test(e))return"ID: ".concat(e);if(e.length>10&&/^[a-zA-Z0-9-]+$/.test(e))return"Details";let r={add:"Add New",admin:"Administration",edit:"Edit",reports:"Reports","service-history":"Service History",settings:"Settings"};return r[e]?r[e]:e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")},p=u.map((e,r)=>{let t="/"+u.slice(0,r+1).join("/"),s=r===u.length-1,i=h(e);return(0,a.jsxs)(n.Fragment,{children:[(0,a.jsx)(c.BreadcrumbItem,{children:s?(0,a.jsx)(c.BreadcrumbPage,{className:"font-medium text-foreground",children:i}):(0,a.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,a.jsx)(l(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:i})})}),!s&&(0,a.jsx)(c.BreadcrumbSeparator,{})]},t)}),x=(0,a.jsx)(c.Breadcrumb,{className:(0,o.cn)("text-sm",r),children:(0,a.jsxs)(c.BreadcrumbList,{className:"flex-wrap",children:[(0,a.jsx)(c.BreadcrumbItem,{children:(0,a.jsx)(c.BreadcrumbLink,{asChild:!0,children:(0,a.jsx)(l(),{className:"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2",href:t,children:s})})}),u.length>0&&(0,a.jsx)(c.BreadcrumbSeparator,{}),p]})});return d?(0,a.jsx)("div",{className:"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm",children:(0,a.jsx)("div",{className:"flex items-center",children:x})}):x}},98328:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(40157).A)("CalendarDays",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 18h.01",key:"lrp35t"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M16 18h.01",key:"kzsmim"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[6476,1137,3860,9664,1263,5495,1859,6874,5247,5669,4629,6463,6766,4036,8658,111,3712,283,3615,5320,6554,6563,8441,1684,7358],()=>r(28041)),_N_E=e.O()}]);