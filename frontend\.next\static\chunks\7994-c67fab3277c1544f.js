(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7994],{3567:(e,t,s)=>{"use strict";s.d(t,{R:()=>i});var r=s(35476);function i(e){return+(0,r.a)(e)<Date.now()}},5041:(e,t,s)=>{"use strict";s.d(t,{n:()=>h});var r=s(12115),i=s(34560),n=s(7165),o=s(25910),u=s(52020),a=class extends o.Q{#e;#t=void 0;#s;#r;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,u.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,u.EN)(t.mutationKey)!==(0,u.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(e){this.#i(),this.#n(e)}getCurrentResult(){return this.#t}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#i(),this.#n()}mutate(e,t){return this.#r=t,this.#s?.removeObserver(this),this.#s=this.#e.getMutationCache().build(this.#e,this.options),this.#s.addObserver(this),this.#s.execute(e)}#i(){let e=this.#s?.state??(0,i.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#n(e){n.jG.batch(()=>{if(this.#r&&this.hasListeners()){let t=this.#t.variables,s=this.#t.context;e?.type==="success"?(this.#r.onSuccess?.(e.data,t,s),this.#r.onSettled?.(e.data,null,t,s)):e?.type==="error"&&(this.#r.onError?.(e.error,t,s),this.#r.onSettled?.(void 0,e.error,t,s))}this.listeners.forEach(e=>{e(this.#t)})})}},l=s(26715);function h(e,t){let s=(0,l.jE)(t),[i]=r.useState(()=>new a(s,e));r.useEffect(()=>{i.setOptions(e)},[i,e]);let o=r.useSyncExternalStore(r.useCallback(e=>i.subscribe(n.jG.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),h=r.useCallback((e,t)=>{i.mutate(e,t).catch(u.lQ)},[i]);if(o.error&&(0,u.GU)(i.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:h,mutateAsync:o.mutate}}},10233:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("ChevronsRight",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},11518:(e,t,s)=>{"use strict";e.exports=s(82269).style},15300:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},18018:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},20203:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},24371:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},25318:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},27150:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("ArrowUpDown",[["path",{d:"m21 16-4 4-4-4",key:"f6ql7i"}],["path",{d:"M17 20V4",key:"1ejh1v"}],["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}]])},31896:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("ChevronsLeft",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},35079:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("ClipboardList",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},35695:(e,t,s)=>{"use strict";var r=s(18999);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},40968:(e,t,s)=>{"use strict";s.d(t,{b:()=>u});var r=s(12115),i=s(63655),n=s(95155),o=r.forwardRef((e,t)=>(0,n.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{var s;t.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var u=o},44956:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])},46786:(e,t,s)=>{"use strict";s.d(t,{KU:()=>c,Zr:()=>p,eh:()=>h,lt:()=>a});let r=new Map,i=e=>{let t=r.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},n=(e,t,s)=>{if(void 0===e)return{type:"untracked",connection:t.connect(s)};let i=r.get(s.name);if(i)return{type:"tracked",store:e,...i};let n={connection:t.connect(s),stores:{}};return r.set(s.name,n),{type:"tracked",store:e,...n}},o=(e,t)=>{if(void 0===t)return;let s=r.get(e);s&&(delete s.stores[t],0===Object.keys(s.stores).length&&r.delete(e))},u=e=>{var t,s;if(!e)return;let r=e.split("\n"),i=r.findIndex(e=>e.includes("api.setState"));if(i<0)return;let n=(null==(t=r[i+1])?void 0:t.trim())||"";return null==(s=/.+ (.+) .+/.exec(n))?void 0:s[1]},a=(e,t={})=>(s,r,a)=>{let h,{enabled:c,anonymousActionType:d,store:p,...f}=t;try{h=(null==c||c)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!h)return e(s,r,a);let{connection:y,...m}=n(p,h,f),v=!0;a.setState=(e,t,n)=>{let o=s(e,t);if(!v)return o;let l=u(Error().stack),h=void 0===n?{type:d||l||"anonymous"}:"string"==typeof n?{type:n}:n;return void 0===p?null==y||y.send(h,r()):null==y||y.send({...h,type:`${p}/${h.type}`},{...i(f.name),[p]:a.getState()}),o},a.devtools={cleanup:()=>{y&&"function"==typeof y.unsubscribe&&y.unsubscribe(),o(f.name,p)}};let b=(...e)=>{let t=v;v=!1,s(...e),v=t},S=e(a.setState,r,a);if("untracked"===m.type?null==y||y.init(S):(m.stores[m.store]=a,null==y||y.init(Object.fromEntries(Object.entries(m.stores).map(([e,t])=>[e,e===m.store?S:t.getState()])))),a.dispatchFromDevtools&&"function"==typeof a.dispatch){let e=!1,t=a.dispatch;a.dispatch=(...s)=>{"__setState"!==s[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...s)}}return y.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload)return void console.error("[zustand devtools middleware] Unsupported action format");return l(e.payload,e=>{if("__setState"===e.type){if(void 0===p)return void b(e.state);1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[p];return void(null==t||JSON.stringify(a.getState())!==JSON.stringify(t)&&b(t))}a.dispatchFromDevtools&&"function"==typeof a.dispatch&&a.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(b(S),void 0===p)return null==y?void 0:y.init(a.getState());return null==y?void 0:y.init(i(f.name));case"COMMIT":if(void 0===p){null==y||y.init(a.getState());break}return null==y?void 0:y.init(i(f.name));case"ROLLBACK":return l(e.state,e=>{if(void 0===p){b(e),null==y||y.init(a.getState());return}b(e[p]),null==y||y.init(i(f.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return l(e.state,e=>{if(void 0===p)return void b(e);JSON.stringify(a.getState())!==JSON.stringify(e[p])&&b(e[p])});case"IMPORT_STATE":{let{nextLiftedState:s}=e.payload,r=null==(t=s.computedStates.slice(-1)[0])?void 0:t.state;if(!r)return;void 0===p?b(r):b(r[p]),null==y||y.send(null,s);break}case"PAUSE_RECORDING":return v=!v}return}}),S},l=(e,t)=>{let s;try{s=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==s&&t(s)},h=e=>(t,s,r)=>{let i=r.subscribe;return r.subscribe=(e,t,s)=>{let n=e;if(t){let i=(null==s?void 0:s.equalityFn)||Object.is,o=e(r.getState());n=s=>{let r=e(s);if(!i(o,r)){let e=o;t(o=r,e)}},(null==s?void 0:s.fireImmediately)&&t(o,o)}return i(n)},e(t,s,r)};function c(e,t){let s;try{s=e()}catch(e){return}return{getItem:e=>{var r;let i=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),n=null!=(r=s.getItem(e))?r:null;return n instanceof Promise?n.then(i):i(n)},setItem:(e,r)=>s.setItem(e,JSON.stringify(r,null==t?void 0:t.replacer)),removeItem:e=>s.removeItem(e)}}let d=e=>t=>{try{let s=e(t);if(s instanceof Promise)return s;return{then:e=>d(e)(s),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>d(t)(e)}}},p=(e,t)=>(s,r,i)=>{let n,o={storage:c(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},u=!1,a=new Set,l=new Set,h=o.storage;if(!h)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),s(...e)},r,i);let p=()=>{let e=o.partialize({...r()});return h.setItem(o.name,{state:e,version:o.version})},f=i.setState;i.setState=(e,t)=>{f(e,t),p()};let y=e((...e)=>{s(...e),p()},r,i);i.getInitialState=()=>y;let m=()=>{var e,t;if(!h)return;u=!1,a.forEach(e=>{var t;return e(null!=(t=r())?t:y)});let i=(null==(t=o.onRehydrateStorage)?void 0:t.call(o,null!=(e=r())?e:y))||void 0;return d(h.getItem.bind(h))(o.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];else{if(o.migrate){let t=o.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[i,u]=e;if(s(n=o.merge(u,null!=(t=r())?t:y),!0),i)return p()}).then(()=>{null==i||i(n,void 0),n=r(),u=!0,l.forEach(e=>e(n))}).catch(e=>{null==i||i(void 0,e)})};return i.persist={setOptions:e=>{o={...o,...e},e.storage&&(h=e.storage)},clearStorage:()=>{null==h||h.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>m(),hasHydrated:()=>u,onHydrate:e=>(a.add(e),()=>{a.delete(e)}),onFinishHydration:e=>(l.add(e),()=>{l.delete(e)})},o.skipHydration||m(),n||y}},50594:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},60679:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},65064:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},65453:(e,t,s)=>{"use strict";s.d(t,{v:()=>a});var r=s(12115);let i=e=>{let t,s=new Set,r=(e,r)=>{let i="function"==typeof e?e(t):e;if(!Object.is(i,t)){let e=t;t=(null!=r?r:"object"!=typeof i||null===i)?i:Object.assign({},t,i),s.forEach(s=>s(t,e))}},i=()=>t,n={setState:r,getState:i,getInitialState:()=>o,subscribe:e=>(s.add(e),()=>s.delete(e))},o=t=e(r,i,n);return n},n=e=>e?i(e):i,o=e=>e,u=e=>{let t=n(e),s=e=>(function(e,t=o){let s=r.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return r.useDebugValue(s),s})(t,e);return Object.assign(s,t),s},a=e=>e?u(e):u},68375:()=>{},68718:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},71610:(e,t,s)=>{"use strict";s.d(t,{E:()=>y});var r=s(12115),i=s(7165),n=s(76347),o=s(25910),u=s(52020);function a(e,t){let s=new Set(t);return e.filter(e=>!s.has(e))}var l=class extends o.Q{#e;#o;#u;#a;#l;#h;#c;#d;#p=[];constructor(e,t,s){super(),this.#e=e,this.#a=s,this.#u=[],this.#l=[],this.#o=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#l.forEach(e=>{e.subscribe(t=>{this.#f(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#l.forEach(e=>{e.destroy()})}setQueries(e,t){this.#u=e,this.#a=t,i.jG.batch(()=>{let e=this.#l,t=this.#y(this.#u);this.#p=t,t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let s=t.map(e=>e.observer),r=s.map(e=>e.getCurrentResult()),i=s.some((t,s)=>t!==e[s]);(e.length!==s.length||i)&&(this.#l=s,this.#o=r,this.hasListeners()&&(a(e,s).forEach(e=>{e.destroy()}),a(s,e).forEach(e=>{e.subscribe(t=>{this.#f(e,t)})}),this.#n()))})}getCurrentResult(){return this.#o}getQueries(){return this.#l.map(e=>e.getCurrentQuery())}getObservers(){return this.#l}getOptimisticResult(e,t){let s=this.#y(e),r=s.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[r,e=>this.#m(e??r,t),()=>this.#v(r,s)]}#v(e,t){return t.map((s,r)=>{let i=e[r];return s.defaultedQueryOptions.notifyOnChangeProps?i:s.observer.trackResult(i,e=>{t.forEach(t=>{t.observer.trackProp(e)})})})}#m(e,t){return t?(this.#h&&this.#o===this.#d&&t===this.#c||(this.#c=t,this.#d=this.#o,this.#h=(0,u.BH)(this.#h,t(e))),this.#h):e}#y(e){let t=new Map(this.#l.map(e=>[e.options.queryHash,e])),s=[];return e.forEach(e=>{let r=this.#e.defaultQueryOptions(e),i=t.get(r.queryHash);i?s.push({defaultedQueryOptions:r,observer:i}):s.push({defaultedQueryOptions:r,observer:new n.$(this.#e,r)})}),s}#f(e,t){let s=this.#l.indexOf(e);-1!==s&&(this.#o=function(e,t,s){let r=e.slice(0);return r[t]=s,r}(this.#o,s,t),this.#n())}#n(){if(this.hasListeners()){let e=this.#h,t=this.#v(this.#o,this.#p);e!==this.#m(t,this.#a?.combine)&&i.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#o)})})}}},h=s(26715),c=s(61581),d=s(80382),p=s(22450),f=s(4791);function y(e,t){let{queries:s,...o}=e,a=(0,h.jE)(t),y=(0,c.w)(),m=(0,d.h)(),v=r.useMemo(()=>s.map(e=>{let t=a.defaultQueryOptions(e);return t._optimisticResults=y?"isRestoring":"optimistic",t}),[s,a,y]);v.forEach(e=>{(0,f.jv)(e),(0,p.LJ)(e,m)}),(0,p.wZ)(m);let[b]=r.useState(()=>new l(a,v,o)),[S,g,_]=b.getOptimisticResult(v,o.combine),k=!y&&!1!==o.subscribed;r.useSyncExternalStore(r.useCallback(e=>k?b.subscribe(i.jG.batchCalls(e)):u.lQ,[b,k]),()=>b.getCurrentResult(),()=>b.getCurrentResult()),r.useEffect(()=>{b.setQueries(v,o)},[v,o,b]);let w=S.some((e,t)=>(0,f.EU)(v[t],e))?S.flatMap((e,t)=>{let s=v[t];if(s){let t=new n.$(a,s);if((0,f.EU)(s,e))return(0,f.iL)(s,t,m);(0,f.nE)(e,y)&&(0,f.iL)(s,t,m)}return[]}):[];if(w.length>0)throw Promise.all(w);let R=S.find((e,t)=>{let s=v[t];return s&&(0,p.$1)({result:e,errorResetBoundary:m,throwOnError:s.throwOnError,query:a.getQueryCache().get(s.queryHash),suspense:s.suspense})});if(null==R?void 0:R.error)throw R.error;return g(_())}},73926:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},75074:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},82269:(e,t,s)=>{"use strict";var r=s(87358);s(68375);var i=s(12115),n=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(i),o=void 0!==r&&r.env&&!0,u=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,s=t.name,r=void 0===s?"stylesheet":s,i=t.optimizeForSpeed,n=void 0===i?o:i;l(u(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",l("boolean"==typeof n,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=n,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var a="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=a?a.getAttribute("content"):null}var t,s=e.prototype;return s.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;if(l(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,t){if(l(u(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var s=this.getSheet();"number"!=typeof t&&(t=s.cssRules.length);try{s.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},s.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var s="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(r){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];l(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},s.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];l(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},s.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},s.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,s){return s?t=t.concat(Array.prototype.map.call(e.getSheetForTag(s).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},s.makeStyleTag=function(e,t,s){t&&l(u(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return s?i.insertBefore(r,s):i.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var h=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},c={};function d(e,t){if(!t)return"jsx-"+e;var s=String(t),r=e+s;return c[r]||(c[r]="jsx-"+h(e+"-"+s)),c[r]}function p(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var s=e+t;return c[s]||(c[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),c[s]}var f=function(){function e(e){var t=void 0===e?{}:e,s=t.styleSheet,r=void 0===s?null:s,i=t.optimizeForSpeed,n=void 0!==i&&i;this._sheet=r||new a({name:"styled-jsx",optimizeForSpeed:n}),this._sheet.inject(),r&&"boolean"==typeof n&&(this._sheet.setOptimizeForSpeed(n),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var s=this.getIdAndRules(e),r=s.styleId,i=s.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var n=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=n,this._instancesCounts[r]=1},t.remove=function(e){var t=this,s=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(s in this._instancesCounts,"styleId: `"+s+"` not found"),this._instancesCounts[s]-=1,this._instancesCounts[s]<1){var r=this._fromServer&&this._fromServer[s];r?(r.parentNode.removeChild(r),delete this._fromServer[s]):(this._indices[s].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[s]),delete this._instancesCounts[s]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],s=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return s[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,s;return t=this.cssRules(),void 0===(s=e)&&(s={}),t.map(function(e){var t=e[0],r=e[1];return n.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:s.nonce?s.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,s=e.dynamic,r=e.id;if(s){var i=d(r,s);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return p(i,e)}):[p(i,t)]}}return{styleId:d(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),y=i.createContext(null);y.displayName="StyleSheetContext";var m=n.default.useInsertionEffect||n.default.useLayoutEffect,v="undefined"!=typeof window?new f:void 0;function b(e){var t=v||i.useContext(y);return t&&("undefined"==typeof window?t.add(e):m(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}b.dynamic=function(e){return e.map(function(e){return d(e[0],e[1])}).join(" ")},t.style=b}}]);