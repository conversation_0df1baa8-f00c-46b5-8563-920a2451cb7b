"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[703],{5041:(t,e,s)=>{s.d(e,{n:()=>l});var r=s(12115),i=s(34560),n=s(7165),u=s(25910),o=s(52020),a=class extends u.Q{#t;#e=void 0;#s;#r;constructor(t,e){super(),this.#t=t,this.setOptions(e),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){let e=this.options;this.options=this.#t.defaultMutationOptions(t),(0,o.f8)(this.options,e)||this.#t.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),e?.mutationKey&&this.options.mutationKey&&(0,o.EN)(e.mutation<PERSON>ey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(t){this.#i(),this.#n(t)}getCurrentResult(){return this.#e}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#i(),this.#n()}mutate(t,e){return this.#r=e,this.#s?.removeObserver(this),this.#s=this.#t.getMutationCache().build(this.#t,this.options),this.#s.addObserver(this),this.#s.execute(t)}#i(){let t=this.#s?.state??(0,i.$)();this.#e={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#n(t){n.jG.batch(()=>{if(this.#r&&this.hasListeners()){let e=this.#e.variables,s=this.#e.context;t?.type==="success"?(this.#r.onSuccess?.(t.data,e,s),this.#r.onSettled?.(t.data,null,e,s)):t?.type==="error"&&(this.#r.onError?.(t.error,e,s),this.#r.onSettled?.(void 0,t.error,e,s))}this.listeners.forEach(t=>{t(this.#e)})})}},h=s(26715);function l(t,e){let s=(0,h.jE)(e),[i]=r.useState(()=>new a(s,t));r.useEffect(()=>{i.setOptions(t)},[i,t]);let u=r.useSyncExternalStore(r.useCallback(t=>i.subscribe(n.jG.batchCalls(t)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),l=r.useCallback((t,e)=>{i.mutate(t,e).catch(o.lQ)},[i]);if(u.error&&(0,o.GU)(i.options.throwOnError,[u.error]))throw u.error;return{...u,mutate:l,mutateAsync:u.mutate}}},6654:(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=s(12115);function i(t,e){let s=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let t=s.current;t&&(s.current=null,t());let e=i.current;e&&(i.current=null,e())}else t&&(s.current=n(t,r)),e&&(i.current=n(e,r))},[t,e])}function n(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let s=t(e);return"function"==typeof s?s:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},15300:(t,e,s)=>{s.d(e,{A:()=>r});let r=(0,s(40157).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},18018:(t,e,s)=>{s.d(e,{A:()=>r});let r=(0,s(40157).A)("Printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]])},35695:(t,e,s)=>{var r=s(18999);s.o(r,"useParams")&&s.d(e,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(e,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(e,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(e,{useSearchParams:function(){return r.useSearchParams}})},37648:(t,e,s)=>{s.d(e,{A:()=>r});let r=(0,s(40157).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},58260:(t,e,s)=>{s.d(e,{A:()=>r});let r=(0,s(40157).A)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]])},60679:(t,e,s)=>{s.d(e,{A:()=>r});let r=(0,s(40157).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},68718:(t,e,s)=>{s.d(e,{A:()=>r});let r=(0,s(40157).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},71610:(t,e,s)=>{s.d(e,{E:()=>f});var r=s(12115),i=s(7165),n=s(76347),u=s(25910),o=s(52020);function a(t,e){let s=new Set(e);return t.filter(t=>!s.has(t))}var h=class extends u.Q{#t;#u;#o;#a;#h;#l;#c;#d;#p=[];constructor(t,e,s){super(),this.#t=t,this.#a=s,this.#o=[],this.#h=[],this.#u=[],this.setQueries(e)}onSubscribe(){1===this.listeners.size&&this.#h.forEach(t=>{t.subscribe(e=>{this.#b(t,e)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#h.forEach(t=>{t.destroy()})}setQueries(t,e){this.#o=t,this.#a=e,i.jG.batch(()=>{let t=this.#h,e=this.#f(this.#o);this.#p=e,e.forEach(t=>t.observer.setOptions(t.defaultedQueryOptions));let s=e.map(t=>t.observer),r=s.map(t=>t.getCurrentResult()),i=s.some((e,s)=>e!==t[s]);(t.length!==s.length||i)&&(this.#h=s,this.#u=r,this.hasListeners()&&(a(t,s).forEach(t=>{t.destroy()}),a(s,t).forEach(t=>{t.subscribe(e=>{this.#b(t,e)})}),this.#n()))})}getCurrentResult(){return this.#u}getQueries(){return this.#h.map(t=>t.getCurrentQuery())}getObservers(){return this.#h}getOptimisticResult(t,e){let s=this.#f(t),r=s.map(t=>t.observer.getOptimisticResult(t.defaultedQueryOptions));return[r,t=>this.#y(t??r,e),()=>this.#m(r,s)]}#m(t,e){return e.map((s,r)=>{let i=t[r];return s.defaultedQueryOptions.notifyOnChangeProps?i:s.observer.trackResult(i,t=>{e.forEach(e=>{e.observer.trackProp(t)})})})}#y(t,e){return e?(this.#l&&this.#u===this.#d&&e===this.#c||(this.#c=e,this.#d=this.#u,this.#l=(0,o.BH)(this.#l,e(t))),this.#l):t}#f(t){let e=new Map(this.#h.map(t=>[t.options.queryHash,t])),s=[];return t.forEach(t=>{let r=this.#t.defaultQueryOptions(t),i=e.get(r.queryHash);i?s.push({defaultedQueryOptions:r,observer:i}):s.push({defaultedQueryOptions:r,observer:new n.$(this.#t,r)})}),s}#b(t,e){let s=this.#h.indexOf(t);-1!==s&&(this.#u=function(t,e,s){let r=t.slice(0);return r[e]=s,r}(this.#u,s,e),this.#n())}#n(){if(this.hasListeners()){let t=this.#l,e=this.#m(this.#u,this.#p);t!==this.#y(e,this.#a?.combine)&&i.jG.batch(()=>{this.listeners.forEach(t=>{t(this.#u)})})}}},l=s(26715),c=s(61581),d=s(80382),p=s(22450),b=s(4791);function f(t,e){let{queries:s,...u}=t,a=(0,l.jE)(e),f=(0,c.w)(),y=(0,d.h)(),m=r.useMemo(()=>s.map(t=>{let e=a.defaultQueryOptions(t);return e._optimisticResults=f?"isRestoring":"optimistic",e}),[s,a,f]);m.forEach(t=>{(0,b.jv)(t),(0,p.LJ)(t,y)}),(0,p.wZ)(y);let[v]=r.useState(()=>new h(a,m,u)),[M,R,O]=v.getOptimisticResult(m,u.combine),k=!f&&!1!==u.subscribed;r.useSyncExternalStore(r.useCallback(t=>k?v.subscribe(i.jG.batchCalls(t)):o.lQ,[v,k]),()=>v.getCurrentResult(),()=>v.getCurrentResult()),r.useEffect(()=>{v.setQueries(m,u)},[m,u,v]);let g=M.some((t,e)=>(0,b.EU)(m[e],t))?M.flatMap((t,e)=>{let s=m[e];if(s){let e=new n.$(a,s);if((0,b.EU)(s,t))return(0,b.iL)(s,e,y);(0,b.nE)(t,f)&&(0,b.iL)(s,e,y)}return[]}):[];if(g.length>0)throw Promise.all(g);let E=M.find((t,e)=>{let s=m[e];return s&&(0,p.$1)({result:t,errorResetBoundary:y,throwOnError:s.throwOnError,query:a.getQueryCache().get(s.queryHash),suspense:s.suspense})});if(null==E?void 0:E.error)throw E.error;return R(O())}},83662:(t,e,s)=>{s.d(e,{A:()=>r});let r=(0,s(40157).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}}]);