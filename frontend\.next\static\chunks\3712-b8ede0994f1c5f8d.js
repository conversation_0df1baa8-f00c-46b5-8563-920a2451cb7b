"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3712],{40879:(e,t,r)=>{r.d(t,{dj:()=>p,oR:()=>u});var i=r(12115);let s=0,c=new Map,o=e=>{if(c.has(e))return;let t=setTimeout(()=>{c.delete(e),l({toastId:e,type:"REMOVE_TOAST"})},1e6);c.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"DISMISS_TOAST":{let{toastId:r}=t;if(r)o(r);else for(let t of e.toasts)o(t.id);return{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)}}},a=[],d={toasts:[]};function l(e){for(let t of(d=n(d,e),a))t(d)}function u(e){let{...t}=e,r=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),i=()=>l({toastId:r,type:"DISMISS_TOAST"});return l({toast:{...t,id:r,onOpenChange:e=>{e||i()},open:!0},type:"ADD_TOAST"}),{dismiss:i,id:r,update:e=>l({toast:{...e,id:r},type:"UPDATE_TOAST"})}}function p(){let[e,t]=i.useState(d);return i.useEffect(()=>(a.push(t),()=>{let e=a.indexOf(t);-1!==e&&a.splice(e,1)}),[e]),{...e,dismiss:e=>l({type:"DISMISS_TOAST",...e&&{toastId:e}}),toast:u}}},53712:(e,t,r)=>{r.d(t,{O_:()=>o,t6:()=>c});var i=r(12115),s=r(83940);function c(){let e=(0,i.useCallback)((e,t)=>s.JP.success(e,t),[]),t=(0,i.useCallback)((e,t)=>s.JP.error(e,t),[]),r=(0,i.useCallback)((e,t)=>s.JP.info(e,t),[]),c=(0,i.useCallback)(t=>e((null==t?void 0:t.successTitle)||"Success",(null==t?void 0:t.successDescription)||"Operation completed successfully"),[e]),o=(0,i.useCallback)((e,r)=>{let i=e instanceof Error?e.message:e;return t((null==r?void 0:r.errorTitle)||"Error",(null==r?void 0:r.errorDescription)||i||"An unexpected error occurred")},[t]);return{showSuccess:e,showError:t,showInfo:r,showFormSuccess:c,showFormError:o}}function o(e){let t;switch(e){case"employee":t=r(83940).Ok;break;case"vehicle":t=r(83940).G7;break;case"task":t=r(83940).z0;break;case"delegation":t=r(83940).Qu;break;default:throw Error("Unknown entity type: ".concat(e))}return function(e,t){let{showFormSuccess:r,showFormError:o}=c(),n=t||(e?(0,s.iw)(e):null),a=(0,i.useCallback)(e=>n?n.entityCreated(e):r({successTitle:"Created",successDescription:"Item has been created successfully"}),[n,r]),d=(0,i.useCallback)(e=>n?n.entityUpdated(e):r({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[n,r]),l=(0,i.useCallback)(e=>n?n.entityDeleted(e):r({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[n,r]),u=(0,i.useCallback)(e=>{if(n){let t=e instanceof Error?e.message:e;return n.entityCreationError(t)}return o(e,{errorTitle:"Creation Failed"})},[n,o]);return{showEntityCreated:a,showEntityUpdated:d,showEntityDeleted:l,showEntityCreationError:u,showEntityUpdateError:(0,i.useCallback)(e=>{if(n){let t=e instanceof Error?e.message:e;return n.entityUpdateError(t)}return o(e,{errorTitle:"Update Failed"})},[n,o]),showEntityDeletionError:(0,i.useCallback)(e=>{if(n){let t=e instanceof Error?e.message:e;return n.entityDeletionError(t)}return o(e,{errorTitle:"Deletion Failed"})},[n,o]),showFormSuccess:r,showFormError:o}}(void 0,t)}},83940:(e,t,r)=>{r.d(t,{G7:()=>p,Gb:()=>a,JP:()=>d,Ok:()=>l,Qu:()=>u,iw:()=>n,oz:()=>g,z0:()=>h});var i=r(40879);class s{show(e){return(0,i.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,t){return this.show({title:e,description:t,variant:"default"})}error(e,t){return this.show({title:e,description:t,variant:"destructive"})}info(e,t){return this.show({title:e,description:t,variant:"default"})}}class c extends s{entityCreated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(t))}entityUpdated(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(t))}entityDeleted(e){let t=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(t))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}constructor(e){super(),this.config=e}}class o extends s{serviceRecordCreated(e,t){return this.success("Service Record Added","".concat(t,' service for "').concat(e,'" has been successfully logged.'))}serviceRecordUpdated(e,t){return this.success("Service Record Updated","".concat(t,' service for "').concat(e,'" has been updated.'))}serviceRecordDeleted(e,t){return this.success("Service Record Deleted","".concat(t,' service record for "').concat(e,'" has been permanently removed.'))}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function n(e){return new c(e)}function a(e,t){return new c({entityName:e,getDisplayName:t,messages:{created:{title:"".concat(e," Created"),description:t=>"The ".concat(e.toLowerCase(),' "').concat(t,'" has been successfully created.')},updated:{title:"".concat(e," Updated Successfully"),description:e=>"".concat(e," has been updated.")},deleted:{title:"".concat(e," Deleted Successfully"),description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create ".concat(e),description:t=>t||"An unexpected error occurred while creating the ".concat(e.toLowerCase(),".")},updateError:{title:"Update Failed",description:t=>t||"An unexpected error occurred while updating the ".concat(e.toLowerCase(),".")},deletionError:{title:"Failed to Delete ".concat(e),description:t=>t||"An unexpected error occurred while deleting the ".concat(e.toLowerCase(),".")}}})}let d=new s,l=new c({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>'The employee "'.concat(e,'" has been successfully created.')},updated:{title:"Employee Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Employee Deleted Successfully",description:e=>"".concat(e," has been permanently removed from the system.")},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),u=new c({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>'The delegation "'.concat(e,'" has been successfully created.')},updated:{title:"Delegation Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Delegation Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),p=new c({entityName:"Vehicle",getDisplayName:e=>"".concat(e.make," ").concat(e.model),messages:{created:{title:"Vehicle Added",description:e=>'The vehicle "'.concat(e,'" has been successfully created.')},updated:{title:"Vehicle Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Vehicle Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),h=new c({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>'The task "'.concat(e,'" has been successfully created.')},updated:{title:"Task Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Task Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),g=new o}}]);