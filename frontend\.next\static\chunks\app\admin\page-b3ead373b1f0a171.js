(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[425,3698],{6548:(e,s,a)=>{"use strict";a.d(s,{Sk:()=>i,p9:()=>n});var t=a(28755),r=a(12115),l=a(40879);let i=function(e,s){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{toast:i}=(0,l.dj)(),{cacheDuration:n=3e5,enableRetry:c=!0,errorMessage:d,retryAttempts:o=3,showErrorToast:m=!0,showSuccessToast:x=!1,successMessage:u,...h}=a,f=(0,t.I)({gcTime:2*n,queryFn:s,queryKey:e,retry:!!c&&o,retryDelay:e=>Math.min(1e3*2**e,3e4),staleTime:n,...h});return(0,r.useEffect)(()=>{x&&f.isSuccess&&f.data&&u&&i({description:u,title:"Success"})},[x,f.isSuccess,f.data,u,i]),(0,r.useEffect)(()=>{m&&f.isError&&i({description:d||(f.error instanceof Error?f.error.message:"An error occurred"),title:"Error",variant:"destructive"})},[m,f.isError,f.error,d,i]),{...f,forceRefresh:async()=>await f.refetch(),isStale:f.isStale||!1,lastUpdated:f.dataUpdatedAt||null}},n=function(e,s){var a,t,r;let l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{keepPreviousData:n=!0,page:c=1,pageSize:d=10,...o}=l,m=i([...e,"paginated",c,d],()=>s(c,d),{...o,...n?{placeholderData:e=>e}:{}}),x=null==(a=m.data)?void 0:a.pagination;return{...m,currentPage:c,data:null!=(r=null==(t=m.data)?void 0:t.data)?r:[],goToPage:e=>{},hasNextPage:!!x&&x.hasNext,hasPrevPage:!!x&&x.hasPrevious,nextPage:()=>{x&&x.hasNext},pagination:null!=x?x:{hasNext:!1,hasPrevious:!1,limit:d,page:1,total:0,totalPages:1},prevPage:()=>{x&&x.hasPrevious},totalPages:x?x.totalPages:1}}},14636:(e,s,a)=>{"use strict";a.d(s,{AM:()=>n,Wv:()=>c,hl:()=>d});var t=a(95155),r=a(20547),l=a(12115),i=a(54036);let n=r.bL,c=r.l9;r.bm;let d=l.forwardRef((e,s)=>{let{align:a="center",className:l,sideOffset:n=4,...c}=e;return(0,t.jsx)(r.ZL,{children:(0,t.jsx)(r.UC,{align:a,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",l),ref:s,sideOffset:n,...c})})});d.displayName=r.UC.displayName},17313:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>d,av:()=>o,j7:()=>c,tU:()=>n});var t=a(95155),r=a(60704),l=a(12115),i=a(54036);let n=r.bL,c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.B8,{className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),ref:s,...l})});c.displayName=r.B8.displayName;let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.l9,{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),ref:s,...l})});d.displayName=r.l9.displayName;let o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.UC,{className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),ref:s,...l})});o.displayName=r.UC.displayName},29797:(e,s,a)=>{"use strict";a.d(s,{$o:()=>g,Eb:()=>h,Iu:()=>m,WA:()=>f,cU:()=>x,dK:()=>o,n$:()=>u});var t=a(95155),r=a(965),l=a(73158),i=a(3561),n=a(12115),c=a(30285),d=a(54036);let o=n.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{className:(0,d.cn)("flex justify-center",a),ref:s,...r})});o.displayName="Pagination";let m=n.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("ul",{className:(0,d.cn)("flex flex-row items-center gap-1",a),ref:s,...r})});m.displayName="PaginationContent";let x=n.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("li",{className:(0,d.cn)("",a),ref:s,...r})});x.displayName="PaginationItem";let u=n.forwardRef((e,s)=>{let{className:a,isActive:r,...l}=e;return(0,t.jsx)(c.$,{"aria-current":r?"page":void 0,className:(0,d.cn)("h-9 w-9",a),ref:s,size:"icon",variant:r?"outline":"ghost",...l})});u.displayName="PaginationLink";let h=n.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsxs)(c.$,{className:(0,d.cn)("h-9 w-9 gap-1",a),ref:s,size:"icon",variant:"ghost",...l,children:[(0,t.jsx)(r.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Previous page"})]})});h.displayName="PaginationPrevious";let f=n.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsxs)(c.$,{className:(0,d.cn)("h-9 w-9 gap-1",a),ref:s,size:"icon",variant:"ghost",...r,children:[(0,t.jsx)(l.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Next page"})]})});f.displayName="PaginationNext";let p=n.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsxs)("span",{"aria-hidden":!0,className:(0,d.cn)("flex h-9 w-9 items-center justify-center",a),ref:s,...r,children:[(0,t.jsx)(i.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"More pages"})]})});function g(e){let{className:s,currentPage:a,onPageChange:r,totalPages:l}=e,i=(()=>{let e=[];e.push(1);let s=Math.max(2,a-1),t=Math.min(l-1,a+1);s>2&&e.push("ellipsis1");for(let a=s;a<=t;a++)e.push(a);return t<l-1&&e.push("ellipsis2"),l>1&&e.push(l),e})();return l<=1?null:(0,t.jsx)(o,{className:s,children:(0,t.jsxs)(m,{children:[(0,t.jsx)(x,{children:(0,t.jsx)(h,{"aria-disabled":1===a?"true":void 0,"aria-label":"Go to previous page",disabled:1===a,onClick:()=>r(a-1)})}),i.map((e,s)=>"ellipsis1"===e||"ellipsis2"===e?(0,t.jsx)(x,{children:(0,t.jsx)(p,{})},"ellipsis-".concat(s)):(0,t.jsx)(x,{children:(0,t.jsx)(u,{"aria-label":"Go to page ".concat(e),isActive:a===e,onClick:()=>r(e),children:e})},"page-".concat(e))),(0,t.jsx)(x,{children:(0,t.jsx)(f,{"aria-disabled":a===l?"true":void 0,"aria-label":"Go to next page",disabled:a===l,onClick:()=>r(a+1)})})]})})}p.displayName="PaginationEllipsis"},30425:(e,s,a)=>{"use strict";a.d(s,{aN:()=>x,adminService:()=>m});var t=a(25982),r=a(75908);let l={fromApi:e=>({action:e.action||"",details:e.details||"",id:e.id||"",timestamp:new Date(e.created_at||e.timestamp||new Date),userId:e.user_id||e.userId||e.auth_user_id||"",auth_user_id:e.auth_user_id||"",auth_user:e.auth_user||null}),toApi:e=>e};class i extends t.v{async getByAction(e,s){return(await this.getAll({...s,action:e})).data}async getByDateRange(e,s,a){let t={endDate:s.toISOString(),startDate:e.toISOString(),...a};return this.getAll(t)}async getByUserId(e,s){return(await this.getAll({...s,userId:e})).data}constructor(e,s){super(e,{cacheDuration:12e4,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...s}),this.endpoint="/admin/audit-logs",this.transformer=l}}let n={fromApi:e=>{var s,a,t,r,l;let i=(null==(a=e.users)||null==(s=a[0])?void 0:s.email)||e.email||"",n=(null==(r=e.users)||null==(t=r[0])?void 0:t.email_confirmed_at)||e.email_confirmed_at||null;return{created_at:e.created_at||"",email:i,email_confirmed_at:n,employee_id:e.employee_id||null,full_name:e.full_name||e.name||"",id:e.id,isActive:null==(l=e.is_active)||l,last_sign_in_at:e.last_sign_in_at||null,phone:e.phone||null,phone_confirmed_at:e.phone_confirmed_at||null,role:e.role||"USER",updated_at:e.updated_at||"",users:e.users}},toApi:e=>e};class c extends t.v{async getUsersByRole(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return(await this.getAll({...s,role:e})).data}async toggleActivation(e,s){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.patch("".concat(this.endpoint,"/").concat(e,"/toggle-activation"),{isActive:s});return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidate("".concat(this.endpoint,":getById:").concat(e)),this.transformer.fromApi?this.transformer.fromApi(a):a})}constructor(e,s){super(e,{cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...s}),this.endpoint="/admin/users",this.transformer=n}}let d={fromApi:e=>e,toApi:e=>e};class o extends t.v{get cacheUtils(){return{clearAll:()=>this.clearCache(),forceRefreshHealth:async()=>(this.cache.invalidate("admin:health"),this.getSystemHealthStatus()),forceRefreshPerformance:async()=>(this.cache.invalidate("admin:performance"),this.getPerformanceMetrics()),getStats:()=>this.cache.getStats(),invalidateAll:()=>this.cache.invalidatePattern(/^admin:/)}}async createAuditLog(e){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.post("/admin/audit-logs",e);return this.cache.invalidatePattern(/^admin:audit:/),s})}async createUser(e){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.post("/admin/users",e);return this.cache.invalidatePattern(/^admin:users:/),s})}async deleteUser(e){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete("/admin/users/".concat(e)),this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate("admin:user:".concat(e))})}async getAllUsers(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.userService.getAll(e)}async getAuditLogs(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.auditService.getAll(e)}getMockHealthStatus(){return{services:{api:{responseTime:23,status:"healthy"},cache:{responseTime:12,status:"healthy"},database:{responseTime:45,status:"healthy"}},status:"healthy",timestamp:new Date().toISOString(),uptime:3600}}getMockPerformanceMetrics(){return{cpu:{cores:4,usage:100*Math.random()},errors:{rate:5*Math.random(),total:Math.floor(500*Math.random())},memory:{percentage:100*Math.random(),total:8e3,used:8e3*Math.random()},requests:{averageResponseTime:1e3*Math.random(),perSecond:100*Math.random(),total:Math.floor(1e4*Math.random())},timestamp:new Date().toISOString()}}getMockRecentErrors(){let e=[{details:{component:"database",errorType:"timeout"},id:"1",level:"ERROR",message:"Database connection timeout",requestId:"req-456",source:"database.service.ts",stack:"Error: Connection timeout\n    at Database.connect...",timestamp:new Date().toISOString(),userId:"user123"},{details:{component:"system",metric:"memory"},id:"2",level:"WARNING",message:"High memory usage detected",requestId:"req-789",source:"monitoring.service.ts",timestamp:new Date(Date.now()-3e5).toISOString()}];return{data:e,pagination:{hasNext:!1,hasPrevious:!1,limit:10,page:1,total:e.length,totalPages:Math.ceil(e.length/10)}}}async getPerformanceMetrics(){return this.executeWithInfrastructure("admin:performance",async()=>this.apiClient.get("/admin/performance"))}async getRecentErrors(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2?arguments[2]:void 0,t=new URLSearchParams;t.append("page",e.toString()),t.append("limit",s.toString()),a&&t.append("level",a);let r="/admin/logs/errors?".concat(t.toString()),l="admin:errors:".concat(e,":").concat(s,":").concat(a||"all");return this.executeWithInfrastructure(l,async()=>this.apiClient.get(r))}async getSystemHealthStatus(){return this.executeWithInfrastructure("admin:health",async()=>this.apiClient.get("/admin/health"))}async toggleUserActivation(e,s){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.patch("/admin/users/".concat(e,"/toggle-activation"),{isActive:s});return this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate("admin:user:".concat(e)),a})}async updateUser(e,s){return this.executeWithInfrastructure(null,async()=>{let a=await this.apiClient.put("/admin/users/".concat(e),s);return this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate("admin:user:".concat(e)),a})}constructor(e,s){let a=e||r.cl.getApiClient();super(a,{cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...s}),this.endpoint="/admin",this.transformer=d,this.auditService=new i(a),this.userService=new c(a)}}let m=new o,x=m.cacheUtils},45876:(e,s,a)=>{"use strict";a.d(s,{ProtectedRoute:()=>m});var t=a(95155),r=a(50172),l=a(31949),i=a(45731);a(12115);var n=a(40283),c=a(55365),d=a(66695),o=a(92999);function m(e){let{allowedRoles:s=[],children:a,fallback:m,requireEmailVerification:x=!0}=e,{error:u,loading:h,session:f,user:p,userRole:g}=(0,n.useAuthContext)();if(h)return(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50",children:(0,t.jsx)(d.Zp,{className:"mx-auto w-full max-w-md",children:(0,t.jsxs)(d.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,t.jsx)(r.A,{className:"mb-4 size-8 animate-spin text-blue-600"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Verifying security credentials..."})]})})});if(u&&!p)return(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,t.jsxs)(d.Zp,{className:"mx-auto w-full max-w-md",children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsxs)(d.ZB,{className:"flex items-center text-red-600",children:[(0,t.jsx)(l.A,{className:"mr-2 size-5"}),"Authentication Error"]}),(0,t.jsx)(d.BT,{children:"There was a problem with the security system"})]}),(0,t.jsxs)(d.Wu,{children:[(0,t.jsx)(c.Fc,{variant:"destructive",children:(0,t.jsx)(c.TN,{children:u})}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsx)(o.LoginForm,{})})]})]})});if(!p||!f)return m?(0,t.jsx)(t.Fragment,{children:m}):(0,t.jsx)(o.LoginForm,{onSuccess:()=>{globalThis.location.href="/"}});if(x&&!p.email_confirmed_at)return(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,t.jsxs)(d.Zp,{className:"mx-auto w-full max-w-md",children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsxs)(d.ZB,{className:"flex items-center text-yellow-600",children:[(0,t.jsx)(i.A,{className:"mr-2 size-5"}),"Email Verification Required"]}),(0,t.jsx)(d.BT,{children:"Please verify your email address to continue"})]}),(0,t.jsxs)(d.Wu,{children:[(0,t.jsxs)(c.Fc,{children:[(0,t.jsx)(l.A,{className:"size-4"}),(0,t.jsxs)(c.TN,{children:["We've sent a verification email to ",(0,t.jsx)("strong",{children:p.email}),". Please check your inbox and click the verification link to access the system."]})]}),(0,t.jsx)("div",{className:"mt-4 text-center",children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Didn't receive the email? Check your spam folder or contact your administrator."})})]})]})});if(s.length>0){let e=g||"USER";if(!s.includes(e))return(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,t.jsxs)(d.Zp,{className:"mx-auto w-full max-w-md",children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsxs)(d.ZB,{className:"flex items-center text-red-600",children:[(0,t.jsx)(i.A,{className:"mr-2 size-5"}),"Access Denied"]}),(0,t.jsx)(d.BT,{children:"Insufficient permissions to access this resource"})]}),(0,t.jsxs)(d.Wu,{children:[(0,t.jsx)(c.Fc,{variant:"destructive",children:(0,t.jsxs)(c.TN,{children:["Your account (",e,") does not have permission to access this area. Required roles: ",s.join(", ")]})}),(0,t.jsx)("div",{className:"mt-4 text-center",children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Contact your administrator if you believe this is an error."})})]})]})})}return(0,t.jsx)(t.Fragment,{children:a})}},54165:(e,s,a)=>{"use strict";a.d(s,{Cf:()=>x,Es:()=>h,L3:()=>f,c7:()=>u,lG:()=>c,rr:()=>p,zM:()=>d});var t=a(95155),r=a(15452),l=a(25318),i=a(12115),n=a(54036);let c=r.bL,d=r.l9,o=r.ZL;r.bm;let m=i.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.hJ,{className:(0,n.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),ref:s,...l})});m.displayName=r.hJ.displayName;let x=i.forwardRef((e,s)=>{let{children:a,className:i,...c}=e;return(0,t.jsxs)(o,{children:[(0,t.jsx)(m,{}),(0,t.jsxs)(r.UC,{className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",i),ref:s,...c,children:[a,(0,t.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(l.A,{className:"size-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});x.displayName=r.UC.displayName;let u=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...a})};u.displayName="DialogHeader";let h=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a})};h.displayName="DialogFooter";let f=i.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.hE,{className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",a),ref:s,...l})});f.displayName=r.hE.displayName;let p=i.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.VY,{className:(0,n.cn)("text-sm text-muted-foreground",a),ref:s,...l})});p.displayName=r.VY.displayName},57684:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>eA});var t=a(95155),r=a(18271),l=a(50594),i=a(45876),n=a(41784),c=a(75074),d=a(50172),o=a(51920),m=a(12115),x=a(30285),u=a(85511),h=a(62523),f=a(29797),p=a(14636),g=a(85127),j=a(40879),N=a(30425),v=a(54036);function y(){var e,s,a;let[r,l]=(0,m.useState)([]),[i,y]=(0,m.useState)(!0),[b,A]=(0,m.useState)(""),[w,S]=(0,m.useState)(""),[E,C]=(0,m.useState)(""),[R,_]=(0,m.useState)(),[z,k]=(0,m.useState)(),[I,D]=(0,m.useState)(1),[U,P]=(0,m.useState)(0),{toast:T}=(0,j.dj)(),L=Math.ceil(U/10),M=(0,m.useCallback)(async()=>{y(!0);try{let e=await N.adminService.getAuditLogs({action:w,endDate:z,limit:10,page:I,search:b,startDate:R,userId:E});l(e.data),P(e.pagination.total)}catch(s){var e;T({description:null!=(e=s.message)?e:"Failed to load audit log data.",title:"Error fetching audit logs",variant:"destructive"})}finally{y(!1)}},[I,b,w,E,R,z,T,U]);(0,m.useEffect)(()=>{M()},[M]);let F=e=>{D(e)};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"Audit Log Viewer"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.A,{className:"absolute left-2 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,t.jsx)(h.p,{className:"w-[250px] pl-8",onChange:e=>{A(e.target.value),D(1)},placeholder:"Search logs (user, action, details)...",value:b})]}),(0,t.jsx)(h.p,{className:"w-[200px]",onChange:e=>{S(e.target.value),D(1)},placeholder:"Filter by action (e.g., LOGIN)",value:w}),(0,t.jsx)(h.p,{className:"w-[200px]",onChange:e=>{C(e.target.value),D(1)},placeholder:"Filter by User ID",value:E}),(0,t.jsxs)(p.AM,{children:[(0,t.jsx)(p.Wv,{asChild:!0,children:(0,t.jsxs)(x.$,{className:(0,v.cn)("w-[280px] justify-start text-left font-normal",!R&&!z&&"text-muted-foreground"),variant:"outline",children:[(0,t.jsx)(o.A,{className:"mr-2 size-4"}),R?z?(0,t.jsxs)(t.Fragment,{children:[(0,n.GP)(R,"LLL dd, y")," -"," ",(0,n.GP)(z,"LLL dd, y")]}):(0,n.GP)(R,"LLL dd, y"):(0,t.jsx)("span",{children:"Pick a date range"})]})}),(0,t.jsx)(p.hl,{className:"flex w-auto p-0",children:(0,t.jsx)(u.V,{mode:"range",numberOfMonths:2,onSelect:e=>{_(null==e?void 0:e.from),k(null==e?void 0:e.to),D(1)},selected:{from:R,to:z}})})]}),(null!=(a=null!=(s=null!=(e=null!=R?R:z)?e:w)?s:E)?a:b)&&(0,t.jsx)(x.$,{onClick:()=>{A(""),S(""),C(""),_(void 0),k(void 0),D(1)},variant:"outline",children:"Clear Filters"})]})]}),i?(0,t.jsxs)("div",{className:"flex h-64 items-center justify-center",children:[(0,t.jsx)(d.A,{className:"size-8 animate-spin text-primary"}),(0,t.jsx)("span",{className:"ml-2 text-lg",children:"Loading audit logs..."})]}):(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(g.XI,{children:[(0,t.jsx)(g.A0,{children:(0,t.jsxs)(g.Hj,{children:[(0,t.jsx)(g.nd,{className:"w-[100px]",children:"ID"}),(0,t.jsx)(g.nd,{children:"Timestamp"}),(0,t.jsx)(g.nd,{children:"User Email"}),(0,t.jsx)(g.nd,{children:"Action"}),(0,t.jsx)(g.nd,{children:"Details"}),(0,t.jsx)(g.nd,{children:"IP Address"})]})}),(0,t.jsx)(g.BF,{children:0===r.length?(0,t.jsx)(g.Hj,{children:(0,t.jsx)(g.nA,{className:"h-24 text-center",colSpan:6,children:"No audit logs found."})}):r.map(e=>(0,t.jsxs)(g.Hj,{children:[(0,t.jsx)(g.nA,{className:"font-medium",children:e.id}),(0,t.jsx)(g.nA,{children:new Date(e.timestamp).toLocaleString()}),(0,t.jsx)(g.nA,{children:e.userId||"N/A"}),(0,t.jsx)(g.nA,{children:e.action}),(0,t.jsx)(g.nA,{className:"max-w-[300px] truncate",children:e.details}),(0,t.jsx)(g.nA,{children:"N/A"})," "]},e.id))})]})}),U>0&&(0,t.jsx)(f.dK,{children:(0,t.jsxs)(f.Iu,{children:[(0,t.jsx)(f.cU,{children:(0,t.jsx)(f.Eb,{className:1===I?"pointer-events-none opacity-50":"",onClick:e=>{e.preventDefault(),F(I-1)}})}),Array.from({length:L},(e,s)=>(0,t.jsx)(f.cU,{children:(0,t.jsx)(f.n$,{isActive:I===s+1,onClick:e=>{e.preventDefault(),F(s+1)},children:s+1})},s)),(0,t.jsx)(f.cU,{children:(0,t.jsx)(f.WA,{className:I===L?"pointer-events-none opacity-50":"",onClick:e=>{e.preventDefault(),F(I+1)}})})]})})]})}var b=a(95120),A=a(19968),w=a(31949),S=a(26854),E=a(6874),C=a.n(E),R=a(55365),_=a(66695),z=a(17313),k=a(45727),I=a(67554),D=a(77223),U=a(45731),P=a(37648),T=a(26126),L=a(10694),M=a(77170);function F(){let[e,s]=(0,m.useState)({entries:[],size:0}),[a,r]=(0,m.useState)({}),[l,i]=(0,m.useState)(!1),n=()=>{s(M.Qb.getStats()),r({})};(0,m.useEffect)(()=>{n();let e=setInterval(n,1e3);return()=>clearInterval(e)},[]);let c=async()=>{i(!0),n(),setTimeout(()=>i(!1),500)},d=e=>e<0?"Expired":e<1e3?"".concat(Math.round(e),"ms"):e<6e4?"".concat(Math.round(e/1e3),"s"):"".concat(Math.round(e/6e4),"m"),o=e=>e.includes("health")?"Health":e.includes("performance")?"Performance":e.includes("errors")?"Errors":"Other",u=e=>e.includes("health")?"bg-green-100 text-green-800":e.includes("performance")?"bg-blue-100 text-blue-800":e.includes("errors")?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800",h=e.entries.filter(e=>e.key.startsWith("admin:"));return(0,t.jsxs)(_.Zp,{className:"shadow-md",children:[(0,t.jsx)(_.aR,{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(_.ZB,{className:"flex items-center gap-2 text-xl font-semibold text-primary",children:[(0,t.jsx)(k.A,{className:"size-5"}),"Cache Status"]}),(0,t.jsx)("p",{className:"mt-1 text-sm text-muted-foreground",children:"Request cache performance and timing"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(x.$,{className:"flex items-center gap-2",disabled:l,onClick:c,size:"sm",variant:"outline",children:[(0,t.jsx)(I.A,{className:"size-4 ".concat(l?"animate-spin":"")}),"Refresh"]}),(0,t.jsxs)(x.$,{className:"flex items-center gap-2 text-red-600 hover:text-red-700",onClick:()=>{N.aN.clearAll(),n()},size:"sm",variant:"outline",children:[(0,t.jsx)(D.A,{className:"size-4"}),"Clear Cache"]}),(0,t.jsxs)(x.$,{className:"flex items-center gap-2 text-orange-600 hover:text-orange-700",onClick:()=>{N.aN.clearAll(),n()},size:"sm",variant:"outline",children:[(0,t.jsx)(U.A,{className:"size-4"}),"Reset Breakers"]})]})]})}),(0,t.jsx)(_.Wu,{className:"p-5",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 md:grid-cols-4",children:[(0,t.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-primary",children:e.size}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Total Entries"})]}),(0,t.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600",children:h.length}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Admin Entries"})]}),(0,t.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:d(M.xR.HEALTH_STATUS)}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Health Cache"})]}),(0,t.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:d(M.xR.PERFORMANCE_METRICS)}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Performance Cache"})]})]}),h.length>0?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold uppercase tracking-wide text-muted-foreground",children:"Active Cache Entries"}),h.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-gray-50 p-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(T.E,{className:u(e.key),children:o(e.key)}),(0,t.jsx)("span",{className:"font-mono text-sm text-gray-600",children:e.key.replace("admin:","")})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1 text-muted-foreground",children:[(0,t.jsx)(P.A,{className:"size-3"}),"Age: ",d(e.age)]}),(0,t.jsx)("div",{className:"font-medium ".concat(e.expiresIn>0?"text-green-600":"text-red-600"),children:e.expiresIn>0?"Expires in ".concat(d(e.expiresIn)):"Expired"})]})]},s))]}):(0,t.jsxs)("div",{className:"py-8 text-center text-muted-foreground",children:[(0,t.jsx)(k.A,{className:"mx-auto mb-3 size-12 opacity-50"}),(0,t.jsx)("p",{children:"No cache entries found"}),(0,t.jsx)("p",{className:"text-sm",children:"Cache entries will appear here after API calls"})]}),(0,t.jsxs)("div",{className:"mt-6 rounded-lg bg-orange-50 p-4",children:[(0,t.jsxs)("h4",{className:"mb-3 flex items-center gap-2 font-semibold text-orange-900",children:[(0,t.jsx)(U.A,{className:"size-4"}),"Circuit Breaker Status"]}),(0,t.jsx)("div",{className:"grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4",children:Object.entries(a).map(e=>{let[s,a]=e;return(0,t.jsxs)("div",{className:"rounded-lg border p-3 ".concat(a.state===L.vz.CLOSED?"border-green-200 bg-green-50":a.state===L.vz.HALF_OPEN?"border-yellow-200 bg-yellow-50":"border-red-200 bg-red-50"),children:[(0,t.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium capitalize",children:s}),(0,t.jsxs)(T.E,{className:a.state===L.vz.CLOSED?"bg-green-100 text-green-800":a.state===L.vz.HALF_OPEN?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800",children:[a.state===L.vz.CLOSED&&(0,t.jsx)(U.A,{className:"mr-1 size-3"}),a.state===L.vz.OPEN&&(0,t.jsx)(w.A,{className:"mr-1 size-3"}),a.state===L.vz.HALF_OPEN&&(0,t.jsx)(I.A,{className:"mr-1 size-3"}),a.state]})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,t.jsxs)("div",{children:["Failures: ",a.failureCount]}),a.timeUntilRetry&&(0,t.jsxs)("div",{children:["Retry in: ",d(a.timeUntilRetry)]})]})]},s)})})]}),(0,t.jsxs)("div",{className:"mt-6 rounded-lg bg-blue-50 p-4",children:[(0,t.jsx)("h4",{className:"mb-2 font-semibold text-blue-900",children:"Cache Configuration"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm md:grid-cols-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-blue-800",children:"Health Status:"}),(0,t.jsx)("span",{className:"ml-2 text-blue-600",children:d(M.xR.HEALTH_STATUS)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-blue-800",children:"Performance:"}),(0,t.jsx)("span",{className:"ml-2 text-blue-600",children:d(M.xR.PERFORMANCE_METRICS)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-blue-800",children:"Error Logs:"}),(0,t.jsx)("span",{className:"ml-2 text-blue-600",children:d(M.xR.ERROR_LOGS)})]})]})]})]})})]})}var O=a(24371),V=a(9572),B=a(88240),W=a(6560),Z=a(11133),$=a(68856);function H(e){let{className:s,message:a,onRetry:r}=e;return(0,t.jsxs)(R.Fc,{className:(0,v.cn)("my-4",s),variant:"destructive",children:[(0,t.jsx)(Z.A,{className:"size-4"}),(0,t.jsx)(R.XL,{children:"Error"}),(0,t.jsx)(R.TN,{children:(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:a}),r&&(0,t.jsxs)(x.$,{className:"flex items-center",onClick:r,size:"sm",variant:"outline",children:[(0,t.jsx)(d.A,{className:"mr-2 size-4"}),"Retry"]})]})})]})}var G=a(66424),J=a(59409),K=a(6548),q=a(89699),X=a(28113);function Y(){let[e,s]=(0,m.useState)(),[a,r]=(0,m.useState)(!1),[i,n]=(0,m.useState)(null),{currentPage:c,data:d,error:o,hasNextPage:x,hasPrevPage:u,isLoading:h,nextPage:f,prevPage:p,refetch:g,totalPages:j}=(0,K.p9)(["adminErrors",e],(s,a)=>N.adminService.getRecentErrors(s,a,e),{pageSize:10});(0,m.useEffect)(()=>{o&&(async()=>{if("status"in o&&(401===o.status||500===o.status)||"code"in o&&("NO_TOKEN"===o.code||"INVALID_TOKEN"===o.code)||o.message.includes("Authentication failed")||o.message.includes("Not Found")){let e=q.SessionManager.getSessionState();if(q.SessionManager.detectTimeout()||!(null==e?void 0:e.isActive))return n('Your session has expired. Click "Refresh Authentication" to renew your session.');try{let e=(0,X.Q)(),s=await e.getSessionInfo();s.isValid?n("Server error occurred. This might be a temporary issue. Try refreshing."):s.isExpired?n('Your session has expired. Click "Refresh Authentication" to renew your session.'):n("Authentication failed. Please refresh the page to sign in again.")}catch(e){n("Authentication system error. Please refresh the page to sign in again.")}}})()},[o]);let v=async()=>{r(!0),n(null),await g(),r(!1)},y=async()=>{r(!0),n(null);try{let e=(0,X.Q)();await e.refreshNow()?await g():n("Failed to refresh authentication. Please sign in again.")}catch(e){n("Authentication refresh failed. Please sign in again.")}finally{r(!1)}},b=e=>"ERROR"===e?(0,t.jsx)(O.A,{className:"size-4 text-red-500"}):"WARNING"===e?(0,t.jsx)(w.A,{className:"size-4 text-yellow-500"}):(0,t.jsx)(l.A,{className:"size-4 text-blue-500"}),A=e=>"ERROR"===e?(0,t.jsx)(T.E,{className:"border-red-500/30 bg-red-500/20 text-red-700",variant:"outline",children:"Error"}):"WARNING"===e?(0,t.jsx)(T.E,{className:"border-yellow-500/30 bg-yellow-500/20 text-yellow-700",variant:"outline",children:"Warning"}):(0,t.jsx)(T.E,{className:"border-blue-500/30 bg-blue-500/20 text-blue-700",variant:"outline",children:"Info"});return(0,t.jsx)(B.A,{children:(0,t.jsxs)(_.Zp,{className:"shadow-md",children:[(0,t.jsxs)(_.aR,{className:"p-5 pb-2",children:[(0,t.jsx)(_.ZB,{className:"text-xl font-semibold text-primary",children:"Recent Errors & Warnings"}),(0,t.jsx)(_.BT,{children:"Latest system errors and warnings"})]}),(0,t.jsx)("div",{className:"px-5 pb-2",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(V.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Filter by level:"}),(0,t.jsxs)(J.l6,{onValueChange:e=>{s("all"===e?void 0:e)},value:null!=e?e:"all",children:[(0,t.jsx)(J.bq,{className:"w-[140px]",children:(0,t.jsx)(J.yv,{placeholder:"All levels"})}),(0,t.jsxs)(J.gC,{children:[(0,t.jsx)(J.eb,{value:"all",children:"All levels"}),(0,t.jsx)(J.eb,{value:"ERROR",children:"Errors only"}),(0,t.jsx)(J.eb,{value:"WARNING",children:"Warnings only"}),(0,t.jsx)(J.eb,{value:"INFO",children:"Info only"})]})]})]})}),(0,t.jsx)(_.Wu,{className:"p-5",children:i?(0,t.jsx)("div",{className:"rounded-md border border-red-200 bg-red-50 p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(O.A,{className:"size-5 text-red-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-red-800",children:"Authentication Error"}),(0,t.jsx)("p",{className:"text-sm text-red-700",children:i}),(0,t.jsxs)("div",{className:"mt-3 flex space-x-2",children:[(0,t.jsx)(W.r,{actionType:"primary",isLoading:a,loadingText:"Refreshing...",onClick:y,size:"sm",children:"Refresh Authentication"}),(0,t.jsx)(W.r,{actionType:"tertiary",onClick:()=>{globalThis.location.reload()},size:"sm",children:"Refresh Page"})]})]})]})}):h||a?(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)($.E,{className:"h-6 w-full"}),(0,t.jsx)($.E,{className:"h-6 w-full"}),(0,t.jsx)($.E,{className:"h-6 w-full"}),(0,t.jsx)($.E,{className:"h-6 w-full"}),(0,t.jsx)($.E,{className:"h-6 w-full"})]}):o?(0,t.jsx)(H,{message:o.message,onRetry:g}):d&&d.length>0?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(G.F,{className:"h-[300px] pr-4",children:(0,t.jsx)("div",{className:"space-y-3",children:d.map(e=>(0,t.jsxs)("div",{className:"rounded-md border p-3 transition-colors hover:bg-accent/50",children:[(0,t.jsxs)("div",{className:"mb-1 flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[b(e.level),(0,t.jsx)("span",{className:"font-medium",children:e.message})]}),A(e.level)]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"text-xs text-muted-foreground",children:new Date(e.timestamp).toLocaleString()}),e.source&&(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Source: ",e.source]})]}),e.details&&Object.keys(e.details).length>0&&(0,t.jsx)("div",{className:"mt-2 rounded bg-muted p-2 text-xs",children:(0,t.jsx)("pre",{className:"whitespace-pre-wrap",children:JSON.stringify(e.details,null,2)})})]},e.id))})}),j>1&&(0,t.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,t.jsx)(W.r,{actionType:"tertiary",disabled:!u||h||a,onClick:p,size:"sm",children:"Previous"}),(0,t.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",c," of ",j]}),(0,t.jsx)(W.r,{actionType:"tertiary",disabled:!x||h||a,onClick:f,size:"sm",children:"Next"})]})]}):(0,t.jsxs)("div",{className:"p-8 text-center text-muted-foreground",children:[(0,t.jsx)(w.A,{className:"mx-auto mb-2 size-8 text-muted-foreground/50"}),(0,t.jsx)("p",{children:"No errors or warnings found for the selected filter."}),e&&(0,t.jsx)("p",{className:"mt-2 text-sm",children:"Try changing the filter to see more results."})]})}),(0,t.jsx)(_.wL,{className:"p-5",children:(0,t.jsx)(W.r,{actionType:"tertiary",className:"w-full",icon:(0,t.jsx)(I.A,{className:"size-4"}),isLoading:a||h,loadingText:"Refreshing...",onClick:v,size:"sm",children:"Refresh Logs"})})]})})}var Q=a(15292);function ee(){let[e,s]=(0,m.useState)(null),[a,r]=(0,m.useState)(!1),l=async()=>{r(!0);try{let e=N.aN.getStats(),a=[];s({cacheStats:{errorCacheKeys:a.map(e=>e.key||"unknown"),errorEntries:a.length,totalEntries:e.size},circuitBreakers:{},timestamp:new Date().toISOString()})}catch(e){console.error("Failed to get debug info:",e)}finally{r(!1)}},i=e=>{switch(e){case L.vz.CLOSED:return"bg-green-100 text-green-800";case L.vz.HALF_OPEN:return"bg-yellow-100 text-yellow-800";case L.vz.OPEN:return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return(0,t.jsxs)(_.Zp,{className:"shadow-md",children:[(0,t.jsx)(_.aR,{className:"p-5",children:(0,t.jsxs)(_.ZB,{className:"flex items-center gap-2 text-xl font-semibold text-primary",children:[(0,t.jsx)(Q.A,{className:"size-5"}),"Error Logs Debug"]})}),(0,t.jsx)(_.Wu,{className:"p-5",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsxs)(x.$,{className:"flex items-center gap-2",disabled:a,onClick:l,children:[(0,t.jsx)(I.A,{className:"size-4 ".concat(a?"animate-spin":"")}),"Get Debug Info"]}),(0,t.jsxs)(x.$,{className:"flex items-center gap-2 text-orange-600 hover:text-orange-700",onClick:()=>{N.aN.clearAll(),console.log("\uD83D\uDD04 Circuit breakers reset"),l()},variant:"outline",children:[(0,t.jsx)(U.A,{className:"size-4"}),"Reset Circuit Breakers"]}),(0,t.jsxs)(x.$,{className:"flex items-center gap-2 text-blue-600 hover:text-blue-700",onClick:()=>{N.aN.clearAll(),console.log("\uD83D\uDD04 Error cache cleared"),l()},variant:"outline",children:[(0,t.jsx)(D.A,{className:"size-4"}),"Clear Error Cache"]}),(0,t.jsxs)(x.$,{className:"flex items-center gap-2 text-red-600 hover:text-red-700",onClick:()=>{N.aN.clearAll(),console.log("\uD83D\uDD04 All cache cleared"),l()},variant:"outline",children:[(0,t.jsx)(D.A,{className:"size-4"}),"Clear All Cache"]})]}),e&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"rounded-lg bg-gray-50 p-4",children:[(0,t.jsxs)("h4",{className:"mb-3 flex items-center gap-2 font-semibold",children:[(0,t.jsx)(U.A,{className:"size-4"}),"Circuit Breaker Status"]}),(0,t.jsx)("div",{className:"grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4",children:Object.entries(e.circuitBreakers).map(e=>{let[s,a]=e;return(0,t.jsxs)("div",{className:"rounded border bg-white p-3",children:[(0,t.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium capitalize",children:s}),(0,t.jsx)(T.E,{className:i(a.state),children:a.state})]}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,t.jsxs)("div",{children:["Failures: ",a.failureCount]}),a.timeUntilRetry&&(0,t.jsxs)("div",{children:["Retry in:"," ",Math.round(a.timeUntilRetry/1e3),"s"]})]})]},s)})})]}),(0,t.jsxs)("div",{className:"rounded-lg bg-blue-50 p-4",children:[(0,t.jsx)("h4",{className:"mb-3 font-semibold text-blue-900",children:"Cache Status"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm md:grid-cols-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-blue-800",children:"Total Entries:"}),(0,t.jsx)("span",{className:"ml-2 text-blue-600",children:e.cacheStats.totalEntries})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-blue-800",children:"Error Entries:"}),(0,t.jsx)("span",{className:"ml-2 text-blue-600",children:e.cacheStats.errorEntries})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium text-blue-800",children:"Last Updated:"}),(0,t.jsx)("span",{className:"ml-2 text-blue-600",children:new Date(e.timestamp).toLocaleTimeString()})]})]}),e.cacheStats.errorCacheKeys.length>0&&(0,t.jsxs)("div",{className:"mt-3",children:[(0,t.jsx)("div",{className:"mb-2 font-medium text-blue-800",children:"Error Cache Keys:"}),(0,t.jsx)("div",{className:"space-y-1",children:e.cacheStats.errorCacheKeys.map((e,s)=>(0,t.jsx)("div",{className:"rounded border bg-white p-2 font-mono text-xs",children:e},s))})]})]}),(0,t.jsxs)("div",{className:"rounded-lg bg-yellow-50 p-4",children:[(0,t.jsxs)("h4",{className:"mb-3 flex items-center gap-2 font-semibold text-yellow-900",children:[(0,t.jsx)(w.A,{className:"size-4"}),"Troubleshooting Tips"]}),(0,t.jsxs)("ul",{className:"space-y-1 text-sm text-yellow-800",children:[(0,t.jsx)("li",{children:"• If circuit breakers are OPEN (red), reset them and try again"}),(0,t.jsx)("li",{children:"• If error cache is stale, clear it to force fresh data"}),(0,t.jsx)("li",{children:"• Check browser console for network errors"}),(0,t.jsx)("li",{children:"• Verify backend server is running and accessible"}),(0,t.jsx)("li",{children:"• Check if rate limiting is affecting requests"})]})]})]}),!e&&(0,t.jsxs)("div",{className:"py-8 text-center text-muted-foreground",children:[(0,t.jsx)(Q.A,{className:"mx-auto mb-3 size-12 opacity-50"}),(0,t.jsx)("p",{children:'Click "Get Debug Info" to see error logs debugging information'})]})]})})]})}function es(){return(0,t.jsxs)(_.Zp,{className:"border-none shadow-none",children:[(0,t.jsxs)(_.aR,{className:"px-0 pt-0",children:[(0,t.jsx)(_.ZB,{className:"text-2xl font-bold",children:"Supabase Diagnostics"}),(0,t.jsx)(_.BT,{children:"Monitor and troubleshoot your Supabase database connection"})]}),(0,t.jsxs)(_.Wu,{className:"w-full max-w-full overflow-hidden px-0",children:[(0,t.jsxs)(R.Fc,{className:"mb-6",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),(0,t.jsx)(R.XL,{children:"Enhanced Reliability Dashboard Available"}),(0,t.jsxs)(R.TN,{children:["System health and performance monitoring has been moved to the new Reliability Dashboard with real-time updates, advanced metrics, and comprehensive monitoring capabilities.",(0,t.jsx)("div",{className:"mt-3",children:(0,t.jsx)(x.$,{asChild:!0,variant:"outline",size:"sm",children:(0,t.jsxs)(C(),{href:"/reliability",className:"flex items-center gap-2",children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),"Go to Reliability Dashboard",(0,t.jsx)(A.A,{className:"h-4 w-4"})]})})})]})]}),(0,t.jsxs)(z.tU,{className:"w-full max-w-full overflow-hidden",defaultValue:"errors",children:[(0,t.jsxs)(z.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsxs)(z.Xi,{className:"flex items-center",value:"errors",children:[(0,t.jsx)(w.A,{className:"mr-2 size-4"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Error Logs"})]}),(0,t.jsxs)(z.Xi,{className:"flex items-center",value:"cache",children:[(0,t.jsx)(S.A,{className:"mr-2 size-4"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:"Cache Status"})]})]}),(0,t.jsx)(z.av,{className:"mt-4",value:"errors",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(Y,{}),(0,t.jsx)(ee,{})]})}),(0,t.jsx)(z.av,{className:"mt-4",value:"cache",children:(0,t.jsx)(F,{})})]})]})]})}var ea=a(8376),et=a(3561),er=a(18763),el=a(40207),ei=a(18046),en=a(13896),ec=a(50286),ed=a(17607),eo=a(90010),em=a(91394),ex=a(47262),eu=a(54165),eh=a(44838),ef=a(91721),ep=a(85057),eg=a(83761);let ej=e=>{var s,a;let t=null!=(a=null!=(s=e.fullName)?s:e.name)?a:"Unknown",r=e.employeeId?" (ID: ".concat(e.employeeId,")"):"",l=e.position?" - ".concat(e.position):"";return"".concat(t).concat(r).concat(l)};function eN(e){var s;let{allowClear:a=!0,className:r,disabled:l=!1,error:i,label:n,onValueChange:c,placeholder:d="Select employee...",required:o=!1,value:m}=e,{data:x=[],error:u,isLoading:h}=(0,eg.nR)(),f=x.find(e=>e.id===m);return h?(0,t.jsxs)("div",{className:(0,v.cn)("space-y-2",r),children:[n&&(0,t.jsxs)(ep.J,{className:"text-sm font-medium",children:[n,o&&(0,t.jsx)("span",{className:"ml-1 text-destructive",children:"*"})]}),(0,t.jsx)(J.l6,{disabled:!0,children:(0,t.jsx)(J.bq,{children:(0,t.jsx)(J.yv,{placeholder:"Loading employees..."})})})]}):u?(0,t.jsxs)("div",{className:(0,v.cn)("space-y-2",r),children:[n&&(0,t.jsxs)(ep.J,{className:"text-sm font-medium",children:[n,o&&(0,t.jsx)("span",{className:"ml-1 text-destructive",children:"*"})]}),(0,t.jsx)(J.l6,{disabled:!0,children:(0,t.jsx)(J.bq,{children:(0,t.jsx)(J.yv,{placeholder:"Error loading employees"})})}),(0,t.jsx)("p",{className:"text-sm text-destructive",children:"Failed to load employees"})]}):(0,t.jsxs)("div",{className:(0,v.cn)("space-y-2",r),children:[n&&(0,t.jsxs)(ep.J,{className:"text-sm font-medium",children:[n,o&&(0,t.jsx)("span",{className:"ml-1 text-destructive",children:"*"})]}),(0,t.jsxs)(J.l6,{disabled:l,onValueChange:e=>{if("clear"===e)return void c(null);let s=Number.parseInt(e,10);c(Number.isNaN(s)?null:s)},value:null!=(s=null==m?void 0:m.toString())?s:"",children:[(0,t.jsx)(J.bq,{className:(0,v.cn)(i&&"border-destructive focus:border-destructive"),children:(0,t.jsx)(J.yv,{placeholder:d,children:f&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(ef.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"truncate",children:ej(f)})]})})}),(0,t.jsxs)(J.gC,{children:[a&&m&&(0,t.jsx)(J.eb,{className:"text-muted-foreground",value:"clear",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-xs",children:"\xd7"}),(0,t.jsx)("span",{children:"Clear selection"})]})}),x.map(e=>{var s,a;return(0,t.jsx)(J.eb,{value:e.id.toString(),children:(0,t.jsxs)("div",{className:"flex w-full items-center space-x-2",children:[(0,t.jsx)(ef.A,{className:"size-4 shrink-0 text-muted-foreground"}),(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsx)("div",{className:"truncate font-medium",children:null!=(a=null!=(s=e.fullName)?s:e.name)?a:"Unknown"}),(0,t.jsxs)("div",{className:"truncate text-xs text-muted-foreground",children:[e.employeeId&&"ID: ".concat(e.employeeId),e.position&&" • ".concat(e.position),e.department&&" • ".concat(e.department)]})]})]})},e.id)})]})]}),i&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:i})]})}var ev=a(84411),ey=a(53712);function eb(){var e,s,a;let[r,l]=(0,m.useState)([]),[i,n]=(0,m.useState)(!0),[c,o]=(0,m.useState)(!1),[u,f]=(0,m.useState)(null),[p,g]=(0,m.useState)({email:"",emailVerified:!1,employee_id:"",full_name:"",isActive:!0,phone:"",role:"USER"}),[j,v]=(0,m.useState)(0),[y]=(0,m.useState)(10),{showFormError:b,showFormSuccess:A}=(0,ey.t6)(),[w,S]=(0,m.useState)(!1),[E,C]=(0,m.useState)(null),[R,k]=(0,m.useState)(!1),[P,L]=(0,m.useState)(null),[M,F]=(0,m.useState)(null),[V,B]=(0,m.useState)(null),[W,Z]=(0,m.useState)(!1),$=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),H=e=>e?new Date(e).toLocaleDateString("en-US",{day:"numeric",month:"short",year:"numeric"}):"Never",G=e=>{if(!e)return"Never";let s=new Date,a=new Date(e),t=Math.floor((s.getTime()-a.getTime())/1e3);return t<60?"Just now":t<3600?"".concat(Math.floor(t/60),"m ago"):t<86400?"".concat(Math.floor(t/3600),"h ago"):t<2592e3?"".concat(Math.floor(t/86400),"d ago"):H(e)},K=(e,s)=>{if(null==s?void 0:s.trim()){var a,t;let e=s.trim().split(" ");return e.length>1?"".concat((null==(a=e[0])?void 0:a[0])||"").concat((null==(t=e.at(-1))?void 0:t[0])||"").toUpperCase():s.slice(0,2).toUpperCase()}return e.slice(0,2).toUpperCase()},q=(0,m.useCallback)(async()=>{n(!0),B(null);try{let e=await N.adminService.getAllUsers({limit:100,page:1,search:""});l(e.data||[]);let s=e.pagination||{limit:100,total:0};v(s.total)}catch(a){var e,s;if((null==a?void 0:a.status)===401||(null==a?void 0:a.status)===500||(null==a?void 0:a.code)==="NO_TOKEN"||(null==a?void 0:a.code)==="INVALID_TOKEN"||(null==a||null==(e=a.message)?void 0:e.includes("Authentication failed"))||(null==a||null==(s=a.message)?void 0:s.includes("Failed to fetch users")))try{let e=(0,X.Q)(),s=await e.getSessionInfo();s.isValid?B("Server error occurred. This might be a temporary issue. Try refreshing."):s.isExpired?B('Your session has expired. Click "Refresh Authentication" to renew your session.'):B("Authentication failed. Please refresh the page to sign in again.")}catch(e){B("Authentication system error. Please refresh the page to sign in again.")}else b(a,{errorDescription:a.message||"Failed to load user data.",errorTitle:"Error fetching users"})}finally{n(!1)}},[b]);(0,m.useEffect)(()=>{q()},[q]);let Y=async()=>{Z(!0),B(null);try{let e=(0,X.Q)();await e.refreshNow()?await q():B("Failed to refresh authentication. Please sign in again.")}catch(e){B("Authentication refresh failed. Please sign in again.")}finally{Z(!1)}},Q=async()=>{if(!$(p.email))return void b(Error("Please enter a valid email address."),{errorTitle:"Validation Error"});n(!0);try{let e=await N.adminService.createUser(p);A({successDescription:"User ".concat(e.email," has been added."),successTitle:"User created"}),o(!1),g({email:"",emailVerified:!1,employee_id:"",full_name:"",isActive:!0,phone:"",role:"USER"}),q()}catch(e){b(e.message||"Failed to create user.",{errorTitle:"Error creating user"})}finally{n(!1)}},ee=async()=>{if(u){if(!$(u.email||""))return void b("Please enter a valid email address.",{errorTitle:"Validation Error"});n(!0);try{let e=await N.adminService.updateUser(u.id,{email:u.email,emailVerified:!!u.email_confirmed_at,isActive:u.isActive,role:u.role});A({successDescription:"User ".concat(e.email," has been updated."),successTitle:"User updated"}),o(!1),f(null),q()}catch(e){b(e.message||"Failed to update user.",{errorTitle:"Error updating user"})}finally{n(!1)}}},es=async()=>{if(E){n(!0);try{await N.adminService.deleteUser(E),A({successDescription:"User has been successfully deleted.",successTitle:"User deleted"}),q()}catch(e){b(e.message||"Failed to delete user.",{errorTitle:"Error deleting user"})}finally{n(!1),S(!1),C(null)}}},ef=async()=>{if(P&&null!==M){n(!0);try{let e=await N.adminService.toggleUserActivation(P,!M);A({successDescription:"User ".concat(e.email," is now ").concat(e.isActive?"active":"inactive","."),successTitle:"User status updated"}),q()}catch(e){b(e.message||"Failed to toggle user activation.",{errorTitle:"Error updating status"})}finally{n(!1),k(!1),L(null),F(null)}}},eg=e=>{switch(e){case"ADMIN":return"bg-purple-500 hover:bg-purple-600 text-white";case"MANAGER":return"bg-blue-500 hover:bg-blue-600 text-white";case"READONLY":return"bg-yellow-500 hover:bg-yellow-600 text-white";case"SUPER_ADMIN":return"bg-red-500 hover:bg-red-600 text-white";case"USER":return"bg-green-500 hover:bg-green-600 text-white";default:return"bg-gray-500 hover:bg-gray-600 text-white"}},ej=[{accessorKey:"email",cell:e=>{let{row:s}=e,a=s.original;return(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(em.eu,{className:"size-10",children:[(0,t.jsx)(em.BK,{alt:a.email,src:""}),(0,t.jsx)(em.q5,{className:"text-sm",children:K(a.email,a.full_name)})]}),(0,t.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("p",{className:"truncate text-sm font-medium",children:a.full_name||a.email.split("@")[0]}),a.email_confirmed_at&&(0,t.jsx)(U.A,{className:"size-3 text-green-500"})]}),(0,t.jsx)("p",{className:"truncate text-xs text-muted-foreground",children:a.email}),a.employee_id&&(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["ID: ",a.employee_id]})]})]})},header:"User"},{accessorKey:"role",cell:e=>{let{row:s}=e,a=s.original;return(0,t.jsx)(T.E,{className:eg(a.role),children:(a.role||"USER").replace("_"," ")})},header:"Role"},{accessorKey:"isActive",cell:e=>{let{row:s}=e,a=s.original;return(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(T.E,{variant:a.isActive?"default":"destructive",children:a.isActive?"Active":"Inactive"}),a.email_confirmed_at?(0,t.jsx)(ea.A,{className:"size-4 text-green-500"}):(0,t.jsx)(O.A,{className:"size-4 text-red-500"})]})},header:"Status"},{accessorKey:"last_sign_in_at",cell:e=>{let{row:s}=e,a=s.original;return(0,t.jsx)("div",{className:"text-sm",children:G(a.last_sign_in_at)})},header:"Last Activity"},{accessorKey:"created_at",cell:e=>{let{row:s}=e,a=s.original;return(0,t.jsx)("div",{className:"text-sm",children:H(a.created_at)})},header:"Joined"},{cell:e=>{let{row:s}=e,a=s.original;return(0,t.jsxs)(eh.rI,{children:[(0,t.jsx)(eh.ty,{asChild:!0,children:(0,t.jsxs)(x.$,{className:"size-8 p-0",variant:"ghost",children:[(0,t.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,t.jsx)(et.A,{className:"size-4"})]})}),(0,t.jsxs)(eh.SQ,{align:"end",children:[(0,t.jsx)(eh.lp,{children:"Actions"}),(0,t.jsxs)(eh._2,{onClick:()=>{f(a),o(!0)},children:[(0,t.jsx)(er.A,{className:"mr-2 size-4"}),"Edit user"]}),(0,t.jsx)(eh.mB,{}),(0,t.jsxs)(eh._2,{onClick:()=>{L(a.id),F(a.isActive),k(!0)},children:[(0,t.jsx)(el.A,{className:"mr-2 size-4"}),a.isActive?"Deactivate":"Activate"]}),(0,t.jsxs)(eh._2,{className:"text-red-600 focus:text-red-600",onClick:()=>{C(a.id),S(!0)},children:[(0,t.jsx)(D.A,{className:"mr-2 size-4"}),"Delete user"]})]})]})},header:"Actions",id:"actions"}];return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-3xl font-bold tracking-tight",children:"User Management"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage user accounts, roles, and permissions"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(x.$,{onClick:q,size:"sm",variant:"outline",children:[(0,t.jsx)(I.A,{className:"mr-2 size-4"}),"Refresh"]}),(0,t.jsxs)(eu.lG,{onOpenChange:o,open:c,children:[(0,t.jsx)(eu.zM,{asChild:!0,children:(0,t.jsxs)(x.$,{onClick:()=>{f(null),g({email:"",emailVerified:!1,employee_id:"",full_name:"",isActive:!0,phone:"",role:"USER"})},children:[(0,t.jsx)(ei.A,{className:"mr-2 size-4"})," Add User"]})}),(0,t.jsxs)(eu.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(eu.c7,{children:[(0,t.jsx)(eu.L3,{className:"flex items-center gap-2",children:u?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(er.A,{className:"size-5"}),"Edit User Profile"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(en.A,{className:"size-5"}),"Add New User"]})}),(0,t.jsx)(eu.rr,{children:u?"Update user information and permissions.":"Create a new user account with role and permissions."})]}),(0,t.jsxs)(z.tU,{className:"w-full",defaultValue:"basic",children:[(0,t.jsxs)(z.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsx)(z.Xi,{value:"basic",children:"Basic Info"}),(0,t.jsx)(z.Xi,{value:"advanced",children:"Advanced"})]}),(0,t.jsx)(z.av,{className:"mt-4 space-y-4",value:"basic",children:(0,t.jsxs)("div",{className:"grid gap-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(ep.J,{className:"text-right",htmlFor:"full_name",children:"Full Name"}),(0,t.jsx)(h.p,{className:"col-span-3",id:"full_name",onChange:e=>u?f({...u,full_name:e.target.value}):g({...p,full_name:e.target.value}),placeholder:"Enter full name",value:u?u.full_name||"":p.full_name})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(ep.J,{className:"text-right",htmlFor:"email",children:"Email *"}),(0,t.jsx)(h.p,{className:"col-span-3",id:"email",onChange:e=>u?f({...u,email:e.target.value}):g({...p,email:e.target.value}),placeholder:"<EMAIL>",type:"email",value:u?u.email:p.email})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(ep.J,{className:"text-right",htmlFor:"phone",children:"Phone"}),(0,t.jsx)(h.p,{className:"col-span-3",id:"phone",onChange:e=>u?f({...u,phone:e.target.value}):g({...p,phone:e.target.value}),placeholder:"+****************",type:"tel",value:u?u.phone||"":p.phone})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(ep.J,{className:"text-right",htmlFor:"role",children:"Role *"}),(0,t.jsxs)(J.l6,{onValueChange:e=>u?f({...u,role:e}):g({...p,role:e}),value:u?u.role:p.role,children:[(0,t.jsx)(J.bq,{className:"col-span-3",children:(0,t.jsx)(J.yv,{placeholder:"Select a role"})}),(0,t.jsxs)(J.gC,{children:[(0,t.jsx)(J.eb,{value:"SUPER_ADMIN",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(U.A,{className:"size-4 text-red-500"}),"Super Admin"]})}),(0,t.jsx)(J.eb,{value:"ADMIN",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(U.A,{className:"size-4 text-purple-500"}),"Admin"]})}),(0,t.jsx)(J.eb,{value:"MANAGER",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(ec.A,{className:"size-4 text-blue-500"}),"Manager"]})}),(0,t.jsx)(J.eb,{value:"USER",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(ec.A,{className:"size-4 text-green-500"}),"User"]})}),(0,t.jsx)(J.eb,{value:"READONLY",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(ed.A,{className:"size-4 text-yellow-500"}),"Read Only"]})})]})]})]})]})}),(0,t.jsx)(z.av,{className:"mt-4 space-y-4",value:"advanced",children:(0,t.jsxs)("div",{className:"grid gap-4",children:[(0,t.jsx)("div",{className:"grid grid-cols-4 items-center gap-4",children:(0,t.jsx)("div",{className:"col-span-4",children:(0,t.jsx)(eN,{allowClear:!0,className:"w-full",label:"Link to Employee (Optional)",onValueChange:e=>u?f({...u,employee_id:e}):g({...p,employee_id:(null==e?void 0:e.toString())||""}),placeholder:"Select an employee to link this user account...",value:u?null!=(a=u.employee_id)?a:null:p.employee_id?Number.parseInt(p.employee_id):null})})}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(ep.J,{className:"text-right",htmlFor:"isActive",children:"Account Status"}),(0,t.jsxs)("div",{className:"col-span-3 flex items-center space-x-2",children:[(0,t.jsx)(ex.S,{checked:u?u.isActive:p.isActive,id:"isActive",onCheckedChange:e=>u?f({...u,isActive:!!e}):g({...p,isActive:!!e})}),(0,t.jsx)(ep.J,{className:"text-sm",htmlFor:"isActive",children:"Active account"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(ep.J,{className:"text-right",htmlFor:"emailVerified",children:"Email Verification"}),(0,t.jsxs)("div",{className:"col-span-3 flex items-center space-x-2",children:[(0,t.jsx)(ex.S,{checked:u?!!u.email_confirmed_at:p.emailVerified,id:"emailVerified",onCheckedChange:e=>u?f({...u,email_confirmed_at:e?new Date().toISOString():null}):g({...p,emailVerified:!!e})}),(0,t.jsx)(ep.J,{className:"text-sm",htmlFor:"emailVerified",children:"Email verified"})]})]})]})})]}),(0,t.jsx)(eu.Es,{children:(0,t.jsxs)(x.$,{disabled:i,onClick:u?ee:Q,type:"submit",children:[i&&(0,t.jsx)(d.A,{className:"mr-2 size-4 animate-spin"}),u?"Save changes":"Add User"]})})]})]})]})]}),(0,t.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,t.jsxs)(_.Zp,{children:[(0,t.jsxs)(_.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(_.ZB,{className:"text-sm font-medium",children:"Total Users"}),(0,t.jsx)(ec.A,{className:"size-4 text-muted-foreground"})]}),(0,t.jsxs)(_.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:j}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[r.filter(e=>e.isActive).length," active"]})]})]}),(0,t.jsxs)(_.Zp,{children:[(0,t.jsxs)(_.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(_.ZB,{className:"text-sm font-medium",children:"Active Users"}),(0,t.jsx)(el.A,{className:"size-4 text-muted-foreground"})]}),(0,t.jsxs)(_.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:r.filter(e=>e.isActive).length}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[Math.round(r.filter(e=>e.isActive).length/Math.max(r.length,1)*100),"% of total"]})]})]}),(0,t.jsxs)(_.Zp,{children:[(0,t.jsxs)(_.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(_.ZB,{className:"text-sm font-medium",children:"Verified Emails"}),(0,t.jsx)(U.A,{className:"size-4 text-muted-foreground"})]}),(0,t.jsxs)(_.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:r.filter(e=>e.email_confirmed_at).length}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[Math.round(r.filter(e=>e.email_confirmed_at).length/Math.max(r.length,1)*100),"% verified"]})]})]}),(0,t.jsxs)(_.Zp,{children:[(0,t.jsxs)(_.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(_.ZB,{className:"text-sm font-medium",children:"Admins"}),(0,t.jsx)(U.A,{className:"size-4 text-muted-foreground"})]}),(0,t.jsxs)(_.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:r.filter(e=>"ADMIN"===e.role||"SUPER_ADMIN"===e.role).length}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"System administrators"})]})]})]})]}),V?(0,t.jsx)("div",{className:"rounded-md border border-red-200 bg-red-50 p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(O.A,{className:"size-5 text-red-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-red-800",children:"Authentication Error"}),(0,t.jsx)("p",{className:"text-sm text-red-700",children:V}),(0,t.jsxs)("div",{className:"mt-3 flex space-x-2",children:[(0,t.jsx)(x.$,{disabled:W,onClick:Y,size:"sm",children:W?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.A,{className:"mr-2 size-4 animate-spin"}),"Refreshing..."]}):"Refresh Authentication"}),(0,t.jsx)(x.$,{onClick:()=>{globalThis.location.reload()},size:"sm",variant:"outline",children:"Refresh Page"})]})]})]})}):i?(0,t.jsxs)("div",{className:"flex h-64 items-center justify-center",children:[(0,t.jsx)(d.A,{className:"size-8 animate-spin text-primary"}),(0,t.jsx)("span",{className:"ml-2 text-lg",children:"Loading users..."})]}):(0,t.jsx)(ev.b,{className:"w-full",columns:ej,data:r,emptyMessage:"No users found.",enableColumnVisibility:!0,enableGlobalFilter:!0,enableRowSelection:!1,pageSize:y,searchColumn:"email",searchPlaceholder:"Search users by email or role..."}),(0,t.jsx)(eo.Lt,{onOpenChange:k,open:R,children:(0,t.jsxs)(eo.EO,{children:[(0,t.jsxs)(eo.wd,{children:[(0,t.jsx)(eo.r7,{children:M?"Deactivate User":"Activate User"}),(0,t.jsxs)(eo.$v,{children:["Are you sure you want to"," ",M?"deactivate":"activate"," user"," ",(0,t.jsx)("span",{className:"font-bold",children:null==(e=r.find(e=>e.id===P))?void 0:e.email}),"?"]})]}),(0,t.jsxs)(eo.ck,{children:[(0,t.jsx)(eo.Zr,{children:"Cancel"}),(0,t.jsx)(eo.Rx,{onClick:ef,children:M?"Deactivate":"Activate"})]})]})}),(0,t.jsx)(eo.Lt,{onOpenChange:S,open:w,children:(0,t.jsxs)(eo.EO,{children:[(0,t.jsxs)(eo.wd,{children:[(0,t.jsx)(eo.r7,{children:"Delete User"}),(0,t.jsxs)(eo.$v,{children:["Are you sure you want to permanently delete user"," ",(0,t.jsx)("span",{className:"font-bold",children:null==(s=r.find(e=>e.id===E))?void 0:s.email}),"? This action cannot be undone."]})]}),(0,t.jsxs)(eo.ck,{children:[(0,t.jsx)(eo.Zr,{children:"Cancel"}),(0,t.jsx)(eo.Rx,{onClick:es,children:"Delete"})]})]})})]})}function eA(){return(0,t.jsx)(i.ProtectedRoute,{allowedRoles:["ADMIN","SUPER_ADMIN"],children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"mb-6 flex items-center space-x-2",children:[(0,t.jsx)(r.A,{className:"size-8 text-primary"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-primary",children:"Admin Dashboard"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"System administration and diagnostics"})]})]}),(0,t.jsxs)(R.Fc,{children:[(0,t.jsx)(l.A,{className:"size-4"}),(0,t.jsx)(R.XL,{children:"Information"}),(0,t.jsx)(R.TN,{children:"This admin dashboard provides system diagnostics and monitoring tools. Access is restricted to ADMIN and SUPER_ADMIN roles."})]}),(0,t.jsxs)(z.tU,{className:"w-full",defaultValue:"system-diagnostics",children:[(0,t.jsxs)(z.j7,{className:"grid w-full grid-cols-3",children:[(0,t.jsx)(z.Xi,{value:"system-diagnostics",children:"System Diagnostics"}),(0,t.jsx)(z.Xi,{value:"user-management",children:"User Management"}),(0,t.jsx)(z.Xi,{value:"audit-logs",children:"Audit Logs"})]}),(0,t.jsx)(z.av,{className:"mt-6",value:"system-diagnostics",children:(0,t.jsx)(es,{})}),(0,t.jsx)(z.av,{className:"mt-6",value:"user-management",children:(0,t.jsx)(eb,{})}),(0,t.jsx)(z.av,{className:"mt-6",value:"audit-logs",children:(0,t.jsx)(y,{})})]})]})})}},66424:(e,s,a)=>{"use strict";a.d(s,{F:()=>n});var t=a(95155),r=a(47655),l=a(12115),i=a(54036);let n=l.forwardRef((e,s)=>{let{children:a,className:l,...n}=e;return(0,t.jsxs)(r.bL,{className:(0,i.cn)("relative overflow-hidden",l),ref:s,...n,children:[(0,t.jsx)(r.LM,{className:"size-full rounded-[inherit]",children:a}),(0,t.jsx)(c,{}),(0,t.jsx)(r.OK,{})]})});n.displayName=r.bL.displayName;let c=l.forwardRef((e,s)=>{let{className:a,orientation:l="vertical",...n}=e;return(0,t.jsx)(r.VM,{className:(0,i.cn)("flex touch-none select-none transition-colors","vertical"===l&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===l&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",a),orientation:l,ref:s,...n,children:(0,t.jsx)(r.lr,{className:"relative flex-1 rounded-full bg-border"})})});c.displayName=r.VM.displayName},73616:(e,s,a)=>{Promise.resolve().then(a.bind(a,57684))},85511:(e,s,a)=>{"use strict";a.d(s,{V:()=>d});var t=a(95155),r=a(965),l=a(73158);a(12115);var i=a(33683),n=a(30285),c=a(54036);function d(e){let{className:s,classNames:a,showOutsideDays:d=!0,...o}=e;return(0,t.jsx)(i.hv,{className:(0,c.cn)("p-3",s),classNames:{caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,c.cn)((0,n.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_disabled:"text-muted-foreground opacity-50",day_hidden:"invisible",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_range_end:"day-range-end",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",head_row:"flex",month:"space-y-4",months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",nav:"space-x-1 flex items-center",nav_button:(0,c.cn)((0,n.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_next:"absolute right-1",nav_button_previous:"absolute left-1",row:"flex w-full mt-2",table:"w-full border-collapse space-y-1",...a},components:{IconLeft:e=>{let{className:s,...a}=e;return(0,t.jsx)(r.A,{className:(0,c.cn)("h-4 w-4",s),...a})},IconRight:e=>{let{className:s,...a}=e;return(0,t.jsx)(l.A,{className:(0,c.cn)("h-4 w-4",s),...a})}},showOutsideDays:d,...o})}d.displayName="Calendar"},88240:(e,s,a)=>{"use strict";a.d(s,{A:()=>o});var t=a(95155),r=a(31949),l=a(67554),i=a(12115),n=a(55365),c=a(30285);class d extends i.Component{static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,s){this.setState({errorInfo:s}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",s.componentStack),this.props.onError&&this.props.onError(e,s)}render(){let{description:e="An unexpected error occurred.",resetLabel:s="Try Again",title:a="Something went wrong"}=this.props;if(this.state.hasError){var i;return this.props.fallback?this.props.fallback:(0,t.jsxs)(n.Fc,{className:"my-4",variant:"destructive",children:[(0,t.jsx)(r.A,{className:"mr-2 size-4"}),(0,t.jsx)(n.XL,{className:"text-lg font-semibold",children:a}),(0,t.jsxs)(n.TN,{className:"mt-2",children:[(0,t.jsx)("p",{className:"mb-2",children:(null==(i=this.state.error)?void 0:i.message)||e}),!1,(0,t.jsxs)(c.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,t.jsx)(l.A,{className:"mr-2 size-4"}),s]})]})]})}return this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}}let o=d},90010:(e,s,a)=>{"use strict";a.d(s,{$v:()=>p,EO:()=>x,Lt:()=>c,Rx:()=>g,Zr:()=>j,ck:()=>h,r7:()=>f,tv:()=>d,wd:()=>u});var t=a(95155),r=a(17649),l=a(12115),i=a(30285),n=a(54036);let c=r.bL,d=r.l9,o=r.ZL,m=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.hJ,{className:(0,n.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...l,ref:s})});m.displayName=r.hJ.displayName;let x=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsxs)(o,{children:[(0,t.jsx)(m,{}),(0,t.jsx)(r.UC,{className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),ref:s,...l})]})});x.displayName=r.UC.displayName;let u=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-2 text-center sm:text-left",s),...a})};u.displayName="AlertDialogHeader";let h=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a})};h.displayName="AlertDialogFooter";let f=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.hE,{className:(0,n.cn)("text-lg font-semibold",a),ref:s,...l})});f.displayName=r.hE.displayName;let p=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.VY,{className:(0,n.cn)("text-sm text-muted-foreground",a),ref:s,...l})});p.displayName=r.VY.displayName;let g=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.rc,{className:(0,n.cn)((0,i.r)(),a),ref:s,...l})});g.displayName=r.rc.displayName;let j=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.ZD,{className:(0,n.cn)((0,i.r)({variant:"outline"}),"mt-2 sm:mt-0",a),ref:s,...l})});j.displayName=r.ZD.displayName},91394:(e,s,a)=>{"use strict";a.d(s,{BK:()=>c,eu:()=>n,q5:()=>d});var t=a(95155),r=a(54011),l=a(12115),i=a(54036);let n=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.bL,{className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",a),ref:s,...l})});n.displayName=r.bL.displayName;let c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r._V,{className:(0,i.cn)("aspect-square h-full w-full",a),ref:s,...l})});c.displayName=r._V.displayName;let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,t.jsx)(r.H4,{className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",a),ref:s,...l})});d.displayName=r.H4.displayName}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,1137,3860,9664,1263,5495,1859,6874,5247,6463,7454,8982,2700,8845,4036,8658,111,3712,283,7515,3615,5320,2999,8441,1684,7358],()=>s(73616)),_N_E=e.O()}]);