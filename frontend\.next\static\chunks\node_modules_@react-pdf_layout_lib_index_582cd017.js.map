{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/layout/lib/index.js"], "sourcesContent": ["import { upperFirst, capitalize, parseFloat as parseFloat$1, without, pick, compose, evolve, mapValues, matchPercent, castArray, isNil, omit, asyncCompose } from '@react-pdf/fns';\nimport * as P from '@react-pdf/primitives';\nimport resolveStyle, { transformColor, flatten } from '@react-pdf/stylesheet';\nimport layoutEngine, { fontSubstitution, wordHyphenation, scriptItemizer, textDecoration, justification, linebreaker, bidi, fromFragments } from '@react-pdf/textkit';\nimport * as Yoga from 'yoga-layout/load';\nimport { loadYoga as loadYoga$1 } from 'yoga-layout/load';\nimport emojiRegex from 'emoji-regex';\nimport resolveImage from '@react-pdf/image';\n\n/**\n * Apply transformation to text string\n *\n * @param {string} text\n * @param {string} transformation type\n * @returns {string} transformed text\n */\nconst transformText = (text, transformation) => {\n    switch (transformation) {\n        case 'uppercase':\n            return text.toUpperCase();\n        case 'lowercase':\n            return text.toLowerCase();\n        case 'capitalize':\n            return capitalize(text);\n        case 'upperfirst':\n            return upperFirst(text);\n        default:\n            return text;\n    }\n};\n\nconst isTspan = (node) => node.type === P.Tspan;\nconst isTextInstance$4 = (node) => node.type === P.TextInstance;\nconst engines$1 = {\n    bidi,\n    linebreaker,\n    justification,\n    textDecoration,\n    scriptItemizer,\n    wordHyphenation,\n    fontSubstitution,\n};\nconst engine$1 = layoutEngine(engines$1);\nconst getFragments$1 = (fontStore, instance) => {\n    if (!instance)\n        return [{ string: '' }];\n    const fragments = [];\n    const { fill = 'black', fontFamily = 'Helvetica', fontWeight, fontStyle, fontSize = 18, textDecorationColor, textDecorationStyle, textTransform, opacity, } = instance.props;\n    const _textDecoration = instance.props.textDecoration;\n    const fontFamilies = typeof fontFamily === 'string' ? [fontFamily] : [...(fontFamily || [])];\n    // Fallback font\n    fontFamilies.push('Helvetica');\n    const font = fontFamilies.map((fontFamilyName) => {\n        const opts = { fontFamily: fontFamilyName, fontWeight, fontStyle };\n        const obj = fontStore.getFont(opts);\n        return obj?.data;\n    });\n    const attributes = {\n        font,\n        opacity,\n        fontSize,\n        color: fill,\n        underlineStyle: textDecorationStyle,\n        underline: _textDecoration === 'underline' ||\n            _textDecoration === 'underline line-through' ||\n            _textDecoration === 'line-through underline',\n        underlineColor: textDecorationColor || fill,\n        strike: _textDecoration === 'line-through' ||\n            _textDecoration === 'underline line-through' ||\n            _textDecoration === 'line-through underline',\n        strikeStyle: textDecorationStyle,\n        strikeColor: textDecorationColor || fill,\n    };\n    for (let i = 0; i < instance.children.length; i += 1) {\n        const child = instance.children[i];\n        if (isTextInstance$4(child)) {\n            fragments.push({\n                string: transformText(child.value, textTransform),\n                attributes,\n            });\n        }\n        else if (child) {\n            fragments.push(...getFragments$1(fontStore, child));\n        }\n    }\n    return fragments;\n};\nconst getAttributedString$1 = (fontStore, instance) => fromFragments(getFragments$1(fontStore, instance));\nconst AlmostInfinity = 999999999999;\nconst shrinkWhitespaceFactor = { before: -0.5, after: -0.5 };\nconst layoutTspan = (fontStore) => (node, xOffset) => {\n    const attributedString = getAttributedString$1(fontStore, node);\n    const x = node.props.x === undefined ? xOffset : node.props.x;\n    const y = node.props?.y || 0;\n    const container = { x, y, width: AlmostInfinity, height: AlmostInfinity };\n    const hyphenationCallback = node.props.hyphenationCallback ||\n        fontStore?.getHyphenationCallback() ||\n        null;\n    const layoutOptions = { hyphenationCallback, shrinkWhitespaceFactor };\n    const lines = engine$1(attributedString, container, layoutOptions).flat();\n    return Object.assign({}, node, { lines });\n};\n// Consecutive TSpan elements should be joined with a space\nconst joinTSpanLines = (node) => {\n    const children = node.children.map((child, index) => {\n        if (!isTspan(child))\n            return child;\n        const textInstance = child.children[0];\n        if (child.props.x === undefined &&\n            index < node.children.length - 1 &&\n            textInstance?.value) {\n            return Object.assign({}, child, {\n                children: [{ ...textInstance, value: `${textInstance.value} ` }],\n            });\n        }\n        return child;\n    }, []);\n    return Object.assign({}, node, { children });\n};\nconst layoutText$1 = (fontStore, node) => {\n    if (!node.children)\n        return node;\n    let currentXOffset = node.props?.x || 0;\n    const layoutFn = layoutTspan(fontStore);\n    const joinedNode = joinTSpanLines(node);\n    const children = joinedNode.children.map((child) => {\n        const childWithLayout = layoutFn(child, currentXOffset);\n        currentXOffset += childWithLayout.lines[0].xAdvance;\n        return childWithLayout;\n    });\n    return Object.assign({}, node, { children });\n};\n\nconst isDefs = (node) => node.type === P.Defs;\nconst getDefs = (node) => {\n    const children = node.children || [];\n    const defs = children.find(isDefs);\n    const values = defs?.children || [];\n    return values.reduce((acc, value) => {\n        const id = value.props?.id;\n        if (id)\n            acc[id] = value;\n        return acc;\n    }, {});\n};\n\nconst isNotDefs = (node) => node.type !== P.Defs;\nconst detachDefs = (node) => {\n    if (!node.children)\n        return node;\n    const children = node.children.filter(isNotDefs);\n    return Object.assign({}, node, { children });\n};\nconst URL_REGEX = /url\\(['\"]?#([^'\"]+)['\"]?\\)/;\nconst replaceDef = (defs, value) => {\n    if (!value)\n        return undefined;\n    if (!URL_REGEX.test(value))\n        return value;\n    const match = value.match(URL_REGEX);\n    return defs[match[1]];\n};\nconst parseNodeDefs = (defs) => (node) => {\n    const props = node.props;\n    const fill = `fill` in props ? replaceDef(defs, props?.fill) : undefined;\n    const clipPath = `clipPath` in props\n        ? replaceDef(defs, props?.clipPath)\n        : undefined;\n    const newProps = Object.assign({}, node.props, { fill, clipPath });\n    const children = node.children\n        ? node.children.map(parseNodeDefs(defs))\n        : undefined;\n    return Object.assign({}, node, { props: newProps, children });\n};\nconst parseDefs = (root) => {\n    if (!root.children)\n        return root;\n    const defs = getDefs(root);\n    const children = root.children.map(parseNodeDefs(defs));\n    return Object.assign({}, root, { children });\n};\nconst replaceDefs = (node) => {\n    return detachDefs(parseDefs(node));\n};\n\nconst parseViewbox = (value) => {\n    if (!value)\n        return null;\n    if (typeof value !== 'string')\n        return value;\n    const values = value.split(/[,\\s]+/).map(parseFloat$1);\n    if (values.length !== 4)\n        return null;\n    return { minX: values[0], minY: values[1], maxX: values[2], maxY: values[3] };\n};\n\nconst getContainer$1 = (node) => {\n    const viewbox = parseViewbox(node.props.viewBox);\n    if (viewbox) {\n        return { width: viewbox.maxX, height: viewbox.maxY };\n    }\n    if (node.props.width && node.props.height) {\n        return {\n            width: parseFloat$1(node.props.width),\n            height: parseFloat$1(node.props.height),\n        };\n    }\n    return { width: 0, height: 0 };\n};\n\nconst BASE_SVG_INHERITED_PROPS = [\n    'x',\n    'y',\n    'clipPath',\n    'clipRule',\n    'opacity',\n    'fill',\n    'fillOpacity',\n    'fillRule',\n    'stroke',\n    'strokeLinecap',\n    'strokeLinejoin',\n    'strokeOpacity',\n    'strokeWidth',\n    'textAnchor',\n    'dominantBaseline',\n    'color',\n    'fontFamily',\n    'fontSize',\n    'fontStyle',\n    'fontWeight',\n    'letterSpacing',\n    'opacity',\n    'textDecoration',\n    'lineHeight',\n    'textAlign',\n    'visibility',\n    'wordSpacing',\n];\n// Do not inherit \"x\" for <tspan> elements from <text> parent\nconst TEXT_SVG_INHERITED_PROPS = without(['x'], BASE_SVG_INHERITED_PROPS);\nconst SVG_INHERITED_PROPS = {\n    [P.Text]: TEXT_SVG_INHERITED_PROPS,\n};\nconst getInheritProps = (node) => {\n    const props = node.props || {};\n    const svgInheritedProps = SVG_INHERITED_PROPS[node.type] ?? BASE_SVG_INHERITED_PROPS;\n    return pick(svgInheritedProps, props);\n};\nconst inheritProps = (node) => {\n    if (!node.children)\n        return node;\n    const inheritedProps = getInheritProps(node);\n    const children = node.children.map((child) => {\n        const props = Object.assign({}, inheritedProps, child.props || {});\n        const newChild = Object.assign({}, child, { props });\n        return inheritProps(newChild);\n    });\n    return Object.assign({}, node, { children });\n};\n\nconst parseAspectRatio = (value) => {\n    if (typeof value !== 'string')\n        return value;\n    const match = value\n        .replace(/[\\s\\r\\t\\n]+/gm, ' ')\n        .replace(/^defer\\s/, '')\n        .split(' ');\n    const align = (match[0] || 'xMidYMid');\n    const meetOrSlice = (match[1] ||\n        'meet');\n    return { align, meetOrSlice };\n};\n\nconst STYLE_PROPS = [\n    'width',\n    'height',\n    'color',\n    'stroke',\n    'strokeWidth',\n    'opacity',\n    'fillOpacity',\n    'strokeOpacity',\n    'fill',\n    'fillRule',\n    'clipPath',\n    'offset',\n    'transform',\n    'strokeLinejoin',\n    'strokeLinecap',\n    'strokeDasharray',\n    'gradientUnits',\n    'gradientTransform',\n];\nconst VERTICAL_PROPS = ['y', 'y1', 'y2', 'height', 'cy', 'ry'];\nconst HORIZONTAL_PROPS = ['x', 'x1', 'x2', 'width', 'cx', 'rx'];\nconst isSvg$3 = (node) => node.type === P.Svg;\nconst isText$5 = (node) => node.type === P.Text;\nconst isTextInstance$3 = (node) => node.type === P.TextInstance;\nconst transformPercent = (container) => (props) => mapValues(props, (value, key) => {\n    const match = matchPercent(value);\n    if (match && VERTICAL_PROPS.includes(key)) {\n        return match.percent * container.height;\n    }\n    if (match && HORIZONTAL_PROPS.includes(key)) {\n        return match.percent * container.width;\n    }\n    return value;\n});\nconst parsePercent = (value) => {\n    const match = matchPercent(value);\n    return match ? match.percent : parseFloat$1(value);\n};\nconst parseTransform = (container) => (value) => {\n    return resolveStyle(container, { transform: value }).transform;\n};\nconst parseProps = (container) => (node) => {\n    let props = transformPercent(container)(node.props);\n    props = evolve({\n        x: parseFloat$1,\n        x1: parseFloat$1,\n        x2: parseFloat$1,\n        y: parseFloat$1,\n        y1: parseFloat$1,\n        y2: parseFloat$1,\n        r: parseFloat$1,\n        rx: parseFloat$1,\n        ry: parseFloat$1,\n        cx: parseFloat$1,\n        cy: parseFloat$1,\n        width: parseFloat$1,\n        height: parseFloat$1,\n        offset: parsePercent,\n        fill: transformColor,\n        opacity: parsePercent,\n        stroke: transformColor,\n        stopOpacity: parsePercent,\n        stopColor: transformColor,\n        transform: parseTransform(container),\n        gradientTransform: parseTransform(container),\n    }, props);\n    return Object.assign({}, node, { props });\n};\nconst mergeStyles$1 = (node) => {\n    const style = node.style || {};\n    const props = Object.assign({}, style, node.props);\n    return Object.assign({}, node, { props });\n};\nconst removeNoneValues = (node) => {\n    const removeNone = (value) => (value === 'none' ? null : value);\n    const props = mapValues(node.props, removeNone);\n    return Object.assign({}, node, { props });\n};\nconst pickStyleProps = (node) => {\n    const props = node.props || {};\n    const styleProps = pick(STYLE_PROPS, props);\n    const style = Object.assign({}, styleProps, node.style || {});\n    return Object.assign({}, node, { style });\n};\nconst parseSvgProps = (node) => {\n    const props = evolve({\n        width: parseFloat$1,\n        height: parseFloat$1,\n        viewBox: parseViewbox,\n        preserveAspectRatio: parseAspectRatio,\n    }, node.props);\n    return Object.assign({}, node, { props });\n};\nconst wrapBetweenTspan = (node) => ({\n    type: P.Tspan,\n    props: {},\n    style: {},\n    children: [node],\n});\nconst addMissingTspan = (node) => {\n    if (!isText$5(node))\n        return node;\n    if (!node.children)\n        return node;\n    const resolveChild = (child) => isTextInstance$3(child) ? wrapBetweenTspan(child) : child;\n    const children = node.children.map(resolveChild);\n    return Object.assign({}, node, { children });\n};\nconst parseText = (fontStore) => (node) => {\n    if (isText$5(node))\n        return layoutText$1(fontStore, node);\n    if (!node.children)\n        return node;\n    const children = node.children.map(parseText(fontStore));\n    return Object.assign({}, node, { children });\n};\nconst resolveSvgNode = (container) => compose(parseProps(container), addMissingTspan, removeNoneValues, mergeStyles$1);\nconst resolveChildren = (container) => (node) => {\n    if (!node.children)\n        return node;\n    const resolveChild = compose(resolveChildren(container), resolveSvgNode(container));\n    const children = node.children.map(resolveChild);\n    return Object.assign({}, node, { children });\n};\nconst buildXLinksIndex = (node) => {\n    const idIndex = {};\n    const listToExplore = node.children?.slice(0) || [];\n    while (listToExplore.length > 0) {\n        const child = listToExplore.shift();\n        if (child.props && 'id' in child.props) {\n            idIndex[child.props.id] = child;\n        }\n        if (child.children)\n            listToExplore.push(...child.children);\n    }\n    return idIndex;\n};\nconst replaceXLinks = (node, idIndex) => {\n    if (node.props && 'xlinkHref' in node.props) {\n        const linkedNode = idIndex[node.props.xlinkHref.replace(/^#/, '')];\n        // No node to extend from\n        if (!linkedNode)\n            return node;\n        const newProps = Object.assign({}, linkedNode.props, node.props);\n        delete newProps.xlinkHref;\n        return Object.assign({}, linkedNode, { props: newProps });\n    }\n    const children = node.children?.map((child) => replaceXLinks(child, idIndex));\n    return Object.assign({}, node, { children });\n};\nconst resolveXLinks = (node) => {\n    const idIndex = buildXLinksIndex(node);\n    return replaceXLinks(node, idIndex);\n};\nconst resolveSvgRoot = (node, fontStore) => {\n    const container = getContainer$1(node);\n    return compose(replaceDefs, parseText(fontStore), parseSvgProps, pickStyleProps, inheritProps, resolveChildren(container), resolveXLinks)(node);\n};\n/**\n * Pre-process SVG nodes so they can be rendered in the next steps\n *\n * @param node - Root node\n * @param fontStore - Font store\n * @returns Root node\n */\nconst resolveSvg = (node, fontStore) => {\n    if (!('children' in node))\n        return node;\n    const resolveChild = (child) => resolveSvg(child, fontStore);\n    const root = isSvg$3(node) ? resolveSvgRoot(node, fontStore) : node;\n    const children = root.children?.map(resolveChild);\n    return Object.assign({}, root, { children });\n};\n\nlet instancePromise;\nconst loadYoga = async () => {\n    // Yoga WASM binaries must be asynchronously compiled and loaded\n    // to prevent Event emitter memory leak warnings, Yoga must be loaded only once\n    const instance = await (instancePromise ??= loadYoga$1());\n    const config = instance.Config.create();\n    config.setPointScaleFactor(0);\n    const node = { create: () => instance.Node.createWithConfig(config) };\n    return { node };\n};\n\nconst resolveYoga = async (root) => {\n    const yoga = await loadYoga();\n    return Object.assign({}, root, { yoga });\n};\n\nconst getZIndex = (node) => node.style.zIndex;\nconst shouldSort = (node) => node.type !== P.Document && node.type !== P.Svg;\nconst sortZIndex = (a, b) => {\n    const za = getZIndex(a);\n    const zb = getZIndex(b);\n    if (!za && !zb)\n        return 0;\n    if (!za)\n        return 1;\n    if (!zb)\n        return -1;\n    return zb - za;\n};\n/**\n * Sort children by zIndex value\n *\n * @param node\n * @returns Node\n */\nconst resolveNodeZIndex = (node) => {\n    if (!node.children)\n        return node;\n    const sortedChildren = shouldSort(node)\n        ? node.children.sort(sortZIndex)\n        : node.children;\n    const children = sortedChildren.map(resolveNodeZIndex);\n    return Object.assign({}, node, { children });\n};\n/**\n * Sort children by zIndex value\n *\n * @param node\n * @returns Node\n */\nconst resolveZIndex = (root) => resolveNodeZIndex(root);\n\n// Caches emoji images data\nconst emojis = {};\nconst regex = emojiRegex();\n/**\n * When an emoji as no variations, it might still have 2 parts,\n * the canonical emoji and an empty string.\n * ex.\n *   (no color) Array.from('❤️') => [\"❤\", \"️\"]\n *   (w/ color) Array.from('👍🏿') => [\"👍\", \"🏿\"]\n *\n * The empty string needs to be removed otherwise the generated\n * url will be incorect.\n */\nconst removeVariationSelectors = (x) => x !== '️';\nconst getCodePoints = (string, withVariationSelectors = false) => Array.from(string)\n    .filter(withVariationSelectors ? () => true : removeVariationSelectors)\n    .map((char) => char.codePointAt(0).toString(16))\n    .join('-');\nconst buildEmojiUrl = (emoji, source) => {\n    if ('builder' in source) {\n        return source.builder(getCodePoints(emoji, source.withVariationSelectors));\n    }\n    const { url, format = 'png', withVariationSelectors } = source;\n    return `${url}${getCodePoints(emoji, withVariationSelectors)}.${format}`;\n};\nconst fetchEmojis = (string, source) => {\n    if (!source)\n        return [];\n    const promises = [];\n    Array.from(string.matchAll(regex)).forEach((match) => {\n        const emoji = match[0];\n        if (!emojis[emoji] || emojis[emoji].loading) {\n            const emojiUrl = buildEmojiUrl(emoji, source);\n            emojis[emoji] = { loading: true };\n            promises.push(resolveImage({ uri: emojiUrl }).then((image) => {\n                emojis[emoji].loading = false;\n                emojis[emoji].data = image.data;\n            }));\n        }\n    });\n    return promises;\n};\nconst embedEmojis = (fragments) => {\n    const result = [];\n    for (let i = 0; i < fragments.length; i += 1) {\n        const fragment = fragments[i];\n        let lastIndex = 0;\n        Array.from(fragment.string.matchAll(regex)).forEach((match) => {\n            const { index } = match;\n            const emoji = match[0];\n            const emojiSize = fragment.attributes.fontSize;\n            const chunk = fragment.string.slice(lastIndex, index + match[0].length);\n            // If emoji image was found, we create a new fragment with the\n            // correct attachment and object substitution character;\n            if (emojis[emoji] && emojis[emoji].data) {\n                result.push({\n                    string: chunk.replace(match[0], String.fromCharCode(0xfffc)),\n                    attributes: {\n                        ...fragment.attributes,\n                        attachment: {\n                            width: emojiSize,\n                            height: emojiSize,\n                            yOffset: Math.floor(emojiSize * 0.1),\n                            image: emojis[emoji].data,\n                        },\n                    },\n                });\n            }\n            else {\n                // If no emoji data, we try to use emojis in the font\n                result.push({\n                    string: chunk,\n                    attributes: fragment.attributes,\n                });\n            }\n            lastIndex = index + emoji.length;\n        });\n        if (lastIndex < fragment.string.length) {\n            result.push({\n                string: fragment.string.slice(lastIndex),\n                attributes: fragment.attributes,\n            });\n        }\n    }\n    return result;\n};\n\n/**\n * Get image source\n *\n * @param node - Image node\n * @returns Image src\n */\nconst getSource = (node) => {\n    if (node.props.src)\n        return node.props.src;\n    if (node.props.source)\n        return node.props.source;\n};\n\n/**\n * Resolves `src` to `@react-pdf/image` interface.\n *\n * Also it handles factories and async sources.\n *\n * @param src\n * @returns Resolved src\n */\nconst resolveSource = async (src) => {\n    const source = typeof src === 'function' ? await src() : await src;\n    return typeof source === 'string' ? { uri: source } : source;\n};\n\n/**\n * Fetches image and append data to node\n * Ideally this fn should be immutable.\n *\n * @param node\n */\nconst fetchImage = async (node) => {\n    const src = getSource(node);\n    const { cache } = node.props;\n    if (!src) {\n        console.warn(false, 'Image should receive either a \"src\" or \"source\" prop');\n        return;\n    }\n    try {\n        const source = await resolveSource(src);\n        if (!source) {\n            throw new Error(`Image's \"src\" or \"source\" prop returned ${source}`);\n        }\n        node.image = await resolveImage(source, { cache });\n        if (Buffer.isBuffer(source) || source instanceof Blob)\n            return;\n        node.image.key = 'data' in source ? source.data.toString() : source.uri;\n    }\n    catch (e) {\n        console.warn(e.message);\n    }\n};\n\nconst isImage$2 = (node) => node.type === P.Image;\n/**\n * Get all asset promises that need to be resolved\n *\n * @param fontStore - Font store\n * @param node - Root node\n * @returns Asset promises\n */\nconst fetchAssets = (fontStore, node) => {\n    const promises = [];\n    const listToExplore = node.children?.slice(0) || [];\n    const emojiSource = fontStore ? fontStore.getEmojiSource() : null;\n    while (listToExplore.length > 0) {\n        const n = listToExplore.shift();\n        if (isImage$2(n)) {\n            promises.push(fetchImage(n));\n        }\n        if (fontStore && n.style?.fontFamily) {\n            const fontFamilies = castArray(n.style.fontFamily);\n            promises.push(...fontFamilies.map((fontFamily) => fontStore.load({\n                fontFamily,\n                fontStyle: n.style.fontStyle,\n                fontWeight: n.style.fontWeight,\n            })));\n        }\n        if (typeof n === 'string') {\n            promises.push(...fetchEmojis(n, emojiSource));\n        }\n        if ('value' in n && typeof n.value === 'string') {\n            promises.push(...fetchEmojis(n.value, emojiSource));\n        }\n        if (n.children) {\n            n.children.forEach((childNode) => {\n                listToExplore.push(childNode);\n            });\n        }\n    }\n    return promises;\n};\n/**\n * Fetch image, font and emoji assets in parallel.\n * Layout process will not be resumed until promise resolves.\n *\n * @param node root node\n * @param fontStore font store\n * @returns Root node\n */\nconst resolveAssets = async (node, fontStore) => {\n    const promises = fetchAssets(fontStore, node);\n    await Promise.all(promises);\n    return node;\n};\n\nconst isLink$1 = (node) => node.type === P.Link;\nconst DEFAULT_LINK_STYLES = {\n    color: 'blue',\n    textDecoration: 'underline',\n};\n/**\n * Computes styles using stylesheet\n *\n * @param container\n * @param node - Document node\n * @returns Computed styles\n */\nconst computeStyle = (container, node) => {\n    let baseStyle = [node.style];\n    if (isLink$1(node)) {\n        baseStyle = Array.isArray(node.style)\n            ? [DEFAULT_LINK_STYLES, ...node.style]\n            : [DEFAULT_LINK_STYLES, node.style];\n    }\n    return resolveStyle(container, baseStyle);\n};\n/**\n * Resolves node styles\n *\n * @param container\n * @returns Resolve node styles\n */\nconst resolveNodeStyles = (container) => (node) => {\n    const style = computeStyle(container, node);\n    if (!node.children)\n        return Object.assign({}, node, { style });\n    const children = node.children.map(resolveNodeStyles(container));\n    return Object.assign({}, node, { style, children });\n};\n/**\n * Resolves page styles\n *\n * @param page Document page\n * @returns Document page with resolved styles\n */\nconst resolvePageStyles = (page) => {\n    const dpi = page.props?.dpi || 72;\n    const style = page.style;\n    const width = page.box?.width || style.width;\n    const height = page.box?.height || style.height;\n    const orientation = page.props?.orientation || 'portrait';\n    const remBase = style?.fontSize || 18;\n    const container = { width, height, orientation, dpi, remBase };\n    return resolveNodeStyles(container)(page);\n};\n/**\n * Resolves document styles\n *\n * @param root - Document root\n * @returns Document root with resolved styles\n */\nconst resolveStyles = (root) => {\n    if (!root.children)\n        return root;\n    const children = root.children.map(resolvePageStyles);\n    return Object.assign({}, root, { children });\n};\n\nconst getTransformStyle = (s) => (node) => isNil(node.style?.[s]) ? '50%' : node.style?.[s] ?? null;\n/**\n * Get node origin\n *\n * @param node\n * @returns {{ left?: number, top?: number }} node origin\n */\nconst getOrigin = (node) => {\n    if (!node.box)\n        return null;\n    const { left, top, width, height } = node.box;\n    const transformOriginX = getTransformStyle('transformOriginX')(node);\n    const transformOriginY = getTransformStyle('transformOriginY')(node);\n    const percentX = matchPercent(transformOriginX);\n    const percentY = matchPercent(transformOriginY);\n    const offsetX = percentX ? width * percentX.percent : transformOriginX;\n    const offsetY = percentY ? height * percentY.percent : transformOriginY;\n    if (isNil(offsetX) || typeof offsetX === 'string')\n        throw new Error(`Invalid origin offsetX: ${offsetX}`);\n    if (isNil(offsetY) || typeof offsetY === 'string')\n        throw new Error(`Invalid origin offsetY: ${offsetY}`);\n    return { left: left + offsetX, top: top + offsetY };\n};\n\n/**\n * Resolve node origin\n *\n * @param node\n * @returns Node with origin attribute\n */\nconst resolveNodeOrigin = (node) => {\n    const origin = getOrigin(node);\n    const newNode = Object.assign({}, node, { origin });\n    if (!node.children)\n        return newNode;\n    const children = node.children.map(resolveNodeOrigin);\n    return Object.assign({}, newNode, { children });\n};\n/**\n * Resolve document origins\n *\n * @param root - Document root\n * @returns Document root\n */\nconst resolveOrigin = (root) => {\n    if (!root.children)\n        return root;\n    const children = root.children.map(resolveNodeOrigin);\n    return Object.assign({}, root, { children });\n};\n\nconst getBookmarkValue = (bookmark) => {\n    return typeof bookmark === 'string'\n        ? { title: bookmark, fit: false, expanded: false }\n        : bookmark;\n};\nconst resolveBookmarks = (node) => {\n    let refs = 0;\n    const children = (node.children || []).slice(0);\n    const listToExplore = children.map((value) => ({\n        value,\n        parent: null,\n    }));\n    while (listToExplore.length > 0) {\n        const element = listToExplore.shift();\n        if (!element)\n            break;\n        const child = element.value;\n        let parent = element.parent;\n        if (child.props && 'bookmark' in child.props) {\n            const bookmark = getBookmarkValue(child.props.bookmark);\n            const ref = refs++;\n            const newHierarchy = { ref, parent: parent?.ref, ...bookmark };\n            child.props.bookmark = newHierarchy;\n            parent = newHierarchy;\n        }\n        if (child.children) {\n            child.children.forEach((childNode) => {\n                listToExplore.push({ value: childNode, parent });\n            });\n        }\n    }\n    return node;\n};\n\nconst VALID_ORIENTATIONS = ['portrait', 'landscape'];\n/**\n * Get page orientation. Defaults to portrait\n *\n * @param page - Page object\n * @returns Page orientation\n */\nconst getOrientation = (page) => {\n    const value = page.props?.orientation || 'portrait';\n    return VALID_ORIENTATIONS.includes(value) ? value : 'portrait';\n};\n\n/**\n * Return true if page is landscape\n *\n * @param page - Page instance\n * @returns Is page landscape\n */\nconst isLandscape = (page) => getOrientation(page) === 'landscape';\n\n// Page sizes for 72dpi. 72dpi is used internally by pdfkit.\nconst PAGE_SIZES = {\n    '4A0': [4767.87, 6740.79],\n    '2A0': [3370.39, 4767.87],\n    A0: [2383.94, 3370.39],\n    A1: [1683.78, 2383.94],\n    A2: [1190.55, 1683.78],\n    A3: [841.89, 1190.55],\n    A4: [595.28, 841.89],\n    A5: [419.53, 595.28],\n    A6: [297.64, 419.53],\n    A7: [209.76, 297.64],\n    A8: [147.4, 209.76],\n    A9: [104.88, 147.4],\n    A10: [73.7, 104.88],\n    B0: [2834.65, 4008.19],\n    B1: [2004.09, 2834.65],\n    B2: [1417.32, 2004.09],\n    B3: [1000.63, 1417.32],\n    B4: [708.66, 1000.63],\n    B5: [498.9, 708.66],\n    B6: [354.33, 498.9],\n    B7: [249.45, 354.33],\n    B8: [175.75, 249.45],\n    B9: [124.72, 175.75],\n    B10: [87.87, 124.72],\n    C0: [2599.37, 3676.54],\n    C1: [1836.85, 2599.37],\n    C2: [1298.27, 1836.85],\n    C3: [918.43, 1298.27],\n    C4: [649.13, 918.43],\n    C5: [459.21, 649.13],\n    C6: [323.15, 459.21],\n    C7: [229.61, 323.15],\n    C8: [161.57, 229.61],\n    C9: [113.39, 161.57],\n    C10: [79.37, 113.39],\n    RA0: [2437.8, 3458.27],\n    RA1: [1729.13, 2437.8],\n    RA2: [1218.9, 1729.13],\n    RA3: [864.57, 1218.9],\n    RA4: [609.45, 864.57],\n    SRA0: [2551.18, 3628.35],\n    SRA1: [1814.17, 2551.18],\n    SRA2: [1275.59, 1814.17],\n    SRA3: [907.09, 1275.59],\n    SRA4: [637.8, 907.09],\n    EXECUTIVE: [521.86, 756.0],\n    FOLIO: [612.0, 936.0],\n    LEGAL: [612.0, 1008.0],\n    LETTER: [612.0, 792.0],\n    TABLOID: [792.0, 1224.0],\n    ID1: [153, 243],\n};\n/**\n * Parses scalar value in value and unit pairs\n *\n * @param value - Scalar value\n * @returns Parsed value\n */\nconst parseValue = (value) => {\n    if (typeof value === 'number')\n        return { value, unit: undefined };\n    const match = /^(-?\\d*\\.?\\d+)(in|mm|cm|pt|px)?$/g.exec(value);\n    return match\n        ? { value: parseFloat(match[1]), unit: match[2] || 'pt' }\n        : { value, unit: undefined };\n};\n/**\n * Transform given scalar value to 72dpi equivalent of size\n *\n * @param value - Styles value\n * @param inputDpi - User defined dpi\n * @returns Transformed value\n */\nconst transformUnit = (value, inputDpi) => {\n    if (!value)\n        return 0;\n    const scalar = parseValue(value);\n    const outputDpi = 72;\n    const mmFactor = (1 / 25.4) * outputDpi;\n    const cmFactor = (1 / 2.54) * outputDpi;\n    if (typeof scalar.value === 'string')\n        throw new Error(`Invalid page size: ${value}`);\n    switch (scalar.unit) {\n        case 'in':\n            return scalar.value * outputDpi;\n        case 'mm':\n            return scalar.value * mmFactor;\n        case 'cm':\n            return scalar.value * cmFactor;\n        case 'px':\n            return Math.round(scalar.value * (outputDpi / inputDpi));\n        default:\n            return scalar.value;\n    }\n};\nconst transformUnits = ({ width, height }, dpi) => ({\n    width: transformUnit(width, dpi),\n    height: transformUnit(height, dpi),\n});\n/**\n * Transforms array into size object\n *\n * @param v - Values array\n * @returns Size object with width and height\n */\nconst toSizeObject = (v) => ({\n    width: v[0],\n    height: v[1],\n});\n/**\n * Flip size object\n *\n * @param v - Size object\n * @returns Flipped size object\n */\nconst flipSizeObject = (v) => ({\n    width: v.height,\n    height: v.width,\n});\n/**\n * Returns size object from a given string\n *\n * @param v - Page size string\n * @returns Size object with width and height\n */\nconst getStringSize = (v) => {\n    return toSizeObject(PAGE_SIZES[v.toUpperCase()]);\n};\n/**\n * Returns size object from a single number\n *\n * @param n - Page size number\n * @returns Size object with width and height\n */\nconst getNumberSize = (n) => toSizeObject([n, n]);\n/**\n * Return page size in an object { width, height }\n *\n * @param page - Page node\n * @returns Size object with width and height\n */\nconst getSize = (page) => {\n    const value = page.props?.size || 'A4';\n    const dpi = page.props?.dpi || 72;\n    let size;\n    if (typeof value === 'string') {\n        size = getStringSize(value);\n    }\n    else if (Array.isArray(value)) {\n        size = transformUnits(toSizeObject(value), dpi);\n    }\n    else if (typeof value === 'number') {\n        size = transformUnits(getNumberSize(value), dpi);\n    }\n    else {\n        size = transformUnits(value, dpi);\n    }\n    return isLandscape(page) ? flipSizeObject(size) : size;\n};\n\n/**\n * Resolves page size\n *\n * @param page\n * @returns Page with resolved size in style attribute\n */\nconst resolvePageSize = (page) => {\n    const size = getSize(page);\n    const style = flatten(page.style || {});\n    return { ...page, style: { ...style, ...size } };\n};\n/**\n * Resolves page sizes\n *\n * @param root  -Document root\n * @returns Document root with resolved page sizes\n */\nconst resolvePageSizes = (root) => {\n    if (!root.children)\n        return root;\n    const children = root.children.map(resolvePageSize);\n    return Object.assign({}, root, { children });\n};\n\nconst isFixed = (node) => {\n    if (!node.props)\n        return false;\n    return 'fixed' in node.props ? node.props.fixed === true : false;\n};\n\n/**\n * Get line index at given height\n *\n * @param node\n * @param height\n */\nconst lineIndexAtHeight = (node, height) => {\n    let y = 0;\n    if (!node.lines)\n        return 0;\n    for (let i = 0; i < node.lines.length; i += 1) {\n        const line = node.lines[i];\n        if (y + line.box.height > height)\n            return i;\n        y += line.box.height;\n    }\n    return node.lines.length;\n};\n\n/**\n * Get height for given text line index\n *\n * @param node\n * @param index\n */\nconst heightAtLineIndex = (node, index) => {\n    let counter = 0;\n    if (!node.lines)\n        return counter;\n    for (let i = 0; i < index; i += 1) {\n        const line = node.lines[i];\n        if (!line)\n            break;\n        counter += line.box.height;\n    }\n    return counter;\n};\n\nconst getLineBreak = (node, height) => {\n    const top = node.box?.top || 0;\n    const widows = node.props.widows || 2;\n    const orphans = node.props.orphans || 2;\n    const linesQuantity = node.lines.length;\n    const slicedLine = lineIndexAtHeight(node, height - top);\n    if (slicedLine === 0) {\n        return 0;\n    }\n    if (linesQuantity < orphans) {\n        return linesQuantity;\n    }\n    if (slicedLine < orphans || linesQuantity < orphans + widows) {\n        return 0;\n    }\n    if (linesQuantity === orphans + widows) {\n        return orphans;\n    }\n    if (linesQuantity - slicedLine < widows) {\n        return linesQuantity - widows;\n    }\n    return slicedLine;\n};\n// Also receives contentArea in case it's needed\nconst splitText = (node, height) => {\n    const slicedLineIndex = getLineBreak(node, height);\n    const currentHeight = heightAtLineIndex(node, slicedLineIndex);\n    const nextHeight = node.box.height - currentHeight;\n    const current = Object.assign({}, node, {\n        box: {\n            ...node.box,\n            height: currentHeight,\n            borderBottomWidth: 0,\n        },\n        style: {\n            ...node.style,\n            marginBottom: 0,\n            paddingBottom: 0,\n            borderBottomWidth: 0,\n            borderBottomLeftRadius: 0,\n            borderBottomRightRadius: 0,\n        },\n        lines: node.lines.slice(0, slicedLineIndex),\n    });\n    const next = Object.assign({}, node, {\n        box: {\n            ...node.box,\n            top: 0,\n            height: nextHeight,\n            borderTopWidth: 0,\n        },\n        style: {\n            ...node.style,\n            marginTop: 0,\n            paddingTop: 0,\n            borderTopWidth: 0,\n            borderTopLeftRadius: 0,\n            borderTopRightRadius: 0,\n        },\n        lines: node.lines.slice(slicedLineIndex),\n    });\n    return [current, next];\n};\n\nconst getTop$1 = (node) => node.box?.top || 0;\nconst hasFixedHeight = (node) => !isNil(node.style?.height);\nconst splitNode = (node, height) => {\n    if (!node)\n        return [null, null];\n    const nodeTop = getTop$1(node);\n    const current = Object.assign({}, node, {\n        box: {\n            ...node.box,\n            borderBottomWidth: 0,\n        },\n        style: {\n            ...node.style,\n            marginBottom: 0,\n            paddingBottom: 0,\n            borderBottomWidth: 0,\n            borderBottomLeftRadius: 0,\n            borderBottomRightRadius: 0,\n        },\n    });\n    current.style.height = height - nodeTop;\n    const nextHeight = hasFixedHeight(node)\n        ? node.box.height - (height - nodeTop)\n        : null;\n    const next = Object.assign({}, node, {\n        box: {\n            ...node.box,\n            top: 0,\n            borderTopWidth: 0,\n        },\n        style: {\n            ...node.style,\n            marginTop: 0,\n            paddingTop: 0,\n            borderTopWidth: 0,\n            borderTopLeftRadius: 0,\n            borderTopRightRadius: 0,\n        },\n    });\n    if (nextHeight) {\n        next.style.height = nextHeight;\n    }\n    return [current, next];\n};\n\nconst NON_WRAP_TYPES = [P.Svg, P.Note, P.Image, P.Canvas];\nconst getWrap = (node) => {\n    if (NON_WRAP_TYPES.includes(node.type))\n        return false;\n    if (!node.props)\n        return true;\n    return 'wrap' in node.props ? node.props.wrap : true;\n};\n\nconst getComputedPadding = (node, edge) => {\n    const { yogaNode } = node;\n    return yogaNode ? yogaNode.getComputedPadding(edge) : null;\n};\n/**\n * Get Yoga computed paddings. Zero otherwise\n *\n * @param  node\n * @returns paddings\n */\nconst getPadding = (node) => {\n    const { style, box } = node;\n    const paddingTop = getComputedPadding(node, Yoga.Edge.Top) ||\n        box?.paddingTop ||\n        style?.paddingTop ||\n        0;\n    const paddingRight = getComputedPadding(node, Yoga.Edge.Right) ||\n        box?.paddingRight ||\n        style?.paddingRight ||\n        0;\n    const paddingBottom = getComputedPadding(node, Yoga.Edge.Bottom) ||\n        box?.paddingBottom ||\n        style?.paddingBottom ||\n        0;\n    const paddingLeft = getComputedPadding(node, Yoga.Edge.Left) ||\n        box?.paddingLeft ||\n        style?.paddingLeft ||\n        0;\n    return { paddingTop, paddingRight, paddingBottom, paddingLeft };\n};\n\nconst getWrapArea = (page) => {\n    const height = page.style?.height;\n    const { paddingBottom } = getPadding(page);\n    return height - paddingBottom;\n};\n\nconst getContentArea = (page) => {\n    const height = page.style?.height;\n    const { paddingTop, paddingBottom } = getPadding(page);\n    return height - paddingBottom - paddingTop;\n};\n\nconst isString = (value) => typeof value === 'string';\nconst isNumber = (value) => typeof value === 'number';\nconst isBoolean = (value) => typeof value === 'boolean';\nconst isFragment = (value) => value && value.type === Symbol.for('react.fragment');\n/**\n * Transforms a react element instance to internal element format.\n *\n * Can return multiple instances in the case of arrays or fragments.\n *\n * @param element - React element\n * @returns Parsed React elements\n */\nconst createInstances = (element) => {\n    if (!element)\n        return [];\n    if (Array.isArray(element)) {\n        return element.reduce((acc, el) => acc.concat(createInstances(el)), []);\n    }\n    if (isBoolean(element)) {\n        return [];\n    }\n    if (isString(element) || isNumber(element)) {\n        return [{ type: P.TextInstance, value: `${element}` }];\n    }\n    if (isFragment(element)) {\n        // @ts-expect-error figure out why this is complains\n        return createInstances(element.props.children);\n    }\n    if (!isString(element.type)) {\n        // @ts-expect-error figure out why this is complains\n        return createInstances(element.type(element.props));\n    }\n    const { type, props: { style = {}, children, ...props }, } = element;\n    const nextChildren = castArray(children).reduce((acc, child) => acc.concat(createInstances(child)), []);\n    return [\n        {\n            type,\n            style,\n            props,\n            children: nextChildren,\n        },\n    ];\n};\n\nconst getBreak = (node) => 'break' in node.props ? node.props.break : false;\nconst getMinPresenceAhead = (node) => 'minPresenceAhead' in node.props ? node.props.minPresenceAhead : 0;\nconst getFurthestEnd = (elements) => Math.max(...elements.map((node) => node.box.top + node.box.height));\nconst getEndOfMinPresenceAhead = (child) => {\n    return (child.box.top +\n        child.box.height +\n        child.box.marginBottom +\n        getMinPresenceAhead(child));\n};\nconst getEndOfPresence = (child, futureElements) => {\n    const afterMinPresenceAhead = getEndOfMinPresenceAhead(child);\n    const endOfFurthestFutureElement = getFurthestEnd(futureElements.filter((node) => !('fixed' in node.props)));\n    return Math.min(afterMinPresenceAhead, endOfFurthestFutureElement);\n};\nconst shouldBreak = (child, futureElements, height) => {\n    if ('fixed' in child.props)\n        return false;\n    const shouldSplit = height < child.box.top + child.box.height;\n    const canWrap = getWrap(child);\n    // Calculate the y coordinate where the desired presence of the child ends\n    const endOfPresence = getEndOfPresence(child, futureElements);\n    // If the child is already at the top of the page, breaking won't improve its presence\n    // (as long as react-pdf does not support breaking into differently sized containers)\n    const breakingImprovesPresence = child.box.top > child.box.marginTop;\n    return (getBreak(child) ||\n        (shouldSplit && !canWrap) ||\n        (!shouldSplit && endOfPresence > height && breakingImprovesPresence));\n};\n\nconst IGNORABLE_CODEPOINTS = [\n    8232, // LINE_SEPARATOR\n    8233, // PARAGRAPH_SEPARATOR\n];\nconst buildSubsetForFont = (font) => IGNORABLE_CODEPOINTS.reduce((acc, codePoint) => {\n    if (font &&\n        font.hasGlyphForCodePoint &&\n        font.hasGlyphForCodePoint(codePoint)) {\n        return acc;\n    }\n    return [...acc, String.fromCharCode(codePoint)];\n}, []);\nconst ignoreChars = (fragments) => fragments.map((fragment) => {\n    const charSubset = buildSubsetForFont(fragment.attributes.font[0]);\n    const subsetRegex = new RegExp(charSubset.join('|'));\n    return {\n        string: fragment.string.replace(subsetRegex, ''),\n        attributes: fragment.attributes,\n    };\n});\n\nconst PREPROCESSORS = [ignoreChars, embedEmojis];\nconst isImage$1 = (node) => node.type === P.Image;\nconst isTextInstance$2 = (node) => node.type === P.TextInstance;\n/**\n * Get textkit fragments of given node object\n *\n * @param fontStore - Font store\n * @param instance - Node\n * @param parentLink - Parent link\n * @param level - Fragment level\n * @returns Text fragments\n */\nconst getFragments = (fontStore, instance, parentLink = null, level = 0) => {\n    if (!instance)\n        return [{ string: '' }];\n    let fragments = [];\n    const { color = 'black', direction = 'ltr', fontFamily = 'Helvetica', fontWeight, fontStyle, fontSize = 18, textAlign, lineHeight, textDecoration, textDecorationColor, textDecorationStyle, textTransform, letterSpacing, textIndent, opacity, verticalAlign, } = instance.style;\n    const fontFamilies = typeof fontFamily === 'string' ? [fontFamily] : [...(fontFamily || [])];\n    // Fallback font\n    fontFamilies.push('Helvetica');\n    const font = fontFamilies.map((fontFamilyName) => {\n        const opts = { fontFamily: fontFamilyName, fontWeight, fontStyle };\n        const obj = fontStore.getFont(opts);\n        return obj?.data;\n    });\n    // Don't pass main background color to textkit. Will be rendered by the render package instead\n    const backgroundColor = level === 0 ? null : instance.style.backgroundColor;\n    const attributes = {\n        font,\n        color,\n        opacity,\n        fontSize,\n        lineHeight,\n        direction,\n        verticalAlign,\n        backgroundColor,\n        indent: textIndent,\n        characterSpacing: letterSpacing,\n        strikeStyle: textDecorationStyle,\n        underlineStyle: textDecorationStyle,\n        underline: textDecoration === 'underline' ||\n            textDecoration === 'underline line-through' ||\n            textDecoration === 'line-through underline',\n        strike: textDecoration === 'line-through' ||\n            textDecoration === 'underline line-through' ||\n            textDecoration === 'line-through underline',\n        strikeColor: textDecorationColor || color,\n        underlineColor: textDecorationColor || color,\n        // @ts-expect-error allow this props access\n        link: parentLink || instance.props?.src || instance.props?.href,\n        align: textAlign || (direction === 'rtl' ? 'right' : 'left'),\n    };\n    for (let i = 0; i < instance.children.length; i += 1) {\n        const child = instance.children[i];\n        if (isImage$1(child)) {\n            fragments.push({\n                string: String.fromCharCode(0xfffc),\n                attributes: {\n                    ...attributes,\n                    attachment: {\n                        width: (child.style.width || fontSize),\n                        height: (child.style.height || fontSize),\n                        image: child.image.data,\n                    },\n                },\n            });\n        }\n        else if (isTextInstance$2(child)) {\n            fragments.push({\n                string: transformText(child.value, textTransform),\n                attributes,\n            });\n        }\n        else if (child) {\n            fragments.push(...getFragments(fontStore, child, attributes.link, level + 1));\n        }\n    }\n    for (let i = 0; i < PREPROCESSORS.length; i += 1) {\n        const preprocessor = PREPROCESSORS[i];\n        fragments = preprocessor(fragments);\n    }\n    return fragments;\n};\n/**\n * Get textkit attributed string from text node\n *\n * @param fontStore - Font store\n * @param instance Node\n * @returns Attributed string\n */\nconst getAttributedString = (fontStore, instance) => {\n    const fragments = getFragments(fontStore, instance);\n    return fromFragments(fragments);\n};\n\nconst engines = {\n    bidi,\n    linebreaker,\n    justification,\n    textDecoration,\n    scriptItemizer,\n    wordHyphenation,\n    fontSubstitution,\n};\nconst engine = layoutEngine(engines);\nconst getMaxLines = (node) => node.style?.maxLines;\nconst getTextOverflow = (node) => node.style?.textOverflow;\n/**\n * Get layout container for specific text node\n *\n * @param {number} width\n * @param {number} height\n * @param {Object} node\n * @returns {Object} layout container\n */\nconst getContainer = (width, height, node) => {\n    const maxLines = getMaxLines(node);\n    const textOverflow = getTextOverflow(node);\n    return {\n        x: 0,\n        y: 0,\n        width,\n        maxLines,\n        height: height || Infinity,\n        truncateMode: textOverflow,\n    };\n};\n/**\n * Get text layout options for specific text node\n *\n * @param {Object} node instance\n * @returns {Object} layout options\n */\nconst getLayoutOptions = (fontStore, node) => ({\n    hyphenationPenalty: node.props.hyphenationPenalty,\n    shrinkWhitespaceFactor: { before: -0.5, after: -0.5 },\n    hyphenationCallback: node.props.hyphenationCallback ||\n        fontStore?.getHyphenationCallback() ||\n        null,\n});\n/**\n * Get text lines for given node\n *\n * @param node - Node\n * @param width - Container width\n * @param height - Container height\n * @param fontStore - Font store\n * @returns Layout lines\n */\nconst layoutText = (node, width, height, fontStore) => {\n    const attributedString = getAttributedString(fontStore, node);\n    const container = getContainer(width, height, node);\n    const options = getLayoutOptions(fontStore, node);\n    const lines = engine(attributedString, container, options);\n    return lines.reduce((acc, line) => [...acc, ...line], []);\n};\n\nconst isSvg$2 = (node) => node.type === P.Svg;\nconst isText$4 = (node) => node.type === P.Text;\nconst shouldIterate = (node) => !isSvg$2(node) && !isText$4(node);\nconst shouldLayoutText = (node) => isText$4(node) && !node.lines;\n/**\n * Performs text layout on text node if wasn't calculated before.\n * Text layout is usually performed on Yoga's layout process (via setMeasureFunc),\n * but we need to layout those nodes with fixed width and height.\n *\n * @param node\n * @returns Layout node\n */\nconst resolveTextLayout = (node, fontStore) => {\n    if (shouldLayoutText(node)) {\n        const width = node.box.width - (node.box.paddingRight + node.box.paddingLeft);\n        const height = node.box.height - (node.box.paddingTop + node.box.paddingBottom);\n        node.lines = layoutText(node, width, height, fontStore);\n    }\n    if (shouldIterate(node)) {\n        if (!node.children)\n            return node;\n        const mapChild = (child) => resolveTextLayout(child, fontStore);\n        const children = node.children.map(mapChild);\n        return Object.assign({}, node, { children });\n    }\n    return node;\n};\n\nconst BASE_INHERITABLE_PROPERTIES = [\n    'color',\n    'fontFamily',\n    'fontSize',\n    'fontStyle',\n    'fontWeight',\n    'letterSpacing',\n    'opacity',\n    'textDecoration',\n    'textTransform',\n    'lineHeight',\n    'textAlign',\n    'visibility',\n    'wordSpacing',\n];\nconst TEXT_INHERITABLE_PROPERTIES = [\n    ...BASE_INHERITABLE_PROPERTIES,\n    'backgroundColor',\n];\nconst isType$2 = (type) => (node) => node.type === type;\nconst isSvg$1 = isType$2(P.Svg);\nconst isText$3 = isType$2(P.Text);\n// Merge style values\nconst mergeValues = (styleName, value, inheritedValue) => {\n    switch (styleName) {\n        case 'textDecoration': {\n            // merge not none and not false textDecoration values to one rule\n            return [inheritedValue, value].filter((v) => v && v !== 'none').join(' ');\n        }\n        default:\n            return value;\n    }\n};\n// Merge inherited and node styles\nconst merge = (inheritedStyles, style) => {\n    const mergedStyles = { ...inheritedStyles };\n    Object.entries(style).forEach(([styleName, value]) => {\n        mergedStyles[styleName] = mergeValues(styleName, value, inheritedStyles[styleName]);\n    });\n    return mergedStyles;\n};\n/**\n * Merges styles with node\n *\n * @param inheritedStyles - Style object\n * @returns Merge styles function\n */\nconst mergeStyles = (inheritedStyles) => (node) => {\n    const style = merge(inheritedStyles, node.style || {});\n    return Object.assign({}, node, { style });\n};\n/**\n * Inherit style values from the root to the leafs\n *\n * @param node - Document root\n * @returns Document root with inheritance\n *\n */\nconst resolveInheritance = (node) => {\n    if (isSvg$1(node))\n        return node;\n    if (!('children' in node))\n        return node;\n    const inheritableProperties = isText$3(node)\n        ? TEXT_INHERITABLE_PROPERTIES\n        : BASE_INHERITABLE_PROPERTIES;\n    const inheritStyles = pick(inheritableProperties, node.style || {});\n    const resolveChild = compose(resolveInheritance, mergeStyles(inheritStyles));\n    const children = node.children.map(resolveChild);\n    return Object.assign({}, node, { children });\n};\n\nconst getComputedMargin = (node, edge) => {\n    const { yogaNode } = node;\n    return yogaNode ? yogaNode.getComputedMargin(edge) : null;\n};\n/**\n * Get Yoga computed magins. Zero otherwise\n *\n * @param node\n * @returns Margins\n */\nconst getMargin = (node) => {\n    const { style, box } = node;\n    const marginTop = getComputedMargin(node, Yoga.Edge.Top) ||\n        box?.marginTop ||\n        style?.marginTop ||\n        0;\n    const marginRight = getComputedMargin(node, Yoga.Edge.Right) ||\n        box?.marginRight ||\n        style?.marginRight ||\n        0;\n    const marginBottom = getComputedMargin(node, Yoga.Edge.Bottom) ||\n        box?.marginBottom ||\n        style?.marginBottom ||\n        0;\n    const marginLeft = getComputedMargin(node, Yoga.Edge.Left) ||\n        box?.marginLeft ||\n        style?.marginLeft ||\n        0;\n    return { marginTop, marginRight, marginBottom, marginLeft };\n};\n\n/**\n * Get Yoga computed position. Zero otherwise\n *\n * @param node\n * @returns Position\n */\nconst getPosition = (node) => {\n    const { yogaNode } = node;\n    return {\n        top: yogaNode?.getComputedTop() || 0,\n        right: yogaNode?.getComputedRight() || 0,\n        bottom: yogaNode?.getComputedBottom() || 0,\n        left: yogaNode?.getComputedLeft() || 0,\n    };\n};\n\nconst DEFAULT_DIMENSION = {\n    width: 0,\n    height: 0,\n};\n/**\n * Get Yoga computed dimensions. Zero otherwise\n *\n * @param node\n * @returns Dimensions\n */\nconst getDimension = (node) => {\n    const { yogaNode } = node;\n    if (!yogaNode)\n        return DEFAULT_DIMENSION;\n    return {\n        width: yogaNode.getComputedWidth(),\n        height: yogaNode.getComputedHeight(),\n    };\n};\n\nconst getComputedBorder = (yogaNode, edge) => (yogaNode ? yogaNode.getComputedBorder(edge) : 0);\n/**\n * Get Yoga computed border width. Zero otherwise\n *\n * @param node\n * @returns Border widths\n */\nconst getBorderWidth = (node) => {\n    const { yogaNode } = node;\n    return {\n        borderTopWidth: getComputedBorder(yogaNode, Yoga.Edge.Top),\n        borderRightWidth: getComputedBorder(yogaNode, Yoga.Edge.Right),\n        borderBottomWidth: getComputedBorder(yogaNode, Yoga.Edge.Bottom),\n        borderLeftWidth: getComputedBorder(yogaNode, Yoga.Edge.Left),\n    };\n};\n\n/**\n * Set display attribute to node's Yoga instance\n *\n * @param value - Display\n * @returns Node instance wrapper\n */\nconst setDisplay = (value) => (node) => {\n    const { yogaNode } = node;\n    if (yogaNode) {\n        yogaNode.setDisplay(value === 'none' ? Yoga.Display.None : Yoga.Display.Flex);\n    }\n    return node;\n};\n\nconst OVERFLOW = {\n    hidden: Yoga.Overflow.Hidden,\n    scroll: Yoga.Overflow.Scroll,\n};\n/**\n * Set overflow attribute to node's Yoga instance\n *\n * @param value - Overflow value\n * @returns Node instance wrapper\n */\nconst setOverflow = (value) => (node) => {\n    const { yogaNode } = node;\n    if (!isNil(value) && yogaNode) {\n        const overflow = OVERFLOW[value] || Yoga.Overflow.Visible;\n        yogaNode.setOverflow(overflow);\n    }\n    return node;\n};\n\nconst FLEX_WRAP = {\n    wrap: Yoga.Wrap.Wrap,\n    'wrap-reverse': Yoga.Wrap.WrapReverse,\n};\n/**\n * Set flex wrap attribute to node's Yoga instance\n *\n * @param value - Flex wrap value\n * @returns Node instance wrapper\n */\nconst setFlexWrap = (value) => (node) => {\n    const { yogaNode } = node;\n    if (yogaNode) {\n        const flexWrap = FLEX_WRAP[value] || Yoga.Wrap.NoWrap;\n        yogaNode.setFlexWrap(flexWrap);\n    }\n    return node;\n};\n\n/**\n * Set generic yoga attribute to node's Yoga instance, handing `auto`, edges and percentage cases\n *\n * @param attr - Property\n * @param edge - Edge\n * @returns Node instance wrapper\n */\nconst setYogaValue = (attr, edge) => (value) => (node) => {\n    const { yogaNode } = node;\n    if (!isNil(value) && yogaNode) {\n        const hasEdge = !isNil(edge);\n        const fixedMethod = `set${upperFirst(attr)}`;\n        const autoMethod = `${fixedMethod}Auto`;\n        const percentMethod = `${fixedMethod}Percent`;\n        const percent = matchPercent(value);\n        if (percent && !yogaNode[percentMethod]) {\n            throw new Error(`You can't pass percentage values to ${attr} property`);\n        }\n        if (percent) {\n            if (hasEdge) {\n                yogaNode[percentMethod]?.(edge, percent.value);\n            }\n            else {\n                yogaNode[percentMethod]?.(percent.value);\n            }\n        }\n        else if (value === 'auto') {\n            if (hasEdge) {\n                yogaNode[autoMethod]?.(edge);\n            }\n            else {\n                yogaNode[autoMethod]?.();\n            }\n        }\n        else if (hasEdge) {\n            yogaNode[fixedMethod]?.(edge, value);\n        }\n        else {\n            yogaNode[fixedMethod]?.(value);\n        }\n    }\n    return node;\n};\n\n/**\n * Set flex grow attribute to node's Yoga instance\n *\n * @param  value - Flex grow value\n * @returns Node instance wrapper\n */\nconst setFlexGrow = (value) => (node) => {\n    return setYogaValue('flexGrow')(value || 0)(node);\n};\n\n/**\n * Set flex basis attribute to node's Yoga instance\n *\n * @param flex - Basis value\n * @param node - Node instance\n * @returns Node instance\n */\nconst setFlexBasis = setYogaValue('flexBasis');\n\nconst ALIGN = {\n    'flex-start': Yoga.Align.FlexStart,\n    center: Yoga.Align.Center,\n    'flex-end': Yoga.Align.FlexEnd,\n    stretch: Yoga.Align.Stretch,\n    baseline: Yoga.Align.Baseline,\n    'space-between': Yoga.Align.SpaceBetween,\n    'space-around': Yoga.Align.SpaceAround,\n    'space-evenly': Yoga.Align.SpaceEvenly,\n};\n/**\n * Set generic align attribute to node's Yoga instance\n *\n * @param attr - Specific align property\n * @param value - Specific align value\n * @param node - Node\n * @returns Node\n */\nconst setAlign = (attr) => (value) => (node) => {\n    const { yogaNode } = node;\n    const defaultValue = attr === 'items' ? Yoga.Align.Stretch : Yoga.Align.Auto;\n    if (yogaNode) {\n        const align = ALIGN[value] || defaultValue;\n        yogaNode[`setAlign${upperFirst(attr)}`](align);\n    }\n    return node;\n};\n\n/**\n * Set align self attribute to node's Yoga instance\n *\n * @param align - Value\n * @param node - Node instance\n * @returns Node instance\n */\nconst setAlignSelf = setAlign('self');\n\n/**\n * Set align items attribute to node's Yoga instance\n *\n * @param align - Value\n * @param node - Node instance\n * @returns Node instance\n */\nconst setAlignItems = setAlign('items');\n\n/**\n * Set flex shrink attribute to node's Yoga instance\n *\n * @param value - Flex shrink value\n * @returns Node instance wrapper\n */\nconst setFlexShrink = (value) => (node) => {\n    return setYogaValue('flexShrink')(value || 1)(node);\n};\n\n/**\n * Set aspect ratio attribute to node's Yoga instance\n *\n * @param value - Ratio\n * @returns Node instance\n */\nconst setAspectRatio = (value) => (node) => {\n    const { yogaNode } = node;\n    if (!isNil(value) && yogaNode) {\n        yogaNode.setAspectRatio(value);\n    }\n    return node;\n};\n\n/**\n * Set align content attribute to node's Yoga instance\n *\n * @param align - Value\n * @param node - Instance\n * @returns Node instance\n */\nconst setAlignContent = setAlign('content');\n\nconst POSITION = {\n    absolute: Yoga.PositionType.Absolute,\n    relative: Yoga.PositionType.Relative,\n    static: Yoga.PositionType.Static,\n};\n/**\n * Set position type attribute to node's Yoga instance\n *\n * @param value - Position position type\n * @returns Node instance\n */\nconst setPositionType = (value) => (node) => {\n    const { yogaNode } = node;\n    if (!isNil(value) && yogaNode) {\n        yogaNode.setPositionType(POSITION[value]);\n    }\n    return node;\n};\n\nconst FLEX_DIRECTIONS = {\n    row: Yoga.FlexDirection.Row,\n    'row-reverse': Yoga.FlexDirection.RowReverse,\n    'column-reverse': Yoga.FlexDirection.ColumnReverse,\n};\n/**\n * Set flex direction attribute to node's Yoga instance\n *\n * @param value - Flex direction value\n * @returns Node instance wrapper\n */\nconst setFlexDirection = (value) => (node) => {\n    const { yogaNode } = node;\n    if (yogaNode) {\n        const flexDirection = FLEX_DIRECTIONS[value] || Yoga.FlexDirection.Column;\n        yogaNode.setFlexDirection(flexDirection);\n    }\n    return node;\n};\n\nconst JUSTIFY_CONTENT = {\n    center: Yoga.Justify.Center,\n    'flex-end': Yoga.Justify.FlexEnd,\n    'space-between': Yoga.Justify.SpaceBetween,\n    'space-around': Yoga.Justify.SpaceAround,\n    'space-evenly': Yoga.Justify.SpaceEvenly,\n};\n/**\n * Set justify content attribute to node's Yoga instance\n *\n * @param value - Justify content value\n * @returns Node instance wrapper\n */\nconst setJustifyContent = (value) => (node) => {\n    const { yogaNode } = node;\n    if (!isNil(value) && yogaNode) {\n        const justifyContent = JUSTIFY_CONTENT[value] || Yoga.Justify.FlexStart;\n        yogaNode.setJustifyContent(justifyContent);\n    }\n    return node;\n};\n\n/**\n * Set margin top attribute to node's Yoga instance\n *\n * @param margin - Margin top\n * @param node - Node instance\n * @returns Node instance\n */\nconst setMarginTop = setYogaValue('margin', Yoga.Edge.Top);\n/**\n * Set margin right attribute to node's Yoga instance\n *\n * @param margin - Margin right\n * @param node - Node instance\n * @returns Node instance\n */\nconst setMarginRight = setYogaValue('margin', Yoga.Edge.Right);\n/**\n * Set margin bottom attribute to node's Yoga instance\n *\n * @param margin - Margin bottom\n * @param node - Node instance\n * @returns Node instance\n */\nconst setMarginBottom = setYogaValue('margin', Yoga.Edge.Bottom);\n/**\n * Set margin left attribute to node's Yoga instance\n *\n * @param margin - Margin left\n * @param node - Node instance\n * @returns Node instance\n */\nconst setMarginLeft = setYogaValue('margin', Yoga.Edge.Left);\n\n/**\n * Set padding top attribute to node's Yoga instance\n *\n * @param padding - Padding top\n * @param node - Node instance\n * @returns Node instance\n */\nconst setPaddingTop = setYogaValue('padding', Yoga.Edge.Top);\n/**\n * Set padding right attribute to node's Yoga instance\n *\n * @param padding - Padding right\n * @param node - Node instance\n * @returns Node instance\n */\nconst setPaddingRight = setYogaValue('padding', Yoga.Edge.Right);\n/**\n * Set padding bottom attribute to node's Yoga instance\n *\n * @param padding - Padding bottom\n * @param node Node instance\n * @returns Node instance\n */\nconst setPaddingBottom = setYogaValue('padding', Yoga.Edge.Bottom);\n/**\n * Set padding left attribute to node's Yoga instance\n *\n * @param padding - Padding left\n * @param node - Node instance\n * @returns Node instance\n */\nconst setPaddingLeft = setYogaValue('padding', Yoga.Edge.Left);\n\n/**\n * Set border top attribute to node's Yoga instance\n *\n * @param border - Border top width\n * @param node - Node instance\n * @returns Node instance\n */\nconst setBorderTop = setYogaValue('border', Yoga.Edge.Top);\n/**\n * Set border right attribute to node's Yoga instance\n *\n * @param border - Border right width\n * @param node - Node instance\n * @returns Node instance\n */\nconst setBorderRight = setYogaValue('border', Yoga.Edge.Right);\n/**\n * Set border bottom attribute to node's Yoga instance\n *\n * @param border - Border bottom width\n * @param node - Node instance\n * @returns Node instance\n */\nconst setBorderBottom = setYogaValue('border', Yoga.Edge.Bottom);\n/**\n * Set border left attribute to node's Yoga instance\n *\n * @param border - Border left width\n * @param node - Node instance\n * @returns Node instance\n */\nconst setBorderLeft = setYogaValue('border', Yoga.Edge.Left);\n\n/**\n * Set position top attribute to node's Yoga instance\n *\n * @param position - Position top\n * @param node - Node instance\n * @returns Node instance\n */\nconst setPositionTop = setYogaValue('position', Yoga.Edge.Top);\n/**\n * Set position right attribute to node's Yoga instance\n *\n * @param position - Position right\n * @param node - Node instance\n * @returns Node instance\n */\nconst setPositionRight = setYogaValue('position', Yoga.Edge.Right);\n/**\n * Set position bottom attribute to node's Yoga instance\n *\n * @param position - Position bottom\n * @param node - Node instance\n * @returns Node instance\n */\nconst setPositionBottom = setYogaValue('position', Yoga.Edge.Bottom);\n/**\n * Set position left attribute to node's Yoga instance\n *\n * @param position - Position left\n * @param node - Node instance\n * @returns Node instance\n */\nconst setPositionLeft = setYogaValue('position', Yoga.Edge.Left);\n\n/**\n * Set width to node's Yoga instance\n *\n * @param width - Width\n * @param node - Node instance\n * @returns Node instance\n */\nconst setWidth = setYogaValue('width');\n/**\n * Set min width to node's Yoga instance\n *\n * @param min - Width\n * @param node - Node instance\n * @returns Node instance\n */\nconst setMinWidth = setYogaValue('minWidth');\n/**\n * Set max width to node's Yoga instance\n *\n * @param max - Width\n * @param node - Node instance\n * @returns Node instance\n */\nconst setMaxWidth = setYogaValue('maxWidth');\n/**\n * Set height to node's Yoga instance\n *\n * @param height - Height\n * @param node - Node instance\n * @returns Node instance\n */\nconst setHeight = setYogaValue('height');\n/**\n * Set min height to node's Yoga instance\n *\n * @param min - Height\n * @param node - Node instance\n * @returns Node instance\n */\nconst setMinHeight = setYogaValue('minHeight');\n/**\n * Set max height to node's Yoga instance\n *\n * @param max - Height\n * @param node - Node instance\n * @returns Node instance\n */\nconst setMaxHeight = setYogaValue('maxHeight');\n\n/**\n * Set rowGap value to node's Yoga instance\n *\n * @param value - Gap value\n * @returns Node instance wrapper\n */\nconst setRowGap = setYogaValue('gap', Yoga.Gutter.Row);\n/**\n * Set columnGap value to node's Yoga instance\n *\n * @param value - Gap value\n * @returns Node instance wrapper\n */\nconst setColumnGap = setYogaValue('gap', Yoga.Gutter.Column);\n\nconst getAspectRatio = (viewbox) => {\n    if (!viewbox)\n        return null;\n    if (typeof viewbox === 'string')\n        return null;\n    return (viewbox.maxX - viewbox.minX) / (viewbox.maxY - viewbox.minY);\n};\n/**\n * Yoga svg measure function\n *\n * @param page\n * @param node\n * @returns Measure svg\n */\nconst measureCanvas$1 = (page, node) => (width, widthMode, height, heightMode) => {\n    const aspectRatio = getAspectRatio(node.props.viewBox) || 1;\n    if (widthMode === Yoga.MeasureMode.Exactly ||\n        widthMode === Yoga.MeasureMode.AtMost) {\n        return { width, height: width / aspectRatio };\n    }\n    if (heightMode === Yoga.MeasureMode.Exactly) {\n        return { width: height * aspectRatio };\n    }\n    return {};\n};\n\n/**\n * Get lines width (if any)\n *\n * @param node\n * @returns Lines width\n */\nconst linesWidth = (node) => {\n    if (!node.lines)\n        return 0;\n    return Math.max(0, ...node.lines.map((line) => line.xAdvance));\n};\n\n/**\n * Get lines height (if any)\n *\n * @param node\n * @returns Lines height\n */\nconst linesHeight = (node) => {\n    if (!node.lines)\n        return -1;\n    return node.lines.reduce((acc, line) => acc + line.box.height, 0);\n};\n\nconst ALIGNMENT_FACTORS = { center: 0.5, right: 1 };\n/**\n * Yoga text measure function\n *\n * @param page\n * @param node\n * @param fontStore\n * @returns {MeasureText} measure text function\n */\nconst measureText = (page, node, fontStore) => (width, widthMode, height) => {\n    if (widthMode === Yoga.MeasureMode.Exactly) {\n        if (!node.lines)\n            node.lines = layoutText(node, width, height, fontStore);\n        return { height: linesHeight(node) };\n    }\n    if (widthMode === Yoga.MeasureMode.AtMost) {\n        const alignFactor = ALIGNMENT_FACTORS[node.style?.textAlign] || 0;\n        if (!node.lines) {\n            node.lines = layoutText(node, width, height, fontStore);\n            node.alignOffset = (width - linesWidth(node)) * alignFactor; // Compensate align in variable width containers\n        }\n        return {\n            height: linesHeight(node),\n            width: Math.min(width, linesWidth(node)),\n        };\n    }\n    return {};\n};\n\n/**\n * Get image ratio\n *\n * @param node - Image node\n * @returns Image ratio\n */\nconst getRatio = (node) => {\n    return node.image?.data ? node.image.width / node.image.height : 1;\n};\n\n/**\n * Checks if page has auto height\n *\n * @param page\n * @returns Is page height auto\n */\nconst isHeightAuto = (page) => isNil(page.box?.height);\n\nconst SAFETY_HEIGHT$1 = 10;\n/**\n * Yoga image measure function\n *\n * @param page - Page\n * @param node - Node\n * @returns Measure image\n */\nconst measureImage = (page, node) => (width, widthMode, height, heightMode) => {\n    const imageRatio = getRatio(node);\n    const imageMargin = getMargin(node);\n    const pagePadding = getPadding(page);\n    // TODO: Check image percentage margins\n    const pageArea = isHeightAuto(page)\n        ? Infinity\n        : (page.box?.height || 0) -\n            pagePadding.paddingTop -\n            pagePadding.paddingBottom -\n            imageMargin.marginTop -\n            imageMargin.marginBottom -\n            SAFETY_HEIGHT$1;\n    // Skip measure if image data not present yet\n    if (!node.image)\n        return { width: 0, height: 0 };\n    if (widthMode === Yoga.MeasureMode.Exactly &&\n        heightMode === Yoga.MeasureMode.Undefined) {\n        const scaledHeight = width / imageRatio;\n        return { height: Math.min(pageArea, scaledHeight) };\n    }\n    if (heightMode === Yoga.MeasureMode.Exactly &&\n        (widthMode === Yoga.MeasureMode.AtMost ||\n            widthMode === Yoga.MeasureMode.Undefined)) {\n        return { width: Math.min(height * imageRatio, width) };\n    }\n    if (widthMode === Yoga.MeasureMode.Exactly &&\n        heightMode === Yoga.MeasureMode.AtMost) {\n        const scaledHeight = width / imageRatio;\n        return { height: Math.min(height, pageArea, scaledHeight) };\n    }\n    if (widthMode === Yoga.MeasureMode.AtMost &&\n        heightMode === Yoga.MeasureMode.AtMost) {\n        if (imageRatio > 1) {\n            return {\n                width,\n                height: Math.min(width / imageRatio, height),\n            };\n        }\n        return {\n            height,\n            width: Math.min(height * imageRatio, width),\n        };\n    }\n    return { height, width };\n};\n\nconst SAFETY_HEIGHT = 10;\nconst getMax = (values) => Math.max(-Infinity, ...values);\n/**\n * Helper object to predict canvas size\n * TODO: Implement remaining functions (as close as possible);\n */\nconst measureCtx = () => {\n    const ctx = {};\n    const points = [];\n    const nil = () => ctx;\n    const addPoint = (x, y) => points.push([x, y]);\n    const moveTo = (x, y) => {\n        addPoint(x, y);\n        return ctx;\n    };\n    const rect = (x, y, w, h) => {\n        addPoint(x, y);\n        addPoint(x + w, y);\n        addPoint(x, y + h);\n        addPoint(x + w, y + h);\n        return ctx;\n    };\n    const ellipse = (x, y, rx, ry) => {\n        ry = ry || rx;\n        addPoint(x - rx, y - ry);\n        addPoint(x + rx, y - ry);\n        addPoint(x + rx, y + ry);\n        addPoint(x - rx, y + ry);\n        return ctx;\n    };\n    const polygon = (...pts) => {\n        points.push(...pts);\n        return ctx;\n    };\n    // Change dimensions\n    ctx.rect = rect;\n    ctx.moveTo = moveTo;\n    ctx.lineTo = moveTo;\n    ctx.circle = ellipse;\n    ctx.polygon = polygon;\n    ctx.ellipse = ellipse;\n    ctx.roundedRect = rect;\n    // To be implemented\n    ctx.text = nil;\n    ctx.path = nil;\n    ctx.lineWidth = nil;\n    ctx.bezierCurveTo = nil;\n    ctx.quadraticCurveTo = nil;\n    ctx.scale = nil;\n    ctx.rotate = nil;\n    ctx.translate = nil;\n    // These don't change dimensions\n    ctx.dash = nil;\n    ctx.clip = nil;\n    ctx.save = nil;\n    ctx.fill = nil;\n    ctx.font = nil;\n    ctx.stroke = nil;\n    ctx.lineCap = nil;\n    ctx.opacity = nil;\n    ctx.restore = nil;\n    ctx.lineJoin = nil;\n    ctx.fontSize = nil;\n    ctx.fillColor = nil;\n    ctx.miterLimit = nil;\n    ctx.strokeColor = nil;\n    ctx.fillOpacity = nil;\n    ctx.strokeOpacity = nil;\n    ctx.linearGradient = nil;\n    ctx.radialGradient = nil;\n    ctx.getWidth = () => getMax(points.map((p) => p[0]));\n    ctx.getHeight = () => getMax(points.map((p) => p[1]));\n    return ctx;\n};\n/**\n * @typedef {Function} MeasureCanvas\n * @returns {{ width: number, height: number }} canvas width and height\n */\n/**\n * Yoga canvas measure function\n *\n * @param {Object} page\n * @param {Object} node\n * @returns {MeasureCanvas} measure canvas\n */\nconst measureCanvas = (page, node) => () => {\n    const imageMargin = getMargin(node);\n    const pagePadding = getPadding(page);\n    // TODO: Check image percentage margins\n    const pageArea = isHeightAuto(page)\n        ? Infinity\n        : (page.box?.height || 0) -\n            pagePadding.paddingTop -\n            pagePadding.paddingBottom -\n            imageMargin.marginTop -\n            imageMargin.marginBottom -\n            SAFETY_HEIGHT;\n    const ctx = measureCtx();\n    node.props.paint(ctx);\n    const width = ctx.getWidth();\n    const height = Math.min(pageArea, ctx.getHeight());\n    return { width, height };\n};\n\nconst isType$1 = (type) => (node) => node.type === type;\nconst isSvg = isType$1(P.Svg);\nconst isText$2 = isType$1(P.Text);\nconst isNote = isType$1(P.Note);\nconst isPage = isType$1(P.Page);\nconst isImage = isType$1(P.Image);\nconst isCanvas = isType$1(P.Canvas);\nconst isTextInstance$1 = isType$1(P.TextInstance);\nconst setNodeHeight = (node) => {\n    const value = isPage(node) ? node.box?.height : node.style?.height;\n    return setHeight(value);\n};\n/**\n * Set styles valeus into yoga node before layout calculation\n *\n * @param node\n */\nconst setYogaValues = (node) => {\n    compose(setNodeHeight(node), setWidth(node.style.width), setMinWidth(node.style.minWidth), setMaxWidth(node.style.maxWidth), setMinHeight(node.style.minHeight), setMaxHeight(node.style.maxHeight), setMarginTop(node.style.marginTop), setMarginRight(node.style.marginRight), setMarginBottom(node.style.marginBottom), setMarginLeft(node.style.marginLeft), setPaddingTop(node.style.paddingTop), setPaddingRight(node.style.paddingRight), setPaddingBottom(node.style.paddingBottom), setPaddingLeft(node.style.paddingLeft), setPositionType(node.style.position), setPositionTop(node.style.top), setPositionRight(node.style.right), setPositionBottom(node.style.bottom), setPositionLeft(node.style.left), setBorderTop(node.style.borderTopWidth), setBorderRight(node.style.borderRightWidth), setBorderBottom(node.style.borderBottomWidth), setBorderLeft(node.style.borderLeftWidth), setDisplay(node.style.display), setFlexDirection(node.style.flexDirection), setAlignSelf(node.style.alignSelf), setAlignContent(node.style.alignContent), setAlignItems(node.style.alignItems), setJustifyContent(node.style.justifyContent), setFlexWrap(node.style.flexWrap), setOverflow(node.style.overflow), setAspectRatio(node.style.aspectRatio), setFlexBasis(node.style.flexBasis), setFlexGrow(node.style.flexGrow), setFlexShrink(node.style.flexShrink), setRowGap(node.style.rowGap), setColumnGap(node.style.columnGap))(node);\n};\n/**\n * Inserts child into parent' yoga node\n *\n * @param parent parent\n * @returns Insert yoga nodes\n */\nconst insertYogaNodes = (parent) => (child) => {\n    parent.insertChild(child.yogaNode, parent.getChildCount());\n    return child;\n};\nconst setMeasureFunc = (node, page, fontStore) => {\n    const { yogaNode } = node;\n    if (isText$2(node)) {\n        yogaNode.setMeasureFunc(measureText(page, node, fontStore));\n    }\n    if (isImage(node)) {\n        yogaNode.setMeasureFunc(measureImage(page, node));\n    }\n    if (isCanvas(node)) {\n        yogaNode.setMeasureFunc(measureCanvas(page, node));\n    }\n    if (isSvg(node)) {\n        yogaNode.setMeasureFunc(measureCanvas$1(page, node));\n    }\n    return node;\n};\nconst isLayoutElement = (node) => !isText$2(node) && !isNote(node) && !isSvg(node);\n/**\n * @typedef {Function} CreateYogaNodes\n * @param {Object} node\n * @returns {Object} node with appended yoga node\n */\n/**\n * Creates and add yoga node to document tree\n * Handles measure function for text and image nodes\n *\n * @returns Create yoga nodes\n */\nconst createYogaNodes = (page, fontStore, yoga) => (node) => {\n    const yogaNode = yoga.node.create();\n    const result = Object.assign({}, node, { yogaNode });\n    setYogaValues(result);\n    if (isLayoutElement(node) && node.children) {\n        const resolveChild = compose(insertYogaNodes(yogaNode), createYogaNodes(page, fontStore, yoga));\n        result.children = node.children.map(resolveChild);\n    }\n    setMeasureFunc(result, page, fontStore);\n    return result;\n};\n/**\n * Performs yoga calculation\n *\n * @param page - Page node\n * @returns Page node\n */\nconst calculateLayout = (page) => {\n    page.yogaNode.calculateLayout();\n    return page;\n};\n/**\n * Saves Yoga layout result into 'box' attribute of node\n *\n * @param node\n * @returns Node with box data\n */\nconst persistDimensions = (node) => {\n    if (isTextInstance$1(node))\n        return node;\n    const box = Object.assign(getPadding(node), getMargin(node), getBorderWidth(node), getPosition(node), getDimension(node));\n    const newNode = Object.assign({}, node, { box });\n    if (!node.children)\n        return newNode;\n    const children = node.children.map(persistDimensions);\n    return Object.assign({}, newNode, { children });\n};\n/**\n * Removes yoga node from document tree\n *\n * @param node\n * @returns Node without yoga node\n */\nconst destroyYogaNodes = (node) => {\n    const newNode = Object.assign({}, node);\n    delete newNode.yogaNode;\n    if (!node.children)\n        return newNode;\n    const children = node.children.map(destroyYogaNodes);\n    return Object.assign({}, newNode, { children });\n};\n/**\n * Free yoga node from document tree\n *\n * @param node\n * @returns Node without yoga node\n */\nconst freeYogaNodes = (node) => {\n    if (node.yogaNode)\n        node.yogaNode.freeRecursive();\n    return node;\n};\n/**\n * Calculates page object layout using Yoga.\n * Takes node values from 'box' and 'style' attributes, and persist them back into 'box'\n * Destroy yoga values at the end.\n *\n * @param page - Object\n * @returns Page object with correct 'box' layout attributes\n */\nconst resolvePageDimensions = (page, fontStore, yoga) => {\n    if (isNil(page))\n        return null;\n    return compose(destroyYogaNodes, freeYogaNodes, persistDimensions, calculateLayout, createYogaNodes(page, fontStore, yoga))(page);\n};\n/**\n * Calculates root object layout using Yoga.\n *\n * @param node - Root object\n * @param fontStore - Font store\n * @returns Root object with correct 'box' layout attributes\n */\nconst resolveDimensions = (node, fontStore) => {\n    if (!node.children)\n        return node;\n    const resolveChild = (child) => resolvePageDimensions(child, fontStore, node.yoga);\n    const children = node.children.map(resolveChild);\n    return Object.assign({}, node, { children });\n};\n\nconst isText$1 = (node) => node.type === P.Text;\n// Prevent splitting elements by low decimal numbers\nconst SAFETY_THRESHOLD = 0.001;\nconst assingChildren = (children, node) => Object.assign({}, node, { children });\nconst getTop = (node) => node.box?.top || 0;\nconst allFixed = (nodes) => nodes.every(isFixed);\nconst isDynamic = (node) => node.props && 'render' in node.props;\nconst relayoutPage = compose(resolveTextLayout, resolvePageDimensions, resolveInheritance, resolvePageStyles);\nconst warnUnavailableSpace = (node) => {\n    console.warn(`Node of type ${node.type} can't wrap between pages and it's bigger than available page height`);\n};\nconst splitNodes = (height, contentArea, nodes) => {\n    const currentChildren = [];\n    const nextChildren = [];\n    for (let i = 0; i < nodes.length; i += 1) {\n        const child = nodes[i];\n        const futureNodes = nodes.slice(i + 1);\n        const futureFixedNodes = futureNodes.filter(isFixed);\n        const nodeTop = getTop(child);\n        const nodeHeight = child.box.height;\n        const isOutside = height <= nodeTop;\n        const shouldBreak$1 = shouldBreak(child, futureNodes, height);\n        const shouldSplit = height + SAFETY_THRESHOLD < nodeTop + nodeHeight;\n        const canWrap = getWrap(child);\n        const fitsInsidePage = nodeHeight <= contentArea;\n        if (isFixed(child)) {\n            nextChildren.push(child);\n            currentChildren.push(child);\n            continue;\n        }\n        if (isOutside) {\n            const box = Object.assign({}, child.box, { top: child.box.top - height });\n            const next = Object.assign({}, child, { box });\n            nextChildren.push(next);\n            continue;\n        }\n        if (!fitsInsidePage && !canWrap) {\n            currentChildren.push(child);\n            nextChildren.push(...futureNodes);\n            warnUnavailableSpace(child);\n            break;\n        }\n        if (shouldBreak$1) {\n            const box = Object.assign({}, child.box, { top: child.box.top - height });\n            const props = Object.assign({}, child.props, {\n                wrap: true,\n                break: false,\n            });\n            const next = Object.assign({}, child, { box, props });\n            currentChildren.push(...futureFixedNodes);\n            nextChildren.push(next, ...futureNodes);\n            break;\n        }\n        if (shouldSplit) {\n            const [currentChild, nextChild] = split(child, height, contentArea);\n            // All children are moved to the next page, it doesn't make sense to show the parent on the current page\n            if (child.children.length > 0 && currentChild.children.length === 0) {\n                // But if the current page is empty then we can just include the parent on the current page\n                if (currentChildren.length === 0) {\n                    currentChildren.push(child, ...futureFixedNodes);\n                    nextChildren.push(...futureNodes);\n                }\n                else {\n                    const box = Object.assign({}, child.box, {\n                        top: child.box.top - height,\n                    });\n                    const next = Object.assign({}, child, { box });\n                    currentChildren.push(...futureFixedNodes);\n                    nextChildren.push(next, ...futureNodes);\n                }\n                break;\n            }\n            if (currentChild)\n                currentChildren.push(currentChild);\n            if (nextChild)\n                nextChildren.push(nextChild);\n            continue;\n        }\n        currentChildren.push(child);\n    }\n    return [currentChildren, nextChildren];\n};\nconst splitChildren = (height, contentArea, node) => {\n    const children = node.children || [];\n    const availableHeight = height - getTop(node);\n    return splitNodes(availableHeight, contentArea, children);\n};\nconst splitView = (node, height, contentArea) => {\n    const [currentNode, nextNode] = splitNode(node, height);\n    const [currentChilds, nextChildren] = splitChildren(height, contentArea, node);\n    return [\n        assingChildren(currentChilds, currentNode),\n        assingChildren(nextChildren, nextNode),\n    ];\n};\nconst split = (node, height, contentArea) => isText$1(node) ? splitText(node, height) : splitView(node, height, contentArea);\nconst shouldResolveDynamicNodes = (node) => {\n    const children = node.children || [];\n    return isDynamic(node) || children.some(shouldResolveDynamicNodes);\n};\nconst resolveDynamicNodes = (props, node) => {\n    const isNodeDynamic = isDynamic(node);\n    // Call render prop on dynamic nodes and append result to children\n    const resolveChildren = (children = []) => {\n        if (isNodeDynamic) {\n            const res = node.props.render(props);\n            return (createInstances(res)\n                .filter(Boolean)\n                // @ts-expect-error rework dynamic nodes. conflicting types\n                .map((n) => resolveDynamicNodes(props, n)));\n        }\n        return children.map((c) => resolveDynamicNodes(props, c));\n    };\n    // We reset dynamic text box so it can be computed again later on\n    const resetHeight = isNodeDynamic && isText$1(node);\n    const box = resetHeight ? { ...node.box, height: 0 } : node.box;\n    const children = resolveChildren(node.children);\n    // @ts-expect-error handle text here specifically\n    const lines = isNodeDynamic ? null : node.lines;\n    return Object.assign({}, node, { box, lines, children });\n};\nconst resolveDynamicPage = (props, page, fontStore, yoga) => {\n    if (shouldResolveDynamicNodes(page)) {\n        const resolvedPage = resolveDynamicNodes(props, page);\n        return relayoutPage(resolvedPage, fontStore, yoga);\n    }\n    return page;\n};\nconst splitPage = (page, pageNumber, fontStore, yoga) => {\n    const wrapArea = getWrapArea(page);\n    const contentArea = getContentArea(page);\n    const dynamicPage = resolveDynamicPage({ pageNumber }, page, fontStore, yoga);\n    const height = page.style.height;\n    const [currentChilds, nextChilds] = splitNodes(wrapArea, contentArea, dynamicPage.children);\n    const relayout = (node) => \n    // @ts-expect-error rework pagination\n    relayoutPage(node, fontStore, yoga);\n    const currentBox = { ...page.box, height };\n    const currentPage = relayout(Object.assign({}, page, { box: currentBox, children: currentChilds }));\n    if (nextChilds.length === 0 || allFixed(nextChilds))\n        return [currentPage, null];\n    const nextBox = omit('height', page.box);\n    const nextProps = omit('bookmark', page.props);\n    const nextPage = relayout(Object.assign({}, page, {\n        props: nextProps,\n        box: nextBox,\n        children: nextChilds,\n    }));\n    return [currentPage, nextPage];\n};\nconst resolvePageIndices = (fontStore, yoga, page, pageNumber, pages) => {\n    const totalPages = pages.length;\n    const props = {\n        totalPages,\n        pageNumber: pageNumber + 1,\n        subPageNumber: page.subPageNumber + 1,\n        subPageTotalPages: page.subPageTotalPages,\n    };\n    return resolveDynamicPage(props, page, fontStore, yoga);\n};\nconst assocSubPageData = (subpages) => {\n    return subpages.map((page, i) => ({\n        ...page,\n        subPageNumber: i,\n        subPageTotalPages: subpages.length,\n    }));\n};\nconst dissocSubPageData = (page) => {\n    return omit(['subPageNumber', 'subPageTotalPages'], page);\n};\nconst paginate = (page, pageNumber, fontStore, yoga) => {\n    if (!page)\n        return [];\n    if (page.props?.wrap === false)\n        return [page];\n    let splittedPage = splitPage(page, pageNumber, fontStore, yoga);\n    const pages = [splittedPage[0]];\n    let nextPage = splittedPage[1];\n    while (nextPage !== null) {\n        splittedPage = splitPage(nextPage, pageNumber + pages.length, fontStore, yoga);\n        pages.push(splittedPage[0]);\n        nextPage = splittedPage[1];\n    }\n    return pages;\n};\n/**\n * Performs pagination. This is the step responsible of breaking the whole document\n * into pages following pagiation rules, such as `fixed`, `break` and dynamic nodes.\n *\n * @param root - Document node\n * @param fontStore - Font store\n * @returns Layout node\n */\nconst resolvePagination = (root, fontStore) => {\n    let pages = [];\n    let pageNumber = 1;\n    for (let i = 0; i < root.children.length; i += 1) {\n        const page = root.children[i];\n        let subpages = paginate(page, pageNumber, fontStore, root.yoga);\n        subpages = assocSubPageData(subpages);\n        pageNumber += subpages.length;\n        pages = pages.concat(subpages);\n    }\n    pages = pages.map((...args) => dissocSubPageData(resolvePageIndices(fontStore, root.yoga, ...args)));\n    return assingChildren(pages, root);\n};\n\n/**\n * Translates page percentage horizontal paddings in fixed ones\n *\n * @param container - Page container\n * @returns Resolve page horizontal padding\n */\nconst resolvePageHorizontalPadding = (container) => (value) => {\n    const match = matchPercent(value);\n    const width = container.width;\n    return match ? match.percent * width : value;\n};\n/**\n * Translates page percentage vertical paddings in fixed ones\n *\n * @param container - Page container\n * @returns Resolve page vertical padding\n */\nconst resolvePageVerticalPadding = (container) => (value) => {\n    const match = matchPercent(value);\n    const height = container.height;\n    return match ? match.percent * height : value;\n};\n/**\n * Translates page percentage paddings in fixed ones\n *\n * @param page\n * @returns Page with fixed paddings\n */\nconst resolvePagePaddings = (page) => {\n    const container = page.style;\n    const style = evolve({\n        paddingTop: resolvePageVerticalPadding(container),\n        paddingLeft: resolvePageHorizontalPadding(container),\n        paddingRight: resolvePageHorizontalPadding(container),\n        paddingBottom: resolvePageVerticalPadding(container),\n    }, page.style);\n    return Object.assign({}, page, { style });\n};\n/**\n * Translates all pages percentage paddings in fixed ones\n * This has to be computed from pages calculated size and not by Yoga\n * because at this point we didn't performed pagination yet.\n *\n * @param root - Document root\n * @returns Document root with translated page paddings\n */\nconst resolvePagesPaddings = (root) => {\n    if (!root.children)\n        return root;\n    const children = root.children.map(resolvePagePaddings);\n    return Object.assign({}, root, { children });\n};\n\nconst resolveRadius = (box) => (value) => {\n    if (!value)\n        return undefined;\n    const match = matchPercent(value);\n    return match ? match.percent * Math.min(box.width, box.height) : value;\n};\n/**\n * Transforms percent border radius into fixed values\n *\n * @param node\n * @returns Node\n */\nconst resolvePercentRadius = (node) => {\n    const style = evolve({\n        borderTopLeftRadius: resolveRadius(node.box),\n        borderTopRightRadius: resolveRadius(node.box),\n        borderBottomRightRadius: resolveRadius(node.box),\n        borderBottomLeftRadius: resolveRadius(node.box),\n    }, node.style || {});\n    const newNode = Object.assign({}, node, { style });\n    if (!node.children)\n        return newNode;\n    const children = node.children.map(resolvePercentRadius);\n    return Object.assign({}, newNode, { children });\n};\n\n/**\n * Transform percent height into fixed\n *\n * @param height\n * @returns Height\n */\nconst transformHeight = (pageArea, height) => {\n    const match = matchPercent(height);\n    return match ? match.percent * pageArea : height;\n};\n/**\n * Get page area (height minus paddings)\n *\n * @param page\n * @returns Page area\n */\nconst getPageArea = (page) => {\n    const pageHeight = page.style.height;\n    const pagePaddingTop = (page.style?.paddingTop || 0);\n    const pagePaddingBottom = (page.style?.paddingBottom || 0);\n    return pageHeight - pagePaddingTop - pagePaddingBottom;\n};\n/**\n * Transform node percent height to fixed\n *\n * @param page\n * @param node\n * @returns Transformed node\n */\nconst resolveNodePercentHeight = (page, node) => {\n    if (isNil(page.style?.height))\n        return node;\n    if (isNil(node.style?.height))\n        return node;\n    const pageArea = getPageArea(page);\n    const height = transformHeight(pageArea, node.style.height);\n    const style = Object.assign({}, node.style, { height });\n    return Object.assign({}, node, { style });\n};\n/**\n * Transform page immediate children with percent height to fixed\n *\n * @param page\n * @returns Transformed page\n */\nconst resolvePagePercentHeight = (page) => {\n    if (!page.children)\n        return page;\n    const resolveChild = (child) => resolveNodePercentHeight(page, child);\n    const children = page.children.map(resolveChild);\n    return Object.assign({}, page, { children });\n};\n/**\n * Transform all page immediate children with percent height to fixed.\n * This is needed for computing correct dimensions on pre-pagination layout.\n *\n * @param root - Document root\n * @returns Transformed document root\n */\nconst resolvePercentHeight = (root) => {\n    if (!root.children)\n        return root;\n    const children = root.children.map(resolvePagePercentHeight);\n    return Object.assign({}, root, { children });\n};\n\nconst isType = (type) => (node) => node.type === type;\nconst isLink = isType(P.Link);\nconst isText = isType(P.Text);\nconst isTextInstance = isType(P.TextInstance);\n/**\n * Checks if node has render prop\n *\n * @param node\n * @returns Has render prop?\n */\nconst hasRenderProp = (node) => 'render' in node.props;\n/**\n * Checks if node is text type (Text or TextInstance)\n *\n * @param node\n * @returns Are all children text instances?\n */\nconst isTextType = (node) => isText(node) || isTextInstance(node);\n/**\n * Checks if is tet link that needs to be wrapped in Text\n *\n * @param node\n * @returns Are all children text instances?\n */\nconst isTextLink = (node) => {\n    const children = node.children || [];\n    // Text string inside a Link\n    if (children.every(isTextInstance))\n        return true;\n    // Text node inside a Link\n    if (children.every(isText))\n        return false;\n    return children.every(isTextType);\n};\n/**\n * Wraps node children inside Text node\n *\n * @param node\n * @returns Node with intermediate Text child\n */\nconst wrapText = (node) => {\n    const textElement = {\n        type: P.Text,\n        props: {},\n        style: {},\n        box: {},\n        children: node.children,\n    };\n    return Object.assign({}, node, { children: [textElement] });\n};\nconst transformLink = (node) => {\n    if (!isLink(node))\n        return node;\n    // If has render prop substitute the instance by a Text, that will\n    // ultimately render the inline Link via the textkit PDF renderer.\n    if (hasRenderProp(node))\n        return Object.assign({}, node, { type: P.Text });\n    // If is a text link (either contains Text or TextInstance), wrap it\n    // inside a Text element so styles are applied correctly\n    if (isTextLink(node))\n        return wrapText(node);\n    return node;\n};\n/**\n * Transforms Link layout to correctly render text and dynamic rendered links\n *\n * @param node\n * @returns Node with link substitution\n */\nconst resolveLinkSubstitution = (node) => {\n    if (!node.children)\n        return node;\n    const resolveChild = compose(transformLink, resolveLinkSubstitution);\n    const children = node.children.map(resolveChild);\n    return Object.assign({}, node, { children });\n};\n\nconst layout = asyncCompose(resolveZIndex, resolveOrigin, resolveAssets, resolvePagination, resolveTextLayout, resolvePercentRadius, resolveDimensions, resolveSvg, resolveAssets, resolveInheritance, resolvePercentHeight, resolvePagesPaddings, resolveStyles, resolveLinkSubstitution, resolveBookmarks, resolvePageSizes, resolveYoga);\n\nexport { layout as default };\n"], "names": [], "mappings": ";;;AAynBY;AAznBZ;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;AAEA;;;;;;CAMC,GACD,MAAM,gBAAgB,CAAC,MAAM;IACzB,OAAQ;QACJ,KAAK;YACD,OAAO,KAAK,WAAW;QAC3B,KAAK;YACD,OAAO,KAAK,WAAW;QAC3B,KAAK;YACD,OAAO,CAAA,GAAA,wJAAA,CAAA,aAAU,AAAD,EAAE;QACtB,KAAK;YACD,OAAO,CAAA,GAAA,wJAAA,CAAA,aAAU,AAAD,EAAE;QACtB;YACI,OAAO;IACf;AACJ;AAEA,MAAM,UAAU,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,QAAO;AAC/C,MAAM,mBAAmB,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,eAAc;AAC/D,MAAM,YAAY;IACd,MAAA,8JAAA,CAAA,OAAI;IACJ,aAAA,8JAAA,CAAA,cAAW;IACX,eAAA,8JAAA,CAAA,gBAAa;IACb,gBAAA,8JAAA,CAAA,iBAAc;IACd,gBAAA,8JAAA,CAAA,iBAAc;IACd,iBAAA,8JAAA,CAAA,kBAAe;IACf,kBAAA,8JAAA,CAAA,mBAAgB;AACpB;AACA,MAAM,WAAW,CAAA,GAAA,8JAAA,CAAA,UAAY,AAAD,EAAE;AAC9B,MAAM,iBAAiB,CAAC,WAAW;IAC/B,IAAI,CAAC,UACD,OAAO;QAAC;YAAE,QAAQ;QAAG;KAAE;IAC3B,MAAM,YAAY,EAAE;IACpB,MAAM,EAAE,OAAO,OAAO,EAAE,aAAa,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,aAAa,EAAE,OAAO,EAAG,GAAG,SAAS,KAAK;IAC5K,MAAM,kBAAkB,SAAS,KAAK,CAAC,cAAc;IACrD,MAAM,eAAe,OAAO,eAAe,WAAW;QAAC;KAAW,GAAG;WAAK,cAAc,EAAE;KAAE;IAC5F,gBAAgB;IAChB,aAAa,IAAI,CAAC;IAClB,MAAM,OAAO,aAAa,GAAG,CAAC,CAAC;QAC3B,MAAM,OAAO;YAAE,YAAY;YAAgB;YAAY;QAAU;QACjE,MAAM,MAAM,UAAU,OAAO,CAAC;QAC9B,OAAO,KAAK;IAChB;IACA,MAAM,aAAa;QACf;QACA;QACA;QACA,OAAO;QACP,gBAAgB;QAChB,WAAW,oBAAoB,eAC3B,oBAAoB,4BACpB,oBAAoB;QACxB,gBAAgB,uBAAuB;QACvC,QAAQ,oBAAoB,kBACxB,oBAAoB,4BACpB,oBAAoB;QACxB,aAAa;QACb,aAAa,uBAAuB;IACxC;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAG;QAClD,MAAM,QAAQ,SAAS,QAAQ,CAAC,EAAE;QAClC,IAAI,iBAAiB,QAAQ;YACzB,UAAU,IAAI,CAAC;gBACX,QAAQ,cAAc,MAAM,KAAK,EAAE;gBACnC;YACJ;QACJ,OACK,IAAI,OAAO;YACZ,UAAU,IAAI,IAAI,eAAe,WAAW;QAChD;IACJ;IACA,OAAO;AACX;AACA,MAAM,wBAAwB,CAAC,WAAW,WAAa,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,WAAW;AAC/F,MAAM,iBAAiB;AACvB,MAAM,yBAAyB;IAAE,QAAQ,CAAC;IAAK,OAAO,CAAC;AAAI;AAC3D,MAAM,cAAc,CAAC,YAAc,CAAC,MAAM;QACtC,MAAM,mBAAmB,sBAAsB,WAAW;QAC1D,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,KAAK,YAAY,UAAU,KAAK,KAAK,CAAC,CAAC;QAC7D,MAAM,IAAI,KAAK,KAAK,EAAE,KAAK;QAC3B,MAAM,YAAY;YAAE;YAAG;YAAG,OAAO;YAAgB,QAAQ;QAAe;QACxE,MAAM,sBAAsB,KAAK,KAAK,CAAC,mBAAmB,IACtD,WAAW,4BACX;QACJ,MAAM,gBAAgB;YAAE;YAAqB;QAAuB;QACpE,MAAM,QAAQ,SAAS,kBAAkB,WAAW,eAAe,IAAI;QACvE,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;YAAE;QAAM;IAC3C;AACA,2DAA2D;AAC3D,MAAM,iBAAiB,CAAC;IACpB,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO;QACvC,IAAI,CAAC,QAAQ,QACT,OAAO;QACX,MAAM,eAAe,MAAM,QAAQ,CAAC,EAAE;QACtC,IAAI,MAAM,KAAK,CAAC,CAAC,KAAK,aAClB,QAAQ,KAAK,QAAQ,CAAC,MAAM,GAAG,KAC/B,cAAc,OAAO;YACrB,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;gBAC5B,UAAU;oBAAC;wBAAE,GAAG,YAAY;wBAAE,OAAO,GAAG,aAAa,KAAK,CAAC,CAAC,CAAC;oBAAC;iBAAE;YACpE;QACJ;QACA,OAAO;IACX,GAAG,EAAE;IACL,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AACA,MAAM,eAAe,CAAC,WAAW;IAC7B,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,IAAI,iBAAiB,KAAK,KAAK,EAAE,KAAK;IACtC,MAAM,WAAW,YAAY;IAC7B,MAAM,aAAa,eAAe;IAClC,MAAM,WAAW,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC;QACtC,MAAM,kBAAkB,SAAS,OAAO;QACxC,kBAAkB,gBAAgB,KAAK,CAAC,EAAE,CAAC,QAAQ;QACnD,OAAO;IACX;IACA,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AAEA,MAAM,SAAS,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,OAAM;AAC7C,MAAM,UAAU,CAAC;IACb,MAAM,WAAW,KAAK,QAAQ,IAAI,EAAE;IACpC,MAAM,OAAO,SAAS,IAAI,CAAC;IAC3B,MAAM,SAAS,MAAM,YAAY,EAAE;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,KAAK;QACvB,MAAM,KAAK,MAAM,KAAK,EAAE;QACxB,IAAI,IACA,GAAG,CAAC,GAAG,GAAG;QACd,OAAO;IACX,GAAG,CAAC;AACR;AAEA,MAAM,YAAY,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,OAAM;AAChD,MAAM,aAAa,CAAC;IAChB,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,WAAW,KAAK,QAAQ,CAAC,MAAM,CAAC;IACtC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AACA,MAAM,YAAY;AAClB,MAAM,aAAa,CAAC,MAAM;IACtB,IAAI,CAAC,OACD,OAAO;IACX,IAAI,CAAC,UAAU,IAAI,CAAC,QAChB,OAAO;IACX,MAAM,QAAQ,MAAM,KAAK,CAAC;IAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;AACzB;AACA,MAAM,gBAAgB,CAAC,OAAS,CAAC;QAC7B,MAAM,QAAQ,KAAK,KAAK;QACxB,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,QAAQ,WAAW,MAAM,OAAO,QAAQ;QAC/D,MAAM,WAAW,CAAC,QAAQ,CAAC,IAAI,QACzB,WAAW,MAAM,OAAO,YACxB;QACN,MAAM,WAAW,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK,KAAK,EAAE;YAAE;YAAM;QAAS;QAChE,MAAM,WAAW,KAAK,QAAQ,GACxB,KAAK,QAAQ,CAAC,GAAG,CAAC,cAAc,SAChC;QACN,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;YAAE,OAAO;YAAU;QAAS;IAC/D;AACA,MAAM,YAAY,CAAC;IACf,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,OAAO,QAAQ;IACrB,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC,cAAc;IACjD,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AACA,MAAM,cAAc,CAAC;IACjB,OAAO,WAAW,UAAU;AAChC;AAEA,MAAM,eAAe,CAAC;IAClB,IAAI,CAAC,OACD,OAAO;IACX,IAAI,OAAO,UAAU,UACjB,OAAO;IACX,MAAM,SAAS,MAAM,KAAK,CAAC,UAAU,GAAG,CAAC,wJAAA,CAAA,aAAY;IACrD,IAAI,OAAO,MAAM,KAAK,GAClB,OAAO;IACX,OAAO;QAAE,MAAM,MAAM,CAAC,EAAE;QAAE,MAAM,MAAM,CAAC,EAAE;QAAE,MAAM,MAAM,CAAC,EAAE;QAAE,MAAM,MAAM,CAAC,EAAE;IAAC;AAChF;AAEA,MAAM,iBAAiB,CAAC;IACpB,MAAM,UAAU,aAAa,KAAK,KAAK,CAAC,OAAO;IAC/C,IAAI,SAAS;QACT,OAAO;YAAE,OAAO,QAAQ,IAAI;YAAE,QAAQ,QAAQ,IAAI;QAAC;IACvD;IACA,IAAI,KAAK,KAAK,CAAC,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE;QACvC,OAAO;YACH,OAAO,CAAA,GAAA,wJAAA,CAAA,aAAY,AAAD,EAAE,KAAK,KAAK,CAAC,KAAK;YACpC,QAAQ,CAAA,GAAA,wJAAA,CAAA,aAAY,AAAD,EAAE,KAAK,KAAK,CAAC,MAAM;QAC1C;IACJ;IACA,OAAO;QAAE,OAAO;QAAG,QAAQ;IAAE;AACjC;AAEA,MAAM,2BAA2B;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,6DAA6D;AAC7D,MAAM,2BAA2B,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE;IAAC;CAAI,EAAE;AAChD,MAAM,sBAAsB;IACxB,CAAC,+JAAA,CAAA,OAAM,CAAC,EAAE;AACd;AACA,MAAM,kBAAkB,CAAC;IACrB,MAAM,QAAQ,KAAK,KAAK,IAAI,CAAC;IAC7B,MAAM,oBAAoB,mBAAmB,CAAC,KAAK,IAAI,CAAC,IAAI;IAC5D,OAAO,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,mBAAmB;AACnC;AACA,MAAM,eAAe,CAAC;IAClB,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,iBAAiB,gBAAgB;IACvC,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC;QAChC,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAC,GAAG,gBAAgB,MAAM,KAAK,IAAI,CAAC;QAChE,MAAM,WAAW,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;YAAE;QAAM;QAClD,OAAO,aAAa;IACxB;IACA,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AAEA,MAAM,mBAAmB,CAAC;IACtB,IAAI,OAAO,UAAU,UACjB,OAAO;IACX,MAAM,QAAQ,MACT,OAAO,CAAC,iBAAiB,KACzB,OAAO,CAAC,YAAY,IACpB,KAAK,CAAC;IACX,MAAM,QAAS,KAAK,CAAC,EAAE,IAAI;IAC3B,MAAM,cAAe,KAAK,CAAC,EAAE,IACzB;IACJ,OAAO;QAAE;QAAO;IAAY;AAChC;AAEA,MAAM,cAAc;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,MAAM,iBAAiB;IAAC;IAAK;IAAM;IAAM;IAAU;IAAM;CAAK;AAC9D,MAAM,mBAAmB;IAAC;IAAK;IAAM;IAAM;IAAS;IAAM;CAAK;AAC/D,MAAM,UAAU,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,MAAK;AAC7C,MAAM,WAAW,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,OAAM;AAC/C,MAAM,mBAAmB,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,eAAc;AAC/D,MAAM,mBAAmB,CAAC,YAAc,CAAC,QAAU,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,OAAO,CAAC,OAAO;YACxE,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE;YAC3B,IAAI,SAAS,eAAe,QAAQ,CAAC,MAAM;gBACvC,OAAO,MAAM,OAAO,GAAG,UAAU,MAAM;YAC3C;YACA,IAAI,SAAS,iBAAiB,QAAQ,CAAC,MAAM;gBACzC,OAAO,MAAM,OAAO,GAAG,UAAU,KAAK;YAC1C;YACA,OAAO;QACX;AACA,MAAM,eAAe,CAAC;IAClB,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE;IAC3B,OAAO,QAAQ,MAAM,OAAO,GAAG,CAAA,GAAA,wJAAA,CAAA,aAAY,AAAD,EAAE;AAChD;AACA,MAAM,iBAAiB,CAAC,YAAc,CAAC;QACnC,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAY,AAAD,EAAE,WAAW;YAAE,WAAW;QAAM,GAAG,SAAS;IAClE;AACA,MAAM,aAAa,CAAC,YAAc,CAAC;QAC/B,IAAI,QAAQ,iBAAiB,WAAW,KAAK,KAAK;QAClD,QAAQ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE;YACX,GAAG,wJAAA,CAAA,aAAY;YACf,IAAI,wJAAA,CAAA,aAAY;YAChB,IAAI,wJAAA,CAAA,aAAY;YAChB,GAAG,wJAAA,CAAA,aAAY;YACf,IAAI,wJAAA,CAAA,aAAY;YAChB,IAAI,wJAAA,CAAA,aAAY;YAChB,GAAG,wJAAA,CAAA,aAAY;YACf,IAAI,wJAAA,CAAA,aAAY;YAChB,IAAI,wJAAA,CAAA,aAAY;YAChB,IAAI,wJAAA,CAAA,aAAY;YAChB,IAAI,wJAAA,CAAA,aAAY;YAChB,OAAO,wJAAA,CAAA,aAAY;YACnB,QAAQ,wJAAA,CAAA,aAAY;YACpB,QAAQ;YACR,MAAM,+JAAA,CAAA,iBAAc;YACpB,SAAS;YACT,QAAQ,+JAAA,CAAA,iBAAc;YACtB,aAAa;YACb,WAAW,+JAAA,CAAA,iBAAc;YACzB,WAAW,eAAe;YAC1B,mBAAmB,eAAe;QACtC,GAAG;QACH,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;YAAE;QAAM;IAC3C;AACA,MAAM,gBAAgB,CAAC;IACnB,MAAM,QAAQ,KAAK,KAAK,IAAI,CAAC;IAC7B,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO,KAAK,KAAK;IACjD,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAM;AAC3C;AACA,MAAM,mBAAmB,CAAC;IACtB,MAAM,aAAa,CAAC,QAAW,UAAU,SAAS,OAAO;IACzD,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,KAAK,KAAK,EAAE;IACpC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAM;AAC3C;AACA,MAAM,iBAAiB,CAAC;IACpB,MAAM,QAAQ,KAAK,KAAK,IAAI,CAAC;IAC7B,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,aAAa;IACrC,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAC,GAAG,YAAY,KAAK,KAAK,IAAI,CAAC;IAC3D,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAM;AAC3C;AACA,MAAM,gBAAgB,CAAC;IACnB,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE;QACjB,OAAO,wJAAA,CAAA,aAAY;QACnB,QAAQ,wJAAA,CAAA,aAAY;QACpB,SAAS;QACT,qBAAqB;IACzB,GAAG,KAAK,KAAK;IACb,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAM;AAC3C;AACA,MAAM,mBAAmB,CAAC,OAAS,CAAC;QAChC,MAAM,+JAAA,CAAA,QAAO;QACb,OAAO,CAAC;QACR,OAAO,CAAC;QACR,UAAU;YAAC;SAAK;IACpB,CAAC;AACD,MAAM,kBAAkB,CAAC;IACrB,IAAI,CAAC,SAAS,OACV,OAAO;IACX,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,eAAe,CAAC,QAAU,iBAAiB,SAAS,iBAAiB,SAAS;IACpF,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AACA,MAAM,YAAY,CAAC,YAAc,CAAC;QAC9B,IAAI,SAAS,OACT,OAAO,aAAa,WAAW;QACnC,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;QACX,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC,UAAU;QAC7C,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;YAAE;QAAS;IAC9C;AACA,MAAM,iBAAiB,CAAC,YAAc,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,WAAW,YAAY,iBAAiB,kBAAkB;AACxG,MAAM,kBAAkB,CAAC,YAAc,CAAC;QACpC,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;QACX,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,YAAY,eAAe;QACxE,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;QACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;YAAE;QAAS;IAC9C;AACA,MAAM,mBAAmB,CAAC;IACtB,MAAM,UAAU,CAAC;IACjB,MAAM,gBAAgB,KAAK,QAAQ,EAAE,MAAM,MAAM,EAAE;IACnD,MAAO,cAAc,MAAM,GAAG,EAAG;QAC7B,MAAM,QAAQ,cAAc,KAAK;QACjC,IAAI,MAAM,KAAK,IAAI,QAAQ,MAAM,KAAK,EAAE;YACpC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,GAAG;QAC9B;QACA,IAAI,MAAM,QAAQ,EACd,cAAc,IAAI,IAAI,MAAM,QAAQ;IAC5C;IACA,OAAO;AACX;AACA,MAAM,gBAAgB,CAAC,MAAM;IACzB,IAAI,KAAK,KAAK,IAAI,eAAe,KAAK,KAAK,EAAE;QACzC,MAAM,aAAa,OAAO,CAAC,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,IAAI;QAClE,yBAAyB;QACzB,IAAI,CAAC,YACD,OAAO;QACX,MAAM,WAAW,OAAO,MAAM,CAAC,CAAC,GAAG,WAAW,KAAK,EAAE,KAAK,KAAK;QAC/D,OAAO,SAAS,SAAS;QACzB,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,YAAY;YAAE,OAAO;QAAS;IAC3D;IACA,MAAM,WAAW,KAAK,QAAQ,EAAE,IAAI,CAAC,QAAU,cAAc,OAAO;IACpE,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AACA,MAAM,gBAAgB,CAAC;IACnB,MAAM,UAAU,iBAAiB;IACjC,OAAO,cAAc,MAAM;AAC/B;AACA,MAAM,iBAAiB,CAAC,MAAM;IAC1B,MAAM,YAAY,eAAe;IACjC,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,aAAa,UAAU,YAAY,eAAe,gBAAgB,cAAc,gBAAgB,YAAY,eAAe;AAC9I;AACA;;;;;;CAMC,GACD,MAAM,aAAa,CAAC,MAAM;IACtB,IAAI,CAAC,CAAC,cAAc,IAAI,GACpB,OAAO;IACX,MAAM,eAAe,CAAC,QAAU,WAAW,OAAO;IAClD,MAAM,OAAO,QAAQ,QAAQ,eAAe,MAAM,aAAa;IAC/D,MAAM,WAAW,KAAK,QAAQ,EAAE,IAAI;IACpC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AAEA,IAAI;AACJ,MAAM,WAAW;IACb,gEAAgE;IAChE,+EAA+E;IAC/E,MAAM,WAAW,MAAM,CAAC,oBAAoB,CAAA,GAAA,wKAAA,CAAA,WAAU,AAAD,GAAG;IACxD,MAAM,SAAS,SAAS,MAAM,CAAC,MAAM;IACrC,OAAO,mBAAmB,CAAC;IAC3B,MAAM,OAAO;QAAE,QAAQ,IAAM,SAAS,IAAI,CAAC,gBAAgB,CAAC;IAAQ;IACpE,OAAO;QAAE;IAAK;AAClB;AAEA,MAAM,cAAc,OAAO;IACvB,MAAM,OAAO,MAAM;IACnB,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAK;AAC1C;AAEA,MAAM,YAAY,CAAC,OAAS,KAAK,KAAK,CAAC,MAAM;AAC7C,MAAM,aAAa,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,WAAU,IAAI,KAAK,IAAI,KAAK,+JAAA,CAAA,MAAK;AAC5E,MAAM,aAAa,CAAC,GAAG;IACnB,MAAM,KAAK,UAAU;IACrB,MAAM,KAAK,UAAU;IACrB,IAAI,CAAC,MAAM,CAAC,IACR,OAAO;IACX,IAAI,CAAC,IACD,OAAO;IACX,IAAI,CAAC,IACD,OAAO,CAAC;IACZ,OAAO,KAAK;AAChB;AACA;;;;;CAKC,GACD,MAAM,oBAAoB,CAAC;IACvB,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,iBAAiB,WAAW,QAC5B,KAAK,QAAQ,CAAC,IAAI,CAAC,cACnB,KAAK,QAAQ;IACnB,MAAM,WAAW,eAAe,GAAG,CAAC;IACpC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AACA;;;;;CAKC,GACD,MAAM,gBAAgB,CAAC,OAAS,kBAAkB;AAElD,2BAA2B;AAC3B,MAAM,SAAS,CAAC;AAChB,MAAM,QAAQ,CAAA,GAAA,2IAAA,CAAA,UAAU,AAAD;AACvB;;;;;;;;;CASC,GACD,MAAM,2BAA2B,CAAC,IAAM,MAAM;AAC9C,MAAM,gBAAgB,CAAC,QAAQ,yBAAyB,KAAK,GAAK,MAAM,IAAI,CAAC,QACxE,MAAM,CAAC,yBAAyB,IAAM,OAAO,0BAC7C,GAAG,CAAC,CAAC,OAAS,KAAK,WAAW,CAAC,GAAG,QAAQ,CAAC,KAC3C,IAAI,CAAC;AACV,MAAM,gBAAgB,CAAC,OAAO;IAC1B,IAAI,aAAa,QAAQ;QACrB,OAAO,OAAO,OAAO,CAAC,cAAc,OAAO,OAAO,sBAAsB;IAC5E;IACA,MAAM,EAAE,GAAG,EAAE,SAAS,KAAK,EAAE,sBAAsB,EAAE,GAAG;IACxD,OAAO,GAAG,MAAM,cAAc,OAAO,wBAAwB,CAAC,EAAE,QAAQ;AAC5E;AACA,MAAM,cAAc,CAAC,QAAQ;IACzB,IAAI,CAAC,QACD,OAAO,EAAE;IACb,MAAM,WAAW,EAAE;IACnB,MAAM,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ,OAAO,CAAC,CAAC;QACxC,MAAM,QAAQ,KAAK,CAAC,EAAE;QACtB,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;YACzC,MAAM,WAAW,cAAc,OAAO;YACtC,MAAM,CAAC,MAAM,GAAG;gBAAE,SAAS;YAAK;YAChC,SAAS,IAAI,CAAC,CAAA,GAAA,qKAAA,CAAA,UAAY,AAAD,EAAE;gBAAE,KAAK;YAAS,GAAG,IAAI,CAAC,CAAC;gBAChD,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG;gBACxB,MAAM,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,IAAI;YACnC;QACJ;IACJ;IACA,OAAO;AACX;AACA,MAAM,cAAc,CAAC;IACjB,MAAM,SAAS,EAAE;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,KAAK,EAAG;QAC1C,MAAM,WAAW,SAAS,CAAC,EAAE;QAC7B,IAAI,YAAY;QAChB,MAAM,IAAI,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,QAAQ,OAAO,CAAC,CAAC;YACjD,MAAM,EAAE,KAAK,EAAE,GAAG;YAClB,MAAM,QAAQ,KAAK,CAAC,EAAE;YACtB,MAAM,YAAY,SAAS,UAAU,CAAC,QAAQ;YAC9C,MAAM,QAAQ,SAAS,MAAM,CAAC,KAAK,CAAC,WAAW,QAAQ,KAAK,CAAC,EAAE,CAAC,MAAM;YACtE,8DAA8D;YAC9D,wDAAwD;YACxD,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE;gBACrC,OAAO,IAAI,CAAC;oBACR,QAAQ,MAAM,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,OAAO,YAAY,CAAC;oBACpD,YAAY;wBACR,GAAG,SAAS,UAAU;wBACtB,YAAY;4BACR,OAAO;4BACP,QAAQ;4BACR,SAAS,KAAK,KAAK,CAAC,YAAY;4BAChC,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI;wBAC7B;oBACJ;gBACJ;YACJ,OACK;gBACD,qDAAqD;gBACrD,OAAO,IAAI,CAAC;oBACR,QAAQ;oBACR,YAAY,SAAS,UAAU;gBACnC;YACJ;YACA,YAAY,QAAQ,MAAM,MAAM;QACpC;QACA,IAAI,YAAY,SAAS,MAAM,CAAC,MAAM,EAAE;YACpC,OAAO,IAAI,CAAC;gBACR,QAAQ,SAAS,MAAM,CAAC,KAAK,CAAC;gBAC9B,YAAY,SAAS,UAAU;YACnC;QACJ;IACJ;IACA,OAAO;AACX;AAEA;;;;;CAKC,GACD,MAAM,YAAY,CAAC;IACf,IAAI,KAAK,KAAK,CAAC,GAAG,EACd,OAAO,KAAK,KAAK,CAAC,GAAG;IACzB,IAAI,KAAK,KAAK,CAAC,MAAM,EACjB,OAAO,KAAK,KAAK,CAAC,MAAM;AAChC;AAEA;;;;;;;CAOC,GACD,MAAM,gBAAgB,OAAO;IACzB,MAAM,SAAS,OAAO,QAAQ,aAAa,MAAM,QAAQ,MAAM;IAC/D,OAAO,OAAO,WAAW,WAAW;QAAE,KAAK;IAAO,IAAI;AAC1D;AAEA;;;;;CAKC,GACD,MAAM,aAAa,OAAO;IACtB,MAAM,MAAM,UAAU;IACtB,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,KAAK;IAC5B,IAAI,CAAC,KAAK;QACN,QAAQ,IAAI,CAAC,OAAO;QACpB;IACJ;IACA,IAAI;QACA,MAAM,SAAS,MAAM,cAAc;QACnC,IAAI,CAAC,QAAQ;YACT,MAAM,IAAI,MAAM,CAAC,wCAAwC,EAAE,QAAQ;QACvE;QACA,KAAK,KAAK,GAAG,MAAM,CAAA,GAAA,qKAAA,CAAA,UAAY,AAAD,EAAE,QAAQ;YAAE;QAAM;QAChD,IAAI,8JAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,WAAW,kBAAkB,MAC7C;QACJ,KAAK,KAAK,CAAC,GAAG,GAAG,UAAU,SAAS,OAAO,IAAI,CAAC,QAAQ,KAAK,OAAO,GAAG;IAC3E,EACA,OAAO,GAAG;QACN,QAAQ,IAAI,CAAC,EAAE,OAAO;IAC1B;AACJ;AAEA,MAAM,YAAY,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,QAAO;AACjD;;;;;;CAMC,GACD,MAAM,cAAc,CAAC,WAAW;IAC5B,MAAM,WAAW,EAAE;IACnB,MAAM,gBAAgB,KAAK,QAAQ,EAAE,MAAM,MAAM,EAAE;IACnD,MAAM,cAAc,YAAY,UAAU,cAAc,KAAK;IAC7D,MAAO,cAAc,MAAM,GAAG,EAAG;QAC7B,MAAM,IAAI,cAAc,KAAK;QAC7B,IAAI,UAAU,IAAI;YACd,SAAS,IAAI,CAAC,WAAW;QAC7B;QACA,IAAI,aAAa,EAAE,KAAK,EAAE,YAAY;YAClC,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,EAAE,KAAK,CAAC,UAAU;YACjD,SAAS,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC,aAAe,UAAU,IAAI,CAAC;oBAC7D;oBACA,WAAW,EAAE,KAAK,CAAC,SAAS;oBAC5B,YAAY,EAAE,KAAK,CAAC,UAAU;gBAClC;QACJ;QACA,IAAI,OAAO,MAAM,UAAU;YACvB,SAAS,IAAI,IAAI,YAAY,GAAG;QACpC;QACA,IAAI,WAAW,KAAK,OAAO,EAAE,KAAK,KAAK,UAAU;YAC7C,SAAS,IAAI,IAAI,YAAY,EAAE,KAAK,EAAE;QAC1C;QACA,IAAI,EAAE,QAAQ,EAAE;YACZ,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAChB,cAAc,IAAI,CAAC;YACvB;QACJ;IACJ;IACA,OAAO;AACX;AACA;;;;;;;CAOC,GACD,MAAM,gBAAgB,OAAO,MAAM;IAC/B,MAAM,WAAW,YAAY,WAAW;IACxC,MAAM,QAAQ,GAAG,CAAC;IAClB,OAAO;AACX;AAEA,MAAM,WAAW,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,OAAM;AAC/C,MAAM,sBAAsB;IACxB,OAAO;IACP,gBAAgB;AACpB;AACA;;;;;;CAMC,GACD,MAAM,eAAe,CAAC,WAAW;IAC7B,IAAI,YAAY;QAAC,KAAK,KAAK;KAAC;IAC5B,IAAI,SAAS,OAAO;QAChB,YAAY,MAAM,OAAO,CAAC,KAAK,KAAK,IAC9B;YAAC;eAAwB,KAAK,KAAK;SAAC,GACpC;YAAC;YAAqB,KAAK,KAAK;SAAC;IAC3C;IACA,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAY,AAAD,EAAE,WAAW;AACnC;AACA;;;;;CAKC,GACD,MAAM,oBAAoB,CAAC,YAAc,CAAC;QACtC,MAAM,QAAQ,aAAa,WAAW;QACtC,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;YAAE;QAAM;QAC3C,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC,kBAAkB;QACrD,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;YAAE;YAAO;QAAS;IACrD;AACA;;;;;CAKC,GACD,MAAM,oBAAoB,CAAC;IACvB,MAAM,MAAM,KAAK,KAAK,EAAE,OAAO;IAC/B,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,QAAQ,KAAK,GAAG,EAAE,SAAS,MAAM,KAAK;IAC5C,MAAM,SAAS,KAAK,GAAG,EAAE,UAAU,MAAM,MAAM;IAC/C,MAAM,cAAc,KAAK,KAAK,EAAE,eAAe;IAC/C,MAAM,UAAU,OAAO,YAAY;IACnC,MAAM,YAAY;QAAE;QAAO;QAAQ;QAAa;QAAK;IAAQ;IAC7D,OAAO,kBAAkB,WAAW;AACxC;AACA;;;;;CAKC,GACD,MAAM,gBAAgB,CAAC;IACnB,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AAEA,MAAM,oBAAoB,CAAC,IAAM,CAAC,OAAS,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,IAAI,QAAQ,KAAK,KAAK,EAAE,CAAC,EAAE,IAAI;AAC/F;;;;;CAKC,GACD,MAAM,YAAY,CAAC;IACf,IAAI,CAAC,KAAK,GAAG,EACT,OAAO;IACX,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG;IAC7C,MAAM,mBAAmB,kBAAkB,oBAAoB;IAC/D,MAAM,mBAAmB,kBAAkB,oBAAoB;IAC/D,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE;IAC9B,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE;IAC9B,MAAM,UAAU,WAAW,QAAQ,SAAS,OAAO,GAAG;IACtD,MAAM,UAAU,WAAW,SAAS,SAAS,OAAO,GAAG;IACvD,IAAI,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,YAAY,OAAO,YAAY,UACrC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS;IACxD,IAAI,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,YAAY,OAAO,YAAY,UACrC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS;IACxD,OAAO;QAAE,MAAM,OAAO;QAAS,KAAK,MAAM;IAAQ;AACtD;AAEA;;;;;CAKC,GACD,MAAM,oBAAoB,CAAC;IACvB,MAAM,SAAS,UAAU;IACzB,MAAM,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAO;IACjD,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS;QAAE;IAAS;AACjD;AACA;;;;;CAKC,GACD,MAAM,gBAAgB,CAAC;IACnB,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AAEA,MAAM,mBAAmB,CAAC;IACtB,OAAO,OAAO,aAAa,WACrB;QAAE,OAAO;QAAU,KAAK;QAAO,UAAU;IAAM,IAC/C;AACV;AACA,MAAM,mBAAmB,CAAC;IACtB,IAAI,OAAO;IACX,MAAM,WAAW,CAAC,KAAK,QAAQ,IAAI,EAAE,EAAE,KAAK,CAAC;IAC7C,MAAM,gBAAgB,SAAS,GAAG,CAAC,CAAC,QAAU,CAAC;YAC3C;YACA,QAAQ;QACZ,CAAC;IACD,MAAO,cAAc,MAAM,GAAG,EAAG;QAC7B,MAAM,UAAU,cAAc,KAAK;QACnC,IAAI,CAAC,SACD;QACJ,MAAM,QAAQ,QAAQ,KAAK;QAC3B,IAAI,SAAS,QAAQ,MAAM;QAC3B,IAAI,MAAM,KAAK,IAAI,cAAc,MAAM,KAAK,EAAE;YAC1C,MAAM,WAAW,iBAAiB,MAAM,KAAK,CAAC,QAAQ;YACtD,MAAM,MAAM;YACZ,MAAM,eAAe;gBAAE;gBAAK,QAAQ,QAAQ;gBAAK,GAAG,QAAQ;YAAC;YAC7D,MAAM,KAAK,CAAC,QAAQ,GAAG;YACvB,SAAS;QACb;QACA,IAAI,MAAM,QAAQ,EAAE;YAChB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACpB,cAAc,IAAI,CAAC;oBAAE,OAAO;oBAAW;gBAAO;YAClD;QACJ;IACJ;IACA,OAAO;AACX;AAEA,MAAM,qBAAqB;IAAC;IAAY;CAAY;AACpD;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC;IACpB,MAAM,QAAQ,KAAK,KAAK,EAAE,eAAe;IACzC,OAAO,mBAAmB,QAAQ,CAAC,SAAS,QAAQ;AACxD;AAEA;;;;;CAKC,GACD,MAAM,cAAc,CAAC,OAAS,eAAe,UAAU;AAEvD,4DAA4D;AAC5D,MAAM,aAAa;IACf,OAAO;QAAC;QAAS;KAAQ;IACzB,OAAO;QAAC;QAAS;KAAQ;IACzB,IAAI;QAAC;QAAS;KAAQ;IACtB,IAAI;QAAC;QAAS;KAAQ;IACtB,IAAI;QAAC;QAAS;KAAQ;IACtB,IAAI;QAAC;QAAQ;KAAQ;IACrB,IAAI;QAAC;QAAQ;KAAO;IACpB,IAAI;QAAC;QAAQ;KAAO;IACpB,IAAI;QAAC;QAAQ;KAAO;IACpB,IAAI;QAAC;QAAQ;KAAO;IACpB,IAAI;QAAC;QAAO;KAAO;IACnB,IAAI;QAAC;QAAQ;KAAM;IACnB,KAAK;QAAC;QAAM;KAAO;IACnB,IAAI;QAAC;QAAS;KAAQ;IACtB,IAAI;QAAC;QAAS;KAAQ;IACtB,IAAI;QAAC;QAAS;KAAQ;IACtB,IAAI;QAAC;QAAS;KAAQ;IACtB,IAAI;QAAC;QAAQ;KAAQ;IACrB,IAAI;QAAC;QAAO;KAAO;IACnB,IAAI;QAAC;QAAQ;KAAM;IACnB,IAAI;QAAC;QAAQ;KAAO;IACpB,IAAI;QAAC;QAAQ;KAAO;IACpB,IAAI;QAAC;QAAQ;KAAO;IACpB,KAAK;QAAC;QAAO;KAAO;IACpB,IAAI;QAAC;QAAS;KAAQ;IACtB,IAAI;QAAC;QAAS;KAAQ;IACtB,IAAI;QAAC;QAAS;KAAQ;IACtB,IAAI;QAAC;QAAQ;KAAQ;IACrB,IAAI;QAAC;QAAQ;KAAO;IACpB,IAAI;QAAC;QAAQ;KAAO;IACpB,IAAI;QAAC;QAAQ;KAAO;IACpB,IAAI;QAAC;QAAQ;KAAO;IACpB,IAAI;QAAC;QAAQ;KAAO;IACpB,IAAI;QAAC;QAAQ;KAAO;IACpB,KAAK;QAAC;QAAO;KAAO;IACpB,KAAK;QAAC;QAAQ;KAAQ;IACtB,KAAK;QAAC;QAAS;KAAO;IACtB,KAAK;QAAC;QAAQ;KAAQ;IACtB,KAAK;QAAC;QAAQ;KAAO;IACrB,KAAK;QAAC;QAAQ;KAAO;IACrB,MAAM;QAAC;QAAS;KAAQ;IACxB,MAAM;QAAC;QAAS;KAAQ;IACxB,MAAM;QAAC;QAAS;KAAQ;IACxB,MAAM;QAAC;QAAQ;KAAQ;IACvB,MAAM;QAAC;QAAO;KAAO;IACrB,WAAW;QAAC;QAAQ;KAAM;IAC1B,OAAO;QAAC;QAAO;KAAM;IACrB,OAAO;QAAC;QAAO;KAAO;IACtB,QAAQ;QAAC;QAAO;KAAM;IACtB,SAAS;QAAC;QAAO;KAAO;IACxB,KAAK;QAAC;QAAK;KAAI;AACnB;AACA;;;;;CAKC,GACD,MAAM,aAAa,CAAC;IAChB,IAAI,OAAO,UAAU,UACjB,OAAO;QAAE;QAAO,MAAM;IAAU;IACpC,MAAM,QAAQ,oCAAoC,IAAI,CAAC;IACvD,OAAO,QACD;QAAE,OAAO,WAAW,KAAK,CAAC,EAAE;QAAG,MAAM,KAAK,CAAC,EAAE,IAAI;IAAK,IACtD;QAAE;QAAO,MAAM;IAAU;AACnC;AACA;;;;;;CAMC,GACD,MAAM,gBAAgB,CAAC,OAAO;IAC1B,IAAI,CAAC,OACD,OAAO;IACX,MAAM,SAAS,WAAW;IAC1B,MAAM,YAAY;IAClB,MAAM,WAAW,AAAC,IAAI,OAAQ;IAC9B,MAAM,WAAW,AAAC,IAAI,OAAQ;IAC9B,IAAI,OAAO,OAAO,KAAK,KAAK,UACxB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,OAAO;IACjD,OAAQ,OAAO,IAAI;QACf,KAAK;YACD,OAAO,OAAO,KAAK,GAAG;QAC1B,KAAK;YACD,OAAO,OAAO,KAAK,GAAG;QAC1B,KAAK;YACD,OAAO,OAAO,KAAK,GAAG;QAC1B,KAAK;YACD,OAAO,KAAK,KAAK,CAAC,OAAO,KAAK,GAAG,CAAC,YAAY,QAAQ;QAC1D;YACI,OAAO,OAAO,KAAK;IAC3B;AACJ;AACA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,MAAQ,CAAC;QAChD,OAAO,cAAc,OAAO;QAC5B,QAAQ,cAAc,QAAQ;IAClC,CAAC;AACD;;;;;CAKC,GACD,MAAM,eAAe,CAAC,IAAM,CAAC;QACzB,OAAO,CAAC,CAAC,EAAE;QACX,QAAQ,CAAC,CAAC,EAAE;IAChB,CAAC;AACD;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC,IAAM,CAAC;QAC3B,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE,KAAK;IACnB,CAAC;AACD;;;;;CAKC,GACD,MAAM,gBAAgB,CAAC;IACnB,OAAO,aAAa,UAAU,CAAC,EAAE,WAAW,GAAG;AACnD;AACA;;;;;CAKC,GACD,MAAM,gBAAgB,CAAC,IAAM,aAAa;QAAC;QAAG;KAAE;AAChD;;;;;CAKC,GACD,MAAM,UAAU,CAAC;IACb,MAAM,QAAQ,KAAK,KAAK,EAAE,QAAQ;IAClC,MAAM,MAAM,KAAK,KAAK,EAAE,OAAO;IAC/B,IAAI;IACJ,IAAI,OAAO,UAAU,UAAU;QAC3B,OAAO,cAAc;IACzB,OACK,IAAI,MAAM,OAAO,CAAC,QAAQ;QAC3B,OAAO,eAAe,aAAa,QAAQ;IAC/C,OACK,IAAI,OAAO,UAAU,UAAU;QAChC,OAAO,eAAe,cAAc,QAAQ;IAChD,OACK;QACD,OAAO,eAAe,OAAO;IACjC;IACA,OAAO,YAAY,QAAQ,eAAe,QAAQ;AACtD;AAEA;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC;IACrB,MAAM,OAAO,QAAQ;IACrB,MAAM,QAAQ,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,KAAK,KAAK,IAAI,CAAC;IACrC,OAAO;QAAE,GAAG,IAAI;QAAE,OAAO;YAAE,GAAG,KAAK;YAAE,GAAG,IAAI;QAAC;IAAE;AACnD;AACA;;;;;CAKC,GACD,MAAM,mBAAmB,CAAC;IACtB,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AAEA,MAAM,UAAU,CAAC;IACb,IAAI,CAAC,KAAK,KAAK,EACX,OAAO;IACX,OAAO,WAAW,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,KAAK,KAAK,OAAO;AAC/D;AAEA;;;;;CAKC,GACD,MAAM,oBAAoB,CAAC,MAAM;IAC7B,IAAI,IAAI;IACR,IAAI,CAAC,KAAK,KAAK,EACX,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE,KAAK,EAAG;QAC3C,MAAM,OAAO,KAAK,KAAK,CAAC,EAAE;QAC1B,IAAI,IAAI,KAAK,GAAG,CAAC,MAAM,GAAG,QACtB,OAAO;QACX,KAAK,KAAK,GAAG,CAAC,MAAM;IACxB;IACA,OAAO,KAAK,KAAK,CAAC,MAAM;AAC5B;AAEA;;;;;CAKC,GACD,MAAM,oBAAoB,CAAC,MAAM;IAC7B,IAAI,UAAU;IACd,IAAI,CAAC,KAAK,KAAK,EACX,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,KAAK,EAAG;QAC/B,MAAM,OAAO,KAAK,KAAK,CAAC,EAAE;QAC1B,IAAI,CAAC,MACD;QACJ,WAAW,KAAK,GAAG,CAAC,MAAM;IAC9B;IACA,OAAO;AACX;AAEA,MAAM,eAAe,CAAC,MAAM;IACxB,MAAM,MAAM,KAAK,GAAG,EAAE,OAAO;IAC7B,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM,IAAI;IACpC,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO,IAAI;IACtC,MAAM,gBAAgB,KAAK,KAAK,CAAC,MAAM;IACvC,MAAM,aAAa,kBAAkB,MAAM,SAAS;IACpD,IAAI,eAAe,GAAG;QAClB,OAAO;IACX;IACA,IAAI,gBAAgB,SAAS;QACzB,OAAO;IACX;IACA,IAAI,aAAa,WAAW,gBAAgB,UAAU,QAAQ;QAC1D,OAAO;IACX;IACA,IAAI,kBAAkB,UAAU,QAAQ;QACpC,OAAO;IACX;IACA,IAAI,gBAAgB,aAAa,QAAQ;QACrC,OAAO,gBAAgB;IAC3B;IACA,OAAO;AACX;AACA,gDAAgD;AAChD,MAAM,YAAY,CAAC,MAAM;IACrB,MAAM,kBAAkB,aAAa,MAAM;IAC3C,MAAM,gBAAgB,kBAAkB,MAAM;IAC9C,MAAM,aAAa,KAAK,GAAG,CAAC,MAAM,GAAG;IACrC,MAAM,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QACpC,KAAK;YACD,GAAG,KAAK,GAAG;YACX,QAAQ;YACR,mBAAmB;QACvB;QACA,OAAO;YACH,GAAG,KAAK,KAAK;YACb,cAAc;YACd,eAAe;YACf,mBAAmB;YACnB,wBAAwB;YACxB,yBAAyB;QAC7B;QACA,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,GAAG;IAC/B;IACA,MAAM,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QACjC,KAAK;YACD,GAAG,KAAK,GAAG;YACX,KAAK;YACL,QAAQ;YACR,gBAAgB;QACpB;QACA,OAAO;YACH,GAAG,KAAK,KAAK;YACb,WAAW;YACX,YAAY;YACZ,gBAAgB;YAChB,qBAAqB;YACrB,sBAAsB;QAC1B;QACA,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC;IAC5B;IACA,OAAO;QAAC;QAAS;KAAK;AAC1B;AAEA,MAAM,WAAW,CAAC,OAAS,KAAK,GAAG,EAAE,OAAO;AAC5C,MAAM,iBAAiB,CAAC,OAAS,CAAC,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE;AACpD,MAAM,YAAY,CAAC,MAAM;IACrB,IAAI,CAAC,MACD,OAAO;QAAC;QAAM;KAAK;IACvB,MAAM,UAAU,SAAS;IACzB,MAAM,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QACpC,KAAK;YACD,GAAG,KAAK,GAAG;YACX,mBAAmB;QACvB;QACA,OAAO;YACH,GAAG,KAAK,KAAK;YACb,cAAc;YACd,eAAe;YACf,mBAAmB;YACnB,wBAAwB;YACxB,yBAAyB;QAC7B;IACJ;IACA,QAAQ,KAAK,CAAC,MAAM,GAAG,SAAS;IAChC,MAAM,aAAa,eAAe,QAC5B,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,SAAS,OAAO,IACnC;IACN,MAAM,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QACjC,KAAK;YACD,GAAG,KAAK,GAAG;YACX,KAAK;YACL,gBAAgB;QACpB;QACA,OAAO;YACH,GAAG,KAAK,KAAK;YACb,WAAW;YACX,YAAY;YACZ,gBAAgB;YAChB,qBAAqB;YACrB,sBAAsB;QAC1B;IACJ;IACA,IAAI,YAAY;QACZ,KAAK,KAAK,CAAC,MAAM,GAAG;IACxB;IACA,OAAO;QAAC;QAAS;KAAK;AAC1B;AAEA,MAAM,iBAAiB;IAAC,+JAAA,CAAA,MAAK;IAAE,+JAAA,CAAA,OAAM;IAAE,+JAAA,CAAA,QAAO;IAAE,+JAAA,CAAA,SAAQ;CAAC;AACzD,MAAM,UAAU,CAAC;IACb,IAAI,eAAe,QAAQ,CAAC,KAAK,IAAI,GACjC,OAAO;IACX,IAAI,CAAC,KAAK,KAAK,EACX,OAAO;IACX,OAAO,UAAU,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,IAAI,GAAG;AACpD;AAEA,MAAM,qBAAqB,CAAC,MAAM;IAC9B,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,OAAO,WAAW,SAAS,kBAAkB,CAAC,QAAQ;AAC1D;AACA;;;;;CAKC,GACD,MAAM,aAAa,CAAC;IAChB,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;IACvB,MAAM,aAAa,mBAAmB,MAAM,wKAAA,CAAA,OAAS,CAAC,GAAG,KACrD,KAAK,cACL,OAAO,cACP;IACJ,MAAM,eAAe,mBAAmB,MAAM,wKAAA,CAAA,OAAS,CAAC,KAAK,KACzD,KAAK,gBACL,OAAO,gBACP;IACJ,MAAM,gBAAgB,mBAAmB,MAAM,wKAAA,CAAA,OAAS,CAAC,MAAM,KAC3D,KAAK,iBACL,OAAO,iBACP;IACJ,MAAM,cAAc,mBAAmB,MAAM,wKAAA,CAAA,OAAS,CAAC,IAAI,KACvD,KAAK,eACL,OAAO,eACP;IACJ,OAAO;QAAE;QAAY;QAAc;QAAe;IAAY;AAClE;AAEA,MAAM,cAAc,CAAC;IACjB,MAAM,SAAS,KAAK,KAAK,EAAE;IAC3B,MAAM,EAAE,aAAa,EAAE,GAAG,WAAW;IACrC,OAAO,SAAS;AACpB;AAEA,MAAM,iBAAiB,CAAC;IACpB,MAAM,SAAS,KAAK,KAAK,EAAE;IAC3B,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,WAAW;IACjD,OAAO,SAAS,gBAAgB;AACpC;AAEA,MAAM,WAAW,CAAC,QAAU,OAAO,UAAU;AAC7C,MAAM,WAAW,CAAC,QAAU,OAAO,UAAU;AAC7C,MAAM,YAAY,CAAC,QAAU,OAAO,UAAU;AAC9C,MAAM,aAAa,CAAC,QAAU,SAAS,MAAM,IAAI,KAAK,OAAO,GAAG,CAAC;AACjE;;;;;;;CAOC,GACD,MAAM,kBAAkB,CAAC;IACrB,IAAI,CAAC,SACD,OAAO,EAAE;IACb,IAAI,MAAM,OAAO,CAAC,UAAU;QACxB,OAAO,QAAQ,MAAM,CAAC,CAAC,KAAK,KAAO,IAAI,MAAM,CAAC,gBAAgB,MAAM,EAAE;IAC1E;IACA,IAAI,UAAU,UAAU;QACpB,OAAO,EAAE;IACb;IACA,IAAI,SAAS,YAAY,SAAS,UAAU;QACxC,OAAO;YAAC;gBAAE,MAAM,+JAAA,CAAA,eAAc;gBAAE,OAAO,GAAG,SAAS;YAAC;SAAE;IAC1D;IACA,IAAI,WAAW,UAAU;QACrB,oDAAoD;QACpD,OAAO,gBAAgB,QAAQ,KAAK,CAAC,QAAQ;IACjD;IACA,IAAI,CAAC,SAAS,QAAQ,IAAI,GAAG;QACzB,oDAAoD;QACpD,OAAO,gBAAgB,QAAQ,IAAI,CAAC,QAAQ,KAAK;IACrD;IACA,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAG,GAAG;IAC7D,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,UAAU,MAAM,CAAC,CAAC,KAAK,QAAU,IAAI,MAAM,CAAC,gBAAgB,SAAS,EAAE;IACtG,OAAO;QACH;YACI;YACA;YACA;YACA,UAAU;QACd;KACH;AACL;AAEA,MAAM,WAAW,CAAC,OAAS,WAAW,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG;AACtE,MAAM,sBAAsB,CAAC,OAAS,sBAAsB,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,gBAAgB,GAAG;AACvG,MAAM,iBAAiB,CAAC,WAAa,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAC,OAAS,KAAK,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,MAAM;AACtG,MAAM,2BAA2B,CAAC;IAC9B,OAAQ,MAAM,GAAG,CAAC,GAAG,GACjB,MAAM,GAAG,CAAC,MAAM,GAChB,MAAM,GAAG,CAAC,YAAY,GACtB,oBAAoB;AAC5B;AACA,MAAM,mBAAmB,CAAC,OAAO;IAC7B,MAAM,wBAAwB,yBAAyB;IACvD,MAAM,6BAA6B,eAAe,eAAe,MAAM,CAAC,CAAC,OAAS,CAAC,CAAC,WAAW,KAAK,KAAK;IACzG,OAAO,KAAK,GAAG,CAAC,uBAAuB;AAC3C;AACA,MAAM,cAAc,CAAC,OAAO,gBAAgB;IACxC,IAAI,WAAW,MAAM,KAAK,EACtB,OAAO;IACX,MAAM,cAAc,SAAS,MAAM,GAAG,CAAC,GAAG,GAAG,MAAM,GAAG,CAAC,MAAM;IAC7D,MAAM,UAAU,QAAQ;IACxB,0EAA0E;IAC1E,MAAM,gBAAgB,iBAAiB,OAAO;IAC9C,sFAAsF;IACtF,qFAAqF;IACrF,MAAM,2BAA2B,MAAM,GAAG,CAAC,GAAG,GAAG,MAAM,GAAG,CAAC,SAAS;IACpE,OAAQ,SAAS,UACZ,eAAe,CAAC,WAChB,CAAC,eAAe,gBAAgB,UAAU;AACnD;AAEA,MAAM,uBAAuB;IACzB;IACA;CACH;AACD,MAAM,qBAAqB,CAAC,OAAS,qBAAqB,MAAM,CAAC,CAAC,KAAK;QACnE,IAAI,QACA,KAAK,oBAAoB,IACzB,KAAK,oBAAoB,CAAC,YAAY;YACtC,OAAO;QACX;QACA,OAAO;eAAI;YAAK,OAAO,YAAY,CAAC;SAAW;IACnD,GAAG,EAAE;AACL,MAAM,cAAc,CAAC,YAAc,UAAU,GAAG,CAAC,CAAC;QAC9C,MAAM,aAAa,mBAAmB,SAAS,UAAU,CAAC,IAAI,CAAC,EAAE;QACjE,MAAM,cAAc,IAAI,OAAO,WAAW,IAAI,CAAC;QAC/C,OAAO;YACH,QAAQ,SAAS,MAAM,CAAC,OAAO,CAAC,aAAa;YAC7C,YAAY,SAAS,UAAU;QACnC;IACJ;AAEA,MAAM,gBAAgB;IAAC;IAAa;CAAY;AAChD,MAAM,YAAY,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,QAAO;AACjD,MAAM,mBAAmB,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,eAAc;AAC/D;;;;;;;;CAQC,GACD,MAAM,eAAe,CAAC,WAAW,UAAU,aAAa,IAAI,EAAE,QAAQ,CAAC;IACnE,IAAI,CAAC,UACD,OAAO;QAAC;YAAE,QAAQ;QAAG;KAAE;IAC3B,IAAI,YAAY,EAAE;IAClB,MAAM,EAAE,QAAQ,OAAO,EAAE,YAAY,KAAK,EAAE,aAAa,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,aAAa,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAG,GAAG,SAAS,KAAK;IACjR,MAAM,eAAe,OAAO,eAAe,WAAW;QAAC;KAAW,GAAG;WAAK,cAAc,EAAE;KAAE;IAC5F,gBAAgB;IAChB,aAAa,IAAI,CAAC;IAClB,MAAM,OAAO,aAAa,GAAG,CAAC,CAAC;QAC3B,MAAM,OAAO;YAAE,YAAY;YAAgB;YAAY;QAAU;QACjE,MAAM,MAAM,UAAU,OAAO,CAAC;QAC9B,OAAO,KAAK;IAChB;IACA,8FAA8F;IAC9F,MAAM,kBAAkB,UAAU,IAAI,OAAO,SAAS,KAAK,CAAC,eAAe;IAC3E,MAAM,aAAa;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,QAAQ;QACR,kBAAkB;QAClB,aAAa;QACb,gBAAgB;QAChB,WAAW,mBAAmB,eAC1B,mBAAmB,4BACnB,mBAAmB;QACvB,QAAQ,mBAAmB,kBACvB,mBAAmB,4BACnB,mBAAmB;QACvB,aAAa,uBAAuB;QACpC,gBAAgB,uBAAuB;QACvC,2CAA2C;QAC3C,MAAM,cAAc,SAAS,KAAK,EAAE,OAAO,SAAS,KAAK,EAAE;QAC3D,OAAO,aAAa,CAAC,cAAc,QAAQ,UAAU,MAAM;IAC/D;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAG;QAClD,MAAM,QAAQ,SAAS,QAAQ,CAAC,EAAE;QAClC,IAAI,UAAU,QAAQ;YAClB,UAAU,IAAI,CAAC;gBACX,QAAQ,OAAO,YAAY,CAAC;gBAC5B,YAAY;oBACR,GAAG,UAAU;oBACb,YAAY;wBACR,OAAQ,MAAM,KAAK,CAAC,KAAK,IAAI;wBAC7B,QAAS,MAAM,KAAK,CAAC,MAAM,IAAI;wBAC/B,OAAO,MAAM,KAAK,CAAC,IAAI;oBAC3B;gBACJ;YACJ;QACJ,OACK,IAAI,iBAAiB,QAAQ;YAC9B,UAAU,IAAI,CAAC;gBACX,QAAQ,cAAc,MAAM,KAAK,EAAE;gBACnC;YACJ;QACJ,OACK,IAAI,OAAO;YACZ,UAAU,IAAI,IAAI,aAAa,WAAW,OAAO,WAAW,IAAI,EAAE,QAAQ;QAC9E;IACJ;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,KAAK,EAAG;QAC9C,MAAM,eAAe,aAAa,CAAC,EAAE;QACrC,YAAY,aAAa;IAC7B;IACA,OAAO;AACX;AACA;;;;;;CAMC,GACD,MAAM,sBAAsB,CAAC,WAAW;IACpC,MAAM,YAAY,aAAa,WAAW;IAC1C,OAAO,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE;AACzB;AAEA,MAAM,UAAU;IACZ,MAAA,8JAAA,CAAA,OAAI;IACJ,aAAA,8JAAA,CAAA,cAAW;IACX,eAAA,8JAAA,CAAA,gBAAa;IACb,gBAAA,8JAAA,CAAA,iBAAc;IACd,gBAAA,8JAAA,CAAA,iBAAc;IACd,iBAAA,8JAAA,CAAA,kBAAe;IACf,kBAAA,8JAAA,CAAA,mBAAgB;AACpB;AACA,MAAM,SAAS,CAAA,GAAA,8JAAA,CAAA,UAAY,AAAD,EAAE;AAC5B,MAAM,cAAc,CAAC,OAAS,KAAK,KAAK,EAAE;AAC1C,MAAM,kBAAkB,CAAC,OAAS,KAAK,KAAK,EAAE;AAC9C;;;;;;;CAOC,GACD,MAAM,eAAe,CAAC,OAAO,QAAQ;IACjC,MAAM,WAAW,YAAY;IAC7B,MAAM,eAAe,gBAAgB;IACrC,OAAO;QACH,GAAG;QACH,GAAG;QACH;QACA;QACA,QAAQ,UAAU;QAClB,cAAc;IAClB;AACJ;AACA;;;;;CAKC,GACD,MAAM,mBAAmB,CAAC,WAAW,OAAS,CAAC;QAC3C,oBAAoB,KAAK,KAAK,CAAC,kBAAkB;QACjD,wBAAwB;YAAE,QAAQ,CAAC;YAAK,OAAO,CAAC;QAAI;QACpD,qBAAqB,KAAK,KAAK,CAAC,mBAAmB,IAC/C,WAAW,4BACX;IACR,CAAC;AACD;;;;;;;;CAQC,GACD,MAAM,aAAa,CAAC,MAAM,OAAO,QAAQ;IACrC,MAAM,mBAAmB,oBAAoB,WAAW;IACxD,MAAM,YAAY,aAAa,OAAO,QAAQ;IAC9C,MAAM,UAAU,iBAAiB,WAAW;IAC5C,MAAM,QAAQ,OAAO,kBAAkB,WAAW;IAClD,OAAO,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS;eAAI;eAAQ;SAAK,EAAE,EAAE;AAC5D;AAEA,MAAM,UAAU,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,MAAK;AAC7C,MAAM,WAAW,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,OAAM;AAC/C,MAAM,gBAAgB,CAAC,OAAS,CAAC,QAAQ,SAAS,CAAC,SAAS;AAC5D,MAAM,mBAAmB,CAAC,OAAS,SAAS,SAAS,CAAC,KAAK,KAAK;AAChE;;;;;;;CAOC,GACD,MAAM,oBAAoB,CAAC,MAAM;IAC7B,IAAI,iBAAiB,OAAO;QACxB,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,WAAW;QAC5E,MAAM,SAAS,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC,aAAa;QAC9E,KAAK,KAAK,GAAG,WAAW,MAAM,OAAO,QAAQ;IACjD;IACA,IAAI,cAAc,OAAO;QACrB,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;QACX,MAAM,WAAW,CAAC,QAAU,kBAAkB,OAAO;QACrD,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;QACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;YAAE;QAAS;IAC9C;IACA,OAAO;AACX;AAEA,MAAM,8BAA8B;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,MAAM,8BAA8B;OAC7B;IACH;CACH;AACD,MAAM,WAAW,CAAC,OAAS,CAAC,OAAS,KAAK,IAAI,KAAK;AACnD,MAAM,UAAU,SAAS,+JAAA,CAAA,MAAK;AAC9B,MAAM,WAAW,SAAS,+JAAA,CAAA,OAAM;AAChC,qBAAqB;AACrB,MAAM,cAAc,CAAC,WAAW,OAAO;IACnC,OAAQ;QACJ,KAAK;YAAkB;gBACnB,iEAAiE;gBACjE,OAAO;oBAAC;oBAAgB;iBAAM,CAAC,MAAM,CAAC,CAAC,IAAM,KAAK,MAAM,QAAQ,IAAI,CAAC;YACzE;QACA;YACI,OAAO;IACf;AACJ;AACA,kCAAkC;AAClC,MAAM,QAAQ,CAAC,iBAAiB;IAC5B,MAAM,eAAe;QAAE,GAAG,eAAe;IAAC;IAC1C,OAAO,OAAO,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,WAAW,MAAM;QAC7C,YAAY,CAAC,UAAU,GAAG,YAAY,WAAW,OAAO,eAAe,CAAC,UAAU;IACtF;IACA,OAAO;AACX;AACA;;;;;CAKC,GACD,MAAM,cAAc,CAAC,kBAAoB,CAAC;QACtC,MAAM,QAAQ,MAAM,iBAAiB,KAAK,KAAK,IAAI,CAAC;QACpD,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;YAAE;QAAM;IAC3C;AACA;;;;;;CAMC,GACD,MAAM,qBAAqB,CAAC;IACxB,IAAI,QAAQ,OACR,OAAO;IACX,IAAI,CAAC,CAAC,cAAc,IAAI,GACpB,OAAO;IACX,MAAM,wBAAwB,SAAS,QACjC,8BACA;IACN,MAAM,gBAAgB,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,uBAAuB,KAAK,KAAK,IAAI,CAAC;IACjE,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB,YAAY;IAC7D,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AAEA,MAAM,oBAAoB,CAAC,MAAM;IAC7B,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,OAAO,WAAW,SAAS,iBAAiB,CAAC,QAAQ;AACzD;AACA;;;;;CAKC,GACD,MAAM,YAAY,CAAC;IACf,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;IACvB,MAAM,YAAY,kBAAkB,MAAM,wKAAA,CAAA,OAAS,CAAC,GAAG,KACnD,KAAK,aACL,OAAO,aACP;IACJ,MAAM,cAAc,kBAAkB,MAAM,wKAAA,CAAA,OAAS,CAAC,KAAK,KACvD,KAAK,eACL,OAAO,eACP;IACJ,MAAM,eAAe,kBAAkB,MAAM,wKAAA,CAAA,OAAS,CAAC,MAAM,KACzD,KAAK,gBACL,OAAO,gBACP;IACJ,MAAM,aAAa,kBAAkB,MAAM,wKAAA,CAAA,OAAS,CAAC,IAAI,KACrD,KAAK,cACL,OAAO,cACP;IACJ,OAAO;QAAE;QAAW;QAAa;QAAc;IAAW;AAC9D;AAEA;;;;;CAKC,GACD,MAAM,cAAc,CAAC;IACjB,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,OAAO;QACH,KAAK,UAAU,oBAAoB;QACnC,OAAO,UAAU,sBAAsB;QACvC,QAAQ,UAAU,uBAAuB;QACzC,MAAM,UAAU,qBAAqB;IACzC;AACJ;AAEA,MAAM,oBAAoB;IACtB,OAAO;IACP,QAAQ;AACZ;AACA;;;;;CAKC,GACD,MAAM,eAAe,CAAC;IAClB,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,IAAI,CAAC,UACD,OAAO;IACX,OAAO;QACH,OAAO,SAAS,gBAAgB;QAChC,QAAQ,SAAS,iBAAiB;IACtC;AACJ;AAEA,MAAM,oBAAoB,CAAC,UAAU,OAAU,WAAW,SAAS,iBAAiB,CAAC,QAAQ;AAC7F;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC;IACpB,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,OAAO;QACH,gBAAgB,kBAAkB,UAAU,wKAAA,CAAA,OAAS,CAAC,GAAG;QACzD,kBAAkB,kBAAkB,UAAU,wKAAA,CAAA,OAAS,CAAC,KAAK;QAC7D,mBAAmB,kBAAkB,UAAU,wKAAA,CAAA,OAAS,CAAC,MAAM;QAC/D,iBAAiB,kBAAkB,UAAU,wKAAA,CAAA,OAAS,CAAC,IAAI;IAC/D;AACJ;AAEA;;;;;CAKC,GACD,MAAM,aAAa,CAAC,QAAU,CAAC;QAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,IAAI,UAAU;YACV,SAAS,UAAU,CAAC,UAAU,SAAS,wKAAA,CAAA,UAAY,CAAC,IAAI,GAAG,wKAAA,CAAA,UAAY,CAAC,IAAI;QAChF;QACA,OAAO;IACX;AAEA,MAAM,WAAW;IACb,QAAQ,wKAAA,CAAA,WAAa,CAAC,MAAM;IAC5B,QAAQ,wKAAA,CAAA,WAAa,CAAC,MAAM;AAChC;AACA;;;;;CAKC,GACD,MAAM,cAAc,CAAC,QAAU,CAAC;QAC5B,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,IAAI,CAAC,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,UAAU,UAAU;YAC3B,MAAM,WAAW,QAAQ,CAAC,MAAM,IAAI,wKAAA,CAAA,WAAa,CAAC,OAAO;YACzD,SAAS,WAAW,CAAC;QACzB;QACA,OAAO;IACX;AAEA,MAAM,YAAY;IACd,MAAM,wKAAA,CAAA,OAAS,CAAC,IAAI;IACpB,gBAAgB,wKAAA,CAAA,OAAS,CAAC,WAAW;AACzC;AACA;;;;;CAKC,GACD,MAAM,cAAc,CAAC,QAAU,CAAC;QAC5B,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,IAAI,UAAU;YACV,MAAM,WAAW,SAAS,CAAC,MAAM,IAAI,wKAAA,CAAA,OAAS,CAAC,MAAM;YACrD,SAAS,WAAW,CAAC;QACzB;QACA,OAAO;IACX;AAEA;;;;;;CAMC,GACD,MAAM,eAAe,CAAC,MAAM,OAAS,CAAC,QAAU,CAAC;YAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,IAAI,CAAC,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,UAAU,UAAU;gBAC3B,MAAM,UAAU,CAAC,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE;gBACvB,MAAM,cAAc,CAAC,GAAG,EAAE,CAAA,GAAA,wJAAA,CAAA,aAAU,AAAD,EAAE,OAAO;gBAC5C,MAAM,aAAa,GAAG,YAAY,IAAI,CAAC;gBACvC,MAAM,gBAAgB,GAAG,YAAY,OAAO,CAAC;gBAC7C,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE;gBAC7B,IAAI,WAAW,CAAC,QAAQ,CAAC,cAAc,EAAE;oBACrC,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,KAAK,SAAS,CAAC;gBAC1E;gBACA,IAAI,SAAS;oBACT,IAAI,SAAS;wBACT,QAAQ,CAAC,cAAc,GAAG,MAAM,QAAQ,KAAK;oBACjD,OACK;wBACD,QAAQ,CAAC,cAAc,GAAG,QAAQ,KAAK;oBAC3C;gBACJ,OACK,IAAI,UAAU,QAAQ;oBACvB,IAAI,SAAS;wBACT,QAAQ,CAAC,WAAW,GAAG;oBAC3B,OACK;wBACD,QAAQ,CAAC,WAAW;oBACxB;gBACJ,OACK,IAAI,SAAS;oBACd,QAAQ,CAAC,YAAY,GAAG,MAAM;gBAClC,OACK;oBACD,QAAQ,CAAC,YAAY,GAAG;gBAC5B;YACJ;YACA,OAAO;QACX;AAEA;;;;;CAKC,GACD,MAAM,cAAc,CAAC,QAAU,CAAC;QAC5B,OAAO,aAAa,YAAY,SAAS,GAAG;IAChD;AAEA;;;;;;CAMC,GACD,MAAM,eAAe,aAAa;AAElC,MAAM,QAAQ;IACV,cAAc,wKAAA,CAAA,QAAU,CAAC,SAAS;IAClC,QAAQ,wKAAA,CAAA,QAAU,CAAC,MAAM;IACzB,YAAY,wKAAA,CAAA,QAAU,CAAC,OAAO;IAC9B,SAAS,wKAAA,CAAA,QAAU,CAAC,OAAO;IAC3B,UAAU,wKAAA,CAAA,QAAU,CAAC,QAAQ;IAC7B,iBAAiB,wKAAA,CAAA,QAAU,CAAC,YAAY;IACxC,gBAAgB,wKAAA,CAAA,QAAU,CAAC,WAAW;IACtC,gBAAgB,wKAAA,CAAA,QAAU,CAAC,WAAW;AAC1C;AACA;;;;;;;CAOC,GACD,MAAM,WAAW,CAAC,OAAS,CAAC,QAAU,CAAC;YACnC,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,MAAM,eAAe,SAAS,UAAU,wKAAA,CAAA,QAAU,CAAC,OAAO,GAAG,wKAAA,CAAA,QAAU,CAAC,IAAI;YAC5E,IAAI,UAAU;gBACV,MAAM,QAAQ,KAAK,CAAC,MAAM,IAAI;gBAC9B,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAA,GAAA,wJAAA,CAAA,aAAU,AAAD,EAAE,OAAO,CAAC,CAAC;YAC5C;YACA,OAAO;QACX;AAEA;;;;;;CAMC,GACD,MAAM,eAAe,SAAS;AAE9B;;;;;;CAMC,GACD,MAAM,gBAAgB,SAAS;AAE/B;;;;;CAKC,GACD,MAAM,gBAAgB,CAAC,QAAU,CAAC;QAC9B,OAAO,aAAa,cAAc,SAAS,GAAG;IAClD;AAEA;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC,QAAU,CAAC;QAC/B,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,IAAI,CAAC,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,UAAU,UAAU;YAC3B,SAAS,cAAc,CAAC;QAC5B;QACA,OAAO;IACX;AAEA;;;;;;CAMC,GACD,MAAM,kBAAkB,SAAS;AAEjC,MAAM,WAAW;IACb,UAAU,wKAAA,CAAA,eAAiB,CAAC,QAAQ;IACpC,UAAU,wKAAA,CAAA,eAAiB,CAAC,QAAQ;IACpC,QAAQ,wKAAA,CAAA,eAAiB,CAAC,MAAM;AACpC;AACA;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC,QAAU,CAAC;QAChC,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,IAAI,CAAC,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,UAAU,UAAU;YAC3B,SAAS,eAAe,CAAC,QAAQ,CAAC,MAAM;QAC5C;QACA,OAAO;IACX;AAEA,MAAM,kBAAkB;IACpB,KAAK,wKAAA,CAAA,gBAAkB,CAAC,GAAG;IAC3B,eAAe,wKAAA,CAAA,gBAAkB,CAAC,UAAU;IAC5C,kBAAkB,wKAAA,CAAA,gBAAkB,CAAC,aAAa;AACtD;AACA;;;;;CAKC,GACD,MAAM,mBAAmB,CAAC,QAAU,CAAC;QACjC,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,IAAI,UAAU;YACV,MAAM,gBAAgB,eAAe,CAAC,MAAM,IAAI,wKAAA,CAAA,gBAAkB,CAAC,MAAM;YACzE,SAAS,gBAAgB,CAAC;QAC9B;QACA,OAAO;IACX;AAEA,MAAM,kBAAkB;IACpB,QAAQ,wKAAA,CAAA,UAAY,CAAC,MAAM;IAC3B,YAAY,wKAAA,CAAA,UAAY,CAAC,OAAO;IAChC,iBAAiB,wKAAA,CAAA,UAAY,CAAC,YAAY;IAC1C,gBAAgB,wKAAA,CAAA,UAAY,CAAC,WAAW;IACxC,gBAAgB,wKAAA,CAAA,UAAY,CAAC,WAAW;AAC5C;AACA;;;;;CAKC,GACD,MAAM,oBAAoB,CAAC,QAAU,CAAC;QAClC,MAAM,EAAE,QAAQ,EAAE,GAAG;QACrB,IAAI,CAAC,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,UAAU,UAAU;YAC3B,MAAM,iBAAiB,eAAe,CAAC,MAAM,IAAI,wKAAA,CAAA,UAAY,CAAC,SAAS;YACvE,SAAS,iBAAiB,CAAC;QAC/B;QACA,OAAO;IACX;AAEA;;;;;;CAMC,GACD,MAAM,eAAe,aAAa,UAAU,wKAAA,CAAA,OAAS,CAAC,GAAG;AACzD;;;;;;CAMC,GACD,MAAM,iBAAiB,aAAa,UAAU,wKAAA,CAAA,OAAS,CAAC,KAAK;AAC7D;;;;;;CAMC,GACD,MAAM,kBAAkB,aAAa,UAAU,wKAAA,CAAA,OAAS,CAAC,MAAM;AAC/D;;;;;;CAMC,GACD,MAAM,gBAAgB,aAAa,UAAU,wKAAA,CAAA,OAAS,CAAC,IAAI;AAE3D;;;;;;CAMC,GACD,MAAM,gBAAgB,aAAa,WAAW,wKAAA,CAAA,OAAS,CAAC,GAAG;AAC3D;;;;;;CAMC,GACD,MAAM,kBAAkB,aAAa,WAAW,wKAAA,CAAA,OAAS,CAAC,KAAK;AAC/D;;;;;;CAMC,GACD,MAAM,mBAAmB,aAAa,WAAW,wKAAA,CAAA,OAAS,CAAC,MAAM;AACjE;;;;;;CAMC,GACD,MAAM,iBAAiB,aAAa,WAAW,wKAAA,CAAA,OAAS,CAAC,IAAI;AAE7D;;;;;;CAMC,GACD,MAAM,eAAe,aAAa,UAAU,wKAAA,CAAA,OAAS,CAAC,GAAG;AACzD;;;;;;CAMC,GACD,MAAM,iBAAiB,aAAa,UAAU,wKAAA,CAAA,OAAS,CAAC,KAAK;AAC7D;;;;;;CAMC,GACD,MAAM,kBAAkB,aAAa,UAAU,wKAAA,CAAA,OAAS,CAAC,MAAM;AAC/D;;;;;;CAMC,GACD,MAAM,gBAAgB,aAAa,UAAU,wKAAA,CAAA,OAAS,CAAC,IAAI;AAE3D;;;;;;CAMC,GACD,MAAM,iBAAiB,aAAa,YAAY,wKAAA,CAAA,OAAS,CAAC,GAAG;AAC7D;;;;;;CAMC,GACD,MAAM,mBAAmB,aAAa,YAAY,wKAAA,CAAA,OAAS,CAAC,KAAK;AACjE;;;;;;CAMC,GACD,MAAM,oBAAoB,aAAa,YAAY,wKAAA,CAAA,OAAS,CAAC,MAAM;AACnE;;;;;;CAMC,GACD,MAAM,kBAAkB,aAAa,YAAY,wKAAA,CAAA,OAAS,CAAC,IAAI;AAE/D;;;;;;CAMC,GACD,MAAM,WAAW,aAAa;AAC9B;;;;;;CAMC,GACD,MAAM,cAAc,aAAa;AACjC;;;;;;CAMC,GACD,MAAM,cAAc,aAAa;AACjC;;;;;;CAMC,GACD,MAAM,YAAY,aAAa;AAC/B;;;;;;CAMC,GACD,MAAM,eAAe,aAAa;AAClC;;;;;;CAMC,GACD,MAAM,eAAe,aAAa;AAElC;;;;;CAKC,GACD,MAAM,YAAY,aAAa,OAAO,wKAAA,CAAA,SAAW,CAAC,GAAG;AACrD;;;;;CAKC,GACD,MAAM,eAAe,aAAa,OAAO,wKAAA,CAAA,SAAW,CAAC,MAAM;AAE3D,MAAM,iBAAiB,CAAC;IACpB,IAAI,CAAC,SACD,OAAO;IACX,IAAI,OAAO,YAAY,UACnB,OAAO;IACX,OAAO,CAAC,QAAQ,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,GAAG,QAAQ,IAAI;AACvE;AACA;;;;;;CAMC,GACD,MAAM,kBAAkB,CAAC,MAAM,OAAS,CAAC,OAAO,WAAW,QAAQ;QAC/D,MAAM,cAAc,eAAe,KAAK,KAAK,CAAC,OAAO,KAAK;QAC1D,IAAI,cAAc,wKAAA,CAAA,cAAgB,CAAC,OAAO,IACtC,cAAc,wKAAA,CAAA,cAAgB,CAAC,MAAM,EAAE;YACvC,OAAO;gBAAE;gBAAO,QAAQ,QAAQ;YAAY;QAChD;QACA,IAAI,eAAe,wKAAA,CAAA,cAAgB,CAAC,OAAO,EAAE;YACzC,OAAO;gBAAE,OAAO,SAAS;YAAY;QACzC;QACA,OAAO,CAAC;IACZ;AAEA;;;;;CAKC,GACD,MAAM,aAAa,CAAC;IAChB,IAAI,CAAC,KAAK,KAAK,EACX,OAAO;IACX,OAAO,KAAK,GAAG,CAAC,MAAM,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,QAAQ;AAChE;AAEA;;;;;CAKC,GACD,MAAM,cAAc,CAAC;IACjB,IAAI,CAAC,KAAK,KAAK,EACX,OAAO,CAAC;IACZ,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,GAAG,CAAC,MAAM,EAAE;AACnE;AAEA,MAAM,oBAAoB;IAAE,QAAQ;IAAK,OAAO;AAAE;AAClD;;;;;;;CAOC,GACD,MAAM,cAAc,CAAC,MAAM,MAAM,YAAc,CAAC,OAAO,WAAW;QAC9D,IAAI,cAAc,wKAAA,CAAA,cAAgB,CAAC,OAAO,EAAE;YACxC,IAAI,CAAC,KAAK,KAAK,EACX,KAAK,KAAK,GAAG,WAAW,MAAM,OAAO,QAAQ;YACjD,OAAO;gBAAE,QAAQ,YAAY;YAAM;QACvC;QACA,IAAI,cAAc,wKAAA,CAAA,cAAgB,CAAC,MAAM,EAAE;YACvC,MAAM,cAAc,iBAAiB,CAAC,KAAK,KAAK,EAAE,UAAU,IAAI;YAChE,IAAI,CAAC,KAAK,KAAK,EAAE;gBACb,KAAK,KAAK,GAAG,WAAW,MAAM,OAAO,QAAQ;gBAC7C,KAAK,WAAW,GAAG,CAAC,QAAQ,WAAW,KAAK,IAAI,aAAa,gDAAgD;YACjH;YACA,OAAO;gBACH,QAAQ,YAAY;gBACpB,OAAO,KAAK,GAAG,CAAC,OAAO,WAAW;YACtC;QACJ;QACA,OAAO,CAAC;IACZ;AAEA;;;;;CAKC,GACD,MAAM,WAAW,CAAC;IACd,OAAO,KAAK,KAAK,EAAE,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,KAAK,CAAC,MAAM,GAAG;AACrE;AAEA;;;;;CAKC,GACD,MAAM,eAAe,CAAC,OAAS,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,KAAK,GAAG,EAAE;AAE/C,MAAM,kBAAkB;AACxB;;;;;;CAMC,GACD,MAAM,eAAe,CAAC,MAAM,OAAS,CAAC,OAAO,WAAW,QAAQ;QAC5D,MAAM,aAAa,SAAS;QAC5B,MAAM,cAAc,UAAU;QAC9B,MAAM,cAAc,WAAW;QAC/B,uCAAuC;QACvC,MAAM,WAAW,aAAa,QACxB,WACA,CAAC,KAAK,GAAG,EAAE,UAAU,CAAC,IACpB,YAAY,UAAU,GACtB,YAAY,aAAa,GACzB,YAAY,SAAS,GACrB,YAAY,YAAY,GACxB;QACR,6CAA6C;QAC7C,IAAI,CAAC,KAAK,KAAK,EACX,OAAO;YAAE,OAAO;YAAG,QAAQ;QAAE;QACjC,IAAI,cAAc,wKAAA,CAAA,cAAgB,CAAC,OAAO,IACtC,eAAe,wKAAA,CAAA,cAAgB,CAAC,SAAS,EAAE;YAC3C,MAAM,eAAe,QAAQ;YAC7B,OAAO;gBAAE,QAAQ,KAAK,GAAG,CAAC,UAAU;YAAc;QACtD;QACA,IAAI,eAAe,wKAAA,CAAA,cAAgB,CAAC,OAAO,IACvC,CAAC,cAAc,wKAAA,CAAA,cAAgB,CAAC,MAAM,IAClC,cAAc,wKAAA,CAAA,cAAgB,CAAC,SAAS,GAAG;YAC/C,OAAO;gBAAE,OAAO,KAAK,GAAG,CAAC,SAAS,YAAY;YAAO;QACzD;QACA,IAAI,cAAc,wKAAA,CAAA,cAAgB,CAAC,OAAO,IACtC,eAAe,wKAAA,CAAA,cAAgB,CAAC,MAAM,EAAE;YACxC,MAAM,eAAe,QAAQ;YAC7B,OAAO;gBAAE,QAAQ,KAAK,GAAG,CAAC,QAAQ,UAAU;YAAc;QAC9D;QACA,IAAI,cAAc,wKAAA,CAAA,cAAgB,CAAC,MAAM,IACrC,eAAe,wKAAA,CAAA,cAAgB,CAAC,MAAM,EAAE;YACxC,IAAI,aAAa,GAAG;gBAChB,OAAO;oBACH;oBACA,QAAQ,KAAK,GAAG,CAAC,QAAQ,YAAY;gBACzC;YACJ;YACA,OAAO;gBACH;gBACA,OAAO,KAAK,GAAG,CAAC,SAAS,YAAY;YACzC;QACJ;QACA,OAAO;YAAE;YAAQ;QAAM;IAC3B;AAEA,MAAM,gBAAgB;AACtB,MAAM,SAAS,CAAC,SAAW,KAAK,GAAG,CAAC,CAAC,aAAa;AAClD;;;CAGC,GACD,MAAM,aAAa;IACf,MAAM,MAAM,CAAC;IACb,MAAM,SAAS,EAAE;IACjB,MAAM,MAAM,IAAM;IAClB,MAAM,WAAW,CAAC,GAAG,IAAM,OAAO,IAAI,CAAC;YAAC;YAAG;SAAE;IAC7C,MAAM,SAAS,CAAC,GAAG;QACf,SAAS,GAAG;QACZ,OAAO;IACX;IACA,MAAM,OAAO,CAAC,GAAG,GAAG,GAAG;QACnB,SAAS,GAAG;QACZ,SAAS,IAAI,GAAG;QAChB,SAAS,GAAG,IAAI;QAChB,SAAS,IAAI,GAAG,IAAI;QACpB,OAAO;IACX;IACA,MAAM,UAAU,CAAC,GAAG,GAAG,IAAI;QACvB,KAAK,MAAM;QACX,SAAS,IAAI,IAAI,IAAI;QACrB,SAAS,IAAI,IAAI,IAAI;QACrB,SAAS,IAAI,IAAI,IAAI;QACrB,SAAS,IAAI,IAAI,IAAI;QACrB,OAAO;IACX;IACA,MAAM,UAAU,CAAC,GAAG;QAChB,OAAO,IAAI,IAAI;QACf,OAAO;IACX;IACA,oBAAoB;IACpB,IAAI,IAAI,GAAG;IACX,IAAI,MAAM,GAAG;IACb,IAAI,MAAM,GAAG;IACb,IAAI,MAAM,GAAG;IACb,IAAI,OAAO,GAAG;IACd,IAAI,OAAO,GAAG;IACd,IAAI,WAAW,GAAG;IAClB,oBAAoB;IACpB,IAAI,IAAI,GAAG;IACX,IAAI,IAAI,GAAG;IACX,IAAI,SAAS,GAAG;IAChB,IAAI,aAAa,GAAG;IACpB,IAAI,gBAAgB,GAAG;IACvB,IAAI,KAAK,GAAG;IACZ,IAAI,MAAM,GAAG;IACb,IAAI,SAAS,GAAG;IAChB,gCAAgC;IAChC,IAAI,IAAI,GAAG;IACX,IAAI,IAAI,GAAG;IACX,IAAI,IAAI,GAAG;IACX,IAAI,IAAI,GAAG;IACX,IAAI,IAAI,GAAG;IACX,IAAI,MAAM,GAAG;IACb,IAAI,OAAO,GAAG;IACd,IAAI,OAAO,GAAG;IACd,IAAI,OAAO,GAAG;IACd,IAAI,QAAQ,GAAG;IACf,IAAI,QAAQ,GAAG;IACf,IAAI,SAAS,GAAG;IAChB,IAAI,UAAU,GAAG;IACjB,IAAI,WAAW,GAAG;IAClB,IAAI,WAAW,GAAG;IAClB,IAAI,aAAa,GAAG;IACpB,IAAI,cAAc,GAAG;IACrB,IAAI,cAAc,GAAG;IACrB,IAAI,QAAQ,GAAG,IAAM,OAAO,OAAO,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE;IAClD,IAAI,SAAS,GAAG,IAAM,OAAO,OAAO,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE;IACnD,OAAO;AACX;AACA;;;CAGC,GACD;;;;;;CAMC,GACD,MAAM,gBAAgB,CAAC,MAAM,OAAS;QAClC,MAAM,cAAc,UAAU;QAC9B,MAAM,cAAc,WAAW;QAC/B,uCAAuC;QACvC,MAAM,WAAW,aAAa,QACxB,WACA,CAAC,KAAK,GAAG,EAAE,UAAU,CAAC,IACpB,YAAY,UAAU,GACtB,YAAY,aAAa,GACzB,YAAY,SAAS,GACrB,YAAY,YAAY,GACxB;QACR,MAAM,MAAM;QACZ,KAAK,KAAK,CAAC,KAAK,CAAC;QACjB,MAAM,QAAQ,IAAI,QAAQ;QAC1B,MAAM,SAAS,KAAK,GAAG,CAAC,UAAU,IAAI,SAAS;QAC/C,OAAO;YAAE;YAAO;QAAO;IAC3B;AAEA,MAAM,WAAW,CAAC,OAAS,CAAC,OAAS,KAAK,IAAI,KAAK;AACnD,MAAM,QAAQ,SAAS,+JAAA,CAAA,MAAK;AAC5B,MAAM,WAAW,SAAS,+JAAA,CAAA,OAAM;AAChC,MAAM,SAAS,SAAS,+JAAA,CAAA,OAAM;AAC9B,MAAM,SAAS,SAAS,+JAAA,CAAA,OAAM;AAC9B,MAAM,UAAU,SAAS,+JAAA,CAAA,QAAO;AAChC,MAAM,WAAW,SAAS,+JAAA,CAAA,SAAQ;AAClC,MAAM,mBAAmB,SAAS,+JAAA,CAAA,eAAc;AAChD,MAAM,gBAAgB,CAAC;IACnB,MAAM,QAAQ,OAAO,QAAQ,KAAK,GAAG,EAAE,SAAS,KAAK,KAAK,EAAE;IAC5D,OAAO,UAAU;AACrB;AACA;;;;CAIC,GACD,MAAM,gBAAgB,CAAC;IACnB,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,cAAc,OAAO,SAAS,KAAK,KAAK,CAAC,KAAK,GAAG,YAAY,KAAK,KAAK,CAAC,QAAQ,GAAG,YAAY,KAAK,KAAK,CAAC,QAAQ,GAAG,aAAa,KAAK,KAAK,CAAC,SAAS,GAAG,aAAa,KAAK,KAAK,CAAC,SAAS,GAAG,aAAa,KAAK,KAAK,CAAC,SAAS,GAAG,eAAe,KAAK,KAAK,CAAC,WAAW,GAAG,gBAAgB,KAAK,KAAK,CAAC,YAAY,GAAG,cAAc,KAAK,KAAK,CAAC,UAAU,GAAG,cAAc,KAAK,KAAK,CAAC,UAAU,GAAG,gBAAgB,KAAK,KAAK,CAAC,YAAY,GAAG,iBAAiB,KAAK,KAAK,CAAC,aAAa,GAAG,eAAe,KAAK,KAAK,CAAC,WAAW,GAAG,gBAAgB,KAAK,KAAK,CAAC,QAAQ,GAAG,eAAe,KAAK,KAAK,CAAC,GAAG,GAAG,iBAAiB,KAAK,KAAK,CAAC,KAAK,GAAG,kBAAkB,KAAK,KAAK,CAAC,MAAM,GAAG,gBAAgB,KAAK,KAAK,CAAC,IAAI,GAAG,aAAa,KAAK,KAAK,CAAC,cAAc,GAAG,eAAe,KAAK,KAAK,CAAC,gBAAgB,GAAG,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,GAAG,cAAc,KAAK,KAAK,CAAC,eAAe,GAAG,WAAW,KAAK,KAAK,CAAC,OAAO,GAAG,iBAAiB,KAAK,KAAK,CAAC,aAAa,GAAG,aAAa,KAAK,KAAK,CAAC,SAAS,GAAG,gBAAgB,KAAK,KAAK,CAAC,YAAY,GAAG,cAAc,KAAK,KAAK,CAAC,UAAU,GAAG,kBAAkB,KAAK,KAAK,CAAC,cAAc,GAAG,YAAY,KAAK,KAAK,CAAC,QAAQ,GAAG,YAAY,KAAK,KAAK,CAAC,QAAQ,GAAG,eAAe,KAAK,KAAK,CAAC,WAAW,GAAG,aAAa,KAAK,KAAK,CAAC,SAAS,GAAG,YAAY,KAAK,KAAK,CAAC,QAAQ,GAAG,cAAc,KAAK,KAAK,CAAC,UAAU,GAAG,UAAU,KAAK,KAAK,CAAC,MAAM,GAAG,aAAa,KAAK,KAAK,CAAC,SAAS,GAAG;AACn3C;AACA;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC,SAAW,CAAC;QACjC,OAAO,WAAW,CAAC,MAAM,QAAQ,EAAE,OAAO,aAAa;QACvD,OAAO;IACX;AACA,MAAM,iBAAiB,CAAC,MAAM,MAAM;IAChC,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,IAAI,SAAS,OAAO;QAChB,SAAS,cAAc,CAAC,YAAY,MAAM,MAAM;IACpD;IACA,IAAI,QAAQ,OAAO;QACf,SAAS,cAAc,CAAC,aAAa,MAAM;IAC/C;IACA,IAAI,SAAS,OAAO;QAChB,SAAS,cAAc,CAAC,cAAc,MAAM;IAChD;IACA,IAAI,MAAM,OAAO;QACb,SAAS,cAAc,CAAC,gBAAgB,MAAM;IAClD;IACA,OAAO;AACX;AACA,MAAM,kBAAkB,CAAC,OAAS,CAAC,SAAS,SAAS,CAAC,OAAO,SAAS,CAAC,MAAM;AAC7E;;;;CAIC,GACD;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC,MAAM,WAAW,OAAS,CAAC;QAChD,MAAM,WAAW,KAAK,IAAI,CAAC,MAAM;QACjC,MAAM,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;YAAE;QAAS;QAClD,cAAc;QACd,IAAI,gBAAgB,SAAS,KAAK,QAAQ,EAAE;YACxC,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,WAAW,gBAAgB,MAAM,WAAW;YACzF,OAAO,QAAQ,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC;QACxC;QACA,eAAe,QAAQ,MAAM;QAC7B,OAAO;IACX;AACA;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC;IACrB,KAAK,QAAQ,CAAC,eAAe;IAC7B,OAAO;AACX;AACA;;;;;CAKC,GACD,MAAM,oBAAoB,CAAC;IACvB,IAAI,iBAAiB,OACjB,OAAO;IACX,MAAM,MAAM,OAAO,MAAM,CAAC,WAAW,OAAO,UAAU,OAAO,eAAe,OAAO,YAAY,OAAO,aAAa;IACnH,MAAM,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAI;IAC9C,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS;QAAE;IAAS;AACjD;AACA;;;;;CAKC,GACD,MAAM,mBAAmB,CAAC;IACtB,MAAM,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG;IAClC,OAAO,QAAQ,QAAQ;IACvB,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS;QAAE;IAAS;AACjD;AACA;;;;;CAKC,GACD,MAAM,gBAAgB,CAAC;IACnB,IAAI,KAAK,QAAQ,EACb,KAAK,QAAQ,CAAC,aAAa;IAC/B,OAAO;AACX;AACA;;;;;;;CAOC,GACD,MAAM,wBAAwB,CAAC,MAAM,WAAW;IAC5C,IAAI,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,OACN,OAAO;IACX,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,eAAe,mBAAmB,iBAAiB,gBAAgB,MAAM,WAAW,OAAO;AAChI;AACA;;;;;;CAMC,GACD,MAAM,oBAAoB,CAAC,MAAM;IAC7B,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,eAAe,CAAC,QAAU,sBAAsB,OAAO,WAAW,KAAK,IAAI;IACjF,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AAEA,MAAM,WAAW,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,OAAM;AAC/C,oDAAoD;AACpD,MAAM,mBAAmB;AACzB,MAAM,iBAAiB,CAAC,UAAU,OAAS,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9E,MAAM,SAAS,CAAC,OAAS,KAAK,GAAG,EAAE,OAAO;AAC1C,MAAM,WAAW,CAAC,QAAU,MAAM,KAAK,CAAC;AACxC,MAAM,YAAY,CAAC,OAAS,KAAK,KAAK,IAAI,YAAY,KAAK,KAAK;AAChE,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,mBAAmB,uBAAuB,oBAAoB;AAC3F,MAAM,uBAAuB,CAAC;IAC1B,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,IAAI,CAAC,oEAAoE,CAAC;AAChH;AACA,MAAM,aAAa,CAAC,QAAQ,aAAa;IACrC,MAAM,kBAAkB,EAAE;IAC1B,MAAM,eAAe,EAAE;IACvB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;QACtC,MAAM,QAAQ,KAAK,CAAC,EAAE;QACtB,MAAM,cAAc,MAAM,KAAK,CAAC,IAAI;QACpC,MAAM,mBAAmB,YAAY,MAAM,CAAC;QAC5C,MAAM,UAAU,OAAO;QACvB,MAAM,aAAa,MAAM,GAAG,CAAC,MAAM;QACnC,MAAM,YAAY,UAAU;QAC5B,MAAM,gBAAgB,YAAY,OAAO,aAAa;QACtD,MAAM,cAAc,SAAS,mBAAmB,UAAU;QAC1D,MAAM,UAAU,QAAQ;QACxB,MAAM,iBAAiB,cAAc;QACrC,IAAI,QAAQ,QAAQ;YAChB,aAAa,IAAI,CAAC;YAClB,gBAAgB,IAAI,CAAC;YACrB;QACJ;QACA,IAAI,WAAW;YACX,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,GAAG,EAAE;gBAAE,KAAK,MAAM,GAAG,CAAC,GAAG,GAAG;YAAO;YACvE,MAAM,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;gBAAE;YAAI;YAC5C,aAAa,IAAI,CAAC;YAClB;QACJ;QACA,IAAI,CAAC,kBAAkB,CAAC,SAAS;YAC7B,gBAAgB,IAAI,CAAC;YACrB,aAAa,IAAI,IAAI;YACrB,qBAAqB;YACrB;QACJ;QACA,IAAI,eAAe;YACf,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,GAAG,EAAE;gBAAE,KAAK,MAAM,GAAG,CAAC,GAAG,GAAG;YAAO;YACvE,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,KAAK,EAAE;gBACzC,MAAM;gBACN,OAAO;YACX;YACA,MAAM,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;gBAAE;gBAAK;YAAM;YACnD,gBAAgB,IAAI,IAAI;YACxB,aAAa,IAAI,CAAC,SAAS;YAC3B;QACJ;QACA,IAAI,aAAa;YACb,MAAM,CAAC,cAAc,UAAU,GAAG,MAAM,OAAO,QAAQ;YACvD,wGAAwG;YACxG,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG,KAAK,aAAa,QAAQ,CAAC,MAAM,KAAK,GAAG;gBACjE,2FAA2F;gBAC3F,IAAI,gBAAgB,MAAM,KAAK,GAAG;oBAC9B,gBAAgB,IAAI,CAAC,UAAU;oBAC/B,aAAa,IAAI,IAAI;gBACzB,OACK;oBACD,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM,GAAG,EAAE;wBACrC,KAAK,MAAM,GAAG,CAAC,GAAG,GAAG;oBACzB;oBACA,MAAM,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;wBAAE;oBAAI;oBAC5C,gBAAgB,IAAI,IAAI;oBACxB,aAAa,IAAI,CAAC,SAAS;gBAC/B;gBACA;YACJ;YACA,IAAI,cACA,gBAAgB,IAAI,CAAC;YACzB,IAAI,WACA,aAAa,IAAI,CAAC;YACtB;QACJ;QACA,gBAAgB,IAAI,CAAC;IACzB;IACA,OAAO;QAAC;QAAiB;KAAa;AAC1C;AACA,MAAM,gBAAgB,CAAC,QAAQ,aAAa;IACxC,MAAM,WAAW,KAAK,QAAQ,IAAI,EAAE;IACpC,MAAM,kBAAkB,SAAS,OAAO;IACxC,OAAO,WAAW,iBAAiB,aAAa;AACpD;AACA,MAAM,YAAY,CAAC,MAAM,QAAQ;IAC7B,MAAM,CAAC,aAAa,SAAS,GAAG,UAAU,MAAM;IAChD,MAAM,CAAC,eAAe,aAAa,GAAG,cAAc,QAAQ,aAAa;IACzE,OAAO;QACH,eAAe,eAAe;QAC9B,eAAe,cAAc;KAChC;AACL;AACA,MAAM,QAAQ,CAAC,MAAM,QAAQ,cAAgB,SAAS,QAAQ,UAAU,MAAM,UAAU,UAAU,MAAM,QAAQ;AAChH,MAAM,4BAA4B,CAAC;IAC/B,MAAM,WAAW,KAAK,QAAQ,IAAI,EAAE;IACpC,OAAO,UAAU,SAAS,SAAS,IAAI,CAAC;AAC5C;AACA,MAAM,sBAAsB,CAAC,OAAO;IAChC,MAAM,gBAAgB,UAAU;IAChC,kEAAkE;IAClE,MAAM,kBAAkB,CAAC,WAAW,EAAE;QAClC,IAAI,eAAe;YACf,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM,CAAC;YAC9B,OAAQ,gBAAgB,KACnB,MAAM,CAAC,QACR,2DAA2D;aAC1D,GAAG,CAAC,CAAC,IAAM,oBAAoB,OAAO;QAC/C;QACA,OAAO,SAAS,GAAG,CAAC,CAAC,IAAM,oBAAoB,OAAO;IAC1D;IACA,iEAAiE;IACjE,MAAM,cAAc,iBAAiB,SAAS;IAC9C,MAAM,MAAM,cAAc;QAAE,GAAG,KAAK,GAAG;QAAE,QAAQ;IAAE,IAAI,KAAK,GAAG;IAC/D,MAAM,WAAW,gBAAgB,KAAK,QAAQ;IAC9C,iDAAiD;IACjD,MAAM,QAAQ,gBAAgB,OAAO,KAAK,KAAK;IAC/C,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;QAAK;QAAO;IAAS;AAC1D;AACA,MAAM,qBAAqB,CAAC,OAAO,MAAM,WAAW;IAChD,IAAI,0BAA0B,OAAO;QACjC,MAAM,eAAe,oBAAoB,OAAO;QAChD,OAAO,aAAa,cAAc,WAAW;IACjD;IACA,OAAO;AACX;AACA,MAAM,YAAY,CAAC,MAAM,YAAY,WAAW;IAC5C,MAAM,WAAW,YAAY;IAC7B,MAAM,cAAc,eAAe;IACnC,MAAM,cAAc,mBAAmB;QAAE;IAAW,GAAG,MAAM,WAAW;IACxE,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM;IAChC,MAAM,CAAC,eAAe,WAAW,GAAG,WAAW,UAAU,aAAa,YAAY,QAAQ;IAC1F,MAAM,WAAW,CAAC,OAClB,qCAAqC;QACrC,aAAa,MAAM,WAAW;IAC9B,MAAM,aAAa;QAAE,GAAG,KAAK,GAAG;QAAE;IAAO;IACzC,MAAM,cAAc,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE,KAAK;QAAY,UAAU;IAAc;IAChG,IAAI,WAAW,MAAM,KAAK,KAAK,SAAS,aACpC,OAAO;QAAC;QAAa;KAAK;IAC9B,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,UAAU,KAAK,GAAG;IACvC,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,YAAY,KAAK,KAAK;IAC7C,MAAM,WAAW,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAC9C,OAAO;QACP,KAAK;QACL,UAAU;IACd;IACA,OAAO;QAAC;QAAa;KAAS;AAClC;AACA,MAAM,qBAAqB,CAAC,WAAW,MAAM,MAAM,YAAY;IAC3D,MAAM,aAAa,MAAM,MAAM;IAC/B,MAAM,QAAQ;QACV;QACA,YAAY,aAAa;QACzB,eAAe,KAAK,aAAa,GAAG;QACpC,mBAAmB,KAAK,iBAAiB;IAC7C;IACA,OAAO,mBAAmB,OAAO,MAAM,WAAW;AACtD;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,SAAS,GAAG,CAAC,CAAC,MAAM,IAAM,CAAC;YAC9B,GAAG,IAAI;YACP,eAAe;YACf,mBAAmB,SAAS,MAAM;QACtC,CAAC;AACL;AACA,MAAM,oBAAoB,CAAC;IACvB,OAAO,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE;QAAC;QAAiB;KAAoB,EAAE;AACxD;AACA,MAAM,WAAW,CAAC,MAAM,YAAY,WAAW;IAC3C,IAAI,CAAC,MACD,OAAO,EAAE;IACb,IAAI,KAAK,KAAK,EAAE,SAAS,OACrB,OAAO;QAAC;KAAK;IACjB,IAAI,eAAe,UAAU,MAAM,YAAY,WAAW;IAC1D,MAAM,QAAQ;QAAC,YAAY,CAAC,EAAE;KAAC;IAC/B,IAAI,WAAW,YAAY,CAAC,EAAE;IAC9B,MAAO,aAAa,KAAM;QACtB,eAAe,UAAU,UAAU,aAAa,MAAM,MAAM,EAAE,WAAW;QACzE,MAAM,IAAI,CAAC,YAAY,CAAC,EAAE;QAC1B,WAAW,YAAY,CAAC,EAAE;IAC9B;IACA,OAAO;AACX;AACA;;;;;;;CAOC,GACD,MAAM,oBAAoB,CAAC,MAAM;IAC7B,IAAI,QAAQ,EAAE;IACd,IAAI,aAAa;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAG;QAC9C,MAAM,OAAO,KAAK,QAAQ,CAAC,EAAE;QAC7B,IAAI,WAAW,SAAS,MAAM,YAAY,WAAW,KAAK,IAAI;QAC9D,WAAW,iBAAiB;QAC5B,cAAc,SAAS,MAAM;QAC7B,QAAQ,MAAM,MAAM,CAAC;IACzB;IACA,QAAQ,MAAM,GAAG,CAAC,CAAC,GAAG,OAAS,kBAAkB,mBAAmB,WAAW,KAAK,IAAI,KAAK;IAC7F,OAAO,eAAe,OAAO;AACjC;AAEA;;;;;CAKC,GACD,MAAM,+BAA+B,CAAC,YAAc,CAAC;QACjD,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE;QAC3B,MAAM,QAAQ,UAAU,KAAK;QAC7B,OAAO,QAAQ,MAAM,OAAO,GAAG,QAAQ;IAC3C;AACA;;;;;CAKC,GACD,MAAM,6BAA6B,CAAC,YAAc,CAAC;QAC/C,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE;QAC3B,MAAM,SAAS,UAAU,MAAM;QAC/B,OAAO,QAAQ,MAAM,OAAO,GAAG,SAAS;IAC5C;AACA;;;;;CAKC,GACD,MAAM,sBAAsB,CAAC;IACzB,MAAM,YAAY,KAAK,KAAK;IAC5B,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE;QACjB,YAAY,2BAA2B;QACvC,aAAa,6BAA6B;QAC1C,cAAc,6BAA6B;QAC3C,eAAe,2BAA2B;IAC9C,GAAG,KAAK,KAAK;IACb,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAM;AAC3C;AACA;;;;;;;CAOC,GACD,MAAM,uBAAuB,CAAC;IAC1B,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AAEA,MAAM,gBAAgB,CAAC,MAAQ,CAAC;QAC5B,IAAI,CAAC,OACD,OAAO;QACX,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE;QAC3B,OAAO,QAAQ,MAAM,OAAO,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,IAAI,MAAM,IAAI;IACrE;AACA;;;;;CAKC,GACD,MAAM,uBAAuB,CAAC;IAC1B,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE;QACjB,qBAAqB,cAAc,KAAK,GAAG;QAC3C,sBAAsB,cAAc,KAAK,GAAG;QAC5C,yBAAyB,cAAc,KAAK,GAAG;QAC/C,wBAAwB,cAAc,KAAK,GAAG;IAClD,GAAG,KAAK,KAAK,IAAI,CAAC;IAClB,MAAM,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAM;IAChD,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,SAAS;QAAE;IAAS;AACjD;AAEA;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC,UAAU;IAC/B,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE;IAC3B,OAAO,QAAQ,MAAM,OAAO,GAAG,WAAW;AAC9C;AACA;;;;;CAKC,GACD,MAAM,cAAc,CAAC;IACjB,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM;IACpC,MAAM,iBAAkB,KAAK,KAAK,EAAE,cAAc;IAClD,MAAM,oBAAqB,KAAK,KAAK,EAAE,iBAAiB;IACxD,OAAO,aAAa,iBAAiB;AACzC;AACA;;;;;;CAMC,GACD,MAAM,2BAA2B,CAAC,MAAM;IACpC,IAAI,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE,SAClB,OAAO;IACX,IAAI,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE,SAClB,OAAO;IACX,MAAM,WAAW,YAAY;IAC7B,MAAM,SAAS,gBAAgB,UAAU,KAAK,KAAK,CAAC,MAAM;IAC1D,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK,KAAK,EAAE;QAAE;IAAO;IACrD,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAM;AAC3C;AACA;;;;;CAKC,GACD,MAAM,2BAA2B,CAAC;IAC9B,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,eAAe,CAAC,QAAU,yBAAyB,MAAM;IAC/D,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AACA;;;;;;CAMC,GACD,MAAM,uBAAuB,CAAC;IAC1B,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AAEA,MAAM,SAAS,CAAC,OAAS,CAAC,OAAS,KAAK,IAAI,KAAK;AACjD,MAAM,SAAS,OAAO,+JAAA,CAAA,OAAM;AAC5B,MAAM,SAAS,OAAO,+JAAA,CAAA,OAAM;AAC5B,MAAM,iBAAiB,OAAO,+JAAA,CAAA,eAAc;AAC5C;;;;;CAKC,GACD,MAAM,gBAAgB,CAAC,OAAS,YAAY,KAAK,KAAK;AACtD;;;;;CAKC,GACD,MAAM,aAAa,CAAC,OAAS,OAAO,SAAS,eAAe;AAC5D;;;;;CAKC,GACD,MAAM,aAAa,CAAC;IAChB,MAAM,WAAW,KAAK,QAAQ,IAAI,EAAE;IACpC,4BAA4B;IAC5B,IAAI,SAAS,KAAK,CAAC,iBACf,OAAO;IACX,0BAA0B;IAC1B,IAAI,SAAS,KAAK,CAAC,SACf,OAAO;IACX,OAAO,SAAS,KAAK,CAAC;AAC1B;AACA;;;;;CAKC,GACD,MAAM,WAAW,CAAC;IACd,MAAM,cAAc;QAChB,MAAM,+JAAA,CAAA,OAAM;QACZ,OAAO,CAAC;QACR,OAAO,CAAC;QACR,KAAK,CAAC;QACN,UAAU,KAAK,QAAQ;IAC3B;IACA,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE,UAAU;YAAC;SAAY;IAAC;AAC7D;AACA,MAAM,gBAAgB,CAAC;IACnB,IAAI,CAAC,OAAO,OACR,OAAO;IACX,kEAAkE;IAClE,kEAAkE;IAClE,IAAI,cAAc,OACd,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE,MAAM,+JAAA,CAAA,OAAM;IAAC;IAClD,oEAAoE;IACpE,wDAAwD;IACxD,IAAI,WAAW,OACX,OAAO,SAAS;IACpB,OAAO;AACX;AACA;;;;;CAKC,GACD,MAAM,0BAA0B,CAAC;IAC7B,IAAI,CAAC,KAAK,QAAQ,EACd,OAAO;IACX,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,eAAe;IAC5C,MAAM,WAAW,KAAK,QAAQ,CAAC,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAS;AAC9C;AAEA,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,eAAe,eAAe,eAAe,mBAAmB,mBAAmB,sBAAsB,mBAAmB,YAAY,eAAe,oBAAoB,sBAAsB,sBAAsB,eAAe,yBAAyB,kBAAkB,kBAAkB", "ignoreList": [0], "debugId": null}}]}