# Frontend API Configuration - Network Accessible
NEXT_PUBLIC_API_URL=http://192.168.100.31:3001
NEXT_PUBLIC_API_BASE_URL=http://192.168.100.31:3001/api

# Backend URL for server-side requests (if needed)
BACKEND_URL=http://192.168.100.31:3001

# Development Settings
NODE_ENV=development

# Auth Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Supabase Configuration - EMERGENCY SECURITY IMPLEMENTATION
NEXT_PUBLIC_SUPABASE_URL=https://abylqjnpaegeqwktcukn.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTM0NTMsImV4cCI6MjA2Mjc4OTQ1M30.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o


# Debug Component Configuration
# Set these to 'true' to enable specific debug components

# Token Refresh Debug Component
NEXT_PUBLIC_SHOW_TOKEN_DEBUG=false

# CSP Debug Component  
NEXT_PUBLIC_SHOW_CSP_DEBUG=false

# Performance Monitoring Tools
NEXT_PUBLIC_SHOW_PERFORMANCE_TOOLS=false

# Performance Widgets in Reliability Dashboard
NEXT_PUBLIC_SHOW_PERFORMANCE_WIDGETS=false

# Development Tools
NEXT_PUBLIC_SHOW_DEV_TOOLS=false

# Debug Logging
NEXT_PUBLIC_ENABLE_DEBUG_LOGGING=false

# Example: To enable CSP debug only
# NEXT_PUBLIC_SHOW_CSP_DEBUG=true

# Example: To enable all debug components
# NEXT_PUBLIC_SHOW_TOKEN_DEBUG=true
# NEXT_PUBLIC_SHOW_CSP_DEBUG=true
# NEXT_PUBLIC_SHOW_PERFORMANCE_TOOLS=true
# NEXT_PUBLIC_SHOW_DEV_TOOLS=true
# NEXT_PUBLIC_ENABLE_DEBUG_LOGGING=true
NEXT_PUBLIC_ALLOWED_CSP_CONNECT_SRC="http://192.168.100.31:3001"

# WebSocket Configuration - Network Accessible
NEXT_PUBLIC_WS_URL=ws://192.168.100.31:3001