"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[87],{18271:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(40157).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},35695:(e,t,n)=>{var a=n(18999);n.o(a,"useParams")&&n.d(t,{useParams:function(){return a.useParams}}),n.o(a,"usePathname")&&n.d(t,{usePathname:function(){return a.usePathname}}),n.o(a,"useRouter")&&n.d(t,{useRouter:function(){return a.useRouter}}),n.o(a,"useSearchParams")&&n.d(t,{useSearchParams:function(){return a.useSearchParams}})},36936:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(40157).A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},46786:(e,t,n)=>{n.d(t,{KU:()=>d,Zr:()=>h,eh:()=>u,lt:()=>i});let a=new Map,r=e=>{let t=a.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},o=(e,t,n)=>{if(void 0===e)return{type:"untracked",connection:t.connect(n)};let r=a.get(n.name);if(r)return{type:"tracked",store:e,...r};let o={connection:t.connect(n),stores:{}};return a.set(n.name,o),{type:"tracked",store:e,...o}},l=(e,t)=>{if(void 0===t)return;let n=a.get(e);n&&(delete n.stores[t],0===Object.keys(n.stores).length&&a.delete(e))},s=e=>{var t,n;if(!e)return;let a=e.split("\n"),r=a.findIndex(e=>e.includes("api.setState"));if(r<0)return;let o=(null==(t=a[r+1])?void 0:t.trim())||"";return null==(n=/.+ (.+) .+/.exec(o))?void 0:n[1]},i=(e,t={})=>(n,a,i)=>{let u,{enabled:d,anonymousActionType:m,store:h,...v}=t;try{u=(null==d||d)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!u)return e(n,a,i);let{connection:y,...f}=o(h,u,v),p=!0;i.setState=(e,t,o)=>{let l=n(e,t);if(!p)return l;let c=s(Error().stack),u=void 0===o?{type:m||c||"anonymous"}:"string"==typeof o?{type:o}:o;return void 0===h?null==y||y.send(u,a()):null==y||y.send({...u,type:`${h}/${u.type}`},{...r(v.name),[h]:i.getState()}),l},i.devtools={cleanup:()=>{y&&"function"==typeof y.unsubscribe&&y.unsubscribe(),l(v.name,h)}};let g=(...e)=>{let t=p;p=!1,n(...e),p=t},S=e(i.setState,a,i);if("untracked"===f.type?null==y||y.init(S):(f.stores[f.store]=i,null==y||y.init(Object.fromEntries(Object.entries(f.stores).map(([e,t])=>[e,e===f.store?S:t.getState()])))),i.dispatchFromDevtools&&"function"==typeof i.dispatch){let e=!1,t=i.dispatch;i.dispatch=(...n)=>{"__setState"!==n[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...n)}}return y.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload)return void console.error("[zustand devtools middleware] Unsupported action format");return c(e.payload,e=>{if("__setState"===e.type){if(void 0===h)return void g(e.state);1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[h];return void(null==t||JSON.stringify(i.getState())!==JSON.stringify(t)&&g(t))}i.dispatchFromDevtools&&"function"==typeof i.dispatch&&i.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(g(S),void 0===h)return null==y?void 0:y.init(i.getState());return null==y?void 0:y.init(r(v.name));case"COMMIT":if(void 0===h){null==y||y.init(i.getState());break}return null==y?void 0:y.init(r(v.name));case"ROLLBACK":return c(e.state,e=>{if(void 0===h){g(e),null==y||y.init(i.getState());return}g(e[h]),null==y||y.init(r(v.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return c(e.state,e=>{if(void 0===h)return void g(e);JSON.stringify(i.getState())!==JSON.stringify(e[h])&&g(e[h])});case"IMPORT_STATE":{let{nextLiftedState:n}=e.payload,a=null==(t=n.computedStates.slice(-1)[0])?void 0:t.state;if(!a)return;void 0===h?g(a):g(a[h]),null==y||y.send(null,n);break}case"PAUSE_RECORDING":return p=!p}return}}),S},c=(e,t)=>{let n;try{n=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==n&&t(n)},u=e=>(t,n,a)=>{let r=a.subscribe;return a.subscribe=(e,t,n)=>{let o=e;if(t){let r=(null==n?void 0:n.equalityFn)||Object.is,l=e(a.getState());o=n=>{let a=e(n);if(!r(l,a)){let e=l;t(l=a,e)}},(null==n?void 0:n.fireImmediately)&&t(l,l)}return r(o)},e(t,n,a)};function d(e,t){let n;try{n=e()}catch(e){return}return{getItem:e=>{var a;let r=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),o=null!=(a=n.getItem(e))?a:null;return o instanceof Promise?o.then(r):r(o)},setItem:(e,a)=>n.setItem(e,JSON.stringify(a,null==t?void 0:t.replacer)),removeItem:e=>n.removeItem(e)}}let m=e=>t=>{try{let n=e(t);if(n instanceof Promise)return n;return{then:e=>m(e)(n),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>m(t)(e)}}},h=(e,t)=>(n,a,r)=>{let o,l={storage:d(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},s=!1,i=new Set,c=new Set,u=l.storage;if(!u)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${l.name}', the given storage is currently unavailable.`),n(...e)},a,r);let h=()=>{let e=l.partialize({...a()});return u.setItem(l.name,{state:e,version:l.version})},v=r.setState;r.setState=(e,t)=>{v(e,t),h()};let y=e((...e)=>{n(...e),h()},a,r);r.getInitialState=()=>y;let f=()=>{var e,t;if(!u)return;s=!1,i.forEach(e=>{var t;return e(null!=(t=a())?t:y)});let r=(null==(t=l.onRehydrateStorage)?void 0:t.call(l,null!=(e=a())?e:y))||void 0;return m(u.getItem.bind(u))(l.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===l.version)return[!1,e.state];else{if(l.migrate){let t=l.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[r,s]=e;if(n(o=l.merge(s,null!=(t=a())?t:y),!0),r)return h()}).then(()=>{null==r||r(o,void 0),o=a(),s=!0,c.forEach(e=>e(o))}).catch(e=>{null==r||r(void 0,e)})};return r.persist={setOptions:e=>{l={...l,...e},e.storage&&(u=e.storage)},clearStorage:()=>{null==u||u.removeItem(l.name)},getOptions:()=>l,rehydrate:()=>f(),hasHydrated:()=>s,onHydrate:e=>(i.add(e),()=>{i.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},l.skipHydration||f(),o||y}},51362:(e,t,n)=>{n.d(t,{D:()=>i,N:()=>c});var a=n(12115),r=["light","dark"],o="(prefers-color-scheme: dark)",l=a.createContext(void 0),s={setTheme:e=>{},themes:[]},i=()=>{var e;return null!=(e=a.useContext(l))?e:s},c=e=>a.useContext(l)?e.children:a.createElement(d,{...e}),u=["light","dark"],d=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:s=!0,enableColorScheme:i=!0,storageKey:c="theme",themes:d=u,defaultTheme:f=s?"system":"light",attribute:p="data-theme",value:g,children:S,nonce:b}=e,[k,w]=a.useState(()=>h(c,f)),[O,E]=a.useState(()=>h(c)),T=g?Object.values(g):d,_=a.useCallback(e=>{let t=e;if(!t)return;"system"===e&&s&&(t=y());let a=g?g[t]:t,o=n?v():null,l=document.documentElement;if("class"===p?(l.classList.remove(...T),a&&l.classList.add(a)):a?l.setAttribute(p,a):l.removeAttribute(p),i){let e=r.includes(f)?f:null,n=r.includes(t)?t:e;l.style.colorScheme=n}null==o||o()},[]),I=a.useCallback(e=>{let t="function"==typeof e?e(e):e;w(t);try{localStorage.setItem(c,t)}catch(e){}},[t]),C=a.useCallback(e=>{E(y(e)),"system"===k&&s&&!t&&_("system")},[k,t]);a.useEffect(()=>{let e=window.matchMedia(o);return e.addListener(C),C(e),()=>e.removeListener(C)},[C]),a.useEffect(()=>{let e=e=>{e.key===c&&I(e.newValue||f)};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[I]),a.useEffect(()=>{_(null!=t?t:k)},[t,k]);let x=a.useMemo(()=>({theme:k,setTheme:I,forcedTheme:t,resolvedTheme:"system"===k?O:k,themes:s?[...d,"system"]:d,systemTheme:s?O:void 0}),[k,I,t,O,s,d]);return a.createElement(l.Provider,{value:x},a.createElement(m,{forcedTheme:t,disableTransitionOnChange:n,enableSystem:s,enableColorScheme:i,storageKey:c,themes:d,defaultTheme:f,attribute:p,value:g,children:S,attrs:T,nonce:b}),S)},m=a.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:l,enableSystem:s,enableColorScheme:i,defaultTheme:c,value:u,attrs:d,nonce:m}=e,h="system"===c,v="class"===l?"var d=document.documentElement,c=d.classList;".concat("c.remove(".concat(d.map(e=>"'".concat(e,"'")).join(","),")"),";"):"var d=document.documentElement,n='".concat(l,"',s='setAttribute';"),y=i?(r.includes(c)?c:null)?"if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'".concat(c,"'"):"if(e==='light'||e==='dark')d.style.colorScheme=e":"",f=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=u?u[e]:e,o=t?e+"|| ''":"'".concat(a,"'"),s="";return i&&n&&!t&&r.includes(e)&&(s+="d.style.colorScheme = '".concat(e,"';")),"class"===l?t||a?s+="c.add(".concat(o,")"):s+="null":a&&(s+="d[s](n,".concat(o,")")),s},p=t?"!function(){".concat(v).concat(f(t),"}()"):s?"!function(){try{".concat(v,"var e=localStorage.getItem('").concat(n,"');if('system'===e||(!e&&").concat(h,")){var t='").concat(o,"',m=window.matchMedia(t);if(m.media!==t||m.matches){").concat(f("dark"),"}else{").concat(f("light"),"}}else if(e){").concat(u?"var x=".concat(JSON.stringify(u),";"):"").concat(f(u?"x[e]":"e",!0),"}").concat(h?"":"else{"+f(c,!1,!1)+"}").concat(y,"}catch(e){}}()"):"!function(){try{".concat(v,"var e=localStorage.getItem('").concat(n,"');if(e){").concat(u?"var x=".concat(JSON.stringify(u),";"):"").concat(f(u?"x[e]":"e",!0),"}else{").concat(f(c,!1,!1),";}").concat(y,"}catch(t){}}();");return a.createElement("script",{nonce:m,dangerouslySetInnerHTML:{__html:p}})}),h=(e,t)=>{let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},v=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},y=e=>(e||(e=window.matchMedia(o)),e.matches?"dark":"light")},65453:(e,t,n)=>{n.d(t,{v:()=>i});var a=n(12115);let r=e=>{let t,n=new Set,a=(e,a)=>{let r="function"==typeof e?e(t):e;if(!Object.is(r,t)){let e=t;t=(null!=a?a:"object"!=typeof r||null===r)?r:Object.assign({},t,r),n.forEach(n=>n(t,e))}},r=()=>t,o={setState:a,getState:r,getInitialState:()=>l,subscribe:e=>(n.add(e),()=>n.delete(e))},l=t=e(a,r,o);return o},o=e=>e?r(e):r,l=e=>e,s=e=>{let t=o(e),n=e=>(function(e,t=l){let n=a.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return a.useDebugValue(n),n})(t,e);return Object.assign(n,t),n},i=e=>e?s(e):s},68027:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(40157).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},95120:(e,t,n)=>{n.d(t,{A:()=>a});let a=(0,n(40157).A)("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])}}]);