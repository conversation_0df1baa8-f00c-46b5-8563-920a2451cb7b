"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8247],{10530:(e,t,s)=>{s.d(t,{vt:()=>w});var r=s(52991),n=s(30864),a=s(39249),l=s(51616),o=s(98381),i=s(36154),u=s(65927),c=s(5706),h=s(50884),f=s(51347);function p(e,t,s,r){Object.defineProperty(e,t,{get:s,set:r,enumerable:!0,configurable:!0})}function d(e){return e&&e.__esModule?e.default:e}var g={};p(g,"logErrors",()=>m),p(g,"registerFormat",()=>y),p(g,"create",()=>w),p(g,"defaultLanguage",()=>v),p(g,"setDefaultLanguage",()=>_);let m=!1,b=[];function y(e){b.push(e)}function w(e,t){for(let s=0;s<b.length;s++){let n=b[s];if(n.probe(e)){let s=new n(new(0,r.HI)(e));if(t)return s.getFont(t);return s}}throw Error("Unknown font format")}let v="en";function _(e="en"){v=e}function C(e,t,s){if(s.get){let e=s.get;s.get=function(){let s=e.call(this);return Object.defineProperty(this,t,{value:s}),s}}else if("function"==typeof s.value){let e=s.value;return{get(){let s=new Map;function r(...t){let n=t.length>0?t[0]:"value";if(s.has(n))return s.get(n);let a=e.apply(this,t);return s.set(n,a),a}return Object.defineProperty(this,t,{value:r}),r}}}}let x=new r._k({firstCode:r.oe,entryCount:r.oe,idDelta:r.l8,idRangeOffset:r.oe}),S=new r._k({startCharCode:r.S8,endCharCode:r.S8,glyphID:r.S8}),k=new r._k({startUnicodeValue:r.hv,additionalCount:r.hV}),I=new r._k({unicodeValue:r.hv,glyphID:r.oe}),P=new r.O3(k,r.S8),A=new r.O3(I,r.S8),O=new r._k({varSelector:r.hv,defaultUVS:new r.gm(r.S8,P,{type:"parent"}),nonDefaultUVS:new r.gm(r.S8,A,{type:"parent"})}),D=new r.Dc(r.oe,{0:{length:r.oe,language:r.oe,codeMap:new r.Yt(r.hV,256)},2:{length:r.oe,language:r.oe,subHeaderKeys:new r.O3(r.oe,256),subHeaderCount:e=>Math.max.apply(Math,e.subHeaderKeys),subHeaders:new r.Yt(x,"subHeaderCount"),glyphIndexArray:new r.Yt(r.oe,"subHeaderCount")},4:{length:r.oe,language:r.oe,segCountX2:r.oe,segCount:e=>e.segCountX2>>1,searchRange:r.oe,entrySelector:r.oe,rangeShift:r.oe,endCode:new r.Yt(r.oe,"segCount"),reservedPad:new r.Hb(r.oe),startCode:new r.Yt(r.oe,"segCount"),idDelta:new r.Yt(r.l8,"segCount"),idRangeOffset:new r.Yt(r.oe,"segCount"),glyphIndexArray:new r.Yt(r.oe,e=>(e.length-e._currentOffset)/2)},6:{length:r.oe,language:r.oe,firstCode:r.oe,entryCount:r.oe,glyphIndices:new r.Yt(r.oe,"entryCount")},8:{reserved:new r.Hb(r.oe),length:r.S8,language:r.oe,is32:new r.Yt(r.hV,8192),nGroups:r.S8,groups:new r.Yt(S,"nGroups")},10:{reserved:new r.Hb(r.oe),length:r.S8,language:r.S8,firstCode:r.S8,entryCount:r.S8,glyphIndices:new r.Yt(r.oe,"numChars")},12:{reserved:new r.Hb(r.oe),length:r.S8,language:r.S8,nGroups:r.S8,groups:new r.Yt(S,"nGroups")},13:{reserved:new r.Hb(r.oe),length:r.S8,language:r.S8,nGroups:r.S8,groups:new r.Yt(S,"nGroups")},14:{length:r.S8,numRecords:r.S8,varSelectors:new r.Yt(O,"numRecords")}}),T=new r._k({platformID:r.oe,encodingID:r.oe,table:new r.gm(r.S8,D,{type:"parent",lazy:!0})});var F=new r._k({version:r.oe,numSubtables:r.oe,tables:new r.O3(T,"numSubtables")}),M=new r._k({version:r.HA,revision:r.HA,checkSumAdjustment:r.S8,magicNumber:r.S8,flags:r.oe,unitsPerEm:r.oe,created:new r.O3(r.HA,2),modified:new r.O3(r.HA,2),xMin:r.l8,yMin:r.l8,xMax:r.l8,yMax:r.l8,macStyle:new r.ys(r.oe,["bold","italic","underline","outline","shadow","condensed","extended"]),lowestRecPPEM:r.oe,fontDirectionHint:r.l8,indexToLocFormat:r.l8,glyphDataFormat:r.l8}),L=new r._k({version:r.HA,ascent:r.l8,descent:r.l8,lineGap:r.l8,advanceWidthMax:r.oe,minLeftSideBearing:r.l8,minRightSideBearing:r.l8,xMaxExtent:r.l8,caretSlopeRise:r.l8,caretSlopeRun:r.l8,caretOffset:r.l8,reserved:new r.Hb(r.l8,4),metricDataFormat:r.l8,numberOfMetrics:r.oe});let G=new r._k({advance:r.oe,bearing:r.l8});var E=new r._k({metrics:new r.Yt(G,e=>e.parent.hhea.numberOfMetrics),bearings:new r.Yt(r.l8,e=>e.parent.maxp.numGlyphs-e.parent.hhea.numberOfMetrics)}),V=new r._k({version:r.HA,numGlyphs:r.oe,maxPoints:r.oe,maxContours:r.oe,maxComponentPoints:r.oe,maxComponentContours:r.oe,maxZones:r.oe,maxTwilightPoints:r.oe,maxStorage:r.oe,maxFunctionDefs:r.oe,maxInstructionDefs:r.oe,maxStackElements:r.oe,maxSizeOfInstructions:r.oe,maxComponentElements:r.oe,maxComponentDepth:r.oe});function B(e,t,s=0){return 1===e&&q[s]?q[s]:U[e][t]}let z=new Set(["x-mac-roman","x-mac-cyrillic","iso-8859-6","iso-8859-8"]),R={"x-mac-croatian":"\xc4\xc5\xc7\xc9\xd1\xd6\xdc\xe1\xe0\xe2\xe4\xe3\xe5\xe7\xe9\xe8\xea\xeb\xed\xec\xee\xef\xf1\xf3\xf2\xf4\xf6\xf5\xfa\xf9\xfb\xfc†\xb0\xa2\xa3\xa7•\xb6\xdf\xaeŠ™\xb4\xa8≠Ž\xd8∞\xb1≤≥∆\xb5∂∑∏š∫\xaa\xbaΩž\xf8\xbf\xa1\xac√ƒ≈Ć\xabČ… \xc0\xc3\xd5ŒœĐ—“”‘’\xf7◊\xa9⁄€‹›\xc6\xbb–\xb7‚„‰\xc2ć\xc1č\xc8\xcd\xce\xcf\xcc\xd3\xd4đ\xd2\xda\xdb\xd9ıˆ˜\xafπ\xcb˚\xb8\xca\xe6ˇ","x-mac-gaelic":"\xc4\xc5\xc7\xc9\xd1\xd6\xdc\xe1\xe0\xe2\xe4\xe3\xe5\xe7\xe9\xe8\xea\xeb\xed\xec\xee\xef\xf1\xf3\xf2\xf4\xf6\xf5\xfa\xf9\xfb\xfc†\xb0\xa2\xa3\xa7•\xb6\xdf\xae\xa9™\xb4\xa8≠\xc6\xd8Ḃ\xb1≤≥ḃĊċḊḋḞḟĠġṀ\xe6\xf8ṁṖṗɼƒſṠ\xab\xbb… \xc0\xc3\xd5Œœ–—“”‘’ṡẛ\xffŸṪ€‹›Ŷŷṫ\xb7Ỳỳ⁊\xc2\xca\xc1\xcb\xc8\xcd\xce\xcf\xcc\xd3\xd4♣\xd2\xda\xdb\xd9ı\xdd\xfdŴŵẄẅẀẁẂẃ","x-mac-greek":"\xc4\xb9\xb2\xc9\xb3\xd6\xdc΅\xe0\xe2\xe4΄\xa8\xe7\xe9\xe8\xea\xeb\xa3™\xee\xef•\xbd‰\xf4\xf6\xa6€\xf9\xfb\xfc†ΓΔΘΛΞΠ\xdf\xae\xa9ΣΪ\xa7≠\xb0\xb7Α\xb1≤≥\xa5ΒΕΖΗΙΚΜΦΫΨΩάΝ\xacΟΡ≈Τ\xab\xbb… ΥΧΆΈœ–―“”‘’\xf7ΉΊΌΎέήίόΏύαβψδεφγηιξκλμνοπώρστθωςχυζϊϋΐΰ\xad","x-mac-icelandic":"\xc4\xc5\xc7\xc9\xd1\xd6\xdc\xe1\xe0\xe2\xe4\xe3\xe5\xe7\xe9\xe8\xea\xeb\xed\xec\xee\xef\xf1\xf3\xf2\xf4\xf6\xf5\xfa\xf9\xfb\xfc\xdd\xb0\xa2\xa3\xa7•\xb6\xdf\xae\xa9™\xb4\xa8≠\xc6\xd8∞\xb1≤≥\xa5\xb5∂∑∏π∫\xaa\xbaΩ\xe6\xf8\xbf\xa1\xac√ƒ≈∆\xab\xbb… \xc0\xc3\xd5Œœ–—“”‘’\xf7◊\xffŸ⁄€\xd0\xf0\xde\xfe\xfd\xb7‚„‰\xc2\xca\xc1\xcb\xc8\xcd\xce\xcf\xcc\xd3\xd4\xd2\xda\xdb\xd9ıˆ˜\xaf˘˙˚\xb8˝˛ˇ","x-mac-inuit":"ᐃᐄᐅᐆᐊᐋᐱᐲᐳᐴᐸᐹᑉᑎᑏᑐᑑᑕᑖᑦᑭᑮᑯᑰᑲᑳᒃᒋᒌᒍᒎᒐᒑ\xb0ᒡᒥᒦ•\xb6ᒧ\xae\xa9™ᒨᒪᒫᒻᓂᓃᓄᓅᓇᓈᓐᓯᓰᓱᓲᓴᓵᔅᓕᓖᓗᓘᓚᓛᓪᔨᔩᔪᔫᔭ… ᔮᔾᕕᕖᕗ–—“”‘’ᕘᕙᕚᕝᕆᕇᕈᕉᕋᕌᕐᕿᖀᖁᖂᖃᖄᖅᖏᖐᖑᖒᖓᖔᖕᙱᙲᙳᙴᙵᙶᖖᖠᖡᖢᖣᖤᖥᖦᕼŁł","x-mac-ce":"\xc4Āā\xc9Ą\xd6\xdc\xe1ąČ\xe4čĆć\xe9ŹźĎ\xedďĒēĖ\xf3ė\xf4\xf6\xf5\xfaĚě\xfc†\xb0Ę\xa3\xa7•\xb6\xdf\xae\xa9™ę\xa8≠ģĮįĪ≤≥īĶ∂∑łĻļĽľĹĺŅņŃ\xac√ńŇ∆\xab\xbb… ňŐ\xd5őŌ–—“”‘’\xf7◊ōŔŕŘ‹›řŖŗŠ‚„šŚś\xc1Ťť\xcdŽžŪ\xd3\xd4ūŮ\xdaůŰűŲų\xdd\xfdķŻŁżĢˇ","x-mac-romanian":"\xc4\xc5\xc7\xc9\xd1\xd6\xdc\xe1\xe0\xe2\xe4\xe3\xe5\xe7\xe9\xe8\xea\xeb\xed\xec\xee\xef\xf1\xf3\xf2\xf4\xf6\xf5\xfa\xf9\xfb\xfc†\xb0\xa2\xa3\xa7•\xb6\xdf\xae\xa9™\xb4\xa8≠ĂȘ∞\xb1≤≥\xa5\xb5∂∑∏π∫\xaa\xbaΩăș\xbf\xa1\xac√ƒ≈∆\xab\xbb… \xc0\xc3\xd5Œœ–—“”‘’\xf7◊\xffŸ⁄€‹›Țț‡\xb7‚„‰\xc2\xca\xc1\xcb\xc8\xcd\xce\xcf\xcc\xd3\xd4\xd2\xda\xdb\xd9ıˆ˜\xaf˘˙˚\xb8˝˛ˇ","x-mac-turkish":"\xc4\xc5\xc7\xc9\xd1\xd6\xdc\xe1\xe0\xe2\xe4\xe3\xe5\xe7\xe9\xe8\xea\xeb\xed\xec\xee\xef\xf1\xf3\xf2\xf4\xf6\xf5\xfa\xf9\xfb\xfc†\xb0\xa2\xa3\xa7•\xb6\xdf\xae\xa9™\xb4\xa8≠\xc6\xd8∞\xb1≤≥\xa5\xb5∂∑∏π∫\xaa\xbaΩ\xe6\xf8\xbf\xa1\xac√ƒ≈∆\xab\xbb… \xc0\xc3\xd5Œœ–—“”‘’\xf7◊\xffŸĞğİıŞş‡\xb7‚„‰\xc2\xca\xc1\xcb\xc8\xcd\xce\xcf\xcc\xd3\xd4\xd2\xda\xdb\xd9ˆ˜\xaf˘˙˚\xb8˝˛ˇ"},N=new Map,U=[["utf-16be","utf-16be","utf-16be","utf-16be","utf-16be","utf-16be","utf-16be"],["x-mac-roman","shift-jis","big5","euc-kr","iso-8859-6","iso-8859-8","x-mac-greek","x-mac-cyrillic","x-mac-symbol","x-mac-devanagari","x-mac-gurmukhi","x-mac-gujarati","Oriya","Bengali","Tamil","Telugu","Kannada","Malayalam","Sinhalese","Burmese","Khmer","iso-8859-11","Laotian","Georgian","Armenian","gbk","Tibetan","Mongolian","Geez","x-mac-ce","Vietnamese","Sindhi"],["ascii",null,"iso-8859-1"],["symbol","utf-16be","shift-jis","gb18030","big5","euc-kr","johab",null,null,null,"utf-16be"]],q={15:"x-mac-icelandic",17:"x-mac-turkish",18:"x-mac-croatian",24:"x-mac-ce",25:"x-mac-ce",26:"x-mac-ce",27:"x-mac-ce",28:"x-mac-ce",30:"x-mac-icelandic",37:"x-mac-romanian",38:"x-mac-ce",39:"x-mac-ce",40:"x-mac-ce",143:"x-mac-inuit",146:"x-mac-gaelic"},H=[[],{0:"en",30:"fo",60:"ks",90:"rw",1:"fr",31:"fa",61:"ku",91:"rn",2:"de",32:"ru",62:"sd",92:"ny",3:"it",33:"zh",63:"bo",93:"mg",4:"nl",34:"nl-BE",64:"ne",94:"eo",5:"sv",35:"ga",65:"sa",128:"cy",6:"es",36:"sq",66:"mr",129:"eu",7:"da",37:"ro",67:"bn",130:"ca",8:"pt",38:"cz",68:"as",131:"la",9:"no",39:"sk",69:"gu",132:"qu",10:"he",40:"si",70:"pa",133:"gn",11:"ja",41:"yi",71:"or",134:"ay",12:"ar",42:"sr",72:"ml",135:"tt",13:"fi",43:"mk",73:"kn",136:"ug",14:"el",44:"bg",74:"ta",137:"dz",15:"is",45:"uk",75:"te",138:"jv",16:"mt",46:"be",76:"si",139:"su",17:"tr",47:"uz",77:"my",140:"gl",18:"hr",48:"kk",78:"km",141:"af",19:"zh-Hant",49:"az-Cyrl",79:"lo",142:"br",20:"ur",50:"az-Arab",80:"vi",143:"iu",21:"hi",51:"hy",81:"id",144:"gd",22:"th",52:"ka",82:"tl",145:"gv",23:"ko",53:"mo",83:"ms",146:"ga",24:"lt",54:"ky",84:"ms-Arab",147:"to",25:"pl",55:"tg",85:"am",148:"el-polyton",26:"hu",56:"tk",86:"ti",149:"kl",27:"es",57:"mn-CN",87:"om",150:"az",28:"lv",58:"mn",88:"so",151:"nn",29:"se",59:"ps",89:"sw"},[],{1078:"af",16393:"en-IN",1159:"rw",1074:"tn",1052:"sq",6153:"en-IE",1089:"sw",1115:"si",1156:"gsw",8201:"en-JM",1111:"kok",1051:"sk",1118:"am",17417:"en-MY",1042:"ko",1060:"sl",5121:"ar-DZ",5129:"en-NZ",1088:"ky",11274:"es-AR",15361:"ar-BH",13321:"en-PH",1108:"lo",16394:"es-BO",3073:"ar",18441:"en-SG",1062:"lv",13322:"es-CL",2049:"ar-IQ",7177:"en-ZA",1063:"lt",9226:"es-CO",11265:"ar-JO",11273:"en-TT",2094:"dsb",5130:"es-CR",13313:"ar-KW",2057:"en-GB",1134:"lb",7178:"es-DO",12289:"ar-LB",1033:"en",1071:"mk",12298:"es-EC",4097:"ar-LY",12297:"en-ZW",2110:"ms-BN",17418:"es-SV",6145:"ary",1061:"et",1086:"ms",4106:"es-GT",8193:"ar-OM",1080:"fo",1100:"ml",18442:"es-HN",16385:"ar-QA",1124:"fil",1082:"mt",2058:"es-MX",1025:"ar-SA",1035:"fi",1153:"mi",19466:"es-NI",10241:"ar-SY",2060:"fr-BE",1146:"arn",6154:"es-PA",7169:"aeb",3084:"fr-CA",1102:"mr",15370:"es-PY",14337:"ar-AE",1036:"fr",1148:"moh",10250:"es-PE",9217:"ar-YE",5132:"fr-LU",1104:"mn",20490:"es-PR",1067:"hy",6156:"fr-MC",2128:"mn-CN",3082:"es",1101:"as",4108:"fr-CH",1121:"ne",1034:"es",2092:"az-Cyrl",1122:"fy",1044:"nb",21514:"es-US",1068:"az",1110:"gl",2068:"nn",14346:"es-UY",1133:"ba",1079:"ka",1154:"oc",8202:"es-VE",1069:"eu",3079:"de-AT",1096:"or",2077:"sv-FI",1059:"be",1031:"de",1123:"ps",1053:"sv",2117:"bn",5127:"de-LI",1045:"pl",1114:"syr",1093:"bn-IN",4103:"de-LU",1046:"pt",1064:"tg",8218:"bs-Cyrl",2055:"de-CH",2070:"pt-PT",2143:"tzm",5146:"bs",1032:"el",1094:"pa",1097:"ta",1150:"br",1135:"kl",1131:"qu-BO",1092:"tt",1026:"bg",1095:"gu",2155:"qu-EC",1098:"te",1027:"ca",1128:"ha",3179:"qu",1054:"th",3076:"zh-HK",1037:"he",1048:"ro",1105:"bo",5124:"zh-MO",1081:"hi",1047:"rm",1055:"tr",2052:"zh",1038:"hu",1049:"ru",1090:"tk",4100:"zh-SG",1039:"is",9275:"smn",1152:"ug",1028:"zh-TW",1136:"ig",4155:"smj-NO",1058:"uk",1155:"co",1057:"id",5179:"smj",1070:"hsb",1050:"hr",1117:"iu",3131:"se-FI",1056:"ur",4122:"hr-BA",2141:"iu-Latn",1083:"se",2115:"uz-Cyrl",1029:"cs",2108:"ga",2107:"se-SE",1091:"uz",1030:"da",1076:"xh",8251:"sms",1066:"vi",1164:"prs",1077:"zu",6203:"sma-NO",1106:"cy",1125:"dv",1040:"it",7227:"sms",1160:"wo",2067:"nl-BE",2064:"it-CH",1103:"sa",1157:"sah",1043:"nl",1041:"ja",7194:"sr-Cyrl-BA",1144:"ii",3081:"en-AU",1099:"kn",3098:"sr",1130:"yo",10249:"en-BZ",1087:"kk",6170:"sr-Latn-BA",4105:"en-CA",1107:"km",2074:"sr-Latn",9225:"en-029",1158:"quc",1132:"nso"}],j=new r._k({platformID:r.oe,encodingID:r.oe,languageID:r.oe,nameID:r.oe,length:r.oe,string:new r.gm(r.oe,new r.Qf("length",e=>B(e.platformID,e.encodingID,e.languageID)),{type:"parent",relativeTo:e=>e.parent.stringOffset,allowNull:!1})}),X=new r._k({length:r.oe,tag:new r.gm(r.oe,new r.Qf("length","utf16be"),{type:"parent",relativeTo:e=>e.stringOffset})});var Y=new r.Dc(r.oe,{0:{count:r.oe,stringOffset:r.oe,records:new r.O3(j,"count")},1:{count:r.oe,stringOffset:r.oe,records:new r.O3(j,"count"),langTagCount:r.oe,langTags:new r.O3(X,"langTagCount")}});let W=["copyright","fontFamily","fontSubfamily","uniqueSubfamily","fullName","version","postscriptName","trademark","manufacturer","designer","description","vendorURL","designerURL","license","licenseURL",null,"preferredFamily","preferredSubfamily","compatibleFull","sampleText","postscriptCIDFontName","wwsFamilyName","wwsSubfamilyName"];Y.process=function(e){var t={};for(let e of this.records){let s=H[e.platformID][e.languageID];null==s&&null!=this.langTags&&e.languageID>=32768&&(s=this.langTags[e.languageID-32768].tag),null==s&&(s=e.platformID+"-"+e.languageID);let r=e.nameID>=256?"fontFeatures":W[e.nameID]||e.nameID;null==t[r]&&(t[r]={});let n=t[r];e.nameID>=256&&(n=n[e.nameID]||(n[e.nameID]={})),("string"==typeof e.string||"string"!=typeof n[s])&&(n[s]=e.string)}this.records=t},Y.preEncode=function(){if(Array.isArray(this.records))return;this.version=0;let e=[];for(let t in this.records){let s=this.records[t];"fontFeatures"!==t&&(e.push({platformID:3,encodingID:1,languageID:1033,nameID:W.indexOf(t),length:2*s.en.length,string:s.en}),"postscriptName"===t&&e.push({platformID:1,encodingID:0,languageID:0,nameID:W.indexOf(t),length:s.en.length,string:s.en}))}this.records=e,this.count=e.length,this.stringOffset=Y.size(this,null,!1)};var Z=new r.Dc(r.oe,{header:{xAvgCharWidth:r.l8,usWeightClass:r.oe,usWidthClass:r.oe,fsType:new r.ys(r.oe,[null,"noEmbedding","viewOnly","editable",null,null,null,null,"noSubsetting","bitmapOnly"]),ySubscriptXSize:r.l8,ySubscriptYSize:r.l8,ySubscriptXOffset:r.l8,ySubscriptYOffset:r.l8,ySuperscriptXSize:r.l8,ySuperscriptYSize:r.l8,ySuperscriptXOffset:r.l8,ySuperscriptYOffset:r.l8,yStrikeoutSize:r.l8,yStrikeoutPosition:r.l8,sFamilyClass:r.l8,panose:new r.O3(r.hV,10),ulCharRange:new r.O3(r.S8,4),vendorID:new r.Qf(4),fsSelection:new r.ys(r.oe,["italic","underscore","negative","outlined","strikeout","bold","regular","useTypoMetrics","wws","oblique"]),usFirstCharIndex:r.oe,usLastCharIndex:r.oe},0:{},1:{typoAscender:r.l8,typoDescender:r.l8,typoLineGap:r.l8,winAscent:r.oe,winDescent:r.oe,codePageRange:new r.O3(r.S8,2)},2:{typoAscender:r.l8,typoDescender:r.l8,typoLineGap:r.l8,winAscent:r.oe,winDescent:r.oe,codePageRange:new r.O3(r.S8,2),xHeight:r.l8,capHeight:r.l8,defaultChar:r.oe,breakChar:r.oe,maxContent:r.oe},5:{typoAscender:r.l8,typoDescender:r.l8,typoLineGap:r.l8,winAscent:r.oe,winDescent:r.oe,codePageRange:new r.O3(r.S8,2),xHeight:r.l8,capHeight:r.l8,defaultChar:r.oe,breakChar:r.oe,maxContent:r.oe,usLowerOpticalPointSize:r.oe,usUpperOpticalPointSize:r.oe}});let J=Z.versions;J[3]=J[4]=J[2];var K=new r.Dc(r.Ql,{header:{italicAngle:r.Ql,underlinePosition:r.l8,underlineThickness:r.l8,isFixedPitch:r.S8,minMemType42:r.S8,maxMemType42:r.S8,minMemType1:r.S8,maxMemType1:r.S8},1:{},2:{numberOfGlyphs:r.oe,glyphNameIndex:new r.O3(r.oe,"numberOfGlyphs"),names:new r.O3(new r.Qf(r.hV))},2.5:{numberOfGlyphs:r.oe,offsets:new r.O3(r.hV,"numberOfGlyphs")},3:{},4:{map:new r.O3(r.S8,e=>e.parent.maxp.numGlyphs)}}),Q=new r._k({controlValues:new r.O3(r.l8)}),$=new r._k({instructions:new r.O3(r.hV)});let ee=new r.Dc("head.indexToLocFormat",{0:{offsets:new r.O3(r.oe)},1:{offsets:new r.O3(r.S8)}});ee.process=function(){if(0===this.version&&!this._processed){for(let e=0;e<this.offsets.length;e++)this.offsets[e]<<=1;this._processed=!0}},ee.preEncode=function(){if(0===this.version&&!1!==this._processed){for(let e=0;e<this.offsets.length;e++)this.offsets[e]>>>=1;this._processed=!1}};var et=new r._k({controlValueProgram:new r.O3(r.hV)}),es=new r.O3(new r.hp);class er{getCFFVersion(e){for(;e&&!e.hdrSize;)e=e.parent;return e?e.version:-1}decode(e,t){let s,n=this.getCFFVersion(t)>=2?e.readUInt32BE():e.readUInt16BE();if(0===n)return[];let a=e.readUInt8();if(1===a)s=r.hV;else if(2===a)s=r.oe;else if(3===a)s=r.hv;else if(4===a)s=r.S8;else throw Error(`Bad offset size in CFFIndex: ${a} ${e.pos}`);let l=[],o=e.pos+(n+1)*a-1,i=s.decode(e);for(let r=0;r<n;r++){let r=s.decode(e);if(null!=this.type){let s=e.pos;e.pos=o+i,t.length=r-i,l.push(this.type.decode(e,t)),e.pos=s}else l.push({offset:o+i,length:r-i});i=r}return e.pos=o+i,l}size(e,t){let s,n=2;if(0===e.length)return n;let a=this.type||new r.hp,l=1;for(let s=0;s<e.length;s++){let r=e[s];l+=a.size(r,t)}if(l<=255)s=r.hV;else if(l<=65535)s=r.oe;else if(l<=0xffffff)s=r.hv;else if(l<=0xffffffff)s=r.S8;else throw Error("Bad offset in CFFIndex");return n+(1+s.size()*(e.length+1)+(l-1))}encode(e,t,s){let n;if(e.writeUInt16BE(t.length),0===t.length)return;let a=this.type||new r.hp,l=[],o=1;for(let e of t){let t=a.size(e,s);l.push(t),o+=t}if(o<=255)n=r.hV;else if(o<=65535)n=r.oe;else if(o<=0xffffff)n=r.hv;else if(o<=0xffffffff)n=r.S8;else throw Error("Bad offset in CFFIndex");for(let t of(e.writeUInt8(n.size()),o=1,n.encode(e,o),l))o+=t,n.encode(e,o);for(let r of t)a.encode(e,r,s)}constructor(e){this.type=e}}let en=["0","1","2","3","4","5","6","7","8","9",".","E","E-",null,"-"],ea={".":10,E:11,"E-":12,"-":14};class el{static decode(e,t){if(32<=t&&t<=246)return t-139;if(247<=t&&t<=250)return(t-247)*256+e.readUInt8()+108;if(251<=t&&t<=254)return-(256*(t-251))-e.readUInt8()-108;if(28===t)return e.readInt16BE();if(29===t)return e.readInt32BE();if(30===t){let t="";for(;;){let s=e.readUInt8(),r=s>>4;if(15===r)break;t+=en[r];let n=15&s;if(15===n)break;t+=en[n]}return parseFloat(t)}return null}static size(e){return(e.forceLarge&&(e=32768),(0|e)!==e)?1+Math.ceil(((""+e).length+1)/2):-107<=e&&e<=107?1:108<=e&&e<=1131||-1131<=e&&e<=-108?2:-32768<=e&&e<=32767?3:5}static encode(e,t){let s=Number(t);if(t.forceLarge)return e.writeUInt8(29),e.writeInt32BE(s);if((0|s)!==s){e.writeUInt8(30);let t=""+s;for(let s=0;s<t.length;s+=2){let n=t[s],a=ea[n]||+n;if(s===t.length-1)var r=15;else{let e=t[s+1];var r=ea[e]||+e}e.writeUInt8(a<<4|15&r)}if(15!==r)return e.writeUInt8(240)}else if(-107<=s&&s<=107)return e.writeUInt8(s+139);else if(108<=s&&s<=1131)return s-=108,e.writeUInt8((s>>8)+247),e.writeUInt8(255&s);else if(-1131<=s&&s<=-108)return s=-s-108,e.writeUInt8((s>>8)+251),e.writeUInt8(255&s);else if(-32768<=s&&s<=32767)return e.writeUInt8(28),e.writeInt16BE(s);else return e.writeUInt8(29),e.writeInt32BE(s)}}class eo{decodeOperands(e,t,s,r){if(Array.isArray(e))return r.map((r,n)=>this.decodeOperands(e[n],t,s,[r]));if(null!=e.decode)return e.decode(t,s,r);switch(e){case"number":case"offset":case"sid":return r[0];case"boolean":return!!r[0];default:return r}}encodeOperands(e,t,s,r){if(Array.isArray(e))return r.map((r,n)=>this.encodeOperands(e[n],t,s,r)[0]);if(null!=e.encode)return e.encode(t,r,s);if("number"==typeof r)return[r];if("boolean"==typeof r)return[+r];if(Array.isArray(r))return r;else return[r]}decode(e,t){let s=e.pos+t.length,n={},a=[];for(let s in Object.defineProperties(n,{parent:{value:t},_startOffset:{value:e.pos}}),this.fields){let e=this.fields[s];n[e[1]]=e[3]}for(;e.pos<s;){let t=e.readUInt8();if(t<28){12===t&&(t=t<<8|e.readUInt8());let s=this.fields[t];if(!s)throw Error(`Unknown operator ${t}`);let l=this.decodeOperands(s[2],e,n,a);null!=l&&(l instanceof r.BG?Object.defineProperty(n,s[1],l):n[s[1]]=l),a=[]}else a.push(el.decode(e,t))}return n}size(e,t,s=!0){let r={parent:t,val:e,pointerSize:0,startOffset:t.startOffset||0},n=0;for(let t in this.fields){let s=this.fields[t],a=e[s[1]];if(!(null==a||l(a,s[3]))){for(let e of this.encodeOperands(s[2],null,r,a))n+=el.size(e);n+=(Array.isArray(s[0])?s[0]:[s[0]]).length}}return s&&(n+=r.pointerSize),n}encode(e,t,s){let r={pointers:[],startOffset:e.pos,parent:s,val:t,pointerSize:0};for(let s of(r.pointerOffset=e.pos+this.size(t,r,!1),this.ops)){let n=t[s[1]];if(!(null==n||l(n,s[3]))){for(let t of this.encodeOperands(s[2],e,r,n))el.encode(e,t);for(let t of Array.isArray(s[0])?s[0]:[s[0]])e.writeUInt8(t)}}let n=0;for(;n<r.pointers.length;){let t=r.pointers[n++];t.type.encode(e,t.val,t.parent)}}constructor(e=[]){for(let t of(this.ops=e,this.fields={},e)){let e=Array.isArray(t[0])?t[0][0]<<8|t[0][1]:t[0];this.fields[e]=t}}}class ei extends r.gm{decode(e,t,s){return this.offsetType={decode:()=>s[0]},super.decode(e,t,s)}encode(e,t,s){if(!e)return this.offsetType={size:()=>0},this.size(t,s),[new eu(0)];let r=null;return this.offsetType={encode:(e,t)=>r=t},super.encode(e,t,s),[new eu(r)]}constructor(e,t={}){null==t.type&&(t.type="global"),super(null,e,t)}}class eu{valueOf(){return this.val}constructor(e){this.val=e,this.forceLarge=!0}}class ec{static decode(e,t,s){let r=s.pop();for(;s.length>r;)s.pop()}}var eh=new eo([[6,"BlueValues","delta",null],[7,"OtherBlues","delta",null],[8,"FamilyBlues","delta",null],[9,"FamilyOtherBlues","delta",null],[[12,9],"BlueScale","number",.039625],[[12,10],"BlueShift","number",7],[[12,11],"BlueFuzz","number",1],[10,"StdHW","number",null],[11,"StdVW","number",null],[[12,12],"StemSnapH","delta",null],[[12,13],"StemSnapV","delta",null],[[12,14],"ForceBold","boolean",!1],[[12,17],"LanguageGroup","number",0],[[12,18],"ExpansionFactor","number",.06],[[12,19],"initialRandomSeed","number",0],[20,"defaultWidthX","number",0],[21,"nominalWidthX","number",0],[22,"vsindex","number",0],[23,"blend",ec,null],[19,"Subrs",new ei(new er,{type:"local"}),null]]),ef=[".notdef","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","endash","dagger","daggerdbl","periodcentered","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","questiondown","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","ring","cedilla","hungarumlaut","ogonek","caron","emdash","AE","ordfeminine","Lslash","Oslash","OE","ordmasculine","ae","dotlessi","lslash","oslash","oe","germandbls","onesuperior","logicalnot","mu","trademark","Eth","onehalf","plusminus","Thorn","onequarter","divide","brokenbar","degree","thorn","threequarters","twosuperior","registered","minus","eth","multiply","threesuperior","copyright","Aacute","Acircumflex","Adieresis","Agrave","Aring","Atilde","Ccedilla","Eacute","Ecircumflex","Edieresis","Egrave","Iacute","Icircumflex","Idieresis","Igrave","Ntilde","Oacute","Ocircumflex","Odieresis","Ograve","Otilde","Scaron","Uacute","Ucircumflex","Udieresis","Ugrave","Yacute","Ydieresis","Zcaron","aacute","acircumflex","adieresis","agrave","aring","atilde","ccedilla","eacute","ecircumflex","edieresis","egrave","iacute","icircumflex","idieresis","igrave","ntilde","oacute","ocircumflex","odieresis","ograve","otilde","scaron","uacute","ucircumflex","udieresis","ugrave","yacute","ydieresis","zcaron","exclamsmall","Hungarumlautsmall","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","twodotenleader","onedotenleader","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","commasuperior","threequartersemdash","periodsuperior","questionsmall","asuperior","bsuperior","centsuperior","dsuperior","esuperior","isuperior","lsuperior","msuperior","nsuperior","osuperior","rsuperior","ssuperior","tsuperior","ff","ffi","ffl","parenleftinferior","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","exclamdownsmall","centoldstyle","Lslashsmall","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","Dotaccentsmall","Macronsmall","figuredash","hypheninferior","Ogoneksmall","Ringsmall","Cedillasmall","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","zerosuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall","001.000","001.001","001.002","001.003","Black","Bold","Book","Light","Medium","Regular","Roman","Semibold"];let ep=["","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","","endash","dagger","daggerdbl","periodcentered","","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","","questiondown","","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","","ring","cedilla","","hungarumlaut","ogonek","caron","emdash","","","","","","","","","","","","","","","","","AE","","ordfeminine","","","","","Lslash","Oslash","OE","ordmasculine","","","","","","ae","","","","dotlessi","","","lslash","oslash","oe","germandbls"],ed=[".notdef","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","endash","dagger","daggerdbl","periodcentered","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","questiondown","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","ring","cedilla","hungarumlaut","ogonek","caron","emdash","AE","ordfeminine","Lslash","Oslash","OE","ordmasculine","ae","dotlessi","lslash","oslash","oe","germandbls","onesuperior","logicalnot","mu","trademark","Eth","onehalf","plusminus","Thorn","onequarter","divide","brokenbar","degree","thorn","threequarters","twosuperior","registered","minus","eth","multiply","threesuperior","copyright","Aacute","Acircumflex","Adieresis","Agrave","Aring","Atilde","Ccedilla","Eacute","Ecircumflex","Edieresis","Egrave","Iacute","Icircumflex","Idieresis","Igrave","Ntilde","Oacute","Ocircumflex","Odieresis","Ograve","Otilde","Scaron","Uacute","Ucircumflex","Udieresis","Ugrave","Yacute","Ydieresis","Zcaron","aacute","acircumflex","adieresis","agrave","aring","atilde","ccedilla","eacute","ecircumflex","edieresis","egrave","iacute","icircumflex","idieresis","igrave","ntilde","oacute","ocircumflex","odieresis","ograve","otilde","scaron","uacute","ucircumflex","udieresis","ugrave","yacute","ydieresis","zcaron"],eg=new r._k({reserved:new r.Hb(r.oe),reqFeatureIndex:r.oe,featureCount:r.oe,featureIndexes:new r.O3(r.oe,"featureCount")}),em=new r._k({tag:new r.Qf(4),langSys:new r.gm(r.oe,eg,{type:"parent"})}),eb=new r._k({defaultLangSys:new r.gm(r.oe,eg),count:r.oe,langSysRecords:new r.O3(em,"count")}),ey=new r._k({tag:new r.Qf(4),script:new r.gm(r.oe,eb,{type:"parent"})}),ew=new r.O3(ey,r.oe),ev=new r._k({version:r.oe,nameID:r.oe}),e_=new r._k({featureParams:new r.gm(r.oe,ev),lookupCount:r.oe,lookupListIndexes:new r.O3(r.oe,"lookupCount")}),eC=new r._k({tag:new r.Qf(4),feature:new r.gm(r.oe,e_,{type:"parent"})}),ex=new r.O3(eC,r.oe),eS=new r._k({markAttachmentType:r.hV,flags:new r.ys(r.hV,["rightToLeft","ignoreBaseGlyphs","ignoreLigatures","ignoreMarks","useMarkFilteringSet"])});function ek(e){let t=new r._k({lookupType:r.oe,flags:eS,subTableCount:r.oe,subTables:new r.O3(new r.gm(r.oe,e),"subTableCount"),markFilteringSet:new r.Xx(r.oe,e=>e.flags.flags.useMarkFilteringSet)});return new r.Yt(new r.gm(r.oe,t),r.oe)}let eI=new r._k({start:r.oe,end:r.oe,startCoverageIndex:r.oe}),eP=new r.Dc(r.oe,{1:{glyphCount:r.oe,glyphs:new r.O3(r.oe,"glyphCount")},2:{rangeCount:r.oe,rangeRecords:new r.O3(eI,"rangeCount")}}),eA=new r._k({start:r.oe,end:r.oe,class:r.oe}),eO=new r.Dc(r.oe,{1:{startGlyph:r.oe,glyphCount:r.oe,classValueArray:new r.O3(r.oe,"glyphCount")},2:{classRangeCount:r.oe,classRangeRecord:new r.O3(eA,"classRangeCount")}}),eD=new r._k({a:r.oe,b:r.oe,deltaFormat:r.oe}),eT=new r._k({sequenceIndex:r.oe,lookupListIndex:r.oe}),eF=new r._k({glyphCount:r.oe,lookupCount:r.oe,input:new r.O3(r.oe,e=>e.glyphCount-1),lookupRecords:new r.O3(eT,"lookupCount")}),eM=new r.O3(new r.gm(r.oe,eF),r.oe),eL=new r._k({glyphCount:r.oe,lookupCount:r.oe,classes:new r.O3(r.oe,e=>e.glyphCount-1),lookupRecords:new r.O3(eT,"lookupCount")}),eG=new r.O3(new r.gm(r.oe,eL),r.oe),eE=new r.Dc(r.oe,{1:{coverage:new r.gm(r.oe,eP),ruleSetCount:r.oe,ruleSets:new r.O3(new r.gm(r.oe,eM),"ruleSetCount")},2:{coverage:new r.gm(r.oe,eP),classDef:new r.gm(r.oe,eO),classSetCnt:r.oe,classSet:new r.O3(new r.gm(r.oe,eG),"classSetCnt")},3:{glyphCount:r.oe,lookupCount:r.oe,coverages:new r.O3(new r.gm(r.oe,eP),"glyphCount"),lookupRecords:new r.O3(eT,"lookupCount")}}),eV=new r._k({backtrackGlyphCount:r.oe,backtrack:new r.O3(r.oe,"backtrackGlyphCount"),inputGlyphCount:r.oe,input:new r.O3(r.oe,e=>e.inputGlyphCount-1),lookaheadGlyphCount:r.oe,lookahead:new r.O3(r.oe,"lookaheadGlyphCount"),lookupCount:r.oe,lookupRecords:new r.O3(eT,"lookupCount")}),eB=new r.O3(new r.gm(r.oe,eV),r.oe),ez=new r.Dc(r.oe,{1:{coverage:new r.gm(r.oe,eP),chainCount:r.oe,chainRuleSets:new r.O3(new r.gm(r.oe,eB),"chainCount")},2:{coverage:new r.gm(r.oe,eP),backtrackClassDef:new r.gm(r.oe,eO),inputClassDef:new r.gm(r.oe,eO),lookaheadClassDef:new r.gm(r.oe,eO),chainCount:r.oe,chainClassSet:new r.O3(new r.gm(r.oe,eB),"chainCount")},3:{backtrackGlyphCount:r.oe,backtrackCoverage:new r.O3(new r.gm(r.oe,eP),"backtrackGlyphCount"),inputGlyphCount:r.oe,inputCoverage:new r.O3(new r.gm(r.oe,eP),"inputGlyphCount"),lookaheadGlyphCount:r.oe,lookaheadCoverage:new r.O3(new r.gm(r.oe,eP),"lookaheadGlyphCount"),lookupCount:r.oe,lookupRecords:new r.O3(eT,"lookupCount")}}),eR=new r.Dv(16,"BE",14),eN=new r._k({startCoord:eR,peakCoord:eR,endCoord:eR}),eU=new r._k({axisCount:r.oe,regionCount:r.oe,variationRegions:new r.O3(new r.O3(eN,"axisCount"),"regionCount")}),eq=new r._k({shortDeltas:new r.O3(r.l8,e=>e.parent.shortDeltaCount),regionDeltas:new r.O3(r.YE,e=>e.parent.regionIndexCount-e.parent.shortDeltaCount),deltas:e=>e.shortDeltas.concat(e.regionDeltas)}),eH=new r._k({itemCount:r.oe,shortDeltaCount:r.oe,regionIndexCount:r.oe,regionIndexes:new r.O3(r.oe,"regionIndexCount"),deltaSets:new r.O3(eq,"itemCount")}),ej=new r._k({format:r.oe,variationRegionList:new r.gm(r.S8,eU),variationDataCount:r.oe,itemVariationData:new r.O3(new r.gm(r.S8,eH),"variationDataCount")}),eX=new r.Dc(r.oe,{1:{axisIndex:r.oe,axisIndex:r.oe,filterRangeMinValue:eR,filterRangeMaxValue:eR}}),eY=new r._k({conditionCount:r.oe,conditionTable:new r.O3(new r.gm(r.S8,eX),"conditionCount")}),eW=new r._k({featureIndex:r.oe,alternateFeatureTable:new r.gm(r.S8,e_,{type:"parent"})}),eZ=new r._k({version:r.Ql,substitutionCount:r.oe,substitutions:new r.O3(eW,"substitutionCount")}),eJ=new r._k({conditionSet:new r.gm(r.S8,eY,{type:"parent"}),featureTableSubstitution:new r.gm(r.S8,eZ,{type:"parent"})}),eK=new r._k({majorVersion:r.oe,minorVersion:r.oe,featureVariationRecordCount:r.S8,featureVariationRecords:new r.O3(eJ,"featureVariationRecordCount")});class eQ{decode(e,t,s){return this.predefinedOps[s[0]]?this.predefinedOps[s[0]]:this.type.decode(e,t,s)}size(e,t){return this.type.size(e,t)}encode(e,t,s){let r=this.predefinedOps.indexOf(t);return -1!==r?r:this.type.encode(e,t,s)}constructor(e,t){this.predefinedOps=e,this.type=t}}class e$ extends r.wN{decode(e){return 127&r.hV.decode(e)}constructor(){super("UInt8")}}let e0=new r._k({first:r.oe,nLeft:r.hV}),e1=new r._k({first:r.oe,nLeft:r.oe}),e2=new eQ([ep,["","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","space","exclamsmall","Hungarumlautsmall","","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","twodotenleader","onedotenleader","comma","hyphen","period","fraction","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","colon","semicolon","commasuperior","threequartersemdash","periodsuperior","questionsmall","","asuperior","bsuperior","centsuperior","dsuperior","esuperior","","","isuperior","","","lsuperior","msuperior","nsuperior","osuperior","","","rsuperior","ssuperior","tsuperior","","ff","fi","fl","ffi","ffl","parenleftinferior","","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","exclamdownsmall","centoldstyle","Lslashsmall","","","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","","Dotaccentsmall","","","Macronsmall","","","figuredash","hypheninferior","","","Ogoneksmall","Ringsmall","Cedillasmall","","","","onequarter","onehalf","threequarters","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","","","zerosuperior","onesuperior","twosuperior","threesuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall"]],new ei(new r.Dc(new e$,{0:{nCodes:r.hV,codes:new r.O3(r.hV,"nCodes")},1:{nRanges:r.hV,ranges:new r.O3(e0,"nRanges")}}),{lazy:!0}));class e3 extends r.O3{decode(e,t){let s=(0,r.P0)(this.length,e,t),n=0,a=[];for(;n<s;){let s=this.type.decode(e,t);s.offset=n,n+=s.nLeft+1,a.push(s)}return a}}let e4=new eQ([ed,[".notdef","space","exclamsmall","Hungarumlautsmall","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","twodotenleader","onedotenleader","comma","hyphen","period","fraction","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","colon","semicolon","commasuperior","threequartersemdash","periodsuperior","questionsmall","asuperior","bsuperior","centsuperior","dsuperior","esuperior","isuperior","lsuperior","msuperior","nsuperior","osuperior","rsuperior","ssuperior","tsuperior","ff","fi","fl","ffi","ffl","parenleftinferior","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","exclamdownsmall","centoldstyle","Lslashsmall","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","Dotaccentsmall","Macronsmall","figuredash","hypheninferior","Ogoneksmall","Ringsmall","Cedillasmall","onequarter","onehalf","threequarters","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","zerosuperior","onesuperior","twosuperior","threesuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall"],[".notdef","space","dollaroldstyle","dollarsuperior","parenleftsuperior","parenrightsuperior","twodotenleader","onedotenleader","comma","hyphen","period","fraction","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","colon","semicolon","commasuperior","threequartersemdash","periodsuperior","asuperior","bsuperior","centsuperior","dsuperior","esuperior","isuperior","lsuperior","msuperior","nsuperior","osuperior","rsuperior","ssuperior","tsuperior","ff","fi","fl","ffi","ffl","parenleftinferior","parenrightinferior","hyphensuperior","colonmonetary","onefitted","rupiah","centoldstyle","figuredash","hypheninferior","onequarter","onehalf","threequarters","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","zerosuperior","onesuperior","twosuperior","threesuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior"]],new ei(new r.Dc(r.hV,{0:{glyphs:new r.O3(r.oe,e=>e.parent.CharStrings.length-1)},1:{ranges:new e3(e0,e=>e.parent.CharStrings.length-1)},2:{ranges:new e3(e1,e=>e.parent.CharStrings.length-1)}}),{lazy:!0})),e5=new r._k({first:r.oe,fd:r.hV}),e8=new r._k({first:r.S8,fd:r.oe}),e6=new r.Dc(r.hV,{0:{fds:new r.O3(r.hV,e=>e.parent.CharStrings.length)},3:{nRanges:r.oe,ranges:new r.O3(e5,"nRanges"),sentinel:r.oe},4:{nRanges:r.S8,ranges:new r.O3(e8,"nRanges"),sentinel:r.S8}}),e9=new ei(eh);class e7{decode(e,t,s){return t.length=s[0],e9.decode(e,t,[s[1]])}size(e,t){return[eh.size(e,t,!1),e9.size(e,t)[0]]}encode(e,t,s){return[eh.size(t,s,!1),e9.encode(e,t,s)[0]]}}let te=new eo([[18,"Private",new e7,null],[[12,38],"FontName","sid",null],[[12,7],"FontMatrix","array",[.001,0,0,.001,0,0]],[[12,5],"PaintType","number",0]]),tt=new eo([[[12,30],"ROS",["sid","sid","number"],null],[0,"version","sid",null],[1,"Notice","sid",null],[[12,0],"Copyright","sid",null],[2,"FullName","sid",null],[3,"FamilyName","sid",null],[4,"Weight","sid",null],[[12,1],"isFixedPitch","boolean",!1],[[12,2],"ItalicAngle","number",0],[[12,3],"UnderlinePosition","number",-100],[[12,4],"UnderlineThickness","number",50],[[12,5],"PaintType","number",0],[[12,6],"CharstringType","number",2],[[12,7],"FontMatrix","array",[.001,0,0,.001,0,0]],[13,"UniqueID","number",null],[5,"FontBBox","array",[0,0,0,0]],[[12,8],"StrokeWidth","number",0],[14,"XUID","array",null],[15,"charset",e4,ed],[16,"Encoding",e2,ep],[17,"CharStrings",new ei(new er),null],[18,"Private",new e7,null],[[12,20],"SyntheticBase","number",null],[[12,21],"PostScript","sid",null],[[12,22],"BaseFontName","sid",null],[[12,23],"BaseFontBlend","delta",null],[[12,31],"CIDFontVersion","number",0],[[12,32],"CIDFontRevision","number",0],[[12,33],"CIDFontType","number",0],[[12,34],"CIDCount","number",8720],[[12,35],"UIDBase","number",null],[[12,37],"FDSelect",new ei(e6),null],[[12,36],"FDArray",new ei(new er(te)),null],[[12,38],"FontName","sid",null]]),ts=new r._k({length:r.oe,itemVariationStore:ej}),tr=new eo([[[12,7],"FontMatrix","array",[.001,0,0,.001,0,0]],[17,"CharStrings",new ei(new er),null],[[12,37],"FDSelect",new ei(e6),null],[[12,36],"FDArray",new ei(new er(te)),null],[24,"vstore",new ei(ts),null],[25,"maxstack","number",193]]),tn=new r.Dc(r.ij,{1:{hdrSize:r.hV,offSize:r.hV,nameIndex:new er(new r.Qf("length")),topDictIndex:new er(tt),stringIndex:new er(new r.Qf("length")),globalSubrIndex:new er},2:{hdrSize:r.hV,length:r.oe,topDict:tr,globalSubrIndex:new er}});class ta{static decode(e){return new ta(e)}decode(){this.stream.pos;let e=tn.decode(this.stream);for(let t in e){let s=e[t];this[t]=s}if(this.version<2){if(1!==this.topDictIndex.length)throw Error("Only a single font is allowed in CFF");this.topDict=this.topDictIndex[0]}return this.isCIDFont=null!=this.topDict.ROS,this}string(e){return this.version>=2?null:e<ef.length?ef[e]:this.stringIndex[e-ef.length]}get postscriptName(){return this.version<2?this.nameIndex[0]:null}get fullName(){return this.string(this.topDict.FullName)}get familyName(){return this.string(this.topDict.FamilyName)}getCharString(e){return this.stream.pos=this.topDict.CharStrings[e].offset,this.stream.readBuffer(this.topDict.CharStrings[e].length)}getGlyphName(e){if(this.version>=2||this.isCIDFont)return null;let{charset:t}=this.topDict;if(Array.isArray(t))return t[e];if(0===e)return".notdef";switch(e-=1,t.version){case 0:return this.string(t.glyphs[e]);case 1:case 2:for(let s=0;s<t.ranges.length;s++){let r=t.ranges[s];if(r.offset<=e&&e<=r.offset+r.nLeft)return this.string(r.first+(e-r.offset))}}return null}fdForGlyph(e){if(!this.topDict.FDSelect)return null;switch(this.topDict.FDSelect.version){case 0:return this.topDict.FDSelect.fds[e];case 3:case 4:let{ranges:t}=this.topDict.FDSelect,s=0,r=t.length-1;for(;s<=r;){let n=s+r>>1;if(e<t[n].first)r=n-1;else{if(!(n<r)||!(e>=t[n+1].first))return t[n].fd;s=n+1}}default:throw Error(`Unknown FDSelect version: ${this.topDict.FDSelect.version}`)}}privateDictForGlyph(e){if(this.topDict.FDSelect){let t=this.fdForGlyph(e);return this.topDict.FDArray[t]?this.topDict.FDArray[t].Private:null}return this.version<2?this.topDict.Private:this.topDict.FDArray[0].Private}constructor(e){this.stream=e,this.decode()}}let tl=new r._k({glyphIndex:r.oe,vertOriginY:r.l8});var to=new r._k({majorVersion:r.oe,minorVersion:r.oe,defaultVertOriginY:r.l8,numVertOriginYMetrics:r.oe,metrics:new r.O3(tl,"numVertOriginYMetrics")});let ti=new r._k({height:r.hV,width:r.hV,horiBearingX:r.YE,horiBearingY:r.YE,horiAdvance:r.hV,vertBearingX:r.YE,vertBearingY:r.YE,vertAdvance:r.hV}),tu=new r._k({height:r.hV,width:r.hV,bearingX:r.YE,bearingY:r.YE,advance:r.hV}),tc=new r._k({glyph:r.oe,xOffset:r.YE,yOffset:r.YE});class th{}class tf{}new r.Dc("version",{1:{metrics:tu,data:th},2:{metrics:tu,data:tf},5:{data:tf},6:{metrics:ti,data:th},7:{metrics:ti,data:tf},8:{metrics:tu,pad:new r.Hb(r.hV),numComponents:r.oe,components:new r.O3(tc,"numComponents")},9:{metrics:ti,pad:new r.Hb(r.hV),numComponents:r.oe,components:new r.O3(tc,"numComponents")},17:{metrics:tu,dataLen:r.S8,data:new r.hp("dataLen")},18:{metrics:ti,dataLen:r.S8,data:new r.hp("dataLen")},19:{dataLen:r.S8,data:new r.hp("dataLen")}});let tp=new r._k({ascender:r.YE,descender:r.YE,widthMax:r.hV,caretSlopeNumerator:r.YE,caretSlopeDenominator:r.YE,caretOffset:r.YE,minOriginSB:r.YE,minAdvanceSB:r.YE,maxBeforeBL:r.YE,minAfterBL:r.YE,pad:new r.Hb(r.YE,2)}),td=new r._k({glyphCode:r.oe,offset:r.oe}),tg=new r.Dc(r.oe,{header:{imageFormat:r.oe,imageDataOffset:r.S8},1:{offsetArray:new r.O3(r.S8,e=>e.parent.lastGlyphIndex-e.parent.firstGlyphIndex+1)},2:{imageSize:r.S8,bigMetrics:ti},3:{offsetArray:new r.O3(r.oe,e=>e.parent.lastGlyphIndex-e.parent.firstGlyphIndex+1)},4:{numGlyphs:r.S8,glyphArray:new r.O3(td,e=>e.numGlyphs+1)},5:{imageSize:r.S8,bigMetrics:ti,numGlyphs:r.S8,glyphCodeArray:new r.O3(r.oe,"numGlyphs")}}),tm=new r._k({firstGlyphIndex:r.oe,lastGlyphIndex:r.oe,subtable:new r.gm(r.S8,tg)}),tb=new r._k({indexSubTableArray:new r.gm(r.S8,new r.O3(tm,1),{type:"parent"}),indexTablesSize:r.S8,numberOfIndexSubTables:r.S8,colorRef:r.S8,hori:tp,vert:tp,startGlyphIndex:r.oe,endGlyphIndex:r.oe,ppemX:r.hV,ppemY:r.hV,bitDepth:r.hV,flags:new r.ys(r.hV,["horizontal","vertical"])});var ty=new r._k({version:r.S8,numSizes:r.S8,sizes:new r.O3(tb,"numSizes")});let tw=new r._k({ppem:r.oe,resolution:r.oe,imageOffsets:new r.O3(new r.gm(r.S8,"void"),e=>e.parent.parent.maxp.numGlyphs+1)});var tv=new r._k({version:r.oe,flags:new r.ys(r.oe,["renderOutlines"]),numImgTables:r.S8,imageTables:new r.O3(new r.gm(r.S8,tw),"numImgTables")});let t_=new r._k({gid:r.oe,paletteIndex:r.oe}),tC=new r._k({gid:r.oe,firstLayerIndex:r.oe,numLayers:r.oe});var tx=new r._k({version:r.oe,numBaseGlyphRecords:r.oe,baseGlyphRecord:new r.gm(r.S8,new r.O3(tC,"numBaseGlyphRecords")),layerRecords:new r.gm(r.S8,new r.O3(t_,"numLayerRecords"),{lazy:!0}),numLayerRecords:r.oe});let tS=new r._k({blue:r.hV,green:r.hV,red:r.hV,alpha:r.hV});var tk=new r.Dc(r.oe,{header:{numPaletteEntries:r.oe,numPalettes:r.oe,numColorRecords:r.oe,colorRecords:new r.gm(r.S8,new r.O3(tS,"numColorRecords")),colorRecordIndices:new r.O3(r.oe,"numPalettes")},0:{},1:{offsetPaletteTypeArray:new r.gm(r.S8,new r.O3(r.S8,"numPalettes")),offsetPaletteLabelArray:new r.gm(r.S8,new r.O3(r.oe,"numPalettes")),offsetPaletteEntryLabelArray:new r.gm(r.S8,new r.O3(r.oe,"numPaletteEntries"))}});let tI=new r.Dc(r.oe,{1:{coordinate:r.l8},2:{coordinate:r.l8,referenceGlyph:r.oe,baseCoordPoint:r.oe},3:{coordinate:r.l8,deviceTable:new r.gm(r.oe,eD)}}),tP=new r._k({defaultIndex:r.oe,baseCoordCount:r.oe,baseCoords:new r.O3(new r.gm(r.oe,tI),"baseCoordCount")}),tA=new r._k({tag:new r.Qf(4),minCoord:new r.gm(r.oe,tI,{type:"parent"}),maxCoord:new r.gm(r.oe,tI,{type:"parent"})}),tO=new r._k({minCoord:new r.gm(r.oe,tI),maxCoord:new r.gm(r.oe,tI),featMinMaxCount:r.oe,featMinMaxRecords:new r.O3(tA,"featMinMaxCount")}),tD=new r._k({tag:new r.Qf(4),minMax:new r.gm(r.oe,tO,{type:"parent"})}),tT=new r._k({baseValues:new r.gm(r.oe,tP),defaultMinMax:new r.gm(r.oe,tO),baseLangSysCount:r.oe,baseLangSysRecords:new r.O3(tD,"baseLangSysCount")}),tF=new r._k({tag:new r.Qf(4),script:new r.gm(r.oe,tT,{type:"parent"})}),tM=new r.O3(tF,r.oe),tL=new r.O3(new r.Qf(4),r.oe),tG=new r._k({baseTagList:new r.gm(r.oe,tL),baseScriptList:new r.gm(r.oe,tM)});var tE=new r.Dc(r.S8,{header:{horizAxis:new r.gm(r.oe,tG),vertAxis:new r.gm(r.oe,tG)},65536:{},65537:{itemVariationStore:new r.gm(r.S8,ej)}});let tV=new r.O3(r.oe,r.oe),tB=new r._k({coverage:new r.gm(r.oe,eP),glyphCount:r.oe,attachPoints:new r.O3(new r.gm(r.oe,tV),"glyphCount")}),tz=new r.Dc(r.oe,{1:{coordinate:r.l8},2:{caretValuePoint:r.oe},3:{coordinate:r.l8,deviceTable:new r.gm(r.oe,eD)}}),tR=new r.O3(new r.gm(r.oe,tz),r.oe),tN=new r._k({coverage:new r.gm(r.oe,eP),ligGlyphCount:r.oe,ligGlyphs:new r.O3(new r.gm(r.oe,tR),"ligGlyphCount")}),tU=new r._k({markSetTableFormat:r.oe,markSetCount:r.oe,coverage:new r.O3(new r.gm(r.S8,eP),"markSetCount")});var tq=new r.Dc(r.S8,{header:{glyphClassDef:new r.gm(r.oe,eO),attachList:new r.gm(r.oe,tB),ligCaretList:new r.gm(r.oe,tN),markAttachClassDef:new r.gm(r.oe,eO)},65536:{},65538:{markGlyphSetsDef:new r.gm(r.oe,tU)},65539:{markGlyphSetsDef:new r.gm(r.oe,tU),itemVariationStore:new r.gm(r.S8,ej)}});let tH=new r.ys(r.oe,["xPlacement","yPlacement","xAdvance","yAdvance","xPlaDevice","yPlaDevice","xAdvDevice","yAdvDevice"]),tj={xPlacement:r.l8,yPlacement:r.l8,xAdvance:r.l8,yAdvance:r.l8,xPlaDevice:new r.gm(r.oe,eD,{type:"global",relativeTo:e=>e.rel}),yPlaDevice:new r.gm(r.oe,eD,{type:"global",relativeTo:e=>e.rel}),xAdvDevice:new r.gm(r.oe,eD,{type:"global",relativeTo:e=>e.rel}),yAdvDevice:new r.gm(r.oe,eD,{type:"global",relativeTo:e=>e.rel})};class tX{buildStruct(e){let t=e;for(;!t[this.key]&&t.parent;)t=t.parent;if(!t[this.key])return;let s={};s.rel=()=>t._startOffset;let n=t[this.key];for(let e in n)n[e]&&(s[e]=tj[e]);return new r._k(s)}size(e,t){return this.buildStruct(t).size(e,t)}decode(e,t){let s=this.buildStruct(t).decode(e,t);return delete s.rel,s}constructor(e="valueFormat"){this.key=e}}let tY=new r._k({secondGlyph:r.oe,value1:new tX("valueFormat1"),value2:new tX("valueFormat2")}),tW=new r.O3(tY,r.oe),tZ=new r._k({value1:new tX("valueFormat1"),value2:new tX("valueFormat2")}),tJ=new r.Dc(r.oe,{1:{xCoordinate:r.l8,yCoordinate:r.l8},2:{xCoordinate:r.l8,yCoordinate:r.l8,anchorPoint:r.oe},3:{xCoordinate:r.l8,yCoordinate:r.l8,xDeviceTable:new r.gm(r.oe,eD),yDeviceTable:new r.gm(r.oe,eD)}}),tK=new r._k({entryAnchor:new r.gm(r.oe,tJ,{type:"parent"}),exitAnchor:new r.gm(r.oe,tJ,{type:"parent"})}),tQ=new r._k({class:r.oe,markAnchor:new r.gm(r.oe,tJ,{type:"parent"})}),t$=new r.O3(tQ,r.oe),t0=new r.O3(new r.gm(r.oe,tJ),e=>e.parent.classCount),t1=new r.O3(t0,r.oe),t2=new r.O3(new r.gm(r.oe,tJ),e=>e.parent.parent.classCount),t3=new r.O3(t2,r.oe),t4=new r.O3(new r.gm(r.oe,t3),r.oe),t5=new r.Dc("lookupType",{1:new r.Dc(r.oe,{1:{coverage:new r.gm(r.oe,eP),valueFormat:tH,value:new tX},2:{coverage:new r.gm(r.oe,eP),valueFormat:tH,valueCount:r.oe,values:new r.Yt(new tX,"valueCount")}}),2:new r.Dc(r.oe,{1:{coverage:new r.gm(r.oe,eP),valueFormat1:tH,valueFormat2:tH,pairSetCount:r.oe,pairSets:new r.Yt(new r.gm(r.oe,tW),"pairSetCount")},2:{coverage:new r.gm(r.oe,eP),valueFormat1:tH,valueFormat2:tH,classDef1:new r.gm(r.oe,eO),classDef2:new r.gm(r.oe,eO),class1Count:r.oe,class2Count:r.oe,classRecords:new r.Yt(new r.Yt(tZ,"class2Count"),"class1Count")}}),3:{format:r.oe,coverage:new r.gm(r.oe,eP),entryExitCount:r.oe,entryExitRecords:new r.O3(tK,"entryExitCount")},4:{format:r.oe,markCoverage:new r.gm(r.oe,eP),baseCoverage:new r.gm(r.oe,eP),classCount:r.oe,markArray:new r.gm(r.oe,t$),baseArray:new r.gm(r.oe,t1)},5:{format:r.oe,markCoverage:new r.gm(r.oe,eP),ligatureCoverage:new r.gm(r.oe,eP),classCount:r.oe,markArray:new r.gm(r.oe,t$),ligatureArray:new r.gm(r.oe,t4)},6:{format:r.oe,mark1Coverage:new r.gm(r.oe,eP),mark2Coverage:new r.gm(r.oe,eP),classCount:r.oe,mark1Array:new r.gm(r.oe,t$),mark2Array:new r.gm(r.oe,t1)},7:eE,8:ez,9:{posFormat:r.oe,lookupType:r.oe,extension:new r.gm(r.S8,null)}});t5.versions[9].extension.type=t5;var t8=new r.Dc(r.S8,{header:{scriptList:new r.gm(r.oe,ew),featureList:new r.gm(r.oe,ex),lookupList:new r.gm(r.oe,new ek(t5))},65536:{},65537:{featureVariations:new r.gm(r.S8,eK)}});let t6=new r.O3(r.oe,r.oe),t9=new r._k({glyph:r.oe,compCount:r.oe,components:new r.O3(r.oe,e=>e.compCount-1)}),t7=new r.O3(new r.gm(r.oe,t9),r.oe),se=new r.Dc("lookupType",{1:new r.Dc(r.oe,{1:{coverage:new r.gm(r.oe,eP),deltaGlyphID:r.l8},2:{coverage:new r.gm(r.oe,eP),glyphCount:r.oe,substitute:new r.Yt(r.oe,"glyphCount")}}),2:{substFormat:r.oe,coverage:new r.gm(r.oe,eP),count:r.oe,sequences:new r.Yt(new r.gm(r.oe,t6),"count")},3:{substFormat:r.oe,coverage:new r.gm(r.oe,eP),count:r.oe,alternateSet:new r.Yt(new r.gm(r.oe,t6),"count")},4:{substFormat:r.oe,coverage:new r.gm(r.oe,eP),count:r.oe,ligatureSets:new r.Yt(new r.gm(r.oe,t7),"count")},5:eE,6:ez,7:{substFormat:r.oe,lookupType:r.oe,extension:new r.gm(r.S8,null)},8:{substFormat:r.oe,coverage:new r.gm(r.oe,eP),backtrackCoverage:new r.O3(new r.gm(r.oe,eP),"backtrackGlyphCount"),lookaheadGlyphCount:r.oe,lookaheadCoverage:new r.O3(new r.gm(r.oe,eP),"lookaheadGlyphCount"),glyphCount:r.oe,substitutes:new r.O3(r.oe,"glyphCount")}});se.versions[7].extension.type=se;var st=new r.Dc(r.S8,{header:{scriptList:new r.gm(r.oe,ew),featureList:new r.gm(r.oe,ex),lookupList:new r.gm(r.oe,new ek(se))},65536:{},65537:{featureVariations:new r.gm(r.S8,eK)}});let ss=new r.O3(r.oe,r.oe),sr=new r._k({shrinkageEnableGSUB:new r.gm(r.oe,ss),shrinkageDisableGSUB:new r.gm(r.oe,ss),shrinkageEnableGPOS:new r.gm(r.oe,ss),shrinkageDisableGPOS:new r.gm(r.oe,ss),shrinkageJstfMax:new r.gm(r.oe,new ek(t5)),extensionEnableGSUB:new r.gm(r.oe,ss),extensionDisableGSUB:new r.gm(r.oe,ss),extensionEnableGPOS:new r.gm(r.oe,ss),extensionDisableGPOS:new r.gm(r.oe,ss),extensionJstfMax:new r.gm(r.oe,new ek(t5))}),sn=new r.O3(new r.gm(r.oe,sr),r.oe),sa=new r._k({tag:new r.Qf(4),jstfLangSys:new r.gm(r.oe,sn)}),sl=new r._k({extenderGlyphs:new r.gm(r.oe,new r.O3(r.oe,r.oe)),defaultLangSys:new r.gm(r.oe,sn),langSysCount:r.oe,langSysRecords:new r.O3(sa,"langSysCount")}),so=new r._k({tag:new r.Qf(4),script:new r.gm(r.oe,sl,{type:"parent"})});var si=new r._k({version:r.S8,scriptCount:r.oe,scriptList:new r.O3(so,"scriptCount")});class su{decode(e,t){switch(this.size(0,t)){case 1:return e.readUInt8();case 2:return e.readUInt16BE();case 3:return e.readUInt24BE();case 4:return e.readUInt32BE()}}size(e,t){return(0,r.P0)(this._size,null,t)}constructor(e){this._size=e}}let sc=new r._k({entry:new su(e=>((48&e.parent.entryFormat)>>4)+1),outerIndex:e=>e.entry>>(15&e.parent.entryFormat)+1,innerIndex:e=>e.entry&(1<<(15&e.parent.entryFormat)+1)-1}),sh=new r._k({entryFormat:r.oe,mapCount:r.oe,mapData:new r.O3(sc,"mapCount")});var sf=new r._k({majorVersion:r.oe,minorVersion:r.oe,itemVariationStore:new r.gm(r.S8,ej),advanceWidthMapping:new r.gm(r.S8,sh),LSBMapping:new r.gm(r.S8,sh),RSBMapping:new r.gm(r.S8,sh)});let sp=new r._k({format:r.S8,length:r.S8,offset:r.S8}),sd=new r._k({reserved:new r.Hb(r.oe,2),cbSignature:r.S8,signature:new r.hp("cbSignature")});var sg=new r._k({ulVersion:r.S8,usNumSigs:r.oe,usFlag:r.oe,signatures:new r.O3(sp,"usNumSigs"),signatureBlocks:new r.O3(sd,"usNumSigs")});let sm=new r._k({rangeMaxPPEM:r.oe,rangeGaspBehavior:new r.ys(r.oe,["grayscale","gridfit","symmetricSmoothing","symmetricGridfit"])});var sb=new r._k({version:r.oe,numRanges:r.oe,gaspRanges:new r.O3(sm,"numRanges")});let sy=new r._k({pixelSize:r.hV,maximumWidth:r.hV,widths:new r.O3(r.hV,e=>e.parent.parent.maxp.numGlyphs)});var sw=new r._k({version:r.oe,numRecords:r.l8,sizeDeviceRecord:r.HA,records:new r.O3(sy,"numRecords")});let sv=new r._k({left:r.oe,right:r.oe,value:r.l8}),s_=new r._k({firstGlyph:r.oe,nGlyphs:r.oe,offsets:new r.O3(r.oe,"nGlyphs"),max:e=>e.offsets.length&&Math.max.apply(Math,e.offsets)}),sC=new r._k({off:e=>e._startOffset-e.parent.parent._startOffset,len:e=>((e.parent.leftTable.max-e.off)/e.parent.rowWidth+1)*(e.parent.rowWidth/2),values:new r.Yt(r.l8,"len")}),sx=new r.Dc("format",{0:{nPairs:r.oe,searchRange:r.oe,entrySelector:r.oe,rangeShift:r.oe,pairs:new r.O3(sv,"nPairs")},2:{rowWidth:r.oe,leftTable:new r.gm(r.oe,s_,{type:"parent"}),rightTable:new r.gm(r.oe,s_,{type:"parent"}),array:new r.gm(r.oe,sC,{type:"parent"})},3:{glyphCount:r.oe,kernValueCount:r.hV,leftClassCount:r.hV,rightClassCount:r.hV,flags:r.hV,kernValue:new r.O3(r.l8,"kernValueCount"),leftClass:new r.O3(r.hV,"glyphCount"),rightClass:new r.O3(r.hV,"glyphCount"),kernIndex:new r.O3(r.hV,e=>e.leftClassCount*e.rightClassCount)}}),sS=new r.Dc("version",{0:{subVersion:r.oe,length:r.oe,format:r.hV,coverage:new r.ys(r.hV,["horizontal","minimum","crossStream","override"]),subtable:sx,padding:new r.Hb(r.hV,e=>e.length-e._currentOffset)},1:{length:r.S8,coverage:new r.ys(r.hV,[null,null,null,null,null,"variation","crossStream","vertical"]),format:r.hV,tupleIndex:r.oe,subtable:sx,padding:new r.Hb(r.hV,e=>e.length-e._currentOffset)}});var sk=new r.Dc(r.oe,{0:{nTables:r.oe,tables:new r.O3(sS,"nTables")},1:{reserved:new r.Hb(r.oe),nTables:r.S8,tables:new r.O3(sS,"nTables")}}),sI=new r._k({version:r.oe,numGlyphs:r.oe,yPels:new r.O3(r.hV,"numGlyphs")}),sP=new r._k({version:r.oe,fontNumber:r.S8,pitch:r.oe,xHeight:r.oe,style:r.oe,typeFamily:r.oe,capHeight:r.oe,symbolSet:r.oe,typeface:new r.Qf(16),characterComplement:new r.Qf(8),fileName:new r.Qf(6),strokeWeight:new r.Qf(1),widthType:new r.Qf(1),serifStyle:r.hV,reserved:new r.Hb(r.hV)});let sA=new r._k({bCharSet:r.hV,xRatio:r.hV,yStartRatio:r.hV,yEndRatio:r.hV}),sO=new r._k({yPelHeight:r.oe,yMax:r.l8,yMin:r.l8}),sD=new r._k({recs:r.oe,startsz:r.hV,endsz:r.hV,entries:new r.O3(sO,"recs")});var sT=new r._k({version:r.oe,numRecs:r.oe,numRatios:r.oe,ratioRanges:new r.O3(sA,"numRatios"),offsets:new r.O3(r.oe,"numRatios"),groups:new r.O3(sD,"numRecs")}),sF=new r._k({version:r.oe,ascent:r.l8,descent:r.l8,lineGap:r.l8,advanceHeightMax:r.l8,minTopSideBearing:r.l8,minBottomSideBearing:r.l8,yMaxExtent:r.l8,caretSlopeRise:r.l8,caretSlopeRun:r.l8,caretOffset:r.l8,reserved:new r.Hb(r.l8,4),metricDataFormat:r.l8,numberOfMetrics:r.oe});let sM=new r._k({advance:r.oe,bearing:r.l8});var sL=new r._k({metrics:new r.Yt(sM,e=>e.parent.vhea.numberOfMetrics),bearings:new r.Yt(r.l8,e=>e.parent.maxp.numGlyphs-e.parent.vhea.numberOfMetrics)});let sG=new r.Dv(16,"BE",14),sE=new r._k({fromCoord:sG,toCoord:sG}),sV=new r._k({pairCount:r.oe,correspondence:new r.O3(sE,"pairCount")});var sB=new r._k({version:r.Ql,axisCount:r.S8,segment:new r.O3(sV,"axisCount")});class sz{getItem(e){if(null==this._items[e]){let t=this.stream.pos;this.stream.pos=this.base+this.type.size(null,this.parent)*e,this._items[e]=this.type.decode(this.stream,this.parent),this.stream.pos=t}return this._items[e]}inspect(){return`[UnboundedArray ${this.type.constructor.name}]`}constructor(e,t,s){this.type=e,this.stream=t,this.parent=s,this.base=this.stream.pos,this._items=[]}}class sR extends r.O3{decode(e,t){return new sz(this.type,e,t)}constructor(e){super(e,0)}}let sN=function(e=r.oe){class t{decode(e,t){return t=t.parent.parent,this.type.decode(e,t)}size(e,t){return t=t.parent.parent,this.type.size(e,t)}encode(e,t,s){return s=s.parent.parent,this.type.encode(e,t,s)}constructor(e){this.type=e}}e=new t(e);let s=new r._k({unitSize:r.oe,nUnits:r.oe,searchRange:r.oe,entrySelector:r.oe,rangeShift:r.oe}),n=new r._k({lastGlyph:r.oe,firstGlyph:r.oe,value:e}),a=new r._k({lastGlyph:r.oe,firstGlyph:r.oe,values:new r.gm(r.oe,new r.O3(e,e=>e.lastGlyph-e.firstGlyph+1),{type:"parent"})}),l=new r._k({glyph:r.oe,value:e});return new r.Dc(r.oe,{0:{values:new sR(e)},2:{binarySearchHeader:s,segments:new r.O3(n,e=>e.binarySearchHeader.nUnits)},4:{binarySearchHeader:s,segments:new r.O3(a,e=>e.binarySearchHeader.nUnits)},6:{binarySearchHeader:s,segments:new r.O3(l,e=>e.binarySearchHeader.nUnits)},8:{firstGlyph:r.oe,count:r.oe,values:new r.O3(e,"count")}})};function sU(e={},t=r.oe){let s=Object.assign({newState:r.oe,flags:r.oe},e),n=new r._k(s),a=new sR(new r.O3(r.oe,e=>e.nClasses));return new r._k({nClasses:r.S8,classTable:new r.gm(r.S8,new sN(t)),stateArray:new r.gm(r.S8,a),entryTable:new r.gm(r.S8,new sR(n))})}let sq=new r.Dc("format",{0:{deltas:new r.O3(r.l8,32)},1:{deltas:new r.O3(r.l8,32),mappingData:new sN(r.oe)},2:{standardGlyph:r.oe,controlPoints:new r.O3(r.oe,32)},3:{standardGlyph:r.oe,controlPoints:new r.O3(r.oe,32),mappingData:new sN(r.oe)}});var sH=new r._k({version:r.Ql,format:r.oe,defaultBaseline:r.oe,subtable:sq});let sj=new r._k({setting:r.oe,nameIndex:r.l8,name:e=>e.parent.parent.parent.name.records.fontFeatures[e.nameIndex]}),sX=new r._k({feature:r.oe,nSettings:r.oe,settingTable:new r.gm(r.S8,new r.O3(sj,"nSettings"),{type:"parent"}),featureFlags:new r.ys(r.hV,[null,null,null,null,null,null,"hasDefault","exclusive"]),defaultSetting:r.hV,nameIndex:r.l8,name:e=>e.parent.parent.name.records.fontFeatures[e.nameIndex]});var sY=new r._k({version:r.Ql,featureNameCount:r.oe,reserved1:new r.Hb(r.oe),reserved2:new r.Hb(r.S8),featureNames:new r.O3(sX,"featureNameCount")});let sW=new r._k({axisTag:new r.Qf(4),minValue:r.Ql,defaultValue:r.Ql,maxValue:r.Ql,flags:r.oe,nameID:r.oe,name:e=>e.parent.parent.name.records.fontFeatures[e.nameID]}),sZ=new r._k({nameID:r.oe,name:e=>e.parent.parent.name.records.fontFeatures[e.nameID],flags:r.oe,coord:new r.O3(r.Ql,e=>e.parent.axisCount),postscriptNameID:new r.Xx(r.oe,e=>e.parent.instanceSize-e._currentOffset>0)});var sJ=new r._k({version:r.Ql,offsetToData:r.oe,countSizePairs:r.oe,axisCount:r.oe,axisSize:r.oe,instanceCount:r.oe,instanceSize:r.oe,axis:new r.O3(sW,"axisCount"),instance:new r.O3(sZ,"instanceCount")});let sK=new r.Dv(16,"BE",14);class sQ{static decode(e,t){return t.flags?e.readUInt32BE():2*e.readUInt16BE()}}let s$=new r._k({version:r.oe,reserved:new r.Hb(r.oe),axisCount:r.oe,globalCoordCount:r.oe,globalCoords:new r.gm(r.S8,new r.O3(new r.O3(sK,"axisCount"),"globalCoordCount")),glyphCount:r.oe,flags:r.oe,offsetToData:r.S8,offsets:new r.O3(new r.gm(sQ,"void",{relativeTo:e=>e.offsetToData,allowNull:!1}),e=>e.glyphCount+1)}),s0=new r._k({length:r.oe,coverage:r.oe,subFeatureFlags:r.S8,stateTable:new function(e={},t=r.oe){let s=new r._k({version:()=>8,firstGlyph:r.oe,values:new r.O3(r.hV,r.oe)}),n=Object.assign({newStateOffset:r.oe,newState:e=>(e.newStateOffset-(e.parent.stateArray.base-e.parent._startOffset))/e.parent.nClasses,flags:r.oe},e),a=new r._k(n),l=new sR(new r.O3(r.hV,e=>e.nClasses));return new r._k({nClasses:r.oe,classTable:new r.gm(r.oe,s),stateArray:new r.gm(r.oe,l),entryTable:new r.gm(r.oe,new sR(a))})}}),s1=new r._k({justClass:r.S8,beforeGrowLimit:r.Ql,beforeShrinkLimit:r.Ql,afterGrowLimit:r.Ql,afterShrinkLimit:r.Ql,growFlags:r.oe,shrinkFlags:r.oe}),s2=new r.O3(s1,r.S8),s3=new r.Dc("actionType",{0:{lowerLimit:r.Ql,upperLimit:r.Ql,order:r.oe,glyphs:new r.O3(r.oe,r.oe)},1:{addGlyph:r.oe},2:{substThreshold:r.Ql,addGlyph:r.oe,substGlyph:r.oe},3:{},4:{variationAxis:r.S8,minimumLimit:r.Ql,noStretchValue:r.Ql,maximumLimit:r.Ql},5:{flags:r.oe,glyph:r.oe}}),s4=new r._k({actionClass:r.oe,actionType:r.oe,actionLength:r.S8,actionData:s3,padding:new r.Hb(r.hV,e=>e.actionLength-e._currentOffset)}),s5=new r.O3(s4,r.S8),s8=new r._k({lookupTable:new sN(new r.gm(r.oe,s5))}),s6=new r._k({classTable:new r.gm(r.oe,s0,{type:"parent"}),wdcOffset:r.oe,postCompensationTable:new r.gm(r.oe,s8,{type:"parent"}),widthDeltaClusters:new sN(new r.gm(r.oe,s2,{type:"parent",relativeTo:e=>e.wdcOffset}))});var s9=new r._k({version:r.S8,format:r.oe,horizontal:new r.gm(r.oe,s6),vertical:new r.gm(r.oe,s6)});let s7={action:r.oe},re={markIndex:r.oe,currentIndex:r.oe},rt={currentInsertIndex:r.oe,markedInsertIndex:r.oe},rs=new r._k({items:new sR(new r.gm(r.S8,new sN))}),rr=new r.Dc("type",{0:{stateTable:new sU},1:{stateTable:new sU(re),substitutionTable:new r.gm(r.S8,rs)},2:{stateTable:new sU(s7),ligatureActions:new r.gm(r.S8,new sR(r.S8)),components:new r.gm(r.S8,new sR(r.oe)),ligatureList:new r.gm(r.S8,new sR(r.oe))},4:{lookupTable:new sN},5:{stateTable:new sU(rt),insertionActions:new r.gm(r.S8,new sR(r.oe))}}),rn=new r._k({length:r.S8,coverage:r.hv,type:r.hV,subFeatureFlags:r.S8,table:rr,padding:new r.Hb(r.hV,e=>e.length-e._currentOffset)}),ra=new r._k({featureType:r.oe,featureSetting:r.oe,enableFlags:r.S8,disableFlags:r.S8}),rl=new r._k({defaultFlags:r.S8,chainLength:r.S8,nFeatureEntries:r.S8,nSubtables:r.S8,features:new r.O3(ra,"nFeatureEntries"),subtables:new r.O3(rn,"nSubtables")});var ro=new r._k({version:r.oe,unused:new r.Hb(r.oe),nChains:r.S8,chains:new r.O3(rl,"nChains")});let ri=new r._k({left:r.l8,top:r.l8,right:r.l8,bottom:r.l8});var ru=new r._k({version:r.Ql,format:r.oe,lookupTable:new sN(ri)});let rc={};rc.cmap=F,rc.head=M,rc.hhea=L,rc.hmtx=E,rc.maxp=V,rc.name=Y,rc["OS/2"]=Z,rc.post=K,rc.fpgm=$,rc.loca=ee,rc.prep=et,rc["cvt "]=Q,rc.glyf=es,rc["CFF "]=ta,rc.CFF2=ta,rc.VORG=to,rc.EBLC=ty,rc.CBLC=rc.EBLC,rc.sbix=tv,rc.COLR=tx,rc.CPAL=tk,rc.BASE=tE,rc.GDEF=tq,rc.GPOS=t8,rc.GSUB=st,rc.JSTF=si,rc.HVAR=sf,rc.DSIG=sg,rc.gasp=sb,rc.hdmx=sw,rc.kern=sk,rc.LTSH=sI,rc.PCLT=sP,rc.VDMX=sT,rc.vhea=sF,rc.vmtx=sL,rc.avar=sB,rc.bsln=sH,rc.feat=sY,rc.fvar=sJ,rc.gvar=s$,rc.just=s9,rc.morx=ro,rc.opbd=ru;let rh=new r._k({tag:new r.Qf(4),checkSum:r.S8,offset:new r.gm(r.S8,"void",{type:"global"}),length:r.S8}),rf=new r._k({tag:new r.Qf(4),numTables:r.oe,searchRange:r.oe,entrySelector:r.oe,rangeShift:r.oe,tables:new r.O3(rh,"numTables")});function rp(e,t){let s=0,r=e.length-1;for(;s<=r;){let n=s+r>>1,a=t(e[n]);if(a<0)r=n-1;else{if(!(a>0))return n;s=n+1}}return -1}function rd(e,t){let s=[];for(;e<t;)s.push(e++);return s}rf.process=function(){let e={};for(let t of this.tables)e[t.tag]=t;this.tables=e},rf.preEncode=function(){if(!Array.isArray(this.tables)){let e=[];for(let t in this.tables){let s=this.tables[t];s&&e.push({tag:t,checkSum:0,offset:new r.kF(rc[t],s),length:rc[t].size(s)})}this.tables=e}this.tag="true",this.numTables=this.tables.length;let e=Math.pow(2,Math.floor(Math.log(this.numTables)/Math.LN2));this.searchRange=16*e,this.entrySelector=Math.log(e)/Math.LN2,this.rangeShift=16*this.numTables-this.searchRange};let rg=new TextDecoder("ascii"),rm="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",rb=new Uint8Array(256);for(let e=0;e<rm.length;e++)rb[rm.charCodeAt(e)]=e;function ry(e){let t=.75*e.length;"="===e[e.length-1]&&(t--,"="===e[e.length-2]&&t--);let s=new Uint8Array(t),r=0;for(let t=0,n=e.length;t<n;t+=4){let n=rb[e.charCodeAt(t)],a=rb[e.charCodeAt(t+1)],l=rb[e.charCodeAt(t+2)],o=rb[e.charCodeAt(t+3)];s[r++]=n<<2|a>>4,s[r++]=(15&a)<<4|l>>2,s[r++]=(3&l)<<6|63&o}return s}class rw{findSubtable(e,t){for(let[s,r]of t)for(let t of e.tables)if(t.platformID===s&&t.encodingID===r)return t.table;return null}lookup(e,t){if(this.encoding)e=this.encoding.get(e)||e;else if(t){let s=this.getVariationSelector(e,t);if(s)return s}let s=this.cmap;switch(s.version){case 0:return s.codeMap.get(e)||0;case 4:{let t=0,r=s.segCount-1;for(;t<=r;){let n=t+r>>1;if(e<s.startCode.get(n))r=n-1;else if(e>s.endCode.get(n))t=n+1;else{let t,r=s.idRangeOffset.get(n);if(0===r)t=e+s.idDelta.get(n);else{let a=r/2+(e-s.startCode.get(n))-(s.segCount-n);0!==(t=s.glyphIndexArray.get(a)||0)&&(t+=s.idDelta.get(n))}return 65535&t}}return 0}case 8:throw Error("TODO: cmap format 8");case 6:case 10:return s.glyphIndices.get(e-s.firstCode)||0;case 12:case 13:{let t=0,r=s.nGroups-1;for(;t<=r;){let n=t+r>>1,a=s.groups.get(n);if(e<a.startCharCode)r=n-1;else if(e>a.endCharCode)t=n+1;else if(12===s.version)return a.glyphID+(e-a.startCharCode);else return a.glyphID}return 0}case 14:throw Error("TODO: cmap format 14");default:throw Error(`Unknown cmap format ${s.version}`)}}getVariationSelector(e,t){if(!this.uvs)return 0;let s=this.uvs.varSelectors.toArray(),r=rp(s,e=>t-e.varSelector),n=s[r];return(-1!==r&&n.defaultUVS&&(r=rp(n.defaultUVS,t=>e<t.startUnicodeValue?-1:+(e>t.startUnicodeValue+t.additionalCount))),-1!==r&&n.nonDefaultUVS&&-1!==(r=rp(n.nonDefaultUVS,t=>e-t.unicodeValue)))?n.nonDefaultUVS[r].glyphID:0}getCharacterSet(){let e=this.cmap;switch(e.version){case 0:return rd(0,e.codeMap.length);case 4:{let t=[],s=e.endCode.toArray();for(let r=0;r<s.length;r++){let n=s[r]+1,a=e.startCode.get(r);t.push(...rd(a,n))}return t}case 8:throw Error("TODO: cmap format 8");case 6:case 10:return rd(e.firstCode,e.firstCode+e.glyphIndices.length);case 12:case 13:{let t=[];for(let s of e.groups.toArray())t.push(...rd(s.startCharCode,s.endCharCode+1));return t}case 14:throw Error("TODO: cmap format 14");default:throw Error(`Unknown cmap format ${e.version}`)}}codePointsForGlyph(e){let t=this.cmap;switch(t.version){case 0:{let s=[];for(let r=0;r<256;r++)t.codeMap.get(r)===e&&s.push(r);return s}case 4:{let r=[];for(let n=0;n<t.segCount;n++){let a=t.endCode.get(n),l=t.startCode.get(n),o=t.idRangeOffset.get(n),i=t.idDelta.get(n);for(var s=l;s<=a;s++){let a=0;if(0===o)a=s+i;else{let e=o/2+(s-l)-(t.segCount-n);0!==(a=t.glyphIndexArray.get(e)||0)&&(a+=i)}a===e&&r.push(s)}}return r}case 12:{let s=[];for(let r of t.groups.toArray())e>=r.glyphID&&e<=r.glyphID+(r.endCharCode-r.startCharCode)&&s.push(r.startCharCode+(e-r.glyphID));return s}case 13:{let s=[];for(let r of t.groups.toArray())e===r.glyphID&&s.push(...rd(r.startCharCode,r.endCharCode+1));return s}default:throw Error(`Unknown cmap format ${t.version}`)}}constructor(e){if(this.encoding=null,this.cmap=this.findSubtable(e,[[3,10],[0,6],[0,4],[3,1],[0,3],[0,2],[0,1],[0,0]]),!this.cmap)for(let t of e.tables){let e=function(e){let t=N.get(e);if(t)return t;let s=R[e];if(s){let t=new Map;for(let e=0;e<s.length;e++)t.set(s.charCodeAt(e),128+e);return N.set(e,t),t}if(z.has(e)){let t=new TextDecoder(e),s=new Uint8Array(128);for(let e=0;e<128;e++)s[e]=128+e;let r=new Map,n=t.decode(s);for(let e=0;e<128;e++)r.set(n.charCodeAt(e),128+e);return N.set(e,r),r}}(B(t.platformID,t.encodingID,t.table.language-1));e&&(this.cmap=t.table,this.encoding=e)}if(!this.cmap)throw Error("Could not find a supported cmap table");this.uvs=this.findSubtable(e,[[0,5]]),this.uvs&&14!==this.uvs.version&&(this.uvs=null)}}(0,a.Cg)([C],rw.prototype,"getCharacterSet",null),(0,a.Cg)([C],rw.prototype,"codePointsForGlyph",null);class rv{process(e,t){for(let s=0;s<e.length-1;s++){let r=e[s].id,n=e[s+1].id;t[s].xAdvance+=this.getKerning(r,n)}}getKerning(e,t){let s=0;for(let r of this.kern.tables){if(r.coverage.crossStream)continue;switch(r.version){case 0:if(!r.coverage.horizontal)continue;break;case 1:if(r.coverage.vertical||r.coverage.variation)continue;break;default:throw Error(`Unsupported kerning table version ${r.version}`)}let n=0,a=r.subtable;switch(r.format){case 0:let l=rp(a.pairs,function(s){return e-s.left||t-s.right});l>=0&&(n=a.pairs[l].value);break;case 2:let o=0,i=0;o=e>=a.leftTable.firstGlyph&&e<a.leftTable.firstGlyph+a.leftTable.nGlyphs?a.leftTable.offsets[e-a.leftTable.firstGlyph]:a.array.off,t>=a.rightTable.firstGlyph&&t<a.rightTable.firstGlyph+a.rightTable.nGlyphs&&(i=a.rightTable.offsets[t-a.rightTable.firstGlyph]);let u=(o+i-a.array.off)/2;n=a.array.values.get(u);break;case 3:if(e>=a.glyphCount||t>=a.glyphCount)return 0;n=a.kernValue[a.kernIndex[a.leftClass[e]*a.rightClassCount+a.rightClass[t]]];break;default:throw Error(`Unsupported kerning sub-table format ${r.format}`)}r.coverage.override?s=n:s+=n}return s}constructor(e){this.kern=e.kern}}class r_{positionGlyphs(e,t){let s=0,r=0;for(let n=0;n<e.length;n++)e[n].isMark?r=n:(s!==r&&this.positionCluster(e,t,s,r),s=r=n);return s!==r&&this.positionCluster(e,t,s,r),t}positionCluster(e,t,s,r){let n=e[s],a=n.cbox.copy();n.codePoints.length>1&&(a.minX+=(n.codePoints.length-1)*a.width/n.codePoints.length);let l=-t[s].xAdvance,o=0,i=this.font.unitsPerEm/16;for(let n=s+1;n<=r;n++){let s=e[n],r=s.cbox,u=t[n],c=this.getCombiningClass(s.codePoints[0]);if("Not_Reordered"!==c){switch(u.xOffset=u.yOffset=0,c){case"Double_Above":case"Double_Below":u.xOffset+=a.minX-r.width/2-r.minX;break;case"Attached_Below_Left":case"Below_Left":case"Above_Left":u.xOffset+=a.minX-r.minX;break;case"Attached_Above_Right":case"Below_Right":case"Above_Right":u.xOffset+=a.maxX-r.width-r.minX;break;default:u.xOffset+=a.minX+(a.width-r.width)/2-r.minX}switch(c){case"Double_Below":case"Below_Left":case"Below":case"Below_Right":case"Attached_Below_Left":case"Attached_Below":("Attached_Below_Left"===c||"Attached_Below"===c)&&(a.minY+=i),u.yOffset=-a.minY-r.maxY,a.minY+=r.height;break;case"Double_Above":case"Above_Left":case"Above":case"Above_Right":case"Attached_Above":case"Attached_Above_Right":("Attached_Above"===c||"Attached_Above_Right"===c)&&(a.maxY+=i),u.yOffset=a.maxY-r.minY,a.maxY+=r.height}u.xAdvance=u.yAdvance=0,u.xOffset+=l,u.yOffset+=o}else l-=u.xAdvance,o-=u.yAdvance}}getCombiningClass(e){let t=(0,o.X5)(e);if((-256&e)==3584){if("Not_Reordered"===t)switch(e){case 3633:case 3636:case 3637:case 3638:case 3639:case 3655:case 3660:case 3645:case 3662:return"Above_Right";case 3761:case 3764:case 3765:case 3766:case 3767:case 3771:case 3788:case 3789:return"Above";case 3772:return"Below"}else if(3642===e)return"Below_Right"}switch(t){case"CCC10":case"CCC11":case"CCC12":case"CCC13":case"CCC14":case"CCC15":case"CCC16":case"CCC17":case"CCC18":case"CCC20":case"CCC22":case"CCC29":case"CCC32":case"CCC118":case"CCC129":case"CCC132":return"Below";case"CCC23":return"Attached_Above";case"CCC24":case"CCC107":return"Above_Right";case"CCC25":case"CCC19":return"Above_Left";case"CCC26":case"CCC27":case"CCC28":case"CCC30":case"CCC31":case"CCC33":case"CCC34":case"CCC35":case"CCC36":case"CCC122":case"CCC130":return"Above";case"CCC21":break;case"CCC103":return"Below_Right"}return t}constructor(e){this.font=e}}class rC{get width(){return this.maxX-this.minX}get height(){return this.maxY-this.minY}addPoint(e,t){Math.abs(e)!==1/0&&(e<this.minX&&(this.minX=e),e>this.maxX&&(this.maxX=e)),Math.abs(t)!==1/0&&(t<this.minY&&(this.minY=t),t>this.maxY&&(this.maxY=t))}copy(){return new rC(this.minX,this.minY,this.maxX,this.maxY)}constructor(e=1/0,t=1/0,s=-1/0,r=-1/0){this.minX=e,this.minY=t,this.maxX=s,this.maxY=r}}let rx={Caucasian_Albanian:"aghb",Arabic:"arab",Imperial_Aramaic:"armi",Armenian:"armn",Avestan:"avst",Balinese:"bali",Bamum:"bamu",Bassa_Vah:"bass",Batak:"batk",Bengali:["bng2","beng"],Bopomofo:"bopo",Brahmi:"brah",Braille:"brai",Buginese:"bugi",Buhid:"buhd",Chakma:"cakm",Canadian_Aboriginal:"cans",Carian:"cari",Cham:"cham",Cherokee:"cher",Coptic:"copt",Cypriot:"cprt",Cyrillic:"cyrl",Devanagari:["dev2","deva"],Deseret:"dsrt",Duployan:"dupl",Egyptian_Hieroglyphs:"egyp",Elbasan:"elba",Ethiopic:"ethi",Georgian:"geor",Glagolitic:"glag",Gothic:"goth",Grantha:"gran",Greek:"grek",Gujarati:["gjr2","gujr"],Gurmukhi:["gur2","guru"],Hangul:"hang",Han:"hani",Hanunoo:"hano",Hebrew:"hebr",Hiragana:"hira",Pahawh_Hmong:"hmng",Katakana_Or_Hiragana:"hrkt",Old_Italic:"ital",Javanese:"java",Kayah_Li:"kali",Katakana:"kana",Kharoshthi:"khar",Khmer:"khmr",Khojki:"khoj",Kannada:["knd2","knda"],Kaithi:"kthi",Tai_Tham:"lana",Lao:"lao ",Latin:"latn",Lepcha:"lepc",Limbu:"limb",Linear_A:"lina",Linear_B:"linb",Lisu:"lisu",Lycian:"lyci",Lydian:"lydi",Mahajani:"mahj",Mandaic:"mand",Manichaean:"mani",Mende_Kikakui:"mend",Meroitic_Cursive:"merc",Meroitic_Hieroglyphs:"mero",Malayalam:["mlm2","mlym"],Modi:"modi",Mongolian:"mong",Mro:"mroo",Meetei_Mayek:"mtei",Myanmar:["mym2","mymr"],Old_North_Arabian:"narb",Nabataean:"nbat",Nko:"nko ",Ogham:"ogam",Ol_Chiki:"olck",Old_Turkic:"orkh",Oriya:["ory2","orya"],Osmanya:"osma",Palmyrene:"palm",Pau_Cin_Hau:"pauc",Old_Permic:"perm",Phags_Pa:"phag",Inscriptional_Pahlavi:"phli",Psalter_Pahlavi:"phlp",Phoenician:"phnx",Miao:"plrd",Inscriptional_Parthian:"prti",Rejang:"rjng",Runic:"runr",Samaritan:"samr",Old_South_Arabian:"sarb",Saurashtra:"saur",Shavian:"shaw",Sharada:"shrd",Siddham:"sidd",Khudawadi:"sind",Sinhala:"sinh",Sora_Sompeng:"sora",Sundanese:"sund",Syloti_Nagri:"sylo",Syriac:"syrc",Tagbanwa:"tagb",Takri:"takr",Tai_Le:"tale",New_Tai_Lue:"talu",Tamil:["tml2","taml"],Tai_Viet:"tavt",Telugu:["tel2","telu"],Tifinagh:"tfng",Tagalog:"tglg",Thaana:"thaa",Thai:"thai",Tibetan:"tibt",Tirhuta:"tirh",Ugaritic:"ugar",Vai:"vai ",Warang_Citi:"wara",Old_Persian:"xpeo",Cuneiform:"xsux",Yi:"yi  ",Inherited:"zinh",Common:"zyyy",Unknown:"zzzz"},rS={};for(let e in rx){let t=rx[e];if(Array.isArray(t))for(let s of t)rS[s]=e;else rS[t]=e}let rk={arab:!0,hebr:!0,syrc:!0,thaa:!0,cprt:!0,khar:!0,phnx:!0,"nko ":!0,lydi:!0,avst:!0,armi:!0,phli:!0,prti:!0,sarb:!0,orkh:!0,samr:!0,mand:!0,merc:!0,mero:!0,mani:!0,mend:!0,nbat:!0,narb:!0,palm:!0,phlp:!0};function rI(e){return rk[e]?"rtl":"ltr"}class rP{get advanceWidth(){let e=0;for(let t of this.positions)e+=t.xAdvance;return e}get advanceHeight(){let e=0;for(let t of this.positions)e+=t.yAdvance;return e}get bbox(){let e=new rC,t=0,s=0;for(let r=0;r<this.glyphs.length;r++){let n=this.glyphs[r],a=this.positions[r],l=n.bbox;e.addPoint(l.minX+t+a.xOffset,l.minY+s+a.yOffset),e.addPoint(l.maxX+t+a.xOffset,l.maxY+s+a.yOffset),t+=a.xAdvance,s+=a.yAdvance}return e}constructor(e,t,s,r,n){if(this.glyphs=e,this.positions=null,this.script=s,this.language=r||null,this.direction=n||rI(s),this.features={},Array.isArray(t))for(let e of t)this.features[e]=!0;else"object"==typeof t&&(this.features=t)}}class rA{constructor(e=0,t=0,s=0,r=0){this.xAdvance=e,this.yAdvance=t,this.xOffset=s,this.yOffset=r}}let rO={allTypographicFeatures:{code:0,exclusive:!1,allTypeFeatures:0},ligatures:{code:1,exclusive:!1,requiredLigatures:0,commonLigatures:2,rareLigatures:4,rebusPictures:8,diphthongLigatures:10,squaredLigatures:12,abbrevSquaredLigatures:14,symbolLigatures:16,contextualLigatures:18,historicalLigatures:20},cursiveConnection:{code:2,exclusive:!0,unconnected:0,partiallyConnected:1,cursive:2},letterCase:{code:3,exclusive:!0},verticalSubstitution:{code:4,exclusive:!1,substituteVerticalForms:0},linguisticRearrangement:{code:5,exclusive:!1,linguisticRearrangement:0},numberSpacing:{code:6,exclusive:!0,monospacedNumbers:0,proportionalNumbers:1,thirdWidthNumbers:2,quarterWidthNumbers:3},smartSwash:{code:8,exclusive:!1,wordInitialSwashes:0,wordFinalSwashes:2,nonFinalSwashes:8},diacritics:{code:9,exclusive:!0,showDiacritics:0,hideDiacritics:1,decomposeDiacritics:2},verticalPosition:{code:10,exclusive:!0,normalPosition:0,superiors:1,inferiors:2,ordinals:3,scientificInferiors:4},fractions:{code:11,exclusive:!0,noFractions:0,verticalFractions:1,diagonalFractions:2},overlappingCharacters:{code:13,exclusive:!1,preventOverlap:0},typographicExtras:{code:14,exclusive:!1,slashedZero:4},mathematicalExtras:{code:15,exclusive:!1,mathematicalGreek:10},ornamentSets:{code:16,exclusive:!0,noOrnaments:0,dingbats:1,piCharacters:2,fleurons:3,decorativeBorders:4,internationalSymbols:5,mathSymbols:6},characterAlternatives:{code:17,exclusive:!0,noAlternates:0},designComplexity:{code:18,exclusive:!0,designLevel1:0,designLevel2:1,designLevel3:2,designLevel4:3,designLevel5:4},styleOptions:{code:19,exclusive:!0,noStyleOptions:0,displayText:1,engravedText:2,illuminatedCaps:3,titlingCaps:4,tallCaps:5},characterShape:{code:20,exclusive:!0,traditionalCharacters:0,simplifiedCharacters:1,JIS1978Characters:2,JIS1983Characters:3,JIS1990Characters:4,traditionalAltOne:5,traditionalAltTwo:6,traditionalAltThree:7,traditionalAltFour:8,traditionalAltFive:9,expertCharacters:10,JIS2004Characters:11,hojoCharacters:12,NLCCharacters:13,traditionalNamesCharacters:14},numberCase:{code:21,exclusive:!0,lowerCaseNumbers:0,upperCaseNumbers:1},textSpacing:{code:22,exclusive:!0,proportionalText:0,monospacedText:1,halfWidthText:2,thirdWidthText:3,quarterWidthText:4,altProportionalText:5,altHalfWidthText:6},transliteration:{code:23,exclusive:!0,noTransliteration:0},annotation:{code:24,exclusive:!0,noAnnotation:0,boxAnnotation:1,roundedBoxAnnotation:2,circleAnnotation:3,invertedCircleAnnotation:4,parenthesisAnnotation:5,periodAnnotation:6,romanNumeralAnnotation:7,diamondAnnotation:8,invertedBoxAnnotation:9,invertedRoundedBoxAnnotation:10},kanaSpacing:{code:25,exclusive:!0,fullWidthKana:0,proportionalKana:1},ideographicSpacing:{code:26,exclusive:!0,fullWidthIdeographs:0,proportionalIdeographs:1,halfWidthIdeographs:2},unicodeDecomposition:{code:27,exclusive:!1,canonicalComposition:0,compatibilityComposition:2,transcodingComposition:4},rubyKana:{code:28,exclusive:!1,rubyKana:2},CJKSymbolAlternatives:{code:29,exclusive:!0,noCJKSymbolAlternatives:0,CJKSymbolAltOne:1,CJKSymbolAltTwo:2,CJKSymbolAltThree:3,CJKSymbolAltFour:4,CJKSymbolAltFive:5},ideographicAlternatives:{code:30,exclusive:!0,noIdeographicAlternatives:0,ideographicAltOne:1,ideographicAltTwo:2,ideographicAltThree:3,ideographicAltFour:4,ideographicAltFive:5},CJKVerticalRomanPlacement:{code:31,exclusive:!0,CJKVerticalRomanCentered:0,CJKVerticalRomanHBaseline:1},italicCJKRoman:{code:32,exclusive:!1,CJKItalicRoman:2},caseSensitiveLayout:{code:33,exclusive:!1,caseSensitiveLayout:0,caseSensitiveSpacing:2},alternateKana:{code:34,exclusive:!1,alternateHorizKana:0,alternateVertKana:2},stylisticAlternatives:{code:35,exclusive:!1,noStylisticAlternates:0,stylisticAltOne:2,stylisticAltTwo:4,stylisticAltThree:6,stylisticAltFour:8,stylisticAltFive:10,stylisticAltSix:12,stylisticAltSeven:14,stylisticAltEight:16,stylisticAltNine:18,stylisticAltTen:20,stylisticAltEleven:22,stylisticAltTwelve:24,stylisticAltThirteen:26,stylisticAltFourteen:28,stylisticAltFifteen:30,stylisticAltSixteen:32,stylisticAltSeventeen:34,stylisticAltEighteen:36,stylisticAltNineteen:38,stylisticAltTwenty:40},contextualAlternates:{code:36,exclusive:!1,contextualAlternates:0,swashAlternates:2,contextualSwashAlternates:4},lowerCase:{code:37,exclusive:!0,defaultLowerCase:0,lowerCaseSmallCaps:1,lowerCasePetiteCaps:2},upperCase:{code:38,exclusive:!0,defaultUpperCase:0,upperCaseSmallCaps:1,upperCasePetiteCaps:2},languageTag:{code:39,exclusive:!0},CJKRomanSpacing:{code:103,exclusive:!0,halfWidthCJKRoman:0,proportionalCJKRoman:1,defaultCJKRoman:2,fullWidthCJKRoman:3}},rD=(e,t)=>[rO[e].code,rO[e][t]],rT={rlig:rD("ligatures","requiredLigatures"),clig:rD("ligatures","contextualLigatures"),dlig:rD("ligatures","rareLigatures"),hlig:rD("ligatures","historicalLigatures"),liga:rD("ligatures","commonLigatures"),hist:rD("ligatures","historicalLigatures"),smcp:rD("lowerCase","lowerCaseSmallCaps"),pcap:rD("lowerCase","lowerCasePetiteCaps"),frac:rD("fractions","diagonalFractions"),dnom:rD("fractions","diagonalFractions"),numr:rD("fractions","diagonalFractions"),afrc:rD("fractions","verticalFractions"),case:rD("caseSensitiveLayout","caseSensitiveLayout"),ccmp:rD("unicodeDecomposition","canonicalComposition"),cpct:rD("CJKVerticalRomanPlacement","CJKVerticalRomanCentered"),valt:rD("CJKVerticalRomanPlacement","CJKVerticalRomanCentered"),swsh:rD("contextualAlternates","swashAlternates"),cswh:rD("contextualAlternates","contextualSwashAlternates"),curs:rD("cursiveConnection","cursive"),c2pc:rD("upperCase","upperCasePetiteCaps"),c2sc:rD("upperCase","upperCaseSmallCaps"),init:rD("smartSwash","wordInitialSwashes"),fin2:rD("smartSwash","wordFinalSwashes"),medi:rD("smartSwash","nonFinalSwashes"),med2:rD("smartSwash","nonFinalSwashes"),fin3:rD("smartSwash","wordFinalSwashes"),fina:rD("smartSwash","wordFinalSwashes"),pkna:rD("kanaSpacing","proportionalKana"),half:rD("textSpacing","halfWidthText"),halt:rD("textSpacing","altHalfWidthText"),hkna:rD("alternateKana","alternateHorizKana"),vkna:rD("alternateKana","alternateVertKana"),ital:rD("italicCJKRoman","CJKItalicRoman"),lnum:rD("numberCase","upperCaseNumbers"),onum:rD("numberCase","lowerCaseNumbers"),mgrk:rD("mathematicalExtras","mathematicalGreek"),calt:rD("contextualAlternates","contextualAlternates"),vrt2:rD("verticalSubstitution","substituteVerticalForms"),vert:rD("verticalSubstitution","substituteVerticalForms"),tnum:rD("numberSpacing","monospacedNumbers"),pnum:rD("numberSpacing","proportionalNumbers"),sups:rD("verticalPosition","superiors"),subs:rD("verticalPosition","inferiors"),ordn:rD("verticalPosition","ordinals"),pwid:rD("textSpacing","proportionalText"),hwid:rD("textSpacing","halfWidthText"),qwid:rD("textSpacing","quarterWidthText"),twid:rD("textSpacing","thirdWidthText"),fwid:rD("textSpacing","proportionalText"),palt:rD("textSpacing","altProportionalText"),trad:rD("characterShape","traditionalCharacters"),smpl:rD("characterShape","simplifiedCharacters"),jp78:rD("characterShape","JIS1978Characters"),jp83:rD("characterShape","JIS1983Characters"),jp90:rD("characterShape","JIS1990Characters"),jp04:rD("characterShape","JIS2004Characters"),expt:rD("characterShape","expertCharacters"),hojo:rD("characterShape","hojoCharacters"),nlck:rD("characterShape","NLCCharacters"),tnam:rD("characterShape","traditionalNamesCharacters"),ruby:rD("rubyKana","rubyKana"),titl:rD("styleOptions","titlingCaps"),zero:rD("typographicExtras","slashedZero"),ss01:rD("stylisticAlternatives","stylisticAltOne"),ss02:rD("stylisticAlternatives","stylisticAltTwo"),ss03:rD("stylisticAlternatives","stylisticAltThree"),ss04:rD("stylisticAlternatives","stylisticAltFour"),ss05:rD("stylisticAlternatives","stylisticAltFive"),ss06:rD("stylisticAlternatives","stylisticAltSix"),ss07:rD("stylisticAlternatives","stylisticAltSeven"),ss08:rD("stylisticAlternatives","stylisticAltEight"),ss09:rD("stylisticAlternatives","stylisticAltNine"),ss10:rD("stylisticAlternatives","stylisticAltTen"),ss11:rD("stylisticAlternatives","stylisticAltEleven"),ss12:rD("stylisticAlternatives","stylisticAltTwelve"),ss13:rD("stylisticAlternatives","stylisticAltThirteen"),ss14:rD("stylisticAlternatives","stylisticAltFourteen"),ss15:rD("stylisticAlternatives","stylisticAltFifteen"),ss16:rD("stylisticAlternatives","stylisticAltSixteen"),ss17:rD("stylisticAlternatives","stylisticAltSeventeen"),ss18:rD("stylisticAlternatives","stylisticAltEighteen"),ss19:rD("stylisticAlternatives","stylisticAltNineteen"),ss20:rD("stylisticAlternatives","stylisticAltTwenty")};for(let e=1;e<=99;e++)rT[`cv${`00${e}`.slice(-2)}`]=[rO.characterAlternatives.code,e];let rF={};for(let e in rT){let t=rT[e];null==rF[t[0]]&&(rF[t[0]]={}),rF[t[0]][t[1]]=e}function rM(e){let[t,s]=e;if(isNaN(t))var r=rO[t]&&rO[t].code;else var r=t;if(isNaN(s))var n=rO[t]&&rO[t][s];else var n=s;return[r,n]}class rL{lookup(e){switch(this.table.version){case 0:return this.table.values.getItem(e);case 2:case 4:{let r=0,n=this.table.binarySearchHeader.nUnits-1;for(;r<=n;){var t=r+n>>1,s=this.table.segments[t];if(65535===s.firstGlyph)break;if(e<s.firstGlyph)n=t-1;else if(e>s.lastGlyph)r=t+1;else if(2===this.table.version)return s.value;else return s.values[e-s.firstGlyph]}return null}case 6:{let r=0,n=this.table.binarySearchHeader.nUnits-1;for(;r<=n;){var t=r+n>>1,s=this.table.segments[t];if(65535===s.glyph)break;if(e<s.glyph)n=t-1;else{if(!(e>s.glyph))return s.value;r=t+1}}return null}case 8:return this.table.values[e-this.table.firstGlyph];default:throw Error(`Unknown lookup table format: ${this.table.version}`)}}glyphsForValue(e){let t=[];switch(this.table.version){case 2:case 4:for(let s of this.table.segments)if(2===this.table.version&&s.value===e)t.push(...rd(s.firstGlyph,s.lastGlyph+1));else for(let r=0;r<s.values.length;r++)s.values[r]===e&&t.push(s.firstGlyph+r);break;case 6:for(let s of this.table.segments)s.value===e&&t.push(s.glyph);break;case 8:for(let s=0;s<this.table.values.length;s++)this.table.values[s]===e&&t.push(this.table.firstGlyph+s);break;default:throw Error(`Unknown lookup table format: ${this.table.version}`)}return t}constructor(e){this.table=e}}(0,a.Cg)([C],rL.prototype,"glyphsForValue",null);class rG{process(e,t,s){let r=0,n=t?e.length-1:0,a=t?-1:1;for(;1===a&&n<=e.length||-1===a&&n>=-1;){let t=null,l=1,o=!0;n===e.length||-1===n?l=0:65535===(t=e[n]).id?l=2:null==(l=this.lookupTable.lookup(t.id))&&(l=1);let i=this.stateTable.stateArray.getItem(r)[l],u=this.stateTable.entryTable.getItem(i);0!==l&&2!==l&&(s(t,u,n),o=!(16384&u.flags)),r=u.newState,o&&(n+=a)}return e}traverse(e,t=0,s=new Set){if(s.has(t))return;s.add(t);let{nClasses:r,stateArray:n,entryTable:a}=this.stateTable,l=n.getItem(t);for(let t=4;t<r;t++){let r=l[t],n=a.getItem(r);for(let r of this.lookupTable.glyphsForValue(t))e.enter&&e.enter(r,n),0!==n.newState&&this.traverse(e,n.newState,s),e.exit&&e.exit(r,n)}}constructor(e){this.stateTable=e,this.lookupTable=new rL(e.classTable)}}class rE{process(e,t={}){for(let s of this.morx.chains){let r=s.defaultFlags;for(let e of s.features){let s;(s=t[e.featureType])&&(s[e.featureSetting]?(r&=e.disableFlags,r|=e.enableFlags):!1===s[e.featureSetting]&&(r|=~e.disableFlags,r&=~e.enableFlags))}for(let t of s.subtables)t.subFeatureFlags&r&&this.processSubtable(t,e)}let s=e.length-1;for(;s>=0;)65535===e[s].id&&e.splice(s,1),s--;return e}processSubtable(e,t){if(this.subtable=e,this.glyphs=t,4===this.subtable.type)return void this.processNoncontextualSubstitutions(this.subtable,this.glyphs);this.ligatureStack=[],this.markedGlyph=null,this.firstGlyph=null,this.lastGlyph=null,this.markedIndex=null;let s=this.getStateMachine(e),r=this.getProcessor(),n=!!(4194304&this.subtable.coverage);return s.process(this.glyphs,n,r)}getStateMachine(e){return new rG(e.table.stateTable)}getProcessor(){switch(this.subtable.type){case 0:return this.processIndicRearragement;case 1:return this.processContextualSubstitution;case 2:return this.processLigature;case 4:return this.processNoncontextualSubstitutions;case 5:return this.processGlyphInsertion;default:throw Error(`Invalid morx subtable type: ${this.subtable.type}`)}}processIndicRearragement(e,t,s){32768&t.flags&&(this.firstGlyph=s),8192&t.flags&&(this.lastGlyph=s),function(e,t,s,r){switch(t){case 0:return;case 1:return rV(e,[s,1],[r,0]);case 2:return rV(e,[s,0],[r,1]);case 3:return rV(e,[s,1],[r,1]);case 4:return rV(e,[s,2],[r,0]);case 5:return rV(e,[s,2],[r,0],!0,!1);case 6:return rV(e,[s,0],[r,2]);case 7:return rV(e,[s,0],[r,2],!1,!0);case 8:return rV(e,[s,1],[r,2]);case 9:return rV(e,[s,1],[r,2],!1,!0);case 10:return rV(e,[s,2],[r,1]);case 11:return rV(e,[s,2],[r,1],!0,!1);case 12:return rV(e,[s,2],[r,2]);case 13:return rV(e,[s,2],[r,2],!0,!1);case 14:return rV(e,[s,2],[r,2],!1,!0);case 15:return rV(e,[s,2],[r,2],!0,!0);default:throw Error(`Unknown verb: ${t}`)}}(this.glyphs,15&t.flags,this.firstGlyph,this.lastGlyph)}processContextualSubstitution(e,t,s){let r=this.subtable.table.substitutionTable.items;if(65535!==t.markIndex){let s=new rL(r.getItem(t.markIndex));e=this.glyphs[this.markedGlyph];var n=s.lookup(e.id);n&&(this.glyphs[this.markedGlyph]=this.font.getGlyph(n,e.codePoints))}if(65535!==t.currentIndex){let a=new rL(r.getItem(t.currentIndex));e=this.glyphs[s];var n=a.lookup(e.id);n&&(this.glyphs[s]=this.font.getGlyph(n,e.codePoints))}32768&t.flags&&(this.markedGlyph=s)}processLigature(e,t,s){if(32768&t.flags&&this.ligatureStack.push(s),8192&t.flags){let e=this.subtable.table.ligatureActions,s=this.subtable.table.components,r=this.subtable.table.ligatureList,n=t.action,a=!1,l=0,o=[],i=[];for(;!a;){let t=this.ligatureStack.pop();o.unshift(...this.glyphs[t].codePoints);let u=e.getItem(n++);a=!!(0x80000000&u);let c=!!(0x40000000&u),h=(0x3fffffff&u)<<2>>2;if(h+=this.glyphs[t].id,l+=s.getItem(h),a||c){let e=r.getItem(l);this.glyphs[t]=this.font.getGlyph(e,o),i.push(t),l=0,o=[]}else this.glyphs[t]=this.font.getGlyph(65535)}this.ligatureStack.push(...i)}}processNoncontextualSubstitutions(e,t,s){let r=new rL(e.table.lookupTable);for(s=0;s<t.length;s++){let e=t[s];if(65535!==e.id){let n=r.lookup(e.id);n&&(t[s]=this.font.getGlyph(n,e.codePoints))}}}_insertGlyphs(e,t,s,r){let n=[];for(;s--;){let e=this.subtable.table.insertionActions.getItem(t++);n.push(this.font.getGlyph(e))}!r&&e++,this.glyphs.splice(e,0,...n)}processGlyphInsertion(e,t,s){if(32768&t.flags&&(this.markedIndex=s),65535!==t.markedInsertIndex){let e=(31&t.flags)>>>5,s=!!(1024&t.flags);this._insertGlyphs(this.markedIndex,t.markedInsertIndex,e,s)}if(65535!==t.currentInsertIndex){let e=(992&t.flags)>>>5,r=!!(2048&t.flags);this._insertGlyphs(s,t.currentInsertIndex,e,r)}}getSupportedFeatures(){let e=[];for(let t of this.morx.chains)for(let s of t.features)e.push([s.featureType,s.featureSetting]);return e}generateInputs(e){return this.inputCache||this.generateInputCache(),this.inputCache[e]||[]}generateInputCache(){for(let e of(this.inputCache={},this.morx.chains)){let t=e.defaultFlags;for(let s of e.subtables)s.subFeatureFlags&t&&this.generateInputsForSubtable(s)}}generateInputsForSubtable(e){if(2!==e.type)return;if(4194304&e.coverage)throw Error("Reverse subtable, not supported.");this.subtable=e,this.ligatureStack=[];let t=this.getStateMachine(e),s=this.getProcessor(),r=[],n=[];this.glyphs=[],t.traverse({enter:(e,t)=>{let a=this.glyphs;n.push({glyphs:a.slice(),ligatureStack:this.ligatureStack.slice()});let l=this.font.getGlyph(e);r.push(l),a.push(r[r.length-1]),s(a[a.length-1],t,a.length-1);let o=0,i=0;for(let e=0;e<a.length&&o<=1;e++)65535!==a[e].id&&(o++,i=a[e].id);if(1===o){let e=r.map(e=>e.id),t=this.inputCache[i];t?t.push(e):this.inputCache[i]=[e]}},exit:()=>{({glyphs:this.glyphs,ligatureStack:this.ligatureStack}=n.pop()),r.pop()}})}constructor(e){this.processIndicRearragement=this.processIndicRearragement.bind(this),this.processContextualSubstitution=this.processContextualSubstitution.bind(this),this.processLigature=this.processLigature.bind(this),this.processNoncontextualSubstitutions=this.processNoncontextualSubstitutions.bind(this),this.processGlyphInsertion=this.processGlyphInsertion.bind(this),this.font=e,this.morx=e.morx,this.inputCache=null}}function rV(e,t,s,r=!1,n=!1){let a=e.splice(s[0]-(s[1]-1),s[1]);n&&a.reverse();let l=e.splice(t[0],t[1],...a);return r&&l.reverse(),e.splice(s[0]-(t[1]-1),0,...l),e}(0,a.Cg)([C],rE.prototype,"getStateMachine",null);class rB{substitute(e){"rtl"===e.direction&&e.glyphs.reverse(),this.morxProcessor.process(e.glyphs,function(e){let t={};for(let s in e){let r;(r=rT[s])&&(null==t[r[0]]&&(t[r[0]]={}),t[r[0]][r[1]]=e[s])}return t}(e.features))}getAvailableFeatures(e,t){return function(e){let t={};if(Array.isArray(e))for(let s=0;s<e.length;s++){let r,n=rM(e[s]);(r=rF[n[0]]&&rF[n[0]][n[1]])&&(t[r]=!0)}else if("object"==typeof e)for(let s in e){let r=e[s];for(let e in r){let n,a=rM([s,e]);r[e]&&(n=rF[a[0]]&&rF[a[0]][a[1]])&&(t[n]=!0)}}return Object.keys(t)}(this.morxProcessor.getSupportedFeatures())}stringsForGlyph(e){let t=this.morxProcessor.generateInputs(e),s=new Set;for(let e of t)this._addStrings(e,0,s,"");return s}_addStrings(e,t,s,r){for(let n of this.font._cmapProcessor.codePointsForGlyph(e[t])){let a=r+String.fromCodePoint(n);t<e.length-1?this._addStrings(e,t+1,s,a):s.add(a)}}constructor(e){this.font=e,this.morxProcessor=new rE(e),this.fallbackPosition=!1}}class rz{_addFeatures(e,t){let s=this.stages.length-1,r=this.stages[s];for(let n of e)null==this.allFeatures[n]&&(r.push(n),this.allFeatures[n]=s,t&&(this.globalFeatures[n]=!0))}add(e,t=!0){if(0===this.stages.length&&this.stages.push([]),"string"==typeof e&&(e=[e]),Array.isArray(e))this._addFeatures(e,t);else if("object"==typeof e)this._addFeatures(e.global||[],!0),this._addFeatures(e.local||[],!1);else throw Error("Unsupported argument to ShapingPlan#add")}addStage(e,t){"function"==typeof e?this.stages.push(e,[]):(this.stages.push([]),this.add(e,t))}setFeatureOverrides(e){if(Array.isArray(e))this.add(e);else if("object"==typeof e){for(let t in e)if(e[t])this.add(t);else if(null!=this.allFeatures[t]){let e=this.stages[this.allFeatures[t]];e.splice(e.indexOf(t),1),delete this.allFeatures[t],delete this.globalFeatures[t]}}}assignGlobalFeatures(e){for(let t of e)for(let e in this.globalFeatures)t.features[e]=!0}process(e,t,s){for(let r of this.stages)"function"==typeof r?s||r(this.font,t,this):r.length>0&&e.applyFeatures(r,t,s)}constructor(e,t,s){this.font=e,this.script=t,this.direction=s,this.stages=[],this.globalFeatures={},this.allFeatures={}}}let rR=["rvrn"],rN=["ccmp","locl","rlig","mark","mkmk"],rU=["frac","numr","dnom"],rq=["calt","clig","liga","rclt","curs","kern"],rH={ltr:["ltra","ltrm"],rtl:["rtla","rtlm"]};class rj{static plan(e,t,s){this.planPreprocessing(e),this.planFeatures(e),this.planPostprocessing(e,s),e.assignGlobalFeatures(t),this.assignFeatures(e,t)}static planPreprocessing(e){e.add({global:[...rR,...rH[e.direction]],local:rU})}static planFeatures(e){}static planPostprocessing(e,t){e.add([...rN,...rq]),e.setFeatureOverrides(t)}static assignFeatures(e,t){for(let e=0;e<t.length;e++){let s=t[e];if(8260===s.codePoints[0]){let r=e,n=e+1;for(;r>0&&(0,o.yp)(t[r-1].codePoints[0]);)t[r-1].features.numr=!0,t[r-1].features.frac=!0,r--;for(;n<t.length&&(0,o.yp)(t[n].codePoints[0]);)t[n].features.dnom=!0,t[n].features.frac=!0,n++;s.features.frac=!0,e=n-1}}}}(0,n._)(rj,"zeroMarkWidths","AFTER_GPOS");let rX=new i(ry("APABAAAAAAAAOAAAAf0BAv7tmi1MxDAUx7vtvjhAgcDgkEgEAnmXEBIMCYaEcygEiqBQ4FAkCE4ikUgMiiBJSAgSiUQSDMn9L9eSl6bddddug9t7yS/trevre+3r27pcNxZiG+yCfdCVv/9LeQxOwRm4AJegD27ALbgD9+ABPJF+z+BN/h7yDj5k/VOWX6SdmU5+wLWknggxDxaS8u0qiiX4uiz9XamQ3wzDMAzDMAzDMAzDVI/h959V/v7BMAzDMAzDMLlyNTNiMSdewVxbiA44B4/guz1qW58VYlMI0WsJ0W+N6kXw0spvPtdwhtkwnGM6uLaV4Xyzg3v3PM9DPfQ/sOg4xPWjipy31P8LTqbU304c/cLCUmWJLNB2Uz2U1KTeRKNmKHVMfbJC+/0loTZRH/W5cvEvBJPMbREkWt3FD1NcqXZBSpuE2Ad0PBehPtNrPtIEdYP+hiRt/V1jIiE69X4NT/uVZI3PUHE9bm5M7ePGdZWy951v7Nn6j8v1WWKP3mt6ttnsigx6VN7Vc0VomSSGqW2mGNP1muZPl7LfjNUaKNFtDGVf2fvE9O7VlBS5j333c5p/eeoOqcs1R/hIqDWLJ7TTlksirVT1SI7l8k4Yp+g3jafGcrU1RM6l9th80XOpnlN97bDNY4i4s61B0Si/ipa0uHMl6zqEjlFfCZm/TM8KmzQDjmuTAQ==")),rY=["isol","fina","fin2","fin3","medi","med2","init"],rW={Non_Joining:0,Transparent:6},rZ="isol",rJ="fina",rK="fin2",rQ="medi",r$="med2",r0="init",r1=[[[null,null,0],[null,rZ,2],[null,rZ,1],[null,rZ,2],[null,rZ,1],[null,rZ,6]],[[null,null,0],[null,rZ,2],[null,rZ,1],[null,rZ,2],[null,rK,5],[null,rZ,6]],[[null,null,0],[null,rZ,2],[r0,rJ,1],[r0,rJ,3],[r0,rJ,4],[r0,rJ,6]],[[null,null,0],[null,rZ,2],[rQ,rJ,1],[rQ,rJ,3],[rQ,rJ,4],[rQ,rJ,6]],[[null,null,0],[null,rZ,2],[r$,rZ,1],[r$,rZ,2],[r$,rK,5],[r$,rZ,6]],[[null,null,0],[null,rZ,2],[rZ,rZ,1],[rZ,rZ,2],[rZ,rK,5],[rZ,rZ,6]],[[null,null,0],[null,rZ,2],[null,rZ,1],[null,rZ,2],[null,"fin3",5],[null,rZ,6]]];class r2 extends rj{static planFeatures(e){e.add(["ccmp","locl"]);for(let t=0;t<rY.length;t++){let s=rY[t];e.addStage(s,!1)}e.addStage("mset")}static assignFeatures(e,t){super.assignFeatures(e,t);let s=-1,r=0,n=[];for(let e=0;e<t.length;e++){let l,i;var a=t[e];let u=function(e){let t=rX.get(e);if(t)return t-1;let s=(0,o.p7)(e);return"Mn"===s||"Me"===s||"Cf"===s?rW.Transparent:rW.Non_Joining}(a.codePoints[0]);if(u===rW.Transparent){n[e]=null;continue}[i,l,r]=r1[r][u],null!==i&&-1!==s&&(n[s]=i),n[e]=l,s=e}for(let e=0;e<t.length;e++){let s;var a=t[e];(s=n[e])&&(a.features[s]=!0)}}}class r3{reset(e={},t=0){this.options=e,this.flags=e.flags||{},this.markAttachmentType=e.markAttachmentType||0,this.index=t}get cur(){return this.glyphs[this.index]||null}shouldIgnore(e){return this.flags.ignoreMarks&&e.isMark||this.flags.ignoreBaseGlyphs&&e.isBase||this.flags.ignoreLigatures&&e.isLigature||this.markAttachmentType&&e.isMark&&e.markAttachmentType!==this.markAttachmentType}move(e){for(this.index+=e;0<=this.index&&this.index<this.glyphs.length&&this.shouldIgnore(this.glyphs[this.index]);)this.index+=e;return 0>this.index||this.index>=this.glyphs.length?null:this.glyphs[this.index]}next(){return this.move(1)}prev(){return this.move(-1)}peek(e=1){let t=this.index,s=this.increment(e);return this.index=t,s}peekIndex(e=1){let t=this.index;this.increment(e);let s=this.index;return this.index=t,s}increment(e=1){let t=e<0?-1:1;for(e=Math.abs(e);e--;)this.move(t);return this.glyphs[this.index]}constructor(e,t){this.glyphs=e,this.reset(t)}}let r4=["DFLT","dflt","latn"];class r5{findScript(e){if(null==this.table.scriptList)return null;for(let t of(Array.isArray(e)||(e=[e]),e))for(let e of this.table.scriptList)if(e.tag===t)return e;return null}selectScript(e,t,s){let r,n=!1;if(!this.script||e!==this.scriptTag){if((r=this.findScript(e))||(r=this.findScript(r4)),!r)return this.scriptTag;this.scriptTag=r.tag,this.script=r.script,this.language=null,this.languageTag=null,n=!0}if(s&&s===this.direction||(this.direction=s||rI(e)),t&&t.length<4&&(t+=" ".repeat(4-t.length)),!t||t!==this.languageTag){for(let e of(this.language=null,this.script.langSysRecords))if(e.tag===t){this.language=e.langSys,this.languageTag=e.tag;break}this.language||(this.language=this.script.defaultLangSys,this.languageTag=null),n=!0}if(n&&(this.features={},this.language))for(let e of this.language.featureIndexes){let t=this.table.featureList[e],s=this.substituteFeatureForVariations(e);this.features[t.tag]=s||t.feature}return this.scriptTag}lookupsForFeatures(e=[],t){let s=[];for(let r of e){let e=this.features[r];if(e)for(let n of e.lookupListIndexes)t&&-1!==t.indexOf(n)||s.push({feature:r,index:n,lookup:this.table.lookupList.get(n)})}return s.sort((e,t)=>e.index-t.index),s}substituteFeatureForVariations(e){if(-1===this.variationsIndex)return null;for(let t of this.table.featureVariations.featureVariationRecords[this.variationsIndex].featureTableSubstitution.substitutions)if(t.featureIndex===e)return t.alternateFeatureTable;return null}findVariationsIndex(e){let t=this.table.featureVariations;if(!t)return -1;let s=t.featureVariationRecords;for(let t=0;t<s.length;t++){let r=s[t].conditionSet.conditionTable;if(this.variationConditionsMatch(r,e))return t}return -1}variationConditionsMatch(e,t){return e.every(e=>{let s=e.axisIndex<t.length?t[e.axisIndex]:0;return e.filterRangeMinValue<=s&&s<=e.filterRangeMaxValue})}applyFeatures(e,t,s){let r=this.lookupsForFeatures(e);this.applyLookups(r,t,s)}applyLookups(e,t,s){for(let{feature:r,lookup:n}of(this.glyphs=t,this.positions=s,this.glyphIterator=new r3(t),e))for(this.currentFeature=r,this.glyphIterator.reset(n.flags);this.glyphIterator.index<t.length;){if(!(r in this.glyphIterator.cur.features)){this.glyphIterator.next();continue}for(let e of n.subTables)if(this.applyLookup(n.lookupType,e))break;this.glyphIterator.next()}}applyLookup(e,t){throw Error("applyLookup must be implemented by subclasses")}applyLookupList(e){let t=this.glyphIterator.options,s=this.glyphIterator.index;for(let r of e){this.glyphIterator.reset(t,s),this.glyphIterator.increment(r.sequenceIndex);let e=this.table.lookupList.get(r.lookupListIndex);for(let t of(this.glyphIterator.reset(e.flags,this.glyphIterator.index),e.subTables))if(this.applyLookup(e.lookupType,t))break}return this.glyphIterator.reset(t,s),!0}coverageIndex(e,t){switch(null==t&&(t=this.glyphIterator.cur.id),e.version){case 1:return e.glyphs.indexOf(t);case 2:for(let s of e.rangeRecords)if(s.start<=t&&t<=s.end)return s.startCoverageIndex+t-s.start}return -1}match(e,t,s,r){let n=this.glyphIterator.index,a=this.glyphIterator.increment(e),l=0;for(;l<t.length&&a&&s(t[l],a);)r&&r.push(this.glyphIterator.index),l++,a=this.glyphIterator.next();return this.glyphIterator.index=n,!(l<t.length)&&(r||!0)}sequenceMatches(e,t){return this.match(e,t,(e,t)=>e===t.id)}sequenceMatchIndices(e,t){return this.match(e,t,(e,t)=>this.currentFeature in t.features&&e===t.id,[])}coverageSequenceMatches(e,t){return this.match(e,t,(e,t)=>this.coverageIndex(e,t.id)>=0)}getClassID(e,t){switch(t.version){case 1:let s=e-t.startGlyph;if(s>=0&&s<t.classValueArray.length)return t.classValueArray[s];break;case 2:for(let s of t.classRangeRecord)if(s.start<=e&&e<=s.end)return s.class}return 0}classSequenceMatches(e,t,s){return this.match(e,t,(e,t)=>e===this.getClassID(t.id,s))}applyContext(e){let t,s;switch(e.version){case 1:if(-1===(t=this.coverageIndex(e.coverage)))break;for(let s of e.ruleSets[t])if(this.sequenceMatches(1,s.input))return this.applyLookupList(s.lookupRecords);break;case 2:if(-1===this.coverageIndex(e.coverage)||-1===(t=this.getClassID(this.glyphIterator.cur.id,e.classDef)))break;for(let s of e.classSet[t])if(this.classSequenceMatches(1,s.classes,e.classDef))return this.applyLookupList(s.lookupRecords);break;case 3:if(this.coverageSequenceMatches(0,e.coverages))return this.applyLookupList(e.lookupRecords)}return!1}applyChainingContext(e){let t;switch(e.version){case 1:if(-1===(t=this.coverageIndex(e.coverage)))break;for(let s of e.chainRuleSets[t])if(this.sequenceMatches(-s.backtrack.length,s.backtrack)&&this.sequenceMatches(1,s.input)&&this.sequenceMatches(1+s.input.length,s.lookahead))return this.applyLookupList(s.lookupRecords);break;case 2:if(-1===this.coverageIndex(e.coverage))break;t=this.getClassID(this.glyphIterator.cur.id,e.inputClassDef);let s=e.chainClassSet[t];if(!s)break;for(let t of s)if(this.classSequenceMatches(-t.backtrack.length,t.backtrack,e.backtrackClassDef)&&this.classSequenceMatches(1,t.input,e.inputClassDef)&&this.classSequenceMatches(1+t.input.length,t.lookahead,e.lookaheadClassDef))return this.applyLookupList(t.lookupRecords);break;case 3:if(this.coverageSequenceMatches(-e.backtrackGlyphCount,e.backtrackCoverage)&&this.coverageSequenceMatches(0,e.inputCoverage)&&this.coverageSequenceMatches(e.inputGlyphCount,e.lookaheadCoverage))return this.applyLookupList(e.lookupRecords)}return!1}constructor(e,t){this.font=e,this.table=t,this.script=null,this.scriptTag=null,this.language=null,this.languageTag=null,this.features={},this.lookups={},this.variationsIndex=e._variationProcessor?this.findVariationsIndex(e._variationProcessor.normalizedCoords):-1,this.selectScript(),this.glyphs=[],this.positions=[],this.ligatureID=1,this.currentFeature=null}}class r8{get id(){return this._id}set id(e){this._id=e,this.substituted=!0;let t=this._font.GDEF;if(t&&t.glyphClassDef){let s=r5.prototype.getClassID(e,t.glyphClassDef);this.isBase=1===s,this.isLigature=2===s,this.isMark=3===s,this.markAttachmentType=t.markAttachClassDef?r5.prototype.getClassID(e,t.markAttachClassDef):0}else this.isMark=this.codePoints.length>0&&this.codePoints.every(o.qi),this.isBase=!this.isMark,this.isLigature=this.codePoints.length>1,this.markAttachmentType=0}copy(){return new r8(this._font,this.id,this.codePoints,this.features)}constructor(e,t,s=[],r){if(this._font=e,this.codePoints=s,this.id=t,this.features={},Array.isArray(r))for(let e=0;e<r.length;e++){let t=r[e];this.features[t]=!0}else"object"==typeof r&&Object.assign(this.features,r);this.ligatureID=null,this.ligatureComponent=null,this.isLigated=!1,this.cursiveAttachment=null,this.markAttachment=null,this.shaperInfo=null,this.substituted=!1,this.isMultiplied=!1}}class r6 extends rj{static planFeatures(e){e.add(["ljmo","vjmo","tjmo"],!1)}static assignFeatures(e,t){let s=0,r=0;for(;r<t.length;){let n,a=t[r].codePoints[0],l=no(a);switch([n,s]=nf[s][l],n){case ni:e.font.hasGlyphForCodePoint(a)||(r=nd(t,r,e.font));break;case nu:r=function(e,t,s){let r,n,a,l,o=e[t],i=no(e[t].codePoints[0]),u=e[t-1].codePoints[0],c=no(u);if(4===c&&3===i)r=u,l=o;else{2===i?(n=e[t-1],a=o):(n=e[t-2],a=e[t-1],l=o);let s=n.codePoints[0],u=a.codePoints[0];nn(s)&&na(u)&&(r=44032+((s-4352)*21+(u-4449))*28)}let h=l&&l.codePoints[0]||4519;if(null!=r&&(4519===h||nl(h))){let n=r+(h-4519);if(s.hasGlyphForCodePoint(n)){let r=2===c?3:2;return e.splice(t-r+1,r,np(s,n,o.features)),t-r+1}}return(n&&(n.features.ljmo=!0),a&&(a.features.vjmo=!0),l&&(l.features.tjmo=!0),4===c)?(nd(e,t-1,s),t+1):t}(t,r,e.font);break;case nc:!function(e,t,s){let r=e[t],n=e[t].codePoints[0];if(0===s.glyphForCodePoint(n).advanceWidth)return;let a=function(e){switch(no(e)){case 4:case 5:return 1;case 2:return 2;case 3:return 3}}(e[t-1].codePoints[0]);e.splice(t,1),e.splice(t-a,0,r)}(t,r,e.font);break;case nh:r=function(e,t,s){let r=e[t],n=e[t].codePoints[0];if(s.hasGlyphForCodePoint(9676)){let a=np(s,9676,r.features),l=0===s.glyphForCodePoint(n).advanceWidth?t:t+1;e.splice(l,0,a),t++}return t}(t,r,e.font)}r++}}}(0,n._)(r6,"zeroMarkWidths","NONE");let r9=e=>4352<=e&&e<=4447||43360<=e&&e<=43388,r7=e=>4448<=e&&e<=4519||55216<=e&&e<=55238,ne=e=>4520<=e&&e<=4607||55243<=e&&e<=55291,nt=e=>12334<=e&&e<=12335,ns=e=>44032<=e&&e<=55204,nr=e=>e-44032<11173&&(e-44032)%28==0,nn=e=>4352<=e&&e<=4370,na=e=>4449<=e&&e<=4469,nl=e=>1<=e&&e<=4546;function no(e){return r9(e)?1:r7(e)?2:ne(e)?3:nr(e)?4:ns(e)?5:6*!!nt(e)}let ni=1,nu=2,nc=4,nh=5,nf=[[[0,0],[0,1],[0,0],[0,0],[1,2],[1,3],[5,0]],[[0,0],[0,1],[2,2],[0,0],[1,2],[1,3],[5,0]],[[0,0],[0,1],[0,0],[2,3],[1,2],[1,3],[4,0]],[[0,0],[0,1],[0,0],[0,0],[1,2],[1,3],[4,0]]];function np(e,t,s){return new r8(e,e.glyphForCodePoint(t).id,[t],s)}function nd(e,t,s){let r=e[t],n=r.codePoints[0]-44032,a=4519+n%28,l=4352+(n=n/28|0)/21|0,o=4449+n%21;if(!s.hasGlyphForCodePoint(l)||!s.hasGlyphForCodePoint(o)||4519!==a&&!s.hasGlyphForCodePoint(a))return t;let i=np(s,l,r.features);i.features.ljmo=!0;let u=np(s,o,r.features);u.features.vjmo=!0;let c=[i,u];if(a>4519){let e=np(s,a,r.features);e.features.tjmo=!0,c.push(e)}return e.splice(t,1,...c),t+c.length-1}var ng={};ng=JSON.parse('{"stateTable":[[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,2,3,4,5,6,7,8,9,0,10,11,11,12,13,14,15,16,17],[0,0,0,18,19,20,21,22,23,0,24,0,0,25,26,0,0,27,0],[0,0,0,28,29,30,31,32,33,0,34,0,0,35,36,0,0,37,0],[0,0,0,38,5,7,7,8,9,0,10,0,0,0,13,0,0,16,0],[0,39,0,0,0,40,41,0,9,0,10,0,0,0,42,0,39,0,0],[0,0,0,0,43,44,44,8,9,0,0,0,0,12,43,0,0,0,0],[0,0,0,0,43,44,44,8,9,0,0,0,0,0,43,0,0,0,0],[0,0,0,45,46,47,48,49,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,50,0,0,51,0,10,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,52,0,0,0,0,0,0,0,0],[0,0,0,53,54,55,56,57,58,0,59,0,0,60,61,0,0,62,0],[0,0,0,4,5,7,7,8,9,0,10,0,0,0,13,0,0,16,0],[0,63,64,0,0,40,41,0,9,0,10,0,0,0,42,0,63,0,0],[0,2,3,4,5,6,7,8,9,0,10,11,11,12,13,0,2,16,0],[0,0,0,18,65,20,21,22,23,0,24,0,0,25,26,0,0,27,0],[0,0,0,0,66,67,67,8,9,0,10,0,0,0,68,0,0,0,0],[0,0,0,69,0,70,70,0,71,0,72,0,0,0,0,0,0,0,0],[0,0,0,73,19,74,74,22,23,0,24,0,0,0,26,0,0,27,0],[0,75,0,0,0,76,77,0,23,0,24,0,0,0,78,0,75,0,0],[0,0,0,0,79,80,80,22,23,0,0,0,0,25,79,0,0,0,0],[0,0,0,18,19,20,74,22,23,0,24,0,0,25,26,0,0,27,0],[0,0,0,81,82,83,84,85,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,86,0,0,87,0,24,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,88,0,0,0,0,0,0,0,0],[0,0,0,18,19,74,74,22,23,0,24,0,0,0,26,0,0,27,0],[0,89,90,0,0,76,77,0,23,0,24,0,0,0,78,0,89,0,0],[0,0,0,0,91,92,92,22,23,0,24,0,0,0,93,0,0,0,0],[0,0,0,94,29,95,31,32,33,0,34,0,0,0,36,0,0,37,0],[0,96,0,0,0,97,98,0,33,0,34,0,0,0,99,0,96,0,0],[0,0,0,0,100,101,101,32,33,0,0,0,0,35,100,0,0,0,0],[0,0,0,0,100,101,101,32,33,0,0,0,0,0,100,0,0,0,0],[0,0,0,102,103,104,105,106,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,107,0,0,108,0,34,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,109,0,0,0,0,0,0,0,0],[0,0,0,28,29,95,31,32,33,0,34,0,0,0,36,0,0,37,0],[0,110,111,0,0,97,98,0,33,0,34,0,0,0,99,0,110,0,0],[0,0,0,0,112,113,113,32,33,0,34,0,0,0,114,0,0,0,0],[0,0,0,0,5,7,7,8,9,0,10,0,0,0,13,0,0,16,0],[0,0,0,115,116,117,118,8,9,0,10,0,0,119,120,0,0,16,0],[0,0,0,0,0,121,121,0,9,0,10,0,0,0,42,0,0,0,0],[0,39,0,122,0,123,123,8,9,0,10,0,0,0,42,0,39,0,0],[0,124,64,0,0,0,0,0,0,0,0,0,0,0,0,0,124,0,0],[0,39,0,0,0,121,125,0,9,0,10,0,0,0,42,0,39,0,0],[0,0,0,0,0,126,126,8,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,46,47,48,49,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,47,47,49,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,127,127,49,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,128,127,127,49,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,129,130,131,132,133,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,10,0,0,0,0,0,0,0,0],[0,0,0,0,0,50,0,0,0,0,10,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,134,0,0,0,0,0,0,0,0],[0,0,0,135,54,56,56,57,58,0,59,0,0,0,61,0,0,62,0],[0,136,0,0,0,137,138,0,58,0,59,0,0,0,139,0,136,0,0],[0,0,0,0,140,141,141,57,58,0,0,0,0,60,140,0,0,0,0],[0,0,0,0,140,141,141,57,58,0,0,0,0,0,140,0,0,0,0],[0,0,0,142,143,144,145,146,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,147,0,0,148,0,59,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,149,0,0,0,0,0,0,0,0],[0,0,0,53,54,56,56,57,58,0,59,0,0,0,61,0,0,62,0],[0,150,151,0,0,137,138,0,58,0,59,0,0,0,139,0,150,0,0],[0,0,0,0,152,153,153,57,58,0,59,0,0,0,154,0,0,0,0],[0,0,0,155,116,156,157,8,9,0,10,0,0,158,120,0,0,16,0],[0,0,0,0,0,121,121,0,9,0,10,0,0,0,0,0,0,0,0],[0,75,3,4,5,159,160,8,161,0,162,0,11,12,163,0,75,16,0],[0,0,0,0,0,40,164,0,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,165,44,44,8,9,0,0,0,0,0,165,0,0,0,0],[0,124,64,0,0,40,164,0,9,0,10,0,0,0,42,0,124,0,0],[0,0,0,0,0,70,70,0,71,0,72,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,71,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,166,0,0,167,0,72,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,168,0,0,0,0,0,0,0,0],[0,0,0,0,19,74,74,22,23,0,24,0,0,0,26,0,0,27,0],[0,0,0,0,79,80,80,22,23,0,0,0,0,0,79,0,0,0,0],[0,0,0,169,170,171,172,22,23,0,24,0,0,173,174,0,0,27,0],[0,0,0,0,0,175,175,0,23,0,24,0,0,0,78,0,0,0,0],[0,75,0,176,0,177,177,22,23,0,24,0,0,0,78,0,75,0,0],[0,178,90,0,0,0,0,0,0,0,0,0,0,0,0,0,178,0,0],[0,75,0,0,0,175,179,0,23,0,24,0,0,0,78,0,75,0,0],[0,0,0,0,0,180,180,22,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,82,83,84,85,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,83,83,85,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,181,181,85,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,182,181,181,85,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,183,184,185,186,187,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,24,0,0,0,0,0,0,0,0],[0,0,0,0,0,86,0,0,0,0,24,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,188,0,0,0,0,0,0,0,0],[0,0,0,189,170,190,191,22,23,0,24,0,0,192,174,0,0,27,0],[0,0,0,0,0,175,175,0,23,0,24,0,0,0,0,0,0,0,0],[0,0,0,0,0,76,193,0,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,194,80,80,22,23,0,0,0,0,0,194,0,0,0,0],[0,178,90,0,0,76,193,0,23,0,24,0,0,0,78,0,178,0,0],[0,0,0,0,29,95,31,32,33,0,34,0,0,0,36,0,0,37,0],[0,0,0,0,100,101,101,32,33,0,0,0,0,0,100,0,0,0,0],[0,0,0,195,196,197,198,32,33,0,34,0,0,199,200,0,0,37,0],[0,0,0,0,0,201,201,0,33,0,34,0,0,0,99,0,0,0,0],[0,96,0,202,0,203,203,32,33,0,34,0,0,0,99,0,96,0,0],[0,204,111,0,0,0,0,0,0,0,0,0,0,0,0,0,204,0,0],[0,96,0,0,0,201,205,0,33,0,34,0,0,0,99,0,96,0,0],[0,0,0,0,0,206,206,32,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,103,104,105,106,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,104,104,106,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,207,207,106,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,208,207,207,106,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,209,210,211,212,213,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,34,0,0,0,0,0,0,0,0],[0,0,0,0,0,107,0,0,0,0,34,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,214,0,0,0,0,0,0,0,0],[0,0,0,215,196,216,217,32,33,0,34,0,0,218,200,0,0,37,0],[0,0,0,0,0,201,201,0,33,0,34,0,0,0,0,0,0,0,0],[0,0,0,0,0,97,219,0,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,220,101,101,32,33,0,0,0,0,0,220,0,0,0,0],[0,204,111,0,0,97,219,0,33,0,34,0,0,0,99,0,204,0,0],[0,0,0,221,116,222,222,8,9,0,10,0,0,0,120,0,0,16,0],[0,223,0,0,0,40,224,0,9,0,10,0,0,0,42,0,223,0,0],[0,0,0,0,225,44,44,8,9,0,0,0,0,119,225,0,0,0,0],[0,0,0,115,116,117,222,8,9,0,10,0,0,119,120,0,0,16,0],[0,0,0,115,116,222,222,8,9,0,10,0,0,0,120,0,0,16,0],[0,226,64,0,0,40,224,0,9,0,10,0,0,0,42,0,226,0,0],[0,0,0,0,0,0,0,0,9,0,0,0,0,0,0,0,0,0,0],[0,39,0,0,0,121,121,0,9,0,10,0,0,0,42,0,39,0,0],[0,0,0,0,0,44,44,8,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,227,0,228,229,0,9,0,10,0,0,230,0,0,0,0,0],[0,39,0,122,0,121,121,0,9,0,10,0,0,0,42,0,39,0,0],[0,0,0,0,0,0,0,8,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,231,231,49,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,232,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,130,131,132,133,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,131,131,133,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,233,233,133,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,234,233,233,133,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,235,236,237,238,239,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,54,56,56,57,58,0,59,0,0,0,61,0,0,62,0],[0,0,0,240,241,242,243,57,58,0,59,0,0,244,245,0,0,62,0],[0,0,0,0,0,246,246,0,58,0,59,0,0,0,139,0,0,0,0],[0,136,0,247,0,248,248,57,58,0,59,0,0,0,139,0,136,0,0],[0,249,151,0,0,0,0,0,0,0,0,0,0,0,0,0,249,0,0],[0,136,0,0,0,246,250,0,58,0,59,0,0,0,139,0,136,0,0],[0,0,0,0,0,251,251,57,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,143,144,145,146,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,144,144,146,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,252,252,146,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,253,252,252,146,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,254,255,256,257,258,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,59,0,0,0,0,0,0,0,0],[0,0,0,0,0,147,0,0,0,0,59,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,259,0,0,0,0,0,0,0,0],[0,0,0,260,241,261,262,57,58,0,59,0,0,263,245,0,0,62,0],[0,0,0,0,0,246,246,0,58,0,59,0,0,0,0,0,0,0,0],[0,0,0,0,0,137,264,0,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,265,141,141,57,58,0,0,0,0,0,265,0,0,0,0],[0,249,151,0,0,137,264,0,58,0,59,0,0,0,139,0,249,0,0],[0,0,0,221,116,222,222,8,9,0,10,0,0,0,120,0,0,16,0],[0,0,0,0,225,44,44,8,9,0,0,0,0,158,225,0,0,0,0],[0,0,0,155,116,156,222,8,9,0,10,0,0,158,120,0,0,16,0],[0,0,0,155,116,222,222,8,9,0,10,0,0,0,120,0,0,16,0],[0,0,0,0,43,266,266,8,161,0,24,0,0,12,267,0,0,0,0],[0,75,0,176,43,268,268,269,161,0,24,0,0,0,267,0,75,0,0],[0,0,0,0,0,270,0,0,271,0,162,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,272,0,0,0,0,0,0,0,0],[0,273,274,0,0,40,41,0,9,0,10,0,0,0,42,0,273,0,0],[0,0,0,40,0,123,123,8,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,121,275,0,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,72,0,0,0,0,0,0,0,0],[0,0,0,0,0,166,0,0,0,0,72,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,276,0,0,0,0,0,0,0,0],[0,0,0,277,170,278,278,22,23,0,24,0,0,0,174,0,0,27,0],[0,279,0,0,0,76,280,0,23,0,24,0,0,0,78,0,279,0,0],[0,0,0,0,281,80,80,22,23,0,0,0,0,173,281,0,0,0,0],[0,0,0,169,170,171,278,22,23,0,24,0,0,173,174,0,0,27,0],[0,0,0,169,170,278,278,22,23,0,24,0,0,0,174,0,0,27,0],[0,282,90,0,0,76,280,0,23,0,24,0,0,0,78,0,282,0,0],[0,0,0,0,0,0,0,0,23,0,0,0,0,0,0,0,0,0,0],[0,75,0,0,0,175,175,0,23,0,24,0,0,0,78,0,75,0,0],[0,0,0,0,0,80,80,22,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,283,0,284,285,0,23,0,24,0,0,286,0,0,0,0,0],[0,75,0,176,0,175,175,0,23,0,24,0,0,0,78,0,75,0,0],[0,0,0,0,0,0,0,22,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,287,287,85,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,288,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,184,185,186,187,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,185,185,187,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,289,289,187,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,290,289,289,187,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,291,292,293,294,295,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,277,170,278,278,22,23,0,24,0,0,0,174,0,0,27,0],[0,0,0,0,281,80,80,22,23,0,0,0,0,192,281,0,0,0,0],[0,0,0,189,170,190,278,22,23,0,24,0,0,192,174,0,0,27,0],[0,0,0,189,170,278,278,22,23,0,24,0,0,0,174,0,0,27,0],[0,0,0,76,0,177,177,22,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,175,296,0,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,297,196,298,298,32,33,0,34,0,0,0,200,0,0,37,0],[0,299,0,0,0,97,300,0,33,0,34,0,0,0,99,0,299,0,0],[0,0,0,0,301,101,101,32,33,0,0,0,0,199,301,0,0,0,0],[0,0,0,195,196,197,298,32,33,0,34,0,0,199,200,0,0,37,0],[0,0,0,195,196,298,298,32,33,0,34,0,0,0,200,0,0,37,0],[0,302,111,0,0,97,300,0,33,0,34,0,0,0,99,0,302,0,0],[0,0,0,0,0,0,0,0,33,0,0,0,0,0,0,0,0,0,0],[0,96,0,0,0,201,201,0,33,0,34,0,0,0,99,0,96,0,0],[0,0,0,0,0,101,101,32,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,303,0,304,305,0,33,0,34,0,0,306,0,0,0,0,0],[0,96,0,202,0,201,201,0,33,0,34,0,0,0,99,0,96,0,0],[0,0,0,0,0,0,0,32,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,307,307,106,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,308,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,210,211,212,213,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,211,211,213,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,309,309,213,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,310,309,309,213,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,311,312,313,314,315,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,297,196,298,298,32,33,0,34,0,0,0,200,0,0,37,0],[0,0,0,0,301,101,101,32,33,0,0,0,0,218,301,0,0,0,0],[0,0,0,215,196,216,298,32,33,0,34,0,0,218,200,0,0,37,0],[0,0,0,215,196,298,298,32,33,0,34,0,0,0,200,0,0,37,0],[0,0,0,97,0,203,203,32,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,201,316,0,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,116,222,222,8,9,0,10,0,0,0,120,0,0,16,0],[0,0,0,0,225,44,44,8,9,0,0,0,0,0,225,0,0,0,0],[0,0,0,317,318,319,320,8,9,0,10,0,0,321,322,0,0,16,0],[0,223,0,323,0,123,123,8,9,0,10,0,0,0,42,0,223,0,0],[0,223,0,0,0,121,324,0,9,0,10,0,0,0,42,0,223,0,0],[0,0,0,325,318,326,327,8,9,0,10,0,0,328,322,0,0,16,0],[0,0,0,64,0,121,121,0,9,0,10,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,9,0,0,0,0,230,0,0,0,0,0],[0,0,0,227,0,228,121,0,9,0,10,0,0,230,0,0,0,0,0],[0,0,0,227,0,121,121,0,9,0,10,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,49,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,46,0,0],[0,0,0,0,0,329,329,133,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,330,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,236,237,238,239,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,237,237,239,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,331,331,239,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,332,331,331,239,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,333,40,121,334,0,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,335,241,336,336,57,58,0,59,0,0,0,245,0,0,62,0],[0,337,0,0,0,137,338,0,58,0,59,0,0,0,139,0,337,0,0],[0,0,0,0,339,141,141,57,58,0,0,0,0,244,339,0,0,0,0],[0,0,0,240,241,242,336,57,58,0,59,0,0,244,245,0,0,62,0],[0,0,0,240,241,336,336,57,58,0,59,0,0,0,245,0,0,62,0],[0,340,151,0,0,137,338,0,58,0,59,0,0,0,139,0,340,0,0],[0,0,0,0,0,0,0,0,58,0,0,0,0,0,0,0,0,0,0],[0,136,0,0,0,246,246,0,58,0,59,0,0,0,139,0,136,0,0],[0,0,0,0,0,141,141,57,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,341,0,342,343,0,58,0,59,0,0,344,0,0,0,0,0],[0,136,0,247,0,246,246,0,58,0,59,0,0,0,139,0,136,0,0],[0,0,0,0,0,0,0,57,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,345,345,146,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,346,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,255,256,257,258,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,256,256,258,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,347,347,258,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,348,347,347,258,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,349,350,351,352,353,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,335,241,336,336,57,58,0,59,0,0,0,245,0,0,62,0],[0,0,0,0,339,141,141,57,58,0,0,0,0,263,339,0,0,0,0],[0,0,0,260,241,261,336,57,58,0,59,0,0,263,245,0,0,62,0],[0,0,0,260,241,336,336,57,58,0,59,0,0,0,245,0,0,62,0],[0,0,0,137,0,248,248,57,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,246,354,0,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,126,126,8,23,0,0,0,0,0,0,0,0,0,0],[0,355,90,0,0,121,125,0,9,0,10,0,0,0,42,0,355,0,0],[0,0,0,0,0,356,356,269,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,357,358,359,360,361,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,162,0,0,0,0,0,0,0,0],[0,0,0,0,0,270,0,0,0,0,162,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,363,0,0,0,0,0,0,0,0],[0,0,0,364,116,365,366,8,161,0,162,0,0,367,120,0,0,16,0],[0,0,0,0,0,368,368,0,161,0,162,0,0,0,0,0,0,0,0],[0,0,0,40,0,121,121,0,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,170,278,278,22,23,0,24,0,0,0,174,0,0,27,0],[0,0,0,0,281,80,80,22,23,0,0,0,0,0,281,0,0,0,0],[0,0,0,369,370,371,372,22,23,0,24,0,0,373,374,0,0,27,0],[0,279,0,375,0,177,177,22,23,0,24,0,0,0,78,0,279,0,0],[0,279,0,0,0,175,376,0,23,0,24,0,0,0,78,0,279,0,0],[0,0,0,377,370,378,379,22,23,0,24,0,0,380,374,0,0,27,0],[0,0,0,90,0,175,175,0,23,0,24,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,23,0,0,0,0,286,0,0,0,0,0],[0,0,0,283,0,284,175,0,23,0,24,0,0,286,0,0,0,0,0],[0,0,0,283,0,175,175,0,23,0,24,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,85,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,82,0,0],[0,0,0,0,0,381,381,187,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,382,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,292,293,294,295,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,293,293,295,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,0,383,383,295,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,384,383,383,295,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,385,76,175,386,0,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,76,0,175,175,0,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,196,298,298,32,33,0,34,0,0,0,200,0,0,37,0],[0,0,0,0,301,101,101,32,33,0,0,0,0,0,301,0,0,0,0],[0,0,0,387,388,389,390,32,33,0,34,0,0,391,392,0,0,37,0],[0,299,0,393,0,203,203,32,33,0,34,0,0,0,99,0,299,0,0],[0,299,0,0,0,201,394,0,33,0,34,0,0,0,99,0,299,0,0],[0,0,0,395,388,396,397,32,33,0,34,0,0,398,392,0,0,37,0],[0,0,0,111,0,201,201,0,33,0,34,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,33,0,0,0,0,306,0,0,0,0,0],[0,0,0,303,0,304,201,0,33,0,34,0,0,306,0,0,0,0,0],[0,0,0,303,0,201,201,0,33,0,34,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,106,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,103,0,0],[0,0,0,0,0,399,399,213,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,400,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,312,313,314,315,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,313,313,315,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,0,401,401,315,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,402,401,401,315,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,403,97,201,404,0,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,97,0,201,201,0,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,405,318,406,406,8,9,0,10,0,0,0,322,0,0,16,0],[0,407,0,0,0,40,408,0,9,0,10,0,0,0,42,0,407,0,0],[0,0,0,0,409,44,44,8,9,0,0,0,0,321,409,0,0,0,0],[0,0,0,317,318,319,406,8,9,0,10,0,0,321,322,0,0,16,0],[0,0,0,317,318,406,406,8,9,0,10,0,0,0,322,0,0,16,0],[0,410,64,0,0,40,408,0,9,0,10,0,0,0,42,0,410,0,0],[0,223,0,0,0,121,121,0,9,0,10,0,0,0,42,0,223,0,0],[0,223,0,323,0,121,121,0,9,0,10,0,0,0,42,0,223,0,0],[0,0,0,405,318,406,406,8,9,0,10,0,0,0,322,0,0,16,0],[0,0,0,0,409,44,44,8,9,0,0,0,0,328,409,0,0,0,0],[0,0,0,325,318,326,406,8,9,0,10,0,0,328,322,0,0,16,0],[0,0,0,325,318,406,406,8,9,0,10,0,0,0,322,0,0,16,0],[0,0,0,0,0,0,0,133,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,130,0,0],[0,0,0,0,0,411,411,239,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,412,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,40,121,334,0,9,0,10,0,0,0,42,0,0,0,0],[0,0,0,0,413,0,0,0,9,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,241,336,336,57,58,0,59,0,0,0,245,0,0,62,0],[0,0,0,0,339,141,141,57,58,0,0,0,0,0,339,0,0,0,0],[0,0,0,414,415,416,417,57,58,0,59,0,0,418,419,0,0,62,0],[0,337,0,420,0,248,248,57,58,0,59,0,0,0,139,0,337,0,0],[0,337,0,0,0,246,421,0,58,0,59,0,0,0,139,0,337,0,0],[0,0,0,422,415,423,424,57,58,0,59,0,0,425,419,0,0,62,0],[0,0,0,151,0,246,246,0,58,0,59,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,58,0,0,0,0,344,0,0,0,0,0],[0,0,0,341,0,342,246,0,58,0,59,0,0,344,0,0,0,0,0],[0,0,0,341,0,246,246,0,58,0,59,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,146,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,143,0,0],[0,0,0,0,0,426,426,258,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,427,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,350,351,352,353,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,351,351,353,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,0,428,428,353,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,429,428,428,353,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,430,137,246,431,0,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,137,0,246,246,0,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,432,116,433,434,8,161,0,162,0,0,435,120,0,0,16,0],[0,0,0,0,0,180,180,269,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,358,359,360,361,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,359,359,361,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,436,436,361,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,437,436,436,361,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,438,439,440,441,442,161,0,162,0,0,0,362,0,0,0,0],[0,443,274,0,0,0,0,0,0,0,0,0,0,0,0,0,443,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,444,116,445,445,8,161,0,162,0,0,0,120,0,0,16,0],[0,0,0,0,225,44,44,8,161,0,0,0,0,367,225,0,0,0,0],[0,0,0,364,116,365,445,8,161,0,162,0,0,367,120,0,0,16,0],[0,0,0,364,116,445,445,8,161,0,162,0,0,0,120,0,0,16,0],[0,0,0,0,0,0,0,0,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,446,370,447,447,22,23,0,24,0,0,0,374,0,0,27,0],[0,448,0,0,0,76,449,0,23,0,24,0,0,0,78,0,448,0,0],[0,0,0,0,450,80,80,22,23,0,0,0,0,373,450,0,0,0,0],[0,0,0,369,370,371,447,22,23,0,24,0,0,373,374,0,0,27,0],[0,0,0,369,370,447,447,22,23,0,24,0,0,0,374,0,0,27,0],[0,451,90,0,0,76,449,0,23,0,24,0,0,0,78,0,451,0,0],[0,279,0,0,0,175,175,0,23,0,24,0,0,0,78,0,279,0,0],[0,279,0,375,0,175,175,0,23,0,24,0,0,0,78,0,279,0,0],[0,0,0,446,370,447,447,22,23,0,24,0,0,0,374,0,0,27,0],[0,0,0,0,450,80,80,22,23,0,0,0,0,380,450,0,0,0,0],[0,0,0,377,370,378,447,22,23,0,24,0,0,380,374,0,0,27,0],[0,0,0,377,370,447,447,22,23,0,24,0,0,0,374,0,0,27,0],[0,0,0,0,0,0,0,187,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,184,0,0],[0,0,0,0,0,452,452,295,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,453,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,76,175,386,0,23,0,24,0,0,0,78,0,0,0,0],[0,0,0,0,454,0,0,0,23,0,0,0,0,0,0,0,0,0,0],[0,0,0,455,388,456,456,32,33,0,34,0,0,0,392,0,0,37,0],[0,457,0,0,0,97,458,0,33,0,34,0,0,0,99,0,457,0,0],[0,0,0,0,459,101,101,32,33,0,0,0,0,391,459,0,0,0,0],[0,0,0,387,388,389,456,32,33,0,34,0,0,391,392,0,0,37,0],[0,0,0,387,388,456,456,32,33,0,34,0,0,0,392,0,0,37,0],[0,460,111,0,0,97,458,0,33,0,34,0,0,0,99,0,460,0,0],[0,299,0,0,0,201,201,0,33,0,34,0,0,0,99,0,299,0,0],[0,299,0,393,0,201,201,0,33,0,34,0,0,0,99,0,299,0,0],[0,0,0,455,388,456,456,32,33,0,34,0,0,0,392,0,0,37,0],[0,0,0,0,459,101,101,32,33,0,0,0,0,398,459,0,0,0,0],[0,0,0,395,388,396,456,32,33,0,34,0,0,398,392,0,0,37,0],[0,0,0,395,388,456,456,32,33,0,34,0,0,0,392,0,0,37,0],[0,0,0,0,0,0,0,213,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,210,0,0],[0,0,0,0,0,461,461,315,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,462,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,97,201,404,0,33,0,34,0,0,0,99,0,0,0,0],[0,0,0,0,463,0,0,0,33,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,318,406,406,8,9,0,10,0,0,0,322,0,0,16,0],[0,0,0,0,409,44,44,8,9,0,0,0,0,0,409,0,0,0,0],[0,0,0,464,465,466,467,8,9,0,10,0,0,468,469,0,0,16,0],[0,407,0,470,0,123,123,8,9,0,10,0,0,0,42,0,407,0,0],[0,407,0,0,0,121,471,0,9,0,10,0,0,0,42,0,407,0,0],[0,0,0,472,465,473,474,8,9,0,10,0,0,475,469,0,0,16,0],[0,0,0,0,0,0,0,239,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,236,0,0],[0,0,0,0,0,0,476,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,477,415,478,478,57,58,0,59,0,0,0,419,0,0,62,0],[0,479,0,0,0,137,480,0,58,0,59,0,0,0,139,0,479,0,0],[0,0,0,0,481,141,141,57,58,0,0,0,0,418,481,0,0,0,0],[0,0,0,414,415,416,478,57,58,0,59,0,0,418,419,0,0,62,0],[0,0,0,414,415,478,478,57,58,0,59,0,0,0,419,0,0,62,0],[0,482,151,0,0,137,480,0,58,0,59,0,0,0,139,0,482,0,0],[0,337,0,0,0,246,246,0,58,0,59,0,0,0,139,0,337,0,0],[0,337,0,420,0,246,246,0,58,0,59,0,0,0,139,0,337,0,0],[0,0,0,477,415,478,478,57,58,0,59,0,0,0,419,0,0,62,0],[0,0,0,0,481,141,141,57,58,0,0,0,0,425,481,0,0,0,0],[0,0,0,422,415,423,478,57,58,0,59,0,0,425,419,0,0,62,0],[0,0,0,422,415,478,478,57,58,0,59,0,0,0,419,0,0,62,0],[0,0,0,0,0,0,0,258,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,0,0],[0,0,0,0,0,483,483,353,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,484,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,137,246,431,0,58,0,59,0,0,0,139,0,0,0,0],[0,0,0,0,485,0,0,0,58,0,0,0,0,0,0,0,0,0,0],[0,0,0,444,116,445,445,8,161,0,162,0,0,0,120,0,0,16,0],[0,0,0,0,225,44,44,8,161,0,0,0,0,435,225,0,0,0,0],[0,0,0,432,116,433,445,8,161,0,162,0,0,435,120,0,0,16,0],[0,0,0,432,116,445,445,8,161,0,162,0,0,0,120,0,0,16,0],[0,0,0,0,0,486,486,361,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,487,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,439,440,441,442,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,440,440,442,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,488,488,442,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,489,488,488,442,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,490,491,492,493,494,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,495,0,496,497,0,161,0,162,0,0,498,0,0,0,0,0],[0,0,0,0,116,445,445,8,161,0,162,0,0,0,120,0,0,16,0],[0,0,0,0,225,44,44,8,161,0,0,0,0,0,225,0,0,0,0],[0,0,0,0,370,447,447,22,23,0,24,0,0,0,374,0,0,27,0],[0,0,0,0,450,80,80,22,23,0,0,0,0,0,450,0,0,0,0],[0,0,0,499,500,501,502,22,23,0,24,0,0,503,504,0,0,27,0],[0,448,0,505,0,177,177,22,23,0,24,0,0,0,78,0,448,0,0],[0,448,0,0,0,175,506,0,23,0,24,0,0,0,78,0,448,0,0],[0,0,0,507,500,508,509,22,23,0,24,0,0,510,504,0,0,27,0],[0,0,0,0,0,0,0,295,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,292,0,0],[0,0,0,0,0,0,511,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,388,456,456,32,33,0,34,0,0,0,392,0,0,37,0],[0,0,0,0,459,101,101,32,33,0,0,0,0,0,459,0,0,0,0],[0,0,0,512,513,514,515,32,33,0,34,0,0,516,517,0,0,37,0],[0,457,0,518,0,203,203,32,33,0,34,0,0,0,99,0,457,0,0],[0,457,0,0,0,201,519,0,33,0,34,0,0,0,99,0,457,0,0],[0,0,0,520,513,521,522,32,33,0,34,0,0,523,517,0,0,37,0],[0,0,0,0,0,0,0,315,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,312,0,0],[0,0,0,0,0,0,524,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,525,465,526,526,8,9,0,10,0,0,0,469,0,0,16,0],[0,527,0,0,0,40,528,0,9,0,10,0,0,0,42,0,527,0,0],[0,0,0,0,529,44,44,8,9,0,0,0,0,468,529,0,0,0,0],[0,0,0,464,465,466,526,8,9,0,10,0,0,468,469,0,0,16,0],[0,0,0,464,465,526,526,8,9,0,10,0,0,0,469,0,0,16,0],[0,530,64,0,0,40,528,0,9,0,10,0,0,0,42,0,530,0,0],[0,407,0,0,0,121,121,0,9,0,10,0,0,0,42,0,407,0,0],[0,407,0,470,0,121,121,0,9,0,10,0,0,0,42,0,407,0,0],[0,0,0,525,465,526,526,8,9,0,10,0,0,0,469,0,0,16,0],[0,0,0,0,529,44,44,8,9,0,0,0,0,475,529,0,0,0,0],[0,0,0,472,465,473,526,8,9,0,10,0,0,475,469,0,0,16,0],[0,0,0,472,465,526,526,8,9,0,10,0,0,0,469,0,0,16,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,40,0,0],[0,0,0,0,415,478,478,57,58,0,59,0,0,0,419,0,0,62,0],[0,0,0,0,481,141,141,57,58,0,0,0,0,0,481,0,0,0,0],[0,0,0,531,532,533,534,57,58,0,59,0,0,535,536,0,0,62,0],[0,479,0,537,0,248,248,57,58,0,59,0,0,0,139,0,479,0,0],[0,479,0,0,0,246,538,0,58,0,59,0,0,0,139,0,479,0,0],[0,0,0,539,532,540,541,57,58,0,59,0,0,542,536,0,0,62,0],[0,0,0,0,0,0,0,353,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,350,0,0],[0,0,0,0,0,0,543,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,361,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,358,0,0],[0,0,0,0,0,544,544,442,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,545,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,491,492,493,494,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,492,492,494,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,546,546,494,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,547,546,546,494,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,548,549,368,550,0,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,274,0,368,368,0,161,0,162,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,161,0,0,0,0,498,0,0,0,0,0],[0,0,0,495,0,496,368,0,161,0,162,0,0,498,0,0,0,0,0],[0,0,0,495,0,368,368,0,161,0,162,0,0,0,0,0,0,0,0],[0,0,0,551,500,552,552,22,23,0,24,0,0,0,504,0,0,27,0],[0,553,0,0,0,76,554,0,23,0,24,0,0,0,78,0,553,0,0],[0,0,0,0,555,80,80,22,23,0,0,0,0,503,555,0,0,0,0],[0,0,0,499,500,501,552,22,23,0,24,0,0,503,504,0,0,27,0],[0,0,0,499,500,552,552,22,23,0,24,0,0,0,504,0,0,27,0],[0,556,90,0,0,76,554,0,23,0,24,0,0,0,78,0,556,0,0],[0,448,0,0,0,175,175,0,23,0,24,0,0,0,78,0,448,0,0],[0,448,0,505,0,175,175,0,23,0,24,0,0,0,78,0,448,0,0],[0,0,0,551,500,552,552,22,23,0,24,0,0,0,504,0,0,27,0],[0,0,0,0,555,80,80,22,23,0,0,0,0,510,555,0,0,0,0],[0,0,0,507,500,508,552,22,23,0,24,0,0,510,504,0,0,27,0],[0,0,0,507,500,552,552,22,23,0,24,0,0,0,504,0,0,27,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,76,0,0],[0,0,0,557,513,558,558,32,33,0,34,0,0,0,517,0,0,37,0],[0,559,0,0,0,97,560,0,33,0,34,0,0,0,99,0,559,0,0],[0,0,0,0,561,101,101,32,33,0,0,0,0,516,561,0,0,0,0],[0,0,0,512,513,514,558,32,33,0,34,0,0,516,517,0,0,37,0],[0,0,0,512,513,558,558,32,33,0,34,0,0,0,517,0,0,37,0],[0,562,111,0,0,97,560,0,33,0,34,0,0,0,99,0,562,0,0],[0,457,0,0,0,201,201,0,33,0,34,0,0,0,99,0,457,0,0],[0,457,0,518,0,201,201,0,33,0,34,0,0,0,99,0,457,0,0],[0,0,0,557,513,558,558,32,33,0,34,0,0,0,517,0,0,37,0],[0,0,0,0,561,101,101,32,33,0,0,0,0,523,561,0,0,0,0],[0,0,0,520,513,521,558,32,33,0,34,0,0,523,517,0,0,37,0],[0,0,0,520,513,558,558,32,33,0,34,0,0,0,517,0,0,37,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,97,0,0],[0,0,0,0,465,526,526,8,9,0,10,0,0,0,469,0,0,16,0],[0,0,0,0,529,44,44,8,9,0,0,0,0,0,529,0,0,0,0],[0,0,0,563,66,564,565,8,9,0,10,0,0,566,68,0,0,16,0],[0,527,0,567,0,123,123,8,9,0,10,0,0,0,42,0,527,0,0],[0,527,0,0,0,121,568,0,9,0,10,0,0,0,42,0,527,0,0],[0,0,0,569,66,570,571,8,9,0,10,0,0,572,68,0,0,16,0],[0,0,0,573,532,574,574,57,58,0,59,0,0,0,536,0,0,62,0],[0,575,0,0,0,137,576,0,58,0,59,0,0,0,139,0,575,0,0],[0,0,0,0,577,141,141,57,58,0,0,0,0,535,577,0,0,0,0],[0,0,0,531,532,533,574,57,58,0,59,0,0,535,536,0,0,62,0],[0,0,0,531,532,574,574,57,58,0,59,0,0,0,536,0,0,62,0],[0,578,151,0,0,137,576,0,58,0,59,0,0,0,139,0,578,0,0],[0,479,0,0,0,246,246,0,58,0,59,0,0,0,139,0,479,0,0],[0,479,0,537,0,246,246,0,58,0,59,0,0,0,139,0,479,0,0],[0,0,0,573,532,574,574,57,58,0,59,0,0,0,536,0,0,62,0],[0,0,0,0,577,141,141,57,58,0,0,0,0,542,577,0,0,0,0],[0,0,0,539,532,540,574,57,58,0,59,0,0,542,536,0,0,62,0],[0,0,0,539,532,574,574,57,58,0,59,0,0,0,536,0,0,62,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,137,0,0],[0,0,0,0,0,0,0,442,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,439,0,0],[0,0,0,0,0,579,579,494,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,580,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,549,368,550,0,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,0,368,368,0,161,0,162,0,0,0,362,0,0,0,0],[0,0,0,0,581,0,0,0,161,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,500,552,552,22,23,0,24,0,0,0,504,0,0,27,0],[0,0,0,0,555,80,80,22,23,0,0,0,0,0,555,0,0,0,0],[0,0,0,582,91,583,584,22,23,0,24,0,0,585,93,0,0,27,0],[0,553,0,586,0,177,177,22,23,0,24,0,0,0,78,0,553,0,0],[0,553,0,0,0,175,587,0,23,0,24,0,0,0,78,0,553,0,0],[0,0,0,588,91,589,590,22,23,0,24,0,0,591,93,0,0,27,0],[0,0,0,0,513,558,558,32,33,0,34,0,0,0,517,0,0,37,0],[0,0,0,0,561,101,101,32,33,0,0,0,0,0,561,0,0,0,0],[0,0,0,592,112,593,594,32,33,0,34,0,0,595,114,0,0,37,0],[0,559,0,596,0,203,203,32,33,0,34,0,0,0,99,0,559,0,0],[0,559,0,0,0,201,597,0,33,0,34,0,0,0,99,0,559,0,0],[0,0,0,598,112,599,600,32,33,0,34,0,0,601,114,0,0,37,0],[0,0,0,602,66,67,67,8,9,0,10,0,0,0,68,0,0,16,0],[0,0,0,0,165,44,44,8,9,0,0,0,0,566,165,0,0,0,0],[0,0,0,563,66,564,67,8,9,0,10,0,0,566,68,0,0,16,0],[0,0,0,563,66,67,67,8,9,0,10,0,0,0,68,0,0,16,0],[0,527,0,0,0,121,121,0,9,0,10,0,0,0,42,0,527,0,0],[0,527,0,567,0,121,121,0,9,0,10,0,0,0,42,0,527,0,0],[0,0,0,602,66,67,67,8,9,0,10,0,0,0,68,0,0,16,0],[0,0,0,0,165,44,44,8,9,0,0,0,0,572,165,0,0,0,0],[0,0,0,569,66,570,67,8,9,0,10,0,0,572,68,0,0,16,0],[0,0,0,569,66,67,67,8,9,0,10,0,0,0,68,0,0,16,0],[0,0,0,0,532,574,574,57,58,0,59,0,0,0,536,0,0,62,0],[0,0,0,0,577,141,141,57,58,0,0,0,0,0,577,0,0,0,0],[0,0,0,603,152,604,605,57,58,0,59,0,0,606,154,0,0,62,0],[0,575,0,607,0,248,248,57,58,0,59,0,0,0,139,0,575,0,0],[0,575,0,0,0,246,608,0,58,0,59,0,0,0,139,0,575,0,0],[0,0,0,609,152,610,611,57,58,0,59,0,0,612,154,0,0,62,0],[0,0,0,0,0,0,0,494,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,491,0,0],[0,0,0,0,0,0,613,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,614,91,92,92,22,23,0,24,0,0,0,93,0,0,27,0],[0,0,0,0,194,80,80,22,23,0,0,0,0,585,194,0,0,0,0],[0,0,0,582,91,583,92,22,23,0,24,0,0,585,93,0,0,27,0],[0,0,0,582,91,92,92,22,23,0,24,0,0,0,93,0,0,27,0],[0,553,0,0,0,175,175,0,23,0,24,0,0,0,78,0,553,0,0],[0,553,0,586,0,175,175,0,23,0,24,0,0,0,78,0,553,0,0],[0,0,0,614,91,92,92,22,23,0,24,0,0,0,93,0,0,27,0],[0,0,0,0,194,80,80,22,23,0,0,0,0,591,194,0,0,0,0],[0,0,0,588,91,589,92,22,23,0,24,0,0,591,93,0,0,27,0],[0,0,0,588,91,92,92,22,23,0,24,0,0,0,93,0,0,27,0],[0,0,0,615,112,113,113,32,33,0,34,0,0,0,114,0,0,37,0],[0,0,0,0,220,101,101,32,33,0,0,0,0,595,220,0,0,0,0],[0,0,0,592,112,593,113,32,33,0,34,0,0,595,114,0,0,37,0],[0,0,0,592,112,113,113,32,33,0,34,0,0,0,114,0,0,37,0],[0,559,0,0,0,201,201,0,33,0,34,0,0,0,99,0,559,0,0],[0,559,0,596,0,201,201,0,33,0,34,0,0,0,99,0,559,0,0],[0,0,0,615,112,113,113,32,33,0,34,0,0,0,114,0,0,37,0],[0,0,0,0,220,101,101,32,33,0,0,0,0,601,220,0,0,0,0],[0,0,0,598,112,599,113,32,33,0,34,0,0,601,114,0,0,37,0],[0,0,0,598,112,113,113,32,33,0,34,0,0,0,114,0,0,37,0],[0,0,0,0,66,67,67,8,9,0,10,0,0,0,68,0,0,16,0],[0,0,0,616,152,153,153,57,58,0,59,0,0,0,154,0,0,62,0],[0,0,0,0,265,141,141,57,58,0,0,0,0,606,265,0,0,0,0],[0,0,0,603,152,604,153,57,58,0,59,0,0,606,154,0,0,62,0],[0,0,0,603,152,153,153,57,58,0,59,0,0,0,154,0,0,62,0],[0,575,0,0,0,246,246,0,58,0,59,0,0,0,139,0,575,0,0],[0,575,0,607,0,246,246,0,58,0,59,0,0,0,139,0,575,0,0],[0,0,0,616,152,153,153,57,58,0,59,0,0,0,154,0,0,62,0],[0,0,0,0,265,141,141,57,58,0,0,0,0,612,265,0,0,0,0],[0,0,0,609,152,610,153,57,58,0,59,0,0,612,154,0,0,62,0],[0,0,0,609,152,153,153,57,58,0,59,0,0,0,154,0,0,62,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,549,0,0],[0,0,0,0,91,92,92,22,23,0,24,0,0,0,93,0,0,27,0],[0,0,0,0,112,113,113,32,33,0,34,0,0,0,114,0,0,37,0],[0,0,0,0,152,153,153,57,58,0,59,0,0,0,154,0,0,62,0]],"accepting":[false,true,true,true,true,true,false,false,true,true,true,true,true,true,true,true,true,true,true,true,false,true,true,true,true,true,true,true,true,true,false,true,true,true,true,true,true,true,true,true,true,true,false,true,false,true,true,false,false,true,true,true,true,true,true,false,false,true,true,true,true,true,true,true,true,true,true,false,true,true,false,true,true,true,false,true,true,true,false,true,false,true,true,false,false,true,true,true,true,true,true,true,false,true,true,false,true,true,true,false,true,false,true,true,false,false,true,true,true,true,true,true,true,false,true,true,true,false,true,true,true,false,true,false,true,true,false,false,false,true,true,false,false,true,true,true,true,true,true,false,true,false,true,true,false,false,true,true,true,true,true,true,true,false,true,true,false,true,true,true,true,true,true,true,true,true,true,true,true,true,true,false,true,true,true,false,true,false,true,true,false,false,false,true,true,false,false,true,true,true,false,true,true,true,true,true,true,false,true,true,true,false,true,false,true,true,false,false,false,true,true,false,false,true,true,true,false,true,true,true,true,true,false,true,true,true,true,true,false,true,true,false,false,false,false,true,true,false,false,true,true,true,false,true,true,true,false,true,false,true,true,false,false,false,true,true,false,false,true,true,true,false,true,true,true,true,false,true,false,true,true,true,true,true,true,true,true,true,false,true,true,true,true,true,false,true,true,false,false,false,false,true,true,false,false,true,true,true,false,true,true,true,true,true,false,true,true,false,false,false,false,true,true,false,false,true,true,true,true,false,true,true,true,true,true,true,false,true,true,false,false,false,false,true,false,true,false,true,true,true,true,true,false,true,true,false,false,false,false,true,true,false,false,true,true,true,false,true,true,false,false,true,false,true,true,false,true,true,false,true,true,false,true,true,true,true,true,true,false,true,true,false,false,false,false,true,false,true,true,false,true,true,true,true,true,true,false,true,true,false,false,false,false,true,false,true,false,true,true,true,true,false,false,false,true,true,false,true,true,true,true,true,true,false,true,true,false,false,false,false,true,false,true,false,true,true,false,false,true,true,false,false,true,true,true,false,true,false,true,true,true,true,false,false,false,true,false,true,true,true,true,false,false,false,true,true,false,true,true,true,true,true,true,false,true,true,false,true,false,true,true,true,true,false,false,false,false,false,false,false,true,true,false,false,true,true,false,true,true,true,true,false,true,true,true,true,true,true,false,true,true,false,true,true,false,true,true,true,true,true,true,false,true,true,false,true,false,true,true,true,true,true,true,false,true,true,true,true,true,true,false,true,true,false,false,false,false,false,true,true,false,true,false,true,true,true,true,true,false,true,true,true,true,true,false,true,true,true,true,true,false,true,true,true,false,true,true,true,true,false,false,false,true,false,true,true,true,true,true,false,true,true,true,false,true,true,true,true,true,false,true,true,true,true,false,true,true,true,true,true,false,true,true,false,true,true,true],"tags":[[],["broken_cluster"],["consonant_syllable"],["vowel_syllable"],["broken_cluster"],["broken_cluster"],[],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["standalone_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["consonant_syllable"],["broken_cluster"],["symbol_cluster"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],[],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["standalone_cluster"],["standalone_cluster"],[],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["broken_cluster"],["broken_cluster"],["consonant_syllable","broken_cluster"],["broken_cluster"],[],["broken_cluster"],["symbol_cluster"],[],["symbol_cluster"],["symbol_cluster"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],[],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],[],[],[],["broken_cluster"],["broken_cluster"],[],[],["broken_cluster"],["broken_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],[],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["symbol_cluster"],["symbol_cluster"],["symbol_cluster"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],[],[],[],["consonant_syllable"],["consonant_syllable"],[],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],[],[],["vowel_syllable"],["vowel_syllable"],[],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],[],[],[],[],["broken_cluster"],["broken_cluster"],[],[],["broken_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],[],[],[],["standalone_cluster"],["standalone_cluster"],[],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["broken_cluster"],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["broken_cluster"],["symbol_cluster"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],[],[],[],[],["consonant_syllable"],["consonant_syllable"],[],[],["consonant_syllable"],["consonant_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],[],[],[],["vowel_syllable"],["vowel_syllable"],[],[],["vowel_syllable"],["vowel_syllable"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],[],[],[],[],["broken_cluster"],[],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],[],[],[],[],["standalone_cluster"],["standalone_cluster"],[],[],["standalone_cluster"],["standalone_cluster"],["consonant_syllable","broken_cluster"],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],[],["consonant_syllable","broken_cluster"],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],[],[],[],[],["consonant_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],[],[],[],["vowel_syllable"],[],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],[],[],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],[],[],[],[],["standalone_cluster"],[],["consonant_syllable","broken_cluster"],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],[],[],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],[],[],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],[],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],[],[],[],[],[],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],[],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],[],[],[],[],[],["consonant_syllable","broken_cluster"],["consonant_syllable","broken_cluster"],[],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],[],[],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],["consonant_syllable"],[],["consonant_syllable"],["consonant_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],["vowel_syllable"],[],["vowel_syllable"],["vowel_syllable"],["broken_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],["standalone_cluster"],[],["standalone_cluster"],["standalone_cluster"],[],["consonant_syllable"],["vowel_syllable"],["standalone_cluster"]]}');var nm={};nm=JSON.parse('{"categories":["O","IND","S","GB","B","FM","CGJ","VMAbv","VMPst","VAbv","VPst","CMBlw","VPre","VBlw","H","VMBlw","CMAbv","MBlw","CS","R","SUB","MPst","MPre","FAbv","FPst","FBlw","null","SMAbv","SMBlw","VMPre","ZWNJ","ZWJ","WJ","M","VS","N","HN","MAbv"],"decompositions":{"2507":[2503,2494],"2508":[2503,2519],"2888":[2887,2902],"2891":[2887,2878],"2892":[2887,2903],"3018":[3014,3006],"3019":[3015,3006],"3020":[3014,3031],"3144":[3142,3158],"3264":[3263,3285],"3271":[3270,3285],"3272":[3270,3286],"3274":[3270,3266],"3275":[3270,3266,3285],"3402":[3398,3390],"3403":[3399,3390],"3404":[3398,3415],"3546":[3545,3530],"3548":[3545,3535],"3549":[3545,3535,3530],"3550":[3545,3551],"3635":[3661,3634],"3763":[3789,3762],"3955":[3953,3954],"3957":[3953,3956],"3958":[4018,3968],"3959":[4018,3953,3968],"3960":[4019,3968],"3961":[4019,3953,3968],"3969":[3953,3968],"6971":[6970,6965],"6973":[6972,6965],"6976":[6974,6965],"6977":[6975,6965],"6979":[6978,6965],"69934":[69937,69927],"69935":[69938,69927],"70475":[70471,70462],"70476":[70471,70487],"70843":[70841,70842],"70844":[70841,70832],"70846":[70841,70845],"71098":[71096,71087],"71099":[71097,71087]},"stateTable":[[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[2,2,3,4,4,5,0,6,7,8,9,10,11,12,13,14,15,16,0,17,18,11,19,20,21,22,0,0,0,23,0,0,2,0,0,24,0,25],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,26,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,27,28,0,0,0,0,0,27,0,0,0],[0,0,0,0,0,29,0,30,31,32,33,34,35,36,37,38,39,40,0,0,41,35,42,43,44,45,0,0,0,46,0,0,0,0,39,0,0,47],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,0,0,0,0,0,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,0,7,0,0,0,0,0,0,0,0,0,0,0,0,0,0,20,21,22,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,8,9,0,0,12,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,0,9,0,0,0,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,8,9,10,11,12,13,14,0,16,0,0,18,11,19,20,21,22,0,0,0,23,0,0,0,0,0,0,0,25],[0,0,0,0,0,5,0,6,7,8,9,0,11,12,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,0,9,0,0,12,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,0,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,0,7,0,0,0,0,0,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,8,9,10,11,12,13,14,15,16,0,0,18,11,19,20,21,22,0,0,0,23,0,0,0,0,0,0,0,25],[0,0,0,0,0,5,0,6,7,8,9,0,11,12,0,14,0,0,0,0,0,11,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,4,4,5,0,6,7,8,9,10,11,12,13,14,15,16,0,0,18,11,19,20,21,22,0,0,0,23,0,0,0,0,0,0,0,25],[0,0,0,0,0,5,0,6,7,8,9,48,11,12,13,14,48,16,0,0,18,11,19,20,21,22,0,0,0,23,0,0,0,0,49,0,0,25],[0,0,0,0,0,5,0,6,7,8,9,0,11,12,0,14,0,16,0,0,0,11,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,25],[0,0,0,0,0,5,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,20,21,22,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,21,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,21,22,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,0,0,0,0,0,0,14,0,0,0,0,0,0,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,50,0,51,0],[0,0,0,0,0,5,0,6,7,8,9,0,11,12,0,14,0,16,0,0,0,11,0,20,21,22,0,0,0,23,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,27,28,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,28,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,0,0,0,0,0,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,0,31,0,0,0,0,0,0,0,0,0,0,0,0,0,0,43,44,45,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,32,33,0,0,36,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,0,33,0,0,0,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,32,33,34,35,36,37,38,0,40,0,0,41,35,42,43,44,45,0,0,0,46,0,0,0,0,0,0,0,47],[0,0,0,0,0,29,0,30,31,32,33,0,35,36,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,0,33,0,0,36,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,41,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,0,31,0,0,0,0,0,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,32,33,34,35,36,37,38,39,40,0,0,41,35,42,43,44,45,0,0,0,46,0,0,0,0,0,0,0,47],[0,0,0,0,0,29,0,30,31,32,33,0,35,36,0,38,0,0,0,0,0,35,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,32,33,52,35,36,37,38,52,40,0,0,41,35,42,43,44,45,0,0,0,46,0,0,0,0,53,0,0,47],[0,0,0,0,0,29,0,30,31,32,33,0,35,36,0,38,0,40,0,0,0,35,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,47],[0,0,0,0,0,29,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,43,44,45,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,44,0,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,44,45,0,0,0,0,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,0,0,0,0,0,0,38,0,0,0,0,0,0,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,0,29,0,30,31,32,33,0,35,36,0,38,0,40,0,0,0,35,0,43,44,45,0,0,0,46,0,0,0,0,0,0,0,0],[0,0,0,0,0,5,0,6,7,8,9,48,11,12,13,14,0,16,0,0,18,11,19,20,21,22,0,0,0,23,0,0,0,0,0,0,0,25],[0,0,0,0,0,5,0,6,7,8,9,48,11,12,13,14,48,16,0,0,18,11,19,20,21,22,0,0,0,23,0,0,0,0,0,0,0,25],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,51,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,54,0,0],[0,0,0,0,0,29,0,30,31,32,33,52,35,36,37,38,0,40,0,0,41,35,42,43,44,45,0,0,0,46,0,0,0,0,0,0,0,47],[0,0,0,0,0,29,0,30,31,32,33,52,35,36,37,38,52,40,0,0,41,35,42,43,44,45,0,0,0,46,0,0,0,0,0,0,0,47],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,50,0,51,0]],"accepting":[false,true,true,true,true,true,true,true,true,true,true,true,true,false,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true,true],"tags":[[],["broken_cluster"],["independent_cluster"],["symbol_cluster"],["standard_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],[],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["broken_cluster"],["numeral_cluster"],["broken_cluster"],["independent_cluster"],["symbol_cluster"],["symbol_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["virama_terminated_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["standard_cluster"],["broken_cluster"],["broken_cluster"],["numeral_cluster"],["number_joiner_terminated_cluster"],["standard_cluster"],["standard_cluster"],["numeral_cluster"]]}');let nb={X:1,C:2,V:4,N:8,H:16,ZWNJ:32,ZWJ:64,M:128,Placeholder:2048,Dotted_Circle:4096,RS:8192,Coeng:16384,Repha:32768,Ra:65536,CM:131072},ny={Start:1,Ra_To_Become_Reph:2,Pre_M:4,Pre_C:8,Base_C:16,After_Main:32,Above_C:64,Before_Sub:128,Below_C:256,After_Sub:512,Before_Post:1024,Post_C:2048,After_Post:4096,Final_C:8192,SMVD:16384,End:32768},nw=nb.C|nb.Ra|nb.CM|nb.V|nb.Placeholder|nb.Dotted_Circle,nv=nb.ZWJ|nb.ZWNJ,n_=nb.H|nb.Coeng,nC={Default:{hasOldSpec:!1,virama:0,basePos:"Last",rephPos:ny.Before_Post,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Devanagari:{hasOldSpec:!0,virama:2381,basePos:"Last",rephPos:ny.Before_Post,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Bengali:{hasOldSpec:!0,virama:2509,basePos:"Last",rephPos:ny.After_Sub,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Gurmukhi:{hasOldSpec:!0,virama:2637,basePos:"Last",rephPos:ny.Before_Sub,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Gujarati:{hasOldSpec:!0,virama:2765,basePos:"Last",rephPos:ny.Before_Post,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Oriya:{hasOldSpec:!0,virama:2893,basePos:"Last",rephPos:ny.After_Main,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Tamil:{hasOldSpec:!0,virama:3021,basePos:"Last",rephPos:ny.After_Post,rephMode:"Implicit",blwfMode:"Pre_And_Post"},Telugu:{hasOldSpec:!0,virama:3149,basePos:"Last",rephPos:ny.After_Post,rephMode:"Explicit",blwfMode:"Post_Only"},Kannada:{hasOldSpec:!0,virama:3277,basePos:"Last",rephPos:ny.After_Post,rephMode:"Implicit",blwfMode:"Post_Only"},Malayalam:{hasOldSpec:!0,virama:3405,basePos:"Last",rephPos:ny.After_Main,rephMode:"Log_Repha",blwfMode:"Pre_And_Post"},Khmer:{hasOldSpec:!1,virama:6098,basePos:"First",rephPos:ny.Ra_To_Become_Reph,rephMode:"Vis_Repha",blwfMode:"Pre_And_Post"}},nx={6078:[6081,6078],6079:[6081,6079],6080:[6081,6080],6084:[6081,6084],6085:[6081,6085]},{decompositions:nS}=d(nm),nk=new i(ry("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")),nI=new u(d(ng));class nP extends rj{static planFeatures(e){e.addStage(nT),e.addStage(["locl","ccmp"]),e.addStage(nE),e.addStage("nukt"),e.addStage("akhn"),e.addStage("rphf",!1),e.addStage("rkrf"),e.addStage("pref",!1),e.addStage("blwf",!1),e.addStage("abvf",!1),e.addStage("half",!1),e.addStage("pstf",!1),e.addStage("vatu"),e.addStage("cjct"),e.addStage("cfar",!1),e.addStage(nV),e.addStage({local:["init"],global:["pres","abvs","blws","psts","haln","dist","abvm","blwm","calt","clig"]}),e.unicodeScript=rS[e.script],e.indicConfig=nC[e.unicodeScript]||nC.Default,e.isOldSpec=e.indicConfig.hasOldSpec&&"2"!==e.script[e.script.length-1]}static assignFeatures(e,t){for(let s=t.length-1;s>=0;s--){let r=t[s].codePoints[0],n=nx[r]||nS[r];if(n){let r=n.map(r=>{let n=e.font.glyphForCodePoint(r);return new r8(e.font,n.id,[r],t[s].features)});t.splice(s,1,...r)}}}}function nA(e){return nk.get(e.codePoints[0])>>8}function nO(e){return 1<<(255&nk.get(e.codePoints[0]))}(0,n._)(nP,"zeroMarkWidths","NONE");class nD{constructor(e,t,s,r){this.category=e,this.position=t,this.syllableType=s,this.syllable=r}}function nT(e,t){let s=0,r=0;for(let[e,n,a]of nI.match(t.map(nA))){if(e>r){++s;for(let n=r;n<e;n++)t[n].shaperInfo=new nD(nb.X,ny.End,"non_indic_cluster",s)}++s;for(let r=e;r<=n;r++)t[r].shaperInfo=new nD(1<<nA(t[r]),nO(t[r]),a[0],s);r=n+1}if(r<t.length){++s;for(let e=r;e<t.length;e++)t[e].shaperInfo=new nD(nb.X,ny.End,"non_indic_cluster",s)}}function nF(e){return e.shaperInfo.category&nw}function nM(e){return e.shaperInfo.category&nv}function nL(e){return e.shaperInfo.category&n_}function nG(e,t){for(let s of e)s.features={[t]:!0};return e[0]._font._layoutEngine.engine.GSUBProcessor.applyFeatures([t],e),1===e.length}function nE(e,t,s){let r=s.indicConfig,n=e._layoutEngine.engine.GSUBProcessor.features,a=e.glyphForCodePoint(9676).id,l=e.glyphForCodePoint(r.virama).id;if(l){let s=new r8(e,l,[r.virama]);for(let e=0;e<t.length;e++)t[e].shaperInfo.position===ny.Base_C&&(t[e].shaperInfo.position=function(e,t,s){let r=[s,t,s];return nG(r.slice(0,2),"blwf")||nG(r.slice(1,3),"blwf")?ny.Below_C:nG(r.slice(0,2),"pstf")||nG(r.slice(1,3),"pstf")||nG(r.slice(0,2),"pref")||nG(r.slice(1,3),"pref")?ny.Post_C:ny.Base_C}(0,t[e].copy(),s))}for(let l=0,o=nB(t,0);l<t.length;o=nB(t,l=o)){let{category:i,syllableType:u}=t[l].shaperInfo;if("symbol_cluster"===u||"non_indic_cluster"===u)continue;if("broken_cluster"===u&&a){let s=new r8(e,a,[9676]);s.shaperInfo=new nD(1<<nA(s),nO(s),t[l].shaperInfo.syllableType,t[l].shaperInfo.syllable);let r=l;for(;r<o&&t[r].shaperInfo.category===nb.Repha;)r++;t.splice(r++,0,s),o++}let c=o,h=l,f=!1;if(r.rephPos!==ny.Ra_To_Become_Reph&&n.rphf&&l+3<=o&&("Implicit"===r.rephMode&&!nM(t[l+2])||"Explicit"===r.rephMode&&t[l+2].shaperInfo.category===nb.ZWJ)){let e=[t[l].copy(),t[l+1].copy(),t[l+2].copy()];if(nG(e.slice(0,2),"rphf")||"Explicit"===r.rephMode&&nG(e,"rphf")){for(h+=2;h<o&&nM(t[h]);)h++;c=l,f=!0}}else if("Log_Repha"===r.rephMode&&t[l].shaperInfo.category===nb.Repha){for(h++;h<o&&nM(t[h]);)h++;c=l,f=!0}switch(r.basePos){case"Last":{let e=o,s=!1;do{let r=t[--e].shaperInfo;if(nF(t[e])){if(r.position!==ny.Below_C&&(r.position!==ny.Post_C||s)){c=e;break}r.position===ny.Below_C&&(s=!0),c=e}else if(l<e&&r.category===nb.ZWJ&&t[e-1].shaperInfo.category===nb.H)break}while(e>h);break}case"First":c=l;for(let e=c+1;e<o;e++)nF(t[e])&&(t[e].shaperInfo.position=ny.Below_C)}f&&c===l&&h-c<=2&&(f=!1);for(let e=l;e<c;e++){let s=t[e].shaperInfo;s.position=Math.min(ny.Pre_C,s.position)}c<o&&(t[c].shaperInfo.position=ny.Base_C);for(let e=c+1;e<o;e++)if(t[e].shaperInfo.category===nb.M){for(let s=e+1;s<o;s++)if(nF(t[s])){t[s].shaperInfo.position=ny.Final_C;break}break}if(f&&(t[l].shaperInfo.position=ny.Ra_To_Become_Reph),s.isOldSpec){let e="Malayalam"!==s.unicodeScript;for(let s=c+1;s<o;s++)if(t[s].shaperInfo.category===nb.H){let r;for(r=o-1;r>s&&!nF(t[r])&&(!e||t[r].shaperInfo.category!==nb.H);r--);if(t[r].shaperInfo.category!==nb.H&&r>s){let e=t[s];t.splice(s,0,...t.splice(s+1,r-s)),t[r]=e}break}}let p=ny.Start;for(let e=l;e<o;e++){let s=t[e].shaperInfo;if(s.category&(nv|nb.N|nb.RS|nb.CM|n_&s.category)){if(s.position=p,s.category===nb.H&&s.position===ny.Pre_M){for(let r=e;r>l;r--)if(t[r-1].shaperInfo.position!==ny.Pre_M){s.position=t[r-1].shaperInfo.position;break}}}else s.position!==ny.SMVD&&(p=s.position)}let d=c;for(let e=c+1;e<o;e++)if(nF(t[e])){for(let s=d+1;s<e;s++)t[s].shaperInfo.position<ny.SMVD&&(t[s].shaperInfo.position=t[e].shaperInfo.position);d=e}else t[e].shaperInfo.category===nb.M&&(d=e);let g=t.slice(l,o);g.sort((e,t)=>e.shaperInfo.position-t.shaperInfo.position),t.splice(l,g.length,...g);for(let e=l;e<o;e++)if(t[e].shaperInfo.position===ny.Base_C){c=e;break}for(let e=l;e<o&&t[e].shaperInfo.position===ny.Ra_To_Become_Reph;e++)t[e].features.rphf=!0;let m=!s.isOldSpec&&"Pre_And_Post"===r.blwfMode;for(let e=l;e<c;e++)t[e].features.half=!0,m&&(t[e].features.blwf=!0);for(let e=c+1;e<o;e++)t[e].features.abvf=!0,t[e].features.pstf=!0,t[e].features.blwf=!0;if(s.isOldSpec&&"Devanagari"===s.unicodeScript)for(let e=l;e+1<c;e++)t[e].shaperInfo.category===nb.Ra&&t[e+1].shaperInfo.category===nb.H&&(e+1===c||t[e+2].shaperInfo.category===nb.ZWJ)&&(t[e].features.blwf=!0,t[e+1].features.blwf=!0);if(n.pref&&c+2<o){for(let e=c+1;e+2-1<o;e++)if(nG([t[e].copy(),t[e+1].copy()],"pref")){for(let s=0;s<2;s++)t[e++].features.pref=!0;if(n.cfar)for(;e<o;e++)t[e].features.cfar=!0;break}}for(let e=l+1;e<o;e++)if(nM(t[e])){let s=t[e].shaperInfo.category===nb.ZWNJ,r=e;do r--,s&&delete t[r].features.half;while(r>l&&!nF(t[r]))}}}function nV(e,t,s){let r=s.indicConfig,n=e._layoutEngine.engine.GSUBProcessor.features;for(let e=0,a=nB(t,0);e<t.length;a=nB(t,e=a)){let l=!!n.pref,i=e;for(;i<a;i++)if(t[i].shaperInfo.position>=ny.Base_C){if(l&&i+1<a){for(let e=i+1;e<a;e++)if(t[e].features.pref){if(!(t[e].substituted&&t[e].isLigated&&!t[e].isMultiplied)){for(i=e;i<a&&nL(t[i]);)i++;t[i].shaperInfo.position=ny.BASE_C,l=!1}break}}if("Malayalam"===s.unicodeScript)for(let e=i+1;e<a;e++){for(;e<a&&nM(t[e]);)e++;if(e===a||!nL(t[e]))break;for(e++;e<a&&nM(t[e]);)e++;e<a&&nF(t[e])&&t[e].shaperInfo.position===ny.Below_C&&(t[i=e].shaperInfo.position=ny.Base_C)}e<i&&t[i].shaperInfo.position>ny.Base_C&&i--;break}if(i===a&&e<i&&t[i-1].shaperInfo.category===nb.ZWJ&&i--,i<a)for(;e<i&&t[i].shaperInfo.category&(nb.N|n_);)i--;if(e+1<a&&e<i){let r=i===a?i-2:i-1;if("Malayalam"!==s.unicodeScript&&"Tamil"!==s.unicodeScript){for(;r>e&&!(t[r].shaperInfo.category&(nb.M|n_));)r--;nL(t[r])&&t[r].shaperInfo.position!==ny.Pre_M?r+1<a&&nM(t[r+1])&&r++:r=e}if(e<r&&t[r].shaperInfo.position!==ny.Pre_M){for(let s=r;s>e;s--)if(t[s-1].shaperInfo.position===ny.Pre_M){let e=s-1;e<i&&i<=r&&i--;let n=t[e];t.splice(e,0,...t.splice(e+1,r-e)),t[r]=n,r--}}}if(e+1<a&&t[e].shaperInfo.position===ny.Ra_To_Become_Reph&&t[e].shaperInfo.category===nb.Repha!==(t[e].isLigated&&!t[e].isMultiplied)){let s,n=r.rephPos,l=!1;if(n!==ny.After_Post){for(s=e+1;s<i&&!nL(t[s]);)s++;if(s<i&&nL(t[s])&&(s+1<i&&nM(t[s+1])&&s++,l=!0),!l&&n===ny.After_Main){for(s=i;s+1<a&&t[s+1].shaperInfo.position<=ny.After_Main;)s++;l=s<a}if(!l&&n===ny.After_Sub){for(s=i;s+1<a&&!(t[s+1].shaperInfo.position&(ny.Post_C|ny.After_Post|ny.SMVD));)s++;l=s<a}}if(!l){for(s=e+1;s<i&&!nL(t[s]);)s++;s<i&&nL(t[s])&&(s+1<i&&nM(t[s+1])&&s++,l=!0)}if(!l){for(s=a-1;s>e&&t[s].shaperInfo.position===ny.SMVD;)s--;if(nL(t[s]))for(let e=i+1;e<s;e++)t[e].shaperInfo.category===nb.M&&s--}let o=t[e];t.splice(e,0,...t.splice(e+1,s-e)),t[s]=o,e<i&&i<=s&&i--}if(l&&i+1<a){for(let r=i+1;r<a;r++)if(t[r].features.pref){if(t[r].isLigated&&!t[r].isMultiplied){let n=i;if("Malayalam"!==s.unicodeScript&&"Tamil"!==s.unicodeScript){for(;n>e&&!(t[n-1].shaperInfo.category&(nb.M|n_));)n--;if(n>e&&t[n-1].shaperInfo.category===nb.M){let e=r;for(let s=i+1;s<e;s++)if(t[s].shaperInfo.category===nb.M){n--;break}}}n>e&&nL(t[n-1])&&n<a&&nM(t[n])&&n++;let l=r,o=t[l];t.splice(n+1,0,...t.splice(n,l-n)),t[n]=o,n<=i&&i<l&&i++}break}}t[e].shaperInfo.position!==ny.Pre_M||e&&/Cf|Mn/.test((0,o.p7)(t[e-1].codePoints[0]))||(t[e].features.init=!0)}}function nB(e,t){if(t>=e.length)return t;let s=e[t].shaperInfo.syllable;for(;++t<e.length&&e[t].shaperInfo.syllable===s;);return t}let{categories:nz,decompositions:nR}=d(nm),nN=new i(ry("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")),nU=new u(d(nm));class nq extends rj{static planFeatures(e){e.addStage(nX),e.addStage(["locl","ccmp","nukt","akhn"]),e.addStage(nY),e.addStage(["rphf"],!1),e.addStage(nW),e.addStage(nY),e.addStage(["pref"]),e.addStage(nZ),e.addStage(["rkrf","abvf","blwf","half","pstf","vatu","cjct"]),e.addStage(nJ),e.addStage(["abvs","blws","pres","psts","dist","abvm","blwm"])}static assignFeatures(e,t){for(let s=t.length-1;s>=0;s--){let r=t[s].codePoints[0];if(nR[r]){let n=nR[r].map(r=>{let n=e.font.glyphForCodePoint(r);return new r8(e.font,n.id,[r],t[s].features)});t.splice(s,1,...n)}}}}function nH(e){return nN.get(e.codePoints[0])}(0,n._)(nq,"zeroMarkWidths","BEFORE_GPOS");class nj{constructor(e,t,s){this.category=e,this.syllableType=t,this.syllable=s}}function nX(e,t){let s=0;for(let[e,r,n]of nU.match(t.map(nH))){++s;for(let a=e;a<=r;a++)t[a].shaperInfo=new nj(nz[nH(t[a])],n[0],s);let a="R"===t[e].shaperInfo.category?1:Math.min(3,r-e);for(let s=e;s<e+a;s++)t[s].features.rphf=!0}}function nY(e,t){for(let e of t)e.substituted=!1}function nW(e,t){for(let e of t)e.substituted&&e.features.rphf&&(e.shaperInfo.category="R")}function nZ(e,t){for(let e of t)e.substituted&&(e.shaperInfo.category="VPre")}function nJ(e,t){let s=e.glyphForCodePoint(9676).id;for(let r=0,n=nK(t,0);r<t.length;n=nK(t,r=n)){let a,l,o=t[r].shaperInfo,i=o.syllableType;if("virama_terminated_cluster"===i||"standard_cluster"===i||"broken_cluster"===i){if("broken_cluster"===i&&s){let l=new r8(e,s,[9676]);for(l.shaperInfo=o,a=r;a<n&&"R"===t[a].shaperInfo.category;a++);t.splice(++a,0,l),n++}if("R"===o.category&&n-r>1){for(a=r+1;a<n;a++)if(n$(o=t[a].shaperInfo)||nQ(t[a])){nQ(t[a])&&a--,t.splice(r,0,...t.splice(r+1,a-r),t[a]);break}}for(a=r,l=n;a<n;a++)n$(o=t[a].shaperInfo)||nQ(t[a])?l=nQ(t[a])?a+1:a:("VPre"===o.category||"VMPre"===o.category)&&l<a&&t.splice(l,1,t[a],...t.splice(l,a-l))}}}function nK(e,t){if(t>=e.length)return t;let s=e[t].shaperInfo.syllable;for(;++t<e.length&&e[t].shaperInfo.syllable===s;);return t}function nQ(e){return"H"===e.shaperInfo.category&&!e.isLigated}function n$(e){return"B"===e.category||"GB"===e.category}let n0={arab:r2,mong:r2,syrc:r2,"nko ":r2,phag:r2,mand:r2,mani:r2,phlp:r2,hang:r6,bng2:nP,beng:nP,dev2:nP,deva:nP,gjr2:nP,gujr:nP,guru:nP,gur2:nP,knda:nP,knd2:nP,mlm2:nP,mlym:nP,ory2:nP,orya:nP,taml:nP,tml2:nP,telu:nP,tel2:nP,khmr:nP,bali:nq,batk:nq,brah:nq,bugi:nq,buhd:nq,cakm:nq,cham:nq,dupl:nq,egyp:nq,gran:nq,hano:nq,java:nq,kthi:nq,kali:nq,khar:nq,khoj:nq,sind:nq,lepc:nq,limb:nq,mahj:nq,mtei:nq,modi:nq,hmng:nq,rjng:nq,saur:nq,shrd:nq,sidd:nq,sinh:nP,sund:nq,sylo:nq,tglg:nq,tagb:nq,tale:nq,lana:nq,tavt:nq,takr:nq,tibt:nq,tfng:nq,tirh:nq,latn:rj,DFLT:rj};class n1 extends r5{applyLookup(e,t){switch(e){case 1:{let e=this.coverageIndex(t.coverage);if(-1===e)return!1;let s=this.glyphIterator.cur;switch(t.version){case 1:s.id=s.id+t.deltaGlyphID&65535;break;case 2:s.id=t.substitute.get(e)}return!0}case 2:{let e=this.coverageIndex(t.coverage);if(-1!==e){let s=t.sequences.get(e);if(0===s.length)return this.glyphs.splice(this.glyphIterator.index,1),!0;this.glyphIterator.cur.id=s[0],this.glyphIterator.cur.ligatureComponent=0;let r=this.glyphIterator.cur.features,n=this.glyphIterator.cur,a=s.slice(1).map((e,t)=>{let s=new r8(this.font,e,void 0,r);return s.shaperInfo=n.shaperInfo,s.isLigated=n.isLigated,s.ligatureComponent=t+1,s.substituted=!0,s.isMultiplied=!0,s});return this.glyphs.splice(this.glyphIterator.index+1,0,...a),!0}return!1}case 3:{let e=this.coverageIndex(t.coverage);if(-1!==e)return this.glyphIterator.cur.id=t.alternateSet.get(e)[0],!0;return!1}case 4:{let e=this.coverageIndex(t.coverage);if(-1===e)return!1;for(let r of t.ligatureSets.get(e)){let e=this.sequenceMatchIndices(1,r.components);if(!e)continue;let t=this.glyphIterator.cur,n=t.codePoints.slice();for(let t of e)n.push(...this.glyphs[t].codePoints);let a=new r8(this.font,r.glyph,n,t.features);a.shaperInfo=t.shaperInfo,a.isLigated=!0,a.substituted=!0;let l=t.isMark;for(let t=0;t<e.length&&l;t++)l=this.glyphs[e[t]].isMark;a.ligatureID=l?null:this.ligatureID++;let o=t.ligatureID,i=t.codePoints.length,u=i,c=this.glyphIterator.index+1;for(let t of e){if(l)c=t;else for(;c<t;){var s=u-i+Math.min(this.glyphs[c].ligatureComponent||1,i);this.glyphs[c].ligatureID=a.ligatureID,this.glyphs[c].ligatureComponent=s,c++}o=this.glyphs[c].ligatureID,u+=i=this.glyphs[c].codePoints.length,c++}if(o&&!l)for(let e=c;e<this.glyphs.length;e++)if(this.glyphs[e].ligatureID===o){var s=u-i+Math.min(this.glyphs[e].ligatureComponent||1,i);this.glyphs[e].ligatureComponent=s}else break;for(let t=e.length-1;t>=0;t--)this.glyphs.splice(e[t],1);return this.glyphs[this.glyphIterator.index]=a,!0}return!1}case 5:return this.applyContext(t);case 6:return this.applyChainingContext(t);case 7:return this.applyLookup(t.lookupType,t.extension);default:throw Error(`GSUB lookupType ${e} is not supported`)}}}class n2 extends r5{applyPositionValue(e,t){let s=this.positions[this.glyphIterator.peekIndex(e)];null!=t.xAdvance&&(s.xAdvance+=t.xAdvance),null!=t.yAdvance&&(s.yAdvance+=t.yAdvance),null!=t.xPlacement&&(s.xOffset+=t.xPlacement),null!=t.yPlacement&&(s.yOffset+=t.yPlacement);let r=this.font._variationProcessor,n=this.font.GDEF&&this.font.GDEF.itemVariationStore;r&&n&&(t.xPlaDevice&&(s.xOffset+=r.getDelta(n,t.xPlaDevice.a,t.xPlaDevice.b)),t.yPlaDevice&&(s.yOffset+=r.getDelta(n,t.yPlaDevice.a,t.yPlaDevice.b)),t.xAdvDevice&&(s.xAdvance+=r.getDelta(n,t.xAdvDevice.a,t.xAdvDevice.b)),t.yAdvDevice&&(s.yAdvance+=r.getDelta(n,t.yAdvDevice.a,t.yAdvDevice.b)))}applyLookup(e,t){switch(e){case 1:{let e=this.coverageIndex(t.coverage);if(-1===e)return!1;switch(t.version){case 1:this.applyPositionValue(0,t.value);break;case 2:this.applyPositionValue(0,t.values.get(e))}return!0}case 2:{let e=this.glyphIterator.peek();if(!e)return!1;let r=this.coverageIndex(t.coverage);if(-1===r)return!1;switch(t.version){case 1:for(let s of t.pairSets.get(r))if(s.secondGlyph===e.id)return this.applyPositionValue(0,s.value1),this.applyPositionValue(1,s.value2),!0;return!1;case 2:let n=this.getClassID(this.glyphIterator.cur.id,t.classDef1),a=this.getClassID(e.id,t.classDef2);if(-1===n||-1===a)return!1;var s=t.classRecords.get(n).get(a);return this.applyPositionValue(0,s.value1),this.applyPositionValue(1,s.value2),!0}}case 3:{let e,s=this.glyphIterator.peekIndex(),r=this.glyphs[s];if(!r)return!1;let n=t.entryExitRecords[this.coverageIndex(t.coverage)];if(!n||!n.exitAnchor)return!1;let a=t.entryExitRecords[this.coverageIndex(t.coverage,r.id)];if(!a||!a.entryAnchor)return!1;let l=this.getAnchor(a.entryAnchor),o=this.getAnchor(n.exitAnchor),i=this.positions[this.glyphIterator.index],u=this.positions[s];switch(this.direction){case"ltr":i.xAdvance=o.x+i.xOffset,e=l.x+u.xOffset,u.xAdvance-=e,u.xOffset-=e;break;case"rtl":e=o.x+i.xOffset,i.xAdvance-=e,i.xOffset-=e,u.xAdvance=l.x+u.xOffset}return this.glyphIterator.flags.rightToLeft?(this.glyphIterator.cur.cursiveAttachment=s,i.yOffset=l.y-o.y):(r.cursiveAttachment=this.glyphIterator.index,i.yOffset=o.y-l.y),!0}case 4:{let e=this.coverageIndex(t.markCoverage);if(-1===e)return!1;let s=this.glyphIterator.index;for(;--s>=0&&(this.glyphs[s].isMark||this.glyphs[s].ligatureComponent>0););if(s<0)return!1;let r=this.coverageIndex(t.baseCoverage,this.glyphs[s].id);if(-1===r)return!1;let n=t.markArray[e],a=t.baseArray[r][n.class];return this.applyAnchor(n,a,s),!0}case 5:{let e=this.coverageIndex(t.markCoverage);if(-1===e)return!1;let s=this.glyphIterator.index;for(;--s>=0&&this.glyphs[s].isMark;);if(s<0)return!1;let r=this.coverageIndex(t.ligatureCoverage,this.glyphs[s].id);if(-1===r)return!1;let n=t.ligatureArray[r],a=this.glyphIterator.cur,l=this.glyphs[s],o=l.ligatureID&&l.ligatureID===a.ligatureID&&a.ligatureComponent>0?Math.min(a.ligatureComponent,l.codePoints.length)-1:l.codePoints.length-1,i=t.markArray[e],u=n[o][i.class];return this.applyAnchor(i,u,s),!0}case 6:{let e=this.coverageIndex(t.mark1Coverage);if(-1===e)return!1;let s=this.glyphIterator.peekIndex(-1),r=this.glyphs[s];if(!r||!r.isMark)return!1;let n=this.glyphIterator.cur,a=!1;if(n.ligatureID===r.ligatureID?n.ligatureID?n.ligatureComponent===r.ligatureComponent&&(a=!0):a=!0:(n.ligatureID&&!n.ligatureComponent||r.ligatureID&&!r.ligatureComponent)&&(a=!0),!a)return!1;let l=this.coverageIndex(t.mark2Coverage,r.id);if(-1===l)return!1;let o=t.mark1Array[e],i=t.mark2Array[l][o.class];return this.applyAnchor(o,i,s),!0}case 7:return this.applyContext(t);case 8:return this.applyChainingContext(t);case 9:return this.applyLookup(t.lookupType,t.extension);default:throw Error(`Unsupported GPOS table: ${e}`)}}applyAnchor(e,t,s){let r=this.getAnchor(t),n=this.getAnchor(e.markAnchor);this.positions[s];let a=this.positions[this.glyphIterator.index];a.xOffset=r.x-n.x,a.yOffset=r.y-n.y,this.glyphIterator.cur.markAttachment=s}getAnchor(e){let t=e.xCoordinate,s=e.yCoordinate,r=this.font._variationProcessor,n=this.font.GDEF&&this.font.GDEF.itemVariationStore;return r&&n&&(e.xDeviceTable&&(t+=r.getDelta(n,e.xDeviceTable.a,e.xDeviceTable.b)),e.yDeviceTable&&(s+=r.getDelta(n,e.yDeviceTable.a,e.yDeviceTable.b))),{x:t,y:s}}applyFeatures(e,t,s){super.applyFeatures(e,t,s);for(var r=0;r<this.glyphs.length;r++)this.fixCursiveAttachment(r);this.fixMarkAttachment()}fixCursiveAttachment(e){let t=this.glyphs[e];if(null!=t.cursiveAttachment){let s=t.cursiveAttachment;t.cursiveAttachment=null,this.fixCursiveAttachment(s),this.positions[e].yOffset+=this.positions[s].yOffset}}fixMarkAttachment(){for(let e=0;e<this.glyphs.length;e++){let t=this.glyphs[e];if(null!=t.markAttachment){let s=t.markAttachment;if(this.positions[e].xOffset+=this.positions[s].xOffset,this.positions[e].yOffset+=this.positions[s].yOffset,"ltr"===this.direction)for(let t=s;t<e;t++)this.positions[e].xOffset-=this.positions[t].xAdvance,this.positions[e].yOffset-=this.positions[t].yAdvance;else for(let t=s+1;t<e+1;t++)this.positions[e].xOffset+=this.positions[t].xAdvance,this.positions[e].yOffset+=this.positions[t].yAdvance}}}}class n3{setup(e){this.glyphInfos=e.glyphs.map(e=>new r8(this.font,e.id,[...e.codePoints]));let t=null;for(let s in this.GPOSProcessor&&(t=this.GPOSProcessor.selectScript(e.script,e.language,e.direction)),this.GSUBProcessor&&(t=this.GSUBProcessor.selectScript(e.script,e.language,e.direction)),this.shaper=function(e){for(let t of(Array.isArray(e)||(e=[e]),e)){let e=n0[t];if(e)return e}return rj}(t),this.plan=new rz(this.font,t,e.direction),this.shaper.plan(this.plan,this.glyphInfos,e.features),this.plan.allFeatures)e.features[s]=!0}substitute(e){this.GSUBProcessor&&(this.plan.process(this.GSUBProcessor,this.glyphInfos),e.glyphs=this.glyphInfos.map(e=>this.font.getGlyph(e.id,e.codePoints)))}position(e){return"BEFORE_GPOS"===this.shaper.zeroMarkWidths&&this.zeroMarkAdvances(e.positions),this.GPOSProcessor&&this.plan.process(this.GPOSProcessor,this.glyphInfos,e.positions),"AFTER_GPOS"===this.shaper.zeroMarkWidths&&this.zeroMarkAdvances(e.positions),"rtl"===e.direction&&(e.glyphs.reverse(),e.positions.reverse()),this.GPOSProcessor&&this.GPOSProcessor.features}zeroMarkAdvances(e){for(let t=0;t<this.glyphInfos.length;t++)this.glyphInfos[t].isMark&&(e[t].xAdvance=0,e[t].yAdvance=0)}cleanup(){this.glyphInfos=null,this.plan=null,this.shaper=null}getAvailableFeatures(e,t){let s=[];return this.GSUBProcessor&&(this.GSUBProcessor.selectScript(e,t),s.push(...Object.keys(this.GSUBProcessor.features))),this.GPOSProcessor&&(this.GPOSProcessor.selectScript(e,t),s.push(...Object.keys(this.GPOSProcessor.features))),s}constructor(e){this.font=e,this.glyphInfos=null,this.plan=null,this.GSUBProcessor=null,this.GPOSProcessor=null,this.fallbackPosition=!0,e.GSUB&&(this.GSUBProcessor=new n1(e,e.GSUB)),e.GPOS&&(this.GPOSProcessor=new n2(e,e.GPOS))}}class n4{layout(e,t,s,r,n){if("string"==typeof t&&(n=r,r=s,s=t,t=[]),"string"==typeof e){null==s&&(s=function(e){let t=e.length,s=0;for(;s<t;){let r=e.charCodeAt(s++);if(55296<=r&&r<=56319&&s<t){let t=e.charCodeAt(s);56320<=t&&t<=57343&&(s++,r=((1023&r)<<10)+(1023&t)+65536)}let n=(0,o.IT)(r);if("Common"!==n&&"Inherited"!==n&&"Unknown"!==n)return rx[n]}return rx.Unknown}(e));var a=this.font.glyphsForString(e)}else{if(null==s){let t=[];for(let s of e)t.push(...s.codePoints);s=function(e){for(let t=0;t<e.length;t++){let s=e[t],r=(0,o.IT)(s);if("Common"!==r&&"Inherited"!==r&&"Unknown"!==r)return rx[r]}return rx.Unknown}(t)}var a=e}let l=new rP(a,t,s,r,n);return 0===a.length?l.positions=[]:(this.engine&&this.engine.setup&&this.engine.setup(l),this.substitute(l),this.position(l),this.hideDefaultIgnorables(l.glyphs,l.positions),this.engine&&this.engine.cleanup&&this.engine.cleanup()),l}substitute(e){this.engine&&this.engine.substitute&&this.engine.substitute(e)}position(e){e.positions=e.glyphs.map(e=>new rA(e.advanceWidth));let t=null;this.engine&&this.engine.position&&(t=this.engine.position(e)),t||this.engine&&!this.engine.fallbackPosition||(this.unicodeLayoutEngine||(this.unicodeLayoutEngine=new r_(this.font)),this.unicodeLayoutEngine.positionGlyphs(e.glyphs,e.positions)),t&&t.kern||!1===e.features.kern||!this.font.kern||(this.kernProcessor||(this.kernProcessor=new rv(this.font)),this.kernProcessor.process(e.glyphs,e.positions),e.features.kern=!0)}hideDefaultIgnorables(e,t){let s=this.font.glyphForCodePoint(32);for(let r=0;r<e.length;r++)this.isDefaultIgnorable(e[r].codePoints[0])&&(e[r]=s,t[r].xAdvance=0,t[r].yAdvance=0)}isDefaultIgnorable(e){let t=e>>16;if(0===t)switch(e>>8){case 0:return 173===e;case 3:return 847===e;case 6:return 1564===e;case 23:return 6068<=e&&e<=6069;case 24:return 6155<=e&&e<=6158;case 32:return 8203<=e&&e<=8207||8234<=e&&e<=8238||8288<=e&&e<=8303;case 254:return 65024<=e&&e<=65039||65279===e;case 255:return 65520<=e&&e<=65528;default:return!1}switch(t){case 1:return 113824<=e&&e<=113827||119155<=e&&e<=119162;case 14:return 917504<=e&&e<=921599;default:return!1}}getAvailableFeatures(e,t){let s=[];return this.engine&&s.push(...this.engine.getAvailableFeatures(e,t)),this.font.kern&&-1===s.indexOf("kern")&&s.push("kern"),s}stringsForGlyph(e){let t=new Set;for(let s of this.font._cmapProcessor.codePointsForGlyph(e))t.add(String.fromCodePoint(s));if(this.engine&&this.engine.stringsForGlyph)for(let s of this.engine.stringsForGlyph(e))t.add(s);return Array.from(t)}constructor(e){this.font=e,this.unicodeLayoutEngine=null,this.kernProcessor=null,this.font.morx?this.engine=new rB(this.font):(this.font.GSUB||this.font.GPOS)&&(this.engine=new n3(this.font))}}let n5={moveTo:"M",lineTo:"L",quadraticCurveTo:"Q",bezierCurveTo:"C",closePath:"Z"};class n8{toFunction(){return e=>{this.commands.forEach(t=>e[t.command].apply(e,t.args))}}toSVG(){return this.commands.map(e=>{let t=e.args.map(e=>Math.round(100*e)/100);return`${n5[e.command]}${t.join(" ")}`}).join("")}get cbox(){if(!this._cbox){let e=new rC;for(let t of this.commands)for(let s=0;s<t.args.length;s+=2)e.addPoint(t.args[s],t.args[s+1]);this._cbox=Object.freeze(e)}return this._cbox}get bbox(){if(this._bbox)return this._bbox;let e=new rC,t=0,s=0,r=e=>Math.pow(1-e,3)*f[m]+3*Math.pow(1-e,2)*e*p[m]+3*(1-e)*Math.pow(e,2)*d[m]+Math.pow(e,3)*g[m];for(let b of this.commands)switch(b.command){case"moveTo":case"lineTo":let[y,w]=b.args;e.addPoint(y,w),t=y,s=w;break;case"quadraticCurveTo":case"bezierCurveTo":if("quadraticCurveTo"===b.command)var[n,a,l,o]=b.args,i=t+2/3*(n-t),u=s+2/3*(a-s),c=l+2/3*(n-l),h=o+2/3*(a-o);else var[i,u,c,h,l,o]=b.args;e.addPoint(l,o);for(var f=[t,s],p=[i,u],d=[c,h],g=[l,o],m=0;m<=1;m++){let t=6*f[m]-12*p[m]+6*d[m],s=-3*f[m]+9*p[m]-9*d[m]+3*g[m];if(b=3*p[m]-3*f[m],0===s){if(0===t)continue;let s=-b/t;0<s&&s<1&&(0===m?e.addPoint(r(s),e.maxY):1===m&&e.addPoint(e.maxX,r(s)));continue}let n=Math.pow(t,2)-4*b*s;if(n<0)continue;let a=(-t+Math.sqrt(n))/(2*s);0<a&&a<1&&(0===m?e.addPoint(r(a),e.maxY):1===m&&e.addPoint(e.maxX,r(a)));let l=(-t-Math.sqrt(n))/(2*s);0<l&&l<1&&(0===m?e.addPoint(r(l),e.maxY):1===m&&e.addPoint(e.maxX,r(l)))}t=l,s=o}return this._bbox=Object.freeze(e)}mapPoints(e){let t=new n8;for(let s of this.commands){let r=[];for(let t=0;t<s.args.length;t+=2){let[n,a]=e(s.args[t],s.args[t+1]);r.push(n,a)}t[s.command](...r)}return t}transform(e,t,s,r,n,a){return this.mapPoints((l,o)=>[e*l+s*o+n,t*l+r*o+a])}translate(e,t){return this.transform(1,0,0,1,e,t)}rotate(e){let t=Math.cos(e),s=Math.sin(e);return this.transform(t,s,-s,t,0,0)}scale(e,t=e){return this.transform(e,0,0,t,0,0)}constructor(){this.commands=[],this._bbox=null,this._cbox=null}}for(let e of["moveTo","lineTo","quadraticCurveTo","bezierCurveTo","closePath"])n8.prototype[e]=function(...t){return this._bbox=this._cbox=null,this.commands.push({command:e,args:t}),this};var n6=[".notdef",".null","nonmarkingreturn","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quotesingle","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","grave","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","Adieresis","Aring","Ccedilla","Eacute","Ntilde","Odieresis","Udieresis","aacute","agrave","acircumflex","adieresis","atilde","aring","ccedilla","eacute","egrave","ecircumflex","edieresis","iacute","igrave","icircumflex","idieresis","ntilde","oacute","ograve","ocircumflex","odieresis","otilde","uacute","ugrave","ucircumflex","udieresis","dagger","degree","cent","sterling","section","bullet","paragraph","germandbls","registered","copyright","trademark","acute","dieresis","notequal","AE","Oslash","infinity","plusminus","lessequal","greaterequal","yen","mu","partialdiff","summation","product","pi","integral","ordfeminine","ordmasculine","Omega","ae","oslash","questiondown","exclamdown","logicalnot","radical","florin","approxequal","Delta","guillemotleft","guillemotright","ellipsis","nonbreakingspace","Agrave","Atilde","Otilde","OE","oe","endash","emdash","quotedblleft","quotedblright","quoteleft","quoteright","divide","lozenge","ydieresis","Ydieresis","fraction","currency","guilsinglleft","guilsinglright","fi","fl","daggerdbl","periodcentered","quotesinglbase","quotedblbase","perthousand","Acircumflex","Ecircumflex","Aacute","Edieresis","Egrave","Iacute","Icircumflex","Idieresis","Igrave","Oacute","Ocircumflex","apple","Ograve","Uacute","Ucircumflex","Ugrave","dotlessi","circumflex","tilde","macron","breve","dotaccent","ring","cedilla","hungarumlaut","ogonek","caron","Lslash","lslash","Scaron","scaron","Zcaron","zcaron","brokenbar","Eth","eth","Yacute","yacute","Thorn","thorn","minus","multiply","onesuperior","twosuperior","threesuperior","onehalf","onequarter","threequarters","franc","Gbreve","gbreve","Idotaccent","Scedilla","scedilla","Cacute","cacute","Ccaron","ccaron","dcroat"];class n9{_getPath(){return new n8}_getCBox(){return this.path.cbox}_getBBox(){return this.path.bbox}_getTableMetrics(e){if(this.id<e.metrics.length)return e.metrics.get(this.id);let t=e.metrics.get(e.metrics.length-1);return{advance:t?t.advance:0,bearing:e.bearings.get(this.id-e.metrics.length)||0}}_getMetrics(e){if(this._metrics)return this._metrics;let{advance:t,bearing:s}=this._getTableMetrics(this._font.hmtx);if(this._font.vmtx)var{advance:r,bearing:n}=this._getTableMetrics(this._font.vmtx);else{let t;if(null==e&&({cbox:e}=this),(t=this._font["OS/2"])&&t.version>0)var r=Math.abs(t.typoAscender-t.typoDescender),n=t.typoAscender-e.maxY;else{let{hhea:t}=this._font;var r=Math.abs(t.ascent-t.descent),n=t.ascent-e.maxY}}return this._font._variationProcessor&&this._font.HVAR&&(t+=this._font._variationProcessor.getAdvanceAdjustment(this.id,this._font.HVAR)),this._metrics={advanceWidth:t,advanceHeight:r,leftBearing:s,topBearing:n}}get cbox(){return this._getCBox()}get bbox(){return this._getBBox()}get path(){return this._getPath()}getScaledPath(e){let t=1/this._font.unitsPerEm*e;return this.path.scale(t)}get advanceWidth(){return this._getMetrics().advanceWidth}get advanceHeight(){return this._getMetrics().advanceHeight}get ligatureCaretPositions(){}_getName(){let{post:e}=this._font;if(!e)return null;switch(e.version){case 1:return n6[this.id];case 2:let t=e.glyphNameIndex[this.id];if(t<n6.length)return n6[t];return e.names[t-n6.length];case 2.5:return n6[this.id+e.offsets[this.id]];case 4:return String.fromCharCode(e.map[this.id])}}get name(){return this._getName()}render(e,t){e.save();let s=1/this._font.head.unitsPerEm*t;e.scale(s,s),this.path.toFunction()(e),e.fill(),e.restore()}constructor(e,t,s){this.id=e,this.codePoints=t,this._font=s,this.isMark=this.codePoints.length>0&&this.codePoints.every(o.qi),this.isLigature=this.codePoints.length>1}}(0,a.Cg)([C],n9.prototype,"cbox",null),(0,a.Cg)([C],n9.prototype,"bbox",null),(0,a.Cg)([C],n9.prototype,"path",null),(0,a.Cg)([C],n9.prototype,"advanceWidth",null),(0,a.Cg)([C],n9.prototype,"advanceHeight",null),(0,a.Cg)([C],n9.prototype,"name",null);let n7=new r._k({numberOfContours:r.l8,xMin:r.l8,yMin:r.l8,xMax:r.l8,yMax:r.l8});class ae{copy(){return new ae(this.onCurve,this.endContour,this.x,this.y)}constructor(e,t,s=0,r=0){this.onCurve=e,this.endContour=t,this.x=s,this.y=r}}class at{constructor(e,t,s){this.glyphID=e,this.dx=t,this.dy=s,this.pos=0,this.scaleX=this.scaleY=1,this.scale01=this.scale10=0}}class as extends n9{_getCBox(e){if(this._font._variationProcessor&&!e)return this.path.cbox;let t=this._font._getTableStream("glyf");t.pos+=this._font.loca.offsets[this.id];let s=n7.decode(t);return Object.freeze(new rC(s.xMin,s.yMin,s.xMax,s.yMax))}_parseGlyphCoord(e,t,s,r){if(s){var n=e.readUInt8();r||(n=-n),n+=t}else if(r)var n=t;else var n=t+e.readInt16BE();return n}_decode(){let e=this._font.loca.offsets[this.id];if(e===this._font.loca.offsets[this.id+1])return null;let t=this._font._getTableStream("glyf");t.pos+=e;let s=t.pos,r=n7.decode(t);return r.numberOfContours>0?this._decodeSimple(r,t):r.numberOfContours<0&&this._decodeComposite(r,t,s),r}_decodeSimple(e,t){e.points=[];let s=new r.O3(r.oe,e.numberOfContours).decode(t);e.instructions=new r.O3(r.hV,r.oe).decode(t);let n=[],a=s[s.length-1]+1;for(;n.length<a;){var l=t.readUInt8();if(n.push(l),8&l){let e=t.readUInt8();for(let t=0;t<e;t++)n.push(l)}}for(var o=0;o<n.length;o++){var l=n[o];let t=new ae(!!(1&l),s.indexOf(o)>=0,0,0);e.points.push(t)}let i=0;for(var o=0;o<n.length;o++){var l=n[o];e.points[o].x=i=this._parseGlyphCoord(t,i,2&l,16&l)}let u=0;for(var o=0;o<n.length;o++){var l=n[o];e.points[o].y=u=this._parseGlyphCoord(t,u,4&l,32&l)}if(this._font._variationProcessor){let t=e.points.slice();t.push(...this._getPhantomPoints(e)),this._font._variationProcessor.transformPoints(this.id,t),e.phantomPoints=t.slice(-4)}}_decodeComposite(e,t,s=0){e.components=[];let r=!1,n=32;for(;32&n;){n=t.readUInt16BE();let i=t.pos-s,u=t.readUInt16BE();if(r||(r=(256&n)!=0),1&n)var a=t.readInt16BE(),l=t.readInt16BE();else var a=t.readInt8(),l=t.readInt8();var o=new at(u,a,l);o.pos=i,8&n?o.scaleX=o.scaleY=(t.readUInt8()<<24|t.readUInt8()<<16)/0x40000000:64&n?(o.scaleX=(t.readUInt8()<<24|t.readUInt8()<<16)/0x40000000,o.scaleY=(t.readUInt8()<<24|t.readUInt8()<<16)/0x40000000):128&n&&(o.scaleX=(t.readUInt8()<<24|t.readUInt8()<<16)/0x40000000,o.scale01=(t.readUInt8()<<24|t.readUInt8()<<16)/0x40000000,o.scale10=(t.readUInt8()<<24|t.readUInt8()<<16)/0x40000000,o.scaleY=(t.readUInt8()<<24|t.readUInt8()<<16)/0x40000000),e.components.push(o)}if(this._font._variationProcessor){let t=[];for(let s=0;s<e.components.length;s++){var o=e.components[s];t.push(new ae(!0,!0,o.dx,o.dy))}t.push(...this._getPhantomPoints(e)),this._font._variationProcessor.transformPoints(this.id,t),e.phantomPoints=t.splice(-4,4);for(let s=0;s<t.length;s++){let r=t[s];e.components[s].dx=r.x,e.components[s].dy=r.y}}return r}_getPhantomPoints(e){let t=this._getCBox(!0);null==this._metrics&&(this._metrics=n9.prototype._getMetrics.call(this,t));let{advanceWidth:s,advanceHeight:r,leftBearing:n,topBearing:a}=this._metrics;return[new ae(!1,!0,e.xMin-n,0),new ae(!1,!0,e.xMin-n+s,0),new ae(!1,!0,0,e.yMax+a),new ae(!1,!0,0,e.yMax+a+r)]}_getContours(){let e=this._decode();if(!e)return[];let t=[];if(e.numberOfContours<0)for(let s of e.components){let e=this._font.getGlyph(s.glyphID)._getContours();for(let r=0;r<e.length;r++){let n=e[r];for(let e=0;e<n.length;e++){let r=n[e],a=r.x*s.scaleX+r.y*s.scale01+s.dx,l=r.y*s.scaleY+r.x*s.scale10+s.dy;t.push(new ae(r.onCurve,r.endContour,a,l))}}}else t=e.points||[];e.phantomPoints&&!this._font.directory.tables.HVAR&&(this._metrics.advanceWidth=e.phantomPoints[1].x-e.phantomPoints[0].x,this._metrics.advanceHeight=e.phantomPoints[3].y-e.phantomPoints[2].y,this._metrics.leftBearing=e.xMin-e.phantomPoints[0].x,this._metrics.topBearing=e.phantomPoints[2].y-e.yMax);let s=[],r=[];for(let e=0;e<t.length;e++){var n=t[e];r.push(n),n.endContour&&(s.push(r),r=[])}return s}_getMetrics(){if(this._metrics)return this._metrics;let e=this._getCBox(!0);return super._getMetrics(e),this._font._variationProcessor&&!this._font.HVAR&&this.path,this._metrics}_getPath(){let e=this._getContours(),t=new n8;for(let r=0;r<e.length;r++){let n=e[r],a=n[0],l=n[n.length-1],o=0;if(a.onCurve){var s=null;o=1}else var s=a=l.onCurve?l:new ae(!1,!1,(a.x+l.x)/2,(a.y+l.y)/2);t.moveTo(a.x,a.y);for(let e=o;e<n.length;e++){let r=n[e],l=0===e?a:n[e-1];if(l.onCurve&&r.onCurve)t.lineTo(r.x,r.y);else if(l.onCurve&&!r.onCurve)var s=r;else if(l.onCurve||r.onCurve)if(!l.onCurve&&r.onCurve){t.quadraticCurveTo(s.x,s.y,r.x,r.y);var s=null}else throw Error("Unknown TTF path state");else{let e=(l.x+r.x)/2,n=(l.y+r.y)/2;t.quadraticCurveTo(l.x,l.y,e,n);var s=r}}s&&t.quadraticCurveTo(s.x,s.y,a.x,a.y),t.closePath()}return t}constructor(...e){super(...e),(0,n._)(this,"type","TTF")}}class ar extends n9{_getName(){return this._font.CFF2?super._getName():this._font["CFF "].getGlyphName(this.id)}bias(e){return e.length<1240?107:e.length<33900?1131:32768}_getPath(){let e,t,s=this._font.CFF2||this._font["CFF "],{stream:r}=s,n=s.topDict.CharStrings[this.id],a=n.offset+n.length;r.pos=n.offset;let l=new n8,o=[],i=[],u=null,c=0,h=0,f=0,p=!1;this._usedGsubrs=e={},this._usedSubrs=t={};let d=s.globalSubrIndex||[],g=this.bias(d),m=s.privateDictForGlyph(this.id)||{},b=m.Subrs||[],y=this.bias(b),w=s.topDict.vstore&&s.topDict.vstore.itemVariationStore,v=m.vsindex,_=this._font._variationProcessor;function C(){null==u&&(u=o.shift()+m.nominalWidthX)}function x(){return o.length%2!=0&&C(),c+=o.length>>1,o.length=0}function S(e,t){p&&l.closePath(),l.moveTo(e,t),p=!0}let k=function(){for(;r.pos<a;){let m=r.readUInt8();if(m<32){let u,I,P,A,O,D,T,F,M,L,G,E,V,B,z,R;switch(m){case 1:case 3:case 18:case 23:x();break;case 4:o.length>1&&C(),f+=o.shift(),S(h,f);break;case 5:for(;o.length>=2;)h+=o.shift(),f+=o.shift(),l.lineTo(h,f);break;case 6:case 7:for(P=6===m;o.length>=1;)P?h+=o.shift():f+=o.shift(),l.lineTo(h,f),P=!P;break;case 8:for(;o.length>0;)A=h+o.shift(),O=f+o.shift(),D=A+o.shift(),T=O+o.shift(),h=D+o.shift(),f=T+o.shift(),l.bezierCurveTo(A,O,D,T,h,f);break;case 10:if(I=b[u=o.pop()+y]){t[u]=!0;let e=r.pos,s=a;r.pos=I.offset,a=I.offset+I.length,k(),r.pos=e,a=s}break;case 11:if(s.version>=2)break;return;case 14:if(s.version>=2)break;o.length>0&&C(),p&&(l.closePath(),p=!1);break;case 15:if(s.version<2)throw Error("vsindex operator not supported in CFF v1");v=o.pop();break;case 16:{if(s.version<2)throw Error("blend operator not supported in CFF v1");if(!_)throw Error("blend operator in non-variation font");let e=_.getBlendVector(w,v),t=o.pop(),r=t*e.length,n=o.length-r,a=n-t;for(let s=0;s<t;s++){let t=o[a+s];for(let s=0;s<e.length;s++)t+=e[s]*o[n++];o[a+s]=t}for(;r--;)o.pop();break}case 19:case 20:x(),r.pos+=c+7>>3;break;case 21:o.length>2&&C(),h+=o.shift(),f+=o.shift(),S(h,f);break;case 22:o.length>1&&C(),S(h+=o.shift(),f);break;case 24:for(;o.length>=8;)A=h+o.shift(),O=f+o.shift(),D=A+o.shift(),T=O+o.shift(),h=D+o.shift(),f=T+o.shift(),l.bezierCurveTo(A,O,D,T,h,f);h+=o.shift(),f+=o.shift(),l.lineTo(h,f);break;case 25:for(;o.length>=8;)h+=o.shift(),f+=o.shift(),l.lineTo(h,f);A=h+o.shift(),O=f+o.shift(),D=A+o.shift(),T=O+o.shift(),h=D+o.shift(),f=T+o.shift(),l.bezierCurveTo(A,O,D,T,h,f);break;case 26:for(o.length%2&&(h+=o.shift());o.length>=4;)A=h,O=f+o.shift(),D=A+o.shift(),T=O+o.shift(),h=D,f=T+o.shift(),l.bezierCurveTo(A,O,D,T,h,f);break;case 27:for(o.length%2&&(f+=o.shift());o.length>=4;)A=h+o.shift(),O=f,D=A+o.shift(),T=O+o.shift(),h=D+o.shift(),f=T,l.bezierCurveTo(A,O,D,T,h,f);break;case 28:o.push(r.readInt16BE());break;case 29:if(I=d[u=o.pop()+g]){e[u]=!0;let t=r.pos,s=a;r.pos=I.offset,a=I.offset+I.length,k(),r.pos=t,a=s}break;case 30:case 31:for(P=31===m;o.length>=4;)P?(A=h+o.shift(),O=f,D=A+o.shift(),f=(T=O+o.shift())+o.shift(),h=D+(1===o.length?o.shift():0)):(A=h,O=f+o.shift(),D=A+o.shift(),T=O+o.shift(),h=D+o.shift(),f=T+(1===o.length?o.shift():0)),l.bezierCurveTo(A,O,D,T,h,f),P=!P;break;case 12:switch(m=r.readUInt8()){case 3:let N=o.pop(),U=o.pop();o.push(N&&U?1:0);break;case 4:N=o.pop(),U=o.pop(),o.push(N||U?1:0);break;case 5:N=o.pop(),o.push(+!N);break;case 9:N=o.pop(),o.push(Math.abs(N));break;case 10:N=o.pop(),U=o.pop(),o.push(N+U);break;case 11:N=o.pop(),U=o.pop(),o.push(N-U);break;case 12:N=o.pop(),U=o.pop(),o.push(N/U);break;case 14:N=o.pop(),o.push(-N);break;case 15:N=o.pop(),U=o.pop(),o.push(+(N===U));break;case 18:o.pop();break;case 20:let q=o.pop(),H=o.pop();i[H]=q;break;case 21:H=o.pop(),o.push(i[H]||0);break;case 22:let j=o.pop(),X=o.pop(),Y=o.pop(),W=o.pop();o.push(Y<=W?j:X);break;case 23:o.push(Math.random());break;case 24:N=o.pop(),U=o.pop(),o.push(N*U);break;case 26:N=o.pop(),o.push(Math.sqrt(N));break;case 27:N=o.pop(),o.push(N,N);break;case 28:N=o.pop(),U=o.pop(),o.push(U,N);break;case 29:(H=o.pop())<0?H=0:H>o.length-1&&(H=o.length-1),o.push(o[H]);break;case 30:let Z=o.pop(),J=o.pop();if(J>=0)for(;J>0;){var n=o[Z-1];for(let e=Z-2;e>=0;e--)o[e+1]=o[e];o[0]=n,J--}else for(;J<0;){var n=o[0];for(let e=0;e<=Z;e++)o[e]=o[e+1];o[Z-1]=n,J++}break;case 34:A=h+o.shift(),O=f,D=A+o.shift(),T=O+o.shift(),F=D+o.shift(),M=T,L=F+o.shift(),G=M,E=L+o.shift(),V=G,B=E+o.shift(),z=V,h=B,f=z,l.bezierCurveTo(A,O,D,T,F,M),l.bezierCurveTo(L,G,E,V,B,z);break;case 35:R=[];for(let e=0;e<=5;e++)h+=o.shift(),f+=o.shift(),R.push(h,f);l.bezierCurveTo(...R.slice(0,6)),l.bezierCurveTo(...R.slice(6)),o.shift();break;case 36:A=h+o.shift(),O=f+o.shift(),D=A+o.shift(),T=O+o.shift(),F=D+o.shift(),M=T,L=F+o.shift(),G=M,E=L+o.shift(),V=G+o.shift(),B=E+o.shift(),z=V,h=B,f=z,l.bezierCurveTo(A,O,D,T,F,M),l.bezierCurveTo(L,G,E,V,B,z);break;case 37:let K=h,Q=f;R=[];for(let e=0;e<=4;e++)h+=o.shift(),f+=o.shift(),R.push(h,f);Math.abs(h-K)>Math.abs(f-Q)?(h+=o.shift(),f=Q):(h=K,f+=o.shift()),R.push(h,f),l.bezierCurveTo(...R.slice(0,6)),l.bezierCurveTo(...R.slice(6));break;default:throw Error(`Unknown op: 12 ${m}`)}break;default:throw Error(`Unknown op: ${m}`)}}else if(m<247)o.push(m-139);else if(m<251){var u=r.readUInt8();o.push((m-247)*256+u+108)}else if(m<255){var u=r.readUInt8();o.push(-(256*(m-251))-u-108)}else o.push(r.readInt32BE()/65536)}};return k(),p&&l.closePath(),l}constructor(...e){super(...e),(0,n._)(this,"type","CFF")}}let an=new r._k({originX:r.oe,originY:r.oe,type:new r.Qf(4),data:new r.hp(e=>e.parent.buflen-e._currentOffset)});class aa extends as{getImageForSize(e){for(let s=0;s<this._font.sbix.imageTables.length;s++){var t=this._font.sbix.imageTables[s];if(t.ppem>=e)break}let s=t.imageOffsets,r=s[this.id],n=s[this.id+1];return r===n?null:(this._font.stream.pos=r,an.decode(this._font.stream,{buflen:n-r}))}render(e,t){let s=this.getImageForSize(t);if(null!=s){let r=t/this._font.unitsPerEm;e.image(s.data,{height:t,x:s.originX,y:(this.bbox.minY-s.originY)*r})}this._font.sbix.flags.renderOutlines&&super.render(e,t)}constructor(...e){super(...e),(0,n._)(this,"type","SBIX")}}class al{constructor(e,t){this.glyph=e,this.color=t}}class ao extends n9{_getBBox(){let e=new rC;for(let t=0;t<this.layers.length;t++){let s=this.layers[t].glyph.bbox;e.addPoint(s.minX,s.minY),e.addPoint(s.maxX,s.maxY)}return e}get layers(){let e=this._font.CPAL,t=this._font.COLR,s=0,r=t.baseGlyphRecord.length-1;for(;s<=r;){let e=s+r>>1;var n=t.baseGlyphRecord[e];if(this.id<n.gid)r=e-1;else if(this.id>n.gid)s=e+1;else{var a=n;break}}if(null==a){var l=this._font._getBaseGlyph(this.id),o={red:0,green:0,blue:0,alpha:255};return[new al(l,o)]}let i=[];for(let s=a.firstLayerIndex;s<a.firstLayerIndex+a.numLayers;s++){var n=t.layerRecords[s],o=e.colorRecords[n.paletteIndex],l=this._font._getBaseGlyph(n.gid);i.push(new al(l,o))}return i}render(e,t){for(let{glyph:s,color:r}of this.layers)e.fillColor([r.red,r.green,r.blue],r.alpha/255*100),s.render(e,t)}constructor(...e){super(...e),(0,n._)(this,"type","COLR")}}class ai{normalizeCoords(e){let t=[];for(var s=0;s<this.font.fvar.axis.length;s++){let r=this.font.fvar.axis[s];e[s]<r.defaultValue?t.push((e[s]-r.defaultValue+Number.EPSILON)/(r.defaultValue-r.minValue+Number.EPSILON)):t.push((e[s]-r.defaultValue+Number.EPSILON)/(r.maxValue-r.defaultValue+Number.EPSILON))}if(this.font.avar)for(var s=0;s<this.font.avar.segment.length;s++){let e=this.font.avar.segment[s];for(let r=0;r<e.correspondence.length;r++){let n=e.correspondence[r];if(r>=1&&t[s]<n.fromCoord){let a=e.correspondence[r-1];t[s]=((t[s]-a.fromCoord)*(n.toCoord-a.toCoord)+Number.EPSILON)/(n.fromCoord-a.fromCoord+Number.EPSILON)+a.toCoord;break}}}return t}transformPoints(e,t){if(!this.font.fvar||!this.font.gvar)return;let{gvar:s}=this.font;if(e>=s.glyphCount)return;let r=s.offsets[e];if(r===s.offsets[e+1])return;let{stream:n}=this.font;if(n.pos=r,n.pos>=n.length)return;let a=n.readUInt16BE(),l=r+n.readUInt16BE();if(32768&a){var o=n.pos;n.pos=l;var i=this.decodePoints();l=n.pos,n.pos=o}let u=t.map(e=>e.copy());a&=4095;for(let e=0;e<a;e++){let e=n.readUInt16BE(),r=n.readUInt16BE();if(32768&r){var c=[];for(let e=0;e<s.axisCount;e++)c.push(n.readInt16BE()/16384)}else{if((4095&r)>=s.globalCoordCount)throw Error("Invalid gvar table");var c=s.globalCoords[4095&r]}if(16384&r){var h=[];for(let e=0;e<s.axisCount;e++)h.push(n.readInt16BE()/16384);var f=[];for(let e=0;e<s.axisCount;e++)f.push(n.readInt16BE()/16384)}let a=this.tupleFactor(r,c,h,f);if(0===a){l+=e;continue}var o=n.pos;if(n.pos=l,8192&r)var p=this.decodePoints();else var p=i;let g=0===p.length?t.length:p.length,m=this.decodeDeltas(g),b=this.decodeDeltas(g);if(0===p.length)for(let e=0;e<t.length;e++){var d=t[e];d.x+=Math.round(m[e]*a),d.y+=Math.round(b[e]*a)}else{let e=u.map(e=>e.copy()),s=t.map(()=>!1);for(let r=0;r<p.length;r++){let n=p[r];if(n<t.length){let t=e[n];s[n]=!0,t.x+=m[r]*a,t.y+=b[r]*a}}this.interpolateMissingDeltas(e,u,s);for(let s=0;s<t.length;s++){let r=e[s].x-u[s].x,n=e[s].y-u[s].y;t[s].x=Math.round(t[s].x+r),t[s].y=Math.round(t[s].y+n)}}l+=e,n.pos=o}}decodePoints(){let e=this.font.stream,t=e.readUInt8();128&t&&(t=(127&t)<<8|e.readUInt8());let s=new Uint16Array(t),r=0,n=0;for(;r<t;){let a=e.readUInt8(),l=(127&a)+1,o=128&a?e.readUInt16:e.readUInt8;for(let a=0;a<l&&r<t;a++)n+=o.call(e),s[r++]=n}return s}decodeDeltas(e){let t=this.font.stream,s=0,r=new Int16Array(e);for(;s<e;){let n=t.readUInt8(),a=(63&n)+1;if(128&n)s+=a;else{let l=64&n?t.readInt16BE:t.readInt8;for(let n=0;n<a&&s<e;n++)r[s++]=l.call(t)}}return r}tupleFactor(e,t,s,r){let n=this.normalizedCoords,{gvar:a}=this.font,l=1;for(let o=0;o<a.axisCount;o++)if(0!==t[o]){if(0===n[o])return 0;if((16384&e)==0){if(n[o]<Math.min(0,t[o])||n[o]>Math.max(0,t[o]))return 0;l=(l*n[o]+Number.EPSILON)/(t[o]+Number.EPSILON)}else{if(n[o]<s[o]||n[o]>r[o])return 0;l=n[o]<t[o]?l*(n[o]-s[o]+Number.EPSILON)/(t[o]-s[o]+Number.EPSILON):l*(r[o]-n[o]+Number.EPSILON)/(r[o]-t[o]+Number.EPSILON)}}return l}interpolateMissingDeltas(e,t,s){if(0===e.length)return;let r=0;for(;r<e.length;){let n=r,a=r,l=e[a];for(;!l.endContour;)l=e[++a];for(;r<=a&&!s[r];)r++;if(r>a)continue;let o=r,i=r;for(r++;r<=a;)s[r]&&(this.deltaInterpolate(i+1,r-1,i,r,t,e),i=r),r++;i===o?this.deltaShift(n,a,i,t,e):(this.deltaInterpolate(i+1,a,i,o,t,e),o>0&&this.deltaInterpolate(n,o-1,i,o,t,e)),r=a+1}}deltaInterpolate(e,t,s,r,n,a){if(e>t)return;let l=["x","y"];for(let i=0;i<l.length;i++){let u=l[i];if(n[s][u]>n[r][u]){var o=s;s=r,r=o}let c=n[s][u],h=n[r][u],f=a[s][u],p=a[r][u];if(c!==h||f===p){let s=c===h?0:(p-f)/(h-c);for(let r=e;r<=t;r++){let e=n[r][u];e<=c?e+=f-c:e>=h?e+=p-h:e=f+(e-c)*s,a[r][u]=e}}}}deltaShift(e,t,s,r,n){let a=n[s].x-r[s].x,l=n[s].y-r[s].y;if(0!==a||0!==l)for(let r=e;r<=t;r++)r!==s&&(n[r].x+=a,n[r].y+=l)}getAdvanceAdjustment(e,t){let s,r;if(t.advanceWidthMapping){let n=e;n>=t.advanceWidthMapping.mapCount&&(n=t.advanceWidthMapping.mapCount-1),t.advanceWidthMapping.entryFormat,{outerIndex:s,innerIndex:r}=t.advanceWidthMapping.mapData[n]}else s=0,r=e;return this.getDelta(t.itemVariationStore,s,r)}getDelta(e,t,s){if(t>=e.itemVariationData.length)return 0;let r=e.itemVariationData[t];if(s>=r.deltaSets.length)return 0;let n=r.deltaSets[s],a=this.getBlendVector(e,t),l=0;for(let e=0;e<r.regionIndexCount;e++)l+=n.deltas[e]*a[e];return l}getBlendVector(e,t){let s=e.itemVariationData[t];if(this.blendVectors.has(s))return this.blendVectors.get(s);let r=this.normalizedCoords,n=[];for(let t=0;t<s.regionIndexCount;t++){let a=1,l=s.regionIndexes[t],o=e.variationRegionList.variationRegions[l];for(let e=0;e<o.length;e++){let t,s=o[e];a*=s.startCoord>s.peakCoord||s.peakCoord>s.endCoord||s.startCoord<0&&s.endCoord>0&&0!==s.peakCoord||0===s.peakCoord?1:r[e]<s.startCoord||r[e]>s.endCoord?0:r[e]===s.peakCoord?1:r[e]<s.peakCoord?(r[e]-s.startCoord+Number.EPSILON)/(s.peakCoord-s.startCoord+Number.EPSILON):(s.endCoord-r[e]+Number.EPSILON)/(s.endCoord-s.peakCoord+Number.EPSILON)}n[t]=a}return this.blendVectors.set(s,n),n}constructor(e,t){this.font=e,this.normalizedCoords=this.normalizeCoords(t),this.blendVectors=new Map}}Promise.resolve();class au{includeGlyph(e){return"object"==typeof e&&(e=e.id),null==this.mapping[e]&&(this.glyphs.push(e),this.mapping[e]=this.glyphs.length-1),this.mapping[e]}constructor(e){this.font=e,this.glyphs=[],this.mapping={},this.includeGlyph(0)}}class ac{static size(e){return e>=0&&e<=255?1:2}static encode(e,t){t>=0&&t<=255?e.writeUInt8(t):e.writeInt16BE(t)}}let ah=new r._k({numberOfContours:r.l8,xMin:r.l8,yMin:r.l8,xMax:r.l8,yMax:r.l8,endPtsOfContours:new r.O3(r.oe,"numberOfContours"),instructions:new r.O3(r.hV,r.oe),flags:new r.O3(r.hV,0),xPoints:new r.O3(ac,0),yPoints:new r.O3(ac,0)});class af{encodeSimple(e,t=[]){let s=[],n=[],a=[],l=[],o=0,i=0,u=0,c=0,h=0;for(let t=0;t<e.commands.length;t++){let r=e.commands[t];for(let s=0;s<r.args.length;s+=2){let f=r.args[s],p=r.args[s+1],d=0;if("quadraticCurveTo"===r.command&&2===s){let s=e.commands[t+1];if(s&&"quadraticCurveTo"===s.command){let e=(i+s.args[0])/2,t=(u+s.args[1])/2;if(f===e&&p===t)continue}}("quadraticCurveTo"!==r.command||0!==s)&&(d|=1),d=this._encodePoint(f,i,n,d,2,16),(d=this._encodePoint(p,u,a,d,4,32))===c&&o<255?(l[l.length-1]|=8,o++):(o>0&&(l.push(o),o=0),l.push(d),c=d),i=f,u=p,h++}"closePath"===r.command&&s.push(h-1)}e.commands.length>1&&"closePath"!==e.commands[e.commands.length-1].command&&s.push(h-1);let f=e.bbox,p={numberOfContours:s.length,xMin:f.minX,yMin:f.minY,xMax:f.maxX,yMax:f.maxY,endPtsOfContours:s,instructions:t,flags:l,xPoints:n,yPoints:a},d=ah.size(p),g=4-d%4,m=new r.vO(d+g);return ah.encode(m,p),0!==g&&m.fill(0,g),m.buffer}_encodePoint(e,t,s,r,n,a){let l=e-t;return e===t?r|=a:(-255<=l&&l<=255&&(r|=n,l<0?l=-l:r|=a),s.push(l)),r}}class ap extends au{_addGlyph(e){let t=this.font.getGlyph(e),s=t._decode(),r=this.font.loca.offsets[e],n=this.font.loca.offsets[e+1],a=this.font._getTableStream("glyf");a.pos+=r;let l=a.readBuffer(n-r);if(s&&s.numberOfContours<0){let t=new DataView((l=new Uint8Array(l)).buffer);for(let r of s.components)e=this.includeGlyph(r.glyphID),t.setUint16(r.pos,e)}else s&&this.font._variationProcessor&&(l=this.glyphEncoder.encodeSimple(t.path,s.instructions));return this.glyf.push(l),this.loca.offsets.push(this.offset),this.hmtx.metrics.push({advance:t.advanceWidth,bearing:t._getMetrics().leftBearing}),this.offset+=l.length,this.glyf.length-1}encode(){this.glyf=[],this.offset=0,this.loca={offsets:[],version:this.font.loca.version},this.hmtx={metrics:[],bearings:[]};let e=0;for(;e<this.glyphs.length;)this._addGlyph(this.glyphs[e++]);let t=c(this.font.maxp);t.numGlyphs=this.glyf.length,this.loca.offsets.push(this.offset);let s=c(this.font.head);s.indexToLocFormat=this.loca.version;let r=c(this.font.hhea);return r.numberOfMetrics=this.hmtx.metrics.length,rf.toBuffer({tables:{head:s,hhea:r,loca:this.loca,maxp:t,"cvt ":this.font["cvt "],prep:this.font.prep,glyf:this.glyf,hmtx:this.hmtx,fpgm:this.font.fpgm}})}constructor(e){super(e),this.glyphEncoder=new af}}class ad extends au{subsetCharstrings(){this.charstrings=[];let e={};for(let t of this.glyphs){this.charstrings.push(this.cff.getCharString(t));let s=this.font.getGlyph(t);for(let t in s.path,s._usedGsubrs)e[t]=!0}this.gsubrs=this.subsetSubrs(this.cff.globalSubrIndex,e)}subsetSubrs(e,t){let s=[];for(let r=0;r<e.length;r++){let n=e[r];t[r]?(this.cff.stream.pos=n.offset,s.push(this.cff.stream.readBuffer(n.length))):s.push(new Uint8Array([11]))}return s}subsetFontdict(e){e.FDArray=[],e.FDSelect={version:0,fds:[]};let t={},s=[],r={};for(let n of this.glyphs){let a=this.cff.fdForGlyph(n);if(null==a)continue;t[a]||(e.FDArray.push(Object.assign({},this.cff.topDict.FDArray[a])),s.push({}),r[a]=e.FDArray.length-1),t[a]=!0,e.FDSelect.fds.push(r[a]);let l=this.font.getGlyph(n);for(let e in l.path,l._usedSubrs)s[r[a]][e]=!0}for(let t=0;t<e.FDArray.length;t++){let r=e.FDArray[t];delete r.FontName,r.Private&&r.Private.Subrs&&(r.Private=Object.assign({},r.Private),r.Private.Subrs=this.subsetSubrs(r.Private.Subrs,s[t]))}}createCIDFontdict(e){let t={};for(let e of this.glyphs){let s=this.font.getGlyph(e);for(let e in s.path,s._usedSubrs)t[e]=!0}let s=Object.assign({},this.cff.topDict.Private);return this.cff.topDict.Private&&this.cff.topDict.Private.Subrs&&(s.Subrs=this.subsetSubrs(this.cff.topDict.Private.Subrs,t)),e.FDArray=[{Private:s}],e.FDSelect={version:3,nRanges:1,ranges:[{first:0,fd:0}],sentinel:this.charstrings.length}}addString(e){return e?(this.strings||(this.strings=[]),this.strings.push(e),ef.length+this.strings.length-1):null}encode(){this.subsetCharstrings();let e={version:this.charstrings.length>255?2:1,ranges:[{first:1,nLeft:this.charstrings.length-2}]},t=Object.assign({},this.cff.topDict);for(let s of(t.Private=null,t.charset=e,t.Encoding=null,t.CharStrings=this.charstrings,["version","Notice","Copyright","FullName","FamilyName","Weight","PostScript","BaseFontName","FontName"]))t[s]=this.addString(this.cff.string(t[s]));t.ROS=[this.addString("Adobe"),this.addString("Identity"),0],t.CIDCount=this.charstrings.length,this.cff.isCIDFont?this.subsetFontdict(t):this.createCIDFontdict(t);let s={version:1,hdrSize:this.cff.hdrSize,offSize:4,header:this.cff.header,nameIndex:[this.cff.postscriptName],topDictIndex:[t],stringIndex:this.strings,globalSubrIndex:this.gsubrs};return tn.toBuffer(s)}constructor(e){if(super(e),this.cff=this.font["CFF "],!this.cff)throw Error("Not a CFF Font")}}class ag{static probe(e){let t=rg.decode(e.slice(0,4));return"true"===t||"OTTO"===t||t===String.fromCharCode(0,1,0,0)}setDefaultLanguage(e=null){this.defaultLanguage=e}_getTable(e){if(!(e.tag in this._tables))try{this._tables[e.tag]=this._decodeTable(e)}catch(t){m&&(console.error(`Error decoding table ${e.tag}`),console.error(t.stack))}return this._tables[e.tag]}_getTableStream(e){let t=this.directory.tables[e];return t?(this.stream.pos=t.offset,this.stream):null}_decodeDirectory(){return this.directory=rf.decode(this.stream,{_startOffset:0})}_decodeTable(e){let t=this.stream.pos,s=this._getTableStream(e.tag),r=rc[e.tag].decode(s,this,e.length);return this.stream.pos=t,r}getName(e,t=this.defaultLanguage||v){let s=this.name&&this.name.records[e];return s&&(s[t]||s[this.defaultLanguage]||s[v]||s.en||s[Object.keys(s)[0]])||null}get postscriptName(){return this.getName("postscriptName")}get fullName(){return this.getName("fullName")}get familyName(){return this.getName("fontFamily")}get subfamilyName(){return this.getName("fontSubfamily")}get copyright(){return this.getName("copyright")}get version(){return this.getName("version")}get ascent(){return this.hhea.ascent}get descent(){return this.hhea.descent}get lineGap(){return this.hhea.lineGap}get underlinePosition(){return this.post.underlinePosition}get underlineThickness(){return this.post.underlineThickness}get italicAngle(){return this.post.italicAngle}get capHeight(){let e=this["OS/2"];return e?e.capHeight:this.ascent}get xHeight(){let e=this["OS/2"];return e?e.xHeight:0}get numGlyphs(){return this.maxp.numGlyphs}get unitsPerEm(){return this.head.unitsPerEm}get bbox(){return Object.freeze(new rC(this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax))}get _cmapProcessor(){return new rw(this.cmap)}get characterSet(){return this._cmapProcessor.getCharacterSet()}hasGlyphForCodePoint(e){return!!this._cmapProcessor.lookup(e)}glyphForCodePoint(e){return this.getGlyph(this._cmapProcessor.lookup(e),[e])}glyphsForString(e){let t=[],s=e.length,r=0,n=-1,a=-1;for(;r<=s;){let l=0,o=0;if(r<s){if(55296<=(l=e.charCodeAt(r++))&&l<=56319&&r<s){let t=e.charCodeAt(r);56320<=t&&t<=57343&&(r++,l=((1023&l)<<10)+(1023&t)+65536)}o=+(65024<=l&&l<=65039||917760<=l&&l<=917999)}else r++;0===a&&1===o?t.push(this.getGlyph(this._cmapProcessor.lookup(n,l),[n,l])):0===a&&0===o&&t.push(this.glyphForCodePoint(n)),n=l,a=o}return t}get _layoutEngine(){return new n4(this)}layout(e,t,s,r,n){return this._layoutEngine.layout(e,t,s,r,n)}stringsForGlyph(e){return this._layoutEngine.stringsForGlyph(e)}get availableFeatures(){return this._layoutEngine.getAvailableFeatures()}getAvailableFeatures(e,t){return this._layoutEngine.getAvailableFeatures(e,t)}_getBaseGlyph(e,t=[]){return!this._glyphs[e]&&(this.directory.tables.glyf?this._glyphs[e]=new as(e,t,this):(this.directory.tables["CFF "]||this.directory.tables.CFF2)&&(this._glyphs[e]=new ar(e,t,this))),this._glyphs[e]||null}getGlyph(e,t=[]){return this._glyphs[e]||(this.directory.tables.sbix?this._glyphs[e]=new aa(e,t,this):this.directory.tables.COLR&&this.directory.tables.CPAL?this._glyphs[e]=new ao(e,t,this):this._getBaseGlyph(e,t)),this._glyphs[e]||null}createSubset(){return this.directory.tables["CFF "]?new ad(this):new ap(this)}get variationAxes(){let e={};if(!this.fvar)return e;for(let t of this.fvar.axis)e[t.axisTag.trim()]={name:t.name.en,min:t.minValue,default:t.defaultValue,max:t.maxValue};return e}get namedVariations(){let e={};if(!this.fvar)return e;for(let t of this.fvar.instance){let s={};for(let e=0;e<this.fvar.axis.length;e++)s[this.fvar.axis[e].axisTag.trim()]=t.coord[e];e[t.name.en]=s}return e}getVariation(e){if(!(this.directory.tables.fvar&&(this.directory.tables.gvar&&this.directory.tables.glyf||this.directory.tables.CFF2)))throw Error("Variations require a font with the fvar, gvar and glyf, or CFF2 tables.");if("string"==typeof e&&(e=this.namedVariations[e]),"object"!=typeof e)throw Error("Variation settings must be either a variation name or settings object.");let t=this.fvar.axis.map((t,s)=>{let r=t.axisTag.trim();return r in e?Math.max(t.minValue,Math.min(t.maxValue,e[r])):t.defaultValue}),s=new r.HI(this.stream.buffer);s.pos=this._directoryPos;let n=new ag(s,t);return n._tables=this._tables,n}get _variationProcessor(){if(!this.fvar)return null;let e=this.variationCoords;return e||this.CFF2?(e||(e=this.fvar.axis.map(e=>e.defaultValue)),new ai(this,e)):null}getFont(e){return this.getVariation(e)}constructor(e,t=null){for(let s in(0,n._)(this,"type","TTF"),this.defaultLanguage=null,this.stream=e,this.variationCoords=t,this._directoryPos=this.stream.pos,this._tables={},this._glyphs={},this._decodeDirectory(),this.directory.tables){let e=this.directory.tables[s];rc[s]&&e.length>0&&Object.defineProperty(this,s,{get:this._getTable.bind(this,e)})}}}(0,a.Cg)([C],ag.prototype,"bbox",null),(0,a.Cg)([C],ag.prototype,"_cmapProcessor",null),(0,a.Cg)([C],ag.prototype,"characterSet",null),(0,a.Cg)([C],ag.prototype,"_layoutEngine",null),(0,a.Cg)([C],ag.prototype,"variationAxes",null),(0,a.Cg)([C],ag.prototype,"namedVariations",null),(0,a.Cg)([C],ag.prototype,"_variationProcessor",null);let am=new r._k({tag:new r.Qf(4),offset:new r.gm(r.S8,"void",{type:"global"}),compLength:r.S8,length:r.S8,origChecksum:r.S8}),ab=new r._k({tag:new r.Qf(4),flavor:r.S8,length:r.S8,numTables:r.oe,reserved:new r.Hb(r.oe),totalSfntSize:r.S8,majorVersion:r.oe,minorVersion:r.oe,metaOffset:r.S8,metaLength:r.S8,metaOrigLength:r.S8,privOffset:r.S8,privLength:r.S8,tables:new r.O3(am,"numTables")});ab.process=function(){let e={};for(let t of this.tables)e[t.tag]=t;this.tables=e};class ay extends ag{static probe(e){return"wOFF"===rg.decode(e.slice(0,4))}_decodeDirectory(){this.directory=ab.decode(this.stream,{_startOffset:0})}_getTableStream(e){let t=this.directory.tables[e];if(t){if(this.stream.pos=t.offset,!(t.compLength<t.length))return this.stream;{this.stream.pos+=2;let e=new Uint8Array(t.length),s=h(this.stream.readBuffer(t.compLength-2),e);return new r.HI(s)}}return null}constructor(...e){super(...e),(0,n._)(this,"type","WOFF")}}class aw extends as{_decode(){return this._font._transformedGlyphs[this.id]}_getCBox(){return this.path.bbox}constructor(...e){super(...e),(0,n._)(this,"type","WOFF2")}}let av={decode(e){let t=0,s=[0,1,2,3,4];for(let r=0;r<s.length;r++){s[r];let n=e.readUInt8();if(0xe0000000&t)throw Error("Overflow");if(t=t<<7|127&n,(128&n)==0)return t}throw Error("Bad base 128 number")}},a_=["cmap","head","hhea","hmtx","maxp","name","OS/2","post","cvt ","fpgm","glyf","loca","prep","CFF ","VORG","EBDT","EBLC","gasp","hdmx","kern","LTSH","PCLT","VDMX","vhea","vmtx","BASE","GDEF","GPOS","GSUB","EBSC","JSTF","MATH","CBDT","CBLC","COLR","CPAL","SVG ","sbix","acnt","avar","bdat","bloc","bsln","cvar","fdsc","feat","fmtx","fvar","gvar","hsty","just","lcar","mort","morx","opbd","prop","trak","Zapf","Silf","Glat","Gloc","Feat","Sill"],aC=new r._k({flags:r.hV,customTag:new r.Xx(new r.Qf(4),e=>(63&e.flags)==63),tag:e=>e.customTag||a_[63&e.flags],length:av,transformVersion:e=>e.flags>>>6&3,transformed:e=>"glyf"===e.tag||"loca"===e.tag?0===e.transformVersion:0!==e.transformVersion,transformLength:new r.Xx(av,e=>e.transformed)}),ax=new r._k({tag:new r.Qf(4),flavor:r.S8,length:r.S8,numTables:r.oe,reserved:new r.Hb(r.oe),totalSfntSize:r.S8,totalCompressedSize:r.S8,majorVersion:r.oe,minorVersion:r.oe,metaOffset:r.S8,metaLength:r.S8,metaOrigLength:r.S8,privOffset:r.S8,privLength:r.S8,tables:new r.O3(aC,"numTables")});ax.process=function(){let e={};for(let t=0;t<this.tables.length;t++){let s=this.tables[t];e[s.tag]=s}return this.tables=e};class aS extends ag{static probe(e){return"wOF2"===rg.decode(e.slice(0,4))}_decodeDirectory(){this.directory=ax.decode(this.stream),this._dataPos=this.stream.pos}_decompress(){if(!this._decompressed){this.stream.pos=this._dataPos;let e=this.stream.readBuffer(this.directory.totalCompressedSize),t=0;for(let e in this.directory.tables){let s=this.directory.tables[e];s.offset=t,t+=null!=s.transformLength?s.transformLength:s.length}let s=f(e,t);if(!s)throw Error("Error decoding compressed data in WOFF2");this.stream=new r.HI(s),this._decompressed=!0}}_decodeTable(e){return this._decompress(),super._decodeTable(e)}_getBaseGlyph(e,t=[]){if(!this._glyphs[e])if(this.directory.tables.glyf&&this.directory.tables.glyf.transformed)return this._transformedGlyphs||this._transformGlyfTable(),this._glyphs[e]=new aw(e,t,this);else return super._getBaseGlyph(e,t)}_transformGlyfTable(){this._decompress(),this.stream.pos=this.directory.tables.glyf.offset;let e=aI.decode(this.stream),t=[];for(let s=0;s<e.numGlyphs;s++){let s={},r=e.nContours.readInt16BE();if(s.numberOfContours=r,r>0){let t=[],n=0;for(let s=0;s<r;s++)t.push(n+=aP(e.nPoints));s.points=function(e,t,s){let r,n=r=0,a=[];for(let i=0;i<s;i++){let s=0,i=0,u=e.readUInt8(),c=!(u>>7);if((u&=127)<10)s=0,i=aA(u,((14&u)<<7)+t.readUInt8());else if(u<20)s=aA(u,((u-10&14)<<7)+t.readUInt8()),i=0;else if(u<84){var l=u-20,o=t.readUInt8();s=aA(u,1+(48&l)+(o>>4)),i=aA(u>>1,1+((12&l)<<2)+(15&o))}else if(u<120){var l=u-84;s=aA(u,1+(l/12<<8)+t.readUInt8()),i=aA(u>>1,1+(l%12>>2<<8)+t.readUInt8())}else if(u<124){var o=t.readUInt8();let e=t.readUInt8();s=aA(u,(o<<4)+(e>>4)),i=aA(u>>1,((15&e)<<8)+t.readUInt8())}else s=aA(u,t.readUInt16BE()),i=aA(u>>1,t.readUInt16BE());n+=s,r+=i,a.push(new ae(c,!1,n,r))}return a}(e.flags,e.glyphs,n);for(let e=0;e<r;e++)s.points[t[e]-1].endContour=!0;aP(e.glyphs)}else r<0&&as.prototype._decodeComposite.call({_font:this},s,e.composites)&&aP(e.glyphs);t.push(s)}this._transformedGlyphs=t}constructor(...e){super(...e),(0,n._)(this,"type","WOFF2")}}class ak{decode(e,t){return new r.HI(this._buf.decode(e,t))}constructor(e){this.length=e,this._buf=new r.hp(e)}}let aI=new r._k({version:r.S8,numGlyphs:r.oe,indexFormat:r.oe,nContourStreamSize:r.S8,nPointsStreamSize:r.S8,flagStreamSize:r.S8,glyphStreamSize:r.S8,compositeStreamSize:r.S8,bboxStreamSize:r.S8,instructionStreamSize:r.S8,nContours:new ak("nContourStreamSize"),nPoints:new ak("nPointsStreamSize"),flags:new ak("flagStreamSize"),glyphs:new ak("glyphStreamSize"),composites:new ak("compositeStreamSize"),bboxes:new ak("bboxStreamSize"),instructions:new ak("instructionStreamSize")});function aP(e){let t=e.readUInt8();return 253===t?e.readUInt16BE():255===t?e.readUInt8()+253:254===t?e.readUInt8()+506:t}function aA(e,t){return 1&e?t:-t}let aO=new r.Dc(r.S8,{65536:{numFonts:r.S8,offsets:new r.O3(r.S8,"numFonts")},131072:{numFonts:r.S8,offsets:new r.O3(r.S8,"numFonts"),dsigTag:r.S8,dsigLength:r.S8,dsigOffset:r.S8}});class aD{static probe(e){return"ttcf"===rg.decode(e.slice(0,4))}getFont(e){for(let t of this.header.offsets){let s=new r.HI(this.stream.buffer);s.pos=t;let n=new ag(s);if(n.postscriptName===e||n.postscriptName instanceof Uint8Array&&e instanceof Uint8Array&&n.postscriptName.every((t,s)=>e[s]===t))return n}return null}get fonts(){let e=[];for(let t of this.header.offsets){let s=new r.HI(this.stream.buffer);s.pos=t,e.push(new ag(s))}return e}constructor(e){if((0,n._)(this,"type","TTC"),this.stream=e,"ttcf"!==e.readString(4))throw Error("Not a TrueType collection");this.header=aO.decode(e)}}let aT=new r.Qf(r.hV);new r._k({len:r.S8,buf:new r.hp("len")});let aF=new r._k({id:r.oe,nameOffset:r.l8,attr:r.hV,dataOffset:r.hv,handle:r.S8}),aM=new r._k({name:new r.Qf(4),maxTypeIndex:r.oe,refList:new r.gm(r.oe,new r.O3(aF,e=>e.maxTypeIndex+1),{type:"parent"})}),aL=new r._k({length:r.oe,types:new r.O3(aM,e=>e.length+1)}),aG=new r._k({reserved:new r.Hb(r.hV,24),typeList:new r.gm(r.oe,aL),nameListOffset:new r.gm(r.oe,"void")}),aE=new r._k({dataOffset:r.S8,map:new r.gm(r.S8,aG),dataLength:r.S8,mapLength:r.S8});class aV{static probe(e){let t=new r.HI(e);try{var s=aE.decode(t)}catch(e){return!1}for(let e of s.map.typeList.types)if("sfnt"===e.name)return!0;return!1}getFont(e){if(!this.sfnt)return null;for(let t of this.sfnt.refList){let s=this.header.dataOffset+t.dataOffset+4,n=new ag(new r.HI(this.stream.buffer.slice(s)));if(n.postscriptName===e||n.postscriptName instanceof Uint8Array&&e instanceof Uint8Array&&n.postscriptName.every((t,s)=>e[s]===t))return n}return null}get fonts(){let e=[];for(let t of this.sfnt.refList){let s=this.header.dataOffset+t.dataOffset+4,n=new r.HI(this.stream.buffer.slice(s));e.push(new ag(n))}return e}constructor(e){for(let t of((0,n._)(this,"type","DFont"),this.stream=e,this.header=aE.decode(this.stream),this.header.map.typeList.types)){for(let e of t.refList)e.nameOffset>=0?(this.stream.pos=e.nameOffset+this.header.map.nameListOffset,e.name=aT.decode(this.stream)):e.name=null;"sfnt"===t.name&&(this.sfnt=t)}}}y(ag),y(ay),y(aS),y(aD),y(aV)}}]);