{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/error-boundaries/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ErrorInfo, ReactNode } from 'react';\r\n\r\nimport { <PERSON><PERSON><PERSON>riangle, RefreshCw } from 'lucide-react';\r\nimport React, { Component } from 'react';\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Button } from '@/components/ui/button';\r\n\r\ninterface Props {\r\n  children: ReactNode;\r\n  description?: string;\r\n  fallback?: ReactNode;\r\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\r\n  resetLabel?: string;\r\n  title?: string;\r\n}\r\n\r\ninterface State {\r\n  error: Error | null;\r\n  errorInfo: ErrorInfo | null;\r\n  hasError: boolean;\r\n}\r\n\r\n/**\r\n * Generic Error Boundary component\r\n * Catches errors in its child component tree and displays a fallback UI\r\n */\r\nclass ErrorBoundary extends Component<Props, State> {\r\n  constructor(props: Props) {\r\n    super(props);\r\n    this.state = {\r\n      error: null,\r\n      errorInfo: null,\r\n      hasError: false,\r\n    };\r\n  }\r\n\r\n  static getDerivedStateFromError(error: Error): Partial<State> {\r\n    // Update state so the next render will show the fallback UI\r\n    return {\r\n      error,\r\n      hasError: true,\r\n    };\r\n  }\r\n\r\n  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {\r\n    // Update state with error info for detailed reporting\r\n    this.setState({\r\n      errorInfo,\r\n    });\r\n\r\n    // Log the error\r\n    console.error('Error caught by ErrorBoundary:', error);\r\n    console.error('Component stack:', errorInfo.componentStack);\r\n\r\n    // Call onError prop if provided\r\n    if (this.props.onError) {\r\n      this.props.onError(error, errorInfo);\r\n    }\r\n\r\n    // In a production app, you would send this to a monitoring service\r\n    // Example: errorReportingService.captureError(error, errorInfo);\r\n  }\r\n\r\n  handleRetry = (): void => {\r\n    // Reset the error boundary state to trigger a re-render\r\n    this.setState({\r\n      error: null,\r\n      errorInfo: null,\r\n      hasError: false,\r\n    });\r\n  };\r\n\r\n  render(): ReactNode {\r\n    const {\r\n      description = 'An unexpected error occurred.',\r\n      resetLabel = 'Try Again',\r\n      title = 'Something went wrong',\r\n    } = this.props;\r\n\r\n    if (this.state.hasError) {\r\n      // If a custom fallback is provided, use it\r\n      if (this.props.fallback) {\r\n        return this.props.fallback;\r\n      }\r\n\r\n      // Otherwise, use the default error UI\r\n      return (\r\n        <Alert className=\"my-4\" variant=\"destructive\">\r\n          <AlertTriangle className=\"mr-2 size-4\" />\r\n          <AlertTitle className=\"text-lg font-semibold\">{title}</AlertTitle>\r\n          <AlertDescription className=\"mt-2\">\r\n            <p className=\"mb-2\">{this.state.error?.message || description}</p>\r\n            {process.env.NODE_ENV !== 'production' && this.state.errorInfo && (\r\n              <details className=\"mt-2 text-xs\">\r\n                <summary>Error details</summary>\r\n                <pre className=\"mt-2 max-h-[200px] overflow-auto whitespace-pre-wrap rounded bg-slate-100 p-2 dark:bg-slate-900\">\r\n                  {this.state.error?.stack}\r\n                  {'\\n\\nComponent Stack:\\n'}\r\n                  {this.state.errorInfo.componentStack}\r\n                </pre>\r\n              </details>\r\n            )}\r\n            <Button\r\n              className=\"mt-4\"\r\n              onClick={this.handleRetry}\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n            >\r\n              <RefreshCw className=\"mr-2 size-4\" />\r\n              {resetLabel}\r\n            </Button>\r\n          </AlertDescription>\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // If there's no error, render the children\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\nexport default ErrorBoundary;\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AACA;AAEA;AACA;AARA;;;;;;AAyBA;;;CAGC,GACD,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YACX,OAAO;YACP,WAAW;YACX,UAAU;QACZ;IACF;IAEA,OAAO,yBAAyB,KAAY,EAAkB;QAC5D,4DAA4D;QAC5D,OAAO;YACL;YACA,UAAU;QACZ;IACF;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAQ;QAC1D,sDAAsD;QACtD,IAAI,CAAC,QAAQ,CAAC;YACZ;QACF;QAEA,gBAAgB;QAChB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,QAAQ,KAAK,CAAC,oBAAoB,UAAU,cAAc;QAE1D,gCAAgC;QAChC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;IAEA,mEAAmE;IACnE,iEAAiE;IACnE;IAEA,cAAc;QACZ,wDAAwD;QACxD,IAAI,CAAC,QAAQ,CAAC;YACZ,OAAO;YACP,WAAW;YACX,UAAU;QACZ;IACF,EAAE;IAEF,SAAoB;QAClB,MAAM,EACJ,cAAc,+BAA+B,EAC7C,aAAa,WAAW,EACxB,QAAQ,sBAAsB,EAC/B,GAAG,IAAI,CAAC,KAAK;QAEd,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,2CAA2C;YAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,sCAAsC;YACtC,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBAAC,WAAU;gBAAO,SAAQ;;kCAC9B,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,8OAAC,iIAAA,CAAA,aAAU;wBAAC,WAAU;kCAAyB;;;;;;kCAC/C,8OAAC,iIAAA,CAAA,mBAAgB;wBAAC,WAAU;;0CAC1B,8OAAC;gCAAE,WAAU;0CAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW;;;;;;4BACjD,oDAAyB,gBAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,kBAC5D,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;kDAAQ;;;;;;kDACT,8OAAC;wCAAI,WAAU;;4CACZ,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;4CAClB;4CACA,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;0CAI1C,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,IAAI,CAAC,WAAW;gCACzB,MAAK;gCACL,SAAQ;;kDAER,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCACpB;;;;;;;;;;;;;;;;;;;QAKX;QAEA,2CAA2C;QAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["/**\r\n * @file Generic dashboard layout component\r\n * @module components/dashboard/DashboardLayout\r\n */\r\n\r\n'use client';\r\n\r\nimport React from 'react';\r\n\r\nimport { DashboardProps } from './types';\r\nimport ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';\r\nimport { cn } from '@/lib/utils';\r\n\r\n/**\r\n * Generic dashboard layout component that provides consistent structure\r\n * for all dashboard-style pages in the application.\r\n * \r\n * Features:\r\n * - Consistent spacing and container management\r\n * - Error boundary protection\r\n * - Responsive design foundation\r\n * - Flexible content composition\r\n * \r\n * @param props - Dashboard layout props\r\n * @returns JSX element representing the dashboard layout\r\n */\r\nexport const DashboardLayout: React.FC<DashboardProps> = ({\r\n  children,\r\n  className = '',\r\n  config,\r\n}) => {\r\n  return (\r\n    <ErrorBoundary>\r\n      <div className={cn('min-h-screen bg-background', className)}>\r\n        <main className=\"flex-1\">\r\n          <div className=\"container mx-auto space-y-6 px-4 py-6 sm:px-6 lg:px-8\">\r\n            {children}\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </ErrorBoundary>\r\n  );\r\n};\r\n\r\n/**\r\n * Dashboard page wrapper that provides the standard page structure\r\n */\r\nexport const DashboardPage: React.FC<DashboardProps> = ({\r\n  children,\r\n  className = '',\r\n  config,\r\n}) => {\r\n  return (\r\n    <DashboardLayout config={config} className={className}>\r\n      <div className=\"space-y-8\">\r\n        {children}\r\n      </div>\r\n    </DashboardLayout>\r\n  );\r\n};\r\n\r\nexport default DashboardLayout;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAOD;AACA;AAAA;AANA;;;;AAqBO,MAAM,kBAA4C,CAAC,EACxD,QAAQ,EACR,YAAY,EAAE,EACd,MAAM,EACP;IACC,qBACE,8OAAC,0JAAA,CAAA,UAAa;kBACZ,cAAA,8OAAC;YAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;sBAC/C,cAAA,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;;;;;AAMb;AAKO,MAAM,gBAA0C,CAAC,EACtD,QAAQ,EACR,YAAY,EAAE,EACd,MAAM,EACP;IACC,qBACE,8OAAC;QAAgB,QAAQ;QAAQ,WAAW;kBAC1C,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { Check, ChevronDown, ChevronUp } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ children, className, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    className={cn(\r\n      'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"size-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    className={cn(\r\n      'flex cursor-default items-center justify-center py-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"size-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    className={cn(\r\n      'flex cursor-default items-center justify-center py-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"size-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ children, className, position = 'popper', ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      className={cn(\r\n        'relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\r\n        position === 'popper' &&\r\n          'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n        className\r\n      )}\r\n      position={position}\r\n      ref={ref}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          'p-1',\r\n          position === 'popper' &&\r\n            'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]'\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    className={cn('py-1.5 pl-8 pr-2 text-sm font-semibold', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.memo(\r\n  React.forwardRef<\r\n    React.ElementRef<typeof SelectPrimitive.Item>,\r\n    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n  >(({ children, className, ...props }, forwardedRef) => {\r\n    const composedRefs = React.useCallback(\r\n      (node: any) => {\r\n        if (typeof forwardedRef === 'function') {\r\n          forwardedRef(node);\r\n        } else if (forwardedRef) {\r\n          (forwardedRef as React.MutableRefObject<any>).current = node;\r\n        }\r\n      },\r\n      [forwardedRef]\r\n    );\r\n\r\n    return (\r\n      <SelectPrimitive.Item\r\n        className={cn(\r\n          'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\r\n          className\r\n        )}\r\n        ref={composedRefs}\r\n        {...props}\r\n      >\r\n        <span className=\"absolute left-2 flex size-3.5 items-center justify-center\">\r\n          <SelectPrimitive.ItemIndicator>\r\n            <Check className=\"size-4\" />\r\n          </SelectPrimitive.ItemIndicator>\r\n        </span>\r\n\r\n        <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n      </SelectPrimitive.Item>\r\n    );\r\n  })\r\n);\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AAAA;AAAA;AACA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACV,KAAK;YACJ,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,OAAU,AAAD,gBAC1B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGb,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACpC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EACnC,CAAC;QACC,IAAI,OAAO,iBAAiB,YAAY;YACtC,aAAa;QACf,OAAO,IAAI,cAAc;YACtB,aAA6C,OAAO,GAAG;QAC1D;IACF,GACA;QAAC;KAAa;IAGhB,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAEF,KAAK;QACJ,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEF,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QAC1C,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<'textarea'>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nTextarea.displayName = 'Textarea';\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 478, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/calendar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\r\nimport * as React from 'react';\r\nimport { DayPicker } from 'react-day-picker';\r\n\r\nimport { buttonVariants } from '@/components/ui/button';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>;\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: CalendarProps) {\r\n  return (\r\n    <DayPicker\r\n      className={cn('p-3', className)}\r\n      classNames={{\r\n        caption: 'flex justify-center pt-1 relative items-center',\r\n        caption_label: 'text-sm font-medium',\r\n        cell: 'h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20',\r\n        day: cn(\r\n          buttonVariants({ variant: 'ghost' }),\r\n          'h-9 w-9 p-0 font-normal aria-selected:opacity-100'\r\n        ),\r\n        day_disabled: 'text-muted-foreground opacity-50',\r\n        day_hidden: 'invisible',\r\n        day_outside:\r\n          'day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground',\r\n        day_range_end: 'day-range-end',\r\n        day_range_middle:\r\n          'aria-selected:bg-accent aria-selected:text-accent-foreground',\r\n        day_selected:\r\n          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',\r\n        day_today: 'bg-accent text-accent-foreground',\r\n        head_cell:\r\n          'text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]',\r\n        head_row: 'flex',\r\n        month: 'space-y-4',\r\n        months: 'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',\r\n        nav: 'space-x-1 flex items-center',\r\n        nav_button: cn(\r\n          buttonVariants({ variant: 'outline' }),\r\n          'h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100'\r\n        ),\r\n        nav_button_next: 'absolute right-1',\r\n        nav_button_previous: 'absolute left-1',\r\n        row: 'flex w-full mt-2',\r\n        table: 'w-full border-collapse space-y-1',\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        IconLeft: ({ className, ...props }) => (\r\n          <ChevronLeft className={cn('h-4 w-4', className)} {...props} />\r\n        ),\r\n        IconRight: ({ className, ...props }) => (\r\n          <ChevronRight className={cn('h-4 w-4', className)} {...props} />\r\n        ),\r\n      }}\r\n      showOutsideDays={showOutsideDays}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\nCalendar.displayName = 'Calendar';\r\n\r\nexport { Calendar };\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAEA;AAEA;AACA;AAAA;AAPA;;;;;;AAWA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACW;IACd,qBACE,8OAAC,8JAAA,CAAA,YAAS;QACR,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,SAAS;YACT,eAAe;YACf,MAAM;YACN,KAAK,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,cAAc;YACd,YAAY;YACZ,aACE;YACF,eAAe;YACf,kBACE;YACF,cACE;YACF,WAAW;YACX,WACE;YACF,UAAU;YACV,OAAO;YACP,QAAQ;YACR,KAAK;YACL,YAAY,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,iBAAiB;YACjB,qBAAqB;YACrB,KAAK;YACL,OAAO;YACP,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBAChC,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW;oBAAa,GAAG,KAAK;;;;;;YAE7D,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBACjC,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW;oBAAa,GAAG,KAAK;;;;;;QAEhE;QACA,iBAAiB;QAChB,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Popover = PopoverPrimitive.Root;\r\n\r\nconst PopoverTrigger = PopoverPrimitive.Trigger;\r\n\r\nconst PopoverClose = PopoverPrimitive.Close;\r\n\r\nconst PopoverContent = React.forwardRef<\r\n  React.ElementRef<typeof PopoverPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\r\n>(({ align = 'center', className, sideOffset = 4, ...props }, ref) => (\r\n  <PopoverPrimitive.Portal>\r\n    <PopoverPrimitive.Content\r\n      align={align}\r\n      className={cn(\r\n        'z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]',\r\n        className\r\n      )}\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      {...props}\r\n    />\r\n  </PopoverPrimitive.Portal>\r\n));\r\nPopoverContent.displayName = PopoverPrimitive.Content.displayName;\r\n\r\nexport { Popover, PopoverContent, PopoverTrigger, PopoverClose };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,eAAe,mKAAA,CAAA,QAAsB;AAE3C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,QAAQ,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,OAAO;YACP,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,geACA;YAEF,KAAK;YACL,YAAY;YACX,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/loading-spinner.tsx"], "sourcesContent": ["/**\r\n * @file loading-spinner.tsx\r\n * @description Loading spinner component\r\n */\r\n\r\nimport React from 'react';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface LoadingSpinnerProps {\r\n  className?: string;\r\n  size?: 'sm' | 'md' | 'lg';\r\n}\r\n\r\n/**\r\n * LoadingSpinner Component\r\n * \r\n * Simple animated loading spinner.\r\n */\r\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\r\n  className,\r\n  size = 'md',\r\n}) => {\r\n  const sizeClasses = {\r\n    sm: 'h-4 w-4',\r\n    md: 'h-6 w-6',\r\n    lg: 'h-8 w-8',\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\r\n        sizeClasses[size],\r\n        className\r\n      )}\r\n    />\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAGD;AAAA;;;AAYO,MAAM,iBAAgD,CAAC,EAC5D,SAAS,EACT,OAAO,IAAI,EACZ;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"@/lib/utils/index\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState, formState } = useFormContext()\r\n\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nconst FormItem = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\r\n    </FormItemContext.Provider>\r\n  )\r\n})\r\nFormItem.displayName = \"FormItem\"\r\n\r\nconst FormLabel = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      ref={ref}\r\n      className={cn(error && \"text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormLabel.displayName = \"FormLabel\"\r\n\r\nconst FormControl = React.forwardRef<\r\n  React.ElementRef<typeof Slot>,\r\n  React.ComponentPropsWithoutRef<typeof Slot>\r\n>(({ ...props }, ref) => {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      ref={ref}\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormControl.displayName = \"FormControl\"\r\n\r\nconst FormDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formDescriptionId}\r\n      className={cn(\"text-sm text-muted-foreground\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormDescription.displayName = \"FormDescription\"\r\n\r\nconst FormMessage = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, children, ...props }, ref) => {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formMessageId}\r\n      className={cn(\"text-sm font-medium text-destructive\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n})\r\nFormMessage.displayName = \"FormMessage\"\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AASA;AAAA;AACA;AAfA;;;;;;;AAiBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAa,GAAG,KAAK;;;;;;;;;;;AAGrE;AACA,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,KAAK;QACL,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,SAAS,oBAAoB;QAC3C,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AACA,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,GAAG,OAAO,EAAE;IACf,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,KAAK;QACL,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM;IAEpD,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/action-button.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { Loader2 } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport type { ButtonProps } from '@/components/ui/button';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport interface ActionButtonProps extends Omit<ButtonProps, 'variant'> {\r\n  /**\r\n   * The type of action this button represents\r\n   * - primary: Main actions (Create, Save, Submit)\r\n   * - secondary: Alternative actions (View, Edit)\r\n   * - tertiary: Optional actions (Cancel, Back)\r\n   * - danger: Destructive actions (Delete, Remove)\r\n   */\r\n  actionType?: ActionType;\r\n\r\n  /**\r\n   * Icon to display before the button text\r\n   * Should be a Lucide icon with consistent sizing (h-4 w-4)\r\n   */\r\n  icon?: React.ReactNode;\r\n\r\n  /**\r\n   * Whether the button is in a loading state\r\n   */\r\n  isLoading?: boolean;\r\n\r\n  /**\r\n   * Text to display when button is loading\r\n   * If not provided, will use children\r\n   */\r\n  loadingText?: string;\r\n}\r\n\r\nexport type ActionType = 'danger' | 'primary' | 'secondary' | 'tertiary';\r\n\r\n/**\r\n * ActionButton component for consistent action styling across the application\r\n *\r\n * @example\r\n * <ActionButton actionType=\"primary\" icon={<PlusCircle />}>\r\n *   Add New\r\n * </ActionButton>\r\n */\r\nexport const ActionButton = React.forwardRef<\r\n  HTMLButtonElement,\r\n  ActionButtonProps\r\n>(\r\n  (\r\n    {\r\n      actionType = 'primary',\r\n      asChild = false,\r\n      children,\r\n      className,\r\n      disabled,\r\n      icon,\r\n      isLoading = false,\r\n      loadingText,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    // Map action types to shadcn/ui button variants and additional styling\r\n    const actionStyles: Record<\r\n      ActionType,\r\n      { className: string; variant: ButtonProps['variant']; }\r\n    > = {\r\n      danger: {\r\n        className: 'shadow-md',\r\n        variant: 'destructive',\r\n      },\r\n      primary: {\r\n        className: 'shadow-md',\r\n        variant: 'default',\r\n      },\r\n      secondary: {\r\n        className: '',\r\n        variant: 'secondary',\r\n      },\r\n      tertiary: {\r\n        className: '',\r\n        variant: 'outline',\r\n      },\r\n    };\r\n\r\n    const { className: actionClassName, variant } = actionStyles[actionType];\r\n\r\n    // const Comp = asChild ? Slot : \"button\"; // This was for an older structure, Button handles asChild now\r\n\r\n    return (\r\n      <Button\r\n        asChild={asChild} // This is passed to the underlying shadcn Button\r\n        className={cn(actionClassName, className)}\r\n        disabled={isLoading || disabled}\r\n        ref={ref}\r\n        variant={variant}\r\n        {...props}\r\n      >\r\n        {isLoading ? (\r\n          <span className=\"inline-flex items-center\">\r\n            {' '}\r\n            {/* Replaced Fragment with span */}\r\n            <Loader2 className=\"mr-2 size-4 animate-spin\" />\r\n            {loadingText || children}\r\n          </span>\r\n        ) : (\r\n          <span className=\"inline-flex items-center\">\r\n            {' '}\r\n            {/* Replaced Fragment with span */}\r\n            {icon && <span className=\"mr-2\">{icon}</span>}\r\n            {children}\r\n          </span>\r\n        )}\r\n      </Button>\r\n    );\r\n  }\r\n);\r\n\r\nActionButton.displayName = 'ActionButton';\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAIA;AACA;AAAA;AATA;;;;;;AAiDO,MAAM,6BAAe,qMAAA,CAAA,UAAK,CAAC,UAAU,CAI1C,CACE,EACE,aAAa,SAAS,EACtB,UAAU,KAAK,EACf,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,YAAY,KAAK,EACjB,WAAW,EACX,GAAG,OACJ,EACD;IAEA,uEAAuE;IACvE,MAAM,eAGF;QACF,QAAQ;YACN,WAAW;YACX,SAAS;QACX;QACA,SAAS;YACP,WAAW;YACX,SAAS;QACX;QACA,WAAW;YACT,WAAW;YACX,SAAS;QACX;QACA,UAAU;YACR,WAAW;YACX,SAAS;QACX;IACF;IAEA,MAAM,EAAE,WAAW,eAAe,EAAE,OAAO,EAAE,GAAG,YAAY,CAAC,WAAW;IAExE,yGAAyG;IAEzG,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC/B,UAAU,aAAa;QACvB,KAAK;QACL,SAAS;QACR,GAAG,KAAK;kBAER,0BACC,8OAAC;YAAK,WAAU;;gBACb;8BAED,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBAClB,eAAe;;;;;;iCAGlB,8OAAC;YAAK,WAAU;;gBACb;gBAEA,sBAAQ,8OAAC;oBAAK,WAAU;8BAAQ;;;;;;gBAChC;;;;;;;;;;;;AAKX;AAGF,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn('animate-pulse rounded-md bg-muted', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/loading.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON>cle, Loader2, LucideIcon } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport { ActionButton } from '@/components/ui/action-button';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { cn } from '@/lib/utils';\r\n\r\n// Size variants for the spinner\r\nexport type SpinnerSize = 'lg' | 'md' | 'sm' | 'xl';\r\n\r\n// Size mappings for the spinner\r\nconst spinnerSizeClasses: Record<SpinnerSize, string> = {\r\n  lg: 'h-8 w-8',\r\n  md: 'h-6 w-6',\r\n  sm: 'h-4 w-4',\r\n  xl: 'h-12 w-12',\r\n};\r\n\r\n// Text size mappings for the spinner\r\nconst spinnerTextSizeClasses: Record<SpinnerSize, string> = {\r\n  lg: 'text-base',\r\n  md: 'text-sm',\r\n  sm: 'text-xs',\r\n  xl: 'text-lg',\r\n};\r\n\r\nexport interface DataLoaderProps<T> {\r\n  /**\r\n   * Render function for the data\r\n   */\r\n  children: (data: T) => React.ReactNode;\r\n\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * The data to render\r\n   */\r\n  data: null | T | undefined;\r\n\r\n  /**\r\n   * Custom empty state component\r\n   */\r\n  emptyComponent?: React.ReactNode;\r\n\r\n  /**\r\n   * Error message, if any\r\n   */\r\n  error?: null | string;\r\n\r\n  /**\r\n   * Custom error component\r\n   */\r\n  errorComponent?: React.ReactNode;\r\n\r\n  /**\r\n   * Whether data is currently loading\r\n   */\r\n  isLoading: boolean;\r\n\r\n  /**\r\n   * Custom loading component\r\n   */\r\n  loadingComponent?: React.ReactNode;\r\n\r\n  /**\r\n   * Function to retry loading data\r\n   */\r\n  onRetry?: () => void;\r\n}\r\n\r\nexport interface EmptyStateProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Description text to display\r\n   */\r\n  description?: string;\r\n\r\n  /**\r\n   * Icon to display (Lucide icon component)\r\n   */\r\n  icon?: LucideIcon;\r\n\r\n  /**\r\n   * Primary action button\r\n   */\r\n  primaryAction?: {\r\n    label: string;\r\n    onClick?: () => void;\r\n    href?: string;\r\n    icon?: React.ReactNode;\r\n  };\r\n\r\n  /**\r\n   * Secondary action button\r\n   */\r\n  secondaryAction?: {\r\n    label: string;\r\n    onClick?: () => void;\r\n    href?: string;\r\n    icon?: React.ReactNode;\r\n  };\r\n\r\n  /**\r\n   * Title text to display\r\n   */\r\n  title: string;\r\n}\r\n\r\nexport interface ErrorDisplayProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Error message to display\r\n   */\r\n  message: string;\r\n\r\n  /**\r\n   * Function to retry the operation\r\n   */\r\n  onRetry?: () => void;\r\n}\r\n\r\nexport interface LoadingSpinnerProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Whether to display as a full-page overlay\r\n   */\r\n  fullPage?: boolean;\r\n\r\n  /**\r\n   * Size of the spinner\r\n   */\r\n  size?: SpinnerSize;\r\n\r\n  /**\r\n   * Text to display below the spinner\r\n   */\r\n  text?: string;\r\n}\r\n\r\nexport interface SkeletonLoaderProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Number of skeleton items to display\r\n   */\r\n  count?: number;\r\n\r\n  /**\r\n   * Test ID for testing\r\n   */\r\n  testId?: string;\r\n\r\n  /**\r\n   * Type of content to show a skeleton for\r\n   */\r\n  variant?: SkeletonVariant;\r\n}\r\n\r\n// Skeleton variants\r\nexport type SkeletonVariant = 'card' | 'default' | 'list' | 'stats' | 'table';\r\n\r\n/**\r\n * DataLoader component for handling loading, error, and empty states\r\n *\r\n * @example\r\n * <DataLoader\r\n *   isLoading={isLoading}\r\n *   error={error}\r\n *   data={vehicles}\r\n *   onRetry={refetch}\r\n *   loadingComponent={<SkeletonLoader variant=\"card\" count={3} />}\r\n * >\r\n *   {(vehicles) => (\r\n *     <div className=\"grid grid-cols-3 gap-4\">\r\n *       {vehicles.map(vehicle => (\r\n *         <VehicleCard key={vehicle.id} vehicle={vehicle} />\r\n *       ))}\r\n *     </div>\r\n *   )}\r\n * </DataLoader>\r\n */\r\nexport function DataLoader<T>({\r\n  children,\r\n  className,\r\n  data,\r\n  emptyComponent,\r\n  error,\r\n  errorComponent,\r\n  isLoading,\r\n  loadingComponent,\r\n  onRetry,\r\n}: DataLoaderProps<T>) {\r\n  if (isLoading) {\r\n    return (\r\n      loadingComponent || (\r\n        <LoadingSpinner {...(className && { className })} text=\"Loading...\" />\r\n      )\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      errorComponent || (\r\n        <ErrorDisplay\r\n          {...(className && { className })}\r\n          message={error}\r\n          {...(onRetry && { onRetry })}\r\n        />\r\n      )\r\n    );\r\n  }\r\n\r\n  if (!data || (Array.isArray(data) && data.length === 0)) {\r\n    return (\r\n      emptyComponent || (\r\n        <div className={cn('text-center py-8', className)}>\r\n          <p className=\"text-muted-foreground\">No data available</p>\r\n        </div>\r\n      )\r\n    );\r\n  }\r\n\r\n  return <div className={className}>{children(data)}</div>;\r\n}\r\n\r\n/**\r\n * Unified empty state component following the design pattern from delegations page\r\n *\r\n * @example\r\n * <EmptyState\r\n *   title=\"No Service Records Found\"\r\n *   description=\"There are no service records matching your current filters.\"\r\n *   icon={History}\r\n *   primaryAction={{\r\n *     label: \"Log New Service\",\r\n *     href: \"/vehicles\",\r\n *     icon: <PlusCircle className=\"size-4\" />\r\n *   }}\r\n *   secondaryAction={{\r\n *     label: \"Clear Filters\",\r\n *     onClick: clearFilters\r\n *   }}\r\n * />\r\n */\r\nexport function EmptyState({\r\n  className,\r\n  description,\r\n  icon: Icon,\r\n  primaryAction,\r\n  secondaryAction,\r\n  title,\r\n}: EmptyStateProps) {\r\n  return (\r\n    <div className={cn('space-y-6 text-center py-12', className)}>\r\n      {Icon && (\r\n        <div className=\"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted\">\r\n          <Icon className=\"h-10 w-10 text-muted-foreground\" />\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"space-y-2\">\r\n        <h3 className=\"text-2xl font-semibold text-foreground\">{title}</h3>\r\n        {description && (\r\n          <p className=\"text-muted-foreground max-w-md mx-auto\">\r\n            {description}\r\n          </p>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\r\n        {primaryAction && (\r\n          <ActionButton\r\n            actionType=\"primary\"\r\n            asChild={!!primaryAction.href}\r\n            icon={primaryAction.icon}\r\n            onClick={primaryAction.onClick}\r\n          >\r\n            {primaryAction.href ? (\r\n              <a href={primaryAction.href}>{primaryAction.label}</a>\r\n            ) : (\r\n              primaryAction.label\r\n            )}\r\n          </ActionButton>\r\n        )}\r\n\r\n        {secondaryAction && (\r\n          <ActionButton\r\n            actionType=\"tertiary\"\r\n            asChild={!!secondaryAction.href}\r\n            icon={secondaryAction.icon}\r\n            onClick={secondaryAction.onClick}\r\n          >\r\n            {secondaryAction.href ? (\r\n              <a href={secondaryAction.href}>{secondaryAction.label}</a>\r\n            ) : (\r\n              secondaryAction.label\r\n            )}\r\n          </ActionButton>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * Unified error display component\r\n *\r\n * @example\r\n * <ErrorDisplay message=\"Failed to load data\" onRetry={refetch} />\r\n */\r\nexport function ErrorDisplay({\r\n  className,\r\n  message,\r\n  onRetry,\r\n}: ErrorDisplayProps) {\r\n  return (\r\n    <Alert className={cn('my-4', className)} variant=\"destructive\">\r\n      <AlertCircle className=\"size-4\" />\r\n      <AlertTitle>Error</AlertTitle>\r\n      <AlertDescription>\r\n        <div className=\"mt-2\">\r\n          <p className=\"mb-4 text-sm text-muted-foreground\">{message}</p>\r\n          {onRetry && (\r\n            <ActionButton\r\n              actionType=\"tertiary\"\r\n              icon={<Loader2 className=\"size-4\" />}\r\n              onClick={onRetry}\r\n              size=\"sm\"\r\n            >\r\n              Try Again\r\n            </ActionButton>\r\n          )}\r\n        </div>\r\n      </AlertDescription>\r\n    </Alert>\r\n  );\r\n}\r\n\r\n/**\r\n * Unified loading spinner component\r\n *\r\n * @example\r\n * <LoadingSpinner size=\"md\" text=\"Loading data...\" />\r\n */\r\nexport function LoadingSpinner({\r\n  className,\r\n  fullPage = false,\r\n  size = 'md',\r\n  text,\r\n}: LoadingSpinnerProps) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'flex items-center justify-center',\r\n        fullPage && 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50',\r\n        className\r\n      )}\r\n    >\r\n      <div className=\"flex flex-col items-center\">\r\n        <Loader2\r\n          className={cn('animate-spin text-primary', spinnerSizeClasses[size])}\r\n        />\r\n        {text && (\r\n          <span\r\n            className={cn(\r\n              'mt-2 text-muted-foreground',\r\n              spinnerTextSizeClasses[size]\r\n            )}\r\n          >\r\n            {text}\r\n          </span>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * Unified skeleton loader component\r\n *\r\n * @example\r\n * <SkeletonLoader variant=\"card\" count={3} />\r\n */\r\nexport function SkeletonLoader({\r\n  className,\r\n  count = 1,\r\n  testId = 'loading-skeleton',\r\n  variant = 'default',\r\n}: SkeletonLoaderProps) {\r\n  // Render card skeleton (for entity cards like vehicles, employees)\r\n  if (variant === 'card') {\r\n    return (\r\n      <div\r\n        className={cn(\r\n          'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',\r\n          className\r\n        )}\r\n        data-testid={testId}\r\n      >\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div\r\n            className=\"overflow-hidden rounded-lg border bg-card shadow-md\"\r\n            key={i}\r\n          >\r\n            <Skeleton className=\"aspect-[16/10] w-full\" />\r\n            <div className=\"p-5\">\r\n              <Skeleton className=\"mb-1 h-7 w-3/4\" />\r\n              <Skeleton className=\"mb-3 h-4 w-1/2\" />\r\n              <Skeleton className=\"my-3 h-px w-full\" />\r\n              <div className=\"space-y-2.5\">\r\n                {Array.from({ length: 3 })\r\n                  .fill(0)\r\n                  .map((_, j) => (\r\n                    <div className=\"flex items-center\" key={j}>\r\n                      <Skeleton className=\"mr-2.5 size-5 rounded-full\" />\r\n                      <Skeleton className=\"h-5 w-2/3\" />\r\n                    </div>\r\n                  ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render table skeleton\r\n  if (variant === 'table') {\r\n    return (\r\n      <div className={cn('space-y-3', className)} data-testid={testId}>\r\n        <div className=\"flex gap-4\">\r\n          {Array.from({ length: 3 })\r\n            .fill(0)\r\n            .map((_, i) => (\r\n              <Skeleton className=\"h-8 flex-1\" key={i} />\r\n            ))}\r\n        </div>\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div className=\"flex gap-4\" key={i}>\r\n            {Array.from({ length: 3 })\r\n              .fill(0)\r\n              .map((_, j) => (\r\n                <Skeleton className=\"h-6 flex-1\" key={j} />\r\n              ))}\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render list skeleton\r\n  if (variant === 'list') {\r\n    return (\r\n      <div className={cn('space-y-3', className)} data-testid={testId}>\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div className=\"flex items-center gap-4\" key={i}>\r\n            <Skeleton className=\"size-12 rounded-full\" />\r\n            <div className=\"flex-1 space-y-2\">\r\n              <Skeleton className=\"h-4 w-1/3\" />\r\n              <Skeleton className=\"h-4 w-full\" />\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render stats skeleton\r\n  if (variant === 'stats') {\r\n    return (\r\n      <div\r\n        className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}\r\n        data-testid={testId}\r\n      >\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div className=\"rounded-lg border bg-card p-5 shadow-sm\" key={i}>\r\n            <div className=\"flex justify-between\">\r\n              <Skeleton className=\"h-5 w-1/3\" />\r\n              <Skeleton className=\"size-5 rounded-full\" />\r\n            </div>\r\n            <Skeleton className=\"mt-3 h-8 w-1/2\" />\r\n            <Skeleton className=\"mt-2 h-4 w-2/3\" />\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Default skeleton\r\n  return (\r\n    <div className={cn('space-y-2', className)} data-testid={testId}>\r\n      {new Array(count).fill(0).map((_, i) => (\r\n        <Skeleton className=\"h-5 w-full\" key={i} />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AARA;;;;;;;AAaA,gCAAgC;AAChC,MAAM,qBAAkD;IACtD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEA,qCAAqC;AACrC,MAAM,yBAAsD;IAC1D,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AA+KO,SAAS,WAAc,EAC5B,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,cAAc,EACd,KAAK,EACL,cAAc,EACd,SAAS,EACT,gBAAgB,EAChB,OAAO,EACY;IACnB,IAAI,WAAW;QACb,OACE,kCACE,8OAAC;YAAgB,GAAI,aAAa;gBAAE;YAAU,CAAC;YAAG,MAAK;;;;;;IAG7D;IAEA,IAAI,OAAO;QACT,OACE,gCACE,8OAAC;YACE,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC/B,SAAS;YACR,GAAI,WAAW;gBAAE;YAAQ,CAAC;;;;;;IAInC;IAEA,IAAI,CAAC,QAAS,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,KAAK,GAAI;QACvD,OACE,gCACE,8OAAC;YAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;sBACrC,cAAA,8OAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;IAI7C;IAEA,qBAAO,8OAAC;QAAI,WAAW;kBAAY,SAAS;;;;;;AAC9C;AAqBO,SAAS,WAAW,EACzB,SAAS,EACT,WAAW,EACX,MAAM,IAAI,EACV,aAAa,EACb,eAAe,EACf,KAAK,EACW;IAChB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;;YAC/C,sBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;;;;;;;;;;;0BAIpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;oBACvD,6BACC,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAKP,8OAAC;gBAAI,WAAU;;oBACZ,+BACC,8OAAC,4IAAA,CAAA,eAAY;wBACX,YAAW;wBACX,SAAS,CAAC,CAAC,cAAc,IAAI;wBAC7B,MAAM,cAAc,IAAI;wBACxB,SAAS,cAAc,OAAO;kCAE7B,cAAc,IAAI,iBACjB,8OAAC;4BAAE,MAAM,cAAc,IAAI;sCAAG,cAAc,KAAK;;;;;mCAEjD,cAAc,KAAK;;;;;;oBAKxB,iCACC,8OAAC,4IAAA,CAAA,eAAY;wBACX,YAAW;wBACX,SAAS,CAAC,CAAC,gBAAgB,IAAI;wBAC/B,MAAM,gBAAgB,IAAI;wBAC1B,SAAS,gBAAgB,OAAO;kCAE/B,gBAAgB,IAAI,iBACnB,8OAAC;4BAAE,MAAM,gBAAgB,IAAI;sCAAG,gBAAgB,KAAK;;;;;mCAErD,gBAAgB,KAAK;;;;;;;;;;;;;;;;;;AAOnC;AAQO,SAAS,aAAa,EAC3B,SAAS,EACT,OAAO,EACP,OAAO,EACW;IAClB,qBACE,8OAAC,iIAAA,CAAA,QAAK;QAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAY,SAAQ;;0BAC/C,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,8OAAC,iIAAA,CAAA,aAAU;0BAAC;;;;;;0BACZ,8OAAC,iIAAA,CAAA,mBAAgB;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;wBAClD,yBACC,8OAAC,4IAAA,CAAA,eAAY;4BACX,YAAW;4BACX,oBAAM,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BACzB,SAAS;4BACT,MAAK;sCACN;;;;;;;;;;;;;;;;;;;;;;;AAQb;AAQO,SAAS,eAAe,EAC7B,SAAS,EACT,WAAW,KAAK,EAChB,OAAO,IAAI,EACX,IAAI,EACgB;IACpB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,oCACA,YAAY,wDACZ;kBAGF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iNAAA,CAAA,UAAO;oBACN,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B,kBAAkB,CAAC,KAAK;;;;;;gBAEpE,sBACC,8OAAC;oBACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,8BACA,sBAAsB,CAAC,KAAK;8BAG7B;;;;;;;;;;;;;;;;;AAMb;AAQO,SAAS,eAAe,EAC7B,SAAS,EACT,QAAQ,CAAC,EACT,SAAS,kBAAkB,EAC3B,UAAU,SAAS,EACC;IACpB,mEAAmE;IACnE,IAAI,YAAY,QAAQ;QACtB,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wDACA;YAEF,eAAa;sBAEZ,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC;oBACC,WAAU;;sCAGV,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAE,GACrB,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;2CAFkB;;;;;;;;;;;;;;;;;mBAX3C;;;;;;;;;;IAsBf;IAEA,wBAAwB;IACxB,IAAI,YAAY,SAAS;QACvB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAY,eAAa;;8BACvD,8OAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GACrB,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;2BAAkB;;;;;;;;;;gBAG3C,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GACrB,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;+BAAkB;;;;;uBAJX;;;;;;;;;;;IAUzC;IAEA,uBAAuB;IACvB,IAAI,YAAY,QAAQ;QACtB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAY,eAAa;sBACtD,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;mBAJsB;;;;;;;;;;IAUtD;IAEA,wBAAwB;IACxB,IAAI,YAAY,SAAS;QACvB,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;YAC1D,eAAa;sBAEZ,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;mBANwC;;;;;;;;;;IAWtE;IAEA,mBAAmB;IACnB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAAY,eAAa;kBACtD,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC,oIAAA,CAAA,WAAQ;gBAAC,WAAU;eAAkB;;;;;;;;;;AAI9C", "debugId": null}}, {"offset": {"line": 1455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/data-loader.tsx"], "sourcesContent": ["/**\r\n * @file Data Loader Component Export\r\n * @description Re-exports DataLoader from loading.tsx for backward compatibility\r\n */\r\n\r\nexport { DataLoader } from './loading';\r\nexport type { DataLoaderProps } from './loading';\r\n"], "names": [], "mappings": "AAAA;;;CAGC;AAED", "debugId": null}}, {"offset": {"line": 1476, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/error-display.tsx"], "sourcesContent": ["/**\r\n * @file Error Display Component\r\n * @description Standardized error display component following SOLID principles\r\n */\r\n\r\nimport React from 'react';\r\nimport { AlertTriangle, RefreshCw } from 'lucide-react';\r\nimport { Alert, AlertDescription, AlertTitle } from './alert';\r\nimport { Button } from './button';\r\n\r\nexport interface ErrorDisplayProps {\r\n  /**\r\n   * Error message to display\r\n   */\r\n  error?: string | Error | null;\r\n  \r\n  /**\r\n   * Custom title for the error\r\n   */\r\n  title?: string;\r\n  \r\n  /**\r\n   * Retry function to call when retry button is clicked\r\n   */\r\n  onRetry?: () => void;\r\n  \r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n  \r\n  /**\r\n   * Whether to show the retry button\r\n   */\r\n  showRetry?: boolean;\r\n  \r\n  /**\r\n   * Custom retry button text\r\n   */\r\n  retryText?: string;\r\n}\r\n\r\n/**\r\n * ErrorDisplay component for consistent error handling across the application\r\n * \r\n * @example\r\n * <ErrorDisplay\r\n *   error=\"Failed to load data\"\r\n *   onRetry={refetch}\r\n *   title=\"Loading Error\"\r\n * />\r\n */\r\nexport function ErrorDisplay({\r\n  error,\r\n  title = 'Error',\r\n  onRetry,\r\n  className,\r\n  showRetry = true,\r\n  retryText = 'Try Again',\r\n}: ErrorDisplayProps) {\r\n  if (!error) {\r\n    return null;\r\n  }\r\n\r\n  const errorMessage = error instanceof Error ? error.message : String(error);\r\n\r\n  return (\r\n    <Alert variant=\"destructive\" className={className}>\r\n      <AlertTriangle className=\"h-4 w-4\" />\r\n      <AlertTitle>{title}</AlertTitle>\r\n      <AlertDescription className=\"mt-2\">\r\n        <p className=\"mb-3\">{errorMessage}</p>\r\n        {showRetry && onRetry && (\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={onRetry}\r\n            className=\"h-8\"\r\n          >\r\n            <RefreshCw className=\"h-3 w-3 mr-2\" />\r\n            {retryText}\r\n          </Button>\r\n        )}\r\n      </AlertDescription>\r\n    </Alert>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAGD;AAAA;AACA;AACA;;;;;AA4CO,SAAS,aAAa,EAC3B,KAAK,EACL,QAAQ,OAAO,EACf,OAAO,EACP,SAAS,EACT,YAAY,IAAI,EAChB,YAAY,WAAW,EACL;IAClB,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IAEA,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;IAErE,qBACE,8OAAC,iIAAA,CAAA,QAAK;QAAC,SAAQ;QAAc,WAAW;;0BACtC,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,8OAAC,iIAAA,CAAA,aAAU;0BAAE;;;;;;0BACb,8OAAC,iIAAA,CAAA,mBAAgB;gBAAC,WAAU;;kCAC1B,8OAAC;wBAAE,WAAU;kCAAQ;;;;;;oBACpB,aAAa,yBACZ,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      className={cn('w-full caption-bottom text-sm', className)}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  </div>\r\n));\r\nTable.displayName = 'Table';\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead className={cn('[&_tr]:border-b', className)} ref={ref} {...props} />\r\n));\r\nTableHeader.displayName = 'TableHeader';\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    className={cn('[&_tr:last-child]:border-0', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableBody.displayName = 'TableBody';\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    className={cn(\r\n      'border-t bg-muted/50 font-medium [&>tr]:last:border-b-0',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableFooter.displayName = 'TableFooter';\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    className={cn(\r\n      'border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableRow.displayName = 'TableRow';\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    className={cn(\r\n      'h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableHead.displayName = 'TableHead';\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    className={cn('p-4 align-middle [&:has([role=checkbox])]:pr-0', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableCell.displayName = 'TableCell';\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    className={cn('mt-4 text-sm text-muted-foreground', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableCaption.displayName = 'TableCaption';\r\n\r\nexport {\r\n  Table,\r\n  TableBody,\r\n  TableCaption,\r\n  TableCell,\r\n  TableFooter,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;AAAA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC/C,KAAK;YACJ,GAAG,KAAK;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAM,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAY,KAAK;QAAM,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC5C,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAChE,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1675, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/progress.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as ProgressPrimitive from '@radix-ui/react-progress';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Progress = React.forwardRef<\r\n  React.ElementRef<typeof ProgressPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\r\n>(({ className, value, ...props }, ref) => (\r\n  <ProgressPrimitive.Root\r\n    className={cn(\r\n      'relative h-4 w-full overflow-hidden rounded-full bg-secondary',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ProgressPrimitive.Indicator\r\n      className=\"size-full flex-1 bg-primary transition-all\"\r\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n    />\r\n  </ProgressPrimitive.Root>\r\n));\r\nProgress.displayName = ProgressPrimitive.Root.displayName;\r\n\r\nexport { Progress };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}]}