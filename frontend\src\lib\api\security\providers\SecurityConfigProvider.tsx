/**
 * @file Security Configuration Provider - Centralized security configuration management
 * @module api/security/providers/SecurityConfigProvider
 *
 * Phase 3: React Integration Layer
 * Provides centralized security configuration management across the application
 * using React Context with type-safe defaults and validation.
 */

'use client';

import React, {
  createContext,
  useContext,
  useMemo,
  type ReactNode,
} from 'react';
import type { SecurityConfig } from '../../core/interfaces';
import { getEnvironmentConfig } from '../../../config/environment';
import { SECURITY_CONSTANTS } from '../../../security';

/**
 * Security Configuration Context Interface
 */
export interface SecurityConfigContextValue {
  config: SecurityConfig;
  updateConfig: (newConfig: Partial<SecurityConfig>) => void;
  resetConfig: () => void;
  isConfigValid: boolean;
  configVersion: string;
}

/**
 * Security Configuration Provider Props
 */
export interface SecurityConfigProviderProps {
  children: ReactNode;
  initialConfig?: Partial<SecurityConfig>;
  configVersion?: string;
  onConfigChange?: (config: SecurityConfig) => void;
  validateConfig?: boolean;
}

/**
 * Default Security Configuration using SECURITY_CONSTANTS and environment-aware configuration
 */
const createDefaultSecurityConfig = (): SecurityConfig => {
  const envConfig = getEnvironmentConfig();

  return {
    csrf: {
      enabled: true,
      tokenHeader: 'X-CSRF-Token',
      excludePaths: ['/api/health', '/api/status'],
    },
    tokenValidation: {
      enabled: true,
      refreshThreshold: SECURITY_CONSTANTS.TOKEN_EXPIRY_THRESHOLD_MINUTES * 60, // Convert to seconds
      autoRefresh: true,
    },
    inputSanitization: {
      enabled: true,
      sanitizers: ['xss', 'sql'],
    },
    authentication: {
      enabled: true,
      autoLogout: true,
      redirectOnFailure: true,
    },
    http: {
      baseURL: envConfig.apiBaseUrl, // Use environment-aware configuration
      timeout: 10000,
      retryAttempts: 3,
    },
  };
};

const DEFAULT_SECURITY_CONFIG: SecurityConfig = createDefaultSecurityConfig();

/**
 * Security Configuration Context
 */
const SecurityConfigContext = createContext<SecurityConfigContextValue | null>(
  null
);

/**
 * Security Configuration Provider Component
 *
 * Provides centralized security configuration management with:
 * - Type-safe configuration defaults
 * - Configuration validation
 * - Real-time configuration updates
 * - Integration with SECURITY_CONSTANTS
 */
export function SecurityConfigProvider({
  children,
  initialConfig = {},
  configVersion = '1.0.0',
  onConfigChange,
  validateConfig = true,
}: SecurityConfigProviderProps) {
  // Merge initial config with defaults
  const config = useMemo<SecurityConfig>(() => {
    const mergedConfig = {
      ...DEFAULT_SECURITY_CONFIG,
      ...initialConfig,
      // Deep merge nested objects
      csrf: {
        ...DEFAULT_SECURITY_CONFIG.csrf,
        ...initialConfig.csrf,
      },
      tokenValidation: {
        ...DEFAULT_SECURITY_CONFIG.tokenValidation,
        ...initialConfig.tokenValidation,
      },
      inputSanitization: {
        ...DEFAULT_SECURITY_CONFIG.inputSanitization,
        ...initialConfig.inputSanitization,
      },
      authentication: {
        ...DEFAULT_SECURITY_CONFIG.authentication,
        ...initialConfig.authentication,
      },
      http: {
        ...DEFAULT_SECURITY_CONFIG.http,
        ...initialConfig.http,
      },
    };

    return mergedConfig;
  }, [initialConfig]);

  // Validate configuration
  const isConfigValid = useMemo(() => {
    if (!validateConfig) return true;

    try {
      // Validate required fields
      if (!config.http.baseURL) return false;
      if (config.http.timeout <= 0) return false;
      if (config.http.retryAttempts < 0) return false;
      if (config.tokenValidation.refreshThreshold <= 0) return false;

      // Validate CSRF configuration
      if (config.csrf.enabled && !config.csrf.tokenHeader) return false;

      // Validate sanitizers
      if (
        config.inputSanitization.enabled &&
        config.inputSanitization.sanitizers.length === 0
      ) {
        return false;
      }

      return true;
    } catch (error) {
      console.error(
        'SecurityConfigProvider: Configuration validation failed:',
        error
      );
      return false;
    }
  }, [config, validateConfig]);

  // Update configuration function
  const updateConfig = useMemo(() => {
    return (newConfig: Partial<SecurityConfig>) => {
      const updatedConfig = {
        ...config,
        ...newConfig,
        // Deep merge nested objects
        csrf: {
          ...config.csrf,
          ...newConfig.csrf,
        },
        tokenValidation: {
          ...config.tokenValidation,
          ...newConfig.tokenValidation,
        },
        inputSanitization: {
          ...config.inputSanitization,
          ...newConfig.inputSanitization,
        },
        authentication: {
          ...config.authentication,
          ...newConfig.authentication,
        },
        http: {
          ...config.http,
          ...newConfig.http,
        },
      };

      onConfigChange?.(updatedConfig);
    };
  }, [config, onConfigChange]);

  // Reset configuration function
  const resetConfig = useMemo(() => {
    return () => {
      onConfigChange?.(DEFAULT_SECURITY_CONFIG);
    };
  }, [onConfigChange]);

  // Context value
  const contextValue = useMemo<SecurityConfigContextValue>(
    () => ({
      config,
      updateConfig,
      resetConfig,
      isConfigValid,
      configVersion,
    }),
    [config, updateConfig, resetConfig, isConfigValid, configVersion]
  );

  // Log configuration status
  React.useEffect(() => {
    console.log('🔧 SecurityConfigProvider: Configuration initialized', {
      isValid: isConfigValid,
      version: configVersion,
      constants: SECURITY_CONSTANTS,
      config: {
        csrf: config.csrf.enabled,
        tokenValidation: config.tokenValidation.enabled,
        inputSanitization: config.inputSanitization.enabled,
        authentication: config.authentication.enabled,
      },
    });
  }, [config, isConfigValid, configVersion]);

  return (
    <SecurityConfigContext.Provider value={contextValue}>
      {children}
    </SecurityConfigContext.Provider>
  );
}

/**
 * Hook to use Security Configuration
 */
export function useSecurityConfig(): SecurityConfigContextValue {
  const context = useContext(SecurityConfigContext);

  if (!context) {
    throw new Error(
      'useSecurityConfig must be used within a SecurityConfigProvider'
    );
  }

  return context;
}

/**
 * Hook to get current security configuration (read-only)
 */
export function useSecurityConfigValue(): SecurityConfig {
  const { config } = useSecurityConfig();
  return config;
}

/**
 * Export default configuration for testing and utilities
 */
export { DEFAULT_SECURITY_CONFIG };
