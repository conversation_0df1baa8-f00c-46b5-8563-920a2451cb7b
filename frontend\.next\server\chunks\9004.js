"use strict";exports.id=9004,exports.ids=[9004],exports.modules={15795:(e,s,t)=>{function a(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function i(e){if(e.fullName?.trim())return e.fullName.trim();if(e.name?.trim()){let s=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(s.toLowerCase())||s.includes("_")){let e=s.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${e} (Role)`}return s}if(e.role){let s=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${s} (Role)`}return"Unknown Employee"}function l(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function r(e){return e.replaceAll("_"," ")}t.d(s,{DV:()=>i,fZ:()=>a,s:()=>l,vq:()=>r})},23562:(e,s,t)=>{t.d(s,{k:()=>n});var a=t(60687),i=t(25177),l=t(43210),r=t(22482);let n=l.forwardRef(({className:e,value:s,...t},l)=>(0,a.jsx)(i.bL,{className:(0,r.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),ref:l,...t,children:(0,a.jsx)(i.C1,{className:"size-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));n.displayName=i.bL.displayName},34729:(e,s,t)=>{t.d(s,{T:()=>r});var a=t(60687),i=t(43210),l=t(22482);let r=i.forwardRef(({className:e,...s},t)=>(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...s}));r.displayName="Textarea"},48041:(e,s,t)=>{t.d(s,{z:()=>i});var a=t(60687);function i({children:e,description:s,icon:t,title:i}){return(0,a.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[t&&(0,a.jsx)(t,{className:"size-8 text-primary"}),(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:i})]}),s&&(0,a.jsx)("p",{className:"mt-1 text-muted-foreground",children:s})]}),e&&(0,a.jsx)("div",{className:"flex items-center gap-2",children:e})]})}t(43210)},51356:(e,s,t)=>{t.d(s,{GK:()=>e$});var a=t(60687),i=t(76180),l=t.n(i),r=t(63442),n=t(26622),d=t(48206),o=t(26398),c=t(99196),m=t(3662),u=t(72963),x=t(55817),h=t(71273),g=t(16189),p=t(43210),b=t(27605),f=t(91821),j=t(96834),v=t(29523),N=t(44493),y=t(78726),w=t(14975);let D=[{dependsOn:["driverEmployeeIds"],field:"vehicleIds",validate:e=>{let s=e.vehicleIds&&e.vehicleIds.length>0,t=e.driverEmployeeIds&&e.driverEmployeeIds.length>0;return s&&!t?{isValid:!1,message:"Vehicles cannot be assigned without at least one driver",type:"error"}:{isValid:!0,type:"error"}}},{dependsOn:["durationFrom"],field:"durationTo",validate:e=>{if(e.durationFrom&&e.durationTo){let s=new Date(e.durationFrom),t=new Date(e.durationTo);if(s>t)return{isValid:!1,message:"End date cannot be earlier than start date",type:"error"};if((t.getTime()-s.getTime())/864e5>30)return{isValid:!0,message:"Delegation duration exceeds 30 days. Please verify this is correct.",type:"warning"}}return{isValid:!0,type:"error"}}},{dependsOn:["delegates"],field:"delegates",validate:e=>e.delegates&&e.delegates.length>5?{isValid:!0,message:"Large number of delegates may require additional coordination",type:"warning"}:{isValid:!0,type:"warning"}}],A=()=>{let{formState:{dirtyFields:e,errors:s,touchedFields:t},trigger:a,watch:i}=(0,b.xW)(),l=i(),r=(0,p.useMemo)(()=>{let e=Object.keys(s).length,t=D.filter(e=>!e.validate(l).isValid).length,a=D.filter(e=>{let s=e.validate(l);return s.isValid&&"warning"===s.type&&s.message}).length;return{errorCount:e+t,hasErrors:e>0||t>0,hasWarnings:a>0,isValid:0===e&&0===t,warningCount:a}},[s,l]),n=(0,p.useCallback)(a=>{let i=s[a],r=t[a]??!1,n=e[a]??!1,d=D.find(e=>e.field===a),o=null;return d&&(o=d.validate(l)),{errorMessage:i?.message??(o&&!o.isValid?o.message:"")??"",hasError:!!(i||o&&!o.isValid&&"error"===o.type),hasWarning:!!(o&&o.isValid&&"warning"===o.type&&o.message),isDirty:!!n,isRequired:["eventName","location","durationFrom","durationTo","delegates"].includes(a),isTouched:!!r,isValid:!i&&(!o||o.isValid),warningMessage:(o&&o.isValid&&"warning"===o.type?o.message:"")??""}},[s,t,e,l]),d=(0,p.useCallback)(e=>{let s={assignment:["driverEmployeeIds","escortEmployeeIds"],basic:["eventName","location","durationFrom","durationTo","status"],delegates:["delegates"],logistics:["vehicleIds","flightArrivalDetails","flightDepartureDetails"],notes:["notes","invitationFrom","invitationTo","imageUrl"]}[e]??[],t=s.filter(e=>n(e).hasError),a=s.filter(e=>n(e).hasWarning),i=s.filter(e=>{let s=l[e];return function(e,s){return"delegates"===e?Array.isArray(s)&&s.length>0&&s.every(e=>e.name&&e.title):Array.isArray(s)?s.length>0:"string"==typeof s?s.trim().length>0:null!=s}(e,s)});return{completionPercentage:s.length>0?Math.round(i.length/s.length*100):0,errorCount:t.length,hasErrors:t.length>0,hasWarnings:a.length>0,isComplete:function(e,s){switch(e){case"assignment":return!!((s.driverEmployeeIds&&s.driverEmployeeIds.length>0)??(s.escortEmployeeIds&&s.escortEmployeeIds.length>0));case"basic":return!!(s.eventName&&s.location&&s.durationFrom&&s.durationTo);case"delegates":return!!(s.delegates&&s.delegates.length>0&&s.delegates.every(e=>e.name&&e.title));case"logistics":return!!((s.vehicleIds&&s.vehicleIds.length>0)??s.flightArrivalDetails?.flightNumber??s.flightDepartureDetails?.flightNumber);case"notes":return!!(s.notes?.trim()??s.invitationFrom?.trim()??s.invitationTo?.trim()??s.imageUrl?.trim());default:return!1}}(e,l),isValid:0===t.length,warningCount:a.length}},[n,l]),o=(0,p.useCallback)(async e=>await a(e),[a]),c=(0,p.useCallback)(async()=>await a(),[a]);return{getCrossFieldErrors:(0,p.useCallback)(()=>D.filter(e=>!e.validate(l).isValid).map(e=>({field:e.field,message:e.validate(l).message??"Validation error"})),[l]),getCrossFieldWarnings:(0,p.useCallback)(()=>D.filter(e=>{let s=e.validate(l);return s.isValid&&"warning"===s.type&&s.message}).map(e=>({field:e.field,message:e.validate(l).message??"Validation warning"})),[l]),getFieldValidationState:n,getSectionValidationState:d,validateField:o,validateForm:c,validationState:r}};var I=t(22482);function E(e){return"object"==typeof e&&null!==e&&"isValid"in e&&"boolean"==typeof e.isValid&&"hasErrors"in e&&"boolean"==typeof e.hasErrors&&"hasWarnings"in e&&"boolean"==typeof e.hasWarnings}let T=e=>({delegates:"Delegates",driverEmployeeIds:"Drivers",durationFrom:"Start Date",durationTo:"End Date",escortEmployeeIds:"Escorts",eventName:"Event Name","flightArrivalDetails.flightNumber":"Arrival Flight Number","flightDepartureDetails.flightNumber":"Departure Flight Number",imageUrl:"Image URL",invitationFrom:"Invitation From",invitationTo:"Invitation To",location:"Location",notes:"Notes",vehicleIds:"Vehicles"})[e]??e.replaceAll(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()),C=({className:e="",onDismiss:s,show:t,showFieldNavigation:i=!0,showSuccess:l=!1,showWarnings:r=!0})=>{let{formState:{errors:n},setFocus:d}=(0,b.xW)(),{getCrossFieldErrors:o,getCrossFieldWarnings:c,validationState:x}=A();if(!t)return null;let h=o(),g=r?c():[],p=Object.entries(n),j=E(x)&&x.hasErrors||p.length>0,D=E(x)&&x.hasWarnings&&r,C=E(x)&&x.isValid&&0===p.length,F=e=>{d(e),s&&s()};return C&&l?(0,a.jsxs)(N.Zp,{className:(0,I.cn)("border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20",e),children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2 text-green-700 dark:text-green-400",children:[(0,a.jsx)(m.A,{className:"size-5"}),"Form Validation Successful",s&&(0,a.jsxs)(v.$,{className:"ml-auto size-6 p-0 text-green-700 hover:text-green-800 dark:text-green-400",onClick:s,size:"sm",variant:"ghost",children:[(0,a.jsx)(y.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Dismiss"})]})]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)("p",{className:"text-sm text-green-600 dark:text-green-300",children:"All required fields are completed and valid. You can now submit the delegation form."})})]}):j||D?(0,a.jsxs)(f.Fc,{className:(0,I.cn)(j?"border-destructive/50 text-destructive dark:border-destructive":"border-amber-200 bg-amber-50 text-amber-800 dark:border-amber-800 dark:bg-amber-900/20 dark:text-amber-200",e),variant:j?"destructive":"default",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[j?(0,a.jsx)(u.A,{className:"size-4"}):(0,a.jsx)(w.A,{className:"size-4"}),(0,a.jsx)("div",{className:"font-medium",children:j?"Please correct the following issues:":"Please review the following:"})]}),s&&(0,a.jsxs)(v.$,{className:"size-6 p-0 opacity-70 hover:opacity-100",onClick:s,size:"sm",variant:"ghost",children:[(0,a.jsx)(y.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Dismiss"})]})]}),(0,a.jsxs)(f.TN,{className:"mt-3",children:[p.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:"Field Errors:"}),(0,a.jsx)("ul",{className:"list-inside list-disc space-y-1 text-sm",children:p.map(([e,s])=>(0,a.jsxs)("li",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{children:[(0,a.jsxs)("strong",{children:[T(e),":"]})," ",s.message??"Invalid value"]}),i&&(0,a.jsx)(v.$,{className:"ml-2 h-6 px-2 text-xs",onClick:()=>F(e),size:"sm",variant:"ghost",children:"Go to field"})]},e))})]}),h.length>0&&(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:"Validation Errors:"}),(0,a.jsx)("ul",{className:"list-inside list-disc space-y-1 text-sm",children:h.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{children:[(0,a.jsxs)("strong",{children:[T(e.field),":"]})," ",e.message]}),i&&(0,a.jsx)(v.$,{className:"ml-2 h-6 px-2 text-xs",onClick:()=>F(e.field),size:"sm",variant:"ghost",children:"Go to field"})]},s))})]}),g.length>0&&(0,a.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,a.jsx)("div",{className:"text-sm font-medium",children:"Warnings:"}),(0,a.jsx)("ul",{className:"list-inside list-disc space-y-1 text-sm",children:g.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{children:[(0,a.jsxs)("strong",{children:[T(e.field),":"]})," ",e.message]}),i&&(0,a.jsx)(v.$,{className:"ml-2 h-6 px-2 text-xs",onClick:()=>F(e.field),size:"sm",variant:"ghost",children:"Go to field"})]},s))})]})]})]}):null},F=({show:e,onDismiss:s,showWarnings:t=!0,showSuccess:i=!1,showFieldNavigation:l=!0})=>{let{getCrossFieldErrors:r,getCrossFieldWarnings:n}=A();return(0,a.jsx)(C,{show:e,onDismiss:s,showWarnings:t,showSuccess:i,showFieldNavigation:l})};var S=t(23562),k=t(54827),$=t(9275),R=t(65594);let z=$.k5(["Planned","Confirmed","In_Progress","Completed","Cancelled","No_details"]);$.Ik({changedAt:$.Yj().datetime({message:"Invalid date/time format for status change"}),id:$.Yj().uuid(),reason:$.Yj().optional(),status:z});let V=$.Ik({id:$.Yj().optional(),name:$.Yj().min(1,"Delegate name is required"),notes:$.Yj().optional(),title:$.Yj().min(1,"Delegate title is required")}),q=$.vk(e=>null!=e&&("object"!=typeof e||e.flightNumber&&""!==e.flightNumber.trim()||e.dateTime&&""!==e.dateTime.trim()||e.airport&&""!==e.airport.trim())?e:null,$.Ik({airport:$.vk(e=>e??"",$.Yj().min(1,"Airport is required")),dateTime:$.vk(e=>e??"",$.Yj().min(1,"Date & Time is required").refine(e=>!isNaN(Date.parse(e)),{message:"Invalid date & time format"})),flightNumber:$.vk(e=>e??"",$.Yj().min(1,"Flight number is required")),notes:$.Yj().trim().nullable().optional().transform(e=>""===e?null:e),terminal:$.Yj().trim().nullable().optional().transform(e=>""===e?null:e)}).nullable().optional()),P=$.Ik({delegates:$.YO(V).min(1,"At least one delegate is required"),driverEmployeeIds:$.YO($.ai().int().positive("Driver Employee ID must be a positive integer.")).optional().default([]),durationFrom:$.Yj().refine(e=>(0,R.Qr)(e),{message:"Invalid start date format"}),durationTo:$.Yj().refine(e=>(0,R.Qr)(e),{message:"Invalid end date format"}),escortEmployeeIds:$.YO($.ai().int().positive("Escort Employee ID must be a positive integer.")).optional().default([]).refine(e=>e.length<=10,{message:"Maximum 10 escort employees allowed"}),eventName:$.Yj().min(1,"Event name is required"),flightArrivalDetails:q.nullable().optional(),flightDepartureDetails:q.nullable().optional(),imageUrl:$.Yj().url("Invalid URL for image").optional().or($.eu("")),invitationFrom:$.Yj().optional(),invitationTo:$.Yj().optional(),location:$.Yj().min(1,"Location is required"),notes:$.Yj().optional(),status:z.default("Planned"),statusChangeReason:$.Yj().optional(),vehicleIds:$.YO($.ai().int().positive("Vehicle ID must be a positive integer.")).optional().default([])}).superRefine((e,s)=>{new Date(e.durationFrom)>new Date(e.durationTo)&&s.addIssue({code:$.eq.custom,message:"End date cannot be earlier than start date",path:["durationTo"]}),e.vehicleIds&&e.vehicleIds.length>0&&(!e.driverEmployeeIds||0===e.driverEmployeeIds.length)&&s.addIssue({code:$.eq.custom,message:"Vehicles cannot be assigned without at least one driver",path:["vehicleIds"]})});var L=t(28399),O=t(15036),M=t(92929);let U=({name:e,label:s,type:t="text",placeholder:i,disabled:l=!1,className:r="",showValidationIcon:n=!0,showValidationMessage:d=!0,showRequiredIndicator:o=!0,options:c,icon:x,...h})=>{let{getFieldValidationState:g}=A(),{formState:{errors:p}}=(0,b.xW)(),f=g(e);p[e];let j=o&&f.isRequired?`${s} *`:s;return(0,a.jsxs)("div",{className:(0,I.cn)("space-y-1",r),children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(M.z,{name:e,label:j,type:t,placeholder:i||"",disabled:l,options:c||[],...x&&{icon:x},className:(0,I.cn)("transition-all duration-200",f.hasError?"border-destructive focus:border-destructive focus:ring-destructive/20":f.hasWarning?"border-amber-500 focus:border-amber-500 focus:ring-amber-500/20":f.isValid&&f.isTouched&&f.isDirty?"border-green-500 focus:border-green-500 focus:ring-green-500/20":""),"aria-invalid":f.hasError,"aria-describedby":f.hasError||f.hasWarning?`${e}-validation-message`:void 0,...h}),n&&(0,a.jsx)("div",{className:"absolute right-3 top-9 flex items-center",children:n?f.hasError?(0,a.jsx)(u.A,{className:"h-4 w-4 text-destructive","aria-label":"Error"}):f.hasWarning?(0,a.jsx)(w.A,{className:"h-4 w-4 text-amber-500","aria-label":"Warning"}):f.isValid&&f.isTouched&&f.isDirty?(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-500","aria-label":"Valid"}):null:null})]}),d&&(0,a.jsx)("div",{id:`${e}-validation-message`,role:"alert","aria-live":"polite",children:d?f.hasError&&f.errorMessage?(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm text-destructive mt-1",children:[(0,a.jsx)(u.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:f.errorMessage})]}):f.hasWarning&&f.warningMessage?(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm text-amber-600 mt-1",children:[(0,a.jsx)(w.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:f.warningMessage})]}):null:null})]})};var W=t(15079),_=t(15795);let Y=e=>{switch(e){case"Completed":return"default";case"In_Progress":return"secondary";case"Cancelled":return"destructive";default:return"outline"}},G=(e,s)=>{if(!e||!s)return 0;let t=new Date(e);return Math.ceil(Math.abs(new Date(s).getTime()-t.getTime())/864e5)},Q=({isSubmitting:e=!1,className:s="",userRole:t="user"})=>{let{control:i,watch:l,formState:{errors:r}}=(0,b.xW)(),{getSectionValidationState:d}=A(),u=l(),x=G(u.durationFrom,u.durationTo),h=u.durationFrom&&u.durationTo,g=d("basic").isComplete;return(0,a.jsxs)("section",{className:`space-y-6 rounded-lg border bg-card p-6 ${s}`,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-lg bg-accent/10",children:(0,a.jsx)(n.A,{className:"h-5 w-5 text-accent"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-foreground",children:"Event Details"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Information about the delegation event"})]})]}),g&&(0,a.jsxs)(j.E,{variant:"default",className:"flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),"Complete"]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground mb-3",children:[(0,a.jsx)(L.A,{className:"h-4 w-4 text-accent"}),"Event Information"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsx)(U,{name:"eventName",label:"Event Name",disabled:e,placeholder:"e.g., Board Meeting, Conference, Official Visit"}),(0,a.jsx)(U,{name:"location",label:"Location",disabled:e,placeholder:"e.g., Rabat, Morocco"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground mb-3",children:[(0,a.jsx)(O.A,{className:"h-4 w-4 text-accent"}),"Duration & Timing"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsx)(U,{name:"durationFrom",label:"Start Date",type:"date",disabled:e}),(0,a.jsx)(U,{name:"durationTo",label:"End Date",type:"date",disabled:e})]}),h&&x>0&&(0,a.jsxs)(f.Fc,{children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)(f.TN,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{children:"Duration:"}),(0,a.jsxs)(j.E,{variant:"secondary",children:[x," ",1===x?"day":"days"]}),x>7&&(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"(Extended delegation - consider additional planning)"})]})})]}),r.durationTo&&(0,a.jsx)(f.Fc,{variant:"destructive",children:(0,a.jsx)(f.TN,{children:r.durationTo.message})})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground mb-3",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-accent"}),"Additional Details"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsx)(U,{name:"invitationFrom",label:"Invitation From",disabled:e,placeholder:"Optional: When invitations should be sent",showRequiredIndicator:!1}),(0,a.jsx)(U,{name:"invitationTo",label:"Invitation To",disabled:e,placeholder:"Optional: RSVP deadline",showRequiredIndicator:!1})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-foreground mb-3",children:[(0,a.jsx)("div",{className:`h-4 w-4 rounded-full ${"default"===Y(u.status||"Planned")?"bg-green-500":"secondary"===Y(u.status||"Planned")?"bg-blue-500":"destructive"===Y(u.status||"Planned")?"bg-red-500":"bg-gray-400"}`}),"Status Management"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsx)(M.z,{label:"Current Status",name:"status",render:({field:s})=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(W.l6,{value:s.value||"",onValueChange:s.onChange,disabled:e,children:[(0,a.jsx)(W.bq,{children:(0,a.jsx)(W.yv,{placeholder:"Select delegation status"})}),(0,a.jsx)(W.gC,{children:z.options.map(e=>(0,a.jsx)(W.eb,{value:e,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:`h-2 w-2 rounded-full ${"default"===Y(e)?"bg-green-500":"secondary"===Y(e)?"bg-blue-500":"destructive"===Y(e)?"bg-red-500":"bg-gray-400"}`}),(0,_.fZ)(e)]})},e))})]}),s.value&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Planned"===s.value&&"Initial planning phase - details can be modified","Confirmed"===s.value&&"Event confirmed - notify all stakeholders","In_Progress"===s.value&&"Event is currently ongoing","Completed"===s.value&&"Event has been completed successfully","Cancelled"===s.value&&"Event has been cancelled - notify all parties","No_details"===s.value&&"Requires additional information"]})]})}),(0,a.jsx)(U,{name:"imageUrl",label:"Supporting Image/Document URL",disabled:e,placeholder:"https://example.com/image.jpg",showRequiredIndicator:!1})]})]}),"user"===t&&!g&&(0,a.jsxs)(f.Fc,{children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)(f.TN,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"font-medium",children:"Getting Started"}),(0,a.jsxs)("ul",{className:"text-sm space-y-1",children:[(0,a.jsx)("li",{children:"• Fill in the event name and location first"}),(0,a.jsx)("li",{children:"• Set accurate start and end times for proper planning"}),(0,a.jsx)("li",{children:"• Choose the appropriate status based on planning phase"}),(0,a.jsx)("li",{children:"• Optional fields can be completed later if needed"})]})]})})]})]})};var Z=t(58595),B=t(57207),K=t(35265);let J=e=>!!(e?.name?.trim()&&e?.title?.trim()),H=e=>e?.name?.trim()||e?.title?.trim()?e?.name?.trim()&&e?.title?.trim()?"complete":"incomplete":"empty",X=({isSubmitting:e=!1,className:s="",minDelegates:t=1,maxDelegates:i=10,userRole:l="user"})=>{let{control:r,watch:n,formState:{errors:o}}=(0,b.xW)(),{getSectionValidationState:x}=A(),{fields:h,append:g,remove:p}=(0,b.jz)({control:r,name:"delegates"}),y=n("delegates"),w=y?.filter(e=>J(e))||[],D=w.length>=t,I=e=>{h.length>t&&p(e)},E=h.length<i,T=h.length>t,C=Math.round(w.length/Math.max(h.length,1)*100);return(0,a.jsxs)("section",{className:`space-y-6 rounded-lg border bg-card p-6 ${s}`,children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-lg bg-accent/10",children:(0,a.jsx)(d.A,{className:"h-5 w-5 text-accent"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-foreground",children:"Delegates"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"People receiving delegated responsibilities and authority"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(j.E,{variant:D?"default":"secondary",children:[w.length," of ",h.length," complete"]}),D&&(0,a.jsxs)(j.E,{variant:"default",className:"flex items-center gap-1",children:[(0,a.jsx)(m.A,{className:"h-3 w-3"}),"Valid"]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Section Progress"}),(0,a.jsxs)("span",{className:"font-medium",children:[C,"%"]})]}),(0,a.jsxs)(f.Fc,{children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)(f.TN,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"font-medium",children:"Delegate Guidelines"}),(0,a.jsxs)("ul",{className:"text-sm space-y-1",children:[(0,a.jsxs)("li",{children:["• At least ",t," delegate",t>1?"s are":" is"," required"]}),(0,a.jsx)("li",{children:"• Include full name and official title for each delegate"}),(0,a.jsx)("li",{children:"• Add notes for special responsibilities or requirements"}),(0,a.jsxs)("li",{children:["• Maximum ",i," delegates allowed per delegation"]})]})]})})]})]}),(0,a.jsx)("div",{className:"space-y-4",children:h.map((s,t)=>{let i=H(y?.[t]),l=o.delegates?.[t];return(0,a.jsxs)(N.Zp,{className:`transition-colors ${"complete"===i?"border-green-200 bg-background":"incomplete"===i?"border-amber-200 bg-background":"border-border bg-background"}`,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:`flex items-center justify-center w-8 h-8 rounded-lg ${"complete"===i?"bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400":"incomplete"===i?"bg-amber-100 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400":"bg-muted text-muted-foreground"}`,children:(0,a.jsx)(Z.A,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"font-medium text-sm",children:["Delegate ",t+1,"complete"===i&&y?.[t]?.name&&` - ${y[t].name}`]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["complete"===i&&"Complete","incomplete"===i&&"Needs attention","empty"===i&&"Not started"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["complete"===i&&(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-600"}),"incomplete"===i&&(0,a.jsx)(u.A,{className:"h-4 w-4 text-amber-600"}),T&&(0,a.jsx)(v.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>I(t),disabled:e,className:"h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10",children:(0,a.jsx)(B.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4 pt-0",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(M.z,{label:"Full Name",name:`delegates.${t}.name`,disabled:e,placeholder:"e.g., Dr. Sarah Johnson",description:"Complete legal name"}),y?.[t]?.name&&y[t].name.length>0&&(0,a.jsx)(m.A,{className:"absolute top-8 right-3 h-4 w-4 text-green-500"}),o.delegates?.[t]?.name&&(0,a.jsx)("div",{className:"absolute top-8 right-3",children:(0,a.jsx)(u.A,{className:"h-4 w-4 text-red-500"})})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(M.z,{label:"Official Title",name:`delegates.${t}.title`,disabled:e,placeholder:"e.g., Director of Operations",description:"Current position or role"}),y?.[t]?.title&&y[t].title.length>0&&(0,a.jsx)(m.A,{className:"absolute top-8 right-3 h-4 w-4 text-green-500"}),o.delegates?.[t]?.title&&(0,a.jsx)("div",{className:"absolute top-8 right-3",children:(0,a.jsx)(u.A,{className:"h-4 w-4 text-red-500"})})]})]}),(0,a.jsx)(M.z,{label:"Special Notes or Requirements",name:`delegates.${t}.notes`,disabled:e,placeholder:"Optional: Special responsibilities, dietary requirements, accessibility needs...",description:"Any additional information relevant to this delegate",type:"textarea",rows:2}),l&&(0,a.jsxs)(f.Fc,{variant:"destructive",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)(f.TN,{children:"Please complete the required fields for this delegate."})]})]})]},s.id)})}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t",children:[(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[0===h.length&&"No delegates added yet",1===h.length&&"1 delegate",h.length>1&&`${h.length} delegates`,h.length>0&&` • ${w.length} completed`]}),(0,a.jsxs)(v.$,{type:"button",variant:"outline",onClick:()=>{h.length<i&&g({name:"",notes:"",title:""})},disabled:!E||e,className:"flex items-center gap-2",children:[(0,a.jsx)(K.A,{className:"h-4 w-4"}),"Add Delegate",!E&&` (Max ${i})`]})]}),!D&&h.length>0&&(0,a.jsxs)(f.Fc,{variant:"destructive",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsxs)(f.TN,{children:["At least ",t," completed delegate",t>1?"s are":" is"," required. Please complete the name and title for all delegates."]})]}),D&&w.length===h.length&&(0,a.jsxs)(f.Fc,{children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)(f.TN,{className:"text-green-700",children:"All delegates are properly configured. You can proceed to the next section or add more delegates if needed."})]})]})};var ee=t(24920),es=t(71669),et=t(19599);let ea=({isSubmitting:e=!1,className:s=""})=>{let{watch:t,setValue:i,getValues:l}=(0,b.xW)(),r=t("driverEmployeeIds")||[],{data:n=[],isLoading:d,error:o}=(0,et.sZ)("driver"),c=n.filter(e=>!r.includes(e.id)),m=n.filter(e=>r.includes(e.id)),u=e=>{r.includes(e)||i("driverEmployeeIds",[...r,e])},x=e=>{i("driverEmployeeIds",r.filter(s=>s!==e))};return(0,a.jsxs)("section",{className:`space-y-4 rounded-lg border bg-card p-6 ${s}`,children:[(0,a.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,a.jsx)(ee.A,{className:"mr-2 size-5 text-accent"}),"Drivers"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-foreground",children:"Select Drivers"}),(0,a.jsxs)("select",{className:"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",onChange:e=>{let s=parseInt(e.target.value,10);s&&(u(s),e.target.value="")},disabled:e||d,children:[(0,a.jsx)("option",{value:"",children:d?"Loading drivers...":"Select a driver to add"}),c.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.fullName||e.name,e.employeeId&&` (${e.employeeId})`,e.status&&` - ${e.status}`,e.availability&&` - ${e.availability.replace("_"," ")}`,e.currentLocation&&` @ ${e.currentLocation}`]},e.id))]})]}),m.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"text-sm font-medium text-foreground",children:["Selected Drivers (",m.length,")"]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:m.map(s=>(0,a.jsxs)(j.E,{variant:"secondary",className:"inline-flex items-center gap-1 px-3 py-1",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:s.fullName||s.name}),(s.status||s.availability)&&(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[s.status,s.status&&s.availability&&" • ",s.availability?.replace("_"," ")]})]}),!e&&(0,a.jsx)("button",{type:"button",onClick:()=>x(s.id),className:"ml-1 text-muted-foreground hover:text-destructive","aria-label":`Remove ${s.fullName||s.name}`,children:(0,a.jsx)(B.A,{className:"size-3"})})]},s.id))})]}),0===m.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No drivers selected. Select drivers from the dropdown above."}),!d&&0===n.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No drivers available. Please add drivers to the system first."}),(0,a.jsx)(es.C5,{})]})};var ei=t(53597);let el=({isSubmitting:e=!1,className:s=""})=>{let{watch:t,setValue:i}=(0,b.xW)(),l=t("escortEmployeeIds")||[],{data:r=[],isLoading:n,error:d}=(0,et.sZ)(),o=r.filter(e=>"driver"!==e.role),c=o.filter(e=>!l.includes(e.id)),m=o.filter(e=>l.includes(e.id)),u=e=>{l.includes(e)||i("escortEmployeeIds",[...l,e])},x=e=>{i("escortEmployeeIds",l.filter(s=>s!==e))};return(0,a.jsxs)("section",{className:`space-y-4 rounded-lg border bg-card p-6 ${s}`,children:[(0,a.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,a.jsx)(ei.A,{className:"mr-2 size-5 text-accent"}),"Escorts"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium text-foreground",children:"Select Escorts"}),(0,a.jsxs)("select",{className:"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",onChange:e=>{let s=parseInt(e.target.value,10);s&&(u(s),e.target.value="")},disabled:e||n,children:[(0,a.jsx)("option",{value:"",children:n?"Loading escorts...":"Select an escort to add"}),c.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.fullName||e.name,e.employeeId&&` (${e.employeeId})`,e.status&&` - ${e.status}`,e.role&&` (${e.role})`]},e.id))]})]}),m.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"text-sm font-medium text-foreground",children:["Selected Escorts (",m.length,")"]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:m.map(s=>(0,a.jsxs)(j.E,{variant:"secondary",className:"inline-flex items-center gap-1 px-3 py-1",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"font-medium",children:s.fullName||s.name}),(s.status||s.role)&&(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[s.role,s.role&&s.status&&" • ",s.status]})]}),!e&&(0,a.jsx)("button",{type:"button",onClick:()=>x(s.id),className:"ml-1 text-muted-foreground hover:text-destructive","aria-label":`Remove ${s.fullName||s.name}`,children:(0,a.jsx)(B.A,{className:"size-3"})})]},s.id))})]}),0===m.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No escorts selected. Select escorts from the dropdown above."}),!n&&0===o.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No escorts available. Please add escorts to the system first."}),(0,a.jsx)(es.C5,{})]})};var er=t(71977),en=t(72273);let ed=({isSubmitting:e=!1,className:s=""})=>{let{watch:t,setValue:i}=(0,b.xW)(),l=t("vehicleIds")||[],r=t("driverEmployeeIds")||[],{data:n=[],isLoading:d,error:o}=(0,en.T$)(),c=n.filter(e=>!l.includes(e.id)),m=n.filter(e=>l.includes(e.id)),u=e=>{l.includes(e)||i("vehicleIds",[...l,e])},x=e=>{i("vehicleIds",l.filter(s=>s!==e))},h=e=>{let s=[];return e.make&&s.push(e.make),e.model&&s.push(e.model),e.year&&s.push(`(${e.year})`),e.licensePlate&&s.push(`- ${e.licensePlate}`),s.length>0?s.join(" "):`Vehicle ${e.id}`};return(0,a.jsxs)("section",{className:`space-y-4 rounded-lg border bg-card p-6 ${s}`,children:[(0,a.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,a.jsx)(er.A,{className:"mr-2 size-5 text-accent"}),"Vehicles"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"text-sm font-medium text-foreground",children:["Select Vehicles"," ",0===r.length&&"(Requires Driver)"]}),0===r.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Please select at least one driver before adding vehicles."}),(0,a.jsxs)("select",{className:"w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",onChange:e=>{let s=parseInt(e.target.value,10);s&&(u(s),e.target.value="")},disabled:e||d||0===r.length,children:[(0,a.jsx)("option",{value:"",children:d?"Loading vehicles...":0===r.length?"Select drivers first":"Select a vehicle to add"}),r.length>0&&c.map(e=>(0,a.jsx)("option",{value:e.id,children:h(e)},e.id))]})]}),m.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"text-sm font-medium text-foreground",children:["Selected Vehicles (",m.length,")"]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:m.map(s=>(0,a.jsxs)(j.E,{variant:"secondary",className:"inline-flex items-center gap-1 px-3 py-1",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("span",{className:"font-medium",children:[s.make," ",s.model]}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[s.year," • ",s.licensePlate||"No License"]})]}),!e&&(0,a.jsx)("button",{type:"button",onClick:()=>x(s.id),className:"ml-1 text-muted-foreground hover:text-destructive","aria-label":`Remove ${h(s)}`,children:(0,a.jsx)(B.A,{className:"size-3"})})]},s.id))})]}),0===m.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No vehicles selected. Select vehicles from the dropdown above."}),!d&&0===n.length&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No vehicles available. Please add vehicles to the system first."}),(0,a.jsx)(es.C5,{})]})},eo=({isSubmitting:e=!1,className:s="",employees:t=[],vehicles:i=[],userRole:l="user"})=>(0,a.jsxs)("section",{className:`space-y-6 ${s}`,children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"size-6 text-accent"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-foreground",children:"Assignment Details"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[(0,a.jsx)(ea,{isSubmitting:e}),(0,a.jsx)(el,{isSubmitting:e}),(0,a.jsx)(ed,{isSubmitting:e})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-muted/50 p-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-foreground mb-2",children:"Assignment Guidelines"}),(0,a.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,a.jsx)("li",{children:"• At least one escort is typically required for security"}),(0,a.jsx)("li",{children:"• Drivers are required before vehicles can be assigned"}),(0,a.jsx)("li",{children:"• Vehicle assignments should match the number of drivers"}),(0,a.jsx)("li",{children:"• Consider delegation size when selecting personnel"})]})]})]});var ec=t(93242),em=t(41936),eu=t(48409),ex=t(68752),eh=t(75699),eg=t(11516),ep=t(52856),eb=t(26373),ef=t(63503),ej=t(89667),ev=t(40988),eN=t(42692),ey=t(3389);t(43845),t(16467);let ew=async(e,s)=>{throw Error("Please use FlightApiService instance instead of direct function call")};function eD({isOpen:e,onClose:s,onSelectFlight:t,type:i}){let[l,r]=(0,p.useState)(""),[d,o]=(0,p.useState)(new Date),[c,m]=(0,p.useState)(!1),[u,x]=(0,p.useState)([]),[h,g]=(0,p.useState)(null),{toast:b}=(0,ey.dj)(),f=(0,p.useCallback)(function(e,s){let t=null,a=function(...s){let a=this;t&&clearTimeout(t),t=setTimeout(()=>{t=null,e.apply(a,s)},500)};return a.cancel=function(){t&&(clearTimeout(t),t=null)},a}(async(e,s)=>{if(e.length<2)return void x([]);if(!s){g("Please select a date to search."),b({description:"Please select a date before searching for flights.",title:"Date Required",variant:"destructive"}),x([]);return}m(!0),g(null);let t=(0,eh.GP)(s,"yyyy-MM-dd");try{let s=await ew(e,t);x(s),0===s.length&&b({description:`No flights found matching "${e}" on ${t}.`,title:"No Flights Found",variant:"default"})}catch(e){g(e.message||"Failed to search flights"),b({description:`Failed to search flights: ${e.message}. Please try again.`,title:"Error Searching Flights",variant:"destructive"})}finally{m(!1)}},500),[b]),j=e=>{t(e),s()},v=e=>e?new Date(1e3*e).toLocaleString():"Unknown";return(0,a.jsx)(ef.lG,{onOpenChange:e=>!e&&s(),open:e,children:(0,a.jsxs)(ef.Cf,{className:"flex max-h-[80vh] flex-col sm:max-w-[600px]",children:[(0,a.jsxs)(ef.c7,{children:[(0,a.jsxs)(ef.L3,{className:"flex items-center",children:["arrival"===i?(0,a.jsx)(ec.A,{className:"mr-2 size-5 text-accent"}):(0,a.jsx)(eu.A,{className:"mr-2 size-5 text-accent"}),"Search ","arrival"===i?"Arrival":"Departure"," Flights"]}),(0,a.jsx)(ef.rr,{children:"Enter a flight callsign (e.g., BA123) and select a date to search for flights."})]}),(0,a.jsxs)("div",{className:"mb-4 grid grid-cols-1 gap-4 md:grid-cols-2",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(em.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(ej.p,{className:"pl-10",onChange:e=>r(e.target.value),placeholder:"Callsign (e.g., BA123)",value:l})]}),(0,a.jsx)("div",{children:(0,a.jsxs)(ev.AM,{children:[(0,a.jsx)(ev.Wv,{asChild:!0,children:(0,a.jsx)(ex.r,{actionType:"tertiary",className:(0,I.cn)("w-full justify-start text-left font-normal",!d&&"text-muted-foreground"),icon:(0,a.jsx)(n.A,{className:"size-4"}),children:d?(0,eh.GP)(d,"PPP"):(0,a.jsx)("span",{children:"Pick a date"})})}),(0,a.jsx)(ev.hl,{className:"w-auto p-0",children:(0,a.jsx)(eb.V,{initialFocus:!0,mode:"single",onSelect:o,selected:d})})]})})]}),c?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)(eg.A,{className:"size-8 animate-spin text-primary"}),(0,a.jsx)("span",{className:"ml-2 text-muted-foreground",children:"Searching flights..."})]}):h?(0,a.jsxs)("div",{className:"py-8 text-center",children:[(0,a.jsxs)("div",{className:"mb-4 rounded-md bg-destructive/15 p-4 text-sm text-destructive",children:[(0,a.jsx)("h4",{className:"mb-2 font-bold",children:"Error Details:"}),(0,a.jsx)("p",{className:"mb-2",children:h}),h&&h.details&&(0,a.jsxs)("div",{className:"mt-3 border-t border-destructive/20 pt-3",children:[h.details.possibleReasons&&(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("h5",{className:"mb-1 text-xs font-semibold",children:"Possible Reasons:"}),(0,a.jsx)("ul",{className:"list-inside list-disc text-xs",children:h.details.possibleReasons.map((e,s)=>(0,a.jsx)("li",{className:"mb-1",children:e},s))})]}),h.details.apiInfo&&(0,a.jsxs)("div",{className:"mt-2 text-xs",children:[(0,a.jsx)("h5",{className:"mb-1 font-semibold",children:"API Information:"}),(0,a.jsx)("p",{children:h.details.apiInfo})]})]}),(0,a.jsxs)("p",{className:"mt-3 text-xs text-muted-foreground",children:["API URL:"," ","http://192.168.100.31:3001/api"]})]}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:h.toString().includes("future date")?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ex.r,{actionType:"tertiary",onClick:()=>{o(new Date),setTimeout(()=>f(l,new Date),100)},children:"Try with Today's Date"}),(0,a.jsx)(ex.r,{actionType:"tertiary",onClick:()=>{let e=new Date;e.setDate(e.getDate()-1),o(e),setTimeout(()=>f(l,e),100)},children:"Try with Yesterday"})]}):(0,a.jsx)(ex.r,{actionType:"tertiary",onClick:()=>f(l,d),children:"Try Again"})})]}):0===u.length?(0,a.jsx)("div",{className:"py-8 text-center text-muted-foreground",children:l.length>0?(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"mb-2 font-medium text-amber-500",children:['No flights found matching "',l,'" on'," ",d?(0,eh.GP)(d,"PPP"):"selected date"]}),(0,a.jsxs)("div",{className:"mb-4 rounded-md bg-muted p-4 text-sm",children:[(0,a.jsx)("h4",{className:"mb-2 font-semibold",children:"Suggestions:"}),(0,a.jsxs)("ul",{className:"list-inside list-disc text-sm text-muted-foreground",children:[(0,a.jsx)("li",{className:"mb-1",children:'Check if the callsign format is correct (e.g., "RYR441J" for Ryanair flight 441J)'}),(0,a.jsx)("li",{className:"mb-1",children:"Try searching for a different date - OpenSky may not have data for all dates"}),(0,a.jsx)("li",{className:"mb-1",children:"OpenSky Network API primarily provides historical data, not future schedules"}),(0,a.jsx)("li",{className:"mb-1",children:"Some flights may not be tracked by OpenSky Network"})]})]}),(0,a.jsxs)("div",{className:"inline-block rounded-md bg-muted p-2 text-xs text-muted-foreground",children:[(0,a.jsxs)("p",{children:["API URL:"," ","http://192.168.100.31:3001/api","/flights/search"]}),(0,a.jsxs)("p",{children:["Search Term: ",l]}),(0,a.jsxs)("p",{children:["Date:"," ",d?(0,eh.GP)(d,"yyyy-MM-dd"):"Not selected"]})]})]}):"Enter a flight callsign to search."}):(0,a.jsx)(eN.F,{className:"max-h-[400px] flex-1 pr-4",children:(0,a.jsx)("div",{className:"space-y-2",children:u.map(e=>(0,a.jsxs)("div",{className:"cursor-pointer rounded-md border p-3 transition-colors hover:bg-accent hover:text-accent-foreground",onClick:()=>j(e),children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold",children:e.callsign}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.departureAirport||"Unknown"," →"," ",e.arrivalAirport||"Unknown"]})]}),(0,a.jsxs)("div",{className:"text-right text-sm",children:[(0,a.jsxs)("p",{className:"flex items-center",children:[(0,a.jsx)(ep.A,{className:"mr-1 inline-block size-3 text-muted-foreground"}),e.icao24]}),void 0!==e.onGround&&(0,a.jsx)("p",{className:e.onGround?"text-amber-500":"text-green-500",children:e.onGround?"On Ground":"In Air"})]})]}),(e.departureTime||e.arrivalTime)&&(0,a.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:[e.departureTime&&(0,a.jsxs)("p",{children:["Departure: ",v(e.departureTime)]}),e.arrivalTime&&(0,a.jsxs)("p",{children:["Arrival: ",v(e.arrivalTime)]})]})]},`${e.icao24}-${e.callsign}`))})}),(0,a.jsx)(ef.Es,{className:"mt-4",children:(0,a.jsx)(ex.r,{actionType:"tertiary",onClick:s,children:"Cancel"})})]})})}let eA=({isSubmitting:e=!1,className:s="",userRole:t="user"})=>{let{setValue:i}=(0,b.xW)(),[l,r]=(0,p.useState)(!1),[n,d]=(0,p.useState)(!1),o=(e,s)=>{let t="arrival"===s?"flightArrivalDetails":"flightDepartureDetails";"arrival"===s&&e.arrivalTime&&e.arrivalAirport?(i(`${t}.flightNumber`,e.callsign),i(`${t}.dateTime`,new Date(e.arrivalTime).toISOString().slice(0,16)),i(`${t}.airport`,e.arrivalAirport)):"departure"===s&&e.departureTime&&e.departureAirport&&(i(`${t}.flightNumber`,e.callsign),i(`${t}.dateTime`,new Date(e.departureTime).toISOString().slice(0,16)),i(`${t}.airport`,e.departureAirport)),"arrival"===s?r(!1):d(!1)};return(0,a.jsxs)("div",{className:`grid grid-cols-1 gap-6 lg:grid-cols-2 ${s}`,children:[(0,a.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,a.jsx)(ec.A,{className:"mr-2 size-5 text-accent"}),"Arrival Flight (Optional)"]}),(0,a.jsx)(ex.r,{actionType:"tertiary",icon:(0,a.jsx)(em.A,{className:"size-4"}),onClick:()=>r(!0),size:"sm",type:"button",disabled:e,children:"Search Flights"})]}),(0,a.jsx)(M.z,{label:"Flight Number",name:"flightArrivalDetails.flightNumber",disabled:e}),(0,a.jsx)(M.z,{label:"Date & Time",name:"flightArrivalDetails.dateTime",type:"datetime-local",disabled:e}),(0,a.jsx)(M.z,{label:"Airport",name:"flightArrivalDetails.airport",disabled:e}),(0,a.jsx)(M.z,{label:"Terminal (Optional)",name:"flightArrivalDetails.terminal",disabled:e}),(0,a.jsx)(M.z,{label:"Notes (Optional)",name:"flightArrivalDetails.notes",type:"textarea",disabled:e})]}),(0,a.jsxs)("section",{className:"space-y-4 rounded-lg border bg-card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,a.jsx)(eu.A,{className:"mr-2 size-5 text-accent"}),"Departure Flight (Optional)"]}),(0,a.jsx)(ex.r,{actionType:"tertiary",icon:(0,a.jsx)(em.A,{className:"size-4"}),onClick:()=>d(!0),size:"sm",type:"button",disabled:e,children:"Search Flights"})]}),(0,a.jsx)(M.z,{label:"Flight Number",name:"flightDepartureDetails.flightNumber",disabled:e}),(0,a.jsx)(M.z,{label:"Date & Time",name:"flightDepartureDetails.dateTime",type:"datetime-local",disabled:e}),(0,a.jsx)(M.z,{label:"Airport",name:"flightDepartureDetails.airport",disabled:e}),(0,a.jsx)(M.z,{label:"Terminal (Optional)",name:"flightDepartureDetails.terminal",disabled:e}),(0,a.jsx)(M.z,{label:"Notes (Optional)",name:"flightDepartureDetails.notes",type:"textarea",disabled:e})]}),(0,a.jsx)(eD,{isOpen:l,onClose:()=>r(!1),onSelectFlight:e=>o(e,"arrival"),type:"arrival"}),(0,a.jsx)(eD,{isOpen:n,onClose:()=>d(!1),onSelectFlight:e=>o(e,"departure"),type:"departure"})]})},eI=({isSubmitting:e=!1,className:s="",maxLength:t=2e3,userRole:i="user"})=>{let{watch:l}=(0,b.xW)(),r=(l("notes")||"").length,n=r>.8*t,d=r>t;return(0,a.jsxs)("section",{className:`space-y-4 rounded-lg border bg-card p-6 ${s}`,children:[(0,a.jsxs)("h3",{className:"flex items-center text-lg font-semibold text-foreground",children:[(0,a.jsx)(c.A,{className:"mr-2 size-5 text-accent"}),"General Notes (Optional)"]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(M.z,{label:"Additional Notes for the Delegation",name:"notes",type:"textarea",disabled:e,placeholder:"Enter any additional information, special requirements, or notes about this delegation..."}),(0,a.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Additional context can help with delegation planning and execution"}),(0,a.jsxs)("span",{className:`font-medium ${d?"text-destructive":n?"text-warning":"text-muted-foreground"}`,children:[r,"/",t]})]}),n&&!d&&(0,a.jsx)("p",{className:"text-sm text-warning",children:"Approaching character limit. Consider being more concise."}),d&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:"Character limit exceeded. Please shorten your notes."})]})]})};class eE{validateFlightDetails(e){let s=[];if(!e)return{isValid:!0,errors:[]};let t=e.flightNumber&&""!==e.flightNumber.trim(),a=e.dateTime&&""!==e.dateTime.trim(),i=e.airport&&""!==e.airport.trim();if((t||a||i)&&(t||s.push({field:"flightNumber",message:"Flight number is required when flight details are provided",code:"REQUIRED_FIELD"}),a||s.push({field:"dateTime",message:"Date and time are required when flight details are provided",code:"REQUIRED_FIELD"}),i||s.push({field:"airport",message:"Airport is required when flight details are provided",code:"REQUIRED_FIELD"}),a))try{let t=new Date(e.dateTime);isNaN(t.getTime())&&s.push({field:"dateTime",message:"Invalid date/time format",code:"INVALID_FORMAT"})}catch(e){s.push({field:"dateTime",message:"Invalid date/time format",code:"INVALID_FORMAT"})}return{isValid:0===s.length,errors:s}}validateFormData(e){let s=[],t=[];e.eventName?.trim()||s.push({field:"eventName",message:"Event name is required",code:"REQUIRED_FIELD"}),e.location?.trim()||s.push({field:"location",message:"Location is required",code:"REQUIRED_FIELD"}),this.validateDateRange(e.durationFrom,e.durationTo)||s.push({field:"durationTo",message:"End date cannot be earlier than start date",code:"INVALID_DATE_RANGE"});let a=this.validateDelegates(e.delegates||[]);if(a.isValid||s.push(...a.errors),e.vehicleIds&&e.vehicleIds.length>0&&(!e.driverEmployeeIds||0===e.driverEmployeeIds.length)&&s.push({field:"vehicleIds",message:"Vehicles can only be assigned when at least one driver is assigned",code:"VEHICLES_REQUIRE_DRIVERS"}),e.flightArrivalDetails){let t=this.validateFlightDetails(e.flightArrivalDetails);t.isValid||s.push(...t.errors.map(e=>({...e,field:`flightArrivalDetails.${e.field}`})))}if(e.flightDepartureDetails){let t=this.validateFlightDetails(e.flightDepartureDetails);t.isValid||s.push(...t.errors.map(e=>({...e,field:`flightDepartureDetails.${e.field}`})))}return e.delegates&&e.delegates.length>5&&t.push({field:"delegates",message:"Large number of delegates may require additional coordination",code:"MANY_DELEGATES"}),{isValid:0===s.length,errors:s,warnings:t}}validateSection(e,s){return{isValid:!0,errors:[]}}validateDelegateData(e){return{isValid:!0,errors:[]}}transformForSubmission(e){return{...e,calculatedDuration:this.calculateDuration(e.durationFrom,e.durationTo),processedFlightDetails:{arrival:e.flightArrivalDetails?this.processFlightDetailsForSubmission(e.flightArrivalDetails):void 0,departure:e.flightDepartureDetails?this.processFlightDetailsForSubmission(e.flightDepartureDetails):void 0}}}processFlightDetailsForSubmission(e){return{flightNumber:e.flightNumber?.trim()||"",dateTime:e.dateTime||"",airport:e.airport?.trim()||"",terminal:e.terminal?.trim()||void 0,notes:e.notes?.trim()||void 0,formattedDateTime:e.dateTime?new Date(e.dateTime).toISOString():void 0,airportCode:this.extractAirportCode(e.airport)}}extractAirportCode(e){if(!e)return;let s=e.match(/\(([A-Z]{3})\)|([A-Z]{3})$/);return s?s[1]||s[2]:void 0}transformInitialData(e){return{}}calculateDuration(e,s){try{let t=new Date(e),a=new Date(s);if(isNaN(t.getTime())||isNaN(a.getTime()))return 0;let i=Math.abs(a.getTime()-t.getTime());return Math.ceil(i/864e5)}catch(e){return 0}}validateDateRange(e,s){try{let t=new Date(e),a=new Date(s);if(isNaN(t.getTime())||isNaN(a.getTime()))return!1;return t<=a}catch(e){return!1}}createDefaultDelegate(){return{name:"",title:"",notes:""}}validateDelegates(e){let s=[];return e&&0!==e.length?(e.forEach((t,a)=>{t.name&&""!==t.name.trim()||s.push({field:`delegates.${a}.name`,message:"Delegate name is required",code:"REQUIRED_FIELD"}),t.title&&""!==t.title.trim()||s.push({field:`delegates.${a}.title`,message:"Delegate title is required",code:"REQUIRED_FIELD"}),-1!==e.findIndex((e,s)=>s!==a&&e.name.trim().toLowerCase()===t.name.trim().toLowerCase())&&s.push({field:`delegates.${a}.name`,message:"Delegate names must be unique",code:"DUPLICATE_VALUE"})}),{isValid:0===s.length,errors:s}):(s.push({field:"delegates",message:"At least one delegate is required",code:"REQUIRED_FIELD"}),{isValid:!1,errors:s})}getAvailableStatuses(){return[]}validateStatusTransition(e,s){return!0}}let eT=new eE,eC=[{icon:(0,a.jsx)(n.A,{className:"size-4"}),id:"basic",isComplete:e=>!!(e.eventName&&e.location&&e.durationFrom&&e.durationTo),name:"Event Details",required:!0},{icon:(0,a.jsx)(d.A,{className:"size-4"}),id:"delegates",isComplete:e=>!!(e.delegates&&e.delegates.length>0&&e.delegates.every(e=>e.name&&e.title)),name:"Delegates",required:!0},{icon:(0,a.jsx)(d.A,{className:"size-4"}),id:"assignment",isComplete:e=>!!(e.driverEmployeeIds&&e.driverEmployeeIds.length>0||e.escortEmployeeIds&&e.escortEmployeeIds.length>0),name:"Team Assignment",required:!1},{icon:(0,a.jsx)(o.A,{className:"size-4"}),id:"logistics",isComplete:e=>!!(e.vehicleIds&&e.vehicleIds.length>0||e.flightArrivalDetails?.flightNumber||e.flightDepartureDetails?.flightNumber),name:"Travel & Logistics",required:!1},{icon:(0,a.jsx)(c.A,{className:"size-4"}),id:"notes",isComplete:e=>!!(e.notes?.trim()||e.invitationFrom?.trim()||e.invitationTo?.trim()||e.imageUrl?.trim()),name:"Additional Information",required:!1}],eF=e=>e||{delegates:[{name:"",notes:"",title:""}],driverEmployeeIds:[],durationFrom:"",durationTo:"",escortEmployeeIds:[],eventName:"",flightArrivalDetails:null,flightDepartureDetails:null,imageUrl:"",invitationFrom:"",invitationTo:"",location:"",notes:"",status:"Planned",vehicleIds:[]},eS=e=>{let s=eC.filter(s=>s.isComplete(e)),t=eC.filter(e=>e.required),a=t.filter(s=>s.isComplete(e)),i=a.length/t.length*70,l=(s.length-a.length)/(eC.length-t.length)*30,r=Math.round(i+l);return console.log("Progress Calculation:",{optionalProgress:l,requiredProgress:i,totalProgress:r}),r},ek=(e,s)=>s?Object.keys(e).length>0?"invalid":"valid":"idle",e$=({employees:e=[],initialData:s,isEditing:t=!1,onSubmit:i,userRole:d="user",vehicles:o=[]})=>{let y=(0,g.useRouter)(),[w,D]=(0,p.useState)(!1),A=eF(s),I=(0,b.mN)({defaultValues:A,mode:"onChange",resolver:(0,r.u)(P)}),{formState:{errors:E,isDirty:T,isValid:C},watch:$}=I,R=$(),z=eS(R),V=ek(E,T),{ariaAttributes:q,clearError:L,error:O,handleSubmit:M,isLoading:U,retry:W,state:_}=(0,k.k)(async e=>{if(!await I.trigger()){D(!0);let e=Object.keys(I.formState.errors)[0];throw e&&I.setFocus(e),Error("Please complete all required fields correctly")}let s=eT.validateFormData(e);if(!s.isValid)throw Error(s.errors.map(e=>e.message).join(", "));await i(e)},{accessibility:{announceStatus:!0,focusManagement:"first-error"},formFocus:e=>I.setFocus(e),formReset:I.reset,formValidate:()=>I.trigger(),performance:{debounceMs:500,timeoutMs:45e3},preSubmitValidation:async e=>{let s=await I.trigger();if(!s){D(!0);let e=Object.keys(I.formState.errors)[0];e&&I.setFocus(e)}return s},retry:{exponentialBackoff:!0,maxAttempts:3,retryCondition:e=>e.message.includes("network")||e.message.includes("timeout")||e.message.includes("fetch")},toast:{entity:e=>({eventName:e.eventName,location:e.location}),entityType:"delegation"},transformData:e=>({...e,delegates:e.delegates?.filter(e=>e.name?.trim()&&e.title?.trim())||[],driverEmployeeIds:e.driverEmployeeIds?.filter(e=>!isNaN(e)&&e>0)||[],escortEmployeeIds:e.escortEmployeeIds?.filter(e=>!isNaN(e)&&e>0)||[],vehicleIds:e.vehicleIds?.filter(e=>!isNaN(e)&&e>0)||[]})});return(0,a.jsx)(b.Op,{...I,children:(0,a.jsxs)("form",{onSubmit:I.handleSubmit(e=>{"error"===_&&L(),M({...e,delegates:e.delegates?.filter(e=>e.name?.trim()&&e.title?.trim())||[],driverEmployeeIds:e.driverEmployeeIds?.filter(e=>!isNaN(e)&&e>0)||[],escortEmployeeIds:e.escortEmployeeIds?.filter(e=>!isNaN(e)&&e>0)||[],vehicleIds:e.vehicleIds?.filter(e=>!isNaN(e)&&e>0)||[]})}),...q,className:"jsx-b6970b4a3b531b80 "+(q&&null!=q.className&&q.className||""),children:[(0,a.jsx)(l(),{id:"b6970b4a3b531b80",children:".field-valid.jsx-b6970b4a3b531b80 input.jsx-b6970b4a3b531b80,.field-valid.jsx-b6970b4a3b531b80 select.jsx-b6970b4a3b531b80,.field-valid.jsx-b6970b4a3b531b80 textarea.jsx-b6970b4a3b531b80{border-color:rgb(34,197,94);-webkit-box-shadow:0 0 0 1px rgba(34,197,94,.1);-moz-box-shadow:0 0 0 1px rgba(34,197,94,.1);box-shadow:0 0 0 1px rgba(34,197,94,.1)}.field-invalid.jsx-b6970b4a3b531b80 input.jsx-b6970b4a3b531b80,.field-invalid.jsx-b6970b4a3b531b80 select.jsx-b6970b4a3b531b80,.field-invalid.jsx-b6970b4a3b531b80 textarea.jsx-b6970b4a3b531b80{border-color:rgb(239,68,68);-webkit-box-shadow:0 0 0 1px rgba(239,68,68,.1);-moz-box-shadow:0 0 0 1px rgba(239,68,68,.1);box-shadow:0 0 0 1px rgba(239,68,68,.1)}.field-pending.jsx-b6970b4a3b531b80 input.jsx-b6970b4a3b531b80,.field-pending.jsx-b6970b4a3b531b80 select.jsx-b6970b4a3b531b80,.field-pending.jsx-b6970b4a3b531b80 textarea.jsx-b6970b4a3b531b80{border-color:rgb(59,130,246);-webkit-box-shadow:0 0 0 1px rgba(59,130,246,.1);-moz-box-shadow:0 0 0 1px rgba(59,130,246,.1);box-shadow:0 0 0 1px rgba(59,130,246,.1)}"}),(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 space-y-6",children:[(0,a.jsx)(N.Zp,{children:(0,a.jsxs)(N.aR,{children:[(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"size-5 text-accent"}),t?"Edit Delegation":"Create New Delegation"]}),(0,a.jsx)("p",{className:"jsx-b6970b4a3b531b80 mt-1 text-sm text-muted-foreground",children:t?"Update delegation details and assignments":"Create a new delegation with responsibilities and team assignments"})]}),(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 flex items-center gap-2",children:[(0,a.jsxs)(j.E,{variant:"valid"===V?"default":"invalid"===V?"destructive":"validating"===V?"secondary":"outline",children:["valid"===V&&(0,a.jsx)(m.A,{className:"mr-1 size-3"}),"invalid"===V&&(0,a.jsx)(u.A,{className:"mr-1 size-3"}),"validating"===V&&(0,a.jsx)(c.A,{className:"mr-1 size-3 animate-pulse"}),"idle"===V&&"Not Started","valid"===V&&"All Valid","invalid"===V&&"Has Errors","validating"===V&&"Validating..."]}),Object.keys(E).length>0&&(0,a.jsxs)(j.E,{className:"text-xs",variant:"destructive",children:[Object.keys(E).length," error",Object.keys(E).length>1?"s":""]})]})]}),(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 mt-4",children:[(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 mb-2 flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"jsx-b6970b4a3b531b80 text-muted-foreground",children:"Form Progress"}),(0,a.jsxs)("span",{className:"jsx-b6970b4a3b531b80 font-medium",children:[z,"% Complete"]})]}),(0,a.jsx)(S.k,{className:"h-2",value:z})]}),(0,a.jsx)("div",{className:"jsx-b6970b4a3b531b80 mt-4 grid grid-cols-2 gap-2 md:grid-cols-5",children:eC.map(e=>{let s=e.isComplete(R);return(0,a.jsxs)("div",{className:`jsx-b6970b4a3b531b80 flex items-center gap-2 rounded-lg border p-2 transition-colors ${s?"border-green-200 bg-green-50 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-400":e.required?"border-amber-200 bg-amber-50 text-amber-700 dark:border-amber-800 dark:bg-amber-900/20 dark:text-amber-400":"border-border bg-muted text-muted-foreground"}`,children:[e.icon,(0,a.jsx)("span",{className:"jsx-b6970b4a3b531b80 text-xs font-medium",children:e.name}),s&&(0,a.jsx)(m.A,{className:"ml-auto size-3"}),!s&&e.required&&(0,a.jsx)(u.A,{className:"ml-auto size-3"})]},e.id)})})]})}),(0,a.jsx)(F,{onDismiss:()=>D(!1),show:w,showFieldNavigation:!0,showSuccess:!1,showWarnings:!0}),(0,a.jsxs)(N.Zp,{className:"shadow-lg",children:[(0,a.jsxs)(N.Wu,{className:"space-y-8 pt-6",children:[(0,a.jsx)(Q,{isSubmitting:U,userRole:d}),(0,a.jsx)(X,{isSubmitting:U,userRole:d}),(0,a.jsx)(eo,{employees:e,isSubmitting:U,userRole:d,vehicles:o}),(0,a.jsx)(eA,{isSubmitting:U,userRole:d}),(0,a.jsx)(eI,{isSubmitting:U,userRole:d}),O&&(0,a.jsxs)(f.Fc,{variant:"destructive",children:[(0,a.jsx)(u.A,{className:"size-4"}),(0,a.jsx)(f.TN,{children:(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80",children:[(0,a.jsx)("div",{className:"jsx-b6970b4a3b531b80 font-medium",children:"Submission Error"}),(0,a.jsx)("div",{className:"jsx-b6970b4a3b531b80 mt-1 text-sm",children:O})]}),"error"===_&&(0,a.jsx)(v.$,{onClick:W,size:"sm",type:"button",variant:"outline",children:"Retry"})]})})]}),"validating"===_&&(0,a.jsxs)(f.Fc,{children:[(0,a.jsx)(c.A,{className:"size-4"}),(0,a.jsx)(f.TN,{children:"Validating form data..."})]}),"retrying"===_&&(0,a.jsxs)(f.Fc,{children:[(0,a.jsx)(c.A,{className:"size-4"}),(0,a.jsx)(f.TN,{children:"Retrying submission..."})]})]}),(0,a.jsxs)(N.wL,{className:"flex justify-between gap-4 border-t pt-6",children:[(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 flex gap-2",children:[(0,a.jsxs)(v.$,{className:"min-w-[100px]",disabled:U,onClick:()=>y.back(),type:"button",variant:"outline",children:[(0,a.jsx)(x.A,{className:"mr-2 size-4"}),"Cancel"]}),!1]}),(0,a.jsxs)("div",{className:"jsx-b6970b4a3b531b80 flex items-center gap-4",children:[z<100&&(0,a.jsx)("div",{className:"jsx-b6970b4a3b531b80 text-sm text-muted-foreground",children:"Complete required sections to enable submission"}),(0,a.jsxs)(v.$,{className:"min-w-[140px] bg-accent text-accent-foreground hover:bg-accent/90",disabled:U||z<35||!C,type:"submit",children:[(0,a.jsx)(h.A,{className:"mr-2 size-4"}),U?"Saving...":t?"Save Changes":"Create Delegation"]})]})]})]})]})]})})}},71669:(e,s,t)=>{t.d(s,{C5:()=>f,MJ:()=>p,Rr:()=>b,eI:()=>h,lR:()=>g,lV:()=>o,zB:()=>m});var a=t(60687),i=t(43210),l=t(8730),r=t(27605),n=t(22482),d=t(80013);let o=r.Op,c=i.createContext({}),m=({...e})=>(0,a.jsx)(c.Provider,{value:{name:e.name},children:(0,a.jsx)(r.xI,{...e})}),u=()=>{let e=i.useContext(c),s=i.useContext(x),{getFieldState:t,formState:a}=(0,r.xW)(),l=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=s;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...l}},x=i.createContext({}),h=i.forwardRef(({className:e,...s},t)=>{let l=i.useId();return(0,a.jsx)(x.Provider,{value:{id:l},children:(0,a.jsx)("div",{ref:t,className:(0,n.cn)("space-y-2",e),...s})})});h.displayName="FormItem";let g=i.forwardRef(({className:e,...s},t)=>{let{error:i,formItemId:l}=u();return(0,a.jsx)(d.J,{ref:t,className:(0,n.cn)(i&&"text-destructive",e),htmlFor:l,...s})});g.displayName="FormLabel";let p=i.forwardRef(({...e},s)=>{let{error:t,formItemId:i,formDescriptionId:r,formMessageId:n}=u();return(0,a.jsx)(l.DX,{ref:s,id:i,"aria-describedby":t?`${r} ${n}`:`${r}`,"aria-invalid":!!t,...e})});p.displayName="FormControl";let b=i.forwardRef(({className:e,...s},t)=>{let{formDescriptionId:i}=u();return(0,a.jsx)("p",{ref:t,id:i,className:(0,n.cn)("text-sm text-muted-foreground",e),...s})});b.displayName="FormDescription";let f=i.forwardRef(({className:e,children:s,...t},i)=>{let{error:l,formMessageId:r}=u(),d=l?String(l?.message??""):s;return d?(0,a.jsx)("p",{ref:i,id:r,className:(0,n.cn)("text-sm font-medium text-destructive",e),...t,children:d}):null});f.displayName="FormMessage"},72273:(e,s,t)=>{t.d(s,{NS:()=>h,T$:()=>c,W_:()=>m,Y1:()=>u,lR:()=>x});var a=t(8693),i=t(54050),l=t(46349),r=t(87676),n=t(48839),d=t(49603);let o={all:["vehicles"],detail:e=>["vehicles",e]},c=e=>(0,l.GK)([...o.all],async()=>(await d.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),m=(e,s)=>(0,l.GK)([...o.detail(e)],()=>d.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(s?.enabled??!0),staleTime:3e5,...s}),u=()=>{let e=(0,a.jE)(),{showError:s,showSuccess:t}=(0,r.useNotifications)();return(0,i.n)({mutationFn:e=>{let s=n.M.toCreateRequest(e);return d.vehicleApiService.create(s)},onError:e=>{s(`Failed to create vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:s=>{e.invalidateQueries({queryKey:o.all}),t(`Vehicle "${s.licensePlate}" has been created successfully!`)}})},x=()=>{let e=(0,a.jE)(),{showError:s,showSuccess:t}=(0,r.useNotifications)();return(0,i.n)({mutationFn:({data:e,id:s})=>{let t=n.M.toUpdateRequest(e);return d.vehicleApiService.update(s,t)},onError:e=>{s(`Failed to update vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:s=>{e.invalidateQueries({queryKey:o.all}),e.invalidateQueries({queryKey:o.detail(s.id)}),t(`Vehicle "${s.licensePlate}" has been updated successfully!`)}})},h=()=>{let e=(0,a.jE)(),{showError:s,showSuccess:t}=(0,r.useNotifications)();return(0,i.n)({mutationFn:e=>d.vehicleApiService.delete(e),onError:e=>{s(`Failed to delete vehicle: ${e.message||"Unknown error occurred"}`)},onSuccess:(s,a)=>{e.invalidateQueries({queryKey:o.all}),e.removeQueries({queryKey:o.detail(a)}),t("Vehicle has been deleted successfully!")}})}},92929:(e,s,t)=>{t.d(s,{z:()=>o});var a=t(60687);t(43210);var i=t(27605),l=t(71669),r=t(89667),n=t(34729),d=t(15079);let o=({className:e="",disabled:s=!1,label:t,name:o,placeholder:c,render:m,type:u="text",options:x=[],defaultValue:h,icon:g,...p})=>{let{control:b}=(0,i.xW)();return(0,a.jsxs)(l.eI,{className:e,children:[(0,a.jsx)(l.lR,{htmlFor:o,children:t}),(0,a.jsx)(i.xI,{control:b,name:o,render:m||(({field:e,fieldState:{error:i}})=>(0,a.jsx)(l.MJ,{children:"select"===u?(0,a.jsxs)(d.l6,{onValueChange:e.onChange,value:e.value||h||"",disabled:s,children:[(0,a.jsx)(d.bq,{className:i?"border-red-500":"",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[g&&(0,a.jsx)(g,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)(d.yv,{placeholder:c||`Select ${t.toLowerCase()}`})]})}),(0,a.jsx)(d.gC,{children:x.map(e=>(0,a.jsx)(d.eb,{value:String(e.value),disabled:e.disabled||!1,children:e.label},e.value))})]}):"textarea"===u?(0,a.jsxs)("div",{className:"relative",children:[g&&(0,a.jsx)(g,{className:"absolute left-3 top-3 h-4 w-4 text-gray-500"}),(0,a.jsx)(n.T,{...e,...p,value:e.value??"",className:`${i?"border-red-500":""} ${g?"pl-10":""}`,disabled:s,id:o,placeholder:c})]}):(0,a.jsxs)("div",{className:"relative",children:[g&&(0,a.jsx)(g,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500"}),(0,a.jsx)(r.p,{...e,...p,value:e.value??"",className:`${i?"border-red-500":""} ${g?"pl-10":""}`,disabled:s,id:o,placeholder:c,type:u})]})}))}),(0,a.jsx)(l.C5,{})]})}}};