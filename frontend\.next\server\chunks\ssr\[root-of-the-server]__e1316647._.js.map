{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/error-boundaries/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ErrorInfo, ReactNode } from 'react';\r\n\r\nimport { <PERSON><PERSON><PERSON>riangle, RefreshCw } from 'lucide-react';\r\nimport React, { Component } from 'react';\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Button } from '@/components/ui/button';\r\n\r\ninterface Props {\r\n  children: ReactNode;\r\n  description?: string;\r\n  fallback?: ReactNode;\r\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\r\n  resetLabel?: string;\r\n  title?: string;\r\n}\r\n\r\ninterface State {\r\n  error: Error | null;\r\n  errorInfo: ErrorInfo | null;\r\n  hasError: boolean;\r\n}\r\n\r\n/**\r\n * Generic Error Boundary component\r\n * Catches errors in its child component tree and displays a fallback UI\r\n */\r\nclass ErrorBoundary extends Component<Props, State> {\r\n  constructor(props: Props) {\r\n    super(props);\r\n    this.state = {\r\n      error: null,\r\n      errorInfo: null,\r\n      hasError: false,\r\n    };\r\n  }\r\n\r\n  static getDerivedStateFromError(error: Error): Partial<State> {\r\n    // Update state so the next render will show the fallback UI\r\n    return {\r\n      error,\r\n      hasError: true,\r\n    };\r\n  }\r\n\r\n  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {\r\n    // Update state with error info for detailed reporting\r\n    this.setState({\r\n      errorInfo,\r\n    });\r\n\r\n    // Log the error\r\n    console.error('Error caught by ErrorBoundary:', error);\r\n    console.error('Component stack:', errorInfo.componentStack);\r\n\r\n    // Call onError prop if provided\r\n    if (this.props.onError) {\r\n      this.props.onError(error, errorInfo);\r\n    }\r\n\r\n    // In a production app, you would send this to a monitoring service\r\n    // Example: errorReportingService.captureError(error, errorInfo);\r\n  }\r\n\r\n  handleRetry = (): void => {\r\n    // Reset the error boundary state to trigger a re-render\r\n    this.setState({\r\n      error: null,\r\n      errorInfo: null,\r\n      hasError: false,\r\n    });\r\n  };\r\n\r\n  render(): ReactNode {\r\n    const {\r\n      description = 'An unexpected error occurred.',\r\n      resetLabel = 'Try Again',\r\n      title = 'Something went wrong',\r\n    } = this.props;\r\n\r\n    if (this.state.hasError) {\r\n      // If a custom fallback is provided, use it\r\n      if (this.props.fallback) {\r\n        return this.props.fallback;\r\n      }\r\n\r\n      // Otherwise, use the default error UI\r\n      return (\r\n        <Alert className=\"my-4\" variant=\"destructive\">\r\n          <AlertTriangle className=\"mr-2 size-4\" />\r\n          <AlertTitle className=\"text-lg font-semibold\">{title}</AlertTitle>\r\n          <AlertDescription className=\"mt-2\">\r\n            <p className=\"mb-2\">{this.state.error?.message || description}</p>\r\n            {process.env.NODE_ENV !== 'production' && this.state.errorInfo && (\r\n              <details className=\"mt-2 text-xs\">\r\n                <summary>Error details</summary>\r\n                <pre className=\"mt-2 max-h-[200px] overflow-auto whitespace-pre-wrap rounded bg-slate-100 p-2 dark:bg-slate-900\">\r\n                  {this.state.error?.stack}\r\n                  {'\\n\\nComponent Stack:\\n'}\r\n                  {this.state.errorInfo.componentStack}\r\n                </pre>\r\n              </details>\r\n            )}\r\n            <Button\r\n              className=\"mt-4\"\r\n              onClick={this.handleRetry}\r\n              size=\"sm\"\r\n              variant=\"outline\"\r\n            >\r\n              <RefreshCw className=\"mr-2 size-4\" />\r\n              {resetLabel}\r\n            </Button>\r\n          </AlertDescription>\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // If there's no error, render the children\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\nexport default ErrorBoundary;\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AACA;AAEA;AACA;AARA;;;;;;AAyBA;;;CAGC,GACD,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YACX,OAAO;YACP,WAAW;YACX,UAAU;QACZ;IACF;IAEA,OAAO,yBAAyB,KAAY,EAAkB;QAC5D,4DAA4D;QAC5D,OAAO;YACL;YACA,UAAU;QACZ;IACF;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAQ;QAC1D,sDAAsD;QACtD,IAAI,CAAC,QAAQ,CAAC;YACZ;QACF;QAEA,gBAAgB;QAChB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,QAAQ,KAAK,CAAC,oBAAoB,UAAU,cAAc;QAE1D,gCAAgC;QAChC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;IAEA,mEAAmE;IACnE,iEAAiE;IACnE;IAEA,cAAc;QACZ,wDAAwD;QACxD,IAAI,CAAC,QAAQ,CAAC;YACZ,OAAO;YACP,WAAW;YACX,UAAU;QACZ;IACF,EAAE;IAEF,SAAoB;QAClB,MAAM,EACJ,cAAc,+BAA+B,EAC7C,aAAa,WAAW,EACxB,QAAQ,sBAAsB,EAC/B,GAAG,IAAI,CAAC,KAAK;QAEd,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,2CAA2C;YAC3C,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,sCAAsC;YACtC,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBAAC,WAAU;gBAAO,SAAQ;;kCAC9B,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,8OAAC,iIAAA,CAAA,aAAU;wBAAC,WAAU;kCAAyB;;;;;;kCAC/C,8OAAC,iIAAA,CAAA,mBAAgB;wBAAC,WAAU;;0CAC1B,8OAAC;gCAAE,WAAU;0CAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW;;;;;;4BACjD,oDAAyB,gBAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,kBAC5D,8OAAC;gCAAQ,WAAU;;kDACjB,8OAAC;kDAAQ;;;;;;kDACT,8OAAC;wCAAI,WAAU;;4CACZ,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;4CAClB;4CACA,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc;;;;;;;;;;;;;0CAI1C,8OAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,IAAI,CAAC,WAAW;gCACzB,MAAK;gCACL,SAAQ;;kDAER,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCACpB;;;;;;;;;;;;;;;;;;;QAKX;QAEA,2CAA2C;QAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["/**\r\n * @file Generic dashboard layout component\r\n * @module components/dashboard/DashboardLayout\r\n */\r\n\r\n'use client';\r\n\r\nimport React from 'react';\r\n\r\nimport { DashboardProps } from './types';\r\nimport ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';\r\nimport { cn } from '@/lib/utils';\r\n\r\n/**\r\n * Generic dashboard layout component that provides consistent structure\r\n * for all dashboard-style pages in the application.\r\n * \r\n * Features:\r\n * - Consistent spacing and container management\r\n * - Error boundary protection\r\n * - Responsive design foundation\r\n * - Flexible content composition\r\n * \r\n * @param props - Dashboard layout props\r\n * @returns JSX element representing the dashboard layout\r\n */\r\nexport const DashboardLayout: React.FC<DashboardProps> = ({\r\n  children,\r\n  className = '',\r\n  config,\r\n}) => {\r\n  return (\r\n    <ErrorBoundary>\r\n      <div className={cn('min-h-screen bg-background', className)}>\r\n        <main className=\"flex-1\">\r\n          <div className=\"container mx-auto space-y-6 px-4 py-6 sm:px-6 lg:px-8\">\r\n            {children}\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </ErrorBoundary>\r\n  );\r\n};\r\n\r\n/**\r\n * Dashboard page wrapper that provides the standard page structure\r\n */\r\nexport const DashboardPage: React.FC<DashboardProps> = ({\r\n  children,\r\n  className = '',\r\n  config,\r\n}) => {\r\n  return (\r\n    <DashboardLayout config={config} className={className}>\r\n      <div className=\"space-y-8\">\r\n        {children}\r\n      </div>\r\n    </DashboardLayout>\r\n  );\r\n};\r\n\r\nexport default DashboardLayout;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAOD;AACA;AAAA;AANA;;;;AAqBO,MAAM,kBAA4C,CAAC,EACxD,QAAQ,EACR,YAAY,EAAE,EACd,MAAM,EACP;IACC,qBACE,8OAAC,0JAAA,CAAA,UAAa;kBACZ,cAAA,8OAAC;YAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;sBAC/C,cAAA,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;;;;;AAMb;AAKO,MAAM,gBAA0C,CAAC,EACtD,QAAQ,EACR,YAAY,EAAE,EACd,MAAM,EACP;IACC,qBACE,8OAAC;QAAgB,QAAQ;QAAQ,WAAW;kBAC1C,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/domain/useDashboardStore.ts"], "sourcesContent": ["/**\r\n * @file Generic dashboard store hook\r\n * @module hooks/useDashboardStore\r\n */\r\n\r\nimport { useMemo } from 'react';\r\nimport { create } from 'zustand';\r\nimport { devtools, persist } from 'zustand/middleware';\r\n\r\nimport type {\r\n  DashboardConfig,\r\n  DashboardStore,\r\n  ViewMode,\r\n} from '@/components/dashboard/types';\r\n\r\n/**\r\n * Create a dashboard store for a specific entity type\r\n */\r\nexport function createDashboardStore(entityType: string) {\r\n  return create<DashboardStore>()(\r\n    devtools(\r\n      persist(\r\n        (set, get) => ({\r\n          // Initial state\r\n          activeTab: 'all',\r\n          layout: {\r\n            viewMode: 'cards' as ViewMode,\r\n            gridColumns: 3,\r\n            compactMode: false,\r\n            showFilters: true,\r\n            showSettings: false,\r\n          },\r\n          monitoring: {\r\n            enabled: false,\r\n            refreshInterval: 30000,\r\n            autoRefresh: true,\r\n            pausedDataTypes: new Set<string>(),\r\n          },\r\n          filters: {},\r\n          sortBy: 'createdAt',\r\n          sortDirection: 'desc',\r\n          selectedItems: new Set<string>(),\r\n          searchTerm: '',\r\n\r\n          // Actions\r\n          setActiveTab: (tab: string) =>\r\n            set({ activeTab: tab }, false, 'setActiveTab'),\r\n\r\n          setViewMode: (mode: ViewMode) =>\r\n            set(\r\n              state => ({\r\n                layout: { ...state.layout, viewMode: mode },\r\n              }),\r\n              false,\r\n              'setViewMode'\r\n            ),\r\n\r\n          setGridColumns: (columns: number) =>\r\n            set(\r\n              state => ({\r\n                layout: { ...state.layout, gridColumns: columns },\r\n              }),\r\n              false,\r\n              'setGridColumns'\r\n            ),\r\n\r\n          toggleCompactMode: () =>\r\n            set(\r\n              state => ({\r\n                layout: {\r\n                  ...state.layout,\r\n                  compactMode: !state.layout.compactMode,\r\n                },\r\n              }),\r\n              false,\r\n              'toggleCompactMode'\r\n            ),\r\n\r\n          toggleFilters: () =>\r\n            set(\r\n              state => ({\r\n                layout: {\r\n                  ...state.layout,\r\n                  showFilters: !state.layout.showFilters,\r\n                },\r\n              }),\r\n              false,\r\n              'toggleFilters'\r\n            ),\r\n\r\n          toggleSettings: () =>\r\n            set(\r\n              state => ({\r\n                layout: {\r\n                  ...state.layout,\r\n                  showSettings: !state.layout.showSettings,\r\n                },\r\n              }),\r\n              false,\r\n              'toggleSettings'\r\n            ),\r\n\r\n          updateFilter: (filterId: string, value: any) =>\r\n            set(\r\n              state => ({\r\n                filters: { ...state.filters, [filterId]: value },\r\n              }),\r\n              false,\r\n              'updateFilter'\r\n            ),\r\n\r\n          clearFilters: () => set({ filters: {} }, false, 'clearFilters'),\r\n\r\n          setSorting: (field: string, direction: 'asc' | 'desc') =>\r\n            set(\r\n              { sortBy: field, sortDirection: direction },\r\n              false,\r\n              'setSorting'\r\n            ),\r\n\r\n          setSearchTerm: (term: string) =>\r\n            set({ searchTerm: term }, false, 'setSearchTerm'),\r\n\r\n          toggleItemSelection: (id: string) =>\r\n            set(\r\n              state => {\r\n                const newSelection = new Set(state.selectedItems);\r\n                if (newSelection.has(id)) {\r\n                  newSelection.delete(id);\r\n                } else {\r\n                  newSelection.add(id);\r\n                }\r\n                return { selectedItems: newSelection };\r\n              },\r\n              false,\r\n              'toggleItemSelection'\r\n            ),\r\n\r\n          clearSelection: () =>\r\n            set({ selectedItems: new Set() }, false, 'clearSelection'),\r\n\r\n          selectAll: (ids: string[]) =>\r\n            set({ selectedItems: new Set(ids) }, false, 'selectAll'),\r\n\r\n          // Monitoring actions\r\n          setMonitoringEnabled: (enabled: boolean) =>\r\n            set(\r\n              state => ({\r\n                monitoring: { ...state.monitoring, enabled },\r\n              }),\r\n              false,\r\n              'setMonitoringEnabled'\r\n            ),\r\n\r\n          setRefreshInterval: (interval: number) =>\r\n            set(\r\n              state => ({\r\n                monitoring: { ...state.monitoring, refreshInterval: interval },\r\n              }),\r\n              false,\r\n              'setRefreshInterval'\r\n            ),\r\n\r\n          toggleAutoRefresh: () =>\r\n            set(\r\n              state => ({\r\n                monitoring: {\r\n                  ...state.monitoring,\r\n                  autoRefresh: !state.monitoring.autoRefresh,\r\n                },\r\n              }),\r\n              false,\r\n              'toggleAutoRefresh'\r\n            ),\r\n\r\n          pauseDataType: (dataType: string) =>\r\n            set(\r\n              state => ({\r\n                monitoring: {\r\n                  ...state.monitoring,\r\n                  pausedDataTypes: new Set([\r\n                    ...state.monitoring.pausedDataTypes,\r\n                    dataType,\r\n                  ]),\r\n                },\r\n              }),\r\n              false,\r\n              'pauseDataType'\r\n            ),\r\n\r\n          resumeDataType: (dataType: string) =>\r\n            set(\r\n              state => {\r\n                const newPausedTypes = new Set(\r\n                  state.monitoring.pausedDataTypes\r\n                );\r\n                newPausedTypes.delete(dataType);\r\n                return {\r\n                  monitoring: {\r\n                    ...state.monitoring,\r\n                    pausedDataTypes: newPausedTypes,\r\n                  },\r\n                };\r\n              },\r\n              false,\r\n              'resumeDataType'\r\n            ),\r\n\r\n          resetSettings: () =>\r\n            set(\r\n              {\r\n                layout: {\r\n                  viewMode: 'cards' as ViewMode,\r\n                  gridColumns: 3,\r\n                  compactMode: false,\r\n                  showFilters: true,\r\n                  showSettings: false,\r\n                },\r\n                monitoring: {\r\n                  enabled: false,\r\n                  refreshInterval: 30000,\r\n                  autoRefresh: true,\r\n                  pausedDataTypes: new Set<string>(),\r\n                },\r\n                filters: {},\r\n                sortBy: 'createdAt',\r\n                sortDirection: 'desc',\r\n                selectedItems: new Set<string>(),\r\n                searchTerm: '',\r\n              },\r\n              false,\r\n              'resetSettings'\r\n            ),\r\n\r\n          // Computed selectors\r\n          getFilteredData: <T extends { id: string }>(\r\n            data: T[],\r\n            config: DashboardConfig<T>\r\n          ): T[] => {\r\n            const state = get();\r\n            let filtered = [...data];\r\n\r\n            // Apply search filter\r\n            if (state.searchTerm) {\r\n              const searchLower = state.searchTerm.toLowerCase();\r\n              filtered = filtered.filter((item: any) =>\r\n                Object.values(item).some(value =>\r\n                  String(value).toLowerCase().includes(searchLower)\r\n                )\r\n              );\r\n            }\r\n\r\n            // Apply other filters\r\n            Object.entries(state.filters).forEach(([filterId, value]) => {\r\n              if (value !== undefined && value !== null && value !== '') {\r\n                filtered = filtered.filter((item: any) => {\r\n                  const filterConfig = config.filters?.find(\r\n                    f => f.id === filterId\r\n                  );\r\n                  if (!filterConfig) return true;\r\n\r\n                  switch (filterConfig.type) {\r\n                    case 'select':\r\n                      return item[filterId] === value;\r\n                    case 'multiselect':\r\n                      return Array.isArray(value)\r\n                        ? value.includes(item[filterId])\r\n                        : true;\r\n                    case 'toggle':\r\n                      return value ? item[filterId] : true;\r\n                    default:\r\n                      return true;\r\n                  }\r\n                });\r\n              }\r\n            });\r\n\r\n            // Apply sorting\r\n            filtered.sort((a: any, b: any) => {\r\n              const aValue = a[state.sortBy];\r\n              const bValue = b[state.sortBy];\r\n              const direction = state.sortDirection === 'asc' ? 1 : -1;\r\n\r\n              if (aValue < bValue) return -1 * direction;\r\n              if (aValue > bValue) return 1 * direction;\r\n              return 0;\r\n            });\r\n\r\n            return filtered;\r\n          },\r\n\r\n          getSelectedCount: () => get().selectedItems.size,\r\n\r\n          hasActiveFilters: () => {\r\n            const state = get();\r\n            return (\r\n              state.searchTerm.length > 0 ||\r\n              Object.values(state.filters).some(\r\n                value => value !== undefined && value !== null && value !== ''\r\n              )\r\n            );\r\n          },\r\n        }),\r\n        {\r\n          name: `workhub-dashboard-${entityType}`,\r\n          partialize: state => ({\r\n            layout: state.layout,\r\n            monitoring: state.monitoring,\r\n            filters: state.filters,\r\n            sortBy: state.sortBy,\r\n            sortDirection: state.sortDirection,\r\n          }),\r\n        }\r\n      ),\r\n      {\r\n        name: `dashboard-${entityType}`,\r\n      }\r\n    )\r\n  );\r\n}\r\n\r\n/**\r\n * Store registry to ensure single instance per entity type\r\n */\r\nconst storeRegistry = new Map<\r\n  string,\r\n  ReturnType<typeof createDashboardStore>\r\n>();\r\n\r\n/**\r\n * Hook to use dashboard store for a specific entity type\r\n */\r\nexport function useDashboardStore(entityType: string) {\r\n  return useMemo(() => {\r\n    if (!storeRegistry.has(entityType)) {\r\n      storeRegistry.set(entityType, createDashboardStore(entityType));\r\n    }\r\n    return storeRegistry.get(entityType)!;\r\n  }, [entityType]);\r\n}\r\n\r\nexport default useDashboardStore;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AACA;;;;AAWO,SAAS,qBAAqB,UAAkB;IACrD,OAAO,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACV,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;YACb,gBAAgB;YAChB,WAAW;YACX,QAAQ;gBACN,UAAU;gBACV,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,cAAc;YAChB;YACA,YAAY;gBACV,SAAS;gBACT,iBAAiB;gBACjB,aAAa;gBACb,iBAAiB,IAAI;YACvB;YACA,SAAS,CAAC;YACV,QAAQ;YACR,eAAe;YACf,eAAe,IAAI;YACnB,YAAY;YAEZ,UAAU;YACV,cAAc,CAAC,MACb,IAAI;oBAAE,WAAW;gBAAI,GAAG,OAAO;YAEjC,aAAa,CAAC,OACZ,IACE,CAAA,QAAS,CAAC;wBACR,QAAQ;4BAAE,GAAG,MAAM,MAAM;4BAAE,UAAU;wBAAK;oBAC5C,CAAC,GACD,OACA;YAGJ,gBAAgB,CAAC,UACf,IACE,CAAA,QAAS,CAAC;wBACR,QAAQ;4BAAE,GAAG,MAAM,MAAM;4BAAE,aAAa;wBAAQ;oBAClD,CAAC,GACD,OACA;YAGJ,mBAAmB,IACjB,IACE,CAAA,QAAS,CAAC;wBACR,QAAQ;4BACN,GAAG,MAAM,MAAM;4BACf,aAAa,CAAC,MAAM,MAAM,CAAC,WAAW;wBACxC;oBACF,CAAC,GACD,OACA;YAGJ,eAAe,IACb,IACE,CAAA,QAAS,CAAC;wBACR,QAAQ;4BACN,GAAG,MAAM,MAAM;4BACf,aAAa,CAAC,MAAM,MAAM,CAAC,WAAW;wBACxC;oBACF,CAAC,GACD,OACA;YAGJ,gBAAgB,IACd,IACE,CAAA,QAAS,CAAC;wBACR,QAAQ;4BACN,GAAG,MAAM,MAAM;4BACf,cAAc,CAAC,MAAM,MAAM,CAAC,YAAY;wBAC1C;oBACF,CAAC,GACD,OACA;YAGJ,cAAc,CAAC,UAAkB,QAC/B,IACE,CAAA,QAAS,CAAC;wBACR,SAAS;4BAAE,GAAG,MAAM,OAAO;4BAAE,CAAC,SAAS,EAAE;wBAAM;oBACjD,CAAC,GACD,OACA;YAGJ,cAAc,IAAM,IAAI;oBAAE,SAAS,CAAC;gBAAE,GAAG,OAAO;YAEhD,YAAY,CAAC,OAAe,YAC1B,IACE;oBAAE,QAAQ;oBAAO,eAAe;gBAAU,GAC1C,OACA;YAGJ,eAAe,CAAC,OACd,IAAI;oBAAE,YAAY;gBAAK,GAAG,OAAO;YAEnC,qBAAqB,CAAC,KACpB,IACE,CAAA;oBACE,MAAM,eAAe,IAAI,IAAI,MAAM,aAAa;oBAChD,IAAI,aAAa,GAAG,CAAC,KAAK;wBACxB,aAAa,MAAM,CAAC;oBACtB,OAAO;wBACL,aAAa,GAAG,CAAC;oBACnB;oBACA,OAAO;wBAAE,eAAe;oBAAa;gBACvC,GACA,OACA;YAGJ,gBAAgB,IACd,IAAI;oBAAE,eAAe,IAAI;gBAAM,GAAG,OAAO;YAE3C,WAAW,CAAC,MACV,IAAI;oBAAE,eAAe,IAAI,IAAI;gBAAK,GAAG,OAAO;YAE9C,qBAAqB;YACrB,sBAAsB,CAAC,UACrB,IACE,CAAA,QAAS,CAAC;wBACR,YAAY;4BAAE,GAAG,MAAM,UAAU;4BAAE;wBAAQ;oBAC7C,CAAC,GACD,OACA;YAGJ,oBAAoB,CAAC,WACnB,IACE,CAAA,QAAS,CAAC;wBACR,YAAY;4BAAE,GAAG,MAAM,UAAU;4BAAE,iBAAiB;wBAAS;oBAC/D,CAAC,GACD,OACA;YAGJ,mBAAmB,IACjB,IACE,CAAA,QAAS,CAAC;wBACR,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB,aAAa,CAAC,MAAM,UAAU,CAAC,WAAW;wBAC5C;oBACF,CAAC,GACD,OACA;YAGJ,eAAe,CAAC,WACd,IACE,CAAA,QAAS,CAAC;wBACR,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB,iBAAiB,IAAI,IAAI;mCACpB,MAAM,UAAU,CAAC,eAAe;gCACnC;6BACD;wBACH;oBACF,CAAC,GACD,OACA;YAGJ,gBAAgB,CAAC,WACf,IACE,CAAA;oBACE,MAAM,iBAAiB,IAAI,IACzB,MAAM,UAAU,CAAC,eAAe;oBAElC,eAAe,MAAM,CAAC;oBACtB,OAAO;wBACL,YAAY;4BACV,GAAG,MAAM,UAAU;4BACnB,iBAAiB;wBACnB;oBACF;gBACF,GACA,OACA;YAGJ,eAAe,IACb,IACE;oBACE,QAAQ;wBACN,UAAU;wBACV,aAAa;wBACb,aAAa;wBACb,aAAa;wBACb,cAAc;oBAChB;oBACA,YAAY;wBACV,SAAS;wBACT,iBAAiB;wBACjB,aAAa;wBACb,iBAAiB,IAAI;oBACvB;oBACA,SAAS,CAAC;oBACV,QAAQ;oBACR,eAAe;oBACf,eAAe,IAAI;oBACnB,YAAY;gBACd,GACA,OACA;YAGJ,qBAAqB;YACrB,iBAAiB,CACf,MACA;gBAEA,MAAM,QAAQ;gBACd,IAAI,WAAW;uBAAI;iBAAK;gBAExB,sBAAsB;gBACtB,IAAI,MAAM,UAAU,EAAE;oBACpB,MAAM,cAAc,MAAM,UAAU,CAAC,WAAW;oBAChD,WAAW,SAAS,MAAM,CAAC,CAAC,OAC1B,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,CAAA,QACvB,OAAO,OAAO,WAAW,GAAG,QAAQ,CAAC;gBAG3C;gBAEA,sBAAsB;gBACtB,OAAO,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,UAAU,MAAM;oBACtD,IAAI,UAAU,aAAa,UAAU,QAAQ,UAAU,IAAI;wBACzD,WAAW,SAAS,MAAM,CAAC,CAAC;4BAC1B,MAAM,eAAe,OAAO,OAAO,EAAE,KACnC,CAAA,IAAK,EAAE,EAAE,KAAK;4BAEhB,IAAI,CAAC,cAAc,OAAO;4BAE1B,OAAQ,aAAa,IAAI;gCACvB,KAAK;oCACH,OAAO,IAAI,CAAC,SAAS,KAAK;gCAC5B,KAAK;oCACH,OAAO,MAAM,OAAO,CAAC,SACjB,MAAM,QAAQ,CAAC,IAAI,CAAC,SAAS,IAC7B;gCACN,KAAK;oCACH,OAAO,QAAQ,IAAI,CAAC,SAAS,GAAG;gCAClC;oCACE,OAAO;4BACX;wBACF;oBACF;gBACF;gBAEA,gBAAgB;gBAChB,SAAS,IAAI,CAAC,CAAC,GAAQ;oBACrB,MAAM,SAAS,CAAC,CAAC,MAAM,MAAM,CAAC;oBAC9B,MAAM,SAAS,CAAC,CAAC,MAAM,MAAM,CAAC;oBAC9B,MAAM,YAAY,MAAM,aAAa,KAAK,QAAQ,IAAI,CAAC;oBAEvD,IAAI,SAAS,QAAQ,OAAO,CAAC,IAAI;oBACjC,IAAI,SAAS,QAAQ,OAAO,IAAI;oBAChC,OAAO;gBACT;gBAEA,OAAO;YACT;YAEA,kBAAkB,IAAM,MAAM,aAAa,CAAC,IAAI;YAEhD,kBAAkB;gBAChB,MAAM,QAAQ;gBACd,OACE,MAAM,UAAU,CAAC,MAAM,GAAG,KAC1B,OAAO,MAAM,CAAC,MAAM,OAAO,EAAE,IAAI,CAC/B,CAAA,QAAS,UAAU,aAAa,UAAU,QAAQ,UAAU;YAGlE;QACF,CAAC,GACD;QACE,MAAM,CAAC,kBAAkB,EAAE,YAAY;QACvC,YAAY,CAAA,QAAS,CAAC;gBACpB,QAAQ,MAAM,MAAM;gBACpB,YAAY,MAAM,UAAU;gBAC5B,SAAS,MAAM,OAAO;gBACtB,QAAQ,MAAM,MAAM;gBACpB,eAAe,MAAM,aAAa;YACpC,CAAC;IACH,IAEF;QACE,MAAM,CAAC,UAAU,EAAE,YAAY;IACjC;AAGN;AAEA;;CAEC,GACD,MAAM,gBAAgB,IAAI;AAQnB,SAAS,kBAAkB,UAAkB;IAClD,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,IAAI,CAAC,cAAc,GAAG,CAAC,aAAa;YAClC,cAAc,GAAG,CAAC,YAAY,qBAAqB;QACrD;QACA,OAAO,cAAc,GAAG,CAAC;IAC3B,GAAG;QAAC;KAAW;AACjB;uCAEe", "debugId": null}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/services/WebSocketManager.ts"], "sourcesContent": ["/**\r\n * @file Unified WebSocket Manager for WorkHub Application\r\n * Provides centralized WebSocket connection management with domain-specific channels\r\n * Follows SRP and DRY principles with smart fallback strategies\r\n * @module services/WebSocketManager\r\n */\r\n\r\nimport type { Socket } from 'socket.io-client';\r\n\r\nimport { io } from 'socket.io-client';\r\n\r\nimport { supabase } from '../supabase';\r\nimport { getTokenRefreshService } from './TokenRefreshService';\r\n/*import logger from '../utils/logger';\r\n\r\n/**\r\n * WebSocket connection states\r\n */\r\nexport type ConnectionState =\r\n  | 'connected'\r\n  | 'connecting'\r\n  | 'disconnected'\r\n  | 'error'\r\n  | 'reconnecting';\r\n\r\n/**\r\n * Domain-specific channels for organized event management\r\n */\r\nexport type DomainChannel = 'crud' | 'notifications' | 'reliability' | 'system';\r\n\r\n/**\r\n * Event subscription callback type\r\n */\r\nexport type EventCallback<T = any> = (data: T) => void;\r\n\r\n/**\r\n * WebSocket configuration options\r\n */\r\nexport interface WebSocketConfig {\r\n  autoConnect?: boolean;\r\n  reconnectAttempts?: number;\r\n  reconnectDelay?: number;\r\n  timeout?: number;\r\n  url?: string;\r\n}\r\n\r\n/**\r\n * Unified WebSocket Manager\r\n * Implements Singleton pattern for single connection per application\r\n * Provides domain-specific channels and centralized subscription management\r\n */\r\nexport class WebSocketManager {\r\n  private static instance: null | WebSocketManager = null;\r\n  private readonly config: Required<WebSocketConfig>;\r\n  private connectionState: ConnectionState = 'disconnected';\r\n  private reconnectAttempts = 0;\r\n  private socket: null | Socket = null;\r\n  private readonly stateListeners = new Set<(state: ConnectionState) => void>();\r\n  private readonly subscriptions = new Map<string, Set<EventCallback>>();\r\n\r\n  private constructor(config: WebSocketConfig = {}) {\r\n    this.config = {\r\n      autoConnect: config.autoConnect ?? true,\r\n      reconnectAttempts: config.reconnectAttempts ?? 5,\r\n      reconnectDelay: config.reconnectDelay ?? 1000,\r\n      timeout: config.timeout ?? 10_000,\r\n      url:\r\n        config.url ??\r\n        process.env.NEXT_PUBLIC_WEBSOCKET_URL ??\r\n        'http://localhost:3001',\r\n    };\r\n\r\n    if (this.config.autoConnect) {\r\n      this.connect();\r\n    }\r\n\r\n    // Subscribe to token refresh events\r\n    this.setupTokenRefreshHandling();\r\n  }\r\n\r\n  /**\r\n   * Get singleton instance\r\n   */\r\n  public static getInstance(config?: WebSocketConfig): WebSocketManager {\r\n    WebSocketManager.instance ??= new WebSocketManager(config);\r\n    return WebSocketManager.instance;\r\n  }\r\n\r\n  /**\r\n   * Connect to WebSocket server\r\n   */\r\n  public async connect(): Promise<void> {\r\n    if (this.socket?.connected) {\r\n      console.debug('WebSocket already connected');\r\n      return;\r\n    }\r\n\r\n    this.setConnectionState('connecting');\r\n\r\n    try {\r\n      // Get current session and token for authentication\r\n      const {\r\n        data: { session },\r\n        error,\r\n      } = await supabase.auth.getSession();\r\n\r\n      if (error) {\r\n        console.warn('Failed to get session for WebSocket connection:', error);\r\n      }\r\n\r\n      const connectionOptions: any = {\r\n        forceNew: true,\r\n        timeout: this.config.timeout,\r\n        transports: ['websocket', 'polling'],\r\n        withCredentials: true, // Ensure cookies are sent with WebSocket handshake\r\n      };\r\n\r\n      // Add authentication token if available\r\n      if (session?.access_token) {\r\n        connectionOptions.auth = {\r\n          token: session.access_token,\r\n        };\r\n        console.debug('🔐 WebSocket connecting with authentication token');\r\n\r\n        // Validate token expiration\r\n        const tokenExpiry = session.expires_at ? session.expires_at * 1000 : 0;\r\n        const now = Date.now();\r\n        const timeUntilExpiry = tokenExpiry - now;\r\n\r\n        if (timeUntilExpiry <= 60_000) {\r\n          // Less than 1 minute\r\n          console.warn('⚠️ WebSocket token expires soon, may need refresh');\r\n        }\r\n      } else {\r\n        console.warn(\r\n          '⚠️ WebSocket connecting without authentication token - connection may fail'\r\n        );\r\n      }\r\n\r\n      this.socket = io(this.config.url, connectionOptions);\r\n\r\n      this.setupEventHandlers();\r\n    } catch (error) {\r\n      console.error('Failed to connect WebSocket:', error);\r\n      this.setConnectionState('error');\r\n      this.scheduleReconnect();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Cleanup resources\r\n   */\r\n  public destroy(): void {\r\n    this.disconnect();\r\n    this.subscriptions.clear();\r\n    this.stateListeners.clear();\r\n    WebSocketManager.instance = null;\r\n  }\r\n\r\n  /**\r\n   * Disconnect from WebSocket server\r\n   */\r\n  public disconnect(): void {\r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n      this.socket = null;\r\n    }\r\n    this.setConnectionState('disconnected');\r\n    this.reconnectAttempts = 0;\r\n  }\r\n\r\n  /**\r\n   * Emit event to specific domain channel\r\n   */\r\n  public emit(channel: DomainChannel, event: string, data?: any): void {\r\n    if (!this.socket?.connected) {\r\n      console.warn(`Cannot emit ${channel}:${event} - WebSocket not connected`);\r\n      return;\r\n    }\r\n\r\n    this.socket.emit(event, data);\r\n  }\r\n\r\n  /**\r\n   * Get current connection state\r\n   */\r\n  public getConnectionState(): ConnectionState {\r\n    return this.connectionState;\r\n  }\r\n\r\n  /**\r\n   * Check if WebSocket is connected\r\n   */\r\n  public isConnected(): boolean {\r\n    return (\r\n      this.connectionState === 'connected' && this.socket?.connected === true\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Join domain-specific room\r\n   */\r\n  public joinRoom(room: string): void {\r\n    if (!this.socket?.connected) {\r\n      console.warn(`Cannot join room ${room} - WebSocket not connected`);\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('join-room', room);\r\n  }\r\n\r\n  /**\r\n   * Leave domain-specific room\r\n   */\r\n  public leaveRoom(room: string): void {\r\n    if (!this.socket?.connected) {\r\n      return;\r\n    }\r\n\r\n    this.socket.emit('leave-room', room);\r\n  }\r\n\r\n  /**\r\n   * Subscribe to connection state changes\r\n   */\r\n  public onStateChange(callback: (state: ConnectionState) => void): () => void {\r\n    this.stateListeners.add(callback);\r\n\r\n    return () => {\r\n      this.stateListeners.delete(callback);\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Subscribe to domain-specific events\r\n   */\r\n  public subscribe<T = any>(\r\n    channel: DomainChannel,\r\n    event: string,\r\n    callback: EventCallback<T>\r\n  ): () => void {\r\n    const eventKey = `${channel}:${event}`;\r\n\r\n    if (!this.subscriptions.has(eventKey)) {\r\n      this.subscriptions.set(eventKey, new Set());\r\n    }\r\n\r\n    this.subscriptions.get(eventKey)!.add(callback);\r\n\r\n    // Set up socket listener if connected\r\n    if (this.socket?.connected && event) {\r\n      this.socket.on(event, callback);\r\n    }\r\n\r\n    // Return unsubscribe function\r\n    return () => {\r\n      const callbacks = this.subscriptions.get(eventKey);\r\n      if (callbacks) {\r\n        callbacks.delete(callback);\r\n        if (callbacks.size === 0) {\r\n          this.subscriptions.delete(eventKey);\r\n        }\r\n      }\r\n\r\n      if (this.socket && event) {\r\n        this.socket.off(event, callback);\r\n      }\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Handle authentication errors by triggering token refresh\r\n   */\r\n  private handleAuthenticationError(): void {\r\n    const tokenRefreshService = getTokenRefreshService();\r\n\r\n    console.log('🔐 Handling WebSocket authentication error...');\r\n\r\n    // Disconnect current socket to prevent further auth errors\r\n    if (this.socket) {\r\n      this.socket.disconnect();\r\n      this.socket = null;\r\n    }\r\n\r\n    // Attempt to refresh token\r\n    tokenRefreshService\r\n      .refreshNow()\r\n      .then(success => {\r\n        if (success) {\r\n          console.log(\r\n            '🔄 Token refresh successful, retrying WebSocket connection'\r\n          );\r\n          // The reconnection will be handled by setupTokenRefreshHandling\r\n        } else {\r\n          console.error('🔄 Token refresh failed, scheduling normal reconnect');\r\n          this.scheduleReconnect();\r\n        }\r\n      })\r\n      .catch(error => {\r\n        console.error('🔄 Token refresh error:', error);\r\n        this.scheduleReconnect();\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Resubscribe to all events after reconnection\r\n   */\r\n  private resubscribeToEvents(): void {\r\n    if (!this.socket) return;\r\n\r\n    for (const [eventKey, callbacks] of this.subscriptions) {\r\n      const [, event] = eventKey.split(':');\r\n      for (const callback of callbacks) {\r\n        if (event) {\r\n          this.socket!.on(event, callback);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Schedule reconnection with exponential backoff\r\n   */\r\n  private scheduleReconnect(): void {\r\n    if (this.reconnectAttempts >= this.config.reconnectAttempts) {\r\n      console.error('Max reconnection attempts reached');\r\n      this.setConnectionState('error');\r\n      return;\r\n    }\r\n\r\n    this.setConnectionState('reconnecting');\r\n    this.reconnectAttempts++;\r\n\r\n    const delay =\r\n      this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\r\n\r\n    setTimeout(() => {\r\n      console.info(\r\n        `Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts})`\r\n      );\r\n      this.connect();\r\n    }, delay);\r\n  }\r\n\r\n  /**\r\n   * Set connection state and notify listeners\r\n   */\r\n  private setConnectionState(state: ConnectionState): void {\r\n    if (this.connectionState !== state) {\r\n      this.connectionState = state;\r\n      for (const listener of this.stateListeners) listener(state);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Setup socket event handlers\r\n   */\r\n  private setupEventHandlers(): void {\r\n    if (!this.socket) return;\r\n\r\n    this.socket.on('connect', () => {\r\n      console.info('WebSocket connected');\r\n      this.setConnectionState('connected');\r\n      this.reconnectAttempts = 0;\r\n      this.resubscribeToEvents();\r\n    });\r\n\r\n    this.socket.on('disconnect', reason => {\r\n      console.warn('WebSocket disconnected:', reason);\r\n      this.setConnectionState('disconnected');\r\n\r\n      if (reason === 'io server disconnect') {\r\n        // Server initiated disconnect, don't reconnect automatically\r\n        return;\r\n      }\r\n\r\n      this.scheduleReconnect();\r\n    });\r\n\r\n    this.socket.on('connect_error', error => {\r\n      console.error('WebSocket connection error:', error);\r\n      this.setConnectionState('error');\r\n\r\n      // Check if error is authentication-related\r\n      if (\r\n        error.message?.includes('Authentication') ||\r\n        error.message?.includes('token') ||\r\n        error.message?.includes('No token provided') ||\r\n        error.message?.includes('Unauthorized')\r\n      ) {\r\n        console.warn(\r\n          '🔐 Authentication error detected, attempting token refresh'\r\n        );\r\n        this.handleAuthenticationError();\r\n      } else {\r\n        this.scheduleReconnect();\r\n      }\r\n    });\r\n\r\n    // Listen for authentication errors from the server\r\n    this.socket.on('auth_error', errorData => {\r\n      console.error('🔐 Server authentication error:', errorData);\r\n      this.handleAuthenticationError();\r\n    });\r\n\r\n    // Listen for token refresh requests from server\r\n    this.socket.on('token_refresh_required', () => {\r\n      console.warn('🔄 Server requested token refresh');\r\n      this.handleAuthenticationError();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Setup token refresh event handling\r\n   */\r\n  private setupTokenRefreshHandling(): void {\r\n    const tokenRefreshService = getTokenRefreshService();\r\n\r\n    tokenRefreshService.subscribe((event, _data) => {\r\n      switch (event) {\r\n        case 'critical_refresh_failed': {\r\n          console.error(\r\n            '🔄 Critical token refresh failure, disconnecting WebSocket'\r\n          );\r\n          this.disconnect();\r\n          this.setConnectionState('error');\r\n          break;\r\n        }\r\n\r\n        case 'refresh_failed': {\r\n          console.error(\r\n            '🔄 Token refresh failed, WebSocket may lose connection'\r\n          );\r\n          break;\r\n        }\r\n\r\n        case 'refresh_success': {\r\n          console.log(\r\n            '🔄 Token refreshed, reconnecting WebSocket with new token'\r\n          );\r\n          // Disconnect current connection and reconnect with new token\r\n          if (this.socket) {\r\n            this.socket.disconnect();\r\n            this.socket = null;\r\n          }\r\n          // Reconnect with fresh token\r\n          setTimeout(() => this.connect(), 500);\r\n          break;\r\n        }\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * Get the singleton WebSocket manager instance\r\n */\r\nexport const getWebSocketManager = (\r\n  config?: WebSocketConfig\r\n): WebSocketManager => {\r\n  return WebSocketManager.getInstance(config);\r\n};\r\n\r\n/**\r\n * Hook for WebSocket connection state\r\n */\r\nexport const useWebSocketState = () => {\r\n  const manager = getWebSocketManager();\r\n  return {\r\n    connectionState: manager.getConnectionState(),\r\n    isConnected: manager.isConnected(),\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;AAID;AAAA;AAEA;AACA;;;;AAuCO,MAAM;IACX,OAAe,WAAoC,KAAK;IACvC,OAAkC;IAC3C,kBAAmC,eAAe;IAClD,oBAAoB,EAAE;IACtB,SAAwB,KAAK;IACpB,iBAAiB,IAAI,MAAwC;IAC7D,gBAAgB,IAAI,MAAkC;IAEvE,YAAoB,SAA0B,CAAC,CAAC,CAAE;QAChD,IAAI,CAAC,MAAM,GAAG;YACZ,aAAa,OAAO,WAAW,IAAI;YACnC,mBAAmB,OAAO,iBAAiB,IAAI;YAC/C,gBAAgB,OAAO,cAAc,IAAI;YACzC,SAAS,OAAO,OAAO,IAAI;YAC3B,KACE,OAAO,GAAG,IACV,QAAQ,GAAG,CAAC,yBAAyB,IACrC;QACJ;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC3B,IAAI,CAAC,OAAO;QACd;QAEA,oCAAoC;QACpC,IAAI,CAAC,yBAAyB;IAChC;IAEA;;GAEC,GACD,OAAc,YAAY,MAAwB,EAAoB;QACpE,iBAAiB,QAAQ,KAAK,IAAI,iBAAiB;QACnD,OAAO,iBAAiB,QAAQ;IAClC;IAEA;;GAEC,GACD,MAAa,UAAyB;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE,WAAW;YAC1B,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC;QAExB,IAAI;YACF,mDAAmD;YACnD,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EACjB,KAAK,EACN,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAElC,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,mDAAmD;YAClE;YAEA,MAAM,oBAAyB;gBAC7B,UAAU;gBACV,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO;gBAC5B,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,iBAAiB;YACnB;YAEA,wCAAwC;YACxC,IAAI,SAAS,cAAc;gBACzB,kBAAkB,IAAI,GAAG;oBACvB,OAAO,QAAQ,YAAY;gBAC7B;gBACA,QAAQ,KAAK,CAAC;gBAEd,4BAA4B;gBAC5B,MAAM,cAAc,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG,OAAO;gBACrE,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,kBAAkB,cAAc;gBAEtC,IAAI,mBAAmB,QAAQ;oBAC7B,qBAAqB;oBACrB,QAAQ,IAAI,CAAC;gBACf;YACF,OAAO;gBACL,QAAQ,IAAI,CACV;YAEJ;YAEA,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YAElC,IAAI,CAAC,kBAAkB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,iBAAiB;QACxB;IACF;IAEA;;GAEC,GACD,AAAO,UAAgB;QACrB,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,aAAa,CAAC,KAAK;QACxB,IAAI,CAAC,cAAc,CAAC,KAAK;QACzB,iBAAiB,QAAQ,GAAG;IAC9B;IAEA;;GAEC,GACD,AAAO,aAAmB;QACxB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG;IAC3B;IAEA;;GAEC,GACD,AAAO,KAAK,OAAsB,EAAE,KAAa,EAAE,IAAU,EAAQ;QACnE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,MAAM,0BAA0B,CAAC;YACxE;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;IAC1B;IAEA;;GAEC,GACD,AAAO,qBAAsC;QAC3C,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA;;GAEC,GACD,AAAO,cAAuB;QAC5B,OACE,IAAI,CAAC,eAAe,KAAK,eAAe,IAAI,CAAC,MAAM,EAAE,cAAc;IAEvE;IAEA;;GAEC,GACD,AAAO,SAAS,IAAY,EAAQ;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B,QAAQ,IAAI,CAAC,CAAC,iBAAiB,EAAE,KAAK,0BAA0B,CAAC;YACjE;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa;IAChC;IAEA;;GAEC,GACD,AAAO,UAAU,IAAY,EAAQ;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;YAC3B;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc;IACjC;IAEA;;GAEC,GACD,AAAO,cAAc,QAA0C,EAAc;QAC3E,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAExB,OAAO;YACL,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QAC7B;IACF;IAEA;;GAEC,GACD,AAAO,UACL,OAAsB,EACtB,KAAa,EACb,QAA0B,EACd;QACZ,MAAM,WAAW,GAAG,QAAQ,CAAC,EAAE,OAAO;QAEtC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW;YACrC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,IAAI;QACvC;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAW,GAAG,CAAC;QAEtC,sCAAsC;QACtC,IAAI,IAAI,CAAC,MAAM,EAAE,aAAa,OAAO;YACnC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;QACxB;QAEA,8BAA8B;QAC9B,OAAO;YACL,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;YACzC,IAAI,WAAW;gBACb,UAAU,MAAM,CAAC;gBACjB,IAAI,UAAU,IAAI,KAAK,GAAG;oBACxB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;gBAC5B;YACF;YAEA,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO;gBACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO;YACzB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,4BAAkC;QACxC,MAAM,sBAAsB,CAAA,GAAA,6IAAA,CAAA,yBAAsB,AAAD;QAEjD,QAAQ,GAAG,CAAC;QAEZ,2DAA2D;QAC3D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;QAChB;QAEA,2BAA2B;QAC3B,oBACG,UAAU,GACV,IAAI,CAAC,CAAA;YACJ,IAAI,SAAS;gBACX,QAAQ,GAAG,CACT;YAEF,gEAAgE;YAClE,OAAO;gBACL,QAAQ,KAAK,CAAC;gBACd,IAAI,CAAC,iBAAiB;YACxB;QACF,GACC,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,2BAA2B;YACzC,IAAI,CAAC,iBAAiB;QACxB;IACJ;IAEA;;GAEC,GACD,AAAQ,sBAA4B;QAClC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,KAAK,MAAM,CAAC,UAAU,UAAU,IAAI,IAAI,CAAC,aAAa,CAAE;YACtD,MAAM,GAAG,MAAM,GAAG,SAAS,KAAK,CAAC;YACjC,KAAK,MAAM,YAAY,UAAW;gBAChC,IAAI,OAAO;oBACT,IAAI,CAAC,MAAM,CAAE,EAAE,CAAC,OAAO;gBACzB;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,oBAA0B;QAChC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;YAC3D,QAAQ,KAAK,CAAC;YACd,IAAI,CAAC,kBAAkB,CAAC;YACxB;QACF;QAEA,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,CAAC,iBAAiB;QAEtB,MAAM,QACJ,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,GAAG;QAEpE,WAAW;YACT,QAAQ,IAAI,CACV,CAAC,yBAAyB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC;YAExF,IAAI,CAAC,OAAO;QACd,GAAG;IACL;IAEA;;GAEC,GACD,AAAQ,mBAAmB,KAAsB,EAAQ;QACvD,IAAI,IAAI,CAAC,eAAe,KAAK,OAAO;YAClC,IAAI,CAAC,eAAe,GAAG;YACvB,KAAK,MAAM,YAAY,IAAI,CAAC,cAAc,CAAE,SAAS;QACvD;IACF;IAEA;;GAEC,GACD,AAAQ,qBAA2B;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YACxB,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,iBAAiB,GAAG;YACzB,IAAI,CAAC,mBAAmB;QAC1B;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAA;YAC3B,QAAQ,IAAI,CAAC,2BAA2B;YACxC,IAAI,CAAC,kBAAkB,CAAC;YAExB,IAAI,WAAW,wBAAwB;gBACrC,6DAA6D;gBAC7D;YACF;YAEA,IAAI,CAAC,iBAAiB;QACxB;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAA;YAC9B,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,CAAC,kBAAkB,CAAC;YAExB,2CAA2C;YAC3C,IACE,MAAM,OAAO,EAAE,SAAS,qBACxB,MAAM,OAAO,EAAE,SAAS,YACxB,MAAM,OAAO,EAAE,SAAS,wBACxB,MAAM,OAAO,EAAE,SAAS,iBACxB;gBACA,QAAQ,IAAI,CACV;gBAEF,IAAI,CAAC,yBAAyB;YAChC,OAAO;gBACL,IAAI,CAAC,iBAAiB;YACxB;QACF;QAEA,mDAAmD;QACnD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAA;YAC3B,QAAQ,KAAK,CAAC,mCAAmC;YACjD,IAAI,CAAC,yBAAyB;QAChC;QAEA,gDAAgD;QAChD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,0BAA0B;YACvC,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,yBAAyB;QAChC;IACF;IAEA;;GAEC,GACD,AAAQ,4BAAkC;QACxC,MAAM,sBAAsB,CAAA,GAAA,6IAAA,CAAA,yBAAsB,AAAD;QAEjD,oBAAoB,SAAS,CAAC,CAAC,OAAO;YACpC,OAAQ;gBACN,KAAK;oBAA2B;wBAC9B,QAAQ,KAAK,CACX;wBAEF,IAAI,CAAC,UAAU;wBACf,IAAI,CAAC,kBAAkB,CAAC;wBACxB;oBACF;gBAEA,KAAK;oBAAkB;wBACrB,QAAQ,KAAK,CACX;wBAEF;oBACF;gBAEA,KAAK;oBAAmB;wBACtB,QAAQ,GAAG,CACT;wBAEF,6DAA6D;wBAC7D,IAAI,IAAI,CAAC,MAAM,EAAE;4BACf,IAAI,CAAC,MAAM,CAAC,UAAU;4BACtB,IAAI,CAAC,MAAM,GAAG;wBAChB;wBACA,6BAA6B;wBAC7B,WAAW,IAAM,IAAI,CAAC,OAAO,IAAI;wBACjC;oBACF;YACF;QACF;IACF;AACF;AAKO,MAAM,sBAAsB,CACjC;IAEA,OAAO,iBAAiB,WAAW,CAAC;AACtC;AAKO,MAAM,oBAAoB;IAC/B,MAAM,UAAU;IAChB,OAAO;QACL,iBAAiB,QAAQ,kBAAkB;QAC3C,aAAa,QAAQ,WAAW;IAClC;AACF", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/api/useSmartQuery.ts"], "sourcesContent": ["/**\r\n * @file Smart Query Hook with WebSocket Integration\r\n * Automatically disables polling when WebSocket is connected\r\n * Follows modern best practices for real-time data management\r\n * @module hooks/useSmartQuery\r\n */\r\n\r\nimport type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query';\r\n\r\nimport { useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { useEffect, useState } from 'react';\r\n\r\nimport type { DomainChannel } from '../../lib/services/WebSocketManager';\r\n\r\nimport { getWebSocketManager } from '../../lib/services/WebSocketManager';\r\n\r\n/**\r\n * Mapping of domain channels to Socket.IO room names\r\n * This ensures the frontend joins the correct rooms that the backend emits events to\r\n */\r\nconst CHANNEL_ROOM_MAPPING: Record<DomainChannel, string> = {\r\n  crud: 'entity-updates',\r\n  notifications: 'notifications-monitoring',\r\n  reliability: 'reliability-monitoring',\r\n  system: 'system-monitoring',\r\n} as const;\r\n\r\n/**\r\n * Smart query configuration for WebSocket integration\r\n * @template T - The data type returned by the query function\r\n */\r\nexport interface SmartQueryConfig<T = unknown> {\r\n  /**\r\n   * Domain channel for WebSocket events\r\n   * Automatically maps to appropriate Socket.IO room via CHANNEL_ROOM_MAPPING\r\n   */\r\n  channel: DomainChannel;\r\n  /** Whether to enable fallback polling when WebSocket is disconnected */\r\n  enableFallback?: boolean;\r\n  /** Whether to enable WebSocket integration and room joining */\r\n  enableWebSocket?: boolean;\r\n  /** Events that should trigger data refetch when received via WebSocket */\r\n  events: string[];\r\n  /** Fallback polling interval when WebSocket is disconnected (ms) */\r\n  fallbackInterval?: number;\r\n}\r\n\r\n/**\r\n * Hook for CRUD operations with smart real-time updates\r\n */\r\nexport function useCrudQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  entityType: string,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'crud',\r\n      events: [\r\n        `${entityType}:created`,\r\n        `${entityType}:updated`,\r\n        `${entityType}:deleted`,\r\n        `refresh:${entityType}`,\r\n      ],\r\n      fallbackInterval: 30_000,\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Hook for system notifications with smart real-time updates\r\n */\r\nexport function useNotificationQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'notifications',\r\n      events: ['notification-created', 'notification-updated'],\r\n      fallbackInterval: 60_000, // 1 minute for notifications\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Hook for reliability monitoring with smart real-time updates\r\n */\r\nexport function useReliabilityQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  monitoringType: 'alerts' | 'circuit-breakers' | 'health' | 'metrics',\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  // Increased intervals to reduce aggressive polling and cancellations\r\n  const intervalMap = {\r\n    alerts: 30_000, // 30 seconds for alerts (was 10s)\r\n    'circuit-breakers': 60_000, // 60 seconds for circuit breakers (was 30s)\r\n    health: 45_000, // 45 seconds for health (was 15s)\r\n    metrics: 60_000, // 60 seconds for metrics (was 30s)\r\n  };\r\n\r\n  const webSocketManager = getWebSocketManager();\r\n\r\n  // Join reliability monitoring room when WebSocket is connected\r\n  useEffect(() => {\r\n    if (webSocketManager.isConnected()) {\r\n      console.debug(\r\n        `[ReliabilityQuery] Joining reliability-monitoring room for ${monitoringType}`\r\n      );\r\n      webSocketManager.joinRoom('reliability-monitoring');\r\n    }\r\n\r\n    // Subscribe to connection state changes to join room when connected\r\n    const unsubscribe = webSocketManager.onStateChange(state => {\r\n      if (state === 'connected') {\r\n        console.debug(\r\n          `[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ${monitoringType}`\r\n        );\r\n        webSocketManager.joinRoom('reliability-monitoring');\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      unsubscribe();\r\n      // Leave room when component unmounts\r\n      if (webSocketManager.isConnected()) {\r\n        webSocketManager.leaveRoom('reliability-monitoring');\r\n      }\r\n    };\r\n  }, [webSocketManager, monitoringType]);\r\n\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'reliability',\r\n      events: [\r\n        `${monitoringType}-update`,\r\n        `${monitoringType}-created`,\r\n        `${monitoringType}-resolved`,\r\n      ],\r\n      fallbackInterval: intervalMap[monitoringType],\r\n    },\r\n    options\r\n  );\r\n}\r\n\r\n/**\r\n * Smart Query Hook with Socket.IO Room Management\r\n *\r\n * Combines React Query with WebSocket real-time updates and automatic Socket.IO room joining.\r\n * This hook automatically:\r\n * - Joins the appropriate Socket.IO room based on the domain channel\r\n * - Subscribes to WebSocket events for real-time data updates\r\n * - Switches between WebSocket and polling based on connection state\r\n * - Handles room cleanup when component unmounts\r\n *\r\n * **Room Mapping:**\r\n * - `crud` channel → `entity-updates` room\r\n * - `reliability` channel → `reliability-monitoring` room\r\n * - `notifications` channel → `notifications-monitoring` room\r\n * - `system` channel → `system-monitoring` room\r\n *\r\n * @template T - The data type returned by the query function\r\n * @template E - The error type for failed queries\r\n * @param queryKey - React Query key for caching and invalidation\r\n * @param queryFn - Data fetching function that returns a Promise<T>\r\n * @param config - Smart query configuration including channel and events\r\n * @param options - Additional React Query options (merged with smart defaults)\r\n * @returns Enhanced query result with WebSocket integration and connection state\r\n */\r\nexport function useSmartQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  config: SmartQueryConfig<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n): UseQueryResult<T, E> & {\r\n  isUsingFallback: boolean;\r\n  isWebSocketConnected: boolean;\r\n} {\r\n  const {\r\n    channel,\r\n    enableFallback = true,\r\n    enableWebSocket = true,\r\n    events,\r\n    fallbackInterval = 30_000,\r\n  } = config;\r\n\r\n  const [isWebSocketConnected, setIsWebSocketConnected] = useState(false);\r\n  const webSocketManager = getWebSocketManager();\r\n\r\n  // Track WebSocket connection state\r\n  useEffect(() => {\r\n    const updateConnectionState = () => {\r\n      setIsWebSocketConnected(webSocketManager.isConnected());\r\n    };\r\n\r\n    // Initial state\r\n    updateConnectionState();\r\n\r\n    // Subscribe to state changes\r\n    const unsubscribe = webSocketManager.onStateChange(updateConnectionState);\r\n\r\n    return unsubscribe;\r\n  }, [webSocketManager]);\r\n\r\n  // Determine if we should use fallback polling\r\n  const isUsingFallback =\r\n    enableFallback && (!enableWebSocket || !isWebSocketConnected);\r\n\r\n  // Configure React Query options based on WebSocket state\r\n  const queryOptions: UseQueryOptions<T, E> = {\r\n    // Longer cache time for better performance\r\n    gcTime: 10 * 60 * 1000, // 10 minutes\r\n    queryFn,\r\n    queryKey,\r\n    // Disable polling when WebSocket is connected\r\n    refetchInterval: isUsingFallback ? fallbackInterval : false,\r\n    refetchOnReconnect: true, // Always refetch on network reconnect\r\n    // Enable background refetch only when using fallback\r\n    refetchOnWindowFocus: isUsingFallback,\r\n    // Shorter stale time when using WebSocket (real-time updates)\r\n    staleTime: isWebSocketConnected ? 0 : 30_000,\r\n    ...options,\r\n  };\r\n\r\n  const queryClient = useQueryClient();\r\n  const queryResult = useQuery<T, E>(queryOptions);\r\n\r\n  // Manage Socket.IO room joining/leaving based on channel\r\n  useEffect(() => {\r\n    if (!enableWebSocket || !isWebSocketConnected) {\r\n      return;\r\n    }\r\n\r\n    const roomName = CHANNEL_ROOM_MAPPING[channel];\r\n    if (!roomName) {\r\n      console.warn(\r\n        `[SmartQuery] No room mapping found for channel: ${channel}`\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Join the appropriate room for this channel\r\n    try {\r\n      webSocketManager.joinRoom(roomName);\r\n      console.log(\r\n        `[SmartQuery] Joined room: ${roomName} for channel: ${channel}`\r\n      );\r\n    } catch (error) {\r\n      console.error(`[SmartQuery] Failed to join room ${roomName}:`, error);\r\n    }\r\n\r\n    // Cleanup: leave room when component unmounts or dependencies change\r\n    return () => {\r\n      try {\r\n        webSocketManager.leaveRoom(roomName);\r\n        console.log(\r\n          `[SmartQuery] Left room: ${roomName} for channel: ${channel}`\r\n        );\r\n      } catch (error) {\r\n        console.error(`[SmartQuery] Failed to leave room ${roomName}:`, error);\r\n      }\r\n    };\r\n  }, [enableWebSocket, isWebSocketConnected, channel, webSocketManager]);\r\n\r\n  // Subscribe to WebSocket events for real-time updates\r\n  useEffect(() => {\r\n    if (!enableWebSocket || !isWebSocketConnected || events.length === 0) {\r\n      return;\r\n    }\r\n\r\n    const unsubscribers: (() => void)[] = [];\r\n\r\n    // Subscribe to each event\r\n    for (const event of events) {\r\n      const unsubscribe = webSocketManager.subscribe(\r\n        channel,\r\n        event,\r\n        (data: unknown) => {\r\n          console.log(\r\n            `[SmartQuery] WebSocket event received: ${channel}:${event}`,\r\n            data\r\n          );\r\n\r\n          // Invalidate the specific query to trigger refetch\r\n          queryClient.invalidateQueries({ queryKey });\r\n        }\r\n      );\r\n\r\n      unsubscribers.push(unsubscribe);\r\n    }\r\n\r\n    return () => {\r\n      for (const unsubscribe of unsubscribers) unsubscribe();\r\n    };\r\n  }, [\r\n    enableWebSocket,\r\n    isWebSocketConnected,\r\n    events,\r\n    channel,\r\n    webSocketManager,\r\n    queryClient,\r\n    queryKey,\r\n  ]);\r\n\r\n  return {\r\n    ...queryResult,\r\n    isUsingFallback,\r\n    isWebSocketConnected,\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for system-wide events with smart real-time updates\r\n */\r\nexport function useSystemQuery<T = unknown, E = Error>(\r\n  queryKey: unknown[],\r\n  queryFn: () => Promise<T>,\r\n  options?: Omit<UseQueryOptions<T, E>, 'queryFn' | 'queryKey'>\r\n) {\r\n  return useSmartQuery(\r\n    queryKey,\r\n    queryFn,\r\n    {\r\n      channel: 'system',\r\n      events: ['system-update', 'config-changed'],\r\n      fallbackInterval: 120_000, // 2 minutes for system events\r\n    },\r\n    options\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;AAID;AAAA;AACA;AAIA;;;;AAEA;;;CAGC,GACD,MAAM,uBAAsD;IAC1D,MAAM;IACN,eAAe;IACf,aAAa;IACb,QAAQ;AACV;AAyBO,SAAS,aACd,QAAmB,EACnB,OAAyB,EACzB,UAAkB,EAClB,OAA6D;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YACN,GAAG,WAAW,QAAQ,CAAC;YACvB,GAAG,WAAW,QAAQ,CAAC;YACvB,GAAG,WAAW,QAAQ,CAAC;YACvB,CAAC,QAAQ,EAAE,YAAY;SACxB;QACD,kBAAkB;IACpB,GACA;AAEJ;AAKO,SAAS,qBACd,QAAmB,EACnB,OAAyB,EACzB,OAA6D;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YAAC;YAAwB;SAAuB;QACxD,kBAAkB;IACpB,GACA;AAEJ;AAKO,SAAS,oBACd,QAAmB,EACnB,OAAyB,EACzB,cAAoE,EACpE,OAA6D;IAE7D,qEAAqE;IACrE,MAAM,cAAc;QAClB,QAAQ;QACR,oBAAoB;QACpB,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,mBAAmB,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD;IAE3C,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,WAAW,IAAI;YAClC,QAAQ,KAAK,CACX,CAAC,2DAA2D,EAAE,gBAAgB;YAEhF,iBAAiB,QAAQ,CAAC;QAC5B;QAEA,oEAAoE;QACpE,MAAM,cAAc,iBAAiB,aAAa,CAAC,CAAA;YACjD,IAAI,UAAU,aAAa;gBACzB,QAAQ,KAAK,CACX,CAAC,gFAAgF,EAAE,gBAAgB;gBAErG,iBAAiB,QAAQ,CAAC;YAC5B;QACF;QAEA,OAAO;YACL;YACA,qCAAqC;YACrC,IAAI,iBAAiB,WAAW,IAAI;gBAClC,iBAAiB,SAAS,CAAC;YAC7B;QACF;IACF,GAAG;QAAC;QAAkB;KAAe;IAErC,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YACN,GAAG,eAAe,OAAO,CAAC;YAC1B,GAAG,eAAe,QAAQ,CAAC;YAC3B,GAAG,eAAe,SAAS,CAAC;SAC7B;QACD,kBAAkB,WAAW,CAAC,eAAe;IAC/C,GACA;AAEJ;AA0BO,SAAS,cACd,QAAmB,EACnB,OAAyB,EACzB,MAA2B,EAC3B,OAA6D;IAK7D,MAAM,EACJ,OAAO,EACP,iBAAiB,IAAI,EACrB,kBAAkB,IAAI,EACtB,MAAM,EACN,mBAAmB,MAAM,EAC1B,GAAG;IAEJ,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,mBAAmB,CAAA,GAAA,0IAAA,CAAA,sBAAmB,AAAD;IAE3C,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,wBAAwB;YAC5B,wBAAwB,iBAAiB,WAAW;QACtD;QAEA,gBAAgB;QAChB;QAEA,6BAA6B;QAC7B,MAAM,cAAc,iBAAiB,aAAa,CAAC;QAEnD,OAAO;IACT,GAAG;QAAC;KAAiB;IAErB,8CAA8C;IAC9C,MAAM,kBACJ,kBAAkB,CAAC,CAAC,mBAAmB,CAAC,oBAAoB;IAE9D,yDAAyD;IACzD,MAAM,eAAsC;QAC1C,2CAA2C;QAC3C,QAAQ,KAAK,KAAK;QAClB;QACA;QACA,8CAA8C;QAC9C,iBAAiB,kBAAkB,mBAAmB;QACtD,oBAAoB;QACpB,qDAAqD;QACrD,sBAAsB;QACtB,8DAA8D;QAC9D,WAAW,uBAAuB,IAAI;QACtC,GAAG,OAAO;IACZ;IAEA,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,cAAc,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAQ;IAEnC,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,sBAAsB;YAC7C;QACF;QAEA,MAAM,WAAW,oBAAoB,CAAC,QAAQ;QAC9C,IAAI,CAAC,UAAU;YACb,QAAQ,IAAI,CACV,CAAC,gDAAgD,EAAE,SAAS;YAE9D;QACF;QAEA,6CAA6C;QAC7C,IAAI;YACF,iBAAiB,QAAQ,CAAC;YAC1B,QAAQ,GAAG,CACT,CAAC,0BAA0B,EAAE,SAAS,cAAc,EAAE,SAAS;QAEnE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,SAAS,CAAC,CAAC,EAAE;QACjE;QAEA,qEAAqE;QACrE,OAAO;YACL,IAAI;gBACF,iBAAiB,SAAS,CAAC;gBAC3B,QAAQ,GAAG,CACT,CAAC,wBAAwB,EAAE,SAAS,cAAc,EAAE,SAAS;YAEjE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;YAClE;QACF;IACF,GAAG;QAAC;QAAiB;QAAsB;QAAS;KAAiB;IAErE,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,OAAO,MAAM,KAAK,GAAG;YACpE;QACF;QAEA,MAAM,gBAAgC,EAAE;QAExC,0BAA0B;QAC1B,KAAK,MAAM,SAAS,OAAQ;YAC1B,MAAM,cAAc,iBAAiB,SAAS,CAC5C,SACA,OACA,CAAC;gBACC,QAAQ,GAAG,CACT,CAAC,uCAAuC,EAAE,QAAQ,CAAC,EAAE,OAAO,EAC5D;gBAGF,mDAAmD;gBACnD,YAAY,iBAAiB,CAAC;oBAAE;gBAAS;YAC3C;YAGF,cAAc,IAAI,CAAC;QACrB;QAEA,OAAO;YACL,KAAK,MAAM,eAAe,cAAe;QAC3C;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO;QACL,GAAG,WAAW;QACd;QACA;IACF;AACF;AAKO,SAAS,eACd,QAAmB,EACnB,OAAyB,EACzB,OAA6D;IAE7D,OAAO,cACL,UACA,SACA;QACE,SAAS;QACT,QAAQ;YAAC;YAAiB;SAAiB;QAC3C,kBAAkB;IACpB,GACA;AAEJ", "debugId": null}}, {"offset": {"line": 1070, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/transformers/delegationEnrichment.ts"], "sourcesContent": ["/**\r\n * @file Delegation enrichment transformer following established patterns\r\n * @description Handles the enrichment of delegation data with employee and vehicle details\r\n * @module transformers/delegationEnrichment\r\n */\r\n\r\nimport type {\r\n  Delegation,\r\n  DelegationDriver,\r\n  DelegationEscort,\r\n  DelegationVehicleAssignment,\r\n  Employee,\r\n  Vehicle,\r\n} from '../types/domain';\r\n\r\n/**\r\n * Transformer class for enriching delegation data with related entities\r\n * Follows the same pattern as other transformers in the codebase\r\n */\r\nexport class DelegationEnrichmentTransformer {\r\n  /**\r\n   * Main enrichment method that combines delegation data with employee and vehicle details\r\n   * @param delegation - Base delegation data\r\n   * @param employees - Array of employees\r\n   * @param vehicles - Array of vehicles\r\n   * @returns Fully enriched delegation\r\n   */\r\n  static enrich(\r\n    delegation: Delegation,\r\n    employees: Employee[],\r\n    vehicles: Vehicle[]\r\n  ): Delegation {\r\n    const { employeeMap, vehicleMap } = this.createLookupMaps(\r\n      employees,\r\n      vehicles\r\n    );\r\n\r\n    return {\r\n      ...delegation,\r\n      drivers: this.enrichDrivers(delegation.drivers, employeeMap) ?? [],\r\n      escorts: this.enrichEscorts(delegation.escorts, employeeMap) ?? [],\r\n      vehicles: this.enrichVehicles(delegation.vehicles, vehicleMap) ?? [],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Creates optimized lookup maps for O(1) performance\r\n   * @param employees - Array of employees\r\n   * @param vehicles - Array of vehicles\r\n   * @returns Object containing employee and vehicle maps\r\n   */\r\n  private static createLookupMaps(employees: Employee[], vehicles: Vehicle[]) {\r\n    return {\r\n      employeeMap: new Map(employees.map(emp => [emp.id, emp])),\r\n      vehicleMap: new Map(vehicles.map(veh => [veh.id, veh])),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Enriches driver assignments with employee details\r\n   * @param drivers - Array of driver assignments\r\n   * @param employeeMap - Map of employees for O(1) lookup\r\n   * @returns Enriched driver assignments\r\n   */\r\n  private static enrichDrivers(\r\n    drivers: DelegationDriver[] | undefined,\r\n    employeeMap: Map<number, Employee>\r\n  ): DelegationDriver[] | undefined {\r\n    return drivers?.map(driver => {\r\n      const employee =\r\n        driver.employee || employeeMap.get(Number(driver.employeeId));\r\n      return {\r\n        ...driver,\r\n        ...(employee && { employee }),\r\n      };\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Enriches escort assignments with employee details\r\n   * @param escorts - Array of escort assignments\r\n   * @param employeeMap - Map of employees for O(1) lookup\r\n   * @returns Enriched escort assignments\r\n   */\r\n  private static enrichEscorts(\r\n    escorts: DelegationEscort[] | undefined,\r\n    employeeMap: Map<number, Employee>\r\n  ): DelegationEscort[] | undefined {\r\n    return escorts?.map(escort => {\r\n      const employee =\r\n        escort.employee || employeeMap.get(Number(escort.employeeId));\r\n      return {\r\n        ...escort,\r\n        ...(employee && { employee }),\r\n      };\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Enriches vehicle assignments with vehicle details\r\n   * @param vehicles - Array of vehicle assignments\r\n   * @param vehicleMap - Map of vehicles for O(1) lookup\r\n   * @returns Enriched vehicle assignments\r\n   */\r\n  private static enrichVehicles(\r\n    vehicles: DelegationVehicleAssignment[] | undefined,\r\n    vehicleMap: Map<number, Vehicle>\r\n  ): DelegationVehicleAssignment[] | undefined {\r\n    return vehicles?.map(vehicleAssignment => {\r\n      const vehicle =\r\n        vehicleAssignment.vehicle ||\r\n        vehicleMap.get(vehicleAssignment.vehicleId);\r\n      return {\r\n        ...vehicleAssignment,\r\n        ...(vehicle && { vehicle }),\r\n      };\r\n    });\r\n  }\r\n}\r\n\r\n// Export the main enrichment function for backward compatibility\r\nexport const enrichDelegation = (\r\n  delegation: Delegation,\r\n  employees: Employee[],\r\n  vehicles: Vehicle[]\r\n): Delegation => {\r\n  return DelegationEnrichmentTransformer.enrich(\r\n    delegation,\r\n    employees,\r\n    vehicles\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAeM,MAAM;IACX;;;;;;GAMC,GACD,OAAO,OACL,UAAsB,EACtB,SAAqB,EACrB,QAAmB,EACP;QACZ,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,gBAAgB,CACvD,WACA;QAGF,OAAO;YACL,GAAG,UAAU;YACb,SAAS,IAAI,CAAC,aAAa,CAAC,WAAW,OAAO,EAAE,gBAAgB,EAAE;YAClE,SAAS,IAAI,CAAC,aAAa,CAAC,WAAW,OAAO,EAAE,gBAAgB,EAAE;YAClE,UAAU,IAAI,CAAC,cAAc,CAAC,WAAW,QAAQ,EAAE,eAAe,EAAE;QACtE;IACF;IAEA;;;;;GAKC,GACD,OAAe,iBAAiB,SAAqB,EAAE,QAAmB,EAAE;QAC1E,OAAO;YACL,aAAa,IAAI,IAAI,UAAU,GAAG,CAAC,CAAA,MAAO;oBAAC,IAAI,EAAE;oBAAE;iBAAI;YACvD,YAAY,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,MAAO;oBAAC,IAAI,EAAE;oBAAE;iBAAI;QACvD;IACF;IAEA;;;;;GAKC,GACD,OAAe,cACb,OAAuC,EACvC,WAAkC,EACF;QAChC,OAAO,SAAS,IAAI,CAAA;YAClB,MAAM,WACJ,OAAO,QAAQ,IAAI,YAAY,GAAG,CAAC,OAAO,OAAO,UAAU;YAC7D,OAAO;gBACL,GAAG,MAAM;gBACT,GAAI,YAAY;oBAAE;gBAAS,CAAC;YAC9B;QACF;IACF;IAEA;;;;;GAKC,GACD,OAAe,cACb,OAAuC,EACvC,WAAkC,EACF;QAChC,OAAO,SAAS,IAAI,CAAA;YAClB,MAAM,WACJ,OAAO,QAAQ,IAAI,YAAY,GAAG,CAAC,OAAO,OAAO,UAAU;YAC7D,OAAO;gBACL,GAAG,MAAM;gBACT,GAAI,YAAY;oBAAE;gBAAS,CAAC;YAC9B;QACF;IACF;IAEA;;;;;GAKC,GACD,OAAe,eACb,QAAmD,EACnD,UAAgC,EACW;QAC3C,OAAO,UAAU,IAAI,CAAA;YACnB,MAAM,UACJ,kBAAkB,OAAO,IACzB,WAAW,GAAG,CAAC,kBAAkB,SAAS;YAC5C,OAAO;gBACL,GAAG,iBAAiB;gBACpB,GAAI,WAAW;oBAAE;gBAAQ,CAAC;YAC5B;QACF;IACF;AACF;AAGO,MAAM,mBAAmB,CAC9B,YACA,WACA;IAEA,OAAO,gCAAgC,MAAM,CAC3C,YACA,WACA;AAEJ", "debugId": null}}, {"offset": {"line": 1169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/delegationQueries.ts"], "sourcesContent": ["/**\r\n * @file Delegation query configurations following Single Responsibility Principle\r\n * @description Centralized query configurations for delegation-related data fetching\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport type { Delegation } from '../../types/domain';\r\n\r\nimport {\r\n  delegationApiService,\r\n  employeeApiService,\r\n  vehicleApiService,\r\n} from '../../api/services/apiServiceFactory'; // Use centralized services\r\nimport { DelegationTransformer } from '../../transformers/delegationTransformer';\r\n\r\n/**\r\n * Query keys for delegation-related queries\r\n */\r\nexport const delegationQueryKeys = {\r\n  all: ['delegations'] as const,\r\n  detail: (id: string) => ['delegations', id] as const,\r\n  withAssignments: (id: string) =>\r\n    ['delegations', id, 'with-assignments'] as const,\r\n};\r\n\r\n/**\r\n * Creates query configuration for fetching a single delegation\r\n */\r\nexport const createDelegationQuery = (id: string) => ({\r\n  enabled: !!id,\r\n  queryFn: () => delegationApiService.getById(id),\r\n  queryKey: delegationQueryKeys.detail(id),\r\n  staleTime: 5 * 60 * 1000, // 5 minutes\r\n});\r\n\r\n/**\r\n * Creates query configuration for fetching all employees\r\n */\r\nexport const createEmployeesQuery = () => ({\r\n  queryFn: () => employeeApiService.getAll(),\r\n  queryKey: ['employees'] as const,\r\n  staleTime: 10 * 60 * 1000, // 10 minutes - employees change less frequently\r\n});\r\n\r\n/**\r\n * Creates query configuration for fetching all vehicles\r\n */\r\nexport const createVehiclesQuery = () => ({\r\n  queryFn: () => vehicleApiService.getAll(),\r\n  queryKey: ['vehicles'] as const,\r\n  staleTime: 10 * 60 * 1000, // 10 minutes - vehicles change less frequently\r\n});\r\n\r\n/**\r\n * Creates parallel query configurations for delegation with assignments\r\n */\r\nexport const createDelegationWithAssignmentsQueries = (id: string) => [\r\n  createDelegationQuery(id),\r\n  createEmployeesQuery(),\r\n  createVehiclesQuery(),\r\n];\r\n\r\n/**\r\n * Standard query options for delegation queries\r\n */\r\nexport const delegationQueryOptions: Partial<\r\n  UseQueryOptions<Delegation, Error>\r\n> = {\r\n  gcTime: 10 * 60 * 1000, // 10 minutes garbage collection time\r\n  retry: 3,\r\n  retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30_000),\r\n  staleTime: 5 * 60 * 1000,\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAMD,gTAI+C,2BAA2B;AAJ1E;;AAUO,MAAM,sBAAsB;IACjC,KAAK;QAAC;KAAc;IACpB,QAAQ,CAAC,KAAe;YAAC;YAAe;SAAG;IAC3C,iBAAiB,CAAC,KAChB;YAAC;YAAe;YAAI;SAAmB;AAC3C;AAKO,MAAM,wBAAwB,CAAC,KAAe,CAAC;QACpD,SAAS,CAAC,CAAC;QACX,SAAS,IAAM,wIAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC;QAC5C,UAAU,oBAAoB,MAAM,CAAC;QACrC,WAAW,IAAI,KAAK;IACtB,CAAC;AAKM,MAAM,uBAAuB,IAAM,CAAC;QACzC,SAAS,IAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM;QACxC,UAAU;YAAC;SAAY;QACvB,WAAW,KAAK,KAAK;IACvB,CAAC;AAKM,MAAM,sBAAsB,IAAM,CAAC;QACxC,SAAS,IAAM,wIAAA,CAAA,oBAAiB,CAAC,MAAM;QACvC,UAAU;YAAC;SAAW;QACtB,WAAW,KAAK,KAAK;IACvB,CAAC;AAKM,MAAM,yCAAyC,CAAC,KAAe;QACpE,sBAAsB;QACtB;QACA;KACD;AAKM,MAAM,yBAET;IACF,QAAQ,KAAK,KAAK;IAClB,OAAO;IACP,YAAY,CAAA,eAAgB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;IAC/D,WAAW,IAAI,KAAK;AACtB", "debugId": null}}, {"offset": {"line": 1234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/useDelegations.ts"], "sourcesContent": ["/**\r\n * @file TanStack Query hooks for Delegation-related data.\r\n * These hooks manage fetching, caching, and mutating delegation data,\r\n * integrating with the DelegationApiService and DelegationTransformer.\r\n * @module stores/queries/useDelegations\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport { useMutation, useQueries, useQueryClient } from '@tanstack/react-query';\r\nimport { useCallback, useMemo } from 'react';\r\n\r\nimport { undefinedToNull } from '@/lib/utils/typeHelpers';\r\n\r\n// import { DelegationFormData } from '../../schemas/delegationSchemas'; // Not directly used by hooks' public API\r\nimport type { UpdateDelegationRequest } from '../../types/api'; // For useUpdateDelegation\r\nimport type {\r\n  CreateDelegationData,\r\n  Delegation,\r\n  DelegationStatusPrisma,\r\n  FlightDetails,\r\n  // DelegationEscort, // Not directly used in optimistic updates in a way that needs separate import here\r\n  // DelegationDriver,\r\n  // DelegationVehicleAssignment,\r\n} from '../../types/domain';\r\n\r\nimport { useCrudQuery } from '../../../hooks/api/useSmartQuery'; // Adjusted import path\r\nimport { delegationApiService } from '../../api/services/apiServiceFactory'; // Use centralized service\r\nimport { enrichDelegation } from '../../transformers/delegationEnrichment';\r\nimport { DelegationTransformer } from '../../transformers/delegationTransformer';\r\nimport {\r\n  createDelegationWithAssignmentsQueries,\r\n  delegationQueryKeys,\r\n} from './delegationQueries';\r\n\r\nexport const useDelegations = (\r\n  options?: Omit<\r\n    UseQueryOptions<Delegation[], Error>,\r\n    'queryFn' | 'queryKey'\r\n  > & { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<Delegation[], Error>(\r\n    [...delegationQueryKeys.all], // queryKey - spread for mutability\r\n    async () => {\r\n      const result = await delegationApiService.getAll();\r\n      return result.data;\r\n    },\r\n    'delegation', // entityType\r\n    {\r\n      staleTime: 0, // Existing staleTime\r\n      ...options, // Spread additional options\r\n    }\r\n  );\r\n};\r\n\r\nexport const useDelegation = (id: string) => {\r\n  return useCrudQuery<Delegation, Error>(\r\n    [...delegationQueryKeys.detail(id)],\r\n    async () => {\r\n      return await delegationApiService.getById(id);\r\n    },\r\n    'delegation', // entityType for WebSocket events\r\n    {\r\n      enabled: !!id,\r\n      staleTime: 5 * 60 * 1000,\r\n    }\r\n  );\r\n};\r\n\r\n// ✅ OPTIMIZED: Fast parallel data fetching for delegation with assignments\r\nexport const useDelegationWithAssignments = (id: string) => {\r\n  // Execute all queries in parallel using useQueries for maximum performance\r\n  const results = useQueries({\r\n    queries: createDelegationWithAssignmentsQueries(id),\r\n  });\r\n\r\n  const [delegationQuery, employeesQuery, vehiclesQuery] = results;\r\n\r\n  // Compute enriched delegation when all data is available\r\n  const enrichedDelegation = useMemo(() => {\r\n    if (\r\n      !delegationQuery?.data ||\r\n      !employeesQuery?.data ||\r\n      !vehiclesQuery?.data\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // ✅ PRODUCTION FIX: delegationQuery.data is already transformed by the service layer\r\n      // No need to apply DelegationTransformer.fromApi() again\r\n      const delegation = delegationQuery.data as Delegation;\r\n      return enrichDelegation(\r\n        delegation,\r\n        employeesQuery.data as any,\r\n        vehiclesQuery.data as any\r\n      );\r\n    } catch (error) {\r\n      console.error('Error enriching delegation data:', error);\r\n      throw error;\r\n    }\r\n  }, [delegationQuery?.data, employeesQuery?.data, vehiclesQuery?.data]);\r\n\r\n  // ✅ PRODUCTION FIX: Memoize refetch function to prevent infinite re-renders\r\n  const refetch = useCallback(() => {\r\n    delegationQuery?.refetch();\r\n    employeesQuery?.refetch();\r\n    vehiclesQuery?.refetch();\r\n  }, [\r\n    delegationQuery?.refetch,\r\n    employeesQuery?.refetch,\r\n    vehiclesQuery?.refetch,\r\n  ]);\r\n\r\n  // Return combined state with optimized loading states\r\n  return {\r\n    data: enrichedDelegation,\r\n    error:\r\n      delegationQuery?.error || employeesQuery?.error || vehiclesQuery?.error,\r\n    isError:\r\n      delegationQuery?.isError ||\r\n      employeesQuery?.isError ||\r\n      vehiclesQuery?.isError,\r\n    isLoading:\r\n      delegationQuery?.isLoading ||\r\n      employeesQuery?.isLoading ||\r\n      vehiclesQuery?.isLoading,\r\n    isPending:\r\n      delegationQuery?.isPending ||\r\n      employeesQuery?.isPending ||\r\n      vehiclesQuery?.isPending,\r\n    refetch,\r\n  };\r\n};\r\n\r\n// ✅ BACKWARD COMPATIBILITY: Alias for the optimized hook\r\nexport const useDelegationEnriched = useDelegationWithAssignments;\r\n\r\nexport const useCreateDelegation = () => {\r\n  const queryClient = useQueryClient();\r\n  interface CreateContext {\r\n    previousDelegations: Delegation[] | undefined;\r\n  }\r\n\r\n  return useMutation<Delegation, Error, CreateDelegationData, CreateContext>({\r\n    mutationFn: async (delegationData: CreateDelegationData) => {\r\n      const apiPayload = DelegationTransformer.toCreateRequest(delegationData);\r\n      // ✅ PRODUCTION FIX: delegationApiService.create() already applies transformation\r\n      return await delegationApiService.create(apiPayload);\r\n    },\r\n    onError: (err, _delegationData, context) => {\r\n      if (context?.previousDelegations) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.all,\r\n          context.previousDelegations\r\n        );\r\n      }\r\n      console.error('Failed to create delegation:', err);\r\n      // Invalidate to refetch correct data on error\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n    onMutate: async (delegationData: CreateDelegationData) => {\r\n      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });\r\n      const previousDelegations = queryClient.getQueryData<Delegation[]>(\r\n        delegationQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation[]>(\r\n        delegationQueryKeys.all,\r\n        (old = []) => {\r\n          const tempId = `optimistic-${Date.now()}`;\r\n          const now = new Date().toISOString();\r\n\r\n          const optimisticArrivalFlight: FlightDetails | null =\r\n            delegationData.flightArrivalDetails\r\n              ? {\r\n                  id: `optimistic-flight-arr-${Date.now()}`,\r\n                  ...delegationData.flightArrivalDetails,\r\n                }\r\n              : null;\r\n\r\n          const optimisticDepartureFlight: FlightDetails | null =\r\n            delegationData.flightDepartureDetails\r\n              ? {\r\n                  id: `optimistic-flight-dep-${Date.now() + 1}`,\r\n                  ...delegationData.flightDepartureDetails,\r\n                }\r\n              : null;\r\n\r\n          const optimisticDelegates: Delegation['delegates'] =\r\n            delegationData.delegates?.map((d, index) => ({\r\n              id: `optimistic-delegate-${tempId}-${index}`,\r\n              name: d.name, // Use d.name directly\r\n              notes: d.notes ?? null,\r\n              title: d.title, // Use d.title directly\r\n            })) || [];\r\n\r\n          const optimisticDelegation: Delegation = {\r\n            arrivalFlight: optimisticArrivalFlight ?? null,\r\n            createdAt: now,\r\n            delegates: optimisticDelegates,\r\n            departureFlight: optimisticDepartureFlight ?? null,\r\n            drivers:\r\n              delegationData.drivers?.map(d => ({\r\n                createdAt: now, // Placeholder timestamp\r\n                createdBy: null, // Placeholder\r\n                delegationId: tempId, // Link to optimistic delegation\r\n                employeeId: d.employeeId, // Keep as number\r\n                id: `optimistic-driver-${tempId}-${d.employeeId}`, // Placeholder ID\r\n                notes: d.notes ?? null, // Include notes if available in CreateDelegationData\r\n                updatedAt: now, // Placeholder timestamp\r\n              })) || [],\r\n            durationFrom: delegationData.durationFrom,\r\n            durationTo: delegationData.durationTo,\r\n            escorts:\r\n              delegationData.escorts?.map(e => ({\r\n                createdAt: now, // Placeholder timestamp\r\n                createdBy: null, // Placeholder\r\n                delegationId: tempId, // Link to optimistic delegation\r\n                employeeId: e.employeeId, // Keep as number\r\n                id: `optimistic-escort-${tempId}-${e.employeeId}`, // Placeholder ID\r\n                notes: e.notes ?? null, // Include notes if available in CreateDelegationData\r\n                updatedAt: now, // Placeholder timestamp\r\n              })) || [],\r\n            eventName: delegationData.eventName,\r\n            id: tempId,\r\n            imageUrl: delegationData.imageUrl ?? null,\r\n            invitationFrom: delegationData.invitationFrom ?? null,\r\n            invitationTo: delegationData.invitationTo ?? null,\r\n            location: delegationData.location,\r\n            notes: delegationData.notes ?? null,\r\n            status: delegationData.status || 'Planned',\r\n            statusHistory: [],\r\n            updatedAt: now,\r\n            vehicles:\r\n              delegationData.vehicles?.map(v => ({\r\n                assignedDate: v.assignedDate,\r\n                createdAt: now, // Placeholder timestamp\r\n                createdBy: null, // Placeholder\r\n                delegationId: tempId, // Link to optimistic delegation\r\n                id: `optimistic-vehicle-${tempId}-${v.vehicleId}`, // Placeholder ID\r\n                notes: v.notes ?? null,\r\n                returnDate: v.returnDate ?? null,\r\n                updatedAt: now, // Placeholder timestamp\r\n                vehicleId: v.vehicleId,\r\n              })) || [],\r\n          };\r\n          return [...old, optimisticDelegation];\r\n        }\r\n      );\r\n      return { previousDelegations };\r\n    },\r\n    onSettled: () => {\r\n      // Invalidate to ensure consistency after success or failure\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateDelegation = () => {\r\n  const queryClient = useQueryClient();\r\n  interface UpdateContext {\r\n    previousDelegation: Delegation | undefined;\r\n    previousDelegationsList: Delegation[] | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Delegation,\r\n    Error,\r\n    { data: UpdateDelegationRequest; id: string }, // Corrected: data is UpdateDelegationRequest\r\n    UpdateContext\r\n  >({\r\n    mutationFn: async ({ data, id }) => {\r\n      // ✅ PRODUCTION FIX: delegationApiService.update() already applies transformation\r\n      return await delegationApiService.update(id, data);\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousDelegation) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.detail(variables.id),\r\n          context.previousDelegation\r\n        );\r\n      }\r\n      if (context?.previousDelegationsList) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.all,\r\n          context.previousDelegationsList\r\n        );\r\n      }\r\n      console.error('Failed to update delegation:', err);\r\n      // Invalidate to refetch correct data on error\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n    onMutate: async ({ data, id }) => {\r\n      // data is UpdateDelegationRequest\r\n      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousDelegation = queryClient.getQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id)\r\n      );\r\n      const previousDelegationsList = queryClient.getQueryData<Delegation[]>(\r\n        delegationQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id),\r\n        old => {\r\n          if (!old) return;\r\n          const now = new Date().toISOString();\r\n\r\n          // ✅ PRODUCTION FIX: Use correct field mappings for UpdateDelegationRequest\r\n          const updatedOptimistic: Delegation = {\r\n            ...old,\r\n            // Handle flight details updates\r\n            arrivalFlight: undefinedToNull(\r\n              data.flightArrivalDetails === null\r\n                ? null // Explicitly set to null if requested\r\n                : data.flightArrivalDetails === undefined\r\n                  ? old.arrivalFlight\r\n                  : {\r\n                      airport:\r\n                        data.flightArrivalDetails.airport ||\r\n                        old.arrivalFlight?.airport ||\r\n                        '',\r\n                      dateTime:\r\n                        data.flightArrivalDetails.dateTime ||\r\n                        old.arrivalFlight?.dateTime ||\r\n                        '',\r\n                      flightNumber:\r\n                        data.flightArrivalDetails.flightNumber ||\r\n                        old.arrivalFlight?.flightNumber ||\r\n                        '',\r\n                      id:\r\n                        old.arrivalFlight?.id || `optimistic-arr-${Date.now()}`, // Keep old ID or generate new\r\n                      notes:\r\n                        data.flightArrivalDetails.notes ??\r\n                        old.arrivalFlight?.notes ??\r\n                        null,\r\n                      terminal:\r\n                        data.flightArrivalDetails.terminal ??\r\n                        old.arrivalFlight?.terminal ??\r\n                        null,\r\n                    } // Keep old value if not in request\r\n            ),\r\n            departureFlight: undefinedToNull(\r\n              data.flightDepartureDetails === null\r\n                ? null // Explicitly set to null if requested\r\n                : data.flightDepartureDetails === undefined\r\n                  ? old.departureFlight\r\n                  : {\r\n                      airport:\r\n                        data.flightDepartureDetails.airport ||\r\n                        old.departureFlight?.airport ||\r\n                        '',\r\n                      dateTime:\r\n                        data.flightDepartureDetails.dateTime ||\r\n                        old.departureFlight?.dateTime ||\r\n                        '',\r\n                      flightNumber:\r\n                        data.flightDepartureDetails.flightNumber ||\r\n                        old.departureFlight?.flightNumber ||\r\n                        '',\r\n                      id:\r\n                        old.departureFlight?.id ||\r\n                        `optimistic-dep-${Date.now()}`, // Keep old ID or generate new\r\n                      notes:\r\n                        data.flightDepartureDetails.notes ??\r\n                        old.departureFlight?.notes ??\r\n                        null,\r\n                      terminal:\r\n                        data.flightDepartureDetails.terminal ??\r\n                        old.departureFlight?.terminal ??\r\n                        null,\r\n                    } // Keep old value if not in request\r\n            ),\r\n            durationFrom: data.durationFrom ?? old.durationFrom, // ✅ Direct mapping\r\n            durationTo: data.durationTo ?? old.durationTo, // ✅ Direct mapping\r\n            // Direct field mappings (no transformation needed)\r\n            eventName: data.eventName ?? old.eventName, // ✅ Direct mapping\r\n            imageUrl: undefinedToNull(data.imageUrl ?? old.imageUrl),\r\n            invitationFrom: undefinedToNull(\r\n              data.invitationFrom ?? old.invitationFrom\r\n            ),\r\n            invitationTo: undefinedToNull(\r\n              data.invitationTo ?? old.invitationTo\r\n            ),\r\n            location: data.location ?? old.location,\r\n            notes: undefinedToNull(data.notes ?? old.notes),\r\n            status: (data.status as DelegationStatusPrisma) ?? old.status, // Cast status\r\n            updatedAt: now,\r\n            // Note: Nested assignments (escorts, drivers, vehicles) are typically managed via separate mutations,\r\n            // so they are not included in the main delegation update optimistic logic here.\r\n          };\r\n          return updatedOptimistic;\r\n        }\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation[]>(\r\n        delegationQueryKeys.all,\r\n        (oldList = []) =>\r\n          oldList.map(delegation =>\r\n            delegation.id === id\r\n              ? queryClient.getQueryData<Delegation>(\r\n                  delegationQueryKeys.detail(id)\r\n                ) || delegation\r\n              : delegation\r\n          )\r\n      );\r\n\r\n      return { previousDelegation, previousDelegationsList };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      // Always refetch after error or success\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateDelegationStatus = () => {\r\n  const queryClient = useQueryClient();\r\n  interface StatusUpdateContext {\r\n    previousDelegation: Delegation | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Delegation,\r\n    Error,\r\n    { id: string; status: DelegationStatusPrisma; statusChangeReason?: string },\r\n    StatusUpdateContext\r\n  >({\r\n    mutationFn: async ({ id, status, statusChangeReason }) => {\r\n      const response = await delegationApiService.updateStatus(\r\n        id,\r\n        status,\r\n        statusChangeReason\r\n      );\r\n      return response;\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousDelegation) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.detail(variables.id),\r\n          context.previousDelegation\r\n        );\r\n      }\r\n      console.error('Failed to update delegation status:', err);\r\n    },\r\n    onMutate: async ({ id, status }) => {\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n      const previousDelegation = queryClient.getQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id)\r\n      );\r\n      queryClient.setQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id),\r\n        old => (old ? { ...old, status: status } : undefined)\r\n      );\r\n      return { previousDelegation };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useManageDelegationFlightDetails = () => {\r\n  const queryClient = useQueryClient();\r\n  interface FlightDetailsContext {\r\n    previousDelegation: Delegation | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Delegation,\r\n    Error,\r\n    { flightDetails: FlightDetails; id: string },\r\n    FlightDetailsContext\r\n  >({\r\n    mutationFn: async ({ flightDetails, id }) => {\r\n      // This service method might need adjustment if flightDetails from form is Omit<FlightDetails, 'id'>\r\n      // For now, assuming it expects full FlightDetails (including ID for existing, or will generate for new)\r\n      const response = await delegationApiService.manageFlightDetails(\r\n        id,\r\n        flightDetails\r\n      );\r\n      return response;\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousDelegation) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.detail(variables.id),\r\n          context.previousDelegation\r\n        );\r\n      }\r\n      console.error('Failed to manage delegation flight details:', err);\r\n    },\r\n    onMutate: async ({ flightDetails, id }) => {\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n      const previousDelegation = queryClient.getQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id)\r\n      );\r\n      queryClient.setQueryData<Delegation>(\r\n        delegationQueryKeys.detail(id),\r\n        old => {\r\n          if (!old) return;\r\n          // This optimistic update assumes flightDetails is for arrival.\r\n          // A more robust solution would need to know if it's arrival or departure.\r\n          return { ...old, arrivalFlight: flightDetails };\r\n        }\r\n      );\r\n      return { previousDelegation };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: delegationQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useDeleteDelegation = () => {\r\n  const queryClient = useQueryClient();\r\n  interface DeleteContext {\r\n    previousDelegationsList: Delegation[] | undefined;\r\n  }\r\n\r\n  return useMutation<string, Error, string, DeleteContext>({\r\n    mutationFn: async (id: string) => {\r\n      await delegationApiService.delete(id);\r\n      return id;\r\n    },\r\n    onError: (err, _id, context) => {\r\n      if (context?.previousDelegationsList) {\r\n        queryClient.setQueryData(\r\n          delegationQueryKeys.all,\r\n          context.previousDelegationsList\r\n        );\r\n      }\r\n      console.error('Failed to delete delegation:', err);\r\n    },\r\n    onMutate: async id => {\r\n      await queryClient.cancelQueries({ queryKey: delegationQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: delegationQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousDelegationsList = queryClient.getQueryData<Delegation[]>(\r\n        delegationQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Delegation[]>(\r\n        delegationQueryKeys.all,\r\n        (old = []) => old.filter(delegation => delegation.id !== id)\r\n      );\r\n\r\n      queryClient.removeQueries({ queryKey: delegationQueryKeys.detail(id) });\r\n\r\n      return { previousDelegationsList };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: delegationQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;AAID;AAAA;AAAA;AACA;AAEA;AAcA,uOAAiE,uBAAuB;AACxF,gTAA6E,0BAA0B;AAAvG;AACA;AACA;AACA;;;;;;;;;AAKO,MAAM,iBAAiB,CAC5B;IAKA,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,oJAAA,CAAA,sBAAmB,CAAC,GAAG;KAAC,EAC5B;QACE,MAAM,SAAS,MAAM,wIAAA,CAAA,uBAAoB,CAAC,MAAM;QAChD,OAAO,OAAO,IAAI;IACpB,GACA,cACA;QACE,WAAW;QACX,GAAG,OAAO;IACZ;AAEJ;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;KAAI,EACnC;QACE,OAAO,MAAM,wIAAA,CAAA,uBAAoB,CAAC,OAAO,CAAC;IAC5C,GACA,cACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;AAGO,MAAM,+BAA+B,CAAC;IAC3C,2EAA2E;IAC3E,MAAM,UAAU,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE;QACzB,SAAS,CAAA,GAAA,oJAAA,CAAA,yCAAsC,AAAD,EAAE;IAClD;IAEA,MAAM,CAAC,iBAAiB,gBAAgB,cAAc,GAAG;IAEzD,yDAAyD;IACzD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACjC,IACE,CAAC,iBAAiB,QAClB,CAAC,gBAAgB,QACjB,CAAC,eAAe,MAChB;YACA;QACF;QAEA,IAAI;YACF,qFAAqF;YACrF,yDAAyD;YACzD,MAAM,aAAa,gBAAgB,IAAI;YACvC,OAAO,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EACpB,YACA,eAAe,IAAI,EACnB,cAAc,IAAI;QAEtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF,GAAG;QAAC,iBAAiB;QAAM,gBAAgB;QAAM,eAAe;KAAK;IAErE,4EAA4E;IAC5E,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;IACjB,GAAG;QACD,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;KAChB;IAED,sDAAsD;IACtD,OAAO;QACL,MAAM;QACN,OACE,iBAAiB,SAAS,gBAAgB,SAAS,eAAe;QACpE,SACE,iBAAiB,WACjB,gBAAgB,WAChB,eAAe;QACjB,WACE,iBAAiB,aACjB,gBAAgB,aAChB,eAAe;QACjB,WACE,iBAAiB,aACjB,gBAAgB,aAChB,eAAe;QACjB;IACF;AACF;AAGO,MAAM,wBAAwB;AAE9B,MAAM,sBAAsB;IACjC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAA0D;QACzE,YAAY,OAAO;YACjB,MAAM,aAAa,mJAAA,CAAA,wBAAqB,CAAC,eAAe,CAAC;YACzD,iFAAiF;YACjF,OAAO,MAAM,wIAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC;QAC3C;QACA,SAAS,CAAC,KAAK,iBAAiB;YAC9B,IAAI,SAAS,qBAAqB;gBAChC,YAAY,YAAY,CACtB,oJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,QAAQ,mBAAmB;YAE/B;YACA,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,8CAA8C;YAC9C,YAAY,iBAAiB,CAAC;gBAAE,UAAU,oJAAA,CAAA,sBAAmB,CAAC,GAAG;YAAC;QACpE;QACA,UAAU,OAAO;YACf,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU,oJAAA,CAAA,sBAAmB,CAAC,GAAG;YAAC;YACpE,MAAM,sBAAsB,YAAY,YAAY,CAClD,oJAAA,CAAA,sBAAmB,CAAC,GAAG;YAGzB,YAAY,YAAY,CACtB,oJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,CAAC,MAAM,EAAE;gBACP,MAAM,SAAS,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI;gBACzC,MAAM,MAAM,IAAI,OAAO,WAAW;gBAElC,MAAM,0BACJ,eAAe,oBAAoB,GAC/B;oBACE,IAAI,CAAC,sBAAsB,EAAE,KAAK,GAAG,IAAI;oBACzC,GAAG,eAAe,oBAAoB;gBACxC,IACA;gBAEN,MAAM,4BACJ,eAAe,sBAAsB,GACjC;oBACE,IAAI,CAAC,sBAAsB,EAAE,KAAK,GAAG,KAAK,GAAG;oBAC7C,GAAG,eAAe,sBAAsB;gBAC1C,IACA;gBAEN,MAAM,sBACJ,eAAe,SAAS,EAAE,IAAI,CAAC,GAAG,QAAU,CAAC;wBAC3C,IAAI,CAAC,oBAAoB,EAAE,OAAO,CAAC,EAAE,OAAO;wBAC5C,MAAM,EAAE,IAAI;wBACZ,OAAO,EAAE,KAAK,IAAI;wBAClB,OAAO,EAAE,KAAK;oBAChB,CAAC,MAAM,EAAE;gBAEX,MAAM,uBAAmC;oBACvC,eAAe,2BAA2B;oBAC1C,WAAW;oBACX,WAAW;oBACX,iBAAiB,6BAA6B;oBAC9C,SACE,eAAe,OAAO,EAAE,IAAI,CAAA,IAAK,CAAC;4BAChC,WAAW;4BACX,WAAW;4BACX,cAAc;4BACd,YAAY,EAAE,UAAU;4BACxB,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,EAAE,EAAE,UAAU,EAAE;4BACjD,OAAO,EAAE,KAAK,IAAI;4BAClB,WAAW;wBACb,CAAC,MAAM,EAAE;oBACX,cAAc,eAAe,YAAY;oBACzC,YAAY,eAAe,UAAU;oBACrC,SACE,eAAe,OAAO,EAAE,IAAI,CAAA,IAAK,CAAC;4BAChC,WAAW;4BACX,WAAW;4BACX,cAAc;4BACd,YAAY,EAAE,UAAU;4BACxB,IAAI,CAAC,kBAAkB,EAAE,OAAO,CAAC,EAAE,EAAE,UAAU,EAAE;4BACjD,OAAO,EAAE,KAAK,IAAI;4BAClB,WAAW;wBACb,CAAC,MAAM,EAAE;oBACX,WAAW,eAAe,SAAS;oBACnC,IAAI;oBACJ,UAAU,eAAe,QAAQ,IAAI;oBACrC,gBAAgB,eAAe,cAAc,IAAI;oBACjD,cAAc,eAAe,YAAY,IAAI;oBAC7C,UAAU,eAAe,QAAQ;oBACjC,OAAO,eAAe,KAAK,IAAI;oBAC/B,QAAQ,eAAe,MAAM,IAAI;oBACjC,eAAe,EAAE;oBACjB,WAAW;oBACX,UACE,eAAe,QAAQ,EAAE,IAAI,CAAA,IAAK,CAAC;4BACjC,cAAc,EAAE,YAAY;4BAC5B,WAAW;4BACX,WAAW;4BACX,cAAc;4BACd,IAAI,CAAC,mBAAmB,EAAE,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE;4BACjD,OAAO,EAAE,KAAK,IAAI;4BAClB,YAAY,EAAE,UAAU,IAAI;4BAC5B,WAAW;4BACX,WAAW,EAAE,SAAS;wBACxB,CAAC,MAAM,EAAE;gBACb;gBACA,OAAO;uBAAI;oBAAK;iBAAqB;YACvC;YAEF,OAAO;gBAAE;YAAoB;QAC/B;QACA,WAAW;YACT,4DAA4D;YAC5D,YAAY,iBAAiB,CAAC;gBAAE,UAAU,oJAAA,CAAA,sBAAmB,CAAC,GAAG;YAAC;QACpE;IACF;AACF;AAEO,MAAM,sBAAsB;IACjC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAMjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAKf;QACA,YAAY,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;YAC7B,iFAAiF;YACjF,OAAO,MAAM,wIAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,IAAI;QAC/C;QACA,SAAS,CAAC,KAAK,WAAW;YACxB,IAAI,SAAS,oBAAoB;gBAC/B,YAAY,YAAY,CACtB,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,GACvC,QAAQ,kBAAkB;YAE9B;YACA,IAAI,SAAS,yBAAyB;gBACpC,YAAY,YAAY,CACtB,oJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,QAAQ,uBAAuB;YAEnC;YACA,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,8CAA8C;YAC9C,YAAY,iBAAiB,CAAC;gBAC5B,UAAU,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;YACnD;YACA,YAAY,iBAAiB,CAAC;gBAAE,UAAU,oJAAA,CAAA,sBAAmB,CAAC,GAAG;YAAC;QACpE;QACA,UAAU,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;YAC3B,kCAAkC;YAClC,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU,oJAAA,CAAA,sBAAmB,CAAC,GAAG;YAAC;YACpE,MAAM,YAAY,aAAa,CAAC;gBAC9B,UAAU,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;YACvC;YAEA,MAAM,qBAAqB,YAAY,YAAY,CACjD,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;YAE7B,MAAM,0BAA0B,YAAY,YAAY,CACtD,oJAAA,CAAA,sBAAmB,CAAC,GAAG;YAGzB,YAAY,YAAY,CACtB,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,KAC3B,CAAA;gBACE,IAAI,CAAC,KAAK;gBACV,MAAM,MAAM,IAAI,OAAO,WAAW;gBAElC,2EAA2E;gBAC3E,MAAM,oBAAgC;oBACpC,GAAG,GAAG;oBACN,gCAAgC;oBAChC,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,oBAAoB,KAAK,OAC1B,KAAK,sCAAsC;uBAC3C,KAAK,oBAAoB,KAAK,YAC5B,IAAI,aAAa,GACjB;wBACE,SACE,KAAK,oBAAoB,CAAC,OAAO,IACjC,IAAI,aAAa,EAAE,WACnB;wBACF,UACE,KAAK,oBAAoB,CAAC,QAAQ,IAClC,IAAI,aAAa,EAAE,YACnB;wBACF,cACE,KAAK,oBAAoB,CAAC,YAAY,IACtC,IAAI,aAAa,EAAE,gBACnB;wBACF,IACE,IAAI,aAAa,EAAE,MAAM,CAAC,eAAe,EAAE,KAAK,GAAG,IAAI;wBACzD,OACE,KAAK,oBAAoB,CAAC,KAAK,IAC/B,IAAI,aAAa,EAAE,SACnB;wBACF,UACE,KAAK,oBAAoB,CAAC,QAAQ,IAClC,IAAI,aAAa,EAAE,YACnB;oBACJ,EAAE,mCAAmC;;oBAE7C,iBAAiB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,sBAAsB,KAAK,OAC5B,KAAK,sCAAsC;uBAC3C,KAAK,sBAAsB,KAAK,YAC9B,IAAI,eAAe,GACnB;wBACE,SACE,KAAK,sBAAsB,CAAC,OAAO,IACnC,IAAI,eAAe,EAAE,WACrB;wBACF,UACE,KAAK,sBAAsB,CAAC,QAAQ,IACpC,IAAI,eAAe,EAAE,YACrB;wBACF,cACE,KAAK,sBAAsB,CAAC,YAAY,IACxC,IAAI,eAAe,EAAE,gBACrB;wBACF,IACE,IAAI,eAAe,EAAE,MACrB,CAAC,eAAe,EAAE,KAAK,GAAG,IAAI;wBAChC,OACE,KAAK,sBAAsB,CAAC,KAAK,IACjC,IAAI,eAAe,EAAE,SACrB;wBACF,UACE,KAAK,sBAAsB,CAAC,QAAQ,IACpC,IAAI,eAAe,EAAE,YACrB;oBACJ,EAAE,mCAAmC;;oBAE7C,cAAc,KAAK,YAAY,IAAI,IAAI,YAAY;oBACnD,YAAY,KAAK,UAAU,IAAI,IAAI,UAAU;oBAC7C,mDAAmD;oBACnD,WAAW,KAAK,SAAS,IAAI,IAAI,SAAS;oBAC1C,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,QAAQ;oBACvD,gBAAgB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC5B,KAAK,cAAc,IAAI,IAAI,cAAc;oBAE3C,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,IAAI,IAAI,YAAY;oBAEvC,UAAU,KAAK,QAAQ,IAAI,IAAI,QAAQ;oBACvC,OAAO,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,KAAK,IAAI,IAAI,KAAK;oBAC9C,QAAQ,AAAC,KAAK,MAAM,IAA+B,IAAI,MAAM;oBAC7D,WAAW;gBAGb;gBACA,OAAO;YACT;YAGF,YAAY,YAAY,CACtB,oJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,CAAC,UAAU,EAAE,GACX,QAAQ,GAAG,CAAC,CAAA,aACV,WAAW,EAAE,KAAK,KACd,YAAY,YAAY,CACtB,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,QACxB,aACL;YAIV,OAAO;gBAAE;gBAAoB;YAAwB;QACvD;QACA,WAAW,CAAC,OAAO,QAAQ;YACzB,wCAAwC;YACxC,YAAY,iBAAiB,CAAC;gBAC5B,UAAU,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;YACnD;YACA,YAAY,iBAAiB,CAAC;gBAAE,UAAU,oJAAA,CAAA,sBAAmB,CAAC,GAAG;YAAC;QACpE;IACF;AACF;AAEO,MAAM,4BAA4B;IACvC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAKf;QACA,YAAY,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE;YACnD,MAAM,WAAW,MAAM,wIAAA,CAAA,uBAAoB,CAAC,YAAY,CACtD,IACA,QACA;YAEF,OAAO;QACT;QACA,SAAS,CAAC,KAAK,WAAW;YACxB,IAAI,SAAS,oBAAoB;gBAC/B,YAAY,YAAY,CACtB,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,GACvC,QAAQ,kBAAkB;YAE9B;YACA,QAAQ,KAAK,CAAC,uCAAuC;QACvD;QACA,UAAU,OAAO,EAAE,EAAE,EAAE,MAAM,EAAE;YAC7B,MAAM,YAAY,aAAa,CAAC;gBAC9B,UAAU,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;YACvC;YACA,MAAM,qBAAqB,YAAY,YAAY,CACjD,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;YAE7B,YAAY,YAAY,CACtB,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,KAC3B,CAAA,MAAQ,MAAM;oBAAE,GAAG,GAAG;oBAAE,QAAQ;gBAAO,IAAI;YAE7C,OAAO;gBAAE;YAAmB;QAC9B;QACA,WAAW,CAAC,OAAO,QAAQ;YACzB,YAAY,iBAAiB,CAAC;gBAC5B,UAAU,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;YACnD;YACA,YAAY,iBAAiB,CAAC;gBAAE,UAAU,oJAAA,CAAA,sBAAmB,CAAC,GAAG;YAAC;QACpE;IACF;AACF;AAEO,MAAM,mCAAmC;IAC9C,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAKf;QACA,YAAY,OAAO,EAAE,aAAa,EAAE,EAAE,EAAE;YACtC,oGAAoG;YACpG,wGAAwG;YACxG,MAAM,WAAW,MAAM,wIAAA,CAAA,uBAAoB,CAAC,mBAAmB,CAC7D,IACA;YAEF,OAAO;QACT;QACA,SAAS,CAAC,KAAK,WAAW;YACxB,IAAI,SAAS,oBAAoB;gBAC/B,YAAY,YAAY,CACtB,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE,GACvC,QAAQ,kBAAkB;YAE9B;YACA,QAAQ,KAAK,CAAC,+CAA+C;QAC/D;QACA,UAAU,OAAO,EAAE,aAAa,EAAE,EAAE,EAAE;YACpC,MAAM,YAAY,aAAa,CAAC;gBAC9B,UAAU,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;YACvC;YACA,MAAM,qBAAqB,YAAY,YAAY,CACjD,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;YAE7B,YAAY,YAAY,CACtB,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,KAC3B,CAAA;gBACE,IAAI,CAAC,KAAK;gBACV,+DAA+D;gBAC/D,0EAA0E;gBAC1E,OAAO;oBAAE,GAAG,GAAG;oBAAE,eAAe;gBAAc;YAChD;YAEF,OAAO;gBAAE;YAAmB;QAC9B;QACA,WAAW,CAAC,OAAO,QAAQ;YACzB,YAAY,iBAAiB,CAAC;gBAC5B,UAAU,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,UAAU,EAAE;YACnD;YACA,YAAY,iBAAiB,CAAC;gBAAE,UAAU,oJAAA,CAAA,sBAAmB,CAAC,GAAG;YAAC;QACpE;IACF;AACF;AAEO,MAAM,sBAAsB;IACjC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAKjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAwC;QACvD,YAAY,OAAO;YACjB,MAAM,wIAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC;YAClC,OAAO;QACT;QACA,SAAS,CAAC,KAAK,KAAK;YAClB,IAAI,SAAS,yBAAyB;gBACpC,YAAY,YAAY,CACtB,oJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,QAAQ,uBAAuB;YAEnC;YACA,QAAQ,KAAK,CAAC,gCAAgC;QAChD;QACA,UAAU,OAAM;YACd,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU,oJAAA,CAAA,sBAAmB,CAAC,GAAG;YAAC;YACpE,MAAM,YAAY,aAAa,CAAC;gBAC9B,UAAU,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;YACvC;YAEA,MAAM,0BAA0B,YAAY,YAAY,CACtD,oJAAA,CAAA,sBAAmB,CAAC,GAAG;YAGzB,YAAY,YAAY,CACtB,oJAAA,CAAA,sBAAmB,CAAC,GAAG,EACvB,CAAC,MAAM,EAAE,GAAK,IAAI,MAAM,CAAC,CAAA,aAAc,WAAW,EAAE,KAAK;YAG3D,YAAY,aAAa,CAAC;gBAAE,UAAU,oJAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC;YAAI;YAErE,OAAO;gBAAE;YAAwB;QACnC;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,oJAAA,CAAA,sBAAmB,CAAC,GAAG;YAAC;QACpE;IACF;AACF", "debugId": null}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/delegations/dashboard/DelegationDashboard.tsx"], "sourcesContent": ["/**\r\n * @file Delegation dashboard component using generic dashboard framework\r\n * @module components/delegations/dashboard/DelegationDashboard\r\n */\r\n\r\n'use client';\r\n\r\nimport React from 'react';\r\nimport { Briefcase, Calendar, CheckCircle, Clock, Users } from 'lucide-react';\r\n\r\nimport { DashboardPage } from '@/components/dashboard/DashboardLayout';\r\nimport type { DashboardConfig, TabConfig } from '@/components/dashboard/types';\r\nimport type { Delegation } from '@/lib/types/domain';\r\nimport { useDashboardStore } from '@/hooks/domain/useDashboardStore';\r\nimport { useDelegations } from '@/lib/stores/queries/useDelegations';\r\n\r\n/**\r\n * Delegation dashboard configuration\r\n */\r\nconst delegationDashboardConfig: DashboardConfig<Delegation> = {\r\n  entityType: 'delegation',\r\n  title: 'Manage Delegations',\r\n  description:\r\n    'Track and manage all your events, trips, and delegate information.',\r\n  viewModes: ['cards', 'table', 'calendar'],\r\n  defaultViewMode: 'cards',\r\n  tabs: [\r\n    {\r\n      id: 'all',\r\n      label: 'All Delegations',\r\n      icon: <Briefcase className=\"size-4\" />,\r\n    },\r\n    {\r\n      id: 'upcoming',\r\n      label: 'Upcoming',\r\n      icon: <Clock className=\"size-4\" />,\r\n    },\r\n    {\r\n      id: 'in-progress',\r\n      label: 'In Progress',\r\n      icon: <Users className=\"size-4\" />,\r\n    },\r\n    {\r\n      id: 'completed',\r\n      label: 'Completed',\r\n      icon: <CheckCircle className=\"size-4\" />,\r\n    },\r\n  ] as TabConfig[],\r\n  filters: [\r\n    {\r\n      id: 'status',\r\n      label: 'Status',\r\n      type: 'select',\r\n      options: [\r\n        { label: 'All Statuses', value: '' },\r\n        { label: 'Planned', value: 'Planned' },\r\n        { label: 'Confirmed', value: 'Confirmed' },\r\n        { label: 'In Progress', value: 'In_Progress' },\r\n        { label: 'Completed', value: 'Completed' },\r\n        { label: 'Cancelled', value: 'Cancelled' },\r\n      ],\r\n    },\r\n    {\r\n      id: 'location',\r\n      label: 'Location',\r\n      type: 'search',\r\n      placeholder: 'Filter by location...',\r\n    },\r\n    {\r\n      id: 'dateRange',\r\n      label: 'Date Range',\r\n      type: 'daterange',\r\n    },\r\n  ],\r\n  sortOptions: [\r\n    { id: 'eventName', label: 'Event Name', field: 'eventName' },\r\n    { id: 'durationFrom', label: 'Start Date', field: 'durationFrom' },\r\n    { id: 'status', label: 'Status', field: 'status' },\r\n    { id: 'location', label: 'Location', field: 'location' },\r\n    { id: 'createdAt', label: 'Created', field: 'createdAt' },\r\n  ],\r\n  enableBulkActions: true,\r\n  enableExport: true,\r\n  refreshInterval: 30000, // 30 seconds\r\n};\r\n\r\n/**\r\n * Props for DelegationDashboard component\r\n */\r\ninterface DelegationDashboardProps {\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * Main delegation dashboard component using the generic dashboard framework.\r\n *\r\n * This component provides:\r\n * - Multiple view modes (cards, table, calendar)\r\n * - Advanced filtering and sorting\r\n * - Tab-based organization\r\n * - Bulk operations\r\n * - Export functionality\r\n * - Persistent user preferences\r\n *\r\n * @param props - Component props\r\n * @returns JSX element representing the delegation dashboard\r\n */\r\nexport const DelegationDashboard: React.FC<DelegationDashboardProps> = ({\r\n  className = '',\r\n}) => {\r\n  // Get delegation data\r\n  const {\r\n    data: delegations = [],\r\n    isLoading,\r\n    error,\r\n    refetch,\r\n  } = useDelegations();\r\n\r\n  // Get dashboard store for delegations\r\n  const dashboardStore = useDashboardStore('delegation');\r\n  const {\r\n    activeTab,\r\n    layout,\r\n    filters,\r\n    searchTerm,\r\n    selectedItems,\r\n    getFilteredData,\r\n    getSelectedCount,\r\n    hasActiveFilters,\r\n  } = dashboardStore();\r\n\r\n  // Filter delegations based on active tab\r\n  const getTabFilteredDelegations = (delegations: Delegation[]) => {\r\n    const now = new Date();\r\n\r\n    switch (activeTab) {\r\n      case 'upcoming':\r\n        return delegations.filter(\r\n          d =>\r\n            new Date(d.durationFrom) > now &&\r\n            ['Planned', 'Confirmed'].includes(d.status)\r\n        );\r\n      case 'in-progress':\r\n        return delegations.filter(d => d.status === 'In_Progress');\r\n      case 'completed':\r\n        return delegations.filter(d => d.status === 'Completed');\r\n      default:\r\n        return delegations;\r\n    }\r\n  };\r\n\r\n  // Get filtered and sorted data\r\n  const tabFilteredDelegations = getTabFilteredDelegations(delegations);\r\n  const filteredDelegations = getFilteredData(\r\n    tabFilteredDelegations,\r\n    delegationDashboardConfig\r\n  );\r\n\r\n  // Update tab counts\r\n  const updatedConfig = {\r\n    ...delegationDashboardConfig,\r\n    tabs:\r\n      delegationDashboardConfig.tabs?.map(tab => ({\r\n        ...tab,\r\n        count: getTabFilteredDelegations(delegations).length,\r\n      })) || [],\r\n  };\r\n\r\n  return (\r\n    <DashboardPage config={updatedConfig} className={className}>\r\n      {/* Dashboard implementation will be completed in the next phase */}\r\n      <div className=\"space-y-6\">\r\n        <div className=\"text-center py-12\">\r\n          <Briefcase className=\"mx-auto mb-4 size-16 text-muted-foreground\" />\r\n          <h2 className=\"text-2xl font-semibold mb-2\">\r\n            Enhanced Delegation Dashboard\r\n          </h2>\r\n          <p className=\"text-muted-foreground mb-4\">\r\n            Using reusable patterns from the reliability dashboard\r\n          </p>\r\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 max-w-md mx-auto\">\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-primary\">\r\n                {delegations.length}\r\n              </div>\r\n              <div className=\"text-sm text-muted-foreground\">Total</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-blue-600\">\r\n                {getTabFilteredDelegations(delegations).length}\r\n              </div>\r\n              <div className=\"text-sm text-muted-foreground\">Filtered</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-green-600\">\r\n                {getSelectedCount()}\r\n              </div>\r\n              <div className=\"text-sm text-muted-foreground\">Selected</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"text-2xl font-bold text-orange-600\">\r\n                {hasActiveFilters() ? '✓' : '○'}\r\n              </div>\r\n              <div className=\"text-sm text-muted-foreground\">Filters</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </DashboardPage>\r\n  );\r\n};\r\n\r\nexport default DelegationDashboard;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAKD;AAAA;AAAA;AAAA;AAEA;AAGA;AACA;AATA;;;;;;AAWA;;CAEC,GACD,MAAM,4BAAyD;IAC7D,YAAY;IACZ,OAAO;IACP,aACE;IACF,WAAW;QAAC;QAAS;QAAS;KAAW;IACzC,iBAAiB;IACjB,MAAM;QACJ;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC7B;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QACzB;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAC/B;KACD;IACD,SAAS;QACP;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,SAAS;gBACP;oBAAE,OAAO;oBAAgB,OAAO;gBAAG;gBACnC;oBAAE,OAAO;oBAAW,OAAO;gBAAU;gBACrC;oBAAE,OAAO;oBAAa,OAAO;gBAAY;gBACzC;oBAAE,OAAO;oBAAe,OAAO;gBAAc;gBAC7C;oBAAE,OAAO;oBAAa,OAAO;gBAAY;gBACzC;oBAAE,OAAO;oBAAa,OAAO;gBAAY;aAC1C;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;QACR;KACD;IACD,aAAa;QACX;YAAE,IAAI;YAAa,OAAO;YAAc,OAAO;QAAY;QAC3D;YAAE,IAAI;YAAgB,OAAO;YAAc,OAAO;QAAe;QACjE;YAAE,IAAI;YAAU,OAAO;YAAU,OAAO;QAAS;QACjD;YAAE,IAAI;YAAY,OAAO;YAAY,OAAO;QAAW;QACvD;YAAE,IAAI;YAAa,OAAO;YAAW,OAAO;QAAY;KACzD;IACD,mBAAmB;IACnB,cAAc;IACd,iBAAiB;AACnB;AAuBO,MAAM,sBAA0D,CAAC,EACtE,YAAY,EAAE,EACf;IACC,sBAAsB;IACtB,MAAM,EACJ,MAAM,cAAc,EAAE,EACtB,SAAS,EACT,KAAK,EACL,OAAO,EACR,GAAG,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAEjB,sCAAsC;IACtC,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,oBAAiB,AAAD,EAAE;IACzC,MAAM,EACJ,SAAS,EACT,MAAM,EACN,OAAO,EACP,UAAU,EACV,aAAa,EACb,eAAe,EACf,gBAAgB,EAChB,gBAAgB,EACjB,GAAG;IAEJ,yCAAyC;IACzC,MAAM,4BAA4B,CAAC;QACjC,MAAM,MAAM,IAAI;QAEhB,OAAQ;YACN,KAAK;gBACH,OAAO,YAAY,MAAM,CACvB,CAAA,IACE,IAAI,KAAK,EAAE,YAAY,IAAI,OAC3B;wBAAC;wBAAW;qBAAY,CAAC,QAAQ,CAAC,EAAE,MAAM;YAEhD,KAAK;gBACH,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YAC9C,KAAK;gBACH,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YAC9C;gBACE,OAAO;QACX;IACF;IAEA,+BAA+B;IAC/B,MAAM,yBAAyB,0BAA0B;IACzD,MAAM,sBAAsB,gBAC1B,wBACA;IAGF,oBAAoB;IACpB,MAAM,gBAAgB;QACpB,GAAG,yBAAyB;QAC5B,MACE,0BAA0B,IAAI,EAAE,IAAI,CAAA,MAAO,CAAC;gBAC1C,GAAG,GAAG;gBACN,OAAO,0BAA0B,aAAa,MAAM;YACtD,CAAC,MAAM,EAAE;IACb;IAEA,qBACE,8OAAC,kJAAA,CAAA,gBAAa;QAAC,QAAQ;QAAe,WAAW;kBAE/C,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,8OAAC;wBAAG,WAAU;kCAA8B;;;;;;kCAG5C,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,YAAY,MAAM;;;;;;kDAErB,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;0CAEjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,0BAA0B,aAAa,MAAM;;;;;;kDAEhD,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;0CAEjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAEH,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;0CAEjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,qBAAqB,MAAM;;;;;;kDAE9B,8OAAC;wCAAI,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7D;uCAEe", "debugId": null}}, {"offset": {"line": 2004, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/radio-group.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as RadioGroupPrimitive from '@radix-ui/react-radio-group';\r\nimport { Circle } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst RadioGroup = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Root\r\n      className={cn('grid gap-2', className)}\r\n      {...props}\r\n      ref={ref}\r\n    />\r\n  );\r\n});\r\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName;\r\n\r\nconst RadioGroupItem = React.forwardRef<\r\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <RadioGroupPrimitive.Item\r\n      className={cn(\r\n        'aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    >\r\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\r\n        <Circle className=\"size-2.5 fill-current text-current\" />\r\n      </RadioGroupPrimitive.Indicator>\r\n    </RadioGroupPrimitive.Item>\r\n  );\r\n});\r\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;\r\n\r\nexport { RadioGroup, RadioGroupItem };\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;AACA,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC,0KAAA,CAAA,OAAwB;QACvB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,4OACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,YAA6B;YAAC,WAAU;sBACvC,cAAA,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;AACA,eAAe,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2065, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/slider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as SliderPrimitive from '@radix-ui/react-slider';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Slider = React.forwardRef<\r\n  React.ElementRef<typeof SliderPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SliderPrimitive.Root\r\n    className={cn(\r\n      'relative flex w-full touch-none select-none items-center',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\r\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\r\n    </SliderPrimitive.Track>\r\n    <SliderPrimitive.Thumb className=\"block size-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\r\n  </SliderPrimitive.Root>\r\n));\r\nSlider.displayName = SliderPrimitive.Root.displayName;\r\n\r\nexport { Slider };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAEF,KAAK;QACJ,GAAG,KAAK;;0BAET,8OAAC,kKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,8OAAC,kKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,8OAAC,kKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/dashboard/DashboardSettings.tsx"], "sourcesContent": ["/**\r\n * @file Generic dashboard settings component\r\n * @module components/dashboard/DashboardSettings\r\n */\r\n\r\n'use client';\r\n\r\nimport {\r\n  Calendar,\r\n  Grid3X3,\r\n  LayoutGrid,\r\n  List,\r\n  Minimize,\r\n  RefreshCw,\r\n  Settings,\r\n  Table,\r\n  Zap,\r\n} from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport type { DashboardConfig, ViewMode } from './types';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Label } from '@/components/ui/label';\r\nimport { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';\r\nimport { Slider } from '@/components/ui/slider';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\n\r\n/**\r\n * Props for DashboardSettings component\r\n */\r\ninterface DashboardSettingsProps {\r\n  config: DashboardConfig;\r\n  entityType: string;\r\n  // Dashboard state\r\n  layout: {\r\n    viewMode: ViewMode;\r\n    gridColumns: number;\r\n    compactMode: boolean;\r\n    showFilters: boolean;\r\n    showSettings: boolean;\r\n  };\r\n  monitoring: {\r\n    enabled: boolean;\r\n    refreshInterval: number;\r\n    autoRefresh: boolean;\r\n    pausedDataTypes: Set<string>;\r\n  };\r\n  // Actions\r\n  setViewMode: (mode: ViewMode) => void;\r\n  setGridColumns: (columns: number) => void;\r\n  toggleCompactMode: () => void;\r\n  setMonitoringEnabled: (enabled: boolean) => void;\r\n  setRefreshInterval: (interval: number) => void;\r\n  toggleAutoRefresh: () => void;\r\n  resetSettings: () => void;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * View mode configuration with icons and labels\r\n */\r\nconst viewModeConfig = {\r\n  cards: { icon: LayoutGrid, label: 'Cards' },\r\n  table: { icon: Table, label: 'Table' },\r\n  list: { icon: List, label: 'List' },\r\n  calendar: { icon: Calendar, label: 'Calendar' },\r\n  grid: { icon: Grid3X3, label: 'Grid' },\r\n};\r\n\r\n/**\r\n * Generic dashboard settings component that can be reused across different entities.\r\n *\r\n * Features:\r\n * - View mode selection (cards, table, list, calendar, grid)\r\n * - Grid column configuration\r\n * - Compact mode toggle\r\n * - Monitoring controls\r\n * - Refresh interval settings\r\n * - Reset to defaults\r\n *\r\n * @param props - Component props\r\n * @returns JSX element representing the dashboard settings\r\n */\r\nexport const DashboardSettings: React.FC<DashboardSettingsProps> = ({\r\n  config,\r\n  entityType,\r\n  layout,\r\n  monitoring,\r\n  setViewMode,\r\n  setGridColumns,\r\n  toggleCompactMode,\r\n  setMonitoringEnabled,\r\n  setRefreshInterval,\r\n  toggleAutoRefresh,\r\n  resetSettings,\r\n  className = '',\r\n}) => {\r\n  // Get available view modes from config\r\n  const availableViewModes = config.viewModes || ['cards', 'table', 'list'];\r\n\r\n  // Refresh interval options (in milliseconds)\r\n  const refreshIntervals = [\r\n    { label: '5 seconds', value: 5000 },\r\n    { label: '10 seconds', value: 10000 },\r\n    { label: '30 seconds', value: 30000 },\r\n    { label: '1 minute', value: 60000 },\r\n    { label: '5 minutes', value: 300000 },\r\n  ];\r\n\r\n  return (\r\n    <div className={`space-y-6 p-4 ${className}`}>\r\n      <div className=\"space-y-2\">\r\n        <h2 className=\"text-2xl font-bold flex items-center gap-2\">\r\n          <Settings className=\"size-6\" />\r\n          {config.title} Settings\r\n        </h2>\r\n        <p className=\"text-muted-foreground\">\r\n          Customize your {entityType} dashboard experience\r\n        </p>\r\n      </div>\r\n\r\n      <Tabs className=\"w-full\" defaultValue=\"layout\">\r\n        <TabsList className=\"grid w-full grid-cols-3\">\r\n          <TabsTrigger value=\"layout\">Layout</TabsTrigger>\r\n          <TabsTrigger value=\"display\">Display</TabsTrigger>\r\n          <TabsTrigger value=\"refresh\">Refresh</TabsTrigger>\r\n        </TabsList>\r\n\r\n        {/* Layout Settings */}\r\n        <TabsContent className=\"mt-4 space-y-6\" value=\"layout\">\r\n          <div className=\"space-y-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label className=\"text-lg font-semibold\">View Mode</Label>\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                Choose how {entityType}s are displayed\r\n              </p>\r\n              <RadioGroup\r\n                className=\"grid grid-cols-2 gap-4 pt-2\"\r\n                onValueChange={value => setViewMode(value as ViewMode)}\r\n                value={layout.viewMode}\r\n              >\r\n                {availableViewModes.map(mode => {\r\n                  const ModeIcon =\r\n                    viewModeConfig[mode as keyof typeof viewModeConfig]?.icon ||\r\n                    LayoutGrid;\r\n                  const label =\r\n                    viewModeConfig[mode as keyof typeof viewModeConfig]\r\n                      ?.label || mode;\r\n\r\n                  return (\r\n                    <Label\r\n                      key={mode}\r\n                      className=\"flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary cursor-pointer\"\r\n                      htmlFor={`layout-${mode}`}\r\n                    >\r\n                      <RadioGroupItem\r\n                        className=\"sr-only\"\r\n                        id={`layout-${mode}`}\r\n                        value={mode}\r\n                      />\r\n                      <ModeIcon className=\"mb-3 size-6\" />\r\n                      {label}\r\n                    </Label>\r\n                  );\r\n                })}\r\n              </RadioGroup>\r\n            </div>\r\n\r\n            {(layout.viewMode === 'cards' || layout.viewMode === 'grid') && (\r\n              <div className=\"space-y-2\">\r\n                <Label className=\"text-lg font-semibold\">\r\n                  Grid Columns: {layout.gridColumns}\r\n                </Label>\r\n                <Slider\r\n                  defaultValue={[layout.gridColumns]}\r\n                  max={6}\r\n                  min={1}\r\n                  onValueChange={([value]) =>\r\n                    value !== undefined && setGridColumns(value)\r\n                  }\r\n                  step={1}\r\n                />\r\n                <div className=\"flex justify-between text-sm text-muted-foreground\">\r\n                  <span>1 column</span>\r\n                  <span>6 columns</span>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </TabsContent>\r\n\r\n        {/* Display Settings */}\r\n        <TabsContent className=\"mt-4 space-y-6\" value=\"display\">\r\n          <div className=\"space-y-4\">\r\n            <div className=\"flex items-center justify-between space-x-2\">\r\n              <div className=\"space-y-0.5\">\r\n                <Label className=\"text-lg font-semibold\">Compact Mode</Label>\r\n                <p className=\"text-sm text-muted-foreground\">\r\n                  Show more {entityType}s in less space\r\n                </p>\r\n              </div>\r\n              <Switch\r\n                checked={layout.compactMode}\r\n                onCheckedChange={toggleCompactMode}\r\n              />\r\n            </div>\r\n\r\n            {config.enableBulkActions && (\r\n              <div className=\"flex items-center justify-between space-x-2\">\r\n                <div className=\"space-y-0.5\">\r\n                  <Label className=\"text-lg font-semibold\">Bulk Actions</Label>\r\n                  <p className=\"text-sm text-muted-foreground\">\r\n                    Enable selection and bulk operations\r\n                  </p>\r\n                </div>\r\n                <Switch checked={true} disabled />\r\n              </div>\r\n            )}\r\n          </div>\r\n        </TabsContent>\r\n\r\n        {/* Refresh Settings */}\r\n        <TabsContent className=\"mt-4 space-y-6\" value=\"refresh\">\r\n          <div className=\"space-y-4\">\r\n            <div className=\"flex items-center justify-between space-x-2\">\r\n              <div className=\"space-y-0.5\">\r\n                <Label className=\"text-lg font-semibold flex items-center gap-2\">\r\n                  <Zap className=\"size-4\" />\r\n                  Auto Refresh\r\n                </Label>\r\n                <p className=\"text-sm text-muted-foreground\">\r\n                  Automatically refresh {entityType} data\r\n                </p>\r\n              </div>\r\n              <Switch\r\n                checked={monitoring.autoRefresh}\r\n                onCheckedChange={toggleAutoRefresh}\r\n              />\r\n            </div>\r\n\r\n            {monitoring.autoRefresh && (\r\n              <div className=\"space-y-2\">\r\n                <Label className=\"text-lg font-semibold flex items-center gap-2\">\r\n                  <RefreshCw className=\"size-4\" />\r\n                  Refresh Interval\r\n                </Label>\r\n                <div className=\"grid grid-cols-2 gap-2\">\r\n                  {refreshIntervals.map(interval => (\r\n                    <Button\r\n                      key={interval.value}\r\n                      variant={\r\n                        monitoring.refreshInterval === interval.value\r\n                          ? 'default'\r\n                          : 'outline'\r\n                      }\r\n                      size=\"sm\"\r\n                      onClick={() => setRefreshInterval(interval.value)}\r\n                    >\r\n                      {interval.label}\r\n                    </Button>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"flex items-center justify-between space-x-2\">\r\n              <div className=\"space-y-0.5\">\r\n                <Label className=\"text-lg font-semibold\">\r\n                  Real-time Updates\r\n                </Label>\r\n                <p className=\"text-sm text-muted-foreground\">\r\n                  Enable live data updates\r\n                </p>\r\n              </div>\r\n              <Switch\r\n                checked={monitoring.enabled}\r\n                onCheckedChange={setMonitoringEnabled}\r\n              />\r\n            </div>\r\n          </div>\r\n        </TabsContent>\r\n      </Tabs>\r\n\r\n      <div className=\"flex justify-end pt-4 border-t\">\r\n        <Button onClick={resetSettings} variant=\"outline\">\r\n          Reset to Defaults\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DashboardSettings;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAID;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AACA;AACA;AACA;AArBA;;;;;;;;;AAsDA;;CAEC,GACD,MAAM,iBAAiB;IACrB,OAAO;QAAE,MAAM,kNAAA,CAAA,aAAU;QAAE,OAAO;IAAQ;IAC1C,OAAO;QAAE,MAAM,oMAAA,CAAA,QAAK;QAAE,OAAO;IAAQ;IACrC,MAAM;QAAE,MAAM,kMAAA,CAAA,OAAI;QAAE,OAAO;IAAO;IAClC,UAAU;QAAE,MAAM,0MAAA,CAAA,WAAQ;QAAE,OAAO;IAAW;IAC9C,MAAM;QAAE,MAAM,4MAAA,CAAA,UAAO;QAAE,OAAO;IAAO;AACvC;AAgBO,MAAM,oBAAsD,CAAC,EAClE,MAAM,EACN,UAAU,EACV,MAAM,EACN,UAAU,EACV,WAAW,EACX,cAAc,EACd,iBAAiB,EACjB,oBAAoB,EACpB,kBAAkB,EAClB,iBAAiB,EACjB,aAAa,EACb,YAAY,EAAE,EACf;IACC,uCAAuC;IACvC,MAAM,qBAAqB,OAAO,SAAS,IAAI;QAAC;QAAS;QAAS;KAAO;IAEzE,6CAA6C;IAC7C,MAAM,mBAAmB;QACvB;YAAE,OAAO;YAAa,OAAO;QAAK;QAClC;YAAE,OAAO;YAAc,OAAO;QAAM;QACpC;YAAE,OAAO;YAAc,OAAO;QAAM;QACpC;YAAE,OAAO;YAAY,OAAO;QAAM;QAClC;YAAE,OAAO;YAAa,OAAO;QAAO;KACrC;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,cAAc,EAAE,WAAW;;0BAC1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BACnB,OAAO,KAAK;4BAAC;;;;;;;kCAEhB,8OAAC;wBAAE,WAAU;;4BAAwB;4BACnB;4BAAW;;;;;;;;;;;;;0BAI/B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;gBAAS,cAAa;;kCACpC,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAS;;;;;;0CAC5B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;;;;;;;kCAI/B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;wBAAiB,OAAM;kCAC5C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAwB;;;;;;sDACzC,8OAAC;4CAAE,WAAU;;gDAAgC;gDAC/B;gDAAW;;;;;;;sDAEzB,8OAAC,0IAAA,CAAA,aAAU;4CACT,WAAU;4CACV,eAAe,CAAA,QAAS,YAAY;4CACpC,OAAO,OAAO,QAAQ;sDAErB,mBAAmB,GAAG,CAAC,CAAA;gDACtB,MAAM,WACJ,cAAc,CAAC,KAAoC,EAAE,QACrD,kNAAA,CAAA,aAAU;gDACZ,MAAM,QACJ,cAAc,CAAC,KAAoC,EAC/C,SAAS;gDAEf,qBACE,8OAAC,iIAAA,CAAA,QAAK;oDAEJ,WAAU;oDACV,SAAS,CAAC,OAAO,EAAE,MAAM;;sEAEzB,8OAAC,0IAAA,CAAA,iBAAc;4DACb,WAAU;4DACV,IAAI,CAAC,OAAO,EAAE,MAAM;4DACpB,OAAO;;;;;;sEAET,8OAAC;4DAAS,WAAU;;;;;;wDACnB;;mDAVI;;;;;4CAaX;;;;;;;;;;;;gCAIH,CAAC,OAAO,QAAQ,KAAK,WAAW,OAAO,QAAQ,KAAK,MAAM,mBACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;;gDAAwB;gDACxB,OAAO,WAAW;;;;;;;sDAEnC,8OAAC,kIAAA,CAAA,SAAM;4CACL,cAAc;gDAAC,OAAO,WAAW;6CAAC;4CAClC,KAAK;4CACL,KAAK;4CACL,eAAe,CAAC,CAAC,MAAM,GACrB,UAAU,aAAa,eAAe;4CAExC,MAAM;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;wBAAiB,OAAM;kCAC5C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAwB;;;;;;8DACzC,8OAAC;oDAAE,WAAU;;wDAAgC;wDAChC;wDAAW;;;;;;;;;;;;;sDAG1B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,OAAO,WAAW;4CAC3B,iBAAiB;;;;;;;;;;;;gCAIpB,OAAO,iBAAiB,kBACvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAwB;;;;;;8DACzC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAI/C,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;kCAOvC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;wBAAiB,OAAM;kCAC5C,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;;sEACf,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAW;;;;;;;8DAG5B,8OAAC;oDAAE,WAAU;;wDAAgC;wDACpB;wDAAW;;;;;;;;;;;;;sDAGtC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,WAAW,WAAW;4CAC/B,iBAAiB;;;;;;;;;;;;gCAIpB,WAAW,WAAW,kBACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAW;;;;;;;sDAGlC,8OAAC;4CAAI,WAAU;sDACZ,iBAAiB,GAAG,CAAC,CAAA,yBACpB,8OAAC,kIAAA,CAAA,SAAM;oDAEL,SACE,WAAW,eAAe,KAAK,SAAS,KAAK,GACzC,YACA;oDAEN,MAAK;oDACL,SAAS,IAAM,mBAAmB,SAAS,KAAK;8DAE/C,SAAS,KAAK;mDATV,SAAS,KAAK;;;;;;;;;;;;;;;;8CAgB7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;8DAAwB;;;;;;8DAGzC,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAI/C,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,WAAW,OAAO;4CAC3B,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAe,SAAQ;8BAAU;;;;;;;;;;;;;;;;;AAM1D;uCAEe", "debugId": null}}, {"offset": {"line": 2722, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/delegations/dashboard/DelegationDashboardSettings.tsx"], "sourcesContent": ["/**\r\n * @file Delegation-specific dashboard settings component\r\n * @module components/delegations/dashboard/DelegationDashboardSettings\r\n */\r\n\r\n'use client';\r\n\r\nimport React from 'react';\r\n\r\nimport { DashboardSettings } from '@/components/dashboard/DashboardSettings';\r\nimport type { DashboardConfig } from '@/components/dashboard/types';\r\nimport type { Delegation } from '@/lib/types/domain';\r\nimport { useDashboardStore } from '@/hooks/domain/useDashboardStore';\r\n\r\n/**\r\n * Delegation dashboard configuration\r\n */\r\nconst delegationDashboardConfig: DashboardConfig<Delegation> = {\r\n  entityType: 'delegation',\r\n  title: 'Delegation Dashboard',\r\n  description: 'Track and manage all your events, trips, and delegate information.',\r\n  viewModes: ['cards', 'table', 'list', 'calendar'],\r\n  defaultViewMode: 'cards',\r\n  enableBulkActions: true,\r\n  enableExport: true,\r\n  refreshInterval: 30000,\r\n};\r\n\r\n/**\r\n * Props for DelegationDashboardSettings component\r\n */\r\ninterface DelegationDashboardSettingsProps {\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * Delegation-specific dashboard settings component that uses the generic\r\n * DashboardSettings component with delegation-specific configuration.\r\n * \r\n * This component:\r\n * - Uses the generic dashboard store for delegations\r\n * - Provides delegation-specific configuration\r\n * - Maintains all delegation-specific state and actions\r\n * \r\n * @param props - Component props\r\n * @returns JSX element representing the delegation dashboard settings\r\n */\r\nexport const DelegationDashboardSettings: React.FC<DelegationDashboardSettingsProps> = ({\r\n  className = '',\r\n}) => {\r\n  // Get delegation dashboard store\r\n  const dashboardStore = useDashboardStore('delegation');\r\n  const {\r\n    layout,\r\n    monitoring,\r\n    setViewMode,\r\n    setGridColumns,\r\n    toggleCompactMode,\r\n    setMonitoringEnabled,\r\n    setRefreshInterval,\r\n    toggleAutoRefresh,\r\n    resetSettings,\r\n  } = dashboardStore();\r\n\r\n  return (\r\n    <DashboardSettings\r\n      config={delegationDashboardConfig}\r\n      entityType=\"delegation\"\r\n      layout={layout}\r\n      monitoring={monitoring}\r\n      setViewMode={setViewMode}\r\n      setGridColumns={setGridColumns}\r\n      toggleCompactMode={toggleCompactMode}\r\n      setMonitoringEnabled={setMonitoringEnabled}\r\n      setRefreshInterval={setRefreshInterval}\r\n      toggleAutoRefresh={toggleAutoRefresh}\r\n      resetSettings={resetSettings}\r\n      className={className}\r\n    />\r\n  );\r\n};\r\n\r\nexport default DelegationDashboardSettings;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAMD;AAGA;AAPA;;;;AASA;;CAEC,GACD,MAAM,4BAAyD;IAC7D,YAAY;IACZ,OAAO;IACP,aAAa;IACb,WAAW;QAAC;QAAS;QAAS;QAAQ;KAAW;IACjD,iBAAiB;IACjB,mBAAmB;IACnB,cAAc;IACd,iBAAiB;AACnB;AAqBO,MAAM,8BAA0E,CAAC,EACtF,YAAY,EAAE,EACf;IACC,iCAAiC;IACjC,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,oBAAiB,AAAD,EAAE;IACzC,MAAM,EACJ,MAAM,EACN,UAAU,EACV,WAAW,EACX,cAAc,EACd,iBAAiB,EACjB,oBAAoB,EACpB,kBAAkB,EAClB,iBAAiB,EACjB,aAAa,EACd,GAAG;IAEJ,qBACE,8OAAC,oJAAA,CAAA,oBAAiB;QAChB,QAAQ;QACR,YAAW;QACX,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,gBAAgB;QAChB,mBAAmB;QACnB,sBAAsB;QACtB,oBAAoB;QACpB,mBAAmB;QACnB,eAAe;QACf,WAAW;;;;;;AAGjB;uCAEe", "debugId": null}}, {"offset": {"line": 2783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/delegations/dashboard/index.ts"], "sourcesContent": ["/**\r\n * @file Dashboard components barrel export\r\n * @module components/delegations/dashboard\r\n */\r\n\r\nexport { default as DelegationDashboard } from './DelegationDashboard';\r\nexport { default as DelegationDashboardSettings } from './DelegationDashboardSettings';\r\n"], "names": [], "mappings": "AAAA;;;CAGC;AAED;AACA", "debugId": null}}, {"offset": {"line": 2817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/calendar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\r\nimport * as React from 'react';\r\nimport { DayPicker } from 'react-day-picker';\r\n\r\nimport { buttonVariants } from '@/components/ui/button';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>;\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: CalendarProps) {\r\n  return (\r\n    <DayPicker\r\n      className={cn('p-3', className)}\r\n      classNames={{\r\n        caption: 'flex justify-center pt-1 relative items-center',\r\n        caption_label: 'text-sm font-medium',\r\n        cell: 'h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20',\r\n        day: cn(\r\n          buttonVariants({ variant: 'ghost' }),\r\n          'h-9 w-9 p-0 font-normal aria-selected:opacity-100'\r\n        ),\r\n        day_disabled: 'text-muted-foreground opacity-50',\r\n        day_hidden: 'invisible',\r\n        day_outside:\r\n          'day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground',\r\n        day_range_end: 'day-range-end',\r\n        day_range_middle:\r\n          'aria-selected:bg-accent aria-selected:text-accent-foreground',\r\n        day_selected:\r\n          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',\r\n        day_today: 'bg-accent text-accent-foreground',\r\n        head_cell:\r\n          'text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]',\r\n        head_row: 'flex',\r\n        month: 'space-y-4',\r\n        months: 'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',\r\n        nav: 'space-x-1 flex items-center',\r\n        nav_button: cn(\r\n          buttonVariants({ variant: 'outline' }),\r\n          'h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100'\r\n        ),\r\n        nav_button_next: 'absolute right-1',\r\n        nav_button_previous: 'absolute left-1',\r\n        row: 'flex w-full mt-2',\r\n        table: 'w-full border-collapse space-y-1',\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        IconLeft: ({ className, ...props }) => (\r\n          <ChevronLeft className={cn('h-4 w-4', className)} {...props} />\r\n        ),\r\n        IconRight: ({ className, ...props }) => (\r\n          <ChevronRight className={cn('h-4 w-4', className)} {...props} />\r\n        ),\r\n      }}\r\n      showOutsideDays={showOutsideDays}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\nCalendar.displayName = 'Calendar';\r\n\r\nexport { Calendar };\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAEA;AAEA;AACA;AAAA;AAPA;;;;;;AAWA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACW;IACd,qBACE,8OAAC,8JAAA,CAAA,YAAS;QACR,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,SAAS;YACT,eAAe;YACf,MAAM;YACN,KAAK,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,cAAc;YACd,YAAY;YACZ,aACE;YACF,eAAe;YACf,kBACE;YACF,cACE;YACF,WAAW;YACX,WACE;YACF,UAAU;YACV,OAAO;YACP,QAAQ;YACR,KAAK;YACL,YAAY,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,iBAAiB;YACjB,qBAAqB;YACrB,KAAK;YACL,OAAO;YACP,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBAChC,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW;oBAAa,GAAG,KAAK;;;;;;YAE7D,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBACjC,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW;oBAAa,GAAG,KAAK;;;;;;QAEhE;QACA,iBAAiB;QAChB,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2898, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Popover = PopoverPrimitive.Root;\r\n\r\nconst PopoverTrigger = PopoverPrimitive.Trigger;\r\n\r\nconst PopoverClose = PopoverPrimitive.Close;\r\n\r\nconst PopoverContent = React.forwardRef<\r\n  React.ElementRef<typeof PopoverPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\r\n>(({ align = 'center', className, sideOffset = 4, ...props }, ref) => (\r\n  <PopoverPrimitive.Portal>\r\n    <PopoverPrimitive.Content\r\n      align={align}\r\n      className={cn(\r\n        'z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]',\r\n        className\r\n      )}\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      {...props}\r\n    />\r\n  </PopoverPrimitive.Portal>\r\n));\r\nPopoverContent.displayName = PopoverPrimitive.Content.displayName;\r\n\r\nexport { Popover, PopoverContent, PopoverTrigger, PopoverClose };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,eAAe,mKAAA,CAAA,QAAsB;AAE3C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,QAAQ,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,OAAO;YACP,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,geACA;YAEF,KAAK;YACL,YAAY;YACX,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2942, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/sheet.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as SheetPrimitive from '@radix-ui/react-dialog';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\nimport { X } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Sheet = SheetPrimitive.Root;\r\n\r\nconst SheetTrigger = SheetPrimitive.Trigger;\r\n\r\nconst SheetClose = SheetPrimitive.Close;\r\n\r\nconst SheetPortal = SheetPrimitive.Portal;\r\n\r\nconst SheetOverlay = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Overlay\r\n    className={cn(\r\n      'fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  />\r\n));\r\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName;\r\n\r\nconst sheetVariants = cva(\r\n  'fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out',\r\n  {\r\n    defaultVariants: {\r\n      side: 'right',\r\n    },\r\n    variants: {\r\n      side: {\r\n        bottom:\r\n          'inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom',\r\n        left: 'inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm',\r\n        right:\r\n          'inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm',\r\n        top: 'inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top',\r\n      },\r\n    },\r\n  }\r\n);\r\n\r\ninterface SheetContentProps\r\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\r\n    VariantProps<typeof sheetVariants> {}\r\n\r\nconst SheetContent = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Content>,\r\n  SheetContentProps\r\n>(({ children, className, side = 'right', ...props }, ref) => (\r\n  <SheetPortal>\r\n    <SheetOverlay />\r\n    <SheetPrimitive.Content\r\n      className={cn(sheetVariants({ side }), className)}\r\n      ref={ref}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\r\n        <X className=\"size-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </SheetPrimitive.Close>\r\n    </SheetPrimitive.Content>\r\n  </SheetPortal>\r\n));\r\nSheetContent.displayName = SheetPrimitive.Content.displayName;\r\n\r\nconst SheetHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      'flex flex-col space-y-2 text-center sm:text-left',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nSheetHeader.displayName = 'SheetHeader';\r\n\r\nconst SheetFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nSheetFooter.displayName = 'SheetFooter';\r\n\r\nconst SheetTitle = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Title\r\n    className={cn('text-lg font-semibold text-foreground', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSheetTitle.displayName = SheetPrimitive.Title.displayName;\r\n\r\nconst SheetDescription = React.forwardRef<\r\n  React.ElementRef<typeof SheetPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <SheetPrimitive.Description\r\n    className={cn('text-sm text-muted-foreground', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSheetDescription.displayName = SheetPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Sheet,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetFooter,\r\n  SheetHeader,\r\n  SheetOverlay,\r\n  SheetPortal,\r\n  SheetTitle,\r\n  SheetTrigger,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAPA;;;;;;;AASA,MAAM,QAAQ,kKAAA,CAAA,OAAmB;AAEjC,MAAM,eAAe,kKAAA,CAAA,UAAsB;AAE3C,MAAM,aAAa,kKAAA,CAAA,QAAoB;AAEvC,MAAM,cAAc,kKAAA,CAAA,SAAqB;AAEzC,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAsB;QACrB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,iBAAiB;QACf,MAAM;IACR;IACA,UAAU;QACR,MAAM;YACJ,QACE;YACF,MAAM;YACN,OACE;YACF,KAAK;QACP;IACF;AACF;AAOF,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACvC,KAAK;gBACJ,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,aAAa,WAAW,GAAG,kKAAA,CAAA,UAAsB,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAoB;QACnB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACvD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG,kKAAA,CAAA,QAAoB,CAAC,WAAW;AAEzD,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA0B;QACzB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,kKAAA,CAAA,cAA0B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/delegations/DelegationFilters.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport {\r\n  Filter,\r\n  X,\r\n  Search,\r\n  CalendarDays,\r\n  CheckCircle,\r\n  Flag,\r\n  Users,\r\n  MapPin,\r\n  Briefcase,\r\n  Car,\r\n} from 'lucide-react';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { Calendar as CalendarComponent } from '@/components/ui/calendar';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n  SheetTrigger,\r\n} from '@/components/ui/sheet';\r\nimport { cn } from '@/lib/utils';\r\nimport { format } from 'date-fns';\r\nimport type { DateRange } from 'react-day-picker';\r\n\r\n// Delegation filter values interface\r\nexport interface DelegationFilterValues {\r\n  search: string;\r\n  status: string[];\r\n  dateRange: {\r\n    from?: Date | undefined;\r\n    to?: Date | undefined;\r\n  };\r\n  location: string[];\r\n  drivers: string[];\r\n  escorts: string[];\r\n  vehicles: string[];\r\n}\r\n\r\n// Employee interface for filtering\r\ninterface EmployeeOption {\r\n  id: string;\r\n  name: string;\r\n  role: string;\r\n}\r\n\r\n// Vehicle interface for filtering\r\ninterface VehicleOption {\r\n  id: string;\r\n  name: string;\r\n  type: string;\r\n}\r\n\r\n// DelegationFilters props interface\r\ninterface DelegationFiltersProps {\r\n  onFiltersChange?: (filters: DelegationFilterValues) => void;\r\n  className?: string;\r\n  initialFilters?: Partial<DelegationFilterValues>;\r\n  employeesList?: EmployeeOption[];\r\n  vehiclesList?: VehicleOption[];\r\n  locationsList?: string[];\r\n}\r\n\r\n// Delegation status options\r\nconst DELEGATION_STATUS_OPTIONS = [\r\n  {\r\n    value: 'Planned',\r\n    label: 'Planned',\r\n    icon: Flag,\r\n    color: 'border-blue-200 text-blue-700',\r\n  },\r\n  {\r\n    value: 'Confirmed',\r\n    label: 'Confirmed',\r\n    icon: CheckCircle,\r\n    color: 'border-green-200 text-green-700',\r\n  },\r\n  {\r\n    value: 'In_Progress',\r\n    label: 'In Progress',\r\n    icon: Flag,\r\n    color: 'border-yellow-200 text-yellow-700',\r\n  },\r\n  {\r\n    value: 'Completed',\r\n    label: 'Completed',\r\n    icon: CheckCircle,\r\n    color: 'border-emerald-200 text-emerald-700',\r\n  },\r\n  {\r\n    value: 'Cancelled',\r\n    label: 'Cancelled',\r\n    icon: X,\r\n    color: 'border-red-200 text-red-700',\r\n  },\r\n  {\r\n    value: 'No_details',\r\n    label: 'No Details',\r\n    icon: Flag,\r\n    color: 'border-gray-200 text-gray-700',\r\n  },\r\n];\r\n\r\n/**\r\n * Modern delegation filters component with popover and sheet layouts\r\n */\r\nexport const DelegationFilters: React.FC<DelegationFiltersProps> = ({\r\n  onFiltersChange,\r\n  className,\r\n  initialFilters = {},\r\n  employeesList = [],\r\n  vehiclesList = [],\r\n  locationsList = [],\r\n}) => {\r\n  const [isSheetOpen, setIsSheetOpen] = useState(false);\r\n\r\n  // Initialize filters with defaults\r\n  const [filters, setFilters] = useState<DelegationFilterValues>({\r\n    search: '',\r\n    status: [],\r\n    dateRange: {},\r\n    location: [],\r\n    drivers: [],\r\n    escorts: [],\r\n    vehicles: [],\r\n    ...initialFilters,\r\n  });\r\n\r\n  // Update filters and notify parent\r\n  const updateFilters = (newFilters: Partial<DelegationFilterValues>) => {\r\n    const updatedFilters = { ...filters, ...newFilters };\r\n    setFilters(updatedFilters);\r\n    onFiltersChange?.(updatedFilters);\r\n  };\r\n\r\n  // Clear all filters\r\n  const clearAllFilters = () => {\r\n    const clearedFilters: DelegationFilterValues = {\r\n      search: '',\r\n      status: [],\r\n      dateRange: {},\r\n      location: [],\r\n      drivers: [],\r\n      escorts: [],\r\n      vehicles: [],\r\n    };\r\n    setFilters(clearedFilters);\r\n    onFiltersChange?.(clearedFilters);\r\n  };\r\n\r\n  // Toggle status filter\r\n  const toggleStatus = (status: string) => {\r\n    const newStatus = filters.status.includes(status)\r\n      ? filters.status.filter(s => s !== status)\r\n      : [...filters.status, status];\r\n    updateFilters({ status: newStatus });\r\n  };\r\n\r\n  // Toggle location filter\r\n  const toggleLocation = (location: string) => {\r\n    const newLocation = filters.location.includes(location)\r\n      ? filters.location.filter(l => l !== location)\r\n      : [...filters.location, location];\r\n    updateFilters({ location: newLocation });\r\n  };\r\n\r\n  // Toggle driver filter\r\n  const toggleDriver = (driverId: string) => {\r\n    const newDrivers = filters.drivers.includes(driverId)\r\n      ? filters.drivers.filter(d => d !== driverId)\r\n      : [...filters.drivers, driverId];\r\n    updateFilters({ drivers: newDrivers });\r\n  };\r\n\r\n  // Toggle escort filter\r\n  const toggleEscort = (escortId: string) => {\r\n    const newEscorts = filters.escorts.includes(escortId)\r\n      ? filters.escorts.filter(e => e !== escortId)\r\n      : [...filters.escorts, escortId];\r\n    updateFilters({ escorts: newEscorts });\r\n  };\r\n\r\n  // Toggle vehicle filter\r\n  const toggleVehicle = (vehicleId: string) => {\r\n    const newVehicles = filters.vehicles.includes(vehicleId)\r\n      ? filters.vehicles.filter(v => v !== vehicleId)\r\n      : [...filters.vehicles, vehicleId];\r\n    updateFilters({ vehicles: newVehicles });\r\n  };\r\n\r\n  // Handle date range selection\r\n  const handleDateRangeSelect = (range: DateRange | undefined) => {\r\n    updateFilters({\r\n      dateRange: {\r\n        from: range?.from ?? undefined,\r\n        to: range?.to ?? undefined,\r\n      },\r\n    });\r\n  };\r\n\r\n  // Count active filters\r\n  const activeFiltersCount =\r\n    (filters.search ? 1 : 0) +\r\n    filters.status.length +\r\n    filters.location.length +\r\n    filters.drivers.length +\r\n    filters.escorts.length +\r\n    filters.vehicles.length +\r\n    (filters.dateRange.from || filters.dateRange.to ? 1 : 0);\r\n\r\n  // Render status filter popover\r\n  const StatusFilterPopover = () => (\r\n    <Popover>\r\n      <PopoverTrigger asChild>\r\n        <Button variant=\"outline\" className=\"gap-2\">\r\n          <CheckCircle className=\"size-4\" />\r\n          Status\r\n          {filters.status.length > 0 && (\r\n            <Badge\r\n              variant=\"secondary\"\r\n              className=\"ml-1 h-5 min-w-5 px-1.5 text-xs\"\r\n            >\r\n              {filters.status.length}\r\n            </Badge>\r\n          )}\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-56 p-3\" align=\"start\">\r\n        <div className=\"space-y-3\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h4 className=\"font-medium text-sm\">Delegation Status</h4>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => updateFilters({ status: [] })}\r\n              className=\"h-auto p-1 text-xs\"\r\n            >\r\n              Clear\r\n            </Button>\r\n          </div>\r\n          <Separator />\r\n          <div className=\"space-y-2\">\r\n            {DELEGATION_STATUS_OPTIONS.map(option => {\r\n              const IconComponent = option.icon;\r\n              return (\r\n                <div key={option.value} className=\"flex items-center gap-2\">\r\n                  <Checkbox\r\n                    id={`status-${option.value}`}\r\n                    checked={filters.status.includes(option.value)}\r\n                    onCheckedChange={() => toggleStatus(option.value)}\r\n                  />\r\n                  <Label\r\n                    htmlFor={`status-${option.value}`}\r\n                    className=\"flex items-center gap-2 cursor-pointer text-sm flex-1\"\r\n                  >\r\n                    <IconComponent className=\"size-3\" />\r\n                    <Badge\r\n                      variant=\"outline\"\r\n                      className={cn('text-xs border', option.color)}\r\n                    >\r\n                      {option.label}\r\n                    </Badge>\r\n                  </Label>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n\r\n  // Render date range filter popover\r\n  const DateRangeFilterPopover = () => {\r\n    return (\r\n      <Popover>\r\n        <PopoverTrigger asChild>\r\n          <Button variant=\"outline\" className=\"gap-2\">\r\n            <CalendarDays className=\"size-4\" />\r\n            Date Range\r\n            {(filters.dateRange.from || filters.dateRange.to) && (\r\n              <Badge\r\n                variant=\"secondary\"\r\n                className=\"ml-1 h-5 min-w-5 px-1.5 text-xs\"\r\n              >\r\n                1\r\n              </Badge>\r\n            )}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n          <div className=\"p-3\">\r\n            <div className=\"flex items-center justify-between mb-3\">\r\n              <h4 className=\"font-medium text-sm\">Delegation Date Range</h4>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                onClick={() => updateFilters({ dateRange: {} })}\r\n                className=\"h-auto p-1 text-xs\"\r\n              >\r\n                Clear\r\n              </Button>\r\n            </div>\r\n            <CalendarComponent\r\n              mode=\"range\"\r\n              selected={{\r\n                from: filters.dateRange.from,\r\n                to: filters.dateRange.to,\r\n              }}\r\n              onSelect={handleDateRangeSelect}\r\n              numberOfMonths={2}\r\n              className=\"rounded-md border-0\"\r\n            />\r\n            {/* Helper text */}\r\n            <div className=\"mt-3 text-xs text-muted-foreground text-center\">\r\n              {filters.dateRange.from && !filters.dateRange.to\r\n                ? 'Select end date to complete range'\r\n                : 'Click start date, then end date'}\r\n            </div>\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n    );\r\n  };\r\n\r\n  // Render location filter popover\r\n  const LocationFilterPopover = () => (\r\n    <Popover>\r\n      <PopoverTrigger asChild>\r\n        <Button variant=\"outline\" className=\"gap-2\">\r\n          <MapPin className=\"size-4\" />\r\n          Location\r\n          {filters.location.length > 0 && (\r\n            <Badge\r\n              variant=\"secondary\"\r\n              className=\"ml-1 h-5 min-w-5 px-1.5 text-xs\"\r\n            >\r\n              {filters.location.length}\r\n            </Badge>\r\n          )}\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-64 p-3\" align=\"start\">\r\n        <div className=\"space-y-3\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h4 className=\"font-medium text-sm\">Delegation Location</h4>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => updateFilters({ location: [] })}\r\n              className=\"h-auto p-1 text-xs\"\r\n            >\r\n              Clear\r\n            </Button>\r\n          </div>\r\n          <Separator />\r\n          <div className=\"space-y-2 max-h-48 overflow-y-auto\">\r\n            {locationsList.map(location => (\r\n              <div key={location} className=\"flex items-center gap-2\">\r\n                <Checkbox\r\n                  id={`location-${location}`}\r\n                  checked={filters.location.includes(location)}\r\n                  onCheckedChange={() => toggleLocation(location)}\r\n                />\r\n                <Label\r\n                  htmlFor={`location-${location}`}\r\n                  className=\"flex items-center gap-2 cursor-pointer text-sm flex-1\"\r\n                >\r\n                  <MapPin className=\"size-3\" />\r\n                  <span>{location}</span>\r\n                </Label>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n\r\n  // Render drivers filter popover\r\n  const DriversFilterPopover = () => (\r\n    <Popover>\r\n      <PopoverTrigger asChild>\r\n        <Button variant=\"outline\" className=\"gap-2\">\r\n          <Users className=\"size-4\" />\r\n          Drivers\r\n          {filters.drivers.length > 0 && (\r\n            <Badge\r\n              variant=\"secondary\"\r\n              className=\"ml-1 h-5 min-w-5 px-1.5 text-xs\"\r\n            >\r\n              {filters.drivers.length}\r\n            </Badge>\r\n          )}\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-64 p-3\" align=\"start\">\r\n        <div className=\"space-y-3\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h4 className=\"font-medium text-sm\">Assigned Drivers</h4>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => updateFilters({ drivers: [] })}\r\n              className=\"h-auto p-1 text-xs\"\r\n            >\r\n              Clear\r\n            </Button>\r\n          </div>\r\n          <Separator />\r\n          <div className=\"space-y-2 max-h-48 overflow-y-auto\">\r\n            {employeesList\r\n              .filter(emp => emp.role === 'driver')\r\n              .map(driver => (\r\n                <div key={driver.id} className=\"flex items-center gap-2\">\r\n                  <Checkbox\r\n                    id={`driver-${driver.id}`}\r\n                    checked={filters.drivers.includes(driver.id)}\r\n                    onCheckedChange={() => toggleDriver(driver.id)}\r\n                  />\r\n                  <Label\r\n                    htmlFor={`driver-${driver.id}`}\r\n                    className=\"flex items-center gap-2 cursor-pointer text-sm flex-1\"\r\n                  >\r\n                    <Users className=\"size-3\" />\r\n                    <div className=\"flex flex-col\">\r\n                      <span>{driver.name}</span>\r\n                      <span className=\"text-xs text-muted-foreground capitalize\">\r\n                        {driver.role}\r\n                      </span>\r\n                    </div>\r\n                  </Label>\r\n                </div>\r\n              ))}\r\n          </div>\r\n        </div>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n\r\n  // Render vehicles filter popover\r\n  const VehiclesFilterPopover = () => (\r\n    <Popover>\r\n      <PopoverTrigger asChild>\r\n        <Button variant=\"outline\" className=\"gap-2\">\r\n          <Car className=\"size-4\" />\r\n          Vehicles\r\n          {filters.vehicles.length > 0 && (\r\n            <Badge\r\n              variant=\"secondary\"\r\n              className=\"ml-1 h-5 min-w-5 px-1.5 text-xs\"\r\n            >\r\n              {filters.vehicles.length}\r\n            </Badge>\r\n          )}\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-64 p-3\" align=\"start\">\r\n        <div className=\"space-y-3\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h4 className=\"font-medium text-sm\">Assigned Vehicles</h4>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              onClick={() => updateFilters({ vehicles: [] })}\r\n              className=\"h-auto p-1 text-xs\"\r\n            >\r\n              Clear\r\n            </Button>\r\n          </div>\r\n          <Separator />\r\n          <div className=\"space-y-2 max-h-48 overflow-y-auto\">\r\n            {vehiclesList.map(vehicle => (\r\n              <div key={vehicle.id} className=\"flex items-center gap-2\">\r\n                <Checkbox\r\n                  id={`vehicle-${vehicle.id}`}\r\n                  checked={filters.vehicles.includes(vehicle.id)}\r\n                  onCheckedChange={() => toggleVehicle(vehicle.id)}\r\n                />\r\n                <Label\r\n                  htmlFor={`vehicle-${vehicle.id}`}\r\n                  className=\"flex items-center gap-2 cursor-pointer text-sm flex-1\"\r\n                >\r\n                  <Car className=\"size-3\" />\r\n                  <div className=\"flex flex-col\">\r\n                    <span>{vehicle.name}</span>\r\n                    <span className=\"text-xs text-muted-foreground\">\r\n                      {vehicle.type}\r\n                    </span>\r\n                  </div>\r\n                </Label>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n\r\n  return (\r\n    <div className={cn('flex flex-col gap-4', className)}>\r\n      {/* Search Bar */}\r\n      <div className=\"relative\">\r\n        <Search className=\"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground\" />\r\n        <Input\r\n          placeholder=\"Search delegations (Event, Location, Delegate, Status...)\"\r\n          value={filters.search}\r\n          onChange={e => updateFilters({ search: e.target.value })}\r\n          className=\"pl-10\"\r\n        />\r\n      </div>\r\n\r\n      {/* Filter Controls */}\r\n      <div className=\"flex flex-wrap items-center gap-3\">\r\n        {/* Desktop Filter Buttons */}\r\n        <div className=\"hidden md:flex items-center gap-2\">\r\n          <StatusFilterPopover />\r\n          <DateRangeFilterPopover />\r\n          {locationsList.length > 0 && <LocationFilterPopover />}\r\n          {employeesList.some(emp => emp.role === 'driver') && (\r\n            <DriversFilterPopover />\r\n          )}\r\n          {vehiclesList.length > 0 && <VehiclesFilterPopover />}\r\n        </div>\r\n\r\n        {/* Mobile Filter Sheet */}\r\n        <div className=\"md:hidden\">\r\n          <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>\r\n            <SheetTrigger asChild>\r\n              <Button variant=\"outline\" className=\"gap-2\">\r\n                <Filter className=\"size-4\" />\r\n                Filters\r\n                {activeFiltersCount > 0 && (\r\n                  <Badge\r\n                    variant=\"secondary\"\r\n                    className=\"ml-1 h-5 min-w-5 px-1.5 text-xs\"\r\n                  >\r\n                    {activeFiltersCount}\r\n                  </Badge>\r\n                )}\r\n              </Button>\r\n            </SheetTrigger>\r\n            <SheetContent\r\n              side=\"bottom\"\r\n              className=\"max-h-[80vh] overflow-y-auto\"\r\n            >\r\n              <SheetHeader>\r\n                <SheetTitle>Filter Delegations</SheetTitle>\r\n                <SheetDescription>\r\n                  Apply filters to find specific delegations\r\n                </SheetDescription>\r\n              </SheetHeader>\r\n              <div className=\"mt-6 space-y-6\">\r\n                {/* Mobile Status Filter */}\r\n                <div className=\"space-y-3\">\r\n                  <Label className=\"text-sm font-medium\">Status</Label>\r\n                  <div className=\"grid gap-2\">\r\n                    {DELEGATION_STATUS_OPTIONS.map(option => {\r\n                      const IconComponent = option.icon;\r\n                      return (\r\n                        <div\r\n                          key={option.value}\r\n                          className=\"flex items-center gap-2 p-2 border rounded-md\"\r\n                        >\r\n                          <Checkbox\r\n                            id={`mobile-status-${option.value}`}\r\n                            checked={filters.status.includes(option.value)}\r\n                            onCheckedChange={() => toggleStatus(option.value)}\r\n                          />\r\n                          <Label\r\n                            htmlFor={`mobile-status-${option.value}`}\r\n                            className=\"cursor-pointer text-sm flex-1 flex items-center gap-2\"\r\n                          >\r\n                            <IconComponent className=\"size-3\" />\r\n                            {option.label}\r\n                          </Label>\r\n                        </div>\r\n                      );\r\n                    })}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Mobile Date Range Filter */}\r\n                <div className=\"space-y-3\">\r\n                  <Label className=\"text-sm font-medium\">Date Range</Label>\r\n                  <div className=\"border rounded-md p-3\">\r\n                    <CalendarComponent\r\n                      mode=\"range\"\r\n                      selected={{\r\n                        from: filters.dateRange.from,\r\n                        to: filters.dateRange.to,\r\n                      }}\r\n                      onSelect={handleDateRangeSelect}\r\n                      numberOfMonths={1}\r\n                      className=\"rounded-md border-0\"\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Mobile Clear All Button */}\r\n                {activeFiltersCount > 0 && (\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    onClick={clearAllFilters}\r\n                    className=\"w-full gap-2\"\r\n                  >\r\n                    <X className=\"size-4\" />\r\n                    Clear All Filters ({activeFiltersCount})\r\n                  </Button>\r\n                )}\r\n              </div>\r\n            </SheetContent>\r\n          </Sheet>\r\n        </div>\r\n\r\n        {/* Clear All Filters (Desktop) */}\r\n        {activeFiltersCount > 0 && (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={clearAllFilters}\r\n            className=\"gap-1 text-muted-foreground hover:text-foreground hidden md:flex\"\r\n          >\r\n            <X className=\"size-3\" />\r\n            Clear ({activeFiltersCount})\r\n          </Button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Active Filters Display */}\r\n      {activeFiltersCount > 0 && (\r\n        <div className=\"flex flex-wrap items-center gap-2\">\r\n          <span className=\"text-sm text-muted-foreground\">Active filters:</span>\r\n\r\n          {filters.status.map(status => {\r\n            const option = DELEGATION_STATUS_OPTIONS.find(\r\n              opt => opt.value === status\r\n            );\r\n            if (!option) return null;\r\n            return (\r\n              <Badge key={status} variant=\"secondary\" className=\"gap-1\">\r\n                {option.label}\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"h-auto p-0 ml-1 hover:bg-transparent\"\r\n                  onClick={() => toggleStatus(status)}\r\n                >\r\n                  <X className=\"size-3\" />\r\n                </Button>\r\n              </Badge>\r\n            );\r\n          })}\r\n\r\n          {filters.location.map(location => (\r\n            <Badge key={location} variant=\"secondary\" className=\"gap-1\">\r\n              Location: {location}\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"h-auto p-0 ml-1 hover:bg-transparent\"\r\n                onClick={() => toggleLocation(location)}\r\n              >\r\n                <X className=\"size-3\" />\r\n              </Button>\r\n            </Badge>\r\n          ))}\r\n\r\n          {filters.drivers.map(driverId => {\r\n            const driver = employeesList.find(emp => emp.id === driverId);\r\n            return (\r\n              <Badge key={driverId} variant=\"secondary\" className=\"gap-1\">\r\n                Driver: {driver?.name || 'Unknown'}\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"h-auto p-0 ml-1 hover:bg-transparent\"\r\n                  onClick={() => toggleDriver(driverId)}\r\n                >\r\n                  <X className=\"size-3\" />\r\n                </Button>\r\n              </Badge>\r\n            );\r\n          })}\r\n\r\n          {filters.vehicles.map(vehicleId => {\r\n            const vehicle = vehiclesList.find(v => v.id === vehicleId);\r\n            return (\r\n              <Badge key={vehicleId} variant=\"secondary\" className=\"gap-1\">\r\n                Vehicle: {vehicle?.name || 'Unknown'}\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"h-auto p-0 ml-1 hover:bg-transparent\"\r\n                  onClick={() => toggleVehicle(vehicleId)}\r\n                >\r\n                  <X className=\"size-3\" />\r\n                </Button>\r\n              </Badge>\r\n            );\r\n          })}\r\n\r\n          {(filters.dateRange.from || filters.dateRange.to) && (\r\n            <Badge variant=\"secondary\" className=\"gap-1\">\r\n              Date:{' '}\r\n              {filters.dateRange.from\r\n                ? format(filters.dateRange.from, 'MMM d')\r\n                : '?'}{' '}\r\n              -{' '}\r\n              {filters.dateRange.to\r\n                ? format(filters.dateRange.to, 'MMM d, yyyy')\r\n                : '?'}\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"h-auto p-0 ml-1 hover:bg-transparent\"\r\n                onClick={() => updateFilters({ dateRange: {} })}\r\n              >\r\n                <X className=\"size-3\" />\r\n              </Button>\r\n            </Badge>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\n// Export as default to maintain compatibility\r\nexport default DelegationFilters;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAQA;AAAA;AACA;AArCA;;;;;;;;;;;;;;;AA8EA,4BAA4B;AAC5B,MAAM,4BAA4B;IAChC;QACE,OAAO;QACP,OAAO;QACP,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,2NAAA,CAAA,cAAW;QACjB,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,2NAAA,CAAA,cAAW;QACjB,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,4LAAA,CAAA,IAAC;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;IACT;CACD;AAKM,MAAM,oBAAsD,CAAC,EAClE,eAAe,EACf,SAAS,EACT,iBAAiB,CAAC,CAAC,EACnB,gBAAgB,EAAE,EAClB,eAAe,EAAE,EACjB,gBAAgB,EAAE,EACnB;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,mCAAmC;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;QAC7D,QAAQ;QACR,QAAQ,EAAE;QACV,WAAW,CAAC;QACZ,UAAU,EAAE;QACZ,SAAS,EAAE;QACX,SAAS,EAAE;QACX,UAAU,EAAE;QACZ,GAAG,cAAc;IACnB;IAEA,mCAAmC;IACnC,MAAM,gBAAgB,CAAC;QACrB,MAAM,iBAAiB;YAAE,GAAG,OAAO;YAAE,GAAG,UAAU;QAAC;QACnD,WAAW;QACX,kBAAkB;IACpB;IAEA,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,MAAM,iBAAyC;YAC7C,QAAQ;YACR,QAAQ,EAAE;YACV,WAAW,CAAC;YACZ,UAAU,EAAE;YACZ,SAAS,EAAE;YACX,SAAS,EAAE;YACX,UAAU,EAAE;QACd;QACA,WAAW;QACX,kBAAkB;IACpB;IAEA,uBAAuB;IACvB,MAAM,eAAe,CAAC;QACpB,MAAM,YAAY,QAAQ,MAAM,CAAC,QAAQ,CAAC,UACtC,QAAQ,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,UACjC;eAAI,QAAQ,MAAM;YAAE;SAAO;QAC/B,cAAc;YAAE,QAAQ;QAAU;IACpC;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc,QAAQ,QAAQ,CAAC,QAAQ,CAAC,YAC1C,QAAQ,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,YACnC;eAAI,QAAQ,QAAQ;YAAE;SAAS;QACnC,cAAc;YAAE,UAAU;QAAY;IACxC;IAEA,uBAAuB;IACvB,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,QAAQ,OAAO,CAAC,QAAQ,CAAC,YACxC,QAAQ,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,YAClC;eAAI,QAAQ,OAAO;YAAE;SAAS;QAClC,cAAc;YAAE,SAAS;QAAW;IACtC;IAEA,uBAAuB;IACvB,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,QAAQ,OAAO,CAAC,QAAQ,CAAC,YACxC,QAAQ,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,YAClC;eAAI,QAAQ,OAAO;YAAE;SAAS;QAClC,cAAc;YAAE,SAAS;QAAW;IACtC;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAc,QAAQ,QAAQ,CAAC,QAAQ,CAAC,aAC1C,QAAQ,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM,aACnC;eAAI,QAAQ,QAAQ;YAAE;SAAU;QACpC,cAAc;YAAE,UAAU;QAAY;IACxC;IAEA,8BAA8B;IAC9B,MAAM,wBAAwB,CAAC;QAC7B,cAAc;YACZ,WAAW;gBACT,MAAM,OAAO,QAAQ;gBACrB,IAAI,OAAO,MAAM;YACnB;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,qBACJ,CAAC,QAAQ,MAAM,GAAG,IAAI,CAAC,IACvB,QAAQ,MAAM,CAAC,MAAM,GACrB,QAAQ,QAAQ,CAAC,MAAM,GACvB,QAAQ,OAAO,CAAC,MAAM,GACtB,QAAQ,OAAO,CAAC,MAAM,GACtB,QAAQ,QAAQ,CAAC,MAAM,GACvB,CAAC,QAAQ,SAAS,CAAC,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC;IAEzD,+BAA+B;IAC/B,MAAM,sBAAsB,kBAC1B,8OAAC,mIAAA,CAAA,UAAO;;8BACN,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,WAAU;;0CAClC,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAW;4BAEjC,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,WAAU;0CAET,QAAQ,MAAM,CAAC,MAAM;;;;;;;;;;;;;;;;;8BAK9B,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAW,OAAM;8BACzC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,cAAc;gDAAE,QAAQ,EAAE;4CAAC;wCAC1C,WAAU;kDACX;;;;;;;;;;;;0CAIH,8OAAC,qIAAA,CAAA,YAAS;;;;;0CACV,8OAAC;gCAAI,WAAU;0CACZ,0BAA0B,GAAG,CAAC,CAAA;oCAC7B,MAAM,gBAAgB,OAAO,IAAI;oCACjC,qBACE,8OAAC;wCAAuB,WAAU;;0DAChC,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;gDAC5B,SAAS,QAAQ,MAAM,CAAC,QAAQ,CAAC,OAAO,KAAK;gDAC7C,iBAAiB,IAAM,aAAa,OAAO,KAAK;;;;;;0DAElD,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;gDACjC,WAAU;;kEAEV,8OAAC;wDAAc,WAAU;;;;;;kEACzB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB,OAAO,KAAK;kEAE3C,OAAO,KAAK;;;;;;;;;;;;;uCAfT,OAAO,KAAK;;;;;gCAoB1B;;;;;;;;;;;;;;;;;;;;;;;IAOV,mCAAmC;IACnC,MAAM,yBAAyB;QAC7B,qBACE,8OAAC,mIAAA,CAAA,UAAO;;8BACN,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,WAAU;;0CAClC,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BAAW;4BAElC,CAAC,QAAQ,SAAS,CAAC,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,mBAC9C,8OAAC,iIAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAMP,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAa,OAAM;8BAC3C,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,cAAc;gDAAE,WAAW,CAAC;4CAAE;wCAC7C,WAAU;kDACX;;;;;;;;;;;;0CAIH,8OAAC,oIAAA,CAAA,WAAiB;gCAChB,MAAK;gCACL,UAAU;oCACR,MAAM,QAAQ,SAAS,CAAC,IAAI;oCAC5B,IAAI,QAAQ,SAAS,CAAC,EAAE;gCAC1B;gCACA,UAAU;gCACV,gBAAgB;gCAChB,WAAU;;;;;;0CAGZ,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,SAAS,CAAC,IAAI,IAAI,CAAC,QAAQ,SAAS,CAAC,EAAE,GAC5C,sCACA;;;;;;;;;;;;;;;;;;;;;;;IAMhB;IAEA,iCAAiC;IACjC,MAAM,wBAAwB,kBAC5B,8OAAC,mIAAA,CAAA,UAAO;;8BACN,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,WAAU;;0CAClC,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAW;4BAE5B,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,WAAU;0CAET,QAAQ,QAAQ,CAAC,MAAM;;;;;;;;;;;;;;;;;8BAKhC,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAW,OAAM;8BACzC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,cAAc;gDAAE,UAAU,EAAE;4CAAC;wCAC5C,WAAU;kDACX;;;;;;;;;;;;0CAIH,8OAAC,qIAAA,CAAA,YAAS;;;;;0CACV,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAA,yBACjB,8OAAC;wCAAmB,WAAU;;0DAC5B,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAI,CAAC,SAAS,EAAE,UAAU;gDAC1B,SAAS,QAAQ,QAAQ,CAAC,QAAQ,CAAC;gDACnC,iBAAiB,IAAM,eAAe;;;;;;0DAExC,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,CAAC,SAAS,EAAE,UAAU;gDAC/B,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAM;;;;;;;;;;;;;uCAXD;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqBtB,gCAAgC;IAChC,MAAM,uBAAuB,kBAC3B,8OAAC,mIAAA,CAAA,UAAO;;8BACN,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,WAAU;;0CAClC,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAW;4BAE3B,QAAQ,OAAO,CAAC,MAAM,GAAG,mBACxB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,WAAU;0CAET,QAAQ,OAAO,CAAC,MAAM;;;;;;;;;;;;;;;;;8BAK/B,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAW,OAAM;8BACzC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,cAAc;gDAAE,SAAS,EAAE;4CAAC;wCAC3C,WAAU;kDACX;;;;;;;;;;;;0CAIH,8OAAC,qIAAA,CAAA,YAAS;;;;;0CACV,8OAAC;gCAAI,WAAU;0CACZ,cACE,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,UAC3B,GAAG,CAAC,CAAA,uBACH,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;gDACzB,SAAS,QAAQ,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE;gDAC3C,iBAAiB,IAAM,aAAa,OAAO,EAAE;;;;;;0DAE/C,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE;gDAC9B,WAAU;;kEAEV,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAM,OAAO,IAAI;;;;;;0EAClB,8OAAC;gEAAK,WAAU;0EACb,OAAO,IAAI;;;;;;;;;;;;;;;;;;;uCAdV,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0BjC,iCAAiC;IACjC,MAAM,wBAAwB,kBAC5B,8OAAC,mIAAA,CAAA,UAAO;;8BACN,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,WAAU;;0CAClC,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAW;4BAEzB,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,WAAU;0CAET,QAAQ,QAAQ,CAAC,MAAM;;;;;;;;;;;;;;;;;8BAKhC,8OAAC,mIAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAW,OAAM;8BACzC,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,cAAc;gDAAE,UAAU,EAAE;4CAAC;wCAC5C,WAAU;kDACX;;;;;;;;;;;;0CAIH,8OAAC,qIAAA,CAAA,YAAS;;;;;0CACV,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAA,wBAChB,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;gDAC3B,SAAS,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE;gDAC7C,iBAAiB,IAAM,cAAc,QAAQ,EAAE;;;;;;0DAEjD,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;gDAChC,WAAU;;kEAEV,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAM,QAAQ,IAAI;;;;;;0EACnB,8OAAC;gEAAK,WAAU;0EACb,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;uCAdX,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;IA0BhC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;;0BAExC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,8OAAC,iIAAA,CAAA,QAAK;wBACJ,aAAY;wBACZ,OAAO,QAAQ,MAAM;wBACrB,UAAU,CAAA,IAAK,cAAc;gCAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4BAAC;wBACtD,WAAU;;;;;;;;;;;;0BAKd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;;;;0CACD,8OAAC;;;;;4BACA,cAAc,MAAM,GAAG,mBAAK,8OAAC;;;;;4BAC7B,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,2BACtC,8OAAC;;;;;4BAEF,aAAa,MAAM,GAAG,mBAAK,8OAAC;;;;;;;;;;;kCAI/B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAa,cAAc;;8CACtC,8OAAC,iIAAA,CAAA,eAAY;oCAAC,OAAO;8CACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAW;4CAE5B,qBAAqB,mBACpB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET;;;;;;;;;;;;;;;;;8CAKT,8OAAC,iIAAA,CAAA,eAAY;oCACX,MAAK;oCACL,WAAU;;sDAEV,8OAAC,iIAAA,CAAA,cAAW;;8DACV,8OAAC,iIAAA,CAAA,aAAU;8DAAC;;;;;;8DACZ,8OAAC,iIAAA,CAAA,mBAAgB;8DAAC;;;;;;;;;;;;sDAIpB,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAsB;;;;;;sEACvC,8OAAC;4DAAI,WAAU;sEACZ,0BAA0B,GAAG,CAAC,CAAA;gEAC7B,MAAM,gBAAgB,OAAO,IAAI;gEACjC,qBACE,8OAAC;oEAEC,WAAU;;sFAEV,8OAAC,oIAAA,CAAA,WAAQ;4EACP,IAAI,CAAC,cAAc,EAAE,OAAO,KAAK,EAAE;4EACnC,SAAS,QAAQ,MAAM,CAAC,QAAQ,CAAC,OAAO,KAAK;4EAC7C,iBAAiB,IAAM,aAAa,OAAO,KAAK;;;;;;sFAElD,8OAAC,iIAAA,CAAA,QAAK;4EACJ,SAAS,CAAC,cAAc,EAAE,OAAO,KAAK,EAAE;4EACxC,WAAU;;8FAEV,8OAAC;oFAAc,WAAU;;;;;;gFACxB,OAAO,KAAK;;;;;;;;mEAbV,OAAO,KAAK;;;;;4DAiBvB;;;;;;;;;;;;8DAKJ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAsB;;;;;;sEACvC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oIAAA,CAAA,WAAiB;gEAChB,MAAK;gEACL,UAAU;oEACR,MAAM,QAAQ,SAAS,CAAC,IAAI;oEAC5B,IAAI,QAAQ,SAAS,CAAC,EAAE;gEAC1B;gEACA,UAAU;gEACV,gBAAgB;gEAChB,WAAU;;;;;;;;;;;;;;;;;gDAMf,qBAAqB,mBACpB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS;oDACT,WAAU;;sEAEV,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;wDAAW;wDACJ;wDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASlD,qBAAqB,mBACpB,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;4BAAW;4BAChB;4BAAmB;;;;;;;;;;;;;YAMhC,qBAAqB,mBACpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;oBAE/C,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAA;wBAClB,MAAM,SAAS,0BAA0B,IAAI,CAC3C,CAAA,MAAO,IAAI,KAAK,KAAK;wBAEvB,IAAI,CAAC,QAAQ,OAAO;wBACpB,qBACE,8OAAC,iIAAA,CAAA,QAAK;4BAAc,SAAQ;4BAAY,WAAU;;gCAC/C,OAAO,KAAK;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,aAAa;8CAE5B,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BARL;;;;;oBAYhB;oBAEC,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA,yBACpB,8OAAC,iIAAA,CAAA,QAAK;4BAAgB,SAAQ;4BAAY,WAAU;;gCAAQ;gCAC/C;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe;8CAE9B,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BARL;;;;;oBAab,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAA;wBACnB,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;wBACpD,qBACE,8OAAC,iIAAA,CAAA,QAAK;4BAAgB,SAAQ;4BAAY,WAAU;;gCAAQ;gCACjD,QAAQ,QAAQ;8CACzB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,aAAa;8CAE5B,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BARL;;;;;oBAYhB;oBAEC,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAA;wBACpB,MAAM,UAAU,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wBAChD,qBACE,8OAAC,iIAAA,CAAA,QAAK;4BAAiB,SAAQ;4BAAY,WAAU;;gCAAQ;gCACjD,SAAS,QAAQ;8CAC3B,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE7B,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BARL;;;;;oBAYhB;oBAEC,CAAC,QAAQ,SAAS,CAAC,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,mBAC9C,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAAQ;4BACrC;4BACL,QAAQ,SAAS,CAAC,IAAI,GACnB,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,SAAS,CAAC,IAAI,EAAE,WAC/B;4BAAK;4BAAI;4BACX;4BACD,QAAQ,SAAS,CAAC,EAAE,GACjB,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,SAAS,CAAC,EAAE,EAAE,iBAC7B;0CACJ,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;wCAAE,WAAW,CAAC;oCAAE;0CAE7C,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7B;uCAGe", "debugId": null}}, {"offset": {"line": 4501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as SelectPrimitive from '@radix-ui/react-select';\r\nimport { Check, ChevronDown, ChevronUp } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ children, className, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    className={cn(\r\n      'flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"size-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    className={cn(\r\n      'flex cursor-default items-center justify-center py-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"size-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    className={cn(\r\n      'flex cursor-default items-center justify-center py-1',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"size-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ children, className, position = 'popper', ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      className={cn(\r\n        'relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\r\n        position === 'popper' &&\r\n          'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n        className\r\n      )}\r\n      position={position}\r\n      ref={ref}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          'p-1',\r\n          position === 'popper' &&\r\n            'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]'\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    className={cn('py-1.5 pl-8 pr-2 text-sm font-semibold', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.memo(\r\n  React.forwardRef<\r\n    React.ElementRef<typeof SelectPrimitive.Item>,\r\n    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n  >(({ children, className, ...props }, forwardedRef) => {\r\n    const composedRefs = React.useCallback(\r\n      (node: any) => {\r\n        if (typeof forwardedRef === 'function') {\r\n          forwardedRef(node);\r\n        } else if (forwardedRef) {\r\n          (forwardedRef as React.MutableRefObject<any>).current = node;\r\n        }\r\n      },\r\n      [forwardedRef]\r\n    );\r\n\r\n    return (\r\n      <SelectPrimitive.Item\r\n        className={cn(\r\n          'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\r\n          className\r\n        )}\r\n        ref={composedRefs}\r\n        {...props}\r\n      >\r\n        <span className=\"absolute left-2 flex size-3.5 items-center justify-center\">\r\n          <SelectPrimitive.ItemIndicator>\r\n            <Check className=\"size-4\" />\r\n          </SelectPrimitive.ItemIndicator>\r\n        </span>\r\n\r\n        <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n      </SelectPrimitive.Item>\r\n    );\r\n  })\r\n);\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AAAA;AAAA;AACA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACV,KAAK;YACJ,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,OAAU,AAAD,gBAC1B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGb,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACpC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EACnC,CAAC;QACC,IAAI,OAAO,iBAAiB,YAAY;YACtC,aAAa;QACf,OAAO,IAAI,cAAc;YACtB,aAA6C,OAAO,GAAG;QAC1D;IACF,GACA;QAAC;KAAa;IAGhB,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAEF,KAAK;QACJ,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEF,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QAC1C,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4705, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      className={cn('w-full caption-bottom text-sm', className)}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  </div>\r\n));\r\nTable.displayName = 'Table';\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead className={cn('[&_tr]:border-b', className)} ref={ref} {...props} />\r\n));\r\nTableHeader.displayName = 'TableHeader';\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    className={cn('[&_tr:last-child]:border-0', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableBody.displayName = 'TableBody';\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    className={cn(\r\n      'border-t bg-muted/50 font-medium [&>tr]:last:border-b-0',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableFooter.displayName = 'TableFooter';\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    className={cn(\r\n      'border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableRow.displayName = 'TableRow';\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    className={cn(\r\n      'h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableHead.displayName = 'TableHead';\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    className={cn('p-4 align-middle [&:has([role=checkbox])]:pr-0', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableCell.displayName = 'TableCell';\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    className={cn('mt-4 text-sm text-muted-foreground', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTableCaption.displayName = 'TableCaption';\r\n\r\nexport {\r\n  Table,\r\n  TableBody,\r\n  TableCaption,\r\n  TableCell,\r\n  TableFooter,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;AAAA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC/C,KAAK;YACJ,GAAG,KAAK;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAM,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAY,KAAK;QAAM,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC5C,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAChE,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4816, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/tables/DataTable.tsx"], "sourcesContent": ["/**\r\n * Generic DataTable Component\r\n *\r\n * A reusable table component built on TanStack Table that provides:\r\n * - Sorting, filtering, and pagination\r\n * - Row selection with bulk actions\r\n * - Column visibility controls\r\n * - Responsive design with shadcn/ui styling\r\n * - Type-safe implementation with generics\r\n *\r\n * Based on the excellent patterns from DelegationTable.tsx\r\n */\r\n\r\n'use client';\r\n\r\nimport type {\r\n  ColumnDef,\r\n  ColumnFiltersState,\r\n  SortingState,\r\n  VisibilityState,\r\n} from '@tanstack/react-table';\r\n\r\nimport {\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getFilteredRowModel,\r\n  getPaginationRowModel,\r\n  getSortedRowModel,\r\n  useReactTable,\r\n} from '@tanstack/react-table';\r\nimport {\r\n  ChevronDown,\r\n  Settings,\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  ChevronsLeft,\r\n  ChevronsRight,\r\n} from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Input } from '@/components/ui/input';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@/components/ui/table';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport interface DataTableProps<T> {\r\n  data: T[];\r\n  columns: ColumnDef<T>[];\r\n  className?: string;\r\n  onRowClick?: (row: T) => void;\r\n  searchPlaceholder?: string;\r\n  searchColumn?: string;\r\n  enableRowSelection?: boolean;\r\n  enableColumnVisibility?: boolean;\r\n  enableGlobalFilter?: boolean;\r\n  pageSize?: number;\r\n  emptyMessage?: string;\r\n  // Advanced features from DelegationTable\r\n  enableBulkActions?: boolean;\r\n  bulkActions?: Array<{\r\n    label: string;\r\n    icon?: React.ComponentType<{ className?: string }>;\r\n    onClick: (selectedRows: T[]) => void;\r\n    variant?: 'default' | 'destructive';\r\n  }>;\r\n  // Professional styling options\r\n  tableClassName?: string;\r\n  headerClassName?: string;\r\n  rowClassName?: string;\r\n}\r\n\r\nexport function DataTable<T>({\r\n  data,\r\n  columns,\r\n  className = '',\r\n  onRowClick,\r\n  searchPlaceholder = 'Search...',\r\n  searchColumn,\r\n  enableRowSelection = false,\r\n  enableColumnVisibility = true,\r\n  enableGlobalFilter = true,\r\n  pageSize = 10,\r\n  emptyMessage = 'No results found.',\r\n  enableBulkActions = false,\r\n  bulkActions = [],\r\n  tableClassName = '',\r\n  headerClassName = '',\r\n  rowClassName = '',\r\n}: DataTableProps<T>) {\r\n  const [sorting, setSorting] = React.useState<SortingState>([]);\r\n  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(\r\n    []\r\n  );\r\n  const [columnVisibility, setColumnVisibility] =\r\n    React.useState<VisibilityState>({});\r\n  const [rowSelection, setRowSelection] = React.useState({});\r\n  const [globalFilter, setGlobalFilter] = React.useState('');\r\n\r\n  const table = useReactTable({\r\n    data,\r\n    columns,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getPaginationRowModel: getPaginationRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    onSortingChange: setSorting,\r\n    onColumnFiltersChange: setColumnFilters,\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    onRowSelectionChange: setRowSelection,\r\n    onGlobalFilterChange: setGlobalFilter,\r\n    state: {\r\n      sorting,\r\n      columnFilters,\r\n      columnVisibility,\r\n      rowSelection,\r\n      globalFilter,\r\n    },\r\n    initialState: {\r\n      pagination: {\r\n        pageSize,\r\n      },\r\n    },\r\n  });\r\n\r\n  // Handle search input for specific column\r\n  const handleSearch = (value: string) => {\r\n    if (searchColumn) {\r\n      table.getColumn(searchColumn)?.setFilterValue(value);\r\n    } else {\r\n      setGlobalFilter(value);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={cn('space-y-4', className)}>\r\n      <Card className=\"shadow-md\">\r\n        {/* Table Controls Header */}\r\n        <div className=\"flex items-center justify-between p-4\">\r\n          {/* Search Input */}\r\n          {enableGlobalFilter && (\r\n            <Input\r\n              placeholder={searchPlaceholder}\r\n              value={\r\n                searchColumn\r\n                  ? ((table\r\n                      .getColumn(searchColumn)\r\n                      ?.getFilterValue() as string) ?? '')\r\n                  : globalFilter\r\n              }\r\n              onChange={event => handleSearch(event.target.value)}\r\n              className=\"max-w-sm\"\r\n            />\r\n          )}\r\n\r\n          <div className=\"flex items-center space-x-2\">\r\n            {/* Bulk Actions */}\r\n            {enableBulkActions &&\r\n              enableRowSelection &&\r\n              table.getFilteredSelectedRowModel().rows.length > 0 && (\r\n                <DropdownMenu>\r\n                  <DropdownMenuTrigger asChild>\r\n                    <Button variant=\"outline\" size=\"sm\">\r\n                      Actions ({table.getFilteredSelectedRowModel().rows.length}\r\n                      )\r\n                      <ChevronDown className=\"ml-2 h-4 w-4\" />\r\n                    </Button>\r\n                  </DropdownMenuTrigger>\r\n                  <DropdownMenuContent align=\"end\">\r\n                    {bulkActions.map((action, index) => (\r\n                      <DropdownMenuItem\r\n                        key={index}\r\n                        onClick={() =>\r\n                          action.onClick(\r\n                            table\r\n                              .getFilteredSelectedRowModel()\r\n                              .rows.map(row => row.original)\r\n                          )\r\n                        }\r\n                        className={\r\n                          action.variant === 'destructive'\r\n                            ? 'text-destructive'\r\n                            : ''\r\n                        }\r\n                      >\r\n                        {action.icon && (\r\n                          <action.icon className=\"mr-2 h-4 w-4\" />\r\n                        )}\r\n                        {action.label}\r\n                      </DropdownMenuItem>\r\n                    ))}\r\n                  </DropdownMenuContent>\r\n                </DropdownMenu>\r\n              )}\r\n\r\n            {/* Column Visibility */}\r\n            {enableColumnVisibility && (\r\n              <DropdownMenu>\r\n                <DropdownMenuTrigger asChild>\r\n                  <Button variant=\"outline\">\r\n                    <Settings className=\"mr-2 h-4 w-4\" />\r\n                    Columns\r\n                    <ChevronDown className=\"ml-2 h-4 w-4\" />\r\n                  </Button>\r\n                </DropdownMenuTrigger>\r\n                <DropdownMenuContent align=\"end\" className=\"w-[150px]\">\r\n                  {table\r\n                    .getAllColumns()\r\n                    .filter(column => column.getCanHide())\r\n                    .map(column => {\r\n                      return (\r\n                        <DropdownMenuCheckboxItem\r\n                          key={column.id}\r\n                          className=\"capitalize\"\r\n                          checked={column.getIsVisible()}\r\n                          onCheckedChange={value =>\r\n                            column.toggleVisibility(!!value)\r\n                          }\r\n                        >\r\n                          {column.id}\r\n                        </DropdownMenuCheckboxItem>\r\n                      );\r\n                    })}\r\n                </DropdownMenuContent>\r\n              </DropdownMenu>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Table Content */}\r\n        <CardContent className=\"p-0\">\r\n          <div className={cn('border-t', tableClassName)}>\r\n            <Table>\r\n              <TableHeader>\r\n                {table.getHeaderGroups().map(headerGroup => (\r\n                  <TableRow\r\n                    key={headerGroup.id}\r\n                    className={cn(\r\n                      'border-b border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800',\r\n                      headerClassName\r\n                    )}\r\n                  >\r\n                    {headerGroup.headers.map(header => {\r\n                      return (\r\n                        <TableHead\r\n                          key={header.id}\r\n                          className=\"py-4 font-semibold text-gray-900 dark:text-white\"\r\n                        >\r\n                          {header.isPlaceholder ? null : (\r\n                            <div\r\n                              className={cn(\r\n                                'flex items-center space-x-1',\r\n                                header.column.getCanSort() &&\r\n                                  'cursor-pointer select-none hover:text-gray-600 dark:hover:text-gray-300'\r\n                              )}\r\n                              onClick={header.column.getToggleSortingHandler()}\r\n                            >\r\n                              <span>\r\n                                {flexRender(\r\n                                  header.column.columnDef.header,\r\n                                  header.getContext()\r\n                                )}\r\n                              </span>\r\n                            </div>\r\n                          )}\r\n                        </TableHead>\r\n                      );\r\n                    })}\r\n                  </TableRow>\r\n                ))}\r\n              </TableHeader>\r\n              <TableBody>\r\n                {table.getRowModel().rows?.length ? (\r\n                  table.getRowModel().rows.map(row => (\r\n                    <TableRow\r\n                      key={row.id}\r\n                      data-state={row.getIsSelected() && 'selected'}\r\n                      className={cn(\r\n                        'border-b border-gray-100 hover:bg-gray-50 dark:border-gray-800 dark:hover:bg-gray-800/50',\r\n                        onRowClick && 'cursor-pointer',\r\n                        row.getIsSelected() && 'bg-blue-50 dark:bg-blue-900/20',\r\n                        rowClassName\r\n                      )}\r\n                      onClick={() => onRowClick?.(row.original)}\r\n                    >\r\n                      {row.getVisibleCells().map(cell => (\r\n                        <TableCell key={cell.id}>\r\n                          {flexRender(\r\n                            cell.column.columnDef.cell,\r\n                            cell.getContext()\r\n                          )}\r\n                        </TableCell>\r\n                      ))}\r\n                    </TableRow>\r\n                  ))\r\n                ) : (\r\n                  <TableRow>\r\n                    <TableCell\r\n                      colSpan={columns.length}\r\n                      className=\"h-24 text-center\"\r\n                    >\r\n                      {emptyMessage}\r\n                    </TableCell>\r\n                  </TableRow>\r\n                )}\r\n              </TableBody>\r\n            </Table>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Pagination Footer */}\r\n      <Card className=\"flex items-center justify-between border-t p-4\">\r\n        <div className=\"flex-1 text-sm text-muted-foreground\">\r\n          {enableRowSelection &&\r\n          table.getFilteredSelectedRowModel().rows.length > 0 ? (\r\n            <>\r\n              {table.getFilteredSelectedRowModel().rows.length} of{' '}\r\n              {table.getFilteredRowModel().rows.length} row(s) selected.\r\n            </>\r\n          ) : (\r\n            <>\r\n              Showing{' '}\r\n              {table.getState().pagination.pageIndex *\r\n                table.getState().pagination.pageSize +\r\n                1}{' '}\r\n              to{' '}\r\n              {Math.min(\r\n                (table.getState().pagination.pageIndex + 1) *\r\n                  table.getState().pagination.pageSize,\r\n                table.getFilteredRowModel().rows.length\r\n              )}{' '}\r\n              of {table.getFilteredRowModel().rows.length} entries\r\n              {table.getFilteredRowModel().rows.length !== data.length &&\r\n                ` (filtered from ${data.length} total)`}\r\n            </>\r\n          )}\r\n        </div>\r\n        <div className=\"flex items-center space-x-6 lg:space-x-8\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <p className=\"text-sm font-medium\">Rows per page</p>\r\n            <Select\r\n              value={`${table.getState().pagination.pageSize}`}\r\n              onValueChange={value => {\r\n                table.setPageSize(Number(value));\r\n              }}\r\n            >\r\n              <SelectTrigger className=\"h-8 w-[70px]\">\r\n                <SelectValue\r\n                  placeholder={table.getState().pagination.pageSize}\r\n                />\r\n              </SelectTrigger>\r\n              <SelectContent side=\"top\">\r\n                {[10, 20, 30, 40, 50].map(pageSize => (\r\n                  <SelectItem key={pageSize} value={`${pageSize}`}>\r\n                    {pageSize}\r\n                  </SelectItem>\r\n                ))}\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n          <div className=\"flex w-[100px] items-center justify-center text-sm font-medium\">\r\n            Page {table.getState().pagination.pageIndex + 1} of{' '}\r\n            {table.getPageCount()}\r\n          </div>\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n              onClick={() => table.setPageIndex(0)}\r\n              disabled={!table.getCanPreviousPage()}\r\n            >\r\n              <span className=\"sr-only\">Go to first page</span>\r\n              <ChevronsLeft className=\"h-4 w-4\" />\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"h-8 w-8 p-0\"\r\n              onClick={() => table.previousPage()}\r\n              disabled={!table.getCanPreviousPage()}\r\n            >\r\n              <span className=\"sr-only\">Go to previous page</span>\r\n              <ChevronLeft className=\"h-4 w-4\" />\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"h-8 w-8 p-0\"\r\n              onClick={() => table.nextPage()}\r\n              disabled={!table.getCanNextPage()}\r\n            >\r\n              <span className=\"sr-only\">Go to next page</span>\r\n              <ChevronRight className=\"h-4 w-4\" />\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n              onClick={() => table.setPageIndex(table.getPageCount() - 1)}\r\n              disabled={!table.getCanNextPage()}\r\n            >\r\n              <span className=\"sr-only\">Go to last page</span>\r\n              <ChevronsRight className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;AAWD;AAAA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAEA;AACA;AACA;AAOA;AACA;AAOA;AAQA;AAAA;AApDA;;;;;;;;;;;;AAgFO,SAAS,UAAa,EAC3B,IAAI,EACJ,OAAO,EACP,YAAY,EAAE,EACd,UAAU,EACV,oBAAoB,WAAW,EAC/B,YAAY,EACZ,qBAAqB,KAAK,EAC1B,yBAAyB,IAAI,EAC7B,qBAAqB,IAAI,EACzB,WAAW,EAAE,EACb,eAAe,mBAAmB,EAClC,oBAAoB,KAAK,EACzB,cAAc,EAAE,EAChB,iBAAiB,EAAE,EACnB,kBAAkB,EAAE,EACpB,eAAe,EAAE,EACC;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAgB,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EACrD,EAAE;IAEJ,MAAM,CAAC,kBAAkB,oBAAoB,GAC3C,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAmB,CAAC;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,CAAC;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEvD,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,iBAAiB,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;QAC/B,uBAAuB,CAAA,GAAA,qKAAA,CAAA,wBAAqB,AAAD;QAC3C,mBAAmB,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD;QACnC,qBAAqB,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD;QACvC,iBAAiB;QACjB,uBAAuB;QACvB,0BAA0B;QAC1B,sBAAsB;QACtB,sBAAsB;QACtB,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;QACA,cAAc;YACZ,YAAY;gBACV;YACF;QACF;IACF;IAEA,0CAA0C;IAC1C,MAAM,eAAe,CAAC;QACpB,IAAI,cAAc;YAChB,MAAM,SAAS,CAAC,eAAe,eAAe;QAChD,OAAO;YACL,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;4BAEZ,oCACC,8OAAC,iIAAA,CAAA,QAAK;gCACJ,aAAa;gCACb,OACE,eACK,AAAC,MACC,SAAS,CAAC,eACT,oBAA+B,KACnC;gCAEN,UAAU,CAAA,QAAS,aAAa,MAAM,MAAM,CAAC,KAAK;gCAClD,WAAU;;;;;;0CAId,8OAAC;gCAAI,WAAU;;oCAEZ,qBACC,sBACA,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM,GAAG,mBAChD,8OAAC,4IAAA,CAAA,eAAY;;0DACX,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;;wDAAK;wDACxB,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;wDAAC;sEAE1D,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAG3B,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAM;0DACxB,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,8OAAC,4IAAA,CAAA,mBAAgB;wDAEf,SAAS,IACP,OAAO,OAAO,CACZ,MACG,2BAA2B,GAC3B,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ;wDAGnC,WACE,OAAO,OAAO,KAAK,gBACf,qBACA;;4DAGL,OAAO,IAAI,kBACV,8OAAC,OAAO,IAAI;gEAAC,WAAU;;;;;;4DAExB,OAAO,KAAK;;uDAjBR;;;;;;;;;;;;;;;;oCAyBhB,wCACC,8OAAC,4IAAA,CAAA,eAAY;;0DACX,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;;sEACd,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;sEAErC,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAG3B,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAM;gDAAM,WAAU;0DACxC,MACE,aAAa,GACb,MAAM,CAAC,CAAA,SAAU,OAAO,UAAU,IAClC,GAAG,CAAC,CAAA;oDACH,qBACE,8OAAC,4IAAA,CAAA,2BAAwB;wDAEvB,WAAU;wDACV,SAAS,OAAO,YAAY;wDAC5B,iBAAiB,CAAA,QACf,OAAO,gBAAgB,CAAC,CAAC,CAAC;kEAG3B,OAAO,EAAE;uDAPL,OAAO,EAAE;;;;;gDAUpB;;;;;;;;;;;;;;;;;;;;;;;;kCAQZ,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,YAAY;sCAC7B,cAAA,8OAAC,iIAAA,CAAA,QAAK;;kDACJ,8OAAC,iIAAA,CAAA,cAAW;kDACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAA,4BAC3B,8OAAC,iIAAA,CAAA,WAAQ;gDAEP,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,6EACA;0DAGD,YAAY,OAAO,CAAC,GAAG,CAAC,CAAA;oDACvB,qBACE,8OAAC,iIAAA,CAAA,YAAS;wDAER,WAAU;kEAET,OAAO,aAAa,GAAG,qBACtB,8OAAC;4DACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,+BACA,OAAO,MAAM,CAAC,UAAU,MACtB;4DAEJ,SAAS,OAAO,MAAM,CAAC,uBAAuB;sEAE9C,cAAA,8OAAC;0EACE,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACR,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;;;;;;;;;;;uDAfpB,OAAO,EAAE;;;;;gDAsBpB;+CA/BK,YAAY,EAAE;;;;;;;;;;kDAmCzB,8OAAC,iIAAA,CAAA,YAAS;kDACP,MAAM,WAAW,GAAG,IAAI,EAAE,SACzB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA,oBAC3B,8OAAC,iIAAA,CAAA,WAAQ;gDAEP,cAAY,IAAI,aAAa,MAAM;gDACnC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,4FACA,cAAc,kBACd,IAAI,aAAa,MAAM,kCACvB;gDAEF,SAAS,IAAM,aAAa,IAAI,QAAQ;0DAEvC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAA,qBACzB,8OAAC,iIAAA,CAAA,YAAS;kEACP,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;uDAHH,KAAK,EAAE;;;;;+CAXpB,IAAI,EAAE;;;;sEAqBf,8OAAC,iIAAA,CAAA,WAAQ;sDACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;gDACR,SAAS,QAAQ,MAAM;gDACvB,WAAU;0DAET;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC;wBAAI,WAAU;kCACZ,sBACD,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM,GAAG,kBAChD;;gCACG,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;gCAAC;gCAAI;gCACpD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCAAC;;yDAG3C;;gCAAE;gCACQ;gCACP,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GACpC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,GACpC;gCAAG;gCAAI;gCACN;gCACF,KAAK,GAAG,CACP,CAAC,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,CAAC,IACxC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EACtC,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCACtC;gCAAI;gCACH,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gCAAC;gCAC3C,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,MAAM,IACtD,CAAC,gBAAgB,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC;;;;;;;;kCAI/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE;wCAChD,eAAe,CAAA;4CACb,MAAM,WAAW,CAAC,OAAO;wCAC3B;;0DAEA,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDACV,aAAa,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;;;;;;;;;;;0DAGrD,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,MAAK;0DACjB;oDAAC;oDAAI;oDAAI;oDAAI;oDAAI;iDAAG,CAAC,GAAG,CAAC,CAAA,yBACxB,8OAAC,kIAAA,CAAA,aAAU;wDAAgB,OAAO,GAAG,UAAU;kEAC5C;uDADc;;;;;;;;;;;;;;;;;;;;;;0CAOzB,8OAAC;gCAAI,WAAU;;oCAAiE;oCACxE,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG;oCAAE;oCAAI;oCACnD,MAAM,YAAY;;;;;;;0CAErB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,MAAM,YAAY,CAAC;wCAClC,UAAU,CAAC,MAAM,kBAAkB;;0DAEnC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,MAAM,YAAY;wCACjC,UAAU,CAAC,MAAM,kBAAkB;;0DAEnC,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;kDAEzB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,MAAM,QAAQ;wCAC7B,UAAU,CAAC,MAAM,cAAc;;0DAE/B,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;kDAE1B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,MAAM,YAAY,CAAC,MAAM,YAAY,KAAK;wCACzD,UAAU,CAAC,MAAM,cAAc;;0DAE/B,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}, {"offset": {"line": 5410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/tables/columnHelpers.tsx"], "sourcesContent": ["/**\r\n * Column Helper Utilities for DataTable\r\n *\r\n * Provides reusable column definitions and helper functions for common\r\n * table column patterns, reducing duplication across different tables.\r\n */\r\n\r\nimport type { ColumnDef } from '@tanstack/react-table';\r\nimport { format } from 'date-fns';\r\nimport {\r\n  ArrowUpDown,\r\n  ArrowUp,\r\n  ArrowDown,\r\n  Edit,\r\n  Eye,\r\n  MoreHorizontal,\r\n  Trash,\r\n} from 'lucide-react';\r\nimport Link from 'next/link';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { cn } from '@/lib/utils';\r\n\r\n/**\r\n * Creates a sortable header component with proper sorting state indicators\r\n */\r\nexport const createSortableHeader = (title: string) => {\r\n  return ({ column }: { column: any }) => {\r\n    const sortDirection = column.getIsSorted();\r\n\r\n    return (\r\n      <Button\r\n        variant=\"ghost\"\r\n        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n        className=\"h-auto p-0 font-semibold hover:bg-transparent\"\r\n      >\r\n        {title}\r\n        {sortDirection === 'asc' ? (\r\n          <ArrowUp className=\"ml-2 h-4 w-4\" />\r\n        ) : sortDirection === 'desc' ? (\r\n          <ArrowDown className=\"ml-2 h-4 w-4\" />\r\n        ) : (\r\n          <ArrowUpDown className=\"ml-2 h-4 w-4 opacity-50\" />\r\n        )}\r\n      </Button>\r\n    );\r\n  };\r\n};\r\n\r\n/**\r\n * Creates a date column with consistent formatting\r\n */\r\nexport const createDateColumn = <T,>(\r\n  accessorKey: keyof T,\r\n  header: string,\r\n  dateFormat: string = 'MMM dd, yyyy'\r\n): ColumnDef<T> => ({\r\n  accessorKey: accessorKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ getValue }) => {\r\n    const date = getValue() as string | Date;\r\n    if (!date) return '-';\r\n\r\n    try {\r\n      const dateObj = typeof date === 'string' ? new Date(date) : date;\r\n      return format(dateObj, dateFormat);\r\n    } catch {\r\n      return '-';\r\n    }\r\n  },\r\n});\r\n\r\n/**\r\n * Creates a status badge column\r\n */\r\nexport const createStatusColumn = <T,>(\r\n  accessorKey: keyof T,\r\n  header: string = 'Status',\r\n  statusConfig?: Record<string, { variant: string; label?: string }>\r\n): ColumnDef<T> => ({\r\n  accessorKey: accessorKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ getValue }) => {\r\n    const status = getValue() as string;\r\n    if (!status) return '-';\r\n\r\n    const config = statusConfig?.[status] || { variant: 'secondary' };\r\n    const label = config.label || status;\r\n\r\n    return <Badge variant={config.variant as any}>{label}</Badge>;\r\n  },\r\n});\r\n\r\n/**\r\n * Creates an actions column with common CRUD operations\r\n */\r\nexport const createActionsColumn = <\r\n  T extends { id: number | string },\r\n>(options: {\r\n  onView?: (item: T) => void;\r\n  onEdit?: (item: T) => void;\r\n  onDelete?: (item: T) => void;\r\n  viewHref?: (item: T) => string;\r\n  editHref?: (item: T) => string;\r\n  customActions?: Array<{\r\n    label: string;\r\n    icon?: React.ComponentType<{ className?: string }>;\r\n    onClick: (item: T) => void;\r\n    variant?: 'default' | 'destructive';\r\n  }>;\r\n}): ColumnDef<T> => ({\r\n  id: 'actions',\r\n  header: 'Actions',\r\n  cell: ({ row }) => {\r\n    const item = row.original;\r\n\r\n    return (\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\r\n            <span className=\"sr-only\">Open menu</span>\r\n            <MoreHorizontal className=\"h-4 w-4\" />\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\">\r\n          {/* View Action */}\r\n          {(options.onView || options.viewHref) && (\r\n            <>\r\n              {options.viewHref ? (\r\n                <DropdownMenuItem asChild>\r\n                  <Link href={options.viewHref(item)}>\r\n                    <Eye className=\"mr-2 h-4 w-4\" />\r\n                    View\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n              ) : (\r\n                <DropdownMenuItem onClick={() => options.onView?.(item)}>\r\n                  <Eye className=\"mr-2 h-4 w-4\" />\r\n                  View\r\n                </DropdownMenuItem>\r\n              )}\r\n            </>\r\n          )}\r\n\r\n          {/* Edit Action */}\r\n          {(options.onEdit || options.editHref) && (\r\n            <>\r\n              {options.editHref ? (\r\n                <DropdownMenuItem asChild>\r\n                  <Link href={options.editHref(item)}>\r\n                    <Edit className=\"mr-2 h-4 w-4\" />\r\n                    Edit\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n              ) : (\r\n                <DropdownMenuItem onClick={() => options.onEdit?.(item)}>\r\n                  <Edit className=\"mr-2 h-4 w-4\" />\r\n                  Edit\r\n                </DropdownMenuItem>\r\n              )}\r\n            </>\r\n          )}\r\n\r\n          {/* Custom Actions */}\r\n          {options.customActions?.map((action, index) => (\r\n            <DropdownMenuItem\r\n              key={index}\r\n              onClick={() => action.onClick(item)}\r\n              className={\r\n                action.variant === 'destructive' ? 'text-destructive' : ''\r\n              }\r\n            >\r\n              {action.icon && <action.icon className=\"mr-2 h-4 w-4\" />}\r\n              {action.label}\r\n            </DropdownMenuItem>\r\n          ))}\r\n\r\n          {/* Delete Action */}\r\n          {options.onDelete && (\r\n            <>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                onClick={() => options.onDelete?.(item)}\r\n                className=\"text-destructive\"\r\n              >\r\n                <Trash className=\"mr-2 h-4 w-4\" />\r\n                Delete\r\n              </DropdownMenuItem>\r\n            </>\r\n          )}\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    );\r\n  },\r\n});\r\n\r\n/**\r\n * Creates a text column with optional truncation\r\n */\r\nexport const createTextColumn = <T,>(\r\n  accessorKey: keyof T,\r\n  header: string,\r\n  options?: {\r\n    maxLength?: number;\r\n    className?: string;\r\n  }\r\n): ColumnDef<T> => ({\r\n  accessorKey: accessorKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ getValue }) => {\r\n    const value = getValue() as string;\r\n    if (!value) return '-';\r\n\r\n    const truncated =\r\n      options?.maxLength && value.length > options.maxLength\r\n        ? `${value.substring(0, options.maxLength)}...`\r\n        : value;\r\n\r\n    return (\r\n      <span className={options?.className} title={value}>\r\n        {truncated}\r\n      </span>\r\n    );\r\n  },\r\n});\r\n\r\n/**\r\n * Creates a numeric column with optional formatting\r\n */\r\nexport const createNumericColumn = <T,>(\r\n  accessorKey: keyof T,\r\n  header: string,\r\n  options?: {\r\n    format?: 'currency' | 'percentage' | 'decimal';\r\n    decimals?: number;\r\n    prefix?: string;\r\n    suffix?: string;\r\n  }\r\n): ColumnDef<T> => ({\r\n  accessorKey: accessorKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ getValue }) => {\r\n    const rawValue = getValue();\r\n    const value =\r\n      typeof rawValue === 'string'\r\n        ? parseFloat(rawValue)\r\n        : (rawValue as number);\r\n\r\n    if (value === null || value === undefined || isNaN(value)) return '-';\r\n\r\n    let formatted = value.toString();\r\n\r\n    if (options?.format === 'currency') {\r\n      formatted = new Intl.NumberFormat('en-US', {\r\n        style: 'currency',\r\n        currency: 'USD',\r\n        minimumFractionDigits: options.decimals ?? 2,\r\n      }).format(value);\r\n    } else if (options?.format === 'percentage') {\r\n      formatted = `${(value * 100).toFixed(options.decimals ?? 1)}%`;\r\n    } else if (options?.decimals !== undefined) {\r\n      formatted = value.toFixed(options.decimals);\r\n    }\r\n\r\n    if (options?.prefix) formatted = options.prefix + formatted;\r\n    if (options?.suffix) formatted = formatted + options.suffix;\r\n\r\n    return <span className=\"font-mono\">{formatted}</span>;\r\n  },\r\n});\r\n\r\n/**\r\n * Creates a boolean column with checkmark/cross display\r\n */\r\nexport const createBooleanColumn = <T,>(\r\n  accessorKey: keyof T,\r\n  header: string,\r\n  labels?: { true: string; false: string }\r\n): ColumnDef<T> => ({\r\n  accessorKey: accessorKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ getValue }) => {\r\n    const value = getValue() as boolean;\r\n\r\n    if (labels) {\r\n      return (\r\n        <Badge variant={value ? 'default' : 'secondary'}>\r\n          {value ? labels.true : labels.false}\r\n        </Badge>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <span className={value ? 'text-green-600' : 'text-gray-400'}>\r\n        {value ? '✓' : '✗'}\r\n      </span>\r\n    );\r\n  },\r\n});\r\n\r\n/**\r\n * Creates a row selection column (checkbox)\r\n */\r\nexport const createSelectionColumn = <T,>(): ColumnDef<T> => ({\r\n  id: 'select',\r\n  header: ({ table }) => (\r\n    <Checkbox\r\n      checked={\r\n        table.getIsAllPageRowsSelected() ||\r\n        (table.getIsSomePageRowsSelected() && 'indeterminate')\r\n      }\r\n      onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}\r\n      aria-label=\"Select all\"\r\n    />\r\n  ),\r\n  cell: ({ row }) => (\r\n    <Checkbox\r\n      checked={row.getIsSelected()}\r\n      onCheckedChange={value => row.toggleSelected(!!value)}\r\n      aria-label=\"Select row\"\r\n    />\r\n  ),\r\n  enableSorting: false,\r\n  enableHiding: false,\r\n});\r\n\r\n/**\r\n * Creates a complex column with title and subtitle (like DelegationTable eventName)\r\n */\r\nexport const createTitleSubtitleColumn = <T,>(\r\n  titleKey: keyof T,\r\n  subtitleKey: keyof T,\r\n  header: string\r\n): ColumnDef<T> => ({\r\n  accessorKey: titleKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ row }) => {\r\n    const title = row.getValue(titleKey as string) as string;\r\n    const subtitle = (row.getValue(subtitleKey as string) || '') as string;\r\n\r\n    return (\r\n      <div className=\"space-y-1\">\r\n        <div className=\"font-semibold text-foreground\">{title || '-'}</div>\r\n        {subtitle && (\r\n          <div className=\"line-clamp-1 text-xs text-muted-foreground\">\r\n            {subtitle}\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  },\r\n});\r\n\r\n/**\r\n * Creates a column with icon and text (like DelegationTable delegates count)\r\n */\r\nexport const createIconTextColumn = <T,>(\r\n  accessorKey: keyof T,\r\n  header: string,\r\n  icon: React.ComponentType<{ className?: string }>,\r\n  options?: {\r\n    formatter?: (value: any) => string;\r\n    className?: string;\r\n  }\r\n): ColumnDef<T> => ({\r\n  accessorKey: accessorKey as string,\r\n  header: createSortableHeader(header),\r\n  cell: ({ row }) => {\r\n    const value = row.getValue(accessorKey as string);\r\n    const Icon = icon;\r\n    const displayValue = options?.formatter ? options.formatter(value) : value;\r\n\r\n    return (\r\n      <div\r\n        className={cn(\r\n          'flex items-center justify-center gap-1 text-sm',\r\n          options?.className\r\n        )}\r\n      >\r\n        <Icon className=\"size-3 text-muted-foreground\" />\r\n        {String(displayValue)}\r\n      </div>\r\n    );\r\n  },\r\n});\r\n\r\n/**\r\n * Enhanced actions column with professional styling (like DelegationTable)\r\n */\r\nexport const createEnhancedActionsColumn = <\r\n  T extends { id: number | string },\r\n>(options: {\r\n  onView?: (item: T) => void;\r\n  onEdit?: (item: T) => void;\r\n  onDelete?: (item: T) => void;\r\n  viewHref?: (item: T) => string;\r\n  editHref?: (item: T) => string;\r\n  customActions?: Array<{\r\n    label: string;\r\n    icon?: React.ComponentType<{ className?: string }>;\r\n    onClick: (item: T) => void;\r\n    variant?: 'default' | 'destructive';\r\n  }>;\r\n  showCopyId?: boolean;\r\n}): ColumnDef<T> => ({\r\n  id: 'actions',\r\n  header: 'Actions',\r\n  cell: ({ row }) => {\r\n    const item = row.original;\r\n\r\n    return (\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant=\"ghost\" className=\"size-8 p-0\">\r\n            <span className=\"sr-only\">Open menu</span>\r\n            <MoreHorizontal className=\"size-4\" />\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\">\r\n          <DropdownMenuLabel>Actions</DropdownMenuLabel>\r\n\r\n          {/* View Action */}\r\n          {(options.onView || options.viewHref) && (\r\n            <>\r\n              {options.viewHref ? (\r\n                <DropdownMenuItem asChild>\r\n                  <Link href={options.viewHref(item)}>\r\n                    <Eye className=\"mr-2 size-4\" />\r\n                    View Details\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n              ) : (\r\n                <DropdownMenuItem onClick={() => options.onView?.(item)}>\r\n                  <Eye className=\"mr-2 size-4\" />\r\n                  View Details\r\n                </DropdownMenuItem>\r\n              )}\r\n            </>\r\n          )}\r\n\r\n          {/* Edit Action */}\r\n          {(options.onEdit || options.editHref) && (\r\n            <>\r\n              {options.editHref ? (\r\n                <DropdownMenuItem asChild>\r\n                  <Link href={options.editHref(item)}>\r\n                    <Edit className=\"mr-2 size-4\" />\r\n                    Edit\r\n                  </Link>\r\n                </DropdownMenuItem>\r\n              ) : (\r\n                <DropdownMenuItem onClick={() => options.onEdit?.(item)}>\r\n                  <Edit className=\"mr-2 size-4\" />\r\n                  Edit\r\n                </DropdownMenuItem>\r\n              )}\r\n            </>\r\n          )}\r\n\r\n          {/* Custom Actions */}\r\n          {options.customActions?.map((action, index) => (\r\n            <DropdownMenuItem\r\n              key={index}\r\n              onClick={() => action.onClick(item)}\r\n              className={\r\n                action.variant === 'destructive' ? 'text-destructive' : ''\r\n              }\r\n            >\r\n              {action.icon && <action.icon className=\"mr-2 size-4\" />}\r\n              {action.label}\r\n            </DropdownMenuItem>\r\n          ))}\r\n\r\n          {/* Copy ID */}\r\n          {options.showCopyId && (\r\n            <DropdownMenuItem\r\n              onClick={() => navigator.clipboard.writeText(String(item.id))}\r\n            >\r\n              Copy ID\r\n            </DropdownMenuItem>\r\n          )}\r\n\r\n          {/* Delete Action */}\r\n          {options.onDelete && (\r\n            <>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                onClick={() => options.onDelete?.(item)}\r\n                className=\"text-destructive\"\r\n              >\r\n                <Trash className=\"mr-2 size-4\" />\r\n                Delete\r\n              </DropdownMenuItem>\r\n            </>\r\n          )}\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    );\r\n  },\r\n});\r\n\r\n/**\r\n * Status configuration presets for common entities\r\n */\r\nexport const statusConfigs = {\r\n  delegation: {\r\n    Planned: { variant: 'secondary', label: 'Planned' },\r\n    'In Progress': { variant: 'default', label: 'In Progress' },\r\n    Completed: { variant: 'success', label: 'Completed' },\r\n    Cancelled: { variant: 'destructive', label: 'Cancelled' },\r\n  },\r\n  employee: {\r\n    Active: { variant: 'success', label: 'Active' },\r\n    Inactive: { variant: 'secondary', label: 'Inactive' },\r\n    'On Leave': { variant: 'warning', label: 'On Leave' },\r\n  },\r\n  task: {\r\n    Pending: { variant: 'secondary', label: 'Pending' },\r\n    In_Progress: { variant: 'default', label: 'In Progress' }, // Changed from 'In Progress'\r\n    Completed: { variant: 'success', label: 'Completed' },\r\n    Overdue: { variant: 'destructive', label: 'Overdue' },\r\n    Cancelled: { variant: 'destructive', label: 'Cancelled' },\r\n    Assigned: { variant: 'default', label: 'Assigned' },\r\n  },\r\n} as const;\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;;;;;AAGD;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAEA;AACA;AACA;AACA;AAQA;AAAA;;;;;;;;;;AAKO,MAAM,uBAAuB,CAAC;IACnC,OAAO,CAAC,EAAE,MAAM,EAAmB;QACjC,MAAM,gBAAgB,OAAO,WAAW;QAExC,qBACE,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAQ;YACR,SAAS,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;YAC7D,WAAU;;gBAET;gBACA,kBAAkB,sBACjB,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;2BACjB,kBAAkB,uBACpB,8OAAC,gNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;yCAErB,8OAAC,wNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;IAI/B;AACF;AAKO,MAAM,mBAAmB,CAC9B,aACA,QACA,aAAqB,cAAc,GAClB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,QAAQ,EAAE;YACjB,MAAM,OAAO;YACb,IAAI,CAAC,MAAM,OAAO;YAElB,IAAI;gBACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;gBAC5D,OAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;YACzB,EAAE,OAAM;gBACN,OAAO;YACT;QACF;IACF,CAAC;AAKM,MAAM,qBAAqB,CAChC,aACA,SAAiB,QAAQ,EACzB,eACiB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,QAAQ,EAAE;YACjB,MAAM,SAAS;YACf,IAAI,CAAC,QAAQ,OAAO;YAEpB,MAAM,SAAS,cAAc,CAAC,OAAO,IAAI;gBAAE,SAAS;YAAY;YAChE,MAAM,QAAQ,OAAO,KAAK,IAAI;YAE9B,qBAAO,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,OAAO,OAAO;0BAAU;;;;;;QACjD;IACF,CAAC;AAKM,MAAM,sBAAsB,CAEjC,UAYkB,CAAC;QACnB,IAAI;QACJ,QAAQ;QACR,MAAM,CAAC,EAAE,GAAG,EAAE;YACZ,MAAM,OAAO,IAAI,QAAQ;YAEzB,qBACE,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,WAAU;;8CAChC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,gNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAM;;4BAExB,CAAC,QAAQ,MAAM,IAAI,QAAQ,QAAQ,mBAClC;0CACG,QAAQ,QAAQ,iBACf,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,QAAQ,QAAQ,CAAC;;0DAC3B,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;yDAKpC,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,QAAQ,MAAM,GAAG;;sDAChD,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;4BAQvC,CAAC,QAAQ,MAAM,IAAI,QAAQ,QAAQ,mBAClC;0CACG,QAAQ,QAAQ,iBACf,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,QAAQ,QAAQ,CAAC;;0DAC3B,8OAAC,2MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;yDAKrC,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,QAAQ,MAAM,GAAG;;sDAChD,8OAAC,2MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;4BAQxC,QAAQ,aAAa,EAAE,IAAI,CAAC,QAAQ,sBACnC,8OAAC,4IAAA,CAAA,mBAAgB;oCAEf,SAAS,IAAM,OAAO,OAAO,CAAC;oCAC9B,WACE,OAAO,OAAO,KAAK,gBAAgB,qBAAqB;;wCAGzD,OAAO,IAAI,kBAAI,8OAAC,OAAO,IAAI;4CAAC,WAAU;;;;;;wCACtC,OAAO,KAAK;;mCAPR;;;;;4BAYR,QAAQ,QAAQ,kBACf;;kDACE,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS,IAAM,QAAQ,QAAQ,GAAG;wCAClC,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;QAQhD;IACF,CAAC;AAKM,MAAM,mBAAmB,CAC9B,aACA,QACA,UAIiB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,QAAQ,EAAE;YACjB,MAAM,QAAQ;YACd,IAAI,CAAC,OAAO,OAAO;YAEnB,MAAM,YACJ,SAAS,aAAa,MAAM,MAAM,GAAG,QAAQ,SAAS,GAClD,GAAG,MAAM,SAAS,CAAC,GAAG,QAAQ,SAAS,EAAE,GAAG,CAAC,GAC7C;YAEN,qBACE,8OAAC;gBAAK,WAAW,SAAS;gBAAW,OAAO;0BACzC;;;;;;QAGP;IACF,CAAC;AAKM,MAAM,sBAAsB,CACjC,aACA,QACA,UAMiB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,QAAQ,EAAE;YACjB,MAAM,WAAW;YACjB,MAAM,QACJ,OAAO,aAAa,WAChB,WAAW,YACV;YAEP,IAAI,UAAU,QAAQ,UAAU,aAAa,MAAM,QAAQ,OAAO;YAElE,IAAI,YAAY,MAAM,QAAQ;YAE9B,IAAI,SAAS,WAAW,YAAY;gBAClC,YAAY,IAAI,KAAK,YAAY,CAAC,SAAS;oBACzC,OAAO;oBACP,UAAU;oBACV,uBAAuB,QAAQ,QAAQ,IAAI;gBAC7C,GAAG,MAAM,CAAC;YACZ,OAAO,IAAI,SAAS,WAAW,cAAc;gBAC3C,YAAY,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,QAAQ,QAAQ,IAAI,GAAG,CAAC,CAAC;YAChE,OAAO,IAAI,SAAS,aAAa,WAAW;gBAC1C,YAAY,MAAM,OAAO,CAAC,QAAQ,QAAQ;YAC5C;YAEA,IAAI,SAAS,QAAQ,YAAY,QAAQ,MAAM,GAAG;YAClD,IAAI,SAAS,QAAQ,YAAY,YAAY,QAAQ,MAAM;YAE3D,qBAAO,8OAAC;gBAAK,WAAU;0BAAa;;;;;;QACtC;IACF,CAAC;AAKM,MAAM,sBAAsB,CACjC,aACA,QACA,SACiB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,QAAQ,EAAE;YACjB,MAAM,QAAQ;YAEd,IAAI,QAAQ;gBACV,qBACE,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAS,QAAQ,YAAY;8BACjC,QAAQ,OAAO,IAAI,GAAG,OAAO,KAAK;;;;;;YAGzC;YAEA,qBACE,8OAAC;gBAAK,WAAW,QAAQ,mBAAmB;0BACzC,QAAQ,MAAM;;;;;;QAGrB;IACF,CAAC;AAKM,MAAM,wBAAwB,IAAwB,CAAC;QAC5D,IAAI;QACJ,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,SACE,MAAM,wBAAwB,MAC7B,MAAM,yBAAyB,MAAM;gBAExC,iBAAiB,CAAA,QAAS,MAAM,yBAAyB,CAAC,CAAC,CAAC;gBAC5D,cAAW;;;;;;QAGf,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,8OAAC,oIAAA,CAAA,WAAQ;gBACP,SAAS,IAAI,aAAa;gBAC1B,iBAAiB,CAAA,QAAS,IAAI,cAAc,CAAC,CAAC,CAAC;gBAC/C,cAAW;;;;;;QAGf,eAAe;QACf,cAAc;IAChB,CAAC;AAKM,MAAM,4BAA4B,CACvC,UACA,aACA,SACiB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,GAAG,EAAE;YACZ,MAAM,QAAQ,IAAI,QAAQ,CAAC;YAC3B,MAAM,WAAY,IAAI,QAAQ,CAAC,gBAA0B;YAEzD,qBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAiC,SAAS;;;;;;oBACxD,0BACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;QAKX;IACF,CAAC;AAKM,MAAM,uBAAuB,CAClC,aACA,QACA,MACA,UAIiB,CAAC;QAClB,aAAa;QACb,QAAQ,qBAAqB;QAC7B,MAAM,CAAC,EAAE,GAAG,EAAE;YACZ,MAAM,QAAQ,IAAI,QAAQ,CAAC;YAC3B,MAAM,OAAO;YACb,MAAM,eAAe,SAAS,YAAY,QAAQ,SAAS,CAAC,SAAS;YAErE,qBACE,8OAAC;gBACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,kDACA,SAAS;;kCAGX,8OAAC;wBAAK,WAAU;;;;;;oBACf,OAAO;;;;;;;QAGd;IACF,CAAC;AAKM,MAAM,8BAA8B,CAEzC,UAakB,CAAC;QACnB,IAAI;QACJ,QAAQ;QACR,MAAM,CAAC,EAAE,GAAG,EAAE;YACZ,MAAM,OAAO,IAAI,QAAQ;YAEzB,qBACE,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,WAAU;;8CAChC,8OAAC;oCAAK,WAAU;8CAAU;;;;;;8CAC1B,8OAAC,gNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAM;;0CACzB,8OAAC,4IAAA,CAAA,oBAAiB;0CAAC;;;;;;4BAGlB,CAAC,QAAQ,MAAM,IAAI,QAAQ,QAAQ,mBAClC;0CACG,QAAQ,QAAQ,iBACf,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,QAAQ,QAAQ,CAAC;;0DAC3B,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;;;;;yDAKnC,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,QAAQ,MAAM,GAAG;;sDAChD,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAgB;;;;;;;;4BAQtC,CAAC,QAAQ,MAAM,IAAI,QAAQ,QAAQ,mBAClC;0CACG,QAAQ,QAAQ,iBACf,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,OAAO;8CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,QAAQ,QAAQ,CAAC;;0DAC3B,8OAAC,2MAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;;;;;yDAKpC,8OAAC,4IAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,QAAQ,MAAM,GAAG;;sDAChD,8OAAC,2MAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAgB;;;;;;;;4BAQvC,QAAQ,aAAa,EAAE,IAAI,CAAC,QAAQ,sBACnC,8OAAC,4IAAA,CAAA,mBAAgB;oCAEf,SAAS,IAAM,OAAO,OAAO,CAAC;oCAC9B,WACE,OAAO,OAAO,KAAK,gBAAgB,qBAAqB;;wCAGzD,OAAO,IAAI,kBAAI,8OAAC,OAAO,IAAI;4CAAC,WAAU;;;;;;wCACtC,OAAO,KAAK;;mCAPR;;;;;4BAYR,QAAQ,UAAU,kBACjB,8OAAC,4IAAA,CAAA,mBAAgB;gCACf,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,KAAK,EAAE;0CAC5D;;;;;;4BAMF,QAAQ,QAAQ,kBACf;;kDACE,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS,IAAM,QAAQ,QAAQ,GAAG;wCAClC,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;;;;;;;;;;;;;;;QAQ/C;IACF,CAAC;AAKM,MAAM,gBAAgB;IAC3B,YAAY;QACV,SAAS;YAAE,SAAS;YAAa,OAAO;QAAU;QAClD,eAAe;YAAE,SAAS;YAAW,OAAO;QAAc;QAC1D,WAAW;YAAE,SAAS;YAAW,OAAO;QAAY;QACpD,WAAW;YAAE,SAAS;YAAe,OAAO;QAAY;IAC1D;IACA,UAAU;QACR,QAAQ;YAAE,SAAS;YAAW,OAAO;QAAS;QAC9C,UAAU;YAAE,SAAS;YAAa,OAAO;QAAW;QACpD,YAAY;YAAE,SAAS;YAAW,OAAO;QAAW;IACtD;IACA,MAAM;QACJ,SAAS;YAAE,SAAS;YAAa,OAAO;QAAU;QAClD,aAAa;YAAE,SAAS;YAAW,OAAO;QAAc;QACxD,WAAW;YAAE,SAAS;YAAW,OAAO;QAAY;QACpD,SAAS;YAAE,SAAS;YAAe,OAAO;QAAU;QACpD,WAAW;YAAE,SAAS;YAAe,OAAO;QAAY;QACxD,UAAU;YAAE,SAAS;YAAW,OAAO;QAAW;IACpD;AACF", "debugId": null}}, {"offset": {"line": 6131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/tables/index.ts"], "sourcesContent": ["/**\r\n * Table Components Index\r\n *\r\n * Centralized exports for all table-related components, providing a clean\r\n * interface for importing table functionality throughout the application.\r\n */\r\n\r\n// Export main table components\r\nexport { DataTable } from './DataTable';\r\nexport type { DataTableProps } from './DataTable';\r\n\r\n// Export column helpers\r\nexport {\r\n  createSortableHeader,\r\n  createDateColumn,\r\n  createStatusColumn,\r\n  createActionsColumn,\r\n  createTextColumn,\r\n  createNumericColumn,\r\n  createBooleanColumn,\r\n  createSelectionColumn,\r\n  createTitleSubtitleColumn,\r\n  createIconTextColumn,\r\n  createEnhancedActionsColumn,\r\n  statusConfigs,\r\n} from './columnHelpers';\r\n\r\n// Re-export TanStack Table types for convenience\r\nexport type {\r\n  ColumnDef,\r\n  ColumnFiltersState,\r\n  SortingState,\r\n  VisibilityState,\r\n  Row,\r\n  Table as TanStackTable,\r\n} from '@tanstack/react-table';\r\n\r\n// Re-export shadcn/ui table components for direct use\r\nexport {\r\n  Table,\r\n  TableBody,\r\n  TableCaption,\r\n  TableCell,\r\n  TableFooter,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@/components/ui/table';\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,+BAA+B;;AAC/B;AAGA,wBAAwB;AACxB;AAyBA,sDAAsD;AACtD", "debugId": null}}, {"offset": {"line": 6163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/delegations/list/DelegationTable.tsx"], "sourcesContent": ["/**\r\n * Migrated Delegation Table Component\r\n *\r\n * This demonstrates how to migrate the existing DelegationTable.tsx to use\r\n * our new standardized DataTable component. This migration reduces the code\r\n * from ~500 lines to ~150 lines while maintaining all functionality.\r\n *\r\n * BEFORE: 500+ lines of custom TanStack Table implementation\r\n * AFTER: 150 lines using our DataTable abstraction\r\n */\r\n\r\n'use client';\r\n\r\nimport type { ColumnDef } from '@tanstack/react-table';\r\nimport { format } from 'date-fns';\r\nimport { Calendar, MapPin, Users, Trash, Archive } from 'lucide-react';\r\n\r\nimport type { Delegation } from '@/lib/types/domain';\r\n\r\nimport {\r\n  DataTable,\r\n  createSelectionColumn,\r\n  createStatusColumn,\r\n  createIconTextColumn,\r\n  createEnhancedActionsColumn,\r\n  createSortableHeader,\r\n} from '@/components/ui/tables';\r\nimport { useToast } from '@/hooks/utils/use-toast';\r\n\r\ninterface DelegationTableProps {\r\n  delegations: Delegation[];\r\n  className?: string;\r\n  onDelete?: (delegation: Delegation) => Promise<void>;\r\n  onBulkDelete?: (delegations: Delegation[]) => Promise<void>;\r\n  onBulkArchive?: (delegations: Delegation[]) => Promise<void>;\r\n}\r\n\r\nexport const DelegationTable: React.FC<DelegationTableProps> = ({\r\n  delegations,\r\n  className = '',\r\n  onDelete,\r\n  onBulkDelete,\r\n  onBulkArchive,\r\n}) => {\r\n  const { toast } = useToast();\r\n\r\n  // Helper function for date formatting (from original DelegationTable)\r\n  const formatDate = (dateString: string) => {\r\n    try {\r\n      return format(new Date(dateString), 'MMM dd, yyyy');\r\n    } catch {\r\n      return 'Invalid Date';\r\n    }\r\n  };\r\n\r\n  // Define columns using our standardized helpers\r\n  const columns: ColumnDef<Delegation>[] = [\r\n    // Row selection (replaces the custom checkbox implementation)\r\n    createSelectionColumn<Delegation>(),\r\n\r\n    // Event name with notes as subtitle (custom implementation to handle notes properly)\r\n    {\r\n      accessorKey: 'eventName',\r\n      header: createSortableHeader('Event Name'),\r\n      cell: ({ row }) => {\r\n        const eventName = row.getValue('eventName') as string;\r\n        const delegation = row.original;\r\n        const notes = delegation.notes;\r\n\r\n        return (\r\n          <div className=\"space-y-1\">\r\n            <div className=\"font-semibold text-foreground\">\r\n              {eventName || '-'}\r\n            </div>\r\n            {notes && (\r\n              <div className=\"line-clamp-1 text-xs text-muted-foreground\">\r\n                {notes}\r\n              </div>\r\n            )}\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n\r\n    // Status with proper formatting (replaces custom status cell)\r\n    createStatusColumn('status', 'Status', {\r\n      Planned: { variant: 'secondary', label: 'Planned' },\r\n      'In Progress': { variant: 'default', label: 'In Progress' },\r\n      Completed: { variant: 'success', label: 'Completed' },\r\n      Cancelled: { variant: 'destructive', label: 'Cancelled' },\r\n    }),\r\n\r\n    // Location with icon (replaces custom cell with MapPin icon)\r\n    createIconTextColumn(\r\n      'location',\r\n      'Location',\r\n      ({ className }: { className?: string }) => (\r\n        <MapPin className={className} />\r\n      )\r\n    ),\r\n\r\n    // Start date with calendar icon (replaces custom date cell)\r\n    {\r\n      accessorKey: 'durationFrom',\r\n      header: createSortableHeader('Start Date'),\r\n      cell: ({ row }) => {\r\n        const date = row.getValue('durationFrom') as string;\r\n        return (\r\n          <div className=\"flex items-center gap-1 text-sm\">\r\n            <Calendar className=\"size-3 text-muted-foreground\" />\r\n            {formatDate(date)}\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n\r\n    // End date with calendar icon (replaces custom date cell)\r\n    {\r\n      accessorKey: 'durationTo',\r\n      header: createSortableHeader('End Date'),\r\n      cell: ({ row }) => {\r\n        const date = row.getValue('durationTo') as string;\r\n        return (\r\n          <div className=\"flex items-center gap-1 text-sm\">\r\n            <Calendar className=\"size-3 text-muted-foreground\" />\r\n            {formatDate(date)}\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n\r\n    // Delegates count with icon (replaces custom delegates cell)\r\n    createIconTextColumn(\r\n      'delegates',\r\n      'Delegates',\r\n      ({ className }: { className?: string }) => (\r\n        <Users className={className} />\r\n      ),\r\n      {\r\n        formatter: delegates => delegates?.length ?? 0,\r\n      }\r\n    ),\r\n\r\n    // Enhanced actions (replaces the complex dropdown implementation)\r\n    createEnhancedActionsColumn({\r\n      viewHref: delegation => `/delegations/${delegation.id}`,\r\n      editHref: delegation => `/delegations/${delegation.id}/edit`,\r\n      ...(onDelete && {\r\n        onDelete: (delegation: Delegation) => {\r\n          onDelete(delegation);\r\n        },\r\n      }),\r\n      showCopyId: true,\r\n      customActions: [\r\n        {\r\n          label: 'Duplicate',\r\n          onClick: delegation => {\r\n            toast({\r\n              title: 'Feature Coming Soon',\r\n              description: `Duplicate functionality for ${delegation.eventName}`,\r\n            });\r\n          },\r\n        },\r\n      ],\r\n    }),\r\n  ];\r\n\r\n  // Bulk actions for selected rows\r\n  const bulkActions = [\r\n    {\r\n      label: 'Delete Selected',\r\n      icon: ({ className }: { className?: string }) => (\r\n        <Trash className={className} />\r\n      ),\r\n      onClick: async (selectedDelegations: Delegation[]) => {\r\n        if (onBulkDelete) {\r\n          await onBulkDelete(selectedDelegations);\r\n          toast({\r\n            title: 'Delegations Deleted',\r\n            description: `${selectedDelegations.length} delegations have been deleted`,\r\n          });\r\n        }\r\n      },\r\n      variant: 'destructive' as const,\r\n    },\r\n    {\r\n      label: 'Archive Selected',\r\n      icon: ({ className }: { className?: string }) => (\r\n        <Archive className={className} />\r\n      ),\r\n      onClick: async (selectedDelegations: Delegation[]) => {\r\n        if (onBulkArchive) {\r\n          await onBulkArchive(selectedDelegations);\r\n          toast({\r\n            title: 'Delegations Archived',\r\n            description: `${selectedDelegations.length} delegations have been archived`,\r\n          });\r\n        }\r\n      },\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <DataTable\r\n      data={delegations}\r\n      columns={columns}\r\n      className={className}\r\n      searchPlaceholder=\"Search delegations by event name or location...\"\r\n      searchColumn=\"eventName\"\r\n      emptyMessage=\"No delegations found. Create your first delegation to get started.\"\r\n      pageSize={15}\r\n      // Advanced features (all the functionality from original DelegationTable)\r\n      enableRowSelection={true}\r\n      enableBulkActions={true}\r\n      bulkActions={bulkActions}\r\n      enableColumnVisibility={true}\r\n      // Professional styling (matches original DelegationTable appearance)\r\n      tableClassName=\"shadow-lg\"\r\n      headerClassName=\"bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900\"\r\n      rowClassName=\"hover:bg-gray-50/50 dark:hover:bg-gray-800/50\"\r\n    />\r\n  );\r\n};\r\n\r\n/**\r\n * MIGRATION SUMMARY:\r\n *\r\n * BEFORE (DelegationTable.tsx):\r\n * - 500+ lines of code\r\n * - Custom TanStack Table setup\r\n * - Manual state management for sorting, filtering, selection\r\n * - Custom column definitions with repetitive patterns\r\n * - Complex dropdown menu implementation\r\n * - Manual styling and responsive design\r\n *\r\n * AFTER (DelegationTableNew.tsx):\r\n * - 150 lines of code (-70% reduction)\r\n * - Uses standardized DataTable component\r\n * - Automatic state management handled by DataTable\r\n * - Reusable column helpers eliminate repetition\r\n * - Enhanced actions column with consistent patterns\r\n * - Professional styling with minimal configuration\r\n *\r\n * BENEFITS:\r\n * - Massive code reduction while maintaining all features\r\n * - Consistent patterns across all tables\r\n * - Easier to maintain and extend\r\n * - Better type safety with helper functions\r\n * - Professional appearance with minimal effort\r\n * - Bulk actions and advanced features included\r\n */\r\n\r\nexport default DelegationTable;\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;;;AAKD;AACA;AAAA;AAAA;AAAA;AAAA;AAIA;AAAA;AAAA;AAQA;AAhBA;;;;;;AA0BO,MAAM,kBAAkD,CAAC,EAC9D,WAAW,EACX,YAAY,EAAE,EACd,QAAQ,EACR,YAAY,EACZ,aAAa,EACd;IACC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD;IAEzB,sEAAsE;IACtE,MAAM,aAAa,CAAC;QAClB,IAAI;YACF,OAAO,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa;QACtC,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,gDAAgD;IAChD,MAAM,UAAmC;QACvC,8DAA8D;QAC9D,CAAA,GAAA,mJAAA,CAAA,wBAAqB,AAAD;QAEpB,qFAAqF;QACrF;YACE,aAAa;YACb,QAAQ,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EAAE;YAC7B,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,YAAY,IAAI,QAAQ,CAAC;gBAC/B,MAAM,aAAa,IAAI,QAAQ;gBAC/B,MAAM,QAAQ,WAAW,KAAK;gBAE9B,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,aAAa;;;;;;wBAEf,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;YAKX;QACF;QAEA,8DAA8D;QAC9D,CAAA,GAAA,mJAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,UAAU;YACrC,SAAS;gBAAE,SAAS;gBAAa,OAAO;YAAU;YAClD,eAAe;gBAAE,SAAS;gBAAW,OAAO;YAAc;YAC1D,WAAW;gBAAE,SAAS;gBAAW,OAAO;YAAY;YACpD,WAAW;gBAAE,SAAS;gBAAe,OAAO;YAAY;QAC1D;QAEA,6DAA6D;QAC7D,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EACjB,YACA,YACA,CAAC,EAAE,SAAS,EAA0B,iBACpC,8OAAC,0MAAA,CAAA,SAAM;gBAAC,WAAW;;;;;;QAIvB,4DAA4D;QAC5D;YACE,aAAa;YACb,QAAQ,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EAAE;YAC7B,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ,CAAC;gBAC1B,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBACnB,WAAW;;;;;;;YAGlB;QACF;QAEA,0DAA0D;QAC1D;YACE,aAAa;YACb,QAAQ,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EAAE;YAC7B,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ,CAAC;gBAC1B,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBACnB,WAAW;;;;;;;YAGlB;QACF;QAEA,6DAA6D;QAC7D,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EACjB,aACA,aACA,CAAC,EAAE,SAAS,EAA0B,iBACpC,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAW;;;;;sBAEpB;YACE,WAAW,CAAA,YAAa,WAAW,UAAU;QAC/C;QAGF,kEAAkE;QAClE,CAAA,GAAA,mJAAA,CAAA,8BAA2B,AAAD,EAAE;YAC1B,UAAU,CAAA,aAAc,CAAC,aAAa,EAAE,WAAW,EAAE,EAAE;YACvD,UAAU,CAAA,aAAc,CAAC,aAAa,EAAE,WAAW,EAAE,CAAC,KAAK,CAAC;YAC5D,GAAI,YAAY;gBACd,UAAU,CAAC;oBACT,SAAS;gBACX;YACF,CAAC;YACD,YAAY;YACZ,eAAe;gBACb;oBACE,OAAO;oBACP,SAAS,CAAA;wBACP,MAAM;4BACJ,OAAO;4BACP,aAAa,CAAC,4BAA4B,EAAE,WAAW,SAAS,EAAE;wBACpE;oBACF;gBACF;aACD;QACH;KACD;IAED,iCAAiC;IACjC,MAAM,cAAc;QAClB;YACE,OAAO;YACP,MAAM,CAAC,EAAE,SAAS,EAA0B,iBAC1C,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAW;;;;;;YAEpB,SAAS,OAAO;gBACd,IAAI,cAAc;oBAChB,MAAM,aAAa;oBACnB,MAAM;wBACJ,OAAO;wBACP,aAAa,GAAG,oBAAoB,MAAM,CAAC,8BAA8B,CAAC;oBAC5E;gBACF;YACF;YACA,SAAS;QACX;QACA;YACE,OAAO;YACP,MAAM,CAAC,EAAE,SAAS,EAA0B,iBAC1C,8OAAC,wMAAA,CAAA,UAAO;oBAAC,WAAW;;;;;;YAEtB,SAAS,OAAO;gBACd,IAAI,eAAe;oBACjB,MAAM,cAAc;oBACpB,MAAM;wBACJ,OAAO;wBACP,aAAa,GAAG,oBAAoB,MAAM,CAAC,+BAA+B,CAAC;oBAC7E;gBACF;YACF;QACF;KACD;IAED,qBACE,8OAAC,+IAAA,CAAA,YAAS;QACR,MAAM;QACN,SAAS;QACT,WAAW;QACX,mBAAkB;QAClB,cAAa;QACb,cAAa;QACb,UAAU;QACV,0EAA0E;QAC1E,oBAAoB;QACpB,mBAAmB;QACnB,aAAa;QACb,wBAAwB;QACxB,qEAAqE;QACrE,gBAAe;QACf,iBAAgB;QAChB,cAAa;;;;;;AAGnB;uCA8Be", "debugId": null}}, {"offset": {"line": 6424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/delegations/list/DelegationListContainer.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useCallback, useMemo } from 'react';\r\n\r\nimport type { Delegation } from '@/lib/types/domain'; // Use domain type directly\r\nimport type { DelegationFilterValues } from '@/components/features/delegations/DelegationFilters';\r\n\r\nimport { useDelegations } from '@/lib/stores/queries/useDelegations'; // Import useDelegations hook\r\n\r\ninterface DelegationListContainerProps {\r\n  children: (data: {\r\n    delegations: Delegation[];\r\n    error: null | string;\r\n    fetchDelegations: () => Promise<void>;\r\n    loading: boolean;\r\n  }) => React.ReactNode;\r\n  searchTerm?: string;\r\n  filters?: DelegationFilterValues;\r\n}\r\n\r\nexport default function DelegationListContainer({\r\n  children,\r\n  searchTerm = '',\r\n  filters,\r\n}: DelegationListContainerProps) {\r\n  console.log('DelegationListContainer rendered');\r\n  const {\r\n    data: allDelegations = [],\r\n    error: queryError,\r\n    isLoading: dataIsLoading,\r\n    refetch,\r\n  } = useDelegations();\r\n\r\n  const error = queryError ? queryError.message : null;\r\n\r\n  // ✅ PRODUCTION-READY FIX: Use useMemo to prevent infinite re-renders\r\n  // Memoize the filtered delegations based on stable dependencies\r\n  const filteredDelegations = useMemo(() => {\r\n    console.log('Filtering delegations', {\r\n      allDelegationsCount: allDelegations.length,\r\n      searchTerm,\r\n      filters,\r\n    });\r\n\r\n    let tempDelegations = [...allDelegations];\r\n\r\n    // Apply search filter (from filters or legacy searchTerm)\r\n    const searchQuery = filters?.search || searchTerm;\r\n    if (searchQuery) {\r\n      const lowercaseSearchTerm = searchQuery.toLowerCase();\r\n      tempDelegations = tempDelegations.filter(delegation => {\r\n        return (\r\n          delegation.eventName.toLowerCase().includes(lowercaseSearchTerm) ||\r\n          delegation.location.toLowerCase().includes(lowercaseSearchTerm) ||\r\n          delegation.status.toLowerCase().includes(lowercaseSearchTerm) ||\r\n          delegation.delegates?.some(delegate =>\r\n            delegate.name.toLowerCase().includes(lowercaseSearchTerm)\r\n          )\r\n        );\r\n      });\r\n    }\r\n\r\n    // Apply status filter\r\n    if (filters?.status && filters.status.length > 0) {\r\n      tempDelegations = tempDelegations.filter(delegation =>\r\n        filters.status.includes(delegation.status)\r\n      );\r\n    }\r\n\r\n    // Apply location filter\r\n    if (filters?.location && filters.location.length > 0) {\r\n      tempDelegations = tempDelegations.filter(delegation =>\r\n        filters.location.includes(delegation.location)\r\n      );\r\n    }\r\n\r\n    // Apply date range filter\r\n    if (\r\n      filters?.dateRange &&\r\n      (filters.dateRange.from || filters.dateRange.to)\r\n    ) {\r\n      tempDelegations = tempDelegations.filter(delegation => {\r\n        const delegationStartDate = new Date(delegation.durationFrom);\r\n        const delegationEndDate = new Date(delegation.durationTo);\r\n\r\n        if (filters.dateRange.from && filters.dateRange.to) {\r\n          // Both dates selected - check if delegation overlaps with range\r\n          return (\r\n            delegationStartDate <= filters.dateRange.to &&\r\n            delegationEndDate >= filters.dateRange.from\r\n          );\r\n        } else if (filters.dateRange.from) {\r\n          // Only start date selected - delegation must end after this date\r\n          return delegationEndDate >= filters.dateRange.from;\r\n        } else if (filters.dateRange.to) {\r\n          // Only end date selected - delegation must start before this date\r\n          return delegationStartDate <= filters.dateRange.to;\r\n        }\r\n        return true;\r\n      });\r\n    }\r\n\r\n    // Apply driver filter\r\n    if (filters?.drivers && filters.drivers.length > 0) {\r\n      tempDelegations = tempDelegations.filter(delegation =>\r\n        delegation.drivers?.some(driver =>\r\n          filters.drivers.includes(driver.employee?.id.toString() || '')\r\n        )\r\n      );\r\n    }\r\n\r\n    // Apply escort filter\r\n    if (filters?.escorts && filters.escorts.length > 0) {\r\n      tempDelegations = tempDelegations.filter(delegation =>\r\n        delegation.escorts?.some(escort =>\r\n          filters.escorts.includes(escort.employee?.id.toString() || '')\r\n        )\r\n      );\r\n    }\r\n\r\n    // Apply vehicle filter\r\n    if (filters?.vehicles && filters.vehicles.length > 0) {\r\n      tempDelegations = tempDelegations.filter(delegation =>\r\n        delegation.vehicles?.some(vehicle =>\r\n          filters.vehicles.includes(vehicle.vehicle?.id.toString() || '')\r\n        )\r\n      );\r\n    }\r\n\r\n    return tempDelegations;\r\n  }, [searchTerm, allDelegations, filters]);\r\n\r\n  const stableFetchDelegations = useCallback(async () => {\r\n    console.log('Manual fetch triggered via fetchDelegations prop (refetch)');\r\n    await refetch();\r\n  }, [refetch]);\r\n\r\n  return (\r\n    <>\r\n      {children({\r\n        delegations: filteredDelegations,\r\n        error: error,\r\n        fetchDelegations: stableFetchDelegations,\r\n        loading: dataIsLoading,\r\n      })}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAKA,8PAAsE,6BAA6B;AAPnG;;;;AAoBe,SAAS,wBAAwB,EAC9C,QAAQ,EACR,aAAa,EAAE,EACf,OAAO,EACsB;IAC7B,QAAQ,GAAG,CAAC;IACZ,MAAM,EACJ,MAAM,iBAAiB,EAAE,EACzB,OAAO,UAAU,EACjB,WAAW,aAAa,EACxB,OAAO,EACR,GAAG,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IAEjB,MAAM,QAAQ,aAAa,WAAW,OAAO,GAAG;IAEhD,qEAAqE;IACrE,gEAAgE;IAChE,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAClC,QAAQ,GAAG,CAAC,yBAAyB;YACnC,qBAAqB,eAAe,MAAM;YAC1C;YACA;QACF;QAEA,IAAI,kBAAkB;eAAI;SAAe;QAEzC,0DAA0D;QAC1D,MAAM,cAAc,SAAS,UAAU;QACvC,IAAI,aAAa;YACf,MAAM,sBAAsB,YAAY,WAAW;YACnD,kBAAkB,gBAAgB,MAAM,CAAC,CAAA;gBACvC,OACE,WAAW,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,wBAC5C,WAAW,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,wBAC3C,WAAW,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,wBACzC,WAAW,SAAS,EAAE,KAAK,CAAA,WACzB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;YAG3C;QACF;QAEA,sBAAsB;QACtB,IAAI,SAAS,UAAU,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;YAChD,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,aACvC,QAAQ,MAAM,CAAC,QAAQ,CAAC,WAAW,MAAM;QAE7C;QAEA,wBAAwB;QACxB,IAAI,SAAS,YAAY,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YACpD,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,aACvC,QAAQ,QAAQ,CAAC,QAAQ,CAAC,WAAW,QAAQ;QAEjD;QAEA,0BAA0B;QAC1B,IACE,SAAS,aACT,CAAC,QAAQ,SAAS,CAAC,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAC/C;YACA,kBAAkB,gBAAgB,MAAM,CAAC,CAAA;gBACvC,MAAM,sBAAsB,IAAI,KAAK,WAAW,YAAY;gBAC5D,MAAM,oBAAoB,IAAI,KAAK,WAAW,UAAU;gBAExD,IAAI,QAAQ,SAAS,CAAC,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,EAAE;oBAClD,gEAAgE;oBAChE,OACE,uBAAuB,QAAQ,SAAS,CAAC,EAAE,IAC3C,qBAAqB,QAAQ,SAAS,CAAC,IAAI;gBAE/C,OAAO,IAAI,QAAQ,SAAS,CAAC,IAAI,EAAE;oBACjC,iEAAiE;oBACjE,OAAO,qBAAqB,QAAQ,SAAS,CAAC,IAAI;gBACpD,OAAO,IAAI,QAAQ,SAAS,CAAC,EAAE,EAAE;oBAC/B,kEAAkE;oBAClE,OAAO,uBAAuB,QAAQ,SAAS,CAAC,EAAE;gBACpD;gBACA,OAAO;YACT;QACF;QAEA,sBAAsB;QACtB,IAAI,SAAS,WAAW,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG;YAClD,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,aACvC,WAAW,OAAO,EAAE,KAAK,CAAA,SACvB,QAAQ,OAAO,CAAC,QAAQ,CAAC,OAAO,QAAQ,EAAE,GAAG,cAAc;QAGjE;QAEA,sBAAsB;QACtB,IAAI,SAAS,WAAW,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG;YAClD,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,aACvC,WAAW,OAAO,EAAE,KAAK,CAAA,SACvB,QAAQ,OAAO,CAAC,QAAQ,CAAC,OAAO,QAAQ,EAAE,GAAG,cAAc;QAGjE;QAEA,uBAAuB;QACvB,IAAI,SAAS,YAAY,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YACpD,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,aACvC,WAAW,QAAQ,EAAE,KAAK,CAAA,UACxB,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,OAAO,EAAE,GAAG,cAAc;QAGlE;QAEA,OAAO;IACT,GAAG;QAAC;QAAY;QAAgB;KAAQ;IAExC,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,QAAQ,GAAG,CAAC;QACZ,MAAM;IACR,GAAG;QAAC;KAAQ;IAEZ,qBACE;kBACG,SAAS;YACR,aAAa;YACb,OAAO;YACP,kBAAkB;YAClB,SAAS;QACX;;AAGN", "debugId": null}}, {"offset": {"line": 6522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/delegations/calendar/DelegationCalendar.tsx"], "sourcesContent": ["/**\r\n * @file Enhanced delegation calendar view component with modern shadcn/ui\r\n * @module components/delegations/DelegationCalendar\r\n */\r\n\r\n'use client';\r\n\r\nimport {\r\n  eachDayOfInterval,\r\n  endOfMonth,\r\n  format,\r\n  isToday,\r\n  startOfMonth,\r\n} from 'date-fns';\r\nimport { Calendar, ChevronLeft, ChevronRight } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport * as React from 'react';\r\n\r\nimport type { Delegation, DelegationStatusPrisma } from '@/lib/types/domain'; // Added DelegationStatusPrisma\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { cn } from '@/lib/utils';\r\nimport {\r\n  formatDelegationTime,\r\n  getStatusColor,\r\n} from '@/lib/utils/delegationUtils';\r\nimport { formatDelegationStatusForDisplay } from '@/lib/utils/formattingUtils';\r\n\r\n/**\r\n * Props for DelegationCalendar component\r\n */\r\ninterface DelegationCalendarProps {\r\n  className?: string;\r\n  delegations: Delegation[];\r\n}\r\n\r\n/**\r\n * Get delegations for a specific date\r\n */\r\nconst getDelegationsForDate = (delegations: Delegation[], date: Date) => {\r\n  return delegations.filter(delegation => {\r\n    const startDate = new Date(delegation.durationFrom);\r\n    const endDate = new Date(delegation.durationTo);\r\n    return date >= startDate && date <= endDate;\r\n  });\r\n};\r\n\r\n/**\r\n * Enhanced delegation calendar component with modern shadcn/ui patterns.\r\n *\r\n * Features:\r\n * - Multiple view modes (month, week, agenda)\r\n * - Advanced filtering and search\r\n * - Interactive event details\r\n * - Status-based color coding\r\n * - Responsive design\r\n * - Modern shadcn/ui styling\r\n *\r\n * @param props - Component props\r\n * @returns JSX element representing the enhanced delegation calendar\r\n */\r\nexport const DelegationCalendar: React.FC<DelegationCalendarProps> = ({\r\n  className = '',\r\n  delegations,\r\n}) => {\r\n  const [currentDate, setCurrentDate] = React.useState<Date>(new Date());\r\n\r\n  const monthStart = startOfMonth(currentDate);\r\n  const monthEnd = endOfMonth(currentDate);\r\n  const monthDays = eachDayOfInterval({ end: monthEnd, start: monthStart });\r\n\r\n  const goToPreviousMonth = () => {\r\n    setCurrentDate(\r\n      new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1)\r\n    );\r\n  };\r\n\r\n  const goToNextMonth = () => {\r\n    setCurrentDate(\r\n      new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1)\r\n    );\r\n  };\r\n\r\n  const goToToday = () => {\r\n    setCurrentDate(new Date());\r\n  };\r\n\r\n  return (\r\n    <div className={cn('space-y-6', className)}>\r\n      {/* Professional Header */}\r\n      <Card className=\"border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-900\">\r\n        <CardHeader className=\"pb-4\">\r\n          <div className=\"flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0\">\r\n            <CardTitle className=\"flex items-center gap-3 text-2xl font-semibold text-gray-900 dark:text-white\">\r\n              <div className=\"rounded-full bg-blue-600 p-2\">\r\n                <Calendar className=\"size-6 text-white\" />\r\n              </div>\r\n              {format(currentDate, 'MMMM yyyy')}\r\n            </CardTitle>\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button\r\n                className=\"border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800\"\r\n                onClick={goToToday}\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n              >\r\n                Today\r\n              </Button>\r\n              <Button\r\n                className=\"border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800\"\r\n                onClick={goToPreviousMonth}\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n              >\r\n                <ChevronLeft className=\"size-4\" />\r\n              </Button>\r\n              <Button\r\n                className=\"border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800\"\r\n                onClick={goToNextMonth}\r\n                size=\"sm\"\r\n                variant=\"outline\"\r\n              >\r\n                <ChevronRight className=\"size-4\" />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n      </Card>\r\n\r\n      {/* Professional Calendar Grid */}\r\n      <Card className=\"border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-900\">\r\n        <CardContent className=\"p-6\">\r\n          {/* Calendar Grid */}\r\n          <div className=\"grid grid-cols-7 gap-1\">\r\n            {/* Day Headers */}\r\n            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (\r\n              <div\r\n                className=\"border border-gray-200 bg-gray-50 p-3 text-center text-sm font-semibold text-gray-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-300\"\r\n                key={day}\r\n              >\r\n                {day}\r\n              </div>\r\n            ))}\r\n\r\n            {/* Calendar Days */}\r\n            {monthDays.map(day => {\r\n              const dayDelegations = getDelegationsForDate(delegations, day);\r\n              const isCurrentDay = isToday(day);\r\n\r\n              return (\r\n                <div\r\n                  className={cn(\r\n                    'min-h-[120px] border border-gray-200 dark:border-gray-700 p-2 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200',\r\n                    isCurrentDay &&\r\n                      'bg-blue-50 dark:bg-blue-950/30 border-blue-300 dark:border-blue-700'\r\n                  )}\r\n                  key={day.toISOString()}\r\n                >\r\n                  <div\r\n                    className={cn(\r\n                      'text-sm font-medium mb-2 flex items-center justify-center w-6 h-6 rounded-full',\r\n                      isCurrentDay\r\n                        ? 'bg-blue-600 text-white shadow-sm'\r\n                        : 'text-gray-700 dark:text-gray-300'\r\n                    )}\r\n                  >\r\n                    {format(day, 'd')}\r\n                  </div>\r\n\r\n                  <div className=\"space-y-1\">\r\n                    {dayDelegations.slice(0, 2).map(delegation => (\r\n                      <Link\r\n                        className=\"block\"\r\n                        href={`/delegations/${delegation.id}`}\r\n                        key={delegation.id}\r\n                      >\r\n                        <div\r\n                          className={cn(\r\n                            'text-xs p-2 rounded border cursor-pointer hover:shadow-sm transition-all duration-200',\r\n                            getStatusColor(\r\n                              delegation.status as DelegationStatusPrisma\r\n                            ) // Explicit cast\r\n                          )}\r\n                          title={`${delegation.eventName} - ${formatDelegationStatusForDisplay(delegation.status)}`}\r\n                        >\r\n                          <div className=\"truncate font-medium\">\r\n                            {delegation.eventName}\r\n                          </div>\r\n                          <div className=\"mt-0.5 text-xs opacity-75\">\r\n                            {formatDelegationTime(delegation.durationFrom)}\r\n                          </div>\r\n                        </div>\r\n                      </Link>\r\n                    ))}\r\n\r\n                    {dayDelegations.length > 2 && (\r\n                      <div className=\"rounded border border-gray-200 bg-gray-100 p-1 text-center text-xs text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400\">\r\n                        +{dayDelegations.length - 2} more\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n\r\n          {/* Professional Legend */}\r\n          <div className=\"mt-8 border-t border-gray-200 pt-6 dark:border-gray-700\">\r\n            <div className=\"mb-4 text-sm font-semibold text-gray-900 dark:text-white\">\r\n              Status Legend\r\n            </div>\r\n            <div className=\"flex flex-wrap gap-3\">\r\n              {(\r\n                [\r\n                  { label: 'Planned', status: 'Planned' as const },\r\n                  { label: 'Confirmed', status: 'Confirmed' as const },\r\n                  { label: 'In Progress', status: 'In_Progress' as const },\r\n                  { label: 'Completed', status: 'Completed' as const },\r\n                  { label: 'Cancelled', status: 'Cancelled' as const },\r\n                ] as const\r\n              ).map(({ label, status }) => (\r\n                <div\r\n                  className=\"flex items-center gap-2 rounded-lg border border-gray-200 bg-gray-50 px-3 py-2 dark:border-gray-700 dark:bg-gray-800\"\r\n                  key={status}\r\n                >\r\n                  <div\r\n                    className={cn(\r\n                      'w-3 h-3 rounded-full',\r\n                      getStatusColor(status)\r\n                    )}\r\n                  />\r\n                  <span className=\"text-xs font-medium text-gray-700 dark:text-gray-300\">\r\n                    {label}\r\n                  </span>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Professional Summary */}\r\n          <div className=\"mt-6 border-t border-gray-200 pt-4 dark:border-gray-700\">\r\n            <div className=\"rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-sm text-gray-600 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400\">\r\n              <span className=\"font-medium text-gray-900 dark:text-white\">\r\n                {delegations.length}\r\n              </span>{' '}\r\n              delegation{delegations.length === 1 ? '' : 's'} this month\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DelegationCalendar;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAID;AAAA;AAAA;AAAA;AAAA;AAOA;AAAA;AAAA;AACA;AACA;AAIA;AACA;AACA;AAAA;AACA;AAIA;AAtBA;;;;;;;;;;;AAgCA;;CAEC,GACD,MAAM,wBAAwB,CAAC,aAA2B;IACxD,OAAO,YAAY,MAAM,CAAC,CAAA;QACxB,MAAM,YAAY,IAAI,KAAK,WAAW,YAAY;QAClD,MAAM,UAAU,IAAI,KAAK,WAAW,UAAU;QAC9C,OAAO,QAAQ,aAAa,QAAQ;IACtC;AACF;AAgBO,MAAM,qBAAwD,CAAC,EACpE,YAAY,EAAE,EACd,WAAW,EACZ;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAQ,IAAI;IAE/D,MAAM,aAAa,CAAA,GAAA,4IAAA,CAAA,eAAY,AAAD,EAAE;IAChC,MAAM,WAAW,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD,EAAE;IAC5B,MAAM,YAAY,CAAA,GAAA,iJAAA,CAAA,oBAAiB,AAAD,EAAE;QAAE,KAAK;QAAU,OAAO;IAAW;IAEvE,MAAM,oBAAoB;QACxB,eACE,IAAI,KAAK,YAAY,WAAW,IAAI,YAAY,QAAQ,KAAK,GAAG;IAEpE;IAEA,MAAM,gBAAgB;QACpB,eACE,IAAI,KAAK,YAAY,WAAW,IAAI,YAAY,QAAQ,KAAK,GAAG;IAEpE;IAEA,MAAM,YAAY;QAChB,eAAe,IAAI;IACrB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;oCAErB,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,aAAa;;;;;;;0CAEvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS;wCACT,MAAK;wCACL,SAAQ;kDACT;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS;wCACT,MAAK;wCACL,SAAQ;kDAER,cAAA,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS;wCACT,MAAK;wCACL,SAAQ;kDAER,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,8OAAC;4BAAI,WAAU;;gCAEZ;oCAAC;oCAAO;oCAAO;oCAAO;oCAAO;oCAAO;oCAAO;iCAAM,CAAC,GAAG,CAAC,CAAA,oBACrD,8OAAC;wCACC,WAAU;kDAGT;uCAFI;;;;;gCAOR,UAAU,GAAG,CAAC,CAAA;oCACb,MAAM,iBAAiB,sBAAsB,aAAa;oCAC1D,MAAM,eAAe,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE;oCAE7B,qBACE,8OAAC;wCACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,kKACA,gBACE;;0DAIJ,8OAAC;gDACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,kFACA,eACI,qCACA;0DAGL,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,KAAK;;;;;;0DAGf,8OAAC;gDAAI,WAAU;;oDACZ,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,2BAC9B,8OAAC,4JAAA,CAAA,UAAI;4DACH,WAAU;4DACV,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,EAAE;sEAGrC,cAAA,8OAAC;gEACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,yFACA,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD,EACX,WAAW,MAAM,EACjB,gBAAgB;;gEAEpB,OAAO,GAAG,WAAW,SAAS,CAAC,GAAG,EAAE,CAAA,GAAA,sIAAA,CAAA,mCAAgC,AAAD,EAAE,WAAW,MAAM,GAAG;;kFAEzF,8OAAC;wEAAI,WAAU;kFACZ,WAAW,SAAS;;;;;;kFAEvB,8OAAC;wEAAI,WAAU;kFACZ,CAAA,GAAA,sIAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,YAAY;;;;;;;;;;;;2DAf5C,WAAW,EAAE;;;;;oDAqBrB,eAAe,MAAM,GAAG,mBACvB,8OAAC;wDAAI,WAAU;;4DAA4I;4DACvJ,eAAe,MAAM,GAAG;4DAAE;;;;;;;;;;;;;;uCAzC7B,IAAI,WAAW;;;;;gCA+C1B;;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA2D;;;;;;8CAG1E,8OAAC;oCAAI,WAAU;8CACZ,AACC;wCACE;4CAAE,OAAO;4CAAW,QAAQ;wCAAmB;wCAC/C;4CAAE,OAAO;4CAAa,QAAQ;wCAAqB;wCACnD;4CAAE,OAAO;4CAAe,QAAQ;wCAAuB;wCACvD;4CAAE,OAAO;4CAAa,QAAQ;wCAAqB;wCACnD;4CAAE,OAAO;4CAAa,QAAQ;wCAAqB;qCACpD,CACD,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,iBACtB,8OAAC;4CACC,WAAU;;8DAGV,8OAAC;oDACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wBACA,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;8DAGnB,8OAAC;oDAAK,WAAU;8DACb;;;;;;;2CATE;;;;;;;;;;;;;;;;sCAiBb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDACb,YAAY,MAAM;;;;;;oCACb;oCAAI;oCACD,YAAY,MAAM,KAAK,IAAI,KAAK;oCAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7D;uCAEe", "debugId": null}}, {"offset": {"line": 6916, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/action-button.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { Loader2 } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport type { ButtonProps } from '@/components/ui/button';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { cn } from '@/lib/utils';\r\n\r\nexport interface ActionButtonProps extends Omit<ButtonProps, 'variant'> {\r\n  /**\r\n   * The type of action this button represents\r\n   * - primary: Main actions (Create, Save, Submit)\r\n   * - secondary: Alternative actions (View, Edit)\r\n   * - tertiary: Optional actions (Cancel, Back)\r\n   * - danger: Destructive actions (Delete, Remove)\r\n   */\r\n  actionType?: ActionType;\r\n\r\n  /**\r\n   * Icon to display before the button text\r\n   * Should be a Lucide icon with consistent sizing (h-4 w-4)\r\n   */\r\n  icon?: React.ReactNode;\r\n\r\n  /**\r\n   * Whether the button is in a loading state\r\n   */\r\n  isLoading?: boolean;\r\n\r\n  /**\r\n   * Text to display when button is loading\r\n   * If not provided, will use children\r\n   */\r\n  loadingText?: string;\r\n}\r\n\r\nexport type ActionType = 'danger' | 'primary' | 'secondary' | 'tertiary';\r\n\r\n/**\r\n * ActionButton component for consistent action styling across the application\r\n *\r\n * @example\r\n * <ActionButton actionType=\"primary\" icon={<PlusCircle />}>\r\n *   Add New\r\n * </ActionButton>\r\n */\r\nexport const ActionButton = React.forwardRef<\r\n  HTMLButtonElement,\r\n  ActionButtonProps\r\n>(\r\n  (\r\n    {\r\n      actionType = 'primary',\r\n      asChild = false,\r\n      children,\r\n      className,\r\n      disabled,\r\n      icon,\r\n      isLoading = false,\r\n      loadingText,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    // Map action types to shadcn/ui button variants and additional styling\r\n    const actionStyles: Record<\r\n      ActionType,\r\n      { className: string; variant: ButtonProps['variant']; }\r\n    > = {\r\n      danger: {\r\n        className: 'shadow-md',\r\n        variant: 'destructive',\r\n      },\r\n      primary: {\r\n        className: 'shadow-md',\r\n        variant: 'default',\r\n      },\r\n      secondary: {\r\n        className: '',\r\n        variant: 'secondary',\r\n      },\r\n      tertiary: {\r\n        className: '',\r\n        variant: 'outline',\r\n      },\r\n    };\r\n\r\n    const { className: actionClassName, variant } = actionStyles[actionType];\r\n\r\n    // const Comp = asChild ? Slot : \"button\"; // This was for an older structure, Button handles asChild now\r\n\r\n    return (\r\n      <Button\r\n        asChild={asChild} // This is passed to the underlying shadcn Button\r\n        className={cn(actionClassName, className)}\r\n        disabled={isLoading || disabled}\r\n        ref={ref}\r\n        variant={variant}\r\n        {...props}\r\n      >\r\n        {isLoading ? (\r\n          <span className=\"inline-flex items-center\">\r\n            {' '}\r\n            {/* Replaced Fragment with span */}\r\n            <Loader2 className=\"mr-2 size-4 animate-spin\" />\r\n            {loadingText || children}\r\n          </span>\r\n        ) : (\r\n          <span className=\"inline-flex items-center\">\r\n            {' '}\r\n            {/* Replaced Fragment with span */}\r\n            {icon && <span className=\"mr-2\">{icon}</span>}\r\n            {children}\r\n          </span>\r\n        )}\r\n      </Button>\r\n    );\r\n  }\r\n);\r\n\r\nActionButton.displayName = 'ActionButton';\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAIA;AACA;AAAA;AATA;;;;;;AAiDO,MAAM,6BAAe,qMAAA,CAAA,UAAK,CAAC,UAAU,CAI1C,CACE,EACE,aAAa,SAAS,EACtB,UAAU,KAAK,EACf,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,YAAY,KAAK,EACjB,WAAW,EACX,GAAG,OACJ,EACD;IAEA,uEAAuE;IACvE,MAAM,eAGF;QACF,QAAQ;YACN,WAAW;YACX,SAAS;QACX;QACA,SAAS;YACP,WAAW;YACX,SAAS;QACX;QACA,WAAW;YACT,WAAW;YACX,SAAS;QACX;QACA,UAAU;YACR,WAAW;YACX,SAAS;QACX;IACF;IAEA,MAAM,EAAE,WAAW,eAAe,EAAE,OAAO,EAAE,GAAG,YAAY,CAAC,WAAW;IAExE,yGAAyG;IAEzG,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC/B,UAAU,aAAa;QACvB,KAAK;QACL,SAAS;QACR,GAAG,KAAK;kBAER,0BACC,8OAAC;YAAK,WAAU;;gBACb;8BAED,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;gBAClB,eAAe;;;;;;iCAGlB,8OAAC;YAAK,WAAU;;gBACb;gBAEA,sBAAQ,8OAAC;oBAAK,WAAU;8BAAQ;;;;;;gBAChC;;;;;;;;;;;;AAKX;AAGF,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 7009, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/status-badge.tsx"], "sourcesContent": ["/**\r\n * @file StatusBadge component for delegation status display\r\n * @module components/ui/status-badge\r\n */\r\n\r\nimport React from 'react';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { cn } from '@/lib/utils';\r\nimport { getStatusColor } from '@/lib/utils/delegationUtils';\r\nimport { formatDelegationStatusForDisplay } from '@/lib/utils/formattingUtils';\r\nimport type { Delegation } from '@/lib/types/domain';\r\n\r\ninterface StatusBadgeProps {\r\n  /** Delegation status */\r\n  status: Delegation['status'];\r\n  /** Additional CSS classes */\r\n  className?: string;\r\n  /** Size variant */\r\n  size?: 'sm' | 'md' | 'lg';\r\n  /** Whether to show as floating badge */\r\n  floating?: boolean;\r\n}\r\n\r\n/**\r\n * Reusable component for displaying delegation status\r\n * Follows SRP by handling only status display logic\r\n */\r\nexport const StatusBadge: React.FC<StatusBadgeProps> = ({\r\n  status,\r\n  className,\r\n  size = 'md',\r\n  floating = false,\r\n}) => {\r\n  const sizeClasses = {\r\n    sm: 'text-xs py-1 px-2',\r\n    md: 'text-xs py-1.5 px-3',\r\n    lg: 'text-sm py-2 px-4',\r\n  };\r\n\r\n  const baseClasses = cn(\r\n    'font-medium border shadow-sm transition-all duration-200',\r\n    sizeClasses[size],\r\n    floating && 'absolute top-4 right-4',\r\n    getStatusColor(status)\r\n  );\r\n\r\n  return (\r\n    <Badge className={cn(baseClasses, className)}>\r\n      {formatDelegationStatusForDisplay(status)}\r\n    </Badge>\r\n  );\r\n};\r\n\r\nexport default StatusBadge;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AAAA;AACA;AACA;;;;;;AAkBO,MAAM,cAA0C,CAAC,EACtD,MAAM,EACN,SAAS,EACT,OAAO,IAAI,EACX,WAAW,KAAK,EACjB;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACnB,4DACA,WAAW,CAAC,KAAK,EACjB,YAAY,0BACZ,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD,EAAE;IAGjB,qBACE,8OAAC,iIAAA,CAAA,QAAK;QAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;kBAC/B,CAAA,GAAA,sIAAA,CAAA,mCAAgC,AAAD,EAAE;;;;;;AAGxC;uCAEe", "debugId": null}}, {"offset": {"line": 7050, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/domain/useDelegationInfo.ts"], "sourcesContent": ["/**\r\n * @file Custom hook for delegation info extraction\r\n * @module hooks/useDelegationInfo\r\n */\r\n\r\nimport { useMemo } from 'react';\r\nimport type {\r\n  Delegation,\r\n  DelegationEscort,\r\n  DelegationVehicleAssignment,\r\n} from '@/lib/types/domain';\r\n\r\n// Define types for the partial employee/vehicle data\r\ntype PartialEmployeeInfo = NonNullable<DelegationEscort['employee']>;\r\ntype PartialVehicleInfo = NonNullable<DelegationVehicleAssignment['vehicle']>;\r\n\r\ninterface DelegationInfo {\r\n  escortInfo: PartialEmployeeInfo | null;\r\n  driverInfo: PartialEmployeeInfo | null;\r\n  vehicleInfo: PartialVehicleInfo | null;\r\n  hasFlightDetails: boolean;\r\n  needsEscortAssignment: boolean;\r\n  isActive: boolean;\r\n}\r\n\r\n/**\r\n * Custom hook to extract and process delegation information\r\n * Follows SRP by handling only data extraction logic\r\n *\r\n * @param delegation - Delegation object\r\n * @returns Processed delegation information\r\n */\r\nexport const useDelegationInfo = (delegation: Delegation): DelegationInfo => {\r\n  return useMemo(() => {\r\n    // Extract escort information\r\n    const escortInfo =\r\n      delegation.escorts &&\r\n      delegation.escorts.length > 0 &&\r\n      delegation.escorts[0]?.employee\r\n        ? delegation.escorts[0].employee\r\n        : null;\r\n\r\n    // Extract driver information\r\n    const driverInfo =\r\n      delegation.drivers &&\r\n      delegation.drivers.length > 0 &&\r\n      delegation.drivers[0]?.employee\r\n        ? delegation.drivers[0].employee\r\n        : null;\r\n\r\n    // Extract vehicle information\r\n    const vehicleInfo =\r\n      delegation.vehicles &&\r\n      delegation.vehicles.length > 0 &&\r\n      delegation.vehicles[0]?.vehicle\r\n        ? delegation.vehicles[0].vehicle\r\n        : null;\r\n\r\n    // Check if flight details exist\r\n    const hasFlightDetails = Boolean(\r\n      delegation.arrivalFlight || delegation.departureFlight\r\n    );\r\n\r\n    // Check if escort assignment is needed\r\n    const needsEscortAssignment =\r\n      !escortInfo &&\r\n      delegation.status !== 'Completed' &&\r\n      delegation.status !== 'Cancelled';\r\n\r\n    // Check if delegation is currently active\r\n    const isActive = delegation.status === 'In_Progress';\r\n\r\n    return {\r\n      escortInfo,\r\n      driverInfo,\r\n      vehicleInfo,\r\n      hasFlightDetails,\r\n      needsEscortAssignment,\r\n      isActive,\r\n    };\r\n  }, [delegation]);\r\n};\r\n\r\nexport default useDelegationInfo;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;;AA2BO,MAAM,oBAAoB,CAAC;IAChC,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACb,6BAA6B;QAC7B,MAAM,aACJ,WAAW,OAAO,IAClB,WAAW,OAAO,CAAC,MAAM,GAAG,KAC5B,WAAW,OAAO,CAAC,EAAE,EAAE,WACnB,WAAW,OAAO,CAAC,EAAE,CAAC,QAAQ,GAC9B;QAEN,6BAA6B;QAC7B,MAAM,aACJ,WAAW,OAAO,IAClB,WAAW,OAAO,CAAC,MAAM,GAAG,KAC5B,WAAW,OAAO,CAAC,EAAE,EAAE,WACnB,WAAW,OAAO,CAAC,EAAE,CAAC,QAAQ,GAC9B;QAEN,8BAA8B;QAC9B,MAAM,cACJ,WAAW,QAAQ,IACnB,WAAW,QAAQ,CAAC,MAAM,GAAG,KAC7B,WAAW,QAAQ,CAAC,EAAE,EAAE,UACpB,WAAW,QAAQ,CAAC,EAAE,CAAC,OAAO,GAC9B;QAEN,gCAAgC;QAChC,MAAM,mBAAmB,QACvB,WAAW,aAAa,IAAI,WAAW,eAAe;QAGxD,uCAAuC;QACvC,MAAM,wBACJ,CAAC,cACD,WAAW,MAAM,KAAK,eACtB,WAAW,MAAM,KAAK;QAExB,0CAA0C;QAC1C,MAAM,WAAW,WAAW,MAAM,KAAK;QAEvC,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;KAAW;AACjB;uCAEe", "debugId": null}}, {"offset": {"line": 7092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/delegations/common/DelegationCard.tsx"], "sourcesContent": ["/**\r\n * @file Modern delegation card component aligned with design system\r\n * @module components/delegations/common/DelegationCard\r\n */\r\n\r\n'use client';\r\n\r\nimport {\r\n  AlertTriangle,\r\n  ArrowRight,\r\n  CalendarDays,\r\n  Car,\r\n  Clock,\r\n  Eye,\r\n  Info,\r\n  MapPin,\r\n  Plane,\r\n  User,\r\n  Users,\r\n} from 'lucide-react';\r\nimport Link from 'next/link';\r\n\r\nimport type { Delegation } from '@/lib/types/domain';\r\n\r\nimport { ActionButton } from '@/components/ui/action-button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { StatusBadge } from '@/components/ui/status-badge';\r\nimport { useDelegationInfo } from '@/hooks/domain/useDelegationInfo';\r\nimport {\r\n  formatDelegationDate,\r\n  getInfoIconColor,\r\n} from '@/lib/utils/delegationUtils';\r\nimport { formatEmployeeName } from '@/lib/utils/formattingUtils';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface DelegationCardProps {\r\n  delegation: Delegation;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * Modern delegation card component aligned with the established design system.\r\n *\r\n * Features:\r\n * - Consistent spacing and typography matching VehicleCard and StatCard\r\n * - Modern hover states and interactions\r\n * - Design system color usage with p-5 padding standard\r\n * - Responsive layout with proper information hierarchy\r\n * - Accessibility compliance\r\n * - Clean, professional appearance\r\n */\r\nexport default function DelegationCard({\r\n  delegation,\r\n  className,\r\n}: DelegationCardProps) {\r\n  // Extract delegation info using custom hook\r\n  const {\r\n    driverInfo,\r\n    escortInfo,\r\n    hasFlightDetails,\r\n    isActive,\r\n    needsEscortAssignment,\r\n    vehicleInfo,\r\n  } = useDelegationInfo(delegation);\r\n\r\n  return (\r\n    <Card\r\n      className={cn(\r\n        'flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md',\r\n        'transition-all duration-200 ease-in-out',\r\n        'hover:shadow-lg hover:border-primary/30',\r\n        'group',\r\n        className\r\n      )}\r\n    >\r\n      {/* Status Indicator Line - Aligned with design system */}\r\n      <div\r\n        className={cn(\r\n          'h-1 w-full transition-all duration-200',\r\n          isActive\r\n            ? 'bg-gradient-to-r from-primary to-accent'\r\n            : delegation.status === 'Completed'\r\n              ? 'bg-green-500'\r\n              : delegation.status === 'Cancelled'\r\n                ? 'bg-destructive'\r\n                : 'bg-muted'\r\n        )}\r\n      />\r\n\r\n      {/* Header - Consistent with VehicleCard p-5 padding */}\r\n      <CardHeader className=\"p-5 pb-3\">\r\n        <div className=\"flex items-start justify-between gap-3\">\r\n          <div className=\"min-w-0 flex-1\">\r\n            <CardTitle className=\"text-xl font-semibold text-primary mb-1 line-clamp-2\">\r\n              {delegation.eventName}\r\n            </CardTitle>\r\n            <CardDescription className=\"flex items-center gap-2 text-sm text-muted-foreground\">\r\n              <MapPin className=\"size-4 text-muted-foreground shrink-0\" />\r\n              <span className=\"truncate\">{delegation.location}</span>\r\n            </CardDescription>\r\n          </div>\r\n\r\n          {/* Status Badge */}\r\n          <StatusBadge\r\n            size=\"sm\"\r\n            status={delegation.status}\r\n            className=\"shrink-0\"\r\n          />\r\n        </div>\r\n\r\n        {/* Active Indicator - Refined styling */}\r\n        {isActive && (\r\n          <div className=\"flex w-fit items-center gap-2 rounded-full bg-primary/10 px-3 py-1.5 border border-primary/20 mt-3\">\r\n            <div className=\"size-2 animate-pulse rounded-full bg-primary\" />\r\n            <span className=\"text-xs font-medium text-primary\">\r\n              Currently Active\r\n            </span>\r\n          </div>\r\n        )}\r\n      </CardHeader>\r\n\r\n      {/* Content - Consistent p-5 padding */}\r\n      <CardContent className=\"flex-1 p-5 pt-0\">\r\n        {/* Duration Section - Simplified and clean */}\r\n        <div className=\"mb-4\">\r\n          <div className=\"flex items-center gap-2 mb-2\">\r\n            <CalendarDays className=\"size-4 text-muted-foreground\" />\r\n            <span className=\"text-sm font-medium text-muted-foreground\">\r\n              Duration\r\n            </span>\r\n          </div>\r\n          <p className=\"text-sm font-medium\">\r\n            {formatDelegationDate(delegation.durationFrom)} -{' '}\r\n            {formatDelegationDate(delegation.durationTo)}\r\n          </p>\r\n        </div>\r\n\r\n        <Separator className=\"my-4\" />\r\n\r\n        {/* Key Information - Clean grid layout */}\r\n        <div className=\"space-y-3\">\r\n          {/* Delegates Count */}\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <Users className=\"size-4 text-muted-foreground\" />\r\n              <span className=\"text-sm font-medium\">Delegates</span>\r\n            </div>\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              {delegation.delegates?.length ?? 0}\r\n            </Badge>\r\n          </div>\r\n\r\n          {/* Flight Details */}\r\n          {hasFlightDetails && (\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <Plane className=\"size-4 text-blue-600\" />\r\n                <span className=\"text-sm font-medium\">Flight Details</span>\r\n              </div>\r\n              <Badge\r\n                variant=\"outline\"\r\n                className=\"text-xs text-blue-600 border-blue-200\"\r\n              >\r\n                Available\r\n              </Badge>\r\n            </div>\r\n          )}\r\n\r\n          {/* Assignments - Clean, minimal display */}\r\n          {(escortInfo || driverInfo || vehicleInfo) && (\r\n            <>\r\n              <Separator className=\"my-3\" />\r\n              <div className=\"space-y-2\">\r\n                <span className=\"text-xs font-medium text-muted-foreground uppercase tracking-wide\">\r\n                  Assignments\r\n                </span>\r\n                <div className=\"space-y-2\">\r\n                  {escortInfo && (\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <User className=\"size-4 text-muted-foreground\" />\r\n                        <span className=\"text-sm\">Escort</span>\r\n                      </div>\r\n                      <span className=\"text-sm font-medium truncate max-w-32\">\r\n                        {formatEmployeeName(escortInfo)}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {driverInfo && (\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <User className=\"size-4 text-muted-foreground\" />\r\n                        <span className=\"text-sm\">Driver</span>\r\n                      </div>\r\n                      <span className=\"text-sm font-medium truncate max-w-32\">\r\n                        {formatEmployeeName(driverInfo)}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n\r\n                  {vehicleInfo && (\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Car className=\"size-4 text-muted-foreground\" />\r\n                        <span className=\"text-sm\">Vehicle</span>\r\n                      </div>\r\n                      <span className=\"text-sm font-medium truncate max-w-32\">\r\n                        {vehicleInfo.make} {vehicleInfo.model}\r\n                      </span>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </>\r\n          )}\r\n\r\n          {/* Warning for unassigned escort - Simplified */}\r\n          {needsEscortAssignment && (\r\n            <div className=\"flex items-center gap-2 p-3 rounded-lg bg-destructive/10 border border-destructive/20 mt-3\">\r\n              <AlertTriangle className=\"size-4 text-destructive shrink-0\" />\r\n              <div>\r\n                <p className=\"text-sm font-medium text-destructive\">\r\n                  Escort Required\r\n                </p>\r\n                <p className=\"text-xs text-destructive/80\">\r\n                  No escort assigned\r\n                </p>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Notes Section - Simplified */}\r\n        {delegation.notes && (\r\n          <div className=\"mt-4 p-3 rounded-lg bg-muted/30\">\r\n            <div className=\"flex items-start gap-2\">\r\n              <Info className=\"size-4 text-muted-foreground mt-0.5 shrink-0\" />\r\n              <div className=\"min-w-0 flex-1\">\r\n                <p className=\"text-xs font-medium text-muted-foreground mb-1\">\r\n                  Notes\r\n                </p>\r\n                <p className=\"text-sm line-clamp-2 text-muted-foreground\">\r\n                  {delegation.notes}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n\r\n      {/* Footer - Consistent with VehicleCard styling */}\r\n      <CardFooter className=\"border-t bg-muted/20 p-4\">\r\n        <ActionButton\r\n          actionType=\"primary\"\r\n          asChild\r\n          className=\"w-full\"\r\n          icon={<Eye className=\"size-4\" />}\r\n        >\r\n          <Link href={`/delegations/${delegation.id}`}>View Details</Link>\r\n        </ActionButton>\r\n      </CardFooter>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAIA;AACA;AAEA;AAQA;AACA;AACA;AACA;AAIA;AACA;AAAA;AAtCA;;;;;;;;;;;;;AAwDe,SAAS,eAAe,EACrC,UAAU,EACV,SAAS,EACW;IACpB,4CAA4C;IAC5C,MAAM,EACJ,UAAU,EACV,UAAU,EACV,gBAAgB,EAChB,QAAQ,EACR,qBAAqB,EACrB,WAAW,EACZ,GAAG,CAAA,GAAA,2IAAA,CAAA,oBAAiB,AAAD,EAAE;IAEtB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2EACA,2CACA,2CACA,SACA;;0BAIF,8OAAC;gBACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,0CACA,WACI,4CACA,WAAW,MAAM,KAAK,cACpB,iBACA,WAAW,MAAM,KAAK,cACpB,mBACA;;;;;;0BAKZ,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,WAAW,SAAS;;;;;;kDAEvB,8OAAC,gIAAA,CAAA,kBAAe;wCAAC,WAAU;;0DACzB,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAY,WAAW,QAAQ;;;;;;;;;;;;;;;;;;0CAKnD,8OAAC,2IAAA,CAAA,cAAW;gCACV,MAAK;gCACL,QAAQ,WAAW,MAAM;gCACzB,WAAU;;;;;;;;;;;;oBAKb,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAmC;;;;;;;;;;;;;;;;;;0BAQzD,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;wCAAK,WAAU;kDAA4C;;;;;;;;;;;;0CAI9D,8OAAC;gCAAE,WAAU;;oCACV,CAAA,GAAA,sIAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,YAAY;oCAAE;oCAAG;oCACjD,CAAA,GAAA,sIAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,UAAU;;;;;;;;;;;;;kCAI/C,8OAAC,qIAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCAGrB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAClC,WAAW,SAAS,EAAE,UAAU;;;;;;;;;;;;4BAKpC,kCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;;kDAExC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,SAAQ;wCACR,WAAU;kDACX;;;;;;;;;;;;4BAOJ,CAAC,cAAc,cAAc,WAAW,mBACvC;;kDACE,8OAAC,qIAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAoE;;;;;;0DAGpF,8OAAC;gDAAI,WAAU;;oDACZ,4BACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;wEAAK,WAAU;kFAAU;;;;;;;;;;;;0EAE5B,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,sIAAA,CAAA,qBAAkB,AAAD,EAAE;;;;;;;;;;;;oDAKzB,4BACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,8OAAC;wEAAK,WAAU;kFAAU;;;;;;;;;;;;0EAE5B,8OAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,sIAAA,CAAA,qBAAkB,AAAD,EAAE;;;;;;;;;;;;oDAKzB,6BACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;kFACf,8OAAC;wEAAK,WAAU;kFAAU;;;;;;;;;;;;0EAE5B,8OAAC;gEAAK,WAAU;;oEACb,YAAY,IAAI;oEAAC;oEAAE,YAAY,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAUlD,uCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAuC;;;;;;0DAGpD,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;;oBASlD,WAAW,KAAK,kBACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAiD;;;;;;sDAG9D,8OAAC;4CAAE,WAAU;sDACV,WAAW,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7B,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC,4IAAA,CAAA,eAAY;oBACX,YAAW;oBACX,OAAO;oBACP,WAAU;oBACV,oBAAM,8OAAC,gMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;8BAErB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE,EAAE;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKvD", "debugId": null}}, {"offset": {"line": 7688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/delegations/list/DelegationViewRenderer.tsx"], "sourcesContent": ["/**\r\n * @file Delegation view renderer component\r\n * @module components/delegations/DelegationViewRenderer\r\n */\r\n\r\n'use client';\r\n\r\nimport React from 'react';\r\n\r\nimport type { ViewMode } from '@/components/dashboard/types';\r\nimport type { Delegation } from '@/lib/types/domain';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nimport DelegationCalendar from '../calendar/DelegationCalendar';\r\nimport DelegationCard from '../common/DelegationCard';\r\nimport DelegationTable from './DelegationTable';\r\n\r\n/**\r\n * Props for DelegationViewRenderer component\r\n */\r\ninterface DelegationViewRendererProps {\r\n  className?: string;\r\n  compactMode: boolean;\r\n  delegations: Delegation[];\r\n  gridColumns: number;\r\n  viewMode: ViewMode;\r\n}\r\n\r\n/**\r\n * Delegation view renderer component that switches between different view modes.\r\n *\r\n * Supported view modes:\r\n * - cards: Grid of delegation cards\r\n * - table: Tabular view with sortable columns\r\n * - list: Vertical list of delegation cards\r\n * - calendar: Calendar view showing delegations by date\r\n *\r\n * @param props - Component props\r\n * @returns JSX element representing the delegations in the selected view mode\r\n */\r\nexport const DelegationViewRenderer: React.FC<DelegationViewRendererProps> = ({\r\n  className = '',\r\n  compactMode,\r\n  delegations,\r\n  gridColumns,\r\n  viewMode,\r\n}) => {\r\n  // Render based on view mode\r\n  switch (viewMode) {\r\n    case 'calendar': {\r\n      return (\r\n        <DelegationCalendar className={className} delegations={delegations} />\r\n      );\r\n    }\r\n\r\n    case 'list': {\r\n      return (\r\n        <div\r\n          className={cn(\r\n            'flex flex-col',\r\n            compactMode ? 'gap-2' : 'gap-4',\r\n            className\r\n          )}\r\n        >\r\n          {delegations.map(delegation => (\r\n            <DelegationCard delegation={delegation} key={delegation.id} />\r\n          ))}\r\n        </div>\r\n      );\r\n    }\r\n\r\n    case 'table': {\r\n      return (\r\n        <DelegationTable className={className} delegations={delegations} />\r\n      );\r\n    }\r\n\r\n    case 'cards':\r\n    case 'grid':\r\n    default: {\r\n      return (\r\n        <div\r\n          className={cn(\r\n            'grid grid-cols-1 gap-6',\r\n            `md:grid-cols-2 lg:grid-cols-${gridColumns}`,\r\n            compactMode && 'gap-3',\r\n            className\r\n          )}\r\n        >\r\n          {delegations.map(delegation => (\r\n            <DelegationCard delegation={delegation} key={delegation.id} />\r\n          ))}\r\n        </div>\r\n      );\r\n    }\r\n  }\r\n};\r\n\r\nexport default DelegationViewRenderer;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AASD;AAAA;AAEA;AACA;AACA;AAXA;;;;;;AAoCO,MAAM,yBAAgE,CAAC,EAC5E,YAAY,EAAE,EACd,WAAW,EACX,WAAW,EACX,WAAW,EACX,QAAQ,EACT;IACC,4BAA4B;IAC5B,OAAQ;QACN,KAAK;YAAY;gBACf,qBACE,8OAAC,+KAAA,CAAA,UAAkB;oBAAC,WAAW;oBAAW,aAAa;;;;;;YAE3D;QAEA,KAAK;YAAQ;gBACX,qBACE,8OAAC;oBACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,iBACA,cAAc,UAAU,SACxB;8BAGD,YAAY,GAAG,CAAC,CAAA,2BACf,8OAAC,yKAAA,CAAA,UAAc;4BAAC,YAAY;2BAAiB,WAAW,EAAE;;;;;;;;;;YAIlE;QAEA,KAAK;YAAS;gBACZ,qBACE,8OAAC,wKAAA,CAAA,UAAe;oBAAC,WAAW;oBAAW,aAAa;;;;;;YAExD;QAEA,KAAK;QACL,KAAK;QACL;YAAS;gBACP,qBACE,8OAAC;oBACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,0BACA,CAAC,4BAA4B,EAAE,aAAa,EAC5C,eAAe,SACf;8BAGD,YAAY,GAAG,CAAC,CAAA,2BACf,8OAAC,yKAAA,CAAA,UAAc;4BAAC,YAAY;2BAAiB,WAAW,EAAE;;;;;;;;;;YAIlE;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 7777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/delegations/list/index.ts"], "sourcesContent": ["/**\r\n * @file List view components barrel export\r\n * @module components/delegations/list\r\n */\r\n\r\nexport { default as DelegationTable } from './DelegationTable';\r\nexport { default as DelegationListContainer } from './DelegationListContainer';\r\nexport { default as DelegationViewRenderer } from './DelegationViewRenderer';\r\n"], "names": [], "mappings": "AAAA;;;CAGC;AAED;AACA;AACA", "debugId": null}}, {"offset": {"line": 7824, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/reports/ViewReportButton.tsx"], "sourcesContent": ["import { ExternalLink, FileText } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport React from 'react';\r\n\r\nimport { ActionButton } from '@/components/ui/action-button';\r\n\r\ninterface ViewReportButtonProps {\r\n  /**\r\n   * Additional CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Handler function that returns the URL to the report page (for list reports with dynamic parameters)\r\n   */\r\n  getReportUrl?: () => string;\r\n\r\n  /**\r\n   * Direct URL to the report page (for individual item reports)\r\n   */\r\n  href?: string;\r\n\r\n  /**\r\n   * Whether this button is for a list report (affects button text)\r\n   */\r\n  isList?: boolean;\r\n}\r\n\r\n/**\r\n * Standardized button for navigating to report pages.\r\n *\r\n * For individual item reports (e.g., Vehicle Report, Delegation Report):\r\n * ```tsx\r\n * <ViewReportButton href={`/vehicles/${vehicle.id}/report`} />\r\n * ```\r\n *\r\n * For list reports with dynamic parameters (e.g., Tasks Report, Delegations List Report):\r\n * ```tsx\r\n * <ViewReportButton\r\n *   isList={true}\r\n *   getReportUrl={() => {\r\n *     const params = new URLSearchParams({ searchTerm, status });\r\n *     return `/tasks/report?${params}`;\r\n *   }}\r\n * />\r\n * ```\r\n */\r\nexport function ViewReportButton({\r\n  className,\r\n  getReportUrl,\r\n  href,\r\n  isList = false,\r\n}: ViewReportButtonProps) {\r\n  if (!href && !getReportUrl) {\r\n    console.error('ViewReportButton requires either href or getReportUrl prop');\r\n    return null;\r\n  }\r\n\r\n  const buttonText = isList ? 'View List Report' : 'View Report';\r\n\r\n  // For direct links (individual item reports)\r\n  if (href) {\r\n    return (\r\n      <ActionButton\r\n        actionType=\"secondary\"\r\n        asChild\r\n        className={className}\r\n        icon={<FileText className=\"size-4\" />}\r\n      >\r\n        <Link href={href} rel=\"noopener noreferrer\" target=\"_blank\">\r\n          {buttonText}\r\n          <ExternalLink\r\n            aria-hidden=\"true\"\r\n            className=\"ml-1.5 inline-block size-3\"\r\n          />\r\n          <span className=\"sr-only\">(opens in new tab)</span>\r\n        </Link>\r\n      </ActionButton>\r\n    );\r\n  }\r\n\r\n  // For dynamic links (list reports)\r\n  const handleClick = () => {\r\n    if (getReportUrl) {\r\n      const reportUrl = getReportUrl();\r\n      window.open(reportUrl, '_blank', 'noopener,noreferrer');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <ActionButton\r\n      actionType=\"secondary\"\r\n      className={className}\r\n      icon={<FileText className=\"size-4\" />}\r\n      onClick={handleClick}\r\n    >\r\n      {buttonText}\r\n      <ExternalLink\r\n        aria-hidden=\"true\"\r\n        className=\"ml-1.5 inline-block size-3\"\r\n      />\r\n    </ActionButton>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AAGA;;;;;AA2CO,SAAS,iBAAiB,EAC/B,SAAS,EACT,YAAY,EACZ,IAAI,EACJ,SAAS,KAAK,EACQ;IACtB,IAAI,CAAC,QAAQ,CAAC,cAAc;QAC1B,QAAQ,KAAK,CAAC;QACd,OAAO;IACT;IAEA,MAAM,aAAa,SAAS,qBAAqB;IAEjD,6CAA6C;IAC7C,IAAI,MAAM;QACR,qBACE,8OAAC,4IAAA,CAAA,eAAY;YACX,YAAW;YACX,OAAO;YACP,WAAW;YACX,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;sBAE1B,cAAA,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAM;gBAAM,KAAI;gBAAsB,QAAO;;oBAChD;kCACD,8OAAC,sNAAA,CAAA,eAAY;wBACX,eAAY;wBACZ,WAAU;;;;;;kCAEZ,8OAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAIlC;IAEA,mCAAmC;IACnC,MAAM,cAAc;QAClB,IAAI,cAAc;YAChB,MAAM,YAAY;YAClB,OAAO,IAAI,CAAC,WAAW,UAAU;QACnC;IACF;IAEA,qBACE,8OAAC,4IAAA,CAAA,eAAY;QACX,YAAW;QACX,WAAW;QACX,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,SAAS;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBACX,eAAY;gBACZ,WAAU;;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 7930, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { ChevronRight, MoreHorizontal } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Breadcrumb = React.forwardRef<\r\n  HTMLElement,\r\n  React.HTMLAttributes<HTMLElement>\r\n>(({ className, ...props }, ref) => (\r\n  <nav\r\n    aria-label=\"breadcrumb\"\r\n    className={cn(\r\n      'flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumb.displayName = 'Breadcrumb';\r\n\r\nconst BreadcrumbList = React.forwardRef<\r\n  HTMLOListElement,\r\n  React.OlHTMLAttributes<HTMLOListElement>\r\n>(({ className, ...props }, ref) => (\r\n  <ol\r\n    className={cn(\r\n      'flex flex-wrap items-center gap-1.5 break-words text-sm',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumbList.displayName = 'BreadcrumbList';\r\n\r\nconst BreadcrumbItem = React.forwardRef<\r\n  HTMLLIElement,\r\n  React.LiHTMLAttributes<HTMLLIElement>\r\n>(({ className, ...props }, ref) => (\r\n  <li\r\n    className={cn('inline-flex items-center gap-1.5', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumbItem.displayName = 'BreadcrumbItem';\r\n\r\nconst BreadcrumbLink = React.forwardRef<\r\n  HTMLAnchorElement,\r\n  React.AnchorHTMLAttributes<HTMLAnchorElement> & {\r\n    asChild?: boolean;\r\n  }\r\n>(({ asChild, className, ...props }, ref) => {\r\n  const Comp = asChild ? Slot : 'a';\r\n\r\n  return (\r\n    <Comp\r\n      className={cn('transition-colors hover:text-foreground', className)}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nBreadcrumbLink.displayName = 'BreadcrumbLink';\r\n\r\nconst BreadcrumbPage = React.forwardRef<\r\n  HTMLSpanElement,\r\n  React.HTMLAttributes<HTMLSpanElement>\r\n>(({ className, ...props }, ref) => (\r\n  <span\r\n    aria-current=\"page\"\r\n    aria-disabled=\"true\"\r\n    className={cn('font-normal text-foreground', className)}\r\n    ref={ref}\r\n    role=\"link\"\r\n    {...props}\r\n  />\r\n));\r\nBreadcrumbPage.displayName = 'BreadcrumbPage';\r\n\r\nconst BreadcrumbSeparator = ({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => (\r\n  <span\r\n    aria-hidden=\"true\"\r\n    className={cn('[&>svg]:size-3.5', className)}\r\n    role=\"presentation\"\r\n    {...props}\r\n  >\r\n    {children ?? <ChevronRight className=\"size-4\" />}\r\n  </span>\r\n);\r\nBreadcrumbSeparator.displayName = 'BreadcrumbSeparator';\r\n\r\nconst BreadcrumbEllipsis = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => (\r\n  <span\r\n    aria-hidden=\"true\"\r\n    className={cn('flex h-9 w-9 items-center justify-center', className)}\r\n    role=\"presentation\"\r\n    {...props}\r\n  >\r\n    <MoreHorizontal className=\"size-4\" />\r\n    <span className=\"sr-only\">More</span>\r\n  </span>\r\n);\r\nBreadcrumbEllipsis.displayName = 'BreadcrumbElipssis';\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbEllipsis,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AAAA;AACA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,cAAW;QACX,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,iFACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QAClD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKpC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACnC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACzD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,gBAAa;QACb,iBAAc;QACd,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC7C,KAAK;QACL,MAAK;QACJ,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,sBAAsB,CAAC,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACmC,iBACtC,8OAAC;QACC,eAAY;QACZ,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QAClC,MAAK;QACJ,GAAG,KAAK;kBAER,0BAAY,8OAAC,sNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;;;;;;AAGzC,oBAAoB,WAAW,GAAG;AAElC,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OACmC,iBACtC,8OAAC;QACC,eAAY;QACZ,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QAC1D,MAAK;QACJ,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 8062, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/app-breadcrumb.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport React from 'react';\r\n\r\nimport {\r\n  Breadcrumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from '@/components/ui/breadcrumb';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface AppBreadcrumbProps {\r\n  className?: string;\r\n  homeHref?: string;\r\n  homeLabel?: string;\r\n  showContainer?: boolean;\r\n}\r\n\r\n/**\r\n * Enhanced AppBreadcrumb Component\r\n *\r\n * Professional breadcrumb navigation with improved styling, accessibility,\r\n * and responsive design. Automatically generates breadcrumbs from the current\r\n * pathname with intelligent segment formatting.\r\n *\r\n * Features:\r\n * - Professional visual design with subtle container styling\r\n * - Responsive layout that adapts to screen size\r\n * - Intelligent path segment formatting (handles IDs, special cases)\r\n * - Enhanced accessibility with proper ARIA labels\r\n * - Consistent integration with design system\r\n * - Smooth hover transitions and visual feedback\r\n */\r\nexport function AppBreadcrumb({\r\n  className,\r\n  homeHref = '/',\r\n  homeLabel = 'Dashboard',\r\n  showContainer = true,\r\n}: AppBreadcrumbProps) {\r\n  const pathname = usePathname();\r\n  const pathSegments = pathname ? pathname.split('/').filter(Boolean) : [];\r\n\r\n  /**\r\n   * Format path segments with intelligent handling of different segment types\r\n   */\r\n  const formatSegment = (segment: string): string => {\r\n    // Handle numeric IDs (don't capitalize, show as \"ID: 123\")\r\n    if (/^\\d+$/.test(segment)) {\r\n      return `ID: ${segment}`;\r\n    }\r\n\r\n    // Handle UUIDs or long alphanumeric strings (show as \"Details\")\r\n    if (segment.length > 10 && /^[a-zA-Z0-9-]+$/.test(segment)) {\r\n      return 'Details';\r\n    }\r\n\r\n    // Handle special cases\r\n    const specialCases: Record<string, string> = {\r\n      add: 'Add New',\r\n      admin: 'Administration',\r\n      edit: 'Edit',\r\n      reports: 'Reports',\r\n      'service-history': 'Service History',\r\n      settings: 'Settings',\r\n    };\r\n\r\n    if (specialCases[segment]) {\r\n      return specialCases[segment];\r\n    }\r\n\r\n    // Default formatting: capitalize and replace dashes with spaces\r\n    return segment\r\n      .split('-')\r\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n      .join(' ');\r\n  };\r\n\r\n  const breadcrumbItems = pathSegments.map((segment, index) => {\r\n    const href = '/' + pathSegments.slice(0, index + 1).join('/');\r\n    const isLast = index === pathSegments.length - 1;\r\n    const displaySegment = formatSegment(segment);\r\n\r\n    return (\r\n      <React.Fragment key={href}>\r\n        <BreadcrumbItem>\r\n          {isLast ? (\r\n            <BreadcrumbPage className=\"font-medium text-foreground\">\r\n              {displaySegment}\r\n            </BreadcrumbPage>\r\n          ) : (\r\n            <BreadcrumbLink asChild>\r\n              <Link\r\n                className=\"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2\"\r\n                href={href}\r\n              >\r\n                {displaySegment}\r\n              </Link>\r\n            </BreadcrumbLink>\r\n          )}\r\n        </BreadcrumbItem>\r\n        {!isLast && <BreadcrumbSeparator />}\r\n      </React.Fragment>\r\n    );\r\n  });\r\n\r\n  const breadcrumbContent = (\r\n    <Breadcrumb className={cn('text-sm', className)}>\r\n      <BreadcrumbList className=\"flex-wrap\">\r\n        <BreadcrumbItem>\r\n          <BreadcrumbLink asChild>\r\n            <Link\r\n              className=\"rounded-sm transition-colors hover:text-primary focus:text-primary focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2\"\r\n              href={homeHref}\r\n            >\r\n              {homeLabel}\r\n            </Link>\r\n          </BreadcrumbLink>\r\n        </BreadcrumbItem>\r\n        {pathSegments.length > 0 && <BreadcrumbSeparator />}\r\n        {breadcrumbItems}\r\n      </BreadcrumbList>\r\n    </Breadcrumb>\r\n  );\r\n\r\n  if (!showContainer) {\r\n    return breadcrumbContent;\r\n  }\r\n\r\n  return (\r\n    <div className=\"mb-6 rounded-lg border border-border/50 bg-muted/30 px-4 py-3 backdrop-blur-sm\">\r\n      <div className=\"flex items-center\">{breadcrumbContent}</div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAQA;AAAA;AAdA;;;;;;;AAsCO,SAAS,cAAc,EAC5B,SAAS,EACT,WAAW,GAAG,EACd,YAAY,WAAW,EACvB,gBAAgB,IAAI,EACD;IACnB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC,WAAW,EAAE;IAExE;;GAEC,GACD,MAAM,gBAAgB,CAAC;QACrB,2DAA2D;QAC3D,IAAI,QAAQ,IAAI,CAAC,UAAU;YACzB,OAAO,CAAC,IAAI,EAAE,SAAS;QACzB;QAEA,gEAAgE;QAChE,IAAI,QAAQ,MAAM,GAAG,MAAM,kBAAkB,IAAI,CAAC,UAAU;YAC1D,OAAO;QACT;QAEA,uBAAuB;QACvB,MAAM,eAAuC;YAC3C,KAAK;YACL,OAAO;YACP,MAAM;YACN,SAAS;YACT,mBAAmB;YACnB,UAAU;QACZ;QAEA,IAAI,YAAY,CAAC,QAAQ,EAAE;YACzB,OAAO,YAAY,CAAC,QAAQ;QAC9B;QAEA,gEAAgE;QAChE,OAAO,QACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;IACV;IAEA,MAAM,kBAAkB,aAAa,GAAG,CAAC,CAAC,SAAS;QACjD,MAAM,OAAO,MAAM,aAAa,KAAK,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC;QACzD,MAAM,SAAS,UAAU,aAAa,MAAM,GAAG;QAC/C,MAAM,iBAAiB,cAAc;QAErC,qBACE,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;8BACb,8OAAC,sIAAA,CAAA,iBAAc;8BACZ,uBACC,8OAAC,sIAAA,CAAA,iBAAc;wBAAC,WAAU;kCACvB;;;;;6CAGH,8OAAC,sIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,WAAU;4BACV,MAAM;sCAEL;;;;;;;;;;;;;;;;gBAKR,CAAC,wBAAU,8OAAC,sIAAA,CAAA,sBAAmB;;;;;;WAjBb;;;;;IAoBzB;IAEA,MAAM,kCACJ,8OAAC,sIAAA,CAAA,aAAU;QAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW;kBACnC,cAAA,8OAAC,sIAAA,CAAA,iBAAc;YAAC,WAAU;;8BACxB,8OAAC,sIAAA,CAAA,iBAAc;8BACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,WAAU;4BACV,MAAM;sCAEL;;;;;;;;;;;;;;;;gBAIN,aAAa,MAAM,GAAG,mBAAK,8OAAC,sIAAA,CAAA,sBAAmB;;;;;gBAC/C;;;;;;;;;;;;IAKP,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBAAqB;;;;;;;;;;;AAG1C", "debugId": null}}, {"offset": {"line": 8224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn('animate-pulse rounded-md bg-muted', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 8249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/loading.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON>cle, Loader2, LucideIcon } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport { ActionButton } from '@/components/ui/action-button';\r\nimport { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { cn } from '@/lib/utils';\r\n\r\n// Size variants for the spinner\r\nexport type SpinnerSize = 'lg' | 'md' | 'sm' | 'xl';\r\n\r\n// Size mappings for the spinner\r\nconst spinnerSizeClasses: Record<SpinnerSize, string> = {\r\n  lg: 'h-8 w-8',\r\n  md: 'h-6 w-6',\r\n  sm: 'h-4 w-4',\r\n  xl: 'h-12 w-12',\r\n};\r\n\r\n// Text size mappings for the spinner\r\nconst spinnerTextSizeClasses: Record<SpinnerSize, string> = {\r\n  lg: 'text-base',\r\n  md: 'text-sm',\r\n  sm: 'text-xs',\r\n  xl: 'text-lg',\r\n};\r\n\r\nexport interface DataLoaderProps<T> {\r\n  /**\r\n   * Render function for the data\r\n   */\r\n  children: (data: T) => React.ReactNode;\r\n\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * The data to render\r\n   */\r\n  data: null | T | undefined;\r\n\r\n  /**\r\n   * Custom empty state component\r\n   */\r\n  emptyComponent?: React.ReactNode;\r\n\r\n  /**\r\n   * Error message, if any\r\n   */\r\n  error?: null | string;\r\n\r\n  /**\r\n   * Custom error component\r\n   */\r\n  errorComponent?: React.ReactNode;\r\n\r\n  /**\r\n   * Whether data is currently loading\r\n   */\r\n  isLoading: boolean;\r\n\r\n  /**\r\n   * Custom loading component\r\n   */\r\n  loadingComponent?: React.ReactNode;\r\n\r\n  /**\r\n   * Function to retry loading data\r\n   */\r\n  onRetry?: () => void;\r\n}\r\n\r\nexport interface EmptyStateProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Description text to display\r\n   */\r\n  description?: string;\r\n\r\n  /**\r\n   * Icon to display (Lucide icon component)\r\n   */\r\n  icon?: LucideIcon;\r\n\r\n  /**\r\n   * Primary action button\r\n   */\r\n  primaryAction?: {\r\n    label: string;\r\n    onClick?: () => void;\r\n    href?: string;\r\n    icon?: React.ReactNode;\r\n  };\r\n\r\n  /**\r\n   * Secondary action button\r\n   */\r\n  secondaryAction?: {\r\n    label: string;\r\n    onClick?: () => void;\r\n    href?: string;\r\n    icon?: React.ReactNode;\r\n  };\r\n\r\n  /**\r\n   * Title text to display\r\n   */\r\n  title: string;\r\n}\r\n\r\nexport interface ErrorDisplayProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Error message to display\r\n   */\r\n  message: string;\r\n\r\n  /**\r\n   * Function to retry the operation\r\n   */\r\n  onRetry?: () => void;\r\n}\r\n\r\nexport interface LoadingSpinnerProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Whether to display as a full-page overlay\r\n   */\r\n  fullPage?: boolean;\r\n\r\n  /**\r\n   * Size of the spinner\r\n   */\r\n  size?: SpinnerSize;\r\n\r\n  /**\r\n   * Text to display below the spinner\r\n   */\r\n  text?: string;\r\n}\r\n\r\nexport interface SkeletonLoaderProps {\r\n  /**\r\n   * Custom CSS class names\r\n   */\r\n  className?: string;\r\n\r\n  /**\r\n   * Number of skeleton items to display\r\n   */\r\n  count?: number;\r\n\r\n  /**\r\n   * Test ID for testing\r\n   */\r\n  testId?: string;\r\n\r\n  /**\r\n   * Type of content to show a skeleton for\r\n   */\r\n  variant?: SkeletonVariant;\r\n}\r\n\r\n// Skeleton variants\r\nexport type SkeletonVariant = 'card' | 'default' | 'list' | 'stats' | 'table';\r\n\r\n/**\r\n * DataLoader component for handling loading, error, and empty states\r\n *\r\n * @example\r\n * <DataLoader\r\n *   isLoading={isLoading}\r\n *   error={error}\r\n *   data={vehicles}\r\n *   onRetry={refetch}\r\n *   loadingComponent={<SkeletonLoader variant=\"card\" count={3} />}\r\n * >\r\n *   {(vehicles) => (\r\n *     <div className=\"grid grid-cols-3 gap-4\">\r\n *       {vehicles.map(vehicle => (\r\n *         <VehicleCard key={vehicle.id} vehicle={vehicle} />\r\n *       ))}\r\n *     </div>\r\n *   )}\r\n * </DataLoader>\r\n */\r\nexport function DataLoader<T>({\r\n  children,\r\n  className,\r\n  data,\r\n  emptyComponent,\r\n  error,\r\n  errorComponent,\r\n  isLoading,\r\n  loadingComponent,\r\n  onRetry,\r\n}: DataLoaderProps<T>) {\r\n  if (isLoading) {\r\n    return (\r\n      loadingComponent || (\r\n        <LoadingSpinner {...(className && { className })} text=\"Loading...\" />\r\n      )\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      errorComponent || (\r\n        <ErrorDisplay\r\n          {...(className && { className })}\r\n          message={error}\r\n          {...(onRetry && { onRetry })}\r\n        />\r\n      )\r\n    );\r\n  }\r\n\r\n  if (!data || (Array.isArray(data) && data.length === 0)) {\r\n    return (\r\n      emptyComponent || (\r\n        <div className={cn('text-center py-8', className)}>\r\n          <p className=\"text-muted-foreground\">No data available</p>\r\n        </div>\r\n      )\r\n    );\r\n  }\r\n\r\n  return <div className={className}>{children(data)}</div>;\r\n}\r\n\r\n/**\r\n * Unified empty state component following the design pattern from delegations page\r\n *\r\n * @example\r\n * <EmptyState\r\n *   title=\"No Service Records Found\"\r\n *   description=\"There are no service records matching your current filters.\"\r\n *   icon={History}\r\n *   primaryAction={{\r\n *     label: \"Log New Service\",\r\n *     href: \"/vehicles\",\r\n *     icon: <PlusCircle className=\"size-4\" />\r\n *   }}\r\n *   secondaryAction={{\r\n *     label: \"Clear Filters\",\r\n *     onClick: clearFilters\r\n *   }}\r\n * />\r\n */\r\nexport function EmptyState({\r\n  className,\r\n  description,\r\n  icon: Icon,\r\n  primaryAction,\r\n  secondaryAction,\r\n  title,\r\n}: EmptyStateProps) {\r\n  return (\r\n    <div className={cn('space-y-6 text-center py-12', className)}>\r\n      {Icon && (\r\n        <div className=\"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted\">\r\n          <Icon className=\"h-10 w-10 text-muted-foreground\" />\r\n        </div>\r\n      )}\r\n\r\n      <div className=\"space-y-2\">\r\n        <h3 className=\"text-2xl font-semibold text-foreground\">{title}</h3>\r\n        {description && (\r\n          <p className=\"text-muted-foreground max-w-md mx-auto\">\r\n            {description}\r\n          </p>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\r\n        {primaryAction && (\r\n          <ActionButton\r\n            actionType=\"primary\"\r\n            asChild={!!primaryAction.href}\r\n            icon={primaryAction.icon}\r\n            onClick={primaryAction.onClick}\r\n          >\r\n            {primaryAction.href ? (\r\n              <a href={primaryAction.href}>{primaryAction.label}</a>\r\n            ) : (\r\n              primaryAction.label\r\n            )}\r\n          </ActionButton>\r\n        )}\r\n\r\n        {secondaryAction && (\r\n          <ActionButton\r\n            actionType=\"tertiary\"\r\n            asChild={!!secondaryAction.href}\r\n            icon={secondaryAction.icon}\r\n            onClick={secondaryAction.onClick}\r\n          >\r\n            {secondaryAction.href ? (\r\n              <a href={secondaryAction.href}>{secondaryAction.label}</a>\r\n            ) : (\r\n              secondaryAction.label\r\n            )}\r\n          </ActionButton>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * Unified error display component\r\n *\r\n * @example\r\n * <ErrorDisplay message=\"Failed to load data\" onRetry={refetch} />\r\n */\r\nexport function ErrorDisplay({\r\n  className,\r\n  message,\r\n  onRetry,\r\n}: ErrorDisplayProps) {\r\n  return (\r\n    <Alert className={cn('my-4', className)} variant=\"destructive\">\r\n      <AlertCircle className=\"size-4\" />\r\n      <AlertTitle>Error</AlertTitle>\r\n      <AlertDescription>\r\n        <div className=\"mt-2\">\r\n          <p className=\"mb-4 text-sm text-muted-foreground\">{message}</p>\r\n          {onRetry && (\r\n            <ActionButton\r\n              actionType=\"tertiary\"\r\n              icon={<Loader2 className=\"size-4\" />}\r\n              onClick={onRetry}\r\n              size=\"sm\"\r\n            >\r\n              Try Again\r\n            </ActionButton>\r\n          )}\r\n        </div>\r\n      </AlertDescription>\r\n    </Alert>\r\n  );\r\n}\r\n\r\n/**\r\n * Unified loading spinner component\r\n *\r\n * @example\r\n * <LoadingSpinner size=\"md\" text=\"Loading data...\" />\r\n */\r\nexport function LoadingSpinner({\r\n  className,\r\n  fullPage = false,\r\n  size = 'md',\r\n  text,\r\n}: LoadingSpinnerProps) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'flex items-center justify-center',\r\n        fullPage && 'fixed inset-0 bg-background/80 backdrop-blur-sm z-50',\r\n        className\r\n      )}\r\n    >\r\n      <div className=\"flex flex-col items-center\">\r\n        <Loader2\r\n          className={cn('animate-spin text-primary', spinnerSizeClasses[size])}\r\n        />\r\n        {text && (\r\n          <span\r\n            className={cn(\r\n              'mt-2 text-muted-foreground',\r\n              spinnerTextSizeClasses[size]\r\n            )}\r\n          >\r\n            {text}\r\n          </span>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * Unified skeleton loader component\r\n *\r\n * @example\r\n * <SkeletonLoader variant=\"card\" count={3} />\r\n */\r\nexport function SkeletonLoader({\r\n  className,\r\n  count = 1,\r\n  testId = 'loading-skeleton',\r\n  variant = 'default',\r\n}: SkeletonLoaderProps) {\r\n  // Render card skeleton (for entity cards like vehicles, employees)\r\n  if (variant === 'card') {\r\n    return (\r\n      <div\r\n        className={cn(\r\n          'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',\r\n          className\r\n        )}\r\n        data-testid={testId}\r\n      >\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div\r\n            className=\"overflow-hidden rounded-lg border bg-card shadow-md\"\r\n            key={i}\r\n          >\r\n            <Skeleton className=\"aspect-[16/10] w-full\" />\r\n            <div className=\"p-5\">\r\n              <Skeleton className=\"mb-1 h-7 w-3/4\" />\r\n              <Skeleton className=\"mb-3 h-4 w-1/2\" />\r\n              <Skeleton className=\"my-3 h-px w-full\" />\r\n              <div className=\"space-y-2.5\">\r\n                {Array.from({ length: 3 })\r\n                  .fill(0)\r\n                  .map((_, j) => (\r\n                    <div className=\"flex items-center\" key={j}>\r\n                      <Skeleton className=\"mr-2.5 size-5 rounded-full\" />\r\n                      <Skeleton className=\"h-5 w-2/3\" />\r\n                    </div>\r\n                  ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render table skeleton\r\n  if (variant === 'table') {\r\n    return (\r\n      <div className={cn('space-y-3', className)} data-testid={testId}>\r\n        <div className=\"flex gap-4\">\r\n          {Array.from({ length: 3 })\r\n            .fill(0)\r\n            .map((_, i) => (\r\n              <Skeleton className=\"h-8 flex-1\" key={i} />\r\n            ))}\r\n        </div>\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div className=\"flex gap-4\" key={i}>\r\n            {Array.from({ length: 3 })\r\n              .fill(0)\r\n              .map((_, j) => (\r\n                <Skeleton className=\"h-6 flex-1\" key={j} />\r\n              ))}\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render list skeleton\r\n  if (variant === 'list') {\r\n    return (\r\n      <div className={cn('space-y-3', className)} data-testid={testId}>\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div className=\"flex items-center gap-4\" key={i}>\r\n            <Skeleton className=\"size-12 rounded-full\" />\r\n            <div className=\"flex-1 space-y-2\">\r\n              <Skeleton className=\"h-4 w-1/3\" />\r\n              <Skeleton className=\"h-4 w-full\" />\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render stats skeleton\r\n  if (variant === 'stats') {\r\n    return (\r\n      <div\r\n        className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}\r\n        data-testid={testId}\r\n      >\r\n        {new Array(count).fill(0).map((_, i) => (\r\n          <div className=\"rounded-lg border bg-card p-5 shadow-sm\" key={i}>\r\n            <div className=\"flex justify-between\">\r\n              <Skeleton className=\"h-5 w-1/3\" />\r\n              <Skeleton className=\"size-5 rounded-full\" />\r\n            </div>\r\n            <Skeleton className=\"mt-3 h-8 w-1/2\" />\r\n            <Skeleton className=\"mt-2 h-4 w-2/3\" />\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Default skeleton\r\n  return (\r\n    <div className={cn('space-y-2', className)} data-testid={testId}>\r\n      {new Array(count).fill(0).map((_, i) => (\r\n        <Skeleton className=\"h-5 w-full\" key={i} />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AARA;;;;;;;AAaA,gCAAgC;AAChC,MAAM,qBAAkD;IACtD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEA,qCAAqC;AACrC,MAAM,yBAAsD;IAC1D,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AA+KO,SAAS,WAAc,EAC5B,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,cAAc,EACd,KAAK,EACL,cAAc,EACd,SAAS,EACT,gBAAgB,EAChB,OAAO,EACY;IACnB,IAAI,WAAW;QACb,OACE,kCACE,8OAAC;YAAgB,GAAI,aAAa;gBAAE;YAAU,CAAC;YAAG,MAAK;;;;;;IAG7D;IAEA,IAAI,OAAO;QACT,OACE,gCACE,8OAAC;YACE,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC/B,SAAS;YACR,GAAI,WAAW;gBAAE;YAAQ,CAAC;;;;;;IAInC;IAEA,IAAI,CAAC,QAAS,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,KAAK,GAAI;QACvD,OACE,gCACE,8OAAC;YAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;sBACrC,cAAA,8OAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;IAI7C;IAEA,qBAAO,8OAAC;QAAI,WAAW;kBAAY,SAAS;;;;;;AAC9C;AAqBO,SAAS,WAAW,EACzB,SAAS,EACT,WAAW,EACX,MAAM,IAAI,EACV,aAAa,EACb,eAAe,EACf,KAAK,EACW;IAChB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;;YAC/C,sBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;;;;;;;;;;;0BAIpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;oBACvD,6BACC,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAKP,8OAAC;gBAAI,WAAU;;oBACZ,+BACC,8OAAC,4IAAA,CAAA,eAAY;wBACX,YAAW;wBACX,SAAS,CAAC,CAAC,cAAc,IAAI;wBAC7B,MAAM,cAAc,IAAI;wBACxB,SAAS,cAAc,OAAO;kCAE7B,cAAc,IAAI,iBACjB,8OAAC;4BAAE,MAAM,cAAc,IAAI;sCAAG,cAAc,KAAK;;;;;mCAEjD,cAAc,KAAK;;;;;;oBAKxB,iCACC,8OAAC,4IAAA,CAAA,eAAY;wBACX,YAAW;wBACX,SAAS,CAAC,CAAC,gBAAgB,IAAI;wBAC/B,MAAM,gBAAgB,IAAI;wBAC1B,SAAS,gBAAgB,OAAO;kCAE/B,gBAAgB,IAAI,iBACnB,8OAAC;4BAAE,MAAM,gBAAgB,IAAI;sCAAG,gBAAgB,KAAK;;;;;mCAErD,gBAAgB,KAAK;;;;;;;;;;;;;;;;;;AAOnC;AAQO,SAAS,aAAa,EAC3B,SAAS,EACT,OAAO,EACP,OAAO,EACW;IAClB,qBACE,8OAAC,iIAAA,CAAA,QAAK;QAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAY,SAAQ;;0BAC/C,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;0BACvB,8OAAC,iIAAA,CAAA,aAAU;0BAAC;;;;;;0BACZ,8OAAC,iIAAA,CAAA,mBAAgB;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;wBAClD,yBACC,8OAAC,4IAAA,CAAA,eAAY;4BACX,YAAW;4BACX,oBAAM,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BACzB,SAAS;4BACT,MAAK;sCACN;;;;;;;;;;;;;;;;;;;;;;;AAQb;AAQO,SAAS,eAAe,EAC7B,SAAS,EACT,WAAW,KAAK,EAChB,OAAO,IAAI,EACX,IAAI,EACgB;IACpB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,oCACA,YAAY,wDACZ;kBAGF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iNAAA,CAAA,UAAO;oBACN,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B,kBAAkB,CAAC,KAAK;;;;;;gBAEpE,sBACC,8OAAC;oBACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,8BACA,sBAAsB,CAAC,KAAK;8BAG7B;;;;;;;;;;;;;;;;;AAMb;AAQO,SAAS,eAAe,EAC7B,SAAS,EACT,QAAQ,CAAC,EACT,SAAS,kBAAkB,EAC3B,UAAU,SAAS,EACC;IACpB,mEAAmE;IACnE,IAAI,YAAY,QAAQ;QACtB,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,wDACA;YAEF,eAAa;sBAEZ,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC;oBACC,WAAU;;sCAGV,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAE,GACrB,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;2CAFkB;;;;;;;;;;;;;;;;;mBAX3C;;;;;;;;;;IAsBf;IAEA,wBAAwB;IACxB,IAAI,YAAY,SAAS;QACvB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAY,eAAa;;8BACvD,8OAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GACrB,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;2BAAkB;;;;;;;;;;gBAG3C,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GACrB,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;+BAAkB;;;;;uBAJX;;;;;;;;;;;IAUzC;IAEA,uBAAuB;IACvB,IAAI,YAAY,QAAQ;QACtB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAY,eAAa;sBACtD,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;mBAJsB;;;;;;;;;;IAUtD;IAEA,wBAAwB;IACxB,IAAI,YAAY,SAAS;QACvB,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;YAC1D,eAAa;sBAEZ,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;mBANwC;;;;;;;;;;IAWtE;IAEA,mBAAmB;IACnB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAAY,eAAa;kBACtD,IAAI,MAAM,OAAO,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,kBAChC,8OAAC,oIAAA,CAAA,WAAQ;gBAAC,WAAU;eAAkB;;;;;;;;;;AAI9C", "debugId": null}}, {"offset": {"line": 8798, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/PageHeader.tsx"], "sourcesContent": ["import type { LucideIcon } from 'lucide-react';\r\n\r\nimport React from 'react';\r\n\r\ninterface PageHeaderProps {\r\n  children?: React.ReactNode; // For action buttons like \"Add New\"\r\n  description?: string;\r\n  icon?: LucideIcon;\r\n  title: string;\r\n}\r\n\r\nexport function PageHeader({\r\n  children,\r\n  description,\r\n  icon: Icon,\r\n  title,\r\n}: PageHeaderProps) {\r\n  return (\r\n    <div className=\"mb-6 flex items-center justify-between border-b border-border/50 pb-4\">\r\n      <div>\r\n        <div className=\"flex items-center gap-3\">\r\n          {Icon && <Icon className=\"size-8 text-primary\" />}\r\n          <h1 className=\"text-3xl font-bold tracking-tight text-foreground\">\r\n            {title}\r\n          </h1>\r\n        </div>\r\n        {description && (\r\n          <p className=\"mt-1 text-muted-foreground\">{description}</p>\r\n        )}\r\n      </div>\r\n      {children && <div className=\"flex items-center gap-2\">{children}</div>}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAWO,SAAS,WAAW,EACzB,QAAQ,EACR,WAAW,EACX,MAAM,IAAI,EACV,KAAK,EACW;IAChB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;;4BACZ,sBAAQ,8OAAC;gCAAK,WAAU;;;;;;0CACzB,8OAAC;gCAAG,WAAU;0CACX;;;;;;;;;;;;oBAGJ,6BACC,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;YAG9C,0BAAY,8OAAC;gBAAI,WAAU;0BAA2B;;;;;;;;;;;;AAG7D", "debugId": null}}, {"offset": {"line": 8868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/hooks/ui/useNotifications.ts"], "sourcesContent": ["/**\r\n * @file Custom hook for notification management using Zustand AppStore\r\n * @module hooks/useNotifications\r\n */\r\n\r\nimport { useCallback } from 'react';\r\n\r\nimport { useAppStore } from '@/lib/stores/zustand/appStore';\r\nimport { undefinedToNull } from '../../lib/utils/typeHelpers';\r\n\r\n/**\r\n * Custom hook for simplified notification management\r\n * Provides convenient methods for showing different types of notifications\r\n */\r\nexport const useNotifications = () => {\r\n  const addNotification = useAppStore(state => state.addNotification);\r\n  const removeNotification = useAppStore(state => state.removeNotification);\r\n  const clearAllNotifications = useAppStore(\r\n    state => state.clearAllNotifications\r\n  );\r\n  const unreadCount = useAppStore(state => state.unreadNotificationCount);\r\n\r\n  /**\r\n   * Show a success notification\r\n   */\r\n  const showSuccess = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'success',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show an error notification\r\n   */\r\n  const showError = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'error',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a warning notification\r\n   */\r\n  const showWarning = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'warning',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show an info notification\r\n   */\r\n  const showInfo = useCallback(\r\n    (message: string) => {\r\n      addNotification({\r\n        message,\r\n        type: 'info',\r\n      });\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a notification for API operation results\r\n   */\r\n  const showApiResult = useCallback(\r\n    (success: boolean, successMessage: string, errorMessage: string) => {\r\n      if (success) {\r\n        showSuccess(successMessage);\r\n      } else {\r\n        showError(errorMessage);\r\n      }\r\n    },\r\n    [showSuccess, showError]\r\n  );\r\n\r\n  /**\r\n   * Show a notification with auto-dismiss after specified time\r\n   */\r\n  const showTemporary = useCallback(\r\n    (\r\n      type: 'error' | 'info' | 'success' | 'warning',\r\n      message: string,\r\n      dismissAfter = 5000\r\n    ) => {\r\n      addNotification({ message, type });\r\n\r\n      // Auto-dismiss after specified time\r\n      setTimeout(() => {\r\n        // Note: This is a simplified approach. In a real implementation,\r\n        // you might want to store the notification ID and remove specifically that one\r\n        const notifications = useAppStore.getState().notifications;\r\n        const latestNotification = notifications.at(-1);\r\n        if (latestNotification && latestNotification.message === message) {\r\n          removeNotification(latestNotification.id);\r\n        }\r\n      }, dismissAfter);\r\n    },\r\n    [addNotification, removeNotification]\r\n  );\r\n\r\n  /**\r\n   * Show a loading notification that can be updated\r\n   */\r\n  const showLoading = useCallback(\r\n    (message = 'Loading...') => {\r\n      addNotification({\r\n        message,\r\n        type: 'info',\r\n      });\r\n\r\n      // Return the notification ID for potential updates\r\n      const notifications = useAppStore.getState().notifications;\r\n      return notifications.at(-1)?.id;\r\n    },\r\n    [addNotification]\r\n  );\r\n\r\n  /**\r\n   * Update a loading notification to success or error\r\n   */\r\n  const updateLoadingNotification = useCallback(\r\n    (notificationId: string, success: boolean, message: string) => {\r\n      removeNotification(notificationId);\r\n      if (success) {\r\n        showSuccess(message);\r\n      } else {\r\n        showError(message);\r\n      }\r\n    },\r\n    [removeNotification, showSuccess, showError]\r\n  );\r\n\r\n  return {\r\n    clearAllNotifications,\r\n    // Store methods\r\n    removeNotification,\r\n    // Advanced methods\r\n    showApiResult,\r\n    showError,\r\n\r\n    showInfo,\r\n    showLoading,\r\n    // Basic notification methods\r\n    showSuccess,\r\n    showTemporary,\r\n\r\n    showWarning,\r\n    unreadCount,\r\n    updateLoadingNotification,\r\n  };\r\n};\r\n\r\n/**\r\n * Enhanced notification hook with WorkHub-specific notification types\r\n */\r\nexport const useWorkHubNotifications = () => {\r\n  const {\r\n    clearAllNotifications,\r\n    removeNotification,\r\n    showError,\r\n    showInfo,\r\n    showSuccess,\r\n    showWarning,\r\n    unreadCount,\r\n  } = useNotifications();\r\n\r\n  /**\r\n   * Show delegation-related notifications\r\n   */\r\n  const showDelegationUpdate = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'delegation',\r\n        message,\r\n        type: 'delegation-update',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show vehicle maintenance notifications\r\n   */\r\n  const showVehicleMaintenance = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'vehicle',\r\n        message,\r\n        type: 'vehicle-maintenance',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show task assignment notifications\r\n   */\r\n  const showTaskAssigned = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'task',\r\n        message,\r\n        type: 'task-assigned',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  /**\r\n   * Show employee update notifications\r\n   */\r\n  const showEmployeeUpdate = useCallback(\r\n    (message: string, actionUrl?: string) => {\r\n      const addNotification = useAppStore.getState().addNotification;\r\n      addNotification({\r\n        ...(actionUrl && { actionUrl }),\r\n        category: 'employee',\r\n        message,\r\n        type: 'employee-update',\r\n      });\r\n    },\r\n    []\r\n  );\r\n\r\n  return {\r\n    clearAllNotifications,\r\n    // Management\r\n    removeNotification,\r\n    // WorkHub-specific notifications\r\n    showDelegationUpdate,\r\n    showEmployeeUpdate,\r\n\r\n    showError,\r\n    showInfo,\r\n    // Basic notifications\r\n    showSuccess,\r\n    showTaskAssigned,\r\n\r\n    showVehicleMaintenance,\r\n    showWarning,\r\n    unreadCount,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AAEA;;;AAOO,MAAM,mBAAmB;IAC9B,MAAM,kBAAkB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,eAAe;IAClE,MAAM,qBAAqB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,kBAAkB;IACxE,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EACtC,CAAA,QAAS,MAAM,qBAAqB;IAEtC,MAAM,cAAc,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE,CAAA,QAAS,MAAM,uBAAuB;IAEtE;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC1B,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACzB,CAAC;QACC,gBAAgB;YACd;YACA,MAAM;QACR;IACF,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,SAAkB,gBAAwB;QACzC,IAAI,SAAS;YACX,YAAY;QACd,OAAO;YACL,UAAU;QACZ;IACF,GACA;QAAC;QAAa;KAAU;IAG1B;;GAEC,GACD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CACE,MACA,SACA,eAAe,IAAI;QAEnB,gBAAgB;YAAE;YAAS;QAAK;QAEhC,oCAAoC;QACpC,WAAW;YACT,iEAAiE;YACjE,+EAA+E;YAC/E,MAAM,gBAAgB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;YAC1D,MAAM,qBAAqB,cAAc,EAAE,CAAC,CAAC;YAC7C,IAAI,sBAAsB,mBAAmB,OAAO,KAAK,SAAS;gBAChE,mBAAmB,mBAAmB,EAAE;YAC1C;QACF,GAAG;IACL,GACA;QAAC;QAAiB;KAAmB;IAGvC;;GAEC,GACD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC,UAAU,YAAY;QACrB,gBAAgB;YACd;YACA,MAAM;QACR;QAEA,mDAAmD;QACnD,MAAM,gBAAgB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,aAAa;QAC1D,OAAO,cAAc,EAAE,CAAC,CAAC,IAAI;IAC/B,GACA;QAAC;KAAgB;IAGnB;;GAEC,GACD,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC1C,CAAC,gBAAwB,SAAkB;QACzC,mBAAmB;QACnB,IAAI,SAAS;YACX,YAAY;QACd,OAAO;YACL,UAAU;QACZ;IACF,GACA;QAAC;QAAoB;QAAa;KAAU;IAG9C,OAAO;QACL;QACA,gBAAgB;QAChB;QACA,mBAAmB;QACnB;QACA;QAEA;QACA;QACA,6BAA6B;QAC7B;QACA;QAEA;QACA;QACA;IACF;AACF;AAKO,MAAM,0BAA0B;IACrC,MAAM,EACJ,qBAAqB,EACrB,kBAAkB,EAClB,SAAS,EACT,QAAQ,EACR,WAAW,EACX,WAAW,EACX,WAAW,EACZ,GAAG;IAEJ;;GAEC,GACD,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACrC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACvC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ;;GAEC,GACD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,CAAC,SAAiB;QAChB,MAAM,kBAAkB,2IAAA,CAAA,cAAW,CAAC,QAAQ,GAAG,eAAe;QAC9D,gBAAgB;YACd,GAAI,aAAa;gBAAE;YAAU,CAAC;YAC9B,UAAU;YACV;YACA,MAAM;QACR;IACF,GACA,EAAE;IAGJ,OAAO;QACL;QACA,aAAa;QACb;QACA,iCAAiC;QACjC;QACA;QAEA;QACA;QACA,sBAAsB;QACtB;QACA;QAEA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 9078, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/useEmployees.ts"], "sourcesContent": ["/**\r\n * @file TanStack Query hooks for Employee-related data.\r\n * These hooks manage fetching, caching, and mutating employee data,\r\n * integrating with the EmployeeApiService and EmployeeTransformer.\r\n * @module stores/queries/useEmployees\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\r\n\r\nimport type {\r\n  CreateEmployeeData,\r\n  DriverAvailabilityPrisma,\r\n  Employee,\r\n} from '@/lib/types/domain';\r\n\r\nimport { useCrudQuery } from '@/hooks/api/useSmartQuery'; // Adjusted import path\r\nimport { useNotifications } from '@/hooks/ui/useNotifications';\r\nimport { EmployeeTransformer } from '@/lib/transformers/employeeTransformer';\r\nimport { undefinedToNull } from '@/lib/utils/typeHelpers';\r\n\r\nimport { employeeApiService } from '../../api/services/apiServiceFactory'; // Use centralized service\r\n\r\n/**\r\n * Centralized query keys for employees to ensure consistency.\r\n */\r\nexport const employeeQueryKeys = {\r\n  all: ['employees'] as const,\r\n  detail: (id: string) => ['employees', id] as const,\r\n  // Add other specific query keys as needed\r\n};\r\n\r\n/**\r\n * Custom hook to fetch all employees.\r\n * @param options - Optional React Query options\r\n * @returns Query result containing an array of Employee domain models.\r\n */\r\nexport const useEmployees = (\r\n  options?: Omit<UseQueryOptions<Employee[], Error>, 'queryFn' | 'queryKey'>\r\n) => {\r\n  return useCrudQuery<Employee[], Error>(\r\n    [...employeeQueryKeys.all], // queryKey - spread for mutability\r\n    async () => {\r\n      const response = await employeeApiService.getAll();\r\n      return response.data;\r\n    },\r\n    'employee', // entityType\r\n    {\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n      ...options,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Custom hook to fetch employees filtered by role.\r\n * @param role - Optional role filter (e.g., 'driver', 'manager')\r\n * @param options - Optional React Query options\r\n * @returns Query result containing an array of Employee domain models filtered by role.\r\n */\r\nexport const useEmployeesByRole = (\r\n  role?: string,\r\n  options?: Omit<UseQueryOptions<Employee[], Error>, 'queryFn' | 'queryKey'>\r\n) => {\r\n  return useCrudQuery<Employee[], Error>(\r\n    role ? ['employees', 'role', role] : [...employeeQueryKeys.all],\r\n    async () => {\r\n      if (role) {\r\n        const response = await employeeApiService.getByRole(role);\r\n        return response;\r\n      } else {\r\n        const response = await employeeApiService.getAll();\r\n        return response.data;\r\n      }\r\n    },\r\n    'employee', // entityType for WebSocket events\r\n    {\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n      ...options,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Custom hook to fetch a single employee by their ID.\r\n * @param id - The ID of the employee to fetch.\r\n * @returns Query result containing a single Employee domain model or undefined.\r\n */\r\nexport const useEmployee = (id: string) => {\r\n  return useCrudQuery<Employee, Error>(\r\n    [...employeeQueryKeys.detail(id)],\r\n    async () => {\r\n      return await employeeApiService.getById(id);\r\n    },\r\n    'employee', // entityType for WebSocket events\r\n    {\r\n      enabled: !!id, // Only run query if id is truthy\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Custom hook for creating a new employee.\r\n * Includes optimistic updates and cache invalidation.\r\n * @returns Mutation result for creating an employee.\r\n */\r\nexport const useCreateEmployee = () => {\r\n  const queryClient = useQueryClient();\r\n  const { showError, showSuccess } = useNotifications();\r\n\r\n  interface CreateContext {\r\n    previousEmployees: Employee[] | undefined;\r\n  }\r\n\r\n  return useMutation<Employee, Error, CreateEmployeeData, CreateContext>({\r\n    mutationFn: async (employeeData: CreateEmployeeData) => {\r\n      const request = EmployeeTransformer.toCreateRequest(employeeData);\r\n      return await employeeApiService.create(request); // Removed redundant EmployeeTransformer.fromApi\r\n    },\r\n    onError: (err, _newEmployeeData, context) => {\r\n      if (context?.previousEmployees) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.all,\r\n          context.previousEmployees\r\n        );\r\n      }\r\n      showError(\r\n        `Failed to create employee: ${err.message || 'Unknown error occurred'}`\r\n      );\r\n    },\r\n    onMutate: async newEmployeeData => {\r\n      await queryClient.cancelQueries({ queryKey: employeeQueryKeys.all });\r\n      const previousEmployees = queryClient.getQueryData<Employee[]>(\r\n        employeeQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Employee[]>(\r\n        employeeQueryKeys.all,\r\n        (old = []) => {\r\n          const tempId = 'optimistic-' + Date.now().toString();\r\n          const now = new Date().toISOString();\r\n          const optimisticEmployee: Employee = {\r\n            availability: undefinedToNull(newEmployeeData.availability),\r\n            contactEmail: undefinedToNull(newEmployeeData.contactEmail),\r\n            contactInfo: newEmployeeData.contactInfo,\r\n            contactMobile: undefinedToNull(newEmployeeData.contactMobile),\r\n            contactPhone: undefinedToNull(newEmployeeData.contactPhone),\r\n            createdAt: now,\r\n            currentLocation: undefinedToNull(newEmployeeData.currentLocation),\r\n            department: undefinedToNull(newEmployeeData.department),\r\n            employeeId: newEmployeeData.employeeId,\r\n            fullName: newEmployeeData.fullName || newEmployeeData.name,\r\n            generalAssignments: newEmployeeData.generalAssignments || [],\r\n            hireDate: undefinedToNull(newEmployeeData.hireDate),\r\n            id: Number(tempId.replace('optimistic-', '')), // Attempt number ID\r\n            name: newEmployeeData.name,\r\n            notes: undefinedToNull(newEmployeeData.notes),\r\n            position: undefinedToNull(newEmployeeData.position),\r\n            profileImageUrl: undefinedToNull(newEmployeeData.profileImageUrl),\r\n            role: newEmployeeData.role,\r\n            shiftSchedule: undefinedToNull(newEmployeeData.shiftSchedule),\r\n            skills: newEmployeeData.skills || [],\r\n            status: undefinedToNull(newEmployeeData.status),\r\n            updatedAt: now,\r\n            ...(newEmployeeData.workingHours !== undefined && {\r\n              workingHours: undefinedToNull(newEmployeeData.workingHours),\r\n            }),\r\n          };\r\n          return [...old, optimisticEmployee];\r\n        }\r\n      );\r\n\r\n      return { previousEmployees };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });\r\n    },\r\n    onSuccess: data => {\r\n      showSuccess(`Employee \"${data.name}\" has been created successfully!`);\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Custom hook for updating an existing employee.\r\n * Includes optimistic updates and rollback on error.\r\n * @returns Mutation result for updating an employee.\r\n */\r\nexport const useUpdateEmployee = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface UpdateContext {\r\n    previousEmployee: Employee | undefined;\r\n    previousEmployeesList: Employee[] | undefined;\r\n  }\r\n\r\n  return useMutation<\r\n    Employee,\r\n    Error,\r\n    { data: Partial<CreateEmployeeData>; id: string },\r\n    UpdateContext\r\n  >({\r\n    mutationFn: async ({ data, id }) => {\r\n      const request = EmployeeTransformer.toUpdateRequest(data); // Removed cast\r\n      return await employeeApiService.update(id, request); // Removed redundant EmployeeTransformer.fromApi\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousEmployee) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.detail(variables.id),\r\n          context.previousEmployee\r\n        );\r\n      }\r\n      if (context?.previousEmployeesList) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.all,\r\n          context.previousEmployeesList\r\n        );\r\n      }\r\n      console.error('Failed to update employee:', err);\r\n    },\r\n    onMutate: async ({ data, id }) => {\r\n      await queryClient.cancelQueries({ queryKey: employeeQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: employeeQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousEmployee = queryClient.getQueryData<Employee>(\r\n        employeeQueryKeys.detail(id)\r\n      );\r\n      const previousEmployeesList = queryClient.getQueryData<Employee[]>(\r\n        employeeQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Employee>(employeeQueryKeys.detail(id), old => {\r\n        if (!old) return old;\r\n        const now = new Date().toISOString();\r\n\r\n        // Explicitly map updated fields to avoid issues with spread operator on different types\r\n        const updatedOptimistic: Employee = {\r\n          ...old,\r\n          availability: undefinedToNull(\r\n            data.availability === undefined\r\n              ? old.availability\r\n              : data.availability\r\n          ),\r\n          contactEmail: undefinedToNull(\r\n            data.contactEmail === undefined\r\n              ? old.contactEmail\r\n              : data.contactEmail\r\n          ),\r\n          contactInfo: data.contactInfo ?? old.contactInfo,\r\n          contactMobile: undefinedToNull(\r\n            data.contactMobile === undefined\r\n              ? old.contactMobile\r\n              : data.contactMobile\r\n          ),\r\n          contactPhone: undefinedToNull(\r\n            data.contactPhone === undefined\r\n              ? old.contactPhone\r\n              : data.contactPhone\r\n          ),\r\n          currentLocation: undefinedToNull(\r\n            data.currentLocation === undefined\r\n              ? old.currentLocation\r\n              : data.currentLocation\r\n          ),\r\n          department: undefinedToNull(data.department ?? old.department),\r\n          employeeId: data.employeeId ?? old.employeeId,\r\n          fullName: undefinedToNull(data.fullName ?? old.fullName),\r\n          generalAssignments: data.generalAssignments ?? old.generalAssignments,\r\n          hireDate: undefinedToNull(data.hireDate ?? old.hireDate),\r\n          name: data.name ?? old.name,\r\n          notes: undefinedToNull(\r\n            data.notes === undefined ? old.notes : data.notes\r\n          ),\r\n          position: undefinedToNull(data.position ?? old.position),\r\n          profileImageUrl: undefinedToNull(\r\n            data.profileImageUrl === undefined\r\n              ? old.profileImageUrl\r\n              : data.profileImageUrl\r\n          ),\r\n          role: data.role ?? old.role,\r\n          shiftSchedule: undefinedToNull(\r\n            data.shiftSchedule === undefined\r\n              ? old.shiftSchedule\r\n              : data.shiftSchedule\r\n          ),\r\n          skills: data.skills ?? old.skills,\r\n          status: undefinedToNull(data.status ?? old.status),\r\n          updatedAt: now,\r\n          ...(data.workingHours !== undefined && {\r\n            workingHours: undefinedToNull(data.workingHours),\r\n          }),\r\n        };\r\n        return updatedOptimistic;\r\n      });\r\n\r\n      queryClient.setQueryData<Employee[]>(\r\n        employeeQueryKeys.all,\r\n        (old = []) => {\r\n          return old.map(employee => {\r\n            if (employee.id === Number(id)) {\r\n              const now = new Date().toISOString();\r\n              return {\r\n                ...employee,\r\n                availability: undefinedToNull(\r\n                  data.availability ?? employee.availability\r\n                ),\r\n                contactEmail: undefinedToNull(\r\n                  data.contactEmail ?? employee.contactEmail\r\n                ),\r\n                contactInfo: data.contactInfo ?? employee.contactInfo,\r\n                contactMobile: undefinedToNull(\r\n                  data.contactMobile ?? employee.contactMobile\r\n                ),\r\n                contactPhone: undefinedToNull(\r\n                  data.contactPhone ?? employee.contactPhone\r\n                ),\r\n                currentLocation: undefinedToNull(\r\n                  data.currentLocation ?? employee.currentLocation\r\n                ),\r\n                department: undefinedToNull(\r\n                  data.department ?? employee.department\r\n                ),\r\n                employeeId: data.employeeId ?? employee.employeeId,\r\n                fullName: undefinedToNull(data.fullName ?? employee.fullName),\r\n                generalAssignments:\r\n                  data.generalAssignments ?? employee.generalAssignments,\r\n                hireDate: undefinedToNull(data.hireDate ?? employee.hireDate),\r\n                name: data.name ?? employee.name,\r\n                notes: undefinedToNull(data.notes ?? employee.notes),\r\n                position: undefinedToNull(data.position ?? employee.position),\r\n                profileImageUrl: undefinedToNull(\r\n                  data.profileImageUrl ?? employee.profileImageUrl\r\n                ),\r\n                role: data.role ?? employee.role,\r\n                shiftSchedule: undefinedToNull(\r\n                  data.shiftSchedule ?? employee.shiftSchedule\r\n                ),\r\n                skills: data.skills ?? employee.skills,\r\n                status: undefinedToNull(data.status ?? employee.status),\r\n                updatedAt: now,\r\n                ...(data.workingHours !== undefined && {\r\n                  workingHours: undefinedToNull(data.workingHours),\r\n                }),\r\n              };\r\n            }\r\n            return employee;\r\n          });\r\n        }\r\n      );\r\n\r\n      return { previousEmployee, previousEmployeesList };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: employeeQueryKeys.detail(variables.id),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Custom hook for deleting an existing employee.\r\n * Includes cache updates.\r\n * @returns Mutation result for deleting an employee.\r\n */\r\nexport const useDeleteEmployee = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface DeleteContext {\r\n    previousEmployeesList: Employee[] | undefined;\r\n  }\r\n\r\n  return useMutation<string, Error, string, DeleteContext>({\r\n    mutationFn: async (id: string) => {\r\n      await employeeApiService.delete(id);\r\n      return id;\r\n    },\r\n    onError: (err, _id, context) => {\r\n      if (context?.previousEmployeesList) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.all,\r\n          context.previousEmployeesList\r\n        );\r\n      }\r\n      console.error('Failed to delete employee:', err);\r\n    },\r\n    onMutate: async id => {\r\n      await queryClient.cancelQueries({ queryKey: employeeQueryKeys.all });\r\n      await queryClient.cancelQueries({\r\n        queryKey: employeeQueryKeys.detail(id),\r\n      });\r\n\r\n      const previousEmployeesList = queryClient.getQueryData<Employee[]>(\r\n        employeeQueryKeys.all\r\n      );\r\n\r\n      queryClient.setQueryData<Employee[]>(\r\n        employeeQueryKeys.all,\r\n        (old = []) => old.filter(employee => employee.id !== Number(id)) // Compare number ID\r\n      );\r\n\r\n      queryClient.removeQueries({ queryKey: employeeQueryKeys.detail(id) });\r\n\r\n      return { previousEmployeesList };\r\n    },\r\n    onSettled: () => {\r\n      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n\r\n// Removed useAssignVehicleToEmployee hook as its functionality is now\r\n// handled by the main update employee mutation.\r\n\r\n/**\r\n * Custom hook for updating an employee's availability status.\r\n * @returns Mutation result for updating availability status.\r\n */\r\nexport const useUpdateEmployeeAvailabilityStatus = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  interface UpdateAvailabilityContext {\r\n    previousEmployee: Employee | undefined;\r\n  }\r\n\r\n  // The input 'status' should align with DriverAvailabilityPrisma\r\n  return useMutation<\r\n    Employee,\r\n    Error,\r\n    { employeeId: string; status: DriverAvailabilityPrisma },\r\n    UpdateAvailabilityContext\r\n  >({\r\n    mutationFn: async ({ employeeId, status }) => {\r\n      // employeeApiService.updateAvailabilityStatus now expects DriverAvailabilityPrisma directly\r\n      const response = await employeeApiService.updateAvailabilityStatus(\r\n        employeeId,\r\n        status\r\n      );\r\n      return response; // Removed redundant EmployeeTransformer.fromApi\r\n    },\r\n    onError: (err, variables, context) => {\r\n      if (context?.previousEmployee) {\r\n        queryClient.setQueryData(\r\n          employeeQueryKeys.detail(variables.employeeId),\r\n          context.previousEmployee\r\n        );\r\n      }\r\n      console.error('Failed to update employee availability status:', err);\r\n    },\r\n    onMutate: async ({ employeeId, status }) => {\r\n      await queryClient.cancelQueries({\r\n        queryKey: employeeQueryKeys.detail(employeeId),\r\n      });\r\n      const previousEmployee = queryClient.getQueryData<Employee>(\r\n        employeeQueryKeys.detail(employeeId)\r\n      );\r\n\r\n      queryClient.setQueryData<Employee>(\r\n        employeeQueryKeys.detail(employeeId),\r\n        old => {\r\n          // Update the 'availability' field in the domain model\r\n          return old ? { ...old, availability: status } : old;\r\n        }\r\n      );\r\n\r\n      return { previousEmployee };\r\n    },\r\n    onSettled: (_data, _error, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: employeeQueryKeys.detail(variables.employeeId),\r\n      });\r\n      queryClient.invalidateQueries({ queryKey: employeeQueryKeys.all });\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;AAID;AAAA;AAQA,uOAA0D,uBAAuB;AACjF;AACA;AACA;AAEA,gTAA2E,0BAA0B;AAArG;;;;;;;AAKO,MAAM,oBAAoB;IAC/B,KAAK;QAAC;KAAY;IAClB,QAAQ,CAAC,KAAe;YAAC;YAAa;SAAG;AAE3C;AAOO,MAAM,eAAe,CAC1B;IAEA,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,kBAAkB,GAAG;KAAC,EAC1B;QACE,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM;QAChD,OAAO,SAAS,IAAI;IACtB,GACA,YACA;QACE,WAAW,IAAI,KAAK;QACpB,GAAG,OAAO;IACZ;AAEJ;AAQO,MAAM,qBAAqB,CAChC,MACA;IAEA,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB,OAAO;QAAC;QAAa;QAAQ;KAAK,GAAG;WAAI,kBAAkB,GAAG;KAAC,EAC/D;QACE,IAAI,MAAM;YACR,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC;YACpD,OAAO;QACT,OAAO;YACL,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM;YAChD,OAAO,SAAS,IAAI;QACtB;IACF,GACA,YACA;QACE,WAAW,IAAI,KAAK;QACpB,GAAG,OAAO;IACZ;AAEJ;AAOO,MAAM,cAAc,CAAC;IAC1B,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,kBAAkB,MAAM,CAAC;KAAI,EACjC;QACE,OAAO,MAAM,wIAAA,CAAA,qBAAkB,CAAC,OAAO,CAAC;IAC1C,GACA,YACA;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AAEJ;AAOO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,mBAAgB,AAAD;IAMlD,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAsD;QACrE,YAAY,OAAO;YACjB,MAAM,UAAU,iJAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;YACpD,OAAO,MAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,UAAU,gDAAgD;QACnG;QACA,SAAS,CAAC,KAAK,kBAAkB;YAC/B,IAAI,SAAS,mBAAmB;gBAC9B,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,QAAQ,iBAAiB;YAE7B;YACA,UACE,CAAC,2BAA2B,EAAE,IAAI,OAAO,IAAI,0BAA0B;QAE3E;QACA,UAAU,OAAM;YACd,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;YAClE,MAAM,oBAAoB,YAAY,YAAY,CAChD,kBAAkB,GAAG;YAGvB,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,CAAC,MAAM,EAAE;gBACP,MAAM,SAAS,gBAAgB,KAAK,GAAG,GAAG,QAAQ;gBAClD,MAAM,MAAM,IAAI,OAAO,WAAW;gBAClC,MAAM,qBAA+B;oBACnC,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,YAAY;oBAC1D,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,YAAY;oBAC1D,aAAa,gBAAgB,WAAW;oBACxC,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,aAAa;oBAC5D,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,YAAY;oBAC1D,WAAW;oBACX,iBAAiB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,eAAe;oBAChE,YAAY,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,UAAU;oBACtD,YAAY,gBAAgB,UAAU;oBACtC,UAAU,gBAAgB,QAAQ,IAAI,gBAAgB,IAAI;oBAC1D,oBAAoB,gBAAgB,kBAAkB,IAAI,EAAE;oBAC5D,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,QAAQ;oBAClD,IAAI,OAAO,OAAO,OAAO,CAAC,eAAe;oBACzC,MAAM,gBAAgB,IAAI;oBAC1B,OAAO,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,KAAK;oBAC5C,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,QAAQ;oBAClD,iBAAiB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,eAAe;oBAChE,MAAM,gBAAgB,IAAI;oBAC1B,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,aAAa;oBAC5D,QAAQ,gBAAgB,MAAM,IAAI,EAAE;oBACpC,QAAQ,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,MAAM;oBAC9C,WAAW;oBACX,GAAI,gBAAgB,YAAY,KAAK,aAAa;wBAChD,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,YAAY;oBAC5D,CAAC;gBACH;gBACA,OAAO;uBAAI;oBAAK;iBAAmB;YACrC;YAGF,OAAO;gBAAE;YAAkB;QAC7B;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;QAClE;QACA,WAAW,CAAA;YACT,YAAY,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,gCAAgC,CAAC;QACtE;IACF;AACF;AAOO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAOjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAKf;QACA,YAAY,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;YAC7B,MAAM,UAAU,iJAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC,OAAO,eAAe;YAC1E,OAAO,MAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,IAAI,UAAU,gDAAgD;QACvG;QACA,SAAS,CAAC,KAAK,WAAW;YACxB,IAAI,SAAS,kBAAkB;gBAC7B,YAAY,YAAY,CACtB,kBAAkB,MAAM,CAAC,UAAU,EAAE,GACrC,QAAQ,gBAAgB;YAE5B;YACA,IAAI,SAAS,uBAAuB;gBAClC,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,QAAQ,qBAAqB;YAEjC;YACA,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;QACA,UAAU,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;YAC3B,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;YAClE,MAAM,YAAY,aAAa,CAAC;gBAC9B,UAAU,kBAAkB,MAAM,CAAC;YACrC;YAEA,MAAM,mBAAmB,YAAY,YAAY,CAC/C,kBAAkB,MAAM,CAAC;YAE3B,MAAM,wBAAwB,YAAY,YAAY,CACpD,kBAAkB,GAAG;YAGvB,YAAY,YAAY,CAAW,kBAAkB,MAAM,CAAC,KAAK,CAAA;gBAC/D,IAAI,CAAC,KAAK,OAAO;gBACjB,MAAM,MAAM,IAAI,OAAO,WAAW;gBAElC,wFAAwF;gBACxF,MAAM,oBAA8B;oBAClC,GAAG,GAAG;oBACN,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,KAAK,YAClB,IAAI,YAAY,GAChB,KAAK,YAAY;oBAEvB,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,KAAK,YAClB,IAAI,YAAY,GAChB,KAAK,YAAY;oBAEvB,aAAa,KAAK,WAAW,IAAI,IAAI,WAAW;oBAChD,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,aAAa,KAAK,YACnB,IAAI,aAAa,GACjB,KAAK,aAAa;oBAExB,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,KAAK,YAClB,IAAI,YAAY,GAChB,KAAK,YAAY;oBAEvB,iBAAiB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,eAAe,KAAK,YACrB,IAAI,eAAe,GACnB,KAAK,eAAe;oBAE1B,YAAY,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,UAAU,IAAI,IAAI,UAAU;oBAC7D,YAAY,KAAK,UAAU,IAAI,IAAI,UAAU;oBAC7C,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,QAAQ;oBACvD,oBAAoB,KAAK,kBAAkB,IAAI,IAAI,kBAAkB;oBACrE,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,QAAQ;oBACvD,MAAM,KAAK,IAAI,IAAI,IAAI,IAAI;oBAC3B,OAAO,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EACnB,KAAK,KAAK,KAAK,YAAY,IAAI,KAAK,GAAG,KAAK,KAAK;oBAEnD,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,QAAQ;oBACvD,iBAAiB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,eAAe,KAAK,YACrB,IAAI,eAAe,GACnB,KAAK,eAAe;oBAE1B,MAAM,KAAK,IAAI,IAAI,IAAI,IAAI;oBAC3B,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,aAAa,KAAK,YACnB,IAAI,aAAa,GACjB,KAAK,aAAa;oBAExB,QAAQ,KAAK,MAAM,IAAI,IAAI,MAAM;oBACjC,QAAQ,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,MAAM,IAAI,IAAI,MAAM;oBACjD,WAAW;oBACX,GAAI,KAAK,YAAY,KAAK,aAAa;wBACrC,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,YAAY;oBACjD,CAAC;gBACH;gBACA,OAAO;YACT;YAEA,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,CAAC,MAAM,EAAE;gBACP,OAAO,IAAI,GAAG,CAAC,CAAA;oBACb,IAAI,SAAS,EAAE,KAAK,OAAO,KAAK;wBAC9B,MAAM,MAAM,IAAI,OAAO,WAAW;wBAClC,OAAO;4BACL,GAAG,QAAQ;4BACX,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,IAAI,SAAS,YAAY;4BAE5C,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,IAAI,SAAS,YAAY;4BAE5C,aAAa,KAAK,WAAW,IAAI,SAAS,WAAW;4BACrD,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,aAAa,IAAI,SAAS,aAAa;4BAE9C,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC1B,KAAK,YAAY,IAAI,SAAS,YAAY;4BAE5C,iBAAiB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,eAAe,IAAI,SAAS,eAAe;4BAElD,YAAY,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EACxB,KAAK,UAAU,IAAI,SAAS,UAAU;4BAExC,YAAY,KAAK,UAAU,IAAI,SAAS,UAAU;4BAClD,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,SAAS,QAAQ;4BAC5D,oBACE,KAAK,kBAAkB,IAAI,SAAS,kBAAkB;4BACxD,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,SAAS,QAAQ;4BAC5D,MAAM,KAAK,IAAI,IAAI,SAAS,IAAI;4BAChC,OAAO,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,KAAK,IAAI,SAAS,KAAK;4BACnD,UAAU,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,QAAQ,IAAI,SAAS,QAAQ;4BAC5D,iBAAiB,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC7B,KAAK,eAAe,IAAI,SAAS,eAAe;4BAElD,MAAM,KAAK,IAAI,IAAI,SAAS,IAAI;4BAChC,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAC3B,KAAK,aAAa,IAAI,SAAS,aAAa;4BAE9C,QAAQ,KAAK,MAAM,IAAI,SAAS,MAAM;4BACtC,QAAQ,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,MAAM,IAAI,SAAS,MAAM;4BACtD,WAAW;4BACX,GAAI,KAAK,YAAY,KAAK,aAAa;gCACrC,cAAc,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,YAAY;4BACjD,CAAC;wBACH;oBACF;oBACA,OAAO;gBACT;YACF;YAGF,OAAO;gBAAE;gBAAkB;YAAsB;QACnD;QACA,WAAW,CAAC,OAAO,QAAQ;YACzB,YAAY,iBAAiB,CAAC;gBAC5B,UAAU,kBAAkB,MAAM,CAAC,UAAU,EAAE;YACjD;YACA,YAAY,iBAAiB,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;QAClE;IACF;AACF;AAOO,MAAM,oBAAoB;IAC/B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAMjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAwC;QACvD,YAAY,OAAO;YACjB,MAAM,wIAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC;YAChC,OAAO;QACT;QACA,SAAS,CAAC,KAAK,KAAK;YAClB,IAAI,SAAS,uBAAuB;gBAClC,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,QAAQ,qBAAqB;YAEjC;YACA,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;QACA,UAAU,OAAM;YACd,MAAM,YAAY,aAAa,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;YAClE,MAAM,YAAY,aAAa,CAAC;gBAC9B,UAAU,kBAAkB,MAAM,CAAC;YACrC;YAEA,MAAM,wBAAwB,YAAY,YAAY,CACpD,kBAAkB,GAAG;YAGvB,YAAY,YAAY,CACtB,kBAAkB,GAAG,EACrB,CAAC,MAAM,EAAE,GAAK,IAAI,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK,OAAO,KAAK,oBAAoB;;YAGvF,YAAY,aAAa,CAAC;gBAAE,UAAU,kBAAkB,MAAM,CAAC;YAAI;YAEnE,OAAO;gBAAE;YAAsB;QACjC;QACA,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;QAClE;IACF;AACF;AASO,MAAM,sCAAsC;IACjD,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAMjC,gEAAgE;IAChE,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAKf;QACA,YAAY,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE;YACvC,4FAA4F;YAC5F,MAAM,WAAW,MAAM,wIAAA,CAAA,qBAAkB,CAAC,wBAAwB,CAChE,YACA;YAEF,OAAO,UAAU,gDAAgD;QACnE;QACA,SAAS,CAAC,KAAK,WAAW;YACxB,IAAI,SAAS,kBAAkB;gBAC7B,YAAY,YAAY,CACtB,kBAAkB,MAAM,CAAC,UAAU,UAAU,GAC7C,QAAQ,gBAAgB;YAE5B;YACA,QAAQ,KAAK,CAAC,kDAAkD;QAClE;QACA,UAAU,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE;YACrC,MAAM,YAAY,aAAa,CAAC;gBAC9B,UAAU,kBAAkB,MAAM,CAAC;YACrC;YACA,MAAM,mBAAmB,YAAY,YAAY,CAC/C,kBAAkB,MAAM,CAAC;YAG3B,YAAY,YAAY,CACtB,kBAAkB,MAAM,CAAC,aACzB,CAAA;gBACE,sDAAsD;gBACtD,OAAO,MAAM;oBAAE,GAAG,GAAG;oBAAE,cAAc;gBAAO,IAAI;YAClD;YAGF,OAAO;gBAAE;YAAiB;QAC5B;QACA,WAAW,CAAC,OAAO,QAAQ;YACzB,YAAY,iBAAiB,CAAC;gBAC5B,UAAU,kBAAkB,MAAM,CAAC,UAAU,UAAU;YACzD;YACA,YAAY,iBAAiB,CAAC;gBAAE,UAAU,kBAAkB,GAAG;YAAC;QAClE;IACF;AACF", "debugId": null}}, {"offset": {"line": 9415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/lib/stores/queries/useVehicles.ts"], "sourcesContent": ["/**\r\n * @file TanStack Query hooks for Vehicle-related data.\r\n * @module stores/queries/useVehicles\r\n */\r\n\r\nimport type { UseQueryOptions } from '@tanstack/react-query';\r\n\r\nimport { useMutation, useQueryClient } from '@tanstack/react-query';\r\n\r\nimport type { ApiError } from '@/lib/types/api';\r\nimport type { CreateVehicleData, Vehicle } from '@/lib/types/domain';\r\n\r\nimport { useCrudQuery } from '@/hooks/api/useSmartQuery';\r\nimport { useNotifications } from '@/hooks/ui/useNotifications';\r\nimport { VehicleTransformer } from '@/lib/transformers/vehicleTransformer';\r\n\r\nimport { vehicleApiService } from '../../api/services/apiServiceFactory'; // Use centralized service from factory\r\n\r\nexport const vehicleQueryKeys = {\r\n  all: ['vehicles'] as const,\r\n  detail: (id: number) => ['vehicles', id] as const,\r\n};\r\n\r\n/**\r\n * Hook to fetch all vehicles.\r\n */\r\nexport const useVehicles = (\r\n  options?: Omit<\r\n    UseQueryOptions<Vehicle[], ApiError>,\r\n    'queryFn' | 'queryKey'\r\n  > & { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<Vehicle[], ApiError>(\r\n    [...vehicleQueryKeys.all], // Spread to create a mutable array\r\n    async () => {\r\n      const response = await vehicleApiService.getAll();\r\n      return response.data;\r\n    },\r\n    'vehicle', // entityType for WebSocket events\r\n    {\r\n      staleTime: 5 * 60 * 1000, // 5 minutes\r\n      ...options,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook to fetch a single vehicle by its ID.\r\n */\r\nexport const useVehicle = (\r\n  id: null | number,\r\n  options?: Omit<\r\n    UseQueryOptions<Vehicle, ApiError>,\r\n    'enabled' | 'queryFn' | 'queryKey'\r\n  > & { enabled?: boolean }\r\n) => {\r\n  return useCrudQuery<Vehicle, ApiError>(\r\n    [...vehicleQueryKeys.detail(id!)],\r\n    () => vehicleApiService.getById(id!),\r\n    'vehicle', // entityType for WebSocket events\r\n    {\r\n      enabled: !!id && (options?.enabled ?? true), // Only run query if id is not null AND options.enabled is true (or undefined)\r\n      staleTime: 5 * 60 * 1000,\r\n      ...options, // Spread additional options\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook to create a new vehicle.\r\n */\r\nexport const useCreateVehicle = () => {\r\n  const queryClient = useQueryClient();\r\n  const { showError, showSuccess } = useNotifications();\r\n\r\n  return useMutation<Vehicle, ApiError, CreateVehicleData>({\r\n    mutationFn: (newVehicleData: CreateVehicleData) => {\r\n      const transformedData =\r\n        VehicleTransformer.toCreateRequest(newVehicleData);\r\n      return vehicleApiService.create(transformedData);\r\n    },\r\n    onError: error => {\r\n      showError(\r\n        `Failed to create vehicle: ${error.message || 'Unknown error occurred'}`\r\n      );\r\n    },\r\n    onSuccess: data => {\r\n      // Invalidate and refetch all vehicles query after a successful creation\r\n      queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all });\r\n      showSuccess(\r\n        `Vehicle \"${data.licensePlate}\" has been created successfully!`\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to update an existing vehicle.\r\n */\r\nexport const useUpdateVehicle = () => {\r\n  const queryClient = useQueryClient();\r\n  const { showError, showSuccess } = useNotifications();\r\n\r\n  return useMutation<\r\n    Vehicle,\r\n    ApiError,\r\n    { data: Partial<CreateVehicleData>; id: number }\r\n  >({\r\n    mutationFn: ({ data, id }) => {\r\n      const transformedData = VehicleTransformer.toUpdateRequest(data);\r\n      return vehicleApiService.update(id, transformedData);\r\n    },\r\n    onError: error => {\r\n      showError(\r\n        `Failed to update vehicle: ${error.message || 'Unknown error occurred'}`\r\n      );\r\n    },\r\n    onSuccess: updatedVehicle => {\r\n      queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all });\r\n      queryClient.invalidateQueries({\r\n        queryKey: vehicleQueryKeys.detail(updatedVehicle.id),\r\n      });\r\n      showSuccess(\r\n        `Vehicle \"${updatedVehicle.licensePlate}\" has been updated successfully!`\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to delete a vehicle.\r\n */\r\nexport const useDeleteVehicle = () => {\r\n  const queryClient = useQueryClient();\r\n  const { showError, showSuccess } = useNotifications();\r\n\r\n  return useMutation<void, ApiError, number>({\r\n    mutationFn: (id: number) => vehicleApiService.delete(id),\r\n    onError: error => {\r\n      showError(\r\n        `Failed to delete vehicle: ${error.message || 'Unknown error occurred'}`\r\n      );\r\n    },\r\n    onSuccess: (_data, id) => {\r\n      queryClient.invalidateQueries({ queryKey: vehicleQueryKeys.all });\r\n      queryClient.removeQueries({ queryKey: vehicleQueryKeys.detail(id) });\r\n      showSuccess('Vehicle has been deleted successfully!');\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;AAID;AAAA;AAKA;AACA;AACA;AAEA,gTAA0E,uCAAuC;AAAjH;;;;;;AAEO,MAAM,mBAAmB;IAC9B,KAAK;QAAC;KAAW;IACjB,QAAQ,CAAC,KAAe;YAAC;YAAY;SAAG;AAC1C;AAKO,MAAM,cAAc,CACzB;IAKA,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,iBAAiB,GAAG;KAAC,EACzB;QACE,MAAM,WAAW,MAAM,wIAAA,CAAA,oBAAiB,CAAC,MAAM;QAC/C,OAAO,SAAS,IAAI;IACtB,GACA,WACA;QACE,WAAW,IAAI,KAAK;QACpB,GAAG,OAAO;IACZ;AAEJ;AAKO,MAAM,aAAa,CACxB,IACA;IAKA,OAAO,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAChB;WAAI,iBAAiB,MAAM,CAAC;KAAK,EACjC,IAAM,wIAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC,KAChC,WACA;QACE,SAAS,CAAC,CAAC,MAAM,CAAC,SAAS,WAAW,IAAI;QAC1C,WAAW,IAAI,KAAK;QACpB,GAAG,OAAO;IACZ;AAEJ;AAKO,MAAM,mBAAmB;IAC9B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,mBAAgB,AAAD;IAElD,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAwC;QACvD,YAAY,CAAC;YACX,MAAM,kBACJ,gJAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;YACrC,OAAO,wIAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC;QAClC;QACA,SAAS,CAAA;YACP,UACE,CAAC,0BAA0B,EAAE,MAAM,OAAO,IAAI,0BAA0B;QAE5E;QACA,WAAW,CAAA;YACT,wEAAwE;YACxE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,iBAAiB,GAAG;YAAC;YAC/D,YACE,CAAC,SAAS,EAAE,KAAK,YAAY,CAAC,gCAAgC,CAAC;QAEnE;IACF;AACF;AAKO,MAAM,mBAAmB;IAC9B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,mBAAgB,AAAD;IAElD,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAIf;QACA,YAAY,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;YACvB,MAAM,kBAAkB,gJAAA,CAAA,qBAAkB,CAAC,eAAe,CAAC;YAC3D,OAAO,wIAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC,IAAI;QACtC;QACA,SAAS,CAAA;YACP,UACE,CAAC,0BAA0B,EAAE,MAAM,OAAO,IAAI,0BAA0B;QAE5E;QACA,WAAW,CAAA;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU,iBAAiB,GAAG;YAAC;YAC/D,YAAY,iBAAiB,CAAC;gBAC5B,UAAU,iBAAiB,MAAM,CAAC,eAAe,EAAE;YACrD;YACA,YACE,CAAC,SAAS,EAAE,eAAe,YAAY,CAAC,gCAAgC,CAAC;QAE7E;IACF;AACF;AAKO,MAAM,mBAAmB;IAC9B,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,mBAAgB,AAAD;IAElD,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAA0B;QACzC,YAAY,CAAC,KAAe,wIAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC;QACrD,SAAS,CAAA;YACP,UACE,CAAC,0BAA0B,EAAE,MAAM,OAAO,IAAI,0BAA0B;QAE5E;QACA,WAAW,CAAC,OAAO;YACjB,YAAY,iBAAiB,CAAC;gBAAE,UAAU,iBAAiB,GAAG;YAAC;YAC/D,YAAY,aAAa,CAAC;gBAAE,UAAU,iBAAiB,MAAM,CAAC;YAAI;YAClE,YAAY;QACd;IACF;AACF", "debugId": null}}, {"offset": {"line": 9534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/app/delegations/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Briefcase, PlusCircle, Search, Settings } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport React, { useState } from 'react';\r\n\r\nimport ErrorBoundary from '@/components/error-boundaries/ErrorBoundary';\r\nimport { DelegationDashboardSettings } from '@/components/features/delegations/dashboard';\r\nimport DelegationFilters, {\r\n  type DelegationFilterValues,\r\n} from '@/components/features/delegations/DelegationFilters';\r\nimport {\r\n  DelegationListContainer,\r\n  DelegationViewRenderer,\r\n} from '@/components/features/delegations/list';\r\nimport { ViewReportButton } from '@/components/reports/ViewReportButton';\r\nimport { ActionButton } from '@/components/ui/action-button';\r\nimport { AppBreadcrumb } from '@/components/ui/app-breadcrumb';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog';\r\nimport { Input } from '@/components/ui/input';\r\nimport { DataLoader, SkeletonLoader } from '@/components/ui/loading';\r\nimport { PageHeader } from '@/components/ui/PageHeader';\r\nimport { useDashboardStore } from '@/hooks/domain/useDashboardStore';\r\nimport { useDelegations } from '@/lib/stores/queries/useDelegations';\r\nimport { useEmployees } from '@/lib/stores/queries/useEmployees';\r\nimport { useVehicles } from '@/lib/stores/queries/useVehicles';\r\n\r\nexport default function DelegationsPage() {\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [filters, setFilters] = useState<DelegationFilterValues>({\r\n    dateRange: {},\r\n    drivers: [],\r\n    escorts: [],\r\n    location: [],\r\n    search: '',\r\n    status: [],\r\n    vehicles: [],\r\n  });\r\n\r\n  const getDelegationsReportUrl = () => {\r\n    const queryParams = new URLSearchParams({\r\n      searchTerm: filters.search || searchTerm,\r\n    }).toString();\r\n    return `/delegations/report/list?${queryParams}`;\r\n  };\r\n\r\n  // Handle filter changes\r\n  const handleFiltersChange = (newFilters: DelegationFilterValues) => {\r\n    setFilters(newFilters);\r\n    setSearchTerm(newFilters.search); // Keep legacy search term in sync\r\n  };\r\n\r\n  // Use the generic dashboard store for delegations\r\n  const dashboardStore = useDashboardStore('delegation');\r\n  const { layout } = dashboardStore();\r\n\r\n  // Fetch data for filter options\r\n  const { data: allDelegations = [] } = useDelegations();\r\n  const { data: employees = [] } = useEmployees();\r\n  const { data: vehicles = [] } = useVehicles();\r\n\r\n  // Extract unique locations from delegations\r\n  const locationsList = [\r\n    ...new Set(\r\n      allDelegations.map(delegation => delegation.location).filter(Boolean)\r\n    ),\r\n  ];\r\n\r\n  // Transform employees for filter component\r\n  const employeesList = employees.map(employee => ({\r\n    id: employee.id.toString(),\r\n    name: employee.name,\r\n    role: employee.role,\r\n  }));\r\n\r\n  // Transform vehicles for filter component\r\n  const vehiclesList = vehicles.map(vehicle => ({\r\n    id: vehicle.id.toString(),\r\n    name: `${vehicle.make} ${vehicle.model}`,\r\n    type: `${vehicle.year} ${vehicle.licensePlate}`, // Use year and make as type\r\n  }));\r\n\r\n  return (\r\n    <ErrorBoundary>\r\n      <div className=\"space-y-6\">\r\n        <AppBreadcrumb homeHref=\"/\" homeLabel=\"Dashboard\" />\r\n        <PageHeader\r\n          description=\"Track and manage all your events, trips, and delegate information.\"\r\n          icon={Briefcase}\r\n          title=\"Manage Delegations\"\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            <ActionButton\r\n              actionType=\"primary\"\r\n              asChild\r\n              icon={<PlusCircle className=\"mr-2 size-4\" />}\r\n            >\r\n              <Link href=\"/delegations/add\">Add New Delegation</Link>\r\n            </ActionButton>\r\n            <ViewReportButton\r\n              getReportUrl={getDelegationsReportUrl}\r\n              isList={true}\r\n            />\r\n            {/* New Settings Button */}\r\n            <Dialog>\r\n              <DialogTrigger asChild>\r\n                <ActionButton\r\n                  actionType=\"secondary\"\r\n                  icon={<Settings className=\"size-4\" />}\r\n                >\r\n                  Settings\r\n                </ActionButton>\r\n              </DialogTrigger>\r\n              <DialogContent className=\"sm:max-w-[600px]\">\r\n                <DialogTitle>Dashboard Settings</DialogTitle>\r\n                <DialogDescription>\r\n                  Customize the display and behavior of your delegation\r\n                  dashboard.\r\n                </DialogDescription>\r\n                <DelegationDashboardSettings />\r\n              </DialogContent>\r\n            </Dialog>\r\n          </div>\r\n        </PageHeader>\r\n\r\n        <div className=\"mb-6 rounded-lg bg-card p-4 shadow-md\">\r\n          <DelegationFilters\r\n            employeesList={employeesList}\r\n            initialFilters={{\r\n              dateRange: {},\r\n              drivers: [],\r\n              escorts: [],\r\n              location: [],\r\n              search: searchTerm,\r\n              status: [],\r\n              vehicles: [],\r\n            }}\r\n            locationsList={locationsList}\r\n            onFiltersChange={handleFiltersChange}\r\n            vehiclesList={vehiclesList}\r\n          />\r\n        </div>\r\n\r\n        <DelegationListContainer filters={filters} searchTerm={searchTerm}>\r\n          {({ delegations, error, fetchDelegations, loading }) => (\r\n            <DataLoader\r\n              data={delegations}\r\n              emptyComponent={\r\n                <div className=\"rounded-lg bg-card py-12 text-center shadow-md\">\r\n                  <Briefcase className=\"mx-auto mb-6 size-16 text-muted-foreground\" />\r\n                  <h3 className=\"mb-2 text-2xl font-semibold text-foreground\">\r\n                    {searchTerm\r\n                      ? 'No Delegations Match Your Search'\r\n                      : 'No Delegations Yet!'}\r\n                  </h3>\r\n                  <p className=\"mx-auto mb-6 mt-2 max-w-md text-muted-foreground\">\r\n                    {searchTerm\r\n                      ? 'Try adjusting your search terms or add a new delegation.'\r\n                      : \"It looks like you haven't added any delegations yet. Get started by adding one.\"}\r\n                  </p>\r\n                  {!searchTerm && (\r\n                    <ActionButton\r\n                      actionType=\"primary\"\r\n                      asChild\r\n                      icon={<PlusCircle className=\"size-4\" />}\r\n                      size=\"lg\"\r\n                    >\r\n                      <Link href=\"/delegations/add\">\r\n                        Add Your First Delegation\r\n                      </Link>\r\n                    </ActionButton>\r\n                  )}\r\n                </div>\r\n              }\r\n              error={error}\r\n              isLoading={loading}\r\n              loadingComponent={<SkeletonLoader count={3} variant=\"card\" />}\r\n              onRetry={fetchDelegations}\r\n            >\r\n              {delegationsData => (\r\n                <DelegationViewRenderer\r\n                  compactMode={layout.compactMode}\r\n                  delegations={delegationsData}\r\n                  gridColumns={layout.gridColumns}\r\n                  viewMode={layout.viewMode}\r\n                />\r\n              )}\r\n            </DataLoader>\r\n          )}\r\n        </DelegationListContainer>\r\n      </div>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAAA;AACA;AAGA;AAAA;AAAA;AAIA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AACA;AA/BA;;;;;;;;;;;;;;;;;;;AAiCe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;QAC7D,WAAW,CAAC;QACZ,SAAS,EAAE;QACX,SAAS,EAAE;QACX,UAAU,EAAE;QACZ,QAAQ;QACR,QAAQ,EAAE;QACV,UAAU,EAAE;IACd;IAEA,MAAM,0BAA0B;QAC9B,MAAM,cAAc,IAAI,gBAAgB;YACtC,YAAY,QAAQ,MAAM,IAAI;QAChC,GAAG,QAAQ;QACX,OAAO,CAAC,yBAAyB,EAAE,aAAa;IAClD;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,cAAc,WAAW,MAAM,GAAG,kCAAkC;IACtE;IAEA,kDAAkD;IAClD,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,oBAAiB,AAAD,EAAE;IACzC,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,gCAAgC;IAChC,MAAM,EAAE,MAAM,iBAAiB,EAAE,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD;IACnD,MAAM,EAAE,MAAM,YAAY,EAAE,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,eAAY,AAAD;IAC5C,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD;IAE1C,4CAA4C;IAC5C,MAAM,gBAAgB;WACjB,IAAI,IACL,eAAe,GAAG,CAAC,CAAA,aAAc,WAAW,QAAQ,EAAE,MAAM,CAAC;KAEhE;IAED,2CAA2C;IAC3C,MAAM,gBAAgB,UAAU,GAAG,CAAC,CAAA,WAAY,CAAC;YAC/C,IAAI,SAAS,EAAE,CAAC,QAAQ;YACxB,MAAM,SAAS,IAAI;YACnB,MAAM,SAAS,IAAI;QACrB,CAAC;IAED,0CAA0C;IAC1C,MAAM,eAAe,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;YAC5C,IAAI,QAAQ,EAAE,CAAC,QAAQ;YACvB,MAAM,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,EAAE;YACxC,MAAM,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,YAAY,EAAE;QACjD,CAAC;IAED,qBACE,8OAAC,0JAAA,CAAA,UAAa;kBACZ,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,6IAAA,CAAA,gBAAa;oBAAC,UAAS;oBAAI,WAAU;;;;;;8BACtC,8OAAC,sIAAA,CAAA,aAAU;oBACT,aAAY;oBACZ,MAAM,4MAAA,CAAA,YAAS;oBACf,OAAM;8BAEN,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4IAAA,CAAA,eAAY;gCACX,YAAW;gCACX,OAAO;gCACP,oBAAM,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;0CAE5B,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAmB;;;;;;;;;;;0CAEhC,8OAAC,iJAAA,CAAA,mBAAgB;gCACf,cAAc;gCACd,QAAQ;;;;;;0CAGV,8OAAC,kIAAA,CAAA,SAAM;;kDACL,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,OAAO;kDACpB,cAAA,8OAAC,4IAAA,CAAA,eAAY;4CACX,YAAW;4CACX,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;sDAC3B;;;;;;;;;;;kDAIH,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;;0DACvB,8OAAC,kIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,8OAAC,kIAAA,CAAA,oBAAiB;0DAAC;;;;;;0DAInB,8OAAC,mPAAA,CAAA,8BAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMpC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kKAAA,CAAA,UAAiB;wBAChB,eAAe;wBACf,gBAAgB;4BACd,WAAW,CAAC;4BACZ,SAAS,EAAE;4BACX,SAAS,EAAE;4BACX,UAAU,EAAE;4BACZ,QAAQ;4BACR,QAAQ,EAAE;4BACV,UAAU,EAAE;wBACd;wBACA,eAAe;wBACf,iBAAiB;wBACjB,cAAc;;;;;;;;;;;8BAIlB,8OAAC,sOAAA,CAAA,0BAAuB;oBAAC,SAAS;oBAAS,YAAY;8BACpD,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,gBAAgB,EAAE,OAAO,EAAE,iBACjD,8OAAC,mIAAA,CAAA,aAAU;4BACT,MAAM;4BACN,8BACE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAG,WAAU;kDACX,aACG,qCACA;;;;;;kDAEN,8OAAC;wCAAE,WAAU;kDACV,aACG,6DACA;;;;;;oCAEL,CAAC,4BACA,8OAAC,4IAAA,CAAA,eAAY;wCACX,YAAW;wCACX,OAAO;wCACP,oBAAM,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAC5B,MAAK;kDAEL,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAmB;;;;;;;;;;;;;;;;;4BAOtC,OAAO;4BACP,WAAW;4BACX,gCAAkB,8OAAC,mIAAA,CAAA,iBAAc;gCAAC,OAAO;gCAAG,SAAQ;;;;;;4BACpD,SAAS;sCAER,CAAA,gCACC,8OAAC,oOAAA,CAAA,yBAAsB;oCACrB,aAAa,OAAO,WAAW;oCAC/B,aAAa;oCACb,aAAa,OAAO,WAAW;oCAC/B,UAAU,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C", "debugId": null}}]}