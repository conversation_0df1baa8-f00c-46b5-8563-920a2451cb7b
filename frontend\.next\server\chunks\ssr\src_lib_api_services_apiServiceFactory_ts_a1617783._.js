module.exports = {

"[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiServiceFactory"]),
    "apiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiServiceFactory"]),
    "delegationApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["delegationApiService"]),
    "employeeApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["employeeApiService"]),
    "reliabilityApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reliabilityApiService"]),
    "setFactoryAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setFactoryAuthTokenProvider"]),
    "taskApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["taskApiService"]),
    "vehicleApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["vehicleApiService"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ApiServiceFactory"]),
    "apiServiceFactory": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["apiServiceFactory"]),
    "delegationApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["delegationApiService"]),
    "employeeApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["employeeApiService"]),
    "reliabilityApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["reliabilityApiService"]),
    "setFactoryAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["setFactoryAuthTokenProvider"]),
    "taskApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["taskApiService"]),
    "vehicleApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["vehicleApiService"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <exports>");
}}),

};