{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/alert.tsx"], "sourcesContent": ["import { cva, type VariantProps } from 'class-variance-authority';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst alertVariants = cva(\r\n  'relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7',\r\n  {\r\n    defaultVariants: {\r\n      variant: 'default',\r\n    },\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-background text-foreground',\r\n        destructive:\r\n          'border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive',\r\n      },\r\n    },\r\n  }\r\n);\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    className={cn(alertVariants({ variant }), className)}\r\n    ref={ref}\r\n    role=\"alert\"\r\n    {...props}\r\n  />\r\n));\r\nAlert.displayName = 'Alert';\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    className={cn('mb-1 font-medium leading-none tracking-tight', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nAlertTitle.displayName = 'AlertTitle';\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    className={cn('text-sm [&_p]:leading-relaxed', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nAlertDescription.displayName = 'AlertDescription';\r\n\r\nexport { Alert, AlertDescription, AlertTitle };\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;AAAA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,iBAAiB;QACf,SAAS;IACX;IACA,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAC1C,KAAK;QACL,MAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC9D,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils/index\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAAA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/checkbox.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as CheckboxPrimitive from '@radix-ui/react-checkbox';\r\nimport { Check } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Checkbox = React.forwardRef<\r\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    className={cn(\r\n      'peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  >\r\n    <CheckboxPrimitive.Indicator\r\n      className={cn('flex items-center justify-center text-current')}\r\n    >\r\n      <Check className=\"size-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n));\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName;\r\n\r\nexport { Checkbox };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,uKAAA,CAAA,OAAsB;QACrB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<'input'>>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        className={cn(\r\n          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\r\n          className\r\n        )}\r\n        ref={ref}\r\n        type={type}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nInput.displayName = 'Input';\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACL,MAAM;QACL,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils/index\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/login-loading.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Loader2, Shield, Check } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface LoginLoadingProps {\r\n  className?: string;\r\n  stage?: 'authenticating' | 'verifying' | 'redirecting' | 'success';\r\n  message?: string;\r\n}\r\n\r\n/**\r\n * Login Loading Component\r\n *\r\n * A sophisticated loading animation for the login process that provides\r\n * clear visual feedback about the authentication stage.\r\n */\r\nexport function LoginLoading({\r\n  className,\r\n  stage = 'authenticating',\r\n  message,\r\n}: LoginLoadingProps) {\r\n  const getStageConfig = () => {\r\n    switch (stage) {\r\n      case 'authenticating':\r\n        return {\r\n          icon: <Loader2 className=\"size-6 animate-spin text-primary\" />,\r\n          text: message || 'Authenticating credentials...',\r\n          description: 'Verifying your identity securely',\r\n        };\r\n      case 'verifying':\r\n        return {\r\n          icon: <Shield className=\"size-6 text-accent animate-pulse\" />,\r\n          text: message || 'Verifying security...',\r\n          description: 'Checking account permissions',\r\n        };\r\n      case 'redirecting':\r\n        return {\r\n          icon: <Loader2 className=\"size-6 animate-spin text-primary\" />,\r\n          text: message || 'Preparing your dashboard...',\r\n          description: 'Setting up your workspace',\r\n        };\r\n      case 'success':\r\n        return {\r\n          icon: <Check className=\"size-6 text-green-600\" />,\r\n          text: message || 'Welcome back!',\r\n          description: 'Login successful',\r\n        };\r\n      default:\r\n        return {\r\n          icon: <Loader2 className=\"size-6 animate-spin text-primary\" />,\r\n          text: message || 'Loading...',\r\n          description: 'Please wait',\r\n        };\r\n    }\r\n  };\r\n\r\n  const config = getStageConfig();\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'flex flex-col items-center justify-center p-8 text-center space-y-4',\r\n        className\r\n      )}\r\n    >\r\n      {/* Animated Icon */}\r\n      <div className=\"relative\">\r\n        <div className=\"absolute inset-0 rounded-full bg-primary/10 animate-ping\" />\r\n        <div className=\"relative flex items-center justify-center w-12 h-12 rounded-full bg-background border border-border/60 shadow-lg\">\r\n          {config.icon}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Loading Text */}\r\n      <div className=\"space-y-2\">\r\n        <p className=\"text-lg font-medium text-foreground\">{config.text}</p>\r\n        <p className=\"text-sm text-muted-foreground\">{config.description}</p>\r\n      </div>\r\n\r\n      {/* Progress Dots */}\r\n      <div className=\"flex space-x-2\">\r\n        {[0, 1, 2].map(index => (\r\n          <div\r\n            key={index}\r\n            className={cn(\r\n              'w-2 h-2 rounded-full bg-primary/30 animate-pulse',\r\n              'transition-all duration-300'\r\n            )}\r\n            style={{\r\n              animationDelay: `${index * 0.2}s`,\r\n              animationDuration: '1.5s',\r\n            }}\r\n          />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AACA;AAAA;;;;AAcO,SAAS,aAAa,EAC3B,SAAS,EACT,QAAQ,gBAAgB,EACxB,OAAO,EACW;IAClB,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,oBAAM,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBACzB,MAAM,WAAW;oBACjB,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,oBAAM,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACxB,MAAM,WAAW;oBACjB,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,oBAAM,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBACzB,MAAM,WAAW;oBACjB,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBACvB,MAAM,WAAW;oBACjB,aAAa;gBACf;YACF;gBACE,OAAO;oBACL,oBAAM,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBACzB,MAAM,WAAW;oBACjB,aAAa;gBACf;QACJ;IACF;IAEA,MAAM,SAAS;IAEf,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,uEACA;;0BAIF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;kCACZ,OAAO,IAAI;;;;;;;;;;;;0BAKhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAuC,OAAO,IAAI;;;;;;kCAC/D,6LAAC;wBAAE,WAAU;kCAAiC,OAAO,WAAW;;;;;;;;;;;;0BAIlE,6LAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAA,sBACb,6LAAC;wBAEC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,oDACA;wBAEF,OAAO;4BACL,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;4BACjC,mBAAmB;wBACrB;uBARK;;;;;;;;;;;;;;;;AAcjB;KAjFgB", "debugId": null}}, {"offset": {"line": 454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/auth/loginForm.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport {\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  ArrowRight,\r\n  Check,\r\n  Eye,\r\n  EyeOff,\r\n  Loader2,\r\n  Lock,\r\n  Mail,\r\n  Shield,\r\n  Wifi,\r\n  WifiOff,\r\n} from 'lucide-react';\r\nimport React, { useEffect, useState } from 'react';\r\n\r\nimport { useAuthContext } from '../../contexts/AuthContext';\r\nimport { useLoginValidation } from '../../hooks/forms/useLoginValidation';\r\nimport { Alert, AlertDescription } from '../ui/alert';\r\nimport { Button } from '../ui/button';\r\nimport { Checkbox } from '../ui/checkbox';\r\nimport { Input } from '../ui/input';\r\nimport { Label } from '../ui/label';\r\nimport { LoginLoading } from '../ui/login-loading';\r\n\r\ninterface LoginFormProps {\r\n  onForgotPassword?: () => void;\r\n  onSignUp?: () => void;\r\n  onSuccess?: () => void;\r\n}\r\n\r\n/**\r\n * Modern Login Form with Advanced Features\r\n *\r\n * A comprehensive login interface featuring:\r\n * - Modern minimalist aesthetics\r\n * - Real-time validation with custom hooks\r\n * - Network status awareness\r\n * - Enhanced accessibility and keyboard navigation\r\n * - Progressive enhancement and error recovery\r\n * - Remember me functionality\r\n * - Advanced loading states with proper state management\r\n */\r\nexport function LoginForm({\r\n  onForgotPassword,\r\n  onSignUp,\r\n  onSuccess,\r\n}: LoginFormProps) {\r\n  const { clearError, error, loading, signIn } = useAuthContext();\r\n  const {\r\n    clearAllErrors,\r\n    clearFieldError,\r\n    errors,\r\n    isFieldValid,\r\n    markFormTouched,\r\n    validateForm,\r\n  } = useLoginValidation();\r\n\r\n  const [formData, setFormData] = useState({\r\n    email: '',\r\n    password: '',\r\n    rememberMe: false,\r\n  });\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [loadingStage, setLoadingStage] = useState<\r\n    'authenticating' | 'redirecting' | 'success' | 'verifying'\r\n  >('authenticating');\r\n  const [isOnline, setIsOnline] = useState(true); // Default to true for SSR\r\n  const [focusedField, setFocusedField] = useState<null | string>(null);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  // Network status monitoring\r\n  useEffect(() => {\r\n    // Check if we're in the browser environment\r\n    if (globalThis.window !== undefined && typeof navigator !== 'undefined') {\r\n      // Set initial online status\r\n      setIsOnline(navigator.onLine);\r\n\r\n      const handleOnline = () => setIsOnline(true);\r\n      const handleOffline = () => setIsOnline(false);\r\n\r\n      globalThis.addEventListener('online', handleOnline);\r\n      globalThis.addEventListener('offline', handleOffline);\r\n\r\n      return () => {\r\n        globalThis.removeEventListener('online', handleOnline);\r\n        globalThis.removeEventListener('offline', handleOffline);\r\n      };\r\n    }\r\n    // Explicitly return an empty cleanup function if not in browser environment\r\n    return () => {};\r\n  }, []);\r\n\r\n  // Load remembered email on component mount\r\n  useEffect(() => {\r\n    // Check if we're in the browser environment\r\n    if (\r\n      globalThis.window !== undefined &&\r\n      typeof localStorage !== 'undefined'\r\n    ) {\r\n      const rememberedEmail = localStorage.getItem('workhub_remember_email');\r\n      if (rememberedEmail) {\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          email: rememberedEmail,\r\n          rememberMe: true,\r\n        }));\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Enhanced form submission with proper loading state management\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Prevent double submission\r\n    if (isSubmitting || loading) {\r\n      return;\r\n    }\r\n\r\n    markFormTouched();\r\n    setIsSubmitting(true);\r\n\r\n    // Clear previous errors\r\n    clearError();\r\n    clearAllErrors();\r\n\r\n    // Validate form\r\n    const validation = validateForm(formData);\r\n    if (!validation.isValid) {\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    // Check network connectivity\r\n    if (!isOnline) {\r\n      setIsSubmitting(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoadingStage('authenticating');\r\n\r\n      const { error } = await signIn(formData.email, formData.password);\r\n\r\n      if (error) {\r\n        setIsSubmitting(false);\r\n      } else {\r\n        setLoadingStage('success');\r\n\r\n        // Store remember me preference\r\n        if (\r\n          globalThis.window !== undefined &&\r\n          typeof localStorage !== 'undefined'\r\n        ) {\r\n          if (formData.rememberMe) {\r\n            localStorage.setItem('workhub_remember_email', formData.email);\r\n          } else {\r\n            localStorage.removeItem('workhub_remember_email');\r\n          }\r\n        }\r\n\r\n        // Brief success state before redirect - prevent double loading\r\n        setTimeout(() => {\r\n          setIsSubmitting(false);\r\n          onSuccess?.();\r\n        }, 800);\r\n      }\r\n    } catch (error_) {\r\n      console.error('Login error:', error_);\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Enhanced input handling with validation\r\n  const handleInputChange = (field: string, value: boolean | string) => {\r\n    setFormData(prev => ({ ...prev, [field]: value }));\r\n\r\n    // Clear field error when user starts typing\r\n    if (typeof value === 'string' && errors[field as keyof typeof errors]) {\r\n      clearFieldError(field as keyof typeof errors);\r\n    }\r\n\r\n    // Clear global error when user starts typing\r\n    if (error) {\r\n      clearError();\r\n    }\r\n  };\r\n\r\n  // Handle field focus for enhanced UX\r\n  const handleFieldFocus = (fieldName: string) => {\r\n    setFocusedField(fieldName);\r\n  };\r\n\r\n  const handleFieldBlur = () => {\r\n    setFocusedField(null);\r\n  };\r\n\r\n  // Show loading overlay during authentication - fix double loading\r\n  if (isSubmitting) {\r\n    return (\r\n      <div className=\"flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 p-4\">\r\n        <div className=\"w-full max-w-md\">\r\n          <div className=\"rounded-2xl border border-border/60 bg-card shadow-xl backdrop-blur-sm\">\r\n            <LoginLoading stage={loadingStage} />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 p-4\">\r\n      <div className=\"w-full max-w-md\">\r\n        {/* Network Status Indicator */}\r\n        {!isOnline && (\r\n          <div className=\"mb-4 flex items-center gap-2 rounded-xl border border-destructive/20 bg-destructive/10 p-3 text-destructive\">\r\n            <WifiOff className=\"size-4\" />\r\n            <span className=\"text-sm font-medium\">No internet connection</span>\r\n          </div>\r\n        )}\r\n\r\n        {/* Header Section */}\r\n        <div className=\"mb-8 text-center\">\r\n          {/* Logo with enhanced animation */}\r\n          <div className=\"group mx-auto mb-6 flex size-16 items-center justify-center rounded-2xl bg-gradient-to-r from-primary to-accent shadow-lg ring-1 ring-primary/20 transition-all duration-300 hover:ring-primary/40\">\r\n            <Lock className=\"size-8 text-primary-foreground transition-transform group-hover:scale-110\" />\r\n          </div>\r\n\r\n          {/* Enhanced title with connection status */}\r\n          <h1 className=\"mb-2 text-3xl font-bold tracking-tight text-foreground\">\r\n            Welcome back\r\n          </h1>\r\n          <div className=\"flex items-center justify-center gap-2 text-muted-foreground\">\r\n            <span>Sign in to your WorkHub account</span>\r\n            {isOnline && <Wifi className=\"size-4 text-green-600\" />}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Main Form Card */}\r\n        <div className=\"rounded-2xl border border-border/60 bg-card p-8 shadow-xl backdrop-blur-sm\">\r\n          {/* Global Error Alert */}\r\n          {error && (\r\n            <Alert\r\n              className=\"mb-6 border-destructive/20 bg-destructive/5\"\r\n              variant=\"destructive\"\r\n            >\r\n              <AlertCircle className=\"size-4\" />\r\n              <AlertDescription className=\"text-destructive\">\r\n                {error}\r\n              </AlertDescription>\r\n            </Alert>\r\n          )}\r\n\r\n          {/* Offline Warning */}\r\n          {!isOnline && (\r\n            <Alert className=\"mb-6 border-yellow-200 bg-yellow-50\">\r\n              <WifiOff className=\"size-4\" />\r\n              <AlertDescription>\r\n                You're currently offline. Please check your internet connection\r\n                to sign in.\r\n              </AlertDescription>\r\n            </Alert>\r\n          )}\r\n\r\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\r\n            {/* Email Field with Enhanced UX */}\r\n            <div className=\"space-y-2\">\r\n              <Label\r\n                className={`text-sm font-medium transition-colors ${\r\n                  focusedField === 'email' ? 'text-primary' : 'text-foreground'\r\n                }`}\r\n                htmlFor=\"email\"\r\n              >\r\n                Email address\r\n              </Label>\r\n              <div className=\"group relative\">\r\n                <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\r\n                  <Mail\r\n                    className={`size-5 transition-colors ${\r\n                      focusedField === 'email'\r\n                        ? 'text-primary'\r\n                        : 'text-muted-foreground'\r\n                    }`}\r\n                  />\r\n                </div>\r\n                <Input\r\n                  autoComplete=\"email\"\r\n                  className={`h-12 pl-10 transition-all duration-200 ${\r\n                    errors.email\r\n                      ? 'border-destructive/50 focus-visible:ring-destructive/20'\r\n                      : 'hover:border-primary/30 focus-visible:ring-primary/20'\r\n                  } ${\r\n                    isFieldValid('email', formData.email)\r\n                      ? 'border-green-500/50 focus-visible:ring-green-500/20'\r\n                      : ''\r\n                  }`}\r\n                  disabled={loading || !isOnline || isSubmitting}\r\n                  id=\"email\"\r\n                  onBlur={handleFieldBlur}\r\n                  onChange={e => handleInputChange('email', e.target.value)}\r\n                  onFocus={() => handleFieldFocus('email')}\r\n                  placeholder=\"Enter your email\"\r\n                  type=\"email\"\r\n                  value={formData.email}\r\n                />\r\n                {/* Enhanced success indicator */}\r\n                {isFieldValid('email', formData.email) && (\r\n                  <div className=\"absolute inset-y-0 right-0 flex items-center pr-3\">\r\n                    <Check className=\"size-5 text-green-500 duration-200 animate-in fade-in-0 zoom-in-95\" />\r\n                  </div>\r\n                )}\r\n              </div>\r\n              {errors.email && (\r\n                <p className=\"mt-1 flex items-center gap-2 text-sm text-destructive duration-200 animate-in slide-in-from-top-1\">\r\n                  <AlertCircle className=\"size-4\" />\r\n                  {errors.email}\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            {/* Password Field with Enhanced UX */}\r\n            <div className=\"space-y-2\">\r\n              <Label\r\n                className={`text-sm font-medium transition-colors ${\r\n                  focusedField === 'password'\r\n                    ? 'text-primary'\r\n                    : 'text-foreground'\r\n                }`}\r\n                htmlFor=\"password\"\r\n              >\r\n                Password\r\n              </Label>\r\n              <div className=\"group relative\">\r\n                <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\r\n                  <Lock\r\n                    className={`size-5 transition-colors ${\r\n                      focusedField === 'password'\r\n                        ? 'text-primary'\r\n                        : 'text-muted-foreground'\r\n                    }`}\r\n                  />\r\n                </div>\r\n                <Input\r\n                  autoComplete=\"current-password\"\r\n                  className={`h-12 pl-10 pr-12 transition-all duration-200 ${\r\n                    errors.password\r\n                      ? 'border-destructive/50 focus-visible:ring-destructive/20'\r\n                      : 'hover:border-primary/30 focus-visible:ring-primary/20'\r\n                  } ${\r\n                    isFieldValid('password', formData.password)\r\n                      ? 'border-green-500/50 focus-visible:ring-green-500/20'\r\n                      : ''\r\n                  }`}\r\n                  disabled={loading || !isOnline || isSubmitting}\r\n                  id=\"password\"\r\n                  onBlur={handleFieldBlur}\r\n                  onChange={e => handleInputChange('password', e.target.value)}\r\n                  onFocus={() => handleFieldFocus('password')}\r\n                  placeholder=\"Enter your password\"\r\n                  type={showPassword ? 'text' : 'password'}\r\n                  value={formData.password}\r\n                />\r\n                {/* Password visibility toggle */}\r\n                <button\r\n                  aria-label={showPassword ? 'Hide password' : 'Show password'}\r\n                  className=\"absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground transition-colors hover:text-foreground\"\r\n                  disabled={loading || isSubmitting}\r\n                  onClick={() => setShowPassword(!showPassword)}\r\n                  type=\"button\"\r\n                >\r\n                  {showPassword ? (\r\n                    <EyeOff className=\"size-5\" />\r\n                  ) : (\r\n                    <Eye className=\"size-5\" />\r\n                  )}\r\n                </button>\r\n              </div>\r\n              {errors.password && (\r\n                <p className=\"mt-1 flex items-center gap-2 text-sm text-destructive duration-200 animate-in slide-in-from-top-1\">\r\n                  <AlertCircle className=\"size-4\" />\r\n                  {errors.password}\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            {/* Remember Me & Forgot Password */}\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Checkbox\r\n                  checked={formData.rememberMe}\r\n                  className=\"data-[state=checked]:border-primary data-[state=checked]:bg-primary\"\r\n                  disabled={loading || isSubmitting}\r\n                  id=\"remember-me\"\r\n                  onCheckedChange={checked =>\r\n                    handleInputChange('rememberMe', checked)\r\n                  }\r\n                />\r\n                <Label\r\n                  className=\"cursor-pointer text-sm text-muted-foreground transition-colors hover:text-foreground\"\r\n                  htmlFor=\"remember-me\"\r\n                >\r\n                  Remember me\r\n                </Label>\r\n              </div>\r\n\r\n              {onForgotPassword && (\r\n                <button\r\n                  className=\"text-sm font-medium text-primary transition-colors hover:text-primary/80\"\r\n                  disabled={loading || isSubmitting}\r\n                  onClick={onForgotPassword}\r\n                  type=\"button\"\r\n                >\r\n                  Forgot password?\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Submit Button */}\r\n            <Button\r\n              className=\"group h-12 w-full rounded-xl bg-gradient-to-r from-primary to-accent font-semibold text-primary-foreground shadow-lg transition-all duration-200 hover:from-primary/90 hover:to-accent/90 hover:shadow-xl disabled:opacity-50\"\r\n              disabled={loading || !isOnline || isSubmitting}\r\n              type=\"submit\"\r\n            >\r\n              {loading || isSubmitting ? (\r\n                <>\r\n                  <Loader2 className=\"mr-2 size-5 animate-spin\" />\r\n                  Signing in...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  Sign in\r\n                  <ArrowRight className=\"ml-2 size-5 transition-transform group-hover:translate-x-1\" />\r\n                </>\r\n              )}\r\n            </Button>\r\n\r\n            {/* Demo Credentials */}\r\n            <div className=\"rounded-xl border border-border/40 bg-muted/30 p-4\">\r\n              <div className=\"mb-2 flex items-center gap-2\">\r\n                <Shield className=\"size-4 text-muted-foreground\" />\r\n                <span className=\"text-sm font-medium text-muted-foreground\">\r\n                  Demo Access\r\n                </span>\r\n              </div>\r\n              <div className=\"space-y-1 text-xs text-muted-foreground\">\r\n                <div className=\"font-mono\"><EMAIL></div>\r\n                <div className=\"font-mono\">demo123</div>\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </div>\r\n\r\n        {/* Sign Up Link */}\r\n        {onSignUp && (\r\n          <div className=\"mt-6 text-center\">\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              Don't have an account?{' '}\r\n              <button\r\n                className=\"font-medium text-primary transition-colors hover:text-primary/80\"\r\n                disabled={loading || isSubmitting}\r\n                onClick={onSignUp}\r\n              >\r\n                Create one now\r\n              </button>\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Enhanced Security Notice */}\r\n        <div className=\"mt-8 text-center\">\r\n          <div className=\"inline-flex items-center gap-2 rounded-full border border-border/40 bg-card px-4 py-2 text-xs text-muted-foreground\">\r\n            <Shield className=\"size-4 text-green-600\" />\r\n            <span>Protected by enterprise-grade security</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <footer className=\"mt-8 text-center text-xs text-muted-foreground\">\r\n          <p>© 2024 WorkHub. All rights reserved.</p>\r\n          <div className=\"mt-2 space-x-4\">\r\n            <a className=\"transition-colors hover:text-foreground\" href=\"#\">\r\n              Terms\r\n            </a>\r\n            <a className=\"transition-colors hover:text-foreground\" href=\"#\">\r\n              Privacy\r\n            </a>\r\n            <a className=\"transition-colors hover:text-foreground\" href=\"#\">\r\n              Support\r\n            </a>\r\n          </div>\r\n        </footer>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAxBA;;;;;;;;;;;AA4CO,SAAS,UAAU,EACxB,gBAAgB,EAChB,QAAQ,EACR,SAAS,EACM;;IACf,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAC5D,MAAM,EACJ,cAAc,EACd,eAAe,EACf,MAAM,EACN,YAAY,EACZ,eAAe,EACf,YAAY,EACb,GAAG,CAAA,GAAA,8IAAA,CAAA,qBAAkB,AAAD;IAErB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,YAAY;IACd;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE7C;IACF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,0BAA0B;IAC1E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,4CAA4C;YAC5C,IAAI,WAAW,MAAM,KAAK,aAAa,OAAO,cAAc,aAAa;gBACvE,4BAA4B;gBAC5B,YAAY,UAAU,MAAM;gBAE5B,MAAM;wDAAe,IAAM,YAAY;;gBACvC,MAAM;yDAAgB,IAAM,YAAY;;gBAExC,WAAW,gBAAgB,CAAC,UAAU;gBACtC,WAAW,gBAAgB,CAAC,WAAW;gBAEvC;2CAAO;wBACL,WAAW,mBAAmB,CAAC,UAAU;wBACzC,WAAW,mBAAmB,CAAC,WAAW;oBAC5C;;YACF;YACA,4EAA4E;YAC5E;uCAAO,KAAO;;QAChB;8BAAG,EAAE;IAEL,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,4CAA4C;YAC5C,IACE,WAAW,MAAM,KAAK,aACtB,OAAO,iBAAiB,aACxB;gBACA,MAAM,kBAAkB,aAAa,OAAO,CAAC;gBAC7C,IAAI,iBAAiB;oBACnB;+CAAY,CAAA,OAAQ,CAAC;gCACnB,GAAG,IAAI;gCACP,OAAO;gCACP,YAAY;4BACd,CAAC;;gBACH;YACF;QACF;8BAAG,EAAE;IAEL,gEAAgE;IAChE,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,4BAA4B;QAC5B,IAAI,gBAAgB,SAAS;YAC3B;QACF;QAEA;QACA,gBAAgB;QAEhB,wBAAwB;QACxB;QACA;QAEA,gBAAgB;QAChB,MAAM,aAAa,aAAa;QAChC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,gBAAgB;YAChB;QACF;QAEA,6BAA6B;QAC7B,IAAI,CAAC,UAAU;YACb,gBAAgB;YAChB;QACF;QAEA,IAAI;YACF,gBAAgB;YAEhB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,SAAS,KAAK,EAAE,SAAS,QAAQ;YAEhE,IAAI,OAAO;gBACT,gBAAgB;YAClB,OAAO;gBACL,gBAAgB;gBAEhB,+BAA+B;gBAC/B,IACE,WAAW,MAAM,KAAK,aACtB,OAAO,iBAAiB,aACxB;oBACA,IAAI,SAAS,UAAU,EAAE;wBACvB,aAAa,OAAO,CAAC,0BAA0B,SAAS,KAAK;oBAC/D,OAAO;wBACL,aAAa,UAAU,CAAC;oBAC1B;gBACF;gBAEA,+DAA+D;gBAC/D,WAAW;oBACT,gBAAgB;oBAChB;gBACF,GAAG;YACL;QACF,EAAE,OAAO,QAAQ;YACf,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,gBAAgB;QAClB;IACF;IAEA,0CAA0C;IAC1C,MAAM,oBAAoB,CAAC,OAAe;QACxC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAEhD,4CAA4C;QAC5C,IAAI,OAAO,UAAU,YAAY,MAAM,CAAC,MAA6B,EAAE;YACrE,gBAAgB;QAClB;QAEA,6CAA6C;QAC7C,IAAI,OAAO;YACT;QACF;IACF;IAEA,qCAAqC;IACrC,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;IAClB;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;IAClB;IAEA,kEAAkE;IAClE,IAAI,cAAc;QAChB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+IAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;;;;;;;;;;;;;;;;IAK/B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;gBAEZ,CAAC,0BACA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;4BAAK,WAAU;sCAAsB;;;;;;;;;;;;8BAK1C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAIlB,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;gCACL,0BAAY,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;8BAKjC,6LAAC;oBAAI,WAAU;;wBAEZ,uBACC,6LAAC,oIAAA,CAAA,QAAK;4BACJ,WAAU;4BACV,SAAQ;;8CAER,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,mBAAgB;oCAAC,WAAU;8CACzB;;;;;;;;;;;;wBAMN,CAAC,0BACA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,6LAAC,+MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC,oIAAA,CAAA,mBAAgB;8CAAC;;;;;;;;;;;;sCAOtB,6LAAC;4BAAK,WAAU;4BAAY,UAAU;;8CAEpC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,WAAW,CAAC,sCAAsC,EAChD,iBAAiB,UAAU,iBAAiB,mBAC5C;4CACF,SAAQ;sDACT;;;;;;sDAGD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDACH,WAAW,CAAC,yBAAyB,EACnC,iBAAiB,UACb,iBACA,yBACJ;;;;;;;;;;;8DAGN,6LAAC,oIAAA,CAAA,QAAK;oDACJ,cAAa;oDACb,WAAW,CAAC,uCAAuC,EACjD,OAAO,KAAK,GACR,4DACA,wDACL,CAAC,EACA,aAAa,SAAS,SAAS,KAAK,IAChC,wDACA,IACJ;oDACF,UAAU,WAAW,CAAC,YAAY;oDAClC,IAAG;oDACH,QAAQ;oDACR,UAAU,CAAA,IAAK,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxD,SAAS,IAAM,iBAAiB;oDAChC,aAAY;oDACZ,MAAK;oDACL,OAAO,SAAS,KAAK;;;;;;gDAGtB,aAAa,SAAS,SAAS,KAAK,mBACnC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;wCAItB,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;;8DACX,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDACtB,OAAO,KAAK;;;;;;;;;;;;;8CAMnB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,WAAW,CAAC,sCAAsC,EAChD,iBAAiB,aACb,iBACA,mBACJ;4CACF,SAAQ;sDACT;;;;;;sDAGD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDACH,WAAW,CAAC,yBAAyB,EACnC,iBAAiB,aACb,iBACA,yBACJ;;;;;;;;;;;8DAGN,6LAAC,oIAAA,CAAA,QAAK;oDACJ,cAAa;oDACb,WAAW,CAAC,6CAA6C,EACvD,OAAO,QAAQ,GACX,4DACA,wDACL,CAAC,EACA,aAAa,YAAY,SAAS,QAAQ,IACtC,wDACA,IACJ;oDACF,UAAU,WAAW,CAAC,YAAY;oDAClC,IAAG;oDACH,QAAQ;oDACR,UAAU,CAAA,IAAK,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3D,SAAS,IAAM,iBAAiB;oDAChC,aAAY;oDACZ,MAAM,eAAe,SAAS;oDAC9B,OAAO,SAAS,QAAQ;;;;;;8DAG1B,6LAAC;oDACC,cAAY,eAAe,kBAAkB;oDAC7C,WAAU;oDACV,UAAU,WAAW;oDACrB,SAAS,IAAM,gBAAgB,CAAC;oDAChC,MAAK;8DAEJ,6BACC,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAElB,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;wCAIpB,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;;8DACX,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDACtB,OAAO,QAAQ;;;;;;;;;;;;;8CAMtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uIAAA,CAAA,WAAQ;oDACP,SAAS,SAAS,UAAU;oDAC5B,WAAU;oDACV,UAAU,WAAW;oDACrB,IAAG;oDACH,iBAAiB,CAAA,UACf,kBAAkB,cAAc;;;;;;8DAGpC,6LAAC,oIAAA,CAAA,QAAK;oDACJ,WAAU;oDACV,SAAQ;8DACT;;;;;;;;;;;;wCAKF,kCACC,6LAAC;4CACC,WAAU;4CACV,UAAU,WAAW;4CACrB,SAAS;4CACT,MAAK;sDACN;;;;;;;;;;;;8CAOL,6LAAC,qIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,UAAU,WAAW,CAAC,YAAY;oCAClC,MAAK;8CAEJ,WAAW,6BACV;;0DACE,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA6B;;qEAIlD;;4CAAE;0DAEA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;8CAM5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAY;;;;;;8DAC3B,6LAAC;oDAAI,WAAU;8DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAOlC,0BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAgC;4BACpB;0CACvB,6LAAC;gCACC,WAAU;gCACV,UAAU,WAAW;gCACrB,SAAS;0CACV;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAKV,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;sCAAE;;;;;;sCACH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;oCAA0C,MAAK;8CAAI;;;;;;8CAGhE,6LAAC;oCAAE,WAAU;oCAA0C,MAAK;8CAAI;;;;;;8CAGhE,6LAAC;oCAAE,WAAU;oCAA0C,MAAK;8CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5E;GApcgB;;QAKiC,kIAAA,CAAA,iBAAc;QAQzD,8IAAA,CAAA,qBAAkB;;;KAbR", "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    className={cn(\r\n      'rounded-lg border bg-card text-card-foreground shadow-sm',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nCard.displayName = 'Card';\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nCardHeader.displayName = 'CardHeader';\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    className={cn(\r\n      'text-2xl font-semibold leading-none tracking-tight',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nCardTitle.displayName = 'CardTitle';\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    className={cn('text-sm text-muted-foreground', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nCardDescription.displayName = 'CardDescription';\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className={cn('p-6 pt-0', className)} ref={ref} {...props} />\r\n));\r\nCardContent.displayName = 'CardContent';\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    className={cn('flex items-center p-6 pt-0', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nCardFooter.displayName = 'CardFooter';\r\n\r\nexport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;AAAA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAY,KAAK;QAAM,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC5C,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ReactNode } from 'react';\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON>gle, Loader2, Shield } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport { useAuthContext } from '../../contexts/AuthContext';\r\nimport { Alert, AlertDescription } from '../ui/alert';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '../ui/card';\r\nimport { LoginForm } from './loginForm';\r\n\r\ninterface ProtectedRouteProps {\r\n  allowedRoles?: string[];\r\n  children: ReactNode;\r\n  fallback?: ReactNode;\r\n  requireEmailVerification?: boolean;\r\n}\r\n\r\n/**\r\n * Protected Route Component\r\n *\r\n * This component protects routes by requiring authentication and\r\n * optionally enforces email verification and role-based access.\r\n * It displays appropriate fallback UI for unauthenticated or unauthorized users.\r\n */\r\nexport function ProtectedRoute({\r\n  allowedRoles = [],\r\n  children,\r\n  fallback,\r\n  requireEmailVerification = true,\r\n}: ProtectedRouteProps) {\r\n  const { error, loading, session, user, userRole } = useAuthContext();\r\n\r\n  // Show loading state while checking authentication\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex min-h-screen items-center justify-center bg-gray-50\">\r\n        <Card className=\"mx-auto w-full max-w-md\">\r\n          <CardContent className=\"flex flex-col items-center justify-center py-8\">\r\n            <Loader2 className=\"mb-4 size-8 animate-spin text-blue-600\" />\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              Verifying security credentials...\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state if there's an authentication error and no user\r\n  if (error && !user) {\r\n    return (\r\n      <div className=\"flex min-h-screen items-center justify-center bg-gray-50 p-4\">\r\n        <Card className=\"mx-auto w-full max-w-md\">\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center text-red-600\">\r\n              <AlertTriangle className=\"mr-2 size-5\" />\r\n              Authentication Error\r\n            </CardTitle>\r\n            <CardDescription>\r\n              There was a problem with the security system\r\n            </CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <Alert variant=\"destructive\">\r\n              <AlertDescription>{error}</AlertDescription>\r\n            </Alert>\r\n            <div className=\"mt-4\">\r\n              <LoginForm />\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show login form if user is not authenticated\r\n  if (!user || !session) {\r\n    if (fallback) {\r\n      return <>{fallback}</>;\r\n    }\r\n\r\n    return (\r\n      <LoginForm\r\n        onSuccess={() => {\r\n          // Redirect to main page after successful login\r\n          globalThis.location.href = '/';\r\n        }}\r\n      />\r\n    );\r\n  }\r\n\r\n  // Check email verification if required\r\n  if (requireEmailVerification && !user.email_confirmed_at) {\r\n    return (\r\n      <div className=\"flex min-h-screen items-center justify-center bg-gray-50 p-4\">\r\n        <Card className=\"mx-auto w-full max-w-md\">\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center text-yellow-600\">\r\n              <Shield className=\"mr-2 size-5\" />\r\n              Email Verification Required\r\n            </CardTitle>\r\n            <CardDescription>\r\n              Please verify your email address to continue\r\n            </CardDescription>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <Alert>\r\n              <AlertTriangle className=\"size-4\" />\r\n              <AlertDescription>\r\n                We've sent a verification email to <strong>{user.email}</strong>\r\n                . Please check your inbox and click the verification link to\r\n                access the system.\r\n              </AlertDescription>\r\n            </Alert>\r\n            <div className=\"mt-4 text-center\">\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                Didn't receive the email? Check your spam folder or contact your\r\n                administrator.\r\n              </p>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Check role-based access if roles are specified\r\n  if (allowedRoles.length > 0) {\r\n    // Use the userRole from useAuthContext directly\r\n    const currentUserRole = userRole || 'USER'; // Default to 'USER' if role is not set\r\n\r\n    if (!allowedRoles.includes(currentUserRole)) {\r\n      return (\r\n        <div className=\"flex min-h-screen items-center justify-center bg-gray-50 p-4\">\r\n          <Card className=\"mx-auto w-full max-w-md\">\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center text-red-600\">\r\n                <Shield className=\"mr-2 size-5\" />\r\n                Access Denied\r\n              </CardTitle>\r\n              <CardDescription>\r\n                Insufficient permissions to access this resource\r\n              </CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <Alert variant=\"destructive\">\r\n                <AlertDescription>\r\n                  Your account ({currentUserRole}) does not have permission to\r\n                  access this area. Required roles: {allowedRoles.join(', ')}\r\n                </AlertDescription>\r\n              </Alert>\r\n              <div className=\"mt-4 text-center\">\r\n                <p className=\"text-sm text-muted-foreground\">\r\n                  Contact your administrator if you believe this is an error.\r\n                </p>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      );\r\n    }\r\n  }\r\n\r\n  // User is authenticated and authorized - render children\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAGA;AACA;AACA;AAOA;;;AAhBA;;;;;;AAgCO,SAAS,eAAe,EAC7B,eAAe,EAAE,EACjB,QAAQ,EACR,QAAQ,EACR,2BAA2B,IAAI,EACX;;IACpB,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IAEjE,mDAAmD;IACnD,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;;;;;;IAOvD;IAEA,kEAAkE;IAClE,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAgB;;;;;;;0CAG3C,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;0CACb,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;0CAErB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,0IAAA,CAAA,YAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMtB;IAEA,+CAA+C;IAC/C,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,IAAI,UAAU;YACZ,qBAAO;0BAAG;;QACZ;QAEA,qBACE,6LAAC,0IAAA,CAAA,YAAS;YACR,WAAW;gBACT,+CAA+C;gBAC/C,WAAW,QAAQ,CAAC,IAAI,GAAG;YAC7B;;;;;;IAGN;IAEA,uCAAuC;IACvC,IAAI,4BAA4B,CAAC,KAAK,kBAAkB,EAAE;QACxD,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAgB;;;;;;;0CAGpC,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC,oIAAA,CAAA,QAAK;;kDACJ,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC,oIAAA,CAAA,mBAAgB;;4CAAC;0DACmB,6LAAC;0DAAQ,KAAK,KAAK;;;;;;4CAAU;;;;;;;;;;;;;0CAKpE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASzD;IAEA,iDAAiD;IACjD,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,gDAAgD;QAChD,MAAM,kBAAkB,YAAY,QAAQ,uCAAuC;QAEnF,IAAI,CAAC,aAAa,QAAQ,CAAC,kBAAkB;YAC3C,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;;8CACT,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAgB;;;;;;;8CAGpC,6LAAC,mIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,6LAAC,mIAAA,CAAA,cAAW;;8CACV,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CACb,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;;4CAAC;4CACD;4CAAgB;4CACI,aAAa,IAAI,CAAC;;;;;;;;;;;;8CAGzD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAQzD;IACF;IAEA,yDAAyD;IACzD,qBAAO;kBAAG;;AACZ;GA7IgB;;QAMsC,kIAAA,CAAA,iBAAc;;;KANpD", "debugId": null}}, {"offset": {"line": 1763, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/auth/index.ts"], "sourcesContent": ["/**\r\n * EMERGENCY SECURITY COMPONENTS - Authentication Module\r\n *\r\n * This module exports all authentication-related components\r\n * for the emergency security implementation.\r\n *\r\n * CRITICAL: These components are part of the emergency security implementation\r\n */\r\n\r\n// Re-export auth context for convenience\r\nexport { AuthProvider, useAuthContext } from '../../contexts/AuthContext';\r\n\r\nexport { LoginForm } from './loginForm';\r\nexport { ProtectedRoute } from './ProtectedRoute';\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,yCAAyC;;AACzC;AAEA;AACA", "debugId": null}}, {"offset": {"line": 1798, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/layout/AppSidebar.tsx"], "sourcesContent": ["/**\r\n * @file Modern sidebar navigation component for the expanding WorkHub application\r\n * @module components/layout/AppSidebar\r\n */\r\n\r\n'use client';\r\n\r\nimport {\r\n  Settings as AdminIcon,\r\n  BarChart3,\r\n  Briefcase,\r\n  ChevronDown,\r\n  ChevronRight,\r\n  ClipboardList,\r\n  Users as EmployeesIcon,\r\n  FolderOpen,\r\n  History,\r\n  Home,\r\n  Layers,\r\n  Monitor,\r\n  Car as MyVehiclesIcon,\r\n  Search,\r\n  Shield,\r\n  Truck,\r\n  Wrench,\r\n  Building2 as WorkHubLogoIcon,\r\n} from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport React, { useState } from 'react';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface NavItem {\r\n  href: string;\r\n  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;\r\n  label: string;\r\n  badge?: string;\r\n  isNew?: boolean;\r\n}\r\n\r\ninterface NavSection {\r\n  title: string;\r\n  items: NavItem[];\r\n  defaultOpen?: boolean;\r\n}\r\n\r\ninterface AppSidebarProps {\r\n  className?: string;\r\n  collapsed?: boolean;\r\n  onToggle?: () => void;\r\n}\r\n\r\nconst navigationSections: NavSection[] = [\r\n  {\r\n    title: 'Overview',\r\n    defaultOpen: true,\r\n    items: [\r\n      { href: '/', icon: Home, label: 'Dashboard' },\r\n      { href: '/reports', icon: BarChart3, label: 'Analytics', isNew: true },\r\n    ],\r\n  },\r\n  {\r\n    title: 'Operations',\r\n    defaultOpen: true,\r\n    items: [\r\n      { href: '/vehicles', icon: MyVehiclesIcon, label: 'Fleet' },\r\n      { href: '/service-history', icon: History, label: 'Maintenance' },\r\n      { href: '/service-records', icon: Wrench, label: 'Service Records' },\r\n    ],\r\n  },\r\n  {\r\n    title: 'Workforce',\r\n    defaultOpen: false,\r\n    items: [\r\n      { href: '/employees', icon: EmployeesIcon, label: 'Team Members' },\r\n      { href: '/delegations', icon: Briefcase, label: 'Projects' },\r\n      { href: '/tasks', icon: ClipboardList, label: 'Tasks' },\r\n    ],\r\n  },\r\n  {\r\n    title: 'System',\r\n    defaultOpen: false,\r\n    items: [\r\n      { href: '/reliability', icon: Monitor, label: 'Monitoring' },\r\n      { href: '/admin', icon: AdminIcon, label: 'Administration' },\r\n    ],\r\n  },\r\n];\r\n\r\nexport const AppSidebar: React.FC<AppSidebarProps> = ({\r\n  className,\r\n  collapsed = false,\r\n}) => {\r\n  const pathname = usePathname();\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [expandedSections, setExpandedSections] = useState<Set<string>>(\r\n    new Set(navigationSections.filter(s => s.defaultOpen).map(s => s.title))\r\n  );\r\n\r\n  const toggleSection = (title: string) => {\r\n    const newExpanded = new Set(expandedSections);\r\n    if (newExpanded.has(title)) {\r\n      newExpanded.delete(title);\r\n    } else {\r\n      newExpanded.add(title);\r\n    }\r\n    setExpandedSections(newExpanded);\r\n  };\r\n\r\n  const filteredSections = navigationSections\r\n    .map(section => ({\r\n      ...section,\r\n      items: section.items.filter(item =>\r\n        item.label.toLowerCase().includes(searchQuery.toLowerCase())\r\n      ),\r\n    }))\r\n    .filter(section => section.items.length > 0);\r\n\r\n  const isActive = (href: string) => {\r\n    if (href === '/') {\r\n      return pathname === '/';\r\n    }\r\n    return pathname?.startsWith(href);\r\n  };\r\n\r\n  if (collapsed) {\r\n    return (\r\n      <aside\r\n        className={cn('flex w-16 flex-col border-r bg-background', className)}\r\n      >\r\n        <div className=\"flex h-16 items-center justify-center border-b\">\r\n          <Link href=\"/\" className=\"flex items-center\">\r\n            <WorkHubLogoIcon className=\"size-6 text-primary\" />\r\n          </Link>\r\n        </div>\r\n        <div className=\"flex-1 overflow-y-auto\">\r\n          <div className=\"space-y-2 p-2\">\r\n            {navigationSections\r\n              .flatMap(section => section.items)\r\n              .map(item => (\r\n                <Button\r\n                  key={item.href}\r\n                  asChild\r\n                  variant={isActive(item.href) ? 'secondary' : 'ghost'}\r\n                  size=\"icon\"\r\n                  className=\"size-12\"\r\n                >\r\n                  <Link href={item.href} title={item.label}>\r\n                    <item.icon className=\"size-5\" />\r\n                  </Link>\r\n                </Button>\r\n              ))}\r\n          </div>\r\n        </div>\r\n      </aside>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <aside\r\n      className={cn('flex w-64 flex-col border-r bg-background', className)}\r\n    >\r\n      {/* Header */}\r\n      <div className=\"flex h-16 items-center border-b px-6\">\r\n        <Link\r\n          href=\"/\"\r\n          className=\"flex items-center space-x-2 text-lg font-semibold transition-opacity hover:opacity-80\"\r\n        >\r\n          <WorkHubLogoIcon className=\"size-6 text-primary\" />\r\n          <span>WorkHub</span>\r\n        </Link>\r\n      </div>\r\n\r\n      {/* Search */}\r\n      <div className=\"p-4\">\r\n        <div className=\"relative\">\r\n          <Search className=\"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground\" />\r\n          <Input\r\n            placeholder=\"Search navigation...\"\r\n            value={searchQuery}\r\n            onChange={e => setSearchQuery(e.target.value)}\r\n            className=\"pl-9\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Navigation - Remove ScrollArea to prevent double scrollbar */}\r\n      <div className=\"flex-1 overflow-y-auto\">\r\n        <div className=\"space-y-2 p-4\">\r\n          {filteredSections.map(section => {\r\n            const isExpanded = expandedSections.has(section.title);\r\n            const hasVisibleItems = section.items.length > 0;\r\n\r\n            if (!hasVisibleItems) return null;\r\n\r\n            return (\r\n              <div key={section.title} className=\"space-y-1\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  className=\"w-full justify-between px-2 py-1.5 text-xs font-medium uppercase text-muted-foreground hover:text-foreground\"\r\n                  onClick={() => toggleSection(section.title)}\r\n                >\r\n                  <span>{section.title}</span>\r\n                  {isExpanded ? (\r\n                    <ChevronDown className=\"size-3\" />\r\n                  ) : (\r\n                    <ChevronRight className=\"size-3\" />\r\n                  )}\r\n                </Button>\r\n\r\n                {isExpanded && (\r\n                  <div className=\"ml-2 space-y-1\">\r\n                    {section.items.map(item => (\r\n                      <Button\r\n                        key={item.href}\r\n                        asChild\r\n                        variant={isActive(item.href) ? 'secondary' : 'ghost'}\r\n                        className=\"w-full justify-start px-3 py-2\"\r\n                      >\r\n                        <Link\r\n                          href={item.href}\r\n                          className=\"flex items-center space-x-3\"\r\n                        >\r\n                          <item.icon className=\"size-4\" />\r\n                          <span className=\"flex-1\">{item.label}</span>\r\n                          {item.isNew && (\r\n                            <span className=\"rounded bg-primary px-1.5 py-0.5 text-xs text-primary-foreground\">\r\n                              New\r\n                            </span>\r\n                          )}\r\n                          {item.badge && (\r\n                            <span className=\"rounded bg-muted px-1.5 py-0.5 text-xs\">\r\n                              {item.badge}\r\n                            </span>\r\n                          )}\r\n                        </Link>\r\n                      </Button>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Footer */}\r\n      <div className=\"border-t p-4\">\r\n        <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\r\n          <span>v2.1.0</span>\r\n          <Link href=\"/settings\" className=\"hover:text-foreground\">\r\n            Settings\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </aside>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBA;AACA;AACA;AAEA;AACA;AAEA;AAAA;;;AA7BA;;;;;;;;AAmDA,MAAM,qBAAmC;IACvC;QACE,OAAO;QACP,aAAa;QACb,OAAO;YACL;gBAAE,MAAM;gBAAK,MAAM,sMAAA,CAAA,OAAI;gBAAE,OAAO;YAAY;YAC5C;gBAAE,MAAM;gBAAY,MAAM,qNAAA,CAAA,YAAS;gBAAE,OAAO;gBAAa,OAAO;YAAK;SACtE;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,OAAO;YACL;gBAAE,MAAM;gBAAa,MAAM,mMAAA,CAAA,MAAc;gBAAE,OAAO;YAAQ;YAC1D;gBAAE,MAAM;gBAAoB,MAAM,2MAAA,CAAA,UAAO;gBAAE,OAAO;YAAc;YAChE;gBAAE,MAAM;gBAAoB,MAAM,yMAAA,CAAA,SAAM;gBAAE,OAAO;YAAkB;SACpE;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,OAAO;YACL;gBAAE,MAAM;gBAAc,MAAM,uMAAA,CAAA,QAAa;gBAAE,OAAO;YAAe;YACjE;gBAAE,MAAM;gBAAgB,MAAM,+MAAA,CAAA,YAAS;gBAAE,OAAO;YAAW;YAC3D;gBAAE,MAAM;gBAAU,MAAM,2NAAA,CAAA,gBAAa;gBAAE,OAAO;YAAQ;SACvD;IACH;IACA;QACE,OAAO;QACP,aAAa;QACb,OAAO;YACL;gBAAE,MAAM;gBAAgB,MAAM,2MAAA,CAAA,UAAO;gBAAE,OAAO;YAAa;YAC3D;gBAAE,MAAM;gBAAU,MAAM,6MAAA,CAAA,WAAS;gBAAE,OAAO;YAAiB;SAC5D;IACH;CACD;AAEM,MAAM,aAAwC,CAAC,EACpD,SAAS,EACT,YAAY,KAAK,EAClB;;IACC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACrD,IAAI,IAAI,mBAAmB,MAAM;+BAAC,CAAA,IAAK,EAAE,WAAW;8BAAE,GAAG;+BAAC,CAAA,IAAK,EAAE,KAAK;;IAGxE,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,QAAQ;YAC1B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,oBAAoB;IACtB;IAEA,MAAM,mBAAmB,mBACtB,GAAG,CAAC,CAAA,UAAW,CAAC;YACf,GAAG,OAAO;YACV,OAAO,QAAQ,KAAK,CAAC,MAAM,CAAC,CAAA,OAC1B,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAE7D,CAAC,GACA,MAAM,CAAC,CAAA,UAAW,QAAQ,KAAK,CAAC,MAAM,GAAG;IAE5C,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,UAAU,WAAW;IAC9B;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;;8BAE3D,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;kCACvB,cAAA,6LAAC,mNAAA,CAAA,YAAe;4BAAC,WAAU;;;;;;;;;;;;;;;;8BAG/B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,mBACE,OAAO,CAAC,CAAA,UAAW,QAAQ,KAAK,EAChC,GAAG,CAAC,CAAA,qBACH,6LAAC,qIAAA,CAAA,SAAM;gCAEL,OAAO;gCACP,SAAS,SAAS,KAAK,IAAI,IAAI,cAAc;gCAC7C,MAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,KAAK,IAAI;oCAAE,OAAO,KAAK,KAAK;8CACtC,cAAA,6LAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;;;;;;+BAPlB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;IAe9B;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;;0BAG3D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;;sCAEV,6LAAC,mNAAA,CAAA,YAAe;4BAAC,WAAU;;;;;;sCAC3B,6LAAC;sCAAK;;;;;;;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC,oIAAA,CAAA,QAAK;4BACJ,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAA,IAAK,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC5C,WAAU;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAA;wBACpB,MAAM,aAAa,iBAAiB,GAAG,CAAC,QAAQ,KAAK;wBACrD,MAAM,kBAAkB,QAAQ,KAAK,CAAC,MAAM,GAAG;wBAE/C,IAAI,CAAC,iBAAiB,OAAO;wBAE7B,qBACE,6LAAC;4BAAwB,WAAU;;8CACjC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,cAAc,QAAQ,KAAK;;sDAE1C,6LAAC;sDAAM,QAAQ,KAAK;;;;;;wCACnB,2BACC,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;iEAEvB,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;gCAI3B,4BACC,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAA,qBACjB,6LAAC,qIAAA,CAAA,SAAM;4CAEL,OAAO;4CACP,SAAS,SAAS,KAAK,IAAI,IAAI,cAAc;4CAC7C,WAAU;sDAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;;kEAEV,6LAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;kEACrB,6LAAC;wDAAK,WAAU;kEAAU,KAAK,KAAK;;;;;;oDACnC,KAAK,KAAK,kBACT,6LAAC;wDAAK,WAAU;kEAAmE;;;;;;oDAIpF,KAAK,KAAK,kBACT,6LAAC;wDAAK,WAAU;kEACb,KAAK,KAAK;;;;;;;;;;;;2CAlBZ,KAAK,IAAI;;;;;;;;;;;2BAlBd,QAAQ,KAAK;;;;;oBA8C3B;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAK;;;;;;sCACN,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAY,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAOnE;GAxKa;;QAIM,qIAAA,CAAA,cAAW;;;KAJjB", "debugId": null}}, {"offset": {"line": 2252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    className={cn(\r\n      'relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nAvatar.displayName = AvatarPrimitive.Root.displayName;\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    className={cn('aspect-square h-full w-full', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    className={cn(\r\n      'flex h-full w-full items-center justify-center rounded-full bg-muted',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\r\n\r\nexport { Avatar, AvatarFallback, AvatarImage };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAoB;QACnB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC7C,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,WAAwB;QACvB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,qKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import { cva, type VariantProps } from 'class-variance-authority';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst badgeVariants = cva(\r\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\r\n  {\r\n    defaultVariants: {\r\n      variant: 'default',\r\n    },\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',\r\n        destructive:\r\n          'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',\r\n        outline: 'text-foreground',\r\n        secondary:\r\n          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',\r\n      },\r\n    },\r\n  }\r\n);\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAGA;AAAA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,iBAAiB;QACf,SAAS;IACX;IACA,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SAAS;YACT,WACE;QACJ;IACF;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';\r\nimport { Check, ChevronRight, Circle } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root;\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      'flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',\r\n      inset && 'pl-8',\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n));\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName;\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName;\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        'z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n));\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      'relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',\r\n      inset && 'pl-8',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\r\n      className\r\n    )}\r\n    {...(checked !== undefined && { checked })}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n));\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName;\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n));\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean;\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      'px-2 py-1.5 text-sm font-semibold',\r\n      inset && 'pl-8',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\r\n    {...props}\r\n  />\r\n));\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\r\n      {...props}\r\n    />\r\n  );\r\n};\r\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut';\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,eAAe,+KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,+KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,+KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,+KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,+KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,+KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,+jBACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,+KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,qSACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,+KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,6LAAC,+KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAI,YAAY,aAAa;YAAE;QAAQ,CAAC;QACxC,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,+KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,+KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as TabsPrimitive from '@radix-ui/react-tabs';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Tabs = TabsPrimitive.Root;\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    className={cn(\r\n      'inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTabsList.displayName = TabsPrimitive.List.displayName;\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    className={cn(\r\n      'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    className={cn(\r\n      'mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nTabsContent.displayName = TabsPrimitive.Content.displayName;\r\n\r\nexport { Tabs, TabsContent, TabsList, TabsTrigger };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2664, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/user/UserProfile.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport {\r\n  Activity,\r\n  Calendar,\r\n  CheckCircle,\r\n  Clock,\r\n  Globe,\r\n  Loader2,\r\n  LogOut,\r\n  Mail,\r\n  Shield,\r\n  ShieldCheck,\r\n  Smartphone,\r\n  User,\r\n  XCircle,\r\n} from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport React, { useState } from 'react';\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';\r\nimport { useAuthContext } from '@/contexts/AuthContext';\r\n\r\ninterface UserProfileProps {\r\n  showSignOut?: boolean;\r\n  variant?: 'card' | 'dropdown' | 'detailed';\r\n}\r\n\r\n// Helper function to get role badge color\r\nconst getRoleBadgeColor = (role: null | string) => {\r\n  switch (role) {\r\n    case 'ADMIN': {\r\n      return 'bg-purple-500 hover:bg-purple-600 text-white';\r\n    }\r\n    case 'EMPLOYEE': {\r\n      return 'bg-green-500 hover:bg-green-600 text-white';\r\n    }\r\n    case 'MANAGER': {\r\n      return 'bg-blue-500 hover:bg-blue-600 text-white';\r\n    }\r\n    case 'SUPER_ADMIN': {\r\n      return 'bg-red-500 hover:bg-red-600 text-white';\r\n    }\r\n    default: {\r\n      return 'bg-gray-500 hover:bg-gray-600 text-white';\r\n    }\r\n  }\r\n};\r\n\r\nexport function UserProfile({\r\n  showSignOut = true,\r\n  variant = 'dropdown',\r\n}: UserProfileProps) {\r\n  const { signOut, user, userRole } = useAuthContext();\r\n  const router = useRouter();\r\n  const [isSigningOut, setIsSigningOut] = useState(false);\r\n\r\n  if (!user) {\r\n    return null;\r\n  }\r\n\r\n  const handleSignOut = async () => {\r\n    setIsSigningOut(true);\r\n    try {\r\n      await signOut();\r\n      router.push('/login');\r\n    } catch (error) {\r\n      console.error('Sign out error:', error);\r\n    } finally {\r\n      setIsSigningOut(false);\r\n    }\r\n  };\r\n\r\n  // Helper functions\r\n  const formatDate = (dateString: string | null | undefined) => {\r\n    if (!dateString) return 'Never';\r\n    return new Date(dateString).toLocaleDateString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n    });\r\n  };\r\n\r\n  const formatDateTime = (dateString: string | null | undefined) => {\r\n    if (!dateString) return 'Never';\r\n    return new Date(dateString).toLocaleString('en-US', {\r\n      year: 'numeric',\r\n      month: 'short',\r\n      day: 'numeric',\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n    });\r\n  };\r\n\r\n  const getTimeAgo = (dateString: string | null | undefined) => {\r\n    if (!dateString) return 'Never';\r\n    const now = new Date();\r\n    const date = new Date(dateString);\r\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\r\n\r\n    if (diffInSeconds < 60) return 'Just now';\r\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\r\n    if (diffInSeconds < 86400)\r\n      return `${Math.floor(diffInSeconds / 3600)}h ago`;\r\n    if (diffInSeconds < 2592000)\r\n      return `${Math.floor(diffInSeconds / 86400)}d ago`;\r\n    return formatDate(dateString);\r\n  };\r\n\r\n  const getUserRole = () => {\r\n    // Debug logging to see what data we have\r\n    console.log('🔍 Role Debug Info:', {\r\n      'user.user_metadata?.role': user.user_metadata?.role,\r\n      'userRole (from context)': userRole,\r\n      'user.user_metadata': user.user_metadata,\r\n      'user.app_metadata': user.app_metadata,\r\n      'user.email': user.email,\r\n    });\r\n\r\n    // TEMPORARY FIX: If user_metadata has role but context doesn't, show a refresh button\r\n    if (user.user_metadata?.role && user.user_metadata.role !== userRole) {\r\n      console.warn(\r\n        '🚨 Role mismatch detected! User needs to refresh their session.',\r\n        {\r\n          metadataRole: user.user_metadata.role,\r\n          contextRole: userRole,\r\n        }\r\n      );\r\n    }\r\n\r\n    return user.user_metadata?.role || userRole || 'USER';\r\n  };\r\n\r\n  const getUserStatus = () => {\r\n    return user.user_metadata?.is_active !== false ? 'Active' : 'Inactive';\r\n  };\r\n\r\n  const getRoleBadgeVariant = (role: string) => {\r\n    switch (role.toUpperCase()) {\r\n      case 'SUPER_ADMIN':\r\n        return 'destructive';\r\n      case 'ADMIN':\r\n        return 'destructive';\r\n      case 'MANAGER':\r\n        return 'default';\r\n      case 'USER':\r\n        return 'secondary';\r\n      case 'READONLY':\r\n        return 'outline';\r\n      default:\r\n        return 'secondary';\r\n    }\r\n  };\r\n\r\n  const getStatusBadgeVariant = (status: string) => {\r\n    return status === 'Active' ? 'default' : 'destructive';\r\n  };\r\n\r\n  const userInitials = user.email ? user.email.charAt(0).toUpperCase() : 'U';\r\n  const userEmail = user.email || 'N/A';\r\n  const isEmailVerified = user.email_confirmed_at !== null;\r\n\r\n  // Use consistent role source - prioritize user_metadata over context\r\n  const currentUserRole = getUserRole();\r\n  const roleDisplayName = currentUserRole\r\n    ? currentUserRole.replace('_', ' ')\r\n    : 'N/A';\r\n  const roleBadgeClass = getRoleBadgeColor(currentUserRole);\r\n\r\n  if (variant === 'dropdown') {\r\n    return (\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button className=\"relative size-8 rounded-full\" variant=\"ghost\">\r\n            <Avatar className=\"size-8\">\r\n              <AvatarImage\r\n                alt={userInitials}\r\n                src={user.user_metadata?.avatar_url || ''}\r\n              />\r\n              <AvatarFallback>{userInitials}</AvatarFallback>\r\n            </Avatar>\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\" className=\"w-56\" forceMount>\r\n          <DropdownMenuLabel className=\"font-normal\">\r\n            <div className=\"flex flex-col space-y-1\">\r\n              <p className=\"text-sm font-medium leading-none\">{userEmail}</p>\r\n              <p className=\"text-xs leading-none text-muted-foreground\">\r\n                {user.id}\r\n              </p>\r\n            </div>\r\n          </DropdownMenuLabel>\r\n          <DropdownMenuSeparator />\r\n          <DropdownMenuItem>\r\n            <User className=\"mr-2 size-4\" />\r\n            <Link href=\"/profile\">Profile</Link>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem>\r\n            <Mail className=\"mr-2 size-4\" />\r\n            <span>\r\n              {isEmailVerified ? 'Email Verified' : 'Email Not Verified'}\r\n            </span>\r\n            {isEmailVerified ? (\r\n              <CheckCircle className=\"ml-auto size-4 text-green-500\" />\r\n            ) : (\r\n              <XCircle className=\"ml-auto size-4 text-red-500\" />\r\n            )}\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem>\r\n            <ShieldCheck className=\"mr-2 size-4\" />\r\n            <Badge className={roleBadgeClass}>{roleDisplayName}</Badge>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuSeparator />\r\n          <DropdownMenuItem onClick={handleSignOut}>\r\n            <LogOut className=\"mr-2 size-4\" />\r\n            <span>Log out</span>\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    );\r\n  }\r\n\r\n  if (variant === 'card') {\r\n    return (\r\n      <Card className=\"mx-auto w-full max-w-md\">\r\n        <CardHeader className=\"flex flex-row items-center space-x-4 p-6\">\r\n          <Avatar className=\"size-16\">\r\n            <AvatarImage\r\n              alt={userInitials}\r\n              src={user.user_metadata?.avatar_url || ''}\r\n            />\r\n            <AvatarFallback className=\"text-2xl\">{userInitials}</AvatarFallback>\r\n          </Avatar>\r\n          <div className=\"flex flex-col space-y-1\">\r\n            <CardTitle className=\"text-2xl font-bold\">{userEmail}</CardTitle>\r\n            <CardDescription className=\"text-sm text-muted-foreground\">\r\n              User ID: {user.id}\r\n            </CardDescription>\r\n            <Badge className={`${roleBadgeClass} px-2 py-1 text-sm`}>\r\n              {roleDisplayName}\r\n            </Badge>\r\n          </div>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-4 p-6 pt-0\">\r\n          <div className=\"flex items-center\">\r\n            <Mail className=\"mr-2 size-5 text-muted-foreground\" />\r\n            <span className=\"text-base\">Email: {userEmail}</span>\r\n            {isEmailVerified ? (\r\n              <CheckCircle className=\"ml-2 size-5 text-green-500\" />\r\n            ) : (\r\n              <XCircle className=\"ml-2 size-5 text-red-500\" />\r\n            )}\r\n          </div>\r\n          <div className=\"flex items-center\">\r\n            <ShieldCheck className=\"mr-2 size-5 text-muted-foreground\" />\r\n            <span className=\"text-base\">Role: </span>\r\n            <Badge className={`${roleBadgeClass} ml-1`}>\r\n              {roleDisplayName}\r\n            </Badge>\r\n          </div>\r\n          {/* Add more account details here as needed */}\r\n          {showSignOut && (\r\n            <div className=\"flex justify-end\">\r\n              <Button\r\n                onClick={handleSignOut}\r\n                variant=\"outline\"\r\n                disabled={isSigningOut}\r\n              >\r\n                {isSigningOut ? (\r\n                  <>\r\n                    <Loader2 className=\"mr-2 size-4 animate-spin\" />\r\n                    Signing out...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <LogOut className=\"mr-2 size-4\" />\r\n                    Log out\r\n                  </>\r\n                )}\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Detailed variant with comprehensive information\r\n  if (variant === 'detailed') {\r\n    return (\r\n      <div className=\"w-full max-w-5xl mx-auto space-y-6\">\r\n        {/* Header Card */}\r\n        <Card>\r\n          <CardHeader className=\"pb-4\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center space-x-4\">\r\n                <Avatar className=\"size-20\">\r\n                  <AvatarImage\r\n                    alt={user.email || ''}\r\n                    src={user.user_metadata?.avatar_url}\r\n                  />\r\n                  <AvatarFallback className=\"text-xl\">\r\n                    {userInitials}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"space-y-2\">\r\n                  <div>\r\n                    <h1 className=\"text-3xl font-bold\">\r\n                      {user.user_metadata?.full_name || 'User Profile'}\r\n                    </h1>\r\n                    <p className=\"text-muted-foreground text-lg\">\r\n                      {user.email}\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-3 flex-wrap\">\r\n                    <Badge\r\n                      className=\"text-sm px-3 py-1\"\r\n                      variant={getRoleBadgeVariant(getUserRole())}\r\n                    >\r\n                      {getUserRole()}\r\n                    </Badge>\r\n                    <Badge\r\n                      className=\"text-sm px-3 py-1\"\r\n                      variant={getStatusBadgeVariant(getUserStatus())}\r\n                    >\r\n                      <Activity className=\"mr-1 size-3\" />\r\n                      {getUserStatus()}\r\n                    </Badge>\r\n                    {user.email_confirmed_at && (\r\n                      <Badge className=\"text-sm px-3 py-1\" variant=\"outline\">\r\n                        <Shield className=\"mr-1 size-3\" />\r\n                        Verified\r\n                      </Badge>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              {showSignOut && (\r\n                <Button\r\n                  disabled={isSigningOut}\r\n                  onClick={handleSignOut}\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                >\r\n                  {isSigningOut ? (\r\n                    <>\r\n                      <Loader2 className=\"mr-2 size-4 animate-spin\" />\r\n                      Signing out...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <LogOut className=\"mr-2 size-4\" />\r\n                      Sign out\r\n                    </>\r\n                  )}\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </CardHeader>\r\n        </Card>\r\n\r\n        {/* Detailed Information Tabs */}\r\n        <Tabs defaultValue=\"overview\" className=\"w-full\">\r\n          <TabsList className=\"grid w-full grid-cols-3\">\r\n            <TabsTrigger value=\"overview\">Overview</TabsTrigger>\r\n            <TabsTrigger value=\"security\">Security</TabsTrigger>\r\n            <TabsTrigger value=\"activity\">Activity</TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent value=\"overview\" className=\"space-y-4\">\r\n            <div className=\"grid gap-4 md:grid-cols-2\">\r\n              {/* Account Information */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n                    <User className=\"size-5\" />\r\n                    Account Information\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent className=\"space-y-4\">\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-muted-foreground\">\r\n                        User ID:\r\n                      </span>\r\n                      <span className=\"font-mono text-xs bg-muted px-2 py-1 rounded\">\r\n                        {user.id.slice(0, 8)}...\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-muted-foreground\">\r\n                        Email:\r\n                      </span>\r\n                      <span className=\"text-sm\">{user.email}</span>\r\n                    </div>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-muted-foreground\">\r\n                        Role:\r\n                      </span>\r\n                      <Badge variant={getRoleBadgeVariant(getUserRole())}>\r\n                        {getUserRole()}\r\n                      </Badge>\r\n                    </div>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-muted-foreground\">\r\n                        Status:\r\n                      </span>\r\n                      <Badge variant={getStatusBadgeVariant(getUserStatus())}>\r\n                        {getUserStatus()}\r\n                      </Badge>\r\n                    </div>\r\n                    {user.user_metadata?.employee_id && (\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <span className=\"text-sm text-muted-foreground\">\r\n                          Employee ID:\r\n                        </span>\r\n                        <span className=\"text-sm\">\r\n                          {user.user_metadata.employee_id}\r\n                        </span>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n              {/* Contact & Verification */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n                    <Mail className=\"size-5\" />\r\n                    Contact & Verification\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent className=\"space-y-4\">\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-muted-foreground\">\r\n                        Email Verified:\r\n                      </span>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        {user.email_confirmed_at ? (\r\n                          <>\r\n                            <Shield className=\"size-4 text-green-600\" />\r\n                            <span className=\"text-sm text-green-600\">\r\n                              Verified\r\n                            </span>\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <Shield className=\"size-4 text-red-600\" />\r\n                            <span className=\"text-sm text-red-600\">\r\n                              Not Verified\r\n                            </span>\r\n                          </>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                    {user.email_confirmed_at && (\r\n                      <div className=\"flex items-center justify-between\">\r\n                        <span className=\"text-sm text-muted-foreground\">\r\n                          Verified On:\r\n                        </span>\r\n                        <span className=\"text-sm\">\r\n                          {formatDate(user.email_confirmed_at)}\r\n                        </span>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"security\" className=\"space-y-4\">\r\n            <div className=\"grid gap-4 md:grid-cols-2\">\r\n              {/* Security Status */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n                    <Shield className=\"size-5\" />\r\n                    Security Status\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent className=\"space-y-4\">\r\n                  <Alert>\r\n                    <Shield className=\"size-4\" />\r\n                    <AlertDescription>\r\n                      Your account is protected by enterprise-grade security\r\n                      protocols. All activities are monitored and logged for\r\n                      security purposes.\r\n                    </AlertDescription>\r\n                  </Alert>\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-muted-foreground\">\r\n                        Account Type:\r\n                      </span>\r\n                      <span className=\"text-sm\">Standard User</span>\r\n                    </div>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-muted-foreground\">\r\n                        SSO User:\r\n                      </span>\r\n                      <span className=\"text-sm\">\r\n                        {user.is_sso_user ? 'Yes' : 'No'}\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-muted-foreground\">\r\n                        Anonymous:\r\n                      </span>\r\n                      <span className=\"text-sm\">\r\n                        {user.is_anonymous ? 'Yes' : 'No'}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n\r\n              {/* Session Information */}\r\n              <Card>\r\n                <CardHeader>\r\n                  <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n                    <Globe className=\"size-5\" />\r\n                    Session Information\r\n                  </CardTitle>\r\n                </CardHeader>\r\n                <CardContent className=\"space-y-4\">\r\n                  <div className=\"space-y-3\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-muted-foreground\">\r\n                        Current Session:\r\n                      </span>\r\n                      <Badge variant=\"default\">Active</Badge>\r\n                    </div>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-muted-foreground\">\r\n                        Session ID:\r\n                      </span>\r\n                      <span className=\"font-mono text-xs bg-muted px-2 py-1 rounded\">\r\n                        {user.id.slice(-8)}\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <span className=\"text-sm text-muted-foreground\">\r\n                        App Metadata:\r\n                      </span>\r\n                      <span className=\"text-sm\">\r\n                        {user.app_metadata ? 'Present' : 'None'}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </CardContent>\r\n              </Card>\r\n            </div>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"activity\" className=\"space-y-4\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle className=\"flex items-center gap-2 text-lg\">\r\n                  <Activity className=\"size-5\" />\r\n                  Account Activity\r\n                </CardTitle>\r\n              </CardHeader>\r\n              <CardContent className=\"space-y-4\">\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"flex items-center justify-between p-3 bg-muted rounded-lg\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <Clock className=\"size-5 text-muted-foreground\" />\r\n                      <div>\r\n                        <p className=\"text-sm font-medium\">Last Sign In</p>\r\n                        <p className=\"text-xs text-muted-foreground\">\r\n                          {formatDateTime(user.last_sign_in_at)}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    <span className=\"text-sm text-muted-foreground\">\r\n                      {getTimeAgo(user.last_sign_in_at)}\r\n                    </span>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center justify-between p-3 bg-muted rounded-lg\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <Calendar className=\"size-5 text-muted-foreground\" />\r\n                      <div>\r\n                        <p className=\"text-sm font-medium\">Account Created</p>\r\n                        <p className=\"text-xs text-muted-foreground\">\r\n                          {formatDateTime(user.created_at)}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    <span className=\"text-sm text-muted-foreground\">\r\n                      {getTimeAgo(user.created_at)}\r\n                    </span>\r\n                  </div>\r\n\r\n                  <div className=\"flex items-center justify-between p-3 bg-muted rounded-lg\">\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <User className=\"size-5 text-muted-foreground\" />\r\n                      <div>\r\n                        <p className=\"text-sm font-medium\">Last Updated</p>\r\n                        <p className=\"text-xs text-muted-foreground\">\r\n                          {formatDateTime(user.updated_at)}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                    <span className=\"text-sm text-muted-foreground\">\r\n                      {getTimeAgo(user.updated_at)}\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </TabsContent>\r\n        </Tabs>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return null;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AACA;AAEA;AACA;AACA;AACA;AAOA;AAQA;AAEA;AACA;;;AA1CA;;;;;;;;;;;;;AAiDA,0CAA0C;AAC1C,MAAM,oBAAoB,CAAC;IACzB,OAAQ;QACN,KAAK;YAAS;gBACZ,OAAO;YACT;QACA,KAAK;YAAY;gBACf,OAAO;YACT;QACA,KAAK;YAAW;gBACd,OAAO;YACT;QACA,KAAK;YAAe;gBAClB,OAAO;YACT;QACA;YAAS;gBACP,OAAO;YACT;IACF;AACF;AAEO,SAAS,YAAY,EAC1B,cAAc,IAAI,EAClB,UAAU,UAAU,EACH;;IACjB,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD;IACjD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,mBAAmB;IACnB,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,IAAI,KAAK,YAAY,cAAc,CAAC,SAAS;YAClD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI;QAEpE,IAAI,gBAAgB,IAAI,OAAO;QAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;QACzE,IAAI,gBAAgB,OAClB,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC;QACnD,IAAI,gBAAgB,SAClB,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;QACpD,OAAO,WAAW;IACpB;IAEA,MAAM,cAAc;QAClB,yCAAyC;QACzC,QAAQ,GAAG,CAAC,uBAAuB;YACjC,4BAA4B,KAAK,aAAa,EAAE;YAChD,2BAA2B;YAC3B,sBAAsB,KAAK,aAAa;YACxC,qBAAqB,KAAK,YAAY;YACtC,cAAc,KAAK,KAAK;QAC1B;QAEA,sFAAsF;QACtF,IAAI,KAAK,aAAa,EAAE,QAAQ,KAAK,aAAa,CAAC,IAAI,KAAK,UAAU;YACpE,QAAQ,IAAI,CACV,mEACA;gBACE,cAAc,KAAK,aAAa,CAAC,IAAI;gBACrC,aAAa;YACf;QAEJ;QAEA,OAAO,KAAK,aAAa,EAAE,QAAQ,YAAY;IACjD;IAEA,MAAM,gBAAgB;QACpB,OAAO,KAAK,aAAa,EAAE,cAAc,QAAQ,WAAW;IAC9D;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,KAAK,WAAW;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,WAAW,WAAW,YAAY;IAC3C;IAEA,MAAM,eAAe,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK;IACvE,MAAM,YAAY,KAAK,KAAK,IAAI;IAChC,MAAM,kBAAkB,KAAK,kBAAkB,KAAK;IAEpD,qEAAqE;IACrE,MAAM,kBAAkB;IACxB,MAAM,kBAAkB,uCACpB,gBAAgB,OAAO,CAAC,KAAK;IAEjC,MAAM,iBAAiB,kBAAkB;IAEzC,IAAI,YAAY,YAAY;QAC1B,qBACE,6LAAC,+IAAA,CAAA,eAAY;;8BACX,6LAAC,+IAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,WAAU;wBAA+B,SAAQ;kCACvD,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,6LAAC,qIAAA,CAAA,cAAW;oCACV,KAAK;oCACL,KAAK,KAAK,aAAa,EAAE,cAAc;;;;;;8CAEzC,6LAAC,qIAAA,CAAA,iBAAc;8CAAE;;;;;;;;;;;;;;;;;;;;;;8BAIvB,6LAAC,+IAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAM,WAAU;oBAAO,UAAU;;sCAC1D,6LAAC,+IAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAC3B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAoC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDACV,KAAK,EAAE;;;;;;;;;;;;;;;;;sCAId,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sCACtB,6LAAC,+IAAA,CAAA,mBAAgB;;8CACf,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAW;;;;;;;;;;;;sCAExB,6LAAC,+IAAA,CAAA,mBAAgB;;8CACf,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CACE,kBAAkB,mBAAmB;;;;;;gCAEvC,gCACC,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,6LAAC,+MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;;sCAGvB,6LAAC,+IAAA,CAAA,mBAAgB;;8CACf,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAW;8CAAiB;;;;;;;;;;;;sCAErC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sCACtB,6LAAC,+IAAA,CAAA,mBAAgB;4BAAC,SAAS;;8CACzB,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,IAAI,YAAY,QAAQ;QACtB,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,6LAAC,qIAAA,CAAA,cAAW;oCACV,KAAK;oCACL,KAAK,KAAK,aAAa,EAAE,cAAc;;;;;;8CAEzC,6LAAC,qIAAA,CAAA,iBAAc;oCAAC,WAAU;8CAAY;;;;;;;;;;;;sCAExC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAsB;;;;;;8CAC3C,6LAAC,mIAAA,CAAA,kBAAe;oCAAC,WAAU;;wCAAgC;wCAC/C,KAAK,EAAE;;;;;;;8CAEnB,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAW,GAAG,eAAe,kBAAkB,CAAC;8CACpD;;;;;;;;;;;;;;;;;;8BAIP,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;;wCAAY;wCAAQ;;;;;;;gCACnC,gCACC,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,6LAAC,+MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;;sCAGvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;oCAAK,WAAU;8CAAY;;;;;;8CAC5B,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAW,GAAG,eAAe,KAAK,CAAC;8CACvC;;;;;;;;;;;;wBAIJ,6BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,UAAU;0CAET,6BACC;;sDACE,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA6B;;iEAIlD;;sDACE,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAgB;;;;;;;;;;;;;;;;;;;;;;;;;IAUpD;IAEA,kDAAkD;IAClD,IAAI,YAAY,YAAY;QAC1B,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDACV,KAAK,KAAK,KAAK,IAAI;oDACnB,KAAK,KAAK,aAAa,EAAE;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB;;;;;;;;;;;;sDAGL,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,KAAK,aAAa,EAAE,aAAa;;;;;;sEAEpC,6LAAC;4DAAE,WAAU;sEACV,KAAK,KAAK;;;;;;;;;;;;8DAGf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DACJ,WAAU;4DACV,SAAS,oBAAoB;sEAE5B;;;;;;sEAEH,6LAAC,oIAAA,CAAA,QAAK;4DACJ,WAAU;4DACV,SAAS,sBAAsB;;8EAE/B,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB;;;;;;;wDAEF,KAAK,kBAAkB,kBACtB,6LAAC,oIAAA,CAAA,QAAK;4DAAC,WAAU;4DAAoB,SAAQ;;8EAC3C,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;gCAO3C,6BACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,UAAU;oCACV,SAAS;oCACT,SAAQ;oCACR,MAAK;8CAEJ,6BACC;;0DACE,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA6B;;qEAIlD;;0DACE,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;;;;;;;;;;;;;;;;;;8BAWhD,6LAAC,mIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAW,WAAU;;sCACtC,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;;;;;;;sCAGhC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAW;;;;;;;;;;;;0DAI/B,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgC;;;;;;8EAGhD,6LAAC;oEAAK,WAAU;;wEACb,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG;wEAAG;;;;;;;;;;;;;sEAGzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgC;;;;;;8EAGhD,6LAAC;oEAAK,WAAU;8EAAW,KAAK,KAAK;;;;;;;;;;;;sEAEvC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgC;;;;;;8EAGhD,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAS,oBAAoB;8EACjC;;;;;;;;;;;;sEAGL,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgC;;;;;;8EAGhD,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAS,sBAAsB;8EACnC;;;;;;;;;;;;wDAGJ,KAAK,aAAa,EAAE,6BACnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgC;;;;;;8EAGhD,6LAAC;oEAAK,WAAU;8EACb,KAAK,aAAa,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAS3C,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAW;;;;;;;;;;;;0DAI/B,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgC;;;;;;8EAGhD,6LAAC;oEAAI,WAAU;8EACZ,KAAK,kBAAkB,iBACtB;;0FACE,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;gFAAK,WAAU;0FAAyB;;;;;;;qGAK3C;;0FACE,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;gFAAK,WAAU;0FAAuB;;;;;;;;;;;;;;;;;;;wDAO9C,KAAK,kBAAkB,kBACtB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgC;;;;;;8EAGhD,6LAAC;oEAAK,WAAU;8EACb,WAAW,KAAK,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUnD,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAW;;;;;;;;;;;;0DAIjC,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,6LAAC,oIAAA,CAAA,QAAK;;0EACJ,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,6LAAC,oIAAA,CAAA,mBAAgB;0EAAC;;;;;;;;;;;;kEAMpB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAgC;;;;;;kFAGhD,6LAAC;wEAAK,WAAU;kFAAU;;;;;;;;;;;;0EAE5B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAgC;;;;;;kFAGhD,6LAAC;wEAAK,WAAU;kFACb,KAAK,WAAW,GAAG,QAAQ;;;;;;;;;;;;0EAGhC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAgC;;;;;;kFAGhD,6LAAC;wEAAK,WAAU;kFACb,KAAK,YAAY,GAAG,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQvC,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAW;;;;;;;;;;;;0DAIhC,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgC;;;;;;8EAGhD,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;8EAAU;;;;;;;;;;;;sEAE3B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgC;;;;;;8EAGhD,6LAAC;oEAAK,WAAU;8EACb,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;sEAGpB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAgC;;;;;;8EAGhD,6LAAC;oEAAK,WAAU;8EACb,KAAK,YAAY,GAAG,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS/C,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAW;;;;;;;;;;;;kDAInC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAAsB;;;;;;sFACnC,6LAAC;4EAAE,WAAU;sFACV,eAAe,KAAK,eAAe;;;;;;;;;;;;;;;;;;sEAI1C,6LAAC;4DAAK,WAAU;sEACb,WAAW,KAAK,eAAe;;;;;;;;;;;;8DAIpC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAAsB;;;;;;sFACnC,6LAAC;4EAAE,WAAU;sFACV,eAAe,KAAK,UAAU;;;;;;;;;;;;;;;;;;sEAIrC,6LAAC;4DAAK,WAAU;sEACb,WAAW,KAAK,UAAU;;;;;;;;;;;;8DAI/B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;;sFACC,6LAAC;4EAAE,WAAU;sFAAsB;;;;;;sFACnC,6LAAC;4EAAE,WAAU;sFACV,eAAe,KAAK,UAAU;;;;;;;;;;;;;;;;;;sEAIrC,6LAAC;4DAAK,WAAU;sEACb,WAAW,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAU/C;IAEA,OAAO;AACT;GA5jBgB;;QAIsB,kIAAA,CAAA,iBAAc;QACnC,qIAAA,CAAA,YAAS;;;KALV", "debugId": null}}, {"offset": {"line": 4321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/theme-toggle.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { <PERSON>, <PERSON>, Sun } from 'lucide-react';\r\nimport { useTheme as useNextTheme } from 'next-themes';\r\nimport * as React from 'react';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { useTheme } from '@/hooks/ui/useTheme';\r\n\r\nexport function ThemeToggle() {\r\n  const {\r\n    setTheme: setNextTheme,\r\n    systemTheme,\r\n    theme: nextTheme,\r\n  } = useNextTheme();\r\n  const { currentTheme, setTheme: setZustandTheme } = useTheme();\r\n  const [mounted, setMounted] = React.useState(false);\r\n\r\n  // Ensure component is mounted to avoid hydration issues\r\n  React.useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Enhanced theme switching that updates both systems\r\n  const handleThemeChange = (newTheme: 'dark' | 'light' | 'system') => {\r\n    setNextTheme(newTheme);\r\n    if (newTheme === 'system') {\r\n      // If system theme is selected, update Zustand with the actual system preference\r\n      const systemPreference = systemTheme || 'light';\r\n      setZustandTheme(systemPreference as 'dark' | 'light');\r\n    } else {\r\n      setZustandTheme(newTheme);\r\n    }\r\n  };\r\n\r\n  // Get the current effective theme for display\r\n  const effectiveTheme = nextTheme || currentTheme;\r\n  const resolvedTheme =\r\n    effectiveTheme === 'system' ? systemTheme : effectiveTheme;\r\n  const isDark = resolvedTheme === 'dark';\r\n\r\n  // Don't render until mounted to avoid hydration mismatch\r\n  if (!mounted) {\r\n    return (\r\n      <Button className=\"text-foreground\" size=\"icon\" variant=\"ghost\">\r\n        <div className=\"size-[1.2rem]\" />\r\n      </Button>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button\r\n          aria-label={`Current theme: ${effectiveTheme}. Click to change theme`}\r\n          className=\"text-foreground hover:bg-accent hover:text-accent-foreground\"\r\n          size=\"icon\"\r\n          variant=\"ghost\"\r\n        >\r\n          <Sun\r\n            className={`size-[1.2rem] transition-all duration-300 ${\r\n              isDark ? 'rotate-90 scale-0' : 'rotate-0 scale-100'\r\n            }`}\r\n          />\r\n          <Moon\r\n            className={`absolute size-[1.2rem] transition-all duration-300 ${\r\n              isDark ? 'rotate-0 scale-100' : 'rotate-90 scale-0'\r\n            }`}\r\n          />\r\n          <span className=\"sr-only\">Toggle theme</span>\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"end\" className=\"min-w-32\">\r\n        <DropdownMenuItem\r\n          className={\r\n            effectiveTheme === 'light' ? 'bg-accent text-accent-foreground' : ''\r\n          }\r\n          onClick={() => handleThemeChange('light')}\r\n        >\r\n          <Sun className=\"mr-2 size-4\" />\r\n          Light\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem\r\n          className={\r\n            effectiveTheme === 'dark' ? 'bg-accent text-accent-foreground' : ''\r\n          }\r\n          onClick={() => handleThemeChange('dark')}\r\n        >\r\n          <Moon className=\"mr-2 size-4\" />\r\n          Dark\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem\r\n          className={\r\n            effectiveTheme === 'system'\r\n              ? 'bg-accent text-accent-foreground'\r\n              : ''\r\n          }\r\n          onClick={() => handleThemeChange('system')}\r\n        >\r\n          <Monitor className=\"mr-2 size-4\" />\r\n          System\r\n        </DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAMA;;;AAbA;;;;;;;AAeO,SAAS;;IACd,MAAM,EACJ,UAAU,YAAY,EACtB,WAAW,EACX,OAAO,SAAS,EACjB,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAY,AAAD;IACf,MAAM,EAAE,YAAY,EAAE,UAAU,eAAe,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,WAAQ,AAAD;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAE7C,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,WAAW;QACb;gCAAG,EAAE;IAEL,qDAAqD;IACrD,MAAM,oBAAoB,CAAC;QACzB,aAAa;QACb,IAAI,aAAa,UAAU;YACzB,gFAAgF;YAChF,MAAM,mBAAmB,eAAe;YACxC,gBAAgB;QAClB,OAAO;YACL,gBAAgB;QAClB;IACF;IAEA,8CAA8C;IAC9C,MAAM,iBAAiB,aAAa;IACpC,MAAM,gBACJ,mBAAmB,WAAW,cAAc;IAC9C,MAAM,SAAS,kBAAkB;IAEjC,yDAAyD;IACzD,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC,qIAAA,CAAA,SAAM;YAAC,WAAU;YAAkB,MAAK;YAAO,SAAQ;sBACtD,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,cAAY,CAAC,eAAe,EAAE,eAAe,uBAAuB,CAAC;oBACrE,WAAU;oBACV,MAAK;oBACL,SAAQ;;sCAER,6LAAC,mMAAA,CAAA,MAAG;4BACF,WAAW,CAAC,0CAA0C,EACpD,SAAS,sBAAsB,sBAC/B;;;;;;sCAEJ,6LAAC,qMAAA,CAAA,OAAI;4BACH,WAAW,CAAC,mDAAmD,EAC7D,SAAS,uBAAuB,qBAChC;;;;;;sCAEJ,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAM,WAAU;;kCACzC,6LAAC,+IAAA,CAAA,mBAAgB;wBACf,WACE,mBAAmB,UAAU,qCAAqC;wBAEpE,SAAS,IAAM,kBAAkB;;0CAEjC,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAgB;;;;;;;kCAGjC,6LAAC,+IAAA,CAAA,mBAAgB;wBACf,WACE,mBAAmB,SAAS,qCAAqC;wBAEnE,SAAS,IAAM,kBAAkB;;0CAEjC,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAgB;;;;;;;kCAGlC,6LAAC,+IAAA,CAAA,mBAAgB;wBACf,WACE,mBAAmB,WACf,qCACA;wBAEN,SAAS,IAAM,kBAAkB;;0CAEjC,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAAgB;;;;;;;;;;;;;;;;;;;AAM7C;GAhGgB;;QAKV,mJAAA,CAAA,WAAY;QACoC,iIAAA,CAAA,WAAQ;;;KAN9C", "debugId": null}}, {"offset": {"line": 4519, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/layout/AppLayout.tsx"], "sourcesContent": ["/**\r\n * @file Modern app layout component with sidebar navigation\r\n * @module components/layout/AppLayout\r\n */\r\n\r\n'use client';\r\n\r\nimport { Menu, X } from 'lucide-react';\r\nimport React, { useState } from 'react';\r\n\r\nimport { AppSidebar } from './AppSidebar';\r\nimport { Button } from '@/components/ui/button';\r\nimport { UserProfile } from '@/components/user/UserProfile';\r\nimport { ThemeToggle } from '@/components/theme-toggle';\r\nimport { useUiPreferences } from '@/hooks/ui/useUiPreferences';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface AppLayoutProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\nexport const AppLayout: React.FC<AppLayoutProps> = ({\r\n  children,\r\n  className,\r\n}) => {\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\r\n  const { fontSize } = useUiPreferences();\r\n\r\n  const toggleSidebar = () => {\r\n    setSidebarCollapsed(!sidebarCollapsed);\r\n  };\r\n\r\n  const toggleMobileMenu = () => {\r\n    setMobileMenuOpen(!mobileMenuOpen);\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'flex h-screen bg-background',\r\n        fontSize === 'small' && 'text-sm',\r\n        fontSize === 'large' && 'text-lg',\r\n        className\r\n      )}\r\n    >\r\n      {/* Mobile Sidebar Overlay */}\r\n      {mobileMenuOpen && (\r\n        <div\r\n          className=\"fixed inset-0 z-50 bg-black/50 lg:hidden\"\r\n          onClick={() => setMobileMenuOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <div\r\n        className={cn(\r\n          'fixed inset-y-0 left-0 z-50 lg:static lg:z-auto',\r\n          mobileMenuOpen\r\n            ? 'translate-x-0'\r\n            : '-translate-x-full lg:translate-x-0',\r\n          'transition-transform duration-200 ease-in-out'\r\n        )}\r\n      >\r\n        <AppSidebar collapsed={sidebarCollapsed} className=\"h-full\" />\r\n      </div>\r\n\r\n      {/* Main Content Area */}\r\n      <div className=\"flex flex-1 flex-col overflow-hidden\">\r\n        {/* Top Header */}\r\n        <header className=\"flex h-16 items-center justify-between border-b bg-background px-4 lg:px-6\">\r\n          <div className=\"flex items-center gap-3\">\r\n            {/* Mobile Menu Button */}\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"lg:hidden\"\r\n              onClick={toggleMobileMenu}\r\n            >\r\n              <Menu className=\"size-5\" />\r\n              <span className=\"sr-only\">Open navigation menu</span>\r\n            </Button>\r\n\r\n            {/* Desktop Sidebar Toggle */}\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"hidden lg:flex\"\r\n              onClick={toggleSidebar}\r\n            >\r\n              {sidebarCollapsed ? (\r\n                <Menu className=\"size-5\" />\r\n              ) : (\r\n                <X className=\"size-5\" />\r\n              )}\r\n              <span className=\"sr-only\">Toggle sidebar</span>\r\n            </Button>\r\n          </div>\r\n\r\n          {/* Right Side Controls */}\r\n          <div className=\"flex items-center gap-3\">\r\n            <ThemeToggle />\r\n            <UserProfile variant=\"dropdown\" />\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main Content with consistent padding */}\r\n        <main className=\"flex-1 overflow-auto\">\r\n          <div className=\"h-full p-6\">{children}</div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAID;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAVA;;;;;;;;;AAiBO,MAAM,YAAsC,CAAC,EAClD,QAAQ,EACR,SAAS,EACV;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAEpC,MAAM,gBAAgB;QACpB,oBAAoB,CAAC;IACvB;IAEA,MAAM,mBAAmB;QACvB,kBAAkB,CAAC;IACrB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,+BACA,aAAa,WAAW,WACxB,aAAa,WAAW,WACxB;;YAID,gCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,kBAAkB;;;;;;0BAKrC,6LAAC;gBACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,mDACA,iBACI,kBACA,sCACJ;0BAGF,cAAA,6LAAC,6IAAA,CAAA,aAAU;oBAAC,WAAW;oBAAkB,WAAU;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;;0DAET,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;kDAI5B,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;;4CAER,iCACC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;qEAEhB,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;0DAEf,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;0CAK9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,wIAAA,CAAA,cAAW;;;;;kDACZ,6LAAC,4IAAA,CAAA,cAAW;wCAAC,SAAQ;;;;;;;;;;;;;;;;;;kCAKzB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCAAc;;;;;;;;;;;;;;;;;;;;;;;AAKvC;GA5Fa;;QAMU,yIAAA,CAAA,mBAAgB;;;KAN1B", "debugId": null}}, {"offset": {"line": 4731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\r\nimport { X } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\n\r\nconst DialogPortal = DialogPrimitive.Portal;\r\n\r\nconst DialogClose = DialogPrimitive.Close;\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    className={cn(\r\n      'fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ children, className, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      className={cn(\r\n        'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg',\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"size-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      'flex flex-col space-y-1.5 text-center sm:text-left',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogHeader.displayName = 'DialogHeader';\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2',\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogFooter.displayName = 'DialogFooter';\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    className={cn(\r\n      'text-lg font-semibold leading-none tracking-tight',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    className={cn('text-sm text-muted-foreground', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AAAA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAuB;QACtB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAEF,KAAK;gBACJ,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC/C,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4883, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as SeparatorPrimitive from '@radix-ui/react-separator';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Separator = React.forwardRef<\r\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\r\n>(\r\n  (\r\n    { className, decorative = true, orientation = 'horizontal', ...props },\r\n    ref\r\n  ) => (\r\n    <SeparatorPrimitive.Root\r\n      className={cn(\r\n        'shrink-0 bg-border',\r\n        orientation === 'horizontal' ? 'h-[1px] w-full' : 'h-full w-[1px]',\r\n        className\r\n      )}\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  )\r\n);\r\nSeparator.displayName = SeparatorPrimitive.Root.displayName;\r\n\r\nexport { Separator };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAI/B,CACE,EAAE,SAAS,EAAE,aAAa,IAAI,EAAE,cAAc,YAAY,EAAE,GAAG,OAAO,EACtE,oBAEA,6LAAC,wKAAA,CAAA,OAAuB;QACtB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAEF,YAAY;QACZ,aAAa;QACb,KAAK;QACJ,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,wKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4922, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as SwitchPrimitives from '@radix-ui/react-switch';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst Switch = React.forwardRef<\r\n  React.ElementRef<typeof SwitchPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <SwitchPrimitives.Root\r\n    className={cn(\r\n      'peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input',\r\n      className\r\n    )}\r\n    {...props}\r\n    ref={ref}\r\n  >\r\n    <SwitchPrimitives.Thumb\r\n      className={cn(\r\n        'pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0'\r\n      )}\r\n    />\r\n  </SwitchPrimitives.Root>\r\n));\r\nSwitch.displayName = SwitchPrimitives.Root.displayName;\r\n\r\nexport { Switch };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6LAAC,qKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4966, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/settings/FontSizeSettings.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Check, Type } from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport { useUiPreferences } from '@/hooks/ui/useUiPreferences';\r\n\r\n/**\r\n * Font size options with display labels and descriptions\r\n */\r\nconst FONT_SIZE_OPTIONS = [\r\n  {\r\n    className: 'text-sm',\r\n    description: 'Compact text for more content',\r\n    example: 'The quick brown fox jumps over the lazy dog',\r\n    label: 'Small',\r\n    value: 'small' as const,\r\n  },\r\n  {\r\n    className: 'text-base',\r\n    description: 'Standard readable text',\r\n    example: 'The quick brown fox jumps over the lazy dog',\r\n    label: 'Medium',\r\n    value: 'medium' as const,\r\n  },\r\n  {\r\n    className: 'text-lg',\r\n    description: 'Larger text for better accessibility',\r\n    example: 'The quick brown fox jumps over the lazy dog',\r\n    label: 'Large',\r\n    value: 'large' as const,\r\n  },\r\n];\r\n\r\n/**\r\n * Font Size Settings Component\r\n * Provides UI controls for changing application font size\r\n */\r\nexport const FontSizeSettings: React.FC = () => {\r\n  const { fontSize, getFontSizeClass, setFontSize } = useUiPreferences();\r\n\r\n  return (\r\n    <Card className=\"w-full max-w-2xl\">\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center gap-2\">\r\n          <Type className=\"size-5\" />\r\n          Font Size Preferences\r\n        </CardTitle>\r\n        <CardDescription>\r\n          Choose your preferred font size for better readability and\r\n          accessibility\r\n        </CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        {/* Current Selection Display */}\r\n        <div className=\"flex items-center justify-between rounded-lg bg-muted/50 p-4\">\r\n          <div>\r\n            <p className=\"font-medium\">Current Font Size</p>\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              Applied across the entire application\r\n            </p>\r\n          </div>\r\n          <Badge className=\"capitalize\" variant=\"secondary\">\r\n            {fontSize}\r\n          </Badge>\r\n        </div>\r\n\r\n        {/* Font Size Options */}\r\n        <div className=\"space-y-3\">\r\n          <h4 className=\"text-sm font-medium uppercase tracking-wide text-muted-foreground\">\r\n            Available Options\r\n          </h4>\r\n          {FONT_SIZE_OPTIONS.map(option => (\r\n            <div\r\n              className={`\r\n                relative cursor-pointer rounded-lg border p-4 transition-all\r\n                ${\r\n                  fontSize === option.value\r\n                    ? 'border-primary bg-primary/5 ring-1 ring-primary/20'\r\n                    : 'border-border hover:border-primary/50 hover:bg-muted/30'\r\n                }\r\n              `}\r\n              key={option.value}\r\n              onClick={() => setFontSize(option.value)}\r\n            >\r\n              <div className=\"flex items-start justify-between\">\r\n                <div className=\"flex-1\">\r\n                  <div className=\"mb-2 flex items-center gap-2\">\r\n                    <h5 className=\"font-medium\">{option.label}</h5>\r\n                    {fontSize === option.value && (\r\n                      <Check className=\"size-4 text-primary\" />\r\n                    )}\r\n                  </div>\r\n                  <p className=\"mb-3 text-sm text-muted-foreground\">\r\n                    {option.description}\r\n                  </p>\r\n                  <div\r\n                    className={`rounded border bg-background p-3 ${option.className}`}\r\n                  >\r\n                    {option.example}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Quick Action Buttons */}\r\n        <div className=\"flex items-center justify-between border-t pt-4\">\r\n          <div className=\"flex gap-2\">\r\n            {FONT_SIZE_OPTIONS.map(option => (\r\n              <Button\r\n                className=\"capitalize\"\r\n                key={option.value}\r\n                onClick={() => setFontSize(option.value)}\r\n                size=\"sm\"\r\n                variant={fontSize === option.value ? 'default' : 'outline'}\r\n              >\r\n                {option.label}\r\n              </Button>\r\n            ))}\r\n          </div>\r\n          <Button\r\n            className=\"text-muted-foreground\"\r\n            onClick={() => setFontSize('medium')}\r\n            size=\"sm\"\r\n            variant=\"ghost\"\r\n          >\r\n            Reset to Default\r\n          </Button>\r\n        </div>\r\n\r\n        {/* Live Preview */}\r\n        <div className=\"rounded-lg border bg-background p-4\">\r\n          <h5 className=\"mb-2 font-medium\">Live Preview</h5>\r\n          <div className={`space-y-2 ${getFontSizeClass()}`}>\r\n            <p className=\"font-semibold\">Heading Text</p>\r\n            <p>\r\n              This is how regular paragraph text will appear with your selected\r\n              font size. The setting applies to all text content throughout the\r\n              WorkHub application.\r\n            </p>\r\n            <p className=\"text-muted-foreground\">\r\n              Secondary text and descriptions will also scale accordingly.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\n/**\r\n * Compact Font Size Selector for toolbars or settings panels\r\n */\r\nexport const FontSizeSelector: React.FC = () => {\r\n  const { fontSize, setFontSize } = useUiPreferences();\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <Type className=\"size-4 text-muted-foreground\" />\r\n      <select\r\n        className=\"\r\n          rounded border border-input bg-background px-2 py-1 text-sm\r\n          focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\r\n        \"\r\n        onChange={e =>\r\n          setFontSize(e.target.value as 'large' | 'medium' | 'small')\r\n        }\r\n        value={fontSize}\r\n      >\r\n        <option value=\"small\">Small</option>\r\n        <option value=\"medium\">Medium</option>\r\n        <option value=\"large\">Large</option>\r\n      </select>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * Icon-based Font Size Toggle for minimal UI\r\n */\r\nexport const FontSizeToggle: React.FC = () => {\r\n  const { fontSize, setFontSize } = useUiPreferences();\r\n\r\n  const cycleSize = () => {\r\n    const sizes: ('large' | 'medium' | 'small')[] = [\r\n      'small',\r\n      'medium',\r\n      'large',\r\n    ];\r\n    const currentIndex = sizes.indexOf(fontSize);\r\n    const nextIndex = (currentIndex + 1) % sizes.length;\r\n    const nextSize = sizes[nextIndex];\r\n    if (nextSize) {\r\n      setFontSize(nextSize);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Button\r\n      className=\"flex items-center gap-2\"\r\n      onClick={cycleSize}\r\n      size=\"sm\"\r\n      title={`Current: ${fontSize}. Click to cycle.`}\r\n      variant=\"ghost\"\r\n    >\r\n      <Type className=\"size-4\" />\r\n      <span className=\"text-xs capitalize\">{fontSize}</span>\r\n    </Button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAAA;AAGA;AACA;AACA;AAOA;;;AAdA;;;;;;AAgBA;;CAEC,GACD,MAAM,oBAAoB;IACxB;QACE,WAAW;QACX,aAAa;QACb,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,WAAW;QACX,aAAa;QACb,SAAS;QACT,OAAO;QACP,OAAO;IACT;IACA;QACE,WAAW;QACX,aAAa;QACb,SAAS;QACT,OAAO;QACP,OAAO;IACT;CACD;AAMM,MAAM,mBAA6B;;IACxC,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAEnE,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAW;;;;;;;kCAG7B,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAKnB,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAAc;;;;;;kDAC3B,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAI/C,6LAAC,oIAAA,CAAA,QAAK;gCAAC,WAAU;gCAAa,SAAQ;0CACnC;;;;;;;;;;;;kCAKL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoE;;;;;;4BAGjF,kBAAkB,GAAG,CAAC,CAAA,uBACrB,6LAAC;oCACC,WAAW,CAAC;;gBAEV,EACE,aAAa,OAAO,KAAK,GACrB,uDACA,0DACL;cACH,CAAC;oCAED,SAAS,IAAM,YAAY,OAAO,KAAK;8CAEvC,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAe,OAAO,KAAK;;;;;;wDACxC,aAAa,OAAO,KAAK,kBACxB,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;8DAGrB,6LAAC;oDAAE,WAAU;8DACV,OAAO,WAAW;;;;;;8DAErB,6LAAC;oDACC,WAAW,CAAC,iCAAiC,EAAE,OAAO,SAAS,EAAE;8DAEhE,OAAO,OAAO;;;;;;;;;;;;;;;;;mCAjBhB,OAAO,KAAK;;;;;;;;;;;kCA0BvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,kBAAkB,GAAG,CAAC,CAAA,uBACrB,6LAAC,qIAAA,CAAA,SAAM;wCACL,WAAU;wCAEV,SAAS,IAAM,YAAY,OAAO,KAAK;wCACvC,MAAK;wCACL,SAAS,aAAa,OAAO,KAAK,GAAG,YAAY;kDAEhD,OAAO,KAAK;uCALR,OAAO,KAAK;;;;;;;;;;0CASvB,6LAAC,qIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,IAAM,YAAY;gCAC3B,MAAK;gCACL,SAAQ;0CACT;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmB;;;;;;0CACjC,6LAAC;gCAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;kDAC/C,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAC7B,6LAAC;kDAAE;;;;;;kDAKH,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GAhHa;;QACyC,yIAAA,CAAA,mBAAgB;;;KADzD;AAqHN,MAAM,mBAA6B;;IACxC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAEjD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;0BAChB,6LAAC;gBACC,WAAU;gBAIV,UAAU,CAAA,IACR,YAAY,EAAE,MAAM,CAAC,KAAK;gBAE5B,OAAO;;kCAEP,6LAAC;wBAAO,OAAM;kCAAQ;;;;;;kCACtB,6LAAC;wBAAO,OAAM;kCAAS;;;;;;kCACvB,6LAAC;wBAAO,OAAM;kCAAQ;;;;;;;;;;;;;;;;;;AAI9B;IAtBa;;QACuB,yIAAA,CAAA,mBAAgB;;;MADvC;AA2BN,MAAM,iBAA2B;;IACtC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAEjD,MAAM,YAAY;QAChB,MAAM,QAA0C;YAC9C;YACA;YACA;SACD;QACD,MAAM,eAAe,MAAM,OAAO,CAAC;QACnC,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,MAAM,MAAM;QACnD,MAAM,WAAW,KAAK,CAAC,UAAU;QACjC,IAAI,UAAU;YACZ,YAAY;QACd;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,WAAU;QACV,SAAS;QACT,MAAK;QACL,OAAO,CAAC,SAAS,EAAE,SAAS,iBAAiB,CAAC;QAC9C,SAAQ;;0BAER,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;0BAChB,6LAAC;gBAAK,WAAU;0BAAsB;;;;;;;;;;;;AAG5C;IA7Ba;;QACuB,yIAAA,CAAA,mBAAgB;;;MADvC", "debugId": null}}, {"offset": {"line": 5409, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/settings/ThemeSettings.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> } from 'lucide-react';\r\nimport { useTheme as useNextTheme } from 'next-themes';\r\nimport React from 'react';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport { useTheme } from '@/hooks/ui/useTheme';\r\n\r\n/**\r\n * Theme options with display information\r\n */\r\nconst THEME_OPTIONS = [\r\n  {\r\n    description: 'Clean and bright interface',\r\n    icon: Sun,\r\n    label: 'Light',\r\n    preview: 'bg-white border-gray-200 text-gray-900',\r\n    value: 'light' as const,\r\n  },\r\n  {\r\n    description: 'Easy on the eyes in low light',\r\n    icon: Moon,\r\n    label: 'Dark',\r\n    preview: 'bg-gray-900 border-gray-700 text-white',\r\n    value: 'dark' as const,\r\n  },\r\n  {\r\n    description: 'Follows your device settings',\r\n    icon: Monitor,\r\n    label: 'System',\r\n    preview:\r\n      'bg-gradient-to-r from-white to-gray-900 border-gray-400 text-gray-600',\r\n    value: 'system' as const,\r\n  },\r\n];\r\n\r\n/**\r\n * Theme Settings Component\r\n * Provides comprehensive theme management interface\r\n */\r\nexport const ThemeSettings: React.FC = () => {\r\n  const { currentTheme } = useTheme();\r\n  const {\r\n    setTheme: setNextTheme,\r\n    systemTheme,\r\n    theme: nextTheme,\r\n  } = useNextTheme();\r\n  const { setTheme: setZustandTheme } = useTheme();\r\n\r\n  // Enhanced theme switching that updates both systems\r\n  const handleThemeChange = (newTheme: 'dark' | 'light' | 'system') => {\r\n    setNextTheme(newTheme);\r\n    if (newTheme === 'system') {\r\n      // If system theme is selected, update Zustand with the actual system preference\r\n      const systemPreference = systemTheme || 'light';\r\n      setZustandTheme(systemPreference as 'dark' | 'light');\r\n    } else {\r\n      setZustandTheme(newTheme);\r\n    }\r\n  };\r\n\r\n  const effectiveTheme = nextTheme || 'system';\r\n\r\n  return (\r\n    <Card className=\"w-full max-w-2xl\">\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center gap-2\">\r\n          <Palette className=\"size-5\" />\r\n          Theme Preferences\r\n        </CardTitle>\r\n        <CardDescription>\r\n          Choose your preferred color scheme and appearance settings\r\n        </CardDescription>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        {/* Current Theme Display */}\r\n        <div className=\"flex items-center justify-between rounded-lg bg-muted/50 p-4\">\r\n          <div>\r\n            <p className=\"font-medium\">Current Theme</p>\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              Applied across the entire application\r\n            </p>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Badge className=\"capitalize\" variant=\"secondary\">\r\n              {effectiveTheme}\r\n            </Badge>\r\n            {effectiveTheme === 'system' && systemTheme && (\r\n              <Badge className=\"text-xs\" variant=\"outline\">\r\n                System: {systemTheme}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Theme Options */}\r\n        <div className=\"space-y-3\">\r\n          <h4 className=\"text-sm font-medium uppercase tracking-wide text-muted-foreground\">\r\n            Available Themes\r\n          </h4>\r\n          {THEME_OPTIONS.map(option => {\r\n            const IconComponent = option.icon;\r\n            const isSelected = effectiveTheme === option.value;\r\n\r\n            return (\r\n              <div\r\n                className={`\r\n                  relative cursor-pointer rounded-lg border p-4 transition-all\r\n                  ${\r\n                    isSelected\r\n                      ? 'border-primary bg-primary/5 ring-1 ring-primary/20'\r\n                      : 'border-border hover:border-primary/50 hover:bg-muted/30'\r\n                  }\r\n                `}\r\n                key={option.value}\r\n                onClick={() => handleThemeChange(option.value)}\r\n              >\r\n                <div className=\"flex items-start justify-between\">\r\n                  <div className=\"flex flex-1 items-start gap-3\">\r\n                    <div className=\"flex size-10 items-center justify-center rounded-lg border bg-background\">\r\n                      <IconComponent className=\"size-5\" />\r\n                    </div>\r\n                    <div className=\"flex-1\">\r\n                      <div className=\"mb-1 flex items-center gap-2\">\r\n                        <h5 className=\"font-medium\">{option.label}</h5>\r\n                        {isSelected && (\r\n                          <Check className=\"size-4 text-primary\" />\r\n                        )}\r\n                      </div>\r\n                      <p className=\"mb-3 text-sm text-muted-foreground\">\r\n                        {option.description}\r\n                      </p>\r\n                      {/* Theme Preview */}\r\n                      <div\r\n                        className={`\r\n                        h-8 w-full rounded border-2 ${option.preview}\r\n                        flex items-center justify-center text-xs font-medium\r\n                      `}\r\n                      >\r\n                        Preview\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        {/* Quick Action Buttons */}\r\n        <div className=\"flex items-center justify-between border-t pt-4\">\r\n          <div className=\"flex gap-2\">\r\n            {THEME_OPTIONS.map(option => {\r\n              const IconComponent = option.icon;\r\n              return (\r\n                <Button\r\n                  className=\"flex items-center gap-2\"\r\n                  key={option.value}\r\n                  onClick={() => handleThemeChange(option.value)}\r\n                  size=\"sm\"\r\n                  variant={\r\n                    effectiveTheme === option.value ? 'default' : 'outline'\r\n                  }\r\n                >\r\n                  <IconComponent className=\"size-4\" />\r\n                  {option.label}\r\n                </Button>\r\n              );\r\n            })}\r\n          </div>\r\n          <Button\r\n            className=\"text-muted-foreground\"\r\n            onClick={() => handleThemeChange('system')}\r\n            size=\"sm\"\r\n            variant=\"ghost\"\r\n          >\r\n            Reset to System\r\n          </Button>\r\n        </div>\r\n\r\n        {/* Theme Information */}\r\n        <div className=\"rounded-lg border bg-background p-4\">\r\n          <h5 className=\"mb-2 font-medium\">Theme Information</h5>\r\n          <div className=\"space-y-2 text-sm text-muted-foreground\">\r\n            <div className=\"flex justify-between\">\r\n              <span>Selected Theme:</span>\r\n              <span className=\"font-medium capitalize\">{effectiveTheme}</span>\r\n            </div>\r\n            <div className=\"flex justify-between\">\r\n              <span>Zustand Store:</span>\r\n              <span className=\"font-medium capitalize\">{currentTheme}</span>\r\n            </div>\r\n            {systemTheme && (\r\n              <div className=\"flex justify-between\">\r\n                <span>System Preference:</span>\r\n                <span className=\"font-medium capitalize\">{systemTheme}</span>\r\n              </div>\r\n            )}\r\n            <div className=\"flex justify-between\">\r\n              <span>Auto-sync:</span>\r\n              <span className=\"font-medium\">\r\n                {effectiveTheme === 'system' ? 'Enabled' : 'Disabled'}\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\n/**\r\n * Compact Theme Selector for toolbars or settings panels\r\n */\r\nexport const ThemeSelector: React.FC = () => {\r\n  const { setTheme: setNextTheme, theme: nextTheme } = useNextTheme();\r\n  const { setTheme: setZustandTheme } = useTheme();\r\n\r\n  const handleThemeChange = (newTheme: 'dark' | 'light' | 'system') => {\r\n    setNextTheme(newTheme);\r\n    if (newTheme !== 'system') {\r\n      setZustandTheme(newTheme);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <Palette className=\"size-4 text-muted-foreground\" />\r\n      <select\r\n        className=\"\r\n          rounded border border-input bg-background px-2 py-1 text-sm\r\n          focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\r\n        \"\r\n        onChange={e =>\r\n          handleThemeChange(e.target.value as 'dark' | 'light' | 'system')\r\n        }\r\n        value={nextTheme || 'system'}\r\n      >\r\n        <option value=\"light\">Light</option>\r\n        <option value=\"dark\">Dark</option>\r\n        <option value=\"system\">System</option>\r\n      </select>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * Icon-based Theme Toggle for minimal UI\r\n */\r\nexport const ThemeToggleCompact: React.FC = () => {\r\n  const { setTheme: setNextTheme, theme: nextTheme } = useNextTheme();\r\n  const { setTheme: setZustandTheme } = useTheme();\r\n\r\n  const cycleTheme = () => {\r\n    const themes: ('dark' | 'light' | 'system')[] = ['light', 'dark', 'system'];\r\n    const currentIndex = themes.indexOf((nextTheme as any) || 'system');\r\n    const nextIndex = (currentIndex + 1) % themes.length;\r\n    const newTheme = themes[nextIndex];\r\n    if (newTheme) {\r\n      setNextTheme(newTheme);\r\n      if (setZustandTheme && (newTheme === 'light' || newTheme === 'dark')) {\r\n        setZustandTheme(newTheme);\r\n      }\r\n    }\r\n  };\r\n\r\n  const getIcon = () => {\r\n    switch (nextTheme) {\r\n      case 'dark': {\r\n        return Moon;\r\n      }\r\n      case 'light': {\r\n        return Sun;\r\n      }\r\n      default: {\r\n        return Monitor;\r\n      }\r\n    }\r\n  };\r\n\r\n  const IconComponent = getIcon();\r\n\r\n  return (\r\n    <Button\r\n      className=\"flex items-center gap-2\"\r\n      onClick={cycleTheme}\r\n      size=\"sm\"\r\n      title={`Current: ${nextTheme || 'system'}. Click to cycle.`}\r\n      variant=\"ghost\"\r\n    >\r\n      <IconComponent className=\"size-4\" />\r\n      <span className=\"text-xs capitalize\">{nextTheme || 'system'}</span>\r\n    </Button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AAGA;AACA;AACA;AAOA;;;AAfA;;;;;;;AAiBA;;CAEC,GACD,MAAM,gBAAgB;IACpB;QACE,aAAa;QACb,MAAM,mMAAA,CAAA,MAAG;QACT,OAAO;QACP,SAAS;QACT,OAAO;IACT;IACA;QACE,aAAa;QACb,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,SAAS;QACT,OAAO;IACT;IACA;QACE,aAAa;QACb,MAAM,2MAAA,CAAA,UAAO;QACb,OAAO;QACP,SACE;QACF,OAAO;IACT;CACD;AAMM,MAAM,gBAA0B;;IACrC,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,WAAQ,AAAD;IAChC,MAAM,EACJ,UAAU,YAAY,EACtB,WAAW,EACX,OAAO,SAAS,EACjB,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAY,AAAD;IACf,MAAM,EAAE,UAAU,eAAe,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,WAAQ,AAAD;IAE7C,qDAAqD;IACrD,MAAM,oBAAoB,CAAC;QACzB,aAAa;QACb,IAAI,aAAa,UAAU;YACzB,gFAAgF;YAChF,MAAM,mBAAmB,eAAe;YACxC,gBAAgB;QAClB,OAAO;YACL,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB,aAAa;IAEpC,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAAW;;;;;;;kCAGhC,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAAc;;;;;;kDAC3B,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAI/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;wCAAa,SAAQ;kDACnC;;;;;;oCAEF,mBAAmB,YAAY,6BAC9B,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;wCAAU,SAAQ;;4CAAU;4CAClC;;;;;;;;;;;;;;;;;;;kCAOjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoE;;;;;;4BAGjF,cAAc,GAAG,CAAC,CAAA;gCACjB,MAAM,gBAAgB,OAAO,IAAI;gCACjC,MAAM,aAAa,mBAAmB,OAAO,KAAK;gCAElD,qBACE,6LAAC;oCACC,WAAW,CAAC;;kBAEV,EACE,aACI,uDACA,0DACL;gBACH,CAAC;oCAED,SAAS,IAAM,kBAAkB,OAAO,KAAK;8CAE7C,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAc,WAAU;;;;;;;;;;;8DAE3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAe,OAAO,KAAK;;;;;;gEACxC,4BACC,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;;sEAGrB,6LAAC;4DAAE,WAAU;sEACV,OAAO,WAAW;;;;;;sEAGrB,6LAAC;4DACC,WAAW,CAAC;oDACgB,EAAE,OAAO,OAAO,CAAC;;sBAE/C,CAAC;sEACA;;;;;;;;;;;;;;;;;;;;;;;mCAxBF,OAAO,KAAK;;;;;4BAgCvB;;;;;;;kCAIF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAA;oCACjB,MAAM,gBAAgB,OAAO,IAAI;oCACjC,qBACE,6LAAC,qIAAA,CAAA,SAAM;wCACL,WAAU;wCAEV,SAAS,IAAM,kBAAkB,OAAO,KAAK;wCAC7C,MAAK;wCACL,SACE,mBAAmB,OAAO,KAAK,GAAG,YAAY;;0DAGhD,6LAAC;gDAAc,WAAU;;;;;;4CACxB,OAAO,KAAK;;uCARR,OAAO,KAAK;;;;;gCAWvB;;;;;;0CAEF,6LAAC,qIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB;gCACjC,MAAK;gCACL,SAAQ;0CACT;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmB;;;;;;0CACjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;0DAA0B;;;;;;;;;;;;kDAE5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;0DAA0B;;;;;;;;;;;;oCAE3C,6BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;0DAA0B;;;;;;;;;;;;kDAG9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;0DACb,mBAAmB,WAAW,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3D;GAzKa;;QACc,iIAAA,CAAA,WAAQ;QAK7B,mJAAA,CAAA,WAAY;QACsB,iIAAA,CAAA,WAAQ;;;KAPnC;AA8KN,MAAM,gBAA0B;;IACrC,MAAM,EAAE,UAAU,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAY,AAAD;IAChE,MAAM,EAAE,UAAU,eAAe,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,WAAQ,AAAD;IAE7C,MAAM,oBAAoB,CAAC;QACzB,aAAa;QACb,IAAI,aAAa,UAAU;YACzB,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;0BACnB,6LAAC;gBACC,WAAU;gBAIV,UAAU,CAAA,IACR,kBAAkB,EAAE,MAAM,CAAC,KAAK;gBAElC,OAAO,aAAa;;kCAEpB,6LAAC;wBAAO,OAAM;kCAAQ;;;;;;kCACtB,6LAAC;wBAAO,OAAM;kCAAO;;;;;;kCACrB,6LAAC;wBAAO,OAAM;kCAAS;;;;;;;;;;;;;;;;;;AAI/B;IA9Ba;;QAC0C,mJAAA,CAAA,WAAY;QAC3B,iIAAA,CAAA,WAAQ;;;MAFnC;AAmCN,MAAM,qBAA+B;;IAC1C,MAAM,EAAE,UAAU,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAY,AAAD;IAChE,MAAM,EAAE,UAAU,eAAe,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,WAAQ,AAAD;IAE7C,MAAM,aAAa;QACjB,MAAM,SAA0C;YAAC;YAAS;YAAQ;SAAS;QAC3E,MAAM,eAAe,OAAO,OAAO,CAAC,AAAC,aAAqB;QAC1D,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,OAAO,MAAM;QACpD,MAAM,WAAW,MAAM,CAAC,UAAU;QAClC,IAAI,UAAU;YACZ,aAAa;YACb,IAAI,mBAAmB,CAAC,aAAa,WAAW,aAAa,MAAM,GAAG;gBACpE,gBAAgB;YAClB;QACF;IACF;IAEA,MAAM,UAAU;QACd,OAAQ;YACN,KAAK;gBAAQ;oBACX,OAAO,qMAAA,CAAA,OAAI;gBACb;YACA,KAAK;gBAAS;oBACZ,OAAO,mMAAA,CAAA,MAAG;gBACZ;YACA;gBAAS;oBACP,OAAO,2MAAA,CAAA,UAAO;gBAChB;QACF;IACF;IAEA,MAAM,gBAAgB;IAEtB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,WAAU;QACV,SAAS;QACT,MAAK;QACL,OAAO,CAAC,SAAS,EAAE,aAAa,SAAS,iBAAiB,CAAC;QAC3D,SAAQ;;0BAER,6LAAC;gBAAc,WAAU;;;;;;0BACzB,6LAAC;gBAAK,WAAU;0BAAsB,aAAa;;;;;;;;;;;;AAGzD;IA7Ca;;QAC0C,mJAAA,CAAA,WAAY;QAC3B,iIAAA,CAAA,WAAQ;;;MAFnC", "debugId": null}}, {"offset": {"line": 6039, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/settings/SettingsModal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport {\r\n  <PERSON>,\r\n  Layout,\r\n  <PERSON>,\r\n  <PERSON>,\r\n  <PERSON><PERSON>,\r\n  <PERSON>otateCc<PERSON>,\r\n  <PERSON>ting<PERSON>,\r\n  <PERSON>,\r\n  Type,\r\n  X,\r\n} from 'lucide-react';\r\nimport { useTheme as useNextTheme } from 'next-themes';\r\nimport React from 'react';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { useModal } from '@/lib/hooks/useModal';\r\nimport { useTheme } from '@/hooks/ui/useTheme';\r\nimport { useUiPreferences } from '@/hooks/ui/useUiPreferences';\r\n\r\nimport { FontSizeSettings } from './FontSizeSettings';\r\nimport { ThemeSettings } from './ThemeSettings';\r\n\r\n/**\r\n * Settings Modal Component\r\n * Comprehensive settings interface using the modal system\r\n */\r\nexport const SettingsModal: React.FC = () => {\r\n  const { closeModal, isModalOpen, modalContent } = useModal();\r\n  const {\r\n    dashboardLayout,\r\n    fontSize,\r\n    getAllPreferences,\r\n    notificationsEnabled,\r\n    resetPreferences,\r\n    setDashboardLayout,\r\n    setFontSize,\r\n    setTableDensity,\r\n    tableDensity,\r\n    toggleNotifications,\r\n  } = useUiPreferences();\r\n\r\n  // Theme management\r\n  const { currentTheme, setTheme: setZustandTheme } = useTheme();\r\n  const {\r\n    setTheme: setNextTheme,\r\n    systemTheme,\r\n    theme: nextTheme,\r\n  } = useNextTheme();\r\n\r\n  const isOpen = isModalOpen && modalContent === 'settings';\r\n\r\n  // Enhanced theme switching that updates both systems\r\n  const handleThemeChange = (newTheme: 'dark' | 'light' | 'system') => {\r\n    setNextTheme(newTheme);\r\n    if (newTheme === 'system') {\r\n      // If system theme is selected, update Zustand with the actual system preference\r\n      const systemPreference = systemTheme || 'light';\r\n      setZustandTheme(systemPreference as 'dark' | 'light');\r\n    } else {\r\n      setZustandTheme(newTheme);\r\n    }\r\n  };\r\n\r\n  const handleResetPreferences = () => {\r\n    resetPreferences();\r\n    // Reset theme to system default\r\n    handleThemeChange('system');\r\n    // You could add a toast notification here\r\n  };\r\n\r\n  return (\r\n    <Dialog onOpenChange={open => !open && closeModal()} open={isOpen}>\r\n      <DialogContent className=\"max-h-[90vh] max-w-4xl overflow-y-auto\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"flex items-center gap-2\">\r\n            <Settings className=\"size-5\" />\r\n            Application Settings\r\n          </DialogTitle>\r\n          <DialogDescription>\r\n            Customize your WorkHub experience with these preferences\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-6\">\r\n          {/* Theme Settings */}\r\n          <div>\r\n            <h3 className=\"mb-4 flex items-center gap-2 text-lg font-semibold\">\r\n              <Palette className=\"size-5\" />\r\n              Theme Preferences\r\n            </h3>\r\n            <ThemeSettings />\r\n          </div>\r\n\r\n          <Separator />\r\n\r\n          {/* Font Size Settings */}\r\n          <div>\r\n            <h3 className=\"mb-4 flex items-center gap-2 text-lg font-semibold\">\r\n              <Type className=\"size-5\" />\r\n              Display Preferences\r\n            </h3>\r\n            <FontSizeSettings />\r\n          </div>\r\n\r\n          <Separator />\r\n\r\n          {/* Notification Settings */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <Bell className=\"size-5\" />\r\n                Notifications\r\n              </CardTitle>\r\n              <CardDescription>\r\n                Control how you receive notifications and alerts\r\n              </CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <p className=\"font-medium\">Enable Notifications</p>\r\n                  <p className=\"text-sm text-muted-foreground\">\r\n                    Receive system notifications and updates\r\n                  </p>\r\n                </div>\r\n                <Switch\r\n                  checked={notificationsEnabled}\r\n                  onCheckedChange={toggleNotifications}\r\n                />\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Layout Settings */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <Layout className=\"size-5\" />\r\n                Layout Preferences\r\n              </CardTitle>\r\n              <CardDescription>\r\n                Customize the layout and density of interface elements\r\n              </CardDescription>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-6\">\r\n              {/* Table Density */}\r\n              <div>\r\n                <h5 className=\"mb-3 font-medium\">Table Density</h5>\r\n                <div className=\"flex gap-2\">\r\n                  {(['compact', 'comfortable', 'spacious'] as const).map(\r\n                    density => (\r\n                      <Button\r\n                        className=\"capitalize\"\r\n                        key={density}\r\n                        onClick={() => setTableDensity(density)}\r\n                        size=\"sm\"\r\n                        variant={\r\n                          tableDensity === density ? 'default' : 'outline'\r\n                        }\r\n                      >\r\n                        {density}\r\n                      </Button>\r\n                    )\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Dashboard Layout */}\r\n              <div>\r\n                <h5 className=\"mb-3 font-medium\">Dashboard Layout</h5>\r\n                <div className=\"flex gap-2\">\r\n                  {(['grid', 'list', 'cards'] as const).map(layout => (\r\n                    <Button\r\n                      className=\"capitalize\"\r\n                      key={layout}\r\n                      onClick={() => setDashboardLayout(layout)}\r\n                      size=\"sm\"\r\n                      variant={\r\n                        dashboardLayout === layout ? 'default' : 'outline'\r\n                      }\r\n                    >\r\n                      {layout}\r\n                    </Button>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Current Settings Summary */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Current Settings Summary</CardTitle>\r\n              <CardDescription>\r\n                Overview of your current preferences\r\n              </CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4\">\r\n                <div className=\"space-y-1\">\r\n                  <p className=\"text-sm font-medium\">Theme</p>\r\n                  <Badge className=\"capitalize\" variant=\"secondary\">\r\n                    {nextTheme || 'system'}\r\n                  </Badge>\r\n                </div>\r\n                <div className=\"space-y-1\">\r\n                  <p className=\"text-sm font-medium\">Font Size</p>\r\n                  <Badge className=\"capitalize\" variant=\"secondary\">\r\n                    {fontSize}\r\n                  </Badge>\r\n                </div>\r\n                <div className=\"space-y-1\">\r\n                  <p className=\"text-sm font-medium\">Notifications</p>\r\n                  <Badge\r\n                    variant={notificationsEnabled ? 'default' : 'secondary'}\r\n                  >\r\n                    {notificationsEnabled ? 'Enabled' : 'Disabled'}\r\n                  </Badge>\r\n                </div>\r\n                <div className=\"space-y-1\">\r\n                  <p className=\"text-sm font-medium\">Table Density</p>\r\n                  <Badge className=\"capitalize\" variant=\"secondary\">\r\n                    {tableDensity}\r\n                  </Badge>\r\n                </div>\r\n                <div className=\"space-y-1\">\r\n                  <p className=\"text-sm font-medium\">Dashboard Layout</p>\r\n                  <Badge className=\"capitalize\" variant=\"secondary\">\r\n                    {dashboardLayout}\r\n                  </Badge>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"flex items-center justify-between border-t pt-4\">\r\n            <Button\r\n              className=\"flex items-center gap-2\"\r\n              onClick={handleResetPreferences}\r\n              variant=\"outline\"\r\n            >\r\n              <RotateCcw className=\"size-4\" />\r\n              Reset to Defaults\r\n            </Button>\r\n            <div className=\"flex gap-2\">\r\n              <Button onClick={closeModal} variant=\"outline\">\r\n                Cancel\r\n              </Button>\r\n              <Button onClick={closeModal}>Save Changes</Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\n/**\r\n * Settings Button Component\r\n * Trigger button to open the settings modal\r\n */\r\nexport const SettingsButton: React.FC<{\r\n  variant?: 'default' | 'ghost' | 'outline';\r\n}> = ({ variant = 'ghost' }) => {\r\n  const { openSettingsModal } = useModal();\r\n\r\n  return (\r\n    <Button\r\n      className=\"flex items-center gap-2\"\r\n      onClick={openSettingsModal}\r\n      size=\"sm\"\r\n      variant={variant}\r\n    >\r\n      <Settings className=\"size-4\" />\r\n      Settings\r\n    </Button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAGA;AACA;AACA;AAOA;AAOA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;;;AAxCA;;;;;;;;;;;;;;AA8CO,MAAM,gBAA0B;;IACrC,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,WAAQ,AAAD;IACzD,MAAM,EACJ,eAAe,EACf,QAAQ,EACR,iBAAiB,EACjB,oBAAoB,EACpB,gBAAgB,EAChB,kBAAkB,EAClB,WAAW,EACX,eAAe,EACf,YAAY,EACZ,mBAAmB,EACpB,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAEnB,mBAAmB;IACnB,MAAM,EAAE,YAAY,EAAE,UAAU,eAAe,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,WAAQ,AAAD;IAC3D,MAAM,EACJ,UAAU,YAAY,EACtB,WAAW,EACX,OAAO,SAAS,EACjB,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAY,AAAD;IAEf,MAAM,SAAS,eAAe,iBAAiB;IAE/C,qDAAqD;IACrD,MAAM,oBAAoB,CAAC;QACzB,aAAa;QACb,IAAI,aAAa,UAAU;YACzB,gFAAgF;YAChF,MAAM,mBAAmB,eAAe;YACxC,gBAAgB;QAClB,OAAO;YACL,gBAAgB;QAClB;IACF;IAEA,MAAM,yBAAyB;QAC7B;QACA,gCAAgC;QAChC,kBAAkB;IAClB,0CAA0C;IAC5C;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,cAAc,CAAA,OAAQ,CAAC,QAAQ;QAAc,MAAM;kBACzD,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAW;;;;;;;sCAGjC,6LAAC,qIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAW;;;;;;;8CAGhC,6LAAC,kJAAA,CAAA,gBAAa;;;;;;;;;;;sCAGhB,6LAAC,wIAAA,CAAA,YAAS;;;;;sCAGV,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAW;;;;;;;8CAG7B,6LAAC,qJAAA,CAAA,mBAAgB;;;;;;;;;;;sCAGnB,6LAAC,wIAAA,CAAA,YAAS;;;;;sCAGV,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAW;;;;;;;sDAG7B,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAc;;;;;;kEAC3B,6LAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAI/C,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;sCAOzB,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,wNAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAW;;;;;;;sDAG/B,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDAErB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAmB;;;;;;8DACjC,6LAAC;oDAAI,WAAU;8DACZ,AAAC;wDAAC;wDAAW;wDAAe;qDAAW,CAAW,GAAG,CACpD,CAAA,wBACE,6LAAC,qIAAA,CAAA,SAAM;4DACL,WAAU;4DAEV,SAAS,IAAM,gBAAgB;4DAC/B,MAAK;4DACL,SACE,iBAAiB,UAAU,YAAY;sEAGxC;2DAPI;;;;;;;;;;;;;;;;sDAef,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAmB;;;;;;8DACjC,6LAAC;oDAAI,WAAU;8DACZ,AAAC;wDAAC;wDAAQ;wDAAQ;qDAAQ,CAAW,GAAG,CAAC,CAAA,uBACxC,6LAAC,qIAAA,CAAA,SAAM;4DACL,WAAU;4DAEV,SAAS,IAAM,mBAAmB;4DAClC,MAAK;4DACL,SACE,oBAAoB,SAAS,YAAY;sEAG1C;2DAPI;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAgBjB,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAsB;;;;;;kEACnC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;wDAAa,SAAQ;kEACnC,aAAa;;;;;;;;;;;;0DAGlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAsB;;;;;;kEACnC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;wDAAa,SAAQ;kEACnC;;;;;;;;;;;;0DAGL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAsB;;;;;;kEACnC,6LAAC,oIAAA,CAAA,QAAK;wDACJ,SAAS,uBAAuB,YAAY;kEAE3C,uBAAuB,YAAY;;;;;;;;;;;;0DAGxC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAsB;;;;;;kEACnC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;wDAAa,SAAQ;kEACnC;;;;;;;;;;;;0DAGL,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAsB;;;;;;kEACnC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;wDAAa,SAAQ;kEACnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS;oCACT,SAAQ;;sDAER,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAW;;;;;;;8CAGlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAY,SAAQ;sDAAU;;;;;;sDAG/C,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;sDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GAtOa;;QACuC,iIAAA,CAAA,WAAQ;QAYtD,yIAAA,CAAA,mBAAgB;QAGgC,iIAAA,CAAA,WAAQ;QAKxD,mJAAA,CAAA,WAAY;;;KArBL;AA4ON,MAAM,iBAER,CAAC,EAAE,UAAU,OAAO,EAAE;;IACzB,MAAM,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,WAAQ,AAAD;IAErC,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,WAAU;QACV,SAAS;QACT,MAAK;QACL,SAAS;;0BAET,6LAAC,6MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAAW;;;;;;;AAIrC;IAhBa;;QAGmB,iIAAA,CAAA,WAAQ;;;MAH3B", "debugId": null}}, {"offset": {"line": 6724, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/theme-provider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { ThemeProviderProps } from 'next-themes/dist/types';\r\n\r\nimport {\r\n  ThemeProvider as NextThemesProvider,\r\n  useTheme as useNextTheme,\r\n} from 'next-themes';\r\nimport React, { useEffect } from 'react';\r\n\r\nimport { useAppStore } from '@/lib/stores/zustand/appStore';\r\n\r\n/**\r\n * Enhanced ThemeProvider that integrates Next-themes with Zustand store\r\n */\r\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\r\n  return (\r\n    <NextThemesProvider {...props}>\r\n      <ThemeSync />\r\n      {children}\r\n    </NextThemesProvider>\r\n  );\r\n}\r\n\r\n/**\r\n * Theme synchronization component that bridges Next-themes and Zustand store\r\n */\r\nfunction ThemeSync() {\r\n  const {\r\n    setTheme: setNextTheme,\r\n    systemTheme,\r\n    theme: nextTheme,\r\n  } = useNextTheme();\r\n  const { currentTheme, setTheme: setZustandTheme } = useAppStore();\r\n  const [isInitialized, setIsInitialized] = React.useState(false);\r\n\r\n  // Initialize theme from Next-themes after hydration\r\n  useEffect(() => {\r\n    if (!isInitialized && nextTheme) {\r\n      setIsInitialized(true);\r\n\r\n      // Determine the effective theme\r\n      const effectiveTheme = nextTheme === 'system' ? systemTheme : nextTheme;\r\n\r\n      // Only update Zustand if it's different and not system\r\n      if (\r\n        effectiveTheme &&\r\n        effectiveTheme !== 'system' &&\r\n        effectiveTheme !== currentTheme\r\n      ) {\r\n        setZustandTheme(effectiveTheme as 'dark' | 'light');\r\n      }\r\n    }\r\n  }, [nextTheme, systemTheme, currentTheme, setZustandTheme, isInitialized]);\r\n\r\n  // Only sync from Zustand to Next-themes when explicitly changed via Zustand\r\n  // This prevents infinite loops\r\n  useEffect(() => {\r\n    if (\r\n      isInitialized &&\r\n      currentTheme &&\r\n      currentTheme !== nextTheme &&\r\n      nextTheme !== 'system'\r\n    ) {\r\n      // Only update if the themes are actually different\r\n      const effectiveNextTheme =\r\n        nextTheme === 'system' ? systemTheme : nextTheme;\r\n      if (currentTheme !== effectiveNextTheme) {\r\n        setNextTheme(currentTheme);\r\n      }\r\n    }\r\n  }, [currentTheme, nextTheme, systemTheme, setNextTheme, isInitialized]);\r\n\r\n  return null;\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAIA;AAEA;;;AAVA;;;;AAeO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBACE,6LAAC,mJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;;0BAC3B,6LAAC;;;;;YACA;;;;;;;AAGP;KAPgB;AAShB;;CAEC,GACD,SAAS;;IACP,MAAM,EACJ,UAAU,YAAY,EACtB,WAAW,EACX,OAAO,SAAS,EACjB,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAY,AAAD;IACf,MAAM,EAAE,YAAY,EAAE,UAAU,eAAe,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEzD,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,iBAAiB,WAAW;gBAC/B,iBAAiB;gBAEjB,gCAAgC;gBAChC,MAAM,iBAAiB,cAAc,WAAW,cAAc;gBAE9D,uDAAuD;gBACvD,IACE,kBACA,mBAAmB,YACnB,mBAAmB,cACnB;oBACA,gBAAgB;gBAClB;YACF;QACF;8BAAG;QAAC;QAAW;QAAa;QAAc;QAAiB;KAAc;IAEzE,4EAA4E;IAC5E,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IACE,iBACA,gBACA,iBAAiB,aACjB,cAAc,UACd;gBACA,mDAAmD;gBACnD,MAAM,qBACJ,cAAc,WAAW,cAAc;gBACzC,IAAI,iBAAiB,oBAAoB;oBACvC,aAAa;gBACf;YACF;QACF;8BAAG;QAAC;QAAc;QAAW;QAAa;QAAc;KAAc;IAEtE,OAAO;AACT;GA/CS;;QAKH,mJAAA,CAAA,WAAY;QACoC,8IAAA,CAAA,cAAW;;;MANxD", "debugId": null}}, {"offset": {"line": 6822, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/NotificationDisplay.tsx"], "sourcesContent": ["/**\r\n * @file Notification display component for Zustand notifications\r\n * @module components/ui/NotificationDisplay\r\n */\r\n\r\n'use client';\r\n\r\nimport {\r\n  AlertCircle,\r\n  AlertTriangle,\r\n  Briefcase,\r\n  Car,\r\n  CheckCircle,\r\n  Info,\r\n  Settings,\r\n  Users,\r\n  X,\r\n} from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { useAppStore } from '@/lib/stores/zustand/appStore';\r\nimport { cn } from '@/lib/utils';\r\n\r\n/**\r\n * Get icon for notification type\r\n */\r\nconst getNotificationIcon = (type: string) => {\r\n  switch (type) {\r\n    case 'delegation-update': {\r\n      return <Briefcase className=\"size-4\" />;\r\n    }\r\n    case 'employee-update': {\r\n      return <Users className=\"size-4\" />;\r\n    }\r\n    case 'error': {\r\n      return <AlertCircle className=\"size-4\" />;\r\n    }\r\n    case 'success': {\r\n      return <CheckCircle className=\"size-4\" />;\r\n    }\r\n    case 'task-assigned': {\r\n      return <CheckCircle className=\"size-4\" />;\r\n    }\r\n    case 'vehicle-maintenance': {\r\n      return <Car className=\"size-4\" />;\r\n    }\r\n    case 'warning': {\r\n      return <AlertTriangle className=\"size-4\" />;\r\n    }\r\n    default: {\r\n      return <Info className=\"size-4\" />;\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Get notification styling based on type\r\n */\r\nconst getNotificationStyles = (type: string) => {\r\n  switch (type) {\r\n    case 'delegation-update': {\r\n      return 'border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-950 dark:text-blue-200';\r\n    }\r\n    case 'employee-update': {\r\n      return 'border-teal-200 bg-teal-50 text-teal-800 dark:border-teal-800 dark:bg-teal-950 dark:text-teal-200';\r\n    }\r\n    case 'error': {\r\n      return 'border-red-200 bg-red-50 text-red-800 dark:border-red-800 dark:bg-red-950 dark:text-red-200';\r\n    }\r\n    case 'success': {\r\n      return 'border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950 dark:text-green-200';\r\n    }\r\n    case 'task-assigned': {\r\n      return 'border-indigo-200 bg-indigo-50 text-indigo-800 dark:border-indigo-800 dark:bg-indigo-950 dark:text-indigo-200';\r\n    }\r\n    case 'vehicle-maintenance': {\r\n      return 'border-purple-200 bg-purple-50 text-purple-800 dark:border-purple-800 dark:bg-purple-950 dark:text-purple-200';\r\n    }\r\n    case 'warning': {\r\n      return 'border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-950 dark:text-yellow-200';\r\n    }\r\n    default: {\r\n      return 'border-gray-200 bg-gray-50 text-gray-800 dark:border-gray-800 dark:bg-gray-950 dark:text-gray-200';\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Get badge variant for notification type\r\n */\r\nconst getBadgeVariant = (type: string) => {\r\n  switch (type) {\r\n    case 'error': {\r\n      return 'destructive';\r\n    }\r\n    case 'success': {\r\n      return 'default';\r\n    }\r\n    case 'warning': {\r\n      return 'secondary';\r\n    }\r\n    default: {\r\n      return 'outline';\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Notification display component\r\n */\r\nexport const NotificationDisplay: React.FC = () => {\r\n  const {\r\n    clearAllNotifications,\r\n    markNotificationAsRead,\r\n    notifications,\r\n    removeNotification,\r\n    unreadNotificationCount,\r\n  } = useAppStore();\r\n\r\n  if (notifications.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"fixed right-4 top-4 z-50 w-96 max-w-sm space-y-2\">\r\n      {/* Header with clear all button */}\r\n      {notifications.length > 1 && (\r\n        <div className=\"mb-2 flex items-center justify-between\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-sm font-medium\">Notifications</span>\r\n            {unreadNotificationCount() > 0 && (\r\n              <Badge className=\"text-xs\" variant=\"destructive\">\r\n                {unreadNotificationCount()}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n          <Button\r\n            className=\"text-xs\"\r\n            onClick={clearAllNotifications}\r\n            size=\"sm\"\r\n            variant=\"ghost\"\r\n          >\r\n            Clear All\r\n          </Button>\r\n        </div>\r\n      )}\r\n\r\n      {/* Notification list */}\r\n      <div className=\"max-h-96 space-y-2 overflow-y-auto\">\r\n        {notifications.slice(-5).map(notification => (\r\n          <Card\r\n            className={cn(\r\n              'relative transition-all duration-300 hover:shadow-md',\r\n              getNotificationStyles(notification.type),\r\n              !notification.read &&\r\n                'ring-2 ring-offset-2 ring-offset-background'\r\n            )}\r\n            key={notification.id}\r\n          >\r\n            <CardContent className=\"p-4\">\r\n              <div className=\"flex items-start gap-3\">\r\n                {/* Icon */}\r\n                <div className=\"mt-0.5 shrink-0\">\r\n                  {getNotificationIcon(notification.type)}\r\n                </div>\r\n\r\n                {/* Content */}\r\n                <div className=\"min-w-0 flex-1\">\r\n                  <div className=\"flex items-start justify-between gap-2\">\r\n                    <div className=\"flex-1\">\r\n                      {/* Type badge */}\r\n                      <Badge\r\n                        className=\"mb-1 text-xs\"\r\n                        variant={getBadgeVariant(notification.type)}\r\n                      >\r\n                        {notification.type.replace('-', ' ')}\r\n                      </Badge>\r\n\r\n                      {/* Message */}\r\n                      <p className=\"text-sm font-medium leading-tight\">\r\n                        {notification.message}\r\n                      </p>\r\n\r\n                      {/* Timestamp */}\r\n                      <p className=\"mt-1 text-xs opacity-75\">\r\n                        {new Date(notification.timestamp).toLocaleString()}\r\n                      </p>\r\n\r\n                      {/* Category and action URL */}\r\n                      {(notification.category || notification.actionUrl) && (\r\n                        <div className=\"mt-2 flex items-center gap-2\">\r\n                          {notification.category && (\r\n                            <Badge className=\"text-xs\" variant=\"outline\">\r\n                              {notification.category}\r\n                            </Badge>\r\n                          )}\r\n                          {notification.actionUrl && (\r\n                            <Button\r\n                              className=\"h-auto p-0 text-xs\"\r\n                              onClick={() =>\r\n                                window.open(notification.actionUrl, '_blank')\r\n                              }\r\n                              size=\"sm\"\r\n                              variant=\"link\"\r\n                            >\r\n                              View Details\r\n                            </Button>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* Actions */}\r\n                    <div className=\"flex flex-col gap-1\">\r\n                      <Button\r\n                        className=\"size-6 p-0\"\r\n                        onClick={() => removeNotification(notification.id)}\r\n                        size=\"sm\"\r\n                        variant=\"ghost\"\r\n                      >\r\n                        <X className=\"size-3\" />\r\n                      </Button>\r\n\r\n                      {!notification.read && (\r\n                        <Button\r\n                          className=\"size-6 p-0\"\r\n                          onClick={() =>\r\n                            markNotificationAsRead(notification.id)\r\n                          }\r\n                          size=\"sm\"\r\n                          title=\"Mark as read\"\r\n                          variant=\"ghost\"\r\n                        >\r\n                          <CheckCircle className=\"size-3\" />\r\n                        </Button>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * Notification toast component for temporary notifications\r\n */\r\nexport const NotificationToast: React.FC<{\r\n  notification: {\r\n    id: string;\r\n    message: string;\r\n    timestamp: string;\r\n    type: string;\r\n  };\r\n  onDismiss: (id: string) => void;\r\n}> = ({ notification, onDismiss }) => {\r\n  // Auto-dismiss after 5 seconds\r\n  React.useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      onDismiss(notification.id);\r\n    }, 5000);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [notification.id, onDismiss]);\r\n\r\n  return (\r\n    <Card\r\n      className={cn(\r\n        'fixed bottom-4 right-4 z-50 w-80 transition-all duration-300 animate-in slide-in-from-right',\r\n        getNotificationStyles(notification.type)\r\n      )}\r\n    >\r\n      <CardContent className=\"p-4\">\r\n        <div className=\"flex items-start gap-3\">\r\n          <div className=\"shrink-0\">\r\n            {getNotificationIcon(notification.type)}\r\n          </div>\r\n          <div className=\"flex-1\">\r\n            <p className=\"text-sm font-medium\">{notification.message}</p>\r\n          </div>\r\n          <Button\r\n            className=\"size-6 p-0\"\r\n            onClick={() => onDismiss(notification.id)}\r\n            size=\"sm\"\r\n            variant=\"ghost\"\r\n          >\r\n            <X className=\"size-3\" />\r\n          </Button>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAID;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAEA;AACA;AACA;AACA;AACA;AAAA;;;AAnBA;;;;;;;;AAqBA;;CAEC,GACD,MAAM,sBAAsB,CAAC;IAC3B,OAAQ;QACN,KAAK;YAAqB;gBACxB,qBAAO,6LAAC,+MAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B;QACA,KAAK;YAAmB;gBACtB,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;QACA,KAAK;YAAS;gBACZ,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;QACA,KAAK;YAAW;gBACd,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;QACA,KAAK;YAAiB;gBACpB,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;QACA,KAAK;YAAuB;gBAC1B,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB;QACA,KAAK;YAAW;gBACd,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;QACA;YAAS;gBACP,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB;IACF;AACF;AAEA;;CAEC,GACD,MAAM,wBAAwB,CAAC;IAC7B,OAAQ;QACN,KAAK;YAAqB;gBACxB,OAAO;YACT;QACA,KAAK;YAAmB;gBACtB,OAAO;YACT;QACA,KAAK;YAAS;gBACZ,OAAO;YACT;QACA,KAAK;YAAW;gBACd,OAAO;YACT;QACA,KAAK;YAAiB;gBACpB,OAAO;YACT;QACA,KAAK;YAAuB;gBAC1B,OAAO;YACT;QACA,KAAK;YAAW;gBACd,OAAO;YACT;QACA;YAAS;gBACP,OAAO;YACT;IACF;AACF;AAEA;;CAEC,GACD,MAAM,kBAAkB,CAAC;IACvB,OAAQ;QACN,KAAK;YAAS;gBACZ,OAAO;YACT;QACA,KAAK;YAAW;gBACd,OAAO;YACT;QACA,KAAK;YAAW;gBACd,OAAO;YACT;QACA;YAAS;gBACP,OAAO;YACT;IACF;AACF;AAKO,MAAM,sBAAgC;;IAC3C,MAAM,EACJ,qBAAqB,EACrB,sBAAsB,EACtB,aAAa,EACb,kBAAkB,EAClB,uBAAuB,EACxB,GAAG,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD;IAEd,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,cAAc,MAAM,GAAG,mBACtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;4BACrC,4BAA4B,mBAC3B,6LAAC,oIAAA,CAAA,QAAK;gCAAC,WAAU;gCAAU,SAAQ;0CAChC;;;;;;;;;;;;kCAIP,6LAAC,qIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,SAAS;wBACT,MAAK;wBACL,SAAQ;kCACT;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;0BACZ,cAAc,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAA,6BAC3B,6LAAC,mIAAA,CAAA,OAAI;wBACH,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,wDACA,sBAAsB,aAAa,IAAI,GACvC,CAAC,aAAa,IAAI,IAChB;kCAIJ,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACZ,oBAAoB,aAAa,IAAI;;;;;;kDAIxC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC,oIAAA,CAAA,QAAK;4DACJ,WAAU;4DACV,SAAS,gBAAgB,aAAa,IAAI;sEAEzC,aAAa,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;sEAIlC,6LAAC;4DAAE,WAAU;sEACV,aAAa,OAAO;;;;;;sEAIvB,6LAAC;4DAAE,WAAU;sEACV,IAAI,KAAK,aAAa,SAAS,EAAE,cAAc;;;;;;wDAIjD,CAAC,aAAa,QAAQ,IAAI,aAAa,SAAS,mBAC/C,6LAAC;4DAAI,WAAU;;gEACZ,aAAa,QAAQ,kBACpB,6LAAC,oIAAA,CAAA,QAAK;oEAAC,WAAU;oEAAU,SAAQ;8EAChC,aAAa,QAAQ;;;;;;gEAGzB,aAAa,SAAS,kBACrB,6LAAC,qIAAA,CAAA,SAAM;oEACL,WAAU;oEACV,SAAS,IACP,OAAO,IAAI,CAAC,aAAa,SAAS,EAAE;oEAEtC,MAAK;oEACL,SAAQ;8EACT;;;;;;;;;;;;;;;;;;8DAST,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,WAAU;4DACV,SAAS,IAAM,mBAAmB,aAAa,EAAE;4DACjD,MAAK;4DACL,SAAQ;sEAER,cAAA,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;wDAGd,CAAC,aAAa,IAAI,kBACjB,6LAAC,qIAAA,CAAA,SAAM;4DACL,WAAU;4DACV,SAAS,IACP,uBAAuB,aAAa,EAAE;4DAExC,MAAK;4DACL,OAAM;4DACN,SAAQ;sEAER,cAAA,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA5EhC,aAAa,EAAE;;;;;;;;;;;;;;;;AAyFhC;GAxIa;;QAOP,8IAAA,CAAA,cAAW;;;KAPJ;AA6IN,MAAM,oBAQR,CAAC,EAAE,YAAY,EAAE,SAAS,EAAE;;IAC/B,+BAA+B;IAC/B,6JAAA,CAAA,UAAK,CAAC,SAAS;uCAAC;YACd,MAAM,QAAQ;qDAAW;oBACvB,UAAU,aAAa,EAAE;gBAC3B;oDAAG;YAEH;+CAAO,IAAM,aAAa;;QAC5B;sCAAG;QAAC,aAAa,EAAE;QAAE;KAAU;IAE/B,qBACE,6LAAC,mIAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,+FACA,sBAAsB,aAAa,IAAI;kBAGzC,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,oBAAoB,aAAa,IAAI;;;;;;kCAExC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAuB,aAAa,OAAO;;;;;;;;;;;kCAE1D,6LAAC,qIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,SAAS,IAAM,UAAU,aAAa,EAAE;wBACxC,MAAK;wBACL,SAAQ;kCAER,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzB;IA7Ca;MAAA", "debugId": null}}, {"offset": {"line": 7326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/toast.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as ToastPrimitives from '@radix-ui/react-toast';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\nimport { X } from 'lucide-react';\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst ToastProvider = ToastPrimitives.Provider;\r\n\r\nconst ToastViewport = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Viewport\r\n    className={cn(\r\n      'fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nToastViewport.displayName = ToastPrimitives.Viewport.displayName;\r\n\r\nconst toastVariants = cva(\r\n  'group grid items-center gap-1 rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-10 data-[state=open]:fade-in-10 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[swipe=end]:slide-out-to-right-full data-[swipe=start]:slide-out-to-left-full',\r\n  {\r\n    defaultVariants: {\r\n      variant: 'default',\r\n    },\r\n    variants: {\r\n      variant: {\r\n        default: 'border bg-background text-foreground',\r\n        destructive:\r\n          'destructive group border-destructive bg-destructive text-destructive-foreground',\r\n      },\r\n    },\r\n  }\r\n);\r\n\r\nconst Toast = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\r\n    VariantProps<typeof toastVariants>\r\n>(({ className, variant, ...props }, ref) => {\r\n  return (\r\n    <ToastPrimitives.Root\r\n      className={cn(toastVariants({ variant }), className)}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nToast.displayName = ToastPrimitives.Root.displayName;\r\n\r\nconst ToastAction = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Action>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Action\r\n    className={cn(\r\n      'inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nToastAction.displayName = ToastPrimitives.Action.displayName;\r\n\r\nconst ToastClose = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Close>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Close\r\n    className={cn(\r\n      'absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600',\r\n      className\r\n    )}\r\n    ref={ref}\r\n    toast-close=\"\"\r\n    {...props}\r\n  >\r\n    <X className=\"size-4\" />\r\n  </ToastPrimitives.Close>\r\n));\r\nToastClose.displayName = ToastPrimitives.Close.displayName;\r\n\r\nconst ToastTitle = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Title>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Title\r\n    className={cn('text-sm font-semibold', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nToastTitle.displayName = ToastPrimitives.Title.displayName;\r\n\r\nconst ToastDescription = React.forwardRef<\r\n  React.ElementRef<typeof ToastPrimitives.Description>,\r\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <ToastPrimitives.Description\r\n    className={cn('text-sm opacity-90', className)}\r\n    ref={ref}\r\n    {...props}\r\n  />\r\n));\r\nToastDescription.displayName = ToastPrimitives.Description.displayName;\r\n\r\ntype ToastActionElement = React.ElementRef<typeof ToastAction>;\r\n\r\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>;\r\n\r\nexport {\r\n  Toast,\r\n  ToastAction,\r\n  type ToastActionElement,\r\n  ToastClose,\r\n  ToastDescription,\r\n  type ToastProps,\r\n  ToastProvider,\r\n  ToastTitle,\r\n  ToastViewport,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AAAA;AAPA;;;;;;;AASA,MAAM,gBAAgB,oKAAA,CAAA,WAAwB;AAE9C,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,WAAwB;QACvB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG,oKAAA,CAAA,WAAwB,CAAC,WAAW;AAEhE,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,8dACA;IACE,iBAAiB;QACf,SAAS;IACX;IACA,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAI3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACnC,qBACE,6LAAC,oKAAA,CAAA,OAAoB;QACnB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAC1C,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AACA,MAAM,WAAW,GAAG,oKAAA,CAAA,OAAoB,CAAC,WAAW;AAEpD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,SAAsB;QACrB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,sgBACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAA,CAAA,SAAsB,CAAC,WAAW;AAE5D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,yVACA;QAEF,KAAK;QACL,eAAY;QACX,GAAG,KAAK;kBAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;YAAC,WAAU;;;;;;;;;;;;AAGjB,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,QAAqB;QACpB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACvC,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,oKAAA,CAAA,QAAqB,CAAC,WAAW;AAE1D,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAA,CAAA,cAA2B;QAC1B,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;QACpC,KAAK;QACJ,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,oKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 7462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/toaster.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport {\r\n  Toast,\r\n  ToastClose,\r\n  ToastDescription,\r\n  ToastProvider,\r\n  ToastTitle,\r\n  ToastViewport,\r\n  ToastAction,\r\n} from '@/components/ui/toast';\r\nimport { useToast } from '@/hooks/utils/use-toast';\r\n\r\nexport function Toaster() {\r\n  const { toasts } = useToast();\r\n\r\n  return (\r\n    <ToastProvider>\r\n      {toasts.map(function ({ action, description, id, title, ...props }) {\r\n        return (\r\n          <Toast key={id} {...props}>\r\n            <div className=\"grid gap-1\">\r\n              {title && <ToastTitle>{title}</ToastTitle>}\r\n              {description && (\r\n                <ToastDescription>{description}</ToastDescription>\r\n              )}\r\n            </div>\r\n            {action && React.isValidElement(action) ? action : null}\r\n            <ToastClose />\r\n          </Toast>\r\n        );\r\n      })}\r\n      <ToastViewport />\r\n    </ToastProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;;;AAZA;;;;AAcO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD;IAE1B,qBACE,6LAAC,oIAAA,CAAA,gBAAa;;YACX,OAAO,GAAG,CAAC,SAAU,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,OAAO;gBAChE,qBACE,6LAAC,oIAAA,CAAA,QAAK;oBAAW,GAAG,KAAK;;sCACvB,6LAAC;4BAAI,WAAU;;gCACZ,uBAAS,6LAAC,oIAAA,CAAA,aAAU;8CAAE;;;;;;gCACtB,6BACC,6LAAC,oIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAGtB,wBAAU,6JAAA,CAAA,UAAK,CAAC,cAAc,CAAC,UAAU,SAAS;sCACnD,6LAAC,oIAAA,CAAA,aAAU;;;;;;mBARD;;;;;YAWhB;0BACA,6LAAC,oIAAA,CAAA,gBAAa;;;;;;;;;;;AAGpB;GAtBgB;;QACK,wIAAA,CAAA,WAAQ;;;KADb", "debugId": null}}, {"offset": {"line": 7549, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/ui/QuickAccessFab.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport <PERSON> from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport {\r\n  BarChart3,\r\n  TrendingUp,\r\n  FileText,\r\n  Download,\r\n  Filter,\r\n  X,\r\n  ChevronUp,\r\n} from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface QuickAccessFabProps {\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * Floating Action Button for Quick Access to Reporting Dashboard\r\n *\r\n * Provides quick access to different sections of the reporting dashboard\r\n * from anywhere in the application.\r\n */\r\nexport const QuickAccessFab: React.FC<QuickAccessFabProps> = ({\r\n  className = '',\r\n}) => {\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n  const pathname = usePathname();\r\n\r\n  // Don't show on reports pages to avoid redundancy\r\n  if (pathname?.startsWith('/reports')) {\r\n    return null;\r\n  }\r\n\r\n  const quickAccessItems = [\r\n    {\r\n      href: '/reports',\r\n      icon: BarChart3,\r\n      label: 'Dashboard',\r\n      description: 'Main Analytics',\r\n      color: 'bg-blue-600 hover:bg-blue-700',\r\n    },\r\n    {\r\n      href: '/reports/analytics',\r\n      icon: TrendingUp,\r\n      label: 'Analytics',\r\n      description: 'Advanced Insights',\r\n      color: 'bg-purple-600 hover:bg-purple-700',\r\n    },\r\n    {\r\n      href: '/reports/data',\r\n      icon: FileText,\r\n      label: 'Data Tables',\r\n      description: 'Raw Data View',\r\n      color: 'bg-green-600 hover:bg-green-700',\r\n    },\r\n  ];\r\n\r\n  const toggleExpanded = () => setIsExpanded(!isExpanded);\r\n\r\n  return (\r\n    <div className={cn('fixed bottom-6 right-6 z-50', className)}>\r\n      {/* Expanded Menu Items */}\r\n      {isExpanded && (\r\n        <div className=\"mb-4 space-y-3\">\r\n          {quickAccessItems.map((item, index) => (\r\n            <div\r\n              key={item.href}\r\n              className=\"flex items-center justify-end space-x-3 animate-in slide-in-from-bottom-2 duration-200\"\r\n              style={{ animationDelay: `${index * 50}ms` }}\r\n            >\r\n              {/* Label */}\r\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border px-3 py-2 text-sm\">\r\n                <div className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                  {item.label}\r\n                </div>\r\n                <div className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                  {item.description}\r\n                </div>\r\n              </div>\r\n\r\n              {/* Action Button */}\r\n              <Button\r\n                asChild\r\n                size=\"lg\"\r\n                className={cn(\r\n                  'h-12 w-12 rounded-full shadow-lg border-0 text-white',\r\n                  item.color\r\n                )}\r\n              >\r\n                <Link href={item.href}>\r\n                  <item.icon className=\"h-5 w-5\" />\r\n                  <span className=\"sr-only\">{item.label}</span>\r\n                </Link>\r\n              </Button>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n\r\n      {/* Main FAB Button */}\r\n      <Button\r\n        onClick={toggleExpanded}\r\n        size=\"lg\"\r\n        className={cn(\r\n          'h-14 w-14 rounded-full shadow-lg transition-all duration-200',\r\n          isExpanded\r\n            ? 'bg-red-600 hover:bg-red-700 rotate-45'\r\n            : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'\r\n        )}\r\n      >\r\n        {isExpanded ? (\r\n          <X className=\"h-6 w-6 text-white\" />\r\n        ) : (\r\n          <div className=\"flex flex-col items-center\">\r\n            <BarChart3 className=\"h-5 w-5 text-white\" />\r\n            <ChevronUp className=\"h-3 w-3 text-white -mt-1\" />\r\n          </div>\r\n        )}\r\n        <span className=\"sr-only\">\r\n          {isExpanded\r\n            ? 'Close quick access menu'\r\n            : 'Open reporting quick access'}\r\n        </span>\r\n      </Button>\r\n\r\n      {/* New Feature Badge */}\r\n      {!isExpanded && (\r\n        <Badge\r\n          variant=\"destructive\"\r\n          className=\"absolute -top-2 -right-2 text-xs px-1.5 py-0.5 animate-pulse\"\r\n        >\r\n          NEW\r\n        </Badge>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuickAccessFab;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AAAA;;;AAhBA;;;;;;;;AA4BO,MAAM,iBAAgD,CAAC,EAC5D,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,kDAAkD;IAClD,IAAI,UAAU,WAAW,aAAa;QACpC,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB;YACE,MAAM;YACN,MAAM,qNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM,qNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM,iNAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,iBAAiB,IAAM,cAAc,CAAC;IAE5C,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;;YAE/C,4BACC,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,6LAAC;wBAEC,WAAU;wBACV,OAAO;4BAAE,gBAAgB,GAAG,QAAQ,GAAG,EAAE,CAAC;wBAAC;;0CAG3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;kDAEb,6LAAC;wCAAI,WAAU;kDACZ,KAAK,WAAW;;;;;;;;;;;;0CAKrB,6LAAC,qIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,MAAK;gCACL,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,wDACA,KAAK,KAAK;0CAGZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAM,KAAK,IAAI;;sDACnB,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAK,WAAU;sDAAW,KAAK,KAAK;;;;;;;;;;;;;;;;;;uBAzBpC,KAAK,IAAI;;;;;;;;;;0BAkCtB,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS;gBACT,MAAK;gBACL,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gEACA,aACI,0CACA;;oBAGL,2BACC,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;kCAGzB,6LAAC;wBAAK,WAAU;kCACb,aACG,4BACA;;;;;;;;;;;;YAKP,CAAC,4BACA,6LAAC,oIAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,WAAU;0BACX;;;;;;;;;;;;AAMT;GAlHa;;QAIM,qIAAA,CAAA,cAAW;;;KAJjB;uCAoHE", "debugId": null}}]}