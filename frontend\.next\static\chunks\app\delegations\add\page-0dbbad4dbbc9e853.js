(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5534],{18115:(e,t,r)=>{Promise.resolve().then(r.bind(r,71616))},71616:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(95155),a=r(57082),s=r(35695),o=r(67428),i=r(95647),d=r(53712),l=r(17841);function c(){let e=(0,s.useRouter)(),{showEntityCreated:t,showEntityCreationError:r}=(0,d.O_)("delegation"),{error:c,isPending:u,mutateAsync:m}=(0,l.er)(),g=async n=>{var a;let s={...n,delegates:n.delegates.map(e=>{var t;return{name:e.name,notes:null!=(t=e.notes)?t:"",title:e.title}}),drivers:n.driverEmployeeIds.map(e=>({employeeId:e})),durationFrom:new Date(n.durationFrom).toISOString(),durationTo:new Date(n.durationTo).toISOString(),escorts:n.escortEmployeeIds.map(e=>({employeeId:e})),notes:null!=(a=n.notes)?a:"",status:n.status.replace(" ","_"),vehicles:n.vehicleIds.map(e=>({assignedDate:new Date(n.durationFrom).toISOString(),returnDate:new Date(n.durationTo).toISOString(),vehicleId:e}))};try{await m(s);let r={event:n.eventName,location:n.location};t(r),e.push("/delegations")}catch(t){console.error("Error adding delegation:",t);let e="Failed to add delegation. Please try again.";t instanceof Error?e=t.message:c instanceof Error&&(e=c.message),r(e)}};return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)(i.z,{description:"Enter the details for the new delegation or event.",icon:a.A,title:"Add New Delegation"}),c&&(0,n.jsxs)("p",{className:"rounded-md bg-red-100 p-3 text-red-500",children:["Error: ",c.message]}),(0,n.jsx)(o.GK,{isEditing:!1,onSubmit:g})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6476,1137,3860,9664,1263,5495,1859,5669,4629,7454,8982,6548,4036,8658,111,3712,7515,7841,542,8441,1684,7358],()=>t(18115)),_N_E=e.O()}]);