{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/hooks/useReportGeneration.ts"], "sourcesContent": ["/**\r\n * @file useReportGeneration.ts\r\n * @description Hook for managing data report generation\r\n */\r\n\r\nimport { useState, useCallback } from 'react';\r\nimport { useApiQuery } from '@/hooks/api/useApiQuery';\r\nimport { useSecureApiClient } from '@/lib/api/security';\r\nimport {\r\n  createReportGenerationService,\r\n  IReportGenerationService,\r\n  IApiClient,\r\n} from '../data/services/ReportGenerationService';\r\n\r\n/**\r\n * Report generation configuration interface\r\n */\r\nexport interface ReportGenerationConfig {\r\n  entityTypes: string[];\r\n  template: string;\r\n  format: string;\r\n  filters?: Record<string, any>;\r\n  options?: {\r\n    name?: string;\r\n    description?: string;\r\n    includeCharts?: boolean;\r\n    includeSummary?: boolean;\r\n  };\r\n}\r\n\r\n/**\r\n * Individual report generation configuration\r\n */\r\nexport interface IndividualReportConfig {\r\n  entityType: string;\r\n  entityId: string;\r\n  template?: string;\r\n  format?: string;\r\n  options?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Aggregate report generation configuration\r\n */\r\nexport interface AggregateReportConfig {\r\n  entityType: string;\r\n  filters?: Record<string, any>;\r\n  template?: string;\r\n  format?: string;\r\n  options?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Report generation result\r\n */\r\nexport interface ReportGenerationResult {\r\n  data?: any; // Main report data\r\n  report?: any; // Legacy support\r\n  metadata: {\r\n    id: string;\r\n    type: string;\r\n    entityTypes?: string[];\r\n    entityType?: string;\r\n    entityId?: string;\r\n    format: string;\r\n    template: string;\r\n    generatedAt: string;\r\n    generatedBy: string;\r\n    filters?: Record<string, any>;\r\n    options?: Record<string, any>;\r\n  };\r\n}\r\n\r\n/**\r\n * Report history item\r\n */\r\nexport interface ReportHistoryItem {\r\n  id: string;\r\n  type: 'comprehensive' | 'individual' | 'aggregate';\r\n  entityTypes?: string[];\r\n  entityType?: string;\r\n  entityId?: string;\r\n  format: string;\r\n  template: string;\r\n  status: 'completed' | 'failed' | 'processing';\r\n  generatedAt: string;\r\n  generatedBy: string;\r\n  downloadUrl?: string;\r\n  fileSize?: string;\r\n}\r\n\r\n/**\r\n * Hook for managing report generation\r\n *\r\n * Follows SOLID Principles:\r\n * - SRP: Single responsibility for React state management\r\n * - DIP: Depends on IReportGenerationService abstraction\r\n * - OCP: Open for extension with new report types\r\n */\r\nexport const useReportGeneration = () => {\r\n  const [isGenerating, setIsGenerating] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const { client } = useSecureApiClient();\r\n\r\n  // Create API client adapter for the service\r\n  const apiClient: IApiClient = {\r\n    request: async config => {\r\n      const response = await client.request(config);\r\n      return { data: response };\r\n    },\r\n  };\r\n\r\n  // Create service instance with proper API client\r\n  const service = createReportGenerationService(apiClient);\r\n\r\n  /**\r\n   * Generate comprehensive data report\r\n   * Uses service layer following DIP principle\r\n   */\r\n  const generateReport = useCallback(\r\n    async (config: ReportGenerationConfig): Promise<ReportGenerationResult> => {\r\n      setIsGenerating(true);\r\n      setError(null);\r\n\r\n      try {\r\n        return await service.generateComprehensiveReport(config);\r\n      } catch (err) {\r\n        const errorMessage =\r\n          err instanceof Error ? err.message : 'Failed to generate report';\r\n        setError(errorMessage);\r\n        throw err;\r\n      } finally {\r\n        setIsGenerating(false);\r\n      }\r\n    },\r\n    [service]\r\n  );\r\n\r\n  /**\r\n   * Generate individual entity report\r\n   * Uses service layer following DIP principle\r\n   */\r\n  const generateIndividualReport = useCallback(\r\n    async (config: IndividualReportConfig): Promise<ReportGenerationResult> => {\r\n      setIsGenerating(true);\r\n      setError(null);\r\n\r\n      try {\r\n        return await service.generateIndividualReport(config);\r\n      } catch (err) {\r\n        const errorMessage =\r\n          err instanceof Error\r\n            ? err.message\r\n            : 'Failed to generate individual report';\r\n        setError(errorMessage);\r\n        throw err;\r\n      } finally {\r\n        setIsGenerating(false);\r\n      }\r\n    },\r\n    [service]\r\n  );\r\n\r\n  /**\r\n   * Generate aggregate entity report\r\n   * Uses service layer following DIP principle\r\n   */\r\n  const generateAggregateReport = useCallback(\r\n    async (config: AggregateReportConfig): Promise<ReportGenerationResult> => {\r\n      setIsGenerating(true);\r\n      setError(null);\r\n\r\n      try {\r\n        return await service.generateAggregateReport(config);\r\n      } catch (err) {\r\n        const errorMessage =\r\n          err instanceof Error\r\n            ? err.message\r\n            : 'Failed to generate aggregate report';\r\n        setError(errorMessage);\r\n        throw err;\r\n      } finally {\r\n        setIsGenerating(false);\r\n      }\r\n    },\r\n    [service]\r\n  );\r\n\r\n  /**\r\n   * Export generated report data to PDF/Excel/CSV\r\n   */\r\n  const exportReport = useCallback(\r\n    async (\r\n      reportData: any,\r\n      format: 'pdf' | 'excel' | 'csv',\r\n      entityType: 'delegations' | 'tasks' | 'vehicles' | 'employees',\r\n      reportTitle?: string,\r\n      filename?: string\r\n    ): Promise<void> => {\r\n      try {\r\n        const { useExport } = await import('../exports/hooks/useExport');\r\n        const { exportReportToPDF, exportReportToExcel, exportToCSV } =\r\n          useExport(filename || 'report');\r\n\r\n        switch (format) {\r\n          case 'pdf':\r\n            // Convert report data to PDF using entity-specific components\r\n            await exportReportToPDF(\r\n              reportData,\r\n              entityType,\r\n              reportTitle ||\r\n                `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} Report`,\r\n              filename\r\n            );\r\n            break;\r\n\r\n          case 'excel':\r\n            // Convert to Excel format with proper sheets\r\n            exportReportToExcel(reportData, entityType, filename);\r\n            break;\r\n\r\n          case 'csv':\r\n            // Convert to CSV format (flatten data for CSV)\r\n            const csvData = Array.isArray(reportData.data)\r\n              ? reportData.data\r\n              : [reportData.data || reportData];\r\n            exportToCSV(csvData, { filename: filename || 'report' });\r\n            break;\r\n\r\n          default:\r\n            throw new Error(`Unsupported export format: ${format}`);\r\n        }\r\n      } catch (error) {\r\n        console.error('Export failed:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    []\r\n  );\r\n\r\n  return {\r\n    generateComprehensiveReport: generateReport,\r\n    generateIndividualReport,\r\n    generateAggregateReport,\r\n    exportReport,\r\n    isGenerating,\r\n    error,\r\n  };\r\n};\r\n\r\n/**\r\n * Hook for managing report history\r\n */\r\nexport const useReportHistory = (filters?: {\r\n  type?: string;\r\n  entityType?: string;\r\n}) => {\r\n  const { client } = useSecureApiClient();\r\n  const queryParams = new URLSearchParams();\r\n  if (filters?.type) queryParams.append('type', filters.type);\r\n  if (filters?.entityType) queryParams.append('entityType', filters.entityType);\r\n\r\n  const historyQuery = useApiQuery(\r\n    ['report-history', filters],\r\n    async (): Promise<{ reports: ReportHistoryItem[]; pagination: any }> => {\r\n      const response = await client.get(\r\n        `/api/reporting/reports/history?${queryParams.toString()}`\r\n      );\r\n      return response;\r\n    },\r\n    {\r\n      cacheDuration: 2 * 60 * 1000, // 2 minutes\r\n      enableRetry: true,\r\n    }\r\n  );\r\n\r\n  return {\r\n    reports: historyQuery.data?.reports || [],\r\n    pagination: historyQuery.data?.pagination,\r\n    isLoading: historyQuery.isLoading,\r\n    error: historyQuery.error,\r\n    refetch: historyQuery.refetch,\r\n  };\r\n};\r\n\r\n/**\r\n * Hook for downloading reports\r\n */\r\nexport const useReportDownload = () => {\r\n  const [isDownloading, setIsDownloading] = useState(false);\r\n  const [downloadError, setDownloadError] = useState<string | null>(null);\r\n  const { client } = useSecureApiClient();\r\n\r\n  const downloadReport = useCallback(\r\n    async (reportId: string): Promise<void> => {\r\n      setIsDownloading(true);\r\n      setDownloadError(null);\r\n\r\n      try {\r\n        const response = await client.get(\r\n          `/api/reporting/reports/${reportId}/download`\r\n        );\r\n\r\n        // For now, just show the response since actual file download isn't implemented yet\r\n        console.log('Download result:', response);\r\n\r\n        // TODO: Implement actual file download when backend file storage is ready\r\n        alert('Download functionality will be implemented with file storage');\r\n      } catch (err) {\r\n        const errorMessage =\r\n          err instanceof Error ? err.message : 'Failed to download report';\r\n        setDownloadError(errorMessage);\r\n        throw err;\r\n      } finally {\r\n        setIsDownloading(false);\r\n      }\r\n    },\r\n    [client]\r\n  );\r\n\r\n  return {\r\n    downloadReport,\r\n    isDownloading,\r\n    downloadError,\r\n  };\r\n};\r\n\r\n/**\r\n * Hook for report templates\r\n */\r\nexport const useReportTemplates = () => {\r\n  const { client } = useSecureApiClient();\r\n\r\n  const templatesQuery = useApiQuery(\r\n    ['report-templates'],\r\n    async () => {\r\n      const response = await client.get('/api/reporting/reports/templates');\r\n\r\n      // Ensure we return an array, handle different response structures\r\n      const data = response;\r\n      if (Array.isArray(data)) {\r\n        return data;\r\n      }\r\n\r\n      // If response has nested data property (API wrapper format)\r\n      if (data && Array.isArray(data.data)) {\r\n        return data.data;\r\n      }\r\n\r\n      // Fallback to empty array if data is not in expected format\r\n      console.warn('Report templates API returned unexpected format:', data);\r\n      return [];\r\n    },\r\n    {\r\n      cacheDuration: 10 * 60 * 1000, // 10 minutes\r\n      enableRetry: true,\r\n    }\r\n  );\r\n\r\n  return {\r\n    templates: Array.isArray(templatesQuery.data) ? templatesQuery.data : [],\r\n    isLoading: templatesQuery.isLoading,\r\n    error: templatesQuery.error,\r\n    refetch: templatesQuery.refetch,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;AACA;AACA;AAAA;AACA;;;;;;AA2FO,MAAM,sBAAsB;;IACjC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,qBAAkB,AAAD;IAEpC,4CAA4C;IAC5C,MAAM,YAAwB;QAC5B,SAAS,OAAM;YACb,MAAM,WAAW,MAAM,OAAO,OAAO,CAAC;YACtC,OAAO;gBAAE,MAAM;YAAS;QAC1B;IACF;IAEA,iDAAiD;IACjD,MAAM,UAAU,CAAA,GAAA,4LAAA,CAAA,gCAA6B,AAAD,EAAE;IAE9C;;;GAGC,GACD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAC/B,OAAO;YACL,gBAAgB;YAChB,SAAS;YAET,IAAI;gBACF,OAAO,MAAM,QAAQ,2BAA2B,CAAC;YACnD,EAAE,OAAO,KAAK;gBACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;gBACvC,SAAS;gBACT,MAAM;YACR,SAAU;gBACR,gBAAgB;YAClB;QACF;0DACA;QAAC;KAAQ;IAGX;;;GAGC,GACD,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qEACzC,OAAO;YACL,gBAAgB;YAChB,SAAS;YAET,IAAI;gBACF,OAAO,MAAM,QAAQ,wBAAwB,CAAC;YAChD,EAAE,OAAO,KAAK;gBACZ,MAAM,eACJ,eAAe,QACX,IAAI,OAAO,GACX;gBACN,SAAS;gBACT,MAAM;YACR,SAAU;gBACR,gBAAgB;YAClB;QACF;oEACA;QAAC;KAAQ;IAGX;;;GAGC,GACD,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oEACxC,OAAO;YACL,gBAAgB;YAChB,SAAS;YAET,IAAI;gBACF,OAAO,MAAM,QAAQ,uBAAuB,CAAC;YAC/C,EAAE,OAAO,KAAK;gBACZ,MAAM,eACJ,eAAe,QACX,IAAI,OAAO,GACX;gBACN,SAAS;gBACT,MAAM;YACR,SAAU;gBACR,gBAAgB;YAClB;QACF;mEACA;QAAC;KAAQ;IAGX;;GAEC,GACD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAC7B,OACE,YACA,QACA,YACA,aACA;YAEA,IAAI;gBACF,MAAM,EAAE,SAAS,EAAE,GAAG;gBACtB,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,WAAW,EAAE,GAC3D,UAAU,YAAY;gBAExB,OAAQ;oBACN,KAAK;wBACH,8DAA8D;wBAC9D,MAAM,kBACJ,YACA,YACA,eACE,GAAG,WAAW,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,KAAK,CAAC,GAAG,OAAO,CAAC,EACtE;wBAEF;oBAEF,KAAK;wBACH,6CAA6C;wBAC7C,oBAAoB,YAAY,YAAY;wBAC5C;oBAEF,KAAK;wBACH,+CAA+C;wBAC/C,MAAM,UAAU,MAAM,OAAO,CAAC,WAAW,IAAI,IACzC,WAAW,IAAI,GACf;4BAAC,WAAW,IAAI,IAAI;yBAAW;wBACnC,YAAY,SAAS;4BAAE,UAAU,YAAY;wBAAS;wBACtD;oBAEF;wBACE,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,QAAQ;gBAC1D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,MAAM;YACR;QACF;wDACA,EAAE;IAGJ,OAAO;QACL,6BAA6B;QAC7B;QACA;QACA;QACA;QACA;IACF;AACF;GArJa;;QAGQ,+JAAA,CAAA,qBAAkB;;;AAuJhC,MAAM,mBAAmB,CAAC;;IAI/B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,qBAAkB,AAAD;IACpC,MAAM,cAAc,IAAI;IACxB,IAAI,SAAS,MAAM,YAAY,MAAM,CAAC,QAAQ,QAAQ,IAAI;IAC1D,IAAI,SAAS,YAAY,YAAY,MAAM,CAAC,cAAc,QAAQ,UAAU;IAE5E,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAC7B;QAAC;QAAkB;KAAQ;sDAC3B;YACE,MAAM,WAAW,MAAM,OAAO,GAAG,CAC/B,CAAC,+BAA+B,EAAE,YAAY,QAAQ,IAAI;YAE5D,OAAO;QACT;qDACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;IAGF,OAAO;QACL,SAAS,aAAa,IAAI,EAAE,WAAW,EAAE;QACzC,YAAY,aAAa,IAAI,EAAE;QAC/B,WAAW,aAAa,SAAS;QACjC,OAAO,aAAa,KAAK;QACzB,SAAS,aAAa,OAAO;IAC/B;AACF;IA9Ba;;QAIQ,+JAAA,CAAA,qBAAkB;QAKhB,qIAAA,CAAA,cAAW;;;AA0B3B,MAAM,oBAAoB;;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,qBAAkB,AAAD;IAEpC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAC/B,OAAO;YACL,iBAAiB;YACjB,iBAAiB;YAEjB,IAAI;gBACF,MAAM,WAAW,MAAM,OAAO,GAAG,CAC/B,CAAC,uBAAuB,EAAE,SAAS,SAAS,CAAC;gBAG/C,mFAAmF;gBACnF,QAAQ,GAAG,CAAC,oBAAoB;gBAEhC,0EAA0E;gBAC1E,MAAM;YACR,EAAE,OAAO,KAAK;gBACZ,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;gBACvC,iBAAiB;gBACjB,MAAM;YACR,SAAU;gBACR,iBAAiB;YACnB;QACF;wDACA;QAAC;KAAO;IAGV,OAAO;QACL;QACA;QACA;IACF;AACF;IArCa;;QAGQ,+JAAA,CAAA,qBAAkB;;;AAuChC,MAAM,qBAAqB;;IAChC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,qBAAkB,AAAD;IAEpC,MAAM,iBAAiB,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAC/B;QAAC;KAAmB;0DACpB;YACE,MAAM,WAAW,MAAM,OAAO,GAAG,CAAC;YAElC,kEAAkE;YAClE,MAAM,OAAO;YACb,IAAI,MAAM,OAAO,CAAC,OAAO;gBACvB,OAAO;YACT;YAEA,4DAA4D;YAC5D,IAAI,QAAQ,MAAM,OAAO,CAAC,KAAK,IAAI,GAAG;gBACpC,OAAO,KAAK,IAAI;YAClB;YAEA,4DAA4D;YAC5D,QAAQ,IAAI,CAAC,oDAAoD;YACjE,OAAO,EAAE;QACX;yDACA;QACE,eAAe,KAAK,KAAK;QACzB,aAAa;IACf;IAGF,OAAO;QACL,WAAW,MAAM,OAAO,CAAC,eAAe,IAAI,IAAI,eAAe,IAAI,GAAG,EAAE;QACxE,WAAW,eAAe,SAAS;QACnC,OAAO,eAAe,KAAK;QAC3B,SAAS,eAAe,OAAO;IACjC;AACF;IAnCa;;QACQ,+JAAA,CAAA,qBAAkB;QAEd,qIAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/hooks/useReportTypes.ts"], "sourcesContent": ["/**\r\n * @file useReportTypes.ts\r\n * @description Hook for managing report types following existing patterns\r\n */\r\n\r\nimport { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\r\n\r\nimport { useApiQuery } from '@/hooks/api';\r\nimport { apiClient } from '@/lib/api';\r\n\r\nimport type { ReportType } from '../data/types/reporting';\r\n\r\n/**\r\n * Hook for managing report types\r\n *\r\n * Follows existing patterns from other management hooks.\r\n * Integrates with the established API and caching patterns.\r\n *\r\n * @returns Query and mutation functions for report type management\r\n */\r\nexport const useReportTypes = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  // Query for fetching all report types\r\n  const reportTypesQuery = useApiQuery(\r\n    ['report-types'],\r\n    async (): Promise<ReportType[]> => {\r\n      const result = (await apiClient.get('/reporting/report-types')) as any;\r\n      // Extract the data array from the API response structure\r\n      return result.data?.data || [];\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes\r\n      enableRetry: true,\r\n    }\r\n  );\r\n\r\n  // Mutation for creating a new report type\r\n  const createReportType = useMutation({\r\n    mutationFn: async (\r\n      reportTypeData: Partial<ReportType>\r\n    ): Promise<ReportType> => {\r\n      const result = (await apiClient.post(\r\n        '/reporting/report-types',\r\n        reportTypeData\r\n      )) as any;\r\n      // Extract the data from the API response structure\r\n      return result.data?.data || result.data;\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate and refetch report types\r\n      queryClient.invalidateQueries({ queryKey: ['report-types'] });\r\n    },\r\n  });\r\n\r\n  // Mutation for updating an existing report type\r\n  const updateReportType = useMutation({\r\n    mutationFn: async ({\r\n      id,\r\n      ...reportTypeData\r\n    }: Partial<ReportType> & { id: string }): Promise<ReportType> => {\r\n      const result = (await apiClient.put(\r\n        `/reporting/report-types/${id}`,\r\n        reportTypeData\r\n      )) as any;\r\n      // Extract the data from the API response structure\r\n      return result.data?.data || result.data;\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate and refetch report types\r\n      queryClient.invalidateQueries({ queryKey: ['report-types'] });\r\n    },\r\n  });\r\n\r\n  // Mutation for deleting a report type\r\n  const deleteReportType = useMutation({\r\n    mutationFn: async (id: string): Promise<void> => {\r\n      await apiClient.delete(`/reporting/report-types/${id}`);\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate and refetch report types\r\n      queryClient.invalidateQueries({ queryKey: ['report-types'] });\r\n    },\r\n  });\r\n\r\n  // Mutation for duplicating a report type\r\n  const duplicateReportType = useMutation({\r\n    mutationFn: async (id: string): Promise<ReportType> => {\r\n      const result = (await apiClient.post(\r\n        `/reporting/report-types/${id}/duplicate`\r\n      )) as any;\r\n      // Extract the data from the API response structure\r\n      return result.data?.data || result.data;\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate and refetch report types\r\n      queryClient.invalidateQueries({ queryKey: ['report-types'] });\r\n    },\r\n  });\r\n\r\n  // Mutation for toggling report type active status\r\n  const toggleReportTypeActive = useMutation({\r\n    mutationFn: async ({\r\n      id,\r\n      isActive,\r\n    }: {\r\n      id: string;\r\n      isActive: boolean;\r\n    }): Promise<ReportType> => {\r\n      const result = (await apiClient.patch(\r\n        `/reporting/report-types/${id}/toggle-active`,\r\n        { isActive }\r\n      )) as any;\r\n      // Extract the data from the API response structure\r\n      return result.data?.data || result.data;\r\n    },\r\n    onSuccess: () => {\r\n      // Invalidate and refetch report types\r\n      queryClient.invalidateQueries({ queryKey: ['report-types'] });\r\n    },\r\n  });\r\n\r\n  return {\r\n    // Mutations\r\n    createReportType,\r\n    // Query data\r\n    data: reportTypesQuery.data,\r\n    deleteReportType,\r\n\r\n    duplicateReportType,\r\n    error: reportTypesQuery.error,\r\n    isLoading: reportTypesQuery.isLoading,\r\n    // Utility functions\r\n    refetch: reportTypesQuery.refetch,\r\n    toggleReportTypeActive,\r\n\r\n    updateReportType,\r\n  };\r\n};\r\n\r\n/**\r\n * Hook for fetching a single report type by ID\r\n *\r\n * @param id - Report type ID\r\n * @returns Query result with single report type data\r\n */\r\nexport const useReportType = (id: string) => {\r\n  return useApiQuery(\r\n    ['report-type', id],\r\n    async (): Promise<ReportType> => {\r\n      const result = (await apiClient.get(\r\n        `/reporting/report-types/${id}`\r\n      )) as any;\r\n      // Extract the data from the API response structure\r\n      return result.data?.data || result.data;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enabled: !!id,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching report types by category\r\n *\r\n * @param category - Report type category\r\n * @returns Query result with filtered report types\r\n */\r\nexport const useReportTypesByCategory = (category: string) => {\r\n  return useApiQuery(\r\n    ['report-types', 'category', category],\r\n    async (): Promise<ReportType[]> => {\r\n      const result = (await apiClient.get(\r\n        `/reporting/report-types?category=${encodeURIComponent(category)}`\r\n      )) as any;\r\n      // Extract the data array from the API response structure\r\n      return result.data?.data || [];\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enabled: !!category,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AAAA;AAEA;AAAA;AACA;AAAA;;;;;AAYO,MAAM,iBAAiB;;IAC5B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,sCAAsC;IACtC,MAAM,mBAAmB,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACjC;QAAC;KAAe;wDAChB;YACE,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACpC,yDAAyD;YACzD,OAAO,OAAO,IAAI,EAAE,QAAQ,EAAE;QAChC;uDACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;IAGF,0CAA0C;IAC1C,MAAM,mBAAmB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACnC,UAAU;4DAAE,OACV;gBAEA,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,IAAI,CAClC,2BACA;gBAEF,mDAAmD;gBACnD,OAAO,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI;YACzC;;QACA,SAAS;4DAAE;gBACT,sCAAsC;gBACtC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAe;gBAAC;YAC7D;;IACF;IAEA,gDAAgD;IAChD,MAAM,mBAAmB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACnC,UAAU;4DAAE,OAAO,EACjB,EAAE,EACF,GAAG,gBACkC;gBACrC,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG,CACjC,CAAC,wBAAwB,EAAE,IAAI,EAC/B;gBAEF,mDAAmD;gBACnD,OAAO,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI;YACzC;;QACA,SAAS;4DAAE;gBACT,sCAAsC;gBACtC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAe;gBAAC;YAC7D;;IACF;IAEA,sCAAsC;IACtC,MAAM,mBAAmB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACnC,UAAU;4DAAE,OAAO;gBACjB,MAAM,6IAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,wBAAwB,EAAE,IAAI;YACxD;;QACA,SAAS;4DAAE;gBACT,sCAAsC;gBACtC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAe;gBAAC;YAC7D;;IACF;IAEA,yCAAyC;IACzC,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,UAAU;+DAAE,OAAO;gBACjB,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,IAAI,CAClC,CAAC,wBAAwB,EAAE,GAAG,UAAU,CAAC;gBAE3C,mDAAmD;gBACnD,OAAO,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI;YACzC;;QACA,SAAS;+DAAE;gBACT,sCAAsC;gBACtC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAe;gBAAC;YAC7D;;IACF;IAEA,kDAAkD;IAClD,MAAM,yBAAyB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACzC,UAAU;kEAAE,OAAO,EACjB,EAAE,EACF,QAAQ,EAIT;gBACC,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,KAAK,CACnC,CAAC,wBAAwB,EAAE,GAAG,cAAc,CAAC,EAC7C;oBAAE;gBAAS;gBAEb,mDAAmD;gBACnD,OAAO,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI;YACzC;;QACA,SAAS;kEAAE;gBACT,sCAAsC;gBACtC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAe;gBAAC;YAC7D;;IACF;IAEA,OAAO;QACL,YAAY;QACZ;QACA,aAAa;QACb,MAAM,iBAAiB,IAAI;QAC3B;QAEA;QACA,OAAO,iBAAiB,KAAK;QAC7B,WAAW,iBAAiB,SAAS;QACrC,oBAAoB;QACpB,SAAS,iBAAiB,OAAO;QACjC;QAEA;IACF;AACF;GAtHa;;QACS,yLAAA,CAAA,iBAAc;QAGT,qIAAA,CAAA,cAAW;QAcX,iLAAA,CAAA,cAAW;QAkBX,iLAAA,CAAA,cAAW;QAmBX,iLAAA,CAAA,cAAW;QAWR,iLAAA,CAAA,cAAW;QAeR,iLAAA,CAAA,cAAW;;;AA6CrC,MAAM,gBAAgB,CAAC;;IAC5B,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAe;KAAG;qCACnB;YACE,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG,CACjC,CAAC,wBAAwB,EAAE,IAAI;YAEjC,mDAAmD;YACnD,OAAO,OAAO,IAAI,EAAE,QAAQ,OAAO,IAAI;QACzC;oCACA;QACE,eAAe,IAAI,KAAK;QACxB,SAAS,CAAC,CAAC;QACX,aAAa;IACf;AAEJ;IAhBa;;QACJ,qIAAA,CAAA,cAAW;;;AAuBb,MAAM,2BAA2B,CAAC;;IACvC,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAgB;QAAY;KAAS;gDACtC;YACE,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG,CACjC,CAAC,iCAAiC,EAAE,mBAAmB,WAAW;YAEpE,yDAAyD;YACzD,OAAO,OAAO,IAAI,EAAE,QAAQ,EAAE;QAChC;+CACA;QACE,eAAe,IAAI,KAAK;QACxB,SAAS,CAAC,CAAC;QACX,aAAa;IACf;AAEJ;IAhBa;;QACJ,qIAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/hooks/useVehicleAnalytics.ts"], "sourcesContent": ["/**\r\n * @file useVehicleAnalytics.ts\r\n * @description Hook for fetching vehicle analytics data following existing patterns\r\n */\r\n\r\nimport { useQuery } from '@tanstack/react-query';\r\nimport { useApiQuery } from '@/hooks/api';\r\nimport { apiClient } from '@/lib/api';\r\nimport type {\r\n  ReportingFilters,\r\n  VehicleAnalytics,\r\n} from '../data/types/reporting';\r\n\r\n/**\r\n * Hook for fetching vehicle analytics data\r\n *\r\n * Follows existing patterns from useTaskAnalytics and other reporting hooks.\r\n * Integrates with the established API and caching patterns.\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with vehicle analytics data\r\n */\r\nexport const useVehicleAnalytics = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['vehicle-analytics', filters],\r\n    async (): Promise<VehicleAnalytics> => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        // Defensive programming: Ensure dates are Date objects\r\n        const fromDate =\r\n          filters.dateRange.from instanceof Date\r\n            ? filters.dateRange.from\r\n            : new Date(filters.dateRange.from);\r\n        const toDate =\r\n          filters.dateRange.to instanceof Date\r\n            ? filters.dateRange.to\r\n            : new Date(filters.dateRange.to);\r\n\r\n        queryParams.append(\r\n          'dateRange.from',\r\n          fromDate.toISOString().split('T')[0] || fromDate.toISOString()\r\n        );\r\n        queryParams.append(\r\n          'dateRange.to',\r\n          toDate.toISOString().split('T')[0] || toDate.toISOString()\r\n        );\r\n      }\r\n\r\n      if (filters?.vehicles) {\r\n        filters.vehicles.forEach(vehicle =>\r\n          queryParams.append('vehicles', vehicle.toString())\r\n        );\r\n      }\r\n\r\n      if (filters?.serviceTypes) {\r\n        filters.serviceTypes.forEach(type =>\r\n          queryParams.append('serviceTypes', type)\r\n        );\r\n      }\r\n\r\n      if (filters?.serviceStatus) {\r\n        filters.serviceStatus.forEach(status =>\r\n          queryParams.append('serviceStatus', status)\r\n        );\r\n      }\r\n\r\n      const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes\r\n      enableRetry: true,\r\n      retryAttempts: 3,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching vehicle utilization metrics specifically\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with vehicle utilization data\r\n */\r\nexport const useVehicleUtilization = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['vehicle-utilization', filters],\r\n    async () => {\r\n      // Get utilization data from vehicle analytics endpoint\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.vehicles) {\r\n        filters.vehicles.forEach(vehicle =>\r\n          queryParams.append('vehicles', vehicle.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      const vehicleAnalytics = result.data || result;\r\n\r\n      // Return the utilization metrics from vehicle analytics\r\n      return vehicleAnalytics.utilizationMetrics || [];\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching vehicle maintenance schedule\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with maintenance schedule data\r\n */\r\nexport const useVehicleMaintenanceSchedule = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['vehicle-maintenance-schedule', filters],\r\n    async () => {\r\n      // Get maintenance data from vehicle analytics endpoint\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.vehicles) {\r\n        filters.vehicles.forEach(vehicle =>\r\n          queryParams.append('vehicles', vehicle.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      const vehicleAnalytics = result.data || result;\r\n\r\n      // Return the maintenance schedule from vehicle analytics\r\n      return vehicleAnalytics.maintenanceSchedule || [];\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching vehicle cost analytics\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with cost analytics data\r\n */\r\nexport const useVehicleCostAnalytics = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['vehicle-cost-analytics', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.vehicles) {\r\n        filters.vehicles.forEach(vehicle =>\r\n          queryParams.append('vehicles', vehicle.toString())\r\n        );\r\n      }\r\n\r\n      if (filters?.serviceTypes) {\r\n        filters.serviceTypes.forEach(type =>\r\n          queryParams.append('serviceTypes', type)\r\n        );\r\n      }\r\n\r\n      const url = `/reporting/vehicle-costs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAGD;AAAA;AACA;AAAA;;;;AAeO,MAAM,sBAAsB,CAAC;;IAClC,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAqB;KAAQ;2CAC9B;YACE,MAAM,cAAc,IAAI;YAExB,IAAI,SAAS,WAAW;gBACtB,uDAAuD;gBACvD,MAAM,WACJ,QAAQ,SAAS,CAAC,IAAI,YAAY,OAC9B,QAAQ,SAAS,CAAC,IAAI,GACtB,IAAI,KAAK,QAAQ,SAAS,CAAC,IAAI;gBACrC,MAAM,SACJ,QAAQ,SAAS,CAAC,EAAE,YAAY,OAC5B,QAAQ,SAAS,CAAC,EAAE,GACpB,IAAI,KAAK,QAAQ,SAAS,CAAC,EAAE;gBAEnC,YAAY,MAAM,CAChB,kBACA,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,SAAS,WAAW;gBAE9D,YAAY,MAAM,CAChB,gBACA,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO,WAAW;YAE5D;YAEA,IAAI,SAAS,UAAU;gBACrB,QAAQ,QAAQ,CAAC,OAAO;uDAAC,CAAA,UACvB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;;YAEnD;YAEA,IAAI,SAAS,cAAc;gBACzB,QAAQ,YAAY,CAAC,OAAO;uDAAC,CAAA,OAC3B,YAAY,MAAM,CAAC,gBAAgB;;YAEvC;YAEA,IAAI,SAAS,eAAe;gBAC1B,QAAQ,aAAa,CAAC,OAAO;uDAAC,CAAA,SAC5B,YAAY,MAAM,CAAC,iBAAiB;;YAExC;YAEA,MAAM,MAAM,CAAC,6BAA6B,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YACxG,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACpC,OAAO,OAAO,IAAI,IAAI;QACxB;0CACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;QACb,eAAe;IACjB;AAEJ;GAvDa;;QACJ,qIAAA,CAAA,cAAW;;;AA8Db,MAAM,wBAAwB,CAAC;;IACpC,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAuB;KAAQ;6CAChC;YACE,uDAAuD;YACvD,MAAM,cAAc,IAAI;YAExB,IAAI,SAAS,WAAW;gBACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;gBAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;YAC3D;YAEA,IAAI,SAAS,UAAU;gBACrB,QAAQ,QAAQ,CAAC,OAAO;yDAAC,CAAA,UACvB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;;YAEnD;YAEA,MAAM,MAAM,CAAC,6BAA6B,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YACxG,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACpC,MAAM,mBAAmB,OAAO,IAAI,IAAI;YAExC,wDAAwD;YACxD,OAAO,iBAAiB,kBAAkB,IAAI,EAAE;QAClD;4CACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;IA9Ba;;QACJ,qIAAA,CAAA,cAAW;;;AAqCb,MAAM,gCAAgC,CAAC;;IAC5C,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAgC;KAAQ;qDACzC;YACE,uDAAuD;YACvD,MAAM,cAAc,IAAI;YAExB,IAAI,SAAS,WAAW;gBACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;gBAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;YAC3D;YAEA,IAAI,SAAS,UAAU;gBACrB,QAAQ,QAAQ,CAAC,OAAO;iEAAC,CAAA,UACvB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;;YAEnD;YAEA,MAAM,MAAM,CAAC,6BAA6B,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YACxG,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACpC,MAAM,mBAAmB,OAAO,IAAI,IAAI;YAExC,yDAAyD;YACzD,OAAO,iBAAiB,mBAAmB,IAAI,EAAE;QACnD;oDACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;IA9Ba;;QACJ,qIAAA,CAAA,cAAW;;;AAqCb,MAAM,0BAA0B,CAAC;;IACtC,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAA0B;KAAQ;+CACnC;YACE,MAAM,cAAc,IAAI;YAExB,IAAI,SAAS,WAAW;gBACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;gBAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;YAC3D;YAEA,IAAI,SAAS,UAAU;gBACrB,QAAQ,QAAQ,CAAC,OAAO;2DAAC,CAAA,UACvB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;;YAEnD;YAEA,IAAI,SAAS,cAAc;gBACzB,QAAQ,YAAY,CAAC,OAAO;2DAAC,CAAA,OAC3B,YAAY,MAAM,CAAC,gBAAgB;;YAEvC;YAEA,MAAM,MAAM,CAAC,wBAAwB,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YACnG,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACpC,OAAO,OAAO,IAAI,IAAI;QACxB;8CACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;IAhCa;;QACJ,qIAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/hooks/useEmployeeAnalytics.ts"], "sourcesContent": ["/**\r\n * @file useEmployeeAnalytics.ts\r\n * @description Hook for fetching employee analytics data following existing patterns\r\n */\r\n\r\nimport { useQuery } from '@tanstack/react-query';\r\nimport { useApiQuery } from '@/hooks/api';\r\nimport { apiClient } from '@/lib/api';\r\nimport type {\r\n  ReportingFilters,\r\n  EmployeeAnalytics,\r\n} from '../data/types/reporting';\r\n\r\n/**\r\n * Hook for fetching employee analytics data\r\n *\r\n * Follows existing patterns from useTaskAnalytics and useVehicleAnalytics.\r\n * Integrates with the established API and caching patterns.\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with employee analytics data\r\n */\r\nexport const useEmployeeAnalytics = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['employee-analytics', filters],\r\n    async (): Promise<EmployeeAnalytics> => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        // Defensive programming: Ensure dates are Date objects\r\n        const fromDate =\r\n          filters.dateRange.from instanceof Date\r\n            ? filters.dateRange.from\r\n            : new Date(filters.dateRange.from);\r\n        const toDate =\r\n          filters.dateRange.to instanceof Date\r\n            ? filters.dateRange.to\r\n            : new Date(filters.dateRange.to);\r\n\r\n        queryParams.append(\r\n          'dateRange.from',\r\n          fromDate.toISOString().split('T')[0] || fromDate.toISOString()\r\n        );\r\n        queryParams.append(\r\n          'dateRange.to',\r\n          toDate.toISOString().split('T')[0] || toDate.toISOString()\r\n        );\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      if (filters?.locations) {\r\n        filters.locations.forEach(location =>\r\n          queryParams.append('locations', location)\r\n        );\r\n      }\r\n\r\n      if (filters?.includeEmployeeMetrics) {\r\n        queryParams.append('includeEmployeeMetrics', 'true');\r\n      }\r\n\r\n      const url = `/reporting/employees/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes\r\n      enableRetry: true,\r\n      retryAttempts: 3,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching employee performance metrics specifically\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with employee performance data\r\n */\r\nexport const useEmployeePerformance = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['employee-performance', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/api/reporting/employee-performance${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const response = await fetch(url);\r\n      if (!response.ok) {\r\n        throw new Error(\r\n          `Failed to fetch employee performance: ${response.statusText}`\r\n        );\r\n      }\r\n      return response.json();\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching employee workload distribution\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with workload distribution data\r\n */\r\nexport const useEmployeeWorkload = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['employee-workload', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/api/reporting/employee-workload${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const response = await fetch(url);\r\n      if (!response.ok) {\r\n        throw new Error(\r\n          `Failed to fetch employee workload: ${response.statusText}`\r\n        );\r\n      }\r\n      return response.json();\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching employee task assignments\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with task assignment data\r\n */\r\nexport const useEmployeeTaskAssignments = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['employee-task-assignments', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/api/reporting/employee-tasks${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const response = await fetch(url);\r\n      if (!response.ok) {\r\n        throw new Error(\r\n          `Failed to fetch employee task assignments: ${response.statusText}`\r\n        );\r\n      }\r\n      return response.json();\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching employee availability metrics\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with availability metrics data\r\n */\r\nexport const useEmployeeAvailability = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['employee-availability', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/api/reporting/employee-availability${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const response = await fetch(url);\r\n      if (!response.ok) {\r\n        throw new Error(\r\n          `Failed to fetch employee availability: ${response.statusText}`\r\n        );\r\n      }\r\n      return response.json();\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;AAGD;AAAA;AACA;AAAA;;;;AAeO,MAAM,uBAAuB,CAAC;;IACnC,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAsB;KAAQ;4CAC/B;YACE,MAAM,cAAc,IAAI;YAExB,IAAI,SAAS,WAAW;gBACtB,uDAAuD;gBACvD,MAAM,WACJ,QAAQ,SAAS,CAAC,IAAI,YAAY,OAC9B,QAAQ,SAAS,CAAC,IAAI,GACtB,IAAI,KAAK,QAAQ,SAAS,CAAC,IAAI;gBACrC,MAAM,SACJ,QAAQ,SAAS,CAAC,EAAE,YAAY,OAC5B,QAAQ,SAAS,CAAC,EAAE,GACpB,IAAI,KAAK,QAAQ,SAAS,CAAC,EAAE;gBAEnC,YAAY,MAAM,CAChB,kBACA,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,SAAS,WAAW;gBAE9D,YAAY,MAAM,CAChB,gBACA,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO,WAAW;YAE5D;YAEA,IAAI,SAAS,WAAW;gBACtB,QAAQ,SAAS,CAAC,OAAO;wDAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;;YAErD;YAEA,IAAI,SAAS,WAAW;gBACtB,QAAQ,SAAS,CAAC,OAAO;wDAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa;;YAEpC;YAEA,IAAI,SAAS,wBAAwB;gBACnC,YAAY,MAAM,CAAC,0BAA0B;YAC/C;YAEA,MAAM,MAAM,CAAC,8BAA8B,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YACzG,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACpC,OAAO,OAAO,IAAI,IAAI;QACxB;2CACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;QACb,eAAe;IACjB;AAEJ;GArDa;;QACJ,qIAAA,CAAA,cAAW;;;AA4Db,MAAM,yBAAyB,CAAC;;IACrC,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAwB;KAAQ;8CACjC;YACE,MAAM,cAAc,IAAI;YAExB,IAAI,SAAS,WAAW;gBACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;gBAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;YAC3D;YAEA,IAAI,SAAS,WAAW;gBACtB,QAAQ,SAAS,CAAC,OAAO;0DAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;;YAErD;YAEA,MAAM,MAAM,CAAC,mCAAmC,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YAC9G,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MACR,CAAC,sCAAsC,EAAE,SAAS,UAAU,EAAE;YAElE;YACA,OAAO,SAAS,IAAI;QACtB;6CACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;IA/Ba;;QACJ,qIAAA,CAAA,cAAW;;;AAsCb,MAAM,sBAAsB,CAAC;;IAClC,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAqB;KAAQ;2CAC9B;YACE,MAAM,cAAc,IAAI;YAExB,IAAI,SAAS,WAAW;gBACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;gBAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;YAC3D;YAEA,IAAI,SAAS,WAAW;gBACtB,QAAQ,SAAS,CAAC,OAAO;uDAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;;YAErD;YAEA,MAAM,MAAM,CAAC,gCAAgC,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YAC3G,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MACR,CAAC,mCAAmC,EAAE,SAAS,UAAU,EAAE;YAE/D;YACA,OAAO,SAAS,IAAI;QACtB;0CACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;IA/Ba;;QACJ,qIAAA,CAAA,cAAW;;;AAsCb,MAAM,6BAA6B,CAAC;;IACzC,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAA6B;KAAQ;kDACtC;YACE,MAAM,cAAc,IAAI;YAExB,IAAI,SAAS,WAAW;gBACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;gBAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;YAC3D;YAEA,IAAI,SAAS,WAAW;gBACtB,QAAQ,SAAS,CAAC,OAAO;8DAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;;YAErD;YAEA,MAAM,MAAM,CAAC,6BAA6B,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YACxG,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MACR,CAAC,2CAA2C,EAAE,SAAS,UAAU,EAAE;YAEvE;YACA,OAAO,SAAS,IAAI;QACtB;iDACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;IA/Ba;;QACJ,qIAAA,CAAA,cAAW;;;AAsCb,MAAM,0BAA0B,CAAC;;IACtC,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAyB;KAAQ;+CAClC;YACE,MAAM,cAAc,IAAI;YAExB,IAAI,SAAS,WAAW;gBACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;gBAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;YAC3D;YAEA,IAAI,SAAS,WAAW;gBACtB,QAAQ,SAAS,CAAC,OAAO;2DAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;;YAErD;YAEA,MAAM,MAAM,CAAC,oCAAoC,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YAC/G,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MACR,CAAC,uCAAuC,EAAE,SAAS,UAAU,EAAE;YAEnE;YACA,OAAO,SAAS,IAAI;QACtB;8CACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;IA/Ba;;QACJ,qIAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 859, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/hooks/useCrossEntityAnalytics.ts"], "sourcesContent": ["/**\r\n * @file useCrossEntityAnalytics.ts\r\n * @description Hook for fetching cross-entity analytics data following existing patterns\r\n */\r\n\r\nimport { useQuery } from '@tanstack/react-query';\r\nimport { useApiQuery } from '@/hooks/api';\r\nimport { apiClient } from '@/lib/api';\r\nimport type {\r\n  ReportingFilters,\r\n  CrossEntityAnalytics,\r\n} from '../data/types/reporting';\r\n\r\n/**\r\n * Hook for fetching cross-entity analytics data\r\n *\r\n * Follows existing patterns from other analytics hooks.\r\n * Integrates with the established API and caching patterns.\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with cross-entity analytics data\r\n */\r\nexport const useCrossEntityAnalytics = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['cross-entity-analytics', filters],\r\n    async (): Promise<CrossEntityAnalytics> => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        // Defensive programming: Ensure dates are Date objects\r\n        const fromDate =\r\n          filters.dateRange.from instanceof Date\r\n            ? filters.dateRange.from\r\n            : new Date(filters.dateRange.from);\r\n        const toDate =\r\n          filters.dateRange.to instanceof Date\r\n            ? filters.dateRange.to\r\n            : new Date(filters.dateRange.to);\r\n\r\n        queryParams.append(\r\n          'dateRange.from',\r\n          fromDate.toISOString().split('T')[0] || fromDate.toISOString()\r\n        );\r\n        queryParams.append(\r\n          'dateRange.to',\r\n          toDate.toISOString().split('T')[0] || toDate.toISOString()\r\n        );\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      if (filters?.vehicles) {\r\n        filters.vehicles.forEach(vehicle =>\r\n          queryParams.append('vehicles', vehicle.toString())\r\n        );\r\n      }\r\n\r\n      if (filters?.locations) {\r\n        filters.locations.forEach(location =>\r\n          queryParams.append('locations', location)\r\n        );\r\n      }\r\n\r\n      // Note: includeCorrelations doesn't exist in ReportingFilters type\r\n      // if (filters?.includeCorrelations) {\r\n      //   queryParams.append('includeCorrelations', 'true');\r\n      // }\r\n\r\n      const url = `/reporting/cross-entity/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000, // 5 minutes\r\n      enableRetry: true,\r\n      retryAttempts: 3,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching employee-vehicle correlations specifically\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with employee-vehicle correlation data\r\n */\r\nexport const useEmployeeVehicleCorrelations = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['employee-vehicle-correlations', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      if (filters?.vehicles) {\r\n        filters.vehicles.forEach(vehicle =>\r\n          queryParams.append('vehicles', vehicle.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/reporting/employee-vehicle-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching task-delegation correlations\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with task-delegation correlation data\r\n */\r\nexport const useTaskDelegationCorrelations = (filters?: ReportingFilters) => {\r\n  return useApiQuery(\r\n    ['task-delegation-correlations', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      const url = `/reporting/task-delegation-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n\r\n/**\r\n * Hook for fetching performance-workload correlations\r\n *\r\n * @param filters - Optional reporting filters to apply\r\n * @returns Query result with performance-workload correlation data\r\n */\r\nexport const usePerformanceWorkloadCorrelations = (\r\n  filters?: ReportingFilters\r\n) => {\r\n  return useApiQuery(\r\n    ['performance-workload-correlations', filters],\r\n    async () => {\r\n      const queryParams = new URLSearchParams();\r\n\r\n      if (filters?.dateRange) {\r\n        queryParams.append('from', filters.dateRange.from.toISOString());\r\n        queryParams.append('to', filters.dateRange.to.toISOString());\r\n      }\r\n\r\n      if (filters?.employees) {\r\n        filters.employees.forEach(employee =>\r\n          queryParams.append('employees', employee.toString())\r\n        );\r\n      }\r\n\r\n      const url = `/reporting/performance-workload-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n      const result = (await apiClient.get(url)) as any;\r\n      return result.data || result;\r\n    },\r\n    {\r\n      cacheDuration: 5 * 60 * 1000,\r\n      enableRetry: true,\r\n    }\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAGD;AAAA;AACA;AAAA;;;;AAeO,MAAM,0BAA0B,CAAC;;IACtC,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAA0B;KAAQ;+CACnC;YACE,MAAM,cAAc,IAAI;YAExB,IAAI,SAAS,WAAW;gBACtB,uDAAuD;gBACvD,MAAM,WACJ,QAAQ,SAAS,CAAC,IAAI,YAAY,OAC9B,QAAQ,SAAS,CAAC,IAAI,GACtB,IAAI,KAAK,QAAQ,SAAS,CAAC,IAAI;gBACrC,MAAM,SACJ,QAAQ,SAAS,CAAC,EAAE,YAAY,OAC5B,QAAQ,SAAS,CAAC,EAAE,GACpB,IAAI,KAAK,QAAQ,SAAS,CAAC,EAAE;gBAEnC,YAAY,MAAM,CAChB,kBACA,SAAS,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,SAAS,WAAW;gBAE9D,YAAY,MAAM,CAChB,gBACA,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO,WAAW;YAE5D;YAEA,IAAI,SAAS,WAAW;gBACtB,QAAQ,SAAS,CAAC,OAAO;2DAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;;YAErD;YAEA,IAAI,SAAS,UAAU;gBACrB,QAAQ,QAAQ,CAAC,OAAO;2DAAC,CAAA,UACvB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;;YAEnD;YAEA,IAAI,SAAS,WAAW;gBACtB,QAAQ,SAAS,CAAC,OAAO;2DAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa;;YAEpC;YAEA,mEAAmE;YACnE,sCAAsC;YACtC,uDAAuD;YACvD,IAAI;YAEJ,MAAM,MAAM,CAAC,iCAAiC,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YAC5G,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACpC,OAAO,OAAO,IAAI,IAAI;QACxB;8CACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;QACb,eAAe;IACjB;AAEJ;GA5Da;;QACJ,qIAAA,CAAA,cAAW;;;AAmEb,MAAM,iCAAiC,CAAC;;IAC7C,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAiC;KAAQ;sDAC1C;YACE,MAAM,cAAc,IAAI;YAExB,IAAI,SAAS,WAAW;gBACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;gBAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;YAC3D;YAEA,IAAI,SAAS,WAAW;gBACtB,QAAQ,SAAS,CAAC,OAAO;kEAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;;YAErD;YAEA,IAAI,SAAS,UAAU;gBACrB,QAAQ,QAAQ,CAAC,OAAO;kEAAC,CAAA,UACvB,YAAY,MAAM,CAAC,YAAY,QAAQ,QAAQ;;YAEnD;YAEA,MAAM,MAAM,CAAC,wCAAwC,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YACnH,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACpC,OAAO,OAAO,IAAI,IAAI;QACxB;qDACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;IAhCa;;QACJ,qIAAA,CAAA,cAAW;;;AAuCb,MAAM,gCAAgC,CAAC;;IAC5C,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAgC;KAAQ;qDACzC;YACE,MAAM,cAAc,IAAI;YAExB,IAAI,SAAS,WAAW;gBACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;gBAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;YAC3D;YAEA,MAAM,MAAM,CAAC,uCAAuC,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YAClH,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACpC,OAAO,OAAO,IAAI,IAAI;QACxB;oDACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;IApBa;;QACJ,qIAAA,CAAA,cAAW;;;AA2Bb,MAAM,qCAAqC,CAChD;;IAEA,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EACf;QAAC;QAAqC;KAAQ;0DAC9C;YACE,MAAM,cAAc,IAAI;YAExB,IAAI,SAAS,WAAW;gBACtB,YAAY,MAAM,CAAC,QAAQ,QAAQ,SAAS,CAAC,IAAI,CAAC,WAAW;gBAC7D,YAAY,MAAM,CAAC,MAAM,QAAQ,SAAS,CAAC,EAAE,CAAC,WAAW;YAC3D;YAEA,IAAI,SAAS,WAAW;gBACtB,QAAQ,SAAS,CAAC,OAAO;sEAAC,CAAA,WACxB,YAAY,MAAM,CAAC,aAAa,SAAS,QAAQ;;YAErD;YAEA,MAAM,MAAM,CAAC,4CAA4C,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;YACvH,MAAM,SAAU,MAAM,6IAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACpC,OAAO,OAAO,IAAI,IAAI;QACxB;yDACA;QACE,eAAe,IAAI,KAAK;QACxB,aAAa;IACf;AAEJ;IA5Ba;;QAGJ,qIAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1026, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/components/DateRangePicker.tsx"], "sourcesContent": ["/**\r\n * @file DateRangePicker.tsx\r\n * @description Date range picker component for report filtering\r\n */\r\n\r\nimport React, { useState, useEffect, useMemo } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Calendar } from '@/components/ui/calendar';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { CalendarIcon, X, AlertTriangle, CheckCircle } from 'lucide-react';\r\nimport {\r\n  format,\r\n  subDays,\r\n  subWeeks,\r\n  subMonths,\r\n  startOfMonth,\r\n  endOfMonth,\r\n  startOfYear,\r\n  endOfYear,\r\n  differenceInDays,\r\n  isAfter,\r\n  isBefore,\r\n  isValid,\r\n} from 'date-fns';\r\nimport { cn } from '@/lib/utils';\r\n\r\n/**\r\n * Date range interface\r\n */\r\nexport interface DateRange {\r\n  from: Date;\r\n  to: Date;\r\n}\r\n\r\n/**\r\n * Predefined date range options with enhanced defaults\r\n */\r\nconst PRESET_RANGES = [\r\n  {\r\n    label: 'Today',\r\n    getValue: () => ({\r\n      from: new Date(),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'Yesterday',\r\n    getValue: () => {\r\n      const yesterday = subDays(new Date(), 1);\r\n      return {\r\n        from: yesterday,\r\n        to: yesterday,\r\n      };\r\n    },\r\n  },\r\n  {\r\n    label: 'Last 3 days',\r\n    getValue: () => ({\r\n      from: subDays(new Date(), 2),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'Last 7 days',\r\n    getValue: () => ({\r\n      from: subDays(new Date(), 6),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'Last 2 weeks',\r\n    getValue: () => ({\r\n      from: subWeeks(new Date(), 2),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'Last 30 days',\r\n    getValue: () => ({\r\n      from: subDays(new Date(), 29),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'This week',\r\n    getValue: () => ({\r\n      from: subDays(new Date(), new Date().getDay()),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'This month',\r\n    getValue: () => ({\r\n      from: startOfMonth(new Date()),\r\n      to: endOfMonth(new Date()),\r\n    }),\r\n  },\r\n  {\r\n    label: 'Last month',\r\n    getValue: () => {\r\n      const lastMonth = subMonths(new Date(), 1);\r\n      return {\r\n        from: startOfMonth(lastMonth),\r\n        to: endOfMonth(lastMonth),\r\n      };\r\n    },\r\n  },\r\n  {\r\n    label: 'Last 3 months',\r\n    getValue: () => ({\r\n      from: subMonths(new Date(), 3),\r\n      to: new Date(),\r\n    }),\r\n  },\r\n  {\r\n    label: 'This year',\r\n    getValue: () => ({\r\n      from: startOfYear(new Date()),\r\n      to: endOfYear(new Date()),\r\n    }),\r\n  },\r\n];\r\n\r\n/**\r\n * Validation result interface\r\n */\r\ninterface ValidationResult {\r\n  isValid: boolean;\r\n  message?: string;\r\n  type?: 'error' | 'warning' | 'info';\r\n}\r\n\r\ninterface DateRangePickerProps {\r\n  value?: DateRange | null;\r\n  onChange?: (range: DateRange | null) => void;\r\n  placeholder?: string;\r\n  className?: string;\r\n  disabled?: boolean;\r\n  maxDays?: number; // Maximum allowed days in range\r\n  minDays?: number; // Minimum required days in range\r\n  maxDate?: Date; // Maximum allowed end date\r\n  minDate?: Date; // Minimum allowed start date\r\n  showValidation?: boolean; // Show real-time validation feedback\r\n}\r\n\r\n/**\r\n * DateRangePicker Component\r\n *\r\n * Enhanced with real-time validation and performance optimization.\r\n * Follows SOLID principles with single responsibility for date range selection.\r\n */\r\nexport const DateRangePicker: React.FC<DateRangePickerProps> = ({\r\n  value,\r\n  onChange,\r\n  placeholder = 'Select date range',\r\n  className,\r\n  disabled = false,\r\n  maxDays = 365, // Default max 1 year\r\n  minDays = 1, // Default min 1 day\r\n  maxDate = new Date(), // Default to today\r\n  minDate = new Date(2020, 0, 1), // Default to 2020\r\n  showValidation = true,\r\n}) => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [tempRange, setTempRange] = useState<DateRange | null>(value || null);\r\n\r\n  // Real-time validation with memoization for performance\r\n  const validation = useMemo((): ValidationResult => {\r\n    if (!tempRange) {\r\n      return { isValid: true };\r\n    }\r\n\r\n    const { from, to } = tempRange;\r\n\r\n    // Validate date objects\r\n    if (!isValid(from) || !isValid(to)) {\r\n      return {\r\n        isValid: false,\r\n        message: 'Invalid date selected',\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    // Check date order\r\n    if (isAfter(from, to)) {\r\n      return {\r\n        isValid: false,\r\n        message: 'Start date must be before end date',\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    // Check date range limits\r\n    if (minDate && isBefore(from, minDate)) {\r\n      return {\r\n        isValid: false,\r\n        message: `Start date cannot be before ${format(minDate, 'MMM dd, yyyy')}`,\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    if (maxDate && isAfter(to, maxDate)) {\r\n      return {\r\n        isValid: false,\r\n        message: `End date cannot be after ${format(maxDate, 'MMM dd, yyyy')}`,\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    // Check range duration\r\n    const daysDiff = differenceInDays(to, from) + 1;\r\n\r\n    if (daysDiff < minDays) {\r\n      return {\r\n        isValid: false,\r\n        message: `Date range must be at least ${minDays} day${minDays > 1 ? 's' : ''}`,\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    if (daysDiff > maxDays) {\r\n      return {\r\n        isValid: false,\r\n        message: `Date range cannot exceed ${maxDays} days`,\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    // Performance warning for large ranges\r\n    if (daysDiff > 90) {\r\n      return {\r\n        isValid: true,\r\n        message: `Large date range (${daysDiff} days) may affect performance`,\r\n        type: 'warning',\r\n      };\r\n    }\r\n\r\n    return {\r\n      isValid: true,\r\n      message: `${daysDiff} day${daysDiff > 1 ? 's' : ''} selected`,\r\n      type: 'info',\r\n    };\r\n  }, [tempRange, maxDays, minDays, maxDate, minDate]);\r\n\r\n  // Update temp range when value changes\r\n  useEffect(() => {\r\n    setTempRange(value || null);\r\n  }, [value]);\r\n\r\n  /**\r\n   * Handle preset range selection with validation\r\n   */\r\n  const handlePresetSelect = (preset: (typeof PRESET_RANGES)[0]) => {\r\n    const range = preset.getValue();\r\n    setTempRange(range);\r\n\r\n    // Only call onChange if validation passes\r\n    const tempValidation = validateRange(range);\r\n    if (tempValidation.isValid) {\r\n      onChange?.(range);\r\n      setIsOpen(false);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Validate a date range\r\n   */\r\n  const validateRange = (range: DateRange | null): ValidationResult => {\r\n    if (!range) return { isValid: true };\r\n\r\n    const { from, to } = range;\r\n    const daysDiff = differenceInDays(to, from) + 1;\r\n\r\n    if (daysDiff > maxDays) {\r\n      return {\r\n        isValid: false,\r\n        message: `Date range cannot exceed ${maxDays} days`,\r\n        type: 'error',\r\n      };\r\n    }\r\n\r\n    return { isValid: true };\r\n  };\r\n\r\n  /**\r\n   * Handle custom date selection with enhanced logic\r\n   */\r\n  const handleDateSelect = (date: Date | undefined) => {\r\n    if (!date || disabled) return;\r\n\r\n    // Validate date is within allowed range\r\n    if (minDate && isBefore(date, minDate)) return;\r\n    if (maxDate && isAfter(date, maxDate)) return;\r\n\r\n    if (!tempRange || (tempRange.from && tempRange.to)) {\r\n      // Start new range\r\n      const newRange = { from: date, to: date };\r\n      setTempRange(newRange);\r\n    } else if (tempRange.from && !tempRange.to) {\r\n      // Complete the range\r\n      const newRange = {\r\n        from: date < tempRange.from ? date : tempRange.from,\r\n        to: date < tempRange.from ? tempRange.from : date,\r\n      };\r\n      setTempRange(newRange);\r\n\r\n      // Only call onChange if validation passes\r\n      const tempValidation = validateRange(newRange);\r\n      if (tempValidation.isValid) {\r\n        onChange?.(newRange);\r\n        setIsOpen(false);\r\n      }\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Handle range clear\r\n   */\r\n  const handleClear = () => {\r\n    setTempRange(null);\r\n    onChange?.(null);\r\n    setIsOpen(false);\r\n  };\r\n\r\n  /**\r\n   * Format display text\r\n   */\r\n  const getDisplayText = () => {\r\n    if (!value) return placeholder;\r\n\r\n    if (value.from.toDateString() === value.to.toDateString()) {\r\n      return format(value.from, 'MMM dd, yyyy');\r\n    }\r\n\r\n    return `${format(value.from, 'MMM dd, yyyy')} - ${format(value.to, 'MMM dd, yyyy')}`;\r\n  };\r\n\r\n  return (\r\n    <div className={cn('relative', className)}>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"outline\"\r\n            className={cn(\r\n              'w-full justify-start text-left font-normal',\r\n              !value && 'text-muted-foreground'\r\n            )}\r\n            disabled={disabled}\r\n          >\r\n            <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n            {getDisplayText()}\r\n            {value && (\r\n              <Badge variant=\"secondary\" className=\"ml-auto\">\r\n                {Math.ceil(\r\n                  (value.to.getTime() - value.from.getTime()) /\r\n                    (1000 * 60 * 60 * 24)\r\n                ) + 1}{' '}\r\n                days\r\n              </Badge>\r\n            )}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-auto max-w-4xl p-0\" align=\"start\">\r\n          <div className=\"flex\">\r\n            {/* Preset Options */}\r\n            <div className=\"border-r p-4 space-y-2 min-w-[160px]\">\r\n              <h4 className=\"font-medium text-sm text-gray-900 mb-3\">\r\n                Quick Select\r\n              </h4>\r\n              {PRESET_RANGES.map(preset => (\r\n                <Button\r\n                  key={preset.label}\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"w-full justify-start text-xs h-8 px-2\"\r\n                  onClick={() => handlePresetSelect(preset)}\r\n                >\r\n                  {preset.label}\r\n                </Button>\r\n              ))}\r\n\r\n              {value && (\r\n                <>\r\n                  <div className=\"border-t pt-2 mt-3\">\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"sm\"\r\n                      className=\"w-full justify-start text-sm text-red-600 hover:text-red-700\"\r\n                      onClick={handleClear}\r\n                    >\r\n                      <X className=\"mr-2 h-3 w-3\" />\r\n                      Clear\r\n                    </Button>\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n\r\n            {/* Enhanced Calendar with Range Mode */}\r\n            <div className=\"p-4 min-w-[600px]\">\r\n              <Calendar\r\n                mode=\"range\"\r\n                selected={tempRange || undefined}\r\n                onSelect={range => {\r\n                  if (range && range.from) {\r\n                    const dateRange = {\r\n                      from: range.from,\r\n                      to: range.to || range.from,\r\n                    };\r\n                    setTempRange(dateRange);\r\n                    // Auto-apply valid ranges\r\n                    const tempValidation = validateRange(dateRange);\r\n                    if (\r\n                      tempValidation.isValid &&\r\n                      dateRange.from &&\r\n                      dateRange.to\r\n                    ) {\r\n                      onChange?.(dateRange);\r\n                      setIsOpen(false);\r\n                    }\r\n                  } else {\r\n                    setTempRange(null);\r\n                  }\r\n                }}\r\n                numberOfMonths={2}\r\n                className=\"rounded-md border-0\"\r\n                disabled={[\r\n                  // Disable dates outside the allowed range\r\n                  ...(minDate ? [{ before: minDate }] : []),\r\n                  ...(maxDate ? [{ after: maxDate }] : []),\r\n                ]}\r\n                showOutsideDays\r\n                fixedWeeks\r\n              />\r\n\r\n              {tempRange && (\r\n                <div className=\"mt-4 p-3 bg-gray-50 rounded-lg\">\r\n                  <h5 className=\"font-medium text-sm text-gray-900 mb-2\">\r\n                    Selected Range\r\n                  </h5>\r\n                  <div className=\"space-y-1 text-sm text-gray-600\">\r\n                    <div>From: {format(tempRange.from, 'MMM dd, yyyy')}</div>\r\n                    <div>To: {format(tempRange.to, 'MMM dd, yyyy')}</div>\r\n                    <div className=\"text-xs text-gray-500\">\r\n                      {Math.ceil(\r\n                        (tempRange.to.getTime() - tempRange.from.getTime()) /\r\n                          (1000 * 60 * 60 * 24)\r\n                      ) + 1}{' '}\r\n                      days\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Real-time Validation Feedback */}\r\n                  {showValidation && validation.message && (\r\n                    <Alert\r\n                      className={cn(\r\n                        'mt-3 py-2 px-3',\r\n                        validation.type === 'error' &&\r\n                          'border-red-200 bg-red-50',\r\n                        validation.type === 'warning' &&\r\n                          'border-yellow-200 bg-yellow-50',\r\n                        validation.type === 'info' &&\r\n                          'border-blue-200 bg-blue-50'\r\n                      )}\r\n                    >\r\n                      <div className=\"flex items-center gap-2\">\r\n                        {validation.type === 'error' && (\r\n                          <AlertTriangle className=\"h-3 w-3 text-red-600\" />\r\n                        )}\r\n                        {validation.type === 'warning' && (\r\n                          <AlertTriangle className=\"h-3 w-3 text-yellow-600\" />\r\n                        )}\r\n                        {validation.type === 'info' && (\r\n                          <CheckCircle className=\"h-3 w-3 text-blue-600\" />\r\n                        )}\r\n                        <AlertDescription\r\n                          className={cn(\r\n                            'text-xs',\r\n                            validation.type === 'error' && 'text-red-700',\r\n                            validation.type === 'warning' && 'text-yellow-700',\r\n                            validation.type === 'info' && 'text-blue-700'\r\n                          )}\r\n                        >\r\n                          {validation.message}\r\n                        </AlertDescription>\r\n                      </div>\r\n                    </Alert>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AACA;AACA;AAYA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAAA;;;;;;;;;;;;AAUA;;CAEC,GACD,MAAM,gBAAgB;IACpB;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,IAAI;gBACV,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU;YACR,MAAM,YAAY,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;YACtC,OAAO;gBACL,MAAM;gBACN,IAAI;YACN;QACF;IACF;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;gBAC1B,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;gBAC1B,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,QAAQ;gBAC3B,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;gBAC1B,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ,IAAI,OAAO,MAAM;gBAC3C,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,+IAAA,CAAA,eAAY,AAAD,EAAE,IAAI;gBACvB,IAAI,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE,IAAI;YACrB,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU;YACR,MAAM,YAAY,CAAA,GAAA,4IAAA,CAAA,YAAS,AAAD,EAAE,IAAI,QAAQ;YACxC,OAAO;gBACL,MAAM,CAAA,GAAA,+IAAA,CAAA,eAAY,AAAD,EAAE;gBACnB,IAAI,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;YACjB;QACF;IACF;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,4IAAA,CAAA,YAAS,AAAD,EAAE,IAAI,QAAQ;gBAC5B,IAAI,IAAI;YACV,CAAC;IACH;IACA;QACE,OAAO;QACP,UAAU,IAAM,CAAC;gBACf,MAAM,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD,EAAE,IAAI;gBACtB,IAAI,CAAA,GAAA,4IAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YACpB,CAAC;IACH;CACD;AA8BM,MAAM,kBAAkD,CAAC,EAC9D,KAAK,EACL,QAAQ,EACR,cAAc,mBAAmB,EACjC,SAAS,EACT,WAAW,KAAK,EAChB,UAAU,GAAG,EACb,UAAU,CAAC,EACX,UAAU,IAAI,MAAM,EACpB,UAAU,IAAI,KAAK,MAAM,GAAG,EAAE,EAC9B,iBAAiB,IAAI,EACtB;;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,SAAS;IAEtE,wDAAwD;IACxD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YACzB,IAAI,CAAC,WAAW;gBACd,OAAO;oBAAE,SAAS;gBAAK;YACzB;YAEA,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG;YAErB,wBAAwB;YACxB,IAAI,CAAC,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,SAAS,CAAC,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,KAAK;gBAClC,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,MAAM;gBACR;YACF;YAEA,mBAAmB;YACnB,IAAI,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,KAAK;gBACrB,OAAO;oBACL,SAAS;oBACT,SAAS;oBACT,MAAM;gBACR;YACF;YAEA,0BAA0B;YAC1B,IAAI,WAAW,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,UAAU;gBACtC,OAAO;oBACL,SAAS;oBACT,SAAS,CAAC,4BAA4B,EAAE,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,iBAAiB;oBACzE,MAAM;gBACR;YACF;YAEA,IAAI,WAAW,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,IAAI,UAAU;gBACnC,OAAO;oBACL,SAAS;oBACT,SAAS,CAAC,yBAAyB,EAAE,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,iBAAiB;oBACtE,MAAM;gBACR;YACF;YAEA,uBAAuB;YACvB,MAAM,WAAW,CAAA,GAAA,mJAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,QAAQ;YAE9C,IAAI,WAAW,SAAS;gBACtB,OAAO;oBACL,SAAS;oBACT,SAAS,CAAC,4BAA4B,EAAE,QAAQ,IAAI,EAAE,UAAU,IAAI,MAAM,IAAI;oBAC9E,MAAM;gBACR;YACF;YAEA,IAAI,WAAW,SAAS;gBACtB,OAAO;oBACL,SAAS;oBACT,SAAS,CAAC,yBAAyB,EAAE,QAAQ,KAAK,CAAC;oBACnD,MAAM;gBACR;YACF;YAEA,uCAAuC;YACvC,IAAI,WAAW,IAAI;gBACjB,OAAO;oBACL,SAAS;oBACT,SAAS,CAAC,kBAAkB,EAAE,SAAS,6BAA6B,CAAC;oBACrE,MAAM;gBACR;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,SAAS,GAAG,SAAS,IAAI,EAAE,WAAW,IAAI,MAAM,GAAG,SAAS,CAAC;gBAC7D,MAAM;YACR;QACF;8CAAG;QAAC;QAAW;QAAS;QAAS;QAAS;KAAQ;IAElD,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,aAAa,SAAS;QACxB;oCAAG;QAAC;KAAM;IAEV;;GAEC,GACD,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,OAAO,QAAQ;QAC7B,aAAa;QAEb,0CAA0C;QAC1C,MAAM,iBAAiB,cAAc;QACrC,IAAI,eAAe,OAAO,EAAE;YAC1B,WAAW;YACX,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,OAAO,OAAO;YAAE,SAAS;QAAK;QAEnC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG;QACrB,MAAM,WAAW,CAAA,GAAA,mJAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,QAAQ;QAE9C,IAAI,WAAW,SAAS;YACtB,OAAO;gBACL,SAAS;gBACT,SAAS,CAAC,yBAAyB,EAAE,QAAQ,KAAK,CAAC;gBACnD,MAAM;YACR;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA;;GAEC,GACD,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,QAAQ,UAAU;QAEvB,wCAAwC;QACxC,IAAI,WAAW,CAAA,GAAA,2IAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,UAAU;QACxC,IAAI,WAAW,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,MAAM,UAAU;QAEvC,IAAI,CAAC,aAAc,UAAU,IAAI,IAAI,UAAU,EAAE,EAAG;YAClD,kBAAkB;YAClB,MAAM,WAAW;gBAAE,MAAM;gBAAM,IAAI;YAAK;YACxC,aAAa;QACf,OAAO,IAAI,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YAC1C,qBAAqB;YACrB,MAAM,WAAW;gBACf,MAAM,OAAO,UAAU,IAAI,GAAG,OAAO,UAAU,IAAI;gBACnD,IAAI,OAAO,UAAU,IAAI,GAAG,UAAU,IAAI,GAAG;YAC/C;YACA,aAAa;YAEb,0CAA0C;YAC1C,MAAM,iBAAiB,cAAc;YACrC,IAAI,eAAe,OAAO,EAAE;gBAC1B,WAAW;gBACX,UAAU;YACZ;QACF;IACF;IAEA;;GAEC,GACD,MAAM,cAAc;QAClB,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IAEA;;GAEC,GACD,MAAM,iBAAiB;QACrB,IAAI,CAAC,OAAO,OAAO;QAEnB,IAAI,MAAM,IAAI,CAAC,YAAY,OAAO,MAAM,EAAE,CAAC,YAAY,IAAI;YACzD,OAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EAAE;QAC5B;QAEA,OAAO,GAAG,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EAAE,gBAAgB,GAAG,EAAE,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,EAAE,EAAE,iBAAiB;IACtF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kBAC7B,cAAA,6LAAC,sIAAA,CAAA,UAAO;YAAC,MAAM;YAAQ,cAAc;;8BACnC,6LAAC,sIAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,SAAS;wBAEZ,UAAU;;0CAEV,6LAAC,iNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BACvB;4BACA,uBACC,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;;oCAClC,KAAK,IAAI,CACR,CAAC,MAAM,EAAE,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,OAAO,EAAE,IACxC,CAAC,OAAO,KAAK,KAAK,EAAE,KACpB;oCAAG;oCAAI;;;;;;;;;;;;;;;;;;8BAMnB,6LAAC,sIAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAuB,OAAM;8BACrD,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;oCAGtD,cAAc,GAAG,CAAC,CAAA,uBACjB,6LAAC,qIAAA,CAAA,SAAM;4CAEL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,mBAAmB;sDAEjC,OAAO,KAAK;2CANR,OAAO,KAAK;;;;;oCAUpB,uBACC;kDACE,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;;kEAET,6LAAC,+LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CASxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uIAAA,CAAA,WAAQ;wCACP,MAAK;wCACL,UAAU,aAAa;wCACvB,UAAU,CAAA;4CACR,IAAI,SAAS,MAAM,IAAI,EAAE;gDACvB,MAAM,YAAY;oDAChB,MAAM,MAAM,IAAI;oDAChB,IAAI,MAAM,EAAE,IAAI,MAAM,IAAI;gDAC5B;gDACA,aAAa;gDACb,0BAA0B;gDAC1B,MAAM,iBAAiB,cAAc;gDACrC,IACE,eAAe,OAAO,IACtB,UAAU,IAAI,IACd,UAAU,EAAE,EACZ;oDACA,WAAW;oDACX,UAAU;gDACZ;4CACF,OAAO;gDACL,aAAa;4CACf;wCACF;wCACA,gBAAgB;wCAChB,WAAU;wCACV,UAAU;4CACR,0CAA0C;+CACtC,UAAU;gDAAC;oDAAE,QAAQ;gDAAQ;6CAAE,GAAG,EAAE;+CACpC,UAAU;gDAAC;oDAAE,OAAO;gDAAQ;6CAAE,GAAG,EAAE;yCACxC;wCACD,eAAe;wCACf,UAAU;;;;;;oCAGX,2BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DAGvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;4DAAI;4DAAO,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,UAAU,IAAI,EAAE;;;;;;;kEACnC,6LAAC;;4DAAI;4DAAK,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,UAAU,EAAE,EAAE;;;;;;;kEAC/B,6LAAC;wDAAI,WAAU;;4DACZ,KAAK,IAAI,CACR,CAAC,UAAU,EAAE,CAAC,OAAO,KAAK,UAAU,IAAI,CAAC,OAAO,EAAE,IAChD,CAAC,OAAO,KAAK,KAAK,EAAE,KACpB;4DAAG;4DAAI;;;;;;;;;;;;;4CAMd,kBAAkB,WAAW,OAAO,kBACnC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,kBACA,WAAW,IAAI,KAAK,WAClB,4BACF,WAAW,IAAI,KAAK,aAClB,kCACF,WAAW,IAAI,KAAK,UAClB;0DAGJ,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,WAAW,IAAI,KAAK,yBACnB,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAE1B,WAAW,IAAI,KAAK,2BACnB,6LAAC,2NAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAE1B,WAAW,IAAI,KAAK,wBACnB,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEAEzB,6LAAC,oIAAA,CAAA,mBAAgB;4DACf,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,WACA,WAAW,IAAI,KAAK,WAAW,gBAC/B,WAAW,IAAI,KAAK,aAAa,mBACjC,WAAW,IAAI,KAAK,UAAU;sEAG/B,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa7C;GAzVa;KAAA", "debugId": null}}]}