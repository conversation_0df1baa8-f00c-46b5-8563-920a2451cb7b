# Environment Configuration Guide

## 🌍 Quick Environment Switching

### For Network Access (**************:9002)

**Option 1: Use Environment Switcher Script**
```bash
cd frontend
node scripts/switch-env.js network
npm run dev
```

**Option 2: Manual Configuration**
```bash
cd frontend
cp .env.network .env.local
npm run dev
```

### For Localhost Development
```bash
cd frontend
node scripts/switch-env.js local
npm run dev
```

### For Production Deployment
```bash
cd frontend
node scripts/switch-env.js production
npm run build
```

## 🔧 Environment Files Overview

| File | Purpose | API URL | WebSocket URL |
|------|---------|---------|---------------|
| `.env.local` | Default localhost development | `http://localhost:3001/api` | `ws://localhost:3001` |
| `.env.network` | Network access development | `http://**************:3001/api` | `ws://**************:3001` |
| `.env.production` | Production deployment | `https://your-api.com/api` | `wss://your-api.com` |

## 🚀 Automatic Environment Detection

The application now includes automatic environment detection that:

1. **Detects deployment context** (localhost, network, docker, cloud)
2. **Generates appropriate URLs** based on current hostname
3. **Falls back to environment variables** when explicitly set
4. **Provides debugging information** in development mode

### Environment Detection Logic

```typescript
// Automatic detection based on hostname
if (hostname.startsWith('192.168.')) {
  // Network deployment - use same IP with port 3001
  apiUrl = `http://${hostname}:3001/api`;
} else if (hostname === 'localhost') {
  // Local development
  apiUrl = 'http://localhost:3001/api';
} else {
  // Production/cloud deployment
  apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
}
```

## 🔍 Troubleshooting

### Issue: API calls still go to localhost

**Solution:**
1. Verify environment file is correct
2. Restart development server
3. Clear browser cache
4. Check console for environment config:
   ```javascript
   // In browser DevTools console
   console.log('API Base URL:', process.env.NEXT_PUBLIC_API_BASE_URL);
   ```

### Issue: WebSocket authentication fails

**Solution:**
1. Ensure backend includes your IP in CORS configuration
2. Verify WebSocket URL is correct
3. Clear browser storage and re-login
4. Check backend logs for authentication errors

### Issue: CORS errors

**Solution:**
1. Add your IP to backend `FRONTEND_URL`:
   ```env
   FRONTEND_URL=http://localhost:9002,http://**************:9002
   ```
2. Restart backend service
3. Verify no typos in IP addresses

## 📋 Backend CORS Configuration

Ensure your backend `.env` includes all required origins:

```env
# Backend .env file
FRONTEND_URL=http://localhost:9002,http://localhost:3000,http://**************:9002,https://your-production-domain.com
```

## 🔄 Development Workflow

### For Local Development
```bash
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend (localhost)
cd frontend
node scripts/switch-env.js local
npm run dev
# Access: http://localhost:9002
```

### For Network Testing
```bash
# Terminal 1 - Backend (same as above)
cd backend
npm run dev

# Terminal 2 - Frontend (network)
cd frontend
node scripts/switch-env.js network
npm run dev
# Access: http://**************:9002
```

### For Production Build
```bash
# Frontend
cd frontend
node scripts/switch-env.js production
npm run build
npm start

# Backend
cd backend
NODE_ENV=production npm start
```

## 🛡️ Security Considerations

### Development
- Debug logging enabled
- CORS allows development origins
- Less restrictive security policies

### Network Access
- Same security as development
- Additional CORS origins for network IPs
- Suitable for team testing

### Production
- Debug logging disabled
- Strict CORS policies
- HTTPS/WSS enforced
- Secure headers enabled
- Production secrets used

## 📊 Environment Validation

Use the built-in validation to check configuration:

```typescript
import { validateEnvironmentConfig, logEnvironmentConfig } from '@/lib/config/environment';

// Log current configuration
logEnvironmentConfig();

// Validate configuration
const validation = validateEnvironmentConfig();
if (!validation.isValid) {
  console.error('Environment configuration errors:', validation.errors);
}
```

## 🔧 Custom Environment Setup

For custom network configurations, create a new environment file:

```bash
# Create custom environment file
cp frontend/.env.network frontend/.env.custom

# Edit the file with your specific IP/domain
# NEXT_PUBLIC_API_BASE_URL=http://your-custom-ip:3001/api
# NEXT_PUBLIC_WS_URL=ws://your-custom-ip:3001

# Use the custom configuration
cp frontend/.env.custom frontend/.env.local
npm run dev
```
