(()=>{var e={};e.id=1011,e.ids=[1011],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5149:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>l,tree:()=>p});var s=r(65239),i=r(48088),a=r(88170),o=r.n(a),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let p={children:["",{children:["tasks",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,15593)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\add\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,34595)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\add\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/tasks/add/page",pathname:"/tasks/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15593:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\tasks\\\\add\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\tasks\\add\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36064:(e,t,r)=>{Promise.resolve().then(r.bind(r,88551))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72512:(e,t,r)=>{Promise.resolve().then(r.bind(r,15593))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88551:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687);let i=(0,r(82614).A)("ListPlus",[["path",{d:"M11 12H3",key:"51ecnj"}],["path",{d:"M16 6H3",key:"1wxfjs"}],["path",{d:"M16 18H3",key:"12xzn7"}],["path",{d:"M18 9v6",key:"1twb98"}],["path",{d:"M21 12h-6",key:"bt1uis"}]]);var a=r(16189),o=r(78207),n=r(48041),d=r(3940),p=r(73227);function c(){let e=(0,a.useRouter)(),{showEntityCreated:t,showEntityCreationError:r}=(0,d.O_)("task"),{error:c,isPending:u,mutateAsync:l}=(0,p.ZY)(),m=async s=>{try{let r={dateTime:s.dateTime,deadline:s.deadline||void 0,description:s.description,driverEmployeeId:s.driverEmployeeId||void 0,estimatedDuration:s.estimatedDuration,location:s.location,notes:s.notes||void 0,priority:s.priority,requiredSkills:s.requiredSkills,staffEmployeeId:s.staffEmployeeId,status:s.status.replace(" ","_"),subtasks:s.subtasks,vehicleId:s.vehicleId||void 0};await l(r);let i={title:s.description.slice(0,30)+(s.description.length>30?"...":""),name:s.description.slice(0,30)+(s.description.length>30?"...":"")};t(i),e.push("/tasks")}catch(e){console.error("Error adding task:",e),r(e.message||c?.message||"Failed to add task. Please try again.")}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(n.z,{description:"Enter the details for the new task or job.",icon:i,title:"Add New Task"}),c&&(0,s.jsxs)("p",{className:"rounded-md bg-red-100 p-3 text-red-500",children:["Error: ",c.message]}),(0,s.jsx)(o.A,{isEditing:!1,onSubmit:m,isLoading:u})]})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,211,1658,8390,2670,9275,6013,5941,7055,9599,2153],()=>r(5149));module.exports=s})();