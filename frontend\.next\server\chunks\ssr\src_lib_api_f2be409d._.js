module.exports = {

"[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]),
    "ApiErrorType": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiErrorType"]),
    "AuthenticationError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["AuthenticationError"]),
    "BadRequestError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["BadRequestError"]),
    "InternalServerError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["InternalServerError"]),
    "NetworkError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["NetworkError"]),
    "NotFoundError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["NotFoundError"]),
    "ServiceError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["ServiceError"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/types/api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ApiError"]),
    "ApiErrorType": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ApiErrorType"]),
    "AuthenticationError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["AuthenticationError"]),
    "BadRequestError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["BadRequestError"]),
    "InternalServerError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["InternalServerError"]),
    "NetworkError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NetworkError"]),
    "NotFoundError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NotFoundError"]),
    "ServiceError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ServiceError"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript) <exports>");
}}),
"[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiClient"]),
    "ApiError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiError"]),
    "ApiErrorType": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiErrorType"]),
    "AuthenticationError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthenticationError"]),
    "BadRequestError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BadRequestError"]),
    "BaseApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseApiService"]),
    "DelegationApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationApiService"]),
    "EmployeeApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeApiService"]),
    "FlightApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$external$2f$flightApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlightApiService"]),
    "FlightDetailsApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$external$2f$flightDetailsApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["FlightDetailsApiService"]),
    "InternalServerError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["InternalServerError"]),
    "NetworkError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NetworkError"]),
    "NotFoundError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NotFoundError"]),
    "SecureApiClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$secureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SecureApiClient"]),
    "SecurityComposer": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$composer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SecurityComposer"]),
    "SecurityConfigProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$SecurityConfigProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SecurityConfigProvider"]),
    "ServiceError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ServiceError"]),
    "TaskApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskApiService"]),
    "VehicleApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleApiService"]),
    "apiClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"]),
    "createSecureApiClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$secureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSecureApiClient"]),
    "createSecurityComposer": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$composer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSecurityComposer"]),
    "getGlobalAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getGlobalAuthTokenProvider"]),
    "setGlobalAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["setGlobalAuthTokenProvider"]),
    "useCSRFProtection": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useCSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCSRFProtection"]),
    "useInputValidation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useInputValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useInputValidation"]),
    "useSecureApiClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecureApiClient"]),
    "useSecureApiReplacement": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecureApiReplacement"]),
    "useSecureHttpClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureHttpClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecureHttpClient"]),
    "useSecurityConfig": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$SecurityConfigProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecurityConfig"]),
    "useSecurityConfigValue": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$SecurityConfigProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecurityConfigValue"]),
    "useSecurityMonitoring": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecurityMonitoring$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecurityMonitoring"]),
    "useSessionSecurity": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSessionSecurity$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSessionSecurity"]),
    "useTokenManagement": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useTokenManagement$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTokenManagement"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$apiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/apiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$errors$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/errors.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$types$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/types.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$interfaces$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/interfaces.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureApiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureHttpClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureHttpClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useCSRFProtection$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useCSRFProtection.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useInputValidation$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useInputValidation.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecurityMonitoring$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecurityMonitoring.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSessionSecurity$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSessionSecurity.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useTokenManagement$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useTokenManagement.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$SecurityConfigProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/providers/SecurityConfigProvider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$composer$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/composer.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$secureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/secureApiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$delegationApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/delegationApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$employeeApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/employeeApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$taskApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/taskApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$domain$2f$vehicleApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/domain/vehicleApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$external$2f$flightApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/external/flightApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$external$2f$flightDetailsApi$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/external/flightDetailsApi.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/index.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ApiClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ApiClient"]),
    "ApiError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ApiError"]),
    "ApiErrorType": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ApiErrorType"]),
    "AuthenticationError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["AuthenticationError"]),
    "BadRequestError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["BadRequestError"]),
    "BaseApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["BaseApiService"]),
    "DelegationApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["DelegationApiService"]),
    "EmployeeApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["EmployeeApiService"]),
    "FlightApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FlightApiService"]),
    "FlightDetailsApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["FlightDetailsApiService"]),
    "InternalServerError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["InternalServerError"]),
    "NetworkError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NetworkError"]),
    "NotFoundError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["NotFoundError"]),
    "SecureApiClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SecureApiClient"]),
    "SecurityComposer": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SecurityComposer"]),
    "SecurityConfigProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SecurityConfigProvider"]),
    "ServiceError": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["ServiceError"]),
    "TaskApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["TaskApiService"]),
    "VehicleApiService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["VehicleApiService"]),
    "apiClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["apiClient"]),
    "createSecureApiClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["createSecureApiClient"]),
    "createSecurityComposer": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["createSecurityComposer"]),
    "getGlobalAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["getGlobalAuthTokenProvider"]),
    "setGlobalAuthTokenProvider": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["setGlobalAuthTokenProvider"]),
    "useCSRFProtection": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["useCSRFProtection"]),
    "useInputValidation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["useInputValidation"]),
    "useSecureApiClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["useSecureApiClient"]),
    "useSecureApiReplacement": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["useSecureApiReplacement"]),
    "useSecureHttpClient": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["useSecureHttpClient"]),
    "useSecurityConfig": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["useSecurityConfig"]),
    "useSecurityConfigValue": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["useSecurityConfigValue"]),
    "useSecurityMonitoring": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["useSecurityMonitoring"]),
    "useSessionSecurity": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["useSessionSecurity"]),
    "useTokenManagement": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__["useTokenManagement"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <exports>");
}}),

};