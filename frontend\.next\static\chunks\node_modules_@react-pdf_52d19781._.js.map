{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/primitives/lib/index.js"], "sourcesContent": ["const G = 'G';\nconst Svg = 'SVG';\nconst View = 'VIEW';\nconst Text = 'TEXT';\nconst Link = 'LINK';\nconst Page = 'PAGE';\nconst Note = 'NOTE';\nconst Path = 'PATH';\nconst Rect = 'RECT';\nconst Line = 'LINE';\nconst FieldSet = 'FIELD_SET';\nconst TextInput = 'TEXT_INPUT';\nconst Select = 'SELECT';\nconst Checkbox = 'CHECKBOX';\nconst List = 'LIST';\nconst Stop = 'STOP';\nconst Defs = 'DEFS';\nconst Image = 'IMAGE';\nconst Tspan = 'TSPAN';\nconst Canvas = 'CANVAS';\nconst Circle = 'CIRCLE';\nconst Ellipse = 'ELLIPSE';\nconst Polygon = 'POLYGON';\nconst Document = 'DOCUMENT';\nconst Polyline = 'POLYLINE';\nconst ClipPath = 'CLIP_PATH';\nconst TextInstance = 'TEXT_INSTANCE';\nconst LinearGradient = 'LINEAR_GRADIENT';\nconst RadialGradient = 'RADIAL_GRADIENT';\n\nexport { Canvas, Checkbox, Circle, ClipPath, Defs, Document, Ellipse, FieldSet, G, Image, Line, LinearGradient, Link, List, Note, Page, Path, Polygon, Polyline, RadialGradient, Rect, Select, Stop, Svg, Text, TextInput, TextInstance, Tspan, View };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAM,IAAI;AACV,MAAM,MAAM;AACZ,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,WAAW;AACjB,MAAM,YAAY;AAClB,MAAM,SAAS;AACf,MAAM,WAAW;AACjB,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,eAAe;AACrB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/font/lib/index.browser.js"], "sourcesContent": ["import 'is-url';\nimport * as fontkit from 'fontkit';\nimport { PDFFont } from '@react-pdf/pdfkit';\n\n// @ts-expect-error ts being silly\nconst STANDARD_FONTS = [\n    'Courier',\n    'Courier-Bold',\n    'Courier-Oblique',\n    'Courier-BoldOblique',\n    'Helvetica',\n    'Helvetica-Bold',\n    'Helvetica-Oblique',\n    'Helvetica-BoldOblique',\n    'Times-Roman',\n    'Times-Bold',\n    'Times-Italic',\n    'Times-BoldItalic',\n];\nclass StandardFont {\n    name;\n    src;\n    fullName;\n    familyName;\n    subfamilyName;\n    postscriptName;\n    copyright;\n    version;\n    underlinePosition;\n    underlineThickness;\n    italicAngle;\n    bbox;\n    'OS/2';\n    hhea;\n    numGlyphs;\n    characterSet;\n    availableFeatures;\n    type;\n    constructor(src) {\n        this.name = src;\n        this.fullName = src;\n        this.familyName = src;\n        this.subfamilyName = src;\n        this.type = 'STANDARD';\n        this.postscriptName = src;\n        this.availableFeatures = [];\n        this.copyright = '';\n        this.version = 1;\n        this.underlinePosition = -100;\n        this.underlineThickness = 50;\n        this.italicAngle = 0;\n        this.bbox = {};\n        this['OS/2'] = {};\n        this.hhea = {};\n        this.numGlyphs = 0;\n        this.characterSet = [];\n        this.src = PDFFont.open(null, src);\n    }\n    encode(str) {\n        return this.src.encode(str);\n    }\n    layout(str) {\n        const [encoded, positions] = this.encode(str);\n        const glyphs = encoded.map((g, i) => {\n            const glyph = this.getGlyph(parseInt(g, 16));\n            glyph.advanceWidth = positions[i].advanceWidth;\n            return glyph;\n        });\n        const advanceWidth = positions.reduce((acc, p) => acc + p.advanceWidth, 0);\n        return {\n            positions,\n            stringIndices: positions.map((_, i) => i),\n            glyphs,\n            script: 'latin',\n            language: 'dflt',\n            direction: 'ltr',\n            features: {},\n            advanceWidth,\n            advanceHeight: 0,\n            bbox: undefined,\n        };\n    }\n    glyphForCodePoint(codePoint) {\n        const glyph = this.getGlyph(codePoint);\n        glyph.advanceWidth = 400;\n        return glyph;\n    }\n    getGlyph(id) {\n        return {\n            id,\n            codePoints: [id],\n            isLigature: false,\n            name: this.src.font.characterToGlyph(id),\n            _font: this.src,\n            // @ts-expect-error assign proper value\n            advanceWidth: undefined,\n        };\n    }\n    hasGlyphForCodePoint(codePoint) {\n        return this.src.font.characterToGlyph(codePoint) !== '.notdef';\n    }\n    // Based on empirical observation\n    get ascent() {\n        return 900;\n    }\n    // Based on empirical observation\n    get capHeight() {\n        switch (this.name) {\n            case 'Times-Roman':\n            case 'Times-Bold':\n            case 'Times-Italic':\n            case 'Times-BoldItalic':\n                return 650;\n            case 'Courier':\n            case 'Courier-Bold':\n            case 'Courier-Oblique':\n            case 'Courier-BoldOblique':\n                return 550;\n            default:\n                return 690;\n        }\n    }\n    // Based on empirical observation\n    get xHeight() {\n        switch (this.name) {\n            case 'Times-Roman':\n            case 'Times-Bold':\n            case 'Times-Italic':\n            case 'Times-BoldItalic':\n                return 440;\n            case 'Courier':\n            case 'Courier-Bold':\n            case 'Courier-Oblique':\n            case 'Courier-BoldOblique':\n                return 390;\n            default:\n                return 490;\n        }\n    }\n    // Based on empirical observation\n    get descent() {\n        switch (this.name) {\n            case 'Times-Roman':\n            case 'Times-Bold':\n            case 'Times-Italic':\n            case 'Times-BoldItalic':\n                return -220;\n            case 'Courier':\n            case 'Courier-Bold':\n            case 'Courier-Oblique':\n            case 'Courier-BoldOblique':\n                return -230;\n            default:\n                return -200;\n        }\n    }\n    get lineGap() {\n        return 0;\n    }\n    get unitsPerEm() {\n        return 1000;\n    }\n    stringsForGlyph() {\n        throw new Error('Method not implemented.');\n    }\n    glyphsForString() {\n        throw new Error('Method not implemented.');\n    }\n    widthOfGlyph() {\n        throw new Error('Method not implemented.');\n    }\n    getAvailableFeatures() {\n        throw new Error('Method not implemented.');\n    }\n    createSubset() {\n        throw new Error('Method not implemented.');\n    }\n    getVariation() {\n        throw new Error('Method not implemented.');\n    }\n    getFont() {\n        throw new Error('Method not implemented.');\n    }\n    getName() {\n        throw new Error('Method not implemented.');\n    }\n    setDefaultLanguage() {\n        throw new Error('Method not implemented.');\n    }\n}\n\nconst fetchFont = async (src, options) => {\n    const response = await fetch(src, options);\n    const data = await response.arrayBuffer();\n    return new Uint8Array(data);\n};\nconst isDataUrl = (dataUrl) => {\n    const header = dataUrl.split(',')[0];\n    const hasDataPrefix = header.substring(0, 5) === 'data:';\n    const hasBase64Prefix = header.split(';')[1] === 'base64';\n    return hasDataPrefix && hasBase64Prefix;\n};\nclass FontSource {\n    src;\n    fontFamily;\n    fontStyle;\n    fontWeight;\n    data;\n    options;\n    loadResultPromise;\n    constructor(src, fontFamily, fontStyle, fontWeight, options) {\n        this.src = src;\n        this.fontFamily = fontFamily;\n        this.fontStyle = fontStyle || 'normal';\n        this.fontWeight = fontWeight || 400;\n        this.data = null;\n        this.options = options || {};\n        this.loadResultPromise = null;\n    }\n    async _load() {\n        const { postscriptName } = this.options;\n        let data = null;\n        if (STANDARD_FONTS.includes(this.src)) {\n            data = new StandardFont(this.src);\n        }\n        else if (isDataUrl(this.src)) {\n            const raw = this.src.split(',')[1];\n            const uint8Array = new Uint8Array(atob(raw)\n                .split('')\n                .map((c) => c.charCodeAt(0)));\n            data = fontkit.create(uint8Array, postscriptName);\n        }\n        else {\n            const { headers, body, method = 'GET' } = this.options;\n            const buffer = await fetchFont(this.src, { method, body, headers });\n            data = fontkit.create(buffer, postscriptName);\n        }\n        if (data && 'fonts' in data) {\n            throw new Error('Font collection is not supported');\n        }\n        this.data = data;\n    }\n    async load() {\n        if (this.loadResultPromise === null) {\n            this.loadResultPromise = this._load();\n        }\n        return this.loadResultPromise;\n    }\n}\n\nconst FONT_WEIGHTS = {\n    thin: 100,\n    hairline: 100,\n    ultralight: 200,\n    extralight: 200,\n    light: 300,\n    normal: 400,\n    medium: 500,\n    semibold: 600,\n    demibold: 600,\n    bold: 700,\n    ultrabold: 800,\n    extrabold: 800,\n    heavy: 900,\n    black: 900,\n};\nconst resolveFontWeight = (value) => {\n    return typeof value === 'string' ? FONT_WEIGHTS[value] : value;\n};\nconst sortByFontWeight = (a, b) => a.fontWeight - b.fontWeight;\nclass FontFamily {\n    family;\n    sources;\n    static create(family) {\n        return new FontFamily(family);\n    }\n    constructor(family) {\n        this.family = family;\n        this.sources = [];\n    }\n    register({ src, fontWeight, fontStyle, ...options }) {\n        const numericFontWeight = fontWeight\n            ? resolveFontWeight(fontWeight)\n            : undefined;\n        this.sources.push(new FontSource(src, this.family, fontStyle, numericFontWeight, options));\n    }\n    resolve(descriptor) {\n        const { fontWeight = 400, fontStyle = 'normal' } = descriptor;\n        const styleSources = this.sources.filter((s) => s.fontStyle === fontStyle);\n        const exactFit = styleSources.find((s) => s.fontWeight === fontWeight);\n        if (exactFit)\n            return exactFit;\n        // Weight resolution. https://developer.mozilla.org/en-US/docs/Web/CSS/font-weight#Fallback_weights\n        let font = null;\n        const numericFontWeight = resolveFontWeight(fontWeight);\n        if (numericFontWeight >= 400 && numericFontWeight <= 500) {\n            const leftOffset = styleSources.filter((s) => s.fontWeight <= numericFontWeight);\n            const rightOffset = styleSources.filter((s) => s.fontWeight > 500);\n            const fit = styleSources.filter((s) => s.fontWeight >= numericFontWeight && s.fontWeight < 500);\n            font = fit[0] || leftOffset[leftOffset.length - 1] || rightOffset[0];\n        }\n        const lt = styleSources\n            .filter((s) => s.fontWeight < numericFontWeight)\n            .sort(sortByFontWeight);\n        const gt = styleSources\n            .filter((s) => s.fontWeight > numericFontWeight)\n            .sort(sortByFontWeight);\n        if (numericFontWeight < 400) {\n            font = lt[lt.length - 1] || gt[0];\n        }\n        if (numericFontWeight > 500) {\n            font = gt[0] || lt[lt.length - 1];\n        }\n        if (!font) {\n            throw new Error(`Could not resolve font for ${this.family}, fontWeight ${fontWeight}, fontStyle ${fontStyle}`);\n        }\n        return font;\n    }\n}\n\nclass FontStore {\n    fontFamilies = {};\n    emojiSource = null;\n    constructor() {\n        this.register({\n            family: 'Helvetica',\n            fonts: [\n                { src: 'Helvetica', fontStyle: 'normal', fontWeight: 400 },\n                { src: 'Helvetica-Bold', fontStyle: 'normal', fontWeight: 700 },\n                { src: 'Helvetica-Oblique', fontStyle: 'italic', fontWeight: 400 },\n                { src: 'Helvetica-BoldOblique', fontStyle: 'italic', fontWeight: 700 },\n            ],\n        });\n        this.register({\n            family: 'Courier',\n            fonts: [\n                { src: 'Courier', fontStyle: 'normal', fontWeight: 400 },\n                { src: 'Courier-Bold', fontStyle: 'normal', fontWeight: 700 },\n                { src: 'Courier-Oblique', fontStyle: 'italic', fontWeight: 400 },\n                { src: 'Courier-BoldOblique', fontStyle: 'italic', fontWeight: 700 },\n            ],\n        });\n        this.register({\n            family: 'Times-Roman',\n            fonts: [\n                { src: 'Times-Roman', fontStyle: 'normal', fontWeight: 400 },\n                { src: 'Times-Bold', fontStyle: 'normal', fontWeight: 700 },\n                { src: 'Times-Italic', fontStyle: 'italic', fontWeight: 400 },\n                { src: 'Times-BoldItalic', fontStyle: 'italic', fontWeight: 700 },\n            ],\n        });\n        // For backwards compatibility\n        this.register({\n            family: 'Helvetica-Bold',\n            src: 'Helvetica-Bold',\n        });\n        this.register({\n            family: 'Helvetica-Oblique',\n            src: 'Helvetica-Oblique',\n        });\n        this.register({\n            family: 'Helvetica-BoldOblique',\n            src: 'Helvetica-BoldOblique',\n        });\n        this.register({\n            family: 'Courier-Bold',\n            src: 'Courier-Bold',\n        });\n        this.register({\n            family: 'Courier-Oblique',\n            src: 'Courier-Oblique',\n        });\n        this.register({\n            family: 'Courier-BoldOblique',\n            src: 'Courier-BoldOblique',\n        });\n        this.register({\n            family: 'Times-Bold',\n            src: 'Times-Bold',\n        });\n        this.register({\n            family: 'Times-Italic',\n            src: 'Times-Italic',\n        });\n        this.register({\n            family: 'Times-BoldItalic',\n            src: 'Times-BoldItalic',\n        });\n        // Load default fonts\n        this.load({\n            fontFamily: 'Helvetica',\n            fontStyle: 'normal',\n            fontWeight: 400,\n        });\n        this.load({\n            fontFamily: 'Helvetica',\n            fontStyle: 'normal',\n            fontWeight: 700,\n        });\n        this.load({\n            fontFamily: 'Helvetica',\n            fontStyle: 'italic',\n            fontWeight: 400,\n        });\n        this.load({\n            fontFamily: 'Helvetica',\n            fontStyle: 'italic',\n            fontWeight: 700,\n        });\n    }\n    hyphenationCallback = null;\n    register = (data) => {\n        const { family } = data;\n        if (!this.fontFamilies[family]) {\n            this.fontFamilies[family] = FontFamily.create(family);\n        }\n        // Bulk loading\n        if ('fonts' in data) {\n            for (let i = 0; i < data.fonts.length; i += 1) {\n                const { src, fontStyle, fontWeight, ...options } = data.fonts[i];\n                this.fontFamilies[family].register({\n                    src,\n                    fontStyle,\n                    fontWeight,\n                    ...options,\n                });\n            }\n        }\n        else {\n            const { src, fontStyle, fontWeight, ...options } = data;\n            this.fontFamilies[family].register({\n                src,\n                fontStyle,\n                fontWeight,\n                ...options,\n            });\n        }\n    };\n    registerEmojiSource = (emojiSource) => {\n        this.emojiSource = emojiSource;\n    };\n    registerHyphenationCallback = (callback) => {\n        this.hyphenationCallback = callback;\n    };\n    getFont = (descriptor) => {\n        const { fontFamily } = descriptor;\n        if (!this.fontFamilies[fontFamily]) {\n            throw new Error(`Font family not registered: ${fontFamily}. Please register it calling Font.register() method.`);\n        }\n        return this.fontFamilies[fontFamily].resolve(descriptor);\n    };\n    load = async (descriptor) => {\n        const font = this.getFont(descriptor);\n        if (font)\n            await font.load();\n    };\n    reset = () => {\n        const keys = Object.keys(this.fontFamilies);\n        for (let i = 0; i < keys.length; i += 1) {\n            const key = keys[i];\n            for (let j = 0; j < this.fontFamilies[key].sources.length; j++) {\n                const fontSource = this.fontFamilies[key].sources[j];\n                fontSource.data = null;\n            }\n        }\n    };\n    clear = () => {\n        this.fontFamilies = {};\n    };\n    getRegisteredFonts = () => this.fontFamilies;\n    getEmojiSource = () => this.emojiSource;\n    getHyphenationCallback = () => this.hyphenationCallback;\n    getRegisteredFontFamilies = () => Object.keys(this.fontFamilies);\n}\n\nexport { FontStore as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,kCAAkC;AAClC,MAAM,iBAAiB;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,MAAM;IACF,KAAK;IACL,IAAI;IACJ,SAAS;IACT,WAAW;IACX,cAAc;IACd,eAAe;IACf,UAAU;IACV,QAAQ;IACR,kBAAkB;IAClB,mBAAmB;IACnB,YAAY;IACZ,KAAK;IACL,OAAO;IACP,KAAK;IACL,UAAU;IACV,aAAa;IACb,kBAAkB;IAClB,KAAK;IACL,YAAY,GAAG,CAAE;QACb,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,iBAAiB,GAAG,EAAE;QAC3B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,iBAAiB,GAAG,CAAC;QAC1B,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,IAAI,GAAG,CAAC;QACb,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,IAAI,CAAC,IAAI,GAAG,CAAC;QACb,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,YAAY,GAAG,EAAE;QACtB,IAAI,CAAC,GAAG,GAAG,uKAAA,CAAA,UAAO,CAAC,IAAI,CAAC,MAAM;IAClC;IACA,OAAO,GAAG,EAAE;QACR,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;IAC3B;IACA,OAAO,GAAG,EAAE;QACR,MAAM,CAAC,SAAS,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;QACzC,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAC,GAAG;YAC3B,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG;YACxC,MAAM,YAAY,GAAG,SAAS,CAAC,EAAE,CAAC,YAAY;YAC9C,OAAO;QACX;QACA,MAAM,eAAe,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE;QACxE,OAAO;YACH;YACA,eAAe,UAAU,GAAG,CAAC,CAAC,GAAG,IAAM;YACvC;YACA,QAAQ;YACR,UAAU;YACV,WAAW;YACX,UAAU,CAAC;YACX;YACA,eAAe;YACf,MAAM;QACV;IACJ;IACA,kBAAkB,SAAS,EAAE;QACzB,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC;QAC5B,MAAM,YAAY,GAAG;QACrB,OAAO;IACX;IACA,SAAS,EAAE,EAAE;QACT,OAAO;YACH;YACA,YAAY;gBAAC;aAAG;YAChB,YAAY;YACZ,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC;YACrC,OAAO,IAAI,CAAC,GAAG;YACf,uCAAuC;YACvC,cAAc;QAClB;IACJ;IACA,qBAAqB,SAAS,EAAE;QAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe;IACzD;IACA,iCAAiC;IACjC,IAAI,SAAS;QACT,OAAO;IACX;IACA,iCAAiC;IACjC,IAAI,YAAY;QACZ,OAAQ,IAAI,CAAC,IAAI;YACb,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO;YACX,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IACA,iCAAiC;IACjC,IAAI,UAAU;QACV,OAAQ,IAAI,CAAC,IAAI;YACb,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO;YACX,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IACA,iCAAiC;IACjC,IAAI,UAAU;QACV,OAAQ,IAAI,CAAC,IAAI;YACb,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO,CAAC;YACZ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO,CAAC;YACZ;gBACI,OAAO,CAAC;QAChB;IACJ;IACA,IAAI,UAAU;QACV,OAAO;IACX;IACA,IAAI,aAAa;QACb,OAAO;IACX;IACA,kBAAkB;QACd,MAAM,IAAI,MAAM;IACpB;IACA,kBAAkB;QACd,MAAM,IAAI,MAAM;IACpB;IACA,eAAe;QACX,MAAM,IAAI,MAAM;IACpB;IACA,uBAAuB;QACnB,MAAM,IAAI,MAAM;IACpB;IACA,eAAe;QACX,MAAM,IAAI,MAAM;IACpB;IACA,eAAe;QACX,MAAM,IAAI,MAAM;IACpB;IACA,UAAU;QACN,MAAM,IAAI,MAAM;IACpB;IACA,UAAU;QACN,MAAM,IAAI,MAAM;IACpB;IACA,qBAAqB;QACjB,MAAM,IAAI,MAAM;IACpB;AACJ;AAEA,MAAM,YAAY,OAAO,KAAK;IAC1B,MAAM,WAAW,MAAM,MAAM,KAAK;IAClC,MAAM,OAAO,MAAM,SAAS,WAAW;IACvC,OAAO,IAAI,WAAW;AAC1B;AACA,MAAM,YAAY,CAAC;IACf,MAAM,SAAS,QAAQ,KAAK,CAAC,IAAI,CAAC,EAAE;IACpC,MAAM,gBAAgB,OAAO,SAAS,CAAC,GAAG,OAAO;IACjD,MAAM,kBAAkB,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK;IACjD,OAAO,iBAAiB;AAC5B;AACA,MAAM;IACF,IAAI;IACJ,WAAW;IACX,UAAU;IACV,WAAW;IACX,KAAK;IACL,QAAQ;IACR,kBAAkB;IAClB,YAAY,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,CAAE;QACzD,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG,aAAa;QAC9B,IAAI,CAAC,UAAU,GAAG,cAAc;QAChC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;QAC3B,IAAI,CAAC,iBAAiB,GAAG;IAC7B;IACA,MAAM,QAAQ;QACV,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,OAAO;QACvC,IAAI,OAAO;QACX,IAAI,eAAe,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG;YACnC,OAAO,IAAI,aAAa,IAAI,CAAC,GAAG;QACpC,OACK,IAAI,UAAU,IAAI,CAAC,GAAG,GAAG;YAC1B,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAClC,MAAM,aAAa,IAAI,WAAW,KAAK,KAClC,KAAK,CAAC,IACN,GAAG,CAAC,CAAC,IAAM,EAAE,UAAU,CAAC;YAC7B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAc,AAAD,EAAE,YAAY;QACtC,OACK;YACD,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO;YACtD,MAAM,SAAS,MAAM,UAAU,IAAI,CAAC,GAAG,EAAE;gBAAE;gBAAQ;gBAAM;YAAQ;YACjE,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAc,AAAD,EAAE,QAAQ;QAClC;QACA,IAAI,QAAQ,WAAW,MAAM;YACzB,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,MAAM,OAAO;QACT,IAAI,IAAI,CAAC,iBAAiB,KAAK,MAAM;YACjC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK;QACvC;QACA,OAAO,IAAI,CAAC,iBAAiB;IACjC;AACJ;AAEA,MAAM,eAAe;IACjB,MAAM;IACN,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,UAAU;IACV,MAAM;IACN,WAAW;IACX,WAAW;IACX,OAAO;IACP,OAAO;AACX;AACA,MAAM,oBAAoB,CAAC;IACvB,OAAO,OAAO,UAAU,WAAW,YAAY,CAAC,MAAM,GAAG;AAC7D;AACA,MAAM,mBAAmB,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU;AAC9D,MAAM;IACF,OAAO;IACP,QAAQ;IACR,OAAO,OAAO,MAAM,EAAE;QAClB,OAAO,IAAI,WAAW;IAC1B;IACA,YAAY,MAAM,CAAE;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG,EAAE;IACrB;IACA,SAAS,EAAE,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,SAAS,EAAE;QACjD,MAAM,oBAAoB,aACpB,kBAAkB,cAClB;QACN,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,WAAW,KAAK,IAAI,CAAC,MAAM,EAAE,WAAW,mBAAmB;IACrF;IACA,QAAQ,UAAU,EAAE;QAChB,MAAM,EAAE,aAAa,GAAG,EAAE,YAAY,QAAQ,EAAE,GAAG;QACnD,MAAM,eAAe,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,SAAS,KAAK;QAChE,MAAM,WAAW,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,UAAU,KAAK;QAC3D,IAAI,UACA,OAAO;QACX,mGAAmG;QACnG,IAAI,OAAO;QACX,MAAM,oBAAoB,kBAAkB;QAC5C,IAAI,qBAAqB,OAAO,qBAAqB,KAAK;YACtD,MAAM,aAAa,aAAa,MAAM,CAAC,CAAC,IAAM,EAAE,UAAU,IAAI;YAC9D,MAAM,cAAc,aAAa,MAAM,CAAC,CAAC,IAAM,EAAE,UAAU,GAAG;YAC9D,MAAM,MAAM,aAAa,MAAM,CAAC,CAAC,IAAM,EAAE,UAAU,IAAI,qBAAqB,EAAE,UAAU,GAAG;YAC3F,OAAO,GAAG,CAAC,EAAE,IAAI,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,IAAI,WAAW,CAAC,EAAE;QACxE;QACA,MAAM,KAAK,aACN,MAAM,CAAC,CAAC,IAAM,EAAE,UAAU,GAAG,mBAC7B,IAAI,CAAC;QACV,MAAM,KAAK,aACN,MAAM,CAAC,CAAC,IAAM,EAAE,UAAU,GAAG,mBAC7B,IAAI,CAAC;QACV,IAAI,oBAAoB,KAAK;YACzB,OAAO,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE;QACrC;QACA,IAAI,oBAAoB,KAAK;YACzB,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,MAAM,GAAG,EAAE;QACrC;QACA,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,WAAW,YAAY,EAAE,WAAW;QACjH;QACA,OAAO;IACX;AACJ;AAEA,MAAM;IACF,eAAe,CAAC,EAAE;IAClB,cAAc,KAAK;IACnB,aAAc;QACV,IAAI,CAAC,QAAQ,CAAC;YACV,QAAQ;YACR,OAAO;gBACH;oBAAE,KAAK;oBAAa,WAAW;oBAAU,YAAY;gBAAI;gBACzD;oBAAE,KAAK;oBAAkB,WAAW;oBAAU,YAAY;gBAAI;gBAC9D;oBAAE,KAAK;oBAAqB,WAAW;oBAAU,YAAY;gBAAI;gBACjE;oBAAE,KAAK;oBAAyB,WAAW;oBAAU,YAAY;gBAAI;aACxE;QACL;QACA,IAAI,CAAC,QAAQ,CAAC;YACV,QAAQ;YACR,OAAO;gBACH;oBAAE,KAAK;oBAAW,WAAW;oBAAU,YAAY;gBAAI;gBACvD;oBAAE,KAAK;oBAAgB,WAAW;oBAAU,YAAY;gBAAI;gBAC5D;oBAAE,KAAK;oBAAmB,WAAW;oBAAU,YAAY;gBAAI;gBAC/D;oBAAE,KAAK;oBAAuB,WAAW;oBAAU,YAAY;gBAAI;aACtE;QACL;QACA,IAAI,CAAC,QAAQ,CAAC;YACV,QAAQ;YACR,OAAO;gBACH;oBAAE,KAAK;oBAAe,WAAW;oBAAU,YAAY;gBAAI;gBAC3D;oBAAE,KAAK;oBAAc,WAAW;oBAAU,YAAY;gBAAI;gBAC1D;oBAAE,KAAK;oBAAgB,WAAW;oBAAU,YAAY;gBAAI;gBAC5D;oBAAE,KAAK;oBAAoB,WAAW;oBAAU,YAAY;gBAAI;aACnE;QACL;QACA,8BAA8B;QAC9B,IAAI,CAAC,QAAQ,CAAC;YACV,QAAQ;YACR,KAAK;QACT;QACA,IAAI,CAAC,QAAQ,CAAC;YACV,QAAQ;YACR,KAAK;QACT;QACA,IAAI,CAAC,QAAQ,CAAC;YACV,QAAQ;YACR,KAAK;QACT;QACA,IAAI,CAAC,QAAQ,CAAC;YACV,QAAQ;YACR,KAAK;QACT;QACA,IAAI,CAAC,QAAQ,CAAC;YACV,QAAQ;YACR,KAAK;QACT;QACA,IAAI,CAAC,QAAQ,CAAC;YACV,QAAQ;YACR,KAAK;QACT;QACA,IAAI,CAAC,QAAQ,CAAC;YACV,QAAQ;YACR,KAAK;QACT;QACA,IAAI,CAAC,QAAQ,CAAC;YACV,QAAQ;YACR,KAAK;QACT;QACA,IAAI,CAAC,QAAQ,CAAC;YACV,QAAQ;YACR,KAAK;QACT;QACA,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC;YACN,YAAY;YACZ,WAAW;YACX,YAAY;QAChB;QACA,IAAI,CAAC,IAAI,CAAC;YACN,YAAY;YACZ,WAAW;YACX,YAAY;QAChB;QACA,IAAI,CAAC,IAAI,CAAC;YACN,YAAY;YACZ,WAAW;YACX,YAAY;QAChB;QACA,IAAI,CAAC,IAAI,CAAC;YACN,YAAY;YACZ,WAAW;YACX,YAAY;QAChB;IACJ;IACA,sBAAsB,KAAK;IAC3B,WAAW,CAAC;QACR,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YAC5B,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,WAAW,MAAM,CAAC;QAClD;QACA,eAAe;QACf,IAAI,WAAW,MAAM;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE,KAAK,EAAG;gBAC3C,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,SAAS,GAAG,KAAK,KAAK,CAAC,EAAE;gBAChE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC;oBAC/B;oBACA;oBACA;oBACA,GAAG,OAAO;gBACd;YACJ;QACJ,OACK;YACD,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,SAAS,GAAG;YACnD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAC/B;gBACA;gBACA;gBACA,GAAG,OAAO;YACd;QACJ;IACJ,EAAE;IACF,sBAAsB,CAAC;QACnB,IAAI,CAAC,WAAW,GAAG;IACvB,EAAE;IACF,8BAA8B,CAAC;QAC3B,IAAI,CAAC,mBAAmB,GAAG;IAC/B,EAAE;IACF,UAAU,CAAC;QACP,MAAM,EAAE,UAAU,EAAE,GAAG;QACvB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;YAChC,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,WAAW,oDAAoD,CAAC;QACnH;QACA,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC;IACjD,EAAE;IACF,OAAO,OAAO;QACV,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC;QAC1B,IAAI,MACA,MAAM,KAAK,IAAI;IACvB,EAAE;IACF,QAAQ;QACJ,MAAM,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;QAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACrC,MAAM,MAAM,IAAI,CAAC,EAAE;YACnB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAK;gBAC5D,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBACpD,WAAW,IAAI,GAAG;YACtB;QACJ;IACJ,EAAE;IACF,QAAQ;QACJ,IAAI,CAAC,YAAY,GAAG,CAAC;IACzB,EAAE;IACF,qBAAqB,IAAM,IAAI,CAAC,YAAY,CAAC;IAC7C,iBAAiB,IAAM,IAAI,CAAC,WAAW,CAAC;IACxC,yBAAyB,IAAM,IAAI,CAAC,mBAAmB,CAAC;IACxD,4BAA4B,IAAM,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/fns/lib/index.js"], "sourcesContent": ["/**\n * Applies a function to the value at the given index of an array\n *\n * @param index\n * @param fn\n * @param collection\n * @returns Copy of the array with the element at the given index replaced with the result of the function application.\n */\nconst adjust = (index, fn, collection) => {\n    if (index >= 0 && index >= collection.length)\n        return collection;\n    if (index < 0 && Math.abs(index) > collection.length)\n        return collection;\n    const i = index < 0 ? collection.length + index : index;\n    return Object.assign([], collection, { [i]: fn(collection[i]) });\n};\n\n/* eslint-disable no-await-in-loop */\n/**\n * Performs right-to-left function composition with async functions support\n *\n * @param fns - Functions\n * @returns Composed function\n */\nconst asyncCompose = (...fns) => async (value, ...args) => {\n    let result = value;\n    const reversedFns = fns.slice().reverse();\n    for (let i = 0; i < reversedFns.length; i += 1) {\n        const fn = reversedFns[i];\n        result = await fn(result, ...args);\n    }\n    return result;\n};\n\n/**\n * Capitalize first letter of each word\n *\n * @param value - Any string\n * @returns Capitalized string\n */\nconst capitalize = (value) => {\n    if (!value)\n        return value;\n    return value.replace(/(^|\\s)\\S/g, (l) => l.toUpperCase());\n};\n\n/**\n * Casts value to array\n *\n * @template T - The type of the value.\n * @param value - The value to cast into an array.\n * @returns An array containing the given value.\n */\nconst castArray = (value) => {\n    return Array.isArray(value) ? value : [value];\n};\n\n/**\n * Performs right-to-left function composition\n *\n * @param fns - Functions\n * @returns Composed function\n */\nconst compose = (...fns) => (value, ...args) => {\n    let result = value;\n    const reversedFns = fns.slice().reverse();\n    for (let i = 0; i < reversedFns.length; i += 1) {\n        const fn = reversedFns[i];\n        result = fn(result, ...args);\n    }\n    return result;\n};\n\n/**\n * Drops the last element from an array.\n *\n * @template T\n * @param  array - The array to drop the last element from\n * @returns - The new array with the last element dropped\n */\nconst dropLast = (array) => array.slice(0, array.length - 1);\n\n/**\n * Applies a set of transformations to an object and returns a new object with the transformed values.\n *\n * @template T\n * @param transformations - The transformations to apply.\n * @param object - The object to transform.\n * @returns The transformed object.\n */\nfunction evolve(transformations, object) {\n    const result = {};\n    const keys = Object.keys(object);\n    for (let i = 0; i < keys.length; i += 1) {\n        const key = keys[i];\n        const transformation = transformations[key];\n        if (typeof transformation === 'function') {\n            result[key] = transformation(object[key]);\n        }\n        else {\n            result[key] = object[key];\n        }\n    }\n    return result;\n}\n\n/**\n * Checks if a value is null or undefined.\n *\n * @template T - The type of the value.\n * @param value - The value to check\n * @returns True if the value is null or undefined, false otherwise\n */\nconst isNil = (value) => value === null || value === undefined;\n\n/**\n * Retrieves the value at a given path from an object.\n *\n * @param target - The object to retrieve the value from.\n * @param path - The path of the value to retrieve.\n * @param defaultValue - The default value to return if the path does not exist.\n * @returns The value at the given path, or the default value if the path does not exist.\n */\nconst get = (target, path, defaultValue) => {\n    if (isNil(target))\n        return defaultValue;\n    const _path = castArray(path);\n    let result = target;\n    for (let i = 0; i < _path.length; i += 1) {\n        if (isNil(result))\n            return undefined;\n        result = result[_path[i]];\n    }\n    return isNil(result) ? defaultValue : result;\n};\n\nfunction last(value) {\n    return value === '' ? '' : value[value.length - 1];\n}\n\n/**\n * Maps over the values of an object and applies a function to each value.\n *\n * @param object - The object to map over\n * @param fn - The function to apply to each value\n * @returns A new object with the mapped values\n */\nconst mapValues = (object, fn) => {\n    const entries = Object.entries(object);\n    const acc = {};\n    return entries.reduce((acc, [key, value], index) => {\n        acc[key] = fn(value, key, index);\n        return acc;\n    }, acc);\n};\n\nconst isPercent = (value) => /((-)?\\d+\\.?\\d*)%/g.exec(`${value}`);\n/**\n * Get percentage value of input\n *\n * @param value\n * @returns Percent value (if matches)\n */\nconst matchPercent = (value) => {\n    const match = isPercent(value);\n    if (match) {\n        const f = parseFloat(match[1]);\n        const percent = f / 100;\n        return { percent, value: f };\n    }\n    return null;\n};\n\n/**\n * Creates a new object by omitting specified keys from the original object.\n *\n * @param keys - The key or keys to omit\n * @param object - The original object\n * @returns The new object without the omitted keys\n */\nconst omit = (keys, object) => {\n    const _keys = castArray(keys);\n    const copy = Object.assign({}, object);\n    _keys.forEach((key) => {\n        delete copy[key];\n    });\n    return copy;\n};\n\n/**\n * Picks the specified keys from an object and returns a new object with only those keys.\n *\n * @param keys - The keys to pick from the object\n * @param object - The object to pick the keys from\n * @returns A new object with only the picked keys\n */\nconst pick = (keys, obj) => {\n    const result = {};\n    for (let i = 0; i < keys.length; i += 1) {\n        const key = keys[i];\n        if (key in obj)\n            result[key] = obj[key];\n    }\n    return result;\n};\n\n/**\n * Repeats an element a specified number of times.\n *\n * @template T\n * @param element - Element to be repeated\n * @param length - Number of times to repeat element\n * @returns Repeated elements\n */\nconst repeat = (element, length = 0) => {\n    const result = new Array(length);\n    for (let i = 0; i < length; i += 1) {\n        result[i] = element;\n    }\n    return result;\n};\n\n/**\n * Reverses the list\n *\n * @template T\n * @param list - List to be reversed\n * @returns Reversed list\n */\nconst reverse = (list) => Array.prototype.slice.call(list, 0).reverse();\n\n/**\n * Capitalize first letter of string\n *\n * @param value - String\n * @returns Capitalized string\n */\nconst upperFirst = (value) => {\n    if (!value)\n        return value;\n    return value.charAt(0).toUpperCase() + value.slice(1);\n};\n\n/**\n * Returns a new array with all the values from the original array that are not present in the keys array.\n *\n * @param keys - The keys to pick from the object\n * @param array - Array to filter the values from\n * @returns A new array with without the omitted values\n */\nconst without = (keys, array) => {\n    const result = [];\n    for (let i = 0; i < array.length; i += 1) {\n        const value = array[i];\n        if (!keys.includes(value))\n            result.push(value);\n    }\n    return result;\n};\n\n/**\n * Parse a string or number to a float\n *\n * @param value - String or number\n * @returns Parsed float\n */\nconst parseFloat$1 = (value) => {\n    return typeof value === 'string' ? Number.parseFloat(value) : value;\n};\n\nexport { adjust, asyncCompose, capitalize, castArray, compose, dropLast, evolve, get, isNil, last, mapValues, matchPercent, omit, parseFloat$1 as parseFloat, pick, repeat, reverse, upperFirst, without };\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;;;;;;;;;;;AACD,MAAM,SAAS,CAAC,OAAO,IAAI;IACvB,IAAI,SAAS,KAAK,SAAS,WAAW,MAAM,EACxC,OAAO;IACX,IAAI,QAAQ,KAAK,KAAK,GAAG,CAAC,SAAS,WAAW,MAAM,EAChD,OAAO;IACX,MAAM,IAAI,QAAQ,IAAI,WAAW,MAAM,GAAG,QAAQ;IAClD,OAAO,OAAO,MAAM,CAAC,EAAE,EAAE,YAAY;QAAE,CAAC,EAAE,EAAE,GAAG,UAAU,CAAC,EAAE;IAAE;AAClE;AAEA,mCAAmC,GACnC;;;;;CAKC,GACD,MAAM,eAAe,CAAC,GAAG,MAAQ,OAAO,OAAO,GAAG;QAC9C,IAAI,SAAS;QACb,MAAM,cAAc,IAAI,KAAK,GAAG,OAAO;QACvC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,KAAK,EAAG;YAC5C,MAAM,KAAK,WAAW,CAAC,EAAE;YACzB,SAAS,MAAM,GAAG,WAAW;QACjC;QACA,OAAO;IACX;AAEA;;;;;CAKC,GACD,MAAM,aAAa,CAAC;IAChB,IAAI,CAAC,OACD,OAAO;IACX,OAAO,MAAM,OAAO,CAAC,aAAa,CAAC,IAAM,EAAE,WAAW;AAC1D;AAEA;;;;;;CAMC,GACD,MAAM,YAAY,CAAC;IACf,OAAO,MAAM,OAAO,CAAC,SAAS,QAAQ;QAAC;KAAM;AACjD;AAEA;;;;;CAKC,GACD,MAAM,UAAU,CAAC,GAAG,MAAQ,CAAC,OAAO,GAAG;QACnC,IAAI,SAAS;QACb,MAAM,cAAc,IAAI,KAAK,GAAG,OAAO;QACvC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,KAAK,EAAG;YAC5C,MAAM,KAAK,WAAW,CAAC,EAAE;YACzB,SAAS,GAAG,WAAW;QAC3B;QACA,OAAO;IACX;AAEA;;;;;;CAMC,GACD,MAAM,WAAW,CAAC,QAAU,MAAM,KAAK,CAAC,GAAG,MAAM,MAAM,GAAG;AAE1D;;;;;;;CAOC,GACD,SAAS,OAAO,eAAe,EAAE,MAAM;IACnC,MAAM,SAAS,CAAC;IAChB,MAAM,OAAO,OAAO,IAAI,CAAC;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;QACrC,MAAM,MAAM,IAAI,CAAC,EAAE;QACnB,MAAM,iBAAiB,eAAe,CAAC,IAAI;QAC3C,IAAI,OAAO,mBAAmB,YAAY;YACtC,MAAM,CAAC,IAAI,GAAG,eAAe,MAAM,CAAC,IAAI;QAC5C,OACK;YACD,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAC7B;IACJ;IACA,OAAO;AACX;AAEA;;;;;;CAMC,GACD,MAAM,QAAQ,CAAC,QAAU,UAAU,QAAQ,UAAU;AAErD;;;;;;;CAOC,GACD,MAAM,MAAM,CAAC,QAAQ,MAAM;IACvB,IAAI,MAAM,SACN,OAAO;IACX,MAAM,QAAQ,UAAU;IACxB,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;QACtC,IAAI,MAAM,SACN,OAAO;QACX,SAAS,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;IAC7B;IACA,OAAO,MAAM,UAAU,eAAe;AAC1C;AAEA,SAAS,KAAK,KAAK;IACf,OAAO,UAAU,KAAK,KAAK,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;AACtD;AAEA;;;;;;CAMC,GACD,MAAM,YAAY,CAAC,QAAQ;IACvB,MAAM,UAAU,OAAO,OAAO,CAAC;IAC/B,MAAM,MAAM,CAAC;IACb,OAAO,QAAQ,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM,EAAE;QACtC,GAAG,CAAC,IAAI,GAAG,GAAG,OAAO,KAAK;QAC1B,OAAO;IACX,GAAG;AACP;AAEA,MAAM,YAAY,CAAC,QAAU,oBAAoB,IAAI,CAAC,GAAG,OAAO;AAChE;;;;;CAKC,GACD,MAAM,eAAe,CAAC;IAClB,MAAM,QAAQ,UAAU;IACxB,IAAI,OAAO;QACP,MAAM,IAAI,WAAW,KAAK,CAAC,EAAE;QAC7B,MAAM,UAAU,IAAI;QACpB,OAAO;YAAE;YAAS,OAAO;QAAE;IAC/B;IACA,OAAO;AACX;AAEA;;;;;;CAMC,GACD,MAAM,OAAO,CAAC,MAAM;IAChB,MAAM,QAAQ,UAAU;IACxB,MAAM,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG;IAC/B,MAAM,OAAO,CAAC,CAAC;QACX,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,OAAO;AACX;AAEA;;;;;;CAMC,GACD,MAAM,OAAO,CAAC,MAAM;IAChB,MAAM,SAAS,CAAC;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;QACrC,MAAM,MAAM,IAAI,CAAC,EAAE;QACnB,IAAI,OAAO,KACP,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;IAC9B;IACA,OAAO;AACX;AAEA;;;;;;;CAOC,GACD,MAAM,SAAS,CAAC,SAAS,SAAS,CAAC;IAC/B,MAAM,SAAS,IAAI,MAAM;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,KAAK,EAAG;QAChC,MAAM,CAAC,EAAE,GAAG;IAChB;IACA,OAAO;AACX;AAEA;;;;;;CAMC,GACD,MAAM,UAAU,CAAC,OAAS,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO;AAErE;;;;;CAKC,GACD,MAAM,aAAa,CAAC;IAChB,IAAI,CAAC,OACD,OAAO;IACX,OAAO,MAAM,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,KAAK,CAAC;AACvD;AAEA;;;;;;CAMC,GACD,MAAM,UAAU,CAAC,MAAM;IACnB,MAAM,SAAS,EAAE;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;QACtC,MAAM,QAAQ,KAAK,CAAC,EAAE;QACtB,IAAI,CAAC,KAAK,QAAQ,CAAC,QACf,OAAO,IAAI,CAAC;IACpB;IACA,OAAO;AACX;AAEA;;;;;CAKC,GACD,MAAM,eAAe,CAAC;IAClB,OAAO,OAAO,UAAU,WAAW,OAAO,UAAU,CAAC,SAAS;AAClE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/render/lib/index.js"], "sourcesContent": ["import * as P from '@react-pdf/primitives';\nimport { isNil, matchPercent } from '@react-pdf/fns';\nimport absPath from 'abs-svg-path';\nimport parsePath from 'parse-svg-path';\nimport normalizePath from 'normalize-svg-path';\nimport colorString from 'color-string';\n\nconst renderPath = (ctx, node) => {\n    const d = node.props?.d;\n    if (d)\n        ctx.path(node.props.d);\n};\n\nconst KAPPA$3 = 4.0 * ((Math.sqrt(2) - 1.0) / 3.0);\nconst renderRect = (ctx, node) => {\n    const x = node.props?.x || 0;\n    const y = node.props?.y || 0;\n    const rx = node.props?.rx || 0;\n    const ry = node.props?.ry || 0;\n    const width = node.props?.width || 0;\n    const height = node.props?.height || 0;\n    if (!width || !height)\n        return;\n    if (rx && ry) {\n        const krx = rx * KAPPA$3;\n        const kry = ry * KAPPA$3;\n        ctx.moveTo(x + rx, y);\n        ctx.lineTo(x - rx + width, y);\n        ctx.bezierCurveTo(x - rx + width + krx, y, x + width, y + ry - kry, x + width, y + ry);\n        ctx.lineTo(x + width, y + height - ry);\n        ctx.bezierCurveTo(x + width, y + height - ry + kry, x - rx + width + krx, y + height, x - rx + width, y + height);\n        ctx.lineTo(x + rx, y + height);\n        ctx.bezierCurveTo(x + rx - krx, y + height, x, y + height - ry + kry, x, y + height - ry);\n        ctx.lineTo(x, y + ry);\n        ctx.bezierCurveTo(x, y + ry - kry, x + rx - krx, y, x + rx, y);\n    }\n    else {\n        ctx.moveTo(x, y);\n        ctx.lineTo(x + width, y);\n        ctx.lineTo(x + width, y + height);\n        ctx.lineTo(x, y + height);\n    }\n    ctx.closePath();\n};\n\nconst renderLine$1 = (ctx, node) => {\n    const { x1, x2, y1, y2 } = node.props || {};\n    ctx.moveTo(x1, y1);\n    ctx.lineTo(x2, y2);\n};\n\nconst renderGroup = () => {\n    // noop\n};\n\nconst KAPPA$2 = 4.0 * ((Math.sqrt(2) - 1.0) / 3.0);\nconst drawEllipse = (ctx, rx, ry, cx = 0, cy = 0) => {\n    const x = cx - rx;\n    const y = cy - ry;\n    const ox = rx * KAPPA$2;\n    const oy = ry * KAPPA$2;\n    const xe = x + rx * 2;\n    const ye = y + ry * 2;\n    const xm = x + rx;\n    const ym = y + ry;\n    ctx.moveTo(x, ym);\n    ctx.bezierCurveTo(x, ym - oy, xm - ox, y, xm, y);\n    ctx.bezierCurveTo(xm + ox, y, xe, ym - oy, xe, ym);\n    ctx.bezierCurveTo(xe, ym + oy, xm + ox, ye, xm, ye);\n    ctx.bezierCurveTo(xm - ox, ye, x, ym + oy, x, ym);\n    ctx.closePath();\n};\nconst renderEllipse = (ctx, node) => {\n    const { cx, cy, rx, ry } = node.props || {};\n    drawEllipse(ctx, rx, ry, cx, cy);\n};\n\nconst renderCircle = (ctx, node) => {\n    const cx = node.props?.cx;\n    const cy = node.props?.cy;\n    const r = node.props?.r;\n    drawEllipse(ctx, r, r, cx, cy);\n};\n\n/* eslint-disable no-return-assign */\nconst number = (n) => {\n    if (n > -1e21 && n < 1e21) {\n        return Math.round(n * 1e6) / 1e6;\n    }\n    throw new Error(`unsupported number: ${n}`);\n};\nconst _renderGlyphs = (ctx, encoded, positions, x, y) => {\n    const commands = [];\n    const scale = ctx._fontSize / 1000;\n    let i;\n    let last = 0;\n    let hadOffset = false;\n    ctx.save();\n    // flip coordinate system\n    ctx.transform(1, 0, 0, -1, 0, ctx.page.height);\n    y = ctx.page.height - y;\n    // add current font to page if necessary\n    if (ctx.page.fonts[ctx._font.id] == null) {\n        ctx.page.fonts[ctx._font.id] = ctx._font.ref();\n    }\n    // begin the text object\n    ctx.addContent('BT');\n    // text position\n    ctx.addContent(`1 0 0 1 ${number(x)} ${number(y)} Tm`);\n    // font and font size\n    ctx.addContent(`/${ctx._font.id} ${number(ctx._fontSize)} Tf`);\n    // Adds a segment of text to the TJ command buffer\n    const addSegment = (cur) => {\n        if (last < cur) {\n            const hex = encoded.slice(last, cur).join('');\n            const advance = positions[cur - 1].xAdvance - positions[cur - 1].advanceWidth;\n            commands.push(`<${hex}> ${number(-advance)}`);\n        }\n        return (last = cur);\n    };\n    // Flushes the current TJ commands to the output stream\n    const flush = (s) => {\n        addSegment(s);\n        if (commands.length > 0) {\n            ctx.addContent(`[${commands.join(' ')}] TJ`);\n            return (commands.length = 0);\n        }\n    };\n    for (i = 0; i < positions.length; i += 1) {\n        // If we have an x or y offset, we have to break out of the current TJ command\n        // so we can move the text position.\n        const pos = positions[i];\n        if (pos.xOffset || pos.yOffset) {\n            // Flush the current buffer\n            flush(i);\n            // Move the text position and flush just the current character\n            ctx.addContent(`1 0 0 1 ${number(x + pos.xOffset * scale)} ${number(y + pos.yOffset * scale)} Tm`);\n            flush(i + 1);\n            hadOffset = true;\n        }\n        else {\n            // If the last character had an offset, reset the text position\n            if (hadOffset) {\n                ctx.addContent(`1 0 0 1 ${number(x)} ${number(y)} Tm`);\n                hadOffset = false;\n            }\n            // Group segments that don't have any advance adjustments\n            if (pos.xAdvance - pos.advanceWidth !== 0) {\n                addSegment(i + 1);\n            }\n        }\n        x += pos.xAdvance * scale;\n    }\n    // Flush any remaining commands\n    flush(i);\n    // end the text object\n    ctx.addContent('ET');\n    // restore flipped coordinate system\n    return ctx.restore();\n};\nconst renderGlyphs = (ctx, glyphs, positions, x, y) => {\n    const scale = 1000 / ctx._fontSize;\n    const unitsPerEm = ctx._font.font.unitsPerEm || 1000;\n    const advanceWidthScale = 1000 / unitsPerEm;\n    // Glyph encoding and positioning\n    const encodedGlyphs = ctx._font.encodeGlyphs(glyphs);\n    const encodedPositions = positions.map((pos, i) => ({\n        xAdvance: pos.xAdvance * scale,\n        yAdvance: pos.yAdvance * scale,\n        xOffset: pos.xOffset,\n        yOffset: pos.yOffset,\n        advanceWidth: glyphs[i].advanceWidth * advanceWidthScale,\n    }));\n    return _renderGlyphs(ctx, encodedGlyphs, encodedPositions, x, y);\n};\n\nconst renderRun$1 = (ctx, run) => {\n    if (!run.glyphs)\n        return;\n    if (!run.positions)\n        return;\n    const runAdvanceWidth = run.xAdvance;\n    const font = run.attributes.font?.[0];\n    const { fontSize, color, opacity } = run.attributes;\n    if (color)\n        ctx.fillColor(color);\n    ctx.fillOpacity(opacity);\n    if (font) {\n        ctx.font(font.type === 'STANDARD' ? font.fullName : font, fontSize);\n    }\n    try {\n        renderGlyphs(ctx, run.glyphs, run.positions, 0, 0);\n    }\n    catch (error) {\n        console.log(error);\n    }\n    ctx.translate(runAdvanceWidth, 0);\n};\nconst renderSpan = (ctx, line, textAnchor, dominantBaseline) => {\n    ctx.save();\n    const x = line.box?.x || 0;\n    const y = line.box?.y || 0;\n    const font = line.runs[0]?.attributes.font?.[0];\n    const scale = line.runs[0]?.attributes?.scale || 1;\n    const width = line.xAdvance;\n    if (!font)\n        return;\n    const ascent = font.ascent * scale;\n    const xHeight = font.xHeight * scale;\n    const descent = font.descent * scale;\n    const capHeight = font.capHeight * scale;\n    let xTranslate = x;\n    let yTranslate = y;\n    switch (textAnchor) {\n        case 'middle':\n            xTranslate = x - width / 2;\n            break;\n        case 'end':\n            xTranslate = x - width;\n            break;\n        default:\n            xTranslate = x;\n            break;\n    }\n    switch (dominantBaseline) {\n        case 'middle':\n        case 'central':\n            yTranslate = y + capHeight / 2;\n            break;\n        case 'hanging':\n            yTranslate = y + capHeight;\n            break;\n        case 'mathematical':\n            yTranslate = y + xHeight;\n            break;\n        case 'text-after-edge':\n            yTranslate = y + descent;\n            break;\n        case 'text-before-edge':\n            yTranslate = y + ascent;\n            break;\n        default:\n            yTranslate = y;\n            break;\n    }\n    ctx.translate(xTranslate, yTranslate);\n    line.runs.forEach((run) => renderRun$1(ctx, run));\n    ctx.restore();\n};\nconst renderSvgText = (ctx, node) => {\n    const children = node.children;\n    children.forEach((span) => renderSpan(ctx, span.lines[0], span.props.textAnchor, span.props.dominantBaseline));\n};\n\nconst pairs = (values) => {\n    const result = [];\n    for (let i = 0; i < values.length; i += 2) {\n        result.push([values[i], values[i + 1]]);\n    }\n    return result;\n};\n/**\n * Parse svg-like points into number arrays\n *\n * @param points string ex. \"20,30 50,60\"\n * @returns points array ex. [[20, 30], [50, 60]]\n */\nconst parsePoints = (points) => {\n    let values = (points || '')\n        .trim()\n        .replace(/,/g, ' ')\n        .replace(/(\\d)-(\\d)/g, '$1 -$2')\n        .split(/\\s+/);\n    if (values.length % 2 !== 0) {\n        values = values.slice(0, -1);\n    }\n    const mappedValues = values.map(parseFloat);\n    return pairs(mappedValues);\n};\n\nconst drawPolyline = (ctx, points) => {\n    if (points.length > 0) {\n        ctx.moveTo(points[0][0], points[0][1]);\n        points.slice(1).forEach((p) => ctx.lineTo(p[0], p[1]));\n    }\n};\nconst renderPolyline = (ctx, node) => {\n    const points = parsePoints(node.props.points || '');\n    drawPolyline(ctx, points);\n};\n\nconst renderPolygon = (ctx, node) => {\n    const points = parsePoints(node.props.points || '');\n    drawPolyline(ctx, points);\n    ctx.closePath();\n};\n\nconst renderImage$1 = (ctx, node) => {\n    if (!node.box)\n        return;\n    if (!node.image?.data)\n        return;\n    const { x = 0, y = 0 } = node.props;\n    const { width, height, opacity } = node.style;\n    const paddingTop = node.box.paddingLeft || 0;\n    const paddingLeft = node.box.paddingLeft || 0;\n    if (width === 0 || height === 0) {\n        console.warn(`Image with src '${node.props.href}' skipped due to invalid dimensions`);\n        return;\n    }\n    if (typeof width === 'string' || typeof height === 'string') {\n        console.warn(`Image with src '${node.props.href}' skipped due to percentage width or height`);\n        return;\n    }\n    ctx.save();\n    ctx\n        .fillOpacity(opacity || 1)\n        .image(node.image.data, x + paddingLeft, y + paddingTop, {\n        width,\n        height,\n    });\n    ctx.restore();\n};\n\n// This constant is used to approximate a symmetrical arc using a cubic\n// Bezier curve.\nconst KAPPA$1 = 4.0 * ((Math.sqrt(2) - 1.0) / 3.0);\nconst clipNode = (ctx, node) => {\n    if (!node.box)\n        return;\n    if (!node.style)\n        return;\n    const { top, left, width, height } = node.box;\n    const { borderTopLeftRadius = 0, borderTopRightRadius = 0, borderBottomRightRadius = 0, borderBottomLeftRadius = 0, } = node.style;\n    // Border top\n    // @ts-expect-error this is always a number due to resolve border radius step\n    const rtr = Math.min(borderTopRightRadius, 0.5 * width, 0.5 * height);\n    const ctr = rtr * (1.0 - KAPPA$1);\n    ctx.moveTo(left + rtr, top);\n    ctx.lineTo(left + width - rtr, top);\n    ctx.bezierCurveTo(left + width - ctr, top, left + width, top + ctr, left + width, top + rtr);\n    // Border right\n    // @ts-expect-error this is always a number due to resolve border radius step\n    const rbr = Math.min(borderBottomRightRadius, 0.5 * width, 0.5 * height);\n    const cbr = rbr * (1.0 - KAPPA$1);\n    ctx.lineTo(left + width, top + height - rbr);\n    ctx.bezierCurveTo(left + width, top + height - cbr, left + width - cbr, top + height, left + width - rbr, top + height);\n    // Border bottom\n    // @ts-expect-error this is always a number due to resolve border radius step\n    const rbl = Math.min(borderBottomLeftRadius, 0.5 * width, 0.5 * height);\n    const cbl = rbl * (1.0 - KAPPA$1);\n    ctx.lineTo(left + rbl, top + height);\n    ctx.bezierCurveTo(left + cbl, top + height, left, top + height - cbl, left, top + height - rbl);\n    // Border left\n    // @ts-expect-error this is always a number due to resolve border radius step\n    const rtl = Math.min(borderTopLeftRadius, 0.5 * width, 0.5 * height);\n    const ctl = rtl * (1.0 - KAPPA$1);\n    ctx.lineTo(left, top + rtl);\n    ctx.bezierCurveTo(left, top + ctl, left + ctl, top, left + rtl, top);\n    ctx.closePath();\n    ctx.clip();\n};\n\nconst applySingleTransformation = (ctx, transform, origin) => {\n    const { operation, value } = transform;\n    switch (operation) {\n        case 'scale': {\n            const [scaleX, scaleY] = value;\n            ctx.scale(scaleX, scaleY, { origin });\n            break;\n        }\n        case 'rotate': {\n            const [angle] = value;\n            ctx.rotate(angle, { origin });\n            break;\n        }\n        case 'translate': {\n            const [x, y = 0] = value;\n            ctx.translate(x, y, { origin });\n            break;\n        }\n        case 'skew': {\n            const [xAngle = 0, yAngle = 0] = value;\n            const radx = (xAngle * Math.PI) / 180;\n            const rady = (yAngle * Math.PI) / 180;\n            const tanx = Math.tan(radx);\n            const tany = Math.tan(rady);\n            let x = 0;\n            let y = 0;\n            if (origin != null) {\n                [x, y] = Array.from(origin);\n                const x1 = x + tanx * y;\n                const y1 = y + tany * x;\n                x -= x1;\n                y -= y1;\n            }\n            ctx.transform(1, tany, tanx, 1, x, y);\n            break;\n        }\n        case 'matrix': {\n            ctx.transform(...value);\n            break;\n        }\n        default: {\n            console.error(`Transform operation: '${operation}' doesn't supported`);\n        }\n    }\n};\nconst applyTransformations = (ctx, node) => {\n    if (!node.origin)\n        return;\n    const { props, style } = node;\n    const origin = [node.origin.left, node.origin.top];\n    const propsTransform = 'transform' in props ? props.transform : undefined;\n    const operations = style?.transform || propsTransform || [];\n    operations.forEach((operation) => {\n        applySingleTransformation(ctx, operation, origin);\n    });\n};\n\n// From https://github.com/dy/svg-path-bounds/blob/master/index.js\nconst getPathBoundingBox = (node) => {\n    const path = normalizePath(absPath(parsePath(node.props?.d || '')));\n    if (!path.length)\n        return [0, 0, 0, 0];\n    const bounds = [Infinity, Infinity, -Infinity, -Infinity];\n    for (let i = 0, l = path.length; i < l; i += 1) {\n        const points = path[i].slice(1);\n        for (let j = 0; j < points.length; j += 2) {\n            if (points[j + 0] < bounds[0])\n                bounds[0] = points[j + 0];\n            if (points[j + 1] < bounds[1])\n                bounds[1] = points[j + 1];\n            if (points[j + 0] > bounds[2])\n                bounds[2] = points[j + 0];\n            if (points[j + 1] > bounds[3])\n                bounds[3] = points[j + 1];\n        }\n    }\n    return bounds;\n};\nconst getCircleBoundingBox = (node) => {\n    const r = node.props?.r || 0;\n    const cx = node.props?.cx || 0;\n    const cy = node.props?.cy || 0;\n    return [cx - r, cy - r, cx + r, cy + r];\n};\nconst getEllipseBoundingBox = (node) => {\n    const cx = node.props?.cx || 0;\n    const cy = node.props?.cy || 0;\n    const rx = node.props?.rx || 0;\n    const ry = node.props?.ry || 0;\n    return [cx - rx, cy - ry, cx + rx, cy + ry];\n};\nconst getLineBoundingBox = (node) => {\n    const x1 = node.props?.x1 || 0;\n    const y1 = node.props?.y1 || 0;\n    const x2 = node.props?.x2 || 0;\n    const y2 = node.props?.y2 || 0;\n    return [\n        Math.min(x1, x2),\n        Math.min(y1, y2),\n        Math.max(x1, x2),\n        Math.max(y1, y2),\n    ];\n};\nconst getRectBoundingBox = (node) => {\n    const x = node.props?.x || 0;\n    const y = node.props?.y || 0;\n    const width = node.props?.width || 0;\n    const height = node.props?.height || 0;\n    return [x, y, x + width, y + height];\n};\nconst max = (values) => Math.max(-Infinity, ...values);\nconst min = (values) => Math.min(Infinity, ...values);\nconst getPolylineBoundingBox = (node) => {\n    const points = parsePoints(node.props?.points);\n    const xValues = points.map((p) => p[0]);\n    const yValues = points.map((p) => p[1]);\n    return [min(xValues), min(yValues), max(xValues), max(yValues)];\n};\nconst boundingBoxFns = {\n    [P.Rect]: getRectBoundingBox,\n    [P.Line]: getLineBoundingBox,\n    [P.Path]: getPathBoundingBox,\n    [P.Circle]: getCircleBoundingBox,\n    [P.Ellipse]: getEllipseBoundingBox,\n    [P.Polygon]: getPolylineBoundingBox,\n    [P.Polyline]: getPolylineBoundingBox,\n};\nconst getBoundingBox = (node) => {\n    const boundingBoxFn = boundingBoxFns[node.type];\n    return boundingBoxFn ? boundingBoxFn(node) : [0, 0, 0, 0];\n};\n\nconst setStrokeWidth = (ctx, node) => {\n    if (!node.props)\n        return;\n    if (!('strokeWidth' in node.props))\n        return;\n    const lineWidth = node.props.strokeWidth;\n    if (lineWidth)\n        ctx.lineWidth(lineWidth);\n};\nconst setStrokeColor = (ctx, node) => {\n    if (!node.props)\n        return;\n    if (!('stroke' in node.props))\n        return;\n    const strokeColor = node.props.stroke;\n    if (strokeColor)\n        ctx.strokeColor(strokeColor);\n};\nconst setOpacity = (ctx, node) => {\n    if (!node.props)\n        return;\n    if (!('opacity' in node.props))\n        return;\n    const opacity = node.props.opacity;\n    if (!isNil(opacity))\n        ctx.opacity(opacity);\n};\nconst setFillOpacity = (ctx, node) => {\n    if (!node.props)\n        return;\n    if (!('fillOpacity' in node.props))\n        return;\n    const fillOpacity = node.props.fillOpacity || null;\n    if (!isNil(fillOpacity))\n        ctx.fillOpacity(fillOpacity);\n};\nconst setStrokeOpacity = (ctx, node) => {\n    if (!node.props)\n        return;\n    if (!('strokeOpacity' in node.props))\n        return;\n    const strokeOpacity = node.props?.strokeOpacity;\n    if (!isNil(strokeOpacity))\n        ctx.strokeOpacity(strokeOpacity);\n};\nconst setLineJoin = (ctx, node) => {\n    if (!node.props)\n        return;\n    if (!('strokeLinejoin' in node.props))\n        return;\n    const lineJoin = node.props.strokeLinejoin;\n    if (lineJoin)\n        ctx.lineJoin(lineJoin);\n};\nconst setLineCap = (ctx, node) => {\n    if (!node.props)\n        return;\n    if (!('strokeLinecap' in node.props))\n        return;\n    const lineCap = node.props?.strokeLinecap;\n    if (lineCap)\n        ctx.lineCap(lineCap);\n};\nconst setLineDash = (ctx, node) => {\n    if (!node.props)\n        return;\n    if (!('strokeDasharray' in node.props))\n        return;\n    const value = node.props?.strokeDasharray || null;\n    // @ts-expect-error check this works as expected\n    if (value)\n        ctx.dash(value.split(/[\\s,]+/).map(Number));\n};\nconst hasLinearGradientFill = (node) => {\n    if (!node.props)\n        return false;\n    if (!('fill' in node.props))\n        return false;\n    if (typeof node.props.fill === 'string')\n        return false;\n    return node.props.fill?.type === P.LinearGradient;\n};\nconst hasRadialGradientFill = (node) => {\n    if (!node.props)\n        return false;\n    if (!('fill' in node.props))\n        return false;\n    if (typeof node.props.fill === 'string')\n        return false;\n    return node.props.fill?.type === P.RadialGradient;\n};\nfunction multiplyMatrices(m1, m2) {\n    const a = m1[0] * m2[0] + m1[2] * m2[1];\n    const b = m1[1] * m2[0] + m1[3] * m2[1];\n    const c = m1[0] * m2[2] + m1[2] * m2[3];\n    const d = m1[1] * m2[2] + m1[3] * m2[3];\n    const e = m1[0] * m2[4] + m1[2] * m2[5] + m1[4];\n    const f = m1[1] * m2[4] + m1[3] * m2[5] + m1[5];\n    return [a, b, c, d, e, f];\n}\nconst transformGradient = (grad, transforms, bbox, units) => {\n    const matrices = transforms.map((transform) => {\n        switch (transform.operation) {\n            case 'scale': {\n                const value = transform.value;\n                return [value[0], 0, 0, value[1], 0, 0];\n            }\n            case 'translate': {\n                const value = transform.value;\n                let x = value[0] || 0;\n                let y = value[1] || 0;\n                if (units === 'objectBoundingBox') {\n                    x = (bbox[2] - bbox[0]) * x;\n                    y = (bbox[3] - bbox[1]) * y;\n                }\n                return [1, 0, 0, 1, x, y];\n            }\n            case 'rotate': {\n                const value = transform.value;\n                const cos = Math.cos(value[0]);\n                const sin = Math.sin(value[0]);\n                return [cos, sin, -sin, cos, 0, 0];\n            }\n            case 'skew': {\n                const value = transform.value;\n                return [1, Math.tan(value[0]), Math.tan(value[1]), 1, 0, 0];\n            }\n            case 'matrix': {\n                const value = transform.value;\n                let x = value[4] || 0;\n                let y = value[5] || 0;\n                if (units === 'objectBoundingBox') {\n                    x = (bbox[2] - bbox[0]) * x;\n                    y = (bbox[3] - bbox[1]) * y;\n                }\n                return [value[0], value[1], value[2], value[3], x, y];\n            }\n            default:\n                return [1, 0, 0, 1, 0, 0];\n        }\n    });\n    const matrix = matrices.reduce(multiplyMatrices, [1, 0, 0, 1, 0, 0]);\n    grad.setTransform(...matrix);\n};\n// Math simplified from https://github.com/devongovett/svgkit/blob/master/src/elements/SVGGradient.js#L104\nconst setLinearGradientFill = (ctx, node) => {\n    if (!node.props)\n        return;\n    if (!('fill' in node.props))\n        return;\n    const bbox = getBoundingBox(node);\n    const gradient = node.props?.fill;\n    if (!gradient)\n        return;\n    const units = gradient.props.gradientUnits || 'objectBoundingBox';\n    const transforms = gradient.props.gradientTransform || [];\n    let x1 = gradient.props.x1 || 0;\n    let y1 = gradient.props.y1 || 0;\n    let x2 = gradient.props.x2 || 1;\n    let y2 = gradient.props.y2 || 0;\n    if (units === 'objectBoundingBox') {\n        const m0 = bbox[2] - bbox[0];\n        const m3 = bbox[3] - bbox[1];\n        const m4 = bbox[0];\n        const m5 = bbox[1];\n        x1 = m0 * x1 + m4;\n        y1 = m3 * y1 + m5;\n        x2 = m0 * x2 + m4;\n        y2 = m3 * y2 + m5;\n    }\n    const grad = ctx.linearGradient(x1, y1, x2, y2);\n    transformGradient(grad, transforms, bbox, units);\n    gradient.children?.forEach((stop) => {\n        grad.stop(stop.props.offset, stop.props.stopColor, stop.props.stopOpacity);\n    });\n    ctx.fill(grad);\n};\n// Math simplified from https://github.com/devongovett/svgkit/blob/master/src/elements/SVGGradient.js#L155\nconst setRadialGradientFill = (ctx, node) => {\n    if (!node.props)\n        return;\n    if (!('fill' in node.props))\n        return;\n    const bbox = getBoundingBox(node);\n    const gradient = node.props?.fill;\n    if (!gradient)\n        return;\n    const units = gradient.props.gradientUnits || 'objectBoundingBox';\n    const transforms = gradient.props.gradientTransform || [];\n    let r = gradient.props.r || 0.5;\n    let cx = gradient.props.cx || 0.5;\n    let cy = gradient.props.cy || 0.5;\n    let fx = gradient.props.fx || cx;\n    let fy = gradient.props.fy || cy;\n    if (units === 'objectBoundingBox') {\n        const m0 = bbox[2] - bbox[0];\n        const m3 = bbox[3] - bbox[1];\n        const m4 = bbox[0];\n        const m5 = bbox[1];\n        r = r * m0;\n        cx = m0 * cx + m4;\n        cy = m3 * cy + m5;\n        fx = m0 * fx + m4;\n        fy = m3 * fy + m5;\n    }\n    const grad = ctx.radialGradient(cx, cy, 0, fx, fy, r);\n    transformGradient(grad, transforms, bbox, units);\n    gradient.children?.forEach((stop) => {\n        grad.stop(stop.props.offset, stop.props.stopColor, stop.props.stopOpacity);\n    });\n    ctx.fill(grad);\n};\nconst setFillColor = (ctx, node) => {\n    if (!node.props)\n        return;\n    if (!('fill' in node.props))\n        return;\n    const fillColor = node.props?.fill;\n    if (fillColor)\n        ctx.fillColor(fillColor);\n};\nconst setFill = (ctx, node) => {\n    if (hasLinearGradientFill(node))\n        return setLinearGradientFill(ctx, node);\n    if (hasRadialGradientFill(node))\n        return setRadialGradientFill(ctx, node);\n    return setFillColor(ctx, node);\n};\nconst draw = (ctx, node) => {\n    const props = node.props || {};\n    if ('fill' in props && 'stroke' in props && props.fill && props.stroke) {\n        ctx.fillAndStroke(props.fillRule);\n    }\n    else if ('fill' in props && props.fill) {\n        ctx.fill(props.fillRule);\n    }\n    else if ('stroke' in props && props.stroke) {\n        ctx.stroke();\n    }\n    else {\n        ctx.save();\n        ctx.opacity(0);\n        ctx.fill(null);\n        ctx.restore();\n    }\n};\nconst noop = () => { };\nconst renderFns$1 = {\n    [P.Tspan]: noop,\n    [P.TextInstance]: noop,\n    [P.Path]: renderPath,\n    [P.Rect]: renderRect,\n    [P.Line]: renderLine$1,\n    [P.G]: renderGroup,\n    [P.Text]: renderSvgText,\n    [P.Circle]: renderCircle,\n    [P.Image]: renderImage$1,\n    [P.Ellipse]: renderEllipse,\n    [P.Polygon]: renderPolygon,\n    [P.Polyline]: renderPolyline,\n};\nconst renderNode$1 = (ctx, node) => {\n    const renderFn = renderFns$1[node.type];\n    if (renderFn) {\n        renderFn(ctx, node);\n    }\n    else {\n        console.warn(`SVG node of type ${node.type} is not currently supported`);\n    }\n};\nconst drawNode = (ctx, node) => {\n    setLineCap(ctx, node);\n    setLineDash(ctx, node);\n    setLineJoin(ctx, node);\n    setStrokeWidth(ctx, node);\n    setStrokeColor(ctx, node);\n    setFill(ctx, node);\n    setStrokeOpacity(ctx, node);\n    setFillOpacity(ctx, node);\n    setOpacity(ctx, node);\n    applyTransformations(ctx, node);\n    renderNode$1(ctx, node);\n    draw(ctx, node);\n};\nconst clipPath = (ctx, node) => {\n    if (!node.props)\n        return;\n    if (!('clipPath' in node.props))\n        return;\n    const value = node.props.clipPath;\n    if (value) {\n        const children = value.children || [];\n        children.forEach((child) => renderNode$1(ctx, child));\n        ctx.clip();\n    }\n};\nconst drawChildren = (ctx, node) => {\n    const children = node.children || [];\n    children.forEach((child) => {\n        ctx.save();\n        clipPath(ctx, child);\n        drawNode(ctx, child);\n        drawChildren(ctx, child);\n        ctx.restore();\n    });\n};\nconst resolveAspectRatio = (ctx, node) => {\n    if (!node.box)\n        return;\n    const { width, height } = node.box;\n    const { viewBox, preserveAspectRatio } = node.props;\n    const { meetOrSlice = 'meet', align = 'xMidYMid' } = preserveAspectRatio || {};\n    if (viewBox == null || width == null || height == null)\n        return;\n    const x = viewBox?.minX || 0;\n    const y = viewBox?.minY || 0;\n    const logicalWidth = viewBox?.maxX || width;\n    const logicalHeight = viewBox?.maxY || height;\n    const logicalRatio = logicalWidth / logicalHeight;\n    const physicalRatio = width / height;\n    const scaleX = width / logicalWidth;\n    const scaleY = height / logicalHeight;\n    if (align === 'none') {\n        ctx.scale(scaleX, scaleY);\n        ctx.translate(-x, -y);\n        return;\n    }\n    if ((logicalRatio < physicalRatio && meetOrSlice === 'meet') ||\n        (logicalRatio >= physicalRatio && meetOrSlice === 'slice')) {\n        ctx.scale(scaleY, scaleY);\n        switch (align) {\n            case 'xMinYMin':\n            case 'xMinYMid':\n            case 'xMinYMax':\n                ctx.translate(-x, -y);\n                break;\n            case 'xMidYMin':\n            case 'xMidYMid':\n            case 'xMidYMax':\n                ctx.translate(-x - (logicalWidth - (width * logicalHeight) / height) / 2, -y);\n                break;\n            default:\n                ctx.translate(-x - (logicalWidth - (width * logicalHeight) / height), -y);\n        }\n    }\n    else {\n        ctx.scale(scaleX, scaleX);\n        switch (align) {\n            case 'xMinYMin':\n            case 'xMidYMin':\n            case 'xMaxYMin':\n                ctx.translate(-x, -y);\n                break;\n            case 'xMinYMid':\n            case 'xMidYMid':\n            case 'xMaxYMid':\n                ctx.translate(-x, -y - (logicalHeight - (height * logicalWidth) / width) / 2);\n                break;\n            default:\n                ctx.translate(-x, -y - (logicalHeight - (height * logicalWidth) / width));\n        }\n    }\n};\nconst moveToOrigin = (ctx, node) => {\n    if (!node.box)\n        return;\n    const { top, left } = node.box;\n    const paddingLeft = node.box.paddingLeft || 0;\n    const paddingTop = node.box.paddingTop || 0;\n    ctx.translate(left + paddingLeft, top + paddingTop);\n};\nconst renderSvg = (ctx, node) => {\n    ctx.save();\n    clipNode(ctx, node);\n    moveToOrigin(ctx, node);\n    resolveAspectRatio(ctx, node);\n    drawChildren(ctx, node);\n    ctx.restore();\n};\n\nconst black = { value: '#000', opacity: 1 };\n// TODO: parse to number[] in layout to avoid this step\nconst parseColor = (hex) => {\n    if (!hex)\n        return black;\n    const parsed = colorString.get(hex);\n    if (!parsed)\n        return black;\n    const value = colorString.to.hex(parsed.value.slice(0, 3));\n    const opacity = parsed.value[3];\n    return { value, opacity };\n};\n\nconst DEST_REGEXP = /^#.+/;\nconst isSrcId$1 = (src) => src.match(DEST_REGEXP);\nconst renderAttachment = (ctx, attachment) => {\n    const { xOffset = 0, yOffset = 0, width, height, image } = attachment;\n    ctx.translate(-width + xOffset, -height + yOffset);\n    ctx.image(image, 0, 0, {\n        fit: [width, height],\n        align: 'center',\n        valign: 'bottom',\n    });\n};\nconst renderAttachments = (ctx, run) => {\n    if (!run.glyphs)\n        return;\n    if (!run.positions)\n        return;\n    const font = run.attributes.font?.[0];\n    if (!font)\n        return;\n    ctx.save();\n    const space = font.glyphForCodePoint(0x20);\n    const objectReplacement = font.glyphForCodePoint(0xfffc);\n    let attachmentAdvance = 0;\n    for (let i = 0; i < run.glyphs.length; i += 1) {\n        const position = run.positions[i];\n        const glyph = run.glyphs[i];\n        attachmentAdvance += position.xAdvance || 0;\n        if (glyph.id === objectReplacement.id && run.attributes.attachment) {\n            ctx.translate(attachmentAdvance, position.yOffset || 0);\n            renderAttachment(ctx, run.attributes.attachment);\n            run.glyphs[i] = space;\n            attachmentAdvance = 0;\n        }\n    }\n    ctx.restore();\n};\nconst renderRun = (ctx, run) => {\n    if (!run.glyphs)\n        return;\n    if (!run.positions)\n        return;\n    const font = run.attributes.font?.[0];\n    if (!font)\n        return;\n    const { fontSize, link } = run.attributes;\n    const color = parseColor(run.attributes.color);\n    const opacity = isNil(run.attributes.opacity)\n        ? color.opacity\n        : run.attributes.opacity;\n    const { height = 0, descent = 0, xAdvance = 0 } = run;\n    ctx.fillColor(color.value);\n    ctx.fillOpacity(opacity);\n    if (link) {\n        if (isSrcId$1(link)) {\n            ctx.goTo(0, -height - descent, xAdvance, height, link.slice(1));\n        }\n        else {\n            ctx.link(0, -height - descent, xAdvance, height, link);\n        }\n    }\n    renderAttachments(ctx, run);\n    ctx.font(font.type === 'STANDARD' ? font.fullName : font, fontSize);\n    try {\n        renderGlyphs(ctx, run.glyphs, run.positions, 0, 0);\n    }\n    catch (error) {\n        console.log(error);\n    }\n    ctx.translate(xAdvance, 0);\n};\nconst renderBackground$1 = (ctx, rect, backgroundColor) => {\n    const color = parseColor(backgroundColor);\n    ctx.save();\n    ctx.fillOpacity(color.opacity);\n    ctx.rect(rect.x, rect.y, rect.width, rect.height);\n    ctx.fill(color.value);\n    ctx.restore();\n};\nconst renderDecorationLine = (ctx, decorationLine) => {\n    ctx.save();\n    ctx.lineWidth(decorationLine.rect.height);\n    ctx.strokeOpacity(decorationLine.opacity);\n    if (/dashed/.test(decorationLine.style)) {\n        ctx.dash(3 * decorationLine.rect.height, {});\n    }\n    else if (/dotted/.test(decorationLine.style)) {\n        ctx.dash(decorationLine.rect.height, {});\n    }\n    if (/wavy/.test(decorationLine.style)) {\n        const dist = Math.max(2, decorationLine.rect.height);\n        let step = 1.1 * dist;\n        const stepCount = Math.floor(decorationLine.rect.width / (2 * step));\n        // Adjust step to fill entire width\n        const remainingWidth = decorationLine.rect.width - stepCount * 2 * step;\n        const adjustment = remainingWidth / stepCount / 2;\n        step += adjustment;\n        const cp1y = decorationLine.rect.y + dist;\n        const cp2y = decorationLine.rect.y - dist;\n        let { x } = decorationLine.rect;\n        ctx.moveTo(decorationLine.rect.x, decorationLine.rect.y);\n        for (let i = 0; i < stepCount; i += 1) {\n            ctx.bezierCurveTo(x + step, cp1y, x + step, cp2y, x + 2 * step, decorationLine.rect.y);\n            x += 2 * step;\n        }\n    }\n    else {\n        ctx.moveTo(decorationLine.rect.x, decorationLine.rect.y);\n        ctx.lineTo(decorationLine.rect.x + decorationLine.rect.width, decorationLine.rect.y);\n        if (/double/.test(decorationLine.style)) {\n            ctx.moveTo(decorationLine.rect.x, decorationLine.rect.y + decorationLine.rect.height * 2);\n            ctx.lineTo(decorationLine.rect.x + decorationLine.rect.width, decorationLine.rect.y + decorationLine.rect.height * 2);\n        }\n    }\n    ctx.stroke(decorationLine.color);\n    ctx.restore();\n};\nconst renderLine = (ctx, line) => {\n    if (!line.box)\n        return;\n    const lineAscent = line.ascent || 0;\n    ctx.save();\n    ctx.translate(line.box.x, line.box.y + lineAscent);\n    for (let i = 0; i < line.runs.length; i += 1) {\n        const run = line.runs[i];\n        const isLastRun = i === line.runs.length - 1;\n        if (run.attributes.backgroundColor) {\n            const xAdvance = run.xAdvance ?? 0;\n            const overflowRight = isLastRun ? line.overflowRight ?? 0 : 0;\n            const backgroundRect = {\n                x: 0,\n                y: -lineAscent,\n                height: line.box.height,\n                width: xAdvance - overflowRight,\n            };\n            renderBackground$1(ctx, backgroundRect, run.attributes.backgroundColor);\n        }\n        renderRun(ctx, run);\n    }\n    ctx.restore();\n    ctx.save();\n    ctx.translate(line.box.x, line.box.y);\n    if (line.decorationLines) {\n        for (let i = 0; i < line.decorationLines.length; i += 1) {\n            const decorationLine = line.decorationLines[i];\n            renderDecorationLine(ctx, decorationLine);\n        }\n    }\n    ctx.restore();\n};\nconst renderBlock = (ctx, block) => {\n    block.forEach((line) => {\n        renderLine(ctx, line);\n    });\n};\nconst renderText = (ctx, node) => {\n    if (!node.box)\n        return;\n    if (!node.lines)\n        return;\n    const { top, left } = node.box;\n    const blocks = [node.lines];\n    const paddingTop = node.box?.paddingTop || 0;\n    const paddingLeft = node.box?.paddingLeft || 0;\n    const initialY = node.lines[0] ? node.lines[0].box.y : 0;\n    const offsetX = node.alignOffset || 0;\n    ctx.save();\n    ctx.translate(left + paddingLeft - offsetX, top + paddingTop - initialY);\n    blocks.forEach((block) => {\n        renderBlock(ctx, block);\n    });\n    ctx.restore();\n};\n\nconst renderPage = (ctx, node) => {\n    if (!node.box)\n        return;\n    const { width, height } = node.box;\n    const dpi = node.props?.dpi || 72;\n    const userUnit = dpi / 72;\n    ctx.addPage({ size: [width, height], margin: 0, userUnit });\n};\n\nconst renderNote = (ctx, node) => {\n    if (!node.box)\n        return;\n    const { top, left } = node.box;\n    const value = node?.children?.[0].value || '';\n    const color = node.style?.backgroundColor;\n    ctx.note(left, top, 0, 0, value, { color });\n};\n\nconst embedImage = (ctx, node) => {\n    const src = node.image.data;\n    let image;\n    if (typeof src === 'string') {\n        image = ctx._imageRegistry[src];\n    }\n    if (!image) {\n        image = ctx.openImage(src);\n    }\n    if (!image.obj) {\n        image.embed(ctx);\n    }\n    return image;\n};\n\nconst isNumeric = (n) => {\n    return !Number.isNaN(parseFloat(n)) && Number.isFinite(n);\n};\nconst applyContainObjectFit = (cw, ch, iw, ih, px, py) => {\n    const cr = cw / ch;\n    const ir = iw / ih;\n    const pxp = matchPercent(px ?? null);\n    const pyp = matchPercent(py ?? null);\n    const pxv = pxp ? pxp.percent : 0.5;\n    const pyv = pyp ? pyp.percent : 0.5;\n    if (cr > ir) {\n        const height = ch;\n        const width = height * ir;\n        const yOffset = isNumeric(py) ? py : 0;\n        const xOffset = isNumeric(px) ? px : (cw - width) * pxv;\n        return { width, height, xOffset, yOffset };\n    }\n    const width = cw;\n    const height = width / ir;\n    const xOffset = isNumeric(px) ? px : 0;\n    const yOffset = isNumeric(py) ? py : (ch - height) * pyv;\n    return { width, height, yOffset, xOffset };\n};\nconst applyNoneObjectFit = (cw, ch, iw, ih, px, py) => {\n    const width = iw;\n    const height = ih;\n    const pxp = matchPercent(px ?? null);\n    const pyp = matchPercent(py ?? null);\n    const pxv = pxp ? pxp.percent : 0.5;\n    const pyv = pyp ? pyp.percent : 0.5;\n    const xOffset = isNumeric(px) ? px : (cw - width) * pxv;\n    const yOffset = isNumeric(py) ? py : (ch - height) * pyv;\n    return { width, height, xOffset, yOffset };\n};\nconst applyCoverObjectFit = (cw, ch, iw, ih, px, py) => {\n    const ir = iw / ih;\n    const cr = cw / ch;\n    const pxp = matchPercent(px ?? null);\n    const pyp = matchPercent(py ?? null);\n    const pxv = pxp ? pxp.percent : 0.5;\n    const pyv = pyp ? pyp.percent : 0.5;\n    if (cr > ir) {\n        const width = cw;\n        const height = width / ir;\n        const xOffset = isNumeric(px) ? px : 0;\n        const yOffset = isNumeric(py) ? py : (ch - height) * pyv;\n        return { width, height, yOffset, xOffset };\n    }\n    const height = ch;\n    const width = height * ir;\n    const xOffset = isNumeric(px) ? px : (cw - width) * pxv;\n    const yOffset = isNumeric(py) ? py : 0;\n    return { width, height, xOffset, yOffset };\n};\nconst applyScaleDownObjectFit = (cw, ch, iw, ih, px, py) => {\n    const containDimension = applyContainObjectFit(cw, ch, iw, ih, px, py);\n    const noneDimension = applyNoneObjectFit(cw, ch, iw, ih, px, py);\n    return containDimension.width < noneDimension.width\n        ? containDimension\n        : noneDimension;\n};\nconst applyFillObjectFit = (cw, ch, px, py) => {\n    return {\n        width: cw,\n        height: ch,\n        xOffset: matchPercent(px ?? null) ? 0 : px || 0,\n        yOffset: matchPercent(py ?? null) ? 0 : py || 0,\n    };\n};\nconst resolveObjectFit = (type = 'fill', cw, ch, iw, ih, px, py) => {\n    switch (type) {\n        case 'contain':\n            return applyContainObjectFit(cw, ch, iw, ih, px, py);\n        case 'cover':\n            return applyCoverObjectFit(cw, ch, iw, ih, px, py);\n        case 'none':\n            return applyNoneObjectFit(cw, ch, iw, ih, px, py);\n        case 'scale-down':\n            return applyScaleDownObjectFit(cw, ch, iw, ih, px, py);\n        default:\n            return applyFillObjectFit(cw, ch, px, py);\n    }\n};\n\nconst drawImage = (ctx, node, options) => {\n    if (!node.box)\n        return;\n    if (!node.image)\n        return;\n    const { left, top } = node.box;\n    const opacity = node.style?.opacity;\n    const objectFit = node.style?.objectFit;\n    const objectPositionX = node.style?.objectPositionX;\n    const objectPositionY = node.style?.objectPositionY;\n    const paddingTop = node.box.paddingTop || 0;\n    const paddingRight = node.box.paddingRight || 0;\n    const paddingBottom = node.box.paddingBottom || 0;\n    const paddingLeft = node.box.paddingLeft || 0;\n    const imageCache = options.imageCache || new Map();\n    const { width, height, xOffset, yOffset } = resolveObjectFit(objectFit, node.box.width - paddingLeft - paddingRight, node.box.height - paddingTop - paddingBottom, node.image.width, node.image.height, objectPositionX, objectPositionY);\n    if (node.image.data) {\n        if (width !== 0 && height !== 0) {\n            const cacheKey = node.image.key;\n            const image = imageCache.get(cacheKey) || embedImage(ctx, node);\n            if (cacheKey)\n                imageCache.set(cacheKey, image);\n            const imageOpacity = isNil(opacity) ? 1 : opacity;\n            ctx\n                .fillOpacity(imageOpacity)\n                .image(image, left + paddingLeft + xOffset, top + paddingTop + yOffset, {\n                width,\n                height,\n            });\n        }\n        else {\n            console.warn(`Image with src '${JSON.stringify(node.props.src || node.props.source)}' skipped due to invalid dimensions`);\n        }\n    }\n};\nconst renderImage = (ctx, node, options) => {\n    ctx.save();\n    clipNode(ctx, node);\n    drawImage(ctx, node, options);\n    ctx.restore();\n};\n\nconst CONTENT_COLOR = '#a1c6e7';\nconst PADDING_COLOR = '#c4deb9';\nconst MARGIN_COLOR = '#f8cca1';\n// TODO: Draw debug boxes using clipping to enhance quality\nconst debugContent = (ctx, node) => {\n    if (!node.box)\n        return;\n    const { left, top, width, height, paddingLeft = 0, paddingTop = 0, paddingRight = 0, paddingBottom = 0, borderLeftWidth = 0, borderTopWidth = 0, borderRightWidth = 0, borderBottomWidth = 0, } = node.box;\n    ctx\n        .fillColor(CONTENT_COLOR)\n        .opacity(0.5)\n        .rect(left + paddingLeft + borderLeftWidth, top + paddingTop + borderTopWidth, width - paddingLeft - paddingRight - borderRightWidth - borderLeftWidth, height - paddingTop - paddingBottom - borderTopWidth - borderBottomWidth)\n        .fill();\n};\nconst debugPadding = (ctx, node) => {\n    if (!node.box)\n        return;\n    const { left, top, width, height, paddingLeft = 0, paddingTop = 0, paddingRight = 0, paddingBottom = 0, borderLeftWidth = 0, borderTopWidth = 0, borderRightWidth = 0, borderBottomWidth = 0, } = node.box;\n    ctx.fillColor(PADDING_COLOR).opacity(0.5);\n    // Padding top\n    ctx\n        .rect(left + paddingLeft + borderLeftWidth, top + borderTopWidth, width - paddingRight - paddingLeft - borderLeftWidth - borderRightWidth, paddingTop)\n        .fill();\n    // Padding left\n    ctx\n        .rect(left + borderLeftWidth, top + borderTopWidth, paddingLeft, height - borderTopWidth - borderBottomWidth)\n        .fill();\n    // Padding right\n    ctx\n        .rect(left + width - paddingRight - borderRightWidth, top + borderTopWidth, paddingRight, height - borderTopWidth - borderBottomWidth)\n        .fill();\n    // Padding bottom\n    ctx\n        .rect(left + paddingLeft + borderLeftWidth, top + height - paddingBottom - borderBottomWidth, width - paddingRight - paddingLeft - borderLeftWidth - borderRightWidth, paddingBottom)\n        .fill();\n};\nconst debugMargin = (ctx, node) => {\n    if (!node.box)\n        return;\n    const { left, top, width, height } = node.box;\n    const { marginLeft = 0, marginTop = 0, marginRight = 0, marginBottom = 0, } = node.box;\n    ctx.fillColor(MARGIN_COLOR).opacity(0.5);\n    // Margin top\n    ctx.rect(left, top - marginTop, width, marginTop).fill();\n    // Margin left\n    ctx\n        .rect(left - marginLeft, top - marginTop, marginLeft, height + marginTop + marginBottom)\n        .fill();\n    // Margin right\n    ctx\n        .rect(left + width, top - marginTop, marginRight, height + marginTop + marginBottom)\n        .fill();\n    // Margin bottom\n    ctx.rect(left, top + height, width, marginBottom).fill();\n};\nconst debugText = (ctx, node) => {\n    if (!node.box)\n        return;\n    const { left, top, width, height } = node.box;\n    const { marginLeft = 0, marginTop = 0, marginRight = 0, marginBottom = 0, } = node.box;\n    const roundedWidth = Math.round(width + marginLeft + marginRight);\n    const roundedHeight = Math.round(height + marginTop + marginBottom);\n    ctx\n        .fontSize(6)\n        .opacity(1)\n        .fillColor('black')\n        .text(`${roundedWidth} x ${roundedHeight}`, left - marginLeft, Math.max(top - marginTop - 4, 1), { width: Infinity });\n};\nconst debugOrigin = (ctx, node) => {\n    if (node.origin) {\n        ctx\n            .circle(node.origin.left, node.origin.top, 3)\n            .fill('red')\n            .circle(node.origin.left, node.origin.top, 5)\n            .stroke('red');\n    }\n};\nconst renderDebug = (ctx, node) => {\n    if (!node.props)\n        return;\n    if (!('debug' in node.props) || !node.props.debug)\n        return;\n    ctx.save();\n    debugContent(ctx, node);\n    debugPadding(ctx, node);\n    debugMargin(ctx, node);\n    debugText(ctx, node);\n    debugOrigin(ctx, node);\n    ctx.restore();\n};\n\nconst availableMethods = [\n    'dash',\n    'clip',\n    'save',\n    'path',\n    'fill',\n    'font',\n    'text',\n    'rect',\n    'scale',\n    'moveTo',\n    'lineTo',\n    'stroke',\n    'rotate',\n    'circle',\n    'lineCap',\n    'opacity',\n    'ellipse',\n    'polygon',\n    'restore',\n    'lineJoin',\n    'fontSize',\n    'fillColor',\n    'lineWidth',\n    'translate',\n    'miterLimit',\n    'strokeColor',\n    'fillOpacity',\n    'roundedRect',\n    'fillAndStroke',\n    'strokeOpacity',\n    'bezierCurveTo',\n    'quadraticCurveTo',\n    'linearGradient',\n    'radialGradient',\n];\nconst painter = (ctx) => {\n    const p = availableMethods.reduce((acc, prop) => ({\n        ...acc,\n        [prop]: (...args) => {\n            // @ts-expect-error ctx[prop] is a function\n            ctx[prop](...args);\n            return p;\n        },\n    }), {});\n    return p;\n};\nconst renderCanvas = (ctx, node) => {\n    if (!node.box)\n        return;\n    const { top, left, width, height } = node.box;\n    const paddingTop = node.box.paddingTop || 0;\n    const paddingLeft = node.box.paddingLeft || 0;\n    const paddingRight = node.box.paddingRight || 0;\n    const paddingBottom = node.box.paddingBottom || 0;\n    const availableWidth = width - paddingLeft - paddingRight;\n    const availableHeight = height - paddingTop - paddingBottom;\n    if (!availableWidth || !availableHeight) {\n        console.warn('Canvas element has null width or height. Please provide valid values via the `style` prop in order to correctly render it.');\n    }\n    ctx.save().translate(left + paddingLeft, top + paddingTop);\n    if (node.props.paint) {\n        node.props.paint(painter(ctx), availableWidth, availableHeight);\n    }\n    ctx.restore();\n};\n\n// Ref: https://www.w3.org/TR/css-backgrounds-3/#borders\n// This constant is used to approximate a symmetrical arc using a cubic Bezier curve.\nconst KAPPA = 4.0 * ((Math.sqrt(2) - 1.0) / 3.0);\nconst clipBorderTop = (ctx, layout, style, rtr, rtl) => {\n    const { top, left, width, height } = layout;\n    const { borderTopWidth, borderRightWidth, borderLeftWidth } = style;\n    // Clip outer top border edge\n    ctx.moveTo(left + rtl, top);\n    ctx.lineTo(left + width - rtr, top);\n    // Ellipse coefficients outer top right cap\n    const c0 = rtr * (1.0 - KAPPA);\n    // Clip outer top right cap\n    ctx.bezierCurveTo(left + width - c0, top, left + width, top + c0, left + width, top + rtr);\n    // Move down in case the margin exceedes the radius\n    const topRightYCoord = top + Math.max(borderTopWidth, rtr);\n    ctx.lineTo(left + width, topRightYCoord);\n    // Clip inner top right cap\n    ctx.lineTo(left + width - borderRightWidth, topRightYCoord);\n    // Ellipse coefficients inner top right cap\n    const innerTopRightRadiusX = Math.max(rtr - borderRightWidth, 0);\n    const innerTopRightRadiusY = Math.max(rtr - borderTopWidth, 0);\n    const c1 = innerTopRightRadiusX * (1.0 - KAPPA);\n    const c2 = innerTopRightRadiusY * (1.0 - KAPPA);\n    // Clip inner top right cap\n    ctx.bezierCurveTo(left + width - borderRightWidth, top + borderTopWidth + c2, left + width - borderRightWidth - c1, top + borderTopWidth, left + width - borderRightWidth - innerTopRightRadiusX, top + borderTopWidth);\n    // Clip inner top border edge\n    ctx.lineTo(left + Math.max(rtl, borderLeftWidth), top + borderTopWidth);\n    // Ellipse coefficients inner top left cap\n    const innerTopLeftRadiusX = Math.max(rtl - borderLeftWidth, 0);\n    const innerTopLeftRadiusY = Math.max(rtl - borderTopWidth, 0);\n    const c3 = innerTopLeftRadiusX * (1.0 - KAPPA);\n    const c4 = innerTopLeftRadiusY * (1.0 - KAPPA);\n    const topLeftYCoord = top + Math.max(borderTopWidth, rtl);\n    // Clip inner top left cap\n    ctx.bezierCurveTo(left + borderLeftWidth + c3, top + borderTopWidth, left + borderLeftWidth, top + borderTopWidth + c4, left + borderLeftWidth, topLeftYCoord);\n    ctx.lineTo(left, topLeftYCoord);\n    // Move down in case the margin exceedes the radius\n    ctx.lineTo(left, top + rtl);\n    // Ellipse coefficients outer top left cap\n    const c5 = rtl * (1.0 - KAPPA);\n    // Clip outer top left cap\n    ctx.bezierCurveTo(left, top + c5, left + c5, top, left + rtl, top);\n    ctx.closePath();\n    ctx.clip();\n    // Clip border top cap joins\n    if (borderRightWidth) {\n        const trSlope = -borderTopWidth / borderRightWidth;\n        ctx.moveTo(left + width / 2, trSlope * (-width / 2) + top);\n        ctx.lineTo(left + width, top);\n        ctx.lineTo(left, top);\n        ctx.lineTo(left, top + height);\n        ctx.closePath();\n        ctx.clip();\n    }\n    if (borderLeftWidth) {\n        const trSlope = -borderTopWidth / borderLeftWidth;\n        ctx.moveTo(left + width / 2, trSlope * (-width / 2) + top);\n        ctx.lineTo(left, top);\n        ctx.lineTo(left + width, top);\n        ctx.lineTo(left + width, top + height);\n        ctx.closePath();\n        ctx.clip();\n    }\n};\nconst fillBorderTop = (ctx, layout, style, rtr, rtl) => {\n    const { top, left, width } = layout;\n    const { borderTopColor, borderTopWidth, borderTopStyle, borderRightWidth, borderLeftWidth, } = style;\n    const c0 = rtl * (1.0 - KAPPA);\n    const c1 = rtr * (1.0 - KAPPA);\n    ctx.moveTo(left, top + Math.max(rtl, borderTopWidth));\n    ctx.bezierCurveTo(left, top + c0, left + c0, top, left + rtl, top);\n    ctx.lineTo(left + width - rtr, top);\n    ctx.bezierCurveTo(left + width - c1, top, left + width, top + c1, left + width, top + rtr);\n    ctx.strokeColor(borderTopColor);\n    ctx.lineWidth(Math.max(borderRightWidth, borderTopWidth, borderLeftWidth) * 2);\n    if (borderTopStyle === 'dashed') {\n        ctx.dash(borderTopWidth * 2, { space: borderTopWidth * 1.2 });\n    }\n    else if (borderTopStyle === 'dotted') {\n        ctx.dash(borderTopWidth, { space: borderTopWidth * 1.2 });\n    }\n    ctx.stroke();\n    ctx.undash();\n};\nconst clipBorderRight = (ctx, layout, style, rtr, rbr) => {\n    const { top, left, width, height } = layout;\n    const { borderTopWidth, borderRightWidth, borderBottomWidth } = style;\n    // Clip outer right border edge\n    ctx.moveTo(left + width, top + rtr);\n    ctx.lineTo(left + width, top + height - rbr);\n    // Ellipse coefficients outer bottom right cap\n    const c0 = rbr * (1.0 - KAPPA);\n    // Clip outer top right cap\n    ctx.bezierCurveTo(left + width, top + height - c0, left + width - c0, top + height, left + width - rbr, top + height);\n    // Move left in case the margin exceedes the radius\n    const topBottomXCoord = left + width - Math.max(borderRightWidth, rbr);\n    ctx.lineTo(topBottomXCoord, top + height);\n    // Clip inner bottom right cap\n    ctx.lineTo(topBottomXCoord, top + height - borderBottomWidth);\n    // Ellipse coefficients inner bottom right cap\n    const innerBottomRightRadiusX = Math.max(rbr - borderRightWidth, 0);\n    const innerBottomRightRadiusY = Math.max(rbr - borderBottomWidth, 0);\n    const c1 = innerBottomRightRadiusX * (1.0 - KAPPA);\n    const c2 = innerBottomRightRadiusY * (1.0 - KAPPA);\n    // Clip inner top right cap\n    ctx.bezierCurveTo(left + width - borderRightWidth - c1, top + height - borderBottomWidth, left + width - borderRightWidth, top + height - borderBottomWidth - c2, left + width - borderRightWidth, top + height - Math.max(rbr, borderBottomWidth));\n    // Clip inner right border edge\n    ctx.lineTo(left + width - borderRightWidth, top + Math.max(rtr, borderTopWidth));\n    // Ellipse coefficients inner top right cap\n    const innerTopRightRadiusX = Math.max(rtr - borderRightWidth, 0);\n    const innerTopRightRadiusY = Math.max(rtr - borderTopWidth, 0);\n    const c3 = innerTopRightRadiusX * (1.0 - KAPPA);\n    const c4 = innerTopRightRadiusY * (1.0 - KAPPA);\n    const topRightXCoord = left + width - Math.max(rtr, borderRightWidth);\n    // Clip inner top left cap\n    ctx.bezierCurveTo(left + width - borderRightWidth, top + borderTopWidth + c4, left + width - borderRightWidth - c3, top + borderTopWidth, topRightXCoord, top + borderTopWidth);\n    ctx.lineTo(topRightXCoord, top);\n    // Move right in case the margin exceedes the radius\n    ctx.lineTo(left + width - rtr, top);\n    // Ellipse coefficients outer top right cap\n    const c5 = rtr * (1.0 - KAPPA);\n    // Clip outer top right cap\n    ctx.bezierCurveTo(left + width - c5, top, left + width, top + c5, left + width, top + rtr);\n    ctx.closePath();\n    ctx.clip();\n    // Clip border right cap joins\n    if (borderTopWidth) {\n        const trSlope = -borderTopWidth / borderRightWidth;\n        ctx.moveTo(left + width / 2, trSlope * (-width / 2) + top);\n        ctx.lineTo(left + width, top);\n        ctx.lineTo(left + width, top + height);\n        ctx.lineTo(left, top + height);\n        ctx.closePath();\n        ctx.clip();\n    }\n    if (borderBottomWidth) {\n        const brSlope = borderBottomWidth / borderRightWidth;\n        ctx.moveTo(left + width / 2, brSlope * (-width / 2) + top + height);\n        ctx.lineTo(left + width, top + height);\n        ctx.lineTo(left + width, top);\n        ctx.lineTo(left, top);\n        ctx.closePath();\n        ctx.clip();\n    }\n};\nconst fillBorderRight = (ctx, layout, style, rtr, rbr) => {\n    const { top, left, width, height } = layout;\n    const { borderRightColor, borderRightStyle, borderRightWidth, borderTopWidth, borderBottomWidth, } = style;\n    const c0 = rbr * (1.0 - KAPPA);\n    const c1 = rtr * (1.0 - KAPPA);\n    ctx.moveTo(left + width - rtr, top);\n    ctx.bezierCurveTo(left + width - c1, top, left + width, top + c1, left + width, top + rtr);\n    ctx.lineTo(left + width, top + height - rbr);\n    ctx.bezierCurveTo(left + width, top + height - c0, left + width - c0, top + height, left + width - rbr, top + height);\n    ctx.strokeColor(borderRightColor);\n    ctx.lineWidth(Math.max(borderRightWidth, borderTopWidth, borderBottomWidth) * 2);\n    if (borderRightStyle === 'dashed') {\n        ctx.dash(borderRightWidth * 2, { space: borderRightWidth * 1.2 });\n    }\n    else if (borderRightStyle === 'dotted') {\n        ctx.dash(borderRightWidth, { space: borderRightWidth * 1.2 });\n    }\n    ctx.stroke();\n    ctx.undash();\n};\nconst clipBorderBottom = (ctx, layout, style, rbl, rbr) => {\n    const { top, left, width, height } = layout;\n    const { borderBottomWidth, borderRightWidth, borderLeftWidth } = style;\n    // Clip outer top border edge\n    ctx.moveTo(left + width - rbr, top + height);\n    ctx.lineTo(left + rbl, top + height);\n    // Ellipse coefficients outer top right cap\n    const c0 = rbl * (1.0 - KAPPA);\n    // Clip outer top right cap\n    ctx.bezierCurveTo(left + c0, top + height, left, top + height - c0, left, top + height - rbl);\n    // Move up in case the margin exceedes the radius\n    const bottomLeftYCoord = top + height - Math.max(borderBottomWidth, rbl);\n    ctx.lineTo(left, bottomLeftYCoord);\n    // Clip inner bottom left cap\n    ctx.lineTo(left + borderLeftWidth, bottomLeftYCoord);\n    // Ellipse coefficients inner top right cap\n    const innerBottomLeftRadiusX = Math.max(rbl - borderLeftWidth, 0);\n    const innerBottomLeftRadiusY = Math.max(rbl - borderBottomWidth, 0);\n    const c1 = innerBottomLeftRadiusX * (1.0 - KAPPA);\n    const c2 = innerBottomLeftRadiusY * (1.0 - KAPPA);\n    // Clip inner bottom left cap\n    ctx.bezierCurveTo(left + borderLeftWidth, top + height - borderBottomWidth - c2, left + borderLeftWidth + c1, top + height - borderBottomWidth, left + borderLeftWidth + innerBottomLeftRadiusX, top + height - borderBottomWidth);\n    // Clip inner bottom border edge\n    ctx.lineTo(left + width - Math.max(rbr, borderRightWidth), top + height - borderBottomWidth);\n    // Ellipse coefficients inner top left cap\n    const innerBottomRightRadiusX = Math.max(rbr - borderRightWidth, 0);\n    const innerBottomRightRadiusY = Math.max(rbr - borderBottomWidth, 0);\n    const c3 = innerBottomRightRadiusX * (1.0 - KAPPA);\n    const c4 = innerBottomRightRadiusY * (1.0 - KAPPA);\n    const bottomRightYCoord = top + height - Math.max(borderBottomWidth, rbr);\n    // Clip inner top left cap\n    ctx.bezierCurveTo(left + width - borderRightWidth - c3, top + height - borderBottomWidth, left + width - borderRightWidth, top + height - borderBottomWidth - c4, left + width - borderRightWidth, bottomRightYCoord);\n    ctx.lineTo(left + width, bottomRightYCoord);\n    // Move down in case the margin exceedes the radius\n    ctx.lineTo(left + width, top + height - rbr);\n    // Ellipse coefficients outer top left cap\n    const c5 = rbr * (1.0 - KAPPA);\n    // Clip outer top left cap\n    ctx.bezierCurveTo(left + width, top + height - c5, left + width - c5, top + height, left + width - rbr, top + height);\n    ctx.closePath();\n    ctx.clip();\n    // Clip border bottom cap joins\n    if (borderRightWidth) {\n        const brSlope = borderBottomWidth / borderRightWidth;\n        ctx.moveTo(left + width / 2, brSlope * (-width / 2) + top + height);\n        ctx.lineTo(left + width, top + height);\n        ctx.lineTo(left, top + height);\n        ctx.lineTo(left, top);\n        ctx.closePath();\n        ctx.clip();\n    }\n    if (borderLeftWidth) {\n        const trSlope = -borderBottomWidth / borderLeftWidth;\n        ctx.moveTo(left + width / 2, trSlope * (width / 2) + top + height);\n        ctx.lineTo(left, top + height);\n        ctx.lineTo(left + width, top + height);\n        ctx.lineTo(left + width, top);\n        ctx.closePath();\n        ctx.clip();\n    }\n};\nconst fillBorderBottom = (ctx, layout, style, rbl, rbr) => {\n    const { top, left, width, height } = layout;\n    const { borderBottomColor, borderBottomStyle, borderBottomWidth, borderRightWidth, borderLeftWidth, } = style;\n    const c0 = rbl * (1.0 - KAPPA);\n    const c1 = rbr * (1.0 - KAPPA);\n    ctx.moveTo(left + width, top + height - rbr);\n    ctx.bezierCurveTo(left + width, top + height - c1, left + width - c1, top + height, left + width - rbr, top + height);\n    ctx.lineTo(left + rbl, top + height);\n    ctx.bezierCurveTo(left + c0, top + height, left, top + height - c0, left, top + height - rbl);\n    ctx.strokeColor(borderBottomColor);\n    ctx.lineWidth(Math.max(borderBottomWidth, borderRightWidth, borderLeftWidth) * 2);\n    if (borderBottomStyle === 'dashed') {\n        ctx.dash(borderBottomWidth * 2, { space: borderBottomWidth * 1.2 });\n    }\n    else if (borderBottomStyle === 'dotted') {\n        ctx.dash(borderBottomWidth, { space: borderBottomWidth * 1.2 });\n    }\n    ctx.stroke();\n    ctx.undash();\n};\nconst clipBorderLeft = (ctx, layout, style, rbl, rtl) => {\n    const { top, left, width, height } = layout;\n    const { borderTopWidth, borderLeftWidth, borderBottomWidth } = style;\n    // Clip outer left border edge\n    ctx.moveTo(left, top + height - rbl);\n    ctx.lineTo(left, top + rtl);\n    // Ellipse coefficients outer top left cap\n    const c0 = rtl * (1.0 - KAPPA);\n    // Clip outer top left cap\n    ctx.bezierCurveTo(left, top + c0, left + c0, top, left + rtl, top);\n    // Move right in case the margin exceedes the radius\n    const topLeftCoordX = left + Math.max(borderLeftWidth, rtl);\n    ctx.lineTo(topLeftCoordX, top);\n    // Clip inner top left cap\n    ctx.lineTo(topLeftCoordX, top + borderTopWidth);\n    // Ellipse coefficients inner top left cap\n    const innerTopLeftRadiusX = Math.max(rtl - borderLeftWidth, 0);\n    const innerTopLeftRadiusY = Math.max(rtl - borderTopWidth, 0);\n    const c1 = innerTopLeftRadiusX * (1.0 - KAPPA);\n    const c2 = innerTopLeftRadiusY * (1.0 - KAPPA);\n    // Clip inner top right cap\n    ctx.bezierCurveTo(left + borderLeftWidth + c1, top + borderTopWidth, left + borderLeftWidth, top + borderTopWidth + c2, left + borderLeftWidth, top + Math.max(rtl, borderTopWidth));\n    // Clip inner left border edge\n    ctx.lineTo(left + borderLeftWidth, top + height - Math.max(rbl, borderBottomWidth));\n    // Ellipse coefficients inner bottom left cap\n    const innerBottomLeftRadiusX = Math.max(rbl - borderLeftWidth, 0);\n    const innerBottomLeftRadiusY = Math.max(rbl - borderBottomWidth, 0);\n    const c3 = innerBottomLeftRadiusX * (1.0 - KAPPA);\n    const c4 = innerBottomLeftRadiusY * (1.0 - KAPPA);\n    const bottomLeftXCoord = left + Math.max(rbl, borderLeftWidth);\n    // Clip inner top left cap\n    ctx.bezierCurveTo(left + borderLeftWidth, top + height - borderBottomWidth - c4, left + borderLeftWidth + c3, top + height - borderBottomWidth, bottomLeftXCoord, top + height - borderBottomWidth);\n    ctx.lineTo(bottomLeftXCoord, top + height);\n    // Move left in case the margin exceedes the radius\n    ctx.lineTo(left + rbl, top + height);\n    // Ellipse coefficients outer top right cap\n    const c5 = rbl * (1.0 - KAPPA);\n    // Clip outer top right cap\n    ctx.bezierCurveTo(left + c5, top + height, left, top + height - c5, left, top + height - rbl);\n    ctx.closePath();\n    ctx.clip();\n    // Clip border right cap joins\n    if (borderBottomWidth) {\n        const trSlope = -borderBottomWidth / borderLeftWidth;\n        ctx.moveTo(left + width / 2, trSlope * (width / 2) + top + height);\n        ctx.lineTo(left, top + height);\n        ctx.lineTo(left, top);\n        ctx.lineTo(left + width, top);\n        ctx.closePath();\n        ctx.clip();\n    }\n    if (borderBottomWidth) {\n        const trSlope = -borderTopWidth / borderLeftWidth;\n        ctx.moveTo(left + width / 2, trSlope * (-width / 2) + top);\n        ctx.lineTo(left, top);\n        ctx.lineTo(left, top + height);\n        ctx.lineTo(left + width, top + height);\n        ctx.closePath();\n        ctx.clip();\n    }\n};\nconst fillBorderLeft = (ctx, layout, style, rbl, rtl) => {\n    const { top, left, height } = layout;\n    const { borderLeftColor, borderLeftStyle, borderLeftWidth, borderTopWidth, borderBottomWidth, } = style;\n    const c0 = rbl * (1.0 - KAPPA);\n    const c1 = rtl * (1.0 - KAPPA);\n    ctx.moveTo(left + rbl, top + height);\n    ctx.bezierCurveTo(left + c0, top + height, left, top + height - c0, left, top + height - rbl);\n    ctx.lineTo(left, top + rtl);\n    ctx.bezierCurveTo(left, top + c1, left + c1, top, left + rtl, top);\n    ctx.strokeColor(borderLeftColor);\n    ctx.lineWidth(Math.max(borderLeftWidth, borderTopWidth, borderBottomWidth) * 2);\n    if (borderLeftStyle === 'dashed') {\n        ctx.dash(borderLeftWidth * 2, { space: borderLeftWidth * 1.2 });\n    }\n    else if (borderLeftStyle === 'dotted') {\n        ctx.dash(borderLeftWidth, { space: borderLeftWidth * 1.2 });\n    }\n    ctx.stroke();\n    ctx.undash();\n};\nconst shouldRenderBorders = (node) => node.box &&\n    (node.box.borderTopWidth ||\n        node.box.borderRightWidth ||\n        node.box.borderBottomWidth ||\n        node.box.borderLeftWidth);\nconst renderBorders = (ctx, node) => {\n    if (!node.box)\n        return;\n    if (!shouldRenderBorders(node))\n        return;\n    const { width, height, borderTopWidth = 0, borderLeftWidth = 0, borderRightWidth = 0, borderBottomWidth = 0, } = node.box;\n    const { opacity = 1, borderTopColor = 'black', borderTopStyle = 'solid', borderLeftColor = 'black', borderLeftStyle = 'solid', borderRightColor = 'black', borderRightStyle = 'solid', borderBottomColor = 'black', borderBottomStyle = 'solid', } = node.style;\n    // @ts-expect-error this is always a number due to resolve border radius step\n    const borderTopLeftRadius = node.style.borderTopLeftRadius || 0;\n    // @ts-expect-error this is always a number due to resolve border radius step\n    const borderTopRightRadius = node.style.borderTopRightRadius || 0;\n    // @ts-expect-error this is always a number due to resolve border radius step\n    const borderBottomLeftRadius = node.style.borderBottomLeftRadius || 0;\n    // @ts-expect-error this is always a number due to resolve border radius step\n    const borderBottomRightRadius = node.style.borderBottomRightRadius || 0;\n    const style = {\n        borderTopColor,\n        borderTopWidth,\n        borderTopStyle,\n        borderLeftColor,\n        borderLeftWidth,\n        borderLeftStyle,\n        borderRightColor,\n        borderRightWidth,\n        borderRightStyle,\n        borderBottomColor,\n        borderBottomWidth,\n        borderBottomStyle};\n    const rtr = Math.min(borderTopRightRadius, 0.5 * width, 0.5 * height);\n    const rtl = Math.min(borderTopLeftRadius, 0.5 * width, 0.5 * height);\n    const rbr = Math.min(borderBottomRightRadius, 0.5 * width, 0.5 * height);\n    const rbl = Math.min(borderBottomLeftRadius, 0.5 * width, 0.5 * height);\n    ctx.save();\n    ctx.strokeOpacity(opacity);\n    if (borderTopWidth) {\n        ctx.save();\n        clipBorderTop(ctx, node.box, style, rtr, rtl);\n        fillBorderTop(ctx, node.box, style, rtr, rtl);\n        ctx.restore();\n    }\n    if (borderRightWidth) {\n        ctx.save();\n        clipBorderRight(ctx, node.box, style, rtr, rbr);\n        fillBorderRight(ctx, node.box, style, rtr, rbr);\n        ctx.restore();\n    }\n    if (borderBottomWidth) {\n        ctx.save();\n        clipBorderBottom(ctx, node.box, style, rbl, rbr);\n        fillBorderBottom(ctx, node.box, style, rbl, rbr);\n        ctx.restore();\n    }\n    if (borderLeftWidth) {\n        ctx.save();\n        clipBorderLeft(ctx, node.box, style, rbl, rtl);\n        fillBorderLeft(ctx, node.box, style, rbl, rtl);\n        ctx.restore();\n    }\n    ctx.restore();\n};\n\nconst drawBackground = (ctx, node) => {\n    if (!node.box)\n        return;\n    const { top, left, width, height } = node.box;\n    const color = parseColor(node.style.backgroundColor);\n    const nodeOpacity = isNil(node.style?.opacity) ? 1 : node.style.opacity;\n    const opacity = Math.min(color.opacity, nodeOpacity);\n    ctx\n        .fillOpacity(opacity)\n        .fillColor(color.value)\n        .rect(left, top, width, height)\n        .fill();\n};\nconst renderBackground = (ctx, node) => {\n    const hasBackground = !!node.box && !!node.style?.backgroundColor;\n    if (hasBackground) {\n        ctx.save();\n        clipNode(ctx, node);\n        drawBackground(ctx, node);\n        ctx.restore();\n    }\n};\n\nconst isString = (value) => typeof value === 'string';\nconst isSrcId = (value) => /^#.+/.test(value);\nconst renderLink = (ctx, node, src) => {\n    if (!src || !node.box)\n        return;\n    const isId = isSrcId(src);\n    const method = isId ? 'goTo' : 'link';\n    const value = isId ? src.slice(1) : src;\n    const { top, left, width, height } = node.box;\n    ctx[method](left, top, width, height, value);\n};\nconst setLink = (ctx, node) => {\n    const props = node.props || {};\n    if ('src' in props && isString(props.src))\n        return renderLink(ctx, node, props.src);\n    if ('href' in props && isString(props.href))\n        return renderLink(ctx, node, props.href);\n};\n\nconst setDestination = (ctx, node) => {\n    if (!node.box)\n        return;\n    if (!node.props)\n        return;\n    if ('id' in node.props) {\n        ctx.addNamedDestination(node.props.id, 'XYZ', null, node.box.top, null);\n    }\n};\n\nconst clean = (options) => {\n    const opt = { ...options };\n    // We need to ensure the elements are no present if not true\n    Object.entries(opt).forEach((pair) => {\n        if (!pair[1]) {\n            delete opt[pair[0]];\n        }\n    });\n    return opt;\n};\nconst parseCommonFormOptions = (node) => {\n    // Common Options\n    return {\n        required: node.props?.required || false,\n        noExport: node.props?.noExport || false,\n        readOnly: node.props?.readOnly || false,\n        value: node.props?.value || undefined,\n        defaultValue: node.props?.defaultValue || undefined,\n    };\n};\nconst parseTextInputOptions = (node, fieldSet) => {\n    return clean({\n        ...parseCommonFormOptions(node),\n        parent: fieldSet || undefined,\n        align: node.props?.align || 'left',\n        multiline: node.props?.multiline || undefined,\n        password: node.props?.password || false,\n        noSpell: node.props?.noSpell || false,\n        format: node.props?.format || undefined,\n        fontSize: node.props?.fontSize || undefined,\n        MaxLen: node.props?.maxLength || undefined,\n    });\n};\nconst parseSelectAndListFieldOptions = (node) => {\n    return clean({\n        ...parseCommonFormOptions(node),\n        sort: node.props?.sort || false,\n        edit: node.props?.edit || false,\n        multiSelect: node.props?.multiSelect || false,\n        noSpell: node.props?.noSpell || false,\n        select: node.props?.select || [''],\n    });\n};\nconst getAppearance = (ctx, codepoint, width, height) => {\n    const appearance = ctx.ref({\n        Type: 'XObject',\n        Subtype: 'Form',\n        BBox: [0, 0, width, height],\n        Resources: {\n            ProcSet: ['PDF', 'Text', 'ImageB', 'ImageC', 'ImageI'],\n            Font: {\n                ZaDi: ctx._acroform.fonts.ZaDi,\n            },\n        },\n    });\n    appearance.initDeflate();\n    appearance.write(`/Tx BMC\\nq\\n/ZaDi ${height * 0.8} Tf\\nBT\\n${width * 0.45} ${height / 4} Td (${codepoint}) Tj\\nET\\nQ\\nEMC`);\n    appearance.end(null);\n    return appearance;\n};\nconst parseCheckboxOptions = (ctx, node, fieldSet) => {\n    const { width, height } = node.box || {};\n    const onOption = node.props?.onState || 'Yes';\n    const offOption = node.props?.offState || 'Off';\n    const xMark = node.props?.xMark || false;\n    if (!Object.prototype.hasOwnProperty.call(ctx._acroform.fonts, 'ZaDi')) {\n        const ref = ctx.ref({\n            Type: 'Font',\n            Subtype: 'Type1',\n            BaseFont: 'ZapfDingbats',\n        });\n        ctx._acroform.fonts.ZaDi = ref;\n        ref.end(null);\n    }\n    const normalAppearance = {\n        [onOption]: getAppearance(ctx, xMark ? '8' : '4', width, height),\n        [offOption]: getAppearance(ctx, xMark ? ' ' : '8', width, height),\n    };\n    return clean({\n        ...parseCommonFormOptions(node),\n        backgroundColor: node.props?.backgroundColor || undefined,\n        borderColor: node.props?.borderColor || undefined,\n        parent: fieldSet || undefined,\n        value: `/${node.props?.checked === true ? onOption : offOption}`,\n        defaultValue: `/${node.props?.checked === true ? onOption : offOption}`,\n        AS: node.props?.checked === true ? onOption : offOption,\n        AP: { N: normalAppearance, D: normalAppearance },\n    });\n};\n\nconst renderTextInput = (ctx, node, options) => {\n    if (!node.box)\n        return;\n    const { top, left, width, height } = node.box;\n    // Element's name\n    const name = node.props?.name || '';\n    const fieldSetOptions = options.fieldSets?.at(0);\n    if (!ctx._root.data.AcroForm) {\n        ctx.initForm();\n    }\n    ctx.formText(name, left, top, width, height, parseTextInputOptions(node, fieldSetOptions));\n};\n\nconst renderSelect = (ctx, node) => {\n    if (!node.box)\n        return;\n    const { top, left, width, height } = node.box;\n    // Element's name\n    const name = node.props?.name || '';\n    if (!ctx._root.data.AcroForm) {\n        ctx.initForm();\n    }\n    ctx.formCombo(name, left, top, width, height, parseSelectAndListFieldOptions(node));\n};\n\nconst renderFieldSet = (ctx, node, options) => {\n    const name = node.props?.name || '';\n    if (!ctx._root.data.AcroForm) {\n        ctx.initForm();\n    }\n    const formField = ctx.formField(name);\n    const option = options;\n    if (!option.fieldSets) {\n        option.fieldSets = [formField];\n    }\n    else {\n        option.fieldSets.push(formField);\n    }\n};\nconst cleanUpFieldSet = (_ctx, _node, options) => {\n    options.fieldSets.pop();\n};\n\nconst renderList = (ctx, node) => {\n    if (!node.box)\n        return;\n    const { top, left, width, height } = node.box || {};\n    // Element's name\n    const name = ('name' in node.props ? node.props.name || '' : '');\n    if (!ctx._root.data.AcroForm) {\n        ctx.initForm();\n    }\n    ctx.formList(name, left, top, width, height, parseSelectAndListFieldOptions(node));\n};\n\nconst renderCheckbox = (ctx, node, options) => {\n    if (!node.box)\n        return;\n    const { top, left, width, height } = node.box;\n    // Element's name\n    const name = node.props?.name || '';\n    const fieldSetOptions = options.fieldSets?.at(0);\n    if (!ctx._root.data.AcroForm) {\n        ctx.initForm();\n    }\n    ctx.formCheckbox(name, left, top, width, height, parseCheckboxOptions(ctx, node, fieldSetOptions));\n};\n\nconst isRecursiveNode = (node) => node.type !== P.Text && node.type !== P.Svg;\nconst renderChildren = (ctx, node, options) => {\n    ctx.save();\n    if (node.box) {\n        ctx.translate(node.box.left, node.box.top);\n    }\n    const children = node.children || [];\n    const renderChild = (child) => renderNode(ctx, child, options);\n    children.forEach(renderChild);\n    ctx.restore();\n};\nconst renderFns = {\n    [P.Text]: renderText,\n    [P.Note]: renderNote,\n    [P.Image]: renderImage,\n    [P.FieldSet]: renderFieldSet,\n    [P.TextInput]: renderTextInput,\n    [P.Select]: renderSelect,\n    [P.Checkbox]: renderCheckbox,\n    [P.List]: renderList,\n    [P.Canvas]: renderCanvas,\n    [P.Svg]: renderSvg,\n    [P.Link]: setLink,\n};\nconst cleanUpFns = {\n    [P.FieldSet]: cleanUpFieldSet,\n};\nconst renderNode = (ctx, node, options) => {\n    const overflowHidden = node.style?.overflow === 'hidden';\n    const shouldRenderChildren = isRecursiveNode(node);\n    if (node.type === P.Page)\n        renderPage(ctx, node);\n    ctx.save();\n    if (overflowHidden)\n        clipNode(ctx, node);\n    applyTransformations(ctx, node);\n    renderBackground(ctx, node);\n    renderBorders(ctx, node);\n    const renderFn = renderFns[node.type];\n    if (renderFn)\n        renderFn(ctx, node, options);\n    if (shouldRenderChildren)\n        renderChildren(ctx, node, options);\n    const cleanUpFn = cleanUpFns[node.type];\n    if (cleanUpFn)\n        cleanUpFn(ctx, node, options);\n    setDestination(ctx, node);\n    renderDebug(ctx, node);\n    ctx.restore();\n};\n\nconst addNodeBookmark = (ctx, node, pageNumber, registry) => {\n    if (!node.box)\n        return;\n    if (!node.props)\n        return;\n    if ('bookmark' in node.props && node.props.bookmark) {\n        const bookmark = node.props.bookmark;\n        const { title, parent, expanded, zoom, fit } = bookmark;\n        const outline = registry[parent] || ctx.outline;\n        const top = bookmark.top || node.box.top;\n        const left = bookmark.left || node.box.left;\n        const instance = outline.addItem(title, {\n            pageNumber,\n            expanded,\n            top,\n            left,\n            zoom,\n            fit,\n        });\n        registry[bookmark.ref] = instance;\n    }\n    if (!node.children)\n        return;\n    node.children.forEach((child) => addNodeBookmark(ctx, child, pageNumber, registry));\n};\nconst addBookmarks = (ctx, root) => {\n    const registry = {};\n    const pages = root.children || [];\n    pages.forEach((page, i) => {\n        addNodeBookmark(ctx, page, i, registry);\n    });\n};\n\nconst render = (ctx, doc) => {\n    const pages = doc.children || [];\n    const options = { imageCache: new Map(), fieldSets: [] };\n    pages.forEach((page) => renderNode(ctx, page, options));\n    addBookmarks(ctx, doc);\n    ctx.end();\n    return ctx;\n};\n\nexport { render as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,aAAa,CAAC,KAAK;IACrB,MAAM,IAAI,KAAK,KAAK,EAAE;IACtB,IAAI,GACA,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC;AAC7B;AAEA,MAAM,UAAU,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG;AACjD,MAAM,aAAa,CAAC,KAAK;IACrB,MAAM,IAAI,KAAK,KAAK,EAAE,KAAK;IAC3B,MAAM,IAAI,KAAK,KAAK,EAAE,KAAK;IAC3B,MAAM,KAAK,KAAK,KAAK,EAAE,MAAM;IAC7B,MAAM,KAAK,KAAK,KAAK,EAAE,MAAM;IAC7B,MAAM,QAAQ,KAAK,KAAK,EAAE,SAAS;IACnC,MAAM,SAAS,KAAK,KAAK,EAAE,UAAU;IACrC,IAAI,CAAC,SAAS,CAAC,QACX;IACJ,IAAI,MAAM,IAAI;QACV,MAAM,MAAM,KAAK;QACjB,MAAM,MAAM,KAAK;QACjB,IAAI,MAAM,CAAC,IAAI,IAAI;QACnB,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO;QAC3B,IAAI,aAAa,CAAC,IAAI,KAAK,QAAQ,KAAK,GAAG,IAAI,OAAO,IAAI,KAAK,KAAK,IAAI,OAAO,IAAI;QACnF,IAAI,MAAM,CAAC,IAAI,OAAO,IAAI,SAAS;QACnC,IAAI,aAAa,CAAC,IAAI,OAAO,IAAI,SAAS,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,IAAI,QAAQ,IAAI,KAAK,OAAO,IAAI;QAC1G,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI;QACvB,IAAI,aAAa,CAAC,IAAI,KAAK,KAAK,IAAI,QAAQ,GAAG,IAAI,SAAS,KAAK,KAAK,GAAG,IAAI,SAAS;QACtF,IAAI,MAAM,CAAC,GAAG,IAAI;QAClB,IAAI,aAAa,CAAC,GAAG,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,IAAI,IAAI;IAChE,OACK;QACD,IAAI,MAAM,CAAC,GAAG;QACd,IAAI,MAAM,CAAC,IAAI,OAAO;QACtB,IAAI,MAAM,CAAC,IAAI,OAAO,IAAI;QAC1B,IAAI,MAAM,CAAC,GAAG,IAAI;IACtB;IACA,IAAI,SAAS;AACjB;AAEA,MAAM,eAAe,CAAC,KAAK;IACvB,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,KAAK,IAAI,CAAC;IAC1C,IAAI,MAAM,CAAC,IAAI;IACf,IAAI,MAAM,CAAC,IAAI;AACnB;AAEA,MAAM,cAAc;AAChB,OAAO;AACX;AAEA,MAAM,UAAU,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG;AACjD,MAAM,cAAc,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,KAAK,CAAC;IAC5C,MAAM,IAAI,KAAK;IACf,MAAM,IAAI,KAAK;IACf,MAAM,KAAK,KAAK;IAChB,MAAM,KAAK,KAAK;IAChB,MAAM,KAAK,IAAI,KAAK;IACpB,MAAM,KAAK,IAAI,KAAK;IACpB,MAAM,KAAK,IAAI;IACf,MAAM,KAAK,IAAI;IACf,IAAI,MAAM,CAAC,GAAG;IACd,IAAI,aAAa,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI;IAC9C,IAAI,aAAa,CAAC,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI;IAC/C,IAAI,aAAa,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI;IAChD,IAAI,aAAa,CAAC,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,GAAG;IAC9C,IAAI,SAAS;AACjB;AACA,MAAM,gBAAgB,CAAC,KAAK;IACxB,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,KAAK,KAAK,IAAI,CAAC;IAC1C,YAAY,KAAK,IAAI,IAAI,IAAI;AACjC;AAEA,MAAM,eAAe,CAAC,KAAK;IACvB,MAAM,KAAK,KAAK,KAAK,EAAE;IACvB,MAAM,KAAK,KAAK,KAAK,EAAE;IACvB,MAAM,IAAI,KAAK,KAAK,EAAE;IACtB,YAAY,KAAK,GAAG,GAAG,IAAI;AAC/B;AAEA,mCAAmC,GACnC,MAAM,SAAS,CAAC;IACZ,IAAI,IAAI,CAAC,QAAQ,IAAI,MAAM;QACvB,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO;IACjC;IACA,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,GAAG;AAC9C;AACA,MAAM,gBAAgB,CAAC,KAAK,SAAS,WAAW,GAAG;IAC/C,MAAM,WAAW,EAAE;IACnB,MAAM,QAAQ,IAAI,SAAS,GAAG;IAC9B,IAAI;IACJ,IAAI,OAAO;IACX,IAAI,YAAY;IAChB,IAAI,IAAI;IACR,yBAAyB;IACzB,IAAI,SAAS,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,MAAM;IAC7C,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG;IACtB,wCAAwC;IACxC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,MAAM;QACtC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG;IAChD;IACA,wBAAwB;IACxB,IAAI,UAAU,CAAC;IACf,gBAAgB;IAChB,IAAI,UAAU,CAAC,CAAC,QAAQ,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,GAAG,CAAC;IACrD,qBAAqB;IACrB,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,IAAI,SAAS,EAAE,GAAG,CAAC;IAC7D,kDAAkD;IAClD,MAAM,aAAa,CAAC;QAChB,IAAI,OAAO,KAAK;YACZ,MAAM,MAAM,QAAQ,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC;YAC1C,MAAM,UAAU,SAAS,CAAC,MAAM,EAAE,CAAC,QAAQ,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,YAAY;YAC7E,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,UAAU;QAChD;QACA,OAAQ,OAAO;IACnB;IACA,uDAAuD;IACvD,MAAM,QAAQ,CAAC;QACX,WAAW;QACX,IAAI,SAAS,MAAM,GAAG,GAAG;YACrB,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,SAAS,IAAI,CAAC,KAAK,IAAI,CAAC;YAC3C,OAAQ,SAAS,MAAM,GAAG;QAC9B;IACJ;IACA,IAAK,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,KAAK,EAAG;QACtC,8EAA8E;QAC9E,oCAAoC;QACpC,MAAM,MAAM,SAAS,CAAC,EAAE;QACxB,IAAI,IAAI,OAAO,IAAI,IAAI,OAAO,EAAE;YAC5B,2BAA2B;YAC3B,MAAM;YACN,8DAA8D;YAC9D,IAAI,UAAU,CAAC,CAAC,QAAQ,EAAE,OAAO,IAAI,IAAI,OAAO,GAAG,OAAO,CAAC,EAAE,OAAO,IAAI,IAAI,OAAO,GAAG,OAAO,GAAG,CAAC;YACjG,MAAM,IAAI;YACV,YAAY;QAChB,OACK;YACD,+DAA+D;YAC/D,IAAI,WAAW;gBACX,IAAI,UAAU,CAAC,CAAC,QAAQ,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,GAAG,CAAC;gBACrD,YAAY;YAChB;YACA,yDAAyD;YACzD,IAAI,IAAI,QAAQ,GAAG,IAAI,YAAY,KAAK,GAAG;gBACvC,WAAW,IAAI;YACnB;QACJ;QACA,KAAK,IAAI,QAAQ,GAAG;IACxB;IACA,+BAA+B;IAC/B,MAAM;IACN,sBAAsB;IACtB,IAAI,UAAU,CAAC;IACf,oCAAoC;IACpC,OAAO,IAAI,OAAO;AACtB;AACA,MAAM,eAAe,CAAC,KAAK,QAAQ,WAAW,GAAG;IAC7C,MAAM,QAAQ,OAAO,IAAI,SAAS;IAClC,MAAM,aAAa,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,IAAI;IAChD,MAAM,oBAAoB,OAAO;IACjC,iCAAiC;IACjC,MAAM,gBAAgB,IAAI,KAAK,CAAC,YAAY,CAAC;IAC7C,MAAM,mBAAmB,UAAU,GAAG,CAAC,CAAC,KAAK,IAAM,CAAC;YAChD,UAAU,IAAI,QAAQ,GAAG;YACzB,UAAU,IAAI,QAAQ,GAAG;YACzB,SAAS,IAAI,OAAO;YACpB,SAAS,IAAI,OAAO;YACpB,cAAc,MAAM,CAAC,EAAE,CAAC,YAAY,GAAG;QAC3C,CAAC;IACD,OAAO,cAAc,KAAK,eAAe,kBAAkB,GAAG;AAClE;AAEA,MAAM,cAAc,CAAC,KAAK;IACtB,IAAI,CAAC,IAAI,MAAM,EACX;IACJ,IAAI,CAAC,IAAI,SAAS,EACd;IACJ,MAAM,kBAAkB,IAAI,QAAQ;IACpC,MAAM,OAAO,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE;IACrC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,UAAU;IACnD,IAAI,OACA,IAAI,SAAS,CAAC;IAClB,IAAI,WAAW,CAAC;IAChB,IAAI,MAAM;QACN,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,aAAa,KAAK,QAAQ,GAAG,MAAM;IAC9D;IACA,IAAI;QACA,aAAa,KAAK,IAAI,MAAM,EAAE,IAAI,SAAS,EAAE,GAAG;IACpD,EACA,OAAO,OAAO;QACV,QAAQ,GAAG,CAAC;IAChB;IACA,IAAI,SAAS,CAAC,iBAAiB;AACnC;AACA,MAAM,aAAa,CAAC,KAAK,MAAM,YAAY;IACvC,IAAI,IAAI;IACR,MAAM,IAAI,KAAK,GAAG,EAAE,KAAK;IACzB,MAAM,IAAI,KAAK,GAAG,EAAE,KAAK;IACzB,MAAM,OAAO,KAAK,IAAI,CAAC,EAAE,EAAE,WAAW,MAAM,CAAC,EAAE;IAC/C,MAAM,QAAQ,KAAK,IAAI,CAAC,EAAE,EAAE,YAAY,SAAS;IACjD,MAAM,QAAQ,KAAK,QAAQ;IAC3B,IAAI,CAAC,MACD;IACJ,MAAM,SAAS,KAAK,MAAM,GAAG;IAC7B,MAAM,UAAU,KAAK,OAAO,GAAG;IAC/B,MAAM,UAAU,KAAK,OAAO,GAAG;IAC/B,MAAM,YAAY,KAAK,SAAS,GAAG;IACnC,IAAI,aAAa;IACjB,IAAI,aAAa;IACjB,OAAQ;QACJ,KAAK;YACD,aAAa,IAAI,QAAQ;YACzB;QACJ,KAAK;YACD,aAAa,IAAI;YACjB;QACJ;YACI,aAAa;YACb;IACR;IACA,OAAQ;QACJ,KAAK;QACL,KAAK;YACD,aAAa,IAAI,YAAY;YAC7B;QACJ,KAAK;YACD,aAAa,IAAI;YACjB;QACJ,KAAK;YACD,aAAa,IAAI;YACjB;QACJ,KAAK;YACD,aAAa,IAAI;YACjB;QACJ,KAAK;YACD,aAAa,IAAI;YACjB;QACJ;YACI,aAAa;YACb;IACR;IACA,IAAI,SAAS,CAAC,YAAY;IAC1B,KAAK,IAAI,CAAC,OAAO,CAAC,CAAC,MAAQ,YAAY,KAAK;IAC5C,IAAI,OAAO;AACf;AACA,MAAM,gBAAgB,CAAC,KAAK;IACxB,MAAM,WAAW,KAAK,QAAQ;IAC9B,SAAS,OAAO,CAAC,CAAC,OAAS,WAAW,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,UAAU,EAAE,KAAK,KAAK,CAAC,gBAAgB;AAChH;AAEA,MAAM,QAAQ,CAAC;IACX,MAAM,SAAS,EAAE;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACvC,OAAO,IAAI,CAAC;YAAC,MAAM,CAAC,EAAE;YAAE,MAAM,CAAC,IAAI,EAAE;SAAC;IAC1C;IACA,OAAO;AACX;AACA;;;;;CAKC,GACD,MAAM,cAAc,CAAC;IACjB,IAAI,SAAS,CAAC,UAAU,EAAE,EACrB,IAAI,GACJ,OAAO,CAAC,MAAM,KACd,OAAO,CAAC,cAAc,UACtB,KAAK,CAAC;IACX,IAAI,OAAO,MAAM,GAAG,MAAM,GAAG;QACzB,SAAS,OAAO,KAAK,CAAC,GAAG,CAAC;IAC9B;IACA,MAAM,eAAe,OAAO,GAAG,CAAC;IAChC,OAAO,MAAM;AACjB;AAEA,MAAM,eAAe,CAAC,KAAK;IACvB,IAAI,OAAO,MAAM,GAAG,GAAG;QACnB,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE;QACrC,OAAO,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC,IAAM,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;IACxD;AACJ;AACA,MAAM,iBAAiB,CAAC,KAAK;IACzB,MAAM,SAAS,YAAY,KAAK,KAAK,CAAC,MAAM,IAAI;IAChD,aAAa,KAAK;AACtB;AAEA,MAAM,gBAAgB,CAAC,KAAK;IACxB,MAAM,SAAS,YAAY,KAAK,KAAK,CAAC,MAAM,IAAI;IAChD,aAAa,KAAK;IAClB,IAAI,SAAS;AACjB;AAEA,MAAM,gBAAgB,CAAC,KAAK;IACxB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,IAAI,CAAC,KAAK,KAAK,EAAE,MACb;IACJ,MAAM,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,KAAK,KAAK;IACnC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,KAAK;IAC7C,MAAM,aAAa,KAAK,GAAG,CAAC,WAAW,IAAI;IAC3C,MAAM,cAAc,KAAK,GAAG,CAAC,WAAW,IAAI;IAC5C,IAAI,UAAU,KAAK,WAAW,GAAG;QAC7B,QAAQ,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC,mCAAmC,CAAC;QACpF;IACJ;IACA,IAAI,OAAO,UAAU,YAAY,OAAO,WAAW,UAAU;QACzD,QAAQ,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC,2CAA2C,CAAC;QAC5F;IACJ;IACA,IAAI,IAAI;IACR,IACK,WAAW,CAAC,WAAW,GACvB,KAAK,CAAC,KAAK,KAAK,CAAC,IAAI,EAAE,IAAI,aAAa,IAAI,YAAY;QACzD;QACA;IACJ;IACA,IAAI,OAAO;AACf;AAEA,uEAAuE;AACvE,gBAAgB;AAChB,MAAM,UAAU,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG;AACjD,MAAM,WAAW,CAAC,KAAK;IACnB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG;IAC7C,MAAM,EAAE,sBAAsB,CAAC,EAAE,uBAAuB,CAAC,EAAE,0BAA0B,CAAC,EAAE,yBAAyB,CAAC,EAAG,GAAG,KAAK,KAAK;IAClI,aAAa;IACb,6EAA6E;IAC7E,MAAM,MAAM,KAAK,GAAG,CAAC,sBAAsB,MAAM,OAAO,MAAM;IAC9D,MAAM,MAAM,MAAM,CAAC,MAAM,OAAO;IAChC,IAAI,MAAM,CAAC,OAAO,KAAK;IACvB,IAAI,MAAM,CAAC,OAAO,QAAQ,KAAK;IAC/B,IAAI,aAAa,CAAC,OAAO,QAAQ,KAAK,KAAK,OAAO,OAAO,MAAM,KAAK,OAAO,OAAO,MAAM;IACxF,eAAe;IACf,6EAA6E;IAC7E,MAAM,MAAM,KAAK,GAAG,CAAC,yBAAyB,MAAM,OAAO,MAAM;IACjE,MAAM,MAAM,MAAM,CAAC,MAAM,OAAO;IAChC,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM,SAAS;IACxC,IAAI,aAAa,CAAC,OAAO,OAAO,MAAM,SAAS,KAAK,OAAO,QAAQ,KAAK,MAAM,QAAQ,OAAO,QAAQ,KAAK,MAAM;IAChH,gBAAgB;IAChB,6EAA6E;IAC7E,MAAM,MAAM,KAAK,GAAG,CAAC,wBAAwB,MAAM,OAAO,MAAM;IAChE,MAAM,MAAM,MAAM,CAAC,MAAM,OAAO;IAChC,IAAI,MAAM,CAAC,OAAO,KAAK,MAAM;IAC7B,IAAI,aAAa,CAAC,OAAO,KAAK,MAAM,QAAQ,MAAM,MAAM,SAAS,KAAK,MAAM,MAAM,SAAS;IAC3F,cAAc;IACd,6EAA6E;IAC7E,MAAM,MAAM,KAAK,GAAG,CAAC,qBAAqB,MAAM,OAAO,MAAM;IAC7D,MAAM,MAAM,MAAM,CAAC,MAAM,OAAO;IAChC,IAAI,MAAM,CAAC,MAAM,MAAM;IACvB,IAAI,aAAa,CAAC,MAAM,MAAM,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK;IAChE,IAAI,SAAS;IACb,IAAI,IAAI;AACZ;AAEA,MAAM,4BAA4B,CAAC,KAAK,WAAW;IAC/C,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;IAC7B,OAAQ;QACJ,KAAK;YAAS;gBACV,MAAM,CAAC,QAAQ,OAAO,GAAG;gBACzB,IAAI,KAAK,CAAC,QAAQ,QAAQ;oBAAE;gBAAO;gBACnC;YACJ;QACA,KAAK;YAAU;gBACX,MAAM,CAAC,MAAM,GAAG;gBAChB,IAAI,MAAM,CAAC,OAAO;oBAAE;gBAAO;gBAC3B;YACJ;QACA,KAAK;YAAa;gBACd,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG;gBACnB,IAAI,SAAS,CAAC,GAAG,GAAG;oBAAE;gBAAO;gBAC7B;YACJ;QACA,KAAK;YAAQ;gBACT,MAAM,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG;gBACjC,MAAM,OAAO,AAAC,SAAS,KAAK,EAAE,GAAI;gBAClC,MAAM,OAAO,AAAC,SAAS,KAAK,EAAE,GAAI;gBAClC,MAAM,OAAO,KAAK,GAAG,CAAC;gBACtB,MAAM,OAAO,KAAK,GAAG,CAAC;gBACtB,IAAI,IAAI;gBACR,IAAI,IAAI;gBACR,IAAI,UAAU,MAAM;oBAChB,CAAC,GAAG,EAAE,GAAG,MAAM,IAAI,CAAC;oBACpB,MAAM,KAAK,IAAI,OAAO;oBACtB,MAAM,KAAK,IAAI,OAAO;oBACtB,KAAK;oBACL,KAAK;gBACT;gBACA,IAAI,SAAS,CAAC,GAAG,MAAM,MAAM,GAAG,GAAG;gBACnC;YACJ;QACA,KAAK;YAAU;gBACX,IAAI,SAAS,IAAI;gBACjB;YACJ;QACA;YAAS;gBACL,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,UAAU,mBAAmB,CAAC;YACzE;IACJ;AACJ;AACA,MAAM,uBAAuB,CAAC,KAAK;IAC/B,IAAI,CAAC,KAAK,MAAM,EACZ;IACJ,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG;IACzB,MAAM,SAAS;QAAC,KAAK,MAAM,CAAC,IAAI;QAAE,KAAK,MAAM,CAAC,GAAG;KAAC;IAClD,MAAM,iBAAiB,eAAe,QAAQ,MAAM,SAAS,GAAG;IAChE,MAAM,aAAa,OAAO,aAAa,kBAAkB,EAAE;IAC3D,WAAW,OAAO,CAAC,CAAC;QAChB,0BAA0B,KAAK,WAAW;IAC9C;AACJ;AAEA,kEAAkE;AAClE,MAAM,qBAAqB,CAAC;IACxB,MAAM,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAa,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,gJAAA,CAAA,UAAS,AAAD,EAAE,KAAK,KAAK,EAAE,KAAK;IAC9D,IAAI,CAAC,KAAK,MAAM,EACZ,OAAO;QAAC;QAAG;QAAG;QAAG;KAAE;IACvB,MAAM,SAAS;QAAC;QAAU;QAAU,CAAC;QAAU,CAAC;KAAS;IACzD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,GAAG,KAAK,EAAG;QAC5C,MAAM,SAAS,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;QAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;YACvC,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,EAAE,EACzB,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE;YAC7B,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,EAAE,EACzB,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE;YAC7B,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,EAAE,EACzB,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE;YAC7B,IAAI,MAAM,CAAC,IAAI,EAAE,GAAG,MAAM,CAAC,EAAE,EACzB,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE;QACjC;IACJ;IACA,OAAO;AACX;AACA,MAAM,uBAAuB,CAAC;IAC1B,MAAM,IAAI,KAAK,KAAK,EAAE,KAAK;IAC3B,MAAM,KAAK,KAAK,KAAK,EAAE,MAAM;IAC7B,MAAM,KAAK,KAAK,KAAK,EAAE,MAAM;IAC7B,OAAO;QAAC,KAAK;QAAG,KAAK;QAAG,KAAK;QAAG,KAAK;KAAE;AAC3C;AACA,MAAM,wBAAwB,CAAC;IAC3B,MAAM,KAAK,KAAK,KAAK,EAAE,MAAM;IAC7B,MAAM,KAAK,KAAK,KAAK,EAAE,MAAM;IAC7B,MAAM,KAAK,KAAK,KAAK,EAAE,MAAM;IAC7B,MAAM,KAAK,KAAK,KAAK,EAAE,MAAM;IAC7B,OAAO;QAAC,KAAK;QAAI,KAAK;QAAI,KAAK;QAAI,KAAK;KAAG;AAC/C;AACA,MAAM,qBAAqB,CAAC;IACxB,MAAM,KAAK,KAAK,KAAK,EAAE,MAAM;IAC7B,MAAM,KAAK,KAAK,KAAK,EAAE,MAAM;IAC7B,MAAM,KAAK,KAAK,KAAK,EAAE,MAAM;IAC7B,MAAM,KAAK,KAAK,KAAK,EAAE,MAAM;IAC7B,OAAO;QACH,KAAK,GAAG,CAAC,IAAI;QACb,KAAK,GAAG,CAAC,IAAI;QACb,KAAK,GAAG,CAAC,IAAI;QACb,KAAK,GAAG,CAAC,IAAI;KAChB;AACL;AACA,MAAM,qBAAqB,CAAC;IACxB,MAAM,IAAI,KAAK,KAAK,EAAE,KAAK;IAC3B,MAAM,IAAI,KAAK,KAAK,EAAE,KAAK;IAC3B,MAAM,QAAQ,KAAK,KAAK,EAAE,SAAS;IACnC,MAAM,SAAS,KAAK,KAAK,EAAE,UAAU;IACrC,OAAO;QAAC;QAAG;QAAG,IAAI;QAAO,IAAI;KAAO;AACxC;AACA,MAAM,MAAM,CAAC,SAAW,KAAK,GAAG,CAAC,CAAC,aAAa;AAC/C,MAAM,MAAM,CAAC,SAAW,KAAK,GAAG,CAAC,aAAa;AAC9C,MAAM,yBAAyB,CAAC;IAC5B,MAAM,SAAS,YAAY,KAAK,KAAK,EAAE;IACvC,MAAM,UAAU,OAAO,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE;IACtC,MAAM,UAAU,OAAO,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE;IACtC,OAAO;QAAC,IAAI;QAAU,IAAI;QAAU,IAAI;QAAU,IAAI;KAAS;AACnE;AACA,MAAM,iBAAiB;IACnB,CAAC,+JAAA,CAAA,OAAM,CAAC,EAAE;IACV,CAAC,+JAAA,CAAA,OAAM,CAAC,EAAE;IACV,CAAC,+JAAA,CAAA,OAAM,CAAC,EAAE;IACV,CAAC,+JAAA,CAAA,SAAQ,CAAC,EAAE;IACZ,CAAC,+JAAA,CAAA,UAAS,CAAC,EAAE;IACb,CAAC,+JAAA,CAAA,UAAS,CAAC,EAAE;IACb,CAAC,+JAAA,CAAA,WAAU,CAAC,EAAE;AAClB;AACA,MAAM,iBAAiB,CAAC;IACpB,MAAM,gBAAgB,cAAc,CAAC,KAAK,IAAI,CAAC;IAC/C,OAAO,gBAAgB,cAAc,QAAQ;QAAC;QAAG;QAAG;QAAG;KAAE;AAC7D;AAEA,MAAM,iBAAiB,CAAC,KAAK;IACzB,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,CAAC,CAAC,iBAAiB,KAAK,KAAK,GAC7B;IACJ,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW;IACxC,IAAI,WACA,IAAI,SAAS,CAAC;AACtB;AACA,MAAM,iBAAiB,CAAC,KAAK;IACzB,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,CAAC,CAAC,YAAY,KAAK,KAAK,GACxB;IACJ,MAAM,cAAc,KAAK,KAAK,CAAC,MAAM;IACrC,IAAI,aACA,IAAI,WAAW,CAAC;AACxB;AACA,MAAM,aAAa,CAAC,KAAK;IACrB,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,CAAC,CAAC,aAAa,KAAK,KAAK,GACzB;IACJ,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;IAClC,IAAI,CAAC,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,UACP,IAAI,OAAO,CAAC;AACpB;AACA,MAAM,iBAAiB,CAAC,KAAK;IACzB,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,CAAC,CAAC,iBAAiB,KAAK,KAAK,GAC7B;IACJ,MAAM,cAAc,KAAK,KAAK,CAAC,WAAW,IAAI;IAC9C,IAAI,CAAC,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,cACP,IAAI,WAAW,CAAC;AACxB;AACA,MAAM,mBAAmB,CAAC,KAAK;IAC3B,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,CAAC,CAAC,mBAAmB,KAAK,KAAK,GAC/B;IACJ,MAAM,gBAAgB,KAAK,KAAK,EAAE;IAClC,IAAI,CAAC,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,gBACP,IAAI,aAAa,CAAC;AAC1B;AACA,MAAM,cAAc,CAAC,KAAK;IACtB,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,CAAC,CAAC,oBAAoB,KAAK,KAAK,GAChC;IACJ,MAAM,WAAW,KAAK,KAAK,CAAC,cAAc;IAC1C,IAAI,UACA,IAAI,QAAQ,CAAC;AACrB;AACA,MAAM,aAAa,CAAC,KAAK;IACrB,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,CAAC,CAAC,mBAAmB,KAAK,KAAK,GAC/B;IACJ,MAAM,UAAU,KAAK,KAAK,EAAE;IAC5B,IAAI,SACA,IAAI,OAAO,CAAC;AACpB;AACA,MAAM,cAAc,CAAC,KAAK;IACtB,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,CAAC,CAAC,qBAAqB,KAAK,KAAK,GACjC;IACJ,MAAM,QAAQ,KAAK,KAAK,EAAE,mBAAmB;IAC7C,gDAAgD;IAChD,IAAI,OACA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,UAAU,GAAG,CAAC;AAC3C;AACA,MAAM,wBAAwB,CAAC;IAC3B,IAAI,CAAC,KAAK,KAAK,EACX,OAAO;IACX,IAAI,CAAC,CAAC,UAAU,KAAK,KAAK,GACtB,OAAO;IACX,IAAI,OAAO,KAAK,KAAK,CAAC,IAAI,KAAK,UAC3B,OAAO;IACX,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE,SAAS,+JAAA,CAAA,iBAAgB;AACrD;AACA,MAAM,wBAAwB,CAAC;IAC3B,IAAI,CAAC,KAAK,KAAK,EACX,OAAO;IACX,IAAI,CAAC,CAAC,UAAU,KAAK,KAAK,GACtB,OAAO;IACX,IAAI,OAAO,KAAK,KAAK,CAAC,IAAI,KAAK,UAC3B,OAAO;IACX,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE,SAAS,+JAAA,CAAA,iBAAgB;AACrD;AACA,SAAS,iBAAiB,EAAE,EAAE,EAAE;IAC5B,MAAM,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;IACvC,MAAM,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;IACvC,MAAM,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;IACvC,MAAM,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;IACvC,MAAM,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;IAC/C,MAAM,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;IAC/C,OAAO;QAAC;QAAG;QAAG;QAAG;QAAG;QAAG;KAAE;AAC7B;AACA,MAAM,oBAAoB,CAAC,MAAM,YAAY,MAAM;IAC/C,MAAM,WAAW,WAAW,GAAG,CAAC,CAAC;QAC7B,OAAQ,UAAU,SAAS;YACvB,KAAK;gBAAS;oBACV,MAAM,QAAQ,UAAU,KAAK;oBAC7B,OAAO;wBAAC,KAAK,CAAC,EAAE;wBAAE;wBAAG;wBAAG,KAAK,CAAC,EAAE;wBAAE;wBAAG;qBAAE;gBAC3C;YACA,KAAK;gBAAa;oBACd,MAAM,QAAQ,UAAU,KAAK;oBAC7B,IAAI,IAAI,KAAK,CAAC,EAAE,IAAI;oBACpB,IAAI,IAAI,KAAK,CAAC,EAAE,IAAI;oBACpB,IAAI,UAAU,qBAAqB;wBAC/B,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI;wBAC1B,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI;oBAC9B;oBACA,OAAO;wBAAC;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;qBAAE;gBAC7B;YACA,KAAK;gBAAU;oBACX,MAAM,QAAQ,UAAU,KAAK;oBAC7B,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;oBAC7B,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;oBAC7B,OAAO;wBAAC;wBAAK;wBAAK,CAAC;wBAAK;wBAAK;wBAAG;qBAAE;gBACtC;YACA,KAAK;gBAAQ;oBACT,MAAM,QAAQ,UAAU,KAAK;oBAC7B,OAAO;wBAAC;wBAAG,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;wBAAG,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;wBAAG;wBAAG;wBAAG;qBAAE;gBAC/D;YACA,KAAK;gBAAU;oBACX,MAAM,QAAQ,UAAU,KAAK;oBAC7B,IAAI,IAAI,KAAK,CAAC,EAAE,IAAI;oBACpB,IAAI,IAAI,KAAK,CAAC,EAAE,IAAI;oBACpB,IAAI,UAAU,qBAAqB;wBAC/B,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI;wBAC1B,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI;oBAC9B;oBACA,OAAO;wBAAC,KAAK,CAAC,EAAE;wBAAE,KAAK,CAAC,EAAE;wBAAE,KAAK,CAAC,EAAE;wBAAE,KAAK,CAAC,EAAE;wBAAE;wBAAG;qBAAE;gBACzD;YACA;gBACI,OAAO;oBAAC;oBAAG;oBAAG;oBAAG;oBAAG;oBAAG;iBAAE;QACjC;IACJ;IACA,MAAM,SAAS,SAAS,MAAM,CAAC,kBAAkB;QAAC;QAAG;QAAG;QAAG;QAAG;QAAG;KAAE;IACnE,KAAK,YAAY,IAAI;AACzB;AACA,0GAA0G;AAC1G,MAAM,wBAAwB,CAAC,KAAK;IAChC,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,CAAC,CAAC,UAAU,KAAK,KAAK,GACtB;IACJ,MAAM,OAAO,eAAe;IAC5B,MAAM,WAAW,KAAK,KAAK,EAAE;IAC7B,IAAI,CAAC,UACD;IACJ,MAAM,QAAQ,SAAS,KAAK,CAAC,aAAa,IAAI;IAC9C,MAAM,aAAa,SAAS,KAAK,CAAC,iBAAiB,IAAI,EAAE;IACzD,IAAI,KAAK,SAAS,KAAK,CAAC,EAAE,IAAI;IAC9B,IAAI,KAAK,SAAS,KAAK,CAAC,EAAE,IAAI;IAC9B,IAAI,KAAK,SAAS,KAAK,CAAC,EAAE,IAAI;IAC9B,IAAI,KAAK,SAAS,KAAK,CAAC,EAAE,IAAI;IAC9B,IAAI,UAAU,qBAAqB;QAC/B,MAAM,KAAK,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QAC5B,MAAM,KAAK,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QAC5B,MAAM,KAAK,IAAI,CAAC,EAAE;QAClB,MAAM,KAAK,IAAI,CAAC,EAAE;QAClB,KAAK,KAAK,KAAK;QACf,KAAK,KAAK,KAAK;QACf,KAAK,KAAK,KAAK;QACf,KAAK,KAAK,KAAK;IACnB;IACA,MAAM,OAAO,IAAI,cAAc,CAAC,IAAI,IAAI,IAAI;IAC5C,kBAAkB,MAAM,YAAY,MAAM;IAC1C,SAAS,QAAQ,EAAE,QAAQ,CAAC;QACxB,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,EAAE,KAAK,KAAK,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC,WAAW;IAC7E;IACA,IAAI,IAAI,CAAC;AACb;AACA,0GAA0G;AAC1G,MAAM,wBAAwB,CAAC,KAAK;IAChC,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,CAAC,CAAC,UAAU,KAAK,KAAK,GACtB;IACJ,MAAM,OAAO,eAAe;IAC5B,MAAM,WAAW,KAAK,KAAK,EAAE;IAC7B,IAAI,CAAC,UACD;IACJ,MAAM,QAAQ,SAAS,KAAK,CAAC,aAAa,IAAI;IAC9C,MAAM,aAAa,SAAS,KAAK,CAAC,iBAAiB,IAAI,EAAE;IACzD,IAAI,IAAI,SAAS,KAAK,CAAC,CAAC,IAAI;IAC5B,IAAI,KAAK,SAAS,KAAK,CAAC,EAAE,IAAI;IAC9B,IAAI,KAAK,SAAS,KAAK,CAAC,EAAE,IAAI;IAC9B,IAAI,KAAK,SAAS,KAAK,CAAC,EAAE,IAAI;IAC9B,IAAI,KAAK,SAAS,KAAK,CAAC,EAAE,IAAI;IAC9B,IAAI,UAAU,qBAAqB;QAC/B,MAAM,KAAK,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QAC5B,MAAM,KAAK,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QAC5B,MAAM,KAAK,IAAI,CAAC,EAAE;QAClB,MAAM,KAAK,IAAI,CAAC,EAAE;QAClB,IAAI,IAAI;QACR,KAAK,KAAK,KAAK;QACf,KAAK,KAAK,KAAK;QACf,KAAK,KAAK,KAAK;QACf,KAAK,KAAK,KAAK;IACnB;IACA,MAAM,OAAO,IAAI,cAAc,CAAC,IAAI,IAAI,GAAG,IAAI,IAAI;IACnD,kBAAkB,MAAM,YAAY,MAAM;IAC1C,SAAS,QAAQ,EAAE,QAAQ,CAAC;QACxB,KAAK,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,EAAE,KAAK,KAAK,CAAC,SAAS,EAAE,KAAK,KAAK,CAAC,WAAW;IAC7E;IACA,IAAI,IAAI,CAAC;AACb;AACA,MAAM,eAAe,CAAC,KAAK;IACvB,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,CAAC,CAAC,UAAU,KAAK,KAAK,GACtB;IACJ,MAAM,YAAY,KAAK,KAAK,EAAE;IAC9B,IAAI,WACA,IAAI,SAAS,CAAC;AACtB;AACA,MAAM,UAAU,CAAC,KAAK;IAClB,IAAI,sBAAsB,OACtB,OAAO,sBAAsB,KAAK;IACtC,IAAI,sBAAsB,OACtB,OAAO,sBAAsB,KAAK;IACtC,OAAO,aAAa,KAAK;AAC7B;AACA,MAAM,OAAO,CAAC,KAAK;IACf,MAAM,QAAQ,KAAK,KAAK,IAAI,CAAC;IAC7B,IAAI,UAAU,SAAS,YAAY,SAAS,MAAM,IAAI,IAAI,MAAM,MAAM,EAAE;QACpE,IAAI,aAAa,CAAC,MAAM,QAAQ;IACpC,OACK,IAAI,UAAU,SAAS,MAAM,IAAI,EAAE;QACpC,IAAI,IAAI,CAAC,MAAM,QAAQ;IAC3B,OACK,IAAI,YAAY,SAAS,MAAM,MAAM,EAAE;QACxC,IAAI,MAAM;IACd,OACK;QACD,IAAI,IAAI;QACR,IAAI,OAAO,CAAC;QACZ,IAAI,IAAI,CAAC;QACT,IAAI,OAAO;IACf;AACJ;AACA,MAAM,OAAO,KAAQ;AACrB,MAAM,cAAc;IAChB,CAAC,+JAAA,CAAA,QAAO,CAAC,EAAE;IACX,CAAC,+JAAA,CAAA,eAAc,CAAC,EAAE;IAClB,CAAC,+JAAA,CAAA,OAAM,CAAC,EAAE;IACV,CAAC,+JAAA,CAAA,OAAM,CAAC,EAAE;IACV,CAAC,+JAAA,CAAA,OAAM,CAAC,EAAE;IACV,CAAC,+JAAA,CAAA,IAAG,CAAC,EAAE;IACP,CAAC,+JAAA,CAAA,OAAM,CAAC,EAAE;IACV,CAAC,+JAAA,CAAA,SAAQ,CAAC,EAAE;IACZ,CAAC,+JAAA,CAAA,QAAO,CAAC,EAAE;IACX,CAAC,+JAAA,CAAA,UAAS,CAAC,EAAE;IACb,CAAC,+JAAA,CAAA,UAAS,CAAC,EAAE;IACb,CAAC,+JAAA,CAAA,WAAU,CAAC,EAAE;AAClB;AACA,MAAM,eAAe,CAAC,KAAK;IACvB,MAAM,WAAW,WAAW,CAAC,KAAK,IAAI,CAAC;IACvC,IAAI,UAAU;QACV,SAAS,KAAK;IAClB,OACK;QACD,QAAQ,IAAI,CAAC,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,2BAA2B,CAAC;IAC3E;AACJ;AACA,MAAM,WAAW,CAAC,KAAK;IACnB,WAAW,KAAK;IAChB,YAAY,KAAK;IACjB,YAAY,KAAK;IACjB,eAAe,KAAK;IACpB,eAAe,KAAK;IACpB,QAAQ,KAAK;IACb,iBAAiB,KAAK;IACtB,eAAe,KAAK;IACpB,WAAW,KAAK;IAChB,qBAAqB,KAAK;IAC1B,aAAa,KAAK;IAClB,KAAK,KAAK;AACd;AACA,MAAM,WAAW,CAAC,KAAK;IACnB,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,CAAC,CAAC,cAAc,KAAK,KAAK,GAC1B;IACJ,MAAM,QAAQ,KAAK,KAAK,CAAC,QAAQ;IACjC,IAAI,OAAO;QACP,MAAM,WAAW,MAAM,QAAQ,IAAI,EAAE;QACrC,SAAS,OAAO,CAAC,CAAC,QAAU,aAAa,KAAK;QAC9C,IAAI,IAAI;IACZ;AACJ;AACA,MAAM,eAAe,CAAC,KAAK;IACvB,MAAM,WAAW,KAAK,QAAQ,IAAI,EAAE;IACpC,SAAS,OAAO,CAAC,CAAC;QACd,IAAI,IAAI;QACR,SAAS,KAAK;QACd,SAAS,KAAK;QACd,aAAa,KAAK;QAClB,IAAI,OAAO;IACf;AACJ;AACA,MAAM,qBAAqB,CAAC,KAAK;IAC7B,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG;IAClC,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,GAAG,KAAK,KAAK;IACnD,MAAM,EAAE,cAAc,MAAM,EAAE,QAAQ,UAAU,EAAE,GAAG,uBAAuB,CAAC;IAC7E,IAAI,WAAW,QAAQ,SAAS,QAAQ,UAAU,MAC9C;IACJ,MAAM,IAAI,SAAS,QAAQ;IAC3B,MAAM,IAAI,SAAS,QAAQ;IAC3B,MAAM,eAAe,SAAS,QAAQ;IACtC,MAAM,gBAAgB,SAAS,QAAQ;IACvC,MAAM,eAAe,eAAe;IACpC,MAAM,gBAAgB,QAAQ;IAC9B,MAAM,SAAS,QAAQ;IACvB,MAAM,SAAS,SAAS;IACxB,IAAI,UAAU,QAAQ;QAClB,IAAI,KAAK,CAAC,QAAQ;QAClB,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC;QACnB;IACJ;IACA,IAAI,AAAC,eAAe,iBAAiB,gBAAgB,UAChD,gBAAgB,iBAAiB,gBAAgB,SAAU;QAC5D,IAAI,KAAK,CAAC,QAAQ;QAClB,OAAQ;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC;gBACnB;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,AAAC,QAAQ,gBAAiB,MAAM,IAAI,GAAG,CAAC;gBAC3E;YACJ;gBACI,IAAI,SAAS,CAAC,CAAC,IAAI,CAAC,eAAe,AAAC,QAAQ,gBAAiB,MAAM,GAAG,CAAC;QAC/E;IACJ,OACK;QACD,IAAI,KAAK,CAAC,QAAQ;QAClB,OAAQ;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC;gBACnB;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,AAAC,SAAS,eAAgB,KAAK,IAAI;gBAC3E;YACJ;gBACI,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,AAAC,SAAS,eAAgB,KAAK;QAC/E;IACJ;AACJ;AACA,MAAM,eAAe,CAAC,KAAK;IACvB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,GAAG;IAC9B,MAAM,cAAc,KAAK,GAAG,CAAC,WAAW,IAAI;IAC5C,MAAM,aAAa,KAAK,GAAG,CAAC,UAAU,IAAI;IAC1C,IAAI,SAAS,CAAC,OAAO,aAAa,MAAM;AAC5C;AACA,MAAM,YAAY,CAAC,KAAK;IACpB,IAAI,IAAI;IACR,SAAS,KAAK;IACd,aAAa,KAAK;IAClB,mBAAmB,KAAK;IACxB,aAAa,KAAK;IAClB,IAAI,OAAO;AACf;AAEA,MAAM,QAAQ;IAAE,OAAO;IAAQ,SAAS;AAAE;AAC1C,uDAAuD;AACvD,MAAM,aAAa,CAAC;IAChB,IAAI,CAAC,KACD,OAAO;IACX,MAAM,SAAS,2IAAA,CAAA,UAAW,CAAC,GAAG,CAAC;IAC/B,IAAI,CAAC,QACD,OAAO;IACX,MAAM,QAAQ,2IAAA,CAAA,UAAW,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG;IACvD,MAAM,UAAU,OAAO,KAAK,CAAC,EAAE;IAC/B,OAAO;QAAE;QAAO;IAAQ;AAC5B;AAEA,MAAM,cAAc;AACpB,MAAM,YAAY,CAAC,MAAQ,IAAI,KAAK,CAAC;AACrC,MAAM,mBAAmB,CAAC,KAAK;IAC3B,MAAM,EAAE,UAAU,CAAC,EAAE,UAAU,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;IAC3D,IAAI,SAAS,CAAC,CAAC,QAAQ,SAAS,CAAC,SAAS;IAC1C,IAAI,KAAK,CAAC,OAAO,GAAG,GAAG;QACnB,KAAK;YAAC;YAAO;SAAO;QACpB,OAAO;QACP,QAAQ;IACZ;AACJ;AACA,MAAM,oBAAoB,CAAC,KAAK;IAC5B,IAAI,CAAC,IAAI,MAAM,EACX;IACJ,IAAI,CAAC,IAAI,SAAS,EACd;IACJ,MAAM,OAAO,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE;IACrC,IAAI,CAAC,MACD;IACJ,IAAI,IAAI;IACR,MAAM,QAAQ,KAAK,iBAAiB,CAAC;IACrC,MAAM,oBAAoB,KAAK,iBAAiB,CAAC;IACjD,IAAI,oBAAoB;IACxB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE,KAAK,EAAG;QAC3C,MAAM,WAAW,IAAI,SAAS,CAAC,EAAE;QACjC,MAAM,QAAQ,IAAI,MAAM,CAAC,EAAE;QAC3B,qBAAqB,SAAS,QAAQ,IAAI;QAC1C,IAAI,MAAM,EAAE,KAAK,kBAAkB,EAAE,IAAI,IAAI,UAAU,CAAC,UAAU,EAAE;YAChE,IAAI,SAAS,CAAC,mBAAmB,SAAS,OAAO,IAAI;YACrD,iBAAiB,KAAK,IAAI,UAAU,CAAC,UAAU;YAC/C,IAAI,MAAM,CAAC,EAAE,GAAG;YAChB,oBAAoB;QACxB;IACJ;IACA,IAAI,OAAO;AACf;AACA,MAAM,YAAY,CAAC,KAAK;IACpB,IAAI,CAAC,IAAI,MAAM,EACX;IACJ,IAAI,CAAC,IAAI,SAAS,EACd;IACJ,MAAM,OAAO,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE;IACrC,IAAI,CAAC,MACD;IACJ,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;IACzC,MAAM,QAAQ,WAAW,IAAI,UAAU,CAAC,KAAK;IAC7C,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,IAAI,UAAU,CAAC,OAAO,IACtC,MAAM,OAAO,GACb,IAAI,UAAU,CAAC,OAAO;IAC5B,MAAM,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,EAAE,WAAW,CAAC,EAAE,GAAG;IAClD,IAAI,SAAS,CAAC,MAAM,KAAK;IACzB,IAAI,WAAW,CAAC;IAChB,IAAI,MAAM;QACN,IAAI,UAAU,OAAO;YACjB,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,SAAS,UAAU,QAAQ,KAAK,KAAK,CAAC;QAChE,OACK;YACD,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,SAAS,UAAU,QAAQ;QACrD;IACJ;IACA,kBAAkB,KAAK;IACvB,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,aAAa,KAAK,QAAQ,GAAG,MAAM;IAC1D,IAAI;QACA,aAAa,KAAK,IAAI,MAAM,EAAE,IAAI,SAAS,EAAE,GAAG;IACpD,EACA,OAAO,OAAO;QACV,QAAQ,GAAG,CAAC;IAChB;IACA,IAAI,SAAS,CAAC,UAAU;AAC5B;AACA,MAAM,qBAAqB,CAAC,KAAK,MAAM;IACnC,MAAM,QAAQ,WAAW;IACzB,IAAI,IAAI;IACR,IAAI,WAAW,CAAC,MAAM,OAAO;IAC7B,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,MAAM;IAChD,IAAI,IAAI,CAAC,MAAM,KAAK;IACpB,IAAI,OAAO;AACf;AACA,MAAM,uBAAuB,CAAC,KAAK;IAC/B,IAAI,IAAI;IACR,IAAI,SAAS,CAAC,eAAe,IAAI,CAAC,MAAM;IACxC,IAAI,aAAa,CAAC,eAAe,OAAO;IACxC,IAAI,SAAS,IAAI,CAAC,eAAe,KAAK,GAAG;QACrC,IAAI,IAAI,CAAC,IAAI,eAAe,IAAI,CAAC,MAAM,EAAE,CAAC;IAC9C,OACK,IAAI,SAAS,IAAI,CAAC,eAAe,KAAK,GAAG;QAC1C,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,MAAM,EAAE,CAAC;IAC1C;IACA,IAAI,OAAO,IAAI,CAAC,eAAe,KAAK,GAAG;QACnC,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,eAAe,IAAI,CAAC,MAAM;QACnD,IAAI,OAAO,MAAM;QACjB,MAAM,YAAY,KAAK,KAAK,CAAC,eAAe,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI;QAClE,mCAAmC;QACnC,MAAM,iBAAiB,eAAe,IAAI,CAAC,KAAK,GAAG,YAAY,IAAI;QACnE,MAAM,aAAa,iBAAiB,YAAY;QAChD,QAAQ;QACR,MAAM,OAAO,eAAe,IAAI,CAAC,CAAC,GAAG;QACrC,MAAM,OAAO,eAAe,IAAI,CAAC,CAAC,GAAG;QACrC,IAAI,EAAE,CAAC,EAAE,GAAG,eAAe,IAAI;QAC/B,IAAI,MAAM,CAAC,eAAe,IAAI,CAAC,CAAC,EAAE,eAAe,IAAI,CAAC,CAAC;QACvD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,KAAK,EAAG;YACnC,IAAI,aAAa,CAAC,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI,IAAI,MAAM,eAAe,IAAI,CAAC,CAAC;YACrF,KAAK,IAAI;QACb;IACJ,OACK;QACD,IAAI,MAAM,CAAC,eAAe,IAAI,CAAC,CAAC,EAAE,eAAe,IAAI,CAAC,CAAC;QACvD,IAAI,MAAM,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,eAAe,IAAI,CAAC,KAAK,EAAE,eAAe,IAAI,CAAC,CAAC;QACnF,IAAI,SAAS,IAAI,CAAC,eAAe,KAAK,GAAG;YACrC,IAAI,MAAM,CAAC,eAAe,IAAI,CAAC,CAAC,EAAE,eAAe,IAAI,CAAC,CAAC,GAAG,eAAe,IAAI,CAAC,MAAM,GAAG;YACvF,IAAI,MAAM,CAAC,eAAe,IAAI,CAAC,CAAC,GAAG,eAAe,IAAI,CAAC,KAAK,EAAE,eAAe,IAAI,CAAC,CAAC,GAAG,eAAe,IAAI,CAAC,MAAM,GAAG;QACvH;IACJ;IACA,IAAI,MAAM,CAAC,eAAe,KAAK;IAC/B,IAAI,OAAO;AACf;AACA,MAAM,aAAa,CAAC,KAAK;IACrB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,aAAa,KAAK,MAAM,IAAI;IAClC,IAAI,IAAI;IACR,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG;IACvC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE,KAAK,EAAG;QAC1C,MAAM,MAAM,KAAK,IAAI,CAAC,EAAE;QACxB,MAAM,YAAY,MAAM,KAAK,IAAI,CAAC,MAAM,GAAG;QAC3C,IAAI,IAAI,UAAU,CAAC,eAAe,EAAE;YAChC,MAAM,WAAW,IAAI,QAAQ,IAAI;YACjC,MAAM,gBAAgB,YAAY,KAAK,aAAa,IAAI,IAAI;YAC5D,MAAM,iBAAiB;gBACnB,GAAG;gBACH,GAAG,CAAC;gBACJ,QAAQ,KAAK,GAAG,CAAC,MAAM;gBACvB,OAAO,WAAW;YACtB;YACA,mBAAmB,KAAK,gBAAgB,IAAI,UAAU,CAAC,eAAe;QAC1E;QACA,UAAU,KAAK;IACnB;IACA,IAAI,OAAO;IACX,IAAI,IAAI;IACR,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC;IACpC,IAAI,KAAK,eAAe,EAAE;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,eAAe,CAAC,MAAM,EAAE,KAAK,EAAG;YACrD,MAAM,iBAAiB,KAAK,eAAe,CAAC,EAAE;YAC9C,qBAAqB,KAAK;QAC9B;IACJ;IACA,IAAI,OAAO;AACf;AACA,MAAM,cAAc,CAAC,KAAK;IACtB,MAAM,OAAO,CAAC,CAAC;QACX,WAAW,KAAK;IACpB;AACJ;AACA,MAAM,aAAa,CAAC,KAAK;IACrB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,GAAG;IAC9B,MAAM,SAAS;QAAC,KAAK,KAAK;KAAC;IAC3B,MAAM,aAAa,KAAK,GAAG,EAAE,cAAc;IAC3C,MAAM,cAAc,KAAK,GAAG,EAAE,eAAe;IAC7C,MAAM,WAAW,KAAK,KAAK,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG;IACvD,MAAM,UAAU,KAAK,WAAW,IAAI;IACpC,IAAI,IAAI;IACR,IAAI,SAAS,CAAC,OAAO,cAAc,SAAS,MAAM,aAAa;IAC/D,OAAO,OAAO,CAAC,CAAC;QACZ,YAAY,KAAK;IACrB;IACA,IAAI,OAAO;AACf;AAEA,MAAM,aAAa,CAAC,KAAK;IACrB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG;IAClC,MAAM,MAAM,KAAK,KAAK,EAAE,OAAO;IAC/B,MAAM,WAAW,MAAM;IACvB,IAAI,OAAO,CAAC;QAAE,MAAM;YAAC;YAAO;SAAO;QAAE,QAAQ;QAAG;IAAS;AAC7D;AAEA,MAAM,aAAa,CAAC,KAAK;IACrB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,KAAK,GAAG;IAC9B,MAAM,QAAQ,MAAM,UAAU,CAAC,EAAE,CAAC,SAAS;IAC3C,MAAM,QAAQ,KAAK,KAAK,EAAE;IAC1B,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG,GAAG,OAAO;QAAE;IAAM;AAC7C;AAEA,MAAM,aAAa,CAAC,KAAK;IACrB,MAAM,MAAM,KAAK,KAAK,CAAC,IAAI;IAC3B,IAAI;IACJ,IAAI,OAAO,QAAQ,UAAU;QACzB,QAAQ,IAAI,cAAc,CAAC,IAAI;IACnC;IACA,IAAI,CAAC,OAAO;QACR,QAAQ,IAAI,SAAS,CAAC;IAC1B;IACA,IAAI,CAAC,MAAM,GAAG,EAAE;QACZ,MAAM,KAAK,CAAC;IAChB;IACA,OAAO;AACX;AAEA,MAAM,YAAY,CAAC;IACf,OAAO,CAAC,OAAO,KAAK,CAAC,WAAW,OAAO,OAAO,QAAQ,CAAC;AAC3D;AACA,MAAM,wBAAwB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;IAC/C,MAAM,KAAK,KAAK;IAChB,MAAM,KAAK,KAAK;IAChB,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,MAAM;IAC/B,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,MAAM;IAC/B,MAAM,MAAM,MAAM,IAAI,OAAO,GAAG;IAChC,MAAM,MAAM,MAAM,IAAI,OAAO,GAAG;IAChC,IAAI,KAAK,IAAI;QACT,MAAM,SAAS;QACf,MAAM,QAAQ,SAAS;QACvB,MAAM,UAAU,UAAU,MAAM,KAAK;QACrC,MAAM,UAAU,UAAU,MAAM,KAAK,CAAC,KAAK,KAAK,IAAI;QACpD,OAAO;YAAE;YAAO;YAAQ;YAAS;QAAQ;IAC7C;IACA,MAAM,QAAQ;IACd,MAAM,SAAS,QAAQ;IACvB,MAAM,UAAU,UAAU,MAAM,KAAK;IACrC,MAAM,UAAU,UAAU,MAAM,KAAK,CAAC,KAAK,MAAM,IAAI;IACrD,OAAO;QAAE;QAAO;QAAQ;QAAS;IAAQ;AAC7C;AACA,MAAM,qBAAqB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;IAC5C,MAAM,QAAQ;IACd,MAAM,SAAS;IACf,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,MAAM;IAC/B,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,MAAM;IAC/B,MAAM,MAAM,MAAM,IAAI,OAAO,GAAG;IAChC,MAAM,MAAM,MAAM,IAAI,OAAO,GAAG;IAChC,MAAM,UAAU,UAAU,MAAM,KAAK,CAAC,KAAK,KAAK,IAAI;IACpD,MAAM,UAAU,UAAU,MAAM,KAAK,CAAC,KAAK,MAAM,IAAI;IACrD,OAAO;QAAE;QAAO;QAAQ;QAAS;IAAQ;AAC7C;AACA,MAAM,sBAAsB,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;IAC7C,MAAM,KAAK,KAAK;IAChB,MAAM,KAAK,KAAK;IAChB,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,MAAM;IAC/B,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,MAAM;IAC/B,MAAM,MAAM,MAAM,IAAI,OAAO,GAAG;IAChC,MAAM,MAAM,MAAM,IAAI,OAAO,GAAG;IAChC,IAAI,KAAK,IAAI;QACT,MAAM,QAAQ;QACd,MAAM,SAAS,QAAQ;QACvB,MAAM,UAAU,UAAU,MAAM,KAAK;QACrC,MAAM,UAAU,UAAU,MAAM,KAAK,CAAC,KAAK,MAAM,IAAI;QACrD,OAAO;YAAE;YAAO;YAAQ;YAAS;QAAQ;IAC7C;IACA,MAAM,SAAS;IACf,MAAM,QAAQ,SAAS;IACvB,MAAM,UAAU,UAAU,MAAM,KAAK,CAAC,KAAK,KAAK,IAAI;IACpD,MAAM,UAAU,UAAU,MAAM,KAAK;IACrC,OAAO;QAAE;QAAO;QAAQ;QAAS;IAAQ;AAC7C;AACA,MAAM,0BAA0B,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI;IACjD,MAAM,mBAAmB,sBAAsB,IAAI,IAAI,IAAI,IAAI,IAAI;IACnE,MAAM,gBAAgB,mBAAmB,IAAI,IAAI,IAAI,IAAI,IAAI;IAC7D,OAAO,iBAAiB,KAAK,GAAG,cAAc,KAAK,GAC7C,mBACA;AACV;AACA,MAAM,qBAAqB,CAAC,IAAI,IAAI,IAAI;IACpC,OAAO;QACH,OAAO;QACP,QAAQ;QACR,SAAS,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,MAAM,QAAQ,IAAI,MAAM;QAC9C,SAAS,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,MAAM,QAAQ,IAAI,MAAM;IAClD;AACJ;AACA,MAAM,mBAAmB,CAAC,OAAO,MAAM,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI;IACzD,OAAQ;QACJ,KAAK;YACD,OAAO,sBAAsB,IAAI,IAAI,IAAI,IAAI,IAAI;QACrD,KAAK;YACD,OAAO,oBAAoB,IAAI,IAAI,IAAI,IAAI,IAAI;QACnD,KAAK;YACD,OAAO,mBAAmB,IAAI,IAAI,IAAI,IAAI,IAAI;QAClD,KAAK;YACD,OAAO,wBAAwB,IAAI,IAAI,IAAI,IAAI,IAAI;QACvD;YACI,OAAO,mBAAmB,IAAI,IAAI,IAAI;IAC9C;AACJ;AAEA,MAAM,YAAY,CAAC,KAAK,MAAM;IAC1B,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,KAAK,GAAG;IAC9B,MAAM,UAAU,KAAK,KAAK,EAAE;IAC5B,MAAM,YAAY,KAAK,KAAK,EAAE;IAC9B,MAAM,kBAAkB,KAAK,KAAK,EAAE;IACpC,MAAM,kBAAkB,KAAK,KAAK,EAAE;IACpC,MAAM,aAAa,KAAK,GAAG,CAAC,UAAU,IAAI;IAC1C,MAAM,eAAe,KAAK,GAAG,CAAC,YAAY,IAAI;IAC9C,MAAM,gBAAgB,KAAK,GAAG,CAAC,aAAa,IAAI;IAChD,MAAM,cAAc,KAAK,GAAG,CAAC,WAAW,IAAI;IAC5C,MAAM,aAAa,QAAQ,UAAU,IAAI,IAAI;IAC7C,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,iBAAiB,WAAW,KAAK,GAAG,CAAC,KAAK,GAAG,cAAc,cAAc,KAAK,GAAG,CAAC,MAAM,GAAG,aAAa,eAAe,KAAK,KAAK,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,MAAM,EAAE,iBAAiB;IACzN,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;QACjB,IAAI,UAAU,KAAK,WAAW,GAAG;YAC7B,MAAM,WAAW,KAAK,KAAK,CAAC,GAAG;YAC/B,MAAM,QAAQ,WAAW,GAAG,CAAC,aAAa,WAAW,KAAK;YAC1D,IAAI,UACA,WAAW,GAAG,CAAC,UAAU;YAC7B,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,WAAW,IAAI;YAC1C,IACK,WAAW,CAAC,cACZ,KAAK,CAAC,OAAO,OAAO,cAAc,SAAS,MAAM,aAAa,SAAS;gBACxE;gBACA;YACJ;QACJ,OACK;YACD,QAAQ,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,SAAS,CAAC,KAAK,KAAK,CAAC,GAAG,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE,mCAAmC,CAAC;QAC5H;IACJ;AACJ;AACA,MAAM,cAAc,CAAC,KAAK,MAAM;IAC5B,IAAI,IAAI;IACR,SAAS,KAAK;IACd,UAAU,KAAK,MAAM;IACrB,IAAI,OAAO;AACf;AAEA,MAAM,gBAAgB;AACtB,MAAM,gBAAgB;AACtB,MAAM,eAAe;AACrB,2DAA2D;AAC3D,MAAM,eAAe,CAAC,KAAK;IACvB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,aAAa,CAAC,EAAE,eAAe,CAAC,EAAE,gBAAgB,CAAC,EAAE,kBAAkB,CAAC,EAAE,iBAAiB,CAAC,EAAE,mBAAmB,CAAC,EAAE,oBAAoB,CAAC,EAAG,GAAG,KAAK,GAAG;IAC1M,IACK,SAAS,CAAC,eACV,OAAO,CAAC,KACR,IAAI,CAAC,OAAO,cAAc,iBAAiB,MAAM,aAAa,gBAAgB,QAAQ,cAAc,eAAe,mBAAmB,iBAAiB,SAAS,aAAa,gBAAgB,iBAAiB,mBAC9M,IAAI;AACb;AACA,MAAM,eAAe,CAAC,KAAK;IACvB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc,CAAC,EAAE,aAAa,CAAC,EAAE,eAAe,CAAC,EAAE,gBAAgB,CAAC,EAAE,kBAAkB,CAAC,EAAE,iBAAiB,CAAC,EAAE,mBAAmB,CAAC,EAAE,oBAAoB,CAAC,EAAG,GAAG,KAAK,GAAG;IAC1M,IAAI,SAAS,CAAC,eAAe,OAAO,CAAC;IACrC,cAAc;IACd,IACK,IAAI,CAAC,OAAO,cAAc,iBAAiB,MAAM,gBAAgB,QAAQ,eAAe,cAAc,kBAAkB,kBAAkB,YAC1I,IAAI;IACT,eAAe;IACf,IACK,IAAI,CAAC,OAAO,iBAAiB,MAAM,gBAAgB,aAAa,SAAS,iBAAiB,mBAC1F,IAAI;IACT,gBAAgB;IAChB,IACK,IAAI,CAAC,OAAO,QAAQ,eAAe,kBAAkB,MAAM,gBAAgB,cAAc,SAAS,iBAAiB,mBACnH,IAAI;IACT,iBAAiB;IACjB,IACK,IAAI,CAAC,OAAO,cAAc,iBAAiB,MAAM,SAAS,gBAAgB,mBAAmB,QAAQ,eAAe,cAAc,kBAAkB,kBAAkB,eACtK,IAAI;AACb;AACA,MAAM,cAAc,CAAC,KAAK;IACtB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG;IAC7C,MAAM,EAAE,aAAa,CAAC,EAAE,YAAY,CAAC,EAAE,cAAc,CAAC,EAAE,eAAe,CAAC,EAAG,GAAG,KAAK,GAAG;IACtF,IAAI,SAAS,CAAC,cAAc,OAAO,CAAC;IACpC,aAAa;IACb,IAAI,IAAI,CAAC,MAAM,MAAM,WAAW,OAAO,WAAW,IAAI;IACtD,cAAc;IACd,IACK,IAAI,CAAC,OAAO,YAAY,MAAM,WAAW,YAAY,SAAS,YAAY,cAC1E,IAAI;IACT,eAAe;IACf,IACK,IAAI,CAAC,OAAO,OAAO,MAAM,WAAW,aAAa,SAAS,YAAY,cACtE,IAAI;IACT,gBAAgB;IAChB,IAAI,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,cAAc,IAAI;AAC1D;AACA,MAAM,YAAY,CAAC,KAAK;IACpB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG;IAC7C,MAAM,EAAE,aAAa,CAAC,EAAE,YAAY,CAAC,EAAE,cAAc,CAAC,EAAE,eAAe,CAAC,EAAG,GAAG,KAAK,GAAG;IACtF,MAAM,eAAe,KAAK,KAAK,CAAC,QAAQ,aAAa;IACrD,MAAM,gBAAgB,KAAK,KAAK,CAAC,SAAS,YAAY;IACtD,IACK,QAAQ,CAAC,GACT,OAAO,CAAC,GACR,SAAS,CAAC,SACV,IAAI,CAAC,GAAG,aAAa,GAAG,EAAE,eAAe,EAAE,OAAO,YAAY,KAAK,GAAG,CAAC,MAAM,YAAY,GAAG,IAAI;QAAE,OAAO;IAAS;AAC3H;AACA,MAAM,cAAc,CAAC,KAAK;IACtB,IAAI,KAAK,MAAM,EAAE;QACb,IACK,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,GAC1C,IAAI,CAAC,OACL,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,GAC1C,MAAM,CAAC;IAChB;AACJ;AACA,MAAM,cAAc,CAAC,KAAK;IACtB,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,CAAC,CAAC,WAAW,KAAK,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,EAC7C;IACJ,IAAI,IAAI;IACR,aAAa,KAAK;IAClB,aAAa,KAAK;IAClB,YAAY,KAAK;IACjB,UAAU,KAAK;IACf,YAAY,KAAK;IACjB,IAAI,OAAO;AACf;AAEA,MAAM,mBAAmB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,MAAM,UAAU,CAAC;IACb,MAAM,IAAI,iBAAiB,MAAM,CAAC,CAAC,KAAK,OAAS,CAAC;YAC9C,GAAG,GAAG;YACN,CAAC,KAAK,EAAE,CAAC,GAAG;gBACR,2CAA2C;gBAC3C,GAAG,CAAC,KAAK,IAAI;gBACb,OAAO;YACX;QACJ,CAAC,GAAG,CAAC;IACL,OAAO;AACX;AACA,MAAM,eAAe,CAAC,KAAK;IACvB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG;IAC7C,MAAM,aAAa,KAAK,GAAG,CAAC,UAAU,IAAI;IAC1C,MAAM,cAAc,KAAK,GAAG,CAAC,WAAW,IAAI;IAC5C,MAAM,eAAe,KAAK,GAAG,CAAC,YAAY,IAAI;IAC9C,MAAM,gBAAgB,KAAK,GAAG,CAAC,aAAa,IAAI;IAChD,MAAM,iBAAiB,QAAQ,cAAc;IAC7C,MAAM,kBAAkB,SAAS,aAAa;IAC9C,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;QACrC,QAAQ,IAAI,CAAC;IACjB;IACA,IAAI,IAAI,GAAG,SAAS,CAAC,OAAO,aAAa,MAAM;IAC/C,IAAI,KAAK,KAAK,CAAC,KAAK,EAAE;QAClB,KAAK,KAAK,CAAC,KAAK,CAAC,QAAQ,MAAM,gBAAgB;IACnD;IACA,IAAI,OAAO;AACf;AAEA,wDAAwD;AACxD,qFAAqF;AACrF,MAAM,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG;AAC/C,MAAM,gBAAgB,CAAC,KAAK,QAAQ,OAAO,KAAK;IAC5C,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;IACrC,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,eAAe,EAAE,GAAG;IAC9D,6BAA6B;IAC7B,IAAI,MAAM,CAAC,OAAO,KAAK;IACvB,IAAI,MAAM,CAAC,OAAO,QAAQ,KAAK;IAC/B,2CAA2C;IAC3C,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,2BAA2B;IAC3B,IAAI,aAAa,CAAC,OAAO,QAAQ,IAAI,KAAK,OAAO,OAAO,MAAM,IAAI,OAAO,OAAO,MAAM;IACtF,mDAAmD;IACnD,MAAM,iBAAiB,MAAM,KAAK,GAAG,CAAC,gBAAgB;IACtD,IAAI,MAAM,CAAC,OAAO,OAAO;IACzB,2BAA2B;IAC3B,IAAI,MAAM,CAAC,OAAO,QAAQ,kBAAkB;IAC5C,2CAA2C;IAC3C,MAAM,uBAAuB,KAAK,GAAG,CAAC,MAAM,kBAAkB;IAC9D,MAAM,uBAAuB,KAAK,GAAG,CAAC,MAAM,gBAAgB;IAC5D,MAAM,KAAK,uBAAuB,CAAC,MAAM,KAAK;IAC9C,MAAM,KAAK,uBAAuB,CAAC,MAAM,KAAK;IAC9C,2BAA2B;IAC3B,IAAI,aAAa,CAAC,OAAO,QAAQ,kBAAkB,MAAM,iBAAiB,IAAI,OAAO,QAAQ,mBAAmB,IAAI,MAAM,gBAAgB,OAAO,QAAQ,mBAAmB,sBAAsB,MAAM;IACxM,6BAA6B;IAC7B,IAAI,MAAM,CAAC,OAAO,KAAK,GAAG,CAAC,KAAK,kBAAkB,MAAM;IACxD,0CAA0C;IAC1C,MAAM,sBAAsB,KAAK,GAAG,CAAC,MAAM,iBAAiB;IAC5D,MAAM,sBAAsB,KAAK,GAAG,CAAC,MAAM,gBAAgB;IAC3D,MAAM,KAAK,sBAAsB,CAAC,MAAM,KAAK;IAC7C,MAAM,KAAK,sBAAsB,CAAC,MAAM,KAAK;IAC7C,MAAM,gBAAgB,MAAM,KAAK,GAAG,CAAC,gBAAgB;IACrD,0BAA0B;IAC1B,IAAI,aAAa,CAAC,OAAO,kBAAkB,IAAI,MAAM,gBAAgB,OAAO,iBAAiB,MAAM,iBAAiB,IAAI,OAAO,iBAAiB;IAChJ,IAAI,MAAM,CAAC,MAAM;IACjB,mDAAmD;IACnD,IAAI,MAAM,CAAC,MAAM,MAAM;IACvB,0CAA0C;IAC1C,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,0BAA0B;IAC1B,IAAI,aAAa,CAAC,MAAM,MAAM,IAAI,OAAO,IAAI,KAAK,OAAO,KAAK;IAC9D,IAAI,SAAS;IACb,IAAI,IAAI;IACR,4BAA4B;IAC5B,IAAI,kBAAkB;QAClB,MAAM,UAAU,CAAC,iBAAiB;QAClC,IAAI,MAAM,CAAC,OAAO,QAAQ,GAAG,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI;QACtD,IAAI,MAAM,CAAC,OAAO,OAAO;QACzB,IAAI,MAAM,CAAC,MAAM;QACjB,IAAI,MAAM,CAAC,MAAM,MAAM;QACvB,IAAI,SAAS;QACb,IAAI,IAAI;IACZ;IACA,IAAI,iBAAiB;QACjB,MAAM,UAAU,CAAC,iBAAiB;QAClC,IAAI,MAAM,CAAC,OAAO,QAAQ,GAAG,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI;QACtD,IAAI,MAAM,CAAC,MAAM;QACjB,IAAI,MAAM,CAAC,OAAO,OAAO;QACzB,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM;QAC/B,IAAI,SAAS;QACb,IAAI,IAAI;IACZ;AACJ;AACA,MAAM,gBAAgB,CAAC,KAAK,QAAQ,OAAO,KAAK;IAC5C,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;IAC7B,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,gBAAgB,EAAE,eAAe,EAAG,GAAG;IAC/F,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,IAAI,MAAM,CAAC,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK;IACrC,IAAI,aAAa,CAAC,MAAM,MAAM,IAAI,OAAO,IAAI,KAAK,OAAO,KAAK;IAC9D,IAAI,MAAM,CAAC,OAAO,QAAQ,KAAK;IAC/B,IAAI,aAAa,CAAC,OAAO,QAAQ,IAAI,KAAK,OAAO,OAAO,MAAM,IAAI,OAAO,OAAO,MAAM;IACtF,IAAI,WAAW,CAAC;IAChB,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,kBAAkB,gBAAgB,mBAAmB;IAC5E,IAAI,mBAAmB,UAAU;QAC7B,IAAI,IAAI,CAAC,iBAAiB,GAAG;YAAE,OAAO,iBAAiB;QAAI;IAC/D,OACK,IAAI,mBAAmB,UAAU;QAClC,IAAI,IAAI,CAAC,gBAAgB;YAAE,OAAO,iBAAiB;QAAI;IAC3D;IACA,IAAI,MAAM;IACV,IAAI,MAAM;AACd;AACA,MAAM,kBAAkB,CAAC,KAAK,QAAQ,OAAO,KAAK;IAC9C,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;IACrC,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,GAAG;IAChE,+BAA+B;IAC/B,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM;IAC/B,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM,SAAS;IACxC,8CAA8C;IAC9C,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,2BAA2B;IAC3B,IAAI,aAAa,CAAC,OAAO,OAAO,MAAM,SAAS,IAAI,OAAO,QAAQ,IAAI,MAAM,QAAQ,OAAO,QAAQ,KAAK,MAAM;IAC9G,mDAAmD;IACnD,MAAM,kBAAkB,OAAO,QAAQ,KAAK,GAAG,CAAC,kBAAkB;IAClE,IAAI,MAAM,CAAC,iBAAiB,MAAM;IAClC,8BAA8B;IAC9B,IAAI,MAAM,CAAC,iBAAiB,MAAM,SAAS;IAC3C,8CAA8C;IAC9C,MAAM,0BAA0B,KAAK,GAAG,CAAC,MAAM,kBAAkB;IACjE,MAAM,0BAA0B,KAAK,GAAG,CAAC,MAAM,mBAAmB;IAClE,MAAM,KAAK,0BAA0B,CAAC,MAAM,KAAK;IACjD,MAAM,KAAK,0BAA0B,CAAC,MAAM,KAAK;IACjD,2BAA2B;IAC3B,IAAI,aAAa,CAAC,OAAO,QAAQ,mBAAmB,IAAI,MAAM,SAAS,mBAAmB,OAAO,QAAQ,kBAAkB,MAAM,SAAS,oBAAoB,IAAI,OAAO,QAAQ,kBAAkB,MAAM,SAAS,KAAK,GAAG,CAAC,KAAK;IAChO,+BAA+B;IAC/B,IAAI,MAAM,CAAC,OAAO,QAAQ,kBAAkB,MAAM,KAAK,GAAG,CAAC,KAAK;IAChE,2CAA2C;IAC3C,MAAM,uBAAuB,KAAK,GAAG,CAAC,MAAM,kBAAkB;IAC9D,MAAM,uBAAuB,KAAK,GAAG,CAAC,MAAM,gBAAgB;IAC5D,MAAM,KAAK,uBAAuB,CAAC,MAAM,KAAK;IAC9C,MAAM,KAAK,uBAAuB,CAAC,MAAM,KAAK;IAC9C,MAAM,iBAAiB,OAAO,QAAQ,KAAK,GAAG,CAAC,KAAK;IACpD,0BAA0B;IAC1B,IAAI,aAAa,CAAC,OAAO,QAAQ,kBAAkB,MAAM,iBAAiB,IAAI,OAAO,QAAQ,mBAAmB,IAAI,MAAM,gBAAgB,gBAAgB,MAAM;IAChK,IAAI,MAAM,CAAC,gBAAgB;IAC3B,oDAAoD;IACpD,IAAI,MAAM,CAAC,OAAO,QAAQ,KAAK;IAC/B,2CAA2C;IAC3C,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,2BAA2B;IAC3B,IAAI,aAAa,CAAC,OAAO,QAAQ,IAAI,KAAK,OAAO,OAAO,MAAM,IAAI,OAAO,OAAO,MAAM;IACtF,IAAI,SAAS;IACb,IAAI,IAAI;IACR,8BAA8B;IAC9B,IAAI,gBAAgB;QAChB,MAAM,UAAU,CAAC,iBAAiB;QAClC,IAAI,MAAM,CAAC,OAAO,QAAQ,GAAG,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI;QACtD,IAAI,MAAM,CAAC,OAAO,OAAO;QACzB,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM;QAC/B,IAAI,MAAM,CAAC,MAAM,MAAM;QACvB,IAAI,SAAS;QACb,IAAI,IAAI;IACZ;IACA,IAAI,mBAAmB;QACnB,MAAM,UAAU,oBAAoB;QACpC,IAAI,MAAM,CAAC,OAAO,QAAQ,GAAG,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,MAAM;QAC5D,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM;QAC/B,IAAI,MAAM,CAAC,OAAO,OAAO;QACzB,IAAI,MAAM,CAAC,MAAM;QACjB,IAAI,SAAS;QACb,IAAI,IAAI;IACZ;AACJ;AACA,MAAM,kBAAkB,CAAC,KAAK,QAAQ,OAAO,KAAK;IAC9C,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;IACrC,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,cAAc,EAAE,iBAAiB,EAAG,GAAG;IACrG,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,IAAI,MAAM,CAAC,OAAO,QAAQ,KAAK;IAC/B,IAAI,aAAa,CAAC,OAAO,QAAQ,IAAI,KAAK,OAAO,OAAO,MAAM,IAAI,OAAO,OAAO,MAAM;IACtF,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM,SAAS;IACxC,IAAI,aAAa,CAAC,OAAO,OAAO,MAAM,SAAS,IAAI,OAAO,QAAQ,IAAI,MAAM,QAAQ,OAAO,QAAQ,KAAK,MAAM;IAC9G,IAAI,WAAW,CAAC;IAChB,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,kBAAkB,gBAAgB,qBAAqB;IAC9E,IAAI,qBAAqB,UAAU;QAC/B,IAAI,IAAI,CAAC,mBAAmB,GAAG;YAAE,OAAO,mBAAmB;QAAI;IACnE,OACK,IAAI,qBAAqB,UAAU;QACpC,IAAI,IAAI,CAAC,kBAAkB;YAAE,OAAO,mBAAmB;QAAI;IAC/D;IACA,IAAI,MAAM;IACV,IAAI,MAAM;AACd;AACA,MAAM,mBAAmB,CAAC,KAAK,QAAQ,OAAO,KAAK;IAC/C,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;IACrC,MAAM,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAE,GAAG;IACjE,6BAA6B;IAC7B,IAAI,MAAM,CAAC,OAAO,QAAQ,KAAK,MAAM;IACrC,IAAI,MAAM,CAAC,OAAO,KAAK,MAAM;IAC7B,2CAA2C;IAC3C,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,2BAA2B;IAC3B,IAAI,aAAa,CAAC,OAAO,IAAI,MAAM,QAAQ,MAAM,MAAM,SAAS,IAAI,MAAM,MAAM,SAAS;IACzF,iDAAiD;IACjD,MAAM,mBAAmB,MAAM,SAAS,KAAK,GAAG,CAAC,mBAAmB;IACpE,IAAI,MAAM,CAAC,MAAM;IACjB,6BAA6B;IAC7B,IAAI,MAAM,CAAC,OAAO,iBAAiB;IACnC,2CAA2C;IAC3C,MAAM,yBAAyB,KAAK,GAAG,CAAC,MAAM,iBAAiB;IAC/D,MAAM,yBAAyB,KAAK,GAAG,CAAC,MAAM,mBAAmB;IACjE,MAAM,KAAK,yBAAyB,CAAC,MAAM,KAAK;IAChD,MAAM,KAAK,yBAAyB,CAAC,MAAM,KAAK;IAChD,6BAA6B;IAC7B,IAAI,aAAa,CAAC,OAAO,iBAAiB,MAAM,SAAS,oBAAoB,IAAI,OAAO,kBAAkB,IAAI,MAAM,SAAS,mBAAmB,OAAO,kBAAkB,wBAAwB,MAAM,SAAS;IAChN,gCAAgC;IAChC,IAAI,MAAM,CAAC,OAAO,QAAQ,KAAK,GAAG,CAAC,KAAK,mBAAmB,MAAM,SAAS;IAC1E,0CAA0C;IAC1C,MAAM,0BAA0B,KAAK,GAAG,CAAC,MAAM,kBAAkB;IACjE,MAAM,0BAA0B,KAAK,GAAG,CAAC,MAAM,mBAAmB;IAClE,MAAM,KAAK,0BAA0B,CAAC,MAAM,KAAK;IACjD,MAAM,KAAK,0BAA0B,CAAC,MAAM,KAAK;IACjD,MAAM,oBAAoB,MAAM,SAAS,KAAK,GAAG,CAAC,mBAAmB;IACrE,0BAA0B;IAC1B,IAAI,aAAa,CAAC,OAAO,QAAQ,mBAAmB,IAAI,MAAM,SAAS,mBAAmB,OAAO,QAAQ,kBAAkB,MAAM,SAAS,oBAAoB,IAAI,OAAO,QAAQ,kBAAkB;IACnM,IAAI,MAAM,CAAC,OAAO,OAAO;IACzB,mDAAmD;IACnD,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM,SAAS;IACxC,0CAA0C;IAC1C,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,0BAA0B;IAC1B,IAAI,aAAa,CAAC,OAAO,OAAO,MAAM,SAAS,IAAI,OAAO,QAAQ,IAAI,MAAM,QAAQ,OAAO,QAAQ,KAAK,MAAM;IAC9G,IAAI,SAAS;IACb,IAAI,IAAI;IACR,+BAA+B;IAC/B,IAAI,kBAAkB;QAClB,MAAM,UAAU,oBAAoB;QACpC,IAAI,MAAM,CAAC,OAAO,QAAQ,GAAG,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,MAAM;QAC5D,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM;QAC/B,IAAI,MAAM,CAAC,MAAM,MAAM;QACvB,IAAI,MAAM,CAAC,MAAM;QACjB,IAAI,SAAS;QACb,IAAI,IAAI;IACZ;IACA,IAAI,iBAAiB;QACjB,MAAM,UAAU,CAAC,oBAAoB;QACrC,IAAI,MAAM,CAAC,OAAO,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,MAAM;QAC3D,IAAI,MAAM,CAAC,MAAM,MAAM;QACvB,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM;QAC/B,IAAI,MAAM,CAAC,OAAO,OAAO;QACzB,IAAI,SAAS;QACb,IAAI,IAAI;IACZ;AACJ;AACA,MAAM,mBAAmB,CAAC,KAAK,QAAQ,OAAO,KAAK;IAC/C,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;IACrC,MAAM,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,eAAe,EAAG,GAAG;IACxG,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM,SAAS;IACxC,IAAI,aAAa,CAAC,OAAO,OAAO,MAAM,SAAS,IAAI,OAAO,QAAQ,IAAI,MAAM,QAAQ,OAAO,QAAQ,KAAK,MAAM;IAC9G,IAAI,MAAM,CAAC,OAAO,KAAK,MAAM;IAC7B,IAAI,aAAa,CAAC,OAAO,IAAI,MAAM,QAAQ,MAAM,MAAM,SAAS,IAAI,MAAM,MAAM,SAAS;IACzF,IAAI,WAAW,CAAC;IAChB,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,mBAAmB,kBAAkB,mBAAmB;IAC/E,IAAI,sBAAsB,UAAU;QAChC,IAAI,IAAI,CAAC,oBAAoB,GAAG;YAAE,OAAO,oBAAoB;QAAI;IACrE,OACK,IAAI,sBAAsB,UAAU;QACrC,IAAI,IAAI,CAAC,mBAAmB;YAAE,OAAO,oBAAoB;QAAI;IACjE;IACA,IAAI,MAAM;IACV,IAAI,MAAM;AACd;AACA,MAAM,iBAAiB,CAAC,KAAK,QAAQ,OAAO,KAAK;IAC7C,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;IACrC,MAAM,EAAE,cAAc,EAAE,eAAe,EAAE,iBAAiB,EAAE,GAAG;IAC/D,8BAA8B;IAC9B,IAAI,MAAM,CAAC,MAAM,MAAM,SAAS;IAChC,IAAI,MAAM,CAAC,MAAM,MAAM;IACvB,0CAA0C;IAC1C,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,0BAA0B;IAC1B,IAAI,aAAa,CAAC,MAAM,MAAM,IAAI,OAAO,IAAI,KAAK,OAAO,KAAK;IAC9D,oDAAoD;IACpD,MAAM,gBAAgB,OAAO,KAAK,GAAG,CAAC,iBAAiB;IACvD,IAAI,MAAM,CAAC,eAAe;IAC1B,0BAA0B;IAC1B,IAAI,MAAM,CAAC,eAAe,MAAM;IAChC,0CAA0C;IAC1C,MAAM,sBAAsB,KAAK,GAAG,CAAC,MAAM,iBAAiB;IAC5D,MAAM,sBAAsB,KAAK,GAAG,CAAC,MAAM,gBAAgB;IAC3D,MAAM,KAAK,sBAAsB,CAAC,MAAM,KAAK;IAC7C,MAAM,KAAK,sBAAsB,CAAC,MAAM,KAAK;IAC7C,2BAA2B;IAC3B,IAAI,aAAa,CAAC,OAAO,kBAAkB,IAAI,MAAM,gBAAgB,OAAO,iBAAiB,MAAM,iBAAiB,IAAI,OAAO,iBAAiB,MAAM,KAAK,GAAG,CAAC,KAAK;IACpK,8BAA8B;IAC9B,IAAI,MAAM,CAAC,OAAO,iBAAiB,MAAM,SAAS,KAAK,GAAG,CAAC,KAAK;IAChE,6CAA6C;IAC7C,MAAM,yBAAyB,KAAK,GAAG,CAAC,MAAM,iBAAiB;IAC/D,MAAM,yBAAyB,KAAK,GAAG,CAAC,MAAM,mBAAmB;IACjE,MAAM,KAAK,yBAAyB,CAAC,MAAM,KAAK;IAChD,MAAM,KAAK,yBAAyB,CAAC,MAAM,KAAK;IAChD,MAAM,mBAAmB,OAAO,KAAK,GAAG,CAAC,KAAK;IAC9C,0BAA0B;IAC1B,IAAI,aAAa,CAAC,OAAO,iBAAiB,MAAM,SAAS,oBAAoB,IAAI,OAAO,kBAAkB,IAAI,MAAM,SAAS,mBAAmB,kBAAkB,MAAM,SAAS;IACjL,IAAI,MAAM,CAAC,kBAAkB,MAAM;IACnC,mDAAmD;IACnD,IAAI,MAAM,CAAC,OAAO,KAAK,MAAM;IAC7B,2CAA2C;IAC3C,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,2BAA2B;IAC3B,IAAI,aAAa,CAAC,OAAO,IAAI,MAAM,QAAQ,MAAM,MAAM,SAAS,IAAI,MAAM,MAAM,SAAS;IACzF,IAAI,SAAS;IACb,IAAI,IAAI;IACR,8BAA8B;IAC9B,IAAI,mBAAmB;QACnB,MAAM,UAAU,CAAC,oBAAoB;QACrC,IAAI,MAAM,CAAC,OAAO,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,MAAM;QAC3D,IAAI,MAAM,CAAC,MAAM,MAAM;QACvB,IAAI,MAAM,CAAC,MAAM;QACjB,IAAI,MAAM,CAAC,OAAO,OAAO;QACzB,IAAI,SAAS;QACb,IAAI,IAAI;IACZ;IACA,IAAI,mBAAmB;QACnB,MAAM,UAAU,CAAC,iBAAiB;QAClC,IAAI,MAAM,CAAC,OAAO,QAAQ,GAAG,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI;QACtD,IAAI,MAAM,CAAC,MAAM;QACjB,IAAI,MAAM,CAAC,MAAM,MAAM;QACvB,IAAI,MAAM,CAAC,OAAO,OAAO,MAAM;QAC/B,IAAI,SAAS;QACb,IAAI,IAAI;IACZ;AACJ;AACA,MAAM,iBAAiB,CAAC,KAAK,QAAQ,OAAO,KAAK;IAC7C,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;IAC9B,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,cAAc,EAAE,iBAAiB,EAAG,GAAG;IAClG,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,MAAM,KAAK,MAAM,CAAC,MAAM,KAAK;IAC7B,IAAI,MAAM,CAAC,OAAO,KAAK,MAAM;IAC7B,IAAI,aAAa,CAAC,OAAO,IAAI,MAAM,QAAQ,MAAM,MAAM,SAAS,IAAI,MAAM,MAAM,SAAS;IACzF,IAAI,MAAM,CAAC,MAAM,MAAM;IACvB,IAAI,aAAa,CAAC,MAAM,MAAM,IAAI,OAAO,IAAI,KAAK,OAAO,KAAK;IAC9D,IAAI,WAAW,CAAC;IAChB,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,iBAAiB,gBAAgB,qBAAqB;IAC7E,IAAI,oBAAoB,UAAU;QAC9B,IAAI,IAAI,CAAC,kBAAkB,GAAG;YAAE,OAAO,kBAAkB;QAAI;IACjE,OACK,IAAI,oBAAoB,UAAU;QACnC,IAAI,IAAI,CAAC,iBAAiB;YAAE,OAAO,kBAAkB;QAAI;IAC7D;IACA,IAAI,MAAM;IACV,IAAI,MAAM;AACd;AACA,MAAM,sBAAsB,CAAC,OAAS,KAAK,GAAG,IAC1C,CAAC,KAAK,GAAG,CAAC,cAAc,IACpB,KAAK,GAAG,CAAC,gBAAgB,IACzB,KAAK,GAAG,CAAC,iBAAiB,IAC1B,KAAK,GAAG,CAAC,eAAe;AAChC,MAAM,gBAAgB,CAAC,KAAK;IACxB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,IAAI,CAAC,oBAAoB,OACrB;IACJ,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE,kBAAkB,CAAC,EAAE,mBAAmB,CAAC,EAAE,oBAAoB,CAAC,EAAG,GAAG,KAAK,GAAG;IACzH,MAAM,EAAE,UAAU,CAAC,EAAE,iBAAiB,OAAO,EAAE,iBAAiB,OAAO,EAAE,kBAAkB,OAAO,EAAE,kBAAkB,OAAO,EAAE,mBAAmB,OAAO,EAAE,mBAAmB,OAAO,EAAE,oBAAoB,OAAO,EAAE,oBAAoB,OAAO,EAAG,GAAG,KAAK,KAAK;IAC/P,6EAA6E;IAC7E,MAAM,sBAAsB,KAAK,KAAK,CAAC,mBAAmB,IAAI;IAC9D,6EAA6E;IAC7E,MAAM,uBAAuB,KAAK,KAAK,CAAC,oBAAoB,IAAI;IAChE,6EAA6E;IAC7E,MAAM,yBAAyB,KAAK,KAAK,CAAC,sBAAsB,IAAI;IACpE,6EAA6E;IAC7E,MAAM,0BAA0B,KAAK,KAAK,CAAC,uBAAuB,IAAI;IACtE,MAAM,QAAQ;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IAAiB;IACrB,MAAM,MAAM,KAAK,GAAG,CAAC,sBAAsB,MAAM,OAAO,MAAM;IAC9D,MAAM,MAAM,KAAK,GAAG,CAAC,qBAAqB,MAAM,OAAO,MAAM;IAC7D,MAAM,MAAM,KAAK,GAAG,CAAC,yBAAyB,MAAM,OAAO,MAAM;IACjE,MAAM,MAAM,KAAK,GAAG,CAAC,wBAAwB,MAAM,OAAO,MAAM;IAChE,IAAI,IAAI;IACR,IAAI,aAAa,CAAC;IAClB,IAAI,gBAAgB;QAChB,IAAI,IAAI;QACR,cAAc,KAAK,KAAK,GAAG,EAAE,OAAO,KAAK;QACzC,cAAc,KAAK,KAAK,GAAG,EAAE,OAAO,KAAK;QACzC,IAAI,OAAO;IACf;IACA,IAAI,kBAAkB;QAClB,IAAI,IAAI;QACR,gBAAgB,KAAK,KAAK,GAAG,EAAE,OAAO,KAAK;QAC3C,gBAAgB,KAAK,KAAK,GAAG,EAAE,OAAO,KAAK;QAC3C,IAAI,OAAO;IACf;IACA,IAAI,mBAAmB;QACnB,IAAI,IAAI;QACR,iBAAiB,KAAK,KAAK,GAAG,EAAE,OAAO,KAAK;QAC5C,iBAAiB,KAAK,KAAK,GAAG,EAAE,OAAO,KAAK;QAC5C,IAAI,OAAO;IACf;IACA,IAAI,iBAAiB;QACjB,IAAI,IAAI;QACR,eAAe,KAAK,KAAK,GAAG,EAAE,OAAO,KAAK;QAC1C,eAAe,KAAK,KAAK,GAAG,EAAE,OAAO,KAAK;QAC1C,IAAI,OAAO;IACf;IACA,IAAI,OAAO;AACf;AAEA,MAAM,iBAAiB,CAAC,KAAK;IACzB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG;IAC7C,MAAM,QAAQ,WAAW,KAAK,KAAK,CAAC,eAAe;IACnD,MAAM,cAAc,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,KAAK,KAAK,EAAE,WAAW,IAAI,KAAK,KAAK,CAAC,OAAO;IACvE,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,OAAO,EAAE;IACxC,IACK,WAAW,CAAC,SACZ,SAAS,CAAC,MAAM,KAAK,EACrB,IAAI,CAAC,MAAM,KAAK,OAAO,QACvB,IAAI;AACb;AACA,MAAM,mBAAmB,CAAC,KAAK;IAC3B,MAAM,gBAAgB,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,KAAK,KAAK,EAAE;IAClD,IAAI,eAAe;QACf,IAAI,IAAI;QACR,SAAS,KAAK;QACd,eAAe,KAAK;QACpB,IAAI,OAAO;IACf;AACJ;AAEA,MAAM,WAAW,CAAC,QAAU,OAAO,UAAU;AAC7C,MAAM,UAAU,CAAC,QAAU,OAAO,IAAI,CAAC;AACvC,MAAM,aAAa,CAAC,KAAK,MAAM;IAC3B,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,EACjB;IACJ,MAAM,OAAO,QAAQ;IACrB,MAAM,SAAS,OAAO,SAAS;IAC/B,MAAM,QAAQ,OAAO,IAAI,KAAK,CAAC,KAAK;IACpC,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG;IAC7C,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,QAAQ;AAC1C;AACA,MAAM,UAAU,CAAC,KAAK;IAClB,MAAM,QAAQ,KAAK,KAAK,IAAI,CAAC;IAC7B,IAAI,SAAS,SAAS,SAAS,MAAM,GAAG,GACpC,OAAO,WAAW,KAAK,MAAM,MAAM,GAAG;IAC1C,IAAI,UAAU,SAAS,SAAS,MAAM,IAAI,GACtC,OAAO,WAAW,KAAK,MAAM,MAAM,IAAI;AAC/C;AAEA,MAAM,iBAAiB,CAAC,KAAK;IACzB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,QAAQ,KAAK,KAAK,EAAE;QACpB,IAAI,mBAAmB,CAAC,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE;IACtE;AACJ;AAEA,MAAM,QAAQ,CAAC;IACX,MAAM,MAAM;QAAE,GAAG,OAAO;IAAC;IACzB,4DAA4D;IAC5D,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,CAAC;QACzB,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACV,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QACvB;IACJ;IACA,OAAO;AACX;AACA,MAAM,yBAAyB,CAAC;IAC5B,iBAAiB;IACjB,OAAO;QACH,UAAU,KAAK,KAAK,EAAE,YAAY;QAClC,UAAU,KAAK,KAAK,EAAE,YAAY;QAClC,UAAU,KAAK,KAAK,EAAE,YAAY;QAClC,OAAO,KAAK,KAAK,EAAE,SAAS;QAC5B,cAAc,KAAK,KAAK,EAAE,gBAAgB;IAC9C;AACJ;AACA,MAAM,wBAAwB,CAAC,MAAM;IACjC,OAAO,MAAM;QACT,GAAG,uBAAuB,KAAK;QAC/B,QAAQ,YAAY;QACpB,OAAO,KAAK,KAAK,EAAE,SAAS;QAC5B,WAAW,KAAK,KAAK,EAAE,aAAa;QACpC,UAAU,KAAK,KAAK,EAAE,YAAY;QAClC,SAAS,KAAK,KAAK,EAAE,WAAW;QAChC,QAAQ,KAAK,KAAK,EAAE,UAAU;QAC9B,UAAU,KAAK,KAAK,EAAE,YAAY;QAClC,QAAQ,KAAK,KAAK,EAAE,aAAa;IACrC;AACJ;AACA,MAAM,iCAAiC,CAAC;IACpC,OAAO,MAAM;QACT,GAAG,uBAAuB,KAAK;QAC/B,MAAM,KAAK,KAAK,EAAE,QAAQ;QAC1B,MAAM,KAAK,KAAK,EAAE,QAAQ;QAC1B,aAAa,KAAK,KAAK,EAAE,eAAe;QACxC,SAAS,KAAK,KAAK,EAAE,WAAW;QAChC,QAAQ,KAAK,KAAK,EAAE,UAAU;YAAC;SAAG;IACtC;AACJ;AACA,MAAM,gBAAgB,CAAC,KAAK,WAAW,OAAO;IAC1C,MAAM,aAAa,IAAI,GAAG,CAAC;QACvB,MAAM;QACN,SAAS;QACT,MAAM;YAAC;YAAG;YAAG;YAAO;SAAO;QAC3B,WAAW;YACP,SAAS;gBAAC;gBAAO;gBAAQ;gBAAU;gBAAU;aAAS;YACtD,MAAM;gBACF,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI;YAClC;QACJ;IACJ;IACA,WAAW,WAAW;IACtB,WAAW,KAAK,CAAC,CAAC,kBAAkB,EAAE,SAAS,IAAI,SAAS,EAAE,QAAQ,KAAK,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,gBAAgB,CAAC;IAC3H,WAAW,GAAG,CAAC;IACf,OAAO;AACX;AACA,MAAM,uBAAuB,CAAC,KAAK,MAAM;IACrC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;IACvC,MAAM,WAAW,KAAK,KAAK,EAAE,WAAW;IACxC,MAAM,YAAY,KAAK,KAAK,EAAE,YAAY;IAC1C,MAAM,QAAQ,KAAK,KAAK,EAAE,SAAS;IACnC,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,KAAK,EAAE,SAAS;QACpE,MAAM,MAAM,IAAI,GAAG,CAAC;YAChB,MAAM;YACN,SAAS;YACT,UAAU;QACd;QACA,IAAI,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG;QAC3B,IAAI,GAAG,CAAC;IACZ;IACA,MAAM,mBAAmB;QACrB,CAAC,SAAS,EAAE,cAAc,KAAK,QAAQ,MAAM,KAAK,OAAO;QACzD,CAAC,UAAU,EAAE,cAAc,KAAK,QAAQ,MAAM,KAAK,OAAO;IAC9D;IACA,OAAO,MAAM;QACT,GAAG,uBAAuB,KAAK;QAC/B,iBAAiB,KAAK,KAAK,EAAE,mBAAmB;QAChD,aAAa,KAAK,KAAK,EAAE,eAAe;QACxC,QAAQ,YAAY;QACpB,OAAO,CAAC,CAAC,EAAE,KAAK,KAAK,EAAE,YAAY,OAAO,WAAW,WAAW;QAChE,cAAc,CAAC,CAAC,EAAE,KAAK,KAAK,EAAE,YAAY,OAAO,WAAW,WAAW;QACvE,IAAI,KAAK,KAAK,EAAE,YAAY,OAAO,WAAW;QAC9C,IAAI;YAAE,GAAG;YAAkB,GAAG;QAAiB;IACnD;AACJ;AAEA,MAAM,kBAAkB,CAAC,KAAK,MAAM;IAChC,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG;IAC7C,iBAAiB;IACjB,MAAM,OAAO,KAAK,KAAK,EAAE,QAAQ;IACjC,MAAM,kBAAkB,QAAQ,SAAS,EAAE,GAAG;IAC9C,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;QAC1B,IAAI,QAAQ;IAChB;IACA,IAAI,QAAQ,CAAC,MAAM,MAAM,KAAK,OAAO,QAAQ,sBAAsB,MAAM;AAC7E;AAEA,MAAM,eAAe,CAAC,KAAK;IACvB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG;IAC7C,iBAAiB;IACjB,MAAM,OAAO,KAAK,KAAK,EAAE,QAAQ;IACjC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;QAC1B,IAAI,QAAQ;IAChB;IACA,IAAI,SAAS,CAAC,MAAM,MAAM,KAAK,OAAO,QAAQ,+BAA+B;AACjF;AAEA,MAAM,iBAAiB,CAAC,KAAK,MAAM;IAC/B,MAAM,OAAO,KAAK,KAAK,EAAE,QAAQ;IACjC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;QAC1B,IAAI,QAAQ;IAChB;IACA,MAAM,YAAY,IAAI,SAAS,CAAC;IAChC,MAAM,SAAS;IACf,IAAI,CAAC,OAAO,SAAS,EAAE;QACnB,OAAO,SAAS,GAAG;YAAC;SAAU;IAClC,OACK;QACD,OAAO,SAAS,CAAC,IAAI,CAAC;IAC1B;AACJ;AACA,MAAM,kBAAkB,CAAC,MAAM,OAAO;IAClC,QAAQ,SAAS,CAAC,GAAG;AACzB;AAEA,MAAM,aAAa,CAAC,KAAK;IACrB,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC;IAClD,iBAAiB;IACjB,MAAM,OAAQ,UAAU,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK;IAC7D,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;QAC1B,IAAI,QAAQ;IAChB;IACA,IAAI,QAAQ,CAAC,MAAM,MAAM,KAAK,OAAO,QAAQ,+BAA+B;AAChF;AAEA,MAAM,iBAAiB,CAAC,KAAK,MAAM;IAC/B,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,GAAG;IAC7C,iBAAiB;IACjB,MAAM,OAAO,KAAK,KAAK,EAAE,QAAQ;IACjC,MAAM,kBAAkB,QAAQ,SAAS,EAAE,GAAG;IAC9C,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;QAC1B,IAAI,QAAQ;IAChB;IACA,IAAI,YAAY,CAAC,MAAM,MAAM,KAAK,OAAO,QAAQ,qBAAqB,KAAK,MAAM;AACrF;AAEA,MAAM,kBAAkB,CAAC,OAAS,KAAK,IAAI,KAAK,+JAAA,CAAA,OAAM,IAAI,KAAK,IAAI,KAAK,+JAAA,CAAA,MAAK;AAC7E,MAAM,iBAAiB,CAAC,KAAK,MAAM;IAC/B,IAAI,IAAI;IACR,IAAI,KAAK,GAAG,EAAE;QACV,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,GAAG;IAC7C;IACA,MAAM,WAAW,KAAK,QAAQ,IAAI,EAAE;IACpC,MAAM,cAAc,CAAC,QAAU,WAAW,KAAK,OAAO;IACtD,SAAS,OAAO,CAAC;IACjB,IAAI,OAAO;AACf;AACA,MAAM,YAAY;IACd,CAAC,+JAAA,CAAA,OAAM,CAAC,EAAE;IACV,CAAC,+JAAA,CAAA,OAAM,CAAC,EAAE;IACV,CAAC,+JAAA,CAAA,QAAO,CAAC,EAAE;IACX,CAAC,+JAAA,CAAA,WAAU,CAAC,EAAE;IACd,CAAC,+JAAA,CAAA,YAAW,CAAC,EAAE;IACf,CAAC,+JAAA,CAAA,SAAQ,CAAC,EAAE;IACZ,CAAC,+JAAA,CAAA,WAAU,CAAC,EAAE;IACd,CAAC,+JAAA,CAAA,OAAM,CAAC,EAAE;IACV,CAAC,+JAAA,CAAA,SAAQ,CAAC,EAAE;IACZ,CAAC,+JAAA,CAAA,MAAK,CAAC,EAAE;IACT,CAAC,+JAAA,CAAA,OAAM,CAAC,EAAE;AACd;AACA,MAAM,aAAa;IACf,CAAC,+JAAA,CAAA,WAAU,CAAC,EAAE;AAClB;AACA,MAAM,aAAa,CAAC,KAAK,MAAM;IAC3B,MAAM,iBAAiB,KAAK,KAAK,EAAE,aAAa;IAChD,MAAM,uBAAuB,gBAAgB;IAC7C,IAAI,KAAK,IAAI,KAAK,+JAAA,CAAA,OAAM,EACpB,WAAW,KAAK;IACpB,IAAI,IAAI;IACR,IAAI,gBACA,SAAS,KAAK;IAClB,qBAAqB,KAAK;IAC1B,iBAAiB,KAAK;IACtB,cAAc,KAAK;IACnB,MAAM,WAAW,SAAS,CAAC,KAAK,IAAI,CAAC;IACrC,IAAI,UACA,SAAS,KAAK,MAAM;IACxB,IAAI,sBACA,eAAe,KAAK,MAAM;IAC9B,MAAM,YAAY,UAAU,CAAC,KAAK,IAAI,CAAC;IACvC,IAAI,WACA,UAAU,KAAK,MAAM;IACzB,eAAe,KAAK;IACpB,YAAY,KAAK;IACjB,IAAI,OAAO;AACf;AAEA,MAAM,kBAAkB,CAAC,KAAK,MAAM,YAAY;IAC5C,IAAI,CAAC,KAAK,GAAG,EACT;IACJ,IAAI,CAAC,KAAK,KAAK,EACX;IACJ,IAAI,cAAc,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,QAAQ,EAAE;QACjD,MAAM,WAAW,KAAK,KAAK,CAAC,QAAQ;QACpC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;QAC/C,MAAM,UAAU,QAAQ,CAAC,OAAO,IAAI,IAAI,OAAO;QAC/C,MAAM,MAAM,SAAS,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG;QACxC,MAAM,OAAO,SAAS,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI;QAC3C,MAAM,WAAW,QAAQ,OAAO,CAAC,OAAO;YACpC;YACA;YACA;YACA;YACA;YACA;QACJ;QACA,QAAQ,CAAC,SAAS,GAAG,CAAC,GAAG;IAC7B;IACA,IAAI,CAAC,KAAK,QAAQ,EACd;IACJ,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAU,gBAAgB,KAAK,OAAO,YAAY;AAC7E;AACA,MAAM,eAAe,CAAC,KAAK;IACvB,MAAM,WAAW,CAAC;IAClB,MAAM,QAAQ,KAAK,QAAQ,IAAI,EAAE;IACjC,MAAM,OAAO,CAAC,CAAC,MAAM;QACjB,gBAAgB,KAAK,MAAM,GAAG;IAClC;AACJ;AAEA,MAAM,SAAS,CAAC,KAAK;IACjB,MAAM,QAAQ,IAAI,QAAQ,IAAI,EAAE;IAChC,MAAM,UAAU;QAAE,YAAY,IAAI;QAAO,WAAW,EAAE;IAAC;IACvD,MAAM,OAAO,CAAC,CAAC,OAAS,WAAW,KAAK,MAAM;IAC9C,aAAa,KAAK;IAClB,IAAI,GAAG;IACP,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2938, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/stylesheet/lib/index.js"], "sourcesContent": ["import { compose, castArray, parseFloat as parseFloat$1, matchPercent } from '@react-pdf/fns';\nimport matchMedia from 'media-engine';\nimport hlsToHex from 'hsl-to-hex';\nimport colorString from 'color-string';\nimport parse$1 from 'postcss-value-parser/lib/parse.js';\nimport parseUnit from 'postcss-value-parser/lib/unit.js';\n\n/**\n * Remove nil values from array\n *\n * @param array - Style array\n * @returns Style array without nils\n */\nconst compact = (array) => array.filter(Boolean);\n/**\n * Merges style objects array\n *\n * @param styles - Style array\n * @returns Merged style object\n */\nconst mergeStyles = (styles) => styles.reduce((acc, style) => {\n    const s = Array.isArray(style) ? flatten(style) : style;\n    Object.keys(s).forEach((key) => {\n        if (s[key] !== null && s[key] !== undefined) {\n            acc[key] = s[key];\n        }\n    });\n    return acc;\n}, {});\n/**\n * Flattens an array of style objects, into one aggregated style object.\n *\n * @param styles - Style or style array\n * @returns Flattened style object\n */\nconst flatten = compose(mergeStyles, compact, (castArray));\n\n/**\n * Resolves media queries in styles object\n *\n * @param container - Container for which styles are resolved\n * @param style - Style description\n * @returns Resolved style object\n */\nconst resolveMediaQueries = (container, style) => {\n    return Object.keys(style).reduce((acc, key) => {\n        if (/@media/.test(key)) {\n            return {\n                ...acc,\n                ...matchMedia({ [key]: style[key] }, container),\n            };\n        }\n        return { ...acc, [key]: style[key] };\n    }, {});\n};\n\nconst isRgb = (value) => /rgba?/g.test(value);\nconst isHsl = (value) => /hsla?/g.test(value);\n/**\n * Transform rgb color to hexa\n *\n * @param value - Styles value\n * @returns Transformed value\n */\nconst parseRgb = (value) => {\n    const rgb = colorString.get.rgb(value);\n    return colorString.to.hex(rgb);\n};\n/**\n * Transform Hsl color to hexa\n *\n * @param value - Styles value\n * @returns Transformed value\n */\nconst parseHsl = (value) => {\n    const hsl = colorString.get.hsl(value).map(Math.round);\n    const hex = hlsToHex(...hsl);\n    return hex.toUpperCase();\n};\n/**\n * Transform given color to hexa\n *\n * @param value - Styles value\n * @returns Transformed value\n */\nconst transformColor = (value) => {\n    if (isRgb(value))\n        return parseRgb(value);\n    if (isHsl(value))\n        return parseHsl(value);\n    return value;\n};\n\n/**\n * Parses scalar value in value and unit pairs\n *\n * @param value - Scalar value\n * @returns Parsed value\n */\nconst parseValue = (value) => {\n    if (typeof value === 'number')\n        return { value, unit: undefined };\n    const match = /^(-?\\d*\\.?\\d+)(in|mm|cm|pt|vh|vw|px|rem)?$/g.exec(value);\n    return match\n        ? { value: parseFloat(match[1]), unit: match[2] || 'pt' }\n        : { value, unit: undefined };\n};\n/**\n * Transform given scalar value\n *\n * @param container\n * @param value - Styles value\n * @returns Transformed value\n */\nconst transformUnit = (container, value) => {\n    const scalar = parseValue(value);\n    const outputDpi = 72;\n    const inputDpi = container.dpi || 72;\n    const mmFactor = (1 / 25.4) * outputDpi;\n    const cmFactor = (1 / 2.54) * outputDpi;\n    if (typeof scalar.value !== 'number')\n        return scalar.value;\n    switch (scalar.unit) {\n        case 'rem':\n            return scalar.value * (container.remBase || 18);\n        case 'in':\n            return scalar.value * outputDpi;\n        case 'mm':\n            return scalar.value * mmFactor;\n        case 'cm':\n            return scalar.value * cmFactor;\n        case 'vh':\n            return scalar.value * (container.height / 100);\n        case 'vw':\n            return scalar.value * (container.width / 100);\n        case 'px':\n            return Math.round(scalar.value * (outputDpi / inputDpi));\n        default:\n            return scalar.value;\n    }\n};\n\nconst processNumberValue = (key, value) => ({\n    [key]: parseFloat$1(value),\n});\nconst processUnitValue = (key, value, container) => ({\n    [key]: transformUnit(container, value),\n});\nconst processColorValue = (key, value) => {\n    const result = { [key]: transformColor(value) };\n    return result;\n};\nconst processNoopValue = (key, value) => ({\n    [key]: value,\n});\n\nconst BORDER_SHORTHAND_REGEX = /(-?\\d+(\\.\\d+)?(in|mm|cm|pt|vw|vh|px|rem)?)\\s(\\S+)\\s(.+)/;\nconst matchBorderShorthand = (value) => value.match(BORDER_SHORTHAND_REGEX) || [];\nconst resolveBorderShorthand = (key, value, container) => {\n    const match = matchBorderShorthand(`${value}`);\n    if (match) {\n        const widthMatch = match[1] || value;\n        const styleMatch = match[4] || value;\n        const colorMatch = match[5] || value;\n        const style = styleMatch;\n        const color = colorMatch ? transformColor(colorMatch) : undefined;\n        const width = widthMatch ? transformUnit(container, widthMatch) : undefined;\n        if (key.match(/(Top|Right|Bottom|Left)$/)) {\n            return {\n                [`${key}Color`]: color,\n                [`${key}Style`]: style,\n                [`${key}Width`]: width,\n            };\n        }\n        if (key.match(/Color$/)) {\n            return {\n                borderTopColor: color,\n                borderRightColor: color,\n                borderBottomColor: color,\n                borderLeftColor: color,\n            };\n        }\n        if (key.match(/Style$/)) {\n            if (typeof style === 'number')\n                throw new Error(`Invalid border style: ${style}`);\n            return {\n                borderTopStyle: style,\n                borderRightStyle: style,\n                borderBottomStyle: style,\n                borderLeftStyle: style,\n            };\n        }\n        if (key.match(/Width$/)) {\n            if (typeof width !== 'number')\n                throw new Error(`Invalid border width: ${width}`);\n            return {\n                borderTopWidth: width,\n                borderRightWidth: width,\n                borderBottomWidth: width,\n                borderLeftWidth: width,\n            };\n        }\n        if (key.match(/Radius$/)) {\n            const radius = value ? transformUnit(container, value) : undefined;\n            if (typeof radius !== 'number')\n                throw new Error(`Invalid border radius: ${radius}`);\n            return {\n                borderTopLeftRadius: radius,\n                borderTopRightRadius: radius,\n                borderBottomRightRadius: radius,\n                borderBottomLeftRadius: radius,\n            };\n        }\n        if (typeof width !== 'number')\n            throw new Error(`Invalid border width: ${width}`);\n        if (typeof style === 'number')\n            throw new Error(`Invalid border style: ${style}`);\n        return {\n            borderTopColor: color,\n            borderTopStyle: style,\n            borderTopWidth: width,\n            borderRightColor: color,\n            borderRightStyle: style,\n            borderRightWidth: width,\n            borderBottomColor: color,\n            borderBottomStyle: style,\n            borderBottomWidth: width,\n            borderLeftColor: color,\n            borderLeftStyle: style,\n            borderLeftWidth: width,\n        };\n    }\n    return { [key]: value };\n};\nconst handlers$b = {\n    border: (resolveBorderShorthand),\n    borderBottom: (resolveBorderShorthand),\n    borderBottomColor: (processColorValue),\n    borderBottomLeftRadius: (processUnitValue),\n    borderBottomRightRadius: (processUnitValue),\n    borderBottomStyle: (processNoopValue),\n    borderBottomWidth: (processUnitValue),\n    borderColor: (resolveBorderShorthand),\n    borderLeft: (resolveBorderShorthand),\n    borderLeftColor: (processColorValue),\n    borderLeftStyle: (processNoopValue),\n    borderLeftWidth: (processUnitValue),\n    borderRadius: (resolveBorderShorthand),\n    borderRight: (resolveBorderShorthand),\n    borderRightColor: (processColorValue),\n    borderRightStyle: (processNoopValue),\n    borderRightWidth: (processUnitValue),\n    borderStyle: (resolveBorderShorthand),\n    borderTop: (resolveBorderShorthand),\n    borderTopColor: (processColorValue),\n    borderTopLeftRadius: (processUnitValue),\n    borderTopRightRadius: (processUnitValue),\n    borderTopStyle: (processNoopValue),\n    borderTopWidth: (processUnitValue),\n    borderWidth: (resolveBorderShorthand),\n};\n\nconst handlers$a = {\n    backgroundColor: (processColorValue),\n    color: (processColorValue),\n    opacity: (processNumberValue),\n};\n\nconst handlers$9 = {\n    height: (processUnitValue),\n    maxHeight: (processUnitValue),\n    maxWidth: (processUnitValue),\n    minHeight: (processUnitValue),\n    minWidth: (processUnitValue),\n    width: (processUnitValue),\n};\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/flex#values\n// TODO: change flex defaults to [0, 1, 'auto'] as in spec in next major release\nconst flexDefaults = [1, 1, 0];\nconst flexAuto = [1, 1, 'auto'];\nconst processFlexShorthand = (key, value, container) => {\n    let defaults = flexDefaults;\n    let matches = [];\n    if (value === 'auto') {\n        defaults = flexAuto;\n    }\n    else {\n        matches = `${value}`.split(' ');\n    }\n    const flexGrow = parseFloat$1(matches[0] || defaults[0]);\n    const flexShrink = parseFloat$1(matches[1] || defaults[1]);\n    const flexBasis = transformUnit(container, matches[2] || defaults[2]);\n    return { flexGrow, flexShrink, flexBasis };\n};\nconst handlers$8 = {\n    alignContent: (processNoopValue),\n    alignItems: (processNoopValue),\n    alignSelf: (processNoopValue),\n    flex: (processFlexShorthand),\n    flexBasis: (processUnitValue),\n    flexDirection: (processNoopValue),\n    flexFlow: (processNoopValue),\n    flexGrow: (processNumberValue),\n    flexShrink: (processNumberValue),\n    flexWrap: (processNoopValue),\n    justifyContent: (processNoopValue),\n    justifySelf: (processNoopValue),\n};\n\nconst processGapShorthand = (key, value, container) => {\n    const match = `${value}`.split(' ');\n    const rowGap = transformUnit(container, match?.[0] || value);\n    const columnGap = transformUnit(container, match?.[1] || value);\n    return { rowGap, columnGap };\n};\nconst handlers$7 = {\n    gap: (processGapShorthand),\n    columnGap: (processUnitValue),\n    rowGap: (processUnitValue),\n};\n\nconst handlers$6 = {\n    aspectRatio: (processNumberValue),\n    bottom: (processUnitValue),\n    display: (processNoopValue),\n    left: (processUnitValue),\n    position: (processNoopValue),\n    right: (processUnitValue),\n    top: (processUnitValue),\n    overflow: (processNoopValue),\n    zIndex: (processNumberValue),\n};\n\nconst BOX_MODEL_UNITS = 'px,in,mm,cm,pt,%,vw,vh';\nconst logError = (style, value) => {\n    const name = style.toString();\n    // eslint-disable-next-line no-console\n    console.error(`\n    @react-pdf/stylesheet parsing error:\n    ${name}: ${value},\n    ${' '.repeat(name.length + 2)}^\n    Unsupported ${name} value format\n  `);\n};\n/**\n * @param options\n * @param [options.expandsTo]\n * @param [options.maxValues]\n * @param [options.autoSupported]\n */\nconst expandBoxModel = ({ expandsTo, maxValues = 1, autoSupported = false, } = {}) => (model, value, container) => {\n    const nodes = parse$1(`${value}`);\n    const parts = [];\n    for (let i = 0; i < nodes.length; i++) {\n        const node = nodes[i];\n        // value contains `calc`, `url` or other css function\n        // `,`, `/` or strings that unsupported by margin and padding\n        if (node.type === 'function' ||\n            node.type === 'string' ||\n            node.type === 'div') {\n            logError(model, value);\n            return {};\n        }\n        if (node.type === 'word') {\n            if (node.value === 'auto' && autoSupported) {\n                parts.push(node.value);\n            }\n            else {\n                const result = parseUnit(node.value);\n                // when unit isn't specified this condition is true\n                if (result && BOX_MODEL_UNITS.includes(result.unit)) {\n                    parts.push(node.value);\n                }\n                else {\n                    logError(model, value);\n                    return {};\n                }\n            }\n        }\n    }\n    // checks that we have enough parsed values\n    if (parts.length > maxValues) {\n        logError(model, value);\n        return {};\n    }\n    const first = transformUnit(container, parts[0]);\n    if (expandsTo) {\n        const second = transformUnit(container, parts[1] || parts[0]);\n        const third = transformUnit(container, parts[2] || parts[0]);\n        const fourth = transformUnit(container, parts[3] || parts[1] || parts[0]);\n        return expandsTo({ first, second, third, fourth });\n    }\n    return {\n        [model]: first,\n    };\n};\n\nconst processMargin = expandBoxModel({\n    expandsTo: ({ first, second, third, fourth }) => ({\n        marginTop: first,\n        marginRight: second,\n        marginBottom: third,\n        marginLeft: fourth,\n    }),\n    maxValues: 4,\n    autoSupported: true,\n});\nconst processMarginVertical = expandBoxModel({\n    expandsTo: ({ first, second }) => ({\n        marginTop: first,\n        marginBottom: second,\n    }),\n    maxValues: 2,\n    autoSupported: true,\n});\nconst processMarginHorizontal = expandBoxModel({\n    expandsTo: ({ first, second }) => ({\n        marginRight: first,\n        marginLeft: second,\n    }),\n    maxValues: 2,\n    autoSupported: true,\n});\nconst processMarginSingle = expandBoxModel({\n    autoSupported: true,\n});\nconst handlers$5 = {\n    margin: (processMargin),\n    marginBottom: (processMarginSingle),\n    marginHorizontal: (processMarginHorizontal),\n    marginLeft: (processMarginSingle),\n    marginRight: (processMarginSingle),\n    marginTop: (processMarginSingle),\n    marginVertical: (processMarginVertical),\n};\n\nconst processPadding = expandBoxModel({\n    expandsTo: ({ first, second, third, fourth }) => ({\n        paddingTop: first,\n        paddingRight: second,\n        paddingBottom: third,\n        paddingLeft: fourth,\n    }),\n    maxValues: 4,\n});\nconst processPaddingVertical = expandBoxModel({\n    expandsTo: ({ first, second }) => ({\n        paddingTop: first,\n        paddingBottom: second,\n    }),\n    maxValues: 2,\n});\nconst processPaddingHorizontal = expandBoxModel({\n    expandsTo: ({ first, second }) => ({\n        paddingRight: first,\n        paddingLeft: second,\n    }),\n    maxValues: 2,\n});\nconst processPaddingSingle = expandBoxModel();\nconst handlers$4 = {\n    padding: (processPadding),\n    paddingBottom: (processPaddingSingle),\n    paddingHorizontal: (processPaddingHorizontal),\n    paddingLeft: (processPaddingSingle),\n    paddingRight: (processPaddingSingle),\n    paddingTop: (processPaddingSingle),\n    paddingVertical: (processPaddingVertical),\n};\n\nconst offsetKeyword = (value) => {\n    switch (value) {\n        case 'top':\n        case 'left':\n            return '0%';\n        case 'right':\n        case 'bottom':\n            return '100%';\n        case 'center':\n            return '50%';\n        default:\n            return value;\n    }\n};\n\nconst processObjectPosition = (key, value, container) => {\n    const match = `${value}`.split(' ');\n    const objectPositionX = offsetKeyword(transformUnit(container, match?.[0] || value));\n    const objectPositionY = offsetKeyword(transformUnit(container, match?.[1] || value));\n    return { objectPositionX, objectPositionY };\n};\nconst processObjectPositionValue = (key, value, container) => ({\n    [key]: offsetKeyword(transformUnit(container, value)),\n});\nconst handlers$3 = {\n    objectPosition: (processObjectPosition),\n    objectPositionX: (processObjectPositionValue),\n    objectPositionY: (processObjectPositionValue),\n    objectFit: (processNoopValue),\n};\n\nconst castInt = (value) => {\n    if (typeof value === 'number')\n        return value;\n    return parseInt(value, 10);\n};\n\nconst FONT_WEIGHTS = {\n    thin: 100,\n    hairline: 100,\n    ultralight: 200,\n    extralight: 200,\n    light: 300,\n    normal: 400,\n    medium: 500,\n    semibold: 600,\n    demibold: 600,\n    bold: 700,\n    ultrabold: 800,\n    extrabold: 800,\n    heavy: 900,\n    black: 900,\n};\nconst transformFontWeight = (value) => {\n    if (!value)\n        return FONT_WEIGHTS.normal;\n    if (typeof value === 'number')\n        return value;\n    const lv = value.toLowerCase();\n    if (FONT_WEIGHTS[lv])\n        return FONT_WEIGHTS[lv];\n    return castInt(value);\n};\nconst processFontWeight = (key, value) => {\n    return { [key]: transformFontWeight(value) };\n};\nconst transformLineHeight = (value, styles, container) => {\n    if (value === '')\n        return value;\n    const fontSize = transformUnit(container, styles.fontSize || 18);\n    const lineHeight = transformUnit(container, value);\n    // Percent values: use this number multiplied by the element's font size\n    const { percent } = matchPercent(lineHeight) || {};\n    if (percent)\n        return percent * fontSize;\n    // Unitless values: use this number multiplied by the element's font size\n    return isNaN(value) ? lineHeight : lineHeight * fontSize;\n};\nconst processLineHeight = (key, value, container, styles) => {\n    return {\n        [key]: transformLineHeight(value, styles, container),\n    };\n};\nconst handlers$2 = {\n    direction: (processNoopValue),\n    fontFamily: (processNoopValue),\n    fontSize: (processUnitValue),\n    fontStyle: (processNoopValue),\n    fontWeight: (processFontWeight),\n    letterSpacing: (processUnitValue),\n    lineHeight: (processLineHeight),\n    maxLines: (processNumberValue),\n    textAlign: (processNoopValue),\n    textDecoration: (processNoopValue),\n    textDecorationColor: (processColorValue),\n    textDecorationStyle: (processNoopValue),\n    textIndent: (processNoopValue),\n    textOverflow: (processNoopValue),\n    textTransform: (processNoopValue),\n    verticalAlign: (processNoopValue),\n};\n\nconst matchNumber = (value) => typeof value === 'string' && /^-?\\d*\\.?\\d*$/.test(value);\nconst castFloat = (value) => {\n    if (typeof value !== 'string')\n        return value;\n    if (matchNumber(value))\n        return parseFloat(value);\n    return value;\n};\n\nconst parse = (transformString) => {\n    const transforms = transformString.trim().split(/\\)[ ,]|\\)/);\n    // Handle \"initial\", \"inherit\", \"unset\".\n    if (transforms.length === 1) {\n        return [[transforms[0], true]];\n    }\n    const parsed = [];\n    for (let i = 0; i < transforms.length; i += 1) {\n        const transform = transforms[i];\n        if (transform) {\n            const [name, rawValue] = transform.split('(');\n            const splitChar = rawValue.indexOf(',') >= 0 ? ',' : ' ';\n            const value = rawValue.split(splitChar).map((val) => val.trim());\n            parsed.push({ operation: name.trim(), value });\n        }\n    }\n    return parsed;\n};\nconst parseAngle = (value) => {\n    const unitsRegexp = /(-?\\d*\\.?\\d*)(\\w*)?/i;\n    const [, angle, unit] = unitsRegexp.exec(value);\n    const number = Number.parseFloat(angle);\n    return unit === 'rad' ? (number * 180) / Math.PI : number;\n};\nconst normalizeTransformOperation = ({ operation, value }) => {\n    switch (operation) {\n        case 'scale': {\n            const [scaleX, scaleY = scaleX] = value.map((num) => Number.parseFloat(num));\n            return { operation: 'scale', value: [scaleX, scaleY] };\n        }\n        case 'scaleX': {\n            return { operation: 'scale', value: [Number.parseFloat(value), 1] };\n        }\n        case 'scaleY': {\n            return { operation: 'scale', value: [1, Number.parseFloat(value)] };\n        }\n        case 'rotate': {\n            return { operation: 'rotate', value: [parseAngle(value)] };\n        }\n        case 'translate': {\n            return {\n                operation: 'translate',\n                value: value.map((num) => Number.parseFloat(num)),\n            };\n        }\n        case 'translateX': {\n            return {\n                operation: 'translate',\n                value: [Number.parseFloat(value), 0],\n            };\n        }\n        case 'translateY': {\n            return { operation: 'translate', value: [0, Number.parseFloat(value)] };\n        }\n        case 'skew': {\n            return { operation: 'skew', value: value.map(parseAngle) };\n        }\n        case 'skewX': {\n            return { operation: 'skew', value: [parseAngle(value), 0] };\n        }\n        case 'skewY': {\n            return { operation: 'skew', value: [0, parseAngle(value)] };\n        }\n        default: {\n            return { operation, value: value.map((num) => Number.parseFloat(num)) };\n        }\n    }\n};\nconst normalize = (operations) => {\n    return operations.map((operation) => normalizeTransformOperation(operation));\n};\nconst processTransform = (key, value) => {\n    if (typeof value !== 'string')\n        return { [key]: value };\n    return { [key]: normalize(parse(value)) };\n};\nconst Y_AXIS_SHORTHANDS = { top: true, bottom: true };\nconst sortTransformOriginPair = (a, b) => {\n    if (Y_AXIS_SHORTHANDS[a])\n        return 1;\n    if (Y_AXIS_SHORTHANDS[b])\n        return -1;\n    return 0;\n};\nconst getTransformOriginPair = (values) => {\n    if (!values || values.length === 0)\n        return ['center', 'center'];\n    const pair = values.length === 1 ? [values[0], 'center'] : values;\n    return pair.sort(sortTransformOriginPair);\n};\n// Transforms shorthand transformOrigin values\nconst processTransformOriginShorthand = (key, value, container) => {\n    const match = `${value}`.split(' ');\n    const pair = getTransformOriginPair(match);\n    const transformOriginX = transformUnit(container, pair[0]);\n    const transformOriginY = transformUnit(container, pair[1]);\n    return {\n        transformOriginX: offsetKeyword(transformOriginX) || castFloat(transformOriginX),\n        transformOriginY: offsetKeyword(transformOriginY) || castFloat(transformOriginY),\n    };\n};\nconst processTransformOriginValue = (key, value, container) => {\n    const v = transformUnit(container, value);\n    return { [key]: offsetKeyword(v) || castFloat(v) };\n};\nconst handlers$1 = {\n    transform: processTransform,\n    gradientTransform: processTransform,\n    transformOrigin: (processTransformOriginShorthand),\n    transformOriginX: (processTransformOriginValue),\n    transformOriginY: (processTransformOriginValue),\n};\n\nconst handlers = {\n    fill: (processColorValue),\n    stroke: (processColorValue),\n    strokeDasharray: (processNoopValue),\n    strokeWidth: (processUnitValue),\n    fillOpacity: (processNumberValue),\n    strokeOpacity: (processNumberValue),\n    fillRule: (processNoopValue),\n    textAnchor: (processNoopValue),\n    strokeLinecap: (processNoopValue),\n    strokeLinejoin: (processNoopValue),\n    visibility: (processNoopValue),\n    clipPath: (processNoopValue),\n    dominantBaseline: (processNoopValue),\n};\n\nconst shorthands = {\n    ...handlers$b,\n    ...handlers$a,\n    ...handlers$9,\n    ...handlers$8,\n    ...handlers$7,\n    ...handlers$6,\n    ...handlers$5,\n    ...handlers$4,\n    ...handlers$3,\n    ...handlers$2,\n    ...handlers$1,\n    ...handlers,\n};\n/**\n * Expand the shorthand properties.\n *\n * @param style - Style object\n * @returns Expanded style object\n */\nconst resolve = (container) => (style) => {\n    const propsArray = Object.keys(style);\n    const resolvedStyle = {};\n    for (let i = 0; i < propsArray.length; i += 1) {\n        const key = propsArray[i];\n        const value = style[key];\n        if (!shorthands[key]) {\n            resolvedStyle[key] = value;\n            continue;\n        }\n        const resolved = shorthands[key](key, value, container, style);\n        const keys = Object.keys(resolved);\n        for (let j = 0; j < keys.length; j += 1) {\n            const propName = keys[j];\n            const propValue = resolved[propName];\n            resolvedStyle[propName] = propValue;\n        }\n    }\n    return resolvedStyle;\n};\n\n/**\n * Resolves styles\n *\n * @param container\n * @param style - Style\n * @returns Resolved style\n */\nconst resolveStyles = (container, style) => {\n    const computeMediaQueries = (value) => resolveMediaQueries(container, value);\n    return compose(resolve(container), computeMediaQueries, flatten)(style);\n};\n\nexport { resolveStyles as default, flatten, transformColor };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA;;;;;CAKC,GACD,MAAM,UAAU,CAAC,QAAU,MAAM,MAAM,CAAC;AACxC;;;;;CAKC,GACD,MAAM,cAAc,CAAC,SAAW,OAAO,MAAM,CAAC,CAAC,KAAK;QAChD,MAAM,IAAI,MAAM,OAAO,CAAC,SAAS,QAAQ,SAAS;QAClD,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;YACpB,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,IAAI,KAAK,WAAW;gBACzC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI;YACrB;QACJ;QACA,OAAO;IACX,GAAG,CAAC;AACJ;;;;;CAKC,GACD,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,aAAa,SAAU,wJAAA,CAAA,YAAS;AAExD;;;;;;CAMC,GACD,MAAM,sBAAsB,CAAC,WAAW;IACpC,OAAO,OAAO,IAAI,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK;QACnC,IAAI,SAAS,IAAI,CAAC,MAAM;YACpB,OAAO;gBACH,GAAG,GAAG;gBACN,GAAG,CAAA,GAAA,kJAAA,CAAA,UAAU,AAAD,EAAE;oBAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI;gBAAC,GAAG,UAAU;YACnD;QACJ;QACA,OAAO;YAAE,GAAG,GAAG;YAAE,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI;QAAC;IACvC,GAAG,CAAC;AACR;AAEA,MAAM,QAAQ,CAAC,QAAU,SAAS,IAAI,CAAC;AACvC,MAAM,QAAQ,CAAC,QAAU,SAAS,IAAI,CAAC;AACvC;;;;;CAKC,GACD,MAAM,WAAW,CAAC;IACd,MAAM,MAAM,2IAAA,CAAA,UAAW,CAAC,GAAG,CAAC,GAAG,CAAC;IAChC,OAAO,2IAAA,CAAA,UAAW,CAAC,EAAE,CAAC,GAAG,CAAC;AAC9B;AACA;;;;;CAKC,GACD,MAAM,WAAW,CAAC;IACd,MAAM,MAAM,2IAAA,CAAA,UAAW,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,KAAK,KAAK;IACrD,MAAM,MAAM,CAAA,GAAA,4IAAA,CAAA,UAAQ,AAAD,KAAK;IACxB,OAAO,IAAI,WAAW;AAC1B;AACA;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC;IACpB,IAAI,MAAM,QACN,OAAO,SAAS;IACpB,IAAI,MAAM,QACN,OAAO,SAAS;IACpB,OAAO;AACX;AAEA;;;;;CAKC,GACD,MAAM,aAAa,CAAC;IAChB,IAAI,OAAO,UAAU,UACjB,OAAO;QAAE;QAAO,MAAM;IAAU;IACpC,MAAM,QAAQ,8CAA8C,IAAI,CAAC;IACjE,OAAO,QACD;QAAE,OAAO,WAAW,KAAK,CAAC,EAAE;QAAG,MAAM,KAAK,CAAC,EAAE,IAAI;IAAK,IACtD;QAAE;QAAO,MAAM;IAAU;AACnC;AACA;;;;;;CAMC,GACD,MAAM,gBAAgB,CAAC,WAAW;IAC9B,MAAM,SAAS,WAAW;IAC1B,MAAM,YAAY;IAClB,MAAM,WAAW,UAAU,GAAG,IAAI;IAClC,MAAM,WAAW,AAAC,IAAI,OAAQ;IAC9B,MAAM,WAAW,AAAC,IAAI,OAAQ;IAC9B,IAAI,OAAO,OAAO,KAAK,KAAK,UACxB,OAAO,OAAO,KAAK;IACvB,OAAQ,OAAO,IAAI;QACf,KAAK;YACD,OAAO,OAAO,KAAK,GAAG,CAAC,UAAU,OAAO,IAAI,EAAE;QAClD,KAAK;YACD,OAAO,OAAO,KAAK,GAAG;QAC1B,KAAK;YACD,OAAO,OAAO,KAAK,GAAG;QAC1B,KAAK;YACD,OAAO,OAAO,KAAK,GAAG;QAC1B,KAAK;YACD,OAAO,OAAO,KAAK,GAAG,CAAC,UAAU,MAAM,GAAG,GAAG;QACjD,KAAK;YACD,OAAO,OAAO,KAAK,GAAG,CAAC,UAAU,KAAK,GAAG,GAAG;QAChD,KAAK;YACD,OAAO,KAAK,KAAK,CAAC,OAAO,KAAK,GAAG,CAAC,YAAY,QAAQ;QAC1D;YACI,OAAO,OAAO,KAAK;IAC3B;AACJ;AAEA,MAAM,qBAAqB,CAAC,KAAK,QAAU,CAAC;QACxC,CAAC,IAAI,EAAE,CAAA,GAAA,wJAAA,CAAA,aAAY,AAAD,EAAE;IACxB,CAAC;AACD,MAAM,mBAAmB,CAAC,KAAK,OAAO,YAAc,CAAC;QACjD,CAAC,IAAI,EAAE,cAAc,WAAW;IACpC,CAAC;AACD,MAAM,oBAAoB,CAAC,KAAK;IAC5B,MAAM,SAAS;QAAE,CAAC,IAAI,EAAE,eAAe;IAAO;IAC9C,OAAO;AACX;AACA,MAAM,mBAAmB,CAAC,KAAK,QAAU,CAAC;QACtC,CAAC,IAAI,EAAE;IACX,CAAC;AAED,MAAM,yBAAyB;AAC/B,MAAM,uBAAuB,CAAC,QAAU,MAAM,KAAK,CAAC,2BAA2B,EAAE;AACjF,MAAM,yBAAyB,CAAC,KAAK,OAAO;IACxC,MAAM,QAAQ,qBAAqB,GAAG,OAAO;IAC7C,wCAAW;QACP,MAAM,aAAa,KAAK,CAAC,EAAE,IAAI;QAC/B,MAAM,aAAa,KAAK,CAAC,EAAE,IAAI;QAC/B,MAAM,aAAa,KAAK,CAAC,EAAE,IAAI;QAC/B,MAAM,QAAQ;QACd,MAAM,QAAQ,aAAa,eAAe,cAAc;QACxD,MAAM,QAAQ,aAAa,cAAc,WAAW,cAAc;QAClE,IAAI,IAAI,KAAK,CAAC,6BAA6B;YACvC,OAAO;gBACH,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE;gBACjB,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE;gBACjB,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE;YACrB;QACJ;QACA,IAAI,IAAI,KAAK,CAAC,WAAW;YACrB,OAAO;gBACH,gBAAgB;gBAChB,kBAAkB;gBAClB,mBAAmB;gBACnB,iBAAiB;YACrB;QACJ;QACA,IAAI,IAAI,KAAK,CAAC,WAAW;YACrB,IAAI,OAAO,UAAU,UACjB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,OAAO;YACpD,OAAO;gBACH,gBAAgB;gBAChB,kBAAkB;gBAClB,mBAAmB;gBACnB,iBAAiB;YACrB;QACJ;QACA,IAAI,IAAI,KAAK,CAAC,WAAW;YACrB,IAAI,OAAO,UAAU,UACjB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,OAAO;YACpD,OAAO;gBACH,gBAAgB;gBAChB,kBAAkB;gBAClB,mBAAmB;gBACnB,iBAAiB;YACrB;QACJ;QACA,IAAI,IAAI,KAAK,CAAC,YAAY;YACtB,MAAM,SAAS,QAAQ,cAAc,WAAW,SAAS;YACzD,IAAI,OAAO,WAAW,UAClB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,QAAQ;YACtD,OAAO;gBACH,qBAAqB;gBACrB,sBAAsB;gBACtB,yBAAyB;gBACzB,wBAAwB;YAC5B;QACJ;QACA,IAAI,OAAO,UAAU,UACjB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,OAAO;QACpD,IAAI,OAAO,UAAU,UACjB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,OAAO;QACpD,OAAO;YACH,gBAAgB;YAChB,gBAAgB;YAChB,gBAAgB;YAChB,kBAAkB;YAClB,kBAAkB;YAClB,kBAAkB;YAClB,mBAAmB;YACnB,mBAAmB;YACnB,mBAAmB;YACnB,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB;QACrB;IACJ;;AAEJ;AACA,MAAM,aAAa;IACf,QAAS;IACT,cAAe;IACf,mBAAoB;IACpB,wBAAyB;IACzB,yBAA0B;IAC1B,mBAAoB;IACpB,mBAAoB;IACpB,aAAc;IACd,YAAa;IACb,iBAAkB;IAClB,iBAAkB;IAClB,iBAAkB;IAClB,cAAe;IACf,aAAc;IACd,kBAAmB;IACnB,kBAAmB;IACnB,kBAAmB;IACnB,aAAc;IACd,WAAY;IACZ,gBAAiB;IACjB,qBAAsB;IACtB,sBAAuB;IACvB,gBAAiB;IACjB,gBAAiB;IACjB,aAAc;AAClB;AAEA,MAAM,aAAa;IACf,iBAAkB;IAClB,OAAQ;IACR,SAAU;AACd;AAEA,MAAM,aAAa;IACf,QAAS;IACT,WAAY;IACZ,UAAW;IACX,WAAY;IACZ,UAAW;IACX,OAAQ;AACZ;AAEA,+DAA+D;AAC/D,gFAAgF;AAChF,MAAM,eAAe;IAAC;IAAG;IAAG;CAAE;AAC9B,MAAM,WAAW;IAAC;IAAG;IAAG;CAAO;AAC/B,MAAM,uBAAuB,CAAC,KAAK,OAAO;IACtC,IAAI,WAAW;IACf,IAAI,UAAU,EAAE;IAChB,IAAI,UAAU,QAAQ;QAClB,WAAW;IACf,OACK;QACD,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC;IAC/B;IACA,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,aAAY,AAAD,EAAE,OAAO,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE;IACvD,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,aAAY,AAAD,EAAE,OAAO,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE;IACzD,MAAM,YAAY,cAAc,WAAW,OAAO,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE;IACpE,OAAO;QAAE;QAAU;QAAY;IAAU;AAC7C;AACA,MAAM,aAAa;IACf,cAAe;IACf,YAAa;IACb,WAAY;IACZ,MAAO;IACP,WAAY;IACZ,eAAgB;IAChB,UAAW;IACX,UAAW;IACX,YAAa;IACb,UAAW;IACX,gBAAiB;IACjB,aAAc;AAClB;AAEA,MAAM,sBAAsB,CAAC,KAAK,OAAO;IACrC,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC;IAC/B,MAAM,SAAS,cAAc,WAAW,OAAO,CAAC,EAAE,IAAI;IACtD,MAAM,YAAY,cAAc,WAAW,OAAO,CAAC,EAAE,IAAI;IACzD,OAAO;QAAE;QAAQ;IAAU;AAC/B;AACA,MAAM,aAAa;IACf,KAAM;IACN,WAAY;IACZ,QAAS;AACb;AAEA,MAAM,aAAa;IACf,aAAc;IACd,QAAS;IACT,SAAU;IACV,MAAO;IACP,UAAW;IACX,OAAQ;IACR,KAAM;IACN,UAAW;IACX,QAAS;AACb;AAEA,MAAM,kBAAkB;AACxB,MAAM,WAAW,CAAC,OAAO;IACrB,MAAM,OAAO,MAAM,QAAQ;IAC3B,sCAAsC;IACtC,QAAQ,KAAK,CAAC,CAAC;;IAEf,EAAE,KAAK,EAAE,EAAE,MAAM;IACjB,EAAE,IAAI,MAAM,CAAC,KAAK,MAAM,GAAG,GAAG;gBAClB,EAAE,KAAK;EACrB,CAAC;AACH;AACA;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,EAAE,gBAAgB,KAAK,EAAG,GAAG,CAAC,CAAC,GAAK,CAAC,OAAO,OAAO;QACjG,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,GAAG,OAAO;QAChC,MAAM,QAAQ,EAAE;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,qDAAqD;YACrD,6DAA6D;YAC7D,IAAI,KAAK,IAAI,KAAK,cACd,KAAK,IAAI,KAAK,YACd,KAAK,IAAI,KAAK,OAAO;gBACrB,SAAS,OAAO;gBAChB,OAAO,CAAC;YACZ;YACA,IAAI,KAAK,IAAI,KAAK,QAAQ;gBACtB,IAAI,KAAK,KAAK,KAAK,UAAU,eAAe;oBACxC,MAAM,IAAI,CAAC,KAAK,KAAK;gBACzB,OACK;oBACD,MAAM,SAAS,CAAA,GAAA,4JAAA,CAAA,UAAS,AAAD,EAAE,KAAK,KAAK;oBACnC,mDAAmD;oBACnD,IAAI,UAAU,gBAAgB,QAAQ,CAAC,OAAO,IAAI,GAAG;wBACjD,MAAM,IAAI,CAAC,KAAK,KAAK;oBACzB,OACK;wBACD,SAAS,OAAO;wBAChB,OAAO,CAAC;oBACZ;gBACJ;YACJ;QACJ;QACA,2CAA2C;QAC3C,IAAI,MAAM,MAAM,GAAG,WAAW;YAC1B,SAAS,OAAO;YAChB,OAAO,CAAC;QACZ;QACA,MAAM,QAAQ,cAAc,WAAW,KAAK,CAAC,EAAE;QAC/C,IAAI,WAAW;YACX,MAAM,SAAS,cAAc,WAAW,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE;YAC5D,MAAM,QAAQ,cAAc,WAAW,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE;YAC3D,MAAM,SAAS,cAAc,WAAW,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE;YACxE,OAAO,UAAU;gBAAE;gBAAO;gBAAQ;gBAAO;YAAO;QACpD;QACA,OAAO;YACH,CAAC,MAAM,EAAE;QACb;IACJ;AAEA,MAAM,gBAAgB,eAAe;IACjC,WAAW,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAK,CAAC;YAC9C,WAAW;YACX,aAAa;YACb,cAAc;YACd,YAAY;QAChB,CAAC;IACD,WAAW;IACX,eAAe;AACnB;AACA,MAAM,wBAAwB,eAAe;IACzC,WAAW,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,GAAK,CAAC;YAC/B,WAAW;YACX,cAAc;QAClB,CAAC;IACD,WAAW;IACX,eAAe;AACnB;AACA,MAAM,0BAA0B,eAAe;IAC3C,WAAW,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,GAAK,CAAC;YAC/B,aAAa;YACb,YAAY;QAChB,CAAC;IACD,WAAW;IACX,eAAe;AACnB;AACA,MAAM,sBAAsB,eAAe;IACvC,eAAe;AACnB;AACA,MAAM,aAAa;IACf,QAAS;IACT,cAAe;IACf,kBAAmB;IACnB,YAAa;IACb,aAAc;IACd,WAAY;IACZ,gBAAiB;AACrB;AAEA,MAAM,iBAAiB,eAAe;IAClC,WAAW,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAK,CAAC;YAC9C,YAAY;YACZ,cAAc;YACd,eAAe;YACf,aAAa;QACjB,CAAC;IACD,WAAW;AACf;AACA,MAAM,yBAAyB,eAAe;IAC1C,WAAW,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,GAAK,CAAC;YAC/B,YAAY;YACZ,eAAe;QACnB,CAAC;IACD,WAAW;AACf;AACA,MAAM,2BAA2B,eAAe;IAC5C,WAAW,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,GAAK,CAAC;YAC/B,cAAc;YACd,aAAa;QACjB,CAAC;IACD,WAAW;AACf;AACA,MAAM,uBAAuB;AAC7B,MAAM,aAAa;IACf,SAAU;IACV,eAAgB;IAChB,mBAAoB;IACpB,aAAc;IACd,cAAe;IACf,YAAa;IACb,iBAAkB;AACtB;AAEA,MAAM,gBAAgB,CAAC;IACnB,OAAQ;QACJ,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AAEA,MAAM,wBAAwB,CAAC,KAAK,OAAO;IACvC,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC;IAC/B,MAAM,kBAAkB,cAAc,cAAc,WAAW,OAAO,CAAC,EAAE,IAAI;IAC7E,MAAM,kBAAkB,cAAc,cAAc,WAAW,OAAO,CAAC,EAAE,IAAI;IAC7E,OAAO;QAAE;QAAiB;IAAgB;AAC9C;AACA,MAAM,6BAA6B,CAAC,KAAK,OAAO,YAAc,CAAC;QAC3D,CAAC,IAAI,EAAE,cAAc,cAAc,WAAW;IAClD,CAAC;AACD,MAAM,aAAa;IACf,gBAAiB;IACjB,iBAAkB;IAClB,iBAAkB;IAClB,WAAY;AAChB;AAEA,MAAM,UAAU,CAAC;IACb,IAAI,OAAO,UAAU,UACjB,OAAO;IACX,OAAO,SAAS,OAAO;AAC3B;AAEA,MAAM,eAAe;IACjB,MAAM;IACN,UAAU;IACV,YAAY;IACZ,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,UAAU;IACV,MAAM;IACN,WAAW;IACX,WAAW;IACX,OAAO;IACP,OAAO;AACX;AACA,MAAM,sBAAsB,CAAC;IACzB,IAAI,CAAC,OACD,OAAO,aAAa,MAAM;IAC9B,IAAI,OAAO,UAAU,UACjB,OAAO;IACX,MAAM,KAAK,MAAM,WAAW;IAC5B,IAAI,YAAY,CAAC,GAAG,EAChB,OAAO,YAAY,CAAC,GAAG;IAC3B,OAAO,QAAQ;AACnB;AACA,MAAM,oBAAoB,CAAC,KAAK;IAC5B,OAAO;QAAE,CAAC,IAAI,EAAE,oBAAoB;IAAO;AAC/C;AACA,MAAM,sBAAsB,CAAC,OAAO,QAAQ;IACxC,IAAI,UAAU,IACV,OAAO;IACX,MAAM,WAAW,cAAc,WAAW,OAAO,QAAQ,IAAI;IAC7D,MAAM,aAAa,cAAc,WAAW;IAC5C,wEAAwE;IACxE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,eAAY,AAAD,EAAE,eAAe,CAAC;IACjD,IAAI,SACA,OAAO,UAAU;IACrB,yEAAyE;IACzE,OAAO,MAAM,SAAS,aAAa,aAAa;AACpD;AACA,MAAM,oBAAoB,CAAC,KAAK,OAAO,WAAW;IAC9C,OAAO;QACH,CAAC,IAAI,EAAE,oBAAoB,OAAO,QAAQ;IAC9C;AACJ;AACA,MAAM,aAAa;IACf,WAAY;IACZ,YAAa;IACb,UAAW;IACX,WAAY;IACZ,YAAa;IACb,eAAgB;IAChB,YAAa;IACb,UAAW;IACX,WAAY;IACZ,gBAAiB;IACjB,qBAAsB;IACtB,qBAAsB;IACtB,YAAa;IACb,cAAe;IACf,eAAgB;IAChB,eAAgB;AACpB;AAEA,MAAM,cAAc,CAAC,QAAU,OAAO,UAAU,YAAY,gBAAgB,IAAI,CAAC;AACjF,MAAM,YAAY,CAAC;IACf,IAAI,OAAO,UAAU,UACjB,OAAO;IACX,IAAI,YAAY,QACZ,OAAO,WAAW;IACtB,OAAO;AACX;AAEA,MAAM,QAAQ,CAAC;IACX,MAAM,aAAa,gBAAgB,IAAI,GAAG,KAAK,CAAC;IAChD,wCAAwC;IACxC,IAAI,WAAW,MAAM,KAAK,GAAG;QACzB,OAAO;YAAC;gBAAC,UAAU,CAAC,EAAE;gBAAE;aAAK;SAAC;IAClC;IACA,MAAM,SAAS,EAAE;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,KAAK,EAAG;QAC3C,MAAM,YAAY,UAAU,CAAC,EAAE;QAC/B,IAAI,WAAW;YACX,MAAM,CAAC,MAAM,SAAS,GAAG,UAAU,KAAK,CAAC;YACzC,MAAM,YAAY,SAAS,OAAO,CAAC,QAAQ,IAAI,MAAM;YACrD,MAAM,QAAQ,SAAS,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI;YAC7D,OAAO,IAAI,CAAC;gBAAE,WAAW,KAAK,IAAI;gBAAI;YAAM;QAChD;IACJ;IACA,OAAO;AACX;AACA,MAAM,aAAa,CAAC;IAChB,MAAM,cAAc;IACpB,MAAM,GAAG,OAAO,KAAK,GAAG,YAAY,IAAI,CAAC;IACzC,MAAM,SAAS,OAAO,UAAU,CAAC;IACjC,OAAO,SAAS,QAAQ,AAAC,SAAS,MAAO,KAAK,EAAE,GAAG;AACvD;AACA,MAAM,8BAA8B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE;IACrD,OAAQ;QACJ,KAAK;YAAS;gBACV,MAAM,CAAC,QAAQ,SAAS,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,MAAQ,OAAO,UAAU,CAAC;gBACvE,OAAO;oBAAE,WAAW;oBAAS,OAAO;wBAAC;wBAAQ;qBAAO;gBAAC;YACzD;QACA,KAAK;YAAU;gBACX,OAAO;oBAAE,WAAW;oBAAS,OAAO;wBAAC,OAAO,UAAU,CAAC;wBAAQ;qBAAE;gBAAC;YACtE;QACA,KAAK;YAAU;gBACX,OAAO;oBAAE,WAAW;oBAAS,OAAO;wBAAC;wBAAG,OAAO,UAAU,CAAC;qBAAO;gBAAC;YACtE;QACA,KAAK;YAAU;gBACX,OAAO;oBAAE,WAAW;oBAAU,OAAO;wBAAC,WAAW;qBAAO;gBAAC;YAC7D;QACA,KAAK;YAAa;gBACd,OAAO;oBACH,WAAW;oBACX,OAAO,MAAM,GAAG,CAAC,CAAC,MAAQ,OAAO,UAAU,CAAC;gBAChD;YACJ;QACA,KAAK;YAAc;gBACf,OAAO;oBACH,WAAW;oBACX,OAAO;wBAAC,OAAO,UAAU,CAAC;wBAAQ;qBAAE;gBACxC;YACJ;QACA,KAAK;YAAc;gBACf,OAAO;oBAAE,WAAW;oBAAa,OAAO;wBAAC;wBAAG,OAAO,UAAU,CAAC;qBAAO;gBAAC;YAC1E;QACA,KAAK;YAAQ;gBACT,OAAO;oBAAE,WAAW;oBAAQ,OAAO,MAAM,GAAG,CAAC;gBAAY;YAC7D;QACA,KAAK;YAAS;gBACV,OAAO;oBAAE,WAAW;oBAAQ,OAAO;wBAAC,WAAW;wBAAQ;qBAAE;gBAAC;YAC9D;QACA,KAAK;YAAS;gBACV,OAAO;oBAAE,WAAW;oBAAQ,OAAO;wBAAC;wBAAG,WAAW;qBAAO;gBAAC;YAC9D;QACA;YAAS;gBACL,OAAO;oBAAE;oBAAW,OAAO,MAAM,GAAG,CAAC,CAAC,MAAQ,OAAO,UAAU,CAAC;gBAAM;YAC1E;IACJ;AACJ;AACA,MAAM,YAAY,CAAC;IACf,OAAO,WAAW,GAAG,CAAC,CAAC,YAAc,4BAA4B;AACrE;AACA,MAAM,mBAAmB,CAAC,KAAK;IAC3B,IAAI,OAAO,UAAU,UACjB,OAAO;QAAE,CAAC,IAAI,EAAE;IAAM;IAC1B,OAAO;QAAE,CAAC,IAAI,EAAE,UAAU,MAAM;IAAQ;AAC5C;AACA,MAAM,oBAAoB;IAAE,KAAK;IAAM,QAAQ;AAAK;AACpD,MAAM,0BAA0B,CAAC,GAAG;IAChC,IAAI,iBAAiB,CAAC,EAAE,EACpB,OAAO;IACX,IAAI,iBAAiB,CAAC,EAAE,EACpB,OAAO,CAAC;IACZ,OAAO;AACX;AACA,MAAM,yBAAyB,CAAC;IAC5B,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,GAC7B,OAAO;QAAC;QAAU;KAAS;IAC/B,MAAM,OAAO,OAAO,MAAM,KAAK,IAAI;QAAC,MAAM,CAAC,EAAE;QAAE;KAAS,GAAG;IAC3D,OAAO,KAAK,IAAI,CAAC;AACrB;AACA,8CAA8C;AAC9C,MAAM,kCAAkC,CAAC,KAAK,OAAO;IACjD,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC;IAC/B,MAAM,OAAO,uBAAuB;IACpC,MAAM,mBAAmB,cAAc,WAAW,IAAI,CAAC,EAAE;IACzD,MAAM,mBAAmB,cAAc,WAAW,IAAI,CAAC,EAAE;IACzD,OAAO;QACH,kBAAkB,cAAc,qBAAqB,UAAU;QAC/D,kBAAkB,cAAc,qBAAqB,UAAU;IACnE;AACJ;AACA,MAAM,8BAA8B,CAAC,KAAK,OAAO;IAC7C,MAAM,IAAI,cAAc,WAAW;IACnC,OAAO;QAAE,CAAC,IAAI,EAAE,cAAc,MAAM,UAAU;IAAG;AACrD;AACA,MAAM,aAAa;IACf,WAAW;IACX,mBAAmB;IACnB,iBAAkB;IAClB,kBAAmB;IACnB,kBAAmB;AACvB;AAEA,MAAM,WAAW;IACb,MAAO;IACP,QAAS;IACT,iBAAkB;IAClB,aAAc;IACd,aAAc;IACd,eAAgB;IAChB,UAAW;IACX,YAAa;IACb,eAAgB;IAChB,gBAAiB;IACjB,YAAa;IACb,UAAW;IACX,kBAAmB;AACvB;AAEA,MAAM,aAAa;IACf,GAAG,UAAU;IACb,GAAG,UAAU;IACb,GAAG,UAAU;IACb,GAAG,UAAU;IACb,GAAG,UAAU;IACb,GAAG,UAAU;IACb,GAAG,UAAU;IACb,GAAG,UAAU;IACb,GAAG,UAAU;IACb,GAAG,UAAU;IACb,GAAG,UAAU;IACb,GAAG,QAAQ;AACf;AACA;;;;;CAKC,GACD,MAAM,UAAU,CAAC,YAAc,CAAC;QAC5B,MAAM,aAAa,OAAO,IAAI,CAAC;QAC/B,MAAM,gBAAgB,CAAC;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,KAAK,EAAG;YAC3C,MAAM,MAAM,UAAU,CAAC,EAAE;YACzB,MAAM,QAAQ,KAAK,CAAC,IAAI;YACxB,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;gBAClB,aAAa,CAAC,IAAI,GAAG;gBACrB;YACJ;YACA,MAAM,WAAW,UAAU,CAAC,IAAI,CAAC,KAAK,OAAO,WAAW;YACxD,MAAM,OAAO,OAAO,IAAI,CAAC;YACzB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;gBACrC,MAAM,WAAW,IAAI,CAAC,EAAE;gBACxB,MAAM,YAAY,QAAQ,CAAC,SAAS;gBACpC,aAAa,CAAC,SAAS,GAAG;YAC9B;QACJ;QACA,OAAO;IACX;AAEA;;;;;;CAMC,GACD,MAAM,gBAAgB,CAAC,WAAW;IAC9B,MAAM,sBAAsB,CAAC,QAAU,oBAAoB,WAAW;IACtE,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,YAAY,qBAAqB,SAAS;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3781, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/textkit/lib/textkit.js"], "sourcesContent": ["import { isNil, last, repeat, reverse, dropLast as dropLast$2, adjust, compose } from '@react-pdf/fns';\nimport bidiFactory from 'bidi-js';\nimport unicode from 'unicode-properties';\nimport hyphen from 'hyphen';\nimport pattern from 'hyphen/patterns/en-us.js';\n\n/**\n * Create attributed string from text fragments\n *\n * @param fragments - Fragments\n * @returns Attributed string\n */\nconst fromFragments = (fragments) => {\n    let offset = 0;\n    let string = '';\n    const runs = [];\n    fragments.forEach((fragment) => {\n        string += fragment.string;\n        runs.push({\n            ...fragment,\n            start: offset,\n            end: offset + fragment.string.length,\n            attributes: fragment.attributes || {},\n        });\n        offset += fragment.string.length;\n    });\n    return { string, runs };\n};\n\n/**\n * Default word hyphenation engine used when no one provided.\n * Does not perform word hyphenation at all\n *\n * @param word\n * @returns Same word\n */\nconst defaultHyphenationEngine = (word) => [word];\n/**\n * Wrap words of attribute string\n *\n * @param engines layout engines\n * @param options layout options\n */\nconst wrapWords = (engines = {}, options = {}) => {\n    /**\n     * @param attributedString - Attributed string\n     * @returns Attributed string including syllables\n     */\n    return (attributedString) => {\n        const syllables = [];\n        const fragments = [];\n        const hyphenateWord = options.hyphenationCallback ||\n            engines.wordHyphenation?.() ||\n            defaultHyphenationEngine;\n        for (let i = 0; i < attributedString.runs.length; i += 1) {\n            let string = '';\n            const run = attributedString.runs[i];\n            const words = attributedString.string\n                .slice(run.start, run.end)\n                .split(/([ ]+)/g)\n                .filter(Boolean);\n            for (let j = 0; j < words.length; j += 1) {\n                const word = words[j];\n                const parts = hyphenateWord(word);\n                syllables.push(...parts);\n                string += parts.join('');\n            }\n            fragments.push({ ...run, string });\n        }\n        const result = { ...fromFragments(fragments), syllables };\n        return result;\n    };\n};\n\n/**\n * Clone rect\n *\n * @param rect - Rect\n * @returns Cloned rect\n */\nconst copy = (rect) => {\n    return Object.assign({}, rect);\n};\n\n/**\n * Partition rect in two in the vertical direction\n *\n * @param rect - Rect\n * @param height - Height\n * @returns Partitioned rects\n */\nconst partition = (rect, height) => {\n    const a = Object.assign({}, rect, { height });\n    const b = Object.assign({}, rect, {\n        y: rect.y + height,\n        height: rect.height - height,\n    });\n    return [a, b];\n};\n\n/**\n * Crop upper section of rect\n *\n * @param height - Height\n * @param rect - Rect\n * @returns Cropped rect\n */\nconst crop = (height, rect) => {\n    const [, result] = partition(rect, height);\n    return result;\n};\n\n/**\n * Get paragraph block height\n *\n * @param paragraph - Paragraph\n * @returns Paragraph block height\n */\nconst height$2 = (paragraph) => {\n    return paragraph.reduce((acc, block) => acc + block.box.height, 0);\n};\n\n/**\n * Calculate run scale\n *\n * @param run - Run\n * @returns Scale\n */\nconst calculateScale = (run) => {\n    const attributes = run.attributes || {};\n    const fontSize = attributes.fontSize || 12;\n    const font = attributes.font;\n    const unitsPerEm = typeof font === 'string' ? null : font?.[0]?.unitsPerEm;\n    return unitsPerEm ? fontSize / unitsPerEm : 0;\n};\n/**\n * Get run scale\n *\n * @param  run\n * @returns Scale\n */\nconst scale = (run) => {\n    return run.attributes?.scale || calculateScale(run);\n};\n\n/**\n * Get ligature offset by index\n *\n * Ex. ffi ligature\n *\n *   glyphs:         l  o  f  f  i  m\n *   glyphIndices:   0  1  2  2  2  3\n *   offset:         0  0  0  1  2  0\n *\n * @param index\n * @param run - Run\n * @returns Ligature offset\n */\nconst offset = (index, run) => {\n    if (!run)\n        return 0;\n    const glyphIndices = run.glyphIndices || [];\n    const value = glyphIndices[index];\n    return glyphIndices.slice(0, index).filter((i) => i === value).length;\n};\n\n/**\n * Get run font\n *\n * @param run - Run\n * @returns Font\n */\nconst getFont = (run) => {\n    return run.attributes?.font?.[0] || null;\n};\n\n/**\n * Slice glyph between codePoints range\n * Util for breaking ligatures\n *\n * @param start - Start code point index\n * @param end - End code point index\n * @param font - Font to generate new glyph\n * @param glyph - Glyph to be sliced\n * @returns Sliced glyph parts\n */\nconst slice$2 = (start, end, font, glyph) => {\n    if (!glyph)\n        return [];\n    if (start === end)\n        return [];\n    if (start === 0 && end === glyph.codePoints.length)\n        return [glyph];\n    const codePoints = glyph.codePoints.slice(start, end);\n    const string = String.fromCodePoint(...codePoints);\n    // passing LTR To force fontkit to not reverse the string\n    return font\n        ? font.layout(string, undefined, undefined, undefined, 'ltr').glyphs\n        : [glyph];\n};\n\n/**\n * Return glyph index at string index, if glyph indices present.\n * Otherwise return string index\n *\n * @param index - Index\n * @param run - Run\n * @returns Glyph index\n */\nconst glyphIndexAt = (index, run) => {\n    const result = run?.glyphIndices?.[index];\n    return isNil(result) ? index : result;\n};\n\n/**\n * Returns new array starting with zero, and keeping same relation between consecutive values\n *\n * @param array - List\n * @returns Normalized array\n */\nconst normalize = (array) => {\n    const head = array[0];\n    return array.map((value) => value - head);\n};\n\n/**\n * Slice run between glyph indices range\n *\n * @param start - Glyph index\n * @param end - Glyph index\n * @param run - Run\n * @returns Sliced run\n */\nconst slice$1 = (start, end, run) => {\n    const runScale = scale(run);\n    const font = getFont(run);\n    // Get glyph start and end indices\n    const startIndex = glyphIndexAt(start, run);\n    const endIndex = glyphIndexAt(end, run);\n    // Get start and end glyph\n    const startGlyph = run.glyphs?.[startIndex];\n    const endGlyph = run.glyphs?.[endIndex];\n    // Get start ligature chunks (if any)\n    const startOffset = offset(start, run);\n    const startGlyphs = startOffset > 0 ? slice$2(startOffset, Infinity, font, startGlyph) : [];\n    // Get end ligature chunks (if any)\n    const endOffset = offset(end, run);\n    const endGlyphs = slice$2(0, endOffset, font, endGlyph);\n    // Compute new glyphs\n    const sliceStart = startIndex + Math.min(1, startOffset);\n    const glyphs = (run.glyphs || []).slice(sliceStart, endIndex);\n    // Compute new positions\n    const glyphPosition = (g) => ({\n        xAdvance: g.advanceWidth * runScale,\n        yAdvance: 0,\n        xOffset: 0,\n        yOffset: 0,\n    });\n    const startPositions = startGlyphs.map(glyphPosition);\n    const positions = (run.positions || []).slice(sliceStart, endIndex);\n    const endPositions = endGlyphs.map(glyphPosition);\n    return Object.assign({}, run, {\n        start: run.start + start,\n        end: Math.min(run.end, run.start + end),\n        glyphIndices: normalize((run.glyphIndices || []).slice(start, end)),\n        glyphs: [startGlyphs, glyphs, endGlyphs].flat(),\n        positions: [startPositions, positions, endPositions].flat(),\n    });\n};\n\n/**\n * Get run index that contains passed index\n *\n * @param index - Index\n * @param runs - Runs\n * @returns Run index\n */\nconst runIndexAt$1 = (index, runs) => {\n    if (!runs)\n        return -1;\n    return runs.findIndex((run) => run.start <= index && index < run.end);\n};\n\n/**\n * Filter runs contained between start and end\n *\n * @param start\n * @param end\n * @param runs\n * @returns Filtered runs\n */\nconst filter = (start, end, runs) => {\n    const startIndex = runIndexAt$1(start, runs);\n    const endIndex = Math.max(runIndexAt$1(end - 1, runs), startIndex);\n    return runs.slice(startIndex, endIndex + 1);\n};\n\n/**\n * Subtract scalar to run\n *\n * @param index - Scalar\n * @param run - Run\n * @returns Subtracted run\n */\nconst subtract = (index, run) => {\n    const start = run.start - index;\n    const end = run.end - index;\n    return Object.assign({}, run, { start, end });\n};\n\n/**\n * Slice array of runs\n *\n * @param start - Offset\n * @param end - Offset\n * @param runs\n * @returns Sliced runs\n */\nconst sliceRuns = (start, end, runs) => {\n    const sliceFirstRun = (a) => slice$1(start - a.start, end - a.start, a);\n    const sliceLastRun = (a) => slice$1(0, end - a.start, a);\n    return runs.map((run, i) => {\n        let result = run;\n        const isFirst = i === 0;\n        const isLast = !isFirst && i === runs.length - 1;\n        if (isFirst)\n            result = sliceFirstRun(run);\n        if (isLast)\n            result = sliceLastRun(run);\n        return subtract(start, result);\n    });\n};\n/**\n * Slice attributed string between two indices\n *\n * @param start - Offset\n * @param end - Offset\n * @param attributedString - Attributed string\n * @returns Attributed string\n */\nconst slice = (start, end, attributedString) => {\n    if (attributedString.string.length === 0)\n        return attributedString;\n    const string = attributedString.string.slice(start, end);\n    const filteredRuns = filter(start, end, attributedString.runs);\n    const slicedRuns = sliceRuns(start, end, filteredRuns);\n    return Object.assign({}, attributedString, { string, runs: slicedRuns });\n};\n\nconst findCharIndex = (string) => {\n    return string.search(/\\S/g);\n};\nconst findLastCharIndex = (string) => {\n    const match = string.match(/\\S/g);\n    return match ? string.lastIndexOf(match[match.length - 1]) : -1;\n};\n/**\n * Removes (strips) whitespace from both ends of the attributted string.\n *\n * @param attributedString - Attributed string\n * @returns Attributed string\n */\nconst trim = (attributedString) => {\n    const start = findCharIndex(attributedString.string);\n    const end = findLastCharIndex(attributedString.string);\n    return slice(start, end + 1, attributedString);\n};\n\n/**\n * Returns empty run\n *\n * @returns Empty run\n */\nconst empty$1 = () => {\n    return {\n        start: 0,\n        end: 0,\n        glyphIndices: [],\n        glyphs: [],\n        positions: [],\n        attributes: {},\n    };\n};\n\n/**\n * Check if value is a number\n *\n * @param value - Value to check\n * @returns Whether value is a number\n */\nconst isNumber = (value) => {\n    return typeof value === 'number';\n};\n\n/**\n * Append glyph indices with given length\n *\n * Ex. appendIndices(3, [0, 1, 2, 2]) => [0, 1, 2, 2, 3, 3, 3]\n *\n * @param length - Length\n * @param indices - Glyph indices\n * @returns Extended glyph indices\n */\nconst appendIndices = (length, indices) => {\n    const lastIndex = last(indices);\n    const value = isNil(lastIndex) ? 0 : lastIndex + 1;\n    const newIndices = Array(length).fill(value);\n    return indices.concat(newIndices);\n};\n\n/**\n * Get glyph for a given code point\n *\n * @param value - CodePoint\n * @param font - Font\n * @returns Glyph\n * */\nconst fromCodePoint = (value, font) => {\n    if (typeof font === 'string')\n        return null;\n    return font && value ? font.glyphForCodePoint(value) : null;\n};\n\n/**\n * Append glyph to run\n *\n * @param glyph - Glyph\n * @param run - Run\n * @returns Run with glyph\n */\nconst appendGlyph = (glyph, run) => {\n    const glyphLength = glyph.codePoints?.length || 0;\n    const end = run.end + glyphLength;\n    const glyphs = run.glyphs.concat(glyph);\n    const glyphIndices = appendIndices(glyphLength, run.glyphIndices);\n    if (!run.positions)\n        return Object.assign({}, run, { end, glyphs, glyphIndices });\n    const positions = run.positions.concat({\n        xAdvance: glyph.advanceWidth * scale(run),\n        yAdvance: 0,\n        xOffset: 0,\n        yOffset: 0,\n    });\n    return Object.assign({}, run, { end, glyphs, glyphIndices, positions });\n};\n/**\n * Append glyph or code point to run\n *\n * @param value - Glyph or codePoint\n * @param run - Run\n * @returns Run with glyph\n */\nconst append$1 = (value, run) => {\n    if (!value)\n        return run;\n    const font = getFont(run);\n    const glyph = isNumber(value) ? fromCodePoint(value, font) : value;\n    return appendGlyph(glyph, run);\n};\n\n/**\n * Get string from array of code points\n *\n * @param codePoints - Points\n * @returns String\n */\nconst stringFromCodePoints = (codePoints) => {\n    return String.fromCodePoint(...(codePoints || []));\n};\n\n/**\n * Append glyph into last run of attributed string\n *\n * @param glyph - Glyph or code point\n * @param attributedString - Attributed string\n * @returns Attributed string with new glyph\n */\nconst append = (glyph, attributedString) => {\n    const codePoints = typeof glyph === 'number' ? [glyph] : glyph?.codePoints;\n    const codePointsString = stringFromCodePoints(codePoints || []);\n    const string = attributedString.string + codePointsString;\n    const firstRuns = attributedString.runs.slice(0, -1);\n    const lastRun = last(attributedString.runs) || empty$1();\n    const runs = firstRuns.concat(append$1(glyph, lastRun));\n    return Object.assign({}, attributedString, { string, runs });\n};\n\nconst ELLIPSIS_UNICODE = 8230;\nconst ELLIPSIS_STRING = String.fromCharCode(ELLIPSIS_UNICODE);\n/**\n * Get ellipsis codepoint. This may be different in standard and embedded fonts\n *\n * @param font\n * @returns Ellipsis codepoint\n */\nconst getEllipsisCodePoint = (font) => {\n    if (!font.encode)\n        return ELLIPSIS_UNICODE;\n    const [codePoints] = font.encode(ELLIPSIS_STRING);\n    return parseInt(codePoints[0], 16);\n};\n/**\n * Trucante block with ellipsis\n *\n * @param paragraph - Paragraph\n * @returns Sliced paragraph\n */\nconst truncate = (paragraph) => {\n    const runs = last(paragraph)?.runs || [];\n    const font = last(runs)?.attributes?.font[0];\n    if (font) {\n        const index = paragraph.length - 1;\n        const codePoint = getEllipsisCodePoint(font);\n        const glyph = font.glyphForCodePoint(codePoint);\n        const lastBlock = append(glyph, trim(paragraph[index]));\n        return Object.assign([], paragraph, { [index]: lastBlock });\n    }\n    return paragraph;\n};\n\n/**\n * Omit attribute from run\n *\n * @param value - Attribute key\n * @param run - Run\n * @returns Run without ommited attribute\n */\nconst omit = (value, run) => {\n    const attributes = Object.assign({}, run.attributes);\n    delete attributes[value];\n    return Object.assign({}, run, { attributes });\n};\n\n/**\n * Get run ascent\n *\n * @param run - Run\n * @returns Ascent\n */\nconst ascent$1 = (run) => {\n    const { font, attachment } = run.attributes;\n    const attachmentHeight = attachment?.height || 0;\n    const fontAscent = typeof font === 'string' ? 0 : font?.[0]?.ascent || 0;\n    return Math.max(attachmentHeight, fontAscent * scale(run));\n};\n\n/**\n * Get run descent\n *\n * @param run - Run\n * @returns Descent\n */\nconst descent = (run) => {\n    const font = run.attributes?.font;\n    const fontDescent = typeof font === 'string' ? 0 : font?.[0]?.descent || 0;\n    return scale(run) * fontDescent;\n};\n\n/**\n * Get run lineGap\n *\n * @param run - Run\n * @returns LineGap\n */\nconst lineGap = (run) => {\n    const font = run.attributes?.font;\n    const lineGap = typeof font === 'string' ? 0 : font?.[0]?.lineGap || 0;\n    return lineGap * scale(run);\n};\n\n/**\n * Get run height\n *\n * @param run - Run\n * @returns Height\n */\nconst height$1 = (run) => {\n    const lineHeight = run.attributes?.lineHeight;\n    return lineHeight || lineGap(run) + ascent$1(run) - descent(run);\n};\n\n/**\n * Returns attributed string height\n *\n * @param attributedString - Attributed string\n * @returns Height\n */\nconst height = (attributedString) => {\n    const reducer = (acc, run) => Math.max(acc, height$1(run));\n    return attributedString.runs.reduce(reducer, 0);\n};\n\n/**\n * Checks if two rects intersect each other\n *\n * @param a - Rect A\n * @param b - Rect B\n * @returns Whether rects intersect\n */\nconst intersects = (a, b) => {\n    const x = Math.max(a.x, b.x);\n    const num1 = Math.min(a.x + a.width, b.x + b.width);\n    const y = Math.max(a.y, b.y);\n    const num2 = Math.min(a.y + a.height, b.y + b.height);\n    return num1 >= x && num2 >= y;\n};\n\nconst getLineFragment = (lineRect, excludeRect) => {\n    if (!intersects(excludeRect, lineRect))\n        return [lineRect];\n    const eStart = excludeRect.x;\n    const eEnd = excludeRect.x + excludeRect.width;\n    const lStart = lineRect.x;\n    const lEnd = lineRect.x + lineRect.width;\n    const a = Object.assign({}, lineRect, { width: eStart - lStart });\n    const b = Object.assign({}, lineRect, { x: eEnd, width: lEnd - eEnd });\n    return [a, b].filter((r) => r.width > 0);\n};\nconst getLineFragments = (rect, excludeRects) => {\n    let fragments = [rect];\n    for (let i = 0; i < excludeRects.length; i += 1) {\n        const excludeRect = excludeRects[i];\n        fragments = fragments.reduce((acc, fragment) => {\n            const pieces = getLineFragment(fragment, excludeRect);\n            return acc.concat(pieces);\n        }, []);\n    }\n    return fragments;\n};\nconst generateLineRects = (container, height) => {\n    const { excludeRects, ...rect } = container;\n    if (!excludeRects)\n        return [rect];\n    const lineRects = [];\n    const maxY = Math.max(...excludeRects.map((r) => r.y + r.height));\n    let currentRect = rect;\n    while (currentRect.y < maxY) {\n        const [lineRect, rest] = partition(currentRect, height);\n        const lineRectFragments = getLineFragments(lineRect, excludeRects);\n        currentRect = rest;\n        lineRects.push(...lineRectFragments);\n    }\n    return [...lineRects, currentRect];\n};\n\nconst ATTACHMENT_CODE$1 = '\\ufffc'; // 65532\n/**\n * Remove attachment attribute if no char present\n *\n * @param line - Line\n * @returns Line\n */\nconst purgeAttachments = (line) => {\n    const shouldPurge = !line.string.includes(ATTACHMENT_CODE$1);\n    if (!shouldPurge)\n        return line;\n    const runs = line.runs.map((run) => omit('attachment', run));\n    return Object.assign({}, line, { runs });\n};\n/**\n * Layout paragraphs inside rectangle\n *\n * @param rects - Rects\n * @param lines - Attributed strings\n * @param indent\n * @returns layout blocks\n */\nconst layoutLines = (rects, lines, indent) => {\n    let rect = rects.shift();\n    let currentY = rect.y;\n    return lines.map((line, i) => {\n        const lineIndent = i === 0 ? indent : 0;\n        const style = line.runs?.[0]?.attributes || {};\n        const height$1 = Math.max(height(line), style.lineHeight);\n        if (currentY + height$1 > rect.y + rect.height && rects.length > 0) {\n            rect = rects.shift();\n            currentY = rect.y;\n        }\n        const newLine = {\n            string: line.string,\n            runs: line.runs,\n            box: {\n                x: rect.x + lineIndent,\n                y: currentY,\n                width: rect.width - lineIndent,\n                height: height$1,\n            },\n        };\n        currentY += height$1;\n        return purgeAttachments(newLine);\n    });\n};\n/**\n * Performs line breaking and layout\n *\n * @param engines - Engines\n * @param options - Layout options\n */\nconst layoutParagraph = (engines, options = {}) => {\n    /**\n     * @param container - Container\n     * @param paragraph - Attributed string\n     * @returns Layout block\n     */\n    return (container, paragraph) => {\n        const height$1 = height(paragraph);\n        const indent = paragraph.runs?.[0]?.attributes?.indent || 0;\n        const rects = generateLineRects(container, height$1);\n        const availableWidths = rects.map((r) => r.width);\n        availableWidths.unshift(availableWidths[0] - indent);\n        const lines = engines.linebreaker(options)(paragraph, availableWidths);\n        return layoutLines(rects, lines, indent);\n    };\n};\n\n/**\n * Slice block at given height\n *\n * @param height - Height\n * @param paragraph - Paragraph\n * @returns Sliced paragraph\n */\nconst sliceAtHeight = (height, paragraph) => {\n    const newBlock = [];\n    let counter = 0;\n    for (let i = 0; i < paragraph.length; i += 1) {\n        const line = paragraph[i];\n        counter += line.box.height;\n        if (counter < height) {\n            newBlock.push(line);\n        }\n        else {\n            break;\n        }\n    }\n    return newBlock;\n};\n\n/**\n * Layout paragraphs inside container until it does not\n * fit anymore, performing line wrapping in the process.\n *\n * @param  engines - Engines\n * @param  options - Layout options\n * @param container - Container\n */\nconst typesetter = (engines, options, container) => {\n    /**\n     * @param attributedStrings - Attributed strings (paragraphs)\n     * @returns Paragraph blocks\n     */\n    return (attributedStrings) => {\n        const result = [];\n        const paragraphs = [...attributedStrings];\n        const layout = layoutParagraph(engines, options);\n        const maxLines = isNil(container.maxLines) ? Infinity : container.maxLines;\n        const truncateEllipsis = container.truncateMode === 'ellipsis';\n        let linesCount = maxLines;\n        let paragraphRect = copy(container);\n        let nextParagraph = paragraphs.shift();\n        while (linesCount > 0 && nextParagraph) {\n            const paragraph = layout(paragraphRect, nextParagraph);\n            const slicedBlock = paragraph.slice(0, linesCount);\n            const linesHeight = height$2(slicedBlock);\n            const shouldTruncate = truncateEllipsis && paragraph.length !== slicedBlock.length;\n            linesCount -= slicedBlock.length;\n            if (paragraphRect.height >= linesHeight) {\n                result.push(shouldTruncate ? truncate(slicedBlock) : slicedBlock);\n                paragraphRect = crop(linesHeight, paragraphRect);\n                nextParagraph = paragraphs.shift();\n            }\n            else {\n                result.push(truncate(sliceAtHeight(paragraphRect.height, slicedBlock)));\n                break;\n            }\n        }\n        return result;\n    };\n};\n\n/**\n * Get attributed string start value\n *\n * @param attributedString - Attributed string\n * @returns Start\n */\nconst start = (attributedString) => {\n    const { runs } = attributedString;\n    return runs.length === 0 ? 0 : runs[0].start;\n};\n\n/**\n * Get attributed string end value\n *\n * @param attributedString - Attributed string\n * @returns End\n */\nconst end = (attributedString) => {\n    const { runs } = attributedString;\n    return runs.length === 0 ? 0 : last(runs).end;\n};\n\n/**\n * Get attributed string length\n *\n * @param attributedString - Attributed string\n * @returns End\n */\nconst length$1 = (attributedString) => {\n    return end(attributedString) - start(attributedString);\n};\n\nconst bidi$2 = bidiFactory();\nconst getBidiLevels$1 = (runs) => {\n    return runs.reduce((acc, run) => {\n        const length = run.end - run.start;\n        const levels = repeat(run.attributes.bidiLevel, length);\n        return acc.concat(levels);\n    }, []);\n};\nconst getReorderedIndices = (string, segments) => {\n    // Fill an array with indices\n    const indices = [];\n    for (let i = 0; i < string.length; i += 1) {\n        indices[i] = i;\n    }\n    // Reverse each segment in order\n    segments.forEach(([start, end]) => {\n        const slice = indices.slice(start, end + 1);\n        for (let i = slice.length - 1; i >= 0; i -= 1) {\n            indices[end - i] = slice[i];\n        }\n    });\n    return indices;\n};\nconst getItemAtIndex = (runs, objectName, index) => {\n    for (let i = 0; i < runs.length; i += 1) {\n        const run = runs[i];\n        const updatedIndex = run.glyphIndices[index - run.start];\n        if (index >= run.start && index < run.end) {\n            return run[objectName][updatedIndex];\n        }\n    }\n    throw new Error(`index ${index} out of range`);\n};\nconst reorderLine = (line) => {\n    const levels = getBidiLevels$1(line.runs);\n    const direction = line.runs[0]?.attributes.direction;\n    const level = direction === 'rtl' ? 1 : 0;\n    const end = length$1(line) - 1;\n    const paragraphs = [{ start: 0, end, level }];\n    const embeddingLevels = { paragraphs, levels };\n    const segments = bidi$2.getReorderSegments(line.string, embeddingLevels);\n    // No need for bidi reordering\n    if (segments.length === 0)\n        return line;\n    const indices = getReorderedIndices(line.string, segments);\n    const updatedString = bidi$2.getReorderedString(line.string, embeddingLevels);\n    const updatedRuns = line.runs.map((run) => {\n        const selectedIndices = indices.slice(run.start, run.end);\n        const updatedGlyphs = [];\n        const updatedPositions = [];\n        const addedGlyphs = new Set();\n        for (let i = 0; i < selectedIndices.length; i += 1) {\n            const index = selectedIndices[i];\n            const glyph = getItemAtIndex(line.runs, 'glyphs', index);\n            if (addedGlyphs.has(glyph.id))\n                continue;\n            updatedGlyphs.push(glyph);\n            updatedPositions.push(getItemAtIndex(line.runs, 'positions', index));\n            if (glyph.isLigature) {\n                addedGlyphs.add(glyph.id);\n            }\n        }\n        return {\n            ...run,\n            glyphs: updatedGlyphs,\n            positions: updatedPositions,\n        };\n    });\n    return {\n        box: line.box,\n        runs: updatedRuns,\n        string: updatedString,\n    };\n};\nconst reorderParagraph = (paragraph) => paragraph.map(reorderLine);\n/**\n * Perform bidi reordering\n *\n * @returns Reordered paragraphs\n */\nconst bidiReordering = () => {\n    /**\n     * @param paragraphs - Paragraphs\n     * @returns Reordered paragraphs\n     */\n    return (paragraphs) => paragraphs.map(reorderParagraph);\n};\n\nconst DUMMY_CODEPOINT = 123;\n/**\n * Resolve string indices based on glyphs code points\n *\n * @param glyphs\n * @returns Glyph indices\n */\nconst resolve = (glyphs = []) => {\n    return glyphs.reduce((acc, glyph) => {\n        const codePoints = glyph?.codePoints || [DUMMY_CODEPOINT];\n        if (acc.length === 0)\n            return codePoints.map(() => 0);\n        const last = acc[acc.length - 1];\n        const next = codePoints.map(() => last + 1);\n        return [...acc, ...next];\n    }, []);\n};\n\nconst getCharacterSpacing = (run) => {\n    return run.attributes?.characterSpacing || 0;\n};\n/**\n * Scale run positions\n *\n * @param  run\n * @param  positions\n * @returns Scaled positions\n */\nconst scalePositions = (run, positions) => {\n    const runScale = scale(run);\n    const characterSpacing = getCharacterSpacing(run);\n    return positions.map((position, i) => {\n        const isLast = i === positions.length;\n        const xSpacing = isLast ? 0 : characterSpacing;\n        return Object.assign({}, position, {\n            xAdvance: position.xAdvance * runScale + xSpacing,\n            yAdvance: position.yAdvance * runScale,\n            xOffset: position.xOffset * runScale,\n            yOffset: position.yOffset * runScale,\n        });\n    });\n};\n/**\n * Create glyph run\n *\n * @param string string\n */\nconst layoutRun = (string) => {\n    /**\n     * @param run - Run\n     * @returns Glyph run\n     */\n    return (run) => {\n        const { start, end, attributes = {} } = run;\n        const { font } = attributes;\n        if (!font)\n            return { ...run, glyphs: [], glyphIndices: [], positions: [] };\n        const runString = string.slice(start, end);\n        if (typeof font === 'string')\n            throw new Error('Invalid font');\n        // passing LTR To force fontkit to not reverse the string\n        const glyphRun = font[0].layout(runString, undefined, undefined, undefined, 'ltr');\n        const positions = scalePositions(run, glyphRun.positions);\n        const glyphIndices = resolve(glyphRun.glyphs);\n        const result = {\n            ...run,\n            positions,\n            glyphIndices,\n            glyphs: glyphRun.glyphs,\n        };\n        return result;\n    };\n};\n/**\n * Generate glyphs for single attributed string\n */\nconst generateGlyphs = () => {\n    /**\n     * @param attributedString - Attributed string\n     * @returns Attributed string with glyphs\n     */\n    return (attributedString) => {\n        const runs = attributedString.runs.map(layoutRun(attributedString.string));\n        const res = Object.assign({}, attributedString, { runs });\n        return res;\n    };\n};\n\n/**\n * Resolves yOffset for run\n *\n * @param run - Run\n * @returns Run\n */\nconst resolveRunYOffset = (run) => {\n    if (!run.positions)\n        return run;\n    const unitsPerEm = run.attributes?.font?.[0]?.unitsPerEm || 0;\n    const yOffset = (run.attributes?.yOffset || 0) * unitsPerEm;\n    const positions = run.positions.map((p) => Object.assign({}, p, { yOffset }));\n    return Object.assign({}, run, { positions });\n};\n/**\n * Resolves yOffset for multiple paragraphs\n */\nconst resolveYOffset = () => {\n    /**\n     * @param attributedString - Attributed string\n     * @returns Attributed string\n     */\n    return (attributedString) => {\n        const runs = attributedString.runs.map(resolveRunYOffset);\n        const res = Object.assign({}, attributedString, { runs });\n        return res;\n    };\n};\n\n/**\n * Sort runs in ascending order\n *\n * @param runs\n * @returns Sorted runs\n */\nconst sort = (runs) => {\n    return runs.sort((a, b) => a.start - b.start || a.end - b.end);\n};\n\n/**\n * Is run empty (start === end)\n *\n * @param run - Run\n * @returns Is run empty\n */\nconst isEmpty = (run) => {\n    return run.start === run.end;\n};\n\n/**\n * Sort points in ascending order\n * @param a - First point\n * @param b - Second point\n * @returns Sort order\n */\nconst sortPoints = (a, b) => {\n    return a[1] - b[1] || a[3] - b[3];\n};\n/**\n * @param runs\n * @returns Points\n */\nconst generatePoints = (runs) => {\n    const result = runs.reduce((acc, run, i) => {\n        return acc.concat([\n            ['start', run.start, run.attributes, i],\n            ['end', run.end, run.attributes, i],\n        ]);\n    }, []);\n    return result.sort(sortPoints);\n};\n/**\n * @param runs\n * @returns Merged runs\n */\nconst mergeRuns = (runs) => {\n    return runs.reduce((acc, run) => {\n        const attributes = Object.assign({}, acc.attributes, run.attributes);\n        return Object.assign({}, run, { attributes });\n    }, {});\n};\n/**\n * @param runs\n * @returns Grouped runs\n */\nconst groupEmptyRuns = (runs) => {\n    const groups = runs.reduce((acc, run) => {\n        if (!acc[run.start])\n            acc[run.start] = [];\n        acc[run.start].push(run);\n        return acc;\n    }, []);\n    return Object.values(groups);\n};\n/**\n * @param runs\n * @returns Flattened runs\n */\nconst flattenEmptyRuns = (runs) => {\n    return groupEmptyRuns(runs).map(mergeRuns);\n};\n/**\n * @param runs\n * @returns Flattened runs\n */\nconst flattenRegularRuns = (runs) => {\n    const res = [];\n    const points = generatePoints(runs);\n    let start = -1;\n    let attrs = {};\n    const stack = [];\n    for (let i = 0; i < points.length; i += 1) {\n        const [type, offset, attributes] = points[i];\n        if (start !== -1 && start < offset) {\n            res.push({\n                start,\n                end: offset,\n                attributes: attrs,\n                glyphIndices: [],\n                glyphs: [],\n                positions: [],\n            });\n        }\n        if (type === 'start') {\n            stack.push(attributes);\n            attrs = Object.assign({}, attrs, attributes);\n        }\n        else {\n            attrs = {};\n            for (let j = 0; j < stack.length; j += 1) {\n                if (stack[j] === attributes) {\n                    stack.splice(j--, 1);\n                }\n                else {\n                    attrs = Object.assign({}, attrs, stack[j]);\n                }\n            }\n        }\n        start = offset;\n    }\n    return res;\n};\n/**\n * Flatten many runs\n *\n * @param runs\n * @returns Flattened runs\n */\nconst flatten = (runs = []) => {\n    const emptyRuns = flattenEmptyRuns(runs.filter((run) => isEmpty(run)));\n    const regularRuns = flattenRegularRuns(runs.filter((run) => !isEmpty(run)));\n    return sort(emptyRuns.concat(regularRuns));\n};\n\n/**\n * Returns empty attributed string\n *\n * @returns Empty attributed string\n */\nconst empty = () => ({ string: '', runs: [] });\n\n/**\n *\n * @param attributedString\n * @returns Attributed string without font\n */\nconst omitFont = (attributedString) => {\n    const runs = attributedString.runs.map((run) => omit('font', run));\n    return Object.assign({}, attributedString, { runs });\n};\n/**\n * Performs font substitution and script itemization on attributed string\n *\n * @param engines - engines\n */\nconst preprocessRuns = (engines) => {\n    /**\n     * @param attributedString - Attributed string\n     * @returns Processed attributed string\n     */\n    return (attributedString) => {\n        if (isNil(attributedString))\n            return empty();\n        const { string } = attributedString;\n        const { fontSubstitution, scriptItemizer, bidi } = engines;\n        const { runs: omittedFontRuns } = omitFont(attributedString);\n        const { runs: itemizationRuns } = scriptItemizer()(attributedString);\n        const { runs: substitutedRuns } = fontSubstitution()(attributedString);\n        const { runs: bidiRuns } = bidi()(attributedString);\n        const runs = bidiRuns\n            .concat(substitutedRuns)\n            .concat(itemizationRuns)\n            .concat(omittedFontRuns);\n        return { string, runs: flatten(runs) };\n    };\n};\n\n/**\n * Breaks attributed string into paragraphs\n */\nconst splitParagraphs = () => {\n    /**\n     * @param attributedString - Attributed string\n     * @returns Paragraphs attributed strings\n     */\n    return (attributedString) => {\n        const paragraphs = [];\n        let start = 0;\n        let breakPoint = attributedString.string.indexOf('\\n') + 1;\n        while (breakPoint > 0) {\n            paragraphs.push(slice(start, breakPoint, attributedString));\n            start = breakPoint;\n            breakPoint = attributedString.string.indexOf('\\n', breakPoint) + 1;\n        }\n        if (start === 0) {\n            paragraphs.push(attributedString);\n        }\n        else if (start < attributedString.string.length) {\n            paragraphs.push(slice(start, length$1(attributedString), attributedString));\n        }\n        return paragraphs;\n    };\n};\n\n/**\n * Return positions advance width\n *\n * @param positions - Positions\n * @returns {number} advance width\n */\nconst advanceWidth$2 = (positions) => {\n    return positions.reduce((acc, pos) => acc + (pos.xAdvance || 0), 0);\n};\n\n/**\n * Return run advance width\n *\n * @param run - Run\n * @returns Advance width\n */\nconst advanceWidth$1 = (run) => {\n    return advanceWidth$2(run.positions || []);\n};\n\n/**\n * Returns attributed string advancewidth\n *\n * @param attributedString - Attributed string\n * @returns Advance width\n */\nconst advanceWidth = (attributedString) => {\n    const reducer = (acc, run) => acc + advanceWidth$1(run);\n    return attributedString.runs.reduce(reducer, 0);\n};\n\nconst WHITE_SPACES_CODE = 32;\n/**\n * Check if glyph is white space\n *\n * @param glyph - Glyph\n * @returns Whether glyph is white space\n * */\nconst isWhiteSpace = (glyph) => {\n    const codePoints = glyph?.codePoints || [];\n    return codePoints.includes(WHITE_SPACES_CODE);\n};\n\n/**\n * Get white space leading positions\n *\n * @param run - Run\n * @returns White space leading positions\n */\nconst leadingPositions = (run) => {\n    const glyphs = run.glyphs || [];\n    const positions = run.positions || [];\n    const leadingWhitespaces = glyphs.findIndex((g) => !isWhiteSpace(g));\n    return positions.slice(0, leadingWhitespaces);\n};\n/**\n * Get run leading white space offset\n *\n * @param run - Run\n * @returns Leading white space offset\n */\nconst leadingOffset$1 = (run) => {\n    const positions = leadingPositions(run);\n    return positions.reduce((acc, pos) => acc + (pos.xAdvance || 0), 0);\n};\n\n/**\n * Get attributed string leading white space offset\n *\n * @param attributedString - Attributed string\n * @returns Leading white space offset\n */\nconst leadingOffset = (attributedString) => {\n    const runs = attributedString.runs || [];\n    return leadingOffset$1(runs[0]);\n};\n\n/**\n * Get white space trailing positions\n *\n * @param run run\n * @returns White space trailing positions\n */\nconst trailingPositions = (run) => {\n    const glyphs = reverse(run.glyphs || []);\n    const positions = reverse(run.positions || []);\n    const leadingWhitespaces = glyphs.findIndex((g) => !isWhiteSpace(g));\n    return positions.slice(0, leadingWhitespaces);\n};\n/**\n * Get run trailing white space offset\n *\n * @param run - Run\n * @returns Trailing white space offset\n */\nconst trailingOffset$1 = (run) => {\n    const positions = trailingPositions(run);\n    return positions.reduce((acc, pos) => acc + (pos.xAdvance || 0), 0);\n};\n\n/**\n * Get attributed string trailing white space offset\n *\n * @param attributedString - Attributed string\n * @returns Trailing white space offset\n */\nconst trailingOffset = (attributedString) => {\n    const runs = attributedString.runs || [];\n    return trailingOffset$1(last(runs));\n};\n\n/**\n * Drop last char of run\n *\n * @param run - Run\n * @returns Run without last char\n */\nconst dropLast$1 = (run) => {\n    return slice$1(0, run.end - run.start - 1, run);\n};\n\n/**\n * Drop last glyph\n *\n * @param attributedString - Attributed string\n * @returns Attributed string with new glyph\n */\nconst dropLast = (attributedString) => {\n    const string = dropLast$2(attributedString.string);\n    const runs = adjust(-1, dropLast$1, attributedString.runs);\n    return Object.assign({}, attributedString, { string, runs });\n};\n\nconst ALIGNMENT_FACTORS = { center: 0.5, right: 1 };\n/**\n * Remove new line char at the end of line if present\n *\n * @param line\n * @returns Line\n */\nconst removeNewLine = (line) => {\n    return last(line.string) === '\\n' ? dropLast(line) : line;\n};\nconst getOverflowLeft = (line) => {\n    return leadingOffset(line) + (line.overflowLeft || 0);\n};\nconst getOverflowRight = (line) => {\n    return trailingOffset(line) + (line.overflowRight || 0);\n};\n/**\n * Ignore whitespace at the start and end of a line for alignment\n *\n * @param line\n * @returns Line\n */\nconst adjustOverflow = (line) => {\n    const overflowLeft = getOverflowLeft(line);\n    const overflowRight = getOverflowRight(line);\n    const x = line.box.x - overflowLeft;\n    const width = line.box.width + overflowLeft + overflowRight;\n    const box = Object.assign({}, line.box, { x, width });\n    return Object.assign({}, line, { box, overflowLeft, overflowRight });\n};\n/**\n * Performs line justification by calling appropiate engine\n *\n * @param engines - Engines\n * @param options - Layout options\n * @param align - Text align\n */\nconst justifyLine$1 = (engines, options, align) => {\n    /**\n     * @param line - Line\n     * @returns Line\n     */\n    return (line) => {\n        const lineWidth = advanceWidth(line);\n        const alignFactor = ALIGNMENT_FACTORS[align] || 0;\n        const remainingWidth = Math.max(0, line.box.width - lineWidth);\n        const shouldJustify = align === 'justify' || lineWidth > line.box.width;\n        const x = line.box.x + remainingWidth * alignFactor;\n        const box = Object.assign({}, line.box, { x });\n        const newLine = Object.assign({}, line, { box });\n        return shouldJustify ? engines.justification(options)(newLine) : newLine;\n    };\n};\nconst finalizeLine = (line) => {\n    let lineAscent = 0;\n    let lineDescent = 0;\n    let lineHeight = 0;\n    let lineXAdvance = 0;\n    const runs = line.runs.map((run) => {\n        const height = height$1(run);\n        const ascent = ascent$1(run);\n        const descent$1 = descent(run);\n        const xAdvance = advanceWidth$1(run);\n        lineHeight = Math.max(lineHeight, height);\n        lineAscent = Math.max(lineAscent, ascent);\n        lineDescent = Math.max(lineDescent, descent$1);\n        lineXAdvance += xAdvance;\n        return Object.assign({}, run, { height, ascent, descent: descent$1, xAdvance });\n    });\n    return Object.assign({}, line, {\n        runs,\n        height: lineHeight,\n        ascent: lineAscent,\n        descent: lineDescent,\n        xAdvance: lineXAdvance,\n    });\n};\n/**\n * Finalize line by performing line justification\n * and text decoration (using appropiate engines)\n *\n * @param engines - Engines\n * @param options - Layout options\n */\nconst finalizeBlock = (engines, options) => {\n    /**\n     * @param line - Line\n     * @param i - Line index\n     * @param lines - Total lines\n     * @returns Line\n     */\n    return (line, index, lines) => {\n        const isLastFragment = index === lines.length - 1;\n        const style = line.runs?.[0]?.attributes || {};\n        const align = isLastFragment ? style.alignLastLine : style.align;\n        return compose(finalizeLine, engines.textDecoration(), justifyLine$1(engines, options, align), adjustOverflow, removeNewLine)(line);\n    };\n};\n/**\n * Finalize line block by performing line justification\n * and text decoration (using appropiate engines)\n *\n * @param engines - Engines\n * @param options - Layout options\n */\nconst finalizeFragments = (engines, options) => {\n    /**\n     * @param paragraphs - Paragraphs\n     * @returns Paragraphs\n     */\n    return (paragraphs) => {\n        const blockFinalizer = finalizeBlock(engines, options);\n        return paragraphs.map((paragraph) => paragraph.map(blockFinalizer));\n    };\n};\n\nconst ATTACHMENT_CODE = 0xfffc; // 65532\nconst isReplaceGlyph = (glyph) => glyph.codePoints.includes(ATTACHMENT_CODE);\n/**\n * Resolve attachments of run\n *\n * @param run\n * @returns Run\n */\nconst resolveRunAttachments = (run) => {\n    if (!run.positions)\n        return run;\n    const glyphs = run.glyphs || [];\n    const attachment = run.attributes?.attachment;\n    if (!attachment)\n        return run;\n    const positions = run.positions.map((position, i) => {\n        const glyph = glyphs[i];\n        if (attachment.width && isReplaceGlyph(glyph)) {\n            return Object.assign({}, position, { xAdvance: attachment.width });\n        }\n        return Object.assign({}, position);\n    });\n    return Object.assign({}, run, { positions });\n};\n/**\n * Resolve attachments for multiple paragraphs\n */\nconst resolveAttachments = () => {\n    /**\n     * @param attributedString - Attributed string\n     * @returns Attributed string\n     */\n    return (attributedString) => {\n        const runs = attributedString.runs.map(resolveRunAttachments);\n        const res = Object.assign({}, attributedString, { runs });\n        return res;\n    };\n};\n\n/**\n * @param attributes - Attributes\n * @returns Attributes with defaults\n */\nconst applyAttributes = (a) => {\n    return {\n        align: a.align || (a.direction === 'rtl' ? 'right' : 'left'),\n        alignLastLine: a.alignLastLine || (a.align === 'justify' ? 'left' : a.align || 'left'),\n        attachment: a.attachment || null,\n        backgroundColor: a.backgroundColor || null,\n        bullet: a.bullet || null,\n        characterSpacing: a.characterSpacing || 0,\n        color: a.color || 'black',\n        direction: a.direction || 'ltr',\n        features: a.features || [],\n        fill: a.fill !== false,\n        font: a.font || [],\n        fontSize: a.fontSize || 12,\n        hangingPunctuation: a.hangingPunctuation || false,\n        hyphenationFactor: a.hyphenationFactor || 0,\n        indent: a.indent || 0,\n        justificationFactor: a.justificationFactor || 1,\n        lineHeight: a.lineHeight || null,\n        lineSpacing: a.lineSpacing || 0,\n        link: a.link || null,\n        marginLeft: a.marginLeft || a.margin || 0,\n        marginRight: a.marginRight || a.margin || 0,\n        opacity: a.opacity,\n        paddingTop: a.paddingTop || a.padding || 0,\n        paragraphSpacing: a.paragraphSpacing || 0,\n        script: a.script || null,\n        shrinkFactor: a.shrinkFactor || 0,\n        strike: a.strike || false,\n        strikeColor: a.strikeColor || a.color || 'black',\n        strikeStyle: a.strikeStyle || 'solid',\n        stroke: a.stroke || false,\n        underline: a.underline || false,\n        underlineColor: a.underlineColor || a.color || 'black',\n        underlineStyle: a.underlineStyle || 'solid',\n        verticalAlign: a.verticalAlign || null,\n        wordSpacing: a.wordSpacing || 0,\n        yOffset: a.yOffset || 0,\n    };\n};\n/**\n * Apply default style to run\n *\n * @param run - Run\n * @returns Run with default styles\n */\nconst applyRunStyles = (run) => {\n    const attributes = applyAttributes(run.attributes);\n    return Object.assign({}, run, { attributes });\n};\n/**\n * Apply default attributes for an attributed string\n */\nconst applyDefaultStyles = () => {\n    return (attributedString) => {\n        const string = attributedString.string || '';\n        const runs = (attributedString.runs || []).map(applyRunStyles);\n        return { string, runs };\n    };\n};\n\n/**\n * Apply scaling and yOffset for verticalAlign 'sub' and 'super'.\n */\nconst verticalAlignment = () => {\n    /**\n     * @param attributedString - Attributed string\n     * @returns Attributed string\n     */\n    return (attributedString) => {\n        attributedString.runs.forEach((run) => {\n            const { attributes } = run;\n            const { verticalAlign } = attributes;\n            if (verticalAlign === 'sub') {\n                attributes.yOffset = -0.2;\n            }\n            else if (verticalAlign === 'super') {\n                attributes.yOffset = 0.4;\n            }\n        });\n        return attributedString;\n    };\n};\n\nconst bidi$1 = bidiFactory();\n/**\n * @param runs\n * @returns Bidi levels\n */\nconst getBidiLevels = (runs) => {\n    return runs.reduce((acc, run) => {\n        const length = run.end - run.start;\n        const levels = repeat(run.attributes.bidiLevel, length);\n        return acc.concat(levels);\n    }, []);\n};\n/**\n * Perform bidi mirroring\n */\nconst mirrorString = () => {\n    /**\n     * @param attributedString - Attributed string\n     * @returns Attributed string\n     */\n    return (attributedString) => {\n        const levels = getBidiLevels(attributedString.runs);\n        let updatedString = '';\n        attributedString.string.split('').forEach((char, index) => {\n            const isRTL = levels[index] % 2 === 1;\n            const mirroredChar = isRTL\n                ? bidi$1.getMirroredCharacter(attributedString.string.charAt(index))\n                : null;\n            updatedString += mirroredChar || char;\n        });\n        const result = {\n            ...attributedString,\n            string: updatedString,\n        };\n        return result;\n    };\n};\n\n/**\n * A LayoutEngine is the main object that performs text layout.\n * It accepts an AttributedString and a Container object\n * to layout text into, and uses several helper objects to perform\n * various layout tasks. These objects can be overridden to customize\n * layout behavior.\n */\nconst layoutEngine = (engines) => {\n    return (attributedString, container, options = {}) => {\n        const processParagraph = compose(resolveYOffset(), resolveAttachments(), verticalAlignment(), wrapWords(engines, options), generateGlyphs(), mirrorString(), preprocessRuns(engines));\n        const processParagraphs = (paragraphs) => paragraphs.map(processParagraph);\n        return compose(finalizeFragments(engines, options), bidiReordering(), typesetter(engines, options, container), processParagraphs, splitParagraphs(), applyDefaultStyles())(attributedString);\n    };\n};\n\nconst bidi = bidiFactory();\nconst bidiEngine = () => {\n    /**\n     * @param attributedString - Attributed string\n     * @returns Attributed string\n     */\n    return (attributedString) => {\n        const { string } = attributedString;\n        const direction = attributedString.runs[0]?.attributes.direction;\n        const { levels } = bidi.getEmbeddingLevels(string, direction);\n        let lastLevel = null;\n        let lastIndex = 0;\n        let index = 0;\n        const runs = [];\n        for (let i = 0; i < levels.length; i += 1) {\n            const level = levels[i];\n            if (level !== lastLevel) {\n                if (lastLevel !== null) {\n                    runs.push({\n                        start: lastIndex,\n                        end: index,\n                        attributes: { bidiLevel: lastLevel },\n                    });\n                }\n                lastIndex = index;\n                lastLevel = level;\n            }\n            index += 1;\n        }\n        if (lastIndex < string.length) {\n            runs.push({\n                start: lastIndex,\n                end: string.length,\n                attributes: { bidiLevel: lastLevel },\n            });\n        }\n        const result = { string, runs };\n        return result;\n    };\n};\n\nconst INFINITY = 10000;\nconst getNextBreakpoint = (subnodes, widths, lineNumber) => {\n    let position = null;\n    let minimumBadness = Infinity;\n    const sum = { width: 0, stretch: 0, shrink: 0 };\n    const lineLength = widths[Math.min(lineNumber, widths.length - 1)];\n    const calculateRatio = (node) => {\n        const stretch = 'stretch' in node ? node.stretch : null;\n        if (sum.width < lineLength) {\n            if (!stretch)\n                return INFINITY;\n            return sum.stretch - stretch > 0\n                ? (lineLength - sum.width) / sum.stretch\n                : INFINITY;\n        }\n        const shrink = 'shrink' in node ? node.shrink : null;\n        if (sum.width > lineLength) {\n            if (!shrink)\n                return INFINITY;\n            return sum.shrink - shrink > 0\n                ? (lineLength - sum.width) / sum.shrink\n                : INFINITY;\n        }\n        return 0;\n    };\n    for (let i = 0; i < subnodes.length; i += 1) {\n        const node = subnodes[i];\n        if (node.type === 'box') {\n            sum.width += node.width;\n        }\n        if (node.type === 'glue') {\n            sum.width += node.width;\n            sum.stretch += node.stretch;\n            sum.shrink += node.shrink;\n        }\n        if (sum.width - sum.shrink > lineLength) {\n            if (position === null) {\n                let j = i === 0 ? i + 1 : i;\n                while (j < subnodes.length &&\n                    (subnodes[j].type === 'glue' || subnodes[j].type === 'penalty')) {\n                    j++;\n                }\n                position = j - 1;\n            }\n            break;\n        }\n        if (node.type === 'penalty' || node.type === 'glue') {\n            const ratio = calculateRatio(node);\n            const penalty = node.type === 'penalty' ? node.penalty : 0;\n            const badness = 100 * Math.abs(ratio) ** 3 + penalty;\n            if (minimumBadness >= badness) {\n                position = i;\n                minimumBadness = badness;\n            }\n        }\n    }\n    return sum.width - sum.shrink > lineLength ? position : null;\n};\nconst applyBestFit = (nodes, widths) => {\n    let count = 0;\n    let lineNumber = 0;\n    let subnodes = nodes;\n    const breakpoints = [0];\n    while (subnodes.length > 0) {\n        const breakpoint = getNextBreakpoint(subnodes, widths, lineNumber);\n        if (breakpoint !== null) {\n            count += breakpoint;\n            breakpoints.push(count);\n            subnodes = subnodes.slice(breakpoint + 1, subnodes.length);\n            count++;\n            lineNumber++;\n        }\n        else {\n            subnodes = [];\n        }\n    }\n    return breakpoints;\n};\n\n/* eslint-disable max-classes-per-file */\nclass LinkedListNode {\n    data;\n    prev;\n    next;\n    constructor(data) {\n        this.data = data;\n        this.prev = null;\n        this.next = null;\n    }\n}\nclass LinkedList {\n    static Node = LinkedListNode;\n    head;\n    tail;\n    listSize;\n    listLength;\n    constructor() {\n        this.head = null;\n        this.tail = null;\n        this.listSize = 0;\n        this.listLength = 0;\n    }\n    isLinked(node) {\n        return !((node &&\n            node.prev === null &&\n            node.next === null &&\n            this.tail !== node &&\n            this.head !== node) ||\n            this.isEmpty());\n    }\n    size() {\n        return this.listSize;\n    }\n    isEmpty() {\n        return this.listSize === 0;\n    }\n    first() {\n        return this.head;\n    }\n    last() {\n        return this.last;\n    }\n    forEach(callback) {\n        let node = this.head;\n        while (node !== null) {\n            callback(node);\n            node = node.next;\n        }\n    }\n    at(i) {\n        let node = this.head;\n        let index = 0;\n        if (i >= this.listLength || i < 0) {\n            return null;\n        }\n        while (node !== null) {\n            if (i === index) {\n                return node;\n            }\n            node = node.next;\n            index += 1;\n        }\n        return null;\n    }\n    insertAfter(node, newNode) {\n        if (!this.isLinked(node))\n            return this;\n        newNode.prev = node;\n        newNode.next = node.next;\n        if (node.next === null) {\n            this.tail = newNode;\n        }\n        else {\n            node.next.prev = newNode;\n        }\n        node.next = newNode;\n        this.listSize += 1;\n        return this;\n    }\n    insertBefore(node, newNode) {\n        if (!this.isLinked(node))\n            return this;\n        newNode.prev = node.prev;\n        newNode.next = node;\n        if (node.prev === null) {\n            this.head = newNode;\n        }\n        else {\n            node.prev.next = newNode;\n        }\n        node.prev = newNode;\n        this.listSize += 1;\n        return this;\n    }\n    push(node) {\n        if (this.head === null) {\n            this.unshift(node);\n        }\n        else {\n            this.insertAfter(this.tail, node);\n        }\n        return this;\n    }\n    unshift(node) {\n        if (this.head === null) {\n            this.head = node;\n            this.tail = node;\n            node.prev = null;\n            node.next = null;\n            this.listSize += 1;\n        }\n        else {\n            this.insertBefore(this.head, node);\n        }\n        return this;\n    }\n    remove(node) {\n        if (!this.isLinked(node))\n            return this;\n        if (node.prev === null) {\n            this.head = node.next;\n        }\n        else {\n            node.prev.next = node.next;\n        }\n        if (node.next === null) {\n            this.tail = node.prev;\n        }\n        else {\n            node.next.prev = node.prev;\n        }\n        this.listSize -= 1;\n        return this;\n    }\n}\n\n/**\n * Licensed under the new BSD License.\n * Copyright 2009-2010, Bram Stein\n * All rights reserved.\n */\nfunction breakpoint(position, demerits, line, fitnessClass, totals, previous) {\n    return {\n        position,\n        demerits,\n        line,\n        fitnessClass,\n        totals: totals || {\n            width: 0,\n            stretch: 0,\n            shrink: 0,\n        },\n        previous,\n    };\n}\nfunction computeCost(nodes, lineLengths, sum, end, active, currentLine) {\n    let width = sum.width - active.totals.width;\n    let stretch = 0;\n    let shrink = 0;\n    // If the current line index is within the list of linelengths, use it, otherwise use\n    // the last line length of the list.\n    const lineLength = currentLine < lineLengths.length\n        ? lineLengths[currentLine - 1]\n        : lineLengths[lineLengths.length - 1];\n    if (nodes[end].type === 'penalty') {\n        width += nodes[end].width;\n    }\n    // Calculate the stretch ratio\n    if (width < lineLength) {\n        stretch = sum.stretch - active.totals.stretch;\n        if (stretch > 0) {\n            return (lineLength - width) / stretch;\n        }\n        return linebreak.infinity;\n    }\n    // Calculate the shrink ratio\n    if (width > lineLength) {\n        shrink = sum.shrink - active.totals.shrink;\n        if (shrink > 0) {\n            return (lineLength - width) / shrink;\n        }\n        return linebreak.infinity;\n    }\n    // perfect match\n    return 0;\n}\n// Add width, stretch and shrink values from the current\n// break point up to the next box or forced penalty.\nfunction computeSum(nodes, sum, breakPointIndex) {\n    const result = {\n        width: sum.width,\n        stretch: sum.stretch,\n        shrink: sum.shrink,\n    };\n    for (let i = breakPointIndex; i < nodes.length; i += 1) {\n        const node = nodes[i];\n        if (node.type === 'glue') {\n            result.width += node.width;\n            result.stretch += node.stretch;\n            result.shrink += node.shrink;\n        }\n        else if (node.type === 'box' ||\n            (node.type === 'penalty' &&\n                node.penalty === -linebreak.infinity &&\n                i > breakPointIndex)) {\n            break;\n        }\n    }\n    return result;\n}\nfunction findBestBreakpoints(activeNodes) {\n    const breakpoints = [];\n    if (activeNodes.size() === 0)\n        return [];\n    let tmp = { data: { demerits: Infinity } };\n    // Find the best active node (the one with the least total demerits.)\n    activeNodes.forEach((node) => {\n        if (node.data.demerits < tmp.data.demerits) {\n            tmp = node;\n        }\n    });\n    while (tmp !== null) {\n        breakpoints.push(tmp.data.position);\n        tmp = tmp.data.previous;\n    }\n    return breakpoints.reverse();\n}\n/**\n * @param nodes\n * @param availableWidths\n * @param tolerance\n * @preserve Knuth and Plass line breaking algorithm in JavaScript\n */\nconst linebreak = (nodes, availableWidths, tolerance) => {\n    // Demerits are used as a way to penalize bad line breaks\n    //  - line: applied to each line, depending on how much spaces need to stretch or shrink\n    //  - flagged: applied when consecutive lines end in hyphenation\n    //  - fitness: algorithm groups lines into fitness classes based on how loose or tight the spacing is.\n    //             if a paragraph has consecutive lines from different fitness classes,\n    //             a fitness demerit is applied to maintain visual consistency.\n    const options = {\n        demerits: { line: 10, flagged: 100, fitness: 3000 },\n        tolerance: tolerance || 3,\n    };\n    const activeNodes = new LinkedList();\n    const sum = { width: 0, stretch: 0, shrink: 0 };\n    const lineLengths = availableWidths;\n    // Add an active node for the start of the paragraph.\n    activeNodes.push(new LinkedList.Node(breakpoint(0, 0, 0, 0, undefined, null)));\n    // The main loop of the algorithm\n    function mainLoop(node, index, nodes) {\n        let active = activeNodes.first();\n        // The inner loop iterates through all the active nodes with line < currentLine and then\n        // breaks out to insert the new active node candidates before looking at the next active\n        // nodes for the next lines. The result of this is that the active node list is always\n        // sorted by line number.\n        while (active !== null) {\n            let currentLine = 0;\n            // Candidates fo each fitness class\n            const candidates = [\n                { active: undefined, demerits: Infinity },\n                { active: undefined, demerits: Infinity },\n                { active: undefined, demerits: Infinity },\n                { active: undefined, demerits: Infinity },\n            ];\n            // Iterate through the linked list of active nodes to find new potential active nodes and deactivate current active nodes.\n            while (active !== null) {\n                currentLine = active.data.line + 1;\n                const ratio = computeCost(nodes, lineLengths, sum, index, active.data, currentLine);\n                // Deactive nodes when the distance between the current active node and the\n                // current node becomes too large (i.e. it exceeds the stretch limit and the stretch\n                // ratio becomes negative) or when the current node is a forced break (i.e. the end\n                // of the paragraph when we want to remove all active nodes, but possibly have a final\n                // candidate active node---if the paragraph can be set using the given tolerance value.)\n                if (ratio < -1 ||\n                    (node.type === 'penalty' && node.penalty === -linebreak.infinity)) {\n                    activeNodes.remove(active);\n                }\n                // If the ratio is within the valid range of -1 <= ratio <= tolerance calculate the\n                // total demerits and record a candidate active node.\n                if (ratio >= -1 && ratio <= options.tolerance) {\n                    const badness = 100 * Math.pow(Math.abs(ratio), 3);\n                    let demerits = 0;\n                    // Positive penalty\n                    if (node.type === 'penalty' && node.penalty >= 0) {\n                        demerits =\n                            Math.pow(options.demerits.line + badness, 2) +\n                                Math.pow(node.penalty, 2);\n                        // Negative penalty but not a forced break\n                    }\n                    else if (node.type === 'penalty' &&\n                        node.penalty !== -linebreak.infinity) {\n                        demerits =\n                            Math.pow(options.demerits.line + badness, 2) -\n                                Math.pow(node.penalty, 2);\n                        // All other cases\n                    }\n                    else {\n                        demerits = Math.pow(options.demerits.line + badness, 2);\n                    }\n                    if (node.type === 'penalty' &&\n                        nodes[active.data.position].type === 'penalty') {\n                        demerits +=\n                            options.demerits.flagged *\n                                node.flagged *\n                                // @ts-expect-error node is penalty here\n                                nodes[active.data.position].flagged;\n                    }\n                    // Calculate the fitness class for this candidate active node.\n                    let currentClass;\n                    if (ratio < -0.5) {\n                        currentClass = 0;\n                    }\n                    else if (ratio <= 0.5) {\n                        currentClass = 1;\n                    }\n                    else if (ratio <= 1) {\n                        currentClass = 2;\n                    }\n                    else {\n                        currentClass = 3;\n                    }\n                    // Add a fitness penalty to the demerits if the fitness classes of two adjacent lines differ too much.\n                    if (Math.abs(currentClass - active.data.fitnessClass) > 1) {\n                        demerits += options.demerits.fitness;\n                    }\n                    // Add the total demerits of the active node to get the total demerits of this candidate node.\n                    demerits += active.data.demerits;\n                    // Only store the best candidate for each fitness class\n                    if (demerits < candidates[currentClass].demerits) {\n                        candidates[currentClass] = { active, demerits };\n                    }\n                }\n                active = active.next;\n                // Stop iterating through active nodes to insert new candidate active nodes in the active list\n                // before moving on to the active nodes for the next line.\n                // TODO: The Knuth and Plass paper suggests a conditional for currentLine < j0. This means paragraphs\n                // with identical line lengths will not be sorted by line number. Find out if that is a desirable outcome.\n                // For now I left this out, as it only adds minimal overhead to the algorithm and keeping the active node\n                // list sorted has a higher priority.\n                if (active !== null && active.data.line >= currentLine) {\n                    break;\n                }\n            }\n            const tmpSum = computeSum(nodes, sum, index);\n            for (let fitnessClass = 0; fitnessClass < candidates.length; fitnessClass += 1) {\n                const candidate = candidates[fitnessClass];\n                if (candidate.demerits === Infinity)\n                    continue;\n                const newNode = new LinkedList.Node(breakpoint(index, candidate.demerits, candidate.active.data.line + 1, fitnessClass, tmpSum, candidate.active));\n                if (active !== null) {\n                    activeNodes.insertBefore(active, newNode);\n                }\n                else {\n                    activeNodes.push(newNode);\n                }\n            }\n        }\n    }\n    nodes.forEach((node, index, nodes) => {\n        if (node.type === 'box') {\n            sum.width += node.width;\n            return;\n        }\n        if (node.type === 'glue') {\n            const precedesBox = index > 0 && nodes[index - 1].type === 'box';\n            if (precedesBox)\n                mainLoop(node, index, nodes);\n            sum.width += node.width;\n            sum.stretch += node.stretch;\n            sum.shrink += node.shrink;\n            return;\n        }\n        if (node.type === 'penalty' && node.penalty !== linebreak.infinity) {\n            mainLoop(node, index, nodes);\n        }\n    });\n    return findBestBreakpoints(activeNodes);\n};\nlinebreak.infinity = 10000;\nlinebreak.glue = (width, start, end, stretch, shrink) => ({\n    type: 'glue',\n    start,\n    end,\n    width,\n    stretch,\n    shrink,\n});\nlinebreak.box = (width, start, end, hyphenated = false) => ({\n    type: 'box',\n    width,\n    start,\n    end,\n    hyphenated,\n});\nlinebreak.penalty = (width, penalty, flagged) => ({\n    type: 'penalty',\n    width,\n    penalty,\n    flagged,\n});\n\n/**\n * Add scalar to run\n *\n * @param index - Scalar\n * @param run - Run\n * @returns Added run\n */\nconst add = (index, run) => {\n    const start = run.start + index;\n    const end = run.end + index;\n    return Object.assign({}, run, { start, end });\n};\n\n/**\n * Get run length\n *\n * @param run - Run\n * @returns Length\n */\nconst length = (run) => {\n    return run.end - run.start;\n};\n\n/**\n * Concats two runs into one\n *\n * @param runA - First run\n * @param runB - Second run\n * @returns Concatenated run\n */\nconst concat = (runA, runB) => {\n    const end = runA.end + length(runB);\n    const glyphs = (runA.glyphs || []).concat(runB.glyphs || []);\n    const positions = (runA.positions || []).concat(runB.positions || []);\n    const attributes = Object.assign({}, runA.attributes, runB.attributes);\n    const runAIndices = runA.glyphIndices || [];\n    const runALastIndex = last(runAIndices) || 0;\n    const runBIndices = (runB.glyphIndices || []).map((i) => i + runALastIndex + 1);\n    const glyphIndices = normalize(runAIndices.concat(runBIndices));\n    return Object.assign({}, runA, {\n        end,\n        glyphs,\n        positions,\n        attributes,\n        glyphIndices,\n    });\n};\n\n/**\n * Insert glyph to run in the given index\n *\n * @param index - Index\n * @param glyph - Glyph\n * @param run - Run\n * @returns Run with glyph\n */\nconst insertGlyph$1 = (index, glyph, run) => {\n    if (!glyph)\n        return run;\n    // Split resolves ligature splitting in case new glyph breaks some\n    const leadingRun = slice$1(0, index, run);\n    const trailingRun = slice$1(index, Infinity, run);\n    return concat(append$1(glyph, leadingRun), trailingRun);\n};\n/**\n * Insert either glyph or code point to run in the given index\n *\n * @param index - Index\n * @param value - Glyph or codePoint\n * @param run - Run\n * @returns Run with glyph\n */\nconst insert = (index, value, run) => {\n    const font = getFont(run);\n    const glyph = isNumber(value) ? fromCodePoint(value, font) : value;\n    return insertGlyph$1(index, glyph, run);\n};\n\n/**\n * Get run index at char index\n *\n * @param index - Char index\n * @param attributedString - Attributed string\n * @returns Run index\n */\nconst runIndexAt = (index, attributedString) => {\n    return runIndexAt$1(index, attributedString.runs);\n};\n\n/**\n * Insert glyph into attributed string\n *\n * @param index - Index\n * @param glyph - Glyph or code point\n * @param attributedString - Attributed string\n * @returns Attributed string with new glyph\n */\nconst insertGlyph = (index, glyph, attributedString) => {\n    const runIndex = runIndexAt(index, attributedString);\n    // Add glyph to the end if run index invalid\n    if (runIndex === -1)\n        return append(glyph, attributedString);\n    const codePoints = [glyph] ;\n    const string = attributedString.string.slice(0, index) +\n        stringFromCodePoints(codePoints) +\n        attributedString.string.slice(index);\n    const runs = attributedString.runs.map((run, i) => {\n        if (i === runIndex)\n            return insert(index - run.start, glyph, run);\n        if (i > runIndex)\n            return add(codePoints.length, run);\n        return run;\n    });\n    return Object.assign({}, attributedString, { string, runs });\n};\n\n/**\n * Advance width between two string indices\n *\n * @param start - Glyph index\n * @param end - Glyph index\n * @param run - Run\n * @returns Advanced width run\n */\nconst advanceWidthBetween$1 = (start, end, run) => {\n    const runStart = run.start || 0;\n    const glyphStartIndex = Math.max(0, glyphIndexAt(start - runStart, run));\n    const glyphEndIndex = Math.max(0, glyphIndexAt(end - runStart, run));\n    const positions = (run.positions || []).slice(glyphStartIndex, glyphEndIndex);\n    return advanceWidth$2(positions);\n};\n\n/**\n * Advance width between start and end\n * Does not consider ligature splitting for the moment.\n * Check performance impact on supporting this\n *\n * @param start - Start offset\n * @param end - End offset\n * @param attributedString\n * @returns Advance width\n */\nconst advanceWidthBetween = (start, end, attributedString) => {\n    const runs = filter(start, end, attributedString.runs);\n    return runs.reduce((acc, run) => acc + advanceWidthBetween$1(start, end, run), 0);\n};\n\nconst HYPHEN = 0x002d;\nconst TOLERANCE_STEPS = 5;\nconst TOLERANCE_LIMIT = 50;\nconst opts = {\n    width: 3,\n    stretch: 6,\n    shrink: 9,\n};\n/**\n * Slice attributed string to many lines\n *\n * @param attributedString - Attributed string\n * @param nodes\n * @param breaks\n * @returns Attributed strings\n */\nconst breakLines = (attributedString, nodes, breaks) => {\n    let start = 0;\n    let end = null;\n    const lines = breaks.reduce((acc, breakPoint) => {\n        const node = nodes[breakPoint];\n        const prevNode = nodes[breakPoint - 1];\n        // Last breakpoint corresponds to K&P mandatory final glue\n        if (breakPoint === nodes.length - 1)\n            return acc;\n        let line;\n        if (node.type === 'penalty') {\n            // @ts-expect-error penalty node will always preceed box or glue node\n            end = prevNode.end;\n            line = slice(start, end, attributedString);\n            line = insertGlyph(line.string.length, HYPHEN, line);\n        }\n        else {\n            end = node.end;\n            line = slice(start, end, attributedString);\n        }\n        start = end;\n        return [...acc, line];\n    }, []);\n    // Last line\n    lines.push(slice(start, attributedString.string.length, attributedString));\n    return lines;\n};\n/**\n * Return Knuth & Plass nodes based on line and previously calculated syllables\n *\n * @param attributedString - Attributed string\n * @param attributes - Attributes\n * @param options - Layout options\n * @returns ?\n */\nconst getNodes = (attributedString, { align }, options) => {\n    let start = 0;\n    const hyphenWidth = 5;\n    const { syllables } = attributedString;\n    const hyphenPenalty = options.hyphenationPenalty || (align === 'justify' ? 100 : 600);\n    const result = syllables.reduce((acc, s, index) => {\n        const width = advanceWidthBetween(start, start + s.length, attributedString);\n        if (s.trim() === '') {\n            const stretch = (width * opts.width) / opts.stretch;\n            const shrink = (width * opts.width) / opts.shrink;\n            const end = start + s.length;\n            // Add glue node. Glue nodes are used to fill the space between words.\n            acc.push(linebreak.glue(width, start, end, stretch, shrink));\n        }\n        else {\n            const hyphenated = syllables[index + 1] !== ' ';\n            const end = start + s.length;\n            // Add box node. Box nodes are used to represent words.\n            acc.push(linebreak.box(width, start, end, hyphenated));\n            if (syllables[index + 1] && hyphenated) {\n                // Add penalty node. Penalty nodes are used to represent hyphenation points.\n                acc.push(linebreak.penalty(hyphenWidth, hyphenPenalty, 1));\n            }\n        }\n        start += s.length;\n        return acc;\n    }, []);\n    // Add mandatory final glue\n    result.push(linebreak.glue(0, start, start, linebreak.infinity, 0));\n    result.push(linebreak.penalty(0, -linebreak.infinity, 1));\n    return result;\n};\n/**\n * @param attributedString - Attributed string\n * @returns Attributes\n */\nconst getAttributes = (attributedString) => {\n    return attributedString.runs?.[0]?.attributes || {};\n};\n/**\n * Performs Knuth & Plass line breaking algorithm\n * Fallbacks to best fit algorithm if latter not successful\n *\n * @param options - Layout options\n */\nconst linebreaker = (options) => {\n    /**\n     * @param attributedString - Attributed string\n     * @param availableWidths - Available widths\n     * @returns Attributed string\n     */\n    return (attributedString, availableWidths) => {\n        let tolerance = options.tolerance || 4;\n        const attributes = getAttributes(attributedString);\n        const nodes = getNodes(attributedString, attributes, options);\n        let breaks = linebreak(nodes, availableWidths, tolerance);\n        // Try again with a higher tolerance if the line breaking failed.\n        while (breaks.length === 0 && tolerance < TOLERANCE_LIMIT) {\n            tolerance += TOLERANCE_STEPS;\n            breaks = linebreak(nodes, availableWidths, tolerance);\n        }\n        if (breaks.length === 0 || (breaks.length === 1 && breaks[0] === 0)) {\n            breaks = applyBestFit(nodes, availableWidths);\n        }\n        return breakLines(attributedString, nodes, breaks.slice(1));\n    };\n};\n\nvar Direction;\n(function (Direction) {\n    Direction[Direction[\"GROW\"] = 0] = \"GROW\";\n    Direction[Direction[\"SHRINK\"] = 1] = \"SHRINK\";\n})(Direction || (Direction = {}));\nconst WHITESPACE_PRIORITY = 1;\nconst LETTER_PRIORITY = 2;\nconst EXPAND_WHITESPACE_FACTOR = {\n    before: 0.5,\n    after: 0.5,\n    priority: WHITESPACE_PRIORITY,\n    unconstrained: false,\n};\nconst EXPAND_CHAR_FACTOR = {\n    before: 0.14453125, // 37/256\n    after: 0.14453125,\n    priority: LETTER_PRIORITY,\n    unconstrained: false,\n};\nconst SHRINK_WHITESPACE_FACTOR = {\n    before: -0.04296875, // -11/256\n    after: -0.04296875,\n    priority: WHITESPACE_PRIORITY,\n    unconstrained: false,\n};\nconst SHRINK_CHAR_FACTOR = {\n    before: -0.04296875,\n    after: -0.04296875,\n    priority: LETTER_PRIORITY,\n    unconstrained: false,\n};\nconst getCharFactor = (direction, options) => {\n    const expandCharFactor = options.expandCharFactor || {};\n    const shrinkCharFactor = options.shrinkCharFactor || {};\n    return direction === Direction.GROW\n        ? Object.assign({}, EXPAND_CHAR_FACTOR, expandCharFactor)\n        : Object.assign({}, SHRINK_CHAR_FACTOR, shrinkCharFactor);\n};\nconst getWhitespaceFactor = (direction, options) => {\n    const expandWhitespaceFactor = options.expandWhitespaceFactor || {};\n    const shrinkWhitespaceFactor = options.shrinkWhitespaceFactor || {};\n    return direction === Direction.GROW\n        ? Object.assign({}, EXPAND_WHITESPACE_FACTOR, expandWhitespaceFactor)\n        : Object.assign({}, SHRINK_WHITESPACE_FACTOR, shrinkWhitespaceFactor);\n};\nconst factor = (direction, options) => (glyphs) => {\n    const charFactor = getCharFactor(direction, options);\n    const whitespaceFactor = getWhitespaceFactor(direction, options);\n    const factors = [];\n    for (let index = 0; index < glyphs.length; index += 1) {\n        let f;\n        const glyph = glyphs[index];\n        if (isWhiteSpace(glyph)) {\n            f = Object.assign({}, whitespaceFactor);\n            if (index === glyphs.length - 1) {\n                f.before = 0;\n                if (index > 0) {\n                    factors[index - 1].after = 0;\n                }\n            }\n        }\n        else if (glyph.isMark && index > 0) {\n            f = Object.assign({}, factors[index - 1]);\n            f.before = 0;\n            factors[index - 1].after = 0;\n        }\n        else {\n            f = Object.assign({}, charFactor);\n        }\n        factors.push(f);\n    }\n    return factors;\n};\nconst getFactors = (gap, line, options) => {\n    const direction = gap > 0 ? Direction.GROW : Direction.SHRINK;\n    const getFactor = factor(direction, options);\n    const factors = line.runs.reduce((acc, run) => {\n        return acc.concat(getFactor(run.glyphs));\n    }, []);\n    factors[0].before = 0;\n    factors[factors.length - 1].after = 0;\n    return factors;\n};\n\nconst KASHIDA_PRIORITY = 0;\nconst NULL_PRIORITY = 3;\nconst getDistances = (gap, factors) => {\n    let total = 0;\n    const priorities = [];\n    const unconstrained = [];\n    for (let priority = KASHIDA_PRIORITY; priority <= NULL_PRIORITY; priority += 1) {\n        priorities[priority] = unconstrained[priority] = 0;\n    }\n    // sum the factors at each priority\n    for (let j = 0; j < factors.length; j += 1) {\n        const f = factors[j];\n        const sum = f.before + f.after;\n        total += sum;\n        priorities[f.priority] += sum;\n        if (f.unconstrained) {\n            unconstrained[f.priority] += sum;\n        }\n    }\n    // choose the priorities that need to be applied\n    let highestPriority = -1;\n    let highestPrioritySum = 0;\n    let remainingGap = gap;\n    let priority;\n    for (priority = KASHIDA_PRIORITY; priority <= NULL_PRIORITY; priority += 1) {\n        const prioritySum = priorities[priority];\n        if (prioritySum !== 0) {\n            if (highestPriority === -1) {\n                highestPriority = priority;\n                highestPrioritySum = prioritySum;\n            }\n            // if this priority covers the remaining gap, we're done\n            if (Math.abs(remainingGap) <= Math.abs(prioritySum)) {\n                priorities[priority] = remainingGap / prioritySum;\n                unconstrained[priority] = 0;\n                remainingGap = 0;\n                break;\n            }\n            // mark that we need to use 100% of the adjustment from\n            // this priority, and subtract the space that it consumes\n            priorities[priority] = 1;\n            remainingGap -= prioritySum;\n            // if this priority has unconstrained glyphs, let them consume the remaining space\n            if (unconstrained[priority] !== 0) {\n                unconstrained[priority] = remainingGap / unconstrained[priority];\n                remainingGap = 0;\n                break;\n            }\n        }\n    }\n    // zero out remaining priorities (if any)\n    for (let p = priority + 1; p <= NULL_PRIORITY; p += 1) {\n        priorities[p] = 0;\n        unconstrained[p] = 0;\n    }\n    // if there is still space left over, assign it to the highest priority that we saw.\n    // this violates their factors, but it only happens in extreme cases\n    if (remainingGap > 0 && highestPriority > -1) {\n        priorities[highestPriority] =\n            (highestPrioritySum + (gap - total)) / highestPrioritySum;\n    }\n    // create and return an array of distances to add to each glyph's advance\n    const distances = [];\n    for (let index = 0; index < factors.length; index += 1) {\n        // the distance to add to this glyph is the sum of the space to add\n        // after this glyph, and the space to add before the next glyph\n        const f = factors[index];\n        const next = factors[index + 1];\n        let dist = f.after * priorities[f.priority];\n        if (next) {\n            dist += next.before * priorities[next.priority];\n        }\n        // if this glyph is unconstrained, add the unconstrained distance as well\n        if (f.unconstrained) {\n            dist += f.after * unconstrained[f.priority];\n            if (next) {\n                dist += next.before * unconstrained[next.priority];\n            }\n        }\n        distances.push(dist);\n    }\n    return distances;\n};\n\n/**\n * Adjust run positions by given distances\n *\n * @param distances\n * @param line\n * @returns Line\n */\nconst justifyLine = (distances, line) => {\n    let index = 0;\n    for (const run of line.runs) {\n        for (const position of run.positions) {\n            position.xAdvance += distances[index++];\n        }\n    }\n    return line;\n};\n/**\n * A JustificationEngine is used by a Typesetter to perform line fragment\n * justification. This implementation is based on a description of Apple's\n * justification algorithm from a PDF in the Apple Font Tools package.\n *\n * @param options - Layout options\n */\nconst justification = (options) => {\n    /**\n     * @param line\n     * @returns Line\n     */\n    return (line) => {\n        const gap = line.box.width - advanceWidth(line);\n        if (gap === 0)\n            return line; // Exact fit\n        const factors = getFactors(gap, line, options);\n        const distances = getDistances(gap, factors);\n        return justifyLine(distances, line);\n    };\n};\n\n/**\n * Returns attributed string ascent\n *\n * @param attributedString - Attributed string\n * @returns Ascent\n */\nconst ascent = (attributedString) => {\n    const reducer = (acc, run) => Math.max(acc, ascent$1(run));\n    return attributedString.runs.reduce(reducer, 0);\n};\n\n// The base font size used for calculating underline thickness.\nconst BASE_FONT_SIZE = 12;\n/**\n * A TextDecorationEngine is used by a Typesetter to generate\n * DecorationLines for a line fragment, including underlines\n * and strikes.\n */\nconst textDecoration = () => (line) => {\n    let x = line.overflowLeft || 0;\n    const overflowRight = line.overflowRight || 0;\n    const maxX = advanceWidth(line) - overflowRight;\n    line.decorationLines = [];\n    for (let i = 0; i < line.runs.length; i += 1) {\n        const run = line.runs[i];\n        const width = Math.min(maxX - x, advanceWidth$1(run));\n        const thickness = Math.max(0.5, Math.floor(run.attributes.fontSize / BASE_FONT_SIZE));\n        if (run.attributes.underline) {\n            const rect = {\n                x,\n                y: ascent(line) + thickness * 2,\n                width,\n                height: thickness,\n            };\n            const decorationLine = {\n                rect,\n                opacity: run.attributes.opacity,\n                color: run.attributes.underlineColor || 'black',\n                style: run.attributes.underlineStyle || 'solid',\n            };\n            line.decorationLines.push(decorationLine);\n        }\n        if (run.attributes.strike) {\n            const y = ascent(line) - ascent$1(run) / 3;\n            const rect = { x, y, width, height: thickness };\n            const decorationLine = {\n                rect,\n                opacity: run.attributes.opacity,\n                color: run.attributes.strikeColor || 'black',\n                style: run.attributes.strikeStyle || 'solid',\n            };\n            line.decorationLines.push(decorationLine);\n        }\n        x += width;\n    }\n    return line;\n};\n\nconst ignoredScripts = ['Common', 'Inherited', 'Unknown'];\n/**\n * Resolves unicode script in runs, grouping equal runs together\n */\nconst scriptItemizer = () => {\n    /**\n     * @param attributedString - Attributed string\n     * @returns Attributed string\n     */\n    return (attributedString) => {\n        const { string } = attributedString;\n        let lastScript = 'Unknown';\n        let lastIndex = 0;\n        let index = 0;\n        const runs = [];\n        if (!string)\n            return empty();\n        for (let i = 0; i < string.length; i += 1) {\n            const char = string[i];\n            const codePoint = char.codePointAt(0);\n            const script = unicode.getScript(codePoint);\n            if (script !== lastScript && !ignoredScripts.includes(script)) {\n                if (lastScript !== 'Unknown') {\n                    runs.push({\n                        start: lastIndex,\n                        end: index,\n                        attributes: { script: lastScript },\n                    });\n                }\n                lastIndex = index;\n                lastScript = script;\n            }\n            index += char.length;\n        }\n        if (lastIndex < string.length) {\n            runs.push({\n                start: lastIndex,\n                end: string.length,\n                attributes: { script: lastScript },\n            });\n        }\n        const result = { string, runs: runs };\n        return result;\n    };\n};\n\nconst SOFT_HYPHEN = '\\u00ad';\nconst hyphenator = hyphen(pattern);\n/**\n * @param word\n * @returns Word parts\n */\nconst splitHyphen = (word) => {\n    return word.split(SOFT_HYPHEN);\n};\nconst cache = {};\n/**\n * @param word\n * @returns Word parts\n */\nconst getParts = (word) => {\n    const base = word.includes(SOFT_HYPHEN) ? word : hyphenator(word);\n    return splitHyphen(base);\n};\nconst wordHyphenation = () => {\n    /**\n     * @param word - Word\n     * @returns Word parts\n     */\n    return (word) => {\n        const cacheKey = `_${word}`;\n        if (isNil(word))\n            return [];\n        if (cache[cacheKey])\n            return cache[cacheKey];\n        cache[cacheKey] = getParts(word);\n        return cache[cacheKey];\n    };\n};\n\nconst IGNORED_CODE_POINTS = [173];\nconst getFontSize = (run) => run.attributes.fontSize || 12;\nconst pickFontFromFontStack = (codePoint, fontStack, lastFont) => {\n    const fontStackWithFallback = [...fontStack, lastFont];\n    for (let i = 0; i < fontStackWithFallback.length; i += 1) {\n        const font = fontStackWithFallback[i];\n        if (!IGNORED_CODE_POINTS.includes(codePoint) &&\n            font &&\n            font.hasGlyphForCodePoint &&\n            font.hasGlyphForCodePoint(codePoint)) {\n            return font;\n        }\n    }\n    return fontStack.at(-1);\n};\nconst fontSubstitution = () => ({ string, runs }) => {\n    let lastFont = null;\n    let lastFontSize = null;\n    let lastIndex = 0;\n    let index = 0;\n    const res = [];\n    for (let i = 0; i < runs.length; i += 1) {\n        const run = runs[i];\n        if (string.length === 0) {\n            res.push({\n                start: 0,\n                end: 0,\n                attributes: { font: run.attributes.font },\n            });\n            break;\n        }\n        const chars = string.slice(run.start, run.end);\n        for (let j = 0; j < chars.length; j += 1) {\n            const char = chars[j];\n            const codePoint = char.codePointAt(0);\n            // If the default font does not have a glyph and the fallback font does, we use it\n            const font = pickFontFromFontStack(codePoint, run.attributes.font, lastFont);\n            const fontSize = getFontSize(run);\n            // If anything that would impact res has changed, update it\n            if (font !== lastFont ||\n                fontSize !== lastFontSize ||\n                font.unitsPerEm !== lastFont.unitsPerEm) {\n                if (lastFont) {\n                    res.push({\n                        start: lastIndex,\n                        end: index,\n                        attributes: {\n                            font: [lastFont],\n                            scale: lastFontSize / lastFont.unitsPerEm,\n                        },\n                    });\n                }\n                lastFont = font;\n                lastFontSize = fontSize;\n                lastIndex = index;\n            }\n            index += char.length;\n        }\n    }\n    if (lastIndex < string.length) {\n        const fontSize = getFontSize(last(runs));\n        res.push({\n            start: lastIndex,\n            end: string.length,\n            attributes: {\n                font: [lastFont],\n                scale: fontSize / lastFont.unitsPerEm,\n            },\n        });\n    }\n    return { string, runs: res };\n};\n\nexport { bidiEngine as bidi, layoutEngine as default, fontSubstitution, fromFragments, justification, linebreaker, scriptItemizer, textDecoration, wordHyphenation };\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA;;;;;CAKC,GACD,MAAM,gBAAgB,CAAC;IACnB,IAAI,SAAS;IACb,IAAI,SAAS;IACb,MAAM,OAAO,EAAE;IACf,UAAU,OAAO,CAAC,CAAC;QACf,UAAU,SAAS,MAAM;QACzB,KAAK,IAAI,CAAC;YACN,GAAG,QAAQ;YACX,OAAO;YACP,KAAK,SAAS,SAAS,MAAM,CAAC,MAAM;YACpC,YAAY,SAAS,UAAU,IAAI,CAAC;QACxC;QACA,UAAU,SAAS,MAAM,CAAC,MAAM;IACpC;IACA,OAAO;QAAE;QAAQ;IAAK;AAC1B;AAEA;;;;;;CAMC,GACD,MAAM,2BAA2B,CAAC,OAAS;QAAC;KAAK;AACjD;;;;;CAKC,GACD,MAAM,YAAY,CAAC,UAAU,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;IACzC;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,YAAY,EAAE;QACpB,MAAM,YAAY,EAAE;QACpB,MAAM,gBAAgB,QAAQ,mBAAmB,IAC7C,QAAQ,eAAe,QACvB;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAI,CAAC,MAAM,EAAE,KAAK,EAAG;YACtD,IAAI,SAAS;YACb,MAAM,MAAM,iBAAiB,IAAI,CAAC,EAAE;YACpC,MAAM,QAAQ,iBAAiB,MAAM,CAChC,KAAK,CAAC,IAAI,KAAK,EAAE,IAAI,GAAG,EACxB,KAAK,CAAC,WACN,MAAM,CAAC;YACZ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;gBACtC,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,MAAM,QAAQ,cAAc;gBAC5B,UAAU,IAAI,IAAI;gBAClB,UAAU,MAAM,IAAI,CAAC;YACzB;YACA,UAAU,IAAI,CAAC;gBAAE,GAAG,GAAG;gBAAE;YAAO;QACpC;QACA,MAAM,SAAS;YAAE,GAAG,cAAc,UAAU;YAAE;QAAU;QACxD,OAAO;IACX;AACJ;AAEA;;;;;CAKC,GACD,MAAM,OAAO,CAAC;IACV,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG;AAC7B;AAEA;;;;;;CAMC,GACD,MAAM,YAAY,CAAC,MAAM;IACrB,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAO;IAC3C,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAC9B,GAAG,KAAK,CAAC,GAAG;QACZ,QAAQ,KAAK,MAAM,GAAG;IAC1B;IACA,OAAO;QAAC;QAAG;KAAE;AACjB;AAEA;;;;;;CAMC,GACD,MAAM,OAAO,CAAC,QAAQ;IAClB,MAAM,GAAG,OAAO,GAAG,UAAU,MAAM;IACnC,OAAO;AACX;AAEA;;;;;CAKC,GACD,MAAM,WAAW,CAAC;IACd,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,GAAG,CAAC,MAAM,EAAE;AACpE;AAEA;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC;IACpB,MAAM,aAAa,IAAI,UAAU,IAAI,CAAC;IACtC,MAAM,WAAW,WAAW,QAAQ,IAAI;IACxC,MAAM,OAAO,WAAW,IAAI;IAC5B,MAAM,aAAa,OAAO,SAAS,WAAW,OAAO,MAAM,CAAC,EAAE,EAAE;IAChE,OAAO,aAAa,WAAW,aAAa;AAChD;AACA;;;;;CAKC,GACD,MAAM,QAAQ,CAAC;IACX,OAAO,IAAI,UAAU,EAAE,SAAS,eAAe;AACnD;AAEA;;;;;;;;;;;;CAYC,GACD,MAAM,SAAS,CAAC,OAAO;IACnB,IAAI,CAAC,KACD,OAAO;IACX,MAAM,eAAe,IAAI,YAAY,IAAI,EAAE;IAC3C,MAAM,QAAQ,YAAY,CAAC,MAAM;IACjC,OAAO,aAAa,KAAK,CAAC,GAAG,OAAO,MAAM,CAAC,CAAC,IAAM,MAAM,OAAO,MAAM;AACzE;AAEA;;;;;CAKC,GACD,MAAM,UAAU,CAAC;IACb,OAAO,IAAI,UAAU,EAAE,MAAM,CAAC,EAAE,IAAI;AACxC;AAEA;;;;;;;;;CASC,GACD,MAAM,UAAU,CAAC,OAAO,KAAK,MAAM;IAC/B,IAAI,CAAC,OACD,OAAO,EAAE;IACb,IAAI,UAAU,KACV,OAAO,EAAE;IACb,IAAI,UAAU,KAAK,QAAQ,MAAM,UAAU,CAAC,MAAM,EAC9C,OAAO;QAAC;KAAM;IAClB,MAAM,aAAa,MAAM,UAAU,CAAC,KAAK,CAAC,OAAO;IACjD,MAAM,SAAS,OAAO,aAAa,IAAI;IACvC,yDAAyD;IACzD,OAAO,OACD,KAAK,MAAM,CAAC,QAAQ,WAAW,WAAW,WAAW,OAAO,MAAM,GAClE;QAAC;KAAM;AACjB;AAEA;;;;;;;CAOC,GACD,MAAM,eAAe,CAAC,OAAO;IACzB,MAAM,SAAS,KAAK,cAAc,CAAC,MAAM;IACzC,OAAO,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,UAAU,QAAQ;AACnC;AAEA;;;;;CAKC,GACD,MAAM,YAAY,CAAC;IACf,MAAM,OAAO,KAAK,CAAC,EAAE;IACrB,OAAO,MAAM,GAAG,CAAC,CAAC,QAAU,QAAQ;AACxC;AAEA;;;;;;;CAOC,GACD,MAAM,UAAU,CAAC,OAAO,KAAK;IACzB,MAAM,WAAW,MAAM;IACvB,MAAM,OAAO,QAAQ;IACrB,kCAAkC;IAClC,MAAM,aAAa,aAAa,OAAO;IACvC,MAAM,WAAW,aAAa,KAAK;IACnC,0BAA0B;IAC1B,MAAM,aAAa,IAAI,MAAM,EAAE,CAAC,WAAW;IAC3C,MAAM,WAAW,IAAI,MAAM,EAAE,CAAC,SAAS;IACvC,qCAAqC;IACrC,MAAM,cAAc,OAAO,OAAO;IAClC,MAAM,cAAc,cAAc,IAAI,QAAQ,aAAa,UAAU,MAAM,cAAc,EAAE;IAC3F,mCAAmC;IACnC,MAAM,YAAY,OAAO,KAAK;IAC9B,MAAM,YAAY,QAAQ,GAAG,WAAW,MAAM;IAC9C,qBAAqB;IACrB,MAAM,aAAa,aAAa,KAAK,GAAG,CAAC,GAAG;IAC5C,MAAM,SAAS,CAAC,IAAI,MAAM,IAAI,EAAE,EAAE,KAAK,CAAC,YAAY;IACpD,wBAAwB;IACxB,MAAM,gBAAgB,CAAC,IAAM,CAAC;YAC1B,UAAU,EAAE,YAAY,GAAG;YAC3B,UAAU;YACV,SAAS;YACT,SAAS;QACb,CAAC;IACD,MAAM,iBAAiB,YAAY,GAAG,CAAC;IACvC,MAAM,YAAY,CAAC,IAAI,SAAS,IAAI,EAAE,EAAE,KAAK,CAAC,YAAY;IAC1D,MAAM,eAAe,UAAU,GAAG,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;QAC1B,OAAO,IAAI,KAAK,GAAG;QACnB,KAAK,KAAK,GAAG,CAAC,IAAI,GAAG,EAAE,IAAI,KAAK,GAAG;QACnC,cAAc,UAAU,CAAC,IAAI,YAAY,IAAI,EAAE,EAAE,KAAK,CAAC,OAAO;QAC9D,QAAQ;YAAC;YAAa;YAAQ;SAAU,CAAC,IAAI;QAC7C,WAAW;YAAC;YAAgB;YAAW;SAAa,CAAC,IAAI;IAC7D;AACJ;AAEA;;;;;;CAMC,GACD,MAAM,eAAe,CAAC,OAAO;IACzB,IAAI,CAAC,MACD,OAAO,CAAC;IACZ,OAAO,KAAK,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK,IAAI,SAAS,QAAQ,IAAI,GAAG;AACxE;AAEA;;;;;;;CAOC,GACD,MAAM,SAAS,CAAC,OAAO,KAAK;IACxB,MAAM,aAAa,aAAa,OAAO;IACvC,MAAM,WAAW,KAAK,GAAG,CAAC,aAAa,MAAM,GAAG,OAAO;IACvD,OAAO,KAAK,KAAK,CAAC,YAAY,WAAW;AAC7C;AAEA;;;;;;CAMC,GACD,MAAM,WAAW,CAAC,OAAO;IACrB,MAAM,QAAQ,IAAI,KAAK,GAAG;IAC1B,MAAM,MAAM,IAAI,GAAG,GAAG;IACtB,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;QAAE;QAAO;IAAI;AAC/C;AAEA;;;;;;;CAOC,GACD,MAAM,YAAY,CAAC,OAAO,KAAK;IAC3B,MAAM,gBAAgB,CAAC,IAAM,QAAQ,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;IACrE,MAAM,eAAe,CAAC,IAAM,QAAQ,GAAG,MAAM,EAAE,KAAK,EAAE;IACtD,OAAO,KAAK,GAAG,CAAC,CAAC,KAAK;QAClB,IAAI,SAAS;QACb,MAAM,UAAU,MAAM;QACtB,MAAM,SAAS,CAAC,WAAW,MAAM,KAAK,MAAM,GAAG;QAC/C,IAAI,SACA,SAAS,cAAc;QAC3B,IAAI,QACA,SAAS,aAAa;QAC1B,OAAO,SAAS,OAAO;IAC3B;AACJ;AACA;;;;;;;CAOC,GACD,MAAM,QAAQ,CAAC,OAAO,KAAK;IACvB,IAAI,iBAAiB,MAAM,CAAC,MAAM,KAAK,GACnC,OAAO;IACX,MAAM,SAAS,iBAAiB,MAAM,CAAC,KAAK,CAAC,OAAO;IACpD,MAAM,eAAe,OAAO,OAAO,KAAK,iBAAiB,IAAI;IAC7D,MAAM,aAAa,UAAU,OAAO,KAAK;IACzC,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,kBAAkB;QAAE;QAAQ,MAAM;IAAW;AAC1E;AAEA,MAAM,gBAAgB,CAAC;IACnB,OAAO,OAAO,MAAM,CAAC;AACzB;AACA,MAAM,oBAAoB,CAAC;IACvB,MAAM,QAAQ,OAAO,KAAK,CAAC;IAC3B,OAAO,QAAQ,OAAO,WAAW,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,IAAI,CAAC;AAClE;AACA;;;;;CAKC,GACD,MAAM,OAAO,CAAC;IACV,MAAM,QAAQ,cAAc,iBAAiB,MAAM;IACnD,MAAM,MAAM,kBAAkB,iBAAiB,MAAM;IACrD,OAAO,MAAM,OAAO,MAAM,GAAG;AACjC;AAEA;;;;CAIC,GACD,MAAM,UAAU;IACZ,OAAO;QACH,OAAO;QACP,KAAK;QACL,cAAc,EAAE;QAChB,QAAQ,EAAE;QACV,WAAW,EAAE;QACb,YAAY,CAAC;IACjB;AACJ;AAEA;;;;;CAKC,GACD,MAAM,WAAW,CAAC;IACd,OAAO,OAAO,UAAU;AAC5B;AAEA;;;;;;;;CAQC,GACD,MAAM,gBAAgB,CAAC,QAAQ;IAC3B,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE;IACvB,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,aAAa,IAAI,YAAY;IACjD,MAAM,aAAa,MAAM,QAAQ,IAAI,CAAC;IACtC,OAAO,QAAQ,MAAM,CAAC;AAC1B;AAEA;;;;;;GAMG,GACH,MAAM,gBAAgB,CAAC,OAAO;IAC1B,IAAI,OAAO,SAAS,UAChB,OAAO;IACX,OAAO,QAAQ,QAAQ,KAAK,iBAAiB,CAAC,SAAS;AAC3D;AAEA;;;;;;CAMC,GACD,MAAM,cAAc,CAAC,OAAO;IACxB,MAAM,cAAc,MAAM,UAAU,EAAE,UAAU;IAChD,MAAM,MAAM,IAAI,GAAG,GAAG;IACtB,MAAM,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC;IACjC,MAAM,eAAe,cAAc,aAAa,IAAI,YAAY;IAChE,IAAI,CAAC,IAAI,SAAS,EACd,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;QAAE;QAAK;QAAQ;IAAa;IAC9D,MAAM,YAAY,IAAI,SAAS,CAAC,MAAM,CAAC;QACnC,UAAU,MAAM,YAAY,GAAG,MAAM;QACrC,UAAU;QACV,SAAS;QACT,SAAS;IACb;IACA,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;QAAE;QAAK;QAAQ;QAAc;IAAU;AACzE;AACA;;;;;;CAMC,GACD,MAAM,WAAW,CAAC,OAAO;IACrB,IAAI,CAAC,OACD,OAAO;IACX,MAAM,OAAO,QAAQ;IACrB,MAAM,QAAQ,SAAS,SAAS,cAAc,OAAO,QAAQ;IAC7D,OAAO,YAAY,OAAO;AAC9B;AAEA;;;;;CAKC,GACD,MAAM,uBAAuB,CAAC;IAC1B,OAAO,OAAO,aAAa,IAAK,cAAc,EAAE;AACpD;AAEA;;;;;;CAMC,GACD,MAAM,SAAS,CAAC,OAAO;IACnB,MAAM,aAAa,OAAO,UAAU,WAAW;QAAC;KAAM,GAAG,OAAO;IAChE,MAAM,mBAAmB,qBAAqB,cAAc,EAAE;IAC9D,MAAM,SAAS,iBAAiB,MAAM,GAAG;IACzC,MAAM,YAAY,iBAAiB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;IAClD,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,IAAI,KAAK;IAC/C,MAAM,OAAO,UAAU,MAAM,CAAC,SAAS,OAAO;IAC9C,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,kBAAkB;QAAE;QAAQ;IAAK;AAC9D;AAEA,MAAM,mBAAmB;AACzB,MAAM,kBAAkB,OAAO,YAAY,CAAC;AAC5C;;;;;CAKC,GACD,MAAM,uBAAuB,CAAC;IAC1B,IAAI,CAAC,KAAK,MAAM,EACZ,OAAO;IACX,MAAM,CAAC,WAAW,GAAG,KAAK,MAAM,CAAC;IACjC,OAAO,SAAS,UAAU,CAAC,EAAE,EAAE;AACnC;AACA;;;;;CAKC,GACD,MAAM,WAAW,CAAC;IACd,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,YAAY,QAAQ,EAAE;IACxC,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,OAAO,YAAY,IAAI,CAAC,EAAE;IAC5C,IAAI,MAAM;QACN,MAAM,QAAQ,UAAU,MAAM,GAAG;QACjC,MAAM,YAAY,qBAAqB;QACvC,MAAM,QAAQ,KAAK,iBAAiB,CAAC;QACrC,MAAM,YAAY,OAAO,OAAO,KAAK,SAAS,CAAC,MAAM;QACrD,OAAO,OAAO,MAAM,CAAC,EAAE,EAAE,WAAW;YAAE,CAAC,MAAM,EAAE;QAAU;IAC7D;IACA,OAAO;AACX;AAEA;;;;;;CAMC,GACD,MAAM,OAAO,CAAC,OAAO;IACjB,MAAM,aAAa,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,UAAU;IACnD,OAAO,UAAU,CAAC,MAAM;IACxB,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;QAAE;IAAW;AAC/C;AAEA;;;;;CAKC,GACD,MAAM,WAAW,CAAC;IACd,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,IAAI,UAAU;IAC3C,MAAM,mBAAmB,YAAY,UAAU;IAC/C,MAAM,aAAa,OAAO,SAAS,WAAW,IAAI,MAAM,CAAC,EAAE,EAAE,UAAU;IACvE,OAAO,KAAK,GAAG,CAAC,kBAAkB,aAAa,MAAM;AACzD;AAEA;;;;;CAKC,GACD,MAAM,UAAU,CAAC;IACb,MAAM,OAAO,IAAI,UAAU,EAAE;IAC7B,MAAM,cAAc,OAAO,SAAS,WAAW,IAAI,MAAM,CAAC,EAAE,EAAE,WAAW;IACzE,OAAO,MAAM,OAAO;AACxB;AAEA;;;;;CAKC,GACD,MAAM,UAAU,CAAC;IACb,MAAM,OAAO,IAAI,UAAU,EAAE;IAC7B,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,MAAM,CAAC,EAAE,EAAE,WAAW;IACrE,OAAO,UAAU,MAAM;AAC3B;AAEA;;;;;CAKC,GACD,MAAM,WAAW,CAAC;IACd,MAAM,aAAa,IAAI,UAAU,EAAE;IACnC,OAAO,cAAc,QAAQ,OAAO,SAAS,OAAO,QAAQ;AAChE;AAEA;;;;;CAKC,GACD,MAAM,SAAS,CAAC;IACZ,MAAM,UAAU,CAAC,KAAK,MAAQ,KAAK,GAAG,CAAC,KAAK,SAAS;IACrD,OAAO,iBAAiB,IAAI,CAAC,MAAM,CAAC,SAAS;AACjD;AAEA;;;;;;CAMC,GACD,MAAM,aAAa,CAAC,GAAG;IACnB,MAAM,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC3B,MAAM,OAAO,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK;IAClD,MAAM,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IAC3B,MAAM,OAAO,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,EAAE,MAAM;IACpD,OAAO,QAAQ,KAAK,QAAQ;AAChC;AAEA,MAAM,kBAAkB,CAAC,UAAU;IAC/B,IAAI,CAAC,WAAW,aAAa,WACzB,OAAO;QAAC;KAAS;IACrB,MAAM,SAAS,YAAY,CAAC;IAC5B,MAAM,OAAO,YAAY,CAAC,GAAG,YAAY,KAAK;IAC9C,MAAM,SAAS,SAAS,CAAC;IACzB,MAAM,OAAO,SAAS,CAAC,GAAG,SAAS,KAAK;IACxC,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;QAAE,OAAO,SAAS;IAAO;IAC/D,MAAM,IAAI,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;QAAE,GAAG;QAAM,OAAO,OAAO;IAAK;IACpE,OAAO;QAAC;QAAG;KAAE,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,KAAK,GAAG;AAC1C;AACA,MAAM,mBAAmB,CAAC,MAAM;IAC5B,IAAI,YAAY;QAAC;KAAK;IACtB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,KAAK,EAAG;QAC7C,MAAM,cAAc,YAAY,CAAC,EAAE;QACnC,YAAY,UAAU,MAAM,CAAC,CAAC,KAAK;YAC/B,MAAM,SAAS,gBAAgB,UAAU;YACzC,OAAO,IAAI,MAAM,CAAC;QACtB,GAAG,EAAE;IACT;IACA,OAAO;AACX;AACA,MAAM,oBAAoB,CAAC,WAAW;IAClC,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,GAAG;IAClC,IAAI,CAAC,cACD,OAAO;QAAC;KAAK;IACjB,MAAM,YAAY,EAAE;IACpB,MAAM,OAAO,KAAK,GAAG,IAAI,aAAa,GAAG,CAAC,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,MAAM;IAC/D,IAAI,cAAc;IAClB,MAAO,YAAY,CAAC,GAAG,KAAM;QACzB,MAAM,CAAC,UAAU,KAAK,GAAG,UAAU,aAAa;QAChD,MAAM,oBAAoB,iBAAiB,UAAU;QACrD,cAAc;QACd,UAAU,IAAI,IAAI;IACtB;IACA,OAAO;WAAI;QAAW;KAAY;AACtC;AAEA,MAAM,oBAAoB,UAAU,QAAQ;AAC5C;;;;;CAKC,GACD,MAAM,mBAAmB,CAAC;IACtB,MAAM,cAAc,CAAC,KAAK,MAAM,CAAC,QAAQ,CAAC;IAC1C,IAAI,CAAC,aACD,OAAO;IACX,MAAM,OAAO,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAQ,KAAK,cAAc;IACvD,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;IAAK;AAC1C;AACA;;;;;;;CAOC,GACD,MAAM,cAAc,CAAC,OAAO,OAAO;IAC/B,IAAI,OAAO,MAAM,KAAK;IACtB,IAAI,WAAW,KAAK,CAAC;IACrB,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM;QACpB,MAAM,aAAa,MAAM,IAAI,SAAS;QACtC,MAAM,QAAQ,KAAK,IAAI,EAAE,CAAC,EAAE,EAAE,cAAc,CAAC;QAC7C,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,OAAO,MAAM,UAAU;QACxD,IAAI,WAAW,WAAW,KAAK,CAAC,GAAG,KAAK,MAAM,IAAI,MAAM,MAAM,GAAG,GAAG;YAChE,OAAO,MAAM,KAAK;YAClB,WAAW,KAAK,CAAC;QACrB;QACA,MAAM,UAAU;YACZ,QAAQ,KAAK,MAAM;YACnB,MAAM,KAAK,IAAI;YACf,KAAK;gBACD,GAAG,KAAK,CAAC,GAAG;gBACZ,GAAG;gBACH,OAAO,KAAK,KAAK,GAAG;gBACpB,QAAQ;YACZ;QACJ;QACA,YAAY;QACZ,OAAO,iBAAiB;IAC5B;AACJ;AACA;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC,SAAS,UAAU,CAAC,CAAC;IAC1C;;;;KAIC,GACD,OAAO,CAAC,WAAW;QACf,MAAM,WAAW,OAAO;QACxB,MAAM,SAAS,UAAU,IAAI,EAAE,CAAC,EAAE,EAAE,YAAY,UAAU;QAC1D,MAAM,QAAQ,kBAAkB,WAAW;QAC3C,MAAM,kBAAkB,MAAM,GAAG,CAAC,CAAC,IAAM,EAAE,KAAK;QAChD,gBAAgB,OAAO,CAAC,eAAe,CAAC,EAAE,GAAG;QAC7C,MAAM,QAAQ,QAAQ,WAAW,CAAC,SAAS,WAAW;QACtD,OAAO,YAAY,OAAO,OAAO;IACrC;AACJ;AAEA;;;;;;CAMC,GACD,MAAM,gBAAgB,CAAC,QAAQ;IAC3B,MAAM,WAAW,EAAE;IACnB,IAAI,UAAU;IACd,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,KAAK,EAAG;QAC1C,MAAM,OAAO,SAAS,CAAC,EAAE;QACzB,WAAW,KAAK,GAAG,CAAC,MAAM;QAC1B,IAAI,UAAU,QAAQ;YAClB,SAAS,IAAI,CAAC;QAClB,OACK;YACD;QACJ;IACJ;IACA,OAAO;AACX;AAEA;;;;;;;CAOC,GACD,MAAM,aAAa,CAAC,SAAS,SAAS;IAClC;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,SAAS,EAAE;QACjB,MAAM,aAAa;eAAI;SAAkB;QACzC,MAAM,SAAS,gBAAgB,SAAS;QACxC,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,UAAU,QAAQ,IAAI,WAAW,UAAU,QAAQ;QAC1E,MAAM,mBAAmB,UAAU,YAAY,KAAK;QACpD,IAAI,aAAa;QACjB,IAAI,gBAAgB,KAAK;QACzB,IAAI,gBAAgB,WAAW,KAAK;QACpC,MAAO,aAAa,KAAK,cAAe;YACpC,MAAM,YAAY,OAAO,eAAe;YACxC,MAAM,cAAc,UAAU,KAAK,CAAC,GAAG;YACvC,MAAM,cAAc,SAAS;YAC7B,MAAM,iBAAiB,oBAAoB,UAAU,MAAM,KAAK,YAAY,MAAM;YAClF,cAAc,YAAY,MAAM;YAChC,IAAI,cAAc,MAAM,IAAI,aAAa;gBACrC,OAAO,IAAI,CAAC,iBAAiB,SAAS,eAAe;gBACrD,gBAAgB,KAAK,aAAa;gBAClC,gBAAgB,WAAW,KAAK;YACpC,OACK;gBACD,OAAO,IAAI,CAAC,SAAS,cAAc,cAAc,MAAM,EAAE;gBACzD;YACJ;QACJ;QACA,OAAO;IACX;AACJ;AAEA;;;;;CAKC,GACD,MAAM,QAAQ,CAAC;IACX,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK;AAChD;AAEA;;;;;CAKC,GACD,MAAM,MAAM,CAAC;IACT,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,GAAG;AACjD;AAEA;;;;;CAKC,GACD,MAAM,WAAW,CAAC;IACd,OAAO,IAAI,oBAAoB,MAAM;AACzC;AAEA,MAAM,SAAS,CAAA,GAAA,8IAAA,CAAA,UAAW,AAAD;AACzB,MAAM,kBAAkB,CAAC;IACrB,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK;QACrB,MAAM,SAAS,IAAI,GAAG,GAAG,IAAI,KAAK;QAClC,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,UAAU,CAAC,SAAS,EAAE;QAChD,OAAO,IAAI,MAAM,CAAC;IACtB,GAAG,EAAE;AACT;AACA,MAAM,sBAAsB,CAAC,QAAQ;IACjC,6BAA6B;IAC7B,MAAM,UAAU,EAAE;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACvC,OAAO,CAAC,EAAE,GAAG;IACjB;IACA,gCAAgC;IAChC,SAAS,OAAO,CAAC,CAAC,CAAC,OAAO,IAAI;QAC1B,MAAM,QAAQ,QAAQ,KAAK,CAAC,OAAO,MAAM;QACzC,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,KAAK,EAAG;YAC3C,OAAO,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE;QAC/B;IACJ;IACA,OAAO;AACX;AACA,MAAM,iBAAiB,CAAC,MAAM,YAAY;IACtC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;QACrC,MAAM,MAAM,IAAI,CAAC,EAAE;QACnB,MAAM,eAAe,IAAI,YAAY,CAAC,QAAQ,IAAI,KAAK,CAAC;QACxD,IAAI,SAAS,IAAI,KAAK,IAAI,QAAQ,IAAI,GAAG,EAAE;YACvC,OAAO,GAAG,CAAC,WAAW,CAAC,aAAa;QACxC;IACJ;IACA,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,aAAa,CAAC;AACjD;AACA,MAAM,cAAc,CAAC;IACjB,MAAM,SAAS,gBAAgB,KAAK,IAAI;IACxC,MAAM,YAAY,KAAK,IAAI,CAAC,EAAE,EAAE,WAAW;IAC3C,MAAM,QAAQ,cAAc,QAAQ,IAAI;IACxC,MAAM,MAAM,SAAS,QAAQ;IAC7B,MAAM,aAAa;QAAC;YAAE,OAAO;YAAG;YAAK;QAAM;KAAE;IAC7C,MAAM,kBAAkB;QAAE;QAAY;IAAO;IAC7C,MAAM,WAAW,OAAO,kBAAkB,CAAC,KAAK,MAAM,EAAE;IACxD,8BAA8B;IAC9B,IAAI,SAAS,MAAM,KAAK,GACpB,OAAO;IACX,MAAM,UAAU,oBAAoB,KAAK,MAAM,EAAE;IACjD,MAAM,gBAAgB,OAAO,kBAAkB,CAAC,KAAK,MAAM,EAAE;IAC7D,MAAM,cAAc,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/B,MAAM,kBAAkB,QAAQ,KAAK,CAAC,IAAI,KAAK,EAAE,IAAI,GAAG;QACxD,MAAM,gBAAgB,EAAE;QACxB,MAAM,mBAAmB,EAAE;QAC3B,MAAM,cAAc,IAAI;QACxB,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,KAAK,EAAG;YAChD,MAAM,QAAQ,eAAe,CAAC,EAAE;YAChC,MAAM,QAAQ,eAAe,KAAK,IAAI,EAAE,UAAU;YAClD,IAAI,YAAY,GAAG,CAAC,MAAM,EAAE,GACxB;YACJ,cAAc,IAAI,CAAC;YACnB,iBAAiB,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE,aAAa;YAC7D,IAAI,MAAM,UAAU,EAAE;gBAClB,YAAY,GAAG,CAAC,MAAM,EAAE;YAC5B;QACJ;QACA,OAAO;YACH,GAAG,GAAG;YACN,QAAQ;YACR,WAAW;QACf;IACJ;IACA,OAAO;QACH,KAAK,KAAK,GAAG;QACb,MAAM;QACN,QAAQ;IACZ;AACJ;AACA,MAAM,mBAAmB,CAAC,YAAc,UAAU,GAAG,CAAC;AACtD;;;;CAIC,GACD,MAAM,iBAAiB;IACnB;;;KAGC,GACD,OAAO,CAAC,aAAe,WAAW,GAAG,CAAC;AAC1C;AAEA,MAAM,kBAAkB;AACxB;;;;;CAKC,GACD,MAAM,UAAU,CAAC,SAAS,EAAE;IACxB,OAAO,OAAO,MAAM,CAAC,CAAC,KAAK;QACvB,MAAM,aAAa,OAAO,cAAc;YAAC;SAAgB;QACzD,IAAI,IAAI,MAAM,KAAK,GACf,OAAO,WAAW,GAAG,CAAC,IAAM;QAChC,MAAM,OAAO,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QAChC,MAAM,OAAO,WAAW,GAAG,CAAC,IAAM,OAAO;QACzC,OAAO;eAAI;eAAQ;SAAK;IAC5B,GAAG,EAAE;AACT;AAEA,MAAM,sBAAsB,CAAC;IACzB,OAAO,IAAI,UAAU,EAAE,oBAAoB;AAC/C;AACA;;;;;;CAMC,GACD,MAAM,iBAAiB,CAAC,KAAK;IACzB,MAAM,WAAW,MAAM;IACvB,MAAM,mBAAmB,oBAAoB;IAC7C,OAAO,UAAU,GAAG,CAAC,CAAC,UAAU;QAC5B,MAAM,SAAS,MAAM,UAAU,MAAM;QACrC,MAAM,WAAW,SAAS,IAAI;QAC9B,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;YAC/B,UAAU,SAAS,QAAQ,GAAG,WAAW;YACzC,UAAU,SAAS,QAAQ,GAAG;YAC9B,SAAS,SAAS,OAAO,GAAG;YAC5B,SAAS,SAAS,OAAO,GAAG;QAChC;IACJ;AACJ;AACA;;;;CAIC,GACD,MAAM,YAAY,CAAC;IACf;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC,EAAE,GAAG;QACxC,MAAM,EAAE,IAAI,EAAE,GAAG;QACjB,IAAI,CAAC,MACD,OAAO;YAAE,GAAG,GAAG;YAAE,QAAQ,EAAE;YAAE,cAAc,EAAE;YAAE,WAAW,EAAE;QAAC;QACjE,MAAM,YAAY,OAAO,KAAK,CAAC,OAAO;QACtC,IAAI,OAAO,SAAS,UAChB,MAAM,IAAI,MAAM;QACpB,yDAAyD;QACzD,MAAM,WAAW,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,WAAW,WAAW,WAAW;QAC5E,MAAM,YAAY,eAAe,KAAK,SAAS,SAAS;QACxD,MAAM,eAAe,QAAQ,SAAS,MAAM;QAC5C,MAAM,SAAS;YACX,GAAG,GAAG;YACN;YACA;YACA,QAAQ,SAAS,MAAM;QAC3B;QACA,OAAO;IACX;AACJ;AACA;;CAEC,GACD,MAAM,iBAAiB;IACnB;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,OAAO,iBAAiB,IAAI,CAAC,GAAG,CAAC,UAAU,iBAAiB,MAAM;QACxE,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,kBAAkB;YAAE;QAAK;QACvD,OAAO;IACX;AACJ;AAEA;;;;;CAKC,GACD,MAAM,oBAAoB,CAAC;IACvB,IAAI,CAAC,IAAI,SAAS,EACd,OAAO;IACX,MAAM,aAAa,IAAI,UAAU,EAAE,MAAM,CAAC,EAAE,EAAE,cAAc;IAC5D,MAAM,UAAU,CAAC,IAAI,UAAU,EAAE,WAAW,CAAC,IAAI;IACjD,MAAM,YAAY,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,IAAM,OAAO,MAAM,CAAC,CAAC,GAAG,GAAG;YAAE;QAAQ;IAC1E,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;QAAE;IAAU;AAC9C;AACA;;CAEC,GACD,MAAM,iBAAiB;IACnB;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,OAAO,iBAAiB,IAAI,CAAC,GAAG,CAAC;QACvC,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,kBAAkB;YAAE;QAAK;QACvD,OAAO;IACX;AACJ;AAEA;;;;;CAKC,GACD,MAAM,OAAO,CAAC;IACV,OAAO,KAAK,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI,EAAE,GAAG,GAAG,EAAE,GAAG;AACjE;AAEA;;;;;CAKC,GACD,MAAM,UAAU,CAAC;IACb,OAAO,IAAI,KAAK,KAAK,IAAI,GAAG;AAChC;AAEA;;;;;CAKC,GACD,MAAM,aAAa,CAAC,GAAG;IACnB,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;AACrC;AACA;;;CAGC,GACD,MAAM,iBAAiB,CAAC;IACpB,MAAM,SAAS,KAAK,MAAM,CAAC,CAAC,KAAK,KAAK;QAClC,OAAO,IAAI,MAAM,CAAC;YACd;gBAAC;gBAAS,IAAI,KAAK;gBAAE,IAAI,UAAU;gBAAE;aAAE;YACvC;gBAAC;gBAAO,IAAI,GAAG;gBAAE,IAAI,UAAU;gBAAE;aAAE;SACtC;IACL,GAAG,EAAE;IACL,OAAO,OAAO,IAAI,CAAC;AACvB;AACA;;;CAGC,GACD,MAAM,YAAY,CAAC;IACf,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK;QACrB,MAAM,aAAa,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,UAAU,EAAE,IAAI,UAAU;QACnE,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;YAAE;QAAW;IAC/C,GAAG,CAAC;AACR;AACA;;;CAGC,GACD,MAAM,iBAAiB,CAAC;IACpB,MAAM,SAAS,KAAK,MAAM,CAAC,CAAC,KAAK;QAC7B,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,EACf,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,EAAE;QACvB,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,CAAC;QACpB,OAAO;IACX,GAAG,EAAE;IACL,OAAO,OAAO,MAAM,CAAC;AACzB;AACA;;;CAGC,GACD,MAAM,mBAAmB,CAAC;IACtB,OAAO,eAAe,MAAM,GAAG,CAAC;AACpC;AACA;;;CAGC,GACD,MAAM,qBAAqB,CAAC;IACxB,MAAM,MAAM,EAAE;IACd,MAAM,SAAS,eAAe;IAC9B,IAAI,QAAQ,CAAC;IACb,IAAI,QAAQ,CAAC;IACb,MAAM,QAAQ,EAAE;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACvC,MAAM,CAAC,MAAM,QAAQ,WAAW,GAAG,MAAM,CAAC,EAAE;QAC5C,IAAI,UAAU,CAAC,KAAK,QAAQ,QAAQ;YAChC,IAAI,IAAI,CAAC;gBACL;gBACA,KAAK;gBACL,YAAY;gBACZ,cAAc,EAAE;gBAChB,QAAQ,EAAE;gBACV,WAAW,EAAE;YACjB;QACJ;QACA,IAAI,SAAS,SAAS;YAClB,MAAM,IAAI,CAAC;YACX,QAAQ,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;QACrC,OACK;YACD,QAAQ,CAAC;YACT,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;gBACtC,IAAI,KAAK,CAAC,EAAE,KAAK,YAAY;oBACzB,MAAM,MAAM,CAAC,KAAK;gBACtB,OACK;oBACD,QAAQ,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO,KAAK,CAAC,EAAE;gBAC7C;YACJ;QACJ;QACA,QAAQ;IACZ;IACA,OAAO;AACX;AACA;;;;;CAKC,GACD,MAAM,UAAU,CAAC,OAAO,EAAE;IACtB,MAAM,YAAY,iBAAiB,KAAK,MAAM,CAAC,CAAC,MAAQ,QAAQ;IAChE,MAAM,cAAc,mBAAmB,KAAK,MAAM,CAAC,CAAC,MAAQ,CAAC,QAAQ;IACrE,OAAO,KAAK,UAAU,MAAM,CAAC;AACjC;AAEA;;;;CAIC,GACD,MAAM,QAAQ,IAAM,CAAC;QAAE,QAAQ;QAAI,MAAM,EAAE;IAAC,CAAC;AAE7C;;;;CAIC,GACD,MAAM,WAAW,CAAC;IACd,MAAM,OAAO,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC,MAAQ,KAAK,QAAQ;IAC7D,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,kBAAkB;QAAE;IAAK;AACtD;AACA;;;;CAIC,GACD,MAAM,iBAAiB,CAAC;IACpB;;;KAGC,GACD,OAAO,CAAC;QACJ,IAAI,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,mBACN,OAAO;QACX,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,IAAI,EAAE,GAAG;QACnD,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,SAAS;QAC3C,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,iBAAiB;QACnD,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,mBAAmB;QACrD,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,OAAO;QAClC,MAAM,OAAO,SACR,MAAM,CAAC,iBACP,MAAM,CAAC,iBACP,MAAM,CAAC;QACZ,OAAO;YAAE;YAAQ,MAAM,QAAQ;QAAM;IACzC;AACJ;AAEA;;CAEC,GACD,MAAM,kBAAkB;IACpB;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,aAAa,EAAE;QACrB,IAAI,QAAQ;QACZ,IAAI,aAAa,iBAAiB,MAAM,CAAC,OAAO,CAAC,QAAQ;QACzD,MAAO,aAAa,EAAG;YACnB,WAAW,IAAI,CAAC,MAAM,OAAO,YAAY;YACzC,QAAQ;YACR,aAAa,iBAAiB,MAAM,CAAC,OAAO,CAAC,MAAM,cAAc;QACrE;QACA,IAAI,UAAU,GAAG;YACb,WAAW,IAAI,CAAC;QACpB,OACK,IAAI,QAAQ,iBAAiB,MAAM,CAAC,MAAM,EAAE;YAC7C,WAAW,IAAI,CAAC,MAAM,OAAO,SAAS,mBAAmB;QAC7D;QACA,OAAO;IACX;AACJ;AAEA;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC;IACpB,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,CAAC,IAAI,QAAQ,IAAI,CAAC,GAAG;AACrE;AAEA;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC;IACpB,OAAO,eAAe,IAAI,SAAS,IAAI,EAAE;AAC7C;AAEA;;;;;CAKC,GACD,MAAM,eAAe,CAAC;IAClB,MAAM,UAAU,CAAC,KAAK,MAAQ,MAAM,eAAe;IACnD,OAAO,iBAAiB,IAAI,CAAC,MAAM,CAAC,SAAS;AACjD;AAEA,MAAM,oBAAoB;AAC1B;;;;;GAKG,GACH,MAAM,eAAe,CAAC;IAClB,MAAM,aAAa,OAAO,cAAc,EAAE;IAC1C,OAAO,WAAW,QAAQ,CAAC;AAC/B;AAEA;;;;;CAKC,GACD,MAAM,mBAAmB,CAAC;IACtB,MAAM,SAAS,IAAI,MAAM,IAAI,EAAE;IAC/B,MAAM,YAAY,IAAI,SAAS,IAAI,EAAE;IACrC,MAAM,qBAAqB,OAAO,SAAS,CAAC,CAAC,IAAM,CAAC,aAAa;IACjE,OAAO,UAAU,KAAK,CAAC,GAAG;AAC9B;AACA;;;;;CAKC,GACD,MAAM,kBAAkB,CAAC;IACrB,MAAM,YAAY,iBAAiB;IACnC,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,CAAC,IAAI,QAAQ,IAAI,CAAC,GAAG;AACrE;AAEA;;;;;CAKC,GACD,MAAM,gBAAgB,CAAC;IACnB,MAAM,OAAO,iBAAiB,IAAI,IAAI,EAAE;IACxC,OAAO,gBAAgB,IAAI,CAAC,EAAE;AAClC;AAEA;;;;;CAKC,GACD,MAAM,oBAAoB,CAAC;IACvB,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,IAAI,MAAM,IAAI,EAAE;IACvC,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,IAAI,SAAS,IAAI,EAAE;IAC7C,MAAM,qBAAqB,OAAO,SAAS,CAAC,CAAC,IAAM,CAAC,aAAa;IACjE,OAAO,UAAU,KAAK,CAAC,GAAG;AAC9B;AACA;;;;;CAKC,GACD,MAAM,mBAAmB,CAAC;IACtB,MAAM,YAAY,kBAAkB;IACpC,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,CAAC,IAAI,QAAQ,IAAI,CAAC,GAAG;AACrE;AAEA;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC;IACpB,MAAM,OAAO,iBAAiB,IAAI,IAAI,EAAE;IACxC,OAAO,iBAAiB,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE;AACjC;AAEA;;;;;CAKC,GACD,MAAM,aAAa,CAAC;IAChB,OAAO,QAAQ,GAAG,IAAI,GAAG,GAAG,IAAI,KAAK,GAAG,GAAG;AAC/C;AAEA;;;;;CAKC,GACD,MAAM,WAAW,CAAC;IACd,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,WAAU,AAAD,EAAE,iBAAiB,MAAM;IACjD,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,YAAY,iBAAiB,IAAI;IACzD,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,kBAAkB;QAAE;QAAQ;IAAK;AAC9D;AAEA,MAAM,oBAAoB;IAAE,QAAQ;IAAK,OAAO;AAAE;AAClD;;;;;CAKC,GACD,MAAM,gBAAgB,CAAC;IACnB,OAAO,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,KAAK,MAAM,MAAM,OAAO,SAAS,QAAQ;AACzD;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,cAAc,QAAQ,CAAC,KAAK,YAAY,IAAI,CAAC;AACxD;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,eAAe,QAAQ,CAAC,KAAK,aAAa,IAAI,CAAC;AAC1D;AACA;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC;IACpB,MAAM,eAAe,gBAAgB;IACrC,MAAM,gBAAgB,iBAAiB;IACvC,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,GAAG;IACvB,MAAM,QAAQ,KAAK,GAAG,CAAC,KAAK,GAAG,eAAe;IAC9C,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE;QAAE;QAAG;IAAM;IACnD,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAAE;QAAK;QAAc;IAAc;AACtE;AACA;;;;;;CAMC,GACD,MAAM,gBAAgB,CAAC,SAAS,SAAS;IACrC;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,YAAY,aAAa;QAC/B,MAAM,cAAc,iBAAiB,CAAC,MAAM,IAAI;QAChD,MAAM,iBAAiB,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,GAAG;QACpD,MAAM,gBAAgB,UAAU,aAAa,YAAY,KAAK,GAAG,CAAC,KAAK;QACvE,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC,GAAG,iBAAiB;QACxC,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE;YAAE;QAAE;QAC5C,MAAM,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;YAAE;QAAI;QAC9C,OAAO,gBAAgB,QAAQ,aAAa,CAAC,SAAS,WAAW;IACrE;AACJ;AACA,MAAM,eAAe,CAAC;IAClB,IAAI,aAAa;IACjB,IAAI,cAAc;IAClB,IAAI,aAAa;IACjB,IAAI,eAAe;IACnB,MAAM,OAAO,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,MAAM,SAAS,SAAS;QACxB,MAAM,SAAS,SAAS;QACxB,MAAM,YAAY,QAAQ;QAC1B,MAAM,WAAW,eAAe;QAChC,aAAa,KAAK,GAAG,CAAC,YAAY;QAClC,aAAa,KAAK,GAAG,CAAC,YAAY;QAClC,cAAc,KAAK,GAAG,CAAC,aAAa;QACpC,gBAAgB;QAChB,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;YAAE;YAAQ;YAAQ,SAAS;YAAW;QAAS;IACjF;IACA,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAC3B;QACA,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,UAAU;IACd;AACJ;AACA;;;;;;CAMC,GACD,MAAM,gBAAgB,CAAC,SAAS;IAC5B;;;;;KAKC,GACD,OAAO,CAAC,MAAM,OAAO;QACjB,MAAM,iBAAiB,UAAU,MAAM,MAAM,GAAG;QAChD,MAAM,QAAQ,KAAK,IAAI,EAAE,CAAC,EAAE,EAAE,cAAc,CAAC;QAC7C,MAAM,QAAQ,iBAAiB,MAAM,aAAa,GAAG,MAAM,KAAK;QAChE,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,cAAc,QAAQ,cAAc,IAAI,cAAc,SAAS,SAAS,QAAQ,gBAAgB,eAAe;IAClI;AACJ;AACA;;;;;;CAMC,GACD,MAAM,oBAAoB,CAAC,SAAS;IAChC;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,iBAAiB,cAAc,SAAS;QAC9C,OAAO,WAAW,GAAG,CAAC,CAAC,YAAc,UAAU,GAAG,CAAC;IACvD;AACJ;AAEA,MAAM,kBAAkB,QAAQ,QAAQ;AACxC,MAAM,iBAAiB,CAAC,QAAU,MAAM,UAAU,CAAC,QAAQ,CAAC;AAC5D;;;;;CAKC,GACD,MAAM,wBAAwB,CAAC;IAC3B,IAAI,CAAC,IAAI,SAAS,EACd,OAAO;IACX,MAAM,SAAS,IAAI,MAAM,IAAI,EAAE;IAC/B,MAAM,aAAa,IAAI,UAAU,EAAE;IACnC,IAAI,CAAC,YACD,OAAO;IACX,MAAM,YAAY,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU;QAC3C,MAAM,QAAQ,MAAM,CAAC,EAAE;QACvB,IAAI,WAAW,KAAK,IAAI,eAAe,QAAQ;YAC3C,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU;gBAAE,UAAU,WAAW,KAAK;YAAC;QACpE;QACA,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG;IAC7B;IACA,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;QAAE;IAAU;AAC9C;AACA;;CAEC,GACD,MAAM,qBAAqB;IACvB;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,OAAO,iBAAiB,IAAI,CAAC,GAAG,CAAC;QACvC,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,kBAAkB;YAAE;QAAK;QACvD,OAAO;IACX;AACJ;AAEA;;;CAGC,GACD,MAAM,kBAAkB,CAAC;IACrB,OAAO;QACH,OAAO,EAAE,KAAK,IAAI,CAAC,EAAE,SAAS,KAAK,QAAQ,UAAU,MAAM;QAC3D,eAAe,EAAE,aAAa,IAAI,CAAC,EAAE,KAAK,KAAK,YAAY,SAAS,EAAE,KAAK,IAAI,MAAM;QACrF,YAAY,EAAE,UAAU,IAAI;QAC5B,iBAAiB,EAAE,eAAe,IAAI;QACtC,QAAQ,EAAE,MAAM,IAAI;QACpB,kBAAkB,EAAE,gBAAgB,IAAI;QACxC,OAAO,EAAE,KAAK,IAAI;QAClB,WAAW,EAAE,SAAS,IAAI;QAC1B,UAAU,EAAE,QAAQ,IAAI,EAAE;QAC1B,MAAM,EAAE,IAAI,KAAK;QACjB,MAAM,EAAE,IAAI,IAAI,EAAE;QAClB,UAAU,EAAE,QAAQ,IAAI;QACxB,oBAAoB,EAAE,kBAAkB,IAAI;QAC5C,mBAAmB,EAAE,iBAAiB,IAAI;QAC1C,QAAQ,EAAE,MAAM,IAAI;QACpB,qBAAqB,EAAE,mBAAmB,IAAI;QAC9C,YAAY,EAAE,UAAU,IAAI;QAC5B,aAAa,EAAE,WAAW,IAAI;QAC9B,MAAM,EAAE,IAAI,IAAI;QAChB,YAAY,EAAE,UAAU,IAAI,EAAE,MAAM,IAAI;QACxC,aAAa,EAAE,WAAW,IAAI,EAAE,MAAM,IAAI;QAC1C,SAAS,EAAE,OAAO;QAClB,YAAY,EAAE,UAAU,IAAI,EAAE,OAAO,IAAI;QACzC,kBAAkB,EAAE,gBAAgB,IAAI;QACxC,QAAQ,EAAE,MAAM,IAAI;QACpB,cAAc,EAAE,YAAY,IAAI;QAChC,QAAQ,EAAE,MAAM,IAAI;QACpB,aAAa,EAAE,WAAW,IAAI,EAAE,KAAK,IAAI;QACzC,aAAa,EAAE,WAAW,IAAI;QAC9B,QAAQ,EAAE,MAAM,IAAI;QACpB,WAAW,EAAE,SAAS,IAAI;QAC1B,gBAAgB,EAAE,cAAc,IAAI,EAAE,KAAK,IAAI;QAC/C,gBAAgB,EAAE,cAAc,IAAI;QACpC,eAAe,EAAE,aAAa,IAAI;QAClC,aAAa,EAAE,WAAW,IAAI;QAC9B,SAAS,EAAE,OAAO,IAAI;IAC1B;AACJ;AACA;;;;;CAKC,GACD,MAAM,iBAAiB,CAAC;IACpB,MAAM,aAAa,gBAAgB,IAAI,UAAU;IACjD,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;QAAE;IAAW;AAC/C;AACA;;CAEC,GACD,MAAM,qBAAqB;IACvB,OAAO,CAAC;QACJ,MAAM,SAAS,iBAAiB,MAAM,IAAI;QAC1C,MAAM,OAAO,CAAC,iBAAiB,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC;QAC/C,OAAO;YAAE;YAAQ;QAAK;IAC1B;AACJ;AAEA;;CAEC,GACD,MAAM,oBAAoB;IACtB;;;KAGC,GACD,OAAO,CAAC;QACJ,iBAAiB,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3B,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,MAAM,EAAE,aAAa,EAAE,GAAG;YAC1B,IAAI,kBAAkB,OAAO;gBACzB,WAAW,OAAO,GAAG,CAAC;YAC1B,OACK,IAAI,kBAAkB,SAAS;gBAChC,WAAW,OAAO,GAAG;YACzB;QACJ;QACA,OAAO;IACX;AACJ;AAEA,MAAM,SAAS,CAAA,GAAA,8IAAA,CAAA,UAAW,AAAD;AACzB;;;CAGC,GACD,MAAM,gBAAgB,CAAC;IACnB,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK;QACrB,MAAM,SAAS,IAAI,GAAG,GAAG,IAAI,KAAK;QAClC,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,UAAU,CAAC,SAAS,EAAE;QAChD,OAAO,IAAI,MAAM,CAAC;IACtB,GAAG,EAAE;AACT;AACA;;CAEC,GACD,MAAM,eAAe;IACjB;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,SAAS,cAAc,iBAAiB,IAAI;QAClD,IAAI,gBAAgB;QACpB,iBAAiB,MAAM,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,CAAC,MAAM;YAC7C,MAAM,QAAQ,MAAM,CAAC,MAAM,GAAG,MAAM;YACpC,MAAM,eAAe,QACf,OAAO,oBAAoB,CAAC,iBAAiB,MAAM,CAAC,MAAM,CAAC,UAC3D;YACN,iBAAiB,gBAAgB;QACrC;QACA,MAAM,SAAS;YACX,GAAG,gBAAgB;YACnB,QAAQ;QACZ;QACA,OAAO;IACX;AACJ;AAEA;;;;;;CAMC,GACD,MAAM,eAAe,CAAC;IAClB,OAAO,CAAC,kBAAkB,WAAW,UAAU,CAAC,CAAC;QAC7C,MAAM,mBAAmB,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,sBAAsB,qBAAqB,UAAU,SAAS,UAAU,kBAAkB,gBAAgB,eAAe;QAC5K,MAAM,oBAAoB,CAAC,aAAe,WAAW,GAAG,CAAC;QACzD,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,SAAS,UAAU,kBAAkB,WAAW,SAAS,SAAS,YAAY,mBAAmB,mBAAmB,sBAAsB;IAC/K;AACJ;AAEA,MAAM,OAAO,CAAA,GAAA,8IAAA,CAAA,UAAW,AAAD;AACvB,MAAM,aAAa;IACf;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,MAAM,YAAY,iBAAiB,IAAI,CAAC,EAAE,EAAE,WAAW;QACvD,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,kBAAkB,CAAC,QAAQ;QACnD,IAAI,YAAY;QAChB,IAAI,YAAY;QAChB,IAAI,QAAQ;QACZ,MAAM,OAAO,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;YACvC,MAAM,QAAQ,MAAM,CAAC,EAAE;YACvB,IAAI,UAAU,WAAW;gBACrB,IAAI,cAAc,MAAM;oBACpB,KAAK,IAAI,CAAC;wBACN,OAAO;wBACP,KAAK;wBACL,YAAY;4BAAE,WAAW;wBAAU;oBACvC;gBACJ;gBACA,YAAY;gBACZ,YAAY;YAChB;YACA,SAAS;QACb;QACA,IAAI,YAAY,OAAO,MAAM,EAAE;YAC3B,KAAK,IAAI,CAAC;gBACN,OAAO;gBACP,KAAK,OAAO,MAAM;gBAClB,YAAY;oBAAE,WAAW;gBAAU;YACvC;QACJ;QACA,MAAM,SAAS;YAAE;YAAQ;QAAK;QAC9B,OAAO;IACX;AACJ;AAEA,MAAM,WAAW;AACjB,MAAM,oBAAoB,CAAC,UAAU,QAAQ;IACzC,IAAI,WAAW;IACf,IAAI,iBAAiB;IACrB,MAAM,MAAM;QAAE,OAAO;QAAG,SAAS;QAAG,QAAQ;IAAE;IAC9C,MAAM,aAAa,MAAM,CAAC,KAAK,GAAG,CAAC,YAAY,OAAO,MAAM,GAAG,GAAG;IAClE,MAAM,iBAAiB,CAAC;QACpB,MAAM,UAAU,aAAa,OAAO,KAAK,OAAO,GAAG;QACnD,IAAI,IAAI,KAAK,GAAG,YAAY;YACxB,IAAI,CAAC,SACD,OAAO;YACX,OAAO,IAAI,OAAO,GAAG,UAAU,IACzB,CAAC,aAAa,IAAI,KAAK,IAAI,IAAI,OAAO,GACtC;QACV;QACA,MAAM,SAAS,YAAY,OAAO,KAAK,MAAM,GAAG;QAChD,IAAI,IAAI,KAAK,GAAG,YAAY;YACxB,IAAI,CAAC,QACD,OAAO;YACX,OAAO,IAAI,MAAM,GAAG,SAAS,IACvB,CAAC,aAAa,IAAI,KAAK,IAAI,IAAI,MAAM,GACrC;QACV;QACA,OAAO;IACX;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EAAG;QACzC,MAAM,OAAO,QAAQ,CAAC,EAAE;QACxB,IAAI,KAAK,IAAI,KAAK,OAAO;YACrB,IAAI,KAAK,IAAI,KAAK,KAAK;QAC3B;QACA,IAAI,KAAK,IAAI,KAAK,QAAQ;YACtB,IAAI,KAAK,IAAI,KAAK,KAAK;YACvB,IAAI,OAAO,IAAI,KAAK,OAAO;YAC3B,IAAI,MAAM,IAAI,KAAK,MAAM;QAC7B;QACA,IAAI,IAAI,KAAK,GAAG,IAAI,MAAM,GAAG,YAAY;YACrC,IAAI,aAAa,MAAM;gBACnB,IAAI,IAAI,MAAM,IAAI,IAAI,IAAI;gBAC1B,MAAO,IAAI,SAAS,MAAM,IACtB,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,KAAK,UAAU,QAAQ,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,EAAG;oBACjE;gBACJ;gBACA,WAAW,IAAI;YACnB;YACA;QACJ;QACA,IAAI,KAAK,IAAI,KAAK,aAAa,KAAK,IAAI,KAAK,QAAQ;YACjD,MAAM,QAAQ,eAAe;YAC7B,MAAM,UAAU,KAAK,IAAI,KAAK,YAAY,KAAK,OAAO,GAAG;YACzD,MAAM,UAAU,MAAM,KAAK,GAAG,CAAC,UAAU,IAAI;YAC7C,IAAI,kBAAkB,SAAS;gBAC3B,WAAW;gBACX,iBAAiB;YACrB;QACJ;IACJ;IACA,OAAO,IAAI,KAAK,GAAG,IAAI,MAAM,GAAG,aAAa,WAAW;AAC5D;AACA,MAAM,eAAe,CAAC,OAAO;IACzB,IAAI,QAAQ;IACZ,IAAI,aAAa;IACjB,IAAI,WAAW;IACf,MAAM,cAAc;QAAC;KAAE;IACvB,MAAO,SAAS,MAAM,GAAG,EAAG;QACxB,MAAM,aAAa,kBAAkB,UAAU,QAAQ;QACvD,IAAI,eAAe,MAAM;YACrB,SAAS;YACT,YAAY,IAAI,CAAC;YACjB,WAAW,SAAS,KAAK,CAAC,aAAa,GAAG,SAAS,MAAM;YACzD;YACA;QACJ,OACK;YACD,WAAW,EAAE;QACjB;IACJ;IACA,OAAO;AACX;AAEA,uCAAuC,GACvC,MAAM;IACF,KAAK;IACL,KAAK;IACL,KAAK;IACL,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACA,MAAM;IACF,OAAO,OAAO,eAAe;IAC7B,KAAK;IACL,KAAK;IACL,SAAS;IACT,WAAW;IACX,aAAc;QACV,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,UAAU,GAAG;IACtB;IACA,SAAS,IAAI,EAAE;QACX,OAAO,CAAC,CAAC,AAAC,QACN,KAAK,IAAI,KAAK,QACd,KAAK,IAAI,KAAK,QACd,IAAI,CAAC,IAAI,KAAK,QACd,IAAI,CAAC,IAAI,KAAK,QACd,IAAI,CAAC,OAAO,EAAE;IACtB;IACA,OAAO;QACH,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,UAAU;QACN,OAAO,IAAI,CAAC,QAAQ,KAAK;IAC7B;IACA,QAAQ;QACJ,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,OAAO;QACH,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,QAAQ,QAAQ,EAAE;QACd,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,MAAO,SAAS,KAAM;YAClB,SAAS;YACT,OAAO,KAAK,IAAI;QACpB;IACJ;IACA,GAAG,CAAC,EAAE;QACF,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,QAAQ;QACZ,IAAI,KAAK,IAAI,CAAC,UAAU,IAAI,IAAI,GAAG;YAC/B,OAAO;QACX;QACA,MAAO,SAAS,KAAM;YAClB,IAAI,MAAM,OAAO;gBACb,OAAO;YACX;YACA,OAAO,KAAK,IAAI;YAChB,SAAS;QACb;QACA,OAAO;IACX;IACA,YAAY,IAAI,EAAE,OAAO,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OACf,OAAO,IAAI;QACf,QAAQ,IAAI,GAAG;QACf,QAAQ,IAAI,GAAG,KAAK,IAAI;QACxB,IAAI,KAAK,IAAI,KAAK,MAAM;YACpB,IAAI,CAAC,IAAI,GAAG;QAChB,OACK;YACD,KAAK,IAAI,CAAC,IAAI,GAAG;QACrB;QACA,KAAK,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,IAAI;QACjB,OAAO,IAAI;IACf;IACA,aAAa,IAAI,EAAE,OAAO,EAAE;QACxB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OACf,OAAO,IAAI;QACf,QAAQ,IAAI,GAAG,KAAK,IAAI;QACxB,QAAQ,IAAI,GAAG;QACf,IAAI,KAAK,IAAI,KAAK,MAAM;YACpB,IAAI,CAAC,IAAI,GAAG;QAChB,OACK;YACD,KAAK,IAAI,CAAC,IAAI,GAAG;QACrB;QACA,KAAK,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,IAAI;QACjB,OAAO,IAAI;IACf;IACA,KAAK,IAAI,EAAE;QACP,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;YACpB,IAAI,CAAC,OAAO,CAAC;QACjB,OACK;YACD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;QAChC;QACA,OAAO,IAAI;IACf;IACA,QAAQ,IAAI,EAAE;QACV,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;YACpB,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,IAAI,GAAG;YACZ,KAAK,IAAI,GAAG;YACZ,KAAK,IAAI,GAAG;YACZ,IAAI,CAAC,QAAQ,IAAI;QACrB,OACK;YACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE;QACjC;QACA,OAAO,IAAI;IACf;IACA,OAAO,IAAI,EAAE;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OACf,OAAO,IAAI;QACf,IAAI,KAAK,IAAI,KAAK,MAAM;YACpB,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QACzB,OACK;YACD,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QAC9B;QACA,IAAI,KAAK,IAAI,KAAK,MAAM;YACpB,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QACzB,OACK;YACD,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QAC9B;QACA,IAAI,CAAC,QAAQ,IAAI;QACjB,OAAO,IAAI;IACf;AACJ;AAEA;;;;CAIC,GACD,SAAS,WAAW,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ;IACxE,OAAO;QACH;QACA;QACA;QACA;QACA,QAAQ,UAAU;YACd,OAAO;YACP,SAAS;YACT,QAAQ;QACZ;QACA;IACJ;AACJ;AACA,SAAS,YAAY,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,WAAW;IAClE,IAAI,QAAQ,IAAI,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;IAC3C,IAAI,UAAU;IACd,IAAI,SAAS;IACb,qFAAqF;IACrF,oCAAoC;IACpC,MAAM,aAAa,cAAc,YAAY,MAAM,GAC7C,WAAW,CAAC,cAAc,EAAE,GAC5B,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;IACzC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW;QAC/B,SAAS,KAAK,CAAC,IAAI,CAAC,KAAK;IAC7B;IACA,8BAA8B;IAC9B,IAAI,QAAQ,YAAY;QACpB,UAAU,IAAI,OAAO,GAAG,OAAO,MAAM,CAAC,OAAO;QAC7C,IAAI,UAAU,GAAG;YACb,OAAO,CAAC,aAAa,KAAK,IAAI;QAClC;QACA,OAAO,UAAU,QAAQ;IAC7B;IACA,6BAA6B;IAC7B,IAAI,QAAQ,YAAY;QACpB,SAAS,IAAI,MAAM,GAAG,OAAO,MAAM,CAAC,MAAM;QAC1C,IAAI,SAAS,GAAG;YACZ,OAAO,CAAC,aAAa,KAAK,IAAI;QAClC;QACA,OAAO,UAAU,QAAQ;IAC7B;IACA,gBAAgB;IAChB,OAAO;AACX;AACA,wDAAwD;AACxD,oDAAoD;AACpD,SAAS,WAAW,KAAK,EAAE,GAAG,EAAE,eAAe;IAC3C,MAAM,SAAS;QACX,OAAO,IAAI,KAAK;QAChB,SAAS,IAAI,OAAO;QACpB,QAAQ,IAAI,MAAM;IACtB;IACA,IAAK,IAAI,IAAI,iBAAiB,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;QACpD,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,IAAI,KAAK,IAAI,KAAK,QAAQ;YACtB,OAAO,KAAK,IAAI,KAAK,KAAK;YAC1B,OAAO,OAAO,IAAI,KAAK,OAAO;YAC9B,OAAO,MAAM,IAAI,KAAK,MAAM;QAChC,OACK,IAAI,KAAK,IAAI,KAAK,SAClB,KAAK,IAAI,KAAK,aACX,KAAK,OAAO,KAAK,CAAC,UAAU,QAAQ,IACpC,IAAI,iBAAkB;YAC1B;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,oBAAoB,WAAW;IACpC,MAAM,cAAc,EAAE;IACtB,IAAI,YAAY,IAAI,OAAO,GACvB,OAAO,EAAE;IACb,IAAI,MAAM;QAAE,MAAM;YAAE,UAAU;QAAS;IAAE;IACzC,qEAAqE;IACrE,YAAY,OAAO,CAAC,CAAC;QACjB,IAAI,KAAK,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;YACxC,MAAM;QACV;IACJ;IACA,MAAO,QAAQ,KAAM;QACjB,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ;QAClC,MAAM,IAAI,IAAI,CAAC,QAAQ;IAC3B;IACA,OAAO,YAAY,OAAO;AAC9B;AACA;;;;;CAKC,GACD,MAAM,YAAY,CAAC,OAAO,iBAAiB;IACvC,yDAAyD;IACzD,wFAAwF;IACxF,gEAAgE;IAChE,sGAAsG;IACtG,mFAAmF;IACnF,2EAA2E;IAC3E,MAAM,UAAU;QACZ,UAAU;YAAE,MAAM;YAAI,SAAS;YAAK,SAAS;QAAK;QAClD,WAAW,aAAa;IAC5B;IACA,MAAM,cAAc,IAAI;IACxB,MAAM,MAAM;QAAE,OAAO;QAAG,SAAS;QAAG,QAAQ;IAAE;IAC9C,MAAM,cAAc;IACpB,qDAAqD;IACrD,YAAY,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,WAAW,GAAG,GAAG,GAAG,GAAG,WAAW;IACvE,iCAAiC;IACjC,SAAS,SAAS,IAAI,EAAE,KAAK,EAAE,KAAK;QAChC,IAAI,SAAS,YAAY,KAAK;QAC9B,wFAAwF;QACxF,wFAAwF;QACxF,sFAAsF;QACtF,yBAAyB;QACzB,MAAO,WAAW,KAAM;YACpB,IAAI,cAAc;YAClB,mCAAmC;YACnC,MAAM,aAAa;gBACf;oBAAE,QAAQ;oBAAW,UAAU;gBAAS;gBACxC;oBAAE,QAAQ;oBAAW,UAAU;gBAAS;gBACxC;oBAAE,QAAQ;oBAAW,UAAU;gBAAS;gBACxC;oBAAE,QAAQ;oBAAW,UAAU;gBAAS;aAC3C;YACD,0HAA0H;YAC1H,MAAO,WAAW,KAAM;gBACpB,cAAc,OAAO,IAAI,CAAC,IAAI,GAAG;gBACjC,MAAM,QAAQ,YAAY,OAAO,aAAa,KAAK,OAAO,OAAO,IAAI,EAAE;gBACvE,2EAA2E;gBAC3E,oFAAoF;gBACpF,mFAAmF;gBACnF,sFAAsF;gBACtF,wFAAwF;gBACxF,IAAI,QAAQ,CAAC,KACR,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK,CAAC,UAAU,QAAQ,EAAG;oBACnE,YAAY,MAAM,CAAC;gBACvB;gBACA,mFAAmF;gBACnF,qDAAqD;gBACrD,IAAI,SAAS,CAAC,KAAK,SAAS,QAAQ,SAAS,EAAE;oBAC3C,MAAM,UAAU,MAAM,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,QAAQ;oBAChD,IAAI,WAAW;oBACf,mBAAmB;oBACnB,IAAI,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,IAAI,GAAG;wBAC9C,WACI,KAAK,GAAG,CAAC,QAAQ,QAAQ,CAAC,IAAI,GAAG,SAAS,KACtC,KAAK,GAAG,CAAC,KAAK,OAAO,EAAE;oBAC/B,0CAA0C;oBAC9C,OACK,IAAI,KAAK,IAAI,KAAK,aACnB,KAAK,OAAO,KAAK,CAAC,UAAU,QAAQ,EAAE;wBACtC,WACI,KAAK,GAAG,CAAC,QAAQ,QAAQ,CAAC,IAAI,GAAG,SAAS,KACtC,KAAK,GAAG,CAAC,KAAK,OAAO,EAAE;oBAC/B,kBAAkB;oBACtB,OACK;wBACD,WAAW,KAAK,GAAG,CAAC,QAAQ,QAAQ,CAAC,IAAI,GAAG,SAAS;oBACzD;oBACA,IAAI,KAAK,IAAI,KAAK,aACd,KAAK,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,WAAW;wBAChD,YACI,QAAQ,QAAQ,CAAC,OAAO,GACpB,KAAK,OAAO,GACZ,wCAAwC;wBACxC,KAAK,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO;oBAC/C;oBACA,8DAA8D;oBAC9D,IAAI;oBACJ,IAAI,QAAQ,CAAC,KAAK;wBACd,eAAe;oBACnB,OACK,IAAI,SAAS,KAAK;wBACnB,eAAe;oBACnB,OACK,IAAI,SAAS,GAAG;wBACjB,eAAe;oBACnB,OACK;wBACD,eAAe;oBACnB;oBACA,sGAAsG;oBACtG,IAAI,KAAK,GAAG,CAAC,eAAe,OAAO,IAAI,CAAC,YAAY,IAAI,GAAG;wBACvD,YAAY,QAAQ,QAAQ,CAAC,OAAO;oBACxC;oBACA,8FAA8F;oBAC9F,YAAY,OAAO,IAAI,CAAC,QAAQ;oBAChC,uDAAuD;oBACvD,IAAI,WAAW,UAAU,CAAC,aAAa,CAAC,QAAQ,EAAE;wBAC9C,UAAU,CAAC,aAAa,GAAG;4BAAE;4BAAQ;wBAAS;oBAClD;gBACJ;gBACA,SAAS,OAAO,IAAI;gBACpB,8FAA8F;gBAC9F,0DAA0D;gBAC1D,qGAAqG;gBACrG,0GAA0G;gBAC1G,yGAAyG;gBACzG,qCAAqC;gBACrC,IAAI,WAAW,QAAQ,OAAO,IAAI,CAAC,IAAI,IAAI,aAAa;oBACpD;gBACJ;YACJ;YACA,MAAM,SAAS,WAAW,OAAO,KAAK;YACtC,IAAK,IAAI,eAAe,GAAG,eAAe,WAAW,MAAM,EAAE,gBAAgB,EAAG;gBAC5E,MAAM,YAAY,UAAU,CAAC,aAAa;gBAC1C,IAAI,UAAU,QAAQ,KAAK,UACvB;gBACJ,MAAM,UAAU,IAAI,WAAW,IAAI,CAAC,WAAW,OAAO,UAAU,QAAQ,EAAE,UAAU,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,cAAc,QAAQ,UAAU,MAAM;gBAChJ,IAAI,WAAW,MAAM;oBACjB,YAAY,YAAY,CAAC,QAAQ;gBACrC,OACK;oBACD,YAAY,IAAI,CAAC;gBACrB;YACJ;QACJ;IACJ;IACA,MAAM,OAAO,CAAC,CAAC,MAAM,OAAO;QACxB,IAAI,KAAK,IAAI,KAAK,OAAO;YACrB,IAAI,KAAK,IAAI,KAAK,KAAK;YACvB;QACJ;QACA,IAAI,KAAK,IAAI,KAAK,QAAQ;YACtB,MAAM,cAAc,QAAQ,KAAK,KAAK,CAAC,QAAQ,EAAE,CAAC,IAAI,KAAK;YAC3D,IAAI,aACA,SAAS,MAAM,OAAO;YAC1B,IAAI,KAAK,IAAI,KAAK,KAAK;YACvB,IAAI,OAAO,IAAI,KAAK,OAAO;YAC3B,IAAI,MAAM,IAAI,KAAK,MAAM;YACzB;QACJ;QACA,IAAI,KAAK,IAAI,KAAK,aAAa,KAAK,OAAO,KAAK,UAAU,QAAQ,EAAE;YAChE,SAAS,MAAM,OAAO;QAC1B;IACJ;IACA,OAAO,oBAAoB;AAC/B;AACA,UAAU,QAAQ,GAAG;AACrB,UAAU,IAAI,GAAG,CAAC,OAAO,OAAO,KAAK,SAAS,SAAW,CAAC;QACtD,MAAM;QACN;QACA;QACA;QACA;QACA;IACJ,CAAC;AACD,UAAU,GAAG,GAAG,CAAC,OAAO,OAAO,KAAK,aAAa,KAAK,GAAK,CAAC;QACxD,MAAM;QACN;QACA;QACA;QACA;IACJ,CAAC;AACD,UAAU,OAAO,GAAG,CAAC,OAAO,SAAS,UAAY,CAAC;QAC9C,MAAM;QACN;QACA;QACA;IACJ,CAAC;AAED;;;;;;CAMC,GACD,MAAM,MAAM,CAAC,OAAO;IAChB,MAAM,QAAQ,IAAI,KAAK,GAAG;IAC1B,MAAM,MAAM,IAAI,GAAG,GAAG;IACtB,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK;QAAE;QAAO;IAAI;AAC/C;AAEA;;;;;CAKC,GACD,MAAM,SAAS,CAAC;IACZ,OAAO,IAAI,GAAG,GAAG,IAAI,KAAK;AAC9B;AAEA;;;;;;CAMC,GACD,MAAM,SAAS,CAAC,MAAM;IAClB,MAAM,MAAM,KAAK,GAAG,GAAG,OAAO;IAC9B,MAAM,SAAS,CAAC,KAAK,MAAM,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK,MAAM,IAAI,EAAE;IAC3D,MAAM,YAAY,CAAC,KAAK,SAAS,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK,SAAS,IAAI,EAAE;IACpE,MAAM,aAAa,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK,UAAU,EAAE,KAAK,UAAU;IACrE,MAAM,cAAc,KAAK,YAAY,IAAI,EAAE;IAC3C,MAAM,gBAAgB,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB;IAC3C,MAAM,cAAc,CAAC,KAAK,YAAY,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,IAAM,IAAI,gBAAgB;IAC7E,MAAM,eAAe,UAAU,YAAY,MAAM,CAAC;IAClD,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,MAAM;QAC3B;QACA;QACA;QACA;QACA;IACJ;AACJ;AAEA;;;;;;;CAOC,GACD,MAAM,gBAAgB,CAAC,OAAO,OAAO;IACjC,IAAI,CAAC,OACD,OAAO;IACX,kEAAkE;IAClE,MAAM,aAAa,QAAQ,GAAG,OAAO;IACrC,MAAM,cAAc,QAAQ,OAAO,UAAU;IAC7C,OAAO,OAAO,SAAS,OAAO,aAAa;AAC/C;AACA;;;;;;;CAOC,GACD,MAAM,SAAS,CAAC,OAAO,OAAO;IAC1B,MAAM,OAAO,QAAQ;IACrB,MAAM,QAAQ,SAAS,SAAS,cAAc,OAAO,QAAQ;IAC7D,OAAO,cAAc,OAAO,OAAO;AACvC;AAEA;;;;;;CAMC,GACD,MAAM,aAAa,CAAC,OAAO;IACvB,OAAO,aAAa,OAAO,iBAAiB,IAAI;AACpD;AAEA;;;;;;;CAOC,GACD,MAAM,cAAc,CAAC,OAAO,OAAO;IAC/B,MAAM,WAAW,WAAW,OAAO;IACnC,4CAA4C;IAC5C,IAAI,aAAa,CAAC,GACd,OAAO,OAAO,OAAO;IACzB,MAAM,aAAa;QAAC;KAAM;IAC1B,MAAM,SAAS,iBAAiB,MAAM,CAAC,KAAK,CAAC,GAAG,SAC5C,qBAAqB,cACrB,iBAAiB,MAAM,CAAC,KAAK,CAAC;IAClC,MAAM,OAAO,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK;QACzC,IAAI,MAAM,UACN,OAAO,OAAO,QAAQ,IAAI,KAAK,EAAE,OAAO;QAC5C,IAAI,IAAI,UACJ,OAAO,IAAI,WAAW,MAAM,EAAE;QAClC,OAAO;IACX;IACA,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,kBAAkB;QAAE;QAAQ;IAAK;AAC9D;AAEA;;;;;;;CAOC,GACD,MAAM,wBAAwB,CAAC,OAAO,KAAK;IACvC,MAAM,WAAW,IAAI,KAAK,IAAI;IAC9B,MAAM,kBAAkB,KAAK,GAAG,CAAC,GAAG,aAAa,QAAQ,UAAU;IACnE,MAAM,gBAAgB,KAAK,GAAG,CAAC,GAAG,aAAa,MAAM,UAAU;IAC/D,MAAM,YAAY,CAAC,IAAI,SAAS,IAAI,EAAE,EAAE,KAAK,CAAC,iBAAiB;IAC/D,OAAO,eAAe;AAC1B;AAEA;;;;;;;;;CASC,GACD,MAAM,sBAAsB,CAAC,OAAO,KAAK;IACrC,MAAM,OAAO,OAAO,OAAO,KAAK,iBAAiB,IAAI;IACrD,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,sBAAsB,OAAO,KAAK,MAAM;AACnF;AAEA,MAAM,SAAS;AACf,MAAM,kBAAkB;AACxB,MAAM,kBAAkB;AACxB,MAAM,OAAO;IACT,OAAO;IACP,SAAS;IACT,QAAQ;AACZ;AACA;;;;;;;CAOC,GACD,MAAM,aAAa,CAAC,kBAAkB,OAAO;IACzC,IAAI,QAAQ;IACZ,IAAI,MAAM;IACV,MAAM,QAAQ,OAAO,MAAM,CAAC,CAAC,KAAK;QAC9B,MAAM,OAAO,KAAK,CAAC,WAAW;QAC9B,MAAM,WAAW,KAAK,CAAC,aAAa,EAAE;QACtC,0DAA0D;QAC1D,IAAI,eAAe,MAAM,MAAM,GAAG,GAC9B,OAAO;QACX,IAAI;QACJ,IAAI,KAAK,IAAI,KAAK,WAAW;YACzB,qEAAqE;YACrE,MAAM,SAAS,GAAG;YAClB,OAAO,MAAM,OAAO,KAAK;YACzB,OAAO,YAAY,KAAK,MAAM,CAAC,MAAM,EAAE,QAAQ;QACnD,OACK;YACD,MAAM,KAAK,GAAG;YACd,OAAO,MAAM,OAAO,KAAK;QAC7B;QACA,QAAQ;QACR,OAAO;eAAI;YAAK;SAAK;IACzB,GAAG,EAAE;IACL,YAAY;IACZ,MAAM,IAAI,CAAC,MAAM,OAAO,iBAAiB,MAAM,CAAC,MAAM,EAAE;IACxD,OAAO;AACX;AACA;;;;;;;CAOC,GACD,MAAM,WAAW,CAAC,kBAAkB,EAAE,KAAK,EAAE,EAAE;IAC3C,IAAI,QAAQ;IACZ,MAAM,cAAc;IACpB,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,MAAM,gBAAgB,QAAQ,kBAAkB,IAAI,CAAC,UAAU,YAAY,MAAM,GAAG;IACpF,MAAM,SAAS,UAAU,MAAM,CAAC,CAAC,KAAK,GAAG;QACrC,MAAM,QAAQ,oBAAoB,OAAO,QAAQ,EAAE,MAAM,EAAE;QAC3D,IAAI,EAAE,IAAI,OAAO,IAAI;YACjB,MAAM,UAAU,AAAC,QAAQ,KAAK,KAAK,GAAI,KAAK,OAAO;YACnD,MAAM,SAAS,AAAC,QAAQ,KAAK,KAAK,GAAI,KAAK,MAAM;YACjD,MAAM,MAAM,QAAQ,EAAE,MAAM;YAC5B,sEAAsE;YACtE,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,OAAO,KAAK,SAAS;QACxD,OACK;YACD,MAAM,aAAa,SAAS,CAAC,QAAQ,EAAE,KAAK;YAC5C,MAAM,MAAM,QAAQ,EAAE,MAAM;YAC5B,uDAAuD;YACvD,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,OAAO,OAAO,KAAK;YAC1C,IAAI,SAAS,CAAC,QAAQ,EAAE,IAAI,YAAY;gBACpC,4EAA4E;gBAC5E,IAAI,IAAI,CAAC,UAAU,OAAO,CAAC,aAAa,eAAe;YAC3D;QACJ;QACA,SAAS,EAAE,MAAM;QACjB,OAAO;IACX,GAAG,EAAE;IACL,2BAA2B;IAC3B,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,GAAG,OAAO,OAAO,UAAU,QAAQ,EAAE;IAChE,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,GAAG,CAAC,UAAU,QAAQ,EAAE;IACtD,OAAO;AACX;AACA;;;CAGC,GACD,MAAM,gBAAgB,CAAC;IACnB,OAAO,iBAAiB,IAAI,EAAE,CAAC,EAAE,EAAE,cAAc,CAAC;AACtD;AACA;;;;;CAKC,GACD,MAAM,cAAc,CAAC;IACjB;;;;KAIC,GACD,OAAO,CAAC,kBAAkB;QACtB,IAAI,YAAY,QAAQ,SAAS,IAAI;QACrC,MAAM,aAAa,cAAc;QACjC,MAAM,QAAQ,SAAS,kBAAkB,YAAY;QACrD,IAAI,SAAS,UAAU,OAAO,iBAAiB;QAC/C,iEAAiE;QACjE,MAAO,OAAO,MAAM,KAAK,KAAK,YAAY,gBAAiB;YACvD,aAAa;YACb,SAAS,UAAU,OAAO,iBAAiB;QAC/C;QACA,IAAI,OAAO,MAAM,KAAK,KAAM,OAAO,MAAM,KAAK,KAAK,MAAM,CAAC,EAAE,KAAK,GAAI;YACjE,SAAS,aAAa,OAAO;QACjC;QACA,OAAO,WAAW,kBAAkB,OAAO,OAAO,KAAK,CAAC;IAC5D;AACJ;AAEA,IAAI;AACJ,CAAC,SAAU,SAAS;IAChB,SAAS,CAAC,SAAS,CAAC,OAAO,GAAG,EAAE,GAAG;IACnC,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,GAAG;AACzC,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAC/B,MAAM,sBAAsB;AAC5B,MAAM,kBAAkB;AACxB,MAAM,2BAA2B;IAC7B,QAAQ;IACR,OAAO;IACP,UAAU;IACV,eAAe;AACnB;AACA,MAAM,qBAAqB;IACvB,QAAQ;IACR,OAAO;IACP,UAAU;IACV,eAAe;AACnB;AACA,MAAM,2BAA2B;IAC7B,QAAQ,CAAC;IACT,OAAO,CAAC;IACR,UAAU;IACV,eAAe;AACnB;AACA,MAAM,qBAAqB;IACvB,QAAQ,CAAC;IACT,OAAO,CAAC;IACR,UAAU;IACV,eAAe;AACnB;AACA,MAAM,gBAAgB,CAAC,WAAW;IAC9B,MAAM,mBAAmB,QAAQ,gBAAgB,IAAI,CAAC;IACtD,MAAM,mBAAmB,QAAQ,gBAAgB,IAAI,CAAC;IACtD,OAAO,cAAc,UAAU,IAAI,GAC7B,OAAO,MAAM,CAAC,CAAC,GAAG,oBAAoB,oBACtC,OAAO,MAAM,CAAC,CAAC,GAAG,oBAAoB;AAChD;AACA,MAAM,sBAAsB,CAAC,WAAW;IACpC,MAAM,yBAAyB,QAAQ,sBAAsB,IAAI,CAAC;IAClE,MAAM,yBAAyB,QAAQ,sBAAsB,IAAI,CAAC;IAClE,OAAO,cAAc,UAAU,IAAI,GAC7B,OAAO,MAAM,CAAC,CAAC,GAAG,0BAA0B,0BAC5C,OAAO,MAAM,CAAC,CAAC,GAAG,0BAA0B;AACtD;AACA,MAAM,SAAS,CAAC,WAAW,UAAY,CAAC;QACpC,MAAM,aAAa,cAAc,WAAW;QAC5C,MAAM,mBAAmB,oBAAoB,WAAW;QACxD,MAAM,UAAU,EAAE;QAClB,IAAK,IAAI,QAAQ,GAAG,QAAQ,OAAO,MAAM,EAAE,SAAS,EAAG;YACnD,IAAI;YACJ,MAAM,QAAQ,MAAM,CAAC,MAAM;YAC3B,IAAI,aAAa,QAAQ;gBACrB,IAAI,OAAO,MAAM,CAAC,CAAC,GAAG;gBACtB,IAAI,UAAU,OAAO,MAAM,GAAG,GAAG;oBAC7B,EAAE,MAAM,GAAG;oBACX,IAAI,QAAQ,GAAG;wBACX,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,GAAG;oBAC/B;gBACJ;YACJ,OACK,IAAI,MAAM,MAAM,IAAI,QAAQ,GAAG;gBAChC,IAAI,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,QAAQ,EAAE;gBACxC,EAAE,MAAM,GAAG;gBACX,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,GAAG;YAC/B,OACK;gBACD,IAAI,OAAO,MAAM,CAAC,CAAC,GAAG;YAC1B;YACA,QAAQ,IAAI,CAAC;QACjB;QACA,OAAO;IACX;AACA,MAAM,aAAa,CAAC,KAAK,MAAM;IAC3B,MAAM,YAAY,MAAM,IAAI,UAAU,IAAI,GAAG,UAAU,MAAM;IAC7D,MAAM,YAAY,OAAO,WAAW;IACpC,MAAM,UAAU,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK;QACnC,OAAO,IAAI,MAAM,CAAC,UAAU,IAAI,MAAM;IAC1C,GAAG,EAAE;IACL,OAAO,CAAC,EAAE,CAAC,MAAM,GAAG;IACpB,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG;IACpC,OAAO;AACX;AAEA,MAAM,mBAAmB;AACzB,MAAM,gBAAgB;AACtB,MAAM,eAAe,CAAC,KAAK;IACvB,IAAI,QAAQ;IACZ,MAAM,aAAa,EAAE;IACrB,MAAM,gBAAgB,EAAE;IACxB,IAAK,IAAI,WAAW,kBAAkB,YAAY,eAAe,YAAY,EAAG;QAC5E,UAAU,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS,GAAG;IACrD;IACA,mCAAmC;IACnC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,KAAK,EAAG;QACxC,MAAM,IAAI,OAAO,CAAC,EAAE;QACpB,MAAM,MAAM,EAAE,MAAM,GAAG,EAAE,KAAK;QAC9B,SAAS;QACT,UAAU,CAAC,EAAE,QAAQ,CAAC,IAAI;QAC1B,IAAI,EAAE,aAAa,EAAE;YACjB,aAAa,CAAC,EAAE,QAAQ,CAAC,IAAI;QACjC;IACJ;IACA,gDAAgD;IAChD,IAAI,kBAAkB,CAAC;IACvB,IAAI,qBAAqB;IACzB,IAAI,eAAe;IACnB,IAAI;IACJ,IAAK,WAAW,kBAAkB,YAAY,eAAe,YAAY,EAAG;QACxE,MAAM,cAAc,UAAU,CAAC,SAAS;QACxC,IAAI,gBAAgB,GAAG;YACnB,IAAI,oBAAoB,CAAC,GAAG;gBACxB,kBAAkB;gBAClB,qBAAqB;YACzB;YACA,wDAAwD;YACxD,IAAI,KAAK,GAAG,CAAC,iBAAiB,KAAK,GAAG,CAAC,cAAc;gBACjD,UAAU,CAAC,SAAS,GAAG,eAAe;gBACtC,aAAa,CAAC,SAAS,GAAG;gBAC1B,eAAe;gBACf;YACJ;YACA,uDAAuD;YACvD,yDAAyD;YACzD,UAAU,CAAC,SAAS,GAAG;YACvB,gBAAgB;YAChB,kFAAkF;YAClF,IAAI,aAAa,CAAC,SAAS,KAAK,GAAG;gBAC/B,aAAa,CAAC,SAAS,GAAG,eAAe,aAAa,CAAC,SAAS;gBAChE,eAAe;gBACf;YACJ;QACJ;IACJ;IACA,yCAAyC;IACzC,IAAK,IAAI,IAAI,WAAW,GAAG,KAAK,eAAe,KAAK,EAAG;QACnD,UAAU,CAAC,EAAE,GAAG;QAChB,aAAa,CAAC,EAAE,GAAG;IACvB;IACA,oFAAoF;IACpF,oEAAoE;IACpE,IAAI,eAAe,KAAK,kBAAkB,CAAC,GAAG;QAC1C,UAAU,CAAC,gBAAgB,GACvB,CAAC,qBAAqB,CAAC,MAAM,KAAK,CAAC,IAAI;IAC/C;IACA,yEAAyE;IACzE,MAAM,YAAY,EAAE;IACpB,IAAK,IAAI,QAAQ,GAAG,QAAQ,QAAQ,MAAM,EAAE,SAAS,EAAG;QACpD,mEAAmE;QACnE,+DAA+D;QAC/D,MAAM,IAAI,OAAO,CAAC,MAAM;QACxB,MAAM,OAAO,OAAO,CAAC,QAAQ,EAAE;QAC/B,IAAI,OAAO,EAAE,KAAK,GAAG,UAAU,CAAC,EAAE,QAAQ,CAAC;QAC3C,IAAI,MAAM;YACN,QAAQ,KAAK,MAAM,GAAG,UAAU,CAAC,KAAK,QAAQ,CAAC;QACnD;QACA,yEAAyE;QACzE,IAAI,EAAE,aAAa,EAAE;YACjB,QAAQ,EAAE,KAAK,GAAG,aAAa,CAAC,EAAE,QAAQ,CAAC;YAC3C,IAAI,MAAM;gBACN,QAAQ,KAAK,MAAM,GAAG,aAAa,CAAC,KAAK,QAAQ,CAAC;YACtD;QACJ;QACA,UAAU,IAAI,CAAC;IACnB;IACA,OAAO;AACX;AAEA;;;;;;CAMC,GACD,MAAM,cAAc,CAAC,WAAW;IAC5B,IAAI,QAAQ;IACZ,KAAK,MAAM,OAAO,KAAK,IAAI,CAAE;QACzB,KAAK,MAAM,YAAY,IAAI,SAAS,CAAE;YAClC,SAAS,QAAQ,IAAI,SAAS,CAAC,QAAQ;QAC3C;IACJ;IACA,OAAO;AACX;AACA;;;;;;CAMC,GACD,MAAM,gBAAgB,CAAC;IACnB;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK,GAAG,aAAa;QAC1C,IAAI,QAAQ,GACR,OAAO,MAAM,YAAY;QAC7B,MAAM,UAAU,WAAW,KAAK,MAAM;QACtC,MAAM,YAAY,aAAa,KAAK;QACpC,OAAO,YAAY,WAAW;IAClC;AACJ;AAEA;;;;;CAKC,GACD,MAAM,SAAS,CAAC;IACZ,MAAM,UAAU,CAAC,KAAK,MAAQ,KAAK,GAAG,CAAC,KAAK,SAAS;IACrD,OAAO,iBAAiB,IAAI,CAAC,MAAM,CAAC,SAAS;AACjD;AAEA,+DAA+D;AAC/D,MAAM,iBAAiB;AACvB;;;;CAIC,GACD,MAAM,iBAAiB,IAAM,CAAC;QAC1B,IAAI,IAAI,KAAK,YAAY,IAAI;QAC7B,MAAM,gBAAgB,KAAK,aAAa,IAAI;QAC5C,MAAM,OAAO,aAAa,QAAQ;QAClC,KAAK,eAAe,GAAG,EAAE;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE,KAAK,EAAG;YAC1C,MAAM,MAAM,KAAK,IAAI,CAAC,EAAE;YACxB,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,GAAG,eAAe;YAChD,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,GAAG;YACrE,IAAI,IAAI,UAAU,CAAC,SAAS,EAAE;gBAC1B,MAAM,OAAO;oBACT;oBACA,GAAG,OAAO,QAAQ,YAAY;oBAC9B;oBACA,QAAQ;gBACZ;gBACA,MAAM,iBAAiB;oBACnB;oBACA,SAAS,IAAI,UAAU,CAAC,OAAO;oBAC/B,OAAO,IAAI,UAAU,CAAC,cAAc,IAAI;oBACxC,OAAO,IAAI,UAAU,CAAC,cAAc,IAAI;gBAC5C;gBACA,KAAK,eAAe,CAAC,IAAI,CAAC;YAC9B;YACA,IAAI,IAAI,UAAU,CAAC,MAAM,EAAE;gBACvB,MAAM,IAAI,OAAO,QAAQ,SAAS,OAAO;gBACzC,MAAM,OAAO;oBAAE;oBAAG;oBAAG;oBAAO,QAAQ;gBAAU;gBAC9C,MAAM,iBAAiB;oBACnB;oBACA,SAAS,IAAI,UAAU,CAAC,OAAO;oBAC/B,OAAO,IAAI,UAAU,CAAC,WAAW,IAAI;oBACrC,OAAO,IAAI,UAAU,CAAC,WAAW,IAAI;gBACzC;gBACA,KAAK,eAAe,CAAC,IAAI,CAAC;YAC9B;YACA,KAAK;QACT;QACA,OAAO;IACX;AAEA,MAAM,iBAAiB;IAAC;IAAU;IAAa;CAAU;AACzD;;CAEC,GACD,MAAM,iBAAiB;IACnB;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,IAAI,aAAa;QACjB,IAAI,YAAY;QAChB,IAAI,QAAQ;QACZ,MAAM,OAAO,EAAE;QACf,IAAI,CAAC,QACD,OAAO;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;YACvC,MAAM,OAAO,MAAM,CAAC,EAAE;YACtB,MAAM,YAAY,KAAK,WAAW,CAAC;YACnC,MAAM,SAAS,2JAAA,CAAA,UAAO,CAAC,SAAS,CAAC;YACjC,IAAI,WAAW,cAAc,CAAC,eAAe,QAAQ,CAAC,SAAS;gBAC3D,IAAI,eAAe,WAAW;oBAC1B,KAAK,IAAI,CAAC;wBACN,OAAO;wBACP,KAAK;wBACL,YAAY;4BAAE,QAAQ;wBAAW;oBACrC;gBACJ;gBACA,YAAY;gBACZ,aAAa;YACjB;YACA,SAAS,KAAK,MAAM;QACxB;QACA,IAAI,YAAY,OAAO,MAAM,EAAE;YAC3B,KAAK,IAAI,CAAC;gBACN,OAAO;gBACP,KAAK,OAAO,MAAM;gBAClB,YAAY;oBAAE,QAAQ;gBAAW;YACrC;QACJ;QACA,MAAM,SAAS;YAAE;YAAQ,MAAM;QAAK;QACpC,OAAO;IACX;AACJ;AAEA,MAAM,cAAc;AACpB,MAAM,aAAa,CAAA,GAAA,mIAAA,CAAA,UAAM,AAAD,EAAE,iJAAA,CAAA,UAAO;AACjC;;;CAGC,GACD,MAAM,cAAc,CAAC;IACjB,OAAO,KAAK,KAAK,CAAC;AACtB;AACA,MAAM,QAAQ,CAAC;AACf;;;CAGC,GACD,MAAM,WAAW,CAAC;IACd,MAAM,OAAO,KAAK,QAAQ,CAAC,eAAe,OAAO,WAAW;IAC5D,OAAO,YAAY;AACvB;AACA,MAAM,kBAAkB;IACpB;;;KAGC,GACD,OAAO,CAAC;QACJ,MAAM,WAAW,CAAC,CAAC,EAAE,MAAM;QAC3B,IAAI,CAAA,GAAA,wJAAA,CAAA,QAAK,AAAD,EAAE,OACN,OAAO,EAAE;QACb,IAAI,KAAK,CAAC,SAAS,EACf,OAAO,KAAK,CAAC,SAAS;QAC1B,KAAK,CAAC,SAAS,GAAG,SAAS;QAC3B,OAAO,KAAK,CAAC,SAAS;IAC1B;AACJ;AAEA,MAAM,sBAAsB;IAAC;CAAI;AACjC,MAAM,cAAc,CAAC,MAAQ,IAAI,UAAU,CAAC,QAAQ,IAAI;AACxD,MAAM,wBAAwB,CAAC,WAAW,WAAW;IACjD,MAAM,wBAAwB;WAAI;QAAW;KAAS;IACtD,IAAK,IAAI,IAAI,GAAG,IAAI,sBAAsB,MAAM,EAAE,KAAK,EAAG;QACtD,MAAM,OAAO,qBAAqB,CAAC,EAAE;QACrC,IAAI,CAAC,oBAAoB,QAAQ,CAAC,cAC9B,QACA,KAAK,oBAAoB,IACzB,KAAK,oBAAoB,CAAC,YAAY;YACtC,OAAO;QACX;IACJ;IACA,OAAO,UAAU,EAAE,CAAC,CAAC;AACzB;AACA,MAAM,mBAAmB,IAAM,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE;QAC5C,IAAI,WAAW;QACf,IAAI,eAAe;QACnB,IAAI,YAAY;QAChB,IAAI,QAAQ;QACZ,MAAM,MAAM,EAAE;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACrC,MAAM,MAAM,IAAI,CAAC,EAAE;YACnB,IAAI,OAAO,MAAM,KAAK,GAAG;gBACrB,IAAI,IAAI,CAAC;oBACL,OAAO;oBACP,KAAK;oBACL,YAAY;wBAAE,MAAM,IAAI,UAAU,CAAC,IAAI;oBAAC;gBAC5C;gBACA;YACJ;YACA,MAAM,QAAQ,OAAO,KAAK,CAAC,IAAI,KAAK,EAAE,IAAI,GAAG;YAC7C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;gBACtC,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,MAAM,YAAY,KAAK,WAAW,CAAC;gBACnC,kFAAkF;gBAClF,MAAM,OAAO,sBAAsB,WAAW,IAAI,UAAU,CAAC,IAAI,EAAE;gBACnE,MAAM,WAAW,YAAY;gBAC7B,2DAA2D;gBAC3D,IAAI,SAAS,YACT,aAAa,gBACb,KAAK,UAAU,KAAK,SAAS,UAAU,EAAE;oBACzC,IAAI,UAAU;wBACV,IAAI,IAAI,CAAC;4BACL,OAAO;4BACP,KAAK;4BACL,YAAY;gCACR,MAAM;oCAAC;iCAAS;gCAChB,OAAO,eAAe,SAAS,UAAU;4BAC7C;wBACJ;oBACJ;oBACA,WAAW;oBACX,eAAe;oBACf,YAAY;gBAChB;gBACA,SAAS,KAAK,MAAM;YACxB;QACJ;QACA,IAAI,YAAY,OAAO,MAAM,EAAE;YAC3B,MAAM,WAAW,YAAY,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE;YAClC,IAAI,IAAI,CAAC;gBACL,OAAO;gBACP,KAAK,OAAO,MAAM;gBAClB,YAAY;oBACR,MAAM;wBAAC;qBAAS;oBAChB,OAAO,WAAW,SAAS,UAAU;gBACzC;YACJ;QACJ;QACA,OAAO;YAAE;YAAQ,MAAM;QAAI;IAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6547, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/image/lib/index.browser.js"], "sourcesContent": ["import _PNG from '@react-pdf/png-js';\nimport _JPEG from 'jay-peg';\n\nvar global$1 = (typeof global !== \"undefined\" ? global :\n  typeof self !== \"undefined\" ? self :\n  typeof window !== \"undefined\" ? window : {});\n\nvar lookup = [];\nvar revLookup = [];\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array;\nvar inited = false;\nfunction init () {\n  inited = true;\n  var code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n  for (var i = 0, len = code.length; i < len; ++i) {\n    lookup[i] = code[i];\n    revLookup[code.charCodeAt(i)] = i;\n  }\n\n  revLookup['-'.charCodeAt(0)] = 62;\n  revLookup['_'.charCodeAt(0)] = 63;\n}\n\nfunction toByteArray (b64) {\n  if (!inited) {\n    init();\n  }\n  var i, j, l, tmp, placeHolders, arr;\n  var len = b64.length;\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // the number of equal signs (place holders)\n  // if there are two placeholders, than the two characters before it\n  // represent one byte\n  // if there is only one, then the three characters before it represent 2 bytes\n  // this is just a cheap hack to not do indexOf twice\n  placeHolders = b64[len - 2] === '=' ? 2 : b64[len - 1] === '=' ? 1 : 0;\n\n  // base64 is 4/3 + up to two characters of the original data\n  arr = new Arr(len * 3 / 4 - placeHolders);\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  l = placeHolders > 0 ? len - 4 : len;\n\n  var L = 0;\n\n  for (i = 0, j = 0; i < l; i += 4, j += 3) {\n    tmp = (revLookup[b64.charCodeAt(i)] << 18) | (revLookup[b64.charCodeAt(i + 1)] << 12) | (revLookup[b64.charCodeAt(i + 2)] << 6) | revLookup[b64.charCodeAt(i + 3)];\n    arr[L++] = (tmp >> 16) & 0xFF;\n    arr[L++] = (tmp >> 8) & 0xFF;\n    arr[L++] = tmp & 0xFF;\n  }\n\n  if (placeHolders === 2) {\n    tmp = (revLookup[b64.charCodeAt(i)] << 2) | (revLookup[b64.charCodeAt(i + 1)] >> 4);\n    arr[L++] = tmp & 0xFF;\n  } else if (placeHolders === 1) {\n    tmp = (revLookup[b64.charCodeAt(i)] << 10) | (revLookup[b64.charCodeAt(i + 1)] << 4) | (revLookup[b64.charCodeAt(i + 2)] >> 2);\n    arr[L++] = (tmp >> 8) & 0xFF;\n    arr[L++] = tmp & 0xFF;\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] + lookup[num >> 12 & 0x3F] + lookup[num >> 6 & 0x3F] + lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp;\n  var output = [];\n  for (var i = start; i < end; i += 3) {\n    tmp = (uint8[i] << 16) + (uint8[i + 1] << 8) + (uint8[i + 2]);\n    output.push(tripletToBase64(tmp));\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  if (!inited) {\n    init();\n  }\n  var tmp;\n  var len = uint8.length;\n  var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes\n  var output = '';\n  var parts = [];\n  var maxChunkLength = 16383; // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)));\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1];\n    output += lookup[tmp >> 2];\n    output += lookup[(tmp << 4) & 0x3F];\n    output += '==';\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + (uint8[len - 1]);\n    output += lookup[tmp >> 10];\n    output += lookup[(tmp >> 4) & 0x3F];\n    output += lookup[(tmp << 2) & 0x3F];\n    output += '=';\n  }\n\n  parts.push(output);\n\n  return parts.join('')\n}\n\nfunction read (buffer, offset, isLE, mLen, nBytes) {\n  var e, m;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var nBits = -7;\n  var i = isLE ? (nBytes - 1) : 0;\n  var d = isLE ? -1 : 1;\n  var s = buffer[offset + i];\n\n  i += d;\n\n  e = s & ((1 << (-nBits)) - 1);\n  s >>= (-nBits);\n  nBits += eLen;\n  for (; nBits > 0; e = e * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1);\n  e >>= (-nBits);\n  nBits += mLen;\n  for (; nBits > 0; m = m * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias;\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen);\n    e = e - eBias;\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nfunction write (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0);\n  var i = isLE ? 0 : (nBytes - 1);\n  var d = isLE ? 1 : -1;\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0;\n\n  value = Math.abs(value);\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0;\n    e = eMax;\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2);\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--;\n      c *= 2;\n    }\n    if (e + eBias >= 1) {\n      value += rt / c;\n    } else {\n      value += rt * Math.pow(2, 1 - eBias);\n    }\n    if (value * c >= 2) {\n      e++;\n      c /= 2;\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0;\n      e = eMax;\n    } else if (e + eBias >= 1) {\n      m = (value * c - 1) * Math.pow(2, mLen);\n      e = e + eBias;\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen);\n      e = 0;\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m;\n  eLen += mLen;\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128;\n}\n\nvar toString = {}.toString;\n\nvar isArray = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n\n/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n\nvar INSPECT_MAX_BYTES = 50;\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - IE10 has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\nBuffer.TYPED_ARRAY_SUPPORT = global$1.TYPED_ARRAY_SUPPORT !== undefined\n  ? global$1.TYPED_ARRAY_SUPPORT\n  : true;\n\n/*\n * Export kMaxLength after typed array support is determined.\n */\nkMaxLength();\n\nfunction kMaxLength () {\n  return Buffer.TYPED_ARRAY_SUPPORT\n    ? 0x7fffffff\n    : 0x3fffffff\n}\n\nfunction createBuffer (that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length')\n  }\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length);\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length);\n    }\n    that.length = length;\n  }\n\n  return that\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer (arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length)\n  }\n\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error(\n        'If encoding is specified then the first argument must be a string'\n      )\n    }\n    return allocUnsafe(this, arg)\n  }\n  return from(this, arg, encodingOrOffset, length)\n}\n\nBuffer.poolSize = 8192; // not used by this implementation\n\n// TODO: Legacy, not needed anymore. Remove in next major version.\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype;\n  return arr\n};\n\nfunction from (that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number')\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset)\n  }\n\n  return fromObject(that, value)\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length)\n};\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype;\n  Buffer.__proto__ = Uint8Array;\n  if (typeof Symbol !== 'undefined' && Symbol.species &&\n      Buffer[Symbol.species] === Buffer) ;\n}\n\nfunction assertSize (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number')\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative')\n  }\n}\n\nfunction alloc (that, size, fill, encoding) {\n  assertSize(size);\n  if (size <= 0) {\n    return createBuffer(that, size)\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string'\n      ? createBuffer(that, size).fill(fill, encoding)\n      : createBuffer(that, size).fill(fill)\n  }\n  return createBuffer(that, size)\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding)\n};\n\nfunction allocUnsafe (that, size) {\n  assertSize(size);\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0;\n    }\n  }\n  return that\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size)\n};\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size)\n};\n\nfunction fromString (that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8';\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding')\n  }\n\n  var length = byteLength(string, encoding) | 0;\n  that = createBuffer(that, length);\n\n  var actual = that.write(string, encoding);\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual);\n  }\n\n  return that\n}\n\nfunction fromArrayLike (that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0;\n  that = createBuffer(that, length);\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255;\n  }\n  return that\n}\n\nfunction fromArrayBuffer (that, array, byteOffset, length) {\n  array.byteLength; // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds')\n  }\n\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array);\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset);\n  } else {\n    array = new Uint8Array(array, byteOffset, length);\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array;\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array);\n  }\n  return that\n}\n\nfunction fromObject (that, obj) {\n  if (internalIsBuffer(obj)) {\n    var len = checked(obj.length) | 0;\n    that = createBuffer(that, len);\n\n    if (that.length === 0) {\n      return that\n    }\n\n    obj.copy(that, 0, 0, len);\n    return that\n  }\n\n  if (obj) {\n    if ((typeof ArrayBuffer !== 'undefined' &&\n        obj.buffer instanceof ArrayBuffer) || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0)\n      }\n      return fromArrayLike(that, obj)\n    }\n\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data)\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.')\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + kMaxLength().toString(16) + ' bytes')\n  }\n  return length | 0\n}\nBuffer.isBuffer = isBuffer$1;\nfunction internalIsBuffer (b) {\n  return !!(b != null && b._isBuffer)\n}\n\nBuffer.compare = function compare (a, b) {\n  if (!internalIsBuffer(a) || !internalIsBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers')\n  }\n\n  if (a === b) return 0\n\n  var x = a.length;\n  var y = b.length;\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i];\n      y = b[i];\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n};\n\nBuffer.isEncoding = function isEncoding (encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true\n    default:\n      return false\n  }\n};\n\nBuffer.concat = function concat (list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0)\n  }\n\n  var i;\n  if (length === undefined) {\n    length = 0;\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length;\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length);\n  var pos = 0;\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i];\n    if (!internalIsBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    }\n    buf.copy(buffer, pos);\n    pos += buf.length;\n  }\n  return buffer\n};\n\nfunction byteLength (string, encoding) {\n  if (internalIsBuffer(string)) {\n    return string.length\n  }\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' &&\n      (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    string = '' + string;\n  }\n\n  var len = string.length;\n  if (len === 0) return 0\n\n  // Use a for loop to avoid recursion\n  var loweredCase = false;\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2\n      case 'hex':\n        return len >>> 1\n      case 'base64':\n        return base64ToBytes(string).length\n      default:\n        if (loweredCase) return utf8ToBytes(string).length // assume utf8\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\nBuffer.byteLength = byteLength;\n\nfunction slowToString (encoding, start, end) {\n  var loweredCase = false;\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0;\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return ''\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length;\n  }\n\n  if (end <= 0) {\n    return ''\n  }\n\n  // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0;\n  start >>>= 0;\n\n  if (end <= start) {\n    return ''\n  }\n\n  if (!encoding) encoding = 'utf8';\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end)\n\n      case 'ascii':\n        return asciiSlice(this, start, end)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end)\n\n      case 'base64':\n        return base64Slice(this, start, end)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = (encoding + '').toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\n\n// The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\nBuffer.prototype._isBuffer = true;\n\nfunction swap (b, n, m) {\n  var i = b[n];\n  b[n] = b[m];\n  b[m] = i;\n}\n\nBuffer.prototype.swap16 = function swap16 () {\n  var len = this.length;\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits')\n  }\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1);\n  }\n  return this\n};\n\nBuffer.prototype.swap32 = function swap32 () {\n  var len = this.length;\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits')\n  }\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3);\n    swap(this, i + 1, i + 2);\n  }\n  return this\n};\n\nBuffer.prototype.swap64 = function swap64 () {\n  var len = this.length;\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits')\n  }\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7);\n    swap(this, i + 1, i + 6);\n    swap(this, i + 2, i + 5);\n    swap(this, i + 3, i + 4);\n  }\n  return this\n};\n\nBuffer.prototype.toString = function toString () {\n  var length = this.length | 0;\n  if (length === 0) return ''\n  if (arguments.length === 0) return utf8Slice(this, 0, length)\n  return slowToString.apply(this, arguments)\n};\n\nBuffer.prototype.equals = function equals (b) {\n  if (!internalIsBuffer(b)) throw new TypeError('Argument must be a Buffer')\n  if (this === b) return true\n  return Buffer.compare(this, b) === 0\n};\n\nBuffer.prototype.inspect = function inspect () {\n  var str = '';\n  var max = INSPECT_MAX_BYTES;\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ');\n    if (this.length > max) str += ' ... ';\n  }\n  return '<Buffer ' + str + '>'\n};\n\nBuffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {\n  if (!internalIsBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer')\n  }\n\n  if (start === undefined) {\n    start = 0;\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0;\n  }\n  if (thisStart === undefined) {\n    thisStart = 0;\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length;\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index')\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0\n  }\n  if (thisStart >= thisEnd) {\n    return -1\n  }\n  if (start >= end) {\n    return 1\n  }\n\n  start >>>= 0;\n  end >>>= 0;\n  thisStart >>>= 0;\n  thisEnd >>>= 0;\n\n  if (this === target) return 0\n\n  var x = thisEnd - thisStart;\n  var y = end - start;\n  var len = Math.min(x, y);\n\n  var thisCopy = this.slice(thisStart, thisEnd);\n  var targetCopy = target.slice(start, end);\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i];\n      y = targetCopy[i];\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n};\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset;\n    byteOffset = 0;\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff;\n  } else if (byteOffset < -2147483648) {\n    byteOffset = -2147483648;\n  }\n  byteOffset = +byteOffset;  // Coerce to Number.\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : (buffer.length - 1);\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset;\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1\n    else byteOffset = buffer.length - 1;\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0;\n    else return -1\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding);\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (internalIsBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)\n  } else if (typeof val === 'number') {\n    val = val & 0xFF; // Search for a byte value [0-255]\n    if (Buffer.TYPED_ARRAY_SUPPORT &&\n        typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)\n      }\n    }\n    return arrayIndexOf(buffer, [ val ], byteOffset, encoding, dir)\n  }\n\n  throw new TypeError('val must be string, number or Buffer')\n}\n\nfunction arrayIndexOf (arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1;\n  var arrLength = arr.length;\n  var valLength = val.length;\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase();\n    if (encoding === 'ucs2' || encoding === 'ucs-2' ||\n        encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1\n      }\n      indexSize = 2;\n      arrLength /= 2;\n      valLength /= 2;\n      byteOffset /= 2;\n    }\n  }\n\n  function read (buf, i) {\n    if (indexSize === 1) {\n      return buf[i]\n    } else {\n      return buf.readUInt16BE(i * indexSize)\n    }\n  }\n\n  var i;\n  if (dir) {\n    var foundIndex = -1;\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i;\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex;\n        foundIndex = -1;\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength;\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true;\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false;\n          break\n        }\n      }\n      if (found) return i\n    }\n  }\n\n  return -1\n}\n\nBuffer.prototype.includes = function includes (val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1\n};\n\nBuffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)\n};\n\nBuffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)\n};\n\nfunction hexWrite (buf, string, offset, length) {\n  offset = Number(offset) || 0;\n  var remaining = buf.length - offset;\n  if (!length) {\n    length = remaining;\n  } else {\n    length = Number(length);\n    if (length > remaining) {\n      length = remaining;\n    }\n  }\n\n  // must be an even number of digits\n  var strLen = string.length;\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string')\n\n  if (length > strLen / 2) {\n    length = strLen / 2;\n  }\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16);\n    if (isNaN(parsed)) return i\n    buf[offset + i] = parsed;\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction asciiWrite (buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length)\n}\n\nfunction latin1Write (buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length)\n}\n\nfunction base64Write (buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length)\n}\n\nfunction ucs2Write (buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nBuffer.prototype.write = function write (string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8';\n    length = this.length;\n    offset = 0;\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset;\n    length = this.length;\n    offset = 0;\n  // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0;\n    if (isFinite(length)) {\n      length = length | 0;\n      if (encoding === undefined) encoding = 'utf8';\n    } else {\n      encoding = length;\n      length = undefined;\n    }\n  // legacy write(string, encoding, offset, length) - remove in v0.13\n  } else {\n    throw new Error(\n      'Buffer.write(string, encoding, offset[, length]) is no longer supported'\n    )\n  }\n\n  var remaining = this.length - offset;\n  if (length === undefined || length > remaining) length = remaining;\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  if (!encoding) encoding = 'utf8';\n\n  var loweredCase = false;\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length)\n\n      case 'ascii':\n        return asciiWrite(this, string, offset, length)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length)\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n};\n\nBuffer.prototype.toJSON = function toJSON () {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  }\n};\n\nfunction base64Slice (buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return fromByteArray(buf)\n  } else {\n    return fromByteArray(buf.slice(start, end))\n  }\n}\n\nfunction utf8Slice (buf, start, end) {\n  end = Math.min(buf.length, end);\n  var res = [];\n\n  var i = start;\n  while (i < end) {\n    var firstByte = buf[i];\n    var codePoint = null;\n    var bytesPerSequence = (firstByte > 0xEF) ? 4\n      : (firstByte > 0xDF) ? 3\n      : (firstByte > 0xBF) ? 2\n      : 1;\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint;\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte;\n          }\n          break\n        case 2:\n          secondByte = buf[i + 1];\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F);\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint;\n            }\n          }\n          break\n        case 3:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F);\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint;\n            }\n          }\n          break\n        case 4:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          fourthByte = buf[i + 3];\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F);\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint;\n            }\n          }\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD;\n      bytesPerSequence = 1;\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000;\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800);\n      codePoint = 0xDC00 | codePoint & 0x3FF;\n    }\n\n    res.push(codePoint);\n    i += bytesPerSequence;\n  }\n\n  return decodeCodePointsArray(res)\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nvar MAX_ARGUMENTS_LENGTH = 0x1000;\n\nfunction decodeCodePointsArray (codePoints) {\n  var len = codePoints.length;\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  var res = '';\n  var i = 0;\n  while (i < len) {\n    res += String.fromCharCode.apply(\n      String,\n      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)\n    );\n  }\n  return res\n}\n\nfunction asciiSlice (buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F);\n  }\n  return ret\n}\n\nfunction latin1Slice (buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i]);\n  }\n  return ret\n}\n\nfunction hexSlice (buf, start, end) {\n  var len = buf.length;\n\n  if (!start || start < 0) start = 0;\n  if (!end || end < 0 || end > len) end = len;\n\n  var out = '';\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i]);\n  }\n  return out\n}\n\nfunction utf16leSlice (buf, start, end) {\n  var bytes = buf.slice(start, end);\n  var res = '';\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256);\n  }\n  return res\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  var len = this.length;\n  start = ~~start;\n  end = end === undefined ? len : ~~end;\n\n  if (start < 0) {\n    start += len;\n    if (start < 0) start = 0;\n  } else if (start > len) {\n    start = len;\n  }\n\n  if (end < 0) {\n    end += len;\n    if (end < 0) end = 0;\n  } else if (end > len) {\n    end = len;\n  }\n\n  if (end < start) end = start;\n\n  var newBuf;\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end);\n    newBuf.__proto__ = Buffer.prototype;\n  } else {\n    var sliceLen = end - start;\n    newBuf = new Buffer(sliceLen, undefined);\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start];\n    }\n  }\n\n  return newBuf\n};\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset (offset, ext, length) {\n  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')\n}\n\nBuffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  return val\n};\n\nBuffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length);\n  }\n\n  var val = this[offset + --byteLength];\n  var mul = 1;\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul;\n  }\n\n  return val\n};\n\nBuffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  return this[offset]\n};\n\nBuffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] | (this[offset + 1] << 8)\n};\n\nBuffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return (this[offset] << 8) | this[offset + 1]\n};\n\nBuffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n\n  return ((this[offset]) |\n      (this[offset + 1] << 8) |\n      (this[offset + 2] << 16)) +\n      (this[offset + 3] * 0x1000000)\n};\n\nBuffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n\n  return (this[offset] * 0x1000000) +\n    ((this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    this[offset + 3])\n};\n\nBuffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n  mul *= 0x80;\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n\n  return val\n};\n\nBuffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n\n  var i = byteLength;\n  var mul = 1;\n  var val = this[offset + --i];\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul;\n  }\n  mul *= 0x80;\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n\n  return val\n};\n\nBuffer.prototype.readInt8 = function readInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  if (!(this[offset] & 0x80)) return (this[offset])\n  return ((0xff - this[offset] + 1) * -1)\n};\n\nBuffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset] | (this[offset + 1] << 8);\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n};\n\nBuffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset + 1] | (this[offset] << 8);\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n};\n\nBuffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n\n  return (this[offset]) |\n    (this[offset + 1] << 8) |\n    (this[offset + 2] << 16) |\n    (this[offset + 3] << 24)\n};\n\nBuffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n\n  return (this[offset] << 24) |\n    (this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    (this[offset + 3])\n};\n\nBuffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return read(this, offset, true, 23, 4)\n};\n\nBuffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return read(this, offset, false, 23, 4)\n};\n\nBuffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return read(this, offset, true, 52, 8)\n};\n\nBuffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return read(this, offset, false, 52, 8)\n};\n\nfunction checkInt (buf, value, offset, ext, max, min) {\n  if (!internalIsBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance')\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds')\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n}\n\nBuffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var mul = 1;\n  var i = 0;\n  this[offset] = value & 0xFF;\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF;\n  }\n\n  return offset + byteLength\n};\n\nBuffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  this[offset + i] = value & 0xFF;\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF;\n  }\n\n  return offset + byteLength\n};\n\nBuffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  this[offset] = (value & 0xff);\n  return offset + 1\n};\n\nfunction objectWriteUInt16 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1;\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & (0xff << (8 * (littleEndian ? i : 1 - i)))) >>>\n      (littleEndian ? i : 1 - i) * 8;\n  }\n}\n\nBuffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff);\n    this[offset + 1] = (value >>> 8);\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n  return offset + 2\n};\n\nBuffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8);\n    this[offset + 1] = (value & 0xff);\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n  return offset + 2\n};\n\nfunction objectWriteUInt32 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1;\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = (value >>> (littleEndian ? i : 3 - i) * 8) & 0xff;\n  }\n}\n\nBuffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = (value >>> 24);\n    this[offset + 2] = (value >>> 16);\n    this[offset + 1] = (value >>> 8);\n    this[offset] = (value & 0xff);\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n  return offset + 4\n};\n\nBuffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24);\n    this[offset + 1] = (value >>> 16);\n    this[offset + 2] = (value >>> 8);\n    this[offset + 3] = (value & 0xff);\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n  return offset + 4\n};\n\nBuffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = 0;\n  var mul = 1;\n  var sub = 0;\n  this[offset] = value & 0xFF;\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1;\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength\n};\n\nBuffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  var sub = 0;\n  this[offset + i] = value & 0xFF;\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1;\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength\n};\n\nBuffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -128);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  if (value < 0) value = 0xff + value + 1;\n  this[offset] = (value & 0xff);\n  return offset + 1\n};\n\nBuffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -32768);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff);\n    this[offset + 1] = (value >>> 8);\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n  return offset + 2\n};\n\nBuffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -32768);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8);\n    this[offset + 1] = (value & 0xff);\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n  return offset + 2\n};\n\nBuffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -2147483648);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff);\n    this[offset + 1] = (value >>> 8);\n    this[offset + 2] = (value >>> 16);\n    this[offset + 3] = (value >>> 24);\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n  return offset + 4\n};\n\nBuffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -2147483648);\n  if (value < 0) value = 0xffffffff + value + 1;\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24);\n    this[offset + 1] = (value >>> 16);\n    this[offset + 2] = (value >>> 8);\n    this[offset + 3] = (value & 0xff);\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n  return offset + 4\n};\n\nfunction checkIEEE754 (buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n  if (offset < 0) throw new RangeError('Index out of range')\n}\n\nfunction writeFloat (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4);\n  }\n  write(buf, value, offset, littleEndian, 23, 4);\n  return offset + 4\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert)\n};\n\nBuffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert)\n};\n\nfunction writeDouble (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8);\n  }\n  write(buf, value, offset, littleEndian, 52, 8);\n  return offset + 8\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert)\n};\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert)\n};\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!start) start = 0;\n  if (!end && end !== 0) end = this.length;\n  if (targetStart >= target.length) targetStart = target.length;\n  if (!targetStart) targetStart = 0;\n  if (end > 0 && end < start) end = start;\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length;\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start;\n  }\n\n  var len = end - start;\n  var i;\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else {\n    Uint8Array.prototype.set.call(\n      target,\n      this.subarray(start, start + len),\n      targetStart\n    );\n  }\n\n  return len\n};\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill (val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start;\n      start = 0;\n      end = this.length;\n    } else if (typeof end === 'string') {\n      encoding = end;\n      end = this.length;\n    }\n    if (val.length === 1) {\n      var code = val.charCodeAt(0);\n      if (code < 256) {\n        val = code;\n      }\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string')\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding)\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255;\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0;\n  end = end === undefined ? this.length : end >>> 0;\n\n  if (!val) val = 0;\n\n  var i;\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val;\n    }\n  } else {\n    var bytes = internalIsBuffer(val)\n      ? val\n      : utf8ToBytes(new Buffer(val, encoding).toString());\n    var len = bytes.length;\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len];\n    }\n  }\n\n  return this\n};\n\n// HELPER FUNCTIONS\n// ================\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g;\n\nfunction base64clean (str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, '');\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return ''\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '=';\n  }\n  return str\n}\n\nfunction stringtrim (str) {\n  if (str.trim) return str.trim()\n  return str.replace(/^\\s+|\\s+$/g, '')\n}\n\nfunction toHex (n) {\n  if (n < 16) return '0' + n.toString(16)\n  return n.toString(16)\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity;\n  var codePoint;\n  var length = string.length;\n  var leadSurrogate = null;\n  var bytes = [];\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i);\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint;\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n        leadSurrogate = codePoint;\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000;\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n    }\n\n    leadSurrogate = null;\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint);\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      );\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      );\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      );\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction asciiToBytes (str) {\n  var byteArray = [];\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF);\n  }\n  return byteArray\n}\n\nfunction utf16leToBytes (str, units) {\n  var c, hi, lo;\n  var byteArray = [];\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break\n\n    c = str.charCodeAt(i);\n    hi = c >> 8;\n    lo = c % 256;\n    byteArray.push(lo);\n    byteArray.push(hi);\n  }\n\n  return byteArray\n}\n\n\nfunction base64ToBytes (str) {\n  return toByteArray(base64clean(str))\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i];\n  }\n  return i\n}\n\nfunction isnan (val) {\n  return val !== val // eslint-disable-line no-self-compare\n}\n\n\n// the following is from is-buffer, also by Feross Aboukhadijeh and with same lisence\n// The _isBuffer check is for Safari 5-7 support, because it's missing\n// Object.prototype.constructor. Remove this eventually\nfunction isBuffer$1(obj) {\n  return obj != null && (!!obj._isBuffer || isFastBuffer(obj) || isSlowBuffer(obj))\n}\n\nfunction isFastBuffer (obj) {\n  return !!obj.constructor && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj)\n}\n\n// For Node v0.10 support. Remove this eventually.\nfunction isSlowBuffer (obj) {\n  return typeof obj.readFloatLE === 'function' && typeof obj.slice === 'function' && isFastBuffer(obj.slice(0, 0))\n}\n\nclass PNG {\n    data;\n    width;\n    height;\n    format;\n    constructor(data) {\n        const png = new _PNG(data);\n        this.data = data;\n        this.width = png.width;\n        this.height = png.height;\n        this.format = 'png';\n    }\n    static isValid(data) {\n        try {\n            return !!new PNG(data);\n        }\n        catch {\n            return false;\n        }\n    }\n}\n\nclass JPEG {\n    data;\n    width;\n    height;\n    format;\n    constructor(data) {\n        this.data = data;\n        this.format = 'jpeg';\n        this.width = 0;\n        this.height = 0;\n        if (data.readUInt16BE(0) !== 0xffd8) {\n            throw new Error('SOI not found in JPEG');\n        }\n        const markers = _JPEG.decode(this.data);\n        let orientation;\n        for (let i = 0; i < markers.length; i += 1) {\n            const marker = markers[i];\n            if (marker.name === 'EXIF' && marker.entries.orientation) {\n                orientation = marker.entries.orientation;\n            }\n            if (marker.name === 'SOF') {\n                this.width ||= marker.width;\n                this.height ||= marker.height;\n            }\n        }\n        if (orientation > 4) {\n            [this.width, this.height] = [this.height, this.width];\n        }\n    }\n    static isValid(data) {\n        return data && Buffer.isBuffer(data) && data.readUInt16BE(0) === 0xffd8;\n    }\n}\n\nconst createCache = ({ limit = 100 } = {}) => {\n    let cache = {};\n    let keys = [];\n    return {\n        get: (key) => (key ? cache[key] : null),\n        set: (key, value) => {\n            keys.push(key);\n            if (keys.length > limit) {\n                delete cache[keys.shift()];\n            }\n            cache[key] = value;\n        },\n        reset: () => {\n            cache = {};\n            keys = [];\n        },\n        length: () => keys.length,\n    };\n};\n\nconst IMAGE_CACHE = createCache({ limit: 30 });\nconst isBuffer = Buffer.isBuffer;\nconst isBlob = (src) => {\n    return typeof Blob !== 'undefined' && src instanceof Blob;\n};\nconst isDataImageSrc = (src) => {\n    return 'data' in src;\n};\nconst isBase64Src = (imageSrc) => 'uri' in imageSrc &&\n    /^data:image\\/[a-zA-Z]*;base64,[^\"]*/g.test(imageSrc.uri);\nconst fetchRemoteFile = async (src) => {\n    const { method = 'GET', headers, body, credentials } = src;\n    const response = await fetch(src.uri, {\n        method,\n        headers,\n        body,\n        credentials,\n    });\n    const buffer = await response.arrayBuffer();\n    return Buffer.from(buffer);\n};\nconst isValidFormat = (format) => {\n    const lower = format.toLowerCase();\n    return lower === 'jpg' || lower === 'jpeg' || lower === 'png';\n};\nconst guessFormat = (buffer) => {\n    let format;\n    if (JPEG.isValid(buffer)) {\n        format = 'jpg';\n    }\n    else if (PNG.isValid(buffer)) {\n        format = 'png';\n    }\n    return format;\n};\nfunction getImage(body, format) {\n    switch (format.toLowerCase()) {\n        case 'jpg':\n        case 'jpeg':\n            return new JPEG(body);\n        case 'png':\n            return new PNG(body);\n        default:\n            return null;\n    }\n}\nconst resolveBase64Image = async ({ uri }) => {\n    const match = /^data:image\\/([a-zA-Z]*);base64,([^\"]*)/g.exec(uri);\n    if (!match)\n        throw new Error(`Invalid base64 image: ${uri}`);\n    const format = match[1];\n    const data = match[2];\n    if (!isValidFormat(format))\n        throw new Error(`Base64 image invalid format: ${format}`);\n    return getImage(Buffer.from(data, 'base64'), format);\n};\nconst resolveImageFromData = async (src) => {\n    if (src.data && src.format) {\n        return getImage(src.data, src.format);\n    }\n    throw new Error(`Invalid data given for local file: ${JSON.stringify(src)}`);\n};\nconst resolveBufferImage = async (buffer) => {\n    const format = guessFormat(buffer);\n    if (format) {\n        return getImage(buffer, format);\n    }\n    return null;\n};\nconst resolveBlobImage = async (blob) => {\n    const { type } = blob;\n    if (!type || type === 'application/octet-stream') {\n        const arrayBuffer = await blob.arrayBuffer();\n        const buffer = Buffer.from(arrayBuffer);\n        return resolveBufferImage(buffer);\n    }\n    if (!type.startsWith('image/')) {\n        throw new Error(`Invalid blob type: ${type}`);\n    }\n    const format = type.replace('image/', '');\n    if (!isValidFormat(format)) {\n        throw new Error(`Invalid blob type: ${type}`);\n    }\n    const buffer = await blob.arrayBuffer();\n    return getImage(Buffer.from(buffer), format);\n};\nconst getImageFormat = (body) => {\n    const isPng = body[0] === 137 &&\n        body[1] === 80 &&\n        body[2] === 78 &&\n        body[3] === 71 &&\n        body[4] === 13 &&\n        body[5] === 10 &&\n        body[6] === 26 &&\n        body[7] === 10;\n    const isJpg = body[0] === 255 && body[1] === 216 && body[2] === 255;\n    let extension = '';\n    if (isPng) {\n        extension = 'png';\n    }\n    else if (isJpg) {\n        extension = 'jpg';\n    }\n    else {\n        throw new Error('Not valid image extension');\n    }\n    return extension;\n};\nconst resolveImageFromUrl = async (src) => {\n    const data = await fetchRemoteFile(src);\n    const format = getImageFormat(data);\n    return getImage(data, format);\n};\nconst getCacheKey = (src) => {\n    if (isBlob(src) || isBuffer(src))\n        return null;\n    if (isDataImageSrc(src))\n        return src.data.toString();\n    return src.uri;\n};\nconst resolveImage = (src, { cache = true } = {}) => {\n    let image;\n    const cacheKey = getCacheKey(src);\n    if (isBlob(src)) {\n        image = resolveBlobImage(src);\n    }\n    else if (isBuffer(src)) {\n        image = resolveBufferImage(src);\n    }\n    else if (cache && IMAGE_CACHE.get(cacheKey)) {\n        return IMAGE_CACHE.get(cacheKey);\n    }\n    else if (isBase64Src(src)) {\n        image = resolveBase64Image(src);\n    }\n    else if (isDataImageSrc(src)) {\n        image = resolveImageFromData(src);\n    }\n    else {\n        image = resolveImageFromUrl(src);\n    }\n    if (!image) {\n        throw new Error('Cannot resolve image');\n    }\n    if (cache && cacheKey) {\n        IMAGE_CACHE.set(cacheKey, image);\n    }\n    return image;\n};\n\nexport { resolveImage as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,IAAI,WAAY,OAAO,WAAW,cAAc,SAC9C,OAAO,SAAS,cAAc,OAC9B,OAAO,WAAW,cAAc,SAAS,CAAC;AAE5C,IAAI,SAAS,EAAE;AACf,IAAI,YAAY,EAAE;AAClB,IAAI,MAAM,OAAO,eAAe,cAAc,aAAa;AAC3D,IAAI,SAAS;AACb,SAAS;IACP,SAAS;IACT,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,MAAM,KAAK,MAAM,EAAE,IAAI,KAAK,EAAE,EAAG;QAC/C,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QACnB,SAAS,CAAC,KAAK,UAAU,CAAC,GAAG,GAAG;IAClC;IAEA,SAAS,CAAC,IAAI,UAAU,CAAC,GAAG,GAAG;IAC/B,SAAS,CAAC,IAAI,UAAU,CAAC,GAAG,GAAG;AACjC;AAEA,SAAS,YAAa,GAAG;IACvB,IAAI,CAAC,QAAQ;QACX;IACF;IACA,IAAI,GAAG,GAAG,GAAG,KAAK,cAAc;IAChC,IAAI,MAAM,IAAI,MAAM;IAEpB,IAAI,MAAM,IAAI,GAAG;QACf,MAAM,IAAI,MAAM;IAClB;IAEA,4CAA4C;IAC5C,mEAAmE;IACnE,qBAAqB;IACrB,8EAA8E;IAC9E,oDAAoD;IACpD,eAAe,GAAG,CAAC,MAAM,EAAE,KAAK,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE,KAAK,MAAM,IAAI;IAErE,4DAA4D;IAC5D,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI;IAE5B,sEAAsE;IACtE,IAAI,eAAe,IAAI,MAAM,IAAI;IAEjC,IAAI,IAAI;IAER,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,EAAG;QACxC,MAAM,AAAC,SAAS,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,KAAO,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,KAAO,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,IAAK,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,GAAG;QAClK,GAAG,CAAC,IAAI,GAAG,AAAC,OAAO,KAAM;QACzB,GAAG,CAAC,IAAI,GAAG,AAAC,OAAO,IAAK;QACxB,GAAG,CAAC,IAAI,GAAG,MAAM;IACnB;IAEA,IAAI,iBAAiB,GAAG;QACtB,MAAM,AAAC,SAAS,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,IAAM,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI;QACjF,GAAG,CAAC,IAAI,GAAG,MAAM;IACnB,OAAO,IAAI,iBAAiB,GAAG;QAC7B,MAAM,AAAC,SAAS,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,KAAO,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,IAAM,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI;QAC5H,GAAG,CAAC,IAAI,GAAG,AAAC,OAAO,IAAK;QACxB,GAAG,CAAC,IAAI,GAAG,MAAM;IACnB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAiB,GAAG;IAC3B,OAAO,MAAM,CAAC,OAAO,KAAK,KAAK,GAAG,MAAM,CAAC,OAAO,KAAK,KAAK,GAAG,MAAM,CAAC,OAAO,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,KAAK;AAC3G;AAEA,SAAS,YAAa,KAAK,EAAE,KAAK,EAAE,GAAG;IACrC,IAAI;IACJ,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,IAAI,OAAO,IAAI,KAAK,KAAK,EAAG;QACnC,MAAM,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,IAAK,KAAK,CAAC,IAAI,EAAE;QAC5D,OAAO,IAAI,CAAC,gBAAgB;IAC9B;IACA,OAAO,OAAO,IAAI,CAAC;AACrB;AAEA,SAAS,cAAe,KAAK;IAC3B,IAAI,CAAC,QAAQ;QACX;IACF;IACA,IAAI;IACJ,IAAI,MAAM,MAAM,MAAM;IACtB,IAAI,aAAa,MAAM,GAAG,sCAAsC;IAChE,IAAI,SAAS;IACb,IAAI,QAAQ,EAAE;IACd,IAAI,iBAAiB,OAAO,wBAAwB;IAEpD,+EAA+E;IAC/E,IAAK,IAAI,IAAI,GAAG,OAAO,MAAM,YAAY,IAAI,MAAM,KAAK,eAAgB;QACtE,MAAM,IAAI,CAAC,YAAY,OAAO,GAAG,AAAC,IAAI,iBAAkB,OAAO,OAAQ,IAAI;IAC7E;IAEA,sEAAsE;IACtE,IAAI,eAAe,GAAG;QACpB,MAAM,KAAK,CAAC,MAAM,EAAE;QACpB,UAAU,MAAM,CAAC,OAAO,EAAE;QAC1B,UAAU,MAAM,CAAC,AAAC,OAAO,IAAK,KAAK;QACnC,UAAU;IACZ,OAAO,IAAI,eAAe,GAAG;QAC3B,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,IAAK,KAAK,CAAC,MAAM,EAAE;QAC7C,UAAU,MAAM,CAAC,OAAO,GAAG;QAC3B,UAAU,MAAM,CAAC,AAAC,OAAO,IAAK,KAAK;QACnC,UAAU,MAAM,CAAC,AAAC,OAAO,IAAK,KAAK;QACnC,UAAU;IACZ;IAEA,MAAM,IAAI,CAAC;IAEX,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA,SAAS,KAAM,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM;IAC/C,IAAI,GAAG;IACP,IAAI,OAAO,SAAS,IAAI,OAAO;IAC/B,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI;IACzB,IAAI,QAAQ,QAAQ;IACpB,IAAI,QAAQ,CAAC;IACb,IAAI,IAAI,OAAQ,SAAS,IAAK;IAC9B,IAAI,IAAI,OAAO,CAAC,IAAI;IACpB,IAAI,IAAI,MAAM,CAAC,SAAS,EAAE;IAE1B,KAAK;IAEL,IAAI,IAAK,CAAC,KAAM,CAAC,KAAM,IAAI;IAC3B,MAAO,CAAC;IACR,SAAS;IACT,MAAO,QAAQ,GAAG,IAAI,IAAI,MAAM,MAAM,CAAC,SAAS,EAAE,EAAE,KAAK,GAAG,SAAS,EAAG,CAAC;IAEzE,IAAI,IAAK,CAAC,KAAM,CAAC,KAAM,IAAI;IAC3B,MAAO,CAAC;IACR,SAAS;IACT,MAAO,QAAQ,GAAG,IAAI,IAAI,MAAM,MAAM,CAAC,SAAS,EAAE,EAAE,KAAK,GAAG,SAAS,EAAG,CAAC;IAEzE,IAAI,MAAM,GAAG;QACX,IAAI,IAAI;IACV,OAAO,IAAI,MAAM,MAAM;QACrB,OAAO,IAAI,MAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;IACnC,OAAO;QACL,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG;QACpB,IAAI,IAAI;IACV;IACA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI;AAC5C;AAEA,SAAS,MAAO,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM;IACvD,IAAI,GAAG,GAAG;IACV,IAAI,OAAO,SAAS,IAAI,OAAO;IAC/B,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI;IACzB,IAAI,QAAQ,QAAQ;IACpB,IAAI,KAAM,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM;IAC9D,IAAI,IAAI,OAAO,IAAK,SAAS;IAC7B,IAAI,IAAI,OAAO,IAAI,CAAC;IACpB,IAAI,IAAI,QAAQ,KAAM,UAAU,KAAK,IAAI,QAAQ,IAAK,IAAI;IAE1D,QAAQ,KAAK,GAAG,CAAC;IAEjB,IAAI,MAAM,UAAU,UAAU,UAAU;QACtC,IAAI,MAAM,SAAS,IAAI;QACvB,IAAI;IACN,OAAO;QACL,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG;QACzC,IAAI,QAAQ,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG;YACrC;YACA,KAAK;QACP;QACA,IAAI,IAAI,SAAS,GAAG;YAClB,SAAS,KAAK;QAChB,OAAO;YACL,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI;QAChC;QACA,IAAI,QAAQ,KAAK,GAAG;YAClB;YACA,KAAK;QACP;QAEA,IAAI,IAAI,SAAS,MAAM;YACrB,IAAI;YACJ,IAAI;QACN,OAAO,IAAI,IAAI,SAAS,GAAG;YACzB,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG;YAClC,IAAI,IAAI;QACV,OAAO;YACL,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,QAAQ,KAAK,KAAK,GAAG,CAAC,GAAG;YACjD,IAAI;QACN;IACF;IAEA,MAAO,QAAQ,GAAG,MAAM,CAAC,SAAS,EAAE,GAAG,IAAI,MAAM,KAAK,GAAG,KAAK,KAAK,QAAQ,EAAG,CAAC;IAE/E,IAAI,AAAC,KAAK,OAAQ;IAClB,QAAQ;IACR,MAAO,OAAO,GAAG,MAAM,CAAC,SAAS,EAAE,GAAG,IAAI,MAAM,KAAK,GAAG,KAAK,KAAK,QAAQ,EAAG,CAAC;IAE9E,MAAM,CAAC,SAAS,IAAI,EAAE,IAAI,IAAI;AAChC;AAEA,IAAI,WAAW,CAAC,EAAE,QAAQ;AAE1B,IAAI,UAAU,MAAM,OAAO,IAAI,SAAU,GAAG;IAC1C,OAAO,SAAS,IAAI,CAAC,QAAQ;AAC/B;AAEA;;;;;CAKC,GACD,2BAA2B,GAG3B,IAAI,oBAAoB;AAExB;;;;;;;;;;;;;;;;;;;;;;;CAuBC,GACD,OAAO,mBAAmB,GAAG,SAAS,mBAAmB,KAAK,YAC1D,SAAS,mBAAmB,GAC5B;AAEJ;;CAEC,GACD;AAEA,SAAS;IACP,OAAO,OAAO,mBAAmB,GAC7B,aACA;AACN;AAEA,SAAS,aAAc,IAAI,EAAE,MAAM;IACjC,IAAI,eAAe,QAAQ;QACzB,MAAM,IAAI,WAAW;IACvB;IACA,IAAI,OAAO,mBAAmB,EAAE;QAC9B,kEAAkE;QAClE,OAAO,IAAI,WAAW;QACtB,KAAK,SAAS,GAAG,OAAO,SAAS;IACnC,OAAO;QACL,0DAA0D;QAC1D,IAAI,SAAS,MAAM;YACjB,OAAO,IAAI,OAAO;QACpB;QACA,KAAK,MAAM,GAAG;IAChB;IAEA,OAAO;AACT;AAEA;;;;;;;;CAQC,GAED,SAAS,OAAQ,GAAG,EAAE,gBAAgB,EAAE,MAAM;IAC5C,IAAI,CAAC,OAAO,mBAAmB,IAAI,CAAC,CAAC,IAAI,YAAY,MAAM,GAAG;QAC5D,OAAO,IAAI,OAAO,KAAK,kBAAkB;IAC3C;IAEA,eAAe;IACf,IAAI,OAAO,QAAQ,UAAU;QAC3B,IAAI,OAAO,qBAAqB,UAAU;YACxC,MAAM,IAAI,MACR;QAEJ;QACA,OAAO,YAAY,IAAI,EAAE;IAC3B;IACA,OAAO,KAAK,IAAI,EAAE,KAAK,kBAAkB;AAC3C;AAEA,OAAO,QAAQ,GAAG,MAAM,kCAAkC;AAE1D,kEAAkE;AAClE,OAAO,QAAQ,GAAG,SAAU,GAAG;IAC7B,IAAI,SAAS,GAAG,OAAO,SAAS;IAChC,OAAO;AACT;AAEA,SAAS,KAAM,IAAI,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM;IAClD,IAAI,OAAO,UAAU,UAAU;QAC7B,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,OAAO,gBAAgB,eAAe,iBAAiB,aAAa;QACtE,OAAO,gBAAgB,MAAM,OAAO,kBAAkB;IACxD;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO,WAAW,MAAM,OAAO;IACjC;IAEA,OAAO,WAAW,MAAM;AAC1B;AAEA;;;;;;;EAOE,GACF,OAAO,IAAI,GAAG,SAAU,KAAK,EAAE,gBAAgB,EAAE,MAAM;IACrD,OAAO,KAAK,MAAM,OAAO,kBAAkB;AAC7C;AAEA,IAAI,OAAO,mBAAmB,EAAE;IAC9B,OAAO,SAAS,CAAC,SAAS,GAAG,WAAW,SAAS;IACjD,OAAO,SAAS,GAAG;IACnB,IAAI,OAAO,WAAW,eAAe,OAAO,OAAO,IAC/C,MAAM,CAAC,OAAO,OAAO,CAAC,KAAK;AACjC;AAEA,SAAS,WAAY,IAAI;IACvB,IAAI,OAAO,SAAS,UAAU;QAC5B,MAAM,IAAI,UAAU;IACtB,OAAO,IAAI,OAAO,GAAG;QACnB,MAAM,IAAI,WAAW;IACvB;AACF;AAEA,SAAS,MAAO,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ;IACxC,WAAW;IACX,IAAI,QAAQ,GAAG;QACb,OAAO,aAAa,MAAM;IAC5B;IACA,IAAI,SAAS,WAAW;QACtB,wDAAwD;QACxD,uDAAuD;QACvD,qCAAqC;QACrC,OAAO,OAAO,aAAa,WACvB,aAAa,MAAM,MAAM,IAAI,CAAC,MAAM,YACpC,aAAa,MAAM,MAAM,IAAI,CAAC;IACpC;IACA,OAAO,aAAa,MAAM;AAC5B;AAEA;;;EAGE,GACF,OAAO,KAAK,GAAG,SAAU,IAAI,EAAE,IAAI,EAAE,QAAQ;IAC3C,OAAO,MAAM,MAAM,MAAM,MAAM;AACjC;AAEA,SAAS,YAAa,IAAI,EAAE,IAAI;IAC9B,WAAW;IACX,OAAO,aAAa,MAAM,OAAO,IAAI,IAAI,QAAQ,QAAQ;IACzD,IAAI,CAAC,OAAO,mBAAmB,EAAE;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,EAAE,EAAG;YAC7B,IAAI,CAAC,EAAE,GAAG;QACZ;IACF;IACA,OAAO;AACT;AAEA;;GAEG,GACH,OAAO,WAAW,GAAG,SAAU,IAAI;IACjC,OAAO,YAAY,MAAM;AAC3B;AACA;;CAEC,GACD,OAAO,eAAe,GAAG,SAAU,IAAI;IACrC,OAAO,YAAY,MAAM;AAC3B;AAEA,SAAS,WAAY,IAAI,EAAE,MAAM,EAAE,QAAQ;IACzC,IAAI,OAAO,aAAa,YAAY,aAAa,IAAI;QACnD,WAAW;IACb;IAEA,IAAI,CAAC,OAAO,UAAU,CAAC,WAAW;QAChC,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,SAAS,WAAW,QAAQ,YAAY;IAC5C,OAAO,aAAa,MAAM;IAE1B,IAAI,SAAS,KAAK,KAAK,CAAC,QAAQ;IAEhC,IAAI,WAAW,QAAQ;QACrB,2EAA2E;QAC3E,0EAA0E;QAC1E,oCAAoC;QACpC,OAAO,KAAK,KAAK,CAAC,GAAG;IACvB;IAEA,OAAO;AACT;AAEA,SAAS,cAAe,IAAI,EAAE,KAAK;IACjC,IAAI,SAAS,MAAM,MAAM,GAAG,IAAI,IAAI,QAAQ,MAAM,MAAM,IAAI;IAC5D,OAAO,aAAa,MAAM;IAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,KAAK,EAAG;QAClC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG;IACvB;IACA,OAAO;AACT;AAEA,SAAS,gBAAiB,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM;IACvD,MAAM,UAAU,EAAE,oDAAoD;IAEtE,IAAI,aAAa,KAAK,MAAM,UAAU,GAAG,YAAY;QACnD,MAAM,IAAI,WAAW;IACvB;IAEA,IAAI,MAAM,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC,GAAG;QACjD,MAAM,IAAI,WAAW;IACvB;IAEA,IAAI,eAAe,aAAa,WAAW,WAAW;QACpD,QAAQ,IAAI,WAAW;IACzB,OAAO,IAAI,WAAW,WAAW;QAC/B,QAAQ,IAAI,WAAW,OAAO;IAChC,OAAO;QACL,QAAQ,IAAI,WAAW,OAAO,YAAY;IAC5C;IAEA,IAAI,OAAO,mBAAmB,EAAE;QAC9B,kEAAkE;QAClE,OAAO;QACP,KAAK,SAAS,GAAG,OAAO,SAAS;IACnC,OAAO;QACL,0DAA0D;QAC1D,OAAO,cAAc,MAAM;IAC7B;IACA,OAAO;AACT;AAEA,SAAS,WAAY,IAAI,EAAE,GAAG;IAC5B,IAAI,iBAAiB,MAAM;QACzB,IAAI,MAAM,QAAQ,IAAI,MAAM,IAAI;QAChC,OAAO,aAAa,MAAM;QAE1B,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,OAAO;QACT;QAEA,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG;QACrB,OAAO;IACT;IAEA,IAAI,KAAK;QACP,IAAI,AAAC,OAAO,gBAAgB,eACxB,IAAI,MAAM,YAAY,eAAgB,YAAY,KAAK;YACzD,IAAI,OAAO,IAAI,MAAM,KAAK,YAAY,MAAM,IAAI,MAAM,GAAG;gBACvD,OAAO,aAAa,MAAM;YAC5B;YACA,OAAO,cAAc,MAAM;QAC7B;QAEA,IAAI,IAAI,IAAI,KAAK,YAAY,QAAQ,IAAI,IAAI,GAAG;YAC9C,OAAO,cAAc,MAAM,IAAI,IAAI;QACrC;IACF;IAEA,MAAM,IAAI,UAAU;AACtB;AAEA,SAAS,QAAS,MAAM;IACtB,wEAAwE;IACxE,sDAAsD;IACtD,IAAI,UAAU,cAAc;QAC1B,MAAM,IAAI,WAAW,oDACA,aAAa,aAAa,QAAQ,CAAC,MAAM;IAChE;IACA,OAAO,SAAS;AAClB;AACA,OAAO,QAAQ,GAAG;AAClB,SAAS,iBAAkB,CAAC;IAC1B,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,SAAS;AACpC;AAEA,OAAO,OAAO,GAAG,SAAS,QAAS,CAAC,EAAE,CAAC;IACrC,IAAI,CAAC,iBAAiB,MAAM,CAAC,iBAAiB,IAAI;QAChD,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,MAAM,GAAG,OAAO;IAEpB,IAAI,IAAI,EAAE,MAAM;IAChB,IAAI,IAAI,EAAE,MAAM;IAEhB,IAAK,IAAI,IAAI,GAAG,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,IAAI,KAAK,EAAE,EAAG;QAClD,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE;YACjB,IAAI,CAAC,CAAC,EAAE;YACR,IAAI,CAAC,CAAC,EAAE;YACR;QACF;IACF;IAEA,IAAI,IAAI,GAAG,OAAO,CAAC;IACnB,IAAI,IAAI,GAAG,OAAO;IAClB,OAAO;AACT;AAEA,OAAO,UAAU,GAAG,SAAS,WAAY,QAAQ;IAC/C,OAAQ,OAAO,UAAU,WAAW;QAClC,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,OAAO,MAAM,GAAG,SAAS,OAAQ,IAAI,EAAE,MAAM;IAC3C,IAAI,CAAC,QAAQ,OAAO;QAClB,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,OAAO,OAAO,KAAK,CAAC;IACtB;IAEA,IAAI;IACJ,IAAI,WAAW,WAAW;QACxB,SAAS;QACT,IAAK,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;YAChC,UAAU,IAAI,CAAC,EAAE,CAAC,MAAM;QAC1B;IACF;IAEA,IAAI,SAAS,OAAO,WAAW,CAAC;IAChC,IAAI,MAAM;IACV,IAAK,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;QAChC,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,IAAI,CAAC,iBAAiB,MAAM;YAC1B,MAAM,IAAI,UAAU;QACtB;QACA,IAAI,IAAI,CAAC,QAAQ;QACjB,OAAO,IAAI,MAAM;IACnB;IACA,OAAO;AACT;AAEA,SAAS,WAAY,MAAM,EAAE,QAAQ;IACnC,IAAI,iBAAiB,SAAS;QAC5B,OAAO,OAAO,MAAM;IACtB;IACA,IAAI,OAAO,gBAAgB,eAAe,OAAO,YAAY,MAAM,KAAK,cACpE,CAAC,YAAY,MAAM,CAAC,WAAW,kBAAkB,WAAW,GAAG;QACjE,OAAO,OAAO,UAAU;IAC1B;IACA,IAAI,OAAO,WAAW,UAAU;QAC9B,SAAS,KAAK;IAChB;IAEA,IAAI,MAAM,OAAO,MAAM;IACvB,IAAI,QAAQ,GAAG,OAAO;IAEtB,oCAAoC;IACpC,IAAI,cAAc;IAClB,OAAS;QACP,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,YAAY,QAAQ,MAAM;YACnC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,MAAM;YACf,KAAK;gBACH,OAAO,QAAQ;YACjB,KAAK;gBACH,OAAO,cAAc,QAAQ,MAAM;YACrC;gBACE,IAAI,aAAa,OAAO,YAAY,QAAQ,MAAM,CAAC,cAAc;;gBACjE,WAAW,CAAC,KAAK,QAAQ,EAAE,WAAW;gBACtC,cAAc;QAClB;IACF;AACF;AACA,OAAO,UAAU,GAAG;AAEpB,SAAS,aAAc,QAAQ,EAAE,KAAK,EAAE,GAAG;IACzC,IAAI,cAAc;IAElB,4EAA4E;IAC5E,6BAA6B;IAE7B,2EAA2E;IAC3E,mEAAmE;IACnE,8DAA8D;IAC9D,kEAAkE;IAClE,IAAI,UAAU,aAAa,QAAQ,GAAG;QACpC,QAAQ;IACV;IACA,6EAA6E;IAC7E,uBAAuB;IACvB,IAAI,QAAQ,IAAI,CAAC,MAAM,EAAE;QACvB,OAAO;IACT;IAEA,IAAI,QAAQ,aAAa,MAAM,IAAI,CAAC,MAAM,EAAE;QAC1C,MAAM,IAAI,CAAC,MAAM;IACnB;IAEA,IAAI,OAAO,GAAG;QACZ,OAAO;IACT;IAEA,0EAA0E;IAC1E,SAAS;IACT,WAAW;IAEX,IAAI,OAAO,OAAO;QAChB,OAAO;IACT;IAEA,IAAI,CAAC,UAAU,WAAW;IAE1B,MAAO,KAAM;QACX,OAAQ;YACN,KAAK;gBACH,OAAO,SAAS,IAAI,EAAE,OAAO;YAE/B,KAAK;YACL,KAAK;gBACH,OAAO,UAAU,IAAI,EAAE,OAAO;YAEhC,KAAK;gBACH,OAAO,WAAW,IAAI,EAAE,OAAO;YAEjC,KAAK;YACL,KAAK;gBACH,OAAO,YAAY,IAAI,EAAE,OAAO;YAElC,KAAK;gBACH,OAAO,YAAY,IAAI,EAAE,OAAO;YAElC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,aAAa,IAAI,EAAE,OAAO;YAEnC;gBACE,IAAI,aAAa,MAAM,IAAI,UAAU,uBAAuB;gBAC5D,WAAW,CAAC,WAAW,EAAE,EAAE,WAAW;gBACtC,cAAc;QAClB;IACF;AACF;AAEA,sFAAsF;AACtF,oBAAoB;AACpB,OAAO,SAAS,CAAC,SAAS,GAAG;AAE7B,SAAS,KAAM,CAAC,EAAE,CAAC,EAAE,CAAC;IACpB,IAAI,IAAI,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,EAAE,GAAG;AACT;AAEA,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS;IACjC,IAAI,MAAM,IAAI,CAAC,MAAM;IACrB,IAAI,MAAM,MAAM,GAAG;QACjB,MAAM,IAAI,WAAW;IACvB;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;QAC/B,KAAK,IAAI,EAAE,GAAG,IAAI;IACpB;IACA,OAAO,IAAI;AACb;AAEA,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS;IACjC,IAAI,MAAM,IAAI,CAAC,MAAM;IACrB,IAAI,MAAM,MAAM,GAAG;QACjB,MAAM,IAAI,WAAW;IACvB;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;QAC/B,KAAK,IAAI,EAAE,GAAG,IAAI;QAClB,KAAK,IAAI,EAAE,IAAI,GAAG,IAAI;IACxB;IACA,OAAO,IAAI;AACb;AAEA,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS;IACjC,IAAI,MAAM,IAAI,CAAC,MAAM;IACrB,IAAI,MAAM,MAAM,GAAG;QACjB,MAAM,IAAI,WAAW;IACvB;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;QAC/B,KAAK,IAAI,EAAE,GAAG,IAAI;QAClB,KAAK,IAAI,EAAE,IAAI,GAAG,IAAI;QACtB,KAAK,IAAI,EAAE,IAAI,GAAG,IAAI;QACtB,KAAK,IAAI,EAAE,IAAI,GAAG,IAAI;IACxB;IACA,OAAO,IAAI;AACb;AAEA,OAAO,SAAS,CAAC,QAAQ,GAAG,SAAS;IACnC,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG;IAC3B,IAAI,WAAW,GAAG,OAAO;IACzB,IAAI,UAAU,MAAM,KAAK,GAAG,OAAO,UAAU,IAAI,EAAE,GAAG;IACtD,OAAO,aAAa,KAAK,CAAC,IAAI,EAAE;AAClC;AAEA,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS,OAAQ,CAAC;IAC1C,IAAI,CAAC,iBAAiB,IAAI,MAAM,IAAI,UAAU;IAC9C,IAAI,IAAI,KAAK,GAAG,OAAO;IACvB,OAAO,OAAO,OAAO,CAAC,IAAI,EAAE,OAAO;AACrC;AAEA,OAAO,SAAS,CAAC,OAAO,GAAG,SAAS;IAClC,IAAI,MAAM;IACV,IAAI,MAAM;IACV,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG;QACnB,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,KAAK,KAAK,CAAC,SAAS,IAAI,CAAC;QACvD,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,OAAO;IAChC;IACA,OAAO,aAAa,MAAM;AAC5B;AAEA,OAAO,SAAS,CAAC,OAAO,GAAG,SAAS,QAAS,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO;IACjF,IAAI,CAAC,iBAAiB,SAAS;QAC7B,MAAM,IAAI,UAAU;IACtB;IAEA,IAAI,UAAU,WAAW;QACvB,QAAQ;IACV;IACA,IAAI,QAAQ,WAAW;QACrB,MAAM,SAAS,OAAO,MAAM,GAAG;IACjC;IACA,IAAI,cAAc,WAAW;QAC3B,YAAY;IACd;IACA,IAAI,YAAY,WAAW;QACzB,UAAU,IAAI,CAAC,MAAM;IACvB;IAEA,IAAI,QAAQ,KAAK,MAAM,OAAO,MAAM,IAAI,YAAY,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE;QAC9E,MAAM,IAAI,WAAW;IACvB;IAEA,IAAI,aAAa,WAAW,SAAS,KAAK;QACxC,OAAO;IACT;IACA,IAAI,aAAa,SAAS;QACxB,OAAO,CAAC;IACV;IACA,IAAI,SAAS,KAAK;QAChB,OAAO;IACT;IAEA,WAAW;IACX,SAAS;IACT,eAAe;IACf,aAAa;IAEb,IAAI,IAAI,KAAK,QAAQ,OAAO;IAE5B,IAAI,IAAI,UAAU;IAClB,IAAI,IAAI,MAAM;IACd,IAAI,MAAM,KAAK,GAAG,CAAC,GAAG;IAEtB,IAAI,WAAW,IAAI,CAAC,KAAK,CAAC,WAAW;IACrC,IAAI,aAAa,OAAO,KAAK,CAAC,OAAO;IAErC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;QAC5B,IAAI,QAAQ,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE;YACjC,IAAI,QAAQ,CAAC,EAAE;YACf,IAAI,UAAU,CAAC,EAAE;YACjB;QACF;IACF;IAEA,IAAI,IAAI,GAAG,OAAO,CAAC;IACnB,IAAI,IAAI,GAAG,OAAO;IAClB,OAAO;AACT;AAEA,+EAA+E;AAC/E,oEAAoE;AACpE,EAAE;AACF,aAAa;AACb,gCAAgC;AAChC,sCAAsC;AACtC,qEAAqE;AACrE,iEAAiE;AACjE,kDAAkD;AAClD,SAAS,qBAAsB,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;IACnE,8BAA8B;IAC9B,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO,CAAC;IAEjC,uBAAuB;IACvB,IAAI,OAAO,eAAe,UAAU;QAClC,WAAW;QACX,aAAa;IACf,OAAO,IAAI,aAAa,YAAY;QAClC,aAAa;IACf,OAAO,IAAI,aAAa,CAAC,YAAY;QACnC,aAAa,CAAC;IAChB;IACA,aAAa,CAAC,YAAa,oBAAoB;IAC/C,IAAI,MAAM,aAAa;QACrB,4EAA4E;QAC5E,aAAa,MAAM,IAAK,OAAO,MAAM,GAAG;IAC1C;IAEA,0EAA0E;IAC1E,IAAI,aAAa,GAAG,aAAa,OAAO,MAAM,GAAG;IACjD,IAAI,cAAc,OAAO,MAAM,EAAE;QAC/B,IAAI,KAAK,OAAO,CAAC;aACZ,aAAa,OAAO,MAAM,GAAG;IACpC,OAAO,IAAI,aAAa,GAAG;QACzB,IAAI,KAAK,aAAa;aACjB,OAAO,CAAC;IACf;IAEA,gBAAgB;IAChB,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,OAAO,IAAI,CAAC,KAAK;IACzB;IAEA,iEAAiE;IACjE,IAAI,iBAAiB,MAAM;QACzB,6DAA6D;QAC7D,IAAI,IAAI,MAAM,KAAK,GAAG;YACpB,OAAO,CAAC;QACV;QACA,OAAO,aAAa,QAAQ,KAAK,YAAY,UAAU;IACzD,OAAO,IAAI,OAAO,QAAQ,UAAU;QAClC,MAAM,MAAM,MAAM,kCAAkC;QACpD,IAAI,OAAO,mBAAmB,IAC1B,OAAO,WAAW,SAAS,CAAC,OAAO,KAAK,YAAY;YACtD,IAAI,KAAK;gBACP,OAAO,WAAW,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,KAAK;YACxD,OAAO;gBACL,OAAO,WAAW,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,KAAK;YAC5D;QACF;QACA,OAAO,aAAa,QAAQ;YAAE;SAAK,EAAE,YAAY,UAAU;IAC7D;IAEA,MAAM,IAAI,UAAU;AACtB;AAEA,SAAS,aAAc,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;IACxD,IAAI,YAAY;IAChB,IAAI,YAAY,IAAI,MAAM;IAC1B,IAAI,YAAY,IAAI,MAAM;IAE1B,IAAI,aAAa,WAAW;QAC1B,WAAW,OAAO,UAAU,WAAW;QACvC,IAAI,aAAa,UAAU,aAAa,WACpC,aAAa,aAAa,aAAa,YAAY;YACrD,IAAI,IAAI,MAAM,GAAG,KAAK,IAAI,MAAM,GAAG,GAAG;gBACpC,OAAO,CAAC;YACV;YACA,YAAY;YACZ,aAAa;YACb,aAAa;YACb,cAAc;QAChB;IACF;IAEA,SAAS,KAAM,GAAG,EAAE,CAAC;QACnB,IAAI,cAAc,GAAG;YACnB,OAAO,GAAG,CAAC,EAAE;QACf,OAAO;YACL,OAAO,IAAI,YAAY,CAAC,IAAI;QAC9B;IACF;IAEA,IAAI;IACJ,IAAI,KAAK;QACP,IAAI,aAAa,CAAC;QAClB,IAAK,IAAI,YAAY,IAAI,WAAW,IAAK;YACvC,IAAI,KAAK,KAAK,OAAO,KAAK,KAAK,eAAe,CAAC,IAAI,IAAI,IAAI,aAAa;gBACtE,IAAI,eAAe,CAAC,GAAG,aAAa;gBACpC,IAAI,IAAI,aAAa,MAAM,WAAW,OAAO,aAAa;YAC5D,OAAO;gBACL,IAAI,eAAe,CAAC,GAAG,KAAK,IAAI;gBAChC,aAAa,CAAC;YAChB;QACF;IACF,OAAO;QACL,IAAI,aAAa,YAAY,WAAW,aAAa,YAAY;QACjE,IAAK,IAAI,YAAY,KAAK,GAAG,IAAK;YAChC,IAAI,QAAQ;YACZ,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;gBAClC,IAAI,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,IAAI;oBACrC,QAAQ;oBACR;gBACF;YACF;YACA,IAAI,OAAO,OAAO;QACpB;IACF;IAEA,OAAO,CAAC;AACV;AAEA,OAAO,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAU,GAAG,EAAE,UAAU,EAAE,QAAQ;IACtE,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,YAAY,cAAc,CAAC;AACtD;AAEA,OAAO,SAAS,CAAC,OAAO,GAAG,SAAS,QAAS,GAAG,EAAE,UAAU,EAAE,QAAQ;IACpE,OAAO,qBAAqB,IAAI,EAAE,KAAK,YAAY,UAAU;AAC/D;AAEA,OAAO,SAAS,CAAC,WAAW,GAAG,SAAS,YAAa,GAAG,EAAE,UAAU,EAAE,QAAQ;IAC5E,OAAO,qBAAqB,IAAI,EAAE,KAAK,YAAY,UAAU;AAC/D;AAEA,SAAS,SAAU,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAC5C,SAAS,OAAO,WAAW;IAC3B,IAAI,YAAY,IAAI,MAAM,GAAG;IAC7B,IAAI,CAAC,QAAQ;QACX,SAAS;IACX,OAAO;QACL,SAAS,OAAO;QAChB,IAAI,SAAS,WAAW;YACtB,SAAS;QACX;IACF;IAEA,mCAAmC;IACnC,IAAI,SAAS,OAAO,MAAM;IAC1B,IAAI,SAAS,MAAM,GAAG,MAAM,IAAI,UAAU;IAE1C,IAAI,SAAS,SAAS,GAAG;QACvB,SAAS,SAAS;IACpB;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,EAAE,EAAG;QAC/B,IAAI,SAAS,SAAS,OAAO,MAAM,CAAC,IAAI,GAAG,IAAI;QAC/C,IAAI,MAAM,SAAS,OAAO;QAC1B,GAAG,CAAC,SAAS,EAAE,GAAG;IACpB;IACA,OAAO;AACT;AAEA,SAAS,UAAW,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAC7C,OAAO,WAAW,YAAY,QAAQ,IAAI,MAAM,GAAG,SAAS,KAAK,QAAQ;AAC3E;AAEA,SAAS,WAAY,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAC9C,OAAO,WAAW,aAAa,SAAS,KAAK,QAAQ;AACvD;AAEA,SAAS,YAAa,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAC/C,OAAO,WAAW,KAAK,QAAQ,QAAQ;AACzC;AAEA,SAAS,YAAa,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAC/C,OAAO,WAAW,cAAc,SAAS,KAAK,QAAQ;AACxD;AAEA,SAAS,UAAW,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAC7C,OAAO,WAAW,eAAe,QAAQ,IAAI,MAAM,GAAG,SAAS,KAAK,QAAQ;AAC9E;AAEA,OAAO,SAAS,CAAC,KAAK,GAAG,SAAS,MAAO,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ;IACvE,uBAAuB;IACvB,IAAI,WAAW,WAAW;QACxB,WAAW;QACX,SAAS,IAAI,CAAC,MAAM;QACpB,SAAS;IACX,iCAAiC;IACjC,OAAO,IAAI,WAAW,aAAa,OAAO,WAAW,UAAU;QAC7D,WAAW;QACX,SAAS,IAAI,CAAC,MAAM;QACpB,SAAS;IACX,qDAAqD;IACrD,OAAO,IAAI,SAAS,SAAS;QAC3B,SAAS,SAAS;QAClB,IAAI,SAAS,SAAS;YACpB,SAAS,SAAS;YAClB,IAAI,aAAa,WAAW,WAAW;QACzC,OAAO;YACL,WAAW;YACX,SAAS;QACX;IACF,mEAAmE;IACnE,OAAO;QACL,MAAM,IAAI,MACR;IAEJ;IAEA,IAAI,YAAY,IAAI,CAAC,MAAM,GAAG;IAC9B,IAAI,WAAW,aAAa,SAAS,WAAW,SAAS;IAEzD,IAAI,AAAC,OAAO,MAAM,GAAG,KAAK,CAAC,SAAS,KAAK,SAAS,CAAC,KAAM,SAAS,IAAI,CAAC,MAAM,EAAE;QAC7E,MAAM,IAAI,WAAW;IACvB;IAEA,IAAI,CAAC,UAAU,WAAW;IAE1B,IAAI,cAAc;IAClB,OAAS;QACP,OAAQ;YACN,KAAK;gBACH,OAAO,SAAS,IAAI,EAAE,QAAQ,QAAQ;YAExC,KAAK;YACL,KAAK;gBACH,OAAO,UAAU,IAAI,EAAE,QAAQ,QAAQ;YAEzC,KAAK;gBACH,OAAO,WAAW,IAAI,EAAE,QAAQ,QAAQ;YAE1C,KAAK;YACL,KAAK;gBACH,OAAO,YAAY,IAAI,EAAE,QAAQ,QAAQ;YAE3C,KAAK;gBACH,2DAA2D;gBAC3D,OAAO,YAAY,IAAI,EAAE,QAAQ,QAAQ;YAE3C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,UAAU,IAAI,EAAE,QAAQ,QAAQ;YAEzC;gBACE,IAAI,aAAa,MAAM,IAAI,UAAU,uBAAuB;gBAC5D,WAAW,CAAC,KAAK,QAAQ,EAAE,WAAW;gBACtC,cAAc;QAClB;IACF;AACF;AAEA,OAAO,SAAS,CAAC,MAAM,GAAG,SAAS;IACjC,OAAO;QACL,MAAM;QACN,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;IACtD;AACF;AAEA,SAAS,YAAa,GAAG,EAAE,KAAK,EAAE,GAAG;IACnC,IAAI,UAAU,KAAK,QAAQ,IAAI,MAAM,EAAE;QACrC,OAAO,cAAc;IACvB,OAAO;QACL,OAAO,cAAc,IAAI,KAAK,CAAC,OAAO;IACxC;AACF;AAEA,SAAS,UAAW,GAAG,EAAE,KAAK,EAAE,GAAG;IACjC,MAAM,KAAK,GAAG,CAAC,IAAI,MAAM,EAAE;IAC3B,IAAI,MAAM,EAAE;IAEZ,IAAI,IAAI;IACR,MAAO,IAAI,IAAK;QACd,IAAI,YAAY,GAAG,CAAC,EAAE;QACtB,IAAI,YAAY;QAChB,IAAI,mBAAmB,AAAC,YAAY,OAAQ,IACxC,AAAC,YAAY,OAAQ,IACrB,AAAC,YAAY,OAAQ,IACrB;QAEJ,IAAI,IAAI,oBAAoB,KAAK;YAC/B,IAAI,YAAY,WAAW,YAAY;YAEvC,OAAQ;gBACN,KAAK;oBACH,IAAI,YAAY,MAAM;wBACpB,YAAY;oBACd;oBACA;gBACF,KAAK;oBACH,aAAa,GAAG,CAAC,IAAI,EAAE;oBACvB,IAAI,CAAC,aAAa,IAAI,MAAM,MAAM;wBAChC,gBAAgB,CAAC,YAAY,IAAI,KAAK,MAAO,aAAa;wBAC1D,IAAI,gBAAgB,MAAM;4BACxB,YAAY;wBACd;oBACF;oBACA;gBACF,KAAK;oBACH,aAAa,GAAG,CAAC,IAAI,EAAE;oBACvB,YAAY,GAAG,CAAC,IAAI,EAAE;oBACtB,IAAI,CAAC,aAAa,IAAI,MAAM,QAAQ,CAAC,YAAY,IAAI,MAAM,MAAM;wBAC/D,gBAAgB,CAAC,YAAY,GAAG,KAAK,MAAM,CAAC,aAAa,IAAI,KAAK,MAAO,YAAY;wBACrF,IAAI,gBAAgB,SAAS,CAAC,gBAAgB,UAAU,gBAAgB,MAAM,GAAG;4BAC/E,YAAY;wBACd;oBACF;oBACA;gBACF,KAAK;oBACH,aAAa,GAAG,CAAC,IAAI,EAAE;oBACvB,YAAY,GAAG,CAAC,IAAI,EAAE;oBACtB,aAAa,GAAG,CAAC,IAAI,EAAE;oBACvB,IAAI,CAAC,aAAa,IAAI,MAAM,QAAQ,CAAC,YAAY,IAAI,MAAM,QAAQ,CAAC,aAAa,IAAI,MAAM,MAAM;wBAC/F,gBAAgB,CAAC,YAAY,GAAG,KAAK,OAAO,CAAC,aAAa,IAAI,KAAK,MAAM,CAAC,YAAY,IAAI,KAAK,MAAO,aAAa;wBACnH,IAAI,gBAAgB,UAAU,gBAAgB,UAAU;4BACtD,YAAY;wBACd;oBACF;YACJ;QACF;QAEA,IAAI,cAAc,MAAM;YACtB,oDAAoD;YACpD,oDAAoD;YACpD,YAAY;YACZ,mBAAmB;QACrB,OAAO,IAAI,YAAY,QAAQ;YAC7B,yCAAyC;YACzC,aAAa;YACb,IAAI,IAAI,CAAC,cAAc,KAAK,QAAQ;YACpC,YAAY,SAAS,YAAY;QACnC;QAEA,IAAI,IAAI,CAAC;QACT,KAAK;IACP;IAEA,OAAO,sBAAsB;AAC/B;AAEA,wEAAwE;AACxE,iDAAiD;AACjD,qCAAqC;AACrC,IAAI,uBAAuB;AAE3B,SAAS,sBAAuB,UAAU;IACxC,IAAI,MAAM,WAAW,MAAM;IAC3B,IAAI,OAAO,sBAAsB;QAC/B,OAAO,OAAO,YAAY,CAAC,KAAK,CAAC,QAAQ,YAAY,sBAAsB;;IAC7E;IAEA,wDAAwD;IACxD,IAAI,MAAM;IACV,IAAI,IAAI;IACR,MAAO,IAAI,IAAK;QACd,OAAO,OAAO,YAAY,CAAC,KAAK,CAC9B,QACA,WAAW,KAAK,CAAC,GAAG,KAAK;IAE7B;IACA,OAAO;AACT;AAEA,SAAS,WAAY,GAAG,EAAE,KAAK,EAAE,GAAG;IAClC,IAAI,MAAM;IACV,MAAM,KAAK,GAAG,CAAC,IAAI,MAAM,EAAE;IAE3B,IAAK,IAAI,IAAI,OAAO,IAAI,KAAK,EAAE,EAAG;QAChC,OAAO,OAAO,YAAY,CAAC,GAAG,CAAC,EAAE,GAAG;IACtC;IACA,OAAO;AACT;AAEA,SAAS,YAAa,GAAG,EAAE,KAAK,EAAE,GAAG;IACnC,IAAI,MAAM;IACV,MAAM,KAAK,GAAG,CAAC,IAAI,MAAM,EAAE;IAE3B,IAAK,IAAI,IAAI,OAAO,IAAI,KAAK,EAAE,EAAG;QAChC,OAAO,OAAO,YAAY,CAAC,GAAG,CAAC,EAAE;IACnC;IACA,OAAO;AACT;AAEA,SAAS,SAAU,GAAG,EAAE,KAAK,EAAE,GAAG;IAChC,IAAI,MAAM,IAAI,MAAM;IAEpB,IAAI,CAAC,SAAS,QAAQ,GAAG,QAAQ;IACjC,IAAI,CAAC,OAAO,MAAM,KAAK,MAAM,KAAK,MAAM;IAExC,IAAI,MAAM;IACV,IAAK,IAAI,IAAI,OAAO,IAAI,KAAK,EAAE,EAAG;QAChC,OAAO,MAAM,GAAG,CAAC,EAAE;IACrB;IACA,OAAO;AACT;AAEA,SAAS,aAAc,GAAG,EAAE,KAAK,EAAE,GAAG;IACpC,IAAI,QAAQ,IAAI,KAAK,CAAC,OAAO;IAC7B,IAAI,MAAM;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;QACxC,OAAO,OAAO,YAAY,CAAC,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG;IACvD;IACA,OAAO;AACT;AAEA,OAAO,SAAS,CAAC,KAAK,GAAG,SAAS,MAAO,KAAK,EAAE,GAAG;IACjD,IAAI,MAAM,IAAI,CAAC,MAAM;IACrB,QAAQ,CAAC,CAAC;IACV,MAAM,QAAQ,YAAY,MAAM,CAAC,CAAC;IAElC,IAAI,QAAQ,GAAG;QACb,SAAS;QACT,IAAI,QAAQ,GAAG,QAAQ;IACzB,OAAO,IAAI,QAAQ,KAAK;QACtB,QAAQ;IACV;IAEA,IAAI,MAAM,GAAG;QACX,OAAO;QACP,IAAI,MAAM,GAAG,MAAM;IACrB,OAAO,IAAI,MAAM,KAAK;QACpB,MAAM;IACR;IAEA,IAAI,MAAM,OAAO,MAAM;IAEvB,IAAI;IACJ,IAAI,OAAO,mBAAmB,EAAE;QAC9B,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO;QAC9B,OAAO,SAAS,GAAG,OAAO,SAAS;IACrC,OAAO;QACL,IAAI,WAAW,MAAM;QACrB,SAAS,IAAI,OAAO,UAAU;QAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,EAAE,EAAG;YACjC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,MAAM;QAC7B;IACF;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,YAAa,MAAM,EAAE,GAAG,EAAE,MAAM;IACvC,IAAI,AAAC,SAAS,MAAO,KAAK,SAAS,GAAG,MAAM,IAAI,WAAW;IAC3D,IAAI,SAAS,MAAM,QAAQ,MAAM,IAAI,WAAW;AAClD;AAEA,OAAO,SAAS,CAAC,UAAU,GAAG,SAAS,WAAY,MAAM,EAAE,UAAU,EAAE,QAAQ;IAC7E,SAAS,SAAS;IAClB,aAAa,aAAa;IAC1B,IAAI,CAAC,UAAU,YAAY,QAAQ,YAAY,IAAI,CAAC,MAAM;IAE1D,IAAI,MAAM,IAAI,CAAC,OAAO;IACtB,IAAI,MAAM;IACV,IAAI,IAAI;IACR,MAAO,EAAE,IAAI,cAAc,CAAC,OAAO,KAAK,EAAG;QACzC,OAAO,IAAI,CAAC,SAAS,EAAE,GAAG;IAC5B;IAEA,OAAO;AACT;AAEA,OAAO,SAAS,CAAC,UAAU,GAAG,SAAS,WAAY,MAAM,EAAE,UAAU,EAAE,QAAQ;IAC7E,SAAS,SAAS;IAClB,aAAa,aAAa;IAC1B,IAAI,CAAC,UAAU;QACb,YAAY,QAAQ,YAAY,IAAI,CAAC,MAAM;IAC7C;IAEA,IAAI,MAAM,IAAI,CAAC,SAAS,EAAE,WAAW;IACrC,IAAI,MAAM;IACV,MAAO,aAAa,KAAK,CAAC,OAAO,KAAK,EAAG;QACvC,OAAO,IAAI,CAAC,SAAS,EAAE,WAAW,GAAG;IACvC;IAEA,OAAO;AACT;AAEA,OAAO,SAAS,CAAC,SAAS,GAAG,SAAS,UAAW,MAAM,EAAE,QAAQ;IAC/D,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IACjD,OAAO,IAAI,CAAC,OAAO;AACrB;AAEA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS,aAAc,MAAM,EAAE,QAAQ;IACrE,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IACjD,OAAO,IAAI,CAAC,OAAO,GAAI,IAAI,CAAC,SAAS,EAAE,IAAI;AAC7C;AAEA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS,aAAc,MAAM,EAAE,QAAQ;IACrE,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IACjD,OAAO,AAAC,IAAI,CAAC,OAAO,IAAI,IAAK,IAAI,CAAC,SAAS,EAAE;AAC/C;AAEA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS,aAAc,MAAM,EAAE,QAAQ;IACrE,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IAEjD,OAAO,CAAC,AAAC,IAAI,CAAC,OAAO,GAChB,IAAI,CAAC,SAAS,EAAE,IAAI,IACpB,IAAI,CAAC,SAAS,EAAE,IAAI,EAAG,IACvB,IAAI,CAAC,SAAS,EAAE,GAAG;AAC1B;AAEA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS,aAAc,MAAM,EAAE,QAAQ;IACrE,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IAEjD,OAAO,AAAC,IAAI,CAAC,OAAO,GAAG,YACrB,CAAC,AAAC,IAAI,CAAC,SAAS,EAAE,IAAI,KACrB,IAAI,CAAC,SAAS,EAAE,IAAI,IACrB,IAAI,CAAC,SAAS,EAAE;AACpB;AAEA,OAAO,SAAS,CAAC,SAAS,GAAG,SAAS,UAAW,MAAM,EAAE,UAAU,EAAE,QAAQ;IAC3E,SAAS,SAAS;IAClB,aAAa,aAAa;IAC1B,IAAI,CAAC,UAAU,YAAY,QAAQ,YAAY,IAAI,CAAC,MAAM;IAE1D,IAAI,MAAM,IAAI,CAAC,OAAO;IACtB,IAAI,MAAM;IACV,IAAI,IAAI;IACR,MAAO,EAAE,IAAI,cAAc,CAAC,OAAO,KAAK,EAAG;QACzC,OAAO,IAAI,CAAC,SAAS,EAAE,GAAG;IAC5B;IACA,OAAO;IAEP,IAAI,OAAO,KAAK,OAAO,KAAK,GAAG,CAAC,GAAG,IAAI;IAEvC,OAAO;AACT;AAEA,OAAO,SAAS,CAAC,SAAS,GAAG,SAAS,UAAW,MAAM,EAAE,UAAU,EAAE,QAAQ;IAC3E,SAAS,SAAS;IAClB,aAAa,aAAa;IAC1B,IAAI,CAAC,UAAU,YAAY,QAAQ,YAAY,IAAI,CAAC,MAAM;IAE1D,IAAI,IAAI;IACR,IAAI,MAAM;IACV,IAAI,MAAM,IAAI,CAAC,SAAS,EAAE,EAAE;IAC5B,MAAO,IAAI,KAAK,CAAC,OAAO,KAAK,EAAG;QAC9B,OAAO,IAAI,CAAC,SAAS,EAAE,EAAE,GAAG;IAC9B;IACA,OAAO;IAEP,IAAI,OAAO,KAAK,OAAO,KAAK,GAAG,CAAC,GAAG,IAAI;IAEvC,OAAO;AACT;AAEA,OAAO,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAU,MAAM,EAAE,QAAQ;IAC7D,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IACjD,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,OAAQ,IAAI,CAAC,OAAO;IAChD,OAAQ,CAAC,OAAO,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC;AACvC;AAEA,OAAO,SAAS,CAAC,WAAW,GAAG,SAAS,YAAa,MAAM,EAAE,QAAQ;IACnE,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IACjD,IAAI,MAAM,IAAI,CAAC,OAAO,GAAI,IAAI,CAAC,SAAS,EAAE,IAAI;IAC9C,OAAO,AAAC,MAAM,SAAU,MAAM,aAAa;AAC7C;AAEA,OAAO,SAAS,CAAC,WAAW,GAAG,SAAS,YAAa,MAAM,EAAE,QAAQ;IACnE,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IACjD,IAAI,MAAM,IAAI,CAAC,SAAS,EAAE,GAAI,IAAI,CAAC,OAAO,IAAI;IAC9C,OAAO,AAAC,MAAM,SAAU,MAAM,aAAa;AAC7C;AAEA,OAAO,SAAS,CAAC,WAAW,GAAG,SAAS,YAAa,MAAM,EAAE,QAAQ;IACnE,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IAEjD,OAAO,AAAC,IAAI,CAAC,OAAO,GACjB,IAAI,CAAC,SAAS,EAAE,IAAI,IACpB,IAAI,CAAC,SAAS,EAAE,IAAI,KACpB,IAAI,CAAC,SAAS,EAAE,IAAI;AACzB;AAEA,OAAO,SAAS,CAAC,WAAW,GAAG,SAAS,YAAa,MAAM,EAAE,QAAQ;IACnE,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IAEjD,OAAO,AAAC,IAAI,CAAC,OAAO,IAAI,KACrB,IAAI,CAAC,SAAS,EAAE,IAAI,KACpB,IAAI,CAAC,SAAS,EAAE,IAAI,IACpB,IAAI,CAAC,SAAS,EAAE;AACrB;AAEA,OAAO,SAAS,CAAC,WAAW,GAAG,SAAS,YAAa,MAAM,EAAE,QAAQ;IACnE,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IACjD,OAAO,KAAK,IAAI,EAAE,QAAQ,MAAM,IAAI;AACtC;AAEA,OAAO,SAAS,CAAC,WAAW,GAAG,SAAS,YAAa,MAAM,EAAE,QAAQ;IACnE,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IACjD,OAAO,KAAK,IAAI,EAAE,QAAQ,OAAO,IAAI;AACvC;AAEA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS,aAAc,MAAM,EAAE,QAAQ;IACrE,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IACjD,OAAO,KAAK,IAAI,EAAE,QAAQ,MAAM,IAAI;AACtC;AAEA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS,aAAc,MAAM,EAAE,QAAQ;IACrE,IAAI,CAAC,UAAU,YAAY,QAAQ,GAAG,IAAI,CAAC,MAAM;IACjD,OAAO,KAAK,IAAI,EAAE,QAAQ,OAAO,IAAI;AACvC;AAEA,SAAS,SAAU,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IAClD,IAAI,CAAC,iBAAiB,MAAM,MAAM,IAAI,UAAU;IAChD,IAAI,QAAQ,OAAO,QAAQ,KAAK,MAAM,IAAI,WAAW;IACrD,IAAI,SAAS,MAAM,IAAI,MAAM,EAAE,MAAM,IAAI,WAAW;AACtD;AAEA,OAAO,SAAS,CAAC,WAAW,GAAG,SAAS,YAAa,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ;IACtF,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,aAAa,aAAa;IAC1B,IAAI,CAAC,UAAU;QACb,IAAI,WAAW,KAAK,GAAG,CAAC,GAAG,IAAI,cAAc;QAC7C,SAAS,IAAI,EAAE,OAAO,QAAQ,YAAY,UAAU;IACtD;IAEA,IAAI,MAAM;IACV,IAAI,IAAI;IACR,IAAI,CAAC,OAAO,GAAG,QAAQ;IACvB,MAAO,EAAE,IAAI,cAAc,CAAC,OAAO,KAAK,EAAG;QACzC,IAAI,CAAC,SAAS,EAAE,GAAG,AAAC,QAAQ,MAAO;IACrC;IAEA,OAAO,SAAS;AAClB;AAEA,OAAO,SAAS,CAAC,WAAW,GAAG,SAAS,YAAa,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ;IACtF,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,aAAa,aAAa;IAC1B,IAAI,CAAC,UAAU;QACb,IAAI,WAAW,KAAK,GAAG,CAAC,GAAG,IAAI,cAAc;QAC7C,SAAS,IAAI,EAAE,OAAO,QAAQ,YAAY,UAAU;IACtD;IAEA,IAAI,IAAI,aAAa;IACrB,IAAI,MAAM;IACV,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ;IAC3B,MAAO,EAAE,KAAK,KAAK,CAAC,OAAO,KAAK,EAAG;QACjC,IAAI,CAAC,SAAS,EAAE,GAAG,AAAC,QAAQ,MAAO;IACrC;IAEA,OAAO,SAAS;AAClB;AAEA,OAAO,SAAS,CAAC,UAAU,GAAG,SAAS,WAAY,KAAK,EAAE,MAAM,EAAE,QAAQ;IACxE,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,MAAM;IACtD,IAAI,CAAC,OAAO,mBAAmB,EAAE,QAAQ,KAAK,KAAK,CAAC;IACpD,IAAI,CAAC,OAAO,GAAI,QAAQ;IACxB,OAAO,SAAS;AAClB;AAEA,SAAS,kBAAmB,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY;IAC1D,IAAI,QAAQ,GAAG,QAAQ,SAAS,QAAQ;IACxC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,IAAI,MAAM,GAAG,QAAQ,IAAI,IAAI,GAAG,EAAE,EAAG;QAChE,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,QAAS,QAAS,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,CAAG,MACnE,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI;IACjC;AACF;AAEA,OAAO,SAAS,CAAC,aAAa,GAAG,SAAS,cAAe,KAAK,EAAE,MAAM,EAAE,QAAQ;IAC9E,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,QAAQ;IACxD,IAAI,OAAO,mBAAmB,EAAE;QAC9B,IAAI,CAAC,OAAO,GAAI,QAAQ;QACxB,IAAI,CAAC,SAAS,EAAE,GAAI,UAAU;IAChC,OAAO;QACL,kBAAkB,IAAI,EAAE,OAAO,QAAQ;IACzC;IACA,OAAO,SAAS;AAClB;AAEA,OAAO,SAAS,CAAC,aAAa,GAAG,SAAS,cAAe,KAAK,EAAE,MAAM,EAAE,QAAQ;IAC9E,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,QAAQ;IACxD,IAAI,OAAO,mBAAmB,EAAE;QAC9B,IAAI,CAAC,OAAO,GAAI,UAAU;QAC1B,IAAI,CAAC,SAAS,EAAE,GAAI,QAAQ;IAC9B,OAAO;QACL,kBAAkB,IAAI,EAAE,OAAO,QAAQ;IACzC;IACA,OAAO,SAAS;AAClB;AAEA,SAAS,kBAAmB,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY;IAC1D,IAAI,QAAQ,GAAG,QAAQ,aAAa,QAAQ;IAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,IAAI,MAAM,GAAG,QAAQ,IAAI,IAAI,GAAG,EAAE,EAAG;QAChE,GAAG,CAAC,SAAS,EAAE,GAAG,AAAC,UAAU,CAAC,eAAe,IAAI,IAAI,CAAC,IAAI,IAAK;IACjE;AACF;AAEA,OAAO,SAAS,CAAC,aAAa,GAAG,SAAS,cAAe,KAAK,EAAE,MAAM,EAAE,QAAQ;IAC9E,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,YAAY;IAC5D,IAAI,OAAO,mBAAmB,EAAE;QAC9B,IAAI,CAAC,SAAS,EAAE,GAAI,UAAU;QAC9B,IAAI,CAAC,SAAS,EAAE,GAAI,UAAU;QAC9B,IAAI,CAAC,SAAS,EAAE,GAAI,UAAU;QAC9B,IAAI,CAAC,OAAO,GAAI,QAAQ;IAC1B,OAAO;QACL,kBAAkB,IAAI,EAAE,OAAO,QAAQ;IACzC;IACA,OAAO,SAAS;AAClB;AAEA,OAAO,SAAS,CAAC,aAAa,GAAG,SAAS,cAAe,KAAK,EAAE,MAAM,EAAE,QAAQ;IAC9E,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,YAAY;IAC5D,IAAI,OAAO,mBAAmB,EAAE;QAC9B,IAAI,CAAC,OAAO,GAAI,UAAU;QAC1B,IAAI,CAAC,SAAS,EAAE,GAAI,UAAU;QAC9B,IAAI,CAAC,SAAS,EAAE,GAAI,UAAU;QAC9B,IAAI,CAAC,SAAS,EAAE,GAAI,QAAQ;IAC9B,OAAO;QACL,kBAAkB,IAAI,EAAE,OAAO,QAAQ;IACzC;IACA,OAAO,SAAS;AAClB;AAEA,OAAO,SAAS,CAAC,UAAU,GAAG,SAAS,WAAY,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ;IACpF,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,IAAI,CAAC,UAAU;QACb,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,IAAI,aAAa;QAEzC,SAAS,IAAI,EAAE,OAAO,QAAQ,YAAY,QAAQ,GAAG,CAAC;IACxD;IAEA,IAAI,IAAI;IACR,IAAI,MAAM;IACV,IAAI,MAAM;IACV,IAAI,CAAC,OAAO,GAAG,QAAQ;IACvB,MAAO,EAAE,IAAI,cAAc,CAAC,OAAO,KAAK,EAAG;QACzC,IAAI,QAAQ,KAAK,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,EAAE,KAAK,GAAG;YACxD,MAAM;QACR;QACA,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,AAAC,QAAQ,OAAQ,CAAC,IAAI,MAAM;IAClD;IAEA,OAAO,SAAS;AAClB;AAEA,OAAO,SAAS,CAAC,UAAU,GAAG,SAAS,WAAY,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ;IACpF,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,IAAI,CAAC,UAAU;QACb,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,IAAI,aAAa;QAEzC,SAAS,IAAI,EAAE,OAAO,QAAQ,YAAY,QAAQ,GAAG,CAAC;IACxD;IAEA,IAAI,IAAI,aAAa;IACrB,IAAI,MAAM;IACV,IAAI,MAAM;IACV,IAAI,CAAC,SAAS,EAAE,GAAG,QAAQ;IAC3B,MAAO,EAAE,KAAK,KAAK,CAAC,OAAO,KAAK,EAAG;QACjC,IAAI,QAAQ,KAAK,QAAQ,KAAK,IAAI,CAAC,SAAS,IAAI,EAAE,KAAK,GAAG;YACxD,MAAM;QACR;QACA,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,AAAC,QAAQ,OAAQ,CAAC,IAAI,MAAM;IAClD;IAEA,OAAO,SAAS;AAClB;AAEA,OAAO,SAAS,CAAC,SAAS,GAAG,SAAS,UAAW,KAAK,EAAE,MAAM,EAAE,QAAQ;IACtE,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,MAAM,CAAC;IACvD,IAAI,CAAC,OAAO,mBAAmB,EAAE,QAAQ,KAAK,KAAK,CAAC;IACpD,IAAI,QAAQ,GAAG,QAAQ,OAAO,QAAQ;IACtC,IAAI,CAAC,OAAO,GAAI,QAAQ;IACxB,OAAO,SAAS;AAClB;AAEA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS,aAAc,KAAK,EAAE,MAAM,EAAE,QAAQ;IAC5E,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,QAAQ,CAAC;IACzD,IAAI,OAAO,mBAAmB,EAAE;QAC9B,IAAI,CAAC,OAAO,GAAI,QAAQ;QACxB,IAAI,CAAC,SAAS,EAAE,GAAI,UAAU;IAChC,OAAO;QACL,kBAAkB,IAAI,EAAE,OAAO,QAAQ;IACzC;IACA,OAAO,SAAS;AAClB;AAEA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS,aAAc,KAAK,EAAE,MAAM,EAAE,QAAQ;IAC5E,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,QAAQ,CAAC;IACzD,IAAI,OAAO,mBAAmB,EAAE;QAC9B,IAAI,CAAC,OAAO,GAAI,UAAU;QAC1B,IAAI,CAAC,SAAS,EAAE,GAAI,QAAQ;IAC9B,OAAO;QACL,kBAAkB,IAAI,EAAE,OAAO,QAAQ;IACzC;IACA,OAAO,SAAS;AAClB;AAEA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS,aAAc,KAAK,EAAE,MAAM,EAAE,QAAQ;IAC5E,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,YAAY,CAAC;IAC7D,IAAI,OAAO,mBAAmB,EAAE;QAC9B,IAAI,CAAC,OAAO,GAAI,QAAQ;QACxB,IAAI,CAAC,SAAS,EAAE,GAAI,UAAU;QAC9B,IAAI,CAAC,SAAS,EAAE,GAAI,UAAU;QAC9B,IAAI,CAAC,SAAS,EAAE,GAAI,UAAU;IAChC,OAAO;QACL,kBAAkB,IAAI,EAAE,OAAO,QAAQ;IACzC;IACA,OAAO,SAAS;AAClB;AAEA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS,aAAc,KAAK,EAAE,MAAM,EAAE,QAAQ;IAC5E,QAAQ,CAAC;IACT,SAAS,SAAS;IAClB,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,OAAO,QAAQ,GAAG,YAAY,CAAC;IAC7D,IAAI,QAAQ,GAAG,QAAQ,aAAa,QAAQ;IAC5C,IAAI,OAAO,mBAAmB,EAAE;QAC9B,IAAI,CAAC,OAAO,GAAI,UAAU;QAC1B,IAAI,CAAC,SAAS,EAAE,GAAI,UAAU;QAC9B,IAAI,CAAC,SAAS,EAAE,GAAI,UAAU;QAC9B,IAAI,CAAC,SAAS,EAAE,GAAI,QAAQ;IAC9B,OAAO;QACL,kBAAkB,IAAI,EAAE,OAAO,QAAQ;IACzC;IACA,OAAO,SAAS;AAClB;AAEA,SAAS,aAAc,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACtD,IAAI,SAAS,MAAM,IAAI,MAAM,EAAE,MAAM,IAAI,WAAW;IACpD,IAAI,SAAS,GAAG,MAAM,IAAI,WAAW;AACvC;AAEA,SAAS,WAAY,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ;IAC7D,IAAI,CAAC,UAAU;QACb,aAAa,KAAK,OAAO,QAAQ;IACnC;IACA,MAAM,KAAK,OAAO,QAAQ,cAAc,IAAI;IAC5C,OAAO,SAAS;AAClB;AAEA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS,aAAc,KAAK,EAAE,MAAM,EAAE,QAAQ;IAC5E,OAAO,WAAW,IAAI,EAAE,OAAO,QAAQ,MAAM;AAC/C;AAEA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAS,aAAc,KAAK,EAAE,MAAM,EAAE,QAAQ;IAC5E,OAAO,WAAW,IAAI,EAAE,OAAO,QAAQ,OAAO;AAChD;AAEA,SAAS,YAAa,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ;IAC9D,IAAI,CAAC,UAAU;QACb,aAAa,KAAK,OAAO,QAAQ;IACnC;IACA,MAAM,KAAK,OAAO,QAAQ,cAAc,IAAI;IAC5C,OAAO,SAAS;AAClB;AAEA,OAAO,SAAS,CAAC,aAAa,GAAG,SAAS,cAAe,KAAK,EAAE,MAAM,EAAE,QAAQ;IAC9E,OAAO,YAAY,IAAI,EAAE,OAAO,QAAQ,MAAM;AAChD;AAEA,OAAO,SAAS,CAAC,aAAa,GAAG,SAAS,cAAe,KAAK,EAAE,MAAM,EAAE,QAAQ;IAC9E,OAAO,YAAY,IAAI,EAAE,OAAO,QAAQ,OAAO;AACjD;AAEA,4EAA4E;AAC5E,OAAO,SAAS,CAAC,IAAI,GAAG,SAAS,KAAM,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG;IACpE,IAAI,CAAC,OAAO,QAAQ;IACpB,IAAI,CAAC,OAAO,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM;IACxC,IAAI,eAAe,OAAO,MAAM,EAAE,cAAc,OAAO,MAAM;IAC7D,IAAI,CAAC,aAAa,cAAc;IAChC,IAAI,MAAM,KAAK,MAAM,OAAO,MAAM;IAElC,2BAA2B;IAC3B,IAAI,QAAQ,OAAO,OAAO;IAC1B,IAAI,OAAO,MAAM,KAAK,KAAK,IAAI,CAAC,MAAM,KAAK,GAAG,OAAO;IAErD,yBAAyB;IACzB,IAAI,cAAc,GAAG;QACnB,MAAM,IAAI,WAAW;IACvB;IACA,IAAI,QAAQ,KAAK,SAAS,IAAI,CAAC,MAAM,EAAE,MAAM,IAAI,WAAW;IAC5D,IAAI,MAAM,GAAG,MAAM,IAAI,WAAW;IAElC,cAAc;IACd,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,MAAM;IACxC,IAAI,OAAO,MAAM,GAAG,cAAc,MAAM,OAAO;QAC7C,MAAM,OAAO,MAAM,GAAG,cAAc;IACtC;IAEA,IAAI,MAAM,MAAM;IAChB,IAAI;IAEJ,IAAI,IAAI,KAAK,UAAU,QAAQ,eAAe,cAAc,KAAK;QAC/D,2BAA2B;QAC3B,IAAK,IAAI,MAAM,GAAG,KAAK,GAAG,EAAE,EAAG;YAC7B,MAAM,CAAC,IAAI,YAAY,GAAG,IAAI,CAAC,IAAI,MAAM;QAC3C;IACF,OAAO,IAAI,MAAM,QAAQ,CAAC,OAAO,mBAAmB,EAAE;QACpD,4BAA4B;QAC5B,IAAK,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;YACxB,MAAM,CAAC,IAAI,YAAY,GAAG,IAAI,CAAC,IAAI,MAAM;QAC3C;IACF,OAAO;QACL,WAAW,SAAS,CAAC,GAAG,CAAC,IAAI,CAC3B,QACA,IAAI,CAAC,QAAQ,CAAC,OAAO,QAAQ,MAC7B;IAEJ;IAEA,OAAO;AACT;AAEA,SAAS;AACT,0CAA0C;AAC1C,0CAA0C;AAC1C,sDAAsD;AACtD,OAAO,SAAS,CAAC,IAAI,GAAG,SAAS,KAAM,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ;IAC9D,uBAAuB;IACvB,IAAI,OAAO,QAAQ,UAAU;QAC3B,IAAI,OAAO,UAAU,UAAU;YAC7B,WAAW;YACX,QAAQ;YACR,MAAM,IAAI,CAAC,MAAM;QACnB,OAAO,IAAI,OAAO,QAAQ,UAAU;YAClC,WAAW;YACX,MAAM,IAAI,CAAC,MAAM;QACnB;QACA,IAAI,IAAI,MAAM,KAAK,GAAG;YACpB,IAAI,OAAO,IAAI,UAAU,CAAC;YAC1B,IAAI,OAAO,KAAK;gBACd,MAAM;YACR;QACF;QACA,IAAI,aAAa,aAAa,OAAO,aAAa,UAAU;YAC1D,MAAM,IAAI,UAAU;QACtB;QACA,IAAI,OAAO,aAAa,YAAY,CAAC,OAAO,UAAU,CAAC,WAAW;YAChE,MAAM,IAAI,UAAU,uBAAuB;QAC7C;IACF,OAAO,IAAI,OAAO,QAAQ,UAAU;QAClC,MAAM,MAAM;IACd;IAEA,qEAAqE;IACrE,IAAI,QAAQ,KAAK,IAAI,CAAC,MAAM,GAAG,SAAS,IAAI,CAAC,MAAM,GAAG,KAAK;QACzD,MAAM,IAAI,WAAW;IACvB;IAEA,IAAI,OAAO,OAAO;QAChB,OAAO,IAAI;IACb;IAEA,QAAQ,UAAU;IAClB,MAAM,QAAQ,YAAY,IAAI,CAAC,MAAM,GAAG,QAAQ;IAEhD,IAAI,CAAC,KAAK,MAAM;IAEhB,IAAI;IACJ,IAAI,OAAO,QAAQ,UAAU;QAC3B,IAAK,IAAI,OAAO,IAAI,KAAK,EAAE,EAAG;YAC5B,IAAI,CAAC,EAAE,GAAG;QACZ;IACF,OAAO;QACL,IAAI,QAAQ,iBAAiB,OACzB,MACA,YAAY,IAAI,OAAO,KAAK,UAAU,QAAQ;QAClD,IAAI,MAAM,MAAM,MAAM;QACtB,IAAK,IAAI,GAAG,IAAI,MAAM,OAAO,EAAE,EAAG;YAChC,IAAI,CAAC,IAAI,MAAM,GAAG,KAAK,CAAC,IAAI,IAAI;QAClC;IACF;IAEA,OAAO,IAAI;AACb;AAEA,mBAAmB;AACnB,mBAAmB;AAEnB,IAAI,oBAAoB;AAExB,SAAS,YAAa,GAAG;IACvB,wFAAwF;IACxF,MAAM,WAAW,KAAK,OAAO,CAAC,mBAAmB;IACjD,8CAA8C;IAC9C,IAAI,IAAI,MAAM,GAAG,GAAG,OAAO;IAC3B,uFAAuF;IACvF,MAAO,IAAI,MAAM,GAAG,MAAM,EAAG;QAC3B,MAAM,MAAM;IACd;IACA,OAAO;AACT;AAEA,SAAS,WAAY,GAAG;IACtB,IAAI,IAAI,IAAI,EAAE,OAAO,IAAI,IAAI;IAC7B,OAAO,IAAI,OAAO,CAAC,cAAc;AACnC;AAEA,SAAS,MAAO,CAAC;IACf,IAAI,IAAI,IAAI,OAAO,MAAM,EAAE,QAAQ,CAAC;IACpC,OAAO,EAAE,QAAQ,CAAC;AACpB;AAEA,SAAS,YAAa,MAAM,EAAE,KAAK;IACjC,QAAQ,SAAS;IACjB,IAAI;IACJ,IAAI,SAAS,OAAO,MAAM;IAC1B,IAAI,gBAAgB;IACpB,IAAI,QAAQ,EAAE;IAEd,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,EAAE,EAAG;QAC/B,YAAY,OAAO,UAAU,CAAC;QAE9B,yBAAyB;QACzB,IAAI,YAAY,UAAU,YAAY,QAAQ;YAC5C,uBAAuB;YACvB,IAAI,CAAC,eAAe;gBAClB,cAAc;gBACd,IAAI,YAAY,QAAQ;oBACtB,mBAAmB;oBACnB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,MAAM;oBAC9C;gBACF,OAAO,IAAI,IAAI,MAAM,QAAQ;oBAC3B,gBAAgB;oBAChB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,MAAM;oBAC9C;gBACF;gBAEA,aAAa;gBACb,gBAAgB;gBAEhB;YACF;YAEA,mBAAmB;YACnB,IAAI,YAAY,QAAQ;gBACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,MAAM;gBAC9C,gBAAgB;gBAChB;YACF;YAEA,uBAAuB;YACvB,YAAY,CAAC,gBAAgB,UAAU,KAAK,YAAY,MAAM,IAAI;QACpE,OAAO,IAAI,eAAe;YACxB,2CAA2C;YAC3C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,MAAM;QAChD;QAEA,gBAAgB;QAEhB,cAAc;QACd,IAAI,YAAY,MAAM;YACpB,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG;YACtB,MAAM,IAAI,CAAC;QACb,OAAO,IAAI,YAAY,OAAO;YAC5B,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG;YACtB,MAAM,IAAI,CACR,aAAa,MAAM,MACnB,YAAY,OAAO;QAEvB,OAAO,IAAI,YAAY,SAAS;YAC9B,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG;YACtB,MAAM,IAAI,CACR,aAAa,MAAM,MACnB,aAAa,MAAM,OAAO,MAC1B,YAAY,OAAO;QAEvB,OAAO,IAAI,YAAY,UAAU;YAC/B,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG;YACtB,MAAM,IAAI,CACR,aAAa,OAAO,MACpB,aAAa,MAAM,OAAO,MAC1B,aAAa,MAAM,OAAO,MAC1B,YAAY,OAAO;QAEvB,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAO;AACT;AAEA,SAAS,aAAc,GAAG;IACxB,IAAI,YAAY,EAAE;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,EAAE,EAAG;QACnC,sDAAsD;QACtD,UAAU,IAAI,CAAC,IAAI,UAAU,CAAC,KAAK;IACrC;IACA,OAAO;AACT;AAEA,SAAS,eAAgB,GAAG,EAAE,KAAK;IACjC,IAAI,GAAG,IAAI;IACX,IAAI,YAAY,EAAE;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,EAAE,EAAG;QACnC,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG;QAEtB,IAAI,IAAI,UAAU,CAAC;QACnB,KAAK,KAAK;QACV,KAAK,IAAI;QACT,UAAU,IAAI,CAAC;QACf,UAAU,IAAI,CAAC;IACjB;IAEA,OAAO;AACT;AAGA,SAAS,cAAe,GAAG;IACzB,OAAO,YAAY,YAAY;AACjC;AAEA,SAAS,WAAY,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM;IAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,EAAE,EAAG;QAC/B,IAAI,AAAC,IAAI,UAAU,IAAI,MAAM,IAAM,KAAK,IAAI,MAAM,EAAG;QACrD,GAAG,CAAC,IAAI,OAAO,GAAG,GAAG,CAAC,EAAE;IAC1B;IACA,OAAO;AACT;AAEA,SAAS,MAAO,GAAG;IACjB,OAAO,QAAQ,IAAI,sCAAsC;;AAC3D;AAGA,qFAAqF;AACrF,sEAAsE;AACtE,uDAAuD;AACvD,SAAS,WAAW,GAAG;IACrB,OAAO,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,SAAS,IAAI,aAAa,QAAQ,aAAa,IAAI;AAClF;AAEA,SAAS,aAAc,GAAG;IACxB,OAAO,CAAC,CAAC,IAAI,WAAW,IAAI,OAAO,IAAI,WAAW,CAAC,QAAQ,KAAK,cAAc,IAAI,WAAW,CAAC,QAAQ,CAAC;AACzG;AAEA,kDAAkD;AAClD,SAAS,aAAc,GAAG;IACxB,OAAO,OAAO,IAAI,WAAW,KAAK,cAAc,OAAO,IAAI,KAAK,KAAK,cAAc,aAAa,IAAI,KAAK,CAAC,GAAG;AAC/G;AAEA,MAAM;IACF,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,YAAY,IAAI,CAAE;QACd,MAAM,MAAM,IAAI,6KAAA,CAAA,UAAI,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM;QACxB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,OAAO,QAAQ,IAAI,EAAE;QACjB,IAAI;YACA,OAAO,CAAC,CAAC,IAAI,IAAI;QACrB,EACA,OAAM;YACF,OAAO;QACX;IACJ;AACJ;AAEA,MAAM;IACF,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,KAAK,YAAY,CAAC,OAAO,QAAQ;YACjC,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,UAAU,6IAAA,CAAA,UAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;QACtC,IAAI;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,KAAK,EAAG;YACxC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,IAAI,OAAO,IAAI,KAAK,UAAU,OAAO,OAAO,CAAC,WAAW,EAAE;gBACtD,cAAc,OAAO,OAAO,CAAC,WAAW;YAC5C;YACA,IAAI,OAAO,IAAI,KAAK,OAAO;gBACvB,IAAI,CAAC,KAAK,KAAK,OAAO,KAAK;gBAC3B,IAAI,CAAC,MAAM,KAAK,OAAO,MAAM;YACjC;QACJ;QACA,IAAI,cAAc,GAAG;YACjB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG;gBAAC,IAAI,CAAC,MAAM;gBAAE,IAAI,CAAC,KAAK;aAAC;QACzD;IACJ;IACA,OAAO,QAAQ,IAAI,EAAE;QACjB,OAAO,QAAQ,OAAO,QAAQ,CAAC,SAAS,KAAK,YAAY,CAAC,OAAO;IACrE;AACJ;AAEA,MAAM,cAAc,CAAC,EAAE,QAAQ,GAAG,EAAE,GAAG,CAAC,CAAC;IACrC,IAAI,QAAQ,CAAC;IACb,IAAI,OAAO,EAAE;IACb,OAAO;QACH,KAAK,CAAC,MAAS,MAAM,KAAK,CAAC,IAAI,GAAG;QAClC,KAAK,CAAC,KAAK;YACP,KAAK,IAAI,CAAC;YACV,IAAI,KAAK,MAAM,GAAG,OAAO;gBACrB,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG;YAC9B;YACA,KAAK,CAAC,IAAI,GAAG;QACjB;QACA,OAAO;YACH,QAAQ,CAAC;YACT,OAAO,EAAE;QACb;QACA,QAAQ,IAAM,KAAK,MAAM;IAC7B;AACJ;AAEA,MAAM,cAAc,YAAY;IAAE,OAAO;AAAG;AAC5C,MAAM,WAAW,OAAO,QAAQ;AAChC,MAAM,SAAS,CAAC;IACZ,OAAO,OAAO,SAAS,eAAe,eAAe;AACzD;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,UAAU;AACrB;AACA,MAAM,cAAc,CAAC,WAAa,SAAS,YACvC,uCAAuC,IAAI,CAAC,SAAS,GAAG;AAC5D,MAAM,kBAAkB,OAAO;IAC3B,MAAM,EAAE,SAAS,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG;IACvD,MAAM,WAAW,MAAM,MAAM,IAAI,GAAG,EAAE;QAClC;QACA;QACA;QACA;IACJ;IACA,MAAM,SAAS,MAAM,SAAS,WAAW;IACzC,OAAO,OAAO,IAAI,CAAC;AACvB;AACA,MAAM,gBAAgB,CAAC;IACnB,MAAM,QAAQ,OAAO,WAAW;IAChC,OAAO,UAAU,SAAS,UAAU,UAAU,UAAU;AAC5D;AACA,MAAM,cAAc,CAAC;IACjB,IAAI;IACJ,IAAI,KAAK,OAAO,CAAC,SAAS;QACtB,SAAS;IACb,OACK,IAAI,IAAI,OAAO,CAAC,SAAS;QAC1B,SAAS;IACb;IACA,OAAO;AACX;AACA,SAAS,SAAS,IAAI,EAAE,MAAM;IAC1B,OAAQ,OAAO,WAAW;QACtB,KAAK;QACL,KAAK;YACD,OAAO,IAAI,KAAK;QACpB,KAAK;YACD,OAAO,IAAI,IAAI;QACnB;YACI,OAAO;IACf;AACJ;AACA,MAAM,qBAAqB,OAAO,EAAE,GAAG,EAAE;IACrC,MAAM,QAAQ,2CAA2C,IAAI,CAAC;IAC9D,IAAI,CAAC,OACD,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,KAAK;IAClD,MAAM,SAAS,KAAK,CAAC,EAAE;IACvB,MAAM,OAAO,KAAK,CAAC,EAAE;IACrB,IAAI,CAAC,cAAc,SACf,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,QAAQ;IAC5D,OAAO,SAAS,OAAO,IAAI,CAAC,MAAM,WAAW;AACjD;AACA,MAAM,uBAAuB,OAAO;IAChC,IAAI,IAAI,IAAI,IAAI,IAAI,MAAM,EAAE;QACxB,OAAO,SAAS,IAAI,IAAI,EAAE,IAAI,MAAM;IACxC;IACA,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,KAAK,SAAS,CAAC,MAAM;AAC/E;AACA,MAAM,qBAAqB,OAAO;IAC9B,MAAM,SAAS,YAAY;IAC3B,IAAI,QAAQ;QACR,OAAO,SAAS,QAAQ;IAC5B;IACA,OAAO;AACX;AACA,MAAM,mBAAmB,OAAO;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,IAAI,CAAC,QAAQ,SAAS,4BAA4B;QAC9C,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,MAAM,SAAS,OAAO,IAAI,CAAC;QAC3B,OAAO,mBAAmB;IAC9B;IACA,IAAI,CAAC,KAAK,UAAU,CAAC,WAAW;QAC5B,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,MAAM;IAChD;IACA,MAAM,SAAS,KAAK,OAAO,CAAC,UAAU;IACtC,IAAI,CAAC,cAAc,SAAS;QACxB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,MAAM;IAChD;IACA,MAAM,SAAS,MAAM,KAAK,WAAW;IACrC,OAAO,SAAS,OAAO,IAAI,CAAC,SAAS;AACzC;AACA,MAAM,iBAAiB,CAAC;IACpB,MAAM,QAAQ,IAAI,CAAC,EAAE,KAAK,OACtB,IAAI,CAAC,EAAE,KAAK,MACZ,IAAI,CAAC,EAAE,KAAK,MACZ,IAAI,CAAC,EAAE,KAAK,MACZ,IAAI,CAAC,EAAE,KAAK,MACZ,IAAI,CAAC,EAAE,KAAK,MACZ,IAAI,CAAC,EAAE,KAAK,MACZ,IAAI,CAAC,EAAE,KAAK;IAChB,MAAM,QAAQ,IAAI,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK;IAChE,IAAI,YAAY;IAChB,IAAI,OAAO;QACP,YAAY;IAChB,OACK,IAAI,OAAO;QACZ,YAAY;IAChB,OACK;QACD,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX;AACA,MAAM,sBAAsB,OAAO;IAC/B,MAAM,OAAO,MAAM,gBAAgB;IACnC,MAAM,SAAS,eAAe;IAC9B,OAAO,SAAS,MAAM;AAC1B;AACA,MAAM,cAAc,CAAC;IACjB,IAAI,OAAO,QAAQ,SAAS,MACxB,OAAO;IACX,IAAI,eAAe,MACf,OAAO,IAAI,IAAI,CAAC,QAAQ;IAC5B,OAAO,IAAI,GAAG;AAClB;AACA,MAAM,eAAe,CAAC,KAAK,EAAE,QAAQ,IAAI,EAAE,GAAG,CAAC,CAAC;IAC5C,IAAI;IACJ,MAAM,WAAW,YAAY;IAC7B,IAAI,OAAO,MAAM;QACb,QAAQ,iBAAiB;IAC7B,OACK,IAAI,SAAS,MAAM;QACpB,QAAQ,mBAAmB;IAC/B,OACK,IAAI,SAAS,YAAY,GAAG,CAAC,WAAW;QACzC,OAAO,YAAY,GAAG,CAAC;IAC3B,OACK,IAAI,YAAY,MAAM;QACvB,QAAQ,mBAAmB;IAC/B,OACK,IAAI,eAAe,MAAM;QAC1B,QAAQ,qBAAqB;IACjC,OACK;QACD,QAAQ,oBAAoB;IAChC;IACA,IAAI,CAAC,OAAO;QACR,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,SAAS,UAAU;QACnB,YAAY,GAAG,CAAC,UAAU;IAC9B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8399, "column": 0}, "map": {"version": 3, "file": "react-pdf.browser.js", "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/renderer/src/utils.js", "file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/renderer/src/renderer.js", "file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/renderer/src/index.js", "file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/renderer/src/dom/usePDF.js", "file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/renderer/src/dom/PDFViewer.js", "file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/renderer/src/dom/BlobProvider.js", "file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/renderer/src/dom/PDFDownloadLink.js", "file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/node_modules/%40react-pdf/renderer/src/dom/index.js"], "sourcesContent": ["export const omitNils = (object) =>\n  Object.fromEntries(\n    Object.entries(object).filter(([, value]) => value !== undefined),\n  );\n", "import Reconciler from '@react-pdf/reconciler';\n\nconst createInstance = (type, { style, children, ...props }) => ({\n  type,\n  box: {},\n  style: style || {},\n  props: props || {},\n  children: [],\n});\n\nconst createTextInstance = (text) => ({ type: 'TEXT_INSTANCE', value: text });\n\nconst appendChild = (parent, child) => {\n  const isParentText =\n    parent.type === 'TEXT' ||\n    parent.type === 'LINK' ||\n    parent.type === 'TSPAN' ||\n    parent.type === 'NOTE';\n\n  const isChildTextInstance = child.type === 'TEXT_INSTANCE';\n  const isOrphanTextInstance = isChildTextInstance && !isParentText;\n\n  // Ignore orphan text instances.\n  // Caused by cases such as <>{name && <Text>{name}</Text>}</>\n  if (isOrphanTextInstance) {\n    console.warn(\n      `Invalid '${child.value}' string child outside <Text> component`,\n    );\n    return;\n  }\n\n  parent.children.push(child);\n};\n\nconst appendChildToContainer = (parentInstance, child) => {\n  if (parentInstance.type === 'ROOT') {\n    parentInstance.document = child;\n  } else {\n    appendChild(parentInstance, child);\n  }\n};\n\nconst insertBefore = (parentInstance, child, beforeChild) => {\n  const index = parentInstance.children?.indexOf(beforeChild);\n\n  if (index === undefined) return;\n\n  if (index !== -1 && child) parentInstance.children.splice(index, 0, child);\n};\n\nconst removeChild = (parentInstance, child) => {\n  const index = parentInstance.children?.indexOf(child);\n\n  if (index === undefined) return;\n\n  if (index !== -1) parentInstance.children.splice(index, 1);\n};\n\nconst removeChildFromContainer = (parentInstance, child) => {\n  const index = parentInstance.children?.indexOf(child);\n\n  if (index === undefined) return;\n\n  if (index !== -1) parentInstance.children.splice(index, 1);\n};\n\nconst commitTextUpdate = (textInstance, oldText, newText) => {\n  textInstance.value = newText;\n};\n\nconst commitUpdate = (instance, updatePayload, type, oldProps, newProps) => {\n  const { style, ...props } = newProps;\n  instance.props = props;\n  instance.style = style;\n};\n\nconst createRenderer = ({ onChange = () => {} }) =>\n  Reconciler({\n    appendChild,\n    appendChildToContainer,\n    commitTextUpdate,\n    commitUpdate,\n    createInstance,\n    createTextInstance,\n    insertBefore,\n    removeChild,\n    removeChildFromContainer,\n    resetAfterCommit: onChange,\n  });\n\nexport default createRenderer;\n", "import FontStore from '@react-pdf/font';\nimport renderPDF from '@react-pdf/render';\nimport PDFDocument from '@react-pdf/pdfkit';\nimport layoutDocument from '@react-pdf/layout';\nimport { upperFirst } from '@react-pdf/fns';\n\nimport { omitNils } from './utils';\nimport createRenderer from './renderer';\nimport packageJson from '../package.json';\n\nconst { version } = packageJson;\n\nconst fontStore = new FontStore();\n\n// We must keep a single renderer instance, otherwise <PERSON><PERSON> will complain\nlet renderer;\n\n// The pdf instance acts as an event emitter for DOM usage.\n// We only want to trigger an update when PDF content changes\nconst events = {};\n\nconst pdf = (initialValue) => {\n  const onChange = () => {\n    const listeners = events.change?.slice() || [];\n    for (let i = 0; i < listeners.length; i += 1) listeners[i]();\n  };\n\n  const container = { type: 'ROOT', document: null };\n  renderer = renderer || createRenderer({ onChange });\n  const mountNode = renderer.createContainer(container);\n\n  const updateContainer = (doc, callback) => {\n    renderer.updateContainer(doc, mountNode, null, callback);\n  };\n\n  if (initialValue) updateContainer(initialValue);\n\n  const render = async (compress = true) => {\n    const props = container.document.props || {};\n    const {\n      pdfVersion,\n      language,\n      pageLayout,\n      pageMode,\n      title,\n      author,\n      subject,\n      keyboards,\n      creator = 'react-pdf',\n      producer = 'react-pdf',\n      creationDate = new Date(),\n      modificationDate,\n    } = props;\n\n    const ctx = new PDFDocument({\n      compress,\n      pdfVersion,\n      lang: language,\n      displayTitle: true,\n      autoFirstPage: false,\n      info: omitNils({\n        Title: title,\n        Author: author,\n        Subject: subject,\n        Keywords: keyboards,\n        Creator: creator,\n        Producer: producer,\n        CreationDate: creationDate,\n        ModificationDate: modificationDate,\n      }),\n    });\n\n    if (pageLayout) {\n      ctx._root.data.PageLayout = upperFirst(pageLayout);\n    }\n\n    if (pageMode) {\n      ctx._root.data.PageMode = upperFirst(pageMode);\n    }\n\n    const layout = await layoutDocument(container.document, fontStore);\n    const fileStream = renderPDF(ctx, layout);\n    return { layout, fileStream };\n  };\n\n  const callOnRender = (params = {}) => {\n    if (container.document.props.onRender) {\n      container.document.props.onRender(params);\n    }\n  };\n\n  const toBlob = async () => {\n    const chunks = [];\n    const { layout: _INTERNAL__LAYOUT__DATA_, fileStream: instance } =\n      await render();\n\n    return new Promise((resolve, reject) => {\n      instance.on('data', (chunk) => {\n        chunks.push(\n          chunk instanceof Uint8Array ? chunk : new Uint8Array(chunk),\n        );\n      });\n\n      instance.on('end', () => {\n        try {\n          const blob = new Blob(chunks, { type: 'application/pdf' });\n          callOnRender({ blob, _INTERNAL__LAYOUT__DATA_ });\n          resolve(blob);\n        } catch (error) {\n          reject(error);\n        }\n      });\n    });\n  };\n\n  // TODO: rename this method to `toStream` in next major release, because it return stream not a buffer\n  const toBuffer = async () => {\n    const { layout: _INTERNAL__LAYOUT__DATA_, fileStream } = await render();\n    callOnRender({ _INTERNAL__LAYOUT__DATA_ });\n\n    return fileStream;\n  };\n\n  /*\n   * TODO: remove this method in next major release. it is buggy\n   * see\n   * - https://github.com/diegomura/react-pdf/issues/2112\n   * - https://github.com/diegomura/react-pdf/issues/2095\n   */\n  const toString = async () => {\n    if (process.env.NODE_ENV === 'development') {\n      console.warn(\n        '`toString` is deprecated and will be removed in next major release',\n      );\n    }\n\n    let result = '';\n    const { fileStream: instance } = await render(false); // For some reason, when rendering to string if compress=true the document is blank\n\n    return new Promise((resolve, reject) => {\n      try {\n        instance.on('data', (buffer) => {\n          result += buffer;\n        });\n\n        instance.on('end', () => {\n          callOnRender();\n          resolve(result);\n        });\n      } catch (error) {\n        reject(error);\n      }\n    });\n  };\n\n  const on = (event, listener) => {\n    if (!events[event]) events[event] = [];\n    events[event].push(listener);\n  };\n\n  const removeListener = (event, listener) => {\n    if (!events[event]) return;\n    const idx = events[event].indexOf(listener);\n    if (idx > -1) events[event].splice(idx, 1);\n  };\n\n  return {\n    on,\n    container,\n    toBlob,\n    toBuffer,\n    toString,\n    removeListener,\n    updateContainer,\n  };\n};\n\nconst Font = fontStore;\n\nconst StyleSheet = {\n  create: (s) => s,\n};\n\nexport { version, Font, StyleSheet, pdf, createRenderer };\n", "import queue from 'queue';\nimport { useState, useRef, useEffect, useCallback } from 'react';\n\nimport { pdf } from '../index';\n\n/**\n * PDF hook\n *\n * @param {Object} [options] hook options\n * @returns {[Object, Function]} pdf state and update function\n */\nexport const usePDF = ({ document } = {}) => {\n  const pdfInstance = useRef(null);\n\n  const [state, setState] = useState({\n    url: null,\n    blob: null,\n    error: null,\n    loading: !!document,\n  });\n\n  // Setup rendering queue\n  useEffect(() => {\n    const renderQueue = queue({ autostart: true, concurrency: 1 });\n\n    const queueDocumentRender = () => {\n      setState((prev) => ({ ...prev, loading: true }));\n\n      renderQueue.splice(0, renderQueue.length, () =>\n        state.error ? Promise.resolve() : pdfInstance.current.toBlob(),\n      );\n    };\n\n    const onRenderFailed = (error) => {\n      console.error(error);\n      setState((prev) => ({ ...prev, loading: false, error }));\n    };\n\n    const onRenderSuccessful = (blob) => {\n      setState({\n        blob,\n        error: null,\n        loading: false,\n        url: URL.createObjectURL(blob),\n      });\n    };\n\n    pdfInstance.current = pdf();\n    pdfInstance.current.on('change', queueDocumentRender);\n    if (document) {\n      pdfInstance.current.updateContainer(document);\n    }\n\n    renderQueue.on('error', onRenderFailed);\n    renderQueue.on('success', onRenderSuccessful);\n\n    return () => {\n      renderQueue.end();\n      pdfInstance.current.removeListener('change', queueDocumentRender);\n    };\n  }, []);\n\n  // Revoke old unused url instances\n  useEffect(() => {\n    return () => {\n      if (state.url) {\n        URL.revokeObjectURL(state.url);\n      }\n    };\n  }, [state.url]);\n\n  const update = useCallback((newDoc) => {\n    pdfInstance.current.updateContainer(newDoc);\n  }, []);\n\n  return [state, update];\n};\n\nexport default usePDF;\n", "import { useEffect } from 'react';\n\nimport usePDF from './usePDF';\n\nexport const PDFViewer = ({\n  title,\n  style,\n  className,\n  children,\n  innerRef,\n  showToolbar = true,\n  ...props\n}) => {\n  const [instance, updateInstance] = usePDF();\n\n  useEffect(() => updateInstance(children), [children]);\n\n  const src = instance.url\n    ? `${instance.url}#toolbar=${showToolbar ? 1 : 0}`\n    : null;\n\n  return (\n    <iframe\n      src={src}\n      title={title}\n      ref={innerRef}\n      style={style}\n      className={className}\n      {...props}\n    />\n  );\n};\n\nexport default PDFViewer;\n", "import { useEffect } from 'react';\n\nimport usePDF from './usePDF';\n\nexport const BlobProvider = ({ document: doc, children }) => {\n  const [instance, updateInstance] = usePDF();\n\n  useEffect(() => updateInstance(doc), [doc]);\n\n  if (!doc) {\n    console.warn('You should pass a valid document to BlobProvider');\n    return null;\n  }\n\n  return children(instance);\n};\n\nexport default BlobProvider;\n", "import { useEffect } from 'react';\n\nimport usePDF from './usePDF';\n\nexport const PDFDownloadLink = ({\n  fileName = 'document.pdf',\n  document: doc,\n  children,\n  onClick,\n  href,\n  ...rest\n}) => {\n  const [instance, updateInstance] = usePDF();\n\n  useEffect(() => updateInstance(doc), [doc]);\n\n  if (!doc) {\n    console.warn('You should pass a valid document to PDFDownloadLink');\n    return null;\n  }\n\n  const handleDownloadIE = () => {\n    if (instance && window.navigator.msSaveBlob) {\n      // IE\n      window.navigator.msSaveBlob(instance.blob, fileName);\n    }\n  };\n\n  const handleClick = (event) => {\n    handleDownloadIE();\n    if (typeof onClick === 'function') onClick(event, instance);\n  };\n\n  return (\n    <a href={instance.url} download={fileName} onClick={handleClick} {...rest}>\n      {typeof children === 'function' ? children(instance) : children}\n    </a>\n  );\n};\n\nexport default PDFDownloadLink;\n", "import * as primitives from '@react-pdf/primitives';\n\nimport usePDF from './usePDF';\nimport PDFViewer from './PDFViewer';\nimport BlobProvider from './BlobProvider';\nimport PDFDownloadLink from './PDFDownloadLink';\nimport { pdf, version, Font, StyleSheet } from '../index';\n\nconst throwEnvironmentError = (name) => {\n  throw new Error(\n    `${name} is a Node specific API. You're either using this method in a browser, or your bundler is not loading react-pdf from the appropriate web build.`,\n  );\n};\n\nexport const renderToStream = () => {\n  throwEnvironmentError('renderToStream');\n};\n\nexport const renderToBuffer = () => {\n  throwEnvironmentError('renderToBuffer');\n};\n\nexport const renderToString = () => {\n  throwEnvironmentError('renderToString');\n};\n\nexport const renderToFile = () => {\n  throwEnvironmentError('renderToFile');\n};\n\nexport const render = () => {\n  throwEnvironmentError('render');\n};\n\nexport * from '../index';\n\nexport * from './usePDF';\n\nexport * from './PDFViewer';\n\nexport * from './BlobProvider';\n\nexport * from './PDFDownloadLink';\n\nexport * from '@react-pdf/primitives';\n\n// TODO: remove this default export in next major release because it breaks tree-shacking\nexport default {\n  pdf,\n  usePDF,\n  Font,\n  version,\n  StyleSheet,\n  PDFViewer,\n  BlobProvider,\n  PDFDownloadLink,\n  renderToStream,\n  renderToString,\n  renderToFile,\n  render,\n  ...primitives,\n};\n"], "names": ["omitNils", "object", "Object", "fromEntries", "entries", "filter", "_ref", "value", "undefined", "createInstance", "type", "style", "children", "props", "box", "createTextInstance", "text", "append<PERSON><PERSON><PERSON>", "parent", "child", "isParentText", "isChildTextInstance", "isOrphanTextInstance", "console", "warn", "push", "append<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parentInstance", "document", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "_parentInstance$child", "index", "indexOf", "splice", "<PERSON><PERSON><PERSON><PERSON>", "_parentInstance$child2", "<PERSON><PERSON><PERSON><PERSON><PERSON>rom<PERSON><PERSON><PERSON>", "_parentInstance$child3", "commitTextUpdate", "textInstance", "oldText", "newText", "commitUpdate", "instance", "updatePayload", "oldProps", "newProps", "<PERSON><PERSON><PERSON><PERSON>", "_ref2", "onChange", "<PERSON><PERSON><PERSON><PERSON>", "resetAfterCommit", "version", "packageJson", "fontStore", "FontStore", "renderer", "events", "pdf", "initialValue", "_events$change", "listeners", "change", "slice", "i", "length", "container", "mountNode", "createContainer", "updateContainer", "doc", "callback", "render", "compress", "pdfVersion", "language", "pageLayout", "pageMode", "title", "author", "subject", "keyboards", "creator", "producer", "creationDate", "Date", "modificationDate", "ctx", "PDFDocument", "lang", "displayTitle", "autoFirstPage", "info", "Title", "Author", "Subject", "Keywords", "Creator", "Producer", "CreationDate", "ModificationDate", "_root", "data", "PageLayout", "upperFirst", "PageMode", "layout", "layoutDocument", "fileStream", "renderPDF", "callOnRender", "params", "onRender", "toBlob", "chunks", "_INTERNAL__LAYOUT__DATA_", "Promise", "resolve", "reject", "on", "chunk", "Uint8Array", "blob", "Blob", "error", "<PERSON><PERSON><PERSON><PERSON>", "toString", "process", "env", "NODE_ENV", "result", "buffer", "event", "listener", "removeListener", "idx", "Font", "StyleSheet", "create", "s", "usePDF", "_temp", "pdfInstance", "useRef", "state", "setState", "useState", "url", "loading", "useEffect", "renderQueue", "queue", "autostart", "concurrency", "queueDocumentRender", "prev", "current", "onRenderFailed", "onRenderSuccessful", "URL", "createObjectURL", "end", "revokeObjectURL", "update", "useCallback", "newDoc", "PDFViewer", "className", "innerRef", "showToolbar", "updateInstance", "src", "_jsx", "ref", "BlobProvider", "PDFDownloadLink", "fileName", "onClick", "href", "rest", "handleDownloadIE", "window", "navigator", "msSaveBlob", "handleClick", "download", "throwEnvironmentError", "name", "Error", "renderToStream", "renderToBuffer", "renderToString", "renderToFile", "primitives"], "mappings": ";;;;;;;;;;;;;;;;;AEkIQgI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;;;;;;;;;;;;;;;;;;;;;;AFlIvC,MAAMlI,QAAQ,IAAIC,MAAM,GAC7BC,MAAM,CAACC,WAAW,CAChBD,MAAM,CAACE,OAAO,CAACH,MAAM,CAAC,CAACI,MAAM,EAACC,IAAA,IAAA;QAAA,IAAC,GAAGC,KAAK,CAAC,GAAAD,IAAA;QAAA,OAAKC,KAAK,KAAKC,SAAS;IAAA,CAAA,CAClE,CAAC;ACDH,MAAMC,cAAc,GAAGA,CAACC,IAAI,EAAAJ,IAAA,KAAA;IAAA,IAAE,EAAEK,KAAK,EAAEC,QAAQ,EAAE,GAAGC,OAAO,GAAAP,IAAA;IAAA,OAAM;QAC/DI,IAAI;QACJI,GAAG,EAAE,CAAA,CAAE;QACPH,KAAK,EAAEA,KAAK,IAAI,CAAA,CAAE;QAClBE,KAAK,EAAEA,KAAK,IAAI,CAAA,CAAE;QAClBD,QAAQ,EAAE,EAAA;KACX;AAAA,CAAC;AAEF,MAAMG,kBAAkB,IAAIC,IAAI,GAAA,CAAM;QAAEN,IAAI,EAAE,eAAe;QAAEH,KAAK,EAAES;IAAK,CAAC,CAAC;AAE7E,MAAMC,WAAW,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;IACrC,MAAMC,YAAY,GAChBF,MAAM,CAACR,IAAI,KAAK,MAAM,IACtBQ,MAAM,CAACR,IAAI,KAAK,MAAM,IACtBQ,MAAM,CAACR,IAAI,KAAK,OAAO,IACvBQ,MAAM,CAACR,IAAI,KAAK,MAAM;IAExB,MAAMW,mBAAmB,GAAGF,KAAK,CAACT,IAAI,KAAK,eAAe;IAC1D,MAAMY,oBAAoB,GAAGD,mBAAmB,IAAI,CAACD,YAAY;IAEjE,gCAAA;IACA,6DAAA;IACA,IAAIE,oBAAoB,EAAE;QACxBC,OAAO,CAACC,IAAI,CACT,CAAA,SAAA,EAAWL,KAAK,CAACZ,KAAM,CAAA,uCAAA,CAC1B,CAAC;QACD;IACF;IAEAW,MAAM,CAACN,QAAQ,CAACa,IAAI,CAACN,KAAK,CAAC;AAC7B,CAAC;AAED,MAAMO,sBAAsB,GAAGA,CAACC,cAAc,EAAER,KAAK,KAAK;IACxD,IAAIQ,cAAc,CAACjB,IAAI,KAAK,MAAM,EAAE;QAClCiB,cAAc,CAACC,QAAQ,GAAGT,KAAK;IACjC,CAAC,MAAM;QACLF,WAAW,CAACU,cAAc,EAAER,KAAK,CAAC;IACpC;AACF,CAAC;AAED,MAAMU,YAAY,GAAGA,CAACF,cAAc,EAAER,KAAK,EAAEW,WAAW,KAAK;IAAA,IAAAC,qBAAA;IAC3D,MAAMC,KAAK,GAAA,CAAAD,qBAAA,GAAGJ,cAAc,CAACf,QAAQ,MAAAmB,IAAAA,IAAAA,qBAAA,KAAA,KAAA,IAAA,KAAA,IAAvBA,qBAAA,CAAyBE,OAAO,CAACH,WAAW,CAAC;IAE3D,IAAIE,KAAK,KAAKxB,SAAS,EAAE;IAEzB,IAAIwB,KAAK,KAAK,CAAA,CAAE,IAAIb,KAAK,EAAEQ,cAAc,CAACf,QAAQ,CAACsB,MAAM,CAACF,KAAK,EAAE,CAAC,EAAEb,KAAK,CAAC;AAC5E,CAAC;AAED,MAAMgB,WAAW,GAAGA,CAACR,cAAc,EAAER,KAAK,KAAK;IAAA,IAAAiB,sBAAA;IAC7C,MAAMJ,KAAK,GAAA,CAAAI,sBAAA,GAAGT,cAAc,CAACf,QAAQ,MAAAwB,IAAAA,IAAAA,sBAAA,KAAA,KAAA,IAAA,KAAA,IAAvBA,sBAAA,CAAyBH,OAAO,CAACd,KAAK,CAAC;IAErD,IAAIa,KAAK,KAAKxB,SAAS,EAAE;IAEzB,IAAIwB,KAAK,KAAK,CAAA,CAAE,EAAEL,cAAc,CAACf,QAAQ,CAACsB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMK,wBAAwB,GAAGA,CAACV,cAAc,EAAER,KAAK,KAAK;IAAA,IAAAmB,sBAAA;IAC1D,MAAMN,KAAK,GAAA,CAAAM,sBAAA,GAAGX,cAAc,CAACf,QAAQ,MAAA0B,IAAAA,IAAAA,sBAAA,KAAA,KAAA,IAAA,KAAA,IAAvBA,sBAAA,CAAyBL,OAAO,CAACd,KAAK,CAAC;IAErD,IAAIa,KAAK,KAAKxB,SAAS,EAAE;IAEzB,IAAIwB,KAAK,KAAK,CAAA,CAAE,EAAEL,cAAc,CAACf,QAAQ,CAACsB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMO,gBAAgB,GAAGA,CAACC,YAAY,EAAEC,OAAO,EAAEC,OAAO,KAAK;IAC3DF,YAAY,CAACjC,KAAK,GAAGmC,OAAO;AAC9B,CAAC;AAED,MAAMC,YAAY,GAAGA,CAACC,QAAQ,EAAEC,aAAa,EAAEnC,IAAI,EAAEoC,QAAQ,EAAEC,QAAQ,KAAK;IAC1E,MAAM,EAAEpC,KAAK,EAAE,GAAGE,OAAO,GAAGkC,QAAQ;IACpCH,QAAQ,CAAC/B,KAAK,GAAGA,KAAK;IACtB+B,QAAQ,CAACjC,KAAK,GAAGA,KAAK;AACxB,CAAC;AAEKqC,MAAAA,cAAc,IAAGC,KAAA,IAAA;IAAA,IAAC,EAAEC,QAAQ,GAAGA,KAAM,CAAA,EAAI,GAAAD,KAAA;IAAA,2KAC7CE,UAAU,AAAVA,EAAW;QACTlC,WAAW;QACXS,sBAAsB;QACtBa,gBAAgB;QAChBI,YAAY;QACZlC,cAAc;QACdM,kBAAkB;QAClBc,YAAY;QACZM,WAAW;QACXE,wBAAwB;QACxBe,gBAAgB,EAAEF;IACpB,CAAC,CAAC;AAAA;;;;;AC9EE,MAAA,EAAEG,OAAAA,EAAS,GAAGC;AAEpB,MAAMC,SAAS,GAAG,yKAAIC,UAAS,EAAE;AAEjC,yEAAA;AACA,IAAIC,QAAQ;AAEZ,2DAAA;AACA,6DAAA;AACA,MAAMC,MAAM,GAAG,CAAA,CAAE;AAEXC,MAAAA,GAAG,IAAIC,YAAY,IAAK;IAC5B,MAAMV,QAAQ,GAAGA,MAAM;QAAA,IAAAW,cAAA;QACrB,MAAMC,SAAS,GAAG,CAAA,CAAAD,cAAA,GAAAH,MAAM,CAACK,MAAM,MAAAF,IAAAA,IAAAA,cAAA,KAAA,KAAA,IAAA,KAAA,IAAbA,cAAA,CAAeG,KAAK,EAAE,KAAI,EAAE;QAC9C,IAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACI,MAAM,EAAED,CAAC,IAAI,CAAC,CAAEH,SAAS,CAACG,CAAC,CAAC,EAAE;KAC7D;IAED,MAAME,SAAS,GAAG;QAAEzD,IAAI,EAAE,MAAM;QAAEkB,QAAQ,EAAE;KAAM;IAClD6B,QAAQ,GAAGA,QAAQ,IAAIT,cAAc,CAAC;QAAEE;IAAS,CAAC,CAAC;IACnD,MAAMkB,SAAS,GAAGX,QAAQ,CAACY,eAAe,CAACF,SAAS,CAAC;IAErD,MAAMG,eAAe,GAAGA,CAACC,GAAG,EAAEC,QAAQ,KAAK;QACzCf,QAAQ,CAACa,eAAe,CAACC,GAAG,EAAEH,SAAS,EAAE,IAAI,EAAEI,QAAQ,CAAC;KACzD;IAED,IAAIZ,YAAY,EAAEU,eAAe,CAACV,YAAY,CAAC;IAE/C,MAAMa,MAAM,GAAG,eAAOC,QAAQ,EAAY;QAAA,IAApBA,QAAQ,KAAA,KAAA,CAAA,EAAA;YAARA,QAAQ,GAAG,IAAI;QAAA;QACnC,MAAM7D,KAAK,GAAGsD,SAAS,CAACvC,QAAQ,CAACf,KAAK,IAAI,CAAA,CAAE;QAC5C,MAAM,EACJ8D,UAAU,EACVC,QAAQ,EACRC,UAAU,EACVC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,OAAO,GAAG,WAAW,EACrBC,QAAQ,GAAG,WAAW,EACtBC,YAAY,GAAG,IAAIC,IAAI,EAAE,EACzBC,gBAAAA,EACD,GAAG1E,KAAK;QAET,MAAM2E,GAAG,GAAG,4KAAIC,UAAW,CAAC;YAC1Bf,QAAQ;YACRC,UAAU;YACVe,IAAI,EAAEd,QAAQ;YACde,YAAY,EAAE,IAAI;YAClBC,aAAa,EAAE,KAAK;YACpBC,IAAI,EAAE7F,QAAQ,CAAC;gBACb8F,KAAK,EAAEf,KAAK;gBACZgB,MAAM,EAAEf,MAAM;gBACdgB,OAAO,EAAEf,OAAO;gBAChBgB,QAAQ,EAAEf,SAAS;gBACnBgB,OAAO,EAAEf,OAAO;gBAChBgB,QAAQ,EAAEf,QAAQ;gBAClBgB,YAAY,EAAEf,YAAY;gBAC1BgB,gBAAgB,EAAEd;aACnB;QACH,CAAC,CAAC;QAEF,IAAIV,UAAU,EAAE;YACdW,GAAG,CAACc,KAAK,CAACC,IAAI,CAACC,UAAU,gKAAGC,aAAAA,AAAU,EAAC5B,UAAU,CAAC;QACpD;QAEA,IAAIC,QAAQ,EAAE;YACZU,GAAG,CAACc,KAAK,CAACC,IAAI,CAACG,QAAQ,gKAAGD,aAAAA,AAAU,EAAC3B,QAAQ,CAAC;QAChD;QAEA,MAAM6B,MAAM,GAAG,sKAAMC,UAAAA,AAAc,EAACzC,SAAS,CAACvC,QAAQ,EAAE2B,SAAS,CAAC;QAClE,MAAMsD,UAAU,mKAAGC,UAAAA,AAAS,EAACtB,GAAG,EAAEmB,MAAM,CAAC;QACzC,OAAO;YAAEA,MAAM;YAAEE;SAAY;KAC9B;IAED,MAAME,YAAY,GAAG,SAACC,MAAM,EAAU;QAAA,IAAhBA,MAAM,KAAA,KAAA,CAAA,EAAA;YAANA,MAAM,GAAG,CAAA,CAAE;QAAA;QAC/B,IAAI7C,SAAS,CAACvC,QAAQ,CAACf,KAAK,CAACoG,QAAQ,EAAE;YACrC9C,SAAS,CAACvC,QAAQ,CAACf,KAAK,CAACoG,QAAQ,CAACD,MAAM,CAAC;QAC3C;KACD;IAED,MAAME,MAAM,GAAG,YAAY;QACzB,MAAMC,MAAM,GAAG,EAAE;QACjB,MAAM,EAAER,MAAM,EAAES,wBAAwB,EAAEP,UAAU,EAAEjE,QAAAA,EAAU,GAC9D,MAAM6B,MAAM,EAAE;QAEhB,OAAO,IAAI4C,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;YACtC3E,QAAQ,CAAC4E,EAAE,CAAC,MAAM,GAAGC,KAAK,IAAK;gBAC7BN,MAAM,CAAC1F,IAAI,CACTgG,KAAK,YAAYC,UAAU,GAAGD,KAAK,GAAG,IAAIC,UAAU,CAACD,KAAK,CAC5D,CAAC;YACH,CAAC,CAAC;YAEF7E,QAAQ,CAAC4E,EAAE,CAAC,KAAK,EAAE,MAAM;gBACvB,IAAI;oBACF,MAAMG,IAAI,GAAG,IAAIC,IAAI,CAACT,MAAM,EAAE;wBAAEzG,IAAI,EAAE;oBAAkB,CAAC,CAAC;oBAC1DqG,YAAY,CAAC;wBAAEY,IAAI;wBAAEP;oBAAyB,CAAC,CAAC;oBAChDE,OAAO,CAACK,IAAI,CAAC;iBACd,CAAC,OAAOE,KAAK,EAAE;oBACdN,MAAM,CAACM,KAAK,CAAC;gBACf;YACF,CAAC,CAAC;QACJ,CAAC,CAAC;KACH;IAED,sGAAA;IACA,MAAMC,QAAQ,GAAG,YAAY;QAC3B,MAAM,EAAEnB,MAAM,EAAES,wBAAwB,EAAEP,UAAAA,EAAY,GAAG,MAAMpC,MAAM,EAAE;QACvEsC,YAAY,CAAC;YAAEK;QAAyB,CAAC,CAAC;QAE1C,OAAOP,UAAU;KAClB;IAED;;;;;GAKF,GACE,MAAMkB,QAAQ,GAAG,YAAY;QAC3B,wCAA4C;YAC1CxG,OAAO,CAACC,IAAI,CACV,oEACF,CAAC;QACH;QAEA,IAAI2G,MAAM,GAAG,EAAE;QACf,MAAM,EAAEtB,UAAU,EAAEjE,QAAAA,EAAU,GAAG,MAAM6B,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA,mFAAA;QAErD,OAAO,IAAI4C,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;YACtC,IAAI;gBACF3E,QAAQ,CAAC4E,EAAE,CAAC,MAAM,GAAGY,MAAM,IAAK;oBAC9BD,MAAM,IAAIC,MAAM;gBAClB,CAAC,CAAC;gBAEFxF,QAAQ,CAAC4E,EAAE,CAAC,KAAK,EAAE,MAAM;oBACvBT,YAAY,EAAE;oBACdO,OAAO,CAACa,MAAM,CAAC;gBACjB,CAAC,CAAC;aACH,CAAC,OAAON,KAAK,EAAE;gBACdN,MAAM,CAACM,KAAK,CAAC;YACf;QACF,CAAC,CAAC;KACH;IAED,MAAML,EAAE,GAAGA,CAACa,KAAK,EAAEC,QAAQ,KAAK;QAC9B,IAAI,CAAC5E,MAAM,CAAC2E,KAAK,CAAC,EAAE3E,MAAM,CAAC2E,KAAK,CAAC,GAAG,EAAE;QACtC3E,MAAM,CAAC2E,KAAK,CAAC,CAAC5G,IAAI,CAAC6G,QAAQ,CAAC;KAC7B;IAED,MAAMC,cAAc,GAAGA,CAACF,KAAK,EAAEC,QAAQ,KAAK;QAC1C,IAAI,CAAC5E,MAAM,CAAC2E,KAAK,CAAC,EAAE;QACpB,MAAMG,GAAG,GAAG9E,MAAM,CAAC2E,KAAK,CAAC,CAACpG,OAAO,CAACqG,QAAQ,CAAC;QAC3C,IAAIE,GAAG,GAAG,CAAA,CAAE,EAAE9E,MAAM,CAAC2E,KAAK,CAAC,CAACnG,MAAM,CAACsG,GAAG,EAAE,CAAC,CAAC;KAC3C;IAED,OAAO;QACLhB,EAAE;QACFrD,SAAS;QACT+C,MAAM;QACNY,QAAQ;QACRC,QAAQ;QACRQ,cAAc;QACdjE;KACD;AACH;AAEMmE,MAAAA,IAAI,GAAGlF;AAEb,MAAMmF,UAAU,GAAG;IACjBC,MAAM,EAAGC,CAAC,IAAKA;AACjB;AChLA;;;;;CAKA,SACaC,MAAM,GAAG,SAAAC,KAAA,EAAuB;IAAA,IAAtB,EAAElH,QAAAA,EAAU,GAAAkH,KAAA,KAAA,KAAA,CAAA,GAAG,CAAA,CAAE,GAAAA,KAAA;IACtC,MAAMC,WAAW,oKAAGC,UAAAA,AAAM,EAAC,IAAI,CAAC;IAEhC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,qKAAGC,WAAAA,AAAQ,EAAC;QACjCC,GAAG,EAAE,IAAI;QACTzB,IAAI,EAAE,IAAI;QACVE,KAAK,EAAE,IAAI;QACXwB,OAAO,EAAE,CAAC,CAACzH;IACb,CAAC,CAAC;IAEF,wBAAA;sKACA0H,YAAAA,AAAS;4BAAC,MAAM;YACd,MAAMC,WAAW,yIAAGC,UAAAA,AAAK,EAAC;gBAAEC,SAAS,EAAE,IAAI;gBAAEC,WAAW,EAAE;YAAE,CAAC,CAAC;YAE9D,MAAMC,mBAAmB;wDAAGA,MAAM;oBAChCT,QAAQ;iEAAEU,IAAI,GAAA,CAAM;gCAAE,GAAGA,IAAI;gCAAEP,OAAO,EAAE;4BAAK,CAAC,CAAC,CAAC;;oBAEhDE,WAAW,CAACrH,MAAM,CAAC,CAAC,EAAEqH,WAAW,CAACrF,MAAM;gEAAE,IACxC+E,KAAK,CAACpB,KAAK,GAAGR,OAAO,CAACC,OAAO,EAAE,GAAGyB,WAAW,CAACc,OAAO,CAAC3C,MAAM,EAC9D,CAAC;;iBACF;;YAED,MAAM4C,cAAc;oDAAIjC,KAAK,IAAK;oBAChCtG,OAAO,CAACsG,KAAK,CAACA,KAAK,CAAC;oBACpBqB,QAAQ;4DAAEU,IAAI,GAAA,CAAM;gCAAE,GAAGA,IAAI;gCAAEP,OAAO,EAAE,KAAK;gCAAExB;4BAAM,CAAC,CAAC,CAAC;;iBACzD;;YAED,MAAMkC,kBAAkB;uDAAIpC,IAAI,IAAK;oBACnCuB,QAAQ,CAAC;wBACPvB,IAAI;wBACJE,KAAK,EAAE,IAAI;wBACXwB,OAAO,EAAE,KAAK;wBACdD,GAAG,EAAEY,GAAG,CAACC,eAAe,CAACtC,IAAI;oBAC/B,CAAC,CAAC;iBACH;;YAEDoB,WAAW,CAACc,OAAO,GAAGlG,GAAG,EAAE;YAC3BoF,WAAW,CAACc,OAAO,CAACrC,EAAE,CAAC,QAAQ,EAAEmC,mBAAmB,CAAC;YACrD,IAAI/H,QAAQ,EAAE;gBACZmH,WAAW,CAACc,OAAO,CAACvF,eAAe,CAAC1C,QAAQ,CAAC;YAC/C;YAEA2H,WAAW,CAAC/B,EAAE,CAAC,OAAO,EAAEsC,cAAc,CAAC;YACvCP,WAAW,CAAC/B,EAAE,CAAC,SAAS,EAAEuC,kBAAkB,CAAC;YAE7C;oCAAO,MAAM;oBACXR,WAAW,CAACW,GAAG,EAAE;oBACjBnB,WAAW,CAACc,OAAO,CAACtB,cAAc,CAAC,QAAQ,EAAEoB,mBAAmB,CAAC;iBAClE;;SACF;2BAAE,EAAE,CAAC;IAEN,kCAAA;QACAL,0KAAAA,AAAS;4BAAC,MAAM;YACd;oCAAO,MAAM;oBACX,IAAIL,KAAK,CAACG,GAAG,EAAE;wBACbY,GAAG,CAACG,eAAe,CAAClB,KAAK,CAACG,GAAG,CAAC;oBAChC;iBACD;;QACH,CAAC;2BAAE;QAACH,KAAK,CAACG,GAAG;KAAC,CAAC;IAEf,MAAMgB,MAAM,qKAAGC,cAAAA,AAAW;uCAAEC,MAAM,IAAK;YACrCvB,WAAW,CAACc,OAAO,CAACvF,eAAe,CAACgG,MAAM,CAAC;SAC5C;qCAAE,EAAE,CAAC;IAEN,OAAO;QAACrB,KAAK;QAAEmB,MAAM;KAAC;AACxB;ACxEaG,MAAAA,SAAS,IAAGjK,IAAA,IAQnB;IAAA,IARoB,EACxByE,KAAK,EACLpE,KAAK,EACL6J,SAAS,EACT5J,QAAQ,EACR6J,QAAQ,EACRC,WAAW,GAAG,IAAI,EAClB,GAAG7J,OACJ,GAAAP,IAAA;IACC,MAAM,CAACsC,QAAQ,EAAE+H,cAAc,CAAC,GAAG9B,MAAM,EAAE;sKAE3CS,YAAAA,AAAS;+BAAC,IAAMqB,cAAc,CAAC/J,QAAQ,CAAC;8BAAE;QAACA,QAAQ;KAAC,CAAC;IAErD,MAAMgK,GAAG,GAAGhI,QAAQ,CAACwG,GAAG,GACnB,GAAExG,QAAQ,CAACwG,GAAI,CAAA,SAAA,EAAWsB,WAAW,GAAG,CAAC,GAAG,CAAE,CAAA,CAAC,GAChD,IAAI;IAER,OAAA,WAAA,8KACEG,MAAAA,AAAA,EAAA,QAAA,EAAA;QACED,GAAG,EAAEA,GAAI;QACT7F,KAAK,EAAEA,KAAM;QACb+F,GAAG,EAAEL,QAAS;QACd9J,KAAK,EAAEA,KAAM;QACb6J,SAAS,EAAEA,SAAU;QAAA,GACjB3J,KAAAA;IAAK,CACV,CAAC;AAEN;AC3BakK,MAAAA,YAAY,IAAGzK,IAAA,IAAiC;IAAA,IAAhC,EAAEsB,QAAQ,EAAE2C,GAAG,EAAE3D,QAAAA,EAAU,GAAAN,IAAA;IACtD,MAAM,CAACsC,QAAQ,EAAE+H,cAAc,CAAC,GAAG9B,MAAM,EAAE;QAE3CS,0KAAAA,AAAS;kCAAC,IAAMqB,cAAc,CAACpG,GAAG,CAAC;iCAAE;QAACA,GAAG;KAAC,CAAC;IAE3C,IAAI,CAACA,GAAG,EAAE;QACRhD,OAAO,CAACC,IAAI,CAAC,kDAAkD,CAAC;QAChE,OAAO,IAAI;IACb;IAEA,OAAOZ,QAAQ,CAACgC,QAAQ,CAAC;AAC3B;ACXaoI,MAAAA,eAAe,IAAG1K,IAAA,IAOzB;IAAA,IAP0B,EAC9B2K,QAAQ,GAAG,cAAc,EACzBrJ,QAAQ,EAAE2C,GAAG,EACb3D,QAAQ,EACRsK,OAAO,EACPC,IAAI,EACJ,GAAGC,MACJ,GAAA9K,IAAA;IACC,MAAM,CAACsC,QAAQ,EAAE+H,cAAc,CAAC,GAAG9B,MAAM,EAAE;sKAE3CS,YAAAA,AAAS;qCAAC,IAAMqB,cAAc,CAACpG,GAAG,CAAC;oCAAE;QAACA,GAAG;KAAC,CAAC;IAE3C,IAAI,CAACA,GAAG,EAAE;QACRhD,OAAO,CAACC,IAAI,CAAC,qDAAqD,CAAC;QACnE,OAAO,IAAI;IACb;IAEA,MAAM6J,gBAAgB,GAAGA,MAAM;QAC7B,IAAIzI,QAAQ,IAAI0I,MAAM,CAACC,SAAS,CAACC,UAAU,EAAE;YAC3C,KAAA;YACAF,MAAM,CAACC,SAAS,CAACC,UAAU,CAAC5I,QAAQ,CAAC+E,IAAI,EAAEsD,QAAQ,CAAC;QACtD;KACD;IAED,MAAMQ,WAAW,IAAIpD,KAAK,IAAK;QAC7BgD,gBAAgB,EAAE;QAClB,IAAI,OAAOH,OAAO,KAAK,UAAU,EAAEA,OAAO,CAAC7C,KAAK,EAAEzF,QAAQ,CAAC;KAC5D;IAED,OAAA,WAAA,8KACEiI,MAAAA,AAAA,EAAA,GAAA,EAAA;QAAGM,IAAI,EAAEvI,QAAQ,CAACwG,GAAI;QAACsC,QAAQ,EAAET,QAAS;QAACC,OAAO,EAAEO,WAAY;QAAA,GAAKL,IAAI;QAAAxK,QAAA,EACtE,OAAOA,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACgC,QAAQ,CAAC,GAAGhC;IAAQ,CAC9D,CAAC;AAER;AC9BA,MAAM+K,qBAAqB,IAAIC,IAAI,IAAK;IACtC,MAAM,IAAIC,KAAK,CACZ,CAAED,EAAAA,IAAK,CAAA,+IAAA,CACV,CAAC;AACH,CAAC;AAEYE,MAAAA,cAAc,GAAGA,MAAM;IAClCH,qBAAqB,CAAC,gBAAgB,CAAC;AACzC;AAEaI,MAAAA,cAAc,GAAGA,MAAM;IAClCJ,qBAAqB,CAAC,gBAAgB,CAAC;AACzC;AAEaK,MAAAA,cAAc,GAAGA,MAAM;IAClCL,qBAAqB,CAAC,gBAAgB,CAAC;AACzC;AAEaM,MAAAA,YAAY,GAAGA,MAAM;IAChCN,qBAAqB,CAAC,cAAc,CAAC;AACvC;AAEalH,MAAAA,MAAM,GAAGA,MAAM;IAC1BkH,qBAAqB,CAAC,QAAQ,CAAC;AACjC;AAcA,yFAAA;AACA,IAAA,QAAe;IACbhI,GAAG;IACHkF,MAAM;IACNJ,IAAI;IACJpF,OAAO;IACPqF,UAAU;IACV6B,SAAS;IACTQ,YAAY;IACZC,eAAe;IACfc,cAAc;IACdE,cAAc;IACdC,YAAY;IACZxH,MAAM;IACN,GAAGyH,+JAAAA;AACL,CAAC", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7], "debugId": null}}]}