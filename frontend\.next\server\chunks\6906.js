"use strict";exports.id=6906,exports.ids=[6906],exports.modules={6211:(e,t,r)=>{r.d(t,{A0:()=>l,BF:()=>o,Hj:()=>d,XI:()=>n,nA:()=>u,nd:()=>c});var s=r(60687),a=r(43210),i=r(22482);let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{className:(0,i.cn)("w-full caption-bottom text-sm",e),ref:r,...t})}));n.displayName="Table";let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("thead",{className:(0,i.cn)("[&_tr]:border-b",e),ref:r,...t}));l.displayName="TableHeader";let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("tbody",{className:(0,i.cn)("[&_tr:last-child]:border-0",e),ref:r,...t}));o.displayName="TableBody",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("tfoot",{className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),ref:r,...t})).displayName="TableFooter";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("tr",{className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),ref:r,...t}));d.displayName="TableRow";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("th",{className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),ref:r,...t}));c.displayName="TableHead";let u=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("td",{className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),ref:r,...t}));u.displayName="TableCell",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("caption",{className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),ref:r,...t})).displayName="TableCaption"},21788:(e,t,r)=>{r.r(t),r.d(t,{useSidebar:()=>i});var s=r(43210),a=r(94538);let i=()=>{let e=(0,a.C)(e=>e.sidebarOpen),t=(0,a.C)(e=>e.toggleSidebar),r=(0,s.useCallback)(()=>{e||t()},[e,t]),i=(0,s.useCallback)(()=>{e&&t()},[e,t]),n=(0,s.useCallback)(()=>({content:e?"content-shifted":"content-normal",overlay:e?"overlay-visible":"overlay-hidden",sidebar:e?"sidebar-open":"sidebar-closed",toggle:e?"toggle-close":"toggle-open"}),[e]);return{closeSidebar:i,getAriaAttributes:(0,s.useCallback)(()=>({"aria-expanded":e,"aria-label":e?"Close sidebar":"Open sidebar"}),[e]),getSidebarClasses:n,isClosed:!e,isOpen:e,openSidebar:r,sidebarOpen:e,toggleSidebar:t}}},23338:(e,t,r)=>{r.d(t,{y:()=>a});var s=r(43210);let a=e=>(0,s.useMemo)(()=>{let t=e.escorts&&e.escorts.length>0&&e.escorts[0]?.employee?e.escorts[0].employee:null,r=e.drivers&&e.drivers.length>0&&e.drivers[0]?.employee?e.drivers[0].employee:null,s=e.vehicles&&e.vehicles.length>0&&e.vehicles[0]?.vehicle?e.vehicles[0].vehicle:null,a=!!(e.arrivalFlight||e.departureFlight),i=!t&&"Completed"!==e.status&&"Cancelled"!==e.status;return{escortInfo:t,driverInfo:r,vehicleInfo:s,hasFlightDetails:a,needsEscortAssignment:i,isActive:"In_Progress"===e.status}},[e])},23562:(e,t,r)=>{r.d(t,{k:()=>l});var s=r(60687),a=r(25177),i=r(43210),n=r(22482);let l=i.forwardRef(({className:e,value:t,...r},i)=>(0,s.jsx)(a.bL,{className:(0,n.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),ref:i,...r,children:(0,s.jsx)(a.C1,{className:"size-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));l.displayName=a.bL.displayName},25758:(e,t,r)=>{r.d(t,{Sk:()=>n,p9:()=>l});var s=r(43612),a=r(43210),i=r(3389);let n=(e,t,r={})=>{let{toast:n}=(0,i.dj)(),{cacheDuration:l=3e5,enableRetry:o=!0,errorMessage:d,retryAttempts:c=3,showErrorToast:u=!0,showSuccessToast:p=!1,successMessage:f,...g}=r,h=(0,s.I)({gcTime:2*l,queryFn:t,queryKey:e,retry:!!o&&c,retryDelay:e=>Math.min(1e3*2**e,3e4),staleTime:l,...g});return(0,a.useEffect)(()=>{p&&h.isSuccess&&h.data&&f&&n({description:f,title:"Success"})},[p,h.isSuccess,h.data,f,n]),(0,a.useEffect)(()=>{u&&h.isError&&n({description:d||(h.error instanceof Error?h.error.message:"An error occurred"),title:"Error",variant:"destructive"})},[u,h.isError,h.error,d,n]),{...h,forceRefresh:async()=>await h.refetch(),isStale:h.isStale||!1,lastUpdated:h.dataUpdatedAt||null}},l=(e,t,r={})=>{let{keepPreviousData:s=!0,page:a=1,pageSize:i=10,...l}=r,o=n([...e,"paginated",a,i],()=>t(a,i),{...l,...s?{placeholderData:e=>e}:{}}),d=o.data?.pagination;return{...o,currentPage:a,data:o.data?.data??[],goToPage:e=>{},hasNextPage:!!d&&d.hasNext,hasPrevPage:!!d&&d.hasPrevious,nextPage:()=>{d&&d.hasNext},pagination:d??{hasNext:!1,hasPrevious:!1,limit:i,page:1,total:0,totalPages:1},prevPage:()=>{d&&d.hasPrevious},totalPages:d?d.totalPages:1}}},30259:(e,t,r)=>{r.d(t,{Sk:()=>s.Sk});var s=r(25758);r(3389),r(16189),r(43210),r(49603),r(17839),r(63213),r(46349)},87432:(e,t,r)=>{r.d(t,{AppBreadcrumb:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AppBreadcrumb() from the server but AppBreadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\app-breadcrumb.tsx","AppBreadcrumb")},87676:(e,t,r)=>{r.r(t),r.d(t,{useNotifications:()=>i,useWorkHubNotifications:()=>n});var s=r(43210),a=r(94538);let i=()=>{let e=(0,a.C)(e=>e.addNotification),t=(0,a.C)(e=>e.removeNotification),r=(0,a.C)(e=>e.clearAllNotifications),i=(0,a.C)(e=>e.unreadNotificationCount),n=(0,s.useCallback)(t=>{e({message:t,type:"success"})},[e]),l=(0,s.useCallback)(t=>{e({message:t,type:"error"})},[e]),o=(0,s.useCallback)(t=>{e({message:t,type:"warning"})},[e]),d=(0,s.useCallback)(t=>{e({message:t,type:"info"})},[e]),c=(0,s.useCallback)((e,t,r)=>{e?n(t):l(r)},[n,l]),u=(0,s.useCallback)((r,s,i=5e3)=>{e({message:s,type:r}),setTimeout(()=>{let e=a.C.getState().notifications.at(-1);e&&e.message===s&&t(e.id)},i)},[e,t]),p=(0,s.useCallback)((t="Loading...")=>{e({message:t,type:"info"});let r=a.C.getState().notifications;return r.at(-1)?.id},[e]),f=(0,s.useCallback)((e,r,s)=>{t(e),r?n(s):l(s)},[t,n,l]);return{clearAllNotifications:r,removeNotification:t,showApiResult:c,showError:l,showInfo:d,showLoading:p,showSuccess:n,showTemporary:u,showWarning:o,unreadCount:i,updateLoadingNotification:f}},n=()=>{let{clearAllNotifications:e,removeNotification:t,showError:r,showInfo:n,showSuccess:l,showWarning:o,unreadCount:d}=i(),c=(0,s.useCallback)((e,t)=>{(0,a.C.getState().addNotification)({...t&&{actionUrl:t},category:"delegation",message:e,type:"delegation-update"})},[]),u=(0,s.useCallback)((e,t)=>{(0,a.C.getState().addNotification)({...t&&{actionUrl:t},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),p=(0,s.useCallback)((e,t)=>{(0,a.C.getState().addNotification)({...t&&{actionUrl:t},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:t,showDelegationUpdate:c,showEmployeeUpdate:(0,s.useCallback)((e,t)=>{(0,a.C.getState().addNotification)({...t&&{actionUrl:t},category:"employee",message:e,type:"employee-update"})},[]),showError:r,showInfo:n,showSuccess:l,showTaskAssigned:p,showVehicleMaintenance:u,showWarning:o,unreadCount:d}}},95594:(e,t,r)=>{r.d(t,{Sk:()=>u.Sk,XD:()=>c}),r(94538);var s=r(26787),a=r(59350);let i={"alert-statistics":3e5,alerts:1e4,"circuit-breakers":3e4,dependencies:45e3,"detailed-health":6e4,health:15e3,metrics:3e4},n={expandedWidgets:new Set(["system-health","health-status-indicators","circuit-breakers","active-alerts"]),gridColumns:3,layout:"grid",visibleWidgets:new Set(["system-health","health-status-indicators","circuit-breakers","active-alerts","alert-statistics","dependency-status","health-trends","circuit-breaker-metrics","circuit-breaker-history","deduplication-metrics","performance-metrics","performance-overview","system-metrics","http-metrics","dependency-health"]),widgetOrder:["system-health","health-status-indicators","circuit-breakers","active-alerts","alert-statistics","dependency-status","health-trends","circuit-breaker-metrics","circuit-breaker-list","circuit-breaker-history","circuit-breaker-alerts","system-resources","performance-overview","system-metrics","http-metrics","deduplication-metrics","performance-metrics","dependency-health"]},l={autoDismissTimeout:5e3,desktopEnabled:!0,minimumSeverity:"medium",soundEnabled:!0},o={searchText:"",severities:new Set(["critical","high","low","medium"]),sources:new Set,statuses:new Set(["acknowledged","active"]),timeRange:"24h"},d={"acknowledge-multiple-alerts":!1,"export-metrics":!1,"refresh-all-data":!1,"resolve-multiple-alerts":!1},c=(0,s.v)()((0,a.lt)((0,a.Zr)((e,t)=>({clearAlertFilters:()=>e(e=>({ui:{...e.ui,filters:o}})),clearAlertSelection:()=>e(e=>({ui:{...e.ui,selectedAlerts:new Set}})),getActiveFilters:()=>{let{ui:e}=t(),r=e.filters,s={};return 4!==r.severities.size&&(s.severities=r.severities),2!==r.statuses.size&&(s.statuses=r.statuses),r.sources.size>0&&(s.sources=r.sources),r.searchText.trim()&&(s.searchText=r.searchText),"24h"!==r.timeRange&&(s.timeRange=r.timeRange),s},getSelectedAlertCount:()=>{let{ui:e}=t();return e.selectedAlerts.size},getVisibleWidgets:()=>{let{preferences:e}=t();return e.dashboardLayout.widgetOrder.filter(t=>e.dashboardLayout.visibleWidgets.has(t))},isDataTypePaused:e=>{let{monitoring:r}=t();return r.pausedDataTypes.has(e)||!r.isEnabled},monitoring:{connectionStatus:"disconnected",isEnabled:!0,lastRefresh:{},pausedDataTypes:new Set},pauseAllMonitoring:()=>e(e=>({monitoring:{...e.monitoring,isEnabled:!1,pausedDataTypes:new Set(["alert-statistics","alerts","circuit-breakers","dependencies","detailed-health","health","metrics"])}})),pauseDataType:t=>e(e=>{let r=new Set(e.monitoring.pausedDataTypes);return r.add(t),{monitoring:{...e.monitoring,pausedDataTypes:r}}}),preferences:{dashboardLayout:n,defaultTimeRange:"24h",notifications:l,refreshIntervals:i},reorderWidgets:t=>e(e=>({preferences:{...e.preferences,dashboardLayout:{...e.preferences.dashboardLayout,widgetOrder:t}}})),resetPreferencesToDefaults:()=>e(e=>({preferences:{dashboardLayout:n,defaultTimeRange:"24h",notifications:l,refreshIntervals:i}})),resumeAllMonitoring:()=>e(e=>({monitoring:{...e.monitoring,isEnabled:!0,pausedDataTypes:new Set}})),resumeDataType:t=>e(e=>{let r=new Set(e.monitoring.pausedDataTypes);return r.delete(t),{monitoring:{...e.monitoring,pausedDataTypes:r}}}),selectAllAlerts:t=>e(e=>({ui:{...e.ui,selectedAlerts:new Set(t)}})),setActiveTab:t=>e(e=>({ui:{...e.ui,activeTab:t}})),setAlertFilters:t=>e(e=>({ui:{...e.ui,filters:{...e.ui.filters,...t,severities:t.severities||e.ui.filters.severities,sources:t.sources||e.ui.filters.sources,statuses:t.statuses||e.ui.filters.statuses}}})),setBatchOperationLoading:(t,r)=>e(e=>({ui:{...e.ui,batchOperations:{...e.ui.batchOperations,[t]:r}}})),setConnectionStatus:t=>e(e=>({monitoring:{...e.monitoring,connectionStatus:t}})),setDashboardLayout:t=>e(e=>({preferences:{...e.preferences,dashboardLayout:{...e.preferences.dashboardLayout,layout:t}}})),setDefaultTimeRange:t=>e(e=>({preferences:{...e.preferences,defaultTimeRange:t}})),setGridColumns:t=>e(e=>({preferences:{...e.preferences,dashboardLayout:{...e.preferences.dashboardLayout,gridColumns:Math.max(1,Math.min(6,t))}}})),setMonitoringEnabled:t=>e(e=>({monitoring:{...e.monitoring,isEnabled:t}})),setNotificationPreferences:t=>e(e=>({preferences:{...e.preferences,notifications:{...e.preferences.notifications,...t}}})),setRefreshInterval:(t,r)=>e(e=>({preferences:{...e.preferences,refreshIntervals:{...e.preferences.refreshIntervals,[t]:r}}})),setWidgetExpanded:(t,r)=>e(e=>{let s=new Set(e.preferences.dashboardLayout.expandedWidgets);return r?s.add(t):s.delete(t),{preferences:{...e.preferences,dashboardLayout:{...e.preferences.dashboardLayout,expandedWidgets:s}}}}),toggleAlertSelection:t=>e(e=>{let r=new Set(e.ui.selectedAlerts);return r.has(t)?r.delete(t):r.add(t),{ui:{...e.ui,selectedAlerts:r}}}),toggleFilterPanel:()=>e(e=>({ui:{...e.ui,isFilterPanelOpen:!e.ui.isFilterPanelOpen}})),toggleWidget:t=>e(e=>{let r=new Set(e.preferences.dashboardLayout.visibleWidgets);return r.has(t)?r.delete(t):r.add(t),{preferences:{...e.preferences,dashboardLayout:{...e.preferences.dashboardLayout,visibleWidgets:r}}}}),ui:{activeTab:"overview",batchOperations:d,filters:o,isFilterPanelOpen:!1,selectedAlerts:new Set},updateLastRefresh:(t,r)=>e(e=>({monitoring:{...e.monitoring,lastRefresh:{...e.monitoring.lastRefresh,[t]:r||new Date().toISOString()}}}))}),{merge:(e,t)=>(e.preferences.dashboardLayout.expandedWidgets=new Set(e.preferences.dashboardLayout.expandedWidgets),e.preferences.dashboardLayout.visibleWidgets=new Set(e.preferences.dashboardLayout.visibleWidgets),{...t,...e}),name:"workhub-reliability-store",partialize:e=>({preferences:{dashboardLayout:{expandedWidgets:[...e.preferences.dashboardLayout.expandedWidgets],gridColumns:e.preferences.dashboardLayout.gridColumns,layout:e.preferences.dashboardLayout.layout,visibleWidgets:[...e.preferences.dashboardLayout.visibleWidgets],widgetOrder:e.preferences.dashboardLayout.widgetOrder},defaultTimeRange:e.preferences.defaultTimeRange,notifications:e.preferences.notifications,refreshIntervals:e.preferences.refreshIntervals}})}),{name:"reliability-store"}));r(51887);var u=r(30259);r(50885),r(87676),r(21788),r(41181),r(64968),r(54827),r(10035),r(28439),r(19203),r(3940),r(43210),r(9275);var p=r(93778),f=r(43280),g=r(82512),h=r(54995),m=r(67079);f.b,p.Sk,g.n,h.o,m.C,r(23338),r(29385),r(3389);class b{clear(){this.pendingRequests.clear()}async deduplicate(e,t){if(this.pendingRequests.has(e))return console.log(`🔄 Global Request DEDUPLICATED for ${e}`),this.pendingRequests.get(e);let r=t().finally(()=>{this.pendingRequests.delete(e)});return this.pendingRequests.set(e,r),console.log(`🔄 Global Request STARTED for ${e}`),r}getPendingCount(){return this.pendingRequests.size}getPendingKeys(){return[...this.pendingRequests.keys()]}constructor(){this.pendingRequests=new Map}}new b}};