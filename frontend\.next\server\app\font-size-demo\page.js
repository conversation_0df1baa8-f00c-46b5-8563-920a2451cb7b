(()=>{var e={};e.id=8544,e.ids=[8544],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4100:(e,s,t)=>{Promise.resolve().then(t.bind(t,96530))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},50948:(e,s,t)=>{Promise.resolve().then(t.bind(t,70880))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55817:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(82614).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70880:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\font-size-demo\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\font-size-demo\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94369:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=t(65239),i=t(48088),n=t(88170),a=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(s,l);let c={children:["",{children:["font-size-demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,70880)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\font-size-demo\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,34595)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\font-size-demo\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/font-size-demo/page",pathname:"/font-size-demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94735:e=>{"use strict";e.exports=require("events")},96530:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var r=t(60687),i=t(55817),n=t(50616),a=t(76311),o=t(85814),l=t.n(o);t(43210);var c=t(8639),d=t(96834),x=t(29523),p=t(44493),m=t(64968);function h(){let{fontSize:e,getFontSizeClass:s,setFontSize:t}=(0,m.useUiPreferences)();return(0,r.jsxs)("div",{className:"container mx-auto space-y-8 py-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(l(),{href:"/",children:(0,r.jsxs)(x.$,{className:"flex items-center gap-2",size:"sm",variant:"ghost",children:[(0,r.jsx)(i.A,{className:"size-4"}),"Back to Dashboard"]})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"flex items-center gap-2 text-3xl font-bold",children:[(0,r.jsx)(n.A,{className:"size-8"}),"Font Size Demo"]}),(0,r.jsx)("p",{className:"mt-1 text-muted-foreground",children:"Test and preview font size changes in real-time"})]})]}),(0,r.jsxs)(p.Zp,{children:[(0,r.jsxs)(p.aR,{children:[(0,r.jsxs)(p.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(a.A,{className:"size-5"}),"Current Font Size"]}),(0,r.jsx)(p.BT,{children:"The currently selected font size affects all content on this page"})]}),(0,r.jsx)(p.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-lg font-medium",children:"Active Setting"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Applied globally across WorkHub"})]}),(0,r.jsx)(d.E,{className:"px-4 py-2 text-lg capitalize",variant:"secondary",children:e})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-3",children:[(0,r.jsxs)(p.Zp,{children:[(0,r.jsx)(p.aR,{children:(0,r.jsx)(p.ZB,{className:"text-lg",children:"Quick Toggle"})}),(0,r.jsx)(p.Wu,{children:(0,r.jsx)(c.Ln,{})})]}),(0,r.jsxs)(p.Zp,{children:[(0,r.jsx)(p.aR,{children:(0,r.jsx)(p.ZB,{className:"text-lg",children:"Dropdown Selector"})}),(0,r.jsx)(p.Wu,{children:(0,r.jsx)(c.A5,{})})]}),(0,r.jsxs)(p.Zp,{children:[(0,r.jsx)(p.aR,{children:(0,r.jsx)(p.ZB,{className:"text-lg",children:"Button Controls"})}),(0,r.jsx)(p.Wu,{children:(0,r.jsx)("div",{className:"flex gap-1",children:["small","medium","large"].map(s=>(0,r.jsx)(x.$,{className:"flex-1 capitalize",onClick:()=>t(s),size:"sm",variant:e===s?"default":"outline",children:s},s))})})]})]}),(0,r.jsxs)(p.Zp,{children:[(0,r.jsxs)(p.aR,{children:[(0,r.jsx)(p.ZB,{children:"Sample Content Preview"}),(0,r.jsx)(p.BT,{children:"See how different content types are affected by font size changes"})]}),(0,r.jsxs)(p.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"mb-2 text-xl font-semibold",children:"Heading Text"}),(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"WorkHub Font Size Demonstration"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"mb-2 text-xl font-semibold",children:"Paragraph Text"}),(0,r.jsx)("p",{className:"leading-relaxed",children:"This page demonstrates how font size changes affect the entire application. The font size setting is applied globally and affects all text content throughout WorkHub."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"mb-2 text-xl font-semibold",children:"List Items"}),(0,r.jsx)("ul",{className:"list-inside list-disc space-y-1",children:["Dashboard statistics and metrics","Navigation menu items","Form labels and input text","Table data and headers","Button text and descriptions"].map((e,s)=>(0,r.jsx)("li",{children:e},s))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"mb-2 text-xl font-semibold",children:"Quote Text"}),(0,r.jsx)("blockquote",{className:"border-l-4 border-primary pl-4 italic text-muted-foreground",children:"Good typography is invisible. Bad typography is everywhere."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"mb-2 text-xl font-semibold",children:"Secondary Text"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"This is secondary text that appears in descriptions, captions, and helper text throughout the application."})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"mb-4 text-2xl font-semibold",children:"Complete Font Size Settings"}),(0,r.jsx)(c.uq,{})]}),(0,r.jsxs)(p.Zp,{children:[(0,r.jsx)(p.aR,{children:(0,r.jsx)(p.ZB,{children:"How to Use Font Size Controls"})}),(0,r.jsxs)(p.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"mb-2 font-medium",children:"1. Navigation Bar"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:'Click the "A" icon in the navigation bar for quick font size controls.'})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"mb-2 font-medium",children:"2. Settings Page"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Visit the Settings page for comprehensive font size management with preview."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"mb-2 font-medium",children:"3. This Demo Page"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Use this page to test font size changes and see how they affect different content types."})]})]})]})]})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,211,1658,5941],()=>t(94369));module.exports=r})();