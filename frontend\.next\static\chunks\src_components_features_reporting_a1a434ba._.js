(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/features/reporting/hooks/useReportGeneration.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file useReportGeneration.ts
 * @description Hook for managing data report generation
 */ __turbopack_context__.s({
    "useReportDownload": (()=>useReportDownload),
    "useReportGeneration": (()=>useReportGeneration),
    "useReportHistory": (()=>useReportHistory),
    "useReportTemplates": (()=>useReportTemplates)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureApiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportGenerationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/services/ReportGenerationService.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
;
;
;
;
const useReportGeneration = ()=>{
    _s();
    const [isGenerating, setIsGenerating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const { client } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSecureApiClient"])();
    // Create API client adapter for the service
    const apiClient = {
        request: async (config)=>{
            const response = await client.request(config);
            return {
                data: response
            };
        }
    };
    // Create service instance with proper API client
    const service = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportGenerationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createReportGenerationService"])(apiClient);
    /**
   * Generate comprehensive data report
   * Uses service layer following DIP principle
   */ const generateReport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useReportGeneration.useCallback[generateReport]": async (config)=>{
            setIsGenerating(true);
            setError(null);
            try {
                return await service.generateComprehensiveReport(config);
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : 'Failed to generate report';
                setError(errorMessage);
                throw err;
            } finally{
                setIsGenerating(false);
            }
        }
    }["useReportGeneration.useCallback[generateReport]"], [
        service
    ]);
    /**
   * Generate individual entity report
   * Uses service layer following DIP principle
   */ const generateIndividualReport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useReportGeneration.useCallback[generateIndividualReport]": async (config)=>{
            setIsGenerating(true);
            setError(null);
            try {
                return await service.generateIndividualReport(config);
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : 'Failed to generate individual report';
                setError(errorMessage);
                throw err;
            } finally{
                setIsGenerating(false);
            }
        }
    }["useReportGeneration.useCallback[generateIndividualReport]"], [
        service
    ]);
    /**
   * Generate aggregate entity report
   * Uses service layer following DIP principle
   */ const generateAggregateReport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useReportGeneration.useCallback[generateAggregateReport]": async (config)=>{
            setIsGenerating(true);
            setError(null);
            try {
                return await service.generateAggregateReport(config);
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : 'Failed to generate aggregate report';
                setError(errorMessage);
                throw err;
            } finally{
                setIsGenerating(false);
            }
        }
    }["useReportGeneration.useCallback[generateAggregateReport]"], [
        service
    ]);
    /**
   * Export generated report data to PDF/Excel/CSV
   */ const exportReport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useReportGeneration.useCallback[exportReport]": async (reportData, format, entityType, reportTitle, filename)=>{
            try {
                const { useExport } = await __turbopack_context__.r("[project]/src/components/features/reporting/exports/hooks/useExport.tsx [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                const { exportReportToPDF, exportReportToExcel, exportToCSV } = useExport(filename || 'report');
                switch(format){
                    case 'pdf':
                        // Convert report data to PDF using entity-specific components
                        await exportReportToPDF(reportData, entityType, reportTitle || `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} Report`, filename);
                        break;
                    case 'excel':
                        // Convert to Excel format with proper sheets
                        exportReportToExcel(reportData, entityType, filename);
                        break;
                    case 'csv':
                        // Convert to CSV format (flatten data for CSV)
                        const csvData = Array.isArray(reportData.data) ? reportData.data : [
                            reportData.data || reportData
                        ];
                        exportToCSV(csvData, {
                            filename: filename || 'report'
                        });
                        break;
                    default:
                        throw new Error(`Unsupported export format: ${format}`);
                }
            } catch (error) {
                console.error('Export failed:', error);
                throw error;
            }
        }
    }["useReportGeneration.useCallback[exportReport]"], []);
    return {
        generateComprehensiveReport: generateReport,
        generateIndividualReport,
        generateAggregateReport,
        exportReport,
        isGenerating,
        error
    };
};
_s(useReportGeneration, "3U+lpcGb0pYXt0tTA6br1VIbpfQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSecureApiClient"]
    ];
});
const useReportHistory = (filters)=>{
    _s1();
    const { client } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSecureApiClient"])();
    const queryParams = new URLSearchParams();
    if (filters?.type) queryParams.append('type', filters.type);
    if (filters?.entityType) queryParams.append('entityType', filters.entityType);
    const historyQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'report-history',
        filters
    ], {
        "useReportHistory.useApiQuery[historyQuery]": async ()=>{
            const response = await client.get(`/api/reporting/reports/history?${queryParams.toString()}`);
            return response;
        }
    }["useReportHistory.useApiQuery[historyQuery]"], {
        cacheDuration: 2 * 60 * 1000,
        enableRetry: true
    });
    return {
        reports: historyQuery.data?.reports || [],
        pagination: historyQuery.data?.pagination,
        isLoading: historyQuery.isLoading,
        error: historyQuery.error,
        refetch: historyQuery.refetch
    };
};
_s1(useReportHistory, "YvC/f3VGnENbXxcHmAD91dh4hDc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSecureApiClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useReportDownload = ()=>{
    _s2();
    const [isDownloading, setIsDownloading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [downloadError, setDownloadError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const { client } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSecureApiClient"])();
    const downloadReport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useReportDownload.useCallback[downloadReport]": async (reportId)=>{
            setIsDownloading(true);
            setDownloadError(null);
            try {
                const response = await client.get(`/api/reporting/reports/${reportId}/download`);
                // For now, just show the response since actual file download isn't implemented yet
                console.log('Download result:', response);
                // TODO: Implement actual file download when backend file storage is ready
                alert('Download functionality will be implemented with file storage');
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : 'Failed to download report';
                setDownloadError(errorMessage);
                throw err;
            } finally{
                setIsDownloading(false);
            }
        }
    }["useReportDownload.useCallback[downloadReport]"], [
        client
    ]);
    return {
        downloadReport,
        isDownloading,
        downloadError
    };
};
_s2(useReportDownload, "oKLVi7scwJ/v7dZm3tcz/b1u37E=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSecureApiClient"]
    ];
});
const useReportTemplates = ()=>{
    _s3();
    const { client } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSecureApiClient"])();
    const templatesQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'report-templates'
    ], {
        "useReportTemplates.useApiQuery[templatesQuery]": async ()=>{
            const response = await client.get('/api/reporting/reports/templates');
            // Ensure we return an array, handle different response structures
            const data = response;
            if (Array.isArray(data)) {
                return data;
            }
            // If response has nested data property (API wrapper format)
            if (data && Array.isArray(data.data)) {
                return data.data;
            }
            // Fallback to empty array if data is not in expected format
            console.warn('Report templates API returned unexpected format:', data);
            return [];
        }
    }["useReportTemplates.useApiQuery[templatesQuery]"], {
        cacheDuration: 10 * 60 * 1000,
        enableRetry: true
    });
    return {
        templates: Array.isArray(templatesQuery.data) ? templatesQuery.data : [],
        isLoading: templatesQuery.isLoading,
        error: templatesQuery.error,
        refetch: templatesQuery.refetch
    };
};
_s3(useReportTemplates, "9bl73ubKCux2j1xIbb6m7W+3ZIo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSecureApiClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/hooks/useReportTypes.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file useReportTypes.ts
 * @description Hook for managing report types following existing patterns
 */ __turbopack_context__.s({
    "useReportType": (()=>useReportType),
    "useReportTypes": (()=>useReportTypes),
    "useReportTypesByCategory": (()=>useReportTypesByCategory)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <locals>");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
;
;
;
const useReportTypes = ()=>{
    _s();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    // Query for fetching all report types
    const reportTypesQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'report-types'
    ], {
        "useReportTypes.useApiQuery[reportTypesQuery]": async ()=>{
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get('/reporting/report-types');
            // Extract the data array from the API response structure
            return result.data?.data || [];
        }
    }["useReportTypes.useApiQuery[reportTypesQuery]"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
    // Mutation for creating a new report type
    const createReportType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useReportTypes.useMutation[createReportType]": async (reportTypeData)=>{
                const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].post('/reporting/report-types', reportTypeData);
                // Extract the data from the API response structure
                return result.data?.data || result.data;
            }
        }["useReportTypes.useMutation[createReportType]"],
        onSuccess: {
            "useReportTypes.useMutation[createReportType]": ()=>{
                // Invalidate and refetch report types
                queryClient.invalidateQueries({
                    queryKey: [
                        'report-types'
                    ]
                });
            }
        }["useReportTypes.useMutation[createReportType]"]
    });
    // Mutation for updating an existing report type
    const updateReportType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useReportTypes.useMutation[updateReportType]": async ({ id, ...reportTypeData })=>{
                const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].put(`/reporting/report-types/${id}`, reportTypeData);
                // Extract the data from the API response structure
                return result.data?.data || result.data;
            }
        }["useReportTypes.useMutation[updateReportType]"],
        onSuccess: {
            "useReportTypes.useMutation[updateReportType]": ()=>{
                // Invalidate and refetch report types
                queryClient.invalidateQueries({
                    queryKey: [
                        'report-types'
                    ]
                });
            }
        }["useReportTypes.useMutation[updateReportType]"]
    });
    // Mutation for deleting a report type
    const deleteReportType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useReportTypes.useMutation[deleteReportType]": async (id)=>{
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].delete(`/reporting/report-types/${id}`);
            }
        }["useReportTypes.useMutation[deleteReportType]"],
        onSuccess: {
            "useReportTypes.useMutation[deleteReportType]": ()=>{
                // Invalidate and refetch report types
                queryClient.invalidateQueries({
                    queryKey: [
                        'report-types'
                    ]
                });
            }
        }["useReportTypes.useMutation[deleteReportType]"]
    });
    // Mutation for duplicating a report type
    const duplicateReportType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useReportTypes.useMutation[duplicateReportType]": async (id)=>{
                const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].post(`/reporting/report-types/${id}/duplicate`);
                // Extract the data from the API response structure
                return result.data?.data || result.data;
            }
        }["useReportTypes.useMutation[duplicateReportType]"],
        onSuccess: {
            "useReportTypes.useMutation[duplicateReportType]": ()=>{
                // Invalidate and refetch report types
                queryClient.invalidateQueries({
                    queryKey: [
                        'report-types'
                    ]
                });
            }
        }["useReportTypes.useMutation[duplicateReportType]"]
    });
    // Mutation for toggling report type active status
    const toggleReportTypeActive = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: {
            "useReportTypes.useMutation[toggleReportTypeActive]": async ({ id, isActive })=>{
                const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].patch(`/reporting/report-types/${id}/toggle-active`, {
                    isActive
                });
                // Extract the data from the API response structure
                return result.data?.data || result.data;
            }
        }["useReportTypes.useMutation[toggleReportTypeActive]"],
        onSuccess: {
            "useReportTypes.useMutation[toggleReportTypeActive]": ()=>{
                // Invalidate and refetch report types
                queryClient.invalidateQueries({
                    queryKey: [
                        'report-types'
                    ]
                });
            }
        }["useReportTypes.useMutation[toggleReportTypeActive]"]
    });
    return {
        // Mutations
        createReportType,
        // Query data
        data: reportTypesQuery.data,
        deleteReportType,
        duplicateReportType,
        error: reportTypesQuery.error,
        isLoading: reportTypesQuery.isLoading,
        // Utility functions
        refetch: reportTypesQuery.refetch,
        toggleReportTypeActive,
        updateReportType
    };
};
_s(useReportTypes, "tfwpoEe6d+F+gAWUo9lQj2QsLh4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMutation"]
    ];
});
const useReportType = (id)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'report-type',
        id
    ], {
        "useReportType.useApiQuery": async ()=>{
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/report-types/${id}`);
            // Extract the data from the API response structure
            return result.data?.data || result.data;
        }
    }["useReportType.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enabled: !!id,
        enableRetry: true
    });
};
_s1(useReportType, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useReportTypesByCategory = (category)=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'report-types',
        'category',
        category
    ], {
        "useReportTypesByCategory.useApiQuery": async ()=>{
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/report-types?category=${encodeURIComponent(category)}`);
            // Extract the data array from the API response structure
            return result.data?.data || [];
        }
    }["useReportTypesByCategory.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enabled: !!category,
        enableRetry: true
    });
};
_s2(useReportTypesByCategory, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/hooks/useVehicleAnalytics.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file useVehicleAnalytics.ts
 * @description Hook for fetching vehicle analytics data following existing patterns
 */ __turbopack_context__.s({
    "useVehicleAnalytics": (()=>useVehicleAnalytics),
    "useVehicleCostAnalytics": (()=>useVehicleCostAnalytics),
    "useVehicleMaintenanceSchedule": (()=>useVehicleMaintenanceSchedule),
    "useVehicleUtilization": (()=>useVehicleUtilization)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <locals>");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
;
;
const useVehicleAnalytics = (filters)=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'vehicle-analytics',
        filters
    ], {
        "useVehicleAnalytics.useApiQuery": async ()=>{
            const queryParams = new URLSearchParams();
            if (filters?.dateRange) {
                // Defensive programming: Ensure dates are Date objects
                const fromDate = filters.dateRange.from instanceof Date ? filters.dateRange.from : new Date(filters.dateRange.from);
                const toDate = filters.dateRange.to instanceof Date ? filters.dateRange.to : new Date(filters.dateRange.to);
                queryParams.append('dateRange.from', fromDate.toISOString().split('T')[0] || fromDate.toISOString());
                queryParams.append('dateRange.to', toDate.toISOString().split('T')[0] || toDate.toISOString());
            }
            if (filters?.vehicles) {
                filters.vehicles.forEach({
                    "useVehicleAnalytics.useApiQuery": (vehicle)=>queryParams.append('vehicles', vehicle.toString())
                }["useVehicleAnalytics.useApiQuery"]);
            }
            if (filters?.serviceTypes) {
                filters.serviceTypes.forEach({
                    "useVehicleAnalytics.useApiQuery": (type)=>queryParams.append('serviceTypes', type)
                }["useVehicleAnalytics.useApiQuery"]);
            }
            if (filters?.serviceStatus) {
                filters.serviceStatus.forEach({
                    "useVehicleAnalytics.useApiQuery": (status)=>queryParams.append('serviceStatus', status)
                }["useVehicleAnalytics.useApiQuery"]);
            }
            const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
            return result.data || result;
        }
    }["useVehicleAnalytics.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true,
        retryAttempts: 3
    });
};
_s(useVehicleAnalytics, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useVehicleUtilization = (filters)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'vehicle-utilization',
        filters
    ], {
        "useVehicleUtilization.useApiQuery": async ()=>{
            // Get utilization data from vehicle analytics endpoint
            const queryParams = new URLSearchParams();
            if (filters?.dateRange) {
                queryParams.append('from', filters.dateRange.from.toISOString());
                queryParams.append('to', filters.dateRange.to.toISOString());
            }
            if (filters?.vehicles) {
                filters.vehicles.forEach({
                    "useVehicleUtilization.useApiQuery": (vehicle)=>queryParams.append('vehicles', vehicle.toString())
                }["useVehicleUtilization.useApiQuery"]);
            }
            const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
            const vehicleAnalytics = result.data || result;
            // Return the utilization metrics from vehicle analytics
            return vehicleAnalytics.utilizationMetrics || [];
        }
    }["useVehicleUtilization.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
_s1(useVehicleUtilization, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useVehicleMaintenanceSchedule = (filters)=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'vehicle-maintenance-schedule',
        filters
    ], {
        "useVehicleMaintenanceSchedule.useApiQuery": async ()=>{
            // Get maintenance data from vehicle analytics endpoint
            const queryParams = new URLSearchParams();
            if (filters?.dateRange) {
                queryParams.append('from', filters.dateRange.from.toISOString());
                queryParams.append('to', filters.dateRange.to.toISOString());
            }
            if (filters?.vehicles) {
                filters.vehicles.forEach({
                    "useVehicleMaintenanceSchedule.useApiQuery": (vehicle)=>queryParams.append('vehicles', vehicle.toString())
                }["useVehicleMaintenanceSchedule.useApiQuery"]);
            }
            const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
            const vehicleAnalytics = result.data || result;
            // Return the maintenance schedule from vehicle analytics
            return vehicleAnalytics.maintenanceSchedule || [];
        }
    }["useVehicleMaintenanceSchedule.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
_s2(useVehicleMaintenanceSchedule, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useVehicleCostAnalytics = (filters)=>{
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'vehicle-cost-analytics',
        filters
    ], {
        "useVehicleCostAnalytics.useApiQuery": async ()=>{
            const queryParams = new URLSearchParams();
            if (filters?.dateRange) {
                queryParams.append('from', filters.dateRange.from.toISOString());
                queryParams.append('to', filters.dateRange.to.toISOString());
            }
            if (filters?.vehicles) {
                filters.vehicles.forEach({
                    "useVehicleCostAnalytics.useApiQuery": (vehicle)=>queryParams.append('vehicles', vehicle.toString())
                }["useVehicleCostAnalytics.useApiQuery"]);
            }
            if (filters?.serviceTypes) {
                filters.serviceTypes.forEach({
                    "useVehicleCostAnalytics.useApiQuery": (type)=>queryParams.append('serviceTypes', type)
                }["useVehicleCostAnalytics.useApiQuery"]);
            }
            const url = `/reporting/vehicle-costs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
            return result.data || result;
        }
    }["useVehicleCostAnalytics.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
_s3(useVehicleCostAnalytics, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/hooks/useEmployeeAnalytics.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file useEmployeeAnalytics.ts
 * @description Hook for fetching employee analytics data following existing patterns
 */ __turbopack_context__.s({
    "useEmployeeAnalytics": (()=>useEmployeeAnalytics),
    "useEmployeeAvailability": (()=>useEmployeeAvailability),
    "useEmployeePerformance": (()=>useEmployeePerformance),
    "useEmployeeTaskAssignments": (()=>useEmployeeTaskAssignments),
    "useEmployeeWorkload": (()=>useEmployeeWorkload)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <locals>");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature();
;
;
const useEmployeeAnalytics = (filters)=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'employee-analytics',
        filters
    ], {
        "useEmployeeAnalytics.useApiQuery": async ()=>{
            const queryParams = new URLSearchParams();
            if (filters?.dateRange) {
                // Defensive programming: Ensure dates are Date objects
                const fromDate = filters.dateRange.from instanceof Date ? filters.dateRange.from : new Date(filters.dateRange.from);
                const toDate = filters.dateRange.to instanceof Date ? filters.dateRange.to : new Date(filters.dateRange.to);
                queryParams.append('dateRange.from', fromDate.toISOString().split('T')[0] || fromDate.toISOString());
                queryParams.append('dateRange.to', toDate.toISOString().split('T')[0] || toDate.toISOString());
            }
            if (filters?.employees) {
                filters.employees.forEach({
                    "useEmployeeAnalytics.useApiQuery": (employee)=>queryParams.append('employees', employee.toString())
                }["useEmployeeAnalytics.useApiQuery"]);
            }
            if (filters?.locations) {
                filters.locations.forEach({
                    "useEmployeeAnalytics.useApiQuery": (location)=>queryParams.append('locations', location)
                }["useEmployeeAnalytics.useApiQuery"]);
            }
            if (filters?.includeEmployeeMetrics) {
                queryParams.append('includeEmployeeMetrics', 'true');
            }
            const url = `/reporting/employees/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
            return result.data || result;
        }
    }["useEmployeeAnalytics.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true,
        retryAttempts: 3
    });
};
_s(useEmployeeAnalytics, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useEmployeePerformance = (filters)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'employee-performance',
        filters
    ], {
        "useEmployeePerformance.useApiQuery": async ()=>{
            const queryParams = new URLSearchParams();
            if (filters?.dateRange) {
                queryParams.append('from', filters.dateRange.from.toISOString());
                queryParams.append('to', filters.dateRange.to.toISOString());
            }
            if (filters?.employees) {
                filters.employees.forEach({
                    "useEmployeePerformance.useApiQuery": (employee)=>queryParams.append('employees', employee.toString())
                }["useEmployeePerformance.useApiQuery"]);
            }
            const url = `/api/reporting/employee-performance${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Failed to fetch employee performance: ${response.statusText}`);
            }
            return response.json();
        }
    }["useEmployeePerformance.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
_s1(useEmployeePerformance, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useEmployeeWorkload = (filters)=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'employee-workload',
        filters
    ], {
        "useEmployeeWorkload.useApiQuery": async ()=>{
            const queryParams = new URLSearchParams();
            if (filters?.dateRange) {
                queryParams.append('from', filters.dateRange.from.toISOString());
                queryParams.append('to', filters.dateRange.to.toISOString());
            }
            if (filters?.employees) {
                filters.employees.forEach({
                    "useEmployeeWorkload.useApiQuery": (employee)=>queryParams.append('employees', employee.toString())
                }["useEmployeeWorkload.useApiQuery"]);
            }
            const url = `/api/reporting/employee-workload${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Failed to fetch employee workload: ${response.statusText}`);
            }
            return response.json();
        }
    }["useEmployeeWorkload.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
_s2(useEmployeeWorkload, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useEmployeeTaskAssignments = (filters)=>{
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'employee-task-assignments',
        filters
    ], {
        "useEmployeeTaskAssignments.useApiQuery": async ()=>{
            const queryParams = new URLSearchParams();
            if (filters?.dateRange) {
                queryParams.append('from', filters.dateRange.from.toISOString());
                queryParams.append('to', filters.dateRange.to.toISOString());
            }
            if (filters?.employees) {
                filters.employees.forEach({
                    "useEmployeeTaskAssignments.useApiQuery": (employee)=>queryParams.append('employees', employee.toString())
                }["useEmployeeTaskAssignments.useApiQuery"]);
            }
            const url = `/api/reporting/employee-tasks${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Failed to fetch employee task assignments: ${response.statusText}`);
            }
            return response.json();
        }
    }["useEmployeeTaskAssignments.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
_s3(useEmployeeTaskAssignments, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useEmployeeAvailability = (filters)=>{
    _s4();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'employee-availability',
        filters
    ], {
        "useEmployeeAvailability.useApiQuery": async ()=>{
            const queryParams = new URLSearchParams();
            if (filters?.dateRange) {
                queryParams.append('from', filters.dateRange.from.toISOString());
                queryParams.append('to', filters.dateRange.to.toISOString());
            }
            if (filters?.employees) {
                filters.employees.forEach({
                    "useEmployeeAvailability.useApiQuery": (employee)=>queryParams.append('employees', employee.toString())
                }["useEmployeeAvailability.useApiQuery"]);
            }
            const url = `/api/reporting/employee-availability${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Failed to fetch employee availability: ${response.statusText}`);
            }
            return response.json();
        }
    }["useEmployeeAvailability.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
_s4(useEmployeeAvailability, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/hooks/useCrossEntityAnalytics.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file useCrossEntityAnalytics.ts
 * @description Hook for fetching cross-entity analytics data following existing patterns
 */ __turbopack_context__.s({
    "useCrossEntityAnalytics": (()=>useCrossEntityAnalytics),
    "useEmployeeVehicleCorrelations": (()=>useEmployeeVehicleCorrelations),
    "usePerformanceWorkloadCorrelations": (()=>usePerformanceWorkloadCorrelations),
    "useTaskDelegationCorrelations": (()=>useTaskDelegationCorrelations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <locals>");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
;
;
const useCrossEntityAnalytics = (filters)=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'cross-entity-analytics',
        filters
    ], {
        "useCrossEntityAnalytics.useApiQuery": async ()=>{
            const queryParams = new URLSearchParams();
            if (filters?.dateRange) {
                // Defensive programming: Ensure dates are Date objects
                const fromDate = filters.dateRange.from instanceof Date ? filters.dateRange.from : new Date(filters.dateRange.from);
                const toDate = filters.dateRange.to instanceof Date ? filters.dateRange.to : new Date(filters.dateRange.to);
                queryParams.append('dateRange.from', fromDate.toISOString().split('T')[0] || fromDate.toISOString());
                queryParams.append('dateRange.to', toDate.toISOString().split('T')[0] || toDate.toISOString());
            }
            if (filters?.employees) {
                filters.employees.forEach({
                    "useCrossEntityAnalytics.useApiQuery": (employee)=>queryParams.append('employees', employee.toString())
                }["useCrossEntityAnalytics.useApiQuery"]);
            }
            if (filters?.vehicles) {
                filters.vehicles.forEach({
                    "useCrossEntityAnalytics.useApiQuery": (vehicle)=>queryParams.append('vehicles', vehicle.toString())
                }["useCrossEntityAnalytics.useApiQuery"]);
            }
            if (filters?.locations) {
                filters.locations.forEach({
                    "useCrossEntityAnalytics.useApiQuery": (location)=>queryParams.append('locations', location)
                }["useCrossEntityAnalytics.useApiQuery"]);
            }
            // Note: includeCorrelations doesn't exist in ReportingFilters type
            // if (filters?.includeCorrelations) {
            //   queryParams.append('includeCorrelations', 'true');
            // }
            const url = `/reporting/cross-entity/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
            return result.data || result;
        }
    }["useCrossEntityAnalytics.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true,
        retryAttempts: 3
    });
};
_s(useCrossEntityAnalytics, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useEmployeeVehicleCorrelations = (filters)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'employee-vehicle-correlations',
        filters
    ], {
        "useEmployeeVehicleCorrelations.useApiQuery": async ()=>{
            const queryParams = new URLSearchParams();
            if (filters?.dateRange) {
                queryParams.append('from', filters.dateRange.from.toISOString());
                queryParams.append('to', filters.dateRange.to.toISOString());
            }
            if (filters?.employees) {
                filters.employees.forEach({
                    "useEmployeeVehicleCorrelations.useApiQuery": (employee)=>queryParams.append('employees', employee.toString())
                }["useEmployeeVehicleCorrelations.useApiQuery"]);
            }
            if (filters?.vehicles) {
                filters.vehicles.forEach({
                    "useEmployeeVehicleCorrelations.useApiQuery": (vehicle)=>queryParams.append('vehicles', vehicle.toString())
                }["useEmployeeVehicleCorrelations.useApiQuery"]);
            }
            const url = `/reporting/employee-vehicle-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
            return result.data || result;
        }
    }["useEmployeeVehicleCorrelations.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
_s1(useEmployeeVehicleCorrelations, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useTaskDelegationCorrelations = (filters)=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'task-delegation-correlations',
        filters
    ], {
        "useTaskDelegationCorrelations.useApiQuery": async ()=>{
            const queryParams = new URLSearchParams();
            if (filters?.dateRange) {
                queryParams.append('from', filters.dateRange.from.toISOString());
                queryParams.append('to', filters.dateRange.to.toISOString());
            }
            const url = `/reporting/task-delegation-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
            return result.data || result;
        }
    }["useTaskDelegationCorrelations.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
_s2(useTaskDelegationCorrelations, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const usePerformanceWorkloadCorrelations = (filters)=>{
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'performance-workload-correlations',
        filters
    ], {
        "usePerformanceWorkloadCorrelations.useApiQuery": async ()=>{
            const queryParams = new URLSearchParams();
            if (filters?.dateRange) {
                queryParams.append('from', filters.dateRange.from.toISOString());
                queryParams.append('to', filters.dateRange.to.toISOString());
            }
            if (filters?.employees) {
                filters.employees.forEach({
                    "usePerformanceWorkloadCorrelations.useApiQuery": (employee)=>queryParams.append('employees', employee.toString())
                }["usePerformanceWorkloadCorrelations.useApiQuery"]);
            }
            const url = `/reporting/performance-workload-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
            return result.data || result;
        }
    }["usePerformanceWorkloadCorrelations.useApiQuery"], {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
_s3(usePerformanceWorkloadCorrelations, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/components/DateRangePicker.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file DateRangePicker.tsx
 * @description Date range picker component for report filtering
 */ __turbopack_context__.s({
    "DateRangePicker": (()=>DateRangePicker)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$calendar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/calendar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/popover.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/alert.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as CalendarIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-client] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-client] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/subDays.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subWeeks$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/subWeeks.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subMonths$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/subMonths.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfMonth$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/startOfMonth.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfMonth$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/endOfMonth.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfYear$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/startOfYear.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfYear$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/endOfYear.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$differenceInDays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/differenceInDays.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isAfter$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/isAfter.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isBefore$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/isBefore.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/isValid.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
/**
 * Predefined date range options with enhanced defaults
 */ const PRESET_RANGES = [
    {
        label: 'Today',
        getValue: ()=>({
                from: new Date(),
                to: new Date()
            })
    },
    {
        label: 'Yesterday',
        getValue: ()=>{
            const yesterday = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(new Date(), 1);
            return {
                from: yesterday,
                to: yesterday
            };
        }
    },
    {
        label: 'Last 3 days',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(new Date(), 2),
                to: new Date()
            })
    },
    {
        label: 'Last 7 days',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(new Date(), 6),
                to: new Date()
            })
    },
    {
        label: 'Last 2 weeks',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subWeeks$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subWeeks"])(new Date(), 2),
                to: new Date()
            })
    },
    {
        label: 'Last 30 days',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(new Date(), 29),
                to: new Date()
            })
    },
    {
        label: 'This week',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subDays"])(new Date(), new Date().getDay()),
                to: new Date()
            })
    },
    {
        label: 'This month',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfMonth$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfMonth"])(new Date()),
                to: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfMonth$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfMonth"])(new Date())
            })
    },
    {
        label: 'Last month',
        getValue: ()=>{
            const lastMonth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subMonths$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subMonths"])(new Date(), 1);
            return {
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfMonth$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfMonth"])(lastMonth),
                to: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfMonth$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfMonth"])(lastMonth)
            };
        }
    },
    {
        label: 'Last 3 months',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subMonths$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subMonths"])(new Date(), 3),
                to: new Date()
            })
    },
    {
        label: 'This year',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfYear$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["startOfYear"])(new Date()),
                to: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfYear$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["endOfYear"])(new Date())
            })
    }
];
const DateRangePicker = ({ value, onChange, placeholder = 'Select date range', className, disabled = false, maxDays = 365, minDays = 1, maxDate = new Date(), minDate = new Date(2020, 0, 1), showValidation = true })=>{
    _s();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [tempRange, setTempRange] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(value || null);
    // Real-time validation with memoization for performance
    const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "DateRangePicker.useMemo[validation]": ()=>{
            if (!tempRange) {
                return {
                    isValid: true
                };
            }
            const { from, to } = tempRange;
            // Validate date objects
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValid"])(from) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValid"])(to)) {
                return {
                    isValid: false,
                    message: 'Invalid date selected',
                    type: 'error'
                };
            }
            // Check date order
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isAfter$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isAfter"])(from, to)) {
                return {
                    isValid: false,
                    message: 'Start date must be before end date',
                    type: 'error'
                };
            }
            // Check date range limits
            if (minDate && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isBefore$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isBefore"])(from, minDate)) {
                return {
                    isValid: false,
                    message: `Start date cannot be before ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(minDate, 'MMM dd, yyyy')}`,
                    type: 'error'
                };
            }
            if (maxDate && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isAfter$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isAfter"])(to, maxDate)) {
                return {
                    isValid: false,
                    message: `End date cannot be after ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(maxDate, 'MMM dd, yyyy')}`,
                    type: 'error'
                };
            }
            // Check range duration
            const daysDiff = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$differenceInDays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["differenceInDays"])(to, from) + 1;
            if (daysDiff < minDays) {
                return {
                    isValid: false,
                    message: `Date range must be at least ${minDays} day${minDays > 1 ? 's' : ''}`,
                    type: 'error'
                };
            }
            if (daysDiff > maxDays) {
                return {
                    isValid: false,
                    message: `Date range cannot exceed ${maxDays} days`,
                    type: 'error'
                };
            }
            // Performance warning for large ranges
            if (daysDiff > 90) {
                return {
                    isValid: true,
                    message: `Large date range (${daysDiff} days) may affect performance`,
                    type: 'warning'
                };
            }
            return {
                isValid: true,
                message: `${daysDiff} day${daysDiff > 1 ? 's' : ''} selected`,
                type: 'info'
            };
        }
    }["DateRangePicker.useMemo[validation]"], [
        tempRange,
        maxDays,
        minDays,
        maxDate,
        minDate
    ]);
    // Update temp range when value changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DateRangePicker.useEffect": ()=>{
            setTempRange(value || null);
        }
    }["DateRangePicker.useEffect"], [
        value
    ]);
    /**
   * Handle preset range selection with validation
   */ const handlePresetSelect = (preset)=>{
        const range = preset.getValue();
        setTempRange(range);
        // Only call onChange if validation passes
        const tempValidation = validateRange(range);
        if (tempValidation.isValid) {
            onChange?.(range);
            setIsOpen(false);
        }
    };
    /**
   * Validate a date range
   */ const validateRange = (range)=>{
        if (!range) return {
            isValid: true
        };
        const { from, to } = range;
        const daysDiff = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$differenceInDays$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["differenceInDays"])(to, from) + 1;
        if (daysDiff > maxDays) {
            return {
                isValid: false,
                message: `Date range cannot exceed ${maxDays} days`,
                type: 'error'
            };
        }
        return {
            isValid: true
        };
    };
    /**
   * Handle custom date selection with enhanced logic
   */ const handleDateSelect = (date)=>{
        if (!date || disabled) return;
        // Validate date is within allowed range
        if (minDate && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isBefore$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isBefore"])(date, minDate)) return;
        if (maxDate && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isAfter$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isAfter"])(date, maxDate)) return;
        if (!tempRange || tempRange.from && tempRange.to) {
            // Start new range
            const newRange = {
                from: date,
                to: date
            };
            setTempRange(newRange);
        } else if (tempRange.from && !tempRange.to) {
            // Complete the range
            const newRange = {
                from: date < tempRange.from ? date : tempRange.from,
                to: date < tempRange.from ? tempRange.from : date
            };
            setTempRange(newRange);
            // Only call onChange if validation passes
            const tempValidation = validateRange(newRange);
            if (tempValidation.isValid) {
                onChange?.(newRange);
                setIsOpen(false);
            }
        }
    };
    /**
   * Handle range clear
   */ const handleClear = ()=>{
        setTempRange(null);
        onChange?.(null);
        setIsOpen(false);
    };
    /**
   * Format display text
   */ const getDisplayText = ()=>{
        if (!value) return placeholder;
        if (value.from.toDateString() === value.to.toDateString()) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(value.from, 'MMM dd, yyyy');
        }
        return `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(value.from, 'MMM dd, yyyy')} - ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(value.to, 'MMM dd, yyyy')}`;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('relative', className),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Popover"], {
            open: isOpen,
            onOpenChange: setIsOpen,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverTrigger"], {
                    asChild: true,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        variant: "outline",
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('w-full justify-start text-left font-normal', !value && 'text-muted-foreground'),
                        disabled: disabled,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarIcon$3e$__["CalendarIcon"], {
                                className: "mr-2 h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                lineNumber: 363,
                                columnNumber: 13
                            }, this),
                            getDisplayText(),
                            value && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                variant: "secondary",
                                className: "ml-auto",
                                children: [
                                    Math.ceil((value.to.getTime() - value.from.getTime()) / (1000 * 60 * 60 * 24)) + 1,
                                    ' ',
                                    "days"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                lineNumber: 366,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                        lineNumber: 355,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                    lineNumber: 354,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PopoverContent"], {
                    className: "w-auto max-w-4xl p-0",
                    align: "start",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "border-r p-4 space-y-2 min-w-[160px]",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "font-medium text-sm text-gray-900 mb-3",
                                        children: "Quick Select"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                        lineNumber: 380,
                                        columnNumber: 15
                                    }, this),
                                    PRESET_RANGES.map((preset)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "ghost",
                                            size: "sm",
                                            className: "w-full justify-start text-xs h-8 px-2",
                                            onClick: ()=>handlePresetSelect(preset),
                                            children: preset.label
                                        }, preset.label, false, {
                                            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                            lineNumber: 384,
                                            columnNumber: 17
                                        }, this)),
                                    value && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "border-t pt-2 mt-3",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                variant: "ghost",
                                                size: "sm",
                                                className: "w-full justify-start text-sm text-red-600 hover:text-red-700",
                                                onClick: handleClear,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                                        className: "mr-2 h-3 w-3"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                        lineNumber: 404,
                                                        columnNumber: 23
                                                    }, this),
                                                    "Clear"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                lineNumber: 398,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                            lineNumber: 397,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                lineNumber: 379,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-4 min-w-[600px]",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$calendar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Calendar"], {
                                        mode: "range",
                                        selected: tempRange || undefined,
                                        onSelect: (range)=>{
                                            if (range && range.from) {
                                                const dateRange = {
                                                    from: range.from,
                                                    to: range.to || range.from
                                                };
                                                setTempRange(dateRange);
                                                // Auto-apply valid ranges
                                                const tempValidation = validateRange(dateRange);
                                                if (tempValidation.isValid && dateRange.from && dateRange.to) {
                                                    onChange?.(dateRange);
                                                    setIsOpen(false);
                                                }
                                            } else {
                                                setTempRange(null);
                                            }
                                        },
                                        numberOfMonths: 2,
                                        className: "rounded-md border-0",
                                        disabled: [
                                            // Disable dates outside the allowed range
                                            ...minDate ? [
                                                {
                                                    before: minDate
                                                }
                                            ] : [],
                                            ...maxDate ? [
                                                {
                                                    after: maxDate
                                                }
                                            ] : []
                                        ],
                                        showOutsideDays: true,
                                        fixedWeeks: true
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                        lineNumber: 414,
                                        columnNumber: 15
                                    }, this),
                                    tempRange && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-4 p-3 bg-gray-50 rounded-lg",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                className: "font-medium text-sm text-gray-900 mb-2",
                                                children: "Selected Range"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                lineNumber: 451,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-1 text-sm text-gray-600",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            "From: ",
                                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(tempRange.from, 'MMM dd, yyyy')
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                        lineNumber: 455,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            "To: ",
                                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(tempRange.to, 'MMM dd, yyyy')
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                        lineNumber: 456,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xs text-gray-500",
                                                        children: [
                                                            Math.ceil((tempRange.to.getTime() - tempRange.from.getTime()) / (1000 * 60 * 60 * 24)) + 1,
                                                            ' ',
                                                            "days"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                        lineNumber: 457,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                lineNumber: 454,
                                                columnNumber: 19
                                            }, this),
                                            showValidation && validation.message && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Alert"], {
                                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('mt-3 py-2 px-3', validation.type === 'error' && 'border-red-200 bg-red-50', validation.type === 'warning' && 'border-yellow-200 bg-yellow-50', validation.type === 'info' && 'border-blue-200 bg-blue-50'),
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        validation.type === 'error' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                                                            className: "h-3 w-3 text-red-600"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                            lineNumber: 481,
                                                            columnNumber: 27
                                                        }, this),
                                                        validation.type === 'warning' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                                                            className: "h-3 w-3 text-yellow-600"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                            lineNumber: 484,
                                                            columnNumber: 27
                                                        }, this),
                                                        validation.type === 'info' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                                            className: "h-3 w-3 text-blue-600"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                            lineNumber: 487,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDescription"], {
                                                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('text-xs', validation.type === 'error' && 'text-red-700', validation.type === 'warning' && 'text-yellow-700', validation.type === 'info' && 'text-blue-700'),
                                                            children: validation.message
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                            lineNumber: 489,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                    lineNumber: 479,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                lineNumber: 468,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                        lineNumber: 450,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                lineNumber: 413,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                        lineNumber: 377,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                    lineNumber: 376,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
            lineNumber: 353,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
        lineNumber: 352,
        columnNumber: 5
    }, this);
};
_s(DateRangePicker, "ihaAMTR9KVI1C/D9DC5xiMTFnuU=");
_c = DateRangePicker;
var _c;
__turbopack_context__.k.register(_c, "DateRangePicker");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_components_features_reporting_a1a434ba._.js.map