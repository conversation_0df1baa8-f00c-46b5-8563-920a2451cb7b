(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8147],{3567:(e,s,a)=>{"use strict";a.d(s,{R:()=>t});var r=a(35476);function t(e){return+(0,r.a)(e)<Date.now()}},19968:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(40157).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},24371:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(40157).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},56193:(e,s,a)=>{Promise.resolve().then(a.bind(a,88981))},56819:(e,s,a)=>{"use strict";a.d(s,{z:()=>m});var r=a(95155),t=a(41784),l=a(3567),i=a(31949),n=a(44956),d=a(65064);a(12115);var c=a(1350),o=a(99673);function m(e){let{className:s="",tasks:a,onDelete:m,onBulkDelete:x,onBulkArchive:h}=e,g=e=>{try{return(0,t.GP)(new Date(e),"MMM d, yyyy h:mm a")}catch(e){return"Invalid date"}},u=e=>e.deadline&&(0,l.R)(new Date(e.deadline))&&"Completed"!==e.status&&"Cancelled"!==e.status,p=e=>e.staffEmployeeId?e.staffEmployee?(0,o.DV)(e.staffEmployee):"Staff ID: ".concat(e.staffEmployeeId):"Unassigned",j=[(0,c.BZ)(),(0,c.K)("description","Description",{maxLength:50,className:"max-w-xs"}),(0,c.ZI)("status","Status",{Assigned:{variant:"default",label:"Assigned"},Cancelled:{variant:"secondary",label:"Cancelled"},Completed:{variant:"success",label:"Completed"},In_Progress:{variant:"default",label:"In Progress"},Pending:{variant:"warning",label:"Pending"}}),(0,c.ZI)("priority","Priority",{High:{variant:"destructive",label:"High"},Medium:{variant:"warning",label:"Medium"},Low:{variant:"secondary",label:"Low"}}),{accessorKey:"staffEmployeeId",header:"Assignee",cell:e=>{let{row:s}=e,a=s.original;return(0,r.jsx)("span",{className:a.staffEmployeeId?"":"text-muted-foreground",children:p(a)})}},{accessorKey:"dateTime",header:"Start Time",cell:e=>{let{row:s}=e;return g(s.getValue("dateTime"))}},{accessorKey:"deadline",header:"Deadline",cell:e=>{let{row:s}=e,a=s.original,t=a.deadline;if(!t)return(0,r.jsx)("span",{className:"text-muted-foreground",children:"No deadline"});let l=u(a);return(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{className:l?"text-red-600 font-medium":"",children:g(t)}),l&&(0,r.jsx)(i.A,{"aria-label":"Overdue",className:"size-4 text-red-600"})]})}},(0,c.K)("location","Location",{maxLength:30,className:"max-w-xs"}),(0,c.Wy)({viewHref:e=>"/tasks/".concat(e.id),editHref:e=>"/tasks/".concat(e.id,"/edit"),...m&&{onDelete:e=>{m(e)}},showCopyId:!0})],f=[...x?[{label:"Delete Selected",icon:e=>{let{className:s}=e;return(0,r.jsx)(n.A,{className:s})},onClick:async e=>{await x(e)},variant:"destructive"}]:[],...h?[{label:"Archive Selected",icon:e=>{let{className:s}=e;return(0,r.jsx)(d.A,{className:s})},onClick:async e=>{await h(e)}}]:[]];return(0,r.jsx)(c.bQ,{data:a,columns:j,className:s,searchPlaceholder:"Search tasks by description or location...",searchColumn:"description",emptyMessage:"No tasks found. Create your first task to get started.",pageSize:20,enableRowSelection:!0,enableBulkActions:f.length>0,bulkActions:f,enableColumnVisibility:!0,tableClassName:"shadow-lg",headerClassName:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20",rowClassName:"hover:bg-blue-50/50 dark:hover:bg-blue-900/10"})}},88981:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>em});var r=a(95155);let t=(0,a(40157).A)("ClipboardCheck",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]);var l=a(34301),i=a(18271),n=a(6874),d=a.n(n),c=a(12115),o=a(88240),m=a(38342),x=a(97697);let h={entityType:"task",title:"Task Dashboard",description:"Oversee all tasks, assignments, and progress.",viewModes:["cards","table","list"],defaultViewMode:"cards",enableBulkActions:!0,enableExport:!0,refreshInterval:3e4},g=e=>{let{className:s=""}=e,{layout:a,monitoring:t,setViewMode:l,setGridColumns:i,toggleCompactMode:n,setMonitoringEnabled:d,setRefreshInterval:c,toggleAutoRefresh:o,resetSettings:g}=(0,x.fX)("task")();return(0,r.jsx)(m.s,{config:h,entityType:"task",layout:a,monitoring:t,setViewMode:l,setGridColumns:i,toggleCompactMode:n,setMonitoringEnabled:d,setRefreshInterval:c,toggleAutoRefresh:o,resetSettings:g,className:s})};var u=a(37648),p=a(91721),j=a(11133),f=a(8376),b=a(24371),v=a(27300),N=a(50286),y=a(98328),k=a(75074),w=a(9572),C=a(25318),A=a(26126),z=a(30285),E=a(62523),S=a(85057),M=a(22346),R=a(38382),I=a(14636),D=a(47262),P=a(85511),L=a(54036),T=a(41784);let $=[{value:"Pending",label:"Pending",icon:u.A,color:"text-amber-600 bg-amber-50 border-amber-200 dark:text-amber-400 dark:bg-amber-900/20 dark:border-amber-800"},{value:"Assigned",label:"Assigned",icon:p.A,color:"text-blue-600 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-900/20 dark:border-blue-800"},{value:"In_Progress",label:"In Progress",icon:j.A,color:"text-purple-600 bg-purple-50 border-purple-200 dark:text-purple-400 dark:bg-purple-900/20 dark:border-purple-800"},{value:"Completed",label:"Completed",icon:f.A,color:"text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800"},{value:"Cancelled",label:"Cancelled",icon:b.A,color:"text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800"}],F=[{value:"Low",label:"Low Priority",color:"text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800"},{value:"Medium",label:"Medium Priority",color:"text-amber-600 bg-amber-50 border-amber-200 dark:text-amber-400 dark:bg-amber-900/20 dark:border-amber-800"},{value:"High",label:"High Priority",color:"text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800"}],H=e=>{let{onFiltersChange:s,className:a,initialFilters:t={},employeesList:l=[]}=e,[i,n]=(0,c.useState)({search:"",status:[],priority:[],assignee:[],dateRange:{},...t}),[d,o]=(0,c.useState)(!1),m=e=>{let a={...i,...e};n(a),null==s||s(a)},x=()=>{let e={search:"",status:[],priority:[],assignee:[],dateRange:{}};n(e),null==s||s(e)},h=e=>{m({status:i.status.includes(e)?i.status.filter(s=>s!==e):[...i.status,e]})},g=e=>{m({priority:i.priority.includes(e)?i.priority.filter(s=>s!==e):[...i.priority,e]})},u=e=>{m({assignee:i.assignee.includes(e)?i.assignee.filter(s=>s!==e):[...i.assignee,e]})},j=e=>{var s,a;m({dateRange:{from:null!=(s=null==e?void 0:e.from)?s:void 0,to:null!=(a=null==e?void 0:e.to)?a:void 0}})},b=+!!i.search+i.status.length+i.priority.length+i.assignee.length+(i.dateRange.from||i.dateRange.to?1:0);return(0,r.jsxs)("div",{className:(0,L.cn)("flex flex-col gap-4",a),children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(k.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,r.jsx)(E.p,{placeholder:"Search tasks...",value:i.search,onChange:e=>m({search:e.target.value}),className:"pl-10"})]}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,r.jsxs)("div",{className:"hidden md:flex items-center gap-2",children:[(0,r.jsx)(()=>(0,r.jsxs)(I.AM,{children:[(0,r.jsx)(I.Wv,{asChild:!0,children:(0,r.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,r.jsx)(f.A,{className:"size-4"}),"Status",i.status.length>0&&(0,r.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:i.status.length})]})}),(0,r.jsx)(I.hl,{className:"w-56 p-3",align:"start",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h4",{className:"font-medium text-sm",children:"Task Status"}),(0,r.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>m({status:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,r.jsx)(M.w,{}),(0,r.jsx)("div",{className:"space-y-2",children:$.map(e=>{let s=e.icon;return(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(D.S,{id:"status-".concat(e.value),checked:i.status.includes(e.value),onCheckedChange:()=>h(e.value)}),(0,r.jsxs)(S.J,{htmlFor:"status-".concat(e.value),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,r.jsx)(s,{className:"size-3"}),(0,r.jsx)(A.E,{variant:"outline",className:(0,L.cn)("text-xs border",e.color),children:e.label})]})]},e.value)})})]})})]}),{}),(0,r.jsx)(()=>(0,r.jsxs)(I.AM,{children:[(0,r.jsx)(I.Wv,{asChild:!0,children:(0,r.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,r.jsx)(v.A,{className:"size-4"}),"Priority",i.priority.length>0&&(0,r.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:i.priority.length})]})}),(0,r.jsx)(I.hl,{className:"w-48 p-3",align:"start",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h4",{className:"font-medium text-sm",children:"Priority Level"}),(0,r.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>m({priority:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,r.jsx)(M.w,{}),(0,r.jsx)("div",{className:"space-y-2",children:F.map(e=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(D.S,{id:"priority-".concat(e.value),checked:i.priority.includes(e.value),onCheckedChange:()=>g(e.value)}),(0,r.jsx)(S.J,{htmlFor:"priority-".concat(e.value),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:(0,r.jsx)(A.E,{variant:"outline",className:(0,L.cn)("text-xs border",e.color),children:e.label})})]},e.value))})]})})]}),{}),l&&l.length>0&&(0,r.jsx)(()=>(0,r.jsxs)(I.AM,{children:[(0,r.jsx)(I.Wv,{asChild:!0,children:(0,r.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,r.jsx)(N.A,{className:"size-4"}),"Assignee",i.assignee.length>0&&(0,r.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:i.assignee.length})]})}),(0,r.jsx)(I.hl,{className:"w-64 p-3",align:"start",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h4",{className:"font-medium text-sm",children:"Assigned Employee"}),(0,r.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>m({assignee:[]}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,r.jsx)(M.w,{}),(0,r.jsxs)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(D.S,{id:"assignee-unassigned",checked:i.assignee.includes("unassigned"),onCheckedChange:()=>u("unassigned")}),(0,r.jsxs)(S.J,{htmlFor:"assignee-unassigned",className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,r.jsx)(p.A,{className:"size-3 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-muted-foreground",children:"Unassigned"})]})]}),null==l?void 0:l.map(e=>(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(D.S,{id:"assignee-".concat(e.id),checked:i.assignee.includes(e.id),onCheckedChange:()=>u(e.id)}),(0,r.jsxs)(S.J,{htmlFor:"assignee-".concat(e.id),className:"flex items-center gap-2 cursor-pointer text-sm flex-1",children:[(0,r.jsx)(p.A,{className:"size-3"}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{children:e.name}),(0,r.jsx)("span",{className:"text-xs text-muted-foreground",children:e.role})]})]})]},e.id))]})]})})]}),{}),(0,r.jsx)(()=>(0,r.jsxs)(I.AM,{children:[(0,r.jsx)(I.Wv,{asChild:!0,children:(0,r.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,r.jsx)(y.A,{className:"size-4"}),"Date Range",(i.dateRange.from||i.dateRange.to)&&(0,r.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:"1"})]})}),(0,r.jsx)(I.hl,{className:"w-auto p-0",align:"start",children:(0,r.jsxs)("div",{className:"p-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("h4",{className:"font-medium text-sm",children:"Task Date Range"}),(0,r.jsx)(z.$,{variant:"ghost",size:"sm",onClick:()=>m({dateRange:{}}),className:"h-auto p-1 text-xs",children:"Clear"})]}),(0,r.jsx)(P.V,{mode:"range",selected:{from:i.dateRange.from,to:i.dateRange.to},onSelect:j,numberOfMonths:2,className:"rounded-md border-0"}),(0,r.jsx)("div",{className:"mt-3 text-xs text-muted-foreground text-center",children:i.dateRange.from&&!i.dateRange.to?"Select end date to complete range":"Click start date, then end date"})]})})]}),{})]}),(0,r.jsx)("div",{className:"md:hidden",children:(0,r.jsxs)(R.cj,{open:d,onOpenChange:o,children:[(0,r.jsx)(R.CG,{asChild:!0,children:(0,r.jsxs)(z.$,{variant:"outline",className:"gap-2",children:[(0,r.jsx)(w.A,{className:"size-4"}),"Filters",b>0&&(0,r.jsx)(A.E,{variant:"secondary",className:"ml-1 h-5 min-w-5 px-1.5 text-xs",children:b})]})}),(0,r.jsxs)(R.h,{side:"bottom",className:"h-[80vh]",children:[(0,r.jsxs)(R.Fm,{children:[(0,r.jsx)(R.qp,{children:"Filter Tasks"}),(0,r.jsx)(R.Qs,{children:"Refine your task list with advanced filters"})]}),(0,r.jsxs)("div",{className:"grid gap-6 py-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(S.J,{className:"text-sm font-medium",children:"Status"}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-2",children:$.map(e=>{let s=e.icon;return(0,r.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,r.jsx)(D.S,{id:"mobile-status-".concat(e.value),checked:i.status.includes(e.value),onCheckedChange:()=>h(e.value)}),(0,r.jsxs)(S.J,{htmlFor:"mobile-status-".concat(e.value),className:"flex items-center gap-1 cursor-pointer text-xs flex-1",children:[(0,r.jsx)(s,{className:"size-3"}),e.label]})]},e.value)})})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(S.J,{className:"text-sm font-medium",children:"Priority"}),(0,r.jsx)("div",{className:"grid gap-2",children:F.map(e=>(0,r.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,r.jsx)(D.S,{id:"mobile-priority-".concat(e.value),checked:i.priority.includes(e.value),onCheckedChange:()=>g(e.value)}),(0,r.jsx)(S.J,{htmlFor:"mobile-priority-".concat(e.value),className:"cursor-pointer text-sm flex-1",children:e.label})]},e.value))})]}),l&&l.length>0&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(S.J,{className:"text-sm font-medium",children:"Assignee"}),(0,r.jsxs)("div",{className:"grid gap-2 max-h-48 overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,r.jsx)(D.S,{id:"mobile-assignee-unassigned",checked:i.assignee.includes("unassigned"),onCheckedChange:()=>u("unassigned")}),(0,r.jsx)(S.J,{htmlFor:"mobile-assignee-unassigned",className:"cursor-pointer text-sm flex-1",children:"Unassigned"})]}),l.map(e=>(0,r.jsxs)("div",{className:"flex items-center gap-2 p-2 border rounded-md",children:[(0,r.jsx)(D.S,{id:"mobile-assignee-".concat(e.id),checked:i.assignee.includes(e.id),onCheckedChange:()=>u(e.id)}),(0,r.jsx)(S.J,{htmlFor:"mobile-assignee-".concat(e.id),className:"cursor-pointer text-sm flex-1",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:e.name}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:e.role})]})})]},e.id))]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(S.J,{className:"text-sm font-medium",children:"Date Range"}),(0,r.jsx)("div",{className:"border rounded-md p-3",children:(0,r.jsx)(P.V,{mode:"range",selected:{from:i.dateRange.from,to:i.dateRange.to},onSelect:j,numberOfMonths:1,className:"rounded-md border-0"})})]}),(0,r.jsx)(z.$,{variant:"outline",onClick:x,className:"w-full",children:"Clear All Filters"})]})]})]})}),b>0&&(0,r.jsxs)(z.$,{variant:"ghost",size:"sm",onClick:x,className:"gap-1 text-muted-foreground hover:text-foreground hidden md:flex",children:[(0,r.jsx)(C.A,{className:"size-3"}),"Clear (",b,")"]})]}),b>0&&(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[i.search&&(0,r.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:['Search: "',i.search,'"',(0,r.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>m({search:""}),children:(0,r.jsx)(C.A,{className:"size-3"})})]}),i.status.map(e=>{let s=$.find(s=>s.value===e);return s?(0,r.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:[s.label,(0,r.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>h(e),children:(0,r.jsx)(C.A,{className:"size-3"})})]},e):null}),i.priority.map(e=>{let s=F.find(s=>s.value===e);return s?(0,r.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:[s.label,(0,r.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>g(e),children:(0,r.jsx)(C.A,{className:"size-3"})})]},e):null}),i.assignee.map(e=>{let s=null==l?void 0:l.find(s=>s.id===e),a="unassigned"===e?"Unassigned":(null==s?void 0:s.name)||"Unknown";return(0,r.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:["Assignee: ",a,(0,r.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>u(e),children:(0,r.jsx)(C.A,{className:"size-3"})})]},e)}),(i.dateRange.from||i.dateRange.to)&&(0,r.jsxs)(A.E,{variant:"secondary",className:"gap-1",children:["Date:"," ",i.dateRange.from?(0,T.GP)(i.dateRange.from,"MMM d"):"?"," ","-"," ",i.dateRange.to?(0,T.GP)(i.dateRange.to,"MMM d, yyyy"):"?",(0,r.jsx)(z.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>m({dateRange:{}}),children:(0,r.jsx)(C.A,{className:"size-3"})})]})]})]})};var J=a(83343),V=a(83662),O=a(28328),_=a(31949),G=a(19968),U=a(6560),B=a(66695),W=a(99673);let K=e=>{switch(e){case"Assigned":return"bg-blue-500/20 text-blue-700 border-blue-500/30 dark:text-blue-400 dark:bg-blue-500/10 dark:border-blue-500/20";case"Cancelled":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Completed":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"In_Progress":return"bg-indigo-500/20 text-indigo-700 border-indigo-500/30 dark:text-indigo-400 dark:bg-indigo-500/10 dark:border-indigo-500/20";case"Pending":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},Z=e=>{switch(e){case"High":return"bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20";case"Low":return"bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20";case"Medium":return"bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20";default:return"bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20"}},q=e=>{if(!e)return"N/A";try{return(0,T.GP)((0,J.H)(e),"MMM d, yyyy HH:mm")}catch(e){return"Invalid Date"}};function X(e){let{task:s}=e,a=!!s.staffEmployeeId,t=!!s.driverEmployeeId,l=!!s.vehicleId;return(0,r.jsxs)(B.Zp,{className:"flex h-full flex-col overflow-hidden border-border/60 bg-card shadow-md",children:[(0,r.jsxs)(B.aR,{className:"p-5",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,r.jsx)(B.ZB,{className:"line-clamp-2 text-lg font-semibold text-primary",title:s.description,children:s.description}),(0,r.jsxs)("div",{className:"flex shrink-0 flex-col items-end gap-1",children:[(0,r.jsx)(A.E,{className:(0,L.cn)("text-xs py-1 px-2 font-semibold",K(s.status)),children:s.status}),(0,r.jsxs)(A.E,{className:(0,L.cn)("text-xs py-1 px-2 font-semibold",Z(s.priority)),children:[s.priority," Priority"]})]})]}),(0,r.jsxs)(B.BT,{className:"flex items-center pt-1 text-sm text-muted-foreground",children:[(0,r.jsx)(V.A,{className:"mr-1.5 size-4 shrink-0 text-accent"}),s.location]})]}),(0,r.jsxs)(B.Wu,{className:"flex grow flex-col p-5",children:[(0,r.jsx)(M.w,{className:"my-3 bg-border/50"}),(0,r.jsxs)("div",{className:"grow space-y-2.5 text-sm text-foreground",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(y.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Start: "}),(0,r.jsx)("strong",{className:"font-semibold",children:q(s.dateTime)})]})]}),s.deadline&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Deadline: "}),(0,r.jsx)("strong",{className:"font-semibold",children:q(s.deadline)})]})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Duration: "}),(0,r.jsxs)("strong",{className:"font-semibold",children:[s.estimatedDuration," mins"]})]})]}),a&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Staff: "}),(0,r.jsx)("strong",{className:"font-semibold",children:s.staffEmployee?(0,W.DV)(s.staffEmployee):"ID: ".concat(s.staffEmployeeId)})]})]}),t&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Driver: "}),(0,r.jsx)("strong",{className:"font-semibold",children:s.driverEmployee?(0,W.DV)(s.driverEmployee):"ID: ".concat(s.driverEmployeeId)})]})]}),l&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(O.A,{className:"mr-2.5 size-4 shrink-0 text-accent"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Vehicle: "}),(0,r.jsx)("strong",{className:"font-semibold",children:s.vehicle?"".concat(s.vehicle.make," ").concat(s.vehicle.model," (").concat(s.vehicle.licensePlate||"ID: ".concat(s.vehicle.id),")"):"ID: ".concat(s.vehicleId)})]})]}),!a&&"Completed"!==s.status&&"Cancelled"!==s.status&&(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(_.A,{className:"mr-2.5 size-4 shrink-0 text-destructive"}),(0,r.jsx)("strong",{className:"font-semibold text-destructive",children:"No Staff Assigned"})]})]}),s.notes&&(0,r.jsx)("p",{className:"mt-3 line-clamp-2 border-t border-dashed border-border/50 pt-2 text-xs text-muted-foreground",title:s.notes,children:s.notes})]}),(0,r.jsx)(B.wL,{className:"border-t border-border/60 bg-muted/20 p-4",children:(0,r.jsx)(U.r,{actionType:"tertiary",asChild:!0,className:"w-full",icon:(0,r.jsx)(G.A,{className:"size-4"}),children:(0,r.jsx)(d(),{href:"/tasks/".concat(s.id),children:"View Details"})})})]})}var Y=a(56819);let Q=e=>{let{className:s="",compactMode:a,tasks:t,gridColumns:l=3,viewMode:i}=e;switch(i){case"list":return(0,r.jsx)("div",{className:(0,L.cn)("flex flex-col",a?"gap-2":"gap-4",s),children:t.map(e=>(0,r.jsx)(X,{task:e},e.id))});case"table":return(0,r.jsx)(Y.z,{className:s,tasks:t});default:return(0,r.jsx)("div",{className:(0,L.cn)("grid grid-cols-1 gap-6","md:grid-cols-2 lg:grid-cols-".concat(l),a&&"gap-3",s),children:t.map(e=>(0,r.jsx)(X,{task:e},e.id))})}};var ee=a(24865),es=a(89440),ea=a(54165),er=a(77023),et=a(95647),el=a(83761),ei=a(61051),en=a(80937),ed=a(21354);function ec(){return(0,r.jsxs)("div",{className:"flex h-full flex-col overflow-hidden rounded-lg border-border/60 bg-card shadow-md",children:[(0,r.jsxs)("div",{className:"flex grow flex-col p-5",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsx)(er.jt,{className:"mb-1 h-7 w-3/5 bg-muted/50",count:1,variant:"default"}),(0,r.jsx)(er.jt,{className:"mb-1 h-5 w-1/4 rounded-full bg-muted/50",count:1,variant:"default"})]}),(0,r.jsx)(er.jt,{className:"mb-3 h-4 w-1/2 bg-muted/50",count:1,variant:"default"}),(0,r.jsx)(er.jt,{className:"my-3 h-px w-full bg-border/50",count:1,variant:"default"}),(0,r.jsx)("div",{className:"grow space-y-2.5",children:Array.from({length:3}).map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(er.jt,{className:"mr-2.5 size-5 rounded-full bg-muted/50",count:1,variant:"default"}),(0,r.jsx)(er.jt,{className:"h-5 w-2/3 bg-muted/50",count:1,variant:"default"})]},s))})]}),(0,r.jsx)("div",{className:"border-t border-border/60 bg-muted/20 p-4",children:(0,r.jsx)(er.jt,{className:"h-10 w-full bg-muted/50",count:1,variant:"default"})})]})}let eo=()=>{var e,s,a;let{layout:n}=(0,x.fX)("task")(),{data:o=[],error:m,isLoading:h,refetch:u}=(0,ei.si)(),{data:p=[],error:j,isLoading:f,refetch:b}=(0,el.nR)(),{data:v=[],error:N,isLoading:y,refetch:k}=(0,en.T$)(),[w,C]=(0,c.useState)(""),[A,z]=(0,c.useState)("all"),[E,S]=(0,c.useState)("all"),[M,R]=(0,c.useState)("all"),[I,D]=(0,c.useState)({}),P=(0,c.useMemo)(()=>p.map(e=>{var s;return{id:String(e.id),name:null!=(s=e.fullName)?s:e.name,role:e.role}}),[p]),L=(0,c.useMemo)(()=>{let e=[...o.map(e=>(0,ed.R)(e,p,v))],s=w.toLowerCase();return"all"!==A&&(e=e.filter(e=>e.status===A)),"all"!==E&&(e=e.filter(e=>e.priority===E)),"all"!==M&&(e=e.filter(e=>{var s;return null!=(s=e.staffEmployeeId&&String(e.staffEmployeeId)===M||e.driverEmployeeId&&String(e.driverEmployeeId)===M)?s:"unassigned"===M&&!e.staffEmployeeId&&!e.driverEmployeeId})),s&&(e=e.filter(e=>{var a,r,t;let l=e.staffEmployeeId?P.find(s=>s.id===String(e.staffEmployeeId)):null,i=e.driverEmployeeId?P.find(s=>s.id===String(e.driverEmployeeId)):null;return null!=(t=null!=(r=e.description.toLowerCase().includes(s)||e.location.toLowerCase().includes(s)||(null==(a=e.notes)?void 0:a.toLowerCase().includes(s)))?r:null==l?void 0:l.name.toLowerCase().includes(s))?t:null==i?void 0:i.name.toLowerCase().includes(s)})),(I.from||I.to)&&(e=e.filter(e=>{let s=new Date(e.createdAt);return I.from&&I.to?s>=I.from&&s<=I.to:I.from?s>=I.from:!I.to||s<=I.to})),e},[w,o,p,v,A,E,M,P,I.from,I.to]),T=(0,c.useCallback)(async()=>{await Promise.all([u(),b(),k()])},[u,b,k]),$=w||"all"!==A||"all"!==E||"all"!==M;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(es.AppBreadcrumb,{homeHref:"/",homeLabel:"Dashboard"}),(0,r.jsx)(et.z,{description:"Oversee all tasks, assignments, and progress.",icon:t,title:"Manage Tasks",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(U.r,{actionType:"primary",asChild:!0,icon:(0,r.jsx)(l.A,{className:"size-4"}),children:(0,r.jsx)(d(),{href:"/tasks/add",children:"Add New Task"})}),(0,r.jsx)(ee.M,{getReportUrl:()=>{let e=new URLSearchParams({employee:M,priority:E,searchTerm:w,status:A}).toString();return"/tasks/report?".concat(e)},isList:!0}),(0,r.jsxs)(ea.lG,{children:[(0,r.jsx)(ea.zM,{asChild:!0,children:(0,r.jsx)(U.r,{actionType:"secondary",icon:(0,r.jsx)(i.A,{className:"size-4"}),children:"Settings"})}),(0,r.jsxs)(ea.Cf,{className:"sm:max-w-[600px]",children:[(0,r.jsx)(ea.L3,{children:"Dashboard Settings"}),(0,r.jsx)(ea.rr,{children:"Customize how tasks are displayed and managed."}),(0,r.jsx)(g,{})]})]})]})}),(0,r.jsx)(H,{employeesList:P,initialFilters:{assignee:"all"===M?[]:[M],dateRange:I,priority:"all"===E?[]:[E],search:w,status:"all"===A?[]:[A]},onFiltersChange:e=>{C(e.search),z(e.status.length>0?e.status[0]:"all"),S(e.priority.length>0?e.priority[0]:"all"),R(e.assignee.length>0?e.assignee[0]:"all"),D(e.dateRange)}}),(0,r.jsx)(er.gO,{data:L,emptyComponent:(0,r.jsxs)("div",{className:"rounded-lg bg-card py-12 text-center shadow-md",children:[(0,r.jsx)(t,{className:"mx-auto mb-6 size-16 text-muted-foreground"}),(0,r.jsx)("h3",{className:"mb-2 text-2xl font-semibold text-foreground",children:$?"No Tasks Match Your Filters":"No Tasks Created Yet"}),(0,r.jsx)("p",{className:"mx-auto mb-6 mt-2 max-w-md text-muted-foreground",children:$?"Try adjusting your search or filter criteria.":"It looks like you haven't created any tasks yet. Get started by adding one."}),!$&&(0,r.jsx)(U.r,{actionType:"primary",asChild:!0,icon:(0,r.jsx)(l.A,{className:"size-4"}),size:"lg",children:(0,r.jsx)(d(),{href:"/tasks/add",children:"Create Your First Task"})})]}),error:null!=(a=null!=(s=null!=(e=null==m?void 0:m.message)?e:null==j?void 0:j.message)?s:null==N?void 0:N.message)?a:null,isLoading:h||f||y,loadingComponent:(0,r.jsx)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3",children:Array.from({length:3}).map((e,s)=>(0,r.jsx)(ec,{},s))}),onRetry:T,children:e=>(0,r.jsx)(Q,{compactMode:n.compactMode,gridColumns:n.gridColumns,tasks:e,viewMode:"calendar"===n.viewMode?"cards":n.viewMode})})]})};function em(){return(0,r.jsx)(o.A,{children:(0,r.jsx)(eo,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6476,1137,3860,9664,1263,5495,1859,6874,5247,6463,7454,3030,6233,3769,4036,8658,111,7515,3615,5320,6554,1051,5916,8986,8441,1684,7358],()=>s(56193)),_N_E=e.O()}]);