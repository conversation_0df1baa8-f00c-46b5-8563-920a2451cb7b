"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8986],{14636:(e,t,s)=>{s.d(t,{AM:()=>n,Wv:()=>o,hl:()=>c});var a=s(95155),r=s(20547),l=s(12115),i=s(54036);let n=r.bL,o=r.l9;r.bm;let c=l.forwardRef((e,t)=>{let{align:s="center",className:l,sideOffset:n=4,...o}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{align:s,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",l),ref:t,sideOffset:n,...o})})});c.displayName=r.UC.displayName},30356:(e,t,s)=>{s.d(t,{C:()=>c,z:()=>o});var a=s(95155),r=s(54059),l=s(70154),i=s(12115),n=s(54036);let o=i.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)(r.bL,{className:(0,n.cn)("grid gap-2",s),...l,ref:t})});o.displayName=r.bL.displayName;let c=i.forwardRef((e,t)=>{let{className:s,...i}=e;return(0,a.jsx)(r.q7,{className:(0,n.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...i,children:(0,a.jsx)(r.C1,{className:"flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"size-2.5 fill-current text-current"})})})});c.displayName=r.q7.displayName},38342:(e,t,s)=>{s.d(t,{s:()=>y});var a=s(95155),r=s(66655),l=s(71978),i=s(34214),n=s(51920),o=s(29471),c=s(18271),d=s(89829),u=s(67554);s(12115);var m=s(30285),h=s(85057),f=s(30356),p=s(76202),g=s(80333),x=s(17313);let v={cards:{icon:r.A,label:"Cards"},table:{icon:l.A,label:"Table"},list:{icon:i.A,label:"List"},calendar:{icon:n.A,label:"Calendar"},grid:{icon:o.A,label:"Grid"}},y=e=>{let{config:t,entityType:s,layout:l,monitoring:i,setViewMode:n,setGridColumns:o,toggleCompactMode:y,setMonitoringEnabled:b,setRefreshInterval:j,toggleAutoRefresh:N,resetSettings:w,className:C=""}=e,S=t.viewModes||["cards","table","list"];return(0,a.jsxs)("div",{className:"space-y-6 p-4 ".concat(C),children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"size-6"}),t.title," Settings"]}),(0,a.jsxs)("p",{className:"text-muted-foreground",children:["Customize your ",s," dashboard experience"]})]}),(0,a.jsxs)(x.tU,{className:"w-full",defaultValue:"layout",children:[(0,a.jsxs)(x.j7,{className:"grid w-full grid-cols-3",children:[(0,a.jsx)(x.Xi,{value:"layout",children:"Layout"}),(0,a.jsx)(x.Xi,{value:"display",children:"Display"}),(0,a.jsx)(x.Xi,{value:"refresh",children:"Refresh"})]}),(0,a.jsx)(x.av,{className:"mt-4 space-y-6",value:"layout",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{className:"text-lg font-semibold",children:"View Mode"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Choose how ",s,"s are displayed"]}),(0,a.jsx)(f.z,{className:"grid grid-cols-2 gap-4 pt-2",onValueChange:e=>n(e),value:l.viewMode,children:S.map(e=>{var t,s;let l=(null==(t=v[e])?void 0:t.icon)||r.A,i=(null==(s=v[e])?void 0:s.label)||e;return(0,a.jsxs)(h.J,{className:"flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground [&:has([data-state=checked])]:border-primary cursor-pointer",htmlFor:"layout-".concat(e),children:[(0,a.jsx)(f.C,{className:"sr-only",id:"layout-".concat(e),value:e}),(0,a.jsx)(l,{className:"mb-3 size-6"}),i]},e)})})]}),("cards"===l.viewMode||"grid"===l.viewMode)&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(h.J,{className:"text-lg font-semibold",children:["Grid Columns: ",l.gridColumns]}),(0,a.jsx)(p.A,{defaultValue:[l.gridColumns],max:6,min:1,onValueChange:e=>{let[t]=e;return void 0!==t&&o(t)},step:1}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-muted-foreground",children:[(0,a.jsx)("span",{children:"1 column"}),(0,a.jsx)("span",{children:"6 columns"})]})]})]})}),(0,a.jsx)(x.av,{className:"mt-4 space-y-6",value:"display",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(h.J,{className:"text-lg font-semibold",children:"Compact Mode"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Show more ",s,"s in less space"]})]}),(0,a.jsx)(g.d,{checked:l.compactMode,onCheckedChange:y})]}),t.enableBulkActions&&(0,a.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(h.J,{className:"text-lg font-semibold",children:"Bulk Actions"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable selection and bulk operations"})]}),(0,a.jsx)(g.d,{checked:!0,disabled:!0})]})]})}),(0,a.jsx)(x.av,{className:"mt-4 space-y-6",value:"refresh",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsxs)(h.J,{className:"text-lg font-semibold flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"size-4"}),"Auto Refresh"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Automatically refresh ",s," data"]})]}),(0,a.jsx)(g.d,{checked:i.autoRefresh,onCheckedChange:N})]}),i.autoRefresh&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(h.J,{className:"text-lg font-semibold flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"size-4"}),"Refresh Interval"]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{label:"5 seconds",value:5e3},{label:"10 seconds",value:1e4},{label:"30 seconds",value:3e4},{label:"1 minute",value:6e4},{label:"5 minutes",value:3e5}].map(e=>(0,a.jsx)(m.$,{variant:i.refreshInterval===e.value?"default":"outline",size:"sm",onClick:()=>j(e.value),children:e.label},e.value))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between space-x-2",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(h.J,{className:"text-lg font-semibold",children:"Real-time Updates"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Enable live data updates"})]}),(0,a.jsx)(g.d,{checked:i.enabled,onCheckedChange:b})]})]})})]}),(0,a.jsx)("div",{className:"flex justify-end pt-4 border-t",children:(0,a.jsx)(m.$,{onClick:w,variant:"outline",children:"Reset to Defaults"})})]})}},76202:(e,t,s)=>{s.d(t,{A:()=>n});var a=s(95155),r=s(54073),l=s(12115),i=s(54036);let n=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsxs)(r.bL,{className:(0,i.cn)("relative flex w-full touch-none select-none items-center",s),ref:t,...l,children:[(0,a.jsx)(r.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,a.jsx)(r.Q6,{className:"absolute h-full bg-primary"})}),(0,a.jsx)(r.zi,{className:"block size-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]})});n.displayName=r.bL.displayName},80333:(e,t,s)=>{s.d(t,{d:()=>n});var a=s(95155),r=s(4884),l=s(12115),i=s(54036);let n=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)(r.bL,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...l,ref:t,children:(0,a.jsx)(r.zi,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});n.displayName=r.bL.displayName},80937:(e,t,s)=>{s.d(t,{NS:()=>f,T$:()=>d,W_:()=>u,Y1:()=>m,lR:()=>h});var a=s(26715),r=s(5041),l=s(90111),i=s(42366),n=s(99605),o=s(75908);let c={all:["vehicles"],detail:e=>["vehicles",e]},d=e=>(0,l.GK)([...c.all],async()=>(await o.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),u=(e,t)=>{var s;return(0,l.GK)([...c.detail(e)],()=>o.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(null==(s=null==t?void 0:t.enabled)||s),staleTime:3e5,...t})},m=()=>{let e=(0,a.jE)(),{showError:t,showSuccess:s}=(0,i.useNotifications)();return(0,r.n)({mutationFn:e=>{let t=n.M.toCreateRequest(e);return o.vehicleApiService.create(t)},onError:e=>{t("Failed to create vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:t=>{e.invalidateQueries({queryKey:c.all}),s('Vehicle "'.concat(t.licensePlate,'" has been created successfully!'))}})},h=()=>{let e=(0,a.jE)(),{showError:t,showSuccess:s}=(0,i.useNotifications)();return(0,r.n)({mutationFn:e=>{let{data:t,id:s}=e,a=n.M.toUpdateRequest(t);return o.vehicleApiService.update(s,a)},onError:e=>{t("Failed to update vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:t=>{e.invalidateQueries({queryKey:c.all}),e.invalidateQueries({queryKey:c.detail(t.id)}),s('Vehicle "'.concat(t.licensePlate,'" has been updated successfully!'))}})},f=()=>{let e=(0,a.jE)(),{showError:t,showSuccess:s}=(0,i.useNotifications)();return(0,r.n)({mutationFn:e=>o.vehicleApiService.delete(e),onError:e=>{t("Failed to delete vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:(t,a)=>{e.invalidateQueries({queryKey:c.all}),e.removeQueries({queryKey:c.detail(a)}),s("Vehicle has been deleted successfully!")}})}},85511:(e,t,s)=>{s.d(t,{V:()=>c});var a=s(95155),r=s(965),l=s(73158);s(12115);var i=s(33683),n=s(30285),o=s(54036);function c(e){let{className:t,classNames:s,showOutsideDays:c=!0,...d}=e;return(0,a.jsx)(i.hv,{className:(0,o.cn)("p-3",t),classNames:{caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,o.cn)((0,n.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_disabled:"text-muted-foreground opacity-50",day_hidden:"invisible",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_range_end:"day-range-end",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",head_row:"flex",month:"space-y-4",months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",nav:"space-x-1 flex items-center",nav_button:(0,o.cn)((0,n.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_next:"absolute right-1",nav_button_previous:"absolute left-1",row:"flex w-full mt-2",table:"w-full border-collapse space-y-1",...s},components:{IconLeft:e=>{let{className:t,...s}=e;return(0,a.jsx)(r.A,{className:(0,o.cn)("h-4 w-4",t),...s})},IconRight:e=>{let{className:t,...s}=e;return(0,a.jsx)(l.A,{className:(0,o.cn)("h-4 w-4",t),...s})}},showOutsideDays:c,...d})}c.displayName="Calendar"},88240:(e,t,s)=>{s.d(t,{A:()=>d});var a=s(95155),r=s(31949),l=s(67554),i=s(12115),n=s(55365),o=s(30285);class c extends i.Component{static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,t){this.setState({errorInfo:t}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",t.componentStack),this.props.onError&&this.props.onError(e,t)}render(){let{description:e="An unexpected error occurred.",resetLabel:t="Try Again",title:s="Something went wrong"}=this.props;if(this.state.hasError){var i;return this.props.fallback?this.props.fallback:(0,a.jsxs)(n.Fc,{className:"my-4",variant:"destructive",children:[(0,a.jsx)(r.A,{className:"mr-2 size-4"}),(0,a.jsx)(n.XL,{className:"text-lg font-semibold",children:s}),(0,a.jsxs)(n.TN,{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-2",children:(null==(i=this.state.error)?void 0:i.message)||e}),!1,(0,a.jsxs)(o.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,a.jsx)(l.A,{className:"mr-2 size-4"}),t]})]})]})}return this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}}let d=c},97697:(e,t,s)=>{s.d(t,{fX:()=>n});var a=s(12115),r=s(65453),l=s(46786);let i=new Map;function n(e){return(0,a.useMemo)(()=>(i.has(e)||i.set(e,(0,r.v)()((0,l.lt)((0,l.Zr)((e,t)=>({activeTab:"all",layout:{viewMode:"cards",gridColumns:3,compactMode:!1,showFilters:!0,showSettings:!1},monitoring:{enabled:!1,refreshInterval:3e4,autoRefresh:!0,pausedDataTypes:new Set},filters:{},sortBy:"createdAt",sortDirection:"desc",selectedItems:new Set,searchTerm:"",setActiveTab:t=>e({activeTab:t},!1,"setActiveTab"),setViewMode:t=>e(e=>({layout:{...e.layout,viewMode:t}}),!1,"setViewMode"),setGridColumns:t=>e(e=>({layout:{...e.layout,gridColumns:t}}),!1,"setGridColumns"),toggleCompactMode:()=>e(e=>({layout:{...e.layout,compactMode:!e.layout.compactMode}}),!1,"toggleCompactMode"),toggleFilters:()=>e(e=>({layout:{...e.layout,showFilters:!e.layout.showFilters}}),!1,"toggleFilters"),toggleSettings:()=>e(e=>({layout:{...e.layout,showSettings:!e.layout.showSettings}}),!1,"toggleSettings"),updateFilter:(t,s)=>e(e=>({filters:{...e.filters,[t]:s}}),!1,"updateFilter"),clearFilters:()=>e({filters:{}},!1,"clearFilters"),setSorting:(t,s)=>e({sortBy:t,sortDirection:s},!1,"setSorting"),setSearchTerm:t=>e({searchTerm:t},!1,"setSearchTerm"),toggleItemSelection:t=>e(e=>{let s=new Set(e.selectedItems);return s.has(t)?s.delete(t):s.add(t),{selectedItems:s}},!1,"toggleItemSelection"),clearSelection:()=>e({selectedItems:new Set},!1,"clearSelection"),selectAll:t=>e({selectedItems:new Set(t)},!1,"selectAll"),setMonitoringEnabled:t=>e(e=>({monitoring:{...e.monitoring,enabled:t}}),!1,"setMonitoringEnabled"),setRefreshInterval:t=>e(e=>({monitoring:{...e.monitoring,refreshInterval:t}}),!1,"setRefreshInterval"),toggleAutoRefresh:()=>e(e=>({monitoring:{...e.monitoring,autoRefresh:!e.monitoring.autoRefresh}}),!1,"toggleAutoRefresh"),pauseDataType:t=>e(e=>({monitoring:{...e.monitoring,pausedDataTypes:new Set([...e.monitoring.pausedDataTypes,t])}}),!1,"pauseDataType"),resumeDataType:t=>e(e=>{let s=new Set(e.monitoring.pausedDataTypes);return s.delete(t),{monitoring:{...e.monitoring,pausedDataTypes:s}}},!1,"resumeDataType"),resetSettings:()=>e({layout:{viewMode:"cards",gridColumns:3,compactMode:!1,showFilters:!0,showSettings:!1},monitoring:{enabled:!1,refreshInterval:3e4,autoRefresh:!0,pausedDataTypes:new Set},filters:{},sortBy:"createdAt",sortDirection:"desc",selectedItems:new Set,searchTerm:""},!1,"resetSettings"),getFilteredData:(e,s)=>{let a=t(),r=[...e];if(a.searchTerm){let e=a.searchTerm.toLowerCase();r=r.filter(t=>Object.values(t).some(t=>String(t).toLowerCase().includes(e)))}return Object.entries(a.filters).forEach(e=>{let[t,a]=e;null!=a&&""!==a&&(r=r.filter(e=>{var r;let l=null==(r=s.filters)?void 0:r.find(e=>e.id===t);if(!l)return!0;switch(l.type){case"select":return e[t]===a;case"multiselect":return!Array.isArray(a)||a.includes(e[t]);case"toggle":return!a||e[t];default:return!0}}))}),r.sort((e,t)=>{let s=e[a.sortBy],r=t[a.sortBy],l="asc"===a.sortDirection?1:-1;return s<r?-1*l:s>r?+l:0}),r},getSelectedCount:()=>t().selectedItems.size,hasActiveFilters:()=>{let e=t();return e.searchTerm.length>0||Object.values(e.filters).some(e=>null!=e&&""!==e)}}),{name:"workhub-dashboard-".concat(e),partialize:e=>({layout:e.layout,monitoring:e.monitoring,filters:e.filters,sortBy:e.sortBy,sortDirection:e.sortDirection})}),{name:"dashboard-".concat(e)}))),i.get(e)),[e])}}}]);