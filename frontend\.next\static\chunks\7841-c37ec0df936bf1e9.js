"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7841],{17841:(e,t,l)=>{l.d(t,{er:()=>Q,kA:()=>f,gd:()=>F,BD:()=>D,nB:()=>w,jM:()=>A,lG:()=>q});var i=l(71610),a=l(26715),r=l(5041),n=l(12115),o=l(54120),u=l(90111),d=l(75908);class s{static enrich(e,t,l){var i,a,r;let{employeeMap:n,vehicleMap:o}=this.createLookupMaps(t,l);return{...e,drivers:null!=(i=this.enrichDrivers(e.drivers,n))?i:[],escorts:null!=(a=this.enrichEscorts(e.escorts,n))?a:[],vehicles:null!=(r=this.enrichVehicles(e.vehicles,o))?r:[]}}static createLookupMaps(e,t){return{employeeMap:new Map(e.map(e=>[e.id,e])),vehicleMap:new Map(t.map(e=>[e.id,e]))}}static enrichDrivers(e,t){return null==e?void 0:e.map(e=>{let l=e.employee||t.get(Number(e.employeeId));return{...e,...l&&{employee:l}}})}static enrichEscorts(e,t){return null==e?void 0:e.map(e=>{let l=e.employee||t.get(Number(e.employeeId));return{...e,...l&&{employee:l}}})}static enrichVehicles(e,t){return null==e?void 0:e.map(e=>{let l=e.vehicle||t.get(e.vehicleId);return{...e,...l&&{vehicle:l}}})}}let v=(e,t,l)=>s.enrich(e,t,l);var c=l(31203);let g={all:["delegations"],detail:e=>["delegations",e]},p=e=>({enabled:!!e,queryFn:()=>d.delegationApiService.getById(e),queryKey:g.detail(e),staleTime:3e5}),y=()=>({queryFn:()=>d.employeeApiService.getAll(),queryKey:["employees"],staleTime:6e5}),h=()=>({queryFn:()=>d.vehicleApiService.getAll(),queryKey:["vehicles"],staleTime:6e5}),m=e=>[p(e),y(),h()],D=e=>(0,u.GK)([...g.all],async()=>(await d.delegationApiService.getAll()).data,"delegation",{staleTime:0,...e}),f=e=>(0,u.GK)([...g.detail(e)],async()=>await d.delegationApiService.getById(e),"delegation",{enabled:!!e,staleTime:3e5}),F=e=>{let[t,l,a]=(0,i.E)({queries:m(e)}),r=(0,n.useMemo)(()=>{if((null==t?void 0:t.data)&&(null==l?void 0:l.data)&&(null==a?void 0:a.data))try{let e=t.data;return v(e,l.data,a.data)}catch(e){throw console.error("Error enriching delegation data:",e),e}},[null==t?void 0:t.data,null==l?void 0:l.data,null==a?void 0:a.data]),o=(0,n.useCallback)(()=>{null==t||t.refetch(),null==l||l.refetch(),null==a||a.refetch()},[null==t?void 0:t.refetch,null==l?void 0:l.refetch,null==a?void 0:a.refetch]);return{data:r,error:(null==t?void 0:t.error)||(null==l?void 0:l.error)||(null==a?void 0:a.error),isError:(null==t?void 0:t.isError)||(null==l?void 0:l.isError)||(null==a?void 0:a.isError),isLoading:(null==t?void 0:t.isLoading)||(null==l?void 0:l.isLoading)||(null==a?void 0:a.isLoading),isPending:(null==t?void 0:t.isPending)||(null==l?void 0:l.isPending)||(null==a?void 0:a.isPending),refetch:o}},Q=()=>{let e=(0,a.jE)();return(0,r.n)({mutationFn:async e=>{let t=c.G.toCreateRequest(e);return await d.delegationApiService.create(t)},onError:(t,l,i)=>{(null==i?void 0:i.previousDelegations)&&e.setQueryData(g.all,i.previousDelegations),console.error("Failed to create delegation:",t),e.invalidateQueries({queryKey:g.all})},onMutate:async t=>{await e.cancelQueries({queryKey:g.all});let l=e.getQueryData(g.all);return e.setQueryData(g.all,function(){var e,l,i,a,r,n,o,u;let d=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],s="optimistic-".concat(Date.now()),v=new Date().toISOString(),c=t.flightArrivalDetails?{id:"optimistic-flight-arr-".concat(Date.now()),...t.flightArrivalDetails}:null,g=t.flightDepartureDetails?{id:"optimistic-flight-dep-".concat(Date.now()+1),...t.flightDepartureDetails}:null;return[...d,{arrivalFlight:null!=c?c:null,createdAt:v,delegates:(null==(e=t.delegates)?void 0:e.map((e,t)=>{var l;return{id:"optimistic-delegate-".concat(s,"-").concat(t),name:e.name,notes:null!=(l=e.notes)?l:null,title:e.title}}))||[],departureFlight:null!=g?g:null,drivers:(null==(l=t.drivers)?void 0:l.map(e=>{var t;return{createdAt:v,createdBy:null,delegationId:s,employeeId:e.employeeId,id:"optimistic-driver-".concat(s,"-").concat(e.employeeId),notes:null!=(t=e.notes)?t:null,updatedAt:v}}))||[],durationFrom:t.durationFrom,durationTo:t.durationTo,escorts:(null==(i=t.escorts)?void 0:i.map(e=>{var t;return{createdAt:v,createdBy:null,delegationId:s,employeeId:e.employeeId,id:"optimistic-escort-".concat(s,"-").concat(e.employeeId),notes:null!=(t=e.notes)?t:null,updatedAt:v}}))||[],eventName:t.eventName,id:s,imageUrl:null!=(r=t.imageUrl)?r:null,invitationFrom:null!=(n=t.invitationFrom)?n:null,invitationTo:null!=(o=t.invitationTo)?o:null,location:t.location,notes:null!=(u=t.notes)?u:null,status:t.status||"Planned",statusHistory:[],updatedAt:v,vehicles:(null==(a=t.vehicles)?void 0:a.map(e=>{var t,l;return{assignedDate:e.assignedDate,createdAt:v,createdBy:null,delegationId:s,id:"optimistic-vehicle-".concat(s,"-").concat(e.vehicleId),notes:null!=(t=e.notes)?t:null,returnDate:null!=(l=e.returnDate)?l:null,updatedAt:v,vehicleId:e.vehicleId}}))||[]}]}),{previousDelegations:l}},onSettled:()=>{e.invalidateQueries({queryKey:g.all})}})},A=()=>{let e=(0,a.jE)();return(0,r.n)({mutationFn:async e=>{let{data:t,id:l}=e;return await d.delegationApiService.update(l,t)},onError:(t,l,i)=>{(null==i?void 0:i.previousDelegation)&&e.setQueryData(g.detail(l.id),i.previousDelegation),(null==i?void 0:i.previousDelegationsList)&&e.setQueryData(g.all,i.previousDelegationsList),console.error("Failed to update delegation:",t),e.invalidateQueries({queryKey:g.detail(l.id)}),e.invalidateQueries({queryKey:g.all})},onMutate:async t=>{let{data:l,id:i}=t;await e.cancelQueries({queryKey:g.all}),await e.cancelQueries({queryKey:g.detail(i)});let a=e.getQueryData(g.detail(i)),r=e.getQueryData(g.all);return e.setQueryData(g.detail(i),e=>{var t,i,a,r,n,u,d,s,v,c,g,p,y,h,m,D,f,F,Q,A,q,w,K,T,I,E,S,N,b;if(!e)return;let M=new Date().toISOString();return{...e,arrivalFlight:(0,o.d$)(null===l.flightArrivalDetails?null:void 0===l.flightArrivalDetails?e.arrivalFlight:{airport:l.flightArrivalDetails.airport||(null==(t=e.arrivalFlight)?void 0:t.airport)||"",dateTime:l.flightArrivalDetails.dateTime||(null==(i=e.arrivalFlight)?void 0:i.dateTime)||"",flightNumber:l.flightArrivalDetails.flightNumber||(null==(a=e.arrivalFlight)?void 0:a.flightNumber)||"",id:(null==(r=e.arrivalFlight)?void 0:r.id)||"optimistic-arr-".concat(Date.now()),notes:null!=(h=null!=(y=l.flightArrivalDetails.notes)?y:null==(n=e.arrivalFlight)?void 0:n.notes)?h:null,terminal:null!=(D=null!=(m=l.flightArrivalDetails.terminal)?m:null==(u=e.arrivalFlight)?void 0:u.terminal)?D:null}),departureFlight:(0,o.d$)(null===l.flightDepartureDetails?null:void 0===l.flightDepartureDetails?e.departureFlight:{airport:l.flightDepartureDetails.airport||(null==(d=e.departureFlight)?void 0:d.airport)||"",dateTime:l.flightDepartureDetails.dateTime||(null==(s=e.departureFlight)?void 0:s.dateTime)||"",flightNumber:l.flightDepartureDetails.flightNumber||(null==(v=e.departureFlight)?void 0:v.flightNumber)||"",id:(null==(c=e.departureFlight)?void 0:c.id)||"optimistic-dep-".concat(Date.now()),notes:null!=(F=null!=(f=l.flightDepartureDetails.notes)?f:null==(g=e.departureFlight)?void 0:g.notes)?F:null,terminal:null!=(A=null!=(Q=l.flightDepartureDetails.terminal)?Q:null==(p=e.departureFlight)?void 0:p.terminal)?A:null}),durationFrom:null!=(q=l.durationFrom)?q:e.durationFrom,durationTo:null!=(w=l.durationTo)?w:e.durationTo,eventName:null!=(K=l.eventName)?K:e.eventName,imageUrl:(0,o.d$)(null!=(T=l.imageUrl)?T:e.imageUrl),invitationFrom:(0,o.d$)(null!=(I=l.invitationFrom)?I:e.invitationFrom),invitationTo:(0,o.d$)(null!=(E=l.invitationTo)?E:e.invitationTo),location:null!=(S=l.location)?S:e.location,notes:(0,o.d$)(null!=(N=l.notes)?N:e.notes),status:null!=(b=l.status)?b:e.status,updatedAt:M}}),e.setQueryData(g.all,function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.map(t=>t.id===i&&e.getQueryData(g.detail(i))||t)}),{previousDelegation:a,previousDelegationsList:r}},onSettled:(t,l,i)=>{e.invalidateQueries({queryKey:g.detail(i.id)}),e.invalidateQueries({queryKey:g.all})}})},q=()=>{let e=(0,a.jE)();return(0,r.n)({mutationFn:async e=>{let{id:t,status:l,statusChangeReason:i}=e;return await d.delegationApiService.updateStatus(t,l,i)},onError:(t,l,i)=>{(null==i?void 0:i.previousDelegation)&&e.setQueryData(g.detail(l.id),i.previousDelegation),console.error("Failed to update delegation status:",t)},onMutate:async t=>{let{id:l,status:i}=t;await e.cancelQueries({queryKey:g.detail(l)});let a=e.getQueryData(g.detail(l));return e.setQueryData(g.detail(l),e=>e?{...e,status:i}:void 0),{previousDelegation:a}},onSettled:(t,l,i)=>{e.invalidateQueries({queryKey:g.detail(i.id)}),e.invalidateQueries({queryKey:g.all})}})},w=()=>{let e=(0,a.jE)();return(0,r.n)({mutationFn:async e=>(await d.delegationApiService.delete(e),e),onError:(t,l,i)=>{(null==i?void 0:i.previousDelegationsList)&&e.setQueryData(g.all,i.previousDelegationsList),console.error("Failed to delete delegation:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:g.all}),await e.cancelQueries({queryKey:g.detail(t)});let l=e.getQueryData(g.all);return e.setQueryData(g.all,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.filter(e=>e.id!==t)}),e.removeQueries({queryKey:g.detail(t)}),{previousDelegationsList:l}},onSettled:()=>{e.invalidateQueries({queryKey:g.all})}})}}}]);