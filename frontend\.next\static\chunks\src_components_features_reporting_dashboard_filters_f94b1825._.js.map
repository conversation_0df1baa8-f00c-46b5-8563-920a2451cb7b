{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/filters/DateRangeFilter.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/dashboard/filters/DateRangeFilter.tsx\r\n\r\nimport React from 'react';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Calendar } from '@/components/ui/calendar';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\nimport { cn } from '@/lib/utils';\r\nimport { format } from 'date-fns';\r\nimport { Calendar as CalendarIcon, X } from 'lucide-react';\r\nimport {\r\n  useReportingFilters,\r\n  useReportingFiltersActions,\r\n  useReportingFiltersValidation,\r\n} from '../../data/stores/useReportingFiltersStore';\r\n\r\ninterface DateRangeFilterProps {\r\n  compact?: boolean;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * @component DateRangeFilter\r\n * @description Date range filter component for reporting dashboard\r\n *\r\n * Responsibilities:\r\n * - Provides date range selection interface\r\n * - Integrates with reporting filters store\r\n * - Handles validation and error display\r\n * - Supports compact and full layouts\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of date range filtering\r\n * - OCP: Open for extension via props\r\n * - DIP: Depends on filter store abstractions\r\n */\r\nexport const DateRangeFilter: React.FC<DateRangeFilterProps> = ({\r\n  compact = false,\r\n  className = '',\r\n}) => {\r\n  const filters = useReportingFilters();\r\n  const { setDateRange } = useReportingFiltersActions();\r\n  const { validationErrors } = useReportingFiltersValidation();\r\n\r\n  const [isOpen, setIsOpen] = React.useState(false);\r\n\r\n  // Handle date range selection - FIXED: Updated type for Calendar component compatibility\r\n  const handleDateRangeSelect = (range: any) => {\r\n    if (range?.from && range?.to) {\r\n      setDateRange(range.from, range.to);\r\n      setIsOpen(false);\r\n    }\r\n  };\r\n\r\n  // Quick date range presets\r\n  const quickRanges = [\r\n    {\r\n      label: 'Last 7 days',\r\n      getValue: () => ({\r\n        from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),\r\n        to: new Date(),\r\n      }),\r\n    },\r\n    {\r\n      label: 'Last 30 days',\r\n      getValue: () => ({\r\n        from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),\r\n        to: new Date(),\r\n      }),\r\n    },\r\n    {\r\n      label: 'Last 90 days',\r\n      getValue: () => ({\r\n        from: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),\r\n        to: new Date(),\r\n      }),\r\n    },\r\n    {\r\n      label: 'This year',\r\n      getValue: () => ({\r\n        from: new Date(new Date().getFullYear(), 0, 1),\r\n        to: new Date(),\r\n      }),\r\n    },\r\n  ];\r\n\r\n  const handleQuickRange = (range: { from: Date; to: Date }) => {\r\n    setDateRange(range.from, range.to);\r\n    setIsOpen(false);\r\n  };\r\n\r\n  // Format date range for display\r\n  const formatDateRange = () => {\r\n    const { from, to } = filters.dateRange;\r\n    if (!from || !to) return 'Select date range';\r\n\r\n    if (compact) {\r\n      return `${format(from, 'MMM d')} - ${format(to, 'MMM d')}`;\r\n    }\r\n\r\n    return `${format(from, 'MMM d, yyyy')} - ${format(to, 'MMM d, yyyy')}`;\r\n  };\r\n\r\n  const hasError = validationErrors.dateRange;\r\n\r\n  if (compact) {\r\n    return (\r\n      <div className={cn('space-y-1', className)}>\r\n        <Label className=\"text-xs font-medium\">Date Range</Label>\r\n        <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n          <PopoverTrigger asChild>\r\n            <Button\r\n              variant=\"outline\"\r\n              className={cn(\r\n                'w-full justify-start text-left font-normal',\r\n                !filters.dateRange.from && 'text-muted-foreground',\r\n                hasError && 'border-red-500'\r\n              )}\r\n            >\r\n              <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n              {formatDateRange()}\r\n            </Button>\r\n          </PopoverTrigger>\r\n          <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n            <div className=\"p-3\">\r\n              <div className=\"grid grid-cols-2 gap-2 mb-3\">\r\n                {quickRanges.map(range => (\r\n                  <Button\r\n                    key={range.label}\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={() => handleQuickRange(range.getValue())}\r\n                    className=\"text-xs\"\r\n                  >\r\n                    {range.label}\r\n                  </Button>\r\n                ))}\r\n              </div>\r\n              <Calendar\r\n                mode=\"range\"\r\n                selected={{\r\n                  from: filters.dateRange.from,\r\n                  to: filters.dateRange.to,\r\n                }}\r\n                onSelect={handleDateRangeSelect}\r\n                numberOfMonths={1}\r\n                className=\"rounded-md border\"\r\n              />\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n        {hasError && <p className=\"text-xs text-red-600\">{hasError}</p>}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={cn('space-y-2', className)}>\r\n      <Label className=\"text-sm font-medium\">Date Range</Label>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"outline\"\r\n            className={cn(\r\n              'w-full justify-start text-left font-normal',\r\n              !filters.dateRange.from && 'text-muted-foreground',\r\n              hasError && 'border-red-500'\r\n            )}\r\n          >\r\n            <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n            {formatDateRange()}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n          <div className=\"p-4\">\r\n            <div className=\"space-y-2 mb-4\">\r\n              <h4 className=\"font-medium text-sm\">Quick Ranges</h4>\r\n              <div className=\"grid grid-cols-2 gap-2\">\r\n                {quickRanges.map(range => (\r\n                  <Button\r\n                    key={range.label}\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={() => handleQuickRange(range.getValue())}\r\n                    className=\"text-sm\"\r\n                  >\r\n                    {range.label}\r\n                  </Button>\r\n                ))}\r\n              </div>\r\n            </div>\r\n            <Calendar\r\n              mode=\"range\"\r\n              selected={{\r\n                from: filters.dateRange.from,\r\n                to: filters.dateRange.to,\r\n              }}\r\n              onSelect={handleDateRangeSelect}\r\n              numberOfMonths={2}\r\n              className=\"rounded-md border\"\r\n            />\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n      {hasError && <p className=\"text-sm text-red-600\">{hasError}</p>}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DateRangeFilter;\r\n"], "names": [], "mappings": "AAAA,mFAAmF;;;;;;AAEnF;AACA;AACA;AACA;AACA;AAKA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;AA0BO,MAAM,kBAAkD,CAAC,EAC9D,UAAU,KAAK,EACf,YAAY,EAAE,EACf;;IACC,MAAM,UAAU,CAAA,GAAA,2LAAA,CAAA,sBAAmB,AAAD;IAClC,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,6BAA0B,AAAD;IAClD,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,gCAA6B,AAAD;IAEzD,MAAM,CAAC,QAAQ,UAAU,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE3C,yFAAyF;IACzF,MAAM,wBAAwB,CAAC;QAC7B,IAAI,OAAO,QAAQ,OAAO,IAAI;YAC5B,aAAa,MAAM,IAAI,EAAE,MAAM,EAAE;YACjC,UAAU;QACZ;IACF;IAEA,2BAA2B;IAC3B,MAAM,cAAc;QAClB;YACE,OAAO;YACP,UAAU,IAAM,CAAC;oBACf,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;oBAC/C,IAAI,IAAI;gBACV,CAAC;QACH;QACA;YACE,OAAO;YACP,UAAU,IAAM,CAAC;oBACf,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;oBAChD,IAAI,IAAI;gBACV,CAAC;QACH;QACA;YACE,OAAO;YACP,UAAU,IAAM,CAAC;oBACf,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;oBAChD,IAAI,IAAI;gBACV,CAAC;QACH;QACA;YACE,OAAO;YACP,UAAU,IAAM,CAAC;oBACf,MAAM,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,GAAG;oBAC5C,IAAI,IAAI;gBACV,CAAC;QACH;KACD;IAED,MAAM,mBAAmB,CAAC;QACxB,aAAa,MAAM,IAAI,EAAE,MAAM,EAAE;QACjC,UAAU;IACZ;IAEA,gCAAgC;IAChC,MAAM,kBAAkB;QACtB,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,QAAQ,SAAS;QACtC,IAAI,CAAC,QAAQ,CAAC,IAAI,OAAO;QAEzB,IAAI,SAAS;YACX,OAAO,GAAG,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,SAAS,GAAG,EAAE,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,UAAU;QAC5D;QAEA,OAAO,GAAG,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,eAAe,GAAG,EAAE,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,gBAAgB;IACxE;IAEA,MAAM,WAAW,iBAAiB,SAAS;IAE3C,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;8BAC9B,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAsB;;;;;;8BACvC,6LAAC,sIAAA,CAAA,UAAO;oBAAC,MAAM;oBAAQ,cAAc;;sCACnC,6LAAC,sIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,QAAQ,SAAS,CAAC,IAAI,IAAI,yBAC3B,YAAY;;kDAGd,6LAAC,6MAAA,CAAA,WAAY;wCAAC,WAAU;;;;;;oCACvB;;;;;;;;;;;;sCAGL,6LAAC,sIAAA,CAAA,iBAAc;4BAAC,WAAU;4BAAa,OAAM;sCAC3C,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAA,sBACf,6LAAC,qIAAA,CAAA,SAAM;gDAEL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,iBAAiB,MAAM,QAAQ;gDAC9C,WAAU;0DAET,MAAM,KAAK;+CANP,MAAM,KAAK;;;;;;;;;;kDAUtB,6LAAC,uIAAA,CAAA,WAAQ;wCACP,MAAK;wCACL,UAAU;4CACR,MAAM,QAAQ,SAAS,CAAC,IAAI;4CAC5B,IAAI,QAAQ,SAAS,CAAC,EAAE;wCAC1B;wCACA,UAAU;wCACV,gBAAgB;wCAChB,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAKjB,0BAAY,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAGxD;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAAsB;;;;;;0BACvC,6LAAC,sIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACnC,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,QAAQ,SAAS,CAAC,IAAI,IAAI,yBAC3B,YAAY;;8CAGd,6LAAC,6MAAA,CAAA,WAAY;oCAAC,WAAU;;;;;;gCACvB;;;;;;;;;;;;kCAGL,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAa,OAAM;kCAC3C,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAA,sBACf,6LAAC,qIAAA,CAAA,SAAM;oDAEL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,iBAAiB,MAAM,QAAQ;oDAC9C,WAAU;8DAET,MAAM,KAAK;mDANP,MAAM,KAAK;;;;;;;;;;;;;;;;8CAWxB,6LAAC,uIAAA,CAAA,WAAQ;oCACP,MAAK;oCACL,UAAU;wCACR,MAAM,QAAQ,SAAS,CAAC,IAAI;wCAC5B,IAAI,QAAQ,SAAS,CAAC,EAAE;oCAC1B;oCACA,UAAU;oCACV,gBAAgB;oCAChB,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAKjB,0BAAY,6LAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;;AAGxD;GA3Ka;;QAIK,2LAAA,CAAA,sBAAmB;QACV,2LAAA,CAAA,6BAA0B;QACtB,2LAAA,CAAA,gCAA6B;;;KAN/C;uCA6KE", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/filters/StatusFilter.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/dashboard/filters/StatusFilter.tsx\r\n\r\nimport React from 'react';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { cn } from '@/lib/utils';\r\nimport { ChevronDown, X } from 'lucide-react';\r\nimport {\r\n  useReportingFilters,\r\n  useReportingFiltersActions,\r\n  useReportingFiltersValidation,\r\n} from '../../data/stores/useReportingFiltersStore';\r\nimport { DelegationStatusPrisma } from '../../data/types/reporting';\r\n\r\ninterface StatusFilterProps {\r\n  compact?: boolean;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * @component StatusFilter\r\n * @description Multi-select status filter component for reporting dashboard\r\n * \r\n * Responsibilities:\r\n * - Provides status selection interface\r\n * - Integrates with reporting filters store\r\n * - Handles validation and error display\r\n * - Supports compact and full layouts\r\n * \r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of status filtering\r\n * - OCP: Open for extension via props\r\n * - DIP: Depends on filter store abstractions\r\n */\r\nexport const StatusFilter: React.FC<StatusFilterProps> = ({\r\n  compact = false,\r\n  className = '',\r\n}) => {\r\n  const filters = useReportingFilters();\r\n  const { setStatus } = useReportingFiltersActions();\r\n  const { validationErrors } = useReportingFiltersValidation();\r\n\r\n  const [isOpen, setIsOpen] = React.useState(false);\r\n\r\n  // Available status options\r\n  const statusOptions = [\r\n    { value: 'DRAFT' as DelegationStatusPrisma, label: 'Draft', color: 'bg-gray-100 text-gray-800' },\r\n    { value: 'PENDING' as DelegationStatusPrisma, label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },\r\n    { value: 'APPROVED' as DelegationStatusPrisma, label: 'Approved', color: 'bg-blue-100 text-blue-800' },\r\n    { value: 'IN_PROGRESS' as DelegationStatusPrisma, label: 'In Progress', color: 'bg-purple-100 text-purple-800' },\r\n    { value: 'COMPLETED' as DelegationStatusPrisma, label: 'Completed', color: 'bg-green-100 text-green-800' },\r\n    { value: 'CANCELLED' as DelegationStatusPrisma, label: 'Cancelled', color: 'bg-red-100 text-red-800' },\r\n  ];\r\n\r\n  // Handle status selection\r\n  const handleStatusToggle = (status: DelegationStatusPrisma) => {\r\n    const currentStatuses = filters.status;\r\n    const isSelected = currentStatuses.includes(status);\r\n    \r\n    if (isSelected) {\r\n      setStatus(currentStatuses.filter(s => s !== status));\r\n    } else {\r\n      setStatus([...currentStatuses, status]);\r\n    }\r\n  };\r\n\r\n  // Handle select all/none\r\n  const handleSelectAll = () => {\r\n    setStatus(statusOptions.map(option => option.value));\r\n  };\r\n\r\n  const handleSelectNone = () => {\r\n    setStatus([]);\r\n  };\r\n\r\n  // Get display text\r\n  const getDisplayText = () => {\r\n    const selectedCount = filters.status.length;\r\n    if (selectedCount === 0) return 'All statuses';\r\n    if (selectedCount === 1) {\r\n      const selected = statusOptions.find(option => option.value === filters.status[0]);\r\n      return selected?.label || 'Unknown';\r\n    }\r\n    return `${selectedCount} statuses`;\r\n  };\r\n\r\n  const hasError = validationErrors.status;\r\n\r\n  if (compact) {\r\n    return (\r\n      <div className={cn('space-y-1', className)}>\r\n        <Label className=\"text-xs font-medium\">Status</Label>\r\n        <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n          <PopoverTrigger asChild>\r\n            <Button\r\n              variant=\"outline\"\r\n              className={cn(\r\n                'w-full justify-between text-left font-normal',\r\n                hasError && 'border-red-500'\r\n              )}\r\n            >\r\n              <span className=\"truncate\">{getDisplayText()}</span>\r\n              <ChevronDown className=\"ml-2 h-4 w-4 shrink-0\" />\r\n            </Button>\r\n          </PopoverTrigger>\r\n          <PopoverContent className=\"w-56 p-0\" align=\"start\">\r\n            <div className=\"p-3\">\r\n              <div className=\"flex justify-between mb-3\">\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectAll}>\r\n                  All\r\n                </Button>\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectNone}>\r\n                  None\r\n                </Button>\r\n              </div>\r\n              <div className=\"space-y-2\">\r\n                {statusOptions.map((option) => (\r\n                  <div key={option.value} className=\"flex items-center space-x-2\">\r\n                    <Checkbox\r\n                      id={`status-${option.value}`}\r\n                      checked={filters.status.includes(option.value)}\r\n                      onCheckedChange={() => handleStatusToggle(option.value)}\r\n                    />\r\n                    <Label\r\n                      htmlFor={`status-${option.value}`}\r\n                      className=\"text-sm font-normal cursor-pointer flex-1\"\r\n                    >\r\n                      <Badge variant=\"secondary\" className={cn('text-xs', option.color)}>\r\n                        {option.label}\r\n                      </Badge>\r\n                    </Label>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n        {hasError && (\r\n          <p className=\"text-xs text-red-600\">{hasError}</p>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={cn('space-y-2', className)}>\r\n      <Label className=\"text-sm font-medium\">Status</Label>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"outline\"\r\n            className={cn(\r\n              'w-full justify-between text-left font-normal',\r\n              hasError && 'border-red-500'\r\n            )}\r\n          >\r\n            <span>{getDisplayText()}</span>\r\n            <ChevronDown className=\"ml-2 h-4 w-4 shrink-0\" />\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-64 p-0\" align=\"start\">\r\n          <div className=\"p-4\">\r\n            <div className=\"flex justify-between items-center mb-4\">\r\n              <h4 className=\"font-medium text-sm\">Select Statuses</h4>\r\n              <div className=\"flex gap-2\">\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectAll}>\r\n                  All\r\n                </Button>\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectNone}>\r\n                  None\r\n                </Button>\r\n              </div>\r\n            </div>\r\n            <div className=\"space-y-3\">\r\n              {statusOptions.map((option) => (\r\n                <div key={option.value} className=\"flex items-center space-x-3\">\r\n                  <Checkbox\r\n                    id={`status-${option.value}`}\r\n                    checked={filters.status.includes(option.value)}\r\n                    onCheckedChange={() => handleStatusToggle(option.value)}\r\n                  />\r\n                  <Label\r\n                    htmlFor={`status-${option.value}`}\r\n                    className=\"text-sm font-normal cursor-pointer flex-1\"\r\n                  >\r\n                    <Badge variant=\"secondary\" className={cn('text-sm', option.color)}>\r\n                      {option.label}\r\n                    </Badge>\r\n                  </Label>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n      \r\n      {/* Selected status badges */}\r\n      {filters.status.length > 0 && (\r\n        <div className=\"flex flex-wrap gap-1\">\r\n          {filters.status.map((status) => {\r\n            const option = statusOptions.find(opt => opt.value === status);\r\n            if (!option) return null;\r\n            \r\n            return (\r\n              <Badge\r\n                key={status}\r\n                variant=\"secondary\"\r\n                className={cn('text-xs pr-1', option.color)}\r\n              >\r\n                {option.label}\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"h-auto p-0 ml-1 hover:bg-transparent\"\r\n                  onClick={() => handleStatusToggle(status)}\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>\r\n            );\r\n          })}\r\n        </div>\r\n      )}\r\n      \r\n      {hasError && (\r\n        <p className=\"text-sm text-red-600\">{hasError}</p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StatusFilter;\r\n"], "names": [], "mappings": "AAAA,gFAAgF;;;;;;AAEhF;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;;;;;;;;;;;;AA2BO,MAAM,eAA4C,CAAC,EACxD,UAAU,KAAK,EACf,YAAY,EAAE,EACf;;IACC,MAAM,UAAU,CAAA,GAAA,2LAAA,CAAA,sBAAmB,AAAD;IAClC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,6BAA0B,AAAD;IAC/C,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,gCAA6B,AAAD;IAEzD,MAAM,CAAC,QAAQ,UAAU,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE3C,2BAA2B;IAC3B,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAmC,OAAO;YAAS,OAAO;QAA4B;QAC/F;YAAE,OAAO;YAAqC,OAAO;YAAW,OAAO;QAAgC;QACvG;YAAE,OAAO;YAAsC,OAAO;YAAY,OAAO;QAA4B;QACrG;YAAE,OAAO;YAAyC,OAAO;YAAe,OAAO;QAAgC;QAC/G;YAAE,OAAO;YAAuC,OAAO;YAAa,OAAO;QAA8B;QACzG;YAAE,OAAO;YAAuC,OAAO;YAAa,OAAO;QAA0B;KACtG;IAED,0BAA0B;IAC1B,MAAM,qBAAqB,CAAC;QAC1B,MAAM,kBAAkB,QAAQ,MAAM;QACtC,MAAM,aAAa,gBAAgB,QAAQ,CAAC;QAE5C,IAAI,YAAY;YACd,UAAU,gBAAgB,MAAM,CAAC,CAAA,IAAK,MAAM;QAC9C,OAAO;YACL,UAAU;mBAAI;gBAAiB;aAAO;QACxC;IACF;IAEA,yBAAyB;IACzB,MAAM,kBAAkB;QACtB,UAAU,cAAc,GAAG,CAAC,CAAA,SAAU,OAAO,KAAK;IACpD;IAEA,MAAM,mBAAmB;QACvB,UAAU,EAAE;IACd;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,MAAM,gBAAgB,QAAQ,MAAM,CAAC,MAAM;QAC3C,IAAI,kBAAkB,GAAG,OAAO;QAChC,IAAI,kBAAkB,GAAG;YACvB,MAAM,WAAW,cAAc,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,QAAQ,MAAM,CAAC,EAAE;YAChF,OAAO,UAAU,SAAS;QAC5B;QACA,OAAO,GAAG,cAAc,SAAS,CAAC;IACpC;IAEA,MAAM,WAAW,iBAAiB,MAAM;IAExC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;8BAC9B,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAsB;;;;;;8BACvC,6LAAC,sIAAA,CAAA,UAAO;oBAAC,MAAM;oBAAQ,cAAc;;sCACnC,6LAAC,sIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gDACA,YAAY;;kDAGd,6LAAC;wCAAK,WAAU;kDAAY;;;;;;kDAC5B,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAG3B,6LAAC,sIAAA,CAAA,iBAAc;4BAAC,WAAU;4BAAW,OAAM;sCACzC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,SAAS;0DAAiB;;;;;;0DAG5D,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,SAAS;0DAAkB;;;;;;;;;;;;kDAI/D,6LAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;gDAAuB,WAAU;;kEAChC,6LAAC,uIAAA,CAAA,WAAQ;wDACP,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;wDAC5B,SAAS,QAAQ,MAAM,CAAC,QAAQ,CAAC,OAAO,KAAK;wDAC7C,iBAAiB,IAAM,mBAAmB,OAAO,KAAK;;;;;;kEAExD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,SAAS,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;wDACjC,WAAU;kEAEV,cAAA,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,WAAW,OAAO,KAAK;sEAC7D,OAAO,KAAK;;;;;;;;;;;;+CAXT,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAoB/B,0BACC,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAI7C;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAAsB;;;;;;0BACvC,6LAAC,sIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACnC,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gDACA,YAAY;;8CAGd,6LAAC;8CAAM;;;;;;8CACP,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG3B,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;kCACzC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS;8DAAiB;;;;;;8DAG5D,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS;8DAAkB;;;;;;;;;;;;;;;;;;8CAKjE,6LAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;4CAAuB,WAAU;;8DAChC,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;oDAC5B,SAAS,QAAQ,MAAM,CAAC,QAAQ,CAAC,OAAO,KAAK;oDAC7C,iBAAiB,IAAM,mBAAmB,OAAO,KAAK;;;;;;8DAExD,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SAAS,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;oDACjC,WAAU;8DAEV,cAAA,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,WAAW,OAAO,KAAK;kEAC7D,OAAO,KAAK;;;;;;;;;;;;2CAXT,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;YAsB/B,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC;oBACnB,MAAM,SAAS,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;oBACvD,IAAI,CAAC,QAAQ,OAAO;oBAEpB,qBACE,6LAAC,oIAAA,CAAA,QAAK;wBAEJ,SAAQ;wBACR,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,OAAO,KAAK;;4BAEzC,OAAO,KAAK;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,mBAAmB;0CAElC,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;uBAXV;;;;;gBAeX;;;;;;YAIH,0BACC,6LAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;;AAI7C;GAnMa;;QAIK,2LAAA,CAAA,sBAAmB;QACb,2LAAA,CAAA,6BAA0B;QACnB,2LAAA,CAAA,gCAA6B;;;KAN/C;uCAqME", "debugId": null}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/filters/LocationFilter.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/dashboard/filters/LocationFilter.tsx\r\n\r\nimport React from 'react';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { cn } from '@/lib/utils';\r\nimport { ChevronDown, X, Search, MapPin } from 'lucide-react';\r\nimport {\r\n  useReportingFilters,\r\n  useReportingFiltersActions,\r\n  useReportingFiltersValidation,\r\n} from '../../data/stores/useReportingFiltersStore';\r\n\r\ninterface LocationFilterProps {\r\n  compact?: boolean;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * @component LocationFilter\r\n * @description Multi-select location filter component for reporting dashboard\r\n * \r\n * Responsibilities:\r\n * - Provides location selection interface with search\r\n * - Integrates with reporting filters store\r\n * - Handles validation and error display\r\n * - Supports compact and full layouts\r\n * \r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of location filtering\r\n * - OCP: Open for extension via props\r\n * - DIP: Depends on filter store abstractions\r\n */\r\nexport const LocationFilter: React.FC<LocationFilterProps> = ({\r\n  compact = false,\r\n  className = '',\r\n}) => {\r\n  const filters = useReportingFilters();\r\n  const { setLocations } = useReportingFiltersActions();\r\n  const { validationErrors } = useReportingFiltersValidation();\r\n\r\n  const [isOpen, setIsOpen] = React.useState(false);\r\n  const [searchTerm, setSearchTerm] = React.useState('');\r\n\r\n  // Mock location options - in real app, this would come from an API\r\n  const locationOptions = [\r\n    'New York, NY',\r\n    'Los Angeles, CA',\r\n    'Chicago, IL',\r\n    'Houston, TX',\r\n    'Phoenix, AZ',\r\n    'Philadelphia, PA',\r\n    'San Antonio, TX',\r\n    'San Diego, CA',\r\n    'Dallas, TX',\r\n    'San Jose, CA',\r\n    'Austin, TX',\r\n    'Jacksonville, FL',\r\n    'Fort Worth, TX',\r\n    'Columbus, OH',\r\n    'Charlotte, NC',\r\n    'San Francisco, CA',\r\n    'Indianapolis, IN',\r\n    'Seattle, WA',\r\n    'Denver, CO',\r\n    'Washington, DC',\r\n  ];\r\n\r\n  // Filter locations based on search term\r\n  const filteredLocations = locationOptions.filter(location =>\r\n    location.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  // Handle location selection\r\n  const handleLocationToggle = (location: string) => {\r\n    const currentLocations = filters.locations;\r\n    const isSelected = currentLocations.includes(location);\r\n    \r\n    if (isSelected) {\r\n      setLocations(currentLocations.filter(l => l !== location));\r\n    } else {\r\n      setLocations([...currentLocations, location]);\r\n    }\r\n  };\r\n\r\n  // Handle select all/none\r\n  const handleSelectAll = () => {\r\n    setLocations(filteredLocations);\r\n  };\r\n\r\n  const handleSelectNone = () => {\r\n    setLocations([]);\r\n  };\r\n\r\n  // Get display text\r\n  const getDisplayText = () => {\r\n    const selectedCount = filters.locations.length;\r\n    if (selectedCount === 0) return 'All locations';\r\n    if (selectedCount === 1) return filters.locations[0];\r\n    return `${selectedCount} locations`;\r\n  };\r\n\r\n  const hasError = validationErrors.locations;\r\n\r\n  if (compact) {\r\n    return (\r\n      <div className={cn('space-y-1', className)}>\r\n        <Label className=\"text-xs font-medium\">Location</Label>\r\n        <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n          <PopoverTrigger asChild>\r\n            <Button\r\n              variant=\"outline\"\r\n              className={cn(\r\n                'w-full justify-between text-left font-normal',\r\n                hasError && 'border-red-500'\r\n              )}\r\n            >\r\n              <span className=\"truncate\">{getDisplayText()}</span>\r\n              <ChevronDown className=\"ml-2 h-4 w-4 shrink-0\" />\r\n            </Button>\r\n          </PopoverTrigger>\r\n          <PopoverContent className=\"w-64 p-0\" align=\"start\">\r\n            <div className=\"p-3\">\r\n              <div className=\"relative mb-3\">\r\n                <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n                <Input\r\n                  placeholder=\"Search locations...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"pl-8\"\r\n                />\r\n              </div>\r\n              <div className=\"flex justify-between mb-3\">\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectAll}>\r\n                  All\r\n                </Button>\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectNone}>\r\n                  None\r\n                </Button>\r\n              </div>\r\n              <div className=\"max-h-48 overflow-y-auto space-y-2\">\r\n                {filteredLocations.map((location) => (\r\n                  <div key={location} className=\"flex items-center space-x-2\">\r\n                    <Checkbox\r\n                      id={`location-${location}`}\r\n                      checked={filters.locations.includes(location)}\r\n                      onCheckedChange={() => handleLocationToggle(location)}\r\n                    />\r\n                    <Label\r\n                      htmlFor={`location-${location}`}\r\n                      className=\"text-sm font-normal cursor-pointer flex-1 flex items-center gap-2\"\r\n                    >\r\n                      <MapPin className=\"h-3 w-3 text-muted-foreground\" />\r\n                      {location}\r\n                    </Label>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n        {hasError && (\r\n          <p className=\"text-xs text-red-600\">{hasError}</p>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={cn('space-y-2', className)}>\r\n      <Label className=\"text-sm font-medium\">Locations</Label>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"outline\"\r\n            className={cn(\r\n              'w-full justify-between text-left font-normal',\r\n              hasError && 'border-red-500'\r\n            )}\r\n          >\r\n            <span>{getDisplayText()}</span>\r\n            <ChevronDown className=\"ml-2 h-4 w-4 shrink-0\" />\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-80 p-0\" align=\"start\">\r\n          <div className=\"p-4\">\r\n            <div className=\"flex justify-between items-center mb-4\">\r\n              <h4 className=\"font-medium text-sm\">Select Locations</h4>\r\n              <div className=\"flex gap-2\">\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectAll}>\r\n                  All\r\n                </Button>\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectNone}>\r\n                  None\r\n                </Button>\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"relative mb-4\">\r\n              <Search className=\"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n              <Input\r\n                placeholder=\"Search locations...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                className=\"pl-9\"\r\n              />\r\n            </div>\r\n            \r\n            <div className=\"max-h-64 overflow-y-auto space-y-3\">\r\n              {filteredLocations.map((location) => (\r\n                <div key={location} className=\"flex items-center space-x-3\">\r\n                  <Checkbox\r\n                    id={`location-${location}`}\r\n                    checked={filters.locations.includes(location)}\r\n                    onCheckedChange={() => handleLocationToggle(location)}\r\n                  />\r\n                  <Label\r\n                    htmlFor={`location-${location}`}\r\n                    className=\"text-sm font-normal cursor-pointer flex-1 flex items-center gap-2\"\r\n                  >\r\n                    <MapPin className=\"h-4 w-4 text-muted-foreground\" />\r\n                    {location}\r\n                  </Label>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n      \r\n      {/* Selected location badges */}\r\n      {filters.locations.length > 0 && (\r\n        <div className=\"flex flex-wrap gap-1\">\r\n          {filters.locations.slice(0, 3).map((location) => (\r\n            <Badge key={location} variant=\"secondary\" className=\"text-xs pr-1\">\r\n              {location.length > 15 ? `${location.slice(0, 15)}...` : location}\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"h-auto p-0 ml-1 hover:bg-transparent\"\r\n                onClick={() => handleLocationToggle(location)}\r\n              >\r\n                <X className=\"h-3 w-3\" />\r\n              </Button>\r\n            </Badge>\r\n          ))}\r\n          {filters.locations.length > 3 && (\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              +{filters.locations.length - 3} more\r\n            </Badge>\r\n          )}\r\n        </div>\r\n      )}\r\n      \r\n      {hasError && (\r\n        <p className=\"text-sm text-red-600\">{hasError}</p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LocationFilter;\r\n"], "names": [], "mappings": "AAAA,kFAAkF;;;;;;AAElF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;;;;;;;;AA0BO,MAAM,iBAAgD,CAAC,EAC5D,UAAU,KAAK,EACf,YAAY,EAAE,EACf;;IACC,MAAM,UAAU,CAAA,GAAA,2LAAA,CAAA,sBAAmB,AAAD;IAClC,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,6BAA0B,AAAD;IAClD,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,gCAA6B,AAAD;IAEzD,MAAM,CAAC,QAAQ,UAAU,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,mEAAmE;IACnE,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,wCAAwC;IACxC,MAAM,oBAAoB,gBAAgB,MAAM,CAAC,CAAA,WAC/C,SAAS,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGxD,4BAA4B;IAC5B,MAAM,uBAAuB,CAAC;QAC5B,MAAM,mBAAmB,QAAQ,SAAS;QAC1C,MAAM,aAAa,iBAAiB,QAAQ,CAAC;QAE7C,IAAI,YAAY;YACd,aAAa,iBAAiB,MAAM,CAAC,CAAA,IAAK,MAAM;QAClD,OAAO;YACL,aAAa;mBAAI;gBAAkB;aAAS;QAC9C;IACF;IAEA,yBAAyB;IACzB,MAAM,kBAAkB;QACtB,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,aAAa,EAAE;IACjB;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,MAAM,gBAAgB,QAAQ,SAAS,CAAC,MAAM;QAC9C,IAAI,kBAAkB,GAAG,OAAO;QAChC,IAAI,kBAAkB,GAAG,OAAO,QAAQ,SAAS,CAAC,EAAE;QACpD,OAAO,GAAG,cAAc,UAAU,CAAC;IACrC;IAEA,MAAM,WAAW,iBAAiB,SAAS;IAE3C,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;8BAC9B,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAsB;;;;;;8BACvC,6LAAC,sIAAA,CAAA,UAAO;oBAAC,MAAM;oBAAQ,cAAc;;sCACnC,6LAAC,sIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gDACA,YAAY;;kDAGd,6LAAC;wCAAK,WAAU;kDAAY;;;;;;kDAC5B,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAG3B,6LAAC,sIAAA,CAAA,iBAAc;4BAAC,WAAU;4BAAW,OAAM;sCACzC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAGd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,SAAS;0DAAiB;;;;;;0DAG5D,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,SAAS;0DAAkB;;;;;;;;;;;;kDAI/D,6LAAC;wCAAI,WAAU;kDACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC;gDAAmB,WAAU;;kEAC5B,6LAAC,uIAAA,CAAA,WAAQ;wDACP,IAAI,CAAC,SAAS,EAAE,UAAU;wDAC1B,SAAS,QAAQ,SAAS,CAAC,QAAQ,CAAC;wDACpC,iBAAiB,IAAM,qBAAqB;;;;;;kEAE9C,6LAAC,oIAAA,CAAA,QAAK;wDACJ,SAAS,CAAC,SAAS,EAAE,UAAU;wDAC/B,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DACjB;;;;;;;;+CAXK;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAmBnB,0BACC,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAI7C;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAAsB;;;;;;0BACvC,6LAAC,sIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACnC,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gDACA,YAAY;;8CAGd,6LAAC;8CAAM;;;;;;8CACP,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG3B,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;kCACzC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS;8DAAiB;;;;;;8DAG5D,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS;8DAAkB;;;;;;;;;;;;;;;;;;8CAMjE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC;4CAAmB,WAAU;;8DAC5B,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAI,CAAC,SAAS,EAAE,UAAU;oDAC1B,SAAS,QAAQ,SAAS,CAAC,QAAQ,CAAC;oDACpC,iBAAiB,IAAM,qBAAqB;;;;;;8DAE9C,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SAAS,CAAC,SAAS,EAAE,UAAU;oDAC/B,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACjB;;;;;;;;2CAXK;;;;;;;;;;;;;;;;;;;;;;;;;;;YAqBnB,QAAQ,SAAS,CAAC,MAAM,GAAG,mBAC1B,6LAAC;gBAAI,WAAU;;oBACZ,QAAQ,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAClC,6LAAC,oIAAA,CAAA,QAAK;4BAAgB,SAAQ;4BAAY,WAAU;;gCACjD,SAAS,MAAM,GAAG,KAAK,GAAG,SAAS,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;8CACxD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,qBAAqB;8CAEpC,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BARL;;;;;oBAYb,QAAQ,SAAS,CAAC,MAAM,GAAG,mBAC1B,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAAU;4BAC3C,QAAQ,SAAS,CAAC,MAAM,GAAG;4BAAE;;;;;;;;;;;;;YAMtC,0BACC,6LAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;;AAI7C;GAlOa;;QAIK,2LAAA,CAAA,sBAAmB;QACV,2LAAA,CAAA,6BAA0B;QACtB,2LAAA,CAAA,gCAA6B;;;KAN/C;uCAoOE", "debugId": null}}, {"offset": {"line": 1381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/filters/EmployeeFilter.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/dashboard/filters/EmployeeFilter.tsx\r\n\r\nimport React from 'react';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { cn } from '@/lib/utils';\r\nimport { ChevronDown, X, Search, User } from 'lucide-react';\r\nimport {\r\n  useReportingFilters,\r\n  useReportingFiltersActions,\r\n  useReportingFiltersValidation,\r\n} from '../../data/stores/useReportingFiltersStore';\r\n\r\ninterface EmployeeFilterProps {\r\n  compact?: boolean;\r\n  className?: string;\r\n}\r\n\r\nexport const EmployeeFilter: React.FC<EmployeeFilterProps> = ({\r\n  compact = false,\r\n  className = '',\r\n}) => {\r\n  const filters = useReportingFilters();\r\n  const { setEmployees } = useReportingFiltersActions();\r\n  const { validationErrors } = useReportingFiltersValidation();\r\n\r\n  const [isOpen, setIsOpen] = React.useState(false);\r\n  const [searchTerm, setSearchTerm] = React.useState('');\r\n\r\n  // Mock employee options - in real app, this would come from an API\r\n  const employeeOptions = [\r\n    { id: 1, name: 'John Smith', department: 'Operations' },\r\n    { id: 2, name: 'Sarah Johnson', department: 'Sales' },\r\n    { id: 3, name: 'Mike Davis', department: 'Engineering' },\r\n    { id: 4, name: 'Emily Brown', department: 'Marketing' },\r\n    { id: 5, name: 'David Wilson', department: 'Operations' },\r\n    { id: 6, name: 'Lisa Anderson', department: 'HR' },\r\n    { id: 7, name: 'Tom Miller', department: 'Finance' },\r\n    { id: 8, name: 'Anna Garcia', department: 'Sales' },\r\n    { id: 9, name: 'Chris Taylor', department: 'Engineering' },\r\n    { id: 10, name: 'Jessica Lee', department: 'Marketing' },\r\n  ];\r\n\r\n  const filteredEmployees = employeeOptions.filter(employee =>\r\n    employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    employee.department.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  const handleEmployeeToggle = (employeeId: number) => {\r\n    const currentEmployees = filters.employees;\r\n    const isSelected = currentEmployees.includes(employeeId);\r\n    \r\n    if (isSelected) {\r\n      setEmployees(currentEmployees.filter(id => id !== employeeId));\r\n    } else {\r\n      setEmployees([...currentEmployees, employeeId]);\r\n    }\r\n  };\r\n\r\n  const handleSelectAll = () => {\r\n    setEmployees(filteredEmployees.map(emp => emp.id));\r\n  };\r\n\r\n  const handleSelectNone = () => {\r\n    setEmployees([]);\r\n  };\r\n\r\n  const getDisplayText = () => {\r\n    const selectedCount = filters.employees.length;\r\n    if (selectedCount === 0) return 'All employees';\r\n    if (selectedCount === 1) {\r\n      const employee = employeeOptions.find(emp => emp.id === filters.employees[0]);\r\n      return employee?.name || 'Unknown';\r\n    }\r\n    return `${selectedCount} employees`;\r\n  };\r\n\r\n  const hasError = validationErrors.employees;\r\n\r\n  if (compact) {\r\n    return (\r\n      <div className={cn('space-y-1', className)}>\r\n        <Label className=\"text-xs font-medium\">Employee</Label>\r\n        <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n          <PopoverTrigger asChild>\r\n            <Button\r\n              variant=\"outline\"\r\n              className={cn(\r\n                'w-full justify-between text-left font-normal',\r\n                hasError && 'border-red-500'\r\n              )}\r\n            >\r\n              <span className=\"truncate\">{getDisplayText()}</span>\r\n              <ChevronDown className=\"ml-2 h-4 w-4 shrink-0\" />\r\n            </Button>\r\n          </PopoverTrigger>\r\n          <PopoverContent className=\"w-64 p-0\" align=\"start\">\r\n            <div className=\"p-3\">\r\n              <div className=\"relative mb-3\">\r\n                <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n                <Input\r\n                  placeholder=\"Search employees...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"pl-8\"\r\n                />\r\n              </div>\r\n              <div className=\"flex justify-between mb-3\">\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectAll}>\r\n                  All\r\n                </Button>\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectNone}>\r\n                  None\r\n                </Button>\r\n              </div>\r\n              <div className=\"max-h-48 overflow-y-auto space-y-2\">\r\n                {filteredEmployees.map((employee) => (\r\n                  <div key={employee.id} className=\"flex items-center space-x-2\">\r\n                    <Checkbox\r\n                      id={`employee-${employee.id}`}\r\n                      checked={filters.employees.includes(employee.id)}\r\n                      onCheckedChange={() => handleEmployeeToggle(employee.id)}\r\n                    />\r\n                    <Label\r\n                      htmlFor={`employee-${employee.id}`}\r\n                      className=\"text-sm font-normal cursor-pointer flex-1\"\r\n                    >\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <User className=\"h-3 w-3 text-muted-foreground\" />\r\n                        <div>\r\n                          <div className=\"font-medium\">{employee.name}</div>\r\n                          <div className=\"text-xs text-muted-foreground\">{employee.department}</div>\r\n                        </div>\r\n                      </div>\r\n                    </Label>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n        {hasError && (\r\n          <p className=\"text-xs text-red-600\">{hasError}</p>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={cn('space-y-2', className)}>\r\n      <Label className=\"text-sm font-medium\">Employees</Label>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"outline\"\r\n            className={cn(\r\n              'w-full justify-between text-left font-normal',\r\n              hasError && 'border-red-500'\r\n            )}\r\n          >\r\n            <span>{getDisplayText()}</span>\r\n            <ChevronDown className=\"ml-2 h-4 w-4 shrink-0\" />\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-80 p-0\" align=\"start\">\r\n          <div className=\"p-4\">\r\n            <div className=\"flex justify-between items-center mb-4\">\r\n              <h4 className=\"font-medium text-sm\">Select Employees</h4>\r\n              <div className=\"flex gap-2\">\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectAll}>\r\n                  All\r\n                </Button>\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectNone}>\r\n                  None\r\n                </Button>\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"relative mb-4\">\r\n              <Search className=\"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n              <Input\r\n                placeholder=\"Search employees...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                className=\"pl-9\"\r\n              />\r\n            </div>\r\n            \r\n            <div className=\"max-h-64 overflow-y-auto space-y-3\">\r\n              {filteredEmployees.map((employee) => (\r\n                <div key={employee.id} className=\"flex items-center space-x-3\">\r\n                  <Checkbox\r\n                    id={`employee-${employee.id}`}\r\n                    checked={filters.employees.includes(employee.id)}\r\n                    onCheckedChange={() => handleEmployeeToggle(employee.id)}\r\n                  />\r\n                  <Label\r\n                    htmlFor={`employee-${employee.id}`}\r\n                    className=\"text-sm font-normal cursor-pointer flex-1\"\r\n                  >\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <User className=\"h-4 w-4 text-muted-foreground\" />\r\n                      <div>\r\n                        <div className=\"font-medium\">{employee.name}</div>\r\n                        <div className=\"text-xs text-muted-foreground\">{employee.department}</div>\r\n                      </div>\r\n                    </div>\r\n                  </Label>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n      \r\n      {/* Selected employee badges */}\r\n      {filters.employees.length > 0 && (\r\n        <div className=\"flex flex-wrap gap-1\">\r\n          {filters.employees.slice(0, 3).map((employeeId) => {\r\n            const employee = employeeOptions.find(emp => emp.id === employeeId);\r\n            if (!employee) return null;\r\n            \r\n            return (\r\n              <Badge key={employeeId} variant=\"secondary\" className=\"text-xs pr-1\">\r\n                {employee.name}\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"h-auto p-0 ml-1 hover:bg-transparent\"\r\n                  onClick={() => handleEmployeeToggle(employeeId)}\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>\r\n            );\r\n          })}\r\n          {filters.employees.length > 3 && (\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              +{filters.employees.length - 3} more\r\n            </Badge>\r\n          )}\r\n        </div>\r\n      )}\r\n      \r\n      {hasError && (\r\n        <p className=\"text-sm text-red-600\">{hasError}</p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EmployeeFilter;\r\n"], "names": [], "mappings": "AAAA,kFAAkF;;;;;;AAElF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;;;;;;;;AAWO,MAAM,iBAAgD,CAAC,EAC5D,UAAU,KAAK,EACf,YAAY,EAAE,EACf;;IACC,MAAM,UAAU,CAAA,GAAA,2LAAA,CAAA,sBAAmB,AAAD;IAClC,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,6BAA0B,AAAD;IAClD,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,gCAA6B,AAAD;IAEzD,MAAM,CAAC,QAAQ,UAAU,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,mEAAmE;IACnE,MAAM,kBAAkB;QACtB;YAAE,IAAI;YAAG,MAAM;YAAc,YAAY;QAAa;QACtD;YAAE,IAAI;YAAG,MAAM;YAAiB,YAAY;QAAQ;QACpD;YAAE,IAAI;YAAG,MAAM;YAAc,YAAY;QAAc;QACvD;YAAE,IAAI;YAAG,MAAM;YAAe,YAAY;QAAY;QACtD;YAAE,IAAI;YAAG,MAAM;YAAgB,YAAY;QAAa;QACxD;YAAE,IAAI;YAAG,MAAM;YAAiB,YAAY;QAAK;QACjD;YAAE,IAAI;YAAG,MAAM;YAAc,YAAY;QAAU;QACnD;YAAE,IAAI;YAAG,MAAM;YAAe,YAAY;QAAQ;QAClD;YAAE,IAAI;YAAG,MAAM;YAAgB,YAAY;QAAc;QACzD;YAAE,IAAI;YAAI,MAAM;YAAe,YAAY;QAAY;KACxD;IAED,MAAM,oBAAoB,gBAAgB,MAAM,CAAC,CAAA,WAC/C,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,SAAS,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGnE,MAAM,uBAAuB,CAAC;QAC5B,MAAM,mBAAmB,QAAQ,SAAS;QAC1C,MAAM,aAAa,iBAAiB,QAAQ,CAAC;QAE7C,IAAI,YAAY;YACd,aAAa,iBAAiB,MAAM,CAAC,CAAA,KAAM,OAAO;QACpD,OAAO;YACL,aAAa;mBAAI;gBAAkB;aAAW;QAChD;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa,kBAAkB,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;IAClD;IAEA,MAAM,mBAAmB;QACvB,aAAa,EAAE;IACjB;IAEA,MAAM,iBAAiB;QACrB,MAAM,gBAAgB,QAAQ,SAAS,CAAC,MAAM;QAC9C,IAAI,kBAAkB,GAAG,OAAO;QAChC,IAAI,kBAAkB,GAAG;YACvB,MAAM,WAAW,gBAAgB,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,QAAQ,SAAS,CAAC,EAAE;YAC5E,OAAO,UAAU,QAAQ;QAC3B;QACA,OAAO,GAAG,cAAc,UAAU,CAAC;IACrC;IAEA,MAAM,WAAW,iBAAiB,SAAS;IAE3C,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;8BAC9B,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAsB;;;;;;8BACvC,6LAAC,sIAAA,CAAA,UAAO;oBAAC,MAAM;oBAAQ,cAAc;;sCACnC,6LAAC,sIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gDACA,YAAY;;kDAGd,6LAAC;wCAAK,WAAU;kDAAY;;;;;;kDAC5B,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAG3B,6LAAC,sIAAA,CAAA,iBAAc;4BAAC,WAAU;4BAAW,OAAM;sCACzC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAGd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,SAAS;0DAAiB;;;;;;0DAG5D,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,SAAS;0DAAkB;;;;;;;;;;;;kDAI/D,6LAAC;wCAAI,WAAU;kDACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC;gDAAsB,WAAU;;kEAC/B,6LAAC,uIAAA,CAAA,WAAQ;wDACP,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;wDAC7B,SAAS,QAAQ,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE;wDAC/C,iBAAiB,IAAM,qBAAqB,SAAS,EAAE;;;;;;kEAEzD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;wDAClC,WAAU;kEAEV,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;sFAAe,SAAS,IAAI;;;;;;sFAC3C,6LAAC;4EAAI,WAAU;sFAAiC,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;;+CAdjE,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAwB9B,0BACC,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAI7C;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAAsB;;;;;;0BACvC,6LAAC,sIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACnC,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gDACA,YAAY;;8CAGd,6LAAC;8CAAM;;;;;;8CACP,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG3B,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;kCACzC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS;8DAAiB;;;;;;8DAG5D,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS;8DAAkB;;;;;;;;;;;;;;;;;;8CAMjE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC;4CAAsB,WAAU;;8DAC/B,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;oDAC7B,SAAS,QAAQ,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE;oDAC/C,iBAAiB,IAAM,qBAAqB,SAAS,EAAE;;;;;;8DAEzD,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SAAS,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;oDAClC,WAAU;8DAEV,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAAe,SAAS,IAAI;;;;;;kFAC3C,6LAAC;wEAAI,WAAU;kFAAiC,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;;2CAdjE,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YA0B9B,QAAQ,SAAS,CAAC,MAAM,GAAG,mBAC1B,6LAAC;gBAAI,WAAU;;oBACZ,QAAQ,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;wBAClC,MAAM,WAAW,gBAAgB,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;wBACxD,IAAI,CAAC,UAAU,OAAO;wBAEtB,qBACE,6LAAC,oIAAA,CAAA,QAAK;4BAAkB,SAAQ;4BAAY,WAAU;;gCACnD,SAAS,IAAI;8CACd,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,qBAAqB;8CAEpC,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BARL;;;;;oBAYhB;oBACC,QAAQ,SAAS,CAAC,MAAM,GAAG,mBAC1B,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAAU;4BAC3C,QAAQ,SAAS,CAAC,MAAM,GAAG;4BAAE;;;;;;;;;;;;;YAMtC,0BACC,6LAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;;AAI7C;GAvOa;;QAIK,2LAAA,CAAA,sBAAmB;QACV,2LAAA,CAAA,6BAA0B;QACtB,2LAAA,CAAA,gCAA6B;;;KAN/C;uCAyOE", "debugId": null}}, {"offset": {"line": 2019, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/filters/VehicleFilter.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/dashboard/filters/VehicleFilter.tsx\r\n\r\nimport React from 'react';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { cn } from '@/lib/utils';\r\nimport { ChevronDown, X, Search, Car } from 'lucide-react';\r\nimport {\r\n  useReportingFilters,\r\n  useReportingFiltersActions,\r\n  useReportingFiltersValidation,\r\n} from '../../data/stores/useReportingFiltersStore';\r\n\r\ninterface VehicleFilterProps {\r\n  compact?: boolean;\r\n  className?: string;\r\n}\r\n\r\nexport const VehicleFilter: React.FC<VehicleFilterProps> = ({\r\n  compact = false,\r\n  className = '',\r\n}) => {\r\n  const filters = useReportingFilters();\r\n  const { setVehicles } = useReportingFiltersActions();\r\n  const { validationErrors } = useReportingFiltersValidation();\r\n\r\n  const [isOpen, setIsOpen] = React.useState(false);\r\n  const [searchTerm, setSearchTerm] = React.useState('');\r\n\r\n  // Mock vehicle options - in real app, this would come from an API\r\n  const vehicleOptions = [\r\n    { id: 1, make: 'Toyota', model: 'Camry', year: 2022, licensePlate: 'ABC-123' },\r\n    { id: 2, make: 'Honda', model: 'Accord', year: 2021, licensePlate: 'DEF-456' },\r\n    { id: 3, make: 'Ford', model: 'F-150', year: 2023, licensePlate: 'GHI-789' },\r\n    { id: 4, make: 'Chevrolet', model: 'Silverado', year: 2022, licensePlate: 'JKL-012' },\r\n    { id: 5, make: 'BMW', model: 'X5', year: 2021, licensePlate: 'MNO-345' },\r\n    { id: 6, make: 'Mercedes', model: 'E-Class', year: 2023, licensePlate: 'PQR-678' },\r\n    { id: 7, make: 'Audi', model: 'A4', year: 2022, licensePlate: 'STU-901' },\r\n    { id: 8, make: 'Nissan', model: 'Altima', year: 2021, licensePlate: 'VWX-234' },\r\n  ];\r\n\r\n  const filteredVehicles = vehicleOptions.filter(vehicle =>\r\n    vehicle.make.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n    vehicle.licensePlate.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  const handleVehicleToggle = (vehicleId: number) => {\r\n    const currentVehicles = filters.vehicles;\r\n    const isSelected = currentVehicles.includes(vehicleId);\r\n    \r\n    if (isSelected) {\r\n      setVehicles(currentVehicles.filter(id => id !== vehicleId));\r\n    } else {\r\n      setVehicles([...currentVehicles, vehicleId]);\r\n    }\r\n  };\r\n\r\n  const handleSelectAll = () => {\r\n    setVehicles(filteredVehicles.map(vehicle => vehicle.id));\r\n  };\r\n\r\n  const handleSelectNone = () => {\r\n    setVehicles([]);\r\n  };\r\n\r\n  const getDisplayText = () => {\r\n    const selectedCount = filters.vehicles.length;\r\n    if (selectedCount === 0) return 'All vehicles';\r\n    if (selectedCount === 1) {\r\n      const vehicle = vehicleOptions.find(v => v.id === filters.vehicles[0]);\r\n      return vehicle ? `${vehicle.make} ${vehicle.model}` : 'Unknown';\r\n    }\r\n    return `${selectedCount} vehicles`;\r\n  };\r\n\r\n  const hasError = validationErrors.vehicles;\r\n\r\n  if (compact) {\r\n    return (\r\n      <div className={cn('space-y-1', className)}>\r\n        <Label className=\"text-xs font-medium\">Vehicle</Label>\r\n        <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n          <PopoverTrigger asChild>\r\n            <Button\r\n              variant=\"outline\"\r\n              className={cn(\r\n                'w-full justify-between text-left font-normal',\r\n                hasError && 'border-red-500'\r\n              )}\r\n            >\r\n              <span className=\"truncate\">{getDisplayText()}</span>\r\n              <ChevronDown className=\"ml-2 h-4 w-4 shrink-0\" />\r\n            </Button>\r\n          </PopoverTrigger>\r\n          <PopoverContent className=\"w-64 p-0\" align=\"start\">\r\n            <div className=\"p-3\">\r\n              <div className=\"relative mb-3\">\r\n                <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n                <Input\r\n                  placeholder=\"Search vehicles...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"pl-8\"\r\n                />\r\n              </div>\r\n              <div className=\"flex justify-between mb-3\">\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectAll}>\r\n                  All\r\n                </Button>\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectNone}>\r\n                  None\r\n                </Button>\r\n              </div>\r\n              <div className=\"max-h-48 overflow-y-auto space-y-2\">\r\n                {filteredVehicles.map((vehicle) => (\r\n                  <div key={vehicle.id} className=\"flex items-center space-x-2\">\r\n                    <Checkbox\r\n                      id={`vehicle-${vehicle.id}`}\r\n                      checked={filters.vehicles.includes(vehicle.id)}\r\n                      onCheckedChange={() => handleVehicleToggle(vehicle.id)}\r\n                    />\r\n                    <Label\r\n                      htmlFor={`vehicle-${vehicle.id}`}\r\n                      className=\"text-sm font-normal cursor-pointer flex-1\"\r\n                    >\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Car className=\"h-3 w-3 text-muted-foreground\" />\r\n                        <div>\r\n                          <div className=\"font-medium\">{vehicle.make} {vehicle.model}</div>\r\n                          <div className=\"text-xs text-muted-foreground\">{vehicle.licensePlate}</div>\r\n                        </div>\r\n                      </div>\r\n                    </Label>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </PopoverContent>\r\n        </Popover>\r\n        {hasError && (\r\n          <p className=\"text-xs text-red-600\">{hasError}</p>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={cn('space-y-2', className)}>\r\n      <Label className=\"text-sm font-medium\">Vehicles</Label>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"outline\"\r\n            className={cn(\r\n              'w-full justify-between text-left font-normal',\r\n              hasError && 'border-red-500'\r\n            )}\r\n          >\r\n            <span>{getDisplayText()}</span>\r\n            <ChevronDown className=\"ml-2 h-4 w-4 shrink-0\" />\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-80 p-0\" align=\"start\">\r\n          <div className=\"p-4\">\r\n            <div className=\"flex justify-between items-center mb-4\">\r\n              <h4 className=\"font-medium text-sm\">Select Vehicles</h4>\r\n              <div className=\"flex gap-2\">\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectAll}>\r\n                  All\r\n                </Button>\r\n                <Button variant=\"ghost\" size=\"sm\" onClick={handleSelectNone}>\r\n                  None\r\n                </Button>\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"relative mb-4\">\r\n              <Search className=\"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n              <Input\r\n                placeholder=\"Search vehicles...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                className=\"pl-9\"\r\n              />\r\n            </div>\r\n            \r\n            <div className=\"max-h-64 overflow-y-auto space-y-3\">\r\n              {filteredVehicles.map((vehicle) => (\r\n                <div key={vehicle.id} className=\"flex items-center space-x-3\">\r\n                  <Checkbox\r\n                    id={`vehicle-${vehicle.id}`}\r\n                    checked={filters.vehicles.includes(vehicle.id)}\r\n                    onCheckedChange={() => handleVehicleToggle(vehicle.id)}\r\n                  />\r\n                  <Label\r\n                    htmlFor={`vehicle-${vehicle.id}`}\r\n                    className=\"text-sm font-normal cursor-pointer flex-1\"\r\n                  >\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <Car className=\"h-4 w-4 text-muted-foreground\" />\r\n                      <div>\r\n                        <div className=\"font-medium\">{vehicle.make} {vehicle.model} ({vehicle.year})</div>\r\n                        <div className=\"text-xs text-muted-foreground\">{vehicle.licensePlate}</div>\r\n                      </div>\r\n                    </div>\r\n                  </Label>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n      \r\n      {/* Selected vehicle badges */}\r\n      {filters.vehicles.length > 0 && (\r\n        <div className=\"flex flex-wrap gap-1\">\r\n          {filters.vehicles.slice(0, 3).map((vehicleId) => {\r\n            const vehicle = vehicleOptions.find(v => v.id === vehicleId);\r\n            if (!vehicle) return null;\r\n            \r\n            return (\r\n              <Badge key={vehicleId} variant=\"secondary\" className=\"text-xs pr-1\">\r\n                {vehicle.make} {vehicle.model}\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  className=\"h-auto p-0 ml-1 hover:bg-transparent\"\r\n                  onClick={() => handleVehicleToggle(vehicleId)}\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </Button>\r\n              </Badge>\r\n            );\r\n          })}\r\n          {filters.vehicles.length > 3 && (\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              +{filters.vehicles.length - 3} more\r\n            </Badge>\r\n          )}\r\n        </div>\r\n      )}\r\n      \r\n      {hasError && (\r\n        <p className=\"text-sm text-red-600\">{hasError}</p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default VehicleFilter;\r\n"], "names": [], "mappings": "AAAA,iFAAiF;;;;;;AAEjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;;;;;;;;AAWO,MAAM,gBAA8C,CAAC,EAC1D,UAAU,KAAK,EACf,YAAY,EAAE,EACf;;IACC,MAAM,UAAU,CAAA,GAAA,2LAAA,CAAA,sBAAmB,AAAD;IAClC,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,6BAA0B,AAAD;IACjD,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,gCAA6B,AAAD;IAEzD,MAAM,CAAC,QAAQ,UAAU,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,kEAAkE;IAClE,MAAM,iBAAiB;QACrB;YAAE,IAAI;YAAG,MAAM;YAAU,OAAO;YAAS,MAAM;YAAM,cAAc;QAAU;QAC7E;YAAE,IAAI;YAAG,MAAM;YAAS,OAAO;YAAU,MAAM;YAAM,cAAc;QAAU;QAC7E;YAAE,IAAI;YAAG,MAAM;YAAQ,OAAO;YAAS,MAAM;YAAM,cAAc;QAAU;QAC3E;YAAE,IAAI;YAAG,MAAM;YAAa,OAAO;YAAa,MAAM;YAAM,cAAc;QAAU;QACpF;YAAE,IAAI;YAAG,MAAM;YAAO,OAAO;YAAM,MAAM;YAAM,cAAc;QAAU;QACvE;YAAE,IAAI;YAAG,MAAM;YAAY,OAAO;YAAW,MAAM;YAAM,cAAc;QAAU;QACjF;YAAE,IAAI;YAAG,MAAM;YAAQ,OAAO;YAAM,MAAM;YAAM,cAAc;QAAU;QACxE;YAAE,IAAI;YAAG,MAAM;YAAU,OAAO;YAAU,MAAM;YAAM,cAAc;QAAU;KAC/E;IAED,MAAM,mBAAmB,eAAe,MAAM,CAAC,CAAA,UAC7C,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC3D,QAAQ,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGpE,MAAM,sBAAsB,CAAC;QAC3B,MAAM,kBAAkB,QAAQ,QAAQ;QACxC,MAAM,aAAa,gBAAgB,QAAQ,CAAC;QAE5C,IAAI,YAAY;YACd,YAAY,gBAAgB,MAAM,CAAC,CAAA,KAAM,OAAO;QAClD,OAAO;YACL,YAAY;mBAAI;gBAAiB;aAAU;QAC7C;IACF;IAEA,MAAM,kBAAkB;QACtB,YAAY,iBAAiB,GAAG,CAAC,CAAA,UAAW,QAAQ,EAAE;IACxD;IAEA,MAAM,mBAAmB;QACvB,YAAY,EAAE;IAChB;IAEA,MAAM,iBAAiB;QACrB,MAAM,gBAAgB,QAAQ,QAAQ,CAAC,MAAM;QAC7C,IAAI,kBAAkB,GAAG,OAAO;QAChC,IAAI,kBAAkB,GAAG;YACvB,MAAM,UAAU,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,QAAQ,CAAC,EAAE;YACrE,OAAO,UAAU,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,EAAE,GAAG;QACxD;QACA,OAAO,GAAG,cAAc,SAAS,CAAC;IACpC;IAEA,MAAM,WAAW,iBAAiB,QAAQ;IAE1C,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;8BAC9B,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BAAsB;;;;;;8BACvC,6LAAC,sIAAA,CAAA,UAAO;oBAAC,MAAM;oBAAQ,cAAc;;sCACnC,6LAAC,sIAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gDACA,YAAY;;kDAGd,6LAAC;wCAAK,WAAU;kDAAY;;;;;;kDAC5B,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAG3B,6LAAC,sIAAA,CAAA,iBAAc;4BAAC,WAAU;4BAAW,OAAM;sCACzC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAGd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,SAAS;0DAAiB;;;;;;0DAG5D,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,SAAS;0DAAkB;;;;;;;;;;;;kDAI/D,6LAAC;wCAAI,WAAU;kDACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;gDAAqB,WAAU;;kEAC9B,6LAAC,uIAAA,CAAA,WAAQ;wDACP,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;wDAC3B,SAAS,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE;wDAC7C,iBAAiB,IAAM,oBAAoB,QAAQ,EAAE;;;;;;kEAEvD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,SAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;wDAChC,WAAU;kEAEV,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;8EACf,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;;gFAAe,QAAQ,IAAI;gFAAC;gFAAE,QAAQ,KAAK;;;;;;;sFAC1D,6LAAC;4EAAI,WAAU;sFAAiC,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;;;;;;+CAdlE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAwB7B,0BACC,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAI7C;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,6LAAC,oIAAA,CAAA,QAAK;gBAAC,WAAU;0BAAsB;;;;;;0BACvC,6LAAC,sIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACnC,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gDACA,YAAY;;8CAGd,6LAAC;8CAAM;;;;;;8CACP,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG3B,6LAAC,sIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;kCACzC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS;8DAAiB;;;;;;8DAG5D,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS;8DAAkB;;;;;;;;;;;;;;;;;;8CAMjE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;8CACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;4CAAqB,WAAU;;8DAC9B,6LAAC,uIAAA,CAAA,WAAQ;oDACP,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;oDAC3B,SAAS,QAAQ,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE;oDAC7C,iBAAiB,IAAM,oBAAoB,QAAQ,EAAE;;;;;;8DAEvD,6LAAC,oIAAA,CAAA,QAAK;oDACJ,SAAS,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;oDAChC,WAAU;8DAEV,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;0EACf,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;;4EAAe,QAAQ,IAAI;4EAAC;4EAAE,QAAQ,KAAK;4EAAC;4EAAG,QAAQ,IAAI;4EAAC;;;;;;;kFAC3E,6LAAC;wEAAI,WAAU;kFAAiC,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;;;;;;2CAdlE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YA0B7B,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,6LAAC;gBAAI,WAAU;;oBACZ,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;wBACjC,MAAM,UAAU,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wBAClD,IAAI,CAAC,SAAS,OAAO;wBAErB,qBACE,6LAAC,oIAAA,CAAA,QAAK;4BAAiB,SAAQ;4BAAY,WAAU;;gCAClD,QAAQ,IAAI;gCAAC;gCAAE,QAAQ,KAAK;8CAC7B,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAEnC,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;2BARL;;;;;oBAYhB;oBACC,QAAQ,QAAQ,CAAC,MAAM,GAAG,mBACzB,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAAU;4BAC3C,QAAQ,QAAQ,CAAC,MAAM,GAAG;4BAAE;;;;;;;;;;;;;YAMrC,0BACC,6LAAC;gBAAE,WAAU;0BAAwB;;;;;;;;;;;;AAI7C;GAtOa;;QAIK,2LAAA,CAAA,sBAAmB;QACX,2LAAA,CAAA,6BAA0B;QACrB,2LAAA,CAAA,gCAA6B;;;KAN/C;uCAwOE", "debugId": null}}, {"offset": {"line": 2676, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/filters/FilterPresets.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/dashboard/filters/FilterPresets.tsx\r\n\r\nimport React from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { cn } from '@/lib/utils';\r\nimport { Save, ChevronDown, Trash2, Star } from 'lucide-react';\r\nimport {\r\n  useReportingFiltersPresets,\r\n  useReportingFilters,\r\n} from '../../data/stores/useReportingFiltersStore';\r\n\r\ninterface FilterPresetsProps {\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * @component FilterPresets\r\n * @description Filter presets management component for reporting dashboard\r\n *\r\n * Responsibilities:\r\n * - Provides preset save/load/delete functionality\r\n * - Manages preset storage and retrieval\r\n * - Displays available presets with quick access\r\n * - Handles preset validation and naming\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of managing filter presets\r\n * - OCP: Open for extension via props\r\n * - DIP: Depends on filter store abstractions\r\n */\r\n\r\n// Custom hook to manage presets state efficiently\r\nconst usePresets = () => {\r\n  const [presets, setPresets] = React.useState<Record<string, any>>({});\r\n  const [refreshTrigger, setRefreshTrigger] = React.useState(0);\r\n\r\n  // Load presets from localStorage\r\n  const loadPresets = React.useCallback(() => {\r\n    try {\r\n      const stored = localStorage.getItem('reporting-filter-presets');\r\n      const loadedPresets = stored ? JSON.parse(stored) : {};\r\n      setPresets(loadedPresets);\r\n    } catch {\r\n      setPresets({});\r\n    }\r\n  }, []);\r\n\r\n  // Load presets on mount and when refresh is triggered\r\n  React.useEffect(() => {\r\n    loadPresets();\r\n  }, [loadPresets, refreshTrigger]);\r\n\r\n  // Trigger refresh\r\n  const refreshPresets = React.useCallback(() => {\r\n    setRefreshTrigger(prev => prev + 1);\r\n  }, []);\r\n\r\n  return { presets, refreshPresets };\r\n};\r\n\r\nexport const FilterPresets: React.FC<FilterPresetsProps> = ({\r\n  className = '',\r\n}) => {\r\n  const { applyPreset, saveAsPreset, deletePreset } =\r\n    useReportingFiltersPresets();\r\n  const currentFilters = useReportingFilters();\r\n  const { presets, refreshPresets } = usePresets();\r\n\r\n  const [isDialogOpen, setIsDialogOpen] = React.useState(false);\r\n  const [presetName, setPresetName] = React.useState('');\r\n\r\n  // Handle save preset\r\n  const handleSavePreset = React.useCallback(() => {\r\n    if (presetName.trim()) {\r\n      saveAsPreset(presetName.trim());\r\n      setPresetName('');\r\n      setIsDialogOpen(false);\r\n      refreshPresets(); // Refresh presets after saving\r\n    }\r\n  }, [presetName, saveAsPreset, refreshPresets]);\r\n\r\n  // Handle apply preset\r\n  const handleApplyPreset = React.useCallback(\r\n    (name: string) => {\r\n      applyPreset(name);\r\n    },\r\n    [applyPreset]\r\n  );\r\n\r\n  // Handle delete preset\r\n  const handleDeletePreset = React.useCallback(\r\n    (name: string) => {\r\n      deletePreset(name);\r\n      refreshPresets(); // Refresh presets after deleting\r\n    },\r\n    [deletePreset, refreshPresets]\r\n  );\r\n\r\n  // Get preset summary\r\n  const getPresetSummary = React.useCallback((preset: any) => {\r\n    const parts = [];\r\n    if (preset.status?.length > 0) parts.push(`${preset.status.length} status`);\r\n    if (preset.locations?.length > 0)\r\n      parts.push(`${preset.locations.length} locations`);\r\n    if (preset.employees?.length > 0)\r\n      parts.push(`${preset.employees.length} employees`);\r\n    if (preset.vehicles?.length > 0)\r\n      parts.push(`${preset.vehicles.length} vehicles`);\r\n\r\n    return parts.length > 0 ? parts.join(', ') : 'No filters';\r\n  }, []);\r\n\r\n  // Default presets\r\n  const defaultPresets = [\r\n    {\r\n      name: 'Last 30 Days',\r\n      description: 'All delegations from the last 30 days',\r\n      action: () => {\r\n        // Apply default 30-day filter\r\n        const filters = {\r\n          dateRange: {\r\n            from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),\r\n            to: new Date(),\r\n          },\r\n          status: [],\r\n          locations: [],\r\n          employees: [],\r\n          vehicles: [],\r\n        };\r\n        // This would need to be implemented in the store\r\n        console.log('Applying 30-day preset', filters);\r\n      },\r\n    },\r\n    {\r\n      name: 'Active Delegations',\r\n      description: 'In progress and approved delegations',\r\n      action: () => {\r\n        console.log('Applying active delegations preset');\r\n      },\r\n    },\r\n    {\r\n      name: 'Completed This Month',\r\n      description: 'Completed delegations from current month',\r\n      action: () => {\r\n        console.log('Applying completed this month preset');\r\n      },\r\n    },\r\n  ];\r\n\r\n  const presetNames = Object.keys(presets);\r\n\r\n  return (\r\n    <div className={cn('space-y-3', className)}>\r\n      <div className=\"flex items-center justify-between\">\r\n        <Label className=\"text-sm font-medium\">Filter Presets</Label>\r\n        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>\r\n          <DialogTrigger asChild>\r\n            <Button variant=\"outline\" size=\"sm\" className=\"h-8\">\r\n              <Save className=\"h-4 w-4 mr-2\" />\r\n              Save\r\n            </Button>\r\n          </DialogTrigger>\r\n          <DialogContent className=\"sm:max-w-md\">\r\n            <DialogHeader>\r\n              <DialogTitle>Save Filter Preset</DialogTitle>\r\n              <DialogDescription>\r\n                Save your current filter settings as a preset for quick access\r\n                later.\r\n              </DialogDescription>\r\n            </DialogHeader>\r\n            <div className=\"space-y-4\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"preset-name\">Preset Name</Label>\r\n                <Input\r\n                  id=\"preset-name\"\r\n                  placeholder=\"Enter preset name...\"\r\n                  value={presetName}\r\n                  onChange={e => setPresetName(e.target.value)}\r\n                  onKeyDown={e => {\r\n                    if (e.key === 'Enter') {\r\n                      handleSavePreset();\r\n                    }\r\n                  }}\r\n                />\r\n              </div>\r\n              <div className=\"p-3 bg-muted rounded-lg\">\r\n                <p className=\"text-sm text-muted-foreground mb-2\">\r\n                  Current filters:\r\n                </p>\r\n                <p className=\"text-sm\">{getPresetSummary(currentFilters)}</p>\r\n              </div>\r\n            </div>\r\n            <DialogFooter>\r\n              <Button variant=\"outline\" onClick={() => setIsDialogOpen(false)}>\r\n                Cancel\r\n              </Button>\r\n              <Button onClick={handleSavePreset} disabled={!presetName.trim()}>\r\n                Save Preset\r\n              </Button>\r\n            </DialogFooter>\r\n          </DialogContent>\r\n        </Dialog>\r\n      </div>\r\n\r\n      {/* Default presets */}\r\n      <div className=\"space-y-2\">\r\n        <p className=\"text-xs text-muted-foreground\">Quick Presets</p>\r\n        <div className=\"flex flex-wrap gap-2\">\r\n          {defaultPresets.map(preset => (\r\n            <Button\r\n              key={preset.name}\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={preset.action}\r\n              className=\"h-8 text-xs\"\r\n            >\r\n              <Star className=\"h-3 w-3 mr-1\" />\r\n              {preset.name}\r\n            </Button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Saved presets */}\r\n      {presetNames.length > 0 && (\r\n        <div className=\"space-y-2\">\r\n          <p className=\"text-xs text-muted-foreground\">Saved Presets</p>\r\n          <div className=\"space-y-1\">\r\n            {presetNames.map(name => (\r\n              <div\r\n                key={name}\r\n                className=\"flex items-center justify-between p-2 rounded-lg border bg-card\"\r\n              >\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"text-sm font-medium truncate\">{name}</p>\r\n                  <p className=\"text-xs text-muted-foreground\">\r\n                    {getPresetSummary(presets[name])}\r\n                  </p>\r\n                </div>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={() => handleApplyPreset(name)}\r\n                    className=\"h-8 px-2 text-xs\"\r\n                  >\r\n                    Apply\r\n                  </Button>\r\n                  <DropdownMenu>\r\n                    <DropdownMenuTrigger asChild>\r\n                      <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\r\n                        <ChevronDown className=\"h-4 w-4\" />\r\n                      </Button>\r\n                    </DropdownMenuTrigger>\r\n                    <DropdownMenuContent align=\"end\">\r\n                      <DropdownMenuItem onClick={() => handleApplyPreset(name)}>\r\n                        Apply Preset\r\n                      </DropdownMenuItem>\r\n                      <DropdownMenuSeparator />\r\n                      <DropdownMenuItem\r\n                        onClick={() => handleDeletePreset(name)}\r\n                        className=\"text-red-600\"\r\n                      >\r\n                        <Trash2 className=\"h-4 w-4 mr-2\" />\r\n                        Delete\r\n                      </DropdownMenuItem>\r\n                    </DropdownMenuContent>\r\n                  </DropdownMenu>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FilterPresets;\r\n"], "names": [], "mappings": "AAAA,iFAAiF;;;;;;AAEjF;AACA;AAEA;AACA;AACA;AASA;AAOA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;;;;;;;AASA;;;;;;;;;;;;;;CAcC,GAED,kDAAkD;AAClD,MAAM,aAAa;;IACjB,MAAM,CAAC,SAAS,WAAW,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAsB,CAAC;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE3D,iCAAiC;IACjC,MAAM,cAAc,6JAAA,CAAA,UAAK,CAAC,WAAW;+CAAC;YACpC,IAAI;gBACF,MAAM,SAAS,aAAa,OAAO,CAAC;gBACpC,MAAM,gBAAgB,SAAS,KAAK,KAAK,CAAC,UAAU,CAAC;gBACrD,WAAW;YACb,EAAE,OAAM;gBACN,WAAW,CAAC;YACd;QACF;8CAAG,EAAE;IAEL,sDAAsD;IACtD,6JAAA,CAAA,UAAK,CAAC,SAAS;gCAAC;YACd;QACF;+BAAG;QAAC;QAAa;KAAe;IAEhC,kBAAkB;IAClB,MAAM,iBAAiB,6JAAA,CAAA,UAAK,CAAC,WAAW;kDAAC;YACvC;0DAAkB,CAAA,OAAQ,OAAO;;QACnC;iDAAG,EAAE;IAEL,OAAO;QAAE;QAAS;IAAe;AACnC;GA1BM;AA4BC,MAAM,gBAA8C,CAAC,EAC1D,YAAY,EAAE,EACf;;IACC,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,GAC/C,CAAA,GAAA,2LAAA,CAAA,6BAA0B,AAAD;IAC3B,MAAM,iBAAiB,CAAA,GAAA,2LAAA,CAAA,sBAAmB,AAAD;IACzC,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG;IAEpC,MAAM,CAAC,cAAc,gBAAgB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBAAqB;IACrB,MAAM,mBAAmB,6JAAA,CAAA,UAAK,CAAC,WAAW;uDAAC;YACzC,IAAI,WAAW,IAAI,IAAI;gBACrB,aAAa,WAAW,IAAI;gBAC5B,cAAc;gBACd,gBAAgB;gBAChB,kBAAkB,+BAA+B;YACnD;QACF;sDAAG;QAAC;QAAY;QAAc;KAAe;IAE7C,sBAAsB;IACtB,MAAM,oBAAoB,6JAAA,CAAA,UAAK,CAAC,WAAW;wDACzC,CAAC;YACC,YAAY;QACd;uDACA;QAAC;KAAY;IAGf,uBAAuB;IACvB,MAAM,qBAAqB,6JAAA,CAAA,UAAK,CAAC,WAAW;yDAC1C,CAAC;YACC,aAAa;YACb,kBAAkB,iCAAiC;QACrD;wDACA;QAAC;QAAc;KAAe;IAGhC,qBAAqB;IACrB,MAAM,mBAAmB,6JAAA,CAAA,UAAK,CAAC,WAAW;uDAAC,CAAC;YAC1C,MAAM,QAAQ,EAAE;YAChB,IAAI,OAAO,MAAM,EAAE,SAAS,GAAG,MAAM,IAAI,CAAC,GAAG,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;YAC1E,IAAI,OAAO,SAAS,EAAE,SAAS,GAC7B,MAAM,IAAI,CAAC,GAAG,OAAO,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC;YACnD,IAAI,OAAO,SAAS,EAAE,SAAS,GAC7B,MAAM,IAAI,CAAC,GAAG,OAAO,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC;YACnD,IAAI,OAAO,QAAQ,EAAE,SAAS,GAC5B,MAAM,IAAI,CAAC,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC;YAEjD,OAAO,MAAM,MAAM,GAAG,IAAI,MAAM,IAAI,CAAC,QAAQ;QAC/C;sDAAG,EAAE;IAEL,kBAAkB;IAClB,MAAM,iBAAiB;QACrB;YACE,MAAM;YACN,aAAa;YACb,QAAQ;gBACN,8BAA8B;gBAC9B,MAAM,UAAU;oBACd,WAAW;wBACT,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;wBAChD,IAAI,IAAI;oBACV;oBACA,QAAQ,EAAE;oBACV,WAAW,EAAE;oBACb,WAAW,EAAE;oBACb,UAAU,EAAE;gBACd;gBACA,iDAAiD;gBACjD,QAAQ,GAAG,CAAC,0BAA0B;YACxC;QACF;QACA;YACE,MAAM;YACN,aAAa;YACb,QAAQ;gBACN,QAAQ,GAAG,CAAC;YACd;QACF;QACA;YACE,MAAM;YACN,aAAa;YACb,QAAQ;gBACN,QAAQ,GAAG,CAAC;YACd;QACF;KACD;IAED,MAAM,cAAc,OAAO,IAAI,CAAC;IAEhC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oIAAA,CAAA,QAAK;wBAAC,WAAU;kCAAsB;;;;;;kCACvC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAc,cAAc;;0CACxC,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;;sDAC5C,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIrC,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,6LAAC,qIAAA,CAAA,eAAY;;0DACX,6LAAC,qIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,6LAAC,qIAAA,CAAA,oBAAiB;0DAAC;;;;;;;;;;;;kDAKrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAc;;;;;;kEAC7B,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAA,IAAK,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC3C,WAAW,CAAA;4DACT,IAAI,EAAE,GAAG,KAAK,SAAS;gEACrB;4DACF;wDACF;;;;;;;;;;;;0DAGJ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAGlD,6LAAC;wDAAE,WAAU;kEAAW,iBAAiB;;;;;;;;;;;;;;;;;;kDAG7C,6LAAC,qIAAA,CAAA,eAAY;;0DACX,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAM,gBAAgB;0DAAQ;;;;;;0DAGjE,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAkB,UAAU,CAAC,WAAW,IAAI;0DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;kCAC7C,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAA,uBAClB,6LAAC,qIAAA,CAAA,SAAM;gCAEL,SAAQ;gCACR,MAAK;gCACL,SAAS,OAAO,MAAM;gCACtB,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,OAAO,IAAI;;+BAPP,OAAO,IAAI;;;;;;;;;;;;;;;;YAcvB,YAAY,MAAM,GAAG,mBACpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;kCAC7C,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAA,qBACf,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;0DAC7C,6LAAC;gDAAE,WAAU;0DACV,iBAAiB,OAAO,CAAC,KAAK;;;;;;;;;;;;kDAGnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,kBAAkB;gDACjC,WAAU;0DACX;;;;;;0DAGD,6LAAC,+IAAA,CAAA,eAAY;;kEACX,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;4DAAK,WAAU;sEAC1C,cAAA,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG3B,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,6LAAC,+IAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,kBAAkB;0EAAO;;;;;;0EAG1D,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0EACtB,6LAAC,+IAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,mBAAmB;gEAClC,WAAU;;kFAEV,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;+BAjCtC;;;;;;;;;;;;;;;;;;;;;;AA8CrB;IAxNa;;QAIT,2LAAA,CAAA,6BAA0B;QACL,2LAAA,CAAA,sBAAmB;QACN;;;KANzB;uCA0NE", "debugId": null}}, {"offset": {"line": 3247, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/filters/ReportingFilters.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/dashboard/filters/ReportingFilters.tsx\r\n\r\nimport React from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { DateRangeFilter } from './DateRangeFilter';\r\nimport { StatusFilter } from './StatusFilter';\r\nimport { LocationFilter } from './LocationFilter';\r\nimport { EmployeeFilter } from './EmployeeFilter';\r\nimport { VehicleFilter } from './VehicleFilter';\r\nimport { FilterPresets } from './FilterPresets';\r\nimport {\r\n  useReportingFilters,\r\n  useReportingFiltersActions,\r\n  useReportingFiltersUI,\r\n  useReportingFiltersValidation,\r\n} from '../../data/stores/useReportingFiltersStore';\r\nimport { Filter, X, RotateCcw, Check } from 'lucide-react';\r\n\r\ninterface ReportingFiltersProps {\r\n  className?: string;\r\n  showPresets?: boolean;\r\n  compact?: boolean;\r\n  // ENHANCED: Optional service history support\r\n  includeServiceFilters?: boolean;\r\n  includeTaskFilters?: boolean;\r\n}\r\n\r\n/**\r\n * @component ReportingFilters\r\n * @description Comprehensive filter panel for reporting dashboard following SOLID principles\r\n *\r\n * Responsibilities:\r\n * - Provides unified interface for all reporting filters\r\n * - Manages filter state and validation\r\n * - Handles filter presets and bulk operations\r\n * - Displays filter status and applied filters\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of managing reporting filters UI\r\n * - OCP: Open for extension via new filter components\r\n * - DIP: Depends on filter store abstractions\r\n */\r\nexport const ReportingFilters: React.FC<ReportingFiltersProps> = ({\r\n  className = '',\r\n  showPresets = true,\r\n  compact = false,\r\n  // ENHANCED: Service and task filter support\r\n  includeServiceFilters = false,\r\n  includeTaskFilters = false,\r\n}) => {\r\n  // Filter state and actions\r\n  const filters = useReportingFilters();\r\n  const { resetFilters, applyFilters, revertChanges } =\r\n    useReportingFiltersActions();\r\n  const { isFilterPanelOpen, hasUnsavedChanges, setFilterPanelOpen } =\r\n    useReportingFiltersUI();\r\n  const { validationErrors, isValid } = useReportingFiltersValidation();\r\n\r\n  // Handle filter actions\r\n  const handleReset = () => {\r\n    resetFilters();\r\n  };\r\n\r\n  const handleApply = () => {\r\n    if (isValid) {\r\n      applyFilters();\r\n    }\r\n  };\r\n\r\n  const handleRevert = () => {\r\n    revertChanges();\r\n  };\r\n\r\n  const handleClear = () => {\r\n    setFilterPanelOpen(false);\r\n  };\r\n\r\n  // FIXED: Memoize active filter count calculation to prevent unnecessary re-renders\r\n  const activeFilterCount = React.useMemo(() => {\r\n    let count = 0;\r\n\r\n    // Date range is always active, so don't count it\r\n    if (filters.status.length > 0) count++;\r\n    if (filters.locations.length > 0) count++;\r\n    if (filters.employees.length > 0) count++;\r\n    if (filters.vehicles.length > 0) count++;\r\n\r\n    // ENHANCED: Count service filters when enabled\r\n    if (includeServiceFilters) {\r\n      if (filters.serviceTypes?.length) count++;\r\n      if (filters.serviceStatus?.length) count++;\r\n      if (filters.costRange) count++;\r\n    }\r\n\r\n    // ENHANCED: Count task filters when enabled\r\n    if (includeTaskFilters && filters.includeTaskData) {\r\n      count++;\r\n    }\r\n\r\n    return count;\r\n  }, [filters, includeServiceFilters, includeTaskFilters]);\r\n\r\n  // FIXED: Memoize filter summary badges to prevent unnecessary re-renders\r\n  const filterSummaryBadges = React.useMemo(() => {\r\n    const badges = [];\r\n\r\n    if (filters.status.length > 0) {\r\n      badges.push(\r\n        <Badge key=\"status\" variant=\"secondary\" className=\"text-xs\">\r\n          Status: {filters.status.length}\r\n        </Badge>\r\n      );\r\n    }\r\n\r\n    if (filters.locations.length > 0) {\r\n      badges.push(\r\n        <Badge key=\"locations\" variant=\"secondary\" className=\"text-xs\">\r\n          Locations: {filters.locations.length}\r\n        </Badge>\r\n      );\r\n    }\r\n\r\n    if (filters.employees.length > 0) {\r\n      badges.push(\r\n        <Badge key=\"employees\" variant=\"secondary\" className=\"text-xs\">\r\n          Employees: {filters.employees.length}\r\n        </Badge>\r\n      );\r\n    }\r\n\r\n    if (filters.vehicles.length > 0) {\r\n      badges.push(\r\n        <Badge key=\"vehicles\" variant=\"secondary\" className=\"text-xs\">\r\n          Vehicles: {filters.vehicles.length}\r\n        </Badge>\r\n      );\r\n    }\r\n\r\n    // ENHANCED: Service filter badges\r\n    if (includeServiceFilters) {\r\n      if (filters.serviceTypes?.length) {\r\n        badges.push(\r\n          <Badge key=\"serviceTypes\" variant=\"secondary\" className=\"text-xs\">\r\n            Service Types: {filters.serviceTypes.length}\r\n          </Badge>\r\n        );\r\n      }\r\n\r\n      if (filters.serviceStatus?.length) {\r\n        badges.push(\r\n          <Badge key=\"serviceStatus\" variant=\"secondary\" className=\"text-xs\">\r\n            Service Status: {filters.serviceStatus.length}\r\n          </Badge>\r\n        );\r\n      }\r\n\r\n      if (filters.costRange) {\r\n        badges.push(\r\n          <Badge key=\"costRange\" variant=\"secondary\" className=\"text-xs\">\r\n            Cost: ${filters.costRange.min} - ${filters.costRange.max}\r\n          </Badge>\r\n        );\r\n      }\r\n    }\r\n\r\n    // ENHANCED: Task filter badges\r\n    if (includeTaskFilters && filters.includeTaskData) {\r\n      badges.push(\r\n        <Badge key=\"taskData\" variant=\"secondary\" className=\"text-xs\">\r\n          Task Data Included\r\n        </Badge>\r\n      );\r\n    }\r\n\r\n    return badges;\r\n  }, [filters, includeServiceFilters, includeTaskFilters]);\r\n\r\n  // Render validation errors\r\n  const renderValidationErrors = () => {\r\n    const errors = Object.values(validationErrors);\r\n    if (errors.length === 0) return null;\r\n\r\n    return (\r\n      <div className=\"space-y-1\">\r\n        {errors.map((error, index) => (\r\n          <div\r\n            key={index}\r\n            className=\"text-sm text-red-600 bg-red-50 p-2 rounded\"\r\n          >\r\n            {error}\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  if (compact) {\r\n    return (\r\n      <div className={`space-y-4 ${className}`}>\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <Filter className=\"h-4 w-4\" />\r\n            <span className=\"text-sm font-medium\">Filters</span>\r\n            {activeFilterCount > 0 && (\r\n              <Badge variant=\"default\" className=\"text-xs\">\r\n                {activeFilterCount}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <Button variant=\"ghost\" size=\"sm\" onClick={handleReset}>\r\n              <RotateCcw className=\"h-4 w-4\" />\r\n            </Button>\r\n            <Button\r\n              variant=\"default\"\r\n              size=\"sm\"\r\n              onClick={handleApply}\r\n              disabled={!isValid || !hasUnsavedChanges}\r\n            >\r\n              <Check className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-2 lg:grid-cols-5 gap-4\">\r\n          <DateRangeFilter compact />\r\n          <StatusFilter compact />\r\n          <LocationFilter compact />\r\n          <EmployeeFilter compact />\r\n          <VehicleFilter compact />\r\n        </div>\r\n\r\n        {renderValidationErrors()}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader className=\"pb-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Filter className=\"h-5 w-5\" />\r\n            Reporting Filters\r\n            {activeFilterCount > 0 && (\r\n              <Badge variant=\"default\" className=\"text-xs\">\r\n                {activeFilterCount} active\r\n              </Badge>\r\n            )}\r\n          </CardTitle>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={handleClear}\r\n            className=\"h-8 w-8 p-0\"\r\n          >\r\n            <X className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n\r\n        {filterSummaryBadges.length > 0 && (\r\n          <div className=\"flex flex-wrap gap-2 mt-2\">{filterSummaryBadges}</div>\r\n        )}\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-6\">\r\n        {showPresets && (\r\n          <>\r\n            <FilterPresets />\r\n            <Separator />\r\n          </>\r\n        )}\r\n\r\n        <div className=\"space-y-4\">\r\n          <DateRangeFilter />\r\n          <StatusFilter />\r\n          <LocationFilter />\r\n          <EmployeeFilter />\r\n          <VehicleFilter />\r\n        </div>\r\n\r\n        {renderValidationErrors()}\r\n\r\n        <Separator />\r\n\r\n        <div className=\"flex items-center justify-between\">\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={handleReset}\r\n            className=\"flex items-center gap-2\"\r\n          >\r\n            <RotateCcw className=\"h-4 w-4\" />\r\n            Reset All\r\n          </Button>\r\n\r\n          <div className=\"flex items-center gap-2\">\r\n            {hasUnsavedChanges && (\r\n              <Button variant=\"ghost\" onClick={handleRevert}>\r\n                Revert\r\n              </Button>\r\n            )}\r\n            <Button\r\n              onClick={handleApply}\r\n              disabled={!isValid || !hasUnsavedChanges}\r\n              className=\"flex items-center gap-2\"\r\n            >\r\n              <Check className=\"h-4 w-4\" />\r\n              Apply Filters\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default ReportingFilters;\r\n"], "names": [], "mappings": "AAAA,oFAAoF;;;;;;AAEpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;AA0BO,MAAM,mBAAoD,CAAC,EAChE,YAAY,EAAE,EACd,cAAc,IAAI,EAClB,UAAU,KAAK,EACf,4CAA4C;AAC5C,wBAAwB,KAAK,EAC7B,qBAAqB,KAAK,EAC3B;;IACC,2BAA2B;IAC3B,MAAM,UAAU,CAAA,GAAA,2LAAA,CAAA,sBAAmB,AAAD;IAClC,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,GACjD,CAAA,GAAA,2LAAA,CAAA,6BAA0B,AAAD;IAC3B,MAAM,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,GAChE,CAAA,GAAA,2LAAA,CAAA,wBAAqB,AAAD;IACtB,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2LAAA,CAAA,gCAA6B,AAAD;IAElE,wBAAwB;IACxB,MAAM,cAAc;QAClB;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,SAAS;YACX;QACF;IACF;IAEA,MAAM,eAAe;QACnB;IACF;IAEA,MAAM,cAAc;QAClB,mBAAmB;IACrB;IAEA,mFAAmF;IACnF,MAAM,oBAAoB,6JAAA,CAAA,UAAK,CAAC,OAAO;uDAAC;YACtC,IAAI,QAAQ;YAEZ,iDAAiD;YACjD,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;YAC/B,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;YAClC,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;YAClC,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YAEjC,+CAA+C;YAC/C,IAAI,uBAAuB;gBACzB,IAAI,QAAQ,YAAY,EAAE,QAAQ;gBAClC,IAAI,QAAQ,aAAa,EAAE,QAAQ;gBACnC,IAAI,QAAQ,SAAS,EAAE;YACzB;YAEA,4CAA4C;YAC5C,IAAI,sBAAsB,QAAQ,eAAe,EAAE;gBACjD;YACF;YAEA,OAAO;QACT;sDAAG;QAAC;QAAS;QAAuB;KAAmB;IAEvD,yEAAyE;IACzE,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,OAAO;yDAAC;YACxC,MAAM,SAAS,EAAE;YAEjB,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;gBAC7B,OAAO,IAAI,eACT,6LAAC,oIAAA,CAAA,QAAK;oBAAc,SAAQ;oBAAY,WAAU;;wBAAU;wBACjD,QAAQ,MAAM,CAAC,MAAM;;mBADrB;;;;;YAIf;YAEA,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;gBAChC,OAAO,IAAI,eACT,6LAAC,oIAAA,CAAA,QAAK;oBAAiB,SAAQ;oBAAY,WAAU;;wBAAU;wBACjD,QAAQ,SAAS,CAAC,MAAM;;mBAD3B;;;;;YAIf;YAEA,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;gBAChC,OAAO,IAAI,eACT,6LAAC,oIAAA,CAAA,QAAK;oBAAiB,SAAQ;oBAAY,WAAU;;wBAAU;wBACjD,QAAQ,SAAS,CAAC,MAAM;;mBAD3B;;;;;YAIf;YAEA,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;gBAC/B,OAAO,IAAI,eACT,6LAAC,oIAAA,CAAA,QAAK;oBAAgB,SAAQ;oBAAY,WAAU;;wBAAU;wBACjD,QAAQ,QAAQ,CAAC,MAAM;;mBADzB;;;;;YAIf;YAEA,kCAAkC;YAClC,IAAI,uBAAuB;gBACzB,IAAI,QAAQ,YAAY,EAAE,QAAQ;oBAChC,OAAO,IAAI,eACT,6LAAC,oIAAA,CAAA,QAAK;wBAAoB,SAAQ;wBAAY,WAAU;;4BAAU;4BAChD,QAAQ,YAAY,CAAC,MAAM;;uBADlC;;;;;gBAIf;gBAEA,IAAI,QAAQ,aAAa,EAAE,QAAQ;oBACjC,OAAO,IAAI,eACT,6LAAC,oIAAA,CAAA,QAAK;wBAAqB,SAAQ;wBAAY,WAAU;;4BAAU;4BAChD,QAAQ,aAAa,CAAC,MAAM;;uBADpC;;;;;gBAIf;gBAEA,IAAI,QAAQ,SAAS,EAAE;oBACrB,OAAO,IAAI,eACT,6LAAC,oIAAA,CAAA,QAAK;wBAAiB,SAAQ;wBAAY,WAAU;;4BAAU;4BACrD,QAAQ,SAAS,CAAC,GAAG;4BAAC;4BAAK,QAAQ,SAAS,CAAC,GAAG;;uBAD/C;;;;;gBAIf;YACF;YAEA,+BAA+B;YAC/B,IAAI,sBAAsB,QAAQ,eAAe,EAAE;gBACjD,OAAO,IAAI,eACT,6LAAC,oIAAA,CAAA,QAAK;oBAAgB,SAAQ;oBAAY,WAAU;8BAAU;mBAAnD;;;;;YAIf;YAEA,OAAO;QACT;wDAAG;QAAC;QAAS;QAAuB;KAAmB;IAEvD,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,MAAM,SAAS,OAAO,MAAM,CAAC;QAC7B,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;QAEhC,qBACE,6LAAC;YAAI,WAAU;sBACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;oBAEC,WAAU;8BAET;mBAHI;;;;;;;;;;IAQf;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;8BACtC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAK,WAAU;8CAAsB;;;;;;gCACrC,oBAAoB,mBACnB,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAChC;;;;;;;;;;;;sCAIP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,SAAS;8CACzC,cAAA,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU,CAAC,WAAW,CAAC;8CAEvB,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAKvB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yLAAA,CAAA,kBAAe;4BAAC,OAAO;;;;;;sCACxB,6LAAC,sLAAA,CAAA,eAAY;4BAAC,OAAO;;;;;;sCACrB,6LAAC,wLAAA,CAAA,iBAAc;4BAAC,OAAO;;;;;;sCACvB,6LAAC,wLAAA,CAAA,iBAAc;4BAAC,OAAO;;;;;;sCACvB,6LAAC,uLAAA,CAAA,gBAAa;4BAAC,OAAO;;;;;;;;;;;;gBAGvB;;;;;;;IAGP;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;oCAE7B,oBAAoB,mBACnB,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAChC;4CAAkB;;;;;;;;;;;;;0CAIzB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAIhB,oBAAoB,MAAM,GAAG,mBAC5B,6LAAC;wBAAI,WAAU;kCAA6B;;;;;;;;;;;;0BAIhD,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;oBACpB,6BACC;;0CACE,6LAAC,uLAAA,CAAA,gBAAa;;;;;0CACd,6LAAC,wIAAA,CAAA,YAAS;;;;;;;kCAId,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yLAAA,CAAA,kBAAe;;;;;0CAChB,6LAAC,sLAAA,CAAA,eAAY;;;;;0CACb,6LAAC,wLAAA,CAAA,iBAAc;;;;;0CACf,6LAAC,wLAAA,CAAA,iBAAc;;;;;0CACf,6LAAC,uLAAA,CAAA,gBAAa;;;;;;;;;;;oBAGf;kCAED,6LAAC,wIAAA,CAAA,YAAS;;;;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAInC,6LAAC;gCAAI,WAAU;;oCACZ,mCACC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,SAAS;kDAAc;;;;;;kDAIjD,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,CAAC,WAAW,CAAC;wCACvB,WAAU;;0DAEV,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C;GAhRa;;QASK,2LAAA,CAAA,sBAAmB;QAEjC,2LAAA,CAAA,6BAA0B;QAE1B,2LAAA,CAAA,wBAAqB;QACe,2LAAA,CAAA,gCAA6B;;;KAdxD;uCAkRE", "debugId": null}}]}