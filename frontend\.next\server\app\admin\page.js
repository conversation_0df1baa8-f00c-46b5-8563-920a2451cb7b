(()=>{var e={};e.id=3698,e.ids=[757,3698],e.modules={1132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7112:(e,s,t)=>{Promise.resolve().then(t.bind(t,46564))},9587:(e,s,t)=>{Promise.resolve().then(t.bind(t,22538))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11012:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]])},11997:e=>{"use strict";e.exports=require("punycode")},14583:(e,s,t)=>{"use strict";t.d(s,{$o:()=>g,Eb:()=>u,Iu:()=>m,WA:()=>p,cU:()=>h,dK:()=>o,n$:()=>x});var a=t(60687),r=t(43967),i=t(74158),l=t(69795),n=t(43210),c=t(29523),d=t(22482);let o=n.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{className:(0,d.cn)("flex justify-center",e),ref:t,...s}));o.displayName="Pagination";let m=n.forwardRef(({className:e,...s},t)=>(0,a.jsx)("ul",{className:(0,d.cn)("flex flex-row items-center gap-1",e),ref:t,...s}));m.displayName="PaginationContent";let h=n.forwardRef(({className:e,...s},t)=>(0,a.jsx)("li",{className:(0,d.cn)("",e),ref:t,...s}));h.displayName="PaginationItem";let x=n.forwardRef(({className:e,isActive:s,...t},r)=>(0,a.jsx)(c.$,{"aria-current":s?"page":void 0,className:(0,d.cn)("h-9 w-9",e),ref:r,size:"icon",variant:s?"outline":"ghost",...t}));x.displayName="PaginationLink";let u=n.forwardRef(({className:e,...s},t)=>(0,a.jsxs)(c.$,{className:(0,d.cn)("h-9 w-9 gap-1",e),ref:t,size:"icon",variant:"ghost",...s,children:[(0,a.jsx)(r.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Previous page"})]}));u.displayName="PaginationPrevious";let p=n.forwardRef(({className:e,...s},t)=>(0,a.jsxs)(c.$,{className:(0,d.cn)("h-9 w-9 gap-1",e),ref:t,size:"icon",variant:"ghost",...s,children:[(0,a.jsx)(i.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Next page"})]}));p.displayName="PaginationNext";let j=n.forwardRef(({className:e,...s},t)=>(0,a.jsxs)("span",{"aria-hidden":!0,className:(0,d.cn)("flex h-9 w-9 items-center justify-center",e),ref:t,...s,children:[(0,a.jsx)(l.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"More pages"})]}));function g({className:e,currentPage:s,onPageChange:t,totalPages:r}){let i=(()=>{let e=[];e.push(1);let t=Math.max(2,s-1),a=Math.min(r-1,s+1);t>2&&e.push("ellipsis1");for(let s=t;s<=a;s++)e.push(s);return a<r-1&&e.push("ellipsis2"),r>1&&e.push(r),e})();return r<=1?null:(0,a.jsx)(o,{className:e,children:(0,a.jsxs)(m,{children:[(0,a.jsx)(h,{children:(0,a.jsx)(u,{"aria-disabled":1===s?"true":void 0,"aria-label":"Go to previous page",disabled:1===s,onClick:()=>t(s-1)})}),i.map((e,r)=>"ellipsis1"===e||"ellipsis2"===e?(0,a.jsx)(h,{children:(0,a.jsx)(j,{})},`ellipsis-${r}`):(0,a.jsx)(h,{children:(0,a.jsx)(x,{"aria-label":`Go to page ${e}`,isActive:s===e,onClick:()=>t(e),children:e})},`page-${e}`)),(0,a.jsx)(h,{children:(0,a.jsx)(p,{"aria-disabled":s===r?"true":void 0,"aria-label":"Go to next page",disabled:s===r,onClick:()=>t(s+1)})})]})})}j.displayName="PaginationEllipsis"},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22538:(e,s,t)=>{"use strict";t.d(s,{Breadcrumb:()=>r,BreadcrumbItem:()=>i,BreadcrumbLink:()=>l,BreadcrumbList:()=>n,BreadcrumbPage:()=>c,BreadcrumbSeparator:()=>d});var a=t(12907);let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call Breadcrumb() from the server but Breadcrumb is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","Breadcrumb");(0,a.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbEllipsis() from the server but BreadcrumbEllipsis is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbEllipsis");let i=(0,a.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbItem() from the server but BreadcrumbItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbItem"),l=(0,a.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbLink() from the server but BreadcrumbLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbLink"),n=(0,a.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbList() from the server but BreadcrumbList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbList"),c=(0,a.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbPage() from the server but BreadcrumbPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbPage"),d=(0,a.registerClientReference)(function(){throw Error("Attempted to call BreadcrumbSeparator() from the server but BreadcrumbSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\components\\ui\\breadcrumb.tsx","BreadcrumbSeparator")},25758:(e,s,t)=>{"use strict";t.d(s,{Sk:()=>l,p9:()=>n});var a=t(43612),r=t(43210),i=t(3389);let l=(e,s,t={})=>{let{toast:l}=(0,i.dj)(),{cacheDuration:n=3e5,enableRetry:c=!0,errorMessage:d,retryAttempts:o=3,showErrorToast:m=!0,showSuccessToast:h=!1,successMessage:x,...u}=t,p=(0,a.I)({gcTime:2*n,queryFn:s,queryKey:e,retry:!!c&&o,retryDelay:e=>Math.min(1e3*2**e,3e4),staleTime:n,...u});return(0,r.useEffect)(()=>{h&&p.isSuccess&&p.data&&x&&l({description:x,title:"Success"})},[h,p.isSuccess,p.data,x,l]),(0,r.useEffect)(()=>{m&&p.isError&&l({description:d||(p.error instanceof Error?p.error.message:"An error occurred"),title:"Error",variant:"destructive"})},[m,p.isError,p.error,d,l]),{...p,forceRefresh:async()=>await p.refetch(),isStale:p.isStale||!1,lastUpdated:p.dataUpdatedAt||null}},n=(e,s,t={})=>{let{keepPreviousData:a=!0,page:r=1,pageSize:i=10,...n}=t,c=l([...e,"paginated",r,i],()=>s(r,i),{...n,...a?{placeholderData:e=>e}:{}}),d=c.data?.pagination;return{...c,currentPage:r,data:c.data?.data??[],goToPage:e=>{},hasNextPage:!!d&&d.hasNext,hasPrevPage:!!d&&d.hasPrevious,nextPage:()=>{d&&d.hasNext},pagination:d??{hasNext:!1,hasPrevious:!1,limit:i,page:1,total:0,totalPages:1},prevPage:()=>{d&&d.hasPrevious},totalPages:d?d.totalPages:1}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},43560:(e,s,t)=>{Promise.resolve().then(t.bind(t,1132))},46539:(e,s,t)=>{Promise.resolve().then(t.bind(t,70640))},46564:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>eb});var a=t(60687),r=t(58369),i=t(99196),l=t(34570),n=t(75699),c=t(41936),d=t(11516),o=t(26622),m=t(43210),h=t(29523),x=t(26373),u=t(89667),p=t(14583),j=t(40988),g=t(6211),f=t(3389),v=t(80757),N=t(22482);function y(){let[e,s]=(0,m.useState)([]),[t,r]=(0,m.useState)(!0),[i,l]=(0,m.useState)(""),[y,b]=(0,m.useState)(""),[A,S]=(0,m.useState)(""),[w,C]=(0,m.useState)(),[k,E]=(0,m.useState)(),[R,_]=(0,m.useState)(1),[z,P]=(0,m.useState)(0),{toast:I}=(0,f.dj)(),T=Math.ceil(z/10);(0,m.useCallback)(async()=>{r(!0);try{let e=await v.adminService.getAuditLogs({action:y,endDate:k,limit:10,page:R,search:i,startDate:w,userId:A});s(e.data),P(e.pagination.total)}catch(e){I({description:e.message??"Failed to load audit log data.",title:"Error fetching audit logs",variant:"destructive"})}finally{r(!1)}},[R,i,y,A,w,k,I,z]);let M=e=>{_(e)};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"Audit Log Viewer"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-2 top-1/2 size-4 -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(u.p,{className:"w-[250px] pl-8",onChange:e=>{l(e.target.value),_(1)},placeholder:"Search logs (user, action, details)...",value:i})]}),(0,a.jsx)(u.p,{className:"w-[200px]",onChange:e=>{b(e.target.value),_(1)},placeholder:"Filter by action (e.g., LOGIN)",value:y}),(0,a.jsx)(u.p,{className:"w-[200px]",onChange:e=>{S(e.target.value),_(1)},placeholder:"Filter by User ID",value:A}),(0,a.jsxs)(j.AM,{children:[(0,a.jsx)(j.Wv,{asChild:!0,children:(0,a.jsxs)(h.$,{className:(0,N.cn)("w-[280px] justify-start text-left font-normal",!w&&!k&&"text-muted-foreground"),variant:"outline",children:[(0,a.jsx)(o.A,{className:"mr-2 size-4"}),w?k?(0,a.jsxs)(a.Fragment,{children:[(0,n.GP)(w,"LLL dd, y")," -"," ",(0,n.GP)(k,"LLL dd, y")]}):(0,n.GP)(w,"LLL dd, y"):(0,a.jsx)("span",{children:"Pick a date range"})]})}),(0,a.jsx)(j.hl,{className:"flex w-auto p-0",children:(0,a.jsx)(x.V,{mode:"range",numberOfMonths:2,onSelect:e=>{C(e?.from),E(e?.to),_(1)},selected:{from:w,to:k}})})]}),(w??k??y??A??i)&&(0,a.jsx)(h.$,{onClick:()=>{l(""),b(""),S(""),C(void 0),E(void 0),_(1)},variant:"outline",children:"Clear Filters"})]})]}),t?(0,a.jsxs)("div",{className:"flex h-64 items-center justify-center",children:[(0,a.jsx)(d.A,{className:"size-8 animate-spin text-primary"}),(0,a.jsx)("span",{className:"ml-2 text-lg",children:"Loading audit logs..."})]}):(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(g.XI,{children:[(0,a.jsx)(g.A0,{children:(0,a.jsxs)(g.Hj,{children:[(0,a.jsx)(g.nd,{className:"w-[100px]",children:"ID"}),(0,a.jsx)(g.nd,{children:"Timestamp"}),(0,a.jsx)(g.nd,{children:"User Email"}),(0,a.jsx)(g.nd,{children:"Action"}),(0,a.jsx)(g.nd,{children:"Details"}),(0,a.jsx)(g.nd,{children:"IP Address"})]})}),(0,a.jsx)(g.BF,{children:0===e.length?(0,a.jsx)(g.Hj,{children:(0,a.jsx)(g.nA,{className:"h-24 text-center",colSpan:6,children:"No audit logs found."})}):e.map(e=>(0,a.jsxs)(g.Hj,{children:[(0,a.jsx)(g.nA,{className:"font-medium",children:e.id}),(0,a.jsx)(g.nA,{children:new Date(e.timestamp).toLocaleString()}),(0,a.jsx)(g.nA,{children:e.userId||"N/A"}),(0,a.jsx)(g.nA,{children:e.action}),(0,a.jsx)(g.nA,{className:"max-w-[300px] truncate",children:e.details}),(0,a.jsx)(g.nA,{children:"N/A"})," "]},e.id))})]})}),z>0&&(0,a.jsx)(p.dK,{children:(0,a.jsxs)(p.Iu,{children:[(0,a.jsx)(p.cU,{children:(0,a.jsx)(p.Eb,{className:1===R?"pointer-events-none opacity-50":"",onClick:e=>{e.preventDefault(),M(R-1)}})}),Array.from({length:T},(e,s)=>(0,a.jsx)(p.cU,{children:(0,a.jsx)(p.n$,{isActive:R===s+1,onClick:e=>{e.preventDefault(),M(s+1)},children:s+1})},s)),(0,a.jsx)(p.cU,{children:(0,a.jsx)(p.WA,{className:R===T?"pointer-events-none opacity-50":"",onClick:e=>{e.preventDefault(),M(R+1)}})})]})})]})}var b=t(5600),A=t(8760),S=t(14975);let w=(0,t(82614).A)("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]);var C=t(85814),k=t.n(C),E=t(91821),R=t(44493),_=t(85763),z=t(81801),P=t(77368),I=t(57207),T=t(53597),M=t(15036),D=t(96834),U=t(16398),$=t(59514);function L(){let[e,s]=(0,m.useState)({entries:[],size:0}),[t,r]=(0,m.useState)({}),[i,l]=(0,m.useState)(!1),n=()=>{s($.Qb.getStats()),r({})},c=async()=>{l(!0),n(),setTimeout(()=>l(!1),500)},d=e=>e<0?"Expired":e<1e3?`${Math.round(e)}ms`:e<6e4?`${Math.round(e/1e3)}s`:`${Math.round(e/6e4)}m`,o=e=>e.includes("health")?"Health":e.includes("performance")?"Performance":e.includes("errors")?"Errors":"Other",x=e=>e.includes("health")?"bg-green-100 text-green-800":e.includes("performance")?"bg-blue-100 text-blue-800":e.includes("errors")?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800",u=e.entries.filter(e=>e.key.startsWith("admin:"));return(0,a.jsxs)(R.Zp,{className:"shadow-md",children:[(0,a.jsx)(R.aR,{className:"p-5",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(R.ZB,{className:"flex items-center gap-2 text-xl font-semibold text-primary",children:[(0,a.jsx)(z.A,{className:"size-5"}),"Cache Status"]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-muted-foreground",children:"Request cache performance and timing"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(h.$,{className:"flex items-center gap-2",disabled:i,onClick:c,size:"sm",variant:"outline",children:[(0,a.jsx)(P.A,{className:`size-4 ${i?"animate-spin":""}`}),"Refresh"]}),(0,a.jsxs)(h.$,{className:"flex items-center gap-2 text-red-600 hover:text-red-700",onClick:()=>{v.aN.clearAll(),n()},size:"sm",variant:"outline",children:[(0,a.jsx)(I.A,{className:"size-4"}),"Clear Cache"]}),(0,a.jsxs)(h.$,{className:"flex items-center gap-2 text-orange-600 hover:text-orange-700",onClick:()=>{v.aN.clearAll(),n()},size:"sm",variant:"outline",children:[(0,a.jsx)(T.A,{className:"size-4"}),"Reset Breakers"]})]})]})}),(0,a.jsx)(R.Wu,{className:"p-5",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 md:grid-cols-4",children:[(0,a.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-primary",children:e.size}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Total Entries"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:u.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Admin Entries"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:d($.xR.HEALTH_STATUS)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Health Cache"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-gray-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:d($.xR.PERFORMANCE_METRICS)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Performance Cache"})]})]}),u.length>0?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold uppercase tracking-wide text-muted-foreground",children:"Active Cache Entries"}),u.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-gray-50 p-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(D.E,{className:x(e.key),children:o(e.key)}),(0,a.jsx)("span",{className:"font-mono text-sm text-gray-600",children:e.key.replace("admin:","")})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 text-muted-foreground",children:[(0,a.jsx)(M.A,{className:"size-3"}),"Age: ",d(e.age)]}),(0,a.jsx)("div",{className:`font-medium ${e.expiresIn>0?"text-green-600":"text-red-600"}`,children:e.expiresIn>0?`Expires in ${d(e.expiresIn)}`:"Expired"})]})]},s))]}):(0,a.jsxs)("div",{className:"py-8 text-center text-muted-foreground",children:[(0,a.jsx)(z.A,{className:"mx-auto mb-3 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No cache entries found"}),(0,a.jsx)("p",{className:"text-sm",children:"Cache entries will appear here after API calls"})]}),(0,a.jsxs)("div",{className:"mt-6 rounded-lg bg-orange-50 p-4",children:[(0,a.jsxs)("h4",{className:"mb-3 flex items-center gap-2 font-semibold text-orange-900",children:[(0,a.jsx)(T.A,{className:"size-4"}),"Circuit Breaker Status"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4",children:Object.entries(t).map(([e,s])=>(0,a.jsxs)("div",{className:`rounded-lg border p-3 ${s.state===U.vz.CLOSED?"border-green-200 bg-green-50":s.state===U.vz.HALF_OPEN?"border-yellow-200 bg-yellow-50":"border-red-200 bg-red-50"}`,children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium capitalize",children:e}),(0,a.jsxs)(D.E,{className:s.state===U.vz.CLOSED?"bg-green-100 text-green-800":s.state===U.vz.HALF_OPEN?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800",children:[s.state===U.vz.CLOSED&&(0,a.jsx)(T.A,{className:"mr-1 size-3"}),s.state===U.vz.OPEN&&(0,a.jsx)(S.A,{className:"mr-1 size-3"}),s.state===U.vz.HALF_OPEN&&(0,a.jsx)(P.A,{className:"mr-1 size-3"}),s.state]})]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,a.jsxs)("div",{children:["Failures: ",s.failureCount]}),s.timeUntilRetry&&(0,a.jsxs)("div",{children:["Retry in: ",d(s.timeUntilRetry)]})]})]},e))})]}),(0,a.jsxs)("div",{className:"mt-6 rounded-lg bg-blue-50 p-4",children:[(0,a.jsx)("h4",{className:"mb-2 font-semibold text-blue-900",children:"Cache Configuration"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Health Status:"}),(0,a.jsx)("span",{className:"ml-2 text-blue-600",children:d($.xR.HEALTH_STATUS)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Performance:"}),(0,a.jsx)("span",{className:"ml-2 text-blue-600",children:d($.xR.PERFORMANCE_METRICS)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Error Logs:"}),(0,a.jsx)("span",{className:"ml-2 text-blue-600",children:d($.xR.ERROR_LOGS)})]})]})]})]})})]})}var B=t(97025),O=t(90586),F=t(95668),q=t(68752),W=t(72963),V=t(85726);function G({className:e,message:s,onRetry:t}){return(0,a.jsxs)(E.Fc,{className:(0,N.cn)("my-4",e),variant:"destructive",children:[(0,a.jsx)(W.A,{className:"size-4"}),(0,a.jsx)(E.XL,{children:"Error"}),(0,a.jsx)(E.TN,{children:(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:s}),t&&(0,a.jsxs)(h.$,{className:"flex items-center",onClick:t,size:"sm",variant:"outline",children:[(0,a.jsx)(d.A,{className:"mr-2 size-4"}),"Retry"]})]})})]})}var H=t(42692),Z=t(15079),K=t(25758);t(26461);var J=t(89513);function X(){let[e,s]=(0,m.useState)(),[t,r]=(0,m.useState)(!1),[l,n]=(0,m.useState)(null),{currentPage:c,data:d,error:o,hasNextPage:h,hasPrevPage:x,isLoading:u,nextPage:p,prevPage:j,refetch:g,totalPages:f}=(0,K.p9)(["adminErrors",e],(s,t)=>v.adminService.getRecentErrors(s,t,e),{pageSize:10}),N=async()=>{r(!0),n(null),await g(),r(!1)},y=async()=>{r(!0),n(null);try{let e=(0,J.Q)();await e.refreshNow()?await g():n("Failed to refresh authentication. Please sign in again.")}catch{n("Authentication refresh failed. Please sign in again.")}finally{r(!1)}},b=e=>"ERROR"===e?(0,a.jsx)(B.A,{className:"size-4 text-red-500"}):"WARNING"===e?(0,a.jsx)(S.A,{className:"size-4 text-yellow-500"}):(0,a.jsx)(i.A,{className:"size-4 text-blue-500"}),A=e=>"ERROR"===e?(0,a.jsx)(D.E,{className:"border-red-500/30 bg-red-500/20 text-red-700",variant:"outline",children:"Error"}):"WARNING"===e?(0,a.jsx)(D.E,{className:"border-yellow-500/30 bg-yellow-500/20 text-yellow-700",variant:"outline",children:"Warning"}):(0,a.jsx)(D.E,{className:"border-blue-500/30 bg-blue-500/20 text-blue-700",variant:"outline",children:"Info"});return(0,a.jsx)(F.A,{children:(0,a.jsxs)(R.Zp,{className:"shadow-md",children:[(0,a.jsxs)(R.aR,{className:"p-5 pb-2",children:[(0,a.jsx)(R.ZB,{className:"text-xl font-semibold text-primary",children:"Recent Errors & Warnings"}),(0,a.jsx)(R.BT,{children:"Latest system errors and warnings"})]}),(0,a.jsx)("div",{className:"px-5 pb-2",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(O.A,{className:"size-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Filter by level:"}),(0,a.jsxs)(Z.l6,{onValueChange:e=>{s("all"===e?void 0:e)},value:e??"all",children:[(0,a.jsx)(Z.bq,{className:"w-[140px]",children:(0,a.jsx)(Z.yv,{placeholder:"All levels"})}),(0,a.jsxs)(Z.gC,{children:[(0,a.jsx)(Z.eb,{value:"all",children:"All levels"}),(0,a.jsx)(Z.eb,{value:"ERROR",children:"Errors only"}),(0,a.jsx)(Z.eb,{value:"WARNING",children:"Warnings only"}),(0,a.jsx)(Z.eb,{value:"INFO",children:"Info only"})]})]})]})}),(0,a.jsx)(R.Wu,{className:"p-5",children:l?(0,a.jsx)("div",{className:"rounded-md border border-red-200 bg-red-50 p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(B.A,{className:"size-5 text-red-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-red-800",children:"Authentication Error"}),(0,a.jsx)("p",{className:"text-sm text-red-700",children:l}),(0,a.jsxs)("div",{className:"mt-3 flex space-x-2",children:[(0,a.jsx)(q.r,{actionType:"primary",isLoading:t,loadingText:"Refreshing...",onClick:y,size:"sm",children:"Refresh Authentication"}),(0,a.jsx)(q.r,{actionType:"tertiary",onClick:()=>{globalThis.location.reload()},size:"sm",children:"Refresh Page"})]})]})]})}):u||t?(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(V.E,{className:"h-6 w-full"}),(0,a.jsx)(V.E,{className:"h-6 w-full"}),(0,a.jsx)(V.E,{className:"h-6 w-full"}),(0,a.jsx)(V.E,{className:"h-6 w-full"}),(0,a.jsx)(V.E,{className:"h-6 w-full"})]}):o?(0,a.jsx)(G,{message:o.message,onRetry:g}):d&&d.length>0?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(H.F,{className:"h-[300px] pr-4",children:(0,a.jsx)("div",{className:"space-y-3",children:d.map(e=>(0,a.jsxs)("div",{className:"rounded-md border p-3 transition-colors hover:bg-accent/50",children:[(0,a.jsxs)("div",{className:"mb-1 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[b(e.level),(0,a.jsx)("span",{className:"font-medium",children:e.message})]}),A(e.level)]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:new Date(e.timestamp).toLocaleString()}),e.source&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Source: ",e.source]})]}),e.details&&Object.keys(e.details).length>0&&(0,a.jsx)("div",{className:"mt-2 rounded bg-muted p-2 text-xs",children:(0,a.jsx)("pre",{className:"whitespace-pre-wrap",children:JSON.stringify(e.details,null,2)})})]},e.id))})}),f>1&&(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,a.jsx)(q.r,{actionType:"tertiary",disabled:!x||u||t,onClick:j,size:"sm",children:"Previous"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",c," of ",f]}),(0,a.jsx)(q.r,{actionType:"tertiary",disabled:!h||u||t,onClick:p,size:"sm",children:"Next"})]})]}):(0,a.jsxs)("div",{className:"p-8 text-center text-muted-foreground",children:[(0,a.jsx)(S.A,{className:"mx-auto mb-2 size-8 text-muted-foreground/50"}),(0,a.jsx)("p",{children:"No errors or warnings found for the selected filter."}),e&&(0,a.jsx)("p",{className:"mt-2 text-sm",children:"Try changing the filter to see more results."})]})}),(0,a.jsx)(R.wL,{className:"p-5",children:(0,a.jsx)(q.r,{actionType:"tertiary",className:"w-full",icon:(0,a.jsx)(P.A,{className:"size-4"}),isLoading:t||u,loadingText:"Refreshing...",onClick:N,size:"sm",children:"Refresh Logs"})})]})})}var Q=t(11012);function Y(){let[e,s]=(0,m.useState)(null),[t,r]=(0,m.useState)(!1),i=async()=>{r(!0);try{let e=v.aN.getStats(),t=[];s({cacheStats:{errorCacheKeys:t.map(e=>e.key||"unknown"),errorEntries:t.length,totalEntries:e.size},circuitBreakers:{},timestamp:new Date().toISOString()})}catch(e){console.error("Failed to get debug info:",e)}finally{r(!1)}},l=e=>{switch(e){case U.vz.CLOSED:return"bg-green-100 text-green-800";case U.vz.HALF_OPEN:return"bg-yellow-100 text-yellow-800";case U.vz.OPEN:return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return(0,a.jsxs)(R.Zp,{className:"shadow-md",children:[(0,a.jsx)(R.aR,{className:"p-5",children:(0,a.jsxs)(R.ZB,{className:"flex items-center gap-2 text-xl font-semibold text-primary",children:[(0,a.jsx)(Q.A,{className:"size-5"}),"Error Logs Debug"]})}),(0,a.jsx)(R.Wu,{className:"p-5",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)(h.$,{className:"flex items-center gap-2",disabled:t,onClick:i,children:[(0,a.jsx)(P.A,{className:`size-4 ${t?"animate-spin":""}`}),"Get Debug Info"]}),(0,a.jsxs)(h.$,{className:"flex items-center gap-2 text-orange-600 hover:text-orange-700",onClick:()=>{v.aN.clearAll(),console.log("\uD83D\uDD04 Circuit breakers reset"),i()},variant:"outline",children:[(0,a.jsx)(T.A,{className:"size-4"}),"Reset Circuit Breakers"]}),(0,a.jsxs)(h.$,{className:"flex items-center gap-2 text-blue-600 hover:text-blue-700",onClick:()=>{v.aN.clearAll(),console.log("\uD83D\uDD04 Error cache cleared"),i()},variant:"outline",children:[(0,a.jsx)(I.A,{className:"size-4"}),"Clear Error Cache"]}),(0,a.jsxs)(h.$,{className:"flex items-center gap-2 text-red-600 hover:text-red-700",onClick:()=>{v.aN.clearAll(),console.log("\uD83D\uDD04 All cache cleared"),i()},variant:"outline",children:[(0,a.jsx)(I.A,{className:"size-4"}),"Clear All Cache"]})]}),e&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"rounded-lg bg-gray-50 p-4",children:[(0,a.jsxs)("h4",{className:"mb-3 flex items-center gap-2 font-semibold",children:[(0,a.jsx)(T.A,{className:"size-4"}),"Circuit Breaker Status"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-4",children:Object.entries(e.circuitBreakers).map(([e,s])=>(0,a.jsxs)("div",{className:"rounded border bg-white p-3",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium capitalize",children:e}),(0,a.jsx)(D.E,{className:l(s.state),children:s.state})]}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[(0,a.jsxs)("div",{children:["Failures: ",s.failureCount]}),s.timeUntilRetry&&(0,a.jsxs)("div",{children:["Retry in:"," ",Math.round(s.timeUntilRetry/1e3),"s"]})]})]},e))})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-blue-50 p-4",children:[(0,a.jsx)("h4",{className:"mb-3 font-semibold text-blue-900",children:"Cache Status"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Total Entries:"}),(0,a.jsx)("span",{className:"ml-2 text-blue-600",children:e.cacheStats.totalEntries})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Error Entries:"}),(0,a.jsx)("span",{className:"ml-2 text-blue-600",children:e.cacheStats.errorEntries})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium text-blue-800",children:"Last Updated:"}),(0,a.jsx)("span",{className:"ml-2 text-blue-600",children:new Date(e.timestamp).toLocaleTimeString()})]})]}),e.cacheStats.errorCacheKeys.length>0&&(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("div",{className:"mb-2 font-medium text-blue-800",children:"Error Cache Keys:"}),(0,a.jsx)("div",{className:"space-y-1",children:e.cacheStats.errorCacheKeys.map((e,s)=>(0,a.jsx)("div",{className:"rounded border bg-white p-2 font-mono text-xs",children:e},s))})]})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-yellow-50 p-4",children:[(0,a.jsxs)("h4",{className:"mb-3 flex items-center gap-2 font-semibold text-yellow-900",children:[(0,a.jsx)(S.A,{className:"size-4"}),"Troubleshooting Tips"]}),(0,a.jsxs)("ul",{className:"space-y-1 text-sm text-yellow-800",children:[(0,a.jsx)("li",{children:"• If circuit breakers are OPEN (red), reset them and try again"}),(0,a.jsx)("li",{children:"• If error cache is stale, clear it to force fresh data"}),(0,a.jsx)("li",{children:"• Check browser console for network errors"}),(0,a.jsx)("li",{children:"• Verify backend server is running and accessible"}),(0,a.jsx)("li",{children:"• Check if rate limiting is affecting requests"})]})]})]}),!e&&(0,a.jsxs)("div",{className:"py-8 text-center text-muted-foreground",children:[(0,a.jsx)(Q.A,{className:"mx-auto mb-3 size-12 opacity-50"}),(0,a.jsx)("p",{children:'Click "Get Debug Info" to see error logs debugging information'})]})]})})]})}function ee(){return(0,a.jsxs)(R.Zp,{className:"border-none shadow-none",children:[(0,a.jsxs)(R.aR,{className:"px-0 pt-0",children:[(0,a.jsx)(R.ZB,{className:"text-2xl font-bold",children:"Supabase Diagnostics"}),(0,a.jsx)(R.BT,{children:"Monitor and troubleshoot your Supabase database connection"})]}),(0,a.jsxs)(R.Wu,{className:"w-full max-w-full overflow-hidden px-0",children:[(0,a.jsxs)(E.Fc,{className:"mb-6",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),(0,a.jsx)(E.XL,{children:"Enhanced Reliability Dashboard Available"}),(0,a.jsxs)(E.TN,{children:["System health and performance monitoring has been moved to the new Reliability Dashboard with real-time updates, advanced metrics, and comprehensive monitoring capabilities.",(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsx)(h.$,{asChild:!0,variant:"outline",size:"sm",children:(0,a.jsxs)(k(),{href:"/reliability",className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),"Go to Reliability Dashboard",(0,a.jsx)(A.A,{className:"h-4 w-4"})]})})})]})]}),(0,a.jsxs)(_.tU,{className:"w-full max-w-full overflow-hidden",defaultValue:"errors",children:[(0,a.jsxs)(_.j7,{className:"grid w-full grid-cols-2",children:[(0,a.jsxs)(_.Xi,{className:"flex items-center",value:"errors",children:[(0,a.jsx)(S.A,{className:"mr-2 size-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Error Logs"})]}),(0,a.jsxs)(_.Xi,{className:"flex items-center",value:"cache",children:[(0,a.jsx)(w,{className:"mr-2 size-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"Cache Status"})]})]}),(0,a.jsx)(_.av,{className:"mt-4",value:"errors",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(X,{}),(0,a.jsx)(Y,{})]})}),(0,a.jsx)(_.av,{className:"mt-4",value:"cache",children:(0,a.jsx)(L,{})})]})]})]})}var es=t(3662),et=t(69795),ea=t(35137),er=t(27805),ei=t(54052),el=t(71032),en=t(48206),ec=t(76311),ed=t(55925),eo=t(32584),em=t(56896),eh=t(63503),ex=t(21342),eu=t(58595),ep=t(80013),ej=t(19599);let eg=e=>{let s=e.fullName??e.name??"Unknown",t=e.employeeId?` (ID: ${e.employeeId})`:"",a=e.position?` - ${e.position}`:"";return`${s}${t}${a}`};function ef({allowClear:e=!0,className:s,disabled:t=!1,error:r,label:i,onValueChange:l,placeholder:n="Select employee...",required:c=!1,value:d}){let{data:o=[],error:m,isLoading:h}=(0,ej.nR)(),x=o.find(e=>e.id===d);return h?(0,a.jsxs)("div",{className:(0,N.cn)("space-y-2",s),children:[i&&(0,a.jsxs)(ep.J,{className:"text-sm font-medium",children:[i,c&&(0,a.jsx)("span",{className:"ml-1 text-destructive",children:"*"})]}),(0,a.jsx)(Z.l6,{disabled:!0,children:(0,a.jsx)(Z.bq,{children:(0,a.jsx)(Z.yv,{placeholder:"Loading employees..."})})})]}):m?(0,a.jsxs)("div",{className:(0,N.cn)("space-y-2",s),children:[i&&(0,a.jsxs)(ep.J,{className:"text-sm font-medium",children:[i,c&&(0,a.jsx)("span",{className:"ml-1 text-destructive",children:"*"})]}),(0,a.jsx)(Z.l6,{disabled:!0,children:(0,a.jsx)(Z.bq,{children:(0,a.jsx)(Z.yv,{placeholder:"Error loading employees"})})}),(0,a.jsx)("p",{className:"text-sm text-destructive",children:"Failed to load employees"})]}):(0,a.jsxs)("div",{className:(0,N.cn)("space-y-2",s),children:[i&&(0,a.jsxs)(ep.J,{className:"text-sm font-medium",children:[i,c&&(0,a.jsx)("span",{className:"ml-1 text-destructive",children:"*"})]}),(0,a.jsxs)(Z.l6,{disabled:t,onValueChange:e=>{if("clear"===e)return void l(null);let s=Number.parseInt(e,10);l(Number.isNaN(s)?null:s)},value:d?.toString()??"",children:[(0,a.jsx)(Z.bq,{className:(0,N.cn)(r&&"border-destructive focus:border-destructive"),children:(0,a.jsx)(Z.yv,{placeholder:n,children:x&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eu.A,{className:"size-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"truncate",children:eg(x)})]})})}),(0,a.jsxs)(Z.gC,{children:[e&&d&&(0,a.jsx)(Z.eb,{className:"text-muted-foreground",value:"clear",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xs",children:"\xd7"}),(0,a.jsx)("span",{children:"Clear selection"})]})}),o.map(e=>(0,a.jsx)(Z.eb,{value:e.id.toString(),children:(0,a.jsxs)("div",{className:"flex w-full items-center space-x-2",children:[(0,a.jsx)(eu.A,{className:"size-4 shrink-0 text-muted-foreground"}),(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsx)("div",{className:"truncate font-medium",children:e.fullName??e.name??"Unknown"}),(0,a.jsxs)("div",{className:"truncate text-xs text-muted-foreground",children:[e.employeeId&&`ID: ${e.employeeId}`,e.position&&` • ${e.position}`,e.department&&` • ${e.department}`]})]})]})},e.id))]})]}),r&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:r})]})}var ev=t(95009),eN=t(3940);function ey(){let[e,s]=(0,m.useState)([]),[t,r]=(0,m.useState)(!0),[i,l]=(0,m.useState)(!1),[n,c]=(0,m.useState)(null),[o,x]=(0,m.useState)({email:"",emailVerified:!1,employee_id:"",full_name:"",isActive:!0,phone:"",role:"USER"}),[p,j]=(0,m.useState)(0),[g]=(0,m.useState)(10),{showFormError:f,showFormSuccess:N}=(0,eN.t6)(),[y,b]=(0,m.useState)(!1),[A,S]=(0,m.useState)(null),[w,C]=(0,m.useState)(!1),[k,E]=(0,m.useState)(null),[z,M]=(0,m.useState)(null),[U,$]=(0,m.useState)(null),[L,O]=(0,m.useState)(!1),F=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),q=e=>e?new Date(e).toLocaleDateString("en-US",{day:"numeric",month:"short",year:"numeric"}):"Never",W=e=>{if(!e)return"Never";let s=new Date,t=new Date(e),a=Math.floor((s.getTime()-t.getTime())/1e3);return a<60?"Just now":a<3600?`${Math.floor(a/60)}m ago`:a<86400?`${Math.floor(a/3600)}h ago`:a<2592e3?`${Math.floor(a/86400)}d ago`:q(e)},V=(e,s)=>{if(s?.trim()){let e=s.trim().split(" ");return e.length>1?`${e[0]?.[0]||""}${e.at(-1)?.[0]||""}`.toUpperCase():s.slice(0,2).toUpperCase()}return e.slice(0,2).toUpperCase()},G=(0,m.useCallback)(async()=>{r(!0),$(null);try{let e=await v.adminService.getAllUsers({limit:100,page:1,search:""});s(e.data||[]);let t=e.pagination||{limit:100,total:0};j(t.total)}catch(e){if(e?.status===401||e?.status===500||e?.code==="NO_TOKEN"||e?.code==="INVALID_TOKEN"||e?.message?.includes("Authentication failed")||e?.message?.includes("Failed to fetch users"))try{let e=(0,J.Q)(),s=await e.getSessionInfo();s.isValid?$("Server error occurred. This might be a temporary issue. Try refreshing."):s.isExpired?$('Your session has expired. Click "Refresh Authentication" to renew your session.'):$("Authentication failed. Please refresh the page to sign in again.")}catch{$("Authentication system error. Please refresh the page to sign in again.")}else f(e,{errorDescription:e.message||"Failed to load user data.",errorTitle:"Error fetching users"})}finally{r(!1)}},[f]),H=async()=>{O(!0),$(null);try{let e=(0,J.Q)();await e.refreshNow()?await G():$("Failed to refresh authentication. Please sign in again.")}catch{$("Authentication refresh failed. Please sign in again.")}finally{O(!1)}},K=async()=>{if(!F(o.email))return void f(Error("Please enter a valid email address."),{errorTitle:"Validation Error"});r(!0);try{let e=await v.adminService.createUser(o);N({successDescription:`User ${e.email} has been added.`,successTitle:"User created"}),l(!1),x({email:"",emailVerified:!1,employee_id:"",full_name:"",isActive:!0,phone:"",role:"USER"}),G()}catch(e){f(e.message||"Failed to create user.",{errorTitle:"Error creating user"})}finally{r(!1)}},X=async()=>{if(n){if(!F(n.email||""))return void f("Please enter a valid email address.",{errorTitle:"Validation Error"});r(!0);try{let e=await v.adminService.updateUser(n.id,{email:n.email,emailVerified:!!n.email_confirmed_at,isActive:n.isActive,role:n.role});N({successDescription:`User ${e.email} has been updated.`,successTitle:"User updated"}),l(!1),c(null),G()}catch(e){f(e.message||"Failed to update user.",{errorTitle:"Error updating user"})}finally{r(!1)}}},Q=async()=>{if(A){r(!0);try{await v.adminService.deleteUser(A),N({successDescription:"User has been successfully deleted.",successTitle:"User deleted"}),G()}catch(e){f(e.message||"Failed to delete user.",{errorTitle:"Error deleting user"})}finally{r(!1),b(!1),S(null)}}},Y=async()=>{if(k&&null!==z){r(!0);try{let e=await v.adminService.toggleUserActivation(k,!z);N({successDescription:`User ${e.email} is now ${e.isActive?"active":"inactive"}.`,successTitle:"User status updated"}),G()}catch(e){f(e.message||"Failed to toggle user activation.",{errorTitle:"Error updating status"})}finally{r(!1),C(!1),E(null),M(null)}}},ee=e=>{switch(e){case"ADMIN":return"bg-purple-500 hover:bg-purple-600 text-white";case"MANAGER":return"bg-blue-500 hover:bg-blue-600 text-white";case"READONLY":return"bg-yellow-500 hover:bg-yellow-600 text-white";case"SUPER_ADMIN":return"bg-red-500 hover:bg-red-600 text-white";case"USER":return"bg-green-500 hover:bg-green-600 text-white";default:return"bg-gray-500 hover:bg-gray-600 text-white"}},eu=[{accessorKey:"email",cell:({row:e})=>{let s=e.original;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)(eo.eu,{className:"size-10",children:[(0,a.jsx)(eo.BK,{alt:s.email,src:""}),(0,a.jsx)(eo.q5,{className:"text-sm",children:V(s.email,s.full_name)})]}),(0,a.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"truncate text-sm font-medium",children:s.full_name||s.email.split("@")[0]}),s.email_confirmed_at&&(0,a.jsx)(T.A,{className:"size-3 text-green-500"})]}),(0,a.jsx)("p",{className:"truncate text-xs text-muted-foreground",children:s.email}),s.employee_id&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["ID: ",s.employee_id]})]})]})},header:"User"},{accessorKey:"role",cell:({row:e})=>{let s=e.original;return(0,a.jsx)(D.E,{className:ee(s.role),children:(s.role||"USER").replace("_"," ")})},header:"Role"},{accessorKey:"isActive",cell:({row:e})=>{let s=e.original;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(D.E,{variant:s.isActive?"default":"destructive",children:s.isActive?"Active":"Inactive"}),s.email_confirmed_at?(0,a.jsx)(es.A,{className:"size-4 text-green-500"}):(0,a.jsx)(B.A,{className:"size-4 text-red-500"})]})},header:"Status"},{accessorKey:"last_sign_in_at",cell:({row:e})=>{let s=e.original;return(0,a.jsx)("div",{className:"text-sm",children:W(s.last_sign_in_at)})},header:"Last Activity"},{accessorKey:"created_at",cell:({row:e})=>{let s=e.original;return(0,a.jsx)("div",{className:"text-sm",children:q(s.created_at)})},header:"Joined"},{cell:({row:e})=>{let s=e.original;return(0,a.jsxs)(ex.rI,{children:[(0,a.jsx)(ex.ty,{asChild:!0,children:(0,a.jsxs)(h.$,{className:"size-8 p-0",variant:"ghost",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,a.jsx)(et.A,{className:"size-4"})]})}),(0,a.jsxs)(ex.SQ,{align:"end",children:[(0,a.jsx)(ex.lp,{children:"Actions"}),(0,a.jsxs)(ex._2,{onClick:()=>{c(s),l(!0)},children:[(0,a.jsx)(ea.A,{className:"mr-2 size-4"}),"Edit user"]}),(0,a.jsx)(ex.mB,{}),(0,a.jsxs)(ex._2,{onClick:()=>{E(s.id),M(s.isActive),C(!0)},children:[(0,a.jsx)(er.A,{className:"mr-2 size-4"}),s.isActive?"Deactivate":"Activate"]}),(0,a.jsxs)(ex._2,{className:"text-red-600 focus:text-red-600",onClick:()=>{S(s.id),b(!0)},children:[(0,a.jsx)(I.A,{className:"mr-2 size-4"}),"Delete user"]})]})]})},header:"Actions",id:"actions"}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-3xl font-bold tracking-tight",children:"User Management"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage user accounts, roles, and permissions"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(h.$,{onClick:G,size:"sm",variant:"outline",children:[(0,a.jsx)(P.A,{className:"mr-2 size-4"}),"Refresh"]}),(0,a.jsxs)(eh.lG,{onOpenChange:l,open:i,children:[(0,a.jsx)(eh.zM,{asChild:!0,children:(0,a.jsxs)(h.$,{onClick:()=>{c(null),x({email:"",emailVerified:!1,employee_id:"",full_name:"",isActive:!0,phone:"",role:"USER"})},children:[(0,a.jsx)(ei.A,{className:"mr-2 size-4"})," Add User"]})}),(0,a.jsxs)(eh.Cf,{className:"sm:max-w-[600px]",children:[(0,a.jsxs)(eh.c7,{children:[(0,a.jsx)(eh.L3,{className:"flex items-center gap-2",children:n?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ea.A,{className:"size-5"}),"Edit User Profile"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(el.A,{className:"size-5"}),"Add New User"]})}),(0,a.jsx)(eh.rr,{children:n?"Update user information and permissions.":"Create a new user account with role and permissions."})]}),(0,a.jsxs)(_.tU,{className:"w-full",defaultValue:"basic",children:[(0,a.jsxs)(_.j7,{className:"grid w-full grid-cols-2",children:[(0,a.jsx)(_.Xi,{value:"basic",children:"Basic Info"}),(0,a.jsx)(_.Xi,{value:"advanced",children:"Advanced"})]}),(0,a.jsx)(_.av,{className:"mt-4 space-y-4",value:"basic",children:(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(ep.J,{className:"text-right",htmlFor:"full_name",children:"Full Name"}),(0,a.jsx)(u.p,{className:"col-span-3",id:"full_name",onChange:e=>n?c({...n,full_name:e.target.value}):x({...o,full_name:e.target.value}),placeholder:"Enter full name",value:n?n.full_name||"":o.full_name})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(ep.J,{className:"text-right",htmlFor:"email",children:"Email *"}),(0,a.jsx)(u.p,{className:"col-span-3",id:"email",onChange:e=>n?c({...n,email:e.target.value}):x({...o,email:e.target.value}),placeholder:"<EMAIL>",type:"email",value:n?n.email:o.email})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(ep.J,{className:"text-right",htmlFor:"phone",children:"Phone"}),(0,a.jsx)(u.p,{className:"col-span-3",id:"phone",onChange:e=>n?c({...n,phone:e.target.value}):x({...o,phone:e.target.value}),placeholder:"+****************",type:"tel",value:n?n.phone||"":o.phone})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(ep.J,{className:"text-right",htmlFor:"role",children:"Role *"}),(0,a.jsxs)(Z.l6,{onValueChange:e=>n?c({...n,role:e}):x({...o,role:e}),value:n?n.role:o.role,children:[(0,a.jsx)(Z.bq,{className:"col-span-3",children:(0,a.jsx)(Z.yv,{placeholder:"Select a role"})}),(0,a.jsxs)(Z.gC,{children:[(0,a.jsx)(Z.eb,{value:"SUPER_ADMIN",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(T.A,{className:"size-4 text-red-500"}),"Super Admin"]})}),(0,a.jsx)(Z.eb,{value:"ADMIN",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(T.A,{className:"size-4 text-purple-500"}),"Admin"]})}),(0,a.jsx)(Z.eb,{value:"MANAGER",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(en.A,{className:"size-4 text-blue-500"}),"Manager"]})}),(0,a.jsx)(Z.eb,{value:"USER",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(en.A,{className:"size-4 text-green-500"}),"User"]})}),(0,a.jsx)(Z.eb,{value:"READONLY",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ec.A,{className:"size-4 text-yellow-500"}),"Read Only"]})})]})]})]})]})}),(0,a.jsx)(_.av,{className:"mt-4 space-y-4",value:"advanced",children:(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsx)("div",{className:"grid grid-cols-4 items-center gap-4",children:(0,a.jsx)("div",{className:"col-span-4",children:(0,a.jsx)(ef,{allowClear:!0,className:"w-full",label:"Link to Employee (Optional)",onValueChange:e=>n?c({...n,employee_id:e}):x({...o,employee_id:e?.toString()||""}),placeholder:"Select an employee to link this user account...",value:n?n.employee_id??null:o.employee_id?Number.parseInt(o.employee_id):null})})}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(ep.J,{className:"text-right",htmlFor:"isActive",children:"Account Status"}),(0,a.jsxs)("div",{className:"col-span-3 flex items-center space-x-2",children:[(0,a.jsx)(em.S,{checked:n?n.isActive:o.isActive,id:"isActive",onCheckedChange:e=>n?c({...n,isActive:!!e}):x({...o,isActive:!!e})}),(0,a.jsx)(ep.J,{className:"text-sm",htmlFor:"isActive",children:"Active account"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,a.jsx)(ep.J,{className:"text-right",htmlFor:"emailVerified",children:"Email Verification"}),(0,a.jsxs)("div",{className:"col-span-3 flex items-center space-x-2",children:[(0,a.jsx)(em.S,{checked:n?!!n.email_confirmed_at:o.emailVerified,id:"emailVerified",onCheckedChange:e=>n?c({...n,email_confirmed_at:e?new Date().toISOString():null}):x({...o,emailVerified:!!e})}),(0,a.jsx)(ep.J,{className:"text-sm",htmlFor:"emailVerified",children:"Email verified"})]})]})]})})]}),(0,a.jsx)(eh.Es,{children:(0,a.jsxs)(h.$,{disabled:t,onClick:n?X:K,type:"submit",children:[t&&(0,a.jsx)(d.A,{className:"mr-2 size-4 animate-spin"}),n?"Save changes":"Add User"]})})]})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)(R.Zp,{children:[(0,a.jsxs)(R.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(R.ZB,{className:"text-sm font-medium",children:"Total Users"}),(0,a.jsx)(en.A,{className:"size-4 text-muted-foreground"})]}),(0,a.jsxs)(R.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:p}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.filter(e=>e.isActive).length," active"]})]})]}),(0,a.jsxs)(R.Zp,{children:[(0,a.jsxs)(R.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(R.ZB,{className:"text-sm font-medium",children:"Active Users"}),(0,a.jsx)(er.A,{className:"size-4 text-muted-foreground"})]}),(0,a.jsxs)(R.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>e.isActive).length}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[Math.round(e.filter(e=>e.isActive).length/Math.max(e.length,1)*100),"% of total"]})]})]}),(0,a.jsxs)(R.Zp,{children:[(0,a.jsxs)(R.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(R.ZB,{className:"text-sm font-medium",children:"Verified Emails"}),(0,a.jsx)(T.A,{className:"size-4 text-muted-foreground"})]}),(0,a.jsxs)(R.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>e.email_confirmed_at).length}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[Math.round(e.filter(e=>e.email_confirmed_at).length/Math.max(e.length,1)*100),"% verified"]})]})]}),(0,a.jsxs)(R.Zp,{children:[(0,a.jsxs)(R.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(R.ZB,{className:"text-sm font-medium",children:"Admins"}),(0,a.jsx)(T.A,{className:"size-4 text-muted-foreground"})]}),(0,a.jsxs)(R.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>"ADMIN"===e.role||"SUPER_ADMIN"===e.role).length}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"System administrators"})]})]})]})]}),U?(0,a.jsx)("div",{className:"rounded-md border border-red-200 bg-red-50 p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(B.A,{className:"size-5 text-red-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-red-800",children:"Authentication Error"}),(0,a.jsx)("p",{className:"text-sm text-red-700",children:U}),(0,a.jsxs)("div",{className:"mt-3 flex space-x-2",children:[(0,a.jsx)(h.$,{disabled:L,onClick:H,size:"sm",children:L?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"mr-2 size-4 animate-spin"}),"Refreshing..."]}):"Refresh Authentication"}),(0,a.jsx)(h.$,{onClick:()=>{globalThis.location.reload()},size:"sm",variant:"outline",children:"Refresh Page"})]})]})]})}):t?(0,a.jsxs)("div",{className:"flex h-64 items-center justify-center",children:[(0,a.jsx)(d.A,{className:"size-8 animate-spin text-primary"}),(0,a.jsx)("span",{className:"ml-2 text-lg",children:"Loading users..."})]}):(0,a.jsx)(ev.b,{className:"w-full",columns:eu,data:e,emptyMessage:"No users found.",enableColumnVisibility:!0,enableGlobalFilter:!0,enableRowSelection:!1,pageSize:g,searchColumn:"email",searchPlaceholder:"Search users by email or role..."}),(0,a.jsx)(ed.Lt,{onOpenChange:C,open:w,children:(0,a.jsxs)(ed.EO,{children:[(0,a.jsxs)(ed.wd,{children:[(0,a.jsx)(ed.r7,{children:z?"Deactivate User":"Activate User"}),(0,a.jsxs)(ed.$v,{children:["Are you sure you want to"," ",z?"deactivate":"activate"," user"," ",(0,a.jsx)("span",{className:"font-bold",children:e.find(e=>e.id===k)?.email}),"?"]})]}),(0,a.jsxs)(ed.ck,{children:[(0,a.jsx)(ed.Zr,{children:"Cancel"}),(0,a.jsx)(ed.Rx,{onClick:Y,children:z?"Deactivate":"Activate"})]})]})}),(0,a.jsx)(ed.Lt,{onOpenChange:b,open:y,children:(0,a.jsxs)(ed.EO,{children:[(0,a.jsxs)(ed.wd,{children:[(0,a.jsx)(ed.r7,{children:"Delete User"}),(0,a.jsxs)(ed.$v,{children:["Are you sure you want to permanently delete user"," ",(0,a.jsx)("span",{className:"font-bold",children:e.find(e=>e.id===A)?.email}),"? This action cannot be undone."]})]}),(0,a.jsxs)(ed.ck,{children:[(0,a.jsx)(ed.Zr,{children:"Cancel"}),(0,a.jsx)(ed.Rx,{onClick:Q,children:"Delete"})]})]})})]})}function eb(){return(0,a.jsx)(l.ProtectedRoute,{allowedRoles:["ADMIN","SUPER_ADMIN"],children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"mb-6 flex items-center space-x-2",children:[(0,a.jsx)(r.A,{className:"size-8 text-primary"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-primary",children:"Admin Dashboard"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"System administration and diagnostics"})]})]}),(0,a.jsxs)(E.Fc,{children:[(0,a.jsx)(i.A,{className:"size-4"}),(0,a.jsx)(E.XL,{children:"Information"}),(0,a.jsx)(E.TN,{children:"This admin dashboard provides system diagnostics and monitoring tools. Access is restricted to ADMIN and SUPER_ADMIN roles."})]}),(0,a.jsxs)(_.tU,{className:"w-full",defaultValue:"system-diagnostics",children:[(0,a.jsxs)(_.j7,{className:"grid w-full grid-cols-3",children:[(0,a.jsx)(_.Xi,{value:"system-diagnostics",children:"System Diagnostics"}),(0,a.jsx)(_.Xi,{value:"user-management",children:"User Management"}),(0,a.jsx)(_.Xi,{value:"audit-logs",children:"Audit Logs"})]}),(0,a.jsx)(_.av,{className:"mt-6",value:"system-diagnostics",children:(0,a.jsx)(ee,{})}),(0,a.jsx)(_.av,{className:"mt-6",value:"user-management",children:(0,a.jsx)(ey,{})}),(0,a.jsx)(_.av,{className:"mt-6",value:"audit-logs",children:(0,a.jsx)(y,{})})]})]})})}},54052:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72975:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("ChevronsRight",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80757:(e,s,t)=>{"use strict";t.d(s,{aN:()=>h,adminService:()=>m});var a=t(77312),r=t(49603);let i={fromApi:e=>({action:e.action||"",details:e.details||"",id:e.id||"",timestamp:new Date(e.created_at||e.timestamp||new Date),userId:e.user_id||e.userId||e.auth_user_id||"",auth_user_id:e.auth_user_id||"",auth_user:e.auth_user||null}),toApi:e=>e};class l extends a.v{constructor(e,s){super(e,{cacheDuration:12e4,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...s}),this.endpoint="/admin/audit-logs",this.transformer=i}async getByAction(e,s){return(await this.getAll({...s,action:e})).data}async getByDateRange(e,s,t){let a={endDate:s.toISOString(),startDate:e.toISOString(),...t};return this.getAll(a)}async getByUserId(e,s){return(await this.getAll({...s,userId:e})).data}}let n={fromApi:e=>{let s=e.users?.[0]?.email||e.email||"",t=e.users?.[0]?.email_confirmed_at||e.email_confirmed_at||null;return{created_at:e.created_at||"",email:s,email_confirmed_at:t,employee_id:e.employee_id||null,full_name:e.full_name||e.name||"",id:e.id,isActive:e.is_active??!0,last_sign_in_at:e.last_sign_in_at||null,phone:e.phone||null,phone_confirmed_at:e.phone_confirmed_at||null,role:e.role||"USER",updated_at:e.updated_at||"",users:e.users}},toApi:e=>e};class c extends a.v{constructor(e,s){super(e,{cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...s}),this.endpoint="/admin/users",this.transformer=n}async getUsersByRole(e,s={}){return(await this.getAll({...s,role:e})).data}async toggleActivation(e,s){return this.executeWithInfrastructure(null,async()=>{let t=await this.apiClient.patch(`${this.endpoint}/${e}/toggle-activation`,{isActive:s});return this.cache.invalidatePattern(RegExp(`^${this.endpoint}:`)),this.cache.invalidate(`${this.endpoint}:getById:${e}`),this.transformer.fromApi?this.transformer.fromApi(t):t})}}let d={fromApi:e=>e,toApi:e=>e};class o extends a.v{get cacheUtils(){return{clearAll:()=>this.clearCache(),forceRefreshHealth:async()=>(this.cache.invalidate("admin:health"),this.getSystemHealthStatus()),forceRefreshPerformance:async()=>(this.cache.invalidate("admin:performance"),this.getPerformanceMetrics()),getStats:()=>this.cache.getStats(),invalidateAll:()=>this.cache.invalidatePattern(/^admin:/)}}constructor(e,s){let t=e||r.cl.getApiClient();super(t,{cacheDuration:3e5,circuitBreakerThreshold:5,enableMetrics:!0,retryAttempts:3,...s}),this.endpoint="/admin",this.transformer=d,this.auditService=new l(t),this.userService=new c(t)}async createAuditLog(e){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.post("/admin/audit-logs",e);return this.cache.invalidatePattern(/^admin:audit:/),s})}async createUser(e){return this.executeWithInfrastructure(null,async()=>{let s=await this.apiClient.post("/admin/users",e);return this.cache.invalidatePattern(/^admin:users:/),s})}async deleteUser(e){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete(`/admin/users/${e}`),this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate(`admin:user:${e}`)})}async getAllUsers(e={}){return this.userService.getAll(e)}async getAuditLogs(e={}){return this.auditService.getAll(e)}getMockHealthStatus(){return{services:{api:{responseTime:23,status:"healthy"},cache:{responseTime:12,status:"healthy"},database:{responseTime:45,status:"healthy"}},status:"healthy",timestamp:new Date().toISOString(),uptime:3600}}getMockPerformanceMetrics(){return{cpu:{cores:4,usage:100*Math.random()},errors:{rate:5*Math.random(),total:Math.floor(500*Math.random())},memory:{percentage:100*Math.random(),total:8e3,used:8e3*Math.random()},requests:{averageResponseTime:1e3*Math.random(),perSecond:100*Math.random(),total:Math.floor(1e4*Math.random())},timestamp:new Date().toISOString()}}getMockRecentErrors(){let e=[{details:{component:"database",errorType:"timeout"},id:"1",level:"ERROR",message:"Database connection timeout",requestId:"req-456",source:"database.service.ts",stack:"Error: Connection timeout\n    at Database.connect...",timestamp:new Date().toISOString(),userId:"user123"},{details:{component:"system",metric:"memory"},id:"2",level:"WARNING",message:"High memory usage detected",requestId:"req-789",source:"monitoring.service.ts",timestamp:new Date(Date.now()-3e5).toISOString()}];return{data:e,pagination:{hasNext:!1,hasPrevious:!1,limit:10,page:1,total:e.length,totalPages:Math.ceil(e.length/10)}}}async getPerformanceMetrics(){return this.executeWithInfrastructure("admin:performance",async()=>this.apiClient.get("/admin/performance"))}async getRecentErrors(e=1,s=10,t){let a=new URLSearchParams;a.append("page",e.toString()),a.append("limit",s.toString()),t&&a.append("level",t);let r=`/admin/logs/errors?${a.toString()}`,i=`admin:errors:${e}:${s}:${t||"all"}`;return this.executeWithInfrastructure(i,async()=>this.apiClient.get(r))}async getSystemHealthStatus(){return this.executeWithInfrastructure("admin:health",async()=>this.apiClient.get("/admin/health"))}async toggleUserActivation(e,s){return this.executeWithInfrastructure(null,async()=>{let t=await this.apiClient.patch(`/admin/users/${e}/toggle-activation`,{isActive:s});return this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate(`admin:user:${e}`),t})}async updateUser(e,s){return this.executeWithInfrastructure(null,async()=>{let t=await this.apiClient.put(`/admin/users/${e}`,s);return this.cache.invalidatePattern(/^admin:users:/),this.cache.invalidate(`admin:user:${e}`),t})}}let m=new o,h=m.cacheUtils},81630:e=>{"use strict";e.exports=require("http")},81801:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},83997:e=>{"use strict";e.exports=require("tty")},90586:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},91273:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(65239),r=t(48088),i=t(88170),l=t.n(i),n=t(30893),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\admin\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,34595)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\admin\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95668:(e,s,t)=>{"use strict";t.d(s,{A:()=>o});var a=t(60687),r=t(14975),i=t(77368),l=t(43210),n=t(91821),c=t(29523);class d extends l.Component{constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,s){this.setState({errorInfo:s}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",s.componentStack),this.props.onError&&this.props.onError(e,s)}render(){let{description:e="An unexpected error occurred.",resetLabel:s="Try Again",title:t="Something went wrong"}=this.props;return this.state.hasError?this.props.fallback?this.props.fallback:(0,a.jsxs)(n.Fc,{className:"my-4",variant:"destructive",children:[(0,a.jsx)(r.A,{className:"mr-2 size-4"}),(0,a.jsx)(n.XL,{className:"text-lg font-semibold",children:t}),(0,a.jsxs)(n.TN,{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-2",children:this.state.error?.message||e}),!1,(0,a.jsxs)(c.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,a.jsx)(i.A,{className:"mr-2 size-4"}),s]})]})]}):this.props.children}}let o=d},95688:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(82614).A)("ChevronsLeft",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},99111:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>i});var a=t(37413),r=t(22538);let i={description:"Administrative dashboard for WorkHub system",title:"Admin Dashboard - WorkHub"};function l({children:e}){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(r.Breadcrumb,{className:"mb-4",children:(0,a.jsxs)(r.BreadcrumbList,{children:[(0,a.jsx)(r.BreadcrumbItem,{children:(0,a.jsx)(r.BreadcrumbLink,{href:"/",children:"Home"})}),(0,a.jsx)(r.BreadcrumbSeparator,{}),(0,a.jsx)(r.BreadcrumbItem,{children:(0,a.jsx)(r.BreadcrumbPage,{children:"Admin"})})]})})}),(0,a.jsx)("div",{className:"flex-1",children:e})]})}}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4447,211,1658,8390,2670,4897,6362,7113,5941,7055,9599,5009,3089,3224],()=>t(91273));module.exports=a})();