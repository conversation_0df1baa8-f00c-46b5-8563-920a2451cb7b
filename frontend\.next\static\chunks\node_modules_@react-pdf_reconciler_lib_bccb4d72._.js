(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@react-pdf/reconciler/lib/reconciler-31.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>k)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$scheduler$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/scheduler/index.js [app-client] (ecmascript)");
;
;
function t(e) {
    return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
function r(e) {
    if (e.__esModule) return e;
    var n = e.default;
    if ("function" == typeof n) {
        var t = function e() {
            return this instanceof e ? Reflect.construct(n, arguments, this.constructor) : n.apply(this, arguments);
        };
        t.prototype = n.prototype;
    } else t = {};
    return Object.defineProperty(t, "__esModule", {
        value: !0
    }), Object.keys(e).forEach(function(n) {
        var r = Object.getOwnPropertyDescriptor(e, n);
        Object.defineProperty(t, n, r.get ? r : {
            enumerable: !0,
            get: function() {
                return e[n];
            }
        });
    }), t;
}
var l, a = {
    exports: {}
}, o = {
    exports: {}
}, u = r(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$scheduler$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__);
var i, s, c = {
    exports: {}
};
/**
 * @license React
 * react-reconciler.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : a.exports = function() {
    return i || (i = 1, n = c, "production" !== ("TURBOPACK compile-time value", "development") && (n.exports = function(n) {
        function t(e, n, t, r) {
            return new sa(e, n, t, r);
        }
        function r() {
            console.error("Do not call Hooks inside useEffect(...), useMemo(...), or other built-in Hooks. You can only call Hooks at the top level of your React function. For more information, see https://react.dev/link/rules-of-hooks");
        }
        function l() {
            console.error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");
        }
        function a() {}
        function o(e) {
            var n = [];
            return e.forEach(function(e) {
                n.push(e);
            }), n.sort().join(", ");
        }
        function i(e) {
            return null === e || "object" != typeof e ? null : "function" == typeof (e = Ya && e[Ya] || e["@@iterator"]) ? e : null;
        }
        function s(e) {
            if (null == e) return null;
            if ("function" == typeof e) return e.$$typeof === Ga ? null : e.displayName || e.name || null;
            if ("string" == typeof e) return e;
            switch(e){
                case Ia:
                    return "Fragment";
                case Da:
                    return "Portal";
                case Ma:
                    return "Profiler";
                case Fa:
                    return "StrictMode";
                case Qa:
                    return "Suspense";
                case Oa:
                    return "SuspenseList";
            }
            if ("object" == typeof e) switch("number" == typeof e.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), e.$$typeof){
                case ja:
                    return (e.displayName || "Context") + ".Provider";
                case Ha:
                    return (e._context.displayName || "Context") + ".Consumer";
                case Aa:
                    var n = e.render;
                    return (e = e.displayName) || (e = "" !== (e = n.displayName || n.name || "") ? "ForwardRef(" + e + ")" : "ForwardRef"), e;
                case Ba:
                    return null !== (n = e.displayName || null) ? n : s(e.type) || "Memo";
                case Va:
                    n = e._payload, e = e._init;
                    try {
                        return s(e(n));
                    } catch (e) {}
            }
            return null;
        }
        function c(e) {
            var n = e.type;
            switch(e.tag){
                case 24:
                    return "Cache";
                case 9:
                    return (n._context.displayName || "Context") + ".Consumer";
                case 10:
                    return (n.displayName || "Context") + ".Provider";
                case 18:
                    return "DehydratedFragment";
                case 11:
                    return e = (e = n.render).displayName || e.name || "", n.displayName || ("" !== e ? "ForwardRef(" + e + ")" : "ForwardRef");
                case 7:
                    return "Fragment";
                case 26:
                case 27:
                case 5:
                    return n;
                case 4:
                    return "Portal";
                case 3:
                    return "Root";
                case 6:
                    return "Text";
                case 16:
                    return s(n);
                case 8:
                    return n === Fa ? "StrictMode" : "Mode";
                case 22:
                    return "Offscreen";
                case 12:
                    return "Profiler";
                case 21:
                    return "Scope";
                case 13:
                    return "Suspense";
                case 19:
                    return "SuspenseList";
                case 25:
                    return "TracingMarker";
                case 1:
                case 0:
                case 14:
                case 15:
                    if ("function" == typeof n) return n.displayName || n.name || null;
                    if ("string" == typeof n) return n;
                    break;
                case 29:
                    if (null != (n = e._debugInfo)) {
                        for(var t = n.length - 1; 0 <= t; t--)if ("string" == typeof n[t].name) return n[t].name;
                    }
                    if (null !== e.return) return c(e.return);
            }
            return null;
        }
        function f() {}
        function d(e) {
            if (void 0 === Xa) try {
                throw Error();
            } catch (e) {
                var n = e.stack.trim().match(/\n( *(at )?)/);
                Xa = n && n[1] || "", Za = -1 < e.stack.indexOf("\n    at") ? " (<anonymous>)" : -1 < e.stack.indexOf("@") ? "@unknown:0:0" : "";
            }
            return "\n" + Xa + e + Za;
        }
        function p(e, n) {
            if (!e || eo) return "";
            var t, r = no.get(e);
            if (void 0 !== r) return r;
            eo = !0, r = Error.prepareStackTrace, Error.prepareStackTrace = void 0, t = Ja.H, Ja.H = null, function() {
                if (0 === Ka) {
                    ka = console.log, wa = console.info, xa = console.warn, za = console.error, Ca = console.group, Ea = console.groupCollapsed, Pa = console.groupEnd;
                    var e = {
                        configurable: !0,
                        enumerable: !0,
                        value: f,
                        writable: !0
                    };
                    Object.defineProperties(console, {
                        info: e,
                        log: e,
                        warn: e,
                        error: e,
                        group: e,
                        groupCollapsed: e,
                        groupEnd: e
                    });
                }
                Ka++;
            }();
            try {
                var l = {
                    DetermineComponentFrameRoot: function() {
                        try {
                            if (n) {
                                var t = function() {
                                    throw Error();
                                };
                                if (Object.defineProperty(t.prototype, "props", {
                                    set: function() {
                                        throw Error();
                                    }
                                }), "object" == typeof Reflect && Reflect.construct) {
                                    try {
                                        Reflect.construct(t, []);
                                    } catch (e) {
                                        var r = e;
                                    }
                                    Reflect.construct(e, [], t);
                                } else {
                                    try {
                                        t.call();
                                    } catch (e) {
                                        r = e;
                                    }
                                    e.call(t.prototype);
                                }
                            } else {
                                try {
                                    throw Error();
                                } catch (e) {
                                    r = e;
                                }
                                (t = e()) && "function" == typeof t.catch && t.catch(function() {});
                            }
                        } catch (e) {
                            if (e && r && "string" == typeof e.stack) return [
                                e.stack,
                                r.stack
                            ];
                        }
                        return [
                            null,
                            null
                        ];
                    }
                };
                l.DetermineComponentFrameRoot.displayName = "DetermineComponentFrameRoot";
                var a = Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot, "name");
                a && a.configurable && Object.defineProperty(l.DetermineComponentFrameRoot, "name", {
                    value: "DetermineComponentFrameRoot"
                });
                var o = l.DetermineComponentFrameRoot(), u = o[0], i = o[1];
                if (u && i) {
                    var s = u.split("\n"), c = i.split("\n");
                    for(o = a = 0; a < s.length && !s[a].includes("DetermineComponentFrameRoot");)a++;
                    for(; o < c.length && !c[o].includes("DetermineComponentFrameRoot");)o++;
                    if (a === s.length || o === c.length) for(a = s.length - 1, o = c.length - 1; 1 <= a && 0 <= o && s[a] !== c[o];)o--;
                    for(; 1 <= a && 0 <= o; a--, o--)if (s[a] !== c[o]) {
                        if (1 !== a || 1 !== o) do {
                            if (a--, 0 > --o || s[a] !== c[o]) {
                                var p = "\n" + s[a].replace(" at new ", " at ");
                                return e.displayName && p.includes("<anonymous>") && (p = p.replace("<anonymous>", e.displayName)), "function" == typeof e && no.set(e, p), p;
                            }
                        }while (1 <= a && 0 <= o)
                        break;
                    }
                }
            } finally{
                eo = !1, Ja.H = t, function() {
                    if (0 == --Ka) {
                        var e = {
                            configurable: !0,
                            enumerable: !0,
                            writable: !0
                        };
                        Object.defineProperties(console, {
                            log: Na({}, e, {
                                value: ka
                            }),
                            info: Na({}, e, {
                                value: wa
                            }),
                            warn: Na({}, e, {
                                value: xa
                            }),
                            error: Na({}, e, {
                                value: za
                            }),
                            group: Na({}, e, {
                                value: Ca
                            }),
                            groupCollapsed: Na({}, e, {
                                value: Ea
                            }),
                            groupEnd: Na({}, e, {
                                value: Pa
                            })
                        });
                    }
                    0 > Ka && console.error("disabledDepth fell below zero. This is a bug in React. Please file an issue.");
                }(), Error.prepareStackTrace = r;
            }
            return s = (s = e ? e.displayName || e.name : "") ? d(s) : "", "function" == typeof e && no.set(e, s), s;
        }
        function m(e) {
            switch(e.tag){
                case 26:
                case 27:
                case 5:
                    return d(e.type);
                case 16:
                    return d("Lazy");
                case 13:
                    return d("Suspense");
                case 19:
                    return d("SuspenseList");
                case 0:
                case 15:
                    return p(e.type, !1);
                case 11:
                    return p(e.type.render, !1);
                case 1:
                    return p(e.type, !0);
                default:
                    return "";
            }
        }
        function h(e) {
            try {
                var n = "";
                do {
                    n += m(e);
                    var t = e._debugInfo;
                    if (t) for(var r = t.length - 1; 0 <= r; r--){
                        var l = t[r];
                        if ("string" == typeof l.name) {
                            var a = n, o = l.env;
                            n = a + d(l.name + (o ? " [" + o + "]" : ""));
                        }
                    }
                    e = e.return;
                }while (e)
                return n;
            } catch (e) {
                return "\nError generating stack: " + e.message + "\n" + e.stack;
            }
        }
        function g() {
            return null === to ? "" : h(to);
        }
        function y(e, n, t, r, l, a, o) {
            var u = to;
            Ja.getCurrentStack = null === e ? null : g, ro = !1, to = e;
            try {
                return n(t, r, l, a, o);
            } finally{
                to = u;
            }
            throw Error("runWithFiberInDEV should never be called in production. This is a bug in React.");
        }
        function b(e) {
            return {
                current: e
            };
        }
        function v(e, n) {
            0 > iu ? console.error("Unexpected pop.") : (n !== uu[iu] && console.error("Unexpected Fiber popped."), e.current = ou[iu], ou[iu] = null, uu[iu] = null, iu--);
        }
        function S(e, n, t) {
            iu++, ou[iu] = e.current, uu[iu] = t, e.current = n;
        }
        function k(e) {
            var n = 42 & e;
            if (0 !== n) return n;
            switch(e & -e){
                case 1:
                    return 1;
                case 2:
                    return 2;
                case 4:
                    return 4;
                case 8:
                    return 8;
                case 16:
                    return 16;
                case 32:
                    return 32;
                case 64:
                    return 64;
                case 128:
                case 256:
                case 512:
                case 1024:
                case 2048:
                case 4096:
                case 8192:
                case 16384:
                case 32768:
                case 65536:
                case 131072:
                case 262144:
                case 524288:
                case 1048576:
                case 2097152:
                    return 4194176 & e;
                case 4194304:
                case 8388608:
                case 16777216:
                case 33554432:
                    return 62914560 & e;
                case 67108864:
                    return 67108864;
                case 134217728:
                    return 134217728;
                case 268435456:
                    return 268435456;
                case 536870912:
                    return 536870912;
                case 1073741824:
                    return 0;
                default:
                    return console.error("Should have found matching lanes. This is a bug in React."), e;
            }
        }
        function w(e, n) {
            var t = e.pendingLanes;
            if (0 === t) return 0;
            var r = 0, l = e.suspendedLanes;
            e = e.pingedLanes;
            var a = 134217727 & t;
            return 0 !== a ? 0 != (t = a & ~l) ? r = k(t) : 0 != (e &= a) && (r = k(e)) : 0 != (t &= ~l) ? r = k(t) : 0 !== e && (r = k(e)), 0 === r ? 0 : 0 !== n && n !== r && 0 == (n & l) && ((l = r & -r) >= (e = n & -n) || 32 === l && 0 != (4194176 & e)) ? n : r;
        }
        function x(e, n) {
            switch(e){
                case 1:
                case 2:
                case 4:
                case 8:
                    return n + 250;
                case 16:
                case 32:
                case 64:
                case 128:
                case 256:
                case 512:
                case 1024:
                case 2048:
                case 4096:
                case 8192:
                case 16384:
                case 32768:
                case 65536:
                case 131072:
                case 262144:
                case 524288:
                case 1048576:
                case 2097152:
                    return n + 5e3;
                case 4194304:
                case 8388608:
                case 16777216:
                case 33554432:
                case 67108864:
                case 134217728:
                case 268435456:
                case 536870912:
                case 1073741824:
                    return -1;
                default:
                    return console.error("Should have found matching lanes. This is a bug in React."), -1;
            }
        }
        function z() {
            var e = pu;
            return 0 == (4194176 & (pu <<= 1)) && (pu = 128), e;
        }
        function C() {
            var e = mu;
            return 0 == (62914560 & (mu <<= 1)) && (mu = 4194304), e;
        }
        function E(e) {
            for(var n = [], t = 0; 31 > t; t++)n.push(e);
            return n;
        }
        function P(e, n) {
            e.pendingLanes |= n, 268435456 !== n && (e.suspendedLanes = 0, e.pingedLanes = 0, e.warmLanes = 0);
        }
        function _(e, n, t) {
            e.pendingLanes |= n, e.suspendedLanes &= ~n;
            var r = 31 - cu(n);
            e.entangledLanes |= n, e.entanglements[r] = 1073741824 | e.entanglements[r] | 4194218 & t;
        }
        function R(e, n) {
            var t = e.entangledLanes |= n;
            for(e = e.entanglements; t;){
                var r = 31 - cu(t), l = 1 << r;
                l & n | e[r] & n && (e[r] |= n), t &= ~l;
            }
        }
        function T(e, n, t) {
            if (_u) for(e = e.pendingUpdatersLaneMap; 0 < t;){
                var r = 31 - cu(t), l = 1 << r;
                e[r].add(n), t &= ~l;
            }
        }
        function N(e, n) {
            if (_u) for(var t = e.pendingUpdatersLaneMap, r = e.memoizedUpdaters; 0 < n;){
                var l = 31 - cu(n);
                e = 1 << l, 0 < (l = t[l]).size && (l.forEach(function(e) {
                    var n = e.alternate;
                    null !== n && r.has(n) || r.add(e);
                }), l.clear()), n &= ~e;
            }
        }
        function L(e) {
            return 2 < (e &= -e) ? 8 < e ? 0 != (134217727 & e) ? 32 : 268435456 : 8 : 2;
        }
        function U(e) {
            "function" == typeof zu && Cu(e);
        }
        function D(e, n) {
            if ("object" == typeof e && null !== e) {
                var t = Tu.get(e);
                return void 0 !== t ? t : (n = {
                    value: e,
                    source: n,
                    stack: h(n)
                }, Tu.set(e, n), n);
            }
            return {
                value: e,
                source: n,
                stack: h(n)
            };
        }
        function I(e) {
            for(; e === Uu;)Uu = Nu[--Lu], Nu[Lu] = null, Du = Nu[--Lu], Nu[Lu] = null;
            for(; e === Mu;)Mu = Iu[--Fu], Iu[Fu] = null, Hu = Iu[--Fu], Iu[Fu] = null, Wu = Iu[--Fu], Iu[Fu] = null;
        }
        function F() {
            console.error("Expected to be hydrating. This is a bug in React. Please file an issue.");
        }
        function M(e) {
            return null === e && console.error("Expected host context to exist. This error is likely caused by a bug in React. Please file an issue."), e;
        }
        function W(e, n) {
            S(Qu, n, e), S(Au, e, e), S(ju, null, e), n = oo(n), v(ju, e), S(ju, n, e);
        }
        function H(e) {
            v(ju, e), v(Au, e), v(Qu, e);
        }
        function j() {
            return M(ju.current);
        }
        function A(e) {
            null !== e.memoizedState && S(Ou, e, e);
            var n = M(ju.current), t = uo(n, e.type);
            n !== t && (S(Au, e, e), S(ju, t, e));
        }
        function Q(e) {
            Au.current === e && (v(ju, e), v(Au, e)), Ou.current === e && (v(Ou, e), Uo._currentValue2 = Lo);
        }
        function O() {
            for(var e = Yu, n = Gu = Yu = 0; n < e;){
                var t = qu[n];
                qu[n++] = null;
                var r = qu[n];
                qu[n++] = null;
                var l = qu[n];
                qu[n++] = null;
                var a = qu[n];
                if (qu[n++] = null, null !== r && null !== l) {
                    var o = r.pending;
                    null === o ? l.next = l : (l.next = o.next, o.next = l), r.pending = l;
                }
                0 !== a && q(t, l, a);
            }
        }
        function B(e, n, t, r) {
            qu[Yu++] = e, qu[Yu++] = n, qu[Yu++] = t, qu[Yu++] = r, Gu |= r, e.lanes |= r, null !== (e = e.alternate) && (e.lanes |= r);
        }
        function V(e, n, t, r) {
            return B(e, n, t, r), Y(e);
        }
        function $(e, n) {
            return B(e, null, null, n), Y(e);
        }
        function q(e, n, t) {
            e.lanes |= t;
            var r = e.alternate;
            null !== r && (r.lanes |= t);
            for(var l = !1, a = e.return; null !== a;)a.childLanes |= t, null !== (r = a.alternate) && (r.childLanes |= t), 22 === a.tag && (null === (e = a.stateNode) || 1 & e._visibility || (l = !0)), e = a, a = a.return;
            l && null !== n && 3 === e.tag && (a = e.stateNode, l = 31 - cu(t), null === (e = (a = a.hiddenUpdates)[l]) ? a[l] = [
                n
            ] : e.push(n), n.lane = 536870912 | t);
        }
        function Y(e) {
            if (Yf > qf) throw Zf = Yf = 0, ed = Gf = null, Error("Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.");
            Zf > Xf && (Zf = 0, ed = null, console.error("Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.")), null === e.alternate && 0 != (4098 & e.flags) && ua(e);
            for(var n = e, t = n.return; null !== t;)null === n.alternate && 0 != (4098 & n.flags) && ua(e), t = (n = t).return;
            return 3 === n.tag ? n.stateNode : null;
        }
        function G() {
            var e = Zu;
            return Zu = 0, e;
        }
        function J(e) {
            var n = Zu;
            return Zu = e, n;
        }
        function K(e) {
            var n = Zu;
            return Zu += e, n;
        }
        function X(e) {
            Xu = Ju(), 0 > e.actualStartTime && (e.actualStartTime = Xu);
        }
        function Z(e) {
            if (0 <= Xu) {
                var n = Ju() - Xu;
                e.actualDuration += n, e.selfBaseDuration = n, Xu = -1;
            }
        }
        function ee(e) {
            if (0 <= Xu) {
                var n = Ju() - Xu;
                e.actualDuration += n, Xu = -1;
            }
        }
        function ne() {
            if (0 <= Xu) {
                var e = Ju() - Xu;
                Xu = -1, Zu += e;
            }
        }
        function te() {
            Xu = Ju();
        }
        function re(e) {
            for(var n = e.child; n;)e.actualDuration += n.actualDuration, n = n.sibling;
        }
        function le(e) {
            e !== ri && null === e.next && (null === ri ? ti = ri = e : ri = ri.next = e), oi = !0, null !== Ja.actQueue ? ai || (ai = !0, fe(oe)) : li || (li = !0, fe(oe));
        }
        function ae(e, n) {
            if (!ui && oi) {
                ui = !0;
                do {
                    for(var t = !1, r = ti; null !== r;){
                        if (0 !== e) {
                            var l = r.pendingLanes;
                            if (0 === l) var a = 0;
                            else {
                                var o = r.suspendedLanes, u = r.pingedLanes;
                                a = (1 << 31 - cu(42 | e) + 1) - 1, a = 201326677 & (a &= l & ~(o & ~u)) ? 201326677 & a | 1 : a ? 2 | a : 0;
                            }
                            0 !== a && (t = !0, se(r, a));
                        } else a = df, 0 != (3 & (a = w(r, r === cf ? a : 0))) && (t = !0, se(r, a));
                        r = r.next;
                    }
                }while (t)
                ui = !1;
            }
        }
        function oe() {
            oi = ai = li = !1;
            var e = 0;
            0 !== ii && (Co() && (e = ii), ii = 0);
            for(var n = vu(), t = null, r = ti; null !== r;){
                var l = r.next, a = ue(r, n);
                0 === a ? (r.next = null, null === t ? ti = l : t.next = l, null === l && (ri = t)) : (t = r, (0 !== e || 0 != (3 & a)) && (oi = !0)), r = l;
            }
            ae(e);
        }
        function ue(e, n) {
            for(var t = e.suspendedLanes, r = e.pingedLanes, l = e.expirationTimes, a = -62914561 & e.pendingLanes; 0 < a;){
                var o = 31 - cu(a), u = 1 << o, i = l[o];
                -1 === i ? 0 != (u & t) && 0 == (u & r) || (l[o] = x(u, n)) : i <= n && (e.expiredLanes |= u), a &= ~u;
            }
            if (t = df, t = w(e, e === (n = cf) ? t : 0), r = e.callbackNode, 0 === t || e === n && wf === hf || null !== e.cancelPendingCommit) return null !== r && ce(r), e.callbackNode = null, e.callbackPriority = 0;
            if (0 != (3 & t)) return null !== r && ce(r), e.callbackPriority = 2, e.callbackNode = null, 2;
            if ((n = t & -t) === e.callbackPriority && (null === Ja.actQueue || r === si)) return n;
            switch(ce(r), L(t)){
                case 2:
                    t = Su;
                    break;
                case 8:
                    t = ku;
                    break;
                case 32:
                default:
                    t = wu;
                    break;
                case 268435456:
                    t = xu;
            }
            return r = ie.bind(null, e), null !== Ja.actQueue ? (Ja.actQueue.push(r), t = si) : t = hu(t, r), e.callbackPriority = n, e.callbackNode = t, n;
        }
        function ie(e, n) {
            ni = ei = !1;
            var t = e.callbackNode;
            if (Jl() && e.callbackNode !== t) return null;
            var r = df;
            return 0 === (r = w(e, e === cf ? r : 0)) ? null : (El(e, r, n), ue(e, vu()), e.callbackNode === t ? ie.bind(null, e) : null);
        }
        function se(e, n) {
            if (Jl()) return null;
            ei = ni, ni = !1, El(e, n, !0);
        }
        function ce(e) {
            e !== si && null !== e && gu(e);
        }
        function fe(e) {
            null !== Ja.actQueue && Ja.actQueue.push(function() {
                return e(), null;
            }), hu(Su, e);
        }
        function de() {
            return 0 === ii && (ii = z()), ii;
        }
        function pe() {
            if (0 == --fi && null !== ci) {
                null !== pi && (pi.status = "fulfilled");
                var e = ci;
                ci = null, di = 0, pi = null;
                for(var n = 0; n < e.length; n++)(0, e[n])();
            }
        }
        function me(e) {
            e.updateQueue = {
                baseState: e.memoizedState,
                firstBaseUpdate: null,
                lastBaseUpdate: null,
                shared: {
                    pending: null,
                    lanes: 0,
                    hiddenCallbacks: null
                },
                callbacks: null
            };
        }
        function he(e, n) {
            e = e.updateQueue, n.updateQueue === e && (n.updateQueue = {
                baseState: e.baseState,
                firstBaseUpdate: e.firstBaseUpdate,
                lastBaseUpdate: e.lastBaseUpdate,
                shared: e.shared,
                callbacks: null
            });
        }
        function ge(e) {
            return {
                lane: e,
                tag: mi,
                payload: null,
                callback: null,
                next: null
            };
        }
        function ye(e, n, t) {
            var r = e.updateQueue;
            if (null === r) return null;
            if (r = r.shared, Si === r && !vi) {
                var l = c(e);
                console.error("An update (setState, replaceState, or forceUpdate) was scheduled from inside an update function. Update functions should be pure, with zero side-effects. Consider using componentDidUpdate or a callback.\n\nPlease update the following component: %s", l), vi = !0;
            }
            return (sf & Zc) !== Xc ? (null === (l = r.pending) ? n.next = n : (n.next = l.next, l.next = n), r.pending = n, n = Y(e), q(e, null, t), n) : (B(e, r, n, t), Y(e));
        }
        function be(e, n, t) {
            if (null !== (n = n.updateQueue) && (n = n.shared, 0 != (4194176 & t))) {
                var r = n.lanes;
                t |= r &= e.pendingLanes, n.lanes = t, R(e, t);
            }
        }
        function ve(e, n) {
            var t = e.updateQueue, r = e.alternate;
            if (null !== r && t === (r = r.updateQueue)) {
                var l = null, a = null;
                if (null !== (t = t.firstBaseUpdate)) {
                    do {
                        var o = {
                            lane: t.lane,
                            tag: t.tag,
                            payload: t.payload,
                            callback: null,
                            next: null
                        };
                        null === a ? l = a = o : a = a.next = o, t = t.next;
                    }while (null !== t)
                    null === a ? l = a = n : a = a.next = n;
                } else l = a = n;
                return t = {
                    baseState: r.baseState,
                    firstBaseUpdate: l,
                    lastBaseUpdate: a,
                    shared: r.shared,
                    callbacks: r.callbacks
                }, void (e.updateQueue = t);
            }
            null === (e = t.lastBaseUpdate) ? t.firstBaseUpdate = n : e.next = n, t.lastBaseUpdate = n;
        }
        function Se() {
            if (ki && null !== pi) throw pi;
        }
        function ke(e, n, t, r) {
            ki = !1;
            var l = e.updateQueue;
            bi = !1, Si = l.shared;
            var a = l.firstBaseUpdate, o = l.lastBaseUpdate, u = l.shared.pending;
            if (null !== u) {
                l.shared.pending = null;
                var i = u, s = i.next;
                i.next = null, null === o ? a = s : o.next = s, o = i;
                var c = e.alternate;
                null !== c && (u = (c = c.updateQueue).lastBaseUpdate) !== o && (null === u ? c.firstBaseUpdate = s : u.next = s, c.lastBaseUpdate = i);
            }
            if (null !== a) {
                var f = l.baseState;
                for(o = 0, c = s = i = null, u = a;;){
                    var d = -536870913 & u.lane, p = d !== u.lane;
                    if (p ? (df & d) === d : (r & d) === d) {
                        0 !== d && d === di && (ki = !0), null !== c && (c = c.next = {
                            lane: 0,
                            tag: u.tag,
                            payload: u.payload,
                            callback: null,
                            next: null
                        });
                        e: {
                            d = e;
                            var m = u, h = n, g = t;
                            switch(m.tag){
                                case hi:
                                    if ("function" == typeof (m = m.payload)) {
                                        Rc = !0;
                                        var y = m.call(g, f, h);
                                        if (8 & d.mode) {
                                            U(!0);
                                            try {
                                                m.call(g, f, h);
                                            } finally{
                                                U(!1);
                                            }
                                        }
                                        Rc = !1, f = y;
                                        break e;
                                    }
                                    f = m;
                                    break e;
                                case yi:
                                    d.flags = -65537 & d.flags | 128;
                                case mi:
                                    if ("function" == typeof (y = m.payload)) {
                                        if (Rc = !0, m = y.call(g, f, h), 8 & d.mode) {
                                            U(!0);
                                            try {
                                                y.call(g, f, h);
                                            } finally{
                                                U(!1);
                                            }
                                        }
                                        Rc = !1;
                                    } else m = y;
                                    if (null == m) break e;
                                    f = Na({}, f, m);
                                    break e;
                                case gi:
                                    bi = !0;
                            }
                        }
                        null !== (d = u.callback) && (e.flags |= 64, p && (e.flags |= 8192), null === (p = l.callbacks) ? l.callbacks = [
                            d
                        ] : p.push(d));
                    } else p = {
                        lane: d,
                        tag: u.tag,
                        payload: u.payload,
                        callback: u.callback,
                        next: null
                    }, null === c ? (s = c = p, i = f) : c = c.next = p, o |= d;
                    if (null === (u = u.next)) {
                        if (null === (u = l.shared.pending)) break;
                        u = (p = u).next, p.next = null, l.lastBaseUpdate = p, l.shared.pending = null;
                    }
                }
                null === c && (i = f), l.baseState = i, l.firstBaseUpdate = s, l.lastBaseUpdate = c, null === a && (l.shared.lanes = 0), _f |= o, e.lanes = o, e.memoizedState = f;
            }
            Si = null;
        }
        function we(e, n) {
            if ("function" != typeof e) throw Error("Invalid argument passed as callback. Expected a function. Instead received: " + e);
            e.call(n);
        }
        function xe(e, n) {
            var t = e.shared.hiddenCallbacks;
            if (null !== t) for(e.shared.hiddenCallbacks = null, e = 0; e < t.length; e++)we(t[e], n);
        }
        function ze(e, n) {
            var t = e.callbacks;
            if (null !== t) for(e.callbacks = null, e = 0; e < t.length; e++)we(t[e], n);
        }
        function Ce(e, n) {
            if (Ru(e, n)) return !0;
            if ("object" != typeof e || null === e || "object" != typeof n || null === n) return !1;
            var t = Object.keys(e), r = Object.keys(n);
            if (t.length !== r.length) return !1;
            for(r = 0; r < t.length; r++){
                var l = t[r];
                if (!wi.call(n, l) || !Ru(e[l], n[l])) return !1;
            }
            return !0;
        }
        function Ee(e) {
            return "fulfilled" === (e = e.status) || "rejected" === e;
        }
        function Pe() {}
        function _e(e, n, t) {
            null !== Ja.actQueue && (Ja.didUsePromise = !0);
            var r = e.thenables;
            switch(void 0 === (t = r[t]) ? r.push(n) : t !== n && (e.didWarnAboutUncachedPromise || (e.didWarnAboutUncachedPromise = !0, console.error("A component was suspended by an uncached promise. Creating promises inside a Client Component or hook is not yet supported, except via a Suspense-compatible library or framework.")), n.then(Pe, Pe), n = t), n.status){
                case "fulfilled":
                    return n.value;
                case "rejected":
                    throw Te(e = n.reason), e;
                default:
                    if ("string" == typeof n.status) n.then(Pe, Pe);
                    else {
                        if (null !== (e = cf) && 100 < e.shellSuspendCounter) throw Error("async/await is not yet supported in Client Components, only Server Components. This error is often caused by accidentally adding `'use client'` to a module that was originally written for the server.");
                        (e = n).status = "pending", e.then(function(e) {
                            if ("pending" === n.status) {
                                var t = n;
                                t.status = "fulfilled", t.value = e;
                            }
                        }, function(e) {
                            if ("pending" === n.status) {
                                var t = n;
                                t.status = "rejected", t.reason = e;
                            }
                        });
                    }
                    switch(n.status){
                        case "fulfilled":
                            return n.value;
                        case "rejected":
                            throw Te(e = n.reason), e;
                    }
                    throw Mi = n, Wi = !0, Di;
            }
        }
        function Re() {
            if (null === Mi) throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");
            var e = Mi;
            return Mi = null, Wi = !1, e;
        }
        function Te(e) {
            if (e === Di) throw Error("Hooks are not supported inside an async component. This error is often caused by accidentally adding `'use client'` to a module that was originally written for the server.");
        }
        function Ne(e) {
            var n = as;
            return null != e && (as = null === n ? e : n.concat(e)), n;
        }
        function Le(e, n, t) {
            for(var r = Object.keys(e.props), l = 0; l < r.length; l++){
                var a = r[l];
                if ("children" !== a && "key" !== a) {
                    null === n && ((n = ma(e, t.mode, 0))._debugInfo = as, n.return = t), y(n, function(e) {
                        console.error("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.", e);
                    }, a);
                    break;
                }
            }
        }
        function Ue(e) {
            var n = ls;
            return ls += 1, null === rs && (rs = {
                didWarnAboutUncachedPromise: !1,
                thenables: []
            }), _e(rs, e, n);
        }
        function De(e, n, t, r) {
            e = r.props.ref, t.ref = void 0 !== e ? e : null;
        }
        function Ie(e, n) {
            if (n.$$typeof === La) throw Error('A React Element from an older version of React was rendered. This is not supported. It can happen if:\n- Multiple copies of the "react" package is used.\n- A library pre-bundled an old copy of "react" or "react/jsx-runtime".\n- A compiler tries to "inline" JSX instead of using the runtime.');
            throw e = Object.prototype.toString.call(n), Error("Objects are not valid as a React child (found: " + ("[object Object]" === e ? "object with keys {" + Object.keys(n).join(", ") + "}" : e) + "). If you meant to render a collection of children, use an array instead.");
        }
        function Fe(e, n) {
            var t = c(e) || "Component";
            is[t] || (is[t] = !0, n = n.displayName || n.name || "Component", 3 === e.tag ? console.error("Functions are not valid as a React child. This may happen if you return %s instead of <%s /> from render. Or maybe you meant to call this function rather than return it.\n  root.render(%s)", n, n, n) : console.error("Functions are not valid as a React child. This may happen if you return %s instead of <%s /> from render. Or maybe you meant to call this function rather than return it.\n  <%s>{%s}</%s>", n, n, t, n, t));
        }
        function Me(e, n) {
            var t = c(e) || "Component";
            ss[t] || (ss[t] = !0, n = String(n), 3 === e.tag ? console.error("Symbols are not valid as a React child.\n  root.render(%s)", n) : console.error("Symbols are not valid as a React child.\n  <%s>%s</%s>", t, n, t));
        }
        function We(e) {
            function n(n, t) {
                if (e) {
                    var r = n.deletions;
                    null === r ? (n.deletions = [
                        t
                    ], n.flags |= 16) : r.push(t);
                }
            }
            function r(t, r) {
                if (!e) return null;
                for(; null !== r;)n(t, r), r = r.sibling;
                return null;
            }
            function l(e) {
                for(var n = new Map; null !== e;)null !== e.key ? n.set(e.key, e) : n.set(e.index, e), e = e.sibling;
                return n;
            }
            function o(e, n) {
                return (e = fa(e, n)).index = 0, e.sibling = null, e;
            }
            function u(n, t, r) {
                return n.index = r, e ? null !== (r = n.alternate) ? (r = r.index) < t ? (n.flags |= 33554434, t) : r : (n.flags |= 33554434, t) : (n.flags |= 1048576, t);
            }
            function s(n) {
                return e && null === n.alternate && (n.flags |= 33554434), n;
            }
            function c(e, n, t, r) {
                return null === n || 6 !== n.tag ? ((n = ya(t, e.mode, r)).return = e, n._debugOwner = e, n._debugInfo = as, n) : ((n = o(n, t)).return = e, n._debugInfo = as, n);
            }
            function f(e, n, t, r) {
                var l = t.type;
                return l === Ia ? (Le(t, n = p(e, n, t.props.children, r, t.key), e), n) : null !== n && (n.elementType === l || "object" == typeof l && null !== l && l.$$typeof === Va && ts(l) === n.type) ? (De(e, 0, r = o(n, t.props), t), r.return = e, r._debugOwner = t._owner, r._debugInfo = as, r) : (De(e, 0, r = ma(t, e.mode, r), t), r.return = e, r._debugInfo = as, r);
            }
            function d(e, n, t, r) {
                return null === n || 4 !== n.tag || n.stateNode.containerInfo !== t.containerInfo || n.stateNode.implementation !== t.implementation ? ((n = ba(t, e.mode, r)).return = e, n._debugInfo = as, n) : ((n = o(n, t.children || [])).return = e, n._debugInfo = as, n);
            }
            function p(e, n, t, r, l) {
                return null === n || 7 !== n.tag ? ((n = ha(t, e.mode, r, l)).return = e, n._debugOwner = e, n._debugInfo = as, n) : ((n = o(n, t)).return = e, n._debugInfo = as, n);
            }
            function m(e, n, t) {
                if ("string" == typeof n && "" !== n || "number" == typeof n || "bigint" == typeof n) return (n = ya("" + n, e.mode, t)).return = e, n._debugOwner = e, n._debugInfo = as, n;
                if ("object" == typeof n && null !== n) {
                    switch(n.$$typeof){
                        case Ua:
                            return De(e, 0, t = ma(n, e.mode, t), n), t.return = e, e = Ne(n._debugInfo), t._debugInfo = as, as = e, t;
                        case Da:
                            return (n = ba(n, e.mode, t)).return = e, n._debugInfo = as, n;
                        case Va:
                            var r = Ne(n._debugInfo);
                            return e = m(e, n = ts(n), t), as = r, e;
                    }
                    if (lo(n) || i(n)) return (t = ha(n, e.mode, t, null)).return = e, t._debugOwner = e, e = Ne(n._debugInfo), t._debugInfo = as, as = e, t;
                    if ("function" == typeof n.then) return r = Ne(n._debugInfo), e = m(e, Ue(n), t), as = r, e;
                    if (n.$$typeof === ja) return m(e, cr(e, n), t);
                    Ie(e, n);
                }
                return "function" == typeof n && Fe(e, n), "symbol" == typeof n && Me(e, n), null;
            }
            function h(e, n, t, r) {
                var l = null !== n ? n.key : null;
                if ("string" == typeof t && "" !== t || "number" == typeof t || "bigint" == typeof t) return null !== l ? null : c(e, n, "" + t, r);
                if ("object" == typeof t && null !== t) {
                    switch(t.$$typeof){
                        case Ua:
                            return t.key === l ? (l = Ne(t._debugInfo), e = f(e, n, t, r), as = l, e) : null;
                        case Da:
                            return t.key === l ? d(e, n, t, r) : null;
                        case Va:
                            return l = Ne(t._debugInfo), e = h(e, n, t = ts(t), r), as = l, e;
                    }
                    if (lo(t) || i(t)) return null !== l ? null : (l = Ne(t._debugInfo), e = p(e, n, t, r, null), as = l, e);
                    if ("function" == typeof t.then) return l = Ne(t._debugInfo), e = h(e, n, Ue(t), r), as = l, e;
                    if (t.$$typeof === ja) return h(e, n, cr(e, t), r);
                    Ie(e, t);
                }
                return "function" == typeof t && Fe(e, t), "symbol" == typeof t && Me(e, t), null;
            }
            function g(e, n, t, r, l) {
                if ("string" == typeof r && "" !== r || "number" == typeof r || "bigint" == typeof r) return c(n, e = e.get(t) || null, "" + r, l);
                if ("object" == typeof r && null !== r) {
                    switch(r.$$typeof){
                        case Ua:
                            return t = e.get(null === r.key ? t : r.key) || null, e = Ne(r._debugInfo), n = f(n, t, r, l), as = e, n;
                        case Da:
                            return d(n, e = e.get(null === r.key ? t : r.key) || null, r, l);
                        case Va:
                            var a = Ne(r._debugInfo);
                            return n = g(e, n, t, r = ts(r), l), as = a, n;
                    }
                    if (lo(r) || i(r)) return t = e.get(t) || null, e = Ne(r._debugInfo), n = p(n, t, r, l, null), as = e, n;
                    if ("function" == typeof r.then) return a = Ne(r._debugInfo), n = g(e, n, t, Ue(r), l), as = a, n;
                    if (r.$$typeof === ja) return g(e, n, t, cr(n, r), l);
                    Ie(n, r);
                }
                return "function" == typeof r && Fe(n, r), "symbol" == typeof r && Me(n, r), null;
            }
            function b(e, n, t, r) {
                if ("object" != typeof t || null === t) return r;
                switch(t.$$typeof){
                    case Ua:
                    case Da:
                        a(e, n, t);
                        var l = t.key;
                        if ("string" != typeof l) break;
                        if (null === r) {
                            (r = new Set).add(l);
                            break;
                        }
                        if (!r.has(l)) {
                            r.add(l);
                            break;
                        }
                        y(n, function() {
                            console.error("Encountered two children with the same key, `%s`. Keys should be unique so that components maintain their identity across updates. Non-unique keys may cause children to be duplicated and/or omitted — the behavior is unsupported and could change in a future version.", l);
                        });
                        break;
                    case Va:
                        b(e, n, t = ts(t), r);
                }
                return r;
            }
            function v(t, a, c, f) {
                if ("object" == typeof c && null !== c && c.type === Ia && null === c.key && (Le(c, null, t), c = c.props.children), "object" == typeof c && null !== c) {
                    switch(c.$$typeof){
                        case Ua:
                            var d = Ne(c._debugInfo);
                            e: {
                                for(var p = c.key, y = a; null !== y;){
                                    if (y.key === p) {
                                        if ((p = c.type) === Ia) {
                                            if (7 === y.tag) {
                                                r(t, y.sibling), (a = o(y, c.props.children)).return = t, a._debugOwner = c._owner, a._debugInfo = as, Le(c, a, t), t = a;
                                                break e;
                                            }
                                        } else if (y.elementType === p || "object" == typeof p && null !== p && p.$$typeof === Va && ts(p) === y.type) {
                                            r(t, y.sibling), De(t, 0, a = o(y, c.props), c), a.return = t, a._debugOwner = c._owner, a._debugInfo = as, t = a;
                                            break e;
                                        }
                                        r(t, y);
                                        break;
                                    }
                                    n(t, y), y = y.sibling;
                                }
                                c.type === Ia ? ((a = ha(c.props.children, t.mode, f, c.key)).return = t, a._debugOwner = t, a._debugInfo = as, Le(c, a, t), t = a) : (De(t, 0, f = ma(c, t.mode, f), c), f.return = t, f._debugInfo = as, t = f);
                            }
                            return t = s(t), as = d, t;
                        case Da:
                            e: {
                                for(c = (d = c).key; null !== a;){
                                    if (a.key === c) {
                                        if (4 === a.tag && a.stateNode.containerInfo === d.containerInfo && a.stateNode.implementation === d.implementation) {
                                            r(t, a.sibling), (a = o(a, d.children || [])).return = t, t = a;
                                            break e;
                                        }
                                        r(t, a);
                                        break;
                                    }
                                    n(t, a), a = a.sibling;
                                }
                                (a = ba(d, t.mode, f)).return = t, t = a;
                            }
                            return s(t);
                        case Va:
                            return d = Ne(c._debugInfo), t = v(t, a, c = ts(c), f), as = d, t;
                    }
                    if (lo(c)) return d = Ne(c._debugInfo), t = function(t, a, o, i) {
                        for(var s = null, c = null, f = null, d = a, p = a = 0, y = null; null !== d && p < o.length; p++){
                            d.index > p ? (y = d, d = null) : y = d.sibling;
                            var v = h(t, d, o[p], i);
                            if (null === v) {
                                null === d && (d = y);
                                break;
                            }
                            s = b(t, v, o[p], s), e && d && null === v.alternate && n(t, d), a = u(v, a, p), null === f ? c = v : f.sibling = v, f = v, d = y;
                        }
                        if (p === o.length) return r(t, d), c;
                        if (null === d) {
                            for(; p < o.length; p++)null !== (d = m(t, o[p], i)) && (s = b(t, d, o[p], s), a = u(d, a, p), null === f ? c = d : f.sibling = d, f = d);
                            return c;
                        }
                        for(d = l(d); p < o.length; p++)null !== (y = g(d, t, p, o[p], i)) && (s = b(t, y, o[p], s), e && null !== y.alternate && d.delete(null === y.key ? p : y.key), a = u(y, a, p), null === f ? c = y : f.sibling = y, f = y);
                        return e && d.forEach(function(e) {
                            return n(t, e);
                        }), c;
                    }(t, a, c, f), as = d, t;
                    if (i(c)) {
                        if (d = Ne(c._debugInfo), "function" != typeof (y = i(c))) throw Error("An object is not an iterable. This error is likely caused by a bug in React. Please file an issue.");
                        return (p = y.call(c)) === c ? 0 === t.tag && "[object GeneratorFunction]" === Object.prototype.toString.call(t.type) && "[object Generator]" === Object.prototype.toString.call(p) || (os || console.error("Using Iterators as children is unsupported and will likely yield unexpected results because enumerating a generator mutates it. You may convert it to an array with `Array.from()` or the `[...spread]` operator before rendering. You can also use an Iterable that can iterate multiple times over the same items."), os = !0) : c.entries !== y || Ui || (console.error("Using Maps as children is not supported. Use an array of keyed ReactElements instead."), Ui = !0), t = function(t, a, o, i) {
                            if (null == o) throw Error("An iterable object provided no iterator.");
                            for(var s = null, c = null, f = a, d = a = 0, p = null, y = null, v = o.next(); null !== f && !v.done; d++, v = o.next()){
                                f.index > d ? (p = f, f = null) : p = f.sibling;
                                var S = h(t, f, v.value, i);
                                if (null === S) {
                                    null === f && (f = p);
                                    break;
                                }
                                y = b(t, S, v.value, y), e && f && null === S.alternate && n(t, f), a = u(S, a, d), null === c ? s = S : c.sibling = S, c = S, f = p;
                            }
                            if (v.done) return r(t, f), s;
                            if (null === f) {
                                for(; !v.done; d++, v = o.next())null !== (f = m(t, v.value, i)) && (y = b(t, f, v.value, y), a = u(f, a, d), null === c ? s = f : c.sibling = f, c = f);
                                return s;
                            }
                            for(f = l(f); !v.done; d++, v = o.next())null !== (p = g(f, t, d, v.value, i)) && (y = b(t, p, v.value, y), e && null !== p.alternate && f.delete(null === p.key ? d : p.key), a = u(p, a, d), null === c ? s = p : c.sibling = p, c = p);
                            return e && f.forEach(function(e) {
                                return n(t, e);
                            }), s;
                        }(t, a, p, f), as = d, t;
                    }
                    if ("function" == typeof c.then) return d = Ne(c._debugInfo), t = v(t, a, Ue(c), f), as = d, t;
                    if (c.$$typeof === ja) return v(t, a, cr(t, c), f);
                    Ie(t, c);
                }
                return "string" == typeof c && "" !== c || "number" == typeof c || "bigint" == typeof c ? (d = "" + c, null !== a && 6 === a.tag ? (r(t, a.sibling), (a = o(a, d)).return = t, t = a) : (r(t, a), (a = ya(d, t.mode, f)).return = t, a._debugOwner = t, a._debugInfo = as, t = a), s(t)) : ("function" == typeof c && Fe(t, c), "symbol" == typeof c && Me(t, c), r(t, a));
            }
            return function(e, n, r, l) {
                var a = as;
                as = null;
                try {
                    ls = 0;
                    var o = v(e, n, r, l);
                    return rs = null, o;
                } catch (n) {
                    if (n === Di) throw n;
                    var u = t(29, n, null, e.mode);
                    u.lanes = l, u.return = e;
                    var i = u._debugInfo = as;
                    if (u._debugOwner = e._debugOwner, null != i) {
                        for(var s = i.length - 1; 0 <= s; s--)if ("string" == typeof i[s].stack) {
                            u._debugOwner = i[s];
                            break;
                        }
                    }
                    return u;
                } finally{
                    as = a;
                }
            };
        }
        function He(e, n) {
            var t = Ef;
            S(ms, t, e), S(ps, n, e), Ef = t | n.baseLanes;
        }
        function je(e) {
            S(ms, Ef, e), S(ps, ps.current, e);
        }
        function Ae(e) {
            Ef = ms.current, v(ps, e), v(ms, e);
        }
        function Qe(e) {
            var n = e.alternate;
            S(vs, vs.current & ys, e), S(hs, e, e), null === gs && (null === n || null !== ps.current || null !== n.memoizedState) && (gs = e);
        }
        function Oe(e) {
            if (22 === e.tag) {
                if (S(vs, vs.current, e), S(hs, e, e), null === gs) {
                    var n = e.alternate;
                    null !== n && null !== n.memoizedState && (gs = e);
                }
            } else Be(e);
        }
        function Be(e) {
            S(vs, vs.current, e), S(hs, hs.current, e);
        }
        function Ve(e) {
            v(hs, e), gs === e && (gs = null), v(vs, e);
        }
        function $e(e) {
            for(var n = e; null !== n;){
                if (13 === n.tag) {
                    var t = n.memoizedState;
                    if (null !== t && (null === (t = t.dehydrated) || Jo(t) || Ko(t))) return n;
                } else if (19 === n.tag && void 0 !== n.memoizedProps.revealOrder) {
                    if (0 != (128 & n.flags)) return n;
                } else if (null !== n.child) {
                    n.child.return = n, n = n.child;
                    continue;
                }
                if (n === e) break;
                for(; null === n.sibling;){
                    if (null === n.return || n.return === e) return null;
                    n = n.return;
                }
                n.sibling.return = n.return, n = n.sibling;
            }
            return null;
        }
        function qe() {
            var e = As;
            null === Qs ? Qs = [
                e
            ] : Qs.push(e);
        }
        function Ye() {
            var e = As;
            if (null !== Qs && (Os++, Qs[Os] !== e)) {
                var n = c(Ts);
                if (!Cs.has(n) && (Cs.add(n), null !== Qs)) {
                    for(var t = "", r = 0; r <= Os; r++){
                        var l = Qs[r], a = r === Os ? e : l;
                        for(l = r + 1 + ". " + l; 30 > l.length;)l += " ";
                        t += l += a + "\n";
                    }
                    console.error("React has detected a change in the order of Hooks called by %s. This will lead to bugs and errors if not fixed. For more information, read the Rules of Hooks: https://react.dev/link/rules-of-hooks\n\n   Previous render            Next render\n   ------------------------------------------------------\n%s   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", n, t);
                }
            }
        }
        function Ge(e) {
            null == e || lo(e) || console.error("%s received a final argument that is not an array (instead, received `%s`). When specified, the final argument must be an array.", As, typeof e);
        }
        function Je() {
            var e = c(Ts);
            _s.has(e) || (_s.add(e), console.error("ReactDOM.useFormState has been renamed to React.useActionState. Please update %s to use React.useActionState.", e));
        }
        function Ke() {
            throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://react.dev/link/invalid-hook-call for tips about how to debug and fix this problem.");
        }
        function Xe(e, n) {
            if (Bs) return !1;
            if (null === n) return console.error("%s received a final argument during this render, but not during the previous render. Even though the final argument is optional, its type cannot change between renders.", As), !1;
            e.length !== n.length && console.error("The final argument passed to %s changed size between renders. The order and size of this array must remain constant.\n\nPrevious: %s\nIncoming: %s", As, "[" + n.join(", ") + "]", "[" + e.join(", ") + "]");
            for(var t = 0; t < n.length && t < e.length; t++)if (!Ru(e[t], n[t])) return !1;
            return !0;
        }
        function Ze(e, n, t, r, l, a) {
            Rs = a, Ts = n, Qs = null !== e ? e._debugHookTypes : null, Os = -1, Bs = null !== e && e.type !== n.type, "[object AsyncFunction]" !== Object.prototype.toString.call(t) && "[object AsyncGeneratorFunction]" !== Object.prototype.toString.call(t) || (a = c(Ts), Ps.has(a) || (Ps.add(a), console.error("async/await is not yet supported in Client Components, only Server Components. This error is often caused by accidentally adding `'use client'` to a module that was originally written for the server."))), n.memoizedState = null, n.updateQueue = null, n.lanes = 0, Ja.H = null !== e && null !== e.memoizedState ? Gs : null !== Qs ? Ys : qs, Is = a = (8 & n.mode) !== $u;
            var o = ji(t, r, l);
            if (Is = !1, Ds && (o = nn(n, t, r, l)), a) {
                U(!0);
                try {
                    o = nn(n, t, r, l);
                } finally{
                    U(!1);
                }
            }
            return en(e, n), o;
        }
        function en(e, n) {
            n._debugHookTypes = Qs, null === n.dependencies ? null !== Ws && (n.dependencies = {
                lanes: 0,
                firstContext: null,
                _debugThenableState: Ws
            }) : n.dependencies._debugThenableState = Ws, Ja.H = $s;
            var t = null !== Ns && null !== Ns.next;
            if (Rs = 0, Qs = As = Ls = Ns = Ts = null, Os = -1, null !== e && (31457280 & e.flags) != (31457280 & n.flags) && console.error("Internal React error: Expected static flag was missing. Please notify the React team."), Us = !1, Ms = 0, Ws = null, t) throw Error("Rendered fewer hooks than expected. This may be caused by an accidental early return statement.");
            null === e || pc || null !== (e = e.dependencies) && ur(e) && (pc = !0), Wi ? (Wi = !1, e = !0) : e = !1, e && (n = c(n) || "Unknown", Es.has(n) || Ps.has(n) || (Es.add(n), console.error("`use` was called from inside a try/catch block. This is not allowed and can lead to unexpected behavior. To handle errors triggered by `use`, wrap your component in a error boundary.")));
        }
        function nn(e, n, t, r) {
            Ts = e;
            var l = 0;
            do {
                if (Ds && (Ws = null), Ms = 0, Ds = !1, l >= js) throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");
                if (l += 1, Bs = !1, Ls = Ns = null, null != e.updateQueue) {
                    var a = e.updateQueue;
                    a.lastEffect = null, a.events = null, a.stores = null, null != a.memoCache && (a.memoCache.index = 0);
                }
                Os = -1, Ja.H = Js, a = ji(n, t, r);
            }while (Ds)
            return a;
        }
        function tn() {
            var e = Ja.H, n = e.useState()[0];
            return n = "function" == typeof n.then ? sn(n) : n, e = e.useState()[0], (null !== Ns ? Ns.memoizedState : null) !== e && (Ts.flags |= 1024), n;
        }
        function rn() {
            var e = 0 !== Fs;
            return Fs = 0, e;
        }
        function ln(e, n, t) {
            n.updateQueue = e.updateQueue, n.flags = (16 & n.mode) !== $u ? -201328645 & n.flags : -2053 & n.flags, e.lanes &= ~t;
        }
        function an(e) {
            if (Us) {
                for(e = e.memoizedState; null !== e;){
                    var n = e.queue;
                    null !== n && (n.pending = null), e = e.next;
                }
                Us = !1;
            }
            Rs = 0, Qs = Ls = Ns = Ts = null, Os = -1, As = null, Ds = !1, Ms = Fs = 0, Ws = null;
        }
        function on() {
            var e = {
                memoizedState: null,
                baseState: null,
                baseQueue: null,
                queue: null,
                next: null
            };
            return null === Ls ? Ts.memoizedState = Ls = e : Ls = Ls.next = e, Ls;
        }
        function un() {
            if (null === Ns) {
                var e = Ts.alternate;
                e = null !== e ? e.memoizedState : null;
            } else e = Ns.next;
            var n = null === Ls ? Ts.memoizedState : Ls.next;
            if (null !== n) Ls = n, Ns = e;
            else {
                if (null === e) {
                    if (null === Ts.alternate) throw Error("Update hook called on initial render. This is likely a bug in React. Please file an issue.");
                    throw Error("Rendered more hooks than during the previous render.");
                }
                e = {
                    memoizedState: (Ns = e).memoizedState,
                    baseState: Ns.baseState,
                    baseQueue: Ns.baseQueue,
                    queue: Ns.queue,
                    next: null
                }, null === Ls ? Ts.memoizedState = Ls = e : Ls = Ls.next = e;
            }
            return Ls;
        }
        function sn(e) {
            var n = Ms;
            return Ms += 1, null === Ws && (Ws = {
                didWarnAboutUncachedPromise: !1,
                thenables: []
            }), e = _e(Ws, e, n), n = Ts, null === (null === Ls ? n.memoizedState : Ls.next) && (n = n.alternate, Ja.H = null !== n && null !== n.memoizedState ? Gs : qs), e;
        }
        function cn(e) {
            if (null !== e && "object" == typeof e) {
                if ("function" == typeof e.then) return sn(e);
                if (e.$$typeof === ja) return sr(e);
            }
            throw Error("An unsupported type was passed to use(): " + String(e));
        }
        function fn(e) {
            var n = null, t = Ts.updateQueue;
            if (null !== t && (n = t.memoCache), null == n) {
                var r = Ts.alternate;
                null !== r && null !== (r = r.updateQueue) && null != (r = r.memoCache) && (n = {
                    data: r.data.map(function(e) {
                        return e.slice();
                    }),
                    index: 0
                });
            }
            if (null == n && (n = {
                data: [],
                index: 0
            }), null === t && (t = Vs(), Ts.updateQueue = t), t.memoCache = n, void 0 === (t = n.data[n.index]) || Bs) for(t = n.data[n.index] = Array(e), r = 0; r < e; r++)t[r] = qa;
            else t.length !== e && console.error("Expected a constant size argument for each invocation of useMemoCache. The previous cache was allocated with size %s but size %s was requested.", t.length, e);
            return n.index++, t;
        }
        function dn(e, n) {
            return "function" == typeof n ? n(e) : n;
        }
        function pn(e, n, t) {
            var r = on();
            if (void 0 !== t) {
                var l = t(n);
                if (Is) {
                    U(!0);
                    try {
                        t(n);
                    } finally{
                        U(!1);
                    }
                }
            } else l = n;
            return r.memoizedState = r.baseState = l, e = {
                pending: null,
                lanes: 0,
                dispatch: null,
                lastRenderedReducer: e,
                lastRenderedState: l
            }, r.queue = e, e = e.dispatch = mt.bind(null, Ts, e), [
                r.memoizedState,
                e
            ];
        }
        function mn(e) {
            return hn(un(), Ns, e);
        }
        function hn(e, n, t) {
            var r = e.queue;
            if (null === r) throw Error("Should have a queue. You are likely calling Hooks conditionally, which is not allowed. (https://react.dev/link/invalid-hook-call)");
            r.lastRenderedReducer = t;
            var l = e.baseQueue, a = r.pending;
            if (null !== a) {
                if (null !== l) {
                    var o = l.next;
                    l.next = a.next, a.next = o;
                }
                n.baseQueue !== l && console.error("Internal error: Expected work-in-progress queue to be a clone. This is a bug in React."), n.baseQueue = l = a, r.pending = null;
            }
            if (a = e.baseState, null === l) e.memoizedState = a;
            else {
                var u = o = null, i = null, s = n = l.next, c = !1;
                do {
                    var f = -536870913 & s.lane;
                    if (f !== s.lane ? (df & f) === f : (Rs & f) === f) {
                        var d = s.revertLane;
                        if (0 === d) null !== i && (i = i.next = {
                            lane: 0,
                            revertLane: 0,
                            action: s.action,
                            hasEagerState: s.hasEagerState,
                            eagerState: s.eagerState,
                            next: null
                        }), f === di && (c = !0);
                        else {
                            if ((Rs & d) === d) {
                                s = s.next, d === di && (c = !0);
                                continue;
                            }
                            f = {
                                lane: 0,
                                revertLane: s.revertLane,
                                action: s.action,
                                hasEagerState: s.hasEagerState,
                                eagerState: s.eagerState,
                                next: null
                            }, null === i ? (u = i = f, o = a) : i = i.next = f, Ts.lanes |= d, _f |= d;
                        }
                        f = s.action, Is && t(a, f), a = s.hasEagerState ? s.eagerState : t(a, f);
                    } else d = {
                        lane: f,
                        revertLane: s.revertLane,
                        action: s.action,
                        hasEagerState: s.hasEagerState,
                        eagerState: s.eagerState,
                        next: null
                    }, null === i ? (u = i = d, o = a) : i = i.next = d, Ts.lanes |= f, _f |= f;
                    s = s.next;
                }while (null !== s && s !== n)
                if (null === i ? o = a : i.next = u, !Ru(a, e.memoizedState) && (pc = !0, c && null !== (t = pi))) throw t;
                e.memoizedState = a, e.baseState = o, e.baseQueue = i, r.lastRenderedState = a;
            }
            return null === l && (r.lanes = 0), [
                e.memoizedState,
                r.dispatch
            ];
        }
        function gn(e) {
            var n = un(), t = n.queue;
            if (null === t) throw Error("Should have a queue. You are likely calling Hooks conditionally, which is not allowed. (https://react.dev/link/invalid-hook-call)");
            t.lastRenderedReducer = e;
            var r = t.dispatch, l = t.pending, a = n.memoizedState;
            if (null !== l) {
                t.pending = null;
                var o = l = l.next;
                do {
                    a = e(a, o.action), o = o.next;
                }while (o !== l)
                Ru(a, n.memoizedState) || (pc = !0), n.memoizedState = a, null === n.baseQueue && (n.baseState = a), t.lastRenderedState = a;
            }
            return [
                a,
                r
            ];
        }
        function yn(e, n, t) {
            var r, l = Ts, a = on();
            if (r = n(), cs || (t = n(), Ru(r, t) || (console.error("The result of getSnapshot should be cached to avoid an infinite loop"), cs = !0)), null === cf) throw Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");
            return 0 != (60 & df) || vn(l, n, r), a.memoizedState = r, t = {
                value: r,
                getSnapshot: n
            }, a.queue = t, $n(kn.bind(null, l, t, e), [
                e
            ]), l.flags |= 2048, Qn(ks | zs, Sn.bind(null, l, t, r, n), {
                destroy: void 0
            }, null), r;
        }
        function bn(e, n, t) {
            var r = Ts, l = un();
            if (t = n(), !cs) {
                var a = n();
                Ru(t, a) || (console.error("The result of getSnapshot should be cached to avoid an infinite loop"), cs = !0);
            }
            (a = !Ru((Ns || l).memoizedState, t)) && (l.memoizedState = t, pc = !0), l = l.queue;
            var o = kn.bind(null, r, l, e);
            if (Vn(2048, zs, o, [
                e
            ]), l.getSnapshot !== n || a || null !== Ls && Ls.memoizedState.tag & ks) {
                if (r.flags |= 2048, Qn(ks | zs, Sn.bind(null, r, l, t, n), {
                    destroy: void 0
                }, null), null === cf) throw Error("Expected a work-in-progress root. This is a bug in React. Please file an issue.");
                0 != (60 & Rs) || vn(r, n, t);
            }
            return t;
        }
        function vn(e, n, t) {
            e.flags |= 16384, e = {
                getSnapshot: n,
                value: t
            }, null === (n = Ts.updateQueue) ? (n = Vs(), Ts.updateQueue = n, n.stores = [
                e
            ]) : null === (t = n.stores) ? n.stores = [
                e
            ] : t.push(e);
        }
        function Sn(e, n, t, r) {
            n.value = t, n.getSnapshot = r, wn(n) && xn(e);
        }
        function kn(e, n, t) {
            return t(function() {
                wn(n) && xn(e);
            });
        }
        function wn(e) {
            var n = e.getSnapshot;
            e = e.value;
            try {
                var t = n();
                return !Ru(e, t);
            } catch (e) {
                return !0;
            }
        }
        function xn(e) {
            var n = $(e, 2);
            null !== n && Cl(n, e, 2);
        }
        function zn(e) {
            var n = on();
            if ("function" == typeof e) {
                var t = e;
                if (e = t(), Is) {
                    U(!0);
                    try {
                        t();
                    } finally{
                        U(!1);
                    }
                }
            }
            return n.memoizedState = n.baseState = e, n.queue = {
                pending: null,
                lanes: 0,
                dispatch: null,
                lastRenderedReducer: dn,
                lastRenderedState: e
            }, n;
        }
        function Cn(e) {
            var n = (e = zn(e)).queue, t = ht.bind(null, Ts, n);
            return n.dispatch = t, [
                e.memoizedState,
                t
            ];
        }
        function En(e) {
            var n = on();
            n.memoizedState = n.baseState = e;
            var t = {
                pending: null,
                lanes: 0,
                dispatch: null,
                lastRenderedReducer: null,
                lastRenderedState: null
            };
            return n.queue = t, n = yt.bind(null, Ts, !0, t), t.dispatch = n, [
                e,
                n
            ];
        }
        function Pn(e, n) {
            return _n(un(), 0, e, n);
        }
        function _n(e, n, t, r) {
            return e.baseState = t, hn(e, Ns, "function" == typeof r ? r : dn);
        }
        function Rn(e, n) {
            var t = un();
            return null !== Ns ? _n(t, 0, e, n) : (t.baseState = e, [
                e,
                t.queue.dispatch
            ]);
        }
        function Tn(e, n, t, r, l) {
            if (bt(e)) throw Error("Cannot update form state while rendering.");
            if (null !== (e = n.action)) {
                var a = {
                    payload: l,
                    action: e,
                    next: null,
                    isTransition: !0,
                    status: "pending",
                    value: null,
                    reason: null,
                    listeners: [],
                    then: function(e) {
                        a.listeners.push(e);
                    }
                };
                null !== Ja.T ? t(!0) : a.isTransition = !1, r(a), null === (t = n.pending) ? (a.next = n.pending = a, Nn(n, a)) : (a.next = t.next, n.pending = t.next = a);
            }
        }
        function Nn(e, n) {
            var t = n.action, r = n.payload, l = e.state;
            if (n.isTransition) {
                var a = Ja.T, o = {};
                Ja.T = o, Ja.T._updatedFibers = new Set;
                try {
                    var u = t(l, r), i = Ja.S;
                    null !== i && i(o, u), Ln(e, n, u);
                } catch (t) {
                    Dn(e, n, t);
                } finally{
                    Ja.T = a, null === a && o._updatedFibers && (e = o._updatedFibers.size, o._updatedFibers.clear(), 10 < e && console.warn("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."));
                }
            } else try {
                Ln(e, n, o = t(l, r));
            } catch (t) {
                Dn(e, n, t);
            }
        }
        function Ln(e, n, t) {
            null !== t && "object" == typeof t && "function" == typeof t.then ? (t.then(function(t) {
                Un(e, n, t);
            }, function(t) {
                return Dn(e, n, t);
            }), n.isTransition || console.error("An async function was passed to useActionState, but it was dispatched outside of an action context. This is likely not what you intended. Either pass the dispatch function to an `action` prop, or dispatch manually inside `startTransition`")) : Un(e, n, t);
        }
        function Un(e, n, t) {
            n.status = "fulfilled", n.value = t, In(n), e.state = t, null !== (n = e.pending) && ((t = n.next) === n ? e.pending = null : (t = t.next, n.next = t, Nn(e, t)));
        }
        function Dn(e, n, t) {
            var r = e.pending;
            if (e.pending = null, null !== r) {
                r = r.next;
                do {
                    n.status = "rejected", n.reason = t, In(n), n = n.next;
                }while (n !== r)
            }
            e.action = null;
        }
        function In(e) {
            e = e.listeners;
            for(var n = 0; n < e.length; n++)(0, e[n])();
        }
        function Fn(e, n) {
            return n;
        }
        function Mn(e, n) {
            var t, r, l;
            (t = on()).memoizedState = t.baseState = n, r = {
                pending: null,
                lanes: 0,
                dispatch: null,
                lastRenderedReducer: Fn,
                lastRenderedState: n
            }, t.queue = r, t = ht.bind(null, Ts, r), r.dispatch = t, r = zn(!1);
            var a = yt.bind(null, Ts, !1, r.queue);
            return l = {
                state: n,
                dispatch: null,
                action: e,
                pending: null
            }, (r = on()).queue = l, t = Tn.bind(null, Ts, l, a, t), l.dispatch = t, r.memoizedState = e, [
                n,
                t,
                !1
            ];
        }
        function Wn(e) {
            return Hn(un(), Ns, e);
        }
        function Hn(e, n, t) {
            n = hn(e, n, Fn)[0], e = mn(dn)[0], n = "object" == typeof n && null !== n && "function" == typeof n.then ? sn(n) : n;
            var r = un(), l = r.queue, a = l.dispatch;
            return t !== r.memoizedState && (Ts.flags |= 2048, Qn(ks | zs, jn.bind(null, l, t), {
                destroy: void 0
            }, null)), [
                n,
                a,
                e
            ];
        }
        function jn(e, n) {
            e.action = n;
        }
        function An(e) {
            var n = un(), t = Ns;
            if (null !== t) return Hn(n, t, e);
            un(), n = n.memoizedState;
            var r = (t = un()).queue.dispatch;
            return t.memoizedState = e, [
                n,
                r,
                !1
            ];
        }
        function Qn(e, n, t, r) {
            return e = {
                tag: e,
                create: n,
                inst: t,
                deps: r,
                next: null
            }, null === (n = Ts.updateQueue) && (n = Vs(), Ts.updateQueue = n), null === (t = n.lastEffect) ? n.lastEffect = e.next = e : (r = t.next, t.next = e, e.next = r, n.lastEffect = e), e;
        }
        function On(e) {
            return e = {
                current: e
            }, on().memoizedState = e;
        }
        function Bn(e, n, t, r) {
            var l = on();
            Ts.flags |= e, l.memoizedState = Qn(ks | n, t, {
                destroy: void 0
            }, void 0 === r ? null : r);
        }
        function Vn(e, n, t, r) {
            var l = un();
            r = void 0 === r ? null : r;
            var a = l.memoizedState.inst;
            null !== Ns && null !== r && Xe(r, Ns.memoizedState.deps) ? l.memoizedState = Qn(n, t, a, r) : (Ts.flags |= e, l.memoizedState = Qn(ks | n, t, a, r));
        }
        function $n(e, n) {
            (16 & Ts.mode) !== $u && (64 & Ts.mode) === $u ? Bn(142608384, zs, e, n) : Bn(8390656, zs, e, n);
        }
        function qn(e, n) {
            var t = 4194308;
            return (16 & Ts.mode) !== $u && (t |= 67108864), Bn(t, xs, e, n);
        }
        function Yn(e, n) {
            if ("function" == typeof n) {
                e = e();
                var t = n(e);
                return function() {
                    "function" == typeof t ? t() : n(null);
                };
            }
            if (null != n) return n.hasOwnProperty("current") || console.error("Expected useImperativeHandle() first argument to either be a ref callback or React.createRef() object. Instead received: %s.", "an object with keys {" + Object.keys(n).join(", ") + "}"), e = e(), n.current = e, function() {
                n.current = null;
            };
        }
        function Gn(e, n, t) {
            "function" != typeof n && console.error("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.", null !== n ? typeof n : "null"), t = null != t ? t.concat([
                e
            ]) : null;
            var r = 4194308;
            (16 & Ts.mode) !== $u && (r |= 67108864), Bn(r, xs, Yn.bind(null, n, e), t);
        }
        function Jn(e, n, t) {
            "function" != typeof n && console.error("Expected useImperativeHandle() second argument to be a function that creates a handle. Instead received: %s.", null !== n ? typeof n : "null"), t = null != t ? t.concat([
                e
            ]) : null, Vn(4, xs, Yn.bind(null, n, e), t);
        }
        function Kn(e, n) {
            return on().memoizedState = [
                e,
                void 0 === n ? null : n
            ], e;
        }
        function Xn(e, n) {
            var t = un();
            n = void 0 === n ? null : n;
            var r = t.memoizedState;
            return null !== n && Xe(n, r[1]) ? r[0] : (t.memoizedState = [
                e,
                n
            ], e);
        }
        function Zn(e, n) {
            var t = on();
            n = void 0 === n ? null : n;
            var r = e();
            if (Is) {
                U(!0);
                try {
                    e();
                } finally{
                    U(!1);
                }
            }
            return t.memoizedState = [
                r,
                n
            ], r;
        }
        function et(e, n) {
            var t = un();
            n = void 0 === n ? null : n;
            var r = t.memoizedState;
            if (null !== n && Xe(n, r[1])) return r[0];
            if (r = e(), Is) {
                U(!0);
                try {
                    e();
                } finally{
                    U(!1);
                }
            }
            return t.memoizedState = [
                r,
                n
            ], r;
        }
        function nt(e, n) {
            return lt(on(), e, n);
        }
        function tt(e, n) {
            return at(un(), Ns.memoizedState, e, n);
        }
        function rt(e, n) {
            var t = un();
            return null === Ns ? lt(t, e, n) : at(t, Ns.memoizedState, e, n);
        }
        function lt(e, n, t) {
            return void 0 === t || 0 != (1073741824 & Rs) ? e.memoizedState = n : (e.memoizedState = t, e = zl(), Ts.lanes |= e, _f |= e, t);
        }
        function at(e, n, t, r) {
            return Ru(t, n) ? t : null !== ps.current ? (e = lt(e, t, r), Ru(e, n) || (pc = !0), e) : 0 == (42 & Rs) ? (pc = !0, e.memoizedState = t) : (e = zl(), Ts.lanes |= e, _f |= e, n);
        }
        function ot(e, n, t, r, l) {
            var a = xo();
            wo(0 !== a && 8 > a ? a : 8);
            var o, u, i, s = Ja.T, c = {};
            Ja.T = c, yt(e, !1, n, t), c._updatedFibers = new Set;
            try {
                var f = l(), d = Ja.S;
                null !== d && d(c, f), null !== f && "object" == typeof f && "function" == typeof f.then ? gt(e, n, (o = r, u = [], i = {
                    status: "pending",
                    value: null,
                    reason: null,
                    then: function(e) {
                        u.push(e);
                    }
                }, f.then(function() {
                    i.status = "fulfilled", i.value = o;
                    for(var e = 0; e < u.length; e++)(0, u[e])(o);
                }, function(e) {
                    for(i.status = "rejected", i.reason = e, e = 0; e < u.length; e++)(0, u[e])(void 0);
                }), i), xl(e)) : gt(e, n, r, xl(e));
            } catch (t) {
                gt(e, n, {
                    then: function() {},
                    status: "rejected",
                    reason: t
                }, xl(e));
            } finally{
                wo(a), Ja.T = s, null === s && c._updatedFibers && (e = c._updatedFibers.size, c._updatedFibers.clear(), 10 < e && console.warn("Detected a large number of updates inside startTransition. If this is due to a subscription please re-write it to use React provided hooks. Otherwise concurrent mode guarantees are off the table."));
            }
        }
        function ut() {
            var e = zn(!1);
            return e = ot.bind(null, Ts, e.queue, !0, !1), on().memoizedState = e, [
                !1,
                e
            ];
        }
        function it() {
            var e = mn(dn)[0], n = un().memoizedState;
            return [
                "boolean" == typeof e ? e : sn(e),
                n
            ];
        }
        function st() {
            var e = gn(dn)[0], n = un().memoizedState;
            return [
                "boolean" == typeof e ? e : sn(e),
                n
            ];
        }
        function ct() {
            return sr(Uo);
        }
        function ft() {
            var e = on(), n = cf.identifierPrefix;
            return n = ":" + n + "r" + (Hs++).toString(32) + ":", e.memoizedState = n;
        }
        function dt() {
            return on().memoizedState = pt.bind(null, Ts);
        }
        function pt(e, n) {
            for(var t = e.return; null !== t;){
                switch(t.tag){
                    case 24:
                    case 3:
                        var r = xl(t), l = ye(t, e = ge(r), r);
                        return null !== l && (Cl(l, t, r), be(l, t, r)), t = dr(), null != n && null !== l && console.error("The seed argument is not enabled outside experimental channels."), void (e.payload = {
                            cache: t
                        });
                }
                t = t.return;
            }
        }
        function mt(e, n, t, r) {
            "function" == typeof r && console.error("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect()."), t = {
                lane: r = xl(e),
                revertLane: 0,
                action: t,
                hasEagerState: !1,
                eagerState: null,
                next: null
            }, bt(e) ? vt(n, t) : null !== (t = V(e, n, t, r)) && (Cl(t, e, r), St(t, n, r));
        }
        function ht(e, n, t, r) {
            "function" == typeof r && console.error("State updates from the useState() and useReducer() Hooks don't support the second callback argument. To execute a side effect after rendering, declare it in the component body with useEffect()."), gt(e, n, t, r = xl(e));
        }
        function gt(e, n, t, r) {
            var l = {
                lane: r,
                revertLane: 0,
                action: t,
                hasEagerState: !1,
                eagerState: null,
                next: null
            };
            if (bt(e)) vt(n, l);
            else {
                var a = e.alternate;
                if (0 === e.lanes && (null === a || 0 === a.lanes) && null !== (a = n.lastRenderedReducer)) {
                    var o = Ja.H;
                    Ja.H = Xs;
                    try {
                        var u = n.lastRenderedState, i = a(u, t);
                        if (l.hasEagerState = !0, l.eagerState = i, Ru(i, u)) return B(e, n, l, 0), null === cf && O(), !1;
                    } catch (e) {} finally{
                        Ja.H = o;
                    }
                }
                if (null !== (t = V(e, n, l, r))) return Cl(t, e, r), St(t, n, r), !0;
            }
            return !1;
        }
        function yt(e, n, t, r) {
            if (null === Ja.T && 0 === di && console.error("An optimistic state update occurred outside a transition or action. To fix, move the update to an action, or wrap with startTransition."), r = {
                lane: 2,
                revertLane: de(),
                action: r,
                hasEagerState: !1,
                eagerState: null,
                next: null
            }, bt(e)) {
                if (n) throw Error("Cannot update optimistic state while rendering.");
                console.error("Cannot call startTransition while rendering.");
            } else null !== (n = V(e, t, r, 2)) && Cl(n, e, 2);
        }
        function bt(e) {
            var n = e.alternate;
            return e === Ts || null !== n && n === Ts;
        }
        function vt(e, n) {
            Ds = Us = !0;
            var t = e.pending;
            null === t ? n.next = n : (n.next = t.next, t.next = n), e.pending = n;
        }
        function St(e, n, t) {
            if (0 != (4194176 & t)) {
                var r = n.lanes;
                t |= r &= e.pendingLanes, n.lanes = t, R(e, t);
            }
        }
        function kt(e) {
            if (null !== e && "function" != typeof e) {
                var n = String(e);
                cc.has(n) || (cc.add(n), console.error("Expected the last optional `callback` argument to be a function. Instead received: %s.", e));
            }
        }
        function wt(e, n, t, r) {
            var l = e.memoizedState, a = t(r, l);
            if (8 & e.mode) {
                U(!0);
                try {
                    a = t(r, l);
                } finally{
                    U(!1);
                }
            }
            void 0 === a && (n = s(n) || "Component", oc.has(n) || (oc.add(n), console.error("%s.getDerivedStateFromProps(): A valid state object (or null) must be returned. You have returned undefined.", n))), l = null == a ? l : Na({}, l, a), e.memoizedState = l, 0 === e.lanes && (e.updateQueue.baseState = l);
        }
        function xt(e, n, t, r, l, a, o) {
            var u = e.stateNode;
            if ("function" == typeof u.shouldComponentUpdate) {
                if (t = u.shouldComponentUpdate(r, a, o), 8 & e.mode) {
                    U(!0);
                    try {
                        t = u.shouldComponentUpdate(r, a, o);
                    } finally{
                        U(!1);
                    }
                }
                return void 0 === t && console.error("%s.shouldComponentUpdate(): Returned undefined instead of a boolean value. Make sure to return true or false.", s(n) || "Component"), t;
            }
            return !(n.prototype && n.prototype.isPureReactComponent && Ce(t, r) && Ce(l, a));
        }
        function zt(e, n, t, r) {
            var l = n.state;
            "function" == typeof n.componentWillReceiveProps && n.componentWillReceiveProps(t, r), "function" == typeof n.UNSAFE_componentWillReceiveProps && n.UNSAFE_componentWillReceiveProps(t, r), n.state !== l && (e = c(e) || "Component", nc.has(e) || (nc.add(e), console.error("%s.componentWillReceiveProps(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.", e)), fc.enqueueReplaceState(n, n.state, null));
        }
        function Ct(e, n) {
            var t = n;
            if ("ref" in n) for(var r in t = {}, n)"ref" !== r && (t[r] = n[r]);
            if (e = e.defaultProps) for(var l in t === n && (t = Na({}, t)), e)void 0 === t[l] && (t[l] = e[l]);
            return t;
        }
        function Et(e, n) {
            try {
                n.source && c(n.source);
                var t = n.value;
                null !== Ja.actQueue ? Ja.thrownErrors.push(t) : (0, e.onUncaughtError)(t, {
                    componentStack: n.stack
                });
            } catch (e) {
                setTimeout(function() {
                    throw e;
                });
            }
        }
        function Pt(e, n, t) {
            try {
                t.source && c(t.source), c(n), (0, e.onCaughtError)(t.value, {
                    componentStack: t.stack,
                    errorBoundary: 1 === n.tag ? n.stateNode : null
                });
            } catch (e) {
                setTimeout(function() {
                    throw e;
                });
            }
        }
        function _t(e, n, t) {
            return (t = ge(t)).tag = yi, t.payload = {
                element: null
            }, t.callback = function() {
                y(n.source, Et, e, n);
            }, t;
        }
        function Rt(e) {
            return (e = ge(e)).tag = yi, e;
        }
        function Tt(e, n, t, r) {
            var l = t.type.getDerivedStateFromError;
            if ("function" == typeof l) {
                var a = r.value;
                e.payload = function() {
                    return l(a);
                }, e.callback = function() {
                    y(r.source, Pt, n, t, r);
                };
            }
            var o = t.stateNode;
            null !== o && "function" == typeof o.componentDidCatch && (e.callback = function() {
                y(r.source, Pt, n, t, r), "function" != typeof l && (null === Af ? Af = new Set([
                    this
                ]) : Af.add(this)), Yi(this, r), "function" == typeof l || 0 == (2 & t.lanes) && console.error("%s: Error boundaries should implement getDerivedStateFromError(). In that method, return a state update to display an error message or fallback UI.", c(t) || "Unknown");
            });
        }
        function Nt(e, n, t, r) {
            n.child = null === e ? ds(n, null, t, r) : fs(n, e.child, t, r);
        }
        function Lt(e, n, t, r, l) {
            t = t.render;
            var a = n.ref;
            if ("ref" in r) {
                var o = {};
                for(var u in r)"ref" !== u && (o[u] = r[u]);
            } else o = r;
            return ir(n), r = Ze(e, n, t, o, a, l), u = rn(), null === e || pc ? (n.flags |= 1, Nt(e, n, r, l), n.child) : (ln(e, n, l), Xt(e, n, l));
        }
        function Ut(e, n, t, r, l) {
            if (null === e) {
                var a = t.type;
                return "function" != typeof a || ca(a) || void 0 !== a.defaultProps || null !== t.compare ? ((e = pa(t.type, null, r, n, n.mode, l)).ref = n.ref, e.return = n, n.child = e) : (t = a, n.tag = 15, n.type = t, At(n, a), Dt(e, n, t, r, l));
            }
            if (a = e.child, !Zt(e, l)) {
                var o = a.memoizedProps;
                if ((t = null !== (t = t.compare) ? t : Ce)(o, r) && e.ref === n.ref) return Xt(e, n, l);
            }
            return n.flags |= 1, (e = fa(a, r)).ref = n.ref, e.return = n, n.child = e;
        }
        function Dt(e, n, t, r, l) {
            if (null !== e) {
                var a = e.memoizedProps;
                if (Ce(a, r) && e.ref === n.ref && n.type === e.type) {
                    if (pc = !1, n.pendingProps = r = a, !Zt(e, l)) return n.lanes = e.lanes, Xt(e, n, l);
                    0 != (131072 & e.flags) && (pc = !0);
                }
            }
            return Wt(e, n, t, r, l);
        }
        function It(e, n, t) {
            var r = n.pendingProps, l = r.children, a = 0 != (2 & n.stateNode._pendingVisibility), o = null !== e ? e.memoizedState : null;
            if (Mt(e, n), "hidden" === r.mode || a) {
                if (0 != (128 & n.flags)) {
                    if (r = null !== o ? o.baseLanes | t : t, null !== e) {
                        for(l = n.child = e.child, a = 0; null !== l;)a = a | l.lanes | l.childLanes, l = l.sibling;
                        n.childLanes = a & ~r;
                    } else n.childLanes = 0, n.child = null;
                    return Ft(e, n, r, t);
                }
                if (0 == (536870912 & t)) return n.lanes = n.childLanes = 536870912, Ft(e, n, null !== o ? o.baseLanes | t : t, t);
                n.memoizedState = {
                    baseLanes: 0,
                    cachePool: null
                }, null !== e && gr(n, null !== o ? o.cachePool : null), null !== o ? He(n, o) : je(n), Oe(n);
            } else null !== o ? (gr(n, o.cachePool), He(n, o), Be(n), n.memoizedState = null) : (null !== e && gr(n, null), je(n), Be(n));
            return Nt(e, n, l, t), n.child;
        }
        function Ft(e, n, t, r) {
            var l = hr();
            return l = null === l ? null : {
                parent: Uc._currentValue2,
                pool: l
            }, n.memoizedState = {
                baseLanes: t,
                cachePool: l
            }, null !== e && gr(n, null), je(n), Oe(n), null !== e && or(e, n, r, !0), null;
        }
        function Mt(e, n) {
            var t = n.ref;
            if (null === t) null !== e && null !== e.ref && (n.flags |= 2097664);
            else {
                if ("function" != typeof t && "object" != typeof t) throw Error("Expected ref to be a function, an object returned by React.createRef(), or undefined/null.");
                null !== e && e.ref === t || (n.flags |= 2097664);
            }
        }
        function Wt(e, n, t, r, l) {
            if (t.prototype && "function" == typeof t.prototype.render) {
                var a = s(t) || "Unknown";
                mc[a] || (console.error("The <%s /> component appears to have a render method, but doesn't extend React.Component. This is likely to cause errors. Change %s to extend React.Component instead.", a, a), mc[a] = !0);
            }
            return 8 & n.mode && xi.recordLegacyContextWarning(n, null), null === e && (At(n, n.type), t.contextTypes && (a = s(t) || "Unknown", gc[a] || (gc[a] = !0, console.error("%s uses the legacy contextTypes API which was removed in React 19. Use React.createContext() with React.useContext() instead. (https://react.dev/link/legacy-context)", a)))), ir(n), t = Ze(e, n, t, r, void 0, l), r = rn(), null === e || pc ? (n.flags |= 1, Nt(e, n, t, l), n.child) : (ln(e, n, l), Xt(e, n, l));
        }
        function Ht(e, n, t, r, l, a) {
            return ir(n), Os = -1, Bs = null !== e && e.type !== n.type, n.updateQueue = null, t = nn(n, r, t, l), en(e, n), r = rn(), null === e || pc ? (n.flags |= 1, Nt(e, n, t, a), n.child) : (ln(e, n, a), Xt(e, n, a));
        }
        function jt(e, n, t, r, l) {
            var a, o, u;
            if (ir(n), null === n.stateNode) {
                if (o = su, a = t.contextType, "contextType" in t && null !== a && (void 0 === a || a.$$typeof !== ja) && !sc.has(t) && (sc.add(t), u = void 0 === a ? " However, it is set to undefined. This can be caused by a typo or by mixing up named and default imports. This can also happen due to a circular dependency, so try moving the createContext() call to a separate file." : "object" != typeof a ? " However, it is set to a " + typeof a + "." : a.$$typeof === Ha ? " Did you accidentally pass the Context.Consumer instead?" : " However, it is set to an object with keys {" + Object.keys(a).join(", ") + "}.", console.error("%s defines an invalid contextType. contextType should point to the Context object returned by React.createContext().%s", s(t) || "Component", u)), "object" == typeof a && null !== a && (o = sr(a)), a = new t(r, o), 8 & n.mode) {
                    U(!0);
                    try {
                        a = new t(r, o);
                    } finally{
                        U(!1);
                    }
                }
                if (o = n.memoizedState = null !== a.state && void 0 !== a.state ? a.state : null, a.updater = fc, n.stateNode = a, a._reactInternals = n, a._reactInternalInstance = ec, "function" == typeof t.getDerivedStateFromProps && null === o && (o = s(t) || "Component", tc.has(o) || (tc.add(o), console.error("`%s` uses `getDerivedStateFromProps` but its initial state is %s. This is not recommended. Instead, define the initial state by assigning an object to `this.state` in the constructor of `%s`. This ensures that `getDerivedStateFromProps` arguments have a consistent shape.", o, null === a.state ? "null" : "undefined", o))), "function" == typeof t.getDerivedStateFromProps || "function" == typeof a.getSnapshotBeforeUpdate) {
                    var i = u = o = null;
                    if ("function" == typeof a.componentWillMount && !0 !== a.componentWillMount.__suppressDeprecationWarning ? o = "componentWillMount" : "function" == typeof a.UNSAFE_componentWillMount && (o = "UNSAFE_componentWillMount"), "function" == typeof a.componentWillReceiveProps && !0 !== a.componentWillReceiveProps.__suppressDeprecationWarning ? u = "componentWillReceiveProps" : "function" == typeof a.UNSAFE_componentWillReceiveProps && (u = "UNSAFE_componentWillReceiveProps"), "function" == typeof a.componentWillUpdate && !0 !== a.componentWillUpdate.__suppressDeprecationWarning ? i = "componentWillUpdate" : "function" == typeof a.UNSAFE_componentWillUpdate && (i = "UNSAFE_componentWillUpdate"), null !== o || null !== u || null !== i) {
                        a = s(t) || "Component";
                        var f = "function" == typeof t.getDerivedStateFromProps ? "getDerivedStateFromProps()" : "getSnapshotBeforeUpdate()";
                        lc.has(a) || (lc.add(a), console.error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n%s uses %s but also contains the following legacy lifecycles:%s%s%s\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://react.dev/link/unsafe-component-lifecycles", a, f, null !== o ? "\n  " + o : "", null !== u ? "\n  " + u : "", null !== i ? "\n  " + i : ""));
                    }
                }
                a = n.stateNode, o = s(t) || "Component", a.render || (t.prototype && "function" == typeof t.prototype.render ? console.error("No `render` method found on the %s instance: did you accidentally return an object from the constructor?", o) : console.error("No `render` method found on the %s instance: you may have forgotten to define `render`.", o)), !a.getInitialState || a.getInitialState.isReactClassApproved || a.state || console.error("getInitialState was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Did you mean to define a state property instead?", o), a.getDefaultProps && !a.getDefaultProps.isReactClassApproved && console.error("getDefaultProps was defined on %s, a plain JavaScript class. This is only supported for classes created using React.createClass. Use a static property to define defaultProps instead.", o), a.contextType && console.error("contextType was defined as an instance property on %s. Use a static property to define contextType instead.", o), t.childContextTypes && !ic.has(t) && (ic.add(t), console.error("%s uses the legacy childContextTypes API which was removed in React 19. Use React.createContext() instead. (https://react.dev/link/legacy-context)", o)), t.contextTypes && !uc.has(t) && (uc.add(t), console.error("%s uses the legacy contextTypes API which was removed in React 19. Use React.createContext() with static contextType instead. (https://react.dev/link/legacy-context)", o)), "function" == typeof a.componentShouldUpdate && console.error("%s has a method called componentShouldUpdate(). Did you mean shouldComponentUpdate()? The name is phrased as a question because the function is expected to return a value.", o), t.prototype && t.prototype.isPureReactComponent && void 0 !== a.shouldComponentUpdate && console.error("%s has a method called shouldComponentUpdate(). shouldComponentUpdate should not be used when extending React.PureComponent. Please extend React.Component if shouldComponentUpdate is used.", s(t) || "A pure component"), "function" == typeof a.componentDidUnmount && console.error("%s has a method called componentDidUnmount(). But there is no such lifecycle method. Did you mean componentWillUnmount()?", o), "function" == typeof a.componentDidReceiveProps && console.error("%s has a method called componentDidReceiveProps(). But there is no such lifecycle method. If you meant to update the state in response to changing props, use componentWillReceiveProps(). If you meant to fetch data or run side-effects or mutations after React has updated the UI, use componentDidUpdate().", o), "function" == typeof a.componentWillRecieveProps && console.error("%s has a method called componentWillRecieveProps(). Did you mean componentWillReceiveProps()?", o), "function" == typeof a.UNSAFE_componentWillRecieveProps && console.error("%s has a method called UNSAFE_componentWillRecieveProps(). Did you mean UNSAFE_componentWillReceiveProps()?", o), u = a.props !== r, void 0 !== a.props && u && console.error("When calling super() in `%s`, make sure to pass up the same props that your component's constructor was passed.", o), a.defaultProps && console.error("Setting defaultProps as an instance property on %s is not supported and will be ignored. Instead, define defaultProps as a static property on %s.", o, o), "function" != typeof a.getSnapshotBeforeUpdate || "function" == typeof a.componentDidUpdate || rc.has(t) || (rc.add(t), console.error("%s: getSnapshotBeforeUpdate() should be used with componentDidUpdate(). This component defines getSnapshotBeforeUpdate() only.", s(t))), "function" == typeof a.getDerivedStateFromProps && console.error("%s: getDerivedStateFromProps() is defined as an instance method and will be ignored. Instead, declare it as a static method.", o), "function" == typeof a.getDerivedStateFromError && console.error("%s: getDerivedStateFromError() is defined as an instance method and will be ignored. Instead, declare it as a static method.", o), "function" == typeof t.getSnapshotBeforeUpdate && console.error("%s: getSnapshotBeforeUpdate() is defined as a static method and will be ignored. Instead, declare it as an instance method.", o), (u = a.state) && ("object" != typeof u || lo(u)) && console.error("%s.state: must be set to an object or null", o), "function" == typeof a.getChildContext && "object" != typeof t.childContextTypes && console.error("%s.getChildContext(): childContextTypes must be defined in order to use getChildContext().", o), (a = n.stateNode).props = r, a.state = n.memoizedState, a.refs = {}, me(n), o = t.contextType, a.context = "object" == typeof o && null !== o ? sr(o) : su, a.state === r && (o = s(t) || "Component", ac.has(o) || (ac.add(o), console.error("%s: It is not recommended to assign props directly to state because updates to props won't be reflected in state. In most cases, it is better to use props directly.", o))), 8 & n.mode && xi.recordLegacyContextWarning(n, a), xi.recordUnsafeLifecycleWarnings(n, a), a.state = n.memoizedState, "function" == typeof (o = t.getDerivedStateFromProps) && (wt(n, t, o, r), a.state = n.memoizedState), "function" == typeof t.getDerivedStateFromProps || "function" == typeof a.getSnapshotBeforeUpdate || "function" != typeof a.UNSAFE_componentWillMount && "function" != typeof a.componentWillMount || (o = a.state, "function" == typeof a.componentWillMount && a.componentWillMount(), "function" == typeof a.UNSAFE_componentWillMount && a.UNSAFE_componentWillMount(), o !== a.state && (console.error("%s.componentWillMount(): Assigning directly to this.state is deprecated (except inside a component's constructor). Use setState instead.", c(n) || "Component"), fc.enqueueReplaceState(a, a.state, null)), ke(n, r, a, l), Se(), a.state = n.memoizedState), "function" == typeof a.componentDidMount && (n.flags |= 4194308), (16 & n.mode) !== $u && (n.flags |= 67108864), a = !0;
            } else if (null === e) {
                a = n.stateNode;
                var d = n.memoizedProps;
                u = Ct(t, d), a.props = u;
                var p = a.context;
                i = t.contextType, o = su, "object" == typeof i && null !== i && (o = sr(i)), i = "function" == typeof (f = t.getDerivedStateFromProps) || "function" == typeof a.getSnapshotBeforeUpdate, d = n.pendingProps !== d, i || "function" != typeof a.UNSAFE_componentWillReceiveProps && "function" != typeof a.componentWillReceiveProps || (d || p !== o) && zt(n, a, r, o), bi = !1;
                var m = n.memoizedState;
                a.state = m, ke(n, r, a, l), Se(), p = n.memoizedState, d || m !== p || bi ? ("function" == typeof f && (wt(n, t, f, r), p = n.memoizedState), (u = bi || xt(n, t, u, r, m, p, o)) ? (i || "function" != typeof a.UNSAFE_componentWillMount && "function" != typeof a.componentWillMount || ("function" == typeof a.componentWillMount && a.componentWillMount(), "function" == typeof a.UNSAFE_componentWillMount && a.UNSAFE_componentWillMount()), "function" == typeof a.componentDidMount && (n.flags |= 4194308), (16 & n.mode) !== $u && (n.flags |= 67108864)) : ("function" == typeof a.componentDidMount && (n.flags |= 4194308), (16 & n.mode) !== $u && (n.flags |= 67108864), n.memoizedProps = r, n.memoizedState = p), a.props = r, a.state = p, a.context = o, a = u) : ("function" == typeof a.componentDidMount && (n.flags |= 4194308), (16 & n.mode) !== $u && (n.flags |= 67108864), a = !1);
            } else {
                a = n.stateNode, he(e, n), i = Ct(t, o = n.memoizedProps), a.props = i, f = n.pendingProps, m = a.context, p = t.contextType, u = su, "object" == typeof p && null !== p && (u = sr(p)), (p = "function" == typeof (d = t.getDerivedStateFromProps) || "function" == typeof a.getSnapshotBeforeUpdate) || "function" != typeof a.UNSAFE_componentWillReceiveProps && "function" != typeof a.componentWillReceiveProps || (o !== f || m !== u) && zt(n, a, r, u), bi = !1, m = n.memoizedState, a.state = m, ke(n, r, a, l), Se();
                var h = n.memoizedState;
                o !== f || m !== h || bi || null !== e && null !== e.dependencies && ur(e.dependencies) ? ("function" == typeof d && (wt(n, t, d, r), h = n.memoizedState), (i = bi || xt(n, t, i, r, m, h, u) || null !== e && null !== e.dependencies && ur(e.dependencies)) ? (p || "function" != typeof a.UNSAFE_componentWillUpdate && "function" != typeof a.componentWillUpdate || ("function" == typeof a.componentWillUpdate && a.componentWillUpdate(r, h, u), "function" == typeof a.UNSAFE_componentWillUpdate && a.UNSAFE_componentWillUpdate(r, h, u)), "function" == typeof a.componentDidUpdate && (n.flags |= 4), "function" == typeof a.getSnapshotBeforeUpdate && (n.flags |= 1024)) : ("function" != typeof a.componentDidUpdate || o === e.memoizedProps && m === e.memoizedState || (n.flags |= 4), "function" != typeof a.getSnapshotBeforeUpdate || o === e.memoizedProps && m === e.memoizedState || (n.flags |= 1024), n.memoizedProps = r, n.memoizedState = h), a.props = r, a.state = h, a.context = u, a = i) : ("function" != typeof a.componentDidUpdate || o === e.memoizedProps && m === e.memoizedState || (n.flags |= 4), "function" != typeof a.getSnapshotBeforeUpdate || o === e.memoizedProps && m === e.memoizedState || (n.flags |= 1024), a = !1);
            }
            if (u = a, Mt(e, n), o = 0 != (128 & n.flags), u || o) {
                if (u = n.stateNode, Ja.getCurrentStack = null === n ? null : g, ro = !1, to = n, o && "function" != typeof t.getDerivedStateFromError) t = null, Xu = -1;
                else if (t = Qi(u), 8 & n.mode) {
                    U(!0);
                    try {
                        Qi(u);
                    } finally{
                        U(!1);
                    }
                }
                n.flags |= 1, null !== e && o ? (n.child = fs(n, e.child, null, l), n.child = fs(n, null, t, l)) : Nt(e, n, t, l), n.memoizedState = u.state, e = n.child;
            } else e = Xt(e, n, l);
            return l = n.stateNode, a && l.props !== r && (bc || console.error("It looks like %s is reassigning its own `this.props` while rendering. This is not supported and can lead to confusing bugs.", c(n) || "a component"), bc = !0), e;
        }
        function At(e, n) {
            n && n.childContextTypes && console.error("childContextTypes cannot be defined on a function component.\n  %s.childContextTypes = ...", n.displayName || n.name || "Component"), "function" == typeof n.getDerivedStateFromProps && (e = s(n) || "Unknown", yc[e] || (console.error("%s: Function components do not support getDerivedStateFromProps.", e), yc[e] = !0)), "object" == typeof n.contextType && null !== n.contextType && (n = s(n) || "Unknown", hc[n] || (console.error("%s: Function components do not support contextType.", n), hc[n] = !0));
        }
        function Qt(e) {
            return {
                baseLanes: e,
                cachePool: yr()
            };
        }
        function Ot(e, n, t) {
            return e = null !== e ? e.childLanes & ~t : 0, n && (e |= Nf), e;
        }
        function Bt(e, n, t) {
            var r, l, a, o, u = n.pendingProps, i = !1, s = 0 != (128 & n.flags);
            if ((r = s) || (r = (null === e || null !== e.memoizedState) && 0 != (vs.current & bs)), r && (i = !0, n.flags &= -129), r = 0 != (32 & n.flags), n.flags &= -33, null === e) return l = u.children, u = u.fallback, i ? (Be(n), l = $t({
                mode: "hidden",
                children: l
            }, i = n.mode), u = ha(u, i, t, null), l.return = n, u.return = n, l.sibling = u, n.child = l, (i = n.child).memoizedState = Qt(t), i.childLanes = Ot(e, r, t), n.memoizedState = kc, u) : (Qe(n), Vt(n, l));
            if (null !== (a = e.memoizedState) && null !== (l = a.dehydrated)) {
                if (s) 256 & n.flags ? (Qe(n), n.flags &= -257, n = qt(e, n, t)) : null !== n.memoizedState ? (Be(n), n.child = e.child, n.flags |= 128, n = null) : (Be(n), i = u.fallback, l = n.mode, u = $t({
                    mode: "visible",
                    children: u.children
                }, l), (i = ha(i, l, t, null)).flags |= 2, u.return = n, i.return = n, u.sibling = i, n.child = u, fs(n, e.child, null, t), (u = n.child).memoizedState = Qt(t), u.childLanes = Ot(e, r, t), n.memoizedState = kc, n = i);
                else if (Qe(n), Ko(l)) r = (l = Xo(l)).digest, i = l.message, u = l.stack, l = l.componentStack, (i = i ? Error(i) : Error("The server could not finish this Suspense boundary, likely due to an error during server rendering. Switched to client rendering.")).stack = u || "", i.digest = r, u = {
                    value: i,
                    source: null,
                    stack: r = void 0 === l ? null : l
                }, "string" == typeof r && Tu.set(i, u), o = u, null === Vu ? Vu = [
                    o
                ] : Vu.push(o), n = qt(e, n, t);
                else if (pc || or(e, n, t, !1), r = 0 != (t & e.childLanes), pc || r) {
                    if (null !== (r = cf)) {
                        if (0 != (42 & (u = t & -t))) u = 1;
                        else switch(u){
                            case 2:
                                u = 1;
                                break;
                            case 8:
                                u = 4;
                                break;
                            case 32:
                                u = 16;
                                break;
                            case 128:
                            case 256:
                            case 512:
                            case 1024:
                            case 2048:
                            case 4096:
                            case 8192:
                            case 16384:
                            case 32768:
                            case 65536:
                            case 131072:
                            case 262144:
                            case 524288:
                            case 1048576:
                            case 2097152:
                            case 4194304:
                            case 8388608:
                            case 16777216:
                            case 33554432:
                                u = 64;
                                break;
                            case 268435456:
                                u = 134217728;
                                break;
                            default:
                                u = 0;
                        }
                        if (0 !== (u = 0 != (u & (r.suspendedLanes | t)) ? 0 : u) && u !== a.retryLane) throw a.retryLane = u, $(e, u), Cl(r, e, u), dc;
                    }
                    Jo(l) || Ml(), n = qt(e, n, t);
                } else Jo(l) ? (n.flags |= 128, n.child = e.child, n = ta.bind(null, e), Zo(l, n), n = null) : (e = a.treeContext, (n = Vt(n, u.children)).flags |= 4096);
                return n;
            }
            return i ? (Be(n), i = u.fallback, l = n.mode, s = (a = e.child).sibling, (u = fa(a, {
                mode: "hidden",
                children: u.children
            })).subtreeFlags = 31457280 & a.subtreeFlags, null !== s ? i = fa(s, i) : (i = ha(i, l, t, null)).flags |= 2, i.return = n, u.return = n, u.sibling = i, n.child = u, u = i, i = n.child, null === (l = e.child.memoizedState) ? l = Qt(t) : (null !== (a = l.cachePool) ? (s = Uc._currentValue2, a = a.parent !== s ? {
                parent: s,
                pool: s
            } : a) : a = yr(), l = {
                baseLanes: l.baseLanes | t,
                cachePool: a
            }), i.memoizedState = l, i.childLanes = Ot(e, r, t), n.memoizedState = kc, u) : (Qe(n), e = (t = e.child).sibling, (t = fa(t, {
                mode: "visible",
                children: u.children
            })).return = n, t.sibling = null, null !== e && (null === (r = n.deletions) ? (n.deletions = [
                e
            ], n.flags |= 16) : r.push(e)), n.child = t, n.memoizedState = null, t);
        }
        function Vt(e, n) {
            return (n = $t({
                mode: "visible",
                children: n
            }, e.mode)).return = e, e.child = n;
        }
        function $t(e, n) {
            return ga(e, n, 0, null);
        }
        function qt(e, n, t) {
            return fs(n, e.child, null, t), (e = Vt(n, n.pendingProps.children)).flags |= 2, n.memoizedState = null, e;
        }
        function Yt(e, n, t) {
            e.lanes |= n;
            var r = e.alternate;
            null !== r && (r.lanes |= n), lr(e.return, n, t);
        }
        function Gt(e, n) {
            var t = lo(e);
            return e = !t && "function" == typeof i(e), !t && !e || (t = t ? "array" : "iterable", console.error("A nested %s was passed to row #%s in <SuspenseList />. Wrap it in an additional SuspenseList to configure its revealOrder: <SuspenseList revealOrder=...> ... <SuspenseList revealOrder=...>{%s}</SuspenseList> ... </SuspenseList>", t, n, t), !1);
        }
        function Jt(e, n, t, r, l) {
            var a = e.memoizedState;
            null === a ? e.memoizedState = {
                isBackwards: n,
                rendering: null,
                renderingStartTime: 0,
                last: r,
                tail: t,
                tailMode: l
            } : (a.isBackwards = n, a.rendering = null, a.renderingStartTime = 0, a.last = r, a.tail = t, a.tailMode = l);
        }
        function Kt(e, n, t) {
            var r = n.pendingProps, l = r.revealOrder, a = r.tail;
            if (r = r.children, void 0 !== l && "forwards" !== l && "backwards" !== l && "together" !== l && !vc[l]) if (vc[l] = !0, "string" == typeof l) switch(l.toLowerCase()){
                case "together":
                case "forwards":
                case "backwards":
                    console.error('"%s" is not a valid value for revealOrder on <SuspenseList />. Use lowercase "%s" instead.', l, l.toLowerCase());
                    break;
                case "forward":
                case "backward":
                    console.error('"%s" is not a valid value for revealOrder on <SuspenseList />. React uses the -s suffix in the spelling. Use "%ss" instead.', l, l.toLowerCase());
                    break;
                default:
                    console.error('"%s" is not a supported revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?', l);
            }
            else console.error('%s is not a supported value for revealOrder on <SuspenseList />. Did you mean "together", "forwards" or "backwards"?', l);
            void 0 === a || Sc[a] || ("collapsed" !== a && "hidden" !== a ? (Sc[a] = !0, console.error('"%s" is not a supported value for tail on <SuspenseList />. Did you mean "collapsed" or "hidden"?', a)) : "forwards" !== l && "backwards" !== l && (Sc[a] = !0, console.error('<SuspenseList tail="%s" /> is only valid if revealOrder is "forwards" or "backwards". Did you mean to specify revealOrder="forwards"?', a)));
            e: if (("forwards" === l || "backwards" === l) && null != r && !1 !== r) if (lo(r)) {
                for(var o = 0; o < r.length; o++)if (!Gt(r[o], o)) break e;
            } else if (o = i(r), "function" == typeof o) {
                if (o = o.call(r)) for(var u = o.next(), s = 0; !u.done; u = o.next()){
                    if (!Gt(u.value, s)) break e;
                    s++;
                }
            } else console.error('A single row was passed to a <SuspenseList revealOrder="%s" />. This is not useful since it needs multiple rows. Did you mean to pass multiple children or an array?', l);
            if (Nt(e, n, r, t), 0 != ((r = vs.current) & bs)) r = r & ys | bs, n.flags |= 128;
            else {
                if (null !== e && 0 != (128 & e.flags)) e: for(e = n.child; null !== e;){
                    if (13 === e.tag) null !== e.memoizedState && Yt(e, t, n);
                    else if (19 === e.tag) Yt(e, t, n);
                    else if (null !== e.child) {
                        e.child.return = e, e = e.child;
                        continue;
                    }
                    if (e === n) break e;
                    for(; null === e.sibling;){
                        if (null === e.return || e.return === n) break e;
                        e = e.return;
                    }
                    e.sibling.return = e.return, e = e.sibling;
                }
                r &= ys;
            }
            switch(S(vs, r, n), l){
                case "forwards":
                    for(t = n.child, l = null; null !== t;)null !== (e = t.alternate) && null === $e(e) && (l = t), t = t.sibling;
                    null === (t = l) ? (l = n.child, n.child = null) : (l = t.sibling, t.sibling = null), Jt(n, !1, l, t, a);
                    break;
                case "backwards":
                    for(t = null, l = n.child, n.child = null; null !== l;){
                        if (null !== (e = l.alternate) && null === $e(e)) {
                            n.child = l;
                            break;
                        }
                        e = l.sibling, l.sibling = t, t = l, l = e;
                    }
                    Jt(n, !0, t, null, a);
                    break;
                case "together":
                    Jt(n, !1, null, null, void 0);
                    break;
                default:
                    n.memoizedState = null;
            }
            return n.child;
        }
        function Xt(e, n, t) {
            if (null !== e && (n.dependencies = e.dependencies), Xu = -1, _f |= n.lanes, 0 == (t & n.childLanes)) {
                if (null === e) return null;
                if (or(e, n, t, !1), 0 == (t & n.childLanes)) return null;
            }
            if (null !== e && n.child !== e.child) throw Error("Resuming work not yet implemented.");
            if (null !== n.child) {
                for(t = fa(e = n.child, e.pendingProps), n.child = t, t.return = n; null !== e.sibling;)e = e.sibling, (t = t.sibling = fa(e, e.pendingProps)).return = n;
                t.sibling = null;
            }
            return n.child;
        }
        function Zt(e, n) {
            return 0 != (e.lanes & n) || !(null === (e = e.dependencies) || !ur(e));
        }
        function er(e, n, t) {
            if (n._debugNeedsRemount && null !== e) {
                t = pa(n.type, n.key, n.pendingProps, n._debugOwner || null, n.mode, n.lanes);
                var r = n.return;
                if (null === r) throw Error("Cannot swap the root fiber.");
                if (e.alternate = null, n.alternate = null, t.index = n.index, t.sibling = n.sibling, t.return = n.return, t.ref = n.ref, t._debugInfo = n._debugInfo, n === r.child) r.child = t;
                else {
                    var l = r.child;
                    if (null === l) throw Error("Expected parent to have a child.");
                    for(; l.sibling !== n;)if (null === (l = l.sibling)) throw Error("Expected to find the previous sibling.");
                    l.sibling = t;
                }
                return null === (n = r.deletions) ? (r.deletions = [
                    e
                ], r.flags |= 16) : n.push(e), t.flags |= 2, t;
            }
            if (null !== e) if (e.memoizedProps !== n.pendingProps || n.type !== e.type) pc = !0;
            else {
                if (!Zt(e, t) && 0 == (128 & n.flags)) return pc = !1, function(e, n, t) {
                    switch(n.tag){
                        case 3:
                            W(n, n.stateNode.containerInfo), tr(n, Uc, e.memoizedState.cache);
                            break;
                        case 27:
                        case 5:
                            A(n);
                            break;
                        case 4:
                            W(n, n.stateNode.containerInfo);
                            break;
                        case 10:
                            tr(n, n.type, n.memoizedProps.value);
                            break;
                        case 12:
                            0 != (t & n.childLanes) && (n.flags |= 4), n.flags |= 2048;
                            var r = n.stateNode;
                            r.effectDuration = -0, r.passiveEffectDuration = -0;
                            break;
                        case 13:
                            if (null !== (r = n.memoizedState)) return null !== r.dehydrated ? (Qe(n), n.flags |= 128, null) : 0 != (t & n.child.childLanes) ? Bt(e, n, t) : (Qe(n), null !== (e = Xt(e, n, t)) ? e.sibling : null);
                            Qe(n);
                            break;
                        case 19:
                            var l = 0 != (128 & e.flags);
                            if ((r = 0 != (t & n.childLanes)) || (or(e, n, t, !1), r = 0 != (t & n.childLanes)), l) {
                                if (r) return Kt(e, n, t);
                                n.flags |= 128;
                            }
                            if (null !== (l = n.memoizedState) && (l.rendering = null, l.tail = null, l.lastEffect = null), S(vs, vs.current, n), r) break;
                            return null;
                        case 22:
                        case 23:
                            return n.lanes = 0, It(e, n, t);
                        case 24:
                            tr(n, Uc, e.memoizedState.cache);
                    }
                    return Xt(e, n, t);
                }(e, n, t);
                pc = 0 != (131072 & e.flags);
            }
            else pc = !1, (r = Bu) && (F(), r = 0 != (1048576 & n.flags)), r && (r = n.index, F(), function(e, n, t) {
                F(), Iu[Fu++] = Wu, Iu[Fu++] = Hu, Iu[Fu++] = Mu, Mu = e;
                var r = Wu;
                e = Hu;
                var l = 32 - cu(r) - 1;
                r &= ~(1 << l), t += 1;
                var a = 32 - cu(n) + l;
                if (30 < a) {
                    var o = l - l % 5;
                    a = (r & (1 << o) - 1).toString(32), r >>= o, l -= o, Wu = 1 << 32 - cu(n) + l | t << l | r, Hu = a + e;
                } else Wu = 1 << a | t << l | r, Hu = e;
            }(n, Du, r));
            switch(n.lanes = 0, n.tag){
                case 16:
                    e: {
                        if (r = n.pendingProps, e = ts(n.elementType), n.type = e, "function" != typeof e) {
                            if (null != e) {
                                if ((l = e.$$typeof) === Aa) {
                                    n.tag = 11, n.type = e, n = Lt(null, n, e, r, t);
                                    break e;
                                }
                                if (l === Ba) {
                                    n.tag = 14, n = Ut(null, n, e, r, t);
                                    break e;
                                }
                            }
                            throw n = "", null !== e && "object" == typeof e && e.$$typeof === Va && (n = " Did you wrap a component in React.lazy() more than once?"), e = s(e) || e, Error("Element type is invalid. Received a promise that resolves to: " + e + ". Lazy element type must resolve to a class or function." + n);
                        }
                        ca(e) ? (r = Ct(e, r), n.tag = 1, n.type = e, n = jt(null, n, e, r, t)) : (n.tag = 0, At(n, e), n.type = e, n = Wt(null, n, e, r, t));
                    }
                    return n;
                case 0:
                    return Wt(e, n, n.type, n.pendingProps, t);
                case 1:
                    return jt(e, n, r = n.type, l = Ct(r, n.pendingProps), t);
                case 3:
                    if (W(n, n.stateNode.containerInfo), null === e) throw Error("Should have a current fiber. This is a bug in React.");
                    var a = n.pendingProps;
                    r = (l = n.memoizedState).element, he(e, n), ke(n, a, null, t);
                    var o = n.memoizedState;
                    return a = o.cache, tr(n, Uc, a), a !== l.cache && ar(n, [
                        Uc
                    ], t, !0), Se(), (a = o.element) !== r ? (Nt(e, n, a, t), n = n.child) : n = Xt(e, n, t), n;
                case 26:
                case 27:
                case 5:
                    return A(n), l = n.type, a = n.pendingProps, o = null !== e ? e.memoizedProps : null, r = a.children, mo(l, a) ? r = null : null !== o && mo(l, o) && (n.flags |= 32), null !== n.memoizedState && (l = Ze(e, n, tn, null, null, t), Uo._currentValue2 = l), Mt(e, n), Nt(e, n, r, t), n.child;
                case 6:
                    return null;
                case 13:
                    return Bt(e, n, t);
                case 4:
                    return W(n, n.stateNode.containerInfo), r = n.pendingProps, null === e ? n.child = fs(n, null, r, t) : Nt(e, n, r, t), n.child;
                case 11:
                    return Lt(e, n, n.type, n.pendingProps, t);
                case 7:
                    return Nt(e, n, n.pendingProps, t), n.child;
                case 8:
                    return Nt(e, n, n.pendingProps.children, t), n.child;
                case 12:
                    return n.flags |= 4, n.flags |= 2048, (r = n.stateNode).effectDuration = -0, r.passiveEffectDuration = -0, Nt(e, n, n.pendingProps.children, t), n.child;
                case 10:
                    return r = n.type, a = (l = n.pendingProps).value, "value" in l || wc || (wc = !0, console.error("The `value` prop is required for the `<Context.Provider>`. Did you misspell it or forget to pass it?")), tr(n, r, a), Nt(e, n, l.children, t), n.child;
                case 9:
                    return l = n.type._context, "function" != typeof (r = n.pendingProps.children) && console.error("A context consumer was rendered with multiple children, or a child that isn't a function. A context consumer expects a single child that is a function. If you did pass a function, make sure there is no trailing or leading whitespace around it."), ir(n), l = sr(l), r = ji(r, l, void 0), n.flags |= 1, Nt(e, n, r, t), n.child;
                case 14:
                    return Ut(e, n, n.type, n.pendingProps, t);
                case 15:
                    return Dt(e, n, n.type, n.pendingProps, t);
                case 19:
                    return Kt(e, n, t);
                case 22:
                    return It(e, n, t);
                case 24:
                    return ir(n), r = sr(Uc), null === e ? (null === (l = hr()) && (l = cf, a = dr(), l.pooledCache = a, pr(a), null !== a && (l.pooledCacheLanes |= t), l = a), n.memoizedState = {
                        parent: r,
                        cache: l
                    }, me(n), tr(n, Uc, l)) : (0 != (e.lanes & t) && (he(e, n), ke(n, null, null, t), Se()), l = e.memoizedState, a = n.memoizedState, l.parent !== r ? (l = {
                        parent: r,
                        cache: r
                    }, n.memoizedState = l, 0 === n.lanes && (n.memoizedState = n.updateQueue.baseState = l), tr(n, Uc, r)) : (r = a.cache, tr(n, Uc, r), r !== l.cache && ar(n, [
                        Uc
                    ], t, !0))), Nt(e, n, n.pendingProps.children, t), n.child;
                case 29:
                    throw n.pendingProps;
            }
            throw Error("Unknown unit of work tag (" + n.tag + "). This error is likely caused by a bug in React. Please file an issue.");
        }
        function nr() {
            _c = Pc = Ec = null, Rc = !1;
        }
        function tr(e, n, t) {
            S(xc, n._currentValue2, e), n._currentValue2 = t, S(zc, n._currentRenderer2, e), void 0 !== n._currentRenderer2 && null !== n._currentRenderer2 && n._currentRenderer2 !== Cc && console.error("Detected multiple renderers concurrently rendering the same context provider. This is currently unsupported."), n._currentRenderer2 = Cc;
        }
        function rr(e, n) {
            var t = xc.current;
            e._currentValue2 = t, t = zc.current, v(zc, n), e._currentRenderer2 = t, v(xc, n);
        }
        function lr(e, n, t) {
            for(; null !== e;){
                var r = e.alternate;
                if ((e.childLanes & n) !== n ? (e.childLanes |= n, null !== r && (r.childLanes |= n)) : null !== r && (r.childLanes & n) !== n && (r.childLanes |= n), e === t) break;
                e = e.return;
            }
            e !== t && console.error("Expected to find the propagation root when scheduling context work. This error is likely caused by a bug in React. Please file an issue.");
        }
        function ar(e, n, t, r) {
            var l = e.child;
            for(null !== l && (l.return = e); null !== l;){
                var a = l.dependencies;
                if (null !== a) {
                    var o = l.child;
                    a = a.firstContext;
                    e: for(; null !== a;){
                        var u = a;
                        a = l;
                        for(var i = 0; i < n.length; i++)if (u.context === n[i]) {
                            a.lanes |= t, null !== (u = a.alternate) && (u.lanes |= t), lr(a.return, t, e), r || (o = null);
                            break e;
                        }
                        a = u.next;
                    }
                } else if (18 === l.tag) {
                    if (null === (o = l.return)) throw Error("We just came from a parent so we must have had a parent. This is a bug in React.");
                    o.lanes |= t, null !== (a = o.alternate) && (a.lanes |= t), lr(o, t, e), o = null;
                } else o = l.child;
                if (null !== o) o.return = l;
                else for(o = l; null !== o;){
                    if (o === e) {
                        o = null;
                        break;
                    }
                    if (null !== (l = o.sibling)) {
                        l.return = o.return, o = l;
                        break;
                    }
                    o = o.return;
                }
                l = o;
            }
        }
        function or(e, n, t, r) {
            e = null;
            for(var l = n, a = !1; null !== l;){
                if (!a) {
                    if (0 != (524288 & l.flags)) a = !0;
                    else if (0 != (262144 & l.flags)) break;
                }
                if (10 === l.tag) {
                    var o = l.alternate;
                    if (null === o) throw Error("Should have a current fiber. This is a bug in React.");
                    if (null !== (o = o.memoizedProps)) {
                        var u = l.type;
                        Ru(l.pendingProps.value, o.value) || (null !== e ? e.push(u) : e = [
                            u
                        ]);
                    }
                } else if (l === Ou.current) {
                    if (null === (o = l.alternate)) throw Error("Should have a current fiber. This is a bug in React.");
                    o.memoizedState.memoizedState !== l.memoizedState.memoizedState && (null !== e ? e.push(Uo) : e = [
                        Uo
                    ]);
                }
                l = l.return;
            }
            null !== e && ar(n, e, t, r), n.flags |= 262144;
        }
        function ur(e) {
            for(e = e.firstContext; null !== e;){
                var n = e.context;
                if (!Ru(n._currentValue2, e.memoizedValue)) return !0;
                e = e.next;
            }
            return !1;
        }
        function ir(e) {
            Ec = e, _c = Pc = null, null !== (e = e.dependencies) && (e.firstContext = null);
        }
        function sr(e) {
            return Rc && console.error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo()."), fr(Ec, e);
        }
        function cr(e, n) {
            return null === Ec && ir(e), fr(e, n);
        }
        function fr(e, n) {
            var t = n._currentValue2;
            if (_c !== n) if (n = {
                context: n,
                memoizedValue: t,
                next: null
            }, null === Pc) {
                if (null === e) throw Error("Context can only be read while React is rendering. In classes, you can read it in the render method or getDerivedStateFromProps. In function components, you can read it directly in the function body, but not inside Hooks like useReducer() or useMemo().");
                Pc = n, e.dependencies = {
                    lanes: 0,
                    firstContext: n,
                    _debugThenableState: null
                }, e.flags |= 524288;
            } else Pc = Pc.next = n;
            return t;
        }
        function dr() {
            return {
                controller: new Tc,
                data: new Map,
                refCount: 0
            };
        }
        function pr(e) {
            e.controller.signal.aborted && console.warn("A cache instance was retained after it was already freed. This likely indicates a bug in React."), e.refCount++;
        }
        function mr(e) {
            e.refCount--, 0 > e.refCount && console.warn("A cache instance was released after it was already freed. This likely indicates a bug in React."), 0 === e.refCount && Nc(Lc, function() {
                e.controller.abort();
            });
        }
        function hr() {
            var e = Ic.current;
            return null !== e ? e : cf.pooledCache;
        }
        function gr(e, n) {
            S(Ic, null === n ? Ic.current : n.pool, e);
        }
        function yr() {
            var e = hr();
            return null === e ? null : {
                parent: Uc._currentValue2,
                pool: e
            };
        }
        function br(e) {
            e.flags |= 4;
        }
        function vr(e, n) {
            null !== n && (e.flags |= 4), 16384 & e.flags && (n = 22 !== e.tag ? C() : 536870912, e.lanes |= n);
        }
        function Sr(e, n) {
            switch(e.tailMode){
                case "hidden":
                    n = e.tail;
                    for(var t = null; null !== n;)null !== n.alternate && (t = n), n = n.sibling;
                    null === t ? e.tail = null : t.sibling = null;
                    break;
                case "collapsed":
                    t = e.tail;
                    for(var r = null; null !== t;)null !== t.alternate && (r = t), t = t.sibling;
                    null === r ? n || null === e.tail ? e.tail = null : e.tail.sibling = null : r.sibling = null;
            }
        }
        function kr(e) {
            var n = null !== e.alternate && e.alternate.child === e.child, t = 0, r = 0;
            if (n) if ((2 & e.mode) !== $u) {
                for(var l = e.selfBaseDuration, a = e.child; null !== a;)t |= a.lanes | a.childLanes, r |= 31457280 & a.subtreeFlags, r |= 31457280 & a.flags, l += a.treeBaseDuration, a = a.sibling;
                e.treeBaseDuration = l;
            } else for(l = e.child; null !== l;)t |= l.lanes | l.childLanes, r |= 31457280 & l.subtreeFlags, r |= 31457280 & l.flags, l.return = e, l = l.sibling;
            else if ((2 & e.mode) !== $u) {
                l = e.actualDuration, a = e.selfBaseDuration;
                for(var o = e.child; null !== o;)t |= o.lanes | o.childLanes, r |= o.subtreeFlags, r |= o.flags, l += o.actualDuration, a += o.treeBaseDuration, o = o.sibling;
                e.actualDuration = l, e.treeBaseDuration = a;
            } else for(l = e.child; null !== l;)t |= l.lanes | l.childLanes, r |= l.subtreeFlags, r |= l.flags, l.return = e, l = l.sibling;
            return e.subtreeFlags |= r, e.childLanes = t, n;
        }
        function wr(e, n, t) {
            var r = n.pendingProps;
            switch(I(n), n.tag){
                case 16:
                case 15:
                case 0:
                case 11:
                case 7:
                case 8:
                case 12:
                case 9:
                case 14:
                case 1:
                    return kr(n), null;
                case 3:
                    return t = n.stateNode, r = null, null !== e && (r = e.memoizedState.cache), n.memoizedState.cache !== r && (n.flags |= 2048), rr(Uc, n), H(n), t.pendingContext && (t.context = t.pendingContext, t.pendingContext = null), null !== e && null !== e.child || null === e || e.memoizedState.isDehydrated && 0 == (256 & n.flags) || (n.flags |= 1024, null !== Vu && (Pl(Vu), Vu = null)), kr(n), null;
                case 26:
                    var l;
                case 27:
                case 5:
                    if (Q(n), t = n.type, null !== e && null != n.stateNode) !function(e, n, t, r) {
                        e.memoizedProps !== r && br(n);
                    }(e, n, 0, r);
                    else {
                        if (!r) {
                            if (null === n.stateNode) throw Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");
                            return kr(n), null;
                        }
                        e = j(), l = M(Qu.current), function(e, n, t, r) {
                            for(t = n.child; null !== t;){
                                if (5 === t.tag || 6 === t.tag) fo(e, t.stateNode);
                                else if (4 !== t.tag && !au && null !== t.child) {
                                    t.child.return = t, t = t.child;
                                    continue;
                                }
                                if (t === n) break;
                                for(; null === t.sibling;){
                                    if (null === t.return || t.return === n) return;
                                    t = t.return;
                                }
                                t.sibling.return = t.return, t = t.sibling;
                            }
                        }(l = co(t, r, l, e, n), n, !1), n.stateNode = l, po(l, t, r, e) && br(n);
                    }
                    return kr(n), function(e, n, t) {
                        if (Po(n, t)) {
                            if (e.flags |= 16777216, !_o(n, t)) {
                                if (!Dl()) throw Mi = Fi, Ii;
                                e.flags |= 8192;
                            }
                        } else e.flags &= -16777217;
                    }(n, n.type, n.pendingProps), null;
                case 6:
                    if (e && null != n.stateNode) (t = e.memoizedProps) !== r && br(n);
                    else {
                        if ("string" != typeof r && null === n.stateNode) throw Error("We must have new props for new mounts. This error is likely caused by a bug in React. Please file an issue.");
                        var a;
                        e = M(Qu.current), t = j(), n.stateNode = ho(r, e, t, n);
                    }
                    return kr(n), null;
                case 13:
                    if (r = n.memoizedState, null === e || null !== e.memoizedState && null !== e.memoizedState.dehydrated) {
                        if (l = !1, null !== r && null !== r.dehydrated) {
                            if (null === e) {
                                if (!l) throw Error("A dehydrated suspense component was completed without a hydrated node. This is probably a bug in React.");
                                throw Error("Expected prepareToHydrateHostSuspenseInstance() to never be called. This error is likely caused by a bug in React. Please file an issue.");
                            }
                            0 == (128 & n.flags) && (n.memoizedState = null), n.flags |= 4, kr(n), (2 & n.mode) !== $u && null !== r && null !== (l = n.child) && (n.treeBaseDuration -= l.treeBaseDuration), l = !1;
                        } else null !== Vu && (Pl(Vu), Vu = null), l = !0;
                        if (!l) return 256 & n.flags ? (Ve(n), n) : (Ve(n), null);
                    }
                    return Ve(n), 0 != (128 & n.flags) ? (n.lanes = t, (2 & n.mode) !== $u && re(n), n) : (t = null !== r, e = null !== e && null !== e.memoizedState, t && (l = null, null !== (r = n.child).alternate && null !== r.alternate.memoizedState && null !== r.alternate.memoizedState.cachePool && (l = r.alternate.memoizedState.cachePool.pool), a = null, null !== r.memoizedState && null !== r.memoizedState.cachePool && (a = r.memoizedState.cachePool.pool), a !== l && (r.flags |= 2048)), t !== e && t && (n.child.flags |= 8192), vr(n, n.updateQueue), kr(n), (2 & n.mode) !== $u && t && null !== (e = n.child) && (n.treeBaseDuration -= e.treeBaseDuration), null);
                case 4:
                    return H(n), null === e && ko(n.stateNode.containerInfo), kr(n), null;
                case 10:
                    return rr(n.type, n), kr(n), null;
                case 19:
                    if (v(vs, n), null === (l = n.memoizedState)) return kr(n), null;
                    if (r = 0 != (128 & n.flags), null === (a = l.rendering)) if (r) Sr(l, !1);
                    else {
                        if (Pf !== nf || null !== e && 0 != (128 & e.flags)) for(e = n.child; null !== e;){
                            if (null !== (a = $e(e))) {
                                for(n.flags |= 128, Sr(l, !1), e = a.updateQueue, n.updateQueue = e, vr(n, e), n.subtreeFlags = 0, e = t, t = n.child; null !== t;)da(t, e), t = t.sibling;
                                return S(vs, vs.current & ys | bs, n), n.child;
                            }
                            e = e.sibling;
                        }
                        null !== l.tail && vu() > Wf && (n.flags |= 128, r = !0, Sr(l, !1), n.lanes = 4194304);
                    }
                    else {
                        if (!r) if (null !== (e = $e(a))) {
                            if (n.flags |= 128, r = !0, e = e.updateQueue, n.updateQueue = e, vr(n, e), Sr(l, !0), null === l.tail && "hidden" === l.tailMode && !a.alternate) return kr(n), null;
                        } else 2 * vu() - l.renderingStartTime > Wf && 536870912 !== t && (n.flags |= 128, r = !0, Sr(l, !1), n.lanes = 4194304);
                        l.isBackwards ? (a.sibling = n.child, n.child = a) : (null !== (e = l.last) ? e.sibling = a : n.child = a, l.last = a);
                    }
                    return null !== l.tail ? (e = l.tail, l.rendering = e, l.tail = e.sibling, l.renderingStartTime = vu(), e.sibling = null, t = vs.current, S(vs, t = r ? t & ys | bs : t & ys, n), e) : (kr(n), null);
                case 22:
                case 23:
                    return Ve(n), Ae(n), r = null !== n.memoizedState, null !== e ? null !== e.memoizedState !== r && (n.flags |= 8192) : r && (n.flags |= 8192), r ? 0 != (536870912 & t) && 0 == (128 & n.flags) && (kr(n), 6 & n.subtreeFlags && (n.flags |= 8192)) : kr(n), null !== (t = n.updateQueue) && vr(n, t.retryQueue), t = null, null !== e && null !== e.memoizedState && null !== e.memoizedState.cachePool && (t = e.memoizedState.cachePool.pool), r = null, null !== n.memoizedState && null !== n.memoizedState.cachePool && (r = n.memoizedState.cachePool.pool), r !== t && (n.flags |= 2048), null !== e && v(Ic, n), null;
                case 24:
                    return t = null, null !== e && (t = e.memoizedState.cache), n.memoizedState.cache !== t && (n.flags |= 2048), rr(Uc, n), kr(n), null;
                case 25:
                    return null;
            }
            throw Error("Unknown unit of work tag (" + n.tag + "). This error is likely caused by a bug in React. Please file an issue.");
        }
        function xr(e, n) {
            switch(I(n), n.tag){
                case 1:
                    return 65536 & (e = n.flags) ? (n.flags = -65537 & e | 128, (2 & n.mode) !== $u && re(n), n) : null;
                case 3:
                    return rr(Uc, n), H(n), 0 != (65536 & (e = n.flags)) && 0 == (128 & e) ? (n.flags = -65537 & e | 128, n) : null;
                case 26:
                case 27:
                case 5:
                    return Q(n), null;
                case 13:
                    if (Ve(n), null !== (e = n.memoizedState) && null !== e.dehydrated && null === n.alternate) throw Error("Threw in newly mounted dehydrated component. This is likely a bug in React. Please file an issue.");
                    return 65536 & (e = n.flags) ? (n.flags = -65537 & e | 128, (2 & n.mode) !== $u && re(n), n) : null;
                case 19:
                    return v(vs, n), null;
                case 4:
                    return H(n), null;
                case 10:
                    return rr(n.type, n), null;
                case 22:
                case 23:
                    return Ve(n), Ae(n), null !== e && v(Ic, n), 65536 & (e = n.flags) ? (n.flags = -65537 & e | 128, (2 & n.mode) !== $u && re(n), n) : null;
                case 24:
                    return rr(Uc, n), null;
                default:
                    return null;
            }
        }
        function zr(e, n) {
            switch(I(n), n.tag){
                case 3:
                    rr(Uc, n), H(n);
                    break;
                case 26:
                case 27:
                case 5:
                    Q(n);
                    break;
                case 4:
                    H(n);
                    break;
                case 13:
                    Ve(n);
                    break;
                case 19:
                    v(vs, n);
                    break;
                case 10:
                    rr(n.type, n);
                    break;
                case 22:
                case 23:
                    Ve(n), Ae(n), null !== e && v(Ic, n);
                    break;
                case 24:
                    rr(Uc, n);
            }
        }
        function Cr(e) {
            return (2 & e.mode) !== $u;
        }
        function Er(e, n) {
            Cr(e) ? (te(), _r(n, e), ne()) : _r(n, e);
        }
        function Pr(e, n, t) {
            Cr(e) ? (te(), Rr(t, e, n), ne()) : Rr(t, e, n);
        }
        function _r(e, n) {
            try {
                var t = n.updateQueue, r = null !== t ? t.lastEffect : null;
                if (null !== r) {
                    var l = r.next;
                    t = l;
                    do {
                        var a;
                        (t.tag & e) === e && ((e & zs) !== Ss ? null !== Pu && "function" == typeof Pu.markComponentPassiveEffectMountStarted && Pu.markComponentPassiveEffectMountStarted(n) : (e & xs) !== Ss && null !== Pu && "function" == typeof Pu.markComponentLayoutEffectMountStarted && Pu.markComponentLayoutEffectMountStarted(n), r = void 0, (e & ws) !== Ss && (nd = !0), r = y(n, Xi, t), (e & ws) !== Ss && (nd = !1), (e & zs) !== Ss ? null !== Pu && "function" == typeof Pu.markComponentPassiveEffectMountStopped && Pu.markComponentPassiveEffectMountStopped() : (e & xs) !== Ss && null !== Pu && "function" == typeof Pu.markComponentLayoutEffectMountStopped && Pu.markComponentLayoutEffectMountStopped(), void 0 !== r && "function" != typeof r) && y(n, function(e, n) {
                            console.error("%s must not return anything besides a function, which is used for clean-up.%s", e, n);
                        }, a = 0 != (t.tag & xs) ? "useLayoutEffect" : 0 != (t.tag & ws) ? "useInsertionEffect" : "useEffect", null === r ? " You returned null. If your effect does not require clean up, return undefined (or nothing)." : "function" == typeof r.then ? "\n\nIt looks like you wrote " + a + "(async () => ...) or returned a Promise. Instead, write the async function inside your effect and call it immediately:\n\n" + a + "(() => {\n  async function fetchData() {\n    // You can await here\n    const response = await MyAPI.getData(someId);\n    // ...\n  }\n  fetchData();\n}, [someId]); // Or [] if effect doesn't need props or state\n\nLearn more about data fetching with Hooks: https://react.dev/link/hooks-data-fetching" : " You returned: " + r), t = t.next;
                    }while (t !== l)
                }
            } catch (e) {
                Xl(n, n.return, e);
            }
        }
        function Rr(e, n, t) {
            try {
                var r = n.updateQueue, l = null !== r ? r.lastEffect : null;
                if (null !== l) {
                    var a = l.next;
                    r = a;
                    do {
                        if ((r.tag & e) === e) {
                            var o = r.inst, u = o.destroy;
                            void 0 !== u && (o.destroy = void 0, (e & zs) !== Ss ? null !== Pu && "function" == typeof Pu.markComponentPassiveEffectUnmountStarted && Pu.markComponentPassiveEffectUnmountStarted(n) : (e & xs) !== Ss && null !== Pu && "function" == typeof Pu.markComponentLayoutEffectUnmountStarted && Pu.markComponentLayoutEffectUnmountStarted(n), (e & ws) !== Ss && (nd = !0), y(n, es, n, t, u), (e & ws) !== Ss && (nd = !1), (e & zs) !== Ss ? null !== Pu && "function" == typeof Pu.markComponentPassiveEffectUnmountStopped && Pu.markComponentPassiveEffectUnmountStopped() : (e & xs) !== Ss && null !== Pu && "function" == typeof Pu.markComponentLayoutEffectUnmountStopped && Pu.markComponentLayoutEffectUnmountStopped());
                        }
                        r = r.next;
                    }while (r !== a)
                }
            } catch (e) {
                Xl(n, n.return, e);
            }
        }
        function Tr(e, n) {
            Cr(e) ? (te(), _r(n, e), ne()) : _r(n, e);
        }
        function Nr(e, n, t) {
            Cr(e) ? (te(), Rr(t, e, n), ne()) : Rr(t, e, n);
        }
        function Lr(e) {
            var n = e.updateQueue;
            if (null !== n) {
                var t = e.stateNode;
                e.type.defaultProps || "ref" in e.memoizedProps || bc || (t.props !== e.memoizedProps && console.error("Expected %s props to match memoized props before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.", c(e) || "instance"), t.state !== e.memoizedState && console.error("Expected %s state to match memoized state before processing the update queue. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.", c(e) || "instance"));
                try {
                    y(e, ze, n, t);
                } catch (n) {
                    Xl(e, e.return, n);
                }
            }
        }
        function Ur(e, n, t) {
            return e.getSnapshotBeforeUpdate(n, t);
        }
        function Dr(e, n) {
            var t = n.memoizedProps, r = n.memoizedState;
            n = e.stateNode, e.type.defaultProps || "ref" in e.memoizedProps || bc || (n.props !== e.memoizedProps && console.error("Expected %s props to match memoized props before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.", c(e) || "instance"), n.state !== e.memoizedState && console.error("Expected %s state to match memoized state before getSnapshotBeforeUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.", c(e) || "instance"));
            try {
                var l = Ct(e.type, t, (e.elementType, e.type)), a = y(e, Ur, n, l, r);
                t = Fc, void 0 !== a || t.has(e.type) || (t.add(e.type), y(e, function() {
                    console.error("%s.getSnapshotBeforeUpdate(): A snapshot value (or null) must be returned. You have returned undefined.", c(e));
                })), n.__reactInternalSnapshotBeforeUpdate = a;
            } catch (n) {
                Xl(e, e.return, n);
            }
        }
        function Ir(e, n, t) {
            t.props = Ct(e.type, e.memoizedProps), t.state = e.memoizedState, Cr(e) ? (te(), y(e, Ji, e, n, t), ne()) : y(e, Ji, e, n, t);
        }
        function Fr(e) {
            var n = e.ref;
            if (null !== n) {
                var t = e.stateNode;
                switch(e.tag){
                    case 26:
                    case 27:
                    case 5:
                        t = ao(t);
                }
                if ("function" == typeof n) if (Cr(e)) try {
                    te(), e.refCleanup = n(t);
                } finally{
                    ne();
                }
                else e.refCleanup = n(t);
                else "string" == typeof n ? console.error("String refs are no longer supported.") : n.hasOwnProperty("current") || console.error("Unexpected ref object provided for %s. Use either a ref-setter function or React.createRef().", c(e)), n.current = t;
            }
        }
        function Mr(e, n) {
            try {
                y(e, Fr, e);
            } catch (t) {
                Xl(e, n, t);
            }
        }
        function Wr(e, n) {
            var t = e.ref, r = e.refCleanup;
            if (null !== t) if ("function" == typeof r) try {
                if (Cr(e)) try {
                    te(), y(e, r);
                } finally{
                    ne();
                }
                else y(e, r);
            } catch (t) {
                Xl(e, n, t);
            } finally{
                e.refCleanup = null, null != (e = e.alternate) && (e.refCleanup = null);
            }
            else if ("function" == typeof t) try {
                if (Cr(e)) try {
                    te(), y(e, t, null);
                } finally{
                    ne();
                }
                else y(e, t, null);
            } catch (t) {
                Xl(e, n, t);
            }
            else t.current = null;
        }
        function Hr(e, n, t, r) {
            var l = e.memoizedProps, a = l.id, o = l.onCommit;
            l = l.onRender, n = null === n ? "mount" : "update", ei && (n = "nested-update"), "function" == typeof l && l(a, n, e.actualDuration, e.treeBaseDuration, e.actualStartTime, t), "function" == typeof o && o(e.memoizedProps.id, n, r, t);
        }
        function jr(e, n, t, r) {
            var l = e.memoizedProps;
            e = l.id, l = l.onPostCommit, n = null === n ? "mount" : "update", ei && (n = "nested-update"), "function" == typeof l && l(e, n, r, t);
        }
        function Ar(e) {
            var n = e.type, t = e.memoizedProps, r = e.stateNode;
            try {
                y(e, Wo, r, n, t, e);
            } catch (n) {
                Xl(e, e.return, n);
            }
        }
        function Qr(e) {
            return 5 === e.tag || 3 === e.tag || 4 === e.tag;
        }
        function Or(e) {
            e: for(;;){
                for(; null === e.sibling;){
                    if (null === e.return || Qr(e.return)) return null;
                    e = e.return;
                }
                for(e.sibling.return = e.return, e = e.sibling; 5 !== e.tag && 6 !== e.tag && 18 !== e.tag;){
                    if (2 & e.flags) continue e;
                    if (null === e.child || 4 === e.tag) continue e;
                    e.child.return = e, e = e.child;
                }
                if (!(2 & e.flags)) return e.stateNode;
            }
        }
        function Br(e, n, t) {
            var r = e.tag;
            if (5 === r || 6 === r) e = e.stateNode, n ? Ao(t, e, n) : Fo(t, e);
            else if (4 !== r && !au && null !== (e = e.child)) for(Br(e, n, t), e = e.sibling; null !== e;)Br(e, n, t), e = e.sibling;
        }
        function Vr(e, n, t) {
            var r = e.tag;
            if (5 === r || 6 === r) e = e.stateNode, n ? jo(t, e, n) : Io(t, e);
            else if (4 !== r && !au && null !== (e = e.child)) for(Vr(e, n, t), e = e.sibling; null !== e;)Vr(e, n, t), e = e.sibling;
        }
        function $r(e) {
            e: {
                for(var n = e.return; null !== n;){
                    if (Qr(n)) {
                        var t = n;
                        break e;
                    }
                    n = n.return;
                }
                throw Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");
            }
            switch(t.tag){
                case 27:
                case 5:
                    n = t.stateNode, 32 & t.flags && (Bo(n), t.flags &= -33), Vr(e, t = Or(e), n);
                    break;
                case 3:
                case 4:
                    n = t.stateNode.containerInfo, Br(e, t = Or(e), n);
                    break;
                default:
                    throw Error("Invalid host parent fiber. This error is likely caused by a bug in React. Please file an issue.");
            }
        }
        function qr(e, n, t) {
            var r = t.flags;
            switch(t.tag){
                case 0:
                case 11:
                case 15:
                    tl(e, t), 4 & r && Er(t, xs | ks);
                    break;
                case 1:
                    if (tl(e, t), 4 & r) if (e = t.stateNode, null === n) t.type.defaultProps || "ref" in t.memoizedProps || bc || (e.props !== t.memoizedProps && console.error("Expected %s props to match memoized props before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.", c(t) || "instance"), e.state !== t.memoizedState && console.error("Expected %s state to match memoized state before componentDidMount. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.", c(t) || "instance")), Cr(t) ? (te(), y(t, Bi, t, e), ne()) : y(t, Bi, t, e);
                    else {
                        var l = Ct(t.type, n.memoizedProps);
                        n = n.memoizedState, t.type.defaultProps || "ref" in t.memoizedProps || bc || (e.props !== t.memoizedProps && console.error("Expected %s props to match memoized props before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.props`. Please file an issue.", c(t) || "instance"), e.state !== t.memoizedState && console.error("Expected %s state to match memoized state before componentDidUpdate. This might either be because of a bug in React, or because a component reassigns its own `this.state`. Please file an issue.", c(t) || "instance")), Cr(t) ? (te(), y(t, $i, t, e, l, n, e.__reactInternalSnapshotBeforeUpdate), ne()) : y(t, $i, t, e, l, n, e.__reactInternalSnapshotBeforeUpdate);
                    }
                    64 & r && Lr(t), 512 & r && Mr(t, t.return);
                    break;
                case 3:
                    if (n = G(), tl(e, t), 64 & r && null !== (r = t.updateQueue)) {
                        if (l = null, null !== t.child) switch(t.child.tag){
                            case 27:
                            case 5:
                                l = ao(t.child.stateNode);
                                break;
                            case 1:
                                l = t.child.stateNode;
                        }
                        try {
                            y(t, ze, r, l);
                        } catch (e) {
                            Xl(t, t.return, e);
                        }
                    }
                    e.effectDuration += J(n);
                    break;
                case 26:
                case 27:
                case 5:
                    tl(e, t), null === n && 4 & r && Ar(t), 512 & r && Mr(t, t.return);
                    break;
                case 12:
                    if (4 & r) {
                        r = G(), tl(e, t), (e = t.stateNode).effectDuration += K(r);
                        try {
                            y(t, Hr, t, n, Ku, e.effectDuration);
                        } catch (e) {
                            Xl(t, t.return, e);
                        }
                    } else tl(e, t);
                    break;
                case 13:
                default:
                    tl(e, t);
                    break;
                case 22:
                    if (!(l = null !== t.memoizedState || Mc)) {
                        n = null !== n && null !== n.memoizedState || Wc;
                        var a = Mc, o = Wc;
                        Mc = l, (Wc = n) && !o ? ol(e, t, 0 != (8772 & t.subtreeFlags)) : tl(e, t), Mc = a, Wc = o;
                    }
                    512 & r && ("manual" === t.memoizedProps.mode ? Mr(t, t.return) : Wr(t, t.return));
            }
        }
        function Yr(e) {
            var n = e.alternate;
            null !== n && (e.alternate = null, Yr(n)), e.child = null, e.deletions = null, e.sibling = null, 5 === e.tag && null !== (n = e.stateNode) && Eo(n), e.stateNode = null, e._debugOwner = null, e.return = null, e.dependencies = null, e.memoizedProps = null, e.memoizedState = null, e.pendingProps = null, e.stateNode = null, e.updateQueue = null;
        }
        function Gr(e, n, t) {
            for(t = t.child; null !== t;)Jr(e, n, t), t = t.sibling;
        }
        function Jr(e, n, t) {
            switch(t.tag){
                case 26:
                case 27:
                    var r, l;
                case 5:
                    Wc || Wr(t, n);
                case 6:
                    if (r = Vc, l = $c, Vc = null, Gr(e, n, t), $c = l, null !== (Vc = r)) if ($c) try {
                        y(t, Oo, Vc, t.stateNode);
                    } catch (e) {
                        Xl(t, n, e);
                    }
                    else try {
                        y(t, Qo, Vc, t.stateNode);
                    } catch (e) {
                        Xl(t, n, e);
                    }
                    break;
                case 18:
                    null !== Vc && ($c ? nu(Vc, t.stateNode) : eu(Vc, t.stateNode));
                    break;
                case 4:
                    r = Vc, l = $c, Vc = t.stateNode.containerInfo, $c = !0, Gr(e, n, t), Vc = r, $c = l;
                    break;
                case 0:
                case 11:
                case 14:
                case 15:
                    Wc || Rr(ws, t, n), Wc || Pr(t, n, xs), Gr(e, n, t);
                    break;
                case 1:
                    Wc || (Wr(t, n), "function" == typeof (r = t.stateNode).componentWillUnmount && Ir(t, n, r)), Gr(e, n, t);
                    break;
                case 21:
                    Gr(e, n, t);
                    break;
                case 22:
                    Wr(t, n), Wc = (r = Wc) || null !== t.memoizedState, Gr(e, n, t), Wc = r;
                    break;
                default:
                    Gr(e, n, t);
            }
        }
        function Kr(e, n) {
            var t = function(e) {
                switch(e.tag){
                    case 13:
                    case 19:
                        var n = e.stateNode;
                        return null === n && (n = e.stateNode = new jc), n;
                    case 22:
                        return null === (n = (e = e.stateNode)._retryCache) && (n = e._retryCache = new jc), n;
                    default:
                        throw Error("Unexpected Suspense handler tag (" + e.tag + "). This is a bug in React.");
                }
            }(e);
            n.forEach(function(n) {
                var r = ra.bind(null, e, n);
                if (!t.has(n)) {
                    if (t.add(n), _u) {
                        if (null === Qc || null === Oc) throw Error("Expected finished root and lanes to be set. This is a bug in React.");
                        ia(Oc, Qc);
                    }
                    n.then(r, r);
                }
            });
        }
        function Xr(e, n) {
            var t = n.deletions;
            if (null !== t) for(var r = 0; r < t.length; r++){
                var l = e, a = n, o = t[r], u = a;
                e: for(; null !== u;){
                    switch(u.tag){
                        case 27:
                        case 5:
                            Vc = u.stateNode, $c = !1;
                            break e;
                        case 3:
                        case 4:
                            Vc = u.stateNode.containerInfo, $c = !0;
                            break e;
                    }
                    u = u.return;
                }
                if (null === Vc) throw Error("Expected to find a host parent. This error is likely caused by a bug in React. Please file an issue.");
                Jr(l, a, o), Vc = null, $c = !1, null !== (a = (l = o).alternate) && (a.return = null), l.return = null;
            }
            if (13878 & n.subtreeFlags) for(n = n.child; null !== n;)Zr(n, e), n = n.sibling;
        }
        function Zr(e, n) {
            var t = e.alternate, r = e.flags;
            switch(e.tag){
                case 0:
                case 11:
                case 14:
                case 15:
                    Xr(n, e), el(e), 4 & r && (Rr(ws | ks, e, e.return), _r(ws | ks, e), Pr(e, e.return, xs | ks));
                    break;
                case 1:
                    Xr(n, e), el(e), 512 & r && null !== t && Wr(t, t.return), 64 & r && Mc && null !== (e = e.updateQueue) && null !== (r = e.callbacks) && (t = e.shared.hiddenCallbacks, e.shared.hiddenCallbacks = null === t ? r : t.concat(r));
                    break;
                case 26:
                    var l;
                case 27:
                    var a;
                case 5:
                    if (Xr(n, e), el(e), 512 & r && null !== t && Wr(t, t.return), 32 & e.flags) {
                        n = e.stateNode;
                        try {
                            y(e, Bo, n);
                        } catch (n) {
                            Xl(e, e.return, n);
                        }
                    }
                    4 & r && null != e.stateNode && function(e, n, t) {
                        try {
                            y(e, Ho, e.stateNode, e.type, t, n, e);
                        } catch (n) {
                            Xl(e, e.return, n);
                        }
                    }(e, n = e.memoizedProps, null !== t ? t.memoizedProps : n), 1024 & r && (Hc = !0, "form" !== e.type && console.error("Unexpected host component type. Expected a form. This is a bug in React."));
                    break;
                case 6:
                    if (Xr(n, e), el(e), 4 & r && vo) {
                        if (null === e.stateNode) throw Error("This should have a text node initialized. This error is likely caused by a bug in React. Please file an issue.");
                        r = e.memoizedProps, t = null !== t ? t.memoizedProps : r, n = e.stateNode;
                        try {
                            y(e, Mo, n, t, r);
                        } catch (n) {
                            Xl(e, e.return, n);
                        }
                    }
                    break;
                case 3:
                    l = G(), Xr(n, e), el(e), Hc && (Hc = !1, nl(e)), n.effectDuration += J(l);
                    break;
                case 4:
                    Xr(n, e), el(e);
                    break;
                case 12:
                    r = G(), Xr(n, e), el(e), e.stateNode.effectDuration += K(r);
                    break;
                case 13:
                    Xr(n, e), el(e), 8192 & e.child.flags && null !== e.memoizedState != (null !== t && null !== t.memoizedState) && (Ff = vu()), 4 & r && null !== (r = e.updateQueue) && (e.updateQueue = null, Kr(e, r));
                    break;
                case 22:
                    512 & r && null !== t && Wr(t, t.return), l = null !== e.memoizedState;
                    var o = null !== t && null !== t.memoizedState, u = Mc, i = Wc;
                    if (Mc = u || l, Wc = i || o, Xr(n, e), Wc = i, Mc = u, el(e), (n = e.stateNode)._current = e, n._visibility &= -3, n._visibility |= 2 & n._pendingVisibility, 8192 & r && (n._visibility = l ? -2 & n._visibility : 1 | n._visibility, l && (n = Mc || Wc, null === t || o || n || ll(e)), null === e.memoizedProps || "manual" !== e.memoizedProps.mode)) {
                        e: if (t = null, vo) for(n = e;;){
                            if (5 === n.tag || tu || au) {
                                if (null === t) {
                                    o = t = n;
                                    try {
                                        a = o.stateNode, l ? y(o, Vo, a) : y(o, qo, o.stateNode, o.memoizedProps);
                                    } catch (e) {
                                        Xl(o, o.return, e);
                                    }
                                }
                            } else if (6 === n.tag) {
                                if (null === t) {
                                    o = n;
                                    try {
                                        var s = o.stateNode;
                                        l ? y(o, $o, s) : y(o, Yo, s, o.memoizedProps);
                                    } catch (e) {
                                        Xl(o, o.return, e);
                                    }
                                }
                            } else if ((22 !== n.tag && 23 !== n.tag || null === n.memoizedState || n === e) && null !== n.child) {
                                n.child.return = n, n = n.child;
                                continue;
                            }
                            if (n === e) break e;
                            for(; null === n.sibling;){
                                if (null === n.return || n.return === e) break e;
                                t === n && (t = null), n = n.return;
                            }
                            t === n && (t = null), n.sibling.return = n.return, n = n.sibling;
                        }
                    }
                    4 & r && null !== (r = e.updateQueue) && null !== (t = r.retryQueue) && (r.retryQueue = null, Kr(e, t));
                    break;
                case 19:
                    Xr(n, e), el(e), 4 & r && null !== (r = e.updateQueue) && (e.updateQueue = null, Kr(e, r));
                    break;
                case 21:
                    break;
                default:
                    Xr(n, e), el(e);
            }
        }
        function el(e) {
            var n = e.flags;
            if (2 & n) {
                try {
                    y(e, $r, e);
                } catch (n) {
                    Xl(e, e.return, n);
                }
                e.flags &= -3;
            }
            4096 & n && (e.flags &= -4097);
        }
        function nl(e) {
            if (1024 & e.subtreeFlags) for(e = e.child; null !== e;){
                var n = e;
                nl(n), 5 === n.tag && 1024 & n.flags && Do(n.stateNode), e = e.sibling;
            }
        }
        function tl(e, n) {
            if (8772 & n.subtreeFlags) for(n = n.child; null !== n;)qr(e, n.alternate, n), n = n.sibling;
        }
        function rl(e) {
            switch(e.tag){
                case 0:
                case 11:
                case 14:
                case 15:
                    Pr(e, e.return, xs), ll(e);
                    break;
                case 1:
                    Wr(e, e.return);
                    var n = e.stateNode;
                    "function" == typeof n.componentWillUnmount && Ir(e, e.return, n), ll(e);
                    break;
                case 26:
                case 27:
                case 5:
                    Wr(e, e.return), ll(e);
                    break;
                case 22:
                    Wr(e, e.return), null === e.memoizedState && ll(e);
                    break;
                default:
                    ll(e);
            }
        }
        function ll(e) {
            for(e = e.child; null !== e;)rl(e), e = e.sibling;
        }
        function al(e, n, t, r) {
            var l = t.flags;
            switch(t.tag){
                case 0:
                case 11:
                case 15:
                    ol(e, t, r), Er(t, xs);
                    break;
                case 1:
                    if (ol(e, t, r), "function" == typeof (n = t.stateNode).componentDidMount && y(t, Bi, t, n), null !== (n = t.updateQueue)) {
                        e = t.stateNode;
                        try {
                            y(t, xe, n, e);
                        } catch (e) {
                            Xl(t, t.return, e);
                        }
                    }
                    r && 64 & l && Lr(t), Mr(t, t.return);
                    break;
                case 26:
                case 27:
                case 5:
                    ol(e, t, r), r && null === n && 4 & l && Ar(t), Mr(t, t.return);
                    break;
                case 12:
                    if (r && 4 & l) {
                        l = G(), ol(e, t, r), (r = t.stateNode).effectDuration += K(l);
                        try {
                            y(t, Hr, t, n, Ku, r.effectDuration);
                        } catch (e) {
                            Xl(t, t.return, e);
                        }
                    } else ol(e, t, r);
                    break;
                case 13:
                default:
                    ol(e, t, r);
                    break;
                case 22:
                    null === t.memoizedState && ol(e, t, r), Mr(t, t.return);
            }
        }
        function ol(e, n, t) {
            for(t = t && 0 != (8772 & n.subtreeFlags), n = n.child; null !== n;)al(e, n.alternate, n, t), n = n.sibling;
        }
        function ul(e, n) {
            var t = null;
            null !== e && null !== e.memoizedState && null !== e.memoizedState.cachePool && (t = e.memoizedState.cachePool.pool), e = null, null !== n.memoizedState && null !== n.memoizedState.cachePool && (e = n.memoizedState.cachePool.pool), e !== t && (null != e && pr(e), null != t && mr(t));
        }
        function il(e, n) {
            e = null, null !== n.alternate && (e = n.alternate.memoizedState.cache), (n = n.memoizedState.cache) !== e && (pr(n), null != e && mr(e));
        }
        function sl(e, n, t, r) {
            if (10256 & n.subtreeFlags) for(n = n.child; null !== n;)cl(e, n, t, r), n = n.sibling;
        }
        function cl(e, n, t, r) {
            var l = n.flags;
            switch(n.tag){
                case 0:
                case 11:
                case 15:
                    sl(e, n, t, r), 2048 & l && Tr(n, zs | ks);
                    break;
                case 3:
                    var a = G();
                    sl(e, n, t, r), 2048 & l && (t = null, null !== n.alternate && (t = n.alternate.memoizedState.cache), (n = n.memoizedState.cache) !== t && (pr(n), null != t && mr(t))), e.passiveEffectDuration += J(a);
                    break;
                case 12:
                    if (2048 & l) {
                        a = G(), sl(e, n, t, r), (e = n.stateNode).passiveEffectDuration += K(a);
                        try {
                            y(n, jr, n, n.alternate, Ku, e.passiveEffectDuration);
                        } catch (e) {
                            Xl(n, n.return, e);
                        }
                    } else sl(e, n, t, r);
                    break;
                case 23:
                    break;
                case 22:
                    a = n.stateNode, null !== n.memoizedState ? 4 & a._visibility ? sl(e, n, t, r) : pl(e, n) : 4 & a._visibility ? sl(e, n, t, r) : (a._visibility |= 4, fl(e, n, t, r, 0 != (10256 & n.subtreeFlags))), 2048 & l && ul(n.alternate, n);
                    break;
                case 24:
                    sl(e, n, t, r), 2048 & l && il(n.alternate, n);
                    break;
                default:
                    sl(e, n, t, r);
            }
        }
        function fl(e, n, t, r, l) {
            for(l = l && 0 != (10256 & n.subtreeFlags), n = n.child; null !== n;)dl(e, n, t, r, l), n = n.sibling;
        }
        function dl(e, n, t, r, l) {
            var a = n.flags;
            switch(n.tag){
                case 0:
                case 11:
                case 15:
                    fl(e, n, t, r, l), Tr(n, zs);
                    break;
                case 23:
                    break;
                case 22:
                    var o = n.stateNode;
                    null !== n.memoizedState ? 4 & o._visibility ? fl(e, n, t, r, l) : pl(e, n) : (o._visibility |= 4, fl(e, n, t, r, l)), l && 2048 & a && ul(n.alternate, n);
                    break;
                case 24:
                    fl(e, n, t, r, l), l && 2048 & a && il(n.alternate, n);
                    break;
                default:
                    fl(e, n, t, r, l);
            }
        }
        function pl(e, n) {
            if (10256 & n.subtreeFlags) for(n = n.child; null !== n;){
                var t = e, r = n, l = r.flags;
                switch(r.tag){
                    case 22:
                        pl(t, r), 2048 & l && ul(r.alternate, r);
                        break;
                    case 24:
                        pl(t, r), 2048 & l && il(r.alternate, r);
                        break;
                    default:
                        pl(t, r);
                }
                n = n.sibling;
            }
        }
        function ml(e) {
            if (e.subtreeFlags & Yc) for(e = e.child; null !== e;)hl(e), e = e.sibling;
        }
        function hl(e) {
            switch(e.tag){
                case 26:
                    ml(e), e.flags & Yc && (null !== e.memoizedState ? lu(qc, e.memoizedState, e.memoizedProps) : To(e.type, e.memoizedProps));
                    break;
                case 5:
                    ml(e), e.flags & Yc && To(e.type, e.memoizedProps);
                    break;
                case 3:
                case 4:
                    var n;
                    ml(e);
                    break;
                case 22:
                    null === e.memoizedState && (null !== (n = e.alternate) && null !== n.memoizedState ? (n = Yc, Yc = 16777216, ml(e), Yc = n) : ml(e));
                    break;
                default:
                    ml(e);
            }
        }
        function gl(e) {
            var n = e.alternate;
            if (null !== n && null !== (e = n.child)) {
                n.child = null;
                do {
                    n = e.sibling, e.sibling = null, e = n;
                }while (null !== e)
            }
        }
        function yl(e) {
            var n = e.deletions;
            if (0 != (16 & e.flags)) {
                if (null !== n) for(var t = 0; t < n.length; t++){
                    var r = n[t];
                    Ac = r, kl(r, e);
                }
                gl(e);
            }
            if (10256 & e.subtreeFlags) for(e = e.child; null !== e;)bl(e), e = e.sibling;
        }
        function bl(e) {
            switch(e.tag){
                case 0:
                case 11:
                case 15:
                    yl(e), 2048 & e.flags && Nr(e, e.return, zs | ks);
                    break;
                case 3:
                    var n = G();
                    yl(e), e.stateNode.passiveEffectDuration += J(n);
                    break;
                case 12:
                    n = G(), yl(e), e.stateNode.passiveEffectDuration += K(n);
                    break;
                case 22:
                    n = e.stateNode, null !== e.memoizedState && 4 & n._visibility && (null === e.return || 13 !== e.return.tag) ? (n._visibility &= -5, vl(e)) : yl(e);
                    break;
                default:
                    yl(e);
            }
        }
        function vl(e) {
            var n = e.deletions;
            if (0 != (16 & e.flags)) {
                if (null !== n) for(var t = 0; t < n.length; t++){
                    var r = n[t];
                    Ac = r, kl(r, e);
                }
                gl(e);
            }
            for(e = e.child; null !== e;)Sl(e), e = e.sibling;
        }
        function Sl(e) {
            switch(e.tag){
                case 0:
                case 11:
                case 15:
                    Nr(e, e.return, zs), vl(e);
                    break;
                case 22:
                    var n = e.stateNode;
                    4 & n._visibility && (n._visibility &= -5, vl(e));
                    break;
                default:
                    vl(e);
            }
        }
        function kl(e, n) {
            for(; null !== Ac;){
                var t = Ac, r = t;
                switch(r.tag){
                    case 0:
                    case 11:
                    case 15:
                        Nr(r, n, zs);
                        break;
                    case 23:
                    case 22:
                        null !== r.memoizedState && null !== r.memoizedState.cachePool && null != (r = r.memoizedState.cachePool.pool) && pr(r);
                        break;
                    case 24:
                        mr(r.memoizedState.cache);
                }
                if (null !== (r = t.child)) r.return = t, Ac = r;
                else e: for(t = e; null !== Ac;){
                    var l = (r = Ac).sibling, a = r.return;
                    if (Yr(r), r === t) {
                        Ac = null;
                        break e;
                    }
                    if (null !== l) {
                        l.return = a, Ac = l;
                        break e;
                    }
                    Ac = a;
                }
            }
        }
        function wl() {
            var e = "undefined" != typeof IS_REACT_ACT_ENVIRONMENT ? IS_REACT_ACT_ENVIRONMENT : void 0;
            return e || null === Ja.actQueue || console.error("The current testing environment is not configured to support act(...)"), e;
        }
        function xl(e) {
            if ((sf & Zc) !== Xc && 0 !== df) return df & -df;
            var n = Ja.T;
            return null !== n ? (n._updatedFibers || (n._updatedFibers = new Set), n._updatedFibers.add(e), 0 !== (e = di) ? e : de()) : zo();
        }
        function zl() {
            0 === Nf && (Nf = 0 == (536870912 & df) || Bu ? z() : 536870912);
            var e = hs.current;
            return null !== e && (e.flags |= 32), Nf;
        }
        function Cl(e, n, t) {
            if (nd && console.error("useInsertionEffect must not schedule updates."), Jf && (Kf = !0), (e === cf && wf === hf || null !== e.cancelPendingCommit) && (Ll(e, 0), Tl(e, df, Nf)), P(e, t), 0 != (sf & Zc) && e === cf) {
                if (ro) switch(n.tag){
                    case 0:
                    case 11:
                    case 15:
                        e = ff && c(ff) || "Unknown", ud.has(e) || (ud.add(e), n = c(n) || "Unknown", console.error("Cannot update a component (`%s`) while rendering a different component (`%s`). To locate the bad setState() call inside `%s`, follow the stack trace as described in https://react.dev/link/setstate-in-render", n, e, e));
                        break;
                    case 1:
                        od || (console.error("Cannot update during an existing state transition (such as within `render`). Render methods should be a pure function of props and state."), od = !0);
                }
            } else _u && T(e, n, t), function(e) {
                wl() && null === Ja.actQueue && y(e, function() {
                    console.error("An update to %s inside a test was not wrapped in act(...).\n\nWhen testing, code that causes React state updates should be wrapped into act(...):\n\nact(() => {\n  /* fire events that update state */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act", c(e));
                });
            }(n), e === cf && ((sf & Zc) === Xc && (Rf |= t), Pf === af && Tl(e, df, Nf)), le(e);
        }
        function El(e, n, t) {
            if ((sf & (Zc | ef)) !== Xc) throw Error("Should not already be working.");
            var r = (t = !t && 0 == (60 & n) && 0 == (n & e.expiredLanes)) ? function(e, n) {
                var t = sf;
                sf |= Zc;
                var r = Il(), l = Fl();
                if (cf !== e || df !== n) {
                    if (_u) {
                        var a = e.memoizedUpdaters;
                        0 < a.size && (ia(e, df), a.clear()), N(e, n);
                    }
                    jf = null, Wf = vu() + Hf, Ll(e, n);
                }
                e: for(;;)try {
                    if (wf !== pf && null !== ff) n: switch(n = ff, a = xf, wf){
                        case mf:
                            wf = pf, xf = null, Bl(e, n, a);
                            break;
                        case hf:
                            if (Ee(a)) {
                                wf = pf, xf = null, Ql(n);
                                break;
                            }
                            n = function() {
                                wf === hf && cf === e && (wf = Sf), le(e);
                            }, a.then(n, n);
                            break e;
                        case gf:
                            wf = Sf;
                            break e;
                        case yf:
                            wf = bf;
                            break e;
                        case Sf:
                            Ee(a) ? (wf = pf, xf = null, Ql(n)) : (wf = pf, xf = null, Bl(e, n, a));
                            break;
                        case bf:
                            var o = null;
                            switch(ff.tag){
                                case 26:
                                    o = ff.memoizedState;
                                case 5:
                                case 27:
                                    var u = ff, i = u.type, s = u.pendingProps;
                                    if (o ? ru(o) : _o(i, s)) {
                                        wf = pf, xf = null;
                                        var c = u.sibling;
                                        if (null !== c) ff = c;
                                        else {
                                            var f = u.return;
                                            null !== f ? (ff = f, Vl(f)) : ff = null;
                                        }
                                        break n;
                                    }
                                    break;
                                default:
                                    console.error("Unexpected type of fiber triggered a suspensey commit. This is a bug in React.");
                            }
                            wf = pf, xf = null, Bl(e, n, a);
                            break;
                        case vf:
                            wf = pf, xf = null, Bl(e, n, a);
                            break;
                        case kf:
                            Nl(), Pf = uf;
                            break e;
                        default:
                            throw Error("Unexpected SuspendedReason. This is a bug in React.");
                    }
                    null !== Ja.actQueue ? Hl() : jl();
                    break;
                } catch (n) {
                    Ul(e, n);
                }
                return nr(), Ja.H = r, Ja.A = l, sf = t, null !== ff ? nf : (cf = null, df = 0, O(), Pf);
            }(e, n) : Wl(e, n);
            if (r !== nf) for(var l = t;;){
                if (r === uf) Tl(e, n, 0);
                else {
                    if (t = e.current.alternate, l && !Rl(t)) {
                        r = Wl(e, n), l = !1;
                        continue;
                    }
                    if (r === rf) {
                        if (l = n, e.errorRecoveryDisabledLanes & l) var a = 0;
                        else a = 0 != (a = -536870913 & e.pendingLanes) ? a : 536870912 & a ? 536870912 : 0;
                        if (0 !== a) {
                            n = a;
                            e: {
                                r = e;
                                var o = a;
                                a = Uf;
                                var u = So;
                                if (u && (Ll(r, o).flags |= 256), (o = Wl(r, o)) !== rf) {
                                    if (Cf && !u) {
                                        r.errorRecoveryDisabledLanes |= l, Rf |= l, r = af;
                                        break e;
                                    }
                                    r = Df, Df = a, null !== r && Pl(r);
                                }
                                r = o;
                            }
                            if (l = !1, r !== rf) continue;
                        }
                    }
                    if (r === tf) {
                        Ll(e, 0), Tl(e, n, 0);
                        break;
                    }
                    e: {
                        switch(l = e, r){
                            case nf:
                            case tf:
                                throw Error("Root did not complete. This is a bug in React.");
                            case af:
                                if ((4194176 & n) === n) {
                                    Tl(l, n, Nf);
                                    break e;
                                }
                                break;
                            case rf:
                                Df = null;
                                break;
                            case lf:
                            case of:
                                break;
                            default:
                                throw Error("Unknown root exit status.");
                        }
                        if (l.finishedWork = t, l.finishedLanes = n, null !== Ja.actQueue) ql(l, Df, jf, If, Nf);
                        else {
                            if ((62914560 & n) === n && 10 < (r = Ff + Mf - vu())) {
                                if (Tl(l, n, Nf), 0 !== w(l, 0)) break e;
                                l.timeoutHandle = go(_l.bind(null, l, t, Df, jf, If, n, Nf, Rf, Lf, zf, ld, -0, 0), r);
                                break e;
                            }
                            _l(l, t, Df, jf, If, n, Nf, Rf, Lf, zf, td, -0, 0);
                        }
                    }
                }
                break;
            }
            le(e);
        }
        function Pl(e) {
            null === Df ? Df = e : Df.push.apply(Df, e);
        }
        function _l(e, n, t, r, l, a, o, u, i, s, c, f, d) {
            if ((8192 & (s = n.subtreeFlags) || 16785408 == (16785408 & s)) && (Ro(), hl(n), null !== (n = No()))) return e.cancelPendingCommit = n(ql.bind(null, e, t, r, l, o, u, i, rd, f, d)), void Tl(e, a, o);
            ql(e, t, r, l, o);
        }
        function Rl(e) {
            for(var n = e;;){
                var t = n.tag;
                if ((0 === t || 11 === t || 15 === t) && 16384 & n.flags && null !== (t = n.updateQueue) && null !== (t = t.stores)) for(var r = 0; r < t.length; r++){
                    var l = t[r], a = l.getSnapshot;
                    l = l.value;
                    try {
                        if (!Ru(a(), l)) return !1;
                    } catch (e) {
                        return !1;
                    }
                }
                if (t = n.child, 16384 & n.subtreeFlags && null !== t) t.return = n, n = t;
                else {
                    if (n === e) break;
                    for(; null === n.sibling;){
                        if (null === n.return || n.return === e) return !0;
                        n = n.return;
                    }
                    n.sibling.return = n.return, n = n.sibling;
                }
            }
            return !0;
        }
        function Tl(e, n, t) {
            n &= ~Tf, n &= ~Rf, e.suspendedLanes |= n, e.pingedLanes &= ~n;
            for(var r = e.expirationTimes, l = n; 0 < l;){
                var a = 31 - cu(l), o = 1 << a;
                r[a] = -1, l &= ~o;
            }
            0 !== t && _(e, t, n);
        }
        function Nl() {
            if (null !== ff) {
                if (wf === pf) var e = ff.return;
                else e = ff, nr(), an(e), rs = null, ls = 0, e = ff;
                for(; null !== e;)zr(e.alternate, e), e = e.return;
                ff = null;
            }
        }
        function Ll(e, n) {
            e.finishedWork = null, e.finishedLanes = 0;
            var t = e.timeoutHandle;
            t !== bo && (e.timeoutHandle = bo, yo(t)), null !== (t = e.cancelPendingCommit) && (e.cancelPendingCommit = null, t()), Nl(), cf = e, ff = t = fa(e.current, null), df = n, wf = pf, xf = null, Cf = zf = !1, Pf = nf, Lf = Nf = Tf = Rf = _f = 0, Df = Uf = null, If = !1, 0 != (8 & n) && (n |= 32 & n);
            var r = e.entangledLanes;
            if (0 !== r) for(e = e.entanglements, r &= n; 0 < r;){
                var l = 31 - cu(r), a = 1 << l;
                n |= e[l], r &= ~a;
            }
            return Ef = n, O(), xi.discardPendingWarnings(), t;
        }
        function Ul(e, n) {
            Ts = null, Ja.H = $s, Ja.getCurrentStack = null, ro = !1, to = null, n === Di ? (n = Re(), wf = Dl() && 0 == (134217727 & _f) && 0 == (134217727 & Rf) ? hf : gf) : n === Ii ? (n = Re(), wf = yf) : wf = n === dc ? kf : null !== n && "object" == typeof n && "function" == typeof n.then ? vf : mf, xf = n;
            var t = ff;
            null === t ? (Pf = tf, Et(e, D(n, e.current))) : 2 & t.mode && Z(t);
        }
        function Dl() {
            var e = hs.current;
            return null === e || ((4194176 & df) === df ? null === gs : ((62914560 & df) === df || 0 != (536870912 & df)) && e === gs);
        }
        function Il() {
            var e = Ja.H;
            return Ja.H = $s, null === e ? $s : e;
        }
        function Fl() {
            var e = Ja.A;
            return Ja.A = Gc, e;
        }
        function Ml() {
            Pf = af, 0 == (134217727 & _f) && 0 == (134217727 & Rf) || null === cf || Tl(cf, df, Nf);
        }
        function Wl(e, n) {
            var t = sf;
            sf |= Zc;
            var r = Il(), l = Fl();
            if (cf !== e || df !== n) {
                if (_u) {
                    var a = e.memoizedUpdaters;
                    0 < a.size && (ia(e, df), a.clear()), N(e, n);
                }
                jf = null, Ll(e, n);
            }
            n = !1;
            e: for(;;)try {
                if (wf !== pf && null !== ff) {
                    a = ff;
                    var o = xf;
                    switch(wf){
                        case kf:
                            Nl(), Pf = uf;
                            break e;
                        case gf:
                        case hf:
                            n || null !== hs.current || (n = !0);
                        default:
                            wf = pf, xf = null, Bl(e, a, o);
                    }
                }
                Hl();
                break;
            } catch (n) {
                Ul(e, n);
            }
            if (n && e.shellSuspendCounter++, nr(), sf = t, Ja.H = r, Ja.A = l, null !== ff) throw Error("Cannot commit an incomplete root. This error is likely caused by a bug in React. Please file an issue.");
            return cf = null, df = 0, O(), Pf;
        }
        function Hl() {
            for(; null !== ff;)Al(ff);
        }
        function jl() {
            for(; null !== ff && !yu();)Al(ff);
        }
        function Al(e) {
            var n = e.alternate;
            (2 & e.mode) !== $u ? (X(e), n = y(e, er, n, e, Ef), Z(e)) : n = y(e, er, n, e, Ef), e.memoizedProps = e.pendingProps, null === n ? Vl(e) : ff = n;
        }
        function Ql(e) {
            var n = y(e, Ol, e);
            e.memoizedProps = e.pendingProps, null === n ? Vl(e) : ff = n;
        }
        function Ol(e) {
            var n = e.alternate, t = (2 & e.mode) !== $u;
            switch(t && X(e), e.tag){
                case 15:
                case 0:
                    n = Ht(n, e, e.pendingProps, e.type, void 0, df);
                    break;
                case 11:
                    n = Ht(n, e, e.pendingProps, e.type.render, e.ref, df);
                    break;
                case 5:
                    an(e);
                default:
                    zr(n, e), n = er(n, e = ff = da(e, Ef), Ef);
            }
            return t && Z(e), n;
        }
        function Bl(e, n, t) {
            nr(), an(n), rs = null, ls = 0;
            var r = n.return;
            try {
                if (function(e, n, t, r, l) {
                    if (t.flags |= 32768, _u && ia(e, l), null !== r && "object" == typeof r && "function" == typeof r.then) {
                        if (null !== (n = t.alternate) && or(n, t, l, !0), null !== (t = hs.current)) {
                            switch(t.tag){
                                case 13:
                                    return null === gs ? Ml() : null === t.alternate && Pf === nf && (Pf = lf), t.flags &= -257, t.flags |= 65536, t.lanes = l, r === Fi ? t.flags |= 16384 : (null === (n = t.updateQueue) ? t.updateQueue = new Set([
                                        r
                                    ]) : n.add(r), Zl(e, r, l)), !1;
                                case 22:
                                    return t.flags |= 65536, r === Fi ? t.flags |= 16384 : (null === (n = t.updateQueue) ? (n = {
                                        transitions: null,
                                        markerInstances: null,
                                        retryQueue: new Set([
                                            r
                                        ])
                                    }, t.updateQueue = n) : null === (t = n.retryQueue) ? n.retryQueue = new Set([
                                        r
                                    ]) : t.add(r), Zl(e, r, l)), !1;
                            }
                            throw Error("Unexpected Suspense handler tag (" + t.tag + "). This is a bug in React.");
                        }
                        return Zl(e, r, l), Ml(), !1;
                    }
                    var a = D(Error("There was an error during concurrent rendering but React was able to recover by instead synchronously rendering the entire root.", {
                        cause: r
                    }), t);
                    if (null === Uf ? Uf = [
                        a
                    ] : Uf.push(a), Pf !== af && (Pf = rf), null === n) return !0;
                    r = D(r, t), t = n;
                    do {
                        switch(t.tag){
                            case 3:
                                return t.flags |= 65536, e = l & -l, t.lanes |= e, ve(t, e = _t(t.stateNode, r, e)), !1;
                            case 1:
                                if (n = t.type, a = t.stateNode, 0 == (128 & t.flags) && ("function" == typeof n.getDerivedStateFromError || null !== a && "function" == typeof a.componentDidCatch && (null === Af || !Af.has(a)))) return t.flags |= 65536, l &= -l, t.lanes |= l, Tt(l = Rt(l), e, t, r), ve(t, l), !1;
                        }
                        t = t.return;
                    }while (null !== t)
                    return !1;
                }(e, r, n, t, df)) return Pf = tf, Et(e, D(t, e.current)), void (ff = null);
            } catch (n) {
                if (null !== r) throw ff = r, n;
                return Pf = tf, Et(e, D(t, e.current)), void (ff = null);
            }
            32768 & n.flags ? $l(n, !0) : Vl(n);
        }
        function Vl(e) {
            var n = e;
            do {
                if (0 != (32768 & n.flags)) return void $l(n, zf);
                var t = n.alternate;
                if (e = n.return, X(n), t = y(n, wr, t, n, Ef), (2 & n.mode) !== $u && ee(n), null !== t) return void (ff = t);
                if (null !== (n = n.sibling)) return void (ff = n);
                ff = n = e;
            }while (null !== n)
            Pf === nf && (Pf = of);
        }
        function $l(e, n) {
            do {
                var t = xr(e.alternate, e);
                if (null !== t) return t.flags &= 32767, void (ff = t);
                if ((2 & e.mode) !== $u) {
                    ee(e), t = e.actualDuration;
                    for(var r = e.child; null !== r;)t += r.actualDuration, r = r.sibling;
                    e.actualDuration = t;
                }
                if (null !== (t = e.return) && (t.flags |= 32768, t.subtreeFlags = 0, t.deletions = null), !n && null !== (e = e.sibling)) return void (ff = e);
                ff = e = t;
            }while (null !== e)
            Pf = uf, ff = null;
        }
        function ql(e, n, t, r, l, a, o, u, i, s) {
            var c = Ja.T, f = xo();
            try {
                wo(2), Ja.T = null, function(e, n, t, r, l, a) {
                    do {
                        Jl();
                    }while (null !== Of)
                    if (xi.flushLegacyContextWarning(), xi.flushPendingUnsafeLifecycleWarnings(), (sf & (Zc | ef)) !== Xc) throw Error("Should not already be working.");
                    var o = e.finishedWork;
                    if (r = e.finishedLanes, null === o) return null;
                    if (0 === r && console.error("root.finishedLanes should not be empty during a commit. This is a bug in React."), e.finishedWork = null, e.finishedLanes = 0, o === e.current) throw Error("Cannot commit the same tree as before. This error is likely caused by a bug in React. Please file an issue.");
                    e.callbackNode = null, e.callbackPriority = 0, e.cancelPendingCommit = null;
                    var u, i, s, c = o.lanes | o.childLanes;
                    if (function(e, n, t, r) {
                        var l = e.pendingLanes;
                        e.pendingLanes = t, e.suspendedLanes = 0, e.pingedLanes = 0, e.warmLanes = 0, e.expiredLanes &= t, e.entangledLanes &= t, e.errorRecoveryDisabledLanes &= t, e.shellSuspendCounter = 0, n = e.entanglements;
                        var a = e.expirationTimes, o = e.hiddenUpdates;
                        for(t = l & ~t; 0 < t;){
                            var u = 31 - cu(t);
                            l = 1 << u, n[u] = 0, a[u] = -1;
                            var i = o[u];
                            if (null !== i) for(o[u] = null, u = 0; u < i.length; u++){
                                var s = i[u];
                                null !== s && (s.lane &= -536870913);
                            }
                            t &= ~l;
                        }
                        0 !== r && _(e, r, 0);
                    }(e, r, c |= Gu, a), e === cf && (ff = cf = null, df = 0), 0 == (10256 & o.subtreeFlags) && 0 == (10256 & o.flags) || Qf || (Qf = !0, Vf = c, $f = t, u = wu, i = function() {
                        return Jl(), null;
                    }, null !== (s = Ja.actQueue) ? s.push(i) : hu(u, i)), Ku = Ju(), t = 0 != (15990 & o.flags), 0 != (15990 & o.subtreeFlags) || t) {
                        t = Ja.T, Ja.T = null, a = xo(), wo(2);
                        var f = sf;
                        sf |= ef, function(e, n) {
                            for(io(e.containerInfo), Ac = n; null !== Ac;)if (n = (e = Ac).child, 0 != (1028 & e.subtreeFlags) && null !== n) n.return = e, Ac = n;
                            else for(; null !== Ac;){
                                var t = (n = e = Ac).alternate, r = n.flags;
                                switch(n.tag){
                                    case 0:
                                    case 11:
                                    case 15:
                                    case 5:
                                    case 26:
                                    case 27:
                                    case 6:
                                    case 4:
                                    case 17:
                                        break;
                                    case 1:
                                        0 != (1024 & r) && null !== t && Dr(n, t);
                                        break;
                                    case 3:
                                        0 != (1024 & r) && vo && Go(n.stateNode.containerInfo);
                                        break;
                                    default:
                                        if (0 != (1024 & r)) throw Error("This unit of work tag should not have side-effects. This error is likely caused by a bug in React. Please file an issue.");
                                }
                                if (null !== (n = e.sibling)) {
                                    n.return = e.return, Ac = n;
                                    break;
                                }
                                Ac = e.return;
                            }
                            e = Bc, Bc = !1;
                        }(e, o), function(e, n, t) {
                            Qc = t, Oc = e, Zr(n, e), Oc = Qc = null;
                        }(e, o, r), so(e.containerInfo), e.current = o, function(e, n, t) {
                            Qc = t, Oc = n, qr(n, e.alternate, e), Oc = Qc = null;
                        }(o, e, r), bu(), sf = f, wo(a), Ja.T = t;
                    } else e.current = o;
                    if ((t = Qf) ? (Qf = !1, Of = e, Bf = r) : (Gl(e, c), Zf = 0, ed = null), 0 === (c = e.pendingLanes) && (Af = null), t || oa(e), o.stateNode, _u && e.memoizedUpdaters.clear(), le(e), null !== n) for(l = e.onRecoverableError, o = 0; o < n.length; o++)t = Yl((c = n[o]).stack), y(c.source, l, c.value, t);
                    0 != (3 & Bf) && Jl(), c = e.pendingLanes, 0 != (4194218 & r) && 0 != (42 & c) ? (ni = !0, e === Gf ? Yf++ : (Yf = 0, Gf = e)) : Yf = 0, ae(0);
                }(e, n, t, r, f, l);
            } finally{
                Ja.T = c, wo(f);
            }
        }
        function Yl(e) {
            return e = {
                componentStack: e
            }, Object.defineProperty(e, "digest", {
                get: function() {
                    console.error('You are accessing "digest" from the errorInfo object passed to onRecoverableError. This property is no longer provided as part of errorInfo but can be accessed as a property of the Error instance itself.');
                }
            }), e;
        }
        function Gl(e, n) {
            0 == (e.pooledCacheLanes &= n) && null != (n = e.pooledCache) && (e.pooledCache = null, mr(n));
        }
        function Jl() {
            if (null !== Of) {
                var e = Of, n = Vf;
                Vf = 0;
                var t = L(Bf), r = 32 > t ? 32 : t;
                t = Ja.T;
                var l = xo();
                try {
                    if (wo(r), Ja.T = null, null === Of) var a = !1;
                    else {
                        r = $f, $f = null;
                        var o = Of, u = Bf;
                        if (Of = null, Bf = 0, (sf & (Zc | ef)) !== Xc) throw Error("Cannot flush passive effects while already rendering.");
                        Jf = !0, Kf = !1, null !== Pu && "function" == typeof Pu.markPassiveEffectsStarted && Pu.markPassiveEffectsStarted(u);
                        var i = sf;
                        sf |= ef, bl(o.current), cl(o, o.current, u, r), null !== Pu && "function" == typeof Pu.markPassiveEffectsStopped && Pu.markPassiveEffectsStopped(), oa(o), sf = i, ae(0), Kf ? o === ed ? Zf++ : (Zf = 0, ed = o) : Zf = 0, Kf = Jf = !1, Eu && Eu.onPostCommitFiberRoot;
                        var s = o.current.stateNode;
                        s.effectDuration = 0, s.passiveEffectDuration = 0, a = !0;
                    }
                    return a;
                } finally{
                    wo(l), Ja.T = t, Gl(e, n);
                }
            }
            return !1;
        }
        function Kl(e, n, t) {
            n = D(t, n), null !== (e = ye(e, n = _t(e.stateNode, n, 2), 2)) && (P(e, 2), le(e));
        }
        function Xl(e, n, t) {
            if (nd = !1, 3 === e.tag) Kl(e, e, t);
            else {
                for(; null !== n;){
                    if (3 === n.tag) return void Kl(n, e, t);
                    if (1 === n.tag) {
                        var r = n.stateNode;
                        if ("function" == typeof n.type.getDerivedStateFromError || "function" == typeof r.componentDidCatch && (null === Af || !Af.has(r))) return e = D(t, e), void (null !== (r = ye(n, t = Rt(2), 2)) && (Tt(t, r, n, e), P(r, 2), le(r)));
                    }
                    n = n.return;
                }
                console.error("Internal React error: Attempted to capture a commit phase error inside a detached tree. This indicates a bug in React. Potential causes include deleting the same fiber more than once, committing an already-finished tree, or an inconsistent return pointer.\n\nError message:\n\n%s", t);
            }
        }
        function Zl(e, n, t) {
            var r = e.pingCache;
            if (null === r) {
                r = e.pingCache = new Kc;
                var l = new Set;
                r.set(n, l);
            } else void 0 === (l = r.get(n)) && (l = new Set, r.set(n, l));
            l.has(t) || (Cf = !0, l.add(t), r = ea.bind(null, e, n, t), _u && ia(e, t), n.then(r, r));
        }
        function ea(e, n, t) {
            var r = e.pingCache;
            null !== r && r.delete(n), e.pingedLanes |= e.suspendedLanes & t, e.warmLanes &= ~t, wl() && null === Ja.actQueue && console.error("A suspended resource finished loading inside a test, but the event was not wrapped in act(...).\n\nWhen testing, code that resolves suspended data should be wrapped into act(...):\n\nact(() => {\n  /* finish loading suspended data */\n});\n/* assert on the output */\n\nThis ensures that you're testing the behavior the user would see in the browser. Learn more at https://react.dev/link/wrap-tests-with-act"), cf === e && (df & t) === t && (Pf === af || Pf === lf && (62914560 & df) === df && vu() - Ff < Mf ? (sf & Zc) === Xc && Ll(e, 0) : Tf |= t, Lf === df && (Lf = 0)), le(e);
        }
        function na(e, n) {
            0 === n && (n = C()), null !== (e = $(e, n)) && (P(e, n), le(e));
        }
        function ta(e) {
            var n = e.memoizedState, t = 0;
            null !== n && (t = n.retryLane), na(e, t);
        }
        function ra(e, n) {
            var t = 0;
            switch(e.tag){
                case 13:
                    var r = e.stateNode, l = e.memoizedState;
                    null !== l && (t = l.retryLane);
                    break;
                case 19:
                    r = e.stateNode;
                    break;
                case 22:
                    r = e.stateNode._retryCache;
                    break;
                default:
                    throw Error("Pinged unknown suspense boundary type. This is probably a bug in React.");
            }
            null !== r && r.delete(n), na(e, t);
        }
        function la(e, n, t) {
            if (0 != (33562624 & n.subtreeFlags)) for(n = n.child; null !== n;){
                var r = e, l = n, a = l.type === Fa;
                a = t || a, 22 !== l.tag ? 33554432 & l.flags ? a && y(l, aa, r, l, (64 & l.mode) === $u) : la(r, l, a) : null === l.memoizedState && (a && 8192 & l.flags ? y(l, aa, r, l) : 33554432 & l.subtreeFlags && y(l, la, r, l, a)), n = n.sibling;
            }
        }
        function aa(e, n) {
            var t = !(2 < arguments.length && void 0 !== arguments[2]) || arguments[2];
            U(!0);
            try {
                rl(n), t && Sl(n), al(e, n.alternate, n, !1), t && dl(e, n, 0, null, !1);
            } finally{
                U(!1);
            }
        }
        function oa(e) {
            var n = !0;
            24 & e.current.mode || (n = !1), la(e, e.current, n);
        }
        function ua(e) {
            if ((sf & Zc) === Xc) {
                var n = e.tag;
                if (3 === n || 1 === n || 0 === n || 11 === n || 14 === n || 15 === n) {
                    if (n = c(e) || "ReactComponent", null !== ad) {
                        if (ad.has(n)) return;
                        ad.add(n);
                    } else ad = new Set([
                        n
                    ]);
                    y(e, function() {
                        console.error("Can't perform a React state update on a component that hasn't mounted yet. This indicates that you have a side-effect in your render function that asynchronously later calls tries to update the component. Move this work to useEffect instead.");
                    });
                }
            }
        }
        function ia(e, n) {
            _u && e.memoizedUpdaters.forEach(function(t) {
                T(e, t, n);
            });
        }
        function sa(e, n, t, r) {
            this.tag = e, this.key = t, this.sibling = this.child = this.return = this.stateNode = this.type = this.elementType = null, this.index = 0, this.refCleanup = this.ref = null, this.pendingProps = n, this.dependencies = this.memoizedState = this.updateQueue = this.memoizedProps = null, this.mode = r, this.subtreeFlags = this.flags = 0, this.deletions = null, this.childLanes = this.lanes = 0, this.alternate = null, this.actualDuration = -0, this.actualStartTime = -1.1, this.treeBaseDuration = this.selfBaseDuration = -0, this._debugOwner = this._debugInfo = null, this._debugNeedsRemount = !1, this._debugHookTypes = null, id || "function" != typeof Object.preventExtensions || Object.preventExtensions(this);
        }
        function ca(e) {
            return !(!(e = e.prototype) || !e.isReactComponent);
        }
        function fa(e, n) {
            var r = e.alternate;
            switch(null === r ? ((r = t(e.tag, n, e.key, e.mode)).elementType = e.elementType, r.type = e.type, r.stateNode = e.stateNode, r._debugOwner = e._debugOwner, r._debugHookTypes = e._debugHookTypes, r.alternate = e, e.alternate = r) : (r.pendingProps = n, r.type = e.type, r.flags = 0, r.subtreeFlags = 0, r.deletions = null, r.actualDuration = -0, r.actualStartTime = -1.1), r.flags = 31457280 & e.flags, r.childLanes = e.childLanes, r.lanes = e.lanes, r.child = e.child, r.memoizedProps = e.memoizedProps, r.memoizedState = e.memoizedState, r.updateQueue = e.updateQueue, n = e.dependencies, r.dependencies = null === n ? null : {
                lanes: n.lanes,
                firstContext: n.firstContext,
                _debugThenableState: n._debugThenableState
            }, r.sibling = e.sibling, r.index = e.index, r.ref = e.ref, r.refCleanup = e.refCleanup, r.selfBaseDuration = e.selfBaseDuration, r.treeBaseDuration = e.treeBaseDuration, r._debugInfo = e._debugInfo, r._debugNeedsRemount = e._debugNeedsRemount, r.tag){
                case 0:
                case 15:
                case 1:
                case 11:
                    r.type = e.type;
            }
            return r;
        }
        function da(e, n) {
            e.flags &= 31457282;
            var t = e.alternate;
            return null === t ? (e.childLanes = 0, e.lanes = n, e.child = null, e.subtreeFlags = 0, e.memoizedProps = null, e.memoizedState = null, e.updateQueue = null, e.dependencies = null, e.stateNode = null, e.selfBaseDuration = 0, e.treeBaseDuration = 0) : (e.childLanes = t.childLanes, e.lanes = t.lanes, e.child = t.child, e.subtreeFlags = 0, e.deletions = null, e.memoizedProps = t.memoizedProps, e.memoizedState = t.memoizedState, e.updateQueue = t.updateQueue, e.type = t.type, n = t.dependencies, e.dependencies = null === n ? null : {
                lanes: n.lanes,
                firstContext: n.firstContext,
                _debugThenableState: n._debugThenableState
            }, e.selfBaseDuration = t.selfBaseDuration, e.treeBaseDuration = t.treeBaseDuration), e;
        }
        function pa(e, n, r, l, a, o) {
            var u = 0, i = e;
            if ("function" == typeof e) ca(e) && (u = 1);
            else if ("string" == typeof e) u = 5;
            else e: switch(e){
                case Ia:
                    return ha(r.children, a, o, n);
                case Fa:
                    u = 8, a |= 24;
                    break;
                case Ma:
                    return l = a, "string" != typeof (e = r).id && console.error('Profiler must specify an "id" of type `string` as a prop. Received the type `%s` instead.', typeof e.id), (n = t(12, e, n, 2 | l)).elementType = Ma, n.lanes = o, n.stateNode = {
                        effectDuration: 0,
                        passiveEffectDuration: 0
                    }, n;
                case Qa:
                    return (n = t(13, r, n, a)).elementType = Qa, n.lanes = o, n;
                case Oa:
                    return (n = t(19, r, n, a)).elementType = Oa, n.lanes = o, n;
                case $a:
                    return ga(r, a, o, n);
                default:
                    if ("object" == typeof e && null !== e) switch(e.$$typeof){
                        case Wa:
                        case ja:
                            u = 10;
                            break e;
                        case Ha:
                            u = 9;
                            break e;
                        case Aa:
                            u = 11;
                            break e;
                        case Ba:
                            u = 14;
                            break e;
                        case Va:
                            u = 16, i = null;
                            break e;
                    }
                    i = "", (void 0 === e || "object" == typeof e && null !== e && 0 === Object.keys(e).length) && (i += " You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports."), null === e ? r = "null" : lo(e) ? r = "array" : void 0 !== e && e.$$typeof === Ua ? (r = "<" + (s(e.type) || "Unknown") + " />", i = " Did you accidentally export a JSX literal instead of a component?") : r = typeof e, (u = l ? "number" == typeof l.tag ? c(l) : "string" == typeof l.name ? l.name : null : null) && (i += "\n\nCheck the render method of `" + u + "`."), u = 29, r = Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: " + r + "." + i), i = null;
            }
            return (n = t(u, r, n, a)).elementType = e, n.type = i, n.lanes = o, n._debugOwner = l, n;
        }
        function ma(e, n, t) {
            return (n = pa(e.type, e.key, e.props, e._owner, n, t))._debugOwner = e._owner, n;
        }
        function ha(e, n, r, l) {
            return (e = t(7, e, l, n)).lanes = r, e;
        }
        function ga(e, n, r, l) {
            (e = t(22, e, l, n)).elementType = $a, e.lanes = r;
            var a = {
                _visibility: 1,
                _pendingVisibility: 1,
                _pendingMarkers: null,
                _retryCache: null,
                _transitions: null,
                _current: null,
                detach: function() {
                    var e = a, n = e._current;
                    if (null === n) throw Error("Calling Offscreen.detach before instance handle has been set.");
                    if (0 == (2 & e._pendingVisibility)) {
                        var t = $(n, 2);
                        null !== t && (e._pendingVisibility |= 2, Cl(t, n, 2));
                    }
                },
                attach: function() {
                    var e = a, n = e._current;
                    if (null === n) throw Error("Calling Offscreen.detach before instance handle has been set.");
                    if (0 != (2 & e._pendingVisibility)) {
                        var t = $(n, 2);
                        null !== t && (e._pendingVisibility &= -3, Cl(t, n, 2));
                    }
                }
            };
            return e.stateNode = a, e;
        }
        function ya(e, n, r) {
            return (e = t(6, e, null, n)).lanes = r, e;
        }
        function ba(e, n, r) {
            return (n = t(4, null !== e.children ? e.children : [], e.key, n)).lanes = r, n.stateNode = {
                containerInfo: e.containerInfo,
                pendingChildren: null,
                implementation: e.implementation
            }, n;
        }
        function va(e, n, t, r, l, a, o, u) {
            for(this.tag = 1, this.containerInfo = e, this.finishedWork = this.pingCache = this.current = this.pendingChildren = null, this.timeoutHandle = bo, this.callbackNode = this.next = this.pendingContext = this.context = this.cancelPendingCommit = null, this.callbackPriority = 0, this.expirationTimes = E(-1), this.entangledLanes = this.shellSuspendCounter = this.errorRecoveryDisabledLanes = this.finishedLanes = this.expiredLanes = this.warmLanes = this.pingedLanes = this.suspendedLanes = this.pendingLanes = 0, this.entanglements = E(0), this.hiddenUpdates = E(null), this.identifierPrefix = r, this.onUncaughtError = l, this.onCaughtError = a, this.onRecoverableError = o, this.pooledCache = null, this.pooledCacheLanes = 0, this.formState = u, this.incompleteTransitions = new Map, this.passiveEffectDuration = this.effectDuration = -0, this.memoizedUpdaters = new Set, e = this.pendingUpdatersLaneMap = [], n = 0; 31 > n; n++)e.push(new Set);
            this._debugRootType = t ? "hydrateRoot()" : "createRoot()";
        }
        function Sa(e, n, t, r, l, a) {
            l = function(e) {
                return e ? e = su : su;
            }(l), null === r.context ? r.context = l : r.pendingContext = l, ro && null !== to && !cd && (cd = !0, console.error("Render methods should be a pure function of props and state; triggering nested component updates from render is not allowed. If necessary, trigger nested updates in componentDidUpdate.\n\nCheck the render method of %s.", c(to) || "Unknown")), (r = ge(n)).payload = {
                element: t
            }, null !== (a = void 0 === a ? null : a) && ("function" != typeof a && console.error("Expected the last optional `callback` argument to be a function. Instead received: %s.", a), r.callback = a), null !== (t = ye(e, r, n)) && (Cl(t, e, n), be(t, e, n));
        }
        var ka, wa, xa, za, Ca, Ea, Pa, _a = {}, Ra = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Ta = u, Na = Object.assign, La = Symbol.for("react.element"), Ua = Symbol.for("react.transitional.element"), Da = Symbol.for("react.portal"), Ia = Symbol.for("react.fragment"), Fa = Symbol.for("react.strict_mode"), Ma = Symbol.for("react.profiler"), Wa = Symbol.for("react.provider"), Ha = Symbol.for("react.consumer"), ja = Symbol.for("react.context"), Aa = Symbol.for("react.forward_ref"), Qa = Symbol.for("react.suspense"), Oa = Symbol.for("react.suspense_list"), Ba = Symbol.for("react.memo"), Va = Symbol.for("react.lazy"), $a = Symbol.for("react.offscreen"), qa = Symbol.for("react.memo_cache_sentinel"), Ya = Symbol.iterator, Ga = Symbol.for("react.client.reference"), Ja = Ra.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, Ka = 0;
        f.__reactDisabledLog = !0;
        var Xa, Za, eo = !1, no = new ("function" == typeof WeakMap ? WeakMap : Map), to = null, ro = !1, lo = Array.isArray, ao = n.getPublicInstance, oo = n.getRootHostContext, uo = n.getChildHostContext, io = n.prepareForCommit, so = n.resetAfterCommit, co = n.createInstance, fo = n.appendInitialChild, po = n.finalizeInitialChildren, mo = n.shouldSetTextContent, ho = n.createTextInstance, go = null, yo = null, bo = n.noTimeout, vo = !0, So = null, ko = null, wo = n.setCurrentUpdatePriority, xo = n.getCurrentUpdatePriority, zo = n.resolveUpdatePriority, Co = n.shouldAttemptEagerTransition, Eo = null;
        n.requestPostPaintCallback;
        var Po = n.maySuspendCommit, _o = null, Ro = null, To = null, No = null, Lo = null, Uo = null, Do = null, Io = n.appendChild, Fo = n.appendChildToContainer, Mo = n.commitTextUpdate, Wo = null, Ho = n.commitUpdate, jo = n.insertBefore, Ao = null, Qo = n.removeChild, Oo = n.removeChildFromContainer, Bo = n.resetTextContent, Vo = null, $o = null, qo = null, Yo = null, Go = n.clearContainer, Jo = null, Ko = null, Xo = null, Zo = null, eu = null, nu = null, tu = null, ru = null, lu = null, au = null, ou = [], uu = [], iu = -1, su = {};
        Object.freeze(su);
        var cu = Math.clz32 ? Math.clz32 : function(e) {
            return 0 == (e >>>= 0) ? 32 : 31 - (fu(e) / du | 0) | 0;
        }, fu = Math.log, du = Math.LN2, pu = 128, mu = 4194304, hu = Ta.unstable_scheduleCallback, gu = Ta.unstable_cancelCallback, yu = Ta.unstable_shouldYield, bu = Ta.unstable_requestPaint, vu = Ta.unstable_now, Su = Ta.unstable_ImmediatePriority, ku = Ta.unstable_UserBlockingPriority, wu = Ta.unstable_NormalPriority, xu = Ta.unstable_IdlePriority, zu = Ta.log, Cu = Ta.unstable_setDisableYieldValue, Eu = null, Pu = null, _u = "undefined" != typeof __REACT_DEVTOOLS_GLOBAL_HOOK__, Ru = "function" == typeof Object.is ? Object.is : function(e, n) {
            return e === n && (0 !== e || 1 / e == 1 / n) || e != e && n != n;
        }, Tu = new WeakMap, Nu = [], Lu = 0, Uu = null, Du = 0, Iu = [], Fu = 0, Mu = null, Wu = 1, Hu = "", ju = b(null), Au = b(null), Qu = b(null), Ou = b(null), Bu = !1, Vu = null, $u = 0, qu = [], Yu = 0, Gu = 0, Ju = Ta.unstable_now, Ku = -0, Xu = -1.1, Zu = -0, ei = !1, ni = !1, ti = null, ri = null, li = !1, ai = !1, oi = !1, ui = !1, ii = 0, si = {}, ci = null, fi = 0, di = 0, pi = null, mi = 0, hi = 1, gi = 2, yi = 3, bi = !1, vi = !1, Si = null, ki = !1, wi = Object.prototype.hasOwnProperty, xi = {
            recordUnsafeLifecycleWarnings: function() {},
            flushPendingUnsafeLifecycleWarnings: function() {},
            recordLegacyContextWarning: function() {},
            flushLegacyContextWarning: function() {},
            discardPendingWarnings: function() {}
        }, zi = [], Ci = [], Ei = [], Pi = [], _i = [], Ri = [], Ti = new Set;
        xi.recordUnsafeLifecycleWarnings = function(e, n) {
            Ti.has(e.type) || ("function" == typeof n.componentWillMount && !0 !== n.componentWillMount.__suppressDeprecationWarning && zi.push(e), 8 & e.mode && "function" == typeof n.UNSAFE_componentWillMount && Ci.push(e), "function" == typeof n.componentWillReceiveProps && !0 !== n.componentWillReceiveProps.__suppressDeprecationWarning && Ei.push(e), 8 & e.mode && "function" == typeof n.UNSAFE_componentWillReceiveProps && Pi.push(e), "function" == typeof n.componentWillUpdate && !0 !== n.componentWillUpdate.__suppressDeprecationWarning && _i.push(e), 8 & e.mode && "function" == typeof n.UNSAFE_componentWillUpdate && Ri.push(e));
        }, xi.flushPendingUnsafeLifecycleWarnings = function() {
            var e = new Set;
            0 < zi.length && (zi.forEach(function(n) {
                e.add(c(n) || "Component"), Ti.add(n.type);
            }), zi = []);
            var n = new Set;
            0 < Ci.length && (Ci.forEach(function(e) {
                n.add(c(e) || "Component"), Ti.add(e.type);
            }), Ci = []);
            var t = new Set;
            0 < Ei.length && (Ei.forEach(function(e) {
                t.add(c(e) || "Component"), Ti.add(e.type);
            }), Ei = []);
            var r = new Set;
            0 < Pi.length && (Pi.forEach(function(e) {
                r.add(c(e) || "Component"), Ti.add(e.type);
            }), Pi = []);
            var l = new Set;
            0 < _i.length && (_i.forEach(function(e) {
                l.add(c(e) || "Component"), Ti.add(e.type);
            }), _i = []);
            var a = new Set;
            if (0 < Ri.length && (Ri.forEach(function(e) {
                a.add(c(e) || "Component"), Ti.add(e.type);
            }), Ri = []), 0 < n.size) {
                var u = o(n);
                console.error("Using UNSAFE_componentWillMount in strict mode is not recommended and may indicate bugs in your code. See https://react.dev/link/unsafe-component-lifecycles for details.\n\n* Move code with side effects to componentDidMount, and set initial state in the constructor.\n\nPlease update the following components: %s", u);
            }
            0 < r.size && (u = o(r), console.error("Using UNSAFE_componentWillReceiveProps in strict mode is not recommended and may indicate bugs in your code. See https://react.dev/link/unsafe-component-lifecycles for details.\n\n* Move data fetching code or side effects to componentDidUpdate.\n* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://react.dev/link/derived-state\n\nPlease update the following components: %s", u)), 0 < a.size && (u = o(a), console.error("Using UNSAFE_componentWillUpdate in strict mode is not recommended and may indicate bugs in your code. See https://react.dev/link/unsafe-component-lifecycles for details.\n\n* Move data fetching code or side effects to componentDidUpdate.\n\nPlease update the following components: %s", u)), 0 < e.size && (u = o(e), console.warn("componentWillMount has been renamed, and is not recommended for use. See https://react.dev/link/unsafe-component-lifecycles for details.\n\n* Move code with side effects to componentDidMount, and set initial state in the constructor.\n* Rename componentWillMount to UNSAFE_componentWillMount to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run `npx react-codemod rename-unsafe-lifecycles` in your project source folder.\n\nPlease update the following components: %s", u)), 0 < t.size && (u = o(t), console.warn("componentWillReceiveProps has been renamed, and is not recommended for use. See https://react.dev/link/unsafe-component-lifecycles for details.\n\n* Move data fetching code or side effects to componentDidUpdate.\n* If you're updating state whenever props change, refactor your code to use memoization techniques or move it to static getDerivedStateFromProps. Learn more at: https://react.dev/link/derived-state\n* Rename componentWillReceiveProps to UNSAFE_componentWillReceiveProps to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run `npx react-codemod rename-unsafe-lifecycles` in your project source folder.\n\nPlease update the following components: %s", u)), 0 < l.size && (u = o(l), console.warn("componentWillUpdate has been renamed, and is not recommended for use. See https://react.dev/link/unsafe-component-lifecycles for details.\n\n* Move data fetching code or side effects to componentDidUpdate.\n* Rename componentWillUpdate to UNSAFE_componentWillUpdate to suppress this warning in non-strict mode. In React 18.x, only the UNSAFE_ name will work. To rename all deprecated lifecycles to their new names, you can run `npx react-codemod rename-unsafe-lifecycles` in your project source folder.\n\nPlease update the following components: %s", u));
        };
        var Ni = new Map, Li = new Set;
        xi.recordLegacyContextWarning = function(e, n) {
            for(var t = null, r = e; null !== r;)8 & r.mode && (t = r), r = r.return;
            null === t ? console.error("Expected to find a StrictMode component in a strict mode tree. This error is likely caused by a bug in React. Please file an issue.") : !Li.has(e.type) && (r = Ni.get(t), null != e.type.contextTypes || null != e.type.childContextTypes || null !== n && "function" == typeof n.getChildContext) && (void 0 === r && (r = [], Ni.set(t, r)), r.push(e));
        }, xi.flushLegacyContextWarning = function() {
            Ni.forEach(function(e) {
                if (0 !== e.length) {
                    var n = e[0], t = new Set;
                    e.forEach(function(e) {
                        t.add(c(e) || "Component"), Li.add(e.type);
                    });
                    var r = o(t);
                    y(n, function() {
                        console.error("Legacy context API has been detected within a strict-mode tree.\n\nThe old API will be supported in all 16.x releases, but applications using it should migrate to the new version.\n\nPlease update the following components: %s\n\nLearn more about this warning here: https://react.dev/link/legacy-context", r);
                    });
                }
            });
        }, xi.discardPendingWarnings = function() {
            zi = [], Ci = [], Ei = [], Pi = [], _i = [], Ri = [], Ni = new Map;
        };
        var Ui, Di = Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`"), Ii = Error("Suspense Exception: This is not a real error, and should not leak into userspace. If you're seeing this, it's likely a bug in React."), Fi = {
            then: function() {
                console.error('Internal React error: A listener was unexpectedly attached to a "noop" thenable. This is a bug in React. Please file an issue.');
            }
        }, Mi = null, Wi = !1, Hi = {
            "react-stack-bottom-frame": function(e, n, t) {
                var r = ro;
                ro = !0;
                try {
                    return e(n, t);
                } finally{
                    ro = r;
                }
            }
        }, ji = Hi["react-stack-bottom-frame"].bind(Hi), Ai = {
            "react-stack-bottom-frame": function(e) {
                var n = ro;
                ro = !0;
                try {
                    return e.render();
                } finally{
                    ro = n;
                }
            }
        }, Qi = Ai["react-stack-bottom-frame"].bind(Ai), Oi = {
            "react-stack-bottom-frame": function(e, n) {
                try {
                    n.componentDidMount();
                } catch (n) {
                    Xl(e, e.return, n);
                }
            }
        }, Bi = Oi["react-stack-bottom-frame"].bind(Oi), Vi = {
            "react-stack-bottom-frame": function(e, n, t, r, l) {
                try {
                    n.componentDidUpdate(t, r, l);
                } catch (n) {
                    Xl(e, e.return, n);
                }
            }
        }, $i = Vi["react-stack-bottom-frame"].bind(Vi), qi = {
            "react-stack-bottom-frame": function(e, n) {
                var t = n.stack;
                e.componentDidCatch(n.value, {
                    componentStack: null !== t ? t : ""
                });
            }
        }, Yi = qi["react-stack-bottom-frame"].bind(qi), Gi = {
            "react-stack-bottom-frame": function(e, n, t) {
                try {
                    t.componentWillUnmount();
                } catch (t) {
                    Xl(e, n, t);
                }
            }
        }, Ji = Gi["react-stack-bottom-frame"].bind(Gi), Ki = {
            "react-stack-bottom-frame": function(e) {
                var n = e.create;
                return e = e.inst, n = n(), e.destroy = n;
            }
        }, Xi = Ki["react-stack-bottom-frame"].bind(Ki), Zi = {
            "react-stack-bottom-frame": function(e, n, t) {
                try {
                    t();
                } catch (t) {
                    Xl(e, n, t);
                }
            }
        }, es = Zi["react-stack-bottom-frame"].bind(Zi), ns = {
            "react-stack-bottom-frame": function(e) {
                return (0, e._init)(e._payload);
            }
        }, ts = ns["react-stack-bottom-frame"].bind(ns), rs = null, ls = 0, as = null, os = Ui = !1, us = {}, is = {}, ss = {};
        a = function(e, n, t) {
            if (null !== t && "object" == typeof t && t._store && (!t._store.validated && null == t.key || 2 === t._store.validated)) {
                if ("object" != typeof t._store) throw Error("React Component in warnForMissingKey should have a _store. This error is likely caused by a bug in React. Please file an issue.");
                t._store.validated = 1;
                var r = c(e), l = r || "null";
                if (!us[l]) {
                    us[l] = !0, t = t._owner, e = e._debugOwner;
                    var a = "";
                    e && "number" == typeof e.tag && (l = c(e)) && (a = "\n\nCheck the render method of `" + l + "`."), a || r && (a = "\n\nCheck the top-level render call using <" + r + ">.");
                    var o = "";
                    null != t && e !== t && (r = null, "number" == typeof t.tag ? r = c(t) : "string" == typeof t.name && (r = t.name), r && (o = " It was passed a child from " + r + ".")), y(n, function() {
                        console.error('Each child in a list should have a unique "key" prop.%s%s See https://react.dev/link/warning-keys for more information.', a, o);
                    });
                }
            }
        };
        var cs, fs = We(!0), ds = We(!1), ps = b(null), ms = b(0), hs = b(null), gs = null, ys = 1, bs = 2, vs = b(0), Ss = 0, ks = 1, ws = 2, xs = 4, zs = 8, Cs = new Set, Es = new Set, Ps = new Set, _s = new Set, Rs = 0, Ts = null, Ns = null, Ls = null, Us = !1, Ds = !1, Is = !1, Fs = 0, Ms = 0, Ws = null, Hs = 0, js = 25, As = null, Qs = null, Os = -1, Bs = !1, Vs = function() {
            return {
                lastEffect: null,
                events: null,
                stores: null,
                memoCache: null
            };
        }, $s = {
            readContext: sr,
            use: cn,
            useCallback: Ke,
            useContext: Ke,
            useEffect: Ke,
            useImperativeHandle: Ke,
            useLayoutEffect: Ke,
            useInsertionEffect: Ke,
            useMemo: Ke,
            useReducer: Ke,
            useRef: Ke,
            useState: Ke,
            useDebugValue: Ke,
            useDeferredValue: Ke,
            useTransition: Ke,
            useSyncExternalStore: Ke,
            useId: Ke
        };
        $s.useCacheRefresh = Ke, $s.useMemoCache = Ke, $s.useHostTransitionStatus = Ke, $s.useFormState = Ke, $s.useActionState = Ke, $s.useOptimistic = Ke;
        var qs = null, Ys = null, Gs = null, Js = null, Ks = null, Xs = null, Zs = null;
        (qs = {
            readContext: function(e) {
                return sr(e);
            },
            use: cn,
            useCallback: function(e, n) {
                return As = "useCallback", qe(), Ge(n), Kn(e, n);
            },
            useContext: function(e) {
                return As = "useContext", qe(), sr(e);
            },
            useEffect: function(e, n) {
                return As = "useEffect", qe(), Ge(n), $n(e, n);
            },
            useImperativeHandle: function(e, n, t) {
                return As = "useImperativeHandle", qe(), Ge(t), Gn(e, n, t);
            },
            useInsertionEffect: function(e, n) {
                As = "useInsertionEffect", qe(), Ge(n), Bn(4, ws, e, n);
            },
            useLayoutEffect: function(e, n) {
                return As = "useLayoutEffect", qe(), Ge(n), qn(e, n);
            },
            useMemo: function(e, n) {
                As = "useMemo", qe(), Ge(n);
                var t = Ja.H;
                Ja.H = Ks;
                try {
                    return Zn(e, n);
                } finally{
                    Ja.H = t;
                }
            },
            useReducer: function(e, n, t) {
                As = "useReducer", qe();
                var r = Ja.H;
                Ja.H = Ks;
                try {
                    return pn(e, n, t);
                } finally{
                    Ja.H = r;
                }
            },
            useRef: function(e) {
                return As = "useRef", qe(), On(e);
            },
            useState: function(e) {
                As = "useState", qe();
                var n = Ja.H;
                Ja.H = Ks;
                try {
                    return Cn(e);
                } finally{
                    Ja.H = n;
                }
            },
            useDebugValue: function() {
                As = "useDebugValue", qe();
            },
            useDeferredValue: function(e, n) {
                return As = "useDeferredValue", qe(), nt(e, n);
            },
            useTransition: function() {
                return As = "useTransition", qe(), ut();
            },
            useSyncExternalStore: function(e, n, t) {
                return As = "useSyncExternalStore", qe(), yn(e, n, t);
            },
            useId: function() {
                return As = "useId", qe(), ft();
            },
            useCacheRefresh: function() {
                return As = "useCacheRefresh", qe(), dt();
            }
        }).useMemoCache = fn, qs.useHostTransitionStatus = ct, qs.useFormState = function(e, n) {
            return As = "useFormState", qe(), Je(), Mn(e, n);
        }, qs.useActionState = function(e, n) {
            return As = "useActionState", qe(), Mn(e, n);
        }, qs.useOptimistic = function(e) {
            return As = "useOptimistic", qe(), En(e);
        }, (Ys = {
            readContext: function(e) {
                return sr(e);
            },
            use: cn,
            useCallback: function(e, n) {
                return As = "useCallback", Ye(), Kn(e, n);
            },
            useContext: function(e) {
                return As = "useContext", Ye(), sr(e);
            },
            useEffect: function(e, n) {
                return As = "useEffect", Ye(), $n(e, n);
            },
            useImperativeHandle: function(e, n, t) {
                return As = "useImperativeHandle", Ye(), Gn(e, n, t);
            },
            useInsertionEffect: function(e, n) {
                As = "useInsertionEffect", Ye(), Bn(4, ws, e, n);
            },
            useLayoutEffect: function(e, n) {
                return As = "useLayoutEffect", Ye(), qn(e, n);
            },
            useMemo: function(e, n) {
                As = "useMemo", Ye();
                var t = Ja.H;
                Ja.H = Ks;
                try {
                    return Zn(e, n);
                } finally{
                    Ja.H = t;
                }
            },
            useReducer: function(e, n, t) {
                As = "useReducer", Ye();
                var r = Ja.H;
                Ja.H = Ks;
                try {
                    return pn(e, n, t);
                } finally{
                    Ja.H = r;
                }
            },
            useRef: function(e) {
                return As = "useRef", Ye(), On(e);
            },
            useState: function(e) {
                As = "useState", Ye();
                var n = Ja.H;
                Ja.H = Ks;
                try {
                    return Cn(e);
                } finally{
                    Ja.H = n;
                }
            },
            useDebugValue: function() {
                As = "useDebugValue", Ye();
            },
            useDeferredValue: function(e, n) {
                return As = "useDeferredValue", Ye(), nt(e, n);
            },
            useTransition: function() {
                return As = "useTransition", Ye(), ut();
            },
            useSyncExternalStore: function(e, n, t) {
                return As = "useSyncExternalStore", Ye(), yn(e, n, t);
            },
            useId: function() {
                return As = "useId", Ye(), ft();
            },
            useCacheRefresh: function() {
                return As = "useCacheRefresh", Ye(), dt();
            }
        }).useMemoCache = fn, Ys.useHostTransitionStatus = ct, Ys.useFormState = function(e, n) {
            return As = "useFormState", Ye(), Je(), Mn(e, n);
        }, Ys.useActionState = function(e, n) {
            return As = "useActionState", Ye(), Mn(e, n);
        }, Ys.useOptimistic = function(e) {
            return As = "useOptimistic", Ye(), En(e);
        }, (Gs = {
            readContext: function(e) {
                return sr(e);
            },
            use: cn,
            useCallback: function(e, n) {
                return As = "useCallback", Ye(), Xn(e, n);
            },
            useContext: function(e) {
                return As = "useContext", Ye(), sr(e);
            },
            useEffect: function(e, n) {
                As = "useEffect", Ye(), Vn(2048, zs, e, n);
            },
            useImperativeHandle: function(e, n, t) {
                return As = "useImperativeHandle", Ye(), Jn(e, n, t);
            },
            useInsertionEffect: function(e, n) {
                return As = "useInsertionEffect", Ye(), Vn(4, ws, e, n);
            },
            useLayoutEffect: function(e, n) {
                return As = "useLayoutEffect", Ye(), Vn(4, xs, e, n);
            },
            useMemo: function(e, n) {
                As = "useMemo", Ye();
                var t = Ja.H;
                Ja.H = Xs;
                try {
                    return et(e, n);
                } finally{
                    Ja.H = t;
                }
            },
            useReducer: function(e, n, t) {
                As = "useReducer", Ye();
                var r = Ja.H;
                Ja.H = Xs;
                try {
                    return mn(e);
                } finally{
                    Ja.H = r;
                }
            },
            useRef: function() {
                return As = "useRef", Ye(), un().memoizedState;
            },
            useState: function() {
                As = "useState", Ye();
                var e = Ja.H;
                Ja.H = Xs;
                try {
                    return mn(dn);
                } finally{
                    Ja.H = e;
                }
            },
            useDebugValue: function() {
                As = "useDebugValue", Ye();
            },
            useDeferredValue: function(e, n) {
                return As = "useDeferredValue", Ye(), tt(e, n);
            },
            useTransition: function() {
                return As = "useTransition", Ye(), it();
            },
            useSyncExternalStore: function(e, n, t) {
                return As = "useSyncExternalStore", Ye(), bn(e, n, t);
            },
            useId: function() {
                return As = "useId", Ye(), un().memoizedState;
            },
            useCacheRefresh: function() {
                return As = "useCacheRefresh", Ye(), un().memoizedState;
            }
        }).useMemoCache = fn, Gs.useHostTransitionStatus = ct, Gs.useFormState = function(e) {
            return As = "useFormState", Ye(), Je(), Wn(e);
        }, Gs.useActionState = function(e) {
            return As = "useActionState", Ye(), Wn(e);
        }, Gs.useOptimistic = function(e, n) {
            return As = "useOptimistic", Ye(), Pn(e, n);
        }, (Js = {
            readContext: function(e) {
                return sr(e);
            },
            use: cn,
            useCallback: function(e, n) {
                return As = "useCallback", Ye(), Xn(e, n);
            },
            useContext: function(e) {
                return As = "useContext", Ye(), sr(e);
            },
            useEffect: function(e, n) {
                As = "useEffect", Ye(), Vn(2048, zs, e, n);
            },
            useImperativeHandle: function(e, n, t) {
                return As = "useImperativeHandle", Ye(), Jn(e, n, t);
            },
            useInsertionEffect: function(e, n) {
                return As = "useInsertionEffect", Ye(), Vn(4, ws, e, n);
            },
            useLayoutEffect: function(e, n) {
                return As = "useLayoutEffect", Ye(), Vn(4, xs, e, n);
            },
            useMemo: function(e, n) {
                As = "useMemo", Ye();
                var t = Ja.H;
                Ja.H = Zs;
                try {
                    return et(e, n);
                } finally{
                    Ja.H = t;
                }
            },
            useReducer: function(e, n, t) {
                As = "useReducer", Ye();
                var r = Ja.H;
                Ja.H = Zs;
                try {
                    return gn(e);
                } finally{
                    Ja.H = r;
                }
            },
            useRef: function() {
                return As = "useRef", Ye(), un().memoizedState;
            },
            useState: function() {
                As = "useState", Ye();
                var e = Ja.H;
                Ja.H = Zs;
                try {
                    return gn(dn);
                } finally{
                    Ja.H = e;
                }
            },
            useDebugValue: function() {
                As = "useDebugValue", Ye();
            },
            useDeferredValue: function(e, n) {
                return As = "useDeferredValue", Ye(), rt(e, n);
            },
            useTransition: function() {
                return As = "useTransition", Ye(), st();
            },
            useSyncExternalStore: function(e, n, t) {
                return As = "useSyncExternalStore", Ye(), bn(e, n, t);
            },
            useId: function() {
                return As = "useId", Ye(), un().memoizedState;
            },
            useCacheRefresh: function() {
                return As = "useCacheRefresh", Ye(), un().memoizedState;
            }
        }).useMemoCache = fn, Js.useHostTransitionStatus = ct, Js.useFormState = function(e) {
            return As = "useFormState", Ye(), Je(), An(e);
        }, Js.useActionState = function(e) {
            return As = "useActionState", Ye(), An(e);
        }, Js.useOptimistic = function(e, n) {
            return As = "useOptimistic", Ye(), Rn(e, n);
        }, (Ks = {
            readContext: function(e) {
                return l(), sr(e);
            },
            use: function(e) {
                return r(), cn(e);
            },
            useCallback: function(e, n) {
                return As = "useCallback", r(), qe(), Kn(e, n);
            },
            useContext: function(e) {
                return As = "useContext", r(), qe(), sr(e);
            },
            useEffect: function(e, n) {
                return As = "useEffect", r(), qe(), $n(e, n);
            },
            useImperativeHandle: function(e, n, t) {
                return As = "useImperativeHandle", r(), qe(), Gn(e, n, t);
            },
            useInsertionEffect: function(e, n) {
                As = "useInsertionEffect", r(), qe(), Bn(4, ws, e, n);
            },
            useLayoutEffect: function(e, n) {
                return As = "useLayoutEffect", r(), qe(), qn(e, n);
            },
            useMemo: function(e, n) {
                As = "useMemo", r(), qe();
                var t = Ja.H;
                Ja.H = Ks;
                try {
                    return Zn(e, n);
                } finally{
                    Ja.H = t;
                }
            },
            useReducer: function(e, n, t) {
                As = "useReducer", r(), qe();
                var l = Ja.H;
                Ja.H = Ks;
                try {
                    return pn(e, n, t);
                } finally{
                    Ja.H = l;
                }
            },
            useRef: function(e) {
                return As = "useRef", r(), qe(), On(e);
            },
            useState: function(e) {
                As = "useState", r(), qe();
                var n = Ja.H;
                Ja.H = Ks;
                try {
                    return Cn(e);
                } finally{
                    Ja.H = n;
                }
            },
            useDebugValue: function() {
                As = "useDebugValue", r(), qe();
            },
            useDeferredValue: function(e, n) {
                return As = "useDeferredValue", r(), qe(), nt(e, n);
            },
            useTransition: function() {
                return As = "useTransition", r(), qe(), ut();
            },
            useSyncExternalStore: function(e, n, t) {
                return As = "useSyncExternalStore", r(), qe(), yn(e, n, t);
            },
            useId: function() {
                return As = "useId", r(), qe(), ft();
            },
            useCacheRefresh: function() {
                return As = "useCacheRefresh", qe(), dt();
            },
            useMemoCache: function(e) {
                return r(), fn(e);
            }
        }).useHostTransitionStatus = ct, Ks.useFormState = function(e, n) {
            return As = "useFormState", r(), qe(), Mn(e, n);
        }, Ks.useActionState = function(e, n) {
            return As = "useActionState", r(), qe(), Mn(e, n);
        }, Ks.useOptimistic = function(e) {
            return As = "useOptimistic", r(), qe(), En(e);
        }, (Xs = {
            readContext: function(e) {
                return l(), sr(e);
            },
            use: function(e) {
                return r(), cn(e);
            },
            useCallback: function(e, n) {
                return As = "useCallback", r(), Ye(), Xn(e, n);
            },
            useContext: function(e) {
                return As = "useContext", r(), Ye(), sr(e);
            },
            useEffect: function(e, n) {
                As = "useEffect", r(), Ye(), Vn(2048, zs, e, n);
            },
            useImperativeHandle: function(e, n, t) {
                return As = "useImperativeHandle", r(), Ye(), Jn(e, n, t);
            },
            useInsertionEffect: function(e, n) {
                return As = "useInsertionEffect", r(), Ye(), Vn(4, ws, e, n);
            },
            useLayoutEffect: function(e, n) {
                return As = "useLayoutEffect", r(), Ye(), Vn(4, xs, e, n);
            },
            useMemo: function(e, n) {
                As = "useMemo", r(), Ye();
                var t = Ja.H;
                Ja.H = Xs;
                try {
                    return et(e, n);
                } finally{
                    Ja.H = t;
                }
            },
            useReducer: function(e, n, t) {
                As = "useReducer", r(), Ye();
                var l = Ja.H;
                Ja.H = Xs;
                try {
                    return mn(e);
                } finally{
                    Ja.H = l;
                }
            },
            useRef: function() {
                return As = "useRef", r(), Ye(), un().memoizedState;
            },
            useState: function() {
                As = "useState", r(), Ye();
                var e = Ja.H;
                Ja.H = Xs;
                try {
                    return mn(dn);
                } finally{
                    Ja.H = e;
                }
            },
            useDebugValue: function() {
                As = "useDebugValue", r(), Ye();
            },
            useDeferredValue: function(e, n) {
                return As = "useDeferredValue", r(), Ye(), tt(e, n);
            },
            useTransition: function() {
                return As = "useTransition", r(), Ye(), it();
            },
            useSyncExternalStore: function(e, n, t) {
                return As = "useSyncExternalStore", r(), Ye(), bn(e, n, t);
            },
            useId: function() {
                return As = "useId", r(), Ye(), un().memoizedState;
            },
            useCacheRefresh: function() {
                return As = "useCacheRefresh", Ye(), un().memoizedState;
            },
            useMemoCache: function(e) {
                return r(), fn(e);
            }
        }).useHostTransitionStatus = ct, Xs.useFormState = function(e) {
            return As = "useFormState", r(), Ye(), Wn(e);
        }, Xs.useActionState = function(e) {
            return As = "useActionState", r(), Ye(), Wn(e);
        }, Xs.useOptimistic = function(e, n) {
            return As = "useOptimistic", r(), Ye(), Pn(e, n);
        }, (Zs = {
            readContext: function(e) {
                return l(), sr(e);
            },
            use: function(e) {
                return r(), cn(e);
            },
            useCallback: function(e, n) {
                return As = "useCallback", r(), Ye(), Xn(e, n);
            },
            useContext: function(e) {
                return As = "useContext", r(), Ye(), sr(e);
            },
            useEffect: function(e, n) {
                As = "useEffect", r(), Ye(), Vn(2048, zs, e, n);
            },
            useImperativeHandle: function(e, n, t) {
                return As = "useImperativeHandle", r(), Ye(), Jn(e, n, t);
            },
            useInsertionEffect: function(e, n) {
                return As = "useInsertionEffect", r(), Ye(), Vn(4, ws, e, n);
            },
            useLayoutEffect: function(e, n) {
                return As = "useLayoutEffect", r(), Ye(), Vn(4, xs, e, n);
            },
            useMemo: function(e, n) {
                As = "useMemo", r(), Ye();
                var t = Ja.H;
                Ja.H = Xs;
                try {
                    return et(e, n);
                } finally{
                    Ja.H = t;
                }
            },
            useReducer: function(e, n, t) {
                As = "useReducer", r(), Ye();
                var l = Ja.H;
                Ja.H = Xs;
                try {
                    return gn(e);
                } finally{
                    Ja.H = l;
                }
            },
            useRef: function() {
                return As = "useRef", r(), Ye(), un().memoizedState;
            },
            useState: function() {
                As = "useState", r(), Ye();
                var e = Ja.H;
                Ja.H = Xs;
                try {
                    return gn(dn);
                } finally{
                    Ja.H = e;
                }
            },
            useDebugValue: function() {
                As = "useDebugValue", r(), Ye();
            },
            useDeferredValue: function(e, n) {
                return As = "useDeferredValue", r(), Ye(), rt(e, n);
            },
            useTransition: function() {
                return As = "useTransition", r(), Ye(), st();
            },
            useSyncExternalStore: function(e, n, t) {
                return As = "useSyncExternalStore", r(), Ye(), bn(e, n, t);
            },
            useId: function() {
                return As = "useId", r(), Ye(), un().memoizedState;
            },
            useCacheRefresh: function() {
                return As = "useCacheRefresh", Ye(), un().memoizedState;
            },
            useMemoCache: function(e) {
                return r(), fn(e);
            }
        }).useHostTransitionStatus = ct, Zs.useFormState = function(e) {
            return As = "useFormState", r(), Ye(), An(e);
        }, Zs.useActionState = function(e) {
            return As = "useActionState", r(), Ye(), An(e);
        }, Zs.useOptimistic = function(e, n) {
            return As = "useOptimistic", r(), Ye(), Rn(e, n);
        };
        var ec = {}, nc = new Set, tc = new Set, rc = new Set, lc = new Set, ac = new Set, oc = new Set, uc = new Set, ic = new Set, sc = new Set, cc = new Set;
        Object.freeze(ec);
        var fc = {
            isMounted: function(e) {
                var n = to;
                if (null !== n && ro && 1 === n.tag) {
                    var t = n.stateNode;
                    t._warnedAboutRefsInRender || console.error("%s is accessing isMounted inside its render() function. render() should be a pure function of props and state. It should never access something that requires stale data from the previous render, such as refs. Move this logic to componentDidMount and componentDidUpdate instead.", c(n) || "A component"), t._warnedAboutRefsInRender = !0;
                }
                return !!(e = e._reactInternals) && function(e) {
                    var n = e, t = e;
                    if (e.alternate) for(; n.return;)n = n.return;
                    else {
                        e = n;
                        do {
                            0 != (4098 & (n = e).flags) && (t = n.return), e = n.return;
                        }while (e)
                    }
                    return 3 === n.tag ? t : null;
                }(e) === e;
            },
            enqueueSetState: function(e, n, t) {
                var r = xl(e = e._reactInternals), l = ge(r);
                l.payload = n, null != t && (kt(t), l.callback = t), null !== (n = ye(e, l, r)) && (Cl(n, e, r), be(n, e, r));
            },
            enqueueReplaceState: function(e, n, t) {
                var r = xl(e = e._reactInternals), l = ge(r);
                l.tag = hi, l.payload = n, null != t && (kt(t), l.callback = t), null !== (n = ye(e, l, r)) && (Cl(n, e, r), be(n, e, r));
            },
            enqueueForceUpdate: function(e, n) {
                var t = xl(e = e._reactInternals), r = ge(t);
                r.tag = gi, null != n && (kt(n), r.callback = n), null !== (n = ye(e, r, t)) && (Cl(n, e, t), be(n, e, t));
            }
        };
        "function" == typeof reportError && reportError;
        var dc = Error("This is not a real error. It's an implementation detail of React's selective hydration feature. If this leaks into userspace, it's a bug in React. Please file an issue."), pc = !1, mc = {}, hc = {}, gc = {}, yc = {}, bc = !1, vc = {}, Sc = {}, kc = {
            dehydrated: null,
            treeContext: null,
            retryLane: 0
        }, wc = !1, xc = b(null), zc = b(null), Cc = {}, Ec = null, Pc = null, _c = null, Rc = !1, Tc = "undefined" != typeof AbortController ? AbortController : function() {
            var e = [], n = this.signal = {
                aborted: !1,
                addEventListener: function(n, t) {
                    e.push(t);
                }
            };
            this.abort = function() {
                n.aborted = !0, e.forEach(function(e) {
                    return e();
                });
            };
        }, Nc = Ta.unstable_scheduleCallback, Lc = Ta.unstable_NormalPriority, Uc = {
            $$typeof: ja,
            Consumer: null,
            Provider: null,
            _currentValue: null,
            _currentValue2: null,
            _threadCount: 0,
            _currentRenderer: null,
            _currentRenderer2: null
        }, Dc = Ja.S;
        Ja.S = function(e, n) {
            "object" == typeof n && null !== n && "function" == typeof n.then && function(e, n) {
                if (null === ci) {
                    var t = ci = [];
                    fi = 0, di = de(), pi = {
                        status: "pending",
                        value: void 0,
                        then: function(e) {
                            t.push(e);
                        }
                    };
                }
                fi++, n.then(pe, pe);
            }(0, n), null !== Dc && Dc(e, n);
        };
        var Ic = b(null), Fc = null;
        Fc = new Set;
        var Mc = !1, Wc = !1, Hc = !1, jc = "function" == typeof WeakSet ? WeakSet : Set, Ac = null, Qc = null, Oc = null, Bc = !1, Vc = null, $c = !1, qc = null, Yc = 8192, Gc = {
            getCacheForType: function(e) {
                var n = sr(Uc), t = n.data.get(e);
                return void 0 === t && (t = e(), n.data.set(e, t)), t;
            },
            getOwner: function() {
                return to;
            }
        };
        if ("function" == typeof Symbol && Symbol.for) {
            var Jc = Symbol.for;
            Jc("selector.component"), Jc("selector.has_pseudo_class"), Jc("selector.role"), Jc("selector.test_id"), Jc("selector.text");
        }
        var Kc = "function" == typeof WeakMap ? WeakMap : Map, Xc = 0, Zc = 2, ef = 4, nf = 0, tf = 1, rf = 2, lf = 3, af = 4, of = 5, uf = 6, sf = Xc, cf = null, ff = null, df = 0, pf = 0, mf = 1, hf = 2, gf = 3, yf = 4, bf = 5, vf = 6, Sf = 7, kf = 8, wf = pf, xf = null, zf = !1, Cf = !1, Ef = 0, Pf = nf, _f = 0, Rf = 0, Tf = 0, Nf = 0, Lf = 0, Uf = null, Df = null, If = !1, Ff = 0, Mf = 300, Wf = 1 / 0, Hf = 500, jf = null, Af = null, Qf = !1, Of = null, Bf = 0, Vf = 0, $f = null, qf = 50, Yf = 0, Gf = null, Jf = !1, Kf = !1, Xf = 50, Zf = 0, ed = null, nd = !1, td = 0, rd = 1, ld = 2, ad = null, od = !1, ud = new Set, id = !1;
        try {
            var sd = Object.preventExtensions({});
            new Map([
                [
                    sd,
                    null
                ]
            ]), new Set([
                sd
            ]);
        } catch (e) {
            id = !0;
        }
        var cd = !1;
        return _a.createContainer = function(e, n, r, l, a, o, u, i, s, c) {
            return function(e, n, r, l, a, o, u, i, s, c, f, d) {
                return e = new va(e, n, r, u, i, s, c, null), n = 1, !0 === o && (n |= 24), _u && (n |= 2), o = t(3, null, null, n), e.current = o, o.stateNode = e, pr(n = dr()), e.pooledCache = n, pr(n), o.memoizedState = {
                    element: l,
                    isDehydrated: r,
                    cache: n
                }, me(o), e;
            }(e, n, !1, null, 0, l, o, u, i, s);
        }, _a.flushSyncWork = function() {
            return (sf & (Zc | ef)) !== Xc || (ae(0), !1);
        }, _a.updateContainer = function(e, n, t, r) {
            var l = n.current, a = xl(l);
            return Sa(l, a, e, n, t, r), a;
        }, _a.updateContainerSync = function(e, n, t, r) {
            return 0 === n.tag && Jl(), Sa(n.current, 2, e, n, t, r), 2;
        }, _a;
    }, n.exports.default = n.exports, Object.defineProperty(n.exports, "__esModule", {
        value: !0
    }))), c.exports;
    "TURBOPACK unreachable";
    var n;
}();
var f, d = t(a.exports), p = {
    exports: {}
}, m = {};
var h, g = {};
/**
 * @license React
 * react-reconciler-constants.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : p.exports = (h || (h = 1, "production" !== ("TURBOPACK compile-time value", "development") && (g.ConcurrentRoot = 1, g.ContinuousEventPriority = 8, g.DefaultEventPriority = 32, g.DiscreteEventPriority = 2, g.IdleEventPriority = 268435456, g.LegacyRoot = 0, g.NoEventPriority = 0)), g);
var y = p.exports;
const b = (e, n)=>{
    const t = Object.keys(e), r = Object.keys(n);
    if (t.length !== r.length) return !1;
    for(let r = 0; r < t.length; r += 1){
        const l = t[r];
        if ("render" === l && !e[l] != !n[l]) return !1;
        if ("children" !== l && e[l] !== n[l]) {
            if ("object" == typeof e[l] && "object" == typeof n[l] && b(e[l], n[l])) continue;
            return !1;
        }
        if ("children" === l && ("string" == typeof e[l] || "string" == typeof n[l])) return e[l] === n[l];
    }
    return !0;
}, v = {}, S = console.error, k = ({ appendChild: e, appendChildToContainer: n, commitTextUpdate: t, commitUpdate: r, createInstance: l, createTextInstance: a, insertBefore: o, removeChild: u, removeChildFromContainer: i, resetAfterCommit: s })=>{
    const c = d({
        appendChild: e,
        appendChildToContainer: n,
        appendInitialChild: e,
        createInstance: l,
        createTextInstance: a,
        insertBefore: o,
        commitUpdate: (e, n, t, l)=>{
            b(t, l) || r(e, null, n, t, l);
        },
        commitTextUpdate: t,
        removeChild: u,
        removeChildFromContainer: i,
        resetAfterCommit: s,
        noTimeout: -1,
        shouldSetTextContent: ()=>!1,
        finalizeInitialChildren: ()=>!1,
        getPublicInstance: (e)=>e,
        getRootHostContext: ()=>v,
        getChildHostContext: ()=>v,
        prepareForCommit () {},
        clearContainer () {},
        resetTextContent () {},
        getCurrentUpdatePriority: ()=>y.DefaultEventPriority,
        maySuspendCommit: ()=>!1,
        requestPostPaintCallback: ()=>{},
        resolveUpdatePriority: ()=>y.DefaultEventPriority,
        setCurrentUpdatePriority: ()=>{},
        shouldAttemptEagerTransition: ()=>!1
    });
    return {
        createContainer: (e)=>c.createContainer(e, y.ConcurrentRoot, null, !1, null, "", S, S, S, null),
        updateContainer: (e, n, t, r)=>{
            c.updateContainerSync(e, n, t, r), c.flushSyncWork();
        }
    };
};
;
}}),
"[project]/node_modules/@react-pdf/reconciler/lib/reconciler-23.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>s)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$object$2d$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/object-assign.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$scheduler$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/scheduler/index.js [app-client] (ecmascript)");
;
;
;
function r(e) {
    return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
function l(e) {
    if (e.__esModule) return e;
    var t = e.default;
    if ("function" == typeof t) {
        var n = function e() {
            return this instanceof e ? Reflect.construct(t, arguments, this.constructor) : t.apply(this, arguments);
        };
        n.prototype = t.prototype;
    } else n = {};
    return Object.defineProperty(n, "__esModule", {
        value: !0
    }), Object.keys(e).forEach(function(t) {
        var r = Object.getOwnPropertyDescriptor(e, t);
        Object.defineProperty(n, t, r.get ? r : {
            enumerable: !0,
            get: function() {
                return e[t];
            }
        });
    }), n;
}
var i, a = {
    exports: {}
}, u = l(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$scheduler$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__);
(/** @license React v0.23.0
 * react-reconciler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ i = a).exports = function n(r) {
    var l = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$object$2d$assign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], a = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], o = u;
    function f(e) {
        for(var t = "https://reactjs.org/docs/error-decoder.html?invariant=" + e, n = 1; n < arguments.length; n++)t += "&args[]=" + encodeURIComponent(arguments[n]);
        return "Minified React error #" + e + "; visit " + t + " for the full message or use the non-minified dev environment for full errors and additional helpful warnings.";
    }
    var c = a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;
    c.hasOwnProperty("ReactCurrentDispatcher") || (c.ReactCurrentDispatcher = {
        current: null
    }), c.hasOwnProperty("ReactCurrentBatchConfig") || (c.ReactCurrentBatchConfig = {
        suspense: null
    });
    var s = "function" == typeof Symbol && Symbol.for, d = s ? Symbol.for("react.element") : 60103, p = s ? Symbol.for("react.portal") : 60106, m = s ? Symbol.for("react.fragment") : 60107, h = s ? Symbol.for("react.strict_mode") : 60108, g = s ? Symbol.for("react.profiler") : 60114, b = s ? Symbol.for("react.provider") : 60109, y = s ? Symbol.for("react.context") : 60110, v = s ? Symbol.for("react.concurrent_mode") : 60111, T = s ? Symbol.for("react.forward_ref") : 60112, x = s ? Symbol.for("react.suspense") : 60113, E = s ? Symbol.for("react.suspense_list") : 60120, k = s ? Symbol.for("react.memo") : 60115, S = s ? Symbol.for("react.lazy") : 60116, C = "function" == typeof Symbol && Symbol.iterator;
    function w(e) {
        return null === e || "object" != typeof e ? null : "function" == typeof (e = C && e[C] || e["@@iterator"]) ? e : null;
    }
    function z(e) {
        if (null == e) return null;
        if ("function" == typeof e) return e.displayName || e.name || null;
        if ("string" == typeof e) return e;
        switch(e){
            case m:
                return "Fragment";
            case p:
                return "Portal";
            case g:
                return "Profiler";
            case h:
                return "StrictMode";
            case x:
                return "Suspense";
            case E:
                return "SuspenseList";
        }
        if ("object" == typeof e) switch(e.$$typeof){
            case y:
                return "Context.Consumer";
            case b:
                return "Context.Provider";
            case T:
                var t = e.render;
                return t = t.displayName || t.name || "", e.displayName || ("" !== t ? "ForwardRef(" + t + ")" : "ForwardRef");
            case k:
                return z(e.type);
            case S:
                if (e = 1 === e._status ? e._result : null) return z(e);
        }
        return null;
    }
    function P(e) {
        var t = e, n = e;
        if (e.alternate) for(; t.return;)t = t.return;
        else {
            e = t;
            do {
                0 != (1026 & (t = e).effectTag) && (n = t.return), e = t.return;
            }while (e)
        }
        return 3 === t.tag ? n : null;
    }
    function _(e) {
        if (P(e) !== e) throw Error(f(188));
    }
    function N(e) {
        var t = e.alternate;
        if (!t) {
            if (null === (t = P(e))) throw Error(f(188));
            return t !== e ? null : e;
        }
        for(var n = e, r = t;;){
            var l = n.return;
            if (null === l) break;
            var i = l.alternate;
            if (null === i) {
                if (null !== (r = l.return)) {
                    n = r;
                    continue;
                }
                break;
            }
            if (l.child === i.child) {
                for(i = l.child; i;){
                    if (i === n) return _(l), e;
                    if (i === r) return _(l), t;
                    i = i.sibling;
                }
                throw Error(f(188));
            }
            if (n.return !== r.return) n = l, r = i;
            else {
                for(var a = !1, u = l.child; u;){
                    if (u === n) {
                        a = !0, n = l, r = i;
                        break;
                    }
                    if (u === r) {
                        a = !0, r = l, n = i;
                        break;
                    }
                    u = u.sibling;
                }
                if (!a) {
                    for(u = i.child; u;){
                        if (u === n) {
                            a = !0, n = i, r = l;
                            break;
                        }
                        if (u === r) {
                            a = !0, r = i, n = l;
                            break;
                        }
                        u = u.sibling;
                    }
                    if (!a) throw Error(f(189));
                }
            }
            if (n.alternate !== r) throw Error(f(190));
        }
        if (3 !== n.tag) throw Error(f(188));
        return n.stateNode.current === n ? e : t;
    }
    function U(e) {
        if (!(e = N(e))) return null;
        for(var t = e;;){
            if (5 === t.tag || 6 === t.tag) return t;
            if (t.child) t.child.return = t, t = t.child;
            else {
                if (t === e) break;
                for(; !t.sibling;){
                    if (!t.return || t.return === e) return null;
                    t = t.return;
                }
                t.sibling.return = t.return, t = t.sibling;
            }
        }
        return null;
    }
    var R = r.getPublicInstance, I = r.getRootHostContext, M = r.getChildHostContext, F = r.prepareForCommit, Q = r.resetAfterCommit, D = r.createInstance, W = r.appendInitialChild, j = r.finalizeInitialChildren, O = r.prepareUpdate, B = r.shouldSetTextContent, H = r.createTextInstance, A = null, L = null, $ = r.noTimeout, q = !0, V = null, K = r.appendChild, G = r.appendChildToContainer, Y = r.commitTextUpdate, J = null, X = r.commitUpdate, Z = r.insertBefore, ee = null, te = r.removeChild, ne = r.removeChildFromContainer, re = r.resetTextContent, le = null, ie = null, ae = null, ue = null, oe = null, fe = null, ce = /^(.*)[\\\/]/;
    function se(e) {
        var t = "";
        do {
            e: switch(e.tag){
                case 3:
                case 4:
                case 6:
                case 7:
                case 10:
                case 9:
                    var n = "";
                    break e;
                default:
                    var r = e._debugOwner, l = e._debugSource, i = z(e.type);
                    n = null, r && (n = z(r.type)), r = i, i = "", l ? i = " (at " + l.fileName.replace(ce, "") + ":" + l.lineNumber + ")" : n && (i = " (created by " + n + ")"), n = "\n    in " + (r || "Unknown") + i;
            }
            t += n, e = e.return;
        }while (e)
        return t;
    }
    var de = [], pe = -1;
    function me(e) {
        0 > pe || (e.current = de[pe], de[pe] = null, pe--);
    }
    function he(e, t) {
        pe++, de[pe] = e.current, e.current = t;
    }
    var ge = {}, be = {
        current: ge
    }, ye = {
        current: !1
    }, ve = ge;
    function Te(e, t) {
        var n = e.type.contextTypes;
        if (!n) return ge;
        var r = e.stateNode;
        if (r && r.__reactInternalMemoizedUnmaskedChildContext === t) return r.__reactInternalMemoizedMaskedChildContext;
        var l, i = {};
        for(l in n)i[l] = t[l];
        return r && ((e = e.stateNode).__reactInternalMemoizedUnmaskedChildContext = t, e.__reactInternalMemoizedMaskedChildContext = i), i;
    }
    function xe(e) {
        return null != (e = e.childContextTypes);
    }
    function Ee(e) {
        me(ye), me(be);
    }
    function ke(e) {
        me(ye), me(be);
    }
    function Se(e, t, n) {
        if (be.current !== ge) throw Error(f(168));
        he(be, t), he(ye, n);
    }
    function Ce(e, t, n) {
        var r = e.stateNode;
        if (e = t.childContextTypes, "function" != typeof r.getChildContext) return n;
        for(var i in r = r.getChildContext())if (!(i in e)) throw Error(f(108, z(t) || "Unknown", i));
        return l({}, n, {}, r);
    }
    function we(e) {
        var t = e.stateNode;
        return t = t && t.__reactInternalMemoizedMergedChildContext || ge, ve = be.current, he(be, t), he(ye, ye.current), !0;
    }
    function ze(e, t, n) {
        var r = e.stateNode;
        if (!r) throw Error(f(169));
        n ? (t = Ce(e, t, ve), r.__reactInternalMemoizedMergedChildContext = t, me(ye), me(be), he(be, t)) : me(ye), he(ye, n);
    }
    var Pe = o.unstable_runWithPriority, _e = o.unstable_scheduleCallback, Ne = o.unstable_cancelCallback, Ue = o.unstable_shouldYield, Re = o.unstable_requestPaint, Ie = o.unstable_now, Me = o.unstable_getCurrentPriorityLevel, Fe = o.unstable_ImmediatePriority, Qe = o.unstable_UserBlockingPriority, De = o.unstable_NormalPriority, We = o.unstable_LowPriority, je = o.unstable_IdlePriority, Oe = {}, Be = void 0 !== Re ? Re : function() {}, He = null, Ae = null, Le = !1, $e = Ie(), qe = 1e4 > $e ? Ie : function() {
        return Ie() - $e;
    };
    function Ve() {
        switch(Me()){
            case Fe:
                return 99;
            case Qe:
                return 98;
            case De:
                return 97;
            case We:
                return 96;
            case je:
                return 95;
            default:
                throw Error(f(332));
        }
    }
    function Ke(e) {
        switch(e){
            case 99:
                return Fe;
            case 98:
                return Qe;
            case 97:
                return De;
            case 96:
                return We;
            case 95:
                return je;
            default:
                throw Error(f(332));
        }
    }
    function Ge(e, t) {
        return e = Ke(e), Pe(e, t);
    }
    function Ye(e, t, n) {
        return e = Ke(e), _e(e, t, n);
    }
    function Je(e) {
        return null === He ? (He = [
            e
        ], Ae = _e(Fe, Ze)) : He.push(e), Oe;
    }
    function Xe() {
        if (null !== Ae) {
            var e = Ae;
            Ae = null, Ne(e);
        }
        Ze();
    }
    function Ze() {
        if (!Le && null !== He) {
            Le = !0;
            var e = 0;
            try {
                var t = He;
                Ge(99, function() {
                    for(; e < t.length; e++){
                        var n = t[e];
                        do {
                            n = n(!0);
                        }while (null !== n)
                    }
                }), He = null;
            } catch (t) {
                throw null !== He && (He = He.slice(e + 1)), _e(Fe, Xe), t;
            } finally{
                Le = !1;
            }
        }
    }
    var et = 3;
    function tt(e, t, n) {
        return 1073741821 - (1 + ((1073741821 - e + t / 10) / (n /= 10) | 0)) * n;
    }
    var nt = "function" == typeof Object.is ? Object.is : function(e, t) {
        return e === t && (0 !== e || 1 / e == 1 / t) || e != e && t != t;
    }, rt = Object.prototype.hasOwnProperty;
    function lt(e, t) {
        if (nt(e, t)) return !0;
        if ("object" != typeof e || null === e || "object" != typeof t || null === t) return !1;
        var n = Object.keys(e), r = Object.keys(t);
        if (n.length !== r.length) return !1;
        for(r = 0; r < n.length; r++)if (!rt.call(t, n[r]) || !nt(e[n[r]], t[n[r]])) return !1;
        return !0;
    }
    function it(e, t) {
        if (e && e.defaultProps) for(var n in t = l({}, t), e = e.defaultProps)void 0 === t[n] && (t[n] = e[n]);
        return t;
    }
    var at = {
        current: null
    }, ut = null, ot = null, ft = null;
    function ct() {
        ft = ot = ut = null;
    }
    function st(e, t) {
        var n = e.type._context;
        he(at, n._currentValue2), n._currentValue2 = t;
    }
    function dt(e) {
        var t = at.current;
        me(at), (e = e.type._context)._currentValue2 = t;
    }
    function pt(e, t) {
        for(; null !== e;){
            var n = e.alternate;
            if (e.childExpirationTime < t) e.childExpirationTime = t, null !== n && n.childExpirationTime < t && (n.childExpirationTime = t);
            else {
                if (!(null !== n && n.childExpirationTime < t)) break;
                n.childExpirationTime = t;
            }
            e = e.return;
        }
    }
    function mt(e, t) {
        ut = e, ft = ot = null, null !== (e = e.dependencies) && null !== e.firstContext && (e.expirationTime >= t && (On = !0), e.firstContext = null);
    }
    function ht(e, t) {
        if (ft !== e && !1 !== t && 0 !== t) if ("number" == typeof t && 1073741823 !== t || (ft = e, t = 1073741823), t = {
            context: e,
            observedBits: t,
            next: null
        }, null === ot) {
            if (null === ut) throw Error(f(308));
            ot = t, ut.dependencies = {
                expirationTime: 0,
                firstContext: t,
                responders: null
            };
        } else ot = ot.next = t;
        return e._currentValue2;
    }
    var gt = !1;
    function bt(e) {
        return {
            baseState: e,
            firstUpdate: null,
            lastUpdate: null,
            firstCapturedUpdate: null,
            lastCapturedUpdate: null,
            firstEffect: null,
            lastEffect: null,
            firstCapturedEffect: null,
            lastCapturedEffect: null
        };
    }
    function yt(e) {
        return {
            baseState: e.baseState,
            firstUpdate: e.firstUpdate,
            lastUpdate: e.lastUpdate,
            firstCapturedUpdate: null,
            lastCapturedUpdate: null,
            firstEffect: null,
            lastEffect: null,
            firstCapturedEffect: null,
            lastCapturedEffect: null
        };
    }
    function vt(e, t) {
        return {
            expirationTime: e,
            suspenseConfig: t,
            tag: 0,
            payload: null,
            callback: null,
            next: null,
            nextEffect: null
        };
    }
    function Tt(e, t) {
        null === e.lastUpdate ? e.firstUpdate = e.lastUpdate = t : (e.lastUpdate.next = t, e.lastUpdate = t);
    }
    function xt(e, t) {
        var n = e.alternate;
        if (null === n) {
            var r = e.updateQueue, l = null;
            null === r && (r = e.updateQueue = bt(e.memoizedState));
        } else r = e.updateQueue, l = n.updateQueue, null === r ? null === l ? (r = e.updateQueue = bt(e.memoizedState), l = n.updateQueue = bt(n.memoizedState)) : r = e.updateQueue = yt(l) : null === l && (l = n.updateQueue = yt(r));
        null === l || r === l ? Tt(r, t) : null === r.lastUpdate || null === l.lastUpdate ? (Tt(r, t), Tt(l, t)) : (Tt(r, t), l.lastUpdate = t);
    }
    function Et(e, t) {
        var n = e.updateQueue;
        null === (n = null === n ? e.updateQueue = bt(e.memoizedState) : kt(e, n)).lastCapturedUpdate ? n.firstCapturedUpdate = n.lastCapturedUpdate = t : (n.lastCapturedUpdate.next = t, n.lastCapturedUpdate = t);
    }
    function kt(e, t) {
        var n = e.alternate;
        return null !== n && t === n.updateQueue && (t = e.updateQueue = yt(t)), t;
    }
    function St(e, t, n, r, i, a) {
        switch(n.tag){
            case 1:
                return "function" == typeof (e = n.payload) ? e.call(a, r, i) : e;
            case 3:
                e.effectTag = -4097 & e.effectTag | 64;
            case 0:
                if (null == (i = "function" == typeof (e = n.payload) ? e.call(a, r, i) : e)) break;
                return l({}, r, i);
            case 2:
                gt = !0;
        }
        return r;
    }
    function Ct(e, t, n, r, l) {
        gt = !1;
        for(var i = (t = kt(e, t)).baseState, a = null, u = 0, o = t.firstUpdate, f = i; null !== o;){
            var c = o.expirationTime;
            c < l ? (null === a && (a = o, i = f), u < c && (u = c)) : (El(c, o.suspenseConfig), f = St(e, 0, o, f, n, r), null !== o.callback && (e.effectTag |= 32, o.nextEffect = null, null === t.lastEffect ? t.firstEffect = t.lastEffect = o : (t.lastEffect.nextEffect = o, t.lastEffect = o))), o = o.next;
        }
        for(c = null, o = t.firstCapturedUpdate; null !== o;){
            var s = o.expirationTime;
            s < l ? (null === c && (c = o, null === a && (i = f)), u < s && (u = s)) : (f = St(e, 0, o, f, n, r), null !== o.callback && (e.effectTag |= 32, o.nextEffect = null, null === t.lastCapturedEffect ? t.firstCapturedEffect = t.lastCapturedEffect = o : (t.lastCapturedEffect.nextEffect = o, t.lastCapturedEffect = o))), o = o.next;
        }
        null === a && (t.lastUpdate = null), null === c ? t.lastCapturedUpdate = null : e.effectTag |= 32, null === a && null === c && (i = f), t.baseState = i, t.firstUpdate = a, t.firstCapturedUpdate = c, kl(u), e.expirationTime = u, e.memoizedState = f;
    }
    function wt(e, t, n) {
        null !== t.firstCapturedUpdate && (null !== t.lastUpdate && (t.lastUpdate.next = t.firstCapturedUpdate, t.lastUpdate = t.lastCapturedUpdate), t.firstCapturedUpdate = t.lastCapturedUpdate = null), zt(t.firstEffect, n), t.firstEffect = t.lastEffect = null, zt(t.firstCapturedEffect, n), t.firstCapturedEffect = t.lastCapturedEffect = null;
    }
    function zt(e, t) {
        for(; null !== e;){
            var n = e.callback;
            if (null !== n) {
                e.callback = null;
                var r = t;
                if ("function" != typeof n) throw Error(f(191, n));
                n.call(r);
            }
            e = e.nextEffect;
        }
    }
    var Pt = c.ReactCurrentBatchConfig, _t = (new a.Component).refs;
    function Nt(e, t, n, r) {
        n = null == (n = n(r, t = e.memoizedState)) ? t : l({}, t, n), e.memoizedState = n, null !== (r = e.updateQueue) && 0 === e.expirationTime && (r.baseState = n);
    }
    var Ut = {
        isMounted: function(e) {
            return !!(e = e._reactInternalFiber) && P(e) === e;
        },
        enqueueSetState: function(e, t, n) {
            e = e._reactInternalFiber;
            var r = cl(), l = Pt.suspense;
            (l = vt(r = sl(r, e, l), l)).payload = t, null != n && (l.callback = n), xt(e, l), dl(e, r);
        },
        enqueueReplaceState: function(e, t, n) {
            e = e._reactInternalFiber;
            var r = cl(), l = Pt.suspense;
            (l = vt(r = sl(r, e, l), l)).tag = 1, l.payload = t, null != n && (l.callback = n), xt(e, l), dl(e, r);
        },
        enqueueForceUpdate: function(e, t) {
            e = e._reactInternalFiber;
            var n = cl(), r = Pt.suspense;
            (r = vt(n = sl(n, e, r), r)).tag = 2, null != t && (r.callback = t), xt(e, r), dl(e, n);
        }
    };
    function Rt(e, t, n, r, l, i, a) {
        return "function" == typeof (e = e.stateNode).shouldComponentUpdate ? e.shouldComponentUpdate(r, i, a) : !(t.prototype && t.prototype.isPureReactComponent && lt(n, r) && lt(l, i));
    }
    function It(e, t, n) {
        var r = !1, l = ge, i = t.contextType;
        return "object" == typeof i && null !== i ? i = ht(i) : (l = xe(t) ? ve : be.current, i = (r = null != (r = t.contextTypes)) ? Te(e, l) : ge), t = new t(n, i), e.memoizedState = null !== t.state && void 0 !== t.state ? t.state : null, t.updater = Ut, e.stateNode = t, t._reactInternalFiber = e, r && ((e = e.stateNode).__reactInternalMemoizedUnmaskedChildContext = l, e.__reactInternalMemoizedMaskedChildContext = i), t;
    }
    function Mt(e, t, n, r) {
        e = t.state, "function" == typeof t.componentWillReceiveProps && t.componentWillReceiveProps(n, r), "function" == typeof t.UNSAFE_componentWillReceiveProps && t.UNSAFE_componentWillReceiveProps(n, r), t.state !== e && Ut.enqueueReplaceState(t, t.state, null);
    }
    function Ft(e, t, n, r) {
        var l = e.stateNode;
        l.props = n, l.state = e.memoizedState, l.refs = _t;
        var i = t.contextType;
        "object" == typeof i && null !== i ? l.context = ht(i) : (i = xe(t) ? ve : be.current, l.context = Te(e, i)), null !== (i = e.updateQueue) && (Ct(e, i, n, l, r), l.state = e.memoizedState), "function" == typeof (i = t.getDerivedStateFromProps) && (Nt(e, t, i, n), l.state = e.memoizedState), "function" == typeof t.getDerivedStateFromProps || "function" == typeof l.getSnapshotBeforeUpdate || "function" != typeof l.UNSAFE_componentWillMount && "function" != typeof l.componentWillMount || (t = l.state, "function" == typeof l.componentWillMount && l.componentWillMount(), "function" == typeof l.UNSAFE_componentWillMount && l.UNSAFE_componentWillMount(), t !== l.state && Ut.enqueueReplaceState(l, l.state, null), null !== (i = e.updateQueue) && (Ct(e, i, n, l, r), l.state = e.memoizedState)), "function" == typeof l.componentDidMount && (e.effectTag |= 4);
    }
    var Qt = Array.isArray;
    function Dt(e, t, n) {
        if (null !== (e = n.ref) && "function" != typeof e && "object" != typeof e) {
            if (n._owner) {
                if (n = n._owner) {
                    if (1 !== n.tag) throw Error(f(309));
                    var r = n.stateNode;
                }
                if (!r) throw Error(f(147, e));
                var l = "" + e;
                return null !== t && null !== t.ref && "function" == typeof t.ref && t.ref._stringRef === l ? t.ref : (t = function(e) {
                    var t = r.refs;
                    t === _t && (t = r.refs = {}), null === e ? delete t[l] : t[l] = e;
                }, t._stringRef = l, t);
            }
            if ("string" != typeof e) throw Error(f(284));
            if (!n._owner) throw Error(f(290, e));
        }
        return e;
    }
    function Wt(e, t) {
        if ("textarea" !== e.type) throw Error(f(31, "[object Object]" === Object.prototype.toString.call(t) ? "object with keys {" + Object.keys(t).join(", ") + "}" : t, ""));
    }
    function jt(e) {
        function t(t, n) {
            if (e) {
                var r = t.lastEffect;
                null !== r ? (r.nextEffect = n, t.lastEffect = n) : t.firstEffect = t.lastEffect = n, n.nextEffect = null, n.effectTag = 8;
            }
        }
        function n(n, r) {
            if (!e) return null;
            for(; null !== r;)t(n, r), r = r.sibling;
            return null;
        }
        function r(e, t) {
            for(e = new Map; null !== t;)null !== t.key ? e.set(t.key, t) : e.set(t.index, t), t = t.sibling;
            return e;
        }
        function l(e, t, n) {
            return (e = Al(e, t)).index = 0, e.sibling = null, e;
        }
        function i(t, n, r) {
            return t.index = r, e ? null !== (r = t.alternate) ? (r = r.index) < n ? (t.effectTag = 2, n) : r : (t.effectTag = 2, n) : n;
        }
        function a(t) {
            return e && null === t.alternate && (t.effectTag = 2), t;
        }
        function u(e, t, n, r) {
            return null === t || 6 !== t.tag ? ((t = ql(n, e.mode, r)).return = e, t) : ((t = l(t, n)).return = e, t);
        }
        function o(e, t, n, r) {
            return null !== t && t.elementType === n.type ? ((r = l(t, n.props)).ref = Dt(e, t, n), r.return = e, r) : ((r = Ll(n.type, n.key, n.props, null, e.mode, r)).ref = Dt(e, t, n), r.return = e, r);
        }
        function c(e, t, n, r) {
            return null === t || 4 !== t.tag || t.stateNode.containerInfo !== n.containerInfo || t.stateNode.implementation !== n.implementation ? ((t = Vl(n, e.mode, r)).return = e, t) : ((t = l(t, n.children || [])).return = e, t);
        }
        function s(e, t, n, r, i) {
            return null === t || 7 !== t.tag ? ((t = $l(n, e.mode, r, i)).return = e, t) : ((t = l(t, n)).return = e, t);
        }
        function h(e, t, n) {
            if ("string" == typeof t || "number" == typeof t) return (t = ql("" + t, e.mode, n)).return = e, t;
            if ("object" == typeof t && null !== t) {
                switch(t.$$typeof){
                    case d:
                        return (n = Ll(t.type, t.key, t.props, null, e.mode, n)).ref = Dt(e, null, t), n.return = e, n;
                    case p:
                        return (t = Vl(t, e.mode, n)).return = e, t;
                }
                if (Qt(t) || w(t)) return (t = $l(t, e.mode, n, null)).return = e, t;
                Wt(e, t);
            }
            return null;
        }
        function g(e, t, n, r) {
            var l = null !== t ? t.key : null;
            if ("string" == typeof n || "number" == typeof n) return null !== l ? null : u(e, t, "" + n, r);
            if ("object" == typeof n && null !== n) {
                switch(n.$$typeof){
                    case d:
                        return n.key === l ? n.type === m ? s(e, t, n.props.children, r, l) : o(e, t, n, r) : null;
                    case p:
                        return n.key === l ? c(e, t, n, r) : null;
                }
                if (Qt(n) || w(n)) return null !== l ? null : s(e, t, n, r, null);
                Wt(e, n);
            }
            return null;
        }
        function b(e, t, n, r, l) {
            if ("string" == typeof r || "number" == typeof r) return u(t, e = e.get(n) || null, "" + r, l);
            if ("object" == typeof r && null !== r) {
                switch(r.$$typeof){
                    case d:
                        return e = e.get(null === r.key ? n : r.key) || null, r.type === m ? s(t, e, r.props.children, l, r.key) : o(t, e, r, l);
                    case p:
                        return c(t, e = e.get(null === r.key ? n : r.key) || null, r, l);
                }
                if (Qt(r) || w(r)) return s(t, e = e.get(n) || null, r, l, null);
                Wt(t, r);
            }
            return null;
        }
        function y(l, a, u, o) {
            for(var f = null, c = null, s = a, d = a = 0, p = null; null !== s && d < u.length; d++){
                s.index > d ? (p = s, s = null) : p = s.sibling;
                var m = g(l, s, u[d], o);
                if (null === m) {
                    null === s && (s = p);
                    break;
                }
                e && s && null === m.alternate && t(l, s), a = i(m, a, d), null === c ? f = m : c.sibling = m, c = m, s = p;
            }
            if (d === u.length) return n(l, s), f;
            if (null === s) {
                for(; d < u.length; d++)null !== (s = h(l, u[d], o)) && (a = i(s, a, d), null === c ? f = s : c.sibling = s, c = s);
                return f;
            }
            for(s = r(l, s); d < u.length; d++)null !== (p = b(s, l, d, u[d], o)) && (e && null !== p.alternate && s.delete(null === p.key ? d : p.key), a = i(p, a, d), null === c ? f = p : c.sibling = p, c = p);
            return e && s.forEach(function(e) {
                return t(l, e);
            }), f;
        }
        function v(l, a, u, o) {
            var c = w(u);
            if ("function" != typeof c) throw Error(f(150));
            if (null == (u = c.call(u))) throw Error(f(151));
            for(var s = c = null, d = a, p = a = 0, m = null, y = u.next(); null !== d && !y.done; p++, y = u.next()){
                d.index > p ? (m = d, d = null) : m = d.sibling;
                var v = g(l, d, y.value, o);
                if (null === v) {
                    null === d && (d = m);
                    break;
                }
                e && d && null === v.alternate && t(l, d), a = i(v, a, p), null === s ? c = v : s.sibling = v, s = v, d = m;
            }
            if (y.done) return n(l, d), c;
            if (null === d) {
                for(; !y.done; p++, y = u.next())null !== (y = h(l, y.value, o)) && (a = i(y, a, p), null === s ? c = y : s.sibling = y, s = y);
                return c;
            }
            for(d = r(l, d); !y.done; p++, y = u.next())null !== (y = b(d, l, p, y.value, o)) && (e && null !== y.alternate && d.delete(null === y.key ? p : y.key), a = i(y, a, p), null === s ? c = y : s.sibling = y, s = y);
            return e && d.forEach(function(e) {
                return t(l, e);
            }), c;
        }
        return function(e, r, i, u) {
            var o = "object" == typeof i && null !== i && i.type === m && null === i.key;
            o && (i = i.props.children);
            var c = "object" == typeof i && null !== i;
            if (c) switch(i.$$typeof){
                case d:
                    e: {
                        for(c = i.key, o = r; null !== o;){
                            if (o.key === c) {
                                if (7 === o.tag ? i.type === m : o.elementType === i.type) {
                                    n(e, o.sibling), (r = l(o, i.type === m ? i.props.children : i.props)).ref = Dt(e, o, i), r.return = e, e = r;
                                    break e;
                                }
                                n(e, o);
                                break;
                            }
                            t(e, o), o = o.sibling;
                        }
                        i.type === m ? ((r = $l(i.props.children, e.mode, u, i.key)).return = e, e = r) : ((u = Ll(i.type, i.key, i.props, null, e.mode, u)).ref = Dt(e, r, i), u.return = e, e = u);
                    }
                    return a(e);
                case p:
                    e: {
                        for(o = i.key; null !== r;){
                            if (r.key === o) {
                                if (4 === r.tag && r.stateNode.containerInfo === i.containerInfo && r.stateNode.implementation === i.implementation) {
                                    n(e, r.sibling), (r = l(r, i.children || [])).return = e, e = r;
                                    break e;
                                }
                                n(e, r);
                                break;
                            }
                            t(e, r), r = r.sibling;
                        }
                        (r = Vl(i, e.mode, u)).return = e, e = r;
                    }
                    return a(e);
            }
            if ("string" == typeof i || "number" == typeof i) return i = "" + i, null !== r && 6 === r.tag ? (n(e, r.sibling), (r = l(r, i)).return = e, e = r) : (n(e, r), (r = ql(i, e.mode, u)).return = e, e = r), a(e);
            if (Qt(i)) return y(e, r, i, u);
            if (w(i)) return v(e, r, i, u);
            if (c && Wt(e, i), void 0 === i && !o) switch(e.tag){
                case 1:
                case 0:
                    throw e = e.type, Error(f(152, e.displayName || e.name || "Component"));
            }
            return n(e, r);
        };
    }
    var Ot = jt(!0), Bt = jt(!1), Ht = {}, At = {
        current: Ht
    }, Lt = {
        current: Ht
    }, $t = {
        current: Ht
    };
    function qt(e) {
        if (e === Ht) throw Error(f(174));
        return e;
    }
    function Vt(e, t) {
        he($t, t), he(Lt, e), he(At, Ht), t = I(t), me(At), he(At, t);
    }
    function Kt(e) {
        me(At), me(Lt), me($t);
    }
    function Gt(e) {
        var t = qt($t.current), n = qt(At.current);
        n !== (t = M(n, e.type, t)) && (he(Lt, e), he(At, t));
    }
    function Yt(e) {
        Lt.current === e && (me(At), me(Lt));
    }
    var Jt = {
        current: 0
    };
    function Xt(e) {
        for(var t = e; null !== t;){
            if (13 === t.tag) {
                var n = t.memoizedState;
                if (null !== n && (null === (n = n.dehydrated) || oe(n) || fe(n))) return t;
            } else if (19 === t.tag && void 0 !== t.memoizedProps.revealOrder) {
                if (0 != (64 & t.effectTag)) return t;
            } else if (null !== t.child) {
                t.child.return = t, t = t.child;
                continue;
            }
            if (t === e) break;
            for(; null === t.sibling;){
                if (null === t.return || t.return === e) return null;
                t = t.return;
            }
            t.sibling.return = t.return, t = t.sibling;
        }
        return null;
    }
    function Zt(e, t) {
        return {
            responder: e,
            props: t
        };
    }
    var en = c.ReactCurrentDispatcher, tn = c.ReactCurrentBatchConfig, nn = 0, rn = null, ln = null, an = null, un = null, on = null, fn = null, cn = 0, sn = null, dn = 0, pn = !1, mn = null, hn = 0;
    function gn() {
        throw Error(f(321));
    }
    function bn(e, t) {
        if (null === t) return !1;
        for(var n = 0; n < t.length && n < e.length; n++)if (!nt(e[n], t[n])) return !1;
        return !0;
    }
    function yn(e, t, n, r, l, i) {
        if (nn = i, rn = t, an = null !== e ? e.memoizedState : null, en.current = null === an ? Dn : Wn, t = n(r, l), pn) {
            do {
                pn = !1, hn += 1, an = null !== e ? e.memoizedState : null, fn = un, sn = on = ln = null, en.current = Wn, t = n(r, l);
            }while (pn)
            mn = null, hn = 0;
        }
        if (en.current = Qn, (e = rn).memoizedState = un, e.expirationTime = cn, e.updateQueue = sn, e.effectTag |= dn, e = null !== ln && null !== ln.next, nn = 0, fn = on = un = an = ln = rn = null, cn = 0, sn = null, dn = 0, e) throw Error(f(300));
        return t;
    }
    function vn() {
        en.current = Qn, nn = 0, fn = on = un = an = ln = rn = null, cn = 0, sn = null, dn = 0, pn = !1, mn = null, hn = 0;
    }
    function Tn() {
        var e = {
            memoizedState: null,
            baseState: null,
            queue: null,
            baseUpdate: null,
            next: null
        };
        return null === on ? un = on = e : on = on.next = e, on;
    }
    function xn() {
        if (null !== fn) fn = (on = fn).next, an = null !== (ln = an) ? ln.next : null;
        else {
            if (null === an) throw Error(f(310));
            var e = {
                memoizedState: (ln = an).memoizedState,
                baseState: ln.baseState,
                queue: ln.queue,
                baseUpdate: ln.baseUpdate,
                next: null
            };
            on = null === on ? un = e : on.next = e, an = ln.next;
        }
        return on;
    }
    function En(e, t) {
        return "function" == typeof t ? t(e) : t;
    }
    function kn(e) {
        var t = xn(), n = t.queue;
        if (null === n) throw Error(f(311));
        if (n.lastRenderedReducer = e, 0 < hn) {
            var r = n.dispatch;
            if (null !== mn) {
                var l = mn.get(n);
                if (void 0 !== l) {
                    mn.delete(n);
                    var i = t.memoizedState;
                    do {
                        i = e(i, l.action), l = l.next;
                    }while (null !== l)
                    return nt(i, t.memoizedState) || (On = !0), t.memoizedState = i, t.baseUpdate === n.last && (t.baseState = i), n.lastRenderedState = i, [
                        i,
                        r
                    ];
                }
            }
            return [
                t.memoizedState,
                r
            ];
        }
        r = n.last;
        var a = t.baseUpdate;
        if (i = t.baseState, null !== a ? (null !== r && (r.next = null), r = a.next) : r = null !== r ? r.next : null, null !== r) {
            var u = l = null, o = r, c = !1;
            do {
                var s = o.expirationTime;
                s < nn ? (c || (c = !0, u = a, l = i), s > cn && kl(cn = s)) : (El(s, o.suspenseConfig), i = o.eagerReducer === e ? o.eagerState : e(i, o.action)), a = o, o = o.next;
            }while (null !== o && o !== r)
            c || (u = a, l = i), nt(i, t.memoizedState) || (On = !0), t.memoizedState = i, t.baseUpdate = u, t.baseState = l, n.lastRenderedState = i;
        }
        return [
            t.memoizedState,
            n.dispatch
        ];
    }
    function Sn(e) {
        var t = Tn();
        return "function" == typeof e && (e = e()), t.memoizedState = t.baseState = e, e = (e = t.queue = {
            last: null,
            dispatch: null,
            lastRenderedReducer: En,
            lastRenderedState: e
        }).dispatch = Fn.bind(null, rn, e), [
            t.memoizedState,
            e
        ];
    }
    function Cn(e) {
        return kn(En);
    }
    function wn(e, t, n, r) {
        return e = {
            tag: e,
            create: t,
            destroy: n,
            deps: r,
            next: null
        }, null === sn ? (sn = {
            lastEffect: null
        }).lastEffect = e.next = e : null === (t = sn.lastEffect) ? sn.lastEffect = e.next = e : (n = t.next, t.next = e, e.next = n, sn.lastEffect = e), e;
    }
    function zn(e, t, n, r) {
        var l = Tn();
        dn |= e, l.memoizedState = wn(t, n, void 0, void 0 === r ? null : r);
    }
    function Pn(e, t, n, r) {
        var l = xn();
        r = void 0 === r ? null : r;
        var i = void 0;
        if (null !== ln) {
            var a = ln.memoizedState;
            if (i = a.destroy, null !== r && bn(r, a.deps)) return void wn(0, n, i, r);
        }
        dn |= e, l.memoizedState = wn(t, n, i, r);
    }
    function _n(e, t) {
        return zn(516, 192, e, t);
    }
    function Nn(e, t) {
        return Pn(516, 192, e, t);
    }
    function Un(e, t) {
        return "function" == typeof t ? (e = e(), t(e), function() {
            t(null);
        }) : null != t ? (e = e(), t.current = e, function() {
            t.current = null;
        }) : void 0;
    }
    function Rn() {}
    function In(e, t) {
        return Tn().memoizedState = [
            e,
            void 0 === t ? null : t
        ], e;
    }
    function Mn(e, t) {
        var n = xn();
        t = void 0 === t ? null : t;
        var r = n.memoizedState;
        return null !== r && null !== t && bn(t, r[1]) ? r[0] : (n.memoizedState = [
            e,
            t
        ], e);
    }
    function Fn(e, t, n) {
        if (!(25 > hn)) throw Error(f(301));
        var r = e.alternate;
        if (e === rn || null !== r && r === rn) if (pn = !0, e = {
            expirationTime: nn,
            suspenseConfig: null,
            action: n,
            eagerReducer: null,
            eagerState: null,
            next: null
        }, null === mn && (mn = new Map), void 0 === (n = mn.get(t))) mn.set(t, e);
        else {
            for(t = n; null !== t.next;)t = t.next;
            t.next = e;
        }
        else {
            var l = cl(), i = Pt.suspense;
            i = {
                expirationTime: l = sl(l, e, i),
                suspenseConfig: i,
                action: n,
                eagerReducer: null,
                eagerState: null,
                next: null
            };
            var a = t.last;
            if (null === a) i.next = i;
            else {
                var u = a.next;
                null !== u && (i.next = u), a.next = i;
            }
            if (t.last = i, 0 === e.expirationTime && (null === r || 0 === r.expirationTime) && null !== (r = t.lastRenderedReducer)) try {
                var o = t.lastRenderedState, c = r(o, n);
                if (i.eagerReducer = r, i.eagerState = c, nt(c, o)) return;
            } catch (e) {}
            dl(e, l);
        }
    }
    var Qn = {
        readContext: ht,
        useCallback: gn,
        useContext: gn,
        useEffect: gn,
        useImperativeHandle: gn,
        useLayoutEffect: gn,
        useMemo: gn,
        useReducer: gn,
        useRef: gn,
        useState: gn,
        useDebugValue: gn,
        useResponder: gn,
        useDeferredValue: gn,
        useTransition: gn
    }, Dn = {
        readContext: ht,
        useCallback: In,
        useContext: ht,
        useEffect: _n,
        useImperativeHandle: function(e, t, n) {
            return n = null != n ? n.concat([
                e
            ]) : null, zn(4, 36, Un.bind(null, t, e), n);
        },
        useLayoutEffect: function(e, t) {
            return zn(4, 36, e, t);
        },
        useMemo: function(e, t) {
            var n = Tn();
            return t = void 0 === t ? null : t, e = e(), n.memoizedState = [
                e,
                t
            ], e;
        },
        useReducer: function(e, t, n) {
            var r = Tn();
            return t = void 0 !== n ? n(t) : t, r.memoizedState = r.baseState = t, e = (e = r.queue = {
                last: null,
                dispatch: null,
                lastRenderedReducer: e,
                lastRenderedState: t
            }).dispatch = Fn.bind(null, rn, e), [
                r.memoizedState,
                e
            ];
        },
        useRef: function(e) {
            return e = {
                current: e
            }, Tn().memoizedState = e;
        },
        useState: Sn,
        useDebugValue: Rn,
        useResponder: Zt,
        useDeferredValue: function(e, t) {
            var n = Sn(e), r = n[0], l = n[1];
            return _n(function() {
                o.unstable_next(function() {
                    var n = tn.suspense;
                    tn.suspense = void 0 === t ? null : t;
                    try {
                        l(e);
                    } finally{
                        tn.suspense = n;
                    }
                });
            }, [
                e,
                t
            ]), r;
        },
        useTransition: function(e) {
            var t = Sn(!1), n = t[0], r = t[1];
            return [
                In(function(t) {
                    r(!0), o.unstable_next(function() {
                        var n = tn.suspense;
                        tn.suspense = void 0 === e ? null : e;
                        try {
                            r(!1), t();
                        } finally{
                            tn.suspense = n;
                        }
                    });
                }, [
                    e,
                    n
                ]),
                n
            ];
        }
    }, Wn = {
        readContext: ht,
        useCallback: Mn,
        useContext: ht,
        useEffect: Nn,
        useImperativeHandle: function(e, t, n) {
            return n = null != n ? n.concat([
                e
            ]) : null, Pn(4, 36, Un.bind(null, t, e), n);
        },
        useLayoutEffect: function(e, t) {
            return Pn(4, 36, e, t);
        },
        useMemo: function(e, t) {
            var n = xn();
            t = void 0 === t ? null : t;
            var r = n.memoizedState;
            return null !== r && null !== t && bn(t, r[1]) ? r[0] : (e = e(), n.memoizedState = [
                e,
                t
            ], e);
        },
        useReducer: kn,
        useRef: function() {
            return xn().memoizedState;
        },
        useState: Cn,
        useDebugValue: Rn,
        useResponder: Zt,
        useDeferredValue: function(e, t) {
            var n = Cn(), r = n[0], l = n[1];
            return Nn(function() {
                o.unstable_next(function() {
                    var n = tn.suspense;
                    tn.suspense = void 0 === t ? null : t;
                    try {
                        l(e);
                    } finally{
                        tn.suspense = n;
                    }
                });
            }, [
                e,
                t
            ]), r;
        },
        useTransition: function(e) {
            var t = Cn(), n = t[0], r = t[1];
            return [
                Mn(function(t) {
                    r(!0), o.unstable_next(function() {
                        var n = tn.suspense;
                        tn.suspense = void 0 === e ? null : e;
                        try {
                            r(!1), t();
                        } finally{
                            tn.suspense = n;
                        }
                    });
                }, [
                    e,
                    n
                ]),
                n
            ];
        }
    }, jn = c.ReactCurrentOwner, On = !1;
    function Bn(e, t, n, r) {
        t.child = null === e ? Bt(t, null, n, r) : Ot(t, e.child, n, r);
    }
    function Hn(e, t, n, r, l) {
        n = n.render;
        var i = t.ref;
        return mt(t, l), r = yn(e, t, n, r, i, l), null === e || On ? (t.effectTag |= 1, Bn(e, t, r, l), t.child) : (t.updateQueue = e.updateQueue, t.effectTag &= -517, e.expirationTime <= l && (e.expirationTime = 0), ir(e, t, l));
    }
    function An(e, t, n, r, l, i) {
        if (null === e) {
            var a = n.type;
            return "function" != typeof a || Hl(a) || void 0 !== a.defaultProps || null !== n.compare || void 0 !== n.defaultProps ? ((e = Ll(n.type, null, r, null, t.mode, i)).ref = t.ref, e.return = t, t.child = e) : (t.tag = 15, t.type = a, Ln(e, t, a, r, l, i));
        }
        return a = e.child, l < i && (l = a.memoizedProps, (n = null !== (n = n.compare) ? n : lt)(l, r) && e.ref === t.ref) ? ir(e, t, i) : (t.effectTag |= 1, (e = Al(a, r)).ref = t.ref, e.return = t, t.child = e);
    }
    function Ln(e, t, n, r, l, i) {
        return null !== e && lt(e.memoizedProps, r) && e.ref === t.ref && (On = !1, l < i) ? ir(e, t, i) : qn(e, t, n, r, i);
    }
    function $n(e, t) {
        var n = t.ref;
        (null === e && null !== n || null !== e && e.ref !== n) && (t.effectTag |= 128);
    }
    function qn(e, t, n, r, l) {
        var i = xe(n) ? ve : be.current;
        return i = Te(t, i), mt(t, l), n = yn(e, t, n, r, i, l), null === e || On ? (t.effectTag |= 1, Bn(e, t, n, l), t.child) : (t.updateQueue = e.updateQueue, t.effectTag &= -517, e.expirationTime <= l && (e.expirationTime = 0), ir(e, t, l));
    }
    function Vn(e, t, n, r, l) {
        if (xe(n)) {
            var i = !0;
            we(t);
        } else i = !1;
        if (mt(t, l), null === t.stateNode) null !== e && (e.alternate = null, t.alternate = null, t.effectTag |= 2), It(t, n, r), Ft(t, n, r, l), r = !0;
        else if (null === e) {
            var a = t.stateNode, u = t.memoizedProps;
            a.props = u;
            var o = a.context, f = n.contextType;
            f = "object" == typeof f && null !== f ? ht(f) : Te(t, f = xe(n) ? ve : be.current);
            var c = n.getDerivedStateFromProps, s = "function" == typeof c || "function" == typeof a.getSnapshotBeforeUpdate;
            s || "function" != typeof a.UNSAFE_componentWillReceiveProps && "function" != typeof a.componentWillReceiveProps || (u !== r || o !== f) && Mt(t, a, r, f), gt = !1;
            var d = t.memoizedState;
            o = a.state = d;
            var p = t.updateQueue;
            null !== p && (Ct(t, p, r, a, l), o = t.memoizedState), u !== r || d !== o || ye.current || gt ? ("function" == typeof c && (Nt(t, n, c, r), o = t.memoizedState), (u = gt || Rt(t, n, u, r, d, o, f)) ? (s || "function" != typeof a.UNSAFE_componentWillMount && "function" != typeof a.componentWillMount || ("function" == typeof a.componentWillMount && a.componentWillMount(), "function" == typeof a.UNSAFE_componentWillMount && a.UNSAFE_componentWillMount()), "function" == typeof a.componentDidMount && (t.effectTag |= 4)) : ("function" == typeof a.componentDidMount && (t.effectTag |= 4), t.memoizedProps = r, t.memoizedState = o), a.props = r, a.state = o, a.context = f, r = u) : ("function" == typeof a.componentDidMount && (t.effectTag |= 4), r = !1);
        } else a = t.stateNode, u = t.memoizedProps, a.props = t.type === t.elementType ? u : it(t.type, u), o = a.context, f = "object" == typeof (f = n.contextType) && null !== f ? ht(f) : Te(t, f = xe(n) ? ve : be.current), (s = "function" == typeof (c = n.getDerivedStateFromProps) || "function" == typeof a.getSnapshotBeforeUpdate) || "function" != typeof a.UNSAFE_componentWillReceiveProps && "function" != typeof a.componentWillReceiveProps || (u !== r || o !== f) && Mt(t, a, r, f), gt = !1, o = t.memoizedState, d = a.state = o, null !== (p = t.updateQueue) && (Ct(t, p, r, a, l), d = t.memoizedState), u !== r || o !== d || ye.current || gt ? ("function" == typeof c && (Nt(t, n, c, r), d = t.memoizedState), (c = gt || Rt(t, n, u, r, o, d, f)) ? (s || "function" != typeof a.UNSAFE_componentWillUpdate && "function" != typeof a.componentWillUpdate || ("function" == typeof a.componentWillUpdate && a.componentWillUpdate(r, d, f), "function" == typeof a.UNSAFE_componentWillUpdate && a.UNSAFE_componentWillUpdate(r, d, f)), "function" == typeof a.componentDidUpdate && (t.effectTag |= 4), "function" == typeof a.getSnapshotBeforeUpdate && (t.effectTag |= 256)) : ("function" != typeof a.componentDidUpdate || u === e.memoizedProps && o === e.memoizedState || (t.effectTag |= 4), "function" != typeof a.getSnapshotBeforeUpdate || u === e.memoizedProps && o === e.memoizedState || (t.effectTag |= 256), t.memoizedProps = r, t.memoizedState = d), a.props = r, a.state = d, a.context = f, r = c) : ("function" != typeof a.componentDidUpdate || u === e.memoizedProps && o === e.memoizedState || (t.effectTag |= 4), "function" != typeof a.getSnapshotBeforeUpdate || u === e.memoizedProps && o === e.memoizedState || (t.effectTag |= 256), r = !1);
        return Kn(e, t, n, r, i, l);
    }
    function Kn(e, t, n, r, l, i) {
        $n(e, t);
        var a = 0 != (64 & t.effectTag);
        if (!r && !a) return l && ze(t, n, !1), ir(e, t, i);
        r = t.stateNode, jn.current = t;
        var u = a && "function" != typeof n.getDerivedStateFromError ? null : r.render();
        return t.effectTag |= 1, null !== e && a ? (t.child = Ot(t, e.child, null, i), t.child = Ot(t, null, u, i)) : Bn(e, t, u, i), t.memoizedState = r.state, l && ze(t, n, !0), t.child;
    }
    function Gn(e) {
        var t = e.stateNode;
        t.pendingContext ? Se(0, t.pendingContext, t.pendingContext !== t.context) : t.context && Se(0, t.context, !1), Vt(e, t.containerInfo);
    }
    var Yn, Jn, Xn, Zn, er = {
        dehydrated: null,
        retryTime: 0
    };
    function tr(e, t, n) {
        var r, l = t.mode, i = t.pendingProps, a = Jt.current, u = !1;
        if ((r = 0 != (64 & t.effectTag)) || (r = 0 != (2 & a) && (null === e || null !== e.memoizedState)), r ? (u = !0, t.effectTag &= -65) : null !== e && null === e.memoizedState || void 0 === i.fallback || !0 === i.unstable_avoidThisFallback || (a |= 1), he(Jt, 1 & a), null === e) {
            if (i.fallback, u) {
                if (u = i.fallback, (i = $l(null, l, 0, null)).return = t, 0 == (2 & t.mode)) for(e = null !== t.memoizedState ? t.child.child : t.child, i.child = e; null !== e;)e.return = i, e = e.sibling;
                return (n = $l(u, l, n, null)).return = t, i.sibling = n, t.memoizedState = er, t.child = i, n;
            }
            return l = i.children, t.memoizedState = null, t.child = Bt(t, null, l, n);
        }
        if (null !== e.memoizedState) {
            if (l = (e = e.child).sibling, u) {
                if (i = i.fallback, (n = Al(e, e.pendingProps)).return = t, 0 == (2 & t.mode) && (u = null !== t.memoizedState ? t.child.child : t.child) !== e.child) for(n.child = u; null !== u;)u.return = n, u = u.sibling;
                return (l = Al(l, i, l.expirationTime)).return = t, n.sibling = l, n.childExpirationTime = 0, t.memoizedState = er, t.child = n, l;
            }
            return n = Ot(t, e.child, i.children, n), t.memoizedState = null, t.child = n;
        }
        if (e = e.child, u) {
            if (u = i.fallback, (i = $l(null, l, 0, null)).return = t, i.child = e, null !== e && (e.return = i), 0 == (2 & t.mode)) for(e = null !== t.memoizedState ? t.child.child : t.child, i.child = e; null !== e;)e.return = i, e = e.sibling;
            return (n = $l(u, l, n, null)).return = t, i.sibling = n, n.effectTag |= 2, i.childExpirationTime = 0, t.memoizedState = er, t.child = i, n;
        }
        return t.memoizedState = null, t.child = Ot(t, e, i.children, n);
    }
    function nr(e, t) {
        e.expirationTime < t && (e.expirationTime = t);
        var n = e.alternate;
        null !== n && n.expirationTime < t && (n.expirationTime = t), pt(e.return, t);
    }
    function rr(e, t, n, r, l, i) {
        var a = e.memoizedState;
        null === a ? e.memoizedState = {
            isBackwards: t,
            rendering: null,
            last: r,
            tail: n,
            tailExpiration: 0,
            tailMode: l,
            lastEffect: i
        } : (a.isBackwards = t, a.rendering = null, a.last = r, a.tail = n, a.tailExpiration = 0, a.tailMode = l, a.lastEffect = i);
    }
    function lr(e, t, n) {
        var r = t.pendingProps, l = r.revealOrder, i = r.tail;
        if (Bn(e, t, r.children, n), 0 != (2 & (r = Jt.current))) r = 1 & r | 2, t.effectTag |= 64;
        else {
            if (null !== e && 0 != (64 & e.effectTag)) e: for(e = t.child; null !== e;){
                if (13 === e.tag) null !== e.memoizedState && nr(e, n);
                else if (19 === e.tag) nr(e, n);
                else if (null !== e.child) {
                    e.child.return = e, e = e.child;
                    continue;
                }
                if (e === t) break e;
                for(; null === e.sibling;){
                    if (null === e.return || e.return === t) break e;
                    e = e.return;
                }
                e.sibling.return = e.return, e = e.sibling;
            }
            r &= 1;
        }
        if (he(Jt, r), 0 == (2 & t.mode)) t.memoizedState = null;
        else switch(l){
            case "forwards":
                for(n = t.child, l = null; null !== n;)null !== (e = n.alternate) && null === Xt(e) && (l = n), n = n.sibling;
                null === (n = l) ? (l = t.child, t.child = null) : (l = n.sibling, n.sibling = null), rr(t, !1, l, n, i, t.lastEffect);
                break;
            case "backwards":
                for(n = null, l = t.child, t.child = null; null !== l;){
                    if (null !== (e = l.alternate) && null === Xt(e)) {
                        t.child = l;
                        break;
                    }
                    e = l.sibling, l.sibling = n, n = l, l = e;
                }
                rr(t, !0, n, null, i, t.lastEffect);
                break;
            case "together":
                rr(t, !1, null, null, void 0, t.lastEffect);
                break;
            default:
                t.memoizedState = null;
        }
        return t.child;
    }
    function ir(e, t, n) {
        null !== e && (t.dependencies = e.dependencies);
        var r = t.expirationTime;
        if (0 !== r && kl(r), t.childExpirationTime < n) return null;
        if (null !== e && t.child !== e.child) throw Error(f(153));
        if (null !== t.child) {
            for(n = Al(e = t.child, e.pendingProps, e.expirationTime), t.child = n, n.return = t; null !== e.sibling;)e = e.sibling, (n = n.sibling = Al(e, e.pendingProps, e.expirationTime)).return = t;
            n.sibling = null;
        }
        return t.child;
    }
    function ar(e) {
        e.effectTag |= 4;
    }
    function ur(e, t) {
        switch(e.tailMode){
            case "hidden":
                t = e.tail;
                for(var n = null; null !== t;)null !== t.alternate && (n = t), t = t.sibling;
                null === n ? e.tail = null : n.sibling = null;
                break;
            case "collapsed":
                n = e.tail;
                for(var r = null; null !== n;)null !== n.alternate && (r = n), n = n.sibling;
                null === r ? t || null === e.tail ? e.tail = null : e.tail.sibling = null : r.sibling = null;
        }
    }
    function or(e) {
        switch(e.tag){
            case 1:
                xe(e.type) && Ee();
                var t = e.effectTag;
                return 4096 & t ? (e.effectTag = -4097 & t | 64, e) : null;
            case 3:
                if (Kt(), ke(), 0 != (64 & (t = e.effectTag))) throw Error(f(285));
                return e.effectTag = -4097 & t | 64, e;
            case 5:
                return Yt(e), null;
            case 13:
                return me(Jt), 4096 & (t = e.effectTag) ? (e.effectTag = -4097 & t | 64, e) : null;
            case 19:
                return me(Jt), null;
            case 4:
                return Kt(), null;
            case 10:
                return dt(e), null;
            default:
                return null;
        }
    }
    function fr(e, t) {
        return {
            value: e,
            source: t,
            stack: se(t)
        };
    }
    Yn = function(e, t) {
        for(var n = t.child; null !== n;){
            if (5 === n.tag || 6 === n.tag) W(e, n.stateNode);
            else if (4 !== n.tag && null !== n.child) {
                n.child.return = n, n = n.child;
                continue;
            }
            if (n === t) break;
            for(; null === n.sibling;){
                if (null === n.return || n.return === t) return;
                n = n.return;
            }
            n.sibling.return = n.return, n = n.sibling;
        }
    }, Jn = function() {}, Xn = function(e, t, n, r, l) {
        if ((e = e.memoizedProps) !== r) {
            var i = t.stateNode, a = qt(At.current);
            n = O(i, n, e, r, l, a), (t.updateQueue = n) && ar(t);
        }
    }, Zn = function(e, t, n, r) {
        n !== r && ar(t);
    };
    var cr = "function" == typeof WeakSet ? WeakSet : Set;
    function sr(e, t) {
        var n = t.source, r = t.stack;
        null === r && null !== n && (r = se(n)), null !== n && z(n.type), t = t.value, null !== e && 1 === e.tag && z(e.type);
        try {
            console.error(t);
        } catch (e) {
            setTimeout(function() {
                throw e;
            });
        }
    }
    function dr(e) {
        var t = e.ref;
        if (null !== t) if ("function" == typeof t) try {
            t(null);
        } catch (t) {
            Fl(e, t);
        }
        else t.current = null;
    }
    function pr(e, t) {
        switch(t.tag){
            case 0:
            case 11:
            case 15:
                mr(2, 0, t);
                break;
            case 1:
                if (256 & t.effectTag && null !== e) {
                    var n = e.memoizedProps, r = e.memoizedState;
                    t = (e = t.stateNode).getSnapshotBeforeUpdate(t.elementType === t.type ? n : it(t.type, n), r), e.__reactInternalSnapshotBeforeUpdate = t;
                }
                break;
            case 3:
            case 5:
            case 6:
            case 4:
            case 17:
                break;
            default:
                throw Error(f(163));
        }
    }
    function mr(e, t, n) {
        if (null !== (n = null !== (n = n.updateQueue) ? n.lastEffect : null)) {
            var r = n = n.next;
            do {
                if (0 != (r.tag & e)) {
                    var l = r.destroy;
                    r.destroy = void 0, void 0 !== l && l();
                }
                0 != (r.tag & t) && (l = r.create, r.destroy = l()), r = r.next;
            }while (r !== n)
        }
    }
    function hr(e, t, n) {
        switch("function" == typeof jl && jl(t), t.tag){
            case 0:
            case 11:
            case 14:
            case 15:
                if (null !== (e = t.updateQueue) && null !== (e = e.lastEffect)) {
                    var r = e.next;
                    Ge(97 < n ? 97 : n, function() {
                        var e = r;
                        do {
                            var n = e.destroy;
                            if (void 0 !== n) {
                                var l = t;
                                try {
                                    n();
                                } catch (e) {
                                    Fl(l, e);
                                }
                            }
                            e = e.next;
                        }while (e !== r)
                    });
                }
                break;
            case 1:
                dr(t), "function" == typeof (n = t.stateNode).componentWillUnmount && function(e, t) {
                    try {
                        t.props = e.memoizedProps, t.state = e.memoizedState, t.componentWillUnmount();
                    } catch (t) {
                        Fl(e, t);
                    }
                }(t, n);
                break;
            case 5:
                dr(t);
                break;
            case 4:
                Tr(e, t, n);
        }
    }
    function gr(e, t, n) {
        for(var r = t;;)if (hr(e, r, n), null === r.child || 4 === r.tag) {
            if (r === t) break;
            for(; null === r.sibling;){
                if (null === r.return || r.return === t) return;
                r = r.return;
            }
            r.sibling.return = r.return, r = r.sibling;
        } else r.child.return = r, r = r.child;
    }
    function br(e) {
        var t = e.alternate;
        e.return = null, e.child = null, e.memoizedState = null, e.updateQueue = null, e.dependencies = null, e.alternate = null, e.firstEffect = null, e.lastEffect = null, e.pendingProps = null, e.memoizedProps = null, null !== t && br(t);
    }
    function yr(e) {
        return 5 === e.tag || 3 === e.tag || 4 === e.tag;
    }
    function vr(e) {
        e: {
            for(var t = e.return; null !== t;){
                if (yr(t)) {
                    var n = t;
                    break e;
                }
                t = t.return;
            }
            throw Error(f(160));
        }
        switch(t = n.stateNode, n.tag){
            case 5:
                var r = !1;
                break;
            case 3:
            case 4:
                t = t.containerInfo, r = !0;
                break;
            default:
                throw Error(f(161));
        }
        16 & n.effectTag && (re(t), n.effectTag &= -17);
        e: t: for(n = e;;){
            for(; null === n.sibling;){
                if (null === n.return || yr(n.return)) {
                    n = null;
                    break e;
                }
                n = n.return;
            }
            for(n.sibling.return = n.return, n = n.sibling; 5 !== n.tag && 6 !== n.tag && 18 !== n.tag;){
                if (2 & n.effectTag) continue t;
                if (null === n.child || 4 === n.tag) continue t;
                n.child.return = n, n = n.child;
            }
            if (!(2 & n.effectTag)) {
                n = n.stateNode;
                break e;
            }
        }
        for(var l = e;;){
            var i = 5 === l.tag || 6 === l.tag;
            if (i) i = i ? l.stateNode : l.stateNode.instance, n ? r ? ee(t, i, n) : Z(t, i, n) : r ? G(t, i) : K(t, i);
            else if (4 !== l.tag && null !== l.child) {
                l.child.return = l, l = l.child;
                continue;
            }
            if (l === e) break;
            for(; null === l.sibling;){
                if (null === l.return || l.return === e) return;
                l = l.return;
            }
            l.sibling.return = l.return, l = l.sibling;
        }
    }
    function Tr(e, t, n) {
        for(var r, l, i = t, a = !1;;){
            if (!a) {
                a = i.return;
                e: for(;;){
                    if (null === a) throw Error(f(160));
                    switch(r = a.stateNode, a.tag){
                        case 5:
                            l = !1;
                            break e;
                        case 3:
                        case 4:
                            r = r.containerInfo, l = !0;
                            break e;
                    }
                    a = a.return;
                }
                a = !0;
            }
            if (5 === i.tag || 6 === i.tag) gr(e, i, n), l ? ne(r, i.stateNode) : te(r, i.stateNode);
            else if (4 === i.tag) {
                if (null !== i.child) {
                    r = i.stateNode.containerInfo, l = !0, i.child.return = i, i = i.child;
                    continue;
                }
            } else if (hr(e, i, n), null !== i.child) {
                i.child.return = i, i = i.child;
                continue;
            }
            if (i === t) break;
            for(; null === i.sibling;){
                if (null === i.return || i.return === t) return;
                4 === (i = i.return).tag && (a = !1);
            }
            i.sibling.return = i.return, i = i.sibling;
        }
    }
    function xr(e, t) {
        switch(t.tag){
            case 0:
            case 11:
            case 14:
            case 15:
                mr(4, 8, t);
                break;
            case 1:
            case 3:
            case 12:
            case 17:
            case 20:
            case 21:
                break;
            case 5:
                var n = t.stateNode;
                if (null != n) {
                    var r = t.memoizedProps;
                    e = null !== e ? e.memoizedProps : r;
                    var l = t.type, i = t.updateQueue;
                    t.updateQueue = null, null !== i && X(n, i, l, e, r, t);
                }
                break;
            case 6:
                if (null === t.stateNode) throw Error(f(162));
                n = t.memoizedProps, Y(t.stateNode, null !== e ? e.memoizedProps : n, n);
                break;
            case 13:
                !function(e) {
                    var t = e;
                    if (null === e.memoizedState) var n = !1;
                    else n = !0, t = e.child, Jr = qe();
                    if (null !== t) {
                        e: if (e = t, q) for(t = e;;){
                            if (5 === t.tag) {
                                var r = t.stateNode;
                                n ? le(r) : ae(t.stateNode, t.memoizedProps);
                            } else if (6 === t.tag) r = t.stateNode, n ? ie(r) : ue(r, t.memoizedProps);
                            else {
                                if (13 === t.tag && null !== t.memoizedState && null === t.memoizedState.dehydrated) {
                                    (r = t.child.sibling).return = t, t = r;
                                    continue;
                                }
                                if (null !== t.child) {
                                    t.child.return = t, t = t.child;
                                    continue;
                                }
                            }
                            if (t === e) break e;
                            for(; null === t.sibling;){
                                if (null === t.return || t.return === e) break e;
                                t = t.return;
                            }
                            t.sibling.return = t.return, t = t.sibling;
                        }
                    }
                }(t), Er(t);
                break;
            case 19:
                Er(t);
                break;
            default:
                throw Error(f(163));
        }
    }
    function Er(e) {
        var t = e.updateQueue;
        if (null !== t) {
            e.updateQueue = null;
            var n = e.stateNode;
            null === n && (n = e.stateNode = new cr), t.forEach(function(t) {
                var r = Dl.bind(null, e, t);
                n.has(t) || (n.add(t), t.then(r, r));
            });
        }
    }
    var kr = "function" == typeof WeakMap ? WeakMap : Map;
    function Sr(e, t, n) {
        (n = vt(n, null)).tag = 3, n.payload = {
            element: null
        };
        var r = t.value;
        return n.callback = function() {
            el || (el = !0, tl = r), sr(e, t);
        }, n;
    }
    function Cr(e, t, n) {
        (n = vt(n, null)).tag = 3;
        var r = e.type.getDerivedStateFromError;
        if ("function" == typeof r) {
            var l = t.value;
            n.payload = function() {
                return sr(e, t), r(l);
            };
        }
        var i = e.stateNode;
        return null !== i && "function" == typeof i.componentDidCatch && (n.callback = function() {
            "function" != typeof r && (null === nl ? nl = new Set([
                this
            ]) : nl.add(this), sr(e, t));
            var n = t.stack;
            this.componentDidCatch(t.value, {
                componentStack: null !== n ? n : ""
            });
        }), n;
    }
    var wr, zr = Math.ceil, Pr = c.ReactCurrentDispatcher, _r = c.ReactCurrentOwner, Nr = 0, Ur = 8, Rr = 16, Ir = 32, Mr = 0, Fr = 1, Qr = 2, Dr = 3, Wr = 4, jr = 5, Or = Nr, Br = null, Hr = null, Ar = 0, Lr = Mr, $r = null, qr = 1073741823, Vr = 1073741823, Kr = null, Gr = 0, Yr = !1, Jr = 0, Xr = 500, Zr = null, el = !1, tl = null, nl = null, rl = !1, ll = null, il = 90, al = null, ul = 0, ol = null, fl = 0;
    function cl() {
        return (Or & (Rr | Ir)) !== Nr ? 1073741821 - (qe() / 10 | 0) : 0 !== fl ? fl : fl = 1073741821 - (qe() / 10 | 0);
    }
    function sl(e, t, n) {
        if (0 == (2 & (t = t.mode))) return 1073741823;
        var r = Ve();
        if (0 == (4 & t)) return 99 === r ? 1073741823 : 1073741822;
        if ((Or & Rr) !== Nr) return Ar;
        if (null !== n) e = tt(e, 0 | n.timeoutMs || 5e3, 250);
        else switch(r){
            case 99:
                e = 1073741823;
                break;
            case 98:
                e = tt(e, 150, 100);
                break;
            case 97:
            case 96:
                e = tt(e, 5e3, 250);
                break;
            case 95:
                e = 2;
                break;
            default:
                throw Error(f(326));
        }
        return null !== Br && e === Ar && --e, e;
    }
    function dl(e, t) {
        if (50 < ul) throw ul = 0, ol = null, Error(f(185));
        if (null !== (e = pl(e, t))) {
            var n = Ve();
            1073741823 === t ? (Or & Ur) !== Nr && (Or & (Rr | Ir)) === Nr ? bl(e) : (hl(e), Or === Nr && Xe()) : hl(e), (4 & Or) === Nr || 98 !== n && 99 !== n || (null === al ? al = new Map([
                [
                    e,
                    t
                ]
            ]) : (void 0 === (n = al.get(e)) || n > t) && al.set(e, t));
        }
    }
    function pl(e, t) {
        e.expirationTime < t && (e.expirationTime = t);
        var n = e.alternate;
        null !== n && n.expirationTime < t && (n.expirationTime = t);
        var r = e.return, l = null;
        if (null === r && 3 === e.tag) l = e.stateNode;
        else for(; null !== r;){
            if (n = r.alternate, r.childExpirationTime < t && (r.childExpirationTime = t), null !== n && n.childExpirationTime < t && (n.childExpirationTime = t), null === r.return && 3 === r.tag) {
                l = r.stateNode;
                break;
            }
            r = r.return;
        }
        return null !== l && (Br === l && (kl(t), Lr === Wr && Yl(l, Ar)), Jl(l, t)), l;
    }
    function ml(e) {
        var t = e.lastExpiredTime;
        return 0 !== t ? t : Gl(e, t = e.firstPendingTime) ? (t = e.lastPingedTime) > (e = e.nextKnownPendingLevel) ? t : e : t;
    }
    function hl(e) {
        if (0 !== e.lastExpiredTime) e.callbackExpirationTime = 1073741823, e.callbackPriority = 99, e.callbackNode = Je(bl.bind(null, e));
        else {
            var t = ml(e), n = e.callbackNode;
            if (0 === t) null !== n && (e.callbackNode = null, e.callbackExpirationTime = 0, e.callbackPriority = 90);
            else {
                var r = cl();
                if (r = 1073741823 === t ? 99 : 1 === t || 2 === t ? 95 : 0 >= (r = 10 * (1073741821 - t) - 10 * (1073741821 - r)) ? 99 : 250 >= r ? 98 : 5250 >= r ? 97 : 95, null !== n) {
                    var l = e.callbackPriority;
                    if (e.callbackExpirationTime === t && l >= r) return;
                    n !== Oe && Ne(n);
                }
                e.callbackExpirationTime = t, e.callbackPriority = r, t = 1073741823 === t ? Je(bl.bind(null, e)) : Ye(r, gl.bind(null, e), {
                    timeout: 10 * (1073741821 - t) - qe()
                }), e.callbackNode = t;
            }
        }
    }
    function gl(e, t) {
        if (fl = 0, t) return Xl(e, t = cl()), hl(e), null;
        var n = ml(e);
        if (0 !== n) {
            if (t = e.callbackNode, (Or & (Rr | Ir)) !== Nr) throw Error(f(327));
            if (Rl(), e === Br && n === Ar || vl(e, n), null !== Hr) {
                var r = Or;
                Or |= Rr;
                for(var l = xl();;)try {
                    Cl();
                    break;
                } catch (t) {
                    Tl(e, t);
                }
                if (ct(), Or = r, Pr.current = l, Lr === Fr) throw t = $r, vl(e, n), Yl(e, n), hl(e), t;
                if (null === Hr) switch(l = e.finishedWork = e.current.alternate, e.finishedExpirationTime = n, r = Lr, Br = null, r){
                    case Mr:
                    case Fr:
                        throw Error(f(345));
                    case Qr:
                        Xl(e, 2 < n ? 2 : n);
                        break;
                    case Dr:
                        if (Yl(e, n), n === (r = e.lastSuspendedTime) && (e.nextKnownPendingLevel = Pl(l)), 1073741823 === qr && 10 < (l = Jr + Xr - qe())) {
                            if (Yr) {
                                var i = e.lastPingedTime;
                                if (0 === i || i >= n) {
                                    e.lastPingedTime = n, vl(e, n);
                                    break;
                                }
                            }
                            if (0 !== (i = ml(e)) && i !== n) break;
                            if (0 !== r && r !== n) {
                                e.lastPingedTime = r;
                                break;
                            }
                            e.timeoutHandle = A(_l.bind(null, e), l);
                            break;
                        }
                        _l(e);
                        break;
                    case Wr:
                        if (Yl(e, n), n === (r = e.lastSuspendedTime) && (e.nextKnownPendingLevel = Pl(l)), Yr && (0 === (l = e.lastPingedTime) || l >= n)) {
                            e.lastPingedTime = n, vl(e, n);
                            break;
                        }
                        if (0 !== (l = ml(e)) && l !== n) break;
                        if (0 !== r && r !== n) {
                            e.lastPingedTime = r;
                            break;
                        }
                        if (1073741823 !== Vr ? r = 10 * (1073741821 - Vr) - qe() : 1073741823 === qr ? r = 0 : (r = 10 * (1073741821 - qr) - 5e3, 0 > (r = (l = qe()) - r) && (r = 0), (n = 10 * (1073741821 - n) - l) < (r = (120 > r ? 120 : 480 > r ? 480 : 1080 > r ? 1080 : 1920 > r ? 1920 : 3e3 > r ? 3e3 : 4320 > r ? 4320 : 1960 * zr(r / 1960)) - r) && (r = n)), 10 < r) {
                            e.timeoutHandle = A(_l.bind(null, e), r);
                            break;
                        }
                        _l(e);
                        break;
                    case jr:
                        if (1073741823 !== qr && null !== Kr) {
                            i = qr;
                            var a = Kr;
                            if (0 >= (r = 0 | a.busyMinDurationMs) ? r = 0 : (l = 0 | a.busyDelayMs, r = (i = qe() - (10 * (1073741821 - i) - (0 | a.timeoutMs || 5e3))) <= l ? 0 : l + r - i), 10 < r) {
                                Yl(e, n), e.timeoutHandle = A(_l.bind(null, e), r);
                                break;
                            }
                        }
                        _l(e);
                        break;
                    default:
                        throw Error(f(329));
                }
                if (hl(e), e.callbackNode === t) return gl.bind(null, e);
            }
        }
        return null;
    }
    function bl(e) {
        var t = e.lastExpiredTime;
        if (t = 0 !== t ? t : 1073741823, e.finishedExpirationTime === t) _l(e);
        else {
            if ((Or & (Rr | Ir)) !== Nr) throw Error(f(327));
            if (Rl(), e === Br && t === Ar || vl(e, t), null !== Hr) {
                var n = Or;
                Or |= Rr;
                for(var r = xl();;)try {
                    Sl();
                    break;
                } catch (t) {
                    Tl(e, t);
                }
                if (ct(), Or = n, Pr.current = r, Lr === Fr) throw n = $r, vl(e, t), Yl(e, t), hl(e), n;
                if (null !== Hr) throw Error(f(261));
                e.finishedWork = e.current.alternate, e.finishedExpirationTime = t, Br = null, _l(e), hl(e);
            }
        }
        return null;
    }
    function yl(e, t) {
        if ((Or & (Rr | Ir)) !== Nr) throw Error(f(187));
        var n = Or;
        Or |= 1;
        try {
            return Ge(99, e.bind(null, t));
        } finally{
            Or = n, Xe();
        }
    }
    function vl(e, t) {
        e.finishedWork = null, e.finishedExpirationTime = 0;
        var n = e.timeoutHandle;
        if (n !== $ && (e.timeoutHandle = $, L(n)), null !== Hr) for(n = Hr.return; null !== n;){
            var r = n;
            switch(r.tag){
                case 1:
                    var l = r.type.childContextTypes;
                    null != l && Ee();
                    break;
                case 3:
                    Kt(), ke();
                    break;
                case 5:
                    Yt(r);
                    break;
                case 4:
                    Kt();
                    break;
                case 13:
                case 19:
                    me(Jt);
                    break;
                case 10:
                    dt(r);
            }
            n = n.return;
        }
        Br = e, Hr = Al(e.current, null), Ar = t, Lr = Mr, $r = null, Vr = qr = 1073741823, Kr = null, Gr = 0, Yr = !1;
    }
    function Tl(e, t) {
        for(;;){
            try {
                if (ct(), vn(), null === Hr || null === Hr.return) return Lr = Fr, $r = t, null;
                e: {
                    var n = e, r = Hr.return, l = Hr, i = t;
                    if (t = Ar, l.effectTag |= 2048, l.firstEffect = l.lastEffect = null, null !== i && "object" == typeof i && "function" == typeof i.then) {
                        var a = i, u = 0 != (1 & Jt.current), o = r;
                        do {
                            var f;
                            if (f = 13 === o.tag) {
                                var c = o.memoizedState;
                                if (null !== c) f = null !== c.dehydrated;
                                else {
                                    var s = o.memoizedProps;
                                    f = void 0 !== s.fallback && (!0 !== s.unstable_avoidThisFallback || !u);
                                }
                            }
                            if (f) {
                                var d = o.updateQueue;
                                if (null === d) {
                                    var p = new Set;
                                    p.add(a), o.updateQueue = p;
                                } else d.add(a);
                                if (0 == (2 & o.mode)) {
                                    if (o.effectTag |= 64, l.effectTag &= -2981, 1 === l.tag) if (null === l.alternate) l.tag = 17;
                                    else {
                                        var m = vt(1073741823, null);
                                        m.tag = 2, xt(l, m);
                                    }
                                    l.expirationTime = 1073741823;
                                    break e;
                                }
                                i = void 0, l = t;
                                var h = n.pingCache;
                                if (null === h ? (h = n.pingCache = new kr, i = new Set, h.set(a, i)) : void 0 === (i = h.get(a)) && (i = new Set, h.set(a, i)), !i.has(l)) {
                                    i.add(l);
                                    var g = Ql.bind(null, n, a, l);
                                    a.then(g, g);
                                }
                                o.effectTag |= 4096, o.expirationTime = t;
                                break e;
                            }
                            o = o.return;
                        }while (null !== o)
                        i = Error((z(l.type) || "A React component") + " suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display." + se(l));
                    }
                    Lr !== jr && (Lr = Qr), i = fr(i, l), o = r;
                    do {
                        switch(o.tag){
                            case 3:
                                a = i, o.effectTag |= 4096, o.expirationTime = t, Et(o, Sr(o, a, t));
                                break e;
                            case 1:
                                a = i;
                                var b = o.type, y = o.stateNode;
                                if (0 == (64 & o.effectTag) && ("function" == typeof b.getDerivedStateFromError || null !== y && "function" == typeof y.componentDidCatch && (null === nl || !nl.has(y)))) {
                                    o.effectTag |= 4096, o.expirationTime = t, Et(o, Cr(o, a, t));
                                    break e;
                                }
                        }
                        o = o.return;
                    }while (null !== o)
                }
                Hr = zl(Hr);
            } catch (e) {
                t = e;
                continue;
            }
            break;
        }
    }
    function xl() {
        var e = Pr.current;
        return Pr.current = Qn, null === e ? Qn : e;
    }
    function El(e, t) {
        e < qr && 2 < e && (qr = e), null !== t && e < Vr && 2 < e && (Vr = e, Kr = t);
    }
    function kl(e) {
        e > Gr && (Gr = e);
    }
    function Sl() {
        for(; null !== Hr;)Hr = wl(Hr);
    }
    function Cl() {
        for(; null !== Hr && !Ue();)Hr = wl(Hr);
    }
    function wl(e) {
        var t = wr(e.alternate, e, Ar);
        return e.memoizedProps = e.pendingProps, null === t && (t = zl(e)), _r.current = null, t;
    }
    function zl(e) {
        Hr = e;
        do {
            var t = Hr.alternate;
            if (e = Hr.return, 0 == (2048 & Hr.effectTag)) {
                e: {
                    var n = t, r = Ar, l = (t = Hr).pendingProps;
                    switch(t.tag){
                        case 2:
                        case 16:
                        case 15:
                        case 0:
                        case 11:
                        case 7:
                        case 8:
                        case 12:
                        case 9:
                        case 14:
                        case 20:
                        case 21:
                            break;
                        case 1:
                        case 17:
                            xe(t.type) && Ee();
                            break;
                        case 3:
                            Kt(), ke(), (l = t.stateNode).pendingContext && (l.context = l.pendingContext, l.pendingContext = null), null === n || n.child, Jn(t);
                            break;
                        case 5:
                            Yt(t);
                            var i = qt($t.current);
                            if (r = t.type, null !== n && null != t.stateNode) Xn(n, t, r, l, i), n.ref !== t.ref && (t.effectTag |= 128);
                            else if (l) {
                                n = qt(At.current);
                                var a = D(r, l, i, n, t);
                                Yn(a, t, !1, !1), t.stateNode = a, j(a, r, l, i, n) && ar(t), null !== t.ref && (t.effectTag |= 128);
                            } else if (null === t.stateNode) throw Error(f(166));
                            break;
                        case 6:
                            if (n && null != t.stateNode) Zn(n, t, n.memoizedProps, l);
                            else {
                                if ("string" != typeof l && null === t.stateNode) throw Error(f(166));
                                n = qt($t.current), i = qt(At.current), t.stateNode = H(l, n, i, t);
                            }
                            break;
                        case 13:
                            if (me(Jt), l = t.memoizedState, 0 != (64 & t.effectTag)) {
                                t.expirationTime = r;
                                break e;
                            }
                            l = null !== l, i = !1, null === n ? t.memoizedProps.fallback : (i = null !== (r = n.memoizedState), l || null === r || null !== (r = n.child.sibling) && (null !== (a = t.firstEffect) ? (t.firstEffect = r, r.nextEffect = a) : (t.firstEffect = t.lastEffect = r, r.nextEffect = null), r.effectTag = 8)), l && !i && 0 != (2 & t.mode) && (null === n && !0 !== t.memoizedProps.unstable_avoidThisFallback || 0 != (1 & Jt.current) ? Lr === Mr && (Lr = Dr) : (Lr !== Mr && Lr !== Dr || (Lr = Wr), 0 !== Gr && null !== Br && (Yl(Br, Ar), Jl(Br, Gr)))), (l || i) && (t.effectTag |= 4);
                            break;
                        case 4:
                            Kt(), Jn(t);
                            break;
                        case 10:
                            dt(t);
                            break;
                        case 19:
                            if (me(Jt), null === (l = t.memoizedState)) break;
                            if (i = 0 != (64 & t.effectTag), null === (a = l.rendering)) {
                                if (i) ur(l, !1);
                                else if (Lr !== Mr || null !== n && 0 != (64 & n.effectTag)) for(n = t.child; null !== n;){
                                    if (null !== (a = Xt(n))) {
                                        for(t.effectTag |= 64, ur(l, !1), null !== (n = a.updateQueue) && (t.updateQueue = n, t.effectTag |= 4), null === l.lastEffect && (t.firstEffect = null), t.lastEffect = l.lastEffect, n = r, l = t.child; null !== l;)r = n, (i = l).effectTag &= 2, i.nextEffect = null, i.firstEffect = null, i.lastEffect = null, null === (a = i.alternate) ? (i.childExpirationTime = 0, i.expirationTime = r, i.child = null, i.memoizedProps = null, i.memoizedState = null, i.updateQueue = null, i.dependencies = null) : (i.childExpirationTime = a.childExpirationTime, i.expirationTime = a.expirationTime, i.child = a.child, i.memoizedProps = a.memoizedProps, i.memoizedState = a.memoizedState, i.updateQueue = a.updateQueue, r = a.dependencies, i.dependencies = null === r ? null : {
                                            expirationTime: r.expirationTime,
                                            firstContext: r.firstContext,
                                            responders: r.responders
                                        }), l = l.sibling;
                                        he(Jt, 1 & Jt.current | 2), t = t.child;
                                        break e;
                                    }
                                    n = n.sibling;
                                }
                            } else {
                                if (!i) if (null !== (n = Xt(a))) {
                                    if (t.effectTag |= 64, i = !0, null !== (n = n.updateQueue) && (t.updateQueue = n, t.effectTag |= 4), ur(l, !0), null === l.tail && "hidden" === l.tailMode) {
                                        null !== (t = t.lastEffect = l.lastEffect) && (t.nextEffect = null);
                                        break;
                                    }
                                } else qe() > l.tailExpiration && 1 < r && (t.effectTag |= 64, i = !0, ur(l, !1), t.expirationTime = t.childExpirationTime = r - 1);
                                l.isBackwards ? (a.sibling = t.child, t.child = a) : (null !== (n = l.last) ? n.sibling = a : t.child = a, l.last = a);
                            }
                            if (null !== l.tail) {
                                0 === l.tailExpiration && (l.tailExpiration = qe() + 500), n = l.tail, l.rendering = n, l.tail = n.sibling, l.lastEffect = t.lastEffect, n.sibling = null, l = Jt.current, he(Jt, l = i ? 1 & l | 2 : 1 & l), t = n;
                                break e;
                            }
                            break;
                        default:
                            throw Error(f(156, t.tag));
                    }
                    t = null;
                }
                if (n = Hr, 1 === Ar || 1 !== n.childExpirationTime) {
                    for(l = 0, i = n.child; null !== i;)(r = i.expirationTime) > l && (l = r), (a = i.childExpirationTime) > l && (l = a), i = i.sibling;
                    n.childExpirationTime = l;
                }
                if (null !== t) return t;
                null !== e && 0 == (2048 & e.effectTag) && (null === e.firstEffect && (e.firstEffect = Hr.firstEffect), null !== Hr.lastEffect && (null !== e.lastEffect && (e.lastEffect.nextEffect = Hr.firstEffect), e.lastEffect = Hr.lastEffect), 1 < Hr.effectTag && (null !== e.lastEffect ? e.lastEffect.nextEffect = Hr : e.firstEffect = Hr, e.lastEffect = Hr));
            } else {
                if (null !== (t = or(Hr))) return t.effectTag &= 2047, t;
                null !== e && (e.firstEffect = e.lastEffect = null, e.effectTag |= 2048);
            }
            if (null !== (t = Hr.sibling)) return t;
            Hr = e;
        }while (null !== Hr)
        return Lr === Mr && (Lr = jr), null;
    }
    function Pl(e) {
        var t = e.expirationTime;
        return t > (e = e.childExpirationTime) ? t : e;
    }
    function _l(e) {
        var t = Ve();
        return Ge(99, Nl.bind(null, e, t)), null;
    }
    function Nl(e, t) {
        if (Rl(), (Or & (Rr | Ir)) !== Nr) throw Error(f(327));
        var n = e.finishedWork, r = e.finishedExpirationTime;
        if (null === n) return null;
        if (e.finishedWork = null, e.finishedExpirationTime = 0, n === e.current) throw Error(f(177));
        e.callbackNode = null, e.callbackExpirationTime = 0, e.callbackPriority = 90, e.nextKnownPendingLevel = 0;
        var l = Pl(n);
        if (e.firstPendingTime = l, r <= e.lastSuspendedTime ? e.firstSuspendedTime = e.lastSuspendedTime = e.nextKnownPendingLevel = 0 : r <= e.firstSuspendedTime && (e.firstSuspendedTime = r - 1), r <= e.lastPingedTime && (e.lastPingedTime = 0), r <= e.lastExpiredTime && (e.lastExpiredTime = 0), e === Br && (Hr = Br = null, Ar = 0), 1 < n.effectTag ? null !== n.lastEffect ? (n.lastEffect.nextEffect = n, l = n.firstEffect) : l = n : l = n.firstEffect, null !== l) {
            var i = Or;
            Or |= Ir, _r.current = null, F(e.containerInfo), Zr = l;
            do {
                try {
                    Ul();
                } catch (e) {
                    if (null === Zr) throw Error(f(330));
                    Fl(Zr, e), Zr = Zr.nextEffect;
                }
            }while (null !== Zr)
            Zr = l;
            do {
                try {
                    for(var a = e, u = t; null !== Zr;){
                        var o = Zr.effectTag;
                        if (16 & o && q && re(Zr.stateNode), 128 & o) {
                            var c = Zr.alternate;
                            if (null !== c) {
                                var s = c.ref;
                                null !== s && ("function" == typeof s ? s(null) : s.current = null);
                            }
                        }
                        switch(1038 & o){
                            case 2:
                                vr(Zr), Zr.effectTag &= -3;
                                break;
                            case 6:
                                vr(Zr), Zr.effectTag &= -3, xr(Zr.alternate, Zr);
                                break;
                            case 1024:
                                Zr.effectTag &= -1025;
                                break;
                            case 1028:
                                Zr.effectTag &= -1025, xr(Zr.alternate, Zr);
                                break;
                            case 4:
                                xr(Zr.alternate, Zr);
                                break;
                            case 8:
                                var d = a, p = Zr, m = u;
                                ("TURBOPACK compile-time truthy", 1) ? Tr(d, p, m) : ("TURBOPACK unreachable", undefined), br(p);
                        }
                        Zr = Zr.nextEffect;
                    }
                } catch (e) {
                    if (null === Zr) throw Error(f(330));
                    Fl(Zr, e), Zr = Zr.nextEffect;
                }
            }while (null !== Zr)
            Q(e.containerInfo), e.current = n, Zr = l;
            do {
                try {
                    for(o = r; null !== Zr;){
                        var h = Zr.effectTag;
                        if (36 & h) {
                            var g = Zr.alternate;
                            switch(s = o, (c = Zr).tag){
                                case 0:
                                case 11:
                                case 15:
                                    mr(16, 32, c);
                                    break;
                                case 1:
                                    var b = c.stateNode;
                                    if (4 & c.effectTag) if (null === g) b.componentDidMount();
                                    else {
                                        var y = c.elementType === c.type ? g.memoizedProps : it(c.type, g.memoizedProps);
                                        b.componentDidUpdate(y, g.memoizedState, b.__reactInternalSnapshotBeforeUpdate);
                                    }
                                    var v = c.updateQueue;
                                    null !== v && wt(0, v, b);
                                    break;
                                case 3:
                                    var T = c.updateQueue;
                                    if (null !== T) {
                                        if (a = null, null !== c.child) switch(c.child.tag){
                                            case 5:
                                                a = R(c.child.stateNode);
                                                break;
                                            case 1:
                                                a = c.child.stateNode;
                                        }
                                        wt(0, T, a);
                                    }
                                    break;
                                case 5:
                                    var x = c.stateNode;
                                    null === g && 4 & c.effectTag && J(x, c.type, c.memoizedProps, c);
                                    break;
                                case 6:
                                case 4:
                                case 12:
                                case 19:
                                case 17:
                                case 20:
                                case 21:
                                    break;
                                case 13:
                                    V && c.memoizedState;
                                    break;
                                default:
                                    throw Error(f(163));
                            }
                        }
                        if (128 & h) {
                            c = void 0;
                            var E = Zr.ref;
                            if (null !== E) {
                                var k = Zr.stateNode;
                                c = 5 === Zr.tag ? R(k) : k, "function" == typeof E ? E(c) : E.current = c;
                            }
                        }
                        Zr = Zr.nextEffect;
                    }
                } catch (e) {
                    if (null === Zr) throw Error(f(330));
                    Fl(Zr, e), Zr = Zr.nextEffect;
                }
            }while (null !== Zr)
            Zr = null, Be(), Or = i;
        } else e.current = n;
        if (rl) rl = !1, ll = e, il = t;
        else for(Zr = l; null !== Zr;)t = Zr.nextEffect, Zr.nextEffect = null, Zr = t;
        if (0 === (t = e.firstPendingTime) && (nl = null), 1073741823 === t ? e === ol ? ul++ : (ul = 0, ol = e) : ul = 0, "function" == typeof Wl && Wl(n.stateNode, r), hl(e), el) throw el = !1, e = tl, tl = null, e;
        return (Or & Ur) !== Nr || Xe(), null;
    }
    function Ul() {
        for(; null !== Zr;){
            var e = Zr.effectTag;
            0 != (256 & e) && pr(Zr.alternate, Zr), 0 == (512 & e) || rl || (rl = !0, Ye(97, function() {
                return Rl(), null;
            })), Zr = Zr.nextEffect;
        }
    }
    function Rl() {
        if (90 !== il) {
            var e = 97 < il ? 97 : il;
            return il = 90, Ge(e, Il);
        }
    }
    function Il() {
        if (null === ll) return !1;
        var e = ll;
        if (ll = null, (Or & (Rr | Ir)) !== Nr) throw Error(f(331));
        var t = Or;
        for(Or |= Ir, e = e.current.firstEffect; null !== e;){
            try {
                var n = e;
                if (0 != (512 & n.effectTag)) switch(n.tag){
                    case 0:
                    case 11:
                    case 15:
                        mr(128, 0, n), mr(0, 64, n);
                }
            } catch (t) {
                if (null === e) throw Error(f(330));
                Fl(e, t);
            }
            n = e.nextEffect, e.nextEffect = null, e = n;
        }
        return Or = t, Xe(), !0;
    }
    function Ml(e, t, n) {
        xt(e, t = Sr(e, t = fr(n, t), 1073741823)), null !== (e = pl(e, 1073741823)) && hl(e);
    }
    function Fl(e, t) {
        if (3 === e.tag) Ml(e, e, t);
        else for(var n = e.return; null !== n;){
            if (3 === n.tag) {
                Ml(n, e, t);
                break;
            }
            if (1 === n.tag) {
                var r = n.stateNode;
                if ("function" == typeof n.type.getDerivedStateFromError || "function" == typeof r.componentDidCatch && (null === nl || !nl.has(r))) {
                    xt(n, e = Cr(n, e = fr(t, e), 1073741823)), null !== (n = pl(n, 1073741823)) && hl(n);
                    break;
                }
            }
            n = n.return;
        }
    }
    function Ql(e, t, n) {
        var r = e.pingCache;
        null !== r && r.delete(t), Br === e && Ar === n ? Lr === Wr || Lr === Dr && 1073741823 === qr && qe() - Jr < Xr ? vl(e, Ar) : Yr = !0 : Gl(e, n) && (0 !== (t = e.lastPingedTime) && t < n || (e.lastPingedTime = n, e.finishedExpirationTime === n && (e.finishedExpirationTime = 0, e.finishedWork = null), hl(e)));
    }
    function Dl(e, t) {
        var n = e.stateNode;
        null !== n && n.delete(t), 0 == (t = 0) && (t = sl(t = cl(), e, null)), null !== (e = pl(e, t)) && hl(e);
    }
    wr = function(e, t, n) {
        var r = t.expirationTime;
        if (null !== e) {
            var l = t.pendingProps;
            if (e.memoizedProps !== l || ye.current) On = !0;
            else {
                if (r < n) {
                    switch(On = !1, t.tag){
                        case 3:
                            Gn(t);
                            break;
                        case 5:
                            if (Gt(t), 4 & t.mode && 1 !== n && (null)(t.type, l)) return t.expirationTime = t.childExpirationTime = 1, null;
                            break;
                        case 1:
                            xe(t.type) && we(t);
                            break;
                        case 4:
                            Vt(t, t.stateNode.containerInfo);
                            break;
                        case 10:
                            st(t, t.memoizedProps.value);
                            break;
                        case 13:
                            if (null !== t.memoizedState) return 0 !== (r = t.child.childExpirationTime) && r >= n ? tr(e, t, n) : (he(Jt, 1 & Jt.current), null !== (t = ir(e, t, n)) ? t.sibling : null);
                            he(Jt, 1 & Jt.current);
                            break;
                        case 19:
                            if (r = t.childExpirationTime >= n, 0 != (64 & e.effectTag)) {
                                if (r) return lr(e, t, n);
                                t.effectTag |= 64;
                            }
                            if (null !== (l = t.memoizedState) && (l.rendering = null, l.tail = null), he(Jt, Jt.current), !r) return null;
                    }
                    return ir(e, t, n);
                }
                On = !1;
            }
        } else On = !1;
        switch(t.expirationTime = 0, t.tag){
            case 2:
                if (r = t.type, null !== e && (e.alternate = null, t.alternate = null, t.effectTag |= 2), e = t.pendingProps, l = Te(t, be.current), mt(t, n), l = yn(null, t, r, e, l, n), t.effectTag |= 1, "object" == typeof l && null !== l && "function" == typeof l.render && void 0 === l.$$typeof) {
                    if (t.tag = 1, vn(), xe(r)) {
                        var i = !0;
                        we(t);
                    } else i = !1;
                    t.memoizedState = null !== l.state && void 0 !== l.state ? l.state : null;
                    var a = r.getDerivedStateFromProps;
                    "function" == typeof a && Nt(t, r, a, e), l.updater = Ut, t.stateNode = l, l._reactInternalFiber = t, Ft(t, r, e, n), t = Kn(null, t, r, !0, i, n);
                } else t.tag = 0, Bn(null, t, l, n), t = t.child;
                return t;
            case 16:
                if (l = t.elementType, null !== e && (e.alternate = null, t.alternate = null, t.effectTag |= 2), e = t.pendingProps, function(e) {
                    if (-1 === e._status) {
                        e._status = 0;
                        var t = e._ctor;
                        t = t(), e._result = t, t.then(function(t) {
                            0 === e._status && (t = t.default, e._status = 1, e._result = t);
                        }, function(t) {
                            0 === e._status && (e._status = 2, e._result = t);
                        });
                    }
                }(l), 1 !== l._status) throw l._result;
                switch(l = l._result, t.type = l, i = t.tag = function(e) {
                    if ("function" == typeof e) return Hl(e) ? 1 : 0;
                    if (null != e) {
                        if ((e = e.$$typeof) === T) return 11;
                        if (e === k) return 14;
                    }
                    return 2;
                }(l), e = it(l, e), i){
                    case 0:
                        t = qn(null, t, l, e, n);
                        break;
                    case 1:
                        t = Vn(null, t, l, e, n);
                        break;
                    case 11:
                        t = Hn(null, t, l, e, n);
                        break;
                    case 14:
                        t = An(null, t, l, it(l.type, e), r, n);
                        break;
                    default:
                        throw Error(f(306, l, ""));
                }
                return t;
            case 0:
                return r = t.type, l = t.pendingProps, qn(e, t, r, l = t.elementType === r ? l : it(r, l), n);
            case 1:
                return r = t.type, l = t.pendingProps, Vn(e, t, r, l = t.elementType === r ? l : it(r, l), n);
            case 3:
                if (Gn(t), null === (r = t.updateQueue)) throw Error(f(282));
                if (l = null !== (l = t.memoizedState) ? l.element : null, Ct(t, r, t.pendingProps, null, n), (r = t.memoizedState.element) === l) t = ir(e, t, n);
                else {
                    if ((l = t.stateNode.hydrate) && (l = !1), l) for(n = Bt(t, null, r, n), t.child = n; n;)n.effectTag = -3 & n.effectTag | 1024, n = n.sibling;
                    else Bn(e, t, r, n);
                    t = t.child;
                }
                return t;
            case 5:
                return Gt(t), r = t.type, l = t.pendingProps, i = null !== e ? e.memoizedProps : null, a = l.children, B(r, l) ? a = null : null !== i && B(r, i) && (t.effectTag |= 16), $n(e, t), 4 & t.mode && 1 !== n && (null)(r, l) ? (t.expirationTime = t.childExpirationTime = 1, t = null) : (Bn(e, t, a, n), t = t.child), t;
            case 6:
                return null;
            case 13:
                return tr(e, t, n);
            case 4:
                return Vt(t, t.stateNode.containerInfo), r = t.pendingProps, null === e ? t.child = Ot(t, null, r, n) : Bn(e, t, r, n), t.child;
            case 11:
                return r = t.type, l = t.pendingProps, Hn(e, t, r, l = t.elementType === r ? l : it(r, l), n);
            case 7:
                return Bn(e, t, t.pendingProps, n), t.child;
            case 8:
            case 12:
                return Bn(e, t, t.pendingProps.children, n), t.child;
            case 10:
                e: {
                    if (r = t.type._context, l = t.pendingProps, a = t.memoizedProps, st(t, i = l.value), null !== a) {
                        var u = a.value;
                        if (0 == (i = nt(u, i) ? 0 : 0 | ("function" == typeof r._calculateChangedBits ? r._calculateChangedBits(u, i) : 1073741823))) {
                            if (a.children === l.children && !ye.current) {
                                t = ir(e, t, n);
                                break e;
                            }
                        } else for(null !== (u = t.child) && (u.return = t); null !== u;){
                            var o = u.dependencies;
                            if (null !== o) {
                                a = u.child;
                                for(var c = o.firstContext; null !== c;){
                                    if (c.context === r && 0 != (c.observedBits & i)) {
                                        1 === u.tag && ((c = vt(n, null)).tag = 2, xt(u, c)), u.expirationTime < n && (u.expirationTime = n), null !== (c = u.alternate) && c.expirationTime < n && (c.expirationTime = n), pt(u.return, n), o.expirationTime < n && (o.expirationTime = n);
                                        break;
                                    }
                                    c = c.next;
                                }
                            } else a = 10 === u.tag && u.type === t.type ? null : u.child;
                            if (null !== a) a.return = u;
                            else for(a = u; null !== a;){
                                if (a === t) {
                                    a = null;
                                    break;
                                }
                                if (null !== (u = a.sibling)) {
                                    u.return = a.return, a = u;
                                    break;
                                }
                                a = a.return;
                            }
                            u = a;
                        }
                    }
                    Bn(e, t, l.children, n), t = t.child;
                }
                return t;
            case 9:
                return l = t.type, r = (i = t.pendingProps).children, mt(t, n), r = r(l = ht(l, i.unstable_observedBits)), t.effectTag |= 1, Bn(e, t, r, n), t.child;
            case 14:
                return i = it(l = t.type, t.pendingProps), An(e, t, l, i = it(l.type, i), r, n);
            case 15:
                return Ln(e, t, t.type, t.pendingProps, r, n);
            case 17:
                return r = t.type, l = t.pendingProps, l = t.elementType === r ? l : it(r, l), null !== e && (e.alternate = null, t.alternate = null, t.effectTag |= 2), t.tag = 1, xe(r) ? (e = !0, we(t)) : e = !1, mt(t, n), It(t, r, l), Ft(t, r, l, n), Kn(null, t, r, !0, e, n);
            case 19:
                return lr(e, t, n);
        }
        throw Error(f(156, t.tag));
    };
    var Wl = null, jl = null;
    function Ol(e, t, n, r) {
        this.tag = e, this.key = n, this.sibling = this.child = this.return = this.stateNode = this.type = this.elementType = null, this.index = 0, this.ref = null, this.pendingProps = t, this.dependencies = this.memoizedState = this.updateQueue = this.memoizedProps = null, this.mode = r, this.effectTag = 0, this.lastEffect = this.firstEffect = this.nextEffect = null, this.childExpirationTime = this.expirationTime = 0, this.alternate = null;
    }
    function Bl(e, t, n, r) {
        return new Ol(e, t, n, r);
    }
    function Hl(e) {
        return !(!(e = e.prototype) || !e.isReactComponent);
    }
    function Al(e, t) {
        var n = e.alternate;
        return null === n ? ((n = Bl(e.tag, t, e.key, e.mode)).elementType = e.elementType, n.type = e.type, n.stateNode = e.stateNode, n.alternate = e, e.alternate = n) : (n.pendingProps = t, n.effectTag = 0, n.nextEffect = null, n.firstEffect = null, n.lastEffect = null), n.childExpirationTime = e.childExpirationTime, n.expirationTime = e.expirationTime, n.child = e.child, n.memoizedProps = e.memoizedProps, n.memoizedState = e.memoizedState, n.updateQueue = e.updateQueue, t = e.dependencies, n.dependencies = null === t ? null : {
            expirationTime: t.expirationTime,
            firstContext: t.firstContext,
            responders: t.responders
        }, n.sibling = e.sibling, n.index = e.index, n.ref = e.ref, n;
    }
    function Ll(e, t, n, r, l, i) {
        var a = 2;
        if (r = e, "function" == typeof e) Hl(e) && (a = 1);
        else if ("string" == typeof e) a = 5;
        else e: switch(e){
            case m:
                return $l(n.children, l, i, t);
            case v:
                a = 8, l |= 7;
                break;
            case h:
                a = 8, l |= 1;
                break;
            case g:
                return (e = Bl(12, n, t, 8 | l)).elementType = g, e.type = g, e.expirationTime = i, e;
            case x:
                return (e = Bl(13, n, t, l)).type = x, e.elementType = x, e.expirationTime = i, e;
            case E:
                return (e = Bl(19, n, t, l)).elementType = E, e.expirationTime = i, e;
            default:
                if ("object" == typeof e && null !== e) switch(e.$$typeof){
                    case b:
                        a = 10;
                        break e;
                    case y:
                        a = 9;
                        break e;
                    case T:
                        a = 11;
                        break e;
                    case k:
                        a = 14;
                        break e;
                    case S:
                        a = 16, r = null;
                        break e;
                }
                throw Error(f(130, null == e ? e : typeof e, ""));
        }
        return (t = Bl(a, n, t, l)).elementType = e, t.type = r, t.expirationTime = i, t;
    }
    function $l(e, t, n, r) {
        return (e = Bl(7, e, r, t)).expirationTime = n, e;
    }
    function ql(e, t, n) {
        return (e = Bl(6, e, null, t)).expirationTime = n, e;
    }
    function Vl(e, t, n) {
        return (t = Bl(4, null !== e.children ? e.children : [], e.key, t)).expirationTime = n, t.stateNode = {
            containerInfo: e.containerInfo,
            pendingChildren: null,
            implementation: e.implementation
        }, t;
    }
    function Kl(e, t, n) {
        this.tag = t, this.current = null, this.containerInfo = e, this.pingCache = this.pendingChildren = null, this.finishedExpirationTime = 0, this.finishedWork = null, this.timeoutHandle = $, this.pendingContext = this.context = null, this.hydrate = n, this.callbackNode = null, this.callbackPriority = 90, this.lastExpiredTime = this.lastPingedTime = this.nextKnownPendingLevel = this.lastSuspendedTime = this.firstSuspendedTime = this.firstPendingTime = 0;
    }
    function Gl(e, t) {
        var n = e.firstSuspendedTime;
        return e = e.lastSuspendedTime, 0 !== n && n >= t && e <= t;
    }
    function Yl(e, t) {
        var n = e.firstSuspendedTime, r = e.lastSuspendedTime;
        n < t && (e.firstSuspendedTime = t), (r > t || 0 === n) && (e.lastSuspendedTime = t), t <= e.lastPingedTime && (e.lastPingedTime = 0), t <= e.lastExpiredTime && (e.lastExpiredTime = 0);
    }
    function Jl(e, t) {
        t > e.firstPendingTime && (e.firstPendingTime = t);
        var n = e.firstSuspendedTime;
        0 !== n && (t >= n ? e.firstSuspendedTime = e.lastSuspendedTime = e.nextKnownPendingLevel = 0 : t >= e.lastSuspendedTime && (e.lastSuspendedTime = t + 1), t > e.nextKnownPendingLevel && (e.nextKnownPendingLevel = t));
    }
    function Xl(e, t) {
        var n = e.lastExpiredTime;
        (0 === n || n > t) && (e.lastExpiredTime = t);
    }
    function Zl(e) {
        var t = e._reactInternalFiber;
        if (void 0 === t) {
            if ("function" == typeof e.render) throw Error(f(188));
            throw Error(f(268, Object.keys(e)));
        }
        return null === (e = U(t)) ? null : e.stateNode;
    }
    function ei(e, t) {
        null !== (e = e.memoizedState) && null !== e.dehydrated && e.retryTime < t && (e.retryTime = t);
    }
    function ti(e, t) {
        ei(e, t), (e = e.alternate) && ei(e, t);
    }
    var ni = {
        createContainer: function(e, t, n) {
            return e = new Kl(e, t, n), t = Bl(3, null, null, 2 === t ? 7 : 1 === t ? 3 : 0), e.current = t, t.stateNode = e;
        },
        updateContainer: function(e, t, n, r) {
            var l = t.current, i = cl(), a = Pt.suspense;
            i = sl(i, l, a);
            e: if (n) {
                t: {
                    if (P(n = n._reactInternalFiber) !== n || 1 !== n.tag) throw Error(f(170));
                    var u = n;
                    do {
                        switch(u.tag){
                            case 3:
                                u = u.stateNode.context;
                                break t;
                            case 1:
                                if (xe(u.type)) {
                                    u = u.stateNode.__reactInternalMemoizedMergedChildContext;
                                    break t;
                                }
                        }
                        u = u.return;
                    }while (null !== u)
                    throw Error(f(171));
                }
                if (1 === n.tag) {
                    var o = n.type;
                    if (xe(o)) {
                        n = Ce(n, o, u);
                        break e;
                    }
                }
                n = u;
            } else n = ge;
            return null === t.context ? t.context = n : t.pendingContext = n, (t = vt(i, a)).payload = {
                element: e
            }, null !== (r = void 0 === r ? null : r) && (t.callback = r), xt(l, t), dl(l, i), i;
        },
        batchedEventUpdates: function(e, t) {
            var n = Or;
            Or |= 2;
            try {
                return e(t);
            } finally{
                (Or = n) === Nr && Xe();
            }
        },
        batchedUpdates: function(e, t) {
            var n = Or;
            Or |= 1;
            try {
                return e(t);
            } finally{
                (Or = n) === Nr && Xe();
            }
        },
        unbatchedUpdates: function(e, t) {
            var n = Or;
            Or &= -2, Or |= Ur;
            try {
                return e(t);
            } finally{
                (Or = n) === Nr && Xe();
            }
        },
        deferredUpdates: function(e) {
            return Ge(97, e);
        },
        syncUpdates: function(e, t, n, r) {
            return Ge(99, e.bind(null, t, n, r));
        },
        discreteUpdates: function(e, t, n, r) {
            var l = Or;
            Or |= 4;
            try {
                return Ge(98, e.bind(null, t, n, r));
            } finally{
                (Or = l) === Nr && Xe();
            }
        },
        flushDiscreteUpdates: function() {
            (Or & (1 | Rr | Ir)) === Nr && (function() {
                if (null !== al) {
                    var e = al;
                    al = null, e.forEach(function(e, t) {
                        Xl(t, e), hl(t);
                    }), Xe();
                }
            }(), Rl());
        },
        flushControlled: function(e) {
            var t = Or;
            Or |= 1;
            try {
                Ge(99, e);
            } finally{
                (Or = t) === Nr && Xe();
            }
        },
        flushSync: yl,
        flushPassiveEffects: Rl,
        IsThisRendererActing: {
            current: !1
        },
        getPublicRootInstance: function(e) {
            return (e = e.current).child ? 5 === e.child.tag ? R(e.child.stateNode) : e.child.stateNode : null;
        },
        attemptSynchronousHydration: function(e) {
            switch(e.tag){
                case 3:
                    var t = e.stateNode;
                    t.hydrate && function(e, t) {
                        Xl(e, t), hl(e), (Or & (Rr | Ir)) === Nr && Xe();
                    }(t, t.firstPendingTime);
                    break;
                case 13:
                    yl(function() {
                        return dl(e, 1073741823);
                    }), t = tt(cl(), 150, 100), ti(e, t);
            }
        },
        attemptUserBlockingHydration: function(e) {
            if (13 === e.tag) {
                var t = tt(cl(), 150, 100);
                dl(e, t), ti(e, t);
            }
        },
        attemptContinuousHydration: function(e) {
            if (13 === e.tag) {
                cl();
                var t = et++;
                dl(e, t), ti(e, t);
            }
        },
        attemptHydrationAtCurrentPriority: function(e) {
            if (13 === e.tag) {
                var t = cl();
                dl(e, t = sl(t, e, null)), ti(e, t);
            }
        },
        findHostInstance: Zl,
        findHostInstanceWithWarning: function(e) {
            return Zl(e);
        },
        findHostInstanceWithNoPortals: function(e) {
            return null === (e = function(e) {
                if (!(e = N(e))) return null;
                for(var t = e;;){
                    if (5 === t.tag || 6 === t.tag) return t;
                    if (t.child && 4 !== t.tag) t.child.return = t, t = t.child;
                    else {
                        if (t === e) break;
                        for(; !t.sibling;){
                            if (!t.return || t.return === e) return null;
                            t = t.return;
                        }
                        t.sibling.return = t.return, t = t.sibling;
                    }
                }
                return null;
            }(e)) ? null : 20 === e.tag ? e.stateNode.instance : e.stateNode;
        },
        shouldSuspend: function() {
            return !1;
        },
        injectIntoDevTools: function(e) {
            var t = e.findFiberByHostInstance;
            return function(e) {
                if ("undefined" == typeof __REACT_DEVTOOLS_GLOBAL_HOOK__) return !1;
                var t = __REACT_DEVTOOLS_GLOBAL_HOOK__;
                if (t.isDisabled || !t.supportsFiber) return !0;
                try {
                    var n = t.inject(e);
                    Wl = function(e) {
                        try {
                            t.onCommitFiberRoot(n, e, void 0, 64 == (64 & e.current.effectTag));
                        } catch (e) {}
                    }, jl = function(e) {
                        try {
                            t.onCommitFiberUnmount(n, e);
                        } catch (e) {}
                    };
                } catch (e) {}
                return !0;
            }(l({}, e, {
                overrideHookState: null,
                overrideProps: null,
                setSuspenseHandler: null,
                scheduleUpdate: null,
                currentDispatcherRef: c.ReactCurrentDispatcher,
                findHostInstanceByFiber: function(e) {
                    return null === (e = U(e)) ? null : e.stateNode;
                },
                findFiberByHostInstance: function(e) {
                    return t ? t(e) : null;
                },
                findHostInstancesForRefresh: null,
                scheduleRefresh: null,
                scheduleRoot: null,
                setRefreshHandler: null,
                getCurrentFiber: null
            }));
        }
    };
    i.exports = ni.default || ni;
    var ri = i.exports;
    return i.exports = n, ri;
};
var o = r(a.exports);
const f = (e, t)=>{
    const n = Object.keys(e), r = Object.keys(t);
    if (n.length !== r.length) return !1;
    for(let r = 0; r < n.length; r += 1){
        const l = n[r];
        if ("render" === l && !e[l] != !t[l]) return !1;
        if ("children" !== l && e[l] !== t[l]) {
            if ("object" == typeof e[l] && "object" == typeof t[l] && f(e[l], t[l])) continue;
            return !1;
        }
        if ("children" === l && ("string" == typeof e[l] || "string" == typeof t[l])) return e[l] === t[l];
    }
    return !0;
}, c = {}, s = ({ appendChild: e, appendChildToContainer: t, commitTextUpdate: n, commitUpdate: r, createInstance: l, createTextInstance: i, insertBefore: a, removeChild: u, removeChildFromContainer: s, resetAfterCommit: d })=>o({
        appendChild: e,
        appendChildToContainer: t,
        appendInitialChild: e,
        createInstance: l,
        createTextInstance: i,
        insertBefore: a,
        commitUpdate: r,
        commitTextUpdate: n,
        removeChild: u,
        removeChildFromContainer: s,
        resetAfterCommit: d,
        shouldSetTextContent: ()=>!1,
        finalizeInitialChildren: ()=>!1,
        getPublicInstance: (e)=>e,
        getRootHostContext: ()=>c,
        getChildHostContext: ()=>c,
        prepareForCommit () {},
        clearContainer () {},
        resetTextContent () {},
        prepareUpdate: (e, t, n, r)=>!f(n, r)
    });
;
}}),
"[project]/node_modules/@react-pdf/reconciler/lib/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>index)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$reconciler$2f$lib$2f$reconciler$2d$31$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/reconciler/lib/reconciler-31.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$reconciler$2f$lib$2f$reconciler$2d$23$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/reconciler/lib/reconciler-23.js [app-client] (ecmascript)");
;
;
;
const isReact19 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].version.startsWith('19');
var index = isReact19 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$reconciler$2f$lib$2f$reconciler$2d$31$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$reconciler$2f$lib$2f$reconciler$2d$23$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
;
}}),
}]);

//# sourceMappingURL=node_modules_%40react-pdf_reconciler_lib_bccb4d72._.js.map