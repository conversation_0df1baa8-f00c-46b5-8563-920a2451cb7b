"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7684],{6548:(e,t,r)=>{r.d(t,{Sk:()=>n,p9:()=>o});var s=r(28755),a=r(12115),i=r(40879);let n=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{toast:n}=(0,i.dj)(),{cacheDuration:o=3e5,enableRetry:l=!0,errorMessage:c,retryAttempts:u=3,showErrorToast:d=!0,showSuccessToast:m=!1,successMessage:f,...g}=r,p=(0,s.I)({gcTime:2*o,queryFn:t,queryKey:e,retry:!!l&&u,retryDelay:e=>Math.min(1e3*2**e,3e4),staleTime:o,...g});return(0,a.useEffect)(()=>{m&&p.isSuccess&&p.data&&f&&n({description:f,title:"Success"})},[m,p.isSuccess,p.data,f,n]),(0,a.useEffect)(()=>{d&&p.isError&&n({description:c||(p.error instanceof Error?p.error.message:"An error occurred"),title:"Error",variant:"destructive"})},[d,p.isError,p.error,c,n]),{...p,forceRefresh:async()=>await p.refetch(),isStale:p.isStale||!1,lastUpdated:p.dataUpdatedAt||null}},o=function(e,t){var r,s,a;let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{keepPreviousData:o=!0,page:l=1,pageSize:c=10,...u}=i,d=n([...e,"paginated",l,c],()=>t(l,c),{...u,...o?{placeholderData:e=>e}:{}}),m=null==(r=d.data)?void 0:r.pagination;return{...d,currentPage:l,data:null!=(a=null==(s=d.data)?void 0:s.data)?a:[],goToPage:e=>{},hasNextPage:!!m&&m.hasNext,hasPrevPage:!!m&&m.hasPrevious,nextPage:()=>{m&&m.hasNext},pagination:null!=m?m:{hasNext:!1,hasPrevious:!1,limit:c,page:1,total:0,totalPages:1},prevPage:()=>{m&&m.hasPrevious},totalPages:m?m.totalPages:1}}},14056:(e,t,r)=>{r.d(t,{Sk:()=>d.Sk,XD:()=>u}),r(96016);var s=r(65453),a=r(46786);let i={"alert-statistics":3e5,alerts:1e4,"circuit-breakers":3e4,dependencies:45e3,"detailed-health":6e4,health:15e3,metrics:3e4},n={expandedWidgets:new Set(["system-health","health-status-indicators","circuit-breakers","active-alerts"]),gridColumns:3,layout:"grid",visibleWidgets:new Set(["system-health","health-status-indicators","circuit-breakers","active-alerts","alert-statistics","dependency-status","health-trends","circuit-breaker-metrics","circuit-breaker-history","deduplication-metrics","performance-metrics","performance-overview","system-metrics","http-metrics","dependency-health"]),widgetOrder:["system-health","health-status-indicators","circuit-breakers","active-alerts","alert-statistics","dependency-status","health-trends","circuit-breaker-metrics","circuit-breaker-list","circuit-breaker-history","circuit-breaker-alerts","system-resources","performance-overview","system-metrics","http-metrics","deduplication-metrics","performance-metrics","dependency-health"]},o={autoDismissTimeout:5e3,desktopEnabled:!0,minimumSeverity:"medium",soundEnabled:!0},l={searchText:"",severities:new Set(["critical","high","low","medium"]),sources:new Set,statuses:new Set(["acknowledged","active"]),timeRange:"24h"},c={"acknowledge-multiple-alerts":!1,"export-metrics":!1,"refresh-all-data":!1,"resolve-multiple-alerts":!1},u=(0,s.v)()((0,a.lt)((0,a.Zr)((e,t)=>({clearAlertFilters:()=>e(e=>({ui:{...e.ui,filters:l}})),clearAlertSelection:()=>e(e=>({ui:{...e.ui,selectedAlerts:new Set}})),getActiveFilters:()=>{let{ui:e}=t(),r=e.filters,s={};return 4!==r.severities.size&&(s.severities=r.severities),2!==r.statuses.size&&(s.statuses=r.statuses),r.sources.size>0&&(s.sources=r.sources),r.searchText.trim()&&(s.searchText=r.searchText),"24h"!==r.timeRange&&(s.timeRange=r.timeRange),s},getSelectedAlertCount:()=>{let{ui:e}=t();return e.selectedAlerts.size},getVisibleWidgets:()=>{let{preferences:e}=t();return e.dashboardLayout.widgetOrder.filter(t=>e.dashboardLayout.visibleWidgets.has(t))},isDataTypePaused:e=>{let{monitoring:r}=t();return r.pausedDataTypes.has(e)||!r.isEnabled},monitoring:{connectionStatus:"disconnected",isEnabled:!0,lastRefresh:{},pausedDataTypes:new Set},pauseAllMonitoring:()=>e(e=>({monitoring:{...e.monitoring,isEnabled:!1,pausedDataTypes:new Set(["alert-statistics","alerts","circuit-breakers","dependencies","detailed-health","health","metrics"])}})),pauseDataType:t=>e(e=>{let r=new Set(e.monitoring.pausedDataTypes);return r.add(t),{monitoring:{...e.monitoring,pausedDataTypes:r}}}),preferences:{dashboardLayout:n,defaultTimeRange:"24h",notifications:o,refreshIntervals:i},reorderWidgets:t=>e(e=>({preferences:{...e.preferences,dashboardLayout:{...e.preferences.dashboardLayout,widgetOrder:t}}})),resetPreferencesToDefaults:()=>e(e=>({preferences:{dashboardLayout:n,defaultTimeRange:"24h",notifications:o,refreshIntervals:i}})),resumeAllMonitoring:()=>e(e=>({monitoring:{...e.monitoring,isEnabled:!0,pausedDataTypes:new Set}})),resumeDataType:t=>e(e=>{let r=new Set(e.monitoring.pausedDataTypes);return r.delete(t),{monitoring:{...e.monitoring,pausedDataTypes:r}}}),selectAllAlerts:t=>e(e=>({ui:{...e.ui,selectedAlerts:new Set(t)}})),setActiveTab:t=>e(e=>({ui:{...e.ui,activeTab:t}})),setAlertFilters:t=>e(e=>({ui:{...e.ui,filters:{...e.ui.filters,...t,severities:t.severities||e.ui.filters.severities,sources:t.sources||e.ui.filters.sources,statuses:t.statuses||e.ui.filters.statuses}}})),setBatchOperationLoading:(t,r)=>e(e=>({ui:{...e.ui,batchOperations:{...e.ui.batchOperations,[t]:r}}})),setConnectionStatus:t=>e(e=>({monitoring:{...e.monitoring,connectionStatus:t}})),setDashboardLayout:t=>e(e=>({preferences:{...e.preferences,dashboardLayout:{...e.preferences.dashboardLayout,layout:t}}})),setDefaultTimeRange:t=>e(e=>({preferences:{...e.preferences,defaultTimeRange:t}})),setGridColumns:t=>e(e=>({preferences:{...e.preferences,dashboardLayout:{...e.preferences.dashboardLayout,gridColumns:Math.max(1,Math.min(6,t))}}})),setMonitoringEnabled:t=>e(e=>({monitoring:{...e.monitoring,isEnabled:t}})),setNotificationPreferences:t=>e(e=>({preferences:{...e.preferences,notifications:{...e.preferences.notifications,...t}}})),setRefreshInterval:(t,r)=>e(e=>({preferences:{...e.preferences,refreshIntervals:{...e.preferences.refreshIntervals,[t]:r}}})),setWidgetExpanded:(t,r)=>e(e=>{let s=new Set(e.preferences.dashboardLayout.expandedWidgets);return r?s.add(t):s.delete(t),{preferences:{...e.preferences,dashboardLayout:{...e.preferences.dashboardLayout,expandedWidgets:s}}}}),toggleAlertSelection:t=>e(e=>{let r=new Set(e.ui.selectedAlerts);return r.has(t)?r.delete(t):r.add(t),{ui:{...e.ui,selectedAlerts:r}}}),toggleFilterPanel:()=>e(e=>({ui:{...e.ui,isFilterPanelOpen:!e.ui.isFilterPanelOpen}})),toggleWidget:t=>e(e=>{let r=new Set(e.preferences.dashboardLayout.visibleWidgets);return r.has(t)?r.delete(t):r.add(t),{preferences:{...e.preferences,dashboardLayout:{...e.preferences.dashboardLayout,visibleWidgets:r}}}}),ui:{activeTab:"overview",batchOperations:c,filters:l,isFilterPanelOpen:!1,selectedAlerts:new Set},updateLastRefresh:(t,r)=>e(e=>({monitoring:{...e.monitoring,lastRefresh:{...e.monitoring.lastRefresh,[t]:r||new Date().toISOString()}}}))}),{merge:(e,t)=>(e.preferences.dashboardLayout.expandedWidgets=new Set(e.preferences.dashboardLayout.expandedWidgets),e.preferences.dashboardLayout.visibleWidgets=new Set(e.preferences.dashboardLayout.visibleWidgets),{...t,...e}),name:"workhub-reliability-store",partialize:e=>({preferences:{dashboardLayout:{expandedWidgets:[...e.preferences.dashboardLayout.expandedWidgets],gridColumns:e.preferences.dashboardLayout.gridColumns,layout:e.preferences.dashboardLayout.layout,visibleWidgets:[...e.preferences.dashboardLayout.visibleWidgets],widgetOrder:e.preferences.dashboardLayout.widgetOrder},defaultTimeRange:e.preferences.defaultTimeRange,notifications:e.preferences.notifications,refreshIntervals:e.preferences.refreshIntervals}})}),{name:"reliability-store"}));r(26119);var d=r(43772);r(30043),r(42366),r(62590),r(85187),r(36846),r(39097),r(86719),r(94141),r(20317),r(53712),r(12115),r(71153);var m=r(52747),f=r(8264),g=r(21991),p=r(53953),h=r(20249);f.b,m.Sk,g.n,p.o,h.C,r(69738),r(97697),r(40879);class b{clear(){this.pendingRequests.clear()}async deduplicate(e,t){if(this.pendingRequests.has(e))return console.log("\uD83D\uDD04 Global Request DEDUPLICATED for ".concat(e)),this.pendingRequests.get(e);let r=t().finally(()=>{this.pendingRequests.delete(e)});return this.pendingRequests.set(e,r),console.log("\uD83D\uDD04 Global Request STARTED for ".concat(e)),r}getPendingCount(){return this.pendingRequests.size}getPendingKeys(){return[...this.pendingRequests.keys()]}constructor(){this.pendingRequests=new Map}}new b},17313:(e,t,r)=>{r.d(t,{Xi:()=>c,av:()=>u,j7:()=>l,tU:()=>o});var s=r(95155),a=r(60704),i=r(12115),n=r(54036);let o=a.bL,l=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(a.B8,{className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),ref:t,...i})});l.displayName=a.B8.displayName;let c=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(a.l9,{className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),ref:t,...i})});c.displayName=a.l9.displayName;let u=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(a.UC,{className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),ref:t,...i})});u.displayName=a.UC.displayName},20317:(e,t,r)=>{r.d(t,{P:()=>a});var s=r(83940);class a{static showSuccessToast(e,t,r){if(!e.showSuccessToast)return;let{entityType:a,entity:i,successMessage:n}=e;try{switch(a){case"employee":i?s.Ok.entityCreated(i):s.JP.success("Employee Created",n);break;case"vehicle":i?s.G7.entityCreated(i):s.JP.success("Vehicle Added",n);break;case"task":i?s.z0.entityCreated(i):s.JP.success("Task Created",n);break;case"delegation":i?s.Qu.entityCreated(i):s.JP.success("Delegation Created",n);break;case"serviceRecord":i&&r?s.oz.serviceRecordCreated(i.vehicleName||"Vehicle",i.serviceType||"Service"):s.JP.success("Service Record Added",n);break;default:s.JP.success("Success",n||"Operation completed successfully")}}catch(e){s.JP.success("Success",n||"Operation completed successfully")}}static showErrorToast(e,t,r){if(!e.showErrorToast)return;let{entityType:a,errorMessage:i}=e,n=t.message||i||"An unexpected error occurred";try{switch(a){case"employee":s.Ok.entityCreationError(n);break;case"vehicle":s.G7.entityCreationError(n);break;case"task":s.z0.entityCreationError(n);break;case"delegation":s.Qu.entityCreationError(n);break;case"serviceRecord":s.oz.serviceRecordCreationError(n);break;default:s.JP.error("Error",n)}}catch(e){s.JP.error("Error",n)}}static showUpdateSuccessToast(e,t,r){if(!e.showSuccessToast)return;let{entityType:a,entity:i,successMessage:n}=e;try{switch(a){case"employee":i?s.Ok.entityUpdated(i):s.JP.success("Employee Updated",n);break;case"vehicle":i?s.G7.entityUpdated(i):s.JP.success("Vehicle Updated",n);break;case"task":i?s.z0.entityUpdated(i):s.JP.success("Task Updated",n);break;case"delegation":i?s.Qu.entityUpdated(i):s.JP.success("Delegation Updated",n);break;case"serviceRecord":i&&r?s.oz.serviceRecordUpdated(i.vehicleName||"Vehicle",i.serviceType||"Service"):s.JP.success("Service Record Updated",n);break;default:s.JP.success("Success",n||"Update completed successfully")}}catch(e){s.JP.success("Success",n||"Update completed successfully")}}static showUpdateErrorToast(e,t,r){if(!e.showErrorToast)return;let{entityType:a,errorMessage:i}=e,n=t.message||i||"An unexpected error occurred";try{switch(a){case"employee":s.Ok.entityUpdateError(n);break;case"vehicle":s.G7.entityUpdateError(n);break;case"task":s.z0.entityUpdateError(n);break;case"delegation":s.Qu.entityUpdateError(n);break;case"serviceRecord":s.oz.serviceRecordUpdateError(n);break;default:s.JP.error("Update Failed",n)}}catch(e){s.JP.error("Update Failed",n)}}static createCustomEntityToastService(e,t){return(0,s.Gb)(e,t)}}},22346:(e,t,r)=>{r.d(t,{w:()=>o});var s=r(95155),a=r(87489),i=r(12115),n=r(54036);let o=i.forwardRef((e,t)=>{let{className:r,decorative:i=!0,orientation:o="horizontal",...l}=e;return(0,s.jsx)(a.b,{className:(0,n.cn)("shrink-0 bg-border","horizontal"===o?"h-[1px] w-full":"h-full w-[1px]",r),decorative:i,orientation:o,ref:t,...l})});o.displayName=a.b.displayName},24944:(e,t,r)=>{r.d(t,{k:()=>o});var s=r(95155),a=r(55863),i=r(12115),n=r(54036);let o=i.forwardRef((e,t)=>{let{className:r,value:i,...o}=e;return(0,s.jsx)(a.bL,{className:(0,n.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",r),ref:t,...o,children:(0,s.jsx)(a.C1,{className:"size-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(i||0),"%)")}})})});o.displayName=a.bL.displayName},26119:(e,t,r)=>{r.d(t,{n:()=>i});var s=r(65453),a=r(46786);let i=(0,s.v)()((0,a.lt)((0,a.Zr)(e=>({autoRefreshInterval:30,closeModal:()=>e({isModalOpen:!1,modalContent:null}),dashboardLayout:"cards",fontSize:"medium",isModalOpen:!1,mapViewPreference:"roadmap",modalContent:null,notificationsEnabled:!0,openModal:t=>e({isModalOpen:!0,modalContent:t}),setAutoRefreshInterval:t=>e({autoRefreshInterval:t}),setDashboardLayout:t=>e({dashboardLayout:t}),setFontSize:t=>e({fontSize:t}),setMapViewPreference:t=>e({mapViewPreference:t}),setTableDensity:t=>e({tableDensity:t}),tableDensity:"comfortable",toggleNotifications:()=>e(e=>({notificationsEnabled:!e.notificationsEnabled}))}),{name:"workhub-ui-store",partialize:e=>({autoRefreshInterval:e.autoRefreshInterval,dashboardLayout:e.dashboardLayout,fontSize:e.fontSize,mapViewPreference:e.mapViewPreference,notificationsEnabled:e.notificationsEnabled,tableDensity:e.tableDensity})}),{name:"ui-store"}))},30043:(e,t,r)=>{r.r(t),r.d(t,{useModal:()=>i});var s=r(12115),a=r(26119);let i=()=>{let e=(0,a.n)(e=>e.isModalOpen),t=(0,a.n)(e=>e.modalContent),r=(0,a.n)(e=>e.openModal),i=(0,a.n)(e=>e.closeModal),n=(0,s.useCallback)(()=>{r("login")},[r]),o=(0,s.useCallback)(()=>{r("signup")},[r]),l=(0,s.useCallback)(()=>{r("settings")},[r]),c=(0,s.useCallback)(()=>{r("delegation-form")},[r]),u=(0,s.useCallback)(()=>{r("vehicle-details")},[r]),d=(0,s.useCallback)(()=>{r("task-assignment")},[r]),m=(0,s.useCallback)(()=>{r("employee-profile")},[r]),f=(0,s.useCallback)(r=>e&&t===r,[e,t]),g=(0,s.useCallback)(()=>({backdrop:"modal-backdrop",container:e?"modal-container-visible":"modal-container-hidden",content:"modal-content modal-content-".concat(t||"default"),overlay:e?"modal-overlay-visible":"modal-overlay-hidden"}),[e,t]),p=(0,s.useCallback)(()=>({"aria-describedby":t?"".concat(t,"-modal-description"):void 0,"aria-hidden":!e,"aria-labelledby":t?"".concat(t,"-modal-title"):void 0,"aria-modal":e,role:"dialog"}),[e,t]),h=(0,s.useCallback)(t=>{"Escape"===t.key&&e&&i()},[e,i]),b=(0,s.useCallback)(t=>{t.target===t.currentTarget&&e&&i()},[e,i]);return{closeModal:i,getModalAriaAttributes:p,getModalClasses:g,getModalTitle:(0,s.useCallback)(()=>{switch(t){case"delegation-form":return"Create Delegation";case"employee-profile":return"Employee Profile";case"login":return"Sign In";case"settings":return"Settings";case"signup":return"Create Account";case"task-assignment":return"Assign Task";case"vehicle-details":return"Vehicle Details";default:return"Modal"}},[t]),handleBackdropClick:b,handleEscapeKey:h,isModalOfTypeOpen:f,isModalOpen:e,isWorkHubModal:(0,s.useCallback)(()=>["delegation-form","employee-profile","task-assignment","vehicle-details"].includes(t||""),[t])(),modalContent:t,openDelegationFormModal:c,openEmployeeProfileModal:m,openLoginModal:n,openModal:r,openSettingsModal:l,openSignupModal:o,openTaskAssignmentModal:d,openVehicleDetailsModal:u}}},36846:(e,t,r)=>{r.r(t),r.d(t,{useUiPreferences:()=>i});var s=r(12115),a=r(26119);let i=()=>{let e=(0,a.n)(e=>e.fontSize),t=(0,a.n)(e=>e.setFontSize),r=(0,a.n)(e=>e.notificationsEnabled),i=(0,a.n)(e=>e.toggleNotifications),n=(0,a.n)(e=>e.tableDensity),o=(0,a.n)(e=>e.setTableDensity),l=(0,a.n)(e=>e.mapViewPreference),c=(0,a.n)(e=>e.setMapViewPreference),u=(0,a.n)(e=>e.dashboardLayout),d=(0,a.n)(e=>e.setDashboardLayout),m=(0,a.n)(e=>e.autoRefreshInterval),f=(0,a.n)(e=>e.setAutoRefreshInterval),g=(0,s.useCallback)(()=>{switch(e){case"large":return"text-lg";case"small":return"text-sm";default:return"text-base"}},[e]),p=(0,s.useCallback)(()=>{switch(n){case"compact":return{cell:"py-1 px-2",row:"h-8",table:"table-compact"};case"spacious":return{cell:"py-4 px-4",row:"h-16",table:"table-spacious"};default:return{cell:"py-2 px-3",row:"h-12",table:"table-comfortable"}}},[n]),h=(0,s.useCallback)(()=>{switch(u){case"cards":default:return"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6";case"grid":return"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4";case"list":return"flex flex-col space-y-4"}},[u]),b=(0,s.useCallback)(()=>{r||i()},[r,i]),y=(0,s.useCallback)(()=>{r&&i()},[r,i]),v=(0,s.useCallback)(()=>{t("medium"),o("comfortable"),c("roadmap"),d("cards"),f(30)},[t,o,c,d,f]),w=(0,s.useCallback)(()=>({autoRefreshInterval:m,dashboardLayout:u,fontSize:e,mapViewPreference:l,notificationsEnabled:r,tableDensity:n}),[e,r,n,l,u,m]);return{autoRefreshInterval:m,dashboardLayout:u,disableNotifications:y,enableNotifications:b,fontSize:e,getAllPreferences:w,getDashboardLayoutClasses:h,getFontSizeClass:g,getTableDensityClasses:p,mapViewPreference:l,notificationsEnabled:r,resetPreferences:v,setAutoRefreshInterval:f,setDashboardLayout:d,setFontSize:t,setMapViewPreference:c,setTableDensity:o,tableDensity:n,toggleNotifications:i}}},39097:(e,t,r)=>{r.d(t,{k:()=>c});var s=r(12115),a=r(94141),i=r(20317);class n{announceStatus(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"polite";if(!this.config.announceStatus||!this.config.screenReaderAnnouncements)return;let r=document.getElementById("form-submission-announcements");r||((r=document.createElement("div")).id="form-submission-announcements",r.setAttribute("aria-live",t),r.setAttribute("aria-atomic","true"),r.className="sr-only absolute left-[-10000px] top-[-10000px] w-[1px] h-[1px] overflow-hidden",document.body.appendChild(r)),r.textContent=e,setTimeout(()=>{r&&r.textContent===e&&(r.textContent="")},1e3)}generateAriaAttributes(e,t,r){return{"aria-busy":e,"aria-invalid":t,"aria-describedby":this.config.errorDescribedBy||(t?"form-error":void 0),"aria-live":"submitting"===r||"validating"===r?"polite":"off"}}manageFocus(e,t){if("none"!==this.config.focusManagement)switch(e){case"error":"first-error"===this.config.focusManagement&&t&&t("first-error");break;case"success":if("success-message"===this.config.focusManagement){let e=document.getElementById("form-success-message");e&&e.focus()}else"next-field"===this.config.focusManagement&&t&&t("next-field");break;case"retry":t&&t("retry-button")}}createErrorMessage(e){let t=document.createElement("div");return t.id=this.config.errorDescribedBy||"form-error",t.setAttribute("role","alert"),t.setAttribute("aria-live","assertive"),t.className="sr-only",t.textContent=e,t}updateErrorMessage(e){let t=this.config.errorDescribedBy||"form-error",r=document.getElementById(t);e?r?r.textContent=e:(r=this.createErrorMessage(e),document.body.appendChild(r)):r&&r.remove()}getStatusMessage(e,t,r){switch(e){case"validating":return"Validating form data...";case"submitting":return"Submitting form...";case"retrying":return"Retrying submission... (Attempt ".concat(t||1,"/").concat(r||3,")");case"success":return"Form submitted successfully";case"error":return"Form submission failed";default:return""}}setupKeyboardNavigation(){let e=e=>{if("Escape"===e.key){let e=document.querySelector("[data-form-cancel]");e&&e.click()}if((e.ctrlKey||e.metaKey)&&"Enter"===e.key){let e=document.querySelector('[type="submit"]');e&&!e.disabled&&e.click()}};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}announceProgress(e,t,r){if(!this.config.screenReaderAnnouncements)return;let s="Step ".concat(e," of ").concat(t,": ").concat(r);this.announceStatus(s,"polite")}cleanup(){let e=document.getElementById("form-submission-announcements");e&&e.remove(),this.updateErrorMessage(null)}constructor(e){this.config=e}}class o{shouldRetry(e){return this.currentAttempt<this.config.maxAttempts&&(!this.config.retryCondition||this.config.retryCondition(e))}getRetryDelay(){let e=this.config.delay;return this.config.exponentialBackoff?e*Math.pow(2,this.currentAttempt):e}incrementAttempt(){return this.currentAttempt+=1,this.currentAttempt}resetAttempts(){this.currentAttempt=0}getCurrentAttempt(){return this.currentAttempt}getMaxAttempts(){return this.config.maxAttempts}async sleep(e){return new Promise(t=>setTimeout(t,e))}async executeRetry(e){if(!this.shouldRetry(Error("Manual retry")))throw Error("Maximum retry attempts exceeded");let t=this.getRetryDelay();return this.incrementAttempt(),await this.sleep(t),e()}getRetryStatus(){return{currentAttempt:this.currentAttempt,maxAttempts:this.config.maxAttempts,hasRetriesLeft:this.currentAttempt<this.config.maxAttempts,nextDelay:this.getRetryDelay()}}withConfig(e){return new o({...this.config,...e})}constructor(e){this.currentAttempt=0,this.config=e}}class l{startTiming(){this.submissionStartTime=Date.now()}endTiming(e){if(!this.submissionStartTime)return 0;let t=Date.now()-this.submissionStartTime;return this.updateMetrics(e,t),this.submissionStartTime=null,t}updateMetrics(e,t){let r=this.metrics.totalSubmissions+1,s=e?this.metrics.successfulSubmissions+1:this.metrics.successfulSubmissions,a=e?this.metrics.failedSubmissions:this.metrics.failedSubmissions+1,i=this.metrics.averageDuration*this.metrics.totalSubmissions+t;this.metrics={totalSubmissions:r,successfulSubmissions:s,failedSubmissions:a,averageDuration:i/r}}getMetrics(){return{...this.metrics}}resetMetrics(){this.metrics={totalSubmissions:0,successfulSubmissions:0,failedSubmissions:0,averageDuration:0}}debounce(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.config.debounceMs;var r=this;return function(){for(var s=arguments.length,a=Array(s),i=0;i<s;i++)a[i]=arguments[i];r.debounceTimer&&clearTimeout(r.debounceTimer),r.debounceTimer=setTimeout(()=>{e(...a)},t)}}clearDebounce(){this.debounceTimer&&(clearTimeout(this.debounceTimer),this.debounceTimer=null)}createTimeoutPromise(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.config.timeoutMs;return new Promise((t,r)=>{setTimeout(()=>{r(Error("Request timeout after ".concat(e,"ms")))},e)})}async withTimeout(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.config.timeoutMs;return Promise.race([e,this.createTimeoutPromise(t)])}getSuccessRate(){return 0===this.metrics.totalSubmissions?0:this.metrics.successfulSubmissions/this.metrics.totalSubmissions*100}getFailureRate(){return 0===this.metrics.totalSubmissions?0:this.metrics.failedSubmissions/this.metrics.totalSubmissions*100}isPerformanceAcceptable(e){let t={maxAverageDuration:5e3,minSuccessRate:95,...e};return this.metrics.averageDuration<=t.maxAverageDuration&&this.getSuccessRate()>=t.minSuccessRate}generateReport(){let e=this.getSuccessRate(),t=this.getFailureRate(),r=this.isPerformanceAcceptable(),s=[];return this.metrics.averageDuration>3e3&&s.push("Consider optimizing form validation or submission logic"),e<90&&s.push("High failure rate detected - review error handling"),this.metrics.totalSubmissions>100&&this.metrics.averageDuration>1e3&&s.push("Consider implementing caching for better performance"),{metrics:this.getMetrics(),successRate:e,failureRate:t,isAcceptable:r,recommendations:s}}cleanup(){this.clearDebounce(),this.submissionStartTime=null}constructor(e){this.submissionStartTime=null,this.debounceTimer=null,this.config=e,this.metrics={totalSubmissions:0,successfulSubmissions:0,failedSubmissions:0,averageDuration:0}}}let c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=a.SY.mergeRetryConfig(t.retry),c=a.SY.mergeAccessibilityConfig(t.accessibility),u=a.SY.mergePerformanceConfig(t.performance),d=a.SY.mergeToastConfig(t.toast),m=(0,s.useRef)(new n(c)).current,f=(0,s.useRef)(new o(r)).current,g=(0,s.useRef)(new l(u)).current,[p,h]=(0,s.useState)("idle"),[b,y]=(0,s.useState)(null),[v,w]=(0,s.useState)(null),[S,k]=(0,s.useState)(null),[C,x]=(0,s.useState)(null),[T,A]=(0,s.useState)(null),[D,R]=(0,s.useState)(null),E=(0,s.useRef)(null),N="submitting"===p||"validating"===p,M="success"===p,P="validating"===p,L="retrying"===p,F=f.getCurrentAttempt();(0,s.useEffect)(()=>()=>{E.current&&E.current.abort(),g.cleanup(),m.cleanup()},[g,m]);let I=(0,s.useCallback)(()=>{y(null),w(null),m.updateErrorMessage(null),"error"===p&&h("idle")},[p,m]),j=(0,s.useCallback)(()=>{h("idle"),y(null),w(null),k(null),x(null),A(null),R(null),f.resetAttempts(),g.resetMetrics(),m.updateErrorMessage(null)},[f,g,m]),O=(0,s.useCallback)(()=>{E.current&&E.current.abort(),h("idle"),m.announceStatus("Form submission cancelled")},[m]),z=(0,s.useCallback)(async function(s){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{g.startTiming(),E.current=new AbortController;let n=a?"retrying":"submitting";h(n);let o=m.getStatusMessage(n,F,r.maxAttempts);if(m.announceStatus(o),t.onSubmitStart&&await t.onSubmitStart(s),t.preSubmitValidation&&(h("validating"),m.announceStatus("Validating form data..."),!await t.preSubmitValidation(s)))throw Error("Validation failed");h(n);let l=s;t.transformData&&(l=await t.transformData(s));let c=e(l),u=await g.withTimeout(c),p=u;if(t.transformResult&&(p=await t.transformResult(u)),t.postSubmitValidation&&!await t.postSubmitValidation(p))throw Error("Post-submission validation failed");let b=g.endTiming(!0);h("success"),A(p),x(Date.now()),k(s),R(b),f.resetAttempts(),i.P.showSuccessToast(d,s,p),m.announceStatus("Form submitted successfully","assertive"),m.manageFocus("success",t.formFocus),t.resetOnSuccess&&t.formReset&&t.formReset(),t.onSuccess&&await t.onSuccess(s,p),t.onSubmitComplete&&await t.onSubmitComplete(s,!0)}catch(l){let e=l instanceof Error?l:Error(String(l)),n=g.endTiming(!1);if(!a&&f.shouldRetry(e)){h("retrying");let e=f.getRetryDelay();return f.incrementAttempt(),m.announceStatus("Retrying in ".concat(e,"ms... (Attempt ").concat(f.getCurrentAttempt(),"/").concat(r.maxAttempts,")")),await f.sleep(e),z(s,!0)}h("error");let o=e.message||d.errorMessage||"An unexpected error occurred";y(o),w(e),R(n),i.P.showErrorToast(d,e,s),m.updateErrorMessage(o),m.announceStatus("Error: ".concat(o),"assertive"),m.manageFocus("error",t.formFocus),t.onError&&await t.onError(e,s),t.onSubmitComplete&&await t.onSubmitComplete(s,!1)}},[e,t,f,g,m,d,r.maxAttempts,F]),q=(0,s.useCallback)(async(e,t)=>{t&&t.preventDefault(),g.debounce(()=>z(e),u.debounceMs)()},[z,g,u.debounceMs]),U=(0,s.useCallback)(async()=>{S&&(f.resetAttempts(),await z(S))},[S,z,f]),V=m.generateAriaAttributes(N,!!b,p);return{isLoading:N,state:p,error:b,errorObject:v,isSuccess:M,isValidating:P,isRetrying:L,lastSubmittedData:S,lastSubmitted:C,lastResult:T,retryAttempt:F,handleSubmit:q,clearError:I,reset:j,retry:U,cancel:O,ariaAttributes:V,submissionDuration:D,metrics:g.getMetrics()}}},42366:(e,t,r)=>{r.r(t),r.d(t,{useNotifications:()=>i,useWorkHubNotifications:()=>n});var s=r(12115),a=r(96016);let i=()=>{let e=(0,a.C)(e=>e.addNotification),t=(0,a.C)(e=>e.removeNotification),r=(0,a.C)(e=>e.clearAllNotifications),i=(0,a.C)(e=>e.unreadNotificationCount),n=(0,s.useCallback)(t=>{e({message:t,type:"success"})},[e]),o=(0,s.useCallback)(t=>{e({message:t,type:"error"})},[e]),l=(0,s.useCallback)(t=>{e({message:t,type:"warning"})},[e]),c=(0,s.useCallback)(t=>{e({message:t,type:"info"})},[e]),u=(0,s.useCallback)((e,t,r)=>{e?n(t):o(r)},[n,o]),d=(0,s.useCallback)(function(r,s){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;e({message:s,type:r}),setTimeout(()=>{let e=a.C.getState().notifications.at(-1);e&&e.message===s&&t(e.id)},i)},[e,t]),m=(0,s.useCallback)(function(){var t;let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";return e({message:r,type:"info"}),null==(t=a.C.getState().notifications.at(-1))?void 0:t.id},[e]),f=(0,s.useCallback)((e,r,s)=>{t(e),r?n(s):o(s)},[t,n,o]);return{clearAllNotifications:r,removeNotification:t,showApiResult:u,showError:o,showInfo:c,showLoading:m,showSuccess:n,showTemporary:d,showWarning:l,unreadCount:i,updateLoadingNotification:f}},n=()=>{let{clearAllNotifications:e,removeNotification:t,showError:r,showInfo:n,showSuccess:o,showWarning:l,unreadCount:c}=i(),u=(0,s.useCallback)((e,t)=>{(0,a.C.getState().addNotification)({...t&&{actionUrl:t},category:"delegation",message:e,type:"delegation-update"})},[]),d=(0,s.useCallback)((e,t)=>{(0,a.C.getState().addNotification)({...t&&{actionUrl:t},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),m=(0,s.useCallback)((e,t)=>{(0,a.C.getState().addNotification)({...t&&{actionUrl:t},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:t,showDelegationUpdate:u,showEmployeeUpdate:(0,s.useCallback)((e,t)=>{(0,a.C.getState().addNotification)({...t&&{actionUrl:t},category:"employee",message:e,type:"employee-update"})},[]),showError:r,showInfo:n,showSuccess:o,showTaskAssigned:m,showVehicleMaintenance:d,showWarning:l,unreadCount:c}}},43772:(e,t,r)=>{r.d(t,{Sk:()=>s.Sk});var s=r(6548);r(40879),r(35695),r(12115),r(75908),r(70647),r(40283),r(90111)},47262:(e,t,r)=>{r.d(t,{S:()=>l});var s=r(95155),a=r(76981),i=r(10518),n=r(12115),o=r(54036);let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)(a.bL,{className:(0,o.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",r),ref:t,...n,children:(0,s.jsx)(a.C1,{className:(0,o.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(i.A,{className:"size-4"})})})});l.displayName=a.bL.displayName},54165:(e,t,r)=>{r.d(t,{Cf:()=>m,Es:()=>g,L3:()=>p,c7:()=>f,lG:()=>l,rr:()=>h,zM:()=>c});var s=r(95155),a=r(15452),i=r(25318),n=r(12115),o=r(54036);let l=a.bL,c=a.l9,u=a.ZL;a.bm;let d=n.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(a.hJ,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),ref:t,...i})});d.displayName=a.hJ.displayName;let m=n.forwardRef((e,t)=>{let{children:r,className:n,...l}=e;return(0,s.jsxs)(u,{children:[(0,s.jsx)(d,{}),(0,s.jsxs)(a.UC,{className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",n),ref:t,...l,children:[r,(0,s.jsxs)(a.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(i.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=a.UC.displayName;let f=e=>{let{className:t,...r}=e;return(0,s.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...r})};f.displayName="DialogHeader";let g=e=>{let{className:t,...r}=e;return(0,s.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...r})};g.displayName="DialogFooter";let p=n.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(a.hE,{className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",r),ref:t,...i})});p.displayName=a.hE.displayName;let h=n.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(a.VY,{className:(0,o.cn)("text-sm text-muted-foreground",r),ref:t,...i})});h.displayName=a.VY.displayName},62590:(e,t,r)=>{r.r(t),r.d(t,{useSidebar:()=>i});var s=r(12115),a=r(96016);let i=()=>{let e=(0,a.C)(e=>e.sidebarOpen),t=(0,a.C)(e=>e.toggleSidebar),r=(0,s.useCallback)(()=>{e||t()},[e,t]),i=(0,s.useCallback)(()=>{e&&t()},[e,t]),n=(0,s.useCallback)(()=>({content:e?"content-shifted":"content-normal",overlay:e?"overlay-visible":"overlay-hidden",sidebar:e?"sidebar-open":"sidebar-closed",toggle:e?"toggle-close":"toggle-open"}),[e]);return{closeSidebar:i,getAriaAttributes:(0,s.useCallback)(()=>({"aria-expanded":e,"aria-label":e?"Close sidebar":"Open sidebar"}),[e]),getSidebarClasses:n,isClosed:!e,isOpen:e,openSidebar:r,sidebarOpen:e,toggleSidebar:t}}},66695:(e,t,r)=>{r.d(t,{BT:()=>c,Wu:()=>u,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>d});var s=r(95155),a=r(12115),i=r(54036);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),ref:t,...a})});n.displayName="Card";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),ref:t,...a})});o.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",r),ref:t,...a})});l.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("text-sm text-muted-foreground",r),ref:t,...a})});c.displayName="CardDescription";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("p-6 pt-0",r),ref:t,...a})});u.displayName="CardContent";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex items-center p-6 pt-0",r),ref:t,...a})});d.displayName="CardFooter"},69738:(e,t,r)=>{r.d(t,{y:()=>a});var s=r(12115);let a=e=>(0,s.useMemo)(()=>{var t,r,s;let a=e.escorts&&e.escorts.length>0&&(null==(t=e.escorts[0])?void 0:t.employee)?e.escorts[0].employee:null,i=e.drivers&&e.drivers.length>0&&(null==(r=e.drivers[0])?void 0:r.employee)?e.drivers[0].employee:null,n=e.vehicles&&e.vehicles.length>0&&(null==(s=e.vehicles[0])?void 0:s.vehicle)?e.vehicles[0].vehicle:null,o=!!(e.arrivalFlight||e.departureFlight),l=!a&&"Completed"!==e.status&&"Cancelled"!==e.status;return{escortInfo:a,driverInfo:i,vehicleInfo:n,hasFlightDetails:o,needsEscortAssignment:l,isActive:"In_Progress"===e.status}},[e])},70647:(e,t,r)=>{r.d(t,{DC:()=>c,qQ:()=>l});var s=r(27461),a=r(2602),i=r(55513),n=r(3695);let o=[],l=new s.E({defaultOptions:{mutations:{retry:0,retryDelay:1e3},queries:{gcTime:6e5,refetchInterval:!1,refetchOnMount:!0,refetchOnReconnect:!0,refetchOnWindowFocus:!0,retry:(e,t)=>!(t instanceof n.v3)&&!(t instanceof n.Dr)&&(!(t instanceof n.hD)||400!==t.status&&401!==t.status&&404!==t.status)&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),staleTime:3e5}},mutationCache:new a.q({onError:e=>{e instanceof n.v3?console.error("Mutation Authentication Error:",e.message):e instanceof n.Dr?console.error("Mutation Network Error:",e.message):e instanceof n.hD?console.error("Mutation API Error (".concat(e.status,"):"),e.message,e.details):console.error("An unexpected mutation error occurred:",e)}}),queryCache:new i.$({onError:e=>{e instanceof n.v3?console.error("Authentication Error:",e.message):e instanceof n.Dr?console.error("Network Error:",e.message):e instanceof n.hD?console.error("API Error (".concat(e.status,"):"),e.message,e.details):console.error("An unexpected error occurred:",e)},onSuccess:(e,t)=>{let r=JSON.stringify(t.queryKey),s=Date.now()-(t.state.dataUpdatedAt||Date.now()),a="fetching"!==t.state.fetchStatus&&void 0!==t.state.data;o.push({cacheHit:a,duration:s,queryKey:r,timestamp:Date.now()}),o.length>1e3&&o.splice(0,o.length-1e3)}})}),c={prefetchDashboardData:async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!e)return void console.warn("Authentication not ready, deferring dashboard data prefetch.");let{getGlobalAuthTokenProvider:t}=await Promise.resolve().then(r.bind(r,72248)),s=t();if(!s||!s())return void console.warn("No auth token available, skipping dashboard data prefetch.");let{delegationApiService:a,employeeApiService:i,taskApiService:n,vehicleApiService:o}=await Promise.resolve().then(r.bind(r,75908)),c=[l.prefetchQuery({queryFn:()=>o.getAll(),queryKey:["vehicles"],staleTime:6e5}),l.prefetchQuery({queryFn:()=>n.getAll(),queryKey:["tasks"],staleTime:3e5}),l.prefetchQuery({queryFn:()=>i.getAll(),queryKey:["employees"],staleTime:6e5}),l.prefetchQuery({queryFn:()=>a.getAll(),queryKey:["delegations"],staleTime:3e5})];await Promise.allSettled(c)},prefetchTaskManagementData:async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(!e)return void console.warn("Authentication not ready, deferring task management data prefetch.");let{employeeApiService:t,taskApiService:s,vehicleApiService:a}=await Promise.resolve().then(r.bind(r,75908)),i=[l.prefetchQuery({queryFn:()=>s.getAll(),queryKey:["tasks"],staleTime:3e5}),l.prefetchQuery({queryFn:()=>t.getAll(),queryKey:["employees"],staleTime:6e5}),l.prefetchQuery({queryFn:()=>a.getAll(),queryKey:["vehicles"],staleTime:6e5})];await Promise.allSettled(i)},prefetchVehicleDetails:async function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(!t)return void console.warn("Authentication not ready, deferring vehicle details prefetch.");let{vehicleApiService:s}=await Promise.resolve().then(r.bind(r,75908));await l.prefetchQuery({queryFn:()=>s.getById(e),queryKey:["vehicles",e],staleTime:6e5})}}},85187:(e,t,r)=>{r.r(t),r.d(t,{useTheme:()=>i});var s=r(12115),a=r(96016);let i=()=>{let e=(0,a.C)(e=>e.currentTheme),t=(0,a.C)(e=>e.setTheme),r=(0,s.useCallback)(()=>{t("light"===e?"dark":"light")},[e,t]),i="dark"===e,n="light"===e,o=(0,s.useCallback)(()=>{t("light")},[t]),l=(0,s.useCallback)(()=>{t("dark")},[t]),c=(0,s.useCallback)(()=>({background:i?"bg-gray-900":"bg-white",border:i?"border-gray-700":"border-gray-200",isDark:i,isLight:n,root:e,text:i?"text-white":"text-gray-900"}),[e,i,n]);return{currentTheme:e,getThemeClasses:c,isDark:i,isLight:n,setDarkTheme:l,setLightTheme:o,setTheme:t,toggleTheme:r}}},88240:(e,t,r)=>{r.d(t,{A:()=>u});var s=r(95155),a=r(31949),i=r(67554),n=r(12115),o=r(55365),l=r(30285);class c extends n.Component{static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,t){this.setState({errorInfo:t}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",t.componentStack),this.props.onError&&this.props.onError(e,t)}render(){let{description:e="An unexpected error occurred.",resetLabel:t="Try Again",title:r="Something went wrong"}=this.props;if(this.state.hasError){var n;return this.props.fallback?this.props.fallback:(0,s.jsxs)(o.Fc,{className:"my-4",variant:"destructive",children:[(0,s.jsx)(a.A,{className:"mr-2 size-4"}),(0,s.jsx)(o.XL,{className:"text-lg font-semibold",children:r}),(0,s.jsxs)(o.TN,{className:"mt-2",children:[(0,s.jsx)("p",{className:"mb-2",children:(null==(n=this.state.error)?void 0:n.message)||e}),!1,(0,s.jsxs)(l.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,s.jsx)(i.A,{className:"mr-2 size-4"}),t]})]})]})}return this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}}let u=c},94141:(e,t,r)=>{r.d(t,{SY:()=>o});let s={maxAttempts:3,delay:1e3,exponentialBackoff:!0,retryCondition:e=>e.message.includes("network")||e.message.includes("timeout")||e.message.includes("502")||e.message.includes("503")||e.message.includes("504")},a={announceStatus:!0,focusManagement:"first-error",screenReaderAnnouncements:!0},i={debounceMs:300,enableDeduplication:!0,cacheResults:!1,timeoutMs:3e4},n={showSuccessToast:!0,showErrorToast:!0,successMessage:"Operation completed successfully",errorMessage:"An unexpected error occurred",entityType:"generic"};class o{static mergeRetryConfig(e){return{...s,...e}}static mergeAccessibilityConfig(e){return{...a,...e}}static mergePerformanceConfig(e){return{...i,...e}}static mergeToastConfig(e){return{...n,...e}}}},96016:(e,t,r)=>{r.d(t,{C:()=>i});var s=r(65453),a=r(46786);let i=(0,s.v)()((0,a.lt)((0,a.Zr)((e,t)=>({addNotification:t=>e(e=>({notifications:[...e.notifications,{...t,id:crypto.randomUUID(),read:!1,timestamp:new Date().toISOString()}]})),clearAllNotifications:()=>e({notifications:[]}),currentTheme:"light",markNotificationAsRead:t=>e(e=>({notifications:e.notifications.map(e=>e.id===t?{...e,read:!0}:e)})),notifications:[],removeNotification:t=>e(e=>({notifications:e.notifications.filter(e=>e.id!==t)})),setTheme:t=>{e({currentTheme:t})},sidebarOpen:!1,toggleSidebar:()=>e(e=>({sidebarOpen:!e.sidebarOpen})),unreadNotificationCount:()=>{let{notifications:e}=t();return e.filter(e=>!e.read).length}}),{name:"workhub-app-store",partialize:e=>({currentTheme:e.currentTheme})}),{name:"app-store"}))},97697:(e,t,r)=>{r.d(t,{fX:()=>o});var s=r(12115),a=r(65453),i=r(46786);let n=new Map;function o(e){return(0,s.useMemo)(()=>(n.has(e)||n.set(e,(0,a.v)()((0,i.lt)((0,i.Zr)((e,t)=>({activeTab:"all",layout:{viewMode:"cards",gridColumns:3,compactMode:!1,showFilters:!0,showSettings:!1},monitoring:{enabled:!1,refreshInterval:3e4,autoRefresh:!0,pausedDataTypes:new Set},filters:{},sortBy:"createdAt",sortDirection:"desc",selectedItems:new Set,searchTerm:"",setActiveTab:t=>e({activeTab:t},!1,"setActiveTab"),setViewMode:t=>e(e=>({layout:{...e.layout,viewMode:t}}),!1,"setViewMode"),setGridColumns:t=>e(e=>({layout:{...e.layout,gridColumns:t}}),!1,"setGridColumns"),toggleCompactMode:()=>e(e=>({layout:{...e.layout,compactMode:!e.layout.compactMode}}),!1,"toggleCompactMode"),toggleFilters:()=>e(e=>({layout:{...e.layout,showFilters:!e.layout.showFilters}}),!1,"toggleFilters"),toggleSettings:()=>e(e=>({layout:{...e.layout,showSettings:!e.layout.showSettings}}),!1,"toggleSettings"),updateFilter:(t,r)=>e(e=>({filters:{...e.filters,[t]:r}}),!1,"updateFilter"),clearFilters:()=>e({filters:{}},!1,"clearFilters"),setSorting:(t,r)=>e({sortBy:t,sortDirection:r},!1,"setSorting"),setSearchTerm:t=>e({searchTerm:t},!1,"setSearchTerm"),toggleItemSelection:t=>e(e=>{let r=new Set(e.selectedItems);return r.has(t)?r.delete(t):r.add(t),{selectedItems:r}},!1,"toggleItemSelection"),clearSelection:()=>e({selectedItems:new Set},!1,"clearSelection"),selectAll:t=>e({selectedItems:new Set(t)},!1,"selectAll"),setMonitoringEnabled:t=>e(e=>({monitoring:{...e.monitoring,enabled:t}}),!1,"setMonitoringEnabled"),setRefreshInterval:t=>e(e=>({monitoring:{...e.monitoring,refreshInterval:t}}),!1,"setRefreshInterval"),toggleAutoRefresh:()=>e(e=>({monitoring:{...e.monitoring,autoRefresh:!e.monitoring.autoRefresh}}),!1,"toggleAutoRefresh"),pauseDataType:t=>e(e=>({monitoring:{...e.monitoring,pausedDataTypes:new Set([...e.monitoring.pausedDataTypes,t])}}),!1,"pauseDataType"),resumeDataType:t=>e(e=>{let r=new Set(e.monitoring.pausedDataTypes);return r.delete(t),{monitoring:{...e.monitoring,pausedDataTypes:r}}},!1,"resumeDataType"),resetSettings:()=>e({layout:{viewMode:"cards",gridColumns:3,compactMode:!1,showFilters:!0,showSettings:!1},monitoring:{enabled:!1,refreshInterval:3e4,autoRefresh:!0,pausedDataTypes:new Set},filters:{},sortBy:"createdAt",sortDirection:"desc",selectedItems:new Set,searchTerm:""},!1,"resetSettings"),getFilteredData:(e,r)=>{let s=t(),a=[...e];if(s.searchTerm){let e=s.searchTerm.toLowerCase();a=a.filter(t=>Object.values(t).some(t=>String(t).toLowerCase().includes(e)))}return Object.entries(s.filters).forEach(e=>{let[t,s]=e;null!=s&&""!==s&&(a=a.filter(e=>{var a;let i=null==(a=r.filters)?void 0:a.find(e=>e.id===t);if(!i)return!0;switch(i.type){case"select":return e[t]===s;case"multiselect":return!Array.isArray(s)||s.includes(e[t]);case"toggle":return!s||e[t];default:return!0}}))}),a.sort((e,t)=>{let r=e[s.sortBy],a=t[s.sortBy],i="asc"===s.sortDirection?1:-1;return r<a?-1*i:r>a?+i:0}),a},getSelectedCount:()=>t().selectedItems.size,hasActiveFilters:()=>{let e=t();return e.searchTerm.length>0||Object.values(e.filters).some(e=>null!=e&&""!==e)}}),{name:"workhub-dashboard-".concat(e),partialize:e=>({layout:e.layout,monitoring:e.monitoring,filters:e.filters,sortBy:e.sortBy,sortDirection:e.sortDirection})}),{name:"dashboard-".concat(e)}))),n.get(e)),[e])}}}]);