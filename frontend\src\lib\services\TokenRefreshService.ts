/**
 * @file Token Refresh Service for automatic JWT token management
 * @module lib/services/TokenRefreshService
 */

import type { Session } from '@supabase/supabase-js';

import { getEnvironmentConfig } from '../config/environment';
import { SessionManager } from '../security/SessionManager';
import { supabase } from '../supabase';

/**
 * Token refresh callback type
 */
export type TokenRefreshCallback = (
  event: TokenRefreshEvent,
  data?: any
) => void;

/**
 * Token refresh event types
 */
export type TokenRefreshEvent =
  | 'critical_refresh_failed'
  | 'refresh_failed'
  | 'refresh_scheduled'
  | 'refresh_success'
  | 'session_invalid'
  | 'session_validated';

/**
 * Token refresh service configuration
 */
interface TokenRefreshConfig {
  /** Base delay for exponential backoff in milliseconds (default: 1000) */
  baseRetryDelay: number;
  /** Enable debug logging (default: false) */
  enableDebugLogging: boolean;
  /** Maximum retry attempts for failed refresh (default: 3) */
  maxRetryAttempts: number;
  /** Refresh token X minutes before expiration (default: 5 minutes) */
  refreshBeforeExpiryMinutes: number;
}

/**
 * Default configuration for token refresh service
 */
const DEFAULT_CONFIG: TokenRefreshConfig = {
  baseRetryDelay: 1000,
  enableDebugLogging: process.env.NODE_ENV !== 'production',
  maxRetryAttempts: 3,
  refreshBeforeExpiryMinutes: 5,
};

/**
 * Singleton service for managing automatic JWT token refresh
 *
 * Features:
 * - Automatic scheduling based on token expiration
 * - Exponential backoff retry logic
 * - Event-driven architecture for integration
 * - Browser tab visibility handling
 * - Concurrent refresh prevention
 */
export class TokenRefreshService {
  private static instance: null | TokenRefreshService = null;

  private readonly callbacks = new Set<TokenRefreshCallback>();
  private readonly config: TokenRefreshConfig;
  private currentSession: null | Session = null;
  private isRefreshing = false;
  private isTabVisible = true;
  private refreshTimeout: NodeJS.Timeout | null = null;
  private retryAttempts = 0;
  private retryTimeout: NodeJS.Timeout | null = null;

  private constructor(config: Partial<TokenRefreshConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.setupVisibilityHandling();
    this.log('TokenRefreshService initialized');
  }

  /**
   * Get singleton instance
   */
  public static getInstance(
    config?: Partial<TokenRefreshConfig>
  ): TokenRefreshService {
    if (!TokenRefreshService.instance) {
      TokenRefreshService.instance = new TokenRefreshService(config);
    }
    return TokenRefreshService.instance;
  }

  /**
   * Get current session information for debugging and validation
   */
  public async getSessionInfo(): Promise<{
    error?: string;
    expiresAt?: number;
    isExpired?: boolean;
    isValid: boolean;
    user?: {
      email?: string;
      id: string;
      role?: string;
    };
  }> {
    try {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      if (error) {
        this.log('Error getting session info', { error });
        SessionManager.handleSessionValidation(false, { error: error.message });
        return { error: error.message, isValid: false };
      }

      if (!session) {
        this.log('No active session found');
        SessionManager.handleSessionValidation(false, {
          error: 'No active session',
        });
        return { error: 'No active session', isValid: false };
      }

      const now = Math.floor(Date.now() / 1000);
      const isExpired = session.expires_at ? session.expires_at < now : false;

      const result: {
        error?: string;
        expiresAt?: number;
        isExpired?: boolean;
        isValid: boolean;
        user?: {
          email?: string;
          id: string;
          role?: string;
        };
      } = {
        isExpired,
        isValid: !isExpired,
        user: {
          id: session.user.id,
        },
      };

      if (session.expires_at) {
        result.expiresAt = session.expires_at;
      }

      if (session.user.email) {
        result.user!.email = session.user.email;
      }

      if (session.user.user_metadata?.role) {
        result.user!.role = session.user.user_metadata.role;
      }

      return result;
    } catch (error) {
      this.log('Exception in getSessionInfo', { error });
      return {
        error: error instanceof Error ? error.message : 'Unknown error',
        isValid: false,
      };
    }
  }

  /**
   * Manually trigger token refresh
   */
  public async refreshNow(): Promise<boolean> {
    if (this.isRefreshing) {
      this.log('Refresh already in progress, skipping');
      return false;
    }

    return this.performRefresh();
  }

  /**
   * Stop the service and cleanup
   */
  public stop(): void {
    this.clearScheduledRefresh();
    this.clearRetryTimeout();
    this.callbacks.clear();
    this.currentSession = null;
    this.isRefreshing = false;
    this.retryAttempts = 0;
    this.log('TokenRefreshService stopped');
  }

  /**
   * Subscribe to token refresh events
   */
  public subscribe(callback: TokenRefreshCallback): () => void {
    this.callbacks.add(callback);
    return () => this.callbacks.delete(callback);
  }

  /**
   * Update session and schedule refresh
   */
  public updateSession(session: null | Session): void {
    this.currentSession = session;

    if (session) {
      this.scheduleRefresh(session);
      this.log('Session updated, refresh scheduled', {
        expiresAt: new Date(session.expires_at! * 1000).toISOString(),
      });
    } else {
      this.clearScheduledRefresh();
      this.log('Session cleared, refresh cancelled');
    }
  }

  /**
   * Clear retry timeout
   */
  private clearRetryTimeout(): void {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
      this.retryTimeout = null;
    }
  }

  /**
   * Clear scheduled refresh timeout
   */
  private clearScheduledRefresh(): void {
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout);
      this.refreshTimeout = null;
    }
  }

  /**
   * Emit event to all subscribers
   */
  private emitEvent(event: TokenRefreshEvent, data?: any): void {
    for (const callback of this.callbacks) {
      try {
        callback(event, data);
      } catch (error) {
        console.error('Error in token refresh callback:', error);
      }
    }
  }

  /**
   * Handle refresh failure with retry logic
   */
  private handleRefreshFailure(errorData: any): void {
    this.retryAttempts++;

    if (this.retryAttempts >= this.config.maxRetryAttempts) {
      this.log(
        'Max retry attempts reached, giving up and signaling critical failure'
      );
      this.emitEvent('critical_refresh_failed', {
        attempts: this.retryAttempts,
        error: 'Max retry attempts exceeded, session unrecoverable',
        ...errorData,
      });
      // Optionally, you might want to clear the session here or trigger a logout
      // this.updateSession(null);
      return;
    }

    // Exponential backoff
    const delay =
      this.config.baseRetryDelay * Math.pow(2, this.retryAttempts - 1);

    this.log('Scheduling retry', {
      attempt: this.retryAttempts,
      delayMs: delay,
    });

    this.retryTimeout = setTimeout(() => {
      this.performRefresh();
    }, delay);
  }

  /**
   * Debug logging
   */
  private log(message: string, data?: any): void {
    if (this.config.enableDebugLogging) {
      console.log(`🔄 TokenRefreshService: ${message}`, data || '');
    }
  }

  /**
   * Perform the actual token refresh
   */
  private async performRefresh(): Promise<boolean> {
    if (this.isRefreshing) {
      return false;
    }

    this.isRefreshing = true;
    this.log('Starting token refresh');

    try {
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (this.currentSession?.access_token) {
        headers.Authorization = `Bearer ${this.currentSession.access_token}`;
      }

      // Prepare request body with refresh token (Supabase OAuth2 standard)
      const requestBody: any = {};

      // Include refresh token in body if available from current session
      if (this.currentSession?.refresh_token) {
        requestBody.refresh_token = this.currentSession.refresh_token;
        this.log('Including refresh token in request body');
      } else {
        this.log('Warning: No refresh token available in current session');
      }

      // Use environment-aware backend URL for token refresh
      const backendUrl = getEnvironmentConfig().apiBaseUrl;
      const response = await fetch(`${backendUrl}/auth/refresh`, {
        credentials: 'include', // Include httpOnly cookies
        headers,
        method: 'POST',
        body: JSON.stringify(requestBody), // Send refresh token in body
      });

      if (response.ok) {
        const data = await response.json();
        this.log('Token refresh successful', {
          expiresIn: data.expiresIn,
        });

        // Reset retry attempts on success
        this.retryAttempts = 0;
        this.clearRetryTimeout();

        // Explicitly update Supabase session with the new tokens received from backend
        try {
          // The 'data' object from the backend response should contain 'newTokens'
          const { newTokens } = data;
          if (newTokens?.session && newTokens?.user) {
            // Use setSession to explicitly update Supabase's internal state
            await supabase.auth.setSession({
              access_token: newTokens.session.access_token,
              refresh_token: newTokens.session.refresh_token,
            });
            this.currentSession = newTokens.session; // Update internal currentSession
            this.log('Supabase session explicitly updated with new tokens');
          } else {
            this.log(
              'Warning: New tokens from backend did not contain full session/user data',
              { data } // Log the entire data object for debugging
            );
          }
        } catch (sessionUpdateError) {
          this.log('Error explicitly updating Supabase session after refresh', {
            sessionUpdateError,
          });
        }

        this.emitEvent('refresh_success', data);
        SessionManager.handleTokenRefresh(true, data);
        return true;
      } else {
        const errorData = await response.json().catch(() => ({}));
        this.log('Token refresh failed', {
          error: errorData,
          status: response.status,
        });

        this.handleRefreshFailure(errorData);
        SessionManager.handleTokenRefresh(false, errorData);
        return false;
      }
    } catch (error) {
      this.log('Token refresh error', {
        error: error instanceof Error ? error.message : String(error),
      });

      this.handleRefreshFailure({ error: 'Network error' });
      SessionManager.handleTokenRefresh(false, { error: 'Network error' });
      return false;
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * Schedule token refresh based on expiration time
   */
  private scheduleRefresh(session: Session): void {
    this.clearScheduledRefresh();

    if (!session.expires_at) {
      this.log('No expiration time in session, cannot schedule refresh');
      return;
    }

    const expiresAt = session.expires_at * 1000; // Convert to milliseconds
    const refreshAt =
      expiresAt - this.config.refreshBeforeExpiryMinutes * 60 * 1000;
    const now = Date.now();
    const delay = Math.max(0, refreshAt - now);

    if (delay === 0) {
      // Token is already expired or about to expire, refresh immediately
      this.log('Token expired or about to expire, refreshing immediately');
      this.performRefresh();
      return;
    }

    this.refreshTimeout = setTimeout(() => {
      this.performRefresh();
    }, delay);

    this.log('Refresh scheduled', {
      delayMinutes: Math.round(delay / 60_000),
      refreshAt: new Date(refreshAt).toISOString(),
    });

    this.emitEvent('refresh_scheduled', { delay, refreshAt });
  }

  /**
   * Setup browser tab visibility handling
   */
  private setupVisibilityHandling(): void {
    if (typeof document === 'undefined') return;

    document.addEventListener('visibilitychange', () => {
      this.isTabVisible = !document.hidden;

      if (this.isTabVisible && this.currentSession) {
        // Tab became visible, check if we need to refresh
        const now = Date.now();
        const expiresAt = (this.currentSession.expires_at || 0) * 1000;
        const timeUntilExpiry = expiresAt - now;

        if (
          timeUntilExpiry <=
          this.config.refreshBeforeExpiryMinutes * 60 * 1000
        ) {
          this.log('Tab visible and token needs refresh');
          this.performRefresh();
        }
      }
    });
  }
}

/**
 * Get the singleton token refresh service instance
 */
export const getTokenRefreshService = (config?: Partial<TokenRefreshConfig>) =>
  TokenRefreshService.getInstance(config);
