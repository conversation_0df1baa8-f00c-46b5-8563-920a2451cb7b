"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[970],{12543:(e,l,a)=>{a.d(l,{A:()=>t});let t=(0,a(40157).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},17759:(e,l,a)=>{a.d(l,{C5:()=>b,MJ:()=>h,Rr:()=>g,eI:()=>f,lR:()=>x,lV:()=>d,zB:()=>m});var t=a(95155),r=a(12115),s=a(99708),n=a(62177),i=a(54036),o=a(85057);let d=n.Op,c=r.createContext({}),m=e=>{let{...l}=e;return(0,t.jsx)(c.Provider,{value:{name:l.name},children:(0,t.jsx)(n.xI,{...l})})},u=()=>{let e=r.useContext(c),l=r.useContext(p),{getFieldState:a,formState:t}=(0,n.xW)(),s=a(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=l;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...s}},p=r.createContext({}),f=r.forwardRef((e,l)=>{let{className:a,...s}=e,n=r.useId();return(0,t.jsx)(p.Provider,{value:{id:n},children:(0,t.jsx)("div",{ref:l,className:(0,i.cn)("space-y-2",a),...s})})});f.displayName="FormItem";let x=r.forwardRef((e,l)=>{let{className:a,...r}=e,{error:s,formItemId:n}=u();return(0,t.jsx)(o.J,{ref:l,className:(0,i.cn)(s&&"text-destructive",a),htmlFor:n,...r})});x.displayName="FormLabel";let h=r.forwardRef((e,l)=>{let{...a}=e,{error:r,formItemId:n,formDescriptionId:i,formMessageId:o}=u();return(0,t.jsx)(s.DX,{ref:l,id:n,"aria-describedby":r?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!r,...a})});h.displayName="FormControl";let g=r.forwardRef((e,l)=>{let{className:a,...r}=e,{formDescriptionId:s}=u();return(0,t.jsx)("p",{ref:l,id:s,className:(0,i.cn)("text-sm text-muted-foreground",a),...r})});g.displayName="FormDescription";let b=r.forwardRef((e,l)=>{var a;let{className:r,children:s,...n}=e,{error:o,formMessageId:d}=u(),c=o?String(null!=(a=null==o?void 0:o.message)?a:""):s;return c?(0,t.jsx)("p",{ref:l,id:d,className:(0,i.cn)("text-sm font-medium text-destructive",r),...n,children:c}):null});b.displayName="FormMessage"},20636:(e,l,a)=>{a.d(l,{O2:()=>s,Q:()=>n,gT:()=>i});var t=a(71153),r=a(33450);let s=t.k5(["Active","On_Leave","Terminated","Inactive"]),n=t.k5(["driver","mechanic","administrator","office_staff","manager","service_advisor","technician","other"]),i=t.Ik({availability:r.X.optional().nullable(),contactEmail:t.Yj().email("Invalid email address").nullable().optional(),contactInfo:t.Yj().min(1,"Contact Info is required"),contactMobile:t.Yj().nullable().optional(),contactPhone:t.Yj().nullable().optional(),currentLocation:t.Yj().nullable().optional(),department:t.Yj().nullable().optional(),employeeId:t.Yj().min(1,"Employee ID (unique business ID) is required"),fullName:t.Yj().nullable().optional(),generalAssignments:t.YO(t.Yj()),hireDate:t.Yj().nullable().optional().refine(e=>null===e||""===e||null==e||""===e||!isNaN(Date.parse(e)),{message:"Invalid hire date"}),name:t.Yj().min(1,"Name is required"),notes:t.Yj().nullable().optional(),position:t.Yj().nullable().optional(),profileImageUrl:t.Yj().optional().nullable().refine(e=>{if(!e||""===e.trim())return!0;try{return new URL(e),!0}catch(e){return!1}},{message:"Invalid URL for profile image"}),role:n,shiftSchedule:t.Yj().nullable().optional(),skills:t.YO(t.Yj()),status:s,workingHours:t.Yj().nullable().optional()})},33450:(e,l,a)=>{a.d(l,{X:()=>t});let t=a(71153).k5(["On_Shift","Off_Shift","On_Break","Busy"])},44744:(e,l,a)=>{a.d(l,{N:()=>g});var t=a(95155),r=a(12543),s=a(59119),n=a(35695);a(12115);var i=a(62177),o=a(30285),d=a(66695),c=a(75668),m=a(68801),u=a(62523),p=a(59409),f=a(33450),x=a(20636),h=a(99673);let g=e=>{var l,a,i,m,u,p,f,h,g,j,v,y,N,w,C,I,k,z,A,R,S,D;let{initialData:O={},isEditing:_=!1,onSubmit:Y}=e,E=(0,n.useRouter)(),L={availability:null!=(l=O.availability)?l:void 0,contactEmail:null!=(a=O.contactEmail)?a:"",contactInfo:null!=(m=null!=(i=O.contactInfo)?i:O.contactEmail)?m:"",contactMobile:null!=(u=O.contactMobile)?u:"",contactPhone:null!=(p=O.contactPhone)?p:"",currentLocation:null!=(f=O.currentLocation)?f:"",department:null!=(h=O.department)?h:"",employeeId:null!=(g=O.employeeId)?g:"",fullName:null!=(v=null!=(j=O.fullName)?j:O.name)?v:"",generalAssignments:null!=(y=O.generalAssignments)?y:[],hireDate:O.hireDate?new Date(O.hireDate).toISOString().split("T")[0]:"",name:null!=(w=null!=(N=O.name)?N:O.fullName)?w:"",notes:null!=(C=O.notes)?C:"",position:null!=(I=O.position)?I:"",profileImageUrl:null!=(k=O.profileImageUrl)?k:"",role:null!=(z=O.role)?z:"other",shiftSchedule:null!=(A=O.shiftSchedule)?A:"",skills:null!=(R=O.skills)?R:[],status:null!=(S=O.status)?S:x.O2.enum.Active,workingHours:null!=(D=O.workingHours)?D:""},P=async e=>{await Y(e)};return(0,t.jsx)(c.I,{defaultValues:L,onSubmit:P,schema:x.gT,children:(0,t.jsxs)(d.Zp,{className:"shadow-lg",children:[(0,t.jsx)(d.aR,{children:(0,t.jsx)(d.ZB,{className:"text-2xl text-primary",children:_?"Edit Employee":"Add New Employee"})}),(0,t.jsx)(d.Wu,{className:"space-y-6",children:(0,t.jsx)(b,{})}),(0,t.jsxs)(d.wL,{className:"flex justify-between gap-2 border-t pt-6",children:[(0,t.jsxs)(o.$,{onClick:()=>E.back(),type:"button",variant:"outline",children:[(0,t.jsx)(r.A,{className:"mr-2 size-4"}),"Cancel"]}),(0,t.jsxs)(o.$,{className:"bg-accent text-accent-foreground hover:bg-accent/90",type:"submit",children:[(0,t.jsx)(s.A,{className:"mr-2 size-4"}),_?"Save Changes":"Create Employee"]})]})]})})},b=()=>{let{watch:e}=(0,i.xW)(),l=e("role");return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,t.jsx)(m.z,{label:"Display Name",name:"name"}),(0,t.jsx)(m.z,{label:"Full Name (Optional)",name:"fullName"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,t.jsx)(m.z,{label:"Employee ID (Business Key)",name:"employeeId"}),(0,t.jsx)(m.z,{label:"Position/Title",name:"position"})]}),(0,t.jsx)(m.z,{label:"Department",name:"department"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Contact Information"}),(0,t.jsx)(m.z,{label:"Primary Contact (Email/Phone)",name:"contactInfo"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,t.jsx)(m.z,{label:"Contact Email (Optional)",name:"contactEmail",type:"email"}),(0,t.jsx)(m.z,{label:"Contact Phone (Optional)",name:"contactPhone",type:"tel"})]}),(0,t.jsx)(m.z,{label:"Contact Mobile (Optional)",name:"contactMobile",type:"tel"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Employment Details"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,t.jsx)(m.z,{label:"Hire Date",name:"hireDate",type:"date"}),(0,t.jsx)(m.z,{label:"Status",name:"status",render:e=>{let{field:l}=e;return(0,t.jsxs)(p.l6,{onValueChange:l.onChange,value:l.value,children:[(0,t.jsx)(p.bq,{id:"status",children:(0,t.jsx)(p.yv,{placeholder:"Select status"})}),(0,t.jsx)(p.gC,{children:x.O2.options.map(e=>(0,t.jsx)(p.eb,{value:e,children:(0,h.vq)(e)},e))})]})}})]}),(0,t.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Role & Availability"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,t.jsx)(m.z,{label:"Role",name:"role",render:e=>{let{field:l}=e;return(0,t.jsxs)(p.l6,{onValueChange:l.onChange,value:l.value,children:[(0,t.jsx)(p.bq,{id:"role",children:(0,t.jsx)(p.yv,{placeholder:"Select role"})}),(0,t.jsx)(p.gC,{children:x.Q.options.map(e=>(0,t.jsx)(p.eb,{value:e,children:e.charAt(0).toUpperCase()+e.slice(1).replace("_"," ")},e))})]})}}),"driver"===l&&(0,t.jsx)(m.z,{label:"Availability (for Drivers)",name:"availability",render:e=>{let{field:l}=e;return(0,t.jsxs)(p.l6,{value:l.value||"",onValueChange:l.onChange,children:[(0,t.jsx)(p.bq,{id:"availability",children:(0,t.jsx)(p.yv,{placeholder:"Select availability"})}),(0,t.jsx)(p.gC,{children:f.X.options.map(e=>(0,t.jsx)(p.eb,{value:e,children:e.replace("_"," ")},e))})]})}})]}),"driver"===l&&(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,t.jsx)(m.z,{label:"Current Location (Optional, for Drivers)",name:"currentLocation"}),(0,t.jsx)(m.z,{label:"Working Hours (Optional, for Drivers)",name:"workingHours"})]})}),(0,t.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Other Details"}),(0,t.jsx)(m.z,{label:"Skills (comma-separated)",name:"skills",render:e=>{let{field:l}=e;return(0,t.jsx)(u.p,{onChange:e=>{let a=e.target.value.split(",").map(e=>e.trim()).filter(Boolean);l.onChange(a)},placeholder:"e.g., Diesel Engine Repair, HVAC Systems, Welding",value:Array.isArray(l.value)?l.value.join(", "):""})}}),(0,t.jsx)(m.z,{label:"Shift Schedule (Optional)",name:"shiftSchedule"}),(0,t.jsx)(m.z,{label:"General Assignments (comma-separated, Optional)",name:"generalAssignments",render:e=>{let{field:l}=e;return(0,t.jsx)(u.p,{onChange:e=>{let a=e.target.value.split(",").map(e=>e.trim()).filter(Boolean);l.onChange(a)},placeholder:"e.g., Workshop Cleanup, Inventory Check",value:Array.isArray(l.value)?l.value.join(", "):""})}}),(0,t.jsx)(m.z,{label:"Profile Image URL (Optional)",name:"profileImageUrl"}),(0,t.jsx)(m.z,{label:"Notes (Optional)",name:"notes",type:"textarea"})]})}},59119:(e,l,a)=>{a.d(l,{A:()=>t});let t=(0,a(40157).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},59409:(e,l,a)=>{a.d(l,{bq:()=>u,eb:()=>h,gC:()=>x,l6:()=>c,yv:()=>m});var t=a(95155),r=a(31992),s=a(79556),n=a(77381),i=a(10518),o=a(12115),d=a(54036);let c=r.bL;r.YJ;let m=r.WT,u=o.forwardRef((e,l)=>{let{children:a,className:n,...i}=e;return(0,t.jsxs)(r.l9,{className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",n),ref:l,...i,children:[a,(0,t.jsx)(r.In,{asChild:!0,children:(0,t.jsx)(s.A,{className:"size-4 opacity-50"})})]})});u.displayName=r.l9.displayName;let p=o.forwardRef((e,l)=>{let{className:a,...s}=e;return(0,t.jsx)(r.PP,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),ref:l,...s,children:(0,t.jsx)(n.A,{className:"size-4"})})});p.displayName=r.PP.displayName;let f=o.forwardRef((e,l)=>{let{className:a,...n}=e;return(0,t.jsx)(r.wn,{className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),ref:l,...n,children:(0,t.jsx)(s.A,{className:"size-4"})})});f.displayName=r.wn.displayName;let x=o.forwardRef((e,l)=>{let{children:a,className:s,position:n="popper",...i}=e;return(0,t.jsx)(r.ZL,{children:(0,t.jsxs)(r.UC,{className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,ref:l,...i,children:[(0,t.jsx)(p,{}),(0,t.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,t.jsx)(f,{})]})})});x.displayName=r.UC.displayName,o.forwardRef((e,l)=>{let{className:a,...s}=e;return(0,t.jsx)(r.JU,{className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),ref:l,...s})}).displayName=r.JU.displayName;let h=o.memo(o.forwardRef((e,l)=>{let{children:a,className:s,...n}=e,c=o.useCallback(e=>{"function"==typeof l?l(e):l&&(l.current=e)},[l]);return(0,t.jsxs)(r.q7,{className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),ref:c,...n,children:[(0,t.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,t.jsx)(r.VF,{children:(0,t.jsx)(i.A,{className:"size-4"})})}),(0,t.jsx)(r.p4,{children:a})]})}));h.displayName=r.q7.displayName,o.forwardRef((e,l)=>{let{className:a,...s}=e;return(0,t.jsx)(r.wv,{className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",a),ref:l,...s})}).displayName=r.wv.displayName},62523:(e,l,a)=>{a.d(l,{p:()=>n});var t=a(95155),r=a(12115),s=a(54036);let n=r.forwardRef((e,l)=>{let{className:a,type:r,...n}=e;return(0,t.jsx)("input",{className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:l,type:r,...n})});n.displayName="Input"},68801:(e,l,a)=>{a.d(l,{z:()=>d});var t=a(95155);a(12115);var r=a(62177),s=a(17759),n=a(62523),i=a(88539),o=a(59409);let d=e=>{let{className:l="",disabled:a=!1,label:d,name:c,placeholder:m,render:u,type:p="text",options:f=[],defaultValue:x,icon:h,...g}=e,{control:b}=(0,r.xW)();return(0,t.jsxs)(s.eI,{className:l,children:[(0,t.jsx)(s.lR,{htmlFor:c,children:d}),(0,t.jsx)(r.xI,{control:b,name:c,render:u||(e=>{var l,r;let{field:u,fieldState:{error:b}}=e;return(0,t.jsx)(s.MJ,{children:"select"===p?(0,t.jsxs)(o.l6,{onValueChange:u.onChange,value:u.value||x||"",disabled:a,children:[(0,t.jsx)(o.bq,{className:b?"border-red-500":"",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[h&&(0,t.jsx)(h,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)(o.yv,{placeholder:m||"Select ".concat(d.toLowerCase())})]})}),(0,t.jsx)(o.gC,{children:f.map(e=>(0,t.jsx)(o.eb,{value:String(e.value),disabled:e.disabled||!1,children:e.label},e.value))})]}):"textarea"===p?(0,t.jsxs)("div",{className:"relative",children:[h&&(0,t.jsx)(h,{className:"absolute left-3 top-3 h-4 w-4 text-gray-500"}),(0,t.jsx)(i.T,{...u,...g,value:null!=(l=u.value)?l:"",className:"".concat(b?"border-red-500":""," ").concat(h?"pl-10":""),disabled:a,id:c,placeholder:m})]}):(0,t.jsxs)("div",{className:"relative",children:[h&&(0,t.jsx)(h,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500"}),(0,t.jsx)(n.p,{...u,...g,value:null!=(r=u.value)?r:"",className:"".concat(b?"border-red-500":""," ").concat(h?"pl-10":""),disabled:a,id:c,placeholder:m,type:p})]})})})}),(0,t.jsx)(s.C5,{})]})}},75668:(e,l,a)=>{a.d(l,{I:()=>i});var t=a(95155),r=a(90221),s=a(62177),n=a(17759);let i=e=>{let{children:l,defaultValues:a,onSubmit:i,schema:o,className:d="",ariaAttributes:c={}}=e,m=(0,s.mN)({...a&&{defaultValues:a},resolver:(0,r.u)(o)}),u=async e=>{await i(e)};return(0,t.jsx)(n.lV,{...m,children:(0,t.jsx)("form",{onSubmit:m.handleSubmit(u),className:d,...c,children:l})})}},85057:(e,l,a)=>{a.d(l,{J:()=>d});var t=a(95155),r=a(12115),s=a(40968),n=a(74466),i=a(54036);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,l)=>{let{className:a,...r}=e;return(0,t.jsx)(s.b,{ref:l,className:(0,i.cn)(o(),a),...r})});d.displayName=s.b.displayName},88539:(e,l,a)=>{a.d(l,{T:()=>n});var t=a(95155),r=a(12115),s=a(54036);let n=r.forwardRef((e,l)=>{let{className:a,...r}=e;return(0,t.jsx)("textarea",{className:(0,s.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:l,...r})});n.displayName="Textarea"},95647:(e,l,a)=>{a.d(l,{z:()=>r});var t=a(95155);function r(e){let{children:l,description:a,icon:r,title:s}=e;return(0,t.jsxs)("div",{className:"mb-6 flex items-center justify-between border-b border-border/50 pb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[r&&(0,t.jsx)(r,{className:"size-8 text-primary"}),(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:s})]}),a&&(0,t.jsx)("p",{className:"mt-1 text-muted-foreground",children:a})]}),l&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:l})]})}a(12115)},99673:(e,l,a)=>{function t(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function r(e){var l,a;if(null==(l=e.fullName)?void 0:l.trim())return e.fullName.trim();if(null==(a=e.name)?void 0:a.trim()){let l=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(l.toLowerCase())||l.includes("_")){let e=l.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(e," (Role)")}return l}if(e.role){let l=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return"".concat(l," (Role)")}return"Unknown Employee"}function s(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function n(e){return e.replaceAll("_"," ")}a.d(l,{DV:()=>r,fZ:()=>t,s:()=>s,vq:()=>n})}}]);