"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8982],{45731:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(40157).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},47655:(e,r,t)=>{t.d(r,{LM:()=>q,OK:()=>K,VM:()=>E,bL:()=>G,lr:()=>N});var o=t(12115),n=t(63655),l=t(28905),i=t(46081),a=t(6101),s=t(39033),c=t(94315),d=t(52712),u=t(89367),p=t(85185),f=t(95155),h="ScrollArea",[v,w]=(0,i.A)(h),[b,g]=v(h),m=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:l="hover",dir:i,scrollHideDelay:s=600,...d}=e,[u,p]=o.useState(null),[h,v]=o.useState(null),[w,g]=o.useState(null),[m,S]=o.useState(null),[y,x]=o.useState(null),[E,C]=o.useState(0),[T,R]=o.useState(0),[L,P]=o.useState(!1),[_,A]=o.useState(!1),j=(0,a.s)(r,e=>p(e)),D=(0,c.jH)(i);return(0,f.jsx)(b,{scope:t,type:l,dir:D,scrollHideDelay:s,scrollArea:u,viewport:h,onViewportChange:v,content:w,onContentChange:g,scrollbarX:m,onScrollbarXChange:S,scrollbarXEnabled:L,onScrollbarXEnabledChange:P,scrollbarY:y,onScrollbarYChange:x,scrollbarYEnabled:_,onScrollbarYEnabledChange:A,onCornerWidthChange:C,onCornerHeightChange:R,children:(0,f.jsx)(n.sG.div,{dir:D,...d,ref:j,style:{position:"relative","--radix-scroll-area-corner-width":E+"px","--radix-scroll-area-corner-height":T+"px",...e.style}})})});m.displayName=h;var S="ScrollAreaViewport",y=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:l,nonce:i,...s}=e,c=g(S,t),d=o.useRef(null),u=(0,a.s)(r,d,c.onViewportChange);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,f.jsx)(n.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:u,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,f.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});y.displayName=S;var x="ScrollAreaScrollbar",E=o.forwardRef((e,r)=>{let{forceMount:t,...n}=e,l=g(x,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=l,s="horizontal"===e.orientation;return o.useEffect(()=>(s?i(!0):a(!0),()=>{s?i(!1):a(!1)}),[s,i,a]),"hover"===l.type?(0,f.jsx)(C,{...n,ref:r,forceMount:t}):"scroll"===l.type?(0,f.jsx)(T,{...n,ref:r,forceMount:t}):"auto"===l.type?(0,f.jsx)(R,{...n,ref:r,forceMount:t}):"always"===l.type?(0,f.jsx)(L,{...n,ref:r}):null});E.displayName=x;var C=o.forwardRef((e,r)=>{let{forceMount:t,...n}=e,i=g(x,e.__scopeScrollArea),[a,s]=o.useState(!1);return o.useEffect(()=>{let e=i.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),s(!0)},o=()=>{r=window.setTimeout(()=>s(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",o),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",o)}}},[i.scrollArea,i.scrollHideDelay]),(0,f.jsx)(l.C,{present:t||a,children:(0,f.jsx)(R,{"data-state":a?"visible":"hidden",...n,ref:r})})}),T=o.forwardRef((e,r)=>{var t,n;let{forceMount:i,...a}=e,s=g(x,e.__scopeScrollArea),c="horizontal"===e.orientation,d=F(()=>h("SCROLL_END"),100),[u,h]=(t="hidden",n={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},o.useReducer((e,r)=>{let t=n[e][r];return null!=t?t:e},t));return o.useEffect(()=>{if("idle"===u){let e=window.setTimeout(()=>h("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[u,s.scrollHideDelay,h]),o.useEffect(()=>{let e=s.viewport,r=c?"scrollLeft":"scrollTop";if(e){let t=e[r],o=()=>{let o=e[r];t!==o&&(h("SCROLL"),d()),t=o};return e.addEventListener("scroll",o),()=>e.removeEventListener("scroll",o)}},[s.viewport,c,h,d]),(0,f.jsx)(l.C,{present:i||"hidden"!==u,children:(0,f.jsx)(L,{"data-state":"hidden"===u?"hidden":"visible",...a,ref:r,onPointerEnter:(0,p.m)(e.onPointerEnter,()=>h("POINTER_ENTER")),onPointerLeave:(0,p.m)(e.onPointerLeave,()=>h("POINTER_LEAVE"))})})}),R=o.forwardRef((e,r)=>{let t=g(x,e.__scopeScrollArea),{forceMount:n,...i}=e,[a,s]=o.useState(!1),c="horizontal"===e.orientation,d=F(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;s(c?e:r)}},10);return B(t.viewport,d),B(t.content,d),(0,f.jsx)(l.C,{present:n||a,children:(0,f.jsx)(L,{"data-state":a?"visible":"hidden",...i,ref:r})})}),L=o.forwardRef((e,r)=>{let{orientation:t="vertical",...n}=e,l=g(x,e.__scopeScrollArea),i=o.useRef(null),a=o.useRef(0),[s,c]=o.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=M(s.viewport,s.content),u={...n,sizes:s,onSizesChange:c,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function p(e,r){return function(e,r,t){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",n=X(t),l=r||n/2,i=t.scrollbar.paddingStart+l,a=t.scrollbar.size-t.scrollbar.paddingEnd-(n-l),s=t.content-t.viewport;return U([i,a],"ltr"===o?[0,s]:[-1*s,0])(e)}(e,a.current,s,r)}return"horizontal"===t?(0,f.jsx)(P,{...u,ref:r,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=Y(l.viewport.scrollLeft,s,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===t?(0,f.jsx)(_,{...u,ref:r,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=Y(l.viewport.scrollTop,s);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),P=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:n,...l}=e,i=g(x,e.__scopeScrollArea),[s,c]=o.useState(),d=o.useRef(null),u=(0,a.s)(r,d,i.onScrollbarXChange);return o.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,f.jsx)(D,{"data-orientation":"horizontal",...l,ref:u,sizes:t,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":X(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(i.viewport){let o=i.viewport.scrollLeft+r.deltaX;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&n({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:O(s.paddingLeft),paddingEnd:O(s.paddingRight)}})}})}),_=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:n,...l}=e,i=g(x,e.__scopeScrollArea),[s,c]=o.useState(),d=o.useRef(null),u=(0,a.s)(r,d,i.onScrollbarYChange);return o.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,f.jsx)(D,{"data-orientation":"vertical",...l,ref:u,sizes:t,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":X(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(i.viewport){let o=i.viewport.scrollTop+r.deltaY;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&n({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:O(s.paddingTop),paddingEnd:O(s.paddingBottom)}})}})}),[A,j]=v(x),D=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:l,hasThumb:i,onThumbChange:c,onThumbPointerUp:d,onThumbPointerDown:u,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:w,onResize:b,...m}=e,S=g(x,t),[y,E]=o.useState(null),C=(0,a.s)(r,e=>E(e)),T=o.useRef(null),R=o.useRef(""),L=S.viewport,P=l.content-l.viewport,_=(0,s.c)(w),j=(0,s.c)(h),D=F(b,10);function k(e){T.current&&v({x:e.clientX-T.current.left,y:e.clientY-T.current.top})}return o.useEffect(()=>{let e=e=>{let r=e.target;(null==y?void 0:y.contains(r))&&_(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[L,y,P,_]),o.useEffect(j,[l,j]),B(y,D),B(S.content,D),(0,f.jsx)(A,{scope:t,scrollbar:y,hasThumb:i,onThumbChange:(0,s.c)(c),onThumbPointerUp:(0,s.c)(d),onThumbPositionChange:j,onThumbPointerDown:(0,s.c)(u),children:(0,f.jsx)(n.sG.div,{...m,ref:C,style:{position:"absolute",...m.style},onPointerDown:(0,p.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),T.current=y.getBoundingClientRect(),R.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",S.viewport&&(S.viewport.style.scrollBehavior="auto"),k(e))}),onPointerMove:(0,p.m)(e.onPointerMove,k),onPointerUp:(0,p.m)(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=R.current,S.viewport&&(S.viewport.style.scrollBehavior=""),T.current=null})})})}),k="ScrollAreaThumb",N=o.forwardRef((e,r)=>{let{forceMount:t,...o}=e,n=j(k,e.__scopeScrollArea);return(0,f.jsx)(l.C,{present:t||n.hasThumb,children:(0,f.jsx)(H,{ref:r,...o})})}),H=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:l,...i}=e,s=g(k,t),c=j(k,t),{onThumbPositionChange:d}=c,u=(0,a.s)(r,e=>c.onThumbChange(e)),h=o.useRef(void 0),v=F(()=>{h.current&&(h.current(),h.current=void 0)},100);return o.useEffect(()=>{let e=s.viewport;if(e){let r=()=>{v(),h.current||(h.current=V(e,d),d())};return d(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[s.viewport,v,d]),(0,f.jsx)(n.sG.div,{"data-state":c.hasThumb?"visible":"hidden",...i,ref:u,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:(0,p.m)(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,o=e.clientY-r.top;c.onThumbPointerDown({x:t,y:o})}),onPointerUp:(0,p.m)(e.onPointerUp,c.onThumbPointerUp)})});N.displayName=k;var z="ScrollAreaCorner",W=o.forwardRef((e,r)=>{let t=g(z,e.__scopeScrollArea),o=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&o?(0,f.jsx)(I,{...e,ref:r}):null});W.displayName=z;var I=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,...l}=e,i=g(z,t),[a,s]=o.useState(0),[c,d]=o.useState(0),u=!!(a&&c);return B(i.scrollbarX,()=>{var e;let r=(null==(e=i.scrollbarX)?void 0:e.offsetHeight)||0;i.onCornerHeightChange(r),d(r)}),B(i.scrollbarY,()=>{var e;let r=(null==(e=i.scrollbarY)?void 0:e.offsetWidth)||0;i.onCornerWidthChange(r),s(r)}),u?(0,f.jsx)(n.sG.div,{...l,ref:r,style:{width:a,height:c,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function O(e){return e?parseInt(e,10):0}function M(e,r){let t=e/r;return isNaN(t)?0:t}function X(e){let r=M(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function Y(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",o=X(r),n=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,l=r.scrollbar.size-n,i=r.content-r.viewport,a=(0,u.q)(e,"ltr"===t?[0,i]:[-1*i,0]);return U([0,i],[0,l-o])(a)}function U(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let o=(r[1]-r[0])/(e[1]-e[0]);return r[0]+o*(t-e[0])}}var V=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},t={left:e.scrollLeft,top:e.scrollTop},o=0;return!function n(){let l={left:e.scrollLeft,top:e.scrollTop},i=t.left!==l.left,a=t.top!==l.top;(i||a)&&r(),t=l,o=window.requestAnimationFrame(n)}(),()=>window.cancelAnimationFrame(o)};function F(e,r){let t=(0,s.c)(e),n=o.useRef(0);return o.useEffect(()=>()=>window.clearTimeout(n.current),[]),o.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(t,r)},[t,r])}function B(e,r){let t=(0,s.c)(r);(0,d.N)(()=>{let r=0;if(e){let o=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return o.observe(e),()=>{window.cancelAnimationFrame(r),o.unobserve(e)}}},[e,t])}var G=m,q=y,K=W},50594:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},77223:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(40157).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}}]);