"use strict";exports.id=9922,exports.ids=[9922],exports.modules={15795:(e,s,a)=>{function l(e){switch(e){case"In_Progress":return"In Progress";case"No_details":return"No Details";default:return e}}function t(e){if(e.fullName?.trim())return e.fullName.trim();if(e.name?.trim()){let s=e.name.trim();if(["office_staff","service_advisor","administrator","mechanic","driver","manager","technician","other"].includes(s.toLowerCase())||s.includes("_")){let e=s.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${e} (Role)`}return s}if(e.role){let s=e.role.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase());return`${s} (Role)`}return"Unknown Employee"}function r(e){return e.replaceAll("_"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())}function i(e){return e.replaceAll("_"," ")}a.d(s,{DV:()=>t,fZ:()=>l,s:()=>r,vq:()=>i})},52027:(e,s,a)=>{a.d(s,{gO:()=>x,jt:()=>u,pp:()=>f});var l=a(60687),t=a(72963),r=a(11516);a(43210);var i=a(68752),n=a(91821),c=a(85726),d=a(22482);let o={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},m={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function x({children:e,className:s,data:a,emptyComponent:t,error:r,errorComponent:i,isLoading:n,loadingComponent:c,onRetry:o}){return n?c||(0,l.jsx)(p,{...s&&{className:s},text:"Loading..."}):r?i||(0,l.jsx)(h,{...s&&{className:s},message:r,...o&&{onRetry:o}}):!a||Array.isArray(a)&&0===a.length?t||(0,l.jsx)("div",{className:(0,d.cn)("text-center py-8",s),children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,l.jsx)("div",{className:s,children:e(a)})}function f({className:e,description:s,icon:a,primaryAction:t,secondaryAction:r,title:n}){return(0,l.jsxs)("div",{className:(0,d.cn)("space-y-6 text-center py-12",e),children:[a&&(0,l.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,l.jsx)(a,{className:"h-10 w-10 text-muted-foreground"})}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:n}),s&&(0,l.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:s})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[t&&(0,l.jsx)(i.r,{actionType:"primary",asChild:!!t.href,icon:t.icon,onClick:t.onClick,children:t.href?(0,l.jsx)("a",{href:t.href,children:t.label}):t.label}),r&&(0,l.jsx)(i.r,{actionType:"tertiary",asChild:!!r.href,icon:r.icon,onClick:r.onClick,children:r.href?(0,l.jsx)("a",{href:r.href,children:r.label}):r.label})]})]})}function h({className:e,message:s,onRetry:a}){return(0,l.jsxs)(n.Fc,{className:(0,d.cn)("my-4",e),variant:"destructive",children:[(0,l.jsx)(t.A,{className:"size-4"}),(0,l.jsx)(n.XL,{children:"Error"}),(0,l.jsx)(n.TN,{children:(0,l.jsxs)("div",{className:"mt-2",children:[(0,l.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:s}),a&&(0,l.jsx)(i.r,{actionType:"tertiary",icon:(0,l.jsx)(r.A,{className:"size-4"}),onClick:a,size:"sm",children:"Try Again"})]})})]})}function p({className:e,fullPage:s=!1,size:a="md",text:t}){return(0,l.jsx)("div",{className:(0,d.cn)("flex items-center justify-center",s&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",e),children:(0,l.jsxs)("div",{className:"flex flex-col items-center",children:[(0,l.jsx)(r.A,{className:(0,d.cn)("animate-spin text-primary",o[a])}),t&&(0,l.jsx)("span",{className:(0,d.cn)("mt-2 text-muted-foreground",m[a]),children:t})]})})}function u({className:e,count:s=1,testId:a="loading-skeleton",variant:t="default"}){return"card"===t?(0,l.jsx)("div",{className:(0,d.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",e),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,l.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,l.jsx)(c.E,{className:"aspect-[16/10] w-full"}),(0,l.jsxs)("div",{className:"p-5",children:[(0,l.jsx)(c.E,{className:"mb-1 h-7 w-3/4"}),(0,l.jsx)(c.E,{className:"mb-3 h-4 w-1/2"}),(0,l.jsx)(c.E,{className:"my-3 h-px w-full"}),(0,l.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(c.E,{className:"mr-2.5 size-5 rounded-full"}),(0,l.jsx)(c.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===t?(0,l.jsxs)("div",{className:(0,d.cn)("space-y-3",e),"data-testid":a,children:[(0,l.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,l.jsx)(c.E,{className:"h-8 flex-1"},s))}),Array(s).fill(0).map((e,s)=>(0,l.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,l.jsx)(c.E,{className:"h-6 flex-1"},s))},s))]}):"list"===t?(0,l.jsx)("div",{className:(0,d.cn)("space-y-3",e),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)(c.E,{className:"size-12 rounded-full"}),(0,l.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,l.jsx)(c.E,{className:"h-4 w-1/3"}),(0,l.jsx)(c.E,{className:"h-4 w-full"})]})]},s))}):"stats"===t?(0,l.jsx)("div",{className:(0,d.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",e),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,l.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)(c.E,{className:"h-5 w-1/3"}),(0,l.jsx)(c.E,{className:"size-5 rounded-full"})]}),(0,l.jsx)(c.E,{className:"mt-3 h-8 w-1/2"}),(0,l.jsx)(c.E,{className:"mt-2 h-4 w-2/3"})]},s))}):(0,l.jsx)("div",{className:(0,d.cn)("space-y-2",e),"data-testid":a,children:Array(s).fill(0).map((e,s)=>(0,l.jsx)(c.E,{className:"h-5 w-full"},s))})}},67146:(e,s,a)=>{a.d(s,{CG:()=>o,Fm:()=>p,Qs:()=>N,cj:()=>d,h:()=>h,qp:()=>u});var l=a(60687),t=a(26134),r=a(24224),i=a(78726),n=a(43210),c=a(22482);let d=t.bL,o=t.l9;t.bm;let m=t.ZL,x=n.forwardRef(({className:e,...s},a)=>(0,l.jsx)(t.hJ,{className:(0,c.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s,ref:a}));x.displayName=t.hJ.displayName;let f=(0,r.F)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",{defaultVariants:{side:"right"},variants:{side:{bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm",top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top"}}}),h=n.forwardRef(({children:e,className:s,side:a="right",...r},n)=>(0,l.jsxs)(m,{children:[(0,l.jsx)(x,{}),(0,l.jsxs)(t.UC,{className:(0,c.cn)(f({side:a}),s),ref:n,...r,children:[e,(0,l.jsxs)(t.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,l.jsx)(i.A,{className:"size-4"}),(0,l.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));h.displayName=t.UC.displayName;let p=({className:e,...s})=>(0,l.jsx)("div",{className:(0,c.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...s});p.displayName="SheetHeader";let u=n.forwardRef(({className:e,...s},a)=>(0,l.jsx)(t.hE,{className:(0,c.cn)("text-lg font-semibold text-foreground",e),ref:a,...s}));u.displayName=t.hE.displayName;let N=n.forwardRef(({className:e,...s},a)=>(0,l.jsx)(t.VY,{className:(0,c.cn)("text-sm text-muted-foreground",e),ref:a,...s}));N.displayName=t.VY.displayName},68752:(e,s,a)=>{a.d(s,{r:()=>d});var l=a(60687),t=a(11516),r=a(43210),i=a.n(r),n=a(29523),c=a(22482);let d=i().forwardRef(({actionType:e="primary",asChild:s=!1,children:a,className:r,disabled:i,icon:d,isLoading:o=!1,loadingText:m,...x},f)=>{let{className:h,variant:p}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[e];return(0,l.jsx)(n.$,{asChild:s,className:(0,c.cn)(h,r),disabled:o||i,ref:f,variant:p,...x,children:o?(0,l.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,l.jsx)(t.A,{className:"mr-2 size-4 animate-spin"}),m||a]}):(0,l.jsxs)("span",{className:"inline-flex items-center",children:[" ",d&&(0,l.jsx)("span",{className:"mr-2",children:d}),a]})})});d.displayName="ActionButton"},69981:(e,s,a)=>{a.d(s,{M:()=>d});var l=a(60687),t=a(36644),r=a(60368),i=a(85814),n=a.n(i);a(43210);var c=a(68752);function d({className:e,getReportUrl:s,href:a,isList:i=!1}){if(!a&&!s)return console.error("ViewReportButton requires either href or getReportUrl prop"),null;let d=i?"View List Report":"View Report";return a?(0,l.jsx)(c.r,{actionType:"secondary",asChild:!0,className:e,icon:(0,l.jsx)(t.A,{className:"size-4"}),children:(0,l.jsxs)(n(),{href:a,rel:"noopener noreferrer",target:"_blank",children:[d,(0,l.jsx)(r.A,{"aria-hidden":"true",className:"ml-1.5 inline-block size-3"}),(0,l.jsx)("span",{className:"sr-only",children:"(opens in new tab)"})]})}):(0,l.jsxs)(c.r,{actionType:"secondary",className:e,icon:(0,l.jsx)(t.A,{className:"size-4"}),onClick:()=>{if(s){let e=s();window.open(e,"_blank","noopener,noreferrer")}},children:[d,(0,l.jsx)(r.A,{"aria-hidden":"true",className:"ml-1.5 inline-block size-3"})]})}},85726:(e,s,a)=>{a.d(s,{E:()=>r});var l=a(60687),t=a(22482);function r({className:e,...s}){return(0,l.jsx)("div",{className:(0,t.cn)("animate-pulse rounded-md bg-muted",e),...s})}}};