module.exports = {

"[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthContext": (()=>AuthContext),
    "AuthProvider": (()=>AuthProvider),
    "useAuthContext": (()=>useAuthContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)"); // Import useRef, useCallback, useState
// Remove circular dependency - auth logic will be moved here
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/security/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/SessionManager.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/TokenRefreshService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
// Define a key for cross-tab logout events
const LOGOUT_EVENT_KEY = 'workhub-logout-event';
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    // State management for authentication
    const [session, setSession] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Helper function to transform Supabase user to our User interface
    const transformUser = (supabaseUser)=>({
            app_metadata: supabaseUser.app_metadata,
            created_at: supabaseUser.created_at,
            email: supabaseUser.email,
            email_confirmed_at: supabaseUser.email_confirmed_at,
            id: supabaseUser.id,
            is_anonymous: supabaseUser.is_anonymous,
            is_sso_user: supabaseUser.app_metadata?.provider !== 'email',
            last_sign_in_at: supabaseUser.last_sign_in_at,
            updated_at: supabaseUser.updated_at,
            user_metadata: supabaseUser.user_metadata
        });
    // Initialize Supabase auth state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        let mounted = true;
        // Get initial session with circuit breaker integration
        const initializeAuth = async ()=>{
            // Initialize circuit breaker first
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].initializeCircuitBreaker();
            // Circuit breaker check for auth initialization
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
                console.debug('🔒 Auth initialization blocked by circuit breaker');
                if (mounted) {
                    setLoading(false);
                    setIsInitialized(true);
                    setError('Authentication system temporarily unavailable');
                }
                return;
            }
            const operationId = 'auth-initialization';
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
                console.debug('🔄 Auth initialization already in progress');
                return;
            }
            try {
                // First, get the initial session without integrity check to avoid race condition
                const { data: { session: initialSession }, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.getSession();
                if (error) {
                    console.error('Error getting initial session:', error);
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                    setError(error.message);
                } else if (mounted) {
                    console.log('✅ Auth initialization successful');
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecuritySuccess();
                    setSession(initialSession);
                    setUser(initialSession?.user ? transformUser(initialSession.user) : null);
                    // Update session manager with successful authentication
                    if (initialSession) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].updateActivity();
                        // Perform session integrity check AFTER authentication is established
                        setTimeout(async ()=>{
                            try {
                                const integrityCheck = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].performIntegrityCheck();
                                if (integrityCheck) {
                                    console.log('✅ Session integrity check passed after auth initialization');
                                } else {
                                    console.log('📊 Session integrity check failed - automatic recovery will handle this');
                                    const recovered = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].recoverFromCorruptedState();
                                    if (!recovered) {
                                        console.warn('⚠️ Session recovery completed with warnings after auth initialization');
                                    }
                                }
                            } catch (error) {
                                console.warn('Session integrity check error:', error);
                            // Don't treat this as a critical error during initialization
                            }
                        }, 1000); // Increased delay to allow cookies to be properly set
                    }
                }
            } catch (error_) {
                console.error('Error initializing auth:', error_);
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                if (mounted) {
                    setError('Failed to initialize authentication');
                }
            } finally{
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
                if (mounted) {
                    setLoading(false);
                    setIsInitialized(true);
                }
            }
        };
        initializeAuth();
        // Listen for auth changes
        const { data: { subscription } } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.onAuthStateChange(async (event, session)=>{
            if (!mounted) return;
            console.log('Auth state changed:', event, session?.user?.email);
            setSession(session);
            setUser(session?.user ? transformUser(session.user) : null);
            setLoading(false);
            setIsInitialized(true);
            if (event === 'SIGNED_OUT') {
                setError(null);
            }
        });
        return ()=>{
            mounted = false;
            subscription.unsubscribe();
        };
    }, []);
    // Get user role from metadata
    const getUserRole = (user)=>{
        if (!user) return null;
        return user.user_metadata?.role || user.app_metadata?.role || 'USER';
    };
    // Auth actions
    const signIn = async (email, password)=>{
        try {
            setLoading(true);
            setError(null);
            const { data, error: signInError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.signInWithPassword({
                email,
                password
            });
            if (signInError) {
                setError(signInError.message);
                return {
                    error: signInError.message
                };
            }
            return {};
        } catch (error_) {
            const errorMessage = error_ instanceof Error ? error_.message : 'An unexpected error occurred';
            setError(errorMessage);
            return {
                error: errorMessage
            };
        } finally{
            setLoading(false);
        }
    };
    const signOut = async ()=>{
        // Circuit breaker check for sign out
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
            console.debug('🔒 Sign out blocked by circuit breaker');
            return;
        }
        const operationId = 'auth-signout';
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
            console.debug('🔄 Sign out already in progress');
            return;
        }
        try {
            setLoading(true);
            console.log('🔐 Starting sign out process...');
            // Clear session state first
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$SessionManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SessionManager"].clearSessionState();
            // Clear all secure storage
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].clearAllCookies();
            const { error: signOutError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supabase"].auth.signOut();
            if (signOutError) {
                console.error('❌ Supabase sign out error:', signOutError);
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                setError(signOutError.message);
            } else {
                console.log('✅ Sign out successful');
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecuritySuccess();
                // Clear any remaining authentication state
                setSession(null);
                setUser(null);
                setError(null);
            }
        } catch (error_) {
            console.error('Sign out error:', error_);
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
            setError('Sign out failed');
        } finally{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
            setLoading(false);
        }
    };
    const clearError = ()=>{
        setError(null);
    };
    // Create auth object
    // SECURITY NOTE: HttpOnly Cookie Compliance
    // The access_token exposed here is for client-side validation and header construction only.
    // Actual authentication relies on HttpOnly cookies set by the backend.
    // This token should NOT be stored in localStorage or used for direct API authentication.
    const auth = {
        clearError,
        error,
        isInitialized,
        loading,
        session: session ? {
            // SECURITY: Token exposed for compatibility but HttpOnly cookies are primary auth method
            access_token: session.access_token,
            user: user ?? null
        } : null,
        signIn,
        signOut,
        user,
        userRole: getUserRole(user)
    };
    const isLoggingOutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    const tokenRefreshService = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$TokenRefreshService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTokenRefreshService"])();
    // Set up auth token provider for API clients
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const getAuthToken = ()=>auth.session?.access_token || null;
        // Set the token provider for both API client instances
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["setGlobalAuthTokenProvider"])(getAuthToken);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setFactoryAuthTokenProvider"])(getAuthToken);
    }, [
        auth.session?.access_token
    ]);
    // Handle cross-tab logout with circuit breaker protection
    const handleStorageChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((event)=>{
        if (event.key === LOGOUT_EVENT_KEY && event.newValue === 'true') {
            console.log('🔐 Cross-tab logout detected. Signing out...');
            // Circuit breaker check for cross-tab logout
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
                console.debug('🔒 Cross-tab logout blocked by circuit breaker');
                return;
            }
            if (!isLoggingOutRef.current) {
                isLoggingOutRef.current = true;
                const operationId = 'cross-tab-logout';
                if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
                    auth.signOut().finally(()=>{
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
                        isLoggingOutRef.current = false;
                        // Clear the event key to allow future logout events
                        localStorage.removeItem(LOGOUT_EVENT_KEY);
                    });
                } else {
                    console.debug('🔄 Cross-tab logout already in progress');
                    isLoggingOutRef.current = false;
                }
            }
        }
    }, [
        auth.signOut
    ]);
    // Handle critical token refresh failures with circuit breaker protection
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleCriticalRefreshFailed = ()=>{
            console.warn('🔐 Critical token refresh failure detected, signing out user');
            // Circuit breaker check for critical refresh failure
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].canPerformSecurityCheck()) {
                console.debug('🔒 Critical refresh failure handling blocked by circuit breaker');
                return;
            }
            if (!isLoggingOutRef.current) {
                isLoggingOutRef.current = true;
                const operationId = 'critical-refresh-failure';
                if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].startSecurityOperation(operationId)) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].recordSecurityAttempt();
                    auth.signOut().finally(()=>{
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["SecurityUtils"].endSecurityOperation(operationId);
                        isLoggingOutRef.current = false;
                    });
                } else {
                    console.debug('🔄 Critical refresh failure handling already in progress');
                    isLoggingOutRef.current = false;
                }
            }
        };
        tokenRefreshService.subscribe((event)=>{
            if (event === 'critical_refresh_failed') {
                handleCriticalRefreshFailed();
            }
        });
        return ()=>{
        // No direct unsubscribe method on the service, but the service is a singleton
        // and callbacks are managed by the Set. For a more robust solution,
        // TokenRefreshService would need an unsubscribe method that takes the specific callback.
        // For now, relying on the singleton nature and the fact that AuthContext is long-lived.
        };
    }, [
        auth.signOut,
        tokenRefreshService
    ]);
    // Phase 3: Token management now handled by TokenManager - no manual sync needed
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const token = auth.session?.access_token || null;
        globalThis.addEventListener('storage', handleStorageChange);
        // When this tab logs out, signal other tabs
        if (!auth.session && !auth.loading && !isLoggingOutRef.current) {
            // Only set if not already logging out to prevent loop
            localStorage.setItem(LOGOUT_EVENT_KEY, 'true');
        } else if (auth.session && localStorage.getItem(LOGOUT_EVENT_KEY) === 'true') {
            // If a session exists but a logout signal is present, clear the signal
            // This handles cases where a tab might have been open during a logout,
            // then refreshed or re-authenticated.
            localStorage.removeItem(LOGOUT_EVENT_KEY);
        }
        // Development-only authentication debugging
        if ("TURBOPACK compile-time truthy", 1) {
            console.debug('🔐 AuthProvider: Token sync signal sent', {
                authLoading: auth.loading,
                tokenAvailable: !!token,
                userEmail: auth.user?.email
            });
            if (token && auth.user) {
                console.debug('✅ Authentication ready for API calls (via httpOnly cookies)', {
                    hasToken: true,
                    userEmail: auth.user.email
                });
            } else if (!auth.loading) {
                console.warn('⚠️ No token available client-side for direct access (expected for httpOnly cookies)', {
                    hasSession: !!auth.session,
                    hasUser: !!auth.user,
                    loading: auth.loading
                });
            }
        }
    }, [
        auth.session?.access_token,
        auth.user,
        auth.loading,
        handleStorageChange
    ]); // Add handleStorageChange to dependencies
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        return ()=>{
            globalThis.removeEventListener('storage', handleStorageChange);
        };
    }, [
        handleStorageChange
    ]); // Depend on handleStorageChange
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: auth,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 469,
        columnNumber: 10
    }, this);
}
function useAuthContext() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuthContext must be used within an AuthProvider');
    }
    return context;
}
;
}}),
"[project]/src/hooks/forms/useLoginValidation.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useLoginValidation": (()=>useLoginValidation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function useLoginValidation() {
    const [errors, setErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const [isFormTouched, setIsFormTouched] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Enhanced email validation
    const validateEmail = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((email)=>{
        if (!email) {
            return 'Email address is required';
        }
        // More comprehensive email validation
        const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
        if (!emailRegex.test(email)) {
            return 'Please enter a valid email address';
        }
        return undefined;
    }, []);
    // Enhanced password validation
    const validatePassword = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((password)=>{
        if (!password) {
            return 'Password is required';
        }
        if (password.length < 6) {
            return 'Password must be at least 6 characters long';
        }
        // Additional password strength checks can be added here
        return undefined;
    }, []);
    // Validate entire form
    const validateForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((formData)=>{
        const newErrors = {};
        const emailError = validateEmail(formData.email);
        const passwordError = validatePassword(formData.password);
        if (emailError) newErrors.email = emailError;
        if (passwordError) newErrors.password = passwordError;
        setErrors(newErrors);
        return {
            isValid: Object.keys(newErrors).length === 0,
            errors: newErrors
        };
    }, [
        validateEmail,
        validatePassword
    ]);
    // Validate single field
    const validateField = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((fieldName, value)=>{
        let fieldError;
        switch(fieldName){
            case 'email':
                fieldError = validateEmail(value);
                break;
            case 'password':
                fieldError = validatePassword(value);
                break;
            default:
                return;
        }
        setErrors((prev)=>({
                ...prev,
                [fieldName]: fieldError
            }));
    }, [
        validateEmail,
        validatePassword
    ]);
    // Clear specific field error
    const clearFieldError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((fieldName)=>{
        setErrors((prev)=>{
            const newErrors = {
                ...prev
            };
            delete newErrors[fieldName];
            return newErrors;
        });
    }, []);
    // Clear all errors
    const clearAllErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setErrors({});
    }, []);
    // Mark form as touched
    const markFormTouched = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setIsFormTouched(true);
    }, []);
    // Reset validation state
    const resetValidation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setErrors({});
        setIsFormTouched(false);
    }, []);
    // Check if field has been validated successfully
    const isFieldValid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((fieldName, value)=>{
        return isFormTouched && value && !errors[fieldName];
    }, [
        errors,
        isFormTouched
    ]);
    return {
        errors,
        isFormTouched,
        validateForm,
        validateField,
        clearFieldError,
        clearAllErrors,
        markFormTouched,
        resetValidation,
        isFieldValid
    };
}
}}),
"[project]/src/hooks/ui/useTheme.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Theme management hook using Zustand AppStore
 * @module hooks/useTheme
 */ __turbopack_context__.s({
    "useTheme": (()=>useTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/appStore.ts [app-ssr] (ecmascript)");
;
;
const useTheme = ()=>{
    const currentTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.currentTheme);
    const setTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$appStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAppStore"])((state)=>state.setTheme);
    /**
   * Toggle between light and dark themes
   */ const toggleTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setTheme(currentTheme === 'light' ? 'dark' : 'light');
    }, [
        currentTheme,
        setTheme
    ]);
    /**
   * Check if current theme is dark
   */ const isDark = currentTheme === 'dark';
    /**
   * Check if current theme is light
   */ const isLight = currentTheme === 'light';
    /**
   * Set theme to light
   */ const setLightTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setTheme('light');
    }, [
        setTheme
    ]);
    /**
   * Set theme to dark
   */ const setDarkTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setTheme('dark');
    }, [
        setTheme
    ]);
    /**
   * Get theme-specific CSS classes
   */ const getThemeClasses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return {
            background: isDark ? 'bg-gray-900' : 'bg-white',
            border: isDark ? 'border-gray-700' : 'border-gray-200',
            isDark,
            isLight,
            root: currentTheme,
            text: isDark ? 'text-white' : 'text-gray-900'
        };
    }, [
        currentTheme,
        isDark,
        isLight
    ]);
    return {
        // State
        currentTheme,
        // Utilities
        getThemeClasses,
        isDark,
        isLight,
        setDarkTheme,
        setLightTheme,
        // Actions
        setTheme,
        toggleTheme
    };
};
}}),
"[project]/src/hooks/ui/useUiPreferences.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file UI preferences management hook using Zustand UiStore
 * @module hooks/useUiPreferences
 */ __turbopack_context__.s({
    "useUiPreferences": (()=>useUiPreferences)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/uiStore.ts [app-ssr] (ecmascript)");
;
;
const useUiPreferences = ()=>{
    // Font size preferences
    const fontSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.fontSize);
    const setFontSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.setFontSize);
    // Notification preferences
    const notificationsEnabled = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.notificationsEnabled);
    const toggleNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.toggleNotifications);
    // WorkHub-specific preferences
    const tableDensity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.tableDensity);
    const setTableDensity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.setTableDensity);
    const mapViewPreference = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.mapViewPreference);
    const setMapViewPreference = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.setMapViewPreference);
    const dashboardLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.dashboardLayout);
    const setDashboardLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.setDashboardLayout);
    const autoRefreshInterval = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.autoRefreshInterval);
    const setAutoRefreshInterval = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.setAutoRefreshInterval);
    /**
   * Get font size CSS class
   */ const getFontSizeClass = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        switch(fontSize){
            case 'large':
                {
                    return 'text-lg';
                }
            case 'small':
                {
                    return 'text-sm';
                }
            default:
                {
                    return 'text-base';
                }
        }
    }, [
        fontSize
    ]);
    /**
   * Get table density CSS classes
   */ const getTableDensityClasses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        switch(tableDensity){
            case 'compact':
                {
                    return {
                        cell: 'py-1 px-2',
                        row: 'h-8',
                        table: 'table-compact'
                    };
                }
            case 'spacious':
                {
                    return {
                        cell: 'py-4 px-4',
                        row: 'h-16',
                        table: 'table-spacious'
                    };
                }
            default:
                {
                    return {
                        cell: 'py-2 px-3',
                        row: 'h-12',
                        table: 'table-comfortable'
                    };
                }
        }
    }, [
        tableDensity
    ]);
    /**
   * Get dashboard layout CSS classes
   */ const getDashboardLayoutClasses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        switch(dashboardLayout){
            case 'cards':
                {
                    return 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6';
                }
            case 'grid':
                {
                    return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
                }
            case 'list':
                {
                    return 'flex flex-col space-y-4';
                }
            default:
                {
                    return 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6';
                }
        }
    }, [
        dashboardLayout
    ]);
    /**
   * Enable notifications
   */ const enableNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!notificationsEnabled) {
            toggleNotifications();
        }
    }, [
        notificationsEnabled,
        toggleNotifications
    ]);
    /**
   * Disable notifications
   */ const disableNotifications = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (notificationsEnabled) {
            toggleNotifications();
        }
    }, [
        notificationsEnabled,
        toggleNotifications
    ]);
    /**
   * Reset all preferences to defaults
   */ const resetPreferences = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        setFontSize('medium');
        setTableDensity('comfortable');
        setMapViewPreference('roadmap');
        setDashboardLayout('cards');
        setAutoRefreshInterval(30);
    // Note: We don't reset notifications as that's a user choice
    }, [
        setFontSize,
        setTableDensity,
        setMapViewPreference,
        setDashboardLayout,
        setAutoRefreshInterval
    ]);
    /**
   * Get all preferences as an object
   */ const getAllPreferences = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return {
            autoRefreshInterval,
            dashboardLayout,
            fontSize,
            mapViewPreference,
            notificationsEnabled,
            tableDensity
        };
    }, [
        fontSize,
        notificationsEnabled,
        tableDensity,
        mapViewPreference,
        dashboardLayout,
        autoRefreshInterval
    ]);
    return {
        // Auto-refresh preferences
        autoRefreshInterval,
        // Dashboard preferences
        dashboardLayout,
        disableNotifications,
        enableNotifications,
        // Font size
        fontSize,
        getAllPreferences,
        getDashboardLayoutClasses,
        getFontSizeClass,
        getTableDensityClasses,
        // Map preferences
        mapViewPreference,
        // Notifications
        notificationsEnabled,
        // Utilities
        resetPreferences,
        setAutoRefreshInterval,
        setDashboardLayout,
        setFontSize,
        setMapViewPreference,
        setTableDensity,
        // Table preferences
        tableDensity,
        toggleNotifications
    };
};
}}),
"[project]/src/hooks/ui/useModal.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Modal management hook using Zustand UiStore
 * @module hooks/useModal
 */ __turbopack_context__.s({
    "useModal": (()=>useModal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/zustand/uiStore.ts [app-ssr] (ecmascript)");
;
;
const useModal = ()=>{
    const isModalOpen = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.isModalOpen);
    const modalContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.modalContent);
    const openModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.openModal);
    const closeModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$zustand$2f$uiStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiStore"])((state)=>state.closeModal);
    /**
   * Open login modal
   */ const openLoginModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        openModal('login');
    }, [
        openModal
    ]);
    /**
   * Open signup modal
   */ const openSignupModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        openModal('signup');
    }, [
        openModal
    ]);
    /**
   * Open settings modal
   */ const openSettingsModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        openModal('settings');
    }, [
        openModal
    ]);
    /**
   * Open delegation form modal
   */ const openDelegationFormModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        openModal('delegation-form');
    }, [
        openModal
    ]);
    /**
   * Open vehicle details modal
   */ const openVehicleDetailsModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        openModal('vehicle-details');
    }, [
        openModal
    ]);
    /**
   * Open task assignment modal
   */ const openTaskAssignmentModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        openModal('task-assignment');
    }, [
        openModal
    ]);
    /**
   * Open employee profile modal
   */ const openEmployeeProfileModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        openModal('employee-profile');
    }, [
        openModal
    ]);
    /**
   * Check if a specific modal is open
   */ const isModalOfTypeOpen = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((type)=>{
        return isModalOpen && modalContent === type;
    }, [
        isModalOpen,
        modalContent
    ]);
    /**
   * Get modal-specific CSS classes
   */ const getModalClasses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return {
            backdrop: 'modal-backdrop',
            container: isModalOpen ? 'modal-container-visible' : 'modal-container-hidden',
            content: `modal-content modal-content-${modalContent || 'default'}`,
            overlay: isModalOpen ? 'modal-overlay-visible' : 'modal-overlay-hidden'
        };
    }, [
        isModalOpen,
        modalContent
    ]);
    /**
   * Get modal accessibility attributes
   */ const getModalAriaAttributes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return {
            'aria-describedby': modalContent ? `${modalContent}-modal-description` : undefined,
            'aria-hidden': !isModalOpen,
            'aria-labelledby': modalContent ? `${modalContent}-modal-title` : undefined,
            'aria-modal': isModalOpen,
            role: 'dialog'
        };
    }, [
        isModalOpen,
        modalContent
    ]);
    /**
   * Handle escape key press to close modal
   */ const handleEscapeKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((event)=>{
        if (event.key === 'Escape' && isModalOpen) {
            closeModal();
        }
    }, [
        isModalOpen,
        closeModal
    ]);
    /**
   * Handle backdrop click to close modal
   */ const handleBackdropClick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((event)=>{
        if (event.target === event.currentTarget && isModalOpen) {
            closeModal();
        }
    }, [
        isModalOpen,
        closeModal
    ]);
    /**
   * Get modal title based on content type
   */ const getModalTitle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        switch(modalContent){
            case 'delegation-form':
                {
                    return 'Create Delegation';
                }
            case 'employee-profile':
                {
                    return 'Employee Profile';
                }
            case 'login':
                {
                    return 'Sign In';
                }
            case 'settings':
                {
                    return 'Settings';
                }
            case 'signup':
                {
                    return 'Create Account';
                }
            case 'task-assignment':
                {
                    return 'Assign Task';
                }
            case 'vehicle-details':
                {
                    return 'Vehicle Details';
                }
            default:
                {
                    return 'Modal';
                }
        }
    }, [
        modalContent
    ]);
    /**
   * Check if modal content is WorkHub-specific
   */ const isWorkHubModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return [
            'delegation-form',
            'employee-profile',
            'task-assignment',
            'vehicle-details'
        ].includes(modalContent || '');
    }, [
        modalContent
    ]);
    return {
        closeModal,
        getModalAriaAttributes,
        getModalClasses,
        getModalTitle,
        handleBackdropClick,
        handleEscapeKey,
        // Utilities
        isModalOfTypeOpen,
        // State
        isModalOpen,
        isWorkHubModal: isWorkHubModal(),
        modalContent,
        openDelegationFormModal,
        openEmployeeProfileModal,
        openLoginModal,
        // Actions
        openModal,
        openSettingsModal,
        openSignupModal,
        openTaskAssignmentModal,
        openVehicleDetailsModal
    };
};
}}),
"[project]/src/hooks/utils/use-toast.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "reducer": (()=>reducer),
    "toast": (()=>toast),
    "useToast": (()=>useToast)
});
// Inspired by react-hot-toast library
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
const TOAST_LIMIT = 1;
const TOAST_REMOVE_DELAY = 1_000_000;
const actionTypes = {
    ADD_TOAST: 'ADD_TOAST',
    DISMISS_TOAST: 'DISMISS_TOAST',
    REMOVE_TOAST: 'REMOVE_TOAST',
    UPDATE_TOAST: 'UPDATE_TOAST'
};
let count = 0;
function genId() {
    count = (count + 1) % Number.MAX_SAFE_INTEGER;
    return count.toString();
}
const toastTimeouts = new Map();
const addToRemoveQueue = (toastId)=>{
    if (toastTimeouts.has(toastId)) {
        return;
    }
    const timeout = setTimeout(()=>{
        toastTimeouts.delete(toastId);
        dispatch({
            toastId: toastId,
            type: 'REMOVE_TOAST'
        });
    }, TOAST_REMOVE_DELAY);
    toastTimeouts.set(toastId, timeout);
};
const reducer = (state, action)=>{
    switch(action.type){
        case 'ADD_TOAST':
            {
                return {
                    ...state,
                    toasts: [
                        action.toast,
                        ...state.toasts
                    ].slice(0, TOAST_LIMIT)
                };
            }
        case 'DISMISS_TOAST':
            {
                const { toastId } = action;
                // ! Side effects ! - This could be extracted into a dismissToast() action,
                // but I'll keep it here for simplicity
                if (toastId) {
                    addToRemoveQueue(toastId);
                } else {
                    for (const toast of state.toasts){
                        addToRemoveQueue(toast.id);
                    }
                }
                return {
                    ...state,
                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {
                            ...t,
                            open: false
                        } : t)
                };
            }
        case 'REMOVE_TOAST':
            {
                if (action.toastId === undefined) {
                    return {
                        ...state,
                        toasts: []
                    };
                }
                return {
                    ...state,
                    toasts: state.toasts.filter((t)=>t.id !== action.toastId)
                };
            }
        case 'UPDATE_TOAST':
            {
                return {
                    ...state,
                    toasts: state.toasts.map((t)=>t.id === action.toast.id ? {
                            ...t,
                            ...action.toast
                        } : t)
                };
            }
    }
};
const listeners = [];
let memoryState = {
    toasts: []
};
function dispatch(action) {
    memoryState = reducer(memoryState, action);
    for (const listener of listeners){
        listener(memoryState);
    }
}
function toast({ ...props }) {
    const id = genId();
    const update = (props)=>dispatch({
            toast: {
                ...props,
                id
            },
            type: 'UPDATE_TOAST'
        });
    const dismiss = ()=>dispatch({
            toastId: id,
            type: 'DISMISS_TOAST'
        });
    dispatch({
        toast: {
            ...props,
            id,
            onOpenChange: (open)=>{
                if (!open) dismiss();
            },
            open: true
        },
        type: 'ADD_TOAST'
    });
    return {
        dismiss,
        id: id,
        update
    };
}
function useToast() {
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(memoryState);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        listeners.push(setState);
        return ()=>{
            const index = listeners.indexOf(setState);
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        };
    }, [
        state
    ]);
    return {
        ...state,
        dismiss: (toastId)=>dispatch({
                type: 'DISMISS_TOAST',
                ...toastId && {
                    toastId
                }
            }),
        toast
    };
}
;
}}),
"[project]/src/app/layout-client.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Client Layout Component with CSP Support
 * @module app/layout-client
 *
 * Client component containing the original layout logic with CSP nonce support.
 * Follows 2025 security standards for script loading and CSP compliance.
 */ __turbopack_context__.s({
    "default": (()=>ClientLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/auth/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$ProtectedRoute$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/auth/ProtectedRoute.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$AppLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/AppLayout.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$settings$2f$SettingsModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/settings/SettingsModal.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$theme$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/theme-provider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$NotificationDisplay$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/NotificationDisplay.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$toaster$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/toaster.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$QuickAccessFab$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/QuickAccessFab.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useUiPreferences$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/ui/useUiPreferences.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/queryClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$SecurityConfigProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/providers/SecurityConfigProvider.tsx [app-ssr] (ecmascript)");
// Debug components imports removed for cleaner UI
// import { TokenRefreshDebug } from '@/components/debug/TokenRefreshDebug';
// import { EnhancedCSPDebug } from '@/components/debug/EnhancedCSPDebug';
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSPProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/CSPProvider.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function ClientLayout({ children }) {
    const nonce = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSPProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useNonce"])();
    const reportViolation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSPProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCSPReporting"])();
    // Initialize CSP violation reporting
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$CSPProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initializeCSPViolationReporting"])(reportViolation);
    }, [
        reportViolation
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$theme$2d$provider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ThemeProvider"], {
        attribute: "class",
        defaultTheme: "system",
        disableTransitionOnChange: true,
        enableSystem: true,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
            client: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["queryClient"],
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthProvider"], {
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$providers$2f$SecurityConfigProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SecurityConfigProvider"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ProtectedLayoutWrapper, {
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/src/app/layout-client.tsx",
                            lineNumber: 69,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout-client.tsx",
                        lineNumber: 68,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 67,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$NotificationDisplay$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NotificationDisplay"], {}, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 74,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$settings$2f$SettingsModal$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SettingsModal"], {}, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 77,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$QuickAccessFab$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QuickAccessFab"], {}, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 80,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/layout-client.tsx",
            lineNumber: 66,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/layout-client.tsx",
        lineNumber: 60,
        columnNumber: 5
    }, this);
}
/**
 * Protected Layout Wrapper Component
 *
 * This component determines which routes require authentication.
 * Public routes (like auth-test) are excluded from protection.
 * Also handles initial data prefetching when authentication is ready.
 */ function ProtectedLayoutWrapper({ children }) {
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const { isInitialized, loading, user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAuthContext"])();
    // Get UI preferences from Zustand stores (but not theme to avoid hydration issues)
    const { getFontSizeClass } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$ui$2f$useUiPreferences$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUiPreferences"])();
    // Define public routes that don't require authentication
    const publicRoutes = [
        '/auth-test',
        '/supabase-diagnostics',
        '/login'
    ];
    // Define auth routes that should have no layout wrapper
    const authRoutes = [
        '/login'
    ];
    // Check if current route is public
    const isPublicRoute = publicRoutes.some((route)=>pathname?.startsWith(route));
    // Check if current route is an auth route (no layout needed)
    const isAuthRoute = authRoutes.some((route)=>pathname?.startsWith(route));
    // Determine if authentication system is ready for API calls
    // CRITICAL: Must include user check to prevent API calls before authentication
    // MEMOIZED to prevent infinite re-renders caused by user object reference changes
    const isAuthReady = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return isInitialized && !loading && !!user;
    }, [
        isInitialized,
        loading,
        user?.id
    ]); // Use user.id instead of user object to prevent reference issues
    // Trigger initial data prefetching when authentication is ready and on dashboard
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isAuthReady && pathname === '/') {
            console.log('Authentication ready, triggering dashboard data prefetch.');
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$queryClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["prefetchUtils"].prefetchDashboardData(isAuthReady).catch((error)=>{
                console.warn('Failed to prefetch dashboard data:', error);
            });
        }
    }, [
        isAuthReady,
        pathname
    ]);
    // If it's an auth route, render without any layout wrapper
    if (isAuthRoute) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                children,
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$toaster$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Toaster"], {}, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 138,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true);
    }
    // If it's a public route, render without protection but with layout
    if (isPublicRoute) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `app-layout ${getFontSizeClass()}`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$AppLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AppLayout"], {
                    children: children
                }, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 147,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$toaster$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Toaster"], {}, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 148,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
                    className: "no-print border-t border-border bg-card py-4 text-center text-sm text-card-foreground",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "© ",
                            new Date().getFullYear(),
                            " WorkHub. All rights reserved."
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/layout-client.tsx",
                        lineNumber: 150,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 149,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/layout-client.tsx",
            lineNumber: 146,
            columnNumber: 7
        }, this);
    }
    // For all other routes, require authentication
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `app-layout ${getFontSizeClass()}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$auth$2f$ProtectedRoute$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ProtectedRoute"], {
            requireEmailVerification: true,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$AppLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AppLayout"], {
                    children: children
                }, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 160,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$toaster$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Toaster"], {}, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 161,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
                    className: "no-print border-t border-border bg-card py-4 text-center text-sm text-card-foreground",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: [
                            "© ",
                            new Date().getFullYear(),
                            " WorkHub. All rights reserved."
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/layout-client.tsx",
                        lineNumber: 163,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/layout-client.tsx",
                    lineNumber: 162,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/layout-client.tsx",
            lineNumber: 159,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/layout-client.tsx",
        lineNumber: 158,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_cbd35140._.js.map