exports.id=7055,exports.ids=[7055],exports.modules={658:(e,t,n)=>{"use strict";n.d(t,{gv:()=>h,j9:()=>a});var o=n(40463),s=n(23133),c=n(65931),i=n(89513);class r{static{this.instance=null}constructor(e={}){this.connectionState="disconnected",this.reconnectAttempts=0,this.socket=null,this.stateListeners=new Set,this.subscriptions=new Map,this.config={autoConnect:e.autoConnect??!0,reconnectAttempts:e.reconnectAttempts??5,reconnectDelay:e.reconnectDelay??1e3,timeout:e.timeout??1e4,url:e.url??process.env.NEXT_PUBLIC_WEBSOCKET_URL??(0,s.Qq)().wsUrl.replace("ws://","http://").replace("wss://","https://")},this.config.autoConnect&&this.connect(),this.setupTokenRefreshHandling()}static getInstance(e){return r.instance??=new r(e),r.instance}async connect(){if(this.socket?.connected)return void console.debug("WebSocket already connected");this.setConnectionState("connecting");try{let{data:{session:e},error:t}=await c.N.auth.getSession();t&&console.warn("Failed to get session for WebSocket connection:",t);let n={forceNew:!0,timeout:this.config.timeout,transports:["websocket","polling"],withCredentials:!0};if(e?.access_token){n.auth={token:e.access_token},console.debug("\uD83D\uDD10 WebSocket connecting with authentication token");let t=e.expires_at?1e3*e.expires_at:0,o=Date.now();t-o<=6e4&&console.warn("⚠️ WebSocket token expires soon, may need refresh")}else console.warn("⚠️ WebSocket connecting without authentication token - connection may fail");this.socket=(0,o.io)(this.config.url,n),this.setupEventHandlers()}catch(e){console.error("Failed to connect WebSocket:",e),this.setConnectionState("error"),this.scheduleReconnect()}}destroy(){this.disconnect(),this.subscriptions.clear(),this.stateListeners.clear(),r.instance=null}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionState("disconnected"),this.reconnectAttempts=0}emit(e,t,n){if(!this.socket?.connected)return void console.warn(`Cannot emit ${e}:${t} - WebSocket not connected`);this.socket.emit(t,n)}getConnectionState(){return this.connectionState}isConnected(){return"connected"===this.connectionState&&this.socket?.connected===!0}joinRoom(e){if(!this.socket?.connected)return void console.warn(`Cannot join room ${e} - WebSocket not connected`);this.socket.emit("join-room",e)}leaveRoom(e){this.socket?.connected&&this.socket.emit("leave-room",e)}onStateChange(e){return this.stateListeners.add(e),()=>{this.stateListeners.delete(e)}}subscribe(e,t,n){let o=`${e}:${t}`;return this.subscriptions.has(o)||this.subscriptions.set(o,new Set),this.subscriptions.get(o).add(n),this.socket?.connected&&t&&this.socket.on(t,n),()=>{let e=this.subscriptions.get(o);e&&(e.delete(n),0===e.size&&this.subscriptions.delete(o)),this.socket&&t&&this.socket.off(t,n)}}handleAuthenticationError(){let e=(0,i.Q)();console.log("\uD83D\uDD10 Handling WebSocket authentication error..."),this.socket&&(this.socket.disconnect(),this.socket=null),e.refreshNow().then(e=>{e?console.log("\uD83D\uDD04 Token refresh successful, retrying WebSocket connection"):(console.error("\uD83D\uDD04 Token refresh failed, scheduling normal reconnect"),this.scheduleReconnect())}).catch(e=>{console.error("\uD83D\uDD04 Token refresh error:",e),this.scheduleReconnect()})}resubscribeToEvents(){if(this.socket)for(let[e,t]of this.subscriptions){let[,n]=e.split(":");for(let e of t)n&&this.socket.on(n,e)}}scheduleReconnect(){if(this.reconnectAttempts>=this.config.reconnectAttempts){console.error("Max reconnection attempts reached"),this.setConnectionState("error");return}this.setConnectionState("reconnecting"),this.reconnectAttempts++,setTimeout(()=>{console.info(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts})`),this.connect()},this.config.reconnectDelay*Math.pow(2,this.reconnectAttempts-1))}setConnectionState(e){if(this.connectionState!==e)for(let t of(this.connectionState=e,this.stateListeners))t(e)}setupEventHandlers(){this.socket&&(this.socket.on("connect",()=>{console.info("WebSocket connected"),this.setConnectionState("connected"),this.reconnectAttempts=0,this.resubscribeToEvents()}),this.socket.on("disconnect",e=>{console.warn("WebSocket disconnected:",e),this.setConnectionState("disconnected"),"io server disconnect"!==e&&this.scheduleReconnect()}),this.socket.on("connect_error",e=>{console.error("WebSocket connection error:",e),this.setConnectionState("error"),e.message?.includes("Authentication")||e.message?.includes("token")||e.message?.includes("No token provided")||e.message?.includes("Unauthorized")?(console.warn("\uD83D\uDD10 Authentication error detected, attempting token refresh"),this.handleAuthenticationError()):this.scheduleReconnect()}),this.socket.on("auth_error",e=>{console.error("\uD83D\uDD10 Server authentication error:",e),this.handleAuthenticationError()}),this.socket.on("token_refresh_required",()=>{console.warn("\uD83D\uDD04 Server requested token refresh"),this.handleAuthenticationError()}))}setupTokenRefreshHandling(){(0,i.Q)().subscribe((e,t)=>{switch(e){case"critical_refresh_failed":console.error("\uD83D\uDD04 Critical token refresh failure, disconnecting WebSocket"),this.disconnect(),this.setConnectionState("error");break;case"refresh_failed":console.error("\uD83D\uDD04 Token refresh failed, WebSocket may lose connection");break;case"refresh_success":console.log("\uD83D\uDD04 Token refreshed, reconnecting WebSocket with new token"),this.socket&&(this.socket.disconnect(),this.socket=null),setTimeout(()=>this.connect(),500)}})}}let a=e=>r.getInstance(e),h=()=>{let e=a();return{connectionState:e.getConnectionState(),isConnected:e.isConnected()}}},5991:()=>{},46349:(e,t,n)=>{"use strict";n.d(t,{GK:()=>r,ol:()=>a});var o=n(8693),s=n(43612),c=n(43210),i=n(658);function r(e,t,n,o){return h(e,t,{channel:"crud",events:[`${n}:created`,`${n}:updated`,`${n}:deleted`,`refresh:${n}`],fallbackInterval:3e4},o)}function a(e,t,n,o){return(0,i.j9)(),h(e,t,{channel:"reliability",events:[`${n}-update`,`${n}-created`,`${n}-resolved`],fallbackInterval:{alerts:3e4,"circuit-breakers":6e4,health:45e3,metrics:6e4}[n]},o)}function h(e,t,n,r){let{channel:a,enableFallback:h=!0,enableWebSocket:l=!0,events:d,fallbackInterval:u=3e4}=n,[k,f]=(0,c.useState)(!1);(0,i.j9)();let p=h&&(!l||!k),m={gcTime:6e5,queryFn:t,queryKey:e,refetchInterval:!!p&&u,refetchOnReconnect:!0,refetchOnWindowFocus:p,staleTime:3e4*!k,...r};return(0,o.jE)(),{...(0,s.I)(m),isUsingFallback:p,isWebSocketConnected:k}}},70440:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s});var o=n(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},80702:()=>{}};