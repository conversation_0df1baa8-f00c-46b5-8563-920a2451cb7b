"use strict";exports.id=9303,exports.ids=[9303],exports.modules={39303:(e,t,r)=>{r.d(t,{toFormData:()=>D});var a=r(56177),n=r(41130);let i=0,A={START_BOUNDARY:i++,HEADER_FIELD_START:i++,HEADER_FIELD:i++,HEADER_VALUE_START:i++,HEADER_VALUE:i++,HEADER_VALUE_ALMOST_DONE:i++,HEADERS_ALMOST_DONE:i++,PART_DATA_START:i++,PART_DATA:i++,END:i++},s={PART_BOUNDARY:1,LAST_BOUNDARY:2},o=e=>32|e,E=()=>{};class d{constructor(e){this.index=0,this.flags=0,this.onHeaderEnd=E,this.onHeaderField=E,this.onHeadersEnd=E,this.onHeaderValue=E,this.onPartBegin=E,this.onPartData=E,this.onPartEnd=E,this.boundaryChars={};let t=new Uint8Array((e="\r\n--"+e).length);for(let r=0;r<e.length;r++)t[r]=e.charCodeAt(r),this.boundaryChars[t[r]]=!0;this.boundary=t,this.lookbehind=new Uint8Array(this.boundary.length+8),this.state=A.START_BOUNDARY}write(e){let t,r,a=0,n=e.length,i=this.index,{lookbehind:E,boundary:d,boundaryChars:D,index:h,state:l,flags:T}=this,_=this.boundary.length,R=_-1,f=e.length,c=e=>{this[e+"Mark"]=a},u=e=>{delete this[e+"Mark"]},H=(e,t,r,a)=>{(void 0===t||t!==r)&&this[e](a&&a.subarray(t,r))},L=(t,r)=>{let n=t+"Mark";n in this&&(r?(H(t,this[n],a,e),delete this[n]):(H(t,this[n],e.length,e),this[n]=0))};for(a=0;a<n;a++)switch(t=e[a],l){case A.START_BOUNDARY:if(h===d.length-2){if(45===t)T|=s.LAST_BOUNDARY;else if(13!==t)return;h++;break}if(h-1==d.length-2){if(T&s.LAST_BOUNDARY&&45===t)l=A.END,T=0;else{if(T&s.LAST_BOUNDARY||10!==t)return;h=0,H("onPartBegin"),l=A.HEADER_FIELD_START}break}t!==d[h+2]&&(h=-2),t===d[h+2]&&h++;break;case A.HEADER_FIELD_START:l=A.HEADER_FIELD,c("onHeaderField"),h=0;case A.HEADER_FIELD:if(13===t){u("onHeaderField"),l=A.HEADERS_ALMOST_DONE;break}if(h++,45===t)break;if(58===t){if(1===h)return;L("onHeaderField",!0),l=A.HEADER_VALUE_START;break}if((r=o(t))<97||r>122)return;break;case A.HEADER_VALUE_START:if(32===t)break;c("onHeaderValue"),l=A.HEADER_VALUE;case A.HEADER_VALUE:13===t&&(L("onHeaderValue",!0),H("onHeaderEnd"),l=A.HEADER_VALUE_ALMOST_DONE);break;case A.HEADER_VALUE_ALMOST_DONE:if(10!==t)return;l=A.HEADER_FIELD_START;break;case A.HEADERS_ALMOST_DONE:if(10!==t)return;H("onHeadersEnd"),l=A.PART_DATA_START;break;case A.PART_DATA_START:l=A.PART_DATA,c("onPartData");case A.PART_DATA:if(i=h,0===h){for(a+=R;a<f&&!(e[a]in D);)a+=_;a-=R,t=e[a]}if(h<d.length)d[h]===t?(0===h&&L("onPartData",!0),h++):h=0;else if(h===d.length)h++,13===t?T|=s.PART_BOUNDARY:45===t?T|=s.LAST_BOUNDARY:h=0;else if(h-1===d.length)if(T&s.PART_BOUNDARY){if(h=0,10===t){T&=~s.PART_BOUNDARY,H("onPartEnd"),H("onPartBegin"),l=A.HEADER_FIELD_START;break}}else T&s.LAST_BOUNDARY&&45===t?(H("onPartEnd"),l=A.END,T=0):h=0;h>0?E[h-1]=t:i>0&&(H("onPartData",0,i,new Uint8Array(E.buffer,E.byteOffset,E.byteLength)),i=0,c("onPartData"),a--);break;case A.END:break;default:throw Error(`Unexpected state entered: ${l}`)}L("onHeaderField"),L("onHeaderValue"),L("onPartData"),this.index=h,this.state=l,this.flags=T}end(){if(this.state===A.HEADER_FIELD_START&&0===this.index||this.state===A.PART_DATA&&this.index===this.boundary.length)this.onPartEnd();else if(this.state!==A.END)throw Error("MultipartParser.end(): stream ended unexpectedly")}}async function D(e,t){let r,i,A,s,o,E;if(!/multipart/i.test(t))throw TypeError("Failed to fetch");let D=t.match(/boundary=(?:"([^"]+)"|([^;]+))/i);if(!D)throw TypeError("no or bad content-type header, no multipart boundary");let h=new d(D[1]||D[2]),l=[],T=new n.fS,_=e=>{A+=u.decode(e,{stream:!0})},R=e=>{l.push(e)},f=()=>{let e=new a.ZH(l,E,{type:o});T.append(s,e)},c=()=>{T.append(s,A)},u=new TextDecoder("utf-8");for await(let t of(u.decode(),h.onPartBegin=function(){h.onPartData=_,h.onPartEnd=c,r="",i="",A="",s="",o="",E=null,l.length=0},h.onHeaderField=function(e){r+=u.decode(e,{stream:!0})},h.onHeaderValue=function(e){i+=u.decode(e,{stream:!0})},h.onHeaderEnd=function(){if(i+=u.decode(),"content-disposition"===(r=r.toLowerCase())){let e=i.match(/\bname=("([^"]*)"|([^()<>@,;:\\"/[\]?={}\s\t]+))/i);e&&(s=e[2]||e[3]||""),(E=function(e){let t=e.match(/\bfilename=("(.*?)"|([^()<>@,;:\\"/[\]?={}\s\t]+))($|;\s)/i);if(!t)return;let r=t[2]||t[3]||"",a=r.slice(r.lastIndexOf("\\")+1);return(a=a.replace(/%22/g,'"')).replace(/&#(\d{4});/g,(e,t)=>String.fromCharCode(t))}(i))&&(h.onPartData=R,h.onPartEnd=f)}else"content-type"===r&&(o=i);i="",r=""},e))h.write(t);return h.end(),T}}};