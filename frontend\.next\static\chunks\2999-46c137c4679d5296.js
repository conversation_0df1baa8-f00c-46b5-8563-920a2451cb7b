"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2999],{85057:(e,s,r)=>{r.d(s,{J:()=>c});var t=r(95155),a=r(12115),i=r(40968),n=r(74466),o=r(54036);let l=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)(i.b,{ref:s,className:(0,o.cn)(l(),r),...a})});c.displayName=i.b.displayName},86719:(e,s,r)=>{r.d(s,{M:()=>a});var t=r(12115);function a(){let[e,s]=(0,t.useState)({}),[r,a]=(0,t.useState)(!1),i=(0,t.useCallback)(e=>e?/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)?void 0:"Please enter a valid email address":"Email address is required",[]),n=(0,t.useCallback)(e=>e?e.length<6?"Password must be at least 6 characters long":void 0:"Password is required",[]),o=(0,t.useCallback)(e=>{let r={},t=i(e.email),a=n(e.password);return t&&(r.email=t),a&&(r.password=a),s(r),{isValid:0===Object.keys(r).length,errors:r}},[i,n]),l=(0,t.useCallback)((e,r)=>{let t;switch(e){case"email":t=i(r);break;case"password":t=n(r);break;default:return}s(s=>({...s,[e]:t}))},[i,n]),c=(0,t.useCallback)(e=>{s(s=>{let r={...s};return delete r[e],r})},[]),d=(0,t.useCallback)(()=>{s({})},[]),m=(0,t.useCallback)(()=>{a(!0)},[]),u=(0,t.useCallback)(()=>{s({}),a(!1)},[]),x=(0,t.useCallback)((s,t)=>r&&t&&!e[s],[e,r]);return{errors:e,isFormTouched:r,validateForm:o,validateField:l,clearFieldError:c,clearAllErrors:d,markFormTouched:m,resetValidation:u,isFieldValid:x}}},92999:(e,s,r)=>{r.d(s,{LoginForm:()=>A});var t=r(95155),a=r(32087),i=r(31573),n=r(48639),o=r(11133),l=r(19637),c=r(10518),d=r(4607),m=r(17607),u=r(50172),x=r(19968),p=r(45731),h=r(12115),g=r(40283),b=r(86719),f=r(55365),v=r(30285),j=r(47262),N=r(62523),y=r(85057),w=r(54036);function k(e){let{className:s,stage:r="authenticating",message:a}=e,i=(()=>{switch(r){case"authenticating":return{icon:(0,t.jsx)(u.A,{className:"size-6 animate-spin text-primary"}),text:a||"Authenticating credentials...",description:"Verifying your identity securely"};case"verifying":return{icon:(0,t.jsx)(p.A,{className:"size-6 text-accent animate-pulse"}),text:a||"Verifying security...",description:"Checking account permissions"};case"redirecting":return{icon:(0,t.jsx)(u.A,{className:"size-6 animate-spin text-primary"}),text:a||"Preparing your dashboard...",description:"Setting up your workspace"};case"success":return{icon:(0,t.jsx)(c.A,{className:"size-6 text-green-600"}),text:a||"Welcome back!",description:"Login successful"};default:return{icon:(0,t.jsx)(u.A,{className:"size-6 animate-spin text-primary"}),text:a||"Loading...",description:"Please wait"}}})();return(0,t.jsxs)("div",{className:(0,w.cn)("flex flex-col items-center justify-center p-8 text-center space-y-4",s),children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 rounded-full bg-primary/10 animate-ping"}),(0,t.jsx)("div",{className:"relative flex items-center justify-center w-12 h-12 rounded-full bg-background border border-border/60 shadow-lg",children:i.icon})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("p",{className:"text-lg font-medium text-foreground",children:i.text}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:i.description})]}),(0,t.jsx)("div",{className:"flex space-x-2",children:[0,1,2].map(e=>(0,t.jsx)("div",{className:(0,w.cn)("w-2 h-2 rounded-full bg-primary/30 animate-pulse","transition-all duration-300"),style:{animationDelay:"".concat(.2*e,"s"),animationDuration:"1.5s"}},e))})]})}function A(e){let{onForgotPassword:s,onSignUp:r,onSuccess:w}=e,{clearError:A,error:z,loading:C,signIn:S}=(0,g.useAuthContext)(),{clearAllErrors:E,clearFieldError:F,errors:T,isFieldValid:_,markFormTouched:L,validateForm:P}=(0,b.M)(),[M,Z]=(0,h.useState)({email:"",password:"",rememberMe:!1}),[D,J]=(0,h.useState)(!1),[V,W]=(0,h.useState)("authenticating"),[H,I]=(0,h.useState)(!0),[$,q]=(0,h.useState)(null),[B,R]=(0,h.useState)(!1);(0,h.useEffect)(()=>{if(void 0!==globalThis.window&&"undefined"!=typeof navigator){I(navigator.onLine);let e=()=>I(!0),s=()=>I(!1);return globalThis.addEventListener("online",e),globalThis.addEventListener("offline",s),()=>{globalThis.removeEventListener("online",e),globalThis.removeEventListener("offline",s)}}return()=>{}},[]),(0,h.useEffect)(()=>{if(void 0!==globalThis.window&&"undefined"!=typeof localStorage){let e=localStorage.getItem("workhub_remember_email");e&&Z(s=>({...s,email:e,rememberMe:!0}))}},[]);let O=async e=>{if(e.preventDefault(),!B&&!C){if(L(),R(!0),A(),E(),!P(M).isValid||!H)return void R(!1);try{W("authenticating");let{error:e}=await S(M.email,M.password);e?R(!1):(W("success"),void 0!==globalThis.window&&"undefined"!=typeof localStorage&&(M.rememberMe?localStorage.setItem("workhub_remember_email",M.email):localStorage.removeItem("workhub_remember_email")),setTimeout(()=>{R(!1),null==w||w()},800))}catch(e){console.error("Login error:",e),R(!1)}}},Y=(e,s)=>{Z(r=>({...r,[e]:s})),"string"==typeof s&&T[e]&&F(e),z&&A()},G=e=>{q(e)},K=()=>{q(null)};return B?(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 p-4",children:(0,t.jsx)("div",{className:"w-full max-w-md",children:(0,t.jsx)("div",{className:"rounded-2xl border border-border/60 bg-card shadow-xl backdrop-blur-sm",children:(0,t.jsx)(k,{stage:V})})})}):(0,t.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gradient-to-br from-background via-background to-muted/20 p-4",children:(0,t.jsxs)("div",{className:"w-full max-w-md",children:[!H&&(0,t.jsxs)("div",{className:"mb-4 flex items-center gap-2 rounded-xl border border-destructive/20 bg-destructive/10 p-3 text-destructive",children:[(0,t.jsx)(a.A,{className:"size-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"No internet connection"})]}),(0,t.jsxs)("div",{className:"mb-8 text-center",children:[(0,t.jsx)("div",{className:"group mx-auto mb-6 flex size-16 items-center justify-center rounded-2xl bg-gradient-to-r from-primary to-accent shadow-lg ring-1 ring-primary/20 transition-all duration-300 hover:ring-primary/40",children:(0,t.jsx)(i.A,{className:"size-8 text-primary-foreground transition-transform group-hover:scale-110"})}),(0,t.jsx)("h1",{className:"mb-2 text-3xl font-bold tracking-tight text-foreground",children:"Welcome back"}),(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-muted-foreground",children:[(0,t.jsx)("span",{children:"Sign in to your WorkHub account"}),H&&(0,t.jsx)(n.A,{className:"size-4 text-green-600"})]})]}),(0,t.jsxs)("div",{className:"rounded-2xl border border-border/60 bg-card p-8 shadow-xl backdrop-blur-sm",children:[z&&(0,t.jsxs)(f.Fc,{className:"mb-6 border-destructive/20 bg-destructive/5",variant:"destructive",children:[(0,t.jsx)(o.A,{className:"size-4"}),(0,t.jsx)(f.TN,{className:"text-destructive",children:z})]}),!H&&(0,t.jsxs)(f.Fc,{className:"mb-6 border-yellow-200 bg-yellow-50",children:[(0,t.jsx)(a.A,{className:"size-4"}),(0,t.jsx)(f.TN,{children:"You're currently offline. Please check your internet connection to sign in."})]}),(0,t.jsxs)("form",{className:"space-y-6",onSubmit:O,children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(y.J,{className:"text-sm font-medium transition-colors ".concat("email"===$?"text-primary":"text-foreground"),htmlFor:"email",children:"Email address"}),(0,t.jsxs)("div",{className:"group relative",children:[(0,t.jsx)("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:(0,t.jsx)(l.A,{className:"size-5 transition-colors ".concat("email"===$?"text-primary":"text-muted-foreground")})}),(0,t.jsx)(N.p,{autoComplete:"email",className:"h-12 pl-10 transition-all duration-200 ".concat(T.email?"border-destructive/50 focus-visible:ring-destructive/20":"hover:border-primary/30 focus-visible:ring-primary/20"," ").concat(_("email",M.email)?"border-green-500/50 focus-visible:ring-green-500/20":""),disabled:C||!H||B,id:"email",onBlur:K,onChange:e=>Y("email",e.target.value),onFocus:()=>G("email"),placeholder:"Enter your email",type:"email",value:M.email}),_("email",M.email)&&(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3",children:(0,t.jsx)(c.A,{className:"size-5 text-green-500 duration-200 animate-in fade-in-0 zoom-in-95"})})]}),T.email&&(0,t.jsxs)("p",{className:"mt-1 flex items-center gap-2 text-sm text-destructive duration-200 animate-in slide-in-from-top-1",children:[(0,t.jsx)(o.A,{className:"size-4"}),T.email]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(y.J,{className:"text-sm font-medium transition-colors ".concat("password"===$?"text-primary":"text-foreground"),htmlFor:"password",children:"Password"}),(0,t.jsxs)("div",{className:"group relative",children:[(0,t.jsx)("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:(0,t.jsx)(i.A,{className:"size-5 transition-colors ".concat("password"===$?"text-primary":"text-muted-foreground")})}),(0,t.jsx)(N.p,{autoComplete:"current-password",className:"h-12 pl-10 pr-12 transition-all duration-200 ".concat(T.password?"border-destructive/50 focus-visible:ring-destructive/20":"hover:border-primary/30 focus-visible:ring-primary/20"," ").concat(_("password",M.password)?"border-green-500/50 focus-visible:ring-green-500/20":""),disabled:C||!H||B,id:"password",onBlur:K,onChange:e=>Y("password",e.target.value),onFocus:()=>G("password"),placeholder:"Enter your password",type:D?"text":"password",value:M.password}),(0,t.jsx)("button",{"aria-label":D?"Hide password":"Show password",className:"absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground transition-colors hover:text-foreground",disabled:C||B,onClick:()=>J(!D),type:"button",children:D?(0,t.jsx)(d.A,{className:"size-5"}):(0,t.jsx)(m.A,{className:"size-5"})})]}),T.password&&(0,t.jsxs)("p",{className:"mt-1 flex items-center gap-2 text-sm text-destructive duration-200 animate-in slide-in-from-top-1",children:[(0,t.jsx)(o.A,{className:"size-4"}),T.password]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.S,{checked:M.rememberMe,className:"data-[state=checked]:border-primary data-[state=checked]:bg-primary",disabled:C||B,id:"remember-me",onCheckedChange:e=>Y("rememberMe",e)}),(0,t.jsx)(y.J,{className:"cursor-pointer text-sm text-muted-foreground transition-colors hover:text-foreground",htmlFor:"remember-me",children:"Remember me"})]}),s&&(0,t.jsx)("button",{className:"text-sm font-medium text-primary transition-colors hover:text-primary/80",disabled:C||B,onClick:s,type:"button",children:"Forgot password?"})]}),(0,t.jsx)(v.$,{className:"group h-12 w-full rounded-xl bg-gradient-to-r from-primary to-accent font-semibold text-primary-foreground shadow-lg transition-all duration-200 hover:from-primary/90 hover:to-accent/90 hover:shadow-xl disabled:opacity-50",disabled:C||!H||B,type:"submit",children:C||B?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(u.A,{className:"mr-2 size-5 animate-spin"}),"Signing in..."]}):(0,t.jsxs)(t.Fragment,{children:["Sign in",(0,t.jsx)(x.A,{className:"ml-2 size-5 transition-transform group-hover:translate-x-1"})]})}),(0,t.jsxs)("div",{className:"rounded-xl border border-border/40 bg-muted/30 p-4",children:[(0,t.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"size-4 text-muted-foreground"}),(0,t.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Demo Access"})]}),(0,t.jsxs)("div",{className:"space-y-1 text-xs text-muted-foreground",children:[(0,t.jsx)("div",{className:"font-mono",children:"<EMAIL>"}),(0,t.jsx)("div",{className:"font-mono",children:"demo123"})]})]})]})]}),r&&(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,t.jsx)("button",{className:"font-medium text-primary transition-colors hover:text-primary/80",disabled:C||B,onClick:r,children:"Create one now"})]})}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)("div",{className:"inline-flex items-center gap-2 rounded-full border border-border/40 bg-card px-4 py-2 text-xs text-muted-foreground",children:[(0,t.jsx)(p.A,{className:"size-4 text-green-600"}),(0,t.jsx)("span",{children:"Protected by enterprise-grade security"})]})}),(0,t.jsxs)("footer",{className:"mt-8 text-center text-xs text-muted-foreground",children:[(0,t.jsx)("p",{children:"\xa9 2024 WorkHub. All rights reserved."}),(0,t.jsxs)("div",{className:"mt-2 space-x-4",children:[(0,t.jsx)("a",{className:"transition-colors hover:text-foreground",href:"#",children:"Terms"}),(0,t.jsx)("a",{className:"transition-colors hover:text-foreground",href:"#",children:"Privacy"}),(0,t.jsx)("a",{className:"transition-colors hover:text-foreground",href:"#",children:"Support"})]})]})]})})}}}]);