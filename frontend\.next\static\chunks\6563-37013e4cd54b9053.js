"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6563],{42366:(e,t,r)=>{r.r(t),r.d(t,{useNotifications:()=>s,useWorkHubNotifications:()=>c});var a=r(12115),i=r(96016);let s=()=>{let e=(0,i.C)(e=>e.addNotification),t=(0,i.C)(e=>e.removeNotification),r=(0,i.C)(e=>e.clearAllNotifications),s=(0,i.C)(e=>e.unreadNotificationCount),c=(0,a.useCallback)(t=>{e({message:t,type:"success"})},[e]),l=(0,a.useCallback)(t=>{e({message:t,type:"error"})},[e]),n=(0,a.useCallback)(t=>{e({message:t,type:"warning"})},[e]),o=(0,a.useCallback)(t=>{e({message:t,type:"info"})},[e]),d=(0,a.useCallback)((e,t,r)=>{e?c(t):l(r)},[c,l]),u=(0,a.useCallback)(function(r,a){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;e({message:a,type:r}),setTimeout(()=>{let e=i.C.getState().notifications.at(-1);e&&e.message===a&&t(e.id)},s)},[e,t]),h=(0,a.useCallback)(function(){var t;let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";return e({message:r,type:"info"}),null==(t=i.C.getState().notifications.at(-1))?void 0:t.id},[e]),m=(0,a.useCallback)((e,r,a)=>{t(e),r?c(a):l(a)},[t,c,l]);return{clearAllNotifications:r,removeNotification:t,showApiResult:d,showError:l,showInfo:o,showLoading:h,showSuccess:c,showTemporary:u,showWarning:n,unreadCount:s,updateLoadingNotification:m}},c=()=>{let{clearAllNotifications:e,removeNotification:t,showError:r,showInfo:c,showSuccess:l,showWarning:n,unreadCount:o}=s(),d=(0,a.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"delegation",message:e,type:"delegation-update"})},[]),u=(0,a.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),h=(0,a.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:t,showDelegationUpdate:d,showEmployeeUpdate:(0,a.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"employee",message:e,type:"employee-update"})},[]),showError:r,showInfo:c,showSuccess:l,showTaskAssigned:h,showVehicleMaintenance:u,showWarning:n,unreadCount:o}}},43326:(e,t,r)=>{r.d(t,{zc:()=>u});var a=r(95155),i=r(24371),s=r(31949),c=r(50594),l=r(12115),n=r(55365),o=r(53712),d=r(50546);function u(e){var t;let{className:r="",context:u,error:h,showToast:m=!1}=e,{showFormError:v}=(0,o.t6)();if(l.useEffect(()=>{if(h&&m){(0,d.u1)(h);let e=(0,d.iG)(h,u);v(e.message,{errorTitle:e.title})}},[h,m,v,u]),!h)return null;let p=(0,d.iG)(h,u),f=(t=p.code||"UNKNOWN_ERROR",["VALIDATION_ERROR","ASSIGNMENT_ERROR","ROLE_ERROR"].some(e=>t.includes(e))?"error":["BUSINESS_RULE_WARNING","DEPRECATION_WARNING"].some(e=>t.includes(e))?"warning":"info");return(0,a.jsxs)(n.Fc,{className:"".concat(r," ").concat(function(e){switch(e){case"error":return"border-destructive/50 text-destructive dark:border-destructive";case"warning":return"border-yellow-500/50 text-yellow-600 dark:border-yellow-500 dark:text-yellow-400";default:return"border-blue-500/50 text-blue-600 dark:border-blue-500 dark:text-blue-400"}}(f)),children:[function(e){switch(e){case"error":return(0,a.jsx)(i.A,{className:"size-4"});case"warning":return(0,a.jsx)(s.A,{className:"size-4"});default:return(0,a.jsx)(c.A,{className:"size-4"})}}(f),(0,a.jsx)(n.XL,{children:p.title}),(0,a.jsxs)(n.TN,{children:[p.message,p.code&&(0,a.jsxs)("span",{className:"mt-1 block text-xs text-muted-foreground",children:["Error Code: ",p.code]}),p.field&&(0,a.jsxs)("span",{className:"mt-1 block text-xs text-muted-foreground",children:["Field: ",p.field]})]})]})}},58824:(e,t,r)=>{r.d(t,{R:()=>N});var a=r(95155),i=r(67554),s=r(3638),c=r(34301);r(12115);var l=r(26126),n=r(66695),o=r(54036);function d(e){let{className:t,records:r,vehicleSpecific:i=!1}=e,s=r.length,c=r.reduce((e,t)=>e+(Number(t.cost)||0),0),d=r.map(e=>new Date(e.date).getTime()),h=d.length>0?new Date(Math.min(...d)):null,m=d.length>0?new Date(Math.max(...d)):null,v=Object.entries(r.reduce((e,t)=>{for(let r of t.servicePerformed)e[r]=(e[r]||0)+1;return e},{})).sort((e,t)=>t[1]-e[1]).slice(0,3),p=i&&r.length>0?Math.max(...r.map(e=>e.odometer))-Math.min(...r.map(e=>e.odometer)):0,f=!i&&r.length>0?new Set(r.map(e=>e.vehicleId)).size:0;return(0,a.jsxs)("div",{className:(0,o.cn)("mt-6 grid grid-cols-2 sm:grid-cols-3 gap-4 summary-grid",t),children:[(0,a.jsx)(u,{className:"border-primary/10",label:"Total Services",value:s}),(0,a.jsx)(u,{className:"border-primary/10",label:"Total Cost",value:"$".concat(c.toFixed(2))}),!i&&r.length>0&&(0,a.jsx)(u,{className:"border-primary/10",label:"Vehicles Serviced",value:f}),i&&r.length>0&&(0,a.jsx)(u,{className:"border-primary/10",label:"Odometer Range Covered",value:p.toLocaleString()}),r.length>0&&(0,a.jsx)(u,{className:"border-primary/10",colSpan:"col-span-2 sm:col-span-3",label:"Date Range",value:"".concat(null==h?void 0:h.toLocaleDateString()," - ").concat(null==m?void 0:m.toLocaleDateString())}),v.length>0&&(0,a.jsx)(n.Zp,{className:"col-span-2 overflow-hidden border-primary/10 sm:col-span-3",children:(0,a.jsxs)(n.Wu,{className:"p-4",children:[(0,a.jsx)("h3",{className:"mb-2 text-sm font-semibold text-card-foreground",children:"Top Services"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:v.map(e=>{let[t,r]=e;return(0,a.jsxs)(l.E,{"aria-label":"".concat(t,": ").concat(r," services"),className:"px-2 py-1 text-xs",variant:"secondary",children:[t," (",r,")"]},t)})})]})})]})}function u(e){let{className:t,colSpan:r,label:i,textColor:s="text-muted-foreground",value:c}=e;return(0,a.jsx)(n.Zp,{className:(0,o.cn)("overflow-hidden",t,r),children:(0,a.jsxs)(n.Wu,{className:"p-4 text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-semibold text-card-foreground",children:c}),(0,a.jsx)("p",{className:(0,o.cn)("text-sm",s),children:i})]})})}var h=r(30285),m=r(43326),v=r(77023),p=r(40879),f=r(98691),y=r(44956),x=r(1350);function g(e){let{className:t="",records:r,showVehicleInfo:i=!0,onDelete:s,onBulkDelete:c}=e,l=[(0,x.BZ)(),(0,x.vk)("date","Date","MMM dd, yyyy"),...i?[{accessorKey:"vehicleMake",header:(0,x.YB)("Vehicle"),cell:e=>{let{row:t}=e,r=t.original;return(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"font-medium text-sm",children:[r.vehicleMake," ",r.vehicleModel," (",r.vehicleYear,")"]}),r.licensePlate&&(0,a.jsx)("div",{className:"text-xs text-muted-foreground font-mono",children:r.licensePlate})]})}}]:[],{accessorKey:"servicePerformed",header:(0,x.YB)("Service(s)"),cell:e=>{let{row:t}=e,r=t.getValue("servicePerformed").join(", ");return(0,a.jsx)("div",{className:"max-w-xs truncate",title:r,children:r})}},{accessorKey:"odometer",header:(0,x.YB)("Odometer"),cell:e=>{let{row:t}=e,r=t.getValue("odometer");return r?r.toLocaleString():"-"}},(0,x.yX)("cost","Cost",{decimals:2,prefix:"$"}),(0,x.K)("notes","Notes",{maxLength:50,className:"max-w-xs"}),(0,x.Wy)({viewHref:e=>"/service-records/".concat(e.id),editHref:e=>"/service-records/".concat(e.id,"/edit"),...s&&{onDelete:e=>{s(e)}},showCopyId:!0,customActions:[{label:"View Vehicle",onClick:e=>{window.location.href="/vehicles/".concat(e.vehicleId)}}]})],n=[...c?[{label:"Delete Selected",icon:e=>{let{className:t}=e;return(0,a.jsx)(y.A,{className:t})},onClick:async e=>{await c(e)},variant:"destructive"}]:[]];return(0,a.jsx)(x.bQ,{data:r,columns:l,className:t,searchPlaceholder:"Search service records by service type or notes...",searchColumn:"servicePerformed",emptyMessage:"No service records found. Add your first service record to get started.",pageSize:15,enableRowSelection:!0,enableBulkActions:n.length>0,bulkActions:n,enableColumnVisibility:!0,tableClassName:"shadow-lg",headerClassName:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20",rowClassName:"hover:bg-green-50/50 dark:hover:bg-green-900/10"})}function N(e){let{className:t,error:r,isLoading:l,onRetry:o,records:u,showVehicleInfo:y=!0,vehicleSpecific:x=!1}=e,{toast:N}=(0,p.dj)(),b=(0,f.xT)(),w=async e=>{try{await b.mutateAsync({id:e.id,vehicleId:e.vehicleId}),N({title:"Deleted!",description:"Service record deleted successfully.",variant:"default"}),o()}catch(e){N({title:"Error",description:"Failed to delete service record. Please try again.",variant:"destructive"})}},j=async e=>{try{await Promise.all(e.map(e=>b.mutateAsync({id:e.id,vehicleId:e.vehicleId}))),N({title:"Deleted!",description:"".concat(e.length," service records deleted successfully."),variant:"default"}),o()}catch(e){N({title:"Error",description:"Failed to delete some service records. Please try again.",variant:"destructive"})}};return l?(0,a.jsx)("div",{className:"space-y-4","data-testid":"loading-skeleton",children:(0,a.jsx)(v.jt,{count:5,variant:"table"})}):r?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(m.zc,{context:"Loading Service Records",error:r}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)(h.$,{"aria-label":"Try loading service records again",onClick:o,size:"sm",variant:"outline",children:[(0,a.jsx)(i.A,{className:"mr-2 size-4"}),"Try Again"]})})]}):0===u.length?(0,a.jsx)(v.pp,{title:"No Service Records Found",description:x?"No service records available for this vehicle. You can log a new service record to get started.":"There are no service records matching your current filters. You can log a new service record or adjust your filters.",icon:s.A,primaryAction:{label:"Log New Service",href:"/vehicles",icon:(0,a.jsx)(c.A,{className:"size-4"})}}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(d,{records:u,vehicleSpecific:x}),(0,a.jsx)(n.Zp,{className:"card-print shadow-md",children:(0,a.jsx)(n.Wu,{className:"p-0",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsx)(g,{records:u,showVehicleInfo:y,onDelete:w,onBulkDelete:j})})})})]})}},66695:(e,t,r)=>{r.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>n,Zp:()=>c,aR:()=>l,wL:()=>u});var a=r(95155),i=r(12115),s=r(54036);let c=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,a.jsx)("div",{className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),ref:t,...i})});c.displayName="Card";let l=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,a.jsx)("div",{className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),ref:t,...i})});l.displayName="CardHeader";let n=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,a.jsx)("div",{className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),ref:t,...i})});n.displayName="CardTitle";let o=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,a.jsx)("div",{className:(0,s.cn)("text-sm text-muted-foreground",r),ref:t,...i})});o.displayName="CardDescription";let d=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,a.jsx)("div",{className:(0,s.cn)("p-6 pt-0",r),ref:t,...i})});d.displayName="CardContent";let u=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,a.jsx)("div",{className:(0,s.cn)("flex items-center p-6 pt-0",r),ref:t,...i})});u.displayName="CardFooter"},80937:(e,t,r)=>{r.d(t,{NS:()=>v,T$:()=>d,W_:()=>u,Y1:()=>h,lR:()=>m});var a=r(26715),i=r(5041),s=r(90111),c=r(42366),l=r(99605),n=r(75908);let o={all:["vehicles"],detail:e=>["vehicles",e]},d=e=>(0,s.GK)([...o.all],async()=>(await n.vehicleApiService.getAll()).data,"vehicle",{staleTime:3e5,...e}),u=(e,t)=>{var r;return(0,s.GK)([...o.detail(e)],()=>n.vehicleApiService.getById(e),"vehicle",{enabled:!!e&&(null==(r=null==t?void 0:t.enabled)||r),staleTime:3e5,...t})},h=()=>{let e=(0,a.jE)(),{showError:t,showSuccess:r}=(0,c.useNotifications)();return(0,i.n)({mutationFn:e=>{let t=l.M.toCreateRequest(e);return n.vehicleApiService.create(t)},onError:e=>{t("Failed to create vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:t=>{e.invalidateQueries({queryKey:o.all}),r('Vehicle "'.concat(t.licensePlate,'" has been created successfully!'))}})},m=()=>{let e=(0,a.jE)(),{showError:t,showSuccess:r}=(0,c.useNotifications)();return(0,i.n)({mutationFn:e=>{let{data:t,id:r}=e,a=l.M.toUpdateRequest(t);return n.vehicleApiService.update(r,a)},onError:e=>{t("Failed to update vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:t=>{e.invalidateQueries({queryKey:o.all}),e.invalidateQueries({queryKey:o.detail(t.id)}),r('Vehicle "'.concat(t.licensePlate,'" has been updated successfully!'))}})},v=()=>{let e=(0,a.jE)(),{showError:t,showSuccess:r}=(0,c.useNotifications)();return(0,i.n)({mutationFn:e=>n.vehicleApiService.delete(e),onError:e=>{t("Failed to delete vehicle: ".concat(e.message||"Unknown error occurred"))},onSuccess:(t,a)=>{e.invalidateQueries({queryKey:o.all}),e.removeQueries({queryKey:o.detail(a)}),r("Vehicle has been deleted successfully!")}})}},88240:(e,t,r)=>{r.d(t,{A:()=>d});var a=r(95155),i=r(31949),s=r(67554),c=r(12115),l=r(55365),n=r(30285);class o extends c.Component{static getDerivedStateFromError(e){return{error:e,hasError:!0}}componentDidCatch(e,t){this.setState({errorInfo:t}),console.error("Error caught by ErrorBoundary:",e),console.error("Component stack:",t.componentStack),this.props.onError&&this.props.onError(e,t)}render(){let{description:e="An unexpected error occurred.",resetLabel:t="Try Again",title:r="Something went wrong"}=this.props;if(this.state.hasError){var c;return this.props.fallback?this.props.fallback:(0,a.jsxs)(l.Fc,{className:"my-4",variant:"destructive",children:[(0,a.jsx)(i.A,{className:"mr-2 size-4"}),(0,a.jsx)(l.XL,{className:"text-lg font-semibold",children:r}),(0,a.jsxs)(l.TN,{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-2",children:(null==(c=this.state.error)?void 0:c.message)||e}),!1,(0,a.jsxs)(n.$,{className:"mt-4",onClick:this.handleRetry,size:"sm",variant:"outline",children:[(0,a.jsx)(s.A,{className:"mr-2 size-4"}),t]})]})]})}return this.props.children}constructor(e){super(e),this.handleRetry=()=>{this.setState({error:null,errorInfo:null,hasError:!1})},this.state={error:null,errorInfo:null,hasError:!1}}}let d=o},96016:(e,t,r)=>{r.d(t,{C:()=>s});var a=r(65453),i=r(46786);let s=(0,a.v)()((0,i.lt)((0,i.Zr)((e,t)=>({addNotification:t=>e(e=>({notifications:[...e.notifications,{...t,id:crypto.randomUUID(),read:!1,timestamp:new Date().toISOString()}]})),clearAllNotifications:()=>e({notifications:[]}),currentTheme:"light",markNotificationAsRead:t=>e(e=>({notifications:e.notifications.map(e=>e.id===t?{...e,read:!0}:e)})),notifications:[],removeNotification:t=>e(e=>({notifications:e.notifications.filter(e=>e.id!==t)})),setTheme:t=>{e({currentTheme:t})},sidebarOpen:!1,toggleSidebar:()=>e(e=>({sidebarOpen:!e.sidebarOpen})),unreadNotificationCount:()=>{let{notifications:e}=t();return e.filter(e=>!e.read).length}}),{name:"workhub-app-store",partialize:e=>({currentTheme:e.currentTheme})}),{name:"app-store"}))},98691:(e,t,r)=>{r.d(t,{Ln:()=>f,WV:()=>h,fs:()=>m,kI:()=>p,xH:()=>v,xT:()=>y});var a=r(26715),i=r(5041),s=r(25982),c=r(90111),l=r(72248);let n={fromApi(e){let t={cost:e.cost,createdAt:e.createdAt,date:e.date,employeeId:e.employeeId,id:e.id,notes:e.notes,odometer:e.odometer,servicePerformed:Array.isArray(e.servicePerformed)?e.servicePerformed:[],updatedAt:e.updatedAt,vehicleId:e.vehicleId};return e.vehicleMake||e.vehicleModel||e.vehicleYear?{...t,vehicleMake:e.vehicleMake||"Unknown",vehicleModel:e.vehicleModel||"Unknown",vehicleYear:e.vehicleYear||new Date().getFullYear(),licensePlate:e.licensePlate||null,employeeName:e.employeeName||null}:t},toApi:e=>e};class o extends s.v{async getById(e){return this.executeWithInfrastructure("".concat(this.endpoint,":").concat(e),async()=>{let t=await this.apiClient.get("".concat(this.endpoint,"/").concat(e));return this.transformer.fromApi?this.transformer.fromApi(t):t})}async updateRecord(e,t,r){return this.executeWithInfrastructure(null,async()=>{let{vehicleId:a,...i}=r,s=await this.apiClient.put("/vehicles/".concat(t,"/servicerecords/").concat(e),i);return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":").concat(e))),this.cache.invalidatePattern(new RegExp("^vehicles:".concat(t,":"))),this.transformer.fromApi?this.transformer.fromApi(s):s})}async deleteRecord(e,t){return this.executeWithInfrastructure(null,async()=>{await this.apiClient.delete("/vehicles/".concat(t,"/servicerecords/").concat(e)),this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":").concat(e))),this.cache.invalidatePattern(new RegExp("^vehicles:".concat(t,":")))})}async getVehicleServiceRecords(e){return this.executeWithInfrastructure("vehicles:".concat(e,":servicerecords"),async()=>await this.apiClient.get("/vehicles/".concat(e,"/servicerecords")))}async createVehicleServiceRecord(e,t){return this.executeWithInfrastructure(null,async()=>{let r=await this.apiClient.post("/vehicles/".concat(e,"/servicerecords"),t);return this.cache.invalidatePattern(new RegExp("^".concat(this.endpoint,":"))),this.cache.invalidatePattern(new RegExp("^vehicles:".concat(e,":"))),this.transformer.fromApi?this.transformer.fromApi(r):r})}async getAllEnriched(){return this.executeWithInfrastructure("".concat(this.endpoint,":enriched"),async()=>(await this.apiClient.get("".concat(this.endpoint,"/enriched"))).map(e=>this.transformer.fromApi?this.transformer.fromApi(e):e))}constructor(e,t){super(e,{cacheDuration:3e5,retryAttempts:3,circuitBreakerThreshold:5,enableMetrics:!0,...t}),this.endpoint="/servicerecords",this.transformer=n}}let d=new o(l.uE),u="serviceRecords",h=(e,t)=>{var r;return(0,c.GK)([u,e],()=>d.getById(e),"serviceRecord",{enabled:null!=(r=null==t?void 0:t.enabled)?r:!!e,staleTime:3e5})},m=e=>{var t;return(0,c.GK)([u,"allEnriched"],()=>d.getAllEnriched(),"serviceRecord",{enabled:null==(t=null==e?void 0:e.enabled)||t,staleTime:3e5})},v=(e,t)=>{var r;return(0,c.GK)([u,"forVehicle",e],()=>d.getVehicleServiceRecords(e),"serviceRecord",{enabled:null==(r=null==t?void 0:t.enabled)||r,staleTime:3e5})},p=()=>{let e=(0,a.jE)();return(0,i.n)({mutationFn:async e=>{let{vehicleId:t}=e;return d.createVehicleServiceRecord(t,e)},onSuccess:(t,r)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,"forVehicle",r.vehicleId]}),e.invalidateQueries({queryKey:["vehicle",r.vehicleId]})}})},f=()=>{let e=(0,a.jE)();return(0,i.n)({mutationFn:async e=>{let{id:t,vehicleId:r,data:a}=e;return d.updateRecord(t,r,a)},onSuccess:(t,r)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,r.id]}),e.invalidateQueries({queryKey:[u,"forVehicle",r.vehicleId]}),e.invalidateQueries({queryKey:["vehicle",r.vehicleId]})}})},y=()=>{let e=(0,a.jE)();return(0,i.n)({mutationFn:async e=>{let{id:t,vehicleId:r}=e;return d.deleteRecord(t,r)},onSuccess:(t,r)=>{e.invalidateQueries({queryKey:[u,"allEnriched"]}),e.invalidateQueries({queryKey:[u,r.id]}),e.invalidateQueries({queryKey:[u,"forVehicle"]}),e.invalidateQueries({queryKey:["vehicle"]})}})}}}]);