"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5320],{47262:(e,s,a)=>{a.d(s,{S:()=>o});var l=a(95155),t=a(76981),n=a(10518),i=a(12115),r=a(54036);let o=i.forwardRef((e,s)=>{let{className:a,...i}=e;return(0,l.jsx)(t.bL,{className:(0,r.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),ref:s,...i,children:(0,l.jsx)(t.C1,{className:(0,r.cn)("flex items-center justify-center text-current"),children:(0,l.jsx)(n.A,{className:"size-4"})})})});o.displayName=t.bL.displayName},84411:(e,s,a)=>{a.d(s,{b:()=>b});var l=a(95155),t=a(36268),n=a(11032),i=a(79556),r=a(18271),o=a(31896),d=a(965),c=a(73158),g=a(10233),h=a(12115),x=a(30285),m=a(66695),u=a(44838),p=a(62523),j=a(59409),w=a(85127),N=a(54036);function b(e){var s,a,b;let{data:f,columns:v,className:C="",onRowClick:S,searchPlaceholder:y="Search...",searchColumn:k,enableRowSelection:R=!1,enableColumnVisibility:F=!0,enableGlobalFilter:M=!0,pageSize:P=10,emptyMessage:A="No results found.",enableBulkActions:I=!1,bulkActions:z=[],tableClassName:H="",headerClassName:V="",rowClassName:G=""}=e,[$,_]=h.useState([]),[D,E]=h.useState([]),[K,L]=h.useState({}),[Q,T]=h.useState({}),[W,Z]=h.useState(""),q=(0,t.N4)({data:f,columns:v,getCoreRowModel:(0,n.HT)(),getPaginationRowModel:(0,n.kW)(),getSortedRowModel:(0,n.h5)(),getFilteredRowModel:(0,n.hM)(),onSortingChange:_,onColumnFiltersChange:E,onColumnVisibilityChange:L,onRowSelectionChange:T,onGlobalFilterChange:Z,state:{sorting:$,columnFilters:D,columnVisibility:K,rowSelection:Q,globalFilter:W},initialState:{pagination:{pageSize:P}}}),B=e=>{if(k){var s;null==(s=q.getColumn(k))||s.setFilterValue(e)}else Z(e)};return(0,l.jsxs)("div",{className:(0,N.cn)("space-y-4",C),children:[(0,l.jsxs)(m.Zp,{className:"shadow-md",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-4",children:[M&&(0,l.jsx)(p.p,{placeholder:y,value:k?null!=(b=null==(s=q.getColumn(k))?void 0:s.getFilterValue())?b:"":W,onChange:e=>B(e.target.value),className:"max-w-sm"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[I&&R&&q.getFilteredSelectedRowModel().rows.length>0&&(0,l.jsxs)(u.rI,{children:[(0,l.jsx)(u.ty,{asChild:!0,children:(0,l.jsxs)(x.$,{variant:"outline",size:"sm",children:["Actions (",q.getFilteredSelectedRowModel().rows.length,")",(0,l.jsx)(i.A,{className:"ml-2 h-4 w-4"})]})}),(0,l.jsx)(u.SQ,{align:"end",children:z.map((e,s)=>(0,l.jsxs)(u._2,{onClick:()=>e.onClick(q.getFilteredSelectedRowModel().rows.map(e=>e.original)),className:"destructive"===e.variant?"text-destructive":"",children:[e.icon&&(0,l.jsx)(e.icon,{className:"mr-2 h-4 w-4"}),e.label]},s))})]}),F&&(0,l.jsxs)(u.rI,{children:[(0,l.jsx)(u.ty,{asChild:!0,children:(0,l.jsxs)(x.$,{variant:"outline",children:[(0,l.jsx)(r.A,{className:"mr-2 h-4 w-4"}),"Columns",(0,l.jsx)(i.A,{className:"ml-2 h-4 w-4"})]})}),(0,l.jsx)(u.SQ,{align:"end",className:"w-[150px]",children:q.getAllColumns().filter(e=>e.getCanHide()).map(e=>(0,l.jsx)(u.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:s=>e.toggleVisibility(!!s),children:e.id},e.id))})]})]})]}),(0,l.jsx)(m.Wu,{className:"p-0",children:(0,l.jsx)("div",{className:(0,N.cn)("border-t",H),children:(0,l.jsxs)(w.XI,{children:[(0,l.jsx)(w.A0,{children:q.getHeaderGroups().map(e=>(0,l.jsx)(w.Hj,{className:(0,N.cn)("border-b border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800",V),children:e.headers.map(e=>(0,l.jsx)(w.nd,{className:"py-4 font-semibold text-gray-900 dark:text-white",children:e.isPlaceholder?null:(0,l.jsx)("div",{className:(0,N.cn)("flex items-center space-x-1",e.column.getCanSort()&&"cursor-pointer select-none hover:text-gray-600 dark:hover:text-gray-300"),onClick:e.column.getToggleSortingHandler(),children:(0,l.jsx)("span",{children:(0,t.Kv)(e.column.columnDef.header,e.getContext())})})},e.id))},e.id))}),(0,l.jsx)(w.BF,{children:(null==(a=q.getRowModel().rows)?void 0:a.length)?q.getRowModel().rows.map(e=>(0,l.jsx)(w.Hj,{"data-state":e.getIsSelected()&&"selected",className:(0,N.cn)("border-b border-gray-100 hover:bg-gray-50 dark:border-gray-800 dark:hover:bg-gray-800/50",S&&"cursor-pointer",e.getIsSelected()&&"bg-blue-50 dark:bg-blue-900/20",G),onClick:()=>null==S?void 0:S(e.original),children:e.getVisibleCells().map(e=>(0,l.jsx)(w.nA,{children:(0,t.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,l.jsx)(w.Hj,{children:(0,l.jsx)(w.nA,{colSpan:v.length,className:"h-24 text-center",children:A})})})]})})})]}),(0,l.jsxs)(m.Zp,{className:"flex items-center justify-between border-t p-4",children:[(0,l.jsx)("div",{className:"flex-1 text-sm text-muted-foreground",children:R&&q.getFilteredSelectedRowModel().rows.length>0?(0,l.jsxs)(l.Fragment,{children:[q.getFilteredSelectedRowModel().rows.length," of"," ",q.getFilteredRowModel().rows.length," row(s) selected."]}):(0,l.jsxs)(l.Fragment,{children:["Showing"," ",q.getState().pagination.pageIndex*q.getState().pagination.pageSize+1," ","to"," ",Math.min((q.getState().pagination.pageIndex+1)*q.getState().pagination.pageSize,q.getFilteredRowModel().rows.length)," ","of ",q.getFilteredRowModel().rows.length," entries",q.getFilteredRowModel().rows.length!==f.length&&" (filtered from ".concat(f.length," total)")]})}),(0,l.jsxs)("div",{className:"flex items-center space-x-6 lg:space-x-8",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("p",{className:"text-sm font-medium",children:"Rows per page"}),(0,l.jsxs)(j.l6,{value:"".concat(q.getState().pagination.pageSize),onValueChange:e=>{q.setPageSize(Number(e))},children:[(0,l.jsx)(j.bq,{className:"h-8 w-[70px]",children:(0,l.jsx)(j.yv,{placeholder:q.getState().pagination.pageSize})}),(0,l.jsx)(j.gC,{side:"top",children:[10,20,30,40,50].map(e=>(0,l.jsx)(j.eb,{value:"".concat(e),children:e},e))})]})]}),(0,l.jsxs)("div",{className:"flex w-[100px] items-center justify-center text-sm font-medium",children:["Page ",q.getState().pagination.pageIndex+1," of"," ",q.getPageCount()]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsxs)(x.$,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>q.setPageIndex(0),disabled:!q.getCanPreviousPage(),children:[(0,l.jsx)("span",{className:"sr-only",children:"Go to first page"}),(0,l.jsx)(o.A,{className:"h-4 w-4"})]}),(0,l.jsxs)(x.$,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>q.previousPage(),disabled:!q.getCanPreviousPage(),children:[(0,l.jsx)("span",{className:"sr-only",children:"Go to previous page"}),(0,l.jsx)(d.A,{className:"h-4 w-4"})]}),(0,l.jsxs)(x.$,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>q.nextPage(),disabled:!q.getCanNextPage(),children:[(0,l.jsx)("span",{className:"sr-only",children:"Go to next page"}),(0,l.jsx)(c.A,{className:"h-4 w-4"})]}),(0,l.jsxs)(x.$,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>q.setPageIndex(q.getPageCount()-1),disabled:!q.getCanNextPage(),children:[(0,l.jsx)("span",{className:"sr-only",children:"Go to last page"}),(0,l.jsx)(g.A,{className:"h-4 w-4"})]})]})]})]})]})}}}]);