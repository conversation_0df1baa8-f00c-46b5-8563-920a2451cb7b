/**
 * @file Session Management Service - Single Responsibility Principle (SRP)
 * @module lib/security/SessionManager
 *
 * This class handles ONLY session security operations following SRP principles.
 * It provides session timeout detection, cross-tab communication, and concurrent session management.
 *
 * SECURITY NOTE: This manages session security without handling authentication logic.
 */

import { getEnvironmentConfig } from '../config/environment';

// Define constants locally to avoid circular dependency
const SECURITY_CONSTANTS = {
  MAX_CONCURRENT_SESSIONS: 5,
  SESSION_TIMEOUT_MINUTES: 30,
} as const;

const SECURITY_EVENTS = {
  CROSS_TAB_LOGOUT: 'cross_tab_logout',
  SESSION_INVALID: 'session_invalid',
  SESSION_TIMEOUT: 'session_timeout',
  SESSION_VALIDATED: 'session_validated',
  TOKEN_REFRESH_FAILED: 'token_refresh_failed',
  TOKEN_REFRESH_SUCCESS: 'token_refresh_success',
} as const;

export interface ConcurrentSession {
  ipAddress?: string;
  lastActivity: Date;
  sessionId: string;
  startTime: Date;
  userAgent: string;
}

export interface SessionEvent {
  data?: any;
  sessionId: string;
  timestamp: Date;
  type: string;
}

export interface SessionState {
  expiresAt: Date;
  isActive: boolean;
  lastActivity: Date;
  sessionId: string;
}

/**
 * SessionManager - Single Responsibility: Session Security Operations Only
 *
 * Handles session timeout detection, cross-tab logout, and concurrent session management.
 * Does NOT handle authentication or token management.
 */
export class SessionManager {
  private static activityListeners: (() => void)[] = [];

  private static readonly BROADCAST_CHANNEL_NAME = 'workhub_session_events';
  private static broadcastChannel: BroadcastChannel | null = null;
  private static sessionCheckInterval: NodeJS.Timeout | null = null;
  private static readonly STORAGE_KEYS = {
    CONCURRENT_SESSIONS: 'workhub_concurrent_sessions',
    LAST_ACTIVITY: 'workhub_last_activity',
    SESSION_ID: 'workhub_session_id',
    SESSION_STATE: 'workhub_session_state',
  } as const;

  /**
   * Add session event listener
   * Single responsibility: Event listener management only
   */
  static addSessionEventListener(
    callback: (event: SessionEvent) => void
  ): () => void {
    if (globalThis.window === undefined) return () => {};

    const handler = (event: MessageEvent<SessionEvent>) => {
      callback(event.data);
    };

    this.broadcastChannel?.addEventListener('message', handler);

    // Return cleanup function
    return () => {
      this.broadcastChannel?.removeEventListener('message', handler);
    };
  }

  /**
   * Cleanup session management
   * Single responsibility: Cleanup only
   */
  static cleanup(): void {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
    }

    this.broadcastChannel?.close();
    this.broadcastChannel = null;

    // Remove activity listeners
    for (const cleanup of this.activityListeners) cleanup();
    this.activityListeners = [];
  }

  /**
   * Clear session state
   * Single responsibility: Session state clearing only
   */
  static clearSessionState(): void {
    if (globalThis.window === undefined) return;

    for (const key of Object.values(this.STORAGE_KEYS)) {
      localStorage.removeItem(key);
    }
  }

  /**
   * Detect and handle session state conflicts
   * Single responsibility: Conflict detection and resolution only
   */
  static detectAndResolveConflicts(): boolean {
    if (globalThis.window === undefined) return true;

    try {
      const sessionState = this.getSessionState();
      const lastActivity = this.getLastActivity();
      const concurrentSessions = this.getConcurrentSessions();

      // Check for timestamp conflicts
      if (sessionState && lastActivity) {
        const stateDiff = Math.abs(
          sessionState.lastActivity.getTime() - lastActivity.getTime()
        );

        // If timestamps differ by more than 5 minutes, there's a conflict
        if (stateDiff > 5 * 60 * 1000) {
          console.warn('⚠️ Session timestamp conflict detected, resolving...');

          // Use the more recent timestamp (proper Date comparison)
          const recentTime =
            sessionState.lastActivity.getTime() > lastActivity.getTime()
              ? sessionState.lastActivity
              : lastActivity;

          // Update both to the more recent time
          localStorage.setItem(
            this.STORAGE_KEYS.LAST_ACTIVITY,
            recentTime.toISOString()
          );
          this.setSessionState({
            ...sessionState,
            lastActivity: recentTime,
          });

          console.log('✅ Session timestamp conflict resolved');
        }
      }

      // Check for duplicate sessions
      const currentSessionId = this.getCurrentSessionId();
      const duplicates = concurrentSessions.filter(
        s => s.sessionId === currentSessionId
      );

      if (duplicates.length > 1) {
        console.warn('⚠️ Duplicate session entries detected, cleaning up...');

        // Keep only the most recent one
        const mostRecent = duplicates.reduce((latest, current) =>
          current.lastActivity > latest.lastActivity ? current : latest
        );

        const cleanedSessions = concurrentSessions.filter(
          s => s.sessionId !== currentSessionId
        );
        cleanedSessions.push(mostRecent);

        this.setConcurrentSessions(cleanedSessions);
        console.log('✅ Duplicate sessions cleaned up');
      }

      return true;
    } catch (error) {
      console.error('Failed to detect and resolve conflicts:', error);
      return false;
    }
  }

  /**
   * Detect session timeout
   * Single responsibility: Timeout detection only
   */
  static detectTimeout(): boolean {
    if (globalThis.window === undefined) return false;

    const lastActivity = this.getLastActivity();
    if (!lastActivity) return true;

    const timeoutThreshold =
      SECURITY_CONSTANTS.SESSION_TIMEOUT_MINUTES * 60 * 1000;
    const timeSinceActivity = Date.now() - lastActivity.getTime();

    return timeSinceActivity > timeoutThreshold;
  }

  /**
   * Get current session ID
   * Single responsibility: Session ID retrieval only
   */
  static getCurrentSessionId(): string {
    if (globalThis.window === undefined) return '';

    let sessionId = localStorage.getItem(this.STORAGE_KEYS.SESSION_ID);
    if (!sessionId) {
      sessionId = this.generateSessionId();
      localStorage.setItem(this.STORAGE_KEYS.SESSION_ID, sessionId);
    }
    return sessionId;
  }

  /**
   * Get current session state
   * Single responsibility: Session state retrieval only
   */
  static getSessionState(): null | SessionState {
    if (globalThis.window === undefined) return null;

    try {
      const stateJson = localStorage.getItem(this.STORAGE_KEYS.SESSION_STATE);
      if (!stateJson) return null;

      const state = JSON.parse(stateJson);
      return {
        ...state,
        expiresAt: new Date(state.expiresAt),
        lastActivity: new Date(state.lastActivity),
      };
    } catch {
      return null;
    }
  }

  /**
   * Handle cross-tab logout
   * Single responsibility: Cross-tab communication only
   */
  static handleCrossTabLogout(): void {
    if (globalThis.window === undefined) return;

    const sessionEvent: SessionEvent = {
      sessionId: this.getCurrentSessionId(),
      timestamp: new Date(),
      type: SECURITY_EVENTS.CROSS_TAB_LOGOUT,
    };

    this.broadcastSessionEvent(sessionEvent);
    this.clearSessionState();
  }

  /**
   * Handle session validation events from TokenRefreshService
   * Single responsibility: Session validation event handling only
   */
  static handleSessionValidation(isValid: boolean, data?: any): void {
    if (globalThis.window === undefined) return;

    const sessionEvent: SessionEvent = {
      data,
      sessionId: this.getCurrentSessionId(),
      timestamp: new Date(),
      type: isValid
        ? SECURITY_EVENTS.SESSION_VALIDATED
        : SECURITY_EVENTS.SESSION_INVALID,
    };

    this.broadcastSessionEvent(sessionEvent);

    if (isValid) {
      // If session is valid, update activity
      this.updateActivity();
    } else {
      // If session is invalid, clear session state
      this.clearSessionState();
    }
  }

  /**
   * Handle token refresh events from TokenRefreshService
   * Single responsibility: Token refresh event handling only
   */
  static handleTokenRefresh(success: boolean, data?: any): void {
    if (globalThis.window === undefined) return;

    const sessionEvent: SessionEvent = {
      data,
      sessionId: this.getCurrentSessionId(),
      timestamp: new Date(),
      type: success
        ? SECURITY_EVENTS.TOKEN_REFRESH_SUCCESS
        : SECURITY_EVENTS.TOKEN_REFRESH_FAILED,
    };

    this.broadcastSessionEvent(sessionEvent);

    if (success) {
      // If token refresh succeeded, update activity and session state
      this.updateActivity();
      const currentState = this.getSessionState();
      if (currentState) {
        // Extend session expiration
        const now = new Date();
        const expiresAt = new Date(
          now.getTime() + SECURITY_CONSTANTS.SESSION_TIMEOUT_MINUTES * 60 * 1000
        );
        this.setSessionState({
          ...currentState,
          expiresAt,
          lastActivity: now,
        });
      }
    }
  }

  /**
   * Initialize session management
   * Single responsibility: Session initialization only
   */
  static initialize(): void {
    if (globalThis.window === undefined) return;

    // Initialize broadcast channel for cross-tab communication
    this.initializeBroadcastChannel();

    // Start session monitoring
    this.startSessionMonitoring();

    // Setup activity tracking
    this.setupActivityTracking();

    // Initialize session state
    this.initializeSessionState();
  }

  /**
   * Manage concurrent sessions
   * Single responsibility: Concurrent session tracking only
   */
  static manageConcurrentSessions(): void {
    if (globalThis.window === undefined) return;

    const currentSessions = this.getConcurrentSessions();
    const currentSessionId = this.getCurrentSessionId();

    // Add current session if not exists
    if (!currentSessions.find(s => s.sessionId === currentSessionId)) {
      const newSession: ConcurrentSession = {
        lastActivity: new Date(),
        sessionId: currentSessionId,
        startTime: new Date(),
        userAgent: navigator.userAgent,
      };

      currentSessions.push(newSession);
    }

    // Update last activity for current session
    const currentSession = currentSessions.find(
      s => s.sessionId === currentSessionId
    );
    if (currentSession) {
      currentSession.lastActivity = new Date();
    }

    // Remove expired sessions
    const validSessions = currentSessions.filter(session => {
      const timeoutThreshold =
        SECURITY_CONSTANTS.SESSION_TIMEOUT_MINUTES * 60 * 1000;
      const timeSinceActivity = Date.now() - session.lastActivity.getTime();
      return timeSinceActivity <= timeoutThreshold;
    });

    // Enforce maximum concurrent sessions
    if (validSessions.length > SECURITY_CONSTANTS.MAX_CONCURRENT_SESSIONS) {
      // Keep most recent sessions
      validSessions.sort(
        (a, b) => b.lastActivity.getTime() - a.lastActivity.getTime()
      );
      validSessions.splice(SECURITY_CONSTANTS.MAX_CONCURRENT_SESSIONS);
    }

    this.setConcurrentSessions(validSessions);
  }

  /**
   * Perform comprehensive session integrity check
   * Single responsibility: Session integrity validation only
   */
  static async performIntegrityCheck(): Promise<boolean> {
    if (globalThis.window === undefined) return true;

    try {
      // 1. Check local session consistency
      const isConsistent = this.validateSessionConsistency();
      if (!isConsistent) {
        console.warn('📊 Local session state is inconsistent');
        return false;
      }

      // 2. Validate against backend (lightweight check)
      const backendValid = await this.validateWithBackend();
      if (!backendValid) {
        console.warn('🔗 Backend session validation failed');
        return false;
      }

      // 3. Check for stale concurrent sessions
      this.cleanupStaleSessions();

      console.log('✅ Session integrity check passed');
      return true;
    } catch (error) {
      console.error('❌ Session integrity check failed:', error);
      return false;
    }
  }

  // State Coordination and Validation Methods

  /**
   * Recover from corrupted session state
   * Single responsibility: Session state recovery only
   */
  static recoverFromCorruptedState(): boolean {
    if (globalThis.window === undefined) return true;

    try {
      console.log('🔧 Attempting session state recovery...');

      // 1. Validate current state
      const isConsistent = this.validateSessionConsistency();
      if (isConsistent) {
        console.log('✅ Session state is already consistent');
        return true;
      }

      // 2. Perform selective cleanup
      const preservedData = this.preserveNonSecurityData();

      // 3. Clear corrupted session data
      this.clearSessionState();

      // 4. Restore preserved data
      this.restorePreservedData(preservedData);

      // 5. Re-initialize session state
      this.initializeSessionState();

      console.log('✅ Session state recovery completed');
      return true;
    } catch (error) {
      console.error('❌ Session state recovery failed:', error);
      return false;
    }
  }

  /**
   * Set session state
   * Single responsibility: Session state storage only
   */
  static setSessionState(state: SessionState): void {
    if (globalThis.window === undefined) return;

    try {
      localStorage.setItem(
        this.STORAGE_KEYS.SESSION_STATE,
        JSON.stringify(state)
      );
    } catch (error) {
      console.error('Failed to set session state:', error);
    }
  }

  /**
   * Update activity timestamp
   * Single responsibility: Activity tracking only
   */
  static updateActivity(): void {
    if (globalThis.window === undefined) return;

    const now = new Date();
    localStorage.setItem(this.STORAGE_KEYS.LAST_ACTIVITY, now.toISOString());

    // Update concurrent sessions
    this.manageConcurrentSessions();
  }

  /**
   * Validate session state consistency
   * Single responsibility: Session state validation only
   */
  static validateSessionConsistency(): boolean {
    if (globalThis.window === undefined) return true;

    try {
      const sessionState = this.getSessionState();
      const lastActivity = this.getLastActivity();
      const sessionId = this.getCurrentSessionId();

      // Check for missing or inconsistent data
      if (sessionState && !lastActivity) {
        console.warn('🔍 Session state exists but no last activity found');
        return false;
      }

      if (lastActivity && !sessionId) {
        console.warn('🔍 Last activity exists but no session ID found');
        return false;
      }

      if (sessionState && sessionState.sessionId !== sessionId) {
        console.warn('🔍 Session state ID mismatch with current session ID');
        return false;
      }

      // Check for expired session state
      if (sessionState && sessionState.expiresAt < new Date()) {
        console.warn('🔍 Session state has expired');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to validate session consistency:', error);
      return false;
    }
  }

  // Private helper methods for state coordination

  private static broadcastSessionEvent(event: SessionEvent): void {
    this.broadcastChannel?.postMessage(event);
  }

  /**
   * Clean up stale concurrent sessions
   * Single responsibility: Stale session cleanup only
   */
  private static cleanupStaleSessions(): void {
    try {
      const sessions = this.getConcurrentSessions();
      const now = Date.now();
      const timeoutThreshold =
        SECURITY_CONSTANTS.SESSION_TIMEOUT_MINUTES * 60 * 1000;

      const activeSessions = sessions.filter(session => {
        const timeSinceActivity = now - session.lastActivity.getTime();
        return timeSinceActivity <= timeoutThreshold;
      });

      if (activeSessions.length !== sessions.length) {
        console.log(
          `🧹 Cleaned up ${sessions.length - activeSessions.length} stale sessions`
        );
        this.setConcurrentSessions(activeSessions);
      }
    } catch (error) {
      console.warn('Failed to cleanup stale sessions:', error);
    }
  }

  private static generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;
  }

  private static getConcurrentSessions(): ConcurrentSession[] {
    try {
      const sessionsJson = localStorage.getItem(
        this.STORAGE_KEYS.CONCURRENT_SESSIONS
      );
      if (!sessionsJson) return [];

      const sessions = JSON.parse(sessionsJson);
      return sessions.map((s: any) => ({
        ...s,
        lastActivity: new Date(s.lastActivity),
        startTime: new Date(s.startTime),
      }));
    } catch {
      return [];
    }
  }

  // Existing private helper methods

  private static getLastActivity(): Date | null {
    const activityString = localStorage.getItem(
      this.STORAGE_KEYS.LAST_ACTIVITY
    );
    return activityString ? new Date(activityString) : null;
  }

  private static handleSessionTimeout(): void {
    const sessionEvent: SessionEvent = {
      sessionId: this.getCurrentSessionId(),
      timestamp: new Date(),
      type: SECURITY_EVENTS.SESSION_TIMEOUT,
    };

    this.broadcastSessionEvent(sessionEvent);
    this.clearSessionState();
  }

  private static initializeBroadcastChannel(): void {
    if (typeof BroadcastChannel !== 'undefined') {
      this.broadcastChannel = new BroadcastChannel(this.BROADCAST_CHANNEL_NAME);
    }
  }

  private static initializeSessionState(): void {
    const sessionId = this.getCurrentSessionId();
    const now = new Date();
    const expiresAt = new Date(
      now.getTime() + SECURITY_CONSTANTS.SESSION_TIMEOUT_MINUTES * 60 * 1000
    );

    const initialState: SessionState = {
      expiresAt,
      isActive: true,
      lastActivity: now,
      sessionId,
    };

    this.setSessionState(initialState);
    this.updateActivity();
  }

  /**
   * Preserve non-security data during recovery
   * Single responsibility: Data preservation only
   */
  private static preserveNonSecurityData(): Record<string, null | string> {
    const preservedKeys = [
      'workhub-app-store', // Theme and UI preferences
      'workhub_user_preferences', // User settings
      // Add other non-security keys as needed
    ];

    const preserved: Record<string, null | string> = {};

    for (const key of preservedKeys) {
      try {
        preserved[key] = localStorage.getItem(key);
      } catch (error) {
        console.warn(`Failed to preserve data for key ${key}:`, error);
      }
    }

    return preserved;
  }

  /**
   * Restore preserved data after recovery
   * Single responsibility: Data restoration only
   */
  private static restorePreservedData(
    preservedData: Record<string, null | string>
  ): void {
    for (const [key, value] of Object.entries(preservedData)) {
      if (value !== null) {
        try {
          localStorage.setItem(key, value);
        } catch (error) {
          console.warn(`Failed to restore data for key ${key}:`, error);
        }
      }
    }
  }

  private static setConcurrentSessions(sessions: ConcurrentSession[]): void {
    try {
      localStorage.setItem(
        this.STORAGE_KEYS.CONCURRENT_SESSIONS,
        JSON.stringify(sessions)
      );
    } catch (error) {
      console.error('Failed to set concurrent sessions:', error);
    }
  }

  private static setupActivityTracking(): void {
    const events = [
      'mousedown',
      'mousemove',
      'keypress',
      'scroll',
      'touchstart',
      'click',
    ];

    const activityHandler = () => {
      this.updateActivity();
    };

    for (const event of events) {
      document.addEventListener(event, activityHandler, { passive: true });

      // Store cleanup function
      this.activityListeners.push(() => {
        document.removeEventListener(event, activityHandler);
      });
    }
  }

  private static startSessionMonitoring(): void {
    this.sessionCheckInterval = setInterval(() => {
      if (this.detectTimeout()) {
        this.handleSessionTimeout();
      }
    }, 60_000); // Check every minute
  }

  /**
   * Validate session with backend (lightweight check)
   * Single responsibility: Backend validation only
   */
  private static async validateWithBackend(): Promise<boolean> {
    try {
      const envConfig = getEnvironmentConfig();
      const backendUrl = envConfig.apiBaseUrl;

      // Add a small delay to allow authentication cookies to be set
      await new Promise(resolve => setTimeout(resolve, 200));

      // Backend connectivity check - validate that backend is accessible
      // This is a lightweight check that doesn't require authentication

      // Use health endpoint for lightweight backend connectivity check
      // This validates that the backend is reachable without requiring authentication
      const response = await fetch(`${backendUrl}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-cache',
      });

      if (response.ok) {
        console.log('✅ Backend connectivity validation successful');
        return true;
      } else {
        console.log(
          `🔍 Backend connectivity check failed with status: ${response.status}`
        );
        return false;
      }
    } catch (error) {
      console.warn('Backend validation failed:', error);
      return true; // Don't block authentication flow on network errors
    }
  }
}

// NOTE: Any decryption of sensitive session data or JWTs should occur on the server-side
// where cryptographic keys can be securely managed and protected.
// Client-side base64 decoding (as was previously here) does NOT provide encryption.
