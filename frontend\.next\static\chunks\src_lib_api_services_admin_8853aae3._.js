(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/api/services/admin/auditService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuditService": (()=>AuditService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-client] (ecmascript)");
;
// Define AuditLogTransformer for consistent data transformation
const AuditLogTransformer = {
    fromApi: (data)=>({
            action: data.action || '',
            details: data.details || '',
            id: data.id || '',
            timestamp: new Date(data.created_at || data.timestamp || new Date()),
            userId: data.user_id || data.userId || data.auth_user_id || '',
            auth_user_id: data.auth_user_id || '',
            auth_user: data.auth_user || null
        }),
    toApi: (data)=>data
};
class AuditService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/admin/audit-logs';
    transformer = AuditLogTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 2 * 60 * 1000,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            retryAttempts: 3,
            ...config
        });
    }
    /**
   * Get audit logs by action type
   */ async getByAction(action, filters) {
        const result = await this.getAll({
            ...filters,
            action
        });
        return result.data;
    }
    /**
   * Get audit logs by date range with filtering
   */ async getByDateRange(startDate, endDate, filters) {
        const dateFilters = {
            endDate: endDate.toISOString(),
            startDate: startDate.toISOString(),
            ...filters
        };
        return this.getAll(dateFilters);
    }
    /**
   * Get audit logs by user ID
   */ async getByUserId(userId, filters) {
        const result = await this.getAll({
            ...filters,
            userId
        });
        return result.data;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/services/admin/UserService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "UserService": (()=>UserService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-client] (ecmascript)");
;
// Define UserTransformer for consistent data transformation
const UserTransformer = {
    fromApi: (data)=>{
        // Extract email from the nested users array structure returned by backend
        const email = data.users?.[0]?.email || data.email || '';
        const email_confirmed_at = data.users?.[0]?.email_confirmed_at || data.email_confirmed_at || null;
        return {
            created_at: data.created_at || '',
            email,
            email_confirmed_at,
            employee_id: data.employee_id || null,
            full_name: data.full_name || data.name || '',
            id: data.id,
            isActive: data.is_active ?? true,
            last_sign_in_at: data.last_sign_in_at || null,
            phone: data.phone || null,
            phone_confirmed_at: data.phone_confirmed_at || null,
            role: data.role || 'USER',
            updated_at: data.updated_at || '',
            users: data.users
        };
    },
    toApi: (data)=>data
};
class UserService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseApiService"] {
    endpoint = '/admin/users';
    transformer = UserTransformer;
    constructor(apiClient, config){
        super(apiClient, {
            cacheDuration: 5 * 60 * 1000,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            retryAttempts: 3,
            ...config
        });
    }
    /**
   * Get users by role with filtering
   */ async getUsersByRole(role, filters = {}) {
        const result = await this.getAll({
            ...filters,
            role
        });
        return result.data;
    }
    /**
   * Toggle user activation status
   */ async toggleActivation(id, isActive) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.patch(`${this.endpoint}/${id}/toggle-activation`, {
                isActive
            });
            // Invalidate related caches
            this.cache.invalidatePattern(new RegExp(`^${this.endpoint}:`));
            this.cache.invalidate(`${this.endpoint}:getById:${id}`);
            return this.transformer.fromApi ? this.transformer.fromApi(response) : response;
        });
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/services/admin/adminService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Enhanced Admin Service - Production-Ready Service Foundation
 *
 * This service provides a unified interface for all admin operations,
 * consolidating functionality from multiple admin services while maintaining
 * Single Responsibility Principle through delegation to specialized services.
 *
 * Features:
 * - BaseApiService integration for consistent patterns
 * - Circuit breaker protection
 * - Request caching and deduplication
 * - Comprehensive error handling with retry logic
 * - Performance monitoring and metrics
 * - Audit logging integration
 *
 * Built upon the BaseApiService foundation for production-grade reliability.
 */ __turbopack_context__.s({
    "AdminService": (()=>AdminService),
    "adminCache": (()=>adminCache),
    "adminService": (()=>adminService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/core/baseApiService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$apiServiceFactory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/apiServiceFactory.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/factory.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$auditService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/auditService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$UserService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/UserService.ts [app-client] (ecmascript)");
;
;
;
;
/**
 * Admin data transformer for consistent data transformation
 */ const AdminTransformer = {
    fromApi: (data)=>data,
    toApi: (data)=>data
};
class AdminService extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$core$2f$baseApiService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseApiService"] {
    /**
   * Enhanced cache management utilities using BaseApiService infrastructure
   */ get cacheUtils() {
        return {
            /**
       * Clear all cache
       */ clearAll: ()=>this.clearCache(),
            /**
       * Force refresh health status (bypasses cache)
       */ forceRefreshHealth: async ()=>{
                this.cache.invalidate('admin:health');
                return this.getSystemHealthStatus();
            },
            /**
       * Force refresh performance metrics (bypasses cache)
       */ forceRefreshPerformance: async ()=>{
                this.cache.invalidate('admin:performance');
                return this.getPerformanceMetrics();
            },
            /**
       * Get cache statistics
       */ getStats: ()=>this.cache.getStats(),
            /**
       * Invalidate all admin cache entries
       */ invalidateAll: ()=>this.cache.invalidatePattern(/^admin:/)
        };
    }
    endpoint = '/admin';
    transformer = AdminTransformer;
    // Specialized service instances
    auditService;
    userService;
    constructor(apiClient, config){
        const client = apiClient || __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$factory$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiServiceFactory"].getApiClient();
        super(client, {
            cacheDuration: 5 * 60 * 1000,
            circuitBreakerThreshold: 5,
            enableMetrics: true,
            retryAttempts: 3,
            ...config
        });
        // Initialize specialized services with the same API client
        this.auditService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$auditService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuditService"](client);
        this.userService = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$UserService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserService"](client);
    }
    // ===== USER MANAGEMENT =====
    /**
   * Create audit log entry using BaseApiService infrastructure
   */ async createAuditLog(logData) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post('/admin/audit-logs', logData);
            // Invalidate audit logs cache
            this.cache.invalidatePattern(/^admin:audit:/);
            return response;
        });
    }
    /**
   * Create a new user using BaseApiService infrastructure
   */ async createUser(userData) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.post('/admin/users', userData);
            // Invalidate users cache after creation
            this.cache.invalidatePattern(/^admin:users:/);
            return response;
        });
    }
    // ===== USER MANAGEMENT OPERATIONS =====
    /**
   * Delete a user using BaseApiService infrastructure
   */ async deleteUser(userId) {
        return this.executeWithInfrastructure(null, async ()=>{
            await this.apiClient.delete(`/admin/users/${userId}`);
            // Invalidate relevant caches
            this.cache.invalidatePattern(/^admin:users:/);
            this.cache.invalidate(`admin:user:${userId}`);
        });
    }
    /**
   * Get all users with pagination and filtering (delegates to UserService)
   */ async getAllUsers(filters = {}) {
        return this.userService.getAll(filters);
    }
    /**
   * Get audit logs with pagination and filtering (delegates to AuditService)
   */ async getAuditLogs(filters = {}) {
        // Use the AuditService's getAll method directly with proper filters
        return this.auditService.getAll(filters);
    }
    /**
   * Get mock health status data
   */ getMockHealthStatus() {
        return {
            services: {
                api: {
                    responseTime: 23,
                    status: 'healthy'
                },
                cache: {
                    responseTime: 12,
                    status: 'healthy'
                },
                database: {
                    responseTime: 45,
                    status: 'healthy'
                }
            },
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: 3600
        };
    }
    // ===== AUDIT LOG MANAGEMENT =====
    /**
   * Get mock performance metrics
   */ getMockPerformanceMetrics() {
        return {
            cpu: {
                cores: 4,
                usage: Math.random() * 100
            },
            errors: {
                rate: Math.random() * 5,
                total: Math.floor(Math.random() * 500)
            },
            memory: {
                percentage: Math.random() * 100,
                total: 8000,
                used: Math.random() * 8000
            },
            requests: {
                averageResponseTime: Math.random() * 1000,
                perSecond: Math.random() * 100,
                total: Math.floor(Math.random() * 10_000)
            },
            timestamp: new Date().toISOString()
        };
    }
    // ===== HEALTH MONITORING =====
    /**
   * Get mock recent errors
   */ getMockRecentErrors() {
        const mockErrors = [
            {
                details: {
                    component: 'database',
                    errorType: 'timeout'
                },
                id: '1',
                level: 'ERROR',
                message: 'Database connection timeout',
                requestId: 'req-456',
                source: 'database.service.ts',
                stack: 'Error: Connection timeout\n    at Database.connect...',
                timestamp: new Date().toISOString(),
                userId: 'user123'
            },
            {
                details: {
                    component: 'system',
                    metric: 'memory'
                },
                id: '2',
                level: 'WARNING',
                message: 'High memory usage detected',
                requestId: 'req-789',
                source: 'monitoring.service.ts',
                timestamp: new Date(Date.now() - 300_000).toISOString()
            }
        ];
        return {
            data: mockErrors,
            pagination: {
                hasNext: false,
                hasPrevious: false,
                limit: 10,
                page: 1,
                total: mockErrors.length,
                totalPages: Math.ceil(mockErrors.length / 10)
            }
        };
    }
    /**
   * Get performance metrics using BaseApiService infrastructure
   */ async getPerformanceMetrics() {
        const cacheKey = 'admin:performance';
        return this.executeWithInfrastructure(cacheKey, async ()=>{
            return this.apiClient.get('/admin/performance');
        });
    }
    /**
   * Get recent error logs using BaseApiService infrastructure
   */ async getRecentErrors(page = 1, limit = 10, level) {
        // Changed return type
        const params = new URLSearchParams();
        params.append('page', page.toString());
        params.append('limit', limit.toString());
        if (level) params.append('level', level);
        const url = `/admin/logs/errors?${params.toString()}`;
        const cacheKey = `admin:errors:${page}:${limit}:${level || 'all'}`;
        return this.executeWithInfrastructure(cacheKey, async ()=>{
            return this.apiClient.get(url); // Changed return type
        });
    }
    // ===== MOCK DATA METHODS (for development) =====
    /**
   * Get system health status using BaseApiService infrastructure
   */ async getSystemHealthStatus() {
        const cacheKey = 'admin:health';
        return this.executeWithInfrastructure(cacheKey, async ()=>{
            return this.apiClient.get('/admin/health');
        });
    }
    // ===== MOCK DATA METHODS (for development) =====
    /**
   * Toggle user activation status using BaseApiService infrastructure
   */ async toggleUserActivation(userId, isActive) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.patch(`/admin/users/${userId}/toggle-activation`, {
                isActive
            });
            // Invalidate relevant caches
            this.cache.invalidatePattern(/^admin:users:/);
            this.cache.invalidate(`admin:user:${userId}`);
            return response;
        });
    }
    /**
   * Update user details using BaseApiService infrastructure
   */ async updateUser(userId, userData) {
        return this.executeWithInfrastructure(null, async ()=>{
            const response = await this.apiClient.put(`/admin/users/${userId}`, userData);
            // Invalidate relevant caches
            this.cache.invalidatePattern(/^admin:users:/);
            this.cache.invalidate(`admin:user:${userId}`);
            return response;
        });
    }
}
const adminService = new AdminService();
const adminCache = adminService.cacheUtils;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/services/admin/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Admin Services Index
 *
 * Centralized exports for all admin-related services, providing a clean
 * interface for importing admin functionality throughout the application.
 *
 * This consolidates:
 * - Unified AdminService (main interface)
 * - Specialized services (UserService, AuditService)
 * - Cache utilities
 * - Type definitions
 */ // Export the main AdminService class and singleton instance
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/adminService.ts [app-client] (ecmascript)");
// Export specialized services for direct access if needed
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$auditService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/auditService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$UserService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/UserService.ts [app-client] (ecmascript)");
;
;
;
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api/services/admin/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/adminService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$auditService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/auditService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$UserService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/UserService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/services/admin/index.ts [app-client] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AdminService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AdminService"]),
    "AuditService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$auditService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuditService"]),
    "UserService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$UserService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UserService"]),
    "adminCache": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adminCache"]),
    "adminService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adminService"]),
    "createAuditLog": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adminService"]),
    "createUser": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adminService"]),
    "default": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adminService"]),
    "deleteUser": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adminService"]),
    "getAllUsers": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adminService"]),
    "getAuditLogs": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adminService"]),
    "getHealthStatus": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adminService"]),
    "getPerformanceMetrics": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adminService"]),
    "getRecentErrors": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adminService"]),
    "toggleUserActivation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adminService"]),
    "updateUser": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adminService"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$adminService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/adminService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$auditService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/auditService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$UserService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/UserService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/lib/api/services/admin/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AdminService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["AdminService"]),
    "AuditService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["AuditService"]),
    "UserService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["UserService"]),
    "adminCache": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["adminCache"]),
    "adminService": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["adminService"]),
    "createAuditLog": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["createAuditLog"]),
    "createUser": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["createUser"]),
    "default": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["default"]),
    "deleteUser": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["deleteUser"]),
    "getAllUsers": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["getAllUsers"]),
    "getAuditLogs": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["getAuditLogs"]),
    "getHealthStatus": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["getHealthStatus"]),
    "getPerformanceMetrics": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["getPerformanceMetrics"]),
    "getRecentErrors": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["getRecentErrors"]),
    "toggleUserActivation": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["toggleUserActivation"]),
    "updateUser": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__["updateUser"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$services$2f$admin$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/src/lib/api/services/admin/index.ts [app-client] (ecmascript) <exports>");
}}),
}]);

//# sourceMappingURL=src_lib_api_services_admin_8853aae3._.js.map