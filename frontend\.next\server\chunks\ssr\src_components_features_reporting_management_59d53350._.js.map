{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/management/DropZone.tsx"], "sourcesContent": ["/**\r\n * @file DropZone.tsx\r\n * @description Drop zone component for report builder drag-and-drop functionality\r\n */\r\n\r\nimport React from 'react';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { useDroppable } from '@dnd-kit/core';\r\nimport { useSortable } from '@dnd-kit/sortable';\r\nimport {\r\n  SortableContext,\r\n  verticalListSortingStrategy,\r\n} from '@dnd-kit/sortable';\r\nimport { Settings, Trash2, GripVertical, Layout, Plus } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport type { WidgetConfig } from '../data/types/reporting';\r\n\r\n/**\r\n * Props interface for DropZone\r\n */\r\ninterface DropZoneProps {\r\n  widgets: WidgetConfig[];\r\n  columns: number;\r\n  onWidgetConfigure: (widget: WidgetConfig) => void;\r\n  onWidgetDelete: (widgetId: string) => void;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * Sortable widget item component\r\n */\r\ninterface SortableWidgetProps {\r\n  widget: WidgetConfig;\r\n  onConfigure: () => void;\r\n  onDelete: () => void;\r\n}\r\n\r\nconst SortableWidget: React.FC<SortableWidgetProps> = ({\r\n  widget,\r\n  onConfigure,\r\n  onDelete,\r\n}) => {\r\n  const {\r\n    attributes,\r\n    listeners,\r\n    setNodeRef,\r\n    transform,\r\n    transition,\r\n    isDragging,\r\n  } = useSortable({\r\n    id: widget.id,\r\n    data: {\r\n      type: 'widget',\r\n      widget,\r\n    },\r\n  });\r\n\r\n  const style = {\r\n    transform: transform\r\n      ? `translate3d(${transform.x}px, ${transform.y}px, 0)`\r\n      : undefined,\r\n    transition,\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      style={style}\r\n      className={cn('group relative', isDragging && 'opacity-50')}\r\n    >\r\n      <Card className=\"border-2 border-dashed border-gray-200 hover:border-blue-300 transition-colors\">\r\n        <CardContent className=\"p-4\">\r\n          {/* Widget Header */}\r\n          <div className=\"flex items-center justify-between mb-3\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <div\r\n                {...attributes}\r\n                {...listeners}\r\n                className=\"cursor-grab active:cursor-grabbing p-1 hover:bg-gray-100 rounded\"\r\n              >\r\n                <GripVertical className=\"h-4 w-4 text-gray-400\" />\r\n              </div>\r\n              <h4 className=\"text-sm font-medium\">{widget.title}</h4>\r\n            </div>\r\n\r\n            <div className=\"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                onClick={onConfigure}\r\n                className=\"h-8 w-8 p-0\"\r\n              >\r\n                <Settings className=\"h-4 w-4\" />\r\n              </Button>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                onClick={onDelete}\r\n                className=\"h-8 w-8 p-0 text-red-600 hover:text-red-700\"\r\n              >\r\n                <Trash2 className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Widget Preview */}\r\n          <div className=\"bg-gray-50 rounded-lg p-4 min-h-[120px] flex items-center justify-center\">\r\n            <div className=\"text-center text-gray-500\">\r\n              <Layout className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\r\n              <div className=\"text-sm\">{widget.type} Widget</div>\r\n              <div className=\"text-xs mt-1\">\r\n                {widget.config && Object.keys(widget.config).length > 0\r\n                  ? 'Configured'\r\n                  : 'Click settings to configure'}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Widget Info */}\r\n          <div className=\"mt-3 flex items-center justify-between text-xs text-gray-500\">\r\n            <span>Type: {widget.type}</span>\r\n            <span>Span: {widget.span}</span>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * DropZone Component\r\n *\r\n * Provides drop zone for widgets in the report builder.\r\n *\r\n * Responsibilities:\r\n * - Accept dropped widgets from palette\r\n * - Display arranged widgets in grid layout\r\n * - Handle widget reordering via drag-and-drop\r\n * - Provide widget configuration and deletion\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of managing widget drop zone\r\n * - OCP: Open for extension via widget types\r\n * - DIP: Depends on drag-and-drop framework abstractions\r\n */\r\nexport const DropZone: React.FC<DropZoneProps> = ({\r\n  widgets,\r\n  columns,\r\n  onWidgetConfigure,\r\n  onWidgetDelete,\r\n  className = '',\r\n}) => {\r\n  const { isOver, setNodeRef } = useDroppable({\r\n    id: 'drop-zone',\r\n  });\r\n\r\n  // Calculate grid columns class\r\n  const gridColsClass =\r\n    {\r\n      1: 'grid-cols-1',\r\n      2: 'grid-cols-1 md:grid-cols-2',\r\n      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\r\n      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',\r\n    }[columns] || 'grid-cols-2';\r\n\r\n  return (\r\n    <Card className={cn('min-h-[400px]', className)}>\r\n      <CardContent className=\"p-6\">\r\n        <div className=\"flex items-center justify-between mb-4\">\r\n          <h3 className=\"text-lg font-medium\">Report Canvas</h3>\r\n          <div className=\"text-sm text-gray-500\">\r\n            {widgets.length} widget{widgets.length !== 1 ? 's' : ''}\r\n          </div>\r\n        </div>\r\n\r\n        <div\r\n          ref={setNodeRef}\r\n          className={cn(\r\n            'min-h-[300px] border-2 border-dashed rounded-lg p-4 transition-colors',\r\n            isOver ? 'border-blue-400 bg-blue-50' : 'border-gray-200',\r\n            widgets.length === 0 && 'flex items-center justify-center'\r\n          )}\r\n        >\r\n          {widgets.length === 0 ? (\r\n            // Empty state\r\n            <div className=\"text-center text-gray-500\">\r\n              <Layout className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\r\n              <h4 className=\"text-lg font-medium mb-2\">\r\n                Start Building Your Report\r\n              </h4>\r\n              <p className=\"text-sm mb-4\">\r\n                Drag widgets from the palette on the left to create your custom\r\n                report\r\n              </p>\r\n              <div className=\"flex items-center justify-center gap-2 text-xs\">\r\n                <Plus className=\"h-4 w-4\" />\r\n                <span>Drop widgets here</span>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            // Widgets grid\r\n            <SortableContext\r\n              items={widgets.map(w => w.id)}\r\n              strategy={verticalListSortingStrategy}\r\n            >\r\n              <div className={cn('grid gap-4', gridColsClass)}>\r\n                {widgets\r\n                  .sort((a, b) => (a.position || 0) - (b.position || 0))\r\n                  .map(widget => (\r\n                    <SortableWidget\r\n                      key={widget.id}\r\n                      widget={widget}\r\n                      onConfigure={() => onWidgetConfigure(widget)}\r\n                      onDelete={() => onWidgetDelete(widget.id)}\r\n                    />\r\n                  ))}\r\n              </div>\r\n            </SortableContext>\r\n          )}\r\n        </div>\r\n\r\n        {/* Drop zone instructions */}\r\n        {isOver && (\r\n          <div className=\"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\r\n            <div className=\"flex items-center gap-2 text-blue-700\">\r\n              <Plus className=\"h-4 w-4\" />\r\n              <span className=\"text-sm font-medium\">\r\n                Drop widget here to add to report\r\n              </span>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Layout controls */}\r\n        <div className=\"mt-4 pt-4 border-t\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"text-sm text-gray-600\">\r\n              Layout: {columns} column{columns !== 1 ? 's' : ''}\r\n            </div>\r\n            <div className=\"text-xs text-gray-500\">\r\n              Drag widgets to reorder • Click settings to configure\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default DropZone;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;;;;;;;;;AAuBA,MAAM,iBAAgD,CAAC,EACrD,MAAM,EACN,WAAW,EACX,QAAQ,EACT;IACC,MAAM,EACJ,UAAU,EACV,SAAS,EACT,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACX,GAAG,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE;QACd,IAAI,OAAO,EAAE;QACb,MAAM;YACJ,MAAM;YACN;QACF;IACF;IAEA,MAAM,QAAQ;QACZ,WAAW,YACP,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,MAAM,CAAC,GACpD;QACJ;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,OAAO;QACP,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB,cAAc;kBAE9C,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACE,GAAG,UAAU;wCACb,GAAG,SAAS;wCACb,WAAU;kDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;kDAE1B,8OAAC;wCAAG,WAAU;kDAAuB,OAAO,KAAK;;;;;;;;;;;;0CAGnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qNAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAI,WAAU;;wCAAW,OAAO,IAAI;wCAAC;;;;;;;8CACtC,8OAAC;oCAAI,WAAU;8CACZ,OAAO,MAAM,IAAI,OAAO,IAAI,CAAC,OAAO,MAAM,EAAE,MAAM,GAAG,IAClD,eACA;;;;;;;;;;;;;;;;;kCAMV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAK;oCAAO,OAAO,IAAI;;;;;;;0CACxB,8OAAC;;oCAAK;oCAAO,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC;AAkBO,MAAM,WAAoC,CAAC,EAChD,OAAO,EACP,OAAO,EACP,iBAAiB,EACjB,cAAc,EACd,YAAY,EAAE,EACf;IACC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD,EAAE;QAC1C,IAAI;IACN;IAEA,+BAA+B;IAC/B,MAAM,gBACJ;QACE,GAAG;QACH,GAAG;QACH,GAAG;QACH,GAAG;IACL,CAAC,CAAC,QAAQ,IAAI;IAEhB,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBACnC,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsB;;;;;;sCACpC,8OAAC;4BAAI,WAAU;;gCACZ,QAAQ,MAAM;gCAAC;gCAAQ,QAAQ,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;8BAIzD,8OAAC;oBACC,KAAK;oBACL,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,yEACA,SAAS,+BAA+B,mBACxC,QAAQ,MAAM,KAAK,KAAK;8BAGzB,QAAQ,MAAM,KAAK,IAClB,cAAc;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,qNAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAGzC,8OAAC;gCAAE,WAAU;0CAAe;;;;;;0CAI5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;+BAIV,eAAe;kCACf,8OAAC,mKAAA,CAAA,kBAAe;wBACd,OAAO,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;wBAC5B,UAAU,mKAAA,CAAA,8BAA2B;kCAErC,cAAA,8OAAC;4BAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,cAAc;sCAC9B,QACE,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,IAAI,CAAC,GACnD,GAAG,CAAC,CAAA,uBACH,8OAAC;oCAEC,QAAQ;oCACR,aAAa,IAAM,kBAAkB;oCACrC,UAAU,IAAM,eAAe,OAAO,EAAE;mCAHnC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;gBAY3B,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;;;;;;;8BAQ5C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAwB;oCAC5B;oCAAQ;oCAAQ,YAAY,IAAI,MAAM;;;;;;;0CAEjD,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD;uCAEe", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/management/WidgetConfigurator.tsx"], "sourcesContent": ["/**\r\n * @file WidgetConfigurator.tsx\r\n * @description Widget configuration component for customizing widget settings\r\n */\r\n\r\nimport React from 'react';\r\nimport { useForm } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { z } from 'zod';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from '@/components/ui/form';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { Settings } from 'lucide-react';\r\nimport type { WidgetConfig } from '../data/types/reporting';\r\n\r\n/**\r\n * Widget configuration schema\r\n */\r\nconst widgetConfigSchema = z.object({\r\n  title: z.string().min(1, 'Title is required'),\r\n  span: z.string(),\r\n  showTitle: z.boolean().default(true),\r\n  showBorder: z.boolean().default(true),\r\n  refreshInterval: z.number().min(0).max(1440).optional(),\r\n  height: z.number().min(200).max(800).optional(),\r\n  // Chart-specific configs\r\n  chartType: z.string().optional(),\r\n  xAxisField: z.string().optional(),\r\n  yAxisField: z.string().optional(),\r\n  colorScheme: z.string().optional(),\r\n  showLegend: z.boolean().optional(),\r\n  showGrid: z.boolean().optional(),\r\n  // Table-specific configs\r\n  pageSize: z.number().min(5).max(100).optional(),\r\n  sortable: z.boolean().optional(),\r\n  filterable: z.boolean().optional(),\r\n  exportable: z.boolean().optional(),\r\n  // Analytics-specific configs\r\n  metricType: z.string().optional(),\r\n  aggregationType: z.string().optional(),\r\n  comparisonPeriod: z.string().optional(),\r\n});\r\n\r\ntype WidgetConfigFormData = z.infer<typeof widgetConfigSchema>;\r\n\r\n/**\r\n * Props interface for WidgetConfigurator\r\n */\r\ninterface WidgetConfiguratorProps {\r\n  widget: WidgetConfig;\r\n  onSave: (widget: WidgetConfig) => void;\r\n  onCancel: () => void;\r\n}\r\n\r\n/**\r\n * Available span options\r\n */\r\nconst SPAN_OPTIONS = [\r\n  { value: 'col-span-1', label: '1 Column' },\r\n  { value: 'col-span-2', label: '2 Columns' },\r\n  { value: 'col-span-3', label: '3 Columns' },\r\n  { value: 'col-span-4', label: '4 Columns' },\r\n  { value: 'col-span-full', label: 'Full Width' },\r\n];\r\n\r\n/**\r\n * Chart type options\r\n */\r\nconst CHART_TYPES = [\r\n  { value: 'bar', label: 'Bar Chart' },\r\n  { value: 'line', label: 'Line Chart' },\r\n  { value: 'pie', label: 'Pie Chart' },\r\n  { value: 'area', label: 'Area Chart' },\r\n  { value: 'scatter', label: 'Scatter Plot' },\r\n];\r\n\r\n/**\r\n * Color scheme options\r\n */\r\nconst COLOR_SCHEMES = [\r\n  { value: 'default', label: 'Default' },\r\n  { value: 'blue', label: 'Blue' },\r\n  { value: 'green', label: 'Green' },\r\n  { value: 'red', label: 'Red' },\r\n  { value: 'purple', label: 'Purple' },\r\n  { value: 'orange', label: 'Orange' },\r\n];\r\n\r\n/**\r\n * WidgetConfigurator Component\r\n *\r\n * Provides configuration interface for widget settings.\r\n *\r\n * Responsibilities:\r\n * - Display widget-specific configuration options\r\n * - Validate configuration data\r\n * - Handle configuration save and cancel\r\n * - Adapt to different widget types\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of configuring widgets\r\n * - OCP: Open for extension via widget type configurations\r\n * - DIP: Depends on form framework abstractions\r\n */\r\nexport const WidgetConfigurator: React.FC<WidgetConfiguratorProps> = ({\r\n  widget,\r\n  onSave,\r\n  onCancel,\r\n}) => {\r\n  // Initialize form with widget configuration\r\n  const form = useForm<WidgetConfigFormData>({\r\n    resolver: zodResolver(widgetConfigSchema),\r\n    defaultValues: {\r\n      title: widget.title,\r\n      span: widget.span,\r\n      showTitle: widget.config?.showTitle ?? true,\r\n      showBorder: widget.config?.showBorder ?? true,\r\n      refreshInterval: widget.config?.refreshInterval || 60,\r\n      height: widget.config?.height || 300,\r\n      // Chart configs\r\n      chartType: widget.config?.chartType || 'bar',\r\n      xAxisField: widget.config?.xAxisField || '',\r\n      yAxisField: widget.config?.yAxisField || '',\r\n      colorScheme: widget.config?.colorScheme || 'default',\r\n      showLegend: widget.config?.showLegend ?? true,\r\n      showGrid: widget.config?.showGrid ?? true,\r\n      // Table configs\r\n      pageSize: widget.config?.pageSize || 10,\r\n      sortable: widget.config?.sortable ?? true,\r\n      filterable: widget.config?.filterable ?? true,\r\n      exportable: widget.config?.exportable ?? true,\r\n      // Analytics configs\r\n      metricType: widget.config?.metricType || 'count',\r\n      aggregationType: widget.config?.aggregationType || 'sum',\r\n      comparisonPeriod: widget.config?.comparisonPeriod || 'previous-month',\r\n    },\r\n  });\r\n\r\n  // Handle form submission\r\n  const handleSubmit = (data: WidgetConfigFormData) => {\r\n    const updatedWidget: WidgetConfig = {\r\n      ...widget,\r\n      title: data.title,\r\n      span: data.span,\r\n      config: {\r\n        ...widget.config,\r\n        ...data,\r\n      },\r\n    };\r\n\r\n    onSave(updatedWidget);\r\n  };\r\n\r\n  // Check if widget type supports specific configurations\r\n  const isChartWidget = ['bar-chart', 'pie-chart', 'line-chart'].includes(\r\n    widget.type\r\n  );\r\n  const isTableWidget = widget.type === 'data-table';\r\n  const isAnalyticsWidget = ['analytics', 'metrics'].includes(widget.type);\r\n\r\n  return (\r\n    <Dialog open={true} onOpenChange={onCancel}>\r\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"flex items-center gap-2\">\r\n            <Settings className=\"h-5 w-5\" />\r\n            Configure Widget: {widget.title}\r\n          </DialogTitle>\r\n          <DialogDescription>\r\n            Customize the appearance and behavior of this widget.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <Form {...form}>\r\n          <form\r\n            onSubmit={form.handleSubmit(handleSubmit)}\r\n            className=\"space-y-6\"\r\n          >\r\n            {/* Basic Configuration */}\r\n            <div className=\"space-y-4\">\r\n              <h3 className=\"text-lg font-medium\">Basic Settings</h3>\r\n\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"title\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Widget Title</FormLabel>\r\n                      <FormControl>\r\n                        <Input placeholder=\"Enter widget title\" {...field} />\r\n                      </FormControl>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"span\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Width</FormLabel>\r\n                      <Select\r\n                        onValueChange={field.onChange}\r\n                        defaultValue={field.value}\r\n                      >\r\n                        <FormControl>\r\n                          <SelectTrigger>\r\n                            <SelectValue placeholder=\"Select width\" />\r\n                          </SelectTrigger>\r\n                        </FormControl>\r\n                        <SelectContent>\r\n                          {SPAN_OPTIONS.map(option => (\r\n                            <SelectItem key={option.value} value={option.value}>\r\n                              {option.label}\r\n                            </SelectItem>\r\n                          ))}\r\n                        </SelectContent>\r\n                      </Select>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"height\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Height (px)</FormLabel>\r\n                      <FormControl>\r\n                        <Input\r\n                          type=\"number\"\r\n                          min=\"200\"\r\n                          max=\"800\"\r\n                          {...field}\r\n                          onChange={e =>\r\n                            field.onChange(parseInt(e.target.value) || 300)\r\n                          }\r\n                        />\r\n                      </FormControl>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"refreshInterval\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Refresh Interval (minutes)</FormLabel>\r\n                      <FormControl>\r\n                        <Input\r\n                          type=\"number\"\r\n                          min=\"0\"\r\n                          max=\"1440\"\r\n                          {...field}\r\n                          onChange={e =>\r\n                            field.onChange(parseInt(e.target.value) || 60)\r\n                          }\r\n                        />\r\n                      </FormControl>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n              </div>\r\n\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"showTitle\"\r\n                  render={({ field }) => (\r\n                    <FormItem className=\"flex flex-row items-center justify-between rounded-lg border p-3\">\r\n                      <div className=\"space-y-0.5\">\r\n                        <FormLabel>Show Title</FormLabel>\r\n                        <FormDescription>Display widget title</FormDescription>\r\n                      </div>\r\n                      <FormControl>\r\n                        <Checkbox\r\n                          checked={field.value}\r\n                          onCheckedChange={field.onChange}\r\n                        />\r\n                      </FormControl>\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"showBorder\"\r\n                  render={({ field }) => (\r\n                    <FormItem className=\"flex flex-row items-center justify-between rounded-lg border p-3\">\r\n                      <div className=\"space-y-0.5\">\r\n                        <FormLabel>Show Border</FormLabel>\r\n                        <FormDescription>Display widget border</FormDescription>\r\n                      </div>\r\n                      <FormControl>\r\n                        <Checkbox\r\n                          checked={field.value}\r\n                          onCheckedChange={field.onChange}\r\n                        />\r\n                      </FormControl>\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Chart-specific Configuration */}\r\n            {isChartWidget && (\r\n              <div className=\"space-y-4\">\r\n                <h3 className=\"text-lg font-medium\">Chart Settings</h3>\r\n\r\n                <div className=\"grid grid-cols-2 gap-4\">\r\n                  <FormField\r\n                    control={form.control}\r\n                    name=\"chartType\"\r\n                    render={({ field }) => (\r\n                      <FormItem>\r\n                        <FormLabel>Chart Type</FormLabel>\r\n                        <Select\r\n                          onValueChange={field.onChange}\r\n                          value={field.value || ''}\r\n                        >\r\n                          <FormControl>\r\n                            <SelectTrigger>\r\n                              <SelectValue placeholder=\"Select chart type\" />\r\n                            </SelectTrigger>\r\n                          </FormControl>\r\n                          <SelectContent>\r\n                            {CHART_TYPES.map(type => (\r\n                              <SelectItem key={type.value} value={type.value}>\r\n                                {type.label}\r\n                              </SelectItem>\r\n                            ))}\r\n                          </SelectContent>\r\n                        </Select>\r\n                        <FormMessage />\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                  <FormField\r\n                    control={form.control}\r\n                    name=\"colorScheme\"\r\n                    render={({ field }) => (\r\n                      <FormItem>\r\n                        <FormLabel>Color Scheme</FormLabel>\r\n                        <Select\r\n                          onValueChange={field.onChange}\r\n                          value={field.value || ''}\r\n                        >\r\n                          <FormControl>\r\n                            <SelectTrigger>\r\n                              <SelectValue placeholder=\"Select color scheme\" />\r\n                            </SelectTrigger>\r\n                          </FormControl>\r\n                          <SelectContent>\r\n                            {COLOR_SCHEMES.map(scheme => (\r\n                              <SelectItem\r\n                                key={scheme.value}\r\n                                value={scheme.value}\r\n                              >\r\n                                {scheme.label}\r\n                              </SelectItem>\r\n                            ))}\r\n                          </SelectContent>\r\n                        </Select>\r\n                        <FormMessage />\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-2 gap-4\">\r\n                  <FormField\r\n                    control={form.control}\r\n                    name=\"showLegend\"\r\n                    render={({ field }) => (\r\n                      <FormItem className=\"flex flex-row items-center justify-between rounded-lg border p-3\">\r\n                        <div className=\"space-y-0.5\">\r\n                          <FormLabel>Show Legend</FormLabel>\r\n                          <FormDescription>\r\n                            Display chart legend\r\n                          </FormDescription>\r\n                        </div>\r\n                        <FormControl>\r\n                          <Checkbox\r\n                            checked={field.value || false}\r\n                            onCheckedChange={field.onChange}\r\n                          />\r\n                        </FormControl>\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                  <FormField\r\n                    control={form.control}\r\n                    name=\"showGrid\"\r\n                    render={({ field }) => (\r\n                      <FormItem className=\"flex flex-row items-center justify-between rounded-lg border p-3\">\r\n                        <div className=\"space-y-0.5\">\r\n                          <FormLabel>Show Grid</FormLabel>\r\n                          <FormDescription>\r\n                            Display chart grid lines\r\n                          </FormDescription>\r\n                        </div>\r\n                        <FormControl>\r\n                          <Checkbox\r\n                            checked={field.value || false}\r\n                            onCheckedChange={field.onChange}\r\n                          />\r\n                        </FormControl>\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Table-specific Configuration */}\r\n            {isTableWidget && (\r\n              <div className=\"space-y-4\">\r\n                <h3 className=\"text-lg font-medium\">Table Settings</h3>\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"pageSize\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Page Size</FormLabel>\r\n                      <FormControl>\r\n                        <Input\r\n                          type=\"number\"\r\n                          min=\"5\"\r\n                          max=\"100\"\r\n                          {...field}\r\n                          onChange={e =>\r\n                            field.onChange(parseInt(e.target.value) || 10)\r\n                          }\r\n                        />\r\n                      </FormControl>\r\n                      <FormDescription>Number of rows per page</FormDescription>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <div className=\"grid grid-cols-3 gap-4\">\r\n                  <FormField\r\n                    control={form.control}\r\n                    name=\"sortable\"\r\n                    render={({ field }) => (\r\n                      <FormItem className=\"flex flex-row items-center justify-between rounded-lg border p-3\">\r\n                        <div className=\"space-y-0.5\">\r\n                          <FormLabel>Sortable</FormLabel>\r\n                          <FormDescription>\r\n                            Enable column sorting\r\n                          </FormDescription>\r\n                        </div>\r\n                        <FormControl>\r\n                          <Checkbox\r\n                            checked={field.value || false}\r\n                            onCheckedChange={field.onChange}\r\n                          />\r\n                        </FormControl>\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                  <FormField\r\n                    control={form.control}\r\n                    name=\"filterable\"\r\n                    render={({ field }) => (\r\n                      <FormItem className=\"flex flex-row items-center justify-between rounded-lg border p-3\">\r\n                        <div className=\"space-y-0.5\">\r\n                          <FormLabel>Filterable</FormLabel>\r\n                          <FormDescription>\r\n                            Enable column filters\r\n                          </FormDescription>\r\n                        </div>\r\n                        <FormControl>\r\n                          <Checkbox\r\n                            checked={field.value || false}\r\n                            onCheckedChange={field.onChange}\r\n                          />\r\n                        </FormControl>\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                  <FormField\r\n                    control={form.control}\r\n                    name=\"exportable\"\r\n                    render={({ field }) => (\r\n                      <FormItem className=\"flex flex-row items-center justify-between rounded-lg border p-3\">\r\n                        <div className=\"space-y-0.5\">\r\n                          <FormLabel>Exportable</FormLabel>\r\n                          <FormDescription>Enable data export</FormDescription>\r\n                        </div>\r\n                        <FormControl>\r\n                          <Checkbox\r\n                            checked={field.value || false}\r\n                            onCheckedChange={field.onChange}\r\n                          />\r\n                        </FormControl>\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <DialogFooter>\r\n              <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\">Save Configuration</Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </Form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default WidgetConfigurator;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AACA;AAAA;AACA;AAQA;AASA;AAEA;AACA;AAOA;AACA;;;;;;;;;;;;AAGA;;CAEC,GACD,MAAM,qBAAqB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,MAAM,iLAAA,CAAA,IAAC,CAAC,MAAM;IACd,WAAW,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,YAAY,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAChC,iBAAiB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,QAAQ;IACrD,QAAQ,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,QAAQ;IAC7C,yBAAyB;IACzB,WAAW,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,YAAY,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,YAAY,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAChC,UAAU,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAC9B,yBAAyB;IACzB,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,QAAQ;IAC7C,UAAU,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAC9B,YAAY,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAChC,YAAY,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IAChC,6BAA6B;IAC7B,YAAY,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,iBAAiB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,kBAAkB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AACvC;AAaA;;CAEC,GACD,MAAM,eAAe;IACnB;QAAE,OAAO;QAAc,OAAO;IAAW;IACzC;QAAE,OAAO;QAAc,OAAO;IAAY;IAC1C;QAAE,OAAO;QAAc,OAAO;IAAY;IAC1C;QAAE,OAAO;QAAc,OAAO;IAAY;IAC1C;QAAE,OAAO;QAAiB,OAAO;IAAa;CAC/C;AAED;;CAEC,GACD,MAAM,cAAc;IAClB;QAAE,OAAO;QAAO,OAAO;IAAY;IACnC;QAAE,OAAO;QAAQ,OAAO;IAAa;IACrC;QAAE,OAAO;QAAO,OAAO;IAAY;IACnC;QAAE,OAAO;QAAQ,OAAO;IAAa;IACrC;QAAE,OAAO;QAAW,OAAO;IAAe;CAC3C;AAED;;CAEC,GACD,MAAM,gBAAgB;IACpB;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAO,OAAO;IAAM;IAC7B;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAU,OAAO;IAAS;CACpC;AAkBM,MAAM,qBAAwD,CAAC,EACpE,MAAM,EACN,MAAM,EACN,QAAQ,EACT;IACC,4CAA4C;IAC5C,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAwB;QACzC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO,OAAO,KAAK;YACnB,MAAM,OAAO,IAAI;YACjB,WAAW,OAAO,MAAM,EAAE,aAAa;YACvC,YAAY,OAAO,MAAM,EAAE,cAAc;YACzC,iBAAiB,OAAO,MAAM,EAAE,mBAAmB;YACnD,QAAQ,OAAO,MAAM,EAAE,UAAU;YACjC,gBAAgB;YAChB,WAAW,OAAO,MAAM,EAAE,aAAa;YACvC,YAAY,OAAO,MAAM,EAAE,cAAc;YACzC,YAAY,OAAO,MAAM,EAAE,cAAc;YACzC,aAAa,OAAO,MAAM,EAAE,eAAe;YAC3C,YAAY,OAAO,MAAM,EAAE,cAAc;YACzC,UAAU,OAAO,MAAM,EAAE,YAAY;YACrC,gBAAgB;YAChB,UAAU,OAAO,MAAM,EAAE,YAAY;YACrC,UAAU,OAAO,MAAM,EAAE,YAAY;YACrC,YAAY,OAAO,MAAM,EAAE,cAAc;YACzC,YAAY,OAAO,MAAM,EAAE,cAAc;YACzC,oBAAoB;YACpB,YAAY,OAAO,MAAM,EAAE,cAAc;YACzC,iBAAiB,OAAO,MAAM,EAAE,mBAAmB;YACnD,kBAAkB,OAAO,MAAM,EAAE,oBAAoB;QACvD;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe,CAAC;QACpB,MAAM,gBAA8B;YAClC,GAAG,MAAM;YACT,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;YACf,QAAQ;gBACN,GAAG,OAAO,MAAM;gBAChB,GAAG,IAAI;YACT;QACF;QAEA,OAAO;IACT;IAEA,wDAAwD;IACxD,MAAM,gBAAgB;QAAC;QAAa;QAAa;KAAa,CAAC,QAAQ,CACrE,OAAO,IAAI;IAEb,MAAM,gBAAgB,OAAO,IAAI,KAAK;IACtC,MAAM,oBAAoB;QAAC;QAAa;KAAU,CAAC,QAAQ,CAAC,OAAO,IAAI;IAEvE,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;gCACb,OAAO,KAAK;;;;;;;sCAEjC,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC,gIAAA,CAAA,OAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,8OAAC;wBACC,UAAU,KAAK,YAAY,CAAC;wBAC5B,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDAEpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEAAC,aAAY;oEAAsB,GAAG,KAAK;;;;;;;;;;;0EAEnD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,kIAAA,CAAA,SAAM;gEACL,eAAe,MAAM,QAAQ;gEAC7B,cAAc,MAAM,KAAK;;kFAEzB,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;sFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG7B,8OAAC,kIAAA,CAAA,gBAAa;kFACX,aAAa,GAAG,CAAC,CAAA,uBAChB,8OAAC,kIAAA,CAAA,aAAU;gFAAoB,OAAO,OAAO,KAAK;0FAC/C,OAAO,KAAK;+EADE,OAAO,KAAK;;;;;;;;;;;;;;;;0EAMnC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;kDAMpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,KAAI;oEACJ,KAAI;oEACH,GAAG,KAAK;oEACT,UAAU,CAAA,IACR,MAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;0EAIjD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,KAAI;oEACJ,KAAI;oEACH,GAAG,KAAK;oEACT,UAAU,CAAA,IACR,MAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;0EAIjD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;kDAMpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,kBAAe;kFAAC;;;;;;;;;;;;0EAEnB,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,SAAS,MAAM,KAAK;oEACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;0DAOzC,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,kBAAe;kFAAC;;;;;;;;;;;;0EAEnB,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,SAAS,MAAM,KAAK;oEACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAU5C,+BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDAEpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,kIAAA,CAAA,SAAM;gEACL,eAAe,MAAM,QAAQ;gEAC7B,OAAO,MAAM,KAAK,IAAI;;kFAEtB,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;sFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG7B,8OAAC,kIAAA,CAAA,gBAAa;kFACX,YAAY,GAAG,CAAC,CAAA,qBACf,8OAAC,kIAAA,CAAA,aAAU;gFAAkB,OAAO,KAAK,KAAK;0FAC3C,KAAK,KAAK;+EADI,KAAK,KAAK;;;;;;;;;;;;;;;;0EAMjC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,kIAAA,CAAA,SAAM;gEACL,eAAe,MAAM,QAAQ;gEAC7B,OAAO,MAAM,KAAK,IAAI;;kFAEtB,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;sFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG7B,8OAAC,kIAAA,CAAA,gBAAa;kFACX,cAAc,GAAG,CAAC,CAAA,uBACjB,8OAAC,kIAAA,CAAA,aAAU;gFAET,OAAO,OAAO,KAAK;0FAElB,OAAO,KAAK;+EAHR,OAAO,KAAK;;;;;;;;;;;;;;;;0EAQzB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;kDAMpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,kBAAe;kFAAC;;;;;;;;;;;;0EAInB,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,SAAS,MAAM,KAAK,IAAI;oEACxB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;0DAOzC,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,kBAAe;kFAAC;;;;;;;;;;;;0EAInB,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,SAAS,MAAM,KAAK,IAAI;oEACxB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAW9C,+BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDAEpC,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACH,GAAG,KAAK;4DACT,UAAU,CAAA,IACR,MAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;kEAIjD,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;kEACjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAKlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,kBAAe;kFAAC;;;;;;;;;;;;0EAInB,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,SAAS,MAAM,KAAK,IAAI;oEACxB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;0DAOzC,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,kBAAe;kFAAC;;;;;;;;;;;;0EAInB,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,SAAS,MAAM,KAAK,IAAI;oEACxB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;0DAOzC,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,kBAAe;kFAAC;;;;;;;;;;;;0EAEnB,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,SAAS,MAAM,KAAK,IAAI;oEACxB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAU/C,8OAAC,kIAAA,CAAA,eAAY;;kDACX,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS;kDAAU;;;;;;kDAG3D,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;uCAEe", "debugId": null}}, {"offset": {"line": 1614, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/management/WidgetPalette.tsx"], "sourcesContent": ["/**\r\n * @file WidgetPalette.tsx\r\n * @description Widget palette component for drag-and-drop report builder\r\n */\r\n\r\nimport React from 'react';\r\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { useDraggable } from '@dnd-kit/core';\r\nimport {\r\n  BarChart3,\r\n  Pie<PERSON>hart,\r\n  LineChart,\r\n  Table,\r\n  Users,\r\n  Car,\r\n  CheckSquare,\r\n  FileText,\r\n  TrendingUp,\r\n  Network,\r\n  Calendar,\r\n  DollarSign,\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\n\r\n/**\r\n * Props interface for WidgetPalette\r\n */\r\ninterface WidgetPaletteProps {\r\n  dataSource: string;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * Widget type definition\r\n */\r\ninterface WidgetType {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  icon: React.ReactNode;\r\n  category: string;\r\n  supportedDataSources: string[];\r\n}\r\n\r\n/**\r\n * Draggable widget item component\r\n */\r\ninterface DraggableWidgetProps {\r\n  widget: WidgetType;\r\n  isDisabled?: boolean;\r\n}\r\n\r\nconst DraggableWidget: React.FC<DraggableWidgetProps> = ({\r\n  widget,\r\n  isDisabled = false,\r\n}) => {\r\n  const { attributes, listeners, setNodeRef, transform, isDragging } =\r\n    useDraggable({\r\n      id: widget.id,\r\n      data: {\r\n        type: 'widget-type',\r\n        widgetType: widget.id,\r\n      },\r\n      disabled: isDisabled,\r\n    });\r\n\r\n  const style = transform\r\n    ? {\r\n        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,\r\n      }\r\n    : undefined;\r\n\r\n  return (\r\n    <div\r\n      ref={setNodeRef}\r\n      style={style}\r\n      {...listeners}\r\n      {...attributes}\r\n      className={cn(\r\n        'p-3 border rounded-lg cursor-grab active:cursor-grabbing transition-all',\r\n        isDragging && 'opacity-50',\r\n        isDisabled\r\n          ? 'opacity-50 cursor-not-allowed'\r\n          : 'hover:shadow-md hover:border-blue-300',\r\n        !isDisabled && 'bg-white'\r\n      )}\r\n    >\r\n      <div className=\"flex items-center gap-2 mb-2\">\r\n        {widget.icon}\r\n        <span className=\"text-sm font-medium\">{widget.name}</span>\r\n      </div>\r\n      <p className=\"text-xs text-gray-600 mb-2\">{widget.description}</p>\r\n      <Badge variant=\"outline\" className=\"text-xs\">\r\n        {widget.category}\r\n      </Badge>\r\n    </div>\r\n  );\r\n};\r\n\r\n/**\r\n * Available widget types\r\n */\r\nconst WIDGET_TYPES: WidgetType[] = [\r\n  // Analytics Widgets\r\n  {\r\n    id: 'analytics',\r\n    name: 'Analytics Widget',\r\n    description: 'Key metrics and performance indicators',\r\n    icon: <TrendingUp className=\"h-4 w-4\" />,\r\n    category: 'Analytics',\r\n    supportedDataSources: [\r\n      'delegations',\r\n      'tasks',\r\n      'vehicles',\r\n      'employees',\r\n      'cross-entity',\r\n    ],\r\n  },\r\n  {\r\n    id: 'metrics',\r\n    name: 'Metrics Widget',\r\n    description: 'Display key performance metrics',\r\n    icon: <BarChart3 className=\"h-4 w-4\" />,\r\n    category: 'Analytics',\r\n    supportedDataSources: ['delegations', 'tasks', 'vehicles', 'employees'],\r\n  },\r\n\r\n  // Chart Widgets\r\n  {\r\n    id: 'bar-chart',\r\n    name: 'Bar Chart',\r\n    description: 'Compare values across categories',\r\n    icon: <BarChart3 className=\"h-4 w-4\" />,\r\n    category: 'Charts',\r\n    supportedDataSources: ['delegations', 'tasks', 'vehicles', 'employees'],\r\n  },\r\n  {\r\n    id: 'pie-chart',\r\n    name: 'Pie Chart',\r\n    description: 'Show proportional data distribution',\r\n    icon: <PieChart className=\"h-4 w-4\" />,\r\n    category: 'Charts',\r\n    supportedDataSources: ['delegations', 'tasks', 'vehicles', 'employees'],\r\n  },\r\n  {\r\n    id: 'line-chart',\r\n    name: 'Line Chart',\r\n    description: 'Display trends over time',\r\n    icon: <LineChart className=\"h-4 w-4\" />,\r\n    category: 'Charts',\r\n    supportedDataSources: ['delegations', 'tasks', 'vehicles', 'employees'],\r\n  },\r\n\r\n  // Data Widgets\r\n  {\r\n    id: 'data-table',\r\n    name: 'Data Table',\r\n    description: 'Tabular data with sorting and filtering',\r\n    icon: <Table className=\"h-4 w-4\" />,\r\n    category: 'Data',\r\n    supportedDataSources: ['delegations', 'tasks', 'vehicles', 'employees'],\r\n  },\r\n\r\n  // Entity-Specific Widgets\r\n  {\r\n    id: 'employee-performance',\r\n    name: 'Employee Performance',\r\n    description: 'Employee performance metrics and charts',\r\n    icon: <Users className=\"h-4 w-4\" />,\r\n    category: 'Employee',\r\n    supportedDataSources: ['employees', 'cross-entity'],\r\n  },\r\n  {\r\n    id: 'vehicle-utilization',\r\n    name: 'Vehicle Utilization',\r\n    description: 'Vehicle usage and utilization metrics',\r\n    icon: <Car className=\"h-4 w-4\" />,\r\n    category: 'Vehicle',\r\n    supportedDataSources: ['vehicles', 'cross-entity'],\r\n  },\r\n  {\r\n    id: 'task-status',\r\n    name: 'Task Status',\r\n    description: 'Task completion and status tracking',\r\n    icon: <CheckSquare className=\"h-4 w-4\" />,\r\n    category: 'Task',\r\n    supportedDataSources: ['tasks', 'cross-entity'],\r\n  },\r\n  {\r\n    id: 'delegation-overview',\r\n    name: 'Delegation Overview',\r\n    description: 'Delegation status and distribution',\r\n    icon: <FileText className=\"h-4 w-4\" />,\r\n    category: 'Delegation',\r\n    supportedDataSources: ['delegations', 'cross-entity'],\r\n  },\r\n\r\n  // Specialized Widgets\r\n  {\r\n    id: 'correlation',\r\n    name: 'Correlation Analysis',\r\n    description: 'Cross-entity relationships and correlations',\r\n    icon: <Network className=\"h-4 w-4\" />,\r\n    category: 'Analysis',\r\n    supportedDataSources: ['cross-entity'],\r\n  },\r\n  {\r\n    id: 'timeline',\r\n    name: 'Timeline Widget',\r\n    description: 'Events and activities over time',\r\n    icon: <Calendar className=\"h-4 w-4\" />,\r\n    category: 'Timeline',\r\n    supportedDataSources: ['delegations', 'tasks', 'vehicles', 'employees'],\r\n  },\r\n  {\r\n    id: 'cost-analysis',\r\n    name: 'Cost Analysis',\r\n    description: 'Financial metrics and cost tracking',\r\n    icon: <DollarSign className=\"h-4 w-4\" />,\r\n    category: 'Financial',\r\n    supportedDataSources: ['vehicles', 'employees', 'cross-entity'],\r\n  },\r\n];\r\n\r\n/**\r\n * WidgetPalette Component\r\n *\r\n * Displays available widgets for drag-and-drop into report builder.\r\n *\r\n * Responsibilities:\r\n * - Display available widget types\r\n * - Filter widgets by data source compatibility\r\n * - Provide drag-and-drop functionality\r\n * - Group widgets by category\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying widget palette\r\n * - OCP: Open for extension via new widget types\r\n * - DIP: Depends on drag-and-drop framework abstractions\r\n */\r\nexport const WidgetPalette: React.FC<WidgetPaletteProps> = ({\r\n  dataSource,\r\n  className = '',\r\n}) => {\r\n  // Filter widgets by data source compatibility\r\n  const availableWidgets = WIDGET_TYPES.filter(\r\n    widget =>\r\n      widget.supportedDataSources.includes(dataSource) ||\r\n      widget.supportedDataSources.includes('cross-entity')\r\n  );\r\n\r\n  // Group widgets by category\r\n  const widgetsByCategory = availableWidgets.reduce(\r\n    (acc, widget) => {\r\n      if (!acc[widget.category]) {\r\n        acc[widget.category] = [];\r\n      }\r\n      acc[widget.category]!.push(widget);\r\n      return acc;\r\n    },\r\n    {} as Record<string, WidgetType[]>\r\n  );\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader className=\"pb-3\">\r\n        <CardTitle className=\"text-lg\">Widget Palette</CardTitle>\r\n        <p className=\"text-sm text-gray-600\">\r\n          Drag widgets to the report canvas to build your custom report\r\n        </p>\r\n        <Badge variant=\"secondary\" className=\"text-xs w-fit\">\r\n          {availableWidgets.length} widgets available\r\n        </Badge>\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-4\">\r\n        {Object.entries(widgetsByCategory).map(([category, widgets]) => (\r\n          <div key={category}>\r\n            <h4 className=\"text-sm font-medium mb-2 text-gray-700\">\r\n              {category}\r\n            </h4>\r\n            <div className=\"space-y-2\">\r\n              {widgets.map(widget => (\r\n                <DraggableWidget\r\n                  key={widget.id}\r\n                  widget={widget}\r\n                  isDisabled={\r\n                    !widget.supportedDataSources.includes(dataSource) &&\r\n                    !widget.supportedDataSources.includes('cross-entity')\r\n                  }\r\n                />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        ))}\r\n\r\n        {availableWidgets.length === 0 && (\r\n          <div className=\"text-center py-8 text-gray-500\">\r\n            <div className=\"text-sm\">\r\n              No widgets available for this data source\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default WidgetPalette;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAAA;;;;;;;AA8BA,MAAM,kBAAkD,CAAC,EACvD,MAAM,EACN,aAAa,KAAK,EACnB;IACC,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,GAChE,CAAA,GAAA,2JAAA,CAAA,eAAY,AAAD,EAAE;QACX,IAAI,OAAO,EAAE;QACb,MAAM;YACJ,MAAM;YACN,YAAY,OAAO,EAAE;QACvB;QACA,UAAU;IACZ;IAEF,MAAM,QAAQ,YACV;QACE,WAAW,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,MAAM,CAAC;IACjE,IACA;IAEJ,qBACE,8OAAC;QACC,KAAK;QACL,OAAO;QACN,GAAG,SAAS;QACZ,GAAG,UAAU;QACd,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,2EACA,cAAc,cACd,aACI,kCACA,yCACJ,CAAC,cAAc;;0BAGjB,8OAAC;gBAAI,WAAU;;oBACZ,OAAO,IAAI;kCACZ,8OAAC;wBAAK,WAAU;kCAAuB,OAAO,IAAI;;;;;;;;;;;;0BAEpD,8OAAC;gBAAE,WAAU;0BAA8B,OAAO,WAAW;;;;;;0BAC7D,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAU,WAAU;0BAChC,OAAO,QAAQ;;;;;;;;;;;;AAIxB;AAEA;;CAEC,GACD,MAAM,eAA6B;IACjC,oBAAoB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,kNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC5B,UAAU;QACV,sBAAsB;YACpB;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,kNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,UAAU;QACV,sBAAsB;YAAC;YAAe;YAAS;YAAY;SAAY;IACzE;IAEA,gBAAgB;IAChB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,kNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,UAAU;QACV,sBAAsB;YAAC;YAAe;YAAS;YAAY;SAAY;IACzE;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,UAAU;QACV,sBAAsB;YAAC;YAAe;YAAS;YAAY;SAAY;IACzE;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,UAAU;QACV,sBAAsB;YAAC;YAAe;YAAS;YAAY;SAAY;IACzE;IAEA,eAAe;IACf;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvB,UAAU;QACV,sBAAsB;YAAC;YAAe;YAAS;YAAY;SAAY;IACzE;IAEA,0BAA0B;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvB,UAAU;QACV,sBAAsB;YAAC;YAAa;SAAe;IACrD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;QACrB,UAAU;QACV,sBAAsB;YAAC;YAAY;SAAe;IACpD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,2NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC7B,UAAU;QACV,sBAAsB;YAAC;YAAS;SAAe;IACjD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,UAAU;QACV,sBAAsB;YAAC;YAAe;SAAe;IACvD;IAEA,sBAAsB;IACtB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,wMAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACzB,UAAU;QACV,sBAAsB;YAAC;SAAe;IACxC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,UAAU;QACV,sBAAsB;YAAC;YAAe;YAAS;YAAY;SAAY;IACzE;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,oBAAM,8OAAC,kNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QAC5B,UAAU;QACV,sBAAsB;YAAC;YAAY;YAAa;SAAe;IACjE;CACD;AAkBM,MAAM,gBAA8C,CAAC,EAC1D,UAAU,EACV,YAAY,EAAE,EACf;IACC,8CAA8C;IAC9C,MAAM,mBAAmB,aAAa,MAAM,CAC1C,CAAA,SACE,OAAO,oBAAoB,CAAC,QAAQ,CAAC,eACrC,OAAO,oBAAoB,CAAC,QAAQ,CAAC;IAGzC,4BAA4B;IAC5B,MAAM,oBAAoB,iBAAiB,MAAM,CAC/C,CAAC,KAAK;QACJ,IAAI,CAAC,GAAG,CAAC,OAAO,QAAQ,CAAC,EAAE;YACzB,GAAG,CAAC,OAAO,QAAQ,CAAC,GAAG,EAAE;QAC3B;QACA,GAAG,CAAC,OAAO,QAAQ,CAAC,CAAE,IAAI,CAAC;QAC3B,OAAO;IACT,GACA,CAAC;IAGH,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAU;;;;;;kCAC/B,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;kCAGrC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAClC,iBAAiB,MAAM;4BAAC;;;;;;;;;;;;;0BAI7B,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;oBACpB,OAAO,OAAO,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC,UAAU,QAAQ,iBACzD,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX;;;;;;8CAEH,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAA,uBACX,8OAAC;4CAEC,QAAQ;4CACR,YACE,CAAC,OAAO,oBAAoB,CAAC,QAAQ,CAAC,eACtC,CAAC,OAAO,oBAAoB,CAAC,QAAQ,CAAC;2CAJnC,OAAO,EAAE;;;;;;;;;;;2BAPZ;;;;;oBAmBX,iBAAiB,MAAM,KAAK,mBAC3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;;AAQrC;uCAEe", "debugId": null}}, {"offset": {"line": 2071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/management/ReportBuilder.tsx"], "sourcesContent": ["/**\r\n * @file ReportBuilder.tsx\r\n * @description Dynamic report builder component following SOLID principles and established patterns\r\n */\r\n\r\nimport type { DragEndEvent, DragStartEvent } from '@dnd-kit/core';\r\n\r\nimport { DndContext, DragOverlay } from '@dnd-kit/core';\r\nimport { Eye, Layout, Save } from 'lucide-react';\r\nimport React, { useState } from 'react';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { cn } from '@/lib/utils';\r\n\r\nimport type { ReportType, WidgetConfig } from '../data/types/reporting';\r\n\r\nimport { DropZone } from './DropZone';\r\nimport { WidgetConfigurator } from './WidgetConfigurator';\r\nimport { WidgetPalette } from './WidgetPalette';\r\n\r\n/**\r\n * Props interface for ReportBuilder\r\n */\r\ninterface ReportBuilderProps {\r\n  className?: string;\r\n  onPreview?: (reportConfig: ReportConfig) => void;\r\n  onSave?: (reportConfig: ReportConfig) => void;\r\n  reportType?: ReportType;\r\n}\r\n\r\n/**\r\n * Report configuration interface\r\n */\r\ninterface ReportConfig {\r\n  dataSource: string;\r\n  description?: string;\r\n  filters: string[];\r\n  layout: {\r\n    columns: number;\r\n    spacing: number;\r\n  };\r\n  name: string;\r\n  widgets: WidgetConfig[];\r\n}\r\n\r\n/**\r\n * ReportBuilder Component\r\n *\r\n * Provides drag-and-drop interface for building custom reports.\r\n *\r\n * Responsibilities:\r\n * - Provide drag-and-drop widget placement\r\n * - Handle widget configuration\r\n * - Manage report layout and structure\r\n * - Follow established component patterns\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of building reports\r\n * - OCP: Open for extension via widget types\r\n * - DIP: Depends on drag-and-drop framework abstractions\r\n */\r\nexport const ReportBuilder: React.FC<ReportBuilderProps> = ({\r\n  className = '',\r\n  onPreview,\r\n  onSave,\r\n  reportType,\r\n}) => {\r\n  // State management\r\n  const [widgets, setWidgets] = useState<WidgetConfig[]>(\r\n    reportType?.widgetConfigs || []\r\n  );\r\n  const [selectedWidget, setSelectedWidget] = useState<null | WidgetConfig>(\r\n    null\r\n  );\r\n  const [isConfiguring, setIsConfiguring] = useState(false);\r\n  const [activeId, setActiveId] = useState<null | string>(null);\r\n  const [reportConfig, setReportConfig] = useState<ReportConfig>({\r\n    dataSource: reportType?.dataSource || 'delegations',\r\n    description: reportType?.description || '',\r\n    filters: reportType?.filters || [],\r\n    layout: {\r\n      columns: 2,\r\n      spacing: 4,\r\n    },\r\n    name: reportType?.name || 'New Report',\r\n    widgets: widgets,\r\n  });\r\n\r\n  // Handle drag start\r\n  const handleDragStart = (event: DragStartEvent) => {\r\n    setActiveId(event.active.id as string);\r\n  };\r\n\r\n  // Handle drag end\r\n  const handleDragEnd = (event: DragEndEvent) => {\r\n    const { active, over } = event;\r\n\r\n    if (!over) {\r\n      setActiveId(null);\r\n      return;\r\n    }\r\n\r\n    // Handle dropping widget from palette to drop zone\r\n    if (\r\n      over.id === 'drop-zone' &&\r\n      active.data.current?.type === 'widget-type'\r\n    ) {\r\n      const widgetType = active.data.current.widgetType;\r\n      const newWidget: WidgetConfig = {\r\n        config: {},\r\n        id: `widget-${Date.now()}`,\r\n        position: widgets.length,\r\n        span: 'col-span-1',\r\n        title: `${widgetType} Widget`,\r\n        type: widgetType,\r\n      };\r\n\r\n      setWidgets(prev => [...prev, newWidget]);\r\n      setReportConfig(prev => ({\r\n        ...prev,\r\n        widgets: [...prev.widgets, newWidget],\r\n      }));\r\n    }\r\n\r\n    // Handle reordering widgets within drop zone\r\n    if (over.id !== active.id && active.data.current?.type === 'widget') {\r\n      const activeIndex = widgets.findIndex(w => w.id === active.id);\r\n      const overIndex = widgets.findIndex(w => w.id === over.id);\r\n\r\n      if (activeIndex !== -1 && overIndex !== -1) {\r\n        const newWidgets = [...widgets];\r\n        const [removed] = newWidgets.splice(activeIndex, 1);\r\n        if (removed) {\r\n          newWidgets.splice(overIndex, 0, removed);\r\n        }\r\n\r\n        // Update positions\r\n        const updatedWidgets = newWidgets.map((widget, index) => ({\r\n          ...widget,\r\n          position: index,\r\n        }));\r\n\r\n        setWidgets(updatedWidgets);\r\n        setReportConfig(prev => ({\r\n          ...prev,\r\n          widgets: updatedWidgets,\r\n        }));\r\n      }\r\n    }\r\n\r\n    setActiveId(null);\r\n  };\r\n\r\n  // Handle widget configuration\r\n  const handleWidgetConfigure = (widget: WidgetConfig) => {\r\n    setSelectedWidget(widget);\r\n    setIsConfiguring(true);\r\n  };\r\n\r\n  // Handle widget update\r\n  const handleWidgetUpdate = (updatedWidget: WidgetConfig) => {\r\n    const updatedWidgets = widgets.map(w =>\r\n      w.id === updatedWidget.id ? updatedWidget : w\r\n    );\r\n\r\n    setWidgets(updatedWidgets);\r\n    setReportConfig(prev => ({\r\n      ...prev,\r\n      widgets: updatedWidgets,\r\n    }));\r\n\r\n    setIsConfiguring(false);\r\n    setSelectedWidget(null);\r\n  };\r\n\r\n  // Handle widget deletion\r\n  const handleWidgetDelete = (widgetId: string) => {\r\n    const updatedWidgets = widgets.filter(w => w.id !== widgetId);\r\n    setWidgets(updatedWidgets);\r\n    setReportConfig(prev => ({\r\n      ...prev,\r\n      widgets: updatedWidgets,\r\n    }));\r\n  };\r\n\r\n  // Handle save\r\n  const handleSave = () => {\r\n    if (onSave) {\r\n      onSave(reportConfig);\r\n    }\r\n  };\r\n\r\n  // Handle preview\r\n  const handlePreview = () => {\r\n    if (onPreview) {\r\n      onPreview(reportConfig);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={cn('space-y-6', className)}>\r\n      {/* Header */}\r\n      <Card>\r\n        <CardHeader className=\"pb-3\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <Layout className=\"size-5\" />\r\n              Report Builder\r\n            </CardTitle>\r\n            <div className=\"flex items-center gap-2\">\r\n              <Badge className=\"text-xs\" variant=\"secondary\">\r\n                {widgets.length} widgets\r\n              </Badge>\r\n              <Button onClick={handlePreview} size=\"sm\" variant=\"outline\">\r\n                <Eye className=\"mr-2 size-4\" />\r\n                Preview\r\n              </Button>\r\n              <Button onClick={handleSave} size=\"sm\">\r\n                <Save className=\"mr-2 size-4\" />\r\n                Save Report\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n\r\n        <CardContent>\r\n          {/* Report Configuration */}\r\n          <div className=\"mb-6 grid grid-cols-1 gap-4 md:grid-cols-3\">\r\n            <div>\r\n              <label className=\"text-sm font-medium\">Report Name</label>\r\n              <input\r\n                className=\"mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                onChange={e =>\r\n                  setReportConfig(prev => ({ ...prev, name: e.target.value }))\r\n                }\r\n                type=\"text\"\r\n                value={reportConfig.name}\r\n              />\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium\">Data Source</label>\r\n              <select\r\n                className=\"mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                onChange={e =>\r\n                  setReportConfig(prev => ({\r\n                    ...prev,\r\n                    dataSource: e.target.value,\r\n                  }))\r\n                }\r\n                value={reportConfig.dataSource}\r\n              >\r\n                <option value=\"delegations\">Delegations</option>\r\n                <option value=\"tasks\">Tasks</option>\r\n                <option value=\"vehicles\">Vehicles</option>\r\n                <option value=\"employees\">Employees</option>\r\n                <option value=\"cross-entity\">Cross-Entity</option>\r\n              </select>\r\n            </div>\r\n            <div>\r\n              <label className=\"text-sm font-medium\">Layout Columns</label>\r\n              <select\r\n                className=\"mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                onChange={e =>\r\n                  setReportConfig(prev => ({\r\n                    ...prev,\r\n                    layout: {\r\n                      ...prev.layout,\r\n                      columns: Number.parseInt(e.target.value),\r\n                    },\r\n                  }))\r\n                }\r\n                value={reportConfig.layout.columns}\r\n              >\r\n                <option value={1}>1 Column</option>\r\n                <option value={2}>2 Columns</option>\r\n                <option value={3}>3 Columns</option>\r\n                <option value={4}>4 Columns</option>\r\n              </select>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Builder Interface */}\r\n      <DndContext onDragEnd={handleDragEnd} onDragStart={handleDragStart}>\r\n        <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-4\">\r\n          {/* Widget Palette */}\r\n          <div className=\"lg:col-span-1\">\r\n            <WidgetPalette dataSource={reportConfig.dataSource} />\r\n          </div>\r\n\r\n          {/* Drop Zone */}\r\n          <div className=\"lg:col-span-3\">\r\n            <DropZone\r\n              columns={reportConfig.layout.columns}\r\n              onWidgetConfigure={handleWidgetConfigure}\r\n              onWidgetDelete={handleWidgetDelete}\r\n              widgets={widgets}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Drag Overlay */}\r\n        <DragOverlay>\r\n          {activeId ? (\r\n            <div className=\"rounded-lg border bg-white p-4 shadow-lg\">\r\n              <div className=\"text-sm font-medium\">\r\n                {activeId\r\n                  .replace('-', ' ')\r\n                  .replaceAll(/\\b\\w/g, l => l.toUpperCase())}\r\n              </div>\r\n            </div>\r\n          ) : null}\r\n        </DragOverlay>\r\n      </DndContext>\r\n\r\n      {/* Widget Configurator */}\r\n      {isConfiguring && selectedWidget && (\r\n        <WidgetConfigurator\r\n          onCancel={() => {\r\n            setIsConfiguring(false);\r\n            setSelectedWidget(null);\r\n          }}\r\n          onSave={handleWidgetUpdate}\r\n          widget={selectedWidget}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReportBuilder;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAID;AACA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAIA;AACA;AACA;;;;;;;;;;;;AA2CO,MAAM,gBAA8C,CAAC,EAC1D,YAAY,EAAE,EACd,SAAS,EACT,MAAM,EACN,UAAU,EACX;IACC,mBAAmB;IACnB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACnC,YAAY,iBAAiB,EAAE;IAEjC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACjD;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,YAAY,YAAY,cAAc;QACtC,aAAa,YAAY,eAAe;QACxC,SAAS,YAAY,WAAW,EAAE;QAClC,QAAQ;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM,YAAY,QAAQ;QAC1B,SAAS;IACX;IAEA,oBAAoB;IACpB,MAAM,kBAAkB,CAAC;QACvB,YAAY,MAAM,MAAM,CAAC,EAAE;IAC7B;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,IAAI,CAAC,MAAM;YACT,YAAY;YACZ;QACF;QAEA,mDAAmD;QACnD,IACE,KAAK,EAAE,KAAK,eACZ,OAAO,IAAI,CAAC,OAAO,EAAE,SAAS,eAC9B;YACA,MAAM,aAAa,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU;YACjD,MAAM,YAA0B;gBAC9B,QAAQ,CAAC;gBACT,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;gBAC1B,UAAU,QAAQ,MAAM;gBACxB,MAAM;gBACN,OAAO,GAAG,WAAW,OAAO,CAAC;gBAC7B,MAAM;YACR;YAEA,WAAW,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;YACvC,gBAAgB,CAAA,OAAQ,CAAC;oBACvB,GAAG,IAAI;oBACP,SAAS;2BAAI,KAAK,OAAO;wBAAE;qBAAU;gBACvC,CAAC;QACH;QAEA,6CAA6C;QAC7C,IAAI,KAAK,EAAE,KAAK,OAAO,EAAE,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE,SAAS,UAAU;YACnE,MAAM,cAAc,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,EAAE;YAC7D,MAAM,YAAY,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;YAEzD,IAAI,gBAAgB,CAAC,KAAK,cAAc,CAAC,GAAG;gBAC1C,MAAM,aAAa;uBAAI;iBAAQ;gBAC/B,MAAM,CAAC,QAAQ,GAAG,WAAW,MAAM,CAAC,aAAa;gBACjD,IAAI,SAAS;oBACX,WAAW,MAAM,CAAC,WAAW,GAAG;gBAClC;gBAEA,mBAAmB;gBACnB,MAAM,iBAAiB,WAAW,GAAG,CAAC,CAAC,QAAQ,QAAU,CAAC;wBACxD,GAAG,MAAM;wBACT,UAAU;oBACZ,CAAC;gBAED,WAAW;gBACX,gBAAgB,CAAA,OAAQ,CAAC;wBACvB,GAAG,IAAI;wBACP,SAAS;oBACX,CAAC;YACH;QACF;QAEA,YAAY;IACd;IAEA,8BAA8B;IAC9B,MAAM,wBAAwB,CAAC;QAC7B,kBAAkB;QAClB,iBAAiB;IACnB;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC;QAC1B,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA,IACjC,EAAE,EAAE,KAAK,cAAc,EAAE,GAAG,gBAAgB;QAG9C,WAAW;QACX,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,SAAS;YACX,CAAC;QAED,iBAAiB;QACjB,kBAAkB;IACpB;IAEA,yBAAyB;IACzB,MAAM,qBAAqB,CAAC;QAC1B,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,WAAW;QACX,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,SAAS;YACX,CAAC;IACH;IAEA,cAAc;IACd,MAAM,aAAa;QACjB,IAAI,QAAQ;YACV,OAAO;QACT;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,IAAI,WAAW;YACb,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,qNAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAW;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;4CAAU,SAAQ;;gDAChC,QAAQ,MAAM;gDAAC;;;;;;;sDAElB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAe,MAAK;4CAAK,SAAQ;;8DAChD,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAgB;;;;;;;sDAGjC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAY,MAAK;;8DAChC,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAOxC,8OAAC,gIAAA,CAAA,cAAW;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CACC,WAAU;4CACV,UAAU,CAAA,IACR,gBAAgB,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAE5D,MAAK;4CACL,OAAO,aAAa,IAAI;;;;;;;;;;;;8CAG5B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CACC,WAAU;4CACV,UAAU,CAAA,IACR,gBAAgB,CAAA,OAAQ,CAAC;wDACvB,GAAG,IAAI;wDACP,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC5B,CAAC;4CAEH,OAAO,aAAa,UAAU;;8DAE9B,8OAAC;oDAAO,OAAM;8DAAc;;;;;;8DAC5B,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAe;;;;;;;;;;;;;;;;;;8CAGjC,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CACC,WAAU;4CACV,UAAU,CAAA,IACR,gBAAgB,CAAA,OAAQ,CAAC;wDACvB,GAAG,IAAI;wDACP,QAAQ;4DACN,GAAG,KAAK,MAAM;4DACd,SAAS,OAAO,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK;wDACzC;oDACF,CAAC;4CAEH,OAAO,aAAa,MAAM,CAAC,OAAO;;8DAElC,8OAAC;oDAAO,OAAO;8DAAG;;;;;;8DAClB,8OAAC;oDAAO,OAAO;8DAAG;;;;;;8DAClB,8OAAC;oDAAO,OAAO;8DAAG;;;;;;8DAClB,8OAAC;oDAAO,OAAO;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,8OAAC,2JAAA,CAAA,aAAU;gBAAC,WAAW;gBAAe,aAAa;;kCACjD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0KAAA,CAAA,gBAAa;oCAAC,YAAY,aAAa,UAAU;;;;;;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,qKAAA,CAAA,WAAQ;oCACP,SAAS,aAAa,MAAM,CAAC,OAAO;oCACpC,mBAAmB;oCACnB,gBAAgB;oCAChB,SAAS;;;;;;;;;;;;;;;;;kCAMf,8OAAC,2JAAA,CAAA,cAAW;kCACT,yBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ,SACE,OAAO,CAAC,KAAK,KACb,UAAU,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;;;;;;;;;;mCAG3C;;;;;;;;;;;;YAKP,iBAAiB,gCAChB,8OAAC,+KAAA,CAAA,qBAAkB;gBACjB,UAAU;oBACR,iBAAiB;oBACjB,kBAAkB;gBACpB;gBACA,QAAQ;gBACR,QAAQ;;;;;;;;;;;;AAKlB;uCAEe", "debugId": null}}, {"offset": {"line": 2595, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/management/ReportTypeForm.tsx"], "sourcesContent": ["/**\r\n * @file ReportTypeForm.tsx\r\n * @description Report type form component following established form patterns and SOLID principles\r\n */\r\n\r\nimport React from 'react';\r\nimport { useForm } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport { z } from 'zod';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from '@/components/ui/form';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { \r\n  FileText, \r\n  X,\r\n  Plus,\r\n  Trash2\r\n} from 'lucide-react';\r\nimport type { ReportType } from '../data/types/reporting';\r\n\r\n/**\r\n * Form validation schema\r\n */\r\nconst reportTypeSchema = z.object({\r\n  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),\r\n  description: z.string().optional(),\r\n  category: z.string().min(1, 'Category is required'),\r\n  dataSource: z.string().min(1, 'Data source is required'),\r\n  widgets: z.array(z.string()).min(1, 'At least one widget is required'),\r\n  filters: z.array(z.string()).optional(),\r\n  isActive: z.boolean().default(true),\r\n  isPublic: z.boolean().default(false),\r\n  refreshInterval: z.number().min(1).max(1440).optional(), // 1 minute to 24 hours\r\n  tags: z.array(z.string()).optional(),\r\n});\r\n\r\ntype ReportTypeFormData = z.infer<typeof reportTypeSchema>;\r\n\r\n/**\r\n * Props interface for ReportTypeForm\r\n */\r\ninterface ReportTypeFormProps {\r\n  reportType?: ReportType | null;\r\n  onSubmit: (data: ReportTypeFormData) => Promise<void>;\r\n  onCancel: () => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\n/**\r\n * Available widget types\r\n */\r\nconst WIDGET_TYPES = [\r\n  { value: 'analytics', label: 'Analytics Widget' },\r\n  { value: 'chart', label: 'Chart Widget' },\r\n  { value: 'table', label: 'Data Table' },\r\n  { value: 'metrics', label: 'Metrics Widget' },\r\n  { value: 'correlation', label: 'Correlation Widget' },\r\n];\r\n\r\n/**\r\n * Available data sources\r\n */\r\nconst DATA_SOURCES = [\r\n  { value: 'delegations', label: 'Delegations' },\r\n  { value: 'tasks', label: 'Tasks' },\r\n  { value: 'vehicles', label: 'Vehicles' },\r\n  { value: 'employees', label: 'Employees' },\r\n  { value: 'cross-entity', label: 'Cross-Entity' },\r\n];\r\n\r\n/**\r\n * Available categories\r\n */\r\nconst CATEGORIES = [\r\n  { value: 'operational', label: 'Operational' },\r\n  { value: 'performance', label: 'Performance' },\r\n  { value: 'financial', label: 'Financial' },\r\n  { value: 'compliance', label: 'Compliance' },\r\n  { value: 'analytics', label: 'Analytics' },\r\n];\r\n\r\n/**\r\n * ReportTypeForm Component\r\n * \r\n * Form for creating and editing report types following established form patterns.\r\n * \r\n * Responsibilities:\r\n * - Handle report type creation and editing\r\n * - Validate form data using Zod schema\r\n * - Follow established form component patterns\r\n * - Maintain consistent styling and behavior\r\n * \r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of handling report type form\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on established form framework abstractions\r\n */\r\nexport const ReportTypeForm: React.FC<ReportTypeFormProps> = ({\r\n  reportType,\r\n  onSubmit,\r\n  onCancel,\r\n  isLoading = false\r\n}) => {\r\n  // Initialize form with existing data or defaults\r\n  const form = useForm<ReportTypeFormData>({\r\n    resolver: zodResolver(reportTypeSchema),\r\n    defaultValues: {\r\n      name: reportType?.name || '',\r\n      description: reportType?.description || '',\r\n      category: reportType?.category || '',\r\n      dataSource: reportType?.dataSource || '',\r\n      widgets: reportType?.widgets || [],\r\n      filters: reportType?.filters || [],\r\n      isActive: reportType?.isActive ?? true,\r\n      isPublic: reportType?.isPublic ?? false,\r\n      refreshInterval: reportType?.refreshInterval || 60,\r\n      tags: reportType?.tags || [],\r\n    },\r\n  });\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (data: ReportTypeFormData) => {\r\n    try {\r\n      await onSubmit(data);\r\n    } catch (error) {\r\n      console.error('Form submission error:', error);\r\n    }\r\n  };\r\n\r\n  // Handle tag management\r\n  const addTag = (tag: string) => {\r\n    const currentTags = form.getValues('tags') || [];\r\n    if (tag && !currentTags.includes(tag)) {\r\n      form.setValue('tags', [...currentTags, tag]);\r\n    }\r\n  };\r\n\r\n  const removeTag = (tagToRemove: string) => {\r\n    const currentTags = form.getValues('tags') || [];\r\n    form.setValue('tags', currentTags.filter(tag => tag !== tagToRemove));\r\n  };\r\n\r\n  return (\r\n    <Dialog open={true} onOpenChange={onCancel}>\r\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"flex items-center gap-2\">\r\n            <FileText className=\"h-5 w-5\" />\r\n            {reportType ? 'Edit Report Type' : 'Create Report Type'}\r\n          </DialogTitle>\r\n          <DialogDescription>\r\n            {reportType \r\n              ? 'Update the report type configuration and settings.'\r\n              : 'Create a new report type with custom widgets and data sources.'\r\n            }\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <Form {...form}>\r\n          <form onSubmit={form.handleSubmit(handleSubmit)} className=\"space-y-6\">\r\n            {/* Basic Information */}\r\n            <div className=\"space-y-4\">\r\n              <h3 className=\"text-lg font-medium\">Basic Information</h3>\r\n              \r\n              <FormField\r\n                control={form.control}\r\n                name=\"name\"\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <FormLabel>Name *</FormLabel>\r\n                    <FormControl>\r\n                      <Input placeholder=\"Enter report type name\" {...field} />\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n\r\n              <FormField\r\n                control={form.control}\r\n                name=\"description\"\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <FormLabel>Description</FormLabel>\r\n                    <FormControl>\r\n                      <Textarea \r\n                        placeholder=\"Describe what this report type shows\"\r\n                        rows={3}\r\n                        {...field} \r\n                      />\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n\r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"category\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Category *</FormLabel>\r\n                      <Select onValueChange={field.onChange} defaultValue={field.value}>\r\n                        <FormControl>\r\n                          <SelectTrigger>\r\n                            <SelectValue placeholder=\"Select category\" />\r\n                          </SelectTrigger>\r\n                        </FormControl>\r\n                        <SelectContent>\r\n                          {CATEGORIES.map((category) => (\r\n                            <SelectItem key={category.value} value={category.value}>\r\n                              {category.label}\r\n                            </SelectItem>\r\n                          ))}\r\n                        </SelectContent>\r\n                      </Select>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"dataSource\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Data Source *</FormLabel>\r\n                      <Select onValueChange={field.onChange} defaultValue={field.value}>\r\n                        <FormControl>\r\n                          <SelectTrigger>\r\n                            <SelectValue placeholder=\"Select data source\" />\r\n                          </SelectTrigger>\r\n                        </FormControl>\r\n                        <SelectContent>\r\n                          {DATA_SOURCES.map((source) => (\r\n                            <SelectItem key={source.value} value={source.value}>\r\n                              {source.label}\r\n                            </SelectItem>\r\n                          ))}\r\n                        </SelectContent>\r\n                      </Select>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Widget Configuration */}\r\n            <div className=\"space-y-4\">\r\n              <h3 className=\"text-lg font-medium\">Widget Configuration</h3>\r\n              \r\n              <FormField\r\n                control={form.control}\r\n                name=\"widgets\"\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <FormLabel>Widgets *</FormLabel>\r\n                    <FormDescription>\r\n                      Select the widgets to include in this report type\r\n                    </FormDescription>\r\n                    <div className=\"grid grid-cols-2 gap-2\">\r\n                      {WIDGET_TYPES.map((widget) => (\r\n                        <div key={widget.value} className=\"flex items-center space-x-2\">\r\n                          <Checkbox\r\n                            id={widget.value}\r\n                            checked={field.value?.includes(widget.value)}\r\n                            onCheckedChange={(checked) => {\r\n                              const currentWidgets = field.value || [];\r\n                              if (checked) {\r\n                                field.onChange([...currentWidgets, widget.value]);\r\n                              } else {\r\n                                field.onChange(currentWidgets.filter(w => w !== widget.value));\r\n                              }\r\n                            }}\r\n                          />\r\n                          <label htmlFor={widget.value} className=\"text-sm\">\r\n                            {widget.label}\r\n                          </label>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n            </div>\r\n\r\n            {/* Settings */}\r\n            <div className=\"space-y-4\">\r\n              <h3 className=\"text-lg font-medium\">Settings</h3>\r\n              \r\n              <div className=\"grid grid-cols-2 gap-4\">\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"isActive\"\r\n                  render={({ field }) => (\r\n                    <FormItem className=\"flex flex-row items-center justify-between rounded-lg border p-3\">\r\n                      <div className=\"space-y-0.5\">\r\n                        <FormLabel>Active</FormLabel>\r\n                        <FormDescription>\r\n                          Enable this report type for use\r\n                        </FormDescription>\r\n                      </div>\r\n                      <FormControl>\r\n                        <Checkbox\r\n                          checked={field.value}\r\n                          onCheckedChange={field.onChange}\r\n                        />\r\n                      </FormControl>\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"isPublic\"\r\n                  render={({ field }) => (\r\n                    <FormItem className=\"flex flex-row items-center justify-between rounded-lg border p-3\">\r\n                      <div className=\"space-y-0.5\">\r\n                        <FormLabel>Public</FormLabel>\r\n                        <FormDescription>\r\n                          Make available to all users\r\n                        </FormDescription>\r\n                      </div>\r\n                      <FormControl>\r\n                        <Checkbox\r\n                          checked={field.value}\r\n                          onCheckedChange={field.onChange}\r\n                        />\r\n                      </FormControl>\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n              </div>\r\n\r\n              <FormField\r\n                control={form.control}\r\n                name=\"refreshInterval\"\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <FormLabel>Refresh Interval (minutes)</FormLabel>\r\n                    <FormControl>\r\n                      <Input \r\n                        type=\"number\" \r\n                        min=\"1\" \r\n                        max=\"1440\"\r\n                        placeholder=\"60\"\r\n                        {...field}\r\n                        onChange={(e) => field.onChange(parseInt(e.target.value) || 60)}\r\n                      />\r\n                    </FormControl>\r\n                    <FormDescription>\r\n                      How often the report data should refresh (1-1440 minutes)\r\n                    </FormDescription>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n            </div>\r\n\r\n            <DialogFooter>\r\n              <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\r\n                Cancel\r\n              </Button>\r\n              <Button type=\"submit\" disabled={isLoading}>\r\n                {isLoading ? 'Saving...' : reportType ? 'Update' : 'Create'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </Form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n};\r\n\r\nexport default ReportTypeForm;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AACA;AAAA;AACA;AAQA;AASA;AACA;AACA;AAEA;AAOA;AACA;;;;;;;;;;;;;AAQA;;CAEC,GACD,MAAM,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,MAAM,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,KAAK;IACrD,aAAa,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,UAAU,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,YAAY,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC9B,SAAS,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG;IACpC,SAAS,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;IACrC,UAAU,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,UAAU,iLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC9B,iBAAiB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,QAAQ;IACrD,MAAM,iLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,iLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;AACpC;AAcA;;CAEC,GACD,MAAM,eAAe;IACnB;QAAE,OAAO;QAAa,OAAO;IAAmB;IAChD;QAAE,OAAO;QAAS,OAAO;IAAe;IACxC;QAAE,OAAO;QAAS,OAAO;IAAa;IACtC;QAAE,OAAO;QAAW,OAAO;IAAiB;IAC5C;QAAE,OAAO;QAAe,OAAO;IAAqB;CACrD;AAED;;CAEC,GACD,MAAM,eAAe;IACnB;QAAE,OAAO;QAAe,OAAO;IAAc;IAC7C;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAgB,OAAO;IAAe;CAChD;AAED;;CAEC,GACD,MAAM,aAAa;IACjB;QAAE,OAAO;QAAe,OAAO;IAAc;IAC7C;QAAE,OAAO;QAAe,OAAO;IAAc;IAC7C;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAkBM,MAAM,iBAAgD,CAAC,EAC5D,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,YAAY,KAAK,EAClB;IACC,iDAAiD;IACjD,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAsB;QACvC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM,YAAY,QAAQ;YAC1B,aAAa,YAAY,eAAe;YACxC,UAAU,YAAY,YAAY;YAClC,YAAY,YAAY,cAAc;YACtC,SAAS,YAAY,WAAW,EAAE;YAClC,SAAS,YAAY,WAAW,EAAE;YAClC,UAAU,YAAY,YAAY;YAClC,UAAU,YAAY,YAAY;YAClC,iBAAiB,YAAY,mBAAmB;YAChD,MAAM,YAAY,QAAQ,EAAE;QAC9B;IACF;IAEA,yBAAyB;IACzB,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,SAAS;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,wBAAwB;IACxB,MAAM,SAAS,CAAC;QACd,MAAM,cAAc,KAAK,SAAS,CAAC,WAAW,EAAE;QAChD,IAAI,OAAO,CAAC,YAAY,QAAQ,CAAC,MAAM;YACrC,KAAK,QAAQ,CAAC,QAAQ;mBAAI;gBAAa;aAAI;QAC7C;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,MAAM,cAAc,KAAK,SAAS,CAAC,WAAW,EAAE;QAChD,KAAK,QAAQ,CAAC,QAAQ,YAAY,MAAM,CAAC,CAAA,MAAO,QAAQ;IAC1D;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCACnB,aAAa,qBAAqB;;;;;;;sCAErC,8OAAC,kIAAA,CAAA,oBAAiB;sCACf,aACG,uDACA;;;;;;;;;;;;8BAKR,8OAAC,gIAAA,CAAA,OAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,8OAAC;wBAAK,UAAU,KAAK,YAAY,CAAC;wBAAe,WAAU;;0CAEzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDAEpC,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,aAAY;4DAA0B,GAAG,KAAK;;;;;;;;;;;kEAEvD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAKlB,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4DACP,aAAY;4DACZ,MAAM;4DACL,GAAG,KAAK;;;;;;;;;;;kEAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAKlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,kIAAA,CAAA,SAAM;gEAAC,eAAe,MAAM,QAAQ;gEAAE,cAAc,MAAM,KAAK;;kFAC9D,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;sFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG7B,8OAAC,kIAAA,CAAA,gBAAa;kFACX,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,aAAU;gFAAsB,OAAO,SAAS,KAAK;0FACnD,SAAS,KAAK;+EADA,SAAS,KAAK;;;;;;;;;;;;;;;;0EAMrC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0DAKlB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0EACP,8OAAC,gIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,kIAAA,CAAA,SAAM;gEAAC,eAAe,MAAM,QAAQ;gEAAE,cAAc,MAAM,KAAK;;kFAC9D,8OAAC,gIAAA,CAAA,cAAW;kFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;sFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gFAAC,aAAY;;;;;;;;;;;;;;;;kFAG7B,8OAAC,kIAAA,CAAA,gBAAa;kFACX,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC,kIAAA,CAAA,aAAU;gFAAoB,OAAO,OAAO,KAAK;0FAC/C,OAAO,KAAK;+EADE,OAAO,KAAK;;;;;;;;;;;;;;;;0EAMnC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDAEpC,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;kEAGjB,8OAAC;wDAAI,WAAU;kEACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,8OAAC;gEAAuB,WAAU;;kFAChC,8OAAC,oIAAA,CAAA,WAAQ;wEACP,IAAI,OAAO,KAAK;wEAChB,SAAS,MAAM,KAAK,EAAE,SAAS,OAAO,KAAK;wEAC3C,iBAAiB,CAAC;4EAChB,MAAM,iBAAiB,MAAM,KAAK,IAAI,EAAE;4EACxC,IAAI,SAAS;gFACX,MAAM,QAAQ,CAAC;uFAAI;oFAAgB,OAAO,KAAK;iFAAC;4EAClD,OAAO;gFACL,MAAM,QAAQ,CAAC,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM,OAAO,KAAK;4EAC9D;wEACF;;;;;;kFAEF,8OAAC;wEAAM,SAAS,OAAO,KAAK;wEAAE,WAAU;kFACrC,OAAO,KAAK;;;;;;;+DAdP,OAAO,KAAK;;;;;;;;;;kEAmB1B,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0CAOpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDAEpC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,kBAAe;kFAAC;;;;;;;;;;;;0EAInB,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,SAAS,MAAM,KAAK;oEACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;0DAOzC,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,gIAAA,CAAA,kBAAe;kFAAC;;;;;;;;;;;;0EAInB,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,SAAS,MAAM,KAAK;oEACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ3C,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,aAAY;4DACX,GAAG,KAAK;4DACT,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;kEAGhE,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;kEAGjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0CAMpB,8OAAC,kIAAA,CAAA,eAAY;;kDACX,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,SAAQ;wCAAU,SAAS;kDAAU;;;;;;kDAG3D,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,UAAU;kDAC7B,YAAY,cAAc,aAAa,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnE;uCAEe", "debugId": null}}, {"offset": {"line": 3389, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/management/ReportTypeCard.tsx"], "sourcesContent": ["/**\r\n * @file ReportTypeCard.tsx\r\n * @description Report type card component following established card patterns and SOLID principles\r\n */\r\n\r\nimport React from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { \r\n  FileText, \r\n  Edit, \r\n  Trash2, \r\n  Copy,\r\n  Play,\r\n  Pause,\r\n  MoreHorizontal,\r\n  Clock,\r\n  Users,\r\n  Eye\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { format } from 'date-fns';\r\nimport type { ReportType } from '../data/types/reporting';\r\n\r\n/**\r\n * Props interface for ReportTypeCard\r\n */\r\ninterface ReportTypeCardProps {\r\n  reportType: ReportType;\r\n  onSelect?: (reportType: ReportType) => void;\r\n  onEdit?: (reportType: ReportType) => void;\r\n  onDelete?: (reportType: ReportType) => void;\r\n  onDuplicate?: (reportType: ReportType) => void;\r\n  onToggleActive?: (reportType: ReportType) => void;\r\n  className?: string;\r\n  showActions?: boolean;\r\n}\r\n\r\n/**\r\n * Get category color based on category type\r\n */\r\nconst getCategoryColor = (category: string): string => {\r\n  switch (category.toLowerCase()) {\r\n    case 'operational':\r\n      return 'bg-blue-100 text-blue-800';\r\n    case 'performance':\r\n      return 'bg-green-100 text-green-800';\r\n    case 'financial':\r\n      return 'bg-yellow-100 text-yellow-800';\r\n    case 'compliance':\r\n      return 'bg-red-100 text-red-800';\r\n    case 'analytics':\r\n      return 'bg-purple-100 text-purple-800';\r\n    default:\r\n      return 'bg-gray-100 text-gray-800';\r\n  }\r\n};\r\n\r\n/**\r\n * Get data source icon\r\n */\r\nconst getDataSourceIcon = (dataSource: string) => {\r\n  switch (dataSource.toLowerCase()) {\r\n    case 'delegations':\r\n      return <FileText className=\"h-4 w-4\" />;\r\n    case 'tasks':\r\n      return <FileText className=\"h-4 w-4\" />;\r\n    case 'vehicles':\r\n      return <FileText className=\"h-4 w-4\" />;\r\n    case 'employees':\r\n      return <Users className=\"h-4 w-4\" />;\r\n    default:\r\n      return <FileText className=\"h-4 w-4\" />;\r\n  }\r\n};\r\n\r\n/**\r\n * ReportTypeCard Component\r\n * \r\n * Displays report type information in a card format following established patterns.\r\n * \r\n * Responsibilities:\r\n * - Display report type details in card format\r\n * - Handle user interactions (select, edit, delete, etc.)\r\n * - Follow established card component patterns\r\n * - Maintain consistent styling and behavior\r\n * \r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying report type information\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on established UI component abstractions\r\n */\r\nexport const ReportTypeCard: React.FC<ReportTypeCardProps> = ({\r\n  reportType,\r\n  onSelect,\r\n  onEdit,\r\n  onDelete,\r\n  onDuplicate,\r\n  onToggleActive,\r\n  className = '',\r\n  showActions = true\r\n}) => {\r\n  // Handle card click\r\n  const handleCardClick = () => {\r\n    if (onSelect) {\r\n      onSelect(reportType);\r\n    }\r\n  };\r\n\r\n  // Handle action clicks (prevent event bubbling)\r\n  const handleActionClick = (e: React.MouseEvent, action: () => void) => {\r\n    e.stopPropagation();\r\n    action();\r\n  };\r\n\r\n  return (\r\n    <Card \r\n      className={cn(\r\n        'cursor-pointer transition-all duration-200 hover:shadow-md',\r\n        !reportType.isActive && 'opacity-60',\r\n        className\r\n      )}\r\n      onClick={handleCardClick}\r\n    >\r\n      <CardHeader className=\"pb-3\">\r\n        <div className=\"flex items-start justify-between\">\r\n          <div className=\"flex-1\">\r\n            <CardTitle className=\"text-lg flex items-center gap-2\">\r\n              {getDataSourceIcon(reportType.dataSource)}\r\n              {reportType.name}\r\n            </CardTitle>\r\n            <div className=\"flex items-center gap-2 mt-2\">\r\n              <Badge className={cn('text-xs', getCategoryColor(reportType.category))}>\r\n                {reportType.category}\r\n              </Badge>\r\n              {!reportType.isActive && (\r\n                <Badge variant=\"secondary\" className=\"text-xs\">\r\n                  Inactive\r\n                </Badge>\r\n              )}\r\n              {reportType.isPublic && (\r\n                <Badge variant=\"outline\" className=\"text-xs\">\r\n                  <Eye className=\"h-3 w-3 mr-1\" />\r\n                  Public\r\n                </Badge>\r\n              )}\r\n            </div>\r\n          </div>\r\n          \r\n          {showActions && (\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <Button \r\n                  variant=\"ghost\" \r\n                  size=\"sm\"\r\n                  onClick={(e) => e.stopPropagation()}\r\n                >\r\n                  <MoreHorizontal className=\"h-4 w-4\" />\r\n                </Button>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent align=\"end\">\r\n                {onSelect && (\r\n                  <DropdownMenuItem onClick={(e) => handleActionClick(e, () => onSelect(reportType))}>\r\n                    <Play className=\"h-4 w-4 mr-2\" />\r\n                    Use Report Type\r\n                  </DropdownMenuItem>\r\n                )}\r\n                {onEdit && (\r\n                  <DropdownMenuItem onClick={(e) => handleActionClick(e, () => onEdit(reportType))}>\r\n                    <Edit className=\"h-4 w-4 mr-2\" />\r\n                    Edit\r\n                  </DropdownMenuItem>\r\n                )}\r\n                {onDuplicate && (\r\n                  <DropdownMenuItem onClick={(e) => handleActionClick(e, () => onDuplicate(reportType))}>\r\n                    <Copy className=\"h-4 w-4 mr-2\" />\r\n                    Duplicate\r\n                  </DropdownMenuItem>\r\n                )}\r\n                {onToggleActive && (\r\n                  <DropdownMenuItem onClick={(e) => handleActionClick(e, () => onToggleActive(reportType))}>\r\n                    {reportType.isActive ? (\r\n                      <>\r\n                        <Pause className=\"h-4 w-4 mr-2\" />\r\n                        Deactivate\r\n                      </>\r\n                    ) : (\r\n                      <>\r\n                        <Play className=\"h-4 w-4 mr-2\" />\r\n                        Activate\r\n                      </>\r\n                    )}\r\n                  </DropdownMenuItem>\r\n                )}\r\n                {onDelete && (\r\n                  <>\r\n                    <DropdownMenuSeparator />\r\n                    <DropdownMenuItem \r\n                      onClick={(e) => handleActionClick(e, () => onDelete(reportType))}\r\n                      className=\"text-red-600\"\r\n                    >\r\n                      <Trash2 className=\"h-4 w-4 mr-2\" />\r\n                      Delete\r\n                    </DropdownMenuItem>\r\n                  </>\r\n                )}\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          )}\r\n        </div>\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Description */}\r\n        {reportType.description && (\r\n          <p className=\"text-sm text-gray-600 line-clamp-2\">\r\n            {reportType.description}\r\n          </p>\r\n        )}\r\n\r\n        {/* Widgets */}\r\n        <div>\r\n          <h4 className=\"text-sm font-medium mb-2\">Widgets ({reportType.widgets?.length || 0})</h4>\r\n          <div className=\"flex flex-wrap gap-1\">\r\n            {reportType.widgets?.slice(0, 3).map((widget, index) => (\r\n              <Badge key={index} variant=\"outline\" className=\"text-xs\">\r\n                {widget}\r\n              </Badge>\r\n            ))}\r\n            {(reportType.widgets?.length || 0) > 3 && (\r\n              <Badge variant=\"outline\" className=\"text-xs\">\r\n                +{(reportType.widgets?.length || 0) - 3} more\r\n              </Badge>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Data Source and Refresh Info */}\r\n        <div className=\"flex items-center justify-between text-xs text-gray-500\">\r\n          <div className=\"flex items-center gap-1\">\r\n            {getDataSourceIcon(reportType.dataSource)}\r\n            <span>{reportType.dataSource}</span>\r\n          </div>\r\n          {reportType.refreshInterval && (\r\n            <div className=\"flex items-center gap-1\">\r\n              <Clock className=\"h-3 w-3\" />\r\n              <span>{reportType.refreshInterval}m</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Tags */}\r\n        {reportType.tags && reportType.tags.length > 0 && (\r\n          <div className=\"flex flex-wrap gap-1\">\r\n            {reportType.tags.slice(0, 3).map((tag, index) => (\r\n              <Badge key={index} variant=\"secondary\" className=\"text-xs\">\r\n                {tag}\r\n              </Badge>\r\n            ))}\r\n            {reportType.tags.length > 3 && (\r\n              <Badge variant=\"secondary\" className=\"text-xs\">\r\n                +{reportType.tags.length - 3}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Metadata */}\r\n        <div className=\"pt-2 border-t text-xs text-gray-500\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <span>\r\n              Created: {reportType.createdAt ? format(new Date(reportType.createdAt), 'MMM dd, yyyy') : 'Unknown'}\r\n            </span>\r\n            {reportType.updatedAt && (\r\n              <span>\r\n                Updated: {format(new Date(reportType.updatedAt), 'MMM dd')}\r\n              </span>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default ReportTypeCard;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAGD;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAAA;AACA;;;;;;;;;AAiBA;;CAEC,GACD,MAAM,mBAAmB,CAAC;IACxB,OAAQ,SAAS,WAAW;QAC1B,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA;;CAEC,GACD,MAAM,oBAAoB,CAAC;IACzB,OAAQ,WAAW,WAAW;QAC5B,KAAK;YACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC7B,KAAK;YACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC7B,KAAK;YACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;QAC7B,KAAK;YACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC1B;YACE,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;IAC/B;AACF;AAkBO,MAAM,iBAAgD,CAAC,EAC5D,UAAU,EACV,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,WAAW,EACX,cAAc,EACd,YAAY,EAAE,EACd,cAAc,IAAI,EACnB;IACC,oBAAoB;IACpB,MAAM,kBAAkB;QACtB,IAAI,UAAU;YACZ,SAAS;QACX;IACF;IAEA,gDAAgD;IAChD,MAAM,oBAAoB,CAAC,GAAqB;QAC9C,EAAE,eAAe;QACjB;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QACH,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACV,8DACA,CAAC,WAAW,QAAQ,IAAI,cACxB;QAEF,SAAS;;0BAET,8OAAC,gIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;wCAClB,kBAAkB,WAAW,UAAU;wCACvC,WAAW,IAAI;;;;;;;8CAElB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,WAAW,iBAAiB,WAAW,QAAQ;sDACjE,WAAW,QAAQ;;;;;;wCAErB,CAAC,WAAW,QAAQ,kBACnB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAAU;;;;;;wCAIhD,WAAW,QAAQ,kBAClB,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;;8DACjC,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;wBAOvC,6BACC,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,CAAC,IAAM,EAAE,eAAe;kDAEjC,cAAA,8OAAC,gNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG9B,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;;wCACxB,0BACC,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS,CAAC,IAAM,kBAAkB,GAAG,IAAM,SAAS;;8DACpE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAIpC,wBACC,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS,CAAC,IAAM,kBAAkB,GAAG,IAAM,OAAO;;8DAClE,8OAAC,2MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAIpC,6BACC,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS,CAAC,IAAM,kBAAkB,GAAG,IAAM,YAAY;;8DACvE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAIpC,gCACC,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS,CAAC,IAAM,kBAAkB,GAAG,IAAM,eAAe;sDACzE,WAAW,QAAQ,iBAClB;;kEACE,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;6EAIpC;;kEACE,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;wCAMxC,0BACC;;8DACE,8OAAC,4IAAA,CAAA,wBAAqB;;;;;8DACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDACf,SAAS,CAAC,IAAM,kBAAkB,GAAG,IAAM,SAAS;oDACpD,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWnD,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,WAAW,WAAW,kBACrB,8OAAC;wBAAE,WAAU;kCACV,WAAW,WAAW;;;;;;kCAK3B,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;oCAA2B;oCAAU,WAAW,OAAO,EAAE,UAAU;oCAAE;;;;;;;0CACnF,8OAAC;gCAAI,WAAU;;oCACZ,WAAW,OAAO,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,sBAC5C,8OAAC,iIAAA,CAAA,QAAK;4CAAa,SAAQ;4CAAU,WAAU;sDAC5C;2CADS;;;;;oCAIb,CAAC,WAAW,OAAO,EAAE,UAAU,CAAC,IAAI,mBACnC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAAU;4CACzC,CAAC,WAAW,OAAO,EAAE,UAAU,CAAC,IAAI;4CAAE;;;;;;;;;;;;;;;;;;;kCAOhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCACZ,kBAAkB,WAAW,UAAU;kDACxC,8OAAC;kDAAM,WAAW,UAAU;;;;;;;;;;;;4BAE7B,WAAW,eAAe,kBACzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;;4CAAM,WAAW,eAAe;4CAAC;;;;;;;;;;;;;;;;;;;oBAMvC,WAAW,IAAI,IAAI,WAAW,IAAI,CAAC,MAAM,GAAG,mBAC3C,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBACrC,8OAAC,iIAAA,CAAA,QAAK;oCAAa,SAAQ;oCAAY,WAAU;8CAC9C;mCADS;;;;;4BAIb,WAAW,IAAI,CAAC,MAAM,GAAG,mBACxB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;;oCAAU;oCAC3C,WAAW,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;kCAOnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAK;wCACM,WAAW,SAAS,GAAG,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,WAAW,SAAS,GAAG,kBAAkB;;;;;;;gCAE3F,WAAW,SAAS,kBACnB,8OAAC;;wCAAK;wCACM,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,WAAW,SAAS,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjE;uCAEe", "debugId": null}}, {"offset": {"line": 3930, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/management/ReportTypeManager.tsx"], "sourcesContent": ["/**\r\n * @file ReportTypeManager.tsx\r\n * @description Report type management component following SOLID principles and established patterns\r\n */\r\n\r\nimport React, { useState, useMemo } from 'react';\r\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  Plus,\r\n  Edit,\r\n  Trash2,\r\n  Copy,\r\n  Settings,\r\n  FileText,\r\n  MoreHorizontal,\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { useReportTypes } from '../hooks/useReportTypes';\r\nimport { DataLoader } from '@/components/ui/data-loader';\r\nimport { ErrorDisplay } from '@/components/ui/error-display';\r\nimport { ReportTypeForm } from './ReportTypeForm';\r\nimport { ReportTypeCard } from './ReportTypeCard';\r\nimport type { ReportType } from '../data/types/reporting';\r\n\r\n/**\r\n * Props interface for ReportTypeManager\r\n */\r\ninterface ReportTypeManagerProps {\r\n  className?: string;\r\n  onReportTypeSelect?: (reportType: ReportType) => void;\r\n  allowEdit?: boolean;\r\n  allowDelete?: boolean;\r\n}\r\n\r\n/**\r\n * ReportTypeManager Component\r\n *\r\n * Manages CRUD operations for report types following established patterns.\r\n *\r\n * Responsibilities:\r\n * - Display list of available report types\r\n * - Handle creation, editing, and deletion of report types\r\n * - Follow established CRUD component patterns\r\n * - Maintain consistent styling and behavior\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of managing report types\r\n * - OCP: Open for extension via props configuration\r\n * - DIP: Depends on established hook and component abstractions\r\n */\r\nexport const ReportTypeManager: React.FC<ReportTypeManagerProps> = ({\r\n  className = '',\r\n  onReportTypeSelect,\r\n  allowEdit = true,\r\n  allowDelete = true,\r\n}) => {\r\n  // State management\r\n  const [isFormOpen, setIsFormOpen] = useState(false);\r\n  const [editingReportType, setEditingReportType] = useState<ReportType | null>(\r\n    null\r\n  );\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n\r\n  // Use established hook patterns\r\n  const {\r\n    data: reportTypes,\r\n    isLoading,\r\n    error,\r\n    createReportType,\r\n    updateReportType,\r\n    deleteReportType,\r\n    duplicateReportType,\r\n  } = useReportTypes();\r\n\r\n  // Handle form submission\r\n  const handleFormSubmit = async (formData: {\r\n    name: string;\r\n    category: string;\r\n    dataSource: string;\r\n    widgets: string[];\r\n    isActive: boolean;\r\n    isPublic: boolean;\r\n    description?: string | undefined;\r\n    filters?: string[] | undefined;\r\n    refreshInterval?: number | undefined;\r\n    tags?: string[] | undefined;\r\n  }) => {\r\n    try {\r\n      // Convert form data to ReportType format\r\n      const reportTypeData: Partial<ReportType> = {\r\n        name: formData.name,\r\n        category: formData.category,\r\n        dataSource: formData.dataSource,\r\n        widgets: formData.widgets,\r\n        isActive: formData.isActive,\r\n        isPublic: formData.isPublic,\r\n        ...(formData.description && { description: formData.description }),\r\n        ...(formData.filters && { filters: formData.filters }),\r\n        ...(formData.refreshInterval && {\r\n          refreshInterval: formData.refreshInterval,\r\n        }),\r\n        ...(formData.tags && { tags: formData.tags }),\r\n      };\r\n\r\n      if (editingReportType) {\r\n        await updateReportType.mutateAsync({\r\n          id: editingReportType.id,\r\n          ...reportTypeData,\r\n        });\r\n      } else {\r\n        await createReportType.mutateAsync(reportTypeData);\r\n      }\r\n\r\n      // Reset form state\r\n      setIsFormOpen(false);\r\n      setEditingReportType(null);\r\n    } catch (error) {\r\n      console.error('Failed to save report type:', error);\r\n    }\r\n  };\r\n\r\n  // Handle edit action\r\n  const handleEdit = (reportType: ReportType) => {\r\n    setEditingReportType(reportType);\r\n    setIsFormOpen(true);\r\n  };\r\n\r\n  // Handle delete action\r\n  const handleDelete = async (reportType: ReportType) => {\r\n    if (\r\n      window.confirm(`Are you sure you want to delete \"${reportType.name}\"?`)\r\n    ) {\r\n      try {\r\n        await deleteReportType.mutateAsync(reportType.id);\r\n      } catch (error) {\r\n        console.error('Failed to delete report type:', error);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handle duplicate action\r\n  const handleDuplicate = async (reportType: ReportType) => {\r\n    try {\r\n      await duplicateReportType.mutateAsync(reportType.id);\r\n    } catch (error) {\r\n      console.error('Failed to duplicate report type:', error);\r\n    }\r\n  };\r\n\r\n  // Filter report types based on search\r\n  const filteredReportTypes = useMemo(\r\n    () =>\r\n      reportTypes?.filter(\r\n        reportType =>\r\n          reportType.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n          reportType.description\r\n            ?.toLowerCase()\r\n            .includes(searchTerm.toLowerCase())\r\n      ) || [],\r\n    [reportTypes, searchTerm]\r\n  );\r\n\r\n  // Handle loading state\r\n  if (isLoading) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <FileText className=\"h-5 w-5\" />\r\n            Report Type Manager\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <DataLoader isLoading={true} data={null} error={null}>\r\n            {() => null}\r\n          </DataLoader>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Handle error state\r\n  if (error) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <FileText className=\"h-5 w-5\" />\r\n            Report Type Manager\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <ErrorDisplay error={error} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={cn('space-y-6', className)}>\r\n      {/* Header Section */}\r\n      <Card>\r\n        <CardHeader className=\"pb-3\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <FileText className=\"h-5 w-5\" />\r\n              Report Type Manager\r\n            </CardTitle>\r\n            <div className=\"flex items-center gap-2\">\r\n              <Badge variant=\"secondary\" className=\"text-xs\">\r\n                {filteredReportTypes.length} types\r\n              </Badge>\r\n              <Button\r\n                onClick={() => setIsFormOpen(true)}\r\n                size=\"sm\"\r\n                className=\"flex items-center gap-2\"\r\n              >\r\n                <Plus className=\"h-4 w-4\" />\r\n                New Report Type\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </CardHeader>\r\n\r\n        <CardContent>\r\n          {/* Search and Filters */}\r\n          <div className=\"flex items-center gap-4 mb-6\">\r\n            <div className=\"flex-1\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search report types...\"\r\n                value={searchTerm}\r\n                onChange={e => setSearchTerm(e.target.value)}\r\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n              />\r\n            </div>\r\n            <Button variant=\"outline\" size=\"sm\">\r\n              <Settings className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n\r\n          {/* Report Types Grid */}\r\n          {filteredReportTypes.length === 0 ? (\r\n            <div className=\"text-center py-12 text-gray-500\">\r\n              <FileText className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\r\n              <h3 className=\"text-lg font-medium mb-2\">\r\n                No Report Types Found\r\n              </h3>\r\n              <p className=\"text-sm mb-4\">\r\n                {searchTerm\r\n                  ? 'No report types match your search.'\r\n                  : 'Get started by creating your first report type.'}\r\n              </p>\r\n              <Button onClick={() => setIsFormOpen(true)}>\r\n                <Plus className=\"h-4 w-4 mr-2\" />\r\n                Create Report Type\r\n              </Button>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n              {filteredReportTypes.map(reportType => (\r\n                <ReportTypeCard\r\n                  key={reportType.id}\r\n                  reportType={reportType}\r\n                  {...(onReportTypeSelect && { onSelect: onReportTypeSelect })}\r\n                  {...(allowEdit && { onEdit: handleEdit })}\r\n                  {...(allowDelete && { onDelete: handleDelete })}\r\n                  onDuplicate={handleDuplicate}\r\n                />\r\n              ))}\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Report Type Form Modal/Drawer */}\r\n      {isFormOpen && (\r\n        <ReportTypeForm\r\n          reportType={editingReportType}\r\n          onSubmit={handleFormSubmit}\r\n          onCancel={() => {\r\n            setIsFormOpen(false);\r\n            setEditingReportType(null);\r\n          }}\r\n          isLoading={createReportType.isPending || updateReportType.isPending}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReportTypeManager;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AACA;AACA;AACA;AACA;AAAA;AAAA;AASA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;AA6BO,MAAM,oBAAsD,CAAC,EAClE,YAAY,EAAE,EACd,kBAAkB,EAClB,YAAY,IAAI,EAChB,cAAc,IAAI,EACnB;IACC,mBAAmB;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvD;IAEF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,gCAAgC;IAChC,MAAM,EACJ,MAAM,WAAW,EACjB,SAAS,EACT,KAAK,EACL,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,mBAAmB,EACpB,GAAG,CAAA,GAAA,qKAAA,CAAA,iBAAc,AAAD;IAEjB,yBAAyB;IACzB,MAAM,mBAAmB,OAAO;QAY9B,IAAI;YACF,yCAAyC;YACzC,MAAM,iBAAsC;gBAC1C,MAAM,SAAS,IAAI;gBACnB,UAAU,SAAS,QAAQ;gBAC3B,YAAY,SAAS,UAAU;gBAC/B,SAAS,SAAS,OAAO;gBACzB,UAAU,SAAS,QAAQ;gBAC3B,UAAU,SAAS,QAAQ;gBAC3B,GAAI,SAAS,WAAW,IAAI;oBAAE,aAAa,SAAS,WAAW;gBAAC,CAAC;gBACjE,GAAI,SAAS,OAAO,IAAI;oBAAE,SAAS,SAAS,OAAO;gBAAC,CAAC;gBACrD,GAAI,SAAS,eAAe,IAAI;oBAC9B,iBAAiB,SAAS,eAAe;gBAC3C,CAAC;gBACD,GAAI,SAAS,IAAI,IAAI;oBAAE,MAAM,SAAS,IAAI;gBAAC,CAAC;YAC9C;YAEA,IAAI,mBAAmB;gBACrB,MAAM,iBAAiB,WAAW,CAAC;oBACjC,IAAI,kBAAkB,EAAE;oBACxB,GAAG,cAAc;gBACnB;YACF,OAAO;gBACL,MAAM,iBAAiB,WAAW,CAAC;YACrC;YAEA,mBAAmB;YACnB,cAAc;YACd,qBAAqB;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,qBAAqB;IACrB,MAAM,aAAa,CAAC;QAClB,qBAAqB;QACrB,cAAc;IAChB;IAEA,uBAAuB;IACvB,MAAM,eAAe,OAAO;QAC1B,IACE,OAAO,OAAO,CAAC,CAAC,iCAAiC,EAAE,WAAW,IAAI,CAAC,EAAE,CAAC,GACtE;YACA,IAAI;gBACF,MAAM,iBAAiB,WAAW,CAAC,WAAW,EAAE;YAClD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,oBAAoB,WAAW,CAAC,WAAW,EAAE;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,sCAAsC;IACtC,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAChC,IACE,aAAa,OACX,CAAA,aACE,WAAW,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC7D,WAAW,WAAW,EAClB,cACD,SAAS,WAAW,WAAW,QACjC,EAAE,EACT;QAAC;QAAa;KAAW;IAG3B,uBAAuB;IACvB,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIpC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,mIAAA,CAAA,aAAU;wBAAC,WAAW;wBAAM,MAAM;wBAAM,OAAO;kCAC7C,IAAM;;;;;;;;;;;;;;;;;IAKjB;IAEA,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIpC,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,4IAAA,CAAA,eAAY;wBAAC,OAAO;;;;;;;;;;;;;;;;;IAI7B;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;;gDAClC,oBAAoB,MAAM;gDAAC;;;;;;;sDAE9B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,cAAc;4CAC7B,MAAK;4CACL,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;;;;;;kCAOpC,8OAAC,gIAAA,CAAA,cAAW;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAA,IAAK,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;;;;;;;;;;;kDAGd,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAC7B,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAKvB,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAA2B;;;;;;kDAGzC,8OAAC;wCAAE,WAAU;kDACV,aACG,uCACA;;;;;;kDAEN,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,cAAc;;0DACnC,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;qDAKrC,8OAAC;gCAAI,WAAU;0CACZ,oBAAoB,GAAG,CAAC,CAAA,2BACvB,8OAAC,2KAAA,CAAA,iBAAc;wCAEb,YAAY;wCACX,GAAI,sBAAsB;4CAAE,UAAU;wCAAmB,CAAC;wCAC1D,GAAI,aAAa;4CAAE,QAAQ;wCAAW,CAAC;wCACvC,GAAI,eAAe;4CAAE,UAAU;wCAAa,CAAC;wCAC9C,aAAa;uCALR,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;YAc7B,4BACC,8OAAC,2KAAA,CAAA,iBAAc;gBACb,YAAY;gBACZ,UAAU;gBACV,UAAU;oBACR,cAAc;oBACd,qBAAqB;gBACvB;gBACA,WAAW,iBAAiB,SAAS,IAAI,iBAAiB,SAAS;;;;;;;;;;;;AAK7E;uCAEe", "debugId": null}}]}