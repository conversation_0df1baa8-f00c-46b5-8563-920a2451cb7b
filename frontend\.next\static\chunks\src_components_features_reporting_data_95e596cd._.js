(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/features/reporting/data/services/ReportGenerationService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// frontend/src/components/features/reporting/data/services/ReportGenerationService.ts
/**
 * Report Generation Service
 *
 * Follows SOLID Principles:
 * - SRP: Single responsibility for coordinating report generation
 * - OCP: Open for extension with new report types
 * - LSP: Implements consistent interface for all report types
 * - ISP: Focused interface for report generation
 * - DIP: Depends on abstractions (data services, export services)
 */ // Import types from the hook file where they are defined
__turbopack_context__.s({
    "ReportGenerationService": (()=>ReportGenerationService),
    "createReportGenerationService": (()=>createReportGenerationService),
    "reportGenerationService": (()=>reportGenerationService)
});
class ReportGenerationService {
    apiClient;
    constructor(apiClient){
        this.apiClient = apiClient;
    }
    /**
   * Generate aggregate analytics report for entity type
   *
   * @param config - Aggregate report configuration
   * @returns Promise<ReportGenerationResult>
   */ async generateAggregateReport(config) {
        try {
            const response = await this.apiClient.request({
                url: `/api/reporting/reports/aggregate/${config.entityType}`,
                method: 'POST',
                data: {
                    filters: config.filters,
                    template: config.template || 'default',
                    format: 'json',
                    options: config.options || {}
                }
            });
            // Normalize response structure - handle nested aggregate data
            const responseData = response.data.data || response.data;
            const aggregateData = responseData?.aggregate || responseData;
            return {
                data: aggregateData,
                metadata: response.data.metadata || responseData?.metadata || {
                    id: `aggregate_${config.entityType}_${Date.now()}`,
                    type: 'aggregate',
                    entityType: config.entityType,
                    format: config.format || 'json',
                    template: config.template || 'default',
                    generatedAt: new Date().toISOString(),
                    generatedBy: 'system',
                    filters: config.filters,
                    options: config.options
                }
            };
        } catch (error) {
            console.error('Failed to generate aggregate report:', error);
            throw new Error(`Failed to generate ${config.entityType} aggregate report`);
        }
    }
    /**
   * Generate individual entity report
   *
   * @param config - Individual report configuration
   * @returns Promise<ReportGenerationResult>
   */ async generateIndividualReport(config) {
        try {
            const response = await this.apiClient.request({
                url: `/api/reporting/reports/individual/${config.entityType}/${config.entityId}`,
                method: 'POST',
                data: {
                    template: config.template || 'default',
                    format: 'json',
                    options: config.options || {}
                }
            });
            return {
                data: response.data.data || response.data,
                metadata: response.data.metadata || {
                    id: `individual_${config.entityType}_${config.entityId}_${Date.now()}`,
                    type: 'individual',
                    entityType: config.entityType,
                    entityId: config.entityId,
                    format: config.format || 'json',
                    template: config.template || 'default',
                    generatedAt: new Date().toISOString(),
                    generatedBy: 'system',
                    options: config.options
                }
            };
        } catch (error) {
            console.error('Failed to generate individual report:', error);
            throw new Error(`Failed to generate ${config.entityType} individual report`);
        }
    }
    /**
   * Generate comprehensive cross-entity report
   *
   * @param config - Comprehensive report configuration
   * @returns Promise<ReportGenerationResult>
   */ async generateComprehensiveReport(config) {
        try {
            const response = await this.apiClient.request({
                url: '/api/reporting/reports/generate',
                method: 'POST',
                data: {
                    entityTypes: config.entityTypes,
                    filters: config.filters,
                    template: config.template || 'comprehensive',
                    format: 'json',
                    options: config.options || {}
                }
            });
            return {
                data: response.data.data || response.data,
                metadata: response.data.metadata || {
                    id: `comprehensive_${Date.now()}`,
                    type: 'comprehensive',
                    entityTypes: config.entityTypes,
                    format: config.format || 'json',
                    template: config.template || 'comprehensive',
                    generatedAt: new Date().toISOString(),
                    generatedBy: 'system',
                    filters: config.filters,
                    options: config.options
                }
            };
        } catch (error) {
            console.error('Failed to generate comprehensive report:', error);
            throw new Error('Failed to generate comprehensive report');
        }
    }
}
/**
 * Default API client implementation
 * Note: This will be injected from the hook that has access to secureRequest
 */ class DefaultApiClient {
    async request(config) {
        throw new Error('API client not initialized. Use createReportGenerationService with proper API client.');
    }
}
const createReportGenerationService = (apiClient)=>{
    return new ReportGenerationService(apiClient);
};
const reportGenerationService = new ReportGenerationService(new DefaultApiClient());
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/data/transformers/reportingTransformers.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// PHASE 1 ENHANCEMENT: Import new entity analytics types
__turbopack_context__.s({
    "transformCrossEntityAnalytics": (()=>transformCrossEntityAnalytics),
    "transformDelegationAnalytics": (()=>transformDelegationAnalytics),
    "transformEmployeeAnalytics": (()=>transformEmployeeAnalytics),
    "transformLocationMetrics": (()=>transformLocationMetrics),
    "transformServiceCostSummary": (()=>transformServiceCostSummary),
    "transformServiceHistory": (()=>transformServiceHistory),
    "transformTaskAnalytics": (()=>transformTaskAnalytics),
    "transformTaskAnalyticsData": (()=>transformTaskAnalyticsData),
    "transformTaskMetrics": (()=>transformTaskMetrics),
    "transformTrendData": (()=>transformTrendData),
    "transformVehicleAnalytics": (()=>transformVehicleAnalytics)
});
/**
 * Helper function to get color for delegation status.
 * FIXED: Use correct Prisma enum values instead of incorrect UPPERCASE versions.
 * @param status - Delegation status
 * @returns Color string for the status
 */ const getStatusColor = (status)=>{
    const colorMap = {
        Planned: '#3b82f6',
        Confirmed: '#10b981',
        In_Progress: '#f59e0b',
        Completed: '#22c55e',
        Cancelled: '#ef4444',
        No_details: '#6b7280'
    };
    return colorMap[status] || '#6b7280';
};
/**
 * PHASE 1 ENHANCEMENT: Helper function to get color for task status
 * @param status - Task status
 * @returns Color string for the status
 */ const getTaskStatusColor = (status)=>{
    const colorMap = {
        Pending: '#6b7280',
        Assigned: '#3b82f6',
        In_Progress: '#f59e0b',
        Completed: '#22c55e',
        Cancelled: '#ef4444'
    };
    return colorMap[status] || '#6b7280';
};
/**
 * PHASE 1 ENHANCEMENT: Helper function to get color for task priority
 * @param priority - Task priority
 * @returns Color string for the priority
 */ const getTaskPriorityColor = (priority)=>{
    const colorMap = {
        Low: '#10b981',
        Medium: '#f59e0b',
        High: '#ef4444'
    };
    return colorMap[priority] || '#6b7280';
};
const transformDelegationAnalytics = (rawData)=>{
    const result = {
        totalCount: rawData.totalCount || 0,
        statusDistribution: rawData.statusDistribution?.map((item)=>({
                status: item.status,
                count: item.count || 0,
                percentage: item.percentage || 0,
                color: getStatusColor(item.status)
            })) || [],
        trendData: rawData.trendData?.map((item)=>({
                date: item.date,
                created: item.created || 0,
                completed: item.completed || 0,
                inProgress: item.inProgress || 0
            })) || [],
        locationMetrics: rawData.locationMetrics || [],
        summary: {
            totalDelegations: rawData.summary?.totalDelegations || 0,
            activeDelegations: rawData.summary?.activeDelegations || 0,
            completedDelegations: rawData.summary?.completedDelegations || 0,
            totalDelegates: rawData.summary?.totalDelegates || 0,
            averageDuration: rawData.summary?.averageDuration || 0,
            completionRate: rawData.summary?.completionRate || 0
        },
        // FIXED: Initialize delegations array
        delegations: []
    };
    // ENHANCED: Optional service history data transformation
    if (rawData.serviceHistory) {
        result.serviceHistory = transformServiceHistory(rawData.serviceHistory);
    }
    if (rawData.serviceCosts && typeof rawData.serviceCosts === 'object' && !Array.isArray(rawData.serviceCosts)) {
        result.serviceCosts = transformServiceCostSummary(rawData.serviceCosts);
    }
    if (rawData.taskData && typeof rawData.taskData === 'object' && !Array.isArray(rawData.taskData)) {
        result.taskData = transformTaskAnalyticsData(rawData.taskData);
    }
    // FIXED: Add real delegations data from API response
    result.delegations = rawData.delegations?.map((item)=>({
            id: (item.id || 0).toString(),
            delegationId: item.delegationId || item.id?.toString() || '',
            customerName: item.customerName || item.customer?.name || 'Unknown Customer',
            vehicleModel: item.vehicleModel || item.vehicle?.model || 'Unknown Vehicle',
            licensePlate: item.licensePlate || item.vehicle?.licensePlate || 'Unknown',
            status: item.status,
            assignedEmployee: item.driverEmployee?.name || item.staffEmployee?.name || 'Unassigned',
            location: item.location || '',
            createdAt: item.createdAt || '',
            completedAt: item.completedAt
        })) || [];
    return result;
};
const transformTaskMetrics = (rawData)=>{
    return {
        totalTasks: rawData.totalTasks || 0,
        completedTasks: rawData.completedTasks || 0,
        pendingTasks: rawData.pendingTasks || 0,
        inProgressTasks: rawData.inProgressTasks || 0,
        overdueTasks: rawData.overdueTasks || 0,
        averageCompletionTime: rawData.averageCompletionTime || 0,
        tasksByPriority: rawData.tasksByPriority?.map((item)=>({
                priority: item.priority,
                count: item.count || 0
            })) || [],
        tasksByStatus: rawData.tasksByStatus?.map((item)=>({
                status: item.status,
                count: item.count || 0
            })) || []
    };
};
const transformTrendData = (rawData)=>{
    return rawData?.map((item)=>({
            date: item.date,
            created: item.created || 0,
            completed: item.completed || 0,
            inProgress: item.inProgress || 0
        })) || [];
};
const transformLocationMetrics = (rawData)=>{
    return rawData?.map((item)=>({
            location: item.location || '',
            delegationCount: item.delegationCount || item.delegationsCount || item.count || 0,
            averageDuration: item.averageDuration || 0,
            completionRate: item.completionRate || 0
        })) || [];
};
const transformServiceHistory = (rawData)=>{
    return {
        id: rawData.id || '',
        vehicleId: rawData.vehicleId || 0,
        vehicleName: rawData.vehicleName || '',
        serviceType: rawData.serviceType,
        status: rawData.status,
        scheduledDate: rawData.scheduledDate || '',
        completedDate: rawData.completedDate,
        cost: rawData.cost || 0,
        description: rawData.description || '',
        relatedDelegationId: rawData.relatedDelegationId,
        relatedTaskId: rawData.relatedTaskId
    };
};
const transformServiceCostSummary = (rawData)=>{
    return {
        totalCost: rawData.totalCost || 0,
        averageCostPerService: rawData.averageCostPerService || 0,
        costByType: rawData.costByType?.map((item)=>({
                type: item.type,
                cost: item.cost || 0,
                count: item.count || 0
            })) || [],
        monthlyTrend: rawData.monthlyTrend?.map((item)=>({
                month: item.month || '',
                cost: item.cost || 0
            })) || []
    };
};
const transformTaskAnalyticsData = (rawData)=>{
    return {
        totalTasks: rawData.totalTasks || 0,
        completedTasks: rawData.completedTasks || 0,
        pendingTasks: rawData.pendingTasks || 0,
        overdueTasks: rawData.overdueTasks || 0,
        averageCompletionTime: rawData.averageCompletionTime || 0,
        tasksByPriority: rawData.tasksByPriority?.map((item)=>({
                priority: item.priority,
                count: item.count || 0
            })) || []
    };
};
const transformTaskAnalytics = (rawData)=>{
    const totalCount = rawData.totalCount || 0;
    return {
        totalCount,
        statusDistribution: rawData.statusDistribution?.map((item)=>({
                status: item.status,
                count: item.count || 0,
                percentage: totalCount > 0 ? (item.count || 0) / totalCount * 100 : 0,
                color: getTaskStatusColor(item.status)
            })) || [],
        priorityDistribution: rawData.priorityDistribution?.map((item)=>({
                priority: item.priority,
                count: item.count || 0,
                percentage: totalCount > 0 ? (item.count || 0) / totalCount * 100 : 0,
                color: getTaskPriorityColor(item.priority)
            })) || [],
        completionRate: rawData.completionRate || 0,
        overdueCount: rawData.overdueCount || 0,
        averageCompletionTime: rawData.averageCompletionTime || 0,
        assignmentMetrics: rawData.assignmentMetrics?.map((item)=>({
                employeeId: item.employeeId || 0,
                employeeName: item.employeeName || '',
                assignedTasks: item.assignedTasks || 0,
                completedTasks: item.completedTasks || 0,
                completionRate: item.completionRate || 0,
                averageCompletionTime: item.averageCompletionTime || 0
            })) || [],
        trendData: rawData.trendData?.map((item)=>({
                date: item.date || '',
                created: item.created || 0,
                completed: item.completed || 0,
                inProgress: item.inProgress || 0,
                overdue: item.overdue || 0
            })) || []
    };
};
const transformVehicleAnalytics = (rawData)=>{
    return {
        totalCount: rawData.totalCount || 0,
        serviceHistory: rawData.serviceHistory?.map((item)=>transformServiceHistory(item)) || [],
        costAnalysis: rawData.costAnalysis ? transformServiceCostSummary(rawData.costAnalysis) : {
            totalCost: 0,
            averageCostPerService: 0,
            costByType: [],
            monthlyTrend: []
        },
        utilizationMetrics: rawData.utilizationMetrics?.map((item)=>({
                vehicleId: item.vehicleId || 0,
                vehicleName: item.vehicleName || '',
                utilizationRate: item.utilizationRate || 0,
                totalDelegations: item.totalDelegations || 0,
                activeDelegations: item.activeDelegations || 0,
                maintenanceHours: item.maintenanceHours || 0
            })) || [],
        maintenanceSchedule: rawData.maintenanceSchedule?.map((item)=>({
                vehicleId: item.vehicleId || 0,
                vehicleName: item.vehicleName || '',
                nextMaintenanceDate: item.nextMaintenanceDate || '',
                maintenanceType: item.maintenanceType,
                priority: item.priority || 'Medium',
                estimatedCost: item.estimatedCost || 0
            })) || [],
        performanceMetrics: rawData.performanceMetrics?.map((item)=>({
                vehicleId: item.vehicleId || 0,
                vehicleName: item.vehicleName || '',
                fuelEfficiency: item.fuelEfficiency || 0,
                maintenanceCost: item.maintenanceCost || 0,
                downtime: item.downtime || 0,
                reliabilityScore: item.reliabilityScore || 0
            })) || []
    };
};
const transformEmployeeAnalytics = (rawData)=>{
    return {
        totalCount: rawData.totalCount || 0,
        performanceMetrics: rawData.performanceMetrics?.map((item)=>({
                employeeId: item.employeeId || 0,
                employeeName: item.employeeName || '',
                completedDelegations: item.completedDelegations || 0,
                completedTasks: item.completedTasks || 0,
                averageRating: item.averageRating || 0,
                onTimePerformance: item.onTimePerformance || 0,
                workloadScore: item.workloadScore || 0
            })) || [],
        delegationHistory: rawData.delegationHistory?.map((item)=>({
                employeeId: item.employeeId || 0,
                employeeName: item.employeeName || '',
                totalDelegations: item.totalDelegations || 0,
                completedDelegations: item.completedDelegations || 0,
                averageDuration: item.averageDuration || 0,
                successRate: item.successRate || 0
            })) || [],
        taskAssignments: rawData.taskAssignments?.map((item)=>({
                employeeId: item.employeeId || 0,
                employeeName: item.employeeName || '',
                assignedTasks: item.assignedTasks || 0,
                completedTasks: item.completedTasks || 0,
                pendingTasks: item.pendingTasks || 0,
                overdueTasksCount: item.overdueTasksCount || 0
            })) || [],
        availabilityMetrics: rawData.availabilityMetrics?.map((item)=>({
                employeeId: item.employeeId || 0,
                employeeName: item.employeeName || '',
                availableHours: item.availableHours || 0,
                scheduledHours: item.scheduledHours || 0,
                utilizationRate: item.utilizationRate || 0,
                overtimeHours: item.overtimeHours || 0
            })) || [],
        workloadDistribution: rawData.workloadDistribution?.map((item)=>({
                employeeId: item.employeeId || 0,
                employeeName: item.employeeName || '',
                currentWorkload: item.currentWorkload || 0,
                capacity: item.capacity || 0,
                workloadPercentage: item.workloadPercentage || 0,
                status: item.status || 'Optimal'
            })) || []
    };
};
const transformCrossEntityAnalytics = (rawData)=>{
    const result = {
        correlations: {
            employeeVehicle: rawData.correlations?.employeeVehicle || [],
            taskDelegation: rawData.correlations?.taskDelegation || [],
            performanceWorkload: rawData.correlations?.performanceWorkload || [],
            overall: rawData.correlations?.overall || []
        },
        metrics: {
            employeeVehicle: rawData.metrics?.employeeVehicle || 0,
            taskDelegation: rawData.metrics?.taskDelegation || 0,
            performanceWorkload: rawData.metrics?.performanceWorkload || 0,
            overallEfficiency: rawData.metrics?.overallEfficiency || 0
        }
    };
    // Add optional properties only if they exist
    if (rawData.network) {
        result.network = {
            nodes: rawData.network.nodes || [],
            edges: rawData.network.edges || []
        };
    }
    if (rawData.insights) {
        result.insights = rawData.insights || [];
    }
    return result;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/data/services/ReportingDataService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// frontend/src/components/features/reporting/data/services/ReportingDataService.ts
__turbopack_context__.s({
    "ReportingDataService": (()=>ReportingDataService),
    "reportingDataService": (()=>reportingDataService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$transformers$2f$reportingTransformers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/transformers/reportingTransformers.ts [app-client] (ecmascript)");
;
;
class ReportingDataService {
    baseUrl;
    constructor(baseUrl = '/api/reporting'){
        this.baseUrl = baseUrl;
    }
    /**
   * PHASE 1 ENHANCEMENT: Get cross-entity analytics data
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to cross-entity analytics data
   */ async getCrossEntityAnalytics(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const queryParams = new URLSearchParams(queryParamsString);
            // Add cross-entity parameters
            if (filters.includeCrossEntityCorrelations) {
                queryParams.append('includeCrossEntityCorrelations', 'true');
            }
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/cross-entity/analytics?${queryParams.toString()}`);
            return apiResponse.data || apiResponse;
        } catch (error) {
            console.error('Error fetching cross-entity analytics:', error);
            throw new Error(`Failed to load cross-entity analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Fetches delegation analytics data based on provided filters
   * ENHANCED: Now supports service history and task data integration
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to delegation analytics data
   */ async getDelegationAnalytics(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const queryParams = new URLSearchParams(queryParamsString);
            // ENHANCED: Add service history and task data parameters
            if (filters.includeServiceHistory) {
                queryParams.append('includeServiceHistory', 'true');
            }
            if (filters.includeTaskData) {
                queryParams.append('includeTaskData', 'true');
            }
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/delegations/analytics?${queryParams.toString()}`);
            // Backend returns { status: 'success', data: {...}, timestamp: '...' }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$transformers$2f$reportingTransformers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["transformDelegationAnalytics"])(apiResponse.data || apiResponse);
        } catch (error) {
            console.error('Error fetching delegation analytics:', error);
            throw new Error(`Failed to load delegation analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Fetches paginated delegations data based on provided filters
   * @param filters - Reporting filters to apply
   * @param pagination - Pagination parameters
   * @returns Promise resolving to paginated delegations response
   */ async getDelegations(filters, pagination) {
        try {
            const queryParams = this.buildQueryParams(filters);
            const paginationParams = new URLSearchParams(queryParams);
            // Add pagination parameters
            paginationParams.append('page', pagination.page.toString());
            paginationParams.append('pageSize', pagination.pageSize.toString());
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/delegations?${paginationParams.toString()}`);
            // Backend returns { status: 'success', data: {...}, timestamp: '...' }
            return apiResponse.data || apiResponse;
        } catch (error) {
            console.error('Error fetching delegations:', error);
            throw new Error(`Failed to load delegations: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * PHASE 1 ENHANCEMENT: Get employee analytics data
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to employee analytics data
   */ async getEmployeeAnalytics(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/employee/analytics?${queryParamsString}`);
            return apiResponse.data || apiResponse;
        } catch (error) {
            console.error('Error fetching employee analytics:', error);
            throw new Error(`Failed to load employee analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Fetches location metrics based on provided filters
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to location metrics array
   */ async getLocationMetrics(filters) {
        try {
            const queryParams = this.buildQueryParams(filters);
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/locations/metrics?${queryParams}`);
            // Backend returns { status: 'success', data: [...], timestamp: '...' }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$transformers$2f$reportingTransformers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["transformLocationMetrics"])(apiResponse.data || apiResponse);
        } catch (error) {
            console.error('Error fetching location metrics:', error);
            throw new Error(`Failed to load location metrics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    // Private helper methods for data transformation and utility functions
    /**
   * ENHANCED: Fetches service cost summary
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to service cost summary
   */ async getServiceCostSummary(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const rawData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/services/costs?${queryParamsString}`);
            return rawData.data || {
                averageCostPerService: 0,
                costByType: [],
                monthlyTrend: [],
                totalCost: 0
            };
        } catch (error) {
            console.error('Error fetching service costs:', error);
            throw new Error(`Failed to load service costs: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * ENHANCED: Fetches service history data for vehicles
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to service history data
   */ async getServiceHistory(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const rawData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/services/history?${queryParamsString}`);
            // Transform service history data if needed
            return rawData.data || [];
        } catch (error) {
            console.error('Error fetching service history:', error);
            throw new Error(`Failed to load service history: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * PHASE 1 ENHANCEMENT: Get task analytics data
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to task analytics data
   */ async getTaskAnalytics(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const queryParams = new URLSearchParams(queryParamsString);
            // Add task-specific parameters
            if (filters.taskStatus && filters.taskStatus.length > 0) {
                queryParams.append('taskStatus', filters.taskStatus.join(','));
            }
            if (filters.taskPriority && filters.taskPriority.length > 0) {
                queryParams.append('taskPriority', filters.taskPriority.join(','));
            }
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/tasks/analytics?${queryParams.toString()}`);
            return apiResponse.data || apiResponse;
        } catch (error) {
            console.error('Error fetching task analytics:', error);
            throw new Error(`Failed to load task analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Fetches task metrics for specific delegations or all tasks
   * @param delegationIds - Optional array of delegation IDs to filter by
   * @returns Promise resolving to task metrics data
   */ async getTaskMetrics(delegationIds) {
        try {
            const queryParams = delegationIds?.length ? `delegationIds=${delegationIds.join(',')}` : '';
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/tasks/metrics?${queryParams}`);
            // Backend returns { status: 'success', data: {...}, timestamp: '...' }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$transformers$2f$reportingTransformers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["transformTaskMetrics"])(apiResponse.data || apiResponse);
        } catch (error) {
            console.error('Error fetching task metrics:', error);
            throw new Error(`Failed to load task metrics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Fetches trend data based on provided filters
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to trend data array
   */ async getTrendData(filters) {
        try {
            const queryParams = this.buildQueryParams(filters);
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/trends?${queryParams}`);
            // Backend returns { status: 'success', data: [...], timestamp: '...' }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$transformers$2f$reportingTransformers$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["transformTrendData"])(apiResponse.data || apiResponse);
        } catch (error) {
            console.error('Error fetching trend data:', error);
            throw new Error(`Failed to load trend data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * PHASE 1 ENHANCEMENT: Get vehicle analytics data
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to vehicle analytics data
   */ async getVehicleAnalytics(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const queryParams = new URLSearchParams(queryParamsString);
            // Add vehicle-specific parameters
            if (filters.vehicles && filters.vehicles.length > 0) {
                queryParams.append('vehicles', filters.vehicles.join(','));
            }
            if (filters.serviceTypes && filters.serviceTypes.length > 0) {
                queryParams.append('serviceTypes', filters.serviceTypes.join(','));
            }
            if (filters.serviceStatus && filters.serviceStatus.length > 0) {
                queryParams.append('serviceStatus', filters.serviceStatus.join(','));
            }
            const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
            return result.data || result;
        } catch (error) {
            console.error('Error fetching vehicle analytics:', error);
            throw error;
        }
    }
    /**
   * PHASE 1 ENHANCEMENT: Helper method for appending array parameters (DRY principle)
   * @param params - URLSearchParams object
   * @param key - Parameter key
   * @param values - Array of values to append
   */ appendArrayParams(params, key, values) {
        if (values && values.length > 0) {
            params.append(key, values.join(','));
        }
    }
    /**
   * PHASE 1 ENHANCEMENT: Builds query parameters string from filters object
   * Following DRY principle: Reusable query building logic
   * @param filters - Reporting filters
   * @returns URL-encoded query parameters string
   */ buildQueryParams(filters) {
        const params = new URLSearchParams();
        // Date range - use the format expected by backend validation
        // Defensive programming: Ensure dates are Date objects
        try {
            const fromDate = filters.dateRange.from instanceof Date ? filters.dateRange.from : new Date(filters.dateRange.from);
            const toDate = filters.dateRange.to instanceof Date ? filters.dateRange.to : new Date(filters.dateRange.to);
            // Validate that dates are valid
            if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
                throw new TypeError('Invalid date range provided');
            }
            params.append('dateRange.from', fromDate.toISOString().split('T')[0] || fromDate.toISOString());
            params.append('dateRange.to', toDate.toISOString().split('T')[0] || toDate.toISOString());
        } catch (error) {
            console.error('Error processing date range:', error);
            // Fallback to default date range (last 30 days)
            const defaultToDate = new Date();
            const defaultFromDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            params.append('dateRange.from', defaultFromDate.toISOString().split('T')[0] || defaultFromDate.toISOString());
            params.append('dateRange.to', defaultToDate.toISOString().split('T')[0] || defaultToDate.toISOString());
        }
        // Common array handling using helper method (DRY principle)
        this.appendArrayParams(params, 'status', filters.status);
        this.appendArrayParams(params, 'locations', filters.locations);
        this.appendArrayParams(params, 'employees', filters.employees);
        this.appendArrayParams(params, 'vehicles', filters.vehicles);
        // PHASE 1: Additional entity parameters
        if (filters.taskStatus) {
            this.appendArrayParams(params, 'taskStatus', filters.taskStatus);
        }
        if (filters.taskPriority) {
            this.appendArrayParams(params, 'taskPriority', filters.taskPriority);
        }
        if (filters.serviceTypes) {
            this.appendArrayParams(params, 'serviceTypes', filters.serviceTypes);
        }
        if (filters.serviceStatus) {
            this.appendArrayParams(params, 'serviceStatus', filters.serviceStatus);
        }
        // Cost range handling
        if (filters.costRange) {
            params.append('minCost', filters.costRange.min.toString());
            params.append('maxCost', filters.costRange.max.toString());
        }
        return params.toString();
    }
}
const reportingDataService = new ReportingDataService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/data/hooks/useReportingData.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file Reporting Data Hooks
 * @description Contains custom hooks for fetching and managing reporting data.
 */ __turbopack_context__.s({
    "useDelegationAnalytics": (()=>useDelegationAnalytics),
    "useLocationMetrics": (()=>useLocationMetrics),
    "useTaskMetrics": (()=>useTaskMetrics),
    "useTrendData": (()=>useTrendData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/api/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/services/ReportingDataService.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
;
;
const useDelegationAnalytics = (filters, options = {})=>{
    _s();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'delegationAnalytics',
        filters
    ], {
        "useDelegationAnalytics.useApiQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getDelegationAnalytics(filters)
    }["useDelegationAnalytics.useApiQuery"], options);
};
_s(useDelegationAnalytics, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useTaskMetrics = (delegationIds, options = {} // Replace 'any' with TaskMetrics type
)=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'taskMetrics',
        delegationIds
    ], {
        "useTaskMetrics.useApiQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getTaskMetrics(delegationIds)
    }["useTaskMetrics.useApiQuery"], {
        enabled: !!delegationIds && delegationIds.length > 0,
        ...options
    });
};
_s1(useTaskMetrics, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useTrendData = (filters, options = {})=>{
    _s2();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'trendData',
        filters
    ], {
        "useTrendData.useApiQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getTrendData(filters)
    }["useTrendData.useApiQuery"], options);
};
_s2(useTrendData, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
const useLocationMetrics = (filters, options = {})=>{
    _s3();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'locationMetrics',
        filters
    ], {
        "useLocationMetrics.useApiQuery": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getLocationMetrics(filters)
    }["useLocationMetrics.useApiQuery"], options);
};
_s3(useLocationMetrics, "tmWOYF8R5KQGIRjpc8FdyrcAtDE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/data/hooks/useDelegations.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * @file useDelegations Hook
 * @description A hook for fetching paginated delegation data.
 */ __turbopack_context__.s({
    "useDelegations": (()=>useDelegations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/hooks/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/services/ReportingDataService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
;
const useDelegations = (filters, pagination, options)=>{
    _s();
    // FIXED: Use analytics endpoint which includes delegations data
    const analyticsQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'delegations',
        filters,
        pagination
    ], {
        "useDelegations.useApiQuery[analyticsQuery]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getDelegationAnalytics(filters)
    }["useDelegations.useApiQuery[analyticsQuery]"], {
        placeholderData: {
            "useDelegations.useApiQuery[analyticsQuery]": (prev)=>prev
        }["useDelegations.useApiQuery[analyticsQuery]"],
        showErrorToast: true,
        ...options
    });
    // Transform analytics response to match expected PaginatedDelegationsResponse format
    const transformedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useDelegations.useMemo[transformedData]": ()=>{
            if (!analyticsQuery.data?.delegations) {
                return undefined;
            }
            // Type assertion since we know the backend returns AnalyticsDelegation structure
            const allDelegations = analyticsQuery.data.delegations;
            const startIndex = (pagination.page - 1) * pagination.pageSize;
            const endIndex = startIndex + pagination.pageSize;
            const paginatedDelegations = allDelegations.slice(startIndex, endIndex);
            return {
                data: paginatedDelegations.map({
                    "useDelegations.useMemo[transformedData]": (delegation)=>({
                            id: delegation.id,
                            delegationId: delegation.id.toString(),
                            customerName: delegation.title,
                            vehicleModel: 'N/A',
                            licensePlate: 'N/A',
                            status: delegation.status,
                            assignedEmployee: delegation.assignedTo,
                            location: delegation.location,
                            createdAt: delegation.createdAt,
                            completedAt: delegation.completedAt || null
                        })
                }["useDelegations.useMemo[transformedData]"]),
                meta: {
                    total: allDelegations.length,
                    page: pagination.page,
                    pageSize: pagination.pageSize,
                    totalPages: Math.ceil(allDelegations.length / pagination.pageSize)
                }
            };
        }
    }["useDelegations.useMemo[transformedData]"], [
        analyticsQuery.data?.delegations,
        pagination.page,
        pagination.pageSize
    ]);
    return {
        ...analyticsQuery,
        data: transformedData
    };
};
_s(useDelegations, "wmfaLv6Djyglh0LO4AjoMatenVw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useApiQuery"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// frontend/src/components/features/reporting/data/hooks/useReportingQueries.ts
__turbopack_context__.s({
    "reportingQueryKeys": (()=>reportingQueryKeys),
    "useCrossEntityAnalytics": (()=>useCrossEntityAnalytics),
    "useDelegationAnalytics": (()=>useDelegationAnalytics),
    "useEmployeeAnalytics": (()=>useEmployeeAnalytics),
    "useLocationMetrics": (()=>useLocationMetrics),
    "usePrefetchReportingData": (()=>usePrefetchReportingData),
    "useReportingData": (()=>useReportingData),
    "useServiceCostSummary": (()=>useServiceCostSummary),
    "useServiceHistory": (()=>useServiceHistory),
    "useTaskAnalytics": (()=>useTaskAnalytics),
    "useTaskMetrics": (()=>useTaskMetrics),
    "useTrendData": (()=>useTrendData),
    "useVehicleAnalytics": (()=>useVehicleAnalytics)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQueries.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/services/ReportingDataService.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature(), _s5 = __turbopack_context__.k.signature(), _s6 = __turbopack_context__.k.signature(), _s7 = __turbopack_context__.k.signature(), _s8 = __turbopack_context__.k.signature(), _s9 = __turbopack_context__.k.signature(), _s10 = __turbopack_context__.k.signature(), _s11 = __turbopack_context__.k.signature();
;
;
;
const reportingQueryKeys = {
    all: [
        'reporting'
    ],
    analytics: ()=>[
            ...reportingQueryKeys.all,
            'analytics'
        ],
    delegationAnalytics: (filters)=>[
            ...reportingQueryKeys.analytics(),
            'delegations',
            filters
        ],
    taskMetrics: (delegationIds)=>[
            ...reportingQueryKeys.all,
            'tasks',
            'metrics',
            delegationIds
        ],
    trends: (filters)=>[
            ...reportingQueryKeys.all,
            'trends',
            filters
        ],
    locationMetrics: (filters)=>[
            ...reportingQueryKeys.all,
            'locations',
            'metrics',
            filters
        ],
    // ENHANCED: Service history query keys
    serviceHistory: (filters)=>[
            ...reportingQueryKeys.all,
            'services',
            'history',
            filters
        ],
    serviceCosts: (filters)=>[
            ...reportingQueryKeys.all,
            'services',
            'costs',
            filters
        ],
    // PHASE 1: New entity analytics query keys
    taskAnalytics: (filters)=>[
            ...reportingQueryKeys.analytics(),
            'tasks',
            filters
        ],
    vehicleAnalytics: (filters)=>[
            ...reportingQueryKeys.analytics(),
            'vehicles',
            filters
        ],
    employeeAnalytics: (filters)=>[
            ...reportingQueryKeys.analytics(),
            'employees',
            filters
        ],
    crossEntityAnalytics: (filters)=>[
            ...reportingQueryKeys.analytics(),
            'cross-entity',
            filters
        ]
};
const useDelegationAnalytics = (filters, options)=>{
    _s();
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useDelegationAnalytics.useMemo[queryKey]": ()=>reportingQueryKeys.delegationAnalytics(filters)
    }["useDelegationAnalytics.useMemo[queryKey]"], [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useDelegationAnalytics.useCallback[queryFn]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getDelegationAnalytics(filters)
    }["useDelegationAnalytics.useCallback[queryFn]"], [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 3,
        retryDelay: {
            "useDelegationAnalytics.useQuery": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
        }["useDelegationAnalytics.useQuery"],
        refetchOnWindowFocus: false,
        refetchOnMount: true,
        ...options
    });
};
_s(useDelegationAnalytics, "sW6nyFUe1xv1wFg9wQphR1hv8KQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useTaskMetrics = (delegationIds, options)=>{
    _s1();
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useTaskMetrics.useMemo[queryKey]": ()=>reportingQueryKeys.taskMetrics(delegationIds)
    }["useTaskMetrics.useMemo[queryKey]"], [
        delegationIds
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTaskMetrics.useCallback[queryFn]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getTaskMetrics(delegationIds)
    }["useTaskMetrics.useCallback[queryFn]"], [
        delegationIds
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 2 * 60 * 1000,
        gcTime: 5 * 60 * 1000,
        retry: 3,
        retryDelay: {
            "useTaskMetrics.useQuery": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
        }["useTaskMetrics.useQuery"],
        refetchOnWindowFocus: false,
        ...options
    });
};
_s1(useTaskMetrics, "sW6nyFUe1xv1wFg9wQphR1hv8KQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useTrendData = (filters, options)=>{
    _s2();
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useTrendData.useMemo[queryKey]": ()=>reportingQueryKeys.trends(filters)
    }["useTrendData.useMemo[queryKey]"], [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTrendData.useCallback[queryFn]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getTrendData(filters)
    }["useTrendData.useCallback[queryFn]"], [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 3,
        retryDelay: {
            "useTrendData.useQuery": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
        }["useTrendData.useQuery"],
        refetchOnWindowFocus: false,
        ...options
    });
};
_s2(useTrendData, "sW6nyFUe1xv1wFg9wQphR1hv8KQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useLocationMetrics = (filters, options)=>{
    _s3();
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useLocationMetrics.useMemo[queryKey]": ()=>reportingQueryKeys.locationMetrics(filters)
    }["useLocationMetrics.useMemo[queryKey]"], [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLocationMetrics.useCallback[queryFn]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getLocationMetrics(filters)
    }["useLocationMetrics.useCallback[queryFn]"], [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 3,
        retryDelay: {
            "useLocationMetrics.useQuery": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
        }["useLocationMetrics.useQuery"],
        refetchOnWindowFocus: false,
        ...options
    });
};
_s3(useLocationMetrics, "sW6nyFUe1xv1wFg9wQphR1hv8KQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useReportingData = (filters, delegationIds)=>{
    _s4();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueries"])({
        queries: [
            {
                queryKey: reportingQueryKeys.delegationAnalytics(filters),
                queryFn: {
                    "useReportingData.useQueries": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getDelegationAnalytics(filters)
                }["useReportingData.useQueries"],
                staleTime: 5 * 60 * 1000,
                gcTime: 10 * 60 * 1000,
                retry: 3,
                retryDelay: {
                    "useReportingData.useQueries": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
                }["useReportingData.useQueries"]
            },
            {
                queryKey: reportingQueryKeys.taskMetrics(delegationIds),
                queryFn: {
                    "useReportingData.useQueries": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getTaskMetrics(delegationIds)
                }["useReportingData.useQueries"],
                staleTime: 2 * 60 * 1000,
                gcTime: 5 * 60 * 1000,
                retry: 3,
                retryDelay: {
                    "useReportingData.useQueries": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
                }["useReportingData.useQueries"]
            },
            {
                queryKey: reportingQueryKeys.trends(filters),
                queryFn: {
                    "useReportingData.useQueries": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getTrendData(filters)
                }["useReportingData.useQueries"],
                staleTime: 5 * 60 * 1000,
                gcTime: 10 * 60 * 1000,
                retry: 3,
                retryDelay: {
                    "useReportingData.useQueries": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
                }["useReportingData.useQueries"]
            },
            {
                queryKey: reportingQueryKeys.locationMetrics(filters),
                queryFn: {
                    "useReportingData.useQueries": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getLocationMetrics(filters)
                }["useReportingData.useQueries"],
                staleTime: 5 * 60 * 1000,
                gcTime: 10 * 60 * 1000,
                retry: 3,
                retryDelay: {
                    "useReportingData.useQueries": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
                }["useReportingData.useQueries"]
            }
        ]
    });
};
_s4(useReportingData, "VA1QY/lBnNflnomfjItiWlphNVs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueries"]
    ];
});
const usePrefetchReportingData = ()=>{
    _s5();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const prefetchDelegationAnalytics = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePrefetchReportingData.useCallback[prefetchDelegationAnalytics]": (filters)=>{
            return queryClient.prefetchQuery({
                queryKey: reportingQueryKeys.delegationAnalytics(filters),
                queryFn: {
                    "usePrefetchReportingData.useCallback[prefetchDelegationAnalytics]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getDelegationAnalytics(filters)
                }["usePrefetchReportingData.useCallback[prefetchDelegationAnalytics]"],
                staleTime: 5 * 60 * 1000
            });
        }
    }["usePrefetchReportingData.useCallback[prefetchDelegationAnalytics]"], [
        queryClient
    ]);
    const prefetchTaskMetrics = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePrefetchReportingData.useCallback[prefetchTaskMetrics]": (delegationIds)=>{
            return queryClient.prefetchQuery({
                queryKey: reportingQueryKeys.taskMetrics(delegationIds),
                queryFn: {
                    "usePrefetchReportingData.useCallback[prefetchTaskMetrics]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getTaskMetrics(delegationIds)
                }["usePrefetchReportingData.useCallback[prefetchTaskMetrics]"],
                staleTime: 2 * 60 * 1000
            });
        }
    }["usePrefetchReportingData.useCallback[prefetchTaskMetrics]"], [
        queryClient
    ]);
    return {
        prefetchDelegationAnalytics,
        prefetchTaskMetrics
    };
};
_s5(usePrefetchReportingData, "LHHYIijaufB/dsgzxrYrkrw3mCQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"]
    ];
});
const useServiceHistory = (filters, options)=>{
    _s6();
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useServiceHistory.useMemo[queryKey]": ()=>reportingQueryKeys.serviceHistory(filters)
    }["useServiceHistory.useMemo[queryKey]"], [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useServiceHistory.useCallback[queryFn]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getServiceHistory(filters)
    }["useServiceHistory.useCallback[queryFn]"], [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 3 * 60 * 1000,
        gcTime: 8 * 60 * 1000,
        retry: 3,
        retryDelay: {
            "useServiceHistory.useQuery": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
        }["useServiceHistory.useQuery"],
        refetchOnWindowFocus: false,
        enabled: !!filters.includeServiceHistory,
        ...options
    });
};
_s6(useServiceHistory, "sW6nyFUe1xv1wFg9wQphR1hv8KQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useTaskAnalytics = (filters, options)=>{
    _s7();
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useTaskAnalytics.useMemo[queryKey]": ()=>reportingQueryKeys.taskAnalytics(filters)
    }["useTaskAnalytics.useMemo[queryKey]"], [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTaskAnalytics.useCallback[queryFn]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getTaskAnalytics(filters)
    }["useTaskAnalytics.useCallback[queryFn]"], [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 3 * 60 * 1000,
        gcTime: 8 * 60 * 1000,
        retry: 3,
        retryDelay: {
            "useTaskAnalytics.useQuery": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
        }["useTaskAnalytics.useQuery"],
        refetchOnWindowFocus: false,
        enabled: !!filters.includeTaskData,
        ...options
    });
};
_s7(useTaskAnalytics, "sW6nyFUe1xv1wFg9wQphR1hv8KQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useVehicleAnalytics = (filters, options)=>{
    _s8();
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useVehicleAnalytics.useMemo[queryKey]": ()=>reportingQueryKeys.vehicleAnalytics(filters)
    }["useVehicleAnalytics.useMemo[queryKey]"], [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useVehicleAnalytics.useCallback[queryFn]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getVehicleAnalytics(filters)
    }["useVehicleAnalytics.useCallback[queryFn]"], [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 3,
        retryDelay: {
            "useVehicleAnalytics.useQuery": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
        }["useVehicleAnalytics.useQuery"],
        refetchOnWindowFocus: false,
        enabled: !!filters.includeVehicleAnalytics,
        ...options
    });
};
_s8(useVehicleAnalytics, "sW6nyFUe1xv1wFg9wQphR1hv8KQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useEmployeeAnalytics = (filters, options)=>{
    _s9();
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useEmployeeAnalytics.useMemo[queryKey]": ()=>reportingQueryKeys.employeeAnalytics(filters)
    }["useEmployeeAnalytics.useMemo[queryKey]"], [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useEmployeeAnalytics.useCallback[queryFn]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getEmployeeAnalytics(filters)
    }["useEmployeeAnalytics.useCallback[queryFn]"], [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 4 * 60 * 1000,
        gcTime: 9 * 60 * 1000,
        retry: 3,
        retryDelay: {
            "useEmployeeAnalytics.useQuery": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
        }["useEmployeeAnalytics.useQuery"],
        refetchOnWindowFocus: false,
        enabled: !!filters.includeEmployeeMetrics,
        ...options
    });
};
_s9(useEmployeeAnalytics, "sW6nyFUe1xv1wFg9wQphR1hv8KQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useCrossEntityAnalytics = (filters, options)=>{
    _s10();
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useCrossEntityAnalytics.useMemo[queryKey]": ()=>reportingQueryKeys.crossEntityAnalytics(filters)
    }["useCrossEntityAnalytics.useMemo[queryKey]"], [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useCrossEntityAnalytics.useCallback[queryFn]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getCrossEntityAnalytics(filters)
    }["useCrossEntityAnalytics.useCallback[queryFn]"], [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 6 * 60 * 1000,
        gcTime: 12 * 60 * 1000,
        retry: 3,
        retryDelay: {
            "useCrossEntityAnalytics.useQuery": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
        }["useCrossEntityAnalytics.useQuery"],
        refetchOnWindowFocus: false,
        enabled: !!(filters.includeCrossEntityCorrelations || filters.includeTaskData || filters.includeEmployeeMetrics || filters.includeVehicleAnalytics),
        ...options
    });
};
_s10(useCrossEntityAnalytics, "sW6nyFUe1xv1wFg9wQphR1hv8KQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
const useServiceCostSummary = (filters, options)=>{
    _s11();
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useServiceCostSummary.useMemo[queryKey]": ()=>reportingQueryKeys.serviceCosts(filters)
    }["useServiceCostSummary.useMemo[queryKey]"], [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useServiceCostSummary.useCallback[queryFn]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingDataService"].getServiceCostSummary(filters)
    }["useServiceCostSummary.useCallback[queryFn]"], [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 3,
        retryDelay: {
            "useServiceCostSummary.useQuery": (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
        }["useServiceCostSummary.useQuery"],
        refetchOnWindowFocus: false,
        enabled: !!filters.includeServiceHistory,
        ...options
    });
};
_s11(useServiceCostSummary, "sW6nyFUe1xv1wFg9wQphR1hv8KQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQuery"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/data/hooks/useRealtimeReportingUpdates.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// frontend/src/components/features/reporting/data/hooks/useRealtimeReportingUpdates.ts
__turbopack_context__.s({
    "WebSocketConnectionState": (()=>WebSocketConnectionState),
    "useRealtimeReportingUpdates": (()=>useRealtimeReportingUpdates)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
var WebSocketConnectionState = /*#__PURE__*/ function(WebSocketConnectionState) {
    WebSocketConnectionState["CONNECTING"] = "connecting";
    WebSocketConnectionState["CONNECTED"] = "connected";
    WebSocketConnectionState["DISCONNECTED"] = "disconnected";
    WebSocketConnectionState["RECONNECTING"] = "reconnecting";
    WebSocketConnectionState["ERROR"] = "error";
    return WebSocketConnectionState;
}({});
const useRealtimeReportingUpdates = (filters, config = {})=>{
    _s();
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const wsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const reconnectTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const heartbeatIntervalRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const updateBatchRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(new Set());
    const batchTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Configuration with defaults using environment-aware configuration
    const envConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getEnvironmentConfig"])();
    const { url = `${envConfig.wsUrl}/ws/reporting`, reconnectInterval = 3000, maxReconnectAttempts = 5, heartbeatInterval = 30000, batchUpdateDelay = 1000 } = config;
    // Connection state management
    const [connectionState, setConnectionState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("disconnected");
    const [reconnectAttempts, setReconnectAttempts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [lastError, setLastError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    /**
   * Batched cache invalidation to prevent excessive re-renders
   * Collects multiple updates and processes them together
   */ const processBatchedUpdates = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRealtimeReportingUpdates.useCallback[processBatchedUpdates]": ()=>{
            if (updateBatchRef.current.size === 0) return;
            const updates = Array.from(updateBatchRef.current);
            updateBatchRef.current.clear();
            // Invalidate relevant queries based on batched updates
            const queryKeysToInvalidate = new Set();
            updates.forEach({
                "useRealtimeReportingUpdates.useCallback[processBatchedUpdates]": (updateType)=>{
                    switch(updateType){
                        case 'delegation-updated':
                            queryKeysToInvalidate.add('delegation-analytics');
                            queryKeysToInvalidate.add('trends');
                            queryKeysToInvalidate.add('location-metrics');
                            break;
                        case 'task-updated':
                            queryKeysToInvalidate.add('task-metrics');
                            break;
                        case 'analytics-refresh':
                            queryKeysToInvalidate.add('delegation-analytics');
                            queryKeysToInvalidate.add('trends');
                            queryKeysToInvalidate.add('location-metrics');
                            queryKeysToInvalidate.add('task-metrics');
                            break;
                        case 'location-updated':
                            queryKeysToInvalidate.add('location-metrics');
                            break;
                    }
                }
            }["useRealtimeReportingUpdates.useCallback[processBatchedUpdates]"]);
            // Invalidate queries efficiently
            queryKeysToInvalidate.forEach({
                "useRealtimeReportingUpdates.useCallback[processBatchedUpdates]": (queryType)=>{
                    switch(queryType){
                        case 'delegation-analytics':
                            queryClient.invalidateQueries({
                                queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingQueryKeys"].analytics()
                            });
                            break;
                        case 'task-metrics':
                            queryClient.invalidateQueries({
                                queryKey: [
                                    ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingQueryKeys"].all,
                                    'tasks',
                                    'metrics'
                                ]
                            });
                            break;
                        case 'trends':
                            queryClient.invalidateQueries({
                                queryKey: [
                                    ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingQueryKeys"].all,
                                    'trends'
                                ]
                            });
                            break;
                        case 'location-metrics':
                            queryClient.invalidateQueries({
                                queryKey: [
                                    ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reportingQueryKeys"].all,
                                    'locations',
                                    'metrics'
                                ]
                            });
                            break;
                    }
                }
            }["useRealtimeReportingUpdates.useCallback[processBatchedUpdates]"]);
        }
    }["useRealtimeReportingUpdates.useCallback[processBatchedUpdates]"], [
        queryClient
    ]);
    /**
   * Handles incoming WebSocket messages with proper error handling
   */ const handleMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRealtimeReportingUpdates.useCallback[handleMessage]": (event)=>{
            try {
                const message = JSON.parse(event.data);
                // Add to batch for processing
                updateBatchRef.current.add(message.type);
                // Clear existing batch timeout and set new one
                if (batchTimeoutRef.current) {
                    clearTimeout(batchTimeoutRef.current);
                }
                batchTimeoutRef.current = setTimeout({
                    "useRealtimeReportingUpdates.useCallback[handleMessage]": ()=>{
                        processBatchedUpdates();
                    }
                }["useRealtimeReportingUpdates.useCallback[handleMessage]"], batchUpdateDelay);
            } catch (error) {
                console.error('Failed to parse WebSocket message:', error);
            }
        }
    }["useRealtimeReportingUpdates.useCallback[handleMessage]"], [
        processBatchedUpdates,
        batchUpdateDelay
    ]);
    /**
   * Establishes WebSocket connection with proper error handling
   */ const connect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRealtimeReportingUpdates.useCallback[connect]": ()=>{
            if (wsRef.current?.readyState === WebSocket.OPEN) {
                return; // Already connected
            }
            setConnectionState("connecting");
            setLastError(null);
            try {
                const ws = new WebSocket(url);
                wsRef.current = ws;
                ws.onopen = ({
                    "useRealtimeReportingUpdates.useCallback[connect]": ()=>{
                        setConnectionState("connected");
                        setReconnectAttempts(0);
                        // Send subscription message with current filters
                        ws.send(JSON.stringify({
                            type: 'subscribe',
                            filters: filters,
                            timestamp: new Date().toISOString()
                        }));
                        // Start heartbeat
                        heartbeatIntervalRef.current = setInterval({
                            "useRealtimeReportingUpdates.useCallback[connect]": ()=>{
                                if (ws.readyState === WebSocket.OPEN) {
                                    ws.send(JSON.stringify({
                                        type: 'ping'
                                    }));
                                }
                            }
                        }["useRealtimeReportingUpdates.useCallback[connect]"], heartbeatInterval);
                    }
                })["useRealtimeReportingUpdates.useCallback[connect]"];
                ws.onmessage = handleMessage;
                ws.onclose = ({
                    "useRealtimeReportingUpdates.useCallback[connect]": (event)=>{
                        setConnectionState("disconnected");
                        // Clear heartbeat
                        if (heartbeatIntervalRef.current) {
                            clearInterval(heartbeatIntervalRef.current);
                            heartbeatIntervalRef.current = null;
                        }
                        // Attempt reconnection if not a clean close
                        if (!event.wasClean && reconnectAttempts < maxReconnectAttempts) {
                            setConnectionState("reconnecting");
                            setReconnectAttempts({
                                "useRealtimeReportingUpdates.useCallback[connect]": (prev)=>prev + 1
                            }["useRealtimeReportingUpdates.useCallback[connect]"]);
                            reconnectTimeoutRef.current = setTimeout({
                                "useRealtimeReportingUpdates.useCallback[connect]": ()=>{
                                    connect();
                                }
                            }["useRealtimeReportingUpdates.useCallback[connect]"], reconnectInterval);
                        }
                    }
                })["useRealtimeReportingUpdates.useCallback[connect]"];
                ws.onerror = ({
                    "useRealtimeReportingUpdates.useCallback[connect]": (error)=>{
                        console.error('WebSocket error:', error);
                        setConnectionState("error");
                        setLastError('WebSocket connection failed');
                    }
                })["useRealtimeReportingUpdates.useCallback[connect]"];
            } catch (error) {
                console.error('Failed to create WebSocket connection:', error);
                setConnectionState("error");
                setLastError(error instanceof Error ? error.message : 'Unknown connection error');
            }
        }
    }["useRealtimeReportingUpdates.useCallback[connect]"], [
        url,
        filters,
        handleMessage,
        reconnectAttempts,
        maxReconnectAttempts,
        reconnectInterval,
        heartbeatInterval
    ]);
    /**
   * Cleanly disconnects WebSocket connection
   */ const disconnect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRealtimeReportingUpdates.useCallback[disconnect]": ()=>{
            // Clear all timeouts
            if (reconnectTimeoutRef.current) {
                clearTimeout(reconnectTimeoutRef.current);
                reconnectTimeoutRef.current = null;
            }
            if (heartbeatIntervalRef.current) {
                clearInterval(heartbeatIntervalRef.current);
                heartbeatIntervalRef.current = null;
            }
            if (batchTimeoutRef.current) {
                clearTimeout(batchTimeoutRef.current);
                batchTimeoutRef.current = null;
            }
            // Close WebSocket connection
            if (wsRef.current) {
                wsRef.current.close(1000, 'Component unmounting');
                wsRef.current = null;
            }
            setConnectionState("disconnected");
            setReconnectAttempts(0);
            setLastError(null);
        }
    }["useRealtimeReportingUpdates.useCallback[disconnect]"], []);
    /**
   * Manually trigger reconnection
   */ const reconnect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useRealtimeReportingUpdates.useCallback[reconnect]": ()=>{
            disconnect();
            setReconnectAttempts(0);
            setTimeout(connect, 100); // Small delay to ensure clean disconnect
        }
    }["useRealtimeReportingUpdates.useCallback[reconnect]"], [
        disconnect,
        connect
    ]);
    // Auto-connect on mount and filter changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useRealtimeReportingUpdates.useEffect": ()=>{
            connect();
            return disconnect;
        }
    }["useRealtimeReportingUpdates.useEffect"], [
        connect,
        disconnect
    ]);
    // Update subscription when filters change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useRealtimeReportingUpdates.useEffect": ()=>{
            if (wsRef.current?.readyState === WebSocket.OPEN) {
                wsRef.current.send(JSON.stringify({
                    type: 'update-subscription',
                    filters: filters,
                    timestamp: new Date().toISOString()
                }));
            }
        }
    }["useRealtimeReportingUpdates.useEffect"], [
        filters
    ]);
    return {
        connectionState,
        reconnectAttempts,
        lastError,
        connect,
        disconnect,
        reconnect,
        isConnected: connectionState === "connected",
        isConnecting: connectionState === "connecting",
        isReconnecting: connectionState === "reconnecting",
        hasError: connectionState === "error"
    };
};
_s(useRealtimeReportingUpdates, "VEWNzxYZlgKfV5+cEYVwFMwQj9E=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/data/hooks/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Export specific hooks to avoid conflicts
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingData.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useDelegations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useDelegations.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useRealtimeReportingUpdates$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useRealtimeReportingUpdates.ts [app-client] (ecmascript)");
// Export unique hooks from useReportingQueries
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-client] (ecmascript)");
;
;
;
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/data/hooks/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingData.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useDelegations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useDelegations.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useRealtimeReportingUpdates$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useRealtimeReportingUpdates.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-client] (ecmascript) <export useDelegationAnalytics as useDelegationAnalyticsQuery>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDelegationAnalyticsQuery": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useDelegationAnalytics"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-client] (ecmascript)");
}}),
"[project]/src/components/features/reporting/data/stores/useReportingFiltersStore.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// frontend/src/components/features/reporting/data/stores/useReportingFiltersStore.ts
__turbopack_context__.s({
    "useReportingFilters": (()=>useReportingFilters),
    "useReportingFiltersActions": (()=>useReportingFiltersActions),
    "useReportingFiltersPresets": (()=>useReportingFiltersPresets),
    "useReportingFiltersStore": (()=>useReportingFiltersStore),
    "useReportingFiltersUI": (()=>useReportingFiltersUI),
    "useReportingFiltersValidation": (()=>useReportingFiltersValidation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react/shallow.mjs [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature(), _s4 = __turbopack_context__.k.signature();
;
;
;
;
;
/**
 * Default filter values following business requirements
 * FIXED: Create stable default filters to prevent infinite re-renders
 * Use a function that creates fresh dates only when needed
 */ const getDefaultFilters = ()=>{
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    return {
        costRange: {
            max: 10_000,
            min: 0
        },
        dateRange: {
            from: thirtyDaysAgo,
            to: now
        },
        employees: [],
        includeServiceHistory: false,
        includeTaskData: false,
        locations: [],
        serviceStatus: [],
        // ENHANCED: Default service history values
        serviceTypes: [],
        status: [],
        vehicles: []
    };
};
/**
 * FIXED: Create stable default filters object to prevent infinite re-renders
 * Only create new dates when absolutely necessary
 */ const createStableDefaultFilters = ()=>{
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    return {
        costRange: {
            max: 10_000,
            min: 0
        },
        dateRange: {
            from: thirtyDaysAgo,
            to: now
        },
        employees: [],
        includeServiceHistory: false,
        includeTaskData: false,
        locations: [],
        serviceStatus: [],
        serviceTypes: [],
        status: [],
        vehicles: []
    };
};
// FIXED: Create stable default filters instance
const STABLE_DEFAULT_FILTERS = createStableDefaultFilters();
/**
 * Filter validation logic
 *
 * @param filters - Filters to validate
 * @returns Validation errors object
 */ const validateFiltersLogic = (filters)=>{
    const errors = {};
    // Date range validation
    if (filters.dateRange) {
        if (filters.dateRange.from > filters.dateRange.to) {
            errors.dateRange = 'Start date must be before end date';
        }
        const daysDiff = Math.abs(filters.dateRange.to.getTime() - filters.dateRange.from.getTime()) / (1000 * 60 * 60 * 24);
        if (daysDiff > 365) {
            errors.dateRange = 'Date range cannot exceed 365 days';
        }
    }
    // Status validation
    if (filters.status.length > 10) {
        errors.status = 'Too many statuses selected (maximum 10)';
    }
    // Location validation
    if (filters.locations.length > 50) {
        errors.locations = 'Too many locations selected (maximum 50)';
    }
    // Employee validation
    if (filters.employees.length > 100) {
        errors.employees = 'Too many employees selected (maximum 100)';
    }
    // Vehicle validation
    if (filters.vehicles.length > 100) {
        errors.vehicles = 'Too many vehicles selected (maximum 100)';
    }
    // ENHANCED: Service history validation
    if (filters.serviceTypes && filters.serviceTypes.length > 20) {
        errors.serviceTypes = 'Too many service types selected (maximum 20)';
    }
    if (filters.serviceStatus && filters.serviceStatus.length > 10) {
        errors.serviceStatus = 'Too many service statuses selected (maximum 10)';
    }
    if (filters.costRange) {
        if (filters.costRange.min < 0) {
            errors.costRange = 'Minimum cost cannot be negative';
        }
        if (filters.costRange.min >= filters.costRange.max) {
            errors.costRange = 'Minimum cost must be less than maximum cost';
        }
        if (filters.costRange.max > 1_000_000) {
            errors.costRange = 'Maximum cost cannot exceed $1,000,000';
        }
    }
    return errors;
};
const useReportingFiltersStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devtools"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subscribeWithSelector"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        applyFilters: ()=>{
            const { filters, isValid } = get();
            if (isValid) {
                set({
                    hasUnsavedChanges: false,
                    lastAppliedFilters: {
                        ...filters
                    }
                });
            }
        },
        // Preset management
        applyPreset: (presetName)=>{
            try {
                const stored = localStorage.getItem('reporting-filter-presets');
                const presets = stored ? JSON.parse(stored) : {};
                const preset = presets[presetName];
                if (preset) {
                    set((state)=>({
                            filters: {
                                ...preset
                            },
                            hasUnsavedChanges: true,
                            lastAppliedFilters: state.lastAppliedFilters
                        }));
                }
            } catch (error) {
                console.error('Failed to apply preset:', error);
            }
        },
        clearValidationErrors: ()=>{
            set({
                isValid: true,
                validationErrors: {}
            });
        },
        deletePreset: (name)=>{
            try {
                const stored = localStorage.getItem('reporting-filter-presets');
                const presets = stored ? JSON.parse(stored) : {};
                delete presets[name];
                localStorage.setItem('reporting-filter-presets', JSON.stringify(presets));
            } catch (error) {
                console.error('Failed to delete preset:', error);
            }
        },
        // Initial state
        filters: getDefaultFilters(),
        getPresets: ()=>{
            try {
                const stored = localStorage.getItem('reporting-filter-presets');
                return stored ? JSON.parse(stored) : {};
            } catch  {
                return {};
            }
        },
        hasUnsavedChanges: false,
        isFilterPanelOpen: false,
        isValid: true,
        lastAppliedFilters: getDefaultFilters(),
        resetFilters: ()=>{
            set({
                filters: getDefaultFilters(),
                hasUnsavedChanges: true,
                isValid: true,
                validationErrors: {}
            });
        },
        revertChanges: ()=>{
            const { lastAppliedFilters } = get();
            set({
                filters: {
                    ...lastAppliedFilters
                },
                hasUnsavedChanges: false,
                isValid: true,
                validationErrors: {}
            });
        },
        saveAsPreset: (name)=>{
            try {
                const { filters } = get();
                const stored = localStorage.getItem('reporting-filter-presets');
                const presets = stored ? JSON.parse(stored) : {};
                presets[name] = {
                    ...filters
                };
                localStorage.setItem('reporting-filter-presets', JSON.stringify(presets));
            } catch (error) {
                console.error('Failed to save preset:', error);
            }
        },
        setCostRange: (min, max)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    costRange: {
                        max,
                        min
                    }
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        // Filter value setters
        setDateRange: (from, to)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    dateRange: {
                        from,
                        to
                    }
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setEmployees: (employees)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    employees
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setFilterPanelOpen: (open)=>{
            set({
                isFilterPanelOpen: open
            });
        },
        // Bulk operations
        setFilters: (partialFilters)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    ...partialFilters
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setIncludeServiceHistory: (includeServiceHistory)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    includeServiceHistory
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setIncludeTaskData: (includeTaskData)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    includeTaskData
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setLocations: (locations)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    locations
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setServiceStatus: (serviceStatus)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    serviceStatus
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        // ENHANCED: Service history filter setters
        setServiceTypes: (serviceTypes)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    serviceTypes
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setStatus: (status)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    status
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setTaskPriorities: (taskPriorities)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    taskPriorities
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        // ENHANCED: Task filter setters
        setTaskStatus: (taskStatus)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    taskStatus
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setVehicles: (vehicles)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    vehicles
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        // UI state management
        toggleFilterPanel: ()=>{
            set((state)=>({
                    isFilterPanelOpen: !state.isFilterPanelOpen
                }));
        },
        // Validation
        validateFilters: ()=>{
            const { filters } = get();
            const errors = validateFiltersLogic(filters);
            const isValid = Object.keys(errors).length === 0;
            set({
                isValid,
                validationErrors: errors
            });
            return isValid;
        },
        validationErrors: {}
    }), {
    name: 'reporting-filters-storage',
    partialize: (state)=>({
            // Only persist essential filter state, not UI state
            filters: state.filters,
            lastAppliedFilters: state.lastAppliedFilters
        }),
    storage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createJSONStorage"])(()=>localStorage)
})), {
    name: 'reporting-filters-store'
}));
/**
 * Selector hooks for optimized component subscriptions
 *
 * Follows DRY principle by providing reusable selectors
 * FIXED: Create stable selectors to prevent infinite re-renders
 */ // FIXED: Create stable selector functions to prevent infinite loops
const filtersSelector = (state)=>state.filters;
const actionsSelector = (state)=>({
        applyFilters: state.applyFilters,
        resetFilters: state.resetFilters,
        revertChanges: state.revertChanges,
        setCostRange: state.setCostRange,
        setDateRange: state.setDateRange,
        setEmployees: state.setEmployees,
        setFilters: state.setFilters,
        setIncludeServiceHistory: state.setIncludeServiceHistory,
        setIncludeTaskData: state.setIncludeTaskData,
        setLocations: state.setLocations,
        setServiceStatus: state.setServiceStatus,
        setServiceTypes: state.setServiceTypes,
        setStatus: state.setStatus,
        setTaskPriorities: state.setTaskPriorities,
        setTaskStatus: state.setTaskStatus,
        setVehicles: state.setVehicles
    });
const uiSelector = (state)=>({
        hasUnsavedChanges: state.hasUnsavedChanges,
        isFilterPanelOpen: state.isFilterPanelOpen,
        setFilterPanelOpen: state.setFilterPanelOpen,
        toggleFilterPanel: state.toggleFilterPanel
    });
const validationSelector = (state)=>({
        clearValidationErrors: state.clearValidationErrors,
        isValid: state.isValid,
        validateFilters: state.validateFilters,
        validationErrors: state.validationErrors
    });
const presetsSelector = (state)=>({
        applyPreset: state.applyPreset,
        deletePreset: state.deletePreset,
        getPresets: state.getPresets,
        saveAsPreset: state.saveAsPreset
    });
const useReportingFilters = ()=>{
    _s();
    return useReportingFiltersStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useShallow"])(filtersSelector));
};
_s(useReportingFilters, "ti8kaJ2ClmyuOMiGp/OFa70ohbA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useShallow"],
        useReportingFiltersStore
    ];
});
const useReportingFiltersActions = ()=>{
    _s1();
    return useReportingFiltersStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useShallow"])(actionsSelector));
};
_s1(useReportingFiltersActions, "ti8kaJ2ClmyuOMiGp/OFa70ohbA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useShallow"],
        useReportingFiltersStore
    ];
});
const useReportingFiltersUI = ()=>{
    _s2();
    return useReportingFiltersStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useShallow"])(uiSelector));
};
_s2(useReportingFiltersUI, "ti8kaJ2ClmyuOMiGp/OFa70ohbA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useShallow"],
        useReportingFiltersStore
    ];
});
const useReportingFiltersValidation = ()=>{
    _s3();
    return useReportingFiltersStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useShallow"])(validationSelector));
};
_s3(useReportingFiltersValidation, "ti8kaJ2ClmyuOMiGp/OFa70ohbA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useShallow"],
        useReportingFiltersStore
    ];
});
const useReportingFiltersPresets = ()=>{
    _s4();
    return useReportingFiltersStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useShallow"])(presetsSelector));
};
_s4(useReportingFiltersPresets, "ti8kaJ2ClmyuOMiGp/OFa70ohbA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useShallow"],
        useReportingFiltersStore
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/data/stores/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$stores$2f$useReportingFiltersStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/stores/useReportingFiltersStore.ts [app-client] (ecmascript)");
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/features/reporting/data/stores/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$stores$2f$useReportingFiltersStore$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/stores/useReportingFiltersStore.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$stores$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/stores/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-client] (ecmascript) <export useTrendData as useTrendDataQuery>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useTrendDataQuery": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTrendData"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-client] (ecmascript)");
}}),
"[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-client] (ecmascript) <export useLocationMetrics as useLocationMetricsQuery>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useLocationMetricsQuery": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocationMetrics"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-client] (ecmascript)");
}}),
}]);

//# sourceMappingURL=src_components_features_reporting_data_95e596cd._.js.map