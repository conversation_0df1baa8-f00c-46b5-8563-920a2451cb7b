"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7515],{42366:(e,t,a)=>{a.r(t),a.d(t,{useNotifications:()=>n,useWorkHubNotifications:()=>o});var l=a(12115),i=a(96016);let n=()=>{let e=(0,i.C)(e=>e.addNotification),t=(0,i.C)(e=>e.removeNotification),a=(0,i.C)(e=>e.clearAllNotifications),n=(0,i.C)(e=>e.unreadNotificationCount),o=(0,l.useCallback)(t=>{e({message:t,type:"success"})},[e]),r=(0,l.useCallback)(t=>{e({message:t,type:"error"})},[e]),s=(0,l.useCallback)(t=>{e({message:t,type:"warning"})},[e]),c=(0,l.useCallback)(t=>{e({message:t,type:"info"})},[e]),u=(0,l.useCallback)((e,t,a)=>{e?o(t):r(a)},[o,r]),d=(0,l.useCallback)(function(a,l){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5e3;e({message:l,type:a}),setTimeout(()=>{let e=i.C.getState().notifications.at(-1);e&&e.message===l&&t(e.id)},n)},[e,t]),m=(0,l.useCallback)(function(){var t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Loading...";return e({message:a,type:"info"}),null==(t=i.C.getState().notifications.at(-1))?void 0:t.id},[e]),p=(0,l.useCallback)((e,a,l)=>{t(e),a?o(l):r(l)},[t,o,r]);return{clearAllNotifications:a,removeNotification:t,showApiResult:u,showError:r,showInfo:c,showLoading:m,showSuccess:o,showTemporary:d,showWarning:s,unreadCount:n,updateLoadingNotification:p}},o=()=>{let{clearAllNotifications:e,removeNotification:t,showError:a,showInfo:o,showSuccess:r,showWarning:s,unreadCount:c}=n(),u=(0,l.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"delegation",message:e,type:"delegation-update"})},[]),d=(0,l.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"vehicle",message:e,type:"vehicle-maintenance"})},[]),m=(0,l.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"task",message:e,type:"task-assigned"})},[]);return{clearAllNotifications:e,removeNotification:t,showDelegationUpdate:u,showEmployeeUpdate:(0,l.useCallback)((e,t)=>{(0,i.C.getState().addNotification)({...t&&{actionUrl:t},category:"employee",message:e,type:"employee-update"})},[]),showError:a,showInfo:o,showSuccess:r,showTaskAssigned:m,showVehicleMaintenance:d,showWarning:s,unreadCount:c}}},66695:(e,t,a)=>{a.d(t,{BT:()=>c,Wu:()=>u,ZB:()=>s,Zp:()=>o,aR:()=>r,wL:()=>d});var l=a(95155),i=a(12115),n=a(54036);let o=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,l.jsx)("div",{className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),ref:t,...i})});o.displayName="Card";let r=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,l.jsx)("div",{className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),ref:t,...i})});r.displayName="CardHeader";let s=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,l.jsx)("div",{className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),ref:t,...i})});s.displayName="CardTitle";let c=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,l.jsx)("div",{className:(0,n.cn)("text-sm text-muted-foreground",a),ref:t,...i})});c.displayName="CardDescription";let u=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,l.jsx)("div",{className:(0,n.cn)("p-6 pt-0",a),ref:t,...i})});u.displayName="CardContent";let d=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,l.jsx)("div",{className:(0,n.cn)("flex items-center p-6 pt-0",a),ref:t,...i})});d.displayName="CardFooter"},83761:(e,t,a)=>{a.d(t,{Ar:()=>y,Db:()=>f,FT:()=>g,nR:()=>d,sZ:()=>m,uC:()=>p});var l=a(26715),i=a(5041),n=a(90111),o=a(42366),r=a(38069),s=a(54120),c=a(75908);let u={all:["employees"],detail:e=>["employees",e]},d=e=>(0,n.GK)([...u.all],async()=>(await c.employeeApiService.getAll()).data,"employee",{staleTime:3e5,...e}),m=(e,t)=>(0,n.GK)(e?["employees","role",e]:[...u.all],async()=>e?await c.employeeApiService.getByRole(e):(await c.employeeApiService.getAll()).data,"employee",{staleTime:3e5,...t}),p=e=>(0,n.GK)([...u.detail(e)],async()=>await c.employeeApiService.getById(e),"employee",{enabled:!!e,staleTime:3e5}),y=()=>{let e=(0,l.jE)(),{showError:t,showSuccess:a}=(0,o.useNotifications)();return(0,i.n)({mutationFn:async e=>{let t=r.A.toCreateRequest(e);return await c.employeeApiService.create(t)},onError:(a,l,i)=>{(null==i?void 0:i.previousEmployees)&&e.setQueryData(u.all,i.previousEmployees),t("Failed to create employee: ".concat(a.message||"Unknown error occurred"))},onMutate:async t=>{await e.cancelQueries({queryKey:u.all});let a=e.getQueryData(u.all);return e.setQueryData(u.all,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],a="optimistic-"+Date.now().toString(),l=new Date().toISOString();return[...e,{availability:(0,s.d$)(t.availability),contactEmail:(0,s.d$)(t.contactEmail),contactInfo:t.contactInfo,contactMobile:(0,s.d$)(t.contactMobile),contactPhone:(0,s.d$)(t.contactPhone),createdAt:l,currentLocation:(0,s.d$)(t.currentLocation),department:(0,s.d$)(t.department),employeeId:t.employeeId,fullName:t.fullName||t.name,generalAssignments:t.generalAssignments||[],hireDate:(0,s.d$)(t.hireDate),id:Number(a.replace("optimistic-","")),name:t.name,notes:(0,s.d$)(t.notes),position:(0,s.d$)(t.position),profileImageUrl:(0,s.d$)(t.profileImageUrl),role:t.role,shiftSchedule:(0,s.d$)(t.shiftSchedule),skills:t.skills||[],status:(0,s.d$)(t.status),updatedAt:l,...void 0!==t.workingHours&&{workingHours:(0,s.d$)(t.workingHours)}}]}),{previousEmployees:a}},onSettled:()=>{e.invalidateQueries({queryKey:u.all})},onSuccess:e=>{a('Employee "'.concat(e.name,'" has been created successfully!'))}})},f=()=>{let e=(0,l.jE)();return(0,i.n)({mutationFn:async e=>{let{data:t,id:a}=e,l=r.A.toUpdateRequest(t);return await c.employeeApiService.update(a,l)},onError:(t,a,l)=>{(null==l?void 0:l.previousEmployee)&&e.setQueryData(u.detail(a.id),l.previousEmployee),(null==l?void 0:l.previousEmployeesList)&&e.setQueryData(u.all,l.previousEmployeesList),console.error("Failed to update employee:",t)},onMutate:async t=>{let{data:a,id:l}=t;await e.cancelQueries({queryKey:u.all}),await e.cancelQueries({queryKey:u.detail(l)});let i=e.getQueryData(u.detail(l)),n=e.getQueryData(u.all);return e.setQueryData(u.detail(l),e=>{var t,l,i,n,o,r,c,u,d,m,p;if(!e)return e;let y=new Date().toISOString();return{...e,availability:(0,s.d$)(void 0===a.availability?e.availability:a.availability),contactEmail:(0,s.d$)(void 0===a.contactEmail?e.contactEmail:a.contactEmail),contactInfo:null!=(t=a.contactInfo)?t:e.contactInfo,contactMobile:(0,s.d$)(void 0===a.contactMobile?e.contactMobile:a.contactMobile),contactPhone:(0,s.d$)(void 0===a.contactPhone?e.contactPhone:a.contactPhone),currentLocation:(0,s.d$)(void 0===a.currentLocation?e.currentLocation:a.currentLocation),department:(0,s.d$)(null!=(l=a.department)?l:e.department),employeeId:null!=(i=a.employeeId)?i:e.employeeId,fullName:(0,s.d$)(null!=(n=a.fullName)?n:e.fullName),generalAssignments:null!=(o=a.generalAssignments)?o:e.generalAssignments,hireDate:(0,s.d$)(null!=(r=a.hireDate)?r:e.hireDate),name:null!=(c=a.name)?c:e.name,notes:(0,s.d$)(void 0===a.notes?e.notes:a.notes),position:(0,s.d$)(null!=(u=a.position)?u:e.position),profileImageUrl:(0,s.d$)(void 0===a.profileImageUrl?e.profileImageUrl:a.profileImageUrl),role:null!=(d=a.role)?d:e.role,shiftSchedule:(0,s.d$)(void 0===a.shiftSchedule?e.shiftSchedule:a.shiftSchedule),skills:null!=(m=a.skills)?m:e.skills,status:(0,s.d$)(null!=(p=a.status)?p:e.status),updatedAt:y,...void 0!==a.workingHours&&{workingHours:(0,s.d$)(a.workingHours)}}}),e.setQueryData(u.all,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.map(e=>{if(e.id===Number(l)){var t,i,n,o,r,c,u,d,m,p,y,f,g,v,h,b,N,$,k;let l=new Date().toISOString();return{...e,availability:(0,s.d$)(null!=(t=a.availability)?t:e.availability),contactEmail:(0,s.d$)(null!=(i=a.contactEmail)?i:e.contactEmail),contactInfo:null!=(n=a.contactInfo)?n:e.contactInfo,contactMobile:(0,s.d$)(null!=(o=a.contactMobile)?o:e.contactMobile),contactPhone:(0,s.d$)(null!=(r=a.contactPhone)?r:e.contactPhone),currentLocation:(0,s.d$)(null!=(c=a.currentLocation)?c:e.currentLocation),department:(0,s.d$)(null!=(u=a.department)?u:e.department),employeeId:null!=(d=a.employeeId)?d:e.employeeId,fullName:(0,s.d$)(null!=(m=a.fullName)?m:e.fullName),generalAssignments:null!=(p=a.generalAssignments)?p:e.generalAssignments,hireDate:(0,s.d$)(null!=(y=a.hireDate)?y:e.hireDate),name:null!=(f=a.name)?f:e.name,notes:(0,s.d$)(null!=(g=a.notes)?g:e.notes),position:(0,s.d$)(null!=(v=a.position)?v:e.position),profileImageUrl:(0,s.d$)(null!=(h=a.profileImageUrl)?h:e.profileImageUrl),role:null!=(b=a.role)?b:e.role,shiftSchedule:(0,s.d$)(null!=(N=a.shiftSchedule)?N:e.shiftSchedule),skills:null!=($=a.skills)?$:e.skills,status:(0,s.d$)(null!=(k=a.status)?k:e.status),updatedAt:l,...void 0!==a.workingHours&&{workingHours:(0,s.d$)(a.workingHours)}}}return e})}),{previousEmployee:i,previousEmployeesList:n}},onSettled:(t,a,l)=>{e.invalidateQueries({queryKey:u.detail(l.id)}),e.invalidateQueries({queryKey:u.all})}})},g=()=>{let e=(0,l.jE)();return(0,i.n)({mutationFn:async e=>(await c.employeeApiService.delete(e),e),onError:(t,a,l)=>{(null==l?void 0:l.previousEmployeesList)&&e.setQueryData(u.all,l.previousEmployeesList),console.error("Failed to delete employee:",t)},onMutate:async t=>{await e.cancelQueries({queryKey:u.all}),await e.cancelQueries({queryKey:u.detail(t)});let a=e.getQueryData(u.all);return e.setQueryData(u.all,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.filter(e=>e.id!==Number(t))}),e.removeQueries({queryKey:u.detail(t)}),{previousEmployeesList:a}},onSettled:()=>{e.invalidateQueries({queryKey:u.all})}})}},96016:(e,t,a)=>{a.d(t,{C:()=>n});var l=a(65453),i=a(46786);let n=(0,l.v)()((0,i.lt)((0,i.Zr)((e,t)=>({addNotification:t=>e(e=>({notifications:[...e.notifications,{...t,id:crypto.randomUUID(),read:!1,timestamp:new Date().toISOString()}]})),clearAllNotifications:()=>e({notifications:[]}),currentTheme:"light",markNotificationAsRead:t=>e(e=>({notifications:e.notifications.map(e=>e.id===t?{...e,read:!0}:e)})),notifications:[],removeNotification:t=>e(e=>({notifications:e.notifications.filter(e=>e.id!==t)})),setTheme:t=>{e({currentTheme:t})},sidebarOpen:!1,toggleSidebar:()=>e(e=>({sidebarOpen:!e.sidebarOpen})),unreadNotificationCount:()=>{let{notifications:e}=t();return e.filter(e=>!e.read).length}}),{name:"workhub-app-store",partialize:e=>({currentTheme:e.currentTheme})}),{name:"app-store"}))}}]);