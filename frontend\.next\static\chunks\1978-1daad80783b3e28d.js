"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1978],{26119:(e,s,a)=>{a.d(s,{n:()=>i});var t=a(65453),r=a(46786);let i=(0,t.v)()((0,r.lt)((0,r.Zr)(e=>({autoRefreshInterval:30,closeModal:()=>e({isModalOpen:!1,modalContent:null}),dashboardLayout:"cards",fontSize:"medium",isModalOpen:!1,mapViewPreference:"roadmap",modalContent:null,notificationsEnabled:!0,openModal:s=>e({isModalOpen:!0,modalContent:s}),setAutoRefreshInterval:s=>e({autoRefreshInterval:s}),setDashboardLayout:s=>e({dashboardLayout:s}),setFontSize:s=>e({fontSize:s}),setMapViewPreference:s=>e({mapViewPreference:s}),setTableDensity:s=>e({tableDensity:s}),tableDensity:"comfortable",toggleNotifications:()=>e(e=>({notificationsEnabled:!e.notificationsEnabled}))}),{name:"workhub-ui-store",partialize:e=>({autoRefreshInterval:e.autoRefreshInterval,dashboardLayout:e.dashboardLayout,fontSize:e.fontSize,mapViewPreference:e.mapViewPreference,notificationsEnabled:e.notificationsEnabled,tableDensity:e.tableDensity})}),{name:"ui-store"}))},27945:(e,s,a)=>{a.d(s,{A5:()=>u,Ln:()=>x,uq:()=>m});var t=a(95155),r=a(73350),i=a(10518);a(12115);var l=a(26126),n=a(30285),c=a(66695),d=a(36846);let o=[{className:"text-sm",description:"Compact text for more content",example:"The quick brown fox jumps over the lazy dog",label:"Small",value:"small"},{className:"text-base",description:"Standard readable text",example:"The quick brown fox jumps over the lazy dog",label:"Medium",value:"medium"},{className:"text-lg",description:"Larger text for better accessibility",example:"The quick brown fox jumps over the lazy dog",label:"Large",value:"large"}],m=()=>{let{fontSize:e,getFontSizeClass:s,setFontSize:a}=(0,d.useUiPreferences)();return(0,t.jsxs)(c.Zp,{className:"w-full max-w-2xl",children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(r.A,{className:"size-5"}),"Font Size Preferences"]}),(0,t.jsx)(c.BT,{children:"Choose your preferred font size for better readability and accessibility"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-muted/50 p-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Current Font Size"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Applied across the entire application"})]}),(0,t.jsx)(l.E,{className:"capitalize",variant:"secondary",children:e})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h4",{className:"text-sm font-medium uppercase tracking-wide text-muted-foreground",children:"Available Options"}),o.map(s=>(0,t.jsx)("div",{className:"\n                relative cursor-pointer rounded-lg border p-4 transition-all\n                ".concat(e===s.value?"border-primary bg-primary/5 ring-1 ring-primary/20":"border-border hover:border-primary/50 hover:bg-muted/30","\n              "),onClick:()=>a(s.value),children:(0,t.jsx)("div",{className:"flex items-start justify-between",children:(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:[(0,t.jsx)("h5",{className:"font-medium",children:s.label}),e===s.value&&(0,t.jsx)(i.A,{className:"size-4 text-primary"})]}),(0,t.jsx)("p",{className:"mb-3 text-sm text-muted-foreground",children:s.description}),(0,t.jsx)("div",{className:"rounded border bg-background p-3 ".concat(s.className),children:s.example})]})})},s.value))]}),(0,t.jsxs)("div",{className:"flex items-center justify-between border-t pt-4",children:[(0,t.jsx)("div",{className:"flex gap-2",children:o.map(s=>(0,t.jsx)(n.$,{className:"capitalize",onClick:()=>a(s.value),size:"sm",variant:e===s.value?"default":"outline",children:s.label},s.value))}),(0,t.jsx)(n.$,{className:"text-muted-foreground",onClick:()=>a("medium"),size:"sm",variant:"ghost",children:"Reset to Default"})]}),(0,t.jsxs)("div",{className:"rounded-lg border bg-background p-4",children:[(0,t.jsx)("h5",{className:"mb-2 font-medium",children:"Live Preview"}),(0,t.jsxs)("div",{className:"space-y-2 ".concat(s()),children:[(0,t.jsx)("p",{className:"font-semibold",children:"Heading Text"}),(0,t.jsx)("p",{children:"This is how regular paragraph text will appear with your selected font size. The setting applies to all text content throughout the WorkHub application."}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Secondary text and descriptions will also scale accordingly."})]})]})]})]})},u=()=>{let{fontSize:e,setFontSize:s}=(0,d.useUiPreferences)();return(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(r.A,{className:"size-4 text-muted-foreground"}),(0,t.jsxs)("select",{className:"   rounded border border-input bg-background px-2 py-1 text-sm   focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2   ",onChange:e=>s(e.target.value),value:e,children:[(0,t.jsx)("option",{value:"small",children:"Small"}),(0,t.jsx)("option",{value:"medium",children:"Medium"}),(0,t.jsx)("option",{value:"large",children:"Large"})]})]})},x=()=>{let{fontSize:e,setFontSize:s}=(0,d.useUiPreferences)();return(0,t.jsxs)(n.$,{className:"flex items-center gap-2",onClick:()=>{let a=["small","medium","large"],t=(a.indexOf(e)+1)%a.length,r=a[t];r&&s(r)},size:"sm",title:"Current: ".concat(e,". Click to cycle."),variant:"ghost",children:[(0,t.jsx)(r.A,{className:"size-4"}),(0,t.jsx)("span",{className:"text-xs capitalize",children:e})]})}},36846:(e,s,a)=>{a.r(s),a.d(s,{useUiPreferences:()=>i});var t=a(12115),r=a(26119);let i=()=>{let e=(0,r.n)(e=>e.fontSize),s=(0,r.n)(e=>e.setFontSize),a=(0,r.n)(e=>e.notificationsEnabled),i=(0,r.n)(e=>e.toggleNotifications),l=(0,r.n)(e=>e.tableDensity),n=(0,r.n)(e=>e.setTableDensity),c=(0,r.n)(e=>e.mapViewPreference),d=(0,r.n)(e=>e.setMapViewPreference),o=(0,r.n)(e=>e.dashboardLayout),m=(0,r.n)(e=>e.setDashboardLayout),u=(0,r.n)(e=>e.autoRefreshInterval),x=(0,r.n)(e=>e.setAutoRefreshInterval),h=(0,t.useCallback)(()=>{switch(e){case"large":return"text-lg";case"small":return"text-sm";default:return"text-base"}},[e]),p=(0,t.useCallback)(()=>{switch(l){case"compact":return{cell:"py-1 px-2",row:"h-8",table:"table-compact"};case"spacious":return{cell:"py-4 px-4",row:"h-16",table:"table-spacious"};default:return{cell:"py-2 px-3",row:"h-12",table:"table-comfortable"}}},[l]),f=(0,t.useCallback)(()=>{switch(o){case"cards":default:return"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6";case"grid":return"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4";case"list":return"flex flex-col space-y-4"}},[o]),b=(0,t.useCallback)(()=>{a||i()},[a,i]),g=(0,t.useCallback)(()=>{a&&i()},[a,i]),j=(0,t.useCallback)(()=>{s("medium"),n("comfortable"),d("roadmap"),m("cards"),x(30)},[s,n,d,m,x]),v=(0,t.useCallback)(()=>({autoRefreshInterval:u,dashboardLayout:o,fontSize:e,mapViewPreference:c,notificationsEnabled:a,tableDensity:l}),[e,a,l,c,o,u]);return{autoRefreshInterval:u,dashboardLayout:o,disableNotifications:g,enableNotifications:b,fontSize:e,getAllPreferences:v,getDashboardLayoutClasses:f,getFontSizeClass:h,getTableDensityClasses:p,mapViewPreference:c,notificationsEnabled:a,resetPreferences:j,setAutoRefreshInterval:x,setDashboardLayout:m,setFontSize:s,setMapViewPreference:d,setTableDensity:n,tableDensity:l,toggleNotifications:i}}},66866:(e,s,a)=>{a.d(s,{LG:()=>p});var t=a(95155),r=a(36936),i=a(68027),l=a(95120),n=a(82733),c=a(10518),d=a(51362);a(12115);var o=a(26126),m=a(30285),u=a(66695),x=a(85187);let h=[{description:"Clean and bright interface",icon:r.A,label:"Light",preview:"bg-white border-gray-200 text-gray-900",value:"light"},{description:"Easy on the eyes in low light",icon:i.A,label:"Dark",preview:"bg-gray-900 border-gray-700 text-white",value:"dark"},{description:"Follows your device settings",icon:l.A,label:"System",preview:"bg-gradient-to-r from-white to-gray-900 border-gray-400 text-gray-600",value:"system"}],p=()=>{let{currentTheme:e}=(0,x.useTheme)(),{setTheme:s,systemTheme:a,theme:r}=(0,d.D)(),{setTheme:i}=(0,x.useTheme)(),l=e=>{s(e),"system"===e?i(a||"light"):i(e)},p=r||"system";return(0,t.jsxs)(u.Zp,{className:"w-full max-w-2xl",children:[(0,t.jsxs)(u.aR,{children:[(0,t.jsxs)(u.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(n.A,{className:"size-5"}),"Theme Preferences"]}),(0,t.jsx)(u.BT,{children:"Choose your preferred color scheme and appearance settings"})]}),(0,t.jsxs)(u.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between rounded-lg bg-muted/50 p-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:"Current Theme"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Applied across the entire application"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o.E,{className:"capitalize",variant:"secondary",children:p}),"system"===p&&a&&(0,t.jsxs)(o.E,{className:"text-xs",variant:"outline",children:["System: ",a]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h4",{className:"text-sm font-medium uppercase tracking-wide text-muted-foreground",children:"Available Themes"}),h.map(e=>{let s=e.icon,a=p===e.value;return(0,t.jsx)("div",{className:"\n                  relative cursor-pointer rounded-lg border p-4 transition-all\n                  ".concat(a?"border-primary bg-primary/5 ring-1 ring-primary/20":"border-border hover:border-primary/50 hover:bg-muted/30","\n                "),onClick:()=>l(e.value),children:(0,t.jsx)("div",{className:"flex items-start justify-between",children:(0,t.jsxs)("div",{className:"flex flex-1 items-start gap-3",children:[(0,t.jsx)("div",{className:"flex size-10 items-center justify-center rounded-lg border bg-background",children:(0,t.jsx)(s,{className:"size-5"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"mb-1 flex items-center gap-2",children:[(0,t.jsx)("h5",{className:"font-medium",children:e.label}),a&&(0,t.jsx)(c.A,{className:"size-4 text-primary"})]}),(0,t.jsx)("p",{className:"mb-3 text-sm text-muted-foreground",children:e.description}),(0,t.jsx)("div",{className:"\n                        h-8 w-full rounded border-2 ".concat(e.preview,"\n                        flex items-center justify-center text-xs font-medium\n                      "),children:"Preview"})]})]})})},e.value)})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between border-t pt-4",children:[(0,t.jsx)("div",{className:"flex gap-2",children:h.map(e=>{let s=e.icon;return(0,t.jsxs)(m.$,{className:"flex items-center gap-2",onClick:()=>l(e.value),size:"sm",variant:p===e.value?"default":"outline",children:[(0,t.jsx)(s,{className:"size-4"}),e.label]},e.value)})}),(0,t.jsx)(m.$,{className:"text-muted-foreground",onClick:()=>l("system"),size:"sm",variant:"ghost",children:"Reset to System"})]}),(0,t.jsxs)("div",{className:"rounded-lg border bg-background p-4",children:[(0,t.jsx)("h5",{className:"mb-2 font-medium",children:"Theme Information"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Selected Theme:"}),(0,t.jsx)("span",{className:"font-medium capitalize",children:p})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Zustand Store:"}),(0,t.jsx)("span",{className:"font-medium capitalize",children:e})]}),a&&(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"System Preference:"}),(0,t.jsx)("span",{className:"font-medium capitalize",children:a})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Auto-sync:"}),(0,t.jsx)("span",{className:"font-medium",children:"system"===p?"Enabled":"Disabled"})]})]})]})]})]})}},85187:(e,s,a)=>{a.r(s),a.d(s,{useTheme:()=>i});var t=a(12115),r=a(96016);let i=()=>{let e=(0,r.C)(e=>e.currentTheme),s=(0,r.C)(e=>e.setTheme),a=(0,t.useCallback)(()=>{s("light"===e?"dark":"light")},[e,s]),i="dark"===e,l="light"===e,n=(0,t.useCallback)(()=>{s("light")},[s]),c=(0,t.useCallback)(()=>{s("dark")},[s]),d=(0,t.useCallback)(()=>({background:i?"bg-gray-900":"bg-white",border:i?"border-gray-700":"border-gray-200",isDark:i,isLight:l,root:e,text:i?"text-white":"text-gray-900"}),[e,i,l]);return{currentTheme:e,getThemeClasses:d,isDark:i,isLight:l,setDarkTheme:c,setLightTheme:n,setTheme:s,toggleTheme:a}}},96016:(e,s,a)=>{a.d(s,{C:()=>i});var t=a(65453),r=a(46786);let i=(0,t.v)()((0,r.lt)((0,r.Zr)((e,s)=>({addNotification:s=>e(e=>({notifications:[...e.notifications,{...s,id:crypto.randomUUID(),read:!1,timestamp:new Date().toISOString()}]})),clearAllNotifications:()=>e({notifications:[]}),currentTheme:"light",markNotificationAsRead:s=>e(e=>({notifications:e.notifications.map(e=>e.id===s?{...e,read:!0}:e)})),notifications:[],removeNotification:s=>e(e=>({notifications:e.notifications.filter(e=>e.id!==s)})),setTheme:s=>{e({currentTheme:s})},sidebarOpen:!1,toggleSidebar:()=>e(e=>({sidebarOpen:!e.sidebarOpen})),unreadNotificationCount:()=>{let{notifications:e}=s();return e.filter(e=>!e.read).length}}),{name:"workhub-app-store",partialize:e=>({currentTheme:e.currentTheme})}),{name:"app-store"}))}}]);