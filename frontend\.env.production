# Production Environment Configuration
NODE_ENV=production

# API Configuration - Production
# These should be set to your production backend URLs
NEXT_PUBLIC_API_URL=https://your-production-api.com
NEXT_PUBLIC_API_BASE_URL=https://your-production-api.com/api
NEXT_PUBLIC_WS_URL=wss://your-production-api.com

# Supabase Configuration - Production
NEXT_PUBLIC_SUPABASE_URL=https://your-production-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_anon_key

# Security Configuration
NEXT_PUBLIC_ALLOWED_CSP_CONNECT_SRC="https://your-production-api.com"

# Feature Flags - Production
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_DEV_TOOLS_ENABLED=false
NEXT_PUBLIC_ENABLE_DEBUG_LOGGING=false

# Docker Configuration
NEXT_PUBLIC_DOCKER_ENV=false
