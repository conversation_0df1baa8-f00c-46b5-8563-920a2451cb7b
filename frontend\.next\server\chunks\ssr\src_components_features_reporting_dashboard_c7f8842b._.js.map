{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/config/reportingDashboardConfig.tsx"], "sourcesContent": ["/**\r\n * @file Reporting Dashboard Configuration\r\n * @description Defines the layout, tabs, and widgets for the reporting dashboard.\r\n */\r\n\r\nimport React from 'react';\r\nimport type { DashboardConfig, ViewMode } from '@/components/dashboard/types';\r\nimport {\r\n  DelegationStatusWidget,\r\n  DelegationTrendWidget,\r\n  LocationDistributionWidget,\r\n  TaskMetricsWidget,\r\n  ReportingTableWidget,\r\n} from '../widgets';\r\nimport { BarChart3, TrendingUp, Table } from 'lucide-react';\r\n\r\n// Defines the structure for a widget in the dashboard grid\r\ninterface WidgetConfig {\r\n  id: string;\r\n  component: React.ComponentType<any>;\r\n  span: string; // Tailwind CSS class for column span\r\n}\r\n\r\n// Defines the structure for a tab in the dashboard (extended from base TabConfig)\r\ninterface ReportingTabConfig {\r\n  id: string;\r\n  label: string;\r\n  icon: React.ReactNode;\r\n  description: string;\r\n  widgets: WidgetConfig[];\r\n}\r\n\r\n// Custom dashboard config for reporting\r\ninterface ReportingDashboardConfig extends Omit<DashboardConfig, 'tabs'> {\r\n  tabs: ReportingTabConfig[];\r\n}\r\n\r\nexport const reportingDashboardConfig: ReportingDashboardConfig = {\r\n  entityType: 'reporting',\r\n  title: 'Reporting Dashboard',\r\n  description: 'Comprehensive reporting and analytics dashboard',\r\n  viewModes: ['grid', 'list'] as ViewMode[],\r\n  defaultViewMode: 'grid' as ViewMode,\r\n  tabs: [\r\n    {\r\n      id: 'overview',\r\n      label: 'Overview',\r\n      icon: <BarChart3 className=\"h-4 w-4\" />,\r\n      description: 'High-level analytics and status distribution.',\r\n      widgets: [\r\n        {\r\n          id: 'delegation-status',\r\n          component: DelegationStatusWidget,\r\n          span: 'lg:col-span-1',\r\n        },\r\n        {\r\n          id: 'delegation-trend',\r\n          component: DelegationTrendWidget,\r\n          span: 'lg:col-span-2',\r\n        },\r\n        {\r\n          id: 'location-distribution',\r\n          component: LocationDistributionWidget,\r\n          span: 'lg:col-span-2',\r\n        },\r\n        {\r\n          id: 'task-metrics',\r\n          component: TaskMetricsWidget,\r\n          span: 'lg:col-span-1',\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      id: 'details',\r\n      label: 'Detailed Report',\r\n      icon: <Table className=\"h-4 w-4\" />,\r\n      description: 'In-depth data table with filtering and sorting.',\r\n      widgets: [\r\n        {\r\n          id: 'reporting-table',\r\n          component: ReportingTableWidget,\r\n          span: 'lg:col-span-3 xl:col-span-4',\r\n        },\r\n      ],\r\n    },\r\n  ],\r\n};\r\n\r\n// Widget configurations for backward compatibility\r\nexport const widgetConfigurations = reportingDashboardConfig.tabs.flatMap(\r\n  tab => tab.widgets\r\n);\r\n\r\n// Filter presets for common reporting scenarios\r\nexport const filterPresets = {\r\n  lastWeek: {\r\n    dateRange: {\r\n      from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),\r\n      to: new Date(),\r\n    },\r\n    status: [],\r\n    locations: [],\r\n    employees: [],\r\n    vehicles: [],\r\n  },\r\n  lastMonth: {\r\n    dateRange: {\r\n      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),\r\n      to: new Date(),\r\n    },\r\n    status: [],\r\n    locations: [],\r\n    employees: [],\r\n    vehicles: [],\r\n  },\r\n  currentYear: {\r\n    dateRange: {\r\n      from: new Date(new Date().getFullYear(), 0, 1),\r\n      to: new Date(),\r\n    },\r\n    status: [],\r\n    locations: [],\r\n    employees: [],\r\n    vehicles: [],\r\n  },\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAID;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA;AAAA;;;;AAuBO,MAAM,2BAAqD;IAChE,YAAY;IACZ,OAAO;IACP,aAAa;IACb,WAAW;QAAC;QAAQ;KAAO;IAC3B,iBAAiB;IACjB,MAAM;QACJ;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,8OAAC,kNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,aAAa;YACb,SAAS;gBACP;oBACE,IAAI;oBACJ,WAAW,6LAAA,CAAA,yBAAsB;oBACjC,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,WAAW,4LAAA,CAAA,wBAAqB;oBAChC,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,WAAW,iMAAA,CAAA,6BAA0B;oBACrC,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,WAAW,wLAAA,CAAA,oBAAiB;oBAC5B,MAAM;gBACR;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,aAAa;YACb,SAAS;gBACP;oBACE,IAAI;oBACJ,WAAW,2LAAA,CAAA,uBAAoB;oBAC/B,MAAM;gBACR;aACD;QACH;KACD;AACH;AAGO,MAAM,uBAAuB,yBAAyB,IAAI,CAAC,OAAO,CACvE,CAAA,MAAO,IAAI,OAAO;AAIb,MAAM,gBAAgB;IAC3B,UAAU;QACR,WAAW;YACT,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;YAC/C,IAAI,IAAI;QACV;QACA,QAAQ,EAAE;QACV,WAAW,EAAE;QACb,WAAW,EAAE;QACb,UAAU,EAAE;IACd;IACA,WAAW;QACT,WAAW;YACT,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;YAChD,IAAI,IAAI;QACV;QACA,QAAQ,EAAE;QACV,WAAW,EAAE;QACb,WAAW,EAAE;QACb,UAAU,EAAE;IACd;IACA,aAAa;QACX,WAAW;YACT,MAAM,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,GAAG;YAC5C,IAAI,IAAI;QACV;QACA,QAAQ,EAAE;QACV,WAAW,EAAE;QACb,WAAW,EAAE;QACb,UAAU,EAAE;IACd;AACF", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/layout/ReportingLayout.tsx"], "sourcesContent": ["/**\r\n * @file Reporting Layout Component\r\n * @description Provides a consistent layout structure for all reporting pages.\r\n */\r\n\r\nimport React from 'react';\r\n\r\ninterface ReportingLayoutProps {\r\n  title: string;\r\n  description?: string;\r\n  children: React.ReactNode;\r\n  actions?: React.ReactNode;\r\n  filters?: React.ReactNode;\r\n}\r\n\r\nexport const ReportingLayout: React.FC<ReportingLayoutProps> = ({\r\n  title,\r\n  description,\r\n  children,\r\n  actions,\r\n  filters,\r\n}) => {\r\n  return (\r\n    <div className=\"flex flex-col h-full p-4 md:p-6 lg:p-8 gap-6\">\r\n      <header className=\"flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\r\n        <div className=\"flex-grow\">\r\n          <h1 className=\"text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100\">\r\n            {title}\r\n          </h1>\r\n          {description && (\r\n            <p className=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\r\n              {description}\r\n            </p>\r\n          )}\r\n        </div>\r\n        {actions && <div className=\"flex-shrink-0\">{actions}</div>}\r\n      </header>\r\n\r\n      {filters && <aside>{filters}</aside>}\r\n\r\n      <main className=\"flex-1\">{children}</main>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAYM,MAAM,kBAAkD,CAAC,EAC9D,KAAK,EACL,WAAW,EACX,QAAQ,EACR,OAAO,EACP,OAAO,EACR;IACC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX;;;;;;4BAEF,6BACC,8OAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;oBAIN,yBAAW,8OAAC;wBAAI,WAAU;kCAAiB;;;;;;;;;;;;YAG7C,yBAAW,8OAAC;0BAAO;;;;;;0BAEpB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/dashboard/ReportingDashboard.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/dashboard/ReportingDashboard.tsx\r\n\r\n'use client';\r\n\r\nimport {\r\n  BarChart3,\r\n  Car,\r\n  CheckSquare,\r\n  Download,\r\n  FileText,\r\n  Filter,\r\n  Network,\r\n  RefreshCw,\r\n  Settings,\r\n  Table,\r\n  TrendingUp,\r\n  Users,\r\n} from 'lucide-react';\r\nimport React from 'react';\r\n\r\nimport { DashboardPage } from '@/components/dashboard/DashboardLayout';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { useDashboardStore } from '@/hooks/domain/useDashboardStore';\r\n\r\nimport { useRealtimeReportingUpdates } from '../data/hooks/useRealtimeReportingUpdates';\r\nimport {\r\n  useReportingFilters,\r\n  useReportingFiltersUI,\r\n} from '../data/stores/useReportingFiltersStore';\r\nimport { TaskPriorityFilter, TaskStatusFilter } from '../filters';\r\nimport { ReportGenerationPage } from '../generation/ReportGenerationPage';\r\nimport { ReportBuilder } from '../management/ReportBuilder';\r\nimport { ReportTypeManager } from '../management/ReportTypeManager';\r\n// Note: Using new export system from exports/hooks/useExport\r\nimport { TaskReportingTable } from '../tables';\r\nimport { reportingDashboardConfig } from './config/reportingDashboardConfig';\r\nimport { ReportingFilters } from './filters/ReportingFilters';\r\nimport { DashboardGrid } from './layout/DashboardGrid';\r\nimport { ReportingLayout } from './layout/ReportingLayout';\r\nimport {\r\n  CrossEntityCorrelationWidget,\r\n  DelegationStatusWidget,\r\n  DelegationTrendWidget,\r\n  EmployeeAnalyticsWidget,\r\n  EmployeePerformanceChart,\r\n  EmployeeWorkloadWidget,\r\n  EntityRelationshipNetworkWidget,\r\n  LocationDistributionWidget,\r\n  ReportingTableWidget,\r\n  TaskAssignmentMetrics,\r\n  TaskMetricsWidget,\r\n  TaskPriorityDistribution,\r\n  TaskStatusChart,\r\n  VehicleAnalyticsWidget,\r\n  VehicleCostAnalyticsWidget,\r\n  VehicleMaintenanceWidget,\r\n  VehicleUtilizationChart,\r\n} from './widgets';\r\n// TEMPORARY: Import test function for React-PDF v4 compatibility testing\r\n\r\ninterface ReportingDashboardProps {\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * @component ReportingDashboard\r\n * @description Professional reporting dashboard with UX-optimized layout\r\n *\r\n * UX Improvements Applied:\r\n * - Simplified hierarchy: Single dashboard focus, removed redundant cards\r\n * - Consolidated actions: Single action bar for all dashboard operations\r\n * - Professional 12-column grid system with proper widget sizing\r\n * - Improved spacing and alignment throughout\r\n * - Clean, consistent visual hierarchy\r\n *\r\n * Responsibilities:\r\n * - Orchestrates all reporting dashboard components\r\n * - Manages clean, professional layout with strict grid alignment\r\n * - Integrates with existing dashboard framework\r\n * - Handles real-time updates and data synchronization\r\n */\r\nexport const ReportingDashboard: React.FC<ReportingDashboardProps> = ({\r\n  className = '',\r\n}) => {\r\n  const [activeTab, setActiveTab] = React.useState('overview');\r\n  const [isFilterPanelOpen, setIsFilterPanelOpen] = React.useState(false);\r\n\r\n  // Simplified filters to avoid store issues\r\n  const filters = {\r\n    costRange: { max: 10_000, min: 0 },\r\n    dateRange: {\r\n      from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),\r\n      to: new Date(),\r\n    },\r\n    employees: [],\r\n    includeServiceHistory: false,\r\n    includeTaskData: false,\r\n    locations: [],\r\n    serviceStatus: [],\r\n    serviceTypes: [],\r\n    status: [],\r\n    vehicles: [],\r\n  };\r\n\r\n  const toggleFilterPanel = () => setIsFilterPanelOpen(!isFilterPanelOpen);\r\n\r\n  // TODO: Re-enable real-time updates after fixing store\r\n  // useRealtimeReportingUpdates(filters);\r\n\r\n  // Professional dashboard tabs configuration\r\n  const dashboardTabs = [\r\n    {\r\n      description: 'High-level metrics and key performance indicators',\r\n      icon: <BarChart3 className=\"size-4\" />,\r\n      id: 'overview',\r\n      label: 'Overview',\r\n    },\r\n    {\r\n      description: 'Detailed analytics and trend analysis',\r\n      icon: <TrendingUp className=\"size-4\" />,\r\n      id: 'analytics',\r\n      label: 'Analytics',\r\n    },\r\n    {\r\n      description: 'Task metrics and performance analysis',\r\n      icon: <CheckSquare className=\"size-4\" />,\r\n      id: 'tasks',\r\n      label: 'Tasks',\r\n    },\r\n    {\r\n      description: 'Vehicle utilization and maintenance analytics',\r\n      icon: <Car className=\"size-4\" />,\r\n      id: 'vehicles',\r\n      label: 'Vehicles',\r\n    },\r\n    {\r\n      description: 'Employee performance and workload analysis',\r\n      icon: <Users className=\"size-4\" />,\r\n      id: 'employees',\r\n      label: 'Employees',\r\n    },\r\n    {\r\n      description: 'Cross-entity relationships and correlations',\r\n      icon: <Network className=\"size-4\" />,\r\n      id: 'correlations',\r\n      label: 'Correlations',\r\n    },\r\n    {\r\n      description: 'Generate comprehensive data reports for all entities',\r\n      icon: <FileText className=\"size-4\" />,\r\n      id: 'generation',\r\n      label: 'Generate Reports',\r\n    },\r\n    {\r\n      description: 'Manage report types and build custom reports',\r\n      icon: <Settings className=\"size-4\" />,\r\n      id: 'management',\r\n      label: 'Management',\r\n    },\r\n    {\r\n      description: 'Raw delegation data in tabular format',\r\n      icon: <Table className=\"size-4\" />,\r\n      id: 'data',\r\n      label: 'Data',\r\n    },\r\n  ];\r\n\r\n  // Professional 12-column grid widget configurations\r\n  const getWidgetsForTab = (tabId: string) => {\r\n    switch (tabId) {\r\n      case 'overview': {\r\n        return [\r\n          {\r\n            component: DelegationStatusWidget,\r\n            id: 'status',\r\n            span: 'col-span-12 lg:col-span-4', // 1/3 width on large screens\r\n          },\r\n          {\r\n            component: TaskMetricsWidget,\r\n            id: 'tasks',\r\n            span: 'col-span-12 lg:col-span-8', // 2/3 width on large screens\r\n          },\r\n          {\r\n            component: DelegationTrendWidget,\r\n            id: 'trend',\r\n            span: 'col-span-12 lg:col-span-8', // Full width trend chart\r\n          },\r\n          {\r\n            component: LocationDistributionWidget,\r\n            id: 'location',\r\n            span: 'col-span-12 lg:col-span-4', // 1/3 width location chart\r\n          },\r\n        ];\r\n      }\r\n      case 'analytics': {\r\n        return [\r\n          {\r\n            component: DelegationTrendWidget,\r\n            id: 'trend',\r\n            span: 'col-span-12 lg:col-span-8', // 2/3 width for main trend\r\n          },\r\n          {\r\n            component: LocationDistributionWidget,\r\n            id: 'location',\r\n            span: 'col-span-12 lg:col-span-4', // 1/3 width for location\r\n          },\r\n          {\r\n            component: TaskMetricsWidget,\r\n            id: 'tasks',\r\n            span: 'col-span-12 lg:col-span-6', // Half width task metrics\r\n          },\r\n          {\r\n            component: DelegationStatusWidget,\r\n            id: 'status',\r\n            span: 'col-span-12 lg:col-span-6', // Half width status\r\n          },\r\n        ];\r\n      }\r\n      case 'correlations': {\r\n        return [\r\n          {\r\n            component: CrossEntityCorrelationWidget,\r\n            id: 'cross-entity-correlations',\r\n            span: 'col-span-12', // Full width for complex correlations\r\n          },\r\n          {\r\n            component: EntityRelationshipNetworkWidget,\r\n            id: 'entity-relationships',\r\n            span: 'col-span-12 lg:col-span-6',\r\n          },\r\n          {\r\n            component: TaskMetricsWidget,\r\n            id: 'task-correlations',\r\n            span: 'col-span-12 lg:col-span-6',\r\n          },\r\n        ];\r\n      }\r\n      case 'tasks': {\r\n        return [\r\n          {\r\n            component: TaskMetricsWidget,\r\n            id: 'task-metrics',\r\n            span: 'col-span-12', // Full width for detailed task view\r\n          },\r\n          {\r\n            component: TaskAssignmentMetrics,\r\n            id: 'task-assignments',\r\n            span: 'col-span-12 lg:col-span-6',\r\n          },\r\n          {\r\n            component: TaskStatusChart,\r\n            id: 'task-status-chart',\r\n            span: 'col-span-12 lg:col-span-6',\r\n          },\r\n        ];\r\n      }\r\n      case 'vehicles': {\r\n        return [\r\n          {\r\n            component: VehicleAnalyticsWidget,\r\n            id: 'vehicle-analytics',\r\n            span: 'col-span-12 lg:col-span-6',\r\n          },\r\n          {\r\n            component: VehicleUtilizationChart,\r\n            id: 'vehicle-utilization',\r\n            span: 'col-span-12 lg:col-span-6',\r\n          },\r\n          {\r\n            component: VehicleMaintenanceWidget,\r\n            id: 'vehicle-maintenance',\r\n            span: 'col-span-12 lg:col-span-6',\r\n          },\r\n          {\r\n            component: VehicleCostAnalyticsWidget,\r\n            id: 'vehicle-costs',\r\n            span: 'col-span-12 lg:col-span-6',\r\n          },\r\n        ];\r\n      }\r\n      case 'employees': {\r\n        return [\r\n          {\r\n            component: EmployeeAnalyticsWidget,\r\n            id: 'employee-analytics',\r\n            span: 'col-span-12 lg:col-span-8',\r\n          },\r\n          {\r\n            component: EmployeeWorkloadWidget,\r\n            id: 'employee-workload',\r\n            span: 'col-span-12 lg:col-span-4',\r\n          },\r\n          {\r\n            component: EmployeePerformanceChart,\r\n            id: 'employee-performance',\r\n            span: 'col-span-12',\r\n          },\r\n        ];\r\n      }\r\n      case 'generation': {\r\n        return [\r\n          {\r\n            component: () => <ReportGenerationPage />,\r\n            id: 'report-generation',\r\n            span: 'col-span-12',\r\n          },\r\n        ];\r\n      }\r\n      case 'management': {\r\n        return [\r\n          {\r\n            component: () => <ReportTypeManager />,\r\n            id: 'report-type-manager',\r\n            span: 'col-span-12 lg:col-span-6',\r\n          },\r\n          {\r\n            component: () => <ReportBuilder />,\r\n            id: 'report-builder',\r\n            span: 'col-span-12 lg:col-span-6',\r\n          },\r\n        ];\r\n      }\r\n      case 'data': {\r\n        return [\r\n          {\r\n            component: () => <TaskReportingTable />,\r\n            id: 'data-table',\r\n            span: 'col-span-12',\r\n          },\r\n        ];\r\n      }\r\n      default: {\r\n        return [];\r\n      }\r\n    }\r\n  };\r\n\r\n  // Consolidated export functionality\r\n  const handleExportDashboard = async () => {\r\n    try {\r\n      const exportData = {\r\n        activeTab: activeTab,\r\n        filters: filters,\r\n        metadata: {\r\n          exportedBy: 'Reporting Dashboard',\r\n          version: '2.0.0',\r\n        },\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n\r\n      const blob = new Blob([JSON.stringify(exportData, null, 2)], {\r\n        type: 'application/json',\r\n      });\r\n      const url = globalThis.URL.createObjectURL(blob);\r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `dashboard-export-${new Date().toISOString().split('T')[0]}.json`;\r\n      document.body.append(a);\r\n      a.click();\r\n      a.remove();\r\n      globalThis.URL.revokeObjectURL(url);\r\n    } catch (error) {\r\n      console.error('Export failed:', error);\r\n    }\r\n  };\r\n\r\n  const handleRefreshDashboard = () => {\r\n    globalThis.location.reload();\r\n  };\r\n\r\n  // Consolidated action bar - single, clean interface\r\n  const renderDashboardActions = () => (\r\n    <div className=\"flex items-center gap-3\">\r\n      <Button\r\n        className={\r\n          isFilterPanelOpen ? 'bg-primary text-primary-foreground' : ''\r\n        }\r\n        onClick={toggleFilterPanel}\r\n        size=\"sm\"\r\n        variant=\"outline\"\r\n      >\r\n        <Filter className=\"mr-2 size-4\" />\r\n        Filters\r\n      </Button>\r\n      <Button onClick={handleRefreshDashboard} size=\"sm\" variant=\"outline\">\r\n        <RefreshCw className=\"mr-2 size-4\" />\r\n        Refresh\r\n      </Button>\r\n      <Button onClick={handleExportDashboard} size=\"sm\" variant=\"outline\">\r\n        <Download className=\"mr-2 size-4\" />\r\n        Export\r\n      </Button>\r\n    </div>\r\n  );\r\n\r\n  // Clean filter summary\r\n  const renderFilterSummary = () => {\r\n    const activeFilters = [];\r\n    if (filters.status.length > 0)\r\n      activeFilters.push(`${filters.status.length} status`);\r\n    if (filters.locations.length > 0)\r\n      activeFilters.push(`${filters.locations.length} locations`);\r\n    if (filters.employees.length > 0)\r\n      activeFilters.push(`${filters.employees.length} employees`);\r\n    if (filters.vehicles.length > 0)\r\n      activeFilters.push(`${filters.vehicles.length} vehicles`);\r\n\r\n    if (activeFilters.length === 0) return null;\r\n\r\n    return (\r\n      <div className=\"flex items-center gap-3 text-sm text-muted-foreground\">\r\n        <span>Active filters:</span>\r\n        {activeFilters.map((filter, index) => (\r\n          <Badge\r\n            className=\"text-xs\"\r\n            key={`filter-${index}-${filter}`}\r\n            variant=\"secondary\"\r\n          >\r\n            {filter}\r\n          </Badge>\r\n        ))}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Professional widget rendering with 12-column grid\r\n  const renderWidgets = () => {\r\n    const widgets = getWidgetsForTab(activeTab);\r\n\r\n    return (\r\n      <div className=\"grid grid-cols-12 gap-6\">\r\n        {widgets.map(({ component: WidgetComponent, id, span }) => (\r\n          <div className={span} key={`${activeTab}-${id}`}>\r\n            <WidgetComponent />\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <DashboardPage className={className} config={reportingDashboardConfig}>\r\n      <ReportingLayout\r\n        actions={renderDashboardActions()}\r\n        description=\"Interactive dashboard with real-time metrics and insights\"\r\n        filters={isFilterPanelOpen ? <ReportingFilters /> : undefined}\r\n        title=\"Reporting Dashboard\"\r\n      >\r\n        <div className=\"space-y-8\">\r\n          {/* Clean filter summary */}\r\n          {renderFilterSummary()}\r\n\r\n          {/* Professional dashboard tabs */}\r\n          <Tabs\r\n            className=\"w-full\"\r\n            onValueChange={setActiveTab}\r\n            value={activeTab}\r\n          >\r\n            <TabsList className=\"grid w-full grid-cols-9 h-12\">\r\n              {dashboardTabs.map(tab => (\r\n                <TabsTrigger\r\n                  className=\"flex items-center gap-2 text-sm font-medium\"\r\n                  key={tab.id}\r\n                  value={tab.id}\r\n                >\r\n                  {tab.icon}\r\n                  <span className=\"hidden sm:inline\">{tab.label}</span>\r\n                </TabsTrigger>\r\n              ))}\r\n            </TabsList>\r\n\r\n            {dashboardTabs.map(tab => (\r\n              <TabsContent className=\"space-y-8\" key={tab.id} value={tab.id}>\r\n                <div className=\"space-y-2\">\r\n                  <h2 className=\"text-2xl font-semibold tracking-tight flex items-center gap-3\">\r\n                    {tab.icon}\r\n                    {tab.label}\r\n                  </h2>\r\n                  <p className=\"text-muted-foreground\">{tab.description}</p>\r\n                </div>\r\n\r\n                {/* Professional 12-column grid layout */}\r\n                {renderWidgets()}\r\n              </TabsContent>\r\n            ))}\r\n          </Tabs>\r\n        </div>\r\n      </ReportingLayout>\r\n    </DashboardPage>\r\n  );\r\n};\r\n\r\nexport default ReportingDashboard;\r\n"], "names": [], "mappings": "AAAA,8EAA8E;;;;;;AAI9E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAEA;AACA;AACA;AAEA;AASA;AACA;AACA;AACA,6DAA6D;AAC7D;AAAA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAxCA;;;;;;;;;;;;;;;;AAkFO,MAAM,qBAAwD,CAAC,EACpE,YAAY,EAAE,EACf;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEjE,2CAA2C;IAC3C,MAAM,UAAU;QACd,WAAW;YAAE,KAAK;YAAQ,KAAK;QAAE;QACjC,WAAW;YACT,MAAM,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;YAChD,IAAI,IAAI;QACV;QACA,WAAW,EAAE;QACb,uBAAuB;QACvB,iBAAiB;QACjB,WAAW,EAAE;QACb,eAAe,EAAE;QACjB,cAAc,EAAE;QAChB,QAAQ,EAAE;QACV,UAAU,EAAE;IACd;IAEA,MAAM,oBAAoB,IAAM,qBAAqB,CAAC;IAEtD,uDAAuD;IACvD,wCAAwC;IAExC,4CAA4C;IAC5C,MAAM,gBAAgB;QACpB;YACE,aAAa;YACb,oBAAM,8OAAC,kNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,IAAI;YACJ,OAAO;QACT;QACA;YACE,aAAa;YACb,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,IAAI;YACJ,OAAO;QACT;QACA;YACE,aAAa;YACb,oBAAM,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,IAAI;YACJ,OAAO;QACT;QACA;YACE,aAAa;YACb,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,IAAI;YACJ,OAAO;QACT;QACA;YACE,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,IAAI;YACJ,OAAO;QACT;QACA;YACE,aAAa;YACb,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,IAAI;YACJ,OAAO;QACT;QACA;YACE,aAAa;YACb,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,IAAI;YACJ,OAAO;QACT;QACA;YACE,aAAa;YACb,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,IAAI;YACJ,OAAO;QACT;QACA;YACE,aAAa;YACb,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,IAAI;YACJ,OAAO;QACT;KACD;IAED,oDAAoD;IACpD,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAY;oBACf,OAAO;wBACL;4BACE,WAAW,6LAAA,CAAA,yBAAsB;4BACjC,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,wLAAA,CAAA,oBAAiB;4BAC5B,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,4LAAA,CAAA,wBAAqB;4BAChC,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,iMAAA,CAAA,6BAA0B;4BACrC,IAAI;4BACJ,MAAM;wBACR;qBACD;gBACH;YACA,KAAK;gBAAa;oBAChB,OAAO;wBACL;4BACE,WAAW,4LAAA,CAAA,wBAAqB;4BAChC,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,iMAAA,CAAA,6BAA0B;4BACrC,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,wLAAA,CAAA,oBAAiB;4BAC5B,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,6LAAA,CAAA,yBAAsB;4BACjC,IAAI;4BACJ,MAAM;wBACR;qBACD;gBACH;YACA,KAAK;gBAAgB;oBACnB,OAAO;wBACL;4BACE,WAAW,mMAAA,CAAA,+BAA4B;4BACvC,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,sMAAA,CAAA,kCAA+B;4BAC1C,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,wLAAA,CAAA,oBAAiB;4BAC5B,IAAI;4BACJ,MAAM;wBACR;qBACD;gBACH;YACA,KAAK;gBAAS;oBACZ,OAAO;wBACL;4BACE,WAAW,wLAAA,CAAA,oBAAiB;4BAC5B,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,4LAAA,CAAA,wBAAqB;4BAChC,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,sLAAA,CAAA,kBAAe;4BAC1B,IAAI;4BACJ,MAAM;wBACR;qBACD;gBACH;YACA,KAAK;gBAAY;oBACf,OAAO;wBACL;4BACE,WAAW,6LAAA,CAAA,yBAAsB;4BACjC,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,8LAAA,CAAA,0BAAuB;4BAClC,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,+LAAA,CAAA,2BAAwB;4BACnC,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,iMAAA,CAAA,6BAA0B;4BACrC,IAAI;4BACJ,MAAM;wBACR;qBACD;gBACH;YACA,KAAK;gBAAa;oBAChB,OAAO;wBACL;4BACE,WAAW,8LAAA,CAAA,0BAAuB;4BAClC,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,6LAAA,CAAA,yBAAsB;4BACjC,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,+LAAA,CAAA,2BAAwB;4BACnC,IAAI;4BACJ,MAAM;wBACR;qBACD;gBACH;YACA,KAAK;gBAAc;oBACjB,OAAO;wBACL;4BACE,WAAW,kBAAM,8OAAC,iLAAA,CAAA,uBAAoB;;;;;4BACtC,IAAI;4BACJ,MAAM;wBACR;qBACD;gBACH;YACA,KAAK;gBAAc;oBACjB,OAAO;wBACL;4BACE,WAAW,kBAAM,8OAAC,8KAAA,CAAA,oBAAiB;;;;;4BACnC,IAAI;4BACJ,MAAM;wBACR;wBACA;4BACE,WAAW,kBAAM,8OAAC,0KAAA,CAAA,gBAAa;;;;;4BAC/B,IAAI;4BACJ,MAAM;wBACR;qBACD;gBACH;YACA,KAAK;gBAAQ;oBACX,OAAO;wBACL;4BACE,WAAW,kBAAM,8OAAC,2KAAA,CAAA,qBAAkB;;;;;4BACpC,IAAI;4BACJ,MAAM;wBACR;qBACD;gBACH;YACA;gBAAS;oBACP,OAAO,EAAE;gBACX;QACF;IACF;IAEA,oCAAoC;IACpC,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,aAAa;gBACjB,WAAW;gBACX,SAAS;gBACT,UAAU;oBACR,YAAY;oBACZ,SAAS;gBACX;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,MAAM,OAAO,IAAI,KAAK;gBAAC,KAAK,SAAS,CAAC,YAAY,MAAM;aAAG,EAAE;gBAC3D,MAAM;YACR;YACA,MAAM,MAAM,WAAW,GAAG,CAAC,eAAe,CAAC;YAC3C,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,iBAAiB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAC9E,SAAS,IAAI,CAAC,MAAM,CAAC;YACrB,EAAE,KAAK;YACP,EAAE,MAAM;YACR,WAAW,GAAG,CAAC,eAAe,CAAC;QACjC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,MAAM,yBAAyB;QAC7B,WAAW,QAAQ,CAAC,MAAM;IAC5B;IAEA,oDAAoD;IACpD,MAAM,yBAAyB,kBAC7B,8OAAC;YAAI,WAAU;;8BACb,8OAAC,kIAAA,CAAA,SAAM;oBACL,WACE,oBAAoB,uCAAuC;oBAE7D,SAAS;oBACT,MAAK;oBACL,SAAQ;;sCAER,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAgB;;;;;;;8BAGpC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAwB,MAAK;oBAAK,SAAQ;;sCACzD,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAgB;;;;;;;8BAGvC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAS;oBAAuB,MAAK;oBAAK,SAAQ;;sCACxD,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAgB;;;;;;;;;;;;;IAM1C,uBAAuB;IACvB,MAAM,sBAAsB;QAC1B,MAAM,gBAAgB,EAAE;QACxB,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,GAC1B,cAAc,IAAI,CAAC,GAAG,QAAQ,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;QACtD,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAC7B,cAAc,IAAI,CAAC,GAAG,QAAQ,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC;QAC5D,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAC7B,cAAc,IAAI,CAAC,GAAG,QAAQ,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC;QAC5D,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAC5B,cAAc,IAAI,CAAC,GAAG,QAAQ,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC;QAE1D,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;QAEvC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;8BAAK;;;;;;gBACL,cAAc,GAAG,CAAC,CAAC,QAAQ,sBAC1B,8OAAC,iIAAA,CAAA,QAAK;wBACJ,WAAU;wBAEV,SAAQ;kCAEP;uBAHI,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,QAAQ;;;;;;;;;;;IAQ1C;IAEA,oDAAoD;IACpD,MAAM,gBAAgB;QACpB,MAAM,UAAU,iBAAiB;QAEjC,qBACE,8OAAC;YAAI,WAAU;sBACZ,QAAQ,GAAG,CAAC,CAAC,EAAE,WAAW,eAAe,EAAE,EAAE,EAAE,IAAI,EAAE,iBACpD,8OAAC;oBAAI,WAAW;8BACd,cAAA,8OAAC;;;;;mBADwB,GAAG,UAAU,CAAC,EAAE,IAAI;;;;;;;;;;IAMvD;IAEA,qBACE,8OAAC,kJAAA,CAAA,gBAAa;QAAC,WAAW;QAAW,QAAQ,8LAAA,CAAA,2BAAwB;kBACnE,cAAA,8OAAC,qLAAA,CAAA,kBAAe;YACd,SAAS;YACT,aAAY;YACZ,SAAS,kCAAoB,8OAAC,uLAAA,CAAA,mBAAgB;;;;yBAAM;YACpD,OAAM;sBAEN,cAAA,8OAAC;gBAAI,WAAU;;oBAEZ;kCAGD,8OAAC,gIAAA,CAAA,OAAI;wBACH,WAAU;wBACV,eAAe;wBACf,OAAO;;0CAEP,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;0CACjB,cAAc,GAAG,CAAC,CAAA,oBACjB,8OAAC,gIAAA,CAAA,cAAW;wCACV,WAAU;wCAEV,OAAO,IAAI,EAAE;;4CAEZ,IAAI,IAAI;0DACT,8OAAC;gDAAK,WAAU;0DAAoB,IAAI,KAAK;;;;;;;uCAJxC,IAAI,EAAE;;;;;;;;;;4BAShB,cAAc,GAAG,CAAC,CAAA,oBACjB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;oCAAyB,OAAO,IAAI,EAAE;;sDAC3D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;wDACX,IAAI,IAAI;wDACR,IAAI,KAAK;;;;;;;8DAEZ,8OAAC;oDAAE,WAAU;8DAAyB,IAAI,WAAW;;;;;;;;;;;;wCAItD;;mCAVqC,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB5D;uCAEe", "debugId": null}}]}