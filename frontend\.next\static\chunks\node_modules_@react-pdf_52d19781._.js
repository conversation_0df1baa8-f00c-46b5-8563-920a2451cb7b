(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@react-pdf/primitives/lib/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Canvas": (()=>Canvas),
    "Checkbox": (()=>Checkbox),
    "Circle": (()=>Circle),
    "ClipPath": (()=>ClipPath),
    "Defs": (()=>Defs),
    "Document": (()=>Document),
    "Ellipse": (()=>Ellipse),
    "FieldSet": (()=>FieldSet),
    "G": (()=>G),
    "Image": (()=>Image),
    "Line": (()=>Line),
    "LinearGradient": (()=>LinearGradient),
    "Link": (()=>Link),
    "List": (()=>List),
    "Note": (()=>Note),
    "Page": (()=>Page),
    "Path": (()=>Path),
    "Polygon": (()=>Polygon),
    "Polyline": (()=>Polyline),
    "RadialGradient": (()=>RadialGradient),
    "Rect": (()=>Rect),
    "Select": (()=>Select),
    "Stop": (()=>Stop),
    "Svg": (()=>Svg),
    "Text": (()=>Text),
    "TextInput": (()=>TextInput),
    "TextInstance": (()=>TextInstance),
    "Tspan": (()=>Tspan),
    "View": (()=>View)
});
const G = 'G';
const Svg = 'SVG';
const View = 'VIEW';
const Text = 'TEXT';
const Link = 'LINK';
const Page = 'PAGE';
const Note = 'NOTE';
const Path = 'PATH';
const Rect = 'RECT';
const Line = 'LINE';
const FieldSet = 'FIELD_SET';
const TextInput = 'TEXT_INPUT';
const Select = 'SELECT';
const Checkbox = 'CHECKBOX';
const List = 'LIST';
const Stop = 'STOP';
const Defs = 'DEFS';
const Image = 'IMAGE';
const Tspan = 'TSPAN';
const Canvas = 'CANVAS';
const Circle = 'CIRCLE';
const Ellipse = 'ELLIPSE';
const Polygon = 'POLYGON';
const Document = 'DOCUMENT';
const Polyline = 'POLYLINE';
const ClipPath = 'CLIP_PATH';
const TextInstance = 'TEXT_INSTANCE';
const LinearGradient = 'LINEAR_GRADIENT';
const RadialGradient = 'RADIAL_GRADIENT';
;
}}),
"[project]/node_modules/@react-pdf/font/lib/index.browser.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>FontStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$is$2d$url$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/is-url/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fontkit$2f$dist$2f$browser$2d$module$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fontkit/dist/browser-module.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$pdfkit$2f$lib$2f$pdfkit$2e$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/pdfkit/lib/pdfkit.browser.js [app-client] (ecmascript)");
;
;
;
// @ts-expect-error ts being silly
const STANDARD_FONTS = [
    'Courier',
    'Courier-Bold',
    'Courier-Oblique',
    'Courier-BoldOblique',
    'Helvetica',
    'Helvetica-Bold',
    'Helvetica-Oblique',
    'Helvetica-BoldOblique',
    'Times-Roman',
    'Times-Bold',
    'Times-Italic',
    'Times-BoldItalic'
];
class StandardFont {
    name;
    src;
    fullName;
    familyName;
    subfamilyName;
    postscriptName;
    copyright;
    version;
    underlinePosition;
    underlineThickness;
    italicAngle;
    bbox;
    'OS/2';
    hhea;
    numGlyphs;
    characterSet;
    availableFeatures;
    type;
    constructor(src){
        this.name = src;
        this.fullName = src;
        this.familyName = src;
        this.subfamilyName = src;
        this.type = 'STANDARD';
        this.postscriptName = src;
        this.availableFeatures = [];
        this.copyright = '';
        this.version = 1;
        this.underlinePosition = -100;
        this.underlineThickness = 50;
        this.italicAngle = 0;
        this.bbox = {};
        this['OS/2'] = {};
        this.hhea = {};
        this.numGlyphs = 0;
        this.characterSet = [];
        this.src = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$pdfkit$2f$lib$2f$pdfkit$2e$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PDFFont"].open(null, src);
    }
    encode(str) {
        return this.src.encode(str);
    }
    layout(str) {
        const [encoded, positions] = this.encode(str);
        const glyphs = encoded.map((g, i)=>{
            const glyph = this.getGlyph(parseInt(g, 16));
            glyph.advanceWidth = positions[i].advanceWidth;
            return glyph;
        });
        const advanceWidth = positions.reduce((acc, p)=>acc + p.advanceWidth, 0);
        return {
            positions,
            stringIndices: positions.map((_, i)=>i),
            glyphs,
            script: 'latin',
            language: 'dflt',
            direction: 'ltr',
            features: {},
            advanceWidth,
            advanceHeight: 0,
            bbox: undefined
        };
    }
    glyphForCodePoint(codePoint) {
        const glyph = this.getGlyph(codePoint);
        glyph.advanceWidth = 400;
        return glyph;
    }
    getGlyph(id) {
        return {
            id,
            codePoints: [
                id
            ],
            isLigature: false,
            name: this.src.font.characterToGlyph(id),
            _font: this.src,
            // @ts-expect-error assign proper value
            advanceWidth: undefined
        };
    }
    hasGlyphForCodePoint(codePoint) {
        return this.src.font.characterToGlyph(codePoint) !== '.notdef';
    }
    // Based on empirical observation
    get ascent() {
        return 900;
    }
    // Based on empirical observation
    get capHeight() {
        switch(this.name){
            case 'Times-Roman':
            case 'Times-Bold':
            case 'Times-Italic':
            case 'Times-BoldItalic':
                return 650;
            case 'Courier':
            case 'Courier-Bold':
            case 'Courier-Oblique':
            case 'Courier-BoldOblique':
                return 550;
            default:
                return 690;
        }
    }
    // Based on empirical observation
    get xHeight() {
        switch(this.name){
            case 'Times-Roman':
            case 'Times-Bold':
            case 'Times-Italic':
            case 'Times-BoldItalic':
                return 440;
            case 'Courier':
            case 'Courier-Bold':
            case 'Courier-Oblique':
            case 'Courier-BoldOblique':
                return 390;
            default:
                return 490;
        }
    }
    // Based on empirical observation
    get descent() {
        switch(this.name){
            case 'Times-Roman':
            case 'Times-Bold':
            case 'Times-Italic':
            case 'Times-BoldItalic':
                return -220;
            case 'Courier':
            case 'Courier-Bold':
            case 'Courier-Oblique':
            case 'Courier-BoldOblique':
                return -230;
            default:
                return -200;
        }
    }
    get lineGap() {
        return 0;
    }
    get unitsPerEm() {
        return 1000;
    }
    stringsForGlyph() {
        throw new Error('Method not implemented.');
    }
    glyphsForString() {
        throw new Error('Method not implemented.');
    }
    widthOfGlyph() {
        throw new Error('Method not implemented.');
    }
    getAvailableFeatures() {
        throw new Error('Method not implemented.');
    }
    createSubset() {
        throw new Error('Method not implemented.');
    }
    getVariation() {
        throw new Error('Method not implemented.');
    }
    getFont() {
        throw new Error('Method not implemented.');
    }
    getName() {
        throw new Error('Method not implemented.');
    }
    setDefaultLanguage() {
        throw new Error('Method not implemented.');
    }
}
const fetchFont = async (src, options)=>{
    const response = await fetch(src, options);
    const data = await response.arrayBuffer();
    return new Uint8Array(data);
};
const isDataUrl = (dataUrl)=>{
    const header = dataUrl.split(',')[0];
    const hasDataPrefix = header.substring(0, 5) === 'data:';
    const hasBase64Prefix = header.split(';')[1] === 'base64';
    return hasDataPrefix && hasBase64Prefix;
};
class FontSource {
    src;
    fontFamily;
    fontStyle;
    fontWeight;
    data;
    options;
    loadResultPromise;
    constructor(src, fontFamily, fontStyle, fontWeight, options){
        this.src = src;
        this.fontFamily = fontFamily;
        this.fontStyle = fontStyle || 'normal';
        this.fontWeight = fontWeight || 400;
        this.data = null;
        this.options = options || {};
        this.loadResultPromise = null;
    }
    async _load() {
        const { postscriptName } = this.options;
        let data = null;
        if (STANDARD_FONTS.includes(this.src)) {
            data = new StandardFont(this.src);
        } else if (isDataUrl(this.src)) {
            const raw = this.src.split(',')[1];
            const uint8Array = new Uint8Array(atob(raw).split('').map((c)=>c.charCodeAt(0)));
            data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fontkit$2f$dist$2f$browser$2d$module$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])(uint8Array, postscriptName);
        } else {
            const { headers, body, method = 'GET' } = this.options;
            const buffer = await fetchFont(this.src, {
                method,
                body,
                headers
            });
            data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fontkit$2f$dist$2f$browser$2d$module$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["create"])(buffer, postscriptName);
        }
        if (data && 'fonts' in data) {
            throw new Error('Font collection is not supported');
        }
        this.data = data;
    }
    async load() {
        if (this.loadResultPromise === null) {
            this.loadResultPromise = this._load();
        }
        return this.loadResultPromise;
    }
}
const FONT_WEIGHTS = {
    thin: 100,
    hairline: 100,
    ultralight: 200,
    extralight: 200,
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    demibold: 600,
    bold: 700,
    ultrabold: 800,
    extrabold: 800,
    heavy: 900,
    black: 900
};
const resolveFontWeight = (value)=>{
    return typeof value === 'string' ? FONT_WEIGHTS[value] : value;
};
const sortByFontWeight = (a, b)=>a.fontWeight - b.fontWeight;
class FontFamily {
    family;
    sources;
    static create(family) {
        return new FontFamily(family);
    }
    constructor(family){
        this.family = family;
        this.sources = [];
    }
    register({ src, fontWeight, fontStyle, ...options }) {
        const numericFontWeight = fontWeight ? resolveFontWeight(fontWeight) : undefined;
        this.sources.push(new FontSource(src, this.family, fontStyle, numericFontWeight, options));
    }
    resolve(descriptor) {
        const { fontWeight = 400, fontStyle = 'normal' } = descriptor;
        const styleSources = this.sources.filter((s)=>s.fontStyle === fontStyle);
        const exactFit = styleSources.find((s)=>s.fontWeight === fontWeight);
        if (exactFit) return exactFit;
        // Weight resolution. https://developer.mozilla.org/en-US/docs/Web/CSS/font-weight#Fallback_weights
        let font = null;
        const numericFontWeight = resolveFontWeight(fontWeight);
        if (numericFontWeight >= 400 && numericFontWeight <= 500) {
            const leftOffset = styleSources.filter((s)=>s.fontWeight <= numericFontWeight);
            const rightOffset = styleSources.filter((s)=>s.fontWeight > 500);
            const fit = styleSources.filter((s)=>s.fontWeight >= numericFontWeight && s.fontWeight < 500);
            font = fit[0] || leftOffset[leftOffset.length - 1] || rightOffset[0];
        }
        const lt = styleSources.filter((s)=>s.fontWeight < numericFontWeight).sort(sortByFontWeight);
        const gt = styleSources.filter((s)=>s.fontWeight > numericFontWeight).sort(sortByFontWeight);
        if (numericFontWeight < 400) {
            font = lt[lt.length - 1] || gt[0];
        }
        if (numericFontWeight > 500) {
            font = gt[0] || lt[lt.length - 1];
        }
        if (!font) {
            throw new Error(`Could not resolve font for ${this.family}, fontWeight ${fontWeight}, fontStyle ${fontStyle}`);
        }
        return font;
    }
}
class FontStore {
    fontFamilies = {};
    emojiSource = null;
    constructor(){
        this.register({
            family: 'Helvetica',
            fonts: [
                {
                    src: 'Helvetica',
                    fontStyle: 'normal',
                    fontWeight: 400
                },
                {
                    src: 'Helvetica-Bold',
                    fontStyle: 'normal',
                    fontWeight: 700
                },
                {
                    src: 'Helvetica-Oblique',
                    fontStyle: 'italic',
                    fontWeight: 400
                },
                {
                    src: 'Helvetica-BoldOblique',
                    fontStyle: 'italic',
                    fontWeight: 700
                }
            ]
        });
        this.register({
            family: 'Courier',
            fonts: [
                {
                    src: 'Courier',
                    fontStyle: 'normal',
                    fontWeight: 400
                },
                {
                    src: 'Courier-Bold',
                    fontStyle: 'normal',
                    fontWeight: 700
                },
                {
                    src: 'Courier-Oblique',
                    fontStyle: 'italic',
                    fontWeight: 400
                },
                {
                    src: 'Courier-BoldOblique',
                    fontStyle: 'italic',
                    fontWeight: 700
                }
            ]
        });
        this.register({
            family: 'Times-Roman',
            fonts: [
                {
                    src: 'Times-Roman',
                    fontStyle: 'normal',
                    fontWeight: 400
                },
                {
                    src: 'Times-Bold',
                    fontStyle: 'normal',
                    fontWeight: 700
                },
                {
                    src: 'Times-Italic',
                    fontStyle: 'italic',
                    fontWeight: 400
                },
                {
                    src: 'Times-BoldItalic',
                    fontStyle: 'italic',
                    fontWeight: 700
                }
            ]
        });
        // For backwards compatibility
        this.register({
            family: 'Helvetica-Bold',
            src: 'Helvetica-Bold'
        });
        this.register({
            family: 'Helvetica-Oblique',
            src: 'Helvetica-Oblique'
        });
        this.register({
            family: 'Helvetica-BoldOblique',
            src: 'Helvetica-BoldOblique'
        });
        this.register({
            family: 'Courier-Bold',
            src: 'Courier-Bold'
        });
        this.register({
            family: 'Courier-Oblique',
            src: 'Courier-Oblique'
        });
        this.register({
            family: 'Courier-BoldOblique',
            src: 'Courier-BoldOblique'
        });
        this.register({
            family: 'Times-Bold',
            src: 'Times-Bold'
        });
        this.register({
            family: 'Times-Italic',
            src: 'Times-Italic'
        });
        this.register({
            family: 'Times-BoldItalic',
            src: 'Times-BoldItalic'
        });
        // Load default fonts
        this.load({
            fontFamily: 'Helvetica',
            fontStyle: 'normal',
            fontWeight: 400
        });
        this.load({
            fontFamily: 'Helvetica',
            fontStyle: 'normal',
            fontWeight: 700
        });
        this.load({
            fontFamily: 'Helvetica',
            fontStyle: 'italic',
            fontWeight: 400
        });
        this.load({
            fontFamily: 'Helvetica',
            fontStyle: 'italic',
            fontWeight: 700
        });
    }
    hyphenationCallback = null;
    register = (data)=>{
        const { family } = data;
        if (!this.fontFamilies[family]) {
            this.fontFamilies[family] = FontFamily.create(family);
        }
        // Bulk loading
        if ('fonts' in data) {
            for(let i = 0; i < data.fonts.length; i += 1){
                const { src, fontStyle, fontWeight, ...options } = data.fonts[i];
                this.fontFamilies[family].register({
                    src,
                    fontStyle,
                    fontWeight,
                    ...options
                });
            }
        } else {
            const { src, fontStyle, fontWeight, ...options } = data;
            this.fontFamilies[family].register({
                src,
                fontStyle,
                fontWeight,
                ...options
            });
        }
    };
    registerEmojiSource = (emojiSource)=>{
        this.emojiSource = emojiSource;
    };
    registerHyphenationCallback = (callback)=>{
        this.hyphenationCallback = callback;
    };
    getFont = (descriptor)=>{
        const { fontFamily } = descriptor;
        if (!this.fontFamilies[fontFamily]) {
            throw new Error(`Font family not registered: ${fontFamily}. Please register it calling Font.register() method.`);
        }
        return this.fontFamilies[fontFamily].resolve(descriptor);
    };
    load = async (descriptor)=>{
        const font = this.getFont(descriptor);
        if (font) await font.load();
    };
    reset = ()=>{
        const keys = Object.keys(this.fontFamilies);
        for(let i = 0; i < keys.length; i += 1){
            const key = keys[i];
            for(let j = 0; j < this.fontFamilies[key].sources.length; j++){
                const fontSource = this.fontFamilies[key].sources[j];
                fontSource.data = null;
            }
        }
    };
    clear = ()=>{
        this.fontFamilies = {};
    };
    getRegisteredFonts = ()=>this.fontFamilies;
    getEmojiSource = ()=>this.emojiSource;
    getHyphenationCallback = ()=>this.hyphenationCallback;
    getRegisteredFontFamilies = ()=>Object.keys(this.fontFamilies);
}
;
}}),
"[project]/node_modules/@react-pdf/fns/lib/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Applies a function to the value at the given index of an array
 *
 * @param index
 * @param fn
 * @param collection
 * @returns Copy of the array with the element at the given index replaced with the result of the function application.
 */ __turbopack_context__.s({
    "adjust": (()=>adjust),
    "asyncCompose": (()=>asyncCompose),
    "capitalize": (()=>capitalize),
    "castArray": (()=>castArray),
    "compose": (()=>compose),
    "dropLast": (()=>dropLast),
    "evolve": (()=>evolve),
    "get": (()=>get),
    "isNil": (()=>isNil),
    "last": (()=>last),
    "mapValues": (()=>mapValues),
    "matchPercent": (()=>matchPercent),
    "omit": (()=>omit),
    "parseFloat": (()=>parseFloat$1),
    "pick": (()=>pick),
    "repeat": (()=>repeat),
    "reverse": (()=>reverse),
    "upperFirst": (()=>upperFirst),
    "without": (()=>without)
});
const adjust = (index, fn, collection)=>{
    if (index >= 0 && index >= collection.length) return collection;
    if (index < 0 && Math.abs(index) > collection.length) return collection;
    const i = index < 0 ? collection.length + index : index;
    return Object.assign([], collection, {
        [i]: fn(collection[i])
    });
};
/* eslint-disable no-await-in-loop */ /**
 * Performs right-to-left function composition with async functions support
 *
 * @param fns - Functions
 * @returns Composed function
 */ const asyncCompose = (...fns)=>async (value, ...args)=>{
        let result = value;
        const reversedFns = fns.slice().reverse();
        for(let i = 0; i < reversedFns.length; i += 1){
            const fn = reversedFns[i];
            result = await fn(result, ...args);
        }
        return result;
    };
/**
 * Capitalize first letter of each word
 *
 * @param value - Any string
 * @returns Capitalized string
 */ const capitalize = (value)=>{
    if (!value) return value;
    return value.replace(/(^|\s)\S/g, (l)=>l.toUpperCase());
};
/**
 * Casts value to array
 *
 * @template T - The type of the value.
 * @param value - The value to cast into an array.
 * @returns An array containing the given value.
 */ const castArray = (value)=>{
    return Array.isArray(value) ? value : [
        value
    ];
};
/**
 * Performs right-to-left function composition
 *
 * @param fns - Functions
 * @returns Composed function
 */ const compose = (...fns)=>(value, ...args)=>{
        let result = value;
        const reversedFns = fns.slice().reverse();
        for(let i = 0; i < reversedFns.length; i += 1){
            const fn = reversedFns[i];
            result = fn(result, ...args);
        }
        return result;
    };
/**
 * Drops the last element from an array.
 *
 * @template T
 * @param  array - The array to drop the last element from
 * @returns - The new array with the last element dropped
 */ const dropLast = (array)=>array.slice(0, array.length - 1);
/**
 * Applies a set of transformations to an object and returns a new object with the transformed values.
 *
 * @template T
 * @param transformations - The transformations to apply.
 * @param object - The object to transform.
 * @returns The transformed object.
 */ function evolve(transformations, object) {
    const result = {};
    const keys = Object.keys(object);
    for(let i = 0; i < keys.length; i += 1){
        const key = keys[i];
        const transformation = transformations[key];
        if (typeof transformation === 'function') {
            result[key] = transformation(object[key]);
        } else {
            result[key] = object[key];
        }
    }
    return result;
}
/**
 * Checks if a value is null or undefined.
 *
 * @template T - The type of the value.
 * @param value - The value to check
 * @returns True if the value is null or undefined, false otherwise
 */ const isNil = (value)=>value === null || value === undefined;
/**
 * Retrieves the value at a given path from an object.
 *
 * @param target - The object to retrieve the value from.
 * @param path - The path of the value to retrieve.
 * @param defaultValue - The default value to return if the path does not exist.
 * @returns The value at the given path, or the default value if the path does not exist.
 */ const get = (target, path, defaultValue)=>{
    if (isNil(target)) return defaultValue;
    const _path = castArray(path);
    let result = target;
    for(let i = 0; i < _path.length; i += 1){
        if (isNil(result)) return undefined;
        result = result[_path[i]];
    }
    return isNil(result) ? defaultValue : result;
};
function last(value) {
    return value === '' ? '' : value[value.length - 1];
}
/**
 * Maps over the values of an object and applies a function to each value.
 *
 * @param object - The object to map over
 * @param fn - The function to apply to each value
 * @returns A new object with the mapped values
 */ const mapValues = (object, fn)=>{
    const entries = Object.entries(object);
    const acc = {};
    return entries.reduce((acc, [key, value], index)=>{
        acc[key] = fn(value, key, index);
        return acc;
    }, acc);
};
const isPercent = (value)=>/((-)?\d+\.?\d*)%/g.exec(`${value}`);
/**
 * Get percentage value of input
 *
 * @param value
 * @returns Percent value (if matches)
 */ const matchPercent = (value)=>{
    const match = isPercent(value);
    if (match) {
        const f = parseFloat(match[1]);
        const percent = f / 100;
        return {
            percent,
            value: f
        };
    }
    return null;
};
/**
 * Creates a new object by omitting specified keys from the original object.
 *
 * @param keys - The key or keys to omit
 * @param object - The original object
 * @returns The new object without the omitted keys
 */ const omit = (keys, object)=>{
    const _keys = castArray(keys);
    const copy = Object.assign({}, object);
    _keys.forEach((key)=>{
        delete copy[key];
    });
    return copy;
};
/**
 * Picks the specified keys from an object and returns a new object with only those keys.
 *
 * @param keys - The keys to pick from the object
 * @param object - The object to pick the keys from
 * @returns A new object with only the picked keys
 */ const pick = (keys, obj)=>{
    const result = {};
    for(let i = 0; i < keys.length; i += 1){
        const key = keys[i];
        if (key in obj) result[key] = obj[key];
    }
    return result;
};
/**
 * Repeats an element a specified number of times.
 *
 * @template T
 * @param element - Element to be repeated
 * @param length - Number of times to repeat element
 * @returns Repeated elements
 */ const repeat = (element, length = 0)=>{
    const result = new Array(length);
    for(let i = 0; i < length; i += 1){
        result[i] = element;
    }
    return result;
};
/**
 * Reverses the list
 *
 * @template T
 * @param list - List to be reversed
 * @returns Reversed list
 */ const reverse = (list)=>Array.prototype.slice.call(list, 0).reverse();
/**
 * Capitalize first letter of string
 *
 * @param value - String
 * @returns Capitalized string
 */ const upperFirst = (value)=>{
    if (!value) return value;
    return value.charAt(0).toUpperCase() + value.slice(1);
};
/**
 * Returns a new array with all the values from the original array that are not present in the keys array.
 *
 * @param keys - The keys to pick from the object
 * @param array - Array to filter the values from
 * @returns A new array with without the omitted values
 */ const without = (keys, array)=>{
    const result = [];
    for(let i = 0; i < array.length; i += 1){
        const value = array[i];
        if (!keys.includes(value)) result.push(value);
    }
    return result;
};
/**
 * Parse a string or number to a float
 *
 * @param value - String or number
 * @returns Parsed float
 */ const parseFloat$1 = (value)=>{
    return typeof value === 'string' ? Number.parseFloat(value) : value;
};
;
}}),
"[project]/node_modules/@react-pdf/render/lib/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>render)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/primitives/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/fns/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$abs$2d$svg$2d$path$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/abs-svg-path/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parse$2d$svg$2d$path$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/parse-svg-path/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$normalize$2d$svg$2d$path$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/normalize-svg-path/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$string$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/color-string/index.js [app-client] (ecmascript)");
;
;
;
;
;
;
const renderPath = (ctx, node)=>{
    const d = node.props?.d;
    if (d) ctx.path(node.props.d);
};
const KAPPA$3 = 4.0 * ((Math.sqrt(2) - 1.0) / 3.0);
const renderRect = (ctx, node)=>{
    const x = node.props?.x || 0;
    const y = node.props?.y || 0;
    const rx = node.props?.rx || 0;
    const ry = node.props?.ry || 0;
    const width = node.props?.width || 0;
    const height = node.props?.height || 0;
    if (!width || !height) return;
    if (rx && ry) {
        const krx = rx * KAPPA$3;
        const kry = ry * KAPPA$3;
        ctx.moveTo(x + rx, y);
        ctx.lineTo(x - rx + width, y);
        ctx.bezierCurveTo(x - rx + width + krx, y, x + width, y + ry - kry, x + width, y + ry);
        ctx.lineTo(x + width, y + height - ry);
        ctx.bezierCurveTo(x + width, y + height - ry + kry, x - rx + width + krx, y + height, x - rx + width, y + height);
        ctx.lineTo(x + rx, y + height);
        ctx.bezierCurveTo(x + rx - krx, y + height, x, y + height - ry + kry, x, y + height - ry);
        ctx.lineTo(x, y + ry);
        ctx.bezierCurveTo(x, y + ry - kry, x + rx - krx, y, x + rx, y);
    } else {
        ctx.moveTo(x, y);
        ctx.lineTo(x + width, y);
        ctx.lineTo(x + width, y + height);
        ctx.lineTo(x, y + height);
    }
    ctx.closePath();
};
const renderLine$1 = (ctx, node)=>{
    const { x1, x2, y1, y2 } = node.props || {};
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
};
const renderGroup = ()=>{
// noop
};
const KAPPA$2 = 4.0 * ((Math.sqrt(2) - 1.0) / 3.0);
const drawEllipse = (ctx, rx, ry, cx = 0, cy = 0)=>{
    const x = cx - rx;
    const y = cy - ry;
    const ox = rx * KAPPA$2;
    const oy = ry * KAPPA$2;
    const xe = x + rx * 2;
    const ye = y + ry * 2;
    const xm = x + rx;
    const ym = y + ry;
    ctx.moveTo(x, ym);
    ctx.bezierCurveTo(x, ym - oy, xm - ox, y, xm, y);
    ctx.bezierCurveTo(xm + ox, y, xe, ym - oy, xe, ym);
    ctx.bezierCurveTo(xe, ym + oy, xm + ox, ye, xm, ye);
    ctx.bezierCurveTo(xm - ox, ye, x, ym + oy, x, ym);
    ctx.closePath();
};
const renderEllipse = (ctx, node)=>{
    const { cx, cy, rx, ry } = node.props || {};
    drawEllipse(ctx, rx, ry, cx, cy);
};
const renderCircle = (ctx, node)=>{
    const cx = node.props?.cx;
    const cy = node.props?.cy;
    const r = node.props?.r;
    drawEllipse(ctx, r, r, cx, cy);
};
/* eslint-disable no-return-assign */ const number = (n)=>{
    if (n > -1e21 && n < 1e21) {
        return Math.round(n * 1e6) / 1e6;
    }
    throw new Error(`unsupported number: ${n}`);
};
const _renderGlyphs = (ctx, encoded, positions, x, y)=>{
    const commands = [];
    const scale = ctx._fontSize / 1000;
    let i;
    let last = 0;
    let hadOffset = false;
    ctx.save();
    // flip coordinate system
    ctx.transform(1, 0, 0, -1, 0, ctx.page.height);
    y = ctx.page.height - y;
    // add current font to page if necessary
    if (ctx.page.fonts[ctx._font.id] == null) {
        ctx.page.fonts[ctx._font.id] = ctx._font.ref();
    }
    // begin the text object
    ctx.addContent('BT');
    // text position
    ctx.addContent(`1 0 0 1 ${number(x)} ${number(y)} Tm`);
    // font and font size
    ctx.addContent(`/${ctx._font.id} ${number(ctx._fontSize)} Tf`);
    // Adds a segment of text to the TJ command buffer
    const addSegment = (cur)=>{
        if (last < cur) {
            const hex = encoded.slice(last, cur).join('');
            const advance = positions[cur - 1].xAdvance - positions[cur - 1].advanceWidth;
            commands.push(`<${hex}> ${number(-advance)}`);
        }
        return last = cur;
    };
    // Flushes the current TJ commands to the output stream
    const flush = (s)=>{
        addSegment(s);
        if (commands.length > 0) {
            ctx.addContent(`[${commands.join(' ')}] TJ`);
            return commands.length = 0;
        }
    };
    for(i = 0; i < positions.length; i += 1){
        // If we have an x or y offset, we have to break out of the current TJ command
        // so we can move the text position.
        const pos = positions[i];
        if (pos.xOffset || pos.yOffset) {
            // Flush the current buffer
            flush(i);
            // Move the text position and flush just the current character
            ctx.addContent(`1 0 0 1 ${number(x + pos.xOffset * scale)} ${number(y + pos.yOffset * scale)} Tm`);
            flush(i + 1);
            hadOffset = true;
        } else {
            // If the last character had an offset, reset the text position
            if (hadOffset) {
                ctx.addContent(`1 0 0 1 ${number(x)} ${number(y)} Tm`);
                hadOffset = false;
            }
            // Group segments that don't have any advance adjustments
            if (pos.xAdvance - pos.advanceWidth !== 0) {
                addSegment(i + 1);
            }
        }
        x += pos.xAdvance * scale;
    }
    // Flush any remaining commands
    flush(i);
    // end the text object
    ctx.addContent('ET');
    // restore flipped coordinate system
    return ctx.restore();
};
const renderGlyphs = (ctx, glyphs, positions, x, y)=>{
    const scale = 1000 / ctx._fontSize;
    const unitsPerEm = ctx._font.font.unitsPerEm || 1000;
    const advanceWidthScale = 1000 / unitsPerEm;
    // Glyph encoding and positioning
    const encodedGlyphs = ctx._font.encodeGlyphs(glyphs);
    const encodedPositions = positions.map((pos, i)=>({
            xAdvance: pos.xAdvance * scale,
            yAdvance: pos.yAdvance * scale,
            xOffset: pos.xOffset,
            yOffset: pos.yOffset,
            advanceWidth: glyphs[i].advanceWidth * advanceWidthScale
        }));
    return _renderGlyphs(ctx, encodedGlyphs, encodedPositions, x, y);
};
const renderRun$1 = (ctx, run)=>{
    if (!run.glyphs) return;
    if (!run.positions) return;
    const runAdvanceWidth = run.xAdvance;
    const font = run.attributes.font?.[0];
    const { fontSize, color, opacity } = run.attributes;
    if (color) ctx.fillColor(color);
    ctx.fillOpacity(opacity);
    if (font) {
        ctx.font(font.type === 'STANDARD' ? font.fullName : font, fontSize);
    }
    try {
        renderGlyphs(ctx, run.glyphs, run.positions, 0, 0);
    } catch (error) {
        console.log(error);
    }
    ctx.translate(runAdvanceWidth, 0);
};
const renderSpan = (ctx, line, textAnchor, dominantBaseline)=>{
    ctx.save();
    const x = line.box?.x || 0;
    const y = line.box?.y || 0;
    const font = line.runs[0]?.attributes.font?.[0];
    const scale = line.runs[0]?.attributes?.scale || 1;
    const width = line.xAdvance;
    if (!font) return;
    const ascent = font.ascent * scale;
    const xHeight = font.xHeight * scale;
    const descent = font.descent * scale;
    const capHeight = font.capHeight * scale;
    let xTranslate = x;
    let yTranslate = y;
    switch(textAnchor){
        case 'middle':
            xTranslate = x - width / 2;
            break;
        case 'end':
            xTranslate = x - width;
            break;
        default:
            xTranslate = x;
            break;
    }
    switch(dominantBaseline){
        case 'middle':
        case 'central':
            yTranslate = y + capHeight / 2;
            break;
        case 'hanging':
            yTranslate = y + capHeight;
            break;
        case 'mathematical':
            yTranslate = y + xHeight;
            break;
        case 'text-after-edge':
            yTranslate = y + descent;
            break;
        case 'text-before-edge':
            yTranslate = y + ascent;
            break;
        default:
            yTranslate = y;
            break;
    }
    ctx.translate(xTranslate, yTranslate);
    line.runs.forEach((run)=>renderRun$1(ctx, run));
    ctx.restore();
};
const renderSvgText = (ctx, node)=>{
    const children = node.children;
    children.forEach((span)=>renderSpan(ctx, span.lines[0], span.props.textAnchor, span.props.dominantBaseline));
};
const pairs = (values)=>{
    const result = [];
    for(let i = 0; i < values.length; i += 2){
        result.push([
            values[i],
            values[i + 1]
        ]);
    }
    return result;
};
/**
 * Parse svg-like points into number arrays
 *
 * @param points string ex. "20,30 50,60"
 * @returns points array ex. [[20, 30], [50, 60]]
 */ const parsePoints = (points)=>{
    let values = (points || '').trim().replace(/,/g, ' ').replace(/(\d)-(\d)/g, '$1 -$2').split(/\s+/);
    if (values.length % 2 !== 0) {
        values = values.slice(0, -1);
    }
    const mappedValues = values.map(parseFloat);
    return pairs(mappedValues);
};
const drawPolyline = (ctx, points)=>{
    if (points.length > 0) {
        ctx.moveTo(points[0][0], points[0][1]);
        points.slice(1).forEach((p)=>ctx.lineTo(p[0], p[1]));
    }
};
const renderPolyline = (ctx, node)=>{
    const points = parsePoints(node.props.points || '');
    drawPolyline(ctx, points);
};
const renderPolygon = (ctx, node)=>{
    const points = parsePoints(node.props.points || '');
    drawPolyline(ctx, points);
    ctx.closePath();
};
const renderImage$1 = (ctx, node)=>{
    if (!node.box) return;
    if (!node.image?.data) return;
    const { x = 0, y = 0 } = node.props;
    const { width, height, opacity } = node.style;
    const paddingTop = node.box.paddingLeft || 0;
    const paddingLeft = node.box.paddingLeft || 0;
    if (width === 0 || height === 0) {
        console.warn(`Image with src '${node.props.href}' skipped due to invalid dimensions`);
        return;
    }
    if (typeof width === 'string' || typeof height === 'string') {
        console.warn(`Image with src '${node.props.href}' skipped due to percentage width or height`);
        return;
    }
    ctx.save();
    ctx.fillOpacity(opacity || 1).image(node.image.data, x + paddingLeft, y + paddingTop, {
        width,
        height
    });
    ctx.restore();
};
// This constant is used to approximate a symmetrical arc using a cubic
// Bezier curve.
const KAPPA$1 = 4.0 * ((Math.sqrt(2) - 1.0) / 3.0);
const clipNode = (ctx, node)=>{
    if (!node.box) return;
    if (!node.style) return;
    const { top, left, width, height } = node.box;
    const { borderTopLeftRadius = 0, borderTopRightRadius = 0, borderBottomRightRadius = 0, borderBottomLeftRadius = 0 } = node.style;
    // Border top
    // @ts-expect-error this is always a number due to resolve border radius step
    const rtr = Math.min(borderTopRightRadius, 0.5 * width, 0.5 * height);
    const ctr = rtr * (1.0 - KAPPA$1);
    ctx.moveTo(left + rtr, top);
    ctx.lineTo(left + width - rtr, top);
    ctx.bezierCurveTo(left + width - ctr, top, left + width, top + ctr, left + width, top + rtr);
    // Border right
    // @ts-expect-error this is always a number due to resolve border radius step
    const rbr = Math.min(borderBottomRightRadius, 0.5 * width, 0.5 * height);
    const cbr = rbr * (1.0 - KAPPA$1);
    ctx.lineTo(left + width, top + height - rbr);
    ctx.bezierCurveTo(left + width, top + height - cbr, left + width - cbr, top + height, left + width - rbr, top + height);
    // Border bottom
    // @ts-expect-error this is always a number due to resolve border radius step
    const rbl = Math.min(borderBottomLeftRadius, 0.5 * width, 0.5 * height);
    const cbl = rbl * (1.0 - KAPPA$1);
    ctx.lineTo(left + rbl, top + height);
    ctx.bezierCurveTo(left + cbl, top + height, left, top + height - cbl, left, top + height - rbl);
    // Border left
    // @ts-expect-error this is always a number due to resolve border radius step
    const rtl = Math.min(borderTopLeftRadius, 0.5 * width, 0.5 * height);
    const ctl = rtl * (1.0 - KAPPA$1);
    ctx.lineTo(left, top + rtl);
    ctx.bezierCurveTo(left, top + ctl, left + ctl, top, left + rtl, top);
    ctx.closePath();
    ctx.clip();
};
const applySingleTransformation = (ctx, transform, origin)=>{
    const { operation, value } = transform;
    switch(operation){
        case 'scale':
            {
                const [scaleX, scaleY] = value;
                ctx.scale(scaleX, scaleY, {
                    origin
                });
                break;
            }
        case 'rotate':
            {
                const [angle] = value;
                ctx.rotate(angle, {
                    origin
                });
                break;
            }
        case 'translate':
            {
                const [x, y = 0] = value;
                ctx.translate(x, y, {
                    origin
                });
                break;
            }
        case 'skew':
            {
                const [xAngle = 0, yAngle = 0] = value;
                const radx = xAngle * Math.PI / 180;
                const rady = yAngle * Math.PI / 180;
                const tanx = Math.tan(radx);
                const tany = Math.tan(rady);
                let x = 0;
                let y = 0;
                if (origin != null) {
                    [x, y] = Array.from(origin);
                    const x1 = x + tanx * y;
                    const y1 = y + tany * x;
                    x -= x1;
                    y -= y1;
                }
                ctx.transform(1, tany, tanx, 1, x, y);
                break;
            }
        case 'matrix':
            {
                ctx.transform(...value);
                break;
            }
        default:
            {
                console.error(`Transform operation: '${operation}' doesn't supported`);
            }
    }
};
const applyTransformations = (ctx, node)=>{
    if (!node.origin) return;
    const { props, style } = node;
    const origin = [
        node.origin.left,
        node.origin.top
    ];
    const propsTransform = 'transform' in props ? props.transform : undefined;
    const operations = style?.transform || propsTransform || [];
    operations.forEach((operation)=>{
        applySingleTransformation(ctx, operation, origin);
    });
};
// From https://github.com/dy/svg-path-bounds/blob/master/index.js
const getPathBoundingBox = (node)=>{
    const path = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$normalize$2d$svg$2d$path$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$abs$2d$svg$2d$path$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$parse$2d$svg$2d$path$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(node.props?.d || '')));
    if (!path.length) return [
        0,
        0,
        0,
        0
    ];
    const bounds = [
        Infinity,
        Infinity,
        -Infinity,
        -Infinity
    ];
    for(let i = 0, l = path.length; i < l; i += 1){
        const points = path[i].slice(1);
        for(let j = 0; j < points.length; j += 2){
            if (points[j + 0] < bounds[0]) bounds[0] = points[j + 0];
            if (points[j + 1] < bounds[1]) bounds[1] = points[j + 1];
            if (points[j + 0] > bounds[2]) bounds[2] = points[j + 0];
            if (points[j + 1] > bounds[3]) bounds[3] = points[j + 1];
        }
    }
    return bounds;
};
const getCircleBoundingBox = (node)=>{
    const r = node.props?.r || 0;
    const cx = node.props?.cx || 0;
    const cy = node.props?.cy || 0;
    return [
        cx - r,
        cy - r,
        cx + r,
        cy + r
    ];
};
const getEllipseBoundingBox = (node)=>{
    const cx = node.props?.cx || 0;
    const cy = node.props?.cy || 0;
    const rx = node.props?.rx || 0;
    const ry = node.props?.ry || 0;
    return [
        cx - rx,
        cy - ry,
        cx + rx,
        cy + ry
    ];
};
const getLineBoundingBox = (node)=>{
    const x1 = node.props?.x1 || 0;
    const y1 = node.props?.y1 || 0;
    const x2 = node.props?.x2 || 0;
    const y2 = node.props?.y2 || 0;
    return [
        Math.min(x1, x2),
        Math.min(y1, y2),
        Math.max(x1, x2),
        Math.max(y1, y2)
    ];
};
const getRectBoundingBox = (node)=>{
    const x = node.props?.x || 0;
    const y = node.props?.y || 0;
    const width = node.props?.width || 0;
    const height = node.props?.height || 0;
    return [
        x,
        y,
        x + width,
        y + height
    ];
};
const max = (values)=>Math.max(-Infinity, ...values);
const min = (values)=>Math.min(Infinity, ...values);
const getPolylineBoundingBox = (node)=>{
    const points = parsePoints(node.props?.points);
    const xValues = points.map((p)=>p[0]);
    const yValues = points.map((p)=>p[1]);
    return [
        min(xValues),
        min(yValues),
        max(xValues),
        max(yValues)
    ];
};
const boundingBoxFns = {
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Rect"]]: getRectBoundingBox,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Line"]]: getLineBoundingBox,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Path"]]: getPathBoundingBox,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Circle"]]: getCircleBoundingBox,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Ellipse"]]: getEllipseBoundingBox,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Polygon"]]: getPolylineBoundingBox,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Polyline"]]: getPolylineBoundingBox
};
const getBoundingBox = (node)=>{
    const boundingBoxFn = boundingBoxFns[node.type];
    return boundingBoxFn ? boundingBoxFn(node) : [
        0,
        0,
        0,
        0
    ];
};
const setStrokeWidth = (ctx, node)=>{
    if (!node.props) return;
    if (!('strokeWidth' in node.props)) return;
    const lineWidth = node.props.strokeWidth;
    if (lineWidth) ctx.lineWidth(lineWidth);
};
const setStrokeColor = (ctx, node)=>{
    if (!node.props) return;
    if (!('stroke' in node.props)) return;
    const strokeColor = node.props.stroke;
    if (strokeColor) ctx.strokeColor(strokeColor);
};
const setOpacity = (ctx, node)=>{
    if (!node.props) return;
    if (!('opacity' in node.props)) return;
    const opacity = node.props.opacity;
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(opacity)) ctx.opacity(opacity);
};
const setFillOpacity = (ctx, node)=>{
    if (!node.props) return;
    if (!('fillOpacity' in node.props)) return;
    const fillOpacity = node.props.fillOpacity || null;
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(fillOpacity)) ctx.fillOpacity(fillOpacity);
};
const setStrokeOpacity = (ctx, node)=>{
    if (!node.props) return;
    if (!('strokeOpacity' in node.props)) return;
    const strokeOpacity = node.props?.strokeOpacity;
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(strokeOpacity)) ctx.strokeOpacity(strokeOpacity);
};
const setLineJoin = (ctx, node)=>{
    if (!node.props) return;
    if (!('strokeLinejoin' in node.props)) return;
    const lineJoin = node.props.strokeLinejoin;
    if (lineJoin) ctx.lineJoin(lineJoin);
};
const setLineCap = (ctx, node)=>{
    if (!node.props) return;
    if (!('strokeLinecap' in node.props)) return;
    const lineCap = node.props?.strokeLinecap;
    if (lineCap) ctx.lineCap(lineCap);
};
const setLineDash = (ctx, node)=>{
    if (!node.props) return;
    if (!('strokeDasharray' in node.props)) return;
    const value = node.props?.strokeDasharray || null;
    // @ts-expect-error check this works as expected
    if (value) ctx.dash(value.split(/[\s,]+/).map(Number));
};
const hasLinearGradientFill = (node)=>{
    if (!node.props) return false;
    if (!('fill' in node.props)) return false;
    if (typeof node.props.fill === 'string') return false;
    return node.props.fill?.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LinearGradient"];
};
const hasRadialGradientFill = (node)=>{
    if (!node.props) return false;
    if (!('fill' in node.props)) return false;
    if (typeof node.props.fill === 'string') return false;
    return node.props.fill?.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadialGradient"];
};
function multiplyMatrices(m1, m2) {
    const a = m1[0] * m2[0] + m1[2] * m2[1];
    const b = m1[1] * m2[0] + m1[3] * m2[1];
    const c = m1[0] * m2[2] + m1[2] * m2[3];
    const d = m1[1] * m2[2] + m1[3] * m2[3];
    const e = m1[0] * m2[4] + m1[2] * m2[5] + m1[4];
    const f = m1[1] * m2[4] + m1[3] * m2[5] + m1[5];
    return [
        a,
        b,
        c,
        d,
        e,
        f
    ];
}
const transformGradient = (grad, transforms, bbox, units)=>{
    const matrices = transforms.map((transform)=>{
        switch(transform.operation){
            case 'scale':
                {
                    const value = transform.value;
                    return [
                        value[0],
                        0,
                        0,
                        value[1],
                        0,
                        0
                    ];
                }
            case 'translate':
                {
                    const value = transform.value;
                    let x = value[0] || 0;
                    let y = value[1] || 0;
                    if (units === 'objectBoundingBox') {
                        x = (bbox[2] - bbox[0]) * x;
                        y = (bbox[3] - bbox[1]) * y;
                    }
                    return [
                        1,
                        0,
                        0,
                        1,
                        x,
                        y
                    ];
                }
            case 'rotate':
                {
                    const value = transform.value;
                    const cos = Math.cos(value[0]);
                    const sin = Math.sin(value[0]);
                    return [
                        cos,
                        sin,
                        -sin,
                        cos,
                        0,
                        0
                    ];
                }
            case 'skew':
                {
                    const value = transform.value;
                    return [
                        1,
                        Math.tan(value[0]),
                        Math.tan(value[1]),
                        1,
                        0,
                        0
                    ];
                }
            case 'matrix':
                {
                    const value = transform.value;
                    let x = value[4] || 0;
                    let y = value[5] || 0;
                    if (units === 'objectBoundingBox') {
                        x = (bbox[2] - bbox[0]) * x;
                        y = (bbox[3] - bbox[1]) * y;
                    }
                    return [
                        value[0],
                        value[1],
                        value[2],
                        value[3],
                        x,
                        y
                    ];
                }
            default:
                return [
                    1,
                    0,
                    0,
                    1,
                    0,
                    0
                ];
        }
    });
    const matrix = matrices.reduce(multiplyMatrices, [
        1,
        0,
        0,
        1,
        0,
        0
    ]);
    grad.setTransform(...matrix);
};
// Math simplified from https://github.com/devongovett/svgkit/blob/master/src/elements/SVGGradient.js#L104
const setLinearGradientFill = (ctx, node)=>{
    if (!node.props) return;
    if (!('fill' in node.props)) return;
    const bbox = getBoundingBox(node);
    const gradient = node.props?.fill;
    if (!gradient) return;
    const units = gradient.props.gradientUnits || 'objectBoundingBox';
    const transforms = gradient.props.gradientTransform || [];
    let x1 = gradient.props.x1 || 0;
    let y1 = gradient.props.y1 || 0;
    let x2 = gradient.props.x2 || 1;
    let y2 = gradient.props.y2 || 0;
    if (units === 'objectBoundingBox') {
        const m0 = bbox[2] - bbox[0];
        const m3 = bbox[3] - bbox[1];
        const m4 = bbox[0];
        const m5 = bbox[1];
        x1 = m0 * x1 + m4;
        y1 = m3 * y1 + m5;
        x2 = m0 * x2 + m4;
        y2 = m3 * y2 + m5;
    }
    const grad = ctx.linearGradient(x1, y1, x2, y2);
    transformGradient(grad, transforms, bbox, units);
    gradient.children?.forEach((stop)=>{
        grad.stop(stop.props.offset, stop.props.stopColor, stop.props.stopOpacity);
    });
    ctx.fill(grad);
};
// Math simplified from https://github.com/devongovett/svgkit/blob/master/src/elements/SVGGradient.js#L155
const setRadialGradientFill = (ctx, node)=>{
    if (!node.props) return;
    if (!('fill' in node.props)) return;
    const bbox = getBoundingBox(node);
    const gradient = node.props?.fill;
    if (!gradient) return;
    const units = gradient.props.gradientUnits || 'objectBoundingBox';
    const transforms = gradient.props.gradientTransform || [];
    let r = gradient.props.r || 0.5;
    let cx = gradient.props.cx || 0.5;
    let cy = gradient.props.cy || 0.5;
    let fx = gradient.props.fx || cx;
    let fy = gradient.props.fy || cy;
    if (units === 'objectBoundingBox') {
        const m0 = bbox[2] - bbox[0];
        const m3 = bbox[3] - bbox[1];
        const m4 = bbox[0];
        const m5 = bbox[1];
        r = r * m0;
        cx = m0 * cx + m4;
        cy = m3 * cy + m5;
        fx = m0 * fx + m4;
        fy = m3 * fy + m5;
    }
    const grad = ctx.radialGradient(cx, cy, 0, fx, fy, r);
    transformGradient(grad, transforms, bbox, units);
    gradient.children?.forEach((stop)=>{
        grad.stop(stop.props.offset, stop.props.stopColor, stop.props.stopOpacity);
    });
    ctx.fill(grad);
};
const setFillColor = (ctx, node)=>{
    if (!node.props) return;
    if (!('fill' in node.props)) return;
    const fillColor = node.props?.fill;
    if (fillColor) ctx.fillColor(fillColor);
};
const setFill = (ctx, node)=>{
    if (hasLinearGradientFill(node)) return setLinearGradientFill(ctx, node);
    if (hasRadialGradientFill(node)) return setRadialGradientFill(ctx, node);
    return setFillColor(ctx, node);
};
const draw = (ctx, node)=>{
    const props = node.props || {};
    if ('fill' in props && 'stroke' in props && props.fill && props.stroke) {
        ctx.fillAndStroke(props.fillRule);
    } else if ('fill' in props && props.fill) {
        ctx.fill(props.fillRule);
    } else if ('stroke' in props && props.stroke) {
        ctx.stroke();
    } else {
        ctx.save();
        ctx.opacity(0);
        ctx.fill(null);
        ctx.restore();
    }
};
const noop = ()=>{};
const renderFns$1 = {
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tspan"]]: noop,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextInstance"]]: noop,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Path"]]: renderPath,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Rect"]]: renderRect,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Line"]]: renderLine$1,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["G"]]: renderGroup,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"]]: renderSvgText,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Circle"]]: renderCircle,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Image"]]: renderImage$1,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Ellipse"]]: renderEllipse,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Polygon"]]: renderPolygon,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Polyline"]]: renderPolyline
};
const renderNode$1 = (ctx, node)=>{
    const renderFn = renderFns$1[node.type];
    if (renderFn) {
        renderFn(ctx, node);
    } else {
        console.warn(`SVG node of type ${node.type} is not currently supported`);
    }
};
const drawNode = (ctx, node)=>{
    setLineCap(ctx, node);
    setLineDash(ctx, node);
    setLineJoin(ctx, node);
    setStrokeWidth(ctx, node);
    setStrokeColor(ctx, node);
    setFill(ctx, node);
    setStrokeOpacity(ctx, node);
    setFillOpacity(ctx, node);
    setOpacity(ctx, node);
    applyTransformations(ctx, node);
    renderNode$1(ctx, node);
    draw(ctx, node);
};
const clipPath = (ctx, node)=>{
    if (!node.props) return;
    if (!('clipPath' in node.props)) return;
    const value = node.props.clipPath;
    if (value) {
        const children = value.children || [];
        children.forEach((child)=>renderNode$1(ctx, child));
        ctx.clip();
    }
};
const drawChildren = (ctx, node)=>{
    const children = node.children || [];
    children.forEach((child)=>{
        ctx.save();
        clipPath(ctx, child);
        drawNode(ctx, child);
        drawChildren(ctx, child);
        ctx.restore();
    });
};
const resolveAspectRatio = (ctx, node)=>{
    if (!node.box) return;
    const { width, height } = node.box;
    const { viewBox, preserveAspectRatio } = node.props;
    const { meetOrSlice = 'meet', align = 'xMidYMid' } = preserveAspectRatio || {};
    if (viewBox == null || width == null || height == null) return;
    const x = viewBox?.minX || 0;
    const y = viewBox?.minY || 0;
    const logicalWidth = viewBox?.maxX || width;
    const logicalHeight = viewBox?.maxY || height;
    const logicalRatio = logicalWidth / logicalHeight;
    const physicalRatio = width / height;
    const scaleX = width / logicalWidth;
    const scaleY = height / logicalHeight;
    if (align === 'none') {
        ctx.scale(scaleX, scaleY);
        ctx.translate(-x, -y);
        return;
    }
    if (logicalRatio < physicalRatio && meetOrSlice === 'meet' || logicalRatio >= physicalRatio && meetOrSlice === 'slice') {
        ctx.scale(scaleY, scaleY);
        switch(align){
            case 'xMinYMin':
            case 'xMinYMid':
            case 'xMinYMax':
                ctx.translate(-x, -y);
                break;
            case 'xMidYMin':
            case 'xMidYMid':
            case 'xMidYMax':
                ctx.translate(-x - (logicalWidth - width * logicalHeight / height) / 2, -y);
                break;
            default:
                ctx.translate(-x - (logicalWidth - width * logicalHeight / height), -y);
        }
    } else {
        ctx.scale(scaleX, scaleX);
        switch(align){
            case 'xMinYMin':
            case 'xMidYMin':
            case 'xMaxYMin':
                ctx.translate(-x, -y);
                break;
            case 'xMinYMid':
            case 'xMidYMid':
            case 'xMaxYMid':
                ctx.translate(-x, -y - (logicalHeight - height * logicalWidth / width) / 2);
                break;
            default:
                ctx.translate(-x, -y - (logicalHeight - height * logicalWidth / width));
        }
    }
};
const moveToOrigin = (ctx, node)=>{
    if (!node.box) return;
    const { top, left } = node.box;
    const paddingLeft = node.box.paddingLeft || 0;
    const paddingTop = node.box.paddingTop || 0;
    ctx.translate(left + paddingLeft, top + paddingTop);
};
const renderSvg = (ctx, node)=>{
    ctx.save();
    clipNode(ctx, node);
    moveToOrigin(ctx, node);
    resolveAspectRatio(ctx, node);
    drawChildren(ctx, node);
    ctx.restore();
};
const black = {
    value: '#000',
    opacity: 1
};
// TODO: parse to number[] in layout to avoid this step
const parseColor = (hex)=>{
    if (!hex) return black;
    const parsed = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$string$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(hex);
    if (!parsed) return black;
    const value = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$string$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].to.hex(parsed.value.slice(0, 3));
    const opacity = parsed.value[3];
    return {
        value,
        opacity
    };
};
const DEST_REGEXP = /^#.+/;
const isSrcId$1 = (src)=>src.match(DEST_REGEXP);
const renderAttachment = (ctx, attachment)=>{
    const { xOffset = 0, yOffset = 0, width, height, image } = attachment;
    ctx.translate(-width + xOffset, -height + yOffset);
    ctx.image(image, 0, 0, {
        fit: [
            width,
            height
        ],
        align: 'center',
        valign: 'bottom'
    });
};
const renderAttachments = (ctx, run)=>{
    if (!run.glyphs) return;
    if (!run.positions) return;
    const font = run.attributes.font?.[0];
    if (!font) return;
    ctx.save();
    const space = font.glyphForCodePoint(0x20);
    const objectReplacement = font.glyphForCodePoint(0xfffc);
    let attachmentAdvance = 0;
    for(let i = 0; i < run.glyphs.length; i += 1){
        const position = run.positions[i];
        const glyph = run.glyphs[i];
        attachmentAdvance += position.xAdvance || 0;
        if (glyph.id === objectReplacement.id && run.attributes.attachment) {
            ctx.translate(attachmentAdvance, position.yOffset || 0);
            renderAttachment(ctx, run.attributes.attachment);
            run.glyphs[i] = space;
            attachmentAdvance = 0;
        }
    }
    ctx.restore();
};
const renderRun = (ctx, run)=>{
    if (!run.glyphs) return;
    if (!run.positions) return;
    const font = run.attributes.font?.[0];
    if (!font) return;
    const { fontSize, link } = run.attributes;
    const color = parseColor(run.attributes.color);
    const opacity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(run.attributes.opacity) ? color.opacity : run.attributes.opacity;
    const { height = 0, descent = 0, xAdvance = 0 } = run;
    ctx.fillColor(color.value);
    ctx.fillOpacity(opacity);
    if (link) {
        if (isSrcId$1(link)) {
            ctx.goTo(0, -height - descent, xAdvance, height, link.slice(1));
        } else {
            ctx.link(0, -height - descent, xAdvance, height, link);
        }
    }
    renderAttachments(ctx, run);
    ctx.font(font.type === 'STANDARD' ? font.fullName : font, fontSize);
    try {
        renderGlyphs(ctx, run.glyphs, run.positions, 0, 0);
    } catch (error) {
        console.log(error);
    }
    ctx.translate(xAdvance, 0);
};
const renderBackground$1 = (ctx, rect, backgroundColor)=>{
    const color = parseColor(backgroundColor);
    ctx.save();
    ctx.fillOpacity(color.opacity);
    ctx.rect(rect.x, rect.y, rect.width, rect.height);
    ctx.fill(color.value);
    ctx.restore();
};
const renderDecorationLine = (ctx, decorationLine)=>{
    ctx.save();
    ctx.lineWidth(decorationLine.rect.height);
    ctx.strokeOpacity(decorationLine.opacity);
    if (/dashed/.test(decorationLine.style)) {
        ctx.dash(3 * decorationLine.rect.height, {});
    } else if (/dotted/.test(decorationLine.style)) {
        ctx.dash(decorationLine.rect.height, {});
    }
    if (/wavy/.test(decorationLine.style)) {
        const dist = Math.max(2, decorationLine.rect.height);
        let step = 1.1 * dist;
        const stepCount = Math.floor(decorationLine.rect.width / (2 * step));
        // Adjust step to fill entire width
        const remainingWidth = decorationLine.rect.width - stepCount * 2 * step;
        const adjustment = remainingWidth / stepCount / 2;
        step += adjustment;
        const cp1y = decorationLine.rect.y + dist;
        const cp2y = decorationLine.rect.y - dist;
        let { x } = decorationLine.rect;
        ctx.moveTo(decorationLine.rect.x, decorationLine.rect.y);
        for(let i = 0; i < stepCount; i += 1){
            ctx.bezierCurveTo(x + step, cp1y, x + step, cp2y, x + 2 * step, decorationLine.rect.y);
            x += 2 * step;
        }
    } else {
        ctx.moveTo(decorationLine.rect.x, decorationLine.rect.y);
        ctx.lineTo(decorationLine.rect.x + decorationLine.rect.width, decorationLine.rect.y);
        if (/double/.test(decorationLine.style)) {
            ctx.moveTo(decorationLine.rect.x, decorationLine.rect.y + decorationLine.rect.height * 2);
            ctx.lineTo(decorationLine.rect.x + decorationLine.rect.width, decorationLine.rect.y + decorationLine.rect.height * 2);
        }
    }
    ctx.stroke(decorationLine.color);
    ctx.restore();
};
const renderLine = (ctx, line)=>{
    if (!line.box) return;
    const lineAscent = line.ascent || 0;
    ctx.save();
    ctx.translate(line.box.x, line.box.y + lineAscent);
    for(let i = 0; i < line.runs.length; i += 1){
        const run = line.runs[i];
        const isLastRun = i === line.runs.length - 1;
        if (run.attributes.backgroundColor) {
            const xAdvance = run.xAdvance ?? 0;
            const overflowRight = isLastRun ? line.overflowRight ?? 0 : 0;
            const backgroundRect = {
                x: 0,
                y: -lineAscent,
                height: line.box.height,
                width: xAdvance - overflowRight
            };
            renderBackground$1(ctx, backgroundRect, run.attributes.backgroundColor);
        }
        renderRun(ctx, run);
    }
    ctx.restore();
    ctx.save();
    ctx.translate(line.box.x, line.box.y);
    if (line.decorationLines) {
        for(let i = 0; i < line.decorationLines.length; i += 1){
            const decorationLine = line.decorationLines[i];
            renderDecorationLine(ctx, decorationLine);
        }
    }
    ctx.restore();
};
const renderBlock = (ctx, block)=>{
    block.forEach((line)=>{
        renderLine(ctx, line);
    });
};
const renderText = (ctx, node)=>{
    if (!node.box) return;
    if (!node.lines) return;
    const { top, left } = node.box;
    const blocks = [
        node.lines
    ];
    const paddingTop = node.box?.paddingTop || 0;
    const paddingLeft = node.box?.paddingLeft || 0;
    const initialY = node.lines[0] ? node.lines[0].box.y : 0;
    const offsetX = node.alignOffset || 0;
    ctx.save();
    ctx.translate(left + paddingLeft - offsetX, top + paddingTop - initialY);
    blocks.forEach((block)=>{
        renderBlock(ctx, block);
    });
    ctx.restore();
};
const renderPage = (ctx, node)=>{
    if (!node.box) return;
    const { width, height } = node.box;
    const dpi = node.props?.dpi || 72;
    const userUnit = dpi / 72;
    ctx.addPage({
        size: [
            width,
            height
        ],
        margin: 0,
        userUnit
    });
};
const renderNote = (ctx, node)=>{
    if (!node.box) return;
    const { top, left } = node.box;
    const value = node?.children?.[0].value || '';
    const color = node.style?.backgroundColor;
    ctx.note(left, top, 0, 0, value, {
        color
    });
};
const embedImage = (ctx, node)=>{
    const src = node.image.data;
    let image;
    if (typeof src === 'string') {
        image = ctx._imageRegistry[src];
    }
    if (!image) {
        image = ctx.openImage(src);
    }
    if (!image.obj) {
        image.embed(ctx);
    }
    return image;
};
const isNumeric = (n)=>{
    return !Number.isNaN(parseFloat(n)) && Number.isFinite(n);
};
const applyContainObjectFit = (cw, ch, iw, ih, px, py)=>{
    const cr = cw / ch;
    const ir = iw / ih;
    const pxp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["matchPercent"])(px ?? null);
    const pyp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["matchPercent"])(py ?? null);
    const pxv = pxp ? pxp.percent : 0.5;
    const pyv = pyp ? pyp.percent : 0.5;
    if (cr > ir) {
        const height = ch;
        const width = height * ir;
        const yOffset = isNumeric(py) ? py : 0;
        const xOffset = isNumeric(px) ? px : (cw - width) * pxv;
        return {
            width,
            height,
            xOffset,
            yOffset
        };
    }
    const width = cw;
    const height = width / ir;
    const xOffset = isNumeric(px) ? px : 0;
    const yOffset = isNumeric(py) ? py : (ch - height) * pyv;
    return {
        width,
        height,
        yOffset,
        xOffset
    };
};
const applyNoneObjectFit = (cw, ch, iw, ih, px, py)=>{
    const width = iw;
    const height = ih;
    const pxp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["matchPercent"])(px ?? null);
    const pyp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["matchPercent"])(py ?? null);
    const pxv = pxp ? pxp.percent : 0.5;
    const pyv = pyp ? pyp.percent : 0.5;
    const xOffset = isNumeric(px) ? px : (cw - width) * pxv;
    const yOffset = isNumeric(py) ? py : (ch - height) * pyv;
    return {
        width,
        height,
        xOffset,
        yOffset
    };
};
const applyCoverObjectFit = (cw, ch, iw, ih, px, py)=>{
    const ir = iw / ih;
    const cr = cw / ch;
    const pxp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["matchPercent"])(px ?? null);
    const pyp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["matchPercent"])(py ?? null);
    const pxv = pxp ? pxp.percent : 0.5;
    const pyv = pyp ? pyp.percent : 0.5;
    if (cr > ir) {
        const width = cw;
        const height = width / ir;
        const xOffset = isNumeric(px) ? px : 0;
        const yOffset = isNumeric(py) ? py : (ch - height) * pyv;
        return {
            width,
            height,
            yOffset,
            xOffset
        };
    }
    const height = ch;
    const width = height * ir;
    const xOffset = isNumeric(px) ? px : (cw - width) * pxv;
    const yOffset = isNumeric(py) ? py : 0;
    return {
        width,
        height,
        xOffset,
        yOffset
    };
};
const applyScaleDownObjectFit = (cw, ch, iw, ih, px, py)=>{
    const containDimension = applyContainObjectFit(cw, ch, iw, ih, px, py);
    const noneDimension = applyNoneObjectFit(cw, ch, iw, ih, px, py);
    return containDimension.width < noneDimension.width ? containDimension : noneDimension;
};
const applyFillObjectFit = (cw, ch, px, py)=>{
    return {
        width: cw,
        height: ch,
        xOffset: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["matchPercent"])(px ?? null) ? 0 : px || 0,
        yOffset: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["matchPercent"])(py ?? null) ? 0 : py || 0
    };
};
const resolveObjectFit = (type = 'fill', cw, ch, iw, ih, px, py)=>{
    switch(type){
        case 'contain':
            return applyContainObjectFit(cw, ch, iw, ih, px, py);
        case 'cover':
            return applyCoverObjectFit(cw, ch, iw, ih, px, py);
        case 'none':
            return applyNoneObjectFit(cw, ch, iw, ih, px, py);
        case 'scale-down':
            return applyScaleDownObjectFit(cw, ch, iw, ih, px, py);
        default:
            return applyFillObjectFit(cw, ch, px, py);
    }
};
const drawImage = (ctx, node, options)=>{
    if (!node.box) return;
    if (!node.image) return;
    const { left, top } = node.box;
    const opacity = node.style?.opacity;
    const objectFit = node.style?.objectFit;
    const objectPositionX = node.style?.objectPositionX;
    const objectPositionY = node.style?.objectPositionY;
    const paddingTop = node.box.paddingTop || 0;
    const paddingRight = node.box.paddingRight || 0;
    const paddingBottom = node.box.paddingBottom || 0;
    const paddingLeft = node.box.paddingLeft || 0;
    const imageCache = options.imageCache || new Map();
    const { width, height, xOffset, yOffset } = resolveObjectFit(objectFit, node.box.width - paddingLeft - paddingRight, node.box.height - paddingTop - paddingBottom, node.image.width, node.image.height, objectPositionX, objectPositionY);
    if (node.image.data) {
        if (width !== 0 && height !== 0) {
            const cacheKey = node.image.key;
            const image = imageCache.get(cacheKey) || embedImage(ctx, node);
            if (cacheKey) imageCache.set(cacheKey, image);
            const imageOpacity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(opacity) ? 1 : opacity;
            ctx.fillOpacity(imageOpacity).image(image, left + paddingLeft + xOffset, top + paddingTop + yOffset, {
                width,
                height
            });
        } else {
            console.warn(`Image with src '${JSON.stringify(node.props.src || node.props.source)}' skipped due to invalid dimensions`);
        }
    }
};
const renderImage = (ctx, node, options)=>{
    ctx.save();
    clipNode(ctx, node);
    drawImage(ctx, node, options);
    ctx.restore();
};
const CONTENT_COLOR = '#a1c6e7';
const PADDING_COLOR = '#c4deb9';
const MARGIN_COLOR = '#f8cca1';
// TODO: Draw debug boxes using clipping to enhance quality
const debugContent = (ctx, node)=>{
    if (!node.box) return;
    const { left, top, width, height, paddingLeft = 0, paddingTop = 0, paddingRight = 0, paddingBottom = 0, borderLeftWidth = 0, borderTopWidth = 0, borderRightWidth = 0, borderBottomWidth = 0 } = node.box;
    ctx.fillColor(CONTENT_COLOR).opacity(0.5).rect(left + paddingLeft + borderLeftWidth, top + paddingTop + borderTopWidth, width - paddingLeft - paddingRight - borderRightWidth - borderLeftWidth, height - paddingTop - paddingBottom - borderTopWidth - borderBottomWidth).fill();
};
const debugPadding = (ctx, node)=>{
    if (!node.box) return;
    const { left, top, width, height, paddingLeft = 0, paddingTop = 0, paddingRight = 0, paddingBottom = 0, borderLeftWidth = 0, borderTopWidth = 0, borderRightWidth = 0, borderBottomWidth = 0 } = node.box;
    ctx.fillColor(PADDING_COLOR).opacity(0.5);
    // Padding top
    ctx.rect(left + paddingLeft + borderLeftWidth, top + borderTopWidth, width - paddingRight - paddingLeft - borderLeftWidth - borderRightWidth, paddingTop).fill();
    // Padding left
    ctx.rect(left + borderLeftWidth, top + borderTopWidth, paddingLeft, height - borderTopWidth - borderBottomWidth).fill();
    // Padding right
    ctx.rect(left + width - paddingRight - borderRightWidth, top + borderTopWidth, paddingRight, height - borderTopWidth - borderBottomWidth).fill();
    // Padding bottom
    ctx.rect(left + paddingLeft + borderLeftWidth, top + height - paddingBottom - borderBottomWidth, width - paddingRight - paddingLeft - borderLeftWidth - borderRightWidth, paddingBottom).fill();
};
const debugMargin = (ctx, node)=>{
    if (!node.box) return;
    const { left, top, width, height } = node.box;
    const { marginLeft = 0, marginTop = 0, marginRight = 0, marginBottom = 0 } = node.box;
    ctx.fillColor(MARGIN_COLOR).opacity(0.5);
    // Margin top
    ctx.rect(left, top - marginTop, width, marginTop).fill();
    // Margin left
    ctx.rect(left - marginLeft, top - marginTop, marginLeft, height + marginTop + marginBottom).fill();
    // Margin right
    ctx.rect(left + width, top - marginTop, marginRight, height + marginTop + marginBottom).fill();
    // Margin bottom
    ctx.rect(left, top + height, width, marginBottom).fill();
};
const debugText = (ctx, node)=>{
    if (!node.box) return;
    const { left, top, width, height } = node.box;
    const { marginLeft = 0, marginTop = 0, marginRight = 0, marginBottom = 0 } = node.box;
    const roundedWidth = Math.round(width + marginLeft + marginRight);
    const roundedHeight = Math.round(height + marginTop + marginBottom);
    ctx.fontSize(6).opacity(1).fillColor('black').text(`${roundedWidth} x ${roundedHeight}`, left - marginLeft, Math.max(top - marginTop - 4, 1), {
        width: Infinity
    });
};
const debugOrigin = (ctx, node)=>{
    if (node.origin) {
        ctx.circle(node.origin.left, node.origin.top, 3).fill('red').circle(node.origin.left, node.origin.top, 5).stroke('red');
    }
};
const renderDebug = (ctx, node)=>{
    if (!node.props) return;
    if (!('debug' in node.props) || !node.props.debug) return;
    ctx.save();
    debugContent(ctx, node);
    debugPadding(ctx, node);
    debugMargin(ctx, node);
    debugText(ctx, node);
    debugOrigin(ctx, node);
    ctx.restore();
};
const availableMethods = [
    'dash',
    'clip',
    'save',
    'path',
    'fill',
    'font',
    'text',
    'rect',
    'scale',
    'moveTo',
    'lineTo',
    'stroke',
    'rotate',
    'circle',
    'lineCap',
    'opacity',
    'ellipse',
    'polygon',
    'restore',
    'lineJoin',
    'fontSize',
    'fillColor',
    'lineWidth',
    'translate',
    'miterLimit',
    'strokeColor',
    'fillOpacity',
    'roundedRect',
    'fillAndStroke',
    'strokeOpacity',
    'bezierCurveTo',
    'quadraticCurveTo',
    'linearGradient',
    'radialGradient'
];
const painter = (ctx)=>{
    const p = availableMethods.reduce((acc, prop)=>({
            ...acc,
            [prop]: (...args)=>{
                // @ts-expect-error ctx[prop] is a function
                ctx[prop](...args);
                return p;
            }
        }), {});
    return p;
};
const renderCanvas = (ctx, node)=>{
    if (!node.box) return;
    const { top, left, width, height } = node.box;
    const paddingTop = node.box.paddingTop || 0;
    const paddingLeft = node.box.paddingLeft || 0;
    const paddingRight = node.box.paddingRight || 0;
    const paddingBottom = node.box.paddingBottom || 0;
    const availableWidth = width - paddingLeft - paddingRight;
    const availableHeight = height - paddingTop - paddingBottom;
    if (!availableWidth || !availableHeight) {
        console.warn('Canvas element has null width or height. Please provide valid values via the `style` prop in order to correctly render it.');
    }
    ctx.save().translate(left + paddingLeft, top + paddingTop);
    if (node.props.paint) {
        node.props.paint(painter(ctx), availableWidth, availableHeight);
    }
    ctx.restore();
};
// Ref: https://www.w3.org/TR/css-backgrounds-3/#borders
// This constant is used to approximate a symmetrical arc using a cubic Bezier curve.
const KAPPA = 4.0 * ((Math.sqrt(2) - 1.0) / 3.0);
const clipBorderTop = (ctx, layout, style, rtr, rtl)=>{
    const { top, left, width, height } = layout;
    const { borderTopWidth, borderRightWidth, borderLeftWidth } = style;
    // Clip outer top border edge
    ctx.moveTo(left + rtl, top);
    ctx.lineTo(left + width - rtr, top);
    // Ellipse coefficients outer top right cap
    const c0 = rtr * (1.0 - KAPPA);
    // Clip outer top right cap
    ctx.bezierCurveTo(left + width - c0, top, left + width, top + c0, left + width, top + rtr);
    // Move down in case the margin exceedes the radius
    const topRightYCoord = top + Math.max(borderTopWidth, rtr);
    ctx.lineTo(left + width, topRightYCoord);
    // Clip inner top right cap
    ctx.lineTo(left + width - borderRightWidth, topRightYCoord);
    // Ellipse coefficients inner top right cap
    const innerTopRightRadiusX = Math.max(rtr - borderRightWidth, 0);
    const innerTopRightRadiusY = Math.max(rtr - borderTopWidth, 0);
    const c1 = innerTopRightRadiusX * (1.0 - KAPPA);
    const c2 = innerTopRightRadiusY * (1.0 - KAPPA);
    // Clip inner top right cap
    ctx.bezierCurveTo(left + width - borderRightWidth, top + borderTopWidth + c2, left + width - borderRightWidth - c1, top + borderTopWidth, left + width - borderRightWidth - innerTopRightRadiusX, top + borderTopWidth);
    // Clip inner top border edge
    ctx.lineTo(left + Math.max(rtl, borderLeftWidth), top + borderTopWidth);
    // Ellipse coefficients inner top left cap
    const innerTopLeftRadiusX = Math.max(rtl - borderLeftWidth, 0);
    const innerTopLeftRadiusY = Math.max(rtl - borderTopWidth, 0);
    const c3 = innerTopLeftRadiusX * (1.0 - KAPPA);
    const c4 = innerTopLeftRadiusY * (1.0 - KAPPA);
    const topLeftYCoord = top + Math.max(borderTopWidth, rtl);
    // Clip inner top left cap
    ctx.bezierCurveTo(left + borderLeftWidth + c3, top + borderTopWidth, left + borderLeftWidth, top + borderTopWidth + c4, left + borderLeftWidth, topLeftYCoord);
    ctx.lineTo(left, topLeftYCoord);
    // Move down in case the margin exceedes the radius
    ctx.lineTo(left, top + rtl);
    // Ellipse coefficients outer top left cap
    const c5 = rtl * (1.0 - KAPPA);
    // Clip outer top left cap
    ctx.bezierCurveTo(left, top + c5, left + c5, top, left + rtl, top);
    ctx.closePath();
    ctx.clip();
    // Clip border top cap joins
    if (borderRightWidth) {
        const trSlope = -borderTopWidth / borderRightWidth;
        ctx.moveTo(left + width / 2, trSlope * (-width / 2) + top);
        ctx.lineTo(left + width, top);
        ctx.lineTo(left, top);
        ctx.lineTo(left, top + height);
        ctx.closePath();
        ctx.clip();
    }
    if (borderLeftWidth) {
        const trSlope = -borderTopWidth / borderLeftWidth;
        ctx.moveTo(left + width / 2, trSlope * (-width / 2) + top);
        ctx.lineTo(left, top);
        ctx.lineTo(left + width, top);
        ctx.lineTo(left + width, top + height);
        ctx.closePath();
        ctx.clip();
    }
};
const fillBorderTop = (ctx, layout, style, rtr, rtl)=>{
    const { top, left, width } = layout;
    const { borderTopColor, borderTopWidth, borderTopStyle, borderRightWidth, borderLeftWidth } = style;
    const c0 = rtl * (1.0 - KAPPA);
    const c1 = rtr * (1.0 - KAPPA);
    ctx.moveTo(left, top + Math.max(rtl, borderTopWidth));
    ctx.bezierCurveTo(left, top + c0, left + c0, top, left + rtl, top);
    ctx.lineTo(left + width - rtr, top);
    ctx.bezierCurveTo(left + width - c1, top, left + width, top + c1, left + width, top + rtr);
    ctx.strokeColor(borderTopColor);
    ctx.lineWidth(Math.max(borderRightWidth, borderTopWidth, borderLeftWidth) * 2);
    if (borderTopStyle === 'dashed') {
        ctx.dash(borderTopWidth * 2, {
            space: borderTopWidth * 1.2
        });
    } else if (borderTopStyle === 'dotted') {
        ctx.dash(borderTopWidth, {
            space: borderTopWidth * 1.2
        });
    }
    ctx.stroke();
    ctx.undash();
};
const clipBorderRight = (ctx, layout, style, rtr, rbr)=>{
    const { top, left, width, height } = layout;
    const { borderTopWidth, borderRightWidth, borderBottomWidth } = style;
    // Clip outer right border edge
    ctx.moveTo(left + width, top + rtr);
    ctx.lineTo(left + width, top + height - rbr);
    // Ellipse coefficients outer bottom right cap
    const c0 = rbr * (1.0 - KAPPA);
    // Clip outer top right cap
    ctx.bezierCurveTo(left + width, top + height - c0, left + width - c0, top + height, left + width - rbr, top + height);
    // Move left in case the margin exceedes the radius
    const topBottomXCoord = left + width - Math.max(borderRightWidth, rbr);
    ctx.lineTo(topBottomXCoord, top + height);
    // Clip inner bottom right cap
    ctx.lineTo(topBottomXCoord, top + height - borderBottomWidth);
    // Ellipse coefficients inner bottom right cap
    const innerBottomRightRadiusX = Math.max(rbr - borderRightWidth, 0);
    const innerBottomRightRadiusY = Math.max(rbr - borderBottomWidth, 0);
    const c1 = innerBottomRightRadiusX * (1.0 - KAPPA);
    const c2 = innerBottomRightRadiusY * (1.0 - KAPPA);
    // Clip inner top right cap
    ctx.bezierCurveTo(left + width - borderRightWidth - c1, top + height - borderBottomWidth, left + width - borderRightWidth, top + height - borderBottomWidth - c2, left + width - borderRightWidth, top + height - Math.max(rbr, borderBottomWidth));
    // Clip inner right border edge
    ctx.lineTo(left + width - borderRightWidth, top + Math.max(rtr, borderTopWidth));
    // Ellipse coefficients inner top right cap
    const innerTopRightRadiusX = Math.max(rtr - borderRightWidth, 0);
    const innerTopRightRadiusY = Math.max(rtr - borderTopWidth, 0);
    const c3 = innerTopRightRadiusX * (1.0 - KAPPA);
    const c4 = innerTopRightRadiusY * (1.0 - KAPPA);
    const topRightXCoord = left + width - Math.max(rtr, borderRightWidth);
    // Clip inner top left cap
    ctx.bezierCurveTo(left + width - borderRightWidth, top + borderTopWidth + c4, left + width - borderRightWidth - c3, top + borderTopWidth, topRightXCoord, top + borderTopWidth);
    ctx.lineTo(topRightXCoord, top);
    // Move right in case the margin exceedes the radius
    ctx.lineTo(left + width - rtr, top);
    // Ellipse coefficients outer top right cap
    const c5 = rtr * (1.0 - KAPPA);
    // Clip outer top right cap
    ctx.bezierCurveTo(left + width - c5, top, left + width, top + c5, left + width, top + rtr);
    ctx.closePath();
    ctx.clip();
    // Clip border right cap joins
    if (borderTopWidth) {
        const trSlope = -borderTopWidth / borderRightWidth;
        ctx.moveTo(left + width / 2, trSlope * (-width / 2) + top);
        ctx.lineTo(left + width, top);
        ctx.lineTo(left + width, top + height);
        ctx.lineTo(left, top + height);
        ctx.closePath();
        ctx.clip();
    }
    if (borderBottomWidth) {
        const brSlope = borderBottomWidth / borderRightWidth;
        ctx.moveTo(left + width / 2, brSlope * (-width / 2) + top + height);
        ctx.lineTo(left + width, top + height);
        ctx.lineTo(left + width, top);
        ctx.lineTo(left, top);
        ctx.closePath();
        ctx.clip();
    }
};
const fillBorderRight = (ctx, layout, style, rtr, rbr)=>{
    const { top, left, width, height } = layout;
    const { borderRightColor, borderRightStyle, borderRightWidth, borderTopWidth, borderBottomWidth } = style;
    const c0 = rbr * (1.0 - KAPPA);
    const c1 = rtr * (1.0 - KAPPA);
    ctx.moveTo(left + width - rtr, top);
    ctx.bezierCurveTo(left + width - c1, top, left + width, top + c1, left + width, top + rtr);
    ctx.lineTo(left + width, top + height - rbr);
    ctx.bezierCurveTo(left + width, top + height - c0, left + width - c0, top + height, left + width - rbr, top + height);
    ctx.strokeColor(borderRightColor);
    ctx.lineWidth(Math.max(borderRightWidth, borderTopWidth, borderBottomWidth) * 2);
    if (borderRightStyle === 'dashed') {
        ctx.dash(borderRightWidth * 2, {
            space: borderRightWidth * 1.2
        });
    } else if (borderRightStyle === 'dotted') {
        ctx.dash(borderRightWidth, {
            space: borderRightWidth * 1.2
        });
    }
    ctx.stroke();
    ctx.undash();
};
const clipBorderBottom = (ctx, layout, style, rbl, rbr)=>{
    const { top, left, width, height } = layout;
    const { borderBottomWidth, borderRightWidth, borderLeftWidth } = style;
    // Clip outer top border edge
    ctx.moveTo(left + width - rbr, top + height);
    ctx.lineTo(left + rbl, top + height);
    // Ellipse coefficients outer top right cap
    const c0 = rbl * (1.0 - KAPPA);
    // Clip outer top right cap
    ctx.bezierCurveTo(left + c0, top + height, left, top + height - c0, left, top + height - rbl);
    // Move up in case the margin exceedes the radius
    const bottomLeftYCoord = top + height - Math.max(borderBottomWidth, rbl);
    ctx.lineTo(left, bottomLeftYCoord);
    // Clip inner bottom left cap
    ctx.lineTo(left + borderLeftWidth, bottomLeftYCoord);
    // Ellipse coefficients inner top right cap
    const innerBottomLeftRadiusX = Math.max(rbl - borderLeftWidth, 0);
    const innerBottomLeftRadiusY = Math.max(rbl - borderBottomWidth, 0);
    const c1 = innerBottomLeftRadiusX * (1.0 - KAPPA);
    const c2 = innerBottomLeftRadiusY * (1.0 - KAPPA);
    // Clip inner bottom left cap
    ctx.bezierCurveTo(left + borderLeftWidth, top + height - borderBottomWidth - c2, left + borderLeftWidth + c1, top + height - borderBottomWidth, left + borderLeftWidth + innerBottomLeftRadiusX, top + height - borderBottomWidth);
    // Clip inner bottom border edge
    ctx.lineTo(left + width - Math.max(rbr, borderRightWidth), top + height - borderBottomWidth);
    // Ellipse coefficients inner top left cap
    const innerBottomRightRadiusX = Math.max(rbr - borderRightWidth, 0);
    const innerBottomRightRadiusY = Math.max(rbr - borderBottomWidth, 0);
    const c3 = innerBottomRightRadiusX * (1.0 - KAPPA);
    const c4 = innerBottomRightRadiusY * (1.0 - KAPPA);
    const bottomRightYCoord = top + height - Math.max(borderBottomWidth, rbr);
    // Clip inner top left cap
    ctx.bezierCurveTo(left + width - borderRightWidth - c3, top + height - borderBottomWidth, left + width - borderRightWidth, top + height - borderBottomWidth - c4, left + width - borderRightWidth, bottomRightYCoord);
    ctx.lineTo(left + width, bottomRightYCoord);
    // Move down in case the margin exceedes the radius
    ctx.lineTo(left + width, top + height - rbr);
    // Ellipse coefficients outer top left cap
    const c5 = rbr * (1.0 - KAPPA);
    // Clip outer top left cap
    ctx.bezierCurveTo(left + width, top + height - c5, left + width - c5, top + height, left + width - rbr, top + height);
    ctx.closePath();
    ctx.clip();
    // Clip border bottom cap joins
    if (borderRightWidth) {
        const brSlope = borderBottomWidth / borderRightWidth;
        ctx.moveTo(left + width / 2, brSlope * (-width / 2) + top + height);
        ctx.lineTo(left + width, top + height);
        ctx.lineTo(left, top + height);
        ctx.lineTo(left, top);
        ctx.closePath();
        ctx.clip();
    }
    if (borderLeftWidth) {
        const trSlope = -borderBottomWidth / borderLeftWidth;
        ctx.moveTo(left + width / 2, trSlope * (width / 2) + top + height);
        ctx.lineTo(left, top + height);
        ctx.lineTo(left + width, top + height);
        ctx.lineTo(left + width, top);
        ctx.closePath();
        ctx.clip();
    }
};
const fillBorderBottom = (ctx, layout, style, rbl, rbr)=>{
    const { top, left, width, height } = layout;
    const { borderBottomColor, borderBottomStyle, borderBottomWidth, borderRightWidth, borderLeftWidth } = style;
    const c0 = rbl * (1.0 - KAPPA);
    const c1 = rbr * (1.0 - KAPPA);
    ctx.moveTo(left + width, top + height - rbr);
    ctx.bezierCurveTo(left + width, top + height - c1, left + width - c1, top + height, left + width - rbr, top + height);
    ctx.lineTo(left + rbl, top + height);
    ctx.bezierCurveTo(left + c0, top + height, left, top + height - c0, left, top + height - rbl);
    ctx.strokeColor(borderBottomColor);
    ctx.lineWidth(Math.max(borderBottomWidth, borderRightWidth, borderLeftWidth) * 2);
    if (borderBottomStyle === 'dashed') {
        ctx.dash(borderBottomWidth * 2, {
            space: borderBottomWidth * 1.2
        });
    } else if (borderBottomStyle === 'dotted') {
        ctx.dash(borderBottomWidth, {
            space: borderBottomWidth * 1.2
        });
    }
    ctx.stroke();
    ctx.undash();
};
const clipBorderLeft = (ctx, layout, style, rbl, rtl)=>{
    const { top, left, width, height } = layout;
    const { borderTopWidth, borderLeftWidth, borderBottomWidth } = style;
    // Clip outer left border edge
    ctx.moveTo(left, top + height - rbl);
    ctx.lineTo(left, top + rtl);
    // Ellipse coefficients outer top left cap
    const c0 = rtl * (1.0 - KAPPA);
    // Clip outer top left cap
    ctx.bezierCurveTo(left, top + c0, left + c0, top, left + rtl, top);
    // Move right in case the margin exceedes the radius
    const topLeftCoordX = left + Math.max(borderLeftWidth, rtl);
    ctx.lineTo(topLeftCoordX, top);
    // Clip inner top left cap
    ctx.lineTo(topLeftCoordX, top + borderTopWidth);
    // Ellipse coefficients inner top left cap
    const innerTopLeftRadiusX = Math.max(rtl - borderLeftWidth, 0);
    const innerTopLeftRadiusY = Math.max(rtl - borderTopWidth, 0);
    const c1 = innerTopLeftRadiusX * (1.0 - KAPPA);
    const c2 = innerTopLeftRadiusY * (1.0 - KAPPA);
    // Clip inner top right cap
    ctx.bezierCurveTo(left + borderLeftWidth + c1, top + borderTopWidth, left + borderLeftWidth, top + borderTopWidth + c2, left + borderLeftWidth, top + Math.max(rtl, borderTopWidth));
    // Clip inner left border edge
    ctx.lineTo(left + borderLeftWidth, top + height - Math.max(rbl, borderBottomWidth));
    // Ellipse coefficients inner bottom left cap
    const innerBottomLeftRadiusX = Math.max(rbl - borderLeftWidth, 0);
    const innerBottomLeftRadiusY = Math.max(rbl - borderBottomWidth, 0);
    const c3 = innerBottomLeftRadiusX * (1.0 - KAPPA);
    const c4 = innerBottomLeftRadiusY * (1.0 - KAPPA);
    const bottomLeftXCoord = left + Math.max(rbl, borderLeftWidth);
    // Clip inner top left cap
    ctx.bezierCurveTo(left + borderLeftWidth, top + height - borderBottomWidth - c4, left + borderLeftWidth + c3, top + height - borderBottomWidth, bottomLeftXCoord, top + height - borderBottomWidth);
    ctx.lineTo(bottomLeftXCoord, top + height);
    // Move left in case the margin exceedes the radius
    ctx.lineTo(left + rbl, top + height);
    // Ellipse coefficients outer top right cap
    const c5 = rbl * (1.0 - KAPPA);
    // Clip outer top right cap
    ctx.bezierCurveTo(left + c5, top + height, left, top + height - c5, left, top + height - rbl);
    ctx.closePath();
    ctx.clip();
    // Clip border right cap joins
    if (borderBottomWidth) {
        const trSlope = -borderBottomWidth / borderLeftWidth;
        ctx.moveTo(left + width / 2, trSlope * (width / 2) + top + height);
        ctx.lineTo(left, top + height);
        ctx.lineTo(left, top);
        ctx.lineTo(left + width, top);
        ctx.closePath();
        ctx.clip();
    }
    if (borderBottomWidth) {
        const trSlope = -borderTopWidth / borderLeftWidth;
        ctx.moveTo(left + width / 2, trSlope * (-width / 2) + top);
        ctx.lineTo(left, top);
        ctx.lineTo(left, top + height);
        ctx.lineTo(left + width, top + height);
        ctx.closePath();
        ctx.clip();
    }
};
const fillBorderLeft = (ctx, layout, style, rbl, rtl)=>{
    const { top, left, height } = layout;
    const { borderLeftColor, borderLeftStyle, borderLeftWidth, borderTopWidth, borderBottomWidth } = style;
    const c0 = rbl * (1.0 - KAPPA);
    const c1 = rtl * (1.0 - KAPPA);
    ctx.moveTo(left + rbl, top + height);
    ctx.bezierCurveTo(left + c0, top + height, left, top + height - c0, left, top + height - rbl);
    ctx.lineTo(left, top + rtl);
    ctx.bezierCurveTo(left, top + c1, left + c1, top, left + rtl, top);
    ctx.strokeColor(borderLeftColor);
    ctx.lineWidth(Math.max(borderLeftWidth, borderTopWidth, borderBottomWidth) * 2);
    if (borderLeftStyle === 'dashed') {
        ctx.dash(borderLeftWidth * 2, {
            space: borderLeftWidth * 1.2
        });
    } else if (borderLeftStyle === 'dotted') {
        ctx.dash(borderLeftWidth, {
            space: borderLeftWidth * 1.2
        });
    }
    ctx.stroke();
    ctx.undash();
};
const shouldRenderBorders = (node)=>node.box && (node.box.borderTopWidth || node.box.borderRightWidth || node.box.borderBottomWidth || node.box.borderLeftWidth);
const renderBorders = (ctx, node)=>{
    if (!node.box) return;
    if (!shouldRenderBorders(node)) return;
    const { width, height, borderTopWidth = 0, borderLeftWidth = 0, borderRightWidth = 0, borderBottomWidth = 0 } = node.box;
    const { opacity = 1, borderTopColor = 'black', borderTopStyle = 'solid', borderLeftColor = 'black', borderLeftStyle = 'solid', borderRightColor = 'black', borderRightStyle = 'solid', borderBottomColor = 'black', borderBottomStyle = 'solid' } = node.style;
    // @ts-expect-error this is always a number due to resolve border radius step
    const borderTopLeftRadius = node.style.borderTopLeftRadius || 0;
    // @ts-expect-error this is always a number due to resolve border radius step
    const borderTopRightRadius = node.style.borderTopRightRadius || 0;
    // @ts-expect-error this is always a number due to resolve border radius step
    const borderBottomLeftRadius = node.style.borderBottomLeftRadius || 0;
    // @ts-expect-error this is always a number due to resolve border radius step
    const borderBottomRightRadius = node.style.borderBottomRightRadius || 0;
    const style = {
        borderTopColor,
        borderTopWidth,
        borderTopStyle,
        borderLeftColor,
        borderLeftWidth,
        borderLeftStyle,
        borderRightColor,
        borderRightWidth,
        borderRightStyle,
        borderBottomColor,
        borderBottomWidth,
        borderBottomStyle
    };
    const rtr = Math.min(borderTopRightRadius, 0.5 * width, 0.5 * height);
    const rtl = Math.min(borderTopLeftRadius, 0.5 * width, 0.5 * height);
    const rbr = Math.min(borderBottomRightRadius, 0.5 * width, 0.5 * height);
    const rbl = Math.min(borderBottomLeftRadius, 0.5 * width, 0.5 * height);
    ctx.save();
    ctx.strokeOpacity(opacity);
    if (borderTopWidth) {
        ctx.save();
        clipBorderTop(ctx, node.box, style, rtr, rtl);
        fillBorderTop(ctx, node.box, style, rtr, rtl);
        ctx.restore();
    }
    if (borderRightWidth) {
        ctx.save();
        clipBorderRight(ctx, node.box, style, rtr, rbr);
        fillBorderRight(ctx, node.box, style, rtr, rbr);
        ctx.restore();
    }
    if (borderBottomWidth) {
        ctx.save();
        clipBorderBottom(ctx, node.box, style, rbl, rbr);
        fillBorderBottom(ctx, node.box, style, rbl, rbr);
        ctx.restore();
    }
    if (borderLeftWidth) {
        ctx.save();
        clipBorderLeft(ctx, node.box, style, rbl, rtl);
        fillBorderLeft(ctx, node.box, style, rbl, rtl);
        ctx.restore();
    }
    ctx.restore();
};
const drawBackground = (ctx, node)=>{
    if (!node.box) return;
    const { top, left, width, height } = node.box;
    const color = parseColor(node.style.backgroundColor);
    const nodeOpacity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(node.style?.opacity) ? 1 : node.style.opacity;
    const opacity = Math.min(color.opacity, nodeOpacity);
    ctx.fillOpacity(opacity).fillColor(color.value).rect(left, top, width, height).fill();
};
const renderBackground = (ctx, node)=>{
    const hasBackground = !!node.box && !!node.style?.backgroundColor;
    if (hasBackground) {
        ctx.save();
        clipNode(ctx, node);
        drawBackground(ctx, node);
        ctx.restore();
    }
};
const isString = (value)=>typeof value === 'string';
const isSrcId = (value)=>/^#.+/.test(value);
const renderLink = (ctx, node, src)=>{
    if (!src || !node.box) return;
    const isId = isSrcId(src);
    const method = isId ? 'goTo' : 'link';
    const value = isId ? src.slice(1) : src;
    const { top, left, width, height } = node.box;
    ctx[method](left, top, width, height, value);
};
const setLink = (ctx, node)=>{
    const props = node.props || {};
    if ('src' in props && isString(props.src)) return renderLink(ctx, node, props.src);
    if ('href' in props && isString(props.href)) return renderLink(ctx, node, props.href);
};
const setDestination = (ctx, node)=>{
    if (!node.box) return;
    if (!node.props) return;
    if ('id' in node.props) {
        ctx.addNamedDestination(node.props.id, 'XYZ', null, node.box.top, null);
    }
};
const clean = (options)=>{
    const opt = {
        ...options
    };
    // We need to ensure the elements are no present if not true
    Object.entries(opt).forEach((pair)=>{
        if (!pair[1]) {
            delete opt[pair[0]];
        }
    });
    return opt;
};
const parseCommonFormOptions = (node)=>{
    // Common Options
    return {
        required: node.props?.required || false,
        noExport: node.props?.noExport || false,
        readOnly: node.props?.readOnly || false,
        value: node.props?.value || undefined,
        defaultValue: node.props?.defaultValue || undefined
    };
};
const parseTextInputOptions = (node, fieldSet)=>{
    return clean({
        ...parseCommonFormOptions(node),
        parent: fieldSet || undefined,
        align: node.props?.align || 'left',
        multiline: node.props?.multiline || undefined,
        password: node.props?.password || false,
        noSpell: node.props?.noSpell || false,
        format: node.props?.format || undefined,
        fontSize: node.props?.fontSize || undefined,
        MaxLen: node.props?.maxLength || undefined
    });
};
const parseSelectAndListFieldOptions = (node)=>{
    return clean({
        ...parseCommonFormOptions(node),
        sort: node.props?.sort || false,
        edit: node.props?.edit || false,
        multiSelect: node.props?.multiSelect || false,
        noSpell: node.props?.noSpell || false,
        select: node.props?.select || [
            ''
        ]
    });
};
const getAppearance = (ctx, codepoint, width, height)=>{
    const appearance = ctx.ref({
        Type: 'XObject',
        Subtype: 'Form',
        BBox: [
            0,
            0,
            width,
            height
        ],
        Resources: {
            ProcSet: [
                'PDF',
                'Text',
                'ImageB',
                'ImageC',
                'ImageI'
            ],
            Font: {
                ZaDi: ctx._acroform.fonts.ZaDi
            }
        }
    });
    appearance.initDeflate();
    appearance.write(`/Tx BMC\nq\n/ZaDi ${height * 0.8} Tf\nBT\n${width * 0.45} ${height / 4} Td (${codepoint}) Tj\nET\nQ\nEMC`);
    appearance.end(null);
    return appearance;
};
const parseCheckboxOptions = (ctx, node, fieldSet)=>{
    const { width, height } = node.box || {};
    const onOption = node.props?.onState || 'Yes';
    const offOption = node.props?.offState || 'Off';
    const xMark = node.props?.xMark || false;
    if (!Object.prototype.hasOwnProperty.call(ctx._acroform.fonts, 'ZaDi')) {
        const ref = ctx.ref({
            Type: 'Font',
            Subtype: 'Type1',
            BaseFont: 'ZapfDingbats'
        });
        ctx._acroform.fonts.ZaDi = ref;
        ref.end(null);
    }
    const normalAppearance = {
        [onOption]: getAppearance(ctx, xMark ? '8' : '4', width, height),
        [offOption]: getAppearance(ctx, xMark ? ' ' : '8', width, height)
    };
    return clean({
        ...parseCommonFormOptions(node),
        backgroundColor: node.props?.backgroundColor || undefined,
        borderColor: node.props?.borderColor || undefined,
        parent: fieldSet || undefined,
        value: `/${node.props?.checked === true ? onOption : offOption}`,
        defaultValue: `/${node.props?.checked === true ? onOption : offOption}`,
        AS: node.props?.checked === true ? onOption : offOption,
        AP: {
            N: normalAppearance,
            D: normalAppearance
        }
    });
};
const renderTextInput = (ctx, node, options)=>{
    if (!node.box) return;
    const { top, left, width, height } = node.box;
    // Element's name
    const name = node.props?.name || '';
    const fieldSetOptions = options.fieldSets?.at(0);
    if (!ctx._root.data.AcroForm) {
        ctx.initForm();
    }
    ctx.formText(name, left, top, width, height, parseTextInputOptions(node, fieldSetOptions));
};
const renderSelect = (ctx, node)=>{
    if (!node.box) return;
    const { top, left, width, height } = node.box;
    // Element's name
    const name = node.props?.name || '';
    if (!ctx._root.data.AcroForm) {
        ctx.initForm();
    }
    ctx.formCombo(name, left, top, width, height, parseSelectAndListFieldOptions(node));
};
const renderFieldSet = (ctx, node, options)=>{
    const name = node.props?.name || '';
    if (!ctx._root.data.AcroForm) {
        ctx.initForm();
    }
    const formField = ctx.formField(name);
    const option = options;
    if (!option.fieldSets) {
        option.fieldSets = [
            formField
        ];
    } else {
        option.fieldSets.push(formField);
    }
};
const cleanUpFieldSet = (_ctx, _node, options)=>{
    options.fieldSets.pop();
};
const renderList = (ctx, node)=>{
    if (!node.box) return;
    const { top, left, width, height } = node.box || {};
    // Element's name
    const name = 'name' in node.props ? node.props.name || '' : '';
    if (!ctx._root.data.AcroForm) {
        ctx.initForm();
    }
    ctx.formList(name, left, top, width, height, parseSelectAndListFieldOptions(node));
};
const renderCheckbox = (ctx, node, options)=>{
    if (!node.box) return;
    const { top, left, width, height } = node.box;
    // Element's name
    const name = node.props?.name || '';
    const fieldSetOptions = options.fieldSets?.at(0);
    if (!ctx._root.data.AcroForm) {
        ctx.initForm();
    }
    ctx.formCheckbox(name, left, top, width, height, parseCheckboxOptions(ctx, node, fieldSetOptions));
};
const isRecursiveNode = (node)=>node.type !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"] && node.type !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Svg"];
const renderChildren = (ctx, node, options)=>{
    ctx.save();
    if (node.box) {
        ctx.translate(node.box.left, node.box.top);
    }
    const children = node.children || [];
    const renderChild = (child)=>renderNode(ctx, child, options);
    children.forEach(renderChild);
    ctx.restore();
};
const renderFns = {
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Text"]]: renderText,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Note"]]: renderNote,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Image"]]: renderImage,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FieldSet"]]: renderFieldSet,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TextInput"]]: renderTextInput,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Select"]]: renderSelect,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Checkbox"]]: renderCheckbox,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["List"]]: renderList,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Canvas"]]: renderCanvas,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Svg"]]: renderSvg,
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Link"]]: setLink
};
const cleanUpFns = {
    [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FieldSet"]]: cleanUpFieldSet
};
const renderNode = (ctx, node, options)=>{
    const overflowHidden = node.style?.overflow === 'hidden';
    const shouldRenderChildren = isRecursiveNode(node);
    if (node.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Page"]) renderPage(ctx, node);
    ctx.save();
    if (overflowHidden) clipNode(ctx, node);
    applyTransformations(ctx, node);
    renderBackground(ctx, node);
    renderBorders(ctx, node);
    const renderFn = renderFns[node.type];
    if (renderFn) renderFn(ctx, node, options);
    if (shouldRenderChildren) renderChildren(ctx, node, options);
    const cleanUpFn = cleanUpFns[node.type];
    if (cleanUpFn) cleanUpFn(ctx, node, options);
    setDestination(ctx, node);
    renderDebug(ctx, node);
    ctx.restore();
};
const addNodeBookmark = (ctx, node, pageNumber, registry)=>{
    if (!node.box) return;
    if (!node.props) return;
    if ('bookmark' in node.props && node.props.bookmark) {
        const bookmark = node.props.bookmark;
        const { title, parent, expanded, zoom, fit } = bookmark;
        const outline = registry[parent] || ctx.outline;
        const top = bookmark.top || node.box.top;
        const left = bookmark.left || node.box.left;
        const instance = outline.addItem(title, {
            pageNumber,
            expanded,
            top,
            left,
            zoom,
            fit
        });
        registry[bookmark.ref] = instance;
    }
    if (!node.children) return;
    node.children.forEach((child)=>addNodeBookmark(ctx, child, pageNumber, registry));
};
const addBookmarks = (ctx, root)=>{
    const registry = {};
    const pages = root.children || [];
    pages.forEach((page, i)=>{
        addNodeBookmark(ctx, page, i, registry);
    });
};
const render = (ctx, doc)=>{
    const pages = doc.children || [];
    const options = {
        imageCache: new Map(),
        fieldSets: []
    };
    pages.forEach((page)=>renderNode(ctx, page, options));
    addBookmarks(ctx, doc);
    ctx.end();
    return ctx;
};
;
}}),
"[project]/node_modules/@react-pdf/stylesheet/lib/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>resolveStyles),
    "flatten": (()=>flatten),
    "transformColor": (()=>transformColor)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/fns/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$media$2d$engine$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/media-engine/src/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$hsl$2d$to$2d$hex$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/hsl-to-hex/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$string$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/color-string/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postcss$2d$value$2d$parser$2f$lib$2f$parse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postcss-value-parser/lib/parse.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postcss$2d$value$2d$parser$2f$lib$2f$unit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/postcss-value-parser/lib/unit.js [app-client] (ecmascript)");
;
;
;
;
;
;
/**
 * Remove nil values from array
 *
 * @param array - Style array
 * @returns Style array without nils
 */ const compact = (array)=>array.filter(Boolean);
/**
 * Merges style objects array
 *
 * @param styles - Style array
 * @returns Merged style object
 */ const mergeStyles = (styles)=>styles.reduce((acc, style)=>{
        const s = Array.isArray(style) ? flatten(style) : style;
        Object.keys(s).forEach((key)=>{
            if (s[key] !== null && s[key] !== undefined) {
                acc[key] = s[key];
            }
        });
        return acc;
    }, {});
/**
 * Flattens an array of style objects, into one aggregated style object.
 *
 * @param styles - Style or style array
 * @returns Flattened style object
 */ const flatten = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["compose"])(mergeStyles, compact, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["castArray"]);
/**
 * Resolves media queries in styles object
 *
 * @param container - Container for which styles are resolved
 * @param style - Style description
 * @returns Resolved style object
 */ const resolveMediaQueries = (container, style)=>{
    return Object.keys(style).reduce((acc, key)=>{
        if (/@media/.test(key)) {
            return {
                ...acc,
                ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$media$2d$engine$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                    [key]: style[key]
                }, container)
            };
        }
        return {
            ...acc,
            [key]: style[key]
        };
    }, {});
};
const isRgb = (value)=>/rgba?/g.test(value);
const isHsl = (value)=>/hsla?/g.test(value);
/**
 * Transform rgb color to hexa
 *
 * @param value - Styles value
 * @returns Transformed value
 */ const parseRgb = (value)=>{
    const rgb = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$string$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get.rgb(value);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$string$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].to.hex(rgb);
};
/**
 * Transform Hsl color to hexa
 *
 * @param value - Styles value
 * @returns Transformed value
 */ const parseHsl = (value)=>{
    const hsl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$string$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get.hsl(value).map(Math.round);
    const hex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$hsl$2d$to$2d$hex$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(...hsl);
    return hex.toUpperCase();
};
/**
 * Transform given color to hexa
 *
 * @param value - Styles value
 * @returns Transformed value
 */ const transformColor = (value)=>{
    if (isRgb(value)) return parseRgb(value);
    if (isHsl(value)) return parseHsl(value);
    return value;
};
/**
 * Parses scalar value in value and unit pairs
 *
 * @param value - Scalar value
 * @returns Parsed value
 */ const parseValue = (value)=>{
    if (typeof value === 'number') return {
        value,
        unit: undefined
    };
    const match = /^(-?\d*\.?\d+)(in|mm|cm|pt|vh|vw|px|rem)?$/g.exec(value);
    return match ? {
        value: parseFloat(match[1]),
        unit: match[2] || 'pt'
    } : {
        value,
        unit: undefined
    };
};
/**
 * Transform given scalar value
 *
 * @param container
 * @param value - Styles value
 * @returns Transformed value
 */ const transformUnit = (container, value)=>{
    const scalar = parseValue(value);
    const outputDpi = 72;
    const inputDpi = container.dpi || 72;
    const mmFactor = 1 / 25.4 * outputDpi;
    const cmFactor = 1 / 2.54 * outputDpi;
    if (typeof scalar.value !== 'number') return scalar.value;
    switch(scalar.unit){
        case 'rem':
            return scalar.value * (container.remBase || 18);
        case 'in':
            return scalar.value * outputDpi;
        case 'mm':
            return scalar.value * mmFactor;
        case 'cm':
            return scalar.value * cmFactor;
        case 'vh':
            return scalar.value * (container.height / 100);
        case 'vw':
            return scalar.value * (container.width / 100);
        case 'px':
            return Math.round(scalar.value * (outputDpi / inputDpi));
        default:
            return scalar.value;
    }
};
const processNumberValue = (key, value)=>({
        [key]: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseFloat"])(value)
    });
const processUnitValue = (key, value, container)=>({
        [key]: transformUnit(container, value)
    });
const processColorValue = (key, value)=>{
    const result = {
        [key]: transformColor(value)
    };
    return result;
};
const processNoopValue = (key, value)=>({
        [key]: value
    });
const BORDER_SHORTHAND_REGEX = /(-?\d+(\.\d+)?(in|mm|cm|pt|vw|vh|px|rem)?)\s(\S+)\s(.+)/;
const matchBorderShorthand = (value)=>value.match(BORDER_SHORTHAND_REGEX) || [];
const resolveBorderShorthand = (key, value, container)=>{
    const match = matchBorderShorthand(`${value}`);
    if ("TURBOPACK compile-time truthy", 1) {
        const widthMatch = match[1] || value;
        const styleMatch = match[4] || value;
        const colorMatch = match[5] || value;
        const style = styleMatch;
        const color = colorMatch ? transformColor(colorMatch) : undefined;
        const width = widthMatch ? transformUnit(container, widthMatch) : undefined;
        if (key.match(/(Top|Right|Bottom|Left)$/)) {
            return {
                [`${key}Color`]: color,
                [`${key}Style`]: style,
                [`${key}Width`]: width
            };
        }
        if (key.match(/Color$/)) {
            return {
                borderTopColor: color,
                borderRightColor: color,
                borderBottomColor: color,
                borderLeftColor: color
            };
        }
        if (key.match(/Style$/)) {
            if (typeof style === 'number') throw new Error(`Invalid border style: ${style}`);
            return {
                borderTopStyle: style,
                borderRightStyle: style,
                borderBottomStyle: style,
                borderLeftStyle: style
            };
        }
        if (key.match(/Width$/)) {
            if (typeof width !== 'number') throw new Error(`Invalid border width: ${width}`);
            return {
                borderTopWidth: width,
                borderRightWidth: width,
                borderBottomWidth: width,
                borderLeftWidth: width
            };
        }
        if (key.match(/Radius$/)) {
            const radius = value ? transformUnit(container, value) : undefined;
            if (typeof radius !== 'number') throw new Error(`Invalid border radius: ${radius}`);
            return {
                borderTopLeftRadius: radius,
                borderTopRightRadius: radius,
                borderBottomRightRadius: radius,
                borderBottomLeftRadius: radius
            };
        }
        if (typeof width !== 'number') throw new Error(`Invalid border width: ${width}`);
        if (typeof style === 'number') throw new Error(`Invalid border style: ${style}`);
        return {
            borderTopColor: color,
            borderTopStyle: style,
            borderTopWidth: width,
            borderRightColor: color,
            borderRightStyle: style,
            borderRightWidth: width,
            borderBottomColor: color,
            borderBottomStyle: style,
            borderBottomWidth: width,
            borderLeftColor: color,
            borderLeftStyle: style,
            borderLeftWidth: width
        };
    }
    "TURBOPACK unreachable";
};
const handlers$b = {
    border: resolveBorderShorthand,
    borderBottom: resolveBorderShorthand,
    borderBottomColor: processColorValue,
    borderBottomLeftRadius: processUnitValue,
    borderBottomRightRadius: processUnitValue,
    borderBottomStyle: processNoopValue,
    borderBottomWidth: processUnitValue,
    borderColor: resolveBorderShorthand,
    borderLeft: resolveBorderShorthand,
    borderLeftColor: processColorValue,
    borderLeftStyle: processNoopValue,
    borderLeftWidth: processUnitValue,
    borderRadius: resolveBorderShorthand,
    borderRight: resolveBorderShorthand,
    borderRightColor: processColorValue,
    borderRightStyle: processNoopValue,
    borderRightWidth: processUnitValue,
    borderStyle: resolveBorderShorthand,
    borderTop: resolveBorderShorthand,
    borderTopColor: processColorValue,
    borderTopLeftRadius: processUnitValue,
    borderTopRightRadius: processUnitValue,
    borderTopStyle: processNoopValue,
    borderTopWidth: processUnitValue,
    borderWidth: resolveBorderShorthand
};
const handlers$a = {
    backgroundColor: processColorValue,
    color: processColorValue,
    opacity: processNumberValue
};
const handlers$9 = {
    height: processUnitValue,
    maxHeight: processUnitValue,
    maxWidth: processUnitValue,
    minHeight: processUnitValue,
    minWidth: processUnitValue,
    width: processUnitValue
};
// https://developer.mozilla.org/en-US/docs/Web/CSS/flex#values
// TODO: change flex defaults to [0, 1, 'auto'] as in spec in next major release
const flexDefaults = [
    1,
    1,
    0
];
const flexAuto = [
    1,
    1,
    'auto'
];
const processFlexShorthand = (key, value, container)=>{
    let defaults = flexDefaults;
    let matches = [];
    if (value === 'auto') {
        defaults = flexAuto;
    } else {
        matches = `${value}`.split(' ');
    }
    const flexGrow = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseFloat"])(matches[0] || defaults[0]);
    const flexShrink = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseFloat"])(matches[1] || defaults[1]);
    const flexBasis = transformUnit(container, matches[2] || defaults[2]);
    return {
        flexGrow,
        flexShrink,
        flexBasis
    };
};
const handlers$8 = {
    alignContent: processNoopValue,
    alignItems: processNoopValue,
    alignSelf: processNoopValue,
    flex: processFlexShorthand,
    flexBasis: processUnitValue,
    flexDirection: processNoopValue,
    flexFlow: processNoopValue,
    flexGrow: processNumberValue,
    flexShrink: processNumberValue,
    flexWrap: processNoopValue,
    justifyContent: processNoopValue,
    justifySelf: processNoopValue
};
const processGapShorthand = (key, value, container)=>{
    const match = `${value}`.split(' ');
    const rowGap = transformUnit(container, match?.[0] || value);
    const columnGap = transformUnit(container, match?.[1] || value);
    return {
        rowGap,
        columnGap
    };
};
const handlers$7 = {
    gap: processGapShorthand,
    columnGap: processUnitValue,
    rowGap: processUnitValue
};
const handlers$6 = {
    aspectRatio: processNumberValue,
    bottom: processUnitValue,
    display: processNoopValue,
    left: processUnitValue,
    position: processNoopValue,
    right: processUnitValue,
    top: processUnitValue,
    overflow: processNoopValue,
    zIndex: processNumberValue
};
const BOX_MODEL_UNITS = 'px,in,mm,cm,pt,%,vw,vh';
const logError = (style, value)=>{
    const name = style.toString();
    // eslint-disable-next-line no-console
    console.error(`
    @react-pdf/stylesheet parsing error:
    ${name}: ${value},
    ${' '.repeat(name.length + 2)}^
    Unsupported ${name} value format
  `);
};
/**
 * @param options
 * @param [options.expandsTo]
 * @param [options.maxValues]
 * @param [options.autoSupported]
 */ const expandBoxModel = ({ expandsTo, maxValues = 1, autoSupported = false } = {})=>(model, value, container)=>{
        const nodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postcss$2d$value$2d$parser$2f$lib$2f$parse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${value}`);
        const parts = [];
        for(let i = 0; i < nodes.length; i++){
            const node = nodes[i];
            // value contains `calc`, `url` or other css function
            // `,`, `/` or strings that unsupported by margin and padding
            if (node.type === 'function' || node.type === 'string' || node.type === 'div') {
                logError(model, value);
                return {};
            }
            if (node.type === 'word') {
                if (node.value === 'auto' && autoSupported) {
                    parts.push(node.value);
                } else {
                    const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$postcss$2d$value$2d$parser$2f$lib$2f$unit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(node.value);
                    // when unit isn't specified this condition is true
                    if (result && BOX_MODEL_UNITS.includes(result.unit)) {
                        parts.push(node.value);
                    } else {
                        logError(model, value);
                        return {};
                    }
                }
            }
        }
        // checks that we have enough parsed values
        if (parts.length > maxValues) {
            logError(model, value);
            return {};
        }
        const first = transformUnit(container, parts[0]);
        if (expandsTo) {
            const second = transformUnit(container, parts[1] || parts[0]);
            const third = transformUnit(container, parts[2] || parts[0]);
            const fourth = transformUnit(container, parts[3] || parts[1] || parts[0]);
            return expandsTo({
                first,
                second,
                third,
                fourth
            });
        }
        return {
            [model]: first
        };
    };
const processMargin = expandBoxModel({
    expandsTo: ({ first, second, third, fourth })=>({
            marginTop: first,
            marginRight: second,
            marginBottom: third,
            marginLeft: fourth
        }),
    maxValues: 4,
    autoSupported: true
});
const processMarginVertical = expandBoxModel({
    expandsTo: ({ first, second })=>({
            marginTop: first,
            marginBottom: second
        }),
    maxValues: 2,
    autoSupported: true
});
const processMarginHorizontal = expandBoxModel({
    expandsTo: ({ first, second })=>({
            marginRight: first,
            marginLeft: second
        }),
    maxValues: 2,
    autoSupported: true
});
const processMarginSingle = expandBoxModel({
    autoSupported: true
});
const handlers$5 = {
    margin: processMargin,
    marginBottom: processMarginSingle,
    marginHorizontal: processMarginHorizontal,
    marginLeft: processMarginSingle,
    marginRight: processMarginSingle,
    marginTop: processMarginSingle,
    marginVertical: processMarginVertical
};
const processPadding = expandBoxModel({
    expandsTo: ({ first, second, third, fourth })=>({
            paddingTop: first,
            paddingRight: second,
            paddingBottom: third,
            paddingLeft: fourth
        }),
    maxValues: 4
});
const processPaddingVertical = expandBoxModel({
    expandsTo: ({ first, second })=>({
            paddingTop: first,
            paddingBottom: second
        }),
    maxValues: 2
});
const processPaddingHorizontal = expandBoxModel({
    expandsTo: ({ first, second })=>({
            paddingRight: first,
            paddingLeft: second
        }),
    maxValues: 2
});
const processPaddingSingle = expandBoxModel();
const handlers$4 = {
    padding: processPadding,
    paddingBottom: processPaddingSingle,
    paddingHorizontal: processPaddingHorizontal,
    paddingLeft: processPaddingSingle,
    paddingRight: processPaddingSingle,
    paddingTop: processPaddingSingle,
    paddingVertical: processPaddingVertical
};
const offsetKeyword = (value)=>{
    switch(value){
        case 'top':
        case 'left':
            return '0%';
        case 'right':
        case 'bottom':
            return '100%';
        case 'center':
            return '50%';
        default:
            return value;
    }
};
const processObjectPosition = (key, value, container)=>{
    const match = `${value}`.split(' ');
    const objectPositionX = offsetKeyword(transformUnit(container, match?.[0] || value));
    const objectPositionY = offsetKeyword(transformUnit(container, match?.[1] || value));
    return {
        objectPositionX,
        objectPositionY
    };
};
const processObjectPositionValue = (key, value, container)=>({
        [key]: offsetKeyword(transformUnit(container, value))
    });
const handlers$3 = {
    objectPosition: processObjectPosition,
    objectPositionX: processObjectPositionValue,
    objectPositionY: processObjectPositionValue,
    objectFit: processNoopValue
};
const castInt = (value)=>{
    if (typeof value === 'number') return value;
    return parseInt(value, 10);
};
const FONT_WEIGHTS = {
    thin: 100,
    hairline: 100,
    ultralight: 200,
    extralight: 200,
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    demibold: 600,
    bold: 700,
    ultrabold: 800,
    extrabold: 800,
    heavy: 900,
    black: 900
};
const transformFontWeight = (value)=>{
    if (!value) return FONT_WEIGHTS.normal;
    if (typeof value === 'number') return value;
    const lv = value.toLowerCase();
    if (FONT_WEIGHTS[lv]) return FONT_WEIGHTS[lv];
    return castInt(value);
};
const processFontWeight = (key, value)=>{
    return {
        [key]: transformFontWeight(value)
    };
};
const transformLineHeight = (value, styles, container)=>{
    if (value === '') return value;
    const fontSize = transformUnit(container, styles.fontSize || 18);
    const lineHeight = transformUnit(container, value);
    // Percent values: use this number multiplied by the element's font size
    const { percent } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["matchPercent"])(lineHeight) || {};
    if (percent) return percent * fontSize;
    // Unitless values: use this number multiplied by the element's font size
    return isNaN(value) ? lineHeight : lineHeight * fontSize;
};
const processLineHeight = (key, value, container, styles)=>{
    return {
        [key]: transformLineHeight(value, styles, container)
    };
};
const handlers$2 = {
    direction: processNoopValue,
    fontFamily: processNoopValue,
    fontSize: processUnitValue,
    fontStyle: processNoopValue,
    fontWeight: processFontWeight,
    letterSpacing: processUnitValue,
    lineHeight: processLineHeight,
    maxLines: processNumberValue,
    textAlign: processNoopValue,
    textDecoration: processNoopValue,
    textDecorationColor: processColorValue,
    textDecorationStyle: processNoopValue,
    textIndent: processNoopValue,
    textOverflow: processNoopValue,
    textTransform: processNoopValue,
    verticalAlign: processNoopValue
};
const matchNumber = (value)=>typeof value === 'string' && /^-?\d*\.?\d*$/.test(value);
const castFloat = (value)=>{
    if (typeof value !== 'string') return value;
    if (matchNumber(value)) return parseFloat(value);
    return value;
};
const parse = (transformString)=>{
    const transforms = transformString.trim().split(/\)[ ,]|\)/);
    // Handle "initial", "inherit", "unset".
    if (transforms.length === 1) {
        return [
            [
                transforms[0],
                true
            ]
        ];
    }
    const parsed = [];
    for(let i = 0; i < transforms.length; i += 1){
        const transform = transforms[i];
        if (transform) {
            const [name, rawValue] = transform.split('(');
            const splitChar = rawValue.indexOf(',') >= 0 ? ',' : ' ';
            const value = rawValue.split(splitChar).map((val)=>val.trim());
            parsed.push({
                operation: name.trim(),
                value
            });
        }
    }
    return parsed;
};
const parseAngle = (value)=>{
    const unitsRegexp = /(-?\d*\.?\d*)(\w*)?/i;
    const [, angle, unit] = unitsRegexp.exec(value);
    const number = Number.parseFloat(angle);
    return unit === 'rad' ? number * 180 / Math.PI : number;
};
const normalizeTransformOperation = ({ operation, value })=>{
    switch(operation){
        case 'scale':
            {
                const [scaleX, scaleY = scaleX] = value.map((num)=>Number.parseFloat(num));
                return {
                    operation: 'scale',
                    value: [
                        scaleX,
                        scaleY
                    ]
                };
            }
        case 'scaleX':
            {
                return {
                    operation: 'scale',
                    value: [
                        Number.parseFloat(value),
                        1
                    ]
                };
            }
        case 'scaleY':
            {
                return {
                    operation: 'scale',
                    value: [
                        1,
                        Number.parseFloat(value)
                    ]
                };
            }
        case 'rotate':
            {
                return {
                    operation: 'rotate',
                    value: [
                        parseAngle(value)
                    ]
                };
            }
        case 'translate':
            {
                return {
                    operation: 'translate',
                    value: value.map((num)=>Number.parseFloat(num))
                };
            }
        case 'translateX':
            {
                return {
                    operation: 'translate',
                    value: [
                        Number.parseFloat(value),
                        0
                    ]
                };
            }
        case 'translateY':
            {
                return {
                    operation: 'translate',
                    value: [
                        0,
                        Number.parseFloat(value)
                    ]
                };
            }
        case 'skew':
            {
                return {
                    operation: 'skew',
                    value: value.map(parseAngle)
                };
            }
        case 'skewX':
            {
                return {
                    operation: 'skew',
                    value: [
                        parseAngle(value),
                        0
                    ]
                };
            }
        case 'skewY':
            {
                return {
                    operation: 'skew',
                    value: [
                        0,
                        parseAngle(value)
                    ]
                };
            }
        default:
            {
                return {
                    operation,
                    value: value.map((num)=>Number.parseFloat(num))
                };
            }
    }
};
const normalize = (operations)=>{
    return operations.map((operation)=>normalizeTransformOperation(operation));
};
const processTransform = (key, value)=>{
    if (typeof value !== 'string') return {
        [key]: value
    };
    return {
        [key]: normalize(parse(value))
    };
};
const Y_AXIS_SHORTHANDS = {
    top: true,
    bottom: true
};
const sortTransformOriginPair = (a, b)=>{
    if (Y_AXIS_SHORTHANDS[a]) return 1;
    if (Y_AXIS_SHORTHANDS[b]) return -1;
    return 0;
};
const getTransformOriginPair = (values)=>{
    if (!values || values.length === 0) return [
        'center',
        'center'
    ];
    const pair = values.length === 1 ? [
        values[0],
        'center'
    ] : values;
    return pair.sort(sortTransformOriginPair);
};
// Transforms shorthand transformOrigin values
const processTransformOriginShorthand = (key, value, container)=>{
    const match = `${value}`.split(' ');
    const pair = getTransformOriginPair(match);
    const transformOriginX = transformUnit(container, pair[0]);
    const transformOriginY = transformUnit(container, pair[1]);
    return {
        transformOriginX: offsetKeyword(transformOriginX) || castFloat(transformOriginX),
        transformOriginY: offsetKeyword(transformOriginY) || castFloat(transformOriginY)
    };
};
const processTransformOriginValue = (key, value, container)=>{
    const v = transformUnit(container, value);
    return {
        [key]: offsetKeyword(v) || castFloat(v)
    };
};
const handlers$1 = {
    transform: processTransform,
    gradientTransform: processTransform,
    transformOrigin: processTransformOriginShorthand,
    transformOriginX: processTransformOriginValue,
    transformOriginY: processTransformOriginValue
};
const handlers = {
    fill: processColorValue,
    stroke: processColorValue,
    strokeDasharray: processNoopValue,
    strokeWidth: processUnitValue,
    fillOpacity: processNumberValue,
    strokeOpacity: processNumberValue,
    fillRule: processNoopValue,
    textAnchor: processNoopValue,
    strokeLinecap: processNoopValue,
    strokeLinejoin: processNoopValue,
    visibility: processNoopValue,
    clipPath: processNoopValue,
    dominantBaseline: processNoopValue
};
const shorthands = {
    ...handlers$b,
    ...handlers$a,
    ...handlers$9,
    ...handlers$8,
    ...handlers$7,
    ...handlers$6,
    ...handlers$5,
    ...handlers$4,
    ...handlers$3,
    ...handlers$2,
    ...handlers$1,
    ...handlers
};
/**
 * Expand the shorthand properties.
 *
 * @param style - Style object
 * @returns Expanded style object
 */ const resolve = (container)=>(style)=>{
        const propsArray = Object.keys(style);
        const resolvedStyle = {};
        for(let i = 0; i < propsArray.length; i += 1){
            const key = propsArray[i];
            const value = style[key];
            if (!shorthands[key]) {
                resolvedStyle[key] = value;
                continue;
            }
            const resolved = shorthands[key](key, value, container, style);
            const keys = Object.keys(resolved);
            for(let j = 0; j < keys.length; j += 1){
                const propName = keys[j];
                const propValue = resolved[propName];
                resolvedStyle[propName] = propValue;
            }
        }
        return resolvedStyle;
    };
/**
 * Resolves styles
 *
 * @param container
 * @param style - Style
 * @returns Resolved style
 */ const resolveStyles = (container, style)=>{
    const computeMediaQueries = (value)=>resolveMediaQueries(container, value);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["compose"])(resolve(container), computeMediaQueries, flatten)(style);
};
;
}}),
"[project]/node_modules/@react-pdf/textkit/lib/textkit.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "bidi": (()=>bidiEngine),
    "default": (()=>layoutEngine),
    "fontSubstitution": (()=>fontSubstitution),
    "fromFragments": (()=>fromFragments),
    "justification": (()=>justification),
    "linebreaker": (()=>linebreaker),
    "scriptItemizer": (()=>scriptItemizer),
    "textDecoration": (()=>textDecoration),
    "wordHyphenation": (()=>wordHyphenation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/fns/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bidi$2d$js$2f$dist$2f$bidi$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bidi-js/dist/bidi.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$unicode$2d$properties$2f$dist$2f$module$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/unicode-properties/dist/module.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$hyphen$2f$hyphen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/hyphen/hyphen.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$hyphen$2f$patterns$2f$en$2d$us$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/hyphen/patterns/en-us.js [app-client] (ecmascript)");
;
;
;
;
;
/**
 * Create attributed string from text fragments
 *
 * @param fragments - Fragments
 * @returns Attributed string
 */ const fromFragments = (fragments)=>{
    let offset = 0;
    let string = '';
    const runs = [];
    fragments.forEach((fragment)=>{
        string += fragment.string;
        runs.push({
            ...fragment,
            start: offset,
            end: offset + fragment.string.length,
            attributes: fragment.attributes || {}
        });
        offset += fragment.string.length;
    });
    return {
        string,
        runs
    };
};
/**
 * Default word hyphenation engine used when no one provided.
 * Does not perform word hyphenation at all
 *
 * @param word
 * @returns Same word
 */ const defaultHyphenationEngine = (word)=>[
        word
    ];
/**
 * Wrap words of attribute string
 *
 * @param engines layout engines
 * @param options layout options
 */ const wrapWords = (engines = {}, options = {})=>{
    /**
     * @param attributedString - Attributed string
     * @returns Attributed string including syllables
     */ return (attributedString)=>{
        const syllables = [];
        const fragments = [];
        const hyphenateWord = options.hyphenationCallback || engines.wordHyphenation?.() || defaultHyphenationEngine;
        for(let i = 0; i < attributedString.runs.length; i += 1){
            let string = '';
            const run = attributedString.runs[i];
            const words = attributedString.string.slice(run.start, run.end).split(/([ ]+)/g).filter(Boolean);
            for(let j = 0; j < words.length; j += 1){
                const word = words[j];
                const parts = hyphenateWord(word);
                syllables.push(...parts);
                string += parts.join('');
            }
            fragments.push({
                ...run,
                string
            });
        }
        const result = {
            ...fromFragments(fragments),
            syllables
        };
        return result;
    };
};
/**
 * Clone rect
 *
 * @param rect - Rect
 * @returns Cloned rect
 */ const copy = (rect)=>{
    return Object.assign({}, rect);
};
/**
 * Partition rect in two in the vertical direction
 *
 * @param rect - Rect
 * @param height - Height
 * @returns Partitioned rects
 */ const partition = (rect, height)=>{
    const a = Object.assign({}, rect, {
        height
    });
    const b = Object.assign({}, rect, {
        y: rect.y + height,
        height: rect.height - height
    });
    return [
        a,
        b
    ];
};
/**
 * Crop upper section of rect
 *
 * @param height - Height
 * @param rect - Rect
 * @returns Cropped rect
 */ const crop = (height, rect)=>{
    const [, result] = partition(rect, height);
    return result;
};
/**
 * Get paragraph block height
 *
 * @param paragraph - Paragraph
 * @returns Paragraph block height
 */ const height$2 = (paragraph)=>{
    return paragraph.reduce((acc, block)=>acc + block.box.height, 0);
};
/**
 * Calculate run scale
 *
 * @param run - Run
 * @returns Scale
 */ const calculateScale = (run)=>{
    const attributes = run.attributes || {};
    const fontSize = attributes.fontSize || 12;
    const font = attributes.font;
    const unitsPerEm = typeof font === 'string' ? null : font?.[0]?.unitsPerEm;
    return unitsPerEm ? fontSize / unitsPerEm : 0;
};
/**
 * Get run scale
 *
 * @param  run
 * @returns Scale
 */ const scale = (run)=>{
    return run.attributes?.scale || calculateScale(run);
};
/**
 * Get ligature offset by index
 *
 * Ex. ffi ligature
 *
 *   glyphs:         l  o  f  f  i  m
 *   glyphIndices:   0  1  2  2  2  3
 *   offset:         0  0  0  1  2  0
 *
 * @param index
 * @param run - Run
 * @returns Ligature offset
 */ const offset = (index, run)=>{
    if (!run) return 0;
    const glyphIndices = run.glyphIndices || [];
    const value = glyphIndices[index];
    return glyphIndices.slice(0, index).filter((i)=>i === value).length;
};
/**
 * Get run font
 *
 * @param run - Run
 * @returns Font
 */ const getFont = (run)=>{
    return run.attributes?.font?.[0] || null;
};
/**
 * Slice glyph between codePoints range
 * Util for breaking ligatures
 *
 * @param start - Start code point index
 * @param end - End code point index
 * @param font - Font to generate new glyph
 * @param glyph - Glyph to be sliced
 * @returns Sliced glyph parts
 */ const slice$2 = (start, end, font, glyph)=>{
    if (!glyph) return [];
    if (start === end) return [];
    if (start === 0 && end === glyph.codePoints.length) return [
        glyph
    ];
    const codePoints = glyph.codePoints.slice(start, end);
    const string = String.fromCodePoint(...codePoints);
    // passing LTR To force fontkit to not reverse the string
    return font ? font.layout(string, undefined, undefined, undefined, 'ltr').glyphs : [
        glyph
    ];
};
/**
 * Return glyph index at string index, if glyph indices present.
 * Otherwise return string index
 *
 * @param index - Index
 * @param run - Run
 * @returns Glyph index
 */ const glyphIndexAt = (index, run)=>{
    const result = run?.glyphIndices?.[index];
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(result) ? index : result;
};
/**
 * Returns new array starting with zero, and keeping same relation between consecutive values
 *
 * @param array - List
 * @returns Normalized array
 */ const normalize = (array)=>{
    const head = array[0];
    return array.map((value)=>value - head);
};
/**
 * Slice run between glyph indices range
 *
 * @param start - Glyph index
 * @param end - Glyph index
 * @param run - Run
 * @returns Sliced run
 */ const slice$1 = (start, end, run)=>{
    const runScale = scale(run);
    const font = getFont(run);
    // Get glyph start and end indices
    const startIndex = glyphIndexAt(start, run);
    const endIndex = glyphIndexAt(end, run);
    // Get start and end glyph
    const startGlyph = run.glyphs?.[startIndex];
    const endGlyph = run.glyphs?.[endIndex];
    // Get start ligature chunks (if any)
    const startOffset = offset(start, run);
    const startGlyphs = startOffset > 0 ? slice$2(startOffset, Infinity, font, startGlyph) : [];
    // Get end ligature chunks (if any)
    const endOffset = offset(end, run);
    const endGlyphs = slice$2(0, endOffset, font, endGlyph);
    // Compute new glyphs
    const sliceStart = startIndex + Math.min(1, startOffset);
    const glyphs = (run.glyphs || []).slice(sliceStart, endIndex);
    // Compute new positions
    const glyphPosition = (g)=>({
            xAdvance: g.advanceWidth * runScale,
            yAdvance: 0,
            xOffset: 0,
            yOffset: 0
        });
    const startPositions = startGlyphs.map(glyphPosition);
    const positions = (run.positions || []).slice(sliceStart, endIndex);
    const endPositions = endGlyphs.map(glyphPosition);
    return Object.assign({}, run, {
        start: run.start + start,
        end: Math.min(run.end, run.start + end),
        glyphIndices: normalize((run.glyphIndices || []).slice(start, end)),
        glyphs: [
            startGlyphs,
            glyphs,
            endGlyphs
        ].flat(),
        positions: [
            startPositions,
            positions,
            endPositions
        ].flat()
    });
};
/**
 * Get run index that contains passed index
 *
 * @param index - Index
 * @param runs - Runs
 * @returns Run index
 */ const runIndexAt$1 = (index, runs)=>{
    if (!runs) return -1;
    return runs.findIndex((run)=>run.start <= index && index < run.end);
};
/**
 * Filter runs contained between start and end
 *
 * @param start
 * @param end
 * @param runs
 * @returns Filtered runs
 */ const filter = (start, end, runs)=>{
    const startIndex = runIndexAt$1(start, runs);
    const endIndex = Math.max(runIndexAt$1(end - 1, runs), startIndex);
    return runs.slice(startIndex, endIndex + 1);
};
/**
 * Subtract scalar to run
 *
 * @param index - Scalar
 * @param run - Run
 * @returns Subtracted run
 */ const subtract = (index, run)=>{
    const start = run.start - index;
    const end = run.end - index;
    return Object.assign({}, run, {
        start,
        end
    });
};
/**
 * Slice array of runs
 *
 * @param start - Offset
 * @param end - Offset
 * @param runs
 * @returns Sliced runs
 */ const sliceRuns = (start, end, runs)=>{
    const sliceFirstRun = (a)=>slice$1(start - a.start, end - a.start, a);
    const sliceLastRun = (a)=>slice$1(0, end - a.start, a);
    return runs.map((run, i)=>{
        let result = run;
        const isFirst = i === 0;
        const isLast = !isFirst && i === runs.length - 1;
        if (isFirst) result = sliceFirstRun(run);
        if (isLast) result = sliceLastRun(run);
        return subtract(start, result);
    });
};
/**
 * Slice attributed string between two indices
 *
 * @param start - Offset
 * @param end - Offset
 * @param attributedString - Attributed string
 * @returns Attributed string
 */ const slice = (start, end, attributedString)=>{
    if (attributedString.string.length === 0) return attributedString;
    const string = attributedString.string.slice(start, end);
    const filteredRuns = filter(start, end, attributedString.runs);
    const slicedRuns = sliceRuns(start, end, filteredRuns);
    return Object.assign({}, attributedString, {
        string,
        runs: slicedRuns
    });
};
const findCharIndex = (string)=>{
    return string.search(/\S/g);
};
const findLastCharIndex = (string)=>{
    const match = string.match(/\S/g);
    return match ? string.lastIndexOf(match[match.length - 1]) : -1;
};
/**
 * Removes (strips) whitespace from both ends of the attributted string.
 *
 * @param attributedString - Attributed string
 * @returns Attributed string
 */ const trim = (attributedString)=>{
    const start = findCharIndex(attributedString.string);
    const end = findLastCharIndex(attributedString.string);
    return slice(start, end + 1, attributedString);
};
/**
 * Returns empty run
 *
 * @returns Empty run
 */ const empty$1 = ()=>{
    return {
        start: 0,
        end: 0,
        glyphIndices: [],
        glyphs: [],
        positions: [],
        attributes: {}
    };
};
/**
 * Check if value is a number
 *
 * @param value - Value to check
 * @returns Whether value is a number
 */ const isNumber = (value)=>{
    return typeof value === 'number';
};
/**
 * Append glyph indices with given length
 *
 * Ex. appendIndices(3, [0, 1, 2, 2]) => [0, 1, 2, 2, 3, 3, 3]
 *
 * @param length - Length
 * @param indices - Glyph indices
 * @returns Extended glyph indices
 */ const appendIndices = (length, indices)=>{
    const lastIndex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["last"])(indices);
    const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(lastIndex) ? 0 : lastIndex + 1;
    const newIndices = Array(length).fill(value);
    return indices.concat(newIndices);
};
/**
 * Get glyph for a given code point
 *
 * @param value - CodePoint
 * @param font - Font
 * @returns Glyph
 * */ const fromCodePoint = (value, font)=>{
    if (typeof font === 'string') return null;
    return font && value ? font.glyphForCodePoint(value) : null;
};
/**
 * Append glyph to run
 *
 * @param glyph - Glyph
 * @param run - Run
 * @returns Run with glyph
 */ const appendGlyph = (glyph, run)=>{
    const glyphLength = glyph.codePoints?.length || 0;
    const end = run.end + glyphLength;
    const glyphs = run.glyphs.concat(glyph);
    const glyphIndices = appendIndices(glyphLength, run.glyphIndices);
    if (!run.positions) return Object.assign({}, run, {
        end,
        glyphs,
        glyphIndices
    });
    const positions = run.positions.concat({
        xAdvance: glyph.advanceWidth * scale(run),
        yAdvance: 0,
        xOffset: 0,
        yOffset: 0
    });
    return Object.assign({}, run, {
        end,
        glyphs,
        glyphIndices,
        positions
    });
};
/**
 * Append glyph or code point to run
 *
 * @param value - Glyph or codePoint
 * @param run - Run
 * @returns Run with glyph
 */ const append$1 = (value, run)=>{
    if (!value) return run;
    const font = getFont(run);
    const glyph = isNumber(value) ? fromCodePoint(value, font) : value;
    return appendGlyph(glyph, run);
};
/**
 * Get string from array of code points
 *
 * @param codePoints - Points
 * @returns String
 */ const stringFromCodePoints = (codePoints)=>{
    return String.fromCodePoint(...codePoints || []);
};
/**
 * Append glyph into last run of attributed string
 *
 * @param glyph - Glyph or code point
 * @param attributedString - Attributed string
 * @returns Attributed string with new glyph
 */ const append = (glyph, attributedString)=>{
    const codePoints = typeof glyph === 'number' ? [
        glyph
    ] : glyph?.codePoints;
    const codePointsString = stringFromCodePoints(codePoints || []);
    const string = attributedString.string + codePointsString;
    const firstRuns = attributedString.runs.slice(0, -1);
    const lastRun = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["last"])(attributedString.runs) || empty$1();
    const runs = firstRuns.concat(append$1(glyph, lastRun));
    return Object.assign({}, attributedString, {
        string,
        runs
    });
};
const ELLIPSIS_UNICODE = 8230;
const ELLIPSIS_STRING = String.fromCharCode(ELLIPSIS_UNICODE);
/**
 * Get ellipsis codepoint. This may be different in standard and embedded fonts
 *
 * @param font
 * @returns Ellipsis codepoint
 */ const getEllipsisCodePoint = (font)=>{
    if (!font.encode) return ELLIPSIS_UNICODE;
    const [codePoints] = font.encode(ELLIPSIS_STRING);
    return parseInt(codePoints[0], 16);
};
/**
 * Trucante block with ellipsis
 *
 * @param paragraph - Paragraph
 * @returns Sliced paragraph
 */ const truncate = (paragraph)=>{
    const runs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["last"])(paragraph)?.runs || [];
    const font = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["last"])(runs)?.attributes?.font[0];
    if (font) {
        const index = paragraph.length - 1;
        const codePoint = getEllipsisCodePoint(font);
        const glyph = font.glyphForCodePoint(codePoint);
        const lastBlock = append(glyph, trim(paragraph[index]));
        return Object.assign([], paragraph, {
            [index]: lastBlock
        });
    }
    return paragraph;
};
/**
 * Omit attribute from run
 *
 * @param value - Attribute key
 * @param run - Run
 * @returns Run without ommited attribute
 */ const omit = (value, run)=>{
    const attributes = Object.assign({}, run.attributes);
    delete attributes[value];
    return Object.assign({}, run, {
        attributes
    });
};
/**
 * Get run ascent
 *
 * @param run - Run
 * @returns Ascent
 */ const ascent$1 = (run)=>{
    const { font, attachment } = run.attributes;
    const attachmentHeight = attachment?.height || 0;
    const fontAscent = typeof font === 'string' ? 0 : font?.[0]?.ascent || 0;
    return Math.max(attachmentHeight, fontAscent * scale(run));
};
/**
 * Get run descent
 *
 * @param run - Run
 * @returns Descent
 */ const descent = (run)=>{
    const font = run.attributes?.font;
    const fontDescent = typeof font === 'string' ? 0 : font?.[0]?.descent || 0;
    return scale(run) * fontDescent;
};
/**
 * Get run lineGap
 *
 * @param run - Run
 * @returns LineGap
 */ const lineGap = (run)=>{
    const font = run.attributes?.font;
    const lineGap = typeof font === 'string' ? 0 : font?.[0]?.lineGap || 0;
    return lineGap * scale(run);
};
/**
 * Get run height
 *
 * @param run - Run
 * @returns Height
 */ const height$1 = (run)=>{
    const lineHeight = run.attributes?.lineHeight;
    return lineHeight || lineGap(run) + ascent$1(run) - descent(run);
};
/**
 * Returns attributed string height
 *
 * @param attributedString - Attributed string
 * @returns Height
 */ const height = (attributedString)=>{
    const reducer = (acc, run)=>Math.max(acc, height$1(run));
    return attributedString.runs.reduce(reducer, 0);
};
/**
 * Checks if two rects intersect each other
 *
 * @param a - Rect A
 * @param b - Rect B
 * @returns Whether rects intersect
 */ const intersects = (a, b)=>{
    const x = Math.max(a.x, b.x);
    const num1 = Math.min(a.x + a.width, b.x + b.width);
    const y = Math.max(a.y, b.y);
    const num2 = Math.min(a.y + a.height, b.y + b.height);
    return num1 >= x && num2 >= y;
};
const getLineFragment = (lineRect, excludeRect)=>{
    if (!intersects(excludeRect, lineRect)) return [
        lineRect
    ];
    const eStart = excludeRect.x;
    const eEnd = excludeRect.x + excludeRect.width;
    const lStart = lineRect.x;
    const lEnd = lineRect.x + lineRect.width;
    const a = Object.assign({}, lineRect, {
        width: eStart - lStart
    });
    const b = Object.assign({}, lineRect, {
        x: eEnd,
        width: lEnd - eEnd
    });
    return [
        a,
        b
    ].filter((r)=>r.width > 0);
};
const getLineFragments = (rect, excludeRects)=>{
    let fragments = [
        rect
    ];
    for(let i = 0; i < excludeRects.length; i += 1){
        const excludeRect = excludeRects[i];
        fragments = fragments.reduce((acc, fragment)=>{
            const pieces = getLineFragment(fragment, excludeRect);
            return acc.concat(pieces);
        }, []);
    }
    return fragments;
};
const generateLineRects = (container, height)=>{
    const { excludeRects, ...rect } = container;
    if (!excludeRects) return [
        rect
    ];
    const lineRects = [];
    const maxY = Math.max(...excludeRects.map((r)=>r.y + r.height));
    let currentRect = rect;
    while(currentRect.y < maxY){
        const [lineRect, rest] = partition(currentRect, height);
        const lineRectFragments = getLineFragments(lineRect, excludeRects);
        currentRect = rest;
        lineRects.push(...lineRectFragments);
    }
    return [
        ...lineRects,
        currentRect
    ];
};
const ATTACHMENT_CODE$1 = '\ufffc'; // 65532
/**
 * Remove attachment attribute if no char present
 *
 * @param line - Line
 * @returns Line
 */ const purgeAttachments = (line)=>{
    const shouldPurge = !line.string.includes(ATTACHMENT_CODE$1);
    if (!shouldPurge) return line;
    const runs = line.runs.map((run)=>omit('attachment', run));
    return Object.assign({}, line, {
        runs
    });
};
/**
 * Layout paragraphs inside rectangle
 *
 * @param rects - Rects
 * @param lines - Attributed strings
 * @param indent
 * @returns layout blocks
 */ const layoutLines = (rects, lines, indent)=>{
    let rect = rects.shift();
    let currentY = rect.y;
    return lines.map((line, i)=>{
        const lineIndent = i === 0 ? indent : 0;
        const style = line.runs?.[0]?.attributes || {};
        const height$1 = Math.max(height(line), style.lineHeight);
        if (currentY + height$1 > rect.y + rect.height && rects.length > 0) {
            rect = rects.shift();
            currentY = rect.y;
        }
        const newLine = {
            string: line.string,
            runs: line.runs,
            box: {
                x: rect.x + lineIndent,
                y: currentY,
                width: rect.width - lineIndent,
                height: height$1
            }
        };
        currentY += height$1;
        return purgeAttachments(newLine);
    });
};
/**
 * Performs line breaking and layout
 *
 * @param engines - Engines
 * @param options - Layout options
 */ const layoutParagraph = (engines, options = {})=>{
    /**
     * @param container - Container
     * @param paragraph - Attributed string
     * @returns Layout block
     */ return (container, paragraph)=>{
        const height$1 = height(paragraph);
        const indent = paragraph.runs?.[0]?.attributes?.indent || 0;
        const rects = generateLineRects(container, height$1);
        const availableWidths = rects.map((r)=>r.width);
        availableWidths.unshift(availableWidths[0] - indent);
        const lines = engines.linebreaker(options)(paragraph, availableWidths);
        return layoutLines(rects, lines, indent);
    };
};
/**
 * Slice block at given height
 *
 * @param height - Height
 * @param paragraph - Paragraph
 * @returns Sliced paragraph
 */ const sliceAtHeight = (height, paragraph)=>{
    const newBlock = [];
    let counter = 0;
    for(let i = 0; i < paragraph.length; i += 1){
        const line = paragraph[i];
        counter += line.box.height;
        if (counter < height) {
            newBlock.push(line);
        } else {
            break;
        }
    }
    return newBlock;
};
/**
 * Layout paragraphs inside container until it does not
 * fit anymore, performing line wrapping in the process.
 *
 * @param  engines - Engines
 * @param  options - Layout options
 * @param container - Container
 */ const typesetter = (engines, options, container)=>{
    /**
     * @param attributedStrings - Attributed strings (paragraphs)
     * @returns Paragraph blocks
     */ return (attributedStrings)=>{
        const result = [];
        const paragraphs = [
            ...attributedStrings
        ];
        const layout = layoutParagraph(engines, options);
        const maxLines = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(container.maxLines) ? Infinity : container.maxLines;
        const truncateEllipsis = container.truncateMode === 'ellipsis';
        let linesCount = maxLines;
        let paragraphRect = copy(container);
        let nextParagraph = paragraphs.shift();
        while(linesCount > 0 && nextParagraph){
            const paragraph = layout(paragraphRect, nextParagraph);
            const slicedBlock = paragraph.slice(0, linesCount);
            const linesHeight = height$2(slicedBlock);
            const shouldTruncate = truncateEllipsis && paragraph.length !== slicedBlock.length;
            linesCount -= slicedBlock.length;
            if (paragraphRect.height >= linesHeight) {
                result.push(shouldTruncate ? truncate(slicedBlock) : slicedBlock);
                paragraphRect = crop(linesHeight, paragraphRect);
                nextParagraph = paragraphs.shift();
            } else {
                result.push(truncate(sliceAtHeight(paragraphRect.height, slicedBlock)));
                break;
            }
        }
        return result;
    };
};
/**
 * Get attributed string start value
 *
 * @param attributedString - Attributed string
 * @returns Start
 */ const start = (attributedString)=>{
    const { runs } = attributedString;
    return runs.length === 0 ? 0 : runs[0].start;
};
/**
 * Get attributed string end value
 *
 * @param attributedString - Attributed string
 * @returns End
 */ const end = (attributedString)=>{
    const { runs } = attributedString;
    return runs.length === 0 ? 0 : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["last"])(runs).end;
};
/**
 * Get attributed string length
 *
 * @param attributedString - Attributed string
 * @returns End
 */ const length$1 = (attributedString)=>{
    return end(attributedString) - start(attributedString);
};
const bidi$2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bidi$2d$js$2f$dist$2f$bidi$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
const getBidiLevels$1 = (runs)=>{
    return runs.reduce((acc, run)=>{
        const length = run.end - run.start;
        const levels = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["repeat"])(run.attributes.bidiLevel, length);
        return acc.concat(levels);
    }, []);
};
const getReorderedIndices = (string, segments)=>{
    // Fill an array with indices
    const indices = [];
    for(let i = 0; i < string.length; i += 1){
        indices[i] = i;
    }
    // Reverse each segment in order
    segments.forEach(([start, end])=>{
        const slice = indices.slice(start, end + 1);
        for(let i = slice.length - 1; i >= 0; i -= 1){
            indices[end - i] = slice[i];
        }
    });
    return indices;
};
const getItemAtIndex = (runs, objectName, index)=>{
    for(let i = 0; i < runs.length; i += 1){
        const run = runs[i];
        const updatedIndex = run.glyphIndices[index - run.start];
        if (index >= run.start && index < run.end) {
            return run[objectName][updatedIndex];
        }
    }
    throw new Error(`index ${index} out of range`);
};
const reorderLine = (line)=>{
    const levels = getBidiLevels$1(line.runs);
    const direction = line.runs[0]?.attributes.direction;
    const level = direction === 'rtl' ? 1 : 0;
    const end = length$1(line) - 1;
    const paragraphs = [
        {
            start: 0,
            end,
            level
        }
    ];
    const embeddingLevels = {
        paragraphs,
        levels
    };
    const segments = bidi$2.getReorderSegments(line.string, embeddingLevels);
    // No need for bidi reordering
    if (segments.length === 0) return line;
    const indices = getReorderedIndices(line.string, segments);
    const updatedString = bidi$2.getReorderedString(line.string, embeddingLevels);
    const updatedRuns = line.runs.map((run)=>{
        const selectedIndices = indices.slice(run.start, run.end);
        const updatedGlyphs = [];
        const updatedPositions = [];
        const addedGlyphs = new Set();
        for(let i = 0; i < selectedIndices.length; i += 1){
            const index = selectedIndices[i];
            const glyph = getItemAtIndex(line.runs, 'glyphs', index);
            if (addedGlyphs.has(glyph.id)) continue;
            updatedGlyphs.push(glyph);
            updatedPositions.push(getItemAtIndex(line.runs, 'positions', index));
            if (glyph.isLigature) {
                addedGlyphs.add(glyph.id);
            }
        }
        return {
            ...run,
            glyphs: updatedGlyphs,
            positions: updatedPositions
        };
    });
    return {
        box: line.box,
        runs: updatedRuns,
        string: updatedString
    };
};
const reorderParagraph = (paragraph)=>paragraph.map(reorderLine);
/**
 * Perform bidi reordering
 *
 * @returns Reordered paragraphs
 */ const bidiReordering = ()=>{
    /**
     * @param paragraphs - Paragraphs
     * @returns Reordered paragraphs
     */ return (paragraphs)=>paragraphs.map(reorderParagraph);
};
const DUMMY_CODEPOINT = 123;
/**
 * Resolve string indices based on glyphs code points
 *
 * @param glyphs
 * @returns Glyph indices
 */ const resolve = (glyphs = [])=>{
    return glyphs.reduce((acc, glyph)=>{
        const codePoints = glyph?.codePoints || [
            DUMMY_CODEPOINT
        ];
        if (acc.length === 0) return codePoints.map(()=>0);
        const last = acc[acc.length - 1];
        const next = codePoints.map(()=>last + 1);
        return [
            ...acc,
            ...next
        ];
    }, []);
};
const getCharacterSpacing = (run)=>{
    return run.attributes?.characterSpacing || 0;
};
/**
 * Scale run positions
 *
 * @param  run
 * @param  positions
 * @returns Scaled positions
 */ const scalePositions = (run, positions)=>{
    const runScale = scale(run);
    const characterSpacing = getCharacterSpacing(run);
    return positions.map((position, i)=>{
        const isLast = i === positions.length;
        const xSpacing = isLast ? 0 : characterSpacing;
        return Object.assign({}, position, {
            xAdvance: position.xAdvance * runScale + xSpacing,
            yAdvance: position.yAdvance * runScale,
            xOffset: position.xOffset * runScale,
            yOffset: position.yOffset * runScale
        });
    });
};
/**
 * Create glyph run
 *
 * @param string string
 */ const layoutRun = (string)=>{
    /**
     * @param run - Run
     * @returns Glyph run
     */ return (run)=>{
        const { start, end, attributes = {} } = run;
        const { font } = attributes;
        if (!font) return {
            ...run,
            glyphs: [],
            glyphIndices: [],
            positions: []
        };
        const runString = string.slice(start, end);
        if (typeof font === 'string') throw new Error('Invalid font');
        // passing LTR To force fontkit to not reverse the string
        const glyphRun = font[0].layout(runString, undefined, undefined, undefined, 'ltr');
        const positions = scalePositions(run, glyphRun.positions);
        const glyphIndices = resolve(glyphRun.glyphs);
        const result = {
            ...run,
            positions,
            glyphIndices,
            glyphs: glyphRun.glyphs
        };
        return result;
    };
};
/**
 * Generate glyphs for single attributed string
 */ const generateGlyphs = ()=>{
    /**
     * @param attributedString - Attributed string
     * @returns Attributed string with glyphs
     */ return (attributedString)=>{
        const runs = attributedString.runs.map(layoutRun(attributedString.string));
        const res = Object.assign({}, attributedString, {
            runs
        });
        return res;
    };
};
/**
 * Resolves yOffset for run
 *
 * @param run - Run
 * @returns Run
 */ const resolveRunYOffset = (run)=>{
    if (!run.positions) return run;
    const unitsPerEm = run.attributes?.font?.[0]?.unitsPerEm || 0;
    const yOffset = (run.attributes?.yOffset || 0) * unitsPerEm;
    const positions = run.positions.map((p)=>Object.assign({}, p, {
            yOffset
        }));
    return Object.assign({}, run, {
        positions
    });
};
/**
 * Resolves yOffset for multiple paragraphs
 */ const resolveYOffset = ()=>{
    /**
     * @param attributedString - Attributed string
     * @returns Attributed string
     */ return (attributedString)=>{
        const runs = attributedString.runs.map(resolveRunYOffset);
        const res = Object.assign({}, attributedString, {
            runs
        });
        return res;
    };
};
/**
 * Sort runs in ascending order
 *
 * @param runs
 * @returns Sorted runs
 */ const sort = (runs)=>{
    return runs.sort((a, b)=>a.start - b.start || a.end - b.end);
};
/**
 * Is run empty (start === end)
 *
 * @param run - Run
 * @returns Is run empty
 */ const isEmpty = (run)=>{
    return run.start === run.end;
};
/**
 * Sort points in ascending order
 * @param a - First point
 * @param b - Second point
 * @returns Sort order
 */ const sortPoints = (a, b)=>{
    return a[1] - b[1] || a[3] - b[3];
};
/**
 * @param runs
 * @returns Points
 */ const generatePoints = (runs)=>{
    const result = runs.reduce((acc, run, i)=>{
        return acc.concat([
            [
                'start',
                run.start,
                run.attributes,
                i
            ],
            [
                'end',
                run.end,
                run.attributes,
                i
            ]
        ]);
    }, []);
    return result.sort(sortPoints);
};
/**
 * @param runs
 * @returns Merged runs
 */ const mergeRuns = (runs)=>{
    return runs.reduce((acc, run)=>{
        const attributes = Object.assign({}, acc.attributes, run.attributes);
        return Object.assign({}, run, {
            attributes
        });
    }, {});
};
/**
 * @param runs
 * @returns Grouped runs
 */ const groupEmptyRuns = (runs)=>{
    const groups = runs.reduce((acc, run)=>{
        if (!acc[run.start]) acc[run.start] = [];
        acc[run.start].push(run);
        return acc;
    }, []);
    return Object.values(groups);
};
/**
 * @param runs
 * @returns Flattened runs
 */ const flattenEmptyRuns = (runs)=>{
    return groupEmptyRuns(runs).map(mergeRuns);
};
/**
 * @param runs
 * @returns Flattened runs
 */ const flattenRegularRuns = (runs)=>{
    const res = [];
    const points = generatePoints(runs);
    let start = -1;
    let attrs = {};
    const stack = [];
    for(let i = 0; i < points.length; i += 1){
        const [type, offset, attributes] = points[i];
        if (start !== -1 && start < offset) {
            res.push({
                start,
                end: offset,
                attributes: attrs,
                glyphIndices: [],
                glyphs: [],
                positions: []
            });
        }
        if (type === 'start') {
            stack.push(attributes);
            attrs = Object.assign({}, attrs, attributes);
        } else {
            attrs = {};
            for(let j = 0; j < stack.length; j += 1){
                if (stack[j] === attributes) {
                    stack.splice(j--, 1);
                } else {
                    attrs = Object.assign({}, attrs, stack[j]);
                }
            }
        }
        start = offset;
    }
    return res;
};
/**
 * Flatten many runs
 *
 * @param runs
 * @returns Flattened runs
 */ const flatten = (runs = [])=>{
    const emptyRuns = flattenEmptyRuns(runs.filter((run)=>isEmpty(run)));
    const regularRuns = flattenRegularRuns(runs.filter((run)=>!isEmpty(run)));
    return sort(emptyRuns.concat(regularRuns));
};
/**
 * Returns empty attributed string
 *
 * @returns Empty attributed string
 */ const empty = ()=>({
        string: '',
        runs: []
    });
/**
 *
 * @param attributedString
 * @returns Attributed string without font
 */ const omitFont = (attributedString)=>{
    const runs = attributedString.runs.map((run)=>omit('font', run));
    return Object.assign({}, attributedString, {
        runs
    });
};
/**
 * Performs font substitution and script itemization on attributed string
 *
 * @param engines - engines
 */ const preprocessRuns = (engines)=>{
    /**
     * @param attributedString - Attributed string
     * @returns Processed attributed string
     */ return (attributedString)=>{
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(attributedString)) return empty();
        const { string } = attributedString;
        const { fontSubstitution, scriptItemizer, bidi } = engines;
        const { runs: omittedFontRuns } = omitFont(attributedString);
        const { runs: itemizationRuns } = scriptItemizer()(attributedString);
        const { runs: substitutedRuns } = fontSubstitution()(attributedString);
        const { runs: bidiRuns } = bidi()(attributedString);
        const runs = bidiRuns.concat(substitutedRuns).concat(itemizationRuns).concat(omittedFontRuns);
        return {
            string,
            runs: flatten(runs)
        };
    };
};
/**
 * Breaks attributed string into paragraphs
 */ const splitParagraphs = ()=>{
    /**
     * @param attributedString - Attributed string
     * @returns Paragraphs attributed strings
     */ return (attributedString)=>{
        const paragraphs = [];
        let start = 0;
        let breakPoint = attributedString.string.indexOf('\n') + 1;
        while(breakPoint > 0){
            paragraphs.push(slice(start, breakPoint, attributedString));
            start = breakPoint;
            breakPoint = attributedString.string.indexOf('\n', breakPoint) + 1;
        }
        if (start === 0) {
            paragraphs.push(attributedString);
        } else if (start < attributedString.string.length) {
            paragraphs.push(slice(start, length$1(attributedString), attributedString));
        }
        return paragraphs;
    };
};
/**
 * Return positions advance width
 *
 * @param positions - Positions
 * @returns {number} advance width
 */ const advanceWidth$2 = (positions)=>{
    return positions.reduce((acc, pos)=>acc + (pos.xAdvance || 0), 0);
};
/**
 * Return run advance width
 *
 * @param run - Run
 * @returns Advance width
 */ const advanceWidth$1 = (run)=>{
    return advanceWidth$2(run.positions || []);
};
/**
 * Returns attributed string advancewidth
 *
 * @param attributedString - Attributed string
 * @returns Advance width
 */ const advanceWidth = (attributedString)=>{
    const reducer = (acc, run)=>acc + advanceWidth$1(run);
    return attributedString.runs.reduce(reducer, 0);
};
const WHITE_SPACES_CODE = 32;
/**
 * Check if glyph is white space
 *
 * @param glyph - Glyph
 * @returns Whether glyph is white space
 * */ const isWhiteSpace = (glyph)=>{
    const codePoints = glyph?.codePoints || [];
    return codePoints.includes(WHITE_SPACES_CODE);
};
/**
 * Get white space leading positions
 *
 * @param run - Run
 * @returns White space leading positions
 */ const leadingPositions = (run)=>{
    const glyphs = run.glyphs || [];
    const positions = run.positions || [];
    const leadingWhitespaces = glyphs.findIndex((g)=>!isWhiteSpace(g));
    return positions.slice(0, leadingWhitespaces);
};
/**
 * Get run leading white space offset
 *
 * @param run - Run
 * @returns Leading white space offset
 */ const leadingOffset$1 = (run)=>{
    const positions = leadingPositions(run);
    return positions.reduce((acc, pos)=>acc + (pos.xAdvance || 0), 0);
};
/**
 * Get attributed string leading white space offset
 *
 * @param attributedString - Attributed string
 * @returns Leading white space offset
 */ const leadingOffset = (attributedString)=>{
    const runs = attributedString.runs || [];
    return leadingOffset$1(runs[0]);
};
/**
 * Get white space trailing positions
 *
 * @param run run
 * @returns White space trailing positions
 */ const trailingPositions = (run)=>{
    const glyphs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reverse"])(run.glyphs || []);
    const positions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reverse"])(run.positions || []);
    const leadingWhitespaces = glyphs.findIndex((g)=>!isWhiteSpace(g));
    return positions.slice(0, leadingWhitespaces);
};
/**
 * Get run trailing white space offset
 *
 * @param run - Run
 * @returns Trailing white space offset
 */ const trailingOffset$1 = (run)=>{
    const positions = trailingPositions(run);
    return positions.reduce((acc, pos)=>acc + (pos.xAdvance || 0), 0);
};
/**
 * Get attributed string trailing white space offset
 *
 * @param attributedString - Attributed string
 * @returns Trailing white space offset
 */ const trailingOffset = (attributedString)=>{
    const runs = attributedString.runs || [];
    return trailingOffset$1((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["last"])(runs));
};
/**
 * Drop last char of run
 *
 * @param run - Run
 * @returns Run without last char
 */ const dropLast$1 = (run)=>{
    return slice$1(0, run.end - run.start - 1, run);
};
/**
 * Drop last glyph
 *
 * @param attributedString - Attributed string
 * @returns Attributed string with new glyph
 */ const dropLast = (attributedString)=>{
    const string = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dropLast"])(attributedString.string);
    const runs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjust"])(-1, dropLast$1, attributedString.runs);
    return Object.assign({}, attributedString, {
        string,
        runs
    });
};
const ALIGNMENT_FACTORS = {
    center: 0.5,
    right: 1
};
/**
 * Remove new line char at the end of line if present
 *
 * @param line
 * @returns Line
 */ const removeNewLine = (line)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["last"])(line.string) === '\n' ? dropLast(line) : line;
};
const getOverflowLeft = (line)=>{
    return leadingOffset(line) + (line.overflowLeft || 0);
};
const getOverflowRight = (line)=>{
    return trailingOffset(line) + (line.overflowRight || 0);
};
/**
 * Ignore whitespace at the start and end of a line for alignment
 *
 * @param line
 * @returns Line
 */ const adjustOverflow = (line)=>{
    const overflowLeft = getOverflowLeft(line);
    const overflowRight = getOverflowRight(line);
    const x = line.box.x - overflowLeft;
    const width = line.box.width + overflowLeft + overflowRight;
    const box = Object.assign({}, line.box, {
        x,
        width
    });
    return Object.assign({}, line, {
        box,
        overflowLeft,
        overflowRight
    });
};
/**
 * Performs line justification by calling appropiate engine
 *
 * @param engines - Engines
 * @param options - Layout options
 * @param align - Text align
 */ const justifyLine$1 = (engines, options, align)=>{
    /**
     * @param line - Line
     * @returns Line
     */ return (line)=>{
        const lineWidth = advanceWidth(line);
        const alignFactor = ALIGNMENT_FACTORS[align] || 0;
        const remainingWidth = Math.max(0, line.box.width - lineWidth);
        const shouldJustify = align === 'justify' || lineWidth > line.box.width;
        const x = line.box.x + remainingWidth * alignFactor;
        const box = Object.assign({}, line.box, {
            x
        });
        const newLine = Object.assign({}, line, {
            box
        });
        return shouldJustify ? engines.justification(options)(newLine) : newLine;
    };
};
const finalizeLine = (line)=>{
    let lineAscent = 0;
    let lineDescent = 0;
    let lineHeight = 0;
    let lineXAdvance = 0;
    const runs = line.runs.map((run)=>{
        const height = height$1(run);
        const ascent = ascent$1(run);
        const descent$1 = descent(run);
        const xAdvance = advanceWidth$1(run);
        lineHeight = Math.max(lineHeight, height);
        lineAscent = Math.max(lineAscent, ascent);
        lineDescent = Math.max(lineDescent, descent$1);
        lineXAdvance += xAdvance;
        return Object.assign({}, run, {
            height,
            ascent,
            descent: descent$1,
            xAdvance
        });
    });
    return Object.assign({}, line, {
        runs,
        height: lineHeight,
        ascent: lineAscent,
        descent: lineDescent,
        xAdvance: lineXAdvance
    });
};
/**
 * Finalize line by performing line justification
 * and text decoration (using appropiate engines)
 *
 * @param engines - Engines
 * @param options - Layout options
 */ const finalizeBlock = (engines, options)=>{
    /**
     * @param line - Line
     * @param i - Line index
     * @param lines - Total lines
     * @returns Line
     */ return (line, index, lines)=>{
        const isLastFragment = index === lines.length - 1;
        const style = line.runs?.[0]?.attributes || {};
        const align = isLastFragment ? style.alignLastLine : style.align;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["compose"])(finalizeLine, engines.textDecoration(), justifyLine$1(engines, options, align), adjustOverflow, removeNewLine)(line);
    };
};
/**
 * Finalize line block by performing line justification
 * and text decoration (using appropiate engines)
 *
 * @param engines - Engines
 * @param options - Layout options
 */ const finalizeFragments = (engines, options)=>{
    /**
     * @param paragraphs - Paragraphs
     * @returns Paragraphs
     */ return (paragraphs)=>{
        const blockFinalizer = finalizeBlock(engines, options);
        return paragraphs.map((paragraph)=>paragraph.map(blockFinalizer));
    };
};
const ATTACHMENT_CODE = 0xfffc; // 65532
const isReplaceGlyph = (glyph)=>glyph.codePoints.includes(ATTACHMENT_CODE);
/**
 * Resolve attachments of run
 *
 * @param run
 * @returns Run
 */ const resolveRunAttachments = (run)=>{
    if (!run.positions) return run;
    const glyphs = run.glyphs || [];
    const attachment = run.attributes?.attachment;
    if (!attachment) return run;
    const positions = run.positions.map((position, i)=>{
        const glyph = glyphs[i];
        if (attachment.width && isReplaceGlyph(glyph)) {
            return Object.assign({}, position, {
                xAdvance: attachment.width
            });
        }
        return Object.assign({}, position);
    });
    return Object.assign({}, run, {
        positions
    });
};
/**
 * Resolve attachments for multiple paragraphs
 */ const resolveAttachments = ()=>{
    /**
     * @param attributedString - Attributed string
     * @returns Attributed string
     */ return (attributedString)=>{
        const runs = attributedString.runs.map(resolveRunAttachments);
        const res = Object.assign({}, attributedString, {
            runs
        });
        return res;
    };
};
/**
 * @param attributes - Attributes
 * @returns Attributes with defaults
 */ const applyAttributes = (a)=>{
    return {
        align: a.align || (a.direction === 'rtl' ? 'right' : 'left'),
        alignLastLine: a.alignLastLine || (a.align === 'justify' ? 'left' : a.align || 'left'),
        attachment: a.attachment || null,
        backgroundColor: a.backgroundColor || null,
        bullet: a.bullet || null,
        characterSpacing: a.characterSpacing || 0,
        color: a.color || 'black',
        direction: a.direction || 'ltr',
        features: a.features || [],
        fill: a.fill !== false,
        font: a.font || [],
        fontSize: a.fontSize || 12,
        hangingPunctuation: a.hangingPunctuation || false,
        hyphenationFactor: a.hyphenationFactor || 0,
        indent: a.indent || 0,
        justificationFactor: a.justificationFactor || 1,
        lineHeight: a.lineHeight || null,
        lineSpacing: a.lineSpacing || 0,
        link: a.link || null,
        marginLeft: a.marginLeft || a.margin || 0,
        marginRight: a.marginRight || a.margin || 0,
        opacity: a.opacity,
        paddingTop: a.paddingTop || a.padding || 0,
        paragraphSpacing: a.paragraphSpacing || 0,
        script: a.script || null,
        shrinkFactor: a.shrinkFactor || 0,
        strike: a.strike || false,
        strikeColor: a.strikeColor || a.color || 'black',
        strikeStyle: a.strikeStyle || 'solid',
        stroke: a.stroke || false,
        underline: a.underline || false,
        underlineColor: a.underlineColor || a.color || 'black',
        underlineStyle: a.underlineStyle || 'solid',
        verticalAlign: a.verticalAlign || null,
        wordSpacing: a.wordSpacing || 0,
        yOffset: a.yOffset || 0
    };
};
/**
 * Apply default style to run
 *
 * @param run - Run
 * @returns Run with default styles
 */ const applyRunStyles = (run)=>{
    const attributes = applyAttributes(run.attributes);
    return Object.assign({}, run, {
        attributes
    });
};
/**
 * Apply default attributes for an attributed string
 */ const applyDefaultStyles = ()=>{
    return (attributedString)=>{
        const string = attributedString.string || '';
        const runs = (attributedString.runs || []).map(applyRunStyles);
        return {
            string,
            runs
        };
    };
};
/**
 * Apply scaling and yOffset for verticalAlign 'sub' and 'super'.
 */ const verticalAlignment = ()=>{
    /**
     * @param attributedString - Attributed string
     * @returns Attributed string
     */ return (attributedString)=>{
        attributedString.runs.forEach((run)=>{
            const { attributes } = run;
            const { verticalAlign } = attributes;
            if (verticalAlign === 'sub') {
                attributes.yOffset = -0.2;
            } else if (verticalAlign === 'super') {
                attributes.yOffset = 0.4;
            }
        });
        return attributedString;
    };
};
const bidi$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bidi$2d$js$2f$dist$2f$bidi$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
/**
 * @param runs
 * @returns Bidi levels
 */ const getBidiLevels = (runs)=>{
    return runs.reduce((acc, run)=>{
        const length = run.end - run.start;
        const levels = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["repeat"])(run.attributes.bidiLevel, length);
        return acc.concat(levels);
    }, []);
};
/**
 * Perform bidi mirroring
 */ const mirrorString = ()=>{
    /**
     * @param attributedString - Attributed string
     * @returns Attributed string
     */ return (attributedString)=>{
        const levels = getBidiLevels(attributedString.runs);
        let updatedString = '';
        attributedString.string.split('').forEach((char, index)=>{
            const isRTL = levels[index] % 2 === 1;
            const mirroredChar = isRTL ? bidi$1.getMirroredCharacter(attributedString.string.charAt(index)) : null;
            updatedString += mirroredChar || char;
        });
        const result = {
            ...attributedString,
            string: updatedString
        };
        return result;
    };
};
/**
 * A LayoutEngine is the main object that performs text layout.
 * It accepts an AttributedString and a Container object
 * to layout text into, and uses several helper objects to perform
 * various layout tasks. These objects can be overridden to customize
 * layout behavior.
 */ const layoutEngine = (engines)=>{
    return (attributedString, container, options = {})=>{
        const processParagraph = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["compose"])(resolveYOffset(), resolveAttachments(), verticalAlignment(), wrapWords(engines, options), generateGlyphs(), mirrorString(), preprocessRuns(engines));
        const processParagraphs = (paragraphs)=>paragraphs.map(processParagraph);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["compose"])(finalizeFragments(engines, options), bidiReordering(), typesetter(engines, options, container), processParagraphs, splitParagraphs(), applyDefaultStyles())(attributedString);
    };
};
const bidi = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bidi$2d$js$2f$dist$2f$bidi$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
const bidiEngine = ()=>{
    /**
     * @param attributedString - Attributed string
     * @returns Attributed string
     */ return (attributedString)=>{
        const { string } = attributedString;
        const direction = attributedString.runs[0]?.attributes.direction;
        const { levels } = bidi.getEmbeddingLevels(string, direction);
        let lastLevel = null;
        let lastIndex = 0;
        let index = 0;
        const runs = [];
        for(let i = 0; i < levels.length; i += 1){
            const level = levels[i];
            if (level !== lastLevel) {
                if (lastLevel !== null) {
                    runs.push({
                        start: lastIndex,
                        end: index,
                        attributes: {
                            bidiLevel: lastLevel
                        }
                    });
                }
                lastIndex = index;
                lastLevel = level;
            }
            index += 1;
        }
        if (lastIndex < string.length) {
            runs.push({
                start: lastIndex,
                end: string.length,
                attributes: {
                    bidiLevel: lastLevel
                }
            });
        }
        const result = {
            string,
            runs
        };
        return result;
    };
};
const INFINITY = 10000;
const getNextBreakpoint = (subnodes, widths, lineNumber)=>{
    let position = null;
    let minimumBadness = Infinity;
    const sum = {
        width: 0,
        stretch: 0,
        shrink: 0
    };
    const lineLength = widths[Math.min(lineNumber, widths.length - 1)];
    const calculateRatio = (node)=>{
        const stretch = 'stretch' in node ? node.stretch : null;
        if (sum.width < lineLength) {
            if (!stretch) return INFINITY;
            return sum.stretch - stretch > 0 ? (lineLength - sum.width) / sum.stretch : INFINITY;
        }
        const shrink = 'shrink' in node ? node.shrink : null;
        if (sum.width > lineLength) {
            if (!shrink) return INFINITY;
            return sum.shrink - shrink > 0 ? (lineLength - sum.width) / sum.shrink : INFINITY;
        }
        return 0;
    };
    for(let i = 0; i < subnodes.length; i += 1){
        const node = subnodes[i];
        if (node.type === 'box') {
            sum.width += node.width;
        }
        if (node.type === 'glue') {
            sum.width += node.width;
            sum.stretch += node.stretch;
            sum.shrink += node.shrink;
        }
        if (sum.width - sum.shrink > lineLength) {
            if (position === null) {
                let j = i === 0 ? i + 1 : i;
                while(j < subnodes.length && (subnodes[j].type === 'glue' || subnodes[j].type === 'penalty')){
                    j++;
                }
                position = j - 1;
            }
            break;
        }
        if (node.type === 'penalty' || node.type === 'glue') {
            const ratio = calculateRatio(node);
            const penalty = node.type === 'penalty' ? node.penalty : 0;
            const badness = 100 * Math.abs(ratio) ** 3 + penalty;
            if (minimumBadness >= badness) {
                position = i;
                minimumBadness = badness;
            }
        }
    }
    return sum.width - sum.shrink > lineLength ? position : null;
};
const applyBestFit = (nodes, widths)=>{
    let count = 0;
    let lineNumber = 0;
    let subnodes = nodes;
    const breakpoints = [
        0
    ];
    while(subnodes.length > 0){
        const breakpoint = getNextBreakpoint(subnodes, widths, lineNumber);
        if (breakpoint !== null) {
            count += breakpoint;
            breakpoints.push(count);
            subnodes = subnodes.slice(breakpoint + 1, subnodes.length);
            count++;
            lineNumber++;
        } else {
            subnodes = [];
        }
    }
    return breakpoints;
};
/* eslint-disable max-classes-per-file */ class LinkedListNode {
    data;
    prev;
    next;
    constructor(data){
        this.data = data;
        this.prev = null;
        this.next = null;
    }
}
class LinkedList {
    static Node = LinkedListNode;
    head;
    tail;
    listSize;
    listLength;
    constructor(){
        this.head = null;
        this.tail = null;
        this.listSize = 0;
        this.listLength = 0;
    }
    isLinked(node) {
        return !(node && node.prev === null && node.next === null && this.tail !== node && this.head !== node || this.isEmpty());
    }
    size() {
        return this.listSize;
    }
    isEmpty() {
        return this.listSize === 0;
    }
    first() {
        return this.head;
    }
    last() {
        return this.last;
    }
    forEach(callback) {
        let node = this.head;
        while(node !== null){
            callback(node);
            node = node.next;
        }
    }
    at(i) {
        let node = this.head;
        let index = 0;
        if (i >= this.listLength || i < 0) {
            return null;
        }
        while(node !== null){
            if (i === index) {
                return node;
            }
            node = node.next;
            index += 1;
        }
        return null;
    }
    insertAfter(node, newNode) {
        if (!this.isLinked(node)) return this;
        newNode.prev = node;
        newNode.next = node.next;
        if (node.next === null) {
            this.tail = newNode;
        } else {
            node.next.prev = newNode;
        }
        node.next = newNode;
        this.listSize += 1;
        return this;
    }
    insertBefore(node, newNode) {
        if (!this.isLinked(node)) return this;
        newNode.prev = node.prev;
        newNode.next = node;
        if (node.prev === null) {
            this.head = newNode;
        } else {
            node.prev.next = newNode;
        }
        node.prev = newNode;
        this.listSize += 1;
        return this;
    }
    push(node) {
        if (this.head === null) {
            this.unshift(node);
        } else {
            this.insertAfter(this.tail, node);
        }
        return this;
    }
    unshift(node) {
        if (this.head === null) {
            this.head = node;
            this.tail = node;
            node.prev = null;
            node.next = null;
            this.listSize += 1;
        } else {
            this.insertBefore(this.head, node);
        }
        return this;
    }
    remove(node) {
        if (!this.isLinked(node)) return this;
        if (node.prev === null) {
            this.head = node.next;
        } else {
            node.prev.next = node.next;
        }
        if (node.next === null) {
            this.tail = node.prev;
        } else {
            node.next.prev = node.prev;
        }
        this.listSize -= 1;
        return this;
    }
}
/**
 * Licensed under the new BSD License.
 * Copyright 2009-2010, Bram Stein
 * All rights reserved.
 */ function breakpoint(position, demerits, line, fitnessClass, totals, previous) {
    return {
        position,
        demerits,
        line,
        fitnessClass,
        totals: totals || {
            width: 0,
            stretch: 0,
            shrink: 0
        },
        previous
    };
}
function computeCost(nodes, lineLengths, sum, end, active, currentLine) {
    let width = sum.width - active.totals.width;
    let stretch = 0;
    let shrink = 0;
    // If the current line index is within the list of linelengths, use it, otherwise use
    // the last line length of the list.
    const lineLength = currentLine < lineLengths.length ? lineLengths[currentLine - 1] : lineLengths[lineLengths.length - 1];
    if (nodes[end].type === 'penalty') {
        width += nodes[end].width;
    }
    // Calculate the stretch ratio
    if (width < lineLength) {
        stretch = sum.stretch - active.totals.stretch;
        if (stretch > 0) {
            return (lineLength - width) / stretch;
        }
        return linebreak.infinity;
    }
    // Calculate the shrink ratio
    if (width > lineLength) {
        shrink = sum.shrink - active.totals.shrink;
        if (shrink > 0) {
            return (lineLength - width) / shrink;
        }
        return linebreak.infinity;
    }
    // perfect match
    return 0;
}
// Add width, stretch and shrink values from the current
// break point up to the next box or forced penalty.
function computeSum(nodes, sum, breakPointIndex) {
    const result = {
        width: sum.width,
        stretch: sum.stretch,
        shrink: sum.shrink
    };
    for(let i = breakPointIndex; i < nodes.length; i += 1){
        const node = nodes[i];
        if (node.type === 'glue') {
            result.width += node.width;
            result.stretch += node.stretch;
            result.shrink += node.shrink;
        } else if (node.type === 'box' || node.type === 'penalty' && node.penalty === -linebreak.infinity && i > breakPointIndex) {
            break;
        }
    }
    return result;
}
function findBestBreakpoints(activeNodes) {
    const breakpoints = [];
    if (activeNodes.size() === 0) return [];
    let tmp = {
        data: {
            demerits: Infinity
        }
    };
    // Find the best active node (the one with the least total demerits.)
    activeNodes.forEach((node)=>{
        if (node.data.demerits < tmp.data.demerits) {
            tmp = node;
        }
    });
    while(tmp !== null){
        breakpoints.push(tmp.data.position);
        tmp = tmp.data.previous;
    }
    return breakpoints.reverse();
}
/**
 * @param nodes
 * @param availableWidths
 * @param tolerance
 * @preserve Knuth and Plass line breaking algorithm in JavaScript
 */ const linebreak = (nodes, availableWidths, tolerance)=>{
    // Demerits are used as a way to penalize bad line breaks
    //  - line: applied to each line, depending on how much spaces need to stretch or shrink
    //  - flagged: applied when consecutive lines end in hyphenation
    //  - fitness: algorithm groups lines into fitness classes based on how loose or tight the spacing is.
    //             if a paragraph has consecutive lines from different fitness classes,
    //             a fitness demerit is applied to maintain visual consistency.
    const options = {
        demerits: {
            line: 10,
            flagged: 100,
            fitness: 3000
        },
        tolerance: tolerance || 3
    };
    const activeNodes = new LinkedList();
    const sum = {
        width: 0,
        stretch: 0,
        shrink: 0
    };
    const lineLengths = availableWidths;
    // Add an active node for the start of the paragraph.
    activeNodes.push(new LinkedList.Node(breakpoint(0, 0, 0, 0, undefined, null)));
    // The main loop of the algorithm
    function mainLoop(node, index, nodes) {
        let active = activeNodes.first();
        // The inner loop iterates through all the active nodes with line < currentLine and then
        // breaks out to insert the new active node candidates before looking at the next active
        // nodes for the next lines. The result of this is that the active node list is always
        // sorted by line number.
        while(active !== null){
            let currentLine = 0;
            // Candidates fo each fitness class
            const candidates = [
                {
                    active: undefined,
                    demerits: Infinity
                },
                {
                    active: undefined,
                    demerits: Infinity
                },
                {
                    active: undefined,
                    demerits: Infinity
                },
                {
                    active: undefined,
                    demerits: Infinity
                }
            ];
            // Iterate through the linked list of active nodes to find new potential active nodes and deactivate current active nodes.
            while(active !== null){
                currentLine = active.data.line + 1;
                const ratio = computeCost(nodes, lineLengths, sum, index, active.data, currentLine);
                // Deactive nodes when the distance between the current active node and the
                // current node becomes too large (i.e. it exceeds the stretch limit and the stretch
                // ratio becomes negative) or when the current node is a forced break (i.e. the end
                // of the paragraph when we want to remove all active nodes, but possibly have a final
                // candidate active node---if the paragraph can be set using the given tolerance value.)
                if (ratio < -1 || node.type === 'penalty' && node.penalty === -linebreak.infinity) {
                    activeNodes.remove(active);
                }
                // If the ratio is within the valid range of -1 <= ratio <= tolerance calculate the
                // total demerits and record a candidate active node.
                if (ratio >= -1 && ratio <= options.tolerance) {
                    const badness = 100 * Math.pow(Math.abs(ratio), 3);
                    let demerits = 0;
                    // Positive penalty
                    if (node.type === 'penalty' && node.penalty >= 0) {
                        demerits = Math.pow(options.demerits.line + badness, 2) + Math.pow(node.penalty, 2);
                    // Negative penalty but not a forced break
                    } else if (node.type === 'penalty' && node.penalty !== -linebreak.infinity) {
                        demerits = Math.pow(options.demerits.line + badness, 2) - Math.pow(node.penalty, 2);
                    // All other cases
                    } else {
                        demerits = Math.pow(options.demerits.line + badness, 2);
                    }
                    if (node.type === 'penalty' && nodes[active.data.position].type === 'penalty') {
                        demerits += options.demerits.flagged * node.flagged * // @ts-expect-error node is penalty here
                        nodes[active.data.position].flagged;
                    }
                    // Calculate the fitness class for this candidate active node.
                    let currentClass;
                    if (ratio < -0.5) {
                        currentClass = 0;
                    } else if (ratio <= 0.5) {
                        currentClass = 1;
                    } else if (ratio <= 1) {
                        currentClass = 2;
                    } else {
                        currentClass = 3;
                    }
                    // Add a fitness penalty to the demerits if the fitness classes of two adjacent lines differ too much.
                    if (Math.abs(currentClass - active.data.fitnessClass) > 1) {
                        demerits += options.demerits.fitness;
                    }
                    // Add the total demerits of the active node to get the total demerits of this candidate node.
                    demerits += active.data.demerits;
                    // Only store the best candidate for each fitness class
                    if (demerits < candidates[currentClass].demerits) {
                        candidates[currentClass] = {
                            active,
                            demerits
                        };
                    }
                }
                active = active.next;
                // Stop iterating through active nodes to insert new candidate active nodes in the active list
                // before moving on to the active nodes for the next line.
                // TODO: The Knuth and Plass paper suggests a conditional for currentLine < j0. This means paragraphs
                // with identical line lengths will not be sorted by line number. Find out if that is a desirable outcome.
                // For now I left this out, as it only adds minimal overhead to the algorithm and keeping the active node
                // list sorted has a higher priority.
                if (active !== null && active.data.line >= currentLine) {
                    break;
                }
            }
            const tmpSum = computeSum(nodes, sum, index);
            for(let fitnessClass = 0; fitnessClass < candidates.length; fitnessClass += 1){
                const candidate = candidates[fitnessClass];
                if (candidate.demerits === Infinity) continue;
                const newNode = new LinkedList.Node(breakpoint(index, candidate.demerits, candidate.active.data.line + 1, fitnessClass, tmpSum, candidate.active));
                if (active !== null) {
                    activeNodes.insertBefore(active, newNode);
                } else {
                    activeNodes.push(newNode);
                }
            }
        }
    }
    nodes.forEach((node, index, nodes)=>{
        if (node.type === 'box') {
            sum.width += node.width;
            return;
        }
        if (node.type === 'glue') {
            const precedesBox = index > 0 && nodes[index - 1].type === 'box';
            if (precedesBox) mainLoop(node, index, nodes);
            sum.width += node.width;
            sum.stretch += node.stretch;
            sum.shrink += node.shrink;
            return;
        }
        if (node.type === 'penalty' && node.penalty !== linebreak.infinity) {
            mainLoop(node, index, nodes);
        }
    });
    return findBestBreakpoints(activeNodes);
};
linebreak.infinity = 10000;
linebreak.glue = (width, start, end, stretch, shrink)=>({
        type: 'glue',
        start,
        end,
        width,
        stretch,
        shrink
    });
linebreak.box = (width, start, end, hyphenated = false)=>({
        type: 'box',
        width,
        start,
        end,
        hyphenated
    });
linebreak.penalty = (width, penalty, flagged)=>({
        type: 'penalty',
        width,
        penalty,
        flagged
    });
/**
 * Add scalar to run
 *
 * @param index - Scalar
 * @param run - Run
 * @returns Added run
 */ const add = (index, run)=>{
    const start = run.start + index;
    const end = run.end + index;
    return Object.assign({}, run, {
        start,
        end
    });
};
/**
 * Get run length
 *
 * @param run - Run
 * @returns Length
 */ const length = (run)=>{
    return run.end - run.start;
};
/**
 * Concats two runs into one
 *
 * @param runA - First run
 * @param runB - Second run
 * @returns Concatenated run
 */ const concat = (runA, runB)=>{
    const end = runA.end + length(runB);
    const glyphs = (runA.glyphs || []).concat(runB.glyphs || []);
    const positions = (runA.positions || []).concat(runB.positions || []);
    const attributes = Object.assign({}, runA.attributes, runB.attributes);
    const runAIndices = runA.glyphIndices || [];
    const runALastIndex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["last"])(runAIndices) || 0;
    const runBIndices = (runB.glyphIndices || []).map((i)=>i + runALastIndex + 1);
    const glyphIndices = normalize(runAIndices.concat(runBIndices));
    return Object.assign({}, runA, {
        end,
        glyphs,
        positions,
        attributes,
        glyphIndices
    });
};
/**
 * Insert glyph to run in the given index
 *
 * @param index - Index
 * @param glyph - Glyph
 * @param run - Run
 * @returns Run with glyph
 */ const insertGlyph$1 = (index, glyph, run)=>{
    if (!glyph) return run;
    // Split resolves ligature splitting in case new glyph breaks some
    const leadingRun = slice$1(0, index, run);
    const trailingRun = slice$1(index, Infinity, run);
    return concat(append$1(glyph, leadingRun), trailingRun);
};
/**
 * Insert either glyph or code point to run in the given index
 *
 * @param index - Index
 * @param value - Glyph or codePoint
 * @param run - Run
 * @returns Run with glyph
 */ const insert = (index, value, run)=>{
    const font = getFont(run);
    const glyph = isNumber(value) ? fromCodePoint(value, font) : value;
    return insertGlyph$1(index, glyph, run);
};
/**
 * Get run index at char index
 *
 * @param index - Char index
 * @param attributedString - Attributed string
 * @returns Run index
 */ const runIndexAt = (index, attributedString)=>{
    return runIndexAt$1(index, attributedString.runs);
};
/**
 * Insert glyph into attributed string
 *
 * @param index - Index
 * @param glyph - Glyph or code point
 * @param attributedString - Attributed string
 * @returns Attributed string with new glyph
 */ const insertGlyph = (index, glyph, attributedString)=>{
    const runIndex = runIndexAt(index, attributedString);
    // Add glyph to the end if run index invalid
    if (runIndex === -1) return append(glyph, attributedString);
    const codePoints = [
        glyph
    ];
    const string = attributedString.string.slice(0, index) + stringFromCodePoints(codePoints) + attributedString.string.slice(index);
    const runs = attributedString.runs.map((run, i)=>{
        if (i === runIndex) return insert(index - run.start, glyph, run);
        if (i > runIndex) return add(codePoints.length, run);
        return run;
    });
    return Object.assign({}, attributedString, {
        string,
        runs
    });
};
/**
 * Advance width between two string indices
 *
 * @param start - Glyph index
 * @param end - Glyph index
 * @param run - Run
 * @returns Advanced width run
 */ const advanceWidthBetween$1 = (start, end, run)=>{
    const runStart = run.start || 0;
    const glyphStartIndex = Math.max(0, glyphIndexAt(start - runStart, run));
    const glyphEndIndex = Math.max(0, glyphIndexAt(end - runStart, run));
    const positions = (run.positions || []).slice(glyphStartIndex, glyphEndIndex);
    return advanceWidth$2(positions);
};
/**
 * Advance width between start and end
 * Does not consider ligature splitting for the moment.
 * Check performance impact on supporting this
 *
 * @param start - Start offset
 * @param end - End offset
 * @param attributedString
 * @returns Advance width
 */ const advanceWidthBetween = (start, end, attributedString)=>{
    const runs = filter(start, end, attributedString.runs);
    return runs.reduce((acc, run)=>acc + advanceWidthBetween$1(start, end, run), 0);
};
const HYPHEN = 0x002d;
const TOLERANCE_STEPS = 5;
const TOLERANCE_LIMIT = 50;
const opts = {
    width: 3,
    stretch: 6,
    shrink: 9
};
/**
 * Slice attributed string to many lines
 *
 * @param attributedString - Attributed string
 * @param nodes
 * @param breaks
 * @returns Attributed strings
 */ const breakLines = (attributedString, nodes, breaks)=>{
    let start = 0;
    let end = null;
    const lines = breaks.reduce((acc, breakPoint)=>{
        const node = nodes[breakPoint];
        const prevNode = nodes[breakPoint - 1];
        // Last breakpoint corresponds to K&P mandatory final glue
        if (breakPoint === nodes.length - 1) return acc;
        let line;
        if (node.type === 'penalty') {
            // @ts-expect-error penalty node will always preceed box or glue node
            end = prevNode.end;
            line = slice(start, end, attributedString);
            line = insertGlyph(line.string.length, HYPHEN, line);
        } else {
            end = node.end;
            line = slice(start, end, attributedString);
        }
        start = end;
        return [
            ...acc,
            line
        ];
    }, []);
    // Last line
    lines.push(slice(start, attributedString.string.length, attributedString));
    return lines;
};
/**
 * Return Knuth & Plass nodes based on line and previously calculated syllables
 *
 * @param attributedString - Attributed string
 * @param attributes - Attributes
 * @param options - Layout options
 * @returns ?
 */ const getNodes = (attributedString, { align }, options)=>{
    let start = 0;
    const hyphenWidth = 5;
    const { syllables } = attributedString;
    const hyphenPenalty = options.hyphenationPenalty || (align === 'justify' ? 100 : 600);
    const result = syllables.reduce((acc, s, index)=>{
        const width = advanceWidthBetween(start, start + s.length, attributedString);
        if (s.trim() === '') {
            const stretch = width * opts.width / opts.stretch;
            const shrink = width * opts.width / opts.shrink;
            const end = start + s.length;
            // Add glue node. Glue nodes are used to fill the space between words.
            acc.push(linebreak.glue(width, start, end, stretch, shrink));
        } else {
            const hyphenated = syllables[index + 1] !== ' ';
            const end = start + s.length;
            // Add box node. Box nodes are used to represent words.
            acc.push(linebreak.box(width, start, end, hyphenated));
            if (syllables[index + 1] && hyphenated) {
                // Add penalty node. Penalty nodes are used to represent hyphenation points.
                acc.push(linebreak.penalty(hyphenWidth, hyphenPenalty, 1));
            }
        }
        start += s.length;
        return acc;
    }, []);
    // Add mandatory final glue
    result.push(linebreak.glue(0, start, start, linebreak.infinity, 0));
    result.push(linebreak.penalty(0, -linebreak.infinity, 1));
    return result;
};
/**
 * @param attributedString - Attributed string
 * @returns Attributes
 */ const getAttributes = (attributedString)=>{
    return attributedString.runs?.[0]?.attributes || {};
};
/**
 * Performs Knuth & Plass line breaking algorithm
 * Fallbacks to best fit algorithm if latter not successful
 *
 * @param options - Layout options
 */ const linebreaker = (options)=>{
    /**
     * @param attributedString - Attributed string
     * @param availableWidths - Available widths
     * @returns Attributed string
     */ return (attributedString, availableWidths)=>{
        let tolerance = options.tolerance || 4;
        const attributes = getAttributes(attributedString);
        const nodes = getNodes(attributedString, attributes, options);
        let breaks = linebreak(nodes, availableWidths, tolerance);
        // Try again with a higher tolerance if the line breaking failed.
        while(breaks.length === 0 && tolerance < TOLERANCE_LIMIT){
            tolerance += TOLERANCE_STEPS;
            breaks = linebreak(nodes, availableWidths, tolerance);
        }
        if (breaks.length === 0 || breaks.length === 1 && breaks[0] === 0) {
            breaks = applyBestFit(nodes, availableWidths);
        }
        return breakLines(attributedString, nodes, breaks.slice(1));
    };
};
var Direction;
(function(Direction) {
    Direction[Direction["GROW"] = 0] = "GROW";
    Direction[Direction["SHRINK"] = 1] = "SHRINK";
})(Direction || (Direction = {}));
const WHITESPACE_PRIORITY = 1;
const LETTER_PRIORITY = 2;
const EXPAND_WHITESPACE_FACTOR = {
    before: 0.5,
    after: 0.5,
    priority: WHITESPACE_PRIORITY,
    unconstrained: false
};
const EXPAND_CHAR_FACTOR = {
    before: 0.14453125,
    after: 0.14453125,
    priority: LETTER_PRIORITY,
    unconstrained: false
};
const SHRINK_WHITESPACE_FACTOR = {
    before: -0.04296875,
    after: -0.04296875,
    priority: WHITESPACE_PRIORITY,
    unconstrained: false
};
const SHRINK_CHAR_FACTOR = {
    before: -0.04296875,
    after: -0.04296875,
    priority: LETTER_PRIORITY,
    unconstrained: false
};
const getCharFactor = (direction, options)=>{
    const expandCharFactor = options.expandCharFactor || {};
    const shrinkCharFactor = options.shrinkCharFactor || {};
    return direction === Direction.GROW ? Object.assign({}, EXPAND_CHAR_FACTOR, expandCharFactor) : Object.assign({}, SHRINK_CHAR_FACTOR, shrinkCharFactor);
};
const getWhitespaceFactor = (direction, options)=>{
    const expandWhitespaceFactor = options.expandWhitespaceFactor || {};
    const shrinkWhitespaceFactor = options.shrinkWhitespaceFactor || {};
    return direction === Direction.GROW ? Object.assign({}, EXPAND_WHITESPACE_FACTOR, expandWhitespaceFactor) : Object.assign({}, SHRINK_WHITESPACE_FACTOR, shrinkWhitespaceFactor);
};
const factor = (direction, options)=>(glyphs)=>{
        const charFactor = getCharFactor(direction, options);
        const whitespaceFactor = getWhitespaceFactor(direction, options);
        const factors = [];
        for(let index = 0; index < glyphs.length; index += 1){
            let f;
            const glyph = glyphs[index];
            if (isWhiteSpace(glyph)) {
                f = Object.assign({}, whitespaceFactor);
                if (index === glyphs.length - 1) {
                    f.before = 0;
                    if (index > 0) {
                        factors[index - 1].after = 0;
                    }
                }
            } else if (glyph.isMark && index > 0) {
                f = Object.assign({}, factors[index - 1]);
                f.before = 0;
                factors[index - 1].after = 0;
            } else {
                f = Object.assign({}, charFactor);
            }
            factors.push(f);
        }
        return factors;
    };
const getFactors = (gap, line, options)=>{
    const direction = gap > 0 ? Direction.GROW : Direction.SHRINK;
    const getFactor = factor(direction, options);
    const factors = line.runs.reduce((acc, run)=>{
        return acc.concat(getFactor(run.glyphs));
    }, []);
    factors[0].before = 0;
    factors[factors.length - 1].after = 0;
    return factors;
};
const KASHIDA_PRIORITY = 0;
const NULL_PRIORITY = 3;
const getDistances = (gap, factors)=>{
    let total = 0;
    const priorities = [];
    const unconstrained = [];
    for(let priority = KASHIDA_PRIORITY; priority <= NULL_PRIORITY; priority += 1){
        priorities[priority] = unconstrained[priority] = 0;
    }
    // sum the factors at each priority
    for(let j = 0; j < factors.length; j += 1){
        const f = factors[j];
        const sum = f.before + f.after;
        total += sum;
        priorities[f.priority] += sum;
        if (f.unconstrained) {
            unconstrained[f.priority] += sum;
        }
    }
    // choose the priorities that need to be applied
    let highestPriority = -1;
    let highestPrioritySum = 0;
    let remainingGap = gap;
    let priority;
    for(priority = KASHIDA_PRIORITY; priority <= NULL_PRIORITY; priority += 1){
        const prioritySum = priorities[priority];
        if (prioritySum !== 0) {
            if (highestPriority === -1) {
                highestPriority = priority;
                highestPrioritySum = prioritySum;
            }
            // if this priority covers the remaining gap, we're done
            if (Math.abs(remainingGap) <= Math.abs(prioritySum)) {
                priorities[priority] = remainingGap / prioritySum;
                unconstrained[priority] = 0;
                remainingGap = 0;
                break;
            }
            // mark that we need to use 100% of the adjustment from
            // this priority, and subtract the space that it consumes
            priorities[priority] = 1;
            remainingGap -= prioritySum;
            // if this priority has unconstrained glyphs, let them consume the remaining space
            if (unconstrained[priority] !== 0) {
                unconstrained[priority] = remainingGap / unconstrained[priority];
                remainingGap = 0;
                break;
            }
        }
    }
    // zero out remaining priorities (if any)
    for(let p = priority + 1; p <= NULL_PRIORITY; p += 1){
        priorities[p] = 0;
        unconstrained[p] = 0;
    }
    // if there is still space left over, assign it to the highest priority that we saw.
    // this violates their factors, but it only happens in extreme cases
    if (remainingGap > 0 && highestPriority > -1) {
        priorities[highestPriority] = (highestPrioritySum + (gap - total)) / highestPrioritySum;
    }
    // create and return an array of distances to add to each glyph's advance
    const distances = [];
    for(let index = 0; index < factors.length; index += 1){
        // the distance to add to this glyph is the sum of the space to add
        // after this glyph, and the space to add before the next glyph
        const f = factors[index];
        const next = factors[index + 1];
        let dist = f.after * priorities[f.priority];
        if (next) {
            dist += next.before * priorities[next.priority];
        }
        // if this glyph is unconstrained, add the unconstrained distance as well
        if (f.unconstrained) {
            dist += f.after * unconstrained[f.priority];
            if (next) {
                dist += next.before * unconstrained[next.priority];
            }
        }
        distances.push(dist);
    }
    return distances;
};
/**
 * Adjust run positions by given distances
 *
 * @param distances
 * @param line
 * @returns Line
 */ const justifyLine = (distances, line)=>{
    let index = 0;
    for (const run of line.runs){
        for (const position of run.positions){
            position.xAdvance += distances[index++];
        }
    }
    return line;
};
/**
 * A JustificationEngine is used by a Typesetter to perform line fragment
 * justification. This implementation is based on a description of Apple's
 * justification algorithm from a PDF in the Apple Font Tools package.
 *
 * @param options - Layout options
 */ const justification = (options)=>{
    /**
     * @param line
     * @returns Line
     */ return (line)=>{
        const gap = line.box.width - advanceWidth(line);
        if (gap === 0) return line; // Exact fit
        const factors = getFactors(gap, line, options);
        const distances = getDistances(gap, factors);
        return justifyLine(distances, line);
    };
};
/**
 * Returns attributed string ascent
 *
 * @param attributedString - Attributed string
 * @returns Ascent
 */ const ascent = (attributedString)=>{
    const reducer = (acc, run)=>Math.max(acc, ascent$1(run));
    return attributedString.runs.reduce(reducer, 0);
};
// The base font size used for calculating underline thickness.
const BASE_FONT_SIZE = 12;
/**
 * A TextDecorationEngine is used by a Typesetter to generate
 * DecorationLines for a line fragment, including underlines
 * and strikes.
 */ const textDecoration = ()=>(line)=>{
        let x = line.overflowLeft || 0;
        const overflowRight = line.overflowRight || 0;
        const maxX = advanceWidth(line) - overflowRight;
        line.decorationLines = [];
        for(let i = 0; i < line.runs.length; i += 1){
            const run = line.runs[i];
            const width = Math.min(maxX - x, advanceWidth$1(run));
            const thickness = Math.max(0.5, Math.floor(run.attributes.fontSize / BASE_FONT_SIZE));
            if (run.attributes.underline) {
                const rect = {
                    x,
                    y: ascent(line) + thickness * 2,
                    width,
                    height: thickness
                };
                const decorationLine = {
                    rect,
                    opacity: run.attributes.opacity,
                    color: run.attributes.underlineColor || 'black',
                    style: run.attributes.underlineStyle || 'solid'
                };
                line.decorationLines.push(decorationLine);
            }
            if (run.attributes.strike) {
                const y = ascent(line) - ascent$1(run) / 3;
                const rect = {
                    x,
                    y,
                    width,
                    height: thickness
                };
                const decorationLine = {
                    rect,
                    opacity: run.attributes.opacity,
                    color: run.attributes.strikeColor || 'black',
                    style: run.attributes.strikeStyle || 'solid'
                };
                line.decorationLines.push(decorationLine);
            }
            x += width;
        }
        return line;
    };
const ignoredScripts = [
    'Common',
    'Inherited',
    'Unknown'
];
/**
 * Resolves unicode script in runs, grouping equal runs together
 */ const scriptItemizer = ()=>{
    /**
     * @param attributedString - Attributed string
     * @returns Attributed string
     */ return (attributedString)=>{
        const { string } = attributedString;
        let lastScript = 'Unknown';
        let lastIndex = 0;
        let index = 0;
        const runs = [];
        if (!string) return empty();
        for(let i = 0; i < string.length; i += 1){
            const char = string[i];
            const codePoint = char.codePointAt(0);
            const script = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$unicode$2d$properties$2f$dist$2f$module$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getScript(codePoint);
            if (script !== lastScript && !ignoredScripts.includes(script)) {
                if (lastScript !== 'Unknown') {
                    runs.push({
                        start: lastIndex,
                        end: index,
                        attributes: {
                            script: lastScript
                        }
                    });
                }
                lastIndex = index;
                lastScript = script;
            }
            index += char.length;
        }
        if (lastIndex < string.length) {
            runs.push({
                start: lastIndex,
                end: string.length,
                attributes: {
                    script: lastScript
                }
            });
        }
        const result = {
            string,
            runs: runs
        };
        return result;
    };
};
const SOFT_HYPHEN = '\u00ad';
const hyphenator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$hyphen$2f$hyphen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$hyphen$2f$patterns$2f$en$2d$us$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
/**
 * @param word
 * @returns Word parts
 */ const splitHyphen = (word)=>{
    return word.split(SOFT_HYPHEN);
};
const cache = {};
/**
 * @param word
 * @returns Word parts
 */ const getParts = (word)=>{
    const base = word.includes(SOFT_HYPHEN) ? word : hyphenator(word);
    return splitHyphen(base);
};
const wordHyphenation = ()=>{
    /**
     * @param word - Word
     * @returns Word parts
     */ return (word)=>{
        const cacheKey = `_${word}`;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNil"])(word)) return [];
        if (cache[cacheKey]) return cache[cacheKey];
        cache[cacheKey] = getParts(word);
        return cache[cacheKey];
    };
};
const IGNORED_CODE_POINTS = [
    173
];
const getFontSize = (run)=>run.attributes.fontSize || 12;
const pickFontFromFontStack = (codePoint, fontStack, lastFont)=>{
    const fontStackWithFallback = [
        ...fontStack,
        lastFont
    ];
    for(let i = 0; i < fontStackWithFallback.length; i += 1){
        const font = fontStackWithFallback[i];
        if (!IGNORED_CODE_POINTS.includes(codePoint) && font && font.hasGlyphForCodePoint && font.hasGlyphForCodePoint(codePoint)) {
            return font;
        }
    }
    return fontStack.at(-1);
};
const fontSubstitution = ()=>({ string, runs })=>{
        let lastFont = null;
        let lastFontSize = null;
        let lastIndex = 0;
        let index = 0;
        const res = [];
        for(let i = 0; i < runs.length; i += 1){
            const run = runs[i];
            if (string.length === 0) {
                res.push({
                    start: 0,
                    end: 0,
                    attributes: {
                        font: run.attributes.font
                    }
                });
                break;
            }
            const chars = string.slice(run.start, run.end);
            for(let j = 0; j < chars.length; j += 1){
                const char = chars[j];
                const codePoint = char.codePointAt(0);
                // If the default font does not have a glyph and the fallback font does, we use it
                const font = pickFontFromFontStack(codePoint, run.attributes.font, lastFont);
                const fontSize = getFontSize(run);
                // If anything that would impact res has changed, update it
                if (font !== lastFont || fontSize !== lastFontSize || font.unitsPerEm !== lastFont.unitsPerEm) {
                    if (lastFont) {
                        res.push({
                            start: lastIndex,
                            end: index,
                            attributes: {
                                font: [
                                    lastFont
                                ],
                                scale: lastFontSize / lastFont.unitsPerEm
                            }
                        });
                    }
                    lastFont = font;
                    lastFontSize = fontSize;
                    lastIndex = index;
                }
                index += char.length;
            }
        }
        if (lastIndex < string.length) {
            const fontSize = getFontSize((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["last"])(runs));
            res.push({
                start: lastIndex,
                end: string.length,
                attributes: {
                    font: [
                        lastFont
                    ],
                    scale: fontSize / lastFont.unitsPerEm
                }
            });
        }
        return {
            string,
            runs: res
        };
    };
;
}}),
"[project]/node_modules/@react-pdf/image/lib/index.browser.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>resolveImage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$png$2d$js$2f$lib$2f$png$2d$js$2e$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/png-js/lib/png-js.browser.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jay$2d$peg$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jay-peg/src/index.js [app-client] (ecmascript)");
;
;
var global$1 = typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {};
var lookup = [];
var revLookup = [];
var Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array;
var inited = false;
function init() {
    inited = true;
    var code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    for(var i = 0, len = code.length; i < len; ++i){
        lookup[i] = code[i];
        revLookup[code.charCodeAt(i)] = i;
    }
    revLookup['-'.charCodeAt(0)] = 62;
    revLookup['_'.charCodeAt(0)] = 63;
}
function toByteArray(b64) {
    if (!inited) {
        init();
    }
    var i, j, l, tmp, placeHolders, arr;
    var len = b64.length;
    if (len % 4 > 0) {
        throw new Error('Invalid string. Length must be a multiple of 4');
    }
    // the number of equal signs (place holders)
    // if there are two placeholders, than the two characters before it
    // represent one byte
    // if there is only one, then the three characters before it represent 2 bytes
    // this is just a cheap hack to not do indexOf twice
    placeHolders = b64[len - 2] === '=' ? 2 : b64[len - 1] === '=' ? 1 : 0;
    // base64 is 4/3 + up to two characters of the original data
    arr = new Arr(len * 3 / 4 - placeHolders);
    // if there are placeholders, only get up to the last complete 4 chars
    l = placeHolders > 0 ? len - 4 : len;
    var L = 0;
    for(i = 0, j = 0; i < l; i += 4, j += 3){
        tmp = revLookup[b64.charCodeAt(i)] << 18 | revLookup[b64.charCodeAt(i + 1)] << 12 | revLookup[b64.charCodeAt(i + 2)] << 6 | revLookup[b64.charCodeAt(i + 3)];
        arr[L++] = tmp >> 16 & 0xFF;
        arr[L++] = tmp >> 8 & 0xFF;
        arr[L++] = tmp & 0xFF;
    }
    if (placeHolders === 2) {
        tmp = revLookup[b64.charCodeAt(i)] << 2 | revLookup[b64.charCodeAt(i + 1)] >> 4;
        arr[L++] = tmp & 0xFF;
    } else if (placeHolders === 1) {
        tmp = revLookup[b64.charCodeAt(i)] << 10 | revLookup[b64.charCodeAt(i + 1)] << 4 | revLookup[b64.charCodeAt(i + 2)] >> 2;
        arr[L++] = tmp >> 8 & 0xFF;
        arr[L++] = tmp & 0xFF;
    }
    return arr;
}
function tripletToBase64(num) {
    return lookup[num >> 18 & 0x3F] + lookup[num >> 12 & 0x3F] + lookup[num >> 6 & 0x3F] + lookup[num & 0x3F];
}
function encodeChunk(uint8, start, end) {
    var tmp;
    var output = [];
    for(var i = start; i < end; i += 3){
        tmp = (uint8[i] << 16) + (uint8[i + 1] << 8) + uint8[i + 2];
        output.push(tripletToBase64(tmp));
    }
    return output.join('');
}
function fromByteArray(uint8) {
    if (!inited) {
        init();
    }
    var tmp;
    var len = uint8.length;
    var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes
    var output = '';
    var parts = [];
    var maxChunkLength = 16383; // must be multiple of 3
    // go through the array every three bytes, we'll deal with trailing stuff later
    for(var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength){
        parts.push(encodeChunk(uint8, i, i + maxChunkLength > len2 ? len2 : i + maxChunkLength));
    }
    // pad the end with zeros, but make sure to not forget the extra bytes
    if (extraBytes === 1) {
        tmp = uint8[len - 1];
        output += lookup[tmp >> 2];
        output += lookup[tmp << 4 & 0x3F];
        output += '==';
    } else if (extraBytes === 2) {
        tmp = (uint8[len - 2] << 8) + uint8[len - 1];
        output += lookup[tmp >> 10];
        output += lookup[tmp >> 4 & 0x3F];
        output += lookup[tmp << 2 & 0x3F];
        output += '=';
    }
    parts.push(output);
    return parts.join('');
}
function read(buffer, offset, isLE, mLen, nBytes) {
    var e, m;
    var eLen = nBytes * 8 - mLen - 1;
    var eMax = (1 << eLen) - 1;
    var eBias = eMax >> 1;
    var nBits = -7;
    var i = isLE ? nBytes - 1 : 0;
    var d = isLE ? -1 : 1;
    var s = buffer[offset + i];
    i += d;
    e = s & (1 << -nBits) - 1;
    s >>= -nBits;
    nBits += eLen;
    for(; nBits > 0; e = e * 256 + buffer[offset + i], i += d, nBits -= 8){}
    m = e & (1 << -nBits) - 1;
    e >>= -nBits;
    nBits += mLen;
    for(; nBits > 0; m = m * 256 + buffer[offset + i], i += d, nBits -= 8){}
    if (e === 0) {
        e = 1 - eBias;
    } else if (e === eMax) {
        return m ? NaN : (s ? -1 : 1) * Infinity;
    } else {
        m = m + Math.pow(2, mLen);
        e = e - eBias;
    }
    return (s ? -1 : 1) * m * Math.pow(2, e - mLen);
}
function write(buffer, value, offset, isLE, mLen, nBytes) {
    var e, m, c;
    var eLen = nBytes * 8 - mLen - 1;
    var eMax = (1 << eLen) - 1;
    var eBias = eMax >> 1;
    var rt = mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0;
    var i = isLE ? 0 : nBytes - 1;
    var d = isLE ? 1 : -1;
    var s = value < 0 || value === 0 && 1 / value < 0 ? 1 : 0;
    value = Math.abs(value);
    if (isNaN(value) || value === Infinity) {
        m = isNaN(value) ? 1 : 0;
        e = eMax;
    } else {
        e = Math.floor(Math.log(value) / Math.LN2);
        if (value * (c = Math.pow(2, -e)) < 1) {
            e--;
            c *= 2;
        }
        if (e + eBias >= 1) {
            value += rt / c;
        } else {
            value += rt * Math.pow(2, 1 - eBias);
        }
        if (value * c >= 2) {
            e++;
            c /= 2;
        }
        if (e + eBias >= eMax) {
            m = 0;
            e = eMax;
        } else if (e + eBias >= 1) {
            m = (value * c - 1) * Math.pow(2, mLen);
            e = e + eBias;
        } else {
            m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen);
            e = 0;
        }
    }
    for(; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8){}
    e = e << mLen | m;
    eLen += mLen;
    for(; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8){}
    buffer[offset + i - d] |= s * 128;
}
var toString = {}.toString;
var isArray = Array.isArray || function(arr) {
    return toString.call(arr) == '[object Array]';
};
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */ /* eslint-disable no-proto */ var INSPECT_MAX_BYTES = 50;
/**
 * If `Buffer.TYPED_ARRAY_SUPPORT`:
 *   === true    Use Uint8Array implementation (fastest)
 *   === false   Use Object implementation (most compatible, even IE6)
 *
 * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,
 * Opera 11.6+, iOS 4.2+.
 *
 * Due to various browser bugs, sometimes the Object implementation will be used even
 * when the browser supports typed arrays.
 *
 * Note:
 *
 *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,
 *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.
 *
 *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.
 *
 *   - IE10 has a broken `TypedArray.prototype.subarray` function which returns arrays of
 *     incorrect length in some situations.

 * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they
 * get the Object implementation, which is slower but behaves correctly.
 */ Buffer.TYPED_ARRAY_SUPPORT = global$1.TYPED_ARRAY_SUPPORT !== undefined ? global$1.TYPED_ARRAY_SUPPORT : true;
/*
 * Export kMaxLength after typed array support is determined.
 */ kMaxLength();
function kMaxLength() {
    return Buffer.TYPED_ARRAY_SUPPORT ? 0x7fffffff : 0x3fffffff;
}
function createBuffer(that, length) {
    if (kMaxLength() < length) {
        throw new RangeError('Invalid typed array length');
    }
    if (Buffer.TYPED_ARRAY_SUPPORT) {
        // Return an augmented `Uint8Array` instance, for best performance
        that = new Uint8Array(length);
        that.__proto__ = Buffer.prototype;
    } else {
        // Fallback: Return an object instance of the Buffer class
        if (that === null) {
            that = new Buffer(length);
        }
        that.length = length;
    }
    return that;
}
/**
 * The Buffer constructor returns instances of `Uint8Array` that have their
 * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of
 * `Uint8Array`, so the returned instances will have all the node `Buffer` methods
 * and the `Uint8Array` methods. Square bracket notation works as expected -- it
 * returns a single octet.
 *
 * The `Uint8Array` prototype remains unmodified.
 */ function Buffer(arg, encodingOrOffset, length) {
    if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {
        return new Buffer(arg, encodingOrOffset, length);
    }
    // Common case.
    if (typeof arg === 'number') {
        if (typeof encodingOrOffset === 'string') {
            throw new Error('If encoding is specified then the first argument must be a string');
        }
        return allocUnsafe(this, arg);
    }
    return from(this, arg, encodingOrOffset, length);
}
Buffer.poolSize = 8192; // not used by this implementation
// TODO: Legacy, not needed anymore. Remove in next major version.
Buffer._augment = function(arr) {
    arr.__proto__ = Buffer.prototype;
    return arr;
};
function from(that, value, encodingOrOffset, length) {
    if (typeof value === 'number') {
        throw new TypeError('"value" argument must not be a number');
    }
    if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {
        return fromArrayBuffer(that, value, encodingOrOffset, length);
    }
    if (typeof value === 'string') {
        return fromString(that, value, encodingOrOffset);
    }
    return fromObject(that, value);
}
/**
 * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError
 * if value is a number.
 * Buffer.from(str[, encoding])
 * Buffer.from(array)
 * Buffer.from(buffer)
 * Buffer.from(arrayBuffer[, byteOffset[, length]])
 **/ Buffer.from = function(value, encodingOrOffset, length) {
    return from(null, value, encodingOrOffset, length);
};
if (Buffer.TYPED_ARRAY_SUPPORT) {
    Buffer.prototype.__proto__ = Uint8Array.prototype;
    Buffer.__proto__ = Uint8Array;
    if (typeof Symbol !== 'undefined' && Symbol.species && Buffer[Symbol.species] === Buffer) ;
}
function assertSize(size) {
    if (typeof size !== 'number') {
        throw new TypeError('"size" argument must be a number');
    } else if (size < 0) {
        throw new RangeError('"size" argument must not be negative');
    }
}
function alloc(that, size, fill, encoding) {
    assertSize(size);
    if (size <= 0) {
        return createBuffer(that, size);
    }
    if (fill !== undefined) {
        // Only pay attention to encoding if it's a string. This
        // prevents accidentally sending in a number that would
        // be interpretted as a start offset.
        return typeof encoding === 'string' ? createBuffer(that, size).fill(fill, encoding) : createBuffer(that, size).fill(fill);
    }
    return createBuffer(that, size);
}
/**
 * Creates a new filled Buffer instance.
 * alloc(size[, fill[, encoding]])
 **/ Buffer.alloc = function(size, fill, encoding) {
    return alloc(null, size, fill, encoding);
};
function allocUnsafe(that, size) {
    assertSize(size);
    that = createBuffer(that, size < 0 ? 0 : checked(size) | 0);
    if (!Buffer.TYPED_ARRAY_SUPPORT) {
        for(var i = 0; i < size; ++i){
            that[i] = 0;
        }
    }
    return that;
}
/**
 * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.
 * */ Buffer.allocUnsafe = function(size) {
    return allocUnsafe(null, size);
};
/**
 * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.
 */ Buffer.allocUnsafeSlow = function(size) {
    return allocUnsafe(null, size);
};
function fromString(that, string, encoding) {
    if (typeof encoding !== 'string' || encoding === '') {
        encoding = 'utf8';
    }
    if (!Buffer.isEncoding(encoding)) {
        throw new TypeError('"encoding" must be a valid string encoding');
    }
    var length = byteLength(string, encoding) | 0;
    that = createBuffer(that, length);
    var actual = that.write(string, encoding);
    if (actual !== length) {
        // Writing a hex string, for example, that contains invalid characters will
        // cause everything after the first invalid character to be ignored. (e.g.
        // 'abxxcd' will be treated as 'ab')
        that = that.slice(0, actual);
    }
    return that;
}
function fromArrayLike(that, array) {
    var length = array.length < 0 ? 0 : checked(array.length) | 0;
    that = createBuffer(that, length);
    for(var i = 0; i < length; i += 1){
        that[i] = array[i] & 255;
    }
    return that;
}
function fromArrayBuffer(that, array, byteOffset, length) {
    array.byteLength; // this throws if `array` is not a valid ArrayBuffer
    if (byteOffset < 0 || array.byteLength < byteOffset) {
        throw new RangeError('\'offset\' is out of bounds');
    }
    if (array.byteLength < byteOffset + (length || 0)) {
        throw new RangeError('\'length\' is out of bounds');
    }
    if (byteOffset === undefined && length === undefined) {
        array = new Uint8Array(array);
    } else if (length === undefined) {
        array = new Uint8Array(array, byteOffset);
    } else {
        array = new Uint8Array(array, byteOffset, length);
    }
    if (Buffer.TYPED_ARRAY_SUPPORT) {
        // Return an augmented `Uint8Array` instance, for best performance
        that = array;
        that.__proto__ = Buffer.prototype;
    } else {
        // Fallback: Return an object instance of the Buffer class
        that = fromArrayLike(that, array);
    }
    return that;
}
function fromObject(that, obj) {
    if (internalIsBuffer(obj)) {
        var len = checked(obj.length) | 0;
        that = createBuffer(that, len);
        if (that.length === 0) {
            return that;
        }
        obj.copy(that, 0, 0, len);
        return that;
    }
    if (obj) {
        if (typeof ArrayBuffer !== 'undefined' && obj.buffer instanceof ArrayBuffer || 'length' in obj) {
            if (typeof obj.length !== 'number' || isnan(obj.length)) {
                return createBuffer(that, 0);
            }
            return fromArrayLike(that, obj);
        }
        if (obj.type === 'Buffer' && isArray(obj.data)) {
            return fromArrayLike(that, obj.data);
        }
    }
    throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.');
}
function checked(length) {
    // Note: cannot use `length < kMaxLength()` here because that fails when
    // length is NaN (which is otherwise coerced to zero.)
    if (length >= kMaxLength()) {
        throw new RangeError('Attempt to allocate Buffer larger than maximum ' + 'size: 0x' + kMaxLength().toString(16) + ' bytes');
    }
    return length | 0;
}
Buffer.isBuffer = isBuffer$1;
function internalIsBuffer(b) {
    return !!(b != null && b._isBuffer);
}
Buffer.compare = function compare(a, b) {
    if (!internalIsBuffer(a) || !internalIsBuffer(b)) {
        throw new TypeError('Arguments must be Buffers');
    }
    if (a === b) return 0;
    var x = a.length;
    var y = b.length;
    for(var i = 0, len = Math.min(x, y); i < len; ++i){
        if (a[i] !== b[i]) {
            x = a[i];
            y = b[i];
            break;
        }
    }
    if (x < y) return -1;
    if (y < x) return 1;
    return 0;
};
Buffer.isEncoding = function isEncoding(encoding) {
    switch(String(encoding).toLowerCase()){
        case 'hex':
        case 'utf8':
        case 'utf-8':
        case 'ascii':
        case 'latin1':
        case 'binary':
        case 'base64':
        case 'ucs2':
        case 'ucs-2':
        case 'utf16le':
        case 'utf-16le':
            return true;
        default:
            return false;
    }
};
Buffer.concat = function concat(list, length) {
    if (!isArray(list)) {
        throw new TypeError('"list" argument must be an Array of Buffers');
    }
    if (list.length === 0) {
        return Buffer.alloc(0);
    }
    var i;
    if (length === undefined) {
        length = 0;
        for(i = 0; i < list.length; ++i){
            length += list[i].length;
        }
    }
    var buffer = Buffer.allocUnsafe(length);
    var pos = 0;
    for(i = 0; i < list.length; ++i){
        var buf = list[i];
        if (!internalIsBuffer(buf)) {
            throw new TypeError('"list" argument must be an Array of Buffers');
        }
        buf.copy(buffer, pos);
        pos += buf.length;
    }
    return buffer;
};
function byteLength(string, encoding) {
    if (internalIsBuffer(string)) {
        return string.length;
    }
    if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' && (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {
        return string.byteLength;
    }
    if (typeof string !== 'string') {
        string = '' + string;
    }
    var len = string.length;
    if (len === 0) return 0;
    // Use a for loop to avoid recursion
    var loweredCase = false;
    for(;;){
        switch(encoding){
            case 'ascii':
            case 'latin1':
            case 'binary':
                return len;
            case 'utf8':
            case 'utf-8':
            case undefined:
                return utf8ToBytes(string).length;
            case 'ucs2':
            case 'ucs-2':
            case 'utf16le':
            case 'utf-16le':
                return len * 2;
            case 'hex':
                return len >>> 1;
            case 'base64':
                return base64ToBytes(string).length;
            default:
                if (loweredCase) return utf8ToBytes(string).length // assume utf8
                ;
                encoding = ('' + encoding).toLowerCase();
                loweredCase = true;
        }
    }
}
Buffer.byteLength = byteLength;
function slowToString(encoding, start, end) {
    var loweredCase = false;
    // No need to verify that "this.length <= MAX_UINT32" since it's a read-only
    // property of a typed array.
    // This behaves neither like String nor Uint8Array in that we set start/end
    // to their upper/lower bounds if the value passed is out of range.
    // undefined is handled specially as per ECMA-262 6th Edition,
    // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.
    if (start === undefined || start < 0) {
        start = 0;
    }
    // Return early if start > this.length. Done here to prevent potential uint32
    // coercion fail below.
    if (start > this.length) {
        return '';
    }
    if (end === undefined || end > this.length) {
        end = this.length;
    }
    if (end <= 0) {
        return '';
    }
    // Force coersion to uint32. This will also coerce falsey/NaN values to 0.
    end >>>= 0;
    start >>>= 0;
    if (end <= start) {
        return '';
    }
    if (!encoding) encoding = 'utf8';
    while(true){
        switch(encoding){
            case 'hex':
                return hexSlice(this, start, end);
            case 'utf8':
            case 'utf-8':
                return utf8Slice(this, start, end);
            case 'ascii':
                return asciiSlice(this, start, end);
            case 'latin1':
            case 'binary':
                return latin1Slice(this, start, end);
            case 'base64':
                return base64Slice(this, start, end);
            case 'ucs2':
            case 'ucs-2':
            case 'utf16le':
            case 'utf-16le':
                return utf16leSlice(this, start, end);
            default:
                if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);
                encoding = (encoding + '').toLowerCase();
                loweredCase = true;
        }
    }
}
// The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect
// Buffer instances.
Buffer.prototype._isBuffer = true;
function swap(b, n, m) {
    var i = b[n];
    b[n] = b[m];
    b[m] = i;
}
Buffer.prototype.swap16 = function swap16() {
    var len = this.length;
    if (len % 2 !== 0) {
        throw new RangeError('Buffer size must be a multiple of 16-bits');
    }
    for(var i = 0; i < len; i += 2){
        swap(this, i, i + 1);
    }
    return this;
};
Buffer.prototype.swap32 = function swap32() {
    var len = this.length;
    if (len % 4 !== 0) {
        throw new RangeError('Buffer size must be a multiple of 32-bits');
    }
    for(var i = 0; i < len; i += 4){
        swap(this, i, i + 3);
        swap(this, i + 1, i + 2);
    }
    return this;
};
Buffer.prototype.swap64 = function swap64() {
    var len = this.length;
    if (len % 8 !== 0) {
        throw new RangeError('Buffer size must be a multiple of 64-bits');
    }
    for(var i = 0; i < len; i += 8){
        swap(this, i, i + 7);
        swap(this, i + 1, i + 6);
        swap(this, i + 2, i + 5);
        swap(this, i + 3, i + 4);
    }
    return this;
};
Buffer.prototype.toString = function toString() {
    var length = this.length | 0;
    if (length === 0) return '';
    if (arguments.length === 0) return utf8Slice(this, 0, length);
    return slowToString.apply(this, arguments);
};
Buffer.prototype.equals = function equals(b) {
    if (!internalIsBuffer(b)) throw new TypeError('Argument must be a Buffer');
    if (this === b) return true;
    return Buffer.compare(this, b) === 0;
};
Buffer.prototype.inspect = function inspect() {
    var str = '';
    var max = INSPECT_MAX_BYTES;
    if (this.length > 0) {
        str = this.toString('hex', 0, max).match(/.{2}/g).join(' ');
        if (this.length > max) str += ' ... ';
    }
    return '<Buffer ' + str + '>';
};
Buffer.prototype.compare = function compare(target, start, end, thisStart, thisEnd) {
    if (!internalIsBuffer(target)) {
        throw new TypeError('Argument must be a Buffer');
    }
    if (start === undefined) {
        start = 0;
    }
    if (end === undefined) {
        end = target ? target.length : 0;
    }
    if (thisStart === undefined) {
        thisStart = 0;
    }
    if (thisEnd === undefined) {
        thisEnd = this.length;
    }
    if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {
        throw new RangeError('out of range index');
    }
    if (thisStart >= thisEnd && start >= end) {
        return 0;
    }
    if (thisStart >= thisEnd) {
        return -1;
    }
    if (start >= end) {
        return 1;
    }
    start >>>= 0;
    end >>>= 0;
    thisStart >>>= 0;
    thisEnd >>>= 0;
    if (this === target) return 0;
    var x = thisEnd - thisStart;
    var y = end - start;
    var len = Math.min(x, y);
    var thisCopy = this.slice(thisStart, thisEnd);
    var targetCopy = target.slice(start, end);
    for(var i = 0; i < len; ++i){
        if (thisCopy[i] !== targetCopy[i]) {
            x = thisCopy[i];
            y = targetCopy[i];
            break;
        }
    }
    if (x < y) return -1;
    if (y < x) return 1;
    return 0;
};
// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,
// OR the last index of `val` in `buffer` at offset <= `byteOffset`.
//
// Arguments:
// - buffer - a Buffer to search
// - val - a string, Buffer, or number
// - byteOffset - an index into `buffer`; will be clamped to an int32
// - encoding - an optional encoding, relevant is val is a string
// - dir - true for indexOf, false for lastIndexOf
function bidirectionalIndexOf(buffer, val, byteOffset, encoding, dir) {
    // Empty buffer means no match
    if (buffer.length === 0) return -1;
    // Normalize byteOffset
    if (typeof byteOffset === 'string') {
        encoding = byteOffset;
        byteOffset = 0;
    } else if (byteOffset > 0x7fffffff) {
        byteOffset = 0x7fffffff;
    } else if (byteOffset < -2147483648) {
        byteOffset = -2147483648;
    }
    byteOffset = +byteOffset; // Coerce to Number.
    if (isNaN(byteOffset)) {
        // byteOffset: it it's undefined, null, NaN, "foo", etc, search whole buffer
        byteOffset = dir ? 0 : buffer.length - 1;
    }
    // Normalize byteOffset: negative offsets start from the end of the buffer
    if (byteOffset < 0) byteOffset = buffer.length + byteOffset;
    if (byteOffset >= buffer.length) {
        if (dir) return -1;
        else byteOffset = buffer.length - 1;
    } else if (byteOffset < 0) {
        if (dir) byteOffset = 0;
        else return -1;
    }
    // Normalize val
    if (typeof val === 'string') {
        val = Buffer.from(val, encoding);
    }
    // Finally, search either indexOf (if dir is true) or lastIndexOf
    if (internalIsBuffer(val)) {
        // Special case: looking for empty string/buffer always fails
        if (val.length === 0) {
            return -1;
        }
        return arrayIndexOf(buffer, val, byteOffset, encoding, dir);
    } else if (typeof val === 'number') {
        val = val & 0xFF; // Search for a byte value [0-255]
        if (Buffer.TYPED_ARRAY_SUPPORT && typeof Uint8Array.prototype.indexOf === 'function') {
            if (dir) {
                return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset);
            } else {
                return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset);
            }
        }
        return arrayIndexOf(buffer, [
            val
        ], byteOffset, encoding, dir);
    }
    throw new TypeError('val must be string, number or Buffer');
}
function arrayIndexOf(arr, val, byteOffset, encoding, dir) {
    var indexSize = 1;
    var arrLength = arr.length;
    var valLength = val.length;
    if (encoding !== undefined) {
        encoding = String(encoding).toLowerCase();
        if (encoding === 'ucs2' || encoding === 'ucs-2' || encoding === 'utf16le' || encoding === 'utf-16le') {
            if (arr.length < 2 || val.length < 2) {
                return -1;
            }
            indexSize = 2;
            arrLength /= 2;
            valLength /= 2;
            byteOffset /= 2;
        }
    }
    function read(buf, i) {
        if (indexSize === 1) {
            return buf[i];
        } else {
            return buf.readUInt16BE(i * indexSize);
        }
    }
    var i;
    if (dir) {
        var foundIndex = -1;
        for(i = byteOffset; i < arrLength; i++){
            if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {
                if (foundIndex === -1) foundIndex = i;
                if (i - foundIndex + 1 === valLength) return foundIndex * indexSize;
            } else {
                if (foundIndex !== -1) i -= i - foundIndex;
                foundIndex = -1;
            }
        }
    } else {
        if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength;
        for(i = byteOffset; i >= 0; i--){
            var found = true;
            for(var j = 0; j < valLength; j++){
                if (read(arr, i + j) !== read(val, j)) {
                    found = false;
                    break;
                }
            }
            if (found) return i;
        }
    }
    return -1;
}
Buffer.prototype.includes = function includes(val, byteOffset, encoding) {
    return this.indexOf(val, byteOffset, encoding) !== -1;
};
Buffer.prototype.indexOf = function indexOf(val, byteOffset, encoding) {
    return bidirectionalIndexOf(this, val, byteOffset, encoding, true);
};
Buffer.prototype.lastIndexOf = function lastIndexOf(val, byteOffset, encoding) {
    return bidirectionalIndexOf(this, val, byteOffset, encoding, false);
};
function hexWrite(buf, string, offset, length) {
    offset = Number(offset) || 0;
    var remaining = buf.length - offset;
    if (!length) {
        length = remaining;
    } else {
        length = Number(length);
        if (length > remaining) {
            length = remaining;
        }
    }
    // must be an even number of digits
    var strLen = string.length;
    if (strLen % 2 !== 0) throw new TypeError('Invalid hex string');
    if (length > strLen / 2) {
        length = strLen / 2;
    }
    for(var i = 0; i < length; ++i){
        var parsed = parseInt(string.substr(i * 2, 2), 16);
        if (isNaN(parsed)) return i;
        buf[offset + i] = parsed;
    }
    return i;
}
function utf8Write(buf, string, offset, length) {
    return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length);
}
function asciiWrite(buf, string, offset, length) {
    return blitBuffer(asciiToBytes(string), buf, offset, length);
}
function latin1Write(buf, string, offset, length) {
    return asciiWrite(buf, string, offset, length);
}
function base64Write(buf, string, offset, length) {
    return blitBuffer(base64ToBytes(string), buf, offset, length);
}
function ucs2Write(buf, string, offset, length) {
    return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length);
}
Buffer.prototype.write = function write(string, offset, length, encoding) {
    // Buffer#write(string)
    if (offset === undefined) {
        encoding = 'utf8';
        length = this.length;
        offset = 0;
    // Buffer#write(string, encoding)
    } else if (length === undefined && typeof offset === 'string') {
        encoding = offset;
        length = this.length;
        offset = 0;
    // Buffer#write(string, offset[, length][, encoding])
    } else if (isFinite(offset)) {
        offset = offset | 0;
        if (isFinite(length)) {
            length = length | 0;
            if (encoding === undefined) encoding = 'utf8';
        } else {
            encoding = length;
            length = undefined;
        }
    // legacy write(string, encoding, offset, length) - remove in v0.13
    } else {
        throw new Error('Buffer.write(string, encoding, offset[, length]) is no longer supported');
    }
    var remaining = this.length - offset;
    if (length === undefined || length > remaining) length = remaining;
    if (string.length > 0 && (length < 0 || offset < 0) || offset > this.length) {
        throw new RangeError('Attempt to write outside buffer bounds');
    }
    if (!encoding) encoding = 'utf8';
    var loweredCase = false;
    for(;;){
        switch(encoding){
            case 'hex':
                return hexWrite(this, string, offset, length);
            case 'utf8':
            case 'utf-8':
                return utf8Write(this, string, offset, length);
            case 'ascii':
                return asciiWrite(this, string, offset, length);
            case 'latin1':
            case 'binary':
                return latin1Write(this, string, offset, length);
            case 'base64':
                // Warning: maxLength not taken into account in base64Write
                return base64Write(this, string, offset, length);
            case 'ucs2':
            case 'ucs-2':
            case 'utf16le':
            case 'utf-16le':
                return ucs2Write(this, string, offset, length);
            default:
                if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding);
                encoding = ('' + encoding).toLowerCase();
                loweredCase = true;
        }
    }
};
Buffer.prototype.toJSON = function toJSON() {
    return {
        type: 'Buffer',
        data: Array.prototype.slice.call(this._arr || this, 0)
    };
};
function base64Slice(buf, start, end) {
    if (start === 0 && end === buf.length) {
        return fromByteArray(buf);
    } else {
        return fromByteArray(buf.slice(start, end));
    }
}
function utf8Slice(buf, start, end) {
    end = Math.min(buf.length, end);
    var res = [];
    var i = start;
    while(i < end){
        var firstByte = buf[i];
        var codePoint = null;
        var bytesPerSequence = firstByte > 0xEF ? 4 : firstByte > 0xDF ? 3 : firstByte > 0xBF ? 2 : 1;
        if (i + bytesPerSequence <= end) {
            var secondByte, thirdByte, fourthByte, tempCodePoint;
            switch(bytesPerSequence){
                case 1:
                    if (firstByte < 0x80) {
                        codePoint = firstByte;
                    }
                    break;
                case 2:
                    secondByte = buf[i + 1];
                    if ((secondByte & 0xC0) === 0x80) {
                        tempCodePoint = (firstByte & 0x1F) << 0x6 | secondByte & 0x3F;
                        if (tempCodePoint > 0x7F) {
                            codePoint = tempCodePoint;
                        }
                    }
                    break;
                case 3:
                    secondByte = buf[i + 1];
                    thirdByte = buf[i + 2];
                    if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {
                        tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | thirdByte & 0x3F;
                        if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {
                            codePoint = tempCodePoint;
                        }
                    }
                    break;
                case 4:
                    secondByte = buf[i + 1];
                    thirdByte = buf[i + 2];
                    fourthByte = buf[i + 3];
                    if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {
                        tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | fourthByte & 0x3F;
                        if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {
                            codePoint = tempCodePoint;
                        }
                    }
            }
        }
        if (codePoint === null) {
            // we did not generate a valid codePoint so insert a
            // replacement char (U+FFFD) and advance only 1 byte
            codePoint = 0xFFFD;
            bytesPerSequence = 1;
        } else if (codePoint > 0xFFFF) {
            // encode to utf16 (surrogate pair dance)
            codePoint -= 0x10000;
            res.push(codePoint >>> 10 & 0x3FF | 0xD800);
            codePoint = 0xDC00 | codePoint & 0x3FF;
        }
        res.push(codePoint);
        i += bytesPerSequence;
    }
    return decodeCodePointsArray(res);
}
// Based on http://stackoverflow.com/a/22747272/680742, the browser with
// the lowest limit is Chrome, with 0x10000 args.
// We go 1 magnitude less, for safety
var MAX_ARGUMENTS_LENGTH = 0x1000;
function decodeCodePointsArray(codePoints) {
    var len = codePoints.length;
    if (len <= MAX_ARGUMENTS_LENGTH) {
        return String.fromCharCode.apply(String, codePoints) // avoid extra slice()
        ;
    }
    // Decode in chunks to avoid "call stack size exceeded".
    var res = '';
    var i = 0;
    while(i < len){
        res += String.fromCharCode.apply(String, codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH));
    }
    return res;
}
function asciiSlice(buf, start, end) {
    var ret = '';
    end = Math.min(buf.length, end);
    for(var i = start; i < end; ++i){
        ret += String.fromCharCode(buf[i] & 0x7F);
    }
    return ret;
}
function latin1Slice(buf, start, end) {
    var ret = '';
    end = Math.min(buf.length, end);
    for(var i = start; i < end; ++i){
        ret += String.fromCharCode(buf[i]);
    }
    return ret;
}
function hexSlice(buf, start, end) {
    var len = buf.length;
    if (!start || start < 0) start = 0;
    if (!end || end < 0 || end > len) end = len;
    var out = '';
    for(var i = start; i < end; ++i){
        out += toHex(buf[i]);
    }
    return out;
}
function utf16leSlice(buf, start, end) {
    var bytes = buf.slice(start, end);
    var res = '';
    for(var i = 0; i < bytes.length; i += 2){
        res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256);
    }
    return res;
}
Buffer.prototype.slice = function slice(start, end) {
    var len = this.length;
    start = ~~start;
    end = end === undefined ? len : ~~end;
    if (start < 0) {
        start += len;
        if (start < 0) start = 0;
    } else if (start > len) {
        start = len;
    }
    if (end < 0) {
        end += len;
        if (end < 0) end = 0;
    } else if (end > len) {
        end = len;
    }
    if (end < start) end = start;
    var newBuf;
    if (Buffer.TYPED_ARRAY_SUPPORT) {
        newBuf = this.subarray(start, end);
        newBuf.__proto__ = Buffer.prototype;
    } else {
        var sliceLen = end - start;
        newBuf = new Buffer(sliceLen, undefined);
        for(var i = 0; i < sliceLen; ++i){
            newBuf[i] = this[i + start];
        }
    }
    return newBuf;
};
/*
 * Need to make sure that buffer isn't trying to write out of bounds.
 */ function checkOffset(offset, ext, length) {
    if (offset % 1 !== 0 || offset < 0) throw new RangeError('offset is not uint');
    if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length');
}
Buffer.prototype.readUIntLE = function readUIntLE(offset, byteLength, noAssert) {
    offset = offset | 0;
    byteLength = byteLength | 0;
    if (!noAssert) checkOffset(offset, byteLength, this.length);
    var val = this[offset];
    var mul = 1;
    var i = 0;
    while(++i < byteLength && (mul *= 0x100)){
        val += this[offset + i] * mul;
    }
    return val;
};
Buffer.prototype.readUIntBE = function readUIntBE(offset, byteLength, noAssert) {
    offset = offset | 0;
    byteLength = byteLength | 0;
    if (!noAssert) {
        checkOffset(offset, byteLength, this.length);
    }
    var val = this[offset + --byteLength];
    var mul = 1;
    while(byteLength > 0 && (mul *= 0x100)){
        val += this[offset + --byteLength] * mul;
    }
    return val;
};
Buffer.prototype.readUInt8 = function readUInt8(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 1, this.length);
    return this[offset];
};
Buffer.prototype.readUInt16LE = function readUInt16LE(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 2, this.length);
    return this[offset] | this[offset + 1] << 8;
};
Buffer.prototype.readUInt16BE = function readUInt16BE(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 2, this.length);
    return this[offset] << 8 | this[offset + 1];
};
Buffer.prototype.readUInt32LE = function readUInt32LE(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 4, this.length);
    return (this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16) + this[offset + 3] * 0x1000000;
};
Buffer.prototype.readUInt32BE = function readUInt32BE(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 4, this.length);
    return this[offset] * 0x1000000 + (this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3]);
};
Buffer.prototype.readIntLE = function readIntLE(offset, byteLength, noAssert) {
    offset = offset | 0;
    byteLength = byteLength | 0;
    if (!noAssert) checkOffset(offset, byteLength, this.length);
    var val = this[offset];
    var mul = 1;
    var i = 0;
    while(++i < byteLength && (mul *= 0x100)){
        val += this[offset + i] * mul;
    }
    mul *= 0x80;
    if (val >= mul) val -= Math.pow(2, 8 * byteLength);
    return val;
};
Buffer.prototype.readIntBE = function readIntBE(offset, byteLength, noAssert) {
    offset = offset | 0;
    byteLength = byteLength | 0;
    if (!noAssert) checkOffset(offset, byteLength, this.length);
    var i = byteLength;
    var mul = 1;
    var val = this[offset + --i];
    while(i > 0 && (mul *= 0x100)){
        val += this[offset + --i] * mul;
    }
    mul *= 0x80;
    if (val >= mul) val -= Math.pow(2, 8 * byteLength);
    return val;
};
Buffer.prototype.readInt8 = function readInt8(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 1, this.length);
    if (!(this[offset] & 0x80)) return this[offset];
    return (0xff - this[offset] + 1) * -1;
};
Buffer.prototype.readInt16LE = function readInt16LE(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 2, this.length);
    var val = this[offset] | this[offset + 1] << 8;
    return val & 0x8000 ? val | 0xFFFF0000 : val;
};
Buffer.prototype.readInt16BE = function readInt16BE(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 2, this.length);
    var val = this[offset + 1] | this[offset] << 8;
    return val & 0x8000 ? val | 0xFFFF0000 : val;
};
Buffer.prototype.readInt32LE = function readInt32LE(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 4, this.length);
    return this[offset] | this[offset + 1] << 8 | this[offset + 2] << 16 | this[offset + 3] << 24;
};
Buffer.prototype.readInt32BE = function readInt32BE(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 4, this.length);
    return this[offset] << 24 | this[offset + 1] << 16 | this[offset + 2] << 8 | this[offset + 3];
};
Buffer.prototype.readFloatLE = function readFloatLE(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 4, this.length);
    return read(this, offset, true, 23, 4);
};
Buffer.prototype.readFloatBE = function readFloatBE(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 4, this.length);
    return read(this, offset, false, 23, 4);
};
Buffer.prototype.readDoubleLE = function readDoubleLE(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 8, this.length);
    return read(this, offset, true, 52, 8);
};
Buffer.prototype.readDoubleBE = function readDoubleBE(offset, noAssert) {
    if (!noAssert) checkOffset(offset, 8, this.length);
    return read(this, offset, false, 52, 8);
};
function checkInt(buf, value, offset, ext, max, min) {
    if (!internalIsBuffer(buf)) throw new TypeError('"buffer" argument must be a Buffer instance');
    if (value > max || value < min) throw new RangeError('"value" argument is out of bounds');
    if (offset + ext > buf.length) throw new RangeError('Index out of range');
}
Buffer.prototype.writeUIntLE = function writeUIntLE(value, offset, byteLength, noAssert) {
    value = +value;
    offset = offset | 0;
    byteLength = byteLength | 0;
    if (!noAssert) {
        var maxBytes = Math.pow(2, 8 * byteLength) - 1;
        checkInt(this, value, offset, byteLength, maxBytes, 0);
    }
    var mul = 1;
    var i = 0;
    this[offset] = value & 0xFF;
    while(++i < byteLength && (mul *= 0x100)){
        this[offset + i] = value / mul & 0xFF;
    }
    return offset + byteLength;
};
Buffer.prototype.writeUIntBE = function writeUIntBE(value, offset, byteLength, noAssert) {
    value = +value;
    offset = offset | 0;
    byteLength = byteLength | 0;
    if (!noAssert) {
        var maxBytes = Math.pow(2, 8 * byteLength) - 1;
        checkInt(this, value, offset, byteLength, maxBytes, 0);
    }
    var i = byteLength - 1;
    var mul = 1;
    this[offset + i] = value & 0xFF;
    while(--i >= 0 && (mul *= 0x100)){
        this[offset + i] = value / mul & 0xFF;
    }
    return offset + byteLength;
};
Buffer.prototype.writeUInt8 = function writeUInt8(value, offset, noAssert) {
    value = +value;
    offset = offset | 0;
    if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0);
    if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);
    this[offset] = value & 0xff;
    return offset + 1;
};
function objectWriteUInt16(buf, value, offset, littleEndian) {
    if (value < 0) value = 0xffff + value + 1;
    for(var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i){
        buf[offset + i] = (value & 0xff << 8 * (littleEndian ? i : 1 - i)) >>> (littleEndian ? i : 1 - i) * 8;
    }
}
Buffer.prototype.writeUInt16LE = function writeUInt16LE(value, offset, noAssert) {
    value = +value;
    offset = offset | 0;
    if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);
    if (Buffer.TYPED_ARRAY_SUPPORT) {
        this[offset] = value & 0xff;
        this[offset + 1] = value >>> 8;
    } else {
        objectWriteUInt16(this, value, offset, true);
    }
    return offset + 2;
};
Buffer.prototype.writeUInt16BE = function writeUInt16BE(value, offset, noAssert) {
    value = +value;
    offset = offset | 0;
    if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);
    if (Buffer.TYPED_ARRAY_SUPPORT) {
        this[offset] = value >>> 8;
        this[offset + 1] = value & 0xff;
    } else {
        objectWriteUInt16(this, value, offset, false);
    }
    return offset + 2;
};
function objectWriteUInt32(buf, value, offset, littleEndian) {
    if (value < 0) value = 0xffffffff + value + 1;
    for(var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i){
        buf[offset + i] = value >>> (littleEndian ? i : 3 - i) * 8 & 0xff;
    }
}
Buffer.prototype.writeUInt32LE = function writeUInt32LE(value, offset, noAssert) {
    value = +value;
    offset = offset | 0;
    if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);
    if (Buffer.TYPED_ARRAY_SUPPORT) {
        this[offset + 3] = value >>> 24;
        this[offset + 2] = value >>> 16;
        this[offset + 1] = value >>> 8;
        this[offset] = value & 0xff;
    } else {
        objectWriteUInt32(this, value, offset, true);
    }
    return offset + 4;
};
Buffer.prototype.writeUInt32BE = function writeUInt32BE(value, offset, noAssert) {
    value = +value;
    offset = offset | 0;
    if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);
    if (Buffer.TYPED_ARRAY_SUPPORT) {
        this[offset] = value >>> 24;
        this[offset + 1] = value >>> 16;
        this[offset + 2] = value >>> 8;
        this[offset + 3] = value & 0xff;
    } else {
        objectWriteUInt32(this, value, offset, false);
    }
    return offset + 4;
};
Buffer.prototype.writeIntLE = function writeIntLE(value, offset, byteLength, noAssert) {
    value = +value;
    offset = offset | 0;
    if (!noAssert) {
        var limit = Math.pow(2, 8 * byteLength - 1);
        checkInt(this, value, offset, byteLength, limit - 1, -limit);
    }
    var i = 0;
    var mul = 1;
    var sub = 0;
    this[offset] = value & 0xFF;
    while(++i < byteLength && (mul *= 0x100)){
        if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {
            sub = 1;
        }
        this[offset + i] = (value / mul >> 0) - sub & 0xFF;
    }
    return offset + byteLength;
};
Buffer.prototype.writeIntBE = function writeIntBE(value, offset, byteLength, noAssert) {
    value = +value;
    offset = offset | 0;
    if (!noAssert) {
        var limit = Math.pow(2, 8 * byteLength - 1);
        checkInt(this, value, offset, byteLength, limit - 1, -limit);
    }
    var i = byteLength - 1;
    var mul = 1;
    var sub = 0;
    this[offset + i] = value & 0xFF;
    while(--i >= 0 && (mul *= 0x100)){
        if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {
            sub = 1;
        }
        this[offset + i] = (value / mul >> 0) - sub & 0xFF;
    }
    return offset + byteLength;
};
Buffer.prototype.writeInt8 = function writeInt8(value, offset, noAssert) {
    value = +value;
    offset = offset | 0;
    if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -128);
    if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);
    if (value < 0) value = 0xff + value + 1;
    this[offset] = value & 0xff;
    return offset + 1;
};
Buffer.prototype.writeInt16LE = function writeInt16LE(value, offset, noAssert) {
    value = +value;
    offset = offset | 0;
    if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -32768);
    if (Buffer.TYPED_ARRAY_SUPPORT) {
        this[offset] = value & 0xff;
        this[offset + 1] = value >>> 8;
    } else {
        objectWriteUInt16(this, value, offset, true);
    }
    return offset + 2;
};
Buffer.prototype.writeInt16BE = function writeInt16BE(value, offset, noAssert) {
    value = +value;
    offset = offset | 0;
    if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -32768);
    if (Buffer.TYPED_ARRAY_SUPPORT) {
        this[offset] = value >>> 8;
        this[offset + 1] = value & 0xff;
    } else {
        objectWriteUInt16(this, value, offset, false);
    }
    return offset + 2;
};
Buffer.prototype.writeInt32LE = function writeInt32LE(value, offset, noAssert) {
    value = +value;
    offset = offset | 0;
    if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -2147483648);
    if (Buffer.TYPED_ARRAY_SUPPORT) {
        this[offset] = value & 0xff;
        this[offset + 1] = value >>> 8;
        this[offset + 2] = value >>> 16;
        this[offset + 3] = value >>> 24;
    } else {
        objectWriteUInt32(this, value, offset, true);
    }
    return offset + 4;
};
Buffer.prototype.writeInt32BE = function writeInt32BE(value, offset, noAssert) {
    value = +value;
    offset = offset | 0;
    if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -2147483648);
    if (value < 0) value = 0xffffffff + value + 1;
    if (Buffer.TYPED_ARRAY_SUPPORT) {
        this[offset] = value >>> 24;
        this[offset + 1] = value >>> 16;
        this[offset + 2] = value >>> 8;
        this[offset + 3] = value & 0xff;
    } else {
        objectWriteUInt32(this, value, offset, false);
    }
    return offset + 4;
};
function checkIEEE754(buf, value, offset, ext, max, min) {
    if (offset + ext > buf.length) throw new RangeError('Index out of range');
    if (offset < 0) throw new RangeError('Index out of range');
}
function writeFloat(buf, value, offset, littleEndian, noAssert) {
    if (!noAssert) {
        checkIEEE754(buf, value, offset, 4);
    }
    write(buf, value, offset, littleEndian, 23, 4);
    return offset + 4;
}
Buffer.prototype.writeFloatLE = function writeFloatLE(value, offset, noAssert) {
    return writeFloat(this, value, offset, true, noAssert);
};
Buffer.prototype.writeFloatBE = function writeFloatBE(value, offset, noAssert) {
    return writeFloat(this, value, offset, false, noAssert);
};
function writeDouble(buf, value, offset, littleEndian, noAssert) {
    if (!noAssert) {
        checkIEEE754(buf, value, offset, 8);
    }
    write(buf, value, offset, littleEndian, 52, 8);
    return offset + 8;
}
Buffer.prototype.writeDoubleLE = function writeDoubleLE(value, offset, noAssert) {
    return writeDouble(this, value, offset, true, noAssert);
};
Buffer.prototype.writeDoubleBE = function writeDoubleBE(value, offset, noAssert) {
    return writeDouble(this, value, offset, false, noAssert);
};
// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)
Buffer.prototype.copy = function copy(target, targetStart, start, end) {
    if (!start) start = 0;
    if (!end && end !== 0) end = this.length;
    if (targetStart >= target.length) targetStart = target.length;
    if (!targetStart) targetStart = 0;
    if (end > 0 && end < start) end = start;
    // Copy 0 bytes; we're done
    if (end === start) return 0;
    if (target.length === 0 || this.length === 0) return 0;
    // Fatal error conditions
    if (targetStart < 0) {
        throw new RangeError('targetStart out of bounds');
    }
    if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds');
    if (end < 0) throw new RangeError('sourceEnd out of bounds');
    // Are we oob?
    if (end > this.length) end = this.length;
    if (target.length - targetStart < end - start) {
        end = target.length - targetStart + start;
    }
    var len = end - start;
    var i;
    if (this === target && start < targetStart && targetStart < end) {
        // descending copy from end
        for(i = len - 1; i >= 0; --i){
            target[i + targetStart] = this[i + start];
        }
    } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {
        // ascending copy from start
        for(i = 0; i < len; ++i){
            target[i + targetStart] = this[i + start];
        }
    } else {
        Uint8Array.prototype.set.call(target, this.subarray(start, start + len), targetStart);
    }
    return len;
};
// Usage:
//    buffer.fill(number[, offset[, end]])
//    buffer.fill(buffer[, offset[, end]])
//    buffer.fill(string[, offset[, end]][, encoding])
Buffer.prototype.fill = function fill(val, start, end, encoding) {
    // Handle string cases:
    if (typeof val === 'string') {
        if (typeof start === 'string') {
            encoding = start;
            start = 0;
            end = this.length;
        } else if (typeof end === 'string') {
            encoding = end;
            end = this.length;
        }
        if (val.length === 1) {
            var code = val.charCodeAt(0);
            if (code < 256) {
                val = code;
            }
        }
        if (encoding !== undefined && typeof encoding !== 'string') {
            throw new TypeError('encoding must be a string');
        }
        if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {
            throw new TypeError('Unknown encoding: ' + encoding);
        }
    } else if (typeof val === 'number') {
        val = val & 255;
    }
    // Invalid ranges are not set to a default, so can range check early.
    if (start < 0 || this.length < start || this.length < end) {
        throw new RangeError('Out of range index');
    }
    if (end <= start) {
        return this;
    }
    start = start >>> 0;
    end = end === undefined ? this.length : end >>> 0;
    if (!val) val = 0;
    var i;
    if (typeof val === 'number') {
        for(i = start; i < end; ++i){
            this[i] = val;
        }
    } else {
        var bytes = internalIsBuffer(val) ? val : utf8ToBytes(new Buffer(val, encoding).toString());
        var len = bytes.length;
        for(i = 0; i < end - start; ++i){
            this[i + start] = bytes[i % len];
        }
    }
    return this;
};
// HELPER FUNCTIONS
// ================
var INVALID_BASE64_RE = /[^+\/0-9A-Za-z-_]/g;
function base64clean(str) {
    // Node strips out invalid characters like \n and \t from the string, base64-js does not
    str = stringtrim(str).replace(INVALID_BASE64_RE, '');
    // Node converts strings with length < 2 to ''
    if (str.length < 2) return '';
    // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not
    while(str.length % 4 !== 0){
        str = str + '=';
    }
    return str;
}
function stringtrim(str) {
    if (str.trim) return str.trim();
    return str.replace(/^\s+|\s+$/g, '');
}
function toHex(n) {
    if (n < 16) return '0' + n.toString(16);
    return n.toString(16);
}
function utf8ToBytes(string, units) {
    units = units || Infinity;
    var codePoint;
    var length = string.length;
    var leadSurrogate = null;
    var bytes = [];
    for(var i = 0; i < length; ++i){
        codePoint = string.charCodeAt(i);
        // is surrogate component
        if (codePoint > 0xD7FF && codePoint < 0xE000) {
            // last char was a lead
            if (!leadSurrogate) {
                // no lead yet
                if (codePoint > 0xDBFF) {
                    // unexpected trail
                    if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);
                    continue;
                } else if (i + 1 === length) {
                    // unpaired lead
                    if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);
                    continue;
                }
                // valid lead
                leadSurrogate = codePoint;
                continue;
            }
            // 2 leads in a row
            if (codePoint < 0xDC00) {
                if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);
                leadSurrogate = codePoint;
                continue;
            }
            // valid surrogate pair
            codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000;
        } else if (leadSurrogate) {
            // valid bmp char, but last char was a lead
            if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);
        }
        leadSurrogate = null;
        // encode utf8
        if (codePoint < 0x80) {
            if ((units -= 1) < 0) break;
            bytes.push(codePoint);
        } else if (codePoint < 0x800) {
            if ((units -= 2) < 0) break;
            bytes.push(codePoint >> 0x6 | 0xC0, codePoint & 0x3F | 0x80);
        } else if (codePoint < 0x10000) {
            if ((units -= 3) < 0) break;
            bytes.push(codePoint >> 0xC | 0xE0, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);
        } else if (codePoint < 0x110000) {
            if ((units -= 4) < 0) break;
            bytes.push(codePoint >> 0x12 | 0xF0, codePoint >> 0xC & 0x3F | 0x80, codePoint >> 0x6 & 0x3F | 0x80, codePoint & 0x3F | 0x80);
        } else {
            throw new Error('Invalid code point');
        }
    }
    return bytes;
}
function asciiToBytes(str) {
    var byteArray = [];
    for(var i = 0; i < str.length; ++i){
        // Node's code seems to be doing this and not & 0x7F..
        byteArray.push(str.charCodeAt(i) & 0xFF);
    }
    return byteArray;
}
function utf16leToBytes(str, units) {
    var c, hi, lo;
    var byteArray = [];
    for(var i = 0; i < str.length; ++i){
        if ((units -= 2) < 0) break;
        c = str.charCodeAt(i);
        hi = c >> 8;
        lo = c % 256;
        byteArray.push(lo);
        byteArray.push(hi);
    }
    return byteArray;
}
function base64ToBytes(str) {
    return toByteArray(base64clean(str));
}
function blitBuffer(src, dst, offset, length) {
    for(var i = 0; i < length; ++i){
        if (i + offset >= dst.length || i >= src.length) break;
        dst[i + offset] = src[i];
    }
    return i;
}
function isnan(val) {
    return val !== val // eslint-disable-line no-self-compare
    ;
}
// the following is from is-buffer, also by Feross Aboukhadijeh and with same lisence
// The _isBuffer check is for Safari 5-7 support, because it's missing
// Object.prototype.constructor. Remove this eventually
function isBuffer$1(obj) {
    return obj != null && (!!obj._isBuffer || isFastBuffer(obj) || isSlowBuffer(obj));
}
function isFastBuffer(obj) {
    return !!obj.constructor && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj);
}
// For Node v0.10 support. Remove this eventually.
function isSlowBuffer(obj) {
    return typeof obj.readFloatLE === 'function' && typeof obj.slice === 'function' && isFastBuffer(obj.slice(0, 0));
}
class PNG {
    data;
    width;
    height;
    format;
    constructor(data){
        const png = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$png$2d$js$2f$lib$2f$png$2d$js$2e$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](data);
        this.data = data;
        this.width = png.width;
        this.height = png.height;
        this.format = 'png';
    }
    static isValid(data) {
        try {
            return !!new PNG(data);
        } catch  {
            return false;
        }
    }
}
class JPEG {
    data;
    width;
    height;
    format;
    constructor(data){
        this.data = data;
        this.format = 'jpeg';
        this.width = 0;
        this.height = 0;
        if (data.readUInt16BE(0) !== 0xffd8) {
            throw new Error('SOI not found in JPEG');
        }
        const markers = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jay$2d$peg$2f$src$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].decode(this.data);
        let orientation;
        for(let i = 0; i < markers.length; i += 1){
            const marker = markers[i];
            if (marker.name === 'EXIF' && marker.entries.orientation) {
                orientation = marker.entries.orientation;
            }
            if (marker.name === 'SOF') {
                this.width ||= marker.width;
                this.height ||= marker.height;
            }
        }
        if (orientation > 4) {
            [this.width, this.height] = [
                this.height,
                this.width
            ];
        }
    }
    static isValid(data) {
        return data && Buffer.isBuffer(data) && data.readUInt16BE(0) === 0xffd8;
    }
}
const createCache = ({ limit = 100 } = {})=>{
    let cache = {};
    let keys = [];
    return {
        get: (key)=>key ? cache[key] : null,
        set: (key, value)=>{
            keys.push(key);
            if (keys.length > limit) {
                delete cache[keys.shift()];
            }
            cache[key] = value;
        },
        reset: ()=>{
            cache = {};
            keys = [];
        },
        length: ()=>keys.length
    };
};
const IMAGE_CACHE = createCache({
    limit: 30
});
const isBuffer = Buffer.isBuffer;
const isBlob = (src)=>{
    return typeof Blob !== 'undefined' && src instanceof Blob;
};
const isDataImageSrc = (src)=>{
    return 'data' in src;
};
const isBase64Src = (imageSrc)=>'uri' in imageSrc && /^data:image\/[a-zA-Z]*;base64,[^"]*/g.test(imageSrc.uri);
const fetchRemoteFile = async (src)=>{
    const { method = 'GET', headers, body, credentials } = src;
    const response = await fetch(src.uri, {
        method,
        headers,
        body,
        credentials
    });
    const buffer = await response.arrayBuffer();
    return Buffer.from(buffer);
};
const isValidFormat = (format)=>{
    const lower = format.toLowerCase();
    return lower === 'jpg' || lower === 'jpeg' || lower === 'png';
};
const guessFormat = (buffer)=>{
    let format;
    if (JPEG.isValid(buffer)) {
        format = 'jpg';
    } else if (PNG.isValid(buffer)) {
        format = 'png';
    }
    return format;
};
function getImage(body, format) {
    switch(format.toLowerCase()){
        case 'jpg':
        case 'jpeg':
            return new JPEG(body);
        case 'png':
            return new PNG(body);
        default:
            return null;
    }
}
const resolveBase64Image = async ({ uri })=>{
    const match = /^data:image\/([a-zA-Z]*);base64,([^"]*)/g.exec(uri);
    if (!match) throw new Error(`Invalid base64 image: ${uri}`);
    const format = match[1];
    const data = match[2];
    if (!isValidFormat(format)) throw new Error(`Base64 image invalid format: ${format}`);
    return getImage(Buffer.from(data, 'base64'), format);
};
const resolveImageFromData = async (src)=>{
    if (src.data && src.format) {
        return getImage(src.data, src.format);
    }
    throw new Error(`Invalid data given for local file: ${JSON.stringify(src)}`);
};
const resolveBufferImage = async (buffer)=>{
    const format = guessFormat(buffer);
    if (format) {
        return getImage(buffer, format);
    }
    return null;
};
const resolveBlobImage = async (blob)=>{
    const { type } = blob;
    if (!type || type === 'application/octet-stream') {
        const arrayBuffer = await blob.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        return resolveBufferImage(buffer);
    }
    if (!type.startsWith('image/')) {
        throw new Error(`Invalid blob type: ${type}`);
    }
    const format = type.replace('image/', '');
    if (!isValidFormat(format)) {
        throw new Error(`Invalid blob type: ${type}`);
    }
    const buffer = await blob.arrayBuffer();
    return getImage(Buffer.from(buffer), format);
};
const getImageFormat = (body)=>{
    const isPng = body[0] === 137 && body[1] === 80 && body[2] === 78 && body[3] === 71 && body[4] === 13 && body[5] === 10 && body[6] === 26 && body[7] === 10;
    const isJpg = body[0] === 255 && body[1] === 216 && body[2] === 255;
    let extension = '';
    if (isPng) {
        extension = 'png';
    } else if (isJpg) {
        extension = 'jpg';
    } else {
        throw new Error('Not valid image extension');
    }
    return extension;
};
const resolveImageFromUrl = async (src)=>{
    const data = await fetchRemoteFile(src);
    const format = getImageFormat(data);
    return getImage(data, format);
};
const getCacheKey = (src)=>{
    if (isBlob(src) || isBuffer(src)) return null;
    if (isDataImageSrc(src)) return src.data.toString();
    return src.uri;
};
const resolveImage = (src, { cache = true } = {})=>{
    let image;
    const cacheKey = getCacheKey(src);
    if (isBlob(src)) {
        image = resolveBlobImage(src);
    } else if (isBuffer(src)) {
        image = resolveBufferImage(src);
    } else if (cache && IMAGE_CACHE.get(cacheKey)) {
        return IMAGE_CACHE.get(cacheKey);
    } else if (isBase64Src(src)) {
        image = resolveBase64Image(src);
    } else if (isDataImageSrc(src)) {
        image = resolveImageFromData(src);
    } else {
        image = resolveImageFromUrl(src);
    }
    if (!image) {
        throw new Error('Cannot resolve image');
    }
    if (cache && cacheKey) {
        IMAGE_CACHE.set(cacheKey, image);
    }
    return image;
};
;
}}),
"[project]/node_modules/@react-pdf/renderer/lib/react-pdf.browser.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BlobProvider": (()=>BlobProvider),
    "Font": (()=>Font),
    "PDFDownloadLink": (()=>PDFDownloadLink),
    "PDFViewer": (()=>PDFViewer),
    "StyleSheet": (()=>StyleSheet),
    "createRenderer": (()=>createRenderer),
    "default": (()=>index),
    "pdf": (()=>pdf),
    "render": (()=>render),
    "renderToBuffer": (()=>renderToBuffer),
    "renderToFile": (()=>renderToFile),
    "renderToStream": (()=>renderToStream),
    "renderToString": (()=>renderToString),
    "usePDF": (()=>usePDF),
    "version": (()=>version)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/primitives/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$queue$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/queue/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$font$2f$lib$2f$index$2e$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/font/lib/index.browser.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$render$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/render/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$pdfkit$2f$lib$2f$pdfkit$2e$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/pdfkit/lib/pdfkit.browser.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$layout$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/layout/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/fns/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$reconciler$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/reconciler/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
const omitNils = (object)=>Object.fromEntries(Object.entries(object).filter((_ref)=>{
        let [, value] = _ref;
        return value !== undefined;
    }));
const createInstance = (type, _ref)=>{
    let { style, children, ...props } = _ref;
    return {
        type,
        box: {},
        style: style || {},
        props: props || {},
        children: []
    };
};
const createTextInstance = (text)=>({
        type: 'TEXT_INSTANCE',
        value: text
    });
const appendChild = (parent, child)=>{
    const isParentText = parent.type === 'TEXT' || parent.type === 'LINK' || parent.type === 'TSPAN' || parent.type === 'NOTE';
    const isChildTextInstance = child.type === 'TEXT_INSTANCE';
    const isOrphanTextInstance = isChildTextInstance && !isParentText;
    // Ignore orphan text instances.
    // Caused by cases such as <>{name && <Text>{name}</Text>}</>
    if (isOrphanTextInstance) {
        console.warn(`Invalid '${child.value}' string child outside <Text> component`);
        return;
    }
    parent.children.push(child);
};
const appendChildToContainer = (parentInstance, child)=>{
    if (parentInstance.type === 'ROOT') {
        parentInstance.document = child;
    } else {
        appendChild(parentInstance, child);
    }
};
const insertBefore = (parentInstance, child, beforeChild)=>{
    var _parentInstance$child;
    const index = (_parentInstance$child = parentInstance.children) === null || _parentInstance$child === void 0 ? void 0 : _parentInstance$child.indexOf(beforeChild);
    if (index === undefined) return;
    if (index !== -1 && child) parentInstance.children.splice(index, 0, child);
};
const removeChild = (parentInstance, child)=>{
    var _parentInstance$child2;
    const index = (_parentInstance$child2 = parentInstance.children) === null || _parentInstance$child2 === void 0 ? void 0 : _parentInstance$child2.indexOf(child);
    if (index === undefined) return;
    if (index !== -1) parentInstance.children.splice(index, 1);
};
const removeChildFromContainer = (parentInstance, child)=>{
    var _parentInstance$child3;
    const index = (_parentInstance$child3 = parentInstance.children) === null || _parentInstance$child3 === void 0 ? void 0 : _parentInstance$child3.indexOf(child);
    if (index === undefined) return;
    if (index !== -1) parentInstance.children.splice(index, 1);
};
const commitTextUpdate = (textInstance, oldText, newText)=>{
    textInstance.value = newText;
};
const commitUpdate = (instance, updatePayload, type, oldProps, newProps)=>{
    const { style, ...props } = newProps;
    instance.props = props;
    instance.style = style;
};
const createRenderer = (_ref2)=>{
    let { onChange = ()=>{} } = _ref2;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$reconciler$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        appendChild,
        appendChildToContainer,
        commitTextUpdate,
        commitUpdate,
        createInstance,
        createTextInstance,
        insertBefore,
        removeChild,
        removeChildFromContainer,
        resetAfterCommit: onChange
    });
};
var version$1 = "4.3.0";
var packageJson = {
    version: version$1
};
const { version } = packageJson;
const fontStore = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$font$2f$lib$2f$index$2e$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]();
// We must keep a single renderer instance, otherwise React will complain
let renderer;
// The pdf instance acts as an event emitter for DOM usage.
// We only want to trigger an update when PDF content changes
const events = {};
const pdf = (initialValue)=>{
    const onChange = ()=>{
        var _events$change;
        const listeners = ((_events$change = events.change) === null || _events$change === void 0 ? void 0 : _events$change.slice()) || [];
        for(let i = 0; i < listeners.length; i += 1)listeners[i]();
    };
    const container = {
        type: 'ROOT',
        document: null
    };
    renderer = renderer || createRenderer({
        onChange
    });
    const mountNode = renderer.createContainer(container);
    const updateContainer = (doc, callback)=>{
        renderer.updateContainer(doc, mountNode, null, callback);
    };
    if (initialValue) updateContainer(initialValue);
    const render = async function(compress) {
        if (compress === void 0) {
            compress = true;
        }
        const props = container.document.props || {};
        const { pdfVersion, language, pageLayout, pageMode, title, author, subject, keyboards, creator = 'react-pdf', producer = 'react-pdf', creationDate = new Date(), modificationDate } = props;
        const ctx = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$pdfkit$2f$lib$2f$pdfkit$2e$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]({
            compress,
            pdfVersion,
            lang: language,
            displayTitle: true,
            autoFirstPage: false,
            info: omitNils({
                Title: title,
                Author: author,
                Subject: subject,
                Keywords: keyboards,
                Creator: creator,
                Producer: producer,
                CreationDate: creationDate,
                ModificationDate: modificationDate
            })
        });
        if (pageLayout) {
            ctx._root.data.PageLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["upperFirst"])(pageLayout);
        }
        if (pageMode) {
            ctx._root.data.PageMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["upperFirst"])(pageMode);
        }
        const layout = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$layout$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(container.document, fontStore);
        const fileStream = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$render$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(ctx, layout);
        return {
            layout,
            fileStream
        };
    };
    const callOnRender = function(params) {
        if (params === void 0) {
            params = {};
        }
        if (container.document.props.onRender) {
            container.document.props.onRender(params);
        }
    };
    const toBlob = async ()=>{
        const chunks = [];
        const { layout: _INTERNAL__LAYOUT__DATA_, fileStream: instance } = await render();
        return new Promise((resolve, reject)=>{
            instance.on('data', (chunk)=>{
                chunks.push(chunk instanceof Uint8Array ? chunk : new Uint8Array(chunk));
            });
            instance.on('end', ()=>{
                try {
                    const blob = new Blob(chunks, {
                        type: 'application/pdf'
                    });
                    callOnRender({
                        blob,
                        _INTERNAL__LAYOUT__DATA_
                    });
                    resolve(blob);
                } catch (error) {
                    reject(error);
                }
            });
        });
    };
    // TODO: rename this method to `toStream` in next major release, because it return stream not a buffer
    const toBuffer = async ()=>{
        const { layout: _INTERNAL__LAYOUT__DATA_, fileStream } = await render();
        callOnRender({
            _INTERNAL__LAYOUT__DATA_
        });
        return fileStream;
    };
    /*
   * TODO: remove this method in next major release. it is buggy
   * see
   * - https://github.com/diegomura/react-pdf/issues/2112
   * - https://github.com/diegomura/react-pdf/issues/2095
   */ const toString = async ()=>{
        if ("TURBOPACK compile-time truthy", 1) {
            console.warn('`toString` is deprecated and will be removed in next major release');
        }
        let result = '';
        const { fileStream: instance } = await render(false); // For some reason, when rendering to string if compress=true the document is blank
        return new Promise((resolve, reject)=>{
            try {
                instance.on('data', (buffer)=>{
                    result += buffer;
                });
                instance.on('end', ()=>{
                    callOnRender();
                    resolve(result);
                });
            } catch (error) {
                reject(error);
            }
        });
    };
    const on = (event, listener)=>{
        if (!events[event]) events[event] = [];
        events[event].push(listener);
    };
    const removeListener = (event, listener)=>{
        if (!events[event]) return;
        const idx = events[event].indexOf(listener);
        if (idx > -1) events[event].splice(idx, 1);
    };
    return {
        on,
        container,
        toBlob,
        toBuffer,
        toString,
        removeListener,
        updateContainer
    };
};
const Font = fontStore;
const StyleSheet = {
    create: (s)=>s
};
/**
 * PDF hook
 *
 * @param {Object} [options] hook options
 * @returns {[Object, Function]} pdf state and update function
 */ const usePDF = function(_temp) {
    let { document } = _temp === void 0 ? {} : _temp;
    const pdfInstance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        url: null,
        blob: null,
        error: null,
        loading: !!document
    });
    // Setup rendering queue
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "usePDF.useEffect": ()=>{
            const renderQueue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$queue$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
                autostart: true,
                concurrency: 1
            });
            const queueDocumentRender = {
                "usePDF.useEffect.queueDocumentRender": ()=>{
                    setState({
                        "usePDF.useEffect.queueDocumentRender": (prev)=>({
                                ...prev,
                                loading: true
                            })
                    }["usePDF.useEffect.queueDocumentRender"]);
                    renderQueue.splice(0, renderQueue.length, {
                        "usePDF.useEffect.queueDocumentRender": ()=>state.error ? Promise.resolve() : pdfInstance.current.toBlob()
                    }["usePDF.useEffect.queueDocumentRender"]);
                }
            }["usePDF.useEffect.queueDocumentRender"];
            const onRenderFailed = {
                "usePDF.useEffect.onRenderFailed": (error)=>{
                    console.error(error);
                    setState({
                        "usePDF.useEffect.onRenderFailed": (prev)=>({
                                ...prev,
                                loading: false,
                                error
                            })
                    }["usePDF.useEffect.onRenderFailed"]);
                }
            }["usePDF.useEffect.onRenderFailed"];
            const onRenderSuccessful = {
                "usePDF.useEffect.onRenderSuccessful": (blob)=>{
                    setState({
                        blob,
                        error: null,
                        loading: false,
                        url: URL.createObjectURL(blob)
                    });
                }
            }["usePDF.useEffect.onRenderSuccessful"];
            pdfInstance.current = pdf();
            pdfInstance.current.on('change', queueDocumentRender);
            if (document) {
                pdfInstance.current.updateContainer(document);
            }
            renderQueue.on('error', onRenderFailed);
            renderQueue.on('success', onRenderSuccessful);
            return ({
                "usePDF.useEffect": ()=>{
                    renderQueue.end();
                    pdfInstance.current.removeListener('change', queueDocumentRender);
                }
            })["usePDF.useEffect"];
        }
    }["usePDF.useEffect"], []);
    // Revoke old unused url instances
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "usePDF.useEffect": ()=>{
            return ({
                "usePDF.useEffect": ()=>{
                    if (state.url) {
                        URL.revokeObjectURL(state.url);
                    }
                }
            })["usePDF.useEffect"];
        }
    }["usePDF.useEffect"], [
        state.url
    ]);
    const update = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePDF.useCallback[update]": (newDoc)=>{
            pdfInstance.current.updateContainer(newDoc);
        }
    }["usePDF.useCallback[update]"], []);
    return [
        state,
        update
    ];
};
const PDFViewer = (_ref)=>{
    let { title, style, className, children, innerRef, showToolbar = true, ...props } = _ref;
    const [instance, updateInstance] = usePDF();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PDFViewer.useEffect": ()=>updateInstance(children)
    }["PDFViewer.useEffect"], [
        children
    ]);
    const src = instance.url ? `${instance.url}#toolbar=${showToolbar ? 1 : 0}` : null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("iframe", {
        src: src,
        title: title,
        ref: innerRef,
        style: style,
        className: className,
        ...props
    });
};
const BlobProvider = (_ref)=>{
    let { document: doc, children } = _ref;
    const [instance, updateInstance] = usePDF();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BlobProvider.useEffect": ()=>updateInstance(doc)
    }["BlobProvider.useEffect"], [
        doc
    ]);
    if (!doc) {
        console.warn('You should pass a valid document to BlobProvider');
        return null;
    }
    return children(instance);
};
const PDFDownloadLink = (_ref)=>{
    let { fileName = 'document.pdf', document: doc, children, onClick, href, ...rest } = _ref;
    const [instance, updateInstance] = usePDF();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PDFDownloadLink.useEffect": ()=>updateInstance(doc)
    }["PDFDownloadLink.useEffect"], [
        doc
    ]);
    if (!doc) {
        console.warn('You should pass a valid document to PDFDownloadLink');
        return null;
    }
    const handleDownloadIE = ()=>{
        if (instance && window.navigator.msSaveBlob) {
            // IE
            window.navigator.msSaveBlob(instance.blob, fileName);
        }
    };
    const handleClick = (event)=>{
        handleDownloadIE();
        if (typeof onClick === 'function') onClick(event, instance);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("a", {
        href: instance.url,
        download: fileName,
        onClick: handleClick,
        ...rest,
        children: typeof children === 'function' ? children(instance) : children
    });
};
const throwEnvironmentError = (name)=>{
    throw new Error(`${name} is a Node specific API. You're either using this method in a browser, or your bundler is not loading react-pdf from the appropriate web build.`);
};
const renderToStream = ()=>{
    throwEnvironmentError('renderToStream');
};
const renderToBuffer = ()=>{
    throwEnvironmentError('renderToBuffer');
};
const renderToString = ()=>{
    throwEnvironmentError('renderToString');
};
const renderToFile = ()=>{
    throwEnvironmentError('renderToFile');
};
const render = ()=>{
    throwEnvironmentError('render');
};
// TODO: remove this default export in next major release because it breaks tree-shacking
var index = {
    pdf,
    usePDF,
    Font,
    version,
    StyleSheet,
    PDFViewer,
    BlobProvider,
    PDFDownloadLink,
    renderToStream,
    renderToString,
    renderToFile,
    render,
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__
};
;
 //# sourceMappingURL=react-pdf.browser.js.map
}}),
"[project]/node_modules/@react-pdf/renderer/lib/react-pdf.browser.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$primitives$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/primitives/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$queue$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/queue/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$font$2f$lib$2f$index$2e$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/font/lib/index.browser.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$render$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/render/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$pdfkit$2f$lib$2f$pdfkit$2e$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/pdfkit/lib/pdfkit.browser.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$layout$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/layout/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$fns$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/fns/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$reconciler$2f$lib$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/reconciler/lib/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$pdf$2f$renderer$2f$lib$2f$react$2d$pdf$2e$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-pdf/renderer/lib/react-pdf.browser.js [app-client] (ecmascript) <locals>");
}}),
}]);

//# sourceMappingURL=node_modules_%40react-pdf_52d19781._.js.map