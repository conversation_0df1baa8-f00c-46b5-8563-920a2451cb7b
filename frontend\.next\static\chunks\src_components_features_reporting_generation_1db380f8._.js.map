{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/generation/DataReportGenerator.tsx"], "sourcesContent": ["/**\r\n * @file DataReportGenerator.tsx\r\n * @description Comprehensive data report generation component for all entities\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport {\r\n  FileText,\r\n  Download,\r\n  Settings,\r\n  Calendar,\r\n  Filter,\r\n  Users,\r\n  Car,\r\n  ClipboardList,\r\n  Building,\r\n} from 'lucide-react';\r\nimport {\r\n  useReportGeneration,\r\n  useReportTemplates,\r\n} from '../hooks/useReportGeneration';\r\nimport { DateRangePicker } from '../components/DateRangePicker';\r\nimport { LoadingSpinner } from '@/components/ui/loading-spinner';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\n\r\n/**\r\n * Entity configuration for report generation\r\n */\r\nconst ENTITY_CONFIGS = [\r\n  {\r\n    id: 'delegations',\r\n    name: 'Delegations',\r\n    icon: ClipboardList,\r\n    description: 'Delegation assignments and status tracking',\r\n    color: 'bg-blue-100 text-blue-800',\r\n  },\r\n  {\r\n    id: 'tasks',\r\n    name: 'Tasks',\r\n    icon: FileText,\r\n    description: 'Task completion and performance metrics',\r\n    color: 'bg-green-100 text-green-800',\r\n  },\r\n  {\r\n    id: 'vehicles',\r\n    name: 'Vehicles',\r\n    icon: Car,\r\n    description: 'Vehicle utilization and maintenance data',\r\n    color: 'bg-orange-100 text-orange-800',\r\n  },\r\n  {\r\n    id: 'employees',\r\n    name: 'Employees',\r\n    icon: Users,\r\n    description: 'Employee performance and workload analysis',\r\n    color: 'bg-purple-100 text-purple-800',\r\n  },\r\n];\r\n\r\n/**\r\n * Export format options\r\n */\r\nconst EXPORT_FORMATS = [\r\n  { id: 'pdf', name: 'PDF', description: 'Formatted document for printing' },\r\n  {\r\n    id: 'excel',\r\n    name: 'Excel',\r\n    description: 'Spreadsheet with multiple sheets',\r\n  },\r\n  {\r\n    id: 'csv',\r\n    name: 'CSV',\r\n    description: 'Comma-separated values for data analysis',\r\n  },\r\n];\r\n\r\n/**\r\n * DataReportGenerator Component\r\n *\r\n * Provides comprehensive interface for generating data reports across all entities.\r\n * Supports both individual and aggregate reporting with customizable templates.\r\n */\r\nexport const DataReportGenerator: React.FC = () => {\r\n  // State management\r\n  const [selectedEntities, setSelectedEntities] = useState<string[]>([\r\n    'delegations',\r\n  ]);\r\n  const [selectedTemplate, setSelectedTemplate] =\r\n    useState<string>('comprehensive');\r\n  const [selectedFormat, setSelectedFormat] = useState<string>('pdf');\r\n  const [dateRange, setDateRange] = useState<{ from: Date; to: Date } | null>(\r\n    null\r\n  );\r\n  const [customFilters, setCustomFilters] = useState<Record<string, any>>({});\r\n  const [reportName, setReportName] = useState<string>('');\r\n  const [reportDescription, setReportDescription] = useState<string>('');\r\n\r\n  // Hooks\r\n  const {\r\n    generateComprehensiveReport,\r\n    isGenerating,\r\n    error: generationError,\r\n  } = useReportGeneration();\r\n  const { templates, isLoading: templatesLoading } = useReportTemplates();\r\n\r\n  /**\r\n   * Handle entity selection toggle\r\n   */\r\n  const handleEntityToggle = (entityId: string) => {\r\n    setSelectedEntities(prev =>\r\n      prev.includes(entityId)\r\n        ? prev.filter(id => id !== entityId)\r\n        : [...prev, entityId]\r\n    );\r\n  };\r\n\r\n  /**\r\n   * Handle report generation\r\n   */\r\n  const handleGenerateReport = async () => {\r\n    if (selectedEntities.length === 0) {\r\n      return;\r\n    }\r\n\r\n    const reportConfig = {\r\n      entityTypes: selectedEntities,\r\n      template: selectedTemplate,\r\n      format: selectedFormat,\r\n      filters: {\r\n        ...customFilters,\r\n        ...(dateRange && {\r\n          dateRange: {\r\n            from: dateRange.from.toISOString(),\r\n            to: dateRange.to.toISOString(),\r\n          },\r\n        }),\r\n      },\r\n      options: {\r\n        name: reportName || `Report ${new Date().toLocaleDateString()}`,\r\n        description: reportDescription,\r\n        includeCharts: true,\r\n        includeSummary: true,\r\n      },\r\n    };\r\n\r\n    try {\r\n      await generateComprehensiveReport(reportConfig);\r\n    } catch (error) {\r\n      console.error('Failed to generate report:', error);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Get selected template details\r\n   */\r\n  const selectedTemplateDetails = Array.isArray(templates)\r\n    ? templates.find((t: any) => t.id === selectedTemplate)\r\n    : null;\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900\">\r\n            Data Report Generator\r\n          </h2>\r\n          <p className=\"text-gray-600 mt-1\">\r\n            Generate comprehensive reports for delegations, tasks, vehicles, and\r\n            employees\r\n          </p>\r\n        </div>\r\n        <Badge variant=\"outline\" className=\"flex items-center gap-2\">\r\n          <FileText className=\"h-4 w-4\" />\r\n          Report Builder\r\n        </Badge>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\r\n        {/* Configuration Panel */}\r\n        <div className=\"lg:col-span-2 space-y-6\">\r\n          {/* Entity Selection */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <Building className=\"h-5 w-5\" />\r\n                Select Data Sources\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                {ENTITY_CONFIGS.map(entity => {\r\n                  const Icon = entity.icon;\r\n                  const isSelected = selectedEntities.includes(entity.id);\r\n\r\n                  return (\r\n                    <div\r\n                      key={entity.id}\r\n                      className={`\r\n                        border rounded-lg p-4 cursor-pointer transition-all\r\n                        ${\r\n                          isSelected\r\n                            ? 'border-blue-500 bg-blue-50'\r\n                            : 'border-gray-200 hover:border-gray-300'\r\n                        }\r\n                      `}\r\n                      onClick={() => handleEntityToggle(entity.id)}\r\n                    >\r\n                      <div className=\"flex items-start gap-3\">\r\n                        <Checkbox\r\n                          checked={isSelected}\r\n                          onCheckedChange={() => handleEntityToggle(entity.id)}\r\n                        />\r\n                        <div className=\"flex-1\">\r\n                          <div className=\"flex items-center gap-2 mb-2\">\r\n                            <Icon className=\"h-5 w-5 text-gray-600\" />\r\n                            <span className=\"font-medium\">{entity.name}</span>\r\n                            <Badge className={entity.color} variant=\"secondary\">\r\n                              {entity.id}\r\n                            </Badge>\r\n                          </div>\r\n                          <p className=\"text-sm text-gray-600\">\r\n                            {entity.description}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                })}\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Template Selection */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <Settings className=\"h-5 w-5\" />\r\n                Report Template\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              {templatesLoading ? (\r\n                <LoadingSpinner />\r\n              ) : (\r\n                <Select\r\n                  value={selectedTemplate}\r\n                  onValueChange={setSelectedTemplate}\r\n                >\r\n                  <SelectTrigger>\r\n                    <SelectValue placeholder=\"Select a template\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    {Array.isArray(templates) &&\r\n                      templates.map((template: any) => (\r\n                        <SelectItem key={template.id} value={template.id}>\r\n                          <div>\r\n                            <div className=\"font-medium\">{template.name}</div>\r\n                            <div className=\"text-sm text-gray-600\">\r\n                              {template.description}\r\n                            </div>\r\n                          </div>\r\n                        </SelectItem>\r\n                      ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              )}\r\n\r\n              {selectedTemplateDetails && (\r\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\r\n                  <h4 className=\"font-medium mb-2\">\r\n                    {selectedTemplateDetails.name}\r\n                  </h4>\r\n                  <p className=\"text-sm text-gray-600 mb-3\">\r\n                    {selectedTemplateDetails.description}\r\n                  </p>\r\n                  <div className=\"flex flex-wrap gap-2\">\r\n                    {selectedTemplateDetails.sections?.map((section: any) => (\r\n                      <Badge\r\n                        key={section}\r\n                        variant=\"outline\"\r\n                        className=\"text-xs\"\r\n                      >\r\n                        {section}\r\n                      </Badge>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Filters */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <Filter className=\"h-5 w-5\" />\r\n                Filters & Options\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              {/* Date Range */}\r\n              <div>\r\n                <Label className=\"flex items-center gap-2 mb-2\">\r\n                  <Calendar className=\"h-4 w-4\" />\r\n                  Date Range\r\n                </Label>\r\n                <DateRangePicker\r\n                  value={dateRange}\r\n                  onChange={setDateRange}\r\n                  placeholder=\"Select date range for data\"\r\n                />\r\n              </div>\r\n\r\n              {/* Export Format */}\r\n              <div>\r\n                <Label className=\"mb-2 block\">Export Format</Label>\r\n                <Select\r\n                  value={selectedFormat}\r\n                  onValueChange={setSelectedFormat}\r\n                >\r\n                  <SelectTrigger>\r\n                    <SelectValue />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    {EXPORT_FORMATS.map(format => (\r\n                      <SelectItem key={format.id} value={format.id}>\r\n                        <div>\r\n                          <div className=\"font-medium\">{format.name}</div>\r\n                          <div className=\"text-sm text-gray-600\">\r\n                            {format.description}\r\n                          </div>\r\n                        </div>\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n\r\n              {/* Report Name */}\r\n              <div>\r\n                <Label htmlFor=\"reportName\" className=\"mb-2 block\">\r\n                  Report Name\r\n                </Label>\r\n                <Input\r\n                  id=\"reportName\"\r\n                  value={reportName}\r\n                  onChange={e => setReportName(e.target.value)}\r\n                  placeholder=\"Enter custom report name\"\r\n                />\r\n              </div>\r\n\r\n              {/* Report Description */}\r\n              <div>\r\n                <Label htmlFor=\"reportDescription\" className=\"mb-2 block\">\r\n                  Description\r\n                </Label>\r\n                <Textarea\r\n                  id=\"reportDescription\"\r\n                  value={reportDescription}\r\n                  onChange={e => setReportDescription(e.target.value)}\r\n                  placeholder=\"Optional description for the report\"\r\n                  rows={3}\r\n                />\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Summary Panel */}\r\n        <div className=\"space-y-6\">\r\n          {/* Generation Summary */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Report Summary</CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div>\r\n                <Label className=\"text-sm font-medium text-gray-600\">\r\n                  Selected Entities\r\n                </Label>\r\n                <div className=\"mt-1 flex flex-wrap gap-1\">\r\n                  {selectedEntities.map(entityId => {\r\n                    const entity = ENTITY_CONFIGS.find(e => e.id === entityId);\r\n                    return entity ? (\r\n                      <Badge\r\n                        key={entityId}\r\n                        className={entity.color}\r\n                        variant=\"secondary\"\r\n                      >\r\n                        {entity.name}\r\n                      </Badge>\r\n                    ) : null;\r\n                  })}\r\n                </div>\r\n              </div>\r\n\r\n              <Separator />\r\n\r\n              <div>\r\n                <Label className=\"text-sm font-medium text-gray-600\">\r\n                  Template\r\n                </Label>\r\n                <p className=\"mt-1 text-sm\">\r\n                  {selectedTemplateDetails?.name || 'None selected'}\r\n                </p>\r\n              </div>\r\n\r\n              <div>\r\n                <Label className=\"text-sm font-medium text-gray-600\">\r\n                  Format\r\n                </Label>\r\n                <p className=\"mt-1 text-sm\">\r\n                  {EXPORT_FORMATS.find(f => f.id === selectedFormat)?.name}\r\n                </p>\r\n              </div>\r\n\r\n              {dateRange && (\r\n                <div>\r\n                  <Label className=\"text-sm font-medium text-gray-600\">\r\n                    Date Range\r\n                  </Label>\r\n                  <p className=\"mt-1 text-sm\">\r\n                    {dateRange.from.toLocaleDateString()} -{' '}\r\n                    {dateRange.to.toLocaleDateString()}\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Generate Button */}\r\n          <Card>\r\n            <CardContent className=\"pt-6\">\r\n              {generationError && (\r\n                <Alert className=\"mb-4\" variant=\"destructive\">\r\n                  <AlertDescription>{generationError}</AlertDescription>\r\n                </Alert>\r\n              )}\r\n\r\n              <Button\r\n                onClick={handleGenerateReport}\r\n                disabled={selectedEntities.length === 0 || isGenerating}\r\n                className=\"w-full\"\r\n                size=\"lg\"\r\n              >\r\n                {isGenerating ? (\r\n                  <>\r\n                    <LoadingSpinner className=\"mr-2 h-4 w-4\" />\r\n                    Generating Report...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Download className=\"mr-2 h-4 w-4\" />\r\n                    Generate Report\r\n                  </>\r\n                )}\r\n              </Button>\r\n\r\n              <p className=\"text-xs text-gray-500 mt-2 text-center\">\r\n                Report will be generated and available for download\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAIA;AACA;AACA;;;;;;;;;;;;;;;;;;AAEA;;CAEC,GACD,MAAM,iBAAiB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,MAAM,2NAAA,CAAA,gBAAa;QACnB,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;QACd,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,mMAAA,CAAA,MAAG;QACT,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,OAAO;IACT;CACD;AAED;;CAEC,GACD,MAAM,iBAAiB;IACrB;QAAE,IAAI;QAAO,MAAM;QAAO,aAAa;IAAkC;IACzE;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;CACD;AAQM,MAAM,sBAAgC;;IAC3C,mBAAmB;IACnB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjE;KACD;IACD,MAAM,CAAC,kBAAkB,oBAAoB,GAC3C,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvC;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IACzE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnE,QAAQ;IACR,MAAM,EACJ,2BAA2B,EAC3B,YAAY,EACZ,OAAO,eAAe,EACvB,GAAG,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;IACtB,MAAM,EAAE,SAAS,EAAE,WAAW,gBAAgB,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,qBAAkB,AAAD;IAEpE;;GAEC,GACD,MAAM,qBAAqB,CAAC;QAC1B,oBAAoB,CAAA,OAClB,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,YACzB;mBAAI;gBAAM;aAAS;IAE3B;IAEA;;GAEC,GACD,MAAM,uBAAuB;QAC3B,IAAI,iBAAiB,MAAM,KAAK,GAAG;YACjC;QACF;QAEA,MAAM,eAAe;YACnB,aAAa;YACb,UAAU;YACV,QAAQ;YACR,SAAS;gBACP,GAAG,aAAa;gBAChB,GAAI,aAAa;oBACf,WAAW;wBACT,MAAM,UAAU,IAAI,CAAC,WAAW;wBAChC,IAAI,UAAU,EAAE,CAAC,WAAW;oBAC9B;gBACF,CAAC;YACH;YACA,SAAS;gBACP,MAAM,cAAc,CAAC,OAAO,EAAE,IAAI,OAAO,kBAAkB,IAAI;gBAC/D,aAAa;gBACb,eAAe;gBACf,gBAAgB;YAClB;QACF;QAEA,IAAI;YACF,MAAM,4BAA4B;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA;;GAEC,GACD,MAAM,0BAA0B,MAAM,OAAO,CAAC,aAC1C,UAAU,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK,oBACpC;IAEJ,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAKpC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAU,WAAU;;0CACjC,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAKpC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIpC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAA;gDAClB,MAAM,OAAO,OAAO,IAAI;gDACxB,MAAM,aAAa,iBAAiB,QAAQ,CAAC,OAAO,EAAE;gDAEtD,qBACE,6LAAC;oDAEC,WAAW,CAAC;;wBAEV,EACE,aACI,+BACA,wCACL;sBACH,CAAC;oDACD,SAAS,IAAM,mBAAmB,OAAO,EAAE;8DAE3C,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uIAAA,CAAA,WAAQ;gEACP,SAAS;gEACT,iBAAiB,IAAM,mBAAmB,OAAO,EAAE;;;;;;0EAErD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;;;;;;0FAChB,6LAAC;gFAAK,WAAU;0FAAe,OAAO,IAAI;;;;;;0FAC1C,6LAAC,oIAAA,CAAA,QAAK;gFAAC,WAAW,OAAO,KAAK;gFAAE,SAAQ;0FACrC,OAAO,EAAE;;;;;;;;;;;;kFAGd,6LAAC;wEAAE,WAAU;kFACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;mDAzBpB,OAAO,EAAE;;;;;4CA+BpB;;;;;;;;;;;;;;;;;0CAMN,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIpC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;4CACpB,iCACC,6LAAC,iJAAA,CAAA,iBAAc;;;;qEAEf,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO;gDACP,eAAe;;kEAEf,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,qIAAA,CAAA,gBAAa;kEACX,MAAM,OAAO,CAAC,cACb,UAAU,GAAG,CAAC,CAAC,yBACb,6LAAC,qIAAA,CAAA,aAAU;gEAAmB,OAAO,SAAS,EAAE;0EAC9C,cAAA,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;sFAAe,SAAS,IAAI;;;;;;sFAC3C,6LAAC;4EAAI,WAAU;sFACZ,SAAS,WAAW;;;;;;;;;;;;+DAJV,SAAS,EAAE;;;;;;;;;;;;;;;;4CAarC,yCACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,wBAAwB,IAAI;;;;;;kEAE/B,6LAAC;wDAAE,WAAU;kEACV,wBAAwB,WAAW;;;;;;kEAEtC,6LAAC;wDAAI,WAAU;kEACZ,wBAAwB,QAAQ,EAAE,IAAI,CAAC,wBACtC,6LAAC,oIAAA,CAAA,QAAK;gEAEJ,SAAQ;gEACR,WAAU;0EAET;+DAJI;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAcnB,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAIlC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;;0EACf,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAGlC,6LAAC,+KAAA,CAAA,kBAAe;wDACd,OAAO;wDACP,UAAU;wDACV,aAAY;;;;;;;;;;;;0DAKhB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAa;;;;;;kEAC9B,6LAAC,qIAAA,CAAA,SAAM;wDACL,OAAO;wDACP,eAAe;;0EAEf,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,6LAAC,qIAAA,CAAA,gBAAa;0EACX,eAAe,GAAG,CAAC,CAAA,uBAClB,6LAAC,qIAAA,CAAA,aAAU;wEAAiB,OAAO,OAAO,EAAE;kFAC1C,cAAA,6LAAC;;8FACC,6LAAC;oFAAI,WAAU;8FAAe,OAAO,IAAI;;;;;;8FACzC,6LAAC;oFAAI,WAAU;8FACZ,OAAO,WAAW;;;;;;;;;;;;uEAJR,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;0DAclC,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAa,WAAU;kEAAa;;;;;;kEAGnD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO;wDACP,UAAU,CAAA,IAAK,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC3C,aAAY;;;;;;;;;;;;0DAKhB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAoB,WAAU;kEAAa;;;;;;kEAG1D,6LAAC,uIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,OAAO;wDACP,UAAU,CAAA,IAAK,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDAClD,aAAY;wDACZ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAoC;;;;;;kEAGrD,6LAAC;wDAAI,WAAU;kEACZ,iBAAiB,GAAG,CAAC,CAAA;4DACpB,MAAM,SAAS,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;4DACjD,OAAO,uBACL,6LAAC,oIAAA,CAAA,QAAK;gEAEJ,WAAW,OAAO,KAAK;gEACvB,SAAQ;0EAEP,OAAO,IAAI;+DAJP;;;;uEAML;wDACN;;;;;;;;;;;;0DAIJ,6LAAC,wIAAA,CAAA,YAAS;;;;;0DAEV,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAoC;;;;;;kEAGrD,6LAAC;wDAAE,WAAU;kEACV,yBAAyB,QAAQ;;;;;;;;;;;;0DAItC,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAoC;;;;;;kEAGrD,6LAAC;wDAAE,WAAU;kEACV,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;;;;;;;;;;;;4CAIvD,2BACC,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAoC;;;;;;kEAGrD,6LAAC;wDAAE,WAAU;;4DACV,UAAU,IAAI,CAAC,kBAAkB;4DAAG;4DAAG;4DACvC,UAAU,EAAE,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ1C,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;wCACpB,iCACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;4CAAO,SAAQ;sDAC9B,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;0DAAE;;;;;;;;;;;sDAIvB,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,iBAAiB,MAAM,KAAK,KAAK;4CAC3C,WAAU;4CACV,MAAK;sDAEJ,6BACC;;kEACE,6LAAC,iJAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDAAiB;;6EAI7C;;kEACE,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;sDAM3C,6LAAC;4CAAE,WAAU;sDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;GAlYa;;QAoBP,6KAAA,CAAA,sBAAmB;QAC4B,6KAAA,CAAA,qBAAkB;;;KArB1D", "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/generation/IndividualReportGenerator.tsx"], "sourcesContent": ["/**\r\n * @file IndividualReportGenerator.tsx\r\n * @description Component for generating individual entity reports\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  FileText,\r\n  Download,\r\n  Search,\r\n  User,\r\n  Car,\r\n  ClipboardList,\r\n  Users,\r\n} from 'lucide-react';\r\nimport {\r\n  useReportGeneration,\r\n  useReportTemplates,\r\n} from '../hooks/useReportGeneration';\r\nimport { LoadingSpinner } from '@/components/ui/loading-spinner';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\n\r\n/**\r\n * Entity type configurations\r\n */\r\nconst ENTITY_TYPES = [\r\n  {\r\n    id: 'delegations',\r\n    name: 'Delegation',\r\n    icon: ClipboardList,\r\n    placeholder: 'Enter delegation ID',\r\n    description: 'Generate detailed report for a specific delegation',\r\n  },\r\n  {\r\n    id: 'tasks',\r\n    name: 'Task',\r\n    icon: FileText,\r\n    placeholder: 'Enter task ID',\r\n    description: 'Generate detailed report for a specific task',\r\n  },\r\n  {\r\n    id: 'vehicles',\r\n    name: 'Vehicle',\r\n    icon: Car,\r\n    placeholder: 'Enter vehicle ID or license plate',\r\n    description: 'Generate detailed report for a specific vehicle',\r\n  },\r\n  {\r\n    id: 'employees',\r\n    name: 'Employee',\r\n    icon: Users,\r\n    placeholder: 'Enter employee ID or email',\r\n    description: 'Generate detailed report for a specific employee',\r\n  },\r\n];\r\n\r\n/**\r\n * Export format options\r\n */\r\nconst EXPORT_FORMATS = [\r\n  { id: 'pdf', name: 'PDF', description: 'Formatted document for printing' },\r\n  { id: 'excel', name: 'Excel', description: 'Spreadsheet format' },\r\n  { id: 'csv', name: 'CSV', description: 'Data export format' },\r\n];\r\n\r\ninterface IndividualReportGeneratorProps {\r\n  defaultEntityType?: string;\r\n  defaultEntityId?: string;\r\n  onReportGenerated?: (result: any) => void;\r\n}\r\n\r\n/**\r\n * IndividualReportGenerator Component\r\n *\r\n * Provides interface for generating detailed reports for individual entities.\r\n */\r\nexport const IndividualReportGenerator: React.FC<\r\n  IndividualReportGeneratorProps\r\n> = ({\r\n  defaultEntityType = 'delegations',\r\n  defaultEntityId = '',\r\n  onReportGenerated,\r\n}) => {\r\n  // State management\r\n  const [selectedEntityType, setSelectedEntityType] =\r\n    useState<string>(defaultEntityType);\r\n  const [entityId, setEntityId] = useState<string>(defaultEntityId);\r\n  const [selectedTemplate, setSelectedTemplate] = useState<string>('default');\r\n  const [selectedFormat, setSelectedFormat] = useState<string>('pdf');\r\n\r\n  // Hooks\r\n  const { generateIndividualReport, isGenerating, error } =\r\n    useReportGeneration();\r\n  const { templates, isLoading: templatesLoading } = useReportTemplates();\r\n\r\n  /**\r\n   * Handle report generation\r\n   */\r\n  const handleGenerateReport = async () => {\r\n    if (!entityId.trim()) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const result = await generateIndividualReport({\r\n        entityType: selectedEntityType,\r\n        entityId: entityId.trim(),\r\n        template: selectedTemplate,\r\n        format: selectedFormat,\r\n      });\r\n\r\n      onReportGenerated?.(result);\r\n    } catch (error) {\r\n      console.error('Failed to generate individual report:', error);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Get selected entity type configuration\r\n   */\r\n  const selectedEntityConfig = ENTITY_TYPES.find(\r\n    type => type.id === selectedEntityType\r\n  );\r\n\r\n  /**\r\n   * Filter templates for selected entity type\r\n   */\r\n  const availableTemplates =\r\n    templates?.filter((template: any) =>\r\n      template.entityTypes.includes(selectedEntityType)\r\n    ) || [];\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h3 className=\"text-xl font-semibold text-gray-900\">\r\n            Individual Report Generator\r\n          </h3>\r\n          <p className=\"text-gray-600 mt-1\">\r\n            Generate detailed reports for specific entities\r\n          </p>\r\n        </div>\r\n        <Badge variant=\"outline\" className=\"flex items-center gap-2\">\r\n          <User className=\"h-4 w-4\" />\r\n          Individual Report\r\n        </Badge>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\r\n        {/* Configuration Panel */}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <Search className=\"h-5 w-5\" />\r\n              Entity Selection\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-4\">\r\n            {/* Entity Type Selection */}\r\n            <div>\r\n              <Label className=\"mb-2 block\">Entity Type</Label>\r\n              <Select\r\n                value={selectedEntityType}\r\n                onValueChange={setSelectedEntityType}\r\n              >\r\n                <SelectTrigger>\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {ENTITY_TYPES.map(type => {\r\n                    const Icon = type.icon;\r\n                    return (\r\n                      <SelectItem key={type.id} value={type.id}>\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <Icon className=\"h-4 w-4\" />\r\n                          <div>\r\n                            <div className=\"font-medium\">{type.name}</div>\r\n                            <div className=\"text-sm text-gray-600\">\r\n                              {type.description}\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </SelectItem>\r\n                    );\r\n                  })}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n\r\n            {/* Entity ID Input */}\r\n            <div>\r\n              <Label htmlFor=\"entityId\" className=\"mb-2 block\">\r\n                {selectedEntityConfig?.name} ID\r\n              </Label>\r\n              <Input\r\n                id=\"entityId\"\r\n                value={entityId}\r\n                onChange={e => setEntityId(e.target.value)}\r\n                placeholder={selectedEntityConfig?.placeholder}\r\n                className=\"w-full\"\r\n              />\r\n              <p className=\"text-sm text-gray-500 mt-1\">\r\n                {selectedEntityConfig?.description}\r\n              </p>\r\n            </div>\r\n\r\n            {/* Template Selection */}\r\n            <div>\r\n              <Label className=\"mb-2 block\">Report Template</Label>\r\n              {templatesLoading ? (\r\n                <LoadingSpinner />\r\n              ) : (\r\n                <Select\r\n                  value={selectedTemplate}\r\n                  onValueChange={setSelectedTemplate}\r\n                >\r\n                  <SelectTrigger>\r\n                    <SelectValue placeholder=\"Select a template\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"default\">\r\n                      <div>\r\n                        <div className=\"font-medium\">Default Template</div>\r\n                        <div className=\"text-sm text-gray-600\">\r\n                          Standard individual report format\r\n                        </div>\r\n                      </div>\r\n                    </SelectItem>\r\n                    {availableTemplates.map((template: any) => (\r\n                      <SelectItem key={template.id} value={template.id}>\r\n                        <div>\r\n                          <div className=\"font-medium\">{template.name}</div>\r\n                          <div className=\"text-sm text-gray-600\">\r\n                            {template.description}\r\n                          </div>\r\n                        </div>\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              )}\r\n            </div>\r\n\r\n            {/* Export Format */}\r\n            <div>\r\n              <Label className=\"mb-2 block\">Export Format</Label>\r\n              <Select value={selectedFormat} onValueChange={setSelectedFormat}>\r\n                <SelectTrigger>\r\n                  <SelectValue />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {EXPORT_FORMATS.map(format => (\r\n                    <SelectItem key={format.id} value={format.id}>\r\n                      <div>\r\n                        <div className=\"font-medium\">{format.name}</div>\r\n                        <div className=\"text-sm text-gray-600\">\r\n                          {format.description}\r\n                        </div>\r\n                      </div>\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Preview & Generate Panel */}\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle>Report Preview</CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-4\">\r\n            {/* Report Summary */}\r\n            <div className=\"bg-gray-50 p-4 rounded-lg space-y-3\">\r\n              <div>\r\n                <Label className=\"text-sm font-medium text-gray-600\">\r\n                  Entity Type\r\n                </Label>\r\n                <div className=\"flex items-center gap-2 mt-1\">\r\n                  {selectedEntityConfig && (\r\n                    <>\r\n                      <selectedEntityConfig.icon className=\"h-4 w-4 text-gray-600\" />\r\n                      <span className=\"text-sm\">\r\n                        {selectedEntityConfig.name}\r\n                      </span>\r\n                    </>\r\n                  )}\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <Label className=\"text-sm font-medium text-gray-600\">\r\n                  Entity ID\r\n                </Label>\r\n                <p className=\"mt-1 text-sm font-mono bg-white px-2 py-1 rounded border\">\r\n                  {entityId || 'Not specified'}\r\n                </p>\r\n              </div>\r\n\r\n              <div>\r\n                <Label className=\"text-sm font-medium text-gray-600\">\r\n                  Template\r\n                </Label>\r\n                <p className=\"mt-1 text-sm\">\r\n                  {availableTemplates.find(\r\n                    (t: any) => t.id === selectedTemplate\r\n                  )?.name || 'Default Template'}\r\n                </p>\r\n              </div>\r\n\r\n              <div>\r\n                <Label className=\"text-sm font-medium text-gray-600\">\r\n                  Format\r\n                </Label>\r\n                <p className=\"mt-1 text-sm\">\r\n                  {EXPORT_FORMATS.find(f => f.id === selectedFormat)?.name}\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Error Display */}\r\n            {error && (\r\n              <Alert variant=\"destructive\">\r\n                <AlertDescription>{error}</AlertDescription>\r\n              </Alert>\r\n            )}\r\n\r\n            {/* Generate Button */}\r\n            <Button\r\n              onClick={handleGenerateReport}\r\n              disabled={!entityId.trim() || isGenerating}\r\n              className=\"w-full\"\r\n              size=\"lg\"\r\n            >\r\n              {isGenerating ? (\r\n                <>\r\n                  <LoadingSpinner className=\"mr-2 h-4 w-4\" />\r\n                  Generating Report...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Download className=\"mr-2 h-4 w-4\" />\r\n                  Generate Individual Report\r\n                </>\r\n              )}\r\n            </Button>\r\n\r\n            <p className=\"text-xs text-gray-500 text-center\">\r\n              Report will include detailed information about the selected{' '}\r\n              {selectedEntityConfig?.name.toLowerCase()}\r\n            </p>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      {/* Quick Actions */}\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>Quick Actions</CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n            {ENTITY_TYPES.map(type => {\r\n              const Icon = type.icon;\r\n              return (\r\n                <Button\r\n                  key={type.id}\r\n                  variant=\"outline\"\r\n                  className=\"h-auto p-4 flex flex-col items-center gap-2\"\r\n                  onClick={() => {\r\n                    setSelectedEntityType(type.id);\r\n                    setEntityId('');\r\n                  }}\r\n                >\r\n                  <Icon className=\"h-6 w-6\" />\r\n                  <div className=\"text-center\">\r\n                    <div className=\"font-medium\">{type.name} Report</div>\r\n                    <div className=\"text-xs text-gray-600\">\r\n                      Generate {type.name.toLowerCase()} report\r\n                    </div>\r\n                  </div>\r\n                </Button>\r\n              );\r\n            })}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAIA;AACA;;;;;;;;;;;;;;AAEA;;CAEC,GACD,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,MAAM,2NAAA,CAAA,gBAAa;QACnB,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;QACd,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,mMAAA,CAAA,MAAG;QACT,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,aAAa;QACb,aAAa;IACf;CACD;AAED;;CAEC,GACD,MAAM,iBAAiB;IACrB;QAAE,IAAI;QAAO,MAAM;QAAO,aAAa;IAAkC;IACzE;QAAE,IAAI;QAAS,MAAM;QAAS,aAAa;IAAqB;IAChE;QAAE,IAAI;QAAO,MAAM;QAAO,aAAa;IAAqB;CAC7D;AAaM,MAAM,4BAET,CAAC,EACH,oBAAoB,aAAa,EACjC,kBAAkB,EAAE,EACpB,iBAAiB,EAClB;;IACC,mBAAmB;IACnB,MAAM,CAAC,oBAAoB,sBAAsB,GAC/C,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,QAAQ;IACR,MAAM,EAAE,wBAAwB,EAAE,YAAY,EAAE,KAAK,EAAE,GACrD,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;IACpB,MAAM,EAAE,SAAS,EAAE,WAAW,gBAAgB,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,qBAAkB,AAAD;IAEpE;;GAEC,GACD,MAAM,uBAAuB;QAC3B,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB;QACF;QAEA,IAAI;YACF,MAAM,SAAS,MAAM,yBAAyB;gBAC5C,YAAY;gBACZ,UAAU,SAAS,IAAI;gBACvB,UAAU;gBACV,QAAQ;YACV;YAEA,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;QACzD;IACF;IAEA;;GAEC,GACD,MAAM,uBAAuB,aAAa,IAAI,CAC5C,CAAA,OAAQ,KAAK,EAAE,KAAK;IAGtB;;GAEC,GACD,MAAM,qBACJ,WAAW,OAAO,CAAC,WACjB,SAAS,WAAW,CAAC,QAAQ,CAAC,wBAC3B,EAAE;IAET,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAU,WAAU;;0CACjC,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAKhC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIlC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAa;;;;;;0DAC9B,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO;gDACP,eAAe;;kEAEf,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;kEACX,aAAa,GAAG,CAAC,CAAA;4DAChB,MAAM,OAAO,KAAK,IAAI;4DACtB,qBACE,6LAAC,qIAAA,CAAA,aAAU;gEAAe,OAAO,KAAK,EAAE;0EACtC,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;;;;;;sFAChB,6LAAC;;8FACC,6LAAC;oFAAI,WAAU;8FAAe,KAAK,IAAI;;;;;;8FACvC,6LAAC;oFAAI,WAAU;8FACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;;+DANR,KAAK,EAAE;;;;;wDAY5B;;;;;;;;;;;;;;;;;;kDAMN,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAW,WAAU;;oDACjC,sBAAsB;oDAAK;;;;;;;0DAE9B,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO;gDACP,UAAU,CAAA,IAAK,YAAY,EAAE,MAAM,CAAC,KAAK;gDACzC,aAAa,sBAAsB;gDACnC,WAAU;;;;;;0DAEZ,6LAAC;gDAAE,WAAU;0DACV,sBAAsB;;;;;;;;;;;;kDAK3B,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAa;;;;;;4CAC7B,iCACC,6LAAC,iJAAA,CAAA,iBAAc;;;;qEAEf,6LAAC,qIAAA,CAAA,SAAM;gDACL,OAAO;gDACP,eAAe;;kEAEf,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0EACZ,6LAAC,qIAAA,CAAA,aAAU;gEAAC,OAAM;0EAChB,cAAA,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;sFAAc;;;;;;sFAC7B,6LAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;;;;;;4DAK1C,mBAAmB,GAAG,CAAC,CAAC,yBACvB,6LAAC,qIAAA,CAAA,aAAU;oEAAmB,OAAO,SAAS,EAAE;8EAC9C,cAAA,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FAAe,SAAS,IAAI;;;;;;0FAC3C,6LAAC;gFAAI,WAAU;0FACZ,SAAS,WAAW;;;;;;;;;;;;mEAJV,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;kDAetC,6LAAC;;0DACC,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAa;;;;;;0DAC9B,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAgB,eAAe;;kEAC5C,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,qIAAA,CAAA,gBAAa;kEACX,eAAe,GAAG,CAAC,CAAA,uBAClB,6LAAC,qIAAA,CAAA,aAAU;gEAAiB,OAAO,OAAO,EAAE;0EAC1C,cAAA,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;sFAAe,OAAO,IAAI;;;;;;sFACzC,6LAAC;4EAAI,WAAU;sFACZ,OAAO,WAAW;;;;;;;;;;;;+DAJR,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgBtC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAoC;;;;;;kEAGrD,6LAAC;wDAAI,WAAU;kEACZ,sCACC;;8EACE,6LAAC,qBAAqB,IAAI;oEAAC,WAAU;;;;;;8EACrC,6LAAC;oEAAK,WAAU;8EACb,qBAAqB,IAAI;;;;;;;;;;;;;;;;;;;0DAOpC,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAoC;;;;;;kEAGrD,6LAAC;wDAAE,WAAU;kEACV,YAAY;;;;;;;;;;;;0DAIjB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAoC;;;;;;kEAGrD,6LAAC;wDAAE,WAAU;kEACV,mBAAmB,IAAI,CACtB,CAAC,IAAW,EAAE,EAAE,KAAK,mBACpB,QAAQ;;;;;;;;;;;;0DAIf,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAoC;;;;;;kEAGrD,6LAAC;wDAAE,WAAU;kEACV,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;;;;;;;;;;;;;;;;;;oCAMzD,uBACC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDACb,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;sDAAE;;;;;;;;;;;kDAKvB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,CAAC,SAAS,IAAI,MAAM;wCAC9B,WAAU;wCACV,MAAK;kDAEJ,6BACC;;8DACE,6LAAC,iJAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;gDAAiB;;yEAI7C;;8DACE,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;kDAM3C,6LAAC;wCAAE,WAAU;;4CAAoC;4CACa;4CAC3D,sBAAsB,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAA;gCAChB,MAAM,OAAO,KAAK,IAAI;gCACtB,qBACE,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS;wCACP,sBAAsB,KAAK,EAAE;wCAC7B,YAAY;oCACd;;sDAEA,6LAAC;4CAAK,WAAU;;;;;;sDAChB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDAAe,KAAK,IAAI;wDAAC;;;;;;;8DACxC,6LAAC;oDAAI,WAAU;;wDAAwB;wDAC3B,KAAK,IAAI,CAAC,WAAW;wDAAG;;;;;;;;;;;;;;mCAZjC,KAAK,EAAE;;;;;4BAiBlB;;;;;;;;;;;;;;;;;;;;;;;AAMZ;GA5Ta;;QAgBT,6KAAA,CAAA,sBAAmB;QAC8B,6KAAA,CAAA,qBAAkB;;;KAjB1D", "debugId": null}}, {"offset": {"line": 1833, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/generation/AggregateReportGenerator.tsx"], "sourcesContent": ["/**\r\n * @file AggregateReportGenerator.tsx\r\n * @description Component for generating aggregate entity reports\r\n */\r\n\r\nimport { subDays } from 'date-fns';\r\nimport {\r\n  BarChart3,\r\n  Calendar,\r\n  Car,\r\n  ClipboardList,\r\n  Download,\r\n  FileText,\r\n  Filter,\r\n  TrendingUp,\r\n  Users,\r\n} from 'lucide-react';\r\nimport React, { useState } from 'react';\r\n\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { Label } from '@/components/ui/label';\r\nimport { LoadingSpinner } from '@/components/ui/loading-spinner';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { toastService } from '@/lib/services/toastService';\r\n\r\nimport { DateRangePicker } from '../components/DateRangePicker';\r\nimport { useExport } from '../exports/hooks/useExport';\r\nimport {\r\n  useReportGeneration,\r\n  useReportTemplates,\r\n} from '../hooks/useReportGeneration';\r\n\r\n/**\r\n * Entity type configurations for aggregate reports\r\n */\r\nconst ENTITY_TYPES = [\r\n  {\r\n    color: 'bg-blue-100 text-blue-800',\r\n    description: 'Aggregate delegation analytics and trends',\r\n    icon: ClipboardList,\r\n    id: 'delegations',\r\n    metrics: [\r\n      'Total Count',\r\n      'Completion Rate',\r\n      'Average Duration',\r\n      'Status Distribution',\r\n    ],\r\n    name: 'Delegations',\r\n  },\r\n  {\r\n    color: 'bg-green-100 text-green-800',\r\n    description: 'Task performance and completion analytics',\r\n    icon: FileText,\r\n    id: 'tasks',\r\n    metrics: [\r\n      'Total Tasks',\r\n      'Completion Rate',\r\n      'Average Time',\r\n      'Priority Distribution',\r\n    ],\r\n    name: 'Tasks',\r\n  },\r\n  {\r\n    color: 'bg-orange-100 text-orange-800',\r\n    description: 'Vehicle utilization and maintenance analytics',\r\n    icon: Car,\r\n    id: 'vehicles',\r\n    metrics: [\r\n      'Fleet Size',\r\n      'Utilization Rate',\r\n      'Maintenance Costs',\r\n      'Performance Metrics',\r\n    ],\r\n    name: 'Vehicles',\r\n  },\r\n  {\r\n    color: 'bg-purple-100 text-purple-800',\r\n    description: 'Employee performance and workload analytics',\r\n    icon: Users,\r\n    id: 'employees',\r\n    metrics: [\r\n      'Total Employees',\r\n      'Performance Scores',\r\n      'Workload Distribution',\r\n      'Availability',\r\n    ],\r\n    name: 'Employees',\r\n  },\r\n];\r\n\r\n/**\r\n * Export format options\r\n */\r\nconst EXPORT_FORMATS = [\r\n  { description: 'Formatted analytics report', id: 'pdf', name: 'PDF' },\r\n  { description: 'Spreadsheet with charts', id: 'excel', name: 'Excel' },\r\n  { description: 'Raw data export', id: 'csv', name: 'CSV' },\r\n];\r\n\r\n/**\r\n * Filter options for different entity types\r\n */\r\nconst FILTER_OPTIONS = {\r\n  delegations: [\r\n    {\r\n      id: 'status',\r\n      name: 'Status',\r\n      options: ['Active', 'Completed', 'Pending', 'Cancelled'],\r\n      type: 'select',\r\n    },\r\n    {\r\n      id: 'priority',\r\n      name: 'Priority',\r\n      options: ['High', 'Medium', 'Low'],\r\n      type: 'select',\r\n    },\r\n    { id: 'location', name: 'Location', type: 'text' },\r\n  ],\r\n  employees: [\r\n    { id: 'department', name: 'Department', type: 'text' },\r\n    { id: 'position', name: 'Position', type: 'text' },\r\n    {\r\n      id: 'status',\r\n      name: 'Status',\r\n      options: ['Active', 'Inactive', 'On Leave'],\r\n      type: 'select',\r\n    },\r\n  ],\r\n  tasks: [\r\n    {\r\n      id: 'status',\r\n      name: 'Status',\r\n      options: ['Pending', 'In Progress', 'Completed', 'Cancelled'],\r\n      type: 'select',\r\n    },\r\n    {\r\n      id: 'priority',\r\n      name: 'Priority',\r\n      options: ['High', 'Medium', 'Low'],\r\n      type: 'select',\r\n    },\r\n    { id: 'assignee', name: 'Assignee', type: 'text' },\r\n  ],\r\n  vehicles: [\r\n    {\r\n      id: 'status',\r\n      name: 'Status',\r\n      options: ['Active', 'Maintenance', 'Inactive'],\r\n      type: 'select',\r\n    },\r\n    { id: 'type', name: 'Vehicle Type', type: 'text' },\r\n    { id: 'location', name: 'Location', type: 'text' },\r\n  ],\r\n};\r\n\r\ninterface AggregateReportGeneratorProps {\r\n  defaultEntityType?: string;\r\n  onReportGenerated?: (result: any) => void;\r\n}\r\n\r\n/**\r\n * AggregateReportGenerator Component\r\n *\r\n * Provides interface for generating aggregate analytics reports for entity types.\r\n */\r\nexport const AggregateReportGenerator: React.FC<\r\n  AggregateReportGeneratorProps\r\n> = ({ defaultEntityType = 'delegations', onReportGenerated }) => {\r\n  // State management\r\n  const [selectedEntityType, setSelectedEntityType] =\r\n    useState<string>(defaultEntityType);\r\n  const [selectedTemplate, setSelectedTemplate] = useState<string>('default');\r\n  const [selectedFormat, setSelectedFormat] = useState<string>('pdf');\r\n  const [dateRange, setDateRange] = useState<null | { from: Date; to: Date }>({\r\n    from: subDays(new Date(), 29), // Default to last 30 days\r\n    to: new Date(),\r\n  });\r\n  const [filters, setFilters] = useState<Record<string, any>>({});\r\n  const [includeCharts, setIncludeCharts] = useState<boolean>(true);\r\n  const [includeTrends, setIncludeTrends] = useState<boolean>(true);\r\n\r\n  // Hooks\r\n  const { error, generateAggregateReport, isGenerating } =\r\n    useReportGeneration();\r\n  const { isLoading: templatesLoading, templates } = useReportTemplates();\r\n  const { exportReportToExcel, exportReportToPDF, exportToCSV } = useExport();\r\n\r\n  /**\r\n   * Handle filter change\r\n   */\r\n  const handleFilterChange = (filterId: string, value: any) => {\r\n    setFilters(prev => ({\r\n      ...prev,\r\n      [filterId]: value,\r\n    }));\r\n  };\r\n\r\n  /**\r\n   * Handle report generation and export\r\n   */\r\n  const handleGenerateReport = async () => {\r\n    try {\r\n      const reportFilters = {\r\n        ...filters,\r\n        ...(dateRange && {\r\n          dateRange: {\r\n            from: dateRange.from.toISOString(),\r\n            to: dateRange.to.toISOString(),\r\n          },\r\n        }),\r\n      };\r\n\r\n      let result;\r\n\r\n      try {\r\n        // Generate the report data from backend\r\n        result = await generateAggregateReport({\r\n          entityType: selectedEntityType,\r\n          filters: reportFilters,\r\n          format: 'json', // Always get JSON data first\r\n          options: {\r\n            includeCharts,\r\n            includeTrends,\r\n          },\r\n          template: selectedTemplate,\r\n        });\r\n      } catch (apiError) {\r\n        console.warn('API call failed, providing fallback data:', apiError);\r\n\r\n        // Provide fallback data structure when API fails\r\n        result = {\r\n          data: {\r\n            priorityDistribution: [],\r\n            records: [],\r\n            statusDistribution: [],\r\n            summary: {\r\n              generatedAt: new Date().toISOString(),\r\n              message: `Unable to fetch ${selectedEntityType} data from server. This is a sample report with fallback data.`,\r\n              note: 'Please check your connection and try again.',\r\n            },\r\n            totalCount: 0,\r\n          },\r\n          metadata: {\r\n            entityType: selectedEntityType,\r\n            generatedAt: new Date().toISOString(),\r\n            generatedBy: 'System (Fallback)',\r\n            id: `fallback_${Date.now()}`,\r\n            note: 'Generated with fallback data due to API unavailability',\r\n            status: 'fallback',\r\n          },\r\n        };\r\n      }\r\n\r\n      // Validate that we have data to export\r\n      if (!result) {\r\n        throw new Error('No report data received from server');\r\n      }\r\n\r\n      // Ensure data structure exists (even if empty)\r\n      if (!result.data) {\r\n        result.data = {\r\n          priorityDistribution: [],\r\n          records: [],\r\n          statusDistribution: [],\r\n          summary: {\r\n            generatedAt: new Date().toISOString(),\r\n            message: 'No data available for the selected criteria',\r\n          },\r\n          totalCount: 0,\r\n        };\r\n      }\r\n\r\n      // Now export the data in the selected format\r\n      const reportTitle = `${selectedEntityType.charAt(0).toUpperCase() + selectedEntityType.slice(1)} Analytics Report`;\r\n      const filename = `${selectedEntityType}-analytics-${new Date().toISOString().split('T')[0]}`;\r\n\r\n      // Export using the selected format with proper error handling\r\n      try {\r\n        // Show initial feedback to user\r\n        toastService.show({\r\n          description: `Creating ${selectedFormat.toUpperCase()} report for ${selectedEntityType}...`,\r\n          duration: 2000,\r\n          title: 'Generating Report',\r\n        });\r\n\r\n        switch (selectedFormat) {\r\n          case 'csv': {\r\n            const csvData = Array.isArray(result.data)\r\n              ? result.data\r\n              : [result.data];\r\n            exportToCSV(csvData, { filename });\r\n            console.log('CSV export completed successfully');\r\n            break;\r\n          }\r\n          case 'excel': {\r\n            exportReportToExcel(result, selectedEntityType, filename);\r\n            console.log('Excel export completed successfully');\r\n            break;\r\n          }\r\n          case 'pdf': {\r\n            console.log('Starting PDF export with data:', result);\r\n            await exportReportToPDF(\r\n              result,\r\n              selectedEntityType as\r\n                | 'delegations'\r\n                | 'employees'\r\n                | 'tasks'\r\n                | 'vehicles',\r\n              reportTitle,\r\n              filename\r\n            );\r\n            console.log('PDF export completed successfully');\r\n            break;\r\n          }\r\n          default: {\r\n            throw new Error(`Unsupported export format: ${selectedFormat}`);\r\n          }\r\n        }\r\n\r\n        // Only show success message after export completes successfully\r\n        toastService.success(\r\n          'Report Generated Successfully',\r\n          `${selectedFormat.toUpperCase()} report has been generated and downloaded. ID: ${result.metadata?.id || 'N/A'}`\r\n        );\r\n      } catch (exportError: any) {\r\n        console.error(\r\n          `Failed to export ${selectedFormat} report:`,\r\n          exportError\r\n        );\r\n        toastService.error(\r\n          'Export Failed',\r\n          `Report was generated but ${selectedFormat.toUpperCase()} export failed: ${exportError.message || 'Unknown export error'}`\r\n        );\r\n        throw new Error(\r\n          `Report generated successfully but export failed: ${exportError.message || 'Unknown export error'}`\r\n        );\r\n      }\r\n\r\n      onReportGenerated?.(result);\r\n    } catch (error) {\r\n      console.error('Failed to generate aggregate report:', error);\r\n\r\n      // Re-throw the error to ensure it's properly handled by the useReportGeneration hook\r\n      // This will set the error state which is displayed in the UI\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Get selected entity type configuration\r\n   */\r\n  const selectedEntityConfig = ENTITY_TYPES.find(\r\n    type => type.id === selectedEntityType\r\n  );\r\n\r\n  /**\r\n   * Filter templates for selected entity type\r\n   */\r\n  const availableTemplates =\r\n    templates?.filter((template: any) =>\r\n      template.entityTypes.includes(selectedEntityType)\r\n    ) || [];\r\n\r\n  /**\r\n   * Get filter options for selected entity type\r\n   */\r\n  const entityFilters =\r\n    FILTER_OPTIONS[selectedEntityType as keyof typeof FILTER_OPTIONS] || [];\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h3 className=\"text-xl font-semibold text-gray-900\">\r\n            Aggregate Report Generator\r\n          </h3>\r\n          <p className=\"mt-1 text-gray-600\">\r\n            Generate analytics reports with aggregated data and insights\r\n          </p>\r\n        </div>\r\n        <Badge className=\"flex items-center gap-2\" variant=\"outline\">\r\n          <BarChart3 className=\"size-4\" />\r\n          Aggregate Report\r\n        </Badge>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 gap-6 lg:grid-cols-3\">\r\n        {/* Configuration Panel */}\r\n        <div className=\"space-y-6 lg:col-span-2\">\r\n          {/* Entity Type Selection */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <TrendingUp className=\"size-5\" />\r\n                Entity Type & Analytics\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              <div className=\"grid grid-cols-1 gap-4 md:grid-cols-2\">\r\n                {ENTITY_TYPES.map(entity => {\r\n                  const Icon = entity.icon;\r\n                  const isSelected = selectedEntityType === entity.id;\r\n\r\n                  return (\r\n                    <div\r\n                      className={`\r\n                        cursor-pointer rounded-lg border p-4 transition-all\r\n                        ${\r\n                          isSelected\r\n                            ? 'border-blue-500 bg-blue-50'\r\n                            : 'border-gray-200 hover:border-gray-300'\r\n                        }\r\n                      `}\r\n                      key={entity.id}\r\n                      onClick={() => setSelectedEntityType(entity.id)}\r\n                    >\r\n                      <div className=\"flex items-start gap-3\">\r\n                        <Icon className=\"mt-1 size-6 text-gray-600\" />\r\n                        <div className=\"flex-1\">\r\n                          <div className=\"mb-2 flex items-center gap-2\">\r\n                            <span className=\"font-medium\">{entity.name}</span>\r\n                            <Badge className={entity.color} variant=\"secondary\">\r\n                              Analytics\r\n                            </Badge>\r\n                          </div>\r\n                          <p className=\"mb-3 text-sm text-gray-600\">\r\n                            {entity.description}\r\n                          </p>\r\n                          <div className=\"space-y-1\">\r\n                            {entity.metrics.map(metric => (\r\n                              <div\r\n                                className=\"flex items-center gap-1 text-xs text-gray-500\"\r\n                                key={metric}\r\n                              >\r\n                                <div className=\"size-1 rounded-full bg-gray-400\" />\r\n                                {metric}\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                })}\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Filters */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center gap-2\">\r\n                <Filter className=\"size-5\" />\r\n                Filters & Date Range\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              {/* Date Range */}\r\n              <div>\r\n                <Label className=\"mb-2 flex items-center gap-2\">\r\n                  <Calendar className=\"size-4\" />\r\n                  Date Range\r\n                </Label>\r\n                <DateRangePicker\r\n                  onChange={setDateRange}\r\n                  placeholder=\"Select date range for analytics\"\r\n                  value={dateRange}\r\n                />\r\n              </div>\r\n\r\n              {/* Entity-specific filters */}\r\n              {entityFilters.length > 0 && (\r\n                <div className=\"space-y-3\">\r\n                  <Label className=\"text-sm font-medium\">Entity Filters</Label>\r\n                  <div className=\"grid grid-cols-1 gap-4 md:grid-cols-2\">\r\n                    {entityFilters.map(filter => (\r\n                      <div key={filter.id}>\r\n                        <Label className=\"mb-1 block text-sm\">\r\n                          {filter.name}\r\n                        </Label>\r\n                        {filter.type === 'select' ? (\r\n                          <Select\r\n                            onValueChange={value => {\r\n                              // Convert 'all' back to empty string for filtering logic\r\n                              const filterValue = value === 'all' ? '' : value;\r\n                              handleFilterChange(filter.id, filterValue);\r\n                            }}\r\n                            value={filters[filter.id] || 'all'}\r\n                          >\r\n                            <SelectTrigger className=\"h-8\">\r\n                              <SelectValue\r\n                                placeholder={`Select ${filter.name.toLowerCase()}`}\r\n                              />\r\n                            </SelectTrigger>\r\n                            <SelectContent>\r\n                              <SelectItem value=\"all\">\r\n                                All {filter.name}s\r\n                              </SelectItem>\r\n                              {filter.options?.map(option => (\r\n                                <SelectItem key={option} value={option}>\r\n                                  {option}\r\n                                </SelectItem>\r\n                              ))}\r\n                            </SelectContent>\r\n                          </Select>\r\n                        ) : (\r\n                          <input\r\n                            className=\"h-8 w-full rounded-md border border-gray-300 px-3 text-sm\"\r\n                            onChange={e =>\r\n                              handleFilterChange(filter.id, e.target.value)\r\n                            }\r\n                            placeholder={`Filter by ${filter.name.toLowerCase()}`}\r\n                            type=\"text\"\r\n                            value={filters[filter.id] || ''}\r\n                          />\r\n                        )}\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {/* Report Options */}\r\n              <div className=\"space-y-3\">\r\n                <Label className=\"text-sm font-medium\">Report Options</Label>\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <Checkbox\r\n                      checked={includeCharts}\r\n                      id=\"includeCharts\"\r\n                      onCheckedChange={checked =>\r\n                        setIncludeCharts(checked === true)\r\n                      }\r\n                    />\r\n                    <Label className=\"text-sm\" htmlFor=\"includeCharts\">\r\n                      Include Charts & Visualizations\r\n                    </Label>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <Checkbox\r\n                      checked={includeTrends}\r\n                      id=\"includeTrends\"\r\n                      onCheckedChange={checked =>\r\n                        setIncludeTrends(checked === true)\r\n                      }\r\n                    />\r\n                    <Label className=\"text-sm\" htmlFor=\"includeTrends\">\r\n                      Include Trend Analysis\r\n                    </Label>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Summary & Generate Panel */}\r\n        <div className=\"space-y-6\">\r\n          {/* Report Configuration */}\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>Report Configuration</CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-4\">\r\n              {/* Template Selection */}\r\n              <div>\r\n                <Label className=\"mb-2 block\">Template</Label>\r\n                {templatesLoading ? (\r\n                  <LoadingSpinner />\r\n                ) : (\r\n                  <Select\r\n                    onValueChange={setSelectedTemplate}\r\n                    value={selectedTemplate}\r\n                  >\r\n                    <SelectTrigger>\r\n                      <SelectValue placeholder=\"Select template\" />\r\n                    </SelectTrigger>\r\n                    <SelectContent>\r\n                      <SelectItem value=\"default\">Default Analytics</SelectItem>\r\n                      {availableTemplates.map((template: any) => (\r\n                        <SelectItem key={template.id} value={template.id}>\r\n                          {template.name}\r\n                        </SelectItem>\r\n                      ))}\r\n                    </SelectContent>\r\n                  </Select>\r\n                )}\r\n              </div>\r\n\r\n              {/* Export Format */}\r\n              <div>\r\n                <Label className=\"mb-2 block\">Export Format</Label>\r\n                <Select\r\n                  onValueChange={setSelectedFormat}\r\n                  value={selectedFormat}\r\n                >\r\n                  <SelectTrigger>\r\n                    <SelectValue />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    {EXPORT_FORMATS.map(format => (\r\n                      <SelectItem key={format.id} value={format.id}>\r\n                        <div>\r\n                          <div className=\"font-medium\">{format.name}</div>\r\n                          <div className=\"text-sm text-gray-600\">\r\n                            {format.description}\r\n                          </div>\r\n                        </div>\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n\r\n              {/* Summary */}\r\n              <div className=\"space-y-2 rounded-lg bg-gray-50 p-3\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  {selectedEntityConfig && (\r\n                    <>\r\n                      <selectedEntityConfig.icon className=\"size-4\" />\r\n                      <span className=\"text-sm font-medium\">\r\n                        {selectedEntityConfig.name} Analytics\r\n                      </span>\r\n                    </>\r\n                  )}\r\n                </div>\r\n                {dateRange && (\r\n                  <p className=\"text-xs text-gray-600\">\r\n                    {dateRange.from.toLocaleDateString()} -{' '}\r\n                    {dateRange.to.toLocaleDateString()}\r\n                  </p>\r\n                )}\r\n                <div className=\"flex flex-wrap gap-1\">\r\n                  {Object.entries(filters)\r\n                    .filter(([_, value]) => value)\r\n                    .map(([key, value]) => (\r\n                      <Badge className=\"text-xs\" key={key} variant=\"outline\">\r\n                        {key}: {value}\r\n                      </Badge>\r\n                    ))}\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Generate Button */}\r\n          <Card>\r\n            <CardContent className=\"pt-6\">\r\n              {error && (\r\n                <Alert className=\"mb-4\" variant=\"destructive\">\r\n                  <AlertDescription>{error}</AlertDescription>\r\n                </Alert>\r\n              )}\r\n\r\n              <Button\r\n                className=\"w-full\"\r\n                disabled={isGenerating}\r\n                onClick={handleGenerateReport}\r\n                size=\"lg\"\r\n              >\r\n                {isGenerating ? (\r\n                  <>\r\n                    <LoadingSpinner className=\"mr-2 size-4\" />\r\n                    Generating Analytics...\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <Download className=\"mr-2 size-4\" />\r\n                    Generate Aggregate Report\r\n                  </>\r\n                )}\r\n              </Button>\r\n\r\n              <p className=\"mt-2 text-center text-xs text-gray-500\">\r\n                Report will include aggregated analytics and insights\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;AAKA;;CAEC,GACD,MAAM,eAAe;IACnB;QACE,OAAO;QACP,aAAa;QACb,MAAM,2NAAA,CAAA,gBAAa;QACnB,IAAI;QACJ,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,iNAAA,CAAA,WAAQ;QACd,IAAI;QACJ,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,mMAAA,CAAA,MAAG;QACT,IAAI;QACJ,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,MAAM;IACR;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,uMAAA,CAAA,QAAK;QACX,IAAI;QACJ,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,MAAM;IACR;CACD;AAED;;CAEC,GACD,MAAM,iBAAiB;IACrB;QAAE,aAAa;QAA8B,IAAI;QAAO,MAAM;IAAM;IACpE;QAAE,aAAa;QAA2B,IAAI;QAAS,MAAM;IAAQ;IACrE;QAAE,aAAa;QAAmB,IAAI;QAAO,MAAM;IAAM;CAC1D;AAED;;CAEC,GACD,MAAM,iBAAiB;IACrB,aAAa;QACX;YACE,IAAI;YACJ,MAAM;YACN,SAAS;gBAAC;gBAAU;gBAAa;gBAAW;aAAY;YACxD,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;gBAAC;gBAAQ;gBAAU;aAAM;YAClC,MAAM;QACR;QACA;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAO;KAClD;IACD,WAAW;QACT;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAO;QACrD;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAO;QACjD;YACE,IAAI;YACJ,MAAM;YACN,SAAS;gBAAC;gBAAU;gBAAY;aAAW;YAC3C,MAAM;QACR;KACD;IACD,OAAO;QACL;YACE,IAAI;YACJ,MAAM;YACN,SAAS;gBAAC;gBAAW;gBAAe;gBAAa;aAAY;YAC7D,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;gBAAC;gBAAQ;gBAAU;aAAM;YAClC,MAAM;QACR;QACA;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAO;KAClD;IACD,UAAU;QACR;YACE,IAAI;YACJ,MAAM;YACN,SAAS;gBAAC;gBAAU;gBAAe;aAAW;YAC9C,MAAM;QACR;QACA;YAAE,IAAI;YAAQ,MAAM;YAAgB,MAAM;QAAO;QACjD;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAO;KAClD;AACH;AAYO,MAAM,2BAET,CAAC,EAAE,oBAAoB,aAAa,EAAE,iBAAiB,EAAE;;IAC3D,mBAAmB;IACnB,MAAM,CAAC,oBAAoB,sBAAsB,GAC/C,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;QAC1E,MAAM,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;QAC1B,IAAI,IAAI;IACV;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAE5D,QAAQ;IACR,MAAM,EAAE,KAAK,EAAE,uBAAuB,EAAE,YAAY,EAAE,GACpD,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;IACpB,MAAM,EAAE,WAAW,gBAAgB,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,qBAAkB,AAAD;IACpE,MAAM,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD;IAExE;;GAEC,GACD,MAAM,qBAAqB,CAAC,UAAkB;QAC5C,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,SAAS,EAAE;YACd,CAAC;IACH;IAEA;;GAEC,GACD,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,gBAAgB;gBACpB,GAAG,OAAO;gBACV,GAAI,aAAa;oBACf,WAAW;wBACT,MAAM,UAAU,IAAI,CAAC,WAAW;wBAChC,IAAI,UAAU,EAAE,CAAC,WAAW;oBAC9B;gBACF,CAAC;YACH;YAEA,IAAI;YAEJ,IAAI;gBACF,wCAAwC;gBACxC,SAAS,MAAM,wBAAwB;oBACrC,YAAY;oBACZ,SAAS;oBACT,QAAQ;oBACR,SAAS;wBACP;wBACA;oBACF;oBACA,UAAU;gBACZ;YACF,EAAE,OAAO,UAAU;gBACjB,QAAQ,IAAI,CAAC,6CAA6C;gBAE1D,iDAAiD;gBACjD,SAAS;oBACP,MAAM;wBACJ,sBAAsB,EAAE;wBACxB,SAAS,EAAE;wBACX,oBAAoB,EAAE;wBACtB,SAAS;4BACP,aAAa,IAAI,OAAO,WAAW;4BACnC,SAAS,CAAC,gBAAgB,EAAE,mBAAmB,8DAA8D,CAAC;4BAC9G,MAAM;wBACR;wBACA,YAAY;oBACd;oBACA,UAAU;wBACR,YAAY;wBACZ,aAAa,IAAI,OAAO,WAAW;wBACnC,aAAa;wBACb,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;wBAC5B,MAAM;wBACN,QAAQ;oBACV;gBACF;YACF;YAEA,uCAAuC;YACvC,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,+CAA+C;YAC/C,IAAI,CAAC,OAAO,IAAI,EAAE;gBAChB,OAAO,IAAI,GAAG;oBACZ,sBAAsB,EAAE;oBACxB,SAAS,EAAE;oBACX,oBAAoB,EAAE;oBACtB,SAAS;wBACP,aAAa,IAAI,OAAO,WAAW;wBACnC,SAAS;oBACX;oBACA,YAAY;gBACd;YACF;YAEA,6CAA6C;YAC7C,MAAM,cAAc,GAAG,mBAAmB,MAAM,CAAC,GAAG,WAAW,KAAK,mBAAmB,KAAK,CAAC,GAAG,iBAAiB,CAAC;YAClH,MAAM,WAAW,GAAG,mBAAmB,WAAW,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;YAE5F,8DAA8D;YAC9D,IAAI;gBACF,gCAAgC;gBAChC,yIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAChB,aAAa,CAAC,SAAS,EAAE,eAAe,WAAW,GAAG,YAAY,EAAE,mBAAmB,GAAG,CAAC;oBAC3F,UAAU;oBACV,OAAO;gBACT;gBAEA,OAAQ;oBACN,KAAK;wBAAO;4BACV,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO,IAAI,IACrC,OAAO,IAAI,GACX;gCAAC,OAAO,IAAI;6BAAC;4BACjB,YAAY,SAAS;gCAAE;4BAAS;4BAChC,QAAQ,GAAG,CAAC;4BACZ;wBACF;oBACA,KAAK;wBAAS;4BACZ,oBAAoB,QAAQ,oBAAoB;4BAChD,QAAQ,GAAG,CAAC;4BACZ;wBACF;oBACA,KAAK;wBAAO;4BACV,QAAQ,GAAG,CAAC,kCAAkC;4BAC9C,MAAM,kBACJ,QACA,oBAKA,aACA;4BAEF,QAAQ,GAAG,CAAC;4BACZ;wBACF;oBACA;wBAAS;4BACP,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,gBAAgB;wBAChE;gBACF;gBAEA,gEAAgE;gBAChE,yIAAA,CAAA,eAAY,CAAC,OAAO,CAClB,iCACA,GAAG,eAAe,WAAW,GAAG,+CAA+C,EAAE,OAAO,QAAQ,EAAE,MAAM,OAAO;YAEnH,EAAE,OAAO,aAAkB;gBACzB,QAAQ,KAAK,CACX,CAAC,iBAAiB,EAAE,eAAe,QAAQ,CAAC,EAC5C;gBAEF,yIAAA,CAAA,eAAY,CAAC,KAAK,CAChB,iBACA,CAAC,yBAAyB,EAAE,eAAe,WAAW,GAAG,gBAAgB,EAAE,YAAY,OAAO,IAAI,wBAAwB;gBAE5H,MAAM,IAAI,MACR,CAAC,iDAAiD,EAAE,YAAY,OAAO,IAAI,wBAAwB;YAEvG;YAEA,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YAEtD,qFAAqF;YACrF,6DAA6D;YAC7D,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,uBAAuB,aAAa,IAAI,CAC5C,CAAA,OAAQ,KAAK,EAAE,KAAK;IAGtB;;GAEC,GACD,MAAM,qBACJ,WAAW,OAAO,CAAC,WACjB,SAAS,WAAW,CAAC,QAAQ,CAAC,wBAC3B,EAAE;IAET;;GAEC,GACD,MAAM,gBACJ,cAAc,CAAC,mBAAkD,IAAI,EAAE;IAEzE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,WAAU;wBAA0B,SAAQ;;0CACjD,6LAAC,qNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAW;;;;;;;;;;;;;0BAKpC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAW;;;;;;;;;;;;kDAIrC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAA;gDAChB,MAAM,OAAO,OAAO,IAAI;gDACxB,MAAM,aAAa,uBAAuB,OAAO,EAAE;gDAEnD,qBACE,6LAAC;oDACC,WAAW,CAAC;;wBAEV,EACE,aACI,+BACA,wCACL;sBACH,CAAC;oDAED,SAAS,IAAM,sBAAsB,OAAO,EAAE;8DAE9C,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;;;;;0EAChB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAe,OAAO,IAAI;;;;;;0FAC1C,6LAAC,oIAAA,CAAA,QAAK;gFAAC,WAAW,OAAO,KAAK;gFAAE,SAAQ;0FAAY;;;;;;;;;;;;kFAItD,6LAAC;wEAAE,WAAU;kFACV,OAAO,WAAW;;;;;;kFAErB,6LAAC;wEAAI,WAAU;kFACZ,OAAO,OAAO,CAAC,GAAG,CAAC,CAAA,uBAClB,6LAAC;gFACC,WAAU;;kGAGV,6LAAC;wFAAI,WAAU;;;;;;oFACd;;+EAHI;;;;;;;;;;;;;;;;;;;;;;mDAnBV,OAAO,EAAE;;;;;4CA8BpB;;;;;;;;;;;;;;;;;0CAMN,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAW;;;;;;;;;;;;kDAIjC,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;;0EACf,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAW;;;;;;;kEAGjC,6LAAC,+KAAA,CAAA,kBAAe;wDACd,UAAU;wDACV,aAAY;wDACZ,OAAO;;;;;;;;;;;;4CAKV,cAAc,MAAM,GAAG,mBACtB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAsB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;kEACZ,cAAc,GAAG,CAAC,CAAA,uBACjB,6LAAC;;kFACC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,WAAU;kFACd,OAAO,IAAI;;;;;;oEAEb,OAAO,IAAI,KAAK,yBACf,6LAAC,qIAAA,CAAA,SAAM;wEACL,eAAe,CAAA;4EACb,yDAAyD;4EACzD,MAAM,cAAc,UAAU,QAAQ,KAAK;4EAC3C,mBAAmB,OAAO,EAAE,EAAE;wEAChC;wEACA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI;;0FAE7B,6LAAC,qIAAA,CAAA,gBAAa;gFAAC,WAAU;0FACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oFACV,aAAa,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,WAAW,IAAI;;;;;;;;;;;0FAGtD,6LAAC,qIAAA,CAAA,gBAAa;;kGACZ,6LAAC,qIAAA,CAAA,aAAU;wFAAC,OAAM;;4FAAM;4FACjB,OAAO,IAAI;4FAAC;;;;;;;oFAElB,OAAO,OAAO,EAAE,IAAI,CAAA,uBACnB,6LAAC,qIAAA,CAAA,aAAU;4FAAc,OAAO;sGAC7B;2FADc;;;;;;;;;;;;;;;;6FAOvB,6LAAC;wEACC,WAAU;wEACV,UAAU,CAAA,IACR,mBAAmB,OAAO,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;wEAE9C,aAAa,CAAC,UAAU,EAAE,OAAO,IAAI,CAAC,WAAW,IAAI;wEACrD,MAAK;wEACL,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI;;;;;;;+DArCzB,OAAO,EAAE;;;;;;;;;;;;;;;;0DA+C3B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAsB;;;;;;kEACvC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uIAAA,CAAA,WAAQ;wEACP,SAAS;wEACT,IAAG;wEACH,iBAAiB,CAAA,UACf,iBAAiB,YAAY;;;;;;kFAGjC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,WAAU;wEAAU,SAAQ;kFAAgB;;;;;;;;;;;;0EAIrD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uIAAA,CAAA,WAAQ;wEACP,SAAS;wEACT,IAAG;wEACH,iBAAiB,CAAA,UACf,iBAAiB,YAAY;;;;;;kFAGjC,6LAAC,oIAAA,CAAA,QAAK;wEAAC,WAAU;wEAAU,SAAQ;kFAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAW/D,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DAErB,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAa;;;;;;oDAC7B,iCACC,6LAAC,iJAAA,CAAA,iBAAc;;;;6EAEf,6LAAC,qIAAA,CAAA,SAAM;wDACL,eAAe;wDACf,OAAO;;0EAEP,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oEAAC,aAAY;;;;;;;;;;;0EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;kFACZ,6LAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAU;;;;;;oEAC3B,mBAAmB,GAAG,CAAC,CAAC,yBACvB,6LAAC,qIAAA,CAAA,aAAU;4EAAmB,OAAO,SAAS,EAAE;sFAC7C,SAAS,IAAI;2EADC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;0DAUtC,6LAAC;;kEACC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAa;;;;;;kEAC9B,6LAAC,qIAAA,CAAA,SAAM;wDACL,eAAe;wDACf,OAAO;;0EAEP,6LAAC,qIAAA,CAAA,gBAAa;0EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,6LAAC,qIAAA,CAAA,gBAAa;0EACX,eAAe,GAAG,CAAC,CAAA,uBAClB,6LAAC,qIAAA,CAAA,aAAU;wEAAiB,OAAO,OAAO,EAAE;kFAC1C,cAAA,6LAAC;;8FACC,6LAAC;oFAAI,WAAU;8FAAe,OAAO,IAAI;;;;;;8FACzC,6LAAC;oFAAI,WAAU;8FACZ,OAAO,WAAW;;;;;;;;;;;;uEAJR,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;0DAclC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,sCACC;;8EACE,6LAAC,qBAAqB,IAAI;oEAAC,WAAU;;;;;;8EACrC,6LAAC;oEAAK,WAAU;;wEACb,qBAAqB,IAAI;wEAAC;;;;;;;;;;;;;;oDAKlC,2BACC,6LAAC;wDAAE,WAAU;;4DACV,UAAU,IAAI,CAAC,kBAAkB;4DAAG;4DAAG;4DACvC,UAAU,EAAE,CAAC,kBAAkB;;;;;;;kEAGpC,6LAAC;wDAAI,WAAU;kEACZ,OAAO,OAAO,CAAC,SACb,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,OACvB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAChB,6LAAC,oIAAA,CAAA,QAAK;gEAAC,WAAU;gEAAoB,SAAQ;;oEAC1C;oEAAI;oEAAG;;+DADsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAU5C,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;wCACpB,uBACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;4CAAO,SAAQ;sDAC9B,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;0DAAE;;;;;;;;;;;sDAIvB,6LAAC,qIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,UAAU;4CACV,SAAS;4CACT,MAAK;sDAEJ,6BACC;;kEACE,6LAAC,iJAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;oDAAgB;;6EAI5C;;kEACE,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;;sDAM1C,6LAAC;4CAAE,WAAU;sDAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;GAtgBa;;QAkBT,6KAAA,CAAA,sBAAmB;QAC8B,6KAAA,CAAA,qBAAkB;QACL,+KAAA,CAAA,YAAS;;;KApB9D", "debugId": null}}, {"offset": {"line": 3040, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/generation/ReportHistory.tsx"], "sourcesContent": ["/**\r\n * @file ReportHistory.tsx\r\n * @description Component for viewing and managing report generation history\r\n */\r\n\r\nimport { formatDistanceToNow } from 'date-fns';\r\nimport {\r\n  Calendar,\r\n  Download,\r\n  Eye,\r\n  FileText,\r\n  Filter,\r\n  History,\r\n  RefreshCw,\r\n  Search,\r\n} from 'lucide-react';\r\nimport React, { useState } from 'react';\r\n\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Input } from '@/components/ui/input';\r\nimport { LoadingSpinner } from '@/components/ui/loading-spinner';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\n\r\nimport {\r\n  useReportDownload,\r\n  useReportHistory,\r\n} from '../hooks/useReportGeneration';\r\n\r\n/**\r\n * Report type filter options\r\n */\r\nconst REPORT_TYPE_FILTERS = [\r\n  { label: 'All Types', value: '' },\r\n  { label: 'Comprehensive', value: 'comprehensive' },\r\n  { label: 'Individual', value: 'individual' },\r\n  { label: 'Aggregate', value: 'aggregate' },\r\n];\r\n\r\n/**\r\n * Entity type filter options\r\n */\r\nconst ENTITY_TYPE_FILTERS = [\r\n  { label: 'All Entities', value: '' },\r\n  { label: 'Delegations', value: 'delegations' },\r\n  { label: 'Tasks', value: 'tasks' },\r\n  { label: 'Vehicles', value: 'vehicles' },\r\n  { label: 'Employees', value: 'employees' },\r\n];\r\n\r\n/**\r\n * Status badge configurations\r\n */\r\nconst STATUS_CONFIGS = {\r\n  completed: { color: 'bg-green-100 text-green-800', label: 'Completed' },\r\n  failed: { color: 'bg-red-100 text-red-800', label: 'Failed' },\r\n  processing: { color: 'bg-yellow-100 text-yellow-800', label: 'Processing' },\r\n};\r\n\r\n/**\r\n * Format badge configurations\r\n */\r\nconst FORMAT_CONFIGS = {\r\n  csv: { color: 'bg-blue-100 text-blue-800', label: 'CSV' },\r\n  excel: { color: 'bg-green-100 text-green-800', label: 'Excel' },\r\n  pdf: { color: 'bg-red-100 text-red-800', label: 'PDF' },\r\n};\r\n\r\ninterface ReportHistoryProps {\r\n  onReportSelect?: (report: any) => void;\r\n}\r\n\r\n/**\r\n * ReportHistory Component\r\n *\r\n * Displays history of generated reports with filtering and download capabilities.\r\n */\r\nexport const ReportHistory: React.FC<ReportHistoryProps> = ({\r\n  onReportSelect,\r\n}) => {\r\n  // State management\r\n  const [typeFilter, setTypeFilter] = useState<string>('');\r\n  const [entityFilter, setEntityFilter] = useState<string>('');\r\n  const [searchTerm, setSearchTerm] = useState<string>('');\r\n\r\n  // Hooks\r\n  const { error, isLoading, pagination, refetch, reports } = useReportHistory({\r\n    ...(typeFilter && { type: typeFilter }),\r\n    ...(entityFilter && { entityType: entityFilter }),\r\n  });\r\n  const { downloadError, downloadReport, isDownloading } = useReportDownload();\r\n\r\n  /**\r\n   * Handle report download\r\n   */\r\n  const handleDownload = async (reportId: string) => {\r\n    try {\r\n      await downloadReport(reportId);\r\n    } catch (error) {\r\n      console.error('Failed to download report:', error);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Filter reports based on search term\r\n   */\r\n  const filteredReports = reports.filter((report: any) => {\r\n    if (!searchTerm) return true;\r\n\r\n    const searchLower = searchTerm.toLowerCase();\r\n    return (\r\n      report.id.toLowerCase().includes(searchLower) ||\r\n      report.type.toLowerCase().includes(searchLower) ||\r\n      report.entityType?.toLowerCase().includes(searchLower) ||\r\n      report.entityTypes?.some((type: any) =>\r\n        type.toLowerCase().includes(searchLower)\r\n      )\r\n    );\r\n  });\r\n\r\n  /**\r\n   * Get entity types display for comprehensive reports\r\n   */\r\n  const getEntityTypesDisplay = (report: any) => {\r\n    if (report.type === 'individual') {\r\n      return report.entityType;\r\n    }\r\n    if (report.type === 'aggregate') {\r\n      return report.entityType;\r\n    }\r\n    if (report.entityTypes) {\r\n      return report.entityTypes.join(', ');\r\n    }\r\n    return 'N/A';\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h3 className=\"text-xl font-semibold text-gray-900\">\r\n            Report History\r\n          </h3>\r\n          <p className=\"mt-1 text-gray-600\">\r\n            View and manage your generated reports\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            disabled={isLoading}\r\n            onClick={() => refetch()}\r\n            size=\"sm\"\r\n            variant=\"outline\"\r\n          >\r\n            <RefreshCw\r\n              className={`mr-2 size-4 ${isLoading ? 'animate-spin' : ''}`}\r\n            />\r\n            Refresh\r\n          </Button>\r\n          <Badge className=\"flex items-center gap-2\" variant=\"outline\">\r\n            <History className=\"size-4\" />\r\n            {reports.length} Reports\r\n          </Badge>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Filters */}\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Filter className=\"size-5\" />\r\n            Filters\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"grid grid-cols-1 gap-4 md:grid-cols-4\">\r\n            {/* Search */}\r\n            <div>\r\n              <div className=\"relative\">\r\n                <Search className=\"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-gray-400\" />\r\n                <Input\r\n                  className=\"pl-10\"\r\n                  onChange={e => setSearchTerm(e.target.value)}\r\n                  placeholder=\"Search reports...\"\r\n                  value={searchTerm}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Report Type Filter */}\r\n            <div>\r\n              <Select onValueChange={setTypeFilter} value={typeFilter}>\r\n                <SelectTrigger>\r\n                  <SelectValue placeholder=\"Report Type\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {REPORT_TYPE_FILTERS.map(option => (\r\n                    <SelectItem key={option.value} value={option.value}>\r\n                      {option.label}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n\r\n            {/* Entity Type Filter */}\r\n            <div>\r\n              <Select onValueChange={setEntityFilter} value={entityFilter}>\r\n                <SelectTrigger>\r\n                  <SelectValue placeholder=\"Entity Type\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {ENTITY_TYPE_FILTERS.map(option => (\r\n                    <SelectItem key={option.value} value={option.value}>\r\n                      {option.label}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n\r\n            {/* Clear Filters */}\r\n            <div>\r\n              <Button\r\n                className=\"w-full\"\r\n                onClick={() => {\r\n                  setTypeFilter('');\r\n                  setEntityFilter('');\r\n                  setSearchTerm('');\r\n                }}\r\n                variant=\"outline\"\r\n              >\r\n                Clear Filters\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Error Display */}\r\n      {(error || downloadError) && (\r\n        <Alert variant=\"destructive\">\r\n          <AlertDescription>{String(error || downloadError)}</AlertDescription>\r\n        </Alert>\r\n      )}\r\n\r\n      {/* Reports List */}\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>Generated Reports</CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          {isLoading ? (\r\n            <div className=\"flex items-center justify-center py-8\">\r\n              <LoadingSpinner className=\"size-8\" />\r\n            </div>\r\n          ) : filteredReports.length === 0 ? (\r\n            <div className=\"py-8 text-center\">\r\n              <FileText className=\"mx-auto mb-4 size-12 text-gray-400\" />\r\n              <h3 className=\"mb-2 text-lg font-medium text-gray-900\">\r\n                No Reports Found\r\n              </h3>\r\n              <p className=\"text-gray-600\">\r\n                {reports.length === 0\r\n                  ? 'No reports have been generated yet.'\r\n                  : 'No reports match your current filters.'}\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-4\">\r\n              {filteredReports.map((report: any) => {\r\n                const statusConfig =\r\n                  STATUS_CONFIGS[report.status as keyof typeof STATUS_CONFIGS];\r\n                const formatConfig =\r\n                  FORMAT_CONFIGS[report.format as keyof typeof FORMAT_CONFIGS];\r\n\r\n                return (\r\n                  <div\r\n                    className=\"rounded-lg border p-4 transition-colors hover:bg-gray-50\"\r\n                    key={report.id}\r\n                  >\r\n                    <div className=\"flex items-start justify-between\">\r\n                      <div className=\"flex-1\">\r\n                        <div className=\"mb-2 flex items-center gap-3\">\r\n                          <h4 className=\"font-medium text-gray-900\">\r\n                            {report.id}\r\n                          </h4>\r\n                          <Badge\r\n                            className={statusConfig?.color}\r\n                            variant=\"secondary\"\r\n                          >\r\n                            {statusConfig?.label || report.status}\r\n                          </Badge>\r\n                          <Badge\r\n                            className={formatConfig?.color}\r\n                            variant=\"secondary\"\r\n                          >\r\n                            {formatConfig?.label || report.format}\r\n                          </Badge>\r\n                          <Badge variant=\"outline\">{report.type}</Badge>\r\n                        </div>\r\n\r\n                        <div className=\"grid grid-cols-1 gap-4 text-sm text-gray-600 md:grid-cols-3\">\r\n                          <div>\r\n                            <span className=\"font-medium\">Entity Types:</span>\r\n                            <p className=\"mt-1\">\r\n                              {getEntityTypesDisplay(report)}\r\n                            </p>\r\n                          </div>\r\n                          <div>\r\n                            <span className=\"font-medium\">Generated:</span>\r\n                            <p className=\"mt-1 flex items-center gap-1\">\r\n                              <Calendar className=\"size-3\" />\r\n                              {formatDistanceToNow(\r\n                                new Date(report.generatedAt),\r\n                                { addSuffix: true }\r\n                              )}\r\n                            </p>\r\n                          </div>\r\n                          <div>\r\n                            <span className=\"font-medium\">File Size:</span>\r\n                            <p className=\"mt-1\">{report.fileSize || 'N/A'}</p>\r\n                          </div>\r\n                        </div>\r\n\r\n                        {report.type === 'individual' && report.entityId && (\r\n                          <div className=\"mt-2 text-sm text-gray-600\">\r\n                            <span className=\"font-medium\">Entity ID:</span>{' '}\r\n                            {report.entityId}\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n\r\n                      <div className=\"ml-4 flex items-center gap-2\">\r\n                        {onReportSelect && (\r\n                          <Button\r\n                            onClick={() => onReportSelect(report)}\r\n                            size=\"sm\"\r\n                            variant=\"outline\"\r\n                          >\r\n                            <Eye className=\"mr-1 size-4\" />\r\n                            View\r\n                          </Button>\r\n                        )}\r\n\r\n                        {report.status === 'completed' && (\r\n                          <Button\r\n                            disabled={isDownloading}\r\n                            onClick={() => handleDownload(report.id)}\r\n                            size=\"sm\"\r\n                            variant=\"outline\"\r\n                          >\r\n                            {isDownloading ? (\r\n                              <LoadingSpinner className=\"mr-1 size-4\" />\r\n                            ) : (\r\n                              <Download className=\"mr-1 size-4\" />\r\n                            )}\r\n                            Download\r\n                          </Button>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Pagination */}\r\n      {pagination && pagination.totalPages > 1 && (\r\n        <Card>\r\n          <CardContent className=\"pt-6\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <p className=\"text-sm text-gray-600\">\r\n                Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}\r\n                {Math.min(pagination.page * pagination.limit, pagination.total)}{' '}\r\n                of {pagination.total} reports\r\n              </p>\r\n              <div className=\"flex items-center gap-2\">\r\n                <Button\r\n                  disabled={pagination.page <= 1}\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                >\r\n                  Previous\r\n                </Button>\r\n                <span className=\"text-sm text-gray-600\">\r\n                  Page {pagination.page} of {pagination.totalPages}\r\n                </span>\r\n                <Button\r\n                  disabled={pagination.page >= pagination.totalPages}\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                >\r\n                  Next\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;;;;;;;;;;;;;;AAKA;;CAEC,GACD,MAAM,sBAAsB;IAC1B;QAAE,OAAO;QAAa,OAAO;IAAG;IAChC;QAAE,OAAO;QAAiB,OAAO;IAAgB;IACjD;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAED;;CAEC,GACD,MAAM,sBAAsB;IAC1B;QAAE,OAAO;QAAgB,OAAO;IAAG;IACnC;QAAE,OAAO;QAAe,OAAO;IAAc;IAC7C;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAED;;CAEC,GACD,MAAM,iBAAiB;IACrB,WAAW;QAAE,OAAO;QAA+B,OAAO;IAAY;IACtE,QAAQ;QAAE,OAAO;QAA2B,OAAO;IAAS;IAC5D,YAAY;QAAE,OAAO;QAAiC,OAAO;IAAa;AAC5E;AAEA;;CAEC,GACD,MAAM,iBAAiB;IACrB,KAAK;QAAE,OAAO;QAA6B,OAAO;IAAM;IACxD,OAAO;QAAE,OAAO;QAA+B,OAAO;IAAQ;IAC9D,KAAK;QAAE,OAAO;QAA2B,OAAO;IAAM;AACxD;AAWO,MAAM,gBAA8C,CAAC,EAC1D,cAAc,EACf;;IACC,mBAAmB;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,QAAQ;IACR,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,mBAAgB,AAAD,EAAE;QAC1E,GAAI,cAAc;YAAE,MAAM;QAAW,CAAC;QACtC,GAAI,gBAAgB;YAAE,YAAY;QAAa,CAAC;IAClD;IACA,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD;IAEzE;;GAEC,GACD,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,eAAe;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,YAAY,OAAO;QAExB,MAAM,cAAc,WAAW,WAAW;QAC1C,OACE,OAAO,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACjC,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACnC,OAAO,UAAU,EAAE,cAAc,SAAS,gBAC1C,OAAO,WAAW,EAAE,KAAK,CAAC,OACxB,KAAK,WAAW,GAAG,QAAQ,CAAC;IAGlC;IAEA;;GAEC,GACD,MAAM,wBAAwB,CAAC;QAC7B,IAAI,OAAO,IAAI,KAAK,cAAc;YAChC,OAAO,OAAO,UAAU;QAC1B;QACA,IAAI,OAAO,IAAI,KAAK,aAAa;YAC/B,OAAO,OAAO,UAAU;QAC1B;QACA,IAAI,OAAO,WAAW,EAAE;YACtB,OAAO,OAAO,WAAW,CAAC,IAAI,CAAC;QACjC;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,UAAU;gCACV,SAAS,IAAM;gCACf,MAAK;gCACL,SAAQ;;kDAER,6LAAC,mNAAA,CAAA,YAAS;wCACR,WAAW,CAAC,YAAY,EAAE,YAAY,iBAAiB,IAAI;;;;;;oCAC3D;;;;;;;0CAGJ,6LAAC,oIAAA,CAAA,QAAK;gCAAC,WAAU;gCAA0B,SAAQ;;kDACjD,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAClB,QAAQ,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;0BAMtB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAW;;;;;;;;;;;;kCAIjC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;8CACC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,WAAU;gDACV,UAAU,CAAA,IAAK,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC3C,aAAY;gDACZ,OAAO;;;;;;;;;;;;;;;;;8CAMb,6LAAC;8CACC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,eAAe;wCAAe,OAAO;;0DAC3C,6LAAC,qIAAA,CAAA,gBAAa;0DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,qIAAA,CAAA,gBAAa;0DACX,oBAAoB,GAAG,CAAC,CAAA,uBACvB,6LAAC,qIAAA,CAAA,aAAU;wDAAoB,OAAO,OAAO,KAAK;kEAC/C,OAAO,KAAK;uDADE,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;8CASrC,6LAAC;8CACC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,eAAe;wCAAiB,OAAO;;0DAC7C,6LAAC,qIAAA,CAAA,gBAAa;0DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,qIAAA,CAAA,gBAAa;0DACX,oBAAoB,GAAG,CAAC,CAAA,uBACvB,6LAAC,qIAAA,CAAA,aAAU;wDAAoB,OAAO,OAAO,KAAK;kEAC/C,OAAO,KAAK;uDADE,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;8CASrC,6LAAC;8CACC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS;4CACP,cAAc;4CACd,gBAAgB;4CAChB,cAAc;wCAChB;wCACA,SAAQ;kDACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASR,CAAC,SAAS,aAAa,mBACtB,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;0BACb,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;8BAAE,OAAO,SAAS;;;;;;;;;;;0BAKvC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACT,0BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,iJAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;mCAE1B,gBAAgB,MAAM,KAAK,kBAC7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;8CACV,QAAQ,MAAM,KAAK,IAChB,wCACA;;;;;;;;;;;iDAIR,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,eACJ,cAAc,CAAC,OAAO,MAAM,CAAgC;gCAC9D,MAAM,eACJ,cAAc,CAAC,OAAO,MAAM,CAAgC;gCAE9D,qBACE,6LAAC;oCACC,WAAU;8CAGV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,OAAO,EAAE;;;;;;0EAEZ,6LAAC,oIAAA,CAAA,QAAK;gEACJ,WAAW,cAAc;gEACzB,SAAQ;0EAEP,cAAc,SAAS,OAAO,MAAM;;;;;;0EAEvC,6LAAC,oIAAA,CAAA,QAAK;gEACJ,WAAW,cAAc;gEACzB,SAAQ;0EAEP,cAAc,SAAS,OAAO,MAAM;;;;;;0EAEvC,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW,OAAO,IAAI;;;;;;;;;;;;kEAGvC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,6LAAC;wEAAE,WAAU;kFACV,sBAAsB;;;;;;;;;;;;0EAG3B,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,6LAAC;wEAAE,WAAU;;0FACX,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EACnB,CAAA,GAAA,sJAAA,CAAA,sBAAmB,AAAD,EACjB,IAAI,KAAK,OAAO,WAAW,GAC3B;gFAAE,WAAW;4EAAK;;;;;;;;;;;;;0EAIxB,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAc;;;;;;kFAC9B,6LAAC;wEAAE,WAAU;kFAAQ,OAAO,QAAQ,IAAI;;;;;;;;;;;;;;;;;;oDAI3C,OAAO,IAAI,KAAK,gBAAgB,OAAO,QAAQ,kBAC9C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAc;;;;;;4DAAkB;4DAC/C,OAAO,QAAQ;;;;;;;;;;;;;0DAKtB,6LAAC;gDAAI,WAAU;;oDACZ,gCACC,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,IAAM,eAAe;wDAC9B,MAAK;wDACL,SAAQ;;0EAER,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAgB;;;;;;;oDAKlC,OAAO,MAAM,KAAK,6BACjB,6LAAC,qIAAA,CAAA,SAAM;wDACL,UAAU;wDACV,SAAS,IAAM,eAAe,OAAO,EAAE;wDACvC,MAAK;wDACL,SAAQ;;4DAEP,8BACC,6LAAC,iJAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;qFAE1B,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACpB;;;;;;;;;;;;;;;;;;;mCA7EL,OAAO,EAAE;;;;;4BAqFpB;;;;;;;;;;;;;;;;;YAOP,cAAc,WAAW,UAAU,GAAG,mBACrC,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAAwB;oCAC1B,CAAC,WAAW,IAAI,GAAG,CAAC,IAAI,WAAW,KAAK,GAAG;oCAAE;oCAAI;oCACzD,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,WAAW,KAAK,EAAE,WAAW,KAAK;oCAAG;oCAAI;oCACjE,WAAW,KAAK;oCAAC;;;;;;;0CAEvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,UAAU,WAAW,IAAI,IAAI;wCAC7B,MAAK;wCACL,SAAQ;kDACT;;;;;;kDAGD,6LAAC;wCAAK,WAAU;;4CAAwB;4CAChC,WAAW,IAAI;4CAAC;4CAAK,WAAW,UAAU;;;;;;;kDAElD,6LAAC,qIAAA,CAAA,SAAM;wCACL,UAAU,WAAW,IAAI,IAAI,WAAW,UAAU;wCAClD,MAAK;wCACL,SAAQ;kDACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAzUa;;QASgD,6KAAA,CAAA,mBAAgB;QAIlB,6KAAA,CAAA,oBAAiB;;;KAb/D", "debugId": null}}, {"offset": {"line": 3904, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/generation/ReportGenerationPage.tsx"], "sourcesContent": ["/**\r\n * @file ReportGenerationPage.tsx\r\n * @description Main page for data report generation with tabbed interface\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  FileText,\r\n  BarChart3,\r\n  User,\r\n  History,\r\n  Download,\r\n  Settings,\r\n  Info,\r\n} from 'lucide-react';\r\nimport { DataReportGenerator } from './DataReportGenerator';\r\nimport { IndividualReportGenerator } from './IndividualReportGenerator';\r\nimport { AggregateReportGenerator } from './AggregateReportGenerator';\r\nimport { ReportHistory } from './ReportHistory';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\n\r\n/**\r\n * Tab configurations\r\n */\r\nconst TABS = [\r\n  {\r\n    id: 'comprehensive',\r\n    label: 'Comprehensive Reports',\r\n    icon: FileText,\r\n    description: 'Generate reports across multiple entity types',\r\n    component: DataReportGenerator,\r\n  },\r\n  {\r\n    id: 'individual',\r\n    label: 'Individual Reports',\r\n    icon: User,\r\n    description: 'Generate detailed reports for specific entities',\r\n    component: IndividualReportGenerator,\r\n  },\r\n  {\r\n    id: 'aggregate',\r\n    label: 'Aggregate Analytics',\r\n    icon: BarChart3,\r\n    description: 'Generate analytics reports with aggregated data',\r\n    component: AggregateReportGenerator,\r\n  },\r\n  {\r\n    id: 'history',\r\n    label: 'Report History',\r\n    icon: History,\r\n    description: 'View and manage generated reports',\r\n    component: ReportHistory,\r\n  },\r\n];\r\n\r\n/**\r\n * ReportGenerationPage Component\r\n *\r\n * Main interface for all report generation capabilities.\r\n * Provides tabbed interface for different report types.\r\n */\r\nexport const ReportGenerationPage: React.FC = () => {\r\n  const [activeTab, setActiveTab] = useState<string>('comprehensive');\r\n  const [recentReports, setRecentReports] = useState<any[]>([]);\r\n\r\n  /**\r\n   * Handle successful report generation\r\n   */\r\n  const handleReportGenerated = (result: any) => {\r\n    console.log('Report generated:', result);\r\n\r\n    // Extract metadata from the correct location in the response\r\n    const metadata = result?.data?.metadata || result?.metadata;\r\n\r\n    if (!metadata) {\r\n      console.error('No metadata found in report result:', result);\r\n      alert('Report generated, but metadata is missing');\r\n      return;\r\n    }\r\n\r\n    // Create a normalized report object for storage\r\n    const normalizedReport = {\r\n      ...result,\r\n      metadata,\r\n    };\r\n\r\n    // Add to recent reports\r\n    setRecentReports(prev => [normalizedReport, ...prev.slice(0, 4)]);\r\n\r\n    // Show success message\r\n    // In a real app, you might want to show a toast notification\r\n    alert(`Report generated successfully! ID: ${metadata.id}`);\r\n  };\r\n\r\n  /**\r\n   * Handle report selection from history\r\n   */\r\n  const handleReportSelect = (report: any) => {\r\n    console.log('Report selected:', report);\r\n    // In a real app, you might want to open a report viewer\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold text-gray-900\">\r\n            Data Report Generation\r\n          </h1>\r\n          <p className=\"text-gray-600 mt-2\">\r\n            Generate comprehensive reports for delegations, tasks, vehicles, and\r\n            employees\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center gap-3\">\r\n          <Badge variant=\"outline\" className=\"flex items-center gap-2\">\r\n            <Download className=\"h-4 w-4\" />\r\n            Report Builder\r\n          </Badge>\r\n          <Button variant=\"outline\" size=\"sm\">\r\n            <Settings className=\"h-4 w-4 mr-2\" />\r\n            Settings\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Quick Stats */}\r\n      {recentReports.length > 0 && (\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"flex items-center gap-2\">\r\n              <Info className=\"h-5 w-5\" />\r\n              Recent Activity\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent>\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n              {recentReports.map((report, index) => {\r\n                // Safely extract metadata\r\n                const metadata = report?.metadata;\r\n                if (!metadata) {\r\n                  return null; // Skip reports without metadata\r\n                }\r\n\r\n                return (\r\n                  <div key={index} className=\"bg-gray-50 p-3 rounded-lg\">\r\n                    <div className=\"flex items-center justify-between mb-2\">\r\n                      <Badge variant=\"outline\" className=\"text-xs\">\r\n                        {metadata.type || 'Unknown'}\r\n                      </Badge>\r\n                      <span className=\"text-xs text-gray-500\">\r\n                        {metadata.generatedAt\r\n                          ? new Date(metadata.generatedAt).toLocaleTimeString()\r\n                          : 'Unknown time'}\r\n                      </span>\r\n                    </div>\r\n                    <p className=\"text-sm font-medium truncate\">\r\n                      {metadata.id || 'No ID'}\r\n                    </p>\r\n                    <p className=\"text-xs text-gray-600\">\r\n                      {metadata.entityTypes?.join(', ') ||\r\n                        metadata.entityType ||\r\n                        'Unknown entity'}\r\n                    </p>\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n\r\n      {/* Information Alert */}\r\n      <Alert>\r\n        <Info className=\"h-4 w-4\" />\r\n        <AlertDescription>\r\n          <strong>Report Generation System:</strong> Generate individual entity\r\n          reports, aggregate analytics, or comprehensive reports across multiple\r\n          data sources. All reports support PDF, Excel, and CSV export formats.\r\n        </AlertDescription>\r\n      </Alert>\r\n\r\n      {/* Main Tabs Interface */}\r\n      <Tabs\r\n        value={activeTab}\r\n        onValueChange={setActiveTab}\r\n        className=\"space-y-6\"\r\n      >\r\n        {/* Tab Navigation */}\r\n        <Card>\r\n          <CardContent className=\"pt-6\">\r\n            <TabsList className=\"grid w-full grid-cols-4\">\r\n              {TABS.map(tab => {\r\n                const Icon = tab.icon;\r\n                return (\r\n                  <TabsTrigger\r\n                    key={tab.id}\r\n                    value={tab.id}\r\n                    className=\"flex items-center gap-2 data-[state=active]:bg-blue-50\"\r\n                  >\r\n                    <Icon className=\"h-4 w-4\" />\r\n                    <span className=\"hidden sm:inline\">{tab.label}</span>\r\n                  </TabsTrigger>\r\n                );\r\n              })}\r\n            </TabsList>\r\n\r\n            {/* Tab Description */}\r\n            <div className=\"mt-4 p-4 bg-gray-50 rounded-lg\">\r\n              {TABS.map(tab => {\r\n                if (tab.id !== activeTab) return null;\r\n                const Icon = tab.icon;\r\n                return (\r\n                  <div key={tab.id} className=\"flex items-center gap-3\">\r\n                    <Icon className=\"h-5 w-5 text-gray-600\" />\r\n                    <div>\r\n                      <h3 className=\"font-medium text-gray-900\">{tab.label}</h3>\r\n                      <p className=\"text-sm text-gray-600\">{tab.description}</p>\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </CardContent>\r\n        </Card>\r\n\r\n        {/* Tab Content */}\r\n        {TABS.map(tab => {\r\n          const Component = tab.component;\r\n          return (\r\n            <TabsContent key={tab.id} value={tab.id} className=\"space-y-6\">\r\n              <Component\r\n                onReportGenerated={handleReportGenerated}\r\n                onReportSelect={handleReportSelect}\r\n              />\r\n            </TabsContent>\r\n          );\r\n        })}\r\n      </Tabs>\r\n\r\n      {/* Help Section */}\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>Report Generation Guide</CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <FileText className=\"h-4 w-4 text-blue-600\" />\r\n                <span className=\"font-medium\">Comprehensive</span>\r\n              </div>\r\n              <p className=\"text-sm text-gray-600\">\r\n                Generate reports that include data from multiple entity types\r\n                with cross-entity analytics.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <User className=\"h-4 w-4 text-green-600\" />\r\n                <span className=\"font-medium\">Individual</span>\r\n              </div>\r\n              <p className=\"text-sm text-gray-600\">\r\n                Create detailed reports for specific delegations, tasks,\r\n                vehicles, or employees.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <BarChart3 className=\"h-4 w-4 text-orange-600\" />\r\n                <span className=\"font-medium\">Aggregate</span>\r\n              </div>\r\n              <p className=\"text-sm text-gray-600\">\r\n                Generate analytics reports with aggregated metrics and trend\r\n                analysis.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <History className=\"h-4 w-4 text-purple-600\" />\r\n                <span className=\"font-medium\">History</span>\r\n              </div>\r\n              <p className=\"text-sm text-gray-600\">\r\n                View, download, and manage all your previously generated\r\n                reports.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAEA;;CAEC,GACD,MAAM,OAAO;IACX;QACE,IAAI;QACJ,OAAO;QACP,MAAM,iNAAA,CAAA,WAAQ;QACd,aAAa;QACb,WAAW,mLAAA,CAAA,sBAAmB;IAChC;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,qMAAA,CAAA,OAAI;QACV,aAAa;QACb,WAAW,yLAAA,CAAA,4BAAyB;IACtC;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,qNAAA,CAAA,YAAS;QACf,aAAa;QACb,WAAW,wLAAA,CAAA,2BAAwB;IACrC;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM,2MAAA,CAAA,UAAO;QACb,aAAa;QACb,WAAW,6KAAA,CAAA,gBAAa;IAC1B;CACD;AAQM,MAAM,uBAAiC;;IAC5C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE5D;;GAEC,GACD,MAAM,wBAAwB,CAAC;QAC7B,QAAQ,GAAG,CAAC,qBAAqB;QAEjC,6DAA6D;QAC7D,MAAM,WAAW,QAAQ,MAAM,YAAY,QAAQ;QAEnD,IAAI,CAAC,UAAU;YACb,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;YACN;QACF;QAEA,gDAAgD;QAChD,MAAM,mBAAmB;YACvB,GAAG,MAAM;YACT;QACF;QAEA,wBAAwB;QACxB,iBAAiB,CAAA,OAAQ;gBAAC;mBAAqB,KAAK,KAAK,CAAC,GAAG;aAAG;QAEhE,uBAAuB;QACvB,6DAA6D;QAC7D,MAAM,CAAC,mCAAmC,EAAE,SAAS,EAAE,EAAE;IAC3D;IAEA;;GAEC,GACD,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,oBAAoB;IAChC,wDAAwD;IAC1D;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAKpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;kDACjC,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGlC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAO1C,cAAc,MAAM,GAAG,mBACtB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIhC,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,QAAQ;gCAC1B,0BAA0B;gCAC1B,MAAM,WAAW,QAAQ;gCACzB,IAAI,CAAC,UAAU;oCACb,OAAO,MAAM,gCAAgC;gCAC/C;gCAEA,qBACE,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAChC,SAAS,IAAI,IAAI;;;;;;8DAEpB,6LAAC;oDAAK,WAAU;8DACb,SAAS,WAAW,GACjB,IAAI,KAAK,SAAS,WAAW,EAAE,kBAAkB,KACjD;;;;;;;;;;;;sDAGR,6LAAC;4CAAE,WAAU;sDACV,SAAS,EAAE,IAAI;;;;;;sDAElB,6LAAC;4CAAE,WAAU;sDACV,SAAS,WAAW,EAAE,KAAK,SAC1B,SAAS,UAAU,IACnB;;;;;;;mCAjBI;;;;;4BAqBd;;;;;;;;;;;;;;;;;0BAOR,6LAAC,oIAAA,CAAA,QAAK;;kCACJ,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,6LAAC,oIAAA,CAAA,mBAAgB;;0CACf,6LAAC;0CAAO;;;;;;4BAAkC;;;;;;;;;;;;;0BAO9C,6LAAC,mIAAA,CAAA,OAAI;gBACH,OAAO;gBACP,eAAe;gBACf,WAAU;;kCAGV,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,mIAAA,CAAA,WAAQ;oCAAC,WAAU;8CACjB,KAAK,GAAG,CAAC,CAAA;wCACR,MAAM,OAAO,IAAI,IAAI;wCACrB,qBACE,6LAAC,mIAAA,CAAA,cAAW;4CAEV,OAAO,IAAI,EAAE;4CACb,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAoB,IAAI,KAAK;;;;;;;2CALxC,IAAI,EAAE;;;;;oCAQjB;;;;;;8CAIF,6LAAC;oCAAI,WAAU;8CACZ,KAAK,GAAG,CAAC,CAAA;wCACR,IAAI,IAAI,EAAE,KAAK,WAAW,OAAO;wCACjC,MAAM,OAAO,IAAI,IAAI;wCACrB,qBACE,6LAAC;4CAAiB,WAAU;;8DAC1B,6LAAC;oDAAK,WAAU;;;;;;8DAChB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA6B,IAAI,KAAK;;;;;;sEACpD,6LAAC;4DAAE,WAAU;sEAAyB,IAAI,WAAW;;;;;;;;;;;;;2CAJ/C,IAAI,EAAE;;;;;oCAQpB;;;;;;;;;;;;;;;;;oBAML,KAAK,GAAG,CAAC,CAAA;wBACR,MAAM,YAAY,IAAI,SAAS;wBAC/B,qBACE,6LAAC,mIAAA,CAAA,cAAW;4BAAc,OAAO,IAAI,EAAE;4BAAE,WAAU;sCACjD,cAAA,6LAAC;gCACC,mBAAmB;gCACnB,gBAAgB;;;;;;2BAHF,IAAI,EAAE;;;;;oBAO5B;;;;;;;0BAIF,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAEhC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAMvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAEhC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAMvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAEhC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAMvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;oDAAK,WAAU;8DAAc;;;;;;;;;;;;sDAEhC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD;GA3Oa;KAAA", "debugId": null}}]}