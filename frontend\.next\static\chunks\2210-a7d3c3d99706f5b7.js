(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1051,2210,3615,3712,7841],{1120:(e,s,t)=>{"use strict";t.d(s,{useExport:()=>p});var a=t(95155),l=t(28890),r=t(12115),i=t(3925);let n=l.vv.create({header:{color:"#333",fontSize:24,marginBottom:10,textAlign:"center"},page:{backgroundColor:"#E4E4E4",flexDirection:"column",padding:30},section:{flexGrow:1,margin:10,padding:10},subheader:{color:"#555",fontSize:16,marginBottom:5},text:{fontSize:12,marginBottom:3}}),c=e=>{var s,t,i,c,o,d,m,x,u;let{data:h,filters:p,reportDate:g,reportTitle:j}=e,{metadata:v,reportData:y,summary:f,totalCount:N}=r.useMemo(()=>{var e,s,t,a,l;if(!h)return{metadata:{},reportData:{},summary:{message:"No data available"},totalCount:0};let r=null!=(s=null!=(e=null==h?void 0:h.data)?e:h)?s:{},i=null!=(t=null==h?void 0:h.metadata)?t:{},n="object"!=typeof r||null===r||Array.isArray(r)?{}:r;return{metadata:i,reportData:n,summary:null!=(a=n.summary)?a:{message:"No summary available"},totalCount:null!=(l=n.totalCount)?l:Array.isArray(n)?n.length:0}},[h]),b=null!=p?p:{},w=null!=(d=null==b?void 0:b.dateRange)?d:{},k=null==b?void 0:b.status,A=null!=g?g:new Date().toLocaleString();return(0,a.jsx)(l.yo,{children:(0,a.jsx)(l.YW,{size:"A4",style:n.page,children:(0,a.jsxs)(l.Ss,{style:n.section,children:[(0,a.jsx)(l.EY,{style:n.header,children:null!=j?j:"Delegation Report"}),(0,a.jsxs)(l.EY,{style:n.text,children:["Report Generated: ",A]}),(0,a.jsxs)(l.EY,{style:n.text,children:["Total Delegations: ",N]}),f&&Object.keys(f).length>0&&(0,a.jsxs)(l.Ss,{style:{marginTop:15},children:[(0,a.jsx)(l.EY,{style:n.subheader,children:"Summary:"}),Object.entries(f).map(e=>{let[s,t]=e;return(0,a.jsxs)(l.EY,{style:n.text,children:[s.replaceAll(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()),": ",String(null!=t?t:"N/A")]},s)})]}),b&&Object.keys(b).length>0&&(0,a.jsxs)(l.Ss,{style:{marginTop:20},children:[(0,a.jsx)(l.EY,{style:n.subheader,children:"Filters Applied:"}),(0,a.jsxs)(l.EY,{style:n.text,children:["Date Range:"," ",null!=(m=null==(t=w.from)||null==(s=t.toLocaleDateString)?void 0:s.call(t))?m:"N/A"," -"," ",null!=(x=null==(c=w.to)||null==(i=c.toLocaleDateString)?void 0:i.call(c))?x:"N/A",(0,a.jsxs)(l.EY,{style:n.text,children:["Status: ",null!=(u=null==k||null==(o=k.join)?void 0:o.call(k,", "))?u:"All"]})]})]}),v.id&&(0,a.jsxs)(l.Ss,{style:{marginTop:15},children:[(0,a.jsx)(l.EY,{style:n.subheader,children:"Report Details:"}),(0,a.jsxs)(l.EY,{style:n.text,children:["Report ID: ",v.id]}),(0,a.jsxs)(l.EY,{style:n.text,children:["Generated At:"," ",v.generatedAt?new Date(v.generatedAt).toLocaleString():"N/A"]})]})]})})})},o=l.vv.create({page:{flexDirection:"column",backgroundColor:"#FFFFFF",padding:30,fontSize:12},header:{fontSize:20,marginBottom:20,textAlign:"center",color:"#7c3aed",fontWeight:"bold"},subheader:{fontSize:16,marginBottom:15,color:"#374151",fontWeight:"bold",borderBottom:"1px solid #e5e7eb",paddingBottom:5},section:{marginBottom:20},row:{flexDirection:"row",marginBottom:8,paddingVertical:4},label:{width:"40%",fontWeight:"bold",color:"#4b5563"},value:{width:"60%",color:"#111827"},table:{marginTop:10},tableHeader:{flexDirection:"row",backgroundColor:"#faf5ff",padding:8,fontWeight:"bold"},tableRow:{flexDirection:"row",padding:8,borderBottom:"1px solid #e5e7eb"},tableCell:{flex:1,fontSize:10},metadata:{marginTop:30,padding:15,backgroundColor:"#faf5ff",borderRadius:5},metadataText:{fontSize:10,color:"#6b7280",marginBottom:3}}),d=e=>{var s,t,i,n,c,d;let{data:m,reportTitle:x,metadata:u}=e,h=r.useMemo(()=>{if(!m)return{};let e=(null==m?void 0:m.data)||m;return e&&"object"==typeof e?{totalCount:e.totalCount||0,activeCount:e.activeCount||0,onLeaveCount:e.onLeaveCount||0,averagePerformanceScore:e.averagePerformanceScore||0,satisfactionRate:e.satisfactionRate||0,performanceMetrics:e.performanceMetrics||{},departmentDistribution:Array.isArray(e.departmentDistribution)?e.departmentDistribution:[],taskAssignments:e.taskAssignments||{},workloadDistribution:Array.isArray(e.workloadDistribution)?e.workloadDistribution:[],availabilityMetrics:e.availabilityMetrics||{},...e}:{}},[m]);return(0,a.jsx)(l.yo,{children:(0,a.jsxs)(l.YW,{size:"A4",style:o.page,children:[(0,a.jsx)(l.EY,{style:o.header,children:x||"Employee Report"}),(0,a.jsxs)(l.Ss,{style:o.section,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Employee Summary"}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Total Employees:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.totalCount||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Active Employees:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.activeCount||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"On Leave:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.onLeaveCount||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Average Performance Score:"}),(0,a.jsx)(l.EY,{style:o.value,children:(null==(s=h.averagePerformanceScore)?void 0:s.toFixed(2))||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Employee Satisfaction Rate:"}),(0,a.jsxs)(l.EY,{style:o.value,children:[(null==(t=h.satisfactionRate)?void 0:t.toFixed(2))||0,"%"]})]})]}),h.performanceMetrics&&(0,a.jsxs)(l.Ss,{style:o.section,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Performance Metrics"}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"High Performers:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.performanceMetrics.highPerformers||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Average Performers:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.performanceMetrics.averagePerformers||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Low Performers:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.performanceMetrics.lowPerformers||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Performance Improvement Rate:"}),(0,a.jsxs)(l.EY,{style:o.value,children:[(null==(i=h.performanceMetrics.improvementRate)?void 0:i.toFixed(2))||0,"%"]})]})]}),h.departmentDistribution&&(0,a.jsxs)(l.Ss,{style:o.section,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Department Distribution"}),(0,a.jsxs)(l.Ss,{style:o.table,children:[(0,a.jsxs)(l.Ss,{style:o.tableHeader,children:[(0,a.jsx)(l.EY,{style:o.tableCell,children:"Department"}),(0,a.jsx)(l.EY,{style:o.tableCell,children:"Count"}),(0,a.jsx)(l.EY,{style:o.tableCell,children:"Percentage"})]}),h.departmentDistribution.map((e,s)=>{var t;return(0,a.jsxs)(l.Ss,{style:o.tableRow,children:[(0,a.jsx)(l.EY,{style:o.tableCell,children:(null==e?void 0:e.department)||(null==e?void 0:e._id)||"Unknown"}),(0,a.jsx)(l.EY,{style:o.tableCell,children:(null==e?void 0:e.count)||(null==e||null==(t=e._count)?void 0:t.department)||0}),(0,a.jsx)(l.EY,{style:o.tableCell,children:(null==e?void 0:e.percentage)?"".concat(Number(e.percentage).toFixed(1),"%"):"N/A"})]},"dept-".concat(s))})]})]}),h.taskAssignments&&(0,a.jsxs)(l.Ss,{style:o.section,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Task Assignment Metrics"}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Total Tasks Assigned:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.taskAssignments.totalAssigned||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Completed Tasks:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.taskAssignments.completed||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Pending Tasks:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.taskAssignments.pending||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Task Completion Rate:"}),(0,a.jsxs)(l.EY,{style:o.value,children:[(null==(n=h.taskAssignments.completionRate)?void 0:n.toFixed(2))||0,"%"]})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Average Tasks per Employee:"}),(0,a.jsx)(l.EY,{style:o.value,children:(null==(c=h.taskAssignments.averagePerEmployee)?void 0:c.toFixed(1))||0})]})]}),h.workloadDistribution&&(0,a.jsxs)(l.Ss,{style:o.section,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Workload Distribution"}),(0,a.jsxs)(l.Ss,{style:o.table,children:[(0,a.jsxs)(l.Ss,{style:o.tableHeader,children:[(0,a.jsx)(l.EY,{style:o.tableCell,children:"Workload Level"}),(0,a.jsx)(l.EY,{style:o.tableCell,children:"Employee Count"}),(0,a.jsx)(l.EY,{style:o.tableCell,children:"Percentage"})]}),h.workloadDistribution.map((e,s)=>{var t;return(0,a.jsxs)(l.Ss,{style:o.tableRow,children:[(0,a.jsx)(l.EY,{style:o.tableCell,children:(null==e?void 0:e.level)||(null==e?void 0:e._id)||"Unknown"}),(0,a.jsx)(l.EY,{style:o.tableCell,children:(null==e?void 0:e.count)||(null==e||null==(t=e._count)?void 0:t.level)||0}),(0,a.jsx)(l.EY,{style:o.tableCell,children:(null==e?void 0:e.percentage)?"".concat(Number(e.percentage).toFixed(1),"%"):"N/A"})]},"workload-".concat(s))})]})]}),h.availabilityMetrics&&(0,a.jsxs)(l.Ss,{style:o.section,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Availability Metrics"}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Available Employees:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.availabilityMetrics.available||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"On Assignment:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.availabilityMetrics.onAssignment||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"On Leave:"}),(0,a.jsx)(l.EY,{style:o.value,children:h.availabilityMetrics.onLeave||0})]}),(0,a.jsxs)(l.Ss,{style:o.row,children:[(0,a.jsx)(l.EY,{style:o.label,children:"Availability Rate:"}),(0,a.jsxs)(l.EY,{style:o.value,children:[(null==(d=h.availabilityMetrics.availabilityRate)?void 0:d.toFixed(2))||0,"%"]})]})]}),u&&(0,a.jsxs)(l.Ss,{style:o.metadata,children:[(0,a.jsx)(l.EY,{style:o.subheader,children:"Report Information"}),(0,a.jsxs)(l.EY,{style:o.metadataText,children:["Report ID: ",u.id]}),(0,a.jsxs)(l.EY,{style:o.metadataText,children:["Type: ",u.type]}),(0,a.jsxs)(l.EY,{style:o.metadataText,children:["Entity Type: ",u.entityType]}),(0,a.jsxs)(l.EY,{style:o.metadataText,children:["Generated: ",new Date(u.generatedAt).toLocaleString()]}),(0,a.jsxs)(l.EY,{style:o.metadataText,children:["Generated By: ",u.generatedBy]})]})]})})},m=l.vv.create({page:{flexDirection:"column",backgroundColor:"#FFFFFF",padding:30,fontSize:12},header:{fontSize:20,marginBottom:20,textAlign:"center",color:"#059669",fontWeight:"bold"},subheader:{fontSize:16,marginBottom:15,color:"#374151",fontWeight:"bold",borderBottom:"1px solid #e5e7eb",paddingBottom:5},section:{marginBottom:20},row:{flexDirection:"row",marginBottom:8,paddingVertical:4},label:{width:"40%",fontWeight:"bold",color:"#4b5563"},value:{width:"60%",color:"#111827"},table:{marginTop:10},tableHeader:{flexDirection:"row",backgroundColor:"#ecfdf5",padding:8,fontWeight:"bold"},tableRow:{flexDirection:"row",padding:8,borderBottom:"1px solid #e5e7eb"},tableCell:{flex:1,fontSize:10},metadata:{marginTop:30,padding:15,backgroundColor:"#f0fdf4",borderRadius:5},metadataText:{fontSize:10,color:"#6b7280",marginBottom:3}}),x=e=>{var s,t,i;let{data:n,reportTitle:c,metadata:o}=e,d=r.useMemo(()=>{if(!n)return{};let e=(null==n?void 0:n.data)||n;return e&&"object"==typeof e?{totalCount:e.totalCount||0,completedTasks:e.completedTasks||0,pendingTasks:e.pendingTasks||0,overdueCount:e.overdueCount||0,completionRate:e.completionRate||0,averageCompletionTime:e.averageCompletionTime||0,statusDistribution:Array.isArray(e.statusDistribution)?e.statusDistribution:[],priorityDistribution:Array.isArray(e.priorityDistribution)?e.priorityDistribution:[],assignmentMetrics:e.assignmentMetrics||{},...e}:{}},[n]),x=o||{};return(0,a.jsx)(l.yo,{children:(0,a.jsxs)(l.YW,{size:"A4",style:m.page,children:[(0,a.jsx)(l.EY,{style:m.header,children:c||"Task Report"}),(0,a.jsxs)(l.Ss,{style:m.section,children:[(0,a.jsx)(l.EY,{style:m.subheader,children:"Task Summary"}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Total Tasks:"}),(0,a.jsx)(l.EY,{style:m.value,children:d.totalCount||0})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Completed Tasks:"}),(0,a.jsx)(l.EY,{style:m.value,children:d.completedTasks||0})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Pending Tasks:"}),(0,a.jsx)(l.EY,{style:m.value,children:d.pendingTasks||0})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Overdue Tasks:"}),(0,a.jsx)(l.EY,{style:m.value,children:d.overdueCount||0})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Completion Rate:"}),(0,a.jsxs)(l.EY,{style:m.value,children:[(null==(s=d.completionRate)?void 0:s.toFixed(2))||0,"%"]})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Average Completion Time:"}),(0,a.jsxs)(l.EY,{style:m.value,children:[(null==(t=d.averageCompletionTime)?void 0:t.toFixed(2))||0," hours"]})]})]}),d.statusDistribution&&(0,a.jsxs)(l.Ss,{style:m.section,children:[(0,a.jsx)(l.EY,{style:m.subheader,children:"Status Distribution"}),(0,a.jsxs)(l.Ss,{style:m.table,children:[(0,a.jsxs)(l.Ss,{style:m.tableHeader,children:[(0,a.jsx)(l.EY,{style:m.tableCell,children:"Status"}),(0,a.jsx)(l.EY,{style:m.tableCell,children:"Count"}),(0,a.jsx)(l.EY,{style:m.tableCell,children:"Percentage"})]}),d.statusDistribution.map((e,s)=>{var t;return(0,a.jsxs)(l.Ss,{style:m.tableRow,children:[(0,a.jsx)(l.EY,{style:m.tableCell,children:(null==e?void 0:e.status)||(null==e?void 0:e._id)||"Unknown"}),(0,a.jsx)(l.EY,{style:m.tableCell,children:(null==e?void 0:e.count)||(null==e||null==(t=e._count)?void 0:t.status)||0}),(0,a.jsx)(l.EY,{style:m.tableCell,children:(null==e?void 0:e.percentage)?"".concat(Number(e.percentage).toFixed(1),"%"):"N/A"})]},"status-".concat(s))})]})]}),d.priorityDistribution&&(0,a.jsxs)(l.Ss,{style:m.section,children:[(0,a.jsx)(l.EY,{style:m.subheader,children:"Priority Distribution"}),(0,a.jsxs)(l.Ss,{style:m.table,children:[(0,a.jsxs)(l.Ss,{style:m.tableHeader,children:[(0,a.jsx)(l.EY,{style:m.tableCell,children:"Priority"}),(0,a.jsx)(l.EY,{style:m.tableCell,children:"Count"}),(0,a.jsx)(l.EY,{style:m.tableCell,children:"Percentage"})]}),d.priorityDistribution.map((e,s)=>{var t;return(0,a.jsxs)(l.Ss,{style:m.tableRow,children:[(0,a.jsx)(l.EY,{style:m.tableCell,children:(null==e?void 0:e.priority)||(null==e?void 0:e._id)||"Unknown"}),(0,a.jsx)(l.EY,{style:m.tableCell,children:(null==e?void 0:e.count)||(null==e||null==(t=e._count)?void 0:t.priority)||0}),(0,a.jsx)(l.EY,{style:m.tableCell,children:(null==e?void 0:e.percentage)?"".concat(Number(e.percentage).toFixed(1),"%"):"N/A"})]},"priority-".concat(s))})]})]}),d.assignmentMetrics&&(0,a.jsxs)(l.Ss,{style:m.section,children:[(0,a.jsx)(l.EY,{style:m.subheader,children:"Assignment Metrics"}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Assigned Tasks:"}),(0,a.jsx)(l.EY,{style:m.value,children:d.assignmentMetrics.assignedCount||0})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Unassigned Tasks:"}),(0,a.jsx)(l.EY,{style:m.value,children:d.assignmentMetrics.unassignedCount||0})]}),(0,a.jsxs)(l.Ss,{style:m.row,children:[(0,a.jsx)(l.EY,{style:m.label,children:"Assignment Rate:"}),(0,a.jsxs)(l.EY,{style:m.value,children:[(null==(i=d.assignmentMetrics.assignmentRate)?void 0:i.toFixed(2))||0,"%"]})]})]}),x&&Object.keys(x).length>0&&(0,a.jsxs)(l.Ss,{style:m.metadata,children:[(0,a.jsx)(l.EY,{style:m.subheader,children:"Report Information"}),(0,a.jsxs)(l.EY,{style:m.metadataText,children:["Report ID: ",x.id||"N/A"]}),(0,a.jsxs)(l.EY,{style:m.metadataText,children:["Type: ",x.type||"N/A"]}),(0,a.jsxs)(l.EY,{style:m.metadataText,children:["Entity Type: ",x.entityType||"N/A"]}),(0,a.jsxs)(l.EY,{style:m.metadataText,children:["Generated:"," ",x.generatedAt?new Date(x.generatedAt).toLocaleString():"N/A"]}),(0,a.jsxs)(l.EY,{style:m.metadataText,children:["Generated By: ",x.generatedBy||"N/A"]})]})]})})},u=l.vv.create({page:{flexDirection:"column",backgroundColor:"#FFFFFF",padding:30,fontSize:12},header:{fontSize:20,marginBottom:20,textAlign:"center",color:"#dc2626",fontWeight:"bold"},subheader:{fontSize:16,marginBottom:15,color:"#374151",fontWeight:"bold",borderBottom:"1px solid #e5e7eb",paddingBottom:5},section:{marginBottom:20},row:{flexDirection:"row",marginBottom:8,paddingVertical:4},label:{width:"40%",fontWeight:"bold",color:"#4b5563"},value:{width:"60%",color:"#111827"},table:{marginTop:10},tableHeader:{flexDirection:"row",backgroundColor:"#fef2f2",padding:8,fontWeight:"bold"},tableRow:{flexDirection:"row",padding:8,borderBottom:"1px solid #e5e7eb"},tableCell:{flex:1,fontSize:10},metadata:{marginTop:30,padding:15,backgroundColor:"#fef2f2",borderRadius:5},metadataText:{fontSize:10,color:"#6b7280",marginBottom:3}}),h=e=>{var s,t,i,n,c,o,d;let{data:m,reportTitle:x,metadata:h}=e,p=r.useMemo(()=>{if(!m)return{};let e=(null==m?void 0:m.data)||m;return e&&"object"==typeof e?{totalCount:e.totalCount||0,activeCount:e.activeCount||0,maintenanceCount:e.maintenanceCount||0,outOfServiceCount:e.outOfServiceCount||0,utilizationRate:e.utilizationRate||0,averageMileage:e.averageMileage||0,statusDistribution:Array.isArray(e.statusDistribution)?e.statusDistribution:[],typeDistribution:Array.isArray(e.typeDistribution)?e.typeDistribution:[],maintenanceMetrics:e.maintenanceMetrics||{},...e}:{}},[m]);return(0,a.jsx)(l.yo,{children:(0,a.jsxs)(l.YW,{size:"A4",style:u.page,children:[(0,a.jsx)(l.EY,{style:u.header,children:x||"Vehicle Report"}),(0,a.jsxs)(l.Ss,{style:u.section,children:[(0,a.jsx)(l.EY,{style:u.subheader,children:"Fleet Summary"}),(0,a.jsxs)(l.Ss,{style:u.row,children:[(0,a.jsx)(l.EY,{style:u.label,children:"Total Vehicles:"}),(0,a.jsx)(l.EY,{style:u.value,children:p.totalCount||0})]}),(0,a.jsxs)(l.Ss,{style:u.row,children:[(0,a.jsx)(l.EY,{style:u.label,children:"Active Vehicles:"}),(0,a.jsx)(l.EY,{style:u.value,children:p.activeCount||0})]}),(0,a.jsxs)(l.Ss,{style:u.row,children:[(0,a.jsx)(l.EY,{style:u.label,children:"In Maintenance:"}),(0,a.jsx)(l.EY,{style:u.value,children:p.maintenanceCount||0})]}),(0,a.jsxs)(l.Ss,{style:u.row,children:[(0,a.jsx)(l.EY,{style:u.label,children:"Out of Service:"}),(0,a.jsx)(l.EY,{style:u.value,children:p.outOfServiceCount||0})]}),(0,a.jsxs)(l.Ss,{style:u.row,children:[(0,a.jsx)(l.EY,{style:u.label,children:"Fleet Utilization Rate:"}),(0,a.jsxs)(l.EY,{style:u.value,children:[(null==(s=p.utilizationRate)?void 0:s.toFixed(2))||0,"%"]})]}),(0,a.jsxs)(l.Ss,{style:u.row,children:[(0,a.jsx)(l.EY,{style:u.label,children:"Average Mileage:"}),(0,a.jsxs)(l.EY,{style:u.value,children:[(null==(t=p.averageMileage)?void 0:t.toFixed(0))||0," km"]})]})]}),p.statusDistribution&&(0,a.jsxs)(l.Ss,{style:u.section,children:[(0,a.jsx)(l.EY,{style:u.subheader,children:"Vehicle Status Distribution"}),(0,a.jsxs)(l.Ss,{style:u.table,children:[(0,a.jsxs)(l.Ss,{style:u.tableHeader,children:[(0,a.jsx)(l.EY,{style:u.tableCell,children:"Status"}),(0,a.jsx)(l.EY,{style:u.tableCell,children:"Count"}),(0,a.jsx)(l.EY,{style:u.tableCell,children:"Percentage"})]}),p.statusDistribution.map((e,s)=>{var t;return(0,a.jsxs)(l.Ss,{style:u.tableRow,children:[(0,a.jsx)(l.EY,{style:u.tableCell,children:(null==e?void 0:e.status)||(null==e?void 0:e._id)||"Unknown"}),(0,a.jsx)(l.EY,{style:u.tableCell,children:(null==e?void 0:e.count)||(null==e||null==(t=e._count)?void 0:t.status)||0}),(0,a.jsx)(l.EY,{style:u.tableCell,children:(null==e?void 0:e.percentage)?"".concat(Number(e.percentage).toFixed(1),"%"):"N/A"})]},"status-".concat(s))})]})]}),p.typeDistribution&&(0,a.jsxs)(l.Ss,{style:u.section,children:[(0,a.jsx)(l.EY,{style:u.subheader,children:"Vehicle Type Distribution"}),(0,a.jsxs)(l.Ss,{style:u.table,children:[(0,a.jsxs)(l.Ss,{style:u.tableHeader,children:[(0,a.jsx)(l.EY,{style:u.tableCell,children:"Type"}),(0,a.jsx)(l.EY,{style:u.tableCell,children:"Count"}),(0,a.jsx)(l.EY,{style:u.tableCell,children:"Percentage"})]}),p.typeDistribution.map((e,s)=>{var t;return(0,a.jsxs)(l.Ss,{style:u.tableRow,children:[(0,a.jsx)(l.EY,{style:u.tableCell,children:(null==e?void 0:e.type)||(null==e?void 0:e._id)||"Unknown"}),(0,a.jsx)(l.EY,{style:u.tableCell,children:(null==e?void 0:e.count)||(null==e||null==(t=e._count)?void 0:t.type)||0}),(0,a.jsx)(l.EY,{style:u.tableCell,children:(null==e?void 0:e.percentage)?"".concat(Number(e.percentage).toFixed(1),"%"):"N/A"})]},"type-".concat(s))})]})]}),p.maintenanceMetrics&&(0,a.jsxs)(l.Ss,{style:u.section,children:[(0,a.jsx)(l.EY,{style:u.subheader,children:"Maintenance Metrics"}),(0,a.jsxs)(l.Ss,{style:u.row,children:[(0,a.jsx)(l.EY,{style:u.label,children:"Total Maintenance Records:"}),(0,a.jsx)(l.EY,{style:u.value,children:p.maintenanceMetrics.totalRecords||0})]}),(0,a.jsxs)(l.Ss,{style:u.row,children:[(0,a.jsx)(l.EY,{style:u.label,children:"Average Cost per Service:"}),(0,a.jsxs)(l.EY,{style:u.value,children:["$",(null==(i=p.maintenanceMetrics.averageCost)?void 0:i.toFixed(2))||0]})]}),(0,a.jsxs)(l.Ss,{style:u.row,children:[(0,a.jsx)(l.EY,{style:u.label,children:"Total Maintenance Cost:"}),(0,a.jsxs)(l.EY,{style:u.value,children:["$",(null==(n=p.maintenanceMetrics.totalCost)?void 0:n.toFixed(2))||0]})]}),(0,a.jsxs)(l.Ss,{style:u.row,children:[(0,a.jsx)(l.EY,{style:u.label,children:"Preventive Maintenance Rate:"}),(0,a.jsxs)(l.EY,{style:u.value,children:[(null==(c=p.maintenanceMetrics.preventiveRate)?void 0:c.toFixed(2))||0,"%"]})]})]}),p.utilizationMetrics&&(0,a.jsxs)(l.Ss,{style:u.section,children:[(0,a.jsx)(l.EY,{style:u.subheader,children:"Utilization Metrics"}),(0,a.jsxs)(l.Ss,{style:u.row,children:[(0,a.jsx)(l.EY,{style:u.label,children:"Average Daily Usage:"}),(0,a.jsxs)(l.EY,{style:u.value,children:[(null==(o=p.utilizationMetrics.averageDailyUsage)?void 0:o.toFixed(2))||0," ","hours"]})]}),(0,a.jsxs)(l.Ss,{style:u.row,children:[(0,a.jsx)(l.EY,{style:u.label,children:"Peak Usage Hours:"}),(0,a.jsx)(l.EY,{style:u.value,children:p.utilizationMetrics.peakUsageHours||"N/A"})]}),(0,a.jsxs)(l.Ss,{style:u.row,children:[(0,a.jsx)(l.EY,{style:u.label,children:"Idle Time Percentage:"}),(0,a.jsxs)(l.EY,{style:u.value,children:[(null==(d=p.utilizationMetrics.idleTimePercentage)?void 0:d.toFixed(2))||0,"%"]})]})]}),h&&(0,a.jsxs)(l.Ss,{style:u.metadata,children:[(0,a.jsx)(l.EY,{style:u.subheader,children:"Report Information"}),(0,a.jsxs)(l.EY,{style:u.metadataText,children:["Report ID: ",h.id]}),(0,a.jsxs)(l.EY,{style:u.metadataText,children:["Type: ",h.type]}),(0,a.jsxs)(l.EY,{style:u.metadataText,children:["Entity Type: ",h.entityType]}),(0,a.jsxs)(l.EY,{style:u.metadataText,children:["Generated: ",new Date(h.generatedAt).toLocaleString()]}),(0,a.jsxs)(l.EY,{style:u.metadataText,children:["Generated By: ",h.generatedBy]})]})]})})},p=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"report",[s,t]=(0,r.useState)(!1),[n,o]=(0,r.useState)(null),m=(0,r.useCallback)(async(s,r,i)=>{t(!0),o(null);try{let t=await (0,l.x8)((0,a.jsx)(c,{data:s,filters:r,reportDate:new Date().toLocaleString(),reportTitle:i.filename||e})).toBlob(),n=URL.createObjectURL(t),o=document.createElement("a");o.href=n,o.setAttribute("download","".concat(i.filename||e,".pdf")),document.body.append(o),o.click(),o.remove(),URL.revokeObjectURL(n)}catch(e){console.error("PDF export failed:",e),o(e.message||"Failed to export to PDF.")}finally{t(!1)}},[e]),u=(0,r.useCallback)((s,a)=>{t(!0),o(null);try{let t=i.Wp.json_to_sheet(s),l=i.Wp.book_new();i.Wp.book_append_sheet(l,t,"Report Data"),i._h(l,"".concat(a.filename||e,".xlsx"))}catch(e){console.error("Excel export failed:",e),o(e.message||"Failed to export to Excel.")}finally{t(!1)}},[e]),p=(0,r.useCallback)((s,a)=>{t(!0),o(null);try{let t=Object.keys(s[0]||{}).join(","),l=s.map(e=>Object.values(e).map(e=>'"'.concat(String(e).replaceAll('"','""'),'"')).join(",")),r=[t,...l].join("\n"),i=new Blob([r],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a");if(void 0!==n.download){let s=URL.createObjectURL(i);n.setAttribute("href",s),n.setAttribute("download","".concat(a.filename||e,".csv")),n.style.visibility="hidden",document.body.append(n),n.click(),n.remove()}}catch(e){console.error("CSV export failed:",e),o(e.message||"Failed to export to CSV.")}finally{t(!1)}},[e]),g=(0,r.useCallback)(async(e,s,r)=>{t(!0),o(null);try{let t=await (0,l.x8)((0,a.jsx)(c,{data:e,filters:s,includeCharts:!0,includeServiceHistory:!!s.includeServiceHistory,reportDate:new Date().toLocaleString(),reportTitle:r||"Dashboard Report"})).toBlob(),i=URL.createObjectURL(t),n=document.createElement("a");n.href=i,n.setAttribute("download","".concat(r||"dashboard-report",".pdf")),document.body.append(n),n.click(),n.remove(),URL.revokeObjectURL(i)}catch(e){console.error("Dashboard PDF export failed:",e),o(e.message||"Failed to export dashboard to PDF.")}finally{t(!1)}},[]),j=(0,r.useCallback)(async(e,s,r)=>{t(!0),o(null);try{let t=await (0,l.x8)((0,a.jsx)(()=>(0,a.jsx)(l.yo,{children:(0,a.jsxs)(l.YW,{orientation:"landscape",size:"A4",style:{padding:30},children:[(0,a.jsxs)(l.Ss,{style:{marginBottom:20},children:[(0,a.jsx)(l.EY,{style:{fontSize:20,fontWeight:"bold"},children:s}),(0,a.jsxs)(l.EY,{style:{fontSize:10,marginTop:5},children:["Generated on: ",new Date().toLocaleDateString()]})]}),(0,a.jsx)(l.Ss,{style:{alignItems:"center",flex:1,justifyContent:"center"},children:(0,a.jsxs)(l.EY,{style:{fontSize:14},children:["Chart data: ",JSON.stringify(e,null,2)]})})]})}),{})).toBlob(),i=URL.createObjectURL(t),n=document.createElement("a");n.href=i,n.setAttribute("download",r||"".concat(s.toLowerCase().replaceAll(/\s+/g,"-"),".pdf")),document.body.append(n),n.click(),n.remove(),URL.revokeObjectURL(i)}catch(e){console.error("Chart PDF export failed:",e),o(e.message||"Failed to export chart to PDF.")}finally{t(!1)}},[]),v=(0,r.useCallback)(async(e,s,r,i)=>{t(!0),o(null);let n=e=>{if(null==e)return null;if(Array.isArray(e))return e.map(n);if("object"==typeof e&&e.constructor===Object){let s={};for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&"__proto__"!==t&&"constructor"!==t){let a=n(e[t]);void 0!==a&&(s[t]=a)}return s}return"function"==typeof e||"symbol"==typeof e?null:e};try{let t,o;if(!e)throw Error("No report data provided for PDF export");let u=(null==e?void 0:e.data)||e||{},p=n(u),g={data:{totalCount:(null==p?void 0:p.totalCount)||0,summary:(null==p?void 0:p.summary)||{message:"No data available"},records:(null==p?void 0:p.records)||[],statusDistribution:(null==p?void 0:p.statusDistribution)||[],priorityDistribution:(null==p?void 0:p.priorityDistribution)||[],...p},metadata:(null==e?void 0:e.metadata)||{entityType:s,format:"pdf",generatedAt:new Date().toISOString(),generatedBy:"system",id:"".concat(s,"_").concat(Date.now()),type:"aggregate"}};if(0===Object.keys(g.data).length)throw Error("After sanitization, no valid data is available for PDF export.");let j=r||"".concat(s," Report"),v=g.metadata||{};switch(s){case"delegations":{let e={dateRange:{from:new Date,to:new Date},employees:[],locations:[],status:[],vehicles:[]};t=(0,a.jsx)(c,{data:g,filters:e,reportDate:new Date().toLocaleString(),reportTitle:j});break}case"employees":t=(0,a.jsx)(d,{data:g,metadata:v,reportTitle:j});break;case"tasks":t=(0,a.jsx)(x,{data:g,metadata:v,reportTitle:j});break;case"vehicles":t=(0,a.jsx)(h,{data:g,metadata:v,reportTitle:j});break;default:throw Error("Unsupported entity type: ".concat(s,". Supported types are: delegations, tasks, vehicles, employees"))}console.log("Generating PDF with SANITIZED data:",g),console.log("Entity Type:",s),console.log("Raw data structure:",e),console.log("Normalized data structure:",g),console.log("PDF Component:",t),console.log("Entity type:",s),console.log("Report title:",j);try{console.log("Starting PDF blob generation..."),o=await (0,l.x8)(t).toBlob(),console.log("PDF blob generated successfully:",{size:o.size,type:o.type})}catch(e){var m;throw console.error("PDF generation failed:",e),console.error("PDF error stack:",e.stack),Error("PDF generation failed: ".concat(null!=(m=e.message)?m:"Unknown PDF error"))}if(!o||0===o.size)throw Error("Generated PDF blob is empty or invalid");console.log("Creating download link...");let y=URL.createObjectURL(o),f=document.createElement("a");f.href=y;let N="".concat(i||"".concat(s,"-report"),".pdf");f.setAttribute("download",N),console.log("Triggering download for:",N),document.body.append(f),f.click(),f.remove(),URL.revokeObjectURL(y),console.log("Download triggered successfully")}catch(e){console.error("Report PDF export failed:",e),o(e.message||"Failed to export report to PDF.")}finally{t(!1)}},[]);return{exportChartToPDF:j,exportDashboardToPDF:g,exportError:n,exportReportToExcel:(0,r.useCallback)((e,s,a)=>{t(!0),o(null);try{let t=i.Wp.book_new(),l=e.data||e;if(l.summary||l.totalCount){let e=[];if(l.totalCount&&e.push({Metric:"Total Count",Value:l.totalCount}),l.summary)for(let[s,t]of Object.entries(l.summary))e.push({Metric:s.replaceAll(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()),Value:t});let s=i.Wp.json_to_sheet(e);i.Wp.book_append_sheet(t,s,"Summary")}if(l.statusDistribution){let e=i.Wp.json_to_sheet(l.statusDistribution);i.Wp.book_append_sheet(t,e,"Status Distribution")}if(l.priorityDistribution){let e=i.Wp.json_to_sheet(l.priorityDistribution);i.Wp.book_append_sheet(t,e,"Priority Distribution")}if(l.locationMetrics){let e=i.Wp.json_to_sheet(l.locationMetrics);i.Wp.book_append_sheet(t,e,"Location Metrics")}if(l.maintenanceMetrics){let e=i.Wp.json_to_sheet([l.maintenanceMetrics]);i.Wp.book_append_sheet(t,e,"Maintenance Metrics")}if(l.performanceMetrics){let e=i.Wp.json_to_sheet([l.performanceMetrics]);i.Wp.book_append_sheet(t,e,"Performance Metrics")}i._h(t,"".concat(a||"".concat(s,"-report"),".xlsx"))}catch(e){console.error("Report Excel export failed:",e),o(e.message||"Failed to export report to Excel.")}finally{t(!1)}},[]),exportReportToPDF:v,exportToCSV:p,exportToExcel:u,exportToPDF:m,isExporting:s}}},6560:(e,s,t)=>{"use strict";t.d(s,{r:()=>c});var a=t(95155),l=t(50172),r=t(12115),i=t(30285),n=t(54036);let c=r.forwardRef((e,s)=>{let{actionType:t="primary",asChild:r=!1,children:c,className:o,disabled:d,icon:m,isLoading:x=!1,loadingText:u,...h}=e,{className:p,variant:g}={danger:{className:"shadow-md",variant:"destructive"},primary:{className:"shadow-md",variant:"default"},secondary:{className:"",variant:"secondary"},tertiary:{className:"",variant:"outline"}}[t];return(0,a.jsx)(i.$,{asChild:r,className:(0,n.cn)(p,o),disabled:x||d,ref:s,variant:g,...h,children:x?(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",(0,a.jsx)(l.A,{className:"mr-2 size-4 animate-spin"}),u||c]}):(0,a.jsxs)("span",{className:"inline-flex items-center",children:[" ",m&&(0,a.jsx)("span",{className:"mr-2",children:m}),c]})})});c.displayName="ActionButton"},14636:(e,s,t)=>{"use strict";t.d(s,{AM:()=>n,Wv:()=>c,hl:()=>o});var a=t(95155),l=t(20547),r=t(12115),i=t(54036);let n=l.bL,c=l.l9;l.bm;let o=r.forwardRef((e,s)=>{let{align:t="center",className:r,sideOffset:n=4,...c}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsx)(l.UC,{align:t,className:(0,i.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",r),ref:s,sideOffset:n,...c})})});o.displayName=l.UC.displayName},17759:(e,s,t)=>{"use strict";t.d(s,{C5:()=>v,MJ:()=>g,Rr:()=>j,eI:()=>h,lR:()=>p,lV:()=>o,zB:()=>m});var a=t(95155),l=t(12115),r=t(99708),i=t(62177),n=t(54036),c=t(85057);let o=i.Op,d=l.createContext({}),m=e=>{let{...s}=e;return(0,a.jsx)(d.Provider,{value:{name:s.name},children:(0,a.jsx)(i.xI,{...s})})},x=()=>{let e=l.useContext(d),s=l.useContext(u),{getFieldState:t,formState:a}=(0,i.xW)(),r=t(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=s;return{id:n,name:e.name,formItemId:"".concat(n,"-form-item"),formDescriptionId:"".concat(n,"-form-item-description"),formMessageId:"".concat(n,"-form-item-message"),...r}},u=l.createContext({}),h=l.forwardRef((e,s)=>{let{className:t,...r}=e,i=l.useId();return(0,a.jsx)(u.Provider,{value:{id:i},children:(0,a.jsx)("div",{ref:s,className:(0,n.cn)("space-y-2",t),...r})})});h.displayName="FormItem";let p=l.forwardRef((e,s)=>{let{className:t,...l}=e,{error:r,formItemId:i}=x();return(0,a.jsx)(c.J,{ref:s,className:(0,n.cn)(r&&"text-destructive",t),htmlFor:i,...l})});p.displayName="FormLabel";let g=l.forwardRef((e,s)=>{let{...t}=e,{error:l,formItemId:i,formDescriptionId:n,formMessageId:c}=x();return(0,a.jsx)(r.DX,{ref:s,id:i,"aria-describedby":l?"".concat(n," ").concat(c):"".concat(n),"aria-invalid":!!l,...t})});g.displayName="FormControl";let j=l.forwardRef((e,s)=>{let{className:t,...l}=e,{formDescriptionId:r}=x();return(0,a.jsx)("p",{ref:s,id:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...l})});j.displayName="FormDescription";let v=l.forwardRef((e,s)=>{var t;let{className:l,children:r,...i}=e,{error:c,formMessageId:o}=x(),d=c?String(null!=(t=null==c?void 0:c.message)?t:""):r;return d?(0,a.jsx)("p",{ref:s,id:o,className:(0,n.cn)("text-sm font-medium text-destructive",l),...i,children:d}):null});v.displayName="FormMessage"},17841:(e,s,t)=>{"use strict";t.d(s,{er:()=>N,kA:()=>y,gd:()=>f,BD:()=>v,nB:()=>k,jM:()=>b,lG:()=>w});var a=t(71610),l=t(26715),r=t(5041),i=t(12115),n=t(54120),c=t(90111),o=t(75908);class d{static enrich(e,s,t){var a,l,r;let{employeeMap:i,vehicleMap:n}=this.createLookupMaps(s,t);return{...e,drivers:null!=(a=this.enrichDrivers(e.drivers,i))?a:[],escorts:null!=(l=this.enrichEscorts(e.escorts,i))?l:[],vehicles:null!=(r=this.enrichVehicles(e.vehicles,n))?r:[]}}static createLookupMaps(e,s){return{employeeMap:new Map(e.map(e=>[e.id,e])),vehicleMap:new Map(s.map(e=>[e.id,e]))}}static enrichDrivers(e,s){return null==e?void 0:e.map(e=>{let t=e.employee||s.get(Number(e.employeeId));return{...e,...t&&{employee:t}}})}static enrichEscorts(e,s){return null==e?void 0:e.map(e=>{let t=e.employee||s.get(Number(e.employeeId));return{...e,...t&&{employee:t}}})}static enrichVehicles(e,s){return null==e?void 0:e.map(e=>{let t=e.vehicle||s.get(e.vehicleId);return{...e,...t&&{vehicle:t}}})}}let m=(e,s,t)=>d.enrich(e,s,t);var x=t(31203);let u={all:["delegations"],detail:e=>["delegations",e]},h=e=>({enabled:!!e,queryFn:()=>o.delegationApiService.getById(e),queryKey:u.detail(e),staleTime:3e5}),p=()=>({queryFn:()=>o.employeeApiService.getAll(),queryKey:["employees"],staleTime:6e5}),g=()=>({queryFn:()=>o.vehicleApiService.getAll(),queryKey:["vehicles"],staleTime:6e5}),j=e=>[h(e),p(),g()],v=e=>(0,c.GK)([...u.all],async()=>(await o.delegationApiService.getAll()).data,"delegation",{staleTime:0,...e}),y=e=>(0,c.GK)([...u.detail(e)],async()=>await o.delegationApiService.getById(e),"delegation",{enabled:!!e,staleTime:3e5}),f=e=>{let[s,t,l]=(0,a.E)({queries:j(e)}),r=(0,i.useMemo)(()=>{if((null==s?void 0:s.data)&&(null==t?void 0:t.data)&&(null==l?void 0:l.data))try{let e=s.data;return m(e,t.data,l.data)}catch(e){throw console.error("Error enriching delegation data:",e),e}},[null==s?void 0:s.data,null==t?void 0:t.data,null==l?void 0:l.data]),n=(0,i.useCallback)(()=>{null==s||s.refetch(),null==t||t.refetch(),null==l||l.refetch()},[null==s?void 0:s.refetch,null==t?void 0:t.refetch,null==l?void 0:l.refetch]);return{data:r,error:(null==s?void 0:s.error)||(null==t?void 0:t.error)||(null==l?void 0:l.error),isError:(null==s?void 0:s.isError)||(null==t?void 0:t.isError)||(null==l?void 0:l.isError),isLoading:(null==s?void 0:s.isLoading)||(null==t?void 0:t.isLoading)||(null==l?void 0:l.isLoading),isPending:(null==s?void 0:s.isPending)||(null==t?void 0:t.isPending)||(null==l?void 0:l.isPending),refetch:n}},N=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>{let s=x.G.toCreateRequest(e);return await o.delegationApiService.create(s)},onError:(s,t,a)=>{(null==a?void 0:a.previousDelegations)&&e.setQueryData(u.all,a.previousDelegations),console.error("Failed to create delegation:",s),e.invalidateQueries({queryKey:u.all})},onMutate:async s=>{await e.cancelQueries({queryKey:u.all});let t=e.getQueryData(u.all);return e.setQueryData(u.all,function(){var e,t,a,l,r,i,n,c;let o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],d="optimistic-".concat(Date.now()),m=new Date().toISOString(),x=s.flightArrivalDetails?{id:"optimistic-flight-arr-".concat(Date.now()),...s.flightArrivalDetails}:null,u=s.flightDepartureDetails?{id:"optimistic-flight-dep-".concat(Date.now()+1),...s.flightDepartureDetails}:null;return[...o,{arrivalFlight:null!=x?x:null,createdAt:m,delegates:(null==(e=s.delegates)?void 0:e.map((e,s)=>{var t;return{id:"optimistic-delegate-".concat(d,"-").concat(s),name:e.name,notes:null!=(t=e.notes)?t:null,title:e.title}}))||[],departureFlight:null!=u?u:null,drivers:(null==(t=s.drivers)?void 0:t.map(e=>{var s;return{createdAt:m,createdBy:null,delegationId:d,employeeId:e.employeeId,id:"optimistic-driver-".concat(d,"-").concat(e.employeeId),notes:null!=(s=e.notes)?s:null,updatedAt:m}}))||[],durationFrom:s.durationFrom,durationTo:s.durationTo,escorts:(null==(a=s.escorts)?void 0:a.map(e=>{var s;return{createdAt:m,createdBy:null,delegationId:d,employeeId:e.employeeId,id:"optimistic-escort-".concat(d,"-").concat(e.employeeId),notes:null!=(s=e.notes)?s:null,updatedAt:m}}))||[],eventName:s.eventName,id:d,imageUrl:null!=(r=s.imageUrl)?r:null,invitationFrom:null!=(i=s.invitationFrom)?i:null,invitationTo:null!=(n=s.invitationTo)?n:null,location:s.location,notes:null!=(c=s.notes)?c:null,status:s.status||"Planned",statusHistory:[],updatedAt:m,vehicles:(null==(l=s.vehicles)?void 0:l.map(e=>{var s,t;return{assignedDate:e.assignedDate,createdAt:m,createdBy:null,delegationId:d,id:"optimistic-vehicle-".concat(d,"-").concat(e.vehicleId),notes:null!=(s=e.notes)?s:null,returnDate:null!=(t=e.returnDate)?t:null,updatedAt:m,vehicleId:e.vehicleId}}))||[]}]}),{previousDelegations:t}},onSettled:()=>{e.invalidateQueries({queryKey:u.all})}})},b=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>{let{data:s,id:t}=e;return await o.delegationApiService.update(t,s)},onError:(s,t,a)=>{(null==a?void 0:a.previousDelegation)&&e.setQueryData(u.detail(t.id),a.previousDelegation),(null==a?void 0:a.previousDelegationsList)&&e.setQueryData(u.all,a.previousDelegationsList),console.error("Failed to update delegation:",s),e.invalidateQueries({queryKey:u.detail(t.id)}),e.invalidateQueries({queryKey:u.all})},onMutate:async s=>{let{data:t,id:a}=s;await e.cancelQueries({queryKey:u.all}),await e.cancelQueries({queryKey:u.detail(a)});let l=e.getQueryData(u.detail(a)),r=e.getQueryData(u.all);return e.setQueryData(u.detail(a),e=>{var s,a,l,r,i,c,o,d,m,x,u,h,p,g,j,v,y,f,N,b,w,k,A,C,S,E,D,T,R;if(!e)return;let z=new Date().toISOString();return{...e,arrivalFlight:(0,n.d$)(null===t.flightArrivalDetails?null:void 0===t.flightArrivalDetails?e.arrivalFlight:{airport:t.flightArrivalDetails.airport||(null==(s=e.arrivalFlight)?void 0:s.airport)||"",dateTime:t.flightArrivalDetails.dateTime||(null==(a=e.arrivalFlight)?void 0:a.dateTime)||"",flightNumber:t.flightArrivalDetails.flightNumber||(null==(l=e.arrivalFlight)?void 0:l.flightNumber)||"",id:(null==(r=e.arrivalFlight)?void 0:r.id)||"optimistic-arr-".concat(Date.now()),notes:null!=(g=null!=(p=t.flightArrivalDetails.notes)?p:null==(i=e.arrivalFlight)?void 0:i.notes)?g:null,terminal:null!=(v=null!=(j=t.flightArrivalDetails.terminal)?j:null==(c=e.arrivalFlight)?void 0:c.terminal)?v:null}),departureFlight:(0,n.d$)(null===t.flightDepartureDetails?null:void 0===t.flightDepartureDetails?e.departureFlight:{airport:t.flightDepartureDetails.airport||(null==(o=e.departureFlight)?void 0:o.airport)||"",dateTime:t.flightDepartureDetails.dateTime||(null==(d=e.departureFlight)?void 0:d.dateTime)||"",flightNumber:t.flightDepartureDetails.flightNumber||(null==(m=e.departureFlight)?void 0:m.flightNumber)||"",id:(null==(x=e.departureFlight)?void 0:x.id)||"optimistic-dep-".concat(Date.now()),notes:null!=(f=null!=(y=t.flightDepartureDetails.notes)?y:null==(u=e.departureFlight)?void 0:u.notes)?f:null,terminal:null!=(b=null!=(N=t.flightDepartureDetails.terminal)?N:null==(h=e.departureFlight)?void 0:h.terminal)?b:null}),durationFrom:null!=(w=t.durationFrom)?w:e.durationFrom,durationTo:null!=(k=t.durationTo)?k:e.durationTo,eventName:null!=(A=t.eventName)?A:e.eventName,imageUrl:(0,n.d$)(null!=(C=t.imageUrl)?C:e.imageUrl),invitationFrom:(0,n.d$)(null!=(S=t.invitationFrom)?S:e.invitationFrom),invitationTo:(0,n.d$)(null!=(E=t.invitationTo)?E:e.invitationTo),location:null!=(D=t.location)?D:e.location,notes:(0,n.d$)(null!=(T=t.notes)?T:e.notes),status:null!=(R=t.status)?R:e.status,updatedAt:z}}),e.setQueryData(u.all,function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return s.map(s=>s.id===a&&e.getQueryData(u.detail(a))||s)}),{previousDelegation:l,previousDelegationsList:r}},onSettled:(s,t,a)=>{e.invalidateQueries({queryKey:u.detail(a.id)}),e.invalidateQueries({queryKey:u.all})}})},w=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>{let{id:s,status:t,statusChangeReason:a}=e;return await o.delegationApiService.updateStatus(s,t,a)},onError:(s,t,a)=>{(null==a?void 0:a.previousDelegation)&&e.setQueryData(u.detail(t.id),a.previousDelegation),console.error("Failed to update delegation status:",s)},onMutate:async s=>{let{id:t,status:a}=s;await e.cancelQueries({queryKey:u.detail(t)});let l=e.getQueryData(u.detail(t));return e.setQueryData(u.detail(t),e=>e?{...e,status:a}:void 0),{previousDelegation:l}},onSettled:(s,t,a)=>{e.invalidateQueries({queryKey:u.detail(a.id)}),e.invalidateQueries({queryKey:u.all})}})},k=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>(await o.delegationApiService.delete(e),e),onError:(s,t,a)=>{(null==a?void 0:a.previousDelegationsList)&&e.setQueryData(u.all,a.previousDelegationsList),console.error("Failed to delete delegation:",s)},onMutate:async s=>{await e.cancelQueries({queryKey:u.all}),await e.cancelQueries({queryKey:u.detail(s)});let t=e.getQueryData(u.all);return e.setQueryData(u.all,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.filter(e=>e.id!==s)}),e.removeQueries({queryKey:u.detail(s)}),{previousDelegationsList:t}},onSettled:()=>{e.invalidateQueries({queryKey:u.all})}})}},21354:(e,s,t)=>{"use strict";t.d(s,{R:()=>l});class a{static enrich(e,s,t){let{employeeMap:a,vehicleMap:l}=this.createLookupMaps(s,t),r=this.enrichStaffEmployee(e,a);return r=this.enrichDriverEmployee(r,a),r=this.enrichVehicle(r,l)}static createLookupMaps(e,s){let t=Array.isArray(e)?e:[],a=Array.isArray(s)?s:[];return{employeeMap:new Map(t.map(e=>[e.id,e])),vehicleMap:new Map(a.map(e=>[e.id,e]))}}static enrichDriverEmployee(e,s){var t,a;if(!e.driverEmployeeId)return e;let l=null!=(a=null!=(t=e.driverEmployee)?t:s.get(e.driverEmployeeId))?a:null;return{...e,driverEmployee:l}}static enrichStaffEmployee(e,s){var t,a;if(!e.staffEmployeeId)return e;let l=null!=(a=null!=(t=e.staffEmployee)?t:s.get(e.staffEmployeeId))?a:null;return{...e,staffEmployee:l}}static enrichVehicle(e,s){var t,a;if(!e.vehicleId)return e;let l=null!=(a=null!=(t=e.vehicle)?t:s.get(e.vehicleId))?a:null;return{...e,vehicle:l}}}let l=(e,s,t)=>a.enrich(e,s,t)},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>n});var a=t(95155),l=t(74466);t(12115);var r=t(54036);let i=(0,l.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function n(e){let{className:s,variant:t,...l}=e;return(0,a.jsx)("div",{className:(0,r.cn)(i({variant:t}),s),...l})}},32383:()=>{},32606:(e,s,t)=>{"use strict";t.d(s,{ReportingDashboard:()=>am});var a=t(95155),l=t(5263),r=t(80659),i=t(93223),n=t(28328),c=t(50286),o=t(16736),d=t(15300),m=t(18271),x=t(71978),u=t(9572),h=t(67554),p=t(68718),g=t(12115),j=t(52639),v=t(26126),y=t(30285),f=t(17313),N=t(66695),b=t(91721),w=t(3638),k=t(50594),A=t(47262),C=t(59409),S=t(85057),E=t(88539),D=t(62523),T=t(22346),R=t(35079),z=t(86950),M=t(51920),F=t(6548),P=t(52747);class I{async generateAggregateReport(e){try{let s=await this.apiClient.request({url:"/api/reporting/reports/aggregate/".concat(e.entityType),method:"POST",data:{filters:e.filters,template:e.template||"default",format:"json",options:e.options||{}}}),t=s.data.data||s.data;return{data:(null==t?void 0:t.aggregate)||t,metadata:s.data.metadata||(null==t?void 0:t.metadata)||{id:"aggregate_".concat(e.entityType,"_").concat(Date.now()),type:"aggregate",entityType:e.entityType,format:e.format||"json",template:e.template||"default",generatedAt:new Date().toISOString(),generatedBy:"system",filters:e.filters,options:e.options}}}catch(s){throw console.error("Failed to generate aggregate report:",s),Error("Failed to generate ".concat(e.entityType," aggregate report"))}}async generateIndividualReport(e){try{let s=await this.apiClient.request({url:"/api/reporting/reports/individual/".concat(e.entityType,"/").concat(e.entityId),method:"POST",data:{template:e.template||"default",format:"json",options:e.options||{}}});return{data:s.data.data||s.data,metadata:s.data.metadata||{id:"individual_".concat(e.entityType,"_").concat(e.entityId,"_").concat(Date.now()),type:"individual",entityType:e.entityType,entityId:e.entityId,format:e.format||"json",template:e.template||"default",generatedAt:new Date().toISOString(),generatedBy:"system",options:e.options}}}catch(s){throw console.error("Failed to generate individual report:",s),Error("Failed to generate ".concat(e.entityType," individual report"))}}async generateComprehensiveReport(e){try{let s=await this.apiClient.request({url:"/api/reporting/reports/generate",method:"POST",data:{entityTypes:e.entityTypes,filters:e.filters,template:e.template||"comprehensive",format:"json",options:e.options||{}}});return{data:s.data.data||s.data,metadata:s.data.metadata||{id:"comprehensive_".concat(Date.now()),type:"comprehensive",entityTypes:e.entityTypes,format:e.format||"json",template:e.template||"comprehensive",generatedAt:new Date().toISOString(),generatedBy:"system",filters:e.filters,options:e.options}}}catch(e){throw console.error("Failed to generate comprehensive report:",e),Error("Failed to generate comprehensive report")}}constructor(e){this.apiClient=e}}class L{async request(e){throw Error("API client not initialized. Use createReportGenerationService with proper API client.")}}let O=e=>new I(e);new I(new L);let V=()=>{let[e,s]=(0,g.useState)(!1),[a,l]=(0,g.useState)(null),{client:r}=(0,P.a8)(),i=O({request:async e=>({data:await r.request(e)})}),n=(0,g.useCallback)(async e=>{s(!0),l(null);try{return await i.generateComprehensiveReport(e)}catch(e){throw l(e instanceof Error?e.message:"Failed to generate report"),e}finally{s(!1)}},[i]),c=(0,g.useCallback)(async e=>{s(!0),l(null);try{return await i.generateIndividualReport(e)}catch(e){throw l(e instanceof Error?e.message:"Failed to generate individual report"),e}finally{s(!1)}},[i]);return{generateComprehensiveReport:n,generateIndividualReport:c,generateAggregateReport:(0,g.useCallback)(async e=>{s(!0),l(null);try{return await i.generateAggregateReport(e)}catch(e){throw l(e instanceof Error?e.message:"Failed to generate aggregate report"),e}finally{s(!1)}},[i]),exportReport:(0,g.useCallback)(async(e,s,a,l,r)=>{try{let{useExport:i}=await Promise.resolve().then(t.bind(t,1120)),{exportReportToPDF:n,exportReportToExcel:c,exportToCSV:o}=i(r||"report");switch(s){case"pdf":await n(e,a,l||"".concat(a.charAt(0).toUpperCase()+a.slice(1)," Report"),r);break;case"excel":c(e,a,r);break;case"csv":let d=Array.isArray(e.data)?e.data:[e.data||e];o(d,{filename:r||"report"});break;default:throw Error("Unsupported export format: ".concat(s))}}catch(e){throw console.error("Export failed:",e),e}},[]),isGenerating:e,error:a}},B=e=>{var s,t;let{client:a}=(0,P.a8)(),l=new URLSearchParams;(null==e?void 0:e.type)&&l.append("type",e.type),(null==e?void 0:e.entityType)&&l.append("entityType",e.entityType);let r=(0,F.Sk)(["report-history",e],async()=>await a.get("/api/reporting/reports/history?".concat(l.toString())),{cacheDuration:12e4,enableRetry:!0});return{reports:(null==(s=r.data)?void 0:s.reports)||[],pagination:null==(t=r.data)?void 0:t.pagination,isLoading:r.isLoading,error:r.error,refetch:r.refetch}},Y=()=>{let[e,s]=(0,g.useState)(!1),[t,a]=(0,g.useState)(null),{client:l}=(0,P.a8)();return{downloadReport:(0,g.useCallback)(async e=>{s(!0),a(null);try{let s=await l.get("/api/reporting/reports/".concat(e,"/download"));console.log("Download result:",s),alert("Download functionality will be implemented with file storage")}catch(e){throw a(e instanceof Error?e.message:"Failed to download report"),e}finally{s(!1)}},[l]),isDownloading:e,downloadError:t}},W=()=>{let{client:e}=(0,P.a8)(),s=(0,F.Sk)(["report-templates"],async()=>{let s=await e.get("/api/reporting/reports/templates");return Array.isArray(s)?s:s&&Array.isArray(s.data)?s.data:(console.warn("Report templates API returned unexpected format:",s),[])},{cacheDuration:6e5,enableRetry:!0});return{templates:Array.isArray(s.data)?s.data:[],isLoading:s.isLoading,error:s.error,refetch:s.refetch}};var U=t(85511),Z=t(14636),$=t(55365),_=t(25318),G=t(31949),J=t(8376),q=t(78816),K=t(76198),Q=t(19164),H=t(74641),X=t(37140),ee=t(1407),es=t(94376),et=t(44861),ea=t(53941),el=t(63482),er=t(41784),ei=t(24386),en=t(54036);let ec=[{label:"Today",getValue:()=>({from:new Date,to:new Date})},{label:"Yesterday",getValue:()=>{let e=(0,q.e)(new Date,1);return{from:e,to:e}}},{label:"Last 3 days",getValue:()=>({from:(0,q.e)(new Date,2),to:new Date})},{label:"Last 7 days",getValue:()=>({from:(0,q.e)(new Date,6),to:new Date})},{label:"Last 2 weeks",getValue:()=>({from:(0,K.k)(new Date,2),to:new Date})},{label:"Last 30 days",getValue:()=>({from:(0,q.e)(new Date,29),to:new Date})},{label:"This week",getValue:()=>({from:(0,q.e)(new Date,new Date().getDay()),to:new Date})},{label:"This month",getValue:()=>({from:(0,Q.w)(new Date),to:(0,H.p)(new Date)})},{label:"Last month",getValue:()=>{let e=(0,X.a)(new Date,1);return{from:(0,Q.w)(e),to:(0,H.p)(e)}}},{label:"Last 3 months",getValue:()=>({from:(0,X.a)(new Date,3),to:new Date})},{label:"This year",getValue:()=>({from:(0,ee.D)(new Date),to:(0,es.Q)(new Date)})}],eo=e=>{let{value:s,onChange:t,placeholder:l="Select date range",className:r,disabled:i=!1,maxDays:n=365,minDays:c=1,maxDate:o=new Date,minDate:d=new Date(2020,0,1),showValidation:m=!0}=e,[x,u]=(0,g.useState)(!1),[h,p]=(0,g.useState)(s||null),j=(0,g.useMemo)(()=>{if(!h)return{isValid:!0};let{from:e,to:s}=h;if(!(0,et.f)(e)||!(0,et.f)(s))return{isValid:!1,message:"Invalid date selected",type:"error"};if((0,ea.d)(e,s))return{isValid:!1,message:"Start date must be before end date",type:"error"};if(d&&(0,el.Y)(e,d))return{isValid:!1,message:"Start date cannot be before ".concat((0,er.GP)(d,"MMM dd, yyyy")),type:"error"};if(o&&(0,ea.d)(s,o))return{isValid:!1,message:"End date cannot be after ".concat((0,er.GP)(o,"MMM dd, yyyy")),type:"error"};let t=(0,ei.c)(s,e)+1;return t<c?{isValid:!1,message:"Date range must be at least ".concat(c," day").concat(c>1?"s":""),type:"error"}:t>n?{isValid:!1,message:"Date range cannot exceed ".concat(n," days"),type:"error"}:t>90?{isValid:!0,message:"Large date range (".concat(t," days) may affect performance"),type:"warning"}:{isValid:!0,message:"".concat(t," day").concat(t>1?"s":""," selected"),type:"info"}},[h,n,c,o,d]);(0,g.useEffect)(()=>{p(s||null)},[s]);let f=e=>{let s=e.getValue();p(s),N(s).isValid&&(null==t||t(s),u(!1))},N=e=>{if(!e)return{isValid:!0};let{from:s,to:t}=e;return(0,ei.c)(t,s)+1>n?{isValid:!1,message:"Date range cannot exceed ".concat(n," days"),type:"error"}:{isValid:!0}};return(0,a.jsx)("div",{className:(0,en.cn)("relative",r),children:(0,a.jsxs)(Z.AM,{open:x,onOpenChange:u,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-start text-left font-normal",!s&&"text-muted-foreground"),disabled:i,children:[(0,a.jsx)(M.A,{className:"mr-2 h-4 w-4"}),s?s.from.toDateString()===s.to.toDateString()?(0,er.GP)(s.from,"MMM dd, yyyy"):"".concat((0,er.GP)(s.from,"MMM dd, yyyy")," - ").concat((0,er.GP)(s.to,"MMM dd, yyyy")):l,s&&(0,a.jsxs)(v.E,{variant:"secondary",className:"ml-auto",children:[Math.ceil((s.to.getTime()-s.from.getTime())/864e5)+1," ","days"]})]})}),(0,a.jsx)(Z.hl,{className:"w-auto max-w-4xl p-0",align:"start",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsxs)("div",{className:"border-r p-4 space-y-2 min-w-[160px]",children:[(0,a.jsx)("h4",{className:"font-medium text-sm text-gray-900 mb-3",children:"Quick Select"}),ec.map(e=>(0,a.jsx)(y.$,{variant:"ghost",size:"sm",className:"w-full justify-start text-xs h-8 px-2",onClick:()=>f(e),children:e.label},e.label)),s&&(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"border-t pt-2 mt-3",children:(0,a.jsxs)(y.$,{variant:"ghost",size:"sm",className:"w-full justify-start text-sm text-red-600 hover:text-red-700",onClick:()=>{p(null),null==t||t(null),u(!1)},children:[(0,a.jsx)(_.A,{className:"mr-2 h-3 w-3"}),"Clear"]})})})]}),(0,a.jsxs)("div",{className:"p-4 min-w-[600px]",children:[(0,a.jsx)(U.V,{mode:"range",selected:h||void 0,onSelect:e=>{if(e&&e.from){let s={from:e.from,to:e.to||e.from};p(s),N(s).isValid&&s.from&&s.to&&(null==t||t(s),u(!1))}else p(null)},numberOfMonths:2,className:"rounded-md border-0",disabled:[...d?[{before:d}]:[],...o?[{after:o}]:[]],showOutsideDays:!0,fixedWeeks:!0}),h&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)("h5",{className:"font-medium text-sm text-gray-900 mb-2",children:"Selected Range"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,a.jsxs)("div",{children:["From: ",(0,er.GP)(h.from,"MMM dd, yyyy")]}),(0,a.jsxs)("div",{children:["To: ",(0,er.GP)(h.to,"MMM dd, yyyy")]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[Math.ceil((h.to.getTime()-h.from.getTime())/864e5)+1," ","days"]})]}),m&&j.message&&(0,a.jsx)($.Fc,{className:(0,en.cn)("mt-3 py-2 px-3","error"===j.type&&"border-red-200 bg-red-50","warning"===j.type&&"border-yellow-200 bg-yellow-50","info"===j.type&&"border-blue-200 bg-blue-50"),children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:["error"===j.type&&(0,a.jsx)(G.A,{className:"h-3 w-3 text-red-600"}),"warning"===j.type&&(0,a.jsx)(G.A,{className:"h-3 w-3 text-yellow-600"}),"info"===j.type&&(0,a.jsx)(J.A,{className:"h-3 w-3 text-blue-600"}),(0,a.jsx)($.TN,{className:(0,en.cn)("text-xs","error"===j.type&&"text-red-700","warning"===j.type&&"text-yellow-700","info"===j.type&&"text-blue-700"),children:j.message})]})})]})]})]})})]})})},ed=e=>{let{className:s,size:t="md"}=e;return(0,a.jsx)("div",{className:(0,en.cn)("animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",{sm:"h-4 w-4",md:"h-6 w-6",lg:"h-8 w-8"}[t],s)})},em=[{id:"delegations",name:"Delegations",icon:R.A,description:"Delegation assignments and status tracking",color:"bg-blue-100 text-blue-800"},{id:"tasks",name:"Tasks",icon:d.A,description:"Task completion and performance metrics",color:"bg-green-100 text-green-800"},{id:"vehicles",name:"Vehicles",icon:n.A,description:"Vehicle utilization and maintenance data",color:"bg-orange-100 text-orange-800"},{id:"employees",name:"Employees",icon:c.A,description:"Employee performance and workload analysis",color:"bg-purple-100 text-purple-800"}],ex=[{id:"pdf",name:"PDF",description:"Formatted document for printing"},{id:"excel",name:"Excel",description:"Spreadsheet with multiple sheets"},{id:"csv",name:"CSV",description:"Comma-separated values for data analysis"}];var eu=t(75074);let eh=[{id:"delegations",name:"Delegation",icon:R.A,placeholder:"Enter delegation ID",description:"Generate detailed report for a specific delegation"},{id:"tasks",name:"Task",icon:d.A,placeholder:"Enter task ID",description:"Generate detailed report for a specific task"},{id:"vehicles",name:"Vehicle",icon:n.A,placeholder:"Enter vehicle ID or license plate",description:"Generate detailed report for a specific vehicle"},{id:"employees",name:"Employee",icon:c.A,placeholder:"Enter employee ID or email",description:"Generate detailed report for a specific employee"}],ep=[{id:"pdf",name:"PDF",description:"Formatted document for printing"},{id:"excel",name:"Excel",description:"Spreadsheet format"},{id:"csv",name:"CSV",description:"Data export format"}];var eg=t(83940),ej=t(1120);let ev=[{color:"bg-blue-100 text-blue-800",description:"Aggregate delegation analytics and trends",icon:R.A,id:"delegations",metrics:["Total Count","Completion Rate","Average Duration","Status Distribution"],name:"Delegations"},{color:"bg-green-100 text-green-800",description:"Task performance and completion analytics",icon:d.A,id:"tasks",metrics:["Total Tasks","Completion Rate","Average Time","Priority Distribution"],name:"Tasks"},{color:"bg-orange-100 text-orange-800",description:"Vehicle utilization and maintenance analytics",icon:n.A,id:"vehicles",metrics:["Fleet Size","Utilization Rate","Maintenance Costs","Performance Metrics"],name:"Vehicles"},{color:"bg-purple-100 text-purple-800",description:"Employee performance and workload analytics",icon:c.A,id:"employees",metrics:["Total Employees","Performance Scores","Workload Distribution","Availability"],name:"Employees"}],ey=[{description:"Formatted analytics report",id:"pdf",name:"PDF"},{description:"Spreadsheet with charts",id:"excel",name:"Excel"},{description:"Raw data export",id:"csv",name:"CSV"}],ef={delegations:[{id:"status",name:"Status",options:["Active","Completed","Pending","Cancelled"],type:"select"},{id:"priority",name:"Priority",options:["High","Medium","Low"],type:"select"},{id:"location",name:"Location",type:"text"}],employees:[{id:"department",name:"Department",type:"text"},{id:"position",name:"Position",type:"text"},{id:"status",name:"Status",options:["Active","Inactive","On Leave"],type:"select"}],tasks:[{id:"status",name:"Status",options:["Pending","In Progress","Completed","Cancelled"],type:"select"},{id:"priority",name:"Priority",options:["High","Medium","Low"],type:"select"},{id:"assignee",name:"Assignee",type:"text"}],vehicles:[{id:"status",name:"Status",options:["Active","Maintenance","Inactive"],type:"select"},{id:"type",name:"Vehicle Type",type:"text"},{id:"location",name:"Location",type:"text"}]};var eN=t(35714),eb=t(17607);let ew=[{label:"All Types",value:""},{label:"Comprehensive",value:"comprehensive"},{label:"Individual",value:"individual"},{label:"Aggregate",value:"aggregate"}],ek=[{label:"All Entities",value:""},{label:"Delegations",value:"delegations"},{label:"Tasks",value:"tasks"},{label:"Vehicles",value:"vehicles"},{label:"Employees",value:"employees"}],eA={completed:{color:"bg-green-100 text-green-800",label:"Completed"},failed:{color:"bg-red-100 text-red-800",label:"Failed"},processing:{color:"bg-yellow-100 text-yellow-800",label:"Processing"}},eC={csv:{color:"bg-blue-100 text-blue-800",label:"CSV"},excel:{color:"bg-green-100 text-green-800",label:"Excel"},pdf:{color:"bg-red-100 text-red-800",label:"PDF"}},eS=[{id:"comprehensive",label:"Comprehensive Reports",icon:d.A,description:"Generate reports across multiple entity types",component:()=>{var e,s;let[t,l]=(0,g.useState)(["delegations"]),[r,i]=(0,g.useState)("comprehensive"),[n,c]=(0,g.useState)("pdf"),[o,x]=(0,g.useState)(null),[h,j]=(0,g.useState)({}),[f,b]=(0,g.useState)(""),[w,k]=(0,g.useState)(""),{generateComprehensiveReport:R,isGenerating:F,error:P}=V(),{templates:I,isLoading:L}=W(),O=e=>{l(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},B=async()=>{if(0===t.length)return;let e={entityTypes:t,template:r,format:n,filters:{...h,...o&&{dateRange:{from:o.from.toISOString(),to:o.to.toISOString()}}},options:{name:f||"Report ".concat(new Date().toLocaleDateString()),description:w,includeCharts:!0,includeSummary:!0}};try{await R(e)}catch(e){console.error("Failed to generate report:",e)}},Y=Array.isArray(I)?I.find(e=>e.id===r):null;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Data Report Generator"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Generate comprehensive reports for delegations, tasks, vehicles, and employees"})]}),(0,a.jsxs)(v.E,{variant:"outline",className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),"Report Builder"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"h-5 w-5"}),"Select Data Sources"]})}),(0,a.jsx)(N.Wu,{className:"space-y-4",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:em.map(e=>{let s=e.icon,l=t.includes(e.id);return(0,a.jsx)("div",{className:"\n                        border rounded-lg p-4 cursor-pointer transition-all\n                        ".concat(l?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300","\n                      "),onClick:()=>O(e.id),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(A.S,{checked:l,onCheckedChange:()=>O(e.id)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(s,{className:"h-5 w-5 text-gray-600"}),(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsx)(v.E,{className:e.color,variant:"secondary",children:e.id})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]})},e.id)})})})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Report Template"]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[L?(0,a.jsx)(ed,{}):(0,a.jsxs)(C.l6,{value:r,onValueChange:i,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select a template"})}),(0,a.jsx)(C.gC,{children:Array.isArray(I)&&I.map(e=>(0,a.jsx)(C.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))})]}),Y&&(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:Y.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:Y.description}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:null==(e=Y.sections)?void 0:e.map(e=>(0,a.jsx)(v.E,{variant:"outline",className:"text-xs",children:e},e))})]})]})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5"}),"Filters & Options"]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(S.J,{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(M.A,{className:"h-4 w-4"}),"Date Range"]}),(0,a.jsx)(eo,{value:o,onChange:x,placeholder:"Select date range for data"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"mb-2 block",children:"Export Format"}),(0,a.jsxs)(C.l6,{value:n,onValueChange:c,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{})}),(0,a.jsx)(C.gC,{children:ex.map(e=>(0,a.jsx)(C.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{htmlFor:"reportName",className:"mb-2 block",children:"Report Name"}),(0,a.jsx)(D.p,{id:"reportName",value:f,onChange:e=>b(e.target.value),placeholder:"Enter custom report name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{htmlFor:"reportDescription",className:"mb-2 block",children:"Description"}),(0,a.jsx)(E.T,{id:"reportDescription",value:w,onChange:e=>k(e.target.value),placeholder:"Optional description for the report",rows:3})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(N.ZB,{children:"Report Summary"})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"text-sm font-medium text-gray-600",children:"Selected Entities"}),(0,a.jsx)("div",{className:"mt-1 flex flex-wrap gap-1",children:t.map(e=>{let s=em.find(s=>s.id===e);return s?(0,a.jsx)(v.E,{className:s.color,variant:"secondary",children:s.name},e):null})})]}),(0,a.jsx)(T.w,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"text-sm font-medium text-gray-600",children:"Template"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:(null==Y?void 0:Y.name)||"None selected"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"text-sm font-medium text-gray-600",children:"Format"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:null==(s=ex.find(e=>e.id===n))?void 0:s.name})]}),o&&(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"text-sm font-medium text-gray-600",children:"Date Range"}),(0,a.jsxs)("p",{className:"mt-1 text-sm",children:[o.from.toLocaleDateString()," -"," ",o.to.toLocaleDateString()]})]})]})]}),(0,a.jsx)(N.Zp,{children:(0,a.jsxs)(N.Wu,{className:"pt-6",children:[P&&(0,a.jsx)($.Fc,{className:"mb-4",variant:"destructive",children:(0,a.jsx)($.TN,{children:P})}),(0,a.jsx)(y.$,{onClick:B,disabled:0===t.length||F,className:"w-full",size:"lg",children:F?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ed,{className:"mr-2 h-4 w-4"}),"Generating Report..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Generate Report"]})}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2 text-center",children:"Report will be generated and available for download"})]})})]})]})]})}},{id:"individual",label:"Individual Reports",icon:b.A,description:"Generate detailed reports for specific entities",component:e=>{var s,t;let{defaultEntityType:l="delegations",defaultEntityId:r="",onReportGenerated:i}=e,[n,c]=(0,g.useState)(l),[o,d]=(0,g.useState)(r),[m,x]=(0,g.useState)("default"),[u,h]=(0,g.useState)("pdf"),{generateIndividualReport:j,isGenerating:f,error:w}=V(),{templates:k,isLoading:A}=W(),E=async()=>{if(o.trim())try{let e=await j({entityType:n,entityId:o.trim(),template:m,format:u});null==i||i(e)}catch(e){console.error("Failed to generate individual report:",e)}},T=eh.find(e=>e.id===n),R=(null==k?void 0:k.filter(e=>e.entityTypes.includes(n)))||[];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Individual Report Generator"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Generate detailed reports for specific entities"})]}),(0,a.jsxs)(v.E,{variant:"outline",className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),"Individual Report"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eu.A,{className:"h-5 w-5"}),"Entity Selection"]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"mb-2 block",children:"Entity Type"}),(0,a.jsxs)(C.l6,{value:n,onValueChange:c,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{})}),(0,a.jsx)(C.gC,{children:eh.map(e=>{let s=e.icon;return(0,a.jsx)(C.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})]})},e.id)})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(S.J,{htmlFor:"entityId",className:"mb-2 block",children:[null==T?void 0:T.name," ID"]}),(0,a.jsx)(D.p,{id:"entityId",value:o,onChange:e=>d(e.target.value),placeholder:null==T?void 0:T.placeholder,className:"w-full"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:null==T?void 0:T.description})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"mb-2 block",children:"Report Template"}),A?(0,a.jsx)(ed,{}):(0,a.jsxs)(C.l6,{value:m,onValueChange:x,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select a template"})}),(0,a.jsxs)(C.gC,{children:[(0,a.jsx)(C.eb,{value:"default",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Default Template"}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:"Standard individual report format"})]})}),R.map(e=>(0,a.jsx)(C.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"mb-2 block",children:"Export Format"}),(0,a.jsxs)(C.l6,{value:u,onValueChange:h,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{})}),(0,a.jsx)(C.gC,{children:ep.map(e=>(0,a.jsx)(C.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))})]})]})]})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(N.ZB,{children:"Report Preview"})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"text-sm font-medium text-gray-600",children:"Entity Type"}),(0,a.jsx)("div",{className:"flex items-center gap-2 mt-1",children:T&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(T.icon,{className:"h-4 w-4 text-gray-600"}),(0,a.jsx)("span",{className:"text-sm",children:T.name})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"text-sm font-medium text-gray-600",children:"Entity ID"}),(0,a.jsx)("p",{className:"mt-1 text-sm font-mono bg-white px-2 py-1 rounded border",children:o||"Not specified"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"text-sm font-medium text-gray-600",children:"Template"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:(null==(s=R.find(e=>e.id===m))?void 0:s.name)||"Default Template"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"text-sm font-medium text-gray-600",children:"Format"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:null==(t=ep.find(e=>e.id===u))?void 0:t.name})]})]}),w&&(0,a.jsx)($.Fc,{variant:"destructive",children:(0,a.jsx)($.TN,{children:w})}),(0,a.jsx)(y.$,{onClick:E,disabled:!o.trim()||f,className:"w-full",size:"lg",children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ed,{className:"mr-2 h-4 w-4"}),"Generating Report..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Generate Individual Report"]})}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 text-center",children:["Report will include detailed information about the selected"," ",null==T?void 0:T.name.toLowerCase()]})]})]})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(N.ZB,{children:"Quick Actions"})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:eh.map(e=>{let s=e.icon;return(0,a.jsxs)(y.$,{variant:"outline",className:"h-auto p-4 flex flex-col items-center gap-2",onClick:()=>{c(e.id),d("")},children:[(0,a.jsx)(s,{className:"h-6 w-6"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.name," Report"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:["Generate ",e.name.toLowerCase()," report"]})]})]},e.id)})})})]})]})}},{id:"aggregate",label:"Aggregate Analytics",icon:l.A,description:"Generate analytics reports with aggregated data",component:e=>{let{defaultEntityType:s="delegations",onReportGenerated:t}=e,[i,n]=(0,g.useState)(s),[c,o]=(0,g.useState)("default"),[d,m]=(0,g.useState)("pdf"),[x,h]=(0,g.useState)({from:(0,q.e)(new Date,29),to:new Date}),[j,f]=(0,g.useState)({}),[b,w]=(0,g.useState)(!0),[k,E]=(0,g.useState)(!0),{error:D,generateAggregateReport:T,isGenerating:R}=V(),{isLoading:z,templates:F}=W(),{exportReportToExcel:P,exportReportToPDF:I,exportToCSV:L}=(0,ej.useExport)(),O=(e,s)=>{f(t=>({...t,[e]:s}))},B=async()=>{try{let s,a={...j,...x&&{dateRange:{from:x.from.toISOString(),to:x.to.toISOString()}}};try{s=await T({entityType:i,filters:a,format:"json",options:{includeCharts:b,includeTrends:k},template:c})}catch(e){console.warn("API call failed, providing fallback data:",e),s={data:{priorityDistribution:[],records:[],statusDistribution:[],summary:{generatedAt:new Date().toISOString(),message:"Unable to fetch ".concat(i," data from server. This is a sample report with fallback data."),note:"Please check your connection and try again."},totalCount:0},metadata:{entityType:i,generatedAt:new Date().toISOString(),generatedBy:"System (Fallback)",id:"fallback_".concat(Date.now()),note:"Generated with fallback data due to API unavailability",status:"fallback"}}}if(!s)throw Error("No report data received from server");s.data||(s.data={priorityDistribution:[],records:[],statusDistribution:[],summary:{generatedAt:new Date().toISOString(),message:"No data available for the selected criteria"},totalCount:0});let l="".concat(i.charAt(0).toUpperCase()+i.slice(1)," Analytics Report"),r="".concat(i,"-analytics-").concat(new Date().toISOString().split("T")[0]);try{var e;switch(eg.JP.show({description:"Creating ".concat(d.toUpperCase()," report for ").concat(i,"..."),duration:2e3,title:"Generating Report"}),d){case"csv":{let e=Array.isArray(s.data)?s.data:[s.data];L(e,{filename:r}),console.log("CSV export completed successfully");break}case"excel":P(s,i,r),console.log("Excel export completed successfully");break;case"pdf":console.log("Starting PDF export with data:",s),await I(s,i,l,r),console.log("PDF export completed successfully");break;default:throw Error("Unsupported export format: ".concat(d))}eg.JP.success("Report Generated Successfully","".concat(d.toUpperCase()," report has been generated and downloaded. ID: ").concat((null==(e=s.metadata)?void 0:e.id)||"N/A"))}catch(e){throw console.error("Failed to export ".concat(d," report:"),e),eg.JP.error("Export Failed","Report was generated but ".concat(d.toUpperCase()," export failed: ").concat(e.message||"Unknown export error")),Error("Report generated successfully but export failed: ".concat(e.message||"Unknown export error"))}null==t||t(s)}catch(e){throw console.error("Failed to generate aggregate report:",e),e}},Y=ev.find(e=>e.id===i),U=(null==F?void 0:F.filter(e=>e.entityTypes.includes(i)))||[],Z=ef[i]||[];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Aggregate Report Generator"}),(0,a.jsx)("p",{className:"mt-1 text-gray-600",children:"Generate analytics reports with aggregated data and insights"})]}),(0,a.jsxs)(v.E,{className:"flex items-center gap-2",variant:"outline",children:[(0,a.jsx)(l.A,{className:"size-4"}),"Aggregate Report"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[(0,a.jsxs)("div",{className:"space-y-6 lg:col-span-2",children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(r.A,{className:"size-5"}),"Entity Type & Analytics"]})}),(0,a.jsx)(N.Wu,{className:"space-y-4",children:(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:ev.map(e=>{let s=e.icon,t=i===e.id;return(0,a.jsx)("div",{className:"\n                        cursor-pointer rounded-lg border p-4 transition-all\n                        ".concat(t?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300","\n                      "),onClick:()=>n(e.id),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(s,{className:"mt-1 size-6 text-gray-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center gap-2",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsx)(v.E,{className:e.color,variant:"secondary",children:"Analytics"})]}),(0,a.jsx)("p",{className:"mb-3 text-sm text-gray-600",children:e.description}),(0,a.jsx)("div",{className:"space-y-1",children:e.metrics.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[(0,a.jsx)("div",{className:"size-1 rounded-full bg-gray-400"}),e]},e))})]})]})},e.id)})})})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"size-5"}),"Filters & Date Range"]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(S.J,{className:"mb-2 flex items-center gap-2",children:[(0,a.jsx)(M.A,{className:"size-4"}),"Date Range"]}),(0,a.jsx)(eo,{onChange:h,placeholder:"Select date range for analytics",value:x})]}),Z.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(S.J,{className:"text-sm font-medium",children:"Entity Filters"}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:Z.map(e=>{var s;return(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"mb-1 block text-sm",children:e.name}),"select"===e.type?(0,a.jsxs)(C.l6,{onValueChange:s=>{O(e.id,"all"===s?"":s)},value:j[e.id]||"all",children:[(0,a.jsx)(C.bq,{className:"h-8",children:(0,a.jsx)(C.yv,{placeholder:"Select ".concat(e.name.toLowerCase())})}),(0,a.jsxs)(C.gC,{children:[(0,a.jsxs)(C.eb,{value:"all",children:["All ",e.name,"s"]}),null==(s=e.options)?void 0:s.map(e=>(0,a.jsx)(C.eb,{value:e,children:e},e))]})]}):(0,a.jsx)("input",{className:"h-8 w-full rounded-md border border-gray-300 px-3 text-sm",onChange:s=>O(e.id,s.target.value),placeholder:"Filter by ".concat(e.name.toLowerCase()),type:"text",value:j[e.id]||""})]},e.id)})})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(S.J,{className:"text-sm font-medium",children:"Report Options"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A.S,{checked:b,id:"includeCharts",onCheckedChange:e=>w(!0===e)}),(0,a.jsx)(S.J,{className:"text-sm",htmlFor:"includeCharts",children:"Include Charts & Visualizations"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A.S,{checked:k,id:"includeTrends",onCheckedChange:e=>E(!0===e)}),(0,a.jsx)(S.J,{className:"text-sm",htmlFor:"includeTrends",children:"Include Trend Analysis"})]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(N.ZB,{children:"Report Configuration"})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"mb-2 block",children:"Template"}),z?(0,a.jsx)(ed,{}):(0,a.jsxs)(C.l6,{onValueChange:o,value:c,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select template"})}),(0,a.jsxs)(C.gC,{children:[(0,a.jsx)(C.eb,{value:"default",children:"Default Analytics"}),U.map(e=>(0,a.jsx)(C.eb,{value:e.id,children:e.name},e.id))]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(S.J,{className:"mb-2 block",children:"Export Format"}),(0,a.jsxs)(C.l6,{onValueChange:m,value:d,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{})}),(0,a.jsx)(C.gC,{children:ey.map(e=>(0,a.jsx)(C.eb,{value:e.id,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]})},e.id))})]})]}),(0,a.jsxs)("div",{className:"space-y-2 rounded-lg bg-gray-50 p-3",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:Y&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Y.icon,{className:"size-4"}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[Y.name," Analytics"]})]})}),x&&(0,a.jsxs)("p",{className:"text-xs text-gray-600",children:[x.from.toLocaleDateString()," -"," ",x.to.toLocaleDateString()]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:Object.entries(j).filter(e=>{let[s,t]=e;return t}).map(e=>{let[s,t]=e;return(0,a.jsxs)(v.E,{className:"text-xs",variant:"outline",children:[s,": ",t]},s)})})]})]})]}),(0,a.jsx)(N.Zp,{children:(0,a.jsxs)(N.Wu,{className:"pt-6",children:[D&&(0,a.jsx)($.Fc,{className:"mb-4",variant:"destructive",children:(0,a.jsx)($.TN,{children:D})}),(0,a.jsx)(y.$,{className:"w-full",disabled:R,onClick:B,size:"lg",children:R?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ed,{className:"mr-2 size-4"}),"Generating Analytics..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 size-4"}),"Generate Aggregate Report"]})}),(0,a.jsx)("p",{className:"mt-2 text-center text-xs text-gray-500",children:"Report will include aggregated analytics and insights"})]})})]})]})]})}},{id:"history",label:"Report History",icon:w.A,description:"View and manage generated reports",component:e=>{let{onReportSelect:s}=e,[t,l]=(0,g.useState)(""),[r,i]=(0,g.useState)(""),[n,c]=(0,g.useState)(""),{error:o,isLoading:m,pagination:x,refetch:j,reports:f}=B({...t&&{type:t},...r&&{entityType:r}}),{downloadError:b,downloadReport:k,isDownloading:A}=Y(),S=async e=>{try{await k(e)}catch(e){console.error("Failed to download report:",e)}},E=f.filter(e=>{var s,t;if(!n)return!0;let a=n.toLowerCase();return e.id.toLowerCase().includes(a)||e.type.toLowerCase().includes(a)||(null==(s=e.entityType)?void 0:s.toLowerCase().includes(a))||(null==(t=e.entityTypes)?void 0:t.some(e=>e.toLowerCase().includes(a)))}),T=e=>"individual"===e.type||"aggregate"===e.type?e.entityType:e.entityTypes?e.entityTypes.join(", "):"N/A";return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Report History"}),(0,a.jsx)("p",{className:"mt-1 text-gray-600",children:"View and manage your generated reports"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(y.$,{disabled:m,onClick:()=>j(),size:"sm",variant:"outline",children:[(0,a.jsx)(h.A,{className:"mr-2 size-4 ".concat(m?"animate-spin":"")}),"Refresh"]}),(0,a.jsxs)(v.E,{className:"flex items-center gap-2",variant:"outline",children:[(0,a.jsx)(w.A,{className:"size-4"}),f.length," Reports"]})]})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"size-5"}),"Filters"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-4",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(eu.A,{className:"absolute left-3 top-1/2 size-4 -translate-y-1/2 text-gray-400"}),(0,a.jsx)(D.p,{className:"pl-10",onChange:e=>c(e.target.value),placeholder:"Search reports...",value:n})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)(C.l6,{onValueChange:l,value:t,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Report Type"})}),(0,a.jsx)(C.gC,{children:ew.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)(C.l6,{onValueChange:i,value:r,children:[(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Entity Type"})}),(0,a.jsx)(C.gC,{children:ek.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]})}),(0,a.jsx)("div",{children:(0,a.jsx)(y.$,{className:"w-full",onClick:()=>{l(""),i(""),c("")},variant:"outline",children:"Clear Filters"})})]})})]}),(o||b)&&(0,a.jsx)($.Fc,{variant:"destructive",children:(0,a.jsx)($.TN,{children:String(o||b)})}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(N.ZB,{children:"Generated Reports"})}),(0,a.jsx)(N.Wu,{children:m?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsx)(ed,{className:"size-8"})}):0===E.length?(0,a.jsxs)("div",{className:"py-8 text-center",children:[(0,a.jsx)(d.A,{className:"mx-auto mb-4 size-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"No Reports Found"}),(0,a.jsx)("p",{className:"text-gray-600",children:0===f.length?"No reports have been generated yet.":"No reports match your current filters."})]}):(0,a.jsx)("div",{className:"space-y-4",children:E.map(e=>{let t=eA[e.status],l=eC[e.format];return(0,a.jsx)("div",{className:"rounded-lg border p-4 transition-colors hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center gap-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.id}),(0,a.jsx)(v.E,{className:null==t?void 0:t.color,variant:"secondary",children:(null==t?void 0:t.label)||e.status}),(0,a.jsx)(v.E,{className:null==l?void 0:l.color,variant:"secondary",children:(null==l?void 0:l.label)||e.format}),(0,a.jsx)(v.E,{variant:"outline",children:e.type})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 text-sm text-gray-600 md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Entity Types:"}),(0,a.jsx)("p",{className:"mt-1",children:T(e)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Generated:"}),(0,a.jsxs)("p",{className:"mt-1 flex items-center gap-1",children:[(0,a.jsx)(M.A,{className:"size-3"}),(0,eN.m)(new Date(e.generatedAt),{addSuffix:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"File Size:"}),(0,a.jsx)("p",{className:"mt-1",children:e.fileSize||"N/A"})]})]}),"individual"===e.type&&e.entityId&&(0,a.jsxs)("div",{className:"mt-2 text-sm text-gray-600",children:[(0,a.jsx)("span",{className:"font-medium",children:"Entity ID:"})," ",e.entityId]})]}),(0,a.jsxs)("div",{className:"ml-4 flex items-center gap-2",children:[s&&(0,a.jsxs)(y.$,{onClick:()=>s(e),size:"sm",variant:"outline",children:[(0,a.jsx)(eb.A,{className:"mr-1 size-4"}),"View"]}),"completed"===e.status&&(0,a.jsxs)(y.$,{disabled:A,onClick:()=>S(e.id),size:"sm",variant:"outline",children:[A?(0,a.jsx)(ed,{className:"mr-1 size-4"}):(0,a.jsx)(p.A,{className:"mr-1 size-4"}),"Download"]})]})]})},e.id)})})})]}),x&&x.totalPages>1&&(0,a.jsx)(N.Zp,{children:(0,a.jsx)(N.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Showing ",(x.page-1)*x.limit+1," to"," ",Math.min(x.page*x.limit,x.total)," ","of ",x.total," reports"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(y.$,{disabled:x.page<=1,size:"sm",variant:"outline",children:"Previous"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Page ",x.page," of ",x.totalPages]}),(0,a.jsx)(y.$,{disabled:x.page>=x.totalPages,size:"sm",variant:"outline",children:"Next"})]})]})})})]})}}],eE=()=>{let[e,s]=(0,g.useState)("comprehensive"),[t,r]=(0,g.useState)([]),i=e=>{var s;console.log("Report generated:",e);let t=(null==e||null==(s=e.data)?void 0:s.metadata)||(null==e?void 0:e.metadata);if(!t){console.error("No metadata found in report result:",e),alert("Report generated, but metadata is missing");return}let a={...e,metadata:t};r(e=>[a,...e.slice(0,4)]),alert("Report generated successfully! ID: ".concat(t.id))},n=e=>{console.log("Report selected:",e)};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Data Report Generation"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Generate comprehensive reports for delegations, tasks, vehicles, and employees"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(v.E,{variant:"outline",className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"Report Builder"]}),(0,a.jsxs)(y.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Settings"]})]})]}),t.length>0&&(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(k.A,{className:"h-5 w-5"}),"Recent Activity"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:t.map((e,s)=>{var t;let l=null==e?void 0:e.metadata;return l?(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)(v.E,{variant:"outline",className:"text-xs",children:l.type||"Unknown"}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:l.generatedAt?new Date(l.generatedAt).toLocaleTimeString():"Unknown time"})]}),(0,a.jsx)("p",{className:"text-sm font-medium truncate",children:l.id||"No ID"}),(0,a.jsx)("p",{className:"text-xs text-gray-600",children:(null==(t=l.entityTypes)?void 0:t.join(", "))||l.entityType||"Unknown entity"})]},s):null})})})]}),(0,a.jsxs)($.Fc,{children:[(0,a.jsx)(k.A,{className:"h-4 w-4"}),(0,a.jsxs)($.TN,{children:[(0,a.jsx)("strong",{children:"Report Generation System:"})," Generate individual entity reports, aggregate analytics, or comprehensive reports across multiple data sources. All reports support PDF, Excel, and CSV export formats."]})]}),(0,a.jsxs)(f.tU,{value:e,onValueChange:s,className:"space-y-6",children:[(0,a.jsx)(N.Zp,{children:(0,a.jsxs)(N.Wu,{className:"pt-6",children:[(0,a.jsx)(f.j7,{className:"grid w-full grid-cols-4",children:eS.map(e=>{let s=e.icon;return(0,a.jsxs)(f.Xi,{value:e.id,className:"flex items-center gap-2 data-[state=active]:bg-blue-50",children:[(0,a.jsx)(s,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:e.label})]},e.id)})}),(0,a.jsx)("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:eS.map(s=>{if(s.id!==e)return null;let t=s.icon;return(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(t,{className:"h-5 w-5 text-gray-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:s.label}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:s.description})]})]},s.id)})})]})}),eS.map(e=>{let s=e.component;return(0,a.jsx)(f.av,{value:e.id,className:"space-y-6",children:(0,a.jsx)(s,{onReportGenerated:i,onReportSelect:n})},e.id)})]}),(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(N.ZB,{children:"Report Generation Guide"})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Comprehensive"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Generate reports that include data from multiple entity types with cross-entity analytics."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Individual"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Create detailed reports for specific delegations, tasks, vehicles, or employees."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"h-4 w-4 text-orange-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Aggregate"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Generate analytics reports with aggregated metrics and trend analysis."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 text-purple-600"}),(0,a.jsx)("span",{className:"font-medium",children:"History"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"View, download, and manage all your previously generated reports."})]})]})})]})]})};var eD=t(75143),eT=t(58543),eR=t(59119),ez=t(50402),eM=t(40295),eF=t(77223),eP=t(18046);let eI=e=>{let{widget:s,onConfigure:t,onDelete:l}=e,{attributes:r,listeners:i,setNodeRef:n,transform:c,transition:o,isDragging:d}=(0,ez.gl)({id:s.id,data:{type:"widget",widget:s}}),x={transform:c?"translate3d(".concat(c.x,"px, ").concat(c.y,"px, 0)"):void 0,transition:o};return(0,a.jsx)("div",{ref:n,style:x,className:(0,en.cn)("group relative",d&&"opacity-50"),children:(0,a.jsx)(N.Zp,{className:"border-2 border-dashed border-gray-200 hover:border-blue-300 transition-colors",children:(0,a.jsxs)(N.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{...r,...i,className:"cursor-grab active:cursor-grabbing p-1 hover:bg-gray-100 rounded",children:(0,a.jsx)(eM.A,{className:"h-4 w-4 text-gray-400"})}),(0,a.jsx)("h4",{className:"text-sm font-medium",children:s.title})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:t,className:"h-8 w-8 p-0",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:l,className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:(0,a.jsx)(eF.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 min-h-[120px] flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[(0,a.jsx)(eT.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsxs)("div",{className:"text-sm",children:[s.type," Widget"]}),(0,a.jsx)("div",{className:"text-xs mt-1",children:s.config&&Object.keys(s.config).length>0?"Configured":"Click settings to configure"})]})}),(0,a.jsxs)("div",{className:"mt-3 flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:["Type: ",s.type]}),(0,a.jsxs)("span",{children:["Span: ",s.span]})]})]})})})},eL=e=>{let{widgets:s,columns:t,onWidgetConfigure:l,onWidgetDelete:r,className:i=""}=e,{isOver:n,setNodeRef:c}=(0,eD.zM)({id:"drop-zone"});return(0,a.jsx)(N.Zp,{className:(0,en.cn)("min-h-[400px]",i),children:(0,a.jsxs)(N.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Report Canvas"}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[s.length," widget",1!==s.length?"s":""]})]}),(0,a.jsx)("div",{ref:c,className:(0,en.cn)("min-h-[300px] border-2 border-dashed rounded-lg p-4 transition-colors",n?"border-blue-400 bg-blue-50":"border-gray-200",0===s.length&&"flex items-center justify-center"),children:0===s.length?(0,a.jsxs)("div",{className:"text-center text-gray-500",children:[(0,a.jsx)(eT.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("h4",{className:"text-lg font-medium mb-2",children:"Start Building Your Report"}),(0,a.jsx)("p",{className:"text-sm mb-4",children:"Drag widgets from the palette on the left to create your custom report"}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2 text-xs",children:[(0,a.jsx)(eP.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Drop widgets here"})]})]}):(0,a.jsx)(ez.gB,{items:s.map(e=>e.id),strategy:ez._G,children:(0,a.jsx)("div",{className:(0,en.cn)("grid gap-4",{1:"grid-cols-1",2:"grid-cols-1 md:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3",4:"grid-cols-1 md:grid-cols-2 lg:grid-cols-4"}[t]||"grid-cols-2"),children:s.sort((e,s)=>(e.position||0)-(s.position||0)).map(e=>(0,a.jsx)(eI,{widget:e,onConfigure:()=>l(e),onDelete:()=>r(e.id)},e.id))})})}),n&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 text-blue-700",children:[(0,a.jsx)(eP.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Drop widget here to add to report"})]})}),(0,a.jsx)("div",{className:"mt-4 pt-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Layout: ",t," column",1!==t?"s":""]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:"Drag widgets to reorder • Click settings to configure"})]})})]})})};var eO=t(62177),eV=t(90221),eB=t(71153),eY=t(54165),eW=t(17759);let eU=eB.z.object({title:eB.z.string().min(1,"Title is required"),span:eB.z.string(),showTitle:eB.z.boolean().default(!0),showBorder:eB.z.boolean().default(!0),refreshInterval:eB.z.number().min(0).max(1440).optional(),height:eB.z.number().min(200).max(800).optional(),chartType:eB.z.string().optional(),xAxisField:eB.z.string().optional(),yAxisField:eB.z.string().optional(),colorScheme:eB.z.string().optional(),showLegend:eB.z.boolean().optional(),showGrid:eB.z.boolean().optional(),pageSize:eB.z.number().min(5).max(100).optional(),sortable:eB.z.boolean().optional(),filterable:eB.z.boolean().optional(),exportable:eB.z.boolean().optional(),metricType:eB.z.string().optional(),aggregationType:eB.z.string().optional(),comparisonPeriod:eB.z.string().optional()}),eZ=[{value:"col-span-1",label:"1 Column"},{value:"col-span-2",label:"2 Columns"},{value:"col-span-3",label:"3 Columns"},{value:"col-span-4",label:"4 Columns"},{value:"col-span-full",label:"Full Width"}],e$=[{value:"bar",label:"Bar Chart"},{value:"line",label:"Line Chart"},{value:"pie",label:"Pie Chart"},{value:"area",label:"Area Chart"},{value:"scatter",label:"Scatter Plot"}],e_=[{value:"default",label:"Default"},{value:"blue",label:"Blue"},{value:"green",label:"Green"},{value:"red",label:"Red"},{value:"purple",label:"Purple"},{value:"orange",label:"Orange"}],eG=e=>{var s,t,l,r,i,n,c,o,d,x,u,h,p,g,j,v,f,N,b,w,k,S,E,T;let{widget:R,onSave:z,onCancel:M}=e,F=(0,eO.mN)({resolver:(0,eV.u)(eU),defaultValues:{title:R.title,span:R.span,showTitle:null==(N=null==(s=R.config)?void 0:s.showTitle)||N,showBorder:null==(b=null==(t=R.config)?void 0:t.showBorder)||b,refreshInterval:(null==(l=R.config)?void 0:l.refreshInterval)||60,height:(null==(r=R.config)?void 0:r.height)||300,chartType:(null==(i=R.config)?void 0:i.chartType)||"bar",xAxisField:(null==(n=R.config)?void 0:n.xAxisField)||"",yAxisField:(null==(c=R.config)?void 0:c.yAxisField)||"",colorScheme:(null==(o=R.config)?void 0:o.colorScheme)||"default",showLegend:null==(w=null==(d=R.config)?void 0:d.showLegend)||w,showGrid:null==(k=null==(x=R.config)?void 0:x.showGrid)||k,pageSize:(null==(u=R.config)?void 0:u.pageSize)||10,sortable:null==(S=null==(h=R.config)?void 0:h.sortable)||S,filterable:null==(E=null==(p=R.config)?void 0:p.filterable)||E,exportable:null==(T=null==(g=R.config)?void 0:g.exportable)||T,metricType:(null==(j=R.config)?void 0:j.metricType)||"count",aggregationType:(null==(v=R.config)?void 0:v.aggregationType)||"sum",comparisonPeriod:(null==(f=R.config)?void 0:f.comparisonPeriod)||"previous-month"}}),P=["bar-chart","pie-chart","line-chart"].includes(R.type),I="data-table"===R.type;return["analytics","metrics"].includes(R.type),(0,a.jsx)(eY.lG,{open:!0,onOpenChange:M,children:(0,a.jsxs)(eY.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(eY.c7,{children:[(0,a.jsxs)(eY.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Configure Widget: ",R.title]}),(0,a.jsx)(eY.rr,{children:"Customize the appearance and behavior of this widget."})]}),(0,a.jsx)(eW.lV,{...F,children:(0,a.jsxs)("form",{onSubmit:F.handleSubmit(e=>{z({...R,title:e.title,span:e.span,config:{...R.config,...e}})}),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Basic Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eW.zB,{control:F.control,name:"title",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{children:[(0,a.jsx)(eW.lR,{children:"Widget Title"}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(D.p,{placeholder:"Enter widget title",...s})}),(0,a.jsx)(eW.C5,{})]})}}),(0,a.jsx)(eW.zB,{control:F.control,name:"span",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{children:[(0,a.jsx)(eW.lR,{children:"Width"}),(0,a.jsxs)(C.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select width"})})}),(0,a.jsx)(C.gC,{children:eZ.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(eW.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eW.zB,{control:F.control,name:"height",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{children:[(0,a.jsx)(eW.lR,{children:"Height (px)"}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(D.p,{type:"number",min:"200",max:"800",...s,onChange:e=>s.onChange(parseInt(e.target.value)||300)})}),(0,a.jsx)(eW.C5,{})]})}}),(0,a.jsx)(eW.zB,{control:F.control,name:"refreshInterval",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{children:[(0,a.jsx)(eW.lR,{children:"Refresh Interval (minutes)"}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(D.p,{type:"number",min:"0",max:"1440",...s,onChange:e=>s.onChange(parseInt(e.target.value)||60)})}),(0,a.jsx)(eW.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eW.zB,{control:F.control,name:"showTitle",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eW.lR,{children:"Show Title"}),(0,a.jsx)(eW.Rr,{children:"Display widget title"})]}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(A.S,{checked:s.value,onCheckedChange:s.onChange})})]})}}),(0,a.jsx)(eW.zB,{control:F.control,name:"showBorder",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eW.lR,{children:"Show Border"}),(0,a.jsx)(eW.Rr,{children:"Display widget border"})]}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(A.S,{checked:s.value,onCheckedChange:s.onChange})})]})}})]})]}),P&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Chart Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eW.zB,{control:F.control,name:"chartType",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{children:[(0,a.jsx)(eW.lR,{children:"Chart Type"}),(0,a.jsxs)(C.l6,{onValueChange:s.onChange,value:s.value||"",children:[(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select chart type"})})}),(0,a.jsx)(C.gC,{children:e$.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(eW.C5,{})]})}}),(0,a.jsx)(eW.zB,{control:F.control,name:"colorScheme",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{children:[(0,a.jsx)(eW.lR,{children:"Color Scheme"}),(0,a.jsxs)(C.l6,{onValueChange:s.onChange,value:s.value||"",children:[(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select color scheme"})})}),(0,a.jsx)(C.gC,{children:e_.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(eW.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eW.zB,{control:F.control,name:"showLegend",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eW.lR,{children:"Show Legend"}),(0,a.jsx)(eW.Rr,{children:"Display chart legend"})]}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(A.S,{checked:s.value||!1,onCheckedChange:s.onChange})})]})}}),(0,a.jsx)(eW.zB,{control:F.control,name:"showGrid",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eW.lR,{children:"Show Grid"}),(0,a.jsx)(eW.Rr,{children:"Display chart grid lines"})]}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(A.S,{checked:s.value||!1,onCheckedChange:s.onChange})})]})}})]})]}),I&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Table Settings"}),(0,a.jsx)(eW.zB,{control:F.control,name:"pageSize",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{children:[(0,a.jsx)(eW.lR,{children:"Page Size"}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(D.p,{type:"number",min:"5",max:"100",...s,onChange:e=>s.onChange(parseInt(e.target.value)||10)})}),(0,a.jsx)(eW.Rr,{children:"Number of rows per page"}),(0,a.jsx)(eW.C5,{})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsx)(eW.zB,{control:F.control,name:"sortable",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eW.lR,{children:"Sortable"}),(0,a.jsx)(eW.Rr,{children:"Enable column sorting"})]}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(A.S,{checked:s.value||!1,onCheckedChange:s.onChange})})]})}}),(0,a.jsx)(eW.zB,{control:F.control,name:"filterable",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eW.lR,{children:"Filterable"}),(0,a.jsx)(eW.Rr,{children:"Enable column filters"})]}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(A.S,{checked:s.value||!1,onCheckedChange:s.onChange})})]})}}),(0,a.jsx)(eW.zB,{control:F.control,name:"exportable",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eW.lR,{children:"Exportable"}),(0,a.jsx)(eW.Rr,{children:"Enable data export"})]}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(A.S,{checked:s.value||!1,onCheckedChange:s.onChange})})]})}})]})]}),(0,a.jsxs)(eY.Es,{children:[(0,a.jsx)(y.$,{type:"button",variant:"outline",onClick:M,children:"Cancel"}),(0,a.jsx)(y.$,{type:"submit",children:"Save Configuration"})]})]})})]})})};var eJ=t(9041),eq=t(58527),eK=t(88390);let eQ=e=>{let{widget:s,isDisabled:t=!1}=e,{attributes:l,listeners:r,setNodeRef:i,transform:n,isDragging:c}=(0,eD.PM)({id:s.id,data:{type:"widget-type",widgetType:s.id},disabled:t}),o=n?{transform:"translate3d(".concat(n.x,"px, ").concat(n.y,"px, 0)")}:void 0;return(0,a.jsxs)("div",{ref:i,style:o,...r,...l,className:(0,en.cn)("p-3 border rounded-lg cursor-grab active:cursor-grabbing transition-all",c&&"opacity-50",t?"opacity-50 cursor-not-allowed":"hover:shadow-md hover:border-blue-300",!t&&"bg-white"),children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[s.icon,(0,a.jsx)("span",{className:"text-sm font-medium",children:s.name})]}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:s.description}),(0,a.jsx)(v.E,{variant:"outline",className:"text-xs",children:s.category})]})},eH=[{id:"analytics",name:"Analytics Widget",description:"Key metrics and performance indicators",icon:(0,a.jsx)(r.A,{className:"h-4 w-4"}),category:"Analytics",supportedDataSources:["delegations","tasks","vehicles","employees","cross-entity"]},{id:"metrics",name:"Metrics Widget",description:"Display key performance metrics",icon:(0,a.jsx)(l.A,{className:"h-4 w-4"}),category:"Analytics",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"bar-chart",name:"Bar Chart",description:"Compare values across categories",icon:(0,a.jsx)(l.A,{className:"h-4 w-4"}),category:"Charts",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"pie-chart",name:"Pie Chart",description:"Show proportional data distribution",icon:(0,a.jsx)(eJ.A,{className:"h-4 w-4"}),category:"Charts",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"line-chart",name:"Line Chart",description:"Display trends over time",icon:(0,a.jsx)(eq.A,{className:"h-4 w-4"}),category:"Charts",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"data-table",name:"Data Table",description:"Tabular data with sorting and filtering",icon:(0,a.jsx)(x.A,{className:"h-4 w-4"}),category:"Data",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"employee-performance",name:"Employee Performance",description:"Employee performance metrics and charts",icon:(0,a.jsx)(c.A,{className:"h-4 w-4"}),category:"Employee",supportedDataSources:["employees","cross-entity"]},{id:"vehicle-utilization",name:"Vehicle Utilization",description:"Vehicle usage and utilization metrics",icon:(0,a.jsx)(n.A,{className:"h-4 w-4"}),category:"Vehicle",supportedDataSources:["vehicles","cross-entity"]},{id:"task-status",name:"Task Status",description:"Task completion and status tracking",icon:(0,a.jsx)(i.A,{className:"h-4 w-4"}),category:"Task",supportedDataSources:["tasks","cross-entity"]},{id:"delegation-overview",name:"Delegation Overview",description:"Delegation status and distribution",icon:(0,a.jsx)(d.A,{className:"h-4 w-4"}),category:"Delegation",supportedDataSources:["delegations","cross-entity"]},{id:"correlation",name:"Correlation Analysis",description:"Cross-entity relationships and correlations",icon:(0,a.jsx)(o.A,{className:"h-4 w-4"}),category:"Analysis",supportedDataSources:["cross-entity"]},{id:"timeline",name:"Timeline Widget",description:"Events and activities over time",icon:(0,a.jsx)(M.A,{className:"h-4 w-4"}),category:"Timeline",supportedDataSources:["delegations","tasks","vehicles","employees"]},{id:"cost-analysis",name:"Cost Analysis",description:"Financial metrics and cost tracking",icon:(0,a.jsx)(eK.A,{className:"h-4 w-4"}),category:"Financial",supportedDataSources:["vehicles","employees","cross-entity"]}],eX=e=>{let{dataSource:s,className:t=""}=e,l=eH.filter(e=>e.supportedDataSources.includes(s)||e.supportedDataSources.includes("cross-entity")),r=l.reduce((e,s)=>(e[s.category]||(e[s.category]=[]),e[s.category].push(s),e),{});return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsxs)(N.aR,{className:"pb-3",children:[(0,a.jsx)(N.ZB,{className:"text-lg",children:"Widget Palette"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Drag widgets to the report canvas to build your custom report"}),(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs w-fit",children:[l.length," widgets available"]})]}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[Object.entries(r).map(e=>{let[t,l]=e;return(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2 text-gray-700",children:t}),(0,a.jsx)("div",{className:"space-y-2",children:l.map(e=>(0,a.jsx)(eQ,{widget:e,isDisabled:!e.supportedDataSources.includes(s)&&!e.supportedDataSources.includes("cross-entity")},e.id))})]},t)}),0===l.length&&(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:(0,a.jsx)("div",{className:"text-sm",children:"No widgets available for this data source"})})]})]})},e0=e=>{let{className:s="",onPreview:t,onSave:l,reportType:r}=e,[i,n]=(0,g.useState)((null==r?void 0:r.widgetConfigs)||[]),[c,o]=(0,g.useState)(null),[d,m]=(0,g.useState)(!1),[x,u]=(0,g.useState)(null),[h,p]=(0,g.useState)({dataSource:(null==r?void 0:r.dataSource)||"delegations",description:(null==r?void 0:r.description)||"",filters:(null==r?void 0:r.filters)||[],layout:{columns:2,spacing:4},name:(null==r?void 0:r.name)||"New Report",widgets:i});return(0,a.jsxs)("div",{className:(0,en.cn)("space-y-6",s),children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eT.A,{className:"size-5"}),"Report Builder"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{className:"text-xs",variant:"secondary",children:[i.length," widgets"]}),(0,a.jsxs)(y.$,{onClick:()=>{t&&t(h)},size:"sm",variant:"outline",children:[(0,a.jsx)(eb.A,{className:"mr-2 size-4"}),"Preview"]}),(0,a.jsxs)(y.$,{onClick:()=>{l&&l(h)},size:"sm",children:[(0,a.jsx)(eR.A,{className:"mr-2 size-4"}),"Save Report"]})]})]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)("div",{className:"mb-6 grid grid-cols-1 gap-4 md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Report Name"}),(0,a.jsx)("input",{className:"mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",onChange:e=>p(s=>({...s,name:e.target.value})),type:"text",value:h.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Data Source"}),(0,a.jsxs)("select",{className:"mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",onChange:e=>p(s=>({...s,dataSource:e.target.value})),value:h.dataSource,children:[(0,a.jsx)("option",{value:"delegations",children:"Delegations"}),(0,a.jsx)("option",{value:"tasks",children:"Tasks"}),(0,a.jsx)("option",{value:"vehicles",children:"Vehicles"}),(0,a.jsx)("option",{value:"employees",children:"Employees"}),(0,a.jsx)("option",{value:"cross-entity",children:"Cross-Entity"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Layout Columns"}),(0,a.jsxs)("select",{className:"mt-1 w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",onChange:e=>p(s=>({...s,layout:{...s.layout,columns:Number.parseInt(e.target.value)}})),value:h.layout.columns,children:[(0,a.jsx)("option",{value:1,children:"1 Column"}),(0,a.jsx)("option",{value:2,children:"2 Columns"}),(0,a.jsx)("option",{value:3,children:"3 Columns"}),(0,a.jsx)("option",{value:4,children:"4 Columns"})]})]})]})})]}),(0,a.jsxs)(eD.Mp,{onDragEnd:e=>{var s,t;let{active:a,over:l}=e;if(!l)return void u(null);if("drop-zone"===l.id&&(null==(s=a.data.current)?void 0:s.type)==="widget-type"){let e=a.data.current.widgetType,s={config:{},id:"widget-".concat(Date.now()),position:i.length,span:"col-span-1",title:"".concat(e," Widget"),type:e};n(e=>[...e,s]),p(e=>({...e,widgets:[...e.widgets,s]}))}if(l.id!==a.id&&(null==(t=a.data.current)?void 0:t.type)==="widget"){let e=i.findIndex(e=>e.id===a.id),s=i.findIndex(e=>e.id===l.id);if(-1!==e&&-1!==s){let t=[...i],[a]=t.splice(e,1);a&&t.splice(s,0,a);let l=t.map((e,s)=>({...e,position:s}));n(l),p(e=>({...e,widgets:l}))}}u(null)},onDragStart:e=>{u(e.active.id)},children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-4",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)(eX,{dataSource:h.dataSource})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsx)(eL,{columns:h.layout.columns,onWidgetConfigure:e=>{o(e),m(!0)},onWidgetDelete:e=>{let s=i.filter(s=>s.id!==e);n(s),p(e=>({...e,widgets:s}))},widgets:i})})]}),(0,a.jsx)(eD.Hd,{children:x?(0,a.jsx)("div",{className:"rounded-lg border bg-white p-4 shadow-lg",children:(0,a.jsx)("div",{className:"text-sm font-medium",children:x.replace("-"," ").replaceAll(/\b\w/g,e=>e.toUpperCase())})}):null})]}),d&&c&&(0,a.jsx)(eG,{onCancel:()=>{m(!1),o(null)},onSave:e=>{let s=i.map(s=>s.id===e.id?e:s);n(s),p(e=>({...e,widgets:s})),m(!1),o(null)},widget:c})]})};var e1=t(26715),e2=t(5041),e4=t(43772),e5=t(72248);let e3=()=>{let e=(0,e1.jE)(),s=(0,e4.Sk)(["report-types"],async()=>{var e;return(null==(e=(await e5.uE.get("/reporting/report-types")).data)?void 0:e.data)||[]},{cacheDuration:3e5,enableRetry:!0}),t=(0,e2.n)({mutationFn:async e=>{var s;let t=await e5.uE.post("/reporting/report-types",e);return(null==(s=t.data)?void 0:s.data)||t.data},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}}),a=(0,e2.n)({mutationFn:async e=>{var s;let{id:t,...a}=e,l=await e5.uE.put("/reporting/report-types/".concat(t),a);return(null==(s=l.data)?void 0:s.data)||l.data},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}}),l=(0,e2.n)({mutationFn:async e=>{await e5.uE.delete("/reporting/report-types/".concat(e))},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}}),r=(0,e2.n)({mutationFn:async e=>{var s;let t=await e5.uE.post("/reporting/report-types/".concat(e,"/duplicate"));return(null==(s=t.data)?void 0:s.data)||t.data},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}}),i=(0,e2.n)({mutationFn:async e=>{var s;let{id:t,isActive:a}=e,l=await e5.uE.patch("/reporting/report-types/".concat(t,"/toggle-active"),{isActive:a});return(null==(s=l.data)?void 0:s.data)||l.data},onSuccess:()=>{e.invalidateQueries({queryKey:["report-types"]})}});return{createReportType:t,data:s.data,deleteReportType:l,duplicateReportType:r,error:s.error,isLoading:s.isLoading,refetch:s.refetch,toggleReportTypeActive:i,updateReportType:a}};var e6=t(77023);function e8(e){let{error:s,title:t="Error",onRetry:l,className:r,showRetry:i=!0,retryText:n="Try Again"}=e;if(!s)return null;let c=s instanceof Error?s.message:String(s);return(0,a.jsxs)($.Fc,{variant:"destructive",className:r,children:[(0,a.jsx)(G.A,{className:"h-4 w-4"}),(0,a.jsx)($.XL,{children:t}),(0,a.jsxs)($.TN,{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-3",children:c}),i&&l&&(0,a.jsxs)(y.$,{variant:"outline",size:"sm",onClick:l,className:"h-8",children:[(0,a.jsx)(h.A,{className:"h-3 w-3 mr-2"}),n]})]})]})}let e7=eB.z.object({name:eB.z.string().min(1,"Name is required").max(100,"Name must be less than 100 characters"),description:eB.z.string().optional(),category:eB.z.string().min(1,"Category is required"),dataSource:eB.z.string().min(1,"Data source is required"),widgets:eB.z.array(eB.z.string()).min(1,"At least one widget is required"),filters:eB.z.array(eB.z.string()).optional(),isActive:eB.z.boolean().default(!0),isPublic:eB.z.boolean().default(!1),refreshInterval:eB.z.number().min(1).max(1440).optional(),tags:eB.z.array(eB.z.string()).optional()}),e9=[{value:"analytics",label:"Analytics Widget"},{value:"chart",label:"Chart Widget"},{value:"table",label:"Data Table"},{value:"metrics",label:"Metrics Widget"},{value:"correlation",label:"Correlation Widget"}],se=[{value:"delegations",label:"Delegations"},{value:"tasks",label:"Tasks"},{value:"vehicles",label:"Vehicles"},{value:"employees",label:"Employees"},{value:"cross-entity",label:"Cross-Entity"}],ss=[{value:"operational",label:"Operational"},{value:"performance",label:"Performance"},{value:"financial",label:"Financial"},{value:"compliance",label:"Compliance"},{value:"analytics",label:"Analytics"}],st=e=>{var s,t;let{reportType:l,onSubmit:r,onCancel:i,isLoading:n=!1}=e,c=(0,eO.mN)({resolver:(0,eV.u)(e7),defaultValues:{name:(null==l?void 0:l.name)||"",description:(null==l?void 0:l.description)||"",category:(null==l?void 0:l.category)||"",dataSource:(null==l?void 0:l.dataSource)||"",widgets:(null==l?void 0:l.widgets)||[],filters:(null==l?void 0:l.filters)||[],isActive:null==(s=null==l?void 0:l.isActive)||s,isPublic:null!=(t=null==l?void 0:l.isPublic)&&t,refreshInterval:(null==l?void 0:l.refreshInterval)||60,tags:(null==l?void 0:l.tags)||[]}}),o=async e=>{try{await r(e)}catch(e){console.error("Form submission error:",e)}};return(0,a.jsx)(eY.lG,{open:!0,onOpenChange:i,children:(0,a.jsxs)(eY.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(eY.c7,{children:[(0,a.jsxs)(eY.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-5 w-5"}),l?"Edit Report Type":"Create Report Type"]}),(0,a.jsx)(eY.rr,{children:l?"Update the report type configuration and settings.":"Create a new report type with custom widgets and data sources."})]}),(0,a.jsx)(eW.lV,{...c,children:(0,a.jsxs)("form",{onSubmit:c.handleSubmit(o),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Basic Information"}),(0,a.jsx)(eW.zB,{control:c.control,name:"name",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{children:[(0,a.jsx)(eW.lR,{children:"Name *"}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(D.p,{placeholder:"Enter report type name",...s})}),(0,a.jsx)(eW.C5,{})]})}}),(0,a.jsx)(eW.zB,{control:c.control,name:"description",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{children:[(0,a.jsx)(eW.lR,{children:"Description"}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(E.T,{placeholder:"Describe what this report type shows",rows:3,...s})}),(0,a.jsx)(eW.C5,{})]})}}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eW.zB,{control:c.control,name:"category",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{children:[(0,a.jsx)(eW.lR,{children:"Category *"}),(0,a.jsxs)(C.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select category"})})}),(0,a.jsx)(C.gC,{children:ss.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(eW.C5,{})]})}}),(0,a.jsx)(eW.zB,{control:c.control,name:"dataSource",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{children:[(0,a.jsx)(eW.lR,{children:"Data Source *"}),(0,a.jsxs)(C.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(C.bq,{children:(0,a.jsx)(C.yv,{placeholder:"Select data source"})})}),(0,a.jsx)(C.gC,{children:se.map(e=>(0,a.jsx)(C.eb,{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(eW.C5,{})]})}})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Widget Configuration"}),(0,a.jsx)(eW.zB,{control:c.control,name:"widgets",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{children:[(0,a.jsx)(eW.lR,{children:"Widgets *"}),(0,a.jsx)(eW.Rr,{children:"Select the widgets to include in this report type"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:e9.map(e=>{var t;return(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A.S,{id:e.value,checked:null==(t=s.value)?void 0:t.includes(e.value),onCheckedChange:t=>{let a=s.value||[];t?s.onChange([...a,e.value]):s.onChange(a.filter(s=>s!==e.value))}}),(0,a.jsx)("label",{htmlFor:e.value,className:"text-sm",children:e.label})]},e.value)})}),(0,a.jsx)(eW.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Settings"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(eW.zB,{control:c.control,name:"isActive",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eW.lR,{children:"Active"}),(0,a.jsx)(eW.Rr,{children:"Enable this report type for use"})]}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(A.S,{checked:s.value,onCheckedChange:s.onChange})})]})}}),(0,a.jsx)(eW.zB,{control:c.control,name:"isPublic",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{className:"flex flex-row items-center justify-between rounded-lg border p-3",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(eW.lR,{children:"Public"}),(0,a.jsx)(eW.Rr,{children:"Make available to all users"})]}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(A.S,{checked:s.value,onCheckedChange:s.onChange})})]})}})]}),(0,a.jsx)(eW.zB,{control:c.control,name:"refreshInterval",render:e=>{let{field:s}=e;return(0,a.jsxs)(eW.eI,{children:[(0,a.jsx)(eW.lR,{children:"Refresh Interval (minutes)"}),(0,a.jsx)(eW.MJ,{children:(0,a.jsx)(D.p,{type:"number",min:"1",max:"1440",placeholder:"60",...s,onChange:e=>s.onChange(parseInt(e.target.value)||60)})}),(0,a.jsx)(eW.Rr,{children:"How often the report data should refresh (1-1440 minutes)"}),(0,a.jsx)(eW.C5,{})]})}})]}),(0,a.jsxs)(eY.Es,{children:[(0,a.jsx)(y.$,{type:"button",variant:"outline",onClick:i,children:"Cancel"}),(0,a.jsx)(y.$,{type:"submit",disabled:n,children:n?"Saving...":l?"Update":"Create"})]})]})})]})})};var sa=t(44838),sl=t(3561),sr=t(13300),si=t(18763),sn=t(15599),sc=t(53764),so=t(37648);let sd=e=>{switch(e.toLowerCase()){case"operational":return"bg-blue-100 text-blue-800";case"performance":return"bg-green-100 text-green-800";case"financial":return"bg-yellow-100 text-yellow-800";case"compliance":return"bg-red-100 text-red-800";case"analytics":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},sm=e=>{switch(e.toLowerCase()){case"delegations":case"tasks":case"vehicles":default:return(0,a.jsx)(d.A,{className:"h-4 w-4"});case"employees":return(0,a.jsx)(c.A,{className:"h-4 w-4"})}},sx=e=>{var s,t,l,r;let{reportType:i,onSelect:n,onEdit:c,onDelete:o,onDuplicate:d,onToggleActive:m,className:x="",showActions:u=!0}=e,h=(e,s)=>{e.stopPropagation(),s()};return(0,a.jsxs)(N.Zp,{className:(0,en.cn)("cursor-pointer transition-all duration-200 hover:shadow-md",!i.isActive&&"opacity-60",x),onClick:()=>{n&&n(i)},children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)(N.ZB,{className:"text-lg flex items-center gap-2",children:[sm(i.dataSource),i.name]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,a.jsx)(v.E,{className:(0,en.cn)("text-xs",sd(i.category)),children:i.category}),!i.isActive&&(0,a.jsx)(v.E,{variant:"secondary",className:"text-xs",children:"Inactive"}),i.isPublic&&(0,a.jsxs)(v.E,{variant:"outline",className:"text-xs",children:[(0,a.jsx)(eb.A,{className:"h-3 w-3 mr-1"}),"Public"]})]})]}),u&&(0,a.jsxs)(sa.rI,{children:[(0,a.jsx)(sa.ty,{asChild:!0,children:(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:e=>e.stopPropagation(),children:(0,a.jsx)(sl.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(sa.SQ,{align:"end",children:[n&&(0,a.jsxs)(sa._2,{onClick:e=>h(e,()=>n(i)),children:[(0,a.jsx)(sr.A,{className:"h-4 w-4 mr-2"}),"Use Report Type"]}),c&&(0,a.jsxs)(sa._2,{onClick:e=>h(e,()=>c(i)),children:[(0,a.jsx)(si.A,{className:"h-4 w-4 mr-2"}),"Edit"]}),d&&(0,a.jsxs)(sa._2,{onClick:e=>h(e,()=>d(i)),children:[(0,a.jsx)(sn.A,{className:"h-4 w-4 mr-2"}),"Duplicate"]}),m&&(0,a.jsx)(sa._2,{onClick:e=>h(e,()=>m(i)),children:i.isActive?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(sc.A,{className:"h-4 w-4 mr-2"}),"Deactivate"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(sr.A,{className:"h-4 w-4 mr-2"}),"Activate"]})}),o&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(sa.mB,{}),(0,a.jsxs)(sa._2,{onClick:e=>h(e,()=>o(i)),className:"text-red-600",children:[(0,a.jsx)(eF.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[i.description&&(0,a.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:i.description}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium mb-2",children:["Widgets (",(null==(s=i.widgets)?void 0:s.length)||0,")"]}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[null==(t=i.widgets)?void 0:t.slice(0,3).map((e,s)=>(0,a.jsx)(v.E,{variant:"outline",className:"text-xs",children:e},s)),((null==(l=i.widgets)?void 0:l.length)||0)>3&&(0,a.jsxs)(v.E,{variant:"outline",className:"text-xs",children:["+",((null==(r=i.widgets)?void 0:r.length)||0)-3," more"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[sm(i.dataSource),(0,a.jsx)("span",{children:i.dataSource})]}),i.refreshInterval&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(so.A,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[i.refreshInterval,"m"]})]})]}),i.tags&&i.tags.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[i.tags.slice(0,3).map((e,s)=>(0,a.jsx)(v.E,{variant:"secondary",className:"text-xs",children:e},s)),i.tags.length>3&&(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["+",i.tags.length-3]})]}),(0,a.jsx)("div",{className:"pt-2 border-t text-xs text-gray-500",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{children:["Created: ",i.createdAt?(0,er.GP)(new Date(i.createdAt),"MMM dd, yyyy"):"Unknown"]}),i.updatedAt&&(0,a.jsxs)("span",{children:["Updated: ",(0,er.GP)(new Date(i.updatedAt),"MMM dd")]})]})})]})]})},su=e=>{let{className:s="",onReportTypeSelect:t,allowEdit:l=!0,allowDelete:r=!0}=e,[i,n]=(0,g.useState)(!1),[c,o]=(0,g.useState)(null),[x,u]=(0,g.useState)(""),{data:h,isLoading:p,error:j,createReportType:f,updateReportType:b,deleteReportType:w,duplicateReportType:k}=e3(),A=async e=>{try{let s={name:e.name,category:e.category,dataSource:e.dataSource,widgets:e.widgets,isActive:e.isActive,isPublic:e.isPublic,...e.description&&{description:e.description},...e.filters&&{filters:e.filters},...e.refreshInterval&&{refreshInterval:e.refreshInterval},...e.tags&&{tags:e.tags}};c?await b.mutateAsync({id:c.id,...s}):await f.mutateAsync(s),n(!1),o(null)}catch(e){console.error("Failed to save report type:",e)}},C=e=>{o(e),n(!0)},S=async e=>{if(window.confirm('Are you sure you want to delete "'.concat(e.name,'"?')))try{await w.mutateAsync(e.id)}catch(e){console.error("Failed to delete report type:",e)}},E=async e=>{try{await k.mutateAsync(e.id)}catch(e){console.error("Failed to duplicate report type:",e)}},D=(0,g.useMemo)(()=>(null==h?void 0:h.filter(e=>{var s;return e.name.toLowerCase().includes(x.toLowerCase())||(null==(s=e.description)?void 0:s.toLowerCase().includes(x.toLowerCase()))}))||[],[h,x]);return p?(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-5 w-5"}),"Report Type Manager"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]}):j?(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-5 w-5"}),"Report Type Manager"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:j})})]}):(0,a.jsxs)("div",{className:(0,en.cn)("space-y-6",s),children:[(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-5 w-5"}),"Report Type Manager"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:[D.length," types"]}),(0,a.jsxs)(y.$,{onClick:()=>n(!0),size:"sm",className:"flex items-center gap-2",children:[(0,a.jsx)(eP.A,{className:"h-4 w-4"}),"New Report Type"]})]})]})}),(0,a.jsxs)(N.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("input",{type:"text",placeholder:"Search report types...",value:x,onChange:e=>u(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})}),(0,a.jsx)(y.$,{variant:"outline",size:"sm",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})]}),0===D.length?(0,a.jsxs)("div",{className:"text-center py-12 text-gray-500",children:[(0,a.jsx)(d.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No Report Types Found"}),(0,a.jsx)("p",{className:"text-sm mb-4",children:x?"No report types match your search.":"Get started by creating your first report type."}),(0,a.jsxs)(y.$,{onClick:()=>n(!0),children:[(0,a.jsx)(eP.A,{className:"h-4 w-4 mr-2"}),"Create Report Type"]})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:D.map(e=>(0,a.jsx)(sx,{reportType:e,...t&&{onSelect:t},...l&&{onEdit:C},...r&&{onDelete:S},onDuplicate:E},e.id))})]})]}),i&&(0,a.jsx)(st,{reportType:c,onSubmit:A,onCancel:()=>{n(!1),o(null)},isLoading:f.isPending||b.isPending})]})};var sh=t(85127),sp=t(36268),sg=t(11032),sj=t(61051),sv=t(27150);let sy=e=>{var s,t,l;let{className:r="",showExportOptions:n=!0,maxRows:c=100}=e,[o,d]=g.useState([]),[m,x]=g.useState([]),[h,j]=g.useState({}),[f,b]=g.useState({}),{data:w,isLoading:k,error:A}=(0,sj.si)({staleTime:12e4}),C=(0,g.useMemo)(()=>w?w.slice(0,c).map(e=>{var s,t;return{id:e.id,title:e.description,status:e.status,priority:e.priority,assignedTo:(null==(s=e.staffEmployee)?void 0:s.name)||(null==(t=e.driverEmployee)?void 0:t.name)||"Unassigned",dueDate:e.deadline?(0,er.GP)(new Date(e.deadline),"yyyy-MM-dd"):"No deadline",createdAt:(0,er.GP)(new Date(e.createdAt),"yyyy-MM-dd"),completedAt:"Completed"===e.status?(0,er.GP)(new Date(e.updatedAt),"yyyy-MM-dd"):void 0,estimatedHours:e.estimatedDuration||0,actualHours:e.estimatedDuration||0}}):[],[w,c]),S=(0,g.useMemo)(()=>[{accessorKey:"title",header:e=>{let{column:s}=e;return(0,a.jsxs)(y.$,{variant:"ghost",onClick:()=>s.toggleSorting("asc"===s.getIsSorted()),className:"h-8 px-2",children:["Task Title",(0,a.jsx)(sv.A,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:s}=e;return(0,a.jsx)("div",{className:"font-medium",children:s.getValue("title")})}},{accessorKey:"status",header:e=>{let{column:s}=e;return(0,a.jsxs)(y.$,{variant:"ghost",onClick:()=>s.toggleSorting("asc"===s.getIsSorted()),className:"h-8 px-2",children:["Status",(0,a.jsx)(sv.A,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:s}=e,t=s.getValue("status");return(0,a.jsx)(v.E,{variant:sf(t),children:sb(t)})}},{accessorKey:"priority",header:e=>{let{column:s}=e;return(0,a.jsxs)(y.$,{variant:"ghost",onClick:()=>s.toggleSorting("asc"===s.getIsSorted()),className:"h-8 px-2",children:["Priority",(0,a.jsx)(sv.A,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:s}=e,t=s.getValue("priority");return(0,a.jsx)(v.E,{variant:sN(t),children:t})}},{accessorKey:"assignedTo",header:e=>{let{column:s}=e;return(0,a.jsxs)(y.$,{variant:"ghost",onClick:()=>s.toggleSorting("asc"===s.getIsSorted()),className:"h-8 px-2",children:["Assigned To",(0,a.jsx)(sv.A,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:s}=e;return(0,a.jsx)("div",{className:"text-sm",children:s.getValue("assignedTo")})}},{accessorKey:"dueDate",header:e=>{let{column:s}=e;return(0,a.jsxs)(y.$,{variant:"ghost",onClick:()=>s.toggleSorting("asc"===s.getIsSorted()),className:"h-8 px-2",children:["Due Date",(0,a.jsx)(sv.A,{className:"ml-2 h-4 w-4"})]})},cell:e=>{let{row:s}=e;return(0,a.jsx)("div",{className:"text-sm",children:s.getValue("dueDate")})}},{accessorKey:"estimatedHours",header:"Est. Hours",cell:e=>{let{row:s}=e;return(0,a.jsxs)("div",{className:"text-sm text-center",children:[s.getValue("estimatedHours"),"h"]})}},{accessorKey:"actualHours",header:"Actual Hours",cell:e=>{let{row:s}=e;return(0,a.jsxs)("div",{className:"text-sm text-center",children:[s.getValue("actualHours"),"h"]})}},{id:"actions",header:"Actions",cell:e=>{let{row:s}=e,t=s.original;return(0,a.jsxs)(sa.rI,{children:[(0,a.jsx)(sa.ty,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,a.jsx)(sl.A,{className:"h-4 w-4"})]})}),(0,a.jsxs)(sa.SQ,{align:"end",children:[(0,a.jsxs)(sa.hO,{onClick:()=>console.log("View task:",t.id),children:[(0,a.jsx)(eb.A,{className:"mr-2 h-4 w-4"}),"View Details"]}),(0,a.jsxs)(sa.hO,{onClick:()=>console.log("Edit task:",t.id),children:[(0,a.jsx)(si.A,{className:"mr-2 h-4 w-4"}),"Edit Task"]}),(0,a.jsx)(sa.hO,{onClick:()=>navigator.clipboard.writeText(t.id),children:"Copy Task ID"})]})]})}}],[]),E=(0,sp.N4)({data:C,columns:S,onSortingChange:d,onColumnFiltersChange:x,getCoreRowModel:(0,sg.HT)(),getPaginationRowModel:(0,sg.kW)(),getSortedRowModel:(0,sg.h5)(),getFilteredRowModel:(0,sg.hM)(),onColumnVisibilityChange:j,onRowSelectionChange:b,state:{sorting:o,columnFilters:m,columnVisibility:h,rowSelection:f}}),T=async()=>{try{if(!C||0===C.length)return void console.warn("No data to export");let e=C.map(e=>({"Task ID":e.id,Title:e.title,Status:e.status,Priority:e.priority,"Assigned To":e.assignedTo,"Due Date":e.dueDate,"Created Date":e.createdAt,"Completed Date":e.completedAt||"","Estimated Hours":e.estimatedHours||"","Actual Hours":e.actualHours||""}));if(0===e.length)return void console.warn("No data to export");let s=[Object.keys(e[0]).join(","),...e.map(e=>Object.values(e).join(","))].join("\n"),t=new Blob([s],{type:"text/csv"}),a=window.URL.createObjectURL(t),l=document.createElement("a");l.href=a,l.download="task-report-".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(a),document.body.removeChild(l)}catch(e){console.error("Export failed:",e)}};return k?(0,a.jsxs)(N.Zp,{className:r,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"h-5 w-5"}),"Task Report"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"h-8 bg-muted rounded animate-pulse"}),(0,a.jsx)("div",{className:"space-y-2",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,a.jsx)("div",{className:"h-12 bg-muted rounded animate-pulse"},s))})]})})]}):A?(0,a.jsxs)(N.Zp,{className:r,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"h-5 w-5"}),"Task Report"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(i.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"Failed to load task data"}),(0,a.jsx)("p",{className:"text-sm",children:A.message})]})})]}):(0,a.jsxs)(N.Zp,{className:r,children:[(0,a.jsxs)(N.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"h-5 w-5"}),"Task Report"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:[C.length," tasks found"]})]}),n&&(0,a.jsxs)(y.$,{variant:"outline",size:"sm",onClick:T,className:"h-8",children:[(0,a.jsx)(p.A,{className:"h-3 w-3 mr-1"}),"Export CSV"]})]}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(D.p,{placeholder:"Filter tasks...",value:null!=(l=null==(s=E.getColumn("title"))?void 0:s.getFilterValue())?l:"",onChange:e=>{var s;return null==(s=E.getColumn("title"))?void 0:s.setFilterValue(e.target.value)},className:"max-w-sm"})}),(0,a.jsxs)(sa.rI,{children:[(0,a.jsx)(sa.ty,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:"ml-auto",children:[(0,a.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Columns"]})}),(0,a.jsx)(sa.SQ,{align:"end",children:E.getAllColumns().filter(e=>e.getCanHide()).map(e=>(0,a.jsx)(sa.hO,{className:"capitalize",checked:e.getIsVisible(),onCheckedChange:s=>e.toggleVisibility(!!s),children:e.id},e.id))})]})]}),(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(sh.XI,{children:[(0,a.jsx)(sh.A0,{children:E.getHeaderGroups().map(e=>(0,a.jsx)(sh.Hj,{children:e.headers.map(e=>(0,a.jsx)(sh.nd,{children:e.isPlaceholder?null:(0,sp.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)(sh.BF,{children:(null==(t=E.getRowModel().rows)?void 0:t.length)?E.getRowModel().rows.map(e=>(0,a.jsx)(sh.Hj,{"data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,a.jsx)(sh.nA,{children:(0,sp.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,a.jsx)(sh.Hj,{children:(0,a.jsx)(sh.nA,{colSpan:S.length,className:"h-24 text-center",children:"No tasks found."})})})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-2 py-4",children:[(0,a.jsxs)("div",{className:"flex-1 text-sm text-muted-foreground",children:[E.getFilteredSelectedRowModel().rows.length," of"," ",E.getFilteredRowModel().rows.length," row(s) selected."]}),(0,a.jsxs)("div",{className:"space-x-2",children:[(0,a.jsx)(y.$,{variant:"outline",size:"sm",onClick:()=>E.previousPage(),disabled:!E.getCanPreviousPage(),children:"Previous"}),(0,a.jsx)(y.$,{variant:"outline",size:"sm",onClick:()=>E.nextPage(),disabled:!E.getCanNextPage(),children:"Next"})]})]})]})]})},sf=e=>{switch(e){case"Completed":return"default";case"In_Progress":return"secondary";case"Cancelled":return"destructive";default:return"outline"}},sN=e=>{switch(e){case"High":return"destructive";case"Medium":return"secondary";case"Low":return"outline";default:return"default"}},sb=e=>"In_Progress"===e?"In Progress":e;var sw=t(83540),sk=t(8782),sA=t(34e3),sC=t(54811),sS=t(94517);let sE=e=>({Planned:"#3b82f6",Confirmed:"#10b981",In_Progress:"#f59e0b",Completed:"#22c55e",Cancelled:"#ef4444",No_details:"#6b7280"})[e]||"#6b7280",sD=e=>{var s,t,a,l,r,i,n,c,o;let d={totalCount:e.totalCount||0,statusDistribution:(null==(s=e.statusDistribution)?void 0:s.map(e=>({status:e.status,count:e.count||0,percentage:e.percentage||0,color:sE(e.status)})))||[],trendData:(null==(t=e.trendData)?void 0:t.map(e=>({date:e.date,created:e.created||0,completed:e.completed||0,inProgress:e.inProgress||0})))||[],locationMetrics:e.locationMetrics||[],summary:{totalDelegations:(null==(a=e.summary)?void 0:a.totalDelegations)||0,activeDelegations:(null==(l=e.summary)?void 0:l.activeDelegations)||0,completedDelegations:(null==(r=e.summary)?void 0:r.completedDelegations)||0,totalDelegates:(null==(i=e.summary)?void 0:i.totalDelegates)||0,averageDuration:(null==(n=e.summary)?void 0:n.averageDuration)||0,completionRate:(null==(c=e.summary)?void 0:c.completionRate)||0},delegations:[]};return e.serviceHistory&&(d.serviceHistory=sM(e.serviceHistory)),e.serviceCosts&&"object"==typeof e.serviceCosts&&!Array.isArray(e.serviceCosts)&&(d.serviceCosts=sF(e.serviceCosts)),e.taskData&&"object"==typeof e.taskData&&!Array.isArray(e.taskData)&&(d.taskData=sP(e.taskData)),d.delegations=(null==(o=e.delegations)?void 0:o.map(e=>{var s,t,a,l,r,i;return{id:(e.id||0).toString(),delegationId:e.delegationId||(null==(s=e.id)?void 0:s.toString())||"",customerName:e.customerName||(null==(t=e.customer)?void 0:t.name)||"Unknown Customer",vehicleModel:e.vehicleModel||(null==(a=e.vehicle)?void 0:a.model)||"Unknown Vehicle",licensePlate:e.licensePlate||(null==(l=e.vehicle)?void 0:l.licensePlate)||"Unknown",status:e.status,assignedEmployee:(null==(r=e.driverEmployee)?void 0:r.name)||(null==(i=e.staffEmployee)?void 0:i.name)||"Unassigned",location:e.location||"",createdAt:e.createdAt||"",completedAt:e.completedAt}}))||[],d},sT=e=>{var s,t;return{totalTasks:e.totalTasks||0,completedTasks:e.completedTasks||0,pendingTasks:e.pendingTasks||0,inProgressTasks:e.inProgressTasks||0,overdueTasks:e.overdueTasks||0,averageCompletionTime:e.averageCompletionTime||0,tasksByPriority:(null==(s=e.tasksByPriority)?void 0:s.map(e=>({priority:e.priority,count:e.count||0})))||[],tasksByStatus:(null==(t=e.tasksByStatus)?void 0:t.map(e=>({status:e.status,count:e.count||0})))||[]}},sR=e=>(null==e?void 0:e.map(e=>({date:e.date,created:e.created||0,completed:e.completed||0,inProgress:e.inProgress||0})))||[],sz=e=>(null==e?void 0:e.map(e=>({location:e.location||"",delegationCount:e.delegationCount||e.delegationsCount||e.count||0,averageDuration:e.averageDuration||0,completionRate:e.completionRate||0})))||[],sM=e=>({id:e.id||"",vehicleId:e.vehicleId||0,vehicleName:e.vehicleName||"",serviceType:e.serviceType,status:e.status,scheduledDate:e.scheduledDate||"",completedDate:e.completedDate,cost:e.cost||0,description:e.description||"",relatedDelegationId:e.relatedDelegationId,relatedTaskId:e.relatedTaskId}),sF=e=>{var s,t;return{totalCost:e.totalCost||0,averageCostPerService:e.averageCostPerService||0,costByType:(null==(s=e.costByType)?void 0:s.map(e=>({type:e.type,cost:e.cost||0,count:e.count||0})))||[],monthlyTrend:(null==(t=e.monthlyTrend)?void 0:t.map(e=>({month:e.month||"",cost:e.cost||0})))||[]}},sP=e=>{var s;return{totalTasks:e.totalTasks||0,completedTasks:e.completedTasks||0,pendingTasks:e.pendingTasks||0,overdueTasks:e.overdueTasks||0,averageCompletionTime:e.averageCompletionTime||0,tasksByPriority:(null==(s=e.tasksByPriority)?void 0:s.map(e=>({priority:e.priority,count:e.count||0})))||[]}};class sI{async getCrossEntityAnalytics(e){try{let s=this.buildQueryParams(e),t=new URLSearchParams(s);e.includeCrossEntityCorrelations&&t.append("includeCrossEntityCorrelations","true");let a=await e5.uE.get("/reporting/cross-entity/analytics?".concat(t.toString()));return a.data||a}catch(e){throw console.error("Error fetching cross-entity analytics:",e),Error("Failed to load cross-entity analytics: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getDelegationAnalytics(e){try{let s=this.buildQueryParams(e),t=new URLSearchParams(s);e.includeServiceHistory&&t.append("includeServiceHistory","true"),e.includeTaskData&&t.append("includeTaskData","true");let a=await e5.uE.get("/reporting/delegations/analytics?".concat(t.toString()));return sD(a.data||a)}catch(e){throw console.error("Error fetching delegation analytics:",e),Error("Failed to load delegation analytics: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getDelegations(e,s){try{let t=this.buildQueryParams(e),a=new URLSearchParams(t);a.append("page",s.page.toString()),a.append("pageSize",s.pageSize.toString());let l=await e5.uE.get("/reporting/delegations?".concat(a.toString()));return l.data||l}catch(e){throw console.error("Error fetching delegations:",e),Error("Failed to load delegations: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getEmployeeAnalytics(e){try{let s=this.buildQueryParams(e),t=await e5.uE.get("/reporting/employee/analytics?".concat(s));return t.data||t}catch(e){throw console.error("Error fetching employee analytics:",e),Error("Failed to load employee analytics: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getLocationMetrics(e){try{let s=this.buildQueryParams(e),t=await e5.uE.get("/reporting/locations/metrics?".concat(s));return sz(t.data||t)}catch(e){throw console.error("Error fetching location metrics:",e),Error("Failed to load location metrics: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getServiceCostSummary(e){try{let s=this.buildQueryParams(e);return(await e5.uE.get("/reporting/services/costs?".concat(s))).data||{averageCostPerService:0,costByType:[],monthlyTrend:[],totalCost:0}}catch(e){throw console.error("Error fetching service costs:",e),Error("Failed to load service costs: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getServiceHistory(e){try{let s=this.buildQueryParams(e);return(await e5.uE.get("/reporting/services/history?".concat(s))).data||[]}catch(e){throw console.error("Error fetching service history:",e),Error("Failed to load service history: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getTaskAnalytics(e){try{let s=this.buildQueryParams(e),t=new URLSearchParams(s);e.taskStatus&&e.taskStatus.length>0&&t.append("taskStatus",e.taskStatus.join(",")),e.taskPriority&&e.taskPriority.length>0&&t.append("taskPriority",e.taskPriority.join(","));let a=await e5.uE.get("/reporting/tasks/analytics?".concat(t.toString()));return a.data||a}catch(e){throw console.error("Error fetching task analytics:",e),Error("Failed to load task analytics: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getTaskMetrics(e){try{let s=(null==e?void 0:e.length)?"delegationIds=".concat(e.join(",")):"",t=await e5.uE.get("/reporting/tasks/metrics?".concat(s));return sT(t.data||t)}catch(e){throw console.error("Error fetching task metrics:",e),Error("Failed to load task metrics: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getTrendData(e){try{let s=this.buildQueryParams(e),t=await e5.uE.get("/reporting/trends?".concat(s));return sR(t.data||t)}catch(e){throw console.error("Error fetching trend data:",e),Error("Failed to load trend data: ".concat(e instanceof Error?e.message:"Unknown error"))}}async getVehicleAnalytics(e){try{let s=this.buildQueryParams(e),t=new URLSearchParams(s);e.vehicles&&e.vehicles.length>0&&t.append("vehicles",e.vehicles.join(",")),e.serviceTypes&&e.serviceTypes.length>0&&t.append("serviceTypes",e.serviceTypes.join(",")),e.serviceStatus&&e.serviceStatus.length>0&&t.append("serviceStatus",e.serviceStatus.join(","));let a="/reporting/vehicles/analytics".concat(t.toString()?"?".concat(t.toString()):""),l=await e5.uE.get(a);return l.data||l}catch(e){throw console.error("Error fetching vehicle analytics:",e),e}}appendArrayParams(e,s,t){t&&t.length>0&&e.append(s,t.join(","))}buildQueryParams(e){let s=new URLSearchParams;try{let t=e.dateRange.from instanceof Date?e.dateRange.from:new Date(e.dateRange.from),a=e.dateRange.to instanceof Date?e.dateRange.to:new Date(e.dateRange.to);if(isNaN(t.getTime())||isNaN(a.getTime()))throw TypeError("Invalid date range provided");s.append("dateRange.from",t.toISOString().split("T")[0]||t.toISOString()),s.append("dateRange.to",a.toISOString().split("T")[0]||a.toISOString())}catch(a){console.error("Error processing date range:",a);let e=new Date,t=new Date(Date.now()-2592e6);s.append("dateRange.from",t.toISOString().split("T")[0]||t.toISOString()),s.append("dateRange.to",e.toISOString().split("T")[0]||e.toISOString())}return this.appendArrayParams(s,"status",e.status),this.appendArrayParams(s,"locations",e.locations),this.appendArrayParams(s,"employees",e.employees),this.appendArrayParams(s,"vehicles",e.vehicles),e.taskStatus&&this.appendArrayParams(s,"taskStatus",e.taskStatus),e.taskPriority&&this.appendArrayParams(s,"taskPriority",e.taskPriority),e.serviceTypes&&this.appendArrayParams(s,"serviceTypes",e.serviceTypes),e.serviceStatus&&this.appendArrayParams(s,"serviceStatus",e.serviceStatus),e.costRange&&(s.append("minCost",e.costRange.min.toString()),s.append("maxCost",e.costRange.max.toString())),s.toString()}constructor(e="/api/reporting"){this.baseUrl=e}}let sL=new sI;var sO=t(14056);let sV=(e,s,t)=>{var a;let l=(0,sO.Sk)(["delegations",e,s],()=>sL.getDelegationAnalytics(e),{placeholderData:e=>e,showErrorToast:!0,...t}),r=(0,g.useMemo)(()=>{var e;if(!(null==(e=l.data)?void 0:e.delegations))return;let t=l.data.delegations,a=(s.page-1)*s.pageSize,r=a+s.pageSize;return{data:t.slice(a,r).map(e=>({id:e.id,delegationId:e.id.toString(),customerName:e.title,vehicleModel:"N/A",licensePlate:"N/A",status:e.status,assignedEmployee:e.assignedTo,location:e.location,createdAt:e.createdAt,completedAt:e.completedAt||null})),meta:{total:t.length,page:s.page,pageSize:s.pageSize,totalPages:Math.ceil(t.length/s.pageSize)}}},[null==(a=l.data)?void 0:a.delegations,s.page,s.pageSize]);return{...l,data:r}};t(38549);var sB=t(28755);let sY={all:["reporting"],analytics:()=>[...sY.all,"analytics"],delegationAnalytics:e=>[...sY.analytics(),"delegations",e],taskMetrics:e=>[...sY.all,"tasks","metrics",e],trends:e=>[...sY.all,"trends",e],locationMetrics:e=>[...sY.all,"locations","metrics",e],serviceHistory:e=>[...sY.all,"services","history",e],serviceCosts:e=>[...sY.all,"services","costs",e],taskAnalytics:e=>[...sY.analytics(),"tasks",e],vehicleAnalytics:e=>[...sY.analytics(),"vehicles",e],employeeAnalytics:e=>[...sY.analytics(),"employees",e],crossEntityAnalytics:e=>[...sY.analytics(),"cross-entity",e]},sW=(e,s)=>{let t=(0,g.useMemo)(()=>sY.delegationAnalytics(e),[e]),a=(0,g.useCallback)(()=>sL.getDelegationAnalytics(e),[e]);return(0,sB.I)({queryKey:t,queryFn:a,staleTime:3e5,gcTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnMount:!0,...s})},sU=(e,s)=>{let t=(0,g.useMemo)(()=>sY.trends(e),[e]),a=(0,g.useCallback)(()=>sL.getTrendData(e),[e]);return(0,sB.I)({queryKey:t,queryFn:a,staleTime:3e5,gcTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,...s})},sZ=(e,s)=>{let t=(0,g.useMemo)(()=>sY.locationMetrics(e),[e]),a=(0,g.useCallback)(()=>sL.getLocationMetrics(e),[e]);return(0,sB.I)({queryKey:t,queryFn:a,staleTime:3e5,gcTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,...s})},s$=(e,s)=>{let t=(0,g.useMemo)(()=>sY.taskAnalytics(e),[e]),a=(0,g.useCallback)(()=>sL.getTaskAnalytics(e),[e]);return(0,sB.I)({queryKey:t,queryFn:a,staleTime:18e4,gcTime:48e4,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,enabled:!!e.includeTaskData,...s})};var s_=t(65453),sG=t(46786),sJ=t(21603);let sq=()=>{let e=new Date;return{costRange:{max:1e4,min:0},dateRange:{from:new Date(e.getTime()-2592e6),to:e},employees:[],includeServiceHistory:!1,includeTaskData:!1,locations:[],serviceStatus:[],serviceTypes:[],status:[],vehicles:[]}};new Date().getTime();let sK=e=>{let s={};return e.dateRange&&(e.dateRange.from>e.dateRange.to&&(s.dateRange="Start date must be before end date"),Math.abs(e.dateRange.to.getTime()-e.dateRange.from.getTime())/864e5>365&&(s.dateRange="Date range cannot exceed 365 days")),e.status.length>10&&(s.status="Too many statuses selected (maximum 10)"),e.locations.length>50&&(s.locations="Too many locations selected (maximum 50)"),e.employees.length>100&&(s.employees="Too many employees selected (maximum 100)"),e.vehicles.length>100&&(s.vehicles="Too many vehicles selected (maximum 100)"),e.serviceTypes&&e.serviceTypes.length>20&&(s.serviceTypes="Too many service types selected (maximum 20)"),e.serviceStatus&&e.serviceStatus.length>10&&(s.serviceStatus="Too many service statuses selected (maximum 10)"),e.costRange&&(e.costRange.min<0&&(s.costRange="Minimum cost cannot be negative"),e.costRange.min>=e.costRange.max&&(s.costRange="Minimum cost must be less than maximum cost"),e.costRange.max>1e6&&(s.costRange="Maximum cost cannot exceed $1,000,000")),s},sQ=(0,s_.v)()((0,sG.lt)((0,sG.eh)((0,sG.Zr)((e,s)=>({applyFilters:()=>{let{filters:t,isValid:a}=s();a&&e({hasUnsavedChanges:!1,lastAppliedFilters:{...t}})},applyPreset:s=>{try{let t=localStorage.getItem("reporting-filter-presets"),a=(t?JSON.parse(t):{})[s];a&&e(e=>({filters:{...a},hasUnsavedChanges:!0,lastAppliedFilters:e.lastAppliedFilters}))}catch(e){console.error("Failed to apply preset:",e)}},clearValidationErrors:()=>{e({isValid:!0,validationErrors:{}})},deletePreset:e=>{try{let s=localStorage.getItem("reporting-filter-presets"),t=s?JSON.parse(s):{};delete t[e],localStorage.setItem("reporting-filter-presets",JSON.stringify(t))}catch(e){console.error("Failed to delete preset:",e)}},filters:sq(),getPresets:()=>{try{let e=localStorage.getItem("reporting-filter-presets");return e?JSON.parse(e):{}}catch(e){return{}}},hasUnsavedChanges:!1,isFilterPanelOpen:!1,isValid:!0,lastAppliedFilters:sq(),resetFilters:()=>{e({filters:sq(),hasUnsavedChanges:!0,isValid:!0,validationErrors:{}})},revertChanges:()=>{let{lastAppliedFilters:t}=s();e({filters:{...t},hasUnsavedChanges:!1,isValid:!0,validationErrors:{}})},saveAsPreset:e=>{try{let{filters:t}=s(),a=localStorage.getItem("reporting-filter-presets"),l=a?JSON.parse(a):{};l[e]={...t},localStorage.setItem("reporting-filter-presets",JSON.stringify(l))}catch(e){console.error("Failed to save preset:",e)}},setCostRange:(s,t)=>{e(e=>{let a={...e.filters,costRange:{max:t,min:s}},l=sK(a);return{filters:a,hasUnsavedChanges:!0,isValid:0===Object.keys(l).length,validationErrors:l}})},setDateRange:(s,t)=>{e(e=>{let a={...e.filters,dateRange:{from:s,to:t}},l=sK(a);return{filters:a,hasUnsavedChanges:!0,isValid:0===Object.keys(l).length,validationErrors:l}})},setEmployees:s=>{e(e=>{let t={...e.filters,employees:s},a=sK(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setFilterPanelOpen:s=>{e({isFilterPanelOpen:s})},setFilters:s=>{e(e=>{let t={...e.filters,...s},a=sK(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setIncludeServiceHistory:s=>{e(e=>{let t={...e.filters,includeServiceHistory:s},a=sK(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setIncludeTaskData:s=>{e(e=>{let t={...e.filters,includeTaskData:s},a=sK(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setLocations:s=>{e(e=>{let t={...e.filters,locations:s},a=sK(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setServiceStatus:s=>{e(e=>{let t={...e.filters,serviceStatus:s},a=sK(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setServiceTypes:s=>{e(e=>{let t={...e.filters,serviceTypes:s},a=sK(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setStatus:s=>{e(e=>{let t={...e.filters,status:s},a=sK(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setTaskPriorities:s=>{e(e=>{let t={...e.filters,taskPriorities:s},a=sK(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setTaskStatus:s=>{e(e=>{let t={...e.filters,taskStatus:s},a=sK(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},setVehicles:s=>{e(e=>{let t={...e.filters,vehicles:s},a=sK(t);return{filters:t,hasUnsavedChanges:!0,isValid:0===Object.keys(a).length,validationErrors:a}})},toggleFilterPanel:()=>{e(e=>({isFilterPanelOpen:!e.isFilterPanelOpen}))},validateFilters:()=>{let{filters:t}=s(),a=sK(t),l=0===Object.keys(a).length;return e({isValid:l,validationErrors:a}),l},validationErrors:{}}),{name:"reporting-filters-storage",partialize:e=>({filters:e.filters,lastAppliedFilters:e.lastAppliedFilters}),storage:(0,sG.KU)(()=>localStorage)})),{name:"reporting-filters-store"})),sH=e=>e.filters,sX=e=>({applyFilters:e.applyFilters,resetFilters:e.resetFilters,revertChanges:e.revertChanges,setCostRange:e.setCostRange,setDateRange:e.setDateRange,setEmployees:e.setEmployees,setFilters:e.setFilters,setIncludeServiceHistory:e.setIncludeServiceHistory,setIncludeTaskData:e.setIncludeTaskData,setLocations:e.setLocations,setServiceStatus:e.setServiceStatus,setServiceTypes:e.setServiceTypes,setStatus:e.setStatus,setTaskPriorities:e.setTaskPriorities,setTaskStatus:e.setTaskStatus,setVehicles:e.setVehicles}),s0=e=>({hasUnsavedChanges:e.hasUnsavedChanges,isFilterPanelOpen:e.isFilterPanelOpen,setFilterPanelOpen:e.setFilterPanelOpen,toggleFilterPanel:e.toggleFilterPanel}),s1=e=>({clearValidationErrors:e.clearValidationErrors,isValid:e.isValid,validateFilters:e.validateFilters,validationErrors:e.validationErrors}),s2=e=>({applyPreset:e.applyPreset,deletePreset:e.deletePreset,getPresets:e.getPresets,saveAsPreset:e.saveAsPreset}),s4=()=>sQ((0,sJ.k)(sH)),s5=()=>sQ((0,sJ.k)(sX)),s3=()=>sQ((0,sJ.k)(s0)),s6=()=>sQ((0,sJ.k)(s1)),s8=()=>sQ((0,sJ.k)(s2));var s7=t(68856);let s9=e=>{let{payload:s}=e;return s&&0!==s.length?(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-4 mt-6 pt-4 border-t border-border",children:s.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)("div",{className:"w-4 h-4 rounded-full border border-gray-300",style:{backgroundColor:e.color}}),(0,a.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:e.value}),e.payload&&(0,a.jsxs)("span",{className:"text-muted-foreground",children:["(",e.payload.count,")"]})]},"legend-".concat(s)))}):null},te=e=>{let{active:s,payload:t}=e;if(s&&t&&t.length){let e=t[0],s=t[0].payload.total||100,l=s>0?Math.round(e.value/s*100):0;return(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-2",children:e.name}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center gap-4",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Count:"}),(0,a.jsx)("span",{className:"font-bold text-lg",style:{color:e.color},children:e.value})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center gap-4",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Percentage:"}),(0,a.jsxs)("span",{className:"font-medium text-primary",children:[l,"%"]})]})]})]})}return null},ts=()=>{var e;let{data:s,isLoading:t,error:r}=sW(s4());if(t)return(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(s7.E,{className:"h-7 w-48"})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(s7.E,{className:"h-[400px] w-full rounded-lg"})})]});if(r)return(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3",children:[(0,a.jsx)(l.A,{className:"h-6 w-6"}),"Delegation Status"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)($.Fc,{variant:"destructive",children:[(0,a.jsx)($.XL,{children:"Error"}),(0,a.jsx)($.TN,{children:r.message})]})})]});let i=null!=(e=null==s?void 0:s.statusDistribution)?e:[],n=i.reduce((e,s)=>e+s.count,0),c=i.map(e=>({...e,total:n})),o=["#22c55e","#f59e0b","#ef4444","#3b82f6","#8b5cf6"],d=c.map((e,s)=>({...e,color:e.color||o[s%o.length]}));return(0,a.jsxs)(N.Zp,{children:[(0,a.jsxs)(N.aR,{className:"pb-6",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3 text-xl",children:[(0,a.jsx)(l.A,{className:"h-6 w-6 text-primary"}),"Delegation Status"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,a.jsx)(J.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Distribution of delegation statuses"})]})]}),(0,a.jsxs)(N.Wu,{className:"pt-6",children:[(0,a.jsx)(sw.u,{width:"100%",height:300,children:(0,a.jsxs)(sk.r,{children:[(0,a.jsx)(sA.F,{data:d,cx:"50%",cy:"50%",labelLine:!1,outerRadius:100,innerRadius:40,fill:"#8884d8",dataKey:"count",nameKey:"status",stroke:"#fff",strokeWidth:2,children:d.map((e,s)=>(0,a.jsx)(sC.f,{fill:e.color},"cell-".concat(s)))}),(0,a.jsx)(sS.m,{content:(0,a.jsx)(te,{})})]})}),(0,a.jsx)(s9,{payload:d.map(e=>({value:e.status,color:e.color,payload:e}))}),n>0&&(0,a.jsxs)("div",{className:"mt-6 pt-4 border-t border-border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Total Delegations:"}),(0,a.jsx)("span",{className:"font-semibold text-primary",children:n})]}),d.length>0&&d[0]&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm mt-2",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Most Common Status:"}),(0,a.jsx)("span",{className:"font-semibold",style:{color:d[0].color},children:d[0].status})]})]})]})]})};var tt=t(93504),ta=t(94754),tl=t(96025),tr=t(16238),ti=t(24026),tn=t(21374);let tc=()=>{let{data:e,isLoading:s,error:t}=sU(s4());return s?(0,a.jsx)(s7.E,{className:"h-[350px] w-full"}):t?(0,a.jsxs)($.Fc,{variant:"destructive",children:[(0,a.jsx)($.XL,{children:"Error"}),(0,a.jsx)($.TN,{children:t.message})]}):(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(r.A,{className:"h-5 w-5"}),"Delegation Trends"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(sw.u,{width:"100%",height:300,children:(0,a.jsxs)(tt.b,{data:e||[],children:[(0,a.jsx)(ta.d,{strokeDasharray:"3 3"}),(0,a.jsx)(tl.W,{dataKey:"date"}),(0,a.jsx)(tr.h,{}),(0,a.jsx)(sS.m,{}),(0,a.jsx)(ti.s,{}),(0,a.jsx)(tn.N,{type:"monotone",dataKey:"created",stroke:"#8884d8",name:"Created"}),(0,a.jsx)(tn.N,{type:"monotone",dataKey:"completed",stroke:"#82ca9d",name:"Completed"}),(0,a.jsx)(tn.N,{type:"monotone",dataKey:"inProgress",stroke:"#ffc658",name:"In Progress"})]})})})]})};var to=t(3401),td=t(83394),tm=t(83662);let tx=e=>{let{active:s,payload:t,label:l}=e;if(s&&t&&t.length){let e=t[0].payload,s=t[0].payload.total||100,r=s>0?Math.round(e.delegationCount/s*100):0;return(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-3",children:l}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center gap-6",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Delegations:"}),(0,a.jsx)("span",{className:"font-bold text-blue-600 dark:text-blue-400 text-lg",children:e.delegationCount})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center gap-6",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Percentage:"}),(0,a.jsxs)("span",{className:"font-medium text-emerald-600 dark:text-emerald-400",children:[r,"%"]})]}),void 0!==e.completionRate&&(0,a.jsxs)("div",{className:"flex justify-between items-center gap-6",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Completion Rate:"}),(0,a.jsxs)("span",{className:"font-medium text-green-600 dark:text-green-400",children:[e.completionRate,"%"]})]}),e.averageResponseTime&&(0,a.jsxs)("div",{className:"flex justify-between items-center gap-6",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Avg Response:"}),(0,a.jsxs)("span",{className:"font-medium text-yellow-600 dark:text-yellow-400",children:[e.averageResponseTime,"h"]})]})]})]})}return null},tu=()=>{let{data:e,isLoading:s,error:t}=sZ(s4());if(s)return(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsx)(s7.E,{className:"h-7 w-48"})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(s7.E,{className:"h-[350px] w-full rounded-lg"})})]});if(t)return(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3",children:[(0,a.jsx)(tm.A,{className:"h-6 w-6"}),"Location Distribution"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsxs)($.Fc,{variant:"destructive",children:[(0,a.jsx)($.XL,{children:"Error"}),(0,a.jsx)($.TN,{children:t.message})]})})]});let l=(null==e?void 0:e.reduce((e,s)=>e+s.delegationCount,0))||0,i=(null==e?void 0:e.map(e=>({...e,total:l})))||[];return(0,a.jsxs)(N.Zp,{children:[(0,a.jsxs)(N.aR,{className:"pb-6",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3 text-xl",children:[(0,a.jsx)(tm.A,{className:"h-6 w-6 text-primary"}),"Location Distribution"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,a.jsx)(r.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Delegation distribution across locations"})]})]}),(0,a.jsxs)(N.Wu,{className:"pt-6",children:[(0,a.jsx)(sw.u,{width:"100%",height:350,children:(0,a.jsxs)(to.E,{data:i,layout:"vertical",margin:{top:8,right:40,left:20,bottom:8},children:[(0,a.jsx)(ta.d,{strokeDasharray:"3 3",stroke:"#f1f5f9",horizontal:!0,vertical:!1}),(0,a.jsx)(tl.W,{type:"number",fontSize:12,tick:{fill:"#64748b"},axisLine:{stroke:"#e2e8f0"},tickLine:{stroke:"#e2e8f0"}}),(0,a.jsx)(tr.h,{type:"category",dataKey:"location",width:140,fontSize:12,tick:{fill:"#374151",fontSize:12},axisLine:{stroke:"#e2e8f0"},tickLine:{stroke:"#e2e8f0"},orientation:"left"}),(0,a.jsx)(sS.m,{content:(0,a.jsx)(tx,{})}),(0,a.jsx)(td.y,{dataKey:"delegationCount",fill:"#3b82f6",name:"Delegations",radius:[0,6,6,0],stroke:"#2563eb",strokeWidth:1})]})}),l>0&&(0,a.jsxs)("div",{className:"mt-6 pt-4 border-t border-border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Total Delegations:"}),(0,a.jsx)("span",{className:"font-semibold text-primary",children:l})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm mt-2",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Active Locations:"}),(0,a.jsx)("span",{className:"font-semibold text-primary",children:i.length})]})]})]})]})},th=e=>{let{className:s="",data:t=[],height:r=300,interactive:i=!0,showLegend:n=!0}=e,c=(0,g.useMemo)(()=>t.map(e=>({color:e.color||tg(e.status),name:tp(e.status),percentage:Math.round(e.percentage),status:e.status,value:e.count})),[t]),o=(0,g.useMemo)(()=>c.reduce((e,s)=>e+s.value,0),[c]);return t&&0!==t.length?(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsxs)(N.aR,{children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"size-5"}),"Task Status Distribution"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Total Tasks: ",o]})]}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(sw.u,{height:r,width:"100%",children:(0,a.jsxs)(sk.r,{children:[(0,a.jsx)(sA.F,{cx:"50%",cy:"50%",data:c,dataKey:"value",fill:"#8884d8",label:e=>{let{name:s,percentage:t}=e;return"".concat(s,": ").concat(t,"%")},labelLine:!1,outerRadius:Math.min(.3*r,100),children:c.map((e,s)=>(0,a.jsx)(sC.f,{fill:e.color},"cell-".concat(s)))}),i&&(0,a.jsx)(sS.m,{content:(0,a.jsx)(e=>{let{active:s,payload:t}=e;if(s&&(null==t?void 0:t.length)){let e=t[0].payload;return(0,a.jsxs)("div",{className:"rounded-lg border bg-white p-3 shadow-lg dark:bg-gray-800",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Count: ",(0,a.jsx)("span",{className:"font-medium",children:e.value})]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Percentage: ",(0,a.jsxs)("span",{className:"font-medium",children:[e.percentage,"%"]})]})]})}return null},{})}),n&&(0,a.jsx)(ti.s,{content:(0,a.jsx)(e=>{let{payload:s}=e;return n&&s?(0,a.jsx)("div",{className:"mt-4 flex flex-wrap justify-center gap-4",children:s.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"size-3 rounded-full",style:{backgroundColor:e.color}}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.value," (",e.payload.percentage,"%)"]})]},s))}):null},{})})]})})})]}):(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"size-5"}),"Task Status Distribution"]})}),(0,a.jsx)(N.Wu,{className:"flex items-center justify-center",style:{height:r},children:(0,a.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,a.jsx)(l.A,{className:"mx-auto mb-2 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No task status data available"})]})})]})},tp=e=>({Assigned:"Assigned",Cancelled:"Cancelled",Completed:"Completed",In_Progress:"In Progress",Pending:"Pending"})[e]||e,tg=e=>({Assigned:"#3b82f6",Cancelled:"#ef4444",Completed:"#10b981",In_Progress:"#8b5cf6",Pending:"#f59e0b"})[e]||"#6b7280";var tj=t(27300);let tv=e=>{let{data:s=[],className:t="",showLegend:l=!0,interactive:r=!0,height:i=300}=e,n=(0,g.useMemo)(()=>s.map(e=>({name:ty(e.priority),value:e.count,color:e.color||tf(e.priority),percentage:Math.round(e.percentage),priority:e.priority})),[s]),c=(0,g.useMemo)(()=>n.reduce((e,s)=>e+s.value,0),[n]);return s&&0!==s.length?(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsxs)(N.aR,{children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(tj.A,{className:"h-5 w-5"}),"Task Priority Distribution"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Total Tasks: ",c]})]}),(0,a.jsxs)(N.Wu,{children:[(0,a.jsx)(sw.u,{width:"100%",height:i,children:(0,a.jsxs)(to.E,{data:n,margin:{top:20,right:30,left:20,bottom:5},children:[(0,a.jsx)(ta.d,{strokeDasharray:"3 3",className:"opacity-30"}),(0,a.jsx)(tl.W,{dataKey:"name",tick:{fontSize:12},className:"text-muted-foreground"}),(0,a.jsx)(tr.h,{tick:{fontSize:12},className:"text-muted-foreground"}),r&&(0,a.jsx)(sS.m,{content:(0,a.jsx)(e=>{let{active:s,payload:t,label:l}=e;if(s&&t&&t.length){let e=t[0].payload;return(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-3 border rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:l}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Count: ",(0,a.jsx)("span",{className:"font-medium",children:e.value})]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Percentage: ",(0,a.jsxs)("span",{className:"font-medium",children:[e.percentage,"%"]})]})]})}return null},{})}),(0,a.jsx)(td.y,{dataKey:"value",radius:[4,4,0,0],fill:"#8884d8",children:n.map((e,s)=>(0,a.jsx)(sC.f,{fill:e.color},"cell-".concat(s)))})]})}),l&&(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-4 mt-4",children:n.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded",style:{backgroundColor:e.color}}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.name,": ",e.value," (",e.percentage,"%)"]})]},s))})]})]}):(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(tj.A,{className:"h-5 w-5"}),"Task Priority Distribution"]})}),(0,a.jsx)(N.Wu,{className:"flex items-center justify-center",style:{height:i},children:(0,a.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,a.jsx)(tj.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No task priority data available"})]})})]})},ty=e=>({Low:"Low",Medium:"Medium",High:"High"})[e]||e,tf=e=>({Low:"#10b981",Medium:"#f59e0b",High:"#ef4444"})[e]||"#6b7280";var tN=t(24944);let tb=e=>{let{data:s=[],className:t="",showExportOptions:l=!0,maxDisplayItems:i=10}=e,n=(0,g.useMemo)(()=>s.sort((e,s)=>s.completionRate-e.completionRate).slice(0,i),[s,i]),o=(0,g.useMemo)(()=>{if(!s||0===s.length)return{totalEmployees:0,totalAssignedTasks:0,totalCompletedTasks:0,averageCompletionRate:0,averageCompletionTime:0};let e=s.reduce((e,s)=>e+s.assignedTasks,0),t=s.reduce((e,s)=>e+s.completedTasks,0),a=s.reduce((e,s)=>e+s.completionRate,0)/s.length,l=s.reduce((e,s)=>e+s.averageCompletionTime,0)/s.length;return{totalEmployees:s.length,totalAssignedTasks:e,totalCompletedTasks:t,averageCompletionRate:Math.round(a),averageCompletionTime:Math.round(10*l)/10}},[s]),d=async()=>{try{if(!s||0===s.length)return void console.warn("No data to export");let e=s.map(e=>({"Employee Name":e.employeeName,"Assigned Tasks":e.assignedTasks,"Completed Tasks":e.completedTasks,"Completion Rate (%)":Math.round(e.completionRate),"Average Completion Time (days)":e.averageCompletionTime}));if(0===e.length)return void console.warn("No data to export");let t=[Object.keys(e[0]).join(","),...e.map(e=>Object.values(e).join(","))].join("\n"),a=new Blob([t],{type:"text/csv"}),l=window.URL.createObjectURL(a),r=document.createElement("a");r.href=l,r.download="task-assignment-metrics-".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(l),document.body.removeChild(r)}catch(e){console.error("Export failed:",e)}},m=e=>e>=90?{variant:"default",label:"Excellent"}:e>=75?{variant:"secondary",label:"Good"}:e>=60?{variant:"outline",label:"Average"}:{variant:"destructive",label:"Needs Improvement"};return s&&0!==s.length?(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsxs)(N.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Task Assignment Metrics"]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Employee task performance and completion rates"})]}),l&&(0,a.jsxs)(y.$,{variant:"outline",size:"sm",onClick:d,className:"h-8",children:[(0,a.jsx)(p.A,{className:"h-3 w-3 mr-1"}),"Export CSV"]})]}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-blue-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-blue-600",children:o.totalEmployees})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-blue-700",children:"Employees"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-purple-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(r.A,{className:"h-5 w-5 text-purple-600"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-purple-600",children:o.totalAssignedTasks})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-purple-700",children:"Assigned"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-green-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(J.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("span",{className:"text-2xl font-bold text-green-600",children:o.totalCompletedTasks})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-green-700",children:"Completed"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-emerald-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(r.A,{className:"h-5 w-5 text-emerald-600"}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-emerald-600",children:[o.averageCompletionRate,"%"]})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-emerald-700",children:"Avg. Rate"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center p-3 bg-orange-50 rounded-lg border",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(so.A,{className:"h-5 w-5 text-orange-600"}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-orange-600",children:[o.averageCompletionTime,"d"]})]}),(0,a.jsx)("p",{className:"text-sm font-medium text-orange-700",children:"Avg. Time"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-muted-foreground",children:["Employee Performance (",n.length," of ",s.length,")"]}),(0,a.jsx)("div",{className:"space-y-3",children:n.map((e,s)=>{let t=m(e.completionRate);return(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center justify-between p-4 border rounded-lg bg-muted/20 gap-4",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 mb-3",children:[(0,a.jsx)("h5",{className:"font-medium truncate",children:e.employeeName}),(0,a.jsx)(v.E,{variant:t.variant,className:"text-xs w-fit",children:t.label})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-3 text-sm",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:"Assigned"}),(0,a.jsx)("span",{className:"font-medium text-base",children:e.assignedTasks})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:"Completed"}),(0,a.jsx)("span",{className:"font-medium text-base",children:e.completedTasks})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:"Rate"}),(0,a.jsxs)("span",{className:"font-medium text-base",children:[Math.round(e.completionRate),"%"]})]}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-xs",children:"Avg. Time"}),(0,a.jsxs)("span",{className:"font-medium text-base",children:[e.averageCompletionTime,"d"]})]})]})]}),(0,a.jsxs)("div",{className:"w-full md:w-32 flex flex-col items-center",children:[(0,a.jsx)(tN.k,{value:e.completionRate,className:"h-3 w-full"}),(0,a.jsxs)("p",{className:"text-sm font-medium mt-2 text-center",children:[Math.round(e.completionRate),"%"]})]})]},e.employeeId)})}),s.length>i&&(0,a.jsx)("div",{className:"text-center pt-2",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Showing top ",i," employees by completion rate"]})})]})]})]}):(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Task Assignment Metrics"]})}),(0,a.jsx)(N.Wu,{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center text-muted-foreground",children:[(0,a.jsx)(c.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No task assignment data available"})]})})]})},tw=e=>{let{className:s="",compact:t=!1,showCharts:n=!0}=e,{data:o,isLoading:d,error:m}=s$(s4(),{enabled:!0,staleTime:12e4}),x=(0,g.useMemo)(()=>{var e,s,t;return o?{totalTasks:o.totalCount||0,completedTasks:(null==(s=o.statusDistribution)||null==(e=s.find(e=>"Completed"===e.status))?void 0:e.count)||0,completionRate:Math.round(100*(o.completionRate||0)),overdueTasks:o.overdueCount||0,averageCompletionTime:o.averageCompletionTime||0,assignedEmployees:(null==(t=o.assignmentMetrics)?void 0:t.length)||0}:null},[o]);return d?(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{className:"pb-6",children:(0,a.jsx)(s7.E,{className:"h-7 w-48"})}),(0,a.jsxs)(N.Wu,{className:"space-y-8",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,s)=>(0,a.jsx)(s7.E,{className:"h-32 rounded-lg"},s))}),n&&!t&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsx)(s7.E,{className:"h-80 rounded-lg"}),(0,a.jsx)(s7.E,{className:"h-80 rounded-lg"})]})]})]}):m?(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3",children:[(0,a.jsx)(i.A,{className:"h-6 w-6"}),"Task Analytics"]})}),(0,a.jsx)(N.Wu,{className:"pt-6",children:(0,a.jsxs)($.Fc,{variant:"destructive",children:[(0,a.jsx)(G.A,{className:"h-4 w-4"}),(0,a.jsx)($.XL,{children:"Error Loading Task Analytics"}),(0,a.jsx)($.TN,{children:m.message||"Failed to load task analytics data"})]})})]}):(0,a.jsxs)(N.Zp,{className:"".concat(s," overflow-hidden"),children:[(0,a.jsx)(N.aR,{className:"pb-8",children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-3 text-xl",children:[(0,a.jsx)(i.A,{className:"h-6 w-6 text-primary"}),"Task Analytics"]})}),(0,a.jsxs)(N.Wu,{className:"px-8 pb-8 space-y-10",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-6",children:(0,a.jsx)(i.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsx)("div",{className:"text-4xl font-bold text-blue-600 mb-3",children:(null==x?void 0:x.totalTasks)||0}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Total Tasks"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-green-100 dark:bg-green-900/30 mb-6",children:(0,a.jsx)(J.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsx)("div",{className:"text-4xl font-bold text-green-600 mb-3",children:(null==x?void 0:x.completedTasks)||0}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Completed"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-emerald-100 dark:bg-emerald-900/30 mb-6",children:(0,a.jsx)(r.A,{className:"h-8 w-8 text-emerald-600"})}),(0,a.jsxs)("div",{className:"text-4xl font-bold text-emerald-600 mb-3",children:[(null==x?void 0:x.completionRate)||0,"%"]}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Completion Rate"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-red-100 dark:bg-red-900/30 mb-6",children:(0,a.jsx)(G.A,{className:"h-8 w-8 text-red-600"})}),(0,a.jsx)("div",{className:"text-4xl font-bold text-red-600 mb-3",children:(null==x?void 0:x.overdueTasks)||0}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Overdue"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-yellow-100 dark:bg-yellow-900/30 mb-6",children:(0,a.jsx)(so.A,{className:"h-8 w-8 text-yellow-600"})}),(0,a.jsxs)("div",{className:"text-4xl font-bold text-yellow-600 mb-3",children:[(null==x?void 0:x.averageCompletionTime)||0,"d"]}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Avg. Completion"})]}),(0,a.jsxs)("div",{className:"group flex flex-col items-center text-center p-8 rounded-xl border-2 border-border hover:border-primary/20 hover:bg-primary/5 transition-all duration-200",children:[(0,a.jsx)("div",{className:"p-4 rounded-full bg-purple-100 dark:bg-purple-900/30 mb-6",children:(0,a.jsx)(c.A,{className:"h-8 w-8 text-purple-600"})}),(0,a.jsx)("div",{className:"text-4xl font-bold text-purple-600 mb-3",children:(null==x?void 0:x.assignedEmployees)||0}),(0,a.jsx)("div",{className:"text-sm font-medium text-muted-foreground uppercase tracking-wide",children:"Assigned Staff"})]})]}),n&&!t&&o&&(0,a.jsxs)("div",{className:"space-y-10",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsxs)(N.Zp,{className:"border-2",children:[(0,a.jsx)(N.aR,{className:"pb-6",children:(0,a.jsx)(N.ZB,{className:"text-lg",children:"Status Distribution"})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(th,{data:o.statusDistribution,className:"h-80 w-full",showLegend:!0,interactive:!0})})]}),(0,a.jsxs)(N.Zp,{className:"border-2",children:[(0,a.jsx)(N.aR,{className:"pb-6",children:(0,a.jsx)(N.ZB,{className:"text-lg",children:"Priority Distribution"})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(tv,{data:o.priorityDistribution,className:"h-80 w-full",showLegend:!0,interactive:!0})})]})]}),(0,a.jsx)(tb,{data:o.assignmentMetrics,className:"w-full"})]}),t&&o&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 bg-muted/50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-primary",children:[null==x?void 0:x.completionRate,"%"]}),(0,a.jsx)("span",{className:"text-muted-foreground",children:"completion rate"})]}),(0,a.jsxs)(y.$,{variant:"ghost",size:"sm",className:"gap-2",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),"View Details"]})]})]})]})};var tk=t(35695),tA=t(40879);t(17841);var tC=t(3235);let tS=e=>(0,e4.Sk)(["vehicle-analytics",e],async()=>{let s=new URLSearchParams;if(null==e?void 0:e.dateRange){let t=e.dateRange.from instanceof Date?e.dateRange.from:new Date(e.dateRange.from),a=e.dateRange.to instanceof Date?e.dateRange.to:new Date(e.dateRange.to);s.append("dateRange.from",t.toISOString().split("T")[0]||t.toISOString()),s.append("dateRange.to",a.toISOString().split("T")[0]||a.toISOString())}(null==e?void 0:e.vehicles)&&e.vehicles.forEach(e=>s.append("vehicles",e.toString())),(null==e?void 0:e.serviceTypes)&&e.serviceTypes.forEach(e=>s.append("serviceTypes",e)),(null==e?void 0:e.serviceStatus)&&e.serviceStatus.forEach(e=>s.append("serviceStatus",e));let t="/reporting/vehicles/analytics".concat(s.toString()?"?".concat(s.toString()):""),a=await e5.uE.get(t);return a.data||a},{cacheDuration:3e5,enableRetry:!0,retryAttempts:3}),tE=e=>{let{label:s,value:t,icon:l,trend:r,variant:i="default"}=e;return(0,a.jsxs)("div",{className:(0,en.cn)("p-4 border rounded-lg",{default:"border-gray-200",warning:"border-orange-200 bg-orange-50",success:"border-green-200 bg-green-50",destructive:"border-red-200 bg-red-50"}[i]),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[l,(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:s})]}),r&&(0,a.jsxs)(v.E,{variant:r.isPositive?"default":"destructive",className:"text-xs",children:[r.isPositive?"+":"",r.value,"%"]})]}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("span",{className:"text-2xl font-bold",children:t})})]})},tD=e=>{var s,t,l,i;let{filters:c,className:o="",showExportOptions:d=!0,compact:m=!1}=e,{data:x,isLoading:u,error:h}=tS(c);if(u)return(0,a.jsxs)(N.Zp,{className:o,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Vehicle Analytics"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(h)return(0,a.jsxs)(N.Zp,{className:o,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Vehicle Analytics"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:h})})]});let g=(null==x?void 0:x.totalCount)||0,j=(null==x||null==(s=x.utilizationMetrics)?void 0:s.filter(e=>e.utilizationRate>0).length)||0,f=g>0?j/g*100:0,b=(null==x||null==(t=x.costAnalysis)?void 0:t.totalCost)||0,w=(null==x||null==(l=x.costAnalysis)?void 0:l.averageCostPerService)||0;return(0,a.jsxs)(N.Zp,{className:o,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Vehicle Analytics"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:[g," vehicles"]}),d&&(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(sl.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsx)(tE,{label:"Total Vehicles",value:g,icon:(0,a.jsx)(n.A,{className:"h-4 w-4"})}),(0,a.jsx)(tE,{label:"Utilization Rate",value:"".concat(Math.round(f),"%"),icon:(0,a.jsx)(r.A,{className:"h-4 w-4"}),variant:f<70?"warning":"success"}),(0,a.jsx)(tE,{label:"Total Service Cost",value:"$".concat(b.toLocaleString()),icon:(0,a.jsx)(eK.A,{className:"h-4 w-4"})}),(0,a.jsx)(tE,{label:"Avg. Cost/Service",value:"$".concat(Math.round(w)),icon:(0,a.jsx)(tC.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-blue-700",children:"Active Vehicles"}),(0,a.jsx)(n.A,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-blue-900",children:j})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-green-700",children:"Maintenance Due"}),(0,a.jsx)(G.A,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-green-900",children:(null==x||null==(i=x.maintenanceSchedule)?void 0:i.filter(e=>new Date(e.nextMaintenanceDate)<=new Date).length)||0})]}),(0,a.jsxs)("div",{className:"p-4 bg-orange-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-orange-700",children:"Avg. Service Time"}),(0,a.jsx)(so.A,{className:"h-4 w-4 text-orange-600"})]}),(0,a.jsxs)("span",{className:"text-xl font-bold text-orange-900",children:[(null==x?void 0:x.serviceHistory)&&x.serviceHistory.length>0?Math.round(x.serviceHistory.reduce((e,s)=>e+(s.cost||0),0)/x.serviceHistory.length):0,"h"]})]})]}),!m&&(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Last updated: ",new Date().toLocaleTimeString()]}),(0,a.jsx)(y.$,{variant:"outline",size:"sm",children:"View Details"})]})})]})]})},tT=e=>{let{active:s,payload:t,label:l}=e;if(s&&t&&t.length){let e=t[0].payload;return(0,a.jsxs)("div",{className:"bg-white p-3 border rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:e.vehicleName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Utilization:"," ",(0,a.jsxs)("span",{className:"font-medium",children:[e.utilizationRate,"%"]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Active Delegations:"," ",(0,a.jsx)("span",{className:"font-medium",children:e.activeDelegations})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Total Delegations:"," ",(0,a.jsx)("span",{className:"font-medium",children:e.totalDelegations})]})]})}return null},tR=e=>e>=80?"#ef4444":e>=60?"#f59e0b":e>=40?"#10b981":e>=20?"#3b82f6":"#6b7280",tz=e=>{let{data:s,filters:t,className:l="",showLegend:r=!0,interactive:i=!0,height:c=300}=e,{data:o,isLoading:d,error:m}=tS(t),x=s||(null==o?void 0:o.utilizationMetrics),u=(0,g.useMemo)(()=>x?x.map(e=>({vehicleName:e.vehicleName,utilizationRate:Math.round(e.utilizationRate),activeDelegations:e.activeDelegations,totalDelegations:e.totalDelegations,color:tR(e.utilizationRate),displayName:e.vehicleName.length>15?"".concat(e.vehicleName.substring(0,12),"..."):e.vehicleName})).sort((e,s)=>s.utilizationRate-e.utilizationRate).slice(0,10):[],[x]);if(d)return(0,a.jsxs)(N.Zp,{className:l,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Vehicle Utilization"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(m)return(0,a.jsxs)(N.Zp,{className:l,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Vehicle Utilization"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:m})})]});let h=u.length>0?Math.round(u.reduce((e,s)=>e+s.utilizationRate,0)/u.length):0,p=u.filter(e=>e.utilizationRate>=80).length;return(0,a.jsxs)(N.Zp,{className:l,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Vehicle Utilization"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Avg: ",h,"%"]}),p>0&&(0,a.jsxs)(v.E,{variant:"destructive",className:"text-xs",children:[p," overutilized"]})]})]})}),(0,a.jsx)(N.Wu,{children:0===u.length?(0,a.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(n.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No vehicle utilization data available"})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(sw.u,{width:"100%",height:c,children:(0,a.jsxs)(to.E,{data:u,margin:{top:20,right:30,left:20,bottom:60},children:[(0,a.jsx)(ta.d,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),(0,a.jsx)(tl.W,{dataKey:"displayName",angle:-45,textAnchor:"end",height:60,fontSize:12}),(0,a.jsx)(tr.h,{domain:[0,100],tickFormatter:e=>"".concat(e,"%"),fontSize:12}),i&&(0,a.jsx)(sS.m,{content:(0,a.jsx)(tT,{})}),(0,a.jsx)(td.y,{dataKey:"utilizationRate",radius:[4,4,0,0],name:"Utilization Rate",children:u.map((e,s)=>(0,a.jsx)(sC.f,{fill:e.color},"cell-".concat(s)))})]})}),r&&(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap gap-4 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded"}),(0,a.jsx)("span",{children:"Overutilized (80%+)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-orange-500 rounded"}),(0,a.jsx)("span",{children:"High (60-79%)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded"}),(0,a.jsx)("span",{children:"Good (40-59%)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded"}),(0,a.jsx)("span",{children:"Moderate (20-39%)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-gray-500 rounded"}),(0,a.jsx)("span",{children:"Low (0-19%)"})]})]})]})})]})};var tM=t(70831);let tF=e=>{let{compact:s=!1,item:t}=e,l=new Date(t.nextMaintenanceDate),r=new Date,i=(0,el.Y)(l,r),c=(0,ea.d)(l,r)&&(0,el.Y)(l,(0,tM.f)(r,7)),o=()=>i?(0,a.jsx)(v.E,{variant:"destructive",children:"Overdue"}):c?(0,a.jsx)(v.E,{className:"bg-orange-100 text-orange-800",variant:"secondary",children:"Due Soon"}):(0,a.jsx)(v.E,{variant:"outline",children:"Scheduled"});return(0,a.jsxs)("div",{className:(0,en.cn)("flex items-center justify-between p-3 border rounded-lg",i&&"border-red-200 bg-red-50",c&&"border-orange-200 bg-orange-50"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[i?(0,a.jsx)(G.A,{className:"size-4 text-red-600"}):c?(0,a.jsx)(so.A,{className:"size-4 text-orange-600"}):(0,a.jsx)(M.A,{className:"size-4 text-blue-600"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:t.maintenanceType}),!s&&o()]}),(0,a.jsxs)("div",{className:"mt-1 flex items-center gap-4 text-xs text-gray-500",children:[(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(n.A,{className:"size-3"}),t.vehicleName]}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(M.A,{className:"size-3"}),(0,er.GP)(l,"MMM dd, yyyy")]}),t.estimatedCost&&(0,a.jsxs)("span",{children:["$",t.estimatedCost.toLocaleString()]})]})]})]}),s&&(0,a.jsx)("div",{className:"text-right",children:o()})]})},tP=e=>{let{className:s="",compact:t=!1,filters:l,maxItems:r=5}=e,{data:i,error:n,isLoading:c}=tS(l),o=(null==i?void 0:i.maintenanceSchedule)||[];if(c)return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(tC.A,{className:"size-5"}),"Vehicle Maintenance"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{data:null,error:null,isLoading:!0,children:()=>null})})]});if(n)return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(tC.A,{className:"size-5"}),"Vehicle Maintenance"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:n})})]});let d=new Date,m=o.filter(e=>(0,el.Y)(new Date(e.nextMaintenanceDate),d)),x=o.filter(e=>(0,ea.d)(new Date(e.nextMaintenanceDate),d)&&(0,el.Y)(new Date(e.nextMaintenanceDate),(0,tM.f)(d,7))),u=o.sort((e,s)=>{let t=new Date(e.nextMaintenanceDate),a=new Date(s.nextMaintenanceDate),l=(0,el.Y)(t,d),r=(0,el.Y)(a,d);return l&&!r?-1:!l&&r?1:t.getTime()-a.getTime()}).slice(0,r);return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(tC.A,{className:"size-5"}),"Vehicle Maintenance"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[m.length>0&&(0,a.jsxs)(v.E,{className:"text-xs",variant:"destructive",children:[m.length," overdue"]}),x.length>0&&(0,a.jsxs)(v.E,{className:"bg-orange-100 text-xs text-orange-800",variant:"secondary",children:[x.length," due soon"]}),(0,a.jsx)(y.$,{size:"sm",variant:"ghost",children:(0,a.jsx)(sl.A,{className:"size-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"rounded-lg bg-red-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-700",children:m.length}),(0,a.jsx)("div",{className:"text-xs text-red-600",children:"Overdue"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-orange-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-700",children:x.length}),(0,a.jsx)("div",{className:"text-xs text-orange-600",children:"Due Soon"})]}),(0,a.jsxs)("div",{className:"rounded-lg bg-green-50 p-3 text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-700",children:0}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Completed"})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:0===u.length?(0,a.jsxs)("div",{className:"py-8 text-center text-gray-500",children:[(0,a.jsx)(tC.A,{className:"mx-auto mb-2 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No maintenance items scheduled"})]}):u.map((e,s)=>(0,a.jsx)(tF,{compact:t,item:e},"".concat(e.vehicleId,"-").concat(e.maintenanceType,"-").concat(s)))}),!t&&o.length>r&&(0,a.jsx)("div",{className:"border-t pt-4",children:(0,a.jsxs)(y.$,{className:"w-full",size:"sm",variant:"outline",children:["View All Maintenance (",o.length," total)"]})})]})]})};var tI=t(77070);let tL=e=>{let{label:s,value:t,icon:l,trend:i,variant:n="default"}=e;return(0,a.jsxs)("div",{className:(0,en.cn)("p-4 border rounded-lg",{default:"border-gray-200",warning:"border-orange-200 bg-orange-50",success:"border-green-200 bg-green-50",destructive:"border-red-200 bg-red-50"}[n]),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[l,(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:s})]}),i&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[i.isPositive?(0,a.jsx)(r.A,{className:"h-3 w-3 text-green-600"}):(0,a.jsx)(tI.A,{className:"h-3 w-3 text-red-600"}),(0,a.jsxs)(v.E,{variant:i.isPositive?"default":"destructive",className:"text-xs",children:[i.isPositive?"+":"",i.value,"%"]})]})]}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("span",{className:"text-2xl font-bold",children:t})})]})},tO=e=>{let{active:s,payload:t}=e;if(s&&t&&t.length){let e=t[0].payload;return(0,a.jsxs)("div",{className:"bg-white p-3 border rounded-lg shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Cost:"," ",(0,a.jsxs)("span",{className:"font-medium",children:["$",e.value.toLocaleString()]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Percentage: ",(0,a.jsxs)("span",{className:"font-medium",children:[e.percentage,"%"]})]})]})}return null},tV=e=>{let{filters:s,className:t="",showTrend:i=!0,compact:n=!1}=e,{data:c,isLoading:o,error:d}=tS(s),m=null==c?void 0:c.costAnalysis,x=(0,g.useMemo)(()=>{if(!(null==m?void 0:m.costByType))return[];let e=["#3b82f6","#ef4444","#10b981","#f59e0b","#8b5cf6"];return m.costByType.map((s,t)=>({name:s.type,value:s.cost,percentage:Math.round(s.cost/m.totalCost*100),color:e[t%e.length]}))},[m]),u=(0,g.useMemo)(()=>(null==m?void 0:m.monthlyTrend)?m.monthlyTrend.map((e,s)=>({month:(0,er.GP)(new Date(e.month),"MMM"),cost:e.cost,services:s+1})):[],[m]);if(o)return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eK.A,{className:"h-5 w-5"}),"Cost Analytics"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(d)return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eK.A,{className:"h-5 w-5"}),"Cost Analytics"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:d})})]});let h=(null==m?void 0:m.totalCost)||0,p=(null==m?void 0:m.averageCostPerService)||0,j=h>0?Math.min(h/1e4*100,100):0,f=12*h;return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(eK.A,{className:"h-5 w-5"}),"Cost Analytics"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["$",h.toLocaleString()," total"]}),j>90&&(0,a.jsx)(v.E,{variant:"destructive",className:"text-xs",children:"Budget Alert"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(sl.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsx)(tL,{label:"Total Cost",value:"$".concat(h.toLocaleString()),icon:(0,a.jsx)(eK.A,{className:"h-4 w-4"})}),(0,a.jsx)(tL,{label:"Avg/Service",value:"$".concat(Math.round(p)),icon:(0,a.jsx)(l.A,{className:"h-4 w-4"})}),(0,a.jsx)(tL,{label:"Budget Used",value:"".concat(Math.round(j),"%"),icon:(0,a.jsx)(eJ.A,{className:"h-4 w-4"}),variant:j>90?"destructive":j>75?"warning":"success"}),(0,a.jsx)(tL,{label:"Projected Annual",value:"$".concat(Math.round(f).toLocaleString()),icon:(0,a.jsx)(r.A,{className:"h-4 w-4"})})]}),!n&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Cost by Service Type"}),x.length>0?(0,a.jsx)(sw.u,{width:"100%",height:200,children:(0,a.jsxs)(sk.r,{children:[(0,a.jsx)(sA.F,{data:x,cx:"50%",cy:"50%",outerRadius:80,dataKey:"value",label:e=>{let{name:s,percentage:t}=e;return"".concat(s,": ").concat(t,"%")},labelLine:!1,children:x.map((e,s)=>(0,a.jsx)(sC.f,{fill:e.color},"cell-".concat(s)))}),(0,a.jsx)(sS.m,{content:(0,a.jsx)(tO,{})})]})}):(0,a.jsx)("div",{className:"h-48 flex items-center justify-center text-gray-500",children:"No cost breakdown data available"})]}),i&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Monthly Cost Trend"}),u.length>0?(0,a.jsx)(sw.u,{width:"100%",height:200,children:(0,a.jsxs)(tt.b,{data:u,children:[(0,a.jsx)(ta.d,{strokeDasharray:"3 3",stroke:"#f0f0f0"}),(0,a.jsx)(tl.W,{dataKey:"month",fontSize:12}),(0,a.jsx)(tr.h,{tickFormatter:e=>"$".concat(e),fontSize:12}),(0,a.jsx)(sS.m,{formatter:e=>["$".concat(e.toLocaleString()),"Cost"],labelFormatter:e=>"Month: ".concat(e)}),(0,a.jsx)(tn.N,{type:"monotone",dataKey:"cost",stroke:"#3b82f6",strokeWidth:2,dot:{fill:"#3b82f6",strokeWidth:2,r:4}})]})}):(0,a.jsx)("div",{className:"h-48 flex items-center justify-center text-gray-500",children:"No trend data available"})]})]}),j>90&&(0,a.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(G.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"font-medium text-red-800",children:"Budget Alert"})]}),(0,a.jsxs)("p",{className:"text-sm text-red-700 mt-1",children:["You've used ",Math.round(j),"% of your maintenance budget. Consider reviewing upcoming expenses."]})]})]})]})};var tB=t(57804);let tY=e=>(0,e4.Sk)(["employee-analytics",e],async()=>{let s=new URLSearchParams;if(null==e?void 0:e.dateRange){let t=e.dateRange.from instanceof Date?e.dateRange.from:new Date(e.dateRange.from),a=e.dateRange.to instanceof Date?e.dateRange.to:new Date(e.dateRange.to);s.append("dateRange.from",t.toISOString().split("T")[0]||t.toISOString()),s.append("dateRange.to",a.toISOString().split("T")[0]||a.toISOString())}(null==e?void 0:e.employees)&&e.employees.forEach(e=>s.append("employees",e.toString())),(null==e?void 0:e.locations)&&e.locations.forEach(e=>s.append("locations",e)),(null==e?void 0:e.includeEmployeeMetrics)&&s.append("includeEmployeeMetrics","true");let t="/reporting/employees/analytics".concat(s.toString()?"?".concat(s.toString()):""),a=await e5.uE.get(t);return a.data||a},{cacheDuration:3e5,enableRetry:!0,retryAttempts:3}),tW=e=>{let{label:s,value:t,icon:l,trend:r,variant:i="default"}=e;return(0,a.jsxs)("div",{className:(0,en.cn)("p-4 border rounded-lg",{default:"border-gray-200",warning:"border-orange-200 bg-orange-50",success:"border-green-200 bg-green-50",destructive:"border-red-200 bg-red-50"}[i]),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[l,(0,a.jsx)("span",{className:"text-sm font-medium text-gray-600",children:s})]}),r&&(0,a.jsxs)(v.E,{variant:r.isPositive?"default":"destructive",className:"text-xs",children:[r.isPositive?"+":"",r.value,"%"]})]}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("span",{className:"text-2xl font-bold",children:t})})]})},tU=e=>{var s,t,l,i,n,o;let{filters:d,className:m="",showExportOptions:x=!0,compact:u=!1}=e,{data:h,isLoading:g,error:j}=tY(d);if(g)return(0,a.jsxs)(N.Zp,{className:m,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Employee Analytics"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(j)return(0,a.jsxs)(N.Zp,{className:m,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Employee Analytics"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:j})})]});let f=(null==h?void 0:h.totalCount)||0,b=(null==h||null==(s=h.performanceMetrics)?void 0:s.filter(e=>e.completedDelegations>0||e.completedTasks>0).length)||0,w=(null==h||null==(t=h.performanceMetrics)?void 0:t.length)&&h.performanceMetrics.length>0?Math.round(h.performanceMetrics.reduce((e,s)=>e+s.averageRating,0)/h.performanceMetrics.length):0,k=(null==h||null==(l=h.taskAssignments)?void 0:l.reduce((e,s)=>e+s.completedTasks,0))||0,A=(null==h||null==(i=h.taskAssignments)?void 0:i.reduce((e,s)=>e+s.overdueTasksCount,0))||0,C=(null==h||null==(n=h.availabilityMetrics)?void 0:n.length)&&h.availabilityMetrics.length>0?Math.round(h.availabilityMetrics.reduce((e,s)=>e+s.utilizationRate,0)/h.availabilityMetrics.length):0;return(0,a.jsxs)(N.Zp,{className:m,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Employee Analytics"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:[f," employees"]}),A>0&&(0,a.jsxs)(v.E,{variant:"destructive",className:"text-xs",children:[A," overdue"]}),x&&(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(sl.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsx)(tW,{label:"Total Employees",value:f,icon:(0,a.jsx)(c.A,{className:"h-4 w-4"})}),(0,a.jsx)(tW,{label:"Active Employees",value:b,icon:(0,a.jsx)(tB.A,{className:"h-4 w-4"}),variant:b<.8*f?"warning":"success"}),(0,a.jsx)(tW,{label:"Avg Performance",value:"".concat(w,"/10"),icon:(0,a.jsx)(r.A,{className:"h-4 w-4"}),variant:w<7?"warning":"success"}),(0,a.jsx)(tW,{label:"Utilization Rate",value:"".concat(C,"%"),icon:(0,a.jsx)(so.A,{className:"h-4 w-4"}),variant:C<70?"warning":C>90?"destructive":"success"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-blue-700",children:"Completed Tasks"}),(0,a.jsx)(J.A,{className:"h-4 w-4 text-blue-600"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-blue-900",children:k})]}),(0,a.jsxs)("div",{className:"p-4 bg-red-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-red-700",children:"Overdue Tasks"}),(0,a.jsx)(G.A,{className:"h-4 w-4 text-red-600"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-red-900",children:A})]}),(0,a.jsxs)("div",{className:"p-4 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-green-700",children:"On-Time Rate"}),(0,a.jsx)(so.A,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)("span",{className:"text-xl font-bold text-green-900",children:[(null==h||null==(o=h.performanceMetrics)?void 0:o.length)&&h.performanceMetrics.length>0?Math.round(h.performanceMetrics.reduce((e,s)=>e+s.onTimePerformance,0)/h.performanceMetrics.length):0,"%"]})]})]}),!u&&(null==h?void 0:h.workloadDistribution)&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Workload Distribution"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,a.jsxs)("div",{className:"p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-green-700",children:h.workloadDistribution.filter(e=>"Underutilized"===e.status).length}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Underutilized"})]}),(0,a.jsxs)("div",{className:"p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-blue-700",children:h.workloadDistribution.filter(e=>"Optimal"===e.status).length}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:"Optimal"})]}),(0,a.jsxs)("div",{className:"p-3 bg-red-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-red-700",children:h.workloadDistribution.filter(e=>"Overloaded"===e.status).length}),(0,a.jsx)("div",{className:"text-xs text-red-600",children:"Overloaded"})]})]})]}),!u&&(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["Last updated: ",new Date().toLocaleTimeString()]}),(0,a.jsx)(y.$,{variant:"outline",size:"sm",children:"View Details"})]})})]})]})};var tZ=t(91467),t$=t(91394);let t_=e=>{let{active:s,label:t,payload:l}=e;if(s&&(null==l?void 0:l.length)){let e=l[0].payload;return(0,a.jsxs)("div",{className:"rounded-lg border bg-white p-3 shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:e.employeeName}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Performance Score:"," ",(0,a.jsxs)("span",{className:"font-medium",children:[e.averageRating,"/10"]})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Completed Delegations:"," ",(0,a.jsx)("span",{className:"font-medium",children:e.completedDelegations})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Completed Tasks:"," ",(0,a.jsx)("span",{className:"font-medium",children:e.completedTasks})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["On-Time Rate:"," ",(0,a.jsxs)("span",{className:"font-medium",children:[e.onTimePerformance,"%"]})]})]})}return null},tG=e=>e>=9?"#10b981":e>=8?"#3b82f6":e>=7?"#f59e0b":e>=6?"#ef4444":"#6b7280",tJ=e=>{let{className:s="",data:t,filters:l,height:r=300,interactive:i=!0,maxEmployees:n=10,showLegend:o=!0}=e,{data:d,error:m,isLoading:x}=tY(l),u=t||(null==d?void 0:d.performanceMetrics),h=(0,g.useMemo)(()=>u?u.map(e=>({averageRating:e.averageRating,color:tG(e.averageRating),completedDelegations:e.completedDelegations,completedTasks:e.completedTasks,displayName:e.employeeName.length>12?"".concat(e.employeeName.slice(0,9),"..."):e.employeeName,employeeName:e.employeeName,onTimePerformance:e.onTimePerformance,overallScore:Math.round(.4*e.averageRating+.01*e.onTimePerformance*3+.3*e.workloadScore),workloadScore:e.workloadScore})).sort((e,s)=>s.overallScore-e.overallScore).slice(0,n):[],[u,n]);if(x)return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"size-5"}),"Employee Performance"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{data:null,error:null,isLoading:!0,children:()=>null})})]});if(m)return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"size-5"}),"Employee Performance"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:m})})]});let p=h.length>0?Math.round(h.reduce((e,s)=>e+s.averageRating,0)/h.length*10)/10:0,j=h.filter(e=>e.averageRating>=8).length;return(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"size-5"}),"Employee Performance"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{className:"text-xs",variant:"secondary",children:["Avg: ",p,"/10"]}),j>0&&(0,a.jsxs)(v.E,{className:"text-xs",variant:"default",children:[(0,a.jsx)(tZ.A,{className:"mr-1 size-3"}),j," top performers"]})]})]})}),(0,a.jsx)(N.Wu,{children:0===h.length?(0,a.jsx)("div",{className:"flex h-64 items-center justify-center text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(c.A,{className:"mx-auto mb-2 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No employee performance data available"})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(sw.u,{height:r,width:"100%",children:(0,a.jsxs)(to.E,{data:h,margin:{bottom:60,left:20,right:30,top:20},children:[(0,a.jsx)(ta.d,{stroke:"#f0f0f0",strokeDasharray:"3 3"}),(0,a.jsx)(tl.W,{angle:-45,dataKey:"displayName",fontSize:12,height:60,textAnchor:"end"}),(0,a.jsx)(tr.h,{domain:[0,10],fontSize:12,tickFormatter:e=>"".concat(e,"/10")}),i&&(0,a.jsx)(sS.m,{content:(0,a.jsx)(t_,{})}),(0,a.jsx)(td.y,{dataKey:"averageRating",name:"Performance Score",radius:[4,4,0,0],children:h.map((e,s)=>(0,a.jsx)(sC.f,{fill:e.color},"cell-".concat(s)))})]})}),o&&(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap gap-4 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-green-500"}),(0,a.jsx)("span",{children:"Excellent (9-10)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-blue-500"}),(0,a.jsx)("span",{children:"Good (8-8.9)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-orange-500"}),(0,a.jsx)("span",{children:"Average (7-7.9)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-red-500"}),(0,a.jsx)("span",{children:"Below Average (6-6.9)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("div",{className:"size-3 rounded bg-gray-500"}),(0,a.jsx)("span",{children:"Poor (<6)"})]})]}),(0,a.jsxs)("div",{className:"mt-6 space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Top Performers"}),(0,a.jsx)("div",{className:"space-y-2",children:h.slice(0,3).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between rounded bg-gray-50 p-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v.E,{className:"flex size-6 items-center justify-center p-0 text-xs",variant:"outline",children:s+1}),(0,a.jsx)(t$.eu,{className:"size-6",children:(0,a.jsx)(t$.q5,{className:"text-xs",children:e.employeeName.split(" ").map(e=>e[0]).join("")})}),(0,a.jsx)("span",{className:"text-sm font-medium",children:e.employeeName})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm font-bold",children:[e.averageRating,"/10"]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[e.onTimePerformance,"% on-time"]})]})]},e.employeeName))})]})]})})]})},tq=e=>{let{item:s,compact:t=!1}=e,l=e=>{switch(e){case"Underutilized":return"text-blue-600 bg-blue-100";case"Optimal":return"text-green-600 bg-green-100";case"Overloaded":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}};return"Overloaded"===s.status||s.status,(0,a.jsxs)("div",{className:(0,en.cn)("flex items-center justify-between p-3 border rounded-lg","Overloaded"===s.status&&"border-red-200 bg-red-50","Optimal"===s.status&&"border-green-200 bg-green-50","Underutilized"===s.status&&"border-blue-200 bg-blue-50"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 flex-1",children:[(0,a.jsx)(t$.eu,{className:"h-8 w-8",children:(0,a.jsx)(t$.q5,{className:"text-xs",children:s.employeeName.split(" ").map(e=>e[0]).join("")})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"font-medium text-sm",children:s.employeeName}),!t&&(0,a.jsx)(v.E,{className:(0,en.cn)("text-xs",l(s.status)),children:s.status})]}),(0,a.jsxs)("div",{className:"mt-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[(0,a.jsxs)("span",{children:["Workload: ",s.currentWorkload,"/",s.capacity]}),(0,a.jsx)("span",{children:"•"}),(0,a.jsxs)("span",{children:[s.workloadPercentage,"%"]})]}),(0,a.jsx)(tN.k,{value:s.workloadPercentage,className:"h-2 mt-1",style:{"--progress-background":"Overloaded"===s.status?"#ef4444":"Optimal"===s.status?"#10b981":"#3b82f6"}})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(e=>{switch(e){case"Underutilized":return(0,a.jsx)(so.A,{className:"h-4 w-4 text-blue-600"});case"Optimal":return(0,a.jsx)(J.A,{className:"h-4 w-4 text-green-600"});case"Overloaded":return(0,a.jsx)(G.A,{className:"h-4 w-4 text-red-600"});default:return(0,a.jsx)(c.A,{className:"h-4 w-4 text-gray-600"})}})(s.status),t&&(0,a.jsxs)(v.E,{className:(0,en.cn)("text-xs",l(s.status)),children:[s.workloadPercentage,"%"]})]})]})},tK=e=>{let{filters:s,className:t="",compact:l=!1,maxItems:r=8}=e,{data:i,isLoading:n,error:o}=tY(s),d=(null==i?void 0:i.workloadDistribution)||[];if(n)return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Employee Workload"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(o)return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Employee Workload"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:o})})]});let m=d.filter(e=>"Overloaded"===e.status),x=d.filter(e=>"Underutilized"===e.status),u=d.filter(e=>"Optimal"===e.status),h=[...m,...u,...x].slice(0,r),p=d.length>0?Math.round(d.reduce((e,s)=>e+s.workloadPercentage,0)/d.length):0;return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"Employee Workload"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Avg: ",p,"%"]}),m.length>0&&(0,a.jsxs)(v.E,{variant:"destructive",className:"text-xs",children:[m.length," overloaded"]}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(sl.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-700",children:x.length}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:"Underutilized"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-700",children:u.length}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Optimal"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-red-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-700",children:m.length}),(0,a.jsx)("div",{className:"text-xs text-red-600",children:"Overloaded"})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:0===h.length?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(c.A,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No workload data available"})]}):h.map((e,s)=>(0,a.jsx)(tq,{item:e,compact:l},"".concat(e.employeeId,"-").concat(s)))}),m.length>0&&(0,a.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(G.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"font-medium text-red-800",children:"Workload Alert"})]}),(0,a.jsxs)("p",{className:"text-sm text-red-700 mt-1",children:[m.length," employee",m.length>1?"s are":" is"," overloaded. Consider redistributing tasks or adjusting schedules."]})]}),!l&&d.length>r&&(0,a.jsx)("div",{className:"pt-4 border-t",children:(0,a.jsxs)(y.$,{variant:"outline",size:"sm",className:"w-full",children:["View All Employees (",d.length," total)"]})})]})]})};var tQ=t(83548),tH=t(63807);let tX=e=>(0,e4.Sk)(["cross-entity-analytics",e],async()=>{let s=new URLSearchParams;if(null==e?void 0:e.dateRange){let t=e.dateRange.from instanceof Date?e.dateRange.from:new Date(e.dateRange.from),a=e.dateRange.to instanceof Date?e.dateRange.to:new Date(e.dateRange.to);s.append("dateRange.from",t.toISOString().split("T")[0]||t.toISOString()),s.append("dateRange.to",a.toISOString().split("T")[0]||a.toISOString())}(null==e?void 0:e.employees)&&e.employees.forEach(e=>s.append("employees",e.toString())),(null==e?void 0:e.vehicles)&&e.vehicles.forEach(e=>s.append("vehicles",e.toString())),(null==e?void 0:e.locations)&&e.locations.forEach(e=>s.append("locations",e));let t="/reporting/cross-entity/analytics".concat(s.toString()?"?".concat(s.toString()):""),a=await e5.uE.get(t);return a.data||a},{cacheDuration:3e5,enableRetry:!0,retryAttempts:3}),t0=e=>{let{active:s,payload:t}=e;if(s&&(null==t?void 0:t.length)){let e=t[0].payload;return(0,a.jsxs)("div",{className:"rounded-lg border bg-white p-3 shadow-lg",children:[(0,a.jsx)("p",{className:"font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["X-Axis: ",(0,a.jsx)("span",{className:"font-medium",children:e.x})]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Y-Axis: ",(0,a.jsx)("span",{className:"font-medium",children:e.y})]}),e.correlation&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Correlation: ",(0,a.jsx)("span",{className:"font-medium",children:e.correlation})]})]})}return null},t1=e=>{let{description:s,icon:t,title:l,value:r}=e;return(0,a.jsxs)("div",{className:"rounded-lg border p-4",children:[(0,a.jsxs)("div",{className:"mb-2 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[t,(0,a.jsx)("span",{className:"text-sm font-medium",children:l})]}),(0,a.jsx)(v.E,{className:(0,en.cn)("text-xs",(e=>{let s=Math.abs(e);return s>=.8?"text-green-600 bg-green-100":s>=.6?"text-blue-600 bg-blue-100":s>=.4?"text-orange-600 bg-orange-100":"text-gray-600 bg-gray-100"})(r)),children:(e=>{let s=Math.abs(e);return s>=.8?"Strong":s>=.6?"Moderate":s>=.4?"Weak":"Very Weak"})(r)})]}),(0,a.jsx)("div",{className:"mb-1 text-2xl font-bold",children:r.toFixed(3)}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:s})]})},t2=e=>{var s;let{className:t="",correlationType:l="all",filters:n,interactive:d=!0}=e,{data:m,error:x,isLoading:u}=tX(n),h=(0,g.useMemo)(()=>{var e,s,t,a;if(!(null==m?void 0:m.correlations))return[];switch(l){case"employee-vehicle":return(null==(e=m.correlations.employeeVehicle)?void 0:e.map((e,s)=>({color:e.correlation>.5?"#10b981":e.correlation>0?"#3b82f6":"#ef4444",correlation:e.correlation,name:"".concat(e.employeeName," - ").concat(e.vehicleName),x:e.employeePerformance,y:e.vehicleUtilization})))||[];case"performance-workload":return(null==(s=m.correlations.performanceWorkload)?void 0:s.map((e,s)=>({color:e.correlation>.5?"#10b981":e.correlation>0?"#3b82f6":"#ef4444",correlation:e.correlation,name:e.employeeName,x:e.workloadPercentage,y:e.performanceScore})))||[];case"task-delegation":return(null==(t=m.correlations.taskDelegation)?void 0:t.map((e,s)=>({color:e.correlation>.5?"#10b981":e.correlation>0?"#3b82f6":"#ef4444",correlation:e.correlation,name:e.taskType,x:e.taskComplexity,y:e.delegationSuccess})))||[];default:return(null==(a=m.correlations.overall)?void 0:a.map((e,s)=>({color:e.correlation>.5?"#10b981":e.correlation>0?"#3b82f6":"#ef4444",correlation:e.correlation,name:e.entityName,x:e.xValue,y:e.yValue})))||[]}},[m,l]);if(u)return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"size-5"}),"Cross-Entity Correlations"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{data:null,error:null,isLoading:!0,children:()=>null})})]});if(x)return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"size-5"}),"Cross-Entity Correlations"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:x})})]});let p=null!=(s=null==m?void 0:m.metrics)?s:{employeeVehicle:0,overallEfficiency:0,performanceWorkload:0,taskDelegation:0};return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"size-5"}),"Cross-Entity Correlations"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{className:"text-xs",variant:"secondary",children:[h.length," relationships"]}),(0,a.jsx)(y.$,{size:"sm",variant:"ghost",children:(0,a.jsx)(sl.A,{className:"size-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 md:grid-cols-4",children:[(0,a.jsx)(t1,{description:"Performance vs Vehicle Usage",icon:(0,a.jsx)(c.A,{className:"size-4"}),title:"Employee-Vehicle",value:p.employeeVehicle||0}),(0,a.jsx)(t1,{description:"Task Complexity vs Success",icon:(0,a.jsx)(i.A,{className:"size-4"}),title:"Task-Delegation",value:p.taskDelegation||0}),(0,a.jsx)(t1,{description:"Workload vs Performance",icon:(0,a.jsx)(r.A,{className:"size-4"}),title:"Performance-Workload",value:p.performanceWorkload||0}),(0,a.jsx)(t1,{description:"System-wide Correlation",icon:(0,a.jsx)(o.A,{className:"size-4"}),title:"Overall Efficiency",value:p.overallEfficiency||0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"mb-3 text-sm font-medium",children:"Relationship Visualization"}),h.length>0?(0,a.jsx)(sw.u,{height:300,width:"100%",children:(0,a.jsxs)(tQ.t,{data:h,margin:{bottom:20,left:20,right:30,top:20},children:[(0,a.jsx)(ta.d,{stroke:"#f0f0f0",strokeDasharray:"3 3"}),(0,a.jsx)(tl.W,{dataKey:"x",fontSize:12,name:"X-Axis",type:"number"}),(0,a.jsx)(tr.h,{dataKey:"y",fontSize:12,name:"Y-Axis",type:"number"}),d&&(0,a.jsx)(sS.m,{content:(0,a.jsx)(t0,{})}),(0,a.jsx)(tH.X,{dataKey:"y",fill:"#3b82f6",children:h.map((e,s)=>(0,a.jsx)(sC.f,{fill:e.color},"cell-".concat(s)))})]})}):(0,a.jsx)("div",{className:"flex h-64 items-center justify-center text-gray-500",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.A,{className:"mx-auto mb-2 size-12 opacity-50"}),(0,a.jsx)("p",{children:"No correlation data available"})]})})]}),(null==m?void 0:m.insights)&&m.insights.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium",children:"Key Insights"}),(0,a.jsx)("div",{className:"space-y-2",children:m.insights.slice(0,3).map((e,s)=>(0,a.jsx)("div",{className:"rounded-lg border border-blue-200 bg-blue-50 p-3",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(r.A,{className:"mt-0.5 size-4 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-blue-800",children:e.title}),(0,a.jsx)("p",{className:"mt-1 text-xs text-blue-700",children:e.description})]})]})},s))})]})]})]})};var t4=t(19968);let t5=e=>{let{entity:s,size:t="md"}=e;return(0,a.jsxs)("div",{className:(0,en.cn)("border rounded-lg flex items-center gap-2",(e=>{switch(e){case"employee":return"bg-blue-100 text-blue-700 border-blue-200";case"vehicle":return"bg-green-100 text-green-700 border-green-200";case"task":return"bg-orange-100 text-orange-700 border-orange-200";case"delegation":return"bg-purple-100 text-purple-700 border-purple-200";default:return"bg-gray-100 text-gray-700 border-gray-200"}})(s.type),{sm:"p-2 text-xs",md:"p-3 text-sm",lg:"p-4 text-base"}[t]),children:[(e=>{switch(e){case"employee":return(0,a.jsx)(c.A,{className:"h-4 w-4"});case"vehicle":return(0,a.jsx)(n.A,{className:"h-4 w-4"});case"task":return(0,a.jsx)(i.A,{className:"h-4 w-4"});case"delegation":return(0,a.jsx)(d.A,{className:"h-4 w-4"});default:return(0,a.jsx)(o.A,{className:"h-4 w-4"})}})(s.type),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"font-medium truncate",children:s.name}),(0,a.jsxs)("div",{className:"text-xs opacity-75",children:[s.connections," connections"]})]}),(0,a.jsxs)(v.E,{variant:"outline",className:"text-xs",children:[Math.round(100*s.strength),"%"]})]})},t3=e=>{let{from:s,to:t,strength:l,type:r}=e;return(0,a.jsx)("div",{className:(0,en.cn)("p-3 border rounded-lg",(e=>e>=.8?"border-green-500 bg-green-50":e>=.6?"border-blue-500 bg-blue-50":e>=.4?"border-orange-500 bg-orange-50":"border-gray-500 bg-gray-50")(l)),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-1",children:[(0,a.jsx)(t5,{entity:s,size:"sm"}),(0,a.jsx)(t4.A,{className:"h-4 w-4 text-gray-400"}),(0,a.jsx)(t5,{entity:t,size:"sm"})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)(v.E,{variant:"outline",className:"text-xs",children:[Math.round(100*l),"%"]}),(0,a.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:r})]})]})})},t6=e=>{let{filters:s,className:t="",maxConnections:l=10,showDetails:r=!0}=e,{data:i,isLoading:n,error:c}=tX(s),{entities:d,connections:m}=(0,g.useMemo)(()=>{var e,s;if(!(null==i?void 0:i.network))return{entities:[],connections:[]};let t=(null==(e=i.network.nodes)?void 0:e.map(e=>({id:e.id,name:e.name,type:e.type,connections:e.connectionCount||0,strength:e.strength||0})))||[],a=(null==(s=i.network.edges)?void 0:s.slice(0,l).map(e=>({from:t.find(s=>s.id===e.from)||t[0],to:t.find(s=>s.id===e.to)||t[0],strength:e.weight||0,type:e.type||"related"})))||[];return{entities:t,connections:a}},[i,l]);if(n)return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),"Entity Relationship Network"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e6.gO,{isLoading:!0,data:null,error:null,children:()=>null})})]});if(c)return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),"Entity Relationship Network"]})}),(0,a.jsx)(N.Wu,{children:(0,a.jsx)(e8,{error:c})})]});let x=d.length,u=m.length,h=m.length>0?m.reduce((e,s)=>e+s.strength,0)/m.length:0,p=d.reduce((e,s)=>(e[s.type]=(e[s.type]||0)+1,e),{});return(0,a.jsxs)(N.Zp,{className:t,children:[(0,a.jsx)(N.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),"Entity Relationship Network"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:[x," entities"]}),(0,a.jsxs)(v.E,{variant:"outline",className:"text-xs",children:[u," connections"]}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(sl.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-3 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-700",children:p.employee||0}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:"Employees"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-700",children:p.vehicle||0}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:"Vehicles"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-orange-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-700",children:p.task||0}),(0,a.jsx)("div",{className:"text-xs text-orange-600",children:"Tasks"})]}),(0,a.jsxs)("div",{className:"text-center p-3 bg-purple-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-700",children:p.delegation||0}),(0,a.jsx)("div",{className:"text-xs text-purple-600",children:"Delegations"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Most Connected Entities"}),(0,a.jsx)("div",{className:"space-y-2",children:d.sort((e,s)=>s.connections-e.connections).slice(0,5).map((e,s)=>(0,a.jsx)(t5,{entity:e},e.id))})]}),r&&m.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-3",children:"Strongest Connections"}),(0,a.jsx)("div",{className:"space-y-3",children:m.sort((e,s)=>s.strength-e.strength).slice(0,5).filter(e=>e.from&&e.to).map((e,s)=>(0,a.jsx)(t3,{from:e.from,to:e.to,strength:e.strength,type:e.type},"".concat(e.from.id,"-").concat(e.to.id,"-").concat(s)))})]}),(0,a.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"Network Health"}),(0,a.jsx)(v.E,{variant:h>.7?"default":h>.4?"secondary":"destructive",className:"text-xs",children:h>.7?"Strong":h>.4?"Moderate":"Weak"})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:["Average connection strength:"," ",Math.round(100*h),"%"]})]})]})]})},t8={entityType:"reporting",title:"Reporting Dashboard",description:"Comprehensive reporting and analytics dashboard",viewModes:["grid","list"],defaultViewMode:"grid",tabs:[{id:"overview",label:"Overview",icon:(0,a.jsx)(l.A,{className:"h-4 w-4"}),description:"High-level analytics and status distribution.",widgets:[{id:"delegation-status",component:ts,span:"lg:col-span-1"},{id:"delegation-trend",component:tc,span:"lg:col-span-2"},{id:"location-distribution",component:tu,span:"lg:col-span-2"},{id:"task-metrics",component:tw,span:"lg:col-span-1"}]},{id:"details",label:"Detailed Report",icon:(0,a.jsx)(x.A,{className:"h-4 w-4"}),description:"In-depth data table with filtering and sorting.",widgets:[{id:"reporting-table",component:()=>{var e,s,t;let l=s4(),[r,i]=g.useState({page:1,pageSize:10}),n=(0,tk.useRouter)(),{toast:c}=(0,tA.dj)(),{data:o,error:d,isLoading:m}=sV(l,r),x=e=>{n.push("/delegations/".concat(e.id))},u=[{accessorKey:"delegationId",header:"Delegation ID"},{accessorKey:"customerName",header:"Customer"},{accessorKey:"vehicleModel",header:"Vehicle"},{accessorKey:"status",cell:e=>{let{row:s}=e;return(0,a.jsx)(v.E,{children:s.original.status})},header:"Status"},{accessorKey:"location",header:"Location"},{accessorKey:"createdAt",cell:e=>{let{row:s}=e;return(0,er.GP)(new Date(s.original.createdAt),"PPpp")},header:"Date"},{cell:e=>{let{row:s}=e;return(0,a.jsxs)(y.$,{size:"sm",variant:"outline",onClick:()=>x(s.original),className:"flex items-center gap-1",children:[(0,a.jsx)(eb.A,{className:"h-3 w-3"}),"View"]})},id:"actions",header:"Actions"}],h=(0,sp.N4)({columns:u,data:null!=(e=null==o?void 0:o.data)?e:[],getCoreRowModel:(0,sg.HT)(),manualPagination:!0,rowCount:null!=(s=null==o?void 0:o.meta.total)?s:0});return d?(0,a.jsxs)($.Fc,{variant:"destructive",children:[(0,a.jsx)($.XL,{children:"Error"}),(0,a.jsx)($.TN,{children:d.message})]}):(0,a.jsxs)(N.Zp,{children:[(0,a.jsx)(N.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(N.ZB,{children:"Detailed Report"}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)(y.$,{variant:"outline",size:"sm",onClick:()=>{if(!(null==o?void 0:o.data.length))return void c({title:"No Data",description:"No data available to export.",variant:"destructive"});let e=new Blob([["ID,Customer,Vehicle,Status,Location,Created At,Completed At",...o.data.map(e=>[e.delegationId,e.customerName,e.vehicleModel,e.status,e.location,e.createdAt,e.completedAt||"N/A"].join(","))].join("\n")],{type:"text/csv"}),s=window.URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download="delegation-report-".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(t),t.click(),document.body.removeChild(t),window.URL.revokeObjectURL(s),c({title:"Export Successful",description:"Delegation data has been exported to CSV."})},disabled:!(null==o?void 0:o.data.length),className:"flex items-center gap-1",children:[(0,a.jsx)(p.A,{className:"h-3 w-3"}),"Export"]})})]})}),(0,a.jsxs)(N.Wu,{children:[(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(sh.XI,{children:[(0,a.jsx)(sh.A0,{children:h.getHeaderGroups().map(e=>(0,a.jsx)(sh.Hj,{children:e.headers.map(e=>(0,a.jsx)(sh.nd,{children:(0,sp.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)(sh.BF,{children:m?Array.from({length:r.pageSize}).map((e,s)=>(0,a.jsx)(sh.Hj,{children:u.map((e,t)=>{var l;return(0,a.jsx)(sh.nA,{children:(0,a.jsx)(s7.E,{className:"h-6 w-full"})},"loading-cell-".concat(s,"-").concat(null!=(l=e.id)?l:t))})},"loading-row-".concat(s))):h.getRowModel().rows.map(e=>(0,a.jsx)(sh.Hj,{children:e.getVisibleCells().map(e=>(0,a.jsx)(sh.nA,{children:(0,sp.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]})}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Page ",null==o?void 0:o.meta.page," of ",null==o?void 0:o.meta.totalPages," (",null==o?void 0:o.meta.total," total records)"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(y.$,{disabled:1===r.page,onClick:()=>i(e=>({...e,page:e.page-1})),size:"sm",variant:"outline",children:"Previous"}),(0,a.jsx)(y.$,{disabled:r.page>=(null!=(t=null==o?void 0:o.meta.totalPages)?t:1),onClick:()=>i(e=>({...e,page:e.page+1})),size:"sm",variant:"outline",children:"Next"})]})]})]})]})},span:"lg:col-span-3 xl:col-span-4"}]}]};t8.tabs.flatMap(e=>e.widgets),Date.now(),Date.now(),new Date().getFullYear();let t7=e=>{let{compact:s=!1,className:t=""}=e,l=s4(),{setDateRange:r}=s5(),{validationErrors:i}=s6(),[n,c]=g.useState(!1),o=e=>{(null==e?void 0:e.from)&&(null==e?void 0:e.to)&&(r(e.from,e.to),c(!1))},d=[{label:"Last 7 days",getValue:()=>({from:new Date(Date.now()-6048e5),to:new Date})},{label:"Last 30 days",getValue:()=>({from:new Date(Date.now()-2592e6),to:new Date})},{label:"Last 90 days",getValue:()=>({from:new Date(Date.now()-7776e6),to:new Date})},{label:"This year",getValue:()=>({from:new Date(new Date().getFullYear(),0,1),to:new Date})}],m=e=>{r(e.from,e.to),c(!1)},x=()=>{let{from:e,to:t}=l.dateRange;return e&&t?s?"".concat((0,er.GP)(e,"MMM d")," - ").concat((0,er.GP)(t,"MMM d")):"".concat((0,er.GP)(e,"MMM d, yyyy")," - ").concat((0,er.GP)(t,"MMM d, yyyy")):"Select date range"},u=i.dateRange;return s?(0,a.jsxs)("div",{className:(0,en.cn)("space-y-1",t),children:[(0,a.jsx)(S.J,{className:"text-xs font-medium",children:"Date Range"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-start text-left font-normal",!l.dateRange.from&&"text-muted-foreground",u&&"border-red-500"),children:[(0,a.jsx)(M.A,{className:"mr-2 h-4 w-4"}),x()]})}),(0,a.jsx)(Z.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2 mb-3",children:d.map(e=>(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>m(e.getValue()),className:"text-xs",children:e.label},e.label))}),(0,a.jsx)(U.V,{mode:"range",selected:{from:l.dateRange.from,to:l.dateRange.to},onSelect:o,numberOfMonths:1,className:"rounded-md border"})]})})]}),u&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:u})]}):(0,a.jsxs)("div",{className:(0,en.cn)("space-y-2",t),children:[(0,a.jsx)(S.J,{className:"text-sm font-medium",children:"Date Range"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-start text-left font-normal",!l.dateRange.from&&"text-muted-foreground",u&&"border-red-500"),children:[(0,a.jsx)(M.A,{className:"mr-2 h-4 w-4"}),x()]})}),(0,a.jsx)(Z.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Quick Ranges"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:d.map(e=>(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>m(e.getValue()),className:"text-sm",children:e.label},e.label))})]}),(0,a.jsx)(U.V,{mode:"range",selected:{from:l.dateRange.from,to:l.dateRange.to},onSelect:o,numberOfMonths:2,className:"rounded-md border"})]})})]}),u&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:u})]})};var t9=t(79556);let ae=e=>{let{compact:s=!1,className:t=""}=e,l=s4(),{setStatus:r}=s5(),{validationErrors:i}=s6(),[n,c]=g.useState(!1),o=[{value:"DRAFT",label:"Draft",color:"bg-gray-100 text-gray-800"},{value:"PENDING",label:"Pending",color:"bg-yellow-100 text-yellow-800"},{value:"APPROVED",label:"Approved",color:"bg-blue-100 text-blue-800"},{value:"IN_PROGRESS",label:"In Progress",color:"bg-purple-100 text-purple-800"},{value:"COMPLETED",label:"Completed",color:"bg-green-100 text-green-800"},{value:"CANCELLED",label:"Cancelled",color:"bg-red-100 text-red-800"}],d=e=>{let s=l.status;s.includes(e)?r(s.filter(s=>s!==e)):r([...s,e])},m=()=>{r(o.map(e=>e.value))},x=()=>{r([])},u=()=>{let e=l.status.length;if(0===e)return"All statuses";if(1===e){let e=o.find(e=>e.value===l.status[0]);return(null==e?void 0:e.label)||"Unknown"}return"".concat(e," statuses")},h=i.status;return s?(0,a.jsxs)("div",{className:(0,en.cn)("space-y-1",t),children:[(0,a.jsx)(S.J,{className:"text-xs font-medium",children:"Status"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",h&&"border-red-500"),children:[(0,a.jsx)("span",{className:"truncate",children:u()}),(0,a.jsx)(t9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-56 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:m,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:x,children:"None"})]}),(0,a.jsx)("div",{className:"space-y-2",children:o.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A.S,{id:"status-".concat(e.value),checked:l.status.includes(e.value),onCheckedChange:()=>d(e.value)}),(0,a.jsx)(S.J,{htmlFor:"status-".concat(e.value),className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsx)(v.E,{variant:"secondary",className:(0,en.cn)("text-xs",e.color),children:e.label})})]},e.value))})]})})]}),h&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:h})]}):(0,a.jsxs)("div",{className:(0,en.cn)("space-y-2",t),children:[(0,a.jsx)(S.J,{className:"text-sm font-medium",children:"Status"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",h&&"border-red-500"),children:[(0,a.jsx)("span",{children:u()}),(0,a.jsx)(t9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-64 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Select Statuses"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:m,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:x,children:"None"})]})]}),(0,a.jsx)("div",{className:"space-y-3",children:o.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(A.S,{id:"status-".concat(e.value),checked:l.status.includes(e.value),onCheckedChange:()=>d(e.value)}),(0,a.jsx)(S.J,{htmlFor:"status-".concat(e.value),className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsx)(v.E,{variant:"secondary",className:(0,en.cn)("text-sm",e.color),children:e.label})})]},e.value))})]})})]}),l.status.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:l.status.map(e=>{let s=o.find(s=>s.value===e);return s?(0,a.jsxs)(v.E,{variant:"secondary",className:(0,en.cn)("text-xs pr-1",s.color),children:[s.label,(0,a.jsx)(y.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>d(e),children:(0,a.jsx)(_.A,{className:"h-3 w-3"})})]},e):null})}),h&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:h})]})},as=e=>{let{compact:s=!1,className:t=""}=e,l=s4(),{setLocations:r}=s5(),{validationErrors:i}=s6(),[n,c]=g.useState(!1),[o,d]=g.useState(""),m=["New York, NY","Los Angeles, CA","Chicago, IL","Houston, TX","Phoenix, AZ","Philadelphia, PA","San Antonio, TX","San Diego, CA","Dallas, TX","San Jose, CA","Austin, TX","Jacksonville, FL","Fort Worth, TX","Columbus, OH","Charlotte, NC","San Francisco, CA","Indianapolis, IN","Seattle, WA","Denver, CO","Washington, DC"].filter(e=>e.toLowerCase().includes(o.toLowerCase())),x=e=>{let s=l.locations;s.includes(e)?r(s.filter(s=>s!==e)):r([...s,e])},u=()=>{r(m)},h=()=>{r([])},p=()=>{let e=l.locations.length;return 0===e?"All locations":1===e?l.locations[0]:"".concat(e," locations")},j=i.locations;return s?(0,a.jsxs)("div",{className:(0,en.cn)("space-y-1",t),children:[(0,a.jsx)(S.J,{className:"text-xs font-medium",children:"Location"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",j&&"border-red-500"),children:[(0,a.jsx)("span",{className:"truncate",children:p()}),(0,a.jsx)(t9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-64 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)(eu.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(D.p,{placeholder:"Search locations...",value:o,onChange:e=>d(e.target.value),className:"pl-8"})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:u,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:h,children:"None"})]}),(0,a.jsx)("div",{className:"max-h-48 overflow-y-auto space-y-2",children:m.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A.S,{id:"location-".concat(e),checked:l.locations.includes(e),onCheckedChange:()=>x(e)}),(0,a.jsxs)(S.J,{htmlFor:"location-".concat(e),className:"text-sm font-normal cursor-pointer flex-1 flex items-center gap-2",children:[(0,a.jsx)(tm.A,{className:"h-3 w-3 text-muted-foreground"}),e]})]},e))})]})})]}),j&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:j})]}):(0,a.jsxs)("div",{className:(0,en.cn)("space-y-2",t),children:[(0,a.jsx)(S.J,{className:"text-sm font-medium",children:"Locations"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",j&&"border-red-500"),children:[(0,a.jsx)("span",{children:p()}),(0,a.jsx)(t9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-80 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Select Locations"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:u,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:h,children:"None"})]})]}),(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)(eu.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(D.p,{placeholder:"Search locations...",value:o,onChange:e=>d(e.target.value),className:"pl-9"})]}),(0,a.jsx)("div",{className:"max-h-64 overflow-y-auto space-y-3",children:m.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(A.S,{id:"location-".concat(e),checked:l.locations.includes(e),onCheckedChange:()=>x(e)}),(0,a.jsxs)(S.J,{htmlFor:"location-".concat(e),className:"text-sm font-normal cursor-pointer flex-1 flex items-center gap-2",children:[(0,a.jsx)(tm.A,{className:"h-4 w-4 text-muted-foreground"}),e]})]},e))})]})})]}),l.locations.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[l.locations.slice(0,3).map(e=>(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs pr-1",children:[e.length>15?"".concat(e.slice(0,15),"..."):e,(0,a.jsx)(y.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>x(e),children:(0,a.jsx)(_.A,{className:"h-3 w-3"})})]},e)),l.locations.length>3&&(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["+",l.locations.length-3," more"]})]}),j&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:j})]})},at=e=>{let{compact:s=!1,className:t=""}=e,l=s4(),{setEmployees:r}=s5(),{validationErrors:i}=s6(),[n,c]=g.useState(!1),[o,d]=g.useState(""),m=[{id:1,name:"John Smith",department:"Operations"},{id:2,name:"Sarah Johnson",department:"Sales"},{id:3,name:"Mike Davis",department:"Engineering"},{id:4,name:"Emily Brown",department:"Marketing"},{id:5,name:"David Wilson",department:"Operations"},{id:6,name:"Lisa Anderson",department:"HR"},{id:7,name:"Tom Miller",department:"Finance"},{id:8,name:"Anna Garcia",department:"Sales"},{id:9,name:"Chris Taylor",department:"Engineering"},{id:10,name:"Jessica Lee",department:"Marketing"}],x=m.filter(e=>e.name.toLowerCase().includes(o.toLowerCase())||e.department.toLowerCase().includes(o.toLowerCase())),u=e=>{let s=l.employees;s.includes(e)?r(s.filter(s=>s!==e)):r([...s,e])},h=()=>{r(x.map(e=>e.id))},p=()=>{r([])},j=()=>{let e=l.employees.length;if(0===e)return"All employees";if(1===e){let e=m.find(e=>e.id===l.employees[0]);return(null==e?void 0:e.name)||"Unknown"}return"".concat(e," employees")},f=i.employees;return s?(0,a.jsxs)("div",{className:(0,en.cn)("space-y-1",t),children:[(0,a.jsx)(S.J,{className:"text-xs font-medium",children:"Employee"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",f&&"border-red-500"),children:[(0,a.jsx)("span",{className:"truncate",children:j()}),(0,a.jsx)(t9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-64 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)(eu.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(D.p,{placeholder:"Search employees...",value:o,onChange:e=>d(e.target.value),className:"pl-8"})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:h,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:p,children:"None"})]}),(0,a.jsx)("div",{className:"max-h-48 overflow-y-auto space-y-2",children:x.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A.S,{id:"employee-".concat(e.id),checked:l.employees.includes(e.id),onCheckedChange:()=>u(e.id)}),(0,a.jsx)(S.J,{htmlFor:"employee-".concat(e.id),className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-3 w-3 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.department})]})]})})]},e.id))})]})})]}),f&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:f})]}):(0,a.jsxs)("div",{className:(0,en.cn)("space-y-2",t),children:[(0,a.jsx)(S.J,{className:"text-sm font-medium",children:"Employees"}),(0,a.jsxs)(Z.AM,{open:n,onOpenChange:c,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",f&&"border-red-500"),children:[(0,a.jsx)("span",{children:j()}),(0,a.jsx)(t9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-80 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Select Employees"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:h,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:p,children:"None"})]})]}),(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)(eu.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(D.p,{placeholder:"Search employees...",value:o,onChange:e=>d(e.target.value),className:"pl-9"})]}),(0,a.jsx)("div",{className:"max-h-64 overflow-y-auto space-y-3",children:x.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(A.S,{id:"employee-".concat(e.id),checked:l.employees.includes(e.id),onCheckedChange:()=>u(e.id)}),(0,a.jsx)(S.J,{htmlFor:"employee-".concat(e.id),className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.department})]})]})})]},e.id))})]})})]}),l.employees.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[l.employees.slice(0,3).map(e=>{let s=m.find(s=>s.id===e);return s?(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs pr-1",children:[s.name,(0,a.jsx)(y.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>u(e),children:(0,a.jsx)(_.A,{className:"h-3 w-3"})})]},e):null}),l.employees.length>3&&(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["+",l.employees.length-3," more"]})]}),f&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:f})]})},aa=e=>{let{compact:s=!1,className:t=""}=e,l=s4(),{setVehicles:r}=s5(),{validationErrors:i}=s6(),[c,o]=g.useState(!1),[d,m]=g.useState(""),x=[{id:1,make:"Toyota",model:"Camry",year:2022,licensePlate:"ABC-123"},{id:2,make:"Honda",model:"Accord",year:2021,licensePlate:"DEF-456"},{id:3,make:"Ford",model:"F-150",year:2023,licensePlate:"GHI-789"},{id:4,make:"Chevrolet",model:"Silverado",year:2022,licensePlate:"JKL-012"},{id:5,make:"BMW",model:"X5",year:2021,licensePlate:"MNO-345"},{id:6,make:"Mercedes",model:"E-Class",year:2023,licensePlate:"PQR-678"},{id:7,make:"Audi",model:"A4",year:2022,licensePlate:"STU-901"},{id:8,make:"Nissan",model:"Altima",year:2021,licensePlate:"VWX-234"}],u=x.filter(e=>e.make.toLowerCase().includes(d.toLowerCase())||e.model.toLowerCase().includes(d.toLowerCase())||e.licensePlate.toLowerCase().includes(d.toLowerCase())),h=e=>{let s=l.vehicles;s.includes(e)?r(s.filter(s=>s!==e)):r([...s,e])},p=()=>{r(u.map(e=>e.id))},j=()=>{r([])},f=()=>{let e=l.vehicles.length;if(0===e)return"All vehicles";if(1===e){let e=x.find(e=>e.id===l.vehicles[0]);return e?"".concat(e.make," ").concat(e.model):"Unknown"}return"".concat(e," vehicles")},N=i.vehicles;return s?(0,a.jsxs)("div",{className:(0,en.cn)("space-y-1",t),children:[(0,a.jsx)(S.J,{className:"text-xs font-medium",children:"Vehicle"}),(0,a.jsxs)(Z.AM,{open:c,onOpenChange:o,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",N&&"border-red-500"),children:[(0,a.jsx)("span",{className:"truncate",children:f()}),(0,a.jsx)(t9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-64 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-3",children:[(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)(eu.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(D.p,{placeholder:"Search vehicles...",value:d,onChange:e=>m(e.target.value),className:"pl-8"})]}),(0,a.jsxs)("div",{className:"flex justify-between mb-3",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:p,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:j,children:"None"})]}),(0,a.jsx)("div",{className:"max-h-48 overflow-y-auto space-y-2",children:u.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(A.S,{id:"vehicle-".concat(e.id),checked:l.vehicles.includes(e.id),onCheckedChange:()=>h(e.id)}),(0,a.jsx)(S.J,{htmlFor:"vehicle-".concat(e.id),className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-3 w-3 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.make," ",e.model]}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.licensePlate})]})]})})]},e.id))})]})})]}),N&&(0,a.jsx)("p",{className:"text-xs text-red-600",children:N})]}):(0,a.jsxs)("div",{className:(0,en.cn)("space-y-2",t),children:[(0,a.jsx)(S.J,{className:"text-sm font-medium",children:"Vehicles"}),(0,a.jsxs)(Z.AM,{open:c,onOpenChange:o,children:[(0,a.jsx)(Z.Wv,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",className:(0,en.cn)("w-full justify-between text-left font-normal",N&&"border-red-500"),children:[(0,a.jsx)("span",{children:f()}),(0,a.jsx)(t9.A,{className:"ml-2 h-4 w-4 shrink-0"})]})}),(0,a.jsx)(Z.hl,{className:"w-80 p-0",align:"start",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:"Select Vehicles"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:p,children:"All"}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:j,children:"None"})]})]}),(0,a.jsxs)("div",{className:"relative mb-4",children:[(0,a.jsx)(eu.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(D.p,{placeholder:"Search vehicles...",value:d,onChange:e=>m(e.target.value),className:"pl-9"})]}),(0,a.jsx)("div",{className:"max-h-64 overflow-y-auto space-y-3",children:u.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(A.S,{id:"vehicle-".concat(e.id),checked:l.vehicles.includes(e.id),onCheckedChange:()=>h(e.id)}),(0,a.jsx)(S.J,{htmlFor:"vehicle-".concat(e.id),className:"text-sm font-normal cursor-pointer flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(n.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.make," ",e.model," (",e.year,")"]}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.licensePlate})]})]})})]},e.id))})]})})]}),l.vehicles.length>0&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-1",children:[l.vehicles.slice(0,3).map(e=>{let s=x.find(s=>s.id===e);return s?(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs pr-1",children:[s.make," ",s.model,(0,a.jsx)(y.$,{variant:"ghost",size:"sm",className:"h-auto p-0 ml-1 hover:bg-transparent",onClick:()=>h(e),children:(0,a.jsx)(_.A,{className:"h-3 w-3"})})]},e):null}),l.vehicles.length>3&&(0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["+",l.vehicles.length-3," more"]})]}),N&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:N})]})};var al=t(30462);let ar=()=>{let[e,s]=g.useState({}),[t,a]=g.useState(0),l=g.useCallback(()=>{try{let e=localStorage.getItem("reporting-filter-presets"),t=e?JSON.parse(e):{};s(t)}catch(e){s({})}},[]);return g.useEffect(()=>{l()},[l,t]),{presets:e,refreshPresets:g.useCallback(()=>{a(e=>e+1)},[])}},ai=e=>{let{className:s=""}=e,{applyPreset:t,saveAsPreset:l,deletePreset:r}=s8(),i=s4(),{presets:n,refreshPresets:c}=ar(),[o,d]=g.useState(!1),[m,x]=g.useState(""),u=g.useCallback(()=>{m.trim()&&(l(m.trim()),x(""),d(!1),c())},[m,l,c]),h=g.useCallback(e=>{t(e)},[t]),p=g.useCallback(e=>{r(e),c()},[r,c]),j=g.useCallback(e=>{var s,t,a,l;let r=[];return(null==(s=e.status)?void 0:s.length)>0&&r.push("".concat(e.status.length," status")),(null==(t=e.locations)?void 0:t.length)>0&&r.push("".concat(e.locations.length," locations")),(null==(a=e.employees)?void 0:a.length)>0&&r.push("".concat(e.employees.length," employees")),(null==(l=e.vehicles)?void 0:l.length)>0&&r.push("".concat(e.vehicles.length," vehicles")),r.length>0?r.join(", "):"No filters"},[]),v=[{name:"Last 30 Days",description:"All delegations from the last 30 days",action:()=>{console.log("Applying 30-day preset",{dateRange:{from:new Date(Date.now()-2592e6),to:new Date},status:[],locations:[],employees:[],vehicles:[]})}},{name:"Active Delegations",description:"In progress and approved delegations",action:()=>{console.log("Applying active delegations preset")}},{name:"Completed This Month",description:"Completed delegations from current month",action:()=>{console.log("Applying completed this month preset")}}],f=Object.keys(n);return(0,a.jsxs)("div",{className:(0,en.cn)("space-y-3",s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(S.J,{className:"text-sm font-medium",children:"Filter Presets"}),(0,a.jsxs)(eY.lG,{open:o,onOpenChange:d,children:[(0,a.jsx)(eY.zM,{asChild:!0,children:(0,a.jsxs)(y.$,{variant:"outline",size:"sm",className:"h-8",children:[(0,a.jsx)(eR.A,{className:"h-4 w-4 mr-2"}),"Save"]})}),(0,a.jsxs)(eY.Cf,{className:"sm:max-w-md",children:[(0,a.jsxs)(eY.c7,{children:[(0,a.jsx)(eY.L3,{children:"Save Filter Preset"}),(0,a.jsx)(eY.rr,{children:"Save your current filter settings as a preset for quick access later."})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.J,{htmlFor:"preset-name",children:"Preset Name"}),(0,a.jsx)(D.p,{id:"preset-name",placeholder:"Enter preset name...",value:m,onChange:e=>x(e.target.value),onKeyDown:e=>{"Enter"===e.key&&u()}})]}),(0,a.jsxs)("div",{className:"p-3 bg-muted rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:"Current filters:"}),(0,a.jsx)("p",{className:"text-sm",children:j(i)})]})]}),(0,a.jsxs)(eY.Es,{children:[(0,a.jsx)(y.$,{variant:"outline",onClick:()=>d(!1),children:"Cancel"}),(0,a.jsx)(y.$,{onClick:u,disabled:!m.trim(),children:"Save Preset"})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Quick Presets"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:v.map(e=>(0,a.jsxs)(y.$,{variant:"outline",size:"sm",onClick:e.action,className:"h-8 text-xs",children:[(0,a.jsx)(al.A,{className:"h-3 w-3 mr-1"}),e.name]},e.name))})]}),f.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Saved Presets"}),(0,a.jsx)("div",{className:"space-y-1",children:f.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 rounded-lg border bg-card",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium truncate",children:e}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:j(n[e])})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>h(e),className:"h-8 px-2 text-xs",children:"Apply"}),(0,a.jsxs)(sa.rI,{children:[(0,a.jsx)(sa.ty,{asChild:!0,children:(0,a.jsx)(y.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:(0,a.jsx)(t9.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(sa.SQ,{align:"end",children:[(0,a.jsx)(sa._2,{onClick:()=>h(e),children:"Apply Preset"}),(0,a.jsx)(sa.mB,{}),(0,a.jsxs)(sa._2,{onClick:()=>p(e),className:"text-red-600",children:[(0,a.jsx)(eF.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]})]},e))})]})]})};var an=t(8531),ac=t(10518);let ao=e=>{let{className:s="",showPresets:t=!0,compact:l=!1,includeServiceFilters:r=!1,includeTaskFilters:i=!1}=e,n=s4(),{resetFilters:c,applyFilters:o,revertChanges:d}=s5(),{isFilterPanelOpen:m,hasUnsavedChanges:x,setFilterPanelOpen:h}=s3(),{validationErrors:p,isValid:j}=s6(),f=()=>{c()},b=()=>{j&&o()},w=g.useMemo(()=>{let e=0;if(n.status.length>0&&e++,n.locations.length>0&&e++,n.employees.length>0&&e++,n.vehicles.length>0&&e++,r){var s,t;(null==(s=n.serviceTypes)?void 0:s.length)&&e++,(null==(t=n.serviceStatus)?void 0:t.length)&&e++,n.costRange&&e++}return i&&n.includeTaskData&&e++,e},[n,r,i]),k=g.useMemo(()=>{let e=[];if(n.status.length>0&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Status: ",n.status.length]},"status")),n.locations.length>0&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Locations: ",n.locations.length]},"locations")),n.employees.length>0&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Employees: ",n.employees.length]},"employees")),n.vehicles.length>0&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Vehicles: ",n.vehicles.length]},"vehicles")),r){var s,t;(null==(s=n.serviceTypes)?void 0:s.length)&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Service Types: ",n.serviceTypes.length]},"serviceTypes")),(null==(t=n.serviceStatus)?void 0:t.length)&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Service Status: ",n.serviceStatus.length]},"serviceStatus")),n.costRange&&e.push((0,a.jsxs)(v.E,{variant:"secondary",className:"text-xs",children:["Cost: $",n.costRange.min," - $",n.costRange.max]},"costRange"))}return i&&n.includeTaskData&&e.push((0,a.jsx)(v.E,{variant:"secondary",className:"text-xs",children:"Task Data Included"},"taskData")),e},[n,r,i]),A=()=>{let e=Object.values(p);return 0===e.length?null:(0,a.jsx)("div",{className:"space-y-1",children:e.map((e,s)=>(0,a.jsx)("div",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:e},s))})};return l?(0,a.jsxs)("div",{className:"space-y-4 ".concat(s),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Filters"}),w>0&&(0,a.jsx)(v.E,{variant:"default",className:"text-xs",children:w})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:f,children:(0,a.jsx)(an.A,{className:"h-4 w-4"})}),(0,a.jsx)(y.$,{variant:"default",size:"sm",onClick:b,disabled:!j||!x,children:(0,a.jsx)(ac.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,a.jsx)(t7,{compact:!0}),(0,a.jsx)(ae,{compact:!0}),(0,a.jsx)(as,{compact:!0}),(0,a.jsx)(at,{compact:!0}),(0,a.jsx)(aa,{compact:!0})]}),A()]}):(0,a.jsxs)(N.Zp,{className:s,children:[(0,a.jsxs)(N.aR,{className:"pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(N.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5"}),"Reporting Filters",w>0&&(0,a.jsxs)(v.E,{variant:"default",className:"text-xs",children:[w," active"]})]}),(0,a.jsx)(y.$,{variant:"ghost",size:"sm",onClick:()=>{h(!1)},className:"h-8 w-8 p-0",children:(0,a.jsx)(_.A,{className:"h-4 w-4"})})]}),k.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:k})]}),(0,a.jsxs)(N.Wu,{className:"space-y-6",children:[t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ai,{}),(0,a.jsx)(T.w,{})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(t7,{}),(0,a.jsx)(ae,{}),(0,a.jsx)(as,{}),(0,a.jsx)(at,{}),(0,a.jsx)(aa,{})]}),A(),(0,a.jsx)(T.w,{}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(y.$,{variant:"outline",onClick:f,className:"flex items-center gap-2",children:[(0,a.jsx)(an.A,{className:"h-4 w-4"}),"Reset All"]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[x&&(0,a.jsx)(y.$,{variant:"ghost",onClick:()=>{d()},children:"Revert"}),(0,a.jsxs)(y.$,{onClick:b,disabled:!j||!x,className:"flex items-center gap-2",children:[(0,a.jsx)(ac.A,{className:"h-4 w-4"}),"Apply Filters"]})]})]})]})]})},ad=e=>{let{title:s,description:t,children:l,actions:r,filters:i}=e;return(0,a.jsxs)("div",{className:"flex flex-col h-full p-4 md:p-6 lg:p-8 gap-6",children:[(0,a.jsxs)("header",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsx)("h1",{className:"text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100",children:s}),t&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400",children:t})]}),r&&(0,a.jsx)("div",{className:"flex-shrink-0",children:r})]}),i&&(0,a.jsx)("aside",{children:i}),(0,a.jsx)("main",{className:"flex-1",children:l})]})},am=e=>{let{className:s=""}=e,[t,N]=g.useState("overview"),[b,w]=g.useState(!1),k={costRange:{max:1e4,min:0},dateRange:{from:new Date(Date.now()-2592e6),to:new Date},employees:[],includeServiceHistory:!1,includeTaskData:!1,locations:[],serviceStatus:[],serviceTypes:[],status:[],vehicles:[]},A=[{description:"High-level metrics and key performance indicators",icon:(0,a.jsx)(l.A,{className:"size-4"}),id:"overview",label:"Overview"},{description:"Detailed analytics and trend analysis",icon:(0,a.jsx)(r.A,{className:"size-4"}),id:"analytics",label:"Analytics"},{description:"Task metrics and performance analysis",icon:(0,a.jsx)(i.A,{className:"size-4"}),id:"tasks",label:"Tasks"},{description:"Vehicle utilization and maintenance analytics",icon:(0,a.jsx)(n.A,{className:"size-4"}),id:"vehicles",label:"Vehicles"},{description:"Employee performance and workload analysis",icon:(0,a.jsx)(c.A,{className:"size-4"}),id:"employees",label:"Employees"},{description:"Cross-entity relationships and correlations",icon:(0,a.jsx)(o.A,{className:"size-4"}),id:"correlations",label:"Correlations"},{description:"Generate comprehensive data reports for all entities",icon:(0,a.jsx)(d.A,{className:"size-4"}),id:"generation",label:"Generate Reports"},{description:"Manage report types and build custom reports",icon:(0,a.jsx)(m.A,{className:"size-4"}),id:"management",label:"Management"},{description:"Raw delegation data in tabular format",icon:(0,a.jsx)(x.A,{className:"size-4"}),id:"data",label:"Data"}],C=e=>{switch(e){case"overview":return[{component:ts,id:"status",span:"col-span-12 lg:col-span-4"},{component:tw,id:"tasks",span:"col-span-12 lg:col-span-8"},{component:tc,id:"trend",span:"col-span-12 lg:col-span-8"},{component:tu,id:"location",span:"col-span-12 lg:col-span-4"}];case"analytics":return[{component:tc,id:"trend",span:"col-span-12 lg:col-span-8"},{component:tu,id:"location",span:"col-span-12 lg:col-span-4"},{component:tw,id:"tasks",span:"col-span-12 lg:col-span-6"},{component:ts,id:"status",span:"col-span-12 lg:col-span-6"}];case"correlations":return[{component:t2,id:"cross-entity-correlations",span:"col-span-12"},{component:t6,id:"entity-relationships",span:"col-span-12 lg:col-span-6"},{component:tw,id:"task-correlations",span:"col-span-12 lg:col-span-6"}];case"tasks":return[{component:tw,id:"task-metrics",span:"col-span-12"},{component:tb,id:"task-assignments",span:"col-span-12 lg:col-span-6"},{component:th,id:"task-status-chart",span:"col-span-12 lg:col-span-6"}];case"vehicles":return[{component:tD,id:"vehicle-analytics",span:"col-span-12 lg:col-span-6"},{component:tz,id:"vehicle-utilization",span:"col-span-12 lg:col-span-6"},{component:tP,id:"vehicle-maintenance",span:"col-span-12 lg:col-span-6"},{component:tV,id:"vehicle-costs",span:"col-span-12 lg:col-span-6"}];case"employees":return[{component:tU,id:"employee-analytics",span:"col-span-12 lg:col-span-8"},{component:tK,id:"employee-workload",span:"col-span-12 lg:col-span-4"},{component:tJ,id:"employee-performance",span:"col-span-12"}];case"generation":return[{component:()=>(0,a.jsx)(eE,{}),id:"report-generation",span:"col-span-12"}];case"management":return[{component:()=>(0,a.jsx)(su,{}),id:"report-type-manager",span:"col-span-12 lg:col-span-6"},{component:()=>(0,a.jsx)(e0,{}),id:"report-builder",span:"col-span-12 lg:col-span-6"}];case"data":return[{component:()=>(0,a.jsx)(sy,{}),id:"data-table",span:"col-span-12"}];default:return[]}},S=async()=>{try{let e={activeTab:t,filters:k,metadata:{exportedBy:"Reporting Dashboard",version:"2.0.0"},timestamp:new Date().toISOString()},s=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),a=globalThis.URL.createObjectURL(s),l=document.createElement("a");l.href=a,l.download="dashboard-export-".concat(new Date().toISOString().split("T")[0],".json"),document.body.append(l),l.click(),l.remove(),globalThis.URL.revokeObjectURL(a)}catch(e){console.error("Export failed:",e)}},E=()=>{let e=C(t);return(0,a.jsx)("div",{className:"grid grid-cols-12 gap-6",children:e.map(e=>{let{component:s,id:l,span:r}=e;return(0,a.jsx)("div",{className:r,children:(0,a.jsx)(s,{})},"".concat(t,"-").concat(l))})})};return(0,a.jsx)(j.GW,{className:s,config:t8,children:(0,a.jsx)(ad,{actions:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(y.$,{className:b?"bg-primary text-primary-foreground":"",onClick:()=>w(!b),size:"sm",variant:"outline",children:[(0,a.jsx)(u.A,{className:"mr-2 size-4"}),"Filters"]}),(0,a.jsxs)(y.$,{onClick:()=>{globalThis.location.reload()},size:"sm",variant:"outline",children:[(0,a.jsx)(h.A,{className:"mr-2 size-4"}),"Refresh"]}),(0,a.jsxs)(y.$,{onClick:S,size:"sm",variant:"outline",children:[(0,a.jsx)(p.A,{className:"mr-2 size-4"}),"Export"]})]}),description:"Interactive dashboard with real-time metrics and insights",filters:b?(0,a.jsx)(ao,{}):void 0,title:"Reporting Dashboard",children:(0,a.jsxs)("div",{className:"space-y-8",children:[(()=>{let e=[];return(k.status.length>0&&e.push("".concat(k.status.length," status")),k.locations.length>0&&e.push("".concat(k.locations.length," locations")),k.employees.length>0&&e.push("".concat(k.employees.length," employees")),k.vehicles.length>0&&e.push("".concat(k.vehicles.length," vehicles")),0===e.length)?null:(0,a.jsxs)("div",{className:"flex items-center gap-3 text-sm text-muted-foreground",children:[(0,a.jsx)("span",{children:"Active filters:"}),e.map((e,s)=>(0,a.jsx)(v.E,{className:"text-xs",variant:"secondary",children:e},"filter-".concat(s,"-").concat(e)))]})})(),(0,a.jsxs)(f.tU,{className:"w-full",onValueChange:N,value:t,children:[(0,a.jsx)(f.j7,{className:"grid w-full grid-cols-9 h-12",children:A.map(e=>(0,a.jsxs)(f.Xi,{className:"flex items-center gap-2 text-sm font-medium",value:e.id,children:[e.icon,(0,a.jsx)("span",{className:"hidden sm:inline",children:e.label})]},e.id))}),A.map(e=>(0,a.jsxs)(f.av,{className:"space-y-8",value:e.id,children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("h2",{className:"text-2xl font-semibold tracking-tight flex items-center gap-3",children:[e.icon,e.label]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:e.description})]}),E()]},e.id))]})]})})})}},40879:(e,s,t)=>{"use strict";t.d(s,{dj:()=>x,oR:()=>m});var a=t(12115);let l=0,r=new Map,i=e=>{if(r.has(e))return;let s=setTimeout(()=>{r.delete(e),d({toastId:e,type:"REMOVE_TOAST"})},1e6);r.set(e,s)},n=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"DISMISS_TOAST":{let{toastId:t}=s;if(t)i(t);else for(let s of e.toasts)i(s.id);return{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)}}},c=[],o={toasts:[]};function d(e){for(let s of(o=n(o,e),c))s(o)}function m(e){let{...s}=e,t=(l=(l+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>d({toastId:t,type:"DISMISS_TOAST"});return d({toast:{...s,id:t,onOpenChange:e=>{e||a()},open:!0},type:"ADD_TOAST"}),{dismiss:a,id:t,update:e=>d({toast:{...e,id:t},type:"UPDATE_TOAST"})}}function x(){let[e,s]=a.useState(o);return a.useEffect(()=>(c.push(s),()=>{let e=c.indexOf(s);-1!==e&&c.splice(e,1)}),[e]),{...e,dismiss:e=>d({type:"DISMISS_TOAST",...e&&{toastId:e}}),toast:m}}},44838:(e,s,t)=>{"use strict";t.d(s,{SQ:()=>x,_2:()=>u,hO:()=>h,lp:()=>p,mB:()=>g,rI:()=>d,ty:()=>m});var a=t(95155),l=t(12115),r=t(48698),i=t(73158),n=t(10518),c=t(70154),o=t(54036);let d=r.bL,m=r.l9;r.YJ,r.ZL,r.Pb,r.z6,l.forwardRef((e,s)=>{let{className:t,inset:l,children:n,...c}=e;return(0,a.jsxs)(r.ZP,{ref:s,className:(0,o.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",l&&"pl-8",t),...c,children:[n,(0,a.jsx)(i.A,{className:"ml-auto"})]})}).displayName=r.ZP.displayName,l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.G5,{ref:s,className:(0,o.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...l})}).displayName=r.G5.displayName;let x=l.forwardRef((e,s)=>{let{className:t,sideOffset:l=4,...i}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{ref:s,sideOffset:l,className:(0,o.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...i})})});x.displayName=r.UC.displayName;let u=l.forwardRef((e,s)=>{let{className:t,inset:l,...i}=e;return(0,a.jsx)(r.q7,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",l&&"pl-8",t),...i})});u.displayName=r.q7.displayName;let h=l.forwardRef((e,s)=>{let{className:t,children:l,checked:i,...c}=e;return(0,a.jsxs)(r.H_,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...void 0!==i&&{checked:i},...c,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})}),l]})});h.displayName=r.H_.displayName,l.forwardRef((e,s)=>{let{className:t,children:l,...i}=e;return(0,a.jsxs)(r.hN,{ref:s,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(c.A,{className:"h-2 w-2 fill-current"})})}),l]})}).displayName=r.hN.displayName;let p=l.forwardRef((e,s)=>{let{className:t,inset:l,...i}=e;return(0,a.jsx)(r.JU,{ref:s,className:(0,o.cn)("px-2 py-1.5 text-sm font-semibold",l&&"pl-8",t),...i})});p.displayName=r.JU.displayName;let g=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.wv,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...l})});g.displayName=r.wv.displayName},50477:()=>{},52639:(e,s,t)=>{"use strict";t.d(s,{GW:()=>n});var a=t(95155);t(12115);var l=t(88240),r=t(54036);let i=e=>{let{children:s,className:t="",config:i}=e;return(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:(0,r.cn)("min-h-screen bg-background",t),children:(0,a.jsx)("main",{className:"flex-1",children:(0,a.jsx)("div",{className:"container mx-auto space-y-6 px-4 py-6 sm:px-6 lg:px-8",children:s})})})})},n=e=>{let{children:s,className:t="",config:l}=e;return(0,a.jsx)(i,{config:l,className:t,children:(0,a.jsx)("div",{className:"space-y-8",children:s})})}},53414:(e,s,t)=>{Promise.resolve().then(t.bind(t,32606))},53712:(e,s,t)=>{"use strict";t.d(s,{O_:()=>i,t6:()=>r});var a=t(12115),l=t(83940);function r(){let e=(0,a.useCallback)((e,s)=>l.JP.success(e,s),[]),s=(0,a.useCallback)((e,s)=>l.JP.error(e,s),[]),t=(0,a.useCallback)((e,s)=>l.JP.info(e,s),[]),r=(0,a.useCallback)(s=>e((null==s?void 0:s.successTitle)||"Success",(null==s?void 0:s.successDescription)||"Operation completed successfully"),[e]),i=(0,a.useCallback)((e,t)=>{let a=e instanceof Error?e.message:e;return s((null==t?void 0:t.errorTitle)||"Error",(null==t?void 0:t.errorDescription)||a||"An unexpected error occurred")},[s]);return{showSuccess:e,showError:s,showInfo:t,showFormSuccess:r,showFormError:i}}function i(e){let s;switch(e){case"employee":s=t(83940).Ok;break;case"vehicle":s=t(83940).G7;break;case"task":s=t(83940).z0;break;case"delegation":s=t(83940).Qu;break;default:throw Error("Unknown entity type: ".concat(e))}return function(e,s){let{showFormSuccess:t,showFormError:i}=r(),n=s||(e?(0,l.iw)(e):null),c=(0,a.useCallback)(e=>n?n.entityCreated(e):t({successTitle:"Created",successDescription:"Item has been created successfully"}),[n,t]),o=(0,a.useCallback)(e=>n?n.entityUpdated(e):t({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[n,t]),d=(0,a.useCallback)(e=>n?n.entityDeleted(e):t({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[n,t]),m=(0,a.useCallback)(e=>{if(n){let s=e instanceof Error?e.message:e;return n.entityCreationError(s)}return i(e,{errorTitle:"Creation Failed"})},[n,i]);return{showEntityCreated:c,showEntityUpdated:o,showEntityDeleted:d,showEntityCreationError:m,showEntityUpdateError:(0,a.useCallback)(e=>{if(n){let s=e instanceof Error?e.message:e;return n.entityUpdateError(s)}return i(e,{errorTitle:"Update Failed"})},[n,i]),showEntityDeletionError:(0,a.useCallback)(e=>{if(n){let s=e instanceof Error?e.message:e;return n.entityDeletionError(s)}return i(e,{errorTitle:"Deletion Failed"})},[n,i]),showFormSuccess:t,showFormError:i}}(void 0,s)}},55365:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>c,TN:()=>d,XL:()=>o});var a=t(95155),l=t(74466),r=t(12115),i=t(54036);let n=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),c=r.forwardRef((e,s)=>{let{className:t,variant:l,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:l}),t),ref:s,role:"alert",...r})});c.displayName="Alert";let o=r.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("h5",{className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),ref:s,...l})});o.displayName="AlertTitle";let d=r.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),ref:s,...l})});d.displayName="AlertDescription"},59409:(e,s,t)=>{"use strict";t.d(s,{bq:()=>x,eb:()=>g,gC:()=>p,l6:()=>d,yv:()=>m});var a=t(95155),l=t(31992),r=t(79556),i=t(77381),n=t(10518),c=t(12115),o=t(54036);let d=l.bL;l.YJ;let m=l.WT,x=c.forwardRef((e,s)=>{let{children:t,className:i,...n}=e;return(0,a.jsxs)(l.l9,{className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",i),ref:s,...n,children:[t,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(r.A,{className:"size-4 opacity-50"})})]})});x.displayName=l.l9.displayName;let u=c.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.PP,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),ref:s,...r,children:(0,a.jsx)(i.A,{className:"size-4"})})});u.displayName=l.PP.displayName;let h=c.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)(l.wn,{className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),ref:s,...i,children:(0,a.jsx)(r.A,{className:"size-4"})})});h.displayName=l.wn.displayName;let p=c.forwardRef((e,s)=>{let{children:t,className:r,position:i="popper",...n}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:i,ref:s,...n,children:[(0,a.jsx)(u,{}),(0,a.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,a.jsx)(h,{})]})})});p.displayName=l.UC.displayName,c.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.JU,{className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),ref:s,...r})}).displayName=l.JU.displayName;let g=c.memo(c.forwardRef((e,s)=>{let{children:t,className:r,...i}=e,d=c.useCallback(e=>{"function"==typeof s?s(e):s&&(s.current=e)},[s]);return(0,a.jsxs)(l.q7,{className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),ref:d,...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(l.p4,{children:t})]})}));g.displayName=l.q7.displayName,c.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wv,{className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),ref:s,...r})}).displayName=l.wv.displayName},61051:(e,s,t)=>{"use strict";t.d(s,{ZY:()=>f,AK:()=>b,b7:()=>v,xo:()=>y,si:()=>j,K:()=>N});var a=t(71610),l=t(26715),r=t(5041),i=t(12115),n=t(90111),c=t(75908),o=t(21354),d=t(62494);let m={all:["tasks"],detail:e=>["tasks",e]},x=e=>({enabled:!!e,queryFn:()=>c.taskApiService.getById(e),queryKey:m.detail(e),staleTime:3e5}),u=()=>({queryFn:()=>c.employeeApiService.getAll(),queryKey:["employees"],staleTime:6e5}),h=()=>({queryFn:()=>c.vehicleApiService.getAll(),queryKey:["vehicles"],staleTime:6e5}),p=e=>[x(e),u(),h()];var g=t(54120);let j=e=>(0,n.GK)([...m.all],async()=>(await c.taskApiService.getAll()).data,"task",{staleTime:0,...e}),v=e=>(0,n.GK)([...m.detail(e)],async()=>await c.taskApiService.getById(e),"task",{enabled:!!e,staleTime:3e5}),y=e=>{let[s,t,l]=(0,a.E)({queries:p(e)}),r=(0,i.useMemo)(()=>{if((null==s?void 0:s.data)&&(null==t?void 0:t.data)&&(null==l?void 0:l.data))try{let e=d.J.fromApi(s.data),a=Array.isArray(t.data)?t.data:[],r=Array.isArray(l.data)?l.data:[];return(0,o.R)(e,a,r)}catch(e){throw console.error("Error enriching task data:",e),e}},[null==s?void 0:s.data,null==t?void 0:t.data,null==l?void 0:l.data]),n=(0,i.useCallback)(()=>{null==s||s.refetch(),null==t||t.refetch(),null==l||l.refetch()},[null==s?void 0:s.refetch,null==t?void 0:t.refetch,null==l?void 0:l.refetch]);return{data:r,error:(null==s?void 0:s.error)||(null==t?void 0:t.error)||(null==l?void 0:l.error),isError:(null==s?void 0:s.isError)||(null==t?void 0:t.isError)||(null==l?void 0:l.isError),isLoading:(null==s?void 0:s.isLoading)||(null==t?void 0:t.isLoading)||(null==l?void 0:l.isLoading),isPending:(null==s?void 0:s.isPending)||(null==t?void 0:t.isPending)||(null==l?void 0:l.isPending),refetch:n}},f=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>{let s=d.J.toCreateRequest(e);return await c.taskApiService.create(s)},onError:(s,t,a)=>{(null==a?void 0:a.previousTasks)&&e.setQueryData(m.all,a.previousTasks),console.error("Failed to create task:",s)},onMutate:async s=>{await e.cancelQueries({queryKey:m.all});let t=e.getQueryData(m.all);return e.setQueryData(m.all,function(){var e,t,a,l,r,i,n,c,o,d;let m=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],x="optimistic-"+Date.now().toString(),u=new Date().toISOString();return[...m,{createdAt:u,dateTime:null!=(t=s.dateTime)?t:null,deadline:null!=(a=s.deadline)?a:null,description:s.description,driverEmployee:null,driverEmployeeId:null!=(l=s.driverEmployeeId)?l:null,estimatedDuration:null!=(r=s.estimatedDuration)?r:null,id:x,location:null!=(i=s.location)?i:null,notes:null!=(n=s.notes)?n:null,priority:s.priority,requiredSkills:null!=(c=s.requiredSkills)?c:null,staffEmployee:null,staffEmployeeId:null!=(o=s.staffEmployeeId)?o:null,status:s.status||"Pending",subtasks:(null==(e=s.subtasks)?void 0:e.map(e=>({completed:e.completed||!1,id:"optimistic-subtask-".concat(Date.now(),"-").concat(Math.random().toString(36).slice(2,7)),taskId:x,title:e.title})))||[],updatedAt:u,vehicle:null,vehicleId:null!=(d=s.vehicleId)?d:null}]}),{previousTasks:t}},onSettled:()=>{e.invalidateQueries({queryKey:m.all})}})},N=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>{let{data:s,id:t}=e,a=d.J.toUpdateRequest(s);return await c.taskApiService.update(t,a)},onError:(s,t,a)=>{(null==a?void 0:a.previousTask)&&e.setQueryData(m.detail(t.id),a.previousTask),(null==a?void 0:a.previousTasksList)&&e.setQueryData(m.all,a.previousTasksList),console.error("Failed to update task:",s)},onMutate:async s=>{let{data:t,id:a}=s;await e.cancelQueries({queryKey:m.all}),await e.cancelQueries({queryKey:m.detail(a)});let l=e.getQueryData(m.detail(a)),r=e.getQueryData(m.all);return e.setQueryData(m.detail(a),e=>{var s,l,r,i;if(!e)return e;let n=new Date().toISOString();return{...e,dateTime:void 0!==t.dateTime?t.dateTime:e.dateTime,deadline:(0,g.d$)(void 0!==t.deadline?t.deadline:e.deadline),description:null!=(l=t.description)?l:e.description,driverEmployeeId:(0,g.d$)(void 0!==t.driverEmployeeId?t.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==t.estimatedDuration?t.estimatedDuration:e.estimatedDuration,location:void 0!==t.location?t.location:e.location,notes:(0,g.d$)(void 0!==t.notes?t.notes:e.notes),priority:null!=(r=t.priority)?r:e.priority,requiredSkills:void 0!==t.requiredSkills?t.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==t.staffEmployeeId?t.staffEmployeeId:e.staffEmployeeId,status:null!=(i=t.status)?i:e.status,subtasks:(null==(s=t.subtasks)?void 0:s.map(e=>{var s;return{completed:null!=(s=e.completed)&&s,id:"optimistic-subtask-".concat(Date.now(),"-").concat(Math.random().toString(36).slice(2,7)),taskId:a,title:e.title}}))||e.subtasks||[],updatedAt:n,vehicleId:(0,g.d$)(void 0!==t.vehicleId?t.vehicleId:e.vehicleId)}}),e.setQueryData(m.all,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.map(e=>{if(e.id===a){var s,l,r,i;let n=new Date().toISOString(),c=(null==(s=t.subtasks)?void 0:s.map(e=>{var s;return{completed:null!=(s=e.completed)&&s,id:"optimistic-subtask-".concat(Date.now(),"-").concat(Math.random().toString(36).slice(2,7)),taskId:a,title:e.title}}))||e.subtasks||[];return{...e,dateTime:void 0!==t.dateTime?t.dateTime:e.dateTime,deadline:(0,g.d$)(void 0!==t.deadline?t.deadline:e.deadline),description:null!=(l=t.description)?l:e.description,driverEmployeeId:(0,g.d$)(void 0!==t.driverEmployeeId?t.driverEmployeeId:e.driverEmployeeId),estimatedDuration:void 0!==t.estimatedDuration?t.estimatedDuration:e.estimatedDuration,location:void 0!==t.location?t.location:e.location,notes:(0,g.d$)(void 0!==t.notes?t.notes:e.notes),priority:null!=(r=t.priority)?r:e.priority,requiredSkills:void 0!==t.requiredSkills?t.requiredSkills:e.requiredSkills,staffEmployeeId:void 0!==t.staffEmployeeId?t.staffEmployeeId:e.staffEmployeeId,status:null!=(i=t.status)?i:e.status,subtasks:c,updatedAt:n,vehicleId:(0,g.d$)(void 0!==t.vehicleId?t.vehicleId:e.vehicleId)}}return e})}),{previousTask:l,previousTasksList:r}},onSettled:(s,t,a)=>{e.invalidateQueries({queryKey:m.detail(a.id)}),e.invalidateQueries({queryKey:m.all})}})},b=()=>{let e=(0,l.jE)();return(0,r.n)({mutationFn:async e=>(await c.taskApiService.delete(e),e),onError:(s,t,a)=>{(null==a?void 0:a.previousTasksList)&&e.setQueryData(m.all,a.previousTasksList),console.error("Failed to delete task:",s)},onMutate:async s=>{await e.cancelQueries({queryKey:m.all}),await e.cancelQueries({queryKey:m.detail(s)});let t=e.getQueryData(m.all);return e.setQueryData(m.all,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.filter(e=>e.id!==s)}),e.removeQueries({queryKey:m.detail(s)}),{previousTasksList:t}},onSettled:()=>{e.invalidateQueries({queryKey:m.all})}})}},62523:(e,s,t)=>{"use strict";t.d(s,{p:()=>i});var a=t(95155),l=t(12115),r=t(54036);let i=l.forwardRef((e,s)=>{let{className:t,type:l,...i}=e;return(0,a.jsx)("input",{className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,type:l,...i})});i.displayName="Input"},68856:(e,s,t)=>{"use strict";t.d(s,{E:()=>r});var a=t(95155),l=t(54036);function r(e){let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,l.cn)("animate-pulse rounded-md bg-muted",s),...t})}},77023:(e,s,t)=>{"use strict";t.d(s,{gO:()=>x,jt:()=>g,pp:()=>u});var a=t(95155),l=t(11133),r=t(50172);t(12115);var i=t(6560),n=t(55365),c=t(68856),o=t(54036);let d={lg:"h-8 w-8",md:"h-6 w-6",sm:"h-4 w-4",xl:"h-12 w-12"},m={lg:"text-base",md:"text-sm",sm:"text-xs",xl:"text-lg"};function x(e){let{children:s,className:t,data:l,emptyComponent:r,error:i,errorComponent:n,isLoading:c,loadingComponent:d,onRetry:m}=e;return c?d||(0,a.jsx)(p,{...t&&{className:t},text:"Loading..."}):i?n||(0,a.jsx)(h,{...t&&{className:t},message:i,...m&&{onRetry:m}}):!l||Array.isArray(l)&&0===l.length?r||(0,a.jsx)("div",{className:(0,o.cn)("text-center py-8",t),children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No data available"})}):(0,a.jsx)("div",{className:t,children:s(l)})}function u(e){let{className:s,description:t,icon:l,primaryAction:r,secondaryAction:n,title:c}=e;return(0,a.jsxs)("div",{className:(0,o.cn)("space-y-6 text-center py-12",s),children:[l&&(0,a.jsx)("div",{className:"mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted",children:(0,a.jsx)(l,{className:"h-10 w-10 text-muted-foreground"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"text-2xl font-semibold text-foreground",children:c}),t&&(0,a.jsx)("p",{className:"text-muted-foreground max-w-md mx-auto",children:t})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[r&&(0,a.jsx)(i.r,{actionType:"primary",asChild:!!r.href,icon:r.icon,onClick:r.onClick,children:r.href?(0,a.jsx)("a",{href:r.href,children:r.label}):r.label}),n&&(0,a.jsx)(i.r,{actionType:"tertiary",asChild:!!n.href,icon:n.icon,onClick:n.onClick,children:n.href?(0,a.jsx)("a",{href:n.href,children:n.label}):n.label})]})]})}function h(e){let{className:s,message:t,onRetry:c}=e;return(0,a.jsxs)(n.Fc,{className:(0,o.cn)("my-4",s),variant:"destructive",children:[(0,a.jsx)(l.A,{className:"size-4"}),(0,a.jsx)(n.XL,{children:"Error"}),(0,a.jsx)(n.TN,{children:(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsx)("p",{className:"mb-4 text-sm text-muted-foreground",children:t}),c&&(0,a.jsx)(i.r,{actionType:"tertiary",icon:(0,a.jsx)(r.A,{className:"size-4"}),onClick:c,size:"sm",children:"Try Again"})]})})]})}function p(e){let{className:s,fullPage:t=!1,size:l="md",text:i}=e;return(0,a.jsx)("div",{className:(0,o.cn)("flex items-center justify-center",t&&"fixed inset-0 bg-background/80 backdrop-blur-sm z-50",s),children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(r.A,{className:(0,o.cn)("animate-spin text-primary",d[l])}),i&&(0,a.jsx)("span",{className:(0,o.cn)("mt-2 text-muted-foreground",m[l]),children:i})]})})}function g(e){let{className:s,count:t=1,testId:l="loading-skeleton",variant:r="default"}=e;return"card"===r?(0,a.jsx)("div",{className:(0,o.cn)("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",s),"data-testid":l,children:Array(t).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"overflow-hidden rounded-lg border bg-card shadow-md",children:[(0,a.jsx)(c.E,{className:"aspect-[16/10] w-full"}),(0,a.jsxs)("div",{className:"p-5",children:[(0,a.jsx)(c.E,{className:"mb-1 h-7 w-3/4"}),(0,a.jsx)(c.E,{className:"mb-3 h-4 w-1/2"}),(0,a.jsx)(c.E,{className:"my-3 h-px w-full"}),(0,a.jsx)("div",{className:"space-y-2.5",children:Array.from({length:3}).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.E,{className:"mr-2.5 size-5 rounded-full"}),(0,a.jsx)(c.E,{className:"h-5 w-2/3"})]},s))})]})]},s))}):"table"===r?(0,a.jsxs)("div",{className:(0,o.cn)("space-y-3",s),"data-testid":l,children:[(0,a.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,a.jsx)(c.E,{className:"h-8 flex-1"},s))}),Array(t).fill(0).map((e,s)=>(0,a.jsx)("div",{className:"flex gap-4",children:Array.from({length:3}).fill(0).map((e,s)=>(0,a.jsx)(c.E,{className:"h-6 flex-1"},s))},s))]}):"list"===r?(0,a.jsx)("div",{className:(0,o.cn)("space-y-3",s),"data-testid":l,children:Array(t).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(c.E,{className:"size-12 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)(c.E,{className:"h-4 w-1/3"}),(0,a.jsx)(c.E,{className:"h-4 w-full"})]})]},s))}):"stats"===r?(0,a.jsx)("div",{className:(0,o.cn)("grid gap-4 md:grid-cols-2 lg:grid-cols-3",s),"data-testid":l,children:Array(t).fill(0).map((e,s)=>(0,a.jsxs)("div",{className:"rounded-lg border bg-card p-5 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)(c.E,{className:"h-5 w-1/3"}),(0,a.jsx)(c.E,{className:"size-5 rounded-full"})]}),(0,a.jsx)(c.E,{className:"mt-3 h-8 w-1/2"}),(0,a.jsx)(c.E,{className:"mt-2 h-4 w-2/3"})]},s))}):(0,a.jsx)("div",{className:(0,o.cn)("space-y-2",s),"data-testid":l,children:Array(t).fill(0).map((e,s)=>(0,a.jsx)(c.E,{className:"h-5 w-full"},s))})}},83686:()=>{},83940:(e,s,t)=>{"use strict";t.d(s,{G7:()=>x,Gb:()=>c,JP:()=>o,Ok:()=>d,Qu:()=>m,iw:()=>n,oz:()=>h,z0:()=>u});var a=t(40879);class l{show(e){return(0,a.oR)({title:e.title,description:e.description,variant:e.variant||"default",...e.duration&&{duration:e.duration}})}success(e,s){return this.show({title:e,description:s,variant:"default"})}error(e,s){return this.show({title:e,description:s,variant:"destructive"})}info(e,s){return this.show({title:e,description:s,variant:"default"})}}class r extends l{entityCreated(e){let s=this.config.getDisplayName(e);return this.success(this.config.messages.created.title,this.config.messages.created.description(s))}entityUpdated(e){let s=this.config.getDisplayName(e);return this.success(this.config.messages.updated.title,this.config.messages.updated.description(s))}entityDeleted(e){let s=this.config.getDisplayName(e);return this.success(this.config.messages.deleted.title,this.config.messages.deleted.description(s))}entityCreationError(e){return this.error(this.config.messages.creationError.title,this.config.messages.creationError.description(e))}entityUpdateError(e){return this.error(this.config.messages.updateError.title,this.config.messages.updateError.description(e))}entityDeletionError(e){return this.error(this.config.messages.deletionError.title,this.config.messages.deletionError.description(e))}constructor(e){super(),this.config=e}}class i extends l{serviceRecordCreated(e,s){return this.success("Service Record Added","".concat(s,' service for "').concat(e,'" has been successfully logged.'))}serviceRecordUpdated(e,s){return this.success("Service Record Updated","".concat(s,' service for "').concat(e,'" has been updated.'))}serviceRecordDeleted(e,s){return this.success("Service Record Deleted","".concat(s,' service record for "').concat(e,'" has been permanently removed.'))}serviceRecordCreationError(e){return this.error("Failed to Log Service Record",e||"An unexpected error occurred while logging the service record.")}serviceRecordUpdateError(e){return this.error("Update Failed",e||"An unexpected error occurred while updating the service record.")}serviceRecordDeletionError(e){return this.error("Failed to Delete Service Record",e||"An unexpected error occurred while deleting the service record.")}}function n(e){return new r(e)}function c(e,s){return new r({entityName:e,getDisplayName:s,messages:{created:{title:"".concat(e," Created"),description:s=>"The ".concat(e.toLowerCase(),' "').concat(s,'" has been successfully created.')},updated:{title:"".concat(e," Updated Successfully"),description:e=>"".concat(e," has been updated.")},deleted:{title:"".concat(e," Deleted Successfully"),description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create ".concat(e),description:s=>s||"An unexpected error occurred while creating the ".concat(e.toLowerCase(),".")},updateError:{title:"Update Failed",description:s=>s||"An unexpected error occurred while updating the ".concat(e.toLowerCase(),".")},deletionError:{title:"Failed to Delete ".concat(e),description:s=>s||"An unexpected error occurred while deleting the ".concat(e.toLowerCase(),".")}}})}let o=new l,d=new r({entityName:"Employee",getDisplayName:e=>e.name,messages:{created:{title:"Employee Added",description:e=>'The employee "'.concat(e,'" has been successfully created.')},updated:{title:"Employee Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Employee Deleted Successfully",description:e=>"".concat(e," has been permanently removed from the system.")},creationError:{title:"Failed to Create Employee",description:e=>e||"An unexpected error occurred while creating the employee."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the employee."},deletionError:{title:"Failed to Delete Employee",description:e=>e||"An unexpected error occurred while deleting the employee."}}}),m=new r({entityName:"Delegation",getDisplayName:e=>e.event||e.location||"Delegation",messages:{created:{title:"Delegation Created",description:e=>'The delegation "'.concat(e,'" has been successfully created.')},updated:{title:"Delegation Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Delegation Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Delegation",description:e=>e||"An unexpected error occurred while creating the delegation."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the delegation."},deletionError:{title:"Failed to Delete Delegation",description:e=>e||"An unexpected error occurred while deleting the delegation."}}}),x=new r({entityName:"Vehicle",getDisplayName:e=>"".concat(e.make," ").concat(e.model),messages:{created:{title:"Vehicle Added",description:e=>'The vehicle "'.concat(e,'" has been successfully created.')},updated:{title:"Vehicle Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Vehicle Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Vehicle",description:e=>e||"An unexpected error occurred while creating the vehicle."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the vehicle."},deletionError:{title:"Failed to Delete Vehicle",description:e=>e||"An unexpected error occurred while deleting the vehicle."}}}),u=new r({entityName:"Task",getDisplayName:e=>e.title||e.name||"Task",messages:{created:{title:"Task Created",description:e=>'The task "'.concat(e,'" has been successfully created.')},updated:{title:"Task Updated Successfully",description:e=>"".concat(e," has been updated.")},deleted:{title:"Task Deleted Successfully",description:e=>"".concat(e," has been permanently removed.")},creationError:{title:"Failed to Create Task",description:e=>e||"An unexpected error occurred while creating the task."},updateError:{title:"Update Failed",description:e=>e||"An unexpected error occurred while updating the task."},deletionError:{title:"Failed to Delete Task",description:e=>e||"An unexpected error occurred while deleting the task."}}}),h=new i},85057:(e,s,t)=>{"use strict";t.d(s,{J:()=>o});var a=t(95155),l=t(12115),r=t(40968),i=t(74466),n=t(54036);let c=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.b,{ref:s,className:(0,n.cn)(c(),t),...l})});o.displayName=r.b.displayName},85127:(e,s,t)=>{"use strict";t.d(s,{A0:()=>n,BF:()=>c,Hj:()=>o,XI:()=>i,nA:()=>m,nd:()=>d});var a=t(95155),l=t(12115),r=t(54036);let i=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{className:(0,r.cn)("w-full caption-bottom text-sm",t),ref:s,...l})})});i.displayName="Table";let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("thead",{className:(0,r.cn)("[&_tr]:border-b",t),ref:s,...l})});n.displayName="TableHeader";let c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("tbody",{className:(0,r.cn)("[&_tr:last-child]:border-0",t),ref:s,...l})});c.displayName="TableBody",l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("tfoot",{className:(0,r.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),ref:s,...l})}).displayName="TableFooter";let o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("tr",{className:(0,r.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),ref:s,...l})});o.displayName="TableRow";let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("th",{className:(0,r.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),ref:s,...l})});d.displayName="TableHead";let m=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("td",{className:(0,r.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),ref:s,...l})});m.displayName="TableCell",l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("caption",{className:(0,r.cn)("mt-4 text-sm text-muted-foreground",t),ref:s,...l})}).displayName="TableCaption"},85511:(e,s,t)=>{"use strict";t.d(s,{V:()=>o});var a=t(95155),l=t(965),r=t(73158);t(12115);var i=t(33683),n=t(30285),c=t(54036);function o(e){let{className:s,classNames:t,showOutsideDays:o=!0,...d}=e;return(0,a.jsx)(i.hv,{className:(0,c.cn)("p-3",s),classNames:{caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:(0,c.cn)((0,n.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_disabled:"text-muted-foreground opacity-50",day_hidden:"invisible",day_outside:"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",day_range_end:"day-range-end",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",head_row:"flex",month:"space-y-4",months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",nav:"space-x-1 flex items-center",nav_button:(0,c.cn)((0,n.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_next:"absolute right-1",nav_button_previous:"absolute left-1",row:"flex w-full mt-2",table:"w-full border-collapse space-y-1",...t},components:{IconLeft:e=>{let{className:s,...t}=e;return(0,a.jsx)(l.A,{className:(0,c.cn)("h-4 w-4",s),...t})},IconRight:e=>{let{className:s,...t}=e;return(0,a.jsx)(r.A,{className:(0,c.cn)("h-4 w-4",s),...t})}},showOutsideDays:o,...d})}o.displayName="Calendar"},86719:(e,s,t)=>{"use strict";t.d(s,{M:()=>l});var a=t(12115);function l(){let[e,s]=(0,a.useState)({}),[t,l]=(0,a.useState)(!1),r=(0,a.useCallback)(e=>e?/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)?void 0:"Please enter a valid email address":"Email address is required",[]),i=(0,a.useCallback)(e=>e?e.length<6?"Password must be at least 6 characters long":void 0:"Password is required",[]),n=(0,a.useCallback)(e=>{let t={},a=r(e.email),l=i(e.password);return a&&(t.email=a),l&&(t.password=l),s(t),{isValid:0===Object.keys(t).length,errors:t}},[r,i]),c=(0,a.useCallback)((e,t)=>{let a;switch(e){case"email":a=r(t);break;case"password":a=i(t);break;default:return}s(s=>({...s,[e]:a}))},[r,i]),o=(0,a.useCallback)(e=>{s(s=>{let t={...s};return delete t[e],t})},[]),d=(0,a.useCallback)(()=>{s({})},[]),m=(0,a.useCallback)(()=>{l(!0)},[]),x=(0,a.useCallback)(()=>{s({}),l(!1)},[]),u=(0,a.useCallback)((s,a)=>t&&a&&!e[s],[e,t]);return{errors:e,isFormTouched:t,validateForm:n,validateField:c,clearFieldError:o,clearAllErrors:d,markFormTouched:m,resetValidation:x,isFieldValid:u}}},88539:(e,s,t)=>{"use strict";t.d(s,{T:()=>i});var a=t(95155),l=t(12115),r=t(54036);let i=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("textarea",{className:(0,r.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,...l})});i.displayName="Textarea"},91394:(e,s,t)=>{"use strict";t.d(s,{BK:()=>c,eu:()=>n,q5:()=>o});var a=t(95155),l=t(54011),r=t(12115),i=t(54036);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.bL,{className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),ref:s,...r})});n.displayName=l.bL.displayName;let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l._V,{className:(0,i.cn)("aspect-square h-full w-full",t),ref:s,...r})});c.displayName=l._V.displayName;let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.H4,{className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),ref:s,...r})});o.displayName=l.H4.displayName}}]);