/**
 * @file Unified WebSocket Manager for WorkHub Application
 * Provides centralized WebSocket connection management with domain-specific channels
 * Follows SRP and DRY principles with smart fallback strategies
 * @module services/WebSocketManager
 */

import type { Socket } from 'socket.io-client';

import { io } from 'socket.io-client';

import { getEnvironmentConfig } from '../config/environment';
import { supabase } from '../supabase';
import { getTokenRefreshService } from './TokenRefreshService';
/*import logger from '../utils/logger';

/**
 * WebSocket connection states
 */
export type ConnectionState =
  | 'connected'
  | 'connecting'
  | 'disconnected'
  | 'error'
  | 'reconnecting';

/**
 * Domain-specific channels for organized event management
 */
export type DomainChannel = 'crud' | 'notifications' | 'reliability' | 'system';

/**
 * Event subscription callback type
 */
export type EventCallback<T = any> = (data: T) => void;

/**
 * WebSocket configuration options
 */
export interface WebSocketConfig {
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  timeout?: number;
  url?: string;
}

/**
 * Unified WebSocket Manager
 * Implements Singleton pattern for single connection per application
 * Provides domain-specific channels and centralized subscription management
 */
export class WebSocketManager {
  private static instance: null | WebSocketManager = null;
  private readonly config: Required<WebSocketConfig>;
  private connectionState: ConnectionState = 'disconnected';
  private reconnectAttempts = 0;
  private socket: null | Socket = null;
  private readonly stateListeners = new Set<(state: ConnectionState) => void>();
  private readonly subscriptions = new Map<string, Set<EventCallback>>();

  private constructor(config: WebSocketConfig = {}) {
    this.config = {
      autoConnect: config.autoConnect ?? true,
      reconnectAttempts: config.reconnectAttempts ?? 5,
      reconnectDelay: config.reconnectDelay ?? 1000,
      timeout: config.timeout ?? 10_000,
      url:
        config.url ??
        process.env.NEXT_PUBLIC_WEBSOCKET_URL ??
        getEnvironmentConfig()
          .wsUrl.replace('ws://', 'http://')
          .replace('wss://', 'https://'),
    };

    if (this.config.autoConnect) {
      this.connect();
    }

    // Subscribe to token refresh events
    this.setupTokenRefreshHandling();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(config?: WebSocketConfig): WebSocketManager {
    WebSocketManager.instance ??= new WebSocketManager(config);
    return WebSocketManager.instance;
  }

  /**
   * Connect to WebSocket server
   */
  public async connect(): Promise<void> {
    if (this.socket?.connected) {
      console.debug('WebSocket already connected');
      return;
    }

    this.setConnectionState('connecting');

    try {
      // Get current session and token for authentication
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      if (error) {
        console.warn('Failed to get session for WebSocket connection:', error);
      }

      const connectionOptions: any = {
        forceNew: true,
        timeout: this.config.timeout,
        transports: ['websocket', 'polling'],
        withCredentials: true, // Ensure cookies are sent with WebSocket handshake
      };

      // Add authentication token if available
      if (session?.access_token) {
        connectionOptions.auth = {
          token: session.access_token,
        };
        console.debug('🔐 WebSocket connecting with authentication token');

        // Validate token expiration
        const tokenExpiry = session.expires_at ? session.expires_at * 1000 : 0;
        const now = Date.now();
        const timeUntilExpiry = tokenExpiry - now;

        if (timeUntilExpiry <= 60_000) {
          // Less than 1 minute
          console.warn('⚠️ WebSocket token expires soon, may need refresh');
        }
      } else {
        console.warn(
          '⚠️ WebSocket connecting without authentication token - connection may fail'
        );
      }

      this.socket = io(this.config.url, connectionOptions);

      this.setupEventHandlers();
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      this.setConnectionState('error');
      this.scheduleReconnect();
    }
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    this.disconnect();
    this.subscriptions.clear();
    this.stateListeners.clear();
    WebSocketManager.instance = null;
  }

  /**
   * Disconnect from WebSocket server
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.setConnectionState('disconnected');
    this.reconnectAttempts = 0;
  }

  /**
   * Emit event to specific domain channel
   */
  public emit(channel: DomainChannel, event: string, data?: any): void {
    if (!this.socket?.connected) {
      console.warn(`Cannot emit ${channel}:${event} - WebSocket not connected`);
      return;
    }

    this.socket.emit(event, data);
  }

  /**
   * Get current connection state
   */
  public getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  /**
   * Check if WebSocket is connected
   */
  public isConnected(): boolean {
    return (
      this.connectionState === 'connected' && this.socket?.connected === true
    );
  }

  /**
   * Join domain-specific room
   */
  public joinRoom(room: string): void {
    if (!this.socket?.connected) {
      console.warn(`Cannot join room ${room} - WebSocket not connected`);
      return;
    }

    this.socket.emit('join-room', room);
  }

  /**
   * Leave domain-specific room
   */
  public leaveRoom(room: string): void {
    if (!this.socket?.connected) {
      return;
    }

    this.socket.emit('leave-room', room);
  }

  /**
   * Subscribe to connection state changes
   */
  public onStateChange(callback: (state: ConnectionState) => void): () => void {
    this.stateListeners.add(callback);

    return () => {
      this.stateListeners.delete(callback);
    };
  }

  /**
   * Subscribe to domain-specific events
   */
  public subscribe<T = any>(
    channel: DomainChannel,
    event: string,
    callback: EventCallback<T>
  ): () => void {
    const eventKey = `${channel}:${event}`;

    if (!this.subscriptions.has(eventKey)) {
      this.subscriptions.set(eventKey, new Set());
    }

    this.subscriptions.get(eventKey)!.add(callback);

    // Set up socket listener if connected
    if (this.socket?.connected && event) {
      this.socket.on(event, callback);
    }

    // Return unsubscribe function
    return () => {
      const callbacks = this.subscriptions.get(eventKey);
      if (callbacks) {
        callbacks.delete(callback);
        if (callbacks.size === 0) {
          this.subscriptions.delete(eventKey);
        }
      }

      if (this.socket && event) {
        this.socket.off(event, callback);
      }
    };
  }

  /**
   * Handle authentication errors by triggering token refresh
   */
  private handleAuthenticationError(): void {
    const tokenRefreshService = getTokenRefreshService();

    console.log('🔐 Handling WebSocket authentication error...');

    // Disconnect current socket to prevent further auth errors
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    // Attempt to refresh token
    tokenRefreshService
      .refreshNow()
      .then(success => {
        if (success) {
          console.log(
            '🔄 Token refresh successful, retrying WebSocket connection'
          );
          // The reconnection will be handled by setupTokenRefreshHandling
        } else {
          console.error('🔄 Token refresh failed, scheduling normal reconnect');
          this.scheduleReconnect();
        }
      })
      .catch(error => {
        console.error('🔄 Token refresh error:', error);
        this.scheduleReconnect();
      });
  }

  /**
   * Resubscribe to all events after reconnection
   */
  private resubscribeToEvents(): void {
    if (!this.socket) return;

    for (const [eventKey, callbacks] of this.subscriptions) {
      const [, event] = eventKey.split(':');
      for (const callback of callbacks) {
        if (event) {
          this.socket!.on(event, callback);
        }
      }
    }
  }

  /**
   * Schedule reconnection with exponential backoff
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.reconnectAttempts) {
      console.error('Max reconnection attempts reached');
      this.setConnectionState('error');
      return;
    }

    this.setConnectionState('reconnecting');
    this.reconnectAttempts++;

    const delay =
      this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    setTimeout(() => {
      console.info(
        `Attempting to reconnect (${this.reconnectAttempts}/${this.config.reconnectAttempts})`
      );
      this.connect();
    }, delay);
  }

  /**
   * Set connection state and notify listeners
   */
  private setConnectionState(state: ConnectionState): void {
    if (this.connectionState !== state) {
      this.connectionState = state;
      for (const listener of this.stateListeners) listener(state);
    }
  }

  /**
   * Setup socket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.info('WebSocket connected');
      this.setConnectionState('connected');
      this.reconnectAttempts = 0;
      this.resubscribeToEvents();
    });

    this.socket.on('disconnect', reason => {
      console.warn('WebSocket disconnected:', reason);
      this.setConnectionState('disconnected');

      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect automatically
        return;
      }

      this.scheduleReconnect();
    });

    this.socket.on('connect_error', error => {
      console.error('WebSocket connection error:', error);
      this.setConnectionState('error');

      // Check if error is authentication-related
      if (
        error.message?.includes('Authentication') ||
        error.message?.includes('token') ||
        error.message?.includes('No token provided') ||
        error.message?.includes('Unauthorized')
      ) {
        console.warn(
          '🔐 Authentication error detected, attempting token refresh'
        );
        this.handleAuthenticationError();
      } else {
        this.scheduleReconnect();
      }
    });

    // Listen for authentication errors from the server
    this.socket.on('auth_error', errorData => {
      console.error('🔐 Server authentication error:', errorData);
      this.handleAuthenticationError();
    });

    // Listen for token refresh requests from server
    this.socket.on('token_refresh_required', () => {
      console.warn('🔄 Server requested token refresh');
      this.handleAuthenticationError();
    });
  }

  /**
   * Setup token refresh event handling
   */
  private setupTokenRefreshHandling(): void {
    const tokenRefreshService = getTokenRefreshService();

    tokenRefreshService.subscribe((event, _data) => {
      switch (event) {
        case 'critical_refresh_failed': {
          console.error(
            '🔄 Critical token refresh failure, disconnecting WebSocket'
          );
          this.disconnect();
          this.setConnectionState('error');
          break;
        }

        case 'refresh_failed': {
          console.error(
            '🔄 Token refresh failed, WebSocket may lose connection'
          );
          break;
        }

        case 'refresh_success': {
          console.log(
            '🔄 Token refreshed, reconnecting WebSocket with new token'
          );
          // Disconnect current connection and reconnect with new token
          if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
          }
          // Reconnect with fresh token
          setTimeout(() => this.connect(), 500);
          break;
        }
      }
    });
  }
}

/**
 * Get the singleton WebSocket manager instance
 */
export const getWebSocketManager = (
  config?: WebSocketConfig
): WebSocketManager => {
  return WebSocketManager.getInstance(config);
};

/**
 * Hook for WebSocket connection state
 */
export const useWebSocketState = () => {
  const manager = getWebSocketManager();
  return {
    connectionState: manager.getConnectionState(),
    isConnected: manager.isConnected(),
  };
};
