{"/_not-found/page": "/_not-found", "/api/csp-report/route": "/api/csp-report", "/favicon.ico/route": "/favicon.ico", "/add-vehicle/page": "/add-vehicle", "/auth-test/page": "/auth-test", "/delegations/[id]/edit/page": "/delegations/[id]/edit", "/delegations/[id]/page": "/delegations/[id]", "/employees/[id]/edit/page": "/employees/[id]/edit", "/employees/[id]/page": "/employees/[id]", "/delegations/add/page": "/delegations/add", "/delegations/page": "/delegations", "/employees/new/page": "/employees/new", "/employees/add/page": "/employees/add", "/page": "/", "/profile/page": "/profile", "/font-size-demo/page": "/font-size-demo", "/employees/page": "/employees", "/login/page": "/login", "/service-records/[id]/edit/page": "/service-records/[id]/edit", "/service-records/[id]/page": "/service-records/[id]", "/tasks/[id]/edit/page": "/tasks/[id]/edit", "/service-history/page": "/service-history", "/settings/page": "/settings", "/tasks/add/page": "/tasks/add", "/tasks/[id]/page": "/tasks/[id]", "/tasks/page": "/tasks", "/vehicles/[id]/page": "/vehicles/[id]", "/vehicles/new/page": "/vehicles/new", "/vehicles/page": "/vehicles", "/vehicles/edit/[id]/page": "/vehicles/edit/[id]", "/zustand-test/page": "/zustand-test", "/delegations/[id]/report/page": "/delegations/[id]/report", "/delegations/report/list/page": "/delegations/report/list", "/admin/page": "/admin", "/tasks/report/page": "/tasks/report", "/vehicles/[id]/report/service-history/page": "/vehicles/[id]/report/service-history", "/vehicles/[id]/report/page": "/vehicles/[id]/report", "/reliability/page": "/reliability", "/reports/analytics/page": "/reports/analytics", "/reports/page": "/reports", "/reports/data/page": "/reports/data"}