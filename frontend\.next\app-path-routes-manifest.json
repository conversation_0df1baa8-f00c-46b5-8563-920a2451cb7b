{"/_not-found/page": "/_not-found", "/api/csp-report/route": "/api/csp-report", "/favicon.ico/route": "/favicon.ico", "/add-vehicle/page": "/add-vehicle", "/auth-test/page": "/auth-test", "/delegations/[id]/edit/page": "/delegations/[id]/edit", "/delegations/[id]/page": "/delegations/[id]", "/delegations/add/page": "/delegations/add", "/delegations/page": "/delegations", "/employees/add/page": "/employees/add", "/employees/[id]/page": "/employees/[id]", "/employees/[id]/edit/page": "/employees/[id]/edit", "/employees/new/page": "/employees/new", "/font-size-demo/page": "/font-size-demo", "/employees/page": "/employees", "/profile/page": "/profile", "/page": "/", "/login/page": "/login", "/service-history/page": "/service-history", "/service-records/[id]/edit/page": "/service-records/[id]/edit", "/tasks/[id]/edit/page": "/tasks/[id]/edit", "/service-records/[id]/page": "/service-records/[id]", "/settings/page": "/settings", "/tasks/[id]/page": "/tasks/[id]", "/tasks/add/page": "/tasks/add", "/vehicles/[id]/page": "/vehicles/[id]", "/tasks/page": "/tasks", "/vehicles/edit/[id]/page": "/vehicles/edit/[id]", "/vehicles/new/page": "/vehicles/new", "/vehicles/page": "/vehicles", "/zustand-test/page": "/zustand-test", "/admin/page": "/admin", "/delegations/[id]/report/page": "/delegations/[id]/report", "/delegations/report/list/page": "/delegations/report/list", "/vehicles/[id]/report/page": "/vehicles/[id]/report", "/tasks/report/page": "/tasks/report", "/vehicles/[id]/report/service-history/page": "/vehicles/[id]/report/service-history", "/reliability/page": "/reliability", "/reports/analytics/page": "/reports/analytics", "/reports/data/page": "/reports/data", "/reports/page": "/reports"}