{"/api/csp-report/route": "/api/csp-report", "/_not-found/page": "/_not-found", "/favicon.ico/route": "/favicon.ico", "/add-vehicle/page": "/add-vehicle", "/auth-test/page": "/auth-test", "/delegations/[id]/edit/page": "/delegations/[id]/edit", "/delegations/add/page": "/delegations/add", "/delegations/page": "/delegations", "/employees/[id]/edit/page": "/employees/[id]/edit", "/employees/[id]/page": "/employees/[id]", "/delegations/[id]/page": "/delegations/[id]", "/employees/add/page": "/employees/add", "/employees/new/page": "/employees/new", "/login/page": "/login", "/page": "/", "/employees/page": "/employees", "/font-size-demo/page": "/font-size-demo", "/profile/page": "/profile", "/service-history/page": "/service-history", "/service-records/[id]/edit/page": "/service-records/[id]/edit", "/service-records/[id]/page": "/service-records/[id]", "/settings/page": "/settings", "/tasks/[id]/page": "/tasks/[id]", "/tasks/[id]/edit/page": "/tasks/[id]/edit", "/tasks/page": "/tasks", "/tasks/add/page": "/tasks/add", "/vehicles/[id]/page": "/vehicles/[id]", "/vehicles/edit/[id]/page": "/vehicles/edit/[id]", "/vehicles/new/page": "/vehicles/new", "/vehicles/page": "/vehicles", "/zustand-test/page": "/zustand-test", "/admin/page": "/admin", "/delegations/[id]/report/page": "/delegations/[id]/report", "/tasks/report/page": "/tasks/report", "/delegations/report/list/page": "/delegations/report/list", "/reports/analytics/page": "/reports/analytics", "/reports/page": "/reports", "/reports/data/page": "/reports/data", "/vehicles/[id]/report/service-history/page": "/vehicles/[id]/report/service-history", "/vehicles/[id]/report/page": "/vehicles/[id]/report", "/reliability/page": "/reliability"}