(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__752a1ada._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/lib/security [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__import_unsupported(`crypto`));
}}),
"[project]/src/lib/security/cspConfig.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * PHASE 3 SECURITY HARDENING: Enhanced CSP Configuration - 2025 Best Practices
 *
 * Implements strict Content Security Policy with:
 * - Nonce-based script execution
 * - strict-dynamic for trusted scripts
 * - Comprehensive security directives
 * - CSP violation reporting
 * - Supply chain security for third-party scripts
 * - Enhanced monitoring and alerting
 */ __turbopack_context__.s({
    "TRUSTED_SCRIPTS": (()=>TRUSTED_SCRIPTS),
    "analyzeCSPViolationRisk": (()=>analyzeCSPViolationRisk),
    "generateCSPReportingConfig": (()=>generateCSPReportingConfig),
    "generateSRIPolicy": (()=>generateSRIPolicy),
    "generateSecureNonce": (()=>generateSecureNonce),
    "generateSecurityHeaders": (()=>generateSecurityHeaders),
    "generateStrictCSP": (()=>generateStrictCSP),
    "validateNonce": (()=>validateNonce),
    "validateScriptIntegrity": (()=>validateScriptIntegrity)
});
function generateStrictCSP(config) {
    const { nonce, reportUri, isDevelopment } = config;
    // Base strict CSP directives
    const directives = {
        // Script sources - strict nonce-based approach
        'script-src': [
            "'self'",
            `'nonce-${nonce}'`,
            "'strict-dynamic'",
            // Allow specific trusted domains for production
            ...isDevelopment ? [] : [
                'https://cdn.jsdelivr.net',
                'https://unpkg.com'
            ]
        ],
        // Style sources - nonce-based with fallbacks
        'style-src': [
            "'self'",
            `'nonce-${nonce}'`,
            // Allow inline styles for CSS-in-JS libraries in development
            ...isDevelopment ? [
                "'unsafe-inline'"
            ] : [],
            // Trusted style CDNs
            'https://fonts.googleapis.com',
            'https://cdn.jsdelivr.net'
        ],
        // Font sources
        'font-src': [
            "'self'",
            'https://fonts.gstatic.com',
            'https://cdn.jsdelivr.net',
            'data:'
        ],
        // Image sources
        'img-src': [
            "'self'",
            'data:',
            'blob:',
            'https:',
            // Supabase storage
            'https://*.supabase.co',
            // Common image CDNs
            'https://images.unsplash.com',
            'https://via.placeholder.com'
        ],
        // Connect sources for API calls
        'connect-src': [
            "'self'",
            // Supabase endpoints
            'https://*.supabase.co',
            'wss://*.supabase.co',
            // Development WebSocket
            ...isDevelopment ? [
                'ws://localhost:*',
                'wss://localhost:*'
            ] : [],
            // Backend API endpoints (for development and Docker environments)
            ...isDevelopment || ("TURBOPACK compile-time value", "false") === 'true' ? [
                'http://localhost:3001',
                'http://backend:3001'
            ] : [],
            // Additional connect sources from environment variable
            ...("TURBOPACK compile-time truthy", 1) ? ("TURBOPACK compile-time value", "http://localhost:3001").split(',').map((s)=>s.trim()).filter(Boolean) : ("TURBOPACK unreachable", undefined),
            // API endpoints
            'https://api.github.com'
        ],
        // Frame sources
        'frame-src': [
            "'self'",
            // Allow specific trusted frames
            'https://www.youtube.com',
            'https://player.vimeo.com'
        ],
        // Object and embed restrictions
        'object-src': [
            "'none'"
        ],
        'embed-src': [
            "'none'"
        ],
        // Base URI restriction
        'base-uri': [
            "'self'"
        ],
        // Form action restriction
        'form-action': [
            "'self'"
        ],
        // Frame ancestors (clickjacking protection)
        'frame-ancestors': [
            "'none'"
        ],
        // Block mixed content
        'block-all-mixed-content': [],
        // Default fallback
        'default-src': [
            "'self'"
        ]
    };
    // Add upgrade insecure requests in production
    if (!isDevelopment) {
        directives['upgrade-insecure-requests'] = [];
    }
    // Add reporting if configured
    if (reportUri) {
        directives['report-uri'] = [
            reportUri
        ];
        directives['report-to'] = [
            'csp-endpoint'
        ];
    }
    // Convert directives to CSP string
    return Object.entries(directives).map(([directive, sources])=>{
        if (sources.length === 0) {
            return directive;
        }
        return `${directive} ${sources.join(' ')}`;
    }).join('; ');
}
function generateSecurityHeaders() {
    return {
        // Strict Transport Security
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
        // X-Frame-Options (backup for frame-ancestors)
        'X-Frame-Options': 'DENY',
        // X-Content-Type-Options
        'X-Content-Type-Options': 'nosniff',
        // Referrer Policy
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        // X-XSS-Protection (legacy browsers)
        'X-XSS-Protection': '1; mode=block',
        // Permissions Policy (formerly Feature Policy)
        'Permissions-Policy': [
            'camera=()',
            'microphone=()',
            'geolocation=()',
            'payment=()',
            'usb=()',
            'magnetometer=()',
            'accelerometer=()',
            'gyroscope=()'
        ].join(', '),
        // Cross-Origin Policies
        'Cross-Origin-Embedder-Policy': 'require-corp',
        'Cross-Origin-Opener-Policy': 'same-origin',
        'Cross-Origin-Resource-Policy': 'same-origin'
    };
}
function generateCSPReportingConfig() {
    return {
        group: 'csp-endpoint',
        max_age: 10886400,
        endpoints: [
            {
                url: '/api/csp-report',
                priority: 1,
                weight: 1
            }
        ]
    };
}
const TRUSTED_SCRIPTS = [
    {
        url: 'https://cdn.jsdelivr.net/npm/react@18.2.0/umd/react.production.min.js',
        integrity: 'sha384-/bQdsTh/da6pkI1MST/rWKFNjaCP5gBSY4sEBT38Q/9RBh9AH40zEOg7Hlq2THRZ',
        crossorigin: 'anonymous',
        purpose: 'React library for production builds',
        lastVerified: '2025-01-24'
    }
];
function validateScriptIntegrity(url, expectedIntegrity) {
    const trustedScript = TRUSTED_SCRIPTS.find((script)=>script.url === url);
    return trustedScript?.integrity === expectedIntegrity;
}
function generateSRIPolicy() {
    return TRUSTED_SCRIPTS.map((script)=>`'${script.integrity}'`).join(' ');
}
function validateNonce(nonce) {
    // Nonce should be at least 16 characters, base64 encoded
    const base64Regex = /^[A-Za-z0-9+/]+=*$/;
    const hasMinLength = nonce.length >= 16;
    const isValidBase64 = base64Regex.test(nonce);
    // PHASE 3: Check for sufficient entropy (no repeated patterns)
    const hasEntropy = !/(.)\1{3,}/.test(nonce); // No character repeated 4+ times
    return hasMinLength && isValidBase64 && hasEntropy;
}
function analyzeCSPViolationRisk(violation) {
    const { violatedDirective, blockedUri } = violation;
    // Critical: Script injection attempts
    if (violatedDirective.includes('script-src') && (blockedUri.includes('javascript:') || blockedUri.includes('data:') || blockedUri.includes('blob:'))) {
        return 'CRITICAL';
    }
    // High: External script loading from untrusted domains
    if (violatedDirective.includes('script-src') && !TRUSTED_SCRIPTS.some((script)=>blockedUri.startsWith(script.url.split('/').slice(0, 3).join('/')))) {
        return 'HIGH';
    }
    // Medium: Style or image violations
    if (violatedDirective.includes('style-src') || violatedDirective.includes('img-src')) {
        return 'MEDIUM';
    }
    return 'LOW';
}
function generateSecureNonce() {
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
        // Browser environment
        const array = new Uint8Array(24); // 192 bits
        crypto.getRandomValues(array);
        return btoa(String.fromCharCode(...array));
    } else {
        // Node.js environment
        const crypto1 = __turbopack_context__.r("[project]/src/lib/security [middleware-edge] (ecmascript)");
        return crypto1.randomBytes(24).toString('base64');
    }
}
}}),
"[project]/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:buffer [external] (node:buffer, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
// SECURITY FIX: Removed insecure 'decrypt' import
// Session validation should be done server-side only with proper JWT validation
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$cspConfig$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/cspConfig.ts [middleware-edge] (ecmascript)");
;
;
// Protected routes that require authentication
const protectedRoutes = [
    '/dashboard',
    '/admin',
    '/delegations',
    '/tasks',
    '/vehicles',
    '/employees',
    '/reports',
    '/reporting',
    '/settings'
];
// Public routes that don't require authentication
const publicRoutes = new Set([
    '/',
    '/forgot-password',
    '/login',
    '/reset-password',
    '/signup'
]);
// Admin-only routes
const adminRoutes = [
    '/admin',
    '/settings/system',
    '/reports/admin'
];
async function middleware(request) {
    // SECURITY FIX: Remove production logging to prevent information disclosure
    // Only log in development environment
    if ("TURBOPACK compile-time truthy", 1) {
        console.log(`🔧 Middleware triggered for: ${request.nextUrl.pathname}`);
    }
    // Generate nonce for CSP
    const nonce = generateNonce();
    // Handle CORS for API routes
    const corsResponse = handleCORS(request);
    if (corsResponse) {
        return corsResponse;
    }
    // Handle authentication
    const authResponse = await handleAuthentication(request);
    if (authResponse) {
        return authResponse;
    }
    // Create response with security headers
    const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    // Set nonce in both headers and cookies for use in components
    response.headers.set('x-nonce', nonce);
    response.cookies.set('x-nonce', nonce, {
        httpOnly: false,
        path: '/',
        sameSite: 'strict',
        secure: ("TURBOPACK compile-time value", "development") === 'production'
    });
    // Debug logging
    if ("TURBOPACK compile-time truthy", 1) {
        console.log(`🍪 Setting nonce cookie: ${nonce.slice(0, 16)}...`);
    }
    // Enhanced 2025 Security Headers with Strict CSP
    const cspConfig = {
        isDevelopment: ("TURBOPACK compile-time value", "development") === 'development',
        nonce,
        reportUri: '/api/csp-report'
    };
    const securityHeaders = {
        'Content-Security-Policy': (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$cspConfig$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["generateStrictCSP"])(cspConfig),
        ...(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$cspConfig$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["generateSecurityHeaders"])(),
        // 2025 Standard: Report-To header for modern violation reporting
        'Report-To': JSON.stringify({
            endpoints: [
                {
                    url: '/api/csp-report'
                }
            ],
            group: 'csp-endpoint',
            max_age: 10_886_400
        })
    };
    // Apply security headers
    for (const [key, value] of Object.entries(securityHeaders)){
        response.headers.set(key, value);
    }
    // CORS headers for allowed origins
    const origin = request.headers.get('origin') ?? '';
    const allowedOrigins = [
        'https://workhub.company.com',
        'https://staging.workhub.company.com',
        ...("TURBOPACK compile-time truthy", 1) ? [
            'http://localhost:9002',
            'http://localhost:3000'
        ] : ("TURBOPACK unreachable", undefined)
    ];
    if (allowedOrigins.includes(origin)) {
        response.headers.set('Access-Control-Allow-Origin', origin);
        response.headers.set('Access-Control-Allow-Credentials', 'true');
    }
    return response;
}
/**
 * Enhanced CSP implementation now handled by cspConfig.ts
 * This provides strict 2025 security standards with comprehensive directives
 */ /**
 * Generate cryptographically secure nonce
 */ function generateNonce() {
    // Generate 32 random bytes for a secure nonce
    const bytes = new Uint8Array(32);
    crypto.getRandomValues(bytes);
    return __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$buffer__$5b$external$5d$__$28$node$3a$buffer$2c$__cjs$29$__["Buffer"].from(bytes).toString('base64');
}
/**
 * Authentication middleware
 */ async function handleAuthentication(request) {
    const path = request.nextUrl.pathname;
    const isProtectedRoute = protectedRoutes.some((route)=>path.startsWith(route));
    const isPublicRoute = publicRoutes.has(path);
    const isAdminRoute = adminRoutes.some((route)=>path.startsWith(route));
    // Skip auth for public routes and API routes
    if (isPublicRoute || path.startsWith('/api/') || path.startsWith('/_next/')) {
        return null;
    }
    // SECURITY FIX: Use proper JWT token validation instead of insecure session decryption
    // Get JWT token from secure httpOnly cookie (set by backend)
    const accessToken = request.cookies.get('sb-access-token')?.value;
    if (!accessToken && isProtectedRoute) {
        const loginUrl = new URL('/login', request.url);
        loginUrl.searchParams.set('redirect', path);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
    }
    if (accessToken) {
        try {
            // Basic JWT format validation (3 parts separated by dots)
            const tokenParts = accessToken.split('.');
            if (tokenParts.length !== 3) {
                throw new Error('Invalid JWT format');
            }
            // Decode JWT payload (this is safe as it's just base64 decoding for reading claims)
            // Note: This does NOT validate the signature - that must be done server-side
            const payloadPart = tokenParts[1];
            if (!payloadPart) {
                throw new Error('Invalid JWT payload');
            }
            const payload = JSON.parse(atob(payloadPart));
            // Check if token is expired
            const now = Math.floor(Date.now() / 1000);
            if (payload.exp && payload.exp < now) {
                throw new Error('Token expired');
            }
            // Extract user role for admin route protection
            const userRole = payload.user_role ?? payload.role ?? 'USER';
            // Admin route protection
            if (isAdminRoute && ![
                'ADMIN',
                'SUPER_ADMIN'
            ].includes(userRole)) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/unauthorized', request.url));
            }
            // Redirect authenticated users from auth pages
            if (payload.sub && (path === '/login' || path === '/signup')) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(new URL('/dashboard', request.url));
            }
        } catch  {
            // Invalid or expired token, redirect to login for protected routes
            if (isProtectedRoute) {
                const loginUrl = new URL('/login', request.url);
                loginUrl.searchParams.set('redirect', path);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(loginUrl);
            }
        }
    }
    return null;
}
/**
 * CORS middleware for API routes
 */ function handleCORS(request) {
    // Only apply CORS to API routes
    if (!request.nextUrl.pathname.startsWith('/api/')) {
        return null;
    }
    const allowedOrigins = [
        'https://workhub.company.com',
        'https://staging.workhub.company.com',
        ...("TURBOPACK compile-time truthy", 1) ? [
            'http://localhost:9002',
            'http://localhost:3000'
        ] : ("TURBOPACK unreachable", undefined)
    ];
    const origin = request.headers.get('origin') ?? '';
    const isAllowedOrigin = allowedOrigins.includes(origin);
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
        const preflightHeaders = {
            'Access-Control-Allow-Credentials': 'true',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-CSRF-Token, X-Requested-With',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Max-Age': '86400',
            ...isAllowedOrigin && {
                'Access-Control-Allow-Origin': origin
            }
        };
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"](null, {
            headers: preflightHeaders,
            status: 200
        });
    }
    return null;
}
const config = {
    matcher: [
        /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */ '/((?!_next/static|_next/image|favicon.ico).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__752a1ada._.js.map