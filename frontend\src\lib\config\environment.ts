/**
 * @file Environment Configuration Manager
 * @module lib/config/environment
 * 
 * Production-ready environment configuration system that automatically
 * detects deployment context and provides appropriate API endpoints.
 */

/**
 * Environment types supported by the application
 */
export type EnvironmentType = 'development' | 'production' | 'staging' | 'test';

/**
 * Deployment context detection
 */
export type DeploymentContext = 'localhost' | 'network' | 'docker' | 'cloud';

/**
 * Environment configuration interface
 */
export interface EnvironmentConfig {
  apiUrl: string;
  apiBaseUrl: string;
  wsUrl: string;
  environment: EnvironmentType;
  deploymentContext: DeploymentContext;
  isProduction: boolean;
  isDevelopment: boolean;
  enableDebugLogging: boolean;
}

/**
 * Detect the current deployment context based on window location
 */
function detectDeploymentContext(): DeploymentContext {
  if (typeof window === 'undefined') {
    // Server-side rendering - use environment variables
    return 'localhost';
  }

  const hostname = window.location.hostname;
  const port = window.location.port;

  // Cloud deployment detection
  if (hostname.includes('.vercel.app') || 
      hostname.includes('.netlify.app') || 
      hostname.includes('.herokuapp.com') ||
      hostname.includes('cloudworkstations.dev')) {
    return 'cloud';
  }

  // Docker deployment detection
  if (process.env.NEXT_PUBLIC_DOCKER_ENV === 'true') {
    return 'docker';
  }

  // Network access detection (non-localhost IP)
  if (hostname !== 'localhost' && hostname !== '127.0.0.1' && !hostname.startsWith('192.168.')) {
    return 'cloud';
  }

  // Local network detection
  if (hostname.startsWith('192.168.') || hostname.startsWith('10.') || hostname.startsWith('172.')) {
    return 'network';
  }

  return 'localhost';
}

/**
 * Generate API URLs based on deployment context
 */
function generateApiUrls(deploymentContext: DeploymentContext): {
  apiUrl: string;
  apiBaseUrl: string;
  wsUrl: string;
} {
  // Use environment variables if explicitly set
  if (process.env.NEXT_PUBLIC_API_BASE_URL && process.env.NEXT_PUBLIC_WS_URL) {
    return {
      apiUrl: process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_API_BASE_URL.replace('/api', ''),
      apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL,
      wsUrl: process.env.NEXT_PUBLIC_WS_URL,
    };
  }

  const hostname = typeof window !== 'undefined' ? window.location.hostname : 'localhost';
  const protocol = typeof window !== 'undefined' ? window.location.protocol : 'http:';
  const wsProtocol = protocol === 'https:' ? 'wss:' : 'ws:';

  switch (deploymentContext) {
    case 'network':
      // Use the same hostname but port 3001 for backend
      return {
        apiUrl: `${protocol}//${hostname}:3001`,
        apiBaseUrl: `${protocol}//${hostname}:3001/api`,
        wsUrl: `${wsProtocol}//${hostname}:3001`,
      };

    case 'docker':
      // Use service names in Docker environment
      return {
        apiUrl: `${protocol}//backend:3001`,
        apiBaseUrl: `${protocol}//backend:3001/api`,
        wsUrl: `${wsProtocol}//backend:3001`,
      };

    case 'cloud':
      // Use environment variables or infer from hostname
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || `${protocol}//${hostname}/api`;
      return {
        apiUrl: baseUrl.replace('/api', ''),
        apiBaseUrl: baseUrl,
        wsUrl: process.env.NEXT_PUBLIC_WS_URL || `${wsProtocol}//${hostname}`,
      };

    case 'localhost':
    default:
      // Default localhost configuration
      return {
        apiUrl: 'http://localhost:3001',
        apiBaseUrl: 'http://localhost:3001/api',
        wsUrl: 'ws://localhost:3001',
      };
  }
}

/**
 * Create environment configuration based on current context
 */
function createEnvironmentConfig(): EnvironmentConfig {
  const nodeEnv = (process.env.NODE_ENV || 'development') as EnvironmentType;
  const deploymentContext = detectDeploymentContext();
  const { apiUrl, apiBaseUrl, wsUrl } = generateApiUrls(deploymentContext);

  return {
    apiUrl,
    apiBaseUrl,
    wsUrl,
    environment: nodeEnv,
    deploymentContext,
    isProduction: nodeEnv === 'production',
    isDevelopment: nodeEnv === 'development',
    enableDebugLogging: process.env.NEXT_PUBLIC_ENABLE_DEBUG_LOGGING === 'true' || nodeEnv === 'development',
  };
}

/**
 * Global environment configuration instance
 */
export const environmentConfig: EnvironmentConfig = createEnvironmentConfig();

/**
 * Get current environment configuration
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  return environmentConfig;
}

/**
 * Log environment configuration for debugging
 */
export function logEnvironmentConfig(): void {
  if (environmentConfig.enableDebugLogging) {
    console.group('🌍 Environment Configuration');
    console.log('Environment:', environmentConfig.environment);
    console.log('Deployment Context:', environmentConfig.deploymentContext);
    console.log('API URL:', environmentConfig.apiUrl);
    console.log('API Base URL:', environmentConfig.apiBaseUrl);
    console.log('WebSocket URL:', environmentConfig.wsUrl);
    console.log('Is Production:', environmentConfig.isProduction);
    console.groupEnd();
  }
}

/**
 * Validate environment configuration
 */
export function validateEnvironmentConfig(): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!environmentConfig.apiBaseUrl) {
    errors.push('API Base URL is not configured');
  }

  if (!environmentConfig.wsUrl) {
    errors.push('WebSocket URL is not configured');
  }

  if (!environmentConfig.apiBaseUrl.startsWith('http')) {
    errors.push('API Base URL must start with http or https');
  }

  if (!environmentConfig.wsUrl.startsWith('ws')) {
    errors.push('WebSocket URL must start with ws or wss');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
