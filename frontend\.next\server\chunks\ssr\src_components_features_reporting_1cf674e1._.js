module.exports = {

"[project]/src/components/features/reporting/data/services/ReportGenerationService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// frontend/src/components/features/reporting/data/services/ReportGenerationService.ts
/**
 * Report Generation Service
 *
 * Follows SOLID Principles:
 * - SRP: Single responsibility for coordinating report generation
 * - OCP: Open for extension with new report types
 * - LSP: Implements consistent interface for all report types
 * - ISP: Focused interface for report generation
 * - DIP: Depends on abstractions (data services, export services)
 */ // Import types from the hook file where they are defined
__turbopack_context__.s({
    "ReportGenerationService": (()=>ReportGenerationService),
    "createReportGenerationService": (()=>createReportGenerationService),
    "reportGenerationService": (()=>reportGenerationService)
});
class ReportGenerationService {
    apiClient;
    constructor(apiClient){
        this.apiClient = apiClient;
    }
    /**
   * Generate aggregate analytics report for entity type
   *
   * @param config - Aggregate report configuration
   * @returns Promise<ReportGenerationResult>
   */ async generateAggregateReport(config) {
        try {
            const response = await this.apiClient.request({
                url: `/api/reporting/reports/aggregate/${config.entityType}`,
                method: 'POST',
                data: {
                    filters: config.filters,
                    template: config.template || 'default',
                    format: 'json',
                    options: config.options || {}
                }
            });
            // Normalize response structure - handle nested aggregate data
            const responseData = response.data.data || response.data;
            const aggregateData = responseData?.aggregate || responseData;
            return {
                data: aggregateData,
                metadata: response.data.metadata || responseData?.metadata || {
                    id: `aggregate_${config.entityType}_${Date.now()}`,
                    type: 'aggregate',
                    entityType: config.entityType,
                    format: config.format || 'json',
                    template: config.template || 'default',
                    generatedAt: new Date().toISOString(),
                    generatedBy: 'system',
                    filters: config.filters,
                    options: config.options
                }
            };
        } catch (error) {
            console.error('Failed to generate aggregate report:', error);
            throw new Error(`Failed to generate ${config.entityType} aggregate report`);
        }
    }
    /**
   * Generate individual entity report
   *
   * @param config - Individual report configuration
   * @returns Promise<ReportGenerationResult>
   */ async generateIndividualReport(config) {
        try {
            const response = await this.apiClient.request({
                url: `/api/reporting/reports/individual/${config.entityType}/${config.entityId}`,
                method: 'POST',
                data: {
                    template: config.template || 'default',
                    format: 'json',
                    options: config.options || {}
                }
            });
            return {
                data: response.data.data || response.data,
                metadata: response.data.metadata || {
                    id: `individual_${config.entityType}_${config.entityId}_${Date.now()}`,
                    type: 'individual',
                    entityType: config.entityType,
                    entityId: config.entityId,
                    format: config.format || 'json',
                    template: config.template || 'default',
                    generatedAt: new Date().toISOString(),
                    generatedBy: 'system',
                    options: config.options
                }
            };
        } catch (error) {
            console.error('Failed to generate individual report:', error);
            throw new Error(`Failed to generate ${config.entityType} individual report`);
        }
    }
    /**
   * Generate comprehensive cross-entity report
   *
   * @param config - Comprehensive report configuration
   * @returns Promise<ReportGenerationResult>
   */ async generateComprehensiveReport(config) {
        try {
            const response = await this.apiClient.request({
                url: '/api/reporting/reports/generate',
                method: 'POST',
                data: {
                    entityTypes: config.entityTypes,
                    filters: config.filters,
                    template: config.template || 'comprehensive',
                    format: 'json',
                    options: config.options || {}
                }
            });
            return {
                data: response.data.data || response.data,
                metadata: response.data.metadata || {
                    id: `comprehensive_${Date.now()}`,
                    type: 'comprehensive',
                    entityTypes: config.entityTypes,
                    format: config.format || 'json',
                    template: config.template || 'comprehensive',
                    generatedAt: new Date().toISOString(),
                    generatedBy: 'system',
                    filters: config.filters,
                    options: config.options
                }
            };
        } catch (error) {
            console.error('Failed to generate comprehensive report:', error);
            throw new Error('Failed to generate comprehensive report');
        }
    }
}
/**
 * Default API client implementation
 * Note: This will be injected from the hook that has access to secureRequest
 */ class DefaultApiClient {
    async request(config) {
        throw new Error('API client not initialized. Use createReportGenerationService with proper API client.');
    }
}
const createReportGenerationService = (apiClient)=>{
    return new ReportGenerationService(apiClient);
};
const reportGenerationService = new ReportGenerationService(new DefaultApiClient());
}}),
"[project]/src/components/features/reporting/data/transformers/reportingTransformers.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// PHASE 1 ENHANCEMENT: Import new entity analytics types
__turbopack_context__.s({
    "transformCrossEntityAnalytics": (()=>transformCrossEntityAnalytics),
    "transformDelegationAnalytics": (()=>transformDelegationAnalytics),
    "transformEmployeeAnalytics": (()=>transformEmployeeAnalytics),
    "transformLocationMetrics": (()=>transformLocationMetrics),
    "transformServiceCostSummary": (()=>transformServiceCostSummary),
    "transformServiceHistory": (()=>transformServiceHistory),
    "transformTaskAnalytics": (()=>transformTaskAnalytics),
    "transformTaskAnalyticsData": (()=>transformTaskAnalyticsData),
    "transformTaskMetrics": (()=>transformTaskMetrics),
    "transformTrendData": (()=>transformTrendData),
    "transformVehicleAnalytics": (()=>transformVehicleAnalytics)
});
/**
 * Helper function to get color for delegation status.
 * FIXED: Use correct Prisma enum values instead of incorrect UPPERCASE versions.
 * @param status - Delegation status
 * @returns Color string for the status
 */ const getStatusColor = (status)=>{
    const colorMap = {
        Planned: '#3b82f6',
        Confirmed: '#10b981',
        In_Progress: '#f59e0b',
        Completed: '#22c55e',
        Cancelled: '#ef4444',
        No_details: '#6b7280'
    };
    return colorMap[status] || '#6b7280';
};
/**
 * PHASE 1 ENHANCEMENT: Helper function to get color for task status
 * @param status - Task status
 * @returns Color string for the status
 */ const getTaskStatusColor = (status)=>{
    const colorMap = {
        Pending: '#6b7280',
        Assigned: '#3b82f6',
        In_Progress: '#f59e0b',
        Completed: '#22c55e',
        Cancelled: '#ef4444'
    };
    return colorMap[status] || '#6b7280';
};
/**
 * PHASE 1 ENHANCEMENT: Helper function to get color for task priority
 * @param priority - Task priority
 * @returns Color string for the priority
 */ const getTaskPriorityColor = (priority)=>{
    const colorMap = {
        Low: '#10b981',
        Medium: '#f59e0b',
        High: '#ef4444'
    };
    return colorMap[priority] || '#6b7280';
};
const transformDelegationAnalytics = (rawData)=>{
    const result = {
        totalCount: rawData.totalCount || 0,
        statusDistribution: rawData.statusDistribution?.map((item)=>({
                status: item.status,
                count: item.count || 0,
                percentage: item.percentage || 0,
                color: getStatusColor(item.status)
            })) || [],
        trendData: rawData.trendData?.map((item)=>({
                date: item.date,
                created: item.created || 0,
                completed: item.completed || 0,
                inProgress: item.inProgress || 0
            })) || [],
        locationMetrics: rawData.locationMetrics || [],
        summary: {
            totalDelegations: rawData.summary?.totalDelegations || 0,
            activeDelegations: rawData.summary?.activeDelegations || 0,
            completedDelegations: rawData.summary?.completedDelegations || 0,
            totalDelegates: rawData.summary?.totalDelegates || 0,
            averageDuration: rawData.summary?.averageDuration || 0,
            completionRate: rawData.summary?.completionRate || 0
        },
        // FIXED: Initialize delegations array
        delegations: []
    };
    // ENHANCED: Optional service history data transformation
    if (rawData.serviceHistory) {
        result.serviceHistory = transformServiceHistory(rawData.serviceHistory);
    }
    if (rawData.serviceCosts && typeof rawData.serviceCosts === 'object' && !Array.isArray(rawData.serviceCosts)) {
        result.serviceCosts = transformServiceCostSummary(rawData.serviceCosts);
    }
    if (rawData.taskData && typeof rawData.taskData === 'object' && !Array.isArray(rawData.taskData)) {
        result.taskData = transformTaskAnalyticsData(rawData.taskData);
    }
    // FIXED: Add real delegations data from API response
    result.delegations = rawData.delegations?.map((item)=>({
            id: (item.id || 0).toString(),
            delegationId: item.delegationId || item.id?.toString() || '',
            customerName: item.customerName || item.customer?.name || 'Unknown Customer',
            vehicleModel: item.vehicleModel || item.vehicle?.model || 'Unknown Vehicle',
            licensePlate: item.licensePlate || item.vehicle?.licensePlate || 'Unknown',
            status: item.status,
            assignedEmployee: item.driverEmployee?.name || item.staffEmployee?.name || 'Unassigned',
            location: item.location || '',
            createdAt: item.createdAt || '',
            completedAt: item.completedAt
        })) || [];
    return result;
};
const transformTaskMetrics = (rawData)=>{
    return {
        totalTasks: rawData.totalTasks || 0,
        completedTasks: rawData.completedTasks || 0,
        pendingTasks: rawData.pendingTasks || 0,
        inProgressTasks: rawData.inProgressTasks || 0,
        overdueTasks: rawData.overdueTasks || 0,
        averageCompletionTime: rawData.averageCompletionTime || 0,
        tasksByPriority: rawData.tasksByPriority?.map((item)=>({
                priority: item.priority,
                count: item.count || 0
            })) || [],
        tasksByStatus: rawData.tasksByStatus?.map((item)=>({
                status: item.status,
                count: item.count || 0
            })) || []
    };
};
const transformTrendData = (rawData)=>{
    return rawData?.map((item)=>({
            date: item.date,
            created: item.created || 0,
            completed: item.completed || 0,
            inProgress: item.inProgress || 0
        })) || [];
};
const transformLocationMetrics = (rawData)=>{
    return rawData?.map((item)=>({
            location: item.location || '',
            delegationCount: item.delegationCount || item.delegationsCount || item.count || 0,
            averageDuration: item.averageDuration || 0,
            completionRate: item.completionRate || 0
        })) || [];
};
const transformServiceHistory = (rawData)=>{
    return {
        id: rawData.id || '',
        vehicleId: rawData.vehicleId || 0,
        vehicleName: rawData.vehicleName || '',
        serviceType: rawData.serviceType,
        status: rawData.status,
        scheduledDate: rawData.scheduledDate || '',
        completedDate: rawData.completedDate,
        cost: rawData.cost || 0,
        description: rawData.description || '',
        relatedDelegationId: rawData.relatedDelegationId,
        relatedTaskId: rawData.relatedTaskId
    };
};
const transformServiceCostSummary = (rawData)=>{
    return {
        totalCost: rawData.totalCost || 0,
        averageCostPerService: rawData.averageCostPerService || 0,
        costByType: rawData.costByType?.map((item)=>({
                type: item.type,
                cost: item.cost || 0,
                count: item.count || 0
            })) || [],
        monthlyTrend: rawData.monthlyTrend?.map((item)=>({
                month: item.month || '',
                cost: item.cost || 0
            })) || []
    };
};
const transformTaskAnalyticsData = (rawData)=>{
    return {
        totalTasks: rawData.totalTasks || 0,
        completedTasks: rawData.completedTasks || 0,
        pendingTasks: rawData.pendingTasks || 0,
        overdueTasks: rawData.overdueTasks || 0,
        averageCompletionTime: rawData.averageCompletionTime || 0,
        tasksByPriority: rawData.tasksByPriority?.map((item)=>({
                priority: item.priority,
                count: item.count || 0
            })) || []
    };
};
const transformTaskAnalytics = (rawData)=>{
    const totalCount = rawData.totalCount || 0;
    return {
        totalCount,
        statusDistribution: rawData.statusDistribution?.map((item)=>({
                status: item.status,
                count: item.count || 0,
                percentage: totalCount > 0 ? (item.count || 0) / totalCount * 100 : 0,
                color: getTaskStatusColor(item.status)
            })) || [],
        priorityDistribution: rawData.priorityDistribution?.map((item)=>({
                priority: item.priority,
                count: item.count || 0,
                percentage: totalCount > 0 ? (item.count || 0) / totalCount * 100 : 0,
                color: getTaskPriorityColor(item.priority)
            })) || [],
        completionRate: rawData.completionRate || 0,
        overdueCount: rawData.overdueCount || 0,
        averageCompletionTime: rawData.averageCompletionTime || 0,
        assignmentMetrics: rawData.assignmentMetrics?.map((item)=>({
                employeeId: item.employeeId || 0,
                employeeName: item.employeeName || '',
                assignedTasks: item.assignedTasks || 0,
                completedTasks: item.completedTasks || 0,
                completionRate: item.completionRate || 0,
                averageCompletionTime: item.averageCompletionTime || 0
            })) || [],
        trendData: rawData.trendData?.map((item)=>({
                date: item.date || '',
                created: item.created || 0,
                completed: item.completed || 0,
                inProgress: item.inProgress || 0,
                overdue: item.overdue || 0
            })) || []
    };
};
const transformVehicleAnalytics = (rawData)=>{
    return {
        totalCount: rawData.totalCount || 0,
        serviceHistory: rawData.serviceHistory?.map((item)=>transformServiceHistory(item)) || [],
        costAnalysis: rawData.costAnalysis ? transformServiceCostSummary(rawData.costAnalysis) : {
            totalCost: 0,
            averageCostPerService: 0,
            costByType: [],
            monthlyTrend: []
        },
        utilizationMetrics: rawData.utilizationMetrics?.map((item)=>({
                vehicleId: item.vehicleId || 0,
                vehicleName: item.vehicleName || '',
                utilizationRate: item.utilizationRate || 0,
                totalDelegations: item.totalDelegations || 0,
                activeDelegations: item.activeDelegations || 0,
                maintenanceHours: item.maintenanceHours || 0
            })) || [],
        maintenanceSchedule: rawData.maintenanceSchedule?.map((item)=>({
                vehicleId: item.vehicleId || 0,
                vehicleName: item.vehicleName || '',
                nextMaintenanceDate: item.nextMaintenanceDate || '',
                maintenanceType: item.maintenanceType,
                priority: item.priority || 'Medium',
                estimatedCost: item.estimatedCost || 0
            })) || [],
        performanceMetrics: rawData.performanceMetrics?.map((item)=>({
                vehicleId: item.vehicleId || 0,
                vehicleName: item.vehicleName || '',
                fuelEfficiency: item.fuelEfficiency || 0,
                maintenanceCost: item.maintenanceCost || 0,
                downtime: item.downtime || 0,
                reliabilityScore: item.reliabilityScore || 0
            })) || []
    };
};
const transformEmployeeAnalytics = (rawData)=>{
    return {
        totalCount: rawData.totalCount || 0,
        performanceMetrics: rawData.performanceMetrics?.map((item)=>({
                employeeId: item.employeeId || 0,
                employeeName: item.employeeName || '',
                completedDelegations: item.completedDelegations || 0,
                completedTasks: item.completedTasks || 0,
                averageRating: item.averageRating || 0,
                onTimePerformance: item.onTimePerformance || 0,
                workloadScore: item.workloadScore || 0
            })) || [],
        delegationHistory: rawData.delegationHistory?.map((item)=>({
                employeeId: item.employeeId || 0,
                employeeName: item.employeeName || '',
                totalDelegations: item.totalDelegations || 0,
                completedDelegations: item.completedDelegations || 0,
                averageDuration: item.averageDuration || 0,
                successRate: item.successRate || 0
            })) || [],
        taskAssignments: rawData.taskAssignments?.map((item)=>({
                employeeId: item.employeeId || 0,
                employeeName: item.employeeName || '',
                assignedTasks: item.assignedTasks || 0,
                completedTasks: item.completedTasks || 0,
                pendingTasks: item.pendingTasks || 0,
                overdueTasksCount: item.overdueTasksCount || 0
            })) || [],
        availabilityMetrics: rawData.availabilityMetrics?.map((item)=>({
                employeeId: item.employeeId || 0,
                employeeName: item.employeeName || '',
                availableHours: item.availableHours || 0,
                scheduledHours: item.scheduledHours || 0,
                utilizationRate: item.utilizationRate || 0,
                overtimeHours: item.overtimeHours || 0
            })) || [],
        workloadDistribution: rawData.workloadDistribution?.map((item)=>({
                employeeId: item.employeeId || 0,
                employeeName: item.employeeName || '',
                currentWorkload: item.currentWorkload || 0,
                capacity: item.capacity || 0,
                workloadPercentage: item.workloadPercentage || 0,
                status: item.status || 'Optimal'
            })) || []
    };
};
const transformCrossEntityAnalytics = (rawData)=>{
    const result = {
        correlations: {
            employeeVehicle: rawData.correlations?.employeeVehicle || [],
            taskDelegation: rawData.correlations?.taskDelegation || [],
            performanceWorkload: rawData.correlations?.performanceWorkload || [],
            overall: rawData.correlations?.overall || []
        },
        metrics: {
            employeeVehicle: rawData.metrics?.employeeVehicle || 0,
            taskDelegation: rawData.metrics?.taskDelegation || 0,
            performanceWorkload: rawData.metrics?.performanceWorkload || 0,
            overallEfficiency: rawData.metrics?.overallEfficiency || 0
        }
    };
    // Add optional properties only if they exist
    if (rawData.network) {
        result.network = {
            nodes: rawData.network.nodes || [],
            edges: rawData.network.edges || []
        };
    }
    if (rawData.insights) {
        result.insights = rawData.insights || [];
    }
    return result;
};
}}),
"[project]/src/components/features/reporting/data/services/ReportingDataService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// frontend/src/components/features/reporting/data/services/ReportingDataService.ts
__turbopack_context__.s({
    "ReportingDataService": (()=>ReportingDataService),
    "reportingDataService": (()=>reportingDataService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$transformers$2f$reportingTransformers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/transformers/reportingTransformers.ts [app-ssr] (ecmascript)");
;
;
class ReportingDataService {
    baseUrl;
    constructor(baseUrl = '/api/reporting'){
        this.baseUrl = baseUrl;
    }
    /**
   * PHASE 1 ENHANCEMENT: Get cross-entity analytics data
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to cross-entity analytics data
   */ async getCrossEntityAnalytics(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const queryParams = new URLSearchParams(queryParamsString);
            // Add cross-entity parameters
            if (filters.includeCrossEntityCorrelations) {
                queryParams.append('includeCrossEntityCorrelations', 'true');
            }
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/cross-entity/analytics?${queryParams.toString()}`);
            return apiResponse.data || apiResponse;
        } catch (error) {
            console.error('Error fetching cross-entity analytics:', error);
            throw new Error(`Failed to load cross-entity analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Fetches delegation analytics data based on provided filters
   * ENHANCED: Now supports service history and task data integration
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to delegation analytics data
   */ async getDelegationAnalytics(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const queryParams = new URLSearchParams(queryParamsString);
            // ENHANCED: Add service history and task data parameters
            if (filters.includeServiceHistory) {
                queryParams.append('includeServiceHistory', 'true');
            }
            if (filters.includeTaskData) {
                queryParams.append('includeTaskData', 'true');
            }
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/delegations/analytics?${queryParams.toString()}`);
            // Backend returns { status: 'success', data: {...}, timestamp: '...' }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$transformers$2f$reportingTransformers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transformDelegationAnalytics"])(apiResponse.data || apiResponse);
        } catch (error) {
            console.error('Error fetching delegation analytics:', error);
            throw new Error(`Failed to load delegation analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Fetches paginated delegations data based on provided filters
   * @param filters - Reporting filters to apply
   * @param pagination - Pagination parameters
   * @returns Promise resolving to paginated delegations response
   */ async getDelegations(filters, pagination) {
        try {
            const queryParams = this.buildQueryParams(filters);
            const paginationParams = new URLSearchParams(queryParams);
            // Add pagination parameters
            paginationParams.append('page', pagination.page.toString());
            paginationParams.append('pageSize', pagination.pageSize.toString());
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/delegations?${paginationParams.toString()}`);
            // Backend returns { status: 'success', data: {...}, timestamp: '...' }
            return apiResponse.data || apiResponse;
        } catch (error) {
            console.error('Error fetching delegations:', error);
            throw new Error(`Failed to load delegations: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * PHASE 1 ENHANCEMENT: Get employee analytics data
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to employee analytics data
   */ async getEmployeeAnalytics(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/employee/analytics?${queryParamsString}`);
            return apiResponse.data || apiResponse;
        } catch (error) {
            console.error('Error fetching employee analytics:', error);
            throw new Error(`Failed to load employee analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Fetches location metrics based on provided filters
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to location metrics array
   */ async getLocationMetrics(filters) {
        try {
            const queryParams = this.buildQueryParams(filters);
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/locations/metrics?${queryParams}`);
            // Backend returns { status: 'success', data: [...], timestamp: '...' }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$transformers$2f$reportingTransformers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transformLocationMetrics"])(apiResponse.data || apiResponse);
        } catch (error) {
            console.error('Error fetching location metrics:', error);
            throw new Error(`Failed to load location metrics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    // Private helper methods for data transformation and utility functions
    /**
   * ENHANCED: Fetches service cost summary
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to service cost summary
   */ async getServiceCostSummary(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const rawData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/services/costs?${queryParamsString}`);
            return rawData.data || {
                averageCostPerService: 0,
                costByType: [],
                monthlyTrend: [],
                totalCost: 0
            };
        } catch (error) {
            console.error('Error fetching service costs:', error);
            throw new Error(`Failed to load service costs: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * ENHANCED: Fetches service history data for vehicles
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to service history data
   */ async getServiceHistory(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const rawData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/services/history?${queryParamsString}`);
            // Transform service history data if needed
            return rawData.data || [];
        } catch (error) {
            console.error('Error fetching service history:', error);
            throw new Error(`Failed to load service history: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * PHASE 1 ENHANCEMENT: Get task analytics data
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to task analytics data
   */ async getTaskAnalytics(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const queryParams = new URLSearchParams(queryParamsString);
            // Add task-specific parameters
            if (filters.taskStatus && filters.taskStatus.length > 0) {
                queryParams.append('taskStatus', filters.taskStatus.join(','));
            }
            if (filters.taskPriority && filters.taskPriority.length > 0) {
                queryParams.append('taskPriority', filters.taskPriority.join(','));
            }
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/tasks/analytics?${queryParams.toString()}`);
            return apiResponse.data || apiResponse;
        } catch (error) {
            console.error('Error fetching task analytics:', error);
            throw new Error(`Failed to load task analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Fetches task metrics for specific delegations or all tasks
   * @param delegationIds - Optional array of delegation IDs to filter by
   * @returns Promise resolving to task metrics data
   */ async getTaskMetrics(delegationIds) {
        try {
            const queryParams = delegationIds?.length ? `delegationIds=${delegationIds.join(',')}` : '';
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/tasks/metrics?${queryParams}`);
            // Backend returns { status: 'success', data: {...}, timestamp: '...' }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$transformers$2f$reportingTransformers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transformTaskMetrics"])(apiResponse.data || apiResponse);
        } catch (error) {
            console.error('Error fetching task metrics:', error);
            throw new Error(`Failed to load task metrics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * Fetches trend data based on provided filters
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to trend data array
   */ async getTrendData(filters) {
        try {
            const queryParams = this.buildQueryParams(filters);
            const apiResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/trends?${queryParams}`);
            // Backend returns { status: 'success', data: [...], timestamp: '...' }
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$transformers$2f$reportingTransformers$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["transformTrendData"])(apiResponse.data || apiResponse);
        } catch (error) {
            console.error('Error fetching trend data:', error);
            throw new Error(`Failed to load trend data: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
   * PHASE 1 ENHANCEMENT: Get vehicle analytics data
   * @param filters - Reporting filters to apply
   * @returns Promise resolving to vehicle analytics data
   */ async getVehicleAnalytics(filters) {
        try {
            const queryParamsString = this.buildQueryParams(filters);
            const queryParams = new URLSearchParams(queryParamsString);
            // Add vehicle-specific parameters
            if (filters.vehicles && filters.vehicles.length > 0) {
                queryParams.append('vehicles', filters.vehicles.join(','));
            }
            if (filters.serviceTypes && filters.serviceTypes.length > 0) {
                queryParams.append('serviceTypes', filters.serviceTypes.join(','));
            }
            if (filters.serviceStatus && filters.serviceStatus.length > 0) {
                queryParams.append('serviceStatus', filters.serviceStatus.join(','));
            }
            const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
            return result.data || result;
        } catch (error) {
            console.error('Error fetching vehicle analytics:', error);
            throw error;
        }
    }
    /**
   * PHASE 1 ENHANCEMENT: Helper method for appending array parameters (DRY principle)
   * @param params - URLSearchParams object
   * @param key - Parameter key
   * @param values - Array of values to append
   */ appendArrayParams(params, key, values) {
        if (values && values.length > 0) {
            params.append(key, values.join(','));
        }
    }
    /**
   * PHASE 1 ENHANCEMENT: Builds query parameters string from filters object
   * Following DRY principle: Reusable query building logic
   * @param filters - Reporting filters
   * @returns URL-encoded query parameters string
   */ buildQueryParams(filters) {
        const params = new URLSearchParams();
        // Date range - use the format expected by backend validation
        // Defensive programming: Ensure dates are Date objects
        try {
            const fromDate = filters.dateRange.from instanceof Date ? filters.dateRange.from : new Date(filters.dateRange.from);
            const toDate = filters.dateRange.to instanceof Date ? filters.dateRange.to : new Date(filters.dateRange.to);
            // Validate that dates are valid
            if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
                throw new TypeError('Invalid date range provided');
            }
            params.append('dateRange.from', fromDate.toISOString().split('T')[0] || fromDate.toISOString());
            params.append('dateRange.to', toDate.toISOString().split('T')[0] || toDate.toISOString());
        } catch (error) {
            console.error('Error processing date range:', error);
            // Fallback to default date range (last 30 days)
            const defaultToDate = new Date();
            const defaultFromDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
            params.append('dateRange.from', defaultFromDate.toISOString().split('T')[0] || defaultFromDate.toISOString());
            params.append('dateRange.to', defaultToDate.toISOString().split('T')[0] || defaultToDate.toISOString());
        }
        // Common array handling using helper method (DRY principle)
        this.appendArrayParams(params, 'status', filters.status);
        this.appendArrayParams(params, 'locations', filters.locations);
        this.appendArrayParams(params, 'employees', filters.employees);
        this.appendArrayParams(params, 'vehicles', filters.vehicles);
        // PHASE 1: Additional entity parameters
        if (filters.taskStatus) {
            this.appendArrayParams(params, 'taskStatus', filters.taskStatus);
        }
        if (filters.taskPriority) {
            this.appendArrayParams(params, 'taskPriority', filters.taskPriority);
        }
        if (filters.serviceTypes) {
            this.appendArrayParams(params, 'serviceTypes', filters.serviceTypes);
        }
        if (filters.serviceStatus) {
            this.appendArrayParams(params, 'serviceStatus', filters.serviceStatus);
        }
        // Cost range handling
        if (filters.costRange) {
            params.append('minCost', filters.costRange.min.toString());
            params.append('maxCost', filters.costRange.max.toString());
        }
        return params.toString();
    }
}
const reportingDataService = new ReportingDataService();
}}),
"[project]/src/components/features/reporting/data/hooks/useReportingData.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Reporting Data Hooks
 * @description Contains custom hooks for fetching and managing reporting data.
 */ __turbopack_context__.s({
    "useDelegationAnalytics": (()=>useDelegationAnalytics),
    "useLocationMetrics": (()=>useLocationMetrics),
    "useTaskMetrics": (()=>useTaskMetrics),
    "useTrendData": (()=>useTrendData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/services/ReportingDataService.ts [app-ssr] (ecmascript)");
;
;
const useDelegationAnalytics = (filters, options = {})=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'delegationAnalytics',
        filters
    ], ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getDelegationAnalytics(filters), options);
};
const useTaskMetrics = (delegationIds, options = {} // Replace 'any' with TaskMetrics type
)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'taskMetrics',
        delegationIds
    ], ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getTaskMetrics(delegationIds), {
        enabled: !!delegationIds && delegationIds.length > 0,
        ...options
    });
};
const useTrendData = (filters, options = {})=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'trendData',
        filters
    ], ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getTrendData(filters), options);
};
const useLocationMetrics = (filters, options = {})=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'locationMetrics',
        filters
    ], ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getLocationMetrics(filters), options);
};
}}),
"[project]/src/components/features/reporting/data/hooks/useDelegations.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file useDelegations Hook
 * @description A hook for fetching paginated delegation data.
 */ __turbopack_context__.s({
    "useDelegations": (()=>useDelegations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/hooks/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/services/ReportingDataService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
;
const useDelegations = (filters, pagination, options)=>{
    // FIXED: Use analytics endpoint which includes delegations data
    const analyticsQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'delegations',
        filters,
        pagination
    ], ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getDelegationAnalytics(filters), {
        placeholderData: (prev)=>prev,
        showErrorToast: true,
        ...options
    });
    // Transform analytics response to match expected PaginatedDelegationsResponse format
    const transformedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!analyticsQuery.data?.delegations) {
            return undefined;
        }
        // Type assertion since we know the backend returns AnalyticsDelegation structure
        const allDelegations = analyticsQuery.data.delegations;
        const startIndex = (pagination.page - 1) * pagination.pageSize;
        const endIndex = startIndex + pagination.pageSize;
        const paginatedDelegations = allDelegations.slice(startIndex, endIndex);
        return {
            data: paginatedDelegations.map((delegation)=>({
                    id: delegation.id,
                    delegationId: delegation.id.toString(),
                    customerName: delegation.title,
                    vehicleModel: 'N/A',
                    licensePlate: 'N/A',
                    status: delegation.status,
                    assignedEmployee: delegation.assignedTo,
                    location: delegation.location,
                    createdAt: delegation.createdAt,
                    completedAt: delegation.completedAt || null
                })),
            meta: {
                total: allDelegations.length,
                page: pagination.page,
                pageSize: pagination.pageSize,
                totalPages: Math.ceil(allDelegations.length / pagination.pageSize)
            }
        };
    }, [
        analyticsQuery.data?.delegations,
        pagination.page,
        pagination.pageSize
    ]);
    return {
        ...analyticsQuery,
        data: transformedData
    };
};
}}),
"[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// frontend/src/components/features/reporting/data/hooks/useReportingQueries.ts
__turbopack_context__.s({
    "reportingQueryKeys": (()=>reportingQueryKeys),
    "useCrossEntityAnalytics": (()=>useCrossEntityAnalytics),
    "useDelegationAnalytics": (()=>useDelegationAnalytics),
    "useEmployeeAnalytics": (()=>useEmployeeAnalytics),
    "useLocationMetrics": (()=>useLocationMetrics),
    "usePrefetchReportingData": (()=>usePrefetchReportingData),
    "useReportingData": (()=>useReportingData),
    "useServiceCostSummary": (()=>useServiceCostSummary),
    "useServiceHistory": (()=>useServiceHistory),
    "useTaskAnalytics": (()=>useTaskAnalytics),
    "useTaskMetrics": (()=>useTaskMetrics),
    "useTrendData": (()=>useTrendData),
    "useVehicleAnalytics": (()=>useVehicleAnalytics)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQuery.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useQueries.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/services/ReportingDataService.ts [app-ssr] (ecmascript)");
;
;
;
const reportingQueryKeys = {
    all: [
        'reporting'
    ],
    analytics: ()=>[
            ...reportingQueryKeys.all,
            'analytics'
        ],
    delegationAnalytics: (filters)=>[
            ...reportingQueryKeys.analytics(),
            'delegations',
            filters
        ],
    taskMetrics: (delegationIds)=>[
            ...reportingQueryKeys.all,
            'tasks',
            'metrics',
            delegationIds
        ],
    trends: (filters)=>[
            ...reportingQueryKeys.all,
            'trends',
            filters
        ],
    locationMetrics: (filters)=>[
            ...reportingQueryKeys.all,
            'locations',
            'metrics',
            filters
        ],
    // ENHANCED: Service history query keys
    serviceHistory: (filters)=>[
            ...reportingQueryKeys.all,
            'services',
            'history',
            filters
        ],
    serviceCosts: (filters)=>[
            ...reportingQueryKeys.all,
            'services',
            'costs',
            filters
        ],
    // PHASE 1: New entity analytics query keys
    taskAnalytics: (filters)=>[
            ...reportingQueryKeys.analytics(),
            'tasks',
            filters
        ],
    vehicleAnalytics: (filters)=>[
            ...reportingQueryKeys.analytics(),
            'vehicles',
            filters
        ],
    employeeAnalytics: (filters)=>[
            ...reportingQueryKeys.analytics(),
            'employees',
            filters
        ],
    crossEntityAnalytics: (filters)=>[
            ...reportingQueryKeys.analytics(),
            'cross-entity',
            filters
        ]
};
const useDelegationAnalytics = (filters, options)=>{
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>reportingQueryKeys.delegationAnalytics(filters), [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getDelegationAnalytics(filters), [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false,
        refetchOnMount: true,
        ...options
    });
};
const useTaskMetrics = (delegationIds, options)=>{
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>reportingQueryKeys.taskMetrics(delegationIds), [
        delegationIds
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getTaskMetrics(delegationIds), [
        delegationIds
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 2 * 60 * 1000,
        gcTime: 5 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false,
        ...options
    });
};
const useTrendData = (filters, options)=>{
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>reportingQueryKeys.trends(filters), [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getTrendData(filters), [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false,
        ...options
    });
};
const useLocationMetrics = (filters, options)=>{
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>reportingQueryKeys.locationMetrics(filters), [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getLocationMetrics(filters), [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false,
        ...options
    });
};
const useReportingData = (filters, delegationIds)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQueries$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueries"])({
        queries: [
            {
                queryKey: reportingQueryKeys.delegationAnalytics(filters),
                queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getDelegationAnalytics(filters),
                staleTime: 5 * 60 * 1000,
                gcTime: 10 * 60 * 1000,
                retry: 3,
                retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
            },
            {
                queryKey: reportingQueryKeys.taskMetrics(delegationIds),
                queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getTaskMetrics(delegationIds),
                staleTime: 2 * 60 * 1000,
                gcTime: 5 * 60 * 1000,
                retry: 3,
                retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
            },
            {
                queryKey: reportingQueryKeys.trends(filters),
                queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getTrendData(filters),
                staleTime: 5 * 60 * 1000,
                gcTime: 10 * 60 * 1000,
                retry: 3,
                retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
            },
            {
                queryKey: reportingQueryKeys.locationMetrics(filters),
                queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getLocationMetrics(filters),
                staleTime: 5 * 60 * 1000,
                gcTime: 10 * 60 * 1000,
                retry: 3,
                retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000)
            }
        ]
    });
};
const usePrefetchReportingData = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const prefetchDelegationAnalytics = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((filters)=>{
        return queryClient.prefetchQuery({
            queryKey: reportingQueryKeys.delegationAnalytics(filters),
            queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getDelegationAnalytics(filters),
            staleTime: 5 * 60 * 1000
        });
    }, [
        queryClient
    ]);
    const prefetchTaskMetrics = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((delegationIds)=>{
        return queryClient.prefetchQuery({
            queryKey: reportingQueryKeys.taskMetrics(delegationIds),
            queryFn: ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getTaskMetrics(delegationIds),
            staleTime: 2 * 60 * 1000
        });
    }, [
        queryClient
    ]);
    return {
        prefetchDelegationAnalytics,
        prefetchTaskMetrics
    };
};
const useServiceHistory = (filters, options)=>{
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>reportingQueryKeys.serviceHistory(filters), [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getServiceHistory(filters), [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 3 * 60 * 1000,
        gcTime: 8 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false,
        enabled: !!filters.includeServiceHistory,
        ...options
    });
};
const useTaskAnalytics = (filters, options)=>{
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>reportingQueryKeys.taskAnalytics(filters), [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getTaskAnalytics(filters), [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 3 * 60 * 1000,
        gcTime: 8 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false,
        enabled: !!filters.includeTaskData,
        ...options
    });
};
const useVehicleAnalytics = (filters, options)=>{
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>reportingQueryKeys.vehicleAnalytics(filters), [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getVehicleAnalytics(filters), [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false,
        enabled: !!filters.includeVehicleAnalytics,
        ...options
    });
};
const useEmployeeAnalytics = (filters, options)=>{
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>reportingQueryKeys.employeeAnalytics(filters), [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getEmployeeAnalytics(filters), [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 4 * 60 * 1000,
        gcTime: 9 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false,
        enabled: !!filters.includeEmployeeMetrics,
        ...options
    });
};
const useCrossEntityAnalytics = (filters, options)=>{
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>reportingQueryKeys.crossEntityAnalytics(filters), [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getCrossEntityAnalytics(filters), [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 6 * 60 * 1000,
        gcTime: 12 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false,
        enabled: !!(filters.includeCrossEntityCorrelations || filters.includeTaskData || filters.includeEmployeeMetrics || filters.includeVehicleAnalytics),
        ...options
    });
};
const useServiceCostSummary = (filters, options)=>{
    const queryKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>reportingQueryKeys.serviceCosts(filters), [
        filters
    ]);
    const queryFn = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportingDataService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDataService"].getServiceCostSummary(filters), [
        filters
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useQuery$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQuery"])({
        queryKey,
        queryFn,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 3,
        retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),
        refetchOnWindowFocus: false,
        enabled: !!filters.includeServiceHistory,
        ...options
    });
};
}}),
"[project]/src/components/features/reporting/data/hooks/useRealtimeReportingUpdates.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// frontend/src/components/features/reporting/data/hooks/useRealtimeReportingUpdates.ts
__turbopack_context__.s({
    "WebSocketConnectionState": (()=>WebSocketConnectionState),
    "useRealtimeReportingUpdates": (()=>useRealtimeReportingUpdates)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/config/environment.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
var WebSocketConnectionState = /*#__PURE__*/ function(WebSocketConnectionState) {
    WebSocketConnectionState["CONNECTING"] = "connecting";
    WebSocketConnectionState["CONNECTED"] = "connected";
    WebSocketConnectionState["DISCONNECTED"] = "disconnected";
    WebSocketConnectionState["RECONNECTING"] = "reconnecting";
    WebSocketConnectionState["ERROR"] = "error";
    return WebSocketConnectionState;
}({});
const useRealtimeReportingUpdates = (filters, config = {})=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const wsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const reconnectTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const heartbeatIntervalRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const updateBatchRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(new Set());
    const batchTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Configuration with defaults using environment-aware configuration
    const envConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$config$2f$environment$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getEnvironmentConfig"])();
    const { url = `${envConfig.wsUrl}/ws/reporting`, reconnectInterval = 3000, maxReconnectAttempts = 5, heartbeatInterval = 30000, batchUpdateDelay = 1000 } = config;
    // Connection state management
    const [connectionState, setConnectionState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("disconnected");
    const [reconnectAttempts, setReconnectAttempts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [lastError, setLastError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    /**
   * Batched cache invalidation to prevent excessive re-renders
   * Collects multiple updates and processes them together
   */ const processBatchedUpdates = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (updateBatchRef.current.size === 0) return;
        const updates = Array.from(updateBatchRef.current);
        updateBatchRef.current.clear();
        // Invalidate relevant queries based on batched updates
        const queryKeysToInvalidate = new Set();
        updates.forEach((updateType)=>{
            switch(updateType){
                case 'delegation-updated':
                    queryKeysToInvalidate.add('delegation-analytics');
                    queryKeysToInvalidate.add('trends');
                    queryKeysToInvalidate.add('location-metrics');
                    break;
                case 'task-updated':
                    queryKeysToInvalidate.add('task-metrics');
                    break;
                case 'analytics-refresh':
                    queryKeysToInvalidate.add('delegation-analytics');
                    queryKeysToInvalidate.add('trends');
                    queryKeysToInvalidate.add('location-metrics');
                    queryKeysToInvalidate.add('task-metrics');
                    break;
                case 'location-updated':
                    queryKeysToInvalidate.add('location-metrics');
                    break;
            }
        });
        // Invalidate queries efficiently
        queryKeysToInvalidate.forEach((queryType)=>{
            switch(queryType){
                case 'delegation-analytics':
                    queryClient.invalidateQueries({
                        queryKey: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingQueryKeys"].analytics()
                    });
                    break;
                case 'task-metrics':
                    queryClient.invalidateQueries({
                        queryKey: [
                            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingQueryKeys"].all,
                            'tasks',
                            'metrics'
                        ]
                    });
                    break;
                case 'trends':
                    queryClient.invalidateQueries({
                        queryKey: [
                            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingQueryKeys"].all,
                            'trends'
                        ]
                    });
                    break;
                case 'location-metrics':
                    queryClient.invalidateQueries({
                        queryKey: [
                            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingQueryKeys"].all,
                            'locations',
                            'metrics'
                        ]
                    });
                    break;
            }
        });
    }, [
        queryClient
    ]);
    /**
   * Handles incoming WebSocket messages with proper error handling
   */ const handleMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((event)=>{
        try {
            const message = JSON.parse(event.data);
            // Add to batch for processing
            updateBatchRef.current.add(message.type);
            // Clear existing batch timeout and set new one
            if (batchTimeoutRef.current) {
                clearTimeout(batchTimeoutRef.current);
            }
            batchTimeoutRef.current = setTimeout(()=>{
                processBatchedUpdates();
            }, batchUpdateDelay);
        } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
        }
    }, [
        processBatchedUpdates,
        batchUpdateDelay
    ]);
    /**
   * Establishes WebSocket connection with proper error handling
   */ const connect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (wsRef.current?.readyState === WebSocket.OPEN) {
            return; // Already connected
        }
        setConnectionState("connecting");
        setLastError(null);
        try {
            const ws = new WebSocket(url);
            wsRef.current = ws;
            ws.onopen = ()=>{
                setConnectionState("connected");
                setReconnectAttempts(0);
                // Send subscription message with current filters
                ws.send(JSON.stringify({
                    type: 'subscribe',
                    filters: filters,
                    timestamp: new Date().toISOString()
                }));
                // Start heartbeat
                heartbeatIntervalRef.current = setInterval(()=>{
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({
                            type: 'ping'
                        }));
                    }
                }, heartbeatInterval);
            };
            ws.onmessage = handleMessage;
            ws.onclose = (event)=>{
                setConnectionState("disconnected");
                // Clear heartbeat
                if (heartbeatIntervalRef.current) {
                    clearInterval(heartbeatIntervalRef.current);
                    heartbeatIntervalRef.current = null;
                }
                // Attempt reconnection if not a clean close
                if (!event.wasClean && reconnectAttempts < maxReconnectAttempts) {
                    setConnectionState("reconnecting");
                    setReconnectAttempts((prev)=>prev + 1);
                    reconnectTimeoutRef.current = setTimeout(()=>{
                        connect();
                    }, reconnectInterval);
                }
            };
            ws.onerror = (error)=>{
                console.error('WebSocket error:', error);
                setConnectionState("error");
                setLastError('WebSocket connection failed');
            };
        } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            setConnectionState("error");
            setLastError(error instanceof Error ? error.message : 'Unknown connection error');
        }
    }, [
        url,
        filters,
        handleMessage,
        reconnectAttempts,
        maxReconnectAttempts,
        reconnectInterval,
        heartbeatInterval
    ]);
    /**
   * Cleanly disconnects WebSocket connection
   */ const disconnect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        // Clear all timeouts
        if (reconnectTimeoutRef.current) {
            clearTimeout(reconnectTimeoutRef.current);
            reconnectTimeoutRef.current = null;
        }
        if (heartbeatIntervalRef.current) {
            clearInterval(heartbeatIntervalRef.current);
            heartbeatIntervalRef.current = null;
        }
        if (batchTimeoutRef.current) {
            clearTimeout(batchTimeoutRef.current);
            batchTimeoutRef.current = null;
        }
        // Close WebSocket connection
        if (wsRef.current) {
            wsRef.current.close(1000, 'Component unmounting');
            wsRef.current = null;
        }
        setConnectionState("disconnected");
        setReconnectAttempts(0);
        setLastError(null);
    }, []);
    /**
   * Manually trigger reconnection
   */ const reconnect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        disconnect();
        setReconnectAttempts(0);
        setTimeout(connect, 100); // Small delay to ensure clean disconnect
    }, [
        disconnect,
        connect
    ]);
    // Auto-connect on mount and filter changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        connect();
        return disconnect;
    }, [
        connect,
        disconnect
    ]);
    // Update subscription when filters change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({
                type: 'update-subscription',
                filters: filters,
                timestamp: new Date().toISOString()
            }));
        }
    }, [
        filters
    ]);
    return {
        connectionState,
        reconnectAttempts,
        lastError,
        connect,
        disconnect,
        reconnect,
        isConnected: connectionState === "connected",
        isConnecting: connectionState === "connecting",
        isReconnecting: connectionState === "reconnecting",
        hasError: connectionState === "error"
    };
};
}}),
"[project]/src/components/features/reporting/data/hooks/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Export specific hooks to avoid conflicts
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingData.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useDelegations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useDelegations.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useRealtimeReportingUpdates$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useRealtimeReportingUpdates.ts [app-ssr] (ecmascript)");
// Export unique hooks from useReportingQueries
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-ssr] (ecmascript)");
;
;
;
;
}}),
"[project]/src/components/features/reporting/data/hooks/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingData.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useDelegations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useDelegations.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useRealtimeReportingUpdates$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useRealtimeReportingUpdates.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-ssr] (ecmascript) <export useDelegationAnalytics as useDelegationAnalyticsQuery>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useDelegationAnalyticsQuery": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDelegationAnalytics"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-ssr] (ecmascript)");
}}),
"[project]/src/components/features/reporting/data/stores/useReportingFiltersStore.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// frontend/src/components/features/reporting/data/stores/useReportingFiltersStore.ts
__turbopack_context__.s({
    "useReportingFilters": (()=>useReportingFilters),
    "useReportingFiltersActions": (()=>useReportingFiltersActions),
    "useReportingFiltersPresets": (()=>useReportingFiltersPresets),
    "useReportingFiltersStore": (()=>useReportingFiltersStore),
    "useReportingFiltersUI": (()=>useReportingFiltersUI),
    "useReportingFiltersValidation": (()=>useReportingFiltersValidation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react/shallow.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
/**
 * Default filter values following business requirements
 * FIXED: Create stable default filters to prevent infinite re-renders
 * Use a function that creates fresh dates only when needed
 */ const getDefaultFilters = ()=>{
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    return {
        costRange: {
            max: 10_000,
            min: 0
        },
        dateRange: {
            from: thirtyDaysAgo,
            to: now
        },
        employees: [],
        includeServiceHistory: false,
        includeTaskData: false,
        locations: [],
        serviceStatus: [],
        // ENHANCED: Default service history values
        serviceTypes: [],
        status: [],
        vehicles: []
    };
};
/**
 * FIXED: Create stable default filters object to prevent infinite re-renders
 * Only create new dates when absolutely necessary
 */ const createStableDefaultFilters = ()=>{
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    return {
        costRange: {
            max: 10_000,
            min: 0
        },
        dateRange: {
            from: thirtyDaysAgo,
            to: now
        },
        employees: [],
        includeServiceHistory: false,
        includeTaskData: false,
        locations: [],
        serviceStatus: [],
        serviceTypes: [],
        status: [],
        vehicles: []
    };
};
// FIXED: Create stable default filters instance
const STABLE_DEFAULT_FILTERS = createStableDefaultFilters();
/**
 * Filter validation logic
 *
 * @param filters - Filters to validate
 * @returns Validation errors object
 */ const validateFiltersLogic = (filters)=>{
    const errors = {};
    // Date range validation
    if (filters.dateRange) {
        if (filters.dateRange.from > filters.dateRange.to) {
            errors.dateRange = 'Start date must be before end date';
        }
        const daysDiff = Math.abs(filters.dateRange.to.getTime() - filters.dateRange.from.getTime()) / (1000 * 60 * 60 * 24);
        if (daysDiff > 365) {
            errors.dateRange = 'Date range cannot exceed 365 days';
        }
    }
    // Status validation
    if (filters.status.length > 10) {
        errors.status = 'Too many statuses selected (maximum 10)';
    }
    // Location validation
    if (filters.locations.length > 50) {
        errors.locations = 'Too many locations selected (maximum 50)';
    }
    // Employee validation
    if (filters.employees.length > 100) {
        errors.employees = 'Too many employees selected (maximum 100)';
    }
    // Vehicle validation
    if (filters.vehicles.length > 100) {
        errors.vehicles = 'Too many vehicles selected (maximum 100)';
    }
    // ENHANCED: Service history validation
    if (filters.serviceTypes && filters.serviceTypes.length > 20) {
        errors.serviceTypes = 'Too many service types selected (maximum 20)';
    }
    if (filters.serviceStatus && filters.serviceStatus.length > 10) {
        errors.serviceStatus = 'Too many service statuses selected (maximum 10)';
    }
    if (filters.costRange) {
        if (filters.costRange.min < 0) {
            errors.costRange = 'Minimum cost cannot be negative';
        }
        if (filters.costRange.min >= filters.costRange.max) {
            errors.costRange = 'Minimum cost must be less than maximum cost';
        }
        if (filters.costRange.max > 1_000_000) {
            errors.costRange = 'Maximum cost cannot exceed $1,000,000';
        }
    }
    return errors;
};
const useReportingFiltersStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["devtools"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["subscribeWithSelector"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        applyFilters: ()=>{
            const { filters, isValid } = get();
            if (isValid) {
                set({
                    hasUnsavedChanges: false,
                    lastAppliedFilters: {
                        ...filters
                    }
                });
            }
        },
        // Preset management
        applyPreset: (presetName)=>{
            try {
                const stored = localStorage.getItem('reporting-filter-presets');
                const presets = stored ? JSON.parse(stored) : {};
                const preset = presets[presetName];
                if (preset) {
                    set((state)=>({
                            filters: {
                                ...preset
                            },
                            hasUnsavedChanges: true,
                            lastAppliedFilters: state.lastAppliedFilters
                        }));
                }
            } catch (error) {
                console.error('Failed to apply preset:', error);
            }
        },
        clearValidationErrors: ()=>{
            set({
                isValid: true,
                validationErrors: {}
            });
        },
        deletePreset: (name)=>{
            try {
                const stored = localStorage.getItem('reporting-filter-presets');
                const presets = stored ? JSON.parse(stored) : {};
                delete presets[name];
                localStorage.setItem('reporting-filter-presets', JSON.stringify(presets));
            } catch (error) {
                console.error('Failed to delete preset:', error);
            }
        },
        // Initial state
        filters: getDefaultFilters(),
        getPresets: ()=>{
            try {
                const stored = localStorage.getItem('reporting-filter-presets');
                return stored ? JSON.parse(stored) : {};
            } catch  {
                return {};
            }
        },
        hasUnsavedChanges: false,
        isFilterPanelOpen: false,
        isValid: true,
        lastAppliedFilters: getDefaultFilters(),
        resetFilters: ()=>{
            set({
                filters: getDefaultFilters(),
                hasUnsavedChanges: true,
                isValid: true,
                validationErrors: {}
            });
        },
        revertChanges: ()=>{
            const { lastAppliedFilters } = get();
            set({
                filters: {
                    ...lastAppliedFilters
                },
                hasUnsavedChanges: false,
                isValid: true,
                validationErrors: {}
            });
        },
        saveAsPreset: (name)=>{
            try {
                const { filters } = get();
                const stored = localStorage.getItem('reporting-filter-presets');
                const presets = stored ? JSON.parse(stored) : {};
                presets[name] = {
                    ...filters
                };
                localStorage.setItem('reporting-filter-presets', JSON.stringify(presets));
            } catch (error) {
                console.error('Failed to save preset:', error);
            }
        },
        setCostRange: (min, max)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    costRange: {
                        max,
                        min
                    }
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        // Filter value setters
        setDateRange: (from, to)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    dateRange: {
                        from,
                        to
                    }
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setEmployees: (employees)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    employees
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setFilterPanelOpen: (open)=>{
            set({
                isFilterPanelOpen: open
            });
        },
        // Bulk operations
        setFilters: (partialFilters)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    ...partialFilters
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setIncludeServiceHistory: (includeServiceHistory)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    includeServiceHistory
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setIncludeTaskData: (includeTaskData)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    includeTaskData
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setLocations: (locations)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    locations
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setServiceStatus: (serviceStatus)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    serviceStatus
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        // ENHANCED: Service history filter setters
        setServiceTypes: (serviceTypes)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    serviceTypes
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setStatus: (status)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    status
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setTaskPriorities: (taskPriorities)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    taskPriorities
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        // ENHANCED: Task filter setters
        setTaskStatus: (taskStatus)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    taskStatus
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        setVehicles: (vehicles)=>{
            set((state)=>{
                const newFilters = {
                    ...state.filters,
                    vehicles
                };
                const errors = validateFiltersLogic(newFilters);
                return {
                    filters: newFilters,
                    hasUnsavedChanges: true,
                    isValid: Object.keys(errors).length === 0,
                    validationErrors: errors
                };
            });
        },
        // UI state management
        toggleFilterPanel: ()=>{
            set((state)=>({
                    isFilterPanelOpen: !state.isFilterPanelOpen
                }));
        },
        // Validation
        validateFilters: ()=>{
            const { filters } = get();
            const errors = validateFiltersLogic(filters);
            const isValid = Object.keys(errors).length === 0;
            set({
                isValid,
                validationErrors: errors
            });
            return isValid;
        },
        validationErrors: {}
    }), {
    name: 'reporting-filters-storage',
    partialize: (state)=>({
            // Only persist essential filter state, not UI state
            filters: state.filters,
            lastAppliedFilters: state.lastAppliedFilters
        }),
    storage: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createJSONStorage"])(()=>localStorage)
})), {
    name: 'reporting-filters-store'
}));
/**
 * Selector hooks for optimized component subscriptions
 *
 * Follows DRY principle by providing reusable selectors
 * FIXED: Create stable selectors to prevent infinite re-renders
 */ // FIXED: Create stable selector functions to prevent infinite loops
const filtersSelector = (state)=>state.filters;
const actionsSelector = (state)=>({
        applyFilters: state.applyFilters,
        resetFilters: state.resetFilters,
        revertChanges: state.revertChanges,
        setCostRange: state.setCostRange,
        setDateRange: state.setDateRange,
        setEmployees: state.setEmployees,
        setFilters: state.setFilters,
        setIncludeServiceHistory: state.setIncludeServiceHistory,
        setIncludeTaskData: state.setIncludeTaskData,
        setLocations: state.setLocations,
        setServiceStatus: state.setServiceStatus,
        setServiceTypes: state.setServiceTypes,
        setStatus: state.setStatus,
        setTaskPriorities: state.setTaskPriorities,
        setTaskStatus: state.setTaskStatus,
        setVehicles: state.setVehicles
    });
const uiSelector = (state)=>({
        hasUnsavedChanges: state.hasUnsavedChanges,
        isFilterPanelOpen: state.isFilterPanelOpen,
        setFilterPanelOpen: state.setFilterPanelOpen,
        toggleFilterPanel: state.toggleFilterPanel
    });
const validationSelector = (state)=>({
        clearValidationErrors: state.clearValidationErrors,
        isValid: state.isValid,
        validateFilters: state.validateFilters,
        validationErrors: state.validationErrors
    });
const presetsSelector = (state)=>({
        applyPreset: state.applyPreset,
        deletePreset: state.deletePreset,
        getPresets: state.getPresets,
        saveAsPreset: state.saveAsPreset
    });
const useReportingFilters = ()=>useReportingFiltersStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useShallow"])(filtersSelector));
const useReportingFiltersActions = ()=>useReportingFiltersStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useShallow"])(actionsSelector));
const useReportingFiltersUI = ()=>useReportingFiltersStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useShallow"])(uiSelector));
const useReportingFiltersValidation = ()=>useReportingFiltersStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useShallow"])(validationSelector));
const useReportingFiltersPresets = ()=>useReportingFiltersStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useShallow"])(presetsSelector));
}}),
"[project]/src/components/features/reporting/data/stores/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$stores$2f$useReportingFiltersStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/stores/useReportingFiltersStore.ts [app-ssr] (ecmascript)");
;
}}),
"[project]/src/components/features/reporting/data/stores/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$stores$2f$useReportingFiltersStore$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/stores/useReportingFiltersStore.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$stores$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/stores/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-ssr] (ecmascript) <export useTrendData as useTrendDataQuery>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useTrendDataQuery": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTrendData"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-ssr] (ecmascript)");
}}),
"[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-ssr] (ecmascript) <export useLocationMetrics as useLocationMetricsQuery>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useLocationMetricsQuery": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLocationMetrics"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$hooks$2f$useReportingQueries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/hooks/useReportingQueries.ts [app-ssr] (ecmascript)");
}}),
"[project]/src/components/features/reporting/hooks/useReportGeneration.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file useReportGeneration.ts
 * @description Hook for managing data report generation
 */ __turbopack_context__.s({
    "useReportDownload": (()=>useReportDownload),
    "useReportGeneration": (()=>useReportGeneration),
    "useReportHistory": (()=>useReportHistory),
    "useReportTemplates": (()=>useReportTemplates)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/security/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/security/hooks/useSecureApiClient.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportGenerationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/data/services/ReportGenerationService.ts [app-ssr] (ecmascript)");
;
;
;
;
const useReportGeneration = ()=>{
    const [isGenerating, setIsGenerating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const { client } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecureApiClient"])();
    // Create API client adapter for the service
    const apiClient = {
        request: async (config)=>{
            const response = await client.request(config);
            return {
                data: response
            };
        }
    };
    // Create service instance with proper API client
    const service = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$data$2f$services$2f$ReportGenerationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createReportGenerationService"])(apiClient);
    /**
   * Generate comprehensive data report
   * Uses service layer following DIP principle
   */ const generateReport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (config)=>{
        setIsGenerating(true);
        setError(null);
        try {
            return await service.generateComprehensiveReport(config);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to generate report';
            setError(errorMessage);
            throw err;
        } finally{
            setIsGenerating(false);
        }
    }, [
        service
    ]);
    /**
   * Generate individual entity report
   * Uses service layer following DIP principle
   */ const generateIndividualReport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (config)=>{
        setIsGenerating(true);
        setError(null);
        try {
            return await service.generateIndividualReport(config);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to generate individual report';
            setError(errorMessage);
            throw err;
        } finally{
            setIsGenerating(false);
        }
    }, [
        service
    ]);
    /**
   * Generate aggregate entity report
   * Uses service layer following DIP principle
   */ const generateAggregateReport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (config)=>{
        setIsGenerating(true);
        setError(null);
        try {
            return await service.generateAggregateReport(config);
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to generate aggregate report';
            setError(errorMessage);
            throw err;
        } finally{
            setIsGenerating(false);
        }
    }, [
        service
    ]);
    /**
   * Export generated report data to PDF/Excel/CSV
   */ const exportReport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (reportData, format, entityType, reportTitle, filename)=>{
        try {
            const { useExport } = await __turbopack_context__.r("[project]/src/components/features/reporting/exports/hooks/useExport.tsx [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
            const { exportReportToPDF, exportReportToExcel, exportToCSV } = useExport(filename || 'report');
            switch(format){
                case 'pdf':
                    // Convert report data to PDF using entity-specific components
                    await exportReportToPDF(reportData, entityType, reportTitle || `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} Report`, filename);
                    break;
                case 'excel':
                    // Convert to Excel format with proper sheets
                    exportReportToExcel(reportData, entityType, filename);
                    break;
                case 'csv':
                    // Convert to CSV format (flatten data for CSV)
                    const csvData = Array.isArray(reportData.data) ? reportData.data : [
                        reportData.data || reportData
                    ];
                    exportToCSV(csvData, {
                        filename: filename || 'report'
                    });
                    break;
                default:
                    throw new Error(`Unsupported export format: ${format}`);
            }
        } catch (error) {
            console.error('Export failed:', error);
            throw error;
        }
    }, []);
    return {
        generateComprehensiveReport: generateReport,
        generateIndividualReport,
        generateAggregateReport,
        exportReport,
        isGenerating,
        error
    };
};
const useReportHistory = (filters)=>{
    const { client } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecureApiClient"])();
    const queryParams = new URLSearchParams();
    if (filters?.type) queryParams.append('type', filters.type);
    if (filters?.entityType) queryParams.append('entityType', filters.entityType);
    const historyQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'report-history',
        filters
    ], async ()=>{
        const response = await client.get(`/api/reporting/reports/history?${queryParams.toString()}`);
        return response;
    }, {
        cacheDuration: 2 * 60 * 1000,
        enableRetry: true
    });
    return {
        reports: historyQuery.data?.reports || [],
        pagination: historyQuery.data?.pagination,
        isLoading: historyQuery.isLoading,
        error: historyQuery.error,
        refetch: historyQuery.refetch
    };
};
const useReportDownload = ()=>{
    const [isDownloading, setIsDownloading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [downloadError, setDownloadError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const { client } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecureApiClient"])();
    const downloadReport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (reportId)=>{
        setIsDownloading(true);
        setDownloadError(null);
        try {
            const response = await client.get(`/api/reporting/reports/${reportId}/download`);
            // For now, just show the response since actual file download isn't implemented yet
            console.log('Download result:', response);
            // TODO: Implement actual file download when backend file storage is ready
            alert('Download functionality will be implemented with file storage');
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to download report';
            setDownloadError(errorMessage);
            throw err;
        } finally{
            setIsDownloading(false);
        }
    }, [
        client
    ]);
    return {
        downloadReport,
        isDownloading,
        downloadError
    };
};
const useReportTemplates = ()=>{
    const { client } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$security$2f$hooks$2f$useSecureApiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSecureApiClient"])();
    const templatesQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'report-templates'
    ], async ()=>{
        const response = await client.get('/api/reporting/reports/templates');
        // Ensure we return an array, handle different response structures
        const data = response;
        if (Array.isArray(data)) {
            return data;
        }
        // If response has nested data property (API wrapper format)
        if (data && Array.isArray(data.data)) {
            return data.data;
        }
        // Fallback to empty array if data is not in expected format
        console.warn('Report templates API returned unexpected format:', data);
        return [];
    }, {
        cacheDuration: 10 * 60 * 1000,
        enableRetry: true
    });
    return {
        templates: Array.isArray(templatesQuery.data) ? templatesQuery.data : [],
        isLoading: templatesQuery.isLoading,
        error: templatesQuery.error,
        refetch: templatesQuery.refetch
    };
};
}}),
"[project]/src/components/features/reporting/hooks/useReportTypes.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file useReportTypes.ts
 * @description Hook for managing report types following existing patterns
 */ __turbopack_context__.s({
    "useReportType": (()=>useReportType),
    "useReportTypes": (()=>useReportTypes),
    "useReportTypesByCategory": (()=>useReportTypesByCategory)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/useMutation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
;
;
;
const useReportTypes = ()=>{
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQueryClient"])();
    // Query for fetching all report types
    const reportTypesQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'report-types'
    ], async ()=>{
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get('/reporting/report-types');
        // Extract the data array from the API response structure
        return result.data?.data || [];
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
    // Mutation for creating a new report type
    const createReportType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async (reportTypeData)=>{
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].post('/reporting/report-types', reportTypeData);
            // Extract the data from the API response structure
            return result.data?.data || result.data;
        },
        onSuccess: ()=>{
            // Invalidate and refetch report types
            queryClient.invalidateQueries({
                queryKey: [
                    'report-types'
                ]
            });
        }
    });
    // Mutation for updating an existing report type
    const updateReportType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async ({ id, ...reportTypeData })=>{
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].put(`/reporting/report-types/${id}`, reportTypeData);
            // Extract the data from the API response structure
            return result.data?.data || result.data;
        },
        onSuccess: ()=>{
            // Invalidate and refetch report types
            queryClient.invalidateQueries({
                queryKey: [
                    'report-types'
                ]
            });
        }
    });
    // Mutation for deleting a report type
    const deleteReportType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async (id)=>{
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].delete(`/reporting/report-types/${id}`);
        },
        onSuccess: ()=>{
            // Invalidate and refetch report types
            queryClient.invalidateQueries({
                queryKey: [
                    'report-types'
                ]
            });
        }
    });
    // Mutation for duplicating a report type
    const duplicateReportType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async (id)=>{
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].post(`/reporting/report-types/${id}/duplicate`);
            // Extract the data from the API response structure
            return result.data?.data || result.data;
        },
        onSuccess: ()=>{
            // Invalidate and refetch report types
            queryClient.invalidateQueries({
                queryKey: [
                    'report-types'
                ]
            });
        }
    });
    // Mutation for toggling report type active status
    const toggleReportTypeActive = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$useMutation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMutation"])({
        mutationFn: async ({ id, isActive })=>{
            const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].patch(`/reporting/report-types/${id}/toggle-active`, {
                isActive
            });
            // Extract the data from the API response structure
            return result.data?.data || result.data;
        },
        onSuccess: ()=>{
            // Invalidate and refetch report types
            queryClient.invalidateQueries({
                queryKey: [
                    'report-types'
                ]
            });
        }
    });
    return {
        // Mutations
        createReportType,
        // Query data
        data: reportTypesQuery.data,
        deleteReportType,
        duplicateReportType,
        error: reportTypesQuery.error,
        isLoading: reportTypesQuery.isLoading,
        // Utility functions
        refetch: reportTypesQuery.refetch,
        toggleReportTypeActive,
        updateReportType
    };
};
const useReportType = (id)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'report-type',
        id
    ], async ()=>{
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/report-types/${id}`);
        // Extract the data from the API response structure
        return result.data?.data || result.data;
    }, {
        cacheDuration: 5 * 60 * 1000,
        enabled: !!id,
        enableRetry: true
    });
};
const useReportTypesByCategory = (category)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'report-types',
        'category',
        category
    ], async ()=>{
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(`/reporting/report-types?category=${encodeURIComponent(category)}`);
        // Extract the data array from the API response structure
        return result.data?.data || [];
    }, {
        cacheDuration: 5 * 60 * 1000,
        enabled: !!category,
        enableRetry: true
    });
};
}}),
"[project]/src/components/features/reporting/hooks/useVehicleAnalytics.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file useVehicleAnalytics.ts
 * @description Hook for fetching vehicle analytics data following existing patterns
 */ __turbopack_context__.s({
    "useVehicleAnalytics": (()=>useVehicleAnalytics),
    "useVehicleCostAnalytics": (()=>useVehicleCostAnalytics),
    "useVehicleMaintenanceSchedule": (()=>useVehicleMaintenanceSchedule),
    "useVehicleUtilization": (()=>useVehicleUtilization)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
;
;
const useVehicleAnalytics = (filters)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'vehicle-analytics',
        filters
    ], async ()=>{
        const queryParams = new URLSearchParams();
        if (filters?.dateRange) {
            // Defensive programming: Ensure dates are Date objects
            const fromDate = filters.dateRange.from instanceof Date ? filters.dateRange.from : new Date(filters.dateRange.from);
            const toDate = filters.dateRange.to instanceof Date ? filters.dateRange.to : new Date(filters.dateRange.to);
            queryParams.append('dateRange.from', fromDate.toISOString().split('T')[0] || fromDate.toISOString());
            queryParams.append('dateRange.to', toDate.toISOString().split('T')[0] || toDate.toISOString());
        }
        if (filters?.vehicles) {
            filters.vehicles.forEach((vehicle)=>queryParams.append('vehicles', vehicle.toString()));
        }
        if (filters?.serviceTypes) {
            filters.serviceTypes.forEach((type)=>queryParams.append('serviceTypes', type));
        }
        if (filters?.serviceStatus) {
            filters.serviceStatus.forEach((status)=>queryParams.append('serviceStatus', status));
        }
        const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
        return result.data || result;
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true,
        retryAttempts: 3
    });
};
const useVehicleUtilization = (filters)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'vehicle-utilization',
        filters
    ], async ()=>{
        // Get utilization data from vehicle analytics endpoint
        const queryParams = new URLSearchParams();
        if (filters?.dateRange) {
            queryParams.append('from', filters.dateRange.from.toISOString());
            queryParams.append('to', filters.dateRange.to.toISOString());
        }
        if (filters?.vehicles) {
            filters.vehicles.forEach((vehicle)=>queryParams.append('vehicles', vehicle.toString()));
        }
        const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
        const vehicleAnalytics = result.data || result;
        // Return the utilization metrics from vehicle analytics
        return vehicleAnalytics.utilizationMetrics || [];
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
const useVehicleMaintenanceSchedule = (filters)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'vehicle-maintenance-schedule',
        filters
    ], async ()=>{
        // Get maintenance data from vehicle analytics endpoint
        const queryParams = new URLSearchParams();
        if (filters?.dateRange) {
            queryParams.append('from', filters.dateRange.from.toISOString());
            queryParams.append('to', filters.dateRange.to.toISOString());
        }
        if (filters?.vehicles) {
            filters.vehicles.forEach((vehicle)=>queryParams.append('vehicles', vehicle.toString()));
        }
        const url = `/reporting/vehicles/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
        const vehicleAnalytics = result.data || result;
        // Return the maintenance schedule from vehicle analytics
        return vehicleAnalytics.maintenanceSchedule || [];
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
const useVehicleCostAnalytics = (filters)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'vehicle-cost-analytics',
        filters
    ], async ()=>{
        const queryParams = new URLSearchParams();
        if (filters?.dateRange) {
            queryParams.append('from', filters.dateRange.from.toISOString());
            queryParams.append('to', filters.dateRange.to.toISOString());
        }
        if (filters?.vehicles) {
            filters.vehicles.forEach((vehicle)=>queryParams.append('vehicles', vehicle.toString()));
        }
        if (filters?.serviceTypes) {
            filters.serviceTypes.forEach((type)=>queryParams.append('serviceTypes', type));
        }
        const url = `/reporting/vehicle-costs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
        return result.data || result;
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
}}),
"[project]/src/components/features/reporting/hooks/useEmployeeAnalytics.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file useEmployeeAnalytics.ts
 * @description Hook for fetching employee analytics data following existing patterns
 */ __turbopack_context__.s({
    "useEmployeeAnalytics": (()=>useEmployeeAnalytics),
    "useEmployeeAvailability": (()=>useEmployeeAvailability),
    "useEmployeePerformance": (()=>useEmployeePerformance),
    "useEmployeeTaskAssignments": (()=>useEmployeeTaskAssignments),
    "useEmployeeWorkload": (()=>useEmployeeWorkload)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
;
;
const useEmployeeAnalytics = (filters)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'employee-analytics',
        filters
    ], async ()=>{
        const queryParams = new URLSearchParams();
        if (filters?.dateRange) {
            // Defensive programming: Ensure dates are Date objects
            const fromDate = filters.dateRange.from instanceof Date ? filters.dateRange.from : new Date(filters.dateRange.from);
            const toDate = filters.dateRange.to instanceof Date ? filters.dateRange.to : new Date(filters.dateRange.to);
            queryParams.append('dateRange.from', fromDate.toISOString().split('T')[0] || fromDate.toISOString());
            queryParams.append('dateRange.to', toDate.toISOString().split('T')[0] || toDate.toISOString());
        }
        if (filters?.employees) {
            filters.employees.forEach((employee)=>queryParams.append('employees', employee.toString()));
        }
        if (filters?.locations) {
            filters.locations.forEach((location)=>queryParams.append('locations', location));
        }
        if (filters?.includeEmployeeMetrics) {
            queryParams.append('includeEmployeeMetrics', 'true');
        }
        const url = `/reporting/employees/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
        return result.data || result;
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true,
        retryAttempts: 3
    });
};
const useEmployeePerformance = (filters)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'employee-performance',
        filters
    ], async ()=>{
        const queryParams = new URLSearchParams();
        if (filters?.dateRange) {
            queryParams.append('from', filters.dateRange.from.toISOString());
            queryParams.append('to', filters.dateRange.to.toISOString());
        }
        if (filters?.employees) {
            filters.employees.forEach((employee)=>queryParams.append('employees', employee.toString()));
        }
        const url = `/api/reporting/employee-performance${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to fetch employee performance: ${response.statusText}`);
        }
        return response.json();
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
const useEmployeeWorkload = (filters)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'employee-workload',
        filters
    ], async ()=>{
        const queryParams = new URLSearchParams();
        if (filters?.dateRange) {
            queryParams.append('from', filters.dateRange.from.toISOString());
            queryParams.append('to', filters.dateRange.to.toISOString());
        }
        if (filters?.employees) {
            filters.employees.forEach((employee)=>queryParams.append('employees', employee.toString()));
        }
        const url = `/api/reporting/employee-workload${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to fetch employee workload: ${response.statusText}`);
        }
        return response.json();
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
const useEmployeeTaskAssignments = (filters)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'employee-task-assignments',
        filters
    ], async ()=>{
        const queryParams = new URLSearchParams();
        if (filters?.dateRange) {
            queryParams.append('from', filters.dateRange.from.toISOString());
            queryParams.append('to', filters.dateRange.to.toISOString());
        }
        if (filters?.employees) {
            filters.employees.forEach((employee)=>queryParams.append('employees', employee.toString()));
        }
        const url = `/api/reporting/employee-tasks${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to fetch employee task assignments: ${response.statusText}`);
        }
        return response.json();
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
const useEmployeeAvailability = (filters)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'employee-availability',
        filters
    ], async ()=>{
        const queryParams = new URLSearchParams();
        if (filters?.dateRange) {
            queryParams.append('from', filters.dateRange.from.toISOString());
            queryParams.append('to', filters.dateRange.to.toISOString());
        }
        if (filters?.employees) {
            filters.employees.forEach((employee)=>queryParams.append('employees', employee.toString()));
        }
        const url = `/api/reporting/employee-availability${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to fetch employee availability: ${response.statusText}`);
        }
        return response.json();
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
}}),
"[project]/src/components/features/reporting/hooks/useCrossEntityAnalytics.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file useCrossEntityAnalytics.ts
 * @description Hook for fetching cross-entity analytics data following existing patterns
 */ __turbopack_context__.s({
    "useCrossEntityAnalytics": (()=>useCrossEntityAnalytics),
    "useEmployeeVehicleCorrelations": (()=>useEmployeeVehicleCorrelations),
    "usePerformanceWorkloadCorrelations": (()=>usePerformanceWorkloadCorrelations),
    "useTaskDelegationCorrelations": (()=>useTaskDelegationCorrelations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/hooks/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/api/useApiQuery.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/api/index.ts [app-ssr] (ecmascript) <locals>");
;
;
const useCrossEntityAnalytics = (filters)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'cross-entity-analytics',
        filters
    ], async ()=>{
        const queryParams = new URLSearchParams();
        if (filters?.dateRange) {
            // Defensive programming: Ensure dates are Date objects
            const fromDate = filters.dateRange.from instanceof Date ? filters.dateRange.from : new Date(filters.dateRange.from);
            const toDate = filters.dateRange.to instanceof Date ? filters.dateRange.to : new Date(filters.dateRange.to);
            queryParams.append('dateRange.from', fromDate.toISOString().split('T')[0] || fromDate.toISOString());
            queryParams.append('dateRange.to', toDate.toISOString().split('T')[0] || toDate.toISOString());
        }
        if (filters?.employees) {
            filters.employees.forEach((employee)=>queryParams.append('employees', employee.toString()));
        }
        if (filters?.vehicles) {
            filters.vehicles.forEach((vehicle)=>queryParams.append('vehicles', vehicle.toString()));
        }
        if (filters?.locations) {
            filters.locations.forEach((location)=>queryParams.append('locations', location));
        }
        // Note: includeCorrelations doesn't exist in ReportingFilters type
        // if (filters?.includeCorrelations) {
        //   queryParams.append('includeCorrelations', 'true');
        // }
        const url = `/reporting/cross-entity/analytics${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
        return result.data || result;
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true,
        retryAttempts: 3
    });
};
const useEmployeeVehicleCorrelations = (filters)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'employee-vehicle-correlations',
        filters
    ], async ()=>{
        const queryParams = new URLSearchParams();
        if (filters?.dateRange) {
            queryParams.append('from', filters.dateRange.from.toISOString());
            queryParams.append('to', filters.dateRange.to.toISOString());
        }
        if (filters?.employees) {
            filters.employees.forEach((employee)=>queryParams.append('employees', employee.toString()));
        }
        if (filters?.vehicles) {
            filters.vehicles.forEach((vehicle)=>queryParams.append('vehicles', vehicle.toString()));
        }
        const url = `/reporting/employee-vehicle-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
        return result.data || result;
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
const useTaskDelegationCorrelations = (filters)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'task-delegation-correlations',
        filters
    ], async ()=>{
        const queryParams = new URLSearchParams();
        if (filters?.dateRange) {
            queryParams.append('from', filters.dateRange.from.toISOString());
            queryParams.append('to', filters.dateRange.to.toISOString());
        }
        const url = `/reporting/task-delegation-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
        return result.data || result;
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
const usePerformanceWorkloadCorrelations = (filters)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$api$2f$useApiQuery$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useApiQuery"])([
        'performance-workload-correlations',
        filters
    ], async ()=>{
        const queryParams = new URLSearchParams();
        if (filters?.dateRange) {
            queryParams.append('from', filters.dateRange.from.toISOString());
            queryParams.append('to', filters.dateRange.to.toISOString());
        }
        if (filters?.employees) {
            filters.employees.forEach((employee)=>queryParams.append('employees', employee.toString()));
        }
        const url = `/reporting/performance-workload-correlations${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["apiClient"].get(url);
        return result.data || result;
    }, {
        cacheDuration: 5 * 60 * 1000,
        enableRetry: true
    });
};
}}),
"[project]/src/components/features/reporting/components/DateRangePicker.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file DateRangePicker.tsx
 * @description Date range picker component for report filtering
 */ __turbopack_context__.s({
    "DateRangePicker": (()=>DateRangePicker)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$calendar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/calendar.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/popover.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/alert.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-ssr] (ecmascript) <export default as CalendarIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js [app-ssr] (ecmascript) <export default as AlertTriangle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-ssr] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.mjs [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/subDays.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subWeeks$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/subWeeks.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subMonths$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/subMonths.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfMonth$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/startOfMonth.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfMonth$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/endOfMonth.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfYear$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/startOfYear.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfYear$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/endOfYear.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$differenceInDays$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/differenceInDays.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isAfter$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/isAfter.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isBefore$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/isBefore.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/isValid.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/utils/index.ts [app-ssr] (ecmascript) <locals>");
;
;
;
;
;
;
;
;
;
;
/**
 * Predefined date range options with enhanced defaults
 */ const PRESET_RANGES = [
    {
        label: 'Today',
        getValue: ()=>({
                from: new Date(),
                to: new Date()
            })
    },
    {
        label: 'Yesterday',
        getValue: ()=>{
            const yesterday = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["subDays"])(new Date(), 1);
            return {
                from: yesterday,
                to: yesterday
            };
        }
    },
    {
        label: 'Last 3 days',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["subDays"])(new Date(), 2),
                to: new Date()
            })
    },
    {
        label: 'Last 7 days',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["subDays"])(new Date(), 6),
                to: new Date()
            })
    },
    {
        label: 'Last 2 weeks',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subWeeks$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["subWeeks"])(new Date(), 2),
                to: new Date()
            })
    },
    {
        label: 'Last 30 days',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["subDays"])(new Date(), 29),
                to: new Date()
            })
    },
    {
        label: 'This week',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subDays$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["subDays"])(new Date(), new Date().getDay()),
                to: new Date()
            })
    },
    {
        label: 'This month',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfMonth$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["startOfMonth"])(new Date()),
                to: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfMonth$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["endOfMonth"])(new Date())
            })
    },
    {
        label: 'Last month',
        getValue: ()=>{
            const lastMonth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subMonths$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["subMonths"])(new Date(), 1);
            return {
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfMonth$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["startOfMonth"])(lastMonth),
                to: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfMonth$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["endOfMonth"])(lastMonth)
            };
        }
    },
    {
        label: 'Last 3 months',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$subMonths$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["subMonths"])(new Date(), 3),
                to: new Date()
            })
    },
    {
        label: 'This year',
        getValue: ()=>({
                from: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$startOfYear$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["startOfYear"])(new Date()),
                to: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$endOfYear$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["endOfYear"])(new Date())
            })
    }
];
const DateRangePicker = ({ value, onChange, placeholder = 'Select date range', className, disabled = false, maxDays = 365, minDays = 1, maxDate = new Date(), minDate = new Date(2020, 0, 1), showValidation = true })=>{
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [tempRange, setTempRange] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(value || null);
    // Real-time validation with memoization for performance
    const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!tempRange) {
            return {
                isValid: true
            };
        }
        const { from, to } = tempRange;
        // Validate date objects
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(from) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isValid$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isValid"])(to)) {
            return {
                isValid: false,
                message: 'Invalid date selected',
                type: 'error'
            };
        }
        // Check date order
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isAfter$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isAfter"])(from, to)) {
            return {
                isValid: false,
                message: 'Start date must be before end date',
                type: 'error'
            };
        }
        // Check date range limits
        if (minDate && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isBefore$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isBefore"])(from, minDate)) {
            return {
                isValid: false,
                message: `Start date cannot be before ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(minDate, 'MMM dd, yyyy')}`,
                type: 'error'
            };
        }
        if (maxDate && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isAfter$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isAfter"])(to, maxDate)) {
            return {
                isValid: false,
                message: `End date cannot be after ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(maxDate, 'MMM dd, yyyy')}`,
                type: 'error'
            };
        }
        // Check range duration
        const daysDiff = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$differenceInDays$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["differenceInDays"])(to, from) + 1;
        if (daysDiff < minDays) {
            return {
                isValid: false,
                message: `Date range must be at least ${minDays} day${minDays > 1 ? 's' : ''}`,
                type: 'error'
            };
        }
        if (daysDiff > maxDays) {
            return {
                isValid: false,
                message: `Date range cannot exceed ${maxDays} days`,
                type: 'error'
            };
        }
        // Performance warning for large ranges
        if (daysDiff > 90) {
            return {
                isValid: true,
                message: `Large date range (${daysDiff} days) may affect performance`,
                type: 'warning'
            };
        }
        return {
            isValid: true,
            message: `${daysDiff} day${daysDiff > 1 ? 's' : ''} selected`,
            type: 'info'
        };
    }, [
        tempRange,
        maxDays,
        minDays,
        maxDate,
        minDate
    ]);
    // Update temp range when value changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setTempRange(value || null);
    }, [
        value
    ]);
    /**
   * Handle preset range selection with validation
   */ const handlePresetSelect = (preset)=>{
        const range = preset.getValue();
        setTempRange(range);
        // Only call onChange if validation passes
        const tempValidation = validateRange(range);
        if (tempValidation.isValid) {
            onChange?.(range);
            setIsOpen(false);
        }
    };
    /**
   * Validate a date range
   */ const validateRange = (range)=>{
        if (!range) return {
            isValid: true
        };
        const { from, to } = range;
        const daysDiff = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$differenceInDays$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["differenceInDays"])(to, from) + 1;
        if (daysDiff > maxDays) {
            return {
                isValid: false,
                message: `Date range cannot exceed ${maxDays} days`,
                type: 'error'
            };
        }
        return {
            isValid: true
        };
    };
    /**
   * Handle custom date selection with enhanced logic
   */ const handleDateSelect = (date)=>{
        if (!date || disabled) return;
        // Validate date is within allowed range
        if (minDate && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isBefore$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isBefore"])(date, minDate)) return;
        if (maxDate && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$isAfter$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isAfter"])(date, maxDate)) return;
        if (!tempRange || tempRange.from && tempRange.to) {
            // Start new range
            const newRange = {
                from: date,
                to: date
            };
            setTempRange(newRange);
        } else if (tempRange.from && !tempRange.to) {
            // Complete the range
            const newRange = {
                from: date < tempRange.from ? date : tempRange.from,
                to: date < tempRange.from ? tempRange.from : date
            };
            setTempRange(newRange);
            // Only call onChange if validation passes
            const tempValidation = validateRange(newRange);
            if (tempValidation.isValid) {
                onChange?.(newRange);
                setIsOpen(false);
            }
        }
    };
    /**
   * Handle range clear
   */ const handleClear = ()=>{
        setTempRange(null);
        onChange?.(null);
        setIsOpen(false);
    };
    /**
   * Format display text
   */ const getDisplayText = ()=>{
        if (!value) return placeholder;
        if (value.from.toDateString() === value.to.toDateString()) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(value.from, 'MMM dd, yyyy');
        }
        return `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(value.from, 'MMM dd, yyyy')} - ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(value.to, 'MMM dd, yyyy')}`;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('relative', className),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Popover"], {
            open: isOpen,
            onOpenChange: setIsOpen,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PopoverTrigger"], {
                    asChild: true,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        variant: "outline",
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('w-full justify-start text-left font-normal', !value && 'text-muted-foreground'),
                        disabled: disabled,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarIcon$3e$__["CalendarIcon"], {
                                className: "mr-2 h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                lineNumber: 363,
                                columnNumber: 13
                            }, this),
                            getDisplayText(),
                            value && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                variant: "secondary",
                                className: "ml-auto",
                                children: [
                                    Math.ceil((value.to.getTime() - value.from.getTime()) / (1000 * 60 * 60 * 24)) + 1,
                                    ' ',
                                    "days"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                lineNumber: 366,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                        lineNumber: 355,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                    lineNumber: 354,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$popover$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PopoverContent"], {
                    className: "w-auto max-w-4xl p-0",
                    align: "start",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "border-r p-4 space-y-2 min-w-[160px]",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "font-medium text-sm text-gray-900 mb-3",
                                        children: "Quick Select"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                        lineNumber: 380,
                                        columnNumber: 15
                                    }, this),
                                    PRESET_RANGES.map((preset)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "ghost",
                                            size: "sm",
                                            className: "w-full justify-start text-xs h-8 px-2",
                                            onClick: ()=>handlePresetSelect(preset),
                                            children: preset.label
                                        }, preset.label, false, {
                                            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                            lineNumber: 384,
                                            columnNumber: 17
                                        }, this)),
                                    value && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "border-t pt-2 mt-3",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                variant: "ghost",
                                                size: "sm",
                                                className: "w-full justify-start text-sm text-red-600 hover:text-red-700",
                                                onClick: handleClear,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                                        className: "mr-2 h-3 w-3"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                        lineNumber: 404,
                                                        columnNumber: 23
                                                    }, this),
                                                    "Clear"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                lineNumber: 398,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                            lineNumber: 397,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                lineNumber: 379,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-4 min-w-[600px]",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$calendar$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Calendar"], {
                                        mode: "range",
                                        selected: tempRange || undefined,
                                        onSelect: (range)=>{
                                            if (range && range.from) {
                                                const dateRange = {
                                                    from: range.from,
                                                    to: range.to || range.from
                                                };
                                                setTempRange(dateRange);
                                                // Auto-apply valid ranges
                                                const tempValidation = validateRange(dateRange);
                                                if (tempValidation.isValid && dateRange.from && dateRange.to) {
                                                    onChange?.(dateRange);
                                                    setIsOpen(false);
                                                }
                                            } else {
                                                setTempRange(null);
                                            }
                                        },
                                        numberOfMonths: 2,
                                        className: "rounded-md border-0",
                                        disabled: [
                                            // Disable dates outside the allowed range
                                            ...minDate ? [
                                                {
                                                    before: minDate
                                                }
                                            ] : [],
                                            ...maxDate ? [
                                                {
                                                    after: maxDate
                                                }
                                            ] : []
                                        ],
                                        showOutsideDays: true,
                                        fixedWeeks: true
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                        lineNumber: 414,
                                        columnNumber: 15
                                    }, this),
                                    tempRange && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-4 p-3 bg-gray-50 rounded-lg",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h5", {
                                                className: "font-medium text-sm text-gray-900 mb-2",
                                                children: "Selected Range"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                lineNumber: 451,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-1 text-sm text-gray-600",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            "From: ",
                                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(tempRange.from, 'MMM dd, yyyy')
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                        lineNumber: 455,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            "To: ",
                                                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(tempRange.to, 'MMM dd, yyyy')
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                        lineNumber: 456,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xs text-gray-500",
                                                        children: [
                                                            Math.ceil((tempRange.to.getTime() - tempRange.from.getTime()) / (1000 * 60 * 60 * 24)) + 1,
                                                            ' ',
                                                            "days"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                        lineNumber: 457,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                lineNumber: 454,
                                                columnNumber: 19
                                            }, this),
                                            showValidation && validation.message && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Alert"], {
                                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('mt-3 py-2 px-3', validation.type === 'error' && 'border-red-200 bg-red-50', validation.type === 'warning' && 'border-yellow-200 bg-yellow-50', validation.type === 'info' && 'border-blue-200 bg-blue-50'),
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        validation.type === 'error' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                                                            className: "h-3 w-3 text-red-600"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                            lineNumber: 481,
                                                            columnNumber: 27
                                                        }, this),
                                                        validation.type === 'warning' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$triangle$2d$alert$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertTriangle$3e$__["AlertTriangle"], {
                                                            className: "h-3 w-3 text-yellow-600"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                            lineNumber: 484,
                                                            columnNumber: 27
                                                        }, this),
                                                        validation.type === 'info' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                                                            className: "h-3 w-3 text-blue-600"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                            lineNumber: 487,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AlertDescription"], {
                                                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["cn"])('text-xs', validation.type === 'error' && 'text-red-700', validation.type === 'warning' && 'text-yellow-700', validation.type === 'info' && 'text-blue-700'),
                                                            children: validation.message
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                            lineNumber: 489,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                    lineNumber: 479,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                                lineNumber: 468,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                        lineNumber: 450,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                                lineNumber: 413,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                        lineNumber: 377,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
                    lineNumber: 376,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
            lineNumber: 353,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/features/reporting/components/DateRangePicker.tsx",
        lineNumber: 352,
        columnNumber: 5
    }, this);
};
}}),

};

//# sourceMappingURL=src_components_features_reporting_1cf674e1._.js.map