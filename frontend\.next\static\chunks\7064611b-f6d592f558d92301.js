"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1746],{26475:(e,t,r)=>{r.d(t,{A:()=>nS});var n,i,a,s,o,h,l,f,u,c,d,p="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},_=[],g=[],b="undefined"!=typeof Uint8Array?Uint8Array:Array,m=!1;function v(){m=!0;for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t=0,r=e.length;t<r;++t)_[t]=e[t],g[e.charCodeAt(t)]=t;g[45]=62,g[95]=63}function w(e){m||v();for(var t,r=e.length,n=r%3,i="",a=[],s=0,o=r-n;s<o;s+=16383)a.push(function(e,t,r){for(var n,i=[],a=t;a<r;a+=3)n=(e[a]<<16)+(e[a+1]<<8)+e[a+2],i.push(_[n>>18&63]+_[n>>12&63]+_[n>>6&63]+_[63&n]);return i.join("")}(e,s,s+16383>o?o:s+16383));return 1===n?(i+=_[(t=e[r-1])>>2],i+=_[t<<4&63],i+="=="):2===n&&(i+=_[(t=(e[r-2]<<8)+e[r-1])>>10],i+=_[t>>4&63],i+=_[t<<2&63],i+="="),a.push(i),a.join("")}function y(e,t,r,n,i){var a,s,o=8*i-n-1,h=(1<<o)-1,l=h>>1,f=-7,u=r?i-1:0,c=r?-1:1,d=e[t+u];for(u+=c,a=d&(1<<-f)-1,d>>=-f,f+=o;f>0;a=256*a+e[t+u],u+=c,f-=8);for(s=a&(1<<-f)-1,a>>=-f,f+=n;f>0;s=256*s+e[t+u],u+=c,f-=8);if(0===a)a=1-l;else{if(a===h)return s?NaN:1/0*(d?-1:1);s+=Math.pow(2,n),a-=l}return(d?-1:1)*s*Math.pow(2,a-n)}function E(e,t,r,n,i,a){var s,o,h,l=8*a-i-1,f=(1<<l)-1,u=f>>1,c=5960464477539062e-23*(23===i),d=n?0:a-1,p=n?1:-1,_=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(o=+!!isNaN(t),s=f):(s=Math.floor(Math.log(t)/Math.LN2),t*(h=Math.pow(2,-s))<1&&(s--,h*=2),s+u>=1?t+=c/h:t+=c*Math.pow(2,1-u),t*h>=2&&(s++,h/=2),s+u>=f?(o=0,s=f):s+u>=1?(o=(t*h-1)*Math.pow(2,i),s+=u):(o=t*Math.pow(2,u-1)*Math.pow(2,i),s=0));i>=8;e[r+d]=255&o,d+=p,o/=256,i-=8);for(s=s<<i|o,l+=i;l>0;e[r+d]=255&s,d+=p,s/=256,l-=8);e[r+d-p]|=128*_}var k={}.toString,R=Array.isArray||function(e){return"[object Array]"==k.call(e)};L.TYPED_ARRAY_SUPPORT=void 0===p.TYPED_ARRAY_SUPPORT||p.TYPED_ARRAY_SUPPORT;var S=x();function x(){return L.TYPED_ARRAY_SUPPORT?0x7fffffff:0x3fffffff}function A(e,t){if(x()<t)throw RangeError("Invalid typed array length");return L.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=L.prototype:(null===e&&(e=new L(t)),e.length=t),e}function L(e,t,r){if(!L.TYPED_ARRAY_SUPPORT&&!(this instanceof L))return new L(e,t,r);if("number"==typeof e){if("string"==typeof t)throw Error("If encoding is specified then the first argument must be a string");return z(this,e)}return T(this,e,t,r)}function T(e,t,r,n){if("number"==typeof t)throw TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw RangeError("'length' is out of bounds");return t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n),L.TYPED_ARRAY_SUPPORT?(e=t).__proto__=L.prototype:e=I(e,t),e}(e,t,r,n):"string"==typeof t?function(e,t,r){if(("string"!=typeof r||""===r)&&(r="utf8"),!L.isEncoding(r))throw TypeError('"encoding" must be a valid string encoding');var n=0|N(t,r),i=(e=A(e,n)).write(t,r);return i!==n&&(e=e.slice(0,i)),e}(e,t,r):function(e,t){if(M(t)){var r,n=0|D(t.length);return 0===(e=A(e,n)).length||t.copy(e,0,0,n),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t){return"number"!=typeof t.length||(r=t.length)!=r?A(e,0):I(e,t)}if("Buffer"===t.type&&R(t.data))return I(e,t.data)}throw TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function O(e){if("number"!=typeof e)throw TypeError('"size" argument must be a number');if(e<0)throw RangeError('"size" argument must not be negative')}function z(e,t){if(O(t),e=A(e,t<0?0:0|D(t)),!L.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function I(e,t){var r=t.length<0?0:0|D(t.length);e=A(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function D(e){if(e>=x())throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+x().toString(16)+" bytes");return 0|e}function M(e){return!!(null!=e&&e._isBuffer)}function N(e,t){if(M(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return V(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return J(e).length;default:if(n)return V(e).length;t=(""+t).toLowerCase(),n=!0}}function U(e,t,r){var n,i,a,s=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n,i=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>i)&&(r=i);for(var a="",s=t;s<r;++s){a+=(n=e[s])<16?"0"+n.toString(16):n.toString(16)}return a}(this,t,r);case"utf8":case"utf-8":return F(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}(this,t,r);case"base64":return n=this,i=t,a=r,0===i&&a===n.length?w(n):w(n.slice(i,a));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),i="",a=0;a<n.length;a+=2)i+=String.fromCharCode(n[a]+256*n[a+1]);return i}(this,t,r);default:if(s)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),s=!0}}function B(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function Z(e,t,r,n,i){if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),isNaN(r*=1)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(i)return -1;else r=e.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof t&&(t=L.from(t,n)),M(t))return 0===t.length?-1:P(e,t,r,n,i);if("number"==typeof t){if(t&=255,L.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return P(e,[t],r,n,i)}throw TypeError("val must be string, number or Buffer")}function P(e,t,r,n,i){var a,s=1,o=e.length,h=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;s=2,o/=2,h/=2,r/=2}function l(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(i){var f=-1;for(a=r;a<o;a++)if(l(e,a)===l(t,-1===f?0:a-f)){if(-1===f&&(f=a),a-f+1===h)return f*s}else -1!==f&&(a-=a-f),f=-1}else for(r+h>o&&(r=o-h),a=r;a>=0;a--){for(var u=!0,c=0;c<h;c++)if(l(e,a+c)!==l(t,c)){u=!1;break}if(u)return a}return -1}L.poolSize=8192,L._augment=function(e){return e.__proto__=L.prototype,e},L.from=function(e,t,r){return T(null,e,t,r)},L.TYPED_ARRAY_SUPPORT&&(L.prototype.__proto__=Uint8Array.prototype,L.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&L[Symbol.species]),L.alloc=function(e,t,r){return(O(e),e<=0)?A(null,e):void 0!==t?"string"==typeof r?A(null,e).fill(t,r):A(null,e).fill(t):A(null,e)},L.allocUnsafe=function(e){return z(null,e)},L.allocUnsafeSlow=function(e){return z(null,e)},L.isBuffer=Q,L.compare=function(e,t){if(!M(e)||!M(t))throw TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,a=Math.min(r,n);i<a;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:+(n<r)},L.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},L.concat=function(e,t){if(!R(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return L.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=L.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var a=e[r];if(!M(a))throw TypeError('"list" argument must be an Array of Buffers');a.copy(n,i),i+=a.length}return n},L.byteLength=N,L.prototype._isBuffer=!0,L.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)B(this,t,t+1);return this},L.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)B(this,t,t+3),B(this,t+1,t+2);return this},L.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)B(this,t,t+7),B(this,t+1,t+6),B(this,t+2,t+5),B(this,t+3,t+4);return this},L.prototype.toString=function(){var e=0|this.length;return 0===e?"":0==arguments.length?F(this,0,e):U.apply(this,arguments)},L.prototype.equals=function(e){if(!M(e))throw TypeError("Argument must be a Buffer");return this===e||0===L.compare(this,e)},L.prototype.inspect=function(){var e="";return this.length>0&&(e=this.toString("hex",0,50).match(/.{2}/g).join(" "),this.length>50&&(e+=" ... ")),"<Buffer "+e+">"},L.prototype.compare=function(e,t,r,n,i){if(!M(e))throw TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var a=i-n,s=r-t,o=Math.min(a,s),h=this.slice(n,i),l=e.slice(t,r),f=0;f<o;++f)if(h[f]!==l[f]){a=h[f],s=l[f];break}return a<s?-1:+(s<a)},L.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},L.prototype.indexOf=function(e,t,r){return Z(this,e,t,r,!0)},L.prototype.lastIndexOf=function(e,t,r){return Z(this,e,t,r,!1)};function F(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var a,s,o,h,l=e[i],f=null,u=l>239?4:l>223?3:l>191?2:1;if(i+u<=r)switch(u){case 1:l<128&&(f=l);break;case 2:(192&(a=e[i+1]))==128&&(h=(31&l)<<6|63&a)>127&&(f=h);break;case 3:a=e[i+1],s=e[i+2],(192&a)==128&&(192&s)==128&&(h=(15&l)<<12|(63&a)<<6|63&s)>2047&&(h<55296||h>57343)&&(f=h);break;case 4:a=e[i+1],s=e[i+2],o=e[i+3],(192&a)==128&&(192&s)==128&&(192&o)==128&&(h=(15&l)<<18|(63&a)<<12|(63&s)<<6|63&o)>65535&&h<1114112&&(f=h)}null===f?(f=65533,u=1):f>65535&&(f-=65536,n.push(f>>>10&1023|55296),f=56320|1023&f),n.push(f),i+=u}var c=n,d=c.length;if(d<=4096)return String.fromCharCode.apply(String,c);for(var p="",_=0;_<d;)p+=String.fromCharCode.apply(String,c.slice(_,_+=4096));return p}function C(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function j(e,t,r,n,i,a){if(!M(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<a)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function Y(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,a=Math.min(e.length-r,2);i<a;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>(n?i:1-i)*8}function W(e,t,r,n){t<0&&(t=0xffffffff+t+1);for(var i=0,a=Math.min(e.length-r,4);i<a;++i)e[r+i]=t>>>(n?i:3-i)*8&255}function q(e,t,r,n,i,a){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function H(e,t,r,n,i){return i||q(e,t,r,4),E(e,t,r,n,23,4),r+4}function G(e,t,r,n,i){return i||q(e,t,r,8),E(e,t,r,n,52,8),r+8}L.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,a,s,o,h,l,f,u,c=this.length-t;if((void 0===r||r>c)&&(r=c),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var d=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var a=t.length;if(a%2!=0)throw TypeError("Invalid hex string");n>a/2&&(n=a/2);for(var s=0;s<n;++s){var o=parseInt(t.substr(2*s,2),16);if(isNaN(o))break;e[r+s]=o}return s}(this,e,t,r);case"utf8":case"utf-8":return i=t,a=r,$(V(e,this.length-i),this,i,a);case"ascii":return s=t,o=r,$(X(e),this,s,o);case"latin1":case"binary":return function(e,t,r,n){return $(X(t),e,r,n)}(this,e,t,r);case"base64":return h=t,l=r,$(J(e),this,h,l);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return f=t,u=r,$(function(e,t){for(var r,n,i=[],a=0;a<e.length&&!((t-=2)<0);++a)n=(r=e.charCodeAt(a))>>8,i.push(r%256),i.push(n);return i}(e,this.length-f),this,f,u);default:if(d)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),d=!0}},L.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},L.prototype.slice=function(e,t){var r,n=this.length;if(e=~~e,t=void 0===t?n:~~t,e<0?(e+=n)<0&&(e=0):e>n&&(e=n),t<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),L.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=L.prototype;else{var i=t-e;r=new L(i,void 0);for(var a=0;a<i;++a)r[a]=this[a+e]}return r},L.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||C(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n},L.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||C(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},L.prototype.readUInt8=function(e,t){return t||C(e,1,this.length),this[e]},L.prototype.readUInt16LE=function(e,t){return t||C(e,2,this.length),this[e]|this[e+1]<<8},L.prototype.readUInt16BE=function(e,t){return t||C(e,2,this.length),this[e]<<8|this[e+1]},L.prototype.readUInt32LE=function(e,t){return t||C(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},L.prototype.readUInt32BE=function(e,t){return t||C(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},L.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||C(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},L.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||C(e,t,this.length);for(var n=t,i=1,a=this[e+--n];n>0&&(i*=256);)a+=this[e+--n]*i;return a>=(i*=128)&&(a-=Math.pow(2,8*t)),a},L.prototype.readInt8=function(e,t){return(t||C(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},L.prototype.readInt16LE=function(e,t){t||C(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},L.prototype.readInt16BE=function(e,t){t||C(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},L.prototype.readInt32LE=function(e,t){return t||C(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},L.prototype.readInt32BE=function(e,t){return t||C(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},L.prototype.readFloatLE=function(e,t){return t||C(e,4,this.length),y(this,e,!0,23,4)},L.prototype.readFloatBE=function(e,t){return t||C(e,4,this.length),y(this,e,!1,23,4)},L.prototype.readDoubleLE=function(e,t){return t||C(e,8,this.length),y(this,e,!0,52,8)},L.prototype.readDoubleBE=function(e,t){return t||C(e,8,this.length),y(this,e,!1,52,8)},L.prototype.writeUIntLE=function(e,t,r,n){if(e*=1,t|=0,r|=0,!n){var i=Math.pow(2,8*r)-1;j(this,e,t,r,i,0)}var a=1,s=0;for(this[t]=255&e;++s<r&&(a*=256);)this[t+s]=e/a&255;return t+r},L.prototype.writeUIntBE=function(e,t,r,n){if(e*=1,t|=0,r|=0,!n){var i=Math.pow(2,8*r)-1;j(this,e,t,r,i,0)}var a=r-1,s=1;for(this[t+a]=255&e;--a>=0&&(s*=256);)this[t+a]=e/s&255;return t+r},L.prototype.writeUInt8=function(e,t,r){return e*=1,t|=0,r||j(this,e,t,1,255,0),L.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},L.prototype.writeUInt16LE=function(e,t,r){return e*=1,t|=0,r||j(this,e,t,2,65535,0),L.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):Y(this,e,t,!0),t+2},L.prototype.writeUInt16BE=function(e,t,r){return e*=1,t|=0,r||j(this,e,t,2,65535,0),L.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):Y(this,e,t,!1),t+2},L.prototype.writeUInt32LE=function(e,t,r){return e*=1,t|=0,r||j(this,e,t,4,0xffffffff,0),L.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):W(this,e,t,!0),t+4},L.prototype.writeUInt32BE=function(e,t,r){return e*=1,t|=0,r||j(this,e,t,4,0xffffffff,0),L.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):W(this,e,t,!1),t+4},L.prototype.writeIntLE=function(e,t,r,n){if(e*=1,t|=0,!n){var i=Math.pow(2,8*r-1);j(this,e,t,r,i-1,-i)}var a=0,s=1,o=0;for(this[t]=255&e;++a<r&&(s*=256);)e<0&&0===o&&0!==this[t+a-1]&&(o=1),this[t+a]=(e/s|0)-o&255;return t+r},L.prototype.writeIntBE=function(e,t,r,n){if(e*=1,t|=0,!n){var i=Math.pow(2,8*r-1);j(this,e,t,r,i-1,-i)}var a=r-1,s=1,o=0;for(this[t+a]=255&e;--a>=0&&(s*=256);)e<0&&0===o&&0!==this[t+a+1]&&(o=1),this[t+a]=(e/s|0)-o&255;return t+r},L.prototype.writeInt8=function(e,t,r){return e*=1,t|=0,r||j(this,e,t,1,127,-128),L.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},L.prototype.writeInt16LE=function(e,t,r){return e*=1,t|=0,r||j(this,e,t,2,32767,-32768),L.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):Y(this,e,t,!0),t+2},L.prototype.writeInt16BE=function(e,t,r){return e*=1,t|=0,r||j(this,e,t,2,32767,-32768),L.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):Y(this,e,t,!1),t+2},L.prototype.writeInt32LE=function(e,t,r){return e*=1,t|=0,r||j(this,e,t,4,0x7fffffff,-0x80000000),L.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):W(this,e,t,!0),t+4},L.prototype.writeInt32BE=function(e,t,r){return e*=1,t|=0,r||j(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),L.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):W(this,e,t,!1),t+4},L.prototype.writeFloatLE=function(e,t,r){return H(this,e,t,!0,r)},L.prototype.writeFloatBE=function(e,t,r){return H(this,e,t,!1,r)},L.prototype.writeDoubleLE=function(e,t,r){return G(this,e,t,!0,r)},L.prototype.writeDoubleBE=function(e,t,r){return G(this,e,t,!1,r)},L.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("sourceStart out of bounds");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,a=n-r;if(this===e&&r<t&&t<n)for(i=a-1;i>=0;--i)e[i+t]=this[i+r];else if(a<1e3||!L.TYPED_ARRAY_SUPPORT)for(i=0;i<a;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+a),t);return a},L.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===e.length){var i,a=e.charCodeAt(0);a<256&&(e=a)}if(void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!L.isEncoding(n))throw TypeError("Unknown encoding: "+n)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var s=M(e)?e:V(new L(e,n).toString()),o=s.length;for(i=0;i<r-t;++i)this[i+t]=s[i%o]}return this};var K=/[^+\/0-9A-Za-z-_]/g;function V(e,t){t=t||1/0;for(var r,n=e.length,i=null,a=[],s=0;s<n;++s){if((r=e.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319||s+1===n){(t-=3)>-1&&a.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&a.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&a.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;a.push(r)}else if(r<2048){if((t-=2)<0)break;a.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;a.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return a}function X(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function J(e){return function(e){m||v();var t,r,n,i,a,s,o=e.length;if(o%4>0)throw Error("Invalid string. Length must be a multiple of 4");a="="===e[o-2]?2:+("="===e[o-1]),s=new b(3*o/4-a),n=a>0?o-4:o;var h=0;for(t=0,r=0;t<n;t+=4,r+=3)i=g[e.charCodeAt(t)]<<18|g[e.charCodeAt(t+1)]<<12|g[e.charCodeAt(t+2)]<<6|g[e.charCodeAt(t+3)],s[h++]=i>>16&255,s[h++]=i>>8&255,s[h++]=255&i;return 2===a?(i=g[e.charCodeAt(t)]<<2|g[e.charCodeAt(t+1)]>>4,s[h++]=255&i):1===a&&(i=g[e.charCodeAt(t)]<<10|g[e.charCodeAt(t+1)]<<4|g[e.charCodeAt(t+2)]>>2,s[h++]=i>>8&255,s[h++]=255&i),s}(function(e){var t;if((e=((t=e).trim?t.trim():t.replace(/^\s+|\s+$/g,"")).replace(K,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function $(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}function Q(e){var t;return null!=e&&(!!e._isBuffer||ee(e)||"function"==typeof(t=e).readFloatLE&&"function"==typeof t.slice&&ee(t.slice(0,0)))}function ee(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}var et=Object.freeze({__proto__:null,Buffer:L,INSPECT_MAX_BYTES:50,SlowBuffer:function(e){return+e!=e&&(e=0),L.alloc(+e)},isBuffer:Q,kMaxLength:S});function er(){throw Error("setTimeout has not been defined")}function en(){throw Error("clearTimeout has not been defined")}var ei=er,ea=en;function es(e){if(ei===setTimeout)return setTimeout(e,0);if((ei===er||!ei)&&setTimeout)return ei=setTimeout,setTimeout(e,0);try{return ei(e,0)}catch(t){try{return ei.call(null,e,0)}catch(t){return ei.call(this,e,0)}}}"function"==typeof p.setTimeout&&(ei=setTimeout),"function"==typeof p.clearTimeout&&(ea=clearTimeout);var eo=[],eh=!1,el=-1;function ef(){eh&&n&&(eh=!1,n.length?eo=n.concat(eo):el=-1,eo.length&&eu())}function eu(){if(!eh){var e=es(ef);eh=!0;for(var t=eo.length;t;){for(n=eo,eo=[];++el<t;)n&&n[el].run();el=-1,t=eo.length}n=null,eh=!1,function(e){if(ea===clearTimeout)return clearTimeout(e);if((ea===en||!ea)&&clearTimeout)return ea=clearTimeout,clearTimeout(e);try{ea(e)}catch(t){try{return ea.call(null,e)}catch(t){return ea.call(this,e)}}}(e)}}function ec(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];eo.push(new ed(e,t)),1!==eo.length||eh||es(eu)}function ed(e,t){this.fun=e,this.array=t}function ep(){}ed.prototype.run=function(){this.fun.apply(null,this.array)};var e_=p.performance||{},eg=e_.now||e_.mozNow||e_.msNow||e_.oNow||e_.webkitNow||function(){return new Date().getTime()},eb=new Date,em={nextTick:ec,title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:ep,addListener:ep,once:ep,off:ep,removeListener:ep,removeAllListeners:ep,emit:ep,binding:function(e){throw Error("process.binding is not supported")},cwd:function(){return"/"},chdir:function(e){throw Error("process.chdir is not supported")},umask:function(){return 0},hrtime:function(e){var t=.001*eg.call(e_),r=Math.floor(t),n=Math.floor(t%1*1e9);return e&&(r-=e[0],(n-=e[1])<0&&(r--,n+=1e9)),[r,n]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-eb)/1e3}};function ev(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var r=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}),r}var ew={},ey=ev(et);function eE(){}function ek(){ek.init.call(this)}function eR(e){return void 0===e._maxListeners?ek.defaultMaxListeners:e._maxListeners}function eS(e,t,r,n){if("function"!=typeof r)throw TypeError('"listener" argument must be a function');if((s=e._events)?(s.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),s=e._events),o=s[t]):(s=e._events=new eE,e._eventsCount=0),o){if("function"==typeof o?o=s[t]=n?[r,o]:[o,r]:n?o.unshift(r):o.push(r),!o.warned&&(a=eR(e))&&a>0&&o.length>a){o.warned=!0;var i,a,s,o,h=Error("Possible EventEmitter memory leak detected. "+o.length+" "+t+" listeners added. Use emitter.setMaxListeners() to increase limit");h.name="MaxListenersExceededWarning",h.emitter=e,h.type=t,h.count=o.length,i=h,"function"==typeof console.warn?console.warn(i):console.log(i)}}else o=s[t]=r,++e._eventsCount;return e}function ex(e,t,r){var n=!1;function i(){e.removeListener(t,i),n||(n=!0,r.apply(e,arguments))}return i.listener=r,i}function eA(e){var t=this._events;if(t){var r=t[e];if("function"==typeof r)return 1;if(r)return r.length}return 0}function eL(e,t){for(var r=Array(t);t--;)r[t]=e[t];return r}eE.prototype=Object.create(null),ek.EventEmitter=ek,ek.usingDomains=!1,ek.prototype.domain=void 0,ek.prototype._events=void 0,ek.prototype._maxListeners=void 0,ek.defaultMaxListeners=10,ek.init=function(){this.domain=null,ek.usingDomains&&i.active,this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=new eE,this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},ek.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||isNaN(e))throw TypeError('"n" argument must be a positive number');return this._maxListeners=e,this},ek.prototype.getMaxListeners=function(){return eR(this)},ek.prototype.emit=function(e){var t,r,n,i,a,s,o,h="error"===e;if(s=this._events)h=h&&null==s.error;else if(!h)return!1;if(o=this.domain,h){if(t=arguments[1],o)t||(t=Error('Uncaught, unspecified "error" event')),t.domainEmitter=this,t.domain=o,t.domainThrown=!1,o.emit("error",t);else if(t instanceof Error)throw t;else{var l=Error('Uncaught, unspecified "error" event. ('+t+")");throw l.context=t,l}return!1}if(!(r=s[e]))return!1;var f="function"==typeof r;switch(n=arguments.length){case 1:!function(e,t,r){if(t)e.call(r);else for(var n=e.length,i=eL(e,n),a=0;a<n;++a)i[a].call(r)}(r,f,this);break;case 2:!function(e,t,r,n){if(t)e.call(r,n);else for(var i=e.length,a=eL(e,i),s=0;s<i;++s)a[s].call(r,n)}(r,f,this,arguments[1]);break;case 3:!function(e,t,r,n,i){if(t)e.call(r,n,i);else for(var a=e.length,s=eL(e,a),o=0;o<a;++o)s[o].call(r,n,i)}(r,f,this,arguments[1],arguments[2]);break;case 4:!function(e,t,r,n,i,a){if(t)e.call(r,n,i,a);else for(var s=e.length,o=eL(e,s),h=0;h<s;++h)o[h].call(r,n,i,a)}(r,f,this,arguments[1],arguments[2],arguments[3]);break;default:for(a=1,i=Array(n-1);a<n;a++)i[a-1]=arguments[a];!function(e,t,r,n){if(t)e.apply(r,n);else for(var i=e.length,a=eL(e,i),s=0;s<i;++s)a[s].apply(r,n)}(r,f,this,i)}return!0},ek.prototype.addListener=function(e,t){return eS(this,e,t,!1)},ek.prototype.on=ek.prototype.addListener,ek.prototype.prependListener=function(e,t){return eS(this,e,t,!0)},ek.prototype.once=function(e,t){if("function"!=typeof t)throw TypeError('"listener" argument must be a function');return this.on(e,ex(this,e,t)),this},ek.prototype.prependOnceListener=function(e,t){if("function"!=typeof t)throw TypeError('"listener" argument must be a function');return this.prependListener(e,ex(this,e,t)),this},ek.prototype.removeListener=function(e,t){var r,n,i,a,s;if("function"!=typeof t)throw TypeError('"listener" argument must be a function');if(!(n=this._events)||!(r=n[e]))return this;if(r===t||r.listener&&r.listener===t)0==--this._eventsCount?this._events=new eE:(delete n[e],n.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(i=-1,a=r.length;a-- >0;)if(r[a]===t||r[a].listener&&r[a].listener===t){s=r[a].listener,i=a;break}if(i<0)return this;if(1===r.length){if(r[0]=void 0,0==--this._eventsCount)return this._events=new eE,this;delete n[e]}else{for(var o=r,h=i,l=h,f=l+1,u=o.length;f<u;l+=1,f+=1)o[l]=o[f];o.pop()}n.removeListener&&this.emit("removeListener",e,s||t)}return this},ek.prototype.off=function(e,t){return this.removeListener(e,t)},ek.prototype.removeAllListeners=function(e){if(!(r=this._events))return this;if(!r.removeListener)return 0==arguments.length?(this._events=new eE,this._eventsCount=0):r[e]&&(0==--this._eventsCount?this._events=new eE:delete r[e]),this;if(0==arguments.length){for(var t,r,n,i=Object.keys(r),a=0;a<i.length;++a)"removeListener"!==(n=i[a])&&this.removeAllListeners(n);return this.removeAllListeners("removeListener"),this._events=new eE,this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(t)do this.removeListener(e,t[t.length-1]);while(t[0]);return this},ek.prototype.listeners=function(e){var t,r,n=this._events;return n&&(t=n[e])?"function"==typeof t?[t.listener||t]:function(e){for(var t=Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(t):[]},ek.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):eA.call(e,t)},ek.prototype.listenerCount=eA,ek.prototype.eventNames=function(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]};var eT="function"==typeof Object.create?function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e},eO=Object.getOwnPropertyDescriptors||function(e){for(var t=Object.keys(e),r={},n=0;n<t.length;n++)r[t[n]]=Object.getOwnPropertyDescriptor(e,t[n]);return r},ez=/%[sdj%]/g;function eI(e){if(!eG(e)){for(var t=[],r=0;r<arguments.length;r++)t.push(eU(arguments[r]));return t.join(" ")}for(var r=1,n=arguments,i=n.length,a=String(e).replace(ez,function(e){if("%%"===e)return"%";if(r>=i)return e;switch(e){case"%s":return String(n[r++]);case"%d":return Number(n[r++]);case"%j":try{return JSON.stringify(n[r++])}catch(e){return"[Circular]"}default:return e}}),s=n[r];r<i;s=n[++r])eW(s)||!eJ(s)?a+=" "+s:a+=" "+eU(s);return a}function eD(e,t){if(eV(p.process))return function(){return eD(e,t).apply(this,arguments)};if(!0===em.noDeprecation)return e;var r=!1;return function(){if(!r){if(em.throwDeprecation)throw Error(t);em.traceDeprecation?console.trace(t):console.error(t),r=!0}return e.apply(this,arguments)}}var eM={};function eN(e){return eV(s)&&(s=em.env.NODE_DEBUG||""),eM[e=e.toUpperCase()]||(RegExp("\\b"+e+"\\b","i").test(s)?eM[e]=function(){var t=eI.apply(null,arguments);console.error("%s %d: %s",e,0,t)}:eM[e]=function(){}),eM[e]}function eU(e,t){var r={seen:[],stylize:eZ};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),eY(t)?r.showHidden=t:t&&e4(r,t),eV(r.showHidden)&&(r.showHidden=!1),eV(r.depth)&&(r.depth=2),eV(r.colors)&&(r.colors=!1),eV(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=eB),eP(r,e,r.depth)}function eB(e,t){var r=eU.styles[t];return r?"\x1b["+eU.colors[r][0]+"m"+e+"\x1b["+eU.colors[r][1]+"m":e}function eZ(e,t){return e}function eP(e,t,r){if(e.customInspect&&t&&e0(t.inspect)&&t.inspect!==eU&&!(t.constructor&&t.constructor.prototype===t)){var n,i,a,s,o,h=t.inspect(r,e);return eG(h)||(h=eP(e,h,r)),h}var l=function(e,t){if(eV(t))return e.stylize("undefined","undefined");if(eG(t)){var r="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(r,"string")}return eH(t)?e.stylize(""+t,"number"):eY(t)?e.stylize(""+t,"boolean"):eW(t)?e.stylize("null","null"):void 0}(e,t);if(l)return l;var f=Object.keys(t),u=(s={},f.forEach(function(e,t){s[e]=!0}),s);if(e.showHidden&&(f=Object.getOwnPropertyNames(t)),eQ(t)&&(f.indexOf("message")>=0||f.indexOf("description")>=0))return eF(t);if(0===f.length){if(e0(t)){var c=t.name?": "+t.name:"";return e.stylize("[Function"+c+"]","special")}if(eX(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(e$(t))return e.stylize(Date.prototype.toString.call(t),"date");if(eQ(t))return eF(t)}var d="",p=!1,_=["{","}"];if(ej(t)&&(p=!0,_=["[","]"]),e0(t)&&(d=" [Function"+(t.name?": "+t.name:"")+"]"),eX(t)&&(d=" "+RegExp.prototype.toString.call(t)),e$(t)&&(d=" "+Date.prototype.toUTCString.call(t)),eQ(t)&&(d=" "+eF(t)),0===f.length&&(!p||0==t.length))return _[0]+d+_[1];if(r<0)if(eX(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");else return e.stylize("[Object]","special");return e.seen.push(t),o=p?function(e,t,r,n,i){for(var a=[],s=0,o=t.length;s<o;++s)e9(t,String(s))?a.push(eC(e,t,r,n,String(s),!0)):a.push("");return i.forEach(function(i){i.match(/^\d+$/)||a.push(eC(e,t,r,n,i,!0))}),a}(e,t,r,u,f):f.map(function(n){return eC(e,t,r,u,n,p)}),e.seen.pop(),n=o,i=d,a=_,n.reduce(function(e,t){return t.indexOf("\n"),e+t.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60?a[0]+(""===i?"":i+"\n ")+" "+n.join(",\n  ")+" "+a[1]:a[0]+i+" "+n.join(", ")+" "+a[1]}function eF(e){return"["+Error.prototype.toString.call(e)+"]"}function eC(e,t,r,n,i,a){var s,o,h;if((h=Object.getOwnPropertyDescriptor(t,i)||{value:t[i]}).get?o=h.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):h.set&&(o=e.stylize("[Setter]","special")),e9(n,i)||(s="["+i+"]"),!o&&(0>e.seen.indexOf(h.value)?(o=eW(r)?eP(e,h.value,null):eP(e,h.value,r-1)).indexOf("\n")>-1&&(o=a?o.split("\n").map(function(e){return"  "+e}).join("\n").substr(2):"\n"+o.split("\n").map(function(e){return"   "+e}).join("\n")):o=e.stylize("[Circular]","special")),eV(s)){if(a&&i.match(/^\d+$/))return o;(s=JSON.stringify(""+i)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(s=s.substr(1,s.length-2),s=e.stylize(s,"name")):(s=s.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),s=e.stylize(s,"string"))}return s+": "+o}function ej(e){return Array.isArray(e)}function eY(e){return"boolean"==typeof e}function eW(e){return null===e}function eq(e){return null==e}function eH(e){return"number"==typeof e}function eG(e){return"string"==typeof e}function eK(e){return"symbol"==typeof e}function eV(e){return void 0===e}function eX(e){return eJ(e)&&"[object RegExp]"===e5(e)}function eJ(e){return"object"==typeof e&&null!==e}function e$(e){return eJ(e)&&"[object Date]"===e5(e)}function eQ(e){return eJ(e)&&("[object Error]"===e5(e)||e instanceof Error)}function e0(e){return"function"==typeof e}function e1(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e}function e2(e){return L.isBuffer(e)}function e5(e){return Object.prototype.toString.call(e)}function e3(e){return e<10?"0"+e.toString(10):e.toString(10)}eU.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},eU.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"};var e6=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function e8(){var e,t;console.log("%s - %s",(t=[e3((e=new Date).getHours()),e3(e.getMinutes()),e3(e.getSeconds())].join(":"),[e.getDate(),e6[e.getMonth()],t].join(" ")),eI.apply(null,arguments))}function e4(e,t){if(!t||!eJ(t))return e;for(var r=Object.keys(t),n=r.length;n--;)e[r[n]]=t[r[n]];return e}function e9(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var e7="undefined"!=typeof Symbol?Symbol("util.promisify.custom"):void 0;function te(e){if("function"!=typeof e)throw TypeError('The "original" argument must be of type Function');if(e7&&e[e7]){var t=e[e7];if("function"!=typeof t)throw TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(t,e7,{value:t,enumerable:!1,writable:!1,configurable:!0}),t}function t(){for(var t,r,n=new Promise(function(e,n){t=e,r=n}),i=[],a=0;a<arguments.length;a++)i.push(arguments[a]);i.push(function(e,n){e?r(e):t(n)});try{e.apply(this,i)}catch(e){r(e)}return n}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),e7&&Object.defineProperty(t,e7,{value:t,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(t,eO(e))}function tt(e,t){if(!e){var r=Error("Promise was rejected with a falsy value");r.reason=e,e=r}return t(e)}function tr(e){if("function"!=typeof e)throw TypeError('The "original" argument must be of type Function');function t(){for(var t=[],r=0;r<arguments.length;r++)t.push(arguments[r]);var n=t.pop();if("function"!=typeof n)throw TypeError("The last argument must be of type Function");var i=this,a=function(){return n.apply(i,arguments)};e.apply(this,t).then(function(e){em.nextTick(a.bind(null,null,e))},function(e){em.nextTick(tt.bind(null,e,a))})}return Object.setPrototypeOf(t,Object.getPrototypeOf(e)),Object.defineProperties(t,eO(e)),t}te.custom=e7;var tn=Object.freeze({__proto__:null,_extend:e4,callbackify:tr,debuglog:eN,default:{inherits:eT,_extend:e4,log:e8,isBuffer:e2,isPrimitive:e1,isFunction:e0,isError:eQ,isDate:e$,isObject:eJ,isRegExp:eX,isUndefined:eV,isSymbol:eK,isString:eG,isNumber:eH,isNullOrUndefined:eq,isNull:eW,isBoolean:eY,isArray:ej,inspect:eU,deprecate:eD,format:eI,debuglog:eN,promisify:te,callbackify:tr},deprecate:eD,format:eI,inherits:eT,inspect:eU,isArray:ej,isBoolean:eY,isBuffer:e2,isDate:e$,isError:eQ,isFunction:e0,isNull:eW,isNullOrUndefined:eq,isNumber:eH,isObject:eJ,isPrimitive:e1,isRegExp:eX,isString:eG,isSymbol:eK,isUndefined:eV,log:e8,promisify:te});function ti(){this.head=null,this.tail=null,this.length=0}ti.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},ti.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},ti.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},ti.prototype.clear=function(){this.head=this.tail=null,this.length=0},ti.prototype.join=function(e){if(0===this.length)return"";for(var t=this.head,r=""+t.data;t=t.next;)r+=e+t.data;return r},ti.prototype.concat=function(e){if(0===this.length)return L.alloc(0);if(1===this.length)return this.head.data;for(var t=L.allocUnsafe(e>>>0),r=this.head,n=0;r;)r.data.copy(t,n),n+=r.data.length,r=r.next;return t};var ta=L.isEncoding||function(e){switch(e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function ts(e){this.encoding=(e||"utf8").toLowerCase().replace(/[-_]/,"");if(e&&!ta(e))throw Error("Unknown encoding: "+e);switch(this.encoding){case"utf8":this.surrogateSize=3;break;case"ucs2":case"utf16le":this.surrogateSize=2,this.detectIncompleteChar=th;break;case"base64":this.surrogateSize=3,this.detectIncompleteChar=tl;break;default:this.write=to;return}this.charBuffer=new L(6),this.charReceived=0,this.charLength=0}function to(e){return e.toString(this.encoding)}function th(e){this.charReceived=e.length%2,this.charLength=2*!!this.charReceived}function tl(e){this.charReceived=e.length%3,this.charLength=3*!!this.charReceived}ts.prototype.write=function(e){for(var t="";this.charLength;){var r=e.length>=this.charLength-this.charReceived?this.charLength-this.charReceived:e.length;if(e.copy(this.charBuffer,this.charReceived,0,r),this.charReceived+=r,this.charReceived<this.charLength)return"";e=e.slice(r,e.length);var n=(t=this.charBuffer.slice(0,this.charLength).toString(this.encoding)).charCodeAt(t.length-1);if(n>=55296&&n<=56319){this.charLength+=this.surrogateSize,t="";continue}if(this.charReceived=this.charLength=0,0===e.length)return t;break}this.detectIncompleteChar(e);var i=e.length;this.charLength&&(e.copy(this.charBuffer,0,e.length-this.charReceived,i),i-=this.charReceived);var i=(t+=e.toString(this.encoding,0,i)).length-1,n=t.charCodeAt(i);if(n>=55296&&n<=56319){var a=this.surrogateSize;return this.charLength+=a,this.charReceived+=a,this.charBuffer.copy(this.charBuffer,a,0,a),e.copy(this.charBuffer,0,0,a),t.substring(0,i)}return t},ts.prototype.detectIncompleteChar=function(e){for(var t=e.length>=3?3:e.length;t>0;t--){var r=e[e.length-t];if(1==t&&r>>5==6){this.charLength=2;break}if(t<=2&&r>>4==14){this.charLength=3;break}if(t<=3&&r>>3==30){this.charLength=4;break}}this.charReceived=t},ts.prototype.end=function(e){var t="";if(e&&e.length&&(t=this.write(e)),this.charReceived){var r=this.charReceived,n=this.charBuffer,i=this.encoding;t+=n.slice(0,r).toString(i)}return t},tc.ReadableState=tu;var tf=eN("stream");function tu(e,t){e=e||{},this.objectMode=!!e.objectMode,t instanceof tP&&(this.objectMode=this.objectMode||!!e.readableObjectMode);var r=e.highWaterMark,n=this.objectMode?16:16384;this.highWaterMark=r||0===r?r:n,this.highWaterMark=~~this.highWaterMark,this.buffer=new ti,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.ranOut=!1,this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(this.decoder=new ts(e.encoding),this.encoding=e.encoding)}function tc(e){if(!(this instanceof tc))return new tc(e);this._readableState=new tu(e,this),this.readable=!0,e&&"function"==typeof e.read&&(this._read=e.read),ek.call(this)}function td(e,t,r,n,i){var a,s,o,h=(l=t,f=r,u=null,L.isBuffer(f)||"string"==typeof f||null==f||l.objectMode||(u=TypeError("Invalid non-string/buffer chunk")),u);if(h)e.emit("error",h);else if(null===r)t.reading=!1,function(e,t){if(!t.ended){if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,t_(e)}}(e,t);else if(t.objectMode||r&&r.length>0)if(t.ended&&!i){var l,f,u,c,d=Error("stream.push() after EOF");e.emit("error",d)}else if(t.endEmitted&&i){var p=Error("stream.unshift() after end event");e.emit("error",p)}else{!t.decoder||i||n||(r=t.decoder.write(r),c=!t.objectMode&&0===r.length),i||(t.reading=!1),!c&&(t.flowing&&0===t.length&&!t.sync?(e.emit("data",r),e.read(0)):(t.length+=t.objectMode?1:r.length,i?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&t_(e))),a=e,(s=t).readingMore||(s.readingMore=!0,ec(tb,a,s))}else i||(t.reading=!1);return!(o=t).ended&&(o.needReadable||o.length<o.highWaterMark||0===o.length)}function tp(e,t){var r;if(e<=0||0===t.length&&t.ended)return 0;if(t.objectMode)return 1;if(e!=e)if(t.flowing&&t.length)return t.buffer.head.data.length;else return t.length;return(e>t.highWaterMark&&((r=e)>=8388608?r=8388608:(r--,r|=r>>>1,r|=r>>>2,r|=r>>>4,r|=r>>>8,r|=r>>>16,r++),t.highWaterMark=r),e<=t.length)?e:t.ended?t.length:(t.needReadable=!0,0)}function t_(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(tf("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?ec(tg,e):tg(e))}function tg(e){tf("emit readable"),e.emit("readable"),tw(e)}function tb(e,t){for(var r=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(tf("maybeReadMore read 0"),e.read(0),r!==t.length);)r=t.length;t.readingMore=!1}function tm(e){tf("readable nexttick read 0"),e.read(0)}function tv(e,t){t.reading||(tf("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),tw(e),t.flowing&&!t.reading&&e.read(0)}function tw(e){var t=e._readableState;for(tf("flow",t.flowing);t.flowing&&null!==e.read(););}function ty(e,t){var r,n,i,a,s;return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):(n=e,i=t.buffer,a=t.decoder,n<i.head.data.length?(s=i.head.data.slice(0,n),i.head.data=i.head.data.slice(n)):s=n===i.head.data.length?i.shift():a?function(e,t){var r=t.head,n=1,i=r.data;for(e-=i.length;r=r.next;){var a=r.data,s=e>a.length?a.length:e;if(s===a.length?i+=a:i+=a.slice(0,e),0==(e-=s)){s===a.length?(++n,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=a.slice(s));break}++n}return t.length-=n,i}(n,i):function(e,t){var r=L.allocUnsafe(e),n=t.head,i=1;for(n.data.copy(r),e-=n.data.length;n=n.next;){var a=n.data,s=e>a.length?a.length:e;if(a.copy(r,r.length-e,0,s),0==(e-=s)){s===a.length?(++i,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=a.slice(s));break}++i}return t.length-=i,r}(n,i),r=s),r)}function tE(e){var t=e._readableState;if(t.length>0)throw Error('"endReadable()" called on non-empty stream');t.endEmitted||(t.ended=!0,ec(tk,t,e))}function tk(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function tR(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return -1}function tS(){}function tx(e,t,r){this.chunk=e,this.encoding=t,this.callback=r,this.next=null}function tA(e,t){Object.defineProperty(this,"buffer",{get:eD(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.")}),e=e||{},this.objectMode=!!e.objectMode,t instanceof tP&&(this.objectMode=this.objectMode||!!e.writableObjectMode);var r=e.highWaterMark,n=this.objectMode?16:16384;this.highWaterMark=r||0===r?r:n,this.highWaterMark=~~this.highWaterMark,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1;var i=!1===e.decodeStrings;this.decodeStrings=!i,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var r=e._writableState,n=r.sync,i=r.writecb;if(r.writing=!1,r.writecb=null,r.length-=r.writelen,r.writelen=0,t)--r.pendingcb,n?ec(i,t):i(t),e._writableState.errorEmitted=!0,e.emit("error",t);else{var a=tI(r);a||r.corked||r.bufferProcessing||!r.bufferedRequest||tz(e,r),n?ec(tO,e,r,a,i):tO(e,r,a,i)}}(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new tN(this)}function tL(e){if(!(this instanceof tL)&&!(this instanceof tP))return new tL(e);this._writableState=new tA(e,this),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev)),ek.call(this)}function tT(e,t,r,n,i,a,s){t.writelen=n,t.writecb=s,t.writing=!0,t.sync=!0,r?e._writev(i,t.onwrite):e._write(i,a,t.onwrite),t.sync=!1}function tO(e,t,r,n){var i,a;r||(i=e,0===(a=t).length&&a.needDrain&&(a.needDrain=!1,i.emit("drain"))),t.pendingcb--,n(),tM(e,t)}function tz(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=Array(t.bufferedRequestCount),i=t.corkedRequestsFree;i.entry=r;for(var a=0;r;)n[a]=r,r=r.next,a+=1;tT(e,t,!0,t.length,n,"",i.finish),t.pendingcb++,t.lastBufferedRequest=null,i.next?(t.corkedRequestsFree=i.next,i.next=null):t.corkedRequestsFree=new tN(t)}else{for(;r;){var s=r.chunk,o=r.encoding,h=r.callback,l=t.objectMode?1:s.length;if(tT(e,t,!1,l,s,o,h),r=r.next,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequestCount=0,t.bufferedRequest=r,t.bufferProcessing=!1}function tI(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function tD(e,t){t.prefinished||(t.prefinished=!0,e.emit("prefinish"))}function tM(e,t){var r=tI(t);return r&&(0===t.pendingcb?(tD(e,t),t.finished=!0,e.emit("finish")):tD(e,t)),r}function tN(e){var t=this;this.next=null,this.entry=null,this.finish=function(r){var n=t.entry;for(t.entry=null;n;){var i=n.callback;e.pendingcb--,i(r),n=n.next}e.corkedRequestsFree?e.corkedRequestsFree.next=t:e.corkedRequestsFree=t}}eT(tc,ek),tc.prototype.push=function(e,t){var r=this._readableState;return r.objectMode||"string"!=typeof e||(t=t||r.defaultEncoding)!==r.encoding&&(e=L.from(e,t),t=""),td(this,r,e,t,!1)},tc.prototype.unshift=function(e){return td(this,this._readableState,e,"",!0)},tc.prototype.isPaused=function(){return!1===this._readableState.flowing},tc.prototype.setEncoding=function(e){return this._readableState.decoder=new ts(e),this._readableState.encoding=e,this},tc.prototype.read=function(e){tf("read",e),e=parseInt(e,10);var t,r=this._readableState,n=e;if(0!==e&&(r.emittedReadable=!1),0===e&&r.needReadable&&(r.length>=r.highWaterMark||r.ended))return tf("read: emitReadable",r.length,r.ended),0===r.length&&r.ended?tE(this):t_(this),null;if(0===(e=tp(e,r))&&r.ended)return 0===r.length&&tE(this),null;var i=r.needReadable;return tf("need readable",i),(0===r.length||r.length-e<r.highWaterMark)&&tf("length less than watermark",i=!0),r.ended||r.reading?tf("reading or ended",i=!1):i&&(tf("do read"),r.reading=!0,r.sync=!0,0===r.length&&(r.needReadable=!0),this._read(r.highWaterMark),r.sync=!1,r.reading||(e=tp(n,r))),null===(t=e>0?ty(e,r):null)?(r.needReadable=!0,e=0):r.length-=e,0===r.length&&(r.ended||(r.needReadable=!0),n!==e&&r.ended&&tE(this)),null!==t&&this.emit("data",t),t},tc.prototype._read=function(e){this.emit("error",Error("not implemented"))},tc.prototype.pipe=function(e,t){var r,n=this,i=this._readableState;switch(i.pipesCount){case 0:i.pipes=e;break;case 1:i.pipes=[i.pipes,e];break;default:i.pipes.push(e)}i.pipesCount+=1,tf("pipe count=%d opts=%j",i.pipesCount,t);var a=t&&!1===t.end?f:o;function s(e){tf("onunpipe"),e===n&&f()}function o(){tf("onend"),e.end()}i.endEmitted?ec(a):n.once("end",a),e.on("unpipe",s);var h=(r=n,function(){var e=r._readableState;tf("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&r.listeners("data").length&&(e.flowing=!0,tw(r))});e.on("drain",h);var l=!1;function f(){tf("cleanup"),e.removeListener("close",p),e.removeListener("finish",_),e.removeListener("drain",h),e.removeListener("error",d),e.removeListener("unpipe",s),n.removeListener("end",o),n.removeListener("end",f),n.removeListener("data",c),l=!0,i.awaitDrain&&(!e._writableState||e._writableState.needDrain)&&h()}var u=!1;function c(t){tf("ondata"),u=!1,!1!==e.write(t)||u||((1===i.pipesCount&&i.pipes===e||i.pipesCount>1&&-1!==tR(i.pipes,e))&&!l&&(tf("false write response, pause",n._readableState.awaitDrain),n._readableState.awaitDrain++,u=!0),n.pause())}function d(t){var r;tf("onerror",t),g(),e.removeListener("error",d),0===(r="error",e.listeners(r).length)&&e.emit("error",t)}function p(){e.removeListener("finish",_),g()}function _(){tf("onfinish"),e.removeListener("close",p),g()}function g(){tf("unpipe"),n.unpipe(e)}return n.on("data",c),!function(e,t,r){if("function"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?Array.isArray(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(e,"error",d),e.once("close",p),e.once("finish",_),e.emit("pipe",n),i.flowing||(tf("pipe resume"),n.resume()),e},tc.prototype.unpipe=function(e){var t=this._readableState;if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this)),this;if(!e){var r=t.pipes,n=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var i=0;i<n;i++)r[i].emit("unpipe",this);return this}var a=tR(t.pipes,e);return -1===a||(t.pipes.splice(a,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this)),this},tc.prototype.on=function(e,t){var r=ek.prototype.on.call(this,e,t);if("data"===e)!1!==this._readableState.flowing&&this.resume();else if("readable"===e){var n=this._readableState;n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.emittedReadable=!1,n.reading?n.length&&t_(this):ec(tm,this))}return r},tc.prototype.addListener=tc.prototype.on,tc.prototype.resume=function(){var e,t,r=this._readableState;return r.flowing||(tf("resume"),r.flowing=!0,e=this,(t=r).resumeScheduled||(t.resumeScheduled=!0,ec(tv,e,t))),this},tc.prototype.pause=function(){return tf("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(tf("pause"),this._readableState.flowing=!1,this.emit("pause")),this},tc.prototype.wrap=function(e){var t=this._readableState,r=!1,n=this;for(var i in e.on("end",function(){if(tf("wrapped end"),t.decoder&&!t.ended){var e=t.decoder.end();e&&e.length&&n.push(e)}n.push(null)}),e.on("data",function(i){if(tf("wrapped data"),t.decoder&&(i=t.decoder.write(i)),!t.objectMode||null!=i)(t.objectMode||i&&i.length)&&(n.push(i)||(r=!0,e.pause()))}),e)void 0===this[i]&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));return function(e,t){for(var r=0,n=e.length;r<n;r++)t(e[r],r)}(["error","close","destroy","pause","resume"],function(t){e.on(t,n.emit.bind(n,t))}),n._read=function(t){tf("wrapped _read",t),r&&(r=!1,e.resume())},n},tc._fromList=ty,tL.WritableState=tA,eT(tL,ek),tA.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},tL.prototype.pipe=function(){this.emit("error",Error("Cannot pipe, not readable"))},tL.prototype.write=function(e,t,r){var n,i,a,s,o,h=this._writableState,l=!1;return("function"==typeof t&&(r=t,t=null),L.isBuffer(e)?t="buffer":t||(t=h.defaultEncoding),"function"!=typeof r&&(r=tS),h.ended)?(n=r,i=Error("write after end"),this.emit("error",i),ec(n,i)):(a=r,s=!0,o=!1,null===e?o=TypeError("May not write null values to stream"):L.isBuffer(e)||"string"==typeof e||void 0===e||h.objectMode||(o=TypeError("Invalid non-string/buffer chunk")),o&&(this.emit("error",o),ec(a,o),s=!1),s&&(h.pendingcb++,l=function(e,t,r,n,i){a=r,s=n,t.objectMode||!1===t.decodeStrings||"string"!=typeof a||(a=L.from(a,s)),r=a,L.isBuffer(r)&&(n="buffer");var a,s,o=t.objectMode?1:r.length;t.length+=o;var h=t.length<t.highWaterMark;if(h||(t.needDrain=!0),t.writing||t.corked){var l=t.lastBufferedRequest;t.lastBufferedRequest=new tx(r,n,i),l?l.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else tT(e,t,!1,o,r,n,i);return h}(this,h,e,t,r))),l},tL.prototype.cork=function(){var e=this._writableState;e.corked++},tL.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.finished||e.bufferProcessing||!e.bufferedRequest||tz(this,e))},tL.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},tL.prototype._write=function(e,t,r){r(Error("not implemented"))},tL.prototype._writev=null,tL.prototype.end=function(e,t,r){var n,i,a,s=this._writableState;"function"==typeof e?(r=e,e=null,t=null):"function"==typeof t&&(r=t,t=null),null!=e&&this.write(e,t),s.corked&&(s.corked=1,this.uncork()),s.ending||s.finished||(n=this,i=s,a=r,i.ending=!0,tM(n,i),a&&(i.finished?ec(a):n.once("finish",a)),i.ended=!0,n.writable=!1)},eT(tP,tc);for(var tU=Object.keys(tL.prototype),tB=0;tB<tU.length;tB++){var tZ=tU[tB];tP.prototype[tZ]||(tP.prototype[tZ]=tL.prototype[tZ])}function tP(e){if(!(this instanceof tP))return new tP(e);tc.call(this,e),tL.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",tF)}function tF(){this.allowHalfOpen||this._writableState.ended||ec(tC,this)}function tC(e){e.end()}function tj(e){this.afterTransform=function(t,r){var n=e,i=t,a=r,s=n._transformState;s.transforming=!1;var o=s.writecb;if(!o)return n.emit("error",Error("no writecb in Transform class"));s.writechunk=null,s.writecb=null,null!=a&&n.push(a),o(i);var h=n._readableState;h.reading=!1,(h.needReadable||h.length<h.highWaterMark)&&n._read(h.highWaterMark)},this.needTransform=!1,this.transforming=!1,this.writecb=null,this.writechunk=null,this.writeencoding=null}function tY(e){if(!(this instanceof tY))return new tY(e);tP.call(this,e),this._transformState=new tj(this);var t=this;this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.once("prefinish",function(){"function"==typeof this._flush?this._flush(function(e){tW(t,e)}):tW(t)})}function tW(e,t){if(t)return e.emit("error",t);var r=e._writableState,n=e._transformState;if(r.length)throw Error("Calling transform done when ws.length != 0");if(n.transforming)throw Error("Calling transform done when still transforming");return e.push(null)}function tq(e){if(!(this instanceof tq))return new tq(e);tY.call(this,e)}function tH(){ek.call(this)}eT(tY,tP),tY.prototype.push=function(e,t){return this._transformState.needTransform=!1,tP.prototype.push.call(this,e,t)},tY.prototype._transform=function(e,t,r){throw Error("Not implemented")},tY.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},tY.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},eT(tq,tY),tq.prototype._transform=function(e,t,r){r(null,e)},eT(tH,ek),tH.Readable=tc,tH.Writable=tL,tH.Duplex=tP,tH.Transform=tY,tH.PassThrough=tq,tH.Stream=tH,tH.prototype.pipe=function(e,t){var r=this;function n(t){e.writable&&!1===e.write(t)&&r.pause&&r.pause()}function i(){r.readable&&r.resume&&r.resume()}r.on("data",n),e.on("drain",i),e._isStdio||t&&!1===t.end||(r.on("end",s),r.on("close",o));var a=!1;function s(){a||(a=!0,e.end())}function o(){a||(a=!0,"function"==typeof e.destroy&&e.destroy())}function h(e){if(l(),0===ek.listenerCount(this,"error"))throw e}function l(){r.removeListener("data",n),e.removeListener("drain",i),r.removeListener("end",s),r.removeListener("close",o),r.removeListener("error",h),e.removeListener("error",h),r.removeListener("end",l),r.removeListener("close",l),e.removeListener("close",l)}return r.on("error",h),e.on("error",h),r.on("end",l),r.on("close",l),e.on("close",l),e.emit("pipe",r),e};var tG=ev(Object.freeze({__proto__:null,Duplex:tP,PassThrough:tq,Readable:tc,Stream:tH,Transform:tY,Writable:tL,default:tH})),tK={};function tV(e,t){if(e===t)return 0;for(var r=e.length,n=t.length,i=0,a=Math.min(r,n);i<a;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:+(n<r)}var tX=Object.prototype.hasOwnProperty,tJ=Object.keys||function(e){var t=[];for(var r in e)tX.call(e,r)&&t.push(r);return t},t$=Array.prototype.slice;function tQ(){return void 0!==o?o:o="foo"===(function(){}).name}function t0(e){return Object.prototype.toString.call(e)}function t1(e){return!Q(e)&&"function"==typeof p.ArrayBuffer&&("function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):!!e&&!!(e instanceof DataView||e.buffer&&e.buffer instanceof ArrayBuffer))}function t2(e,t){e||t9(e,!0,t,"==",t7)}var t5=/\s*function\s+([^\(\s]*)\s*/;function t3(e){if(e0(e)){if(tQ())return e.name;var t=e.toString().match(t5);return t&&t[1]}}function t6(e){this.name="AssertionError",this.actual=e.actual,this.expected=e.expected,this.operator=e.operator,e.message?(this.message=e.message,this.generatedMessage=!1):(this.message=(t=this,t8(t4(t.actual),128)+" "+t.operator+" "+t8(t4(t.expected),128)),this.generatedMessage=!0);var t,r=e.stackStartFunction||t9;if(Error.captureStackTrace)Error.captureStackTrace(this,r);else{var n=Error();if(n.stack){var i=n.stack,a=t3(r),s=i.indexOf("\n"+a);if(s>=0){var o=i.indexOf("\n",s+1);i=i.substring(o+1)}this.stack=i}}}function t8(e,t){return"string"==typeof e?e.length<t?e:e.slice(0,t):e}function t4(e){if(tQ()||!e0(e))return eU(e);var t=t3(e);return"[Function"+(t?": "+t:"")+"]"}function t9(e,t,r,n,i){throw new t6({message:r,actual:e,expected:t,operator:n,stackStartFunction:i})}function t7(e,t){e||t9(e,!0,t,"==",t7)}function re(e,t,r){e!=t&&t9(e,t,r,"==",re)}function rt(e,t,r){e==t&&t9(e,t,r,"!=",rt)}function rr(e,t,r){ri(e,t,!1)||t9(e,t,r,"deepEqual",rr)}function rn(e,t,r){ri(e,t,!0)||t9(e,t,r,"deepStrictEqual",rn)}function ri(e,t,r,n){if(e===t)return!0;if(Q(e)&&Q(t))return 0===tV(e,t);if(e$(e)&&e$(t))return e.getTime()===t.getTime();if(eX(e)&&eX(t))return e.source===t.source&&e.global===t.global&&e.multiline===t.multiline&&e.lastIndex===t.lastIndex&&e.ignoreCase===t.ignoreCase;if((null===e||"object"!=typeof e)&&(null===t||"object"!=typeof t))return r?e===t:e==t;if(t1(e)&&t1(t)&&t0(e)===t0(t)&&!(e instanceof Float32Array||e instanceof Float64Array))return 0===tV(new Uint8Array(e.buffer),new Uint8Array(t.buffer));if(Q(e)!==Q(t))return!1;var i=(n=n||{actual:[],expected:[]}).actual.indexOf(e);return -1!==i&&i===n.expected.indexOf(t)||(n.actual.push(e),n.expected.push(t),function(e,t,r,n){if(null==e||null==t)return!1;if(e1(e)||e1(t))return e===t;if(r&&Object.getPrototypeOf(e)!==Object.getPrototypeOf(t))return!1;var i,a,s=ra(e),o=ra(t);if(s&&!o||!s&&o)return!1;if(s)return ri(e=t$.call(e),t=t$.call(t),r);var h=tJ(e),l=tJ(t);if(h.length!==l.length)return!1;for(h.sort(),l.sort(),a=h.length-1;a>=0;a--)if(h[a]!==l[a])return!1;for(a=h.length-1;a>=0;a--)if(!ri(e[i=h[a]],t[i],r,n))return!1;return!0}(e,t,r,n))}function ra(e){return"[object Arguments]"==Object.prototype.toString.call(e)}function rs(e,t,r){ri(e,t,!1)&&t9(e,t,r,"notDeepEqual",rs)}function ro(e,t,r){ri(e,t,!0)&&t9(e,t,r,"notDeepStrictEqual",ro)}function rh(e,t,r){e!==t&&t9(e,t,r,"===",rh)}function rl(e,t,r){e===t&&t9(e,t,r,"!==",rl)}function rf(e,t){if(!e||!t)return!1;if("[object RegExp]"==Object.prototype.toString.call(t))return t.test(e);try{if(e instanceof t)return!0}catch(e){}return!Error.isPrototypeOf(t)&&!0===t.call({},e)}function ru(e,t,r,n){if("function"!=typeof t)throw TypeError('"block" argument must be a function');"string"==typeof r&&(n=r,r=null),i=function(e){var t;try{e()}catch(e){t=e}return t}(t),n=(r&&r.name?" ("+r.name+").":".")+(n?" "+n:"."),e&&!i&&t9(i,r,"Missing expected exception"+n);var i,a="string"==typeof n,s=!e&&eQ(i),o=!e&&i&&!r;if((s&&a&&rf(i,r)||o)&&t9(i,r,"Got unwanted exception"+n),e&&i&&r&&!rf(i,r)||!e&&i)throw i}function rc(e,t,r){ru(!0,e,t,r)}function rd(e,t,r){ru(!1,e,t,r)}function rp(e){if(e)throw e}t2.AssertionError=t6,eT(t6,Error),t2.fail=t9,t2.ok=t7,t2.equal=re,t2.notEqual=rt,t2.deepEqual=rr,t2.deepStrictEqual=rn,t2.notDeepEqual=rs,t2.notDeepStrictEqual=ro,t2.strictEqual=rh,t2.notStrictEqual=rl,t2.throws=rc,t2.doesNotThrow=rd,t2.ifError=rp;var r_=ev(Object.freeze({__proto__:null,AssertionError:t6,assert:t7,deepEqual:rr,deepStrictEqual:rn,default:t2,doesNotThrow:rd,equal:re,fail:t9,ifError:rp,notDeepEqual:rs,notDeepStrictEqual:ro,notEqual:rt,notStrictEqual:rl,ok:t7,strictEqual:rh,throws:rc})),rg=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0},rb={},rm={};!function(e){var t="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;e.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw TypeError(r+"must be non-object");for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}}return e},e.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var r={arraySet:function(e,t,r,n,i){if(t.subarray&&e.subarray)return void e.set(t.subarray(r,r+n),i);for(var a=0;a<n;a++)e[i+a]=t[r+a]},flattenChunks:function(e){var t,r,n,i,a,s;for(t=0,n=0,r=e.length;t<r;t++)n+=e[t].length;for(t=0,s=new Uint8Array(n),i=0,r=e.length;t<r;t++)a=e[t],s.set(a,i),i+=a.length;return s}},n={arraySet:function(e,t,r,n,i){for(var a=0;a<n;a++)e[i+a]=t[r+a]},flattenChunks:function(e){return[].concat.apply([],e)}};e.setTyped=function(t){t?(e.Buf8=Uint8Array,e.Buf16=Uint16Array,e.Buf32=Int32Array,e.assign(e,r)):(e.Buf8=Array,e.Buf16=Array,e.Buf32=Array,e.assign(e,n))},e.setTyped(t)}(rm);var rv={};function rw(e){for(var t=e.length;--t>=0;)e[t]=0}var ry=573,rE=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],rk=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],rR=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],rS=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],rx=Array(576);rw(rx);var rA=Array(60);rw(rA);var rL=Array(512);rw(rL);var rT=Array(256);rw(rT);var rO=Array(29);rw(rO);var rz=Array(30);function rI(e,t,r,n,i){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=n,this.max_length=i,this.has_stree=e&&e.length}function rD(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function rM(e){return e<256?rL[e]:rL[256+(e>>>7)]}function rN(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function rU(e,t,r){e.bi_valid>16-r?(e.bi_buf|=t<<e.bi_valid&65535,rN(e,e.bi_buf),e.bi_buf=t>>16-e.bi_valid,e.bi_valid+=r-16):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function rB(e,t,r){rU(e,r[2*t],r[2*t+1])}function rZ(e,t){var r=0;do r|=1&e,e>>>=1,r<<=1;while(--t>0);return r>>>1}function rP(e,t,r){var n,i,a=Array(16),s=0;for(n=1;n<=15;n++)a[n]=s=s+r[n-1]<<1;for(i=0;i<=t;i++){var o=e[2*i+1];0!==o&&(e[2*i]=rZ(a[o]++,o))}}function rF(e){var t;for(t=0;t<286;t++)e.dyn_ltree[2*t]=0;for(t=0;t<30;t++)e.dyn_dtree[2*t]=0;for(t=0;t<19;t++)e.bl_tree[2*t]=0;e.dyn_ltree[512]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function rC(e){e.bi_valid>8?rN(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function rj(e,t,r,n){var i=2*t,a=2*r;return e[i]<e[a]||e[i]===e[a]&&n[t]<=n[r]}function rY(e,t,r){for(var n=e.heap[r],i=r<<1;i<=e.heap_len&&(i<e.heap_len&&rj(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!rj(t,n,e.heap[i],e.depth));)e.heap[r]=e.heap[i],r=i,i<<=1;e.heap[r]=n}function rW(e,t,r){var n,i,a,s,o=0;if(0!==e.last_lit)do n=e.pending_buf[e.d_buf+2*o]<<8|e.pending_buf[e.d_buf+2*o+1],i=e.pending_buf[e.l_buf+o],o++,0===n?rB(e,i,t):(rB(e,(a=rT[i])+256+1,t),0!==(s=rE[a])&&rU(e,i-=rO[a],s),rB(e,a=rM(--n),r),0!==(s=rk[a])&&rU(e,n-=rz[a],s));while(o<e.last_lit);rB(e,256,t)}function rq(e,t){var r,n,i,a=t.dyn_tree,s=t.stat_desc.static_tree,o=t.stat_desc.has_stree,h=t.stat_desc.elems,l=-1;for(r=0,e.heap_len=0,e.heap_max=ry;r<h;r++)0!==a[2*r]?(e.heap[++e.heap_len]=l=r,e.depth[r]=0):a[2*r+1]=0;for(;e.heap_len<2;)a[2*(i=e.heap[++e.heap_len]=l<2?++l:0)]=1,e.depth[i]=0,e.opt_len--,o&&(e.static_len-=s[2*i+1]);for(t.max_code=l,r=e.heap_len>>1;r>=1;r--)rY(e,a,r);i=h;do r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],rY(e,a,1),n=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=n,a[2*i]=a[2*r]+a[2*n],e.depth[i]=(e.depth[r]>=e.depth[n]?e.depth[r]:e.depth[n])+1,a[2*r+1]=a[2*n+1]=i,e.heap[1]=i++,rY(e,a,1);while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var r,n,i,a,s,o,h=t.dyn_tree,l=t.max_code,f=t.stat_desc.static_tree,u=t.stat_desc.has_stree,c=t.stat_desc.extra_bits,d=t.stat_desc.extra_base,p=t.stat_desc.max_length,_=0;for(a=0;a<=15;a++)e.bl_count[a]=0;for(h[2*e.heap[e.heap_max]+1]=0,r=e.heap_max+1;r<ry;r++)(a=h[2*h[2*(n=e.heap[r])+1]+1]+1)>p&&(a=p,_++),h[2*n+1]=a,!(n>l)&&(e.bl_count[a]++,s=0,n>=d&&(s=c[n-d]),o=h[2*n],e.opt_len+=o*(a+s),u&&(e.static_len+=o*(f[2*n+1]+s)));if(0!==_){do{for(a=p-1;0===e.bl_count[a];)a--;e.bl_count[a]--,e.bl_count[a+1]+=2,e.bl_count[p]--,_-=2}while(_>0);for(a=p;0!==a;a--)for(n=e.bl_count[a];0!==n;)!((i=e.heap[--r])>l)&&(h[2*i+1]!==a&&(e.opt_len+=(a-h[2*i+1])*h[2*i],h[2*i+1]=a),n--)}}(e,t),rP(a,l,e.bl_count)}function rH(e,t,r){var n,i,a=-1,s=t[1],o=0,h=7,l=4;for(0===s&&(h=138,l=3),t[(r+1)*2+1]=65535,n=0;n<=r;n++)i=s,s=t[(n+1)*2+1],++o<h&&i===s||(o<l?e.bl_tree[2*i]+=o:0!==i?(i!==a&&e.bl_tree[2*i]++,e.bl_tree[32]++):o<=10?e.bl_tree[34]++:e.bl_tree[36]++,o=0,a=i,0===s?(h=138,l=3):i===s?(h=6,l=3):(h=7,l=4))}function rG(e,t,r){var n,i,a=-1,s=t[1],o=0,h=7,l=4;for(0===s&&(h=138,l=3),n=0;n<=r;n++)if(i=s,s=t[(n+1)*2+1],!(++o<h)||i!==s){if(o<l)do rB(e,i,e.bl_tree);while(0!=--o);else 0!==i?(i!==a&&(rB(e,i,e.bl_tree),o--),rB(e,16,e.bl_tree),rU(e,o-3,2)):o<=10?(rB(e,17,e.bl_tree),rU(e,o-3,3)):(rB(e,18,e.bl_tree),rU(e,o-11,7));o=0,a=i,0===s?(h=138,l=3):i===s?(h=6,l=3):(h=7,l=4)}}rw(rz);var rK=!1;function rV(e,t,r,n){rU(e,0+ +!!n,3),rC(e),rN(e,r),rN(e,~r),rm.arraySet(e.pending_buf,e.window,t,r,e.pending),e.pending+=r}rv._tr_init=function(e){rK||(!function(){var e,t,r,n,i,a=Array(16);for(n=0,r=0;n<28;n++)for(e=0,rO[n]=r;e<1<<rE[n];e++)rT[r++]=n;for(rT[r-1]=n,i=0,n=0;n<16;n++)for(e=0,rz[n]=i;e<1<<rk[n];e++)rL[i++]=n;for(i>>=7;n<30;n++)for(e=0,rz[n]=i<<7;e<1<<rk[n]-7;e++)rL[256+i++]=n;for(t=0;t<=15;t++)a[t]=0;for(e=0;e<=143;)rx[2*e+1]=8,e++,a[8]++;for(;e<=255;)rx[2*e+1]=9,e++,a[9]++;for(;e<=279;)rx[2*e+1]=7,e++,a[7]++;for(;e<=287;)rx[2*e+1]=8,e++,a[8]++;for(rP(rx,287,a),e=0;e<30;e++)rA[2*e+1]=5,rA[2*e]=rZ(e,5);h=new rI(rx,rE,257,286,15),l=new rI(rA,rk,0,30,15),f=new rI([],rR,0,19,7)}(),rK=!0),e.l_desc=new rD(e.dyn_ltree,h),e.d_desc=new rD(e.dyn_dtree,l),e.bl_desc=new rD(e.bl_tree,f),e.bi_buf=0,e.bi_valid=0,rF(e)},rv._tr_stored_block=rV,rv._tr_flush_block=function(e,t,r,n){var i,a,s=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=function(e){var t,r=0xf3ffc07f;for(t=0;t<=31;t++,r>>>=1)if(1&r&&0!==e.dyn_ltree[2*t])return 0;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return 1;for(t=32;t<256;t++)if(0!==e.dyn_ltree[2*t])return 1;return 0}(e)),rq(e,e.l_desc),rq(e,e.d_desc),s=function(e){var t;for(rH(e,e.dyn_ltree,e.l_desc.max_code),rH(e,e.dyn_dtree,e.d_desc.max_code),rq(e,e.bl_desc),t=18;t>=3&&0===e.bl_tree[2*rS[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),i=e.opt_len+3+7>>>3,(a=e.static_len+3+7>>>3)<=i&&(i=a)):i=a=r+5,r+4<=i&&-1!==t?rV(e,t,r,n):4===e.strategy||a===i?(rU(e,2+ +!!n,3),rW(e,rx,rA)):(rU(e,4+ +!!n,3),function(e,t,r,n){var i;for(rU(e,t-257,5),rU(e,r-1,5),rU(e,n-4,4),i=0;i<n;i++)rU(e,e.bl_tree[2*rS[i]+1],3);rG(e,e.dyn_ltree,t-1),rG(e,e.dyn_dtree,r-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,s+1),rW(e,e.dyn_ltree,e.dyn_dtree)),rF(e),n&&rC(e)},rv._tr_tally=function(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[(rT[r]+256+1)*2]++,e.dyn_dtree[2*rM(t)]++),e.last_lit===e.lit_bufsize-1},rv._tr_align=function(e){rU(e,2,3),rB(e,256,rx),16===e.bi_valid?(rN(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)};var rX=function(e,t,r,n){for(var i=65535&e,a=e>>>16&65535,s=0;0!==r;){s=r>2e3?2e3:r,r-=s;do a=a+(i=i+t[n++]|0)|0;while(--s);i%=65521,a%=65521}return i|a<<16},rJ=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?0xedb88320^e>>>1:e>>>1;t[r]=e}return t}(),r$=function(e,t,r,n){var i=n+r;e^=-1;for(var a=n;a<i;a++)e=e>>>8^rJ[(e^t[a])&255];return -1^e},rQ={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},r0=573;function r1(e,t){return e.msg=rQ[t],t}function r2(e){return(e<<1)-9*(e>4)}function r5(e){for(var t=e.length;--t>=0;)e[t]=0}function r3(e){var t=e.state,r=t.pending;r>e.avail_out&&(r=e.avail_out),0!==r&&(rm.arraySet(e.output,t.pending_buf,t.pending_out,r,e.next_out),e.next_out+=r,t.pending_out+=r,e.total_out+=r,e.avail_out-=r,t.pending-=r,0===t.pending&&(t.pending_out=0))}function r6(e,t){rv._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,r3(e.strm)}function r8(e,t){e.pending_buf[e.pending++]=t}function r4(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function r9(e,t){var r,n,i=e.max_chain_length,a=e.strstart,s=e.prev_length,o=e.nice_match,h=e.strstart>e.w_size-262?e.strstart-(e.w_size-262):0,l=e.window,f=e.w_mask,u=e.prev,c=e.strstart+258,d=l[a+s-1],p=l[a+s];e.prev_length>=e.good_match&&(i>>=2),o>e.lookahead&&(o=e.lookahead);do{if(l[(r=t)+s]!==p||l[r+s-1]!==d||l[r]!==l[a]||l[++r]!==l[a+1])continue;a+=2,r++;do;while(l[++a]===l[++r]&&l[++a]===l[++r]&&l[++a]===l[++r]&&l[++a]===l[++r]&&l[++a]===l[++r]&&l[++a]===l[++r]&&l[++a]===l[++r]&&l[++a]===l[++r]&&a<c);if(n=258-(c-a),a=c-258,n>s){if(e.match_start=t,s=n,n>=o)break;d=l[a+s-1],p=l[a+s]}}while((t=u[t&f])>h&&0!=--i);return s<=e.lookahead?s:e.lookahead}function r7(e){var t,r,n,i,a,s=e.w_size;do{if(i=e.window_size-e.lookahead-e.strstart,e.strstart>=s+(s-262)){rm.arraySet(e.window,e.window,s,s,0),e.match_start-=s,e.strstart-=s,e.block_start-=s,t=r=e.hash_size;do n=e.head[--t],e.head[t]=n>=s?n-s:0;while(--r);t=r=s;do n=e.prev[--t],e.prev[t]=n>=s?n-s:0;while(--r);i+=s}if(0===e.strm.avail_in)break;if(r=function(e,t,r,n){var i=e.avail_in;return(i>n&&(i=n),0===i)?0:(e.avail_in-=i,rm.arraySet(t,e.input,e.next_in,i,r),1===e.state.wrap?e.adler=rX(e.adler,t,i,r):2===e.state.wrap&&(e.adler=r$(e.adler,t,i,r)),e.next_in+=i,e.total_in+=i,i)}(e.strm,e.window,e.strstart+e.lookahead,i),e.lookahead+=r,e.lookahead+e.insert>=3)for(a=e.strstart-e.insert,e.ins_h=e.window[a],e.ins_h=(e.ins_h<<e.hash_shift^e.window[a+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[a+3-1])&e.hash_mask,e.prev[a&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=a,a++,e.insert--,!(e.lookahead+e.insert<3)););}while(e.lookahead<262&&0!==e.strm.avail_in)}function ne(e,t){for(var r,n;;){if(e.lookahead<262){if(r7(e),e.lookahead<262&&0===t)return 1;if(0===e.lookahead)break}if(r=0,e.lookahead>=3&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==r&&e.strstart-r<=e.w_size-262&&(e.match_length=r9(e,r)),e.match_length>=3)if(n=rv._tr_tally(e,e.strstart-e.match_start,e.match_length-3),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=3){e.match_length--;do e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart;while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else n=rv._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(n&&(r6(e,!1),0===e.strm.avail_out))return 1}return(e.insert=e.strstart<2?e.strstart:2,4===t)?(r6(e,!0),0===e.strm.avail_out)?3:4:e.last_lit&&(r6(e,!1),0===e.strm.avail_out)?1:2}function nt(e,t){for(var r,n,i;;){if(e.lookahead<262){if(r7(e),e.lookahead<262&&0===t)return 1;if(0===e.lookahead)break}if(r=0,e.lookahead>=3&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=2,0!==r&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-262&&(e.match_length=r9(e,r),e.match_length<=5&&(1===e.strategy||3===e.match_length&&e.strstart-e.match_start>4096)&&(e.match_length=2)),e.prev_length>=3&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-3,n=rv._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-3),e.lookahead-=e.prev_length-1,e.prev_length-=2;do++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+3-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart);while(0!=--e.prev_length);if(e.match_available=0,e.match_length=2,e.strstart++,n&&(r6(e,!1),0===e.strm.avail_out))return 1}else if(e.match_available){if((n=rv._tr_tally(e,0,e.window[e.strstart-1]))&&r6(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return 1}else e.match_available=1,e.strstart++,e.lookahead--}return(e.match_available&&(n=rv._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<2?e.strstart:2,4===t)?(r6(e,!0),0===e.strm.avail_out)?3:4:e.last_lit&&(r6(e,!1),0===e.strm.avail_out)?1:2}function nr(e,t,r,n,i){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=n,this.func=i}function nn(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=8,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new rm.Buf16(2*r0),this.dyn_dtree=new rm.Buf16(122),this.bl_tree=new rm.Buf16(78),r5(this.dyn_ltree),r5(this.dyn_dtree),r5(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new rm.Buf16(16),this.heap=new rm.Buf16(573),r5(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new rm.Buf16(573),r5(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function ni(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=2,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?42:113,e.adler=+(2!==t.wrap),t.last_flush=0,rv._tr_init(t),0):r1(e,-2)}function na(e){var t,r=ni(e);return 0===r&&((t=e.state).window_size=2*t.w_size,r5(t.head),t.max_lazy_match=u[t.level].max_lazy,t.good_match=u[t.level].good_length,t.nice_match=u[t.level].nice_length,t.max_chain_length=u[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=2,t.match_available=0,t.ins_h=0),r}function ns(e,t,r,n,i,a){if(!e)return -2;var s=1;if(-1===t&&(t=6),n<0?(s=0,n=-n):n>15&&(s=2,n-=16),i<1||i>9||8!==r||n<8||n>15||t<0||t>9||a<0||a>4)return r1(e,-2);8===n&&(n=9);var o=new nn;return e.state=o,o.strm=e,o.wrap=s,o.gzhead=null,o.w_bits=n,o.w_size=1<<o.w_bits,o.w_mask=o.w_size-1,o.hash_bits=i+7,o.hash_size=1<<o.hash_bits,o.hash_mask=o.hash_size-1,o.hash_shift=~~((o.hash_bits+3-1)/3),o.window=new rm.Buf8(2*o.w_size),o.head=new rm.Buf16(o.hash_size),o.prev=new rm.Buf16(o.w_size),o.lit_bufsize=1<<i+6,o.pending_buf_size=4*o.lit_bufsize,o.pending_buf=new rm.Buf8(o.pending_buf_size),o.d_buf=+o.lit_bufsize,o.l_buf=3*o.lit_bufsize,o.level=t,o.strategy=a,o.method=r,na(e)}u=[new nr(0,0,0,0,function(e,t){var r=65535;for(65535>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(r7(e),0===e.lookahead&&0===t)return 1;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var n=e.block_start+r;if((0===e.strstart||e.strstart>=n)&&(e.lookahead=e.strstart-n,e.strstart=n,r6(e,!1),0===e.strm.avail_out)||e.strstart-e.block_start>=e.w_size-262&&(r6(e,!1),0===e.strm.avail_out))return 1}return(e.insert=0,4===t)?(r6(e,!0),0===e.strm.avail_out)?3:4:(e.strstart>e.block_start&&(r6(e,!1),e.strm.avail_out),1)}),new nr(4,4,8,4,ne),new nr(4,5,16,8,ne),new nr(4,6,32,32,ne),new nr(4,4,16,16,nt),new nr(8,16,32,32,nt),new nr(8,16,128,128,nt),new nr(8,32,128,256,nt),new nr(32,128,258,1024,nt),new nr(32,258,258,4096,nt)],rb.deflateInit=function(e,t){return ns(e,t,8,15,8,0)},rb.deflateInit2=ns,rb.deflateReset=na,rb.deflateResetKeep=ni,rb.deflateSetHeader=function(e,t){return e&&e.state&&2===e.state.wrap?(e.state.gzhead=t,0):-2},rb.deflate=function(e,t){if(!e||!e.state||t>5||t<0)return e?r1(e,-2):-2;if(n=e.state,!e.output||!e.input&&0!==e.avail_in||666===n.status&&4!==t)return r1(e,0===e.avail_out?-5:-2);if(n.strm=e,r=n.last_flush,n.last_flush=t,42===n.status)if(2===n.wrap)e.adler=0,r8(n,31),r8(n,139),r8(n,8),n.gzhead?(r8(n,+!!n.gzhead.text+2*!!n.gzhead.hcrc+4*!!n.gzhead.extra+8*!!n.gzhead.name+16*!!n.gzhead.comment),r8(n,255&n.gzhead.time),r8(n,n.gzhead.time>>8&255),r8(n,n.gzhead.time>>16&255),r8(n,n.gzhead.time>>24&255),r8(n,9===n.level?2:4*(n.strategy>=2||n.level<2)),r8(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&(r8(n,255&n.gzhead.extra.length),r8(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(e.adler=r$(e.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=69):(r8(n,0),r8(n,0),r8(n,0),r8(n,0),r8(n,0),r8(n,9===n.level?2:4*(n.strategy>=2||n.level<2)),r8(n,3),n.status=113);else{var r,n,i,a,s=8+(n.w_bits-8<<4)<<8,o=-1;s|=(n.strategy>=2||n.level<2?0:n.level<6?1:6===n.level?2:3)<<6,0!==n.strstart&&(s|=32),s+=31-s%31,n.status=113,r4(n,s),0!==n.strstart&&(r4(n,e.adler>>>16),r4(n,65535&e.adler)),e.adler=1}if(69===n.status)if(n.gzhead.extra){for(i=n.pending;n.gzindex<(65535&n.gzhead.extra.length)&&(n.pending!==n.pending_buf_size||(n.gzhead.hcrc&&n.pending>i&&(e.adler=r$(e.adler,n.pending_buf,n.pending-i,i)),r3(e),i=n.pending,n.pending!==n.pending_buf_size));)r8(n,255&n.gzhead.extra[n.gzindex]),n.gzindex++;n.gzhead.hcrc&&n.pending>i&&(e.adler=r$(e.adler,n.pending_buf,n.pending-i,i)),n.gzindex===n.gzhead.extra.length&&(n.gzindex=0,n.status=73)}else n.status=73;if(73===n.status)if(n.gzhead.name){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(e.adler=r$(e.adler,n.pending_buf,n.pending-i,i)),r3(e),i=n.pending,n.pending===n.pending_buf_size)){a=1;break}a=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0,r8(n,a)}while(0!==a);n.gzhead.hcrc&&n.pending>i&&(e.adler=r$(e.adler,n.pending_buf,n.pending-i,i)),0===a&&(n.gzindex=0,n.status=91)}else n.status=91;if(91===n.status)if(n.gzhead.comment){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(e.adler=r$(e.adler,n.pending_buf,n.pending-i,i)),r3(e),i=n.pending,n.pending===n.pending_buf_size)){a=1;break}a=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0,r8(n,a)}while(0!==a);n.gzhead.hcrc&&n.pending>i&&(e.adler=r$(e.adler,n.pending_buf,n.pending-i,i)),0===a&&(n.status=103)}else n.status=103;if(103===n.status&&(n.gzhead.hcrc?(n.pending+2>n.pending_buf_size&&r3(e),n.pending+2<=n.pending_buf_size&&(r8(n,255&e.adler),r8(n,e.adler>>8&255),e.adler=0,n.status=113)):n.status=113),0!==n.pending){if(r3(e),0===e.avail_out)return n.last_flush=-1,0}else if(0===e.avail_in&&r2(t)<=r2(r)&&4!==t)return r1(e,-5);if(666===n.status&&0!==e.avail_in)return r1(e,-5);if(0!==e.avail_in||0!==n.lookahead||0!==t&&666!==n.status){var h=2===n.strategy?function(e,t){for(var r;;){if(0===e.lookahead&&(r7(e),0===e.lookahead)){if(0===t)return 1;break}if(e.match_length=0,r=rv._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,r&&(r6(e,!1),0===e.strm.avail_out))return 1}return(e.insert=0,4===t)?(r6(e,!0),0===e.strm.avail_out)?3:4:e.last_lit&&(r6(e,!1),0===e.strm.avail_out)?1:2}(n,t):3===n.strategy?function(e,t){for(var r,n,i,a,s=e.window;;){if(e.lookahead<=258){if(r7(e),e.lookahead<=258&&0===t)return 1;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=3&&e.strstart>0&&(n=s[i=e.strstart-1])===s[++i]&&n===s[++i]&&n===s[++i]){a=e.strstart+258;do;while(n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&n===s[++i]&&i<a);e.match_length=258-(a-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=3?(r=rv._tr_tally(e,1,e.match_length-3),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(r=rv._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),r&&(r6(e,!1),0===e.strm.avail_out))return 1}return(e.insert=0,4===t)?(r6(e,!0),0===e.strm.avail_out)?3:4:e.last_lit&&(r6(e,!1),0===e.strm.avail_out)?1:2}(n,t):u[n.level].func(n,t);if((3===h||4===h)&&(n.status=666),1===h||3===h)return 0===e.avail_out&&(n.last_flush=-1),0;if(2===h&&(1===t?rv._tr_align(n):5!==t&&(rv._tr_stored_block(n,0,0,!1),3===t&&(r5(n.head),0===n.lookahead&&(n.strstart=0,n.block_start=0,n.insert=0))),r3(e),0===e.avail_out))return n.last_flush=-1,0}return 4!==t?0:n.wrap<=0?1:(2===n.wrap?(r8(n,255&e.adler),r8(n,e.adler>>8&255),r8(n,e.adler>>16&255),r8(n,e.adler>>24&255),r8(n,255&e.total_in),r8(n,e.total_in>>8&255),r8(n,e.total_in>>16&255),r8(n,e.total_in>>24&255)):(r4(n,e.adler>>>16),r4(n,65535&e.adler)),r3(e),n.wrap>0&&(n.wrap=-n.wrap),+(0===n.pending))},rb.deflateEnd=function(e){var t;return e&&e.state?42!==(t=e.state.status)&&69!==t&&73!==t&&91!==t&&103!==t&&113!==t&&666!==t?r1(e,-2):(e.state=null,113===t?r1(e,-3):0):-2},rb.deflateSetDictionary=function(e,t){var r,n,i,a,s,o,h,l,f=t.length;if(!e||!e.state||2===(a=(r=e.state).wrap)||1===a&&42!==r.status||r.lookahead)return -2;for(1===a&&(e.adler=rX(e.adler,t,f,0)),r.wrap=0,f>=r.w_size&&(0===a&&(r5(r.head),r.strstart=0,r.block_start=0,r.insert=0),l=new rm.Buf8(r.w_size),rm.arraySet(l,t,f-r.w_size,r.w_size,0),t=l,f=r.w_size),s=e.avail_in,o=e.next_in,h=e.input,e.avail_in=f,e.next_in=0,e.input=t,r7(r);r.lookahead>=3;){n=r.strstart,i=r.lookahead-2;do r.ins_h=(r.ins_h<<r.hash_shift^r.window[n+3-1])&r.hash_mask,r.prev[n&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=n,n++;while(--i);r.strstart=n,r.lookahead=2,r7(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=2,r.match_available=0,e.next_in=o,e.input=h,e.avail_in=s,r.wrap=a,0},rb.deflateInfo="pako deflate (from Nodeca project)";var no={},nh=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],nl=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],nf=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],nu=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64],nc=function(e,t){var r,n,i,a,s,o,h,l,f,u,c,d,p,_,g,b,m,v,w,y,E,k,R,S,x;r=e.state,n=e.next_in,S=e.input,i=n+(e.avail_in-5),a=e.next_out,x=e.output,s=a-(t-e.avail_out),o=a+(e.avail_out-257),h=r.dmax,l=r.wsize,f=r.whave,u=r.wnext,c=r.window,d=r.hold,p=r.bits,_=r.lencode,g=r.distcode,b=(1<<r.lenbits)-1,m=(1<<r.distbits)-1;e:do for(p<15&&(d+=S[n++]<<p,p+=8,d+=S[n++]<<p,p+=8),v=_[d&b];;){if(d>>>=w=v>>>24,p-=w,0==(w=v>>>16&255))x[a++]=65535&v;else if(16&w)for(y=65535&v,(w&=15)&&(p<w&&(d+=S[n++]<<p,p+=8),y+=d&(1<<w)-1,d>>>=w,p-=w),p<15&&(d+=S[n++]<<p,p+=8,d+=S[n++]<<p,p+=8),v=g[d&m];;){if(d>>>=w=v>>>24,p-=w,16&(w=v>>>16&255)){if(E=65535&v,p<(w&=15)&&(d+=S[n++]<<p,(p+=8)<w&&(d+=S[n++]<<p,p+=8)),(E+=d&(1<<w)-1)>h){e.msg="invalid distance too far back",r.mode=30;break e}if(d>>>=w,p-=w,E>(w=a-s)){if((w=E-w)>f&&r.sane){e.msg="invalid distance too far back",r.mode=30;break e}if(k=0,R=c,0===u){if(k+=l-w,w<y){y-=w;do x[a++]=c[k++];while(--w);k=a-E,R=x}}else if(u<w){if(k+=l+u-w,(w-=u)<y){y-=w;do x[a++]=c[k++];while(--w);if(k=0,u<y){y-=w=u;do x[a++]=c[k++];while(--w);k=a-E,R=x}}}else if(k+=u-w,w<y){y-=w;do x[a++]=c[k++];while(--w);k=a-E,R=x}for(;y>2;)x[a++]=R[k++],x[a++]=R[k++],x[a++]=R[k++],y-=3;y&&(x[a++]=R[k++],y>1&&(x[a++]=R[k++]))}else{k=a-E;do x[a++]=x[k++],x[a++]=x[k++],x[a++]=x[k++],y-=3;while(y>2);y&&(x[a++]=x[k++],y>1&&(x[a++]=x[k++]))}}else if((64&w)==0){v=g[(65535&v)+(d&(1<<w)-1)];continue}else{e.msg="invalid distance code",r.mode=30;break e}break}else if((64&w)==0){v=_[(65535&v)+(d&(1<<w)-1)];continue}else if(32&w){r.mode=12;break e}else{e.msg="invalid literal/length code",r.mode=30;break e}break}while(n<i&&a<o);n-=y=p>>3,p-=y<<3,d&=(1<<p)-1,e.next_in=n,e.next_out=a,e.avail_in=n<i?5+(i-n):5-(n-i),e.avail_out=a<o?257+(o-a):257-(a-o),r.hold=d,r.bits=p},nd=function(e,t,r,n,i,a,s,o){var h,l,f,u,c,d,p,_,g,b=o.bits,m=0,v=0,w=0,y=0,E=0,k=0,R=0,S=0,x=0,A=0,L=null,T=0,O=new rm.Buf16(16),z=new rm.Buf16(16),I=null,D=0;for(m=0;m<=15;m++)O[m]=0;for(v=0;v<n;v++)O[t[r+v]]++;for(y=15,E=b;y>=1&&0===O[y];y--);if(E>y&&(E=y),0===y)return i[a++]=0x1400000,i[a++]=0x1400000,o.bits=1,0;for(w=1;w<y&&0===O[w];w++);for(E<w&&(E=w),S=1,m=1;m<=15;m++)if(S<<=1,(S-=O[m])<0)return -1;if(S>0&&(0===e||1!==y))return -1;for(m=1,z[1]=0;m<15;m++)z[m+1]=z[m]+O[m];for(v=0;v<n;v++)0!==t[r+v]&&(s[z[t[r+v]]++]=v);if(0===e?(L=I=s,d=19):1===e?(L=nh,T-=257,I=nl,D-=257,d=256):(L=nf,I=nu,d=-1),A=0,v=0,m=w,c=a,k=E,R=0,f=-1,u=(x=1<<E)-1,1===e&&x>852||2===e&&x>592)return 1;for(;;){p=m-R,s[v]<d?(_=0,g=s[v]):s[v]>d?(_=I[D+s[v]],g=L[T+s[v]]):(_=96,g=0),h=1<<m-R,w=l=1<<k;do i[c+(A>>R)+(l-=h)]=p<<24|_<<16|g;while(0!==l);for(h=1<<m-1;A&h;)h>>=1;if(0!==h?(A&=h-1,A+=h):A=0,v++,0==--O[m]){if(m===y)break;m=t[r+s[v]]}if(m>E&&(A&u)!==f){for(0===R&&(R=E),c+=w,S=1<<(k=m-R);k+R<y&&!((S-=O[k+R])<=0);)k++,S<<=1;if(x+=1<<k,1===e&&x>852||2===e&&x>592)return 1;i[f=A&u]=E<<24|k<<16|c-a}}return 0!==A&&(i[c+A]=m-R<<24|4194304),o.bits=E,0};function np(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function n_(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new rm.Buf16(320),this.work=new rm.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function ng(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=1,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new rm.Buf32(852),t.distcode=t.distdyn=new rm.Buf32(592),t.sane=1,t.back=-1,0):-2}function nb(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,ng(e)):-2}function nm(e,t){var r,n;return e&&e.state?(n=e.state,t<0?(r=0,t=-t):(r=(t>>4)+1,t<48&&(t&=15)),t&&(t<8||t>15))?-2:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,nb(e)):-2}function nv(e,t){var r,n;return e?(e.state=n=new n_,n.window=null,0!==(r=nm(e,t))&&(e.state=null),r):-2}var nw=!0;function ny(e,t,r,n){var i,a=e.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new rm.Buf8(a.wsize)),n>=a.wsize?(rm.arraySet(a.window,t,r-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):((i=a.wsize-a.wnext)>n&&(i=n),rm.arraySet(a.window,t,r-n,i,a.wnext),(n-=i)?(rm.arraySet(a.window,t,r-n,n,0),a.wnext=n,a.whave=a.wsize):(a.wnext+=i,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=i))),0}no.inflateReset=nb,no.inflateReset2=nm,no.inflateResetKeep=ng,no.inflateInit=function(e){return nv(e,15)},no.inflateInit2=nv,no.inflate=function(e,t){var r,n,i,a,s,o,h,l,f,u,p,_,g,b,m,v,w,y,E,k,R,S,x,A,L,T=0,O=new rm.Buf8(4),z=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return -2;12===(n=e.state).mode&&(n.mode=13),o=e.next_out,a=e.output,l=e.avail_out,s=e.next_in,i=e.input,h=e.avail_in,f=n.hold,u=n.bits,p=h,_=l,x=0;t:for(;;)switch(n.mode){case 1:if(0===n.wrap){n.mode=13;break}for(;u<16;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}if(2&n.wrap&&35615===f){n.check=0,O[0]=255&f,O[1]=f>>>8&255,n.check=r$(n.check,O,2,0),f=0,u=0,n.mode=2;break}if(n.flags=0,n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&f)<<8)+(f>>8))%31){e.msg="incorrect header check",n.mode=30;break}if((15&f)!=8){e.msg="unknown compression method",n.mode=30;break}if(f>>>=4,u-=4,S=(15&f)+8,0===n.wbits)n.wbits=S;else if(S>n.wbits){e.msg="invalid window size",n.mode=30;break}n.dmax=1<<S,e.adler=n.check=1,n.mode=512&f?10:12,f=0,u=0;break;case 2:for(;u<16;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}if(n.flags=f,(255&n.flags)!=8){e.msg="unknown compression method",n.mode=30;break}if(57344&n.flags){e.msg="unknown header flags set",n.mode=30;break}n.head&&(n.head.text=f>>8&1),512&n.flags&&(O[0]=255&f,O[1]=f>>>8&255,n.check=r$(n.check,O,2,0)),f=0,u=0,n.mode=3;case 3:for(;u<32;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}n.head&&(n.head.time=f),512&n.flags&&(O[0]=255&f,O[1]=f>>>8&255,O[2]=f>>>16&255,O[3]=f>>>24&255,n.check=r$(n.check,O,4,0)),f=0,u=0,n.mode=4;case 4:for(;u<16;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}n.head&&(n.head.xflags=255&f,n.head.os=f>>8),512&n.flags&&(O[0]=255&f,O[1]=f>>>8&255,n.check=r$(n.check,O,2,0)),f=0,u=0,n.mode=5;case 5:if(1024&n.flags){for(;u<16;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}n.length=f,n.head&&(n.head.extra_len=f),512&n.flags&&(O[0]=255&f,O[1]=f>>>8&255,n.check=r$(n.check,O,2,0)),f=0,u=0}else n.head&&(n.head.extra=null);n.mode=6;case 6:if(1024&n.flags&&((g=n.length)>h&&(g=h),g&&(n.head&&(S=n.head.extra_len-n.length,n.head.extra||(n.head.extra=Array(n.head.extra_len)),rm.arraySet(n.head.extra,i,s,g,S)),512&n.flags&&(n.check=r$(n.check,i,g,s)),h-=g,s+=g,n.length-=g),n.length))break t;n.length=0,n.mode=7;case 7:if(2048&n.flags){if(0===h)break t;g=0;do S=i[s+g++],n.head&&S&&n.length<65536&&(n.head.name+=String.fromCharCode(S));while(S&&g<h);if(512&n.flags&&(n.check=r$(n.check,i,g,s)),h-=g,s+=g,S)break t}else n.head&&(n.head.name=null);n.length=0,n.mode=8;case 8:if(4096&n.flags){if(0===h)break t;g=0;do S=i[s+g++],n.head&&S&&n.length<65536&&(n.head.comment+=String.fromCharCode(S));while(S&&g<h);if(512&n.flags&&(n.check=r$(n.check,i,g,s)),h-=g,s+=g,S)break t}else n.head&&(n.head.comment=null);n.mode=9;case 9:if(512&n.flags){for(;u<16;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}if(f!==(65535&n.check)){e.msg="header crc mismatch",n.mode=30;break}f=0,u=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),e.adler=n.check=0,n.mode=12;break;case 10:for(;u<32;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}e.adler=n.check=np(f),f=0,u=0,n.mode=11;case 11:if(0===n.havedict)return e.next_out=o,e.avail_out=l,e.next_in=s,e.avail_in=h,n.hold=f,n.bits=u,2;e.adler=n.check=1,n.mode=12;case 12:if(5===t||6===t)break t;case 13:if(n.last){f>>>=7&u,u-=7&u,n.mode=27;break}for(;u<3;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}switch(n.last=1&f,u-=1,3&(f>>>=1)){case 0:n.mode=14;break;case 1:if(nw){for(c=new rm.Buf32(512),d=new rm.Buf32(32),r=0;r<144;)n.lens[r++]=8;for(;r<256;)n.lens[r++]=9;for(;r<280;)n.lens[r++]=7;for(;r<288;)n.lens[r++]=8;for(nd(1,n.lens,0,288,c,0,n.work,{bits:9}),r=0;r<32;)n.lens[r++]=5;nd(2,n.lens,0,32,d,0,n.work,{bits:5}),nw=!1}if(n.lencode=c,n.lenbits=9,n.distcode=d,n.distbits=5,n.mode=20,6===t){f>>>=2,u-=2;break t}break;case 2:n.mode=17;break;case 3:e.msg="invalid block type",n.mode=30}f>>>=2,u-=2;break;case 14:for(f>>>=7&u,u-=7&u;u<32;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}if((65535&f)!=(f>>>16^65535)){e.msg="invalid stored block lengths",n.mode=30;break}if(n.length=65535&f,f=0,u=0,n.mode=15,6===t)break t;case 15:n.mode=16;case 16:if(g=n.length){if(g>h&&(g=h),g>l&&(g=l),0===g)break t;rm.arraySet(a,i,s,g,o),h-=g,s+=g,l-=g,o+=g,n.length-=g;break}n.mode=12;break;case 17:for(;u<14;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}if(n.nlen=(31&f)+257,u-=5,n.ndist=(31&(f>>>=5))+1,u-=5,n.ncode=(15&(f>>>=5))+4,f>>>=4,u-=4,n.nlen>286||n.ndist>30){e.msg="too many length or distance symbols",n.mode=30;break}n.have=0,n.mode=18;case 18:for(;n.have<n.ncode;){for(;u<3;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}n.lens[z[n.have++]]=7&f,f>>>=3,u-=3}for(;n.have<19;)n.lens[z[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,A={bits:n.lenbits},x=nd(0,n.lens,0,19,n.lencode,0,n.work,A),n.lenbits=A.bits,x){e.msg="invalid code lengths set",n.mode=30;break}n.have=0,n.mode=19;case 19:for(;n.have<n.nlen+n.ndist;){for(;v=(T=n.lencode[f&(1<<n.lenbits)-1])>>>24,w=T>>>16&255,y=65535&T,!(v<=u);){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}if(y<16)f>>>=v,u-=v,n.lens[n.have++]=y;else{if(16===y){for(L=v+2;u<L;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}if(f>>>=v,u-=v,0===n.have){e.msg="invalid bit length repeat",n.mode=30;break}S=n.lens[n.have-1],g=3+(3&f),f>>>=2,u-=2}else if(17===y){for(L=v+3;u<L;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}f>>>=v,u-=v,S=0,g=3+(7&f),f>>>=3,u-=3}else{for(L=v+7;u<L;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}f>>>=v,u-=v,S=0,g=11+(127&f),f>>>=7,u-=7}if(n.have+g>n.nlen+n.ndist){e.msg="invalid bit length repeat",n.mode=30;break}for(;g--;)n.lens[n.have++]=S}}if(30===n.mode)break;if(0===n.lens[256]){e.msg="invalid code -- missing end-of-block",n.mode=30;break}if(n.lenbits=9,A={bits:n.lenbits},x=nd(1,n.lens,0,n.nlen,n.lencode,0,n.work,A),n.lenbits=A.bits,x){e.msg="invalid literal/lengths set",n.mode=30;break}if(n.distbits=6,n.distcode=n.distdyn,A={bits:n.distbits},x=nd(2,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,A),n.distbits=A.bits,x){e.msg="invalid distances set",n.mode=30;break}if(n.mode=20,6===t)break t;case 20:n.mode=21;case 21:if(h>=6&&l>=258){e.next_out=o,e.avail_out=l,e.next_in=s,e.avail_in=h,n.hold=f,n.bits=u,nc(e,_),o=e.next_out,a=e.output,l=e.avail_out,s=e.next_in,i=e.input,h=e.avail_in,f=n.hold,u=n.bits,12===n.mode&&(n.back=-1);break}for(n.back=0;v=(T=n.lencode[f&(1<<n.lenbits)-1])>>>24,w=T>>>16&255,y=65535&T,!(v<=u);){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}if(w&&(240&w)==0){for(E=v,k=w,R=y;v=(T=n.lencode[R+((f&(1<<E+k)-1)>>E)])>>>24,w=T>>>16&255,y=65535&T,!(E+v<=u);){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}f>>>=E,u-=E,n.back+=E}if(f>>>=v,u-=v,n.back+=v,n.length=y,0===w){n.mode=26;break}if(32&w){n.back=-1,n.mode=12;break}if(64&w){e.msg="invalid literal/length code",n.mode=30;break}n.extra=15&w,n.mode=22;case 22:if(n.extra){for(L=n.extra;u<L;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}n.length+=f&(1<<n.extra)-1,f>>>=n.extra,u-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=23;case 23:for(;v=(T=n.distcode[f&(1<<n.distbits)-1])>>>24,w=T>>>16&255,y=65535&T,!(v<=u);){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}if((240&w)==0){for(E=v,k=w,R=y;v=(T=n.distcode[R+((f&(1<<E+k)-1)>>E)])>>>24,w=T>>>16&255,y=65535&T,!(E+v<=u);){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}f>>>=E,u-=E,n.back+=E}if(f>>>=v,u-=v,n.back+=v,64&w){e.msg="invalid distance code",n.mode=30;break}n.offset=y,n.extra=15&w,n.mode=24;case 24:if(n.extra){for(L=n.extra;u<L;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}n.offset+=f&(1<<n.extra)-1,f>>>=n.extra,u-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){e.msg="invalid distance too far back",n.mode=30;break}n.mode=25;case 25:if(0===l)break t;if(g=_-l,n.offset>g){if((g=n.offset-g)>n.whave&&n.sane){e.msg="invalid distance too far back",n.mode=30;break}g>n.wnext?(g-=n.wnext,b=n.wsize-g):b=n.wnext-g,g>n.length&&(g=n.length),m=n.window}else m=a,b=o-n.offset,g=n.length;g>l&&(g=l),l-=g,n.length-=g;do a[o++]=m[b++];while(--g);0===n.length&&(n.mode=21);break;case 26:if(0===l)break t;a[o++]=n.length,l--,n.mode=21;break;case 27:if(n.wrap){for(;u<32;){if(0===h)break t;h--,f|=i[s++]<<u,u+=8}if(_-=l,e.total_out+=_,n.total+=_,_&&(e.adler=n.check=n.flags?r$(n.check,a,_,o-_):rX(n.check,a,_,o-_)),_=l,(n.flags?f:np(f))!==n.check){e.msg="incorrect data check",n.mode=30;break}f=0,u=0}n.mode=28;case 28:if(n.wrap&&n.flags){for(;u<32;){if(0===h)break t;h--,f+=i[s++]<<u,u+=8}if(f!==(0|n.total)){e.msg="incorrect length check",n.mode=30;break}f=0,u=0}n.mode=29;case 29:x=1;break t;case 30:x=-3;break t;case 31:return -4;default:return -2}return e.next_out=o,e.avail_out=l,e.next_in=s,e.avail_in=h,n.hold=f,n.bits=u,(n.wsize||_!==e.avail_out&&n.mode<30&&(n.mode<27||4!==t))&&ny(e,e.output,e.next_out,_-e.avail_out),p-=e.avail_in,_-=e.avail_out,e.total_in+=p,e.total_out+=_,n.total+=_,n.wrap&&_&&(e.adler=n.check=n.flags?r$(n.check,a,_,e.next_out-_):rX(n.check,a,_,e.next_out-_)),e.data_type=n.bits+64*!!n.last+128*(12===n.mode)+256*(20===n.mode||15===n.mode),(0===p&&0===_||4===t)&&0===x&&(x=-5),x},no.inflateEnd=function(e){if(!e||!e.state)return -2;var t=e.state;return t.window&&(t.window=null),e.state=null,0},no.inflateGetHeader=function(e,t){var r;return e&&e.state&&(2&(r=e.state).wrap)!=0?(r.head=t,t.done=!1,0):-2},no.inflateSetDictionary=function(e,t){var r,n,i=t.length;return e&&e.state&&(0===(r=e.state).wrap||11===r.mode)?11===r.mode&&rX(1,t,i,0)!==r.check?-3:ny(e,t,i,i)?(r.mode=31,-4):(r.havedict=1,0):-2},no.inflateInfo="pako inflate (from Nodeca project)";var nE={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8};!function(e){for(var t in nE)e[t]=nE[t];function r(t){if("number"!=typeof t||t<e.DEFLATE||t>e.UNZIP)throw TypeError("Bad argument");this.dictionary=null,this.err=0,this.flush=0,this.init_done=!1,this.level=0,this.memLevel=0,this.mode=t,this.strategy=0,this.windowBits=0,this.write_in_progress=!1,this.pending_close=!1,this.gzip_id_bytes_read=0}e.NONE=0,e.DEFLATE=1,e.INFLATE=2,e.GZIP=3,e.GUNZIP=4,e.DEFLATERAW=5,e.INFLATERAW=6,e.UNZIP=7,r.prototype.close=function(){if(this.write_in_progress){this.pending_close=!0;return}this.pending_close=!1,r_(this.init_done,"close before init"),r_(this.mode<=e.UNZIP),this.mode===e.DEFLATE||this.mode===e.GZIP||this.mode===e.DEFLATERAW?rb.deflateEnd(this.strm):(this.mode===e.INFLATE||this.mode===e.GUNZIP||this.mode===e.INFLATERAW||this.mode===e.UNZIP)&&no.inflateEnd(this.strm),this.mode=e.NONE,this.dictionary=null},r.prototype.write=function(e,t,r,n,i,a,s){return this._write(!0,e,t,r,n,i,a,s)},r.prototype.writeSync=function(e,t,r,n,i,a,s){return this._write(!1,e,t,r,n,i,a,s)},r.prototype._write=function(t,r,n,i,a,s,o,h){if(r_.equal(arguments.length,8),r_(this.init_done,"write before init"),r_(this.mode!==e.NONE,"already finalized"),r_.equal(!1,this.write_in_progress,"write already in progress"),r_.equal(!1,this.pending_close,"close is pending"),this.write_in_progress=!0,r_.equal(!1,void 0===r,"must provide flush value"),this.write_in_progress=!0,r!==e.Z_NO_FLUSH&&r!==e.Z_PARTIAL_FLUSH&&r!==e.Z_SYNC_FLUSH&&r!==e.Z_FULL_FLUSH&&r!==e.Z_FINISH&&r!==e.Z_BLOCK)throw Error("Invalid flush value");if(null==n&&(n=L.alloc(0),a=0,i=0),this.strm.avail_in=a,this.strm.input=n,this.strm.next_in=i,this.strm.avail_out=h,this.strm.output=s,this.strm.next_out=o,this.flush=r,!t)return(this._process(),this._checkError())?this._afterSync():void 0;var l=this;return em.nextTick(function(){l._process(),l._after()}),this},r.prototype._afterSync=function(){var e=this.strm.avail_out,t=this.strm.avail_in;return this.write_in_progress=!1,[t,e]},r.prototype._process=function(){var t=null;switch(this.mode){case e.DEFLATE:case e.GZIP:case e.DEFLATERAW:this.err=rb.deflate(this.strm,this.flush);break;case e.UNZIP:switch(this.strm.avail_in>0&&(t=this.strm.next_in),this.gzip_id_bytes_read){case 0:if(null===t)break;if(31===this.strm.input[t]){if(this.gzip_id_bytes_read=1,t++,1===this.strm.avail_in)break}else{this.mode=e.INFLATE;break}case 1:if(null===t)break;139===this.strm.input[t]?(this.gzip_id_bytes_read=2,this.mode=e.GUNZIP):this.mode=e.INFLATE;break;default:throw Error("invalid number of gzip magic number bytes read")}case e.INFLATE:case e.GUNZIP:case e.INFLATERAW:for(this.err=no.inflate(this.strm,this.flush),this.err===e.Z_NEED_DICT&&this.dictionary&&(this.err=no.inflateSetDictionary(this.strm,this.dictionary),this.err===e.Z_OK?this.err=no.inflate(this.strm,this.flush):this.err===e.Z_DATA_ERROR&&(this.err=e.Z_NEED_DICT));this.strm.avail_in>0&&this.mode===e.GUNZIP&&this.err===e.Z_STREAM_END&&0!==this.strm.next_in[0];)this.reset(),this.err=no.inflate(this.strm,this.flush);break;default:throw Error("Unknown mode "+this.mode)}},r.prototype._checkError=function(){switch(this.err){case e.Z_OK:case e.Z_BUF_ERROR:if(0!==this.strm.avail_out&&this.flush===e.Z_FINISH)return this._error("unexpected end of file"),!1;break;case e.Z_STREAM_END:break;case e.Z_NEED_DICT:return null==this.dictionary?this._error("Missing dictionary"):this._error("Bad dictionary"),!1;default:return this._error("Zlib error"),!1}return!0},r.prototype._after=function(){if(this._checkError()){var e=this.strm.avail_out,t=this.strm.avail_in;this.write_in_progress=!1,this.callback(t,e),this.pending_close&&this.close()}},r.prototype._error=function(e){this.strm.msg&&(e=this.strm.msg),this.onerror(e,this.err),this.write_in_progress=!1,this.pending_close&&this.close()},r.prototype.init=function(t,r,n,i,a){r_(4==arguments.length||5==arguments.length,"init(windowBits, level, memLevel, strategy, [dictionary])"),r_(t>=8&&t<=15,"invalid windowBits"),r_(r>=-1&&r<=9,"invalid compression level"),r_(n>=1&&n<=9,"invalid memlevel"),r_(i===e.Z_FILTERED||i===e.Z_HUFFMAN_ONLY||i===e.Z_RLE||i===e.Z_FIXED||i===e.Z_DEFAULT_STRATEGY,"invalid strategy"),this._init(r,t,n,i,a),this._setDictionary()},r.prototype.params=function(){throw Error("deflateParams Not supported")},r.prototype.reset=function(){this._reset(),this._setDictionary()},r.prototype._init=function(t,r,n,i,a){switch(this.level=t,this.windowBits=r,this.memLevel=n,this.strategy=i,this.flush=e.Z_NO_FLUSH,this.err=e.Z_OK,(this.mode===e.GZIP||this.mode===e.GUNZIP)&&(this.windowBits+=16),this.mode===e.UNZIP&&(this.windowBits+=32),(this.mode===e.DEFLATERAW||this.mode===e.INFLATERAW)&&(this.windowBits=-1*this.windowBits),this.strm=new rg,this.mode){case e.DEFLATE:case e.GZIP:case e.DEFLATERAW:this.err=rb.deflateInit2(this.strm,this.level,e.Z_DEFLATED,this.windowBits,this.memLevel,this.strategy);break;case e.INFLATE:case e.GUNZIP:case e.INFLATERAW:case e.UNZIP:this.err=no.inflateInit2(this.strm,this.windowBits);break;default:throw Error("Unknown mode "+this.mode)}this.err!==e.Z_OK&&this._error("Init error"),this.dictionary=a,this.write_in_progress=!1,this.init_done=!0},r.prototype._setDictionary=function(){if(null!=this.dictionary){switch(this.err=e.Z_OK,this.mode){case e.DEFLATE:case e.DEFLATERAW:this.err=rb.deflateSetDictionary(this.strm,this.dictionary)}this.err!==e.Z_OK&&this._error("Failed to set dictionary")}},r.prototype._reset=function(){switch(this.err=e.Z_OK,this.mode){case e.DEFLATE:case e.DEFLATERAW:case e.GZIP:this.err=rb.deflateReset(this.strm);break;case e.INFLATE:case e.INFLATERAW:case e.GUNZIP:this.err=no.inflateReset(this.strm)}this.err!==e.Z_OK&&this._error("Failed to reset stream")},e.Zlib=r}(tK);var nk=ev(tn);!function(e){var t=ey.Buffer,r=tG.Transform,n=r_.ok,i=ey.kMaxLength,a="Cannot create final Buffer. It would be larger than 0x"+i.toString(16)+" bytes";tK.Z_MIN_WINDOWBITS=8,tK.Z_MAX_WINDOWBITS=15,tK.Z_DEFAULT_WINDOWBITS=15,tK.Z_MIN_CHUNK=64,tK.Z_MAX_CHUNK=1/0,tK.Z_DEFAULT_CHUNK=16384,tK.Z_MIN_MEMLEVEL=1,tK.Z_MAX_MEMLEVEL=9,tK.Z_DEFAULT_MEMLEVEL=8,tK.Z_MIN_LEVEL=-1,tK.Z_MAX_LEVEL=9,tK.Z_DEFAULT_LEVEL=tK.Z_DEFAULT_COMPRESSION;for(var s=Object.keys(tK),o=0;o<s.length;o++){var h=s[o];h.match(/^Z/)&&Object.defineProperty(e,h,{enumerable:!0,value:tK[h],writable:!1})}for(var l={Z_OK:tK.Z_OK,Z_STREAM_END:tK.Z_STREAM_END,Z_NEED_DICT:tK.Z_NEED_DICT,Z_ERRNO:tK.Z_ERRNO,Z_STREAM_ERROR:tK.Z_STREAM_ERROR,Z_DATA_ERROR:tK.Z_DATA_ERROR,Z_MEM_ERROR:tK.Z_MEM_ERROR,Z_BUF_ERROR:tK.Z_BUF_ERROR,Z_VERSION_ERROR:tK.Z_VERSION_ERROR},f=Object.keys(l),u=0;u<f.length;u++){var c=f[u];l[l[c]]=c}function d(e,r,n){var s=[],o=0;function h(){for(var t;null!==(t=e.read());)s.push(t),o+=t.length;e.once("readable",h)}function l(){var r,h=null;o>=i?h=RangeError(a):r=t.concat(s,o),s=[],e.close(),n(h,r)}e.on("error",function(t){e.removeListener("end",l),e.removeListener("readable",h),n(t)}),e.on("end",l),e.end(r),h()}function p(e,r){if("string"==typeof r&&(r=t.from(r)),!t.isBuffer(r))throw TypeError("Not a string or buffer");var n=e._finishFlushFlag;return e._processChunk(r,n)}function _(e){if(!(this instanceof _))return new _(e);k.call(this,e,tK.DEFLATE)}function g(e){if(!(this instanceof g))return new g(e);k.call(this,e,tK.INFLATE)}function b(e){if(!(this instanceof b))return new b(e);k.call(this,e,tK.GZIP)}function m(e){if(!(this instanceof m))return new m(e);k.call(this,e,tK.GUNZIP)}function v(e){if(!(this instanceof v))return new v(e);k.call(this,e,tK.DEFLATERAW)}function w(e){if(!(this instanceof w))return new w(e);k.call(this,e,tK.INFLATERAW)}function y(e){if(!(this instanceof y))return new y(e);k.call(this,e,tK.UNZIP)}function E(e){return e===tK.Z_NO_FLUSH||e===tK.Z_PARTIAL_FLUSH||e===tK.Z_SYNC_FLUSH||e===tK.Z_FULL_FLUSH||e===tK.Z_FINISH||e===tK.Z_BLOCK}function k(n,i){var a=this;if(this._opts=n=n||{},this._chunkSize=n.chunkSize||e.Z_DEFAULT_CHUNK,r.call(this,n),n.flush&&!E(n.flush))throw Error("Invalid flush flag: "+n.flush);if(n.finishFlush&&!E(n.finishFlush))throw Error("Invalid flush flag: "+n.finishFlush);if(this._flushFlag=n.flush||tK.Z_NO_FLUSH,this._finishFlushFlag=void 0!==n.finishFlush?n.finishFlush:tK.Z_FINISH,n.chunkSize&&(n.chunkSize<e.Z_MIN_CHUNK||n.chunkSize>e.Z_MAX_CHUNK))throw Error("Invalid chunk size: "+n.chunkSize);if(n.windowBits&&(n.windowBits<e.Z_MIN_WINDOWBITS||n.windowBits>e.Z_MAX_WINDOWBITS))throw Error("Invalid windowBits: "+n.windowBits);if(n.level&&(n.level<e.Z_MIN_LEVEL||n.level>e.Z_MAX_LEVEL))throw Error("Invalid compression level: "+n.level);if(n.memLevel&&(n.memLevel<e.Z_MIN_MEMLEVEL||n.memLevel>e.Z_MAX_MEMLEVEL))throw Error("Invalid memLevel: "+n.memLevel);if(n.strategy&&n.strategy!=e.Z_FILTERED&&n.strategy!=e.Z_HUFFMAN_ONLY&&n.strategy!=e.Z_RLE&&n.strategy!=e.Z_FIXED&&n.strategy!=e.Z_DEFAULT_STRATEGY)throw Error("Invalid strategy: "+n.strategy);if(n.dictionary&&!t.isBuffer(n.dictionary))throw Error("Invalid dictionary: it should be a Buffer instance");this._handle=new tK.Zlib(i);var s=this;this._hadError=!1,this._handle.onerror=function(t,r){R(s),s._hadError=!0;var n=Error(t);n.errno=r,n.code=e.codes[r],s.emit("error",n)};var o=e.Z_DEFAULT_COMPRESSION;"number"==typeof n.level&&(o=n.level);var h=e.Z_DEFAULT_STRATEGY;"number"==typeof n.strategy&&(h=n.strategy),this._handle.init(n.windowBits||e.Z_DEFAULT_WINDOWBITS,o,n.memLevel||e.Z_DEFAULT_MEMLEVEL,h,n.dictionary),this._buffer=t.allocUnsafe(this._chunkSize),this._offset=0,this._level=o,this._strategy=h,this.once("end",this.close),Object.defineProperty(this,"_closed",{get:function(){return!a._handle},configurable:!0,enumerable:!0})}function R(e,t){t&&em.nextTick(t),e._handle&&(e._handle.close(),e._handle=null)}function S(e){e.emit("close")}Object.defineProperty(e,"codes",{enumerable:!0,value:Object.freeze(l),writable:!1}),e.Deflate=_,e.Inflate=g,e.Gzip=b,e.Gunzip=m,e.DeflateRaw=v,e.InflateRaw=w,e.Unzip=y,e.createDeflate=function(e){return new _(e)},e.createInflate=function(e){return new g(e)},e.createDeflateRaw=function(e){return new v(e)},e.createInflateRaw=function(e){return new w(e)},e.createGzip=function(e){return new b(e)},e.createGunzip=function(e){return new m(e)},e.createUnzip=function(e){return new y(e)},e.deflate=function(e,t,r){return"function"==typeof t&&(r=t,t={}),d(new _(t),e,r)},e.deflateSync=function(e,t){return p(new _(t),e)},e.gzip=function(e,t,r){return"function"==typeof t&&(r=t,t={}),d(new b(t),e,r)},e.gzipSync=function(e,t){return p(new b(t),e)},e.deflateRaw=function(e,t,r){return"function"==typeof t&&(r=t,t={}),d(new v(t),e,r)},e.deflateRawSync=function(e,t){return p(new v(t),e)},e.unzip=function(e,t,r){return"function"==typeof t&&(r=t,t={}),d(new y(t),e,r)},e.unzipSync=function(e,t){return p(new y(t),e)},e.inflate=function(e,t,r){return"function"==typeof t&&(r=t,t={}),d(new g(t),e,r)},e.inflateSync=function(e,t){return p(new g(t),e)},e.gunzip=function(e,t,r){return"function"==typeof t&&(r=t,t={}),d(new m(t),e,r)},e.gunzipSync=function(e,t){return p(new m(t),e)},e.inflateRaw=function(e,t,r){return"function"==typeof t&&(r=t,t={}),d(new w(t),e,r)},e.inflateRawSync=function(e,t){return p(new w(t),e)},nk.inherits(k,r),k.prototype.params=function(t,r,i){if(t<e.Z_MIN_LEVEL||t>e.Z_MAX_LEVEL)throw RangeError("Invalid compression level: "+t);if(r!=e.Z_FILTERED&&r!=e.Z_HUFFMAN_ONLY&&r!=e.Z_RLE&&r!=e.Z_FIXED&&r!=e.Z_DEFAULT_STRATEGY)throw TypeError("Invalid strategy: "+r);if(this._level!==t||this._strategy!==r){var a=this;this.flush(tK.Z_SYNC_FLUSH,function(){n(a._handle,"zlib binding closed"),a._handle.params(t,r),!a._hadError&&(a._level=t,a._strategy=r,i&&i())})}else em.nextTick(i)},k.prototype.reset=function(){return n(this._handle,"zlib binding closed"),this._handle.reset()},k.prototype._flush=function(e){this._transform(t.alloc(0),"",e)},k.prototype.flush=function(e,r){var n=this,i=this._writableState;"function"!=typeof e&&(void 0!==e||r)||(r=e,e=tK.Z_FULL_FLUSH),i.ended?r&&em.nextTick(r):i.ending?r&&this.once("end",r):i.needDrain?r&&this.once("drain",function(){return n.flush(e,r)}):(this._flushFlag=e,this.write(t.alloc(0),"",r))},k.prototype.close=function(e){R(this,e),em.nextTick(S,this)},k.prototype._transform=function(e,r,n){var i,a=this._writableState,s=(a.ending||a.ended)&&(!e||a.length===e.length);return null===e||t.isBuffer(e)?this._handle?void(s?i=this._finishFlushFlag:(i=this._flushFlag,e.length>=a.length&&(this._flushFlag=this._opts.flush||tK.Z_NO_FLUSH)),this._processChunk(e,i,n)):n(Error("zlib binding closed")):n(Error("invalid input"))},k.prototype._processChunk=function(e,r,s){var o=e&&e.length,h=this._chunkSize-this._offset,l=0,f=this,u="function"==typeof s;if(!u){var c,d=[],p=0;this.on("error",function(e){c=e}),n(this._handle,"zlib binding closed");do var _=this._handle.writeSync(r,e,l,o,this._buffer,this._offset,h);while(!this._hadError&&m(_[0],_[1]));if(this._hadError)throw c;if(p>=i)throw R(this),RangeError(a);var g=t.concat(d,p);return R(this),g}n(this._handle,"zlib binding closed");var b=this._handle.write(r,e,l,o,this._buffer,this._offset,h);function m(i,a){if(this&&(this.buffer=null,this.callback=null),!f._hadError){var c=h-a;if(n(c>=0,"have should not go down"),c>0){var _=f._buffer.slice(f._offset,f._offset+c);f._offset+=c,u?f.push(_):(d.push(_),p+=_.length)}if((0===a||f._offset>=f._chunkSize)&&(h=f._chunkSize,f._offset=0,f._buffer=t.allocUnsafe(f._chunkSize)),0===a){if(l+=o-i,o=i,!u)return!0;var g=f._handle.write(r,e,l,o,f._buffer,f._offset,f._chunkSize);return g.callback=m,void(g.buffer=e)}if(!u)return!1;s()}}b.buffer=e,b.callback=m},nk.inherits(_,k),nk.inherits(g,k),nk.inherits(b,k),nk.inherits(m,k),nk.inherits(v,k),nk.inherits(w,k),nk.inherits(y,k)}(ew);var nR=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(ew);class nS{static decode(e,t){throw Error("PNG.decode not available in browser build")}static load(e){throw Error("PNG.load not available in browser build")}constructor(e){let t;for(this.data=e,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.text={};;){let e=this.readUInt32(),o="";for(t=0;t<4;t++)o+=String.fromCharCode(this.data[this.pos++]);switch(o){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"PLTE":this.palette=this.read(e);break;case"IDAT":for(t=0;t<e;t++)this.imgData.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:this.transparency.indexed=this.read(e);var r=255-this.transparency.indexed.length;if(r>0)for(t=0;t<r;t++)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(e)[0];break;case 2:this.transparency.rgb=this.read(e)}break;case"tEXt":var n=this.read(e),i=n.indexOf(0),a=String.fromCharCode.apply(String,n.slice(0,i));this.text[a]=String.fromCharCode.apply(String,n.slice(i+1));break;case"IEND":switch(this.colorType){case 0:case 3:case 4:this.colors=1;break;case 2:case 6:this.colors=3}this.hasAlphaChannel=[4,6].includes(this.colorType);var s=this.colors+ +!!this.hasAlphaChannel;switch(this.pixelBitlength=this.bits*s,this.colors){case 1:this.colorSpace="DeviceGray";break;case 3:this.colorSpace="DeviceRGB"}this.imgData=L.from(this.imgData);return;default:this.pos+=e}if(this.pos+=4,this.pos>this.data.length)throw Error("Incomplete or corrupt PNG file")}}read(e){let t=Array(e);for(let r=0;r<e;r++)t[r]=this.data[this.pos++];return t}readUInt32(){let e=this.data[this.pos++]<<24,t=this.data[this.pos++]<<16;return e|t|this.data[this.pos++]<<8|this.data[this.pos++]}readUInt16(){return this.data[this.pos++]<<8|this.data[this.pos++]}decodePixels(e){return nR.inflate(this.imgData,(t,r)=>{if(t)throw t;var n=0;let{width:i,height:a}=this;var s=this.pixelBitlength/8;let o=L.alloc(i*a*s);function h(e,t,h,l,f){void 0===f&&(f=!1);let u=Math.ceil((i-e)/h),c=Math.ceil((a-t)/l),d=s*u,p=f?o:L.alloc(d*c),_=0,g=0;for(;_<c&&n<r.length;){var b,m,v,w,y,E,k;switch(r[n++]){case 0:for(v=0;v<d;v++)p[g++]=r[n++];break;case 1:for(v=0;v<d;v++)b=r[n++],w=v<s?0:p[g-s],p[g++]=(b+w)%256;break;case 2:for(v=0;v<d;v++)b=r[n++],m=(v-v%s)/s,y=_&&p[(_-1)*d+m*s+v%s],p[g++]=(y+b)%256;break;case 3:for(v=0;v<d;v++)b=r[n++],m=(v-v%s)/s,w=v<s?0:p[g-s],y=_&&p[(_-1)*d+m*s+v%s],p[g++]=(b+Math.floor((w+y)/2))%256;break;case 4:for(v=0;v<d;v++){b=r[n++],m=(v-v%s)/s,w=v<s?0:p[g-s],0===_?y=k=0:(y=p[(_-1)*d+m*s+v%s],k=m&&p[(_-1)*d+(m-1)*s+v%s]);let e=w+y-k,t=Math.abs(e-w),i=Math.abs(e-y),a=Math.abs(e-k);E=t<=i&&t<=a?w:i<=a?y:k,p[g++]=(b+E)%256}break;default:throw Error(`Invalid filter algorithm: ${r[n-1]}`)}if(!f){let r=((t+_*l)*i+e)*s,n=_*d;for(v=0;v<u;v++){for(let e=0;e<s;e++)o[r++]=p[n++];r+=(h-1)*s}}_++}}return 1===this.interlaceMethod?(h(0,0,8,8),h(4,0,8,8),h(0,4,4,8),h(2,0,4,4),h(0,2,2,4),h(1,0,2,2),h(0,1,1,2)):h(0,0,1,1,!0),e(o)})}decodePalette(){let{palette:e}=this,{length:t}=e,r=this.transparency.indexed||[],n=L.alloc(r.length+t),i=0,a=0;for(let o=0;o<t;o+=3){var s;n[i++]=e[o],n[i++]=e[o+1],n[i++]=e[o+2],n[i++]=null!=(s=r[a++])?s:255}return n}copyToImageData(e,t){var r;let n,{colors:i}=this,a=null,s=this.hasAlphaChannel;this.palette.length&&(a=this._decodedPalette||(this._decodedPalette=this.decodePalette()),i=4,s=!0);let o=e.data||e,{length:h}=o,l=a||t,f=n=0;if(1===i)for(;f<h;){r=a?4*t[f/4]:n;let e=l[r++];o[f++]=e,o[f++]=e,o[f++]=e,o[f++]=s?l[r++]:255,n=r}else for(;f<h;)r=a?4*t[f/4]:n,o[f++]=l[r++],o[f++]=l[r++],o[f++]=l[r++],o[f++]=s?l[r++]:255,n=r}decode(e){let t=L.alloc(this.width*this.height*4);return this.decodePixels(r=>(this.copyToImageData(t,r),e(t)))}}}}]);