{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\r\n  :root {\r\n    /* Modern Luxury Palette - Blue, Gray, Light Accents */\r\n    --background: 220 20% 95%; /* #EEF0F7 - Very light almost white-blue */\r\n    --foreground: 215 25% 27%; /* #36454F - Charcoal Gray */\r\n\r\n    --card: 220 20% 100%; /* #FFFFFF - White */\r\n    --card-foreground: 215 25% 27%; /* #36454F - Charcoal Gray */\r\n\r\n    --popover: 220 20% 100%; /* #FFFFFF - White */\r\n    --popover-foreground: 215 25% 27%; /* #36454F - Charcoal Gray */\r\n\r\n    --primary: 210 40% 50%; /* #4D6B99 - Medium Steel Blue */\r\n    --primary-foreground: 220 20% 98%; /* #F7F8FA - Very Light Gray for text on primary */\r\n\r\n    --secondary: 210 30% 75%; /* #AFC2D8 - Light Desaturated Blue/Gray */\r\n    --secondary-foreground: 215 25% 27%; /* #36454F - Charcoal Gray */\r\n\r\n    --muted: 220 20% 90%; /* #DADFEA - Light Gray-Blue */\r\n    --muted-foreground: 215 20% 45%; /* #65737E - Slate Gray */\r\n\r\n    --accent: 200 60% 60%; /* #66A3D9 - Soft Sky Blue */\r\n    --accent-foreground: 215 25% 27%; /* #36454F - Charcoal Gray */\r\n\r\n    --destructive: 0 72% 51%; /* #D94646 - Standard Red */\r\n    --destructive-foreground: 0 0% 100%; /* #FFFFFF - White */\r\n\r\n    --border: 220 20% 85%; /* #CCD3E0 - Light Gray-Blue Border */\r\n    --input: 220 20% 92%; /* #E3E7F0 - Very Light Gray-Blue Input */\r\n    --ring: 200 60% 70%; /* #85BDE0 - Lighter Sky Blue for focus rings */\r\n\r\n    --radius: 0.5rem;\r\n\r\n    /* Chart colors */\r\n    --chart-1: 210 40% 50%; /* Medium Steel Blue */\r\n    --chart-2: 200 60% 60%; /* Soft Sky Blue */\r\n    --chart-3: 210 30% 65%; /* Medium-Light Desaturated Blue */\r\n    --chart-4: 200 50% 75%; /* Lighter Sky Blue */\r\n    --chart-5: 215 25% 40%; /* Darker Charcoal/Slate */\r\n\r\n    /* Sidebar specific vars - kept distinct for potential separate styling */\r\n    --sidebar-background: 215 28% 22%; /* Dark Charcoal Blue #2A3B47 */\r\n    --sidebar-foreground: 220 20% 90%; /* #DADFEA - Light Gray-Blue */\r\n    --sidebar-primary: 200 60% 60%; /* Soft Sky Blue */\r\n    --sidebar-primary-foreground: 215 28% 15%; /* Very Dark Charcoal Blue #1C2830 */\r\n    --sidebar-accent: 200 60% 70%; /* Lighter Sky Blue */\r\n    --sidebar-accent-foreground: 215 28% 15%; /* Very Dark Charcoal Blue */\r\n    --sidebar-border: 215 28% 30%; /* Mid Charcoal Blue border #3B4E5A */\r\n    --sidebar-ring: 200 60% 60%; /* Soft Sky Blue */\r\n  }\r\n\r\n  .dark {\r\n    /* Modern Luxury Palette - Dark Mode */\r\n    --background: 215 28% 15%; /* #1C2830 - Very Dark Charcoal Blue */\r\n    --foreground: 220 20% 90%; /* #DADFEA - Light Gray-Blue */\r\n\r\n    --card: 215 28% 20%; /* #263640 - Dark Charcoal Blue */\r\n    --card-foreground: 220 20% 90%; /* #DADFEA - Light Gray-Blue */\r\n\r\n    --popover: 215 28% 18%; /* #21303A - Slightly Darker Charcoal Blue */\r\n    --popover-foreground: 220 20% 90%; /* #DADFEA - Light Gray-Blue */\r\n\r\n    --primary: 200 60% 60%; /* #66A3D9 - Soft Sky Blue */\r\n    --primary-foreground: 215 28% 10%; /* #111A1F - Near Black for text on primary */\r\n\r\n    --secondary: 215 25% 35%; /* #4A5B68 - Dark Slate Gray */\r\n    --secondary-foreground: 220 20% 90%; /* #DADFEA - Light Gray-Blue */\r\n\r\n    --muted: 215 28% 25%; /* #30414D - Muted Dark Blue/Gray */\r\n    --muted-foreground: 220 20% 70%; /* #AAB4C3 - Medium-Light Gray-Blue */\r\n\r\n    --accent: 200 50% 70%; /* #85B3D9 - Brighter Soft Sky Blue */\r\n    --accent-foreground: 215 28% 10%; /* #111A1F - Near Black */\r\n\r\n    --destructive: 0 65% 55%; /* #E05D5D - Slightly softer red */\r\n    --destructive-foreground: 0 0% 100%; /* #FFFFFF - White */\r\n\r\n    --border: 215 28% 28%; /* #384955 - Dark Gray-Blue Border */\r\n    --input: 215 28% 22%; /* #2A3B47 - Dark Charcoal Blue Input */\r\n    --ring: 200 50% 60%; /* #66A3D9 - Soft Sky Blue for focus rings */\r\n\r\n    /* Chart colors */\r\n    --chart-1: 200 60% 60%; /* Soft Sky Blue */\r\n    --chart-2: 220 20% 80%; /* Lighter Gray-Blue */\r\n    --chart-3: 200 50% 75%; /* Lighter Sky Blue */\r\n    --chart-4: 210 30% 75%; /* Light Desaturated Blue/Gray */\r\n    --chart-5: 200 70% 50%; /* Brighter Sky Blue */\r\n\r\n    /* Sidebar specific vars - kept distinct for potential separate styling */\r\n    --sidebar-background: 215 30% 12%; /* #151E25 - Even Darker Charcoal Blue */\r\n    --sidebar-foreground: 200 60% 70%; /* #85BDE0 - Lighter Sky Blue text */\r\n    --sidebar-primary: 200 60% 60%; /* Soft Sky Blue */\r\n    --sidebar-primary-foreground: 215 30% 8%; /* #0D1318 - Almost Black */\r\n    --sidebar-accent: 220 20% 90%; /* Light Gray-Blue */\r\n    --sidebar-accent-foreground: 215 30% 8%; /* #0D1318 - Almost Black */\r\n    --sidebar-border: 215 28% 20%; /* Dark Charcoal Blue */\r\n    --sidebar-ring: 200 60% 60%; /* Soft Sky Blue */\r\n  }\r\n  * {\r\n    border-color: hsl(var(--border));\r\n  }\r\n  body {\r\n    background-color: hsl(var(--background));\r\n    color: hsl(var(--foreground));\r\n  }\r\n.container {\n  width: 100%;\n  margin-right: auto;\n  margin-left: auto;\n  padding-right: 2rem;\n  padding-left: 2rem;\n}\r\n@media (min-width: 1400px) {\n\n  .container {\n    max-width: 1400px;\n  }\n}\r\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\r\n.pointer-events-none {\n  pointer-events: none;\n}\r\n.\\!visible {\n  visibility: visible !important;\n}\r\n.visible {\n  visibility: visible;\n}\r\n.invisible {\n  visibility: hidden;\n}\r\n.static {\n  position: static;\n}\r\n.fixed {\n  position: fixed;\n}\r\n.absolute {\n  position: absolute;\n}\r\n.relative {\n  position: relative;\n}\r\n.sticky {\n  position: sticky;\n}\r\n.inset-0 {\n  inset: 0px;\n}\r\n.inset-2 {\n  inset: 0.5rem;\n}\r\n.inset-4 {\n  inset: 1rem;\n}\r\n.inset-x-0 {\n  left: 0px;\n  right: 0px;\n}\r\n.inset-y-0 {\n  top: 0px;\n  bottom: 0px;\n}\r\n.-right-1 {\n  right: -0.25rem;\n}\r\n.-right-2 {\n  right: -0.5rem;\n}\r\n.-top-1 {\n  top: -0.25rem;\n}\r\n.-top-2 {\n  top: -0.5rem;\n}\r\n.bottom-0 {\n  bottom: 0px;\n}\r\n.bottom-2 {\n  bottom: 0.5rem;\n}\r\n.bottom-4 {\n  bottom: 1rem;\n}\r\n.bottom-6 {\n  bottom: 1.5rem;\n}\r\n.left-0 {\n  left: 0px;\n}\r\n.left-1 {\n  left: 0.25rem;\n}\r\n.left-2 {\n  left: 0.5rem;\n}\r\n.left-3 {\n  left: 0.75rem;\n}\r\n.left-4 {\n  left: 1rem;\n}\r\n.left-7 {\n  left: 1.75rem;\n}\r\n.left-\\[-10000px\\] {\n  left: -10000px;\n}\r\n.left-\\[50\\%\\] {\n  left: 50%;\n}\r\n.right-0 {\n  right: 0px;\n}\r\n.right-1 {\n  right: 0.25rem;\n}\r\n.right-2 {\n  right: 0.5rem;\n}\r\n.right-3 {\n  right: 0.75rem;\n}\r\n.right-4 {\n  right: 1rem;\n}\r\n.right-6 {\n  right: 1.5rem;\n}\r\n.top-0 {\n  top: 0px;\n}\r\n.top-1\\.5 {\n  top: 0.375rem;\n}\r\n.top-1\\/2 {\n  top: 50%;\n}\r\n.top-16 {\n  top: 4rem;\n}\r\n.top-2 {\n  top: 0.5rem;\n}\r\n.top-2\\.5 {\n  top: 0.625rem;\n}\r\n.top-3 {\n  top: 0.75rem;\n}\r\n.top-3\\.5 {\n  top: 0.875rem;\n}\r\n.top-4 {\n  top: 1rem;\n}\r\n.top-8 {\n  top: 2rem;\n}\r\n.top-9 {\n  top: 2.25rem;\n}\r\n.top-\\[-10000px\\] {\n  top: -10000px;\n}\r\n.top-\\[50\\%\\] {\n  top: 50%;\n}\r\n.top-\\[calc\\(50\\%_-_0\\.5rem_\\+_12px\\)\\] {\n  top: calc(50% - 0.5rem + 12px);\n}\r\n.z-10 {\n  z-index: 10;\n}\r\n.z-20 {\n  z-index: 20;\n}\r\n.z-50 {\n  z-index: 50;\n}\r\n.z-\\[100\\] {\n  z-index: 100;\n}\r\n.col-span-1 {\n  grid-column: span 1 / span 1;\n}\r\n.col-span-12 {\n  grid-column: span 12 / span 12;\n}\r\n.col-span-2 {\n  grid-column: span 2 / span 2;\n}\r\n.col-span-3 {\n  grid-column: span 3 / span 3;\n}\r\n.col-span-4 {\n  grid-column: span 4 / span 4;\n}\r\n.col-span-full {\n  grid-column: 1 / -1;\n}\r\n.row-span-2 {\n  grid-row: span 2 / span 2;\n}\r\n.-mx-1 {\n  margin-left: -0.25rem;\n  margin-right: -0.25rem;\n}\r\n.mx-2 {\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n}\r\n.mx-3\\.5 {\n  margin-left: 0.875rem;\n  margin-right: 0.875rem;\n}\r\n.mx-4 {\n  margin-left: 1rem;\n  margin-right: 1rem;\n}\r\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\r\n.my-0\\.5 {\n  margin-top: 0.125rem;\n  margin-bottom: 0.125rem;\n}\r\n.my-1 {\n  margin-top: 0.25rem;\n  margin-bottom: 0.25rem;\n}\r\n.my-2 {\n  margin-top: 0.5rem;\n  margin-bottom: 0.5rem;\n}\r\n.my-3 {\n  margin-top: 0.75rem;\n  margin-bottom: 0.75rem;\n}\r\n.my-4 {\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n}\r\n.my-6 {\n  margin-top: 1.5rem;\n  margin-bottom: 1.5rem;\n}\r\n.-mt-1 {\n  margin-top: -0.25rem;\n}\r\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\r\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\r\n.mb-3 {\n  margin-bottom: 0.75rem;\n}\r\n.mb-4 {\n  margin-bottom: 1rem;\n}\r\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\r\n.mb-8 {\n  margin-bottom: 2rem;\n}\r\n.ml-1 {\n  margin-left: 0.25rem;\n}\r\n.ml-1\\.5 {\n  margin-left: 0.375rem;\n}\r\n.ml-2 {\n  margin-left: 0.5rem;\n}\r\n.ml-3 {\n  margin-left: 0.75rem;\n}\r\n.ml-4 {\n  margin-left: 1rem;\n}\r\n.ml-6 {\n  margin-left: 1.5rem;\n}\r\n.ml-auto {\n  margin-left: auto;\n}\r\n.mr-0 {\n  margin-right: 0px;\n}\r\n.mr-1 {\n  margin-right: 0.25rem;\n}\r\n.mr-1\\.5 {\n  margin-right: 0.375rem;\n}\r\n.mr-2 {\n  margin-right: 0.5rem;\n}\r\n.mr-2\\.5 {\n  margin-right: 0.625rem;\n}\r\n.mr-3 {\n  margin-right: 0.75rem;\n}\r\n.mt-0 {\n  margin-top: 0px;\n}\r\n.mt-0\\.5 {\n  margin-top: 0.125rem;\n}\r\n.mt-1 {\n  margin-top: 0.25rem;\n}\r\n.mt-1\\.5 {\n  margin-top: 0.375rem;\n}\r\n.mt-10 {\n  margin-top: 2.5rem;\n}\r\n.mt-12 {\n  margin-top: 3rem;\n}\r\n.mt-2 {\n  margin-top: 0.5rem;\n}\r\n.mt-3 {\n  margin-top: 0.75rem;\n}\r\n.mt-4 {\n  margin-top: 1rem;\n}\r\n.mt-6 {\n  margin-top: 1.5rem;\n}\r\n.mt-8 {\n  margin-top: 2rem;\n}\r\n.line-clamp-1 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\r\n.line-clamp-2 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\r\n.line-clamp-3 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 3;\n}\r\n.block {\n  display: block;\n}\r\n.inline-block {\n  display: inline-block;\n}\r\n.inline {\n  display: inline;\n}\r\n.flex {\n  display: flex;\n}\r\n.inline-flex {\n  display: inline-flex;\n}\r\n.\\!table {\n  display: table !important;\n}\r\n.table {\n  display: table;\n}\r\n.grid {\n  display: grid;\n}\r\n.hidden {\n  display: none;\n}\r\n.aspect-\\[16\\/10\\] {\n  aspect-ratio: 16/10;\n}\r\n.aspect-\\[16\\/9\\] {\n  aspect-ratio: 16/9;\n}\r\n.aspect-\\[4\\/3\\] {\n  aspect-ratio: 4/3;\n}\r\n.aspect-square {\n  aspect-ratio: 1 / 1;\n}\r\n.aspect-video {\n  aspect-ratio: 16 / 9;\n}\r\n.size-1 {\n  width: 0.25rem;\n  height: 0.25rem;\n}\r\n.size-10 {\n  width: 2.5rem;\n  height: 2.5rem;\n}\r\n.size-12 {\n  width: 3rem;\n  height: 3rem;\n}\r\n.size-16 {\n  width: 4rem;\n  height: 4rem;\n}\r\n.size-2 {\n  width: 0.5rem;\n  height: 0.5rem;\n}\r\n.size-2\\.5 {\n  width: 0.625rem;\n  height: 0.625rem;\n}\r\n.size-20 {\n  width: 5rem;\n  height: 5rem;\n}\r\n.size-3 {\n  width: 0.75rem;\n  height: 0.75rem;\n}\r\n.size-3\\.5 {\n  width: 0.875rem;\n  height: 0.875rem;\n}\r\n.size-4 {\n  width: 1rem;\n  height: 1rem;\n}\r\n.size-5 {\n  width: 1.25rem;\n  height: 1.25rem;\n}\r\n.size-6 {\n  width: 1.5rem;\n  height: 1.5rem;\n}\r\n.size-7 {\n  width: 1.75rem;\n  height: 1.75rem;\n}\r\n.size-8 {\n  width: 2rem;\n  height: 2rem;\n}\r\n.size-\\[1\\.2rem\\] {\n  width: 1.2rem;\n  height: 1.2rem;\n}\r\n.size-full {\n  width: 100%;\n  height: 100%;\n}\r\n.h-1 {\n  height: 0.25rem;\n}\r\n.h-10 {\n  height: 2.5rem;\n}\r\n.h-11 {\n  height: 2.75rem;\n}\r\n.h-12 {\n  height: 3rem;\n}\r\n.h-14 {\n  height: 3.5rem;\n}\r\n.h-16 {\n  height: 4rem;\n}\r\n.h-2 {\n  height: 0.5rem;\n}\r\n.h-2\\.5 {\n  height: 0.625rem;\n}\r\n.h-20 {\n  height: 5rem;\n}\r\n.h-24 {\n  height: 6rem;\n}\r\n.h-3 {\n  height: 0.75rem;\n}\r\n.h-3\\.5 {\n  height: 0.875rem;\n}\r\n.h-32 {\n  height: 8rem;\n}\r\n.h-4 {\n  height: 1rem;\n}\r\n.h-48 {\n  height: 12rem;\n}\r\n.h-5 {\n  height: 1.25rem;\n}\r\n.h-6 {\n  height: 1.5rem;\n}\r\n.h-64 {\n  height: 16rem;\n}\r\n.h-7 {\n  height: 1.75rem;\n}\r\n.h-8 {\n  height: 2rem;\n}\r\n.h-80 {\n  height: 20rem;\n}\r\n.h-9 {\n  height: 2.25rem;\n}\r\n.h-\\[1px\\] {\n  height: 1px;\n}\r\n.h-\\[200px\\] {\n  height: 200px;\n}\r\n.h-\\[300px\\] {\n  height: 300px;\n}\r\n.h-\\[350px\\] {\n  height: 350px;\n}\r\n.h-\\[400px\\] {\n  height: 400px;\n}\r\n.h-\\[600px\\] {\n  height: 600px;\n}\r\n.h-\\[700px\\] {\n  height: 700px;\n}\r\n.h-\\[80vh\\] {\n  height: 80vh;\n}\r\n.h-\\[var\\(--radix-select-trigger-height\\)\\] {\n  height: var(--radix-select-trigger-height);\n}\r\n.h-auto {\n  height: auto;\n}\r\n.h-fit {\n  height: fit-content;\n}\r\n.h-full {\n  height: 100%;\n}\r\n.h-px {\n  height: 1px;\n}\r\n.h-screen {\n  height: 100vh;\n}\r\n.h-svh {\n  height: 100svh;\n}\r\n.max-h-32 {\n  max-height: 8rem;\n}\r\n.max-h-48 {\n  max-height: 12rem;\n}\r\n.max-h-64 {\n  max-height: 16rem;\n}\r\n.max-h-96 {\n  max-height: 24rem;\n}\r\n.max-h-\\[200px\\] {\n  max-height: 200px;\n}\r\n.max-h-\\[400px\\] {\n  max-height: 400px;\n}\r\n.max-h-\\[80vh\\] {\n  max-height: 80vh;\n}\r\n.max-h-\\[90vh\\] {\n  max-height: 90vh;\n}\r\n.max-h-\\[var\\(--radix-dropdown-menu-content-available-height\\)\\] {\n  max-height: var(--radix-dropdown-menu-content-available-height);\n}\r\n.max-h-screen {\n  max-height: 100vh;\n}\r\n.min-h-0 {\n  min-height: 0px;\n}\r\n.min-h-\\[100px\\] {\n  min-height: 100px;\n}\r\n.min-h-\\[120px\\] {\n  min-height: 120px;\n}\r\n.min-h-\\[150px\\] {\n  min-height: 150px;\n}\r\n.min-h-\\[200px\\] {\n  min-height: 200px;\n}\r\n.min-h-\\[300px\\] {\n  min-height: 300px;\n}\r\n.min-h-\\[400px\\] {\n  min-height: 400px;\n}\r\n.min-h-\\[600px\\] {\n  min-height: 600px;\n}\r\n.min-h-\\[60px\\] {\n  min-height: 60px;\n}\r\n.min-h-\\[80px\\] {\n  min-height: 80px;\n}\r\n.min-h-screen {\n  min-height: 100vh;\n}\r\n.min-h-svh {\n  min-height: 100svh;\n}\r\n.w-0 {\n  width: 0px;\n}\r\n.w-0\\.5 {\n  width: 0.125rem;\n}\r\n.w-1 {\n  width: 0.25rem;\n}\r\n.w-1\\/2 {\n  width: 50%;\n}\r\n.w-1\\/3 {\n  width: 33.333333%;\n}\r\n.w-1\\/4 {\n  width: 25%;\n}\r\n.w-1\\/6 {\n  width: 16.666667%;\n}\r\n.w-10 {\n  width: 2.5rem;\n}\r\n.w-11 {\n  width: 2.75rem;\n}\r\n.w-12 {\n  width: 3rem;\n}\r\n.w-14 {\n  width: 3.5rem;\n}\r\n.w-16 {\n  width: 4rem;\n}\r\n.w-2 {\n  width: 0.5rem;\n}\r\n.w-2\\.5 {\n  width: 0.625rem;\n}\r\n.w-2\\/3 {\n  width: 66.666667%;\n}\r\n.w-2\\/6 {\n  width: 33.333333%;\n}\r\n.w-20 {\n  width: 5rem;\n}\r\n.w-24 {\n  width: 6rem;\n}\r\n.w-3 {\n  width: 0.75rem;\n}\r\n.w-3\\.5 {\n  width: 0.875rem;\n}\r\n.w-3\\/4 {\n  width: 75%;\n}\r\n.w-3\\/5 {\n  width: 60%;\n}\r\n.w-32 {\n  width: 8rem;\n}\r\n.w-4 {\n  width: 1rem;\n}\r\n.w-40 {\n  width: 10rem;\n}\r\n.w-48 {\n  width: 12rem;\n}\r\n.w-5 {\n  width: 1.25rem;\n}\r\n.w-56 {\n  width: 14rem;\n}\r\n.w-6 {\n  width: 1.5rem;\n}\r\n.w-64 {\n  width: 16rem;\n}\r\n.w-7 {\n  width: 1.75rem;\n}\r\n.w-72 {\n  width: 18rem;\n}\r\n.w-8 {\n  width: 2rem;\n}\r\n.w-80 {\n  width: 20rem;\n}\r\n.w-9 {\n  width: 2.25rem;\n}\r\n.w-96 {\n  width: 24rem;\n}\r\n.w-\\[--sidebar-width\\] {\n  width: var(--sidebar-width);\n}\r\n.w-\\[100px\\] {\n  width: 100px;\n}\r\n.w-\\[140px\\] {\n  width: 140px;\n}\r\n.w-\\[150px\\] {\n  width: 150px;\n}\r\n.w-\\[1px\\] {\n  width: 1px;\n}\r\n.w-\\[200px\\] {\n  width: 200px;\n}\r\n.w-\\[250px\\] {\n  width: 250px;\n}\r\n.w-\\[280px\\] {\n  width: 280px;\n}\r\n.w-\\[70px\\] {\n  width: 70px;\n}\r\n.w-auto {\n  width: auto;\n}\r\n.w-fit {\n  width: fit-content;\n}\r\n.w-full {\n  width: 100%;\n}\r\n.w-px {\n  width: 1px;\n}\r\n.min-w-0 {\n  min-width: 0px;\n}\r\n.min-w-32 {\n  min-width: 8rem;\n}\r\n.min-w-48 {\n  min-width: 12rem;\n}\r\n.min-w-5 {\n  min-width: 1.25rem;\n}\r\n.min-w-\\[100px\\] {\n  min-width: 100px;\n}\r\n.min-w-\\[120px\\] {\n  min-width: 120px;\n}\r\n.min-w-\\[12rem\\] {\n  min-width: 12rem;\n}\r\n.min-w-\\[140px\\] {\n  min-width: 140px;\n}\r\n.min-w-\\[160px\\] {\n  min-width: 160px;\n}\r\n.min-w-\\[200px\\] {\n  min-width: 200px;\n}\r\n.min-w-\\[20px\\] {\n  min-width: 20px;\n}\r\n.min-w-\\[3rem\\] {\n  min-width: 3rem;\n}\r\n.min-w-\\[600px\\] {\n  min-width: 600px;\n}\r\n.min-w-\\[8rem\\] {\n  min-width: 8rem;\n}\r\n.min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n  min-width: var(--radix-select-trigger-width);\n}\r\n.min-w-fit {\n  min-width: fit-content;\n}\r\n.max-w-2xl {\n  max-width: 42rem;\n}\r\n.max-w-32 {\n  max-width: 8rem;\n}\r\n.max-w-4xl {\n  max-width: 56rem;\n}\r\n.max-w-5xl {\n  max-width: 64rem;\n}\r\n.max-w-\\[--skeleton-width\\] {\n  max-width: var(--skeleton-width);\n}\r\n.max-w-\\[120px\\] {\n  max-width: 120px;\n}\r\n.max-w-\\[150px\\] {\n  max-width: 150px;\n}\r\n.max-w-\\[200px\\] {\n  max-width: 200px;\n}\r\n.max-w-\\[300px\\] {\n  max-width: 300px;\n}\r\n.max-w-full {\n  max-width: 100%;\n}\r\n.max-w-lg {\n  max-width: 32rem;\n}\r\n.max-w-md {\n  max-width: 28rem;\n}\r\n.max-w-sm {\n  max-width: 24rem;\n}\r\n.max-w-xs {\n  max-width: 20rem;\n}\r\n.flex-1 {\n  flex: 1 1 0%;\n}\r\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\r\n.shrink-0 {\n  flex-shrink: 0;\n}\r\n.flex-grow {\n  flex-grow: 1;\n}\r\n.grow {\n  flex-grow: 1;\n}\r\n.caption-bottom {\n  caption-side: bottom;\n}\r\n.border-collapse {\n  border-collapse: collapse;\n}\r\n.origin-\\[--radix-dropdown-menu-content-transform-origin\\] {\n  transform-origin: var(--radix-dropdown-menu-content-transform-origin);\n}\r\n.origin-\\[--radix-popover-content-transform-origin\\] {\n  transform-origin: var(--radix-popover-content-transform-origin);\n}\r\n.-translate-x-1\\/2 {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-translate-x-full {\n  --tw-translate-x: -100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-translate-x-px {\n  --tw-translate-x: -1px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-translate-y-1\\/2 {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-x-0 {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-x-\\[-50\\%\\] {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-x-px {\n  --tw-translate-x: 1px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-\\[-50\\%\\] {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-0 {\n  --tw-rotate: 0deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-45 {\n  --tw-rotate: 45deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-90 {\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.scale-0 {\n  --tw-scale-x: 0;\n  --tw-scale-y: 0;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.scale-100 {\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n@keyframes ping {\n\n  75%, 100% {\n    transform: scale(2);\n    opacity: 0;\n  }\n}\r\n.animate-ping {\n  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\n}\r\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\r\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\r\n@keyframes spin {\n\n  to {\n    transform: rotate(360deg);\n  }\n}\r\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\r\n.cursor-default {\n  cursor: default;\n}\r\n.cursor-grab {\n  cursor: grab;\n}\r\n.cursor-not-allowed {\n  cursor: not-allowed;\n}\r\n.cursor-pointer {\n  cursor: pointer;\n}\r\n.touch-none {\n  touch-action: none;\n}\r\n.select-none {\n  user-select: none;\n}\r\n.resize {\n  resize: both;\n}\r\n.list-inside {\n  list-style-position: inside;\n}\r\n.list-disc {\n  list-style-type: disc;\n}\r\n.columns-1 {\n  columns: 1;\n}\r\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\r\n.grid-cols-12 {\n  grid-template-columns: repeat(12, minmax(0, 1fr));\n}\r\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\r\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\r\n.grid-cols-4 {\n  grid-template-columns: repeat(4, minmax(0, 1fr));\n}\r\n.grid-cols-5 {\n  grid-template-columns: repeat(5, minmax(0, 1fr));\n}\r\n.grid-cols-7 {\n  grid-template-columns: repeat(7, minmax(0, 1fr));\n}\r\n.grid-cols-9 {\n  grid-template-columns: repeat(9, minmax(0, 1fr));\n}\r\n.flex-row {\n  flex-direction: row;\n}\r\n.flex-col {\n  flex-direction: column;\n}\r\n.flex-col-reverse {\n  flex-direction: column-reverse;\n}\r\n.flex-wrap {\n  flex-wrap: wrap;\n}\r\n.content-normal {\n  align-content: normal;\n}\r\n.items-start {\n  align-items: flex-start;\n}\r\n.items-end {\n  align-items: flex-end;\n}\r\n.items-center {\n  align-items: center;\n}\r\n.items-stretch {\n  align-items: stretch;\n}\r\n.justify-start {\n  justify-content: flex-start;\n}\r\n.justify-end {\n  justify-content: flex-end;\n}\r\n.justify-center {\n  justify-content: center;\n}\r\n.justify-between {\n  justify-content: space-between;\n}\r\n.gap-1 {\n  gap: 0.25rem;\n}\r\n.gap-1\\.5 {\n  gap: 0.375rem;\n}\r\n.gap-2 {\n  gap: 0.5rem;\n}\r\n.gap-3 {\n  gap: 0.75rem;\n}\r\n.gap-4 {\n  gap: 1rem;\n}\r\n.gap-6 {\n  gap: 1.5rem;\n}\r\n.gap-8 {\n  gap: 2rem;\n}\r\n.gap-x-4 {\n  column-gap: 1rem;\n}\r\n.gap-x-6 {\n  column-gap: 1.5rem;\n}\r\n.gap-x-8 {\n  column-gap: 2rem;\n}\r\n.gap-y-2 {\n  row-gap: 0.5rem;\n}\r\n.gap-y-4 {\n  row-gap: 1rem;\n}\r\n.-space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(-0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(-0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-y-0 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0px * var(--tw-space-y-reverse));\n}\r\n.space-y-0\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));\n}\r\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\r\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\r\n.space-y-10 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-2\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse));\n}\r\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\r\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\r\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\r\n.self-start {\n  align-self: flex-start;\n}\r\n.overflow-auto {\n  overflow: auto;\n}\r\n.overflow-hidden {\n  overflow: hidden;\n}\r\n.overflow-x-auto {\n  overflow-x: auto;\n}\r\n.overflow-y-auto {\n  overflow-y: auto;\n}\r\n.overflow-x-hidden {\n  overflow-x: hidden;\n}\r\n.truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\r\n.whitespace-nowrap {\n  white-space: nowrap;\n}\r\n.whitespace-pre-wrap {\n  white-space: pre-wrap;\n}\r\n.break-words {\n  overflow-wrap: break-word;\n}\r\n.rounded {\n  border-radius: 0.25rem;\n}\r\n.rounded-2xl {\n  border-radius: 1rem;\n}\r\n.rounded-\\[2px\\] {\n  border-radius: 2px;\n}\r\n.rounded-\\[inherit\\] {\n  border-radius: inherit;\n}\r\n.rounded-full {\n  border-radius: 9999px;\n}\r\n.rounded-lg {\n  border-radius: var(--radius);\n}\r\n.rounded-md {\n  border-radius: calc(var(--radius) - 2px);\n}\r\n.rounded-sm {\n  border-radius: calc(var(--radius) - 4px);\n}\r\n.rounded-xl {\n  border-radius: 0.75rem;\n}\r\n.rounded-t {\n  border-top-left-radius: 0.25rem;\n  border-top-right-radius: 0.25rem;\n}\r\n.border {\n  border-width: 1px;\n}\r\n.border-0 {\n  border-width: 0px;\n}\r\n.border-2 {\n  border-width: 2px;\n}\r\n.border-8 {\n  border-width: 8px;\n}\r\n.border-\\[1\\.5px\\] {\n  border-width: 1.5px;\n}\r\n.border-b {\n  border-bottom-width: 1px;\n}\r\n.border-b-2 {\n  border-bottom-width: 2px;\n}\r\n.border-l {\n  border-left-width: 1px;\n}\r\n.border-l-4 {\n  border-left-width: 4px;\n}\r\n.border-r {\n  border-right-width: 1px;\n}\r\n.border-t {\n  border-top-width: 1px;\n}\r\n.border-t-2 {\n  border-top-width: 2px;\n}\r\n.border-dashed {\n  border-style: dashed;\n}\r\n.border-none {\n  border-style: none;\n}\r\n.border-\\[--color-border\\] {\n  border-color: var(--color-border);\n}\r\n.border-amber-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));\n}\r\n.border-amber-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));\n}\r\n.border-background {\n  border-color: hsl(var(--background));\n}\r\n.border-blue-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-500\\/30 {\n  border-color: rgb(59 130 246 / 0.3);\n}\r\n.border-blue-500\\/50 {\n  border-color: rgb(59 130 246 / 0.5);\n}\r\n.border-border {\n  border-color: hsl(var(--border));\n}\r\n.border-border\\/30 {\n  border-color: hsl(var(--border) / 0.3);\n}\r\n.border-border\\/40 {\n  border-color: hsl(var(--border) / 0.4);\n}\r\n.border-border\\/50 {\n  border-color: hsl(var(--border) / 0.5);\n}\r\n.border-border\\/60 {\n  border-color: hsl(var(--border) / 0.6);\n}\r\n.border-destructive {\n  border-color: hsl(var(--destructive));\n}\r\n.border-destructive\\/20 {\n  border-color: hsl(var(--destructive) / 0.2);\n}\r\n.border-destructive\\/50 {\n  border-color: hsl(var(--destructive) / 0.5);\n}\r\n.border-emerald-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(167 243 208 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-100 {\n  --tw-border-opacity: 1;\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-500\\/30 {\n  border-color: rgb(107 114 128 / 0.3);\n}\r\n.border-gray-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-700 {\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n}\r\n.border-green-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\n}\r\n.border-green-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));\n}\r\n.border-green-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));\n}\r\n.border-green-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\n}\r\n.border-green-500\\/30 {\n  border-color: rgb(34 197 94 / 0.3);\n}\r\n.border-green-500\\/50 {\n  border-color: rgb(34 197 94 / 0.5);\n}\r\n.border-green-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));\n}\r\n.border-indigo-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));\n}\r\n.border-indigo-500\\/30 {\n  border-color: rgb(99 102 241 / 0.3);\n}\r\n.border-input {\n  border-color: hsl(var(--input));\n}\r\n.border-muted {\n  border-color: hsl(var(--muted));\n}\r\n.border-muted-foreground\\/25 {\n  border-color: hsl(var(--muted-foreground) / 0.25);\n}\r\n.border-orange-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));\n}\r\n.border-orange-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));\n}\r\n.border-orange-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(234 88 12 / var(--tw-border-opacity, 1));\n}\r\n.border-primary {\n  border-color: hsl(var(--primary));\n}\r\n.border-primary\\/10 {\n  border-color: hsl(var(--primary) / 0.1);\n}\r\n.border-primary\\/20 {\n  border-color: hsl(var(--primary) / 0.2);\n}\r\n.border-primary\\/30 {\n  border-color: hsl(var(--primary) / 0.3);\n}\r\n.border-purple-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));\n}\r\n.border-purple-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));\n}\r\n.border-purple-500\\/30 {\n  border-color: rgb(168 85 247 / 0.3);\n}\r\n.border-red-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\n}\r\n.border-red-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\n}\r\n.border-red-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));\n}\r\n.border-red-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\r\n.border-red-500\\/30 {\n  border-color: rgb(239 68 68 / 0.3);\n}\r\n.border-red-800 {\n  --tw-border-opacity: 1;\n  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));\n}\r\n.border-slate-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\n}\r\n.border-teal-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(153 246 228 / var(--tw-border-opacity, 1));\n}\r\n.border-transparent {\n  border-color: transparent;\n}\r\n.border-white {\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\r\n.border-yellow-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\n}\r\n.border-yellow-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(253 224 71 / var(--tw-border-opacity, 1));\n}\r\n.border-yellow-500\\/30 {\n  border-color: rgb(234 179 8 / 0.3);\n}\r\n.border-yellow-500\\/50 {\n  border-color: rgb(234 179 8 / 0.5);\n}\r\n.border-yellow-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(202 138 4 / var(--tw-border-opacity, 1));\n}\r\n.border-b-transparent {\n  border-bottom-color: transparent;\n}\r\n.border-l-transparent {\n  border-left-color: transparent;\n}\r\n.border-r-transparent {\n  border-right-color: transparent;\n}\r\n.border-t-blue-600 {\n  --tw-border-opacity: 1;\n  border-top-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\n}\r\n.border-t-transparent {\n  border-top-color: transparent;\n}\r\n.bg-\\[--color-bg\\] {\n  background-color: var(--color-bg);\n}\r\n.bg-accent {\n  background-color: hsl(var(--accent));\n}\r\n.bg-accent\\/10 {\n  background-color: hsl(var(--accent) / 0.1);\n}\r\n.bg-amber-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));\n}\r\n.bg-amber-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));\n}\r\n.bg-background {\n  background-color: hsl(var(--background));\n}\r\n.bg-background\\/50 {\n  background-color: hsl(var(--background) / 0.5);\n}\r\n.bg-background\\/80 {\n  background-color: hsl(var(--background) / 0.8);\n}\r\n.bg-background\\/95 {\n  background-color: hsl(var(--background) / 0.95);\n}\r\n.bg-black {\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\r\n.bg-black\\/50 {\n  background-color: rgb(0 0 0 / 0.5);\n}\r\n.bg-black\\/80 {\n  background-color: rgb(0 0 0 / 0.8);\n}\r\n.bg-blue-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-500\\/20 {\n  background-color: rgb(59 130 246 / 0.2);\n}\r\n.bg-blue-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\r\n.bg-border {\n  background-color: hsl(var(--border));\n}\r\n.bg-border\\/50 {\n  background-color: hsl(var(--border) / 0.5);\n}\r\n.bg-card {\n  background-color: hsl(var(--card));\n}\r\n.bg-cyan-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));\n}\r\n.bg-cyan-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-destructive {\n  background-color: hsl(var(--destructive));\n}\r\n.bg-destructive\\/10 {\n  background-color: hsl(var(--destructive) / 0.1);\n}\r\n.bg-destructive\\/15 {\n  background-color: hsl(var(--destructive) / 0.15);\n}\r\n.bg-destructive\\/20 {\n  background-color: hsl(var(--destructive) / 0.2);\n}\r\n.bg-destructive\\/5 {\n  background-color: hsl(var(--destructive) / 0.05);\n}\r\n.bg-emerald-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));\n}\r\n.bg-emerald-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));\n}\r\n.bg-foreground {\n  background-color: hsl(var(--foreground));\n}\r\n.bg-gray-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-500\\/20 {\n  background-color: rgb(107 114 128 / 0.2);\n}\r\n.bg-gray-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-800 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-900 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-500\\/20 {\n  background-color: rgb(34 197 94 / 0.2);\n}\r\n.bg-green-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\r\n.bg-indigo-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-indigo-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-indigo-500\\/20 {\n  background-color: rgb(99 102 241 / 0.2);\n}\r\n.bg-muted {\n  background-color: hsl(var(--muted));\n}\r\n.bg-muted-foreground {\n  background-color: hsl(var(--muted-foreground));\n}\r\n.bg-muted\\/10 {\n  background-color: hsl(var(--muted) / 0.1);\n}\r\n.bg-muted\\/20 {\n  background-color: hsl(var(--muted) / 0.2);\n}\r\n.bg-muted\\/30 {\n  background-color: hsl(var(--muted) / 0.3);\n}\r\n.bg-muted\\/50 {\n  background-color: hsl(var(--muted) / 0.5);\n}\r\n.bg-orange-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\n}\r\n.bg-orange-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));\n}\r\n.bg-orange-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\n}\r\n.bg-popover {\n  background-color: hsl(var(--popover));\n}\r\n.bg-primary {\n  background-color: hsl(var(--primary));\n}\r\n.bg-primary\\/10 {\n  background-color: hsl(var(--primary) / 0.1);\n}\r\n.bg-primary\\/30 {\n  background-color: hsl(var(--primary) / 0.3);\n}\r\n.bg-primary\\/5 {\n  background-color: hsl(var(--primary) / 0.05);\n}\r\n.bg-purple-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-purple-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-purple-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\n}\r\n.bg-purple-500\\/20 {\n  background-color: rgb(168 85 247 / 0.2);\n}\r\n.bg-purple-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-500\\/20 {\n  background-color: rgb(239 68 68 / 0.2);\n}\r\n.bg-red-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-900\\/20 {\n  background-color: rgb(127 29 29 / 0.2);\n}\r\n.bg-secondary {\n  background-color: hsl(var(--secondary));\n}\r\n.bg-slate-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));\n}\r\n.bg-slate-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));\n}\r\n.bg-slate-50\\/30 {\n  background-color: rgb(248 250 252 / 0.3);\n}\r\n.bg-teal-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 253 250 / var(--tw-bg-opacity, 1));\n}\r\n.bg-transparent {\n  background-color: transparent;\n}\r\n.bg-white {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-500\\/20 {\n  background-color: rgb(234 179 8 / 0.2);\n}\r\n.bg-opacity-50 {\n  --tw-bg-opacity: 0.5;\n}\r\n.bg-gradient-to-b {\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\n}\r\n.bg-gradient-to-br {\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\r\n.bg-gradient-to-r {\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\r\n.from-background {\n  --tw-gradient-from: hsl(var(--background)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-background\\/50 {\n  --tw-gradient-from: hsl(var(--background) / 0.5) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-blue-50 {\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-blue-500 {\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-blue-500\\/20 {\n  --tw-gradient-from: rgb(59 130 246 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-blue-600 {\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-border {\n  --tw-gradient-from: hsl(var(--border)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--border) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-card {\n  --tw-gradient-from: hsl(var(--card)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--card) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-gray-50 {\n  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-gray-500\\/20 {\n  --tw-gradient-from: rgb(107 114 128 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(107 114 128 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-green-50 {\n  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-green-500\\/20 {\n  --tw-gradient-from: rgb(34 197 94 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-indigo-50 {\n  --tw-gradient-from: #eef2ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(238 242 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-primary {\n  --tw-gradient-from: hsl(var(--primary)) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-purple-50 {\n  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-purple-500\\/20 {\n  --tw-gradient-from: rgb(168 85 247 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-red-50 {\n  --tw-gradient-from: #fef2f2 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(254 242 242 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-red-500\\/20 {\n  --tw-gradient-from: rgb(239 68 68 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-slate-100 {\n  --tw-gradient-from: #f1f5f9 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(241 245 249 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-slate-50 {\n  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-transparent {\n  --tw-gradient-from: transparent var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-white {\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-yellow-50 {\n  --tw-gradient-from: #fefce8 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(254 252 232 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-yellow-500\\/20 {\n  --tw-gradient-from: rgb(234 179 8 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.via-background {\n  --tw-gradient-to: hsl(var(--background) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background)) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-border {\n  --tw-gradient-to: hsl(var(--border) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--border)) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.via-border\\/50 {\n  --tw-gradient-to: hsl(var(--border) / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--border) / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.to-accent {\n  --tw-gradient-to: hsl(var(--accent)) var(--tw-gradient-to-position);\n}\r\n.to-amber-500\\/20 {\n  --tw-gradient-to: rgb(245 158 11 / 0.2) var(--tw-gradient-to-position);\n}\r\n.to-background\\/30 {\n  --tw-gradient-to: hsl(var(--background) / 0.3) var(--tw-gradient-to-position);\n}\r\n.to-blue-100 {\n  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);\n}\r\n.to-blue-600\\/20 {\n  --tw-gradient-to: rgb(37 99 235 / 0.2) var(--tw-gradient-to-position);\n}\r\n.to-card\\/95 {\n  --tw-gradient-to: hsl(var(--card) / 0.95) var(--tw-gradient-to-position);\n}\r\n.to-emerald-50 {\n  --tw-gradient-to: #ecfdf5 var(--tw-gradient-to-position);\n}\r\n.to-emerald-500\\/20 {\n  --tw-gradient-to: rgb(16 185 129 / 0.2) var(--tw-gradient-to-position);\n}\r\n.to-gray-100 {\n  --tw-gradient-to: #f3f4f6 var(--tw-gradient-to-position);\n}\r\n.to-gray-50 {\n  --tw-gradient-to: #f9fafb var(--tw-gradient-to-position);\n}\r\n.to-gray-900 {\n  --tw-gradient-to: #111827 var(--tw-gradient-to-position);\n}\r\n.to-green-100 {\n  --tw-gradient-to: #dcfce7 var(--tw-gradient-to-position);\n}\r\n.to-indigo-100 {\n  --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);\n}\r\n.to-indigo-50 {\n  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);\n}\r\n.to-indigo-600 {\n  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);\n}\r\n.to-muted\\/20 {\n  --tw-gradient-to: hsl(var(--muted) / 0.2) var(--tw-gradient-to-position);\n}\r\n.to-primary\\/90 {\n  --tw-gradient-to: hsl(var(--primary) / 0.9) var(--tw-gradient-to-position);\n}\r\n.to-purple-100 {\n  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);\n}\r\n.to-purple-600 {\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\n}\r\n.to-red-100 {\n  --tw-gradient-to: #fee2e2 var(--tw-gradient-to-position);\n}\r\n.to-rose-500\\/20 {\n  --tw-gradient-to: rgb(244 63 94 / 0.2) var(--tw-gradient-to-position);\n}\r\n.to-slate-100 {\n  --tw-gradient-to: #f1f5f9 var(--tw-gradient-to-position);\n}\r\n.to-slate-500\\/20 {\n  --tw-gradient-to: rgb(100 116 139 / 0.2) var(--tw-gradient-to-position);\n}\r\n.to-transparent {\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\n}\r\n.to-violet-500\\/20 {\n  --tw-gradient-to: rgb(139 92 246 / 0.2) var(--tw-gradient-to-position);\n}\r\n.to-yellow-100 {\n  --tw-gradient-to: #fef9c3 var(--tw-gradient-to-position);\n}\r\n.fill-current {\n  fill: currentColor;\n}\r\n.object-cover {\n  object-fit: cover;\n}\r\n.p-0 {\n  padding: 0px;\n}\r\n.p-1 {\n  padding: 0.25rem;\n}\r\n.p-2 {\n  padding: 0.5rem;\n}\r\n.p-2\\.5 {\n  padding: 0.625rem;\n}\r\n.p-3 {\n  padding: 0.75rem;\n}\r\n.p-4 {\n  padding: 1rem;\n}\r\n.p-5 {\n  padding: 1.25rem;\n}\r\n.p-6 {\n  padding: 1.5rem;\n}\r\n.p-8 {\n  padding: 2rem;\n}\r\n.p-\\[1px\\] {\n  padding: 1px;\n}\r\n.px-0 {\n  padding-left: 0px;\n  padding-right: 0px;\n}\r\n.px-1 {\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n}\r\n.px-1\\.5 {\n  padding-left: 0.375rem;\n  padding-right: 0.375rem;\n}\r\n.px-10 {\n  padding-left: 2.5rem;\n  padding-right: 2.5rem;\n}\r\n.px-2 {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\r\n.px-2\\.5 {\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\r\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\r\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\r\n.px-5 {\n  padding-left: 1.25rem;\n  padding-right: 1.25rem;\n}\r\n.px-6 {\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\r\n.px-8 {\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\r\n.py-0\\.5 {\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\r\n.py-1 {\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\r\n.py-1\\.5 {\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\r\n.py-10 {\n  padding-top: 2.5rem;\n  padding-bottom: 2.5rem;\n}\r\n.py-12 {\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\r\n.py-16 {\n  padding-top: 4rem;\n  padding-bottom: 4rem;\n}\r\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\r\n.py-3 {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\r\n.py-4 {\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\r\n.py-6 {\n  padding-top: 1.5rem;\n  padding-bottom: 1.5rem;\n}\r\n.py-8 {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\r\n.pb-2 {\n  padding-bottom: 0.5rem;\n}\r\n.pb-3 {\n  padding-bottom: 0.75rem;\n}\r\n.pb-4 {\n  padding-bottom: 1rem;\n}\r\n.pb-6 {\n  padding-bottom: 1.5rem;\n}\r\n.pb-8 {\n  padding-bottom: 2rem;\n}\r\n.pl-10 {\n  padding-left: 2.5rem;\n}\r\n.pl-3 {\n  padding-left: 0.75rem;\n}\r\n.pl-4 {\n  padding-left: 1rem;\n}\r\n.pl-8 {\n  padding-left: 2rem;\n}\r\n.pl-9 {\n  padding-left: 2.25rem;\n}\r\n.pr-1 {\n  padding-right: 0.25rem;\n}\r\n.pr-12 {\n  padding-right: 3rem;\n}\r\n.pr-2 {\n  padding-right: 0.5rem;\n}\r\n.pr-3 {\n  padding-right: 0.75rem;\n}\r\n.pr-4 {\n  padding-right: 1rem;\n}\r\n.pr-8 {\n  padding-right: 2rem;\n}\r\n.pt-0 {\n  padding-top: 0px;\n}\r\n.pt-1 {\n  padding-top: 0.25rem;\n}\r\n.pt-2 {\n  padding-top: 0.5rem;\n}\r\n.pt-3 {\n  padding-top: 0.75rem;\n}\r\n.pt-4 {\n  padding-top: 1rem;\n}\r\n.pt-6 {\n  padding-top: 1.5rem;\n}\r\n.text-left {\n  text-align: left;\n}\r\n.text-center {\n  text-align: center;\n}\r\n.text-right {\n  text-align: right;\n}\r\n.align-middle {\n  vertical-align: middle;\n}\r\n.font-mono {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\r\n.font-sans {\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\r\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\r\n.text-3xl {\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\r\n.text-4xl {\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\r\n.text-\\[0\\.8rem\\] {\n  font-size: 0.8rem;\n}\r\n.text-base {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\r\n.text-lg {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\r\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\r\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\r\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\r\n.font-bold {\n  font-weight: 700;\n}\r\n.font-medium {\n  font-weight: 500;\n}\r\n.font-normal {\n  font-weight: 400;\n}\r\n.font-semibold {\n  font-weight: 600;\n}\r\n.uppercase {\n  text-transform: uppercase;\n}\r\n.lowercase {\n  text-transform: lowercase;\n}\r\n.capitalize {\n  text-transform: capitalize;\n}\r\n.italic {\n  font-style: italic;\n}\r\n.tabular-nums {\n  --tw-numeric-spacing: tabular-nums;\n  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);\n}\r\n.leading-none {\n  line-height: 1;\n}\r\n.leading-relaxed {\n  line-height: 1.625;\n}\r\n.leading-tight {\n  line-height: 1.25;\n}\r\n.tracking-tight {\n  letter-spacing: -0.025em;\n}\r\n.tracking-wide {\n  letter-spacing: 0.025em;\n}\r\n.tracking-widest {\n  letter-spacing: 0.1em;\n}\r\n.text-accent {\n  color: hsl(var(--accent));\n}\r\n.text-accent-foreground {\n  color: hsl(var(--accent-foreground));\n}\r\n.text-amber-500 {\n  --tw-text-opacity: 1;\n  color: rgb(245 158 11 / var(--tw-text-opacity, 1));\n}\r\n.text-amber-600 {\n  --tw-text-opacity: 1;\n  color: rgb(217 119 6 / var(--tw-text-opacity, 1));\n}\r\n.text-amber-700 {\n  --tw-text-opacity: 1;\n  color: rgb(180 83 9 / var(--tw-text-opacity, 1));\n}\r\n.text-amber-800 {\n  --tw-text-opacity: 1;\n  color: rgb(146 64 14 / var(--tw-text-opacity, 1));\n}\r\n.text-black {\n  --tw-text-opacity: 1;\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-500 {\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-600 {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-700 {\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-800 {\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-900 {\n  --tw-text-opacity: 1;\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\n}\r\n.text-card-foreground {\n  color: hsl(var(--card-foreground));\n}\r\n.text-current {\n  color: currentColor;\n}\r\n.text-cyan-600 {\n  --tw-text-opacity: 1;\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\n}\r\n.text-cyan-800 {\n  --tw-text-opacity: 1;\n  color: rgb(21 94 117 / var(--tw-text-opacity, 1));\n}\r\n.text-destructive {\n  color: hsl(var(--destructive));\n}\r\n.text-destructive-foreground {\n  color: hsl(var(--destructive-foreground));\n}\r\n.text-destructive\\/80 {\n  color: hsl(var(--destructive) / 0.8);\n}\r\n.text-emerald-600 {\n  --tw-text-opacity: 1;\n  color: rgb(5 150 105 / var(--tw-text-opacity, 1));\n}\r\n.text-emerald-700 {\n  --tw-text-opacity: 1;\n  color: rgb(4 120 87 / var(--tw-text-opacity, 1));\n}\r\n.text-foreground {\n  color: hsl(var(--foreground));\n}\r\n.text-foreground\\/50 {\n  color: hsl(var(--foreground) / 0.5);\n}\r\n.text-gray-100 {\n  --tw-text-opacity: 1;\n  color: rgb(243 244 246 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-300 {\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-400 {\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-500 {\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-600 {\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-700 {\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-800 {\n  --tw-text-opacity: 1;\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-900 {\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\r\n.text-green-500 {\n  --tw-text-opacity: 1;\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\n}\r\n.text-green-600 {\n  --tw-text-opacity: 1;\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\n}\r\n.text-green-700 {\n  --tw-text-opacity: 1;\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\n}\r\n.text-green-800 {\n  --tw-text-opacity: 1;\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\n}\r\n.text-green-900 {\n  --tw-text-opacity: 1;\n  color: rgb(20 83 45 / var(--tw-text-opacity, 1));\n}\r\n.text-indigo-600 {\n  --tw-text-opacity: 1;\n  color: rgb(79 70 229 / var(--tw-text-opacity, 1));\n}\r\n.text-indigo-700 {\n  --tw-text-opacity: 1;\n  color: rgb(67 56 202 / var(--tw-text-opacity, 1));\n}\r\n.text-indigo-800 {\n  --tw-text-opacity: 1;\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\n}\r\n.text-muted-foreground {\n  color: hsl(var(--muted-foreground));\n}\r\n.text-muted-foreground\\/50 {\n  color: hsl(var(--muted-foreground) / 0.5);\n}\r\n.text-muted-foreground\\/75 {\n  color: hsl(var(--muted-foreground) / 0.75);\n}\r\n.text-orange-600 {\n  --tw-text-opacity: 1;\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\n}\r\n.text-orange-700 {\n  --tw-text-opacity: 1;\n  color: rgb(194 65 12 / var(--tw-text-opacity, 1));\n}\r\n.text-orange-800 {\n  --tw-text-opacity: 1;\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\n}\r\n.text-orange-900 {\n  --tw-text-opacity: 1;\n  color: rgb(124 45 18 / var(--tw-text-opacity, 1));\n}\r\n.text-popover-foreground {\n  color: hsl(var(--popover-foreground));\n}\r\n.text-primary {\n  color: hsl(var(--primary));\n}\r\n.text-primary-foreground {\n  color: hsl(var(--primary-foreground));\n}\r\n.text-purple-500 {\n  --tw-text-opacity: 1;\n  color: rgb(168 85 247 / var(--tw-text-opacity, 1));\n}\r\n.text-purple-600 {\n  --tw-text-opacity: 1;\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\n}\r\n.text-purple-700 {\n  --tw-text-opacity: 1;\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\n}\r\n.text-purple-800 {\n  --tw-text-opacity: 1;\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\n}\r\n.text-red-400 {\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\r\n.text-red-500 {\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\r\n.text-red-600 {\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\r\n.text-red-700 {\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\r\n.text-red-800 {\n  --tw-text-opacity: 1;\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\n}\r\n.text-red-900 {\n  --tw-text-opacity: 1;\n  color: rgb(127 29 29 / var(--tw-text-opacity, 1));\n}\r\n.text-secondary-foreground {\n  color: hsl(var(--secondary-foreground));\n}\r\n.text-slate-600 {\n  --tw-text-opacity: 1;\n  color: rgb(71 85 105 / var(--tw-text-opacity, 1));\n}\r\n.text-slate-700 {\n  --tw-text-opacity: 1;\n  color: rgb(51 65 85 / var(--tw-text-opacity, 1));\n}\r\n.text-slate-800 {\n  --tw-text-opacity: 1;\n  color: rgb(30 41 59 / var(--tw-text-opacity, 1));\n}\r\n.text-teal-800 {\n  --tw-text-opacity: 1;\n  color: rgb(17 94 89 / var(--tw-text-opacity, 1));\n}\r\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-500 {\n  --tw-text-opacity: 1;\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-600 {\n  --tw-text-opacity: 1;\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-700 {\n  --tw-text-opacity: 1;\n  color: rgb(161 98 7 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-800 {\n  --tw-text-opacity: 1;\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-900 {\n  --tw-text-opacity: 1;\n  color: rgb(113 63 18 / var(--tw-text-opacity, 1));\n}\r\n.line-through {\n  text-decoration-line: line-through;\n}\r\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\r\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\r\n.opacity-0 {\n  opacity: 0;\n}\r\n.opacity-30 {\n  opacity: 0.3;\n}\r\n.opacity-50 {\n  opacity: 0.5;\n}\r\n.opacity-60 {\n  opacity: 0.6;\n}\r\n.opacity-70 {\n  opacity: 0.7;\n}\r\n.opacity-75 {\n  opacity: 0.75;\n}\r\n.opacity-90 {\n  opacity: 0.9;\n}\r\n.shadow {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-border\\)\\)\\] {\n  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));\n  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-md {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-none {\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-xl {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.outline-none {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.outline {\n  outline-style: solid;\n}\r\n.ring-0 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-1 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-2 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-primary\\/20 {\n  --tw-ring-color: hsl(var(--primary) / 0.2);\n}\r\n.ring-primary\\/30 {\n  --tw-ring-color: hsl(var(--primary) / 0.3);\n}\r\n.ring-offset-2 {\n  --tw-ring-offset-width: 2px;\n}\r\n.ring-offset-background {\n  --tw-ring-offset-color: hsl(var(--background));\n}\r\n.filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.backdrop-blur {\n  --tw-backdrop-blur: blur(8px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.backdrop-blur-sm {\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.backdrop-filter {\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.transition {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-\\[left\\2c right\\2c width\\] {\n  transition-property: left,right,width;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-\\[margin\\2c opa\\] {\n  transition-property: margin,opa;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-\\[width\\2c height\\2c padding\\] {\n  transition-property: width,height,padding;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-\\[width\\] {\n  transition-property: width;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-transform {\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.duration-200 {\n  transition-duration: 200ms;\n}\r\n.duration-300 {\n  transition-duration: 300ms;\n}\r\n.ease-in-out {\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\r\n.ease-linear {\n  transition-timing-function: linear;\n}\r\n@keyframes enter {\n\n  from {\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\r\n@keyframes exit {\n\n  to {\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\r\n.animate-in {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.fade-in-0 {\n  --tw-enter-opacity: 0;\n}\r\n.zoom-in-95 {\n  --tw-enter-scale: .95;\n}\r\n.slide-in-from-bottom-2 {\n  --tw-enter-translate-y: 0.5rem;\n}\r\n.slide-in-from-right {\n  --tw-enter-translate-x: 100%;\n}\r\n.slide-in-from-top-1 {\n  --tw-enter-translate-y: -0.25rem;\n}\r\n.duration-200 {\n  animation-duration: 200ms;\n}\r\n.duration-300 {\n  animation-duration: 300ms;\n}\r\n.ease-in-out {\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\r\n.ease-linear {\n  animation-timing-function: linear;\n}\r\n.running {\n  animation-play-state: running;\n}\r\n.paused {\n  animation-play-state: paused;\n}\r\n.scrollbar-thin {\r\n    scrollbar-width: thin;\r\n    scrollbar-color: hsl(var(--border)) transparent;\r\n  }\r\n.scrollbar-thin::-webkit-scrollbar {\r\n    width: 6px;\r\n  }\r\n.scrollbar-thin::-webkit-scrollbar-track {\r\n    background: transparent;\r\n  }\r\n.scrollbar-thin::-webkit-scrollbar-thumb {\r\n    background-color: hsl(var(--border));\r\n    border-radius: 3px;\r\n  }\r\n.scrollbar-thin::-webkit-scrollbar-thumb:hover {\r\n    background-color: hsl(var(--muted-foreground));\r\n  }\r\n.scrollbar-thumb-border {\r\n    scrollbar-color: hsl(var(--border)) transparent;\r\n  }\r\n.scrollbar-track-transparent {\r\n    scrollbar-color: hsl(var(--border)) transparent;\r\n  }\r\n\r\n/* Custom scrollbar styles */\r\n\r\n/* Global print styles */\r\n@media print {\r\n  /* Reset margins and ensure proper page setup */\r\n  * {\r\n    -webkit-print-color-adjust: exact !important;\r\n    print-color-adjust: exact !important;\r\n    color-adjust: exact !important;\r\n    box-sizing: border-box !important;\r\n  }\r\n\r\n  html {\r\n    margin: 0 !important;\r\n    padding: 0 !important;\r\n    box-sizing: border-box !important; /* Ensure consistent box model */\r\n  }\r\n\r\n  /* Comprehensive page setup to eliminate black borders */\r\n  @page {\r\n    margin: 0.5in !important;\r\n    padding: 0 !important;\r\n    border: none !important;\r\n    box-shadow: none !important;\r\n    size: auto !important;\r\n  }\r\n\r\n  html,\r\n  body {\r\n    border: none !important;\r\n    outline: none !important;\r\n    box-shadow: none !important;\r\n    background: white !important;\r\n    width: 100% !important;\r\n  }\r\n\r\n  /* Reset all container elements that might cause borders - PRINT ONLY */\r\n  #__next,\r\n  #root,\r\n  main,\r\n  .container,\r\n  .mx-auto,\r\n  .max-w-4xl,\r\n  .max-w-6xl,\r\n  .max-w-7xl {\r\n    margin: 0 !important;\r\n    padding: 0 !important;\r\n    border: none !important;\r\n    outline: none !important;\r\n    box-shadow: none !important;\r\n    background: transparent !important;\r\n    width: 100% !important;\r\n  }\r\n\r\n  /* Remove borders from all UI components - PRINT ONLY */\r\n  .card,\r\n  .border,\r\n  .border-t,\r\n  .border-b,\r\n  .border-l,\r\n  .border-r,\r\n  .border-gray-200,\r\n  .border-gray-300,\r\n  .border-border,\r\n  .rounded,\r\n  .rounded-lg,\r\n  .shadow,\r\n  .shadow-sm,\r\n  .shadow-md,\r\n  .shadow-lg {\r\n    border: none !important;\r\n    box-shadow: none !important;\r\n    border-radius: 0 !important;\r\n    outline: none !important;\r\n  }\r\n\r\n  body {\r\n    background: white !important;\r\n    color: black !important;\r\n    font-family:\r\n      system-ui,\r\n      -apple-system,\r\n      BlinkMacSystemFont,\r\n      'Segoe UI',\r\n      Roboto,\r\n      Oxygen,\r\n      Ubuntu,\r\n      Cantarell,\r\n      'Open Sans',\r\n      'Helvetica Neue',\r\n      sans-serif !important;\r\n    font-size: 10pt !important;\r\n    line-height: 1.4 !important;\r\n    margin: 0 !important;\r\n    padding: 0 !important;\r\n  }\r\n\r\n  /* Hide elements that shouldn't be printed */\r\n  .no-print {\r\n    display: none !important;\r\n  }\r\n\r\n  .print-only {\r\n    display: block !important;\r\n  }\r\n\r\n  /* Ensure proper page breaks */\r\n  .page-break-before {\r\n    page-break-before: always !important;\r\n  }\r\n\r\n  .page-break-after {\r\n    page-break-after: always !important;\r\n  }\r\n\r\n  .page-break-inside-avoid {\r\n    page-break-inside: avoid !important;\r\n  }\r\n\r\n  /* Ensure table containers don't have borders */\r\n  .table-container,\r\n  .delegation-table-container {\r\n    border: none !important;\r\n    box-shadow: none !important;\r\n    outline: none !important;\r\n    margin: 0 !important;\r\n    padding: 0 !important;\r\n  }\r\n\r\n  /* Note: Delegation table styles are now consolidated in print.css */\r\n\r\n  /* Links should show URL in print */\r\n  a[href]:after {\r\n    content: ' (' attr(href) ')';\r\n    font-size: 8pt;\r\n    color: #666;\r\n  }\r\n\r\n  /* Remove link styling for print */\r\n  a {\r\n    color: inherit !important;\r\n    text-decoration: none !important;\r\n  }\r\n}\r\n.file\\:border-0::file-selector-button {\n  border-width: 0px;\n}\r\n.file\\:bg-transparent::file-selector-button {\n  background-color: transparent;\n}\r\n.file\\:text-sm::file-selector-button {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\r\n.file\\:font-medium::file-selector-button {\n  font-weight: 500;\n}\r\n.file\\:text-foreground::file-selector-button {\n  color: hsl(var(--foreground));\n}\r\n.placeholder\\:text-muted-foreground::placeholder {\n  color: hsl(var(--muted-foreground));\n}\r\n.after\\:absolute::after {\n  content: var(--tw-content);\n  position: absolute;\n}\r\n.after\\:-inset-2::after {\n  content: var(--tw-content);\n  inset: -0.5rem;\n}\r\n.after\\:inset-y-0::after {\n  content: var(--tw-content);\n  top: 0px;\n  bottom: 0px;\n}\r\n.after\\:left-1\\/2::after {\n  content: var(--tw-content);\n  left: 50%;\n}\r\n.after\\:w-\\[2px\\]::after {\n  content: var(--tw-content);\n  width: 2px;\n}\r\n.focus-within\\:relative:focus-within {\n  position: relative;\n}\r\n.focus-within\\:z-20:focus-within {\n  z-index: 20;\n}\r\n.hover\\:scale-105:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:border-blue-300:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));\n}\r\n.hover\\:border-border:hover {\n  border-color: hsl(var(--border));\n}\r\n.hover\\:border-gray-300:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\r\n.hover\\:border-gray-400:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\n}\r\n.hover\\:border-gray-500:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));\n}\r\n.hover\\:border-primary\\/20:hover {\n  border-color: hsl(var(--primary) / 0.2);\n}\r\n.hover\\:border-primary\\/30:hover {\n  border-color: hsl(var(--primary) / 0.3);\n}\r\n.hover\\:border-primary\\/50:hover {\n  border-color: hsl(var(--primary) / 0.5);\n}\r\n.hover\\:bg-accent:hover {\n  background-color: hsl(var(--accent));\n}\r\n.hover\\:bg-accent\\/50:hover {\n  background-color: hsl(var(--accent) / 0.5);\n}\r\n.hover\\:bg-accent\\/90:hover {\n  background-color: hsl(var(--accent) / 0.9);\n}\r\n.hover\\:bg-blue-100:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-blue-50\\/50:hover {\n  background-color: rgb(239 246 255 / 0.5);\n}\r\n.hover\\:bg-blue-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-blue-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-destructive\\/10:hover {\n  background-color: hsl(var(--destructive) / 0.1);\n}\r\n.hover\\:bg-destructive\\/80:hover {\n  background-color: hsl(var(--destructive) / 0.8);\n}\r\n.hover\\:bg-destructive\\/90:hover {\n  background-color: hsl(var(--destructive) / 0.9);\n}\r\n.hover\\:bg-gray-100:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-gray-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-gray-50\\/50:hover {\n  background-color: rgb(249 250 251 / 0.5);\n}\r\n.hover\\:bg-gray-500:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-gray-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-green-50\\/50:hover {\n  background-color: rgb(240 253 244 / 0.5);\n}\r\n.hover\\:bg-green-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-green-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-muted\\/30:hover {\n  background-color: hsl(var(--muted) / 0.3);\n}\r\n.hover\\:bg-muted\\/50:hover {\n  background-color: hsl(var(--muted) / 0.5);\n}\r\n.hover\\:bg-primary:hover {\n  background-color: hsl(var(--primary));\n}\r\n.hover\\:bg-primary\\/5:hover {\n  background-color: hsl(var(--primary) / 0.05);\n}\r\n.hover\\:bg-primary\\/80:hover {\n  background-color: hsl(var(--primary) / 0.8);\n}\r\n.hover\\:bg-primary\\/90:hover {\n  background-color: hsl(var(--primary) / 0.9);\n}\r\n.hover\\:bg-purple-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-purple-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-red-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-red-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-secondary:hover {\n  background-color: hsl(var(--secondary));\n}\r\n.hover\\:bg-secondary\\/80:hover {\n  background-color: hsl(var(--secondary) / 0.8);\n}\r\n.hover\\:bg-slate-200:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-slate-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-transparent:hover {\n  background-color: transparent;\n}\r\n.hover\\:bg-yellow-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:from-blue-700:hover {\n  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.hover\\:from-primary\\/90:hover {\n  --tw-gradient-from: hsl(var(--primary) / 0.9) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.hover\\:to-accent\\/90:hover {\n  --tw-gradient-to: hsl(var(--accent) / 0.9) var(--tw-gradient-to-position);\n}\r\n.hover\\:to-primary:hover {\n  --tw-gradient-to: hsl(var(--primary)) var(--tw-gradient-to-position);\n}\r\n.hover\\:to-purple-700:hover {\n  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);\n}\r\n.hover\\:text-accent-foreground:hover {\n  color: hsl(var(--accent-foreground));\n}\r\n.hover\\:text-blue-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-destructive:hover {\n  color: hsl(var(--destructive));\n}\r\n.hover\\:text-foreground:hover {\n  color: hsl(var(--foreground));\n}\r\n.hover\\:text-gray-600:hover {\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-green-800:hover {\n  --tw-text-opacity: 1;\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-orange-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(194 65 12 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-orange-800:hover {\n  --tw-text-opacity: 1;\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-primary:hover {\n  color: hsl(var(--primary));\n}\r\n.hover\\:text-primary-foreground:hover {\n  color: hsl(var(--primary-foreground));\n}\r\n.hover\\:text-primary\\/80:hover {\n  color: hsl(var(--primary) / 0.8);\n}\r\n.hover\\:text-red-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-red-900:hover {\n  --tw-text-opacity: 1;\n  color: rgb(127 29 29 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:underline:hover {\n  text-decoration-line: underline;\n}\r\n.hover\\:opacity-100:hover {\n  opacity: 1;\n}\r\n.hover\\:opacity-80:hover {\n  opacity: 0.8;\n}\r\n.hover\\:shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-accent\\)\\)\\]:hover {\n  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));\n  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:shadow-lg:hover {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:shadow-md:hover {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:shadow-sm:hover {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:shadow-xl:hover {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:ring-primary\\/40:hover {\n  --tw-ring-color: hsl(var(--primary) / 0.4);\n}\r\n.focus\\:border-amber-500:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));\n}\r\n.focus\\:border-blue-500:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\r\n.focus\\:border-destructive:focus {\n  border-color: hsl(var(--destructive));\n}\r\n.focus\\:border-green-500:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\n}\r\n.focus\\:bg-accent:focus {\n  background-color: hsl(var(--accent));\n}\r\n.focus\\:bg-primary:focus {\n  background-color: hsl(var(--primary));\n}\r\n.focus\\:text-accent-foreground:focus {\n  color: hsl(var(--accent-foreground));\n}\r\n.focus\\:text-destructive:focus {\n  color: hsl(var(--destructive));\n}\r\n.focus\\:text-primary:focus {\n  color: hsl(var(--primary));\n}\r\n.focus\\:text-primary-foreground:focus {\n  color: hsl(var(--primary-foreground));\n}\r\n.focus\\:text-red-600:focus {\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\r\n.focus\\:opacity-100:focus {\n  opacity: 1;\n}\r\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.focus\\:ring-amber-500\\/20:focus {\n  --tw-ring-color: rgb(245 158 11 / 0.2);\n}\r\n.focus\\:ring-blue-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n}\r\n.focus\\:ring-destructive\\/20:focus {\n  --tw-ring-color: hsl(var(--destructive) / 0.2);\n}\r\n.focus\\:ring-green-500\\/20:focus {\n  --tw-ring-color: rgb(34 197 94 / 0.2);\n}\r\n.focus\\:ring-primary\\/20:focus {\n  --tw-ring-color: hsl(var(--primary) / 0.2);\n}\r\n.focus\\:ring-ring:focus {\n  --tw-ring-color: hsl(var(--ring));\n}\r\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\r\n.focus-visible\\:outline-none:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.focus-visible\\:ring-2:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.focus-visible\\:ring-destructive\\/20:focus-visible {\n  --tw-ring-color: hsl(var(--destructive) / 0.2);\n}\r\n.focus-visible\\:ring-green-500\\/20:focus-visible {\n  --tw-ring-color: rgb(34 197 94 / 0.2);\n}\r\n.focus-visible\\:ring-primary\\/20:focus-visible {\n  --tw-ring-color: hsl(var(--primary) / 0.2);\n}\r\n.focus-visible\\:ring-ring:focus-visible {\n  --tw-ring-color: hsl(var(--ring));\n}\r\n.focus-visible\\:ring-offset-2:focus-visible {\n  --tw-ring-offset-width: 2px;\n}\r\n.focus-visible\\:ring-offset-background:focus-visible {\n  --tw-ring-offset-color: hsl(var(--background));\n}\r\n.active\\:cursor-grabbing:active {\n  cursor: grabbing;\n}\r\n.disabled\\:pointer-events-none:disabled {\n  pointer-events: none;\n}\r\n.disabled\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\r\n.disabled\\:opacity-50:disabled {\n  opacity: 0.5;\n}\r\n.group\\/menu-item:focus-within .group-focus-within\\/menu-item\\:opacity-100 {\n  opacity: 1;\n}\r\n.group:hover .group-hover\\:translate-x-1 {\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group:hover .group-hover\\:scale-105 {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group:hover .group-hover\\:scale-110 {\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group\\/menu-item:hover .group-hover\\/menu-item\\:opacity-100 {\n  opacity: 1;\n}\r\n.group:hover .group-hover\\:opacity-100 {\n  opacity: 1;\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:border-muted\\/40 {\n  border-color: hsl(var(--muted) / 0.4);\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:text-red-300 {\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:border-destructive\\/30:hover {\n  border-color: hsl(var(--destructive) / 0.3);\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:bg-destructive:hover {\n  background-color: hsl(var(--destructive));\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:text-destructive-foreground:hover {\n  color: hsl(var(--destructive-foreground));\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:text-red-50:hover {\n  --tw-text-opacity: 1;\n  color: rgb(254 242 242 / var(--tw-text-opacity, 1));\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-destructive:focus {\n  --tw-ring-color: hsl(var(--destructive));\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-red-400:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-offset-red-600:focus {\n  --tw-ring-offset-color: #dc2626;\n}\r\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed {\n  cursor: not-allowed;\n}\r\n.peer:disabled ~ .peer-disabled\\:opacity-70 {\n  opacity: 0.7;\n}\r\n.group\\/menu-item:has([data-sidebar=menu-action]) .group-has-\\[\\[data-sidebar\\=menu-action\\]\\]\\/menu-item\\:pr-8 {\n  padding-right: 2rem;\n}\r\n.aria-disabled\\:pointer-events-none[aria-disabled=\"true\"] {\n  pointer-events: none;\n}\r\n.aria-disabled\\:opacity-50[aria-disabled=\"true\"] {\n  opacity: 0.5;\n}\r\n.aria-selected\\:bg-accent[aria-selected=\"true\"] {\n  background-color: hsl(var(--accent));\n}\r\n.aria-selected\\:bg-accent\\/50[aria-selected=\"true\"] {\n  background-color: hsl(var(--accent) / 0.5);\n}\r\n.aria-selected\\:text-accent-foreground[aria-selected=\"true\"] {\n  color: hsl(var(--accent-foreground));\n}\r\n.aria-selected\\:text-muted-foreground[aria-selected=\"true\"] {\n  color: hsl(var(--muted-foreground));\n}\r\n.aria-selected\\:opacity-100[aria-selected=\"true\"] {\n  opacity: 1;\n}\r\n.data-\\[disabled\\]\\:pointer-events-none[data-disabled] {\n  pointer-events: none;\n}\r\n.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=\"bottom\"] {\n  --tw-translate-y: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[side\\=left\\]\\:-translate-x-1[data-side=\"left\"] {\n  --tw-translate-x: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[side\\=right\\]\\:translate-x-1[data-side=\"right\"] {\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[side\\=top\\]\\:-translate-y-1[data-side=\"top\"] {\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[state\\=checked\\]\\:translate-x-5[data-state=\"checked\"] {\n  --tw-translate-x: 1.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[state\\=unchecked\\]\\:translate-x-0[data-state=\"unchecked\"] {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[swipe\\=cancel\\]\\:translate-x-0[data-swipe=\"cancel\"] {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[swipe\\=move\\]\\:translate-x-\\[var\\(--radix-toast-swipe-move-x\\)\\][data-swipe=\"move\"] {\n  --tw-translate-x: var(--radix-toast-swipe-move-x);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n@keyframes accordion-up {\n\n  from {\n    height: var(--radix-accordion-content-height);\n  }\n\n  to {\n    height: 0;\n  }\n}\r\n.data-\\[state\\=closed\\]\\:animate-accordion-up[data-state=\"closed\"] {\n  animation: accordion-up 0.2s ease-out;\n}\r\n@keyframes accordion-down {\n\n  from {\n    height: 0;\n  }\n\n  to {\n    height: var(--radix-accordion-content-height);\n  }\n}\r\n.data-\\[state\\=open\\]\\:animate-accordion-down[data-state=\"open\"] {\n  animation: accordion-down 0.2s ease-out;\n}\r\n.data-\\[state\\=checked\\]\\:border-primary[data-state=\"checked\"] {\n  border-color: hsl(var(--primary));\n}\r\n.data-\\[state\\=active\\]\\:bg-background[data-state=\"active\"] {\n  background-color: hsl(var(--background));\n}\r\n.data-\\[state\\=active\\]\\:bg-blue-50[data-state=\"active\"] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\r\n.data-\\[state\\=checked\\]\\:bg-primary[data-state=\"checked\"] {\n  background-color: hsl(var(--primary));\n}\r\n.data-\\[state\\=open\\]\\:bg-accent[data-state=\"open\"] {\n  background-color: hsl(var(--accent));\n}\r\n.data-\\[state\\=open\\]\\:bg-secondary[data-state=\"open\"] {\n  background-color: hsl(var(--secondary));\n}\r\n.data-\\[state\\=selected\\]\\:bg-muted[data-state=\"selected\"] {\n  background-color: hsl(var(--muted));\n}\r\n.data-\\[state\\=unchecked\\]\\:bg-input[data-state=\"unchecked\"] {\n  background-color: hsl(var(--input));\n}\r\n.data-\\[active\\=true\\]\\:font-medium[data-active=\"true\"] {\n  font-weight: 500;\n}\r\n.data-\\[state\\=active\\]\\:text-foreground[data-state=\"active\"] {\n  color: hsl(var(--foreground));\n}\r\n.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=\"checked\"] {\n  color: hsl(var(--primary-foreground));\n}\r\n.data-\\[state\\=open\\]\\:text-accent-foreground[data-state=\"open\"] {\n  color: hsl(var(--accent-foreground));\n}\r\n.data-\\[state\\=open\\]\\:text-muted-foreground[data-state=\"open\"] {\n  color: hsl(var(--muted-foreground));\n}\r\n.data-\\[disabled\\]\\:opacity-50[data-disabled] {\n  opacity: 0.5;\n}\r\n.data-\\[state\\=open\\]\\:opacity-100[data-state=\"open\"] {\n  opacity: 1;\n}\r\n.data-\\[state\\=active\\]\\:shadow-sm[data-state=\"active\"] {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  transition-duration: 300ms;\n}\r\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  transition-duration: 500ms;\n}\r\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n.data-\\[swipe\\=end\\]\\:animate-out[data-swipe=\"end\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"] {\n  --tw-exit-opacity: 0;\n}\r\n.data-\\[state\\=closed\\]\\:fade-out-10[data-state=\"closed\"] {\n  --tw-exit-opacity: 0.1;\n}\r\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"] {\n  --tw-enter-opacity: 0;\n}\r\n.data-\\[state\\=open\\]\\:fade-in-10[data-state=\"open\"] {\n  --tw-enter-opacity: 0.1;\n}\r\n.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"] {\n  --tw-exit-scale: .95;\n}\r\n.data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"] {\n  --tw-enter-scale: .95;\n}\r\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=\"bottom\"] {\n  --tw-enter-translate-y: -0.5rem;\n}\r\n.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=\"left\"] {\n  --tw-enter-translate-x: 0.5rem;\n}\r\n.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=\"right\"] {\n  --tw-enter-translate-x: -0.5rem;\n}\r\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=\"top\"] {\n  --tw-enter-translate-y: 0.5rem;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-bottom[data-state=\"closed\"] {\n  --tw-exit-translate-y: 100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left[data-state=\"closed\"] {\n  --tw-exit-translate-x: -100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2[data-state=\"closed\"] {\n  --tw-exit-translate-x: -50%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-right[data-state=\"closed\"] {\n  --tw-exit-translate-x: 100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-top[data-state=\"closed\"] {\n  --tw-exit-translate-y: -100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-top-\\[48\\%\\][data-state=\"closed\"] {\n  --tw-exit-translate-y: -48%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-bottom[data-state=\"open\"] {\n  --tw-enter-translate-y: 100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-left[data-state=\"open\"] {\n  --tw-enter-translate-x: -100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2[data-state=\"open\"] {\n  --tw-enter-translate-x: -50%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-right[data-state=\"open\"] {\n  --tw-enter-translate-x: 100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-top[data-state=\"open\"] {\n  --tw-enter-translate-y: -100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-top-\\[48\\%\\][data-state=\"open\"] {\n  --tw-enter-translate-y: -48%;\n}\r\n.data-\\[swipe\\=end\\]\\:slide-out-to-right-full[data-swipe=\"end\"] {\n  --tw-exit-translate-x: 100%;\n}\r\n.data-\\[swipe\\=start\\]\\:slide-out-to-left-full[data-swipe=\"start\"] {\n  --tw-exit-translate-x: -100%;\n}\r\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  animation-duration: 300ms;\n}\r\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  animation-duration: 500ms;\n}\r\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n  left: calc(var(--sidebar-width) * -1);\n}\r\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n  right: calc(var(--sidebar-width) * -1);\n}\r\n.group[data-side=\"left\"] .group-data-\\[side\\=left\\]\\:-right-4 {\n  right: -1rem;\n}\r\n.group[data-side=\"right\"] .group-data-\\[side\\=right\\]\\:left-0 {\n  left: 0px;\n}\r\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:-mt-8 {\n  margin-top: -2rem;\n}\r\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:hidden {\n  display: none;\n}\r\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:\\!size-8 {\n  width: 2rem !important;\n  height: 2rem !important;\n}\r\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:w-\\[--sidebar-width-icon\\] {\n  width: var(--sidebar-width-icon);\n}\r\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)_\\+_theme\\(spacing\\.4\\)\\)\\] {\n  width: calc(var(--sidebar-width-icon) + 1rem);\n}\r\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)_\\+_theme\\(spacing\\.4\\)_\\+2px\\)\\] {\n  width: calc(var(--sidebar-width-icon) + 1rem + 2px);\n}\r\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:w-0 {\n  width: 0px;\n}\r\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:translate-x-0 {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group[data-side=\"right\"] .group-data-\\[side\\=right\\]\\:rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:overflow-hidden {\n  overflow: hidden;\n}\r\n.group[data-variant=\"floating\"] .group-data-\\[variant\\=floating\\]\\:rounded-lg {\n  border-radius: var(--radius);\n}\r\n.group[data-variant=\"floating\"] .group-data-\\[variant\\=floating\\]\\:border {\n  border-width: 1px;\n}\r\n.group[data-side=\"left\"] .group-data-\\[side\\=left\\]\\:border-r {\n  border-right-width: 1px;\n}\r\n.group[data-side=\"right\"] .group-data-\\[side\\=right\\]\\:border-l {\n  border-left-width: 1px;\n}\r\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:\\!p-0 {\n  padding: 0px !important;\n}\r\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:\\!p-2 {\n  padding: 0.5rem !important;\n}\r\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:opacity-0 {\n  opacity: 0;\n}\r\n.group[data-variant=\"floating\"] .group-data-\\[variant\\=floating\\]\\:shadow {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:after\\:left-full::after {\n  content: var(--tw-content);\n  left: 100%;\n}\r\n.peer\\/menu-button[data-size=\"default\"] ~ .peer-data-\\[size\\=default\\]\\/menu-button\\:top-1\\.5 {\n  top: 0.375rem;\n}\r\n.peer\\/menu-button[data-size=\"lg\"] ~ .peer-data-\\[size\\=lg\\]\\/menu-button\\:top-2\\.5 {\n  top: 0.625rem;\n}\r\n.peer\\/menu-button[data-size=\"sm\"] ~ .peer-data-\\[size\\=sm\\]\\/menu-button\\:top-1 {\n  top: 0.25rem;\n}\r\n.peer[data-variant=\"inset\"] ~ .peer-data-\\[variant\\=inset\\]\\:min-h-\\[calc\\(100svh-theme\\(spacing\\.4\\)\\)\\] {\n  min-height: calc(100svh - 1rem);\n}\r\n.peer[data-state=\"checked\"] ~ .peer-data-\\[state\\=checked\\]\\:border-primary {\n  border-color: hsl(var(--primary));\n}\r\n@supports (backdrop-filter: var(--tw)) {\n\n  .supports-\\[backdrop-filter\\]\\:bg-background\\/60 {\n    background-color: hsl(var(--background) / 0.6);\n  }\n}\r\n.dark\\:border-amber-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(146 64 14 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-blue-500:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-blue-500\\/20:is(.dark *) {\n  border-color: rgb(59 130 246 / 0.2);\n}\r\n.dark\\:border-blue-700:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-blue-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-blue-800\\/30:is(.dark *) {\n  border-color: rgb(30 64 175 / 0.3);\n}\r\n.dark\\:border-destructive:is(.dark *) {\n  border-color: hsl(var(--destructive));\n}\r\n.dark\\:border-gray-500\\/20:is(.dark *) {\n  border-color: rgb(107 114 128 / 0.2);\n}\r\n.dark\\:border-gray-600:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-gray-700:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-gray-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-gray-800\\/30:is(.dark *) {\n  border-color: rgb(31 41 55 / 0.3);\n}\r\n.dark\\:border-green-500\\/20:is(.dark *) {\n  border-color: rgb(34 197 94 / 0.2);\n}\r\n.dark\\:border-green-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-green-800\\/30:is(.dark *) {\n  border-color: rgb(22 101 52 / 0.3);\n}\r\n.dark\\:border-indigo-500\\/20:is(.dark *) {\n  border-color: rgb(99 102 241 / 0.2);\n}\r\n.dark\\:border-indigo-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(55 48 163 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-indigo-800\\/30:is(.dark *) {\n  border-color: rgb(55 48 163 / 0.3);\n}\r\n.dark\\:border-orange-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(154 52 18 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-purple-500\\/20:is(.dark *) {\n  border-color: rgb(168 85 247 / 0.2);\n}\r\n.dark\\:border-purple-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(107 33 168 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-red-500\\/20:is(.dark *) {\n  border-color: rgb(239 68 68 / 0.2);\n}\r\n.dark\\:border-red-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-red-800\\/30:is(.dark *) {\n  border-color: rgb(153 27 27 / 0.3);\n}\r\n.dark\\:border-teal-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(17 94 89 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-yellow-500:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-yellow-500\\/20:is(.dark *) {\n  border-color: rgb(234 179 8 / 0.2);\n}\r\n.dark\\:border-yellow-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(133 77 14 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-yellow-800\\/30:is(.dark *) {\n  border-color: rgb(133 77 14 / 0.3);\n}\r\n.dark\\:bg-amber-900\\/20:is(.dark *) {\n  background-color: rgb(120 53 15 / 0.2);\n}\r\n.dark\\:bg-amber-900\\/30:is(.dark *) {\n  background-color: rgb(120 53 15 / 0.3);\n}\r\n.dark\\:bg-blue-500\\/10:is(.dark *) {\n  background-color: rgb(59 130 246 / 0.1);\n}\r\n.dark\\:bg-blue-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-blue-900\\/20:is(.dark *) {\n  background-color: rgb(30 58 138 / 0.2);\n}\r\n.dark\\:bg-blue-900\\/30:is(.dark *) {\n  background-color: rgb(30 58 138 / 0.3);\n}\r\n.dark\\:bg-blue-950:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(23 37 84 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-blue-950\\/30:is(.dark *) {\n  background-color: rgb(23 37 84 / 0.3);\n}\r\n.dark\\:bg-cyan-900\\/30:is(.dark *) {\n  background-color: rgb(22 78 99 / 0.3);\n}\r\n.dark\\:bg-emerald-900\\/30:is(.dark *) {\n  background-color: rgb(6 78 59 / 0.3);\n}\r\n.dark\\:bg-gray-500\\/10:is(.dark *) {\n  background-color: rgb(107 114 128 / 0.1);\n}\r\n.dark\\:bg-gray-700:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-gray-800:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-gray-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-gray-900\\/20:is(.dark *) {\n  background-color: rgb(17 24 39 / 0.2);\n}\r\n.dark\\:bg-gray-900\\/30:is(.dark *) {\n  background-color: rgb(17 24 39 / 0.3);\n}\r\n.dark\\:bg-gray-950:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(3 7 18 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-green-500\\/10:is(.dark *) {\n  background-color: rgb(34 197 94 / 0.1);\n}\r\n.dark\\:bg-green-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-green-900\\/20:is(.dark *) {\n  background-color: rgb(20 83 45 / 0.2);\n}\r\n.dark\\:bg-green-900\\/30:is(.dark *) {\n  background-color: rgb(20 83 45 / 0.3);\n}\r\n.dark\\:bg-green-950:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(5 46 22 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-indigo-500\\/10:is(.dark *) {\n  background-color: rgb(99 102 241 / 0.1);\n}\r\n.dark\\:bg-indigo-900\\/20:is(.dark *) {\n  background-color: rgb(49 46 129 / 0.2);\n}\r\n.dark\\:bg-indigo-900\\/30:is(.dark *) {\n  background-color: rgb(49 46 129 / 0.3);\n}\r\n.dark\\:bg-indigo-950:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 27 75 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-orange-900\\/20:is(.dark *) {\n  background-color: rgb(124 45 18 / 0.2);\n}\r\n.dark\\:bg-orange-900\\/30:is(.dark *) {\n  background-color: rgb(124 45 18 / 0.3);\n}\r\n.dark\\:bg-orange-950:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(67 20 7 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-purple-500\\/10:is(.dark *) {\n  background-color: rgb(168 85 247 / 0.1);\n}\r\n.dark\\:bg-purple-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-purple-900\\/20:is(.dark *) {\n  background-color: rgb(88 28 135 / 0.2);\n}\r\n.dark\\:bg-purple-900\\/30:is(.dark *) {\n  background-color: rgb(88 28 135 / 0.3);\n}\r\n.dark\\:bg-purple-950:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 7 100 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-red-500\\/10:is(.dark *) {\n  background-color: rgb(239 68 68 / 0.1);\n}\r\n.dark\\:bg-red-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-red-900\\/20:is(.dark *) {\n  background-color: rgb(127 29 29 / 0.2);\n}\r\n.dark\\:bg-red-900\\/30:is(.dark *) {\n  background-color: rgb(127 29 29 / 0.3);\n}\r\n.dark\\:bg-red-900\\/50:is(.dark *) {\n  background-color: rgb(127 29 29 / 0.5);\n}\r\n.dark\\:bg-red-950:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(69 10 10 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-slate-800:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-slate-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-teal-950:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(4 47 46 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-yellow-500\\/10:is(.dark *) {\n  background-color: rgb(234 179 8 / 0.1);\n}\r\n.dark\\:bg-yellow-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(113 63 18 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-yellow-900\\/20:is(.dark *) {\n  background-color: rgb(113 63 18 / 0.2);\n}\r\n.dark\\:bg-yellow-900\\/30:is(.dark *) {\n  background-color: rgb(113 63 18 / 0.3);\n}\r\n.dark\\:bg-yellow-950:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(66 32 6 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:from-blue-500\\/10:is(.dark *) {\n  --tw-gradient-from: rgb(59 130 246 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.dark\\:from-blue-900\\/20:is(.dark *) {\n  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.dark\\:from-gray-500\\/10:is(.dark *) {\n  --tw-gradient-from: rgb(107 114 128 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(107 114 128 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.dark\\:from-gray-800:is(.dark *) {\n  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.dark\\:from-green-500\\/10:is(.dark *) {\n  --tw-gradient-from: rgb(34 197 94 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.dark\\:from-green-900\\/20:is(.dark *) {\n  --tw-gradient-from: rgb(20 83 45 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.dark\\:from-purple-500\\/10:is(.dark *) {\n  --tw-gradient-from: rgb(168 85 247 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.dark\\:from-red-500\\/10:is(.dark *) {\n  --tw-gradient-from: rgb(239 68 68 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.dark\\:from-yellow-500\\/10:is(.dark *) {\n  --tw-gradient-from: rgb(234 179 8 / 0.1) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.dark\\:to-amber-500\\/10:is(.dark *) {\n  --tw-gradient-to: rgb(245 158 11 / 0.1) var(--tw-gradient-to-position);\n}\r\n.dark\\:to-blue-600\\/10:is(.dark *) {\n  --tw-gradient-to: rgb(37 99 235 / 0.1) var(--tw-gradient-to-position);\n}\r\n.dark\\:to-emerald-500\\/10:is(.dark *) {\n  --tw-gradient-to: rgb(16 185 129 / 0.1) var(--tw-gradient-to-position);\n}\r\n.dark\\:to-emerald-900\\/20:is(.dark *) {\n  --tw-gradient-to: rgb(6 78 59 / 0.2) var(--tw-gradient-to-position);\n}\r\n.dark\\:to-gray-900:is(.dark *) {\n  --tw-gradient-to: #111827 var(--tw-gradient-to-position);\n}\r\n.dark\\:to-indigo-900\\/20:is(.dark *) {\n  --tw-gradient-to: rgb(49 46 129 / 0.2) var(--tw-gradient-to-position);\n}\r\n.dark\\:to-rose-500\\/10:is(.dark *) {\n  --tw-gradient-to: rgb(244 63 94 / 0.1) var(--tw-gradient-to-position);\n}\r\n.dark\\:to-slate-500\\/10:is(.dark *) {\n  --tw-gradient-to: rgb(100 116 139 / 0.1) var(--tw-gradient-to-position);\n}\r\n.dark\\:to-violet-500\\/10:is(.dark *) {\n  --tw-gradient-to: rgb(139 92 246 / 0.1) var(--tw-gradient-to-position);\n}\r\n.dark\\:text-amber-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(253 230 138 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-amber-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(251 191 36 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-blue-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(191 219 254 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-blue-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-blue-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-cyan-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(34 211 238 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-emerald-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(52 211 153 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-gray-100:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(243 244 246 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-gray-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-gray-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-gray-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-gray-500:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-green-100:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(220 252 231 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-green-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(187 247 208 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-green-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(134 239 172 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-green-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-indigo-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(199 210 254 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-indigo-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(129 140 248 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-orange-100:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(255 237 213 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-orange-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(254 215 170 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-orange-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(253 186 116 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-orange-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(251 146 60 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-purple-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(233 213 255 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-purple-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(216 180 254 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-purple-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(192 132 252 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-red-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-red-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-red-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-slate-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-teal-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(153 246 228 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-white:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-yellow-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(254 240 138 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-yellow-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(253 224 71 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-yellow-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:hover\\:bg-blue-900\\/10:hover:is(.dark *) {\n  background-color: rgb(30 58 138 / 0.1);\n}\r\n.dark\\:hover\\:bg-gray-800:hover:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:hover\\:bg-gray-800\\/50:hover:is(.dark *) {\n  background-color: rgb(31 41 55 / 0.5);\n}\r\n.dark\\:hover\\:bg-green-900\\/10:hover:is(.dark *) {\n  background-color: rgb(20 83 45 / 0.1);\n}\r\n.dark\\:hover\\:text-gray-300:hover:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:hover\\:text-orange-200:hover:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(254 215 170 / var(--tw-text-opacity, 1));\n}\r\n@media (min-width: 640px) {\n\n  .sm\\:bottom-0 {\n    bottom: 0px;\n  }\n\n  .sm\\:right-0 {\n    right: 0px;\n  }\n\n  .sm\\:top-auto {\n    top: auto;\n  }\n\n  .sm\\:col-span-3 {\n    grid-column: span 3 / span 3;\n  }\n\n  .sm\\:mt-0 {\n    margin-top: 0px;\n  }\n\n  .sm\\:inline {\n    display: inline;\n  }\n\n  .sm\\:flex {\n    display: flex;\n  }\n\n  .sm\\:hidden {\n    display: none;\n  }\n\n  .sm\\:max-w-\\[425px\\] {\n    max-width: 425px;\n  }\n\n  .sm\\:max-w-\\[600px\\] {\n    max-width: 600px;\n  }\n\n  .sm\\:max-w-md {\n    max-width: 28rem;\n  }\n\n  .sm\\:max-w-sm {\n    max-width: 24rem;\n  }\n\n  .sm\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .sm\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .sm\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .sm\\:flex-row {\n    flex-direction: row;\n  }\n\n  .sm\\:flex-col {\n    flex-direction: column;\n  }\n\n  .sm\\:items-center {\n    align-items: center;\n  }\n\n  .sm\\:justify-end {\n    justify-content: flex-end;\n  }\n\n  .sm\\:justify-between {\n    justify-content: space-between;\n  }\n\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-x-4 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\n  }\n\n  .sm\\:self-center {\n    align-self: center;\n  }\n\n  .sm\\:rounded-lg {\n    border-radius: var(--radius);\n  }\n\n  .sm\\:p-4 {\n    padding: 1rem;\n  }\n\n  .sm\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .sm\\:text-left {\n    text-align: left;\n  }\n}\r\n@media (min-width: 768px) {\n\n  .md\\:col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n\n  .md\\:mr-2 {\n    margin-right: 0.5rem;\n  }\n\n  .md\\:block {\n    display: block;\n  }\n\n  .md\\:inline {\n    display: inline;\n  }\n\n  .md\\:flex {\n    display: flex;\n  }\n\n  .md\\:hidden {\n    display: none;\n  }\n\n  .md\\:w-32 {\n    width: 8rem;\n  }\n\n  .md\\:max-w-\\[420px\\] {\n    max-width: 420px;\n  }\n\n  .md\\:columns-2 {\n    columns: 2;\n  }\n\n  .md\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-5 {\n    grid-template-columns: repeat(5, minmax(0, 1fr));\n  }\n\n  .md\\:flex-row {\n    flex-direction: row;\n  }\n\n  .md\\:items-center {\n    align-items: center;\n  }\n\n  .md\\:justify-between {\n    justify-content: space-between;\n  }\n\n  .md\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .md\\:p-6 {\n    padding: 1.5rem;\n  }\n\n  .md\\:px-3 {\n    padding-left: 0.75rem;\n    padding-right: 0.75rem;\n  }\n\n  .md\\:text-3xl {\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n\n  .md\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n\n  .md\\:opacity-0 {\n    opacity: 0;\n  }\n\n  .after\\:md\\:hidden::after {\n    content: var(--tw-content);\n    display: none;\n  }\n\n  .peer[data-variant=\"inset\"] ~ .md\\:peer-data-\\[variant\\=inset\\]\\:m-2 {\n    margin: 0.5rem;\n  }\n\n  .peer[data-state=\"collapsed\"][data-variant=\"inset\"] ~ .md\\:peer-data-\\[state\\=collapsed\\]\\:peer-data-\\[variant\\=inset\\]\\:ml-2 {\n    margin-left: 0.5rem;\n  }\n\n  .peer[data-variant=\"inset\"] ~ .md\\:peer-data-\\[variant\\=inset\\]\\:ml-0 {\n    margin-left: 0px;\n  }\n\n  .peer[data-variant=\"inset\"] ~ .md\\:peer-data-\\[variant\\=inset\\]\\:rounded-xl {\n    border-radius: 0.75rem;\n  }\n\n  .peer[data-variant=\"inset\"] ~ .md\\:peer-data-\\[variant\\=inset\\]\\:shadow {\n    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  }\n}\r\n@media (min-width: 1024px) {\n\n  .lg\\:static {\n    position: static;\n  }\n\n  .lg\\:z-auto {\n    z-index: auto;\n  }\n\n  .lg\\:col-span-1 {\n    grid-column: span 1 / span 1;\n  }\n\n  .lg\\:col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n\n  .lg\\:col-span-3 {\n    grid-column: span 3 / span 3;\n  }\n\n  .lg\\:col-span-4 {\n    grid-column: span 4 / span 4;\n  }\n\n  .lg\\:col-span-6 {\n    grid-column: span 6 / span 6;\n  }\n\n  .lg\\:col-span-8 {\n    grid-column: span 8 / span 8;\n  }\n\n  .lg\\:mb-0 {\n    margin-bottom: 0px;\n  }\n\n  .lg\\:flex {\n    display: flex;\n  }\n\n  .lg\\:grid {\n    display: grid;\n  }\n\n  .lg\\:hidden {\n    display: none;\n  }\n\n  .lg\\:w-full {\n    width: 100%;\n  }\n\n  .lg\\:translate-x-0 {\n    --tw-translate-x: 0px;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\n\n  .lg\\:columns-3 {\n    columns: 3;\n  }\n\n  .lg\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-5 {\n    grid-template-columns: repeat(5, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-6 {\n    grid-template-columns: repeat(6, minmax(0, 1fr));\n  }\n\n  .lg\\:items-center {\n    align-items: center;\n  }\n\n  .lg\\:justify-center {\n    justify-content: center;\n  }\n\n  .lg\\:justify-between {\n    justify-content: space-between;\n  }\n\n  .lg\\:space-x-8 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(2rem * var(--tw-space-x-reverse));\n    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .lg\\:p-8 {\n    padding: 2rem;\n  }\n\n  .lg\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .lg\\:px-8 {\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n}\r\n@media (min-width: 1280px) {\n\n  .xl\\:col-span-4 {\n    grid-column: span 4 / span 4;\n  }\n\n  .xl\\:columns-4 {\n    columns: 4;\n  }\n\n  .xl\\:grid-cols-12 {\n    grid-template-columns: repeat(12, minmax(0, 1fr));\n  }\n\n  .xl\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .xl\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .xl\\:grid-cols-5 {\n    grid-template-columns: repeat(5, minmax(0, 1fr));\n  }\n\n  .xl\\:grid-cols-6 {\n    grid-template-columns: repeat(6, minmax(0, 1fr));\n  }\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent:has([aria-selected]) {\n  background-color: hsl(var(--accent));\n}\r\n.first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md:has([aria-selected]):first-child {\n  border-top-left-radius: calc(var(--radius) - 2px);\n  border-bottom-left-radius: calc(var(--radius) - 2px);\n}\r\n.last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md:has([aria-selected]):last-child {\n  border-top-right-radius: calc(var(--radius) - 2px);\n  border-bottom-right-radius: calc(var(--radius) - 2px);\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-outside\\)\\]\\:bg-accent\\/50:has([aria-selected].day-outside) {\n  background-color: hsl(var(--accent) / 0.5);\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md:has([aria-selected].day-range-end) {\n  border-top-right-radius: calc(var(--radius) - 2px);\n  border-bottom-right-radius: calc(var(--radius) - 2px);\n}\r\n.\\[\\&\\:has\\(\\[data-state\\=checked\\]\\)\\]\\:border-primary:has([data-state=checked]) {\n  border-color: hsl(var(--primary));\n}\r\n.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]) {\n  padding-right: 0px;\n}\r\n.\\[\\&\\>button\\]\\:hidden>button {\n  display: none;\n}\r\n.\\[\\&\\>span\\:last-child\\]\\:truncate>span:last-child {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\r\n.\\[\\&\\>span\\]\\:line-clamp-1>span {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\r\n.\\[\\&\\>svg\\+div\\]\\:translate-y-\\[-3px\\]>svg+div {\n  --tw-translate-y: -3px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.\\[\\&\\>svg\\]\\:absolute>svg {\n  position: absolute;\n}\r\n.\\[\\&\\>svg\\]\\:left-4>svg {\n  left: 1rem;\n}\r\n.\\[\\&\\>svg\\]\\:top-4>svg {\n  top: 1rem;\n}\r\n.\\[\\&\\>svg\\]\\:size-3\\.5>svg {\n  width: 0.875rem;\n  height: 0.875rem;\n}\r\n.\\[\\&\\>svg\\]\\:size-4>svg {\n  width: 1rem;\n  height: 1rem;\n}\r\n.\\[\\&\\>svg\\]\\:h-2\\.5>svg {\n  height: 0.625rem;\n}\r\n.\\[\\&\\>svg\\]\\:h-3>svg {\n  height: 0.75rem;\n}\r\n.\\[\\&\\>svg\\]\\:w-2\\.5>svg {\n  width: 0.625rem;\n}\r\n.\\[\\&\\>svg\\]\\:w-3>svg {\n  width: 0.75rem;\n}\r\n.\\[\\&\\>svg\\]\\:shrink-0>svg {\n  flex-shrink: 0;\n}\r\n.\\[\\&\\>svg\\]\\:text-destructive>svg {\n  color: hsl(var(--destructive));\n}\r\n.\\[\\&\\>svg\\]\\:text-foreground>svg {\n  color: hsl(var(--foreground));\n}\r\n.\\[\\&\\>svg\\]\\:text-muted-foreground>svg {\n  color: hsl(var(--muted-foreground));\n}\r\n.\\[\\&\\>svg\\~\\*\\]\\:pl-7>svg~* {\n  padding-left: 1.75rem;\n}\r\n.\\[\\&\\>tr\\]\\:last\\:border-b-0:last-child>tr {\n  border-bottom-width: 0px;\n}\r\n.\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180[data-state=open]>svg {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground .recharts-cartesian-axis-tick text {\n  fill: hsl(var(--muted-foreground));\n}\r\n.\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 .recharts-cartesian-grid line[stroke='#ccc'] {\n  stroke: hsl(var(--border) / 0.5);\n}\r\n.\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border .recharts-curve.recharts-tooltip-cursor {\n  stroke: hsl(var(--border));\n}\r\n.\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-dot[stroke='#fff'] {\n  stroke: transparent;\n}\r\n.\\[\\&_\\.recharts-layer\\]\\:outline-none .recharts-layer {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-polar-grid [stroke='#ccc'] {\n  stroke: hsl(var(--border));\n}\r\n.\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted .recharts-radial-bar-background-sector {\n  fill: hsl(var(--muted));\n}\r\n.\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {\n  fill: hsl(var(--muted));\n}\r\n.\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-reference-line [stroke='#ccc'] {\n  stroke: hsl(var(--border));\n}\r\n.\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-sector[stroke='#fff'] {\n  stroke: transparent;\n}\r\n.\\[\\&_\\.recharts-sector\\]\\:outline-none .recharts-sector {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.\\[\\&_\\.recharts-surface\\]\\:outline-none .recharts-surface {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.\\[\\&_p\\]\\:leading-relaxed p {\n  line-height: 1.625;\n}\r\n.\\[\\&_svg\\]\\:pointer-events-none svg {\n  pointer-events: none;\n}\r\n.\\[\\&_svg\\]\\:size-4 svg {\n  width: 1rem;\n  height: 1rem;\n}\r\n.\\[\\&_svg\\]\\:shrink-0 svg {\n  flex-shrink: 0;\n}\r\n.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child {\n  border-width: 0px;\n}\r\n.\\[\\&_tr\\]\\:border-b tr {\n  border-bottom-width: 1px;\n}\r\n[data-side=left][data-collapsible=offcanvas] .\\[\\[data-side\\=left\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-right-2 {\n  right: -0.5rem;\n}\r\n[data-side=left][data-state=collapsed] .\\[\\[data-side\\=left\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-e-resize {\n  cursor: e-resize;\n}\r\n[data-side=left] .\\[\\[data-side\\=left\\]_\\&\\]\\:cursor-w-resize {\n  cursor: w-resize;\n}\r\n[data-side=right][data-collapsible=offcanvas] .\\[\\[data-side\\=right\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-left-2 {\n  left: -0.5rem;\n}\r\n[data-side=right][data-state=collapsed] .\\[\\[data-side\\=right\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-w-resize {\n  cursor: w-resize;\n}\r\n[data-side=right] .\\[\\[data-side\\=right\\]_\\&\\]\\:cursor-e-resize {\n  cursor: e-resize;\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA;;;;;AASA;;;;AAeA;;;;;;;;;;;AAiBA;;;;;AAWA;;;;;;AAUA;;;;AAQA;;;;;AAcA;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAWA;;;;AAQA;;;;AASA;;;;;AAUA;;;;AAQA;;;;AAUA;;;;;AAgBA;;;;;AAOA;;;;AAGE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;;;;AAGA;;;;;AAIF;;;;;;;;AAOA;EAEE;;;;;AAIF;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAMA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;;;AAOA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAUA;EAEE;;;;;;;EAOA;;;;;;EAOA;;;;;;;;EAQA;;;;;;;;EAUA;;;;;;;;;;EAkBA;;;;;;;EAqBA;;;;;;;;;;EAsBA;;;;EAIA;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAKA;;;;;;;;EAYA;;;;;;EAOA;;;;;;AAKF;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;AAMA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAQA;;;;;AAIA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;;;;;;AAkBA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;EAEE;;;;;AAIF;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;AAIF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;;AAMF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;;EAKA;;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAIF;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;;AAMA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAMA;;;;AAGA;;;;AAGA;;;;;AAQA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA"}}]}