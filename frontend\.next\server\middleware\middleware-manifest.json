{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/[root-of-the-server]__752a1ada._.js", "server/edge/chunks/edge-wrapper_14765a8d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "LHhmG87TpRro2qByrxww8dCzqBMX4SPVnL50r2YdI1Y=", "__NEXT_PREVIEW_MODE_ID": "0c3baf93f0d1e5dd5afc3ef6c786a0dc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a31fa63c78848443a2f8fe7f79112ef3df09b6d23848abac96498f8858162cc0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d960db51b241f3fdb93d46539f1761e3b32855c9768a1cb1fdd9d6ef048c6e32"}}}, "instrumentation": null, "functions": {}}