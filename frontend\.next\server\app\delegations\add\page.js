(()=>{var e={};e.id=5534,e.ids=[5534],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3940:(e,r,t)=>{"use strict";t.d(r,{O_:()=>o,t6:()=>n});var s=t(43210),i=t(49278);function n(){let e=(0,s.useCallback)((e,r)=>i.JP.success(e,r),[]),r=(0,s.useCallback)((e,r)=>i.JP.error(e,r),[]),t=(0,s.useCallback)((e,r)=>i.JP.info(e,r),[]),n=(0,s.useCallback)(r=>e(r?.successTitle||"Success",r?.successDescription||"Operation completed successfully"),[e]),o=(0,s.useCallback)((e,t)=>{let s=e instanceof Error?e.message:e;return r(t?.errorTitle||"Error",t?.errorDescription||s||"An unexpected error occurred")},[r]);return{showSuccess:e,showError:r,showInfo:t,showFormSuccess:n,showFormError:o}}function o(e){let r;switch(e){case"employee":r=t(49278).Ok;break;case"vehicle":r=t(49278).G7;break;case"task":r=t(49278).z0;break;case"delegation":r=t(49278).Qu;break;default:throw Error(`Unknown entity type: ${e}`)}return function(e,r){let{showFormSuccess:t,showFormError:o}=n(),a=r||(e?(0,i.iw)(e):null),c=(0,s.useCallback)(e=>a?a.entityCreated(e):t({successTitle:"Created",successDescription:"Item has been created successfully"}),[a,t]),d=(0,s.useCallback)(e=>a?a.entityUpdated(e):t({successTitle:"Updated",successDescription:"Item has been updated successfully"}),[a,t]),l=(0,s.useCallback)(e=>a?a.entityDeleted(e):t({successTitle:"Deleted",successDescription:"Item has been deleted successfully"}),[a,t]),u=(0,s.useCallback)(e=>{if(a){let r=e instanceof Error?e.message:e;return a.entityCreationError(r)}return o(e,{errorTitle:"Creation Failed"})},[a,o]);return{showEntityCreated:c,showEntityUpdated:d,showEntityDeleted:l,showEntityCreationError:u,showEntityUpdateError:(0,s.useCallback)(e=>{if(a){let r=e instanceof Error?e.message:e;return a.entityUpdateError(r)}return o(e,{errorTitle:"Update Failed"})},[a,o]),showEntityDeletionError:(0,s.useCallback)(e=>{if(a){let r=e instanceof Error?e.message:e;return a.entityDeletionError(r)}return o(e,{errorTitle:"Deletion Failed"})},[a,o]),showFormSuccess:t,showFormError:o}}(void 0,r)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25653:(e,r,t)=>{Promise.resolve().then(t.bind(t,41290))},27040:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\projects\\\\CarServiceTrackingSystem\\\\TrackerSystem\\\\frontend\\\\src\\\\app\\\\delegations\\\\add\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\add\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35381:(e,r,t)=>{Promise.resolve().then(t.bind(t,27040))},41290:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(60687),i=t(33886),n=t(16189),o=t(51356),a=t(48041),c=t(3940),d=t(63502);function l(){let e=(0,n.useRouter)(),{showEntityCreated:r,showEntityCreationError:t}=(0,c.O_)("delegation"),{error:l,isPending:u,mutateAsync:p}=(0,d.er)(),m=async s=>{let i={...s,delegates:s.delegates.map(e=>({name:e.name,notes:e.notes??"",title:e.title})),drivers:s.driverEmployeeIds.map(e=>({employeeId:e})),durationFrom:new Date(s.durationFrom).toISOString(),durationTo:new Date(s.durationTo).toISOString(),escorts:s.escortEmployeeIds.map(e=>({employeeId:e})),notes:s.notes??"",status:s.status.replace(" ","_"),vehicles:s.vehicleIds.map(e=>({assignedDate:new Date(s.durationFrom).toISOString(),returnDate:new Date(s.durationTo).toISOString(),vehicleId:e}))};try{await p(i);let t={event:s.eventName,location:s.location};r(t),e.push("/delegations")}catch(r){console.error("Error adding delegation:",r);let e="Failed to add delegation. Please try again.";r instanceof Error?e=r.message:l instanceof Error&&(e=l.message),t(e)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(a.z,{description:"Enter the details for the new delegation or event.",icon:i.A,title:"Add New Delegation"}),l&&(0,s.jsxs)("p",{className:"rounded-md bg-red-100 p-3 text-red-500",children:["Error: ",l.message]}),(0,s.jsx)(o.GK,{isEditing:!1,onSubmit:m})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68165:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>d});var s=t(65239),i=t(48088),n=t(88170),o=t.n(n),a=t(30893),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);t.d(r,c);let d={children:["",{children:["delegations",{children:["add",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,27040)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\add\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,34595)),"C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\projects\\CarServiceTrackingSystem\\TrackerSystem\\frontend\\src\\app\\delegations\\add\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/delegations/add/page",pathname:"/delegations/add",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,211,1658,8390,2670,9275,6013,6362,7113,417,5941,7055,9599,3502,6413,3089,9004],()=>t(68165));module.exports=s})();