{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/tables/ReportingDataTable.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/tables/ReportingDataTable.tsx\r\n\r\n'use client';\r\n\r\nimport React, { useMemo, useState } from 'react';\r\nimport {\r\n  ColumnDef,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getFilteredRowModel,\r\n  getPaginationRowModel,\r\n  getSortedRowModel,\r\n  useReactTable,\r\n  SortingState,\r\n  ColumnFiltersState,\r\n  VisibilityState,\r\n} from '@tanstack/react-table';\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@/components/ui/table';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@/components/ui/card';\r\nimport {\r\n  ChevronDown,\r\n  Download,\r\n  Filter,\r\n  Search,\r\n  Settings,\r\n  ArrowUpDown,\r\n  ArrowUp,\r\n  ArrowDown,\r\n} from 'lucide-react';\r\n\r\n/**\r\n * Generic data interface for reporting table\r\n */\r\nexport interface ReportingTableData {\r\n  id: string;\r\n  [key: string]: any;\r\n}\r\n\r\n/**\r\n * Props for ReportingDataTable component\r\n */\r\ninterface ReportingDataTableProps<TData extends ReportingTableData> {\r\n  data: TData[];\r\n  columns: ColumnDef<TData>[];\r\n  title?: string;\r\n  description?: string;\r\n  loading?: boolean;\r\n  error?: string | undefined;\r\n  searchable?: boolean;\r\n  filterable?: boolean;\r\n  exportable?: boolean;\r\n  pagination?: boolean;\r\n  pageSize?: number;\r\n  className?: string;\r\n  onRowClick?: ((row: TData) => void) | undefined;\r\n  onExport?: ((data: TData[]) => void) | undefined;\r\n}\r\n\r\n/**\r\n * Loading skeleton for data table\r\n */\r\nconst TableLoadingSkeleton = ({ columns }: { columns: number }) => (\r\n  <div className=\"animate-pulse\">\r\n    <div className=\"border rounded-lg\">\r\n      {/* Header */}\r\n      <div className=\"border-b p-4\">\r\n        <div className=\"flex gap-4\">\r\n          {Array.from({ length: columns }).map((_, i) => (\r\n            <div key={i} className=\"h-4 bg-gray-200 rounded flex-1\"></div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Rows */}\r\n      {Array.from({ length: 5 }).map((_, rowIndex) => (\r\n        <div key={rowIndex} className=\"border-b p-4\">\r\n          <div className=\"flex gap-4\">\r\n            {Array.from({ length: columns }).map((_, colIndex) => (\r\n              <div\r\n                key={colIndex}\r\n                className=\"h-4 bg-gray-100 rounded flex-1\"\r\n              ></div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  </div>\r\n);\r\n\r\n/**\r\n * Error display for data table\r\n */\r\nconst TableErrorDisplay = ({ error }: { error: string }) => (\r\n  <div className=\"flex flex-col items-center justify-center h-64 text-gray-500 border rounded-lg\">\r\n    <div className=\"text-4xl mb-2\">📋</div>\r\n    <p className=\"text-sm\">Failed to load table data</p>\r\n    <p className=\"text-xs text-gray-400 mt-1\">{error}</p>\r\n  </div>\r\n);\r\n\r\n/**\r\n * Empty state for data table\r\n */\r\nconst TableEmptyState = () => (\r\n  <div className=\"flex flex-col items-center justify-center h-64 text-gray-500 border rounded-lg\">\r\n    <div className=\"text-4xl mb-2\">📋</div>\r\n    <p className=\"text-sm\">No data available</p>\r\n  </div>\r\n);\r\n\r\n/**\r\n * ReportingDataTable Component\r\n *\r\n * A comprehensive, reusable data table component for reporting data.\r\n * Built with TanStack Table for advanced features and performance.\r\n *\r\n * Features:\r\n * - Sorting, filtering, and pagination\r\n * - Column visibility controls\r\n * - Global search\r\n * - Export functionality\r\n * - Loading and error states\r\n * - Responsive design\r\n * - Row click handling\r\n *\r\n * @param props - Component props\r\n * @returns JSX element\r\n */\r\nexport function ReportingDataTable<TData extends ReportingTableData>({\r\n  data,\r\n  columns,\r\n  title = 'Data Table',\r\n  description,\r\n  loading = false,\r\n  error,\r\n  searchable = true,\r\n  filterable = true,\r\n  exportable = true,\r\n  pagination = true,\r\n  pageSize = 10,\r\n  className = '',\r\n  onRowClick,\r\n  onExport,\r\n}: ReportingDataTableProps<TData>) {\r\n  const [sorting, setSorting] = useState<SortingState>([]);\r\n  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);\r\n  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});\r\n  const [globalFilter, setGlobalFilter] = useState('');\r\n\r\n  // Enhanced columns with sorting icons\r\n  const enhancedColumns = useMemo(() => {\r\n    return columns.map(column => ({\r\n      ...column,\r\n      header: (headerContext: any) => {\r\n        const { column: col } = headerContext;\r\n        const originalHeader =\r\n          typeof column.header === 'function'\r\n            ? column.header(headerContext)\r\n            : column.header;\r\n\r\n        if (!col.getCanSort()) {\r\n          return originalHeader;\r\n        }\r\n\r\n        return (\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => col.toggleSorting(col.getIsSorted() === 'asc')}\r\n            className=\"h-auto p-0 font-semibold hover:bg-transparent\"\r\n          >\r\n            {originalHeader}\r\n            {col.getIsSorted() === 'asc' ? (\r\n              <ArrowUp className=\"ml-2 h-4 w-4\" />\r\n            ) : col.getIsSorted() === 'desc' ? (\r\n              <ArrowDown className=\"ml-2 h-4 w-4\" />\r\n            ) : (\r\n              <ArrowUpDown className=\"ml-2 h-4 w-4\" />\r\n            )}\r\n          </Button>\r\n        );\r\n      },\r\n    }));\r\n  }, [columns]);\r\n\r\n  const table = useReactTable({\r\n    data,\r\n    columns: enhancedColumns as ColumnDef<TData>[],\r\n    onSortingChange: setSorting,\r\n    onColumnFiltersChange: setColumnFilters,\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    onGlobalFilterChange: setGlobalFilter,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    ...(pagination && { getPaginationRowModel: getPaginationRowModel() }),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    state: {\r\n      sorting,\r\n      columnFilters,\r\n      columnVisibility,\r\n      globalFilter,\r\n    },\r\n    initialState: {\r\n      pagination: {\r\n        pageSize,\r\n      },\r\n    },\r\n  });\r\n\r\n  // Handle export\r\n  const handleExport = () => {\r\n    if (onExport) {\r\n      const filteredData = table\r\n        .getFilteredRowModel()\r\n        .rows.map(row => row.original);\r\n      onExport(filteredData);\r\n    }\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle>{title}</CardTitle>\r\n          {description && <CardDescription>{description}</CardDescription>}\r\n        </CardHeader>\r\n        <CardContent>\r\n          <TableLoadingSkeleton columns={columns.length} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle>{title}</CardTitle>\r\n          {description && <CardDescription>{description}</CardDescription>}\r\n        </CardHeader>\r\n        <CardContent>\r\n          <TableErrorDisplay error={error} />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  if (!data || data.length === 0) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle>{title}</CardTitle>\r\n          {description && <CardDescription>{description}</CardDescription>}\r\n        </CardHeader>\r\n        <CardContent>\r\n          <TableEmptyState />\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader>\r\n        <CardTitle>{title}</CardTitle>\r\n        {description && <CardDescription>{description}</CardDescription>}\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Table Controls */}\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            {/* Global Search */}\r\n            {searchable && (\r\n              <div className=\"relative\">\r\n                <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\r\n                <Input\r\n                  placeholder=\"Search all columns...\"\r\n                  value={globalFilter}\r\n                  onChange={e => setGlobalFilter(e.target.value)}\r\n                  className=\"pl-8 w-[250px]\"\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {/* Filter Indicator */}\r\n            {filterable && columnFilters.length > 0 && (\r\n              <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\r\n                <Filter className=\"h-3 w-3\" />\r\n                {columnFilters.length} filter(s)\r\n              </Badge>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"flex items-center space-x-2\">\r\n            {/* Export Button - Placeholder for integration with existing export system */}\r\n            {exportable && (\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={handleExport}\r\n                className=\"flex items-center gap-2\"\r\n              >\r\n                <Download className=\"h-4 w-4\" />\r\n                Export\r\n              </Button>\r\n            )}\r\n\r\n            {/* Column Visibility */}\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className=\"flex items-center gap-2\"\r\n                >\r\n                  <Settings className=\"h-4 w-4\" />\r\n                  Columns\r\n                  <ChevronDown className=\"h-4 w-4\" />\r\n                </Button>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent align=\"end\" className=\"w-[200px]\">\r\n                {table\r\n                  .getAllColumns()\r\n                  .filter(column => column.getCanHide())\r\n                  .map(column => {\r\n                    return (\r\n                      <DropdownMenuCheckboxItem\r\n                        key={column.id}\r\n                        className=\"capitalize\"\r\n                        checked={column.getIsVisible()}\r\n                        onCheckedChange={value =>\r\n                          column.toggleVisibility(!!value)\r\n                        }\r\n                      >\r\n                        {column.id}\r\n                      </DropdownMenuCheckboxItem>\r\n                    );\r\n                  })}\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Table */}\r\n        <div className=\"rounded-md border\">\r\n          <Table>\r\n            <TableHeader>\r\n              {table.getHeaderGroups().map(headerGroup => (\r\n                <TableRow key={headerGroup.id}>\r\n                  {headerGroup.headers.map(header => (\r\n                    <TableHead key={header.id} className=\"font-semibold\">\r\n                      {header.isPlaceholder\r\n                        ? null\r\n                        : flexRender(\r\n                            header.column.columnDef.header,\r\n                            header.getContext()\r\n                          )}\r\n                    </TableHead>\r\n                  ))}\r\n                </TableRow>\r\n              ))}\r\n            </TableHeader>\r\n            <TableBody>\r\n              {table.getRowModel().rows?.length ? (\r\n                table.getRowModel().rows.map(row => (\r\n                  <TableRow\r\n                    key={row.id}\r\n                    data-state={row.getIsSelected() && 'selected'}\r\n                    className={\r\n                      onRowClick ? 'cursor-pointer hover:bg-muted/50' : ''\r\n                    }\r\n                    onClick={() => onRowClick?.(row.original)}\r\n                  >\r\n                    {row.getVisibleCells().map(cell => (\r\n                      <TableCell key={cell.id}>\r\n                        {flexRender(\r\n                          cell.column.columnDef.cell,\r\n                          cell.getContext()\r\n                        )}\r\n                      </TableCell>\r\n                    ))}\r\n                  </TableRow>\r\n                ))\r\n              ) : (\r\n                <TableRow>\r\n                  <TableCell\r\n                    colSpan={columns.length}\r\n                    className=\"h-24 text-center\"\r\n                  >\r\n                    No results found.\r\n                  </TableCell>\r\n                </TableRow>\r\n              )}\r\n            </TableBody>\r\n          </Table>\r\n        </div>\r\n\r\n        {/* Pagination */}\r\n        {pagination && (\r\n          <div className=\"flex items-center justify-between space-x-2 py-4\">\r\n            <div className=\"text-sm text-muted-foreground\">\r\n              Showing {table.getState().pagination.pageIndex * pageSize + 1} to{' '}\r\n              {Math.min(\r\n                (table.getState().pagination.pageIndex + 1) * pageSize,\r\n                table.getFilteredRowModel().rows.length\r\n              )}{' '}\r\n              of {table.getFilteredRowModel().rows.length} entries\r\n            </div>\r\n            <div className=\"flex items-center space-x-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={() => table.previousPage()}\r\n                disabled={!table.getCanPreviousPage()}\r\n              >\r\n                Previous\r\n              </Button>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={() => table.nextPage()}\r\n                disabled={!table.getCanNextPage()}\r\n              >\r\n                Next\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,2EAA2E;;;;;AAI3E;AACA;AAAA;AAYA;AAQA;AAMA;AACA;AACA;AACA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAvCA;;;;;;;;;;AA8EA;;CAEC,GACD,MAAM,uBAAuB,CAAC,EAAE,OAAO,EAAuB,iBAC5D,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,kBACvC,6LAAC;gCAAY,WAAU;+BAAb;;;;;;;;;;;;;;;gBAMf,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,yBACjC,6LAAC;wBAAmB,WAAU;kCAC5B,cAAA,6LAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,yBACvC,6LAAC;oCAEC,WAAU;mCADL;;;;;;;;;;uBAJH;;;;;;;;;;;;;;;;KAdZ;AA6BN;;CAEC,GACD,MAAM,oBAAoB,CAAC,EAAE,KAAK,EAAqB,iBACrD,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAAgB;;;;;;0BAC/B,6LAAC;gBAAE,WAAU;0BAAU;;;;;;0BACvB,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;MAJzC;AAQN;;CAEC,GACD,MAAM,kBAAkB,kBACtB,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAAgB;;;;;;0BAC/B,6LAAC;gBAAE,WAAU;0BAAU;;;;;;;;;;;;MAHrB;AAyBC,SAAS,mBAAqD,EACnE,IAAI,EACJ,OAAO,EACP,QAAQ,YAAY,EACpB,WAAW,EACX,UAAU,KAAK,EACf,KAAK,EACL,aAAa,IAAI,EACjB,aAAa,IAAI,EACjB,aAAa,IAAI,EACjB,aAAa,IAAI,EACjB,WAAW,EAAE,EACb,YAAY,EAAE,EACd,UAAU,EACV,QAAQ,EACuB;;IAC/B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,CAAC;IAC3E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,sCAAsC;IACtC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uDAAE;YAC9B,OAAO,QAAQ,GAAG;+DAAC,CAAA,SAAU,CAAC;wBAC5B,GAAG,MAAM;wBACT,MAAM;2EAAE,CAAC;gCACP,MAAM,EAAE,QAAQ,GAAG,EAAE,GAAG;gCACxB,MAAM,iBACJ,OAAO,OAAO,MAAM,KAAK,aACrB,OAAO,MAAM,CAAC,iBACd,OAAO,MAAM;gCAEnB,IAAI,CAAC,IAAI,UAAU,IAAI;oCACrB,OAAO;gCACT;gCAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,OAAO;uFAAE,IAAM,IAAI,aAAa,CAAC,IAAI,WAAW,OAAO;;oCACvD,WAAU;;wCAET;wCACA,IAAI,WAAW,OAAO,sBACrB,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;mDACjB,IAAI,WAAW,OAAO,uBACxB,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;iEAErB,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;4BAI/B;;oBACF,CAAC;;QACH;sDAAG;QAAC;KAAQ;IAEZ,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA,SAAS;QACT,iBAAiB;QACjB,uBAAuB;QACvB,0BAA0B;QAC1B,sBAAsB;QACtB,iBAAiB,CAAA,GAAA,wKAAA,CAAA,kBAAe,AAAD;QAC/B,GAAI,cAAc;YAAE,uBAAuB,CAAA,GAAA,wKAAA,CAAA,wBAAqB,AAAD;QAAI,CAAC;QACpE,mBAAmB,CAAA,GAAA,wKAAA,CAAA,oBAAiB,AAAD;QACnC,qBAAqB,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD;QACvC,OAAO;YACL;YACA;YACA;YACA;QACF;QACA,cAAc;YACZ,YAAY;gBACV;YACF;QACF;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ,MAAM,eAAe,MAClB,mBAAmB,GACnB,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ;YAC/B,SAAS;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC,mIAAA,CAAA,YAAS;sCAAE;;;;;;wBACX,6BAAe,6LAAC,mIAAA,CAAA,kBAAe;sCAAE;;;;;;;;;;;;8BAEpC,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAqB,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;IAIrD;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC,mIAAA,CAAA,YAAS;sCAAE;;;;;;wBACX,6BAAe,6LAAC,mIAAA,CAAA,kBAAe;sCAAE;;;;;;;;;;;;8BAEpC,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAkB,OAAO;;;;;;;;;;;;;;;;;IAIlC;IAEA,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC,mIAAA,CAAA,YAAS;sCAAE;;;;;;wBACX,6BAAe,6LAAC,mIAAA,CAAA,kBAAe;sCAAE;;;;;;;;;;;;8BAEpC,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;;;;;;;;;;;;;;;;IAIT;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;kCAAE;;;;;;oBACX,6BAAe,6LAAC,mIAAA,CAAA,kBAAe;kCAAE;;;;;;;;;;;;0BAEpC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAEZ,4BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAA,IAAK,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;oCAMf,cAAc,cAAc,MAAM,GAAG,mBACpC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;;0DACnC,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,cAAc,MAAM;4CAAC;;;;;;;;;;;;;0CAK5B,6LAAC;gCAAI,WAAU;;oCAEZ,4BACC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAMpC,6LAAC,+IAAA,CAAA,eAAY;;0DACX,6LAAC,+IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;sEAEhC,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAG3B,6LAAC,+IAAA,CAAA,sBAAmB;gDAAC,OAAM;gDAAM,WAAU;0DACxC,MACE,aAAa,GACb,MAAM,CAAC,CAAA,SAAU,OAAO,UAAU,IAClC,GAAG,CAAC,CAAA;oDACH,qBACE,6LAAC,+IAAA,CAAA,2BAAwB;wDAEvB,WAAU;wDACV,SAAS,OAAO,YAAY;wDAC5B,iBAAiB,CAAA,QACf,OAAO,gBAAgB,CAAC,CAAC,CAAC;kEAG3B,OAAO,EAAE;uDAPL,OAAO,EAAE;;;;;gDAUpB;;;;;;;;;;;;;;;;;;;;;;;;kCAOV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;8CACJ,6LAAC,oIAAA,CAAA,cAAW;8CACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAA,4BAC3B,6LAAC,oIAAA,CAAA,WAAQ;sDACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAA,uBACvB,6LAAC,oIAAA,CAAA,YAAS;oDAAiB,WAAU;8DAClC,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;mDALT,OAAO,EAAE;;;;;2CAFd,YAAY,EAAE;;;;;;;;;;8CAcjC,6LAAC,oIAAA,CAAA,YAAS;8CACP,MAAM,WAAW,GAAG,IAAI,EAAE,SACzB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA,oBAC3B,6LAAC,oIAAA,CAAA,WAAQ;4CAEP,cAAY,IAAI,aAAa,MAAM;4CACnC,WACE,aAAa,qCAAqC;4CAEpD,SAAS,IAAM,aAAa,IAAI,QAAQ;sDAEvC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAA,qBACzB,6LAAC,oIAAA,CAAA,YAAS;8DACP,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;mDAHH,KAAK,EAAE;;;;;2CARpB,IAAI,EAAE;;;;kEAkBf,6LAAC,oIAAA,CAAA,WAAQ;kDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;4CACR,SAAS,QAAQ,MAAM;4CACvB,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUV,4BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAgC;oCACpC,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,WAAW;oCAAE;oCAAI;oCACjE,KAAK,GAAG,CACP,CAAC,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,CAAC,IAAI,UAC9C,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;oCACtC;oCAAI;oCACH,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;oCAAC;;;;;;;0CAE9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,MAAM,YAAY;wCACjC,UAAU,CAAC,MAAM,kBAAkB;kDACpC;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,MAAM,QAAQ;wCAC7B,UAAU,CAAC,MAAM,cAAc;kDAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA9SgB;;QAwDA,yLAAA,CAAA,gBAAa;;;MAxDb", "debugId": null}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/tables/DelegationReportTable.tsx"], "sourcesContent": ["// frontend/src/components/features/reporting/tables/DelegationReportTable.tsx\r\n\r\nimport React, { useMemo } from 'react';\r\nimport { ColumnDef } from '@tanstack/react-table';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport { ReportingDataTable } from './ReportingDataTable';\r\nimport { format } from 'date-fns';\r\nimport { Eye, Edit, MoreHorizontal } from 'lucide-react';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\n\r\n// FIXED: Import centralized types instead of defining duplicate interface\r\nimport { DelegationReportData } from '../data/types/reporting';\r\n\r\n/**\r\n * Props for DelegationReportTable component\r\n */\r\ninterface DelegationReportTableProps {\r\n  data: DelegationReportData[];\r\n  loading?: boolean;\r\n  error?: string | undefined; // FIXED: Allow undefined for strict mode\r\n  onViewDelegation?: (delegation: DelegationReportData) => void;\r\n  onEditDelegation?: (delegation: DelegationReportData) => void;\r\n  onExport?: (data: DelegationReportData[]) => void;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * Status badge component with color coding\r\n */\r\nconst StatusBadge = ({\r\n  status,\r\n}: {\r\n  status: DelegationReportData['status'];\r\n}) => {\r\n  // FIXED: Use correct Prisma enum values\r\n  const statusConfig = {\r\n    Planned: { variant: 'secondary' as const, label: 'Planned' },\r\n    Confirmed: { variant: 'default' as const, label: 'Confirmed' },\r\n    In_Progress: { variant: 'default' as const, label: 'In Progress' },\r\n    Completed: { variant: 'default' as const, label: 'Completed' },\r\n    Cancelled: { variant: 'outline' as const, label: 'Cancelled' },\r\n    No_details: { variant: 'secondary' as const, label: 'No Details' },\r\n  };\r\n\r\n  const config = statusConfig[status];\r\n  return <Badge variant={config.variant}>{config.label}</Badge>;\r\n};\r\n\r\n/**\r\n * Priority badge component with color coding\r\n */\r\nconst PriorityBadge = ({\r\n  priority,\r\n}: {\r\n  priority: DelegationReportData['priority'];\r\n}) => {\r\n  // FIXED: Use correct Prisma enum values\r\n  const priorityConfig = {\r\n    Low: {\r\n      variant: 'outline' as const,\r\n      label: 'Low',\r\n      className: 'text-green-600 border-green-600',\r\n    },\r\n    Medium: {\r\n      variant: 'outline' as const,\r\n      label: 'Medium',\r\n      className: 'text-yellow-600 border-yellow-600',\r\n    },\r\n    High: {\r\n      variant: 'outline' as const,\r\n      label: 'High',\r\n      className: 'text-orange-600 border-orange-600',\r\n    },\r\n  };\r\n\r\n  if (!priority) {\r\n    return (\r\n      <Badge variant=\"outline\" className=\"text-gray-600 border-gray-600\">\r\n        -\r\n      </Badge>\r\n    );\r\n  }\r\n\r\n  const config = priorityConfig[priority];\r\n  if (!config) {\r\n    return (\r\n      <Badge variant=\"outline\" className=\"text-gray-600 border-gray-600\">\r\n        {priority}\r\n      </Badge>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Badge variant={config.variant} className={config.className}>\r\n      {config.label}\r\n    </Badge>\r\n  );\r\n};\r\n\r\n/**\r\n * Progress bar component\r\n */\r\nconst ProgressBar = ({ progress }: { progress: number }) => (\r\n  <div className=\"flex items-center gap-2\">\r\n    <div className=\"w-16 bg-gray-200 rounded-full h-2\">\r\n      <div\r\n        className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\r\n        style={{ width: `${Math.min(progress, 100)}%` }}\r\n      />\r\n    </div>\r\n    <span className=\"text-xs text-gray-600 min-w-[3rem]\">{progress}%</span>\r\n  </div>\r\n);\r\n\r\n/**\r\n * Actions dropdown component\r\n */\r\nconst ActionsDropdown = ({\r\n  delegation,\r\n  onView,\r\n  onEdit,\r\n}: {\r\n  delegation: DelegationReportData;\r\n  onView?: ((delegation: DelegationReportData) => void) | undefined;\r\n  onEdit?: ((delegation: DelegationReportData) => void) | undefined;\r\n}) => (\r\n  <DropdownMenu>\r\n    <DropdownMenuTrigger asChild>\r\n      <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\r\n        <MoreHorizontal className=\"h-4 w-4\" />\r\n      </Button>\r\n    </DropdownMenuTrigger>\r\n    <DropdownMenuContent align=\"end\">\r\n      {onView && (\r\n        <DropdownMenuItem onClick={() => onView(delegation)}>\r\n          <Eye className=\"mr-2 h-4 w-4\" />\r\n          View Details\r\n        </DropdownMenuItem>\r\n      )}\r\n      {onEdit && (\r\n        <DropdownMenuItem onClick={() => onEdit(delegation)}>\r\n          <Edit className=\"mr-2 h-4 w-4\" />\r\n          Edit\r\n        </DropdownMenuItem>\r\n      )}\r\n    </DropdownMenuContent>\r\n  </DropdownMenu>\r\n);\r\n\r\n/**\r\n * DelegationReportTable Component\r\n *\r\n * A specialized data table for delegation reporting data.\r\n * Extends the base ReportingDataTable with delegation-specific features.\r\n *\r\n * Features:\r\n * - Status and priority badges\r\n * - Progress visualization\r\n * - Date formatting\r\n * - Action buttons\r\n * - Export functionality\r\n *\r\n * @param props - Component props\r\n * @returns JSX element\r\n */\r\nexport const DelegationReportTable: React.FC<DelegationReportTableProps> = ({\r\n  data,\r\n  loading = false,\r\n  error,\r\n  onViewDelegation,\r\n  onEditDelegation,\r\n  onExport,\r\n  className = '',\r\n}) => {\r\n  // Define table columns\r\n  const columns = useMemo<ColumnDef<DelegationReportData>[]>(\r\n    () => [\r\n      {\r\n        accessorKey: 'title',\r\n        header: 'Title',\r\n        cell: ({ row }) => (\r\n          <div\r\n            className=\"font-medium max-w-[200px] truncate\"\r\n            title={row.getValue('title')}\r\n          >\r\n            {row.getValue('title')}\r\n          </div>\r\n        ),\r\n      },\r\n      {\r\n        accessorKey: 'status',\r\n        header: 'Status',\r\n        cell: ({ row }) => <StatusBadge status={row.getValue('status')} />,\r\n        filterFn: 'equals',\r\n      },\r\n      {\r\n        accessorKey: 'priority',\r\n        header: 'Priority',\r\n        cell: ({ row }) => (\r\n          <PriorityBadge priority={row.getValue('priority')} />\r\n        ),\r\n        filterFn: 'equals',\r\n      },\r\n      {\r\n        accessorKey: 'assignedTo',\r\n        header: 'Assigned To',\r\n        cell: ({ row }) => (\r\n          <div\r\n            className=\"max-w-[150px] truncate\"\r\n            title={row.getValue('assignedTo')}\r\n          >\r\n            {row.getValue('assignedTo')}\r\n          </div>\r\n        ),\r\n      },\r\n      {\r\n        accessorKey: 'location',\r\n        header: 'Location',\r\n        cell: ({ row }) => (\r\n          <div\r\n            className=\"max-w-[120px] truncate\"\r\n            title={row.getValue('location')}\r\n          >\r\n            {row.getValue('location')}\r\n          </div>\r\n        ),\r\n      },\r\n      {\r\n        accessorKey: 'progress',\r\n        header: 'Progress',\r\n        cell: ({ row }) => <ProgressBar progress={row.getValue('progress')} />,\r\n        sortingFn: 'basic',\r\n      },\r\n      {\r\n        accessorKey: 'createdAt',\r\n        header: 'Created',\r\n        cell: ({ row }) => {\r\n          const date = new Date(row.getValue('createdAt'));\r\n          return <div className=\"text-sm\">{format(date, 'MMM dd, yyyy')}</div>;\r\n        },\r\n        sortingFn: 'datetime',\r\n      },\r\n      {\r\n        accessorKey: 'dueDate',\r\n        header: 'Due Date',\r\n        cell: ({ row }) => {\r\n          const date = new Date(row.getValue('dueDate'));\r\n          // FIXED: Use correct Prisma enum value\r\n          const isOverdue =\r\n            date < new Date() && row.original.status !== 'Completed';\r\n          return (\r\n            <div\r\n              className={`text-sm ${isOverdue ? 'text-red-600 font-medium' : ''}`}\r\n            >\r\n              {format(date, 'MMM dd, yyyy')}\r\n            </div>\r\n          );\r\n        },\r\n        sortingFn: 'datetime',\r\n      },\r\n      {\r\n        accessorKey: 'estimatedHours',\r\n        header: 'Est. Hours',\r\n        cell: ({ row }) => (\r\n          <div className=\"text-sm text-center\">\r\n            {row.getValue('estimatedHours')}h\r\n          </div>\r\n        ),\r\n      },\r\n      {\r\n        accessorKey: 'actualHours',\r\n        header: 'Actual Hours',\r\n        cell: ({ row }) => {\r\n          const actualHours = row.getValue('actualHours') as number | undefined;\r\n          return (\r\n            <div className=\"text-sm text-center\">\r\n              {actualHours ? `${actualHours}h` : '-'}\r\n            </div>\r\n          );\r\n        },\r\n      },\r\n      {\r\n        id: 'actions',\r\n        header: 'Actions',\r\n        cell: ({ row }) => (\r\n          <ActionsDropdown\r\n            delegation={row.original}\r\n            onView={onViewDelegation}\r\n            onEdit={onEditDelegation}\r\n          />\r\n        ),\r\n        enableSorting: false,\r\n        enableHiding: false,\r\n      },\r\n    ],\r\n    [onViewDelegation, onEditDelegation]\r\n  );\r\n\r\n  return (\r\n    <ReportingDataTable\r\n      data={data}\r\n      columns={columns}\r\n      title=\"Delegation Report\"\r\n      description=\"Comprehensive view of all delegations with status, progress, and performance metrics\"\r\n      loading={loading}\r\n      error={error}\r\n      searchable={true}\r\n      filterable={true}\r\n      exportable={true}\r\n      pagination={true}\r\n      pageSize={10}\r\n      className={className}\r\n      onRowClick={onViewDelegation}\r\n      onExport={onExport}\r\n    />\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,8EAA8E;;;;;AAE9E;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;;;;;;;;AAuBA;;CAEC,GACD,MAAM,cAAc,CAAC,EACnB,MAAM,EAGP;IACC,wCAAwC;IACxC,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAsB,OAAO;QAAU;QAC3D,WAAW;YAAE,SAAS;YAAoB,OAAO;QAAY;QAC7D,aAAa;YAAE,SAAS;YAAoB,OAAO;QAAc;QACjE,WAAW;YAAE,SAAS;YAAoB,OAAO;QAAY;QAC7D,WAAW;YAAE,SAAS;YAAoB,OAAO;QAAY;QAC7D,YAAY;YAAE,SAAS;YAAsB,OAAO;QAAa;IACnE;IAEA,MAAM,SAAS,YAAY,CAAC,OAAO;IACnC,qBAAO,6LAAC,oIAAA,CAAA,QAAK;QAAC,SAAS,OAAO,OAAO;kBAAG,OAAO,KAAK;;;;;;AACtD;KAjBM;AAmBN;;CAEC,GACD,MAAM,gBAAgB,CAAC,EACrB,QAAQ,EAGT;IACC,wCAAwC;IACxC,MAAM,iBAAiB;QACrB,KAAK;YACH,SAAS;YACT,OAAO;YACP,WAAW;QACb;QACA,QAAQ;YACN,SAAS;YACT,OAAO;YACP,WAAW;QACb;QACA,MAAM;YACJ,SAAS;YACT,OAAO;YACP,WAAW;QACb;IACF;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,SAAQ;YAAU,WAAU;sBAAgC;;;;;;IAIvE;IAEA,MAAM,SAAS,cAAc,CAAC,SAAS;IACvC,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,SAAQ;YAAU,WAAU;sBAChC;;;;;;IAGP;IAEA,qBACE,6LAAC,oIAAA,CAAA,QAAK;QAAC,SAAS,OAAO,OAAO;QAAE,WAAW,OAAO,SAAS;kBACxD,OAAO,KAAK;;;;;;AAGnB;MA9CM;AAgDN;;CAEC,GACD,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAwB,iBACrD,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,KAAK,GAAG,CAAC,UAAU,KAAK,CAAC,CAAC;oBAAC;;;;;;;;;;;0BAGlD,6LAAC;gBAAK,WAAU;;oBAAsC;oBAAS;;;;;;;;;;;;;MAR7D;AAYN;;CAEC,GACD,MAAM,kBAAkB,CAAC,EACvB,UAAU,EACV,MAAM,EACN,MAAM,EAKP,iBACC,6LAAC,+IAAA,CAAA,eAAY;;0BACX,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,WAAU;8BAChC,cAAA,6LAAC,mNAAA,CAAA,iBAAc;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAG9B,6LAAC,+IAAA,CAAA,sBAAmB;gBAAC,OAAM;;oBACxB,wBACC,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,OAAO;;0CACtC,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;oBAInC,wBACC,6LAAC,+IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,OAAO;;0CACtC,6LAAC,8MAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;MAxBrC;AAgDC,MAAM,wBAA8D,CAAC,EAC1E,IAAI,EACJ,UAAU,KAAK,EACf,KAAK,EACL,gBAAgB,EAChB,gBAAgB,EAChB,QAAQ,EACR,YAAY,EAAE,EACf;;IACC,uBAAuB;IACvB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDACpB,IAAM;gBACJ;oBACE,aAAa;oBACb,QAAQ;oBACR,IAAI;kEAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;gCACC,WAAU;gCACV,OAAO,IAAI,QAAQ,CAAC;0CAEnB,IAAI,QAAQ,CAAC;;;;;;;gBAGpB;gBACA;oBACE,aAAa;oBACb,QAAQ;oBACR,IAAI;kEAAE,CAAC,EAAE,GAAG,EAAE,iBAAK,6LAAC;gCAAY,QAAQ,IAAI,QAAQ,CAAC;;;;;;;oBACrD,UAAU;gBACZ;gBACA;oBACE,aAAa;oBACb,QAAQ;oBACR,IAAI;kEAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;gCAAc,UAAU,IAAI,QAAQ,CAAC;;;;;;;oBAExC,UAAU;gBACZ;gBACA;oBACE,aAAa;oBACb,QAAQ;oBACR,IAAI;kEAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;gCACC,WAAU;gCACV,OAAO,IAAI,QAAQ,CAAC;0CAEnB,IAAI,QAAQ,CAAC;;;;;;;gBAGpB;gBACA;oBACE,aAAa;oBACb,QAAQ;oBACR,IAAI;kEAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;gCACC,WAAU;gCACV,OAAO,IAAI,QAAQ,CAAC;0CAEnB,IAAI,QAAQ,CAAC;;;;;;;gBAGpB;gBACA;oBACE,aAAa;oBACb,QAAQ;oBACR,IAAI;kEAAE,CAAC,EAAE,GAAG,EAAE,iBAAK,6LAAC;gCAAY,UAAU,IAAI,QAAQ,CAAC;;;;;;;oBACvD,WAAW;gBACb;gBACA;oBACE,aAAa;oBACb,QAAQ;oBACR,IAAI;kEAAE,CAAC,EAAE,GAAG,EAAE;4BACZ,MAAM,OAAO,IAAI,KAAK,IAAI,QAAQ,CAAC;4BACnC,qBAAO,6LAAC;gCAAI,WAAU;0CAAW,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;;;;;;wBAChD;;oBACA,WAAW;gBACb;gBACA;oBACE,aAAa;oBACb,QAAQ;oBACR,IAAI;kEAAE,CAAC,EAAE,GAAG,EAAE;4BACZ,MAAM,OAAO,IAAI,KAAK,IAAI,QAAQ,CAAC;4BACnC,uCAAuC;4BACvC,MAAM,YACJ,OAAO,IAAI,UAAU,IAAI,QAAQ,CAAC,MAAM,KAAK;4BAC/C,qBACE,6LAAC;gCACC,WAAW,CAAC,QAAQ,EAAE,YAAY,6BAA6B,IAAI;0CAElE,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;;;;;;wBAGpB;;oBACA,WAAW;gBACb;gBACA;oBACE,aAAa;oBACb,QAAQ;oBACR,IAAI;kEAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;gCAAI,WAAU;;oCACZ,IAAI,QAAQ,CAAC;oCAAkB;;;;;;;;gBAGtC;gBACA;oBACE,aAAa;oBACb,QAAQ;oBACR,IAAI;kEAAE,CAAC,EAAE,GAAG,EAAE;4BACZ,MAAM,cAAc,IAAI,QAAQ,CAAC;4BACjC,qBACE,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,YAAY,CAAC,CAAC,GAAG;;;;;;wBAGzC;;gBACF;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,IAAI;kEAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;gCACC,YAAY,IAAI,QAAQ;gCACxB,QAAQ;gCACR,QAAQ;;;;;;;oBAGZ,eAAe;oBACf,cAAc;gBAChB;aACD;iDACD;QAAC;QAAkB;KAAiB;IAGtC,qBACE,6LAAC,8KAAA,CAAA,qBAAkB;QACjB,MAAM;QACN,SAAS;QACT,OAAM;QACN,aAAY;QACZ,SAAS;QACT,OAAO;QACP,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,UAAU;QACV,WAAW;QACX,YAAY;QACZ,UAAU;;;;;;AAGhB;GAvJa;MAAA", "debugId": null}}, {"offset": {"line": 1202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/tables/TaskReportingTable.tsx"], "sourcesContent": ["/**\r\n * @file Task Reporting Table Component - Phase 2 Implementation\r\n * @description Task data table component following latest shadcn/ui patterns\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying task data in table format\r\n * - OCP: Open for extension via column configuration and filtering options\r\n * - DIP: Depends on existing table abstractions and data service interfaces\r\n *\r\n * Architecture Compliance:\r\n * - Follows latest shadcn/ui DataTable patterns\r\n * - Uses TanStack React Table for advanced functionality\r\n * - Integrates with existing data structures and services\r\n * - Maintains consistent styling and behavior\r\n */\r\n\r\n'use client';\r\n\r\nimport React, { useMemo } from 'react';\r\nimport {\r\n  ColumnDef,\r\n  ColumnFiltersState,\r\n  SortingState,\r\n  VisibilityState,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getFilteredRowModel,\r\n  getPaginationRowModel,\r\n  getSortedRowModel,\r\n  useReactTable,\r\n} from '@tanstack/react-table';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@/components/ui/table';\r\nimport { useTasks } from '@/lib/stores/queries/useTasks';\r\nimport { TaskStatusPrisma, TaskPriorityPrisma, Task } from '@/lib/types/domain';\r\nimport { format } from 'date-fns';\r\nimport {\r\n  CheckSquare,\r\n  Download,\r\n  Filter,\r\n  ArrowUpDown,\r\n  MoreHorizontal,\r\n  Eye,\r\n  Edit,\r\n  Trash2,\r\n} from 'lucide-react';\r\n\r\n// Task data interface for table display\r\ninterface TaskTableData {\r\n  id: string;\r\n  title: string;\r\n  status: TaskStatusPrisma;\r\n  priority: TaskPriorityPrisma;\r\n  assignedTo: string;\r\n  dueDate: string;\r\n  createdAt: string;\r\n  completedAt?: string | undefined;\r\n  estimatedHours: number;\r\n  actualHours: number;\r\n}\r\n\r\ninterface TaskReportingTableProps {\r\n  className?: string;\r\n  showExportOptions?: boolean;\r\n  maxRows?: number;\r\n}\r\n\r\n/**\r\n * @component TaskReportingTable\r\n * @description Task data table following latest shadcn/ui patterns\r\n *\r\n * Responsibilities:\r\n * - Display task data in sortable, filterable table format\r\n * - Provide export functionality for task data\r\n * - Follow latest DataTable patterns from shadcn/ui\r\n * - Integrate with existing data services and filters\r\n * - Support pagination, sorting, and column visibility\r\n *\r\n * SOLID Principles Applied:\r\n * - SRP: Single responsibility of displaying task table data\r\n * - OCP: Open for extension via column configuration\r\n * - DIP: Depends on table and data service abstractions\r\n */\r\nexport const TaskReportingTable: React.FC<TaskReportingTableProps> = ({\r\n  className = '',\r\n  showExportOptions = true,\r\n  maxRows = 100,\r\n}) => {\r\n  // State management for table features\r\n  const [sorting, setSorting] = React.useState<SortingState>([]);\r\n  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(\r\n    []\r\n  );\r\n  const [columnVisibility, setColumnVisibility] =\r\n    React.useState<VisibilityState>({});\r\n  const [rowSelection, setRowSelection] = React.useState({});\r\n\r\n  // Data fetching using real task data\r\n  const {\r\n    data: tasks,\r\n    isLoading,\r\n    error,\r\n  } = useTasks({\r\n    staleTime: 2 * 60 * 1000,\r\n  });\r\n\r\n  // Transform real task data to table format\r\n  const tableData = useMemo<TaskTableData[]>(() => {\r\n    if (!tasks) return [];\r\n\r\n    // Transform real task data to table format\r\n    return tasks.slice(0, maxRows).map((task: Task) => ({\r\n      id: task.id,\r\n      title: task.description, // Use description as title\r\n      status: task.status,\r\n      priority: task.priority,\r\n      assignedTo:\r\n        task.staffEmployee?.name || task.driverEmployee?.name || 'Unassigned',\r\n      dueDate: task.deadline\r\n        ? format(new Date(task.deadline), 'yyyy-MM-dd')\r\n        : 'No deadline',\r\n      createdAt: format(new Date(task.createdAt), 'yyyy-MM-dd'),\r\n      completedAt:\r\n        task.status === 'Completed'\r\n          ? format(new Date(task.updatedAt), 'yyyy-MM-dd')\r\n          : undefined,\r\n      estimatedHours: task.estimatedDuration || 0,\r\n      actualHours: task.estimatedDuration || 0, // Use estimated as actual for now\r\n    }));\r\n  }, [tasks, maxRows]);\r\n\r\n  // Column definitions following latest shadcn/ui patterns\r\n  const columns = useMemo<ColumnDef<TaskTableData>[]>(\r\n    () => [\r\n      {\r\n        accessorKey: 'title',\r\n        header: ({ column }) => (\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n            className=\"h-8 px-2\"\r\n          >\r\n            Task Title\r\n            <ArrowUpDown className=\"ml-2 h-4 w-4\" />\r\n          </Button>\r\n        ),\r\n        cell: ({ row }) => (\r\n          <div className=\"font-medium\">{row.getValue('title')}</div>\r\n        ),\r\n      },\r\n      {\r\n        accessorKey: 'status',\r\n        header: ({ column }) => (\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n            className=\"h-8 px-2\"\r\n          >\r\n            Status\r\n            <ArrowUpDown className=\"ml-2 h-4 w-4\" />\r\n          </Button>\r\n        ),\r\n        cell: ({ row }) => {\r\n          const status = row.getValue('status') as TaskStatusPrisma;\r\n          return (\r\n            <Badge variant={getStatusVariant(status)}>\r\n              {formatStatus(status)}\r\n            </Badge>\r\n          );\r\n        },\r\n      },\r\n      {\r\n        accessorKey: 'priority',\r\n        header: ({ column }) => (\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n            className=\"h-8 px-2\"\r\n          >\r\n            Priority\r\n            <ArrowUpDown className=\"ml-2 h-4 w-4\" />\r\n          </Button>\r\n        ),\r\n        cell: ({ row }) => {\r\n          const priority = row.getValue('priority') as TaskPriorityPrisma;\r\n          return (\r\n            <Badge variant={getPriorityVariant(priority)}>{priority}</Badge>\r\n          );\r\n        },\r\n      },\r\n      {\r\n        accessorKey: 'assignedTo',\r\n        header: ({ column }) => (\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n            className=\"h-8 px-2\"\r\n          >\r\n            Assigned To\r\n            <ArrowUpDown className=\"ml-2 h-4 w-4\" />\r\n          </Button>\r\n        ),\r\n        cell: ({ row }) => (\r\n          <div className=\"text-sm\">{row.getValue('assignedTo')}</div>\r\n        ),\r\n      },\r\n      {\r\n        accessorKey: 'dueDate',\r\n        header: ({ column }) => (\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}\r\n            className=\"h-8 px-2\"\r\n          >\r\n            Due Date\r\n            <ArrowUpDown className=\"ml-2 h-4 w-4\" />\r\n          </Button>\r\n        ),\r\n        cell: ({ row }) => (\r\n          <div className=\"text-sm\">{row.getValue('dueDate')}</div>\r\n        ),\r\n      },\r\n      {\r\n        accessorKey: 'estimatedHours',\r\n        header: 'Est. Hours',\r\n        cell: ({ row }) => (\r\n          <div className=\"text-sm text-center\">\r\n            {row.getValue('estimatedHours')}h\r\n          </div>\r\n        ),\r\n      },\r\n      {\r\n        accessorKey: 'actualHours',\r\n        header: 'Actual Hours',\r\n        cell: ({ row }) => (\r\n          <div className=\"text-sm text-center\">\r\n            {row.getValue('actualHours')}h\r\n          </div>\r\n        ),\r\n      },\r\n      {\r\n        id: 'actions',\r\n        header: 'Actions',\r\n        cell: ({ row }) => {\r\n          const task = row.original;\r\n\r\n          return (\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\r\n                  <span className=\"sr-only\">Open menu</span>\r\n                  <MoreHorizontal className=\"h-4 w-4\" />\r\n                </Button>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent align=\"end\">\r\n                <DropdownMenuCheckboxItem\r\n                  onClick={() => console.log('View task:', task.id)}\r\n                >\r\n                  <Eye className=\"mr-2 h-4 w-4\" />\r\n                  View Details\r\n                </DropdownMenuCheckboxItem>\r\n                <DropdownMenuCheckboxItem\r\n                  onClick={() => console.log('Edit task:', task.id)}\r\n                >\r\n                  <Edit className=\"mr-2 h-4 w-4\" />\r\n                  Edit Task\r\n                </DropdownMenuCheckboxItem>\r\n                <DropdownMenuCheckboxItem\r\n                  onClick={() => navigator.clipboard.writeText(task.id)}\r\n                >\r\n                  Copy Task ID\r\n                </DropdownMenuCheckboxItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          );\r\n        },\r\n      },\r\n    ],\r\n    []\r\n  );\r\n\r\n  // Table configuration using latest patterns\r\n  const table = useReactTable({\r\n    data: tableData,\r\n    columns,\r\n    onSortingChange: setSorting,\r\n    onColumnFiltersChange: setColumnFilters,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getPaginationRowModel: getPaginationRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    onRowSelectionChange: setRowSelection,\r\n    state: {\r\n      sorting,\r\n      columnFilters,\r\n      columnVisibility,\r\n      rowSelection,\r\n    },\r\n  });\r\n\r\n  // Export functionality\r\n  const handleExportCSV = async () => {\r\n    try {\r\n      if (!tableData || tableData.length === 0) {\r\n        console.warn('No data to export');\r\n        return;\r\n      }\r\n\r\n      const csvData = tableData.map(task => ({\r\n        'Task ID': task.id,\r\n        Title: task.title,\r\n        Status: task.status,\r\n        Priority: task.priority,\r\n        'Assigned To': task.assignedTo,\r\n        'Due Date': task.dueDate,\r\n        'Created Date': task.createdAt,\r\n        'Completed Date': task.completedAt || '',\r\n        'Estimated Hours': task.estimatedHours || '',\r\n        'Actual Hours': task.actualHours || '',\r\n      }));\r\n\r\n      if (csvData.length === 0) {\r\n        console.warn('No data to export');\r\n        return;\r\n      }\r\n\r\n      const csvContent = [\r\n        Object.keys(csvData[0]!).join(','),\r\n        ...csvData.map(row => Object.values(row).join(',')),\r\n      ].join('\\n');\r\n\r\n      const blob = new Blob([csvContent], { type: 'text/csv' });\r\n      const url = window.URL.createObjectURL(blob);\r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `task-report-${new Date().toISOString().split('T')[0]}.csv`;\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      window.URL.revokeObjectURL(url);\r\n      document.body.removeChild(a);\r\n    } catch (error) {\r\n      console.error('Export failed:', error);\r\n    }\r\n  };\r\n\r\n  // Loading state\r\n  if (isLoading) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <CheckSquare className=\"h-5 w-5\" />\r\n            Task Report\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-4\">\r\n            <div className=\"h-8 bg-muted rounded animate-pulse\" />\r\n            <div className=\"space-y-2\">\r\n              {[...Array(5)].map((_, i) => (\r\n                <div key={i} className=\"h-12 bg-muted rounded animate-pulse\" />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  // Error state\r\n  if (error) {\r\n    return (\r\n      <Card className={className}>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <CheckSquare className=\"h-5 w-5\" />\r\n            Task Report\r\n          </CardTitle>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"text-center py-8 text-muted-foreground\">\r\n            <CheckSquare className=\"h-12 w-12 mx-auto mb-2 opacity-50\" />\r\n            <p>Failed to load task data</p>\r\n            <p className=\"text-sm\">{error.message}</p>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Card className={className}>\r\n      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-4\">\r\n        <div>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <CheckSquare className=\"h-5 w-5\" />\r\n            Task Report\r\n          </CardTitle>\r\n          <p className=\"text-sm text-muted-foreground mt-1\">\r\n            {tableData.length} tasks found\r\n          </p>\r\n        </div>\r\n        {showExportOptions && (\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={handleExportCSV}\r\n            className=\"h-8\"\r\n          >\r\n            <Download className=\"h-3 w-3 mr-1\" />\r\n            Export CSV\r\n          </Button>\r\n        )}\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Filters and Controls */}\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <Input\r\n              placeholder=\"Filter tasks...\"\r\n              value={\r\n                (table.getColumn('title')?.getFilterValue() as string) ?? ''\r\n              }\r\n              onChange={event =>\r\n                table.getColumn('title')?.setFilterValue(event.target.value)\r\n              }\r\n              className=\"max-w-sm\"\r\n            />\r\n          </div>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"outline\" className=\"ml-auto\">\r\n                <Filter className=\"mr-2 h-4 w-4\" />\r\n                Columns\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\">\r\n              {table\r\n                .getAllColumns()\r\n                .filter(column => column.getCanHide())\r\n                .map(column => (\r\n                  <DropdownMenuCheckboxItem\r\n                    key={column.id}\r\n                    className=\"capitalize\"\r\n                    checked={column.getIsVisible()}\r\n                    onCheckedChange={value => column.toggleVisibility(!!value)}\r\n                  >\r\n                    {column.id}\r\n                  </DropdownMenuCheckboxItem>\r\n                ))}\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n\r\n        {/* Data Table */}\r\n        <div className=\"rounded-md border\">\r\n          <Table>\r\n            <TableHeader>\r\n              {table.getHeaderGroups().map(headerGroup => (\r\n                <TableRow key={headerGroup.id}>\r\n                  {headerGroup.headers.map(header => (\r\n                    <TableHead key={header.id}>\r\n                      {header.isPlaceholder\r\n                        ? null\r\n                        : flexRender(\r\n                            header.column.columnDef.header,\r\n                            header.getContext()\r\n                          )}\r\n                    </TableHead>\r\n                  ))}\r\n                </TableRow>\r\n              ))}\r\n            </TableHeader>\r\n            <TableBody>\r\n              {table.getRowModel().rows?.length ? (\r\n                table.getRowModel().rows.map(row => (\r\n                  <TableRow\r\n                    key={row.id}\r\n                    data-state={row.getIsSelected() && 'selected'}\r\n                  >\r\n                    {row.getVisibleCells().map(cell => (\r\n                      <TableCell key={cell.id}>\r\n                        {flexRender(\r\n                          cell.column.columnDef.cell,\r\n                          cell.getContext()\r\n                        )}\r\n                      </TableCell>\r\n                    ))}\r\n                  </TableRow>\r\n                ))\r\n              ) : (\r\n                <TableRow>\r\n                  <TableCell\r\n                    colSpan={columns.length}\r\n                    className=\"h-24 text-center\"\r\n                  >\r\n                    No tasks found.\r\n                  </TableCell>\r\n                </TableRow>\r\n              )}\r\n            </TableBody>\r\n          </Table>\r\n        </div>\r\n\r\n        {/* Pagination */}\r\n        <div className=\"flex items-center justify-end space-x-2 py-4\">\r\n          <div className=\"flex-1 text-sm text-muted-foreground\">\r\n            {table.getFilteredSelectedRowModel().rows.length} of{' '}\r\n            {table.getFilteredRowModel().rows.length} row(s) selected.\r\n          </div>\r\n          <div className=\"space-x-2\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={() => table.previousPage()}\r\n              disabled={!table.getCanPreviousPage()}\r\n            >\r\n              Previous\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={() => table.nextPage()}\r\n              disabled={!table.getCanNextPage()}\r\n            >\r\n              Next\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n};\r\n\r\n// Utility functions for badge variants\r\nconst getStatusVariant = (\r\n  status: TaskStatusPrisma\r\n): 'default' | 'secondary' | 'destructive' | 'outline' => {\r\n  switch (status) {\r\n    case 'Completed':\r\n      return 'default';\r\n    case 'In_Progress':\r\n      return 'secondary';\r\n    case 'Cancelled':\r\n      return 'destructive';\r\n    default:\r\n      return 'outline';\r\n  }\r\n};\r\n\r\nconst getPriorityVariant = (\r\n  priority: TaskPriorityPrisma\r\n): 'default' | 'secondary' | 'destructive' | 'outline' => {\r\n  switch (priority) {\r\n    case 'High':\r\n      return 'destructive';\r\n    case 'Medium':\r\n      return 'secondary';\r\n    case 'Low':\r\n      return 'outline';\r\n    default:\r\n      return 'default';\r\n  }\r\n};\r\n\r\nconst formatStatus = (status: TaskStatusPrisma): string => {\r\n  switch (status) {\r\n    case 'In_Progress':\r\n      return 'In Progress';\r\n    default:\r\n      return status;\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AAID;AACA;AAAA;AAYA;AACA;AACA;AACA;AACA;AAMA;AAQA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AApCA;;;;;;;;;;;;AAmFO,MAAM,qBAAwD,CAAC,EACpE,YAAY,EAAE,EACd,oBAAoB,IAAI,EACxB,UAAU,GAAG,EACd;;IACC,sCAAsC;IACtC,MAAM,CAAC,SAAS,WAAW,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAe,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CACtD,EAAE;IAEJ,MAAM,CAAC,kBAAkB,oBAAoB,GAC3C,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAkB,CAAC;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC;IAExD,qCAAqC;IACrC,MAAM,EACJ,MAAM,KAAK,EACX,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,WAAQ,AAAD,EAAE;QACX,WAAW,IAAI,KAAK;IACtB;IAEA,2CAA2C;IAC3C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAmB;YACzC,IAAI,CAAC,OAAO,OAAO,EAAE;YAErB,2CAA2C;YAC3C,OAAO,MAAM,KAAK,CAAC,GAAG,SAAS,GAAG;yDAAC,CAAC,OAAe,CAAC;wBAClD,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,WAAW;wBACvB,QAAQ,KAAK,MAAM;wBACnB,UAAU,KAAK,QAAQ;wBACvB,YACE,KAAK,aAAa,EAAE,QAAQ,KAAK,cAAc,EAAE,QAAQ;wBAC3D,SAAS,KAAK,QAAQ,GAClB,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,QAAQ,GAAG,gBAChC;wBACJ,WAAW,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,SAAS,GAAG;wBAC5C,aACE,KAAK,MAAM,KAAK,cACZ,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,SAAS,GAAG,gBACjC;wBACN,gBAAgB,KAAK,iBAAiB,IAAI;wBAC1C,aAAa,KAAK,iBAAiB,IAAI;oBACzC,CAAC;;QACH;gDAAG;QAAC;QAAO;KAAQ;IAEnB,yDAAyD;IACzD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CACpB,IAAM;gBACJ;oBACE,aAAa;oBACb,MAAM;+DAAE,CAAC,EAAE,MAAM,EAAE,iBACjB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,OAAO;2EAAE,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;gCAC7D,WAAU;;oCACX;kDAEC,6LAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;oBAG3B,IAAI;+DAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;gCAAI,WAAU;0CAAe,IAAI,QAAQ,CAAC;;;;;;;gBAE/C;gBACA;oBACE,aAAa;oBACb,MAAM;+DAAE,CAAC,EAAE,MAAM,EAAE,iBACjB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,OAAO;2EAAE,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;gCAC7D,WAAU;;oCACX;kDAEC,6LAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;oBAG3B,IAAI;+DAAE,CAAC,EAAE,GAAG,EAAE;4BACZ,MAAM,SAAS,IAAI,QAAQ,CAAC;4BAC5B,qBACE,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAS,iBAAiB;0CAC9B,aAAa;;;;;;wBAGpB;;gBACF;gBACA;oBACE,aAAa;oBACb,MAAM;+DAAE,CAAC,EAAE,MAAM,EAAE,iBACjB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,OAAO;2EAAE,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;gCAC7D,WAAU;;oCACX;kDAEC,6LAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;oBAG3B,IAAI;+DAAE,CAAC,EAAE,GAAG,EAAE;4BACZ,MAAM,WAAW,IAAI,QAAQ,CAAC;4BAC9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAS,mBAAmB;0CAAY;;;;;;wBAEnD;;gBACF;gBACA;oBACE,aAAa;oBACb,MAAM;+DAAE,CAAC,EAAE,MAAM,EAAE,iBACjB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,OAAO;2EAAE,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;gCAC7D,WAAU;;oCACX;kDAEC,6LAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;oBAG3B,IAAI;+DAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;gCAAI,WAAU;0CAAW,IAAI,QAAQ,CAAC;;;;;;;gBAE3C;gBACA;oBACE,aAAa;oBACb,MAAM;+DAAE,CAAC,EAAE,MAAM,EAAE,iBACjB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,OAAO;2EAAE,IAAM,OAAO,aAAa,CAAC,OAAO,WAAW,OAAO;;gCAC7D,WAAU;;oCACX;kDAEC,6LAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;oBAG3B,IAAI;+DAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;gCAAI,WAAU;0CAAW,IAAI,QAAQ,CAAC;;;;;;;gBAE3C;gBACA;oBACE,aAAa;oBACb,QAAQ;oBACR,IAAI;+DAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;gCAAI,WAAU;;oCACZ,IAAI,QAAQ,CAAC;oCAAkB;;;;;;;;gBAGtC;gBACA;oBACE,aAAa;oBACb,QAAQ;oBACR,IAAI;+DAAE,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;gCAAI,WAAU;;oCACZ,IAAI,QAAQ,CAAC;oCAAe;;;;;;;;gBAGnC;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,IAAI;+DAAE,CAAC,EAAE,GAAG,EAAE;4BACZ,MAAM,OAAO,IAAI,QAAQ;4BAEzB,qBACE,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,WAAU;;8DAChC,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC,mNAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG9B,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,6LAAC,+IAAA,CAAA,2BAAwB;gDACvB,OAAO;2FAAE,IAAM,QAAQ,GAAG,CAAC,cAAc,KAAK,EAAE;;;kEAEhD,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlC,6LAAC,+IAAA,CAAA,2BAAwB;gDACvB,OAAO;2FAAE,IAAM,QAAQ,GAAG,CAAC,cAAc,KAAK,EAAE;;;kEAEhD,6LAAC,8MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,6LAAC,+IAAA,CAAA,2BAAwB;gDACvB,OAAO;2FAAE,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC,KAAK,EAAE;;0DACrD;;;;;;;;;;;;;;;;;;wBAMT;;gBACF;aACD;8CACD,EAAE;IAGJ,4CAA4C;IAC5C,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,MAAM;QACN;QACA,iBAAiB;QACjB,uBAAuB;QACvB,iBAAiB,CAAA,GAAA,wKAAA,CAAA,kBAAe,AAAD;QAC/B,uBAAuB,CAAA,GAAA,wKAAA,CAAA,wBAAqB,AAAD;QAC3C,mBAAmB,CAAA,GAAA,wKAAA,CAAA,oBAAiB,AAAD;QACnC,qBAAqB,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD;QACvC,0BAA0B;QAC1B,sBAAsB;QACtB,OAAO;YACL;YACA;YACA;YACA;QACF;IACF;IAEA,uBAAuB;IACvB,MAAM,kBAAkB;QACtB,IAAI;YACF,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;gBACxC,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,MAAM,UAAU,UAAU,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACrC,WAAW,KAAK,EAAE;oBAClB,OAAO,KAAK,KAAK;oBACjB,QAAQ,KAAK,MAAM;oBACnB,UAAU,KAAK,QAAQ;oBACvB,eAAe,KAAK,UAAU;oBAC9B,YAAY,KAAK,OAAO;oBACxB,gBAAgB,KAAK,SAAS;oBAC9B,kBAAkB,KAAK,WAAW,IAAI;oBACtC,mBAAmB,KAAK,cAAc,IAAI;oBAC1C,gBAAgB,KAAK,WAAW,IAAI;gBACtC,CAAC;YAED,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACxB,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,MAAM,aAAa;gBACjB,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,EAAG,IAAI,CAAC;mBAC3B,QAAQ,GAAG,CAAC,CAAA,MAAO,OAAO,MAAM,CAAC,KAAK,IAAI,CAAC;aAC/C,CAAC,IAAI,CAAC;YAEP,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAW,EAAE;gBAAE,MAAM;YAAW;YACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;YACxE,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,OAAO,GAAG,CAAC,eAAe,CAAC;YAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,gBAAgB;IAChB,IAAI,WAAW;QACb,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIvC,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wCAAY,WAAU;uCAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOxB;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAW;;8BACf,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;8BAIvC,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;0CAAE;;;;;;0CACH,6LAAC;gCAAE,WAAU;0CAAW,MAAM,OAAO;;;;;;;;;;;;;;;;;;;;;;;IAK/C;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC;;0CACC,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGrC,6LAAC;gCAAE,WAAU;;oCACV,UAAU,MAAM;oCAAC;;;;;;;;;;;;;oBAGrB,mCACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAM3C,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OACE,AAAC,MAAM,SAAS,CAAC,UAAU,oBAA+B;oCAE5D,UAAU,CAAA,QACR,MAAM,SAAS,CAAC,UAAU,eAAe,MAAM,MAAM,CAAC,KAAK;oCAE7D,WAAU;;;;;;;;;;;0CAId,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;;8DAClC,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAIvC,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAM;kDACxB,MACE,aAAa,GACb,MAAM,CAAC,CAAA,SAAU,OAAO,UAAU,IAClC,GAAG,CAAC,CAAA,uBACH,6LAAC,+IAAA,CAAA,2BAAwB;gDAEvB,WAAU;gDACV,SAAS,OAAO,YAAY;gDAC5B,iBAAiB,CAAA,QAAS,OAAO,gBAAgB,CAAC,CAAC,CAAC;0DAEnD,OAAO,EAAE;+CALL,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAa1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;8CACJ,6LAAC,oIAAA,CAAA,cAAW;8CACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAA,4BAC3B,6LAAC,oIAAA,CAAA,WAAQ;sDACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAA,uBACvB,6LAAC,oIAAA,CAAA,YAAS;8DACP,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;mDALT,OAAO,EAAE;;;;;2CAFd,YAAY,EAAE;;;;;;;;;;8CAcjC,6LAAC,oIAAA,CAAA,YAAS;8CACP,MAAM,WAAW,GAAG,IAAI,EAAE,SACzB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA,oBAC3B,6LAAC,oIAAA,CAAA,WAAQ;4CAEP,cAAY,IAAI,aAAa,MAAM;sDAElC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAA,qBACzB,6LAAC,oIAAA,CAAA,YAAS;8DACP,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;mDAHH,KAAK,EAAE;;;;;2CAJpB,IAAI,EAAE;;;;kEAcf,6LAAC,oIAAA,CAAA,WAAQ;kDACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;4CACR,SAAS,QAAQ,MAAM;4CACvB,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;oCAAC;oCAAI;oCACpD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;oCAAC;;;;;;;0CAE3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,MAAM,YAAY;wCACjC,UAAU,CAAC,MAAM,kBAAkB;kDACpC;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,MAAM,QAAQ;wCAC7B,UAAU,CAAC,MAAM,cAAc;kDAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GApca;;QAmBP,8JAAA,CAAA,WAAQ;QAoLE,yLAAA,CAAA,gBAAa;;;KAvMhB;AAscb,uCAAuC;AACvC,MAAM,mBAAmB,CACvB;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,qBAAqB,CACzB;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,eAAe,CAAC;IACpB,OAAQ;QACN,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 2160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/CarServiceTrackingSystem/TrackerSystem/frontend/src/components/features/reporting/tables/index.ts"], "sourcesContent": ["// frontend/src/components/features/reporting/tables/index.ts\r\n\r\n/**\r\n * Reporting Tables Export Index\r\n *\r\n * Centralized exports for all reporting table components following DRY principles.\r\n */\r\n\r\n// Base Table Component\r\nexport { ReportingDataTable } from './ReportingDataTable';\r\nexport type { ReportingTableData } from './ReportingDataTable';\r\n\r\n// Specialized Table Components\r\nexport { DelegationReportTable } from './DelegationReportTable';\r\n// FIXED: DelegationReportData is now imported from centralized types, not exported from table\r\n\r\n// Phase 2: Task Reporting Table\r\nexport { TaskReportingTable } from './TaskReportingTable';\r\n"], "names": [], "mappings": "AAAA,6DAA6D;AAE7D;;;;CAIC,GAED,uBAAuB;;AACvB;AAGA,+BAA+B;AAC/B;AACA,8FAA8F;AAE9F,gCAAgC;AAChC", "debugId": null}}]}