"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8444],{17313:(e,s,t)=>{t.d(s,{Xi:()=>c,av:()=>o,j7:()=>d,tU:()=>n});var a=t(95155),r=t(60704),l=t(12115),i=t(54036);let n=r.bL,d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.B8,{className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),ref:s,...l})});d.displayName=r.B8.displayName;let c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.l9,{className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),ref:s,...l})});c.displayName=r.l9.displayName;let o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.UC,{className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),ref:s,...l})});o.displayName=r.UC.displayName},26126:(e,s,t)=>{t.d(s,{E:()=>n});var a=t(95155),r=t(74466);t(12115);var l=t(54036);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{defaultVariants:{variant:"default"},variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80"}}});function n(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(i({variant:t}),s),...r})}},29360:(e,s,t)=>{t.d(s,{F:()=>S});var a=t(95155),r=t(91721),l=t(19637),i=t(8376),n=t(24371),d=t(76570),c=t(33349),o=t(50172),m=t(40207),x=t(45731),u=t(57679),f=t(37648),h=t(51920),j=t(6874),p=t.n(j),N=t(35695),g=t(12115),v=t(91394),y=t(26126),b=t(30285),w=t(66695),_=t(44838),A=t(55365),R=t(17313),z=t(40283);let E=e=>{switch(e){case"ADMIN":return"bg-purple-500 hover:bg-purple-600 text-white";case"EMPLOYEE":return"bg-green-500 hover:bg-green-600 text-white";case"MANAGER":return"bg-blue-500 hover:bg-blue-600 text-white";case"SUPER_ADMIN":return"bg-red-500 hover:bg-red-600 text-white";default:return"bg-gray-500 hover:bg-gray-600 text-white"}};function S(e){var s,t,j,S,k;let{showSignOut:C=!0,variant:Z="dropdown"}=e,{signOut:U,user:B,userRole:F}=(0,z.useAuthContext)(),L=(0,N.useRouter)(),[D,I]=(0,g.useState)(!1);if(!B)return null;let T=async()=>{I(!0);try{await U(),L.push("/login")}catch(e){console.error("Sign out error:",e)}finally{I(!1)}},V=e=>e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):"Never",M=e=>e?new Date(e).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}):"Never",P=e=>{if(!e)return"Never";let s=new Date,t=new Date(e),a=Math.floor((s.getTime()-t.getTime())/1e3);return a<60?"Just now":a<3600?"".concat(Math.floor(a/60),"m ago"):a<86400?"".concat(Math.floor(a/3600),"h ago"):a<2592e3?"".concat(Math.floor(a/86400),"d ago"):V(e)},W=()=>{var e,s,t;return console.log("\uD83D\uDD0D Role Debug Info:",{"user.user_metadata?.role":null==(e=B.user_metadata)?void 0:e.role,"userRole (from context)":F,"user.user_metadata":B.user_metadata,"user.app_metadata":B.app_metadata,"user.email":B.email}),(null==(s=B.user_metadata)?void 0:s.role)&&B.user_metadata.role!==F&&console.warn("\uD83D\uDEA8 Role mismatch detected! User needs to refresh their session.",{metadataRole:B.user_metadata.role,contextRole:F}),(null==(t=B.user_metadata)?void 0:t.role)||F||"USER"},q=()=>{var e;return(null==(e=B.user_metadata)?void 0:e.is_active)!==!1?"Active":"Inactive"},O=e=>{switch(e.toUpperCase()){case"SUPER_ADMIN":case"ADMIN":return"destructive";case"MANAGER":return"default";case"USER":default:return"secondary";case"READONLY":return"outline"}},Y=e=>"Active"===e?"default":"destructive",H=B.email?B.email.charAt(0).toUpperCase():"U",X=B.email||"N/A",G=null!==B.email_confirmed_at,J=W(),K=J?J.replace("_"," "):"N/A",$=E(J);return"dropdown"===Z?(0,a.jsxs)(_.rI,{children:[(0,a.jsx)(_.ty,{asChild:!0,children:(0,a.jsx)(b.$,{className:"relative size-8 rounded-full",variant:"ghost",children:(0,a.jsxs)(v.eu,{className:"size-8",children:[(0,a.jsx)(v.BK,{alt:H,src:(null==(s=B.user_metadata)?void 0:s.avatar_url)||""}),(0,a.jsx)(v.q5,{children:H})]})})}),(0,a.jsxs)(_.SQ,{align:"end",className:"w-56",forceMount:!0,children:[(0,a.jsx)(_.lp,{className:"font-normal",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium leading-none",children:X}),(0,a.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:B.id})]})}),(0,a.jsx)(_.mB,{}),(0,a.jsxs)(_._2,{children:[(0,a.jsx)(r.A,{className:"mr-2 size-4"}),(0,a.jsx)(p(),{href:"/profile",children:"Profile"})]}),(0,a.jsxs)(_._2,{children:[(0,a.jsx)(l.A,{className:"mr-2 size-4"}),(0,a.jsx)("span",{children:G?"Email Verified":"Email Not Verified"}),G?(0,a.jsx)(i.A,{className:"ml-auto size-4 text-green-500"}):(0,a.jsx)(n.A,{className:"ml-auto size-4 text-red-500"})]}),(0,a.jsxs)(_._2,{children:[(0,a.jsx)(d.A,{className:"mr-2 size-4"}),(0,a.jsx)(y.E,{className:$,children:K})]}),(0,a.jsx)(_.mB,{}),(0,a.jsxs)(_._2,{onClick:T,children:[(0,a.jsx)(c.A,{className:"mr-2 size-4"}),(0,a.jsx)("span",{children:"Log out"})]})]})]}):"card"===Z?(0,a.jsxs)(w.Zp,{className:"mx-auto w-full max-w-md",children:[(0,a.jsxs)(w.aR,{className:"flex flex-row items-center space-x-4 p-6",children:[(0,a.jsxs)(v.eu,{className:"size-16",children:[(0,a.jsx)(v.BK,{alt:H,src:(null==(t=B.user_metadata)?void 0:t.avatar_url)||""}),(0,a.jsx)(v.q5,{className:"text-2xl",children:H})]}),(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)(w.ZB,{className:"text-2xl font-bold",children:X}),(0,a.jsxs)(w.BT,{className:"text-sm text-muted-foreground",children:["User ID: ",B.id]}),(0,a.jsx)(y.E,{className:"".concat($," px-2 py-1 text-sm"),children:K})]})]}),(0,a.jsxs)(w.Wu,{className:"space-y-4 p-6 pt-0",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(l.A,{className:"mr-2 size-5 text-muted-foreground"}),(0,a.jsxs)("span",{className:"text-base",children:["Email: ",X]}),G?(0,a.jsx)(i.A,{className:"ml-2 size-5 text-green-500"}):(0,a.jsx)(n.A,{className:"ml-2 size-5 text-red-500"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d.A,{className:"mr-2 size-5 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-base",children:"Role: "}),(0,a.jsx)(y.E,{className:"".concat($," ml-1"),children:K})]}),C&&(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(b.$,{onClick:T,variant:"outline",disabled:D,children:D?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"mr-2 size-4 animate-spin"}),"Signing out..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"mr-2 size-4"}),"Log out"]})})})]})]}):"detailed"===Z?(0,a.jsxs)("div",{className:"w-full max-w-5xl mx-auto space-y-6",children:[(0,a.jsx)(w.Zp,{children:(0,a.jsx)(w.aR,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(v.eu,{className:"size-20",children:[(0,a.jsx)(v.BK,{alt:B.email||"",src:null==(j=B.user_metadata)?void 0:j.avatar_url}),(0,a.jsx)(v.q5,{className:"text-xl",children:H})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:(null==(S=B.user_metadata)?void 0:S.full_name)||"User Profile"}),(0,a.jsx)("p",{className:"text-muted-foreground text-lg",children:B.email})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 flex-wrap",children:[(0,a.jsx)(y.E,{className:"text-sm px-3 py-1",variant:O(W()),children:W()}),(0,a.jsxs)(y.E,{className:"text-sm px-3 py-1",variant:Y(q()),children:[(0,a.jsx)(m.A,{className:"mr-1 size-3"}),q()]}),B.email_confirmed_at&&(0,a.jsxs)(y.E,{className:"text-sm px-3 py-1",variant:"outline",children:[(0,a.jsx)(x.A,{className:"mr-1 size-3"}),"Verified"]})]})]})]}),C&&(0,a.jsx)(b.$,{disabled:D,onClick:T,variant:"outline",size:"sm",children:D?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"mr-2 size-4 animate-spin"}),"Signing out..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"mr-2 size-4"}),"Sign out"]})})]})})}),(0,a.jsxs)(R.tU,{defaultValue:"overview",className:"w-full",children:[(0,a.jsxs)(R.j7,{className:"grid w-full grid-cols-3",children:[(0,a.jsx)(R.Xi,{value:"overview",children:"Overview"}),(0,a.jsx)(R.Xi,{value:"security",children:"Security"}),(0,a.jsx)(R.Xi,{value:"activity",children:"Activity"})]}),(0,a.jsx)(R.av,{value:"overview",className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)(w.Zp,{children:[(0,a.jsx)(w.aR,{children:(0,a.jsxs)(w.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,a.jsx)(r.A,{className:"size-5"}),"Account Information"]})}),(0,a.jsx)(w.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"User ID:"}),(0,a.jsxs)("span",{className:"font-mono text-xs bg-muted px-2 py-1 rounded",children:[B.id.slice(0,8),"..."]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Email:"}),(0,a.jsx)("span",{className:"text-sm",children:B.email})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Role:"}),(0,a.jsx)(y.E,{variant:O(W()),children:W()})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Status:"}),(0,a.jsx)(y.E,{variant:Y(q()),children:q()})]}),(null==(k=B.user_metadata)?void 0:k.employee_id)&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Employee ID:"}),(0,a.jsx)("span",{className:"text-sm",children:B.user_metadata.employee_id})]})]})})]}),(0,a.jsxs)(w.Zp,{children:[(0,a.jsx)(w.aR,{children:(0,a.jsxs)(w.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,a.jsx)(l.A,{className:"size-5"}),"Contact & Verification"]})}),(0,a.jsx)(w.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Email Verified:"}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:B.email_confirmed_at?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"size-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm text-green-600",children:"Verified"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"size-4 text-red-600"}),(0,a.jsx)("span",{className:"text-sm text-red-600",children:"Not Verified"})]})})]}),B.email_confirmed_at&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Verified On:"}),(0,a.jsx)("span",{className:"text-sm",children:V(B.email_confirmed_at)})]})]})})]})]})}),(0,a.jsx)(R.av,{value:"security",className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,a.jsxs)(w.Zp,{children:[(0,a.jsx)(w.aR,{children:(0,a.jsxs)(w.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,a.jsx)(x.A,{className:"size-5"}),"Security Status"]})}),(0,a.jsxs)(w.Wu,{className:"space-y-4",children:[(0,a.jsxs)(A.Fc,{children:[(0,a.jsx)(x.A,{className:"size-4"}),(0,a.jsx)(A.TN,{children:"Your account is protected by enterprise-grade security protocols. All activities are monitored and logged for security purposes."})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Account Type:"}),(0,a.jsx)("span",{className:"text-sm",children:"Standard User"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"SSO User:"}),(0,a.jsx)("span",{className:"text-sm",children:B.is_sso_user?"Yes":"No"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Anonymous:"}),(0,a.jsx)("span",{className:"text-sm",children:B.is_anonymous?"Yes":"No"})]})]})]})]}),(0,a.jsxs)(w.Zp,{children:[(0,a.jsx)(w.aR,{children:(0,a.jsxs)(w.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,a.jsx)(u.A,{className:"size-5"}),"Session Information"]})}),(0,a.jsx)(w.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Current Session:"}),(0,a.jsx)(y.E,{variant:"default",children:"Active"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Session ID:"}),(0,a.jsx)("span",{className:"font-mono text-xs bg-muted px-2 py-1 rounded",children:B.id.slice(-8)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"App Metadata:"}),(0,a.jsx)("span",{className:"text-sm",children:B.app_metadata?"Present":"None"})]})]})})]})]})}),(0,a.jsx)(R.av,{value:"activity",className:"space-y-4",children:(0,a.jsxs)(w.Zp,{children:[(0,a.jsx)(w.aR,{children:(0,a.jsxs)(w.ZB,{className:"flex items-center gap-2 text-lg",children:[(0,a.jsx)(m.A,{className:"size-5"}),"Account Activity"]})}),(0,a.jsx)(w.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(f.A,{className:"size-5 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Last Sign In"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:M(B.last_sign_in_at)})]})]}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:P(B.last_sign_in_at)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(h.A,{className:"size-5 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Account Created"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:M(B.created_at)})]})]}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:P(B.created_at)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(r.A,{className:"size-5 text-muted-foreground"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Last Updated"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:M(B.updated_at)})]})]}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:P(B.updated_at)})]})]})})]})})]})]}):null}},44838:(e,s,t)=>{t.d(s,{SQ:()=>x,_2:()=>u,hO:()=>f,lp:()=>h,mB:()=>j,rI:()=>o,ty:()=>m});var a=t(95155),r=t(12115),l=t(48698),i=t(73158),n=t(10518),d=t(70154),c=t(54036);let o=l.bL,m=l.l9;l.YJ,l.ZL,l.Pb,l.z6,r.forwardRef((e,s)=>{let{className:t,inset:r,children:n,...d}=e;return(0,a.jsxs)(l.ZP,{ref:s,className:(0,c.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",t),...d,children:[n,(0,a.jsx)(i.A,{className:"ml-auto"})]})}).displayName=l.ZP.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.G5,{ref:s,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...r})}).displayName=l.G5.displayName;let x=r.forwardRef((e,s)=>{let{className:t,sideOffset:r=4,...i}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsx)(l.UC,{ref:s,sideOffset:r,className:(0,c.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",t),...i})})});x.displayName=l.UC.displayName;let u=r.forwardRef((e,s)=>{let{className:t,inset:r,...i}=e;return(0,a.jsx)(l.q7,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",r&&"pl-8",t),...i})});u.displayName=l.q7.displayName;let f=r.forwardRef((e,s)=>{let{className:t,children:r,checked:i,...d}=e;return(0,a.jsxs)(l.H_,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...void 0!==i&&{checked:i},...d,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})}),r]})});f.displayName=l.H_.displayName,r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(l.hN,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(d.A,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=l.hN.displayName;let h=r.forwardRef((e,s)=>{let{className:t,inset:r,...i}=e;return(0,a.jsx)(l.JU,{ref:s,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",t),...i})});h.displayName=l.JU.displayName;let j=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...r})});j.displayName=l.wv.displayName},45876:(e,s,t)=>{t.d(s,{ProtectedRoute:()=>m});var a=t(95155),r=t(50172),l=t(31949),i=t(45731);t(12115);var n=t(40283),d=t(55365),c=t(66695),o=t(92999);function m(e){let{allowedRoles:s=[],children:t,fallback:m,requireEmailVerification:x=!0}=e,{error:u,loading:f,session:h,user:j,userRole:p}=(0,n.useAuthContext)();if(f)return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50",children:(0,a.jsx)(c.Zp,{className:"mx-auto w-full max-w-md",children:(0,a.jsxs)(c.Wu,{className:"flex flex-col items-center justify-center py-8",children:[(0,a.jsx)(r.A,{className:"mb-4 size-8 animate-spin text-blue-600"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Verifying security credentials..."})]})})});if(u&&!j)return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,a.jsxs)(c.Zp,{className:"mx-auto w-full max-w-md",children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsxs)(c.ZB,{className:"flex items-center text-red-600",children:[(0,a.jsx)(l.A,{className:"mr-2 size-5"}),"Authentication Error"]}),(0,a.jsx)(c.BT,{children:"There was a problem with the security system"})]}),(0,a.jsxs)(c.Wu,{children:[(0,a.jsx)(d.Fc,{variant:"destructive",children:(0,a.jsx)(d.TN,{children:u})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(o.LoginForm,{})})]})]})});if(!j||!h)return m?(0,a.jsx)(a.Fragment,{children:m}):(0,a.jsx)(o.LoginForm,{onSuccess:()=>{globalThis.location.href="/"}});if(x&&!j.email_confirmed_at)return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,a.jsxs)(c.Zp,{className:"mx-auto w-full max-w-md",children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsxs)(c.ZB,{className:"flex items-center text-yellow-600",children:[(0,a.jsx)(i.A,{className:"mr-2 size-5"}),"Email Verification Required"]}),(0,a.jsx)(c.BT,{children:"Please verify your email address to continue"})]}),(0,a.jsxs)(c.Wu,{children:[(0,a.jsxs)(d.Fc,{children:[(0,a.jsx)(l.A,{className:"size-4"}),(0,a.jsxs)(d.TN,{children:["We've sent a verification email to ",(0,a.jsx)("strong",{children:j.email}),". Please check your inbox and click the verification link to access the system."]})]}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Didn't receive the email? Check your spam folder or contact your administrator."})})]})]})});if(s.length>0){let e=p||"USER";if(!s.includes(e))return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 p-4",children:(0,a.jsxs)(c.Zp,{className:"mx-auto w-full max-w-md",children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsxs)(c.ZB,{className:"flex items-center text-red-600",children:[(0,a.jsx)(i.A,{className:"mr-2 size-5"}),"Access Denied"]}),(0,a.jsx)(c.BT,{children:"Insufficient permissions to access this resource"})]}),(0,a.jsxs)(c.Wu,{children:[(0,a.jsx)(d.Fc,{variant:"destructive",children:(0,a.jsxs)(d.TN,{children:["Your account (",e,") does not have permission to access this area. Required roles: ",s.join(", ")]})}),(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Contact your administrator if you believe this is an error."})})]})]})})}return(0,a.jsx)(a.Fragment,{children:t})}},47262:(e,s,t)=>{t.d(s,{S:()=>d});var a=t(95155),r=t(76981),l=t(10518),i=t(12115),n=t(54036);let d=i.forwardRef((e,s)=>{let{className:t,...i}=e;return(0,a.jsx)(r.bL,{className:(0,n.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),ref:s,...i,children:(0,a.jsx)(r.C1,{className:(0,n.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(l.A,{className:"size-4"})})})});d.displayName=r.bL.displayName},55365:(e,s,t)=>{t.d(s,{Fc:()=>d,TN:()=>o,XL:()=>c});var a=t(95155),r=t(74466),l=t(12115),i=t(54036);let n=(0,r.F)("relative w-full rounded-lg border p-4 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{defaultVariants:{variant:"default"},variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}}}),d=l.forwardRef((e,s)=>{let{className:t,variant:r,...l}=e;return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:r}),t),ref:s,role:"alert",...l})});d.displayName="Alert";let c=l.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h5",{className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),ref:s,...r})});c.displayName="AlertTitle";let o=l.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),ref:s,...r})});o.displayName="AlertDescription"},62523:(e,s,t)=>{t.d(s,{p:()=>i});var a=t(95155),r=t(12115),l=t(54036);let i=r.forwardRef((e,s)=>{let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,type:r,...i})});i.displayName="Input"},66695:(e,s,t)=>{t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n,wL:()=>m});var a=t(95155),r=t(12115),l=t(54036);let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),ref:s,...r})});i.displayName="Card";let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),ref:s,...r})});n.displayName="CardHeader";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),ref:s,...r})});d.displayName="CardTitle";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("text-sm text-muted-foreground",t),ref:s,...r})});c.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("p-6 pt-0",t),ref:s,...r})});o.displayName="CardContent";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)("flex items-center p-6 pt-0",t),ref:s,...r})});m.displayName="CardFooter"},91394:(e,s,t)=>{t.d(s,{BK:()=>d,eu:()=>n,q5:()=>c});var a=t(95155),r=t(54011),l=t(12115),i=t(54036);let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.bL,{className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",t),ref:s,...l})});n.displayName=r.bL.displayName;let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r._V,{className:(0,i.cn)("aspect-square h-full w-full",t),ref:s,...l})});d.displayName=r._V.displayName;let c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.H4,{className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",t),ref:s,...l})});c.displayName=r.H4.displayName}}]);