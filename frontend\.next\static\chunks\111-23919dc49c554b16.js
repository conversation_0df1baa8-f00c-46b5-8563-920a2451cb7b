"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[111],{55012:(e,t,n)=>{n.d(t,{gv:()=>h,j9:()=>l});var o=n(14298),c=n(38549),s=n(14163),i=n(28113),r=n(87358);class a{static getInstance(e){var t;return null!=a.instance||(a.instance=new a(e)),a.instance}async connect(){var e;if(null==(e=this.socket)?void 0:e.connected)return void console.debug("WebSocket already connected");this.setConnectionState("connecting");try{let{data:{session:e},error:t}=await s.N.auth.getSession();t&&console.warn("Failed to get session for WebSocket connection:",t);let n={forceNew:!0,timeout:this.config.timeout,transports:["websocket","polling"],withCredentials:!0};if(null==e?void 0:e.access_token){n.auth={token:e.access_token},console.debug("\uD83D\uDD10 WebSocket connecting with authentication token");let t=e.expires_at?1e3*e.expires_at:0,o=Date.now();t-o<=6e4&&console.warn("⚠️ WebSocket token expires soon, may need refresh")}else console.warn("⚠️ WebSocket connecting without authentication token - connection may fail");this.socket=(0,o.io)(this.config.url,n),this.setupEventHandlers()}catch(e){console.error("Failed to connect WebSocket:",e),this.setConnectionState("error"),this.scheduleReconnect()}}destroy(){this.disconnect(),this.subscriptions.clear(),this.stateListeners.clear(),a.instance=null}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),this.setConnectionState("disconnected"),this.reconnectAttempts=0}emit(e,t,n){var o;if(!(null==(o=this.socket)?void 0:o.connected))return void console.warn("Cannot emit ".concat(e,":").concat(t," - WebSocket not connected"));this.socket.emit(t,n)}getConnectionState(){return this.connectionState}isConnected(){var e;return"connected"===this.connectionState&&(null==(e=this.socket)?void 0:e.connected)===!0}joinRoom(e){var t;if(!(null==(t=this.socket)?void 0:t.connected))return void console.warn("Cannot join room ".concat(e," - WebSocket not connected"));this.socket.emit("join-room",e)}leaveRoom(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.socket.emit("leave-room",e)}onStateChange(e){return this.stateListeners.add(e),()=>{this.stateListeners.delete(e)}}subscribe(e,t,n){var o;let c="".concat(e,":").concat(t);return this.subscriptions.has(c)||this.subscriptions.set(c,new Set),this.subscriptions.get(c).add(n),(null==(o=this.socket)?void 0:o.connected)&&t&&this.socket.on(t,n),()=>{let e=this.subscriptions.get(c);e&&(e.delete(n),0===e.size&&this.subscriptions.delete(c)),this.socket&&t&&this.socket.off(t,n)}}handleAuthenticationError(){let e=(0,i.Q)();console.log("\uD83D\uDD10 Handling WebSocket authentication error..."),this.socket&&(this.socket.disconnect(),this.socket=null),e.refreshNow().then(e=>{e?console.log("\uD83D\uDD04 Token refresh successful, retrying WebSocket connection"):(console.error("\uD83D\uDD04 Token refresh failed, scheduling normal reconnect"),this.scheduleReconnect())}).catch(e=>{console.error("\uD83D\uDD04 Token refresh error:",e),this.scheduleReconnect()})}resubscribeToEvents(){if(this.socket)for(let[e,t]of this.subscriptions){let[,n]=e.split(":");for(let e of t)n&&this.socket.on(n,e)}}scheduleReconnect(){if(this.reconnectAttempts>=this.config.reconnectAttempts){console.error("Max reconnection attempts reached"),this.setConnectionState("error");return}this.setConnectionState("reconnecting"),this.reconnectAttempts++,setTimeout(()=>{console.info("Attempting to reconnect (".concat(this.reconnectAttempts,"/").concat(this.config.reconnectAttempts,")")),this.connect()},this.config.reconnectDelay*Math.pow(2,this.reconnectAttempts-1))}setConnectionState(e){if(this.connectionState!==e)for(let t of(this.connectionState=e,this.stateListeners))t(e)}setupEventHandlers(){this.socket&&(this.socket.on("connect",()=>{console.info("WebSocket connected"),this.setConnectionState("connected"),this.reconnectAttempts=0,this.resubscribeToEvents()}),this.socket.on("disconnect",e=>{console.warn("WebSocket disconnected:",e),this.setConnectionState("disconnected"),"io server disconnect"!==e&&this.scheduleReconnect()}),this.socket.on("connect_error",e=>{var t,n,o,c;console.error("WebSocket connection error:",e),this.setConnectionState("error"),(null==(t=e.message)?void 0:t.includes("Authentication"))||(null==(n=e.message)?void 0:n.includes("token"))||(null==(o=e.message)?void 0:o.includes("No token provided"))||(null==(c=e.message)?void 0:c.includes("Unauthorized"))?(console.warn("\uD83D\uDD10 Authentication error detected, attempting token refresh"),this.handleAuthenticationError()):this.scheduleReconnect()}),this.socket.on("auth_error",e=>{console.error("\uD83D\uDD10 Server authentication error:",e),this.handleAuthenticationError()}),this.socket.on("token_refresh_required",()=>{console.warn("\uD83D\uDD04 Server requested token refresh"),this.handleAuthenticationError()}))}setupTokenRefreshHandling(){(0,i.Q)().subscribe((e,t)=>{switch(e){case"critical_refresh_failed":console.error("\uD83D\uDD04 Critical token refresh failure, disconnecting WebSocket"),this.disconnect(),this.setConnectionState("error");break;case"refresh_failed":console.error("\uD83D\uDD04 Token refresh failed, WebSocket may lose connection");break;case"refresh_success":console.log("\uD83D\uDD04 Token refreshed, reconnecting WebSocket with new token"),this.socket&&(this.socket.disconnect(),this.socket=null),setTimeout(()=>this.connect(),500)}})}constructor(e={}){var t,n,o,s,i,a;this.connectionState="disconnected",this.reconnectAttempts=0,this.socket=null,this.stateListeners=new Set,this.subscriptions=new Map,this.config={autoConnect:null==(t=e.autoConnect)||t,reconnectAttempts:null!=(n=e.reconnectAttempts)?n:5,reconnectDelay:null!=(o=e.reconnectDelay)?o:1e3,timeout:null!=(s=e.timeout)?s:1e4,url:null!=(a=null!=(i=e.url)?i:r.env.NEXT_PUBLIC_WEBSOCKET_URL)?a:(0,c.Qq)().wsUrl.replace("ws://","http://").replace("wss://","https://")},this.config.autoConnect&&this.connect(),this.setupTokenRefreshHandling()}}a.instance=null;let l=e=>a.getInstance(e),h=()=>{let e=l();return{connectionState:e.getConnectionState(),isConnected:e.isConnected()}}},90111:(e,t,n)=>{n.d(t,{GK:()=>a,ol:()=>l});var o=n(26715),c=n(28755),s=n(12115),i=n(55012);let r={crud:"entity-updates",notifications:"notifications-monitoring",reliability:"reliability-monitoring",system:"system-monitoring"};function a(e,t,n,o){return h(e,t,{channel:"crud",events:["".concat(n,":created"),"".concat(n,":updated"),"".concat(n,":deleted"),"refresh:".concat(n)],fallbackInterval:3e4},o)}function l(e,t,n,o){let c=(0,i.j9)();return(0,s.useEffect)(()=>{c.isConnected()&&(console.debug("[ReliabilityQuery] Joining reliability-monitoring room for ".concat(n)),c.joinRoom("reliability-monitoring"));let e=c.onStateChange(e=>{"connected"===e&&(console.debug("[ReliabilityQuery] WebSocket connected, joining reliability-monitoring room for ".concat(n)),c.joinRoom("reliability-monitoring"))});return()=>{e(),c.isConnected()&&c.leaveRoom("reliability-monitoring")}},[c,n]),h(e,t,{channel:"reliability",events:["".concat(n,"-update"),"".concat(n,"-created"),"".concat(n,"-resolved")],fallbackInterval:{alerts:3e4,"circuit-breakers":6e4,health:45e3,metrics:6e4}[n]},o)}function h(e,t,n,a){let{channel:l,enableFallback:h=!0,enableWebSocket:u=!0,events:d,fallbackInterval:f=3e4}=n,[k,m]=(0,s.useState)(!1),g=(0,i.j9)();(0,s.useEffect)(()=>{let e=()=>{m(g.isConnected())};return e(),g.onStateChange(e)},[g]);let b=h&&(!u||!k),S={gcTime:6e5,queryFn:t,queryKey:e,refetchInterval:!!b&&f,refetchOnReconnect:!0,refetchOnWindowFocus:b,staleTime:3e4*!k,...a},v=(0,o.jE)(),p=(0,c.I)(S);return(0,s.useEffect)(()=>{if(!u||!k)return;let e=r[l];if(!e)return void console.warn("[SmartQuery] No room mapping found for channel: ".concat(l));try{g.joinRoom(e),console.log("[SmartQuery] Joined room: ".concat(e," for channel: ").concat(l))}catch(t){console.error("[SmartQuery] Failed to join room ".concat(e,":"),t)}return()=>{try{g.leaveRoom(e),console.log("[SmartQuery] Left room: ".concat(e," for channel: ").concat(l))}catch(t){console.error("[SmartQuery] Failed to leave room ".concat(e,":"),t)}}},[u,k,l,g]),(0,s.useEffect)(()=>{if(!u||!k||0===d.length)return;let t=[];for(let n of d){let o=g.subscribe(l,n,t=>{console.log("[SmartQuery] WebSocket event received: ".concat(l,":").concat(n),t),v.invalidateQueries({queryKey:e})});t.push(o)}return()=>{for(let e of t)e()}},[u,k,d,l,g,v,e]),{...p,isUsingFallback:b,isWebSocketConnected:k}}}}]);