module.exports = {

"[project]/src/components/features/reporting/dashboard/config/reportingDashboardConfig.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Reporting Dashboard Configuration
 * @description Defines the layout, tabs, and widgets for the reporting dashboard.
 */ __turbopack_context__.s({
    "filterPresets": (()=>filterPresets),
    "reportingDashboardConfig": (()=>reportingDashboardConfig),
    "widgetConfigurations": (()=>widgetConfigurations)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$DelegationStatusWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/DelegationStatusWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$DelegationTrendWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/DelegationTrendWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$LocationDistributionWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/LocationDistributionWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$TaskMetricsWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/TaskMetricsWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$ReportingTableWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/ReportingTableWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-ssr] (ecmascript) <export default as BarChart3>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Table$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/table.js [app-ssr] (ecmascript) <export default as Table>");
;
;
;
const reportingDashboardConfig = {
    entityType: 'reporting',
    title: 'Reporting Dashboard',
    description: 'Comprehensive reporting and analytics dashboard',
    viewModes: [
        'grid',
        'list'
    ],
    defaultViewMode: 'grid',
    tabs: [
        {
            id: 'overview',
            label: 'Overview',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"], {
                className: "h-4 w-4"
            }, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/config/reportingDashboardConfig.tsx",
                lineNumber: 48,
                columnNumber: 13
            }, this),
            description: 'High-level analytics and status distribution.',
            widgets: [
                {
                    id: 'delegation-status',
                    component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$DelegationStatusWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationStatusWidget"],
                    span: 'lg:col-span-1'
                },
                {
                    id: 'delegation-trend',
                    component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$DelegationTrendWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTrendWidget"],
                    span: 'lg:col-span-2'
                },
                {
                    id: 'location-distribution',
                    component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$LocationDistributionWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationDistributionWidget"],
                    span: 'lg:col-span-2'
                },
                {
                    id: 'task-metrics',
                    component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$TaskMetricsWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskMetricsWidget"],
                    span: 'lg:col-span-1'
                }
            ]
        },
        {
            id: 'details',
            label: 'Detailed Report',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Table$3e$__["Table"], {
                className: "h-4 w-4"
            }, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/config/reportingDashboardConfig.tsx",
                lineNumber: 76,
                columnNumber: 13
            }, this),
            description: 'In-depth data table with filtering and sorting.',
            widgets: [
                {
                    id: 'reporting-table',
                    component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$ReportingTableWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReportingTableWidget"],
                    span: 'lg:col-span-3 xl:col-span-4'
                }
            ]
        }
    ]
};
const widgetConfigurations = reportingDashboardConfig.tabs.flatMap((tab)=>tab.widgets);
const filterPresets = {
    lastWeek: {
        dateRange: {
            from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            to: new Date()
        },
        status: [],
        locations: [],
        employees: [],
        vehicles: []
    },
    lastMonth: {
        dateRange: {
            from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            to: new Date()
        },
        status: [],
        locations: [],
        employees: [],
        vehicles: []
    },
    currentYear: {
        dateRange: {
            from: new Date(new Date().getFullYear(), 0, 1),
            to: new Date()
        },
        status: [],
        locations: [],
        employees: [],
        vehicles: []
    }
};
}}),
"[project]/src/components/features/reporting/dashboard/layout/ReportingLayout.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @file Reporting Layout Component
 * @description Provides a consistent layout structure for all reporting pages.
 */ __turbopack_context__.s({
    "ReportingLayout": (()=>ReportingLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
const ReportingLayout = ({ title, description, children, actions, filters })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col h-full p-4 md:p-6 lg:p-8 gap-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "flex flex-col md:flex-row md:items-center md:justify-between gap-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-grow",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-2xl md:text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100",
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/reporting/dashboard/layout/ReportingLayout.tsx",
                                lineNumber: 27,
                                columnNumber: 11
                            }, this),
                            description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-gray-600 dark:text-gray-400",
                                children: description
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/reporting/dashboard/layout/ReportingLayout.tsx",
                                lineNumber: 31,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/reporting/dashboard/layout/ReportingLayout.tsx",
                        lineNumber: 26,
                        columnNumber: 9
                    }, this),
                    actions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-shrink-0",
                        children: actions
                    }, void 0, false, {
                        fileName: "[project]/src/components/features/reporting/dashboard/layout/ReportingLayout.tsx",
                        lineNumber: 36,
                        columnNumber: 21
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/features/reporting/dashboard/layout/ReportingLayout.tsx",
                lineNumber: 25,
                columnNumber: 7
            }, this),
            filters && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("aside", {
                children: filters
            }, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/layout/ReportingLayout.tsx",
                lineNumber: 39,
                columnNumber: 19
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "flex-1",
                children: children
            }, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/layout/ReportingLayout.tsx",
                lineNumber: 41,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/features/reporting/dashboard/layout/ReportingLayout.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, this);
};
}}),
"[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
// frontend/src/components/features/reporting/dashboard/ReportingDashboard.tsx
__turbopack_context__.s({
    "ReportingDashboard": (()=>ReportingDashboard),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-ssr] (ecmascript) <export default as BarChart3>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/car.js [app-ssr] (ecmascript) <export default as Car>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$check$2d$big$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckSquare$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/square-check-big.js [app-ssr] (ecmascript) <export default as CheckSquare>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-ssr] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-ssr] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$filter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/filter.js [app-ssr] (ecmascript) <export default as Filter>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$network$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Network$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/network.js [app-ssr] (ecmascript) <export default as Network>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/refresh-cw.js [app-ssr] (ecmascript) <export default as RefreshCw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-ssr] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Table$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/table.js [app-ssr] (ecmascript) <export default as Table>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-ssr] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-ssr] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$DashboardLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/dashboard/DashboardLayout.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tabs.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$generation$2f$ReportGenerationPage$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/generation/ReportGenerationPage.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$management$2f$ReportBuilder$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/management/ReportBuilder.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$management$2f$ReportTypeManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/management/ReportTypeManager.tsx [app-ssr] (ecmascript)");
// Note: Using new export system from exports/hooks/useExport
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$tables$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/features/reporting/tables/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$tables$2f$TaskReportingTable$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/tables/TaskReportingTable.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$config$2f$reportingDashboardConfig$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/config/reportingDashboardConfig.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$filters$2f$ReportingFilters$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/filters/ReportingFilters.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$layout$2f$ReportingLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/layout/ReportingLayout.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$CrossEntityCorrelationWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/CrossEntityCorrelationWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$DelegationStatusWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/DelegationStatusWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$DelegationTrendWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/DelegationTrendWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$EmployeeAnalyticsWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/EmployeeAnalyticsWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$EmployeePerformanceChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/EmployeePerformanceChart.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$EmployeeWorkloadWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/EmployeeWorkloadWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$EntityRelationshipNetworkWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/EntityRelationshipNetworkWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$LocationDistributionWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/LocationDistributionWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$TaskAssignmentMetrics$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/TaskAssignmentMetrics.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$TaskMetricsWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/TaskMetricsWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$TaskStatusChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/TaskStatusChart.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$VehicleAnalyticsWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/VehicleAnalyticsWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$VehicleCostAnalyticsWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/VehicleCostAnalyticsWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$VehicleMaintenanceWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/VehicleMaintenanceWidget.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$VehicleUtilizationChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/features/reporting/dashboard/widgets/VehicleUtilizationChart.tsx [app-ssr] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$generation$2f$ReportGenerationPage$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$generation$2f$ReportGenerationPage$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const ReportingDashboard = ({ className = '' })=>{
    const [activeTab, setActiveTab] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState('overview');
    const [isFilterPanelOpen, setIsFilterPanelOpen] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(false);
    // Simplified filters to avoid store issues
    const filters = {
        costRange: {
            max: 10_000,
            min: 0
        },
        dateRange: {
            from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            to: new Date()
        },
        employees: [],
        includeServiceHistory: false,
        includeTaskData: false,
        locations: [],
        serviceStatus: [],
        serviceTypes: [],
        status: [],
        vehicles: []
    };
    const toggleFilterPanel = ()=>setIsFilterPanelOpen(!isFilterPanelOpen);
    // TODO: Re-enable real-time updates after fixing store
    // useRealtimeReportingUpdates(filters);
    // Professional dashboard tabs configuration
    const dashboardTabs = [
        {
            description: 'High-level metrics and key performance indicators',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                lineNumber: 117,
                columnNumber: 13
            }, this),
            id: 'overview',
            label: 'Overview'
        },
        {
            description: 'Detailed analytics and trend analysis',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                lineNumber: 123,
                columnNumber: 13
            }, this),
            id: 'analytics',
            label: 'Analytics'
        },
        {
            description: 'Task metrics and performance analysis',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$check$2d$big$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckSquare$3e$__["CheckSquare"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                lineNumber: 129,
                columnNumber: 13
            }, this),
            id: 'tasks',
            label: 'Tasks'
        },
        {
            description: 'Vehicle utilization and maintenance analytics',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$car$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Car$3e$__["Car"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                lineNumber: 135,
                columnNumber: 13
            }, this),
            id: 'vehicles',
            label: 'Vehicles'
        },
        {
            description: 'Employee performance and workload analysis',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                lineNumber: 141,
                columnNumber: 13
            }, this),
            id: 'employees',
            label: 'Employees'
        },
        {
            description: 'Cross-entity relationships and correlations',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$network$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Network$3e$__["Network"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                lineNumber: 147,
                columnNumber: 13
            }, this),
            id: 'correlations',
            label: 'Correlations'
        },
        {
            description: 'Generate comprehensive data reports for all entities',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                lineNumber: 153,
                columnNumber: 13
            }, this),
            id: 'generation',
            label: 'Generate Reports'
        },
        {
            description: 'Manage report types and build custom reports',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                lineNumber: 159,
                columnNumber: 13
            }, this),
            id: 'management',
            label: 'Management'
        },
        {
            description: 'Raw delegation data in tabular format',
            icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$table$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Table$3e$__["Table"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                lineNumber: 165,
                columnNumber: 13
            }, this),
            id: 'data',
            label: 'Data'
        }
    ];
    // Professional 12-column grid widget configurations
    const getWidgetsForTab = (tabId)=>{
        switch(tabId){
            case 'overview':
                {
                    return [
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$DelegationStatusWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationStatusWidget"],
                            id: 'status',
                            span: 'col-span-12 lg:col-span-4'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$TaskMetricsWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskMetricsWidget"],
                            id: 'tasks',
                            span: 'col-span-12 lg:col-span-8'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$DelegationTrendWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTrendWidget"],
                            id: 'trend',
                            span: 'col-span-12 lg:col-span-8'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$LocationDistributionWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationDistributionWidget"],
                            id: 'location',
                            span: 'col-span-12 lg:col-span-4'
                        }
                    ];
                }
            case 'analytics':
                {
                    return [
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$DelegationTrendWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationTrendWidget"],
                            id: 'trend',
                            span: 'col-span-12 lg:col-span-8'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$LocationDistributionWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LocationDistributionWidget"],
                            id: 'location',
                            span: 'col-span-12 lg:col-span-4'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$TaskMetricsWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskMetricsWidget"],
                            id: 'tasks',
                            span: 'col-span-12 lg:col-span-6'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$DelegationStatusWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DelegationStatusWidget"],
                            id: 'status',
                            span: 'col-span-12 lg:col-span-6'
                        }
                    ];
                }
            case 'correlations':
                {
                    return [
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$CrossEntityCorrelationWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CrossEntityCorrelationWidget"],
                            id: 'cross-entity-correlations',
                            span: 'col-span-12'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$EntityRelationshipNetworkWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EntityRelationshipNetworkWidget"],
                            id: 'entity-relationships',
                            span: 'col-span-12 lg:col-span-6'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$TaskMetricsWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskMetricsWidget"],
                            id: 'task-correlations',
                            span: 'col-span-12 lg:col-span-6'
                        }
                    ];
                }
            case 'tasks':
                {
                    return [
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$TaskMetricsWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskMetricsWidget"],
                            id: 'task-metrics',
                            span: 'col-span-12'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$TaskAssignmentMetrics$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskAssignmentMetrics"],
                            id: 'task-assignments',
                            span: 'col-span-12 lg:col-span-6'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$TaskStatusChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskStatusChart"],
                            id: 'task-status-chart',
                            span: 'col-span-12 lg:col-span-6'
                        }
                    ];
                }
            case 'vehicles':
                {
                    return [
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$VehicleAnalyticsWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleAnalyticsWidget"],
                            id: 'vehicle-analytics',
                            span: 'col-span-12 lg:col-span-6'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$VehicleUtilizationChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleUtilizationChart"],
                            id: 'vehicle-utilization',
                            span: 'col-span-12 lg:col-span-6'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$VehicleMaintenanceWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleMaintenanceWidget"],
                            id: 'vehicle-maintenance',
                            span: 'col-span-12 lg:col-span-6'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$VehicleCostAnalyticsWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VehicleCostAnalyticsWidget"],
                            id: 'vehicle-costs',
                            span: 'col-span-12 lg:col-span-6'
                        }
                    ];
                }
            case 'employees':
                {
                    return [
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$EmployeeAnalyticsWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeAnalyticsWidget"],
                            id: 'employee-analytics',
                            span: 'col-span-12 lg:col-span-8'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$EmployeeWorkloadWidget$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeeWorkloadWidget"],
                            id: 'employee-workload',
                            span: 'col-span-12 lg:col-span-4'
                        },
                        {
                            component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$widgets$2f$EmployeePerformanceChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["EmployeePerformanceChart"],
                            id: 'employee-performance',
                            span: 'col-span-12'
                        }
                    ];
                }
            case 'generation':
                {
                    return [
                        {
                            component: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$generation$2f$ReportGenerationPage$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReportGenerationPage"], {}, void 0, false, {
                                    fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                                    lineNumber: 306,
                                    columnNumber: 30
                                }, this),
                            id: 'report-generation',
                            span: 'col-span-12'
                        }
                    ];
                }
            case 'management':
                {
                    return [
                        {
                            component: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$management$2f$ReportTypeManager$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReportTypeManager"], {}, void 0, false, {
                                    fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                                    lineNumber: 315,
                                    columnNumber: 30
                                }, this),
                            id: 'report-type-manager',
                            span: 'col-span-12 lg:col-span-6'
                        },
                        {
                            component: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$management$2f$ReportBuilder$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReportBuilder"], {}, void 0, false, {
                                    fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                                    lineNumber: 320,
                                    columnNumber: 30
                                }, this),
                            id: 'report-builder',
                            span: 'col-span-12 lg:col-span-6'
                        }
                    ];
                }
            case 'data':
                {
                    return [
                        {
                            component: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$tables$2f$TaskReportingTable$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TaskReportingTable"], {}, void 0, false, {
                                    fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                                    lineNumber: 329,
                                    columnNumber: 30
                                }, this),
                            id: 'data-table',
                            span: 'col-span-12'
                        }
                    ];
                }
            default:
                {
                    return [];
                }
        }
    };
    // Consolidated export functionality
    const handleExportDashboard = async ()=>{
        try {
            const exportData = {
                activeTab: activeTab,
                filters: filters,
                metadata: {
                    exportedBy: 'Reporting Dashboard',
                    version: '2.0.0'
                },
                timestamp: new Date().toISOString()
            };
            const blob = new Blob([
                JSON.stringify(exportData, null, 2)
            ], {
                type: 'application/json'
            });
            const url = globalThis.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `dashboard-export-${new Date().toISOString().split('T')[0]}.json`;
            document.body.append(a);
            a.click();
            a.remove();
            globalThis.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Export failed:', error);
        }
    };
    const handleRefreshDashboard = ()=>{
        globalThis.location.reload();
    };
    // Consolidated action bar - single, clean interface
    const renderDashboardActions = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center gap-3",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                    className: isFilterPanelOpen ? 'bg-primary text-primary-foreground' : '',
                    onClick: toggleFilterPanel,
                    size: "sm",
                    variant: "outline",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$filter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Filter$3e$__["Filter"], {
                            className: "mr-2 size-4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                            lineNumber: 385,
                            columnNumber: 9
                        }, this),
                        "Filters"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                    lineNumber: 377,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                    onClick: handleRefreshDashboard,
                    size: "sm",
                    variant: "outline",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$refresh$2d$cw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RefreshCw$3e$__["RefreshCw"], {
                            className: "mr-2 size-4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                            lineNumber: 389,
                            columnNumber: 9
                        }, this),
                        "Refresh"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                    lineNumber: 388,
                    columnNumber: 7
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                    onClick: handleExportDashboard,
                    size: "sm",
                    variant: "outline",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                            className: "mr-2 size-4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                            lineNumber: 393,
                            columnNumber: 9
                        }, this),
                        "Export"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                    lineNumber: 392,
                    columnNumber: 7
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
            lineNumber: 376,
            columnNumber: 5
        }, this);
    // Clean filter summary
    const renderFilterSummary = ()=>{
        const activeFilters = [];
        if (filters.status.length > 0) activeFilters.push(`${filters.status.length} status`);
        if (filters.locations.length > 0) activeFilters.push(`${filters.locations.length} locations`);
        if (filters.employees.length > 0) activeFilters.push(`${filters.employees.length} employees`);
        if (filters.vehicles.length > 0) activeFilters.push(`${filters.vehicles.length} vehicles`);
        if (activeFilters.length === 0) return null;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center gap-3 text-sm text-muted-foreground",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    children: "Active filters:"
                }, void 0, false, {
                    fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                    lineNumber: 415,
                    columnNumber: 9
                }, this),
                activeFilters.map((filter, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                        className: "text-xs",
                        variant: "secondary",
                        children: filter
                    }, `filter-${index}-${filter}`, false, {
                        fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                        lineNumber: 417,
                        columnNumber: 11
                    }, this))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
            lineNumber: 414,
            columnNumber: 7
        }, this);
    };
    // Professional widget rendering with 12-column grid
    const renderWidgets = ()=>{
        const widgets = getWidgetsForTab(activeTab);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "grid grid-cols-12 gap-6",
            children: widgets.map(({ component: WidgetComponent, id, span })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: span,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(WidgetComponent, {}, void 0, false, {
                        fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                        lineNumber: 437,
                        columnNumber: 13
                    }, this)
                }, `${activeTab}-${id}`, false, {
                    fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                    lineNumber: 436,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
            lineNumber: 434,
            columnNumber: 7
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$dashboard$2f$DashboardLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DashboardPage"], {
        className: className,
        config: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$config$2f$reportingDashboardConfig$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["reportingDashboardConfig"],
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$layout$2f$ReportingLayout$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReportingLayout"], {
            actions: renderDashboardActions(),
            description: "Interactive dashboard with real-time metrics and insights",
            filters: isFilterPanelOpen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$features$2f$reporting$2f$dashboard$2f$filters$2f$ReportingFilters$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReportingFilters"], {}, void 0, false, {
                fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                lineNumber: 449,
                columnNumber: 38
            }, void 0) : undefined,
            title: "Reporting Dashboard",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-8",
                children: [
                    renderFilterSummary(),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Tabs"], {
                        className: "w-full",
                        onValueChange: setActiveTab,
                        value: activeTab,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsList"], {
                                className: "grid w-full grid-cols-9 h-12",
                                children: dashboardTabs.map((tab)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                        className: "flex items-center gap-2 text-sm font-medium",
                                        value: tab.id,
                                        children: [
                                            tab.icon,
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "hidden sm:inline",
                                                children: tab.label
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                                                lineNumber: 470,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, tab.id, true, {
                                        fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                                        lineNumber: 464,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                                lineNumber: 462,
                                columnNumber: 13
                            }, this),
                            dashboardTabs.map((tab)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsContent"], {
                                    className: "space-y-8",
                                    value: tab.id,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                    className: "text-2xl font-semibold tracking-tight flex items-center gap-3",
                                                    children: [
                                                        tab.icon,
                                                        tab.label
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                                                    lineNumber: 478,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-muted-foreground",
                                                    children: tab.description
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                                                    lineNumber: 482,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                                            lineNumber: 477,
                                            columnNumber: 17
                                        }, this),
                                        renderWidgets()
                                    ]
                                }, tab.id, true, {
                                    fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                                    lineNumber: 476,
                                    columnNumber: 15
                                }, this))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                        lineNumber: 457,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
                lineNumber: 452,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
            lineNumber: 446,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/features/reporting/dashboard/ReportingDashboard.tsx",
        lineNumber: 445,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ReportingDashboard;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=src_components_features_reporting_dashboard_c7f8842b._.js.map